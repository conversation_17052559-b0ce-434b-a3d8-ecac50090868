<template>



  <div class="leftCard">
    <a-tabs v-model="activeMap" @change="changeMap">
      <a-tab-pane key="world" tab="世界地图">

      </a-tab-pane>
      <a-tab-pane key="china" tab="中国地图" >

      </a-tab-pane>
      <a-tab-pane key="gd" tab="广东地图">

      </a-tab-pane>
      <a-tab-pane key="hb" tab="湖北地图">

      </a-tab-pane>
    </a-tabs>

    <a-table :columns="columns" :data-source="data" bordered v-if="showTable"
             style="width: 70%;position: absolute;right: 10px;"
             bordered
             :rowKey="(record) => record.id"
             :pagination="false"
    >
    </a-table>
    <div id="chinaMap" ref="chinaMap"></div>
  </div>
</template>

<script>
  import world from './world.json'
  import china from './china.json'
  import gd from './gd.json'
  import hz from './hz.json'
  import hb from './hb.json'
  import {
    capacityData,listCapacity
  } from "@/api/modular/system/capacityManage"
  export default {
    props: {
      issueId: {
        type: Number,
        default: 0
      },

      projectdetail: {
        type: Object,
        default: {}
      }
    },
    name: 'leftCard',
    data() {
      return {
        activeMap:'china',
        myChart: null,
        dataList: [
        ],
        dataListChina: [

        ],
        dataListCity: [

        ],

        columns: [
          {
            title: '序号',
            align: 'center',
            width: 60,
            customRender: (text, record, index) => index + 1
          }, {
            title: '产能区分',
            dataIndex: 'capacitySplit',
            align: 'center',
            width: 90
          }, {
            title: '工程区分',
            width: 90,
            align: 'center',
            dataIndex: 'projectSplit',
            //scopedSlots: {customRender: 'updateText'},
          },
          {
            title: '地区',
            width: 90,
            align: 'center',
            dataIndex: 'area',
            //scopedSlots: {customRender: 'updateText'},
          },  {
            title: '分区',
            width: 90,
            align: 'center',
            dataIndex: 'split',
            //scopedSlots: {customRender: 'updateText'},
          },   {
            title: '工厂代码',
            width: 90,
            align: 'center',
            dataIndex: 'factoryCode',
            //scopedSlots: {customRender: 'updateText'},
          },   {
            title: '产线编号',
            width: 90,
            align: 'center',
            dataIndex: 'lineCode',
            //scopedSlots: {customRender: 'updateText'},
          },   {
            title: '产品类型',
            width: 90,
            align: 'center',
            dataIndex: 'productType',
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '定义产品',
            width: 90,
            align: 'center',
            dataIndex: 'product',
            //scopedSlots: {customRender: 'updateText'},
          },{
            title: '单线ppm',
            width: 90,
            align: 'center',
            dataIndex: 'ppm',
            //scopedSlots: {customRender: 'updateText'},
          },{
            title: '计算产能',
            width: 90,
            align: 'center',
            dataIndex: 'countCapacity',
            customRender: (text, record, index) => text? text+ 'GWh':null
          },{
            title: '标称产能',
            width: 90,
            align: 'center',
            dataIndex: 'standardCapacity',
            customRender: (text, record, index) => text? text+ 'GWh':null
          },{
            title: '预测PPAP',
            width: 90,
            align: 'center',
            dataIndex: 'planPpap'
          },{
            title: '备注',
            width: 90,
            align: 'center',
            dataIndex: 'remark'
          }
        ],
        data:[],
        showTable:false
      }
    },
    created() {
    },
    mounted() {

      capacityData({type:'province',product:this.projectdetail.productProjectName}).then((res) => {
        this.dataListChina = res.data
      }).then(() => {
        capacityData({type:'country',product:this.projectdetail.productProjectName}).then((res) => {
          this.dataList = res.data
        })
        capacityData({type:'city',product:this.projectdetail.productProjectName}).then((res) => {
          this.dataListCity = res.data
        })
      }).then(() => this.initEchartMap())


    },
    methods: {
      // 初始化地图
      changeMap(){
        this.showTable = false
        this.myChart.clear();
        this.echarts.registerMap(this.activeMap, this.activeMap == 'china'?
          china:this.activeMap == 'world'?world:this.activeMap == 'gd'?gd:hb)
        this.myChart = this.echarts.init(this.$refs.chinaMap, 'shine', {renderer: 'svg'});

        //echart 配制option
        var options = this.getOptions(this.activeMap,this.activeMap == 'china'?
          this.dataListChina:this.activeMap == 'world'?this.dataList:this.dataListCity);
        this.myChart.setOption(options);
      },


      getOptions(name,data) {
        let _this = this
        return {
          tooltip: {
            triggerOn: "mousemove",   //mousemove、click
            padding: 8,
            borderWidth: 1,
            borderColor: '#b8b8b8',
            backgroundColor: 'rgba(255,255,255,0.7)',
            textStyle: {
              color: '#000000',
              fontSize: 13
            },
            formatter: function (e, t, n) {

              if(e.value > 0){
                let param = {}

                if(name == 'china'){
                  param.province = e.name
                }
                if(name == 'world'){
                  param.country = e.name
                }
                if(name == 'hb' || name == 'gd'){
                  param.city = e.name
                }

                param.product = _this.projectdetail.productProjectName
                listCapacity(param).then((res) => {
                  _this.data = res.data
                }).then(() => {
                  _this.showTable = true
                })
              }else {
                _this.showTable = false
              }

            }
          },
          visualMap: {
            show: false,
            left: 26,
            bottom: 100,
            showLabel: false,
            pieces: [
              {
                gte: 50,
                color: "#5475f5"
              },
              {
                gte: 30,
                lt: 50,
                color: "#e6ac53"
              },
              {
                gt: 0,
                lt: 30,
                color: "#9feaa5"
              }

            ]
          },
          geo: {
            roam: true,
            map: name,
            scaleLimit: {
              min: 0.5,
              max: 10
            },
            zoom: 0.9,
            top: 20,
            //layoutSize: "100%", //保持地图宽高比
            label: {
              /* normal: {
                 show:true,
                 fontSize: "0",
                 color: "rgba(0,0,0,0.7)"
               },*/
              emphasis: {
                show: false,
                textStyle: {
                  color: "#F3F3F3"
                }
              }
            },
            itemStyle: {
              normal: {
                shadowBlur: 50,
                shadowColor: 'rgba(0, 0, 0, 0.2)',
                borderColor: "rgba(0, 0, 0, 0.2)",
                areaColor: '#dfdfdf',
              },
              emphasis: {
                areaColor: "#f2d5ad",
                shadowOffsetX: 0,
                shadowOffsetY: 0,
                borderWidth: 0
              }
            }
          },
          series: [
            {
              name: "产能",
              type: "map",
              geoIndex: 0,
              data: data
            }
          ]
        }
      },

      initEchartMap() {
        this.echarts.registerMap('china', china)
        this.myChart = this.echarts.init(this.$refs.chinaMap, 'shine', {renderer: 'svg'});
        var options = this.getOptions('china',this.dataListChina);
        this.myChart.setOption(options);
       /* this.myChart.on("georoam", params => {

          let _option = this.myChart.getOption();     //获取最新的option配制


          if (_option.geo[0].map == 'world' && _option.geo[0].zoom > 3) {
            this.myChart.clear();
            this.echarts.registerMap('china', china) //这个特别重要
            this.myChart = this.echarts.init(this.$refs.chinaMap, 'shine', {renderer: 'svg'});
            //echart 配制option
            var options = this.getChinaMapOptions();
            this.myChart.setOption(options);
          }
          if (_option.geo[0].map == 'china' && _option.geo[0].zoom < 0.8) {
            this.myChart.clear();
            this.echarts.registerMap('world', world) //这个特别重要
            this.myChart = this.echarts.init(this.$refs.chinaMap, 'shine', {renderer: 'svg'});

            //echart 配制option
            var options = this.getWorldMapOptions();
            this.myChart.setOption(options);
          }
          if (_option.geo[0].map == 'china' && _option.geo[0].zoom > 3) {
            this.myChart.clear();
            this.echarts.registerMap('gd', gd) //这个特别重要
            this.myChart = this.echarts.init(this.$refs.chinaMap, 'shine', {renderer: 'svg'});

            //echart 配制option
            var options = this.getGdMapOptions();
            this.myChart.setOption(options);
          }

          if (_option.geo[0].map == 'gd' && _option.geo[0].zoom < 0.8) {
            this.myChart.clear();
            this.echarts.registerMap('china', china) //这个特别重要
            this.myChart = this.echarts.init(this.$refs.chinaMap, 'shine', {renderer: 'svg'});
            //echart 配制option
            var options = this.getChinaMapOptions();
            this.myChart.setOption(options);
          }
          if (_option.geo[0].map == 'gd' && _option.geo[0].zoom > 3) {
            this.myChart.clear();
            this.echarts.registerMap('hz', hz) //这个特别重要
            this.myChart = this.echarts.init(this.$refs.chinaMap, 'shine', {renderer: 'svg'});
            //echart 配制option
            var options = this.getHzMapOptions();
            this.myChart.setOption(options);
          }

          if (_option.geo[0].map == 'hz' && _option.geo[0].zoom < 0.8) {
            this.myChart.clear();
            this.echarts.registerMap('gd', gd) //这个特别重要
            this.myChart = this.echarts.init(this.$refs.chinaMap, 'shine', {renderer: 'svg'});
            //echart 配制option
            var options = this.getGdMapOptions();
            this.myChart.setOption(options);
          }




        })*/

      }
    }
  }
</script>

<style lang="less" scoped>
  .leftCard {
    width: 100%;
    height: 100%;

    #chinaMap {
      width: 100%;
      height: 100%;
    }
  }

  /deep/ .ant-table-tbody > tr > td {
    padding: 2px 0px;

  }
  /deep/ .ant-table-thead > tr > th {
    padding: 2px 0px;

  }
</style>

