<template>
	<div class="wrapper">
		<!-- 左侧tab start -->
		<div class="tabs-wrap" v-if="!needImportFlag">
			<div
				class="tab hand"
				v-for="(item, index) in tabsMenu"
				:key="index"
				@click="handleLeftTab(index)"
				:class="{ check: activeKey === index }"
			>
				{{ item }}
			</div>
		</div>
		<!-- 左侧tab end -->

		<!-- 右侧内容 start -->
		<div v-if="!needImportFlag">
			<doc-change-history-imp v-if="activeKey === 0" :deliverImpBatteryId="impBatteryId" ref="changeHisRef" />
			<mi-version-imp v-if="activeKey === 1" :deliverImpBatteryId="impBatteryId" ref="miVersionRef" />
			<mi-attach-version-imp v-if="activeKey === 2" :deliverImpBatteryId="impBatteryId" ref="attachVerRef" />
		</div>
		<!-- 右侧内容 end -->

		<!-- 空状态 start -->
		<div class="empty-status" v-else>
			<a-empty />
		</div>
		<!-- 空状态 end -->

		<!-- <div>
			<a-tabs @change="tabCallback" v-model="activeKey">
				<a-tab-pane class="changeHisTabClass" key="changeHistoryTab" tab="文件目录修订清单">
					<doc-change-history-imp :deliverImpBatteryId="impBatteryId" ref="changeHisRef" />
				</a-tab-pane>
				<a-tab-pane class="miTabClass" key="miTab" tab="MI">
					<mi-version-imp :deliverImpBatteryId="impBatteryId" ref="miVersionRef" />
				</a-tab-pane>
				<a-tab-pane class="attachVerTabClass" key="attachVersionTab" tab="附图">
					<mi-attach-version-imp :deliverImpBatteryId="impBatteryId" ref="attachVerRef" />
				</a-tab-pane>
			</a-tabs>
		</div> -->
	</div>
</template>
<script>
import Vue from "vue"
import moment from "moment/moment"
import { ACCESS_TOKEN } from "@/store/mutation-types"

import { getBatteryDesign } from "@/api/modular/system/batterydesignManage"
import { getMIStandardByImpBatteryId } from "@/api/modular/system/gCylinderMILibManage"

// tab
import docChangeHistoryImp from "./miComponents/docChangeHistoryImp"
import miVersionImp from "./miComponents/miVersionImp"
import miAttachVersionImp from "./miComponents/miAttachVersionImp"
export default {
	components: { docChangeHistoryImp, miVersionImp, miAttachVersionImp },
	data() {
		return {
			tabsMenu: ["文件变更履历", "MI", "附图"],
			labelCol: {
				xs: {
					span: 24
				},
				sm: {
					span: 8
				}
			},
			wrapperCol: {
				xs: {
					span: 24
				},
				sm: {
					span: 14
				}
			},
			visible: false,
			// activeKey: "changeHistoryTab",
			activeKey: 0,
			needImportFlag: false,
			libraryId: null,
			confirmLoading: false,
			form: this.$form.createForm(this, { name: "form" }),
			bigClient: document.documentElement.clientHeight > 700,
			structureType1: "",
			design: {},
			productName1: "",
			impBatteryId: null,
			headers: {
				Authorization: "Bearer " + Vue.ls.get(ACCESS_TOKEN)
			}
		}
	},
	props: {
		inBatteryId: {
			type: String,
			default: ""
		}
	},
	created() {
		this.impBatteryId = this.inBatteryId
		this.getData()
		this.getBatteryDesignMethod(this.impBatteryId)
	},
	mounted() {
		document.getElementsByClassName("ant-layout-content")[0].style.backgroundColor = "white"
	},
	methods: {
		tabCallback(key) {},
		getBatteryDesignMethod(id) {
			getBatteryDesign({ inBatteryId: id, type: "design" }).then(res => {
				this.design = res.data
				this.productName1 = this.design.productName
				switch (res.data.structureType) {
					case "g_cylinder":
						this.structureType1 = "G圆柱"
						break
					case "c_cylinder":
						this.structureType1 = "C圆柱"
						break
					case "v_cylinder":
						this.structureType1 = "V圆柱"
						break
					case "winding":
						this.structureType1 = "方形卷绕"
						break
					case "lamination":
						this.structureType1 = "方形叠片"
						break
					case "soft_roll":
						this.structureType1 = "软包"
						break
				}
			})
		},
		getData() {
			getMIStandardByImpBatteryId(this.impBatteryId).then(res => {
				this.libraryId = res.data
				this.needImportFlag = !this.libraryId
			})
		},
		importSuccess(flag) {
			this.needImportFlag = flag
		},
		gotoBom() {
			//this.switchApp()
			this.$router.push({
				path: "/sys_battery_design_bom",
				query: {
					batteryId: this.impBatteryId
				}
			})
		},
		gotoDesgin() {
			//this.switchApp()
			this.$router.push({
				path: "/battery_design_manager",
				query: {
					batteryId: this.impBatteryId
				}
			})
		},
		gotoDevelop() {
			this.$router.push({
				path: "/batterydesign"
			})
		},
		checkMiVersion(record) {
			// this.$router.push(
			//   {
			//     path: "/g_cylinder_mi_version",
			//     query: {
			//       record: record
			//     },
			//   }
			// )
		},
		checkMiAttachVersion(record) {
			// this.$router.push(
			//   {
			//     path: "/g_cylinder_mi_attach_version",
			//     query: {
			//       record: record
			//     },
			//   }
			// )
		},
		checkDocumentChangeHistory(record) {
			// this.$router.push(
			//   {
			//     path: "/g_cylinder_doc_change_history",
			//     query: {
			//       record: record
			//     },
			//   }
			// )
		},
		renderUpdateTime(text) {
			return text == null ? "" : moment(new Date(text)).format("YYYY-MM-DD")
		},
		miChooseModelImport() {
			this.$refs.miLibDataDialog.miChooseModelImport(this.impBatteryId, this.libraryId)
		},
		handleCancel() {
			this.visible = false
		},
		// 点击左侧tab
		handleLeftTab(index) {
			this.activeKey = index
		}
	}
}
</script>
<style lang="less" scoped>
// 标题
.tab-title {
	padding: 0 10px;
}
.tab-head {
	border-bottom: 1px solid #d3d2d2c9;
}

.tab-head div:first-child {
	display: inline-block;

	font-weight: 700;
	font-size: 18px;
	color: rgb(128, 128, 128);
	margin-bottom: -6px;
	cursor: pointer;
}

.tab-head div:first-child.active {
	font-size: 24px;
	color: rgba(0, 73, 176, 1);
	margin-bottom: -4px;
	cursor: text;
}

/deep/.ant-table-bordered.ant-table-empty .ant-table-placeholder {
	border: 1px solid black;
}
#miStandardId > div > div > div > div > div > table {
	border: 0;
}
#miStandardId > div > div > div > div > div > table > .ant-table-thead > tr > .ant-table-align-center {
	background-color: #f5f5f5;
	border-bottom: 1.5px solid #c0c0c0;
}
#miStandardId > div > div > div > div > div > table > .ant-table-tbody > tr > td {
	border-bottom: 1.5px solid #c0c0c0;
}
#miStandardId > div > div > div > div > div > table > .ant-table-tbody > tr:last-child > td {
	border: 0;
}
#miModalId > div > div > div > div > .ant-modal-title {
	color: dodgerblue;
	font-size: 20px;
	font-weight: bold;
}
/deep/.ant-table-thead > tr > th,
.ant-table-tbody > tr > td {
	padding: 10px 10px;
	overflow-wrap: break-word;
}
/deep/.ant-table-bordered .ant-table-tbody > tr > td {
	border: 0;
	border: none;
}
/deep/.ant-table-thead > tr > th {
	border: 0;
}
/deep/ .exportButtonClass {
	font-family: SourceHanSansSC;
	font-weight: 400;
	font-size: 15px;
	color: rgba(0, 101, 255, 0.67);
}

/deep/ .ant-table-thead > tr > th {
	background: white;
	padding: 12px 8px;
	font-weight: bold;
	border: 1px solid black;
	border-bottom: 0;
}

/deep/ .ant-table-bordered .ant-table-tbody > tr > td {
	border: 1px solid black;
}

/deep/ .ant-table-bordered .ant-table-body > table {
	border-collapse: collapse;
	border-spacing: 0;
}

/deep/ .ant-table-bordered .ant-table-header > table {
	border-collapse: collapse;
	border-spacing: 0;
}

/deep/ .ant-table-bordered .ant-table-title > table {
	border-collapse: collapse;
	border-spacing: 0;
	border-bottom: 0;
}

/deep/ .ant-table-bordered .ant-table-title > table > tr > td {
	border: 1px solid black;
}

/deep/ .ant-table-bordered .ant-table-footer > table {
	border-collapse: collapse;
	border-spacing: 0px;
}

/deep/ .ant-table-bordered .ant-table-footer > table > tr > td {
	border: 1px solid black;
}

#develop {
	font-size: 12px;
	margin: 0px 70px 0px 70px;
	color: #000;
}

textarea.ant-input {
	max-width: 100%;
	height: auto;
	min-height: 32px;
	line-height: 1.5;
	vertical-align: bottom;
	-webkit-transition: all 0.3s, height 0s;
	transition: all 0.3s, height 0s;
	border: none;
}

// wrapper
.wrapper {
	display: flex;
}

// tabs
.tabs-wrap {
	width: 115px;
	margin: 40px 20px;

	display: flex;
	flex-direction: column;
	flex-shrink: 0;
}

.tabs-wrap .tab {
	margin: 20px 0;
}

.check {
	color: #1890ff;
}

.empty-status {
	width: 100%;
	text-align: center;
	margin-bottom: 20px;
}
</style>
