<template>
<div>
    <div>
        <a-breadcrumb class="breadcrumb" separator=">">
            <a-breadcrumb-item><a @click="gohome"><!-- <a-icon type="home" /> -->产品看板</a></a-breadcrumb-item>
            <a-breadcrumb-item><a @click="goprolist">产品信息对齐表</a></a-breadcrumb-item>
            <a-breadcrumb-item ><a v-if="navTag == '5' && showdoc" @click="hidedoc">{{menuTitle}}</a><span v-else>{{menuTitle}}</span></a-breadcrumb-item>
            <a-breadcrumb-item v-if="navTag == '5' && showdoc">产品主要技术文档</a-breadcrumb-item>
        </a-breadcrumb>
    </div>
    <div class="layout-main">
        <div class="slide" :style="{ overflowY: 'hiden'}">
            <a-menu
                    :inlineIndent="12"
                    style="width: 200px"
                    :default-selected-keys="[navTag]"
                    mode="inline"
                    theme="light"
                    :inline-collapsed="collapsed"
                >
                <a-menu-item key="1" @click="showView(1)">
                    <a-icon type="copy" />
                    <span>基本信息</span>
                </a-menu-item>
                <a-menu-item key="2" @click="showView(2)">
                    <a-icon type="shop" />
                    <span>产品参数</span>
                </a-menu-item>
                <a-menu-item key="3" @click="showView(3)">
                    <a-icon type="profile" />
                    <span>产品开发进度</span>
                </a-menu-item>
                <a-menu-item key="4" @click="showView(4)">
                    <a-icon type="hdd" />
                    <span>产品开发进展</span>
                </a-menu-item>
                <a-menu-item key="5" @click="showView(5)">
                    <a-icon type="file" />
                    <span>产品主要技术文档</span>
                </a-menu-item>
            </a-menu>
            <div class="collapsed_bar" :style="collapsed ? width40 : width187" @click="toggleCollapsed">
                <a-icon class="collapsed_btn" :type="collapsed ? 'menu-unfold' : 'menu-fold'" />
            </div>
        </div>
        <div class="wrap" :style="{ padding: '0', overflowX: 'auto'}">
            <baseInfo v-if="navTag == '1'"  />
            <params v-if="navTag == '2'"  />
            <stageplan v-if="navTag == '3'"  />
            <process v-if="navTag == '4'"  />
            <prodocs v-if="navTag == '5' && !showdoc" @showdetail="showdetail"/>
            <techdoc v-if="navTag == '5' && showdoc" :issueId="issueId" :productName="productName" techStatus="2" />
        </div>
    </div>
</div>
</template>

<script>
import baseInfo from './baseInfo'
import process from './process'
import params from './params'
import prodocs from './prodocs'
import techdoc from './techdoc/index'
import stageplan from './stageplan'
import { mixin } from '@/utils/mixin'
import { mapActions } from 'vuex'
export default {
  mixins: [mixin],
    components: {
        baseInfo,
        process,
        params,
        prodocs,
        techdoc,
        stageplan
    },
    watch: {
      sidebarOpened (val) {
        this.collapsed = !val
      },
     },
    data(){
        return {
            issueId:0,
            showdoc:false,
            menuTitle:'',
            productName:'',
            menusinfos:{
              1:'基本信息',
              2:'产品参数',
              3:'产品开发进度',
              4:'产品开发进展',
              5:'产品文档',
            },
            navTag:'1',
            collapsed: true,
            width187:{
                width:'187px',
                textAlign: 'right'
            },
            width40:{
                width:'40px',
                textAlign: 'center'
            }
        }
    },
    methods:{
        ...mapActions(['setSidebar']),
        hidedoc(){
          this.issueId = 0
          this.showdoc = false
        },
        showdetail(row){
          this.productName = row.productProjectName
          this.issueId = row.issueId
          this.showdoc = true
        },
        gohome(){
          this.$router.push({
            path: "/product_chart",
          })
        },
        goprolist(){
          this.$router.push({
            path: "/report_sum",
          })
        },
        showView(tg){
            this.navTag = tg+''
            window.sessionStorage.setItem('tag',tg+'')
            this.menuTitle = this.menusinfos[this.navTag]
        },
        toggleCollapsed() {
            this.collapsed = !this.collapsed;
            this.setSidebar(!this.collapsed)
        },
    },
    created(){
      if (window.sessionStorage.getItem('tag')) {
        this.navTag = window.sessionStorage.getItem('tag')
      }
      this.menuTitle = this.menusinfos[this.navTag]
      this.collapsed = !this.sidebarOpened
    },
    mounted(){
        if(!window.sessionStorage.getItem('tag')){
          window.sessionStorage.setItem('tag',this.navTag)
        }else if(window.sessionStorage.getItem('tag')){
            this.navTag = window.sessionStorage.getItem('tag')
        }
    },
    destroyed() {
      window.sessionStorage.removeItem('tag')
    },
}
</script>
<style lang="less" scoped=''>
.wrap{
  background: #f0f2f5;
}
 .layout-main{
    display: flex;
    flex-direction: row;
  }
  .wrap{
    flex: 1;
  }
  .slide{
    max-width: 200px;
    background-color: #fff;
    box-shadow: 2px 0px 8px 0px rgba(29, 35, 41, 5%);
    z-index: 100;
    height: 100vh;
  }
  .collapsed_bar{
    position: fixed;
    width: 20px;
    bottom: 0;
    /* top: 0; */
    cursor: pointer;
    
  }
  /deep/.ant-menu-light{
    border-right-color: transparent;
  }
  /deep/.ant-menu-inline-collapsed{
    width: 40px !important;
  }
  /deep/.ant-layout-sider-collapsed{
    flex: 0 0 40px !important;
    max-width: 40px !important;
    min-width: 40px !important;
    width: 40px !important;
  }
  /deep/.ant-menu-inline-collapsed > .ant-menu-item,
  /deep/.ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item, .ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-submenu > .ant-menu-submenu-title, .ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title{
    padding: 0 12px !important;
  }
  /deep/.ant-menu-vertical .ant-menu-item,/deep/.ant-menu-inline .ant-menu-item{
    margin-top: 0;
  }
.breadcrumb{
        padding: 5px 12px;
    }
    .ant-breadcrumb a{
      color:#5d90fa !important;
    }
    .ant-breadcrumb a:first-child{
      color:rgba(0, 0, 0, 0.65) !important;
      
    }
    .ant-breadcrumb{
      font-size: 12px !important;
      color: rgba(0, 0, 0, 0.65) !important;
    }
    /deep/.ant-breadcrumb .anticon.anticon-home{
      font-size: 19px;
    }
</style>
<style>
  .ant-layout-sider-collapsed{
    flex: 0 0 40px !important;
    max-width: 40px !important;
    min-width: 40px !important;
    width: 40px !important;
  }
  .ant-menu-inline-collapsed > .ant-menu-item,
  .ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item, .ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-submenu > .ant-menu-submenu-title, .ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title{
    padding: 0 12px !important;
  }
  .ant-menu-vertical .ant-menu-item,
  .ant-menu-vertical-left .ant-menu-item,
  .ant-menu-vertical-right .ant-menu-item,
  .ant-menu-inline .ant-menu-item,
  .ant-menu-vertical .ant-menu-submenu-title,
  .ant-menu-vertical-left .ant-menu-submenu-title,
  .ant-menu-vertical-right .ant-menu-submenu-title,
  .ant-menu-inline .ant-menu-submenu-title{
    font-size: 13px;
  }
</style>