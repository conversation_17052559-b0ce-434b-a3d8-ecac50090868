<template>
	<div class="container">
		<!-- 面包屑 start -->
		<div class="breadcrumb">
			<a-breadcrumb :routes="routes" separator=">">
				<template slot="itemRender" slot-scope="{ route, params, routes, paths }">
					<router-link v-if="routes.indexOf(route) === routes.length - 1" :to="route.path">
						{{ route.breadcrumbName }}
					</router-link>
					<router-link v-if="routes.indexOf(route) !== routes.length - 1" :to="paths.join('/')">
						<a-icon v-if="routes.indexOf(route) !== routes.length - 1" type="rollback" class="rollback-icon" />
						{{ route.breadcrumbName }}
					</router-link>
				</template>
			</a-breadcrumb>
		</div>
		<!-- 面包屑 end -->

		<!-- <div style="width: 100%;height: 30px">
      <span style="font-size: 8px;margin-left: 20px;color: black;font-weight: bold;float: left;">填写说明：仅限于工艺部门维护</span>
    </div> -->
		<!-- <div style="margin-bottom: 20px;width: 100%;height: 25px">
			<a-button style="float: left;width: 150px;margin-left: 20px;" @click="roleDefinition">审批流程角色定义</a-button>
			<a-popconfirm
				title="确定删除吗?"
				ok-text="确定"
				cancel-text="取消"
				@confirm="deleteData()"
				:disabled="this.beforeDeleteFlag"
				placement="topRight"
			>
				<a-button
					type="primary"
					style="float: right;width: 80px;margin-right: 20px"
					:loading="deleteLoading"
					@click="beforeDelete"
					>删除</a-button
				>
			</a-popconfirm>
			<a-button
				type="primary"
				style="float: right;width: 80px;margin-right: 20px"
				:loading="copyLoading"
				@click="copyData"
				>复制</a-button
			>
			<a-button type="primary" style="float: right;width: 80px;margin-right: 20px" @click="openAdd">新增</a-button>
		</div> -->

		<!-- table start -->
		<div class="table-wrapper">
			<div>
				<span class="tips">填写说明：仅限于工艺部门维护</span>
				<a-button class="left-btn mr10" size="small" @click="roleDefinition">审批流程角色定义</a-button>
				<a-popconfirm
					title="确定删除吗?"
					ok-text="确定"
					cancel-text="取消"
					@confirm="deleteData()"
					:visible="beforeDeleteFlag"
          @cancel="() => beforeDeleteFlag = false"
					placement="topRight"
				>
					<a-button type="primary" class="right-btn" size="small" :loading="deleteLoading" @click="beforeDelete"
						>删除</a-button
					>
				</a-popconfirm>
				<a-button type="primary" class="right-btn mr10" size="small" :loading="copyLoading" @click="copyData"
					>复制</a-button
				>

				<a-button type="primary" size="small" class="right-btn  mr10" @click="openAdd">新增</a-button>
			</div>
			<a-table
				:style="`height:${tableHeight}px;`"
				:data-source="resultData"
				bordered
				:rowKey="record => record.id"
				:row-selection="{
					selectedRowKeys: selectedRowKeys,
					selectedRows: selectedRows,
					onChange: onSelectChange,
					columnWidth: 40
				}"
			>
				<a-table-column align="center" title="MI版本" data-index="miVersionName">
					<template slot-scope="text, record">
						<a @click="checkMiVersion(record)">{{ text }}</a>
					</template>
				</a-table-column>
				<a-table-column align="center" title="MI附图版本" data-index="miAttachVersionName">
					<template slot-scope="text, record">
						<a @click="checkMiAttachVersion(record)">{{ text }}</a>
					</template>
				</a-table-column>
				<a-table-column align="center" title="修订清单" data-index="documentChangeHistoryName">
					<template slot-scope="text, record">
						<a @click="checkDocumentChangeHistory(record)">{{ text }}</a>
					</template>
				</a-table-column>
				<a-table-column align="center" title="变更评审资料(PDF)" data-index="changeReviewDataName">
					<template slot-scope="text, record">
						<span v-if="text == null">
							<a-upload
								name="file"
								:headers="headers"
								:action="postUrl"
								:multiple="false"
								:showUploadList="false"
								@change="uploadFile($event, record)"
								accept=".pdf"
							>
								<a>上传</a>
							</a-upload>
						</span>
						<span v-else
							><a @click="sysFileInfoDownload(record.changeReviewDataId)">{{ text }}</a>
							<a-popconfirm
								placement="topRight"
								ok-text="删除"
								cancel-text="取消"
								@confirm="deleteFile($event, record)"
							>
								<template slot="title"> 确认删除文件{{ text }}吗 </template>
								<a-icon type="close" style="float: right;padding-top: 5px" />
							</a-popconfirm>
						</span>
					</template>
				</a-table-column>
				<a-table-column align="center" title="适用产品名称" data-index="productName" />
				<a-table-column align="center" title="试验线" data-index="produceLine" />
				<a-table-column align="center" title="更新日期" data-index="updateTime" :customRender="renderUpdateTime" />
				<a-table-column align="center" title="创建人" data-index="createName" />
				<a-table-column
					align="center"
					title="文件状态"
					data-index="approvalStatus"
					:custom-render="renderApprovalStatus"
				/>
				<a-table-column align="center" key="action" title="操作">
					<template slot-scope="text, record">
						<span>
							<a @click="openEdit(record)">编辑</a>
							<a-divider type="vertical" />
							<a @click="openSubmit(record)" :disabled="!record.approvalStatus == 0">提交</a>
							<a-divider type="vertical" />
							<a v-if="record.approvalStatus < 4" :disabled="record.approvalStatus < 3" @click="openEnable(record)"
								>启用</a
							>
							<a v-if="record.approvalStatus == 5" @click="openEnable(record)">启用</a>
							<a v-if="record.approvalStatus == 4" @click="openDisable(record)">禁用</a>
							<a-divider type="vertical" />
							<a @click="openExport(record)">导出</a>
						</span>
					</template>
				</a-table-column>
			</a-table>
		</div>
		<!-- table end -->

		<a-modal
			title="新增"
			:width="500"
			:visible="visible"
			:confirmLoading="confirmLoading"
			@ok="handleSubmit"
			@cancel="handleCancel"
		>
			<a-spin :spinning="confirmLoading">
				<a-form :form="form">
					<a-form-item label="MI版本" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
						<a-input
							placeholder="请输入MI版本"
							v-decorator="['miVersionName', { rules: [{ required: true, message: '请输入MI版本！' }] }]"
						/>
					</a-form-item>
					<a-form-item label="MI附图版本" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
						<a-input
							placeholder="请输入MI附图版本"
							v-decorator="['miAttachVersionName', { rules: [{ required: true, message: '请输入MI附图版本！' }] }]"
						/>
					</a-form-item>
					<a-form-item label="文件变更履历" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
						<a-input
							placeholder="请输入文件变更履历"
							v-decorator="[
								'documentChangeHistoryName',
								{ rules: [{ required: true, message: '请输入文件变更履历！' }] }
							]"
						/>
					</a-form-item>
					<a-form-item label="变更评审资料(PDF)" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
						<a-upload
							:headers="headers"
							:action="postUrl"
							:multiple="false"
							name="file"
							@change="addUploadFile($event)"
							v-decorator="['file1']"
							:disabled="uploadFlag"
							:fileList="fileList"
							:showUploadList="false"
							accept=".pdf"
						>
							<a-button><a-icon type="upload" />上传</a-button>
						</a-upload>
					</a-form-item>
					<a-form-item label="适用产品名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
						<a-input
							placeholder="请输入适用产品名称"
							v-decorator="['productName', { rules: [{ required: true, message: '请输入适用产品名称！' }] }]"
						/>
					</a-form-item>
					<a-form-item label="试验线" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
						<a-input
							placeholder="请输入试验线"
							v-decorator="['produceLine', { rules: [{ required: true, message: '请输入试验线！' }] }]"
						/>
					</a-form-item>
					<!--<a-form-item label="文件是否批准" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
						<a-select v-decorator="['isApproved', { rules: [{ required: true, message: '请选择文件是否批准！' }] }]">
							<a-select-option value="1">
								是
							</a-select-option>
							<a-select-option value="0">
								否
							</a-select-option>
						</a-select>
					</a-form-item>
					<a-form-item label="文件编号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
						<a-input
							placeholder="请输入文件编号"
							v-decorator="['documentNo', { rules: [{ required: true, message: '请输入文件编号！' }] }]"
						/>
					</a-form-item>
					<a-form-item label="版本" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
						<a-input
							placeholder="请输入版本"
							v-decorator="['version', { rules: [{ required: true, message: '请输入版本！' }] }]"
						/>
					</a-form-item>
					<a-form-item label="样品阶段" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
						<a-input
							placeholder="请输入样品阶段"
							v-decorator="['sampleStage', { rules: [{ required: true, message: '请输入样品阶段！' }] }]"
						/>
					</a-form-item>
					<a-form-item label="页码" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
						<a-input
							placeholder="请输入页码"
							v-decorator="['page', { rules: [{ required: true, message: '请输入页码！' }] }]"
						/>
					</a-form-item>-->
					<!--          <a-form-item label="项目启动(K0立项评审)" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>-->
					<!--            <a-date-picker placeholder="请选择项目启动(K0立项评审)日期" @change="onChangeSampleDate" style="width: 100%"-->
					<!--                           v-decorator="['startDate', {rules: [{required: true, message: '请选择项目启动(K0立项评审)日期!'}]}]" />-->
					<!--          </a-form-item>-->
				</a-form>
			</a-spin>
		</a-modal>
		<a-modal
			title="编辑"
			:width="500"
			:visible="editVisible"
			:confirmLoading="confirmLoading"
			@ok="editHandleSubmit"
			@cancel="editHandleCancel"
		>
			<a-spin :spinning="confirmLoading">
				<a-form :form="editForm">
					<a-form-item style="display: none;" :labelCol="labelCol" :wrapperCol="wrapperCol">
						<a-input v-decorator="['id']" />
					</a-form-item>
					<a-form-item label="MI版本" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
						<a-input
							placeholder="请输入MI版本"
							v-decorator="['miVersionName', { rules: [{ required: true, message: '请输入MI版本！' }] }]"
						/>
					</a-form-item>
					<a-form-item label="MI附图版本" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
						<a-input
							placeholder="请输入MI附图版本"
							v-decorator="['miAttachVersionName', { rules: [{ required: true, message: '请输入MI附图版本！' }] }]"
						/>
					</a-form-item>
					<a-form-item label="文件变更履历" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
						<a-input
							placeholder="请输入文件变更履历"
							v-decorator="[
								'documentChangeHistoryName',
								{ rules: [{ required: true, message: '请输入文件变更履历！' }] }
							]"
						/>
					</a-form-item>
					<!--          <a-form-item label="变更评审资料(PDF)" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>-->
					<!--            <a-input placeholder="请输入变更评审资料(PDF)" v-decorator="['changeReviewDataName', {rules: [{required: true, message: '请输入变更评审资料(PDF)！'}]}]" />-->
					<!--          </a-form-item>-->
					<a-form-item label="适用产品名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
						<a-input
							placeholder="请输入适用产品名称"
							v-decorator="['productName', { rules: [{ required: true, message: '请输入适用产品名称！' }] }]"
						/>
					</a-form-item>
					<a-form-item label="试验线" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
						<a-input
							placeholder="请输入试验线"
							v-decorator="['produceLine', { rules: [{ required: true, message: '请输入试验线！' }] }]"
						/>
					</a-form-item>
					<!--<a-form-item label="文件是否批准" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
						<a-select v-decorator="['isApproved', { rules: [{ required: true, message: '请选择文件是否批准！' }] }]">
							<a-select-option value="1">
								是
							</a-select-option>
							<a-select-option value="0">
								否
							</a-select-option>
						</a-select>
					</a-form-item>
					<a-form-item label="文件编号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
						<a-input
							placeholder="请输入文件编号"
							v-decorator="['documentNo', { rules: [{ required: true, message: '请输入文件编号！' }] }]"
						/>
					</a-form-item>
					<a-form-item label="版本" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
						<a-input
							placeholder="请输入版本"
							v-decorator="['version', { rules: [{ required: true, message: '请输入版本！' }] }]"
						/>
					</a-form-item>
					<a-form-item label="样品阶段" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
						<a-input
							placeholder="请输入样品阶段"
							v-decorator="['sampleStage', { rules: [{ required: true, message: '请输入样品阶段！' }] }]"
						/>
					</a-form-item>
					<a-form-item label="页码" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
						<a-input
							placeholder="请输入页码"
							v-decorator="['page', { rules: [{ required: true, message: '请输入页码！' }] }]"
						/>
					</a-form-item>-->
				</a-form>
			</a-spin>
		</a-modal>
    <check-man-standard ref="checkManStandard"></check-man-standard>

	</div>
</template>
<script>
import {
	copyMIStandardLib,
	deleteMIStandardLibFile,
	getMIStandardLibList,
	insertMIStandardLib,
	miOutputExportExcel,
	updateMIStandardLib
} from "@/api/modular/system/gCylinderMILibManage"
import moment from "moment/moment"
import Vue from "vue"
import { ACCESS_TOKEN } from "@/store/mutation-types"
import { sysFileInfoDownload } from "@/api/modular/system/fileManage"

import checkManStandard from "../batterydesign/checkManStandard"
import $ from "jquery"
export default {
	components: {checkManStandard},
	data() {
		return {
			selectedRowKeys: [],
			selectedRows: [],
			labelCol: {
				xs: {
					span: 24
				},
				sm: {
					span: 8
				}
			},
			wrapperCol: {
				xs: {
					span: 24
				},
				sm: {
					span: 14
				}
			},
			beforeDeleteFlag: false,
			visible: false,
			editVisible: false,
			confirmLoading: false,
			deleteLoading: false,
			copyLoading: false,
			form: this.$form.createForm(this, { name: "form" }),
			editForm: this.$form.createForm(this, { name: "editForm" }),
			fileList: [],
			saveData: {},
			uploadFlag: false,
			resultData: [],
			headers: {
				Authorization: "Bearer " + Vue.ls.get(ACCESS_TOKEN)
			},
			postUrl: "/api/sysFileInfo/uploadfile",
			// basePath: '/batterydesignStandard',
			routes: [
				{
					path: "/batterydesignStandard",
					breadcrumbName: "标准规范"
				},
				{
					path: "/g_cylinder_mi_library",
					breadcrumbName: "G圆柱 MI设计与组合"
				}
			],
			// 表格
			// 20 页面padding 20 页面padding 10 距离面包屑的高度 18 面包屑高度 40 顶部条高度 24:按钮高度
			tableHeight: document.documentElement.clientHeight - 20 - 10 - 18 - 40 - 20 - 24
		}
	},
	created() {
		document.documentElement.style.setProperty(`--height`, `${this.tableHeight - 63}px`)
	},
	mounted() {
		document.getElementsByClassName("ant-layout-content")[0].style.backgroundColor = "white"
		this.getData()
	},
	methods: {
		onSelectChange(selectedRowKeys, selectedRows) {
			this.selectedRowKeys = selectedRowKeys
			this.selectedRows = selectedRows
			// console.log('selectedRows',selectedRows)
			// console.log('selectedRowKeys',this.selectedRowKeys)
		},
		checkMiVersion(record) {
			// eventBus.$emit('getMIStandardLib', record)
			this.$router.push({
				path: "/g_cylinder_mi_version",
				query: {
          libraryId: record.id,
          miVersionName: record.miVersionName
				}
			})
		},
		checkMiAttachVersion(record) {
			// this.$router.push('/g_cylinder_mi_library')
			// this.$router.go(-1)
			this.$router.push({
				path: "/g_cylinder_mi_attach_version",
				query: {
          libraryId: record.id,
          miAttachVersionName: record.miAttachVersionName
				}
			})
		},
		checkDocumentChangeHistory(record) {
			this.$router.push({
				path: "/g_cylinder_doc_change_history",
				query: {
          libraryId: record.id,
          documentChangeHistoryName: record.documentChangeHistoryName
				}
			})
		},
		getData() {
			getMIStandardLibList().then(res => {
				this.resultData = res.data
			})
		},
		renderApprovalStatus(text) {
			if (text === 0) {
				return "待提交"
			} else if (text === 1) {
				return "审核中"
			} else if (text === 2) {
				return "批准中"
			} else if (text === 3) {
				return "已审批"
			} else if (text === 4) {
				return "启用中"
			} else if (text === 5) {
				return "禁用中"
			} else {
				return ""
			}
		},
		renderUpdateTime(text) {
			return text == null ? "" : moment(new Date(text)).format("YYYY-MM-DD")
		},
		openAdd() {
			const lang = this.$route
			// console.log('this.$route:' + lang);
			this.visible = true
		},
		roleDefinition() {
      this.$refs.checkManStandard.open()
    },
		copyData() {
			if (this.selectedRows.length === 0) {
				this.$message.warning("请至少选择一条数据进行复制！")
				return
			}
			this.copyLoading = true
			this.selectedRows.forEach(item => {
				copyMIStandardLib(item)
			})
			setTimeout(() => {
				this.selectedRows = []
				this.selectedRowKeys = []
				this.getData()
				this.copyLoading = false
			}, 800)
		},
		beforeDelete() {
			if (this.selectedRows.length === 0) {
				this.$message.warning("请至少选择一条数据进行删除！")
        return
			} else {
        this.beforeDeleteFlag = true
      }
		},
		deleteData() {
			this.deleteLoading = true
			this.selectedRows.forEach(item => {
				updateMIStandardLib({ id: item.id, status: 2 })
			})
      this.beforeDeleteFlag = false
			setTimeout(() => {
				this.selectedRows = []
				this.selectedRowKeys = []
				this.getData()
				this.deleteLoading = false
			}, 800)
		},
		editHandleCancel() {
			this.editForm.resetFields(["xxx"])
			this.editForm.resetFields()
			this.editVisible = false
		},
		handleCancel() {
			this.uploadFlag = false
			this.fileList = []
			this.form.resetFields(["file1"])
			this.form.resetFields()
			this.visible = false
			this.saveData = {}
		},
		handleSubmit() {
			const {
				form: { validateFields }
			} = this
			this.confirmLoading = true
			let query = this.$route.query
			validateFields((errors, values) => {
				// values.startDate = this.startDate
				// values.type = this.type
				if (!errors) {
					let $params = Object.assign(values, query)
					$params = Object.assign($params, this.saveData)
					insertMIStandardLib($params)
						.then(res => {
							this.confirmLoading = false
							if (res.success) {
								this.$message.success("新增成功")
								this.getData()
								// this.$refs.gCylinderRef.refresh()
								this.handleCancel()
								// this.$emit('ok', values)
							} else {
								this.$message.error("新增失败：" + res.message)
							}
						})
						.finally(res => {
							this.confirmLoading = false
						})
				} else {
					this.confirmLoading = false
				}
			})
		},
		openEdit(record) {
			setTimeout(() => {
				this.editForm.setFieldsValue(record)
			}, 100)
			this.editVisible = true
		},
		openSubmit(record) {},
		openEnable(record) {},
		openDisable(record) {},
		openExport(record) {
			miOutputExportExcel({ impBatteryId: -1, libraryId: record.id }).then(res => {
				const fileName = "MI规范库导出.xlsx"
				if (!res) return
				const blob = new Blob([res.data], { type: "application/vnd.ms-excel" }) // 构造一个blob对象来处理数据，并设置文件类型
				if (window.navigator.msSaveOrOpenBlob) {
					//兼容IE10
					navigator.msSaveBlob(blob, fileName)
				} else {
					const href = URL.createObjectURL(blob) //创建新的URL表示指定的blob对象
					const a = document.createElement("a") //创建a标签
					a.style.display = "none"
					a.href = href // 指定下载链接
					a.download = fileName //指定下载文件名
					a.click() //触发下载
					URL.revokeObjectURL(a.href) //释放URL对象
				}
			})
		},
		editHandleSubmit() {
			const {
				editForm: { validateFields }
			} = this
			this.confirmLoading = true
			validateFields((errors, values) => {
				if (!errors) {
					updateMIStandardLib(values)
						.then(res => {
							this.confirmLoading = false
							if (res.success) {
								this.$message.success("编辑成功")
								this.getData()
								this.editHandleCancel()
							} else {
								this.$message.error("编辑失败：" + res.message)
							}
						})
						.finally(res => {
							this.confirmLoading = false
						})
				} else {
					this.confirmLoading = false
				}
			})
		},
		uploadFile(info, record) {
			// console.log('info:' + info)
			// console.log('record:' + record)
			if (info.file.status === "done") {
				let file = info.file
				let update = {}
				update.id = record.id
				update.changeReviewDataId = file.response.data.id
				update.changeReviewDataName = file.name
				updateMIStandardLib(update).then(res => {
					if (res.success) {
						this.getData()
					} else {
						this.$message.error("上传文件失败：" + res.message)
					}
				})
			} else if (info.file.status === "error") {
				this.$message.error(`${info.file.name} 文件上传失败`)
			}
		},
		addUploadFile(info) {
			this.fileList = info.fileList
			if (info.file.status === "done") {
				let file = info.fileList[0]
				this.saveData.changeReviewDataId = file.response.data.id
				this.saveData.changeReviewDataName = file.name
				this.uploadFlag = true
				var element = document.getElementsByClassName("ant-form-item-children-icon")[0]
				element.style.position = "absolute"
				element.style.left = "80px"
			} else if (info.file.status === "error") {
				this.$message.error(`${info.file.name} 文件上传失败`)
			}
		},
		deleteFile(e, record) {
			let update = {}
			update.id = record.id
			deleteMIStandardLibFile(update).then(res => {
				if (res.success) {
					this.getData()
				} else {
					this.$message.error("删除文件失败：" + res.message)
				}
			})
		},
		sysFileInfoDownload(id) {
			sysFileInfoDownload({ id: id })
				.then(res => {
					this.downloadfile(res)
					// eslint-disable-next-line handle-callback-err
				})
				.catch(err => {
					this.$message.error("下载错误：获取文件流错误")
				})
		},
		downloadfile(res) {
			var blob = new Blob([res.data], { type: "application/octet-stream;charset=UTF-8" })
			var contentDisposition = res.headers["content-disposition"]
			var patt = new RegExp("filename=([^;]+\\.[^\\.;]+);*")
			var result = patt.exec(contentDisposition)
			var filename = result[1]
			var downloadElement = document.createElement("a")
			var href = window.URL.createObjectURL(blob) // 创建下载的链接
			var reg = /^["](.*)["]$/g
			downloadElement.style.display = "none"
			downloadElement.href = href
			downloadElement.download = decodeURI(filename.replace(reg, "$1")) // 下载后文件名
			document.body.appendChild(downloadElement)
			downloadElement.click() // 点击下载
			document.body.removeChild(downloadElement) // 下载完成移除元素
			window.URL.revokeObjectURL(href)
		}
	}
}
</script>

<style lang="less" scoped>
:root {
	--height: 600px;
}

.container {
	background-color: #f0f2f5;
}
.ant-form-item {
	margin-bottom: 0px;
}

//面包屑
.breadcrumb a {
	color: rgba(0, 0, 0, 0.65);
}
.rollback-icon {
	font-size: 14px;
}
// /deep/.ant-breadcrumb {
// 	font-size: 12px !important;
// 	color: rgba(0, 0, 0, 0.65);
// }
/deep/.ant-breadcrumb a:hover {
	color: #40a9ff;
}

// 提示
.tips {
	font-size: 8px;
}

// 按钮
.left-btn {
	float: left;
	margin-bottom: 10px;
}
.right-btn {
	float: right;
	margin-bottom: 10px;
}

.mr10 {
	margin-right: 10px;
}

// 表格
.table-wrapper {
	margin-top: 10px;
	padding: 10px;
	border-radius: 10px;
	box-shadow: 0 2px 10px 0 rgba(51, 51, 51, 0.1);
	background-color: #fff;
}
/deep/.ant-table-body {
	height: var(--height) !important;
	overflow-y: scroll;
}
// 表头固定
/deep/.ant-table-thead {
	position: sticky;
	top: 0;
}

/deep/.ant-table-thead tr th {
	padding: 10px;
	font-weight: bold;
	background: rgba(232, 232, 232, 0.5) !important;
	color: #333;
}
/deep/.ant-table-tbody tr td {
	padding: 10px;
}

/deep/.ant-table-bordered .ant-table-header > table,
.ant-table-bordered .ant-table-body > table,
.ant-table-bordered .ant-table-fixed-left table,
.ant-table-bordered .ant-table-fixed-right table {
	border: none;
}

// 表格页脚
// 分页大小
/deep/.ant-table-pagination.ant-pagination {
	margin: 10px 0;
}
/deep/.ant-pagination-disabled,
.ant-pagination-next {
	min-width: 20px;
	height: 20px;
	line-height: 20px;
}

/deep/.ant-pagination-prev {
	min-width: 20px;
	height: 20px;
	line-height: 20px;
}
/deep/.ant-pagination-next {
	min-width: 20px;
	height: 20px;
	line-height: 20px;
}
/deep/.ant-pagination-jump-prev {
	min-width: 20px;
	height: 20px;
	line-height: 20px;
}
/deep/.ant-pagination-jump-next {
	min-width: 20px;
	height: 20px;
	line-height: 20px;
}

/deep/.ant-pagination-item {
	min-width: 25px;
	height: 25px;
	line-height: 25px;
}
</style>
