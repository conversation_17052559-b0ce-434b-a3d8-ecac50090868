import { axios } from '@/utils/request'

export function getMIAttachVersionCurList (parameter, impBatteryId) {
  return axios({
    url: '/miAttachVersionCur/getMIAttachVersionCurList/' + impBatteryId,
    method: 'get',
    params: parameter
  })
}

export function insertMIAttachVersionCur (parameter) {
  return axios({
    url: '/miAttachVersionCur/insertMIAttachVersionCur',
    method: 'post',
    data: parameter
  })
}

export function updateMIAttachVersionCur (parameter) {
  return axios({
    url: '/miAttachVersionCur/updateMIAttachVersionCur',
    method: 'post',
    data: parameter
  })
}
