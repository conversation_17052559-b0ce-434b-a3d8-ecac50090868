<template>
  <div ref="wrapper" class="wrapper">
    <div class="flex-sb-center-row">
      <div class="head_title">{{ record.type + ": " + record.reportName }}</div>
    </div>

    <div class="all-wrapper">
      <div class="left-content block">
        <div v-show="testReportType == 'SOC-OCV map' || testReportType == 'SOC-OCV map charge'">
          <pageComponent editObj="charge"  @down="handleNormalDown('charge')" @edit="handleEditEcharts('charge')"></pageComponent>
          <div id="charge" ref="charge" class="mt10" style="width: 597px;height: 417px;border: 0.5px solid #ccc;"></div>
        </div>

        <div v-show="testReportType == 'SOC-OCV map' || testReportType == 'SOC-OCV map discharge'">
          <pageComponent class="mt10" editObj="discharge"  @down="handleNormalDown('discharge')" @edit="handleEditEcharts('discharge')"></pageComponent>
          <div id="discharge" ref="discharge" class="mt10" style="width: 597px;height: 417px;border: 0.5px solid #ccc;">
          </div>
        </div>

      </div>
      <div class="right-content">
        <div class="block" id="export">
          <div class="flex-column">
            <div>
              <div v-if="testReportType == 'SOC-OCV map' || testReportType == 'SOC-OCV map charge'">
                 <div>原始数据</div>
                <strong>充电SOC-OCV</strong>
                <div class="mt10">
                  <a-table :columns="chargeTableColumns" bordered :data-source="originDataCharge" :pagination="false">
                  </a-table>
                </div>
              </div>
              <div v-if="testReportType == 'SOC-OCV map' || testReportType == 'SOC-OCV map discharge'">
                 <strong class="mt10">放电SOC-OCV</strong>
                <div class="mt10">
                  <a-table :columns="dischargeTableColumns" bordered :data-source="originDataDischarge"
                    :pagination="false"> </a-table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="drawerVisible">
      <PreviewDrawer  :screenImageId = "screenImageId" :templateParam = "reportChartTemplateList[editObj]" :isLegendLeft =  "true" :legendOptions="editObj === 'charge' ? chargeLegendList : dischargeLegendList"
        :data="editObj === 'charge' ? chargeSeriesList : dischargeSeriesList"
        :original="editObj === 'charge' ? chargeOriginalData : dischargeOriginalData"
        :editData="editObj === 'charge' ? chargeEditData : dischargeEditData"
        :checkObj="chartCheckObj[editObj]"
         @submit="handleDrawerSubmit"
        @reset="handleDrawerReset" @close="drawerVisible = false" @changeTemplate ="handleChangeTemplate" @screenshot="handleScreenshot"></PreviewDrawer>
    </div>

    <!-- <pbiReturnTop v-if="isShowReturnTop" @returnTop="handleReturnTop"></pbiReturnTop> -->

  </div>
</template>
<script>
import { testReportGet, testReportUpdateDate } from "@/api/modular/system/limsManager"
import { mixin,chart } from "./mixin/index.js"
import {chartTemplate} from "@/views/system/vTestReport/mixin/chartTemplate";

export default {
  mixins: [mixin,chart,chartTemplate],
  data: function () {
    return {
      id: null,
      testReportType:'',
      update: false,
      /* 右边表格 */
      chargeTableColumns: [
        {
          title: "SOC",
          dataIndex: "soc",
          align: "center",
          width: 60,
        },
        {
          title: "Avg.",
          width: 60,
          align: "center",
          dataIndex: "average"
        }
      ],
      dischargeTableColumns: [],
      originData: [],
      originDataCharge: [],
      originDataDischarge: [],


       /* 图表 */
      chargeEchart: null,
      dischargeEchart: null,
      isEditChargeXNum: 0,
      isEditDischargeXNum: 0,


       /* 在线编辑图表 */
      chargeLegendList: [], // 原始值
      dischargeLegendList: [], //原始值
      chartCheckObj: {
        charge:{},
        discharge:{}
      },
      // chargeCheckObj: {}, //点击图标的选中对象
      // dischargeCheckObj: {}, //点击图标的选中对象
      chargeSeriesList: [], // 原始值
      dischargeSeriesList: [], // 原始值
      chargeEditData: {},
      chargeOriginalData: {},
      dischargeEditData: {},
      dischargeOriginalData: {},

    }
  },
  async mounted() {
    await this.getChartTemplateRelationList(this.$route.query.id,['charge','discharge'])
    this.init()
    // const box = this.$refs.wrapper
    //   box.addEventListener("scroll", e => {
    //     if (e.target.scrollTop > 100 && !this.isShowReturnTop) {
    //       this.isShowReturnTop = true
    //     }
    //     if(e.target.scrollTop < 100 && this.isShowReturnTop){
    //       this.isShowReturnTop = false
    //     }
    //   })
  },

  methods: {
    init() {
      this.id = this.$route.query.id
      this.testReportType = this.$route.query.type
      testReportGet({ id: this.id })
        .then(res => {
          this.record = res.data
          this.allDataJson = JSON.parse(res.data.allDataJson)
          this.queryParam = JSON.parse(res.data.queryParam)
          this.originDataCharge = JSON.parse(res.data.allDataJson).chargeTableList
          this.originDataDischarge = JSON.parse(res.data.allDataJson).dischargeTableList

          this.update = false
        })
        .then(async res => {

          if(this.testReportType == 'SOC-OCV map' || this.testReportType == 'SOC-OCV map charge'){
            if (this.originDataCharge.length > 0) {
              this.initChargeTable()
            }

            this.chargeEditData = this._getInitData('charge','edit')
            this.chargeOriginalData = this._getInitData('charge')

            await this.initChargeEchart()
          }
          if(this.testReportType == 'SOC-OCV map' || this.testReportType == 'SOC-OCV map discharge'){
            if (this.originDataDischarge.length > 0) {
              this.initDischargeTable()
            }

            this.dischargeEditData = this._getInitData('discharge','edit')
            this.dischargeOriginalData = this._getInitData('discharge')

            await this.initDischargeEchart()
          }
        })
    },

    initChargeTable() {
      this.chargeTableColumns = [
        {
          title: "SOC",
          dataIndex: "soc",
          align: "center",
          width: 60,
        },
        {
          title: "Avg.",
          width: 60,
          align: "center",
          dataIndex: "average"
        }
      ]
      for (let i = 0; i < this.originDataCharge[0].dataList.length; i++) {
        this.chargeTableColumns.push({
          title: i + 1 + "#",
          align: "center",
          width: 60,
          dataIndex: "dataList[" + i + "].u"
        })
      }
    },
    initDischargeTable() {
      this.dischargeTableColumns = [
        {
          title: "SOC",
          dataIndex: "soc",
          align: "center",
          width: 60,
        },
        {
          title: "Avg.",
          width: 60,
          align: "center",
          dataIndex: "average"
        }
      ]

      for (let i = 0; i < this.originDataDischarge[0].dataList.length; i++) {
        this.dischargeTableColumns.push({
          title: i + 1 + "#",
          align: "center",
          width: 60,
          dataIndex: "dataList[" + i + "].u"
        })
      }
    },

    initChargeEchart(
      legendData = {},
      checkData = [], //选中的数据
      axisData = {},
      titleData = {},
      gridData = {}
    ) {

      this.chargeEchart = this.echarts.init(this.$refs.charge, 'walden',{ devicePixelRatio: 2 })
      this.dischargeEchart = this.echarts.init(this.$refs.discharge, 'walden')

      const templateParam = this.reportChartTemplateList['charge'].templateParamJson
      // 原始图例  使用位置：在线编辑图表--图例数据
      let chargeEchartsList =  _.cloneDeep(this.allDataJson.chargeEcharts)
      chargeEchartsList.sort((item1,item2) => { return item1.batteryNum -  item2.batteryNum })

      //页面上有哪些图例 使用位置：在线编辑图表--选中的图例数据（默认全部选中）
      this.chargeLegendList = _.cloneDeep(this.allDataJson.chargeEcharts).map(e => e.batteryNum + '#')

      // 把模板的数据拼接进去
      const {
        titleData: newTitleData,
        gridData: newGridData,
        legendData: newLegendData,
        axisData: newAxisData,
        legend: newLegend
      } = this._getTemplateParams('charge', titleData, gridData, legendData, axisData, legend);
      titleData = newTitleData;
      gridData = newGridData;
      legendData = newLegendData;
      axisData = newAxisData;

      // 首次渲染,所有图例都显示
      if(!legendData.legendRevealList || templateParam.legendData.legendRevealList){
          const revealNum = this.handleRevealNum(520,this.chargeLegendList)
          this.chargeEditData.legendRevealList = templateParam.legendData.legendRevealList ?? _.cloneDeep(this.chargeLegendList).slice(0,revealNum)
        }

      this.chargeEditData.legend = _.cloneDeep(this.chargeLegendList)

      // 数据
      let seriesList = []
      let lineColorList = [] // 折线颜色

      const echartsColorList = this.chargeEditData.legend.length <= 2 ? this.echartsColorShortList : this.echartsColorLongList
      const isCheck = checkData.length === 0

      this.chargeSeriesList = []
      this.chargeOriginalData.checkData = []
      this.chargeOriginalData.series = []
      // this.chargeEditData.duplicateDataOptions = []

      for (let i = 0; i < chargeEchartsList.length; i++) {
        // 设置折线的颜色
        const have = lineColorList.find(v => v.name === chargeEchartsList[i].batteryNum)
        if (have == undefined) {
          lineColorList.push({ name: chargeEchartsList[i].batteryNum, color: echartsColorList[lineColorList.length % echartsColorList.length] })
        }

        const templateContent = templateParam.checkData.length > 0 ? (templateParam.checkData.filter(item => item.id === chargeEchartsList[i].batteryNum + "#" + (i + 1))[0] || {}) : {}
        const editContentIndex = checkData.findIndex(findItem => findItem.id ===  chargeEchartsList[i].batteryNum + '#' + (i + 1))
        const editContent = editContentIndex !== -1 ? checkData[editContentIndex] : {}

        let series = {
          id: chargeEchartsList[i].batteryNum + '#' + (i + 1),
          name: chargeEchartsList[i].batteryNum + '#',
          soc: chargeEchartsList[i].batteryNum,
          type: 'line',
          barGap: 0,
          markPoint: {
            data: []
          },
          connectNulls: templateContent.connectNulls ?? (isCheck ? false : Boolean(Number(editContent.connectNulls))),
          symbol: templateContent.symbol ?? (isCheck ? "rect" : editContent.symbol),
          symbolSize: templateContent.symbolSize ?? (isCheck ? 5 : editContent.symbolSize),
          lineStyle: {
            width: templateContent.lineWidth ?? (isCheck ? 1 : editContent.lineWidth),
            type: templateContent.lineType ?? (isCheck ? "solid" : editContent.lineType),
            color: templateContent.lineColor ?? (
              isCheck
                ? lineColorList[lineColorList.findIndex(v => v.name === chargeEchartsList[i].batteryNum)].color
                : editContent.lineColor
            )
          },
          itemStyle: {
            color: templateContent.itemColor ?? (
              isCheck
                ? lineColorList[lineColorList.findIndex(v => v.name === chargeEchartsList[i].batteryNum)].color
                : editContent.itemColor
            )
          },
          emphasis: {
            focus: "series"
          },

          batteryNum: chargeEchartsList[i].batteryNum,
          data: chargeEchartsList[i].ocvDataList.map((mapItem, index) => { return { id: index, value: mapItem } }),
        }

        // 设置最大最小值
        if (checkData.length > 0 && editContent.maxPoint || templateContent.maxPoint) {
          series.markPoint.data.push({ type: "max", name: "Max" })
        }
        if (checkData.length > 0 && editContent.minPoint || templateContent.minPoint) {
          series.markPoint.data.push({ type: "min", name: "Min" })
        }


        this.chargeSeriesList.push({
          id: chargeEchartsList[i].batteryNum + '#' + (i + 1),
          index: i + 1,
          name: chargeEchartsList[i].batteryNum + '#',
          soc: chargeEchartsList[i].batteryNum + "#",
          data: chargeEchartsList[i].ocvDataList.map(v => v[1] ? v[1].toString() : null),
          duplicateData:chargeEchartsList[i].ocvDataList.map((mapItem, index) => { return { id: index, name: chargeEchartsList[i].batteryNum + '#', value: mapItem } }),
          synchronization: templateContent.synchronization ?? (isCheck ? i : editContent.synchronization),
          maxPoint: templateContent.maxPoint ?? (isCheck ? false : editContent.maxPoint),
          minPoint: templateContent.minPoint ?? (isCheck ? false : editContent.minPoint),
          connectNulls: templateContent.connectNulls ?? false,
          symbol: templateContent.symbol ?? (isCheck ? "rect" : editContent.symbol),
          symbolSize: templateContent.symbolSize ?? (isCheck ? 5 : editContent.symbolSize),
          itemColor: templateContent.itemColor ?? (
            isCheck
              ? lineColorList[lineColorList.findIndex(v => v.name === chargeEchartsList[i].batteryNum)].color
              : editContent.itemColor
          ),
          lineType: templateContent.lineType ?? (isCheck ? "solid" : editContent.lineType),
          lineWidth: templateContent.lineWidth ?? (isCheck ? 1 : editContent.lineWidth),
          lineColor: templateContent.lineColor ?? (
            isCheck
              ? lineColorList[lineColorList.findIndex(v => v.name === chargeEchartsList[i].batteryNum)].color
              : editContent.lineColor
          )
        })

        // 原始值
        this.chargeOriginalData.checkData.push({
          id: chargeEchartsList[i].batteryNum + '#' + (i + 1),
          index: i + 1,
          soc: chargeEchartsList[i].batteryNum + "#",
          name: chargeEchartsList[i].batteryNum + "#",
          connectNulls: false,
          synchronization: i,
          maxPoint: false,
          minPoint: false,
          symbol: "rect",
          symbolSize: 5,
          itemColor: lineColorList[lineColorList.findIndex(v => v.name === chargeEchartsList[i].batteryNum)].color,
          lineType: "solid",
          lineWidth: 1,
          lineColor: lineColorList[lineColorList.findIndex(v => v.name === chargeEchartsList[i].batteryNum)].color
        })

        this.chargeOriginalData.series.push({
          id: chargeEchartsList[i].batteryNum + '#' + (i + 1),
          index: i + 1,
          soc: chargeEchartsList[i].batteryNum + "#",
          name: chargeEchartsList[i].batteryNum + "#",
          connectNulls: false,
          synchronization: i,
          maxPoint: false,
          minPoint: false,
          symbol: "rect",
          symbolSize: 5,
          itemColor: lineColorList[lineColorList.findIndex(v => v.name === chargeEchartsList[i].batteryNum)].color,
          lineType: "solid",
          lineWidth: 1,
          lineColor: lineColorList[lineColorList.findIndex(v => v.name === chargeEchartsList[i].batteryNum)].color
        })

        // this.chargeEditData.duplicateDataOptions.push({
        //   id: chargeEchartsList[i].batteryNum,
        //   data: chargeEchartsList[i].ocvDataList.map((mapItem, index) => { return { id: index, value: index, label: mapItem[1].toString() } })
        // })

        seriesList.push(series)
      }



      this.chargeEditData.series = _.cloneDeep(this.chargeSeriesList)

      /* 图例排序 开始 */
      if (legendData.legendSort) {
        this.chargeEditData.legend = _.cloneDeep(legendData.legendSort) // 将页面上的图例数组按照用户设置的顺序排序
      }
      this.chargeEditData.legendSort = !legendData.legendSort ? _.cloneDeep(this.chargeLegendList) : _.cloneDeep(this.chargeEditData.legend)
      /* 图例排序 结束 */

      /* 图例变更名称 开始 */
      if (legendData.legendEditName) {
        legendData.legendEditName.forEach(v => {
          if (v.newName && !v.isReset) {
            let temIndex1 = this.chargeLegendList.findIndex(findItem => findItem == v.originName)
            this.chargeLegendList[temIndex1] = v.newName
            let temIndex2 = this.chargeEditData.legend.findIndex(findItem => findItem == v.originName)
            this.chargeEditData.legend[temIndex2] = v.newName
            this.chargeEditData.series.forEach(findItem => {
              findItem.name = findItem.name == v.originName ? v.newName : findItem.name
            })
            seriesList.forEach(findItem => {
              findItem.name = findItem.name == v.originName ? v.newName : findItem.name
            })
          }

          if (!v.newName && v.isReset) {
            v.previousName = ''
            v.isReset = false
          }
        })

        // 赋予修改后的图例修改名称数组
        this.chargeEditData.legendEditName = legendData.legendEditName
      }

      // 图例修改名称数组  使用位置：在线编辑图表--图例--名称 ,首次进入，将图例的值给图例修改名称数组
      // originName 原始值 newName 新值 previousName 上一个值（用于清空的情况下使用）isReset 是否重置（用于清空的时候使用）
      if (this.chargeEditData.legendEditName.length === 0) {
        this.chargeEditData.legendEditName = this.chargeLegendList.map(v => { return {id:v, originName: v, previousName: '', newName: '', isReset: false } })
      }
      /* 图例变更名称 结束 */

      // 处理选中图例，移除没选中的图例数据
      if (legendData.legendEdit) {
        // 图例
        for (let i = 0; i < this.chargeEditData.legend.length; i++) {
          if (!legendData.legendEdit.includes(this.chargeEditData.legend[i])) {
            this.chargeEditData.legend.splice(i, 1)
            i--
          }
        }

        // 移除页面上的对应的图例的图表数据
        for (let i = 0; i < seriesList.length; i++) {
          if (!legendData.legendEdit.includes(seriesList[i].name)) {
            seriesList.splice(i, 1)
            i--
          }
        }

        // 判断依据
        for (let i = 0; i < checkData.length; i++) {
          // 这里的soc 就是 batteryNum
          if (!legendData.legendEdit.includes(checkData[i].name)) {
            checkData.splice(i, 1)
            i--
          }
        }
      }

      // // 处理选中数据
      // if (checkData.length > 0) {
      //   seriesList.forEach((v, index) => {
      //     const handIndex = checkData.findIndex(findItem => findItem.id == v.id)
      //     for (let i = 0; i < v.data.length; i++) {
      //       if (!checkData[handIndex].duplicateData.includes(v.data[i].id)) {
      //         v.data.splice(i, 1)
      //         i--
      //       }
      //     }
      //   })
      // }

      // 结合图例数据（线+图例都存在）和图例显隐（只存在线，剔除图例）
      const legend = []
      this.chargeEditData.legend.forEach(v => {
        const conditions1 = this.chargeEditData.legendRevealList.includes(v)
        if(conditions1){
          legend.push(v)
        }else{
          const conditions2 = this.chargeEditData.legendEditName.findIndex(findItem => findItem.newName == v)
          if(conditions2 > -1 && this.chargeEditData.legendRevealList.includes(this.chargeEditData.legendEditName[conditions2].originName)) legend.push(v)
        }
      })

      let chargeOption = {
        backgroundColor: '#ffffff',
        animationDuration: 2000,
        title: {
          text: titleData.chartTitle || this.queryParam.reportBasic.reportName + ' SOC-OCV',
          left: 'center',
          top: titleData.titleTop ||  20,
          fontSize: 18,
          fontWeight: 500,
          color: "#000"
        },
        grid: {
          show: true,
          top: gridData.gridTop ||  60,
          left: gridData.gridLeft || 80,
          right: gridData.gridRight || 40,
          bottom: gridData.gridBottom || 70,
          borderWidth: 0.5,
          borderColor: "#ccc"
        },
        textStyle: {
          fontFamily: "Times New Roman"
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            var result = params[0].axisValue + '%SOC' + '<br>'; // 添加 x 轴的数值
            params.forEach(function (item, dataIndex) {
              // var type= item.data.name+'#'
              result += '<div style="width:150px;display: inline-block;">' + item.marker + item.seriesName + '</div>' + item.data.value[1] + '<br>'; // 添加每个系列的数值
            });
            return result;
          }
        },

        legend: {
          show: true,
          backgroundColor: legendData.legendBgColor || "#f5f5f5",
          // data: this.chargeEditData.legend,
          data: legend,
          itemWidth: legendData.legendWidth || 30,
          itemHeight: legendData.legendHeight || 5,
          itemGap: legendData.legendGap || 10,
          orient: legendData.legendOrient || 'horizontal',
          // position: "inside",
          top: legendData.legendTop || 70,
          left: legendData.legendLeft || 'center',
          textStyle: {
            fontSize: 14,
            color: "#000000"
          }
        },

        xAxis: [
          {
            type: axisData.xType || 'category',
            axisTick: { show: false },
            splitLine: {
              show: true, // 显示分隔线
              lineStyle: {
                type: "solid",
                width: 0.5
              }
            },
            axisLabel: {
              show: true,
              width: 0.5,
              textStyle: {
                fontSize: "15",
                color: "#000000"
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              }
            },
            name: titleData.XTitle || 'SOC(%)',
            nameLocation: "middle", // 将名称放在轴线的中间位置
            nameGap: 35,
            nameTextStyle: {
              fontSize: 14,
              fontWeight: 500,
              color: "#000000" // 可以根据需要调整字体大小
            }
          }
        ],
        yAxis: [
          {
            type: axisData.yType || 'value',
            name: titleData.YTitle || 'OCV(V)',
            position: "left",
            nameGap: titleData.yTitleLetf || 40,
            splitLine: {
              show: true, // 显示分隔线
              lineStyle: {
                type: "solid",
                width: 0.5
              }
            },
            axisTick: {
              show: true // 显示刻度
            },
            axisLabel: {
              show: true,
              width: 0.5,
              textStyle: {
                fontSize: "15",
                color: "#000000"
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              }
            },
            nameLocation: "middle", // 将名称放在轴线的起始位置
            nameRotate: 90, // 旋转角度，使名称竖排
            nameTextStyle: {
              fontSize: 14, // 可以根据需要调整字体大小
              fontWeight: 500,
              color: "#000000"
            }
          }
        ],
        series: seriesList
      }

      // 传回给在线编辑图表，当前图标上有的点
      // this.chargeEditData.duplicateCheckedList = seriesList.map(mapItem => mapItem.data.filter(filterItem => filterItem.value[1] !== '').map(mapItem2 => mapItem2.id))

      // 处理坐标轴
      if (axisData.xMin) {
        chargeOption.xAxis[0].min = axisData.xMin
      }
      if (axisData.xMax) {
        chargeOption.xAxis[0].max = axisData.xMax
      }
      if (axisData.xInterval) {
        chargeOption.xAxis[0].interval = axisData.xInterval
      }
      if (axisData.yMin) {
        chargeOption.yAxis[0].min = axisData.yMin
      }
      if (axisData.yMax) {
        chargeOption.yAxis[0].max = axisData.yMax
      }
      if (axisData.yInterval) {
        chargeOption.yAxis[0].interval = axisData.yInterval
      }

      // 坐标轴类型赋值
      this.chargeEditData.xType = chargeOption.xAxis[0].type
      this.chargeEditData.yType = chargeOption.yAxis[0].type

      this.chargeEchart.clear()
      // this.chargeEchart.getZr().off('click')
      // this.chargeEchart.getZr().on('click', params => {
      //   const { target, topTarget } = params

      //   // Z 0:坐标轴
      //   if (topTarget?.z === 0 && this.drawerVisible) {
      //     // this.$set(this.chargeCheckObj,'axis',0)
      //     this.$set(this.chargeCheckObj, 'editObj', 'axis')
      //   }
      //   // Z 3:线
      //   if (topTarget?.z === 3 && this.drawerVisible) {
      //     const axs = target.parent?.parent?.__ecComponentInfo?.index
      //     this.$set(this.chargeCheckObj, 'tag', axs)
      //     this.$set(this.chargeCheckObj, 'editObj', 'tag')
      //   }
      //   // Z 4:图例
      //   if (topTarget?.z === 4 && this.drawerVisible) {
      //     const axs = target.parent?.__legendDataIndex
      //     // this.$set(this.chargeCheckObj,'legend',axs)
      //     this.$set(this.chargeCheckObj, 'editObj', 'legend')
      //   }
      // });
      this.chargeEchart.getZr().off('dblclick')
      this.chargeEchart.getZr().on('dblclick', ({target, topTarget}) => {
        this._handleDblclickEchart(target, topTarget, 'charge')
      });
      this.chargeEchart.setOption(chargeOption)

      // 如果坐标轴类型为数值轴，则计算出最大值最小值，以及间距
      if (chargeOption.xAxis[0].type === "value") {
        const dcrXAxis = this.chargeEchart.getModel().getComponent("xAxis").axis.scale._extent
        this.chargeEditData.xMin = dcrXAxis[0]
        this.chargeEditData.xMax = dcrXAxis[1]
      }

      if (chargeOption.yAxis[0].type === "value") {
        const dcrYAxis = this.chargeEchart.getModel().getComponent("yAxis").axis.scale
        this.chargeEditData.yMin = dcrYAxis._extent[0]
        this.chargeEditData.yMax = dcrYAxis._extent[1]
        this.chargeEditData.yInterval = dcrYAxis._interval

        this.chargeOriginalData.yMin = dcrYAxis._extent[0]
        this.chargeOriginalData.yMax = dcrYAxis._extent[1]
        this.chargeOriginalData.yInterval = dcrYAxis._interval

      }

      if (this.isEditChargeXNum === 0 && axisData.xType === "value") {
        this.isEditChargeXNum++
        this.chargeOriginalData.xMin = this.chargeEditData.xMin
        this.chargeOriginalData.xMax = this.chargeEditData.xMax
        this.chargeOriginalData.xInterval = this.chargeEditData.xInterval
      }

      const originalParam = this.reportChartTemplateList['charge'].originalParamJson
      if(originalParam?.xMax > 0){
        this.chargeOriginalData.xMin = originalParam.xMin
        this.chargeOriginalData.xMax = originalParam.xMax
        this.chargeOriginalData.xInterval = originalParam.xInterval
      }
      if(originalParam?.yMax > 0){
        this.chargeOriginalData.yMin = originalParam.yMin
        this.chargeOriginalData.yMax = originalParam.yMax
        this.chargeOriginalData.yInterval = originalParam.yInterval
      }


    },

    initDischargeEchart(
      legendData = {},
      checkData = [], //选中的数据
      axisData = {},
      titleData = {},
      gridData = {}
    ) {

      
      this.dischargeEchart = this.echarts.init(this.$refs.discharge, 'walden',{ devicePixelRatio: 2 })

      const templateParam = this.reportChartTemplateList['discharge'].templateParamJson
      let chargeEchartsList = _.cloneDeep(this.allDataJson.chargeEcharts)
      let dischargeEchartsList = _.cloneDeep(this.allDataJson.dischargeEcharts)
      dischargeEchartsList.sort((item1,item2) => { return item1.batteryNum -  item2.batteryNum })

      this.dischargeLegendList = _.cloneDeep(this.allDataJson.dischargeEcharts).map(e => e.batteryNum + '#')

      // 把模板的数据拼接进去
      const {
        titleData: newTitleData,
        gridData: newGridData,
        legendData: newLegendData,
        axisData: newAxisData,
        legend: newLegend
      } = this._getTemplateParams('discharge', titleData, gridData, legendData, axisData, legend);
      titleData = newTitleData;
      gridData = newGridData;
      legendData = newLegendData;
      axisData = newAxisData;

      // 首次渲染,所有图例都显示
      if(!legendData.legendRevealList || templateParam.legendData.legendRevealList){
        const revealNum = this.handleRevealNum(520,this.dischargeLegendList)
        this.dischargeEditData.legendRevealList = templateParam.legendData.legendRevealList ?? _.cloneDeep(this.dischargeLegendList).slice(0,revealNum)
      }

      this.dischargeEditData.legend = _.cloneDeep(this.dischargeLegendList)

      // 数据
      let seriesList = []
      let lineColorList = [] // 折线颜色

      const echartsColorList = this.dischargeEditData.legend.length <= 2 ? this.echartsColorShortList : this.echartsColorLongList
      const isCheck = checkData.length === 0

      this.dischargeSeriesList = []
      this.dischargeOriginalData.checkData = []
      this.dischargeOriginalData.series = []
      // this.dischargeEditData.duplicateDataOptions = []

      for (let i = 0; i < dischargeEchartsList.length; i++) {
        // 设置折线的颜色
        const have = lineColorList.find(v => v.name === dischargeEchartsList[i].batteryNum)
        if (have == undefined) {
          lineColorList.push({ name: dischargeEchartsList[i].batteryNum, color: echartsColorList[lineColorList.length % echartsColorList.length] })
        }

        const templateContent = templateParam.checkData.length > 0 ? (templateParam.checkData.filter(item => item.id === dischargeEchartsList[i].batteryNum + "#" + (i + 1))[0] || {}) : {}
        const editContentIndex = checkData.findIndex(findItem => findItem.id ===  dischargeEchartsList[i].batteryNum + '#' + (i + 1))
        const editContent = editContentIndex !== -1 ? checkData[editContentIndex] : {}

        let series = {
          id: dischargeEchartsList[i].batteryNum + '#' + (i + 1),
          name: dischargeEchartsList[i].batteryNum + '#',
          soc: dischargeEchartsList[i].batteryNum,
          type: 'line',
          barGap: 0,
          markPoint: {
            data: []
          },
          connectNulls: templateContent.connectNulls ?? (isCheck ? false : Boolean(Number(editContent.connectNulls))),
          symbol: templateContent.symbol ?? (isCheck ? "rect" : editContent.symbol),
          symbolSize: templateContent.symbolSize ?? (isCheck ? 5 : editContent.symbolSize),
          lineStyle: {
            width: templateContent.lineWidth ?? (isCheck ? 1 : editContent.lineWidth),
            type: templateContent.lineType ?? (isCheck ? "solid" : editContent.lineType),
            color: templateContent.lineColor ?? (
              isCheck
                ? lineColorList[lineColorList.findIndex(v => v.name === dischargeEchartsList[i].batteryNum)].color
                : editContent.lineColor
            )
          },
          itemStyle: {
            color: templateContent.itemColor ?? (
              isCheck
                ? lineColorList[lineColorList.findIndex(v => v.name === dischargeEchartsList[i].batteryNum)].color
                : editContent.itemColor
            )
          },
          batteryNum: dischargeEchartsList[i].batteryNum,
          emphasis: {
            focus: "series"
          },
          data: dischargeEchartsList[i].ocvDataList.map((mapItem, index) => { return { id: index, value: mapItem } }),
        }

        // 设置最大最小值
        if (checkData.length > 0 && editContent.maxPoint || templateContent.maxPoint) {
          series.markPoint.data.push({ type: "max", name: "Max" })
        }
        if (checkData.length > 0 && editContent.minPoint || templateContent.minPoint) {
          series.markPoint.data.push({ type: "min", name: "Min" })
        }



        this.dischargeSeriesList.push({
          id: dischargeEchartsList[i].batteryNum + '#' + (i + 1),
          index: i + 1,
          name: dischargeEchartsList[i].batteryNum + '#',
          soc: dischargeEchartsList[i].batteryNum + "#",
          data: dischargeEchartsList[i].ocvDataList.map(v => v[1] ? v[1].toString() : null),
          duplicateData:dischargeEchartsList[i].ocvDataList.map((mapItem, index) => { return { id: index, name: dischargeEchartsList[i].batteryNum + '#', value: mapItem } }),
          synchronization: templateContent.synchronization ?? (isCheck ? i : editContent.synchronization),
          maxPoint: templateContent.maxPoint ?? (isCheck ? false : editContent.maxPoint),
          minPoint: templateContent.minPoint ?? (isCheck ? false : editContent.minPoint),
          connectNulls: templateContent.connectNulls ?? false,
          symbol: templateContent.symbol ?? (isCheck ? "rect" : editContent.symbol),
          symbolSize: templateContent.symbolSize ?? (isCheck ? 5 : editContent.symbolSize),
          itemColor: templateContent.itemColor ?? (
            isCheck
              ? lineColorList[lineColorList.findIndex(v => v.name === dischargeEchartsList[i].batteryNum)].color
              : editContent.itemColor
          ),
          lineType: templateContent.lineType ?? (isCheck ? "solid" : editContent.lineType),
          lineWidth: templateContent.lineWidth ?? (isCheck ? 1 : editContent.lineWidth),
          lineColor: templateContent.lineColor ?? (
            isCheck
              ? lineColorList[lineColorList.findIndex(v => v.name === dischargeEchartsList[i].batteryNum)].color
              : editContent.lineColor
          )
        })
        // 原始值
        this.dischargeOriginalData.checkData.push({
          id: dischargeEchartsList[i].batteryNum + '#' + (i + 1),
          index: i + 1,
          soc: dischargeEchartsList[i].batteryNum + "#",
          name: dischargeEchartsList[i].batteryNum + "#",
          connectNulls: false,
          synchronization: i,
          maxPoint: false,
          minPoint: false,
          symbol: "rect",
          symbolSize: 5,
          itemColor: lineColorList[lineColorList.findIndex(v => v.name === dischargeEchartsList[i].batteryNum)].color,
          lineType: "solid",
          lineWidth: 1,
          lineColor: lineColorList[lineColorList.findIndex(v => v.name === dischargeEchartsList[i].batteryNum)].color
        })

        this.dischargeOriginalData.series.push({
          id: dischargeEchartsList[i].batteryNum + '#' + (i + 1),
          index: i + 1,
          soc: dischargeEchartsList[i].batteryNum + "#",
          name: dischargeEchartsList[i].batteryNum + "#",
          connectNulls: false,
          synchronization: i,
          maxPoint: false,
          minPoint: false,
          symbol: "rect",
          symbolSize: 5,
          itemColor: lineColorList[lineColorList.findIndex(v => v.name === dischargeEchartsList[i].batteryNum)].color,
          lineType: "solid",
          lineWidth: 1,
          lineColor: lineColorList[lineColorList.findIndex(v => v.name === dischargeEchartsList[i].batteryNum)].color
        })

        // this.dischargeEditData.duplicateDataOptions.push({
        //   id: dischargeEchartsList[i].batteryNum,
        //   data: dischargeEchartsList[i].ocvDataList.map((mapItem, index) => { return { id: index, value: index, label: mapItem[1].toString() } })
        // })

        seriesList.push(series)
      }

      this.dischargeEditData.series = _.cloneDeep(this.dischargeSeriesList)

      /* 图例排序 开始 */
      if (legendData.legendSort) {
        this.dischargeEditData.legend = _.cloneDeep(legendData.legendSort) // 将页面上的图例数组按照用户设置的顺序排序
      }
      this.dischargeEditData.legendSort = !legendData.legendSort ? _.cloneDeep(this.dischargeLegendList) : _.cloneDeep(this.dischargeEditData.legend)
      /* 图例排序 结束 */

      /* 图例变更名称 开始 */
      if (legendData.legendEditName) {
        legendData.legendEditName.forEach(v => {
          if (v.newName && !v.isReset) {
            let temIndex1 = this.dischargeLegendList.findIndex(findItem => findItem == v.originName)
            this.dischargeLegendList[temIndex1] = v.newName
            let temIndex2 = this.dischargeEditData.legend.findIndex(findItem => findItem == v.originName)
            this.dischargeEditData.legend[temIndex2] = v.newName
            this.dischargeEditData.series.forEach(findItem => {
              findItem.name = findItem.name == v.originName ? v.newName : findItem.name
            })
            seriesList.forEach(findItem => {
              findItem.name = findItem.name == v.originName ? v.newName : findItem.name
            })
          }

          if (!v.newName && v.isReset) {
            v.previousName = ''
            v.isReset = false
          }
        })

        // 赋予修改后的图例修改名称数组
        this.dischargeEditData.legendEditName = legendData.legendEditName
      }

      // 图例修改名称数组  使用位置：在线编辑图表--图例--名称 ,首次进入，将图例的值给图例修改名称数组
      // originName 原始值 newName 新值 previousName 上一个值（用于清空的情况下使用）isReset 是否重置（用于清空的时候使用）
      if (this.dischargeEditData.legendEditName.length === 0) {
        this.dischargeEditData.legendEditName = this.dischargeLegendList.map(v => { return {id:v, originName: v, previousName: '', newName: '', isReset: false } })
      }
      /* 图例变更名称 结束 */

      // 处理图例
      if (legendData.legendEdit) {
        for (let i = 0; i < this.dischargeEditData.legend.length; i++) {
          if (!legendData.legendEdit.includes(this.dischargeEditData.legend[i])) {
            this.dischargeEditData.legend.splice(i, 1)
            i--
          }
        }

        for (let i = 0; i < seriesList.length; i++) {
          if (!legendData.legendEdit.includes(seriesList[i].name)) {
            seriesList.splice(i, 1)
            i--
          }
        }

        // 判断依据
        for (let i = 0; i < checkData.length; i++) {
          if (!legendData.legendEdit.includes(checkData[i].name)) {
            checkData.splice(i, 1)
            i--
          }
        }
      }

      // 处理选中数据
      // if (checkData.length > 0) {
      //   seriesList.forEach((v, index) => {
      //     const handIndex = checkData.findIndex(findItem => findItem.id == v.id)
      //     for (let i = 0; i < v.data.length; i++) {
      //       if (!checkData[handIndex].duplicateData.includes(v.data[i].id)) {
      //         v.data.splice(i, 1)
      //         i--
      //       }
      //     }
      //   })
      // }

      // 结合图例数据（线+图例都存在）和图例显隐（只存在线，剔除图例）
      const legend = []
      this.dischargeEditData.legend.forEach(v => {
        const conditions1 = this.dischargeEditData.legendRevealList.includes(v)
        if(conditions1){
          legend.push(v)
        }else{
          const conditions2 = this.dischargeEditData.legendEditName.findIndex(findItem => findItem.newName == v)
          if(conditions2 > -1 && this.dischargeEditData.legendRevealList.includes(this.dischargeEditData.legendEditName[conditions2].originName)) legend.push(v)
        }
      })

      let chargeOption = {
        backgroundColor: '#ffffff',
        animationDuration: 2000,
        title: {
          text: titleData.chartTitle || this.queryParam.reportBasic.reportName + ' SOC-OCV',
          left: 'center',
          top: titleData.titleTop ||  20,
          fontSize: 18,
          fontWeight: 500,
          color: "#000"
        },
        grid: {
          show: true,
          top: gridData.gridTop ||  60,
          left: gridData.gridLeft || 80,
          right: gridData.gridRight || 40,
          bottom: gridData.gridBottom || 70,
          borderWidth: 0.5,
          borderColor: "#ccc"
        },
        textStyle: {
          fontFamily: "Times New Roman"
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            var result = params[0].axisValue + '%SOC' + '<br>'; // 添加 x 轴的数值
            params.forEach(function (item, dataIndex) {
              // var type= item.data.name+'#'
              result += '<div style="width:150px;display: inline-block;">' + item.marker + item.seriesName + '</div>' + item.data.value[1] + '<br>'; // 添加每个系列的数值
            });
            return result;
          }
        },


        legend: {
          show: true,
          // data: this.dischargeEditData.legend,
          data: legend,
          backgroundColor: legendData.legendBgColor || "#f5f5f5",
          itemWidth: legendData.legendWidth || 30,
          itemHeight: legendData.legendHeight || 5,
          itemGap: legendData.legendGap || 10,
          orient: legendData.legendOrient || 'horizontal',
          // position: "inside",
          top: legendData.legendTop || 70,
          left: legendData.legendLeft || 'center',
          textStyle: {
            fontSize: 14,
            color: "#000000"
          }
        },

        xAxis: [
          {
            type: axisData.xType || 'category',
            axisTick: { show: false },
            splitLine: {
              show: true, // 显示分隔线
              lineStyle: {
                type: "solid",
                width: 0.5
              }
            },
            axisLabel: {
              show: true,
              textStyle: {
                fontSize: "13",
                color: "#000000"
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              }
            },
            name: titleData.XTitle || 'SOC(%)',
            nameLocation: "middle", // 将名称放在轴线的中间位置
            nameGap: 35,
            nameTextStyle: {
              fontSize: 14, // 可以根据需要调整字体大小
              fontWeight: 500,
              color: "#000000"
            }
          }
        ],
        yAxis: [
          {
            type: axisData.yType || 'value',
            name: titleData.YTitle || 'OCV(V)',
            position: "left",
            nameGap: titleData.yTitleLetf || 40,
            splitLine: {
              show: true, // 显示分隔线
              lineStyle: {
                type: "solid",
                width: 0.5
              }
            },
            axisTick: {
              show: true // 显示刻度
            },
            axisLabel: {
              show: true,
              textStyle: {
                fontSize: "13",
                color: "#000000"
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              }
            },
            nameLocation: "middle", // 将名称放在轴线的起始位置
            nameRotate: 90, // 旋转角度，使名称竖排
            nameTextStyle: {
              fontSize: 14, // 可以根据需要调整字体大小
              fontWeight: 500,
              color: "#000000"
            }
          }
        ],
        series: seriesList
      }

      // 传回给在线编辑图表，当前图标上有的点
      // this.dischargeEditData.duplicateCheckedList = seriesList.map(mapItem => mapItem.data.filter(filterItem => filterItem.value[1] !== '').map(mapItem2 => mapItem2.id))

      // 处理坐标轴
      if (axisData.xMin) {
        chargeOption.xAxis[0].min = axisData.xMin
      }
      if (axisData.xMax) {
        chargeOption.xAxis[0].max = axisData.xMax
      }
      if (axisData.xInterval) {
        chargeOption.xAxis[0].interval = axisData.xInterval
      }
      if (axisData.yMin) {
        chargeOption.yAxis[0].min = axisData.yMin
      }
      if (axisData.yMax) {
        chargeOption.yAxis[0].max = axisData.yMax
      }
      if (axisData.yInterval) {
        chargeOption.yAxis[0].interval = axisData.yInterval
      }

      // 坐标轴类型赋值
      this.dischargeEditData.xType = chargeOption.xAxis[0].type
      this.dischargeEditData.yType = chargeOption.yAxis[0].type

      this.dischargeEchart.clear()

      // this.dischargeEchart.getZr().off('click')
      // this.dischargeEchart.getZr().on('click', params => {
      //   const { target, topTarget } = params

      //   // Z 0:坐标轴
      //   if (topTarget?.z === 0 && this.drawerVisible) {
      //     // this.$set(this.dischargeCheckObj,'axis',0)
      //     this.$set(this.dischargeCheckObj, 'editObj', 'axis')
      //   }
      //   // Z 3:线
      //   if (topTarget?.z === 3 && this.drawerVisible) {
      //     const axs = target.parent?.parent?.__ecComponentInfo?.index
      //     this.$set(this.dischargeCheckObj, 'tag', axs)
      //     this.$set(this.dischargeCheckObj, 'editObj', 'tag')
      //   }
      //   // Z 4:图例
      //   if (topTarget?.z === 4 && this.drawerVisible) {
      //     const axs = target.parent?.__legendDataIndex
      //     this.$set(this.dischargeCheckObj, 'editObj', 'legend')
      //   }
      // });

      this.dischargeEchart.getZr().off('dblclick')
      this.dischargeEchart.getZr().on('dblclick', ({target, topTarget}) => {
        this._handleDblclickEchart(target, topTarget, 'discharge')
      });
      this.dischargeEchart.setOption(chargeOption)

      // 如果坐标轴类型为数值轴，则计算出最大值最小值，以及间距
      if (chargeOption.xAxis[0].type === "value") {
        const dischargeXAxis = this.dischargeEchart.getModel().getComponent("xAxis").axis.scale._extent
        this.dischargeEditData.xMin = dischargeXAxis[0]
        this.dischargeEditData.xMax = dischargeXAxis[1]
      }

      if (chargeOption.yAxis[0].type === "value") {
        const dischargeYAxis = this.dischargeEchart.getModel().getComponent("yAxis").axis.scale
        this.dischargeEditData.yMin = dischargeYAxis._extent[0]
        this.dischargeEditData.yMax = dischargeYAxis._extent[1]
        this.dischargeEditData.yInterval = dischargeYAxis._interval

        this.dischargeOriginalData.yMin = dischargeYAxis._extent[0]
        this.dischargeOriginalData.yMax = dischargeYAxis._extent[0]
        this.dischargeOriginalData.yInterval = dischargeYAxis._interval
      }
      if (this.isEditDischargeXNum === 0 && axisData.xType === "value") {
        this.isEditDischargeXNum++
        this.dischargeOriginalData.xMin = this.dischargeEditData.xMin
        this.dischargeOriginalData.xMax = this.dischargeEditData.xMax
        this.dischargeOriginalData.xInterval = this.dischargeEditData.xInterval
      }

      const originalParam = this.reportChartTemplateList['discharge'].originalParamJson
      if(originalParam?.xMax > 0){
        this.dischargeOriginalData.xMin = originalParam.xMin
        this.dischargeOriginalData.xMax = originalParam.xMax
        this.dischargeOriginalData.xInterval = originalParam.xInterval
      }
       if(originalParam?.yMax > 0){
        this.dischargeOriginalData.yMin = originalParam.yMin
        this.dischargeOriginalData.yMax = originalParam.yMax
        this.dischargeOriginalData.yInterval = originalParam.yInterval
      }
    },


    updateData() {
      testReportUpdateDate(this.originData, this.id).then(res => {
        this.init()
      })
    },
    // 生成
    handleDrawerSubmit(value) {
      
      const legendData = {
        legendEdit: value.legendList,
        legendRevealList: value.legendRevealList,
        legendWidth: value.legendWidth,
        legendHeight: value.legendHeight,
        legendGap: value.legendGap,
        legendSort: value.legendSort, // 图例排序
        legendEditName: value.legendEditName,
        legendBgColor: value.legendBgColor,
        legendOrient: value.legendOrient,
        legendTop:value.legendTop,
        legendLeft: value.legendLeft,
      }

      const axisData = {
        xMin: value.xMin,
        xMax: value.xMax,
        xInterval: value.xInterval,
        xType: value.xType,

        yMin: value.yMin,
        yMax: value.yMax,
        yInterval: value.yInterval,
        yType: value.yType
      }

      const titleData = {
        chartTitle: value.chartTitle,
        XTitle: value.XTitle,
        YTitle: value.YTitle,
        titleTop: value.titleTop,
        yTitleLetf: value.yTitleLetf,
      }

      const gridData = {
          gridTop: value.gridTop,
          gridLeft: value.gridLeft,
          gridRight: value.gridRight,
          gridBottom: value.gridBottom,
        }

        // 赋值的数组
      const assignArr = ['chartTitle', 'XTitle', 'YTitle', 'titleTop','yTitleLetf','legendRevealList', 'legendWidth', 'legendHeight', 'legendGap', 'legendEditName', 'legendOrient','legendBgColor', 'legendTop', 'legendX', 'xMin', 'xMax', 'xInterval', 'xType',
          'yMin', 'yMax', 'yInterval', 'yType', 'synchronization', 'gridTop', 'gridLeft', 'gridRight', 'gridBottom',"targetEditObj"]

      if (this.editObj === "charge") {
        this.chargeEditData.series = _.cloneDeep(value.checkData)

        for (let i = 0; i < assignArr.length; i++) {
          this.chargeEditData[assignArr[i]] = value[assignArr[i]]
        }

        // 处理模板参数
        this._handleTemplateParams(value)

        this.initChargeEchart(
          legendData,
          value.checkData,
          axisData,
          titleData,
          gridData
        )
      } else if (this.editObj === "discharge") {
        this.dischargeEditData.series = _.cloneDeep(value.checkData)

        for (let i = 0; i < assignArr.length; i++) {
          this.dischargeEditData[assignArr[i]] = value[assignArr[i]]
        }

        // 处理模板参数
        this._handleTemplateParams(value)

        this.initDischargeEchart(
          legendData,
          value.checkData,
          axisData,
          titleData,
          gridData
        )
      }

      this.$forceUpdate()

      // 记录数据到后端
      let chartTemplateParams = {}
      if(!this.reportChartTemplateList[this.editObj].templateId){
        chartTemplateParams = {
          targetChart:this.editObj,
          templateName:'报告ID修改默认模板',
          reportId:this.$route.query.id,
          originalParamJson:JSON.stringify(this.editObj === "charge" ? this.chargeOriginalData : this.dischargeOriginalData),
          templateParamJson:JSON.stringify(this.reportChartTemplateList[this.editObj].templateParamJson),
        } 
        console.log('传递的原始值')
        console.log(this.editObj === 'charge' ? this.chargeOriginalData : this.dischargeOriginalData)
        this.saveChartTemplate(chartTemplateParams)
      }else{
        chartTemplateParams = {
          id:this.reportChartTemplateList[this.editObj].templateId,
          templateParamJson:JSON.stringify(this.reportChartTemplateList[this.editObj].templateParamJson),
        }
        if(((this.isEditChargeXNum === 1 && this.editObj === "charge") || (this.isEditDischargeXNum === 1 && this.editObj === "discharge")) && this.reportChartTemplateList[this.editObj].originalParamJson.xMax == 0 ){
          chartTemplateParams.originalParamJson = JSON.stringify(this.editObj === 'charge' ? this.chargeOriginalData : this.dischargeOriginalData)
          this.reportChartTemplateList[this.editObj].originalParamJson = this.editObj === 'charge' ? this.chargeOriginalData : this.dischargeOriginalData
          console.log('传递的原始值')
          console.log(this.editObj === 'charge' ? this.chargeOriginalData : this.dischargeOriginalData)
        }
        
        this.updateChartTemplate(chartTemplateParams)
      }
    },

    // 重置
    handleDrawerReset() {
      this.$confirm({
        title: '请确认是否重置图表?',
        content: '图表重置后，图表修改内容无法恢复',
        okText: '重置',
        cancelText: '取消',
        onOk:async () => {
          await this.deleteChartTemplate({ reportId:this.$route.query.id,id:this.reportChartTemplateList[this.editObj].templateId,targetChart:this.editObj },false)

          if (this.editObj === "charge") {
            this.chargeEditData = this._getInitData('charge','edit')
            this.chargeEditData.series = _.cloneDeep(this.chargeSeriesList)
            this.initChargeEchart()
          } else if (this.editObj === "discharge") {
            this.dischargeEditData = this._getInitData('discharge','edit')
            this.dischargeEditData.series = _.cloneDeep(this.dischargeSeriesList)
            this.initDischargeEchart()
          }
          this.drawerVisible = false
          this.$message.success("重置成功")
        },
        onCancel() {}
      });
    },

    // 重新选择模板
    async handleChangeTemplate(targetObj){
      await this.getChartTemplateRelationList(this.$route.query.id,[targetObj])
      if (targetObj === "charge") {
        this.chargeEditData = this._getInitData('charge','edit')
        this.chargeEditData.series = _.cloneDeep(this.chargeSeriesList)
        this.initChargeEchart()
      } else if (this.editObj === "discharge") {
        this.dischargeEditData = this._getInitData('discharge','edit')
        this.dischargeEditData.series = _.cloneDeep(this.dischargeSeriesList)
        this.initDischargeEchart()
      }
      this.drawerVisible = false
    },

    // 获取编辑图表数据、原始图表数据
    _getInitData(targetObj,type = 'original'){
      const isEdit = type === 'edit'
      const templateParam = this.reportChartTemplateList[targetObj].templateParamJson

      const options = {
        chartTitle: isEdit && templateParam.chartTitle ? templateParam.chartTitle : this.queryParam.reportBasic.reportName + ' SOC-OCV',
        XTitle: isEdit && templateParam.XTitle ? templateParam.XTitle : 'SOC(%)', //X轴标题
        YTitle: isEdit && templateParam.YTitle ? templateParam.YTitle : 'OCV(V)', //Y轴标题
        titleTop: isEdit && templateParam.titleTop ? templateParam.titleTop : 20,
        yTitleLetf:isEdit && templateParam.yTitleLetf ? templateParam.yTitleLetf :  40,

        legendWidth: isEdit && templateParam.legendWidth ? templateParam.legendWidth : 30,
        legendHeight: isEdit && templateParam.legendHeight ? templateParam.legendHeight : 5,
        legendGap: isEdit && templateParam.legendGap ? templateParam.legendGap : 10,//图例间隙
        legendBgColor: isEdit && templateParam.legendBgColor ? templateParam.legendBgColor : '#f5f5f5',
        legendOrient: isEdit && templateParam.legendOrient ? templateParam.legendOrient : 'horizontal',
        legendX: isEdit && templateParam.legendX ? templateParam.legendX : 'center',
        legendTop: isEdit && templateParam.legendTop ? templateParam.legendTop : 70,

        gridTop: isEdit && templateParam.gridTop ? templateParam.gridTop : 60,
        gridLeft: isEdit && templateParam.gridLeft ? templateParam.gridLeft : 80,
        gridRight: isEdit && templateParam.gridRight ? templateParam.gridRight : 40,
        gridBottom: isEdit && templateParam.gridBottom ? templateParam.gridBottom : 70,

        xType: isEdit && templateParam.xType ? templateParam.xType : "category",
        xMin: isEdit && templateParam.xMin ? templateParam.xMin : 0,
        xMax: isEdit && templateParam.xMax ? templateParam.xMax : 0,
        xInterval: isEdit && templateParam.xInterval ? templateParam.xInterval : 0,

        yType: isEdit && templateParam.yType ? templateParam.yType : "value",
        yMin: isEdit && templateParam.yMin ? templateParam.yMin : 0,
        yMax: isEdit && templateParam.yMax ? templateParam.yMax : 0,
        yInterval: isEdit && templateParam.yInterval ? templateParam.yInterval : 0,
      }

      if(type === 'edit'){
        options.series = []
        options.legend = []
        options.legendSort = []
        options.legendEditName = []
        options.allData = templateParam.allData ?? {}
        if(templateParam.legendLeft) options.legendLeft = templateParam.legendLeft
      }
      if(type === 'original'){
        options.checkData = []
      }

      return options
    },

    // 处理模板值
    _handleTemplateParams(value){
      const isEdit = !!value.targetEditObj
      const templateParam = this.reportChartTemplateList[this.editObj].templateParamJson

      if(isEdit && !['legendList','legendEditName','legendSort','legendRevealList'].includes(value.targetEditObj)){
        if(value.targetEditIndex === undefined){
          templateParam[value.targetEditObj] = value[value.targetEditObj]
        }else if(value.targetEditIndex === 'all'){
          for(let i = 0; i < value.checkData.length ; i++){
            if(templateParam.checkData[i] === undefined) templateParam.checkData[i] = {}
            templateParam.checkData[i] = {
              ...templateParam.checkData[i],
              id:value.checkData[i].id,
              [value.targetEditObj]:value.checkData[i][value.targetEditObj]
            }
          }
          templateParam.allData[value.targetEditObj] = value.allData[value.targetEditObj]
        }else{
          let haveIndex = templateParam.checkData.findIndex(item => item.id === value.checkData[value.targetEditIndex].id)
          if(haveIndex === -1){
            templateParam.checkData.push({id:value.checkData[value.targetEditIndex].id})
            haveIndex = templateParam.checkData.length - 1
          }
          templateParam.checkData[haveIndex][value.targetEditObj] = value.checkData[value.targetEditIndex][value.targetEditObj]
        }
      }

      if(!isEdit){
        if (value.targetResetIndex === undefined || value.targetResetIndex === 'yDecimalNum') {
          delete templateParam[value.targetResetObj]

          // 如果是XY轴的类型重置，需同步去除最大最小值
          if(value.targetResetObj === 'xType' && value.xType === 'category'){
            delete templateParam.xMin
            delete templateParam.xMax
            delete templateParam.xInterval
          }
          if(value.targetResetObj === 'yType' && value.yType === 'value'){
            delete templateParam.yMin
            delete templateParam.yMax
            delete templateParam.yInterval
          }
        } else if (value.targetResetIndex === 'all') {
          for(let i = 0; i < templateParam.checkData.length ; i++){
            delete templateParam.checkData[i][value.targetResetObj]
          }
          delete templateParam.allData[value.targetResetObj]
        } else {
          let haveIndex = templateParam.checkData.findIndex(item => item.id === value.checkData[value.targetResetIndex].id)
          if(haveIndex !== -1){
            delete templateParam.checkData[haveIndex][value.targetResetObj]
          }
        }
      }

      // 图例-数据
      if(value.targetEditObj === 'legendList'){
        templateParam.legendData.legendIndeterminate = value.legendIndeterminate
        templateParam.legendData.checkAll = value.checkAll
        templateParam.legendData.legendList = value.legendList
        templateParam.legendData.legendOptions = templateParam.legendData.legendOptions ?? (this.editObj === "charge" ? this.chargeLegendList : this.dischargeLegendList)
      }

      // 图例-名称
      if(value.targetEditObj === 'legendEditName'){
        templateParam.legendData.legendList = value.legendList // 需同步更名后的数组
        templateParam.legendData.legendEditName = value.legendEditName
        templateParam.legendData.legendRevealList = value.legendRevealList
        templateParam.legendData.legendRevealOptions = value.legendRevealOptions

        // 找到改名的那根线，存储修改后的线名称
        const haveIndex =  templateParam.checkData.findIndex(item => item.id === value.checkData[value.targetEditIndex].id)
        if(haveIndex === -1){
          templateParam.checkData.push({id:value.checkData[value.targetEditIndex].id})
          templateParam.checkData[templateParam.checkData.length - 1].name = value.checkData[value.targetEditIndex].name
        }else{
          templateParam.checkData[haveIndex].name = value.checkData[value.targetEditIndex].name
        }
      }

      // 如果有图例-排序的修改
      if(value.targetEditObj === 'legendSort'){
        templateParam.legendData.legendSort = value.legendSort
      }

      if(value.targetEditObj === 'legendNameType'){
        templateParam.legendData.legendSort = value.legendSort
        templateParam.legendData.legendList = value.legendList
      }

      // 图例-显隐
      if(value.targetEditObj === 'legendRevealList'){
        templateParam.legendData.legendRevealIndeterminate = value.legendRevealIndeterminate
        templateParam.legendData.legendRevealcheckAll = value.legendRevealcheckAll
        templateParam.legendData.legendRevealList = value.legendRevealList
        templateParam.legendData.legendRevealOptions = value.legendRevealOptions
      }

      // 图例重置相关的处理
      if(['legendList','legendEditName','legendSort','legendRevealList'].includes(value.targetResetObj)){
        if(value.targetResetObj === 'legendList'){
          delete templateParam.legendData.legendList
          delete templateParam.legendData.checkAll
          delete templateParam.legendData.legendIndeterminate
        }
        if(value.targetResetObj === 'legendEditName'){
          delete templateParam.legendData.legendEditName
          delete templateParam.legendData.legendRevealList
        }
        if(value.targetResetObj === 'legendSort'){
          delete templateParam.legendData.legendSort
        }
        if(value.targetResetObj === 'legendRevealList'){
          delete templateParam.legendData.legendRevealList
        }
      }
    },

    // 获取模板参数
    _getTemplateParams(targetObj,titleData,gridData,legendData,axisData,legend){
      const templateParam = this.reportChartTemplateList[targetObj].templateParamJson

      const titleList = ['chartTitle', 'XTitle', 'YTitle', 'YTitle2', 'titleTop', 'yTitleLetf', 'yTitleRight']
      titleList.forEach(item => {
        titleData[item] = templateParam[item] ?? titleData[item]
      })

      const legendList = ['legendBgColor','legendOrient','legendTop', 'legendLeft','legendWidth', 'legendHeight', 'legendGap','legendX']
      legendList.forEach(item => {
        legendData[item] = templateParam[item] ?? legendData[item]
      })

      const gridList = ['gridTop', 'gridLeft', 'gridRight', 'gridBottom']
      gridList.forEach(item => {
        gridData[item] = templateParam[item] ?? gridData[item]
      })

      const axisList = ['xType','xMin', 'xMax', 'xInterval', 'yType', 'yMin', 'yMax', 'yInterval', 'yType2', 'yMin2', 'yMax2', 'yInterval2']
      axisList.forEach(item => {
        axisData[item] = templateParam[item] ?? axisData[item]
      })
      // 如果有图例-数据的修改
      if(templateParam.legendData.legendList){
        legendData.legendEdit = templateParam.legendData.legendList
        if(templateParam.legendData.checkAll !== undefined) this[`${targetObj}EditData`].checkAll = templateParam.legendData.checkAll
        if(templateParam.legendData.legendIndeterminate !== undefined) this[`${targetObj}EditData`].legendIndeterminate = templateParam.legendData.legendIndeterminate
      }

      // 如果有图例-名称的修改
      if(templateParam.legendData.legendEditName){
        legendData.legendEditName = templateParam.legendData.legendEditName
        legendData.legendRevealList = templateParam.legendData.legendRevealList
      }

      // 如果有图例-排序的修改
      if(templateParam.legendData.legendSort){
        legendData.legendSort = templateParam.legendData.legendSort
      }

      // 如果有图例-显隐的修改
      if(templateParam.legendData.legendRevealList){
        legendData.legendRevealList = templateParam.legendData.legendRevealList
        if(templateParam.legendData.legendRevealcheckAll !== undefined) this[`${targetObj}EditData`].legendRevealcheckAll = templateParam.legendData.legendRevealcheckAll
        if(templateParam.legendData.legendRevealIndeterminate !== undefined) this[`${targetObj}EditData`].legendRevealIndeterminate = templateParam.legendData.legendRevealIndeterminate
        if(templateParam.legendData.legendRevealOptions !== undefined) this[`${targetObj}EditData`].legendRevealOptions = templateParam.legendData.legendRevealOptions
      }

      return { titleData, gridData, legendData, axisData, legend }
    },
  }
}
</script>
<style lang="less" scoped>
@import "./css/preview.less";
</style>