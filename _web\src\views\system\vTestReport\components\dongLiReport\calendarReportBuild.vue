<template>
  <a-spin :spinning="isUpdating">
  <div class="calendar-wrapper">
    <div style="width: 100%;">
      <div class="flex-sb-center-row">
        <h3>一、基本信息填写</h3>
      </div>
      <div class="info-row-div">
        <div class="label-span">项目名称 :</div>
        <a-tooltip :title="calendarParam.projectName">
          <a-input style="width: 100px; margin-top: 10px;" v-model="calendarParam.projectName" @blur="handleBlur"/>
        </a-tooltip>
        <div class="label-span">样品阶段 :</div>
        <a-input style="width: 100px; margin-top: 10px;" v-model="calendarParam.phase" @blur="handleBlur"/>
        <div class="label-span">温度 :</div>
        <a-input class="number-input" suffix="℃" v-model="calendarParam.temp"
                 @keyup="calendarParam.temp = (calendarParam.temp + '').replace(/[^0-9.]/g, '')"
                 @blur="handleNumberBlur('temp')"/>
        <div class="label-span">SOC :</div>
        <a-input class="number-input" suffix="%" v-model="calendarParam.soc"
                 @keyup="calendarParam.soc = (calendarParam.soc + '').replace(/[^0-9.]/g, '')"
                 @blur="handleNumberBlur('soc')"/>
      </div>
    </div>
    <div class="flex-sb-center-row" style="margin: 8px 0;">
      <h3>二、建模参数填写</h3>
    </div>
    <div style="width: 100%; display: flex; margin-bottom: 8px; align-items: center;">
      <a-popconfirm placement="topRight" title="确认清空？" @confirm="() => {
            this.calendarParam.rptStepParamList = []
            this.deleteSelectedRowKeys = []
            this.calendarParam.rptDayComList = []
            this.calendarParam.rptDayList = []
            this.calendarParam.standardRptIndex = null
            this.allOrderDataChange()
            this.$emit('handleVerify', this._handleVerify())
            }">
        <a-button>清空</a-button>
      </a-popconfirm>
      <a-popconfirm placement="topRight" title="确认删除？" @confirm="deleteSelectRpt">
        <a-button class="ml10">删除</a-button>
      </a-popconfirm>
      <a-button class="ml10" @click="isShowCompute = true">中检天数录入</a-button>
      <a-button class="ml10" @click="openConnectModal">样品匹配</a-button>
      <div class="ml10" style="align-items: center;">
        DCIR计算方案：
        <a-radio-group :value="calendarParam.dcirCalcType || 'DongLi'" @change="dcirCalcTypeChange">
          <a-radio-button value="DongLi">动力</a-radio-button>
          <a-radio-button value="V">V圆柱</a-radio-button>
        </a-radio-group>
      </div>
    </div>
    <a-table class="rpt-table"
             :data-source="calendarParam.rptStepParamList"
             :row-selection="{columnWidth: 40, selectedRowKeys: this.deleteSelectedRowKeys, onChange: deleteRowOnChange}"
             :columns="rptParamColumns"
             :pagination="false"
             bordered>
      <template slot="action" slot-scope="text, record, index, columns">
        <a-tooltip placement="top" title="添加RPT" arrow-point-at-center>
          <a-icon type="plus" class="btn-icon" @click="addOneRpt(index + 1)" />
        </a-tooltip>
        <a-tooltip placement="top" title="删除RPT" arrow-point-at-center>
          <a-icon type="delete" class="ml10 btn-icon" @click="deleteOneRpt(index)" />
        </a-tooltip>
      </template>
      <template slot="day" slot-scope="text, record, index, columns">
        <a-input-number class="input-number" :precision="0" v-model="record.day" />
      </template>
      <template slot="rechargeDay" slot-scope="text, record, index, columns">
        <a-input-number class="input-number" :precision="0" v-model="record.rechargeDay" />
      </template>
      <template slot="orderDataList" slot-scope="text, record, index, columns">
        <!-- 全展示 -->
        <div  v-if="record.orderDataList.length <= 2 || record.isUnfolded">
          <div class="samples-block" v-for="(item, index) in record.orderDataList" style="border-bottom: 1px solid #e8e8e8;">
            <a v-if="item.flowId" style="text-align: center" @click="$refs.orderDataSelectModal.openStepData(item)">
              {{ item ? item.celltestcode + (item.day ? " (" + item.day + ")" : "") : "" }}
            </a>
            <span v-else style="text-align: center">
                {{ item ? item.celltestcode + (item.day ? " (" + item.day + ")" : "") : "" }}
            </span>
            <a-tooltip title="删除样品">
              <a-icon class="ml5 btn-icon" type="delete" @click="handleDeleteOne(record, index)" />
            </a-tooltip>
          </div>
        </div>
        <!-- 展示前2个 -->
        <div v-else>
          <div class="samples-block" v-for="(item, index) in record.orderDataList.slice(0, 2)" style="border-bottom: 1px solid #e8e8e8;">
            <a v-if="item.flowId" style="text-align: center" @click="$refs.orderDataSelectModal.openStepData(item)">
              {{ item ? item.celltestcode + (item.day ? " (" + item.day + ")" : "") : "" }}
            </a>
            <span v-else style="text-align: center">
                {{ item ? item.celltestcode + (item.day ? " (" + item.day + ")" : "") : "" }}
            </span>
            <a-tooltip title="删除样品">
              <a-icon class="ml5 btn-icon" type="delete" @click="handleDeleteOne(record, index)" />
            </a-tooltip>
          </div>
        </div>
        <div v-if="record.orderDataList.length > 2" style="border-bottom: 1px solid #e8e8e8;" class="shrink-btn" @click="handleFold(record)">
          {{record.isUnfolded ? '收起' : `${'+'}${record.orderDataList.length - 2} 展开`}}
        </div>
        <div style="margin: 2px 0">
          <a-tooltip title="编辑样品">
            <a-icon class="btn-icon" type="edit" @click="handleShowOrderDataModal(record, index)" />
          </a-tooltip>
          <a-popconfirm placement="topRight" title="确认清空样品？" @confirm="handleDeleteAll(record)">
            <a-tooltip title="清空样品">
              <a-icon v-if="record.orderDataList.length > 1" class="ml10 btn-icon" type="close" />
            </a-tooltip>
          </a-popconfirm>
        </div>
      </template>
      <template slot="retentionStep" slot-scope="text, record, index, columns">
        <a-input class="input"
                 v-model="record.retentionStep"
                 @keyup="verifyStepNumber(index, 'retentionStep')"
                 @paste="copyFromExcel($event, index, 5)"/>
      </template>
      <template slot="retentionStepTitle">
        容量&能量保持率工步号
        <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
          <a-icon type="question-circle" style="color: #1890ff;"/>
          <template slot="title">该工步号的第零天的容量&能量数据会乘以存储SOC值</template>
        </a-tooltip>
      </template>
      <template slot="recoveryStep" slot-scope="text, record, index, columns">
        <a-input class="input"
                 v-model="record.recoveryStep"
                 @keyup="verifyStepNumber(index, 'recoveryStep')"
                 @paste="copyFromExcel($event, index, 6)"/>
      </template>
      <template slot="recoveryStepTitle">
        <a-input class="input" v-model="calendarParam.recoveryStepTiTle" placeholder="示例：2C倍率" @blur="handleBlur"/>
        容量&能量恢复率工步号
        <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
          <a-icon type="question-circle" style="color: #1890ff;"/>
          <template slot="title">
            <span>请输入容量&能量恢复率工步号；<br/>如是多个工步号，需以逗号,隔开，计算平均值</span>
          </template>
        </a-tooltip>
        <a-tooltip v-if="dchCeStepNum === 0">
          <template slot="title">增加放电容量&能量工步号列</template>
          <a-icon class="ml10" type="plus" style="color: #1890ff;" @click="changeDchCeStepStrList(false)"/>
        </a-tooltip>
      </template>
      <template v-for="(item, i) in dchCeStepIndexArr" :slot="`dchCeStepTitle_${i}`">
        <a-input class="input" v-model="calendarParam.dchCeStepTiTleList[i]" placeholder="示例：2C倍率" @blur="handleBlur"/>
        放电容量&能量工步号
        <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
          <a-icon type="question-circle" style="color: #1890ff;"/>
          <template slot="title">
            <span>请输入放电容量&能量工步号；<br/>如是多个工步号，需以逗号,隔开，计算平均值</span>
          </template>
        </a-tooltip>
        <a-tooltip v-if="i === calendarParam.dchCeStepTiTleList.length - 1">
          <template slot="title">增加放电容量&能量工步号列</template>
          <a-icon class="ml10" type="plus" style="color: #1890ff;" @click="changeDchCeStepStrList(false)"/>
        </a-tooltip>
        <a-tooltip v-if="i === calendarParam.dchCeStepTiTleList.length - 1">
          <template slot="title">删除放电容量&能量工步号列</template>
          <a-icon class="ml10" type="minus" style="color: #1890ff;" @click="changeDchCeStepStrList(true)"/>
        </a-tooltip>
      </template>
      <template slot="dchCeStep" slot-scope="text, record, index, columns">
        <a-input class="input"
                 v-model="record.dchCeStepStrList[columns.dataIndex.replace('dchCeStepStrList[','').charAt(0)]"
                 @keyup="record.dchCeStepStrList[columns.dataIndex.replace('dchCeStepStrList[','').charAt(0)] = (record.dchCeStepStrList[columns.dataIndex.replace('dchCeStepStrList[','').charAt(0)] + '').replaceAll(/[\t\n\s]/g, ',').replaceAll(/[^0-9,，]/g, '').replaceAll(/[，]/g, ',')"
                 @blur="handleBlur"
                 @paste="copyFromExcel($event, index, 7 + Number(columns.dataIndex.replace('dchCeStepStrList[','').charAt(0)))"/>
      </template>
      <template v-for="(item, i) in dcirTitleIndexArr" :slot="`dcirTitle_${i}`">
        <div class="flex-sb-center-row">
          <a-input class="input" style="height: 100%;padding: 0" v-model="calendarParam.dcirTiTleList[i]" @blur="handleBlur" placeholder="示例：XX SOC XX s" />
          <a-tooltip v-if="i === calendarParam.dcirTiTleList.length - 1 && i < 3">
            <template slot="title">增加DCIR参数组</template>
            <a-icon class="ml10 btn-icon" type="plus" @click="changeDcirParamList(false)"/>
          </a-tooltip>
          <a-tooltip v-if="i === calendarParam.dcirTiTleList.length - 1 && i > 0">
            <template slot="title">删除DCIR参数组</template>
            <a-icon class="ml10 btn-icon" type="minus" @click="changeDcirParamList(true)"/>
          </a-tooltip>
        </div>
      </template>
      <template slot="dcirStepParam" slot-scope="text, record, index, columns">
        <a-input class="input"
                 v-model="record.dcirStepParamList[columns.dataIndex.replace('dcirStepParamList[','').charAt(0)][columns.dataIndex.replace('dcirStepParamList[','').substring(3)]"
                 @keyup="record.dcirStepParamList[columns.dataIndex.replace('dcirStepParamList[','').charAt(0)][columns.dataIndex.replace('dcirStepParamList[','').substring(3)] = (record.dcirStepParamList[columns.dataIndex.replace('dcirStepParamList[','').charAt(0)][columns.dataIndex.replace('dcirStepParamList[','').substring(3)] + '').replaceAll(columns.dataIndex.includes('dchStepTime') ? /[^0-9:.]/g : /[^0-9]/g, '')"
                 @blur="handleBlur"
                 @paste="copyFromExcel($event, index, null, columns.dataIndex)"/>
      </template>
      <template slot="dchStepTimeTitle">
        放电工步时间
        <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
          <a-icon type="question-circle" style="color: #1890ff;"/>
          <template slot="title">
                  <span>
                    未填写放电工步时间默认取工步数据表数据；<br/>
                    填写了放电工步时间则取详细数据表数据；<br/>
                    格式：HH:mm:ss.SSS，精确匹配；例如：0:00:10.000
                  </span>
          </template>
        </a-tooltip>
      </template>
      <template slot="standardStep" slot-scope="text, record, index, columns">
        <a-radio :checked="index === calendarParam.standardRptIndex" @change="onRadioChange(index)"></a-radio>
      </template>
      <template slot="standardStepTitle">
        基准工步
        <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
          <a-icon type="question-circle" style="color: #1890ff;"/>
          <template slot="title">
								<span>
									未填写的工步号以选中的行为基准进行数据建模；<br/>
                  工步号可从Excel复制（结构需与PBI一致，为五列信息）
								</span>
          </template>
        </a-tooltip>
      </template>
      <template slot="chCeStep" slot-scope="text, record, index, columns">
        <a-input class="input"
                 v-model="calendarParam.rptStepParamList[index].chCeStep"
                 @keyup="calendarParam.rptStepParamList[index].chCeStep = (calendarParam.rptStepParamList[index].chCeStep + '').replaceAll(/[\t\n\s]/g, ',').replaceAll(/[^0-9,，]/g, '').replaceAll(/[，]/g, ',')"
                 @blur="handleBlur"/>
      </template>
      <template slot="chCeStepTitle">
        充电容量&恒流比工步号
        <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
          <a-icon type="question-circle" style="color: #1890ff;"/>
          <template slot="title">
            <span>充电容量&恒流比工步号非必填；<br/>如是多个工步号，需以逗号,隔开，计算平均值</span>
          </template>
        </a-tooltip>
      </template>
      <template slot="footer">
        <div class="footer-btn" :class="{ 'plus-btn': calendarParam.rptStepParamList.length === 0 }" @click="addOneRpt(calendarParam.rptStepParamList.length)">
          <span></span>
          <span></span>
          <span></span>
          <span></span>
          <a-icon type="plus"></a-icon>
        </div>
      </template>
    </a-table>

    <orderDataSelectModal ref="orderDataSelectModal" :selectedOrderDataList="selectedOrderDataList" @selectedOrderDataChange="allOrderDataChange"></orderDataSelectModal>

    <!-- 天数计算规则输入弹窗 -->
    <a-modal title="中检天数录入" :width="1200" :height="600" :bodyStyle="{ padding: 0 }" :visible="isShowCompute"
             style="padding: 0" :maskClosable="false" @cancel="isShowCompute = false">
      <div class="mt10">
        <a-button class="ml10 mr10" size="small" @click="addOneCompute">
          <a-icon type="plus"/>
          增加中检阶段
        </a-button>
        <a-button class="mr10" size="small" @click="computeRptDays">
          <a-icon type="calculator"/>
          计算
        </a-button>
        <a-button size="small" @click="isShowRptDayList = true">
          <a-icon type="ordered-list"/>
          详情
        </a-button>
      </div>
      <a-table id="cycComputeTable" class="mt10"
               bordered
               :columns="rptDayComColumns"
               :data-source="calendarParam.rptDayComList"
               :pagination="false">
        <template slot="action" slot-scope="text, record, index, columns">
          <a @click="deleteOneCompute(index)" style="text-align: center">删除</a>
        </template>
        <template slot="rptDayStart" slot-scope="text, record, index, columns">
          <a-input-number class="input" :min="0" :precision="0" v-model="calendarParam.rptDayComList[index].rptDayStart"/>
        </template>
        <template slot="rptDayEnd" slot-scope="text, record, index, columns">
          <a-input-number class="input" :min="0" :precision="0" v-model="calendarParam.rptDayComList[index].rptDayEnd"/>
        </template>
        <template slot="rptDayInterval" slot-scope="text, record, index, columns">
          <a-input-number class="input" :min="0" :precision="0" v-model="calendarParam.rptDayComList[index].rptDayInterval"/>
        </template>
      </a-table>
      <template slot="footer">
        <a-button key="back" @click="isShowCompute = false">关闭</a-button>
      </template>
    </a-modal>

    <!-- 天数详情展示弹窗 -->
    <a-modal title="详情" :width="600" :height="1200" :bodyStyle="{ padding: 0 }" :visible="isShowRptDayList"
             style="padding: 0" :maskClosable="false" @cancel="isShowRptDayList = false">
      <a-table class="mt10"
               bordered
               :columns="rptDayColumns"
               :data-source="calendarParam.rptDayList.map(rptDay => { return {rptDay: rptDay} })"
               :pagination="false">
      </a-table>
      <template slot="footer">
        <a-button key="back" @click="isShowRptDayList = false">关闭</a-button>
      </template>
    </a-modal>

    <a-modal title="样品匹配" :width="1200" :bodyStyle="{ padding: '10px 10px 0 10px' }" :visible="isShowConnect" style="padding: 0" :maskClosable="false" @cancel="closeConnectModal">
      <div id="connectContainer" class="connectContainer">
        <!-- 电芯分组展示 -->
        <a-row :gutter="24">
          <div v-for="(sampleGroup, groupIndex) in sampleGroups">
            <a-col :key="groupIndex" :span="colSpan">
              <a-card :bordered="true" size="small" :headStyle="{ textAlign: 'center' }" :title="ordtaskList[groupIndex].folderno">
                <div style="display: flex; flex-direction: column; align-items: center;">
                  <a-button v-for="(item, index) in sampleGroup" :id="item"
                            style="width: fit-content; padding: 0 5px;" :class="lines[selectedLineIndex].some(code => code === item) ? 'selectedLineButton' : ''"
                            @click="handleClick(item, groupIndex, index)">{{ item }}</a-button>
                </div>
              </a-card>
            </a-col>
            <a-col v-if="groupIndex < sampleGroups.length - 1" :span="colSpan"></a-col>
          </div>
        </a-row>

        <!-- 所有连线 -->
        <svg v-if="drawReady" ref="svg" class="overlay-svg">
          <path v-for="(connection, index) in connections" :key="index" :d="drawLine(connection.fromId, connection.toId)" stroke="black" stroke-width="2" fill="none" stroke-dasharray="5,5" />
        </svg>
        <!-- 显示所有连线数据 -->
        <!--      <div v-for="(line, idx) in lines" :key="idx">-->
        <!--        <p>线 {{ idx + 1 }}: {{ line.join(' -> ') }}</p>-->
        <!--      </div>-->
      </div>
    </a-modal>

  </div>
  </a-spin>
</template>

<script>
import orderDataSelectModal from "@/views/system/vTestReport/components/dongLiReport/orderDataSelectModal";
import {getRptParamByOrdtaskid} from "@/api/modular/system/cycleReportManager";

export default {
  name: "calendarReportBuild",
  components: {
    orderDataSelectModal
  },
  props: {
    reportQueryParam: {
      type: Object,
    },
    hasQueryParamFlag: {
      type: Boolean,
      default: false,
    },
    width:{
      type: Number,
      default: 0
    },
    padding:{
      type: String,
      default: '8px'
    }
  },
  data() {
    return {
      isUpdating: false,
      // 日历寿命在线报告查询参数
      calendarParam: {
        rptDayComList: [], // RPT天数计算列表
        rptDayList: [], // RPT天数列表
        rptStepParamList: [], // RPT工步参数列表
        dcirTiTleList: [""], // DCIR标题列表
        standardRptIndex: null,
        lines: [],
        dchCeStepTiTleList: [], // 放电工步标题信息列表
      },
      dcirNum: 1,
      dcirTitleIndexArr: [0],
      dchCeStepNum: 0,
      dchCeStepIndexArr: [],
      // rpt数据表头
      rptParamColumns: [
        {
          title: "操作",
          align: "center",
          width: 100,
          scopedSlots: {customRender: "action"}
        },
        {
          title: "RPT",
          align: "center",
          width: 100,
          customRender: (text, record, index) => index,
        },
        {
          title: "Days",
          align: "center",
          width: 100,
          dataIndex: "day",
          scopedSlots: {customRender: "day"}
        },
        {
          title: "Recharge Days",
          align: "center",
          width: 100,
          dataIndex: "rechargeDay",
          scopedSlots: {customRender: "rechargeDay"}
        },
        {
          title: "样品",
          align: "center",
          width: 230,
          dataIndex: "orderDataList",
          scopedSlots: {customRender: "orderDataList"}
        },
        {
          align: "center",
          width: 100,
          dataIndex: "retentionStep",
          scopedSlots: {customRender: "retentionStep", title: "retentionStepTitle"}
        },
        {
          align: "center",
          width: 120,
          dataIndex: "recoveryStep",
          scopedSlots: {customRender: "recoveryStep", title: "recoveryStepTitle"}
        },
        {
          children: [
            {
              title: "DCIR搁置工步号",
              width: 100,
              align: "center",
              dataIndex: "dcirStepParamList[0].restStep",
              scopedSlots: {customRender: "dcirStepParam"}
            },
            {
              title: "DCIR放电工步号",
              width: 100,
              align: "center",
              dataIndex: "dcirStepParamList[0].dchStep",
              scopedSlots: {customRender: "dcirStepParam"}
            },
            {
              width: 130,
              align: "center",
              dataIndex: "dcirStepParamList[0].dchStepTime",
              scopedSlots: {
                title: "dchStepTimeTitle",
                customRender: "dcirStepParam"
              }
            }
          ],
          scopedSlots: {
            title: "dcirTitle_0"
          }
        },
        {
          align: "center",
          width: 100,
          scopedSlots: {
            customRender: "standardStep",
            title: "standardStepTitle"
          }
        },
        {
          dataIndex: "chCeStep",
          align: "center",
          width: 120,
          scopedSlots: {
            title: "chCeStepTitle",
            customRender: "chCeStep"
          }
        },
      ],
      deleteSelectedRowKeys: [],

      selectedRptIndex: null,
      notRefresh: true,
      selectedOrderDataList: [],

      isShowCompute: false,
      // rpt天数计算列表表头
      rptDayComColumns: [
        {
          title: "操作",
          align: "center",
          width: 40,
          scopedSlots: {customRender: "action"}
        },
        {
          title: "序号",
          align: "center",
          width: 40,
          customRender: (text, record, index) => index + 1
        },
        {
          title: "开始中检天数",
          dataIndex: "rptDayStart",
          align: "center",
          width: 100,
          scopedSlots: {customRender: "rptDayStart"}
        },
        {
          title: "结束中检天数",
          dataIndex: "rptDayEnd",
          align: "center",
          width: 100,
          scopedSlots: {customRender: "rptDayEnd"}
        },
        {
          title: "中检间隔天数",
          dataIndex: "rptDayInterval",
          align: "center",
          width: 100,
          scopedSlots: {customRender: "rptDayInterval"}
        },
        {
          title: "中检天数",
          dataIndex: "rptDayList",
          align: "center",
          width: 200,
          customRender: (text, record, index, column) => {
            let cycles = record.rptDayList || []
            return cycles.join(',')
          }
        },
        {
          title: "中检次数",
          dataIndex: "rptDayNum",
          align: "center",
          width: 100
        },
      ],

      isShowRptDayList: false,
      // rpt天数表头
      rptDayColumns: [
        {
          title: "序号",
          align: "center",
          width: 40,
          customRender: (text, record, index) => index + 1
        },
        {
          title: "中检天数",
          align: "center",
          width: 100,
          dataIndex: 'rptDay',
        },
      ],

      isShowConnect: false,
      modalRect: {left: 0, top: 0},
      drawReady: false,
      ordtaskList: [],
      sampleGroups: [],
      selectedSamples: {},
      lines: [],
      selectedLineIndex: 0,
      colSpan: 8,
      connections: [],
    }
  },
  computed: {
    ordtaskidToWatch() {
      // 创建一个计算属性，用于监听 当前选中行RPT第一个电芯的 ordtaskid
      const rptStepParamList = this.calendarParam.rptStepParamList;
      if (this.selectedRptIndex !== null && rptStepParamList && rptStepParamList.length > this.selectedRptIndex) {
        const orderDataList = rptStepParamList[this.selectedRptIndex].orderDataList;
        if (orderDataList && orderDataList.length > 0) {
          return orderDataList[0].ordtaskid;
        }
      }
      return null; // 如果 ordtaskid 不存在，返回 null
    }
  },
  watch: {
    hasQueryParamFlag (newVal, oldVal) {
      this.init()
    },
    ordtaskidToWatch (newVal, oldVal) {
      console.log("ordtaskidToWatch: ", oldVal, newVal, "notRefresh: ", this.notRefresh)

      if (newVal === null || this.notRefresh) {
        return;
      }

      if (this.selectedRptIndex === 0) {
        this.calendarParam.temp = this.calendarParam.rptStepParamList[0].orderDataList[0].tem
      }

      // RPT当前选中行第一个电芯变化的处理，需要填充电芯数据
      this.$refs.orderDataSelectModal.visible = false
      this.isUpdating = true
      getRptParamByOrdtaskid({ordtaskid: newVal}).then(res => {
        // 覆盖RPT列表参数
        if (res.success) {
          for (let i = 0; i < res.data.length; i++) {
            const rptObj = res.data[i]
            if (this.calendarParam.rptStepParamList.length > this.selectedRptIndex + i) {
              if (this.selectedRptIndex === 0) {
                this.calendarParam.rptStepParamList[this.selectedRptIndex + i].rechargeDay = rptObj.rechargeDay
              }
              this.calendarParam.rptStepParamList[this.selectedRptIndex + i].orderDataList = rptObj.orderDataList
            } else {
              let dcirStepParamList = Array.from({ length: this.dcirNum }, () => ({}))
              let dchCeStepStrList = new Array(this.dchCeStepNum).fill('')
              this.calendarParam.rptStepParamList.push({ orderDataList: rptObj.orderDataList, dcirStepParamList: dcirStepParamList, dchCeStepStrList: dchCeStepStrList })
              if (this.selectedRptIndex === 0) {
                this.calendarParam.rptStepParamList[this.calendarParam.rptStepParamList.length - 1].rechargeDay = rptObj.rechargeDay
              }
            }
          }
        }
      }).finally(() => {
        this.isUpdating = false
        this.allOrderDataChange()
        this.$emit('handleVerify', this._handleVerify())
      })
    },
    lines (newVal, oldVal) {
      console.log("lines change")
      const newConnections = []
      for (let i = 0; i < this.lines.length; i++) {
        const line = this.lines[i]
        if (Array.isArray(line) && line.length > 1) {
          for (let j = 0; j < line.length - 1; j++) {
            newConnections.push({fromId: line[j], toId: line[j+1], groupIndex: j})
          }
        }
      }
      this.connections = newConnections

      // 提交连线数据
      this.$emit('handleVerify', this._handleVerify())
    },
  },
  created() {
    this.init()
  },
  mounted() {
    this.changeRptParamColumns(true)
    this.handleHeight()
  },
  destroyed() {
  },
  methods: {
    allOrderDataChange() {
      console.log("allOrderDataChange")
      // 每个测试项目的电芯为一组
      let ordtaskList = []
      let sampleObjGroups = []
      const rptStepParamList = this.calendarParam.rptStepParamList;
      for (let i = 0; i < rptStepParamList.length; i++) {
        const orderDataList = rptStepParamList[i].orderDataList;
        if (Array.isArray(orderDataList)) {
          orderDataList.forEach(orderData => {
            const ordtaskid = orderData.ordtaskid;
            const folderno = orderData.folderno;
            const sampleCode = orderData.orderno;
            const celltestcode = orderData.celltestcode;
            const batteryCode = celltestcode.replace('-' + sampleCode, '')
            const findIndex = ordtaskList.findIndex(item => item.ordtaskid === ordtaskid);
            if (findIndex !== -1) {
              if (sampleObjGroups[findIndex].findIndex(item => item.sampleCode === sampleCode) === -1) {
                sampleObjGroups[findIndex].push({sampleCode: sampleCode, batteryCode: batteryCode})
              }
            } else {
              ordtaskList.push({ordtaskid: ordtaskid, folderno: folderno})
              sampleObjGroups.push([{sampleCode: sampleCode, batteryCode: batteryCode}])
            }
          })
        }
      }

      // 所有连线均从第一个测试项目的电芯开始，即第一组的电芯
      let lines = []
      if (sampleObjGroups.length > 0 && Array.isArray(sampleObjGroups[0])) {
        lines.push(...sampleObjGroups[0].map(sampleObj => [sampleObj]))
      }
      // 第二组开始，根据电芯编码或编号将电芯加入连线
      for (let groupIndex = 1; groupIndex < sampleObjGroups.length; groupIndex++) {
        const sampleObjGroup = sampleObjGroups[groupIndex];
        for (let i = 0; i < sampleObjGroup.length; i++) {
          const sampleObj = sampleObjGroup[i];
          const lineIndex = lines.findIndex(line => line[0].batteryCode === sampleObj.batteryCode);
          if (lineIndex === -1) {
            continue;
          }
          // 加入连线，防止电芯跨组
          if (lines[lineIndex].length === groupIndex) {
            lines[lineIndex].push(sampleObj)
          } else if (lines[lineIndex].length > groupIndex) {
            lines[lineIndex][groupIndex] = sampleObj
          }
        }
      }

      this.ordtaskList = ordtaskList
      this.sampleGroups = sampleObjGroups.map(sampleObjGroup => sampleObjGroup.map(sampleObj => sampleObj.sampleCode))
      this.colSpan = this.sampleGroups.length > 0 ? Math.floor(24 / (this.sampleGroups.length*2 - 1)) : 8
      this.lines = [...lines.map(line => line.map(sampleObj => sampleObj.sampleCode))]
      this.selectedSamples = Object.fromEntries(this.lines.flat().map(item => [item, true]))
    },
    openConnectModal() {
      if (this.sampleGroups.length === 0) {
        return this.$message.warn('请先选择样品')
      }
      else if (this.sampleGroups.length === 1) {
        return this.$message.warn('同一个单据的样品无需匹配')
      }

      this.isShowConnect = true;
      this.$nextTick(() => {
        const modal = document.getElementById('connectContainer');
        this.modalRect = modal ? modal.getBoundingClientRect() : {left: 0, top: 0};
        this.drawReady = true;
      });
    },
    closeConnectModal() {
      this.drawReady = false
      this.isShowConnect = false
    },
    // 处理样品编号点击事件
    handleClick (sampleCode, groupIndex, index) {
      // 以第一个单据分组电芯定位连线索引
      if (groupIndex === 0) {
        this.selectedLineIndex = index
        return
      }

      if (this.selectedSamples[sampleCode]) {
        // 如果已经选中，则取消选择
        for (let lineIndex = 0; lineIndex < this.lines.length; lineIndex++) {
          const line = this.lines[lineIndex];
          const sampleIndex = line.indexOf(sampleCode);
          if (sampleIndex !== -1) {
            this.selectedLineIndex = lineIndex; // todo 取消选中会定位到之前的连线索引
            // 取消选择该线条当前及之后的电芯
            for (let j = sampleIndex; j < line.length; j++) {
              delete this.selectedSamples[line[j]];
            }
            line.splice(sampleIndex);
            break;
          }
        }
      } else {
        // 否则添加到现有线（更改已有数据或push到最后）
        if (this.lines[this.selectedLineIndex].length > groupIndex) {
          const sampleCodeOld = this.lines[this.selectedLineIndex][groupIndex];
          delete this.selectedSamples[sampleCodeOld];
          this.lines[this.selectedLineIndex].splice(groupIndex, groupIndex, sampleCode); // this.lines[this.selectedLineIndex][groupIndex] = sampleCode;
          this.selectedSamples[sampleCode] = true;
        } else if (this.lines[this.selectedLineIndex].length === groupIndex) {
          this.lines[this.selectedLineIndex].push(sampleCode);
          this.selectedSamples[sampleCode] = true;
        } else {
          // 不能跨组选择电芯
          return this.$message.warn('请先选择单据 ' + (this.ordtaskList[this.lines[this.selectedLineIndex].length].folderno) + ' 的样品')
        }
      }
    },
    // 处理连线
    drawLine(fromId, toId) {
      const fromPoint = this.getElementPoint(fromId, false);
      const toPoint = this.getElementPoint(toId, true);

      // 绘制线条和箭头头部
      return `M ${fromPoint.x},${fromPoint.y} L ${toPoint.x},${toPoint.y}`;
    },
    getElementPoint(elementId, isLeft) {
      const element = document.getElementById(elementId);
      if (!element) return {x: 0, y: 0}; // 防止报错

      const rect = element.getBoundingClientRect();
      const position = {
        left: rect.left - this.modalRect.left,
        right: rect.right - this.modalRect.left,
        top: rect.top - this.modalRect.top,
        height: rect.height
      };
      return {
        x: isLeft ? position.left : position.right,
        y: position.top + position.height / 2
      };
    },

    handleHeight() {
      const minHeight = document.body.clientHeight - 42 - this.width - 2*8 - 8 - 37 - 2*8 - 22.5 - 42 - 2*8 -22.5 - 32 - 8 - 33
      document.documentElement.style.setProperty(`--height`, `${minHeight}px`)
      const width = document.body.clientWidth - 40 - 2*12 - (this.width > 0 ? 2*8 : 0) - 2*12
      document.documentElement.style.setProperty(`--width`, `${width}px`)
    },
    init() {
      if (this.hasQueryParamFlag) {
        this.calendarParam = this.reportQueryParam
        this.allOrderDataChange()
        // 历史建模DCIR计算方案默认为动力计算方案
        if (!this.calendarParam.dcirCalcType) {
          this.$set(this.calendarParam, 'dcirCalcType', 'DongLi')
        }
        this.changeRptParamColumns(true)
        this.$emit('handleVerify', this._handleVerify())
      }
    },

    changeDchCeStepStrList(isDelete = false) {
      if (isDelete) {
        if (this.dchCeStepNum > 0) {
          this.dchCeStepNum--
          this.calendarParam.dchCeStepTiTleList.splice(this.dchCeStepNum, 1)
        } else {
          return
        }
      } else {
        if (this.dchCeStepNum < 9) {
          this.dchCeStepNum++
          this.calendarParam.dchCeStepTiTleList.push("")
        } else {
          return this.$message.warn('最多10列放电容量&能量工步号，无法增加')
        }
      }

      this.changeRptParamColumns(false)
    },
    changeDcirParamList(isDelete = false) {
      if (isDelete) {
        if (this.dcirNum > 1) {
          this.dcirNum--
          this.calendarParam.dcirTiTleList.splice(this.dcirNum, 1)
        } else {
          return this.$message.warn('至少一组DCIR参数，无法减少')
        }
      } else {
        if (this.dcirNum < 4) {
          this.dcirNum++
          this.calendarParam.dcirTiTleList.push("")
        } else {
          return this.$message.warn('最多四组DCIR参数，无法增加')
        }
      }

      this.changeRptParamColumns(false)
    },
    changeRptParamColumns(isFirst = false) {
      if (isFirst) {
        if (Array.isArray(this.calendarParam.dcirTiTleList) && this.calendarParam.dcirTiTleList.length > 0) {
          this.dcirNum = this.calendarParam.dcirTiTleList.length
        } else {
          this.calendarParam.dcirTiTleList = [""]
          this.dcirNum = 1
        }
        if (Array.isArray(this.calendarParam.dchCeStepTiTleList) && this.calendarParam.dchCeStepTiTleList.length > 0) {
          this.dchCeStepNum = this.calendarParam.dchCeStepTiTleList.length
        } else {
          this.calendarParam.dchCeStepTiTleList = []
          this.dchCeStepNum = 0
        }
      }

      for (let i = 0; i < this.calendarParam.rptStepParamList.length; i++) {
        let rptStepParam = this.calendarParam.rptStepParamList[i];

        rptStepParam.dcirStepParamList = rptStepParam.dcirStepParamList || []
        // DCIR参数组赋值 如果长度小于目标长度，则用空对象填充
        const dcirStepLen = rptStepParam.dcirStepParamList.length;
        if (dcirStepLen < this.dcirNum) {
          rptStepParam.dcirStepParamList.push(...Array.from({ length: this.dcirNum }, () => ({})));
        }
        // 删除DCIR需要清空参数组 如果长度大于目标长度，则截断数组
        if (dcirStepLen > this.dcirNum) {
          rptStepParam.dcirStepParamList.length = this.dcirNum;
        }

        rptStepParam.dchCeStepStrList = rptStepParam.dchCeStepStrList || []
        // 放电工步号赋值 如果长度小于目标长度，则用空字符串填充
        const dchStepLen = rptStepParam.dchCeStepStrList.length;
        if (dchStepLen < this.dchCeStepNum) {
          rptStepParam.dchCeStepStrList.push(...new Array(this.dchCeStepNum - dchStepLen).fill(''));
        }
        // 删除放电工步号列需要清除放电工步 如果长度大于目标长度，则截断数组
        if (dchStepLen > this.dchCeStepNum) {
          rptStepParam.dchCeStepStrList.length = this.dchCeStepNum;
        }
      }

      this.rptParamColumns =  [
        {
          title: "操作",
          align: "center",
          width: 100,
          scopedSlots: {customRender: "action"}
        },
        {
          title: "RPT",
          align: "center",
          width: 100,
          customRender: (text, record, index) => index,
        },
        {
          title: "Days",
          align: "center",
          width: 100,
          dataIndex: "day",
          scopedSlots: {customRender: "day"}
        },
        {
          title: "Recharge Days",
          align: "center",
          width: 100,
          dataIndex: "rechargeDay",
          scopedSlots: {customRender: "rechargeDay"}
        },
        {
          title: "样品",
          align: "center",
          width: 230,
          dataIndex: "orderDataList",
          scopedSlots: {customRender: "orderDataList"}
        },
        {
          align: "center",
          width: 100,
          dataIndex: "retentionStep",
          scopedSlots: {customRender: "retentionStep", title: "retentionStepTitle"}
        },
        {
          align: "center",
          width: 120,
          dataIndex: "recoveryStep",
          scopedSlots: {customRender: "recoveryStep", title: "recoveryStepTitle"}
        },
      ]

      // 添加放电容量能量工步号列表
      this.dchCeStepIndexArr = []
      for (let i = 0; i < this.dchCeStepNum; i++) {
        this.rptParamColumns.push(
            {
              dataIndex: "dchCeStepStrList[" + i + "]",
              align: "center",
              width: 130,
              scopedSlots: {
                title: "dchCeStepTitle_" + i,
                customRender: "dchCeStep"
              }
            }
        )

        this.dchCeStepIndexArr.push(i)
      }

      this.dcirTitleIndexArr = []
      for (let i = 0; i < this.dcirNum; i++) {
        const children1 = [
          {
            title: "搁置工步号",
            width: 100,
            align: "center",
            dataIndex: "dcirStepParamList[" + i + "].restStep",
            scopedSlots: {customRender: "dcirStepParam"}
          },
          {
            title: "放电工步号",
            width: 100,
            align: "center",
            dataIndex: "dcirStepParamList[" + i + "].dchStep",
            scopedSlots: {customRender: "dcirStepParam"}
          },
          {
            width: 130,
            align: "center",
            dataIndex: "dcirStepParamList[" + i + "].dchStepTime",
            scopedSlots: {
              title: "dchStepTimeTitle",
              customRender: "dcirStepParam"
            }
          }
        ]
        const styleObj = {maxWidth: '600px'};
        const children2 = [
          {
            title: "放电工步号1",
            width: 100,
            align: "center",
            dataIndex: "dcirStepParamList[" + i + "].dchStep",
            scopedSlots: {customRender: "dcirStepParam"}
          },
          {
            title: <a-tooltip overlayStyle={styleObj} arrow-point-at-center>
              <template slot="title">
                <span>
                  未填写放电工步时间默认取工步数据表数据；<br/>
                  填写了放电工步时间则取详细数据表数据；<br/>
                  格式：HH:mm:ss.SSS，精确匹配；例如：0:00:10.000
                </span>
              </template>
              放电工步时间1 <a-icon type="question-circle" style="color: #1890ff;"/>
            </a-tooltip>,
            width: 130,
            align: "center",
            dataIndex: "dcirStepParamList[" + i + "].dchStepTime",
            scopedSlots: {customRender: "dcirStepParam"}
          },
          {
            title: "放电工步号2",
            width: 100,
            align: "center",
            dataIndex: "dcirStepParamList[" + i + "].dchStep2",
            scopedSlots: {customRender: "dcirStepParam"}
          },
          {
            title: <a-tooltip overlayStyle={styleObj} arrow-point-at-center>
              <template slot="title">
                <span>
                  未填写放电工步时间默认取工步数据表数据；<br/>
                  填写了放电工步时间则取详细数据表数据；<br/>
                  格式：HH:mm:ss.SSS，精确匹配；例如：0:00:10.000
                </span>
              </template>
              放电工步时间2 <a-icon type="question-circle" style="color: #1890ff;"/>
            </a-tooltip>,
            width: 130,
            align: "center",
            dataIndex: "dcirStepParamList[" + i + "].dchStepTime2",
            scopedSlots: {customRender: "dcirStepParam"}
          }
        ]

        this.rptParamColumns.push(
          {
            children: this.calendarParam.dcirCalcType === 'V' ? children2 : children1,
            scopedSlots: {
              title: "dcirTitle_" + i
            }
          }
        )

        this.dcirTitleIndexArr.push(i)
      }

      this.rptParamColumns.push({
        align: "center",
        width: 100,
        scopedSlots: {
          customRender: "standardStep",
          title: "standardStepTitle"
        }
      })

      // 添加充电容量&恒流比工步号
      this.rptParamColumns.push(
          {
            dataIndex: "chCeStep",
            align: "center",
            width: 120,
            scopedSlots: {
              title: "chCeStepTitle",
              customRender: "chCeStep"
            }
          }
      )

      this.$emit('handleVerify', this._handleVerify())
    },
    dcirCalcTypeChange(event) {
      this.$set(this.calendarParam, 'dcirCalcType', event.target.value)
      this.changeRptParamColumns(false)
    },
    
    handleNumberBlur(target = null) {
      if (target && this.calendarParam[target]) {
        this.calendarParam[target] = Number.parseFloat((this.calendarParam[target]+'').replaceAll(/\.+/g, '.').replaceAll(/^\.|\.$/g, ''))
      }
      
      this.$emit('handleVerify', this._handleVerify())
    },

    addOneCompute() {
      this.calendarParam.rptDayComList.push({})
      this.$forceUpdate()
    },
    deleteOneCompute(index) {
      this.calendarParam.rptDayComList.splice(index, 1)
      this.$forceUpdate()
    },
    computeRptDays() {
      if (!Array.isArray(this.calendarParam.rptDayComList) || this.calendarParam.rptDayComList.length === 0) {
        return this.$message.warn('请填写数据')
      }

      if (this.calendarParam.rptDayComList[0].rptDayStart != 0) {
        return this.$message.warn('请填写正确数据，中检天数须由0开始！')
      }

      //校验
      for (let i = 0; i < this.calendarParam.rptDayComList.length; i++) {
        let row = this.calendarParam.rptDayComList[i]

        if (typeof row.rptDayStart !== 'number' || typeof row.rptDayEnd !== 'number' || typeof row.rptDayInterval !== 'number') {
          return this.$message.warn('请填写完整数据')
        }

        if ( (row.rptDayEnd - row.rptDayStart) != row.rptDayInterval && (row.rptDayEnd - row.rptDayStart) % row.rptDayInterval != 0 ) {
          return this.$message.warn('请填写正确数据，开始与结束的差值需为间隔天数的倍数')
        }

        if (i + 1 < this.calendarParam.rptDayComList.length && row.rptDayEnd != this.calendarParam.rptDayComList[i + 1].rptDayStart) {
          return this.$message.warn('请填写正确数据，开始中检天数请与上一阶段的结束中检天数对应')
        }
      }

      // 计算
      for (let i = 0; i < this.calendarParam.rptDayComList.length; i++) {
        let row = this.calendarParam.rptDayComList[i]

        let rptDayList = [row.rptDayStart]
        let sum = row.rptDayStart + row.rptDayInterval
        while (sum < row.rptDayEnd) {
          rptDayList.push(sum)
          sum += row.rptDayInterval
        }

        if (i == this.calendarParam.rptDayComList.length - 1) {
          if (row.rptDayStart != row.rptDayEnd) {
            rptDayList.push(row.rptDayEnd)
          }
        }

        this.$set(this.calendarParam.rptDayComList[i], 'rptDayList', rptDayList)
        this.$set(this.calendarParam.rptDayComList[i], 'rptDayNum', rptDayList.length)
      }

      // 赋值给日历寿命RPT参数
      this.refreshRptDayList()
    },
    refreshRptDayList() {
      this.calendarParam.rptDayList = []
      for (let i = 0; i < this.calendarParam.rptDayComList.length; i++) {
        let row = this.calendarParam.rptDayComList[i]
        this.calendarParam.rptDayList.push(
          ...row.rptDayList
        )
      }

      // 添加到rptStepParamList
      this.calendarParam.rptDayList.forEach((rptDay, index) => {
        if (this.calendarParam.rptStepParamList.length > index) {
          this.calendarParam.rptStepParamList[index].day = rptDay
        } else {
          let dcirStepParamList = Array.from({ length: this.dcirNum }, () => ({}))
          let dchCeStepStrList = new Array(this.dchCeStepNum).fill('')
          this.calendarParam.rptStepParamList.push({ day: rptDay, orderDataList: [], dcirStepParamList: dcirStepParamList, dchCeStepStrList: dchCeStepStrList })
        }
      })

      this.$emit('handleVerify', this._handleVerify())
    },

    deleteRowOnChange(selectedRowKeys, selectedRows) {
      this.deleteSelectedRowKeys = selectedRowKeys
    },
    addOneRpt(rptIndex) {
      let dcirStepParamList = Array.from({ length: this.dcirNum }, () => ({}))
      let dchCeStepStrList = new Array(this.dchCeStepNum).fill('')
      this.calendarParam.rptStepParamList.splice(rptIndex, 0, { orderDataList: [], dcirStepParamList: dcirStepParamList, dchCeStepStrList: dchCeStepStrList })
    },
    deleteSelectRpt() {
      this.calendarParam.rptStepParamList = this.calendarParam.rptStepParamList.filter((item, index) => !this.deleteSelectedRowKeys.includes(index));
      this.deleteSelectedRowKeys = []

      this.calendarParam.rptDayComList = []
      this.calendarParam.rptDayList = []

      this.calendarParam.standardRptIndex = null

      this.allOrderDataChange()
      this.$emit('handleVerify', this._handleVerify())
    },
    deleteOneRpt(index) {
      this.calendarParam.rptStepParamList.splice(index, 1)
      if (this.calendarParam.standardRptIndex >= index) {
        this.calendarParam.standardRptIndex = null
      }
      this.allOrderDataChange()
      this.$emit('handleVerify', this._handleVerify())
    },

    handleShowOrderDataModal(record, index) {
      // 当前RPT已有电芯则无需更新
      this.selectedRptIndex = index
      this.notRefresh = Array.isArray(record.orderDataList) && record.orderDataList.length > 0

      this.selectedOrderDataList = record.orderDataList
      this.$refs.orderDataSelectModal.visible = true
    },
    handleFold(record) {
      // 折叠状态变更
      record.isUnfolded = !record.isUnfolded

      // 强制刷新页面
      this.$forceUpdate()
    },
    handleDeleteOne(record, index) {
      record.orderDataList.splice(index, 1)

      // 样品匹配相关变量可能变化
      this.allOrderDataChange()
      this.$emit('handleVerify', this._handleVerify())
    },
    handleDeleteAll(record) {
      record.orderDataList = []

      // 样品匹配相关变量可能变化
      this.allOrderDataChange()
      this.$emit('handleVerify', this._handleVerify())
    },

    verifyStepNumber(rptIndex, type) {
      if (this.calendarParam.rptStepParamList[rptIndex][type]) {
        this.calendarParam.rptStepParamList[rptIndex][type] = (this.calendarParam.rptStepParamList[rptIndex][type] + "").replaceAll(type === "recoveryStep" ? /[^0-9,，]/g : /[^0-9]/g, "")
      }
      
      this.$emit('handleVerify', this._handleVerify())
    },
    handleBlur() {
      // 解决无法感知列表参数变化的问题
      this.$emit('handleVerify', this._handleVerify())
    },
    onRadioChange(rptIndex) {
      // 选择当前行索引为基准工步索引
      this.calendarParam.standardRptIndex = rptIndex
      
      this.$emit('handleVerify', this._handleVerify())
    },

    copyFromExcel(event, rptIndex, startColumnIndex, dcirDataIndex) {
      const dchColNum = Array.isArray(this.calendarParam.dchCeStepTiTleList) ? this.calendarParam.dchCeStepTiTleList.length : 0
      const isV = this.calendarParam.dcirCalcType === 'V';
      // excel复制末尾会有换行符，split后数组多一个空串，先去除
      let rows = event.clipboardData.getData("text").replace(/[\n]$/, "").split("\n")

      let startDcirType
      let startDcirIndex = 0
      if (startColumnIndex === null && dcirDataIndex) {
        startDcirType = dcirDataIndex.replace('dcirStepParamList[','').substring(3)
        let keyIndex
        if (isV) {
          switch (startDcirType) {
            case 'dchStepTime2':
              keyIndex = 4;
              break;
            case 'dchStep2':
              keyIndex = 3;
              break;
            case 'dchStepTime':
              keyIndex = 2;
              break;
            default:
              keyIndex = 1;
          }
        } else {
          keyIndex = startDcirType === 'dchStepTime' ? 3 : startDcirType === 'dchStep' ? 2 : 1
        }

        startDcirIndex = Number(dcirDataIndex.replace('dcirStepParamList[','').charAt(0))
        startColumnIndex = 6 + dchColNum + startDcirIndex * (isV ? 4 : 3) + keyIndex
      }

      let firstData
      // 起始行：rptIndex，结束行：math.min(rptIndex + rows.length, this.calendarParam.rptStepParamList.length - 1)
      for (let i = rptIndex; i < this.calendarParam.rptStepParamList.length && i < rptIndex + rows.length; i++) {
        let rowList = rows[i-rptIndex].split("\t")
        if (i === rptIndex) {
          let type = ''
          if (startColumnIndex <= 6 + dchColNum) {
            type = this.rptParamColumns[startColumnIndex].dataIndex
          } else {
            type = startDcirType
          }
          firstData = rowList[0].replaceAll(type === "recoveryStep" || type.includes('dchCeStepStrList') ? /[^0-9,，]/g : type.includes("dchStepTime") ? /[^0-9:.]/g : /[^0-9]/g, "")
        }

        // 起始列：dataIndex所在列，结束列：math.min(startColumnIndex + rowList.length, 6 + dchColNum + (isV ? 4 : 3) * this.dcirNum)
        for (let j = startColumnIndex; j <= (6 + dchColNum + (isV ? 4 : 3) * this.dcirNum) && j < startColumnIndex + rowList.length; j++) {
          if (j <= 6 + dchColNum) {
            const type = this.rptParamColumns[j].dataIndex
            if (type.includes('dchCeStepStrList')) {
              const dchColIndex = Number(type.replace('dchCeStepStrList[','').charAt(0))
              this.calendarParam.rptStepParamList[i].dchCeStepStrList[dchColIndex] = rowList[j-startColumnIndex].replaceAll(/[^0-9,，]/g, "")
            } else {
              this.calendarParam.rptStepParamList[i][type] = rowList[j-startColumnIndex].replaceAll(type === "recoveryStep" ? /[^0-9,，]/g : /[^0-9]/g, "")
            }
          } else {
            const dcirIndex = Math.floor((j - 7 - dchColNum) / (isV ? 4 : 3))
            let type = this.rptParamColumns[7 + dchColNum].children[(j - 7 - dchColNum) % (isV ? 4 : 3)].dataIndex
            type = type.replace('dcirStepParamList[','').substring(3)
            // console.log("type: ", type)
            this.calendarParam.rptStepParamList[i].dcirStepParamList[dcirIndex][type] = rowList[j-startColumnIndex].replaceAll(type.includes("dchStepTime") ? /[^0-9:.]/g : /[^0-9]/g, "")
          }

        }
      }

      // 解决第一个单元格被覆盖的问题
      setTimeout(() => {
        if (startColumnIndex <= 6 + dchColNum) {
          const type = this.rptParamColumns[startColumnIndex].dataIndex
          if (type.includes('dchCeStepStrList')) {
            const dchColIndex = Number(type.replace('dchCeStepStrList[','').charAt(0))
            this.calendarParam.rptStepParamList[rptIndex].dchCeStepStrList[dchColIndex] = firstData
          } else {
            this.calendarParam.rptStepParamList[rptIndex][type] = firstData
          }
        } else {
          this.calendarParam.rptStepParamList[rptIndex].dcirStepParamList[startDcirIndex][startDcirType] = firstData
        }

        this.$emit('handleVerify', this._handleVerify())
      }, 10)
    },

    // 校验
    _handleVerify() {
      this.$forceUpdate()

      this.calendarParam.lines = this.lines
      
      if (!this.calendarParam.projectName) {
        return [false, '请填写项目名称']
      }

      if (!this.calendarParam.phase) {
        return [false, '请填写样品阶段']
      }

      if (!this.calendarParam.temp && this.calendarParam.temp !== 0) {
        return [false, '请填写温度']
      }

      if (!this.calendarParam.soc && this.calendarParam.soc !== 0) {
        return [false, '请填写SOC']
      }

      const rptStepParamList = this.calendarParam.rptStepParamList;
      const findIndex = rptStepParamList.findIndex(item => !item.day && item.day != 0)
      if (findIndex !== -1) {
        return [false, '请填写完整中检天数']
      }

      // ---------------------- 校验：基准工步索引，基准工步行数据 ------------------------
      let standardRptIndex = this.calendarParam.standardRptIndex
      if (typeof standardRptIndex !== 'number') {
        return [false, '请选择基准工步']
      } else {
        let standardRptParam = rptStepParamList[standardRptIndex]

        if (!standardRptParam.retentionStep) {
          return [false, '请填写基准行的容量&能量保持率工步号']
        }
        if (!standardRptParam.recoveryStep) {
          return [false, '请填写基准行的容量&能量恢复率工步号']
        }

        const dchCeStepTiTleList = this.calendarParam.dchCeStepTiTleList
        if (this.dchCeStepNum > 0) {
          for (let i = 0; i < dchCeStepTiTleList.length; i++) {
            if (!dchCeStepTiTleList[i]) {
              return [false, '请填写第 ' + (i+1) + ' 个放电容量&能量工步号标题']
            }
            if (standardRptParam.dchCeStepStrList.length > i && !standardRptParam.dchCeStepStrList[i]) {
              return [false, '请填写基准行第 ' + (i+1) + ' 个放电容量&能量工步号']
            }
          }
        }

        // ---------------------- 校验：DCIR参数组标题填写 ------------------------
        let dcirTiTleList = this.calendarParam.dcirTiTleList
        const isV = this.calendarParam.dcirCalcType === 'V'
        const hasDcirParam = rptStepParamList.some(rptStepParam => rptStepParam.dcirStepParamList.some(item => item.dchStep || (!isV && item.restStep) || (isV && item.dchStep2)))
        if (hasDcirParam) {
          for (let i = 0; i < dcirTiTleList.length; i++) {
            if (!dcirTiTleList[i]) {
              return [false, '请填写第 ' + (i+1) + ' 组DCIR参数组标题']
            }
          }

          if (isV) {
            for (let i = 0; i < standardRptParam.dcirStepParamList.length; i++) {
              const dcirParam = standardRptParam.dcirStepParamList[i];
              if (!dcirParam.dchStep) {
                return [false, '请填写基准行第 ' + (i+1) + ' 组DCIR放电工步号1']
              }
              if (!dcirParam.dchStep2) {
                return [false, '请填写基准行第 ' + (i+1) + ' 组DCIR放电工步号2']
              }
            }
          } else {
            for (let i = 0; i < standardRptParam.dcirStepParamList.length; i++) {
              const dcirParam = standardRptParam.dcirStepParamList[i];
              if (dcirParam.dchStep && !dcirParam.restStep) {
                return [false, '请填写基准行第 ' + (i+1) + ' 组DCIR搁置工步号']
              }
              if (dcirParam.restStep && !dcirParam.dchStep) {
                return [false, '请填写基准行第 ' + (i+1) + ' 组DCIR放电工步号']
              }
            }
          }

        }

        // if (!Array.isArray(dcirTiTleList) || dcirTiTleList.length === 0) {
        //   return [false, '请填写DCIR参数组标题']
        // }
        // for (let i = 0; i < dcirTiTleList.length; i++) {
        //   if (!dcirTiTleList[i]) {
        //     return [false, '请填写第 ' + (i+1) + ' 组DCIR参数组标题']
        //   }
        // }

        this.handleRecoveryStep()

        return [true, this.calendarParam]
      }
    },
    handleRecoveryStep() {
      let rptStepParamList = this.calendarParam.rptStepParamList
      if (Array.isArray(rptStepParamList) && rptStepParamList.length > 0) {
        for (let i = 0; i < rptStepParamList.length; i++) {
          let rptStepParam = rptStepParamList[i]
          if (rptStepParam.recoveryStep) {
            rptStepParam.recoveryStep = (rptStepParam.recoveryStep + "")
              .replaceAll(/[\t\n\s]/g, ',').replaceAll(/[^0-9,]/g, ',') // 替换所有非法字符为英文逗号
              .replaceAll(/,+/g, ",") // 将连续的逗号替换为单个逗号
              // .replaceAll(/^,|,$/g, "") // 去除字符串开头和结尾的逗号

            rptStepParam.recoveryStepList = rptStepParam.recoveryStep.replaceAll(/^,|,$/g, "").split(",")
          } else {
            rptStepParam.recoveryStepList = []
          }

          if (Array.isArray(rptStepParam.dchCeStepStrList)) {
            rptStepParam.dchCeStepList = Array.from({length:rptStepParam.dchCeStepStrList.length}, () => ([]))
            for (let j = 0; j < rptStepParam.dchCeStepStrList.length; j++) {
              rptStepParam.dchCeStepStrList[j] = (rptStepParam.dchCeStepStrList[j] + "")
                  .replaceAll(/[\t\n\s]/g, ',').replaceAll(/[^0-9,]/g, ',') // 替换所有非法字符为英文逗号
                  .replaceAll(/,+/g, ",") // 将连续的逗号替换为单个逗号
              // .replaceAll(/^,|,$/g, "") // 去除字符串开头和结尾的逗号

              rptStepParam.dchCeStepList[j] = rptStepParam.dchCeStepStrList[j].replaceAll(/^,|,$/g, "").split(",")
            }
          } else {
            rptStepParam.dchCeStepList = []
          }
          if (rptStepParam.chCeStep) {
            rptStepParam.chCeStep = (rptStepParam.chCeStep + "")
                .replaceAll(/[\t\n\s]/g, ',').replaceAll(/[^0-9,]/g, ',') // 替换所有非法字符为英文逗号
                .replaceAll(/,+/g, ",") // 将连续的逗号替换为单个逗号
            // .replaceAll(/^,|,$/g, "") // 去除字符串开头和结尾的逗号

            rptStepParam.chCeStepList = rptStepParam.chCeStep.replaceAll(/^,|,$/g, "").split(",")
          } else {
            rptStepParam.chCeStepList = []
          }
        }
      }
    },

  }
}
</script>

<style lang="less" scoped>
:root {
  --height: calc(100vh - 42px - 8*2px - 8px - 37px - 8*2px - 22.5px - 42px - 8*2px - 22.5px - 32px - 8px - 33px);
  --width: calc(100vw - 40px - 12*2px - 12*2px);
}

.mt10 {
  margin-top: 10px;
}

.ml5 {
  margin-left: 5px;
}

.ml10 {
  margin-left: 10px;
}

.calendar-wrapper {
  padding: 8px 12px;
  display: flex;
  flex-wrap: wrap;
}

h3 {
  font-size: 15px;
  font-weight: bold;
  padding: 0;
  margin: 0;
}

.flex-sb-center-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.normal-btn {
  padding: 5px 10px;
  margin: 3px 0;
  color: #fff;
  background-color: #1890ff;
  letter-spacing: 2px;
  cursor: pointer;
}

/deep/ .rpt-table .ant-table-body {
  border: 1px solid #e8e8e8;
  height: var(--height) !important;
  width: var(--width) !important;
  overflow-y: scroll; /* 强制显示垂直滚动条 */
  overflow-x: scroll; /* 强制显示水平滚动条 */
}

/deep/ .ant-table-thead {
  position: sticky;
  top: 0;
  z-index: 2;
}

/deep/ .ant-table-thead > tr > th {
  padding: 5px !important;
  font-size: 13px !important;
  color: rgba(0, 0, 0, .85) !important;
  font-weight: 500 !important;
}

/deep/ .ant-table-tbody > tr > td {
  padding: 0px !important;
  font-size: 12px !important;
  color: #333 !important;
  font-weight: 400 !important;
}

/* /deep/ .rpt-table .ant-table-body::-webkit-scrollbar {
  height: 8px;
  width: 6px;
}

/deep/ .rpt-table .ant-table-body::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;

  background: #dddbdb;
}

/deep/ .rpt-table .ant-table-body::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: #f1f1f1;
} */

/deep/ .rpt-table .ant-table-placeholder {
  border: none !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 0;
}

/deep/ .rpt-table .ant-empty-normal {
  margin: -2px 0;
}

/deep/ .rpt-table .ant-empty-image {
  display: none;
}

/deep/ .ant-table-footer {
  padding: 0;
}

.footer-btn {
  width: 100%;
  height: 32px;
  border: 1px solid #e8e8e8;
  background: #fff;
  color: #999;
  font-size: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.footer-btn:hover {
  color: #1890ff;
}

.input {
  width: 100%;
  text-align: center;
  border: 0;
}

.samples-block {
  padding: 2px 0;
}

.shrink-btn {
  color: #1890ff;
  cursor: pointer;
  font-size: 12px;
}

.btn-icon {
  font-size: 16px;
  color: #1890ff;
  cursor: pointer;
}

.info-row-div {
  display: flex;
  flex-wrap: wrap;
}

.label-span {
  width: 80px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-right: 10px;
  margin-top: 10px;
}

.number-input {
  width: 100px;
  text-align: left;
  margin-top: 10px;
}

.input-number {
  width: 100%;
  border: 0;
}

/deep/ .input-number .ant-input-number-input {
  text-align: center;
}

.connectContainer {
  position: relative; /* 确保子元素可以相对于此容器定位 */
  width: 100%;
  height: fit-content;
}

.overlay-svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none; /* 确保SVG不会拦截鼠标事件 */
  z-index: 11; /* 确保SVG画线展示 */
}

.selectedLineButton {
  background-color: #1890ff;
  color: white;
}
</style>