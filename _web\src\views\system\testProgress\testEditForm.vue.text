<template>
  <a-modal title="编辑" :width="800"
           :bodyStyle="{padding:0}"
           :visible="visible" :confirmLoading="confirmLoading" @ok="handleSubmit" style="padding: 0"
           @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">

      <a-form :form="form">
        <a-form-item style="display: none;" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['id']" />
        </a-form-item>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24">
            <a-form-item label="测试申请单" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder="请输入测试申请单"
                       v-decorator="['testCode', {rules: [{required: true, message: '请输入测试申请单！'}]}]"/>
            </a-form-item>
          </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item label="数量" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                <a-input placeholder="请输入数量"
                         v-decorator="['quantity', {rules: [{required: true, message: '请输入数量！'}]}]"/>
              </a-form-item>
            </a-col>
           <!-- <a-form-item label="测试类型" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-select v-decorator="['type', {rules: [{required: true, message: '请选择测试周期！'}]}]" style="width: 100%">

                <a-select-option value="seven">
                  案例1(中检第七天)
                </a-select-option>
                <a-select-option value="twenty-eight">
                  案例2(中检第二十八天)
                </a-select-option>
                <a-select-option value="thirty">
                  案例3(中检第三十天)
                </a-select-option>

              </a-select>
            </a-form-item>-->



        </a-row>

        <a-row :gutter="24">
          <a-col :md="12" :sm="24">
            <a-form-item label="产品名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input placeholder="请输入产品名称"
                       v-decorator="['productName', {rules: [{required: true, message: '请输入产品名称！'}]}]"/>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item label="产品样品阶段" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input placeholder="请输入产品样品阶段"
                       v-decorator="['productSampleStage', {rules: [{required: true, message: '请输入产品样品阶段！'}]}]"/>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :md="12" :sm="24">
            <a-form-item label="测试类型" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-select v-decorator="['testType', {rules: [{required: true, message: '请输入测试类型！'}]}]" style="width: 100%">

                <a-select-option value="研发测试">
                  研发测试
                </a-select-option>
                <a-select-option value="产品验证测试">
                  产品验证测试
                </a-select-option>
                <a-select-option value="thirty">
                  产品鉴定测试
                </a-select-option>

              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item label="申请部门" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input placeholder="请输入申请部门"
                       v-decorator="['dept', {rules: [{required: true, message: '请输入申请部门！'}]}]"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24">
            <a-form-item label="申请人" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input placeholder="请输入申请人"
                       v-decorator="['applicant', {rules: [{required: true, message: '请输入申请人！'}]}]"/>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item label="测试项目" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input placeholder="请输入测试项目"
                       v-decorator="['testProject', {rules: [{required: true, message: '请输入测试项目！'}]}]"/>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :md="12" :sm="24">
            <a-form-item label="T/℃" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input placeholder="请输入T/℃"
                       v-decorator="['t', {rules: [{required: true, message: '请输入T/℃！'}]}]"/>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item label="SOC" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input placeholder="请输入soc"
                       v-decorator="['soc', {rules: [{required: true, message: '请输入soc！'}]}]"/>
            </a-form-item>
          </a-col>
        </a-row>




        <a-row :gutter="24">
          <a-col :md="12" :sm="24">

            <a-form-item label="测试技师" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input placeholder="请输入测试技师"
                       v-decorator="['testMan', {rules: [{required: true, message: '请输入测试技师！'}]}]"/>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item label="测试地点" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-select v-decorator="['testAddress', {rules: [{required: true, message: '请输入测试地点！'}]}]" style="width: 100%">

                <a-select-option value="A1_3F">
                  A1_3F
                </a-select-option>
                <a-select-option value="B4_3F">
                  B4_3F
                </a-select-option>
                <a-select-option value="R2_2F">
                  R2_2F
                </a-select-option>
                <a-select-option value="R4_3F">
                  R4_3F
                </a-select-option>
                <a-select-option value="R4_4F">
                  R4_4F
                </a-select-option>
                <a-select-option value="R4_5F">
                  R4_5F
                </a-select-option>

              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :md="12" :sm="24">


            <a-form-item label="样品类型" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">

              <a-select v-decorator="['sampleType', {rules: [{required: true, message: '请输入样品类型！'}]}]" style="width: 100%">

                <a-select-option value="G圆柱">
                  G圆柱
                </a-select-option>
                <a-select-option value="C圆柱">
                  C圆柱
                </a-select-option>
                <a-select-option value="V圆柱">
                  V圆柱
                </a-select-option>
                <a-select-option value="方形">
                  方形
                </a-select-option>
                <a-select-option value="软包_396389">
                  软包_396389
                </a-select-option>
                <a-select-option value="软包_动力">
                  软包_动力
                </a-select-option>
                <a-select-option value="模组">
                  模组
                </a-select-option>

              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item label="测试状态" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-select v-decorator="['testStatus', {rules: [{required: true, message: 'testStatus！'}]}]" style="width: 100%">

                <a-select-option value="Done">
                  Done
                </a-select-option>
                <a-select-option value="Ongoing">
                  Ongoing
                </a-select-option>
                <a-select-option value="Plan">
                  Plan
                </a-select-option>


              </a-select>
            </a-form-item>
          </a-col>
        </a-row>


       <!-- <a-row :gutter="24">

          <a-col :md="12" :sm="24">
            <a-form-item label="已完成天数" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input placeholder="请输入已完成天数"
                       v-decorator="['finishDay', {rules: [{required: true, message: '请输入已完成天数！'}]}]"/>
            </a-form-item>
          </a-col>
        </a-row>-->
      </a-form>

    </a-spin>
  </a-modal>
</template>

<script>
  import {
    testProgressUpdate
  } from '@/api/modular/system/testProgressManager'
  import moment from "moment";

  export default {
    props: {
      type: {
        type: String,
        default: ''
      },
    },
    data() {
      return {
        startDate: null,
        labelCol: {
          xs: {
            span: 12
          },
          sm: {
            span: 8
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 14
          }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this),
      }
    },

    methods: {
      add() {
        this.visible = true
      },
      edit(record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(record)



        }, 100)
      },
      onChangeSampleDate(date, dateString) {
        if (date == null) {
          this.startDate = ''
        } else {
          this.startDate = moment(date).format('YYYY-MM-DD')
        }
      },
      handleSubmit() {
        const {
          form: {
            validateFields
          }
        } = this

        this.confirmLoading = true
        validateFields((errors, values) => {

          if (!errors) {
            testProgressUpdate(values).then((res) => {
              this.confirmLoading = false
              if (res.success) {
                this.$message.success('修改成功')
                this.handleCancel()
                this.$emit('ok', values)
              } else {
                this.$message.error('修改失败：' + res.message)
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel() {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
<style lang="less" scoped>
  .ant-form-item {

    margin-bottom: 0px;

  }

  /deep/.ant-modal-body {
    padding: 0!important;
  }
</style>
