<template>
  <a-modal title="工作转办" :visible="true" :width="1200" :centered="true" @cancel="handleCancel">
    <div>
      <div class="step-wrapper">
        <a-steps :current="current">
          <a-step title="待办选择" />
          <a-step title="测试员选择" />
          <a-step v-if="isHaveLeader" title="责任人选择" />
        </a-steps>
      </div>
      <!-- 待办选择  -->
      <div v-if="current === 0">
        <a-spin :spinning="taskSpinning">
          <div class="operate-wrapper">
            <div class="operate-row-left">
              <div class="operate-row">
                <span class="label">委托单号:</span>
                <a-input v-model="searchParams.searchValue" placeholder="请输入委托单号" style="width: 200px;"
                  @change="debouncedSearch" @key.enter="searchChange" />
              </div>
              <div class="operate-row">
                <span class="label">测试员:</span>
                <a-input v-model="searchParams.searchTesterValue" placeholder="多个测试员以英文逗号分隔" style="width: 200px;"
                  @change="debouncedSearch" @key.enter="searchChange" />
              </div>
            </div>

          </div>
          <div class="table-chil-wrapper">

            <a-table size="small" :columns="columns" :dataSource="tableData" class="mt10" bordered
              :rowKey="record => record.id" :pagination="false" :rowSelection="{
              type:'checkbox',
              selectedRows: selectedRows,
              selectedRowKeys: selectedRowKeys,
              onChange: onSelectChange
            }">
            </a-table>
          </div>
        </a-spin>
      </div>

      <!-- 测试员选择 -->
      <div v-if="current === 1">
        <a-spin :spinning="testerSpinning">
          <div class="operate-wrapper">
            <div class="operate-row-left">
              <div class="operate-row">
                <span class="label">测试员姓名:</span>
                <a-input v-model="searchTesterParam.USERNAME" placeholder="请输入测试员姓名" style="width: 200px;"
                  @change="debouncedSearchTesters" @key.enter="searchTesters" />
              </div>
              <div class="operate-row">
                <span class="label">测试员工号:</span>
                <a-input v-model="searchTesterParam.ID" placeholder="请输入测试员工号" style="width: 200px;"
                  @change="debouncedSearchTesters" @key.enter="searchTesters" />
              </div>
            </div>
          </div>
          <div class="table-chil-wrapper">
            <a-table size="small" ref="personTable" :columns="personColumns" :data-source="personResultData" bordered
              :pagination="false" :rowKey="(record) => record.ID" :row-selection="{
                  type: tableType,
                  selectedRowKeys: personSelectedRowKeys,
                  selectedRows: personSelectedRows,
                  onChange: personOnSelect,
                  columnWidth:20
                 }">
            </a-table>
          </div>
        </a-spin>
      </div>

      <!-- 负责人选择（只有测试员超过两个时需要） -->
      <div v-if="current === 2">
        <div class="operate-wrapper" style="justify-content: center;padding: 50px;">
          <div class="operate-row-left">
            <div class="operate-row">
              <span class="label">负责人:</span>
              <a-select v-model="tester" :options="personSelectedOptions" style="width: 200px" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <template slot="footer">
      <a-button v-if="current > 0" @click="handleStep(-1)">上一步</a-button>
      <a-button v-if="(isHaveLeader && current < 2) || (!isHaveLeader && current < 1)" type="primary"
        @click="handleStep(1)">下一步</a-button>
      <a-button v-if="(isHaveLeader && current == 2) || (!isHaveLeader && current == 1)" type="primary"
        @click="handleTransfer">转办</a-button>
    </template>
  </a-modal>
</template>
<script>
  import _ from "lodash";
  import { mapGetters } from "vuex";
  import {
    getTodoTaskList
  } from "@/api/modular/system/testProgressManager"
  import { getTestPerson } from "@/api/modular/system/limsManager";
  import { executeAlterTesterOrPlanTime, executeAlterYfTester } from "@/api/modular/system/testProgressManager";

  export default {
    computed: {
      ...mapGetters(['userInfo'])
    },
    data() {
      return {
        current: 0,  // 0 选择待办  1 选择转办测试员 2 如果是多测试员,还需要选择负责人
        taskSpinning: false,
        isHaveLeader: false,
        searchParams: {},
        debouncedSearch: null,
        tableData: [],
        selectedRows: [],
        selectedRowKeys: [],
        columns: [
          {
            title: '序号',
            dataIndex: 'index',
            align: 'center',
            width: 45,
            ellipsis: true,
            customRender: (text, record, index) => `${index + 1}`
          },
          {
            title: "项目名称",
            align: "center",
            dataIndex: "testName",
            scopedSlots: {
              customRender: "testName"
            }
          },
          {
            title: "委托单号",
            align: "center",
            dataIndex: "folderNo"
          },
          {
            title: "测试员",
            align: "center",
            dataIndex: "tester",
            scopedSlots: {
              filterDropdown: "testerFilterDropdown",
              filterIcon: "testerFilterIcon",
            }
          },
          {
            title: "计划开始日期",
            align: "center",
            dataIndex: "planStartTime",
            customRender: (text, record, index) => {
              return text.split(" ")[0]
            }
          },
          {
            title: "计划结束日期",
            align: "center",
            dataIndex: "planEndTime",
            customRender: (text, record, index) => {
              return text.split(" ")[0]
            },
            scopedSlots: {
              filterDropdown: "filterDropdown",
              filterIcon: "filterIcon",
            }
          },
          {
            title: "任务状态",
            align: "center",
            dataIndex: "taskStatus",
            scopedSlots: {
              customRender: "taskStatus"
            }
          }
        ],

        tableType: '', // 表格类型，单选or多选
        laboratoryId: '',
        testerSpinning: false,
        searchTesterParam: {},
        debouncedSearchTesters: null,
        personResultData: [],
        allPersonResultData: [],
        personSelectedRowKeys: [],
        personSelectedRows: [],
        personSelectedOptions: [], // 两个及以上测试人，需要选择负责人  （精密实验室、安全实验室可选择多人）
        personColumns: [
          {
            title: '序号',
            dataIndex: 'index',
            align: 'center',
            width: 30,
            ellipsis: true,
            customRender: (text, record, index) => `${index + 1}`
          },
          {
            title: '工号',
            dataIndex: 'ID',
            align: 'center',
            width: 40,
          },
          {
            title: '姓名',
            dataIndex: 'USERNAME',
            align: 'center',
            width: 40,
          },
          {
            title: '部门',
            dataIndex: 'ORGNAME',
            align: 'center',
            width: 50,
          },
          {
            title: '在测测试项目数量',
            dataIndex: 'ORDTASKNUMBER',
            align: 'center',
            width: 50,
            scopedSlots: { customRender: 'ORDTASKNUMBER' },
          },
          {
            title: '在测样品数量',
            dataIndex: 'ORDERNUMBER',
            align: 'center',
            width: 50,
          },
          {
            title: '在测委托单数量',
            dataIndex: 'FOLDERNUMBER',
            align: 'center',
            width: 50,
          },
        ],

        tester: null // 负责人

      }
    },
    created() {
      this.getTodoTaskList()

      this.debouncedSearch = _.debounce(this.searchChange, 500);
      this.debouncedSearchTesters = _.debounce(this.searchTesters, 500);
    },
    mounted() {
      if (this.userInfo.account === "superAdmin") {
        this.laboratoryId = "HZ_YJ_DL_JM"
        this.personLoadData()
      } else {
        // "第零实验室-测试组长"和"第零实验室-计划管理员"角色才能看到【第零实验室】标签页
        let jm = this.userInfo.roles.some(item => item.id === "1712686842365419522" || item.id === "1720008384229163010")
        // "研发检测中心-测试组长"和"研发检测中心-PMC"角色才能看到【研发检测中心】标签页
        let cs = this.userInfo.roles.some(item => item.id === "1676772241413427202" || item.id === "1722493545281904641")
        // "第六实验室(JM)-测试组长"和"第六实验室(JM)-PMC"角色才能看到【第六实验室(JM)】标签页
        let jmcsList = this.userInfo.roles.filter(item => item.id === "1839274430373945345" || item.id === "1839275601205518338")
        // "第四实验室-测试组长"和"第四实验室-PMC"角色才能看到【第四实验室】标签页
        let aq = this.userInfo.roles.some(item => item.id === "1754070159908036609" || item.id === "1773588429358874625")

        if (jm) {
          this.laboratoryId = "HZ_YJ_DL_JM"
        } else if (cs) {
          this.laboratoryId = "HZ_YJ_DL_CS"
        } else if (jmcsList) {
          this.laboratoryId = "JM_YJ_DL_CS"
        } else if (aq) {
          this.laboratoryId = "HZ_YJ_DL_AQ"
        }
        this.personLoadData()
      }
    },

    methods: {
      async getTodoTaskList() {
        this.taskSpinning = true
        const temList = []

        const params = {
          pageNo: 1,
          pageSize: 10000,
          searchValue: this.searchParams.searchValue,
          searchTesterValue: this.searchParams.searchTesterValue,
        }
        for (let i = 1; i < 6; i++) {
          if (i !== 4) {
            params.searchType = i + ''
            await getTodoTaskList(params).then((res) => {
              temList.push(...res.data.rows)
            })
          }
        }


        this.tableData = _.uniqBy(temList, 'id')
        this.taskSpinning = false

      },
      personLoadData() {
        this.testerSpinning = true

        getTestPerson({ status: "deprecated", orgId: this.laboratoryId, roleId: "699586598579968" }).then((res) => {
          this.personResultData = res.data
          this.allPersonResultData = JSON.parse(JSON.stringify(res.data)) // 深克隆
        }).finally(() => {
          this.testerSpinning = false
        })
      },
      searchChange(e) {
        this.getTodoTaskList()
      },
      searchTesters() {
        this.testerSpinning = true
        if (this.searchTesterParam.ID) {
          this.personResultData = this.allPersonResultData.filter(item => item.ID.includes(this.searchTesterParam.ID))
        }
        if (this.searchTesterParam.USERNAME) {
          this.personResultData = this.allPersonResultData.filter(item => item.USERNAME.includes(this.searchTesterParam.USERNAME))
        }
        if (!this.searchTesterParam.ID && !this.searchTesterParam.USERNAME) {
          this.personResultData = this.allPersonResultData
        }
        this.testerSpinning = false
      },
      // 选中数据
      onSelectChange(selectedRowKeys, selectedRows) {
        this.selectedRows = selectedRows
        this.selectedRowKeys = selectedRowKeys
      },
      personOnSelect(selectedRowKeys, selectedRows) {
        this.personSelectedRows = selectedRows
        this.personSelectedRowKeys = selectedRowKeys

        // 如果选择多条
        this.isHaveLeader = this.personSelectedRows.length > 1 ? true : false

        // 如果是可多选，需要处理负责人
        if (this.tableType === 'checkbox') {
          this.personSelectedOptions = []
          for (let i = 0; i < this.personSelectedRows.length; i++) {
            this.personSelectedOptions.push({
              value: this.personSelectedRows[i].ID,
              label: this.personSelectedRows[i].USERNAME
            })
          }
        }

      },
      handleStep(index) {

        // 待办选择，点击下一步
        // 判断是否勾选数据
        if (this.current === 0 && index > 0) {
          if (this.selectedRows.length === 0) return this.$message.warning('请先选择待办')
          // 表格类型
          this.tableType = ['jmcs', 'aqcs'].includes(this.selectedRows[0].taskType) && (this.laboratoryId === 'HZ_YJ_DL_JM' || this.laboratoryId === 'HZ_YJ_DL_AQ') ? 'checkbox' : 'radio'
        }

        this.current = this.current + index

      },
      handleTransfer() {
        // 校验
        if (this.personSelectedRows.length === 0) {
          return this.$message.warning('请选择测试员!')
        } else if (this.personSelectedRows.length > 1 && this.tester === null) {
          return this.$message.warning('请选择责任人!')
        }

        const promiseAll = []
        // 转办
        for (let i = 0; i < this.selectedRows.length; i++) {

          // 日历寿命
          if (['rlsmcs_first', 'rlsmcs_middle', 'rlsmcs_last'].includes(this.selectedRows[i].taskType)) {
            const params = {
              id: this.selectedRows[i].id,
              progressDetailId: this.selectedRows[i].ordTaskId,
              person: {
                userId: this.personSelectedRows[0].ID,
                userName: this.personSelectedRows[0].USERNAME
              }
            }
            promiseAll[i] = executeAlterYfTester(params)
          }

          // 安全、精密
          if (['jmcs', 'aqcs'].includes(this.selectedRows[i].taskType)) {
            let person = {
              opinion: "更改测试人",
              userId: this.personSelectedRows[0].ID,
              userName: this.personSelectedRows[0].USERNAME,
            }

            // 如果不止选择一个测试员，需要进行处理
            if (this.personSelectedRows.length > 1) {
              let participatorArr = []
              let participatorCodeArr = []
              for (let i = 0; i < this.personSelectedRows.length; i++) {
                if (this.personSelectedRows[i].ID === this.tester) { // 负责人
                  person.userName = this.personSelectedRows[i].USERNAME
                  person.userId = this.personSelectedRows[i].ID
                } else { // 参与人数组
                  participatorArr.push(this.personSelectedRows[i].USERNAME)
                  participatorCodeArr.push(this.personSelectedRows[i].ID)
                }
              }
              person.participator = participatorArr.join(',')
              person.participatorCode = participatorCodeArr.join(',')
            }


            promiseAll[i] = executeAlterTesterOrPlanTime({ ordTaskId: this.selectedRows[i].ordTaskId, person })
          }
        }


        // 执行
        Promise.all(promiseAll).then((values) => {
          this.$message.success('转办成功')
          this.$emit("cancel")
        }).catch((error) => {
          console.log(error)
          this.$message.success('转办失败')
        });

      },
      handleCancel() {
        this.$emit("cancel")
      },

    }
  }
</script>
<style lang="less" scoped>
  .operate-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;

    margin-top: 16px;
  }

  .operate-wrapper .operate-row-left {
    display: flex;
  }

  .operate-wrapper .operate-row {
    display: flex;
    align-items: center;
    margin-right: 16px;
  }

  .operate-wrapper .operate-row .label {
    margin-right: 8px;
  }

  .table-chil-wrapper {
    margin-top: 16px;
  }

  /deep/.table-chil-wrapper .ant-table-thead tr th {
    background: #fafafa !important;
  }

  /deep/.table-chil-wrapper .ant-table-body {
    margin: 0;
    overflow-y: scroll;
  }
  /* 笔记本 */
  @media screen and (min-width:900px) and (max-width:1300px) {
    /deep/.table-chil-wrapper .ant-table-body {
      height: 250px;
    }
  }

  /* 大屏 */
  @media screen and (min-width:1300px) and (max-width:3000px) {
    /deep/.table-chil-wrapper .ant-table-body {
      height: 350px;
    }
  }

  /deep/.table-chil-wrapper .ant-table-placeholder {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  /deep/.ant-modal-footer {
    padding: 0 24px 24px;
  }
</style>