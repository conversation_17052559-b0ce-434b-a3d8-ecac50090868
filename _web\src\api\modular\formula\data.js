import request from './request';

/**
 * 上传数据文件
 * @param {FormData} formData - 包含文件的FormData对象
 * @returns {Promise} axios响应对象
 */
export const uploadFile = (formData) => {
  return request.post('/api/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 拟合数据
 * @param {Object} params - 拟合参数
 * @returns {Promise} axios响应对象
 */
export const fitData = (params) => {
  console.log('发送拟合请求, 参数:', JSON.stringify(params));
  return request.post('/api/fit', params);
}

/**
 * 计算容量
 * @param {Object} params - 计算参数
 * @returns {Promise} axios响应对象
 */
export const calculateCapacity = (params) => {
  console.log('发送容量计算请求, 参数:', JSON.stringify(params));

  // 验证params对象的结构
  if (!params.latex_str) {
    console.error('错误: 缺少latex_str参数');
    return Promise.reject(new Error('缺少latex_str参数'));
  }

  if (!params.params || !Array.isArray(params.params)) {
    console.error('错误: 缺少params数组参数或格式不正确');
    return Promise.reject(new Error('缺少params数组参数或格式不正确'));
  }

  // 确保所有参数都有必要字段
  const missingFields = [];
  params.params = params.params.map((param, index) => {
    // 创建一个新对象，避免修改原始对象
    const newParam = { ...param };

    // 检查必要字段
    if (!newParam.name) missingFields.push(`参数${index+1}缺少name字段`);
    if (!newParam.type) missingFields.push(`参数${index+1}缺少type字段`);
    if (newParam.value === undefined) missingFields.push(`参数${index+1}缺少value字段`);

    // 确保describe字段存在，如果不存在则设置为空字符串
    if (newParam.describe === undefined || newParam.describe === null) {
      newParam.describe = '';
    }

    return newParam;
  });

  // 只检查name、type和value字段是否缺失
  if (missingFields.length > 0) {
    console.error('参数验证错误:', missingFields);
    return Promise.reject(new Error(`参数格式不正确: ${missingFields.join(', ')}`));
  }

  // 检查是否所有参数都有describe字段
  const paramsWithoutDescribe = params.params.filter(p => p.describe === undefined || p.describe === null);
  if (paramsWithoutDescribe.length > 0) {
    console.error('警告: 仍然存在缺少describe字段的参数:', paramsWithoutDescribe);
  }

  if (!params.conditions || !Array.isArray(params.conditions)) {
    console.error('错误: 缺少conditions数组参数或格式不正确');
    return Promise.reject(new Error('缺少conditions数组参数或格式不正确'));
  }

  // 确保所有条件都是正确的格式
  const invalidConditions = params.conditions.filter(
    c => !Array.isArray(c) || c.length !== 3 ||
    c.some(val => val === undefined || val === null || isNaN(val))
  );

  if (invalidConditions.length > 0) {
    console.error('错误: 存在格式不正确的conditions:', invalidConditions);
    return Promise.reject(new Error(`存在${invalidConditions.length}个格式不正确的conditions`));
  }

  return request.post('/api/capacity-calculate', params);
}

/**
 * 预测容量
 * @param {Object} params - 预测参数
 * @returns {Promise} axios响应对象
 */
export const predictCapacity = (params) => {
  console.log('发送预测请求, 参数:', JSON.stringify(params));
  return request.post('/api/predict', params);
}