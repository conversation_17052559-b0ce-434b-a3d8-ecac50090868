<template>
  <transition name="showHeader">
    <div v-if="visible" class="header-animat">
      <a-layout-header
        v-if="visible"
        :class="[fixedHeader && 'ant-header-fixedHeader', sidebarOpened ? 'ant-header-side-opened' : 'ant-header-side-closed', ]"
        :style="{ padding: '0', height: '40px' }">
        <div v-if="mode === 'sidemenu'" class="header">
          <a-menu
            style="height: 40px; border-bottom: 0px;"
            mode="horizontal"
            :default-selected-keys="this.defApp"
          >
            <!-- <a-icon v-if="device==='mobile'" class="trigger" :type="collapsed ? 'menu-fold' : 'menu-unfold'" @click="toggle"/>
            <a-icon v-else class="trigger" :type="collapsed ? 'menu-unfold' : 'menu-fold'" @click="toggle" style="padding-left: 20px; padding-right: 20px;"/>
             -->
            
            <router-link :to="{name:'Console'}" style="top:0px; line-height: 40px; padding-left: 20px; padding-right: 10px;display: inline-block;">
              <img src="~@/assets/logo-basic.png"  alt="logo" style="margin-top:-14px;height:21px;">
              <span style="margin-left:2px;color:rgba(0,0,0,.65);font-size: 20px;font-weight: 500;margin-top:-7px;"> PBI测试版</span>
            </router-link>
            
            <template v-for="(item) in userInfo.apps">
              <a-menu-item :key="item.code" v-if="item.code != 'productChartManage'" style="top:0px; line-height: 40px; padding-left: 10px; padding-right: 10px" @click="switchApp(item.code)">
                {{ item.name }}
              </a-menu-item>
            </template>

            <a-menu-item v-for="(item) in blankMenus" :key="item.id" style="top:0px; line-height: 40px; padding-left: 10px; padding-right: 10px">
              <a target="_blank" :href="item.path">{{item.meta.title}}</a>
            </a-menu-item>

            
            
            <user-menu @switchApp="switchApp"></user-menu>
          </a-menu>

        </div>
        <!-- <div v-else :class="['top-nav-header-index', theme]">

          <div class="header-index-wide">
            <div class="header-index-left">
              <logo class="top-nav-header" :show-title="device !== 'mobile'"/>
              <s-menu v-if="device !== 'mobile'" mode="horizontal" :menu="menus" :theme="theme" />
              <a-icon v-else class="trigger" :type="collapsed ? 'menu-fold' : 'menu-unfold'" @click="toggle" />
            </div>
            <user-menu class="header-index-right"></user-menu>
          </div>
        </div> -->
      </a-layout-header>
    </div>
  </transition>
</template>

<script>
import UserMenu from '../tools/UserMenu'
import SMenu from '../Menu/'
//import Logo from '../tools/Logo'
import Logo from '@/components/tools/Logo'
import { mixin } from '@/utils/mixin'
import { mapActions, mapGetters } from 'vuex'
import { ALL_APPS_MENU } from '@/store/mutation-types'
import Vue from 'vue'
import { message } from 'ant-design-vue/es'

export default {
  name: 'GlobalHeader',
  components: {
    UserMenu,
    SMenu,
    Logo
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  created () {
    let apps = Vue.ls.get(ALL_APPS_MENU)
    let app = apps.find(e=>e.active == true)
    if(app){
      this.defApp.push(app.code)
    }
  },
  mixins: [mixin],
  props: {
    mode: {
      type: String,
      // sidemenu, topmenu
      default: 'topmenu'
    },
    menus: {
      type: Array,
      required: true
    },
    blankMenus: {
      type: Array,
      required: true
    },
    theme: {
      type: String,
      required: false,
      default: 'light'
    },
    collapsed: {
      type: Boolean,
      required: false,
      default: false
    },
    device: {
      type: String,
      required: false,
      default: 'desktop'
    }
  },
  data () {
    return {
      visible: true,
      oldScrollTop: 0,
      defApp: [],
    }
  },
  mounted () {
    document.addEventListener('scroll', this.handleScroll, { passive: true })
  },
  methods: {
    ...mapActions(['MenuChange']),

    /**
     * 应用切换
     */
    switchApp (appCode,sourceMenu) {
      //this.defApp = []
      //this.$emit('togglefalse')
      //const applicationData = this.userInfo.apps.filter(item => item.code === appCode)
      //const hideMessage = message.loading('正在切换应用！', 0)
      /* this.MenuChange(applicationData[0]).then((res) => {
          hideMessage()
          this.setShowNav()
      // eslint-disable-next-line handle-callback-err
      }).catch((err) => {
        message.error('应用切换异常')
      }) */
      const apps = Vue.ls.get(ALL_APPS_MENU)
      let menu = null
      //const _newApps = []
      for (let item of apps) {
        if (item.code == appCode) {
          item.active = true
          if(sourceMenu){
            menu = sourceMenu
          }else{
            menu = item.menu.find((item) => item.component != 'PageView' && item.meta.showNav == 1  && item.meta.show == true) //&& item.meta.showSideNav == 1
          }

        }else{
          item.active = false
        }
        //_newApps.push(item)
      }
      
      Vue.ls.set(ALL_APPS_MENU, apps)
      //hideMessage()
      this.setShowNav(menu)
      
    },
    handleScroll () {
      if (!this.autoHideHeader) {
        return
      }

      const scrollTop = document.body.scrollTop + document.documentElement.scrollTop
      if (!this.ticking) {
        this.ticking = true
        requestAnimationFrame(() => {
          if (this.oldScrollTop > scrollTop) {
            this.visible = true
          } else if (scrollTop > 300 && this.visible) {
            this.visible = false
          } else if (scrollTop < 300 && !this.visible) {
            this.visible = true
          }
          this.oldScrollTop = scrollTop
          this.ticking = false
        })
      }
    },
    toggle () {
      this.$emit('toggle')
    },
    setShowNav (menu) {
      this.$emit('setShowNav',menu.meta.showSideNav)
      this.$router.push(menu ? menu.path :'/')
    },
  },
  beforeDestroy () {
    document.body.removeEventListener('scroll', this.handleScroll, true)
  }
}
</script>

<style lang="less">
@import '../index.less';

.header-animat{
  position: relative;
  z-index: @ant-global-header-zindex;
}
.showHeader-enter-active {
  transition: all 0.25s ease;
}
.showHeader-leave-active {
  transition: all 0.5s ease;
}
.showHeader-enter, .showHeader-leave-to {
  opacity: 0;
}
</style>

<style lang="less" scoped=''>
/deep/.ant-menu-horizontal > .ant-menu-item:first-child:hover, 
/deep/.ant-menu-horizontal > .ant-menu-submenu:first-child:hover,
 /deep/.ant-menu-horizontal > .ant-menu-item-active:first-child, 
 /deep/.ant-menu-horizontal > .ant-menu-submenu-active:first-child, 
 /deep/.ant-menu-horizontal > .ant-menu-item-open:first-child, 
 /deep/.ant-menu-horizontal > .ant-menu-submenu-open:first-child, 
 /deep/.ant-menu-horizontal > .ant-menu-item-selected:first-child,
  /deep/.ant-menu-horizontal > .ant-menu-submenu-selected:first-child{
    border-bottom: 2px solid #fff;
}
</style>
