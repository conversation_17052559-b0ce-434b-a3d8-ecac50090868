<template>
  <div class="user-wrapper" style="display: flex;align-items: center">
    <div class="content-box">

      <a-icon class="action" type="search" @click="searchMenu" v-if="!show" style=""/>
      <a-input-search id="myInput" v-model="queryParam" style="width: 220px;display: inline-block;margin-right: 5px" v-show="show" allow-clear @change="searchMenu" @blur="closeMenu" @keyup.enter="gotoNextView" @search="searchMenu" @mouseover="showTree = true">
      </a-input-search>
      <a-menu v-show="show && showTree"
              id="searchMenu"
              :open-keys="openKeys"
              @openChange="onOpenChange"
              style="width: 220px;position: fixed;border: 1px solid #d9d9d9;border-radius: 5px;"
              :inlineCollapsed="false"
              mode="inline"
              @mouseover="inMenu = true"
              @mouseleave="menuLeave"
      >
        <template v-for="(item) in apps">


          <a-sub-menu :title="item.name" :key="item.code" style="line-height: 24px;">
            <template v-for="(menu) in item.menus">

              <a-menu-item v-if="menu.children == null || menu.children.length == 0" :key="menu.name"  @click="pushToOtherMenu(item.code,menu)" style="height: 24px;line-height: 24px;">
                <a-icon v-if="menu.meta.icon != null" :type="menu.meta.icon"/>
                {{ menu.meta.title }}
              </a-menu-item>
              <a-sub-menu v-else :key="menu.name" :title="menu.meta.title" style="line-height: 24px;">
                <template v-for="(subMenu) in menu.children">
                  <a-menu-item :key="subMenu.name" v-if="subMenu.children == null || subMenu.children.length == 0" @click="pushToOtherMenu(item.code,subMenu)" style="height: 24px;line-height: 24px;">
                    <a-icon v-if="subMenu.meta.icon != null" :type="subMenu.meta.icon"/>
                    {{ subMenu.meta.title }}
                  </a-menu-item>

                  <a-sub-menu v-else :key="subMenu.name" :title="subMenu.meta.title" style="line-height: 24px;">
                    <template v-for="(thirdMenu) in subMenu.children">
                      <a-menu-item :key="thirdMenu.name"  @click="pushToOtherMenu(thirdMenu.code,thirdMenu)" style="height: 24px;line-height: 24px;">
                        <a-icon v-if="thirdMenu.meta.icon != null" :type="thirdMenu.meta.icon"/>
                        {{ thirdMenu.meta.title }}
                      </a-menu-item>
                    </template>


                  </a-sub-menu>

                </template>
              </a-sub-menu>
            </template>
          </a-sub-menu>
        </template>
      </a-menu>


      <span class="action" @click="toggleFullscreen" v-if="!show">
        <a-icon type="fullscreen-exit" v-if="isFullscreen"/>
        <a-icon type="fullscreen" v-else/>
      </span>
      <!-- <notice-icon class="action"/> -->
      <a-dropdown :visible="dropdownVisible" v-if="!show">
        <span class="action ant-dropdown-link user-dropdown-menu"  @click="dropdownVisible = !dropdownVisible">
          <!-- <a-avatar class="avatar" size="small" :src="avatar"/> -->
          <a-avatar size="small"  :src="userInfo.avatarUrl" class="avatar"></a-avatar><span>{{ nickname }}</span>
<!--          <span>{{ nickname }}</span>-->
        </span>
        <a-menu slot="overlay" class="user-dropdown-menu-wrapper" @mouseleave="dropdownVisible = !dropdownVisible">
          <!-- <a-menu-item key="4" v-if="mode === 'sidemenu'">
            <a @click="appToggled()" >
              <a-icon type="swap"/>
              <span>切换应用</span>
            </a>
          </a-menu-item> -->
          <!-- <a-menu-item key="0">
            <router-link :to="{ name: 'center' }">
              <a-icon type="user"/>
              <span>个人中心</span>
            </router-link>
          </a-menu-item> -->
<!--          <a-menu-item key="1">
            <router-link :to="{ name: 'settings' }">
              <a-icon type="setting"/>
              <span>账户设置</span>
            </router-link>
          </a-menu-item>-->
          <a-menu-divider/>
          <a-menu-item key="3">
            <a href="javascript:;" @click="handleLogout">
              <a-icon type="logout"/>
              <span>退出登录</span>
            </a>
          </a-menu-item>
        </a-menu>
      </a-dropdown>
    </div>
    <a-modal
      title="切换应用"
      :visible="visible"
      :footer="null"
      :confirm-loading="confirmLoading"
      @cancel="handleCancel"
    >
      <a-form :form="form1" >
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="选择应用"
        >
          <a-menu
            mode="inline"
            :default-selected-keys="this.defApp"
            style="border-bottom:0px;lineHeight:40px;"
          >
            <a-menu-item v-for="(item) in userInfo.apps" :key="item.code" style="top:0px;" @click="pushToOtherMenu(item.code,item.menu[0])">
              {{ item.name }}
            </a-menu-item>
          </a-menu>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import screenfull from 'screenfull'
import NoticeIcon from '@/components/NoticeIcon'
import {mapActions, mapGetters, mapState} from 'vuex'
import { ALL_APPS_MENU } from '@/store/mutation-types'
import Vue from 'vue'
import { message } from 'ant-design-vue/es'
import { convertRoutes } from "@/utils/routeConvert"

export default {
  name: 'UserMenu',
  components: {
    NoticeIcon,
    screenfull
  },
  props: {
    mode: {
      type: String,
      // sidemenu, topmenu
      default: 'sidemenu'
    }
  },

  data () {
    return {
      inMenu:false,
      dropdownVisible:false,
      openKeys:[],
      queryParam:null,
      apps:[],
      showTree:false,
      show:false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      visible: false,
      confirmLoading: false,
      form1: this.$form.createForm(this),
      defApp: [],
      isFullscreen: false
    }
  },

  computed: {
    ...mapGetters(['nickname', 'avatar', 'userInfo',]),
    ...mapState({
      // 动态主路由
      mainMenu: state => state.permission.addRouters
    })
  },
  methods: {
    ...mapActions(['Logout', 'MenuChange']),
    menuLeave(){

      this.inMenu = false
      this.showTree = false
      setTimeout(() => {
        document.getElementById('myInput').focus();
      }, 500)
    },
    closeMenu(){
      if(!this.inMenu){
        this.show = false

      }
    },
    onOpenChange(openKeys) {
      this.openKeys = openKeys;
    },
    gotoNextView(){
      let memu = []
      this.apps.forEach(a => {
        //菜单
        a.menus.forEach(m => {
          //二级菜单
          if(m.children){
            m.children.forEach(ch => {
              //三级菜单
              if(ch.children){
                ch.children.forEach(child => {
                  memu.push(child)
                })
              }else{
                memu.push(ch)
              }
            })
          }else{
            memu.push(m)
          }

        })
      })
      if(memu.length == 1){
        this.$emit('switchApp',memu[0].meta.code)
        this.$router.push(memu[0].path)
      }

    },



    pushToOtherMenu(code,menu){
      this.$emit('switchApp',code)
      this.$router.push(menu.path)
    },
    handleLogout () {
      this.$confirm({
        title: '提示',
        content: '真的要注销登录吗 ?',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          return this.Logout({}).then(() => {
            setTimeout(() => {
              window.location.reload()
            }, 16)
          }).catch(err => {
            this.$message.error({
              title: '错误',
              description: err.message
            })
          })
        },
        onCancel () {
        }
      })
    },
    searchMenu(){
      this.show = true
      const routes = convertRoutes(this.mainMenu.find(item => item.path === "/"))

      this.apps = []
      let cloneApps = JSON.parse(JSON.stringify(this.userInfo.apps)) ;
      cloneApps = cloneApps.filter(c => c.name != '文件传递平台' && c.name != '产品信息对齐表')
      let openKeys = []
      for (let i = 0; i < cloneApps.length; i++) {
        const app = cloneApps[i]
        app.menus = (routes && routes.children.filter(item => item.meta.code == app.code)) || []
        app.menus = app.menus.filter(m =>  m.hidden != true )
        for (let j = 0; j < app.menus.length; j++) {
          if(app.menus[j].children){
            app.menus[j].children = app.menus[j].children.filter(m =>  m.hidden != true )
          }
        }
        if(this.queryParam != null && this.queryParam != ''){
          //一级
          if(app.name.indexOf(this.queryParam) > -1){
            openKeys.push(app.code)
            this.apps.push(app)
          }else{
            //二级
            let menus = app.menus
            if(menus.length > 0){
              let secondMenus = []
              for (let j = 0; j < app.menus.length; j++) {
                //匹配上二级菜单
                if(app.menus[j].meta.title.indexOf(this.queryParam) > -1){
                  openKeys.push(app.code)
                  openKeys.push(app.menus[j].name)
                  secondMenus.push(app.menus[j])
                }else{
                  //匹配三级菜单
                  if(app.menus[j].children){
                    let inFilterMenus = app.menus[j].children.filter(m => m.meta.title.indexOf(this.queryParam) > -1)
                    if(inFilterMenus.length > 0){
                      app.menus[j].children = inFilterMenus
                      openKeys.push(app.code)
                      openKeys.push(app.menus[j].name)
                      secondMenus.push(app.menus[j])

                    }else{
                      //匹配四级菜单
                      app.menus[j].children.forEach(childChild => {
                          if(childChild.children){
                            let inInFilterMenus = childChild.children.filter(m => m.meta.title.indexOf(this.queryParam) > -1)
                            if(inInFilterMenus.length > 0){
                              // console.log(inInFilterMenus)
                              childChild.children = inInFilterMenus

                              app.menus[j].children = []
                              app.menus[j].children.push(childChild)
                              // console.log(childChild)
                              openKeys.push(app.code)
                              openKeys.push(app.menus[j].name)
                              openKeys.push(childChild.name)
                              // openKeys.push(childChild.name)
                              secondMenus.push(app.menus[j])
                            }
                          }


                      })
                    }
                  }
                }
              }

              if(secondMenus.length > 0){
                app.menus = secondMenus
                this.apps.push(app)
              }

            }
          }
        }else{
          this.apps.push(app)
        }

      }

      this.showTree = true
      this.openKeys = openKeys
      setTimeout(() => {
        document.getElementById('myInput').focus();
      }, 500)

    },

    /**
     * 打开切换应用框
     */
    appToggled () {
      this.visible = true
      this.defApp.push(Vue.ls.get(ALL_APPS_MENU)[0].code)
    },

    switchApp (appCode) {
      this.visible = false
      this.defApp = []
      const applicationData = this.userInfo.apps.filter(item => item.code === appCode)
      const hideMessage = message.loading('正在切换应用！', 0)
      this.MenuChange(applicationData[0]).then((res) => {
        hideMessage()
      // eslint-disable-next-line handle-callback-err
      }).catch((err) => {
        message.error('应用切换异常')
      })
    },
    handleCancel () {
      this.form1.resetFields()
      this.visible = false
    },
    /* 全屏切换 */
    toggleFullscreen () {
      if (!screenfull.isEnabled) {
        message.error('您的浏览器不支持全屏模式')
        return
      }
      screenfull.toggle()
      if (screenfull.isFullscreen) {
        this.isFullscreen = false
      } else {
        this.isFullscreen = true
      }
    }
  }
}
</script>

<style lang="less" scoped>
    .appRedio {
    border:1px solid #91d5ff;
    padding:10px 20px;
    background: #e6f7ff;
    border-radius:2px;
    margin-bottom:10px;
      color: #91d5ff;
    /*display: inline;
    margin-bottom:10px;*/
    }

    /deep/ #searchMenu .ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title {
      height: 24px;
      line-height: 24px;
    }

    /deep/ #searchMenu.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title {
      height: 24px;
      line-height: 24px;
    }


</style>
