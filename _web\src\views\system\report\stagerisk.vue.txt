<template>
  <div>
    <a-breadcrumb class="breadcrumb" separator=">" :style="`width:${_elWidth}px;margin:0 auto;`">
      <a-breadcrumb-item><a @click="gotoIndex(-3)">信息对齐表</a></a-breadcrumb-item>
      <a-breadcrumb-item><a @click="gotoIndex(-2)">产品开发进展</a></a-breadcrumb-item>
      <a-breadcrumb-item><a @click="gotoIndex(-1)">质量指标</a></a-breadcrumb-item>
      <a-breadcrumb-item>阶段风险关闭率</a-breadcrumb-item>
    </a-breadcrumb>
    <vxe-table :height="windowHeight" :loading="loading" show-footer border align="center" :data="list" :footer-span-method="footerColspanMethod" :footer-method="footerMethod" :span-method="mergeRowMethod">
    <vxe-column type="seq" width="60" title="序号"></vxe-column>
    <vxe-column width="90" field="productStage" title="产品阶段">
      <template #default="{ row }">
              <span>{{ 'product_stage_status' | dictType(row.productStage) }}</span>
      </template>
        </vxe-column>
        <vxe-column width="90" field="productCustomer" title="客户"></vxe-column>
        <vxe-column width="90" field="riskCategory" title="风险类别">
          <template #default="{ row }">
            <span>{{ 'stage_risk_category' | dictType(row.riskCategory) }}</span>
          </template>
        </vxe-column>
        <vxe-column width="180" field="riskEvent" title="风险事件">
          <template #default="{ row }">
            <clamp :text="row.riskEvent" :maxLines=3 :sourceText="[row.riskEvent]"></clamp>
          </template>
        </vxe-column>
        <vxe-column field="riskReason" width="180" title="风险发生的原因">
          <template #default="{ row }">
            <clamp :text="row.riskReason" :maxLines=3 :sourceText="[row.riskReason]"></clamp>
          </template>
        </vxe-column>
        <vxe-column field="riskResult" width="180" title="风险可能导致的结果">
          <template #default="{ row }">
            <clamp :text="row.riskResult" :maxLines=3 :sourceText="[row.riskResult]"></clamp>
          </template>
        </vxe-column>
        <vxe-column field="riskLevel" title="风险等级">
          <template #default="{ row }">
            <span>{{ 'stage_risk_level' | dictType(row.riskLevel) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="riskMeasures" width="180" title="风险管理措施">
          <template #default="{ row }">
            <clamp :text="row.riskMeasures" :maxLines=3 :sourceText="[row.riskMeasures]"></clamp>
          </template>
        </vxe-column>
        <vxe-column width="90" field="responsiblePersonName" title="责任人"></vxe-column>
        <vxe-column field="plannedCompletionDate" width="90" title="计划完成时间"></vxe-column>
        <vxe-column field="actualCompletionDate" width="90" title="实际完成时间"></vxe-column>
        <vxe-column width="90" field="problemStatus" title="状态">
          <template #default="{ row }">
            <span>{{ 'stage_problem_status' | dictType(row.problemStatus) }}</span>
          </template>
        </vxe-column>
        <vxe-column width="90" field="confirmedPersonName" title="确认人"></vxe-column>
    </vxe-table>
  </div>
  
</template>

<script>
import { clamp } from '@/components'
import {
    getStageRisk
  } from "@/api/modular/system/report"
  export default {
    components: {
        clamp
    },
    data() {
      return {
        windowHeight: document.documentElement.clientHeight - 35,
        list: [],
        loading: false,
      }
    },
    methods: {
      gotoIndex(index){
      this.$router.go(index)
    },
      footerMethod({
        columns
      }) {
        let a = this.list.filter(e => e.problemStatus == 3).length
        let c = !a ? 0 : parseFloat((a / this.list.length) * 100).toFixed(2)
        return [
          columns.map((column, _columnIndex) => {
            if (_columnIndex == 0) {
              return '已识别风险总数'
            }

            if (_columnIndex == 4) {
              return this.list.length
            }
            
            if (_columnIndex == 6) {
              return '风险关闭个数'
            }

            if (_columnIndex === 8) {
              return a
            }
            if (_columnIndex == 10) {
              return '风险关闭率'
            }

            if (_columnIndex === 12) {
              return c + '%'
            }
            return ''
          }),
        ]
      },
      mergeRowMethod({
        row,
        _rowIndex,
        column,
        visibleData
      }) {
        const fields = ['productStage']
        const cellValue = row[column.property]
        if (cellValue && fields.includes(column.property)) {
          const prevRow = visibleData[_rowIndex - 1]
          let nextRow = visibleData[_rowIndex + 1]
          if (prevRow && prevRow[column.property] === cellValue) {
            return {
              rowspan: 0,
              colspan: 0
            }
          } else {
            let countRowspan = 1
            while (nextRow && nextRow[column.property] === cellValue) {
              nextRow = visibleData[++countRowspan + _rowIndex]
            }
            if (countRowspan > 1) {
              return {
                rowspan: countRowspan,
                colspan: 1
              }
            }
          }
        }
      },
      footerColspanMethod({
        $rowIndex,
        _columnIndex
      }) {
        if ($rowIndex === 0) {
          if (_columnIndex === 0) {
            return {
              rowspan: 1,
              colspan: 4
            }
          }
          if (_columnIndex === 4) {
            return {
              rowspan: 1,
              colspan: 2
            }
          }
          if (_columnIndex === 6) {
            return {
              rowspan: 1,
              colspan: 2
            }
          }
          if (_columnIndex === 8) {
            return {
              rowspan: 1,
              colspan: 2
            }
          }
          if (_columnIndex === 10) {
            return {
              rowspan: 1,
              colspan: 2
            }
          }
          if (_columnIndex === 12) {
            return {
              rowspan: 1,
              colspan: 2
            }
          }
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      },
      callStageRisk() {
        this.loading = true
        getStageRisk({
            stage: this.$route.query.stage,
            issueId: this.$route.query.issueId
          })
          .then((res) => {
            if (res.result) {
              this.list = res.data
            } else {
              this.$message.error(res.message, 1);
            }
            this.loading = false
          })
          .catch((err) => {
            this.loading = false
            this.$message.error('错误提示：' + err.message, 1)
          });
      },
    },
    created(){
      this.callStageRisk()
    }
  }
</script>

<style lang="less">
  @import './vetable.less';
  .breadcrumb{
  padding: 5px 0;
  padding-left: 13px;
}.ant-breadcrumb a{
  color:#5d90fa !important;
}.ant-breadcrumb{
  font-size: 12px !important;
}
</style>