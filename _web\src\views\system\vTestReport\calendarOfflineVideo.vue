<template>
  <div>
    <div class="right-top-div">
      <a-button v-if="!editFlag && hasPerm('testProjectTodoTask:getBatteryInfoById')" style="float:right;margin-right: 8px;font-size: 12px;border-radius: 4px;" size="small" type="primary" @click="handleEdit(true)">启动编辑</a-button>
      <a-button v-if="editFlag && hasPerm('testProjectTodoTask:getBatteryInfoById')" style="float:right;margin-right: 8px;font-size: 12px;border-radius: 4px;" size="small" type="primary" @click="handleEdit(false)">关闭编辑</a-button>
      <a-button style="float:right;margin-right: 20px;font-size: 12px;border-radius: 4px;" size="small" type="primary" @click="exportVideo">导出视频</a-button>
    </div>
    <div class="all-wrapper">
      <div style="border-radius: 0 10px 10px 10px;height: 800px;background-color: white;">
        <div style="width:750px;float:left;">
          <a-spin :spinning="videoLoading">
            <p style="position: absolute;margin-top: 15px;margin-left:30px;z-index: 2;color: black;font-size: 13px;">提示：当视频无法在线播放时，需先将视频下载到本地设备，之后再进行播放。</p>
            <div v-if="newestVideoFlag && editFlag" style="float: left;height: 30px;width: 150px;margin:40px 0px 2px 30px;">
              <a-button style="font-size: 12px;border-radius: 4px;z-index: 2;" size="small" type="primary" @click="() => (chooseCodeVisible = true, selectedRowCodeKeys = [])">上传</a-button>
            </div>
          <a-table style="padding: 40px 0px 0px 30px"
                   :customRow="customRow"
                   :columns="videoColumns"
                   :rowKey="record => record.id"
                   :row-selection="{
                    selectedRowKeys: selectedRowKeys, selectedRows: selectedRows,
                    onChange: onSelectChange, columnWidth:10}"
                   :data-source="videoDataList"
                   :pagination="paginationConfig"
                   :loading="videoTableLoading"
                   bordered>
                  <template slot="videoControl" slot-scope="text, record, index, columns">
                    <a-icon v-if="record.videoId && !editFlag" @click="playVideo(record)" style="color: #0d84ff;fontSize: 25px;" type="caret-right"/>

                    <a-upload
                      name="file"
                      v-if="!record.videoId && editFlag"
                      :headers="headers"
                      :customRequest="handleUpload"
                      :data="uploadData"
                      :action="picOrVidPostUrl"
                      :before-upload="beforeUploadVideo"
                      :multiple="false"
                      :showUploadList="false"
                      @change="uploadVideo($event,record)"
                      accept=".mp4,.avi,.wmv,.mov">
                        <a style="color: #0d84ff;fontSize: 12px;">上传</a>
                    </a-upload>
                    <a-popconfirm
                      placement="topRight"
                      ok-text="删除"
                      cancel-text="取消"
                      @confirm="deleteVideo(record)">
                      <template slot="title"> 确认删除视频"{{ record.videoName }}"吗 </template>
                      <a v-if="record.videoId && editFlag" style="color: #0d84ff;fontSize: 12px;">删除</a>
                    </a-popconfirm>
                    <a-upload
                      v-if="record.videoId && editFlag"
                      name="file"
                      :headers="headers"
                      :data="uploadData"
                      :action="picOrVidPostUrl"
                      :customRequest="handleUpload"
                      :before-upload="beforeUploadVideo"
                      :multiple="false"
                      :showUploadList="false"
                      @change="uploadVideo($event,record)"
                      accept=".mp4,.avi,.wmv,.mov">
                      <a style="color: #0d84ff;fontSize: 12px;margin-left: 8px;">替换</a>
                    </a-upload>
                  </template>
                  <template slot="newestVideoControl" slot-scope="text, record, index, columns">
                    <a-icon v-if="record.videoId && !editFlag" @click="playVideo(record)" style="color: #0d84ff;fontSize: 25px;" type="caret-right"/>
                    <a-popconfirm
                      placement="topRight"
                      ok-text="删除"
                      cancel-text="取消"
                      @confirm="deleteVideo(record)">
                      <template slot="title"> 确认删除视频"{{ record.videoName }}"吗 </template>
                      <a v-if="record.videoId && editFlag" style="color: #0d84ff;fontSize: 12px;">删除</a>
                    </a-popconfirm>
                  </template>
          </a-table>
          </a-spin>
          <div style="float: left;width: 98%;margin-left: 30px;" v-if="uploadProgress > 0 || uploadProgressShow">
            <a-progress :strokeWidth="12" :percent="uploadProgress"></a-progress>
          </div>
        </div>
        <div class="videoContent" :style="`width:${videoWidth}px;height:${videoHeight}px;`">
          <video width="100%" height="100%" ref="videoPlayer"
                 autoplay
                 controls
                 playsinline>
            <source :src="iframeUrl" type="video/mp4">
          </video>
        </div>
      </div>
    </div>

    <a-modal
      title="测试编码选择"
      width="30%"
      :height="200"
      :bodyStyle="{ padding: 0 }"
      :visible="chooseCodeVisible"
      style="padding: 0"
      :maskClosable="false"
      @cancel="() => (chooseCodeVisible = false)"
    >
      <div class="child-table">
        <a-table
          :columns="chooseCodeColumns"
          :dataSource="testDataList.filter(item => item.batteryStatus === 'ongoing' || item.batteryStatus === 'testDone')"
          class="mt10"
          bordered
          :rowKey="record => record.cellTestCode"
          :rowSelection="{ selectedRowKeys: selectedRowCodeKeys, onChange: onSelectCodeChange }"
          :pagination="false"
        >
        </a-table>
      </div>
      <template slot="footer" slot-scope="text, record">
        <div>
          <a-button :style="{marginRight: selectedRowCodeKeys.length === 0 ? '0px' : '8px'}" @click="() => (chooseCodeVisible = false)">
            取消
          </a-button>
          <a-button v-if="selectedRowCodeKeys.length === 0" type="primary" @click="chooseAtLeastOneCode">确定</a-button>
          <a-upload
            v-else
            name="file"
            :headers="headers"
            :customRequest="handleUpload"
            :data="uploadData"
            :action="picOrVidPostUrl"
            :before-upload="beforeUploadVideo"
            :multiple="false"
            :disabled="videoLoading"
            :showUploadList="false"
            @change="uploadCodeVideo($event)"
            accept=".mp4,.avi,.wmv,.mov">
            <a-spin :spinning="videoLoading">
              <a-button type="primary">确定</a-button>
            </a-spin>
          </a-upload>
        </div>
      </template>
    </a-modal>

  </div>

</template>
<script>
import {STable} from "@/components";
import {Pagination} from 'ant-design-vue';
import {
  get, getVideoBySafetyTestIds, getVideoByTestProgress, updatePicOrVid, updatePicOrVidOfAq
} from "@/api/modular/system/testProgressManager";
import {calendarCommon} from "./mixin/calendarCommon.js";
import { getMinioDownloadUrl, getMinioPreviewUrl } from "@/api/modular/system/fileManage";
import { downloadMinioFile, downloadMinioFileList } from "@/utils/util";
import Vue from "vue";
import { ACCESS_TOKEN } from "@/store/mutation-types";
import axios from "axios";

export default {
  components: {
    STable,
    'a-pagination': Pagination
  },
  mixins: [calendarCommon],
  data: function () {
    return {
      testDataList: [],
      curSafetyTest: null,
      selectedRowCodeKeys: [],
      chooseCodeColumns: [
        {
          title: "序号",
          dataIndex: "index",
          align: "center",
          customRender: (text, record, index) => `${index + 1}`
        },
        {
          title: "测试编码",
          dataIndex: "cellTestCode",
          align: "center",
          scopedSlots: {
            customRender: "cellTestCode"
          }
        }
      ],
      chooseCodeVisible: false,
      data: {},
      iframeUrl: null,
      selectedRowKeys: [],
      selectedRows: [],
      videoWidth: 0,
      videoHeight: 0,
      paginationConfig: {
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '30', '40', '50'], // 显示的每页数量选项
        size: "small",
        showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
      },
      videoDataList: [],
      videoTableLoading: false,
      uploadProgress: 0,
      uploadProgressShow: false,
      videoLoading: false,
      editFlag: false,
      picOrVidPostUrl: "/api/sysFileInfo/minioUpload",
      uploadData: { bucket: 'safetylab' },
      headers: {
        Authorization: "Bearer " + Vue.ls.get(ACCESS_TOKEN)
      },
      newestVideoColumns: [
        {
          title: "样品编号",
          dataIndex: "sampleNo",
          width: 80,
          align: "center",
        },
        {
          title: "绝对时间",
          dataIndex: "absoluteDate",
          width: 140,
          align: "center"
        },
        {
          title: "名称",
          dataIndex: "videoName",
          width: 200,
          align: "center"
        },
        {
          title: "操作",
          width: 100,
          align: "center",
          scopedSlots: { customRender: 'newestVideoControl' },
        }
      ],
      videoColumns: [
        {
          title: "存储阶段",
          dataIndex: "storeStage",
          width: 80,
          align: "center",
        },
        {
          title: "天数",
          dataIndex: "dayRange",
          width: 60,
          align: "center"
        },
        {
          title: "绝对时间",
          dataIndex: "absoluteDate",
          width: 140,
          align: "center"
        },
        {
          title: "名称",
          dataIndex: "videoName",
          width: 200,
          align: "center"
        },
        {
          title: "操作",
          width: 100,
          align: "center",
          scopedSlots: { customRender: 'videoControl' },
        }
      ],
      testProgress: null,
      beforeAfterFlag: false,
      newestVideoFlag: false,
      safetyTestFlag: false,
    }
  },
  mounted() {
    if (this.$route.query.type === '安全测试') {
      this.getVideoDataOfAq()
      this.safetyTestFlag = true
      this.videoColumns = this.videoColumns.filter(obj => obj.title !== '天数').filter(obj => obj.title !== '存储阶段');
    } else {
      this.getByTestProgressId()
    }
  },
  created() {
    this.initBodySize()
  },
  methods: {
    chooseAtLeastOneCode() {
      return this.$message.warning("请至少选择一个电芯")
    },
    onSelectCodeChange(selectedRowKeys, selectedRows) {
      this.selectedRowCodeKeys = selectedRowKeys
    },
    uploadCodeVideo(info) {
      this.chooseCodeVisible = false
      this.videoLoading = true
      if (info.file.status === "done") {
        let file = info.file
        let update = {}
        update.id = this.curSafetyTest.id
        update.safetyTestIdListString = this.curSafetyTest.safetyTestIdListString
        update.videoId = file.response.data
        update.videoName = file.name
        update.cellTestCodes = this.selectedRowCodeKeys.join(',')
        updatePicOrVidOfAq(update, 'add', 'newestVideo', -1).then(res => {
          if (res.success) {
            this.selectedRowCodeKeys = []
            this.getVideoDataOfAq()
            this.$message.success(`${info.file.name} 上传成功`)
          } else {
            this.$message.error("上传失败：" + res.message)
          }
          setTimeout(() => {
            this.videoLoading = false
          },500)
        })
      } else if (info.file.status === "error") {
        this.videoLoading = false
        this.$message.error(`${info.file.name} 上传失败`)
      } else {
        this.videoLoading = false
      }
    },
    playVideo (record) {
      if (record.videoId) {
        getMinioPreviewUrl(record.videoId).then(res => {
          this.iframeUrl = res.data.replace("http://10.100.1.99:9000/", "/minioDownload/")
          const videoPlayer = this.$refs.videoPlayer;
          if (videoPlayer) {
            videoPlayer.load();
          }
        })
      }
    },
    initBodySize() {
      let initWidth = document.documentElement.clientWidth // 拿到父元素宽
      let initHeight = document.documentElement.clientHeight // 拿到父元素高
      if (initWidth <= 1280) {
        this.videoWidth = initWidth * 0.35
        this.videoHeight = initHeight * 0.6
      } else {
        this.videoWidth = initWidth * 0.56
        this.videoHeight = initHeight * 0.65
      }
    },
    //直接调用lims接口预览或下载
    async openFileOrDownload(fileId,fileName) {
      //pbi上传的文件
      if (fileId) {
        await getMinioDownloadUrl(fileId,encodeURIComponent(fileName)).then(res1 => {
            downloadMinioFile(res1.data)
          })
      }
    },
    async exportVideo() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      let urlList = []
      for (let i = 0; i < this.selectedRows.length; i++) {
        let v = this.selectedRows[i]
        if (v.videoId) {
          await getMinioDownloadUrl(v.videoId,encodeURIComponent(v.videoName)).then(res1 => {
            urlList.push(res1.data)
          })
        }
      }
      if (urlList.length > 0) {
        downloadMinioFileList(urlList)
      } else {
        this.$message.warning('当前没有可导出的视频')
      }
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    customRow(record) {
      return {
        props: {},
        on: { // 事件
          click: (event) => {
          },
        },
      };
    },
    getVideoDataOfAq() {
      getVideoBySafetyTestIds({ safetyTestIds: this.$route.query.safetyTestIds}).then(res => {
        if (res.success) {
          this.videoDataList = res.data.videoDataList
          let testType = res.data.testType
          if (testType === 'newest_before_after') {
            this.newestVideoFlag = true
            this.videoColumns = this.newestVideoColumns
            this.curSafetyTest = res.data.curSafetyTest
            this.testDataList = JSON.parse(this.curSafetyTest.testData)
          } else if (testType === 'before_after') {
            this.beforeAfterFlag = true
          } else {
            if (this.videoColumns.findIndex(item => item.dataIndex === "totalCycleTime") === -1) {
              this.videoColumns.unshift({
                title: "累积循环次数",
                dataIndex: "totalCycleTime",
                width: 100,
                align: "center"
              })
            }
          }
          if (this.videoColumns.findIndex(item => item.dataIndex === "stage") === -1 && !this.newestVideoFlag) {
            this.videoColumns.unshift({
              title: "测试阶段",
              dataIndex: "stage",
              width: 90,
              align: "center",
              customRender: (text, record, index) => {
                if (this.beforeAfterFlag) {
                  if (record.stage === "0") {
                    return "测试前"
                  } else {
                    return "测试后"
                  }
                } else {
                  return text
                }
              }
            })
          }
        } else {
          this.$message.error('获取视频数据失败：' + res.message)
        }
      })
    },
    getVideoDataByTestProgress(testProgress) {
      getVideoByTestProgress(testProgress).then(res => {
        if (res.success) {
          this.videoDataList = res.data
        } else {
          this.$message.error('获取视频数据失败：' + res.message)
        }
      })
    },
    getByTestProgressId () {
      if (this.$route.query.testProgressId) {
        get({ id: this.$route.query.testProgressId }).then(res => {
          if (res.success) {
            this.testProgress = res.data
            this.getVideoDataByTestProgress(this.testProgress)
          }
        })
      } else {
        get({ ordtaskid: this.$route.query.id }).then(res => {
          if (res.success) {
            this.testProgress = res.data
            this.getVideoDataByTestProgress(this.testProgress)
          }
        })
      }
    },
    handleEdit(flag) {
      this.editFlag = flag;
      this.videoTableLoading = true;
      setTimeout(() => {
        this.videoTableLoading = false;
      }, 300)
    },
    handleUpload(options) {
      const { file, onSuccess, onError } = options;
      const formData = new FormData();
      formData.append('file', file);
      axios.post('/api/sysFileInfo/minioUpload', formData, {
        headers: {
          Authorization: 'Bearer ' + Vue.ls.get('Access-Token'),
        },
        onUploadProgress: (progressEvent) => {
          this.videoLoading = true
          this.uploadProgressShow = true
          if (progressEvent.total > 0) {
            this.uploadProgress = Math.round((progressEvent.loaded / progressEvent.total ) * 100)  == 100?99:Math.round((progressEvent.loaded / progressEvent.total ) * 100);
          }
        },
      }).then((response) => {
          this.uploadProgressShow = true
          onSuccess(response.data, file);
          this.uploadProgress = 100;
          setTimeout(() => {
            this.videoLoading = false
            this.uploadProgress = 0;
            this.uploadProgressShow = false
          }, 2000)
          // 重置进度条
        }).catch((error) => {
          onError(error);
          this.uploadProgress = 0; // 重置进度条
        });
    },
    beforeUploadVideo(file) {
      let isJpgOrPng = file.type === 'video/mp4' || file.type === 'video/avi' || file.type === 'video/x-ms-wmv' || file.type === 'video/quicktime';
      if (!isJpgOrPng) {
        this.$message.error('格式错误，只能上传mp4、avi、wmv、mov格式的视频');
        return false;
      }
    },
    uploadVideo(info,record) {
      this.videoLoading = true
      if (info.file.status === "done") {
        let file = info.file
        let update = {}
        update.id = record.id
        update.videoId = file.response.data
        update.videoName = file.name

        if (this.safetyTestFlag) {
          updatePicOrVidOfAq(update, 'add', 'video', -1).then(res => {
            if (res.success) {
              this.getVideoDataOfAq()
              this.$message.success(`${info.file.name} 上传成功`)
            } else {
              this.$message.error("上传失败：" + res.message)
            }
            setTimeout(() => {
              this.videoLoading = false
            },1000)
          })
        } else {
          updatePicOrVid(update, 'add', 'video', -1).then(res => {
            if (res.success) {
              this.getByTestProgressId()
              this.$message.success(`${info.file.name} 上传成功`)
            } else {
              this.$message.error("上传失败：" + res.message)
            }
            setTimeout(() => {
              this.videoLoading = false
            },1000)
          })
        }

      } else if (info.file.status === "error") {
        this.videoLoading = false
        this.$message.error(`${info.file.name} 上传失败`)
      } else {
        this.videoLoading = false
      }
    },
    deleteVideo(record) {
      let update = {}
      update.id = record.id
      this.videoLoading = true
      let field = "video"
      if (this.newestVideoFlag) {
        field = "newestVideo"
        update.id = this.curSafetyTest.id
        update.safetyTestIdListString = this.curSafetyTest.safetyTestIdListString
        update.cellTestCodes = record.cellTestCodeList
      }
      if (this.safetyTestFlag) {
        updatePicOrVidOfAq(update, 'delete', field, -1).then(res => {
          if (res.success) {
            this.getVideoDataOfAq()
            setTimeout(() => {
              this.videoLoading = false
              this.$message.success("删除成功")
            },1000)
          } else {
            this.$message.error("删除失败：" + res.message)
          }
        })
      } else {
        updatePicOrVid(update, 'delete', 'video', -1).then(res => {
          if (res.success) {
            this.getByTestProgressId()
            setTimeout(() => {
              this.videoLoading = false
              this.$message.success("删除成功")
            },1000)
          } else {
            this.$message.error("删除失败：" + res.message)
          }
        })
      }

    },
  }
}
</script>
<style lang="less" scoped>
.right-top-div {
  position: absolute;
  top: 48px;
  right: 10px;
  height:43.5px;
  display: flex;
  align-items: center;
}
/deep/ .ant-table-tbody > tr > td {
  font-size: 12px;
  padding: 4px;
  font-weight: 400;
  overflow-wrap: break-word;
}
/deep/ .ant-table-thead > tr > th{
  font-size: 13px;
  padding: 5px;
  font-weight: 500;
}
.videoContent {
  width: 800px;
  height:500px;
  margin: 40px 0px 0px 40px;
  float: left
}
</style>