<template>
  <div class="formula-editor-content">
    <a-form :form="form" layout="vertical">
      <!-- 编辑区域 -->
      <div class="formula-editor-section">
        <!-- 公式编辑器部分 -->
        <formula-editor
          :initial-formula="latexFormula"
          :initial-params="independentParams"
          @update:formula="latexFormula = $event"
          @update:params="independentParams = $event"
          @update:variableDescription="variableDescription = $event"
          @update:paramDescriptions="paramDescriptions = $event"
          @formula-parsed="handleFormulaParsed"
        />
      </div>

      <!-- 解析结果显示区域 -->
      <div v-if="parsedParams.length > 0" class="analysis-results-section">
        <a-divider orientation="center" class="parse-results-divider">
          <span class="parse-results-label">
            <a-icon type="experiment" class="parse-results-icon" />
            参数解析结果
          </span>
        </a-divider>

        <!-- 变量部分 -->
        <variable-list :variables="variables" />

        <!-- 系数部分 -->
        <div v-if="coefficients.length > 0" class="form-section">
          <h5>系数设置</h5>

          <div>
            <a-tabs default-active-key="custom-mode" @change="handleModeChange" centered :force-render="true" class="unified-tabs coefficient-tabs optimization-tabs">
              <!-- 自定义系数标签页 -->
              <a-tab-pane key="custom-mode" tab="自定义系数" :force-render="true">
                <custom-coefficients-panel
                  ref="customCoefficientsPanel"
                  :coefficients="customCoefficients"
                  :parsed-latex="parsedLatex"
                  :parsed-params="parsedParams"
                  :formula-id="formulaId"
                  :formula-description="formulaDescription"
                  :variable-description="variableDescription"
                  :param-descriptions="paramDescriptions"
                  @update:formulaId="formulaId = $event"
                  @update:formulaDescription="formulaDescription = $event"
                  @coefficients-updated="handleCoefficientsUpdated"
                  @formula-saved="handleFormulaSaved"
                />
              </a-tab-pane>

              <!-- 系数寻优标签页 -->
              <a-tab-pane key="optimize-mode" tab="系数寻优">
                <coefficient-optimization-panel
                  :coefficients="optimizeCoefficients"
                  :parsed-latex="parsedLatex"
                  :parsed-params="parsedParams"
                  :formula-opt-id="formulaOptId"
                  :formula-opt-description="formulaOptDescription"
                  :variable-description="variableDescription"
                  :param-descriptions="paramDescriptions"
                  @update:formulaOptId="formulaOptId = $event"
                  @update:formulaOptDescription="formulaOptDescription = $event"
                  @formula-saved="handleFormulaSaved"
                />
              </a-tab-pane>
            </a-tabs>
          </div>
        </div>
      </div>
    </a-form>
  </div>
</template>

<script>
import { mapActions } from 'vuex';
import FormulaEditor from './FormulaEditor.vue';
import VariableList from './VariableList.vue';
import CustomCoefficientsPanel from './CustomCoefficientsPanel.vue';
import CoefficientOptimizationPanel from '@/components/fitting/CoefficientOptimizationPanel.vue';
import { cloneDeep } from 'lodash';
import formulaMixin from '@/mixins/formulaMixin';
import { showSuccess } from '@/utils/errorUtils';

export default {
  name: 'FormulaEditorWrapper',
  mixins: [formulaMixin],
  components: {
    FormulaEditor,
    VariableList,
    CustomCoefficientsPanel,
    CoefficientOptimizationPanel
  },
  data() {
    return {
      form: this.$form.createForm(this),
      latexFormula: {
        main_formula: 'Q_{capacity} = 1 - \\exp\\Bigg(  A_0 + A_1 * S + A_2 * S^2  + \\frac{B_1}{T_0}   + \\frac{B_2}{T_0^2}   + \\frac{B_3}{T_0^3}  + \\Big( C_0 + C_1 * T_0  + C_2 * T_0^2  + D_1 * S + D_2 * S^2  + E\\Big) * \\ln(t)  \\Bigg)',
        sub_formulas: [
          'E = E_0 * \\exp\\big( E_1 * T_0 + E_2 * S + E_3 * t \\big)',
          'T_0 = F_3 + F_2 * T + F_1 * T^2 + F_0 * T^3'
        ]
      },
      independentParams: ['t', 'T', 'S'],
      parsedLatex: '',
      parsedParams: [],
      coefficientMode: 'custom',
      formulaId: '',
      formulaOptId: '',
      formulaDescription: '',
      formulaOptDescription: '',
      variableDescription: '',
      paramDescriptions: {}
    };
  },
  computed: {
    // 过滤得到系数列表
    coefficients() {
      return this.parsedParams.filter(param => param.type === 'coefficient');
    },
    // 为自定义系数页创建独立的系数副本
    customCoefficients() {
      return this.coefficients.map(coef => cloneDeep(coef));
    },
    // 为系数寻优页创建独立的系数副本
    optimizeCoefficients() {
      return this.coefficients.map(coef => cloneDeep(coef));
    },
    // 过滤得到变量列表
    variables() {
      return this.parsedParams.filter(param => param.type === 'variable');
    }
  },
  methods: {
    ...mapActions(['updateFormula']),

    // 处理模式切换（自定义/寻优）
    handleModeChange(activeKey) {
      this.coefficientMode = activeKey === 'custom-mode' ? 'custom' : 'optimize';

      // 在标签页切换后，等待DOM更新完成后触发MathJax渲染
      this.$nextTick(() => {
        // 短暂延迟确保DOM已完全更新，包括标签页内容
        setTimeout(() => {
          // 只渲染当前激活的标签页内的系数表格
          const selector = `.ant-tabs-tabpane[data-tab-key="${activeKey}"]`;
          this.renderMathJax(true, selector); // 使用immediate=true确保立即渲染
        }, 100);
      });
    },

    // 处理公式解析结果
    handleFormulaParsed(result) {
      this.parsedLatex = result.parsedLatex;

      // 确保系数名称格式正确
      if (result.parsedParams && Array.isArray(result.parsedParams)) {
        // 处理系数名称，确保它们使用正确的LaTeX格式
        result.parsedParams.forEach(param => {
          if (param.type === 'coefficient' && param.name) {
            // 检查是否已经包含下标格式
            if (!param.name.includes('_{') && param.name.includes('_')) {
              // 处理下标（例如：A_1 -> A_{1}）
              param.name = param.name.replace(/([A-Za-z])_([A-Za-z0-9]+)/g, '$1_{$2}');
            }
          }
        });
      }

      this.parsedParams = result.parsedParams;

      // 更新变量和参数描述
      if (result.variableDescription) {
        this.variableDescription = result.variableDescription;
      }

      if (result.paramDescriptions) {
        this.paramDescriptions = result.paramDescriptions;
      }

      // 在解析公式后，等待DOM更新完成后触发MathJax渲染
      this.$nextTick(() => {
        // 短暂延迟确保DOM已完全更新，包括标签页内容
        setTimeout(() => {
          this.renderMathJax(true); // 使用immediate=true确保立即渲染
        }, 200);
      });
    },

    // 处理系数更新
    handleCoefficientsUpdated(updatedCoefficients) {
      if (updatedCoefficients && Array.isArray(updatedCoefficients)) {
        // 更新原始的parsedParams中的系数值
        updatedCoefficients.forEach(updatedCoef => {
          const existingParam = this.parsedParams.find(p => p.name === updatedCoef.name && p.type === 'coefficient');
          if (existingParam) {
            this.$set(existingParam, 'customValue', updatedCoef.customValue);
          }
        });

        // 强制更新视图
        this.$forceUpdate();
      }
    },

    // 处理公式保存
    handleFormulaSaved(formulaData) {
      // 更新到Vuex
      this.updateFormula(formulaData);
      showSuccess('公式已保存');
    },

    // 加载已保存的公式数据
    loadSavedFormula() {
      // 检查Vuex中是否已有公式
      const formula = this.$store.getters.getFormula;
      if (formula && formula.latex && formula.params && formula.params.length > 0) {
        this.latexFormula = formula.latex;
        this.parsedLatex = formula.latex;
        this.parsedParams = formula.params;

        // 如果有公式描述，也加载
        if (formula.description) {
          this.formulaDescription = formula.description;
          this.formulaOptDescription = formula.description;
        }

        // 如果有公式ID，也加载
        if (formula.id) {
          this.formulaId = formula.id;
          this.formulaOptId = formula.id;
        }

        // 设置系数模式
        if (formula.coefficientMode) {
          this.coefficientMode = formula.coefficientMode;
        }

        // 加载变量描述
        if (formula.variableDescription) {
          this.variableDescription = formula.variableDescription;
        }

        // 加载参数描述信息
        if (formula.params && formula.params.length > 0) {
          // 构建参数描述对象
          const descriptions = {};
          formula.params.forEach(param => {
            if (param.describe) {
              descriptions[param.name] = param.describe;
            }
          });
          this.paramDescriptions = descriptions;
        }
      }
    }
  },
  mounted() {
    this.loadSavedFormula();

    // 组件挂载后，等待DOM更新完成后触发MathJax渲染
    this.$nextTick(() => {
      // 使用immediate=true确保立即渲染一次
      this.renderMathJax(true);
    });
  }
};
</script>

<style scoped>


.form-section {
  margin: 16px 0;
  padding: 8px;
  border-radius: 4px;
}

.form-section h5 {
  text-align: center;
  font-weight: bold;
  font-size: 16px;
  background-color: #f5f5f5;
  padding: 8px 0;
  margin-top: 0;
  margin-bottom: 16px;
  border-radius: 4px;
}

h4 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

/* 标签页样式优化 */
:deep(.ant-tabs-nav) {
  margin-bottom: 16px;
}

:deep(.ant-tabs-tab) {
  padding: 8px 16px;
  transition: all 0.3s;
}

:deep(.ant-tabs-tab-active) {
  font-weight: 500;
}

:deep(.ant-divider-inner-text) {
  font-weight: 500;
  color: #333;
}

/* 参数解析结果标签样式优化 */
.parse-results-divider {
  margin: 24px 0 32px 0 !important;
}

.parse-results-divider :deep(.ant-divider-inner-text) {
  font-size: 18px !important;
  font-weight: 700 !important;
  color: #fff !important;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 50%, #0050b3 100%) !important;
  padding: 12px 24px !important;
  border-radius: 20px !important;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3) !important;
  border: 2px solid rgba(255, 255, 255, 0.2) !important;
  position: relative !important;
  overflow: hidden !important;
  animation: parseResultsGlow 2s ease-in-out infinite alternate !important;
}

.parse-results-divider :deep(.ant-divider-inner-text)::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: parseResultsShine 3s ease-in-out infinite;
}

.parse-results-label {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.parse-results-icon {
  font-size: 16px !important;
  animation: parseResultsRotate 2s linear infinite;
}

/* 动画效果 */
@keyframes parseResultsGlow {
  0% {
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  }
  100% {
    box-shadow: 0 6px 20px rgba(24, 144, 255, 0.5);
  }
}

@keyframes parseResultsShine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes parseResultsRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}


</style>