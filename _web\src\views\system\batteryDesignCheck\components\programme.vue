<template>
	<div class="content-wrap" style="background:#fff;overflow: auto;">
		<div class="wrapper">
			<div class="h1">
				方案设计栏(定义区)
			</div>
			<div class="statusbar">
				<span class="tip" style="float:left">★ 数据库关联必填项</span>

				<span class="tip" style="float:left;margin-left:50px">设计信息:灰色为数据库自动填写或自动计算生成</span>

				<span class="tip">设计标准:根据研发经验或工艺标准及客户需求制定</span>
			</div>
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :row-key="(record) => record.id"
        :pagination="false"
        :loading="loading"
        bordered
      >
        <template slot="batteryName">
          {{batteryName}}设计管理
        </template>


        <template slot="reference"  slot-scope="text,record">
          <div :class="record.defaultValue != null && text != record.defaultValue?'mark':''" v-if="record.dataType == 'eq'" @mouseover="() => record.showTip = true" @mouseleave="() => record.showTip = false">
            <a-input  :disabled="!updateBasic" :defaultValue="text != null?record.numType == 'percent'?numberHandle(numberMul(text,100),2) + '%':text:''"
                      @blur="updateData($event,record,'reference',record.numType)">
            </a-input>
            <a-tooltip placement="top" :visible="record.showTip" v-if="record.defaultValue != null && text != record.defaultValue">
              <template slot="title">
                <span>{{record.numType == 'percent'?numberHandle(numberMul(record.defaultValue,100),2) + '%':record.defaultValue}}</span>
              </template>
            </a-tooltip>
          </div>
          <a-input  :disabled="true" v-else-if="record.dataType == 'range'" :defaultValue="text"
                    @blur="updateData($event,record,'reference')"></a-input>

          <div v-else class="divcls div_border_right">{{text}}</div>
        </template>

        <template slot="allowableTolerance"  slot-scope="text,record">
          <input :disabled="true"  v-if="record.dataType == 'eq'"
                 @blur="updateData($event,record,'allowableTolerance')"></input>
          <a-input-group :class="record.defaultValue != null && text != record.defaultValue?'mark':''" v-else-if="record.dataType == 'range' || (record.dataType && record.dataType.startsWith('range') && record.dataType != 'range_up_down' && record.code.indexOf('1') > -1)" @mouseover="() => record.showTip = true" @mouseleave="() => record.showTip = false">
            <a-input style="width: 45%;float:left;padding: 0;font-size: smaller;" :disabled="!updateBasic"
                     @blur="updateDataOnlyRange($event,record,'allowableTolerance',record.numType,'before')"
                     :defaultValue="text != null ?record.numType == 'percent'?null == text.split('~')[0]?'':numberHandle(numberMul(text.split('~')[0],100),2) + '%':text.split('~')[0]:''"/>
            <div style="width: 10%;float:left;height: 25px;background-color: #efefef;" v-if="!updateBasic">~</div>
            <div style="width: 10%;float:left;height: 25px;" v-if="updateBasic">~</div>
            <a-input style="width: 45%;float:left;padding: 0;font-size: smaller;" :disabled="!updateBasic" :defaultValue="text != null ?record.numType == 'percent'?numberHandle(numberMul(text.split('~')[1],100),2) + '%':text.split('~')[1]:''"
                     @blur="updateDataOnlyRange($event,record,'allowableTolerance',record.numType,'after')"/>

            <a-tooltip placement="top" :visible="record.showTip" v-if="record.defaultValue != null && text != record.defaultValue">
              <template slot="title">
                <span>{{record.numType == 'percent'?numberHandle(numberMul(record.defaultValue.split('~')[0],100),2) + '%~'
                  +numberHandle(numberMul(record.defaultValue.split('~')[1],100),2)+'%':record.defaultValue}}</span>
              </template>
            </a-tooltip>
          </a-input-group>

          <a-input-group :class="record.defaultValue != null && text != record.defaultValue?'mark':''" v-else-if="record.dataType == 'range_up_down'" @mouseover="() => record.showTip = true" @mouseleave="() => record.showTip = false">

            <a-input style="width: 45%;float:left;padding: 0;font-size: smaller;" :disabled="!updateBasic"
                     @blur="updateDataOnlyRange($event,record,'allowableTolerance',record.numType,'before')"
                     :defaultValue="text != null ?record.numType == 'percent'?null == text.split('~')[0]?'':numberHandle(numberMul(text.split('~')[0],100),2) + '%':text.split('~')[0]:''">
              <a-icon slot="prefix" type="minus" />
            </a-input>
            <div style="width: 10%;float:left;height: 25px;background-color: #efefef;" v-if="!updateBasic">~</div>
            <div style="width: 10%;float:left;height: 25px;" v-if="updateBasic">~</div>
            <a-input style="width: 45%;float:left;padding: 0;font-size: smaller;" :disabled="!updateBasic" :defaultValue="text != null ?record.numType == 'percent'?numberHandle(numberMul(text.split('~')[1],100),2) + '%':text.split('~')[1]:''"
                     @blur="updateDataOnlyRange($event,record,'allowableTolerance',record.numType,'after')">
              <a-icon slot="prefix" type="plus" />
            </a-input>

            <a-tooltip placement="top" :visible="record.showTip" v-if="record.defaultValue != null && text != record.defaultValue">
              <template slot="title">
                <span>{{'-'+record.defaultValue.split('~')[0]+'~+'+record.defaultValue.split('~')[1]}}</span>
              </template>
            </a-tooltip>

          </a-input-group>

          <div v-else  class="divcls div_border_right"></div>
        </template>
        <template slot="remark"  slot-scope="text,record">
          <input disabled  v-if="record.dataType == 'eq' || record.dataType == 'range'"
                 @blur="updateData($event,record,'remark')" :value="text"></input>
          <div v-else class="divcls div_border_right" style="text-align: center">{{text}}</div>
        </template>

        <!--<div slot="allowableTolerance"  slot-scope="text,record"><input :value="text" @change="updateData($event,record,'allowableTolerance')"/></div>
        <div slot="remark"  slot-scope="text,record"><input :value="text" @change="updateData($event,record,'remark')"/></div>
-->

        <template slot="design"  slot-scope="text,record">

          <div v-if="record.dataType == 'eq'" class="divcls div_border_right">{{text != null?record.numType == 'percent'?numberHandle(numberMul(text,100),2) + '%':text:''}}</div>


          <input v-else-if="
          record.code == 'manager_positive_size_al_foil_width' ||
          record.code == 'manager_positive_size_coating_thickness_a_before' ||
          record.code == 'manager_positive_size_coating_thickness_b_before' ||
          record.code == 'manager_positive_size_coating_thickness_a_after' ||
          record.code == 'manager_positive_size_coating_thickness_b_after' ||
          record.code == 'manager_positive_size_total_electrode_thickness' ||
          record.code == 'manager_negative_size_cu_foil_length' ||
          record.code == 'manager_negative_size_cu_foil_width' ||
          record.code == 'manager_negative_size_coating_thickness_a_before' ||
          record.code == 'manager_negative_size_coating_thickness_b_before' ||
          record.code == 'manager_negative_size_coating_thickness_a_after' ||
          record.code == 'manager_negative_size_coating_thickness_b_after' ||
          record.code == 'manager_negative_size_total_electrode_thickness' ||
          record.code == 'manager_diaphragm_size_total_thickness' ||
          record.code == 'manager_diaphragm_size_c40' ||
          record.code == 'manager_negative_size_c40' ||
          record.code == 'manager_negative_size_length_material_area' ||
          record.code == 'manager_negative_size_width_material_area' ||
          record.code == 'manager_diaphragm_size_diaphragm_width' ||
          record.code == 'manager_diaphragm_size_diaphragm_length' ||


          record.code == 'manager_negative_workmanship_n_p' ||
          record.code == 'manager_electrolyte_workmanship_liquid_injection_volume' ||
          record.code == 'manager_electrolyte_workmanship_liquid_injection_volume_min' ||
          record.code == 'manager_coiling_coiling_coiling_diameter' ||
          record.code == 'manager_coiling_coiling_height_negative' ||
          record.code == 'manager_coiling_coiling_layers_number' ||
          record.code == 'manager_coiling_coiling_height_diaphragm'


            " :value="numberHandle(text,2)" class="divcls" @change="updateData($event,record,'design')" disabled/>


          <input v-else-if="record.code == 'manager_positive_size_al_foil_length' ||
          record.code == 'manager_positive_size_al_foil_length_yzh' ||
          record.code == 'manager_negative_size_cu_foil_length' ||
          record.code == 'manager_negative_size_length_material_area' ||
          record.code == 'manager_positive_size_length_material_area_yzh'"
                 :value="numberHandle(text,1)" class="divcls" @change="updateData($event,record,'design')" disabled/>



          <a-select v-else-if="record.code == 'manager_positive_gram_capacity_active'" style="width: 100%" :value="text"
                    :disabled="isOwn == 1 ?false:true"
                    @change="updateSelectData2($event,record)">
            <a-select-option value="LSN" >
              LSN
            </a-select-option>
            <a-select-option value="NCM" >
              NCM
            </a-select-option>
            <a-select-option value="LFP" >
              LFP
            </a-select-option>
            <a-select-option value="LFMP" >
              LFMP
            </a-select-option>
            <a-select-option value="LMP" >
              LMP
            </a-select-option>


          </a-select>

          <a-select v-else-if="record.code == 'manager_positive_gram_capacity_a2' || record.code == 'manager_negative_gram_capacity_a2' "

                    :disabled="isOwn == 1 ?false:true"
                    style="width: 100%" :value="text"
                    @change="updateSelectData2($event,record)">
            <a-select-option value="Gen.0" >
              Gen.0
            </a-select-option>
            <a-select-option value="Gen.1" >
              Gen.1
            </a-select-option>
            <a-select-option value="Gen.2" >
              Gen.2
            </a-select-option>
            <a-select-option value="Gen.3" >
              Gen.3
            </a-select-option>
            <a-select-option value="Gen.4" >
              Gen.4
            </a-select-option>
            <a-select-option value="Gen.5" >
              Gen.5
            </a-select-option>
            <a-select-option value="Gen.6" >
              Gen.6
            </a-select-option>



          </a-select>


          <a-select v-else-if="record.code == 'manager_positive_gram_capacity_multiplication'" style="width: 100%" :value="text"
                    :disabled="isOwn == 1 ?false:true"
                    @change="updateSelectData2($event,record)">
            <a-select-option value="0.1" >
              0.1
            </a-select-option>

            <a-select-option value="0.2" >
              0.2
            </a-select-option>

            <a-select-option value="0.33" >
              0.33
            </a-select-option>

            <a-select-option value="0.5" >
              0.5
            </a-select-option>

            <a-select-option value="1" >
              1
            </a-select-option>

          </a-select>


          <a-select v-else-if="record.code == 'manager_negative_gram_capacity_active'" style="width: 100%" :value="text"
                    :disabled="isOwn == 1 ?false:true"
                    @change="updateSelectData2($event,record)">
            <a-select-option value="AG" >
              AG
            </a-select-option>
            <a-select-option value="Gr" >
              Gr
            </a-select-option>
            <a-select-option value="SiC/Gr" >
              SiC/Gr
            </a-select-option>
            <a-select-option value="SiO/Gr" >
              SiO/Gr
            </a-select-option>

          </a-select>


          <!--<a-select v-else-if="record.code == 'manager_structural_f10_f10'" style="width: 100%" :value="text"
                    :disabled="isOwn == 1 ?false:true"
                      @change="updateSelectData2($event,record)">
            <a-select-option value="4695" >
              4695
            </a-select-option>
            <a-select-option value="V2" >
              V2
            </a-select-option>

            <a-select-option value="V3" >
              V3
            </a-select-option>

            <a-select-option value="V4" >
              V4
            </a-select-option>

            <a-select-option value="V5" >
              V5
            </a-select-option>


          </a-select>-->



          <a-select v-else-if="record.code == 'manager_structural_f20_f20'" style="width: 100%" :value="text"
                    :disabled="isOwn == 1 ?false:true"
                    @change="updateSelectData2($event,record)">
            <a-select-option value="V1" >
              V1
            </a-select-option>
            <a-select-option value="V2" >
              V2
            </a-select-option>

            <a-select-option value="V3" >
              V3
            </a-select-option>

            <a-select-option value="V4" >
              V4
            </a-select-option>

            <a-select-option value="V5" >
              V5
            </a-select-option>


          </a-select>



          <input :value="null == text?'':numberHandle(numberMul(text,100),2) + '%'" v-else-if="record.unit == '百分数(2位)'"
                 onkeyup="value = value == 0 || value == value.match(/^[1-9]\d*\.\d*|0\.\d*[1-9]\d*$/) ||
                  value == value.match(/^[1-9]\d*$/)
                 || value.match(/^(100|[1-9]?\d(\.\d\d?\d?)?)%$|0$/)?value:''"


                 :class="record.code == 'manager_positive_workmanship_porosity_a' ||
                 record.code == 'manager_positive_workmanship_porosity_b' ||
                 record.code == 'manager_negative_workmanship_porosity_a' ||
                 record.code == 'manager_coiling_coiling_diameter_direction_group_margin' ||
                 record.code == 'manager_negative_workmanship_porosity_b'
                 ?'divcls':''"
                 :disabled="record.code == 'manager_positive_workmanship_porosity_a' ||
                 record.code == 'manager_positive_workmanship_porosity_b' ||
                 record.code == 'manager_negative_workmanship_porosity_a' ||
                 record.code == 'manager_coiling_coiling_diameter_direction_group_margin' ||
                 record.code == 'manager_negative_workmanship_porosity_b'
                 ?true:false"
                 @change="updateData($event,record,'design','percent')" />


          <input v-else-if="record.code == 'manager_positive_gram_capacity_active' ||
          record.code == 'manager_positive_formula_conductive1' ||
          record.code == 'manager_positive_formula_conductive2' ||
          record.code == 'manager_positive_formula_conductive3' ||
          record.code == 'manager_positive_formula_binder' ||
          record.code == 'manager_positive_formula_binder2' ||
          record.code == 'manager_positive_formula_binder3' ||
          record.code == 'manager_negative_gram_capacity_active' ||
          record.code == 'manager_negative_formula_conductive1' ||
          record.code == 'manager_negative_formula_conductive2' ||
          record.code == 'manager_negative_formula_conductive3' ||
          record.code == 'manager_negative_formula_binder' ||
          record.code == 'manager_negative_formula_binder2' ||
          record.code == 'manager_negative_formula_binder3' ||
          record.code == 'manager_negative_size_cu_foil_model' ||
          record.code == 'manager_diaphragm_coating_coating_layer' ||
          record.code == 'manager_electrolyte_component_solvent' ||
          record.code == 'manager_electrolyte_component_solvent_proportion' ||
          record.code == 'manager_electrolyte_component_additive' ||
          record.code == 'manager_diaphragm_coating_base' ||
          record.code == 'manager_structural_f10_f10' ||
          record.code == 'manager_positive_size_al_foil_model' "

                 :value="text"  @change="updateData($event,record,'design')"/>


          <input v-else-if="record.code == 'manager_positive_size_length_material_area'"
                 :value="numberHandle(text,1)" onkeyup="value = value == 0 || value == value.match(/^[1-9]\d*\.\d*|0\.\d*[1-9]\d*$/) ||
                  value == value.match(/^[1-9]\d*$/)
                 ?value:''" @change="updateData($event,record,'design')"/>


          <input v-else :value="record.code == 'manager_positive_workmanship_areal_density_a'
          || record.code == 'manager_positive_workmanship_areal_density_b'
          || record.code == 'manager_positive_workmanship_compaction_density_a'
          || record.code == 'manager_positive_workmanship_compaction_density_b'
          || record.code == 'manager_negative_workmanship_areal_density_a'
          || record.code == 'manager_negative_workmanship_areal_density_b'
          || record.code == 'manager_negative_workmanship_compaction_density_a'
          || record.code == 'manager_negative_workmanship_compaction_density_b'
          || record.code == 'manager_positive_gram_capacity_platform_voltage'
          || record.code == 'manager_positive_type_dycksx'
          || record.code == 'manager_positive_type_dyckxx'
          ?numberHandle(text,3):numberHandle(text,2)" onkeyup="value = value == 0 || value == value.match(/^[1-9]\d*\.\d*|0\.\d*[1-9]\d*$/) ||
                  value == value.match(/^[1-9]\d*$/)
                 ?value:''" @change="updateData($event,record,'design')"/>





        </template>
        <div slot="eve"  slot-scope="text,record">

          <input

            :value="text"  @change="updateData($event,record,'eve')" />

        </div>
        <!--<div slot="reference"  slot-scope="text,record"><input :value="text" @change="updateData($event,record,'reference')"/></div>-->
        <!--<div slot="allowableTolerance"  slot-scope="text,record"><input :value="text" @change="updateData($event,record,'allowableTolerance')"/></div>
        <div slot="remark"  slot-scope="text,record"><input :value="text" @change="updateData($event,record,'remark')"/></div>
        --><div slot="input"><input /></div>

        <template slot="checkStatus" slot-scope="text,record">
          <div class="divcls"  >

            <span>

              <svg width="20" height="20" v-if="record.dataType == 'eq' || (record.dataType == 'eq_unchange' && record.design != null && record.reference != null && parseFloat(record.design) == parseFloat(record.reference)) || checkRangeStatus(record) == 1">
                <circle cx="10" cy="11" r="8" fill="#58a55c" />
              </svg>
              <svg width="20" height="20" v-else-if="checkRangeStatus(record) == 2 || (record.dataType == 'eq_unchange' && record.design != null && record.reference != null && parseFloat(record.design) != parseFloat(record.reference))">
                <circle cx="10" cy="11" r="8" fill="#f33" />
              </svg>
              <svg width="20" height="20" v-else-if="text==3">
                <circle cx="10" cy="11" r="8" fill="#fac858" />
              </svg>

              <span v-else>/</span>
            </span>
          </div>
        </template>
        <div class="divcls ant-table-row-cell-ellipsis ant-table-row-cell-break-word" slot="divcls" slot-scope="text" :title="text">{{text}}</div>
        <div class="divcls div_border_right" slot="unitcls" slot-scope="text">{{text}}</div>
      </a-table>
		</div>
		<div class="wrapper" id="right">
			<div class="h1" style="overflow-y: hidden">
				设计分析栏(自动分析)<a-icon
					type="left"
					v-if="showLeft"
					style="float: right;font-size: 25px"
					@click="openLeft"
				/>
				<a-icon type="right" v-if="!showLeft" style="float: right;font-size: 25px" @click="openLeft" />
			</div>

			<div>
				<div
					style="float: left;width: 70%;display: none;background-color: #fff;"
					id="densityPositiveColumns"
					class="redius"
				>
					<h1 style="display: inline-block"><span class="color-block"></span>密度分析（手动输入定义）</h1>
					<a-table
						id="not_border"
						:columns="densityPositiveColumns"
						:data-source="densityPositiveSource"
						:row-key="record => record.id"
						:pagination="false"
						:loading="loading"
						:showHeader="false"
						bordered
						style="padding: 0 5px 5px 5px;border-radius: 5px;border-left: 0"
					>
						<div slot="name1" slot-scope="text, record, index">
							<div
								v-if="index == 10"
								class="blank"
								style="border-right: 0;border-left: 0;background-color: #FFFFFF;height: 25px"
							></div>
							<input :value="text" v-else :disabled="true" style="border-left: 1px solid #e8e8e8;" class="tdcls" />
						</div>
						<div slot="value3" slot-scope="text, record, index">
							<div
								v-if="index == 10"
								class="blank"
								style="border-right: 0;border-left: 0;background-color: #FFFFFF;height: 25px"
							></div>
							<input
								v-else
								:value="
									record.code == 'unChance' || record.code == 'first' || record.code == 'second'
										? text
										: numberHandle(text, 2)
								"
								disabled
								:class="
									record.code == 'unChance' ||
									record.code == 'first' ||
									record.code == 'second' ||
									record.value1 != null ||
									record.value2 != null ||
									record.name1 != null
										? 'tdcls'
										: ''
								"
							/>
						</div>
						<div slot="value1" slot-scope="text, record, index">
							<div
								v-if="index == 10"
								class="blank"
								style="border-right: 0;border-left: 0;background-color: #FFFFFF;height: 25px"
							></div>
							<input
								v-else
								:value="text != null && record.code == null ? numberHandle(numberMul(text, 100), 2) + '%' : text"
								:disabled="true"
								class="tdcls"
							/>
						</div>
						<div slot="value2" slot-scope="text, record, index">
							<div
								v-if="index == 10"
								class="blank"
								style="border-right: 0;border-left: 0;background-color: #FFFFFF;height: 25px"
							></div>
							<input
								v-else
								:value="record.code == 'unChance' ? text : numberHandle(text, 2)"
								:disabled="
									record.value1 == null ||
									record.code == 'unChance' ||
									record.code == 'first' ||
									record.code == 'second'
										? true
										: false
								"
								@change="updateOtherData($event, record, 'value2', null, true)"
								onkeyup="value = value == 0 || value == value.match(/^[1-9]\d*\.\d*|0\.\d*[1-9]\d*$/) ||
                    value == value.match(/^[1-9]\d*$/)
                   ?value:''"
								:class="record.code == 'unChance' || record.code == 'first' || record.code == 'second' ? 'tdcls' : ''"
							/>
						</div>
					</a-table>
				</div>

				<div
					style="float: right;width: 28.5%;display: none;background-color: #fff;"
					id="densityCheckColumns"
					class="redius1"
				>
					<h1 style="display: inline-block"><span class="color-block"></span>密度查询手册</h1>
					<a-table
						:columns="densityCheckColumns"
						:data-source="densityCheckSource"
						:row-key="record => record.id"
						:pagination="false"
						:loading="loading"
						:showHeader="false"
            :scroll="{ x: false, y: 573 }"
						bordered
						style="padding: 0 5px 5px 5px;border-radius: 5px;"
					>
						<div slot="name1" slot-scope="text, record">
							<input
								:value="text"
                :title="text"
								:disabled="record.code == 'unChance' ? true : false"
								@change="updateOtherData($event, record, 'name1')"
								:class="record.code == 'unChance' ? 'tdcls' : ''"
							/>
						</div>
						<div slot="value1" slot-scope="text, record">
							<input
								:value="record.code == 'unChance' ? text : numberHandle(text, 0)"
								:disabled="record.code == 'unChance' ? true : false"
								@change="updateOtherData($event, record, 'value1')"
								onkeyup="value = value == 0 || value == value.match(/^[1-9]\d*\.\d*|0\.\d*[1-9]\d*$/) ||
                    value == value.match(/^[1-9]\d*$/)
                   ?value:''"
								:class="record.code == 'unChance' ? 'tdcls' : ''"
							/>
						</div>
					</a-table>
				</div>

				<div style="float: left;width: 100%;background-color: #fff;" id="chart0" class="redius">
					<h1 style="display: inline-block"><span class="color-block"></span>容量+能量+能量密度设计分析图</h1>

					<span ref="chart0" style="width: 100%;height: 200px;display: inline-block;background: #fff;"> </span>
				</div>

				<div style="float: right;width: 100%;background-color: #fff;" id="chart1" class="redius1">
					<h1 style="display: inline-block"><span class="color-block"></span>容量设计NP分析</h1>

					<span ref="chart1" style="width: 100%;height: 200px;display: inline-block;background: #fff;"> </span>
				</div>

				<div id="chart34">
					<div style="float: left;width: 100%;background-color: #fff;" class="redius" id="redius">
						<h1 style="display: inline-block"><span class="color-block"></span>群裕度分析图</h1>

						<span ref="chart2" style="width: 100%;height: 223px;display: inline-block;background: #fff;"> </span>
					</div>

					<div style="float: right;width: 100%;background-color: #fff;" class="redius1" id="redius1">
						<h1 style="display: inline-block"><span class="color-block"></span>重量设计分析图</h1>

						<span ref="chart3" style="width: 100%;height: 224px;display: inline-block;background: #fff;"> </span>
					</div>
				</div>

				<div
					v-show="!showLeft"
					style="width: 100%;float: right;
      display: inline-block;background-color: #fff"
					class="redius1"
				>
					<h1 style="display: inline-block"><span class="color-block"></span>分析汇总表</h1>

					<a-table
						:columns="summaryAnalysisColumns"
						:data-source="summaryAnalysisSource"
						:row-key="record => record.id"
						:pagination="false"
						:loading="loading"
						bordered
						:scroll="{ x: 300 }"
						style="padding: 0 5px 5px 5px;border-radius: 5px;"
					>
						<div slot="name1" slot-scope="text, record" :title="text">
							<input :value="text" disabled class="tdcls" />
						</div>
						<div slot="unit" slot-scope="text, record">
							<input :value="text" disabled class="tdcls" />
						</div>

						<div slot="value1" slot-scope="text, record">
							<input
								:value="
									null == text
										? ''
										: record.code == 'other_summary_analysis_j33' ||
										  record.code == 'other_summary_analysis_j36' ||
										  record.code == 'other_summary_analysis_j39' ||
										  record.code == 'other_summary_analysis_j41'
										? numberHandle(numberMul(text, 100), 2) + '%'
										: numberHandle(text, 2)
								"
								disabled
								class="tdcls"
								onkeyup="value = value == 0 || value == value.match(/^[1-9]\d*\.\d*|0\.\d*[1-9]\d*$/) ||
                    value == value.match(/^[1-9]\d*$/)
                   ?value:''"
								@change="updateOtherData($event, record, 'value1')"
							/>
						</div>

						<div slot="remark" slot-scope="text, record">
							<input :value="text" @change="updateOtherData($event, record, 'remark')" />
						</div>
					</a-table>
				</div>

				<div v-show="!showLeft" style="display: inline-block;background-color: #fff" class="redius1">
					<h1><span class="color-block"></span>容量设计分析</h1>

					<a-table
						:columns="capacityDesignColumns"
						:data-source="capacityDesignSource"
						:row-key="record => record.id"
						:pagination="false"
						:loading="loading"
						bordered
						style="padding: 0 5px 5px 5px;border-radius: 5px;"
					>
						<div slot="name1" slot-scope="text, record" :title="text">
							<input :value="text" disabled class="tdcls" />
						</div>
						<div slot="unit" slot-scope="text, record">
							<input :value="text" disabled class="tdcls" />
						</div>

						<div slot="value1" slot-scope="text, record">
							<input
								:value="
									null == text
										? ''
										: record.code == 'other_capacity_design_j7' || record.code == 'other_capacity_design_j22'
										? numberHandle(numberMul(text, 100), 2) + '%'
										: numberHandle(text, 2)
								"
								disabled
								class="tdcls"
								onkeyup="value = value == 0 || value == value.match(/^[1-9]\d*\.\d*|0\.\d*[1-9]\d*$/) ||
                    value == value.match(/^[1-9]\d*$/)
                   || value.match(/^(100|[1-9]?\d(\.\d\d?\d?)?)%$|0$/)?value:''"
								@change="updateOtherData($event, record, 'value1', 'percent')"
							/>
						</div>

						<div slot="value2" slot-scope="text, record">
							<input :value="text" @change="updateOtherData($event, record, 'value2')" />
						</div>

						<div slot="remark" slot-scope="text, record" @change="updateOtherData($event, record, 'remark')">
							<input :value="text" />
						</div>

						<template slot="checkStatus" slot-scope="text, record">
							<div class="divcls" @click="isOwn == 1 && design.manCheckStatus == 0 ? edit2(record.id) : null">
								<a-select
									v-if="record.editable"
									dropdown-class-name="dropdownClassName"
									style="width: 100%"
									@blur="getList(false)"
									:autoFocus="true"
									:open="true"
									:showArrow="false"
									:default-value="text"
									@change="updateOtherSelectData($event, record)"
								>
									<a-select-option :value="parseInt(1)" @click="getList(false)">
										满足
									</a-select-option>
									<a-select-option :value="parseInt(2)" @click="getList(false)">
										不满足
									</a-select-option>
									<a-select-option :value="parseInt(3)" @click="getList(false)">
										TBD
									</a-select-option>
									<a-select-option :value="parseInt(0)" @click="getList(false)">
										/
									</a-select-option>
								</a-select>

								<span class="spanstatus" v-else>
									<a-icon v-if="text == 1" class="success1" type="check" />

									<svg
										t="1669081811549"
										v-else-if="text == 3"
										class="icon warn1"
										viewBox="0 0 1024 1024"
										version="1.1"
										xmlns="http://www.w3.org/2000/svg"
										p-id="3553"
										width="16"
										height="14"
									>
										<path
											d="M450.602458 665.598073a62.463819 62.463819 0 0 0 122.879645 0L614.441984 102.399704A102.615282 102.615282 0 0 0 512.04228 0 105.256116 105.256116 0 0 0 409.642577 112.639674L450.602458 665.598073z m61.439822 153.599556a102.399704 102.399704 0 1 0 102.399704 102.399703 96.740773 96.740773 0 0 0-102.399704-102.399703z"
											p-id="3554"
											fill="#fec303"
										></path>
									</svg>

									<!--<a-icon v-else-if="text==3" class="warn1" type="exclamation" />-->
									<a-icon v-else-if="text == 2" class="fail1" type="close" />
									<span v-else>/</span>
								</span>
							</div>
						</template>
					</a-table>
				</div>

				<div v-show="!showLeft" style="background-color: #fff" class="redius1">
					<h1><span class="color-block"></span>尺寸&群裕度&注液量&重量分析</h1>

					<a-table
						:columns="sizeAndOtherColumns"
						:data-source="sizeAndOtherSource"
						:row-key="record => record.id"
						:pagination="false"
						:loading="loading"
						:scroll="{ x: 400 }"
						bordered
						style="padding: 0 5px 5px 5px;border-radius: 5px;margin-bottom: 8px;"
					>
						<div slot="name2" disabled slot-scope="text, record" :title="text">
							<input :value="text" class="tdcls" disabled />
						</div>
						<div slot="unit" disabled slot-scope="text, record">
							<input :value="text" class="tdcls" disabled />
						</div>
						<div slot="value1" slot-scope="text, record">
							<input
								:value="
									null == text
										? ''
										: record.code == 'other_size_and_other_z31'
										? numberHandle(numberMul(text, 100), 2) + '%'
										: record.code == 'other_size_and_other_x42'
										? numberHandle(text, 3)
										: numberHandle(text, 2)
								"
								disabled
								class="tdcls"
								onkeyup="value = value == 0 || value == value.match(/^[1-9]\d*\.\d*|0\.\d*[1-9]\d*$/) ||
                    value == value.match(/^[1-9]\d*$/)
                   || value.match(/^(100|[1-9]?\d(\.\d\d?\d?)?)%$|0$/)?value:''"
								@change="updateOtherData($event, record, 'value1')"
							/>
						</div>
						<div slot="value2" slot-scope="text, record">
							<input
								:value="
									record.code == 'other_size_and_other_k3' ||
									record.code == 'other_size_and_other_k5' ||
									record.code == 'other_size_and_other_z31' ||
									record.code == 'other_size_and_other_z35' ||
									record.code == 'other_size_and_other_z37' ||
									record.code == 'other_size_and_other_x44'
										? '/'
										: numberHandle(text, 2)
								"
								:disabled="!(record.code == 'other_size_and_other_x42')"
								:class="!(record.code == 'other_size_and_other_x42') ? 'tdcls' : ''"
								onkeyup="value = value == 0 || value == value.match(/^[1-9]\d*\.\d*|0\.\d*[1-9]\d*$/) ||
                    value == value.match(/^[1-9]\d*$/)
                   || value.match(/^(100|[1-9]?\d(\.\d\d?\d?)?)%$|0$/)?value:''"
								@change="updateOtherData($event, record, 'value2')"
							/>
						</div>
						<div slot="value3" slot-scope="text, record">
							<input
								:value="
									null == text
										? ''
										: record.code == 'other_size_and_other_z31'
										? numberHandle(numberMul(text, 100), 2) + '%'
										: record.code == 'other_size_and_other_x42'
										? numberHandle(text, 3)
										: numberHandle(text, 2)
								"
								disabled
								class="tdcls"
								onkeyup="value = value == 0 || value == value.match(/^[1-9]\d*\.\d*|0\.\d*[1-9]\d*$/) ||
                    value == value.match(/^[1-9]\d*$/)
                   || value.match(/^(100|[1-9]?\d(\.\d\d?\d?)?)%$|0$/)?value:''"
								@change="updateOtherData($event, record, 'value3')"
							/>
						</div>
						<div slot="value4" slot-scope="text, record">
							<input
								:value="
									record.code == 'other_size_and_other_k3' ||
									record.code == 'other_size_and_other_k5' ||
									record.code == 'other_size_and_other_z31' ||
									record.code == 'other_size_and_other_z35' ||
									record.code == 'other_size_and_other_z37' ||
									record.code == 'other_size_and_other_x44'
										? '/'
										: numberHandle(text, 2)
								"
								:disabled="!(record.code == 'other_size_and_other_x42')"
								:class="!(record.code == 'other_size_and_other_x42') ? 'tdcls' : ''"
								onkeyup="value = value == 0 || value == value.match(/^[1-9]\d*\.\d*|0\.\d*[1-9]\d*$/) ||
                    value == value.match(/^[1-9]\d*$/)
                   || value.match(/^(100|[1-9]?\d(\.\d\d?\d?)?)%$|0$/)?value:''"
								@change="updateOtherData($event, record, 'value4')"
							/>
						</div>
						<div slot="value5" slot-scope="text, record">
							<input
								:value="
									record.code == 'other_size_and_other_k3' ||
									record.code == 'other_size_and_other_k5' ||
									record.code == 'other_size_and_other_z35' ||
									record.code == 'other_size_and_other_x44'
										? '/'
										: null == text
										? ''
										: record.code == 'other_size_and_other_z31'
										? numberHandle(numberMul(text, 100), 2) + '%'
										: numberHandle(text, 2)
								"
								:disabled="!(record.code == 'other_size_and_other_x42')"
								:class="!(record.code == 'other_size_and_other_x42') ? 'tdcls' : ''"
								onkeyup="value = value == 0 || value == value.match(/^[1-9]\d*\.\d*|0\.\d*[1-9]\d*$/) ||
                    value == value.match(/^[1-9]\d*$/)
                   || value.match(/^(100|[1-9]?\d(\.\d\d?\d?)?)%$|0$/)?value:''"
								@change="updateOtherData($event, record, 'value5')"
							/>
						</div>
						<div slot="value8" slot-scope="text, record">
							<input
								:value="
									record.code != 'other_size_and_other_x44' &&
									record.code != 'other_size_and_other_x46' &&
									record.code != 'other_size_and_other_x48'
										? '/'
										: numberHandle(text, 2)
								"
								:disabled="!(record.code == 'other_size_and_other_x44')"
								:class="!(record.code == 'other_size_and_other_x44') ? 'tdcls' : ''"
								onkeyup="value = value == 0 || value == value.match(/^[1-9]\d*\.\d*|0\.\d*[1-9]\d*$/) ||
                    value == value.match(/^[1-9]\d*$/)
                   || value.match(/^(100|[1-9]?\d(\.\d\d?\d?)?)%$|0$/)?value:''"
								@change="updateOtherData($event, record, 'value8')"
							/>
						</div>
						<div slot="value9" slot-scope="text, record">
							<input
								:value="
									record.code != 'other_size_and_other_x46' && record.code != 'other_size_and_other_x48'
										? '/'
										: numberHandle(text, 2)
								"
								disabled
								class="tdcls"
								onkeyup="value = value == 0 || value == value.match(/^[1-9]\d*\.\d*|0\.\d*[1-9]\d*$/) ||
                    value == value.match(/^[1-9]\d*$/)
                   || value.match(/^(100|[1-9]?\d(\.\d\d?\d?)?)%$|0$/)?value:''"
								@change="updateOtherData($event, record, 'value9')"
							/>
						</div>
						<div slot="value6" slot-scope="text, record">
							<input
								:value="
									record.code == 'other_size_and_other_k3' ||
									record.code == 'other_size_and_other_k5' ||
									record.code == 'other_size_and_other_z31' ||
									record.code == 'other_size_and_other_z35' ||
									record.code == 'other_size_and_other_z37' ||
									record.code == 'other_size_and_other_x42'
										? '/'
										: numberHandle(text, 2)
								"
								:disabled="
									!(
										record.code == 'other_size_and_other_k1' ||
										record.code == 'other_size_and_other_k7' ||
										record.code == 'other_size_and_other_x44' ||
										record.code == 'other_size_and_other_k9'
									)
								"
								:class="
									!(
										record.code == 'other_size_and_other_k1' ||
										record.code == 'other_size_and_other_k7' ||
										record.code == 'other_size_and_other_x44' ||
										record.code == 'other_size_and_other_k9'
									)
										? 'tdcls'
										: ''
								"
								onkeyup="value = value == 0 || value == value.match(/^[1-9]\d*\.\d*|0\.\d*[1-9]\d*$/) ||
                    value == value.match(/^[1-9]\d*$/)
                   || value.match(/^(100|[1-9]?\d(\.\d\d?\d?)?)%$|0$/)?value:''"
								@change="updateOtherData($event, record, 'value6')"
							/>
						</div>
						<div slot="value7" slot-scope="text, record">
							<input
								:value="
									record.code == 'other_size_and_other_k3' ||
									record.code == 'other_size_and_other_k5' ||
									record.code == 'other_size_and_other_z31' ||
									record.code == 'other_size_and_other_z35' ||
									record.code == 'other_size_and_other_z37' ||
									record.code == 'other_size_and_other_x42'
										? '/'
										: numberHandle(text, 2)
								"
								:disabled="
									!(
										record.code == 'other_size_and_other_k1' ||
										record.code == 'other_size_and_other_k7' ||
										record.code == 'other_size_and_other_x44' ||
										record.code == 'other_size_and_other_k9'
									)
								"
								:class="
									!(
										record.code == 'other_size_and_other_k1' ||
										record.code == 'other_size_and_other_k7' ||
										record.code == 'other_size_and_other_x44' ||
										record.code == 'other_size_and_other_k9'
									)
										? 'tdcls'
										: ''
								"
								onkeyup="value = value == 0 || value == value.match(/^[1-9]\d*\.\d*|0\.\d*[1-9]\d*$/) ||
                    value == value.match(/^[1-9]\d*$/)
                   || value.match(/^(100|[1-9]?\d(\.\d\d?\d?)?)%$|0$/)?value:''"
								@change="updateOtherData($event, record, 'value7')"
							/>
						</div>
					</a-table>
				</div>
			</div>
		</div>
		<a-drawer
			:bodyStyle="{ height: '100%' }"
			placement="right"
			:closable="false"
			width="80%"
			:visible="visible3"
			@close="onClose3"
		>
			<iframe :src="pdfUrl + '#view=FitH,top&'" width="100%" height="100%"></iframe>
		</a-drawer>
	</div>
</template>
<script>
import { ALL_APPS_MENU } from "@/store/mutation-types"
import {
	list,
	update,
	otherList,
	otherUpdate,
	exportExcel,
	exportExcel1
} from "@/api/modular/system/batteryDesignManageManager"
import { getBatteryDesign } from "@/api/modular/system/batterydesignManage"
import { numberMul } from "@/api/modular/system/staffManage"

import Vue from "vue"
import { mapActions, mapGetters } from "vuex"
export default {
	components: {},
	data() {
		return {
			spinning: false,
			visible3: false,
			pdfUrl: "",
			checkStatus: { 0: "/", 1: "满足", 2: "不满足", 3: "TBD" },
			design: {},
			structureType1: "",
			showLeft: true,
			windowHeight: document.documentElement.clientHeight - 200,
			manager_project: 0,
			manager_performance: 0,
			manager_size: 0,
			isOwn: 0,
			manager_positive: 0,
			manager_positive_gram_capacity: 0,
			manager_positive_formula: 0,
			manager_positive_size: 0,
			manager_negative_overhang: 0,
			manager_positive_overhang: 0,
			manager_positive_workmanship: 0,
			manager_negative: 0,
      manager_tx:0,
			manager_electrolyte: 0,
			manager_electrolyte_component: 0,
			manager_diaphragm: 0,
			manager_negative_gram_capacity: 0,
			manager_negative_formula: 0,
			manager_negative_size: 0,
			manager_negative_workmanship: 0,
			manager_diaphragm_coating: 0,
			manager_coiling_core_size: 0,
			manager_coiling_overhang: 0,
			manager_structural_positive_terminal: 0,
			manager_structural_negative_terminal: 0,
			manager_structural_negative_rivet1: 0,
			manager_structural_cover: 0,
			manager_structural_positive_bus_plate1: 0,
			manager_structural_positive_rivet_insulating: 0,
			manager_structural_negative_terminal_insulating: 0,
			manager_structural_negative_rivet_insulating: 0,
			manager_structural_positive_bus_plate_insulating: 0,
			manager_structural_negative_bus_plate_insulating: 0,
			manager_structural_jr_insulating: 0,
			manager_structural_insulating: 0,
			manager_structural_other: 0,
			manager_structural_total: 0,
			manager_structural_negative_bus_plate1: 0,
			manager_structural_vent: 0,
			manager_coiling_rolling_pressing: 0,
			manager_diaphragm_size: 0,
			manager_diaphragm_porosity: 0,
			manager_electrolyte_workmanship: 0,
			manager_coiling_needle: 0,
			manager_coiling: 0,
			manager_diaphragm_overhang: 0,
			manager_coiling_coiling: 0,
			manager_structural: 0,
			manager_structural_shell: 0,
			manager_structural_positive_rivet1: 0,
			manager_structural_positive_terminal_insulating: 0,
			dataSource: [],
			batteryName: "",
			batteryId: null,

			loading: false,

			densityPositiveSource: [],
			capacityDesignSource: [],
			sizeAndOtherSource: [],
			summaryAnalysisSource: [],
			densityCheckSource: [],
			densityPositiveColumns: [
				{
					title: "密度分析（手动输入定义）",
					colSpan: 4,
					dataIndex: "name1",
					align: "center",
					scopedSlots: { customRender: "name1" },
					customCell: (record, index) => {
						const obj = {
							children: record.name1,
							attrs: {},
							style: {}
						}
						obj.attrs.rowspan = 1
						if (record.code == "first") {
							obj.attrs.rowspan = 2
							obj.style.background = "#efefef"
							obj.style.background = "rgba(245, 245, 245,.8)"
						}
						if (record.code == "second") {
							obj.style.display = "none"
						}
						return obj
					}
				},
				{
					colSpan: 0,
					dataIndex: "value1",
					align: "center",
					scopedSlots: { customRender: "value1" }
				},
				{
					colSpan: 0,
					dataIndex: "value2",
					align: "center",
					scopedSlots: { customRender: "value2" }
				},
				{
					colSpan: 0,
					dataIndex: "value3",
					align: "center",
					scopedSlots: { customRender: "value3" }
				}
			],
			updateBasic: false,
			densityCheckColumns: [
				{
					title: "密度查询手册",
					colSpan: 2,
					dataIndex: "name1",
					align: "center",
					scopedSlots: { customRender: "name1" }
				},
				{
					colSpan: 0,
					dataIndex: "value1",
					align: "center",
					scopedSlots: { customRender: "value1" }
				}
			],
			summaryAnalysisColumns: [
				{
					title: "组成",
					dataIndex: "name1",
					align: "center",
					ellipsis: true,
					width: 165,
					scopedSlots: { customRender: "name1" }
				},
				{
					title: "单位",
					dataIndex: "unit",
					align: "center",
					width: 70,
					ellipsis: true,
					scopedSlots: { customRender: "unit" }
				},
				{
					title: "电芯",
					dataIndex: "value1",
					align: "center",
					width: 80,
					ellipsis: true,
					scopedSlots: { customRender: "value1" }
				},
				{
					title: "填写说明",
					width: 70,
					dataIndex: "remark",
					ellipsis: true,
					align: "center",
					scopedSlots: { customRender: "remark" }
				}
			],
			capacityDesignColumns: [
				{
					title: "项目",
					dataIndex: "name1",
					align: "center",
					ellipsis: true,
					scopedSlots: { customRender: "name1" }
				},
				{
					title: "单位",
					dataIndex: "unit",
					align: "center",
					ellipsis: true,
					scopedSlots: { customRender: "unit" }
				},
				{
					title: "设计信息",
					dataIndex: "value1",
					ellipsis: true,
					align: "center",
					scopedSlots: { customRender: "value1" }
				},
				/*{
                  title: '设计标准',
                  dataIndex: 'value2',
                  align: 'center',
              ellipsis:true,
                  scopedSlots: { customRender: 'value2' },
                },{
                  title: '状态识别',
                  dataIndex: 'checkStatus',
                  align: 'center',
              ellipsis:true,
                  scopedSlots: { customRender: 'checkStatus' },
                },*/ {
					title: "填写说明",
					dataIndex: "remark",
					ellipsis: true,
					align: "center",
					scopedSlots: { customRender: "remark" }
				}
			],
			sizeAndOtherColumns: [
				{
					title: "分析内容",
					dataIndex: "name1",
					align: "center",
					ellipsis: true,
					width: 70,
					//scopedSlots: { customRender: 'name1' },
					customRender: (text, record, index) => {
						const obj = {
							children: <div class="divcls">{text}</div>,
							attrs: {}
						}
						obj.attrs.rowSpan = 0
						if (record.code == "other_size_and_other_k1") {
							obj.attrs.rowSpan = 5
						}
						if (record.code == "other_size_and_other_l22") {
							obj.attrs.rowSpan = 2
						}
						if (record.code == "other_size_and_other_z31") {
							obj.attrs.rowSpan = 4
						}
						if (record.code == "other_size_and_other_x42") {
							obj.attrs.rowSpan = 4
						}
						return obj
					}
				},
				{
					title: "组成",
					dataIndex: "name2",
					align: "center",

					width: 140,
					ellipsis: true,
					scopedSlots: { customRender: "name2" }
				},
				{
					title: "单位",
					dataIndex: "unit",
					align: "center",
					width: 70,

					ellipsis: true,
					scopedSlots: { customRender: "unit" }
				},
				{
					title: "正极涂层",
					dataIndex: "value1",
					align: "center",
					width: 70,
					ellipsis: true,
					scopedSlots: { customRender: "value1" }
				},
				{
					title: "Al箔",
					dataIndex: "value2",
					align: "center",
					ellipsis: true,
					width: 70,
					scopedSlots: { customRender: "value2" }
				},
				{
					title: "负极涂层",
					dataIndex: "value3",
					align: "center",
					ellipsis: true,
					width: 70,
					scopedSlots: { customRender: "value3" }
				},
				{
					title: "Cu箔",
					dataIndex: "value4",
					align: "center",
					ellipsis: true,
					width: 70,
					scopedSlots: { customRender: "value4" },
					customHeaderCell: () => ({
						style: {
							// background: "rgba(0, 73, 176, 0.7)",
							background: "rgba(245, 245, 245,.8)",
							borderBottom: "1px solid #d2d4d7"
						}
					})
				},
				{
					title: "隔膜",
					dataIndex: "value5",
					align: "center",
					width: 70,
					ellipsis: true,
					scopedSlots: { customRender: "value5" }
				},
				{
					title: "收尾胶",
					dataIndex: "value6",
					align: "center",
					width: 70,
					ellipsis: true,
					scopedSlots: { customRender: "value6" }
				},
				{
					title: "PI绝缘胶",
					dataIndex: "value7",
					align: "center",
					width: 70,
					ellipsis: true,
					scopedSlots: { customRender: "value7" }
				},
				{
					title: "电解液",
					dataIndex: "value8",
					align: "center",
					width: 70,
					ellipsis: true,
					scopedSlots: { customRender: "value8" }
				},
				{
					title: "结构件总重量",
					dataIndex: "value9",
					align: "center",
					width: 90,
					ellipsis: true,
					scopedSlots: { customRender: "value9" }
				}
			],

			height: document.documentElement.clientHeight,

			// 表头
			columns: [
				{
					title: "序号",
					dataIndex: "index",
					align: "center",
					width: 30,
					customRender: (text, record, index) => <div class="divcls div_border_right div_btns">{index + 1}</div>
				},
				{
					title: "项目",
					dataIndex: "projectCategory",
					colSpan: 3,
					align: "center",
					width: 30,
					customRender: (text, record, index) => {
						const obj = {
							children: <div class="divcls div_border_right">{text}</div>,
							attrs: {}
						}
						obj.attrs.rowSpan = 1
						if (record.code == "manager_project_customer") {
							obj.attrs.rowSpan = this.manager_project
							obj.attrs.colSpan = 2
						} else if (record.code == "manager_performance_fast_charging_time") {
							obj.attrs.rowSpan = this.manager_performance
							obj.attrs.colSpan = 2
						} else if (record.code == "manager_size_core_diameter") {
							obj.attrs.rowSpan = this.manager_size
							obj.attrs.colSpan = 2
						} else if (record.code == "manager_positive_gram_capacity_active") {
							obj.attrs.rowSpan = this.manager_tx
						} else if (record.code == "manager_positive_gram_capacity_coulomb") {
							obj.attrs.rowSpan = this.manager_positive
						} else if (record.code == "manager_negative_gram_capacity_active") {
							obj.attrs.rowSpan = this.manager_negative
						} else if (record.code == "manager_electrolyte_component_solvent") {
							obj.attrs.rowSpan = this.manager_electrolyte
						} else if (record.code == "manager_diaphragm_coating_base") {
							obj.attrs.rowSpan = this.manager_diaphragm
						} else if (record.code == "manager_coiling_overhang_negative_envelope_positive") {
							obj.attrs.rowSpan = this.manager_coiling
						} else if (record.code == "manager_structural_f10_f10") {
							obj.attrs.rowSpan = this.manager_structural
						} else if (
							record.code.startsWith("manager_project") ||
							record.code.startsWith("manager_performance") ||
							record.code.startsWith("manager_negative") ||
							record.code.startsWith("manager_diaphragm") ||
							record.code.startsWith("manager_electrolyte") ||
							record.code.startsWith("manager_structural") ||
							record.code.startsWith("manager_size") ||
							record.code.startsWith("manager_coiling") ||
							record.code.startsWith("manager_positive")
						) {
							obj.attrs.rowSpan = 0
						}

						return obj
					}
				},
				{
					title: "项目",
					dataIndex: "projectCategory2",
					colSpan: 0,
					width: 57,
					align: "center",

					customRender: (text, record, index) => {
						const obj = {
							children: <div class="divcls div_border_right">{text}</div>,
							attrs: {}
						}
						obj.attrs.colSpan = 1
						if (record.code.startsWith("manager_project")) {
							obj.attrs.colSpan = 0
						}
						if (record.code.startsWith("manager_size")) {
							obj.attrs.colSpan = 0
						}
						if (record.code.startsWith("manager_performance")) {
							obj.attrs.colSpan = 0
						}
						obj.attrs.rowSpan = 1
						if (record.code == "manager_positive_gram_capacity_active") {
							obj.attrs.rowSpan = this.manager_tx
						} else if (record.code == "manager_positive_formula_active") {
							obj.attrs.rowSpan = this.manager_positive_formula
						} else
            if(record.code == 'manager_positive_gram_capacity_coulomb'){
              obj.attrs.rowSpan = this.manager_positive_gram_capacity
            }else if (record.code == "manager_positive_size_al_foil_model") {
							obj.attrs.rowSpan = this.manager_positive_size
						} else if (record.code == "manager_positive_overhang_al_p1") {
							obj.attrs.rowSpan = this.manager_positive_overhang
						} else if (record.code == "manager_diaphragm_overhang_positive") {
							obj.attrs.rowSpan = this.manager_diaphragm_overhang
						} else if (record.code == "manager_negative_overhang_cu_n1") {
							obj.attrs.rowSpan = this.manager_negative_overhang
						} else if (record.code == "manager_positive_workmanship_areal_density_a") {
							obj.attrs.rowSpan = this.manager_positive_workmanship
						} else if (record.code == "manager_negative_gram_capacity_active") {
							obj.attrs.rowSpan = this.manager_negative_gram_capacity
						} else if (record.code == "manager_electrolyte_component_solvent") {
							obj.attrs.rowSpan = this.manager_electrolyte_component
						} else if (record.code == "manager_negative_formula_active") {
							obj.attrs.rowSpan = this.manager_negative_formula
						} else if (record.code == "manager_negative_size_cu_foil_model") {
							obj.attrs.rowSpan = this.manager_negative_size
						} else if (record.code == "manager_negative_workmanship_areal_density_a") {
							obj.attrs.rowSpan = this.manager_negative_workmanship
						} else if (record.code == "manager_diaphragm_size_total_thickness") {
							obj.attrs.rowSpan = this.manager_diaphragm_size
						} else if (record.code == "manager_diaphragm_porosity_porosity") {
							obj.attrs.rowSpan = this.manager_diaphragm_porosity
						} else if (record.code == "manager_electrolyte_workmanship_liquid_injection_volume_per") {
							obj.attrs.rowSpan = this.manager_electrolyte_workmanship
						} else if (record.code == "manager_coiling_needle_needle_diameter") {
							obj.attrs.rowSpan = this.manager_coiling_needle
						} else if (record.code == "manager_coiling_coiling_coiling_diameter") {
							obj.attrs.rowSpan = this.manager_coiling_coiling
						} else if (record.code == "manager_coiling_rolling_pressing_positive") {
							obj.attrs.rowSpan = this.manager_coiling_rolling_pressing
						} else if (record.code == "manager_coiling_core_size_positive_terminal_foil") {
							obj.attrs.rowSpan = this.manager_coiling_core_size
						} else if (record.code == "manager_diaphragm_coating_base") {
							obj.attrs.rowSpan = this.manager_diaphragm_coating
						} else if (record.code == "manager_structural_shell_material") {
							obj.attrs.rowSpan = this.manager_structural_shell
						} else if (record.code == "manager_structural_positive_rivet1_material") {
							obj.attrs.rowSpan = this.manager_structural_positive_rivet1
						} else if (record.code == "manager_structural_positive_terminal_insulating_material") {
							obj.attrs.rowSpan = this.manager_structural_positive_terminal_insulating
						} else if (record.code == "manager_coiling_overhang_negative_envelope_positive") {
							obj.attrs.rowSpan = this.manager_coiling_overhang
						} else if (record.code == "manager_structural_positive_terminal1_material") {
							obj.attrs.rowSpan = this.manager_structural_positive_terminal
						} else if (record.code == "manager_structural_negative_terminal1_material") {
							obj.attrs.rowSpan = this.manager_structural_negative_terminal
						} else if (record.code == "manager_structural_negative_rivet1_material") {
							obj.attrs.rowSpan = this.manager_structural_negative_rivet1
						} else if (record.code == "manager_structural_cover_material") {
							obj.attrs.rowSpan = this.manager_structural_cover
						} else if (record.code == "manager_structural_positive_bus_plate1_material") {
							obj.attrs.rowSpan = this.manager_structural_positive_bus_plate1
						} else if (record.code == "manager_structural_positive_rivet_insulating_material") {
							obj.attrs.rowSpan = this.manager_structural_positive_rivet_insulating
						} else if (record.code == "manager_structural_negative_terminal_insulating_material") {
							obj.attrs.rowSpan = this.manager_structural_negative_terminal_insulating
						} else if (record.code == "manager_structural_negative_rivet_insulating_material") {
							obj.attrs.rowSpan = this.manager_structural_negative_rivet_insulating
						} else if (record.code == "manager_structural_positive_bus_plate_insulating_material") {
							obj.attrs.rowSpan = this.manager_structural_positive_bus_plate_insulating
						} else if (record.code == "manager_structural_negative_bus_plate_insulating_material") {
							obj.attrs.rowSpan = this.manager_structural_negative_bus_plate_insulating
						} else if (record.code == "manager_structural_jr_insulating_material") {
							obj.attrs.rowSpan = this.manager_structural_jr_insulating
						} else if (record.code == "manager_structural_insulating_material") {
							obj.attrs.rowSpan = this.manager_structural_insulating
						} else if (record.code == "manager_structural_other_weight") {
							obj.attrs.rowSpan = this.manager_structural_other
						} else if (record.code == "manager_structural_total_weight") {
							obj.attrs.rowSpan = this.manager_structural_total
						} else if (record.code == "manager_structural_negative_bus_plate1_material") {
							obj.attrs.rowSpan = this.manager_structural_negative_bus_plate1
						} else if (record.code == "manager_structural_vent_material") {
							obj.attrs.rowSpan = this.manager_structural_vent
						} else if (
							record.code.startsWith("manager_positive_gram_capacity") ||
							record.code.startsWith("manager_positive_formula") ||
							record.code.startsWith("manager_structural_vent") ||
							record.code.startsWith("manager_structural_positive_bus_plate1") ||
							record.code.startsWith("manager_structural_positive_rivet_insulating") ||
							record.code.startsWith("manager_structural_negative_terminal_insulating") ||
							record.code.startsWith("manager_structural_negative_bus_plate1") ||
							record.code.startsWith("manager_structural_negative_rivet_insulating") ||
							record.code.startsWith("manager_electrolyte_component") ||
							record.code.startsWith("manager_positive_workmanship") ||
							record.code.startsWith("manager_negative_gram_capacity") ||
							record.code.startsWith("manager_coiling_rolling_pressing") ||
							record.code.startsWith("manager_electrolyte_workmanship") ||
							record.code.startsWith("manager_positive_workmanship") ||
							record.code.startsWith("manager_negative_formula") ||
							record.code.startsWith("manager_negative_size") ||
							record.code.startsWith("manager_negative_workmanship") ||
							record.code.startsWith("manager_structural_insulating") ||
							record.code.startsWith("manager_structural_jr_insulating") ||
							record.code.startsWith("manager_structural_positive_bus_plate_insulating") ||
							record.code.startsWith("manager_structural_negative_bus_plate_insulating") ||
							record.code.startsWith("manager_coiling_core_size") ||
							record.code.startsWith("manager_diaphragm_coating") ||
							record.code.startsWith("manager_structural_positive_terminal_insulating") ||
							record.code.startsWith("manager_electrolyte_workmanship_liquid_injection_volume") ||
							record.code.startsWith("manager_diaphragm_size") ||
							record.code.startsWith("manager_diaphragm_porosity") ||
							record.code.startsWith("manager_coiling_needle") ||
							record.code.startsWith("manager_coiling_overhang") ||
							record.code.startsWith("manager_structural_negative_rivet1") ||
							record.code.startsWith("manager_structural_positive_terminal1") ||
							record.code.startsWith("manager_structural_negative_terminal1") ||
							record.code.startsWith("manager_coiling_coiling") ||
							record.code.startsWith("manager_structural_shell") ||
							record.code.startsWith("manager_structural_cover") ||
							record.code.startsWith("manager_structural_positive_rivet1") ||
							record.code.startsWith("manager_negative_overhang") ||
							record.code.startsWith("manager_positive_overhang") ||
							record.code.startsWith("manager_diaphragm_overhang") ||
							record.code.startsWith("manager_positive_type") ||
							record.code.startsWith("manager_positive_size")
						) {
							obj.attrs.rowSpan = 0
						}
						return obj
					}
				},
				{
					title: "项目",
					dataIndex: "projectName",
					colSpan: 0,
					width: 180,
					ellipsis: true,
					scopedSlots: { customRender: "divcls" }
				},
				{
					title: "单位",
					dataIndex: "unit",
					width: 68,
					align: "center",
					scopedSlots: { customRender: "unitcls" }
				},
				{
					title: "设计信息",
					dataIndex: "design",
					align: "center",
					width: 65,
					scopedSlots: { customRender: "design" }
				},
				{
					title: "备注信息",
					dataIndex: "eve",
					align: "center",
					width: 65,
					scopedSlots: { customRender: "eve" }
				},

				{
					title: "状态识别",
					dataIndex: "checkStatus",
					align: "center",
					width: 60,
					scopedSlots: { customRender: "checkStatus" }
				},
				{
					width: 25,
					customRender: (text, record, index) => <div class="divcls reset_border_bottom"></div>
				},
				{
					title: "标准值",
					dataIndex: "reference",
					align: "center",
					width: 75,
					//customRender: (text, record, index) => (<div class='divcls div_border_right'>{text}</div>)
					scopedSlots: { customRender: "reference" }
				},
				{
					title: "范围区间",
					dataIndex: "allowableTolerance",
					align: "center",
					width: 84,
					//customRender: (text, record, index) => (<div class='divcls div_border_right'>{text}</div>)
					scopedSlots: { customRender: "allowableTolerance" }
				},
				{
					title: "填写说明",
					dataIndex: "remark",
					align: "center",
					width: 70,
					//customRender: (text, record, index) => (<div class='divcls div_border_right'>{text}</div>)
					scopedSlots: { customRender: "remark" }
				}
			]
		}
	},
	props: {
		inBatteryId: {
			type: String,
			default: ""
		}
	},
  watch:{
    inBatteryId(newVal,oldVal){
      if(null != newVal){
        getBatteryDesign({ inBatteryId: newVal, type: "design" }).then(res => {
          this.design = res.data
          switch (res.data.structureType) {
            case "g_cylinder":
              this.structureType1 = "G圆柱"
              break
            case "c_cylinder":
              this.structureType1 = "C圆柱"
              break
            case "v_cylinder":
              this.structureType1 = "V圆柱"
              break
            case "winding":
              this.structureType1 = "方形卷绕"
              break
            case "lamination":
              this.structureType1 = "方形叠片"
              break
            case "soft_roll":
              this.structureType1 = "软包"
              break
          }
        }).then(() => {
          this.batteryId = this.inBatteryId

          this.getList(true)

          this.myChart0 = this.echarts.init(this.$refs.chart0, "shine")
          this.myChart1 = this.echarts.init(this.$refs.chart1, "shine")
          this.myChart2 = this.echarts.init(this.$refs.chart2, "shine")
          this.myChart3 = this.echarts.init(this.$refs.chart3, "shine")
        })
      }
    }
  },
	created() {

	},
	mounted() {

	},
	computed: {
		...mapGetters(["userInfo"])
	},
	methods: {
		...mapActions(["MenuChange"]),
		edit1(id) {
			const newData = [...this.dataSource]
			const target = newData.find(item => id === item.id)
			this.editingKey = id
			if (target) {
				target.editable = true
				this.dataSource = newData
			}
		},
		edit2(id) {
			const newData = [...this.capacityDesignSource]
			const target = newData.find(item => id === item.id)
			this.editingKey = id
			if (target) {
				target.editable = true
				this.capacityDesignSource = newData
			}
		},

		openLeft() {
			if (this.showLeft) {
				document.getElementById("right").style.width = "62%"
				document.getElementById("densityCheckColumns").style.display = "unset"
				document.getElementById("chart0").style.width = "49%"
				document.getElementById("chart1").style.width = "49%"
				document.getElementById("redius").style.width = "49%"
				document.getElementById("redius1").style.width = "49%"
				document.getElementById("chart34").style.width = "100%"
				document.getElementById("chart34").style.float = "left"
				document.getElementById("densityPositiveColumns").style.display = "unset"

				// 获取具有 "blank" 类的元素
				let elements = document.getElementsByClassName("blank")
				for (let i = 0; i < elements.length; i++) {
					elements[i].parentNode.parentNode.style.borderRight = "0px"
					elements[i].parentNode.parentNode.style.borderLeft = "0px"
				}
			} else {
				document.getElementById("right").style.width = "30%"
				document.getElementById("chart0").style.width = "100%"
				document.getElementById("chart1").style.width = "100%"
				document.getElementById("redius").style.width = "100%"
				document.getElementById("redius1").style.width = "100%"
				document.getElementById("chart34").style.width = "100%"
				document.getElementById("chart34").style.float = "unset"
				document.getElementById("densityCheckColumns").style.display = "none"
				document.getElementById("densityPositiveColumns").style.display = "none"
			}

			this.showLeft = !this.showLeft
			this.init()
		},

		switchApp() {
			const applicationData = Vue.ls.get(ALL_APPS_MENU)
			this.MenuChange(applicationData[0])
				.then(res => {})
				.catch(err => {
					this.$message.error("错误提示：" + err.message, 1)
				})
		},

		gotoBom() {
			//this.switchApp()
			this.$router.push({
				path: "/sys_battery_design_bom",
				query: {
					batteryId: this.batteryId
				}
			})
		},

		onClose3() {
			this.visible3 = false
		},
		gotoSor() {
			//this.switchApp()
			this.$router.push({
				path: "/system_battery_design_sor",
				query: {
					batteryId: this.batteryId
				}
			})
		},
		gotoMi() {
			//this.switchApp()
			this.$router.push({
				path: "/g_cylinder_mi_standard_manage",
				query: {
					batteryId: this.batteryId
				}
			})
		},
		getByClass(parent, cls) {
			if (parent.getElementsByClassName) {
				return Array.from(parent.getElementsByClassName(cls))
			} else {
				var res = []
				var reg = new RegExp(" " + cls + " ", "i")
				var ele = parent.getElementsByTagName("*")
				for (var i = 0; i < ele.length; i++) {
					if (reg.test(" " + ele[i].className + " ")) {
						res.push(ele[i])
					}
				}
				return res
			}
		},
		init() {
			this.$nextTick(() => {
				let items = this.getByClass(document, "divcls")
				for (const e of items) {
					var _e = e.parentNode
					_e.classList.add("tdcls")
					if (e.classList.contains("div_border_right")) {
						_e.classList.add("td_border_right")
					}
					if (e.classList.contains("reset_border_bottom")) {
						_e.classList.add("td_border_botter_rest")
					}
				}

				let $items = this.getByClass(document, "ant-layout")
				for (const e of $items) {
					e.setAttribute("style", "min-height:initial")
				}
			})
		},

		numberMul(arg1, arg2) {
			if (arg1 == undefined) {
				return ""
			}
			var m = 0
			var s1 = arg1.toString()
			var s2 = arg2.toString()
			try {
				m += s1.split(".")[1].length
			} catch (e) {}
			try {
				m += s2.split(".")[1].length
			} catch (e) {}
			return (Number(s1.replace(".", "")) * Number(s2.replace(".", ""))) / Math.pow(10, m)
		},

		updateData(event, record, column, type) {
			let value = event.target.value

			if ("percent" == type) {
				if (!event.target.value.match(/^(100|[1-9]?\d(\.\d\d?\d?)?)%$|0$/)) {
					this.$message.warn("请输入百分比")
					this.getList(false)
					return
				} else {
					value = numberMul(event.target.value.slice(0, event.target.value.length - 1), 0.01)
				}
			}

			record[column] = value

			//修改时禁止输入

			let inputs = document.getElementsByTagName("input")
			let controlInput = []

			for (let i = 0; i < inputs.length; i++) {
				if (!inputs[i].disabled) {
					controlInput.push(inputs[i])
				}
			}

			for (let i = 0; i < controlInput.length; i++) {
				controlInput[i].disabled = true
			}

			let param = {}
			param[column] = value
			param["id"] = record.id
			update(param).then(res => {
				//this.loading = true
				this.getList(false)
				this.$nextTick(() => {
					if (res.success) {
						this.$message.success("保存成功")
					} else {
						this.$message.error(res.message)
					}
					setTimeout(() => {
						for (let i = 0; i < controlInput.length; i++) {
							controlInput[i].disabled = false
						}
					}, 200)
				})
			})
		},
		gotoDevelop(record) {
			//this.switchApp()
			this.$router.push({
				path: "/batterydesign"
			})
		},
		gotoDesign(record) {
			//this.switchApp()
			this.$router.push({
				path: "/battery_design_manager",
				query: {
					batteryId: this.design.id
				}
			})
		},
		updateDataOnlyRange(event, record, column, type, dataSequence) {
			let value = event.target.value

			if ("percent" == type) {
				if (!event.target.value.match(/^(100|[1-9]?\d(\.\d\d?\d?)?)%$|0$/) || !event.target.value.valueOf("%") == -1) {
					this.$message.warn("请输入百分比")
					this.getList(false)
					return
				} else {
					value = numberMul(event.target.value.slice(0, event.target.value.length - 1), 0.01)
				}
			}

			let source = record.allowableTolerance
			let updateSource = ""
			if ("before" == dataSequence) {
				if (null == source) {
					updateSource += value + "~"
				} else {
					let split = source.split("~")
					if (split.length > 1) {
						updateSource += value + "~" + source.split("~")[1]
					} else {
						updateSource += value + "~"
					}
				}
			} else {
				if (null == source) {
					updateSource += "~" + value
				} else {
					updateSource += source.split("~")[0] + "~" + value
				}
			}

			record[column] = updateSource

			//修改时禁止输入

			let inputs = document.getElementsByTagName("input")
			let controlInput = []

			for (let i = 0; i < inputs.length; i++) {
				if (!inputs[i].disabled) {
					controlInput.push(inputs[i])
				}
			}

			for (let i = 0; i < controlInput.length; i++) {
				controlInput[i].disabled = true
			}

			let param = {}
			param[column] = updateSource
			param["id"] = record.id
			update(param).then(res => {
				//this.loading = true
				this.getList(false)
				this.$nextTick(() => {
					if (res.success) {
						this.$message.success("保存成功")
					} else {
						this.$message.error(res.message)
					}
					setTimeout(() => {
						for (let i = 0; i < controlInput.length; i++) {
							controlInput[i].disabled = false
						}
					}, 200)
				})
			})
		},

		exportDataMethod() {
			this.spinning = true
			exportExcel({ batteryId: this.batteryId }).then(res => {
				this.spinning = false
				this.visible3 = true
				this.pdfUrl = process.env.VUE_APP_API_BASE_URL + "/sysFileInfo/previewPdf?id=" + res.data
			})
		},
    checkRangeStatus(record){

      if(record.dataType == null || !record.dataType.startsWith('range')){
        return 4
      }
      let value = record.design
      if(null == value || '' == value){
        return 4
      }

      let range = record.allowableTolerance

      let list = ['manager_positive_formula_conductive_content','manager_positive_formula_binder_content','manager_negative_formula_conductive_content','manager_negative_formula_binder_content']

      if(record.dataType == 'range_add'){
        for (let j = 0; j < list.length; j++) {
          if(record.code.startsWith(list[j])){
            let positive = this.dataSource.filter(d => d.code.startsWith(list[j]))
            let design = 0
            for (let i = 0; i < positive.length; i++) {
              if(positive[i].design != null && positive[i].design != ''){
                design += parseFloat(positive[i].design)
              }
            }
            value = design
            range = this.dataSource.find(d => d.code.startsWith(list[j]+'1')).allowableTolerance
            break;
          }
        }


      }



      if(null == range || '' == range){
        return 4
      }

      if(record.dataType == 'range_up_down'){
        if(record.reference == null || record.reference == ''){
          return 4
        }
        let before = range.substring(0,range.indexOf('~'))
        let after = range.substring(range.indexOf('~') + 1)
        range = ((before == null || before == '')?'':(parseFloat(record.design) - parseFloat(before))) + '~' + ((after == null || after == '')?'':(parseFloat(record.design) + parseFloat(after)))
        value = record.reference
      }

      let before = range.substring(0,range.indexOf('~'))
      let after = range.substring(range.indexOf('~') + 1)

      if(before == null || before == ''){
        if(after != null && after != ''){
          if(parseFloat(value) <= parseFloat(after)){
            return 1
          }else{
            return 2
          }
        }
      }else{
        if(after == null && after == ''){
          if(parseFloat(value) >= parseFloat(before)){
            return 1
          }else{
            return 2
          }
        }else{
          if(parseFloat(value) >= parseFloat(before) && parseFloat(value) <= parseFloat(after)){
            return 1
          }else{
            return 2
          }
        }
      }
      return 4

    },
		exportDataMethod1() {
			this.spinning = true
			exportExcel1({ batteryId: this.batteryId }).then(res => {
				this.spinning = false
				const fileName = "方案设计导出表.xlsx"

				if (!res) return
				const blob = new Blob([res.data], { type: "application/vnd.ms-excel" }) // 构造一个blob对象来处理数据，并设置文件类型

				if (window.navigator.msSaveOrOpenBlob) {
					//兼容IE10
					navigator.msSaveBlob(blob, fileName)
				} else {
					const href = URL.createObjectURL(blob) //创建新的URL表示指定的blob对象
					const a = document.createElement("a") //创建a标签
					a.style.display = "none"
					a.href = href // 指定下载链接
					a.download = fileName //指定下载文件名
					a.click() //触发下载
					URL.revokeObjectURL(a.href) //释放URL对象
				}
			})
		},
		updateOtherData(event, record, column, type, isGt0) {
			let value = event.target.value

			if ("percent" == type) {
				if (!event.target.value.match(/^(100|[1-9]?\d(\.\d\d?\d?)?)%$|0$/)) {
					this.$message.warn("请输入百分比")
					this.getList(false)
					return
				} else {
					value = numberMul(event.target.value.slice(0, event.target.value.length - 1), 0.01)
				}
			}

			if (isGt0) {
				if (value <= 0) {
					this.$message.warn("请输入大于0的数")
					this.getList(false)
					return
				}
			}

			record[column] = value
			//修改时禁止输入
			let inputs = document.getElementsByTagName("input")
			let controlInput = []

			for (let i = 0; i < inputs.length; i++) {
				if (!inputs[i].disabled) {
					controlInput.push(inputs[i])
				}
			}

			for (let i = 0; i < controlInput.length; i++) {
				controlInput[i].disabled = true
			}

			let param = {}
			param[column] = value
			param["id"] = record.id
			otherUpdate(param).then(res => {
				//this.loading = true
				this.getList(false)
				this.$nextTick(() => {
					if (res.success) {
						this.$message.success("保存成功")
					} else {
						this.$message.error(res.message)
					}

					setTimeout(() => {
						for (let i = 0; i < controlInput.length; i++) {
							controlInput[i].disabled = false
						}
					}, 200)
				})
			})
		},

		updateSelectData(value, record) {
			record["checkStatus"] = value
			let param = {}
			param["checkStatus"] = value
			param["id"] = record.id
			update(param).then(res => {
				//this.loading = true
				this.getList(false)
				this.$nextTick(() => {
					if (res.success) {
						this.$message.success("保存成功")
					} else {
						this.$message.error(res.message)
					}
				})
			})
		},
		updateSelectData2(value, record) {
			record["design"] = value
			let param = {}
			param["design"] = value
			param["id"] = record.id
			update(param)
				.then(res => {
					//this.loading = true
					//this.getList(false)
					this.$nextTick(() => {
						if (res.success) {
							this.$message.success("保存成功")
						} else {
							this.$message.error(res.message)
						}
					})
				})
				.finally(res => this.getList())
		},

		updateOtherSelectData(value, record) {
			record["checkStatus"] = value
			let param = {}
			param["checkStatus"] = value
			param["id"] = record.id
			otherUpdate(param).then(res => {
				//this.loading = true
				this.getList(false)
				this.$nextTick(() => {
					if (res.success) {
						this.$message.success("保存成功")
					} else {
						this.$message.error(res.message)
					}
				})
			})
		},

		numberHandle(number, n) {
      if(number == 0){
        return '0.00'
      }
			if (null == number) {
				return ""
			}
			n = n ? parseInt(n) : 0
			if (n <= 0) {
				return Math.round(number)
			}
			number = Math.round(number * Math.pow(10, n)) / Math.pow(10, n) //四舍五入
			number = Number(number).toFixed(n) //补足位数
			return number
		},

		getList(update) {
			list({ batteryId: this.batteryId })
				.then(res => {
					this.dataSource = res.data
					this.batteryName = res.data[0].batteryName
					if (update) {
						for (let i = 0; i < res.data.length; i++) {
							if (res.data[i].code.startsWith("manager_project")) {
								this.manager_project++
							}
							if (res.data[i].code.startsWith("manager_performance")) {
								this.manager_performance++
							}
							if (res.data[i].code.startsWith("manager_size")) {
								this.manager_size++
							}
							if (res.data[i].projectCategory.startsWith("体系")) {
								this.manager_tx++
							}
							if (res.data[i].code.startsWith("manager_positive") && res.data[i].projectCategory != '体系') {
								this.manager_positive++
							}
							if (res.data[i].code.startsWith("manager_positive_gram_capacity") && res.data[i].projectCategory != '体系') {
								this.manager_positive_gram_capacity++
							}
							if (res.data[i].code.startsWith("manager_positive_formula")) {
								this.manager_positive_formula++
							}
							if (res.data[i].code.startsWith("manager_positive_size")) {
								this.manager_positive_size++
							}
							if (res.data[i].code.startsWith("manager_negative_overhang")) {
								this.manager_negative_overhang++
							}
							if (res.data[i].code.startsWith("manager_positive_overhang")) {
								this.manager_positive_overhang++
							}
							if (res.data[i].code.startsWith("manager_positive_workmanship")) {
								this.manager_positive_workmanship++
							}
							if (res.data[i].code.startsWith("manager_negative")) {
								this.manager_negative++
							}
							if (res.data[i].code.startsWith("manager_electrolyte")) {
								this.manager_electrolyte++
							}
							if (res.data[i].code.startsWith("manager_electrolyte_component")) {
								this.manager_electrolyte_component++
							}
							if (res.data[i].code.startsWith("manager_diaphragm")) {
								this.manager_diaphragm++
							}

							if (res.data[i].code.startsWith("manager_negative_gram_capacity")) {
								this.manager_negative_gram_capacity++
							}
							if (res.data[i].code.startsWith("manager_negative_formula")) {
								this.manager_negative_formula++
							}
							if (res.data[i].code.startsWith("manager_negative_size")) {
								this.manager_negative_size++
							}
							if (res.data[i].code.startsWith("manager_negative_workmanship")) {
								this.manager_negative_workmanship++
							}
							if (res.data[i].code.startsWith("manager_diaphragm_coating")) {
								this.manager_diaphragm_coating++
							}
							if (res.data[i].code.startsWith("manager_coiling_core_size")) {
								this.manager_coiling_core_size++
							}
							if (res.data[i].code.startsWith("manager_coiling_overhang")) {
								this.manager_coiling_overhang++
							}
							if (res.data[i].code.startsWith("manager_structural_positive_terminal1")) {
								this.manager_structural_positive_terminal++
							}
							if (res.data[i].code.startsWith("manager_structural_negative_terminal1")) {
								this.manager_structural_negative_terminal++
							}
							if (res.data[i].code.startsWith("manager_structural_negative_rivet1")) {
								this.manager_structural_negative_rivet1++
							}
							if (res.data[i].code.startsWith("manager_structural_cover")) {
								this.manager_structural_cover++
							}
							if (res.data[i].code.startsWith("manager_structural_positive_bus_plate1")) {
								this.manager_structural_positive_bus_plate1++
							}
							if (res.data[i].code.startsWith("manager_structural_positive_rivet_insulating")) {
								this.manager_structural_positive_rivet_insulating++
							}
							if (res.data[i].code.startsWith("manager_structural_negative_terminal_insulating")) {
								this.manager_structural_negative_terminal_insulating++
							}
							if (res.data[i].code.startsWith("manager_structural_negative_rivet_insulating")) {
								this.manager_structural_negative_rivet_insulating++
							}
							if (res.data[i].code.startsWith("manager_structural_positive_bus_plate_insulating")) {
								this.manager_structural_positive_bus_plate_insulating++
							}
							if (res.data[i].code.startsWith("manager_structural_negative_bus_plate_insulating")) {
								this.manager_structural_negative_bus_plate_insulating++
							}
							if (res.data[i].code.startsWith("manager_structural_jr_insulating")) {
								this.manager_structural_jr_insulating++
							}
							if (res.data[i].code.startsWith("manager_structural_insulating")) {
								this.manager_structural_insulating++
							}
							if (res.data[i].code.startsWith("manager_structural_other")) {
								this.manager_structural_other++
							}
							if (res.data[i].code.startsWith("manager_structural_total")) {
								this.manager_structural_total++
							}
							if (res.data[i].code.startsWith("manager_structural_negative_bus_plate1")) {
								this.manager_structural_negative_bus_plate1++
							}
							if (res.data[i].code.startsWith("manager_structural_vent")) {
								this.manager_structural_vent++
							}
							if (res.data[i].code.startsWith("manager_coiling_rolling_pressing")) {
								this.manager_coiling_rolling_pressing++
							}
							if (res.data[i].code.startsWith("manager_diaphragm_size")) {
								this.manager_diaphragm_size++
							}
							if (res.data[i].code.startsWith("manager_diaphragm_porosity")) {
								this.manager_diaphragm_porosity++
							}
							if (res.data[i].code.startsWith("manager_electrolyte_workmanship")) {
								this.manager_electrolyte_workmanship++
							}
							if (res.data[i].code.startsWith("manager_coiling_needle")) {
								this.manager_coiling_needle++
							}
							if (res.data[i].code.startsWith("manager_coiling")) {
								this.manager_coiling++
							}
							if (res.data[i].code.startsWith("manager_coiling_coiling")) {
								this.manager_coiling_coiling++
							}
							if (res.data[i].code.startsWith("manager_structural")) {
								this.manager_structural++
							}
							if (res.data[i].code.startsWith("manager_structural_shell")) {
								this.manager_structural_shell++
							}
							if (res.data[i].code.startsWith("manager_diaphragm_overhang")) {
								this.manager_diaphragm_overhang++
							}
							if (res.data[i].code.startsWith("manager_structural_positive_rivet1")) {
								this.manager_structural_positive_rivet1++
							}
							if (res.data[i].code.startsWith("manager_structural_positive_terminal_insulating")) {
								this.manager_structural_positive_terminal_insulating++
							}
						}
					}
					this.init()
				})
				.then(() => {
					/*getBatteryDesign({ inBatteryId: this.batteryId, type: "design" }).then(res => {
						this.isOwn = res.data.isOwn
						this.design = res.data
						if (res.data.isOwn == 0 || res.data.manCheckStatus != 0) {
							let inputs = document.getElementsByTagName("input")
							let controlInput = []

							for (let i = 0; i < inputs.length; i++) {
								if (!inputs[i].disabled) {
									controlInput.push(inputs[i])
								}
							}

							for (let i = 0; i < controlInput.length; i++) {
								controlInput[i].disabled = true
							}
						}
					})*/
				})
				.finally(() => {
					this.loading = false
				})

			otherList({ batteryId: this.batteryId, type: "density_positive_negative" }).then(res => {
				this.densityPositiveSource = res.data
			})

			otherList({ batteryId: this.batteryId, type: "density_check" }).then(res => {
				this.densityCheckSource = [{ name1: "材料", value1: "真密度(kg/m3)", code: "unChance" }]
				this.densityCheckSource = this.densityCheckSource.concat(res.data)
			})

			otherList({ batteryId: this.batteryId, type: "summary_analysis" }).then(res => {
				this.summaryAnalysisSource = res.data

				let data = []

				for (let i = 0; i < res.data.length; i++) {
					if (
						"other_summary_analysis_j33" == res.data[i].code ||
						"other_summary_analysis_j36" == res.data[i].code ||
						"other_summary_analysis_j41" == res.data[i].code ||
						"other_summary_analysis_j39" == res.data[i].code
					) {
						data.push(res.data[i].value1)
					}
				}

				let option2 = {
					grid: {
						left: "5%",
						right: "2%",
						bottom: "1%",
						top: "10%",
						containLabel: true
					},
					xAxis: {
						type: "category",
						data: ["面积群裕度\n(反弹前)", "面积群裕度\n(反弹后)", "直径群裕度\n(反弹前)", "直径群裕度\n(反弹后)"],
						axisLabel: {
							textStyle: {
								color: "#2b2b2b",
								fontSize: 10
							}
						}
					},
					yAxis: {
						type: "value",
						min: 0.9, //取0为最小刻度
						//max: 1.25, //取100为最大刻度
						interval: 0.05,
						axisLabel: {
							formatter: function(value, index) {
								return numberMul(value, 100) + "%"
							}
						}
					},
					series: [
						{
							data: data,
							type: "bar",
							barWidth: "50%",
							label: {
								show: true,
								position: "top",
								formatter: (value, index) => {
									return this.numberHandle(numberMul(value.data, 100), 2) + "%"
								}
							}
						}
					]
				}
				this.myChart2.clear()
				this.myChart2.setOption(option2)
			})
			otherList({ batteryId: this.batteryId, type: "capacity_design" }).then(res => {
				this.capacityDesignSource = res.data

				let data = []

				for (let i = 0; i < res.data.length; i++) {
					if (
						"other_capacity_design_j25" == res.data[i].code ||
						"other_capacity_design_j28" == res.data[i].code ||
						"other_capacity_design_j33" == res.data[i].code
					) {
						data.push(this.numberHandle(res.data[i].value1, 2))
					}
				}

				let source = [["product", "容量(Ah)", "能量(Wh)", "能量密度(Wh/kg)"]]
				let average = ["average"]
				let min = ["min"]

				for (let i = 0; i < res.data.length; i++) {
					if (
						"other_capacity_design_j1" == res.data[i].code ||
						"other_capacity_design_j39" == res.data[i].code ||
						"other_capacity_design_j44" == res.data[i].code
					) {
						average.push(this.numberHandle(res.data[i].value1, 2))
					}
					if (
						"other_capacity_design_j4" == res.data[i].code ||
						"other_capacity_design_j41" == res.data[i].code ||
						"other_capacity_design_j47" == res.data[i].code
					) {
						min.push(this.numberHandle(res.data[i].value1, 2))
					}
				}
				source.push(average)
				source.push(min)

				let option0 = {
					grid: {
						left: "2%",
						right: "2%",
						bottom: "1%",
						top: "20%",
						containLabel: true
					},
					legend: {},
					tooltip: {},
					dataset: {
						source: source
					},
					xAxis: {
						type: "category",
						axisLabel: {
							textStyle: {
								color: "#2b2b2b"
							}
						}
					},
					yAxis: {},
					// Declare several bar series, each will be mapped
					// to a column of dataset.source by default.
					series: [
						{
							type: "bar",
							label: {
								show: true,
								position: "top"
							}
						},
						{
							type: "bar",
							label: {
								show: true,
								position: "top"
							}
						},
						{
							type: "bar",
							label: {
								show: true,
								position: "top"
							}
						}
					]
				}
				this.myChart0.clear()
				this.myChart0.setOption(option0)

				let option1 = {
					grid: {
						left: "2%",
						right: "2%",
						bottom: "1%",
						top: "10%",
						containLabel: true
					},
					xAxis: {
						type: "category",
						data: ["充电N/P比\n(扣电)", "放电N/P比\n(扣电)", "放电N/P比\n(倍率全电)"],
						axisLabel: {
							textStyle: {
								color: "#2b2b2b",
								fontSize: 10
							}
						}
					},
					yAxis: {
						type: "value",
						min: 0.9, //取0为最小刻度
						//max: 1.3, //取100为最大刻度
						interval: 0.05,
						axisLabel: {
							formatter: function(value, index) {
								return value.toFixed(2)
							}
						}
					},
					series: [
						{
							barWidth: "50%",
							data: data,
							type: "bar",
							backgroundStyle: {
								color: "rgba(180, 180, 180, 0.2)"
							},
							label: {
								show: true,
								position: "top"
							}
						}
					]
				}
				this.myChart1.clear()
				this.myChart1.setOption(option1)
			})

			otherList({ batteryId: this.batteryId, type: "size_and_other" }).then(res => {
				this.sizeAndOtherSource = res.data
				let data = []

				for (let i = 0; i < res.data.length; i++) {
					if ("other_size_and_other_x48" == res.data[i].code) {
						data.push({ value: this.numberHandle(res.data[i].value1, 2), name: "正极涂层" })
						data.push({ value: this.numberHandle(res.data[i].value2, 2), name: "Al箔" })
						data.push({ value: this.numberHandle(res.data[i].value3, 2), name: "负极涂层" })
						data.push({ value: this.numberHandle(res.data[i].value4, 2), name: "Cu箔" })
						data.push({ value: this.numberHandle(res.data[i].value5, 2), name: "隔膜" })
						data.push({ value: this.numberHandle(res.data[i].value6, 2), name: "收尾胶" })
						data.push({ value: this.numberHandle(res.data[i].value7, 2), name: "PI绝缘胶" })
						data.push({ value: this.numberHandle(res.data[i].value8, 2), name: "电解液" })
						data.push({ value: this.numberHandle(res.data[i].value9, 2), name: "结构件总重量" })
					}
				}

				let option3 = {
					tooltip: {
						//提示框组件
						trigger: "item", //item数据项图形触发，主要在散点图，饼图等无类目轴的图表中使用。
						axisPointer: {
							// 坐标轴指示器，坐标轴触发有效
							type: "shadow" // 默认为直线，可选为：'line' | 'shadow'
						},
						formatter: "{b}: {c}g {d}% " //{a}（系列名称），{b}（数据项名称），{c}（数值）, {d}（百分比）
					},

					legend: {
						top: "5%",
						itemHeight: 10
					},
					series: [
						{
							center: ["55%", "65%"],
							type: "pie",
							radius: ["0%", "70%"],
							data: data,
							label: {
								normal: {
									formatter: "{b}: {c} {d}% ",
									textStyle: {
										fontWeight: "normal",
										fontSize: 8
									}
								}
							},
							itemStyle: {
								borderRadius: 3,
								borderColor: "#fff",
								borderWidth: 2
							},
							emphasis: {
								itemStyle: {
									shadowBlur: 10,
									shadowOffsetX: 0,
									shadowColor: "rgba(0, 0, 0, 0.5)"
								}
							}
						}
					]
				}
				this.myChart3.clear()
				this.myChart3.setOption(option3)
			})

			this.init()
		}
	}
}
</script>
<style lang="less" scoped>
.h1 {
	font-size: 15px;
	font-weight: bold;
	text-align: center;
	padding: 5px 0;
	color: #333;
}
h1 {
	padding-left: 5px;
	font-size: 14px;
	font-weight: 600;
}
/deep/.ant-table-wrapper {
	background: #fff;
}
/deep/.table-operator {
	margin-bottom: 18px;
}
button {
	margin-right: 8px;
}
/deep/.ant-table-thead > tr > th {
	padding: 5px 0 5px 4px;
	border-bottom: 1px solid #d2d4d7;
	border-right: 0;
	font-size: 13px;
	font-weight: bold;
	background: #f6f6f6;
	color: #333;
}

/deep/.ant-table-thead > tr > th:nth-child(7) {
	background: #fff;
	border-bottom: 1px solid #fff;
}
/deep/.ant-table-tbody > tr > td {
	padding: 0;
	margin: 0;
	border-bottom: 1px solid #d2d4d7;
	border-right: 1px solid #d2d4d7;
	font-size: 12px;
}

/deep/.tdcls {
	// color: #000;
	color: #666;
	// background: rgb(239, 239, 239);
	background: rgba(245, 245, 245,.8);
	padding: 1px 0 1px 4px !important;
}

/deep/.tdcls1 {
	color: #000;
	// background: rgb(239, 239, 239);
	background: rgba(245, 245, 245,.8);
}

input[disabled]{
	color: #666;
	background: rgba(245, 245, 245,.8) !important;
}

/deep/.ant-input[disabled] {
  height: 26px;
  background-color: #efefef;
  color: rgb(0, 0, 0);
  border: 0;
}
/deep/.ant-input-affix-wrapper .ant-input:not(:first-child) {
  padding-left: 18px;
}

/deep/.ant-input-affix-wrapper .ant-input-prefix{
  left: 5px;
}

/deep/ .td_border_botter_rest {
	border-bottom: 1px solid #fff !important;
	background: #fff;
}

/deep/.td_border_right {
	border-right: 1px solid #d2d4d7;
}
/deep/.td_width {
	width: 50px;
	padding: 0 !important;
}

/deep/.ant-select-selection {
	border: none;
	background-color: unset;
}

.div_width {
	width: 30px;
	margin: auto;
}
input {
	width: 100%;
	border: 0;
	outline: none;
	height: 25px;
	text-align: center;
}
.wrapper {
	width: 70%;
	background: #fff;
	padding: 0 10px;
	overflow: hidden;
	float: left;
	z-index: 888;
}
.wrapper:last-child {
	width: 29%;
	z-index: 998;
	float: right;
	background: #f6f6f6;
	overflow-x: hidden;
	position: absolute;
	top: 0;
	right: 0;
	padding: 5px;
}

.spanstatus {
	display: inline-block;

	padding: 2px 4px;
	border-radius: 2px;
}
.success1 {
	//background: #66b72a;
	color: #66b72a;
}
.warn1 {
	//background: #fec303;
	color: #fec303;
	margin-top: 2px;
	margin-bottom: -2px;
}

.fail1 {
	//background: #e05328;
	color: #e05328;
}

.div_btns span {
	margin-left: 5px;
	display: inline-block;
	width: 20px;
}
.statusbar {
	overflow: hidden;
	height: 32px;
	line-height: 32px;
	font-size: 12px;
	text-align: right;
}
.statusbar::after {
	content: " ";
	display: block;
	height: 0;
	clear: both;
}
.statusbar .tip {
	color: rgb(255, 0, 0);
}
.tab-title {
	padding: 0 10px;
}

div.tab-head {
	border-bottom: 1px solid #d3d2d2c9;
}

div.tab-head div {
	display: inline-block;
	padding: 0 10px;
	font-weight: 700;
	font-size: 18px;
	color: rgb(128, 128, 128);
	margin-bottom: -6px;
	cursor: pointer;
}

div.tab-head div.active {
	font-size: 24px;
	color: rgba(0, 73, 176, 1);
	margin-bottom: -4px;
	cursor: text;
}

.color-block {
	width: 8px;
	height: 20px;
	margin-top: 5px;
	margin-right: 2px;
	margin-bottom: -5px;
	display: inline-block;
	background-color: rgb(47, 84, 235);
}

div.sub-title {
	overflow: hidden;
	padding: 6px 10px;
}

div.sub-title span {
	display: block;
	float: left;
	margin-right: 6px;
}
div.sub-title span:first-child {
	margin-top: 1px;
}
div.sub-title .tip {
	font-family: SourceHanSansSC;
	font-weight: 400;
	font-size: 15px;
	color: rgba(0, 101, 255, 0.67);
}
/*.anticon svg {
      font-size: 25px;
    }*/
/deep/.ant-select-selection-selected-value {
	text-align: center;
}
/deep/.ant-select-open .ant-select-selection {
	border: none;
	box-shadow: none;
}
/deep/ .ant-select-selection__rendered {
	margin: auto;
	line-height: initial;
	font-size: 12px;
	text-align: center;
}
/deep/ .ant-select-selection--single {
	height: auto;
}
/deep/tr.ant-table-row.ant-table-row-level-0 {
	height: 20px;
}

/deep/ .ant-table-scroll {
	overflow-x: auto;
}

.redius {
	border-radius: 5px;
	margin-top: 8px;

	margin-right: 8px;
}
.redius1 {
	border-radius: 5px;
	margin-top: 8px;
}

/deep/.ant-select-arrow .ant-select-arrow-icon svg {
	display: none;
}

/deep/.ant-select-selection--single {
	height: 100%;
}

/deep/.ant-select-selection__rendered {
	display: inline-block;
}
/deep/.ant-select-selection-selected-value {
	color: black;
}
/deep/.ant-input[disabled] {
	// background-color: #efefef;
	background: rgba(245, 245, 245,.8);
	color: rgb(0, 0, 0);
}

.input-white input[disabled] {
	background-color: #fff !important;
}

// /deep/input[disabled] {
// 	background-color: #efefef;
// 	color: rgb(0, 0, 0);
// }

.grey {
	// background-color: #efefef;
	background: rgba(245, 245, 245,.8);

}
/deep/ #not_border > div > div > div > div > div > table {
	border-left: 0;
}

.content-wrap {
	position: relative;
}

// // 设置不可点击
// /deep/td {
// 	pointer-events: none;
// }
</style>

<style lang="less">
.dropdownClassName {
	.ant-select-dropdown-menu-item {
		padding: 2px 8px !important;
		font-size: 12px !important;
		text-align: left !important;
		font-weight: 100;
	}
}
</style>
