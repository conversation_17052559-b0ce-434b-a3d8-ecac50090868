<template>
  <div style="background-color: #FFFFFF;padding: 10px">

    <a-tabs type="card" @change="getList()" v-model="address">
      <a-tab-pane key="all" tab="全部" v-if="hasPerm('progress:all')">
      </a-tab-pane>
      <a-tab-pane key="A1_3F" tab="V圆柱检测室" v-if="hasPerm('progress:all') || hasPerm('progress:a1_3f')">
      </a-tab-pane>

      <a-tab-pane key="R2_2F" tab="材料验证检测室" v-if="hasPerm('progress:all') || hasPerm('progress:r2_2f')">
      </a-tab-pane>

      <a-tab-pane key="R4_4F" tab="动力电池检测室" v-if="hasPerm('progress:all') || hasPerm('progress:r4_4f')">
      </a-tab-pane>

      <a-tab-pane key="lims" tab="LIMS同步" v-if="hasPerm('progress:lims')">
      </a-tab-pane>
    </a-tabs>
    <div style="float: right;position: relative;z-index: 1;padding-bottom: 5px;width:550px">
      <a-icon :type="!show?'down':'up'" style="margin-right: 8px;cursor: pointer" @click="() => show=!show"/>

      <a-button  style="margin-left: 8px;width: 50px" @click="getList(true)">重置</a-button>

      <a-button type="primary" style="margin-left: 8px;width: 50px" @click="openAdd" v-if="hasPerm('progress:add')&& address != 'lims'" >新增</a-button>
      <a-button type="primary" style="margin-left: 8px;width: 50px" @click="openEdit" v-if="hasPerm('progress:edit')&& address != 'lims'">编辑</a-button>
      <a-button type="primary" style="margin-left: 8px;width: 100px" @click="clearTestData" v-if="hasPerm('testProjectTodoTask:clearCalendarTestData')&& address != 'lims'">清空测试数据</a-button>
      <a-button type="primary" style="margin-left: 8px;width: 120px" @click="openEditDataSelect" v-if="hasPerm('progress:editDataInput')&& address != 'lims'">修改数据录入项目</a-button>
      <a-button type="primary" style="margin-left: 8px;width: 120px" @click="openChooseOrdTask" v-if="hasPerm('progress:editOrderTsk')&& address != 'lims'">选择测试项目</a-button>
      <!--<a-button type="primary" style="margin-left: 8px;width: 100px" @click="openCenterAdd" v-if="address != 'lims'">中检结果录入</a-button>-->
      <a-button type="primary" style="margin-left: 8px;width: 50px" @click="openEditActual" v-if="hasPerm('progress:in')&& address != 'lims'">进箱</a-button>
      <a-button type="primary" style="margin-left: 8px;width: 50px" @click="exportData" v-if="hasPerm('progress:export') && address != 'lims'">导出</a-button>

      <a-upload
        v-if="false"
        :customRequest="customRequest"
        :multiple="false"
        :showUploadList="false"
        accept=".xlsx, .xls"
        name="file"
        style="width: 50px"
      >
        <a-button> <a-icon type="upload" />上传excel</a-button>
      </a-upload>
      <!--<a-button type="primary" style="margin-left: 8px;width: 130px" @click="openAllSee" v-if="address != 'lims'">查看所有中检结果</a-button>-->
      <!--<a-input @change="scrollTo"></a-input>-->


      <a-popconfirm
        title="确定删除吗?"
        ok-text="确定"
        cancel-text="取消"
        @confirm="deleteRecord"
        :visible="deleteVisible"
        placement="topRight"
        v-if="hasPerm('progress:delete') && address != 'lims'"
        @cancel="() => deleteVisible = false"
      >
        <a-button type="primary" style="margin-left: 8px;width: 50px"  @click="deleteBefore">删除</a-button>
      </a-popconfirm>


      <a-popconfirm
        title="确定同步吗?"
        ok-text="确定"
        cancel-text="取消"
        @confirm="importLims"
        placement="topRight"
        :visible="importVisible"
        v-if="hasPerm('progress:lims') && address == 'lims'"
        @cancel="() => importVisible = false"
      >
        <a-button type="primary" style="margin-left: 8px;width: 70px" @click="importBefore">确认导入</a-button>
      </a-popconfirm>
      <a-button v-show="!isUpload" type="primary" style="margin-left: 8px;margin-top: 10px;margin-bottom: 5px;" @click="beforeUploadCheck"
                v-if="hasPerm('testProjectTodoTask:importTestDataExceptHeight')">导入数据(除尺寸外)</a-button>
      <a-upload
        v-show="isUpload"
        :headers="headers"
        :action="`/api/testProjectTodoTask/importTestDataExceptHeight`"
        name="file"
        :fileList="fileList"
        :data="{ testProgressId: importParam.id, folderNo: importParam.testCode }"
        :showUploadList="false"
        accept="*"
        @change="importTestData($event)"
      >
        <a-button type="primary" style="margin-left: 8px;margin-top: 10px;margin-bottom: 5px;" @click="beforeUploadCheck"
                  v-if="hasPerm('testProjectTodoTask:importTestDataExceptHeight')">导入数据(除尺寸外)</a-button>
      </a-upload>
      <a-button v-show="!isUpload" type="primary" style="margin-left: 8px;margin-top: 10px;margin-bottom: 5px;" @click="beforeUploadCheck"
                v-if="hasPerm('testProjectTodoTask:importTestDataExceptHeight')">导入数据(仅尺寸)</a-button>
      <a-upload
        v-show="isUpload"
        :headers="headers"
        :action="`/api/testProjectTodoTask/importTestDataOnlyHeight`"
        name="file"
        :fileList="fileList"
        :data="{ testProgressId: importParam.id, folderNo: importParam.testCode }"
        :showUploadList="false"
        accept="*"
        @change="importTestData($event)"
      >
        <a-button type="primary" style="margin-left: 8px;margin-top: 10px;margin-bottom: 5px;" @click="beforeUploadCheck"
                  v-if="hasPerm('testProjectTodoTask:importTestDataExceptHeight')">导入数据(仅尺寸)</a-button>
      </a-upload>
      <a-button type="primary" style="margin-left: 8px;margin-top: 10px;margin-bottom: 5px;" @click="exportHandleResult"
                v-if="hasPerm('testProjectTodoTask:importTestDataExceptHeight')">导出处理结果</a-button>
      <a-button type="primary" style="margin-left: 8px;margin-top: 10px;margin-bottom: 5px;" @click="exportSizeOriData"
                v-if="hasPerm('testProjectTodoTask:importTestDataExceptHeight')">导出尺寸原始数据</a-button>
      <a-button v-show="!isUpload" type="primary" style="margin-left: 8px;margin-top: 10px;margin-bottom: 5px;" @click="beforeUploadCheck"
                v-if="hasPerm('testProjectTodoTask:importTestDataExceptHeight')">导入阶段式数据(除尺寸外)</a-button>
      <a-upload
        v-show="isUpload"
        :headers="headers"
        :action="`/api/testProjectTodoTask/importStageDataExceptHeight`"
        name="file"
        :fileList="fileList"
        :data="{ testProgressId: importParam.id, folderNo: importParam.testCode }"
        :showUploadList="false"
        accept="*"
        @change="importTestData($event)"
      >
        <a-button type="primary" style="margin-left: 8px;margin-top: 10px;margin-bottom: 5px;" @click="beforeUploadCheck"
                  v-if="hasPerm('testProjectTodoTask:importTestDataExceptHeight')">导入阶段式数据(除尺寸外)</a-button>
      </a-upload>
      <a-button type="primary" style="margin-left: 8px;margin-top: 10px;margin-bottom: 5px;" @click="checkCalendarReport()">查看报告</a-button>
    </div>
    <div style="float: left;position: relative;z-index: 1;padding-bottom: 5px;width: 65%">
      <a-row :gutter="[8,8]">
        <a-col :span="6">
          <a-form-item label="测试申请单" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryParam.testCode" @keyup.enter="getList" @change="getList"/>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="出箱日期" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-select v-model="queryParam.out" style="width: 100%;" @change="getList" :allowClear="true">

              <a-select-option value="today">
                今天
              </a-select-option>
              <a-select-option value="two">
                0~2天
              </a-select-option>

            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="6">
          <a-form-item label="申请人" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryParam.applicant" @keyup.enter="getList" @change="getList"/>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="测试技师" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryParam.testMan" @keyup.enter="getList" @change="getList"
            />
          </a-form-item>

        </a-col>
      </a-row>
      <a-row :gutter="[8,8]" v-if="show">
        <a-col :span="6">
          <a-form-item label="产品名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryParam.productName" @keyup.enter="getList" @change="getList"/>
          </a-form-item>
        </a-col>

        <a-col :span="6">
          <a-form-item label="部门" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryParam.dept" @keyup.enter="getList" @change="getList"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="样品类型" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-select v-model="queryParam.sampleType" @keyup.enter="getList" @change="getList" :allowClear="true"
                      style="width: 100%;">

              <a-select-option value="G圆柱">
                G圆柱
              </a-select-option>
              <a-select-option value="C圆柱">
                C圆柱
              </a-select-option>
              <a-select-option value="V圆柱">
                V圆柱
              </a-select-option>
              <a-select-option value="方形">
                方形
              </a-select-option>
              <a-select-option value="软包_396389">
                软包_396389
              </a-select-option>
              <a-select-option value="软包_动力">
                软包_动力
              </a-select-option>
              <a-select-option value="模组">
                模组
              </a-select-option>

            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">

          <a-form-item label="测试状态" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-select  :allowClear="true" mode="multiple" :maxTagCount="parseInt(3)" @change="changeStatus" v-model="status"  style="width: 260px">

              <a-select-option value="Plan">
                Plan
              </a-select-option>
              <a-select-option value="Ongoing">
                Ongoing
              </a-select-option>
              <a-select-option value="Done">
                Done
              </a-select-option>
              <a-select-option value="Stop">
                Stop
              </a-select-option>

            </a-select>
          </a-form-item>
          <!--<a-form-item label="测试地点" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-select @keyup.enter="getList" @change="getList" v-model="queryParam.testAddress" :allow-clear="true"
                      style="width: 100%">

              <a-select-option value="A1_3F">
                A1_3F
              </a-select-option>
              <a-select-option value="B4_3F">
                B4_3F
              </a-select-option>
              <a-select-option value="R2_2F">
                R2_2F
              </a-select-option>
              <a-select-option value="R4_3F">
                R4_3F
              </a-select-option>
              <a-select-option value="R4_4F">
                R4_4F
              </a-select-option>
              <a-select-option value="R4_5F">
                R4_5F
              </a-select-option>

            </a-select>

          </a-form-item>-->
        </a-col>
      </a-row>
      <a-row :gutter="[8,8]" v-if="show">
        <a-col :span="6">
          <a-form-item label="T/℃" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryParam.t" @keyup.enter="getList" @change="getList"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="测试项目" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryParam.testProject" @keyup.enter="getList" @change="getList"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="样品阶段" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryParam.productSampleStage" @keyup.enter="getList" @change="getList"
            />
          </a-form-item>

        </a-col>
        <a-col :span="6" >
          <a-form-item label="出箱范围" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-range-picker @change="rangeChange"  style="width: 260px">
            </a-range-picker>
          </a-form-item>
        </a-col>
        <a-col :span="6" v-if="address != 'all'">
          <a-form-item label="存储位置" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryParam.saveAddress" @keyup.enter="getList" @change="getList"/>
          </a-form-item>
        </a-col>

      </a-row>
      <a-row :gutter="[8,8]" v-if="show">

        <a-col :span="6" v-if="address == 'all'">
          <a-form-item label="测试地点" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-select v-model="allAddress" @keyup.enter="getList" @change="getList" :allowClear="true"
                      style="width: 100%;">
              <a-select-option value="A1_3F">
                V圆柱检测室
              </a-select-option>

              <a-select-option value="R2_2F">
                材料验证检测室
              </a-select-option>

              <a-select-option value="R4_4F">
                动力电池检测室
              </a-select-option>
            </a-select>
          </a-form-item>

        </a-col>


        <a-col :span="6" v-if="address == 'all'">
          <a-form-item label="存储位置" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryParam.saveAddress" @keyup.enter="getList" @change="getList"/>
          </a-form-item>
        </a-col>
      </a-row>

    </div>



    <div class="box" ref="box">
      <!--<div class="left">
        <a-table :columns="columns" id="table1" :data-source="data" bordered :scroll="{x:true}" :rowKey="(record) => record.id"
                 :customRow="customRow"
                 ref="table1"
                 @row-click="onRowClick"
                 :pagination="false"
                 :rowSelection="rowSelection">

          &lt;!&ndash;<template
            v-for="(p, idx) in headData"
            :slot="'data['+idx+'].inDate'"
            slot-scope="text, record, index, columns"
          >
            &lt;!&ndash;<div v-if="(record.type == 'seven' && record.data[idx].day == 28) ||
             (record.type == 'twenty-eight' && (record.data[idx].day == 7 || record.data[idx].day == 14)) ||
             (record.type == 'thirty' && (record.data[idx].day == 7 || record.data[idx].day == 14 || record.data[idx].day == 28)) ">
              /
            </div>&ndash;&gt;
            <div v-if="record.data.length < idx+1"
                 style="background-color: rgba(223, 223, 223, 0.25);width:100%;height:100%"></div>

            <a-date-picker v-else :allowClear="false" placeholder=""
                           :value="null != record.data[idx].inDate ?moment(record.data[idx].inDate, 'YYYY-MM-DD'):null"
                           @change="changeDate($event,record.data[idx])">
            </a-date-picker>


          </template>&ndash;&gt;

        </a-table>

      </div>
      <div class="resize" title="收缩侧边栏" :style="{height:height+'px'}">

      </div>-->
      <div class="mid">
        <s-table :columns="columns" :data="loadData" bordered :scroll="{x:true}" :rowKey="(record) => record.id"
                 :customRow="customRow"
                 :rowSelection="rowSelection"
                 ref="table2"
        >
          <template
          slot="center"
          slot-scope="text, record, index, columns"
          >
            <a @click="openCenter(text.id)" class="grey" style="text-align: center" v-if="text != null && text.day != null
            && record.centerData.length != 0 && checkCenter(record,text)">查看结果</a>
            <div class="grey" v-else-if="text != null && text.day != null">查看结果</div>
            <div class="grey" v-else></div>
          </template>

          <template
          slot="centerZero"
          slot-scope="text, record, index, columns"
          >
            <a @click="openCenter(null,record.id)" class="grey" style="text-align: center" v-if="record.centerData.length != 0 &&
            record.centerData.filter(item => item.orderNumber == 0).length > 0">查看详情</a>
            <div class="grey" v-else>查看详情</div>
          </template>

          <template
            slot="testProject"
            slot-scope="text, record, index, columns">
            <a @click="checkCalendarReportByRecord(record)"  style="text-align: center" >{{text}}</a>
          </template>

          <!--<template
            v-for="(p, idx) in headData"
            :slot="'data['+idx+'].inDate'"
            slot-scope="text, record, index, columns"
          >
            &lt;!&ndash;<div v-if="(record.type == 'seven' && record.data[idx].day == 28) ||
             (record.type == 'twenty-eight' && (record.data[idx].day == 7 || record.data[idx].day == 14)) ||
             (record.type == 'thirty' && (record.data[idx].day == 7 || record.data[idx].day == 14 || record.data[idx].day == 28)) ">
              /
            </div>&ndash;&gt;
            <div v-if="record.data.length < idx+1"
                 style="background-color: rgba(223, 223, 223, 0.25);width:100%;height:100%"></div>

            <a-date-picker v-else :allowClear="false" placeholder=""
                           :value="null != record.data[idx].inDate ?moment(record.data[idx].inDate, 'YYYY-MM-DD'):null"
                           @change="changeDate($event,record.data[idx])">
            </a-date-picker>


          </template>-->

        </s-table>

      </div>
    </div>
    <test-add-form ref="testAddForm" @ok="handleOk" :address="address"/>
    <test-edit-form ref="testEditForm" @ok="handleOk"/>
    <test-edit-actual-form ref="testEditActualForm" @ok="handleOk"/>
    <test-center-add-form ref="testCenterAddForm" @ok="handleOk"/>
    <test-center-see-form ref="testCenterSeeForm" @ok="handleOk"/>
    <test-center-see-all-form ref="testCenterSeeAllForm" @ok="handleOk"/>
    <test-edit-data-select ref="testEditDataSelect" @ok="handleOk"/>
    <choose-lims-ord-task ref="chooseLimsOrdTask" @ok="handleOk"/>
  </div>
</template>
<script>
import {
  testProgressAdd,
  testProgressList,
  testProgressUpdate,
  testProgressUpdateOnlyBean,
  testProgressUpdateData,
  testProgressListPage,
  testProgressImport,
  testProgressExport,
  validExportSizeOriData,
  exportHandleResult, exportSizeOriData, clearCalendarTestData
} from '@/api/modular/system/testProgressManager'
  import testAddForm from './testAddForm'
  import testEditForm from './testEditForm'
  import testEditDataSelect from './testEditDataSelect'
  import testEditActualForm from './testEditActualForm'
  import testCenterAddForm from './testCenterAddForm'
  import testCenterSeeForm from './testCenterSeeForm'
  import testCenterSeeAllForm from './testCenterSeeAllForm'
  import chooseLimsOrdTask from './chooseLimsOrdTask'
  import moment from 'moment';
  import { STable } from '@/components'
  import Vue from "vue";
  import { ACCESS_TOKEN } from "@/store/mutation-types";
import { downloadfile1 } from "@/utils/util";
import { mapGetters } from "vuex";


  export default {
    components: {
      testEditDataSelect,
      testCenterSeeAllForm,
      chooseLimsOrdTask,
      testAddForm, moment, testEditForm,STable,testEditActualForm,testCenterAddForm,testCenterSeeForm
    },
    data() {
      return {
        loadData: parameter => {

          if(this.address != null && this.address == 'lims'){
            this.queryParam.testAddress = null
            this.queryParam.source = 'lims'
            this.queryParam.statusList = ['Plan']
          } else if(this.address != null && this.address != 'all'){
            this.queryParam.testAddress = this.address
            this.queryParam.source = null
            this.queryParam.statusList = this.status
          }else{
            this.queryParam.testAddress = this.allAddress
            this.queryParam.source = null
            this.queryParam.statusList = this.status
          }




          return testProgressListPage(Object.assign(parameter, this.queryParam) ).then((res) => {
            return res
          }).then((res) => {


            this.selectedRowKeys = []
            this.selectedRows = []

            this.data = res.data.rows
            this.height = 32*res.data.rows.length +70

            for (let i = 0; i < this.data.length; i++) {
              if (this.data[i].data.length > this.headData.length) {
                this.headData = this.data[i].data
              }
            }


            this.columns1=[]
            this.columns = [
              {
                title: '序号',
                dataIndex: 'index',
                align: 'center',
                width: 50,
                fixed:'left',
                customRender: (text, record, index) => `${index + 1}`
              }, {
                title: '测试申请单',
                width: 90,
                fixed:'left',
                align: 'center',
                dataIndex: 'testCode',
              }, {
                title: '样品类型',
                width: 70,
                ellipsis:true,
                align: 'center',

                fixed:'left',
                dataIndex: 'sampleType',
              }, {
                title: '产品名称',
                width: 70,
                fixed:'left',
                ellipsis:true,
                align: 'center',
                dataIndex: 'productName',
              }, {
                title: '产品样品阶段',
                width: 100,
                fixed:'left',
                align: 'center',
                dataIndex: 'productSampleStage',
              }, {
                title: '测试类型',
                width: 120,

                align: 'center',
                dataIndex: 'testType',
              }, {
                title: '申请部门',
                width: 140,
                align: 'center',

                dataIndex: 'dept',
              }, {
                title: '申请人',
                width: 70,

                align: 'center',
                dataIndex: 'applicant',
              }, {
                title: '测试项目',
                width: 120,
                align: 'center',

                ellipsis:true,
                dataIndex: 'testProject',
                scopedSlots: { customRender: 'testProject' },
              }, {
                title: '测试项目别名',
                width: 80,
                align: 'center',
                ellipsis:true,
                dataIndex: 'testAlias',
              },  {
                title: '测试目的',
                width: 80,
                align: 'center',

                ellipsis:true,
                dataIndex: 'testPurpose',
              }, {
                title: 'T/℃',
                width: 70,
                align: 'center',

                dataIndex: 't',
              }, {
                title: 'SOC',
                width: 70,
                align: 'center',

                dataIndex: 'soc',
              }, {
                title: '测试周期',
                width: 70,
                align: 'center',

                dataIndex: 'testPeriod',
                customRender: (text, record, index) => {
                  return text + '天'
                }
              }, {
                title: '中检次数',
                width: 70,
                align: 'center',

                dataIndex: 'data',
                customRender: (text, record, index) => {
                  return text.length
                }
              }, {
                title: '数量',
                width: 70,
                align: 'center',

                dataIndex: 'quantity',
              }, {
                title: '测试技师',
                width: 70,
                align: 'center',

                dataIndex: 'testMan',
              }, {
                title: '测试地点',
                width: 100,
                align: 'center',
                dataIndex: 'testAddress',
                customRender: (text, record, index) => {
                  if(text == 'A1_3F'){
                    return 'V圆柱检测室'
                  }
                  if(text == 'R2_2F'){
                    return '材料验证检测室'
                  }
                  if(text == 'R4_4F'){
                    return '动力电池检测室'
                  }

                }
              },{
                title: '存储位置',
                width: 70,
                align: 'center',

                dataIndex: 'saveAddress',
              }, {
                title: '测试状态',
                width: 70,
                align: 'center',

                dataIndex: 'testStatus',
              }, {
                title: '已存储天数',
                width: 100,
                align: 'center',

                dataIndex: 'finishDay',
                customRender: (text, record, index) => {
                  return null != text ? text + '天' : text
                }
              }, {
                title: '进箱开始时间',
                width: 100,
                align: 'center',
                dataIndex: 'zero',
                customRender: (text, record, index) => {
                  return record.data.length > 0?record.data[0].actualInDate != null?record.data[0].actualInDate:
                    record.data[0].inDate:text
                }
              }/*,
              {
                title: '初始性能',
                width: 90,
                align: 'center',
                dataIndex: 'id',
                scopedSlots: {customRender: 'centerZero'},
              },*/


            ]


            if (this.data.length > 0) {
              for (let i = 0; i < this.headData.length; i++) {
                this.columns.push({
                  //title: '第' + res.data[0].data[i].day + '天',
                  title: '存储第' + (i + 1) + '阶段',
                  align: 'center',

                  children: [
                    {
                      title: '存储天数',
                      width: 80,
                      align: 'center',
                      customRender: (text, record, index) => {
                        //<div v-if="record.data.length < idx-1" style="background-color: rgba(223, 223, 223, 0.25)"></div>
                        if (record.data.length < i + 1) {
                          return ( < div
                          style = "background-color: rgba(223, 223, 223, 0.25);width:100%;height:100%" > < /div>)
                        }

                        return ( < div
                        style = "background-color: rgba(223, 223, 223, 0.25);width:100%;height:100%;display: flex;align-items: center;justify-content: center;" >
                          {
                            text
                          }
                        天 < /div>)
                      },
                      dataIndex: 'data[' + i + '].day',
                    }, {
                      title: '开始时间',
                      width: 80,
                      align: 'center',
                      /*scopedSlots: {
                        customRender: 'data[' + i + '].inDate'
                      },*/
                      customRender: (text, record, index) => {

                        /*if(record.data[i].actualInDateStatus == 1){
                          return ( < div
                        class
                          = 'green' > {text} < /div>)
                        }
*/
                        if(record.data.length > i+1 && record.data[i].actualInDate != null){
                            return (<div class='grey'>{record.data[i].actualInDate}</div>)
                        }else if(record.data.length < i+1){
                          return (<div class='grey'></div>)
                        }else{
                          return (<div class='grey'>{text}</div>)
                        }

                      },
                      dataIndex: 'data[' + i + '].inDate',
                    }, {
                      title: '结束时间',
                      width: 80,
                      align: 'center',
                      dataIndex: 'data[' + i + '].outDate',
                      customRender: (text, record, index) => {
                        if (record.data.length < i + 1) {
                          return ( < div
                          style = "background-color: rgba(223, 223, 223, 0.25);width:100%;height:100%" > < /div>)
                        }
                        if (null != text) {
                          var m1 = moment(text, 'YYYY-MM-DD')
                          var m2 = moment(new Date(), 'YYYY-MM-DD')
                          if (m1.date() == m2.date() && m1.month() === m2.month() && m1.year() === m2.year()) {
                            return ( < div
                          class
                            = 'red' > {text} </div>)
                          } else if (m1.month() === m2.month() && m1.year() === m2.year() && m1.date() - m2.date() <= 2 && m1.date() - m2.date() >= 0) {
                            return ( < div
                          class
                            = 'yellow' > {text} </div>)
                          } else {
                            return ( < div
                          class
                            = 'grey' > {text} < /div>)
                          }
                        }

                        if ((record.type == 'seven' && record.data[i].day == 28) ||
                          (record.type == 'twenty-eight' && (record.data[i].day == 7 || record.data[i].day == 14)) ||
                          (record.type == 'thirty' && (record.data[i].day == 7 || record.data[i].day == 14 || record.data[i].day == 28))
                        ) {
                          return ( <div> / </div>)

                        }


                      }
                    }/*,{
                      title: '中检结果',
                      width: 80,
                      align: 'center',
                      dataIndex: 'data[' + i + ']',
                      scopedSlots: {customRender: 'center'}
                    }*/
                  ]
                })
              }
            }
           /* return testProgressListPage(Object.assign(parameter, this.queryParam) ).then((res1) => {
              return res1.data
            })*/
            return res.data
          })
        },

        status:['Ongoing','Plan'],
        height:200,
        address: this.hasPerm('progress:all')?'all': this.hasPerm('progress:a1_3f')?'A1_3F': this.hasPerm('progress:r2_2f')?'R2_2F':
          this.hasPerm('progress:r4_4f')?'R4_4F':'none',
        show: false,
        labelCol: {

          sm: {
            span: 11
          }
        },
        wrapperCol: {

          sm: {
            span: 13
          }
        },
        queryParam: {},
        data: [],
        headData: [],
        allAddress:null,
        // 表头
        columns: [
          {
            title: '序号',
            dataIndex: 'index',
            align: 'center',
            width: 50,

            customRender: (text, record, index) => `${index + 1}`
          }, {
            title: '测试申请单',
            width: 90,

            align: 'center',
            dataIndex: 'testCode',
          }, {
            title: '样品类型',
            width: 70,

            align: 'center',
            dataIndex: 'sampleType',
          }, {
            title: '产品名称',
            width: 70,

            align: 'center',
            dataIndex: 'productName',
          }, {
            title: '产品样品阶段',
            width: 120,

            align: 'center',
            dataIndex: 'productSampleStage',
          }, {
            title: '测试类型',
            width: 120,

            align: 'center',
            dataIndex: 'testType',
          }, {
            title: '申请部门',
            width: 130,
            align: 'center',

            dataIndex: 'dept',
          }, {
            title: '申请人',
            width: 70,

            align: 'center',
            dataIndex: 'applicant',
          }, {
            title: '测试项目',
            width: 120,
            align: 'center',

            dataIndex: 'testProject',
            scopedSlots: { customRender: 'testProject' },
          }, {
            title: 'T/℃',
            width: 70,
            align: 'center',

            dataIndex: 't',
          }, {
            title: 'SOC',
            width: 70,
            align: 'center',

            dataIndex: 'soc',
          }, {
            title: '测试周期',
            width: 70,
            align: 'center',

            dataIndex: 'testPeriod',
          }, {
            title: '中检次数',
            width: 70,
            align: 'center',

            dataIndex: 'data',
            customRender: (text, record, index) => {
              return text.length
            }
          },{
            title: '数量',
            width: 70,
            align: 'center',

            dataIndex: 'quantity',
          }, {
            title: '测试技师',
            width: 70,
            align: 'center',

            dataIndex: 'testMan',
          }, {
            title: '测试地点',
            width: 70,
            align: 'center',

            dataIndex: 'testAddress',
            customRender: (text, record, index) => {
              if(text == 'A1_3F'){
                return 'V圆柱检测室'
              }
              if(text == 'R2_2F'){
                return '材料验证检测室'
              }
              if(text == 'R4_4F'){
                return '动力电池检测室'
              }

            }
          },  {
            title: '存储位置',
            width: 70,
            align: 'center',

            dataIndex: 'saveAddress',
          }, {
            title: '测试状态',
            width: 70,
            align: 'center',

            dataIndex: 'testStatus',
          }, {
            title: '已存储天数',
            width: 100,
            align: 'center',

            dataIndex: 'finishDay',
          }, {
            title: '进箱开始时间',
            width: 100,
            align: 'center',

            dataIndex: 'zero',
          }/*,{
            title: '初始性能',
            width: 100,
            align: 'center',
            dataIndex: 'id',
            scopedSlots: {customRender: 'centerZero'},
          },*/


        ],
        columns1:[{
          //title: '第' + res.data[0].data[i].day + '天',
          title: '存储第' + 1 + '阶段',
          align: 'center',

          children: [
            {
              title: '存储天数',
              width: 80,
              align: 'center',
              dataIndex: 'data[' +0 + '].day',
            }, {
              title: '开始时间',
              width: 80,
              align: 'center',

              dataIndex: 'data[' + 0 + '].inDate',
            }, {
              title: '结束时间',
              width: 80,
              align: 'center',
              dataIndex: 'data[' + 0 + '].outDate',

            }
          ]
        }],
        selectedRowKeys: [],
        selectedRows: [],
        height:'500px',
        deleteVisible:false,
        importVisible:false,
        importParam: {},
        fileList: [],
        isUpload:false,
        headers: {
          Authorization: "Bearer " + Vue.ls.get(ACCESS_TOKEN)
        }
      }
    },
    created() {
    },
    computed: {
      ...mapGetters(['userInfo']),
      rowSelection(){
        return {
          columnWidth: 30,
          type:'radio',
          selectedRowKeys: this.selectedRows.map((row) => row.id),
          onChange: (selectedRowKeys, selectedRows) => {
            this.selectedRows = selectedRows
            this.isUpload = true
            this.importParam.id = this.selectedRows[0].id
            this.importParam.testCode = this.selectedRows[0].testCode
          }
        }
      },
    },
    mounted() {


      //this.dragControllerDiv()
      if(this.hasPerm('progress:all')){
        this.address = 'all'
      }else if(this.hasPerm('progress:a1_3f')){
        this.address = 'A1_3F'
      }else if(this.hasPerm('progress:b4_3f')){
        this.address = 'B4_3F'
      }else if(this.hasPerm('progress:r4_3f_v')){
        this.address = 'R4_3F_V圆柱'
      }else if(this.hasPerm('progress:r4_3f_cc')){
        this.address = 'R4_3F_动力电池'
      }else if(this.hasPerm('progress:r4_4f')){
        this.address = 'R4_4F'
      }else if(this.hasPerm('progress:r4_5f')){
        this.address = 'R4_5F'
      }else{
        this.address = 'none'
      }

      this.$nextTick(() =>{
        this.getList()

      })

    },
    methods: {
      deleteBefore() {
        if(this.selectedRows.length == 0){
          this.$message.warn('请至少选中一条数据')
          return
        }else{
          this.deleteVisible = true
        }
      },
      importBefore() {
        if(this.selectedRows.length == 0){
          this.$message.warn('请至少选中一条数据')
          return
        }else{
          this.importVisible = true
        }
      },
      importLims() {
        let select = this.selectedRows

        for (let i = 0; i < select.length; i++) {
          testProgressUpdateOnlyBean({id: select[i].id, source: 'lims_import',testStatus:'Ongoing'})
        }
        this.getList()
        this.selectedRows = []
        this.selectedRowKeys = []
        this.importVisible = false


      },
      changeStatus(a){

        this.queryParam.statusList = a

        this.$refs.table2.refresh()
      },
      rangeChange(a,b){


        this.queryParam.rangeBegin = b[0]
        this.queryParam.rangeEnd = b[1]

        this.$refs.table2.refresh()
      },
      checkCenter(record,text){
        return record.centerData.filter(item => item.orderNumber == text.orderNumber).length > 0
      },

      scrollTo(index){
        document.getElementsByClassName("ant-table-body")[0].scrollTop = 25*index.target.value
      },
      moment,
      customRequest (data) {
        this.loading = true
        const formData = new FormData()
        formData.append('file', data.file)
        testProgressImport(formData).then((res) => {
          if (res.success) {
            this.$message.success('导入成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('上传失败：' + res.message)
          }
          this.loading = false
        }).finally(() => {
          this.loading = false
        })
      },
      customRow(record){
        return {
          props: {

          },
          on: { // 事件
            click: (event) => {
              this.selectedRows = []
             /* if (this.selectedRows.includes(record)) {
                this.selectedRows = this.selectedRows.filter((r) => r != record);
              } else {*/
                this.selectedRows.push(record);
                this.isUpload = true
                this.importParam.id = this.selectedRows[0].id
                this.importParam.testCode = this.selectedRows[0].testCode
              //}
            },
          },

        };
      },

      onRowClick(row) {
        alert(row)
        if (this.selectedRows.includes(row)) {
          this.selectedRows = this.selectedRows.filter((r) => r !== row);
        } else {
          this.selectedRows.push(row);
        }
      },
      handleOk() {
        this.getList()
      },
      changeDate(event, record) {

        testProgressUpdateData({
          id: record.id,
          inDate: moment(event).format('YYYY-MM-DD')
        }).then().finally(res => this.getList())


      },
      openAdd() {
        this.$refs.testAddForm.add()
      },
      deleteRecord() {
        let select = this.selectedRows

        for (let i = 0; i < select.length; i++) {
          testProgressUpdateOnlyBean({id: select[i].id, status: 1})
        }
        this.getList()
        this.selectedRows = []
        this.selectedRowKeys = []
        this.deleteVisible = false
      },
      openEdit() {

        let num = this.selectedRows.length


        if (num != 1) {
          this.$message.warn('请选中一条数据编辑')
          return
        } else {

          this.$refs.testEditForm.edit(this.data.filter(d => d.id == this.selectedRows[0].id)[0])
        }

      },
      openEditDataSelect() {

        let num = this.selectedRows.length


        if (num != 1) {
          this.$message.warn('请选中一条数据编辑')
          return
        } else {
          if (this.selectedRows[0].stageFlag === 1) {
            this.$message.warn('阶段式日历寿命暂不支持修改')
            return
          }
          this.$refs.testEditDataSelect.edit(this.data.filter(d => d.id == this.selectedRows[0].id)[0])
        }

      },
      clearTestData() {
        if (this.selectedRows.length != 1) {
          this.$message.warn('请选中一条数据编辑')
        } else {
          clearCalendarTestData({ id: this.selectedRows[0].id }).then((res) => {
            if (res.success) {
              this.$message.success('成功')
            } else {
              this.$message.error('失败：' + res.message)
            }
          })
        }
      },
      openChooseOrdTask() {

        let num = this.selectedRows.length


        if (num != 1) {
          this.$message.warn('请选中一条数据编辑')
          return
        } else {

          this.$refs.chooseLimsOrdTask.open(this.data.filter(d => d.id == this.selectedRows[0].id)[0])
        }

      },
      openAllSee() {

        let num = this.selectedRows.length


        if (num != 1) {
          this.$message.warn('请选中一条数据编辑')
          return
        } else {

          this.$refs.testCenterSeeAllForm.add(this.data.filter(d => d.id == this.selectedRows[0].id)[0])
        }

      },

      openCenter(detailId,id) {

        this.$refs.testCenterSeeForm.add(detailId,id)


      },
      openCenterAdd() {

        let num = this.selectedRows.length


        if (num != 1) {
          this.$message.warn('请选中一条数据')
          return
        } else {

          this.$refs.testCenterAddForm.add(this.data.filter(d => d.id == this.selectedRows[0].id)[0])
        }

      },
      openEditActual() {

        let num = this.selectedRows.length


        if (num != 1) {
          this.$message.warn('请选中一条数据编辑')
          return
        } else {
          this.$refs.testEditActualForm.edit(this.data.filter(d => d.id == this.selectedRows[0].id)[0])
        }

      },
      exportData() {
          testProgressExport({testAddress:this.address}).then(response => {

            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', '存储信息表格导出.xls'); // 设置下载文件的名称和扩展名
            document.body.appendChild(link);
            link.click();

        })

      },

      getList(flag) {

        if(flag==true){
          this.queryParam = {}
          this.allAddress = null
          this.queryParam.rangeBegin = null
          this.queryParam.rangeEnd = null
          this.queryParam.range = null
          this.status = []
        }



        this.$refs.table2.refresh()
      },
      importTestData(info) {
        this.fileList = [...info.fileList]
        if (info.file.response.success) {
          this.$message.success(`${info.file.name} 导入数据成功`)
        } else {
          this.$message.error(`${info.file.name} 导入数据失败:` + info.file.response.message)
        }
      },
      beforeUploadCheck() {
        if (this.selectedRows.length === 0) {
          this.$message.warn('请至少选中一条数据')
        }
      },
      validExportSizeOriData(exportType) {
        return validExportSizeOriData({testProgressId: this.selectedRows[0].id, exportType: exportType}).then(res => {
          return Promise.resolve(res);
        })
      },
      exportHandleResult() {
        if (this.selectedRows.length !== 1) {
          this.$message.warning('请选择一条数据')
          return
        }
        this.validExportSizeOriData("handleResult").then(data => {
          if (data.success) {
            exportHandleResult({ testProgressId: this.selectedRows[0].id, alias: this.selectedRows[0].testAlias }).then(res => {
              var alias;
              if (this.selectedRows[0].testAlias) {
                alias = this.selectedRows[0].testAlias
              } else {
                alias = this.selectedRows[0].testProject
              }
              var fileName = this.selectedRows[0].testCode + '-' + this.selectedRows[0].applicant + '-' + alias + '-' + '处理结果' + '.xlsx'
              if (res) {
                downloadfile1(res, fileName)
              }
            })
          } else {
            this.$message.warning(data.message)
          }
        })
      },
      exportSizeOriData() {
        if (this.selectedRows.length !== 1) {
          this.$message.warning('请选择一条数据')
          return
        }
        this.validExportSizeOriData("sizeOriData").then(data => {
          if (data.success) {
            exportSizeOriData({ testProgressId: this.selectedRows[0].id, alias: this.selectedRows[0].testAlias }).then(res => {
              var fileName = this.selectedRows[0].testCode + '-' + this.selectedRows[0].applicant + '-' + this.selectedRows[0].testAlias + '-' + '尺寸原始数据' + '.xlsx'
              if (res) {
                downloadfile1(res, fileName)
              }
            })
          } else {
            this.$message.warning(data.message)
          }
        })
      },
      checkCalendarReportByRecord(record) {
        let checkAllDataFlag = this.userInfo.roles.filter(item => item.code === "check_all_calendar_report")
        // 管理员或者有【查看所有日历寿命测试项目报告】角色的用户
        if (this.userInfo.account === "superAdmin" || checkAllDataFlag.length > 0 || this.userInfo.name === record.applicant) {
          this.getCalendarReport(record)
        } else {
          this.$message.warning('无权限查看其他申请人的报告！')
        }
      },
      checkCalendarReport () {
        if (this.selectedRows.length !== 1) {
          this.$message.warning('请选择一条数据')
          return
        }
        let checkAllDataFlag = this.userInfo.roles.filter(item => item.code === "check_all_calendar_report")
        // 管理员或者有【查看所有日历寿命测试项目报告】角色的用户
        if (this.userInfo.account === "superAdmin" || checkAllDataFlag.length > 0 || this.userInfo.name === this.selectedRows[0].applicant) {
          this.getCalendarReport(this.selectedRows[0])
        } else {
          this.$message.warning('无权限查看其他申请人的报告！')
        }
      },
      getCalendarReport(record) {
        var id = record.id
        var alias;
        if (record.testAlias) {
          alias = record.testAlias
        } else {
          alias = record.testProject
        }
        validExportSizeOriData({ testProgressId: id, exportType: "handleResult" }).then(data => {
          if (data.success) {
            window.open("/v_report_preview?testProgressId=" + id + "&alias=" + encodeURIComponent(alias) + "&type=日历寿命", "_blank")
          } else {
            this.$message.warning(data.message.replace("导出", "查看"))
          }
        })
      },
      dragControllerDiv () {
        var resize = document.getElementsByClassName('resize');
        console.log(resize)
        var left = document.getElementsByClassName('left');
        var mid = document.getElementsByClassName('mid');
        var box = document.getElementsByClassName('box');
        for (let i = 0; i < resize.length; i++) {
          // 鼠标按下事件
          resize[i].onmousedown = function (e) {
            //颜色改变提醒
            resize[i].style.background = '#818181';
            var startX = e.clientX;
            resize[i].left = resize[i].offsetLeft-220;
            // 鼠标拖动事件
            document.onmousemove = function (e) {
              var endX = e.clientX;
              var moveLen = resize[i].left + (endX - startX); // （endx-startx）=移动的距离。resize[i].left+移动的距离=左边区域最后的宽度
              var maxT = box[i].clientWidth - resize[i].offsetWidth; // 容器宽度 - 左边区域的宽度 = 右边区域的宽度

              if (moveLen < 32) moveLen = 32; // 左边区域的最小宽度为32px
              if (moveLen > maxT - 150) moveLen = maxT - 150; //右边区域最小宽度为150px

              resize[i].style.left = moveLen; // 设置左侧区域的宽度

              for (let j = 0; j < left.length; j++) {
                left[j].style.width = moveLen + 'px';
                mid[j].style.width = (box[i].clientWidth - moveLen - 10) + 'px';
              }
            };
            // 鼠标松开事件
            document.onmouseup = function (evt) {
              //颜色恢复
              resize[i].style.background = '#d6d6d6';
              document.onmousemove = null;
              document.onmouseup = null;
              resize[i].releaseCapture && resize[i].releaseCapture(); //当你不在需要继续获得鼠标消息就要应该调用ReleaseCapture()释放掉
            };
            resize[i].setCapture && resize[i].setCapture(); //该函数在属于当前线程的指定窗口里设置鼠标捕获
            return false;
          };
        }
      }


    }
  }
</script>
<style lang="less" scoped=''>
  /deep/ .ant-table-thead > tr > th {
    padding: 5px!important;
    font-size: 14px!important;
  }

  /deep/ .ant-table-tbody > tr > td {
    padding: 0px!important;
    height: 32px!important;
    font-size: 12px!important;
  }



  /deep/ .ant-calendar-picker-icon {
    display: none;
  }
  /deep/.ant-select-selection__rendered {
    margin-right: 0px;
  }

  /deep/ .ant-calendar-picker-input.ant-input {
    color: black;
    font-size: 12px;
    width: 100%;
    text-align: center;
    padding: 0;
  }
  /deep/ .ant-calendar-range-picker-separator {

     vertical-align: unset;

  }

  .red {
    background-color: #ed0000;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .yellow {
    background-color: #ffc000;
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .grey {
    background-color: rgba(223, 223, 223, 0.25);
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .ant-modal-body {
    padding: 0;
  }

  /deep/ .ant-col {
    padding: 0 !important;
    height: 40px !important;
  }

  /deep/.ant-btn > i, /deep/.ant-btn > span {
    display: flex;
    justify-content: center;
  }

  /deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
    color: #fff;
    background: #1890FF;
  }

  .green{
    background-color: #58a55c;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

  }

  /deep/#table1>div>div>div>div>div>div>table>thead{
    height: 64px;
  }

  /deep/#table1>.ant-table-wrapper>div>div>ul{
    display: none;
  }


  /deep/.ant-table-pagination.ant-pagination {
    float: right;
   margin: 0;
  }



</style>