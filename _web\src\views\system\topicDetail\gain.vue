<template>
  <div>
        <div class="topic_wid">
            <a-breadcrumb class="breadcrumb" separator=">">
                <a-breadcrumb-item><a @click="goBack">课题看板</a></a-breadcrumb-item>
                <a-breadcrumb-item>课题成果</a-breadcrumb-item>
            </a-breadcrumb>
        </div>
        
        <x-card class="topic_wid">
            <div slot="content" class="table-page-search-wrapper">
                <a-form layout="inline">
                <a-row :gutter="48">
                    <a-col :md="5" :sm="24">
                        <a-form-item label="立项申请">
                           <a-range-picker
                                size="small"
                                :placeholder="['开始月份', '结束月份']"
                                v-model="dates"
                                :mode="['month', 'month']"
                                format="YYYY-MM"
                                @panelChange="handlePanelChange"
                                @openChange="handleOpenChange"
                                :open="monthPickShow"
                            >
                                <a-icon slot="suffixIcon" type="calendar" style="color:#d9d9d9" />
                            </a-range-picker>
                        </a-form-item>
                    </a-col>
                    <a-col :md="3" :sm="24">
                    <a-form-item label="">
                        <!-- <a-tree-select multiple size="small"
                        :show-checked-strategy="SHOW_PARENT" @change="this.change" v-model="queryParam.cateId" :dropdown-style="{ maxHeight: '130px', overflow: 'auto' }" :tree-data="cate" placeholder="请选择分类" tree-default-expand-all>
                        </a-tree-select> -->
                        <treeselect :limit="1" @input="change" :max-height="200" placeholder="请选择分类" :value-consists-of="ALL" v-model="queryParam.cateId" :multiple="true" :options="cate" :normalizer="normalizer" />
                    </a-form-item>
                    </a-col>
                    <!-- <template v-if="advanced"> -->
                        <a-col :md="3" :sm="24">
                            <a-form-item label="">
                                <!-- <a-tree-select multiple size="small"
                        :show-checked-strategy="SHOW_PARENT" @change="this.change"  v-model="queryParam.deptId" :dropdown-style="{ maxHeight: '130px', overflow: 'auto' }" :tree-data="depts" placeholder="请选择部门" tree-default-expand-all>
                                </a-tree-select> -->
                                <treeselect :limit="1" @input="change" :max-height="200" placeholder="请选择部门" :value-consists-of="ALL" v-model="queryParam.deptId" :multiple="true" :options="depts" :normalizer="normalizer" />
                            </a-form-item>
                        </a-col>
                        <a-col :md="3" :sm="24">
                            <a-form-item label="">
                                <a-input size="small" @keyup.enter.native="change" v-model="queryParam.searchValue" allow-clear placeholder="请输入成果名称"/>
                            </a-form-item>
                        </a-col>
                    <!-- </template> -->
                    <a-col :md="!advanced && 1 || 24" :sm="24">
                    <span class="table-page-search-submitButtons" :style="advanced && { float: 'right', overflow: 'hidden' } || {} ">
                        <!-- <a-button type="primary" @click="callGetAllResults">查询</a-button> -->
                        <a-button size="small" style="margin-left: 8px" @click="resetquery">重置</a-button>
                        <!-- <a @click="toggleAdvanced" style="margin-left: 8px">
                            {{ advanced ? '收起' : '展开' }}
                            <a-icon :type="advanced ? 'up' : 'down'"/>
                        </a> -->
                    </span>
                    </a-col>
                </a-row>
                </a-form>
            </div>
        </x-card>
        <div class="topic_wid head2">成果清单</div>
        <a-table class="topic_wid" :rowKey="(record) => record.issueId" style="background: #fff" :columns="columns" :dataSource="loadData" :loading="loading" :pagination="pagination">
            <span slot="num" slot-scope="text,records,index">
                {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
            </span>
            <template slot="resultName" slot-scope="text">
                <clamp :expend="true" :isCenter="true" :text="text" :sourceText="[text]" :isOneLine='true'></clamp>
            </template>
            <template slot="resultContent" slot-scope="text">
                <clamp :expend="true" :text="text" :sourceText="[text]" :isCenter="true" :isOneLine='true'></clamp>
            </template>
            <template slot="cate" slot-scope="text,record">
                <div v-if="record.resultCateList[0]">{{record.resultCateList[0].value}}</div>
                <div v-if="record.resultCateList[1]">{{record.resultCateList[1].value}}</div>
            </template>
            <template slot="dept" slot-scope="text,record">
                <clamp :expend="true" :isCenter="true" :text="record.departmentCateList.map(e=>e.value).join('-')" :sourceText="[record.departmentCateList.map(e=>e.value).join('-')]" :isOneLine='true'></clamp>
            </template>
            <template slot="resultLeader" slot-scope="text">
                <clamp :expend="true" :isCenter="true" :text="text" :sourceText="[text]" :isOneLine='true'></clamp>
            </template>
            <template slot="action" slot-scope="text,record">
                <a @click="$refs.viewfile.view(record,1)">查阅文件</a>
            </template>
        </a-table>
        <viewfile @preview="preview" ref="viewfile" @ok="handleOk" />
        <a-drawer
			  placement="right"
			  :closable="false"
			  width="80%"
			  :visible="drawerVisible"
			  @close="onClose"
			>
                <!-- <vue-file-viewer :fileUrl="file"  style="height: 500px;" /> -->
			    <!-- <iframe :src="pdfUrl+'#view=FitH,top'" width="100%" height="100%" ></iframe> -->
                <!-- <div ref="file" style="height:100%"></div> -->
		</a-drawer>
    </div>
</template>

<script>
import Vue from 'vue'
import {XCard,clamp} from '@/components'
import {getAllResult,getCateTree,previewFile } from "@/api/modular/system/topic"
import moment from 'moment';
import viewfile from './viewfile'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
/* import { VueFileViewer } from '@zuiyouliao/vue-file-viewer' */
/* let docx = require("docx-preview");
window.JSZip = require("jszip"); */
export default {
    components: {
        XCard,
        clamp,
        viewfile,
        Treeselect
    },
    data() {
        return {
            normalizer(node) {
				return {
					id: node.value,
					label: node.title,
					children: node.children && node.children.length > 0 ? node.children: 0,
				}
			},
            file:'',
            drawerVisible:false,
            dates: [],
            depts:[],
            cate:[],
            monthPickShow: false,
            advanced: false,
            pagination: {
                current: 1,
                pageSize: 8,
                total: 0,
                showSizeChanger: true,
                showQuickJumper: true,
                onChange: (current, size) => {
                    this.pagination.current = current
                    this.pagination.pageSize = size
                },
                onShowSizeChange: (current, pageSize) => {
                    this.pagination.current = 1
                    this.pagination.pageSize = pageSize
                },
            },
            windowHeight: document.documentElement.clientHeight,
            loading: true,
            columns: [
                {
                    title: '序号',
                    width: 40,
                    dataIndex: 'no',
                    align:'center',
                    /* scopedSlots: {
                        customRender: 'num'
                    } */
                    customRender: (text, record, index) => `${index+1}`
                },
                {
                    title: '课题分类',
                    width: 120,
                    dataIndex: 'cate',
                    align:'center',
                    scopedSlots: { customRender: 'cate' },
                },
                {
                    title: '成果名称',
                    width: 120,
                    align:'center',
                    dataIndex: 'resultName',
                    scopedSlots: { customRender: 'resultName' },
                },
/*                {
                    title: '成果内容简介',
                    width: 200,
                    dataIndex: 'resultContent',
                    align:'center',
                    scopedSlots: { customRender: 'resultContent' },
                },*/
                /* {
                    title: '课题等级',
                    width: 40,
                    align:'center',
                    dataIndex: 'projectLevel',
                }, */
                {
                    title: '负责人',
                    width: 80,
                    align:'center',
                    dataIndex: 'resultLeader',
                    scopedSlots: { customRender: 'resultLeader' },
                },
                {
                    title: '部门',
                    width: 120,
                    dataIndex: 'dept',
                    align:'center',
                    scopedSlots: { customRender: 'dept' },
                },
                /* {
                    title: '立项评审日期',
                    width: 80,
                    align:'center',
                    dataIndex: 'planInitiationDate',
                }, */
                {
                    title: '成果认定日期',
                    width: 80,
                    align:'center',
                    dataIndex: 'resultPassDate',
                },
                {
                    title: '操作',
                    width: 120,
                    align:'center',
                    dataIndex: 'action',
                    scopedSlots: { customRender: 'action' },
                },
            ],
            loadData: [],
            queryParam: {},
            loading:false
        }
    },
    methods:{
        preview(attachmentId,attachmentName){
            /* previewFile({issueId:attachmentId}).then((res) => {
                const blob = new Blob([res]);
                docx.renderAsync(blob, this.$refs.file)
                this.drawerVisible = true
            })
            .catch((err) => {
                this.$message.error('错误提示：' + err.message,1)
            }); */
            this.file = url
            this.drawerVisible = true;
        },
        onClose() {
			this.drawerVisible = false;
		},
        goBack(){
            this.$router.push({
                path: "/topic_chart",
            })
        },
        handleOk() {},
        moment,
        resetquery(){
            this.queryParam = {}
            this.dates = [moment().subtract(1, 'year').startOf('year'),moment()],

            this.queryParam.startDate = moment(this.dates[0]._d).format('YYYY-MM')
            this.queryParam.endDate = moment(this.dates[1]._d).format('YYYY-MM')
            this.callGetAllResults()
        },
        change(value, label, extra){
            this.callGetAllResults()
        },
        handlePanelChange(value, mode) {
            /* if (this.dates[1] && this.dates[1]._d != value[1]._d) {
                this.dates = value
                this.monthPickShow = false;
                //this.callChartTopicsPass()
            }
            this.dates = value */

            if (this.dates[1] && this.dates[1]._d != value[1]._d) {
                this.dates = value
                this.monthPickShow = false;
                this.queryParam.startDate = moment(this.dates[0]._d).format('YYYY-MM')
                this.queryParam.endDate = moment(this.dates[1]._d).format('YYYY-MM')
                this.callGetAllResults()
            }
            this.dates = value
        },
        handleOpenChange(status) {
            if(status){
                this.monthPickShow = true;
            }else{
                this.monthPickShow = false
            }
        },
        toggleAdvanced () {
            this.advanced = !this.advanced
        },
        callGetAllResults() {
            this.loading = true
            let params = {flag:1,...this.queryParam}
            getAllResult(params).then((res) => {
            if (res.success) {
                this.loadData = res.data.list
                
            } else {
                this.$message.error('错误提示：' + res.message,1)
            }
            this.loading = false
            })
            .catch((err) => {
                this.$message.error('错误提示：' + err.message,1)
                this.loading = false
            });
        },
        callGetDeptTree(){
            getCateTree({
                fieldName:'department',
                flag:1
            }).then((res)=>{
                if (res.success) {
                    this.depts = res.data
                } else {
                    this.$message.error('错误提示：' + res.message, 1)
                }
            }).catch((err) => {
                this.$message.error('错误提示：' + err.message, 1)
            });
        },

        callGetTree(){
            getCateTree({
                fieldName:'resultCate',
                flag:1
            }).then((res)=>{
                if (res.success) {
                    this.cate = res.data
                } else {
                    this.$message.error('错误提示：' + res.message, 1)
                }
            }).catch((err) => {
                this.$message.error('错误提示：' + err.message, 1)
            });
        },
    },
    created(){
        this.queryParam.deptId = [this.$route.query.deptId]
        this.queryParam.startDate = this.$route.query.startDate
        this.queryParam.endDate = this.$route.query.endDate
        this.dates =[moment(this.$route.query.startDate, 'YYYY-MM'),moment(this.$route.query.endDate, 'YYYY-MM')]
        this.callGetTree()
        this.callGetDeptTree()
        this.callGetAllResults()
    }
}
</script>

<style lang="less" scoped>
@import './topic.less';
</style>