/**
 * 拟合相关的Mixin
 * 提供拟合处理的公共方法，减少组件中的重复代码
 */
import { renderLatex, renderMathJax } from '@/utils/mathUtils';
import { message } from 'ant-design-vue';

export default {
  methods: {
    /**
     * 渲染LaTeX公式
     * @param {string} formula - 公式字符串
     * @returns {string} 渲染后的HTML
     */
    renderLatex(formula) {
      return renderLatex(formula);
    },

    /**
     * 触发MathJax渲染
     * @param {boolean} immediate - 是否立即渲染
     * @param {string} selector - 可选的CSS选择器，用于限制只渲染特定区域的LaTeX内容
     */
    renderMathJax(immediate = false, selector = null) {
      renderMathJax(immediate, selector);
    },

    /**
     * 将参数名称转换为LaTeX格式
     * @param {string} paramName - 参数名称
     * @returns {string} LaTeX格式的参数名称
     */
    convertToLatex(paramName) {
      // 如果已经是LaTeX格式，直接返回
      if (paramName.includes('$') || paramName.includes('_')) {
        return paramName;
      }

      // 提取字母部分和数字部分
      const match = paramName.match(/^([A-Za-z]+)(\d+)$/);
      if (match) {
        const letter = match[1];
        const number = match[2];
        return `$${letter}_{${number}}$`;
      }

      // 如果没有匹配到预期格式，返回原始名称
      return paramName;
    },

    /**
     * 按字母分组系数
     * @param {Array} coefficients - 系数数组
     * @returns {Array} 分组后的系数数组
     */
    groupCoefficientsByLetter(coefficients) {
      if (!coefficients || !Array.isArray(coefficients)) {
        return [];
      }

      const groups = {};

      coefficients.forEach(coefficient => {
        // 获取系数名称的第一个字母
        let name = coefficient.name;
        // 处理可能的下标或花括号
        if (name.includes('_') || name.includes('{')) {
          name = name.split(/[_{]/)[0];
        }
        const firstLetter = name.charAt(0).toUpperCase();

        if (!groups[firstLetter]) {
          groups[firstLetter] = {
            letter: firstLetter,
            coefficients: [],
            description: coefficient.groupDescription || ''
          };
        }

        groups[firstLetter].coefficients.push(coefficient);
      });

      // 将组按字母顺序排序
      return Object.values(groups).sort((a, b) => a.letter.localeCompare(b.letter));
    },

    /**
     * 查找系数所属的组
     * @param {string} name - 系数名称
     * @returns {string|null} 组字母
     */
    findGroupLetterForCoefficient(name) {
      // 假设系数名称格式为 "A_1", "B_2" 等，其中字母表示组
      const match = name.match(/^([A-Za-z])_\d+$/);
      return match ? match[1] : name.charAt(0).toUpperCase();
    },

    /**
     * 格式化指标值，确保正确显示小数
     * @param {number|string} value - 指标值
     * @param {number} precision - 小数位数
     * @returns {string} 格式化后的值
     */
    formatMetricValue(value, precision = 4) {
      if (value === null || value === undefined) return '待计算';

      // 将字符串转换为数字
      const numValue = typeof value === 'string' ? parseFloat(value) : value;

      // 检查是否为有效数字
      if (isNaN(numValue)) return '待计算';

      // 后端返回的值已经是小数形式（如0.05表示5%），需要乘以100转换为百分比显示
      return (numValue * 100).toFixed(precision).replace(/\.?0+$/, '');
    },

    /**
     * 生成有效的权重配置
     * @param {Object} weightConfig - 权重配置
     * @param {Array} dataPoints - 数据点
     * @returns {Object} 有效的权重配置
     */
    generateValidWeightConfig(weightConfig, dataPoints) {
      // 从数据点中提取所有可能的天数
      const allDays = [];
      if (dataPoints && dataPoints.length > 0) {
        dataPoints.forEach(point => {
          if (point.days && Array.isArray(point.days)) {
            point.days.forEach(day => {
              if (!allDays.includes(day)) {
                allDays.push(day);
              }
            });
          }
        });
      }

      // 根据days_min和days_max筛选天数
      const min = parseInt(weightConfig.days_min) || 0;
      const max = parseInt(weightConfig.days_max) || 1000;
      const selectedDays = allDays.filter(day => day >= min && day <= max);

      return {
        selected_temperatures: weightConfig.selected_temperatures || [],
        temperatures_weight: parseFloat(weightConfig.temperatures_weight) || 1.0,
        selected_socs: weightConfig.selected_socs || [],
        socs_weight: parseFloat(weightConfig.socs_weight) || 1.0,
        selected_days: selectedDays, // 自动根据范围生成selected_days
        days_min: min,
        days_max: max,
        days_weight: parseFloat(weightConfig.days_weight) || 1.0
      };
    },

    /**
     * 验证拟合数据
     * @param {boolean} dataLoaded - 数据是否已加载
     * @param {Array} dataPoints - 数据点
     * @returns {boolean} 数据是否有效
     */
    validateFittingData(dataLoaded, dataPoints) {
      if (!dataLoaded || !dataPoints || dataPoints.length === 0) {
        message.warning('请先导入实验数据');
        return false;
      }
      return true;
    },

    /**
     * 根据SOC值获取颜色
     * @param {number} soc - SOC值
     * @param {number} alpha - 透明度
     * @param {Array} colors - 颜色数组
     * @param {Array} socs - SOC数组
     * @returns {string} 颜色字符串
     */
    getColorBySoc(soc, alpha = 1, colors, socs) {
      // 确保soc是数字类型
      const socValue = typeof soc === 'string' ? parseFloat(soc) : soc;

      // 首先尝试使用全局SOC列表索引来确定颜色
      if (socs && socs.length > 0) {
        // 查找SOC值在全局SOC列表中的索引
        let socIndex = -1;
        for (let i = 0; i < socs.length; i++) {
          const globalSoc = typeof socs[i] === 'string' ? parseFloat(socs[i]) : socs[i];
          if (Math.abs(globalSoc - socValue) < 0.01) {
            socIndex = i;
            break;
          }
        }

        // 如果找到索引，使用该索引选择颜色
        if (socIndex >= 0) {
          const color = colors[socIndex % colors.length];
          return color.replace(/[\d.]+\)$/, `${alpha})`);
        }
      }

      // 如果没有找到索引或没有全局SOC列表，使用哈希算法生成唯一颜色
      const hashValue = Math.abs(socValue * 1000);  // 通过放大SOC值使得差异更明显
      const colorIndex = hashValue % colors.length;
      const color = colors[colorIndex];

      // 替换颜色透明度
      return color.replace(/[\d.]+\)$/, `${alpha})`);
    }
  }
};
