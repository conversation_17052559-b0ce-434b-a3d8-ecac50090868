<template>
  <div class="container">
<!--    <div class="topic_wid">
      <a-breadcrumb class="breadcrumb" separator="/">
        <a-breadcrumb-item>当前位置：首页</a-breadcrumb-item>
        <a-breadcrumb-item>科研管理</a-breadcrumb-item>
        <a-breadcrumb-item>竞品动态</a-breadcrumb-item>
        <a-breadcrumb-item>竞品分析</a-breadcrumb-item>
        <a-breadcrumb-item>竞品分析报告管理</a-breadcrumb-item>
      </a-breadcrumb>
    </div>-->
    <pbiTitle title="产品认证管理" style="margin-top: 8px"></pbiTitle>
    <div class="secondary-btn mr12" style="text-align: right;margin-top: -18px;">
      <a-button @click="openQuery" size="small" type="primary">产品认证查询</a-button>
    </div>
    <div class="content" :style="{marginTop: '8px'}">


      <div class="left mr10">
        <div class="left-top">
          <div class="flex-sb-center-row">

            <div style="font-weight: bolder">认证报告:{{treeData.reportNum || '0'}}</div>
            <div class="btn-wrap mt5">
              <span>有效:{{treeData.validNum || '0' }}</span>
              <a-divider type="vertical" style="color: #333;" />
              <span>过期:{{treeData.expireNum || '0' }}</span>
            </div>
          </div>
          <div style="display: flex;margin-top: 10px">
            <div style="align-content: center;width: 25%;font-size: 12px;">产品名称：</div>
            <a-input  v-model="fileName" @keyup.enter="getTreeData" style="width: 30%;margin-right: 12px"></a-input>
            <a-button type="primary"  @click='getTreeData' class="btn-wrap mt5" style="padding: 0 5px;margin-right:3px;width: 20%"><a-icon type="search"></a-icon>搜索</a-button>
            <a-button type="primary"  @click='openFileManager' class="btn-wrap mt5" style="padding: 0 5px;width: 20%">管理</a-button>
          </div>

        </div>

        <div class="left-bottom">

          <a-directory-tree @select="dbclick" :expandedKeys="expandedKeys" :selectedKeys="selectedKeys">
            <a-icon slot="switcherIcon" type="down" />
            <a-tree-node :key="item.id" :title="item.productName" v-for="item in treeData.list" >
              <template slot="icon">
                <svg t="1733186268538" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5379" id="mx_n_1733186268538" width="22" height="22"><path d="M848.8576 199.1936H415.7568c0-26.5728-21.5424-48.128-48.128-48.128H175.1424c-26.5728 0-48.128 21.5424-48.128 48.128V343.5648c0 26.5984 21.5424 48.1408 48.128 48.1408h673.728c26.5728 0 48.128-21.5424 48.128-48.1408v-96.2432c-0.0128-26.5856-21.5552-48.128-48.1408-48.128z" fill="#CCA352" p-id="5380"></path><path d="M800.7424 247.3088H223.2576c-26.5728 0-48.128 21.5424-48.128 48.128v48.128c0 26.5984 21.5424 48.1408 48.128 48.1408h577.472c26.5728 0 48.128-21.5424 48.128-48.1408v-48.128c0-26.5728-21.5424-48.128-48.1152-48.128z" fill="#FFFFFF" p-id="5381"></path><path d="M848.8576 295.4368H175.1424c-26.5728 0-48.128 21.5424-48.128 48.128v481.2544c0 26.5472 21.5424 48.128 48.128 48.128h673.728c26.5728 0 48.128-21.568 48.128-48.128V343.552c-0.0128-26.5728-21.5552-48.1152-48.1408-48.1152z" fill="#FFCC66" p-id="5382"></path></svg>
              </template>
              <a-tree-node :key="inItem.id" :title="inItem.type" v-for="inItem in item.list">
                <template slot="icon">
                  <svg t="1733186268538" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5379" id="mx_n_1733186268538" width="22" height="22"><path d="M848.8576 199.1936H415.7568c0-26.5728-21.5424-48.128-48.128-48.128H175.1424c-26.5728 0-48.128 21.5424-48.128 48.128V343.5648c0 26.5984 21.5424 48.1408 48.128 48.1408h673.728c26.5728 0 48.128-21.5424 48.128-48.1408v-96.2432c-0.0128-26.5856-21.5552-48.128-48.1408-48.128z" fill="#CCA352" p-id="5380"></path><path d="M800.7424 247.3088H223.2576c-26.5728 0-48.128 21.5424-48.128 48.128v48.128c0 26.5984 21.5424 48.1408 48.128 48.1408h577.472c26.5728 0 48.128-21.5424 48.128-48.1408v-48.128c0-26.5728-21.5424-48.128-48.1152-48.128z" fill="#FFFFFF" p-id="5381"></path><path d="M848.8576 295.4368H175.1424c-26.5728 0-48.128 21.5424-48.128 48.128v481.2544c0 26.5472 21.5424 48.128 48.128 48.128h673.728c26.5728 0 48.128-21.568 48.128-48.128V343.552c-0.0128-26.5728-21.5552-48.1152-48.1408-48.1152z" fill="#FFCC66" p-id="5382"></path></svg>
                </template>
                <a-tree-node :key="in2Item.id" :title="in2Item.phase" v-for="in2Item in inItem.list">
                  <template slot="icon">
                    <svg t="1733186268538" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5379" id="mx_n_1733186268538" width="22" height="22"><path d="M848.8576 199.1936H415.7568c0-26.5728-21.5424-48.128-48.128-48.128H175.1424c-26.5728 0-48.128 21.5424-48.128 48.128V343.5648c0 26.5984 21.5424 48.1408 48.128 48.1408h673.728c26.5728 0 48.128-21.5424 48.128-48.1408v-96.2432c-0.0128-26.5856-21.5552-48.128-48.1408-48.128z" fill="#CCA352" p-id="5380"></path><path d="M800.7424 247.3088H223.2576c-26.5728 0-48.128 21.5424-48.128 48.128v48.128c0 26.5984 21.5424 48.1408 48.128 48.1408h577.472c26.5728 0 48.128-21.5424 48.128-48.1408v-48.128c0-26.5728-21.5424-48.128-48.1152-48.128z" fill="#FFFFFF" p-id="5381"></path><path d="M848.8576 295.4368H175.1424c-26.5728 0-48.128 21.5424-48.128 48.128v481.2544c0 26.5472 21.5424 48.128 48.128 48.128h673.728c26.5728 0 48.128-21.568 48.128-48.128V343.552c-0.0128-26.5728-21.5552-48.1152-48.1408-48.1152z" fill="#FFCC66" p-id="5382"></path></svg>
                  </template>
                  <a-tree-node :key="file.id" :title="file.testReportCode" v-for="file in in2Item.list" is-leaf :on="{click : preview}" :value="file" >
                    <template slot="icon">
                      <svg t="1733187029740" v-if="file.fileName.indexOf('pdf') > -1 || file.fileName.indexOf('PDF') > -1" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15201" width="18" height="24"><path d="M870.4 1024h-716.8A51.2 51.2 0 0 1 102.4 972.8v-921.6A51.2 51.2 0 0 1 153.6 0h569.685333a42.666667 42.666667 0 0 1 30.037334 12.288l155.989333 155.989333a42.666667 42.666667 0 0 1 12.288 30.037334V972.8a51.2 51.2 0 0 1-51.2 51.2zM153.6 34.133333a17.066667 17.066667 0 0 0-17.066667 17.066667v921.6a17.066667 17.066667 0 0 0 17.066667 17.066667h716.8a17.066667 17.066667 0 0 0 17.066667-17.066667V198.314667a7.168 7.168 0 0 0-2.389334-5.802667l-155.989333-155.989333a7.168 7.168 0 0 0-5.802667-2.389334z" fill="#4D4D4D" p-id="15202"></path><path d="M904.533333 204.8h-170.666666a17.066667 17.066667 0 0 1-17.066667-17.066667v-170.666666h34.133333V170.666667h153.6z" fill="#4D4D4D" p-id="15203"></path><path d="M204.8 170.666667h443.733333v34.133333H204.8zM204.8 307.2h614.4v34.133333H204.8zM204.8 443.733333h614.4v34.133334H204.8zM204.8 580.266667h614.4v34.133333H204.8zM204.8 853.333333h614.4v34.133334H204.8zM204.8 716.8h614.4v34.133333H204.8z" fill="#B3B3B3" p-id="15204"></path><path d="M51.2 460.8m17.066667 0l887.466666 0q17.066667 0 17.066667 17.066667l0 273.066666q0 17.066667-17.066667 17.066667l-887.466666 0q-17.066667 0-17.066667-17.066667l0-273.066666q0-17.066667 17.066667-17.066667Z" fill="#F33958" p-id="15205"></path><path d="M955.733333 477.866667v273.066666H68.266667v-273.066666h887.466666m0-34.133334H68.266667a34.133333 34.133333 0 0 0-34.133334 34.133334v273.066666a34.133333 34.133333 0 0 0 34.133334 34.133334h887.466666a34.133333 34.133333 0 0 0 34.133334-34.133334v-273.066666a34.133333 34.133333 0 0 0-34.133334-34.133334z" fill="#C42E47" p-id="15206"></path><path d="M348.16 530.090667a55.978667 55.978667 0 1 1 0 111.616H307.2v57.002666h-24.917333v-168.618666zM307.2 618.837333h34.133333c22.528 0 35.84-11.605333 35.84-32.426666s-12.970667-34.133333-35.84-34.133334H307.2zM509.952 530.090667A74.410667 74.410667 0 0 1 589.141333 614.4a75.093333 75.093333 0 0 1-79.189333 84.992h-60.757333v-169.301333z m-34.133333 144.725333h31.744A52.906667 52.906667 0 0 0 562.858667 614.4a53.248 53.248 0 0 0-55.637334-60.074667h-31.744zM636.586667 698.709333v-168.618666h105.130666v23.893333h-78.848v51.882667h72.021334v22.869333h-72.021334v68.266667z" fill="#FFFFFF" p-id="15207"></path></svg>
                      <!--                    <a-icon type="file-pdf" v-if="file.fileName == 'pdf'"/>-->
                      <svg t="1733187630326" v-else-if="file.fileName == 'xlsx' || file.fileName == 'xls'"  class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="18213" width="18" height="24"><path d="M682.666667 42.666667H298.666667c-25.6 0-42.666667 17.066667-42.666667 42.666666v213.333334l426.666667 213.333333 170.666666 64 170.666667-64V298.666667l-341.333333-256z" fill="#21A366" p-id="18214"></path><path d="M256 298.666667h426.666667v213.333333H256z" fill="#107C41" p-id="18215"></path><path d="M1024 85.333333v213.333334h-341.333333V42.666667h298.666666c21.333333 0 42.666667 21.333333 42.666667 42.666666z" fill="#33C481" p-id="18216"></path><path d="M682.666667 512H256v426.666667c0 25.6 17.066667 42.666667 42.666667 42.666666h682.666666c25.6 0 42.666667-17.066667 42.666667-42.666666v-213.333334l-341.333333-213.333333z" fill="#185C37" p-id="18217"></path><path d="M588.8 256H256v597.333333h324.266667c29.866667 0 59.733333-29.866667 59.733333-59.733333V307.2c0-29.866667-21.333333-51.2-51.2-51.2z" opacity=".5" p-id="18218"></path><path d="M546.133333 810.666667H51.2C21.333333 810.666667 0 789.333333 0 759.466667V264.533333C0 234.666667 21.333333 213.333333 51.2 213.333333h499.2c25.6 0 46.933333 21.333333 46.933333 51.2v499.2c0 25.6-21.333333 46.933333-51.2 46.933334z" fill="#107C41" p-id="18219"></path><path d="M145.066667 682.666667L256 512 153.6 341.333333h81.066667l55.466666 106.666667c8.533333 12.8 8.533333 21.333333 12.8 25.6l12.8-25.6L375.466667 341.333333h76.8l-102.4 170.666667 106.666666 170.666667h-85.333333l-64-119.466667c0-4.266667-4.266667-8.533333-8.533333-17.066667 0 4.266667-4.266667 8.533333-8.533334 17.066667L226.133333 682.666667H145.066667z" fill="#FFFFFF" p-id="18220"></path><path d="M682.666667 512h341.333333v213.333333h-341.333333z" fill="#107C41" p-id="18221"></path></svg>
                      <!--                    <a-icon type="file-excel" v-else-if="file.fileName == 'xlxs'"/>-->
                      <svg t="1733187718798" v-else-if="file.fileName == 'pptx'" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19328" width="18" height="24"><path d="M538.731891 0h65.98683v107.168391c124.387582 0.722484 248.895579-1.324553 373.28316 0a40.699906 40.699906 0 0 1 45.034808 46.118533c2.047037 222.404516 0 444.929445 1.204139 667.454374-1.204139 24.082785 2.287865 50.694262-11.198495 72.248354-16.978363 12.041392-39.014111 10.957667-59.002822 12.041392-116.319849-0.60207-232.639699 0-349.200376 0V1023.518344h-72.248354C355.100659 990.886171 177.490122 960.662277 0 928.752587V95.488241C179.537159 63.698965 359.074318 31.30762 538.731891 0z" fill="#D24625" p-id="19329"></path><path d="M604.718721 142.931326H988.598307v726.216369H604.718721v-95.247413h279.239887v-47.563499H604.718721v-60.206962h279.239887v-46.96143H604.839135v-69.960489c46.118532 14.570085 98.619003 14.208843 139.800564-14.088429 44.553151-27.093133 67.793039-78.630292 71.646284-130.047036H663.119473c0-51.777987 0.60207-103.555974-0.963311-155.213547-19.145814 3.732832-38.171214 7.826905-57.196614 12.041392z" fill="#FFFFFF" p-id="19330"></path><path d="M686.35936 224.69238a165.689558 165.689558 0 0 1 153.16651 156.5381c-51.055503 0.60207-102.111007 0-153.286924 0 0.120414-52.380056 0.120414-104.278457 0.120414-156.5381z" fill="#D24625" p-id="19331"></path><path d="M186.64158 314.521167c63.21731 3.130762 139.680151-25.527752 192.662277 22.878645 50.092192 62.374412 36.84666 176.888053-37.44873 214.095955-26.370649 13.847601-56.714958 12.041392-85.373471 10.957667v139.68015l-69.238006-5.900282c-1.806209-127.157103-2.047037-254.434619-0.60207-381.712135z" fill="#FFFFFF" p-id="19332"></path><path d="M255.759172 378.942615c22.878645-0.963311 51.296331-5.298213 66.709313 16.737536a87.902164 87.902164 0 0 1 1.565381 78.148635c-13.245532 24.082785-43.228598 22.035748-66.468485 24.925682-2.408278-39.857008-2.167451-79.714017-1.806209-119.811853z" fill="#D24625" p-id="19333"></path></svg>
                      <svg t="1733187804936" class="icon" v-else-if="file.fileName == 'doc' || file.fileName == 'docx'" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="20412" width="18" height="24"><path d="M950.272 843.776H527.36c-16.384 0-29.696-13.312-29.696-29.696V210.944c0-16.384 13.312-29.696 29.696-29.696h422.912c16.384 0 29.696 13.312 29.696 29.696v603.136c0 16.384-13.312 29.696-29.696 29.696z" fill="#E8E8E8" p-id="20413"></path><path d="M829.44 361.472H527.36c-16.384 0-29.696-13.312-29.696-29.696s13.312-29.696 29.696-29.696H829.44c16.384 0 29.696 13.312 29.696 29.696 0 15.36-13.312 29.696-29.696 29.696z m0 120.832H527.36c-16.384 0-29.696-13.312-29.696-29.696s13.312-29.696 29.696-29.696H829.44c16.384 0 29.696 13.312 29.696 29.696s-13.312 29.696-29.696 29.696z m0 119.808H527.36c-16.384 0-29.696-13.312-29.696-29.696s13.312-29.696 29.696-29.696H829.44c16.384 0 29.696 13.312 29.696 29.696s-13.312 29.696-29.696 29.696z m0 120.832H527.36c-16.384 0-29.696-13.312-29.696-29.696s13.312-29.696 29.696-29.696H829.44c16.384 0 29.696 13.312 29.696 29.696s-13.312 29.696-29.696 29.696z" fill="#B2B2B2" p-id="20414"></path><path d="M607.232 995.328l-563.2-107.52V135.168l563.2-107.52v967.68z" fill="#0D47A1" p-id="20415"></path><path d="M447.488 696.32h-71.68l-47.104-236.544c-3.072-13.312-4.096-27.648-4.096-40.96h-1.024c-1.024 16.384-3.072 30.72-5.12 40.96L269.312 696.32H194.56l-74.752-368.64h70.656l39.936 245.76c2.048 10.24 3.072 24.576 4.096 41.984h1.024c0-13.312 3.072-27.648 6.144-43.008l51.2-244.736h68.608l47.104 247.808c2.048 9.216 3.072 22.528 4.096 39.936h1.024c1.024-13.312 2.048-26.624 4.096-40.96l39.936-245.76H522.24L447.488 696.32z" fill="#FFFFFF" p-id="20416"></path></svg>
                      <svg t="1733188117407" class="icon"  v-else-if="file.fileName == 'jpg' || file.fileName == 'JPG' || file.fileName == 'jepg' || file.fileName == 'png'|| file.fileName == 'PNG'" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="39515" width="18" height="14"><path d="M917.09952 84.6848H114.41152c-32.83968 0-59.45856 26.624-59.45856 59.45856v772.96128c0 32.83456 26.624 59.45344 59.45856 59.45344h802.688c32.83456 0 59.45344-26.61888 59.45344-59.45344V144.13824c0-32.82944-26.61888-59.45344-59.45344-59.45344z" fill="" p-id="39516"></path><path d="M917.09952 54.95296H114.41152c-32.83968 0-59.45856 26.624-59.45856 59.45856V887.35232c0 32.8448 26.624 59.46368 59.45856 59.46368h802.688c32.83456 0 59.45344-26.61888 59.45344-59.46368V114.41152c0-32.83456-26.61888-59.45856-59.45344-59.45856z" fill="#ECEAE0" p-id="39517"></path><path d="M872.50432 114.41152H159.00672a44.5952 44.5952 0 0 0-44.5952 44.5952V590.07488h802.688V159.00672a44.5952 44.5952 0 0 0-44.5952-44.5952z" fill="#98DCF0" p-id="39518"></path><path d="M613.63712 411.55584l-154.94144 178.51904h309.86752z" fill="#699B54" p-id="39519"></path><path d="M586.82368 590.07488l-206.53568-237.9776-206.5408 237.9776H114.41152V694.12352a44.5952 44.5952 0 0 0 44.5952 44.5952h713.4976a44.5952 44.5952 0 0 0 44.5952-44.5952v-104.05376h-330.27584z" fill="#80BB67" p-id="39520"></path><path d="M768.44544 263.05536m-59.45856 0a59.45856 59.45856 0 1 0 118.91712 0 59.45856 59.45856 0 1 0-118.91712 0Z" fill="#FFE68E" p-id="39521"></path></svg>
                      <svg t="1733188265050" class="icon" v-else viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="41415" width="18" height="24"><path d="M847.488 1008.896H61.504V117.056h491.968l294.016 229.952z" fill="#F0A221" p-id="41416"></path><path d="M956.48 901.888H170.496V10.048h556.032l230.016 226.176v665.664zM729.984 102.016v144h144l-144-144z m-104.96 81.984H286.016v57.984h339.008v-57.984z m216.96 164.992H284.992v57.984h556.992v-57.984z m0 169.984H284.992v57.984h556.992v-57.984z m0 165.056H284.992v57.984h556.992v-57.984z" fill="#F1C84C" p-id="41417"></path></svg>
                      <!--                    <a-icon type="file-ppt" v-else-if="file.fileSuffix == 'pptx'"/>-->
                      <!--                    <a-icon type="file-word" v-else-if="file.fileSuffix == 'doc'"/>-->
                    </template>
                  </a-tree-node>
                </a-tree-node>
              </a-tree-node>


            </a-tree-node>
          </a-directory-tree>

        </div>
      </div>
      <div v-if="!showFile" class="right">
        <div class="right-top">
          <div class="overview-block">
            <div>
              <div style="font-size: 16px;font-weight: bold;float: left;">产品认证一览表</div>
              <a-radio-group @change="getEchartsData" v-model="competitiveType" style="float: right;position: relative;z-index: 100;">
                <a-radio-button value="all">
                  总览
                </a-radio-button>
                <a-radio-button value="2024">
                  2024
                </a-radio-button>
                <a-radio-button value="2025">
                  2025
                </a-radio-button>
              </a-radio-group>
            </div>
            <div class="chart_bar_table" ref="gainOverview"></div>
          </div>
        </div>
        <tableIndex
          :pageLevel='4'
          :pageTitleShow=false
          :loading='loading'
          @paginationChange="handlePageChange"
          @paginationSizeChange="handlePageChange"
          :tableTotal="tableTotal"
          @tableFocus="tableFocus"
          @tableBlur="tableBlur"
        >
          <template #search>
            <div style="font-size: 16px;font-weight: bold;float: left;display: contents;">认证情况概览</div>
            <pbiSearchContainer>
              <pbiSearchItem :span="6" label='产品名称'>
                <a-input @change='getPageData' v-model="queryParam.productName"></a-input>
              </pbiSearchItem>
              <pbiSearchItem :span="6" label='样品阶段'>
                <a-input @change='getPageData' v-model="queryParam.phase"></a-input>
              </pbiSearchItem>
              <pbiSearchItem type='btn' :span="12">

                <div class="secondary-btn mr12">
                  <a-button @click="getPageData" size="small" type="primary">查询</a-button>
                </div>
                <div class="secondary-btn">
                  <a-button @click="reset" size="small">重置</a-button>
                </div>
              </pbiSearchItem>

            </pbiSearchContainer>
            <div style="display: flow-root;margin-bottom: 5px">
              <a-radio-group @change="getEchartsData" v-model="tableType" style="margin-left:10px;float: left;position: relative;z-index: 100;">
                <a-radio-button value="normal">
                  健康状态
                </a-radio-button>
                <a-radio-button value="other">
                  有效期
                </a-radio-button>
              </a-radio-group>
              <div style="float: right;font-size: smaller;padding-top: 12px;" v-if='tableType != "normal"'>
                注：“-”表示该报告无有效期限制，<span style="color:green">绿色日期</span>表示有效期内，<span style="color:orange">黄色日期</span>表示有效期<90天，<span style="color:red">红色日期</span>红色日期表示已过期
              </div>
              <div style="float: right;font-size: smaller;padding-top: 12px;display: flex" v-if='tableType == "normal"'>
                <div style="width: 10px;background: green;height: 10px;border-radius: 50%;margin-right: 5px;"></div>
                <div style="margin-right: 10px">有效</div>
                <div style="width: 10px;background: orange;height: 10px;border-radius: 50%;margin-right: 5px;"></div>
                <div style="margin-right: 10px">有效期<90天</div>
                <div style="width: 10px;background: red;height: 10px;border-radius: 50%;margin-right: 5px;"></div>
                <div >已过期</div>
              </div>
            </div>

          </template>
          <template #table>
            <ag-grid-vue :style="`height: ${tableHeight}px`"
                         class='table ag-theme-balham'
                         :columnDefs='tableType == "normal"?secondColumns:columns'
                         :rowData='pageData'
                         :defaultColDef='defaultColDef'>
            </ag-grid-vue>
          </template>
        </tableIndex>
      </div>
      <div v-show="showFile" class="right" style="background: white">
        <file-manager-div ref="fileManagerDiv" />
      </div>

    </div>
    <!--<a-drawer
      :bodyStyle="{ height: '100%' }"
      placement="right"
      :closable="false"
      width="80%"
      :destroyOnClose="true"
      :visible="filePreviewVisible"
      @close="filePreviewVisible = false"
    >
      <iframe :src="iframeUrl" width="100%" height="100%"></iframe>
    </a-drawer>-->

    <pbi-preview-new ref="pbiPreviewNew" width="80%" :show-file-name="true"/>
    <file-manager ref="fileManager" />

  </div>



</template>

<script>
import {
  testProjectOutTestDataGetManagerFileList, testProjectOutTestDataEchartsData,testProjectOutTestDataGetManagerTable
} from "@/api/modular/system/dpvTestManage";
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import Vue from "vue";
import {getMinioDownloadUrl} from "@/api/modular/system/fileManage";
import {downloadMinioFile} from "@/utils/util";
import PbiPreviewNew from "@/components/pageTool/components/pbiPreviewNew";
import fileManager from "./fileManager.vue";
import fileManagerDiv from "./fileManagerDiv.vue";
export default {
  components: {
    fileManager,
    fileManagerDiv,
    PbiPreviewNew,
    fileView:{
      template:`
                <a @click="params.preview2(params.data)" :title="params.value">
                        {{params.value}}
                  </a>
                  `
    },
    date:{
      template:`
                <div v-if="params.value != null" :style="{textAlign:'center',color:params.data[params.column.colId+'Status'] == 'valid'?'green':params.data[params.column.colId+'Status'] == 'nearExpire'?'orange':'red'}">{{params.value}}</div>
                <div v-else-if="params.value == null && params.data[params.column.colId+'Status'] == 'valid'" style="text-align: center">-</div>
                  `
    },
    status:{
      template:`
                <div v-if="params.value != null"
                style="width: 10px;height: 10px;border-radius:50%;display: inline-flex;align-items: center;"
                :style="{textAlign:'center',background:params.value == 'valid'?'green':params.value == 'nearExpire'?'orange':'red'}">
                </div>
                  `
    }
  },
  data() {
    return {
      expandedKeys:[],
      selectedKeys:[],
      showFile:false,
      iframeUrl:'',
      filePreviewVisible:false,
      fileName:null,
      pageNo: 1,
      pageSize: 20,
      tableTotal:0,
      echartsData:{
        allCount: 0,
        allDisassembleCount:0,
        allSavingCount:0,
        allTestingCount:0
      },
      defaultColDef: {
        filter: false,
        floatingFilter: false,
        editable: false,
      },

      isShowAllSearch: false,
      windowHeight: document.documentElement.clientHeight,
      templateHeight: document.documentElement.clientHeight * 0.7 - 40 - 20 - 24 - 10 - 32 - 10 - 14 - 20 - 20 - 6,
      tableHeight: 0,
      loading: true,
      columns: [
        {
          headerName: '序号',
          field: 'no',
          width: 50,
          cellRenderer: function (params) {
            return parseInt(params.node.id) + 1
          },
        },
        {
          headerName: '产品名称',
          field: 'productName',
          width: 110,
        },
        {
          headerName: '样品阶段',
          field: 'phase',
          width: 90
        },{
          headerName: '强检',
          children:[
            {
              headerName: 'GB/T 31484',
              field: 'gb31484',
              width: 110,
              cellRenderer: 'date',
            },{
              headerName: 'GB 38031',
              field: 'gb38031',
              width: 110,
              cellRenderer: 'date',
            },{
              headerName: 'GB/T 31486',
              field: 'gb31486',
              width: 110,
              cellRenderer: 'date'
            }
          ]
        },{
          headerName: 'CCC',
          children:[
            {
              headerName: 'GB 31241',
              field: 'gb31241',
              width: 110,
              cellRenderer: 'date'
            },{
              headerName: 'GB 43854',
              field: 'gb43854',
              width: 110,
              cellRenderer: 'date'
            }
          ]
        },{
          headerName: 'UN38.3',
          children:[
            {
              headerName: 'UN38.3检测报告',
              field: 'un383',
              width: 150,
              cellRenderer: 'date'
            },{
              headerName: 'UN38.3实验概要',
              field: 'un383Test',
              width: 150,
              cellRenderer: 'date'
            },{
              headerName: '空运',
              field: 'air',
              width: 110,
              cellRenderer: 'date'
            },{
              headerName: '海运',
              field: 'sea',
              width: 110,
              cellRenderer: 'date'
            },{
              headerName: '铁路',
              field: 'rail',
              width: 110,
              cellRenderer: 'date'
            },{
              headerName: '公路',
              field: 'road',
              width: 110,
              cellRenderer: 'date'
            },{
              headerName: 'MSDS',
              field: 'msds',
              width: 110,
              cellRenderer: 'date'
            },{
              headerName: '危险特性类别鉴定',
              field: 'dangerous',
              width: 150,
              cellRenderer: 'date'
            }
          ]
        },{
          headerName: 'IEC',
          children:[
            {
              headerName: 'IEC 62133',
              field: 'iec62133',
              width: 110,
              cellRenderer: 'date'
            },{
              headerName: 'IEC 62619',
              field: 'iec62619',
              width: 110,
              cellRenderer: 'date'
            },{
              headerName: 'IEC 62660',
              field: 'iec62660',
              width: 110,
              cellRenderer: 'date'
            }
          ]
        },{
          headerName: 'UL',
          children:[
            {
              headerName: 'UL 1642',
              field: 'ul1642',
              width: 110,
              cellRenderer: 'date'
            },{
              headerName: 'UL 1973',
              field: 'ul1973',
              width: 110,
              cellRenderer: 'date'
            },{
              headerName: 'UL 9540A',
              field: 'ul9540A',
              width: 110,
              cellRenderer: 'date'
            }
          ]
        },{
          headerName: 'BIS',
          children:[
            {
              headerName: 'IS 16046',
              field: 'bis16046',
              width: 110,
              cellRenderer: 'date'
            }
          ]
        },{
          headerName: 'IS16893',
          children:[
            {
              headerName: '1',
              field: 'is168931',
              width: 110,
              cellRenderer: 'date'
            },{
              headerName: '2',
              field: 'is168932',
              width: 110,
              cellRenderer: 'date'
            }
          ]
        }

      ],
      secondColumns: [
        {
          headerName: '序号',
          field: 'no',
          width: 50,
          cellRenderer: function (params) {
            return parseInt(params.node.id) + 1
          },
        },
        {
          headerName: '产品名称',
          field: 'productName',
          width: 110,
        },
        {
          headerName: '样品阶段',
          field: 'phase',
          width: 90
        },{
          headerName: '强检',
          children:[
            {
              headerName: 'GB/T 31484',
              field: 'gb31484Status',
              width: 110,
              cellRenderer: 'status',
            },{
              headerName: 'GB 38031',
              field: 'gb38031Status',
              width: 110,
              cellRenderer: 'status',
            },{
              headerName: 'GB/T 31486',
              field: 'gb31486Status',
              width: 110,
              cellRenderer: 'status'
            }
          ]
        },{
          headerName: 'CCC',
          children:[
            {
              headerName: 'GB 31241',
              field: 'gb31241Status',
              width: 110,
              cellRenderer: 'status'
            },{
              headerName: 'GB 43854',
              field: 'gb43854Status',
              width: 110,
              cellRenderer: 'status'
            }
          ]
        },{
          headerName: 'UN38.3',
          children:[
            {
              headerName: 'UN38.3检测报告',
              field: 'un383Status',
              width: 150,
              cellRenderer: 'status'
            },{
              headerName: 'UN38.3实验概要',
              field: 'un383TestStatus',
              width: 150,
              cellRenderer: 'status'
            },{
              headerName: '空运',
              field: 'airStatus',
              width: 110,
              cellRenderer: 'status'
            },{
              headerName: '海运',
              field: 'seaStatus',
              width: 110,
              cellRenderer: 'status'
            },{
              headerName: '铁路',
              field: 'railStatus',
              width: 110,
              cellRenderer: 'status'
            },{
              headerName: '公路',
              field: 'roadStatus',
              width: 110,
              cellRenderer: 'status'
            },{
              headerName: 'MSDS',
              field: 'msdsStatus',
              width: 110,
              cellRenderer: 'status'
            },{
              headerName: '危险特性类别鉴定',
              field: 'dangerousStatus',
              width: 150,
              cellRenderer: 'status'
            }
          ]
        },{
          headerName: 'IEC',
          children:[
            {
              headerName: 'IEC 62133',
              field: 'iec62133Status',
              width: 110,
              cellRenderer: 'status'
            },{
              headerName: 'IEC 62619',
              field: 'iec62619Status',
              width: 110,
              cellRenderer: 'status'
            },{
              headerName: 'IEC 62660',
              field: 'iec62660Status',
              width: 110,
              cellRenderer: 'status'
            }
          ]
        },{
          headerName: 'UL',
          children:[
            {
              headerName: 'UL 1642',
              field: 'ul1642Status',
              width: 110,
              cellRenderer: 'status'
            },{
              headerName: 'UL 1973',
              field: 'ul1973Status',
              width: 110,
              cellRenderer: 'status'
            },{
              headerName: 'UL 9540A',
              field: 'ul9540AStatus',
              width: 110,
              cellRenderer: 'status'
            }
          ]
        },{
          headerName: 'BIS',
          children:[
            {
              headerName: 'IS 16046',
              field: 'bis16046Status',
              width: 110,
              cellRenderer: 'status'
            }
          ]
        },{
          headerName: 'IS16893',
          children:[
            {
              headerName: '1',
              field: 'is168931Status',
              width: 110,
              cellRenderer: 'status'
            },{
              headerName: '2',
              field: 'is168932Status',
              width: 110,
              cellRenderer: 'status'
            }
          ]
        }

      ],
      loadData: [],
      pageData: [],
      allResultData: [],
      queryParam: {},
      treeData:{},
      competitiveType:'all',
      tableType:'normal'
    }
  },
  methods: {
    showOrHide(){
      this.isShowAllSearch = !this.isShowAllSearch
      this.tableHeight =  document.documentElement.clientHeight * 0.7 - 40 - 20 - 24 - 10 - 20 - 32 - 10 - 14 - 20 - 6 - 37 - (this.isShowAllSearch?40:0)
    },
    reset(){
      this.queryParam = {}
      this.getPageData()
    },
    add(){
      this.$refs.reportAdd.add()
    },
    /*preview(data,data2){
      if(data2.node.value){
        if(data2.node.value.fileName.indexOf(".pdf") > -1 || data2.node.value.fileName.indexOf(".PDF") > -1){
          this.filePreviewVisible = true
          this.iframeUrl = '/api/sysFileInfo/preview?Authorization=Bearer '+Vue.ls.get("Access-Token")+'&id='+data2.node.value.fileId+"#toolbar=0"

        }else{
          getMinioDownloadUrl(data2.node.value.fileId).then(res1 => {
            downloadMinioFile(res1.data)
          })

        }

      }
    },*/
    preview(data,data2){
      if(data2.node.value){

          if (data2.node.value.fileId) {
            this.$refs.pbiPreviewNew.init(data2.node.value.fileId)
          } else if (data2.node.value.fileUrl) {
            this.$refs.pbiPreviewNew.init(null,data2.node.value.fileName,data2.node.value.fileUrl)
          }

        }


    },

    updateExpandedKeys(id,ids){
      // this.expandedKeys = []
      this.selectedKeys = []
      if(id){
        this.expandedKeys = ids
        this.selectedKeys.push(id)
      }else{
        this.expandedKeys = []
      }
    },
    /*dbclick(event){
      alert("进来了")
      console.log(event)
    },*/
    openFileManager(){
      this.showFile = true
      this.$refs.fileManagerDiv.open(this.treeData.list)



    },
    closeFileManager(){
      this.showFile = false
    },
    dbclick(id,source,three){
      // this.updateExpandedKeys(id)
      this.showFile = true
      this.$refs.fileManagerDiv.open2(this.treeData.list,id)

    },
    preview2(data){

        if(data.reportName.indexOf(".pdf") > -1 || data.reportName.fileName.indexOf(".PDF") > -1){
          this.filePreviewVisible = true
          this.iframeUrl = '/api/sysFileInfo/preview?Authorization=Bearer '+Vue.ls.get("Access-Token")+'&id='+data.reportId+"#toolbar=0"

        }else{
          getMinioDownloadUrl(data.reportId).then(res1 => {
            downloadMinioFile(res1.data)
          })

        }


    },

    tableFocus() {
        this.$el.style.setProperty('--scroll-border-bottom-fixed', 'none');
        this.$el.style.setProperty('--scroll-display', 'unset');
        this.$el.style.setProperty('--scroll-border-bottom', '1px solid #Dee1e8');
    },
    // 鼠标移出
    tableBlur() {
        this.$el.style.setProperty('--scroll-border-bottom-fixed', '1px solid #dee1e8');
        this.$el.style.setProperty('--scroll-display', 'none');
        this.$el.style.setProperty('--scroll-border-bottom', 'none');
    },

    handlePageChange(value) {
      let {current, pageSize} = value
      this.pageNo = current
      this.pageSize = pageSize
      //数据分页
      this.getPageData()

    },

    initChart() {
      let chart = this.echarts.init(this.$refs.gainOverview)

      chart.clear()
      let series = []
      for (let i = 0; i < this.echartsData.series.length; i++) {
        let serie =  this.echartsData.series[i]
        series.push({
          name: serie.name,
          type: serie.type,
          showBackground: true,
          backgroundStyle: {
            color: '#f2f2f2',
            borderRadius: 10,
          },
          yAxisIndex: serie.yAxisIndex,
          barWidth: '10%',
          /*tooltip: {
            valueFormatter: (value) => serie.yAxisIndex < 1? value : value + '%'
          },*/
          itemStyle: {
            color:this.getColor(i,this.echartsData.series.length),
            borderRadius: 10
          },
          /*label: {
            show: true,
            position: 'top',
            formatter: (params) => { // 使用 params 对象
              const value = params.value; // 获取数据值
              return serie.yAxisIndex < 1 ? value : value + '%'; // 根据条件格式化
            }
          },*/
          data: serie.data
        })
      }

      const options = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          padding: document.documentElement.clientHeight > 800?[10, 10, 10, 10]:[2, 10, 2, 10], // 减少上下间距为2像素，左右保持10像素
          // 图例标记与文本之间的距离
          itemGap: 1,
          right:'0%',
          bottom:'0%',
          orient:'vertical',
          itemWidth: document.documentElement.clientHeight > 800?16:10, // 图例标记的宽度
          itemHeight: document.documentElement.clientHeight > 800?16:6,
          textStyle: {
            fontSize: document.documentElement.clientHeight > 800?12:9 // 根据需要调整这个值
          }
        },
        grid: {
          left: '-3%',
          top:'12%',
          right: '20%',
          bottom: '0%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: this.echartsData.xData,
            axisLine: { show: false },
            axisTick: { show: false },
          }
        ],
        yAxis: [
          {
            type: 'value',
            show:false
          },
          {
            type: 'value',
            show:false
          }

        ],

        series: series
      };
      chart.setOption(options)
      chart.resize()
    },
    getTreeData(){
      testProjectOutTestDataGetManagerFileList({productName:this.fileName}).then(res => {
        this.treeData = res.data
      })
    },
    getEchartsData(){
      testProjectOutTestDataEchartsData({code:this.competitiveType == 'all'?null:this.competitiveType}).then(res => {
        this.echartsData = res.data
      }).then(() => {
        this.initChart()
      })
    },
    getColor(i,length){
      if (i == length -1){
        return '#00f8e1'
      }else if (i == 0){

        return {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
          offset: 0, color: '#188df0' // 0% 处的颜色
        }, {
          offset: 1, color: '#5faff3' // 100% 处的颜色
        }],
          global: false // 缺省为 false
        }
      }else if (i == 1){
        return {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: '#9bcfff' // 0% 处的颜色
          }, {
            offset: 1, color: '#d1e9ff' // 100% 处的颜色
          }],
          global: false // 缺省为 false
        }
      }else {
        return {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: '#d1e9ff' // 0% 处的颜色
          }, {
            offset: 1, color: '#eaf4fd' // 100% 处的颜色
          }],
          global: false // 缺省为 false
        }
      }
    },
    openQuery(){
      window.open('/dpvProductCert', '_blank')
    },
    getPageData(){
      this.loading = true
      testProjectOutTestDataGetManagerTable({
        ...{
          pageNo: this.pageNo,
          pageSize: this.pageSize
        }, ...this.queryParam
      }).then((res) => {
        if (res.success) {
          this.pageData = res.data.rows
          this.tableTotal = res.data.totalRows

        }
      }).finally(() => {
        if(this.pageData.length == 0 && this.pageNo > 1){
          // this.pageNo -= 1
          this.pageNo = Math.max(1, Math.ceil(this.tableTotal / this.pageSize))
          this.getPageData()
        }
        this.loading = false
      })
    },
    async initGain() {
      await this.getPageData()
      await this.getEchartsData()
      this.getTreeData()

      /*await this.callGetTree()
      await this.callGetDeptTree()
      await this.callAffiliatedPlatformTree()
      await this.callGetResultsByParam()
      await this.getAllPassTopic()*/
      // await this.getAllResults()
    }
  },
  created() {
    this.tableHeight = this.templateHeight - 37 - 20

  },
  mounted() {
    this.$el.style.setProperty('--scroll-border-bottom-fixed', '1px solid #dee1e8');
    this.$el.style.setProperty('--scroll-display', 'none');
    this.$el.style.setProperty('--scroll-border-bottom', 'none');
    this.initGain()
  }
}
</script>


<style lang="less" scoped="">
//@import './topic.less';
@import '/src/components/pageTool/style/pbiSearchItem.less';

:root {
        --scroll-display: none;
        --scroll-border-bottom: none;
        --scroll-border-bottom-fixed: none;
    }
    /deep/.ag-body-horizontal-scroll{
        border-bottom: var(--scroll-border-bottom) !important;
    }
    /deep/.ag-body-horizontal-scroll-viewport {
        display: var(--scroll-display) !important;
        border-bottom: var(--scroll-border-bottom) !important;
    }

    /deep/.ag-horizontal-left-spacer,
    /deep/.ag-horizontal-right-spacer{
        border-bottom: var(--scroll-border-bottom-fixed) !important;
    }

  /deep/ .ant-breadcrumb{
    font-size: 12px;
  }

  /deep/.left .ant-input,
  /deep/.left .ant-btn{
    height: 28px;
    font-size: 12px;
    border-radius: 4px;
  }

  /deep/.ant-btn > .anticon + span,
  /deep/.ant-btn > span + .anticon{
    margin-left: 4px;
  }

  /deep/.ant-radio-group{
    font-size: 12px;
    color: #999;
  }

  /deep/.ant-radio-button-wrapper{
    border: 1px solid #1890ff;
    height: 24px;
    line-height: 23px;
    padding: 0 12px;
  }

  /deep/.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled){
    color: #fff;
    background-color: #1890ff;
  }

  /deep/.ant-radio-button-wrapper:first-child{
    border-radius: 4px 0 0 4px;
  }

  /deep/.ant-radio-button-wrapper:last-child{
    border-radius: 0 4px 4px 0;
  }


.page-container {
  height: calc(70% - 12px)!important;
  border-radius: 4px;
  margin: 0 !important;
  padding: 12px;
}

/deep/ .ant-modal-footer {
  padding: 0;
}

.container {
  height: calc(100vh - 40px);
  background: #f4f5fc;
  color: #333;
}

.content {
  height: calc(100% - 57px);
  display: flex;
}

/* 主标题 */

.head-title {
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.head-headerName::before {
  width: 8px;
  background: #1890ff;
  margin-right: 8px;
  content: "\00a0"; /* 填充空格 */

  color: #5aaef4;
}

.head-content {
  padding: 0 0 10px;
}

.left {
  width: 300px;
  background: #fff;
  text-align: center;
  border-radius: 4px;
  padding: 12px;
}

.left-top {
  width: 100%;
  margin-bottom: 12px;
}

.right-top{
  background: #fff;
  border-radius: 100px;
  height: calc(30%);
  margin-bottom: 12px;
}

.flex-sb-center-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
}

.left-bottom {
  width: 100%;
  height: calc(100% - 80px);
  position: relative;
  overflow: auto;
}

.right {
  width: calc(100% - 300px);
  border-radius: 10px;
}

.top {
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 50px;
}

.center {
  width: 100%;
  display: flex;
  justify-content: space-between;
  height: calc((65% - 10px) * 0.4);
}

.empty-block {
  height: calc(100% - 64px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.empty-block-left {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-25%, -75%);
}

.bottom {
  height: calc(100% - 60px);
}

.block {
  width: 100%;
  height: 100%;
  text-align: center;
  background: #fff;
  border-radius: 10px;
  padding: 10px;
}

.block span,
.overview-block {
  width: 100%;
  height: 100%;
  text-align: center;
  background: #FFF;
  border-radius: 4px;
  padding: 12px;
}

.left-top span {
  font-size: 14px;
  /* font-weight: 600; */
}

.dept {
  display: flex;
  justify-content: space-between;
}

.dept span {
  font-size: 14px;
  font-weight: 500;
  color: #1890ff;
}

.chart_table {
  height: calc(100% - 20px);
}

.chart_bar_table {
  height: calc(100% - 24px);
  margin-top: calc(24px);
}

.table-wrap {
  height: calc(100% - 30px);
  overflow: auto;
}

.table-wrap button {
  z-index: 88;
}

.status-lamp {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  margin: auto;
}

.mt10 {
  margin-top: 10px;
}

.mr10 {
  margin-right: 10px;
}

/deep/ .pbi-title .title {
  font-size: 18px;
}

/deep/ .ant-pagination-options-size-changer.ant-select {
  display: inline-block;
}
/deep/.search-container .vue-treeselect__multi-value-label{
	white-space: nowrap;
	max-width: 41px;
    overflow: hidden;
    text-overflow: ellipsis;
}
/deep/.search-container .vue-treeselect--searchable.vue-treeselect--multi.vue-treeselect--has-value .vue-treeselect__input-container{
  display: none;
}
/deep/.search-container .vue-treeselect__limit-tip-text{
	margin: 0;
	margin-top: 4px;
	font-weight: initial;
	text-indent: -44px;
    overflow: hidden;
    display: none;
}
/deep/.ant-tree li{
  width:100px
}

/deep/.ant-tree-title{
  font-size: 12px;
}

.circle {
  width: 5px; /* 圆的直径 */
  height: 5px; /* 圆的直径 */
  border-radius: 50%; /* 将正方形变为圆形 */
}

/deep/.ant-tree li {
  width: 50px;
}
</style>