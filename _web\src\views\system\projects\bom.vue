<template>
	<div>
		<div v-show="tipShow" :style="position" id="sgtip" class="moveTip">{{tipContent}}</div>
		<a-spin :spinning="dloading" style="margin-bottom:12px">
			<a-descriptions title="物料清单BOM" :column="{ xxl: 4, xl: 4, lg: 4, md: 4, sm: 2, xs: 1 }" style="text-align:left" bordered>
				<!-- <a-descriptions-item label="文件编号">
										<a-Input
											disabled
											@change="(e) => {
												const { value } = e.target
												bom.bomNo = value
												callSysBomSave(1)
											}"
											v-model="bom.bomNo"
										/>
									</a-descriptions-item> -->
				<!-- <a-descriptions-item label="文件名称">
										<a-Input
											disabled
											@change="(e) => {
												const { value } = e.target
												bom.bomName = value
												callSysBomSave(1)
											}"
											placeholder="填写文件名称"
											v-model="bom.bomName"
										/>
									</a-descriptions-item> -->
				<!-- <a-descriptions-item label="试制数量" v-if="projectdetail && projectdetail.state == 4">
								<a-input-number :min="0" :default-value="0" @change="(value) => {
												bom.bomUpmen = value
												callSysBomSave(1)
											}" v-model="bom.bomUpmen" />
							</a-descriptions-item> -->
				<!-- <a-descriptions-item label="项目名称">
										{{ projectdetail.productProjectName }}
									</a-descriptions-item> -->
				<a-descriptions-item label="产品名称" v-if="projectdetail">
					{{ projectdetail.productProjectName }}
				</a-descriptions-item>
				<!-- <a-descriptions-item label="电芯容量(Ah)">
										{{ projectdetail.ah }}
									</a-descriptions-item> -->
				<a-descriptions-item label="样品阶段" v-if="projectdetail">
					{{ 'product_state_status' | dictType(projectdetail.state) }}
				</a-descriptions-item>
				<!-- <a-descriptions-item label="项目阶段">
										{{ 'product_stage_status' | dictType(projectdetail.mstatus) }}
									</a-descriptions-item> -->
				<!-- <a-descriptions-item label="版本">
										<a-Input aria-disabled="true"
											disabled
											@change="(e) => {
												const { value } = e.target
												bom.bomVersion = value
												callSysBomSave(1)
											}"
											v-model="bom.bomVersion"
										/>
									</a-descriptions-item> -->
				<a-descriptions-item label="运输方式">
					<a-select mode="multiple" style="width:120px" :disabled="disabled" :value="bomTransport" @change="(value) => {
												let oldvalue = bomTransport
												if(value.length > 0){
													bomTransport= value
													callSysBomSave(1)
												}else if(value.length < 1 && bom.bomType == 1){
													bomTransport = oldvalue
													$message.error('运输方式不能为空')
												}else if(value.length < 1 && bom.bomType != 1){
													bomTransport= []
													callSysBomSave(1)
												}
											}">
						<a-select-option value='1'>陆运</a-select-option>
						<a-select-option value='2'>海运</a-select-option>
						<a-select-option value='3'>空运</a-select-option>
						<a-select-option value='4'>其他</a-select-option>
					</a-select>
					<span v-if="bom.bomType == 1" style="margin-left:2px;color:red">*</span>
				</a-descriptions-item>
				<!-- <a-descriptions-item label="BOM用途">
										<a-select
											:disabled="disabled"
											v-model="bom.bomUse"
											@change="(value) => {
												bom.bomUse= value
												callSysBomSave(1)
											}"
											>
											<a-select-option value='1'>亿纬生产</a-select-option>
											<a-select-option value='2'>工程/设计</a-select-option>
											<a-select-option value='3'>通用</a-select-option>
											<a-select-option value='4'>工厂维护</a-select-option>
											<a-select-option value='5'>销售和分销</a-select-option>
											<a-select-option value='6'>成本核算</a-select-option>
											<a-select-option value='7'>空的</a-select-option>
											<a-select-option value='8'>稳定性研究</a-select-option>
											<a-select-option value='C'>配置控制</a-select-option>
											<a-select-option value='M'>外部军需品显示</a-select-option>
										</a-select>
									</a-descriptions-item> -->
				<a-descriptions-item label="有效起始日">
					<a-date-picker :disabled="disabled" @change="(date, dateString) => {
					                            bom.bomStartdate = dateString
												callSysBomSave(1)
					                        }" :value="moment(bom.bomStartdate,'YYYY-MM-DD')" :default-value="moment(new Date(),'YYYY-MM-DD')" />
				</a-descriptions-item>
			</a-descriptions>
		</a-spin>
		<a-card v-if="bom.bomStatus == 3" style="margin-bottom:3px">
			<p class="error">{{errorTips}}</p>
			<div v-for="(item, n) in add_fails" :key="n">
				<p class="error" v-for="(_item,i) in item.IT_DATA" :key="i">
					新增失败: 主物料-{{_item.MATNR}} 子物料-{{_item.IDNRK}} 用量-{{_item.MENGE}} 单位-{{_item.MEINS}} 损耗率-{{_item.AUSCH}}
				</p>
			</div>
			<div v-for="(item, n) in edit_fails" :key="n">
				<p class="error" v-for="(_item,i) in item.IT_DATA" :key="i">
					{{fldelte[_item.FLDELETE]}}失败: 主物料-{{item.IV_MATNR}} 子物料-{{_item.IDNRK}} 用量-{{_item.MENGE}} 损耗率-{{_item.AUSCH}}
				</p>
			</div>
		</a-card>
		<div class="main">
			<div class="left_main">
				<template>
									<a-button v-if="!openStatus && (bom.bomStatus == 0 || bom.bomStatus == 4) " type="primary" @click="showDrawer" style="margin-bottom:8px;margin-right:8px" icon="menu-fold">
										选择物料
									</a-button>
					        		<!-- <a-button v-if="!openStatus && bom.bomStatus == 0" type="primary" @click="preview" style="margin-bottom:8px;margin-right:8px" icon="play-circle">
										预览
									</a-button> -->
									<a-button v-else-if="bom.bomStatus == 0 || bom.bomStatus == 4" type="primary" @click="showDrawer" style="margin-bottom:8px;margin-right:8px" icon="menu-unfold">
										取消选择
									</a-button>
									<a-button  type="primary" @click="preview" style="margin-bottom:8px;margin-right:8px" icon="play-circle">
										预览
									</a-button>
									<a-button  type="primary" v-if="bom.bomStatus == 0 && bom.bomVersion == null" @click="copy" style="margin-bottom:8px;margin-right:8px" icon="copy">
										引用
									</a-button>
			            <a-button type="primary" v-if="bom.bomStatus == 0 || bom.bomStatus == 4" @click="bomUpGrade()" icon="check">提交</a-button>
					        <a-modal v-model="copyModal" title="复制bom" @ok="handleOk" style="text-align: center" :confirm-loading="cloading">
					          <span>复制来源：</span><a-select v-model="sourceId"  placeholder="请选择复制来源"  style="width: 300px;text-align: center">
					            <a-select-option v-for="(item,index) in copySource" :key="index" :value="item.id" >{{ item.name }}</a-select-option>
					          </a-select>
					        </a-modal>
					        <a-drawer
					          placement="right"
					          :closable="false"
							  :bodyStyle="{ height: '100%' }"
					          width="80%"
					          :visible="visible1"
					          :after-visible-change="afterVisibleChange"
					          @close="onClose1"
					        >
					          <iframe :src="pdfUrl+'#view=FitH,top'" width="100%" height="100%" ></iframe>
					        </a-drawer>
			          <bomupgrade ref="bomupgrade" @updateVis="updateVis"/>
			</template>
			<a-spin :spinning="vloading">
				
				<dragTreeTable
					id="dratree"
					style="width:100%"
					:data='treeData'
					:onDrag="onTreeDataChange"
					:beforeDragOver="beforeDragOver"
					:isdraggable="isdraggable" ref="dratree" resize border>
					
<template #action="{row}">
	<div @click.stop>
		<a v-if="!disabled && optRow && optRow.id == row.id" @click="opt(row)">
			<a-icon type="check-circle" /></a>
		<a class="uncheck" v-else-if="!disabled && (row.sapNumber.startsWith('8') || row.sapNumber.startsWith('9'))" @click="opt(row)">
			<a-icon type="minus-circle" /></a>
		<a v-else-if="!disabled" style="display:inline-block;width:12px;margin-right:16px;"></a>
		<a-divider v-if="!disabled && (row.sapNumber.startsWith('8') || row.sapNumber.startsWith('9'))" type="vertical" />
		<a-popconfirm v-if="!disabled" placement="topRight" title="确认删除？" @confirm="() => doDel(row)">
			<a>删除</a>
		</a-popconfirm>
	</div>
</template>

<template #desc="{row}">
	<div @click.stop>
		<input style='width:60px' :disabled="disabled" @change="(e) => {
														const { value } = e.target
														row.desc = value
														callSysBomSave(0)
													}" v-model="row.desc" placeholder="备注" />
	</div>
</template>
					
<template #partUse="{row}">
	<div @click.stop v-if="row.parent_id" class="stopdiv">
		<input :id="row.id" type="number" style='width:80px' :disabled="disabled" @change="(e) => {
														const { value } = e.target
														row.validate = validatePrimeNumber(value)
														if(!row.validate){
															return
														}
														if(!equal(row.partUse,parseFloat(value).toFixed(3))){
															row.partUse = parseFloat(value).toFixed(3)
															callSysBomSaveForPartUse()
														}
													}" min="0.000" step="0.001" precision="3" :value="row.partUse" />
		<span v-if="!row.validate" style="color:red;display:block;padding-top:6px">必须大于0.000</span>
	</div>
	<div v-else class="stopdiv" @click.stop>
		<input :id="row.id" type="hidden" value="1000">
	</div>
</template>

<template #partLoss="{row}">
	<div @click.stop class="stopdiv">
		<input v-if="row.parent_id" style="width:62px" type="number" :disabled="disabled" :value="row.partLoss" @change="(e) => {
														const { value } = e.target
														if(!value){
															$message.error('请输入数值')
															row.partLoss = 0.00
															row.partLoss = parseFloat(0.00).toFixed(2)
															return
														}
														if(!equal(row.partLoss,parseFloat(value).toFixed(2))){
															let val = value
															if(val > 100){
																val = 100
															}
															row.partLoss = val
															row.partLoss = parseFloat(val).toFixed(2)
															callSysBomSave(0)
														}
													}" min="0.00" step="0.01" precision="2" max="100.00" /><span v-if="row.parent_id">%</span>
	</div>
</template>
<template #sapNumber="{row}">
	<div class="stopdiv" @click.stop>
		<input class="readonly text-align" :value="row.sapNumber" readonly="readonly" /></div>
</template>
<template #partDescription="{row}">
	<div class="stopdiv" @click.stop>
		<input class="readonly" :value="row.partDescription" readonly="readonly" /></div>
</template>

<template #sapPartUse="{row}">
	<div class="stopdiv" @click.stop>
		<input class="readonly text-align" :value="row.sapPartUse" readonly="readonly" /></div>
</template>
<template #baseUse="{row}" >
	<div class="stopdiv" @click.stop>
		<input v-if="row.parent_id" class="readonly text-align" :value="row.baseUse" readonly="readonly" />
	</div>
</template>
<template #count="{row}">
	<div v-if="row.substitute && row.substitute.length > 0" @click.stop class="stopdiv">
		<a @click="$refs.bomreplaceForm.add(row,partGroupArr)">{{row.substitute.length}}</a>
	</div>
	<div v-else @click.stop class="stopdiv">
		<a @click="$refs.bomreplaceForm.add(row,partGroupArr)">0</a>
	</div>
</template>

<template #version="{row}">
	<div @click.stop class="stopdiv">
		<template v-if="row.version">
							<div  v-for="(item,i) in JSON.parse(row.version)" :key="i">
								{{i}}-{{item}}
							</div>
</template>
	</div>
</template>


					
<template #partUnit="{row}">
	<span>{{row.partUnit}}</span>
	<!-- <div @click.stop class="stopdiv">
		<select v-if="row.parent_id" :disabled="disabled" :value="row.partUnit" @change="(e) => {
														if(row.partUnit != e.target.value){
															row.partUnit= e.target.value
															callSysBomSave(0)
														}
					                                }">
													<option value='BOT'>BOT</option>
					                                <option value='BOX'>BOX</option>
													<option value='CAR'>CAR</option>
													<option value='EA'>EA</option>
													<option value='FT'>FT</option>
													<option value='G'>G</option>
													<option value='KG'>KG</option>
													<option value='L'>L</option>
													<option value='M'>M</option>
													<option value='M2'>M2</option>
													<option value='M3'>M3</option>
													<option value='ML'>ML</option>
													<option value='PAA'>PAA</option>
													<option value='PAK'>PAK</option>
													<option value='ROL'>ROL</option>
													<option value='SET'>SET</option>
													<option value='个'>个</option>
												</select>
	</div> -->
</template>

	<template #sapImport="{row}">
		<div @click.stop>
			<a v-if="!disabled && (row.sapNumber.startsWith('8') || row.sapNumber.startsWith('9'))" @click="sapImport(row)">
										导入sap
									</a>
			<a v-else-if="!disabled" style="display:inline-block;width:12px;margin-right:16px;"></a>
		</div>
	</template>
				</dragTreeTable>
			</a-spin>
		</div>
		<div
			:class="{ right_main_show: !openStatus }"
			class="right_main"
		>
				<div>
					<div slot="content" class="table-page-search-wrapper">
						<a-form layout="inline">
							<a-row :gutter="48">
								<a-col :md="24" :sm="24">
									<a-form-item style="height:auto">
										<!-- <v-selectpage v-model="queryParam.partClass" :data="nodes" key-field="nodeId" show-field="nodeName" >
  										</v-selectpage> -->
										<treeselect :limit="4" :max-height="200" placeholder="选择物料分类" :value-consists-of="valueConsistsOf" v-model="queryParam.partClass" :multiple="true" :options="nodes" :normalizer="normalizer" />
									</a-form-item>
								</a-col>
								<a-col :md="8" :sm="24">
									<a-form-item>
										<a-input @keyup.enter.native="$refs.table.refresh(true)" v-model="queryParam.sapNumber" @pressEnter="$refs.table.refresh(true)" allow-clear placeholder="请输入物料代码"/>
									</a-form-item>
								</a-col>
								<a-col :md="8" :sm="24">
									<a-form-item>
										<a-input @keyup.enter.native="$refs.table.refresh(true)" v-model="queryParam.partDescription" @pressEnter="$refs.table.refresh(true)" allow-clear placeholder="请输入物料规格"/>
									</a-form-item>
								</a-col>
								<a-col :md="8" :sm="24">
									<a-form-item>
										<span class="table-page-search-submitButtons">
											<a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
											<a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
										</span>
									</a-form-item>
								</a-col>
							</a-row>
						</a-form>
					</div>
				</div>
				<div>
					<a-spin :spinning="loading">
						<s-table
							ref="table"
							:rowKey="(record) => record.partNumber"
							:columns="columns"
							:data="loadData"
							>
							<span slot="partClass" slot-scope="text">
								{{nodeMap.get(text)}}
							</span>
							<span slot="action" slot-scope="text, record">
								<a @click="toBom(record)">选择</a>
							</span>
						</s-table>
					</a-spin>
				</div>
		</div>
	</div>
	<sapimport @refresh="refresh" ref="sapimport" />
	<bomreplace  @substitute="substitute" :nodeMap="nodeMap" :nodes="nodes" :disabled="disabled" ref="bomreplaceForm" /> <!-- @oncancel="oncancel" @ok="handleOk" -->
</div> 	
</template>

<script>
	import {
		getPartPage,
		getPartRandom
	} from "@/api/modular/system/partManage"
	import {
		//getwerks,
		sysBomSave,
		getBom,
		pdfUpdate,
		getBomError,
		getBomList,
		copyBom,
		sapImportVerify,
    	updatePLMBomData
	} from "@/api/modular/system/bomManage"
	import {
		getAllNode
	} from "@/api/modular/system/nodeManage"
	import Treeselect from '@riophae/vue-treeselect'
	import '@riophae/vue-treeselect/dist/vue-treeselect.css'
	import dragTreeTable from "drag-tree-table";
	import {
		STable
	} from '@/components'
	import bomreplace from './bomreplace'
	import moment from 'moment';
	import Iframe from "../../../layouts/Iframe";
	import bomupgrade from './bomupgrade'
	import sapimport from './sapimport'
	export default {
		components: {
			Iframe,
			dragTreeTable,
			STable,
			bomreplace,
			bomupgrade,
			Treeselect,
			sapimport
		},
		props: {
			issueId: {
				type: Number,
				default: 0
			},
			bomId: {
				type: String,
				default: 0
			},
			projectdetail: {
				type: Object,
				default: {}
			},
			date: {
				type: Date,
				default: new Date()
			}
		},
		data() {
			return {
				position: {
					left: '',
					top: ''
				},
				tipContent: '',
				tipShow: false,
				sourceId: null,
				copyModal: false,
				optRow: null,
				showview: false,
				valueConsistsOf: 'ALL',
				normalizer(node) {
					return {
						id: node.id,
						label: node.name,
						children: node.lists,
					}
				},
				disabled: false,
				isdraggable: true,
				nodes: [],
				bom: {
					bomStatus: -1
				},
				nodeMap: new Map(),
				//wearks: [],
				openStatus: false,
				visible: false,
				visible1: false,
				loadData: parameter => {
					return getPartPage(Object.assign(parameter, this.queryParam)).then((res) => {
						return res.data
					})
				},
				queryParam: {},
				loading: false,
				vloading: false,
				cloading: false,
				dloading: false,
				pdfUrl: '',
				columns: [{
						title: '操作',
						dataIndex: 'action',
						width: 40,
						scopedSlots: {
							customRender: 'action'
						}
					},
					{
						title: '物料分类',
						dataIndex: 'partClass',
						width: 100,
						scopedSlots: {
							customRender: 'partClass'
						}
					},
					{
						title: '物料代码',
						dataIndex: 'sapNumber',
						width: 80
					},
					{
						title: '物料规格',
						dataIndex: 'partDescription',
					},
					{
						title: '单位',
						dataIndex: 'partUnit',
						width: 50
					}
				],
				treeData: {
					open: false,
					namess: "",
					lists: [],
					columns: [{
							type: 'selection',
							field: 'id',
							title: '物料分类',
							width: 350,
							align: 'left',
							formatter: (item) => {
								let that = this
								return '<a title="' + item.partName + '" class="aid"' + ' data-id=' + item.id + ' name=' + that.nodeMap.get(item.partClass) + '>' + that.nodeMap.get(item.partClass) + '</a>'
							}
						},
						/* {
							field: 'partName',
							title: '物料名称',
							width: 200,
							align: 'left',
						}, */
						{
							type: 'sapNumber',
							field: 'sapNumber',
							title: '物料代码',
							width: 150,
							align: 'center',
						},
						{
							type: 'partDescription',
							field: 'partDescription',
							title: '物料规格',
							width: 400,
							align: 'left'
						},
						{
							type: 'partUnit',
							field: 'partUnit',
							title: '单位',
							width: 110,
							align: 'center'
						},
						{
							type: 'partUse',
							field: 'partUse',
							title: '理论用量(B0)',
							width: 168,
							align: 'center'
						},
						{
							type: 'partLoss',
							field: 'partLoss',
							title: '工艺损耗(B1) /%',
							width: 168,
							align: 'center'
						},
						/* {
							type: 'baseUse',
							field: 'baseUse',
							title: '基本用量(B0*(1+B1))',
							width: 168,
							align: 'center'
						}, */
						{
							type: 'version',
							field: 'version',
							title: '工厂版本号',
							width: 150,
							align: 'center'
						},
						/* {
							field: 'posnr',
							title: '行号',
							width: 100,
							align: 'center'
						}, */
						{
							type: 'sapPartUse',
							field: 'sapPartUse',
							title: 'sap使用量',
							width: 130,
							align: 'center'
						},
						{
							type: 'desc',
							field: 'desc',
							title: '备注',
							width: 150,
							align: 'center'
						},
						{
							type: 'action',
							width: 120,
							align: 'center',
							title: '操作'
						},
					]
				},
				bomTransport: [],
				partGroupArr: [],
				add_fails: [],
				edit_fails: [],
				errorTips: '',
				fldelte: {
					'M': '修改',
					'A': '新增',
					'X': '删除'
				},
				copySource: [],
				timeId: null
			};
		},
		methods: {
			mouseDown() {
				let that = this
				// 鼠标跟随tip
				document.onmousedown = function(e) {
					var ev = e || event;
					var scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
					that.position.left = ev.clientX + 'px';
					that.position.top = ev.clientY - 50 + scrollTop + 'px';
				}
				document.onmouseup = function (e) {
					that.tipShow = false
					that.tipContent = ''
				}
			},
			mouseUp(){
				let that = this
				document.onmouseup = function (e) {
					that.tipShow = false
					that.tipContent = ''
				} 
			},
			sapImport(row) {
				let that = this
				sapImportVerify({
					id: that.bomId
				}).then((res) => {
					if (res.success) {
						that.$refs.sapimport.add(that.bom.id, row)
					} else {
						that.$message.error(res.message)
					}
				})
			},
			copy() {
				this.getList();
			},
			getList() {
				let that = this
				that.copySource = []
				getBomList({
					bomIssueId: that.bom.bomIssueId,
					bomType: that.bom.bomType
				}).then((res) => {
					if (res.success) {
						for (let i = 0; i < res.data.length; i++) {
							if (that.bom.id != res.data[i].id && res.data[i].bomData.length > 2) {
								res.data[i].name = (i + 1) + '、' + (res.data[i].bomNo != null ?res.data[i].bomNo:JSON.parse(res.data[i].bomData)[0].partDescription)

								that.copySource.push(res.data[i]);
							}
						}
					}
				}).finally((res) => {
					that.copyModal = true
				})
			},
			getByClass(parent, cls) {
				if (parent.getElementsByClassName) {
					return Array.from(parent.getElementsByClassName(cls));
				} else {
					var res = [];
					var reg = new RegExp(' ' + cls + ' ', 'i')
					var ele = parent.getElementsByTagName('*');
					for (var i = 0; i < ele.length; i++) {
						if (reg.test(' ' + ele[i].className + ' ')) {
							res.push(ele[i]);
						}
					}
					return res;
				}
			},
			validatePrimeNumber(number) {
				if (this.equal(number, 0.000)) {
					return false
				}
				return true;
			},
			afterVisibleChange(val) {},
			showDrawer1() {
				this.visible1 = true;
			},
			onClose1() {
				this.visible1 = false;
			},
			preview() {
				let that = this
				if (that.bom.bomData.length < 3) {
					that.$message.error("请先搭建bom");
					return false
				}
				that.dloading = true
				that.vloading = true
				pdfUpdate({
					id: that.bom.id
				}).then((res) => {
					if (res.success) {
						that.pdfUrl = process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id=' + res.data
						that.visible1 = true
						that.dloading = false
						that.vloading = false
					}
				})
			},
			toMap(list, _map) {
				let arr = []
				for (const item of list) {
					arr.push(item)
					while (arr.length > 0) {
						let _tempItem = arr.shift()
						_map[_tempItem.id] = {
							partNumber: _tempItem.partNumber,
							parent_id: _tempItem.parent_id
						}
						if (_tempItem.lists) {
							arr.push(..._tempItem.lists)
						}
					}
				}
			},
			getParentIds(_map, id, ids) {
				let $map = null
				if (_map[id]) {
					$map = _map[id]
					while ($map) {
						let _tempItem = $map
						$map = null
						ids.push(_tempItem.partNumber)
						if (_tempItem.parent_id) {
							$map = _map[_tempItem.parent_id]
						}
					}
				}
			},
			moment,
			equal(a, b) {
				const floatEpsilon = Math.pow(2, -23)
				return Math.abs(a - b) <= floatEpsilon * Math.max(Math.abs(a), Math.abs(b));
			},
			showDrawer() {
				this.openStatus = !this.openStatus
			},
			beforeDragOver(from, to, where) {
				let that = this
				clearTimeout(that.timeId)
				that.tipContent = from.partName + '将移动到' + to.partName
				that.tipShow = true
				that.timeId = setTimeout(() => {
					that.tipContent = ''
					that.tipShow = false
				}, 1800);
			},
			onTreeDataChange(list, from, to, where) {
				let that = this
				if (that.bom.bomStatus == 7) {
					that.$message.error('导入状态不允许拖拽')
					return false
				}
				if (!to.sapNumber.startsWith('8') && !to.sapNumber.startsWith('9') && where == 'center') {
					that.$message.error('非8和9物料号不能为BOM')
					return false;
				}
				if (!from.parent_id && that.treeData.lists.length > 0) {
					that.$message.error('顶级BOM已存在')
					return false;
				}
				let map = {}
				let ids = []
				that.toMap(that.treeData.lists, map)
				that.getParentIds(map, to.id, ids)
				let index = ids.findIndex(item => item == from.partNumber)
				if (index > -1) {
					that.$message.error('子物料无法与父级物料相同')
					return false;
				}
				that.$message.info(from.partName + "将移动到" + to.partName)
				that.treeData.lists = list;
				that.optRow = that.getOptRow(list,that.optRow.id)
				that.hightLow(that.optRow.id)
				that.callSysBomSave(0)
				setTimeout(() => {
					that.getlevelid()
				}, 1000);
			},
			getOptRow(list,id){
				
				let arr = []
				for (const item of list) {
					arr.push(item)
					while (arr.length > 0) {
						let _tempItem = arr.shift()
						if (_tempItem.id == id) {
							return _tempItem
						}
						if (_tempItem.lists) {
							arr.push(..._tempItem.lists)
						}
					}
				}
			},
			getlevelid() {
				let that = this
				that.$nextTick(() => {
					let items = that.getByClass(document, 'aid')
					for (const e of items) {
						e.innerText = '[' + (that.$refs.dratree.GetLevelById(e.getAttribute('data-id')) + 1) + ']' + '-' + e.getAttribute('name')
					}
				})
			},
			hightLow(id) {
				let that = this
				that.$nextTick(() => {
					let items = that.getByClass(document, 'tree-row')
					for (const e of items) {
						let $id = e.getAttribute('tree-id')
						if ($id == id) {
							e.classList.add('highlight')
						} else {
							e.classList.remove('highlight')
							e.classList.remove('highlight-row')
						}
					}
				})
			},
			numDiv(num1, num2) {
				num1 = num1 ? num1 : 0.000
				num2 = num2 ? num2 : 0.000
				var baseNum1 = 0,
					baseNum2 = 0;
				var baseNum3, baseNum4;
				try {
					baseNum1 = num1.toString().split(".")[1].length;
				} catch (e) {
					baseNum1 = 0;
				}
				try {
					baseNum2 = num2.toString().split(".")[1].length;
				} catch (e) {
					baseNum2 = 0;
				}
				baseNum3 = Number(num1.toString().replace(".", ""));
				baseNum4 = Number(num2.toString().replace(".", ""));
				return (baseNum3 / baseNum4) * Math.pow(10, baseNum2 - baseNum1);
			},
			getSapPartUse() {
				this.loopSaveBomSap(this.treeData.lists)
			},
			loopSaveBomSap(list) {
				let that = this
				let arr = []
				for (const item of list) {
					arr.push(item)
					while (arr.length > 0) {
						let _tempItem = arr.shift()
						if (_tempItem.lists) {
							for (const _item of _tempItem.lists) {
								let a = _item.partUse
								let b = that.numDiv(_tempItem.partUse, 1000)
								_item.sapPartUse = that.numDiv(a, b).toFixed(3)
								_item.baseUse = parseFloat(a*(1+that.numDiv(_item.partLoss, 100))).toFixed(3)
							}
							arr.push(..._tempItem.lists)
						}
					}
				}
			},
			onSearch(query) {
				this.search = query
				this.offset = 0
			},
			toBom(record) {
				let that = this
				if (that.treeData.lists.length < 1 && !record.sapNumber.startsWith('8') && !record.sapNumber.startsWith('9')) {
					that.$message.error('非8和9物料号不能为BOM')
					return false
				}
				if (that.treeData.lists.length > 0 && that.treeData.lists[0].sapNumber == record.sapNumber) {
					that.$message.error('与一级BOM的料号相同，禁止操作')
					return false
				}
				if (that.optRow && that.optRow.partNumber == record.partNumber) {
					that.$message.error('与父级BOM的料号相同，禁止操作')
					return false
				}
				let _list = that.optRow ? that.optRow.lists : (that.treeData.lists.length > 0 ? that.treeData.lists[0].lists : [])
				let index = _list.findIndex(item => item.sapNumber == record.sapNumber)
				if (index > -1) {
					
					that.$confirm({
						title: '存在相同的物料，确认添加？',
						onOk() {
							that.addBom(record);
						},
						onCancel() {},
						class: 'test',
					});
					return false
				}
				that.addBom(record)
			},
			onClose() {
				this.visible = false;
			},
			addBom(record) {
				let that = this
				that.vloading = true
				
				getPartRandom({}).then((res) => {
					if (res.success) {
						if (that.treeData.lists.length < 1) {
							that.treeData.lists.push({
								'id': res.data,
								'open': true,
								'partName': record.partName,
								'partDescription': record.partDescription,
								'partUnit': record.partUnit,
								'partClass': record.partClass,
								'sapNumber': record.sapNumber,
								'partUse': parseFloat(1000).toFixed(3),
								'sapPartUse': parseFloat(1000).toFixed(3),
								'baseUse':parseFloat(1000).toFixed(3),
								'partLoss': parseFloat(0).toFixed(2),
								'partNumber': record.partNumber,
								'partGroup': '',
								'posnr': '',
								'desc': '',
								'version': '',
								'substitute': [],
								'validate': false,
								'lists': []
							})
						} else {
							if (that.optRow) {
								that.optRow.lists.push({
									'id': res.data,
									'open': true,
									'partName': record.partName,
									'partDescription': record.partDescription,
									'partUnit': record.partUnit,
									'partClass': record.partClass,
									'sapNumber': record.sapNumber,
									'partUse': null,
									'sapPartUse': parseFloat(0).toFixed(3),
									'baseUse':parseFloat(0).toFixed(3),
									'partLoss': parseFloat(0).toFixed(2),
									'partNumber': record.partNumber,
									'partGroup': '',
									'posnr': '',
									'desc': '',
									'version': '',
									'validate': false,
									'parent_id': that.optRow.id,
									'substitute': [],
									'lists': []
								})
							} else {
								that.treeData.lists[0].lists.push({
									'id': res.data,
									'open': true,
									'partName': record.partName,
									'partDescription': record.partDescription,
									'partUnit': record.partUnit,
									'partClass': record.partClass,
									'sapNumber': record.sapNumber,
									'partUse': null,
									'sapPartUse': parseFloat(0).toFixed(3),
									'baseUse':parseFloat(0).toFixed(3),
									'partLoss': parseFloat(0).toFixed(2),
									'partNumber': record.partNumber,
									'partGroup': '',
									'posnr': '',
									'desc': '',
									'version': '',
									'validate': false,
									'parent_id': that.treeData.lists[0].id,
									'substitute': [],
									'lists': []
								})
							}
						}
						that.callSysBomSave(0)
						setTimeout(() => {
							that.getlevelid()
						}, 1000);
					} else {
						that.$message.error(res.message)
					}
					that.vloading = false
				}).catch((err) => {
					that.$message.error('错误：' + err.message)
					that.vloading = false
				})
			},
			refresh() {
				this.$emit('onImport')
			},
			bomUpGrade() {
				this.$refs.bomupgrade.edit(this.bom)
			},
			updateVis() {
				this.disabled = true;
			},
			doDel(row) {
				if (!row.parent_id) {
					this.$message.info('顶级BOM不可删除', 1);
					return
				}
				let that = this
				that.removeTreeListItem(this.treeData.lists, row.id,that)
			},

			resetOptRow(list,id){
				let that = this
				if (that.optRow.id === id) {
					that.optRow = that.treeData.lists[0]
					that.hightLow(that.optRow.id)
					return false
				}
				let arr = []
				for (const item of list) {
					arr.push(item)
					while (arr.length > 0) {
						let _tempItem = arr.shift()
						if (_tempItem.id == that.optRow.id && _tempItem.id == id) {
							that.optRow = that.treeData.lists[0]
							that.hightLow(that.optRow.id)
							return false
						}
						if (_tempItem.lists) {
							arr.push(..._tempItem.lists)
						}
					}
				}
			},
			
			removeTreeListItem(treeList, id,that) { // 根据id属性从数组（树结构）中移除元素
				if (!treeList || !treeList.length) {
					return
				}
				for (let i = 0; i < treeList.length; i++) {
					if (treeList[i].id === id) {
						treeList.splice(i, 1);
						that.resetOptRow(that.treeData.lists,id)
						that.hightLow(that.optRow.id)
						that.callSysBomSave(0)
						return false;
					}
					that.removeTreeListItem(treeList[i].lists, id,that)
				}
			},
			/* callWearks() {
				this.dloading = true
				getwerks({})
					.then((res) => {
						if (res.success) {
							this.wearks = res.data
						} else {
							this.$message.error(res.message, 1);
						}
						this.dloading = false
					})
					.catch((err) => {
						this.dloading = false
						this.$message.error('错误提示：' + err.message, 1)
					});
			}, */
			opt(row) {
				let that = this
				if (!row.sapNumber.startsWith('8') && !row.sapNumber.startsWith('9')) {
					that.$message.error('非8和9物料号不能为BOM')
					return false;
				}
				that.optRow = row;
				that.hightLow(row.id)
				that.$message.info('选择成功,将搭建' + row.partNumber + that.nodeMap.get(row.partClass) + "的BOM");
			},
			callSysBomSaveForPartUse() {
				let that = this
				
				that.vloading = true
				that.getSapPartUse()
				if (that.bomTransport.length <= 0 && that.bom.bomType == 1) {
					that.$message.error('运输方式不能为空')
					that.vloading = false
					return false
				}
				that.bom.bomTransport = JSON.stringify(that.bomTransport)
				that.bom.bomData = JSON.stringify(that.treeData.lists)
				sysBomSave(that.bom)
					.then((res) => {
						if (res.success) {
							/* if (!that.bom.id) {
								that.onSave(res.data)
							} */
							that.bom.id = res.data
						} else {
							that.$message.error(res.message, 1);
						}
						that.vloading = false
					})
					.catch((err) => {
						that.vloading = false
						that.$message.error('错误提示：' + err.message, 1)
					});
			},
			callSysBomSave(flag) {
				let that = this
				if (that.bomTransport.length <= 0 && that.bom.bomType == 1) {
					that.$message.error('运输方式不能为空')
					return false
				}
				if (flag)
					that.dloading = true
				else
					that.vloading = true
				that.bom.bomTransport = JSON.stringify(that.bomTransport)
				that.bom.bomData = JSON.stringify(that.treeData.lists)
				sysBomSave(that.bom)
					.then((res) => {
						if (res.success) {
							/* if (!that.bom.id) {
								that.onSave(res.data)
							} */
							that.bom.id = res.data
						} else {
							that.$message.error(res.message, 1);
						}
						if (flag)
							that.dloading = false
						else
							that.vloading = false
					})
					.catch((err) => {
						if (flag)
							that.dloading = false
						else
							that.vloading = false
						that.$message.error('错误提示：' + err.message, 1)
					});
			},
			getPartGoup(list) {
				let that = this
				let arr = []
				for (const item of list) {
					arr.push(item)
					while (arr.length > 0) {
						let _tempItem = arr.shift()
						if (_tempItem.substitute && _tempItem.substitute.length > 0) {
							that.partGroupArr.push(_tempItem.partGroup)
						}
						if (_tempItem.lists) {
							arr.push(..._tempItem.lists)
						}
					}
				}
			},
			callGetBomError() {
				let that = this
				that.dloading = true
				that.vloading = true
				getBomError({
					id: that.bomId
				}).then((res) => {
					if (res.success) {
						that.add_fails = JSON.parse(res.data.addFails)
						that.edit_fails = JSON.parse(res.data.editFails)
						that.errorTips = res.data.errorMsg
					} else {
						that.$message.error(res.message, 1);
					}
				}).catch((err) => {
					that.dloading = false
					that.vloading = false
					that.$message.error('错误提示：' + err.message, 1)
				});
			},
			callGetBom() {
				let that = this
				that.openStatus = false
				that.dloading = true
				that.vloading = true
				getBom({
						id: that.bomId
					})
					.then((res) => {
						if (res.success) {
							let is0or4 = res.data.bomStatus == '0' || res.data.bomStatus == '4' || (res.data.bomStatus == '7' && that.hasPerm('sysBom:copy'))
							that.isdraggable = is0or4 ? true : false
							that.disabled = is0or4 ? false : true
							that.showview = is0or4 ? false : true
							that.bom = res.data
							let _list = JSON.parse(that.bom.bomData)
							if (_list[0].lists.length < 1 && that.bom.bomStatus == 0) {
								that.openStatus = true
							}
							that.getPartGoup(_list)
							that.treeData.lists = _list
							that.bomTransport = that.bom.bomTransport ? JSON.parse(that.bom.bomTransport) : []
							setTimeout(() => {
								that.getlevelid()
							}, 1000);
							if (that.bom.bomStatus == 3) {
								that.callGetBomError()
							}
							let index = that.treeData.columns.findIndex(item => item.type == 'sapImport')
							if (that.hasPerm('sysBom:copy') && index < 0 && that.bom.canImport == 1) {
								that.treeData.columns.push({
									type: 'sapImport',
									width: 120,
									align: 'center',
									title: 'sap导入'
								})
							}
							if (is0or4) {
								that.optRow = that.treeData.lists[0]
								that.hightLow(that.optRow.id)
							}
						} else {
							that.$message.error(res.message, 1);
						}
						that.dloading = false
						that.vloading = false
					})
					.catch((err) => {
						that.dloading = false
						that.vloading = false
						that.$message.error('错误提示：' + err.message, 1)
					});
			},
			/* onSave(data) {
				this.$emit('onSave', data)
			}, */
			substitute(partGroup, partGroupAdd, tempgroup) {
				let that = this
				if (partGroup && partGroupAdd && that.partGroupArr.indexOf(partGroup) < 0) {
					that.partGroupArr.push(partGroup)
				}
				if (!partGroup && !partGroupAdd && tempgroup) {
					let index = that.partGroupArr.indexOf(tempgroup)
					that.partGroupArr.splice(index, 1)
				}
				that.callSysBomSave(0)
			},
			handleOk() {
				let that = this
				if (null == that.sourceId) {
					that.$message.error("请先选择源头")
					return
				}
				that.dloading = true
				that.vloading = true
				that.cloading = true
				copyBom({
					sourceId: that.sourceId,
					targetId: that.bom.id
				}).then((res) => {
					if (res.success) {
						that.callGetAllNode()
					}
				})
			},
			setNodeMap(list, map) {
				let arr = []
				for (const item of list) {
					arr.push(item)
					while (arr.length > 0) {
						let _tempItem = arr.shift()
						map.set(_tempItem.id, _tempItem.name)
						if (_tempItem.lists) {
							arr.push(..._tempItem.lists)
						}
					}
				}
			},
			callGetAllNode() {
				let that = this
				that.loading = true
				getAllNode()
					.then((res) => {
						if (res.success) {
							let map = new Map()
							that.setNodeMap(res.data, map)
							that.nodeMap = map;
							that.nodes = res.data
							that.callGetBom()
						} else {
							that.$message.error(res.message, 1);
						}
						that.loading = false
					}).finally((res) => {
						that.dloading = false
						that.vloading = false
						that.copyModal = false
						that.cloading = false
					})
					.catch((err) => {
						that.loading = false
						that.$message.error('错误提示：' + err.message, 1)
					});
			}
		},
		watch: {
			date(newVal, oldVal) {
				this.callGetBom()
			}
		},
		created() {
      		updatePLMBomData({id: this.bomId})
			this.callGetAllNode()
			this.mouseDown()
			this.mouseUp()
			/* if (this.bomId) {
				this.callGetBom()
			} */
		},
	}
</script>

<style lang="less" scoped=''>
	.moveTip {
		color: #fff;
		background: rgb(0 0 0 / 69%);
		min-height: 20px;
		min-width: 60px;
		position: fixed;
		border-radius: 2px;
		z-index: 1000;
		padding: 5px;
	}
	/deep/.tree-column {
		padding: 2px 0 !important;
	}
	/deep/.tree-row {
		line-height: initial !important;
	}
	/deep/.drag-tree-table-header {
		height: initial !important;
	}
	.projectdetail {
		margin-bottom: 12px;
	}
	/deep/.drag-tree-table {
		margin: 0;
	}
	/deep/.drag-tree-table-header,
	/deep/.tree-row {
		height: auto;
		line-height: inherit;
		font-size: 14px;
		font-weight: initial;
		color: #000;
	}
	/deep/.drag-tree-table-header {
		background: #fafafa;
		border-bottom: 1px solid #e8e8e8;
	}
	.main {
		display: flex;
		width: 100%;
	}
	.left_main {
		flex: 1;
		overflow: auto;
	}
	.right_main {
		margin-left: 12px;
		width: 520px;
		display: block;
		overflow: auto;
		transition: width 0.5s;
	}
	.right_main_show {
		width: 0;
		margin: 0;
	}
	.right_main_show * {
		display: none;
	}
	/deep/.table-page-search-wrapper .ant-form-inline .ant-form-item {
		margin-bottom: 14px;
	}
	/deep/.table-page-search-wrapper .ant-form-inline .ant-form-item .ant-form-item-control {
		height: auto;
	}
	/deep/.ant-drawer-body {
		height: 100%;
	}
	select {
		color: rgba(0, 0, 0, 0.65);
		display: block;
		height: 23px;
		line-height: 23px;
		font-size: 14px;
		outline: none;
		border: 1px solid #d9d9d9;
	}
	input {
		color: #000;
		height: 23px;
		line-height: 23px;
		width: 60px;
		font-size: 14px;
		border: 1px solid #d9d9d9;
		outline: none;
	}
	p.error {
		color: rgb(250, 9, 9);
		margin-bottom: 2px;
	}
	.uncheck {
		color: #a7a5a5;
	}
	/deep/.vue-treeselect--has-value .vue-treeselect__multi-value {
		margin: 0;
	}
	/deep/.ant-descriptions-bordered .ant-descriptions-item-label,
	/deep/.ant-descriptions-bordered .ant-descriptions-item-content {
		padding: 5px;
	}
	/deep/.ant-descriptions-row>th,
	/deep/.ant-descriptions-row>td {
		padding: 0;
	}
	/deep/.ant-descriptions-item-content,
	/deep/.ant-descriptions-item-label {
		font-size: 12px;
	}
	.stopdiv {
		width: 100%;
		height: 100%;
	}
	.readonly {
		color: #000;
		width: 100%;
		font-size: 12px;
		padding: 0;
		border: 0;
		background: inherit;
	}
	.text-align {
		text-align: center;
	}
	/deep/.tree-row.highlight {
		background: #e1ecf7 !important;
	}
</style>