<template>
    <div>
      <a-descriptions title="基础信息">
        <a-descriptions-item label="测试项目别名">
          {{ originalData.lifeTestRecordDataMap[0].alias }}
        </a-descriptions-item>
        <a-descriptions-item>
          <template slot="label">
            <span style="font-weight: bold">中检类型</span>
          </template>
          <div style="font-weight: bold;color: black">
            {{
            originalData.lifeTestRecordDataMap[0].middleCheck === "small"
            ? "小中检"
            : originalData.lifeTestRecordDataMap[0].middleCheck === "large"
            ? "大中检"
            : originalData.lifeTestRecordDataMap[0].middleCheck === "recharge"
            ? "补电"
            : "-"
            }}
          </div>
        </a-descriptions-item>
        <a-descriptions-item label="计划开始时间">
          {{ originalData.inDate || "-" }}
        </a-descriptions-item>
        <a-descriptions-item label="计划结束时间">
          {{ originalData.outDate || "-" }}
        </a-descriptions-item>
        <a-descriptions-item label="总存储天数">
          {{ originalData.totalDay || "-" }}
        </a-descriptions-item>
        <a-descriptions-item label="存储阶段">
          {{
          originalData.orderNumber
          ? originalData.orderNumber === 1
          ? "初始性能检测"
          : originalData.orderNumber - 1
          : "-"
          }}
        </a-descriptions-item>
      </a-descriptions>
      <a-descriptions title="详细信息">
      </a-descriptions>
        <tableIndex
          height="calc(100vh-400px)"
          :other-height="parseInt(400)"
          :pageLevel='parseInt("2")'
          :tableTotal='tableTotal'
          :pageTitleShow=false
          :paginationShow=false
          :loading='loading'
        >
          <template #table>
            <ag-grid-vue class='table ag-theme-balham'
                         style="height: 300px"
                         :tooltipShowDelay="0"
                         :columnDefs='dataColumnDefs'
                         :rowData='dataRowData'
                         @grid-ready="onGridReady"
                         :defaultColDef='defaultColDef'>
            </ag-grid-vue>
          </template>
        </tableIndex>

    </div>
</template>

<script>

  import {
    getTestProDetailByTaskId
  } from "@/api/modular/system/testProgressManager";

  import {
    earlyWarningGet
  } from "@/api/modular/system/testAbnormalManager";

  export default {
    components: {
      red: {
        template: `<div :style='params.abnormalData.find(a => a.cellCode == params.data.cellTestCode) &&
  params.abnormalData.find(a => a.cellCode == params.data.cellTestCode)[params.column.colId+"Flag"]? {border:"1px solid red",height:"100%"}:null'
  :title='params.abnormalData.find(a => a.cellCode == params.data.cellTestCode) &&
  params.abnormalData.find(a => a.cellCode == params.data.cellTestCode)[params.column.colId+"Flag"]? params.abnormalData.find(a => a.cellCode == params.data.cellTestCode)[params.column.colId+"Msg"]:null'>{{params.value}}</div>`

      },
    },
    data() {
      return {
        abnormalKeyValue: {},
        dataVisible: false,
        handleMsg: null,
        handleResult: null,
        record: {},
        confirmVisible: false,
        visible: false,
        loading: false,
        hide: false,
        gridApi: null,
        columnApi: null,
        isShowAllSearch: false,
        tableTotal: 0,
        queryParam: {},
        rowData: [],
        defaultColDef: {
          flex: 1,
          minWidth: 120,
          filter: false,
          floatingFilter: false,
          editable: false,
        },
        pageNo: 1,
        pageSize: 20,
        originalData: {lifeTestRecordDataMap: [{}]},
        dataRowData: [],
        dataColumnDefs: [

          {
            headerName: '测试项目别名',
            field: 'alias',
            minWidth: 110,
            width: 110,
          },
          {
            headerName: '测试编码',
            field: 'cellTestCode',
            minWidth: 110,
            width: 110,
          }
        ],
        abnormalData:[]
      }
    },
    computed: {},
    methods: {
      openData(record) {
        this.record = record

        earlyWarningGet({id:record.abnormalId}).then(res => {
          this.abnormalData = JSON.parse(res.data.abnormalData)
        }).then(() => {
          getTestProDetailByTaskId(record.progressDetailId).then(res => {
            this.originalData = res.data
            this.dataRowData = res.data.lifeTestRecordDataMap
            this.tableTotal = res.data.lifeTestRecordDataMap.length
            this.initializeGrid();
          }).finally(() => {
            this.dataVisible = true
            this.loading = false
          })
        })


      },
      closeData() {
        this.dataVisible = false
      },
      initializeGrid() {
        let abnormalKeyValue = {
          ongoing: '进行中',
          earlyEnd: '状态正常-提前结束',
          batteryDisassembly: '状态正常-电池拆解',
          pressureDrop: '掉压失效-终止测试',
          abnormalHot: '异常发热-终止测试',
          openShellAndLeak: '开壳漏液-终止测试',
          shellRust: '壳体生锈-终止测试',
          operationError: '作业错误-终止测试',
          thermalRunaway: '热失控-终止测试',
          acrException: '内阻异常-终止测试'
        }
        this.dataColumnDefs = [
          {
            headerName: '测试项目别名',
            field: 'alias',
            minWidth: 110,
            width: 110,
          },
          {
            headerName: '测试编码',
            field: 'cellTestCode',
            minWidth: 110,
            width: 110,
          },
          {
            headerName: '中检类型',
            field: 'middleCheck',
            minWidth: 110,
            width: 110,
            cellRenderer: function (params) {
              return params.value == 'small' ? '小中检' : params.value == 'large' ? '大中检' : params.value == 'recharge' ? '补电' : '-';
            },
            hide: this.shouldHideColumn('middleCheck')
          },
          {
            headerName: '电芯状态',
            field: 'batteryStatus',
            minWidth: 110,
            width: 110,
            cellRenderer: function (params) {
              return abnormalKeyValue[params.value];
            }.bind(this)
          },
          {
            headerName: '内阻/mΩ',
            field: 'beforeInnerres',
            minWidth: 110,
            width: 110,
            cellRenderer: 'red',
            cellRendererParams: {abnormalData: this.abnormalData},
            hide: this.shouldHideColumn('beforeInnerres')
          },
          {
            headerName: '电压/mV',
            field: 'beforeVoltage',
            minWidth: 110,
            width: 110,
            cellRenderer: 'red',
            cellRendererParams: {abnormalData: this.abnormalData},
            hide: this.shouldHideColumn('beforeVoltage')
          },
          {
            headerName: '中检后内阻/mΩ',
            field: 'afterInnerres',
            minWidth: 110,
            width: 110,
            cellRenderer: 'red',
            cellRendererParams: {abnormalData: this.abnormalData},
            valueFormatter: params => null != params.value? String(params.value):'',
            hide: this.shouldHideColumn('afterInnerres')
          },
          {
            headerName: '中检后电压/mV',
            field: 'afterVoltage',
            minWidth: 110,
            width: 110,
            cellRenderer: 'red',
            cellRendererParams: {abnormalData: this.abnormalData},
            valueFormatter: params => null != params.value? String(params.value):'',
            hide: this.shouldHideColumn('afterVoltage')
          },
          {
            headerName: '产气量/g',
            field: 'volume',
            minWidth: 110,
            width: 110,
            cellRenderer: 'red',
            cellRendererParams: {abnormalData: this.abnormalData},
            valueFormatter: params => null != params.value? String(params.value):'',
            hide: this.shouldHideColumn('volume')
          },
          {
            headerName: '重量/g',
            field: 'weight',
            minWidth: 110,
            width: 110,
            cellRenderer: 'red',
            cellRendererParams: {abnormalData: this.abnormalData},
            valueFormatter: params => null != params.value? String(params.value):'',
            hide: this.shouldHideColumn('weight')
          },
        ];

        if (this.gridApi) {
          this.gridApi.setColumnDefs(this.dataColumnDefs);
          this.gridApi.setRowData(this.dataRowData);
        }
      },
      onGridReady(params) {
        this.gridApi = params.api;
        this.columnApi = params.columnApi;
        this.initializeGrid(); // 确保 grid 准备好时也初始化
      },
      formatValue(key) {
        return this.abnormalKeyValue[key]
      },
      shouldHideColumn(column) {
        if (column == 'middleCheck') {
          if (this.originalData.largeinspection == '0' && this.originalData.smallinspection == '0' && this.originalData.recharge == '0') {
            return true
          }
          return false
        }
        if (column == 'beforeInnerres' || column == 'beforeVoltage' ) {
          if (this.record.isBefore == '1' ) {
            return false
          }
          return true
        }

        if (column == 'afterVoltage' || column == 'afterInnerres' ) {
          if (this.record.isBefore != '1' ) {
            return false
          }
          return true
        }
        if (column == 'volume' ) {
          if (this.record.isBefore == '1' && this.originalData.orderNumber > 1 && this.originalData.volume == '1') {
            return false
          }
          return true
        }
        if (column == 'weight' ) {
          if (this.record.isBefore == '1' && this.originalData.orderNumber > 1 && this.originalData.weight == '1') {
            return false
          }
          return true
        }
      }

    }
    ,
    mounted() {

    }

  }
</script>

<style lang='less' scoped="">
  @import '/src/components/pageTool/style/pbiSearchItem.less';

  :root {
    --scroll-display: none;
    --scroll-border-bottom: none;
    --scroll-border-bottom-fixed: none;
  }

  /deep/ .ag-body-horizontal-scroll {
    border-bottom: var(--scroll-border-bottom) !important;
  }

  /deep/ .ag-body-horizontal-scroll-viewport {
    display: var(--scroll-display) !important;
    border-bottom: var(--scroll-border-bottom) !important;
  }

  /deep/ .ag-horizontal-left-spacer,
  /deep/ .ag-horizontal-right-spacer {
    border-bottom: var(--scroll-border-bottom-fixed) !important;
  }

  .page-container {
    height: auto;
  }

  /deep/ .code_link {
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /deep/.ant-descriptions-title{
    margin-bottom: 0;
  }
  /deep/.ant-modal-body {
    padding: 10px;
  }
  /deep/.ant-form-item {
    margin: 0;
  }

</style>