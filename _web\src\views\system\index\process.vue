<template>
	<div class="container">
		<!-- 面包屑 start -->
		<div class="breadcrumb">
			<a-breadcrumb separator=">">
				<a-breadcrumb-item class="hand">
					<router-link to="/product_chart"><a-icon class="rollback-icon" type="rollback" />首页看板</router-link>
				</a-breadcrumb-item>
				<a-breadcrumb-item>{{ $route.query.dept }}项目进展</a-breadcrumb-item>
			</a-breadcrumb>
		</div>
		<!-- 面包屑 end -->

		<!-- 主标题 start -->
		<div class="head-title">{{ $route.query.dept || "动力电池研究院" }}</div>
		<!-- 主标题 end -->

		<!-- 内容 start -->
		<div class="content-wrapper">
			<div class="table-page-search-wrapper mb0">
				<a-form layout="inline">
					<a-row :gutter="32">
						<a-col :md="9" :sm="24">
							<a-form-item label="项目状态">
								<a-select v-model="projectStatu" mode="multiple" placeholder="请选择项目状态" @change="handleQuery">
									<a-select-option v-for="(item, index) in statusOption" :key="index" :value="item.value">
										<div class="select-box">
											<div class="circle" :style="`background:${item.color}`"></div>
											{{ item.label }}
										</div>
									</a-select-option>
								</a-select>
							</a-form-item>
						</a-col>
						<!-- <a-col :md="6" :sm="24">
							<a-form-item label="项目所属研究所">
								<a-select v-model="queryparam.dept" placeholder="请选择研究所" allowClear>
									<a-select-option v-for="item in departmentOption" :value="item.value">
										{{ item.label }}
									</a-select-option>
								</a-select>
							</a-form-item>
						</a-col> -->
						<a-col :md="5" :sm="24">
							<a-form-item label="产品名称">
								<a-input
									size="small"
									class="filter-input"
									@keyup.enter.native="handleChange"
									v-model="queryparam.productProjectName"
									placeholder="请输入产品名称"
								>
									<a-icon slot="suffix" type="search" style="color: rgba(0,0,0,.45)" />
								</a-input>
							</a-form-item>
						</a-col>
						<a-col :md="5" :sm="24">
							<a-form-item label="项目名称">
								<a-input
									size="small"
									class="filter-input"
									@keyup.enter.native="handleChange"
									v-model="queryparam.projectName"
									placeholder="请输入项目名称"
								>
									<a-icon slot="suffix" type="search" style="color: rgba(0,0,0,.45)" />
								</a-input>
							</a-form-item>
						</a-col>
						<a-col :md="1" :sm="24" :style="{ float: 'right' }">
							<div class="table-page-search-submitButtons" :style="{ float: 'right' }">
								<a-button size="small" style="margin-left: 120px;" type="primary" @click="handleQuery">查询</a-button>
							</div>
						</a-col>
					</a-row>
				</a-form>
			</div>
			<!-- 表格 start -->
			<div class="table-wrapper">
				<a-table
					:rowKey="record => record.issueId"
					:pagination="pagination"
					:data-source="data"
					:columns="columns"
					:loading="loading"
					:style="`height:${tableHeight}px;`"
					size="middle"
				>
					<span slot="mstatus" slot-scope="text">
						{{ "product_stage_status" | dictType(text) }}
					</span>

					<span slot="delayDays" slot-scope="text, record">
						{{ record.state < 7 ? text : "-" }}
					</span>

					<span slot="stopTime" slot-scope="text, record">
						{{ record.state >= 7 ? text : "-" }}
					</span>

					<!-- 项目进度 start -->
					<span slot="projectStatu" slot-scope="text, record">
						<!-- state 7 : 暂停 8 : 停产  -->
						<div v-if="record.state === 7 || record.state === 8" class="select-box">
							<div class="circle" style="background:#cacaca"></div>
							停止
						</div>
						<div v-else-if="record.delayDays >= 14" class="select-box">
							<div class="circle" style="background:#d95040"></div>
							延期≥14天
						</div>
						<div v-else-if="14 > record.delayDays >= 7" class="select-box">
							<div class="circle" style="background:#fac858"></div>
							延期7-14天
						</div>
						<div v-else class="select-box">
							<div class="circle" style="background:#9bbb59"></div>
							正常
						</div>

						<!-- {{ record.state === 7 || record.state === 8 ? "停止" : record.delayDays > 0 ? "延期" : "正常" }} -->
					</span>
					<!-- 项目进度 end -->
				</a-table>
			</div>
			<!-- 表格 end -->
		</div>
		<!-- 内容--所 end -->
	</div>
</template>

<script>
import { getProjectProcessDetail } from "@/api/modular/system/chartManage"
import { getCateTree } from "@/api/modular/system/topic"

import _ from "lodash"
export default {
	data() {
		return {
			loading: false,

			//  40 顶部栏 20 页面padding 20 面包屑  48 标题 16 内页面padding
			tableHeight: document.documentElement.clientHeight - 40 - 20 - 20 - 48 - 60,
			chartHeight: document.documentElement.clientHeight - 40 - 20 - 20 - 48 - 16,
			data: [],
			deptFilters: [],
			cateFilters: [],
			nameFilters: [],
			pdFilters: [],
			rpmFilters: [],
			custFilters: [],
			dateFilters: [],
			dayFilters: [],
			columns: [
				{
					title: "序号",
					dataIndex: "seq",
					align: "center",
					width: 50,
					customRender: (text, record, index) => <span>{index + 1}</span>
				},
				{
					title: "产品名称",
					dataIndex: "productProjectName",
					align: "center"
				},
				{
					title: "项目名称",
					dataIndex: "projectName",
					align: "center"
				},
				{
					title: "项目等级",
					dataIndex: "productLevel",
					align: "center"
				},
				{
					title: "状态",
					dataIndex: "projectStatu",
					align: "center",
					scopedSlots: { customRender: "projectStatu" }
				},
				{
					title: "项目阶段",
					dataIndex: "mstatus",
					align: "center",
					scopedSlots: { customRender: "mstatus" }
				},
				{
					title: "计划时间",
					dataIndex: "productPlannedDate",
					align: "center"
				},
				{
					title: "延期天数",
					dataIndex: "delayDays",
					align: "center",
					scopedSlots: { customRender: "delayDays" }
				},
				{
					title: "暂停提出时间",
					dataIndex: "stopTime",
					align: "center",
					scopedSlots: { customRender: "stopTime" }
				},
				{
					title: "PD",
					dataIndex: "productManager",
					align: "center"
				},
				{
					title: "RPM",
					dataIndex: "researchProjectManager",
					align: "center"
				},

				{
					title: "产品中心",
					dataIndex: "departmentOptionList2",
					align: "center"
				},
				{
					title: "研究所",
					dataIndex: "departmentOptionList1",
					align: "center"
				}
			],
			depts: [],
			// 分页
			pagination: {
				current: 1,
				pageSize: 10,
				total: 0,
				showSizeChanger: true,
				showQuickJumper: true,
				onChange: (current, size) => {
					this.pagination.current = current
					this.pagination.pageSize = size
				},
				onShowSizeChange: (current, pageSize) => {
					this.pagination.current = 1
					this.pagination.pageSize = pageSize
				}
			},

			// xiaodong
			queryparam: {},
			projectStatu: [],
			statusOption: [
				{ value: 1, label: "正常", color: "#9bbb59" },
				{ value: 3, label: "延期7-14天", color: "#fac858" },
				{ value: 2, label: "延期≥14天", color: "#d95040" },
				{ value: 4, label: "停止", color: "#cacaca" }
			],
			departmentOption: []
		}
	},
	mounted() {
		if (this.$route.query.projectStatu) {
			switch (this.$route.query.projectStatu) {
				case "正常":
					this.projectStatu = [1]
					break
				case "停止":
					this.projectStatu = [4]
					break
				case "延期":
					this.projectStatu = [2, 3]
					break
			}
		}

		// 动态修改--height的值
		document.documentElement.style.setProperty(`--height`, `${this.tableHeight - 63}px`)
		document.documentElement.style.setProperty(`--chartHeight`, `${this.chartHeight}px`)

		// 获取数据
		this.callProjectProcessDetail()

		// 获取department
		this.initDepartment()
	},
	watch: {
		data(newVal, oldVal) {
			if (this.data.length > 0) {
				// 动态修改--height的值
				document.documentElement.style.setProperty(`--height`, `${this.tableHeight - 63}px`)
			} else {
				// 动态修改--height的值
				document.documentElement.style.setProperty(`--height`, "50px")
			}
		}
	},
	methods: {
		// 获取部门的筛选框数据
		initDepartment() {
			getCateTree({
				fieldName: "department"
			}).then(res => {
				if (!res.success) return this.$message.error(res.message, 1)

				this.departmentOption = res.data.map(v => ({
					value: v.value,
					label: v.title
				}))
			})
		},

		// 获取数据
		callProjectProcessDetail() {
			this.loading = true
			getProjectProcessDetail({ dept: this.$route.query.dept, ...this.queryparam })
				.then(res => {
					if (res.result) {
						this.data = JSON.parse(JSON.stringify(res.data.datas))

						this.data.forEach(element => {
							element.departmentOptionList.forEach(el => {
								if (el.pid == 0) {
									element.departmentOptionList1 = el.value
								}
								if (el.pid !== 0) {
									element.departmentOptionList2 = el.value
								}
							})
						})

						if (this.projectStatu.length !== 0) {
							const temData = []

							this.projectStatu.forEach(value => {
								// 1:正常
								if (value == 1) {
									this.data.forEach(v => {
										if (v.state < 7 && v.delayDays < 7) {
											temData.push(v)
										}
									})
								}

								// 延期大于等于14
								if (value == 2) {
									this.data.forEach(v => {
										if (v.state < 7 && v.delayDays >= 14) {
											temData.push(v)
										}
									})
								}

								// 延期7-14
								if (value == 3) {
									this.data.forEach(v => {
										if (v.state < 7 && 14 > v.delayDays >= 7) {
											temData.push(v)
										}
									})
								}

								// 停止
								if (value == 4) {
									this.data.forEach(v => {
										if (v.state == 7 || v.state == 8) {
											temData.push(v)
										}
									})
								}
							})
							this.data = temData
							this.pagination.current = 1
						}
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
				})
				.catch(err => {
					this.$message.error("错误提示：" + err.message, 1)
				})
				.finally(() => {
					this.loading = false
				})
		},

		// 搜索
		handleQuery() {
			this.callProjectProcessDetail()
		}
	}
}
</script>

<style lang="less" scoped>
.container {
	margin-left: -40px;
}

// 图表
.board {
	display: flex;
	margin-bottom: 10px;
}

.col1 {
	width: 50%;
}

.col2 {
	width: 50%;
}

.head {
	position: absolute;
	top: 12px;
	left: 12px;
}

// select 选项
.select-box {
	display: flex;
	align-items: center;
}
.select-box .circle {
	width: 13px;
	height: 13px;
	border-radius: 50%;
	margin-right: 8px;
}

// 内容
.content-wrapper {
	background: #fff;
	border-radius: 10px;
	padding: 10px;
}

// 筛选框高度
.filter-input {
	height: 32px !important;
}

.item {
	padding: 8px;
	border-radius: 10px;
	overflow: hidden;
	background: #fff;
	box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
	position: relative;
}

// 面包屑
/deep/.ant-breadcrumb {
	font-size: 12px;
	color: rgba(0, 0, 0, 0.65) !important;
}

/deep/.ant-table-placeholder {
	padding: 0;
}

// 表头
/deep/.ant-table-thead > tr > th {
	font-size: 13px;
	background: #f5f5f5 !important;
	color: #333;
}

// 表格内容
/deep/.ant-table-tbody {
	background: #fff;
	color: #666;
}

// 表头icon
/deep/.ant-table-thead > tr > th .anticon-filter,
/deep/.ant-table-thead > tr > th .ant-table-filter-icon {
	color: #999;
}

/deep/.ant-checkbox-group-item {
	display: block;
	margin: 0 8px;
}

:root {
	--height: 600px;
	--chartHeight: 600px;
}

/deep/.ant-table-body {
	height: var(--height) !important;
	overflow-y: scroll;
}

// 表头固定
/deep/.ant-table-thead {
	position: sticky;
	top: 0;
}

/* 主标题 */

.head-title {
	color: #333;
	padding: 10px 0;
	font-size: 18px;
	font-weight: 600;
}

.head-title::before {
	width: 8px;
	background: #1890ff;
	margin-right: 8px;
	content: "\00a0"; /* 填充空格 */

	color: #5aaef4;
}

// 查看所有--图表
.chart-wrapper {
	padding: 8px;
	border-radius: 10px;
	background: #fff;
	box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
}

.chart-wrapper .chart_table {
	width: 100%;
	height: var(--chartHeight) !important;
}

// 筛选框
/deep/.table-page-search-wrapper .ant-form-inline .ant-form-item {
	margin-bottom: 10px;
}

// 去除表格空状态边框
/deep/.ant-table-placeholder {
	border: none;
}

/deep/.ant-select-selection--multiple {
	max-height: 32px !important;
	overflow: auto;
}

/deep/.ant-input-affix-wrapper .ant-input {
	height: 32px !important;
}
</style>
