<template>
<!-- <a-col :span="8">
                                        <a-form-item label="有效起始日">
                                            <a-date-picker
                                                :disabled="disabled"
                                                @change="(date, dateString) => {
                                                    record.partNewStartDate = dateString
                                                }"
                                                :default-value="moment(record.partNewStartDate,'YYYY-MM-DD')"
                                                
                                            />
                                        </a-form-item>
                                    </a-col>
                                    <a-col :span="8">
                                        <a-form-item label="有效到期日">
                                            <a-date-picker
                                                :disabled="disabled"
                                                @change="(date, dateString) => {
                                                    record.partNewEndDate = dateString
                                                }"
                                                :default-value="moment(record.partNewEndDate, 'YYYY-MM-DD')"
                                            />
                                        </a-form-item>
                                    </a-col> -->
  <a-modal
    :title="title"
    :width="1250"
    :visible="visible"
    @cancel="handleCancel"
  >
        <template slot="footer">
            <a-button key="back" @click="handleCancel">
            关闭
            </a-button>
        </template>
        <div class="main_modal">
            
            <div class="left_main_modal" >
                <template v-if="!disabled">
                    <a-button v-if="!openStatus" type="primary" @click="showDrawer" style="margin-bottom:8px;margin-right:8px" icon="menu-fold">
                    选择物料
                    </a-button>
                    <a-button v-else type="primary" @click="showDrawer" style="margin-bottom:8px;margin-right:8px" icon="menu-unfold">
                    取消选择
                    </a-button>
                </template>
                <a-spin :spinning="vloading">
                    <a-table
                        row-key="index"
                        size="small"
                        :columns="treeData.columns"
                        :dataSource="treeData.lists"
                        :showPagination="false"
                        :rowKey="(record) => record.id"
			            :pagination="false"
                        :scroll="{ x: 1160 }"
                    >   
                        <template slot="partClass" slot-scope="text">
                            {{ nodeMap.get(text) }}
                        </template>
                        <template slot="partUse" slot-scope="text,record">
                            <a-input-number
                                size="small"
                                :disabled="disabled"
                                @change="(value) => {
                                    row.validate = validatePrimeNumber(value)
									if(!row.validate){
										return
									}
                                    if(!equal(record.partUse,value)){
                                        record.partUse = value
                                        savetoBom()
                                    }
                                }"
                                
                                :min="0.000" :step="0.001" :precision="3"
                                :default-value="text"
                            />
                            <span v-if="!row.validate" style="color:red;display:block;padding-top:6px">必须大于0.000</span>
                        </template>
                        <template slot="partLoss" slot-scope="text,record">
                            <a-input-number
                                size="small"
                                :disabled="disabled"
                                @change="(value) => {
                                    if(!equal(record.partLoss,value)){
                                        record.partLoss = value
                                        savetoBom()
                                    }
                                }"
                                :formatter="value => `${value}%`"
                                :parser="value => value.replace('%', '')"
                                :min="0.00" :step="0.01" :precision="2"
                                :default-value="text"
                            />
                        </template>
                        <template slot="partUnit" slot-scope="text,record">
                            <a-select 
                                size="small"
                                style="width: 60px"
                                :disabled="disabled"
                                :default-value="text"
                                @change="(value) => {
                                    if(record.partUnit != value){
										record.partUnit= value
                                        savetoBom()
									}
                                }"
                            >
                                <a-select-option value='BOT'>BOT</a-select-option>
                                <a-select-option value='BOX'>BOX</a-select-option>
                                <a-select-option value='CAR'>CAR</a-select-option>
                                <a-select-option value='EA'>EA</a-select-option>
                                <a-select-option value='FT'>FT</a-select-option>
                                <a-select-option value='G'>G</a-select-option>
                                <a-select-option value='KG'>KG</a-select-option>
                                <a-select-option value='L'>L</a-select-option>
                                <a-select-option value='M'>M</a-select-option>
                                <a-select-option value='M2'>M2</a-select-option>
                                <a-select-option value='M3'>M3</a-select-option>
                                <a-select-option value='ML'>ML</a-select-option>
                                <a-select-option value='PAA'>PAA</a-select-option>
                                <a-select-option value='PAK'>PAK</a-select-option>
                                <a-select-option value='ROL'>ROL</a-select-option>
                                <a-select-option value='SET'>SET</a-select-option>
                                <a-select-option value='个'>个</a-select-option>
                            </a-select>
                        </template>
                        <template slot="partPriority" slot-scope="text,record">
                            <a-select 
                                size="small"
                                style="width: 70px"
                                :disabled="disabled"
                                :default-value="text"
                                @change="(value) => {
                                    if(record.partPriority != value){
										record.partPriority= value
                                        savetoBom()
									}
                                }"
                            >
                                <a-select-option value='1'>1</a-select-option>
                                <a-select-option value='2'>2</a-select-option>
                                <a-select-option value='3'>3</a-select-option>
                                <a-select-option value='4'>4</a-select-option>
                                <a-select-option value='5'>5</a-select-option>
                                <a-select-option value='6'>6</a-select-option>
                                <a-select-option value='7'>7</a-select-option>
                                <a-select-option value='8'>8</a-select-option>
                                <a-select-option value='9'>9</a-select-option>
                                <a-select-option value='10'>10</a-select-option>
                                <a-select-option value='11'>11</a-select-option>
                                <a-select-option value='12'>12</a-select-option>
                                <a-select-option value='13'>13</a-select-option>
                                <a-select-option value='14'>14</a-select-option>
                                <a-select-option value='15'>15</a-select-option>
                            </a-select>
                        </template>
                        <template slot="partMaybe" slot-scope="text,record">
                            <a-select 
                                size="small"
                                :disabled="disabled"
                                :default-value="text"
                                @change="(value) => {
                                    if(record.partMaybe != value){
										record.partMaybe= value
                                        savetoBom()
									}
                                }"
                                                >
                                <a-select-option value='0'>0</a-select-option>
                                <a-select-option value='100'>100</a-select-option>
                            </a-select>
                        </template>
                         <template slot="desc" slot-scope="text,record">
                            <a-Input
                                size="small"
                                :disabled="disabled"
                                @change="(e) => {
                                    const { value } = e.target
                                    record.desc = value
                                    savetoBom()
                                }"
                                :value="text"
                                placeholder="备注"
                            />
                        </template>
                        <template v-if="!disabled" slot="action" slot-scope="record">
                            <div @click.stop>
                                <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => doDel(record)">
                                    <a>删除</a>
                                </a-popconfirm>
                            </div>
                        </template>
                    </a-table>
                </a-spin>
            </div>
            <div
                :class="{ right_main_show: !openStatus }"
                class="right_main_modal"
            >
                    <div>
                        <div slot="content" class="table-page-search-wrapper">
                            <a-form layout="inline">
                                <a-row :gutter="24">
                                    <a-col :md="24" :sm="24">
                                        <a-form-item>
                                            <treeselect placeholder="选择部件分类" :value-consists-of="valueConsistsOf" v-model="queryParam.partClass" :multiple="true" :options="nodes" :normalizer="normalizer" />
                                            <!-- <v-selectpage v-model="queryParam.partClass" :data="nodes" key-field="nodeId" show-field="nodeName" >
  										    </v-selectpage> -->
                                        </a-form-item>
                                    </a-col>
                                    <a-col :md="8" :sm="24">
                                        <a-form-item>
                                            <a-input v-model="queryParam.partDescription" @pressEnter="$refs.table.refresh(true)" allow-clear placeholder="请输入物料规格"/>
                                        </a-form-item>
                                    </a-col>
                                    <a-col :md="8" :sm="24">
                                        <a-form-item>
                                            <a-input v-model="queryParam.sapNumber" @pressEnter="$refs.table.refresh(true)" allow-clear placeholder="请输入SAP编码"/>
                                        </a-form-item>
                                    </a-col>
                                    <a-col :md="8" :sm="24">
                                        <a-form-item>
                                            <span class="table-page-search-submitButtons">
                                                <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                                                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                                            </span>
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                            </a-form>
                        </div>
                    </div>
                    <div>
                        <a-spin :spinning="loading">
                            <s-table
                                ref="table"
                                size="small"
                                :rowKey="(record) => record.partNumber"
                                :columns="columns"
                                :data="loadData"
                                >
                                <span slot="action" slot-scope="text, record">
                                    <a @click="selectBom(record)">选择</a>
                                </span>
                            </s-table>
                        </a-spin>
                    </div>
            </div>
        </div>
  </a-modal>
</template>

<script>
import { getPartPage, getPartRandom  } from "@/api/modular/system/partManage"
import { STable } from '@/components'
//import { SelectPage } from 'v-selectpage'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
export default {
    components: {
	  STable,
      Treeselect
      //'v-selectpage': SelectPage
    },
    props:{
        disabled:{
            type:Boolean,
            default:false
        },
        nodes:{
            type:Array,
            default:[]
        },
        nodeMap :{
            type:Map,
            default:new Map()
        }
    },
    data() {
        return {
            partGroupVal :'',
            partGroupAdd: false,
            valueConsistsOf:'ALL',
            normalizer(node) {
				return {
					id: node.id,
					label: node.name,
					children: node.lists,
				}
			},
            title:'',
            loading:false,
            vloading:false,
            visible:false,
            //curDay : new Date().toLocaleDateString().split('/').map(item=>{if (item<10){return '0'+ item}else {return item}}).join('-'),
            openStatus:false,
            loadData: parameter => {
				return getPartPage(Object.assign(parameter, this.queryParam)).then((res) => {
					return res.data
				})
			},
			queryParam: {},
            columns: [
				/* {
					title: '物料名称',
					dataIndex: 'partName'
				}, */
                {
					title: '物料代码',
					dataIndex: 'sapNumber'
				},
				{
					title: '规格',
					dataIndex: 'partDescription',
                    width:200
				},
				{
					title: '单位',
					dataIndex: 'partUnit'
				},
				{
					title: '操作',
					dataIndex: 'action',
					scopedSlots: { customRender: 'action' }
				}
			],
            treeData: {
                row:{},
				lists: [],
				columns: [
                    {
						dataIndex: 'partClass',
						title: '物料分类',
						align: 'left',
                        width: 100,
						scopedSlots: { customRender: 'partClass' },
					},
                    {
						dataIndex: 'sapNumber',
						title: '物料代码',
						align: 'left',
                        width:80,
                        ellipsis: true
					},
					{
						dataIndex: 'partDescription',
						title: '物料规格',
						align: 'left',
                        width: 200,
					},
                    {
						dataIndex: 'partUnit',
						title: '单位',
						align: 'center',
                        width: 60,
                        scopedSlots: { customRender: 'partUnit' },
					},
                    {
						dataIndex: 'partUse',
						title: '组件数量',
						align: 'center',
                        width: 80,
                        scopedSlots: { customRender: 'partUse' },
					},
                    {
						dataIndex: 'partLoss',
						title: '损耗率',
						align: 'center',
                        width: 80,
                        scopedSlots: { customRender: 'partLoss' },
					},
                    {
						dataIndex: 'partPriority',
						title: '组（优先级）',
						align: 'center',
                        width: 80,
                        scopedSlots: { customRender: 'partPriority' },
					},
                    {
						dataIndex: 'partMaybe',
						title: '组（使用可能性）',
						align: 'center',
                        width: 80,
                        scopedSlots: { customRender: 'partMaybe' },
					},
                    {
						dataIndex: 'desc',
						title: '备注',
						align: 'center',
                        scopedSlots: { customRender: 'desc' },
                        width: 100,
					},
				]
			}
        }
    },
    methods: {
        validatePrimeNumber(number) {
			if (this.equal(number,0.000)) {
				return false
			}
			return  true;
			
		},
        savetoBom(){
            let tempgroup = this.row.partGroup
            this.row.partGroup = this.treeData.lists.length > 0 ? this.partGroupVal : ''
            this.row.substitute = this.treeData.lists
            this.$emit('substitute',this.row.partGroup,this.partGroupAdd,tempgroup)
        },
        equal(a, b) {
			const floatEpsilon = Math.pow(2, -23)
			return Math.abs(a - b) <= floatEpsilon * Math.max(Math.abs(a), Math.abs(b));
		},
        doDel(record){
            let index = this.treeData.lists.findIndex(item => item.id === record.id);
            this.treeData.lists.splice(index, 1);
            this.savetoBom()
            
        },
        //moment,
        add (row,partGroupArr) {
            if (!partGroupArr || partGroupArr.length < 1) {
                this.partGroupVal = '00'
                this.partGroupAdd = true
            }else if(row.partGroup){
                this.partGroupVal = row.partGroup
                this.partGroupAdd = false
            }else if(partGroupArr.length > 0){
                let max = Math.max.apply(null, partGroupArr.map(Number))
                this.partGroupVal = max < 10 ? '0'+(max+1) : ''+(max+1)
                this.partGroupAdd = true
            }
            this.openStatus = false
            this.row = row
            
            this.row.substitute = row.substitute
            this.treeData.lists = row.substitute
            
            this.title = row.partName+":"+row.partDescription
            this.visible = true
            let index = this.treeData.columns.findIndex(item => item.type === 'action');
            if(!this.disabled){
                if (index < 0) {
                    this.treeData.columns.push({
						type: 'action',
						align: 'center',
						title: '操作',
                        width: 80,
                        scopedSlots: { customRender: 'action' },
				    })
                }
            }
        },
        showDrawer() {
			this.openStatus= !this.openStatus
		},
        handleCancel () {
            if (this.disabled) {
                this.visible = false;
                return
            }
            this.visible = false
            this.partGroupVal = ''
            this.partGroupAdd = false
            this.row = {}
        },
        selectBom(record){
           
            this.vloading = true

            if (record.partNumber == this.row.partNumber) {
                this.vloading = false
                this.$message.error('不能选择跟组件一样的替代品')
                return ;
            }

            let index = this.treeData.lists.findIndex(item => item.partNumber == record.partNumber)
			if (index > -1 ) {
                this.vloading = false
                this.$message.error('已存在相同的替代品')
				return false;
			}

            
			getPartRandom({}).then((res) => {
				if (res.success) {
					this.treeData.lists.push({
						'id':res.data,
						'partName':record.partName,
						'partDescription':record.partDescription,
						'partUnit':record.partUnit,
                        'partClass':record.partClass,
                        'partNumber':record.partNumber,
                        'sapNumber':record.sapNumber,
						'partUse':0.000,
						'partLoss':0.01,
                        'partGroup':this.partGroupVal,
                        'partPriority':'1',
                        'partMaybe':'0',
                        'posnr':'',
                        'version':'',
                        'desc':'',
                        'validate':false,
					})
                    this.savetoBom()
					
				} else {
					this.$message.error(res.message)
				}
				this.vloading = false
			}).catch((err) => {
				this.$message.error('错误：' + err.message)
				this.vloading = false
			})
            
        },
    },
    created(){
    }
}
</script>

<style>
.main_modal{
	display: flex;
 	width: 100%;
}
.left_main_modal {
	flex: 1;
	overflow: auto;
}
.right_main_modal {
  margin-left: 12px;
  width: 480px;
  display: block;
  overflow: auto;
  transition: width 0.5s;
 }
 .right_main_show {
  width: 0;
  margin: 0;
 }
 .right_main_show *{
	display: none;
 }
 .table-page-search-wrapper .ant-form-inline .ant-form-item{
	margin-bottom: 14px;
  }
.table-page-search-wrapper .ant-form-inline .ant-form-item .ant-form-item-control{
	 height: auto;
 }
</style>