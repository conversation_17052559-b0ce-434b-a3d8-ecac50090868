<template>
  <a-modal
    :visible="visible"
    :width="1200"
    :footer="null"
    :destroy-on-close="true"
    :mask-closable="false"
    :keyboard="true"
    @cancel="closeModal"
    wrap-class-name="fitting-result-modal"
  >
    <template slot="title">
      <div class="modal-title">拟合结果详情</div>
    </template>

    <div v-if="!resultData" class="empty-content">
      <a-empty description="暂无拟合结果数据" />
    </div>

    <div v-else class="result-content">
      <div class="control-panel">
        <a-radio-group v-model="columnsCount" buttonStyle="solid" size="small">
          <a-radio-button :value="1">一列</a-radio-button>
          <a-radio-button :value="2">两列</a-radio-button>
          <a-radio-button :value="3">三列</a-radio-button>
          <a-radio-button :value="4">四列</a-radio-button>
        </a-radio-group>
      </div>

      <div class="temperature-charts-grid" :class="`columns-${columnsCount}`">
        <div v-for="(temperature, index) in temperatures" :key="index" class="chart-container">
          <a-card :title="`${temperature}°C 容量衰减曲线`" size="small" :bordered="true" :headStyle="{textAlign: 'center'}">
            <div :ref="`tempChart_${index}`" class="chart-wrapper"></div>
          </a-card>
        </div>
      </div>

      <div v-if="isLoading" class="loading-message">
        <a-spin tip="正在计算拟合结果..." />
      </div>

      <div v-else-if="temperatures.length === 0 || socs.length === 0" class="error-message">
        <a-alert
          message="数据加载错误"
          description="未能加载温度或SOC数据，请确保上传的Excel文件包含有效数据。"
          type="error"
          show-icon
        />
      </div>
    </div>
  </a-modal>
</template>

<script>
import * as echarts from 'echarts';
import { mapGetters } from 'vuex';
import { renderMathJax } from '@/utils/mathUtils';
import fittingMixin from '@/mixins/fittingMixin';
import { api } from '@/api';
import { chartColors, createCapacityCurveOptions, createScatterSeries, getChartColor, createTooltipFormatter } from '@/utils/chartUtils';

export default {
  name: 'FittingResultModal',
  mixins: [fittingMixin],
  data() {
    return {
      temperatureCharts: [],
      columnsCount: 3,
      dataPoints: [],
      calculatedDataPoints: [],
      temperatures: [],
      socs: [],
      isLoading: false,
      colors: chartColors
    };
  },
  computed: {
    ...mapGetters({
      resultData: 'getFittingResultData',
      visible: 'getFittingResultModalVisible'
    })
  },
  watch: {
    visible(newVal) {
      if (newVal && this.resultData) {
        this.updateDataFromResult();
        this.handleModalOpen();

        if (this.resultData.calculatedDataPoints?.length > 0) {
          this.renderCharts();
        } else {
          this.calculateFittedValues();
        }
      }
    },
    columnsCount() {
      this.renderCharts();
    },
    resultData: {
      handler(newVal) {
        if (!newVal) return;
        this.updateDataFromResult();
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    closeModal() {
      this.$store.commit('setFittingResultModalVisible', false);
    },

    handleModalOpen() {
      // 确保模态框打开时正确管理焦点
      this.$nextTick(() => {
        const modal = document.querySelector('.fitting-result-modal .ant-modal');
        if (modal) {
          modal.focus();
        }
      });
    },

    updateDataFromResult() {
      if (!this.resultData) return;

      this.dataPoints = this.resultData.dataPoints || [];
      this.temperatures = this.resultData.temperatures || [];
      this.socs = this.resultData.socs || [];

      if (this.resultData.calculatedDataPoints?.length > 0) {
        this.calculatedDataPoints = this.resultData.calculatedDataPoints;
      }
    },

    prepareCalculationParams() {
      const optimizedParams = this.resultData.optimized_params || [];

      // 准备条件参数
      const conditions = this.dataPoints.map(point => [
        point.temperature,
        point.soc,
        Math.max(...(Array.isArray(point.days) ? point.days : [point.days]))
      ]);

      // 准备请求参数
      return {
        latex_str: this.resultData.formula,
        params: optimizedParams.map(param => ({
          name: param.name,
          type: 'coefficient',
          value: param.value,
          describe: ''
        })),
        conditions: conditions
      };
    },

    async calculateFittedValues() {
      if (!this.resultData || !this.dataPoints?.length) return;

      try {
        this.isLoading = true;

        // 准备计算参数
        const requestParams = this.prepareCalculationParams();

        // 发送请求
        const response = await api.data.calculateCapacity(requestParams);

        // 处理响应
        if (response.data.success && response.data.data_points?.length > 0) {
          this.calculatedDataPoints = response.data.data_points;

          // 更新状态
          this.$store.commit('setFittingResultData', {
            ...this.resultData,
            calculatedDataPoints: this.calculatedDataPoints
          });

          // 渲染图表
          this.renderCharts();
        } else {
          console.error('计算拟合结果失败:', response.data.message);
        }
      } catch (error) {
        console.error('计算拟合结果失败:', error);
      } finally {
        this.isLoading = false;
      }
    },

    getDerivedColor(baseColor, type = 'fitted', alpha = 1) {
      const rgbaMatch = baseColor.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*[\d.]+\)/);
      if (!rgbaMatch) return baseColor;

      const r = parseInt(rgbaMatch[1], 10);
      const g = parseInt(rgbaMatch[2], 10);
      const b = parseInt(rgbaMatch[3], 10);

      if (type === 'fitted') {
        const newR = Math.max(0, Math.min(255, r * 0.9));
        const newG = Math.max(0, Math.min(255, g * 0.9));
        const newB = Math.max(0, Math.min(255, b * 0.9));
        return `rgba(${Math.round(newR)}, ${Math.round(newG)}, ${Math.round(newB)}, ${alpha})`;
      }

      return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    },



    getChartOptions(_temperature, series) {
      return createCapacityCurveOptions(
        ``, // 删除图表上方的温度标签
        series,
        {
          legendPosition: 'bottom',
          gridConfig: {
            bottom: 80,
            top: 30 // 减小上方间距
          },
          tooltip: {
            formatter: createTooltipFormatter
          }
        }
      );
    },

    renderCharts() {
      // 清理现有图表
      this.temperatureCharts.forEach(chart => {
        if (chart) chart.dispose();
      });
      this.temperatureCharts = [];

      // 检查数据有效性
      if (!this.temperatures.length || !this.socs.length || !this.dataPoints.length) {
        return;
      }

      this.$nextTick(() => {
        setTimeout(() => {
          this.temperatures.forEach((temp, index) => {
            const chartRefs = this.$refs[`tempChart_${index}`];
            if (!chartRefs || chartRefs.length === 0) return;

            // 初始化图表
            const chartDom = chartRefs[0];
            const chart = echarts.init(chartDom);
            this.temperatureCharts.push(chart);

            // 准备数据系列
            const series = this.prepareSeries(temp, this.socs, this.dataPoints);

            // 设置图表选项
            chart.setOption(this.getChartOptions(temp, series));
          });

          // 添加窗口大小调整监听器
          window.addEventListener('resize', this.resizeCharts);
        }, 300);
      });
    },

    parseNumericValue(value) {
      return typeof value === 'string' ? parseFloat(value) : value;
    },

    filterPointsByTemperature(points, temperature) {
      const tempValue = this.parseNumericValue(temperature);
      return points.filter(point => {
        const pointTemp = this.parseNumericValue(point.temperature);
        return Math.abs(pointTemp - tempValue) < 0.01;
      });
    },

    filterPointsBySOC(points, socValue) {
      return points.filter(point => {
        const pointSoc = this.parseNumericValue(point.soc);
        return Math.abs(pointSoc - socValue) < 0.01;
      });
    },

    extractSOCValues(points) {
      const socValues = new Set();
      points.forEach(point => {
        socValues.add(this.parseNumericValue(point.soc));
      });
      return Array.from(socValues).sort((a, b) => a - b);
    },

    processCapacityPoints(point, isCalculated = false) {
      try {
        const days = Array.isArray(point.days) ? point.days : [point.days];
        const capacities = isCalculated
          ? (Array.isArray(point.fitted_capacity) ? point.fitted_capacity : [point.fitted_capacity])
          : (Array.isArray(point.capacities) ? point.capacities : [point.capacities]);

        if (!days.length || !capacities.length) {
          return [];
        }

        // 找到第一个有效的容量值作为初始容量
        let initialCapacity = null;
        for (let i = 0; i < capacities.length; i++) {
          if (capacities[i] !== null && capacities[i] !== undefined && !isNaN(capacities[i])) {
            initialCapacity = capacities[i];
            break;
          }
        }

        if (!initialCapacity) {
          return [];
        }

        const points = [];
        for (let i = 0; i < Math.min(days.length, capacities.length); i++) {
          const day = days[i];
          const capacity = capacities[i];

          if (day !== null && day !== undefined && !isNaN(day) &&
              capacity !== null && capacity !== undefined && !isNaN(capacity)) {
            // 计算容量保持率并格式化为小数点后4位
            const retention = parseFloat(((capacity / initialCapacity) * 100).toFixed(4));
            points.push([day, retention]);
          }
        }
        return points;
      } catch (error) {
        console.error(`处理${isCalculated ? '计算' : '实测'}数据点时出错:`, error);
        return [];
      }
    },

    createSeriesItem(name, type, data, color, options = {}) {
      // 根据类型创建不同的图表系列
      if (type === 'scatter') {
        // 实测值使用散点图
        return createScatterSeries(name, data, 0, {
          itemStyle: { color },
          z: 2,
          symbolSize: 4,
          ...options
        });
      } else {
        // 拟合值使用折线图
        return {
          name: name,
          type: 'line',
          data: data,
          smooth: true,
          symbolSize: 0, // 不显示数据点标记
          showSymbol: false,
          z: 1,
          lineStyle: {
            width: 2,
            color: color,
            shadowBlur: 4,
            shadowColor: 'rgba(0, 0, 0, 0.1)'
          },
          itemStyle: {
            color: color
          },
          ...options
        };
      }
    },

    prepareSeries(temperature, _socs, allDataPoints) {
      const series = [];

      // 按温度筛选数据点
      const tempDataPoints = this.filterPointsByTemperature(allDataPoints, temperature);
      const tempCalculatedPoints = this.filterPointsByTemperature(this.calculatedDataPoints, temperature);

      // 提取所有SOC值
      const allPoints = [...tempDataPoints, ...tempCalculatedPoints];
      const tempSOCs = this.extractSOCValues(allPoints);

      // 为每个SOC值创建系列
      tempSOCs.forEach(socValue => {
        // 按SOC筛选数据点
        const socPoints = this.filterPointsBySOC(tempDataPoints, socValue);
        const socCalculatedPoints = this.filterPointsBySOC(tempCalculatedPoints, socValue);

        // 处理实测和拟合数据点
        let measuredPointsByDay = [];
        let fittedPointsByDay = [];

        socPoints.forEach(point => {
          const points = this.processCapacityPoints(point, false);
          measuredPointsByDay = [...measuredPointsByDay, ...points];
        });

        socCalculatedPoints.forEach(point => {
          const points = this.processCapacityPoints(point, true);
          fittedPointsByDay = [...fittedPointsByDay, ...points];
        });

        // 按天数排序
        measuredPointsByDay.sort((a, b) => a[0] - b[0]);
        fittedPointsByDay.sort((a, b) => a[0] - b[0]);

        // 创建系列
        const colorIndex = Math.floor(socValue * 10) % this.colors.length;
        const baseColor = getChartColor(colorIndex);
        const socLabel = `SOC ${(socValue * 100).toFixed(0)}%`;

        if (measuredPointsByDay.length > 0) {
          const measuredColor = this.getDerivedColor(baseColor, 'measured', 1.0);
          series.push(this.createSeriesItem(
            `${socLabel} 实测值`,
            'scatter',
            measuredPointsByDay,
            measuredColor
          ));
        }

        if (fittedPointsByDay.length > 0) {
          const fittedColor = this.getDerivedColor(baseColor, 'fitted', 0.8);
          series.push(this.createSeriesItem(
            `${socLabel} 拟合值`,
            'line',
            fittedPointsByDay,
            fittedColor
          ));
        }
      });

      return series;
    },

    resizeCharts() {
      this.$nextTick(() => {
        this.temperatureCharts.forEach(chart => {
          if (chart) chart.resize();
        });
      });
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeCharts);

    this.temperatureCharts.forEach(chart => {
      if (chart) chart.dispose();
    });
    this.temperatureCharts = [];
  },
  mounted() {
    this.$nextTick(() => renderMathJax());
  }
};
</script>

<style scoped>
/* 标题和内容样式 */
.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.empty-content {
  padding: 40px 0;
  text-align: center;
}

.result-content {
  max-height: 80vh;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 8px;
}

/* 控制面板样式 */
.control-panel {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 图表网格布局 */
.temperature-charts-grid {
  display: grid;
  gap: 16px;
  width: 100%;
}

.temperature-charts-grid.columns-1 {
  grid-template-columns: 1fr;
}

.temperature-charts-grid.columns-2 {
  grid-template-columns: repeat(2, 1fr);
}

.temperature-charts-grid.columns-3 {
  grid-template-columns: repeat(3, 1fr);
}

.temperature-charts-grid.columns-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* 图表容器样式 */
.chart-container {
  margin-bottom: 8px;
  width: 100%;
}

.chart-wrapper {
  width: 100%;
  height: 360px;
}

/* 消息样式 */
.error-message, .loading-message {
  margin-top: 16px;
  text-align: center;
  padding: 20px 0;
}
</style>

<style>
/* 模态框样式 */
.fitting-result-modal .ant-modal {
  top: 24px;
  width: 1200px !important;
}

.fitting-result-modal .ant-modal-content,
.fitting-result-modal .ant-modal-body {
  width: 100%;
}

.fitting-result-modal .ant-modal-body {
  padding: 16px;
  overflow-x: hidden;
}

.fitting-result-modal .ant-modal-header {
  padding: 12px 16px;
}

/* 卡片样式 */
.fitting-result-modal .ant-card-head {
  min-height: 36px;
}

.fitting-result-modal .ant-card-head-title {
  font-weight: 500;
  font-size: 14px;
  padding: 8px 0;
}

.fitting-result-modal .ant-card-body {
  padding: 12px;
  overflow: hidden;
}

/* 其他组件样式 */
.fitting-result-modal .ant-radio-button-wrapper {
  padding: 0 8px;
}

.fitting-result-modal .ant-card-body canvas {
  width: 100% !important;
}
</style>