/deep/.search-container .ant-input,
/deep/.search-container .vue-treeselect__control {
  height: 28px !important;
  font-size: 12px;
  border-radius: 4px;
  border-color: #Dee1e8;
}

/deep/.search-container .ant-calendar-picker:hover .ant-calendar-picker-input:not(.ant-input-disabled),
/deep/.search-container .vue-treeselect:not(.vue-treeselect--disabled):not(.vue-treeselect--focused) .vue-treeselect__control:hover,
/deep/.search-container .ant-input-affix-wrapper:hover .ant-input:not(.ant-input-disabled) {
  border-color: #1890ff;
}


/deep/.search-container .ant-calendar-picker,
/deep/.search-container .ant-input {
  color: #333;
  width: 100%;
}




/deep/.search-container .vue-treeselect__placeholder,
/deep/.search-container .vue-treeselect__single-value {
  line-height: 28px;
}

/deep/.search-container .vue-treeselect--has-value .vue-treeselect__multi-value {
  margin-bottom: 0;
}

/deep/.search-container .vue-treeselect__multi-value-item-container {
  padding-top: 2px;
}

/deep/.search-container .vue-treeselect__multi-value-item,
/deep/.search-container .vue-treeselect--searchable.vue-treeselect--multi.vue-treeselect--has-value .vue-treeselect__input-container {
  padding: 0;
}

/deep/.search-container .vue-treeselect__limit-tip {
  padding-top: 0;
}




// 数字输入框
/deep/.search-container .ant-input-number{
  font-size: 12px;
  line-height: 28px;
  border-radius: 4px;
  color: #333;
  border: 1px solid #Dee1e8;
  height: 28px;
}

/deep/.search-container .ant-input-number-sm{
  height: 28px; 
}

/deep/.search-container .ant-input-number:hover{
  border-color: #1890ff;
}


// 按钮
/deep/.search-container .ant-btn {
  height: 28px;
  min-width: 50px;
  padding: 0 8px;
  font-size: 12px;
  border-radius: 4px;
}

/deep/.search-container .ant-btn-sm {
  padding: 0 8px;
  font-size: 12px;
  min-width: 50px !important;
  border-radius: 4px;
}

// 主要按钮

.search-container .main-btn .ant-btn-primary:hover {
  background-color: #00b5ff;
}

// 次要按钮
/deep/.search-container .secondary-btn .ant-btn {
  border-color: #Dee1e8;
}

.search-container .secondary-btn .ant-btn:hover {
  color: #2f54e8;
  border-color: #adc6ff;
  background-color: #d6e4ff;
}


// 切换按钮
/deep/.search-container .toggle-btn .ant-btn-link {
  color: #8c8c8c;
}
.search-container .toggle-btn span{
  transform: rotate(90deg);
  margin-left: 4px;
}
.search-container .toggle-btn .ant-btn-link:hover{
  color: #1890ff;
}


// 多选
/deep/.search-container .ant-select{
  font-size: 12px;
  color: #333;
  width: 100%;
}

/deep/.search-container .ant-select-selection{
  border: 1px solid #Dee1e8;
  border-radius: 4px;
}

/deep/.search-container .ant-select-selection--multiple{
  max-height: 28px!important;
  padding-bottom: 0;
}
/deep/.ant-select-selection--multiple{
  max-height: 28px!important;
  padding-bottom: 0;
}

/deep/.ant-select-selection--single {
  position: relative;
  max-height: 28px!important;
  cursor: pointer;
}

/deep/.search-container .ant-select-selection--multiple .ant-select-selection__rendered{
  margin-bottom: 0;
}

/deep/.search-container .ant-select-selection__rendered{
  line-height: 26px;
}

/deep/.search-container .ant-select-selection--multiple > ul > li, 
/deep/.search-container .ant-select-selection--multiple .ant-select-selection__rendered > ul > li{
  height: 22px;
  line-height: 20px;
}


/deep/.ant-select-selection--multiple {
  min-height: 28px;
}


// 通用
.mr12 {
  margin-right: 12px;
}
