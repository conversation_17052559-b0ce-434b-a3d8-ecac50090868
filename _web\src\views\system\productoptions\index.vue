<template>
	<div class="container">
		<!-- 面包屑 start -->
		<div>
			<a-breadcrumb class="breadcrumb" separator=">">
				<a-breadcrumb-item
					><a @click="gohome"><a-icon class="rollback-icon" type="rollback" />产品看板</a></a-breadcrumb-item
				>
				<a-breadcrumb-item><a @click="goprolist">产品信息对齐表</a></a-breadcrumb-item>

				<a-breadcrumb-item
					><a v-if="activeKey == '4' && showdoc" @click="hidedoc">{{ tabsMenu[activeKey] }}</a
					><span v-else>{{ tabsMenu[activeKey] }}</span></a-breadcrumb-item
				>
				<a-breadcrumb-item v-if="activeKey == '4' && showdoc">{{ productName }}产品主要技术文档</a-breadcrumb-item>
			</a-breadcrumb>
		</div>
		<!-- 面包屑 end -->

		<!-- 主标题 start -->
		<div class="head-title">{{ titleMenu[activeKey] }}</div>
		<!-- 主标题 end -->

		<!-- tabs start -->
		<div class="tabs-wrap">
			<a-tabs type="card" :activeKey="activeKey" @change="handleTabsActive" destroyInactiveTabPane>
				<a-tab-pane v-for="(item, index) in tabsMenu" :key="index" :tab="item">
					<div class="tab-wrap">
						<baseInfo v-if="activeKey === 0" :tableHeight="tableHeight" :scrollHeigh="scrollHeigh" />
						<params v-if="activeKey === 1" :tableHeight="tableHeight" :scrollHeigh="scrollHeigh" />
						<!-- <technology v-if="activeKey === 2" :tableHeight="tableHeight" :scrollHeigh="scrollHeigh" /> -->
						<stageplan v-if="activeKey === 2" :tableHeight="tableHeight" :scrollHeigh="scrollHeigh" />
						<process v-if="activeKey === 3" :tableHeight="tableHeight" :scrollHeigh="scrollHeigh" />
						<prodocs
							v-if="activeKey === 4 && !showdoc"
							:tableHeight="tableHeight"
							:scrollHeigh="scrollHeigh"
							@showdetail="showdetail"
						/>
						<techdoc
							v-if="activeKey === 4 && showdoc"
							:tabHeight="tableHeight + 68"
							:issueId="issueId"
							:productName="productName"
							techStatus="2"
						/>
					</div>
				</a-tab-pane>
			</a-tabs>
		</div>
		<!-- tabs end -->
	</div>
</template>

<script>
import baseInfo from "./baseInfo"
import process from "./process"
import params from "./params"
import technology from "./technology.vue"
import prodocs from "./prodocs"
import techdoc from "./techdoc/index"
import stageplan from "./stageplan"
import { mixin } from "@/utils/mixin"
import { mapActions } from "vuex"
export default {
	mixins: [mixin],
	components: {
		baseInfo,
		process,
		params,
		technology,
		prodocs,
		techdoc,
		stageplan
	},
	watch: {
		sidebarOpened(val) {
			this.collapsed = !val
		}
	},
	data() {
		return {
			issueId: 0,
			showdoc: false,
			menuTitle: "",
			productName: "",
			menusinfos: {
				1: "基本信息",
				2: "产品参数",
				3: "产品开发进度",
				4: "产品开发进展",
				5: "产品文档"
			},
			navTag: "-1",
			collapsed: false,
			width187: {
				width: "187px",
				textAlign: "right"
			},
			width40: {
				width: "40px",
				textAlign: "center"
			},
			// tabs
			activeKey: 0, // 当前激活 tab 面板的 key
			tabsMenu: ["基本信息", "产品参数信息", "产品里程碑", "产品周进展", "产品技术文档"],
			titleMenu: ["产品管理基本信息", "产品参数信息", "产品里程碑", "产品周进展", "文档管理"],
			// 表格高度
			// 40：layout 20 : 内边距 14：面包屑 32 主标题边距 27：标题 45：tabs块 68：筛选块 63：分页区
			tableHeight: document.documentElement.clientHeight - 40 - 20 - 14 - 20 - 27 - 45 - 68,
			scrollHeigh: document.documentElement.clientHeight - 40 - 20 - 14 - 20 - 27 - 45 - 68 - 63
		}
	},
	methods: {
		getByClass(parent, cls) {
			if (parent.getElementsByClassName) {
				return Array.from(parent.getElementsByClassName(cls));
			} else {
				var res = [];
				var reg = new RegExp(' ' + cls + ' ', 'i')
				var ele = parent.getElementsByTagName('*');
				for (var i = 0; i < ele.length; i++) {
					if (reg.test(' ' + ele[i].className + ' ')) {
						res.push(ele[i]);
					}
				}
				return res;
			}
		},
		initMain() {
			let that = this
			that.$nextTick(() => {
				let items = that.getByClass(document, 'ant-layout-content')
				for (const e of items) {
					e.style.paddingLeft = 0
				}
			})
		},
		hidedoc() {
			this.issueId = 0
			this.showdoc = false
		},
		showdetail(row) {
			this.productName = row.productProjectName
			this.issueId = row.issueId
			this.showdoc = true
		},
		gohome() {
			this.$router.push({
				path: "/product_chart"
			})
		},
		goprolist() {
			this.$router.push({
				path: "/report_sum"
			})
		},
		showView(tg) {
			this.navTag = tg + ""
			window.sessionStorage.setItem("tag", tg + "")
			this.menuTitle = this.menusinfos[this.navTag]
		},
		// 切换tabs
		handleTabsActive(activeKey) {
			this.activeKey = activeKey
			sessionStorage.setItem("activeKey", activeKey)
		}
	},
	created() {
		if (window.sessionStorage.getItem("tag")) {
			this.navTag = window.sessionStorage.getItem("tag")
		}
		this.menuTitle = this.menusinfos[this.navTag]
		this.collapsed = !this.sidebarOpened
		this.initMain()
	},
	mounted() {
		if (!window.sessionStorage.getItem("tag")) {
			window.sessionStorage.setItem("tag", this.navTag)
		} else if (window.sessionStorage.getItem("tag")) {
			this.navTag = window.sessionStorage.getItem("tag")
		}

		if (sessionStorage.getItem("activeKey")) {
			this.activeKey = Number(sessionStorage.getItem("activeKey"))
		}
	},
	destroyed() {
		window.sessionStorage.removeItem("tag")
		sessionStorage.removeItem("activeKey")
	}
}
</script>
<style lang="less" scoped="">
/deep/.ant-layout-content{
	padding-left: 0 !important;
}
.wrap {
	background: #f0f2f5;
}
.layout-main {
	display: flex;
	flex-direction: row;
}
.wrap {
	flex: 1;
}
.slide {
	max-width: 200px;
	background-color: #fff;
	box-shadow: 2px 0px 8px 0px rgba(29, 35, 41, 5%);
	z-index: 100;
	height: 100vh;
}
.collapsed_bar {
	position: fixed;
	width: 20px;
	bottom: 0;
	/* top: 0; */
	cursor: pointer;
}
/deep/.ant-menu-light {
	border-right-color: transparent;
}
/deep/.ant-menu-inline-collapsed {
	width: 40px !important;
}
/deep/.ant-layout-sider-collapsed {
	flex: 0 0 40px !important;
	max-width: 40px !important;
	min-width: 40px !important;
	width: 40px !important;
}
/deep/.ant-menu-inline-collapsed > .ant-menu-item,
/deep/.ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item,
.ant-menu-inline-collapsed
	> .ant-menu-item-group
	> .ant-menu-item-group-list
	> .ant-menu-submenu
	> .ant-menu-submenu-title,
.ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title {
	padding: 0 12px !important;
}
/deep/.ant-menu-vertical .ant-menu-item,
/deep/.ant-menu-inline .ant-menu-item {
	margin-top: 0;
}

.ant-breadcrumb a {
	color: #5d90fa !important;
}
.ant-breadcrumb a:first-child {
	color: rgba(0, 0, 0, 0.65) !important;
}
.ant-breadcrumb {
	font-size: 12px !important;
	color: rgba(0, 0, 0, 0.65) !important;
}
/deep/.ant-breadcrumb .anticon.anticon-home {
	font-size: 19px;
}
</style>
<style lang="less" scoped>
.ant-layout-sider-collapsed {
	flex: 0 0 40px !important;
	max-width: 40px !important;
	min-width: 40px !important;
	width: 40px !important;
}
.ant-menu-inline-collapsed > .ant-menu-item,
.ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item,
.ant-menu-inline-collapsed
	> .ant-menu-item-group
	> .ant-menu-item-group-list
	> .ant-menu-submenu
	> .ant-menu-submenu-title,
.ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title {
	padding: 0 12px !important;
}
.ant-menu-vertical .ant-menu-item,
.ant-menu-vertical-left .ant-menu-item,
.ant-menu-vertical-right .ant-menu-item,
.ant-menu-inline .ant-menu-item,
.ant-menu-vertical .ant-menu-submenu-title,
.ant-menu-vertical-left .ant-menu-submenu-title,
.ant-menu-vertical-right .ant-menu-submenu-title,
.ant-menu-inline .ant-menu-submenu-title {
	font-size: 13px;
}

/* 面包屑 */
.breadcrumb {
	/* padding-bottom: 16px; */
}

/* 主标题 */

.head-title {
	color: #333;
	padding: 10px 0;
	font-size: 18px;
	font-weight: 600;
}

.head-title::before {
	width: 8px;
	background: #1890ff;
	margin-right: 8px;
	content: "\00a0"; /* 填充空格 */
}

/* tabs */
.tab-wrap {
	background-color: #fff;
	border-radius: 0 10px 10px 10px;
	margin-left: 1px;
}

/* tabs 组件 */
/deep/.ant-tabs-bar {
	margin: 0;
}

/deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab {
	border-radius: 10px 10px 0 0;
	font-size: 15px;
}

// tabs块级颜色
/deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab {
	background-color:rgba(232, 232, 232, 0.5);
	margin-right: 4px;
}
/deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
	background-color: #fff;
}
</style>
