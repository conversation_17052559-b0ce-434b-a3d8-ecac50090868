<template>
  <div>
        <div class="topic_width">
            <a-breadcrumb class="breadcrumb" separator=">">
                <a-breadcrumb-item><a @click="goBack">课题看板</a></a-breadcrumb-item>
                <a-breadcrumb-item>人均课题</a-breadcrumb-item>
            </a-breadcrumb>
        </div>
        
        <x-card class="topic_width">
            <div slot="content" class="table-page-search-wrapper">
                <a-form layout="inline">
                    <a-row :gutter="48">
                        <a-col :md="8" :sm="24">
                            <a-form-item label="立项申请">
                               <a-range-picker
                                    size="small"
                                    :placeholder="['开始月份', '结束月份']"
                                    v-model="dates"
                                    :mode="['month', 'month']"
                                    format="YYYY-MM"
                                    @panelChange="handlePanelChange"
                                    @openChange="handleOpenChange"
                                    :open="monthPickShow"
                                >
                                    <a-icon slot="suffixIcon" type="calendar" style="color:#d9d9d9" />
                                </a-range-picker>
                            </a-form-item>
                        </a-col>
                        <a-col :md="3" :sm="24">
                            <a-form-item label="">
                                <!-- <a-tree-select multiple size="small"
                        :show-checked-strategy="SHOW_PARENT" @change="this.change" v-model="queryParam.deptId" :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" :tree-data="depts" placeholder="请选择部门" tree-default-expand-all>
                                </a-tree-select> -->
                                <treeselect :limit="1" @input="change" :max-height="200" placeholder="请选择部门" :value-consists-of="ALL" v-model="queryParam.deptId" :multiple="true" :options="depts" :normalizer="normalizer" />
                            </a-form-item>
                        </a-col>
                        <a-col :md="1" :sm="24">
                            <span class="table-page-search-submitButtons">
                            <!-- <a-button type="primary" @click="callGetAvgProjects" >查询</a-button> -->
                            <a-button size="small" style="margin-left: 8px" @click="resetquery">重置</a-button>
                        </span>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
        </x-card>
        <div class="topic_width head2">人均课题数分析</div>
        <a-table class="topic_width"  style="background: #fff" :pagination="false" :columns="columns" :dataSource="loadData" :loading="loading"  >
            <!-- <template slot="footer">
                合计： <span class="foot">总人数:{{sum}}</span> <span class="foot">立项通过数:{{count}}</span> <span class="foot">人均课题数:{{ sum == 0 ? 0 : ((count/sum)*100).toFixed(1)+'%'}}</span>
            </template> -->
            <!-- <template slot="parentDeptAvgCount" slot-scope="text,record">
                <span v-if="record.parentDeptCount == 0">0</span>
                <span v-else>{{(record.parentDeptPassCount/record.parentDeptCount).toFixed(1)}}</span>
            </template> -->
            <!-- <template slot="childDeptAvgCount" slot-scope="text,record">
                <div class="text_center">
                    <span v-if="record.childDeptCount == 0">0%</span>
                    <span v-else>{{((record.childDeptPassCount/record.childDeptCount)*100).toFixed(1)+'%'}}</span>
                </div>
            </template> -->

            <div class="divcls" slot="divcls" slot-scope="text">{{text}}</div>
        </a-table>
    <a-modal
      title="明细"
      :width="width"
      v-model="visible"
      :zIndex="parseInt(2)"
      :bodyStyle="{overflow:'hidden',overflowY: 'scroll',maxHeight:clientHeight - 180 + 'px'}"
      @cancel="() => visible=false"
    >


      <template slot="footer">
        <a-button key="back" @click="() => visible=false">
          关闭
        </a-button>

      </template>

      <div>

        <a-table style="background:#fff" size="small"

                 :scroll="{x:bigClient?false:true}"

                 :rowKey="(record) => record.issueId" :columns="columnsIn" :dataSource="detailList" :loading="loading"
        >


          <div slot="topicName" slot-scope="text">
            <clamp :text="text" :sourceText="[text]" :isCenter="true"></clamp>
          </div>


          <div slot="projectLevel" slot-scope="text" style="width: 100%;text-align: center">
            {{levels[text]}}

          </div>

          <div slot="researchContent" slot-scope="text">
            <clamp :text="text" :sourceText="[text]" :isCenter="true"></clamp>
          </div>

          <div slot="projectBackGround" slot-scope="text">
            <clamp :text="text" :sourceText="[text]" :isCenter="true"></clamp>
          </div>


          <div slot="cate1">1级<br/>分类</div>
          <div slot="cate2">2级<br/>分类</div>
          <div slot="cate3">3级<br/>分类</div>
          <div slot="projectNameTitle">课题名称</div>
          <div slot="projectLevelTitle">课题等级</div>
          <div slot="projectLeaderTitle">课题负责人</div>
          <div slot="platformAndTopic">平台课题</div>


          <template slot="reviewResult1" slot-scope="text">
            <div style="text-align:center">
              <span class="green" v-if="text == 1">{{reviewRes[text]}}</span>
              <span class="red" v-else-if="text == 2">{{reviewRes[text]}}</span>
              <span class="yellow" v-else-if="text == 3">{{reviewRes[text]}}</span>

            </div>
          </template>

          <template slot="reviewOpinion1" slot-scope="text">
            <clamp :isCenter="true" :text="text" :sourceText="[text]"></clamp>
          </template>

          <template slot="reviewResult2" slot-scope="text">
            <div style="text-align:center">
              <span class="green" v-if="text == 1">{{reviewRes[text]}}</span>
              <span class="red" v-else-if="text == 2">{{reviewRes[text]}}</span>
              <span class="yellow" v-else-if="text == 3">{{reviewRes[text]}}</span>

            </div>
          </template>

          <template slot="reviewOpinion2" slot-scope="text">
            <clamp :text="text" :sourceText="[text]"></clamp>
          </template>





        </a-table>
      </div>

    </a-modal>
    </div>
</template>

<script>
import {XCard} from '@/components'
import {clamp} from '@/components'
import moment from 'moment';
import {getAvgProjects,getCateTree,getPlatformTopicsFromView} from "@/api/modular/system/topic"
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
export default {
    components: {
        XCard,
        Treeselect,
      clamp
    },
    data() {
        return {
            dates: [],
            monthPickShow: false,
            sum:0,
            count:0,
            depts:[],
            merges: ['parentDept'], reviewRes: ['/', '通过', '不通过', '再确认'],
          levels: ['', '', 'S', 'A','B'],
            windowHeight: document.documentElement.clientHeight,
          visible: false,
          width: document.documentElement.clientWidth * 0.8,
          height: document.documentElement.clientHeight * 0.3,
          bigClient: document.documentElement.clientHeight > 700,
          height1: document.documentElement.clientHeight * 0.3 + 100,
          clientHeight: document.documentElement.clientHeight,
          detailList:[],
          columnsIn: [

            {
              title: '序号',
              dataIndex: 'index',
              align: 'center',
              width: 40,
              customRender: (text, record, index) => ( <div
              class = 'divcls div_border_right div_btns' > {index+1} </div>)
            },
            {
              title: '平台一级分类',
              dataIndex: 'affiliatedPlatform1',
              align: 'center',
              width: 80,
              slots: {title: 'affiliatedPlatform1'},
            },
            {
              title: '平台二级分类',
              dataIndex: 'affiliatedPlatform2',
              align: 'center',
              width: 80,
              slots: {title: 'affiliatedPlatform2'},
            },
            {
              dataIndex: 'cateName',
              width: 80,
              align: 'center',
              title: '课题分类',
            },
            {
              dataIndex: 'topicName',
              width: 120,
              title: '课题名称',
              align:'center',
              scopedSlots: {customRender: 'topicName'},
            },

            {
              title: '研究内容',
              dataIndex: 'researchContent',
              width: 120,
              align:'center',
              scopedSlots: {customRender: 'researchContent'},
            },
            {
              title: '课题背景',
              dataIndex: 'projectBackground',
              width: 120,
              align:'center',
              scopedSlots: {customRender: 'projectBackGround'},
            },

            {
              slots: {title: 'projectLevelTitle'},
              dataIndex: 'projectLevel',
              scopedSlots: {customRender: 'projectLevel'},
              width: 50,
              align:'center',

            },
            {

              dataIndex: 'projectLeader',
              slots: {title: 'projectLeaderTitle'},
              width: 80,
              align:'center',
              scopedSlots: {customRender: 'projectLeader'},
            },
            {
              title: '部门',
              dataIndex: 'deptName',
              width: 100,
              align:'center',
            },

            {
              title: '评审结果',
              dataIndex: 'approvalReviewResult',
              width: 80,
              align: 'center',
              scopedSlots: {customRender: 'reviewResult1'},
            },
            {
              title: '当前节点',
              dataIndex: 'issuestatusName',
              width: 80,
              align: 'center'
            },{
              title: '立项评审日期',
              dataIndex: 'reviewDate',
              width: 80,
              align: 'center',
              customRender: (text, record, index) => null == text ?'':moment(new Date(text)).format('YYYY-MM-DD')
            },

          ],
            loading: true,
            columns: [
                {
                    title: '二级部门',
                    width: 120,
                    dataIndex: 'parentDept',
                    align:'center',
                    customRender: (text, row, index) => {
                        return {
                            children: text,
                            attrs: {
                                rowSpan: row['parentDept_rowSpan'] ? row['parentDept_rowSpan'] : 0
                            }
                        }
                    },
                },
                {
                    title: '总人数',
                    width: 80,
                    align:'center',
                    dataIndex: 'parentDeptCount',
                    customRender: (text, row, index) => {
                        return {
                            children: text,
                            attrs: {
                                rowSpan: row['parentDept_rowSpan'] ? row['parentDept_rowSpan'] : 0
                            }
                        }
                    },
                },
                {
                    title: '课题负责人数',
                    width: 80,
                    align:'center',
                    dataIndex: 'parentDeptLeaderCount',
                    customRender: (text, row, index) => {
                        return {
                            children: text,
                            attrs: {
                                rowSpan: row['parentDept_rowSpan'] ? row['parentDept_rowSpan'] : 0
                            }
                        }
                    },
                },
                {
                    title: '立项通过数',
                    width: 80,
                    align:'center',
                    dataIndex: 'parentDeptPassCount',
                    customRender: (text, row, index) => {
                        if (index == this.loadData.length -1) {
                            return text
                        }
                        return {
                          children: (<a onClick={() => this.openDetail(1,row.parentDept,null)}>{text}</a>),

                      attrs: {
                                rowSpan: row['parentDept_rowSpan'] ? row['parentDept_rowSpan'] : 0
                            }
                        }
                    },
                },
                {
                    title: '人均课题数',
                    width: 80,
                    align:'center',
                    dataIndex: 'parentDeptAvgCount',
                    scopedSlots: { customRender: 'parentDeptAvgCount' },
                    customRender: (text, row, index) => {
                        return {
                            children: row['parentDeptCount'] == 0 ?  0 : ((row['parentDeptPassCount']/row['parentDeptCount'])).toFixed(1),
                            attrs: {
                                rowSpan: row['parentDept_rowSpan'] ? row['parentDept_rowSpan'] : 0
                            }
                        }
                    },
                },
                {
                    title: '三级部门',
                    width: 120,
                    align:'center',
                    dataIndex: 'childDept',
                    scopedSlots: { customRender: 'divcls' },
                },
                {
                    title: '总人数',
                    width: 80,
                    align:'center',
                    dataIndex: 'childDeptCount',
                },
                {
                    title: '课题负责人数',
                    width: 80,
                    align:'center',
                    dataIndex: 'childDeptLeaderCount',
                },
                {
                    title: '立项通过数',
                    width: 80,
                    align:'center',
                    dataIndex: 'childDeptPassCount',
                  customRender: (text, row, index) => {
                    if (index == this.loadData.length -1) {
                        return text
                      }
                    return {
                        
                      children: (<a onClick={() => this.openDetail(1,null,row.childDept)}>{text}</a>),
                  }
                  },
                },
                {
                    title: '人均课题数',
                    width: 80,
                    align:'center',
                    dataIndex: 'childDeptAvgCount',
                    customRender: (text, row, index) => {
                      if (index == this.loadData.length -1) {
                        return ''
                      }
                        return {
                            children: row.childDeptCount == 0 ? 0 :((row.childDeptPassCount/row.childDeptCount)).toFixed(1)
                            //(((row['childDeptDelayCount']+row['childDeptStopCount'])/(row['childDeptCount']+row['childDeptDelayCount']+row['childDeptStopCount']+row['childDeptCloseCount']))*100).toFixed(1)+'%',
                        }
                    },
                    //scopedSlots: { customRender: 'childDeptAvgCount' },
                },
            ],
            loadData: [],
            queryParam: {},
            normalizer(node) {
				return {
					id: node.value,
					label: node.title,
					children: node.children && node.children.length > 0 ? node.children: 0,
				}
			},
        }
    },
    methods:{
        getByClass(parent, cls) {
			if (parent.getElementsByClassName) {
				return Array.from(parent.getElementsByClassName(cls));
			} else {
				var res = [];
				var reg = new RegExp(' ' + cls + ' ', 'i')
				var ele = parent.getElementsByTagName('*');
				for (var i = 0; i < ele.length; i++) {
					if (reg.test(' ' + ele[i].className + ' ')) {
						res.push(ele[i]);
					}
				}
				return res;
			}
		},
      openDetail(pass,parentDeptName,childDeptName){
        getPlatformTopicsFromView({reviewDateBegin:this.queryParam.startDate,childDeptName:childDeptName,
          reviewDateEnd:this.queryParam.endDate,pass:pass,parentDeptName:parentDeptName}).then((res) => {
          this.detailList = res.data
          this.visible = true
        })
      },
        init(){
            this.$nextTick(() => {
                let items = this.getByClass(document, 'divcls')
                for (const e of items) {
                    var _e = e.parentNode 
                    _e.classList.add('tdcls')
                }
            })
        },
        goBack(){
            this.$router.push({
                path: "/topic_chart",
            })
        },
        resetquery(){
            this.queryParam = {}
            this.dates = [moment().subtract(1, 'year').startOf('year'),moment()],

            this.queryParam.startDate = moment(this.dates[0]._d).format('YYYY-MM')
            this.queryParam.endDate = moment(this.dates[1]._d).format('YYYY-MM')

            this.callGetAvgProjects()
        },
        change(value, label, extra){
            this.callGetAvgProjects()
        },
        moment,
        handlePanelChange(value, mode) {
            if (this.dates[1] && this.dates[1]._d != value[1]._d) {
                this.dates = value
                this.monthPickShow = false;
                this.queryParam.startDate = moment(this.dates[0]._d).format('YYYY-MM')
                this.queryParam.endDate = moment(this.dates[1]._d).format('YYYY-MM')
                this.callGetAvgProjects()
            }
            this.dates = value
        },
        handleOpenChange(status) {
            if(status){
                this.monthPickShow = true;
            }else{
                this.monthPickShow = false
            }
        },
        callGetAvgProjects() {
            this.loading = true
            getAvgProjects(this.queryParam).then((res) => {
            if (res.success) {
                
                let deptCount = 0
                let passCount = 0
                let leaderCount = 0
                let depts = []
                for (const item of res.data.list) {
                    if(depts.indexOf(item.parentDept) > -1){
                        continue
                    }
                    deptCount += item.parentDeptCount
                    passCount += item.parentDeptPassCount
                    leaderCount+=item.parentDeptLeaderCount
                    depts.push(item.parentDept)
                }

                res.data.list.push({
                    parentDept:'合计',
                    parentDeptCount:deptCount,
                    parentDeptLeaderCount:leaderCount,
                    parentDeptPassCount:passCount
                })

                this.merges.forEach((item) => {
                    for (let i = 0, j = res.data.list.length; i < j; i++) {
                        let rowSpan = 0;
                        let n = i;
                        while (
                            res.data.list[n + 1] &&
                            res.data.list[n + 1][item] == res.data.list[n][item]
                        ) {
                            rowSpan++;
                            n++;
                            res.data.list[n].rowSpan = 0;
                        }
                        if (rowSpan) res.data.list[i][item + "_rowSpan"] = rowSpan + 1;
                        if (!rowSpan) res.data.list[i][item + "_rowSpan"] = 1;
                        i += rowSpan;
                    }
                });
                this.loadData = res.data.list
                this.init()
            } else {
                this.$message.error('错误提示：' + res.message,1)
            }
            this.loading = false
            })
            .catch((err) => {
                this.$message.error('错误提示：' + err.message,1)
                this.loading = false
            });
        },

        callGetDeptTree(){
            getCateTree({
                fieldName:'department',
                flag:1
            }).then((res)=>{
                if (res.success) {
                    this.depts = res.data
                } else {
                    this.$message.error('错误提示：' + res.message, 1)
                }
            }).catch((err) => {
                this.$message.error('错误提示：' + err.message, 1)
            });
        },

        /* callGetTree(){
            getCateTree({
                fieldName:'projectCate'
            }).then((res)=>{
                if (res.success) {
                    this.cate = res.data
                } else {
                    this.$message.error('错误提示：' + res.message, 1)
                }
            }).catch((err) => {
                this.$message.error('错误提示：' + err.message, 1)
            });
        }, */
    },
    created(){
        this.queryParam.deptId = [this.$route.query.deptId]
        this.queryParam.startDate = this.$route.query.startDate
        this.queryParam.endDate = this.$route.query.endDate
        this.dates =[moment(this.$route.query.startDate, 'YYYY-MM'),moment(this.$route.query.endDate, 'YYYY-MM')]
        this.callGetDeptTree()
        this.callGetAvgProjects()
        
    }
}
</script>

<style lang="less" scoped=''>
@import './topic.less';
span.foot{
    margin-right: 12px;
}

.red {
  display: inline-block;
  padding: 2px 8px;
  background: #ff3333;
  text-align: center;
  color: #fff;
}

.yellow {
  display: inline-block;
  padding: 2px 8px;
  background: #fac858;
  text-align: center;
  color: #fff;
}

.green {
  display: inline-block;
  padding: 2px 8px;
  background: #58a55c;
  text-align: center;
  color: #fff;
}

a {
  color: black;
}

/deep/.topic_width .ant-table-tbody > tr:last-child{
    background: #fafafa;
    font-size: 14px;
}
</style>