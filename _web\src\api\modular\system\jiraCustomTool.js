import { axios } from '@/utils/request'


// export const getReviewListData = (params) => {
//   return axios({
//     url: '/topic/review_list',
//     method: 'post',
//     data: params
//   })
// }
//
//
// export const getChartProjects = (params) => {
//   return axios({
//     url: '/chart/projects',
//     method: 'get',
//     params: params
//   })
// }

export function getAllOptionList (parameter) {
  return axios({
    url: '/jiraOption/getAllOptionList',
    method: 'post',
    data: parameter
  })
}

export function getJiraOptionList (parameter) {
  return axios({
    url: '/jiraOption/getOptionList',
    method: 'post',
    data: parameter
  })
}

