/**
 * 系统应用
 *
 * <AUTHOR>
 * @date 2020年4月23日12:10:57
 */
 import { axios } from '@/utils/request'


 export function getMaterialCodePage (parameter) {
   return axios({
     url: '/sysMaterialCode/page',
     method: 'post',
     data: parameter
   })
 }

 export function getMaterialDictPage (parameter) {
   return axios({
     url: '/sysMaterialDict/page',
     method: 'post',
     data: parameter
   })
 }


 export function addMaterialCode (parameter) {
    return axios({
      url: '/sysMaterialCode/add',
      method: 'post',
      data: parameter
    })
  }


 export function addMaterialDict (parameter) {
    return axios({
      url: '/sysMaterialDict/add',
      method: 'post',
      data: parameter
    })
  }


 export function updateMaterialCode (parameter) {
    return axios({
      url: '/sysMaterialCode/update',
      method: 'post',
      data: parameter
    })
  }

 export function deleteMaterialCode (parameter) {
    return axios({
      url: '/sysMaterialCode/delete',
      method: 'post',
      data: parameter
    })
  }


 export function updateMaterialDict (parameter) {
    return axios({
      url: '/sysMaterialDict/update',
      method: 'post',
      data: parameter
    })
  }

 export function deleteMaterialDict (parameter) {
    return axios({
      url: '/sysMaterialDict/delete',
      method: 'post',
      data: parameter
    })
  }