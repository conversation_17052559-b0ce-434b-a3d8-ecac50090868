import { axios } from '@/utils/request'

export function tLimsFolderListPage (parameter) {
  return axios({
    url: '/tLimsFolder/listPage',
    method: 'post',
    data: parameter
  })
}

export function getFolderByFolderNo (parameter) {
  return axios({
    url: '/tLimsFolder/getByFolderNo',
    method: 'post',
    data: parameter
  })
}

export function tLimsTestdataScheduleList(parameter) {
  return axios({
    url: '/tLimsTestdataSchedule/list',
    method: 'post',
    data: parameter
  })
}

export function g26BeginExportData(parameter) {
  return axios({
    url: '/g26TestReportTask/beginExportData',
    method: 'post',
    data: parameter
  })
}

export function hideData(parameter) {
  return axios({
    url: '/tLimsTestdataSchedule/hideData',
    method: 'post',
    data: parameter
  })
}

export function showData(parameter) {
  return axios({
    url: '/tLimsTestdataSchedule/showData',
    method: 'post',
    data: parameter
  })
}

export function tLimsTestdataSchedulePageList(parameter) {
  return axios({
    url: '/tLimsTestdataSchedule/pageList',
    method: 'post',
    data: parameter
  })
}

export function syncDorisData(parameter) {
  return axios({
    url: '/shenghong/syncDoris',
    method: 'post',
    data: parameter
  })
}

export function shenghongStepListPage(parameter) {
  return axios({
    url: '/shenghong/stepListPage',
    method: 'post',
    data: parameter
  })
}

export function shenghongStepInfoPage(parameter) {
  return axios({
    url: '/shenghong/stepInfoPage',
    method: 'post',
    data: parameter
  })
}
export function jmFlowInfoPage(parameter) {
  return axios({
    url: '/shenghong/flowInfoListPage',
    method: 'post',
    data: parameter
  })
}
export function jmFlowInfoSync(parameter) {
  return axios({
    url: '/shenghong/syncJM',
    method: 'post',
    data: parameter
  })
}

export function shenghongLogPage(parameter) {
  return axios({
    url: '/shenghong/logListPage',
    method: 'post',
    data: parameter
  })
}

export function shenghongDataListPage(parameter) {
  return axios({
    url: '/shenghong/dataListPage',
    method: 'post',
    data: parameter
  })
}

export function shenghongDataExport(parameter) {
  return axios({
    url: '/shenghong/export',
    method: 'post',
    data: parameter
  })
}


export function onlineEcharts(parameter) {
  return axios({
    url: '/testDataExportTask/onlineEcharts',
    method: 'post',
    data: parameter
  })
}


export function shenghongDataExportTaskList(parameter) {
  return axios({
    url: '/testDataExportTask/list',
    method: 'post',
    data: parameter
  })
}

export function shenghongDataExportTaskPageList(parameter) {
  return axios({
    url: '/testDataExportTask/pageList',
    method: 'post',
    data: parameter
  })
}

export function shenghongDataRegenerate(parameter) {
  return axios({
    url: '/testDataExportTask/regenerateData',
    method: 'post',
    data: parameter
  })
}

export function shenghongDataExportTaskSave(parameter) {
  return axios({
    url: '/testDataExportTask/save',
    method: 'post',
    data: parameter
  })
}


export function shenghongCycListPage(parameter) {
  return axios({
    url: '/shenghong/cycListPage',
    method: 'post',
    data: parameter
  })
}

export function shenghongDataFilterExport(parameter) {
  return axios({
    url: '/shenghong/filterDataExport',
    method: 'post',
    data: parameter
  })
}

export function deleteTestDataExportTask(parameter) {
  return axios({
    url: '/testDataExportTask/deleteById',
    method: 'post',
    data: parameter
  })
}

export function getLimsOrdtaskList (parameter) {
  return axios({
    url: '/tLimsOrdtask/getLimsOrdtaskList',
    method: 'post',
    data: parameter
  })
}

export function getLimsOrdtaskListOfSafety (parameter) {
  return axios({
    url: '/tLimsOrdtask/getLimsOrdtaskListOfSafety',
    method: 'post',
    data: parameter
  })
}

export function getLimsOrdtasksOfPreView (parameter) {
  return axios({
    url: '/tLimsOrdtask/getLimsOrdtasksOfPreView',
    method: 'post',
    data: parameter
  })
}

export function getTestPerson (parameter) {
  return axios({
    url: '/tLimsOrdtask/getTestPerson',
    method: 'post',
    data: parameter
  })
}

export function getOrdTasksByTesterCode (parameter) {
  return axios({
    url: '/tLimsOrdtask/getOrdTasksByTesterCode/' + parameter,
    method: 'get'
  })
}

export function assignTask (parameter) {
  return axios({
    url: '/tLimsOrdtask/updateOrdtaskStatus',
    method: 'post',
    data: parameter
  })
}

export function assignTaskOfAq (parameter) {
  return axios({
    url: '/tLimsOrdtask/assignTaskOfAq',
    method: 'post',
    data: parameter
  })
}

export function getFolderInfoByParam (parameter) {
  return axios({
    url: '/tLimsOrdtask/getFolderInfoByParam',
    method: 'post',
    data: parameter
  })
}

export function getLimsOrdtaskListByParam (parameter) {
  return axios({
    url: '/tLimsOrdtask/getLimsOrdtaskListByParam',
    method: 'post',
    data: parameter
  })
}

export function executeReviewSubmit (parameter) {
  return axios({
    url: '/tLimsOrdtask/reviewSubmit',
    method: 'post',
    data: parameter
  })
}

export function executeReviewSendBack (parameter) {
  return axios({
    url: '/tLimsOrdtask/reviewSendBack',
    method: 'post',
    data: parameter
  })
}

export function getProductList (parameter) {
  return axios({
    url: '/productManager/list',
    method: 'post',
    data: parameter
  })
}

export function testReportPageList (parameter) {
  return axios({
    url: '/testReportTask/pageList',
    method: 'post',
    data: parameter
  })
}

export function testReportSave (parameter) {
  return axios({
    url: '/testReportTask/save',
    method: 'post',
    data: parameter
  })
}

export function testReportGet (parameter) {
  return axios({
    url: '/testReportTask/get',
    method: 'post',
    data: parameter
  })
}

export function getCRateTestReport (parameter) {
  return axios({
    url: '/testReportTask/getCRateTestReport',
    method: 'post',
    data: parameter
  })
}

export function getRateStressTestReport (parameter) {
  return axios({
    url: '/testReportTask/getRateStressTestReport',
    method: 'post',
    data: parameter
  })
}

export function getCycleReport (parameter) {
  return axios({
    url: '/testReportTask/getCycleReport',
    method: 'post',
    data: parameter
  })
}

export function testReportRegenerateData (parameter) {
  return axios({
    url: '/testReportTask/regenerateData',
    method: 'post',
    data: parameter
  })
}

export function testReportDownload (parameter) {
  return axios({
    url: '/testReportTask/download',
    method: 'get',
    params: parameter,
    responseType: 'blob'
  })
}

export function testReportUpdateDate (parameter,id) {
  return axios({
    url: '/testReportTask/updateData?id='+id,
    method: 'post',
    data: parameter
  })
}

export function updateCRateData (parameter,id) {
  return axios({
    url: '/testReportTask/updateCRateData?id='+id,
    method: 'post',
    data: parameter
  })
}


export function testReportDelete(parameter) {
  return axios({
    url: '/testReportTask/deleteById',
    method: 'post',
    data: parameter
  })
}

export function getTemGradientsByOrderIdAndOrdtaskId(parameter, folderId, ordtaskId) {
  return axios({
    url: '/tLimsTemGradient/getByFolderIdAndOrdtaskId/' + folderId + '/' + ordtaskId,
    method: 'post',
    data: parameter
  })
}


// 多方案合并报告-报告任务提交
export function commitOptionMergeParam(parameter, reportName, id) {
  return axios({
    url: '/testReportTask/commitOptionMergeParam?reportName=' + reportName + (id ? '&id=' + id : ''),
    method: 'post',
    data: parameter
  })
}

// 多方案合并报告-报告查询
export function getOptionMergeReport (parameter) {
  return axios({
    url: '/testReportTask/getOptionMergeReport',
    method: 'post',
    data: parameter
  })
}

// 多方案合并报告报告-导出多方案合并报告报告数据
export function exportOptionMergeReport(parameter) {
  return axios({
    url: '/testReportTask/exportOptionMergeReport',
    method: 'post',
    data: parameter,
    responseType: 'blob'
  })
}

export function jmFlowInfoEdit(parameter) {
  return axios({
    url: '/shenghong/jmFlowInfoEdit',
    method: 'post',
    data: parameter
  })
}
