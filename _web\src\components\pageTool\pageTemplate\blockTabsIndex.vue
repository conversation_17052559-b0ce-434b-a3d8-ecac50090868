<template>
  <div class="page-container" :style="`margin:${pageMargin}`">

    <div class="page-title">
      <pbiTitle v-if="pageTitleShow" :title="pageTitle"></pbiTitle>
    </div>
    <div class="tabs-List">
      <pbiBlockTabs v-if="pageTabsShow" :tabsList="tabsList" :activeKey="activeKey" @tabClick="tabClick" ></pbiBlockTabs>
      <slot name="tabs"></slot>
    </div>
    
    <div class="content">
      <slot name="search"></slot>
      <slot name="content"></slot>
      <div v-if="pageTableShow" @mouseenter="tableFocus" @mouseleave="tableBlur">
        <a-spin :spinning="loading">
          <slot name="table"></slot>
          <pbiPagination v-if="paginationShow" style="margin-top: 8px;" :total="tableTotal" @change="paginationChange"
            @showSizeChange="paginationSizeChange"></pbiPagination>
        </a-spin>
      </div>
    </div>


  </div>
</template>
<script>
  export default {
    props: {
      pageLevel: {
        type: Number, 
        default: 2, // 1:一级页面，2：二级页面，3：三级页面  4:内部页面
      },
      pageTitle: {
        type: String,
        default: ''
      },
      pageTitleShow: {
        type: Boolean,
        default: true
      },

      // tabs
      pageTabsShow: {
        type: Boolean,
        default: true
      },
      tabsList: {
        type: Array,
        default: () => []
      },
      activeKey: {
        type: String,
        default: ''
      },

      pageTableShow: {
        type: Boolean,
        default: true
      },
      
      loading: {
        type: Boolean,
        default: true
      },
      tableTotal: {
        type: Number,
        default: 0
      },
      paginationShow: {
        type: Boolean,
        default: true
      }

    },
    data() {
      return {
        pageMargin:''
      }
    },
    created() {
      switch(this.pageLevel){
        case 2:
          this.pageMargin = '8px 12px'
          break;
        case 3:
          this.pageMargin = '32px 12px 8px'
          break;
        case 4:
          this.pageMargin = '0px 12px'
      }
    },
    methods: {
      tabClick(value){
        this.$emit('tabClick', value)
      },
      tableFocus() {
        this.$emit('tableFocus')
      },
      tableBlur() {
        this.$emit('tableBlur')
      },

      paginationChange(value) {
        this.$emit('paginationChange', value)
      },
      paginationSizeChange(value) {
        this.$emit('paginationSizeChange', value)
      },
    }

  }
</script>
<style lang="less" scoped>
  .page-container {
    overflow: hidden;
    /* 40:顶栏  16:外边距 */
    height: calc(100vh - 56px);
    margin: 8px 12px;
  }

  .page-container .tabs-List {
    margin-top: 8px;
  }

  .page-container .content {
    padding: 8px 12px;
    background-color: #fff;
    border-radius:0 4px 4px 4px ;
   
  }

  
</style>