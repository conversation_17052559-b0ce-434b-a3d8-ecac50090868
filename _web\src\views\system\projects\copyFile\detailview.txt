<template>
	<div class="spindiv" style="background:#fff">
		<a-tabs default-active-key="1" @change="handleTabChange">
			<!-- 产品项目信息 start -->
			<a-tab-pane key="1" tab="产品项目信息">
				<div class="head_title">
					<a-button
						size="small"
						type="primary"
						style="float:right"
						@click="$refs.editInfo.edit(projectdetail)"
						:loading="updateLoading"
					>
						编辑
					</a-button>
				</div>
				<a-form :form="form" layout="horizontal">
					<a-form-item label="产品分类" :labelCol="labelCol1" :wrapperCol="wrapperCol1">
						<a-select
							class="input-form"
							size="small"
							placeholder=""
							:options="productClassificationOption"
							v-decorator="['productClassification']"
							disabled
						>
						</a-select>
					</a-form-item>

					<a-form-item label="产品类别" :labelCol="labelCol1" :wrapperCol="wrapperCol1">
						<span
							class="ant-input ant-input-sm ant-input-disabled input-form"
							style="color: rgba(0, 0, 0, 0.66);background:#fff;cursor: initial;"
							>{{
								projectdetail.productCateOptionBeans
									? projectdetail.productCateOptionBeans.map(item => item.value).join(",")
									: ""
							}}</span
						>
					</a-form-item>

					<a-form-item label="产品型号" :labelCol="labelCol1" :wrapperCol="wrapperCol1">
						<a-input class="input-form" size="small" placeholder="" v-decorator="['productType']" disabled />
					</a-form-item>
					<a-form-item label="产品名称" :labelCol="labelCol1" :wrapperCol="wrapperCol1">
						<a-input class="input-form" size="small" placeholder="" v-decorator="['productName']" disabled />
					</a-form-item>
					<a-form-item label="项目名称" :labelCol="labelCol1" :wrapperCol="wrapperCol1">
						<a-input class="input-form" size="small" placeholder="" v-decorator="['projectName']" disabled />
					</a-form-item>
					<a-form-item label="所属部门" :labelCol="labelCol1" :wrapperCol="wrapperCol1">
						<a-tree-select
							class="input-form"
							size="small"
							labelInValue
							:autoExpandParent="true"
							:dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
							@change="departmentCateSelectTree"
							:tree-data="departmentCateTreeData"
							placeholder=""
							v-decorator="['department']"
							disabled
						>
						</a-tree-select>
					</a-form-item>
					<a-form-item label="项目等级" :labelCol="labelCol1" :wrapperCol="wrapperCol1">
						<a-select
							class="input-form"
							size="small"
							placeholder=""
							:options="productLevelOption"
							v-decorator="['productLevel']"
							disabled
						>
						</a-select>
					</a-form-item>
					<a-form-item label="客户" :labelCol="labelCol1" :wrapperCol="wrapperCol1">
						<a-input class="input-form" size="small" placeholder="" v-decorator="['customer']" disabled />
					</a-form-item>
					<a-form-item label="定点状态" :labelCol="labelCol1" :wrapperCol="wrapperCol1">
						<a-select
							class="input-form"
							size="small"
							placeholder=""
							:options="fixedStateOption"
							v-decorator="['fixedState']"
							disabled
						>
						</a-select>
					</a-form-item>
					<a-form-item label="计划定点日期" :labelCol="labelCol1" :wrapperCol="wrapperCol1" has-feedback>
						<a-date-picker class="input-form" size="small" placeholder="" v-decorator="['plannedFixedDate']" disabled />
					</a-form-item>
				</a-form>
			</a-tab-pane>
			<!-- 产品项目信息 end -->

			<!-- 产品项目角色信息 start -->
			<a-tab-pane key="2" tab="产品项目角色信息">
				<div class="head_title">
					<a-button size="small" type="primary" style="float:right" @click="submitProductMsg" :loading="updateLoading">
						保存
					</a-button>
				</div>
				<a-form :form="form2" class="form2">
					<!-- 院长、CTO -->
					<a-row :gutter="24">
						<a-col :md="12" :sm="12">
							<a-form-item label="院长" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									type="hidden"
									v-decorator="['president', { rules: [{ required: true, message: '请选择院长！' }] }]"
								/>
								<a-dropdown v-model="yzDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ yz ? yz : "选择院长" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="yzLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search v-model="yzQueryParam.searchValue" placeholder="搜索..." @change="onYzSearch" />
											<s-table
												style="width:100%;"
												ref="yzTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadYzData"
												:customRow="customYzRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
						<!-- xiaodong -->
						<a-col :md="12" :sm="12">
							<a-form-item label="CTO" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									type="hidden"
									v-decorator="['president', { rules: [{ required: true, message: '请选择CTO！' }] }]"
								/>
								<a-dropdown v-model="yzDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ yz ? yz : "选择CTO" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="yzLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search v-model="yzQueryParam.searchValue" placeholder="搜索..." @change="onYzSearch" />
											<s-table
												style="width:100%;"
												ref="yzTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadYzData"
												:customRow="customYzRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
					</a-row>

					<!-- 研究所所长、研究所项目管理员 -->
					<a-row :gutter="24">
						<a-col :md="12" :sm="12">
							<a-form-item label="研究所所长" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									type="hidden"
									v-decorator="['headOfTheInstitute', { rules: [{ required: true, message: '请选择研究所所长！' }] }]"
								/>
								<a-dropdown v-model="szDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ sz ? sz : "选择研究所所长" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="szLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search v-model="szQueryParam.searchValue" placeholder="搜索..." @change="onSzSearch" />
											<s-table
												style="width:100%;"
												ref="szTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadSzData"
												:customRow="customSzRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
						<a-col :md="12" :sm="12">
							<a-form-item label="研究所项目管理员" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									type="hidden"
									v-decorator="[
										'infoCommissioner',
										{ rules: [{ required: true, message: '请选择研究所项目管理员！' }] }
									]"
								/>
								<a-dropdown v-model="xxzyDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ xxzy ? xxzy : "选择研究所项目管理员" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="xxzyLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="xxzyQueryParam.searchValue"
												placeholder="搜索..."
												@change="onXxzySearch"
											/>
											<s-table
												style="width:100%;"
												ref="xxzyTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadXxzyData"
												:customRow="customXxzyRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
					</a-row>

					<!-- PD、RPM -->
					<a-row :gutter="24">
						<a-col :md="12" :sm="12">
							<a-form-item label="产品经理" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									type="hidden"
									v-decorator="['productManager', { rules: [{ required: true, message: '请选择产品经理！' }] }]"
								/>
								<a-dropdown v-model="cpjlDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ cpjl ? cpjl : "选择产品经理" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="cpjlLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="cpjlQueryParam.searchValue"
												placeholder="搜索..."
												@change="oncpjlSearch"
											/>
											<s-table
												style="width:100%;"
												ref="cpjlTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadcpjlData"
												:customRow="customcpjlRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
						<a-col :md="12" :sm="12">
							<a-form-item label="研发项目经理" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									type="hidden"
									v-decorator="['productRPM', { rules: [{ required: true, message: '请选择研发项目经理！' }] }]"
								/>
								<a-dropdown v-model="yfxmjlDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ yfxmjl ? yfxmjl : "选择研发项目经理" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="yfxmjlLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="yfxmjlQueryParam.searchValue"
												placeholder="搜索..."
												@change="onyfxmjlSearch"
											/>
											<s-table
												style="width:100%;"
												ref="yfxmjlTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadyfxmjlData"
												:customRow="customyfxmjlRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
					</a-row>

					<!-- DQE、技术总监 -->
					<a-row :gutter="24">
						<a-col :md="12" :sm="12">
							<a-form-item label="DQE" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									type="hidden"
									v-decorator="['productDQE', { rules: [{ required: true, message: '请选择DQE！' }] }]"
								/>
								<a-dropdown v-model="dqeDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ dqe ? dqe : "选择DQE" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="dqeLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search v-model="dqeQueryParam.searchValue" placeholder="搜索..." @change="ondqeSearch" />
											<s-table
												style="width:100%;"
												ref="dqeTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loaddqeData"
												:customRow="customdqeRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
						<a-col :md="12" :sm="12">
							<a-form-item label="产品总监" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									type="hidden"
									v-decorator="['productTechMajordomo', { rules: [{ required: true, message: '请选择产品总监！' }] }]"
								/>
								<a-dropdown v-model="cpzjDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ cpzj ? cpzj : "选择产品总监" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="cpzjLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="cpzjQueryParam.searchValue"
												placeholder="搜索..."
												@change="oncpzjSearch"
											/>
											<s-table
												style="width:100%;"
												ref="cpzjTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadcpzjData"
												:customRow="customcpzjRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
					</a-row>

					<!-- 工程中心负责人、结构中心负责人 -->
					<a-row :gutter="24">
						<a-col :md="12" :sm="12">
							<a-form-item label="工程中心经理" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									type="hidden"
									v-decorator="[
										'engineCenterManager',
										{ rules: [{ required: true, message: '请选择工程中心经理！' }] }
									]"
								/>
								<a-dropdown v-model="gczxjlDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ gczxjl ? gczxjl : "选择工程中心经理" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="gczxjlLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="gczxjlQueryParam.searchValue"
												placeholder="搜索..."
												@change="ongczxjlSearch"
											/>
											<s-table
												style="width:100%;"
												ref="gczxjlTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadgczxjlData"
												:customRow="customgczxjlRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
						<a-col :md="12" :sm="12">
							<a-form-item label="结构中心经理" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									type="hidden"
									v-decorator="[
										'structureCenterManager',
										{ rules: [{ required: true, message: '请选择结构中心经理！' }] }
									]"
								/>
								<a-dropdown v-model="jgzxjlDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ jgzxjl ? jgzxjl : "选择结构中心经理" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="jgzxjlLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="jgzxjlQueryParam.searchValue"
												placeholder="搜索..."
												@change="onjgzxjlSearch"
											/>
											<s-table
												style="width:100%;"
												ref="jgzxjlTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadjgzxjlData"
												:customRow="customjgzxjlRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
					</a-row>

					<!-- 测试经理、共性平台负责人 -->
					<a-row :gutter="24">
						<a-col :md="12" :sm="12">
							<a-form-item label="测试经理" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									type="hidden"
									v-decorator="['testManager', { rules: [{ required: true, message: '请选择测试经理！' }] }]"
								/>
								<a-dropdown v-model="csjlDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ csjl ? csjl : "选择测试经理" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="csjlLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="csjlQueryParam.searchValue"
												placeholder="搜索..."
												@change="oncsjlSearch"
											/>
											<s-table
												style="width:100%;"
												ref="csjlTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadcsjlData"
												:customRow="customcsjlRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
						<a-col :md="12" :sm="12">
							<a-form-item label="共性中心经理" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									type="hidden"
									v-decorator="[
										'commonCenterManager',
										{ rules: [{ required: true, message: '请选择共性中心经理！' }] }
									]"
								/>
								<a-dropdown v-model="gxzxjlDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ gxzxjl ? gxzxjl : "选择共性中心经理" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="gxzxjlLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="gxzxjlQueryParam.searchValue"
												placeholder="搜索..."
												@change="ongxzxjlSearch"
											/>
											<s-table
												style="width:100%;"
												ref="gxzxjlTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadgxzxjlData"
												:customRow="customgxzxjlRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
					</a-row>

					<!-- 结构工程师、测试代表 -->
					<a-row :gutter="24">
						<a-col :md="12" :sm="12">
							<a-form-item label="结构代表" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									type="hidden"
									v-decorator="[
										'structureTechnologyRepresent',
										{ rules: [{ required: true, message: '请选择结构代表！' }] }
									]"
								/>
								<a-dropdown v-model="jgdbDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ jgdb ? jgdb : "选择结构代表" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="jgdbLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="jgdbQueryParam.searchValue"
												placeholder="搜索..."
												@change="onjgdbSearch"
											/>
											<s-table
												style="width:100%;"
												ref="jgdbTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadjgdbData"
												:customRow="customjgdbRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
						<a-col :md="12" :sm="12">
							<a-form-item label="测试代表" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									type="hidden"
									v-decorator="[
										'testTechnologyRepresent',
										{ rules: [{ required: true, message: '请选择测试代表！' }] }
									]"
								/>
								<a-dropdown v-model="csdbDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ csdb ? csdb : "选择测试代表" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="csdbLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="csdbQueryParam.searchValue"
												placeholder="搜索..."
												@change="oncsdbSearch"
											/>
											<s-table
												style="width:100%;"
												ref="csdbTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadcsdbData"
												:customRow="customcsdbRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
					</a-row>

					<!-- 产品工程师、-->
					<a-row :gutter="24">
						<a-col :md="24" :sm="24">
							<a-form-item label="产品工程师" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									type="hidden"
									v-decorator="['productEnginMulti', { rules: [{ required: true, message: '请选择产品工程师！' }] }]"
								/>
								<a-dropdown v-model="cpgcsDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ cpgcs ? cpgcs : "选择产品工程师" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="cpgcsLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="cpgcsQueryParam.searchValue"
												placeholder="搜索..."
												@change="oncpgcsSearch"
											/>
											<s-table
												style="width:100%;"
												ref="cpgcstable"
												:rowKey="record => record.id"
												:rowSelection="cpgcsrowSelection"
												:columns="vColumns"
												:data="loadcpgcsData"
												:customRow="customcpgcsRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
					</a-row>

					<!-- 1111111111111 -->

					<a-row :gutter="24">
						<a-col :md="12" :sm="12">
							<a-form-item label="院长" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									class="input-form"
									type="hidden"
									v-decorator="['president', { rules: [{ required: true, message: '请选择院长！' }] }]"
								/>
								<a-dropdown v-model="yzDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ yz ? yz : "选择院长" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="yzLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search v-model="yzQueryParam.searchValue" placeholder="搜索..." @change="onYzSearch" />
											<s-table
												style="width:100%;"
												ref="yzTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadYzData"
												:customRow="customYzRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
						<a-col :md="12" :sm="12">
							<a-form-item label="所长" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									class="input-form"
									type="hidden"
									v-decorator="['headOfTheInstitute', { rules: [{ required: true, message: '请选择所长！' }] }]"
								/>
								<a-dropdown v-model="szDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ sz ? sz : "选择所长" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="szLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search v-model="szQueryParam.searchValue" placeholder="搜索..." @change="onSzSearch" />
											<s-table
												style="width:100%;"
												ref="szTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadSzData"
												:customRow="customSzRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
					</a-row>

					<a-row :gutter="24">
						<a-col :md="12" :sm="12">
							<a-form-item label="产品中心经理" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									class="input-form"
									type="hidden"
									v-decorator="[
										'productCenterManager',
										{ rules: [{ required: true, message: '请选择产品中心经理！' }] }
									]"
								/>
								<a-dropdown v-model="cpzxjlDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ cpzxjl ? cpzxjl : "选择产品中心经理" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="cpzxjlLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="cpzxjlQueryParam.searchValue"
												placeholder="搜索..."
												@change="oncpzxjlSearch"
											/>
											<s-table
												style="width:100%;"
												ref="cpzxjlTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadcpzxjlData"
												:customRow="customcpzxjlRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
						<a-col :md="12" :sm="12">
							<a-form-item label="产品总监" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									class="input-form"
									type="hidden"
									v-decorator="['productTechMajordomo', { rules: [{ required: true, message: '请选择产品总监！' }] }]"
								/>
								<a-dropdown v-model="cpzjDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ cpzj ? cpzj : "选择产品总监" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="cpzjLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="cpzjQueryParam.searchValue"
												placeholder="搜索..."
												@change="oncpzjSearch"
											/>
											<s-table
												style="width:100%;"
												ref="cpzjTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadcpzjData"
												:customRow="customcpzjRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :md="12" :sm="12">
							<a-form-item label="产品经理" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									class="input-form"
									type="hidden"
									v-decorator="['productManager', { rules: [{ required: true, message: '请选择产品经理！' }] }]"
								/>
								<a-dropdown v-model="cpjlDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ cpjl ? cpjl : "选择产品经理" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="cpjlLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="cpjlQueryParam.searchValue"
												placeholder="搜索..."
												@change="oncpjlSearch"
											/>
											<s-table
												style="width:100%;"
												ref="cpjlTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadcpjlData"
												:customRow="customcpjlRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
						<a-col :md="12" :sm="12">
							<a-form-item label="产品工程师" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									class="input-form"
									type="hidden"
									v-decorator="['productEnginMulti', { rules: [{ required: true, message: '请选择产品工程师！' }] }]"
								/>
								<a-dropdown v-model="cpgcsDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ cpgcs ? cpgcs : "选择产品工程师" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="cpgcsLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="cpgcsQueryParam.searchValue"
												placeholder="搜索..."
												@change="oncpgcsSearch"
											/>
											<s-table
												style="width:100%;"
												ref="cpgcstable"
												:rowKey="record => record.id"
												:rowSelection="cpgcsrowSelection"
												:columns="vColumns"
												:data="loadcpgcsData"
												:customRow="customcpgcsRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :md="12" :sm="12">
							<a-form-item label="项目总监" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									class="input-form"
									type="hidden"
									v-decorator="['projectMajordomo', { rules: [{ required: true, message: '请选择项目总监！' }] }]"
								/>
								<a-dropdown v-model="xmzjDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ xmzj ? xmzj : "选择项目总监" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="xmzjLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="xmzjQueryParam.searchValue"
												placeholder="搜索..."
												@change="onxmzjSearch"
											/>
											<s-table
												style="width:100%;"
												ref="xmzjTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadxmzjData"
												:customRow="customxmzjRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
						<a-col :md="12" :sm="12">
							<a-form-item label="研发项目经理" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									class="input-form"
									type="hidden"
									v-decorator="['productRPM', { rules: [{ required: true, message: '请选择研发项目经理！' }] }]"
								/>
								<a-dropdown v-model="yfxmjlDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ yfxmjl ? yfxmjl : "选择研发项目经理" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="yfxmjlLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="yfxmjlQueryParam.searchValue"
												placeholder="搜索..."
												@change="onyfxmjlSearch"
											/>
											<s-table
												style="width:100%;"
												ref="yfxmjlTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadyfxmjlData"
												:customRow="customyfxmjlRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :md="12" :sm="12">
							<a-form-item label="工程中心经理" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									class="input-form"
									type="hidden"
									v-decorator="[
										'engineCenterManager',
										{ rules: [{ required: true, message: '请选择工程中心经理！' }] }
									]"
								/>
								<a-dropdown v-model="gczxjlDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ gczxjl ? gczxjl : "选择工程中心经理" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="gczxjlLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="gczxjlQueryParam.searchValue"
												placeholder="搜索..."
												@change="ongczxjlSearch"
											/>
											<s-table
												style="width:100%;"
												ref="gczxjlTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadgczxjlData"
												:customRow="customgczxjlRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
						<a-col :md="12" :sm="12">
							<a-form-item label="工程代表" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									class="input-form"
									type="hidden"
									v-decorator="[
										'engineCenterRepresentMulti',
										{ rules: [{ required: true, message: '请选择工程代表！' }] }
									]"
								/>
								<a-dropdown v-model="gcdbDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ gcdb ? gcdb : "选择工程代表" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="gcdbLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="gcdbQueryParam.searchValue"
												placeholder="搜索..."
												@change="ongcdbSearch"
											/>
											<s-table
												style="width:100%;"
												ref="gcdbTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadgcdbData"
												:rowSelection="gcdbrowSelection"
												:customRow="customgcdbRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :md="12" :sm="12">
							<a-form-item label="结构中心经理" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									class="input-form"
									type="hidden"
									v-decorator="[
										'structureCenterManager',
										{ rules: [{ required: true, message: '请选择结构中心经理！' }] }
									]"
								/>
								<a-dropdown v-model="jgzxjlDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ jgzxjl ? jgzxjl : "选择结构中心经理" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="jgzxjlLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="jgzxjlQueryParam.searchValue"
												placeholder="搜索..."
												@change="onjgzxjlSearch"
											/>
											<s-table
												style="width:100%;"
												ref="jgzxjlTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadjgzxjlData"
												:customRow="customjgzxjlRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
						<a-col :md="12" :sm="12">
							<a-form-item label="结构代表" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									class="input-form"
									type="hidden"
									v-decorator="[
										'structureTechnologyRepresent',
										{ rules: [{ required: true, message: '请选择结构代表！' }] }
									]"
								/>
								<a-dropdown v-model="jgdbDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ jgdb ? jgdb : "选择结构代表" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="jgdbLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="jgdbQueryParam.searchValue"
												placeholder="搜索..."
												@change="onjgdbSearch"
											/>
											<s-table
												style="width:100%;"
												ref="jgdbTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadjgdbData"
												:customRow="customjgdbRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :md="12" :sm="12">
							<a-form-item label="共性中心经理" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									class="input-form"
									type="hidden"
									v-decorator="[
										'commonCenterManager',
										{ rules: [{ required: true, message: '请选择共性中心经理！' }] }
									]"
								/>
								<a-dropdown v-model="gxzxjlDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ gxzxjl ? gxzxjl : "选择共性中心经理" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="gxzxjlLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="gxzxjlQueryParam.searchValue"
												placeholder="搜索..."
												@change="ongxzxjlSearch"
											/>
											<s-table
												style="width:100%;"
												ref="gxzxjlTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadgxzxjlData"
												:customRow="customgxzxjlRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
						<a-col :md="12" :sm="12">
							<a-form-item label="共性代表" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									class="input-form"
									type="hidden"
									v-decorator="['commonCenterRepresent', { rules: [{ required: true, message: '请选择共性代表！' }] }]"
								/>
								<a-dropdown v-model="gxdbDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ gxdb ? gxdb : "选择共性代表" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="gxdbLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="gxdbQueryParam.searchValue"
												placeholder="搜索..."
												@change="ongxdbSearch"
											/>
											<s-table
												style="width:100%;"
												ref="gxdbTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadgxdbData"
												:customRow="customgxdbRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :md="12" :sm="12">
							<a-form-item label="办公室经理" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									class="input-form"
									type="hidden"
									v-decorator="['officeManager', { rules: [{ required: true, message: '请选择办公室经理！' }] }]"
								/>
								<a-dropdown v-model="bgsjlDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ bgsjl ? bgsjl : "选择办公室经理" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="bgsjlLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="bgsjlQueryParam.searchValue"
												placeholder="搜索..."
												@change="onbgsjlSearch"
											/>
											<s-table
												style="width:100%;"
												ref="bgsjlTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadbgsjlData"
												:customRow="custombgsjlRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
						<a-col :md="12" :sm="12">
							<a-form-item label="DQE" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									class="input-form"
									type="hidden"
									v-decorator="['productDQE', { rules: [{ required: true, message: '请选择DQE！' }] }]"
								/>
								<a-dropdown v-model="dqeDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ dqe ? dqe : "选择DQE" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="dqeLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search v-model="dqeQueryParam.searchValue" placeholder="搜索..." @change="ondqeSearch" />
											<s-table
												style="width:100%;"
												ref="dqeTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loaddqeData"
												:customRow="customdqeRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :md="12" :sm="12">
							<a-form-item label="测试经理" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									class="input-form"
									type="hidden"
									v-decorator="['testManager', { rules: [{ required: true, message: '请选择测试经理！' }] }]"
								/>
								<a-dropdown v-model="csjlDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ csjl ? csjl : "选择测试经理" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="csjlLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="csjlQueryParam.searchValue"
												placeholder="搜索..."
												@change="oncsjlSearch"
											/>
											<s-table
												style="width:100%;"
												ref="csjlTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadcsjlData"
												:customRow="customcsjlRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
						<a-col :md="12" :sm="12">
							<a-form-item label="测试代表" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									class="input-form"
									type="hidden"
									v-decorator="[
										'testTechnologyRepresent',
										{ rules: [{ required: true, message: '请选择测试代表！' }] }
									]"
								/>
								<a-dropdown v-model="csdbDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ csdb ? csdb : "选择测试代表" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="csdbLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="csdbQueryParam.searchValue"
												placeholder="搜索..."
												@change="oncsdbSearch"
											/>
											<s-table
												style="width:100%;"
												ref="csdbTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadcsdbData"
												:customRow="customcsdbRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :md="12" :sm="12">
							<a-form-item label="信息专员" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
								<a-input
									class="input-form"
									type="hidden"
									v-decorator="['infoCommissioner', { rules: [{ required: true, message: '请选择信息专员！' }] }]"
								/>
								<a-dropdown v-model="xxzyDownVisible" placement="bottomCenter" :trigger="['click']">
									<a-button
										style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
										>{{ xxzy ? xxzy : "选择信息专员" }} <a-icon type="down"
									/></a-button>
									<a-menu slot="overlay">
										<a-spin :spinning="xxzyLoading" style="padding:10px 24px 0 24px;width:100%">
											<a-input-search
												v-model="xxzyQueryParam.searchValue"
												placeholder="搜索..."
												@change="onXxzySearch"
											/>
											<s-table
												style="width:100%;"
												ref="xxzyTable"
												:rowKey="record => record.id"
												:columns="vColumns"
												:data="loadXxzyData"
												:customRow="customXxzyRow"
												:scroll="{ y: 120, x: 120 }"
											>
											</s-table>
										</a-spin>
									</a-menu>
								</a-dropdown>
							</a-form-item>
						</a-col>
						<a-col :md="12" :sm="12"></a-col>
					</a-row>
				</a-form>
			</a-tab-pane>
			<!-- 产品项目角色信息 end -->
		</a-tabs>
		<edit-info @issueId="issueId" ref="editInfo" @ok="callProjectDetail" />
	</div>
</template>

<script>
import Vue from "vue"
import editInfo from "./create/editInfo"
import { DICT_TYPE_TREE_DATA } from "@/store/mutation-types"
import { getCateTree } from "@/api/modular/system/topic"
import { productMsgToUpdate, getProjectDetail } from "@/api/modular/system/report"
import Treeselect from "@riophae/vue-treeselect"
import { getUserLists } from "@/api/modular/system/userManage"
import moment from "moment"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"
import { STable } from "@/components"
export default {
	components: {
		Treeselect,
		editInfo,
		STable
	},
	props: {
		issueId: {
			type: Number,
			default: 0
		},
		loading: {
			type: Boolean,
			default: false
		},
		projectdetail: {
			type: Object,
			default: {}
		}
	},
	data() {
		return {
			labelCol: {
				xs: {
					span: 8
				},
				sm: {
					span: 7
				}
			},
			wrapperCol: {
				xs: {
					span: 24
				},
				sm: {
					span: 13
				}
			},
			form: this.$form.createForm(this),
			form2: this.$form.createForm(this),
			labelCol1: { span: 4 },
			wrapperCol1: { span: 6 },
			labelCol2: { span: 8 },
			btnWrapperCol: { span: 6, offset: 4 },
			normalizer(node) {
				return {
					id: node.value,
					label: node.title,
					children: node.children && node.children.length > 0 ? node.children : 0
				}
			},
			productCateTreeData: [],
			departmentCateTreeData: [],
			fixedStateOption: [],
			productLevelOption: [],

			productClassificationOption: [
				{ label: "预研产品", value: "1" },
				{ label: "新产品", value: "2" },
				{ label: "其他产品", value: "3" },
				{ label: "量产", value: "4" }
			],

			confirmLoading: false,
			updateLoading: false,

			/* 院长 */
			loadYzData: parameter => {
				return getUserLists(Object.assign(parameter, this.yzQueryParam)).then(res => {
					return res.data
				})
			},
			yz: null,
			yzLoading: false,
			yzQueryParam: {}, //院长
			yzDownVisible: false, //院长

			/* 所长 */
			loadSzData: parameter => {
				return getUserLists(Object.assign(parameter, this.szQueryParam)).then(res => {
					return res.data
				})
			},

			sz: null,
			szLoading: false,
			szQueryParam: {}, //所长
			szDownVisible: false, //所长

			/* 信息专员 */
			loadXxzyData: parameter => {
				return getUserLists(Object.assign(parameter, this.xxzyQueryParam)).then(res => {
					return res.data
				})
			},

			xxzy: null, //信息专员
			xxzyLoading: false,
			xxzyQueryParam: {},
			xxzyDownVisible: false,

			/* 产品中心经理 */
			loadcpzxjlData: parameter => {
				return getUserLists(Object.assign(parameter, this.cpzxjlQueryParam)).then(res => {
					return res.data
				})
			},

			cpzxjl: null, //产品中心经理
			cpzxjlLoading: false,
			cpzxjlQueryParam: {},
			cpzxjlDownVisible: false,

			/* 项目总监 */
			loadxmzjData: parameter => {
				return getUserLists(Object.assign(parameter, this.xmzjQueryParam)).then(res => {
					return res.data
				})
			},

			xmzj: null, //项目总监
			xmzjLoading: false,
			xmzjQueryParam: {},
			xmzjDownVisible: false,

			/* 工程中心经理 */
			loadgczxjlData: parameter => {
				return getUserLists(Object.assign(parameter, this.gczxjlQueryParam)).then(res => {
					return res.data
				})
			},

			gczxjl: null, //工程中心经理
			gczxjlLoading: false,
			gczxjlQueryParam: {},
			gczxjlDownVisible: false,

			/* 结构中心经理 */
			loadjgzxjlData: parameter => {
				return getUserLists(Object.assign(parameter, this.jgzxjlQueryParam)).then(res => {
					return res.data
				})
			},

			jgzxjl: null, //结构中心经理
			jgzxjlLoading: false,
			jgzxjlQueryParam: {},
			jgzxjlDownVisible: false,

			/* 共性中心经理 */
			loadgxzxjlData: parameter => {
				return getUserLists(Object.assign(parameter, this.gxzxjlQueryParam)).then(res => {
					return res.data
				})
			},

			gxzxjl: null, //共性中心经理
			gxzxjlLoading: false,
			gxzxjlQueryParam: {},
			gxzxjlDownVisible: false,

			/* 办公室经理 */
			loadbgsjlData: parameter => {
				return getUserLists(Object.assign(parameter, this.bgsjlQueryParam)).then(res => {
					return res.data
				})
			},

			bgsjl: null, //办公室经理
			bgsjlLoading: false,
			bgsjlQueryParam: {},
			bgsjlDownVisible: false,

			/* 测试经理 */
			loadcsjlData: parameter => {
				return getUserLists(Object.assign(parameter, this.csjlQueryParam)).then(res => {
					return res.data
				})
			},

			csjl: null, //测试经理
			csjlLoading: false,
			csjlQueryParam: {},
			csjlDownVisible: false,

			/* 产品总监 */
			loadcpzjData: parameter => {
				return getUserLists(Object.assign(parameter, this.cpzjQueryParam)).then(res => {
					return res.data
				})
			},

			cpzj: null, //产品总监
			cpzjLoading: false,
			cpzjQueryParam: {},
			cpzjDownVisible: false,

			/* 产品经理 */
			loadcpjlData: parameter => {
				return getUserLists(Object.assign(parameter, this.cpjlQueryParam)).then(res => {
					return res.data
				})
			},

			cpjl: null, //产品经理
			cpjlLoading: false,
			cpjlQueryParam: {},
			cpjlDownVisible: false,

			/* 研发项目经理 */
			loadyfxmjlData: parameter => {
				return getUserLists(Object.assign(parameter, this.yfxmjlQueryParam)).then(res => {
					return res.data
				})
			},

			yfxmjl: null, //研发项目经理
			yfxmjlLoading: false,
			yfxmjlQueryParam: {},
			yfxmjlDownVisible: false,

			/* 工程代表 */
			loadgcdbData: parameter => {
				return getUserLists(Object.assign(parameter, this.gcdbQueryParam)).then(res => {
					return res.data
				})
			},

			gcdb: null, //工程代表
			gcdbLoading: false,
			gcdbQueryParam: {},
			gcdbDownVisible: false,

			/* 结构代表 */
			loadjgdbData: parameter => {
				return getUserLists(Object.assign(parameter, this.jgdbQueryParam)).then(res => {
					return res.data
				})
			},

			jgdb: null, //结构代表
			jgdbLoading: false,
			jgdbQueryParam: {},
			jgdbDownVisible: false,

			/* 共性代表 */
			loadgxdbData: parameter => {
				return getUserLists(Object.assign(parameter, this.gxdbQueryParam)).then(res => {
					return res.data
				})
			},

			gxdb: null, //共性代表
			gxdbLoading: false,
			gxdbQueryParam: {},
			gxdbDownVisible: false,

			loaddqeData: parameter => {
				return getUserLists(Object.assign(parameter, this.dqeQueryParam)).then(res => {
					return res.data
				})
			},

			dqe: null, //DQE
			dqeLoading: false,
			dqeQueryParam: {},
			dqeDownVisible: false,

			loadcsdbData: parameter => {
				return getUserLists(Object.assign(parameter, this.csdbQueryParam)).then(res => {
					return res.data
				})
			},

			csdb: null, //测试代表
			csdbLoading: false,
			csdbQueryParam: {},
			csdbDownVisible: false,

			loadcpgcsData: parameter => {
				return getUserLists(Object.assign(parameter, this.cpgcsQueryParam)).then(res => {
					return res.data
				})
			},

			cpgcs: null, //测试代表
			cpgcsLoading: false,
			cpgcsQueryParam: {},
			cpgcsDownVisible: false,
			cpgcsselectedRowKeys: [],
			cpgcsselectedRow: [],
			gcdbselectedRowKeys: [],
			gcdbselectedRow: [],

			vColumns: [
				{
					title: "账号",
					dataIndex: "account"
				},
				{
					title: "姓名",
					dataIndex: "name"
				}
			]
		}
	},
	methods: {
		initForm2() {
			this.$nextTick(() => {
				this.form2.setFieldsValue({
					president: this.projectdetail.president,
					headOfTheInstitute: this.projectdetail.headOfTheInstitute,
					infoCommissioner: this.projectdetail.infoCommissioner,
					productCenterManager: this.projectdetail.productCenterManager,
					productTechMajordomo: this.projectdetail.productTechMajordomo,
					productManager: this.projectdetail.productManager,
					productEnginMulti: this.projectdetail.productEnginMulti ? this.projectdetail.productEnginMulti.join(",") : "",
					projectMajordomo: this.projectdetail.projectMajordomo,
					productRPM: this.projectdetail.productRPM,
					engineCenterManager: this.projectdetail.engineCenterManager,
					engineCenterRepresentMulti: this.projectdetail.engineCenterRepresentMulti
						? this.projectdetail.engineCenterRepresentMulti.join(",")
						: "",
					structureCenterManager: this.projectdetail.structureCenterManager,
					structureTechnologyRepresent: this.projectdetail.structureTechnologyRepresent,
					commonCenterManager: this.projectdetail.commonCenterManager,
					commonCenterRepresent: this.projectdetail.commonCenterRepresent,
					officeManager: this.projectdetail.officeManager,
					productDQE: this.projectdetail.productDQE,
					testManager: this.projectdetail.testManager,
					testTechnologyRepresent: this.projectdetail.testTechnologyRepresent
				})
				this.yz = this.projectdetail.presidentName
				this.sz = this.projectdetail.headOfTheInstituteName
				this.xxzy = this.projectdetail.infoCommissionerName
				this.cpzxjl = this.projectdetail.productCenterManagerName
				this.cpzj = this.projectdetail.productTechMajordomoName
				this.cpjl = this.projectdetail.productManagerName
				this.cpgcs = this.projectdetail.productEnginMultiName ? this.projectdetail.productEnginMultiName.join(",") : ""
				this.xmzj = this.projectdetail.projectMajordomoName
				this.yfxmjl = this.projectdetail.productRPMName
				this.gczxjl = this.projectdetail.engineCenterManagerName
				this.gcdb = this.projectdetail.engineCenterRepresentMultiName
					? this.projectdetail.engineCenterRepresentMultiName.join(",")
					: ""
				this.jgzxjl = this.projectdetail.structureCenterManagerName
				this.jgdb = this.projectdetail.structureTechnologyRepresentName
				this.gxzxjl = this.projectdetail.commonCenterManagerName
				this.gxdb = this.projectdetail.commonCenterRepresentName
				this.bgsjl = this.projectdetail.officeManagerName
				this.dqe = this.projectdetail.productDQEName
				this.csjl = this.projectdetail.testManagerName
				this.csdb = this.projectdetail.testTechnologyRepresentName
			})
		},
		handleTabChange(activeKey) {
			if (activeKey == "2") {
				this.initForm2()
			}
		},
		onYzSearch(e) {
			this.$refs.yzTable.refresh()
		},
		customYzRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							president: row.account
						})
						this.yz = row.name
						this.yzDownVisible = false
					}
				}
			}
		},
		onSzSearch(e) {
			this.$refs.szTable.refresh()
		},
		customSzRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							headOfTheInstitute: row.account
						})
						this.sz = row.name
						this.szDownVisible = false
					}
				}
			}
		},
		onXxzySearch(e) {
			this.$refs.xxzyTable.refresh()
		},
		customXxzyRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							infoCommissioner: row.account
						})
						this.xxzy = row.name
						this.xxzyDownVisible = false
					}
				}
			}
		},
		oncpzxjlSearch(e) {
			this.$refs.cpzxjlTable.refresh()
		},
		customcpzxjlRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							productCenterManager: row.account
						})
						this.cpzxjl = row.name
						this.cpzxjlDownVisible = false
					}
				}
			}
		},
		onxmzjSearch(e) {
			this.$refs.xmzjTable.refresh()
		},
		customxmzjRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							projectMajordomo: row.account
						})
						this.xmzj = row.name
						this.xmzjDownVisible = false
					}
				}
			}
		},
		ongczxjlSearch(e) {
			this.$refs.gczxjlTable.refresh()
		},
		customgczxjlRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							engineCenterManager: row.account
						})
						this.gczxjl = row.name
						this.gczxjlDownVisible = false
					}
				}
			}
		},
		onjgzxjlSearch(e) {
			this.$refs.jgzxjlTable.refresh()
		},
		customjgzxjlRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							structureCenterManager: row.account
						})
						this.jgzxjl = row.name
						this.jgzxjlDownVisible = false
					}
				}
			}
		},
		ongxzxjlSearch(e) {
			this.$refs.gxzxjlTable.refresh()
		},
		customgxzxjlRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							commonCenterManager: row.account
						})
						this.gxzxjl = row.name
						this.gxzxjlDownVisible = false
					}
				}
			}
		},
		onbgsjlSearch(e) {
			this.$refs.bgsjlTable.refresh()
		},
		custombgsjlRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							officeManager: row.account
						})
						this.bgsjl = row.name
						this.bgsjlDownVisible = false
					}
				}
			}
		},
		oncsjlSearch(e) {
			this.$refs.csjlTable.refresh()
		},
		customcsjlRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							testManager: row.account
						})
						this.csjl = row.name
						this.csjlDownVisible = false
					}
				}
			}
		},
		oncpzjSearch(e) {
			this.$refs.cpzjTable.refresh()
		},
		customcpzjRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							productTechMajordomo: row.account
						})
						this.cpzj = row.name
						this.cpzjDownVisible = false
					}
				}
			}
		},
		oncpjlSearch(e) {
			this.$refs.cpjlTable.refresh()
		},
		customcpjlRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							productManager: row.account
						})
						this.cpjl = row.name
						this.cpjlDownVisible = false
					}
				}
			}
		},
		onyfxmjlSearch(e) {
			this.$refs.yfxmjlTable.refresh()
		},
		customyfxmjlRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							productRPM: row.account
						})
						this.yfxmjl = row.name
						this.yfxmjlDownVisible = false
					}
				}
			}
		},
		ongcdbSearch(e) {
			this.$refs.gcdbTable.refresh()
		},
		customgcdbRow(row, index) {
			return {
				on: {
					click: () => {
						if (this.gcdbselectedRow.includes(row)) {
							this.gcdbselectedRow = this.gcdbselectedRow.filter(r => r != row)
							this.gcdbselectedRowKeys = this.gcdbselectedRow.filter(r => r != row.id)
						} else {
							this.gcdbselectedRow.push(row)
							this.gcdbselectedRowKeys.push(row.id)
						}

						let ids = ""
						this.gcdb = ""
						for (let i = 0; i < this.gcdbselectedRow.length; i++) {
							if (i == 0) {
								this.gcdb = this.gcdbselectedRow[i].name
								ids += this.gcdbselectedRow[i].account
							} else {
								this.gcdb += "," + this.gcdbselectedRow[i].name
								ids += "," + this.gcdbselectedRow[i].account
							}
						}

						this.form2.setFieldsValue({
							engineCenterRepresentMulti: ids
						})
					}
				}
			}
		},
		onjgdbSearch(e) {
			this.$refs.jgdbTable.refresh()
		},
		customjgdbRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							structureTechnologyRepresent: row.account
						})
						this.jgdb = row.name
						this.jgdbDownVisible = false
					}
				}
			}
		},
		ongxdbSearch(e) {
			this.$refs.gxdbTable.refresh()
		},
		customgxdbRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							commonCenterRepresent: row.account
						})
						this.gxdb = row.name
						this.gxdbDownVisible = false
					}
				}
			}
		},
		ondqeSearch(e) {
			this.$refs.dqeTable.refresh()
		},
		customdqeRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							productDQE: row.account
						})
						this.dqe = row.name
						this.dqeDownVisible = false
					}
				}
			}
		},
		oncsdbSearch(e) {
			this.$refs.csdbTable.refresh()
		},
		customcsdbRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							testTechnologyRepresent: row.account
						})
						this.csdb = row.name
						this.csdbDownVisible = false
					}
				}
			}
		},
		oncpgcsSearch(e) {
			this.$refs.cpgcstable.refresh()
		},
		customcpgcsRow(row, index) {
			return {
				on: {
					click: () => {
						if (this.cpgcsselectedRow.includes(row)) {
							this.cpgcsselectedRow = this.cpgcsselectedRow.filter(r => r != row)
							this.cpgcsselectedRowKeys = this.cpgcsselectedRow.filter(r => r != row.id)
						} else {
							this.cpgcsselectedRow.push(row)
							this.cpgcsselectedRowKeys.push(row.id)
						}

						let ids = ""
						this.cpgcs = ""
						for (let i = 0; i < this.cpgcsselectedRow.length; i++) {
							if (i == 0) {
								this.cpgcs = this.cpgcsselectedRow[i].name
								ids += this.cpgcsselectedRow[i].account
							} else {
								this.cpgcs += "," + this.cpgcsselectedRow[i].name
								ids += "," + this.cpgcsselectedRow[i].account
							}
						}

						this.form2.setFieldsValue({
							productEnginMulti: ids
						})
					}
				}
			}
		},
		callProjectDetail() {
			this.updateLoading = true
			let params = { issueId: this.issueId, title: "" }
			getProjectDetail(params)
				.then(res => {
					if (res.result) {
						this.projectdetail = res.data
						setTimeout(() => {
							this.form.setFieldsValue({
								productName: this.projectdetail.productProjectName,
								productLevel: this.projectdetail.level <= 0 ? null : this.projectdetail.level + "",
								fixedState: this.projectdetail.fixedState <= 0 ? null : this.projectdetail.fixedState + "",
								productType: this.projectdetail.productType,
								projectName: this.projectdetail.projectName,
								customer: this.projectdetail.customer,
								productClassification: this.projectdetail.productClassification,
								department: {
									label: this.getSelectedItem(this.projectdetail.department, this.departmentCateTreeData),
									value: this.projectdetail.department
								},
								// plannedFixedDate: this.projectdetail.plannedFixedDate != "-" ? moment(this.projectdetail.plannedFixedDate, 'YYYY-MM-DD') : moment(),
								productCate: this.projectdetail.productCateOptionBeans
									? this.projectdetail.productCateOptionBeans.map(item => item.id)
									: null
							})
							if (this.projectdetail.plannedFixedDate != "-") {
								this.form.setFieldsValue({
									plannedFixedDate: moment(this.projectdetail.plannedFixedDate, "YYYY-MM-DD")
								})
							}
						}, 600)
					} else {
						this.$message.error(res.message, 1)
					}
					this.updateLoading = false
				})
				.catch(err => {
					this.updateLoading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		},
		submitProductMsg() {
			this.form2.validateFields((err, values) => {
				this.updateLoading = true
				if (!err) {
					values.issueId = this.issueId
					productMsgToUpdate(values)
						.then(res => {
							if (res.result) {
								this.$message.success("产品角色信息更新完成", 1)
								this.$emit("ok")
								this.initForm2()
							} else {
								this.$message.error("错误提示：" + res.message, 1)
							}
						})
						.catch(res => {
							this.$message.error("错误提示：" + res.message, 1)
						})
						.finally(() => {
							this.updateLoading = false
						})
				} else {
					setTimeout(() => {
						this.$message.warn("请将填写信息完整", 1)
						this.updateLoading = false
					}, 300)
				}
			})
		},
		callGetProductCateTree() {
			this.confirmLoading = true
			getCateTree({
				fieldName: "productCate"
			})
				.then(res => {
					if (res.success) {
						this.productCateTreeData = res.data
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
					this.confirmLoading = false
				})
				.catch(err => {
					this.confirmLoading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		},
		callGetDepartmentCateTree() {
			this.confirmLoading = true
			getCateTree({
				fieldName: "department"
			})
				.then(res => {
					if (res.success) {
						this.departmentCateTreeData = res.data
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
					this.confirmLoading = false
				})
				.catch(err => {
					this.confirmLoading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		},
		callGetSelectOption() {
			this.fixedStateOption = this.getDict("fix_status").map((item, index) => {
				return Object.assign({}, { value: item.code, label: item.name })
			})
			this.productLevelOption = this.getDict("product_level_status").map((item, index) => {
				return Object.assign({}, { value: item.code, label: item.name })
			})
		},
		//获取字典选项
		getDict(code) {
			const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
			return dictTypeTree.filter(item => item.code == code)[0].children
		},
		departmentCateSelectTree(value, label, extra) {
			// console.log(value);
			this.form.setFieldsValue({
				productCate: value
			})
		},
		/**
		 * @description 当前选中对象的title(拼接所有父级title)
		 */
		getSelectedItem(value, data, title) {
			let $title = ""
			for (const item of data) {
				//在根节点找到对应选项
				if (!title && item.value == value) {
					$title = item.title
				} else if (!title && item.children) {
					//根节点未找到继续递归查找
					$title = item.title
					//this.getSelectedItem(value, item.children, $title);
					for (const e of item.children) {
						if (e.value == value) {
							$title = $title + "->" + e.title
							return $title
						}
					}
				}
				//在子节点找到对应选项
				if (title && item.value == value) {
					$title = title + "-" + item.title
				} else if (title && item.children) {
					//当前子节点未找到继续递归向下查找
					/* $title = title + '-' + item.title
          this.getSelectedItem(value, item.children, $title); */
					for (const e of item.children) {
						if (e.value == value) {
							$title = $title + "->" + e.title
							return $title
						}
					}
				}
			}
		}
	},
	mounted() {},
	created() {
		this.callGetDepartmentCateTree()
		this.callGetProductCateTree()
		this.callGetSelectOption()

		setTimeout(() => {
			this.form.setFieldsValue({
				productName: this.projectdetail.productProjectName,
				productLevel: this.projectdetail.level <= 0 ? null : this.projectdetail.level + "",
				fixedState: this.projectdetail.fixedState <= 0 ? null : this.projectdetail.fixedState + "",
				productType: this.projectdetail.productType,
				projectName: this.projectdetail.projectName,
				customer: this.projectdetail.customer,
				productClassification: this.projectdetail.productClassification,
				department: {
					label: this.getSelectedItem(this.projectdetail.department, this.departmentCateTreeData),
					value: this.projectdetail.department
				},
				productCate: this.projectdetail.productCateOptionBeans
					? this.projectdetail.productCateOptionBeans.map(item => item.id)
					: null
			})
			if (this.projectdetail.plannedFixedDate != "-") {
				this.form.setFieldsValue({ plannedFixedDate: moment(this.projectdetail.plannedFixedDate, "YYYY-MM-DD") })
			}
		}, 600)
	},
	computed: {
		cpgcsrowSelection: {
			get() {
				// 在这里定义 getter 方法中的逻辑
			},
			set() {
				// 在这里定义 setter 方法中的逻辑（如果需要使用 setter 方法）
			},
			columnWidth: 30,
			onChange: (selectedRowKeys, selectedRows) => {
				this.cpgcsselectedRowKeys = selectedRowKeys
				this.cpgcsselectedRow = selectedRows

				for (let i = 0; i < this.cpgcsselectedRow.length; i++) {
					if (i == 0) {
						this.cpgcs = this.cpgcsselectedRow[i].name
					} else {
						this.cpgcs += "," + this.cpgcsselectedRow[i].name
					}
				}

				this.form2.setFieldsValue({
					productEnginMulti: this.cpgcsselectedRowKeys.join(",")
				})
			}
		},
		gcdbrowSelection: {
			get() {
				// 在这里定义 getter 方法中的逻辑
			},
			set() {
				// 在这里定义 setter 方法中的逻辑（如果需要使用 setter 方法）
			},
			columnWidth: 30,
			onChange: (selectedRowKeys, selectedRows) => {
				this.gcdbselectedRowKeys = selectedRowKeys
				this.gcdbselectedRow = selectedRows

				for (let i = 0; i < this.gcdbselectedRow.length; i++) {
					if (i == 0) {
						this.gcdb = this.gcdbselectedRow[i].name
					} else {
						this.gcdb += "," + this.gcdbselectedRow[i].name
					}
				}

				this.form2.setFieldsValue({
					engineCenterRepresentMulti: this.gcdbselectedRowKeys.join(",")
				})
			}
		}
	}
}
</script>

<style lang="less" scoped="">
.spindiv {
	padding-bottom: 10px;
}

/deep/.ant-input[disabled] {
	color: rgba(0, 0, 0, 0.66);
	cursor: initial;
	opacity: 1;
}
/deep/.ant-select-disabled {
	color: rgba(0, 0, 0, 0.66);
}
/deep/.ant-select-disabled .ant-select-selection {
	cursor: initial;
}

/deep/ .ant-form label {
	font-size: 12px;
}
/deep/.ant-select-arrow {
	display: none;
}
/deep/.ant-input-disabled + .ant-calendar-picker-icon {
	display: none;
}
/deep/.anticon {
	display: none;
}
/deep/ .ant-form-item {
	margin-bottom: 1px;
}

/deep/ .ant-form-item-control {
	text-align: left;
}

/deep/ .ant-form-item-label,
/deep/ .ant-form-item-control {
	line-height: 40px;
}

.head_title {
	padding: 0 20px;
	font-size: 16px;
	text-align: left;
	overflow: hidden;
}

/deep/ .ant-select-selection--single .ant-select-selection__rendered {
	margin-right: 0;
}

/deep/.vue-treeselect__multi-value-item {
	background: transparent;
	font-size: 13px;
	vertical-align: initial;
}
/deep/.vue-treeselect {
	margin-top: 8px;
}
/deep/.vue-treeselect__control {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 24px;
	overflow: hidden;
	border-radius: initial;
}
/deep/.vue-treeselect__control * {
	padding: 0 !important;
	margin: 0 !important;
	line-height: initial !important;
	white-space: nowrap;
}
/deep/.vue-treeselect__value-remove {
	color: #e9e9e9;
}
/deep/.vue-treeselect__multi-value-item {
	color: rgba(0, 0, 0, 0.65);
}

/* .ant-descriptions-view {
        text-align: left;
        overflow: auto;
    }.ant-descriptions-bordered .ant-descriptions-item-label, .ant-descriptions-bordered .ant-descriptions-item-content{
		padding: 5px;
	}
	.ant-descriptions-row > th, .ant-descriptions-row > td{
		padding: 0;
	}.ant-descriptions-item-content,.ant-descriptions-item-label{
        font-size: 12px;
    } */

/deep/.ant-input[disabled],
/deep/.ant-select-disabled .ant-select-selection {
	color: rgba(0, 0, 0, 0.7);
	background-color: #fff;
	cursor: initial;
}
</style>
