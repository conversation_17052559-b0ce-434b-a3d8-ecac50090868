import { axios } from '@/utils/request'

export function saveProductparam (params) {
    return axios({
      url: '/productparam/save',
      method: 'post',
      data: params
    })
}

export function getProductparam(parameter) {
    return axios({
      url: '/productparam/getone',
      method: 'get',
      params: parameter
    })
  }


  export function getProductParamsCopy(parameter) {
    return axios({
      url: '/productparam/copylist',
      method: 'get',
      params: parameter
    })
  }