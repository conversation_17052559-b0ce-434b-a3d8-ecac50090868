import { axios } from '@/utils/request'

export function testProgressAdd(parameter) {
  return axios({
    url: '/testProgress/add',
    method: 'post',
    data: parameter
  })
}

export function testProgressList(parameter) {
  return axios({
    url: '/testProgress/list',
    method: 'post',
    data: parameter
  })
}

export function testProgressGetBase(parameter) {
  return axios({
    url: '/testProgress/getBase',
    method: 'post',
    data: parameter
  })
}
export function testProgressListPage(parameter,flag) {
  return axios({
    url: flag?'/testProgress/listPageAndData':'/testProgress/listPage',
    method: 'post',
    data: parameter
  })
}

export function testProgressGantList(parameter) {
  return axios({
    url: '/testProgress/gantList',
    method: 'post',
    data: parameter
  })
}

export function testProgressUpdate(parameter) {
  return axios({
    url: '/testProgress/update',
    method: 'post',
    data: parameter
  })
}

export function listToCopy(parameter) {
  return axios({
    url: '/testProgress/listToCopy',
    method: 'post',
    data: parameter
  })
}
export function get(parameter) {
  return axios({
    url: '/testProgress/get',
    method: 'post',
    data: parameter
  })
}

export function testProgressUpdateOnlyBean(parameter) {
  return axios({
    url: '/testProgress/updateOnlyBean',
    method: 'post',
    data: parameter
  })
}

export function testProgressUpdateData(parameter) {
  return axios({
    url: '/testProgress/updateData',
    method: 'post',
    data: parameter
  })
}

export function testProgressUpdateDataSelect(parameter) {
  return axios({
    url: '/testProgress/updateDataSelect',
    method: 'post',
    data: parameter
  })
}

export function testProgressImport(parameter) {
  return axios({
    url: '/testProgress/importData',
    method: 'post',
    data: parameter
  })
}


export function centerAdd(parameter) {
  return axios({
    url: '/testProgressCenter/add',
    method: 'post',
    data: parameter
  })
}


export function centerList(parameter) {
  return axios({
    url: '/testProgressCenter/list',
    method: 'post',
    data: parameter
  })
}

export function centerAllList(parameter) {
  return axios({
    url: '/testProgressCenter/allList',
    method: 'post',
    data: parameter
  })
}

export function centerAllExportExcel(parameter) {
  return axios({
    url: '/testProgressCenter/export',
    method: 'get',
    params: parameter,
    responseType: 'blob'
  })
}



/* 技师工作台*/
// 列表
export function getTodoTaskList(parameter) {
  if(parameter.searchType)  parameter.searchType = parameter.searchType + ''
  return axios({
    url: '/testProjectTodoTask/getTodoTaskList',
    method:'post',
    data: parameter
  })
}

/* 检测报告工作台*/
export function getReportTodoTaskList(parameter) {
  if(parameter.searchType)  parameter.searchType = parameter.searchType + ''
  return axios({
    url: '/testReportTodoTask/getReportTodoTaskList',
    method:'post',
    data: parameter
  })
}

// 测试员列表
export function getTesterList(parameter) {
  if(parameter.searchType)  parameter.searchType = parameter.searchType + ''
  return axios({
    url: '/testProjectTodoTask/getTesterList',
    method:'post',
    data: parameter
  })
}

// 详情
export function getLimsResultByOrdTaskId(parameter) {
  return axios({
    url: '/tLimsResult/getLimsResultByOrdTaskId',
    method: 'post',
    data: parameter
  })
}

// 修改
export function updateLimsResultByOrdTaskId(parameter) {
  return axios({
    url: '/tLimsResult/updateLimsResultByOrdTaskId',
    method: 'post',
    data: parameter
  })
}

// 提交
export function InputDataSubmit(parameter) {
  return axios({
    url: '/tLimsOrdtask/InputDataSubmit',
    method: 'post',
    data: parameter
  })
}

// 上传
export function uploadFiles(parameter) {
  return axios({
    url: '/limsUpload/open/basemodule/sys/files/upload',
    method: 'post',
    data: parameter
  })
}


// 下载附件
export function getFiles(id) {
  return axios({
    url: `/limsUpload/open/basemodule/sys/files/download/${id}`,
    method: 'get'
  })
}

// 查询附件
export function getFileList(id) {
  return axios({
    url: `/limsUpload/open/basemodule/sys/files/queries/${id}`,
    method: 'get'
  })
}

// 下载附件
export function getFiles1(id) {
  return axios({
    url: `/limsUpload/open/basemodule/sys/files/download/${id}`,
    method: 'get'
  })
}

// 删除附件
export function deleteFiles(parameter) {
  return axios({
    url: `/limsUpload/open/basemodule/sys/files/delete`,
    method: 'post',
    data: parameter
  })
}

// 日历寿命
// 详情
export function getTestProDetailByTaskId(id) {
  return axios({
    url: `testProjectTodoTask/getTestProDetailByTaskId/${id}`,
    method: 'get'
  })
}

// 提交
export function updateTestProDetail(parameter) {
  return axios({
    url: `/testProjectTodoTask/updateTestProDetail`,
    method: 'post',
    data: parameter
  })
}

// 提交
export function handleSaveCalendarTestData(parameter) {
  return axios({
    url: `/testProjectTodoTask/handleSaveCalendarTestData`,
    method: 'post',
    data: parameter
  })
}

export function updateSafetyTestData(parameter) {
  return axios({
    url: `/safetyTest/updateSafetyTestData`,
    method: 'post',
    data: parameter
  })
}

export function handleSaveSafetyTestData(parameter) {
  return axios({
    url: `/safetyTest/handleSaveSafetyTestData`,
    method: 'post',
    data: parameter
  })
}

export function updateBatteryStatusOfTc(parameter) {
  return axios({
    url: `/safetyTest/updateBatteryStatusOfTc`,
    method: 'post',
    data: parameter
  })
}

// 更新计划表电芯状态
export function updateBatteryStatusAndData(parameter,onlyUpdateCurStage) {
  return axios({
    url: `/testProjectTodoTask/updateBatteryStatusAndData/${onlyUpdateCurStage}`,
    method: 'post',
    data: parameter
  })
}

export function alterMiddleCheckInfo(parameter,type) {
  return axios({
    url: `/testProjectTodoTask/alterMiddleCheckInfo/${type}`,
    method: 'post',
    data: parameter
  })
}

export function getTestProgressSizeById(progressId) {
  return axios({
    url: `/testProgressSize/getTestProgressSizeById/${progressId}`,
    method: 'post'
  })
}

export function initTestProgressSize(progressId) {
  return axios({
    url: `/testProgressSize/initTestProgressSize/${progressId}`,
    method: 'post'
  })
}

export function updateTestProgressSize(parameter) {
  return axios({
    url: `/testProgressSize/updateTestProgressSize`,
    method: 'post',
    data: parameter
  })
}

export function deleteTestProgressSize(parameter) {
  return axios({
    url: `/testProgressSize/deleteTestProgressSize`,
    method: 'post',
    data: parameter
  })
}

export function updateInBoxPosition(parameter) {
  return axios({
    url: `/testProjectTodoTask/updateInBoxPosition`,
    method: 'post',
    data: parameter
  })
}


export function updateXsh(parameter) {
  return axios({
    url: `/testProgressDetail/updateXsh`,
    method: 'post',
    data: parameter
  })
}

// 完成
export function finishCalLifeTodoTask(parameter) {
  return axios({
    url: `/testProjectTodoTask/finishCalLifeTodoTask`,
    method: 'post',
    data: parameter
  })
}

export function finishSafetyTestTodoTask(parameter) {
  return axios({
    url: `/safetyTest/finishSafetyTestTodoTask`,
    method: 'post',
    data: parameter
  })
}

// 修改检测人/计划时间
export function executeAlterTesterOrPlanTime (parameter) {
  return axios({
    url: '/testProjectTodoTask/alterTesterOrPlanTime',
    method: 'post',
    data: parameter
  })
}

export function executeAlterYfTester (parameter) {
  return axios({
    url: '/testProjectTodoTask/alterYfTester',
    method: 'post',
    data: parameter
  })
}

// 计划表
export function getTestProgress(parameter) {
  return axios({
    url: `/testProgress/get`,
    method: 'post',
    data: parameter
  })
}

export function testProgressExport (parameter) {
  return axios({
    url: '/testProgress/export',
    method: 'get',
    params: parameter,
    responseType: 'blob'
  })
}

// 导入模板
export function importModel(parameter) {
  return axios({
    url: `/testProjectTodoTask/import/${parameter.ordTaskId}`,
    method: 'post',
    data: parameter
  })
}


// 导出模板
export function exportModel(parameter) {
  return axios({
    url: `/testProjectTodoTask/downloadExportTemplate`,
    method: 'post',
    data: parameter,
    responseType: 'blob'
  })
}

// 导出模板
export function exportModelOfSafetyTest(parameter) {
  return axios({
    url: `/testProjectTodoTask/exportModelOfSafetyTest`,
    method: 'post',
    data: parameter,
    responseType: 'blob'
  })
}

// 导出日历寿命测试中工程师查看的处理结果
export function exportHandleResult(parameter) {
  return axios({
    url: `/testProjectTodoTask/exportHandleResult`,
    method: 'post',
    data: parameter,
    responseType: 'blob'
  })
}

// 导出第四实验室安全测试中工程师查看的处理结果
export function exportHandleResultOfAq(parameter) {
  return axios({
    url: `/safetyTest/exportHandleResultOfAq`,
    method: 'post',
    data: parameter,
    responseType: 'blob'
  })
}

// 导出第四实验室安全测试中尺寸原始数据
export function exportSizeOriDataOfAq(parameter) {
  return axios({
    url: `/safetyTest/exportSizeOriDataOfAq`,
    method: 'post',
    data: parameter,
    responseType: 'blob'
  })
}

// 导出日历寿命测试尺寸模板
export function exportSizeTemplate(progressId) {
  return axios({
    url: `/testProjectTodoTask/exportSizeTemplate/${progressId}`,
    method: 'post',
    responseType: 'blob'
  })
}

// 返回日历寿命测试中工程师查看的处理结果
export function getHandleResult(parameter) {
  return axios({
    url: `/testProjectTodoTask/getHandleResult`,
    method: 'post',
    data: parameter
  })
}

export function getHandleResultOfAq(parameter) {
  return axios({
    url: `/safetyTest/getHandleResultOfAq`,
    method: 'post',
    data: parameter
  })
}

// 日历寿命测试中工程师查看的处理结果更新
export function updateHandleResult(parameter,testProgressId) {
  return axios({
    url: `/testProjectTodoTask/updateHandleResult/` +testProgressId ,
    method: 'post',
    data: parameter
  })
}

export function validExportSizeOriData(parameter) {
  return axios({
    url: `/testProjectTodoTask/validExportSizeOriData`,
    method: 'post',
    data: parameter
  })
}

export function validSafetyTestExport(parameter) {
  return axios({
    url: `/safetyTest/validSafetyTestExport`,
    method: 'post',
    data: parameter
  })
}

export function batchValidExportSizeOriData(parameter) {
  return axios({
    url: `/testProjectTodoTask/batchValidExportSizeOriData`,
    method: 'post',
    data: parameter
  })
}

// 导出日历寿命测试中工程师查看的尺寸原始数据
export function exportSizeOriData(parameter) {
  return axios({
    url: `/testProjectTodoTask/exportSizeOriData`,
    method: 'post',
    data: parameter,
    responseType: 'blob'
  })
}

// 日历寿命测试项目别名复制接口
export function aliasCopy(parameter) {
  return axios({
    url: `/testProjectTodoTask/queryStartCode`,
    method: 'post',
    data: parameter
  })
}

// 第四实验室测试项目别名复制接口
export function aliasCopyOfAq(parameter) {
  return axios({
    url: `/safetyTest/queryStartCodeOfAq`,
    method: 'post',
    data: parameter
  })
}

// 日历寿命测试查询进箱位置
export function getInBoxPositionList (parameter) {
  return axios({
    url: '/testProjectTodoTask/getInBoxPositionList',
    method: 'post',
    data: parameter
  })
}

// 导出测试台账
export function exportTestingLedgerList (parameter) {
  return axios({
    url: '/testProjectTodoTask/exportTestingLedgerList',
    method: 'post',
    data: parameter,
    responseType: 'blob'
  })
}

// 查询测试台账
export function getTestingLedgerList (parameter) {
  return axios({
    url: '/testProjectTodoTask/getTestingLedgerList',
    method: 'post',
    data: parameter
  })
}

// 分配计划开始日期
export function assignPlanStartDate (parameter) {
  return axios({
    url: '/testProjectTodoTask/assignPlanStartDate',
    method: 'post',
    data: parameter
  })
}

// 分配计划
export function assignPlan (parameter) {
  return axios({
    url: '/testProjectTodoTask/assignPlan',
    method: 'post',
    data: parameter
  })
}

// 根据ordTaskId获取中检信息
export function getMidCheckInfoByOrdTaskIds (parameter) {
  return axios({
    url: '/testProjectTodoTask/getMidCheckInfoByOrdTaskIds',
    method: 'post',
    data: parameter
  })
}

// 获取登录lims系统的密钥
export function getMD5Str (parameter) {
  return axios({
    url: '/testProjectTodoTask/getMD5Str',
    method: 'post',
    data: parameter
  })
}

// 获取测试工作量统计数据
export function getTestWorkStatistics(parameter) {
  return axios({
    url: '/testProjectTodoTask/getTestWorkStatistics',
    method: 'post',
    data: parameter
  })
}

// 导出测试工作量统计数据
export function exportTestWorkStatistics(parameter) {
  return axios({
    url: '/testProjectTodoTask/exportTestWorkStatistics',
    method: 'post',
    data: parameter,
    responseType: 'blob'
  })
}

// 获取测试费用统计数据
export function getTestPriceStatistics(parameter) {
  return axios({
    url: '/testProjectTodoTask/getTestPriceStatistics',
    method: 'post',
    data: parameter
  })
}

// 获取测试数量统计数据
export function getTestQuantityStatistics(parameter) {
  return axios({
    url: '/testProjectTodoTask/getTestQuantityStatistics',
    method: 'post',
    data: parameter
  })
}

// 删除日历寿命测试数据
export function clearCalendarTestData(parameter) {
  return axios({
    url: `/testProjectTodoTask/clearCalendarTestData`,
    method: 'post',
    data: parameter
  })
}

// 日历寿命测试计划_获取电芯信息
export function getBatteryInfoById(parameter) {
  return axios({
    url: `/testProjectTodoTask/getBatteryInfoById`,
    method: 'post',
    data: parameter
  })
}

// 日历寿命测试计划_获取所有存储阶段信息
export function getAllTestProgressDetail(parameter) {
  return axios({
    url: `/testProjectTodoTask/getAllTestProgressDetail`,
    method: 'post',
    data: parameter
  })
}

// 日历寿命_根据填写的实际日期更新计划日期
export function syncCalendarLifeTestDate(refreshDateFlag, type, parameter) {
  return axios({
    url: `/testProjectTodoTask/syncCalendarLifeTestDate/${refreshDateFlag}/${type}`,
    method: 'post',
    data: parameter
  })
}

// 日历寿命在线报告-根据id或ordtaskId查询
export function getOnlineReport(parameter) {
  return axios({
    url: `/testProgress/getOnlineReport`,
    method: 'post',
    data: parameter
  })
}

// 日历寿命在线报告-提交查询参数
export function commitQueryParam(parameter, testProgressId) {
  return axios({
    url: `/testProgress/commitQueryParam/${testProgressId}`,
    method: 'post',
    data: parameter
  })
}

// 日历寿命在线报告-刷新在线报告数据
export function updateOnlineReport(parameter) {
  return axios({
    url: `/testProgress/updateOnlineReport`,
    method: 'post',
    data: parameter
  })
}

// 日历寿命在线报告-导出在线报告数据
export function exportOnlineReport(parameter) {
  return axios({
    url: `/testProgress/exportOnlineReport`,
    method: 'post',
    data: parameter,
    responseType: 'blob'
  })
}



/* 技师工作台优化-2024-08-05 */
// 工作台_获取待办任务统计
export function getTodoTaskStatistics(parameter) {
  return axios({
    url: `/testProjectTodoTask/getTodoTaskStatistics`,
    method: 'post',
    data: parameter
  })
}


// 检测报告工作台_获取待办任务统计
export function getReportTodoTaskStatistics(parameter) {
  return axios({
    url: `/testReportTodoTask/getReportTodoTaskStatistics`,
    method: 'post',
    data: parameter
  })
}

// 检测报告工作台_更新任务状态
export function updateReportTaskStatus(parameter) {
  return axios({
    url: `/testReportTodoTask/updateReportTaskStatus`,
    method: 'post',
    data: parameter
  })
}

// 检测报告工作台_分配负责人
export function updateReportUploader(parameter) {
  return axios({
    url: `/testReportTodoTask/updateReportUploader`,
    method: 'post',
    data: parameter
  })
}

// 检测报告工作台_更新检测报告信息
export function updateReportTodoTaskData(parameter) {
  return axios({
    url: `/testReportTodoTask/updateReportTodoTaskData`,
    method: 'post',
    data: parameter
  })
}

// 检测报告工作台_上传检测报告提交
export function uploadReportSubmit(parameter) {
  return axios({
    url: `/testReportTodoTask/uploadReportSubmit`,
    method: 'post',
    data: parameter
  })
}

export function updateReportFile(parameter, type) {
  return axios({
    url: `/testReportTodoTask/updateReportFile/${type}`,
    method: 'post',
    data: parameter
  })
}

export function reUpdateReportFile(parameter) {
  return axios({
    url: `/testReportTodoTask/reUpdateReportFile`,
    method: 'post',
    data: parameter
  })
}

export function getTestReportByFolderNo(parameter) {
  return axios({
    url: `/testReportTodoTask/getTestReportByFolderNo`,
    method: 'post',
    data: parameter
  })
}

export function getAndSetTestReportFile(parameter, version, reportTodoTaskId) {
  return axios({
    url: `/testReportTodoTask/getAndSetTestReportFile/${version}/${reportTodoTaskId}`,
    method: 'post',
    data: parameter
  })
}

// 提交
export function updatePicOrVid(parameter, type, field, index) {
  return axios({
    url: `/testProgressDetail/updatePicOrVid/${type}/${field}/${index}`,
    method: 'post',
    data: parameter
  })
}

export function updatePicOrVidOfAq(parameter, type, field, index) {
  return axios({
    url: `/safetyTest/updatePicOrVidOfAq/${type}/${field}/${index}`,
    method: 'post',
    data: parameter
  })
}

export function getVideoByTestProgress(parameter) {
  return axios({
    url: `/testProgressDetail/getVideoByTestProgress`,
    method: 'post',
    data: parameter
  })
}

export function getPictureByTestProgress(parameter,totalDay) {
  return axios({
    url: `/testProgressDetail/getPictureByTestProgress/${totalDay}`,
    method: 'post',
    data: parameter
  })
}

export function getPictureBySafetyTestIds(parameter) {
  return axios({
    url: `/safetyTest/getPictureBySafetyTestIds`,
    method: 'post',
    data: parameter
  })
}

export function getVideoBySafetyTestIds(parameter) {
  return axios({
    url: `/safetyTest/getVideoBySafetyTestIds`,
    method: 'post',
    data: parameter
  })
}

export function getAttachBySafetyTestIds(parameter) {
  return axios({
    url: `/safetyTest/getAttachBySafetyTestIds`,
    method: 'post',
    data: parameter
  })
}

export function getEquiptInfoList(parameter) {
  return axios({
    url: `/testProjectTodoTask/getEquiptInfoList`,
    method: 'post',
    data: parameter
  })
}

// 获取第四实验室测试阶段为前后的详情
export function getSafetyTestByTask(parameter) {
  return axios({
    url: `/safetyTest/getSafetyTestByTask`,
    method: 'post',
    data: parameter
  })
}