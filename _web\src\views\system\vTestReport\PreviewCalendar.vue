<template>
  <div ref="wrapper" class="wrapper hide-progress-parent">
    <div class="flex-sb-center-row mb10" >
      <div class="head_title">{{ folderAndProjectName }}</div>
      <!-- 不要删，删了图例位置就不准确了 -->
      <span id="revealText14" class="reveal-text-opacity" style="font-size: 14px;"></span>
      <span id="revealText15" class="reveal-text-opacity" style="font-size: 15px;"></span>
      <span id="revealTextNone" class="reveal-text-opacity"></span>
    </div>
    <!-- 不要删，删了图例位置就不准确了 -->
    <span id="revealText14" class="reveal-text-opacity" style="font-size: 14px;"></span>
    <span id="revealText15" class="reveal-text-opacity" style="font-size: 15px;"></span>
    <span id="revealTextNone" class="reveal-text-opacity"></span>

    <pbiTabs :tabsList="tabsList" :activeKey="activeKey" @clickTab="value => this.activeKey = value" @clickPreviewFile="previewFile"></pbiTabs>
    <keep-alive>
      <CalendarOffline @titleChange="getTitle" @returnTop="handleReturnTop" v-if="offFlag == 1 && activeKey === 1" ></CalendarOffline>
    </keep-alive>
    <keep-alive>
      <CalendarOnline @titleChange="getTitle" @returnTop="handleReturnTop" v-if="onlineFlag == 1 && activeKey === 2"></CalendarOnline>
    </keep-alive>
    <keep-alive>
      <CalendarOfflinePicture @titleChange="getTitle" @returnTop="handleReturnTop" v-if="pictureFlag == 1 && activeKey === 3"></CalendarOfflinePicture>
    </keep-alive>
    <keep-alive>
      <CalendarOfflineVideo @titleChange="getTitle" @returnTop="handleReturnTop" v-if="videoFlag == 1 && activeKey === 4"></CalendarOfflineVideo>
    </keep-alive>
    <keep-alive>
      <OfflineAttachmentOfAq @titleChange="getTitle" @returnTop="handleReturnTop" v-if="attachmentFlag == 1 && activeKey === 5"></OfflineAttachmentOfAq>
    </keep-alive>

    <!-- 预览文件 start -->
    <a-drawer :bodyStyle="{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }" width="70%"
              :closable="false" placement="right" :visible="previewVisible" @close="previewVisible = false">
      <img style="max-width: 100%; height: auto;" :src="previewUrl" alt="">
    </a-drawer>
    <!-- 预览文件 end -->
  </div>

</template>
<script>
import pbiTabs from '@/components/tools/pbiTabs.vue';
import CalendarOnline from "@/views/system/vTestReport/calendarOnline";
import CalendarOffline from "@/views/system/vTestReport/calendarOffline";
import CalendarOfflineVideo from "@/views/system/vTestReport/calendarOfflineVideo";
import CalendarOfflinePicture from "@/views/system/vTestReport/calendarOfflinePicture";
import OfflineAttachmentOfAq from "@/views/system/vTestReport/offlineAttachmentOfAq";
import Vue from "vue";

export default {
  components: {
    pbiTabs,
    CalendarOnline,
    CalendarOffline,
    CalendarOfflineVideo,
    CalendarOfflinePicture,
    OfflineAttachmentOfAq
  },
  data: function() {
    return {
      folderAndProjectName: "", // 标题
      tabsList: [
        {value:1, label:'OCV|ACR|weigh|尺寸类'},
        {value:2, label:'容量|能量|DCR'},
        {value:3, label:'照片'},
        {value:4, label:'视频'},
        {value:5, label:'附件'}
      ], // tab列表
      activeKey: 1, // 选中的tab
      offFlag: 0, // 是否有离线数据
      onlineFlag: 0, // 是否有在线数据
      pictureFlag: 0, // 是否有离线图片
      videoFlag: 0, // 是否有离线视频
      attachmentFlag: 0, // 是否有附件

      previewVisible:false,
      previewFileID:'',
      previewUrl: '',
    }
  },
  mounted() {
    this.initTabs()
  },
  methods: {
    initTabs() {
      this.offFlag = this.$route.query.offFlag
      this.onlineFlag = this.$route.query.onlineFlag
      this.pictureFlag = this.$route.query.pictureFlag
      this.videoFlag = this.$route.query.videoFlag
      this.attachmentFlag = this.$route.query.attachmentFlag
      if (this.offFlag == 0) {
        // 离线报告无数据，不展示
        this.tabsList.splice(0, 1)
        this.tabsList = this.tabsList.filter(obj => obj.label !== '照片' && obj.label !== '视频');
        this.activeKey = 2
      }
      if (this.onlineFlag == 0) {
        // 在线报告无数据，不展示
        this.tabsList.splice(1, 1)
        this.activeKey = 1
      }
      if (!this.pictureFlag || this.pictureFlag == 0) {
        // 离线图片无数据，不展示
        this.tabsList = this.tabsList.filter(obj => obj.label !== '照片');
      }
      if (!this.videoFlag || this.videoFlag == 0) {
        // 离线视频无数据，不展示
        this.tabsList = this.tabsList.filter(obj => obj.label !== '视频');
      }
      if (!this.attachmentFlag || this.attachmentFlag == 0) {
        // 离线附件无数据，不展示
        this.tabsList = this.tabsList.filter(obj => obj.label !== '附件');
      }
    },
    getTitle(title) {
      this.folderAndProjectName = title
    },
    previewFile() {
      this.previewFileID = "1827991288916410370"
      this.previewUrl = '/api/sysFileInfo/previewPdf?Authorization=Bearer ' + Vue.ls.get('Access-Token') + '&id=' + this.previewFileID + '#toolbar=0'
      this.previewVisible = true
    },
    handleReturnTop() {
      // let timer = setInterval(() => {
      //   const topHeight = this.$refs.wrapper.scrollTop
      //   if (topHeight == 0) {
      //     clearInterval(timer)
      //     this.isShowReturnTop = false
      //     return
      //   }
      //   const speed = Math.ceil(topHeight / 10)
      //   this.$refs.wrapper.scrollTop = topHeight - speed
      // }, 50)
      this.$refs.wrapper.scrollTop = 0
    }
  }
}
</script>
<style lang="less" scoped>
.wrapper {
  height: 100vh;
  overflow-y: scroll;
  padding: 10px;
  margin: 0 0 0 -40px;
  background-color: #f0f2f5;

  overflow: scroll;
}

.head_title {
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.head_title::before {
  width: 8px;
  background: #1890ff;
  margin-right: 8px;
  content: "\00a0"; //填充空格
}

.flex-sb-center-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reveal-text-opacity{
  /* font-size: 14px; */
  font-family: 'Times New Roman';
  opacity: 0;
  padding: 0;
  margin: 0;
  line-height: 1;
}

/* 悬浮 */
.suspension-icon{
  position:fixed ;
  bottom: 20px;
  left: 20px;
  z-index: 10;
  font-size: 20px;
  padding: 10px 15px;
  border-radius: 50%;
  backdrop-filter: blur(6px);
  background-color:rgba(238, 238, 238,.3);
  cursor: pointer;
}
</style>