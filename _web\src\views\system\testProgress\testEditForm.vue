<template>
  <a-modal title="编辑" :width="800"
           :bodyStyle="{padding:0}"
           :visible="visible" :confirmLoading="confirmLoading" @ok="handleSubmit" style="padding: 0"
           :maskClosable="false"
           @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-tabs v-model="activeKey" @change="changeTab">
        <a-tab-pane key="basic" tab="基础信息">
          <a-form :form="form">
            <a-form-item style="display: none;" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['id']" />
            </a-form-item>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="测试状态" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>

                  <a-select  v-decorator="['testStatus']"
                             style="width: 100%">

                    <a-select-option value="Done">
                      Done
                    </a-select-option>
                    <a-select-option value="Ongoing">
                      Ongoing
                    </a-select-option>
                    <a-select-option value="Plan">
                      Plan
                    </a-select-option>
                    <a-select-option value="Stop">
                      Stop
                    </a-select-option>

                  </a-select>

                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="测试申请单" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input placeholder="请输入测试申请单"
                           v-decorator="['testCode']"/>
                </a-form-item>
              </a-col>

            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="产品名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入产品名称"
                           v-decorator="['productName']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="产品样品阶段" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入产品样品阶段"
                           v-decorator="['productSampleStage']"/>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="测试类型" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-select v-decorator="['testType']"
                            style="width: 100%">

                    <a-select-option value="研发测试">
                      研发测试
                    </a-select-option>
                    <a-select-option value="产品验证测试">
                      产品验证测试
                    </a-select-option>
                    <a-select-option value="产品鉴定测试">
                      产品鉴定测试
                    </a-select-option>

                  </a-select>

                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="申请部门" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入申请部门"
                           v-decorator="['dept']"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="申请人" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入申请人"
                           v-decorator="['applicant']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="测试项目" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入测试项目"
                           v-decorator="['testProject']"/>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="T/℃" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入T/℃"
                           v-decorator="['t']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="SOC" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入soc"
                           v-decorator="['soc']"/>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="测试周期" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input-number placeholder="请输入存储天数" :min="1"  :precision="0"
                                  v-decorator="['testPeriod']"/>

                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="数量" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input-number placeholder="请输入数量" :min="1"  :precision="0"
                           v-decorator="['quantity']"/>
                </a-form-item>
              </a-col>
            </a-row>


            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="测试技师" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input type='hidden' v-decorator="['testMan']" />
                  <a-dropdown v-model="testManDownVisible" placement="bottomCenter" :trigger="['click']">
                    <a-button class="man_button" :style="{color:testMan?'rgba(0, 0, 0, 0.65)':'#b7b7b7'}">{{testMan ? testMan : '选择测试技师'}}
                      <a-icon type="down" /></a-button>
                    <a-menu slot="overlay">
                      <a-spin :spinning="testManLoading" style="padding:10px 24px 0 24px;width:100%">
                        <a-input-search v-model="testManQueryParam.searchValue" placeholder="搜索..." @change="ontestManSearch" />
                        <s-table style="width:100%;" ref="testManTable" :rowKey="(record) => record.id" :columns="vColumns" :data="loadtestManData" :customRow="customtestManRow" :scroll="{ y: 120,x:120}">
                        </s-table>
                      </a-spin>
                    </a-menu>
                  </a-dropdown>
                </a-form-item>

                <a-form-item style="display: none" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input type='hidden' v-decorator="['testManId']" />

                </a-form-item>




              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="测试地点" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-select v-decorator="['testAddress']"
                            style="width: 100%">

                    <a-select-option value="A1_3F">
                      V圆柱检测室
                    </a-select-option>

                    <a-select-option value="R2_2F">
                      材料验证检测室
                    </a-select-option>

                    <a-select-option value="R4_4F">
                      动力电池检测室
                    </a-select-option>

                  </a-select>

                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">


                <a-form-item label="电芯载体" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">

                  <a-select v-decorator="['sampleType']"
                            style="width: 100%">

                    <a-select-option value="G圆柱">
                      G圆柱
                    </a-select-option>
                    <a-select-option value="C圆柱">
                      C圆柱
                    </a-select-option>
                    <a-select-option value="V圆柱">
                      V圆柱
                    </a-select-option>
                    <a-select-option value="方型">
                      方型
                    </a-select-option>
                    <a-select-option value="软包">
                      软包
                    </a-select-option>
                    <a-select-option value="模组">
                      模组
                    </a-select-option>

                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">


                <a-form-item label="存储位置" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">

                  <a-input placeholder="请输入存储位置"
                           v-decorator="['saveAddress']"/>

                </a-form-item>
              </a-col>

            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">


                <a-form-item label="测试目的" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">

                  <a-input placeholder="请输入测试目的"
                           v-decorator="['testPurpose']"/>

                </a-form-item>
              </a-col>

              <a-col :md="12" :sm="24">
                <a-form-item label="是否阶段式" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-select v-decorator="['stageFlag', {rules: [{required: true, message: '请输入是否阶段式！'}]}]"
                            style="width: 100%"  placeholder="请选择是否阶段式">
                    <a-select-option :value="parseInt(1)">
                      是
                    </a-select-option>
                    <a-select-option :value="parseInt(0)">
                      否
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="湿度/%RH" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input-number placeholder="湿度/%RH"
                           v-decorator="['humidity']"/>
                </a-form-item>
              </a-col>

              <a-col :md="12" :sm="24">
                <a-form-item label="样品类型" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-select v-decorator="['sampleOrderType']" style="width: 100%">
                    <a-select-option value="mz">
                      模组
                    </a-select-option>
                    <a-select-option value="dx">
                      电芯
                    </a-select-option>
                    <a-select-option value="dxlc">
                      电芯-Live Cell
                    </a-select-option>
                    <a-select-option value="dxdc">
                      电芯-Dummy Cell
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>


            <!-- <a-row :gutter="24">

               <a-col :md="12" :sm="24">
                 <a-form-item label="已完成天数" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                   <a-input placeholder="请输入已完成天数"
                            v-decorator="['finishDay', {rules: [{required: true, message: '请输入已完成天数！'}]}]"/>
                 </a-form-item>
               </a-col>
             </a-row>-->
          </a-form>
        </a-tab-pane>
        <a-tab-pane key="test" tab="中检信息">
          <a-form :form="form">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">

                <a-form-item label="中检次数" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input-number  placeholder="请输入中检次数" @change="changeNum"  :min="1"  :precision="0"
                            v-decorator="['testNum']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">

                <a-form-item label="进箱开始时间" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-date-picker :allowClear="false" placeholder="" :disabled="canUpdate"
                                 v-decorator="['zero']"
                                 @change="changeZero($event)">
                  </a-date-picker>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
          <a-table :columns="columns" :data-source="record.data" bordered
                   style="padding: 20px"
                   bordered :scroll="{y:210}"
                   :pagination="false"
                   :rowKey="(record) => record.id"
          >

            <template slot="day" slot-scope="text, record, index">
              <div v-if="record.actualInDateStatus == 1" style="text-align: center">{{text}}</div>
              <a-input-number v-else :value="text" @change="changeDay($event,index,record)"   :min="0"  :precision="0" size="small"
                              style="border: 0"
                       ></a-input-number>
            </template>

            <template slot="footer">
              <a @click="addData">
                <a-icon type="plus" style="width: 15px;height: 15px;margin-left: 50px;cursor: pointer"/>
              </a>
            </template>

            <template  slot="actualInDate" slot-scope="text, inRecord, index">
              <div v-if="inRecord.actualInDateStatus == 1" class="green">{{text}}</div>
              <div v-else-if="inRecord.actualInDateStatus == 0 && index != 0 && record.data[index - 1].actualInDate == null && !(index == 1 && record.data[index - 1].day == 0)" style="text-align: center">{{text}}</div>
              <a-date-picker v-else :allow-clear="false"
                             placeholder=""
                             style="width: 100%;"
                             :value="null != text ?moment(text, 'YYYY-MM-DD'):null"
                             :disabledDate="(date) => disabledActStartDate(date,index)"
                             @change="changeActuInDate($event,index,inRecord)">
              </a-date-picker >

            </template>

            <template  slot="inDate" slot-scope="text, record, index">
              <div v-if="record.actualInDateStatus == 1" style="text-align: center">{{text}}</div>
              <a-date-picker v-else :allow-clear="false"
                             placeholder=""
                             :disabledDate="(date) => disabledStartDate(date,index)"
                             style="width: 100%;"
                             :value="null != text ?moment(text, 'YYYY-MM-DD'):null"
                             @change="changeInDate($event,index,record)">
              </a-date-picker >

            </template>

          </a-table>

        </a-tab-pane>
      </a-tabs>


    </a-spin>

    <template slot="footer">
      <a-button key="back" @click="handleCancel">
        取消
      </a-button>
      <a-popconfirm placement="topRight" ok-text="提交" cancel-text="取消" @confirm="handleSubmit">
        <template slot="title">
          <p>确定提交吗</p>
        </template>
        <a-button key="submit" type="primary" >
          确定
        </a-button>
      </a-popconfirm>

    </template>


  </a-modal>
</template>

<script>
  import {
    testProgressUpdate
  } from '@/api/modular/system/testProgressManager'
  import moment from "moment";
  import {
    getUserLists
  } from '@/api/modular/system/userManage'
  import {
    STable
  } from '@/components'

  export default {
    components: {
      STable
    },
    props: {
      type: {
        type: String,
        default: ''
      },
    },
    data() {
      return {
        loadtestManData: parameter => {
          return getUserLists(Object.assign(parameter, this.testManQueryParam)).then((res) => {
            return res.data
          })
        },
        testMan:null,
        testManLoading:false,
        testManQueryParam:{},
        testManDownVisible:false,
        vColumns: [{
          title: '账号',
          dataIndex: 'account'
        },
          {
            title: '姓名',
            dataIndex: 'name'
          },
        ],
        loadSzData: parameter => {
          return getUserLists(Object.assign(parameter, this.szQueryParam)).then((res) => {
            return res.data
          })
        },
        canUpdate:false,
        columns: [
          {
            title: '存储阶段',
            dataIndex: 'index',
            align: 'center',
            width: 50,
            customRender: (text, record, index) => {

              if(this.record.data[0].day == 0){
                if(index == 0){
                  return "初始阶段"
                }else{
                  return `存储第${index}阶段`
                }
              }else{
                return `存储第${index+1}阶段`
              }

            }
          }, {
            title: '存储天数',
            width: 90,
            align: 'center',
            dataIndex: 'day',
            scopedSlots: {
              customRender: 'day'
            },
          }, {
            title: '计划开始时间',
            width: 90,
            align: 'center',
            dataIndex: 'inDate',
            scopedSlots: {
              customRender: 'inDate'
            },
          },{
            title: '实际进箱时间',
            width: 70,
            align: 'center',
            dataIndex: 'actualInDate',
            scopedSlots: {
              customRender: 'actualInDate'
            },

          }, {
            title: '结束时间',
            width: 90,
            align: 'center',
            dataIndex: 'outDate'
          }
        ],
        activeKey: 'basic',
        startDate: null,
        labelCol: {
          xs: {
            span: 12
          },
          sm: {
            span: 8
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 14
          }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this),
        record:{}
      }
    },
    mounted() {

    },
    methods: {
      ontestManSearch(e) {
        this.$refs.testManTable.refresh()
      },
      customtestManRow(row, index) {
        return {
          on: {
            click: () => {
              this.form.setFieldsValue({
                testMan: row.name,
                testManId: row.account
              })
              this.testMan = row.name
              this.testManDownVisible = false
            }
          }
        }
      },
      disabledStartDate(startValue,index) {

        if(index == 0){
          return false
        }else{
          if(this.record.data[index - 1].outDate != null){
            return startValue < moment(this.record.data[index - 1].outDate)
          }
        }

        return false
      },
      disabledActStartDate(startValue,index) {

        if(index == 0){
          return false
        }else{
          if(this.record.data[index - 1].actualInDate != null){
            return startValue < moment(this.record.data[index - 1].actualInDate)
          }
        }

        return false
      },
      moment,
      changeActuInDate($event,index,record){


        if(index == 0){

            this.form.setFieldsValue({
              zero : $event
            })
            this.record.zero = $event

        }


        record.actualInDate = moment($event).format('YYYY-MM-DD')
        record.outDate = moment(record.actualInDate).add(record.day,'days').format('YYYY-MM-DD')

        this.changeInOutDate(index+1)

      },
      changeInDate($event,index,record){

        if(index == 0){
          this.form.setFieldsValue({
            zero : $event,
          })
          this.record.zero = moment($event).format('YYYY-MM-DD')
        }

        record.inDate = moment($event).format('YYYY-MM-DD')
        record.actualInDate = null
        record.outDate = moment(record.inDate).add(record.day,'days').format('YYYY-MM-DD')

        this.changeInOutDate(index+1)

      },
      changeTab(key){

        this.$nextTick(() => {

          if(key == 'basic'){

            if(this.form.getFieldValue('id') == null){
              this.form.setFieldsValue({
                id:this.record.id,
                testStatus:this.record.testStatus,
                testCode:this.record.testCode,
                productName:this.record.productName,
                productSampleStage:this.record.productSampleStage,
                testType:this.record.testType,
                dept:this.record.dept,
                applicant:this.record.applicant,
                testProject:this.record.testProject,
                t:this.record.t,
                soc:this.record.soc,
                testPeriod:this.record.testPeriod,
                quantity:this.record.quantity,
                testMan:this.record.testMan,
                testAddress:this.record.testAddress,
                sampleType:this.record.sampleType,
                sampleOrderType:this.record.sampleOrderType,
                stageFlag:this.record.stageFlag,
                testPurpose:this.record.testPurpose
              })
            }

          }else{
            if(this.form.getFieldValue('zero') == null
              || this.form.getFieldValue('zero') == undefined){
              this.form.setFieldsValue({
                zero : this.record.zero!=null?moment(this.record.zero):null,
                testNum : this.record.data.length
              })
            }
          }




        })

      },
      changeZero(event) {

        //this.zero = moment(event).format('YYYY-MM-DD')
        if (this.record.data.length > 0) {
          this.record.data[0].inDate = moment(event).format('YYYY-MM-DD')
          this.record.zero = event
          if (this.record.data[0].day != null) {
            this.record.data[0].outDate = moment(event).add(this.record.data[0].day, 'days').format('YYYY-MM-DD')
          }
        }
        this.changeInOutDate(1);

      },

      changeInOutDate(index) {
        if (index == 0) {
          if(this.form.getFieldValue('zero') != null){
            this.record.data[0].inDate = moment(this.form.getFieldValue('zero')).format('YYYY-MM-DD')

            if (this.record.data[0].day != null && (this.record.data[0].inDate != null || this.record.data[0].actualInDate != null)) {
              this.record.data[0].outDate = moment(this.record.data[0].actualInDate == null?this.record.data[0].inDate:this.record.data[0].actualInDate).add(this.record.data[0].day, 'days').format('YYYY-MM-DD')
            }
          }


          index += 1;
        }
        for (let i = index; i < this.record.data.length; i++) {
          let before = this.record.data[i - 1]
          if (before.outDate != null) {
            if(index == 1 && before.day == 0){
              this.record.data[i].inDate = before.outDate
            }else{
              this.record.data[i].inDate = moment(before.outDate).add(3, 'days').format('YYYY-MM-DD')
            }

            if (this.record.data[i].inDate != null && (this.record.data[i].day != null || this.record.data[i].actualInDate != null)) {
              this.record.data[i].outDate = moment(this.record.data[i].actualInDate != null?this.record.data[i].actualInDate:this.record.data[i].inDate).add(this.record.data[i].day, 'days').format('YYYY-MM-DD')
            }
          }
        }


      },

      changeNum($event) {

        if (this.record.data.length > $event) {

          for (let i = $event; i < this.record.data.length; i++) {
            if(this.record.data[i].actualInDate != null){
              this.$message.warn('有实际出库时间的数据不能删除')
              return
            }
          }


          this.record.data = this.record.data.slice(0, $event)
        } else {
          for (let i = this.record.data.length; i < $event; i++) {
            this.record.data.push({uuid: i * 10000 + 1000, day: null, inDate: null, outDate: null})
          }
        }

        this.changeInOutDate(0);


      },
      addData() {
        this.record.data.push({uuid: this.record.data.length * 10000 + 1000, day: null, inDate: null, outDate: null})
        this.form.setFieldsValue(
          {
            testNum: this.record.data.length
          }
        )

      },

      changeDay($event, index, record) {


         /* if(this.record.data.length > index+2 && this.record.data[index+1].day != null){

            if(this.record.data[index+1].day <= $event){
              this.$message.warn('天数应小于下次中检天数')
              return
            }
          }

        if(index > 0){
          if(this.record.data[index - 1] != null){

            if(this.record.data[index - 1].day >= $event){
              this.$message.warn('天数应大于上次中检天数')
              return
            }
          }
        }*/
        record.day = $event
        this.changeInOutDate(index);
      },
      add() {
        this.visible = true
      },
      edit(record) {
        this.record = record

        /*if(this.record.data.length > 0){
          if(this.record.data[0].day == 0){
            this.record.data = this.record.data.slice(1);
          }
        }*/

        //this.testManQueryParam.searchValue = this.record.testMan

        this.testMan = this.record.testMan

        if (!record.data) {
          this.$message.warning('请先查看存储明细后再操作编辑')
          return
        }
        if(record.data.length > 0 && record.data[0].actualInDateStatus == 1){
          this.canUpdate = true
        }

        this.visible = true
        setTimeout(() => {

          if(this.activeKey == 'basic'){
            this.form.setFieldsValue({
              id:this.record.id,
              testStatus:this.record.testStatus,
              testCode:this.record.testCode,
              productName:this.record.productName,
              productSampleStage:this.record.productSampleStage,
              testType:this.record.testType,
              dept:this.record.dept,
              applicant:this.record.applicant,
              testProject:this.record.testProject,
              t:this.record.t,
              soc:this.record.soc,
              testPeriod:this.record.testPeriod,
              quantity:this.record.quantity,
              testMan:this.record.testMan,
              testAddress:this.record.testAddress,
              sampleType:this.record.sampleType,
              sampleOrderType:this.record.sampleOrderType,
              saveAddress:this.record.saveAddress,
              stageFlag:this.record.stageFlag,
              testPurpose:this.record.testPurpose,
              humidity:this.record.humidity
            })
          }else{
            this.form.setFieldsValue({
              zero : this.record.zero != null ?moment(this.record.zero):null,
              testNum : this.record.data.length,
              id:this.record.id,
              testStatus:this.record.testStatus,
              testCode:this.record.testCode,
              productName:this.record.productName,
              productSampleStage:this.record.productSampleStage,
              testType:this.record.testType,
              dept:this.record.dept,
              applicant:this.record.applicant,
              testProject:this.record.testProject,
              t:this.record.t,
              soc:this.record.soc,
              testPeriod:this.record.testPeriod,
              quantity:this.record.quantity,
              testMan:this.record.testMan,
              testAddress:this.record.testAddress,
              sampleType:this.record.sampleType,
              sampleOrderType:this.record.sampleOrderType,
              saveAddress:this.record.saveAddress,
              stageFlag:this.record.stageFlag,
              testPurpose:this.record.testPurpose
            })
          }



        }, 100)

      },
      onChangeSampleDate(date, dateString) {
        if (date == null) {
          this.startDate = ''
        } else {
          this.startDate = moment(date).format('YYYY-MM-DD')
        }
      },
      handleSubmit() {
        const {
          form: {
            validateFields
          }
        } = this

        this.confirmLoading = true
        validateFields((errors, values) => {

          if (!errors) {

            if(this.record.data.length == 0){
              this.$message.warn('请填写第中检信息')
              this.activeKey = 'test'
              this.confirmLoading = false
              return
            }
            for (let i = 0; i < this.record.data.length; i++) {
              if(this.record.data[i].day == null){
                this.$message.warn('请填写第'+(i+1)+'次中检天数')
                this.activeKey = 'test'
                this.confirmLoading = false
                return
              }
            }

            for (let i = 0; i < this.record.data.length; i++) {
              if(null != this.record.data[i].actualInDate){
                this.record.data[i].actualInDateStatus = 1
              }
            }

            values.data = this.record.data

            if(this.record.data.length > 0){
              values.zero = moment(this.record.data[0].actualInDate != null?this.record.data[0].actualInDate:
                this.record.data[0].inDate).format('YYYY-MM-DD')
            }

            if(values.zero == 'Invalid date'){
              values.zero = null
            }


            //values.zero = moment(this.record.zero).format('YYYY-MM-DD')
            testProgressUpdate(values).then((res) => {
              this.confirmLoading = false
              if (res.success) {
                this.$message.success('修改成功')
                this.handleCancel()
                this.$emit('ok', values)
              } else {
                this.$message.error('修改失败：' + res.message)
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            if(this.activeKey ==  'test'){
              if(values.testNum != null && values.zero != null){
                this.activeKey = 'basic'
              }
            }
            if(this.activeKey ==  'basic'){
              if(values.testStatus != null && values.testCode != null
                && values.productName != null
                && values.productSampleStage != null
                && values.testType != null
                && values.dept != null
                && values.applicant != null
                && values.testProject != null
                && values.t != null
                && values.soc != null
                && values.testPeriod != null
                && values.quantity != null
                && values.testMan != null
                && values.testAddress != null
                && values.stageFlag != null
                && values.sampleType != null
                && values.sampleOrderType != null
              ){
                this.activeKey = 'test'
              }
            }
            this.confirmLoading = false

          }
        })
      },
      handleCancel() {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
<style lang="less" scoped>
  .ant-form-item {

    margin-bottom: 0px;

  }

  /deep/.ant-modal-body {
    padding: 0!important;
  }


  /deep/ .ant-table-thead > tr > th, /deep/ .ant-table-tbody > tr > td {
    padding: 0px;
  }

  /deep/ .ant-table-footer {

    padding: 0px;
  }

  /deep/ .ant-table-pagination.ant-pagination {
    margin: 5px 0;
  }
  /deep/.ant-input-number {
    width: 100%;
  }

  /deep/.ant-input-number-sm>.ant-input-number-input-wrap>.ant-input-number-input{
    text-align: center;
  }
  .green{
    background-color: #58a55c;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 25px;
  }
  /deep/.ant-calendar-picker>div>i{
    display: none;
  }
  /deep/td>.ant-calendar-picker>div>input{
    text-align: center;
    font-size: 12px;
    color: black;
    height: 25px;
    border: 0;
  }
  /deep/.ant-calendar-picker-input.ant-input {
    color: black;
  }

  .man_button{
    padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;
  }

</style>
