/**
 * 文件处理相关的Mixin
 * 提供文件处理的公共方法，减少组件中的重复代码
 */
import { validateFile } from '@/utils/validationUtils';
import { handleUploadError, handleExcelError, showSuccess, showInfo, showWarning } from '@/utils/errorUtils';
import { api } from '@/api';
import * as XLSX from 'xlsx';

export default {
  data() {
    return {
      fileToUpload: null,
      uploading: false,
      fileList: []
    };
  },
  methods: {
    /**
     * 下载模板文件
     * @param {string} templatePath - 模板文件路径
     * @param {string} fileName - 下载后的文件名
     */
    downloadTemplate(templatePath, fileName) {
      const link = document.createElement('a');
      link.href = templatePath;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      showSuccess('模板文件下载已开始');
    },
    
    /**
     * 处理文件移除
     */
    handleFileRemove() {
      this.fileToUpload = null;
      this.fileList = [];
      showInfo('文件已删除');
      this.$emit('file-removed');
      return true;
    },
    
    /**
     * 文件上传前校验
     * @param {File} file - 文件对象
     * @param {number} maxSize - 最大文件大小（MB）
     * @returns {boolean} 是否通过校验
     */
    beforeFileUpload(file, maxSize = 10) {
      if (!validateFile(file, maxSize)) {
        return false;
      }
      
      this.fileToUpload = file;
      
      // 自动上传文件
      this.$nextTick(() => {
        this.uploadFile();
      });
      
      return false;
    },
    
    /**
     * 创建文件列表项
     * @returns {Array} 文件列表项数组
     */
    createFileListItem() {
      if (!this.fileToUpload) return [];
      
      return [{
        uid: Date.now().toString(),
        name: this.fileToUpload.name,
        status: 'uploading',
        size: this.fileToUpload.size,
        type: this.fileToUpload.type,
        originFileObj: this.fileToUpload
      }];
    },
    
    /**
     * 上传文件到服务器
     * @param {string} apiEndpoint - API端点
     * @param {Function} successCallback - 成功回调
     */
    async uploadFile(apiEndpoint = 'uploadFile', successCallback = null) {
      if (!this.fileToUpload) {
        showWarning('请先选择要上传的文件');
        return;
      }
      
      const formData = new FormData();
      formData.append('file', this.fileToUpload);
      
      this.uploading = true;
      this.$emit('upload-start');
      
      const newFileList = this.createFileListItem();
      this.fileList = newFileList;
      
      try {
        // 调用API上传文件
        const response = await api.data[apiEndpoint](formData);
        
        if (response.data.success) {
          // 更新文件状态
          newFileList[0].status = 'done';
          this.fileList = newFileList;
          
          // 调用成功回调
          if (typeof successCallback === 'function') {
            successCallback(response.data);
          }
          
          showSuccess('文件上传成功');
          this.$emit('upload-success', response.data);
        } else {
          // 更新文件状态为错误
          newFileList[0].status = 'error';
          this.fileList = newFileList;
          
          showWarning(response.data.message || '文件上传失败');
          this.$emit('upload-error', response.data);
        }
      } catch (error) {
        // 更新文件状态为错误
        newFileList[0].status = 'error';
        this.fileList = newFileList;
        
        handleUploadError(error);
        this.$emit('upload-error', error);
      } finally {
        this.uploading = false;
        this.$emit('upload-complete');
      }
    },
    
    /**
     * 导入Excel文件
     * @param {File} file - Excel文件
     * @param {Function} callback - 处理数据的回调函数
     * @returns {boolean} 是否成功导入
     */
    importExcel(file, callback) {
      if (!validateFile(file)) {
        return false;
      }
      
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { type: 'array' });
          
          // 获取第一个工作表
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];
          
          // 将工作表转换为JSON
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
          
          // 调用回调函数处理数据
          if (typeof callback === 'function') {
            callback(jsonData, workbook);
          }
          
          showSuccess('Excel导入成功');
          return true;
        } catch (error) {
          handleExcelError(error, 'import');
          return false;
        }
      };
      
      reader.onerror = (error) => {
        handleExcelError(error, 'import');
        return false;
      };
      
      reader.readAsArrayBuffer(file);
      return true;
    },
    
    /**
     * 导出数据到Excel
     * @param {Array} data - 要导出的数据
     * @param {string} fileName - 文件名
     * @param {string} sheetName - 工作表名
     */
    exportToExcel(data, fileName, sheetName = 'Sheet1') {
      try {
        // 创建工作簿
        const wb = XLSX.utils.book_new();
        
        // 创建工作表
        const ws = XLSX.utils.aoa_to_sheet(data);
        
        // 添加工作表到工作簿
        XLSX.utils.book_append_sheet(wb, ws, sheetName);
        
        // 导出Excel文件
        XLSX.writeFile(wb, fileName);
        
        showSuccess('Excel导出成功');
      } catch (error) {
        handleExcelError(error, 'export');
      }
    }
  }
};
