<template>
    <div>
        <x-card>
            <div slot="content" class="table-page-search-wrapper">
                <a-form layout="inline">
                    <a-row :gutter="48">
                        <a-col :md="8" :sm="24">
                            <a-form-item label="校验状态">
                                <a-select @change="change" v-model="queryParam.verifyRes" allow-clear placeholder="请选择状态">
                                    <a-select-option :value="0" >全部</a-select-option>
                                    <a-select-option :value="2" >失败</a-select-option>
                                    <a-select-option :value="1" >成功</a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :md="8" :sm="24">
                            <span class="table-page-search-submitButtons">
                        <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                        <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                      </span>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
        </x-card>
        <a-card :bordered="false">
            <s-table ref="table" :columns="columns" :data="loadData" :alert="false" :rowKey="(record) => record.id">
                <template slot="verifyRes" slot-scope="text, record">
                    <span v-if="text == 1" style="color: #52c41a;">校验成功</span>
                    <span v-else><a style="color: #fa541c;" @click="$refs.bomverify.view(record.verifyErrors)">校验失败</a></span>
                </template>
            </s-table>
        </a-card>
        <bomverify ref="bomverify"/>
    </div>
</template>

<script>
    import bomverify from './bomverify'
    import {
        getSapVerifyPage
	} from "@/api/modular/system/bomManage"
    import {
        STable,
        XCard
    } from '@/components'
    export default {
        components: {
            XCard,
            STable,
            bomverify
        },
        data() {
            return {
                queryParam: {},
                loadData: parameter => {
                    return getSapVerifyPage(Object.assign(parameter, this.queryParam)).then((res) => {
                        return res.data
                    })
                },
                columns: [{
                        title: 'BOM编号',
                        dataIndex: 'bomNo'
                    },
                    {
                        title: 'BOM文件名',
                        dataIndex: 'bomName'
                    },
                    {
                        title: 'BOM版本',
                        dataIndex: 'bomVersion'
                    },
                    {
                        title: 'sap校验时间',
                        dataIndex: 'verifyDate'
                    },
                    {
                        title: 'sap校验状态',
                        dataIndex: 'verifyRes',
                        scopedSlots: {
							customRender: 'verifyRes'
						}
                    },
                ]
            }
        },
        methods: {
            change(value) {
                this.$refs.table.refresh(true) // { key: "lucy", label: "Lucy (101)" }
            },
        }
    }
</script>

<style>

</style>