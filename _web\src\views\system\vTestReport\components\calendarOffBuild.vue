<template>
  <div class="calendar-off-div">
    <div class="left-content block">
      <div class="flex-sb-center-row">
        <h3>一、数据选择</h3>
        <div style="float: right">
          <a-popconfirm placement="topRight" title="确认清空？" @confirm="handleDeleteAll">
            <a-button class="mr5">清空</a-button>
          </a-popconfirm>
          <a-popconfirm placement="topRight" title="确认删除？" @confirm="handleDeleteSelected('testProgress')">
            <a-button>删除</a-button>
          </a-popconfirm>
        </div>
      </div>
      <div class="mt10">
        <a-table bordered
                 :columns="testProgressColumns"
                 :data-source="templateParam.testProgressList"
                 :row-selection="{
                   selectedRowKeys: testProgressDeleteSelectedRowKeys,
                   columnWidth: 30,
                   onChange: (selectedRowKeys, selectedRows) => {
                     this.testProgressDeleteSelectedRowKeys = selectedRowKeys
                   }
                 }"
                 :pagination="false">
          <template slot="action" slot-scope="text, record, index, columns">
            <a-tooltip placement="top" title="删除" arrow-point-at-center>
              <a @click="deleteDataOne('testProgress', index)" style="text-align: center">
                <a-icon type="delete" style="font-size: large;margin-right: 3px"/>
              </a>
            </a-tooltip>
            <a-tooltip placement="top" title="上移" arrow-point-at-center v-if="index !== 0">
              <a @click="moveUp(templateParam.testProgressList, index)" style="text-align: center">
                <a-icon type="arrow-up" style="font-size: large;margin-right: 3px"/>
              </a>
            </a-tooltip>
            <a-tooltip placement="top" title="下移" arrow-point-at-center v-if="index !== templateParam.testProgressList.length - 1">
              <a @click="moveDown(templateParam.testProgressList, index)" style="text-align: center">
                <a-icon type="arrow-down" style="font-size: large"/>
              </a>
            </a-tooltip>
          </template>
          <template slot="testProject" slot-scope="text, record, index, columns">
            <a @click="checkCalendarReportByRecord(record)">{{text}}</a>
          </template>
          <template slot="footer">
            <div class="footer-btn" :class="{ 'plus-btn': !Array.isArray(templateParam.testProgressList) || templateParam.testProgressList.length === 0 }" @click="handleCancel('isShowTestProgressModal')">
              <span></span>
              <span></span>
              <span></span>
              <span></span>
              <a-icon type="plus"></a-icon>
            </div>
          </template>
        </a-table>
      </div>
    </div>

    <div class="right-content block ml10">
      <div class="flex-sb-center-row">
        <h3>二、方案分组</h3>
        <div style="float: right">
          <a-popconfirm placement="topRight" title="确认清空？" @confirm="() => {
              this.templateParam.optionParamList = []
              this.optionParamDeleteSelectedRowKeys = []
              this.handleEmit()
            }">
            <a-button class="mr5">清空</a-button>
          </a-popconfirm>
          <a-popconfirm placement="topRight" title="确认删除？" @confirm="handleDeleteSelected('optionParam')">
            <a-button>删除</a-button>
          </a-popconfirm>
        </div>
      </div>
      <div class="mt10">
        <a-table class="param-table"
                 bordered
                 :columns="optionParamColumns"
                 :dataSource="templateParam.optionParamList"
                 :row-selection="{
                   selectedRowKeys: optionParamDeleteSelectedRowKeys,
                   columnWidth: 30,
                   onChange: (selectedRowKeys, selectedRows) => {
                     this.optionParamDeleteSelectedRowKeys = selectedRowKeys
                   }
                 }"
                 :pagination="false">
          <template slot="action" slot-scope="text, record, index, columns">
            <a-tooltip placement="top" title="删除" arrow-point-at-center>
              <a @click="deleteDataOne('optionParam', index)">
                <a-icon type="delete" style="font-size: large;margin-right: 3px"/>
              </a>
            </a-tooltip>
            <a-tooltip placement="top" title="上移" arrow-point-at-center v-if="index !== 0">
              <a @click="moveUp(templateParam.optionParamList, index)">
                <a-icon type="arrow-up" style="font-size: large;margin-right: 3px"/>
              </a>
            </a-tooltip>
            <a-tooltip placement="top" title="下移" arrow-point-at-center v-if="index !== templateParam.optionParamList.length - 1">
              <a @click="moveDown(templateParam.optionParamList, index)">
                <a-icon type="arrow-down" style="font-size: large"/>
              </a>
            </a-tooltip>
          </template>
          <template slot="optionName" slot-scope="text, record, index, columns">
            <a-input class="input-text" v-model="templateParam.optionParamList[index].optionName" @blur="handleEmit"/>
          </template>
          <template slot="sampleCodeList" slot-scope="text, record, index, columns">
            <!-- 全展示 -->
            <div  v-if="!Array.isArray(record.sampleCodeList) || record.sampleCodeList.length <= 2 || record.isUnfolded">
              <div class="padding2" v-for="(item, index) in record.sampleCodeList" style="border-bottom: 1px solid #e8e8e8;">
                {{item}}
                <a @click="handleDelete(record, index)">
                  <a-tooltip title="删除数据">
                    <a-icon class="btn-icon" type="delete" />
                  </a-tooltip>
                </a>
              </div>
            </div>
            <!-- 展示前2个 -->
            <div v-else>
              <div class="padding2" v-for="(item, index) in record.sampleCodeList.slice(0, 2)" style="border-bottom: 1px solid #e8e8e8;">
                {{item}}
                <a @click="handleDelete(record, index)">
                  <a-tooltip title="删除数据">
                    <a-icon class="btn-icon" type="delete" />
                  </a-tooltip>
                </a>
              </div>
            </div>
            <div v-if="Array.isArray(record.sampleCodeList) && record.sampleCodeList.length > 2" class="shrink-btn padding2" @click="handleFold(record)">
              {{record.isUnfolded ? '收起' : `${'+'}${record.sampleCodeList.length - 2} 展开`}}
            </div>
            <a @click="handleAddData(index)">
              <a-tooltip title="添加电芯">
                <a-icon class="btn-icon padding2" type="plus" />
              </a-tooltip>
            </a>
          </template>
          <template slot="footer">
            <div class="footer-btn" :class="{ 'plus-btn': !Array.isArray(templateParam.testProgressList) || templateParam.optionParamList.length === 0 }" @click="addParam">
              <span></span>
              <span></span>
              <span></span>
              <span></span>
              <a-icon type="plus"></a-icon>
            </div>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 测试项目选择弹窗 -->
    <a-modal title="测试项目选择"
             :width="1200"
             :height="600"
             :bodyStyle="{ padding: 0 }"
             :visible="isShowTestProgressModal"
             style="padding: 0"
             :maskClosable="false"
             @cancel="handleCancel('isShowTestProgressModal')">

      <div class="operate-wrap">
        <div class="filter-box">
          <div class="operate-block" v-for="item in filterOptions">
            <div class="label mr8">{{item.label}} : </div>
            <div v-if="item.type === 'input'" class="input-short">
              <a-input v-model="queryParam[item.dataIndex]" @keyup.enter="getList" @change="getList"/>
            </div>
            <div v-else-if="item.type === 'select'" class="input-short">
              <a-select v-model="queryParam[item.dataIndex]" dropdown-class-name="dropdownClassName" allowClear style="width:100%" @keyup.enter="getList" @change="getList">
                <a-select-option v-for="cItem in item.selectOption" :value="cItem.value">{{cItem.label}}</a-select-option>
              </a-select>
            </div>
          </div>
        </div>
        <div class="button-box">
          <a-button @click="getList(true)">重置</a-button>
        </div>
      </div>

      <s-table class="testProgressTable2"
               bordered
               :columns="testProgressColumns2"
               :data="loadData"
               :rowKey="record1 => record1.id"
               :row-selection="{
                 selectedRowKeys: testProgressSelectedRowKeys,
                 onSelect: onSelectChange,
                 onSelectAll: onSelectAllChange,
                 columnWidth: 30
               }"
               :scroll="{x:true}"
               ref="testProgressTable">
        <template slot="testProject" slot-scope="text, record, index, columns">
          <a @click="checkCalendarReportByRecord(record)">{{text}}</a>
        </template>
      </s-table>

      <template slot="footer">
        <a-button key="back" @click="handleCancel('isShowTestProgressModal')">关闭</a-button>
      </template>
    </a-modal>

    <!-- 电芯选择弹窗 -->
    <a-modal title="电芯选择"
             :width="600"
             :height="600"
             :bodyStyle="{ padding: 0 }"
             :visible="isShowSampleCodeModal"
             style="padding: 0"
             :maskClosable="false"
             @cancel="handleCancel('isShowSampleCodeModal')">

      <div style="padding: 10px;">
        <a-checkbox-group v-model="checkedSampleCodes" :options="sampleCodeOptions" @change="handleCheckedCodeChange"></a-checkbox-group>
      </div>

      <template slot="footer">
        <a-button key="back" @click="handleCancel('isShowSampleCodeModal')">关闭</a-button>
      </template>
    </a-modal>

  </div>
</template>

<script>
import {STable} from "@/components";
import {tLimsFolderListPage} from "@/api/modular/system/limsManager";
import {testProgressListPage, validExportSizeOriData} from "@/api/modular/system/testProgressManager";
import {mapGetters} from "vuex";

export default {
  name: "calendarOffBuild",
  components: {
    STable,
  },
  props: {
    templateParamObj: {
      type: Object,
      default: { testProgressList: [], optionParamList: [] }
    },
  },
  data() {
    return {
      templateParam: {testProgressList:[], optionParamList: []},

      // testProgress选择
      isShowTestProgressModal: false,
      filterOptions:[
        {
          label:'测试申请单',
          dataIndex:'testCode',
          type:'input'
        },
        {
          label:'申请人',
          dataIndex:'applicant',
          type:'input'
        },
        {
          label:'测试技师',
          dataIndex:'testMan',
          type:'input'
        },
        {
          label:'产品名称',
          dataIndex:'productName',
          type:'input'
        },
        {
          label:'部门',
          dataIndex:'dept',
          type:'input'
        },
        {
          label:'电芯载体',
          dataIndex:'sampleType',
          type:'select',
          selectOption:[
            {
              label:'G圆柱',
              value:'G圆柱'
            },
            {
              label:'C圆柱',
              value:'C圆柱'
            },
            {
              label:'V圆柱',
              value:'V圆柱'
            },
            {
              label:'方型',
              value:'方型'
            },
            {
              label:'软包',
              value:'软包'
            }
          ]
        },
        {
          label:'测试项目',
          dataIndex:'testProject',
          type:'input'
        },
        {
          label:'样品阶段',
          dataIndex:'productSampleStage',
          type:'input'
        },
      ],
      testProgressSelectedRowKeys: [],
      // 表头
      testProgressColumns2: [
        {
          title: '序号',
          dataIndex: 'index',
          align: 'center',
          width: 50,
          customRender: (text, record, index) => `${index + 1}`
        }, {
          title: '测试状态',
          width: 80,
          align: 'center',
          dataIndex: 'testStatus',
        }, {
          title: '测试申请单',
          width: 100,
          align: 'center',
          dataIndex: 'testCode',
        }, {
          title: '电芯载体',
          width: 70,
          align: 'center',
          dataIndex: 'sampleType',
        }, {
          title: '产品名称',
          width: 70,
          align: 'center',
          dataIndex: 'productName',
        }, {
          title: '产品样品阶段',
          width: 100,
          align: 'center',
          dataIndex: 'productSampleStage',
        }, {
          title: '测试类型',
          width: 100,
          align: 'center',
          dataIndex: 'testType',
        }, {
          title: '申请部门',
          width: 100,
          align: 'center',
          dataIndex: 'dept',
        }, {
          title: '申请人',
          width: 80,
          align: 'center',
          dataIndex: 'applicant',
        }, {
          title: '测试项目',
          width: 140,
          align: 'center',
          dataIndex: 'testProject',
          scopedSlots: { customRender: 'testProject' },
        }, {
          title: '测试项目别名',
          width: 100,
          align: 'center',
          ellipsis:true,
          dataIndex: 'testAlias',
        }, {
          title: '数量',
          width: 50,
          align: 'center',
          dataIndex: 'quantity',
        }, {
          title: '测试技师',
          width: 80,
          align: 'center',
          dataIndex: 'testMan',
        },
      ],
      queryParam: {},
      loadData: parameter => {
        return testProgressListPage(Object.assign(parameter, this.queryParam), false).then((res) => {
          return res.data
        })
      },

      // 表头
      testProgressColumns: [
        {
          title: '序号',
          dataIndex: 'index',
          align: 'center',
          width: 50,
          customRender: (text, record, index) => `${index + 1}`
        }, {
          title: '测试状态',
          width: 80,
          align: 'center',
          dataIndex: 'testStatus',
        }, {
          title: '测试申请单',
          width: 100,
          align: 'center',
          dataIndex: 'testCode',
        }, {
          title: '电芯载体',
          width: 70,
          align: 'center',
          dataIndex: 'sampleType',
        }, {
          title: '产品名称',
          width: 70,
          align: 'center',
          dataIndex: 'productName',
        }, {
          title: '测试项目',
          width: 140,
          align: 'center',
          dataIndex: 'testProject',
          scopedSlots: { customRender: 'testProject' },
        }, {
          title: '测试项目别名',
          width: 100,
          align: 'center',
          ellipsis:true,
          dataIndex: 'testAlias',
        },
      ],
      testProgressDeleteSelectedRowKeys: [],

      optionParamColumns: [
        {
          title: "操作",
          width: 50,
          align: "center",
          scopedSlots: {customRender: "action"}
        },
        {
          title: "序号",
          width: 40,
          align: "center",
          customRender: (text, record, index) => index + 1
        },
        {
          title: "方案名称",
          width: 120,
          align: "center",
          dataIndex: "optionName",
          scopedSlots: {customRender: "optionName"}
        },
        {
          title: "电芯选择",
          width: 120,
          align: "center",
          dataIndex: "sampleCodeList",
          scopedSlots: {customRender: "sampleCodeList"}
        },
      ],
      optionParamDeleteSelectedRowKeys: [],

      // 分组电芯选择
      optionRowIndex: 0,
      isShowSampleCodeModal: false,
      checkedSampleCodes: [],
      sampleCodeOptions: [],
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
  },
  created() {
    this.templateParam = this.templateParamObj.templateParam || { testProgressList: [], optionParamList: [] }
  },
  methods: {
    handleCancel(target) {
      if (target === 'isShowTestProgressModal' && this.isShowTestProgressModal === false) {
        this.testProgressSelectedRowKeys = this.templateParam.testProgressList.map(item => item.id)
      }

      this[target] = !this[target]
    },

    handleDeleteAll() {
      this.templateParam.testProgressList = []
      this.testProgressDeleteSelectedRowKeys = []

      // 改变方案行的电芯列表
      this.templateParam.optionParamList.forEach(item => {
        item.sampleCodeList = []
      })

      this.handleEmit()
    },
    addParam() {
      this.templateParam.optionParamList.push({ optionName:'', sampleCodeList: [] })
      this.handleEmit()
    },
    getSampleCodes(testProgress) {
      let sampleCodes = []
      if (testProgress) {
        if (testProgress.sampleCodes) {
          sampleCodes = JSON.parse(testProgress.sampleCodes)
        } else if (testProgress.cellTestCodes) {
          sampleCodes = JSON.parse(testProgress.cellTestCodes)
          sampleCodes = sampleCodes.map(item => {
            return item.length > 17 ? item.substring(item.length - 17) : item
          })
        }
      }

      return sampleCodes
    },
    handleDeleteSelected(target) {
      let deleteSampleCodes = new Set()
      // 按照索引顺序降序排列，避免删除元素影响后续索引
      this[`${target}DeleteSelectedRowKeys`].sort((a, b) => b - a)
      this[`${target}DeleteSelectedRowKeys`].forEach(item => {
        if (target === 'testProgress') {
          let sampleCodes = this.getSampleCodes(this.templateParam.testProgressList[item])
          if (Array.isArray(sampleCodes)) {
            sampleCodes.forEach(code => deleteSampleCodes.add(code))
          }
        }
        this.templateParam[`${target}List`].splice(item, 1)
      })
      this[`${target}DeleteSelectedRowKeys`] = []
      this.handleDeleteSampleCodes(deleteSampleCodes)

      this.handleEmit()
    },
    deleteDataOne(target, index) {
      this.templateParam[`${target}List`].splice(index, 1)
      this.handleEmit()
    },
    moveUp(arr, index) {
      if (arr.length > 1 && index > 0) { // 确保数组至少有两个元素，且索引有效
        arr[index] = arr.splice(index - 1, 1, arr[index])[0]; // 移除元素后立即插入到前一个位置
      }
      this.handleEmit()
    },
    moveDown(arr, index) {
      if (arr.length > 1 && index < arr.length - 1) { // 确保数组至少有两个元素，且索引有效
        arr[index] = arr.splice(index + 1, 1, arr[index])[0]; // 移除元素后立即插入到后一个位置
      }
      this.handleEmit()
    },

    getList(flag) {
      if(flag==true){
        this.queryParam = {}
      }
      this.$refs.testProgressTable.refresh()
    },
    onSelectChange(record, selected) {
      this.templateParam.testProgressList = this.templateParam.testProgressList || []

      if (selected) {
        this.handleValidRole(record)
      } else {
        for (let i = 0; i < this.testProgressSelectedRowKeys.length; i++) {
          if (this.testProgressSelectedRowKeys[i] === record.id) {
            let deleteSampleCodes = new Set(this.getSampleCodes(record))
            this.handleDeleteSampleCodes(deleteSampleCodes)

            this.testProgressSelectedRowKeys.splice(i, 1)
            this.templateParam.testProgressList.splice(i, 1)
            break
          }
        }
      }

      this.handleEmit()
    },
    onSelectAllChange(selected, selectedRows, changeRows) {
      this.templateParam.testProgressList = this.templateParam.testProgressList || []

      if (selected) {
        selectedRows.forEach(item => {
          this.handleValidRole(item)
        })
      } else {
        let deleteSampleCodes = new Set()
        for (let i = 0; i < changeRows.length; i++) {
          if (this.testProgressSelectedRowKeys.includes(changeRows[i].id)) {
            let index = this.testProgressSelectedRowKeys.indexOf(changeRows[i].id)

            let sampleCodes = new Set(this.getSampleCodes(changeRows[i]))
            if (Array.isArray(sampleCodes)) {
              sampleCodes.forEach(code => deleteSampleCodes.add(code))
            }

            this.testProgressSelectedRowKeys.splice(index, 1)

            this.templateParam.testProgressList.splice(index, 1)
          }
        }

        // 清除测试项目需要改变电芯列表、分组中的电芯
        this.handleDeleteSampleCodes(deleteSampleCodes)
      }

      this.handleEmit()
    },
    handleDeleteSampleCodes(deleteSampleCodes) {
      const deleteSampleCodesSet = new Set(deleteSampleCodes)
      if (deleteSampleCodesSet.size === 0) {
        return
      }

      // 改变方案行的电芯列表
      this.templateParam.optionParamList.forEach(item => {
        item.sampleCodeList = item.sampleCodeList.filter(sampleCode => !deleteSampleCodesSet.has(sampleCode))
      })
    },
    handleValidRole(record) {
      let checkAllDataFlag = this.userInfo.roles.filter(item => item.code === "check_all_calendar_report")
      // 管理员或者有【查看所有日历寿命测试项目报告】角色的用户
      if (this.userInfo.account === "superAdmin" || checkAllDataFlag.length > 0 || this.userInfo.name === record.applicant || this.userInfo.account == record.applicantAccount) {
        this.handleValidExportData(record)
      } else {
        tLimsFolderListPage({folderno:record.testCode}).then(res => {
          if(res.data.rows.length === 0){
            this.$message.warning('无权限选择其他申请人的报告！')
          }else{
            this.handleValidExportData(record)
          }
        })
      }
    },
    handleValidExportData(record) {
      validExportSizeOriData({ testProgressId: record.id, exportType: "handleResult" }).then(res => {
        if (!res.success) {
          this.$message.warn("离线数据为空！");
          return;
        }

        if (!this.testProgressSelectedRowKeys.includes(record.id)) {
          this.testProgressSelectedRowKeys.push(record.id);

          // 清除queryParam、resultDataJson属性，减少查询参数大小
          record.queryparam = null;
          record.resultDataJson = null;
          this.templateParam.testProgressList.push(record);

          // 方案列表新增行、电芯集合增加数据
          let sampleCodes = this.getSampleCodes(record)
          this.templateParam.optionParamList.push({ optionName:'', sampleCodeList: sampleCodes })
        }

        this.handleEmit()
      });
    },
    checkCalendarReportByRecord(record) {
      let checkAllDataFlag = this.userInfo.roles.filter(item => item.code === "check_all_calendar_report")

      // 管理员或者有【查看所有日历寿命测试项目报告】角色的用户
      if (this.userInfo.account === "superAdmin" || checkAllDataFlag.length > 0 || this.userInfo.name === record.applicant || this.userInfo.account == record.applicantAccount) {
        this.getCalendarReport(record)
      } else {
        tLimsFolderListPage({folderno:record.testCode}).then(res => {
          if(res.data.rows.length === 0){
            this.$message.warning('无权限查看其他申请人的报告！')
          }else{
            this.getCalendarReport(record)
          }
        })
      }
    },
    getCalendarReport(record) {
      const id = record.id;
      const alias = record.testAlias ? record.testAlias : "";
      validExportSizeOriData({ testProgressId: id, exportType: "handleResult" }).then(res => {
        if (res.success) {
          // 有离线数据，通过record.onlineReportStatus判断是否有在线数据
          window.open("/v_report_preview?testProgressId=" + id + "&alias=" + encodeURIComponent(alias) + "&offFlag=1&onlineFlag=" + (record.onlineReportStatus === 20 ? "1" : "0") + "&type=日历寿命", "_blank")
        } else {
          // 无离线数据，通过record.onlineReportStatus进行判断
          if (record.onlineReportStatus === 20) {
              window.open("/v_report_preview?testProgressId=" + id + "&alias=" + encodeURIComponent(alias) + "&offFlag=0&onlineFlag=1" + "&type=日历寿命", "_blank") // 需要展示在线报告
          } else {
            this.$message.warning(res.message.replace("导出", "查看"))
          }
        }
      })
    },

    handleAddData(index) {
      this.optionRowIndex = index
      this.checkedSampleCodes = this.templateParam.optionParamList[index].sampleCodeList || []
      // 所有电芯
      let sampleCodeSet = new Set()
      this.templateParam.testProgressList.forEach(testProgress => {
        const sampleCodeList = this.getSampleCodes(testProgress)
        sampleCodeList.forEach(code => sampleCodeSet.add(code))
      })
      // 已被选择的电芯
      let selectedSampleCodeSet = new Set()
      this.templateParam.optionParamList.forEach(optionParam => {
        optionParam.sampleCodeList.forEach(code => selectedSampleCodeSet.add(code))
      })
      // 多选框选项配置
      this.sampleCodeOptions = []
      sampleCodeSet.forEach(code => {
        this.sampleCodeOptions.push({label: code, value: code, disabled: !this.checkedSampleCodes.includes(code) && selectedSampleCodeSet.has(code) })
      })

      this.isShowSampleCodeModal = true
    },
    handleCheckedCodeChange(checkedValue) {
      this.templateParam.optionParamList[this.optionRowIndex].sampleCodeList = checkedValue
      this.handleEmit()
    },
    handleFold(record) {
      // 折叠状态变更
      record.isUnfolded = !record.isUnfolded
      // 强制刷新页面
      this.$forceUpdate()
    },
    handleDelete(record, index) {
      record.sampleCodeList.splice(index, 1)
      // 强制刷新页面
      this.$forceUpdate()

      this.handleEmit()
    },

    // 处理参数填写进度
    _handleProgress() {
      let percent = 0;

      // 测试项目名称（10%）
      if (this.templateParamObj.title) {
        percent += 10;
      }

      if (!Array.isArray(this.templateParam.testProgressList) || this.templateParam.testProgressList.length === 0) {
        return [percent, '需选择测试项目'];
      }

      // 已添加测试项目（30%）
      percent += 30;

      if (!Array.isArray(this.templateParam.optionParamList) || this.templateParam.optionParamList.length === 0) {
        return [percent, '需添加方案'];
      }

      // 所有行方案名称（30%）
      let optionNameFlag = true;
      for (let i = 0; i < this.templateParam.optionParamList.length; i++) {
        if (!this.templateParam.optionParamList[i].optionName) {
          optionNameFlag = false;
          break;
        }
      }
      if (optionNameFlag) {
        percent += 30;
      }

      // 所有行电芯选择（30%）
      let sampleCodeListFlag = true
      for (let i = 0; i < this.templateParam.optionParamList.length; i++) {
        if (!Array.isArray(this.templateParam.optionParamList[i].sampleCodeList) || this.templateParam.optionParamList[i].sampleCodeList.length === 0) {
          sampleCodeListFlag = false;
          break;
        }
      }
      if (sampleCodeListFlag) {
        percent += 30;
      }

      // 电芯未选全

      return [percent, !optionNameFlag ? '需填写所有行的方案名称' : !sampleCodeListFlag ? '所有方案均需选择电芯' : ''];
    },
    handleEmit() {
      let progressList = this._handleProgress()

      const param = {
        percent: progressList[0],
        warnStr: progressList[1],
        title: this.templateParamObj.title,
        templateParam: this.templateParam
      }

      this.$emit('updateTemplateParam', param)
    },
  },
}
</script>

<style lang="less" scoped>
@import "../css/optionMergeBuild.less";

.calendar-off-div {
  background: #fff;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
}

.calendar-off-div .left-content {
  width: 60%;
}

.calendar-off-div .right-content {
  width: 40%;
}

.input-text {
  width: 100%;
  text-align: center;
  border: 0;
}

.operate-wrap {
  display: flex;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
  margin-top: 10px;
}

.operate-wrap .filter-box {
  width: 88%;
  display: flex;
  flex-wrap: wrap;
}

.operate-wrap .button-box {
  width: 12%;
  display: flex;
  flex-wrap: wrap;
  margin-left: 20px;
}

.filter-box .operate-block {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.operate-block .label {
  width: 70px;
  text-align: right;

  font-size: 12px;
  color: #333;
}

.operate-block .input-short {
  width: 120px;
}

.operate-block .ant-input {
  font-size: 12px;
}

.operate-block .ant-select {
  font-size: 12px;
}

.padding2 {
  padding: 2px 0;
}

.shrink-btn {
  color: #1890ff;
  cursor: pointer;
  font-size: 12px;
  border-bottom: 1px solid #e8e8e8;
}

/deep/ .testProgressTable2 .ant-table-body {
  border: 1px solid #e8e8e8;
  overflow: auto; /* 滚动条 */
}

/deep/ .testProgressTable2 .ant-table-thead > tr > th {
  padding: 6px 6px !important;
  font-size: 13px;
}

/deep/ .testProgressTable2 .ant-table-tbody > tr > td {
  padding: 2px 2px !important;
  font-size: 12px;
}

/deep/ .testProgressTable2 .ant-table-pagination.ant-pagination {
  float: right;
  margin:2px 0 0;
  font-size: 12px;
}

/deep/ .testProgressTable2 .ant-select-selection-selected-value {
  font-size: 12px;
}

/deep/ .ant-modal-header {
  padding: 20px !important;
  font-size: 14px;
}

/deep/ .s-table-tool {
  padding: 0;
}

/deep/ .ant-modal-footer {
  padding: 10px !important;
}

</style>