import axios from 'axios'
const service = axios.create({
    timeout: 10000000
})

service.interceptors.request.use(
    config => {
        config.headers['X-Atlassian-Token'] = 'no-check'
        config.headers['Content-Type'] = 'application/json'
        return config
    },
    error => {
        //console.log(error)
        Promise.reject(error)
    }
)
service.interceptors.response.use(res => {
    if (res.request.responseType === 'blob') {
        return res
    }
    return res.data
}, error => {
    //console.log(error)
    Promise.reject(error)
})
export default service
