<template>
  <div class="all-wrapper">
    <div class="flex-sb-center-row">
      <div class="head_title">方案预览：{{ reportName }}</div>
    </div>

    <a-spin :spinning="isLoading">
      <div class="block" style="display: flex; flex-wrap: wrap;" v-if="tableVisible || templateResultObj.resultJsonObjList.length === 1">
        <div class="flex-sb-center-row" style="width: 100%;">
          <div style="font-size: 16px;">{{optionIndex+1}}/{{templateResultObj.resultJsonObjList.length}}</div>
          <h3 class="option-title">方案：{{templateResultObj.resultJsonObjList[optionIndex].optionName}}</h3>
          <a-tooltip title="导出数据">
            <a-button class="mr5" size="small" @click="handleDownload(optionIndex)"><a-icon type="download"/></a-button>
          </a-tooltip>
          <a-tooltip title="查看所有方案">
            <a-button v-if="templateResultObj.resultJsonObjList.length > 1" size="small" @click="handleShowAllOption"><a-icon type="rollback"/></a-button>
          </a-tooltip>
        </div>
        <div class="left-content block">
          <optionMergeEchartPreview :key="optionIndex"
                                    :resultJsonObj="templateResultObj.resultJsonObjList[optionIndex]"
                                    :optionKey="optionIndex"
                                    @echartsReady="isLoading = false"
                                    @reExport="reExport"/>
        </div>
        <div class="right-content block">
          <optionMergeTablePreview :key="optionIndex" :tableObjList="tableObjList" />
        </div>
      </div>
      <div v-else class="tab-content">
        <div class="mr10" v-for="(item, index) in templateResultObj.resultJsonObjList || []">
          <div class="flex-sb-center-row" style="padding: 0 2px 0 2px;">
            <div style="font-size: 16px;">{{index+1}}/{{templateResultObj.resultJsonObjList.length}}</div>
            <h3>方案：{{item.optionName}}</h3>
            <div>
              <a-tooltip title="表格数据">
                <a-button class="mr5" size="small" @click="handleShowTables(index)"><a-icon type="table"/></a-button>
              </a-tooltip>
              <a-tooltip title="导出数据">
                <a-button size="small" @click="handleDownload(index)"><a-icon type="download"/></a-button>
              </a-tooltip>
            </div>
          </div>
          <div class="block">
            <optionMergeEchartPreview
                :key="index"
                :resultJsonObj="item"
                :optionKey="index"
                @echartsReady="isLoading = false"
                @reExport="reExport"/>
          </div>
        </div>
      </div>
    </a-spin>

  </div>
</template>

<script>
import optionMergeEchartPreview from "@/views/system/vTestReport/components/optionMergeEchartPreview";
import optionMergeTablePreview from "@/views/system/vTestReport/components/optionMergeTablePreview";
import {getOptionMergeReport, exportOptionMergeReport} from "@/api/modular/system/limsManager";
//解压字符串
import {decodeAndDecompress, downloadfile1} from "@/utils/util"
import jsonBigint from "json-bigint";

export default {
  name: "optionMergePreview",
  components: {
    optionMergeEchartPreview,
    optionMergeTablePreview,
  },
  data() {
    return {
      id: null,
      data: {},
      reportName: '',
      templateResultObj: {resultJsonObjList: []},

      tableVisible: false,
      tableObjList: [],
      optionIndex: 0, // 当前选中的方案索引

      isLoading: true,
    }
  },
  computed: {
  },
  created() {

  },
  mounted() {
    this.initData()
  },
  methods: {
    initData(){
      if (this.$route.query.id) {
        this.id = this.$route.query.id
        getOptionMergeReport({id: this.id}).then(res => {
          this.data = res.data
          this.reportName = res.data.reportName

          let json = jsonBigint({storeAsString: true})
          this.templateResultObj = json.parse(decodeAndDecompress(res.data.allDataJson))
          if (this.templateResultObj.resultJsonObjList.length === 1) {
            this.handleShowTables(0)
          }
        })
      }
    },
    handleShowTables(optionIndex) {
      this.optionIndex = optionIndex
      const resultJsonObj = this.templateResultObj.resultJsonObjList[optionIndex]

      // 获取表头、表格数据
      this.tableObjList = this.getCalendarOffTableObjList(resultJsonObj)

      this.isLoading = true
      setTimeout(() => {
        this.tableVisible = true
      }, 300)
    },
    handleShowAllOption() {
      this.isLoading = true
      setTimeout(() => {
        this.tableVisible = false
      }, 300)
    },
    getCalendarOffTableObjList(resultJsonObj) {
      let tableObjList = []

      const sampleInfoMap = resultJsonObj.sampleInfoMap || {}
      const sampleCodeList = resultJsonObj.sampleCodeList || []
      const storageDaySetMap = resultJsonObj.storageDaySetMap || {}
      const heightColNameList = resultJsonObj.heightColNameList || []

      this.editObjList = ['voltage','innerres','height','volume','weight','isolateres']
      const nameList = ['电压/mV','内阻/mΩ','尺寸/mm','产气量/g','重量/g','绝缘阻值/mΩ']
      this.editObjList.forEach((item, index) => {
        if (['voltage', 'innerres'].includes(item) || (Array.isArray(storageDaySetMap[item]) && storageDaySetMap[item].length > 0)) {
          const tableName = nameList[index].split('/')[0] + '数据'
          let tableObj = {rowKey: 'rowKey', tableName: tableName}

          let tableColumns = [
            {
              title: '累积天数/Day',
              align: "center",
              width: 100,
              dataIndex: 'day',
            },
            {
              title: '出箱后/中检后',
              align: "center",
              width: 100,
              dataIndex: 'beforeOrAfterBox',
            },
            {
              title: '绝对时间',
              align: "center",
              width: 100,
              dataIndex: 'absoluteTime',
            },
            {
              title: nameList[index],
              align: "center",
              children: [],
            }
          ]

          if (item === 'height') {
            heightColNameList.forEach(sizeType => {
              // 含有":trim"字样时图例名称去除后缀
              let sizeTypeName = sizeType
              if (sizeType.includes(':trim') && sizeType.lastIndexOf('-') !== -1) {
                sizeTypeName = sizeType.substr(0, sizeType.lastIndexOf('-'))
              }
              tableColumns[3].children.push(
                {
                  title: sizeTypeName,
                  align: "center",
                  children: []
                }
              )
            })
          } else if (item === 'weight') {
            tableColumns.push(
              {
                title: "失重率/%",
                align: "center",
                children: [],
              }
            )
          }

          sampleCodeList.forEach(sampleCode => {
            let sampleInfo = sampleInfoMap[sampleCode]
            if (item === 'height') {
              for (let i = 0; i < heightColNameList.length; i++) {
                tableColumns[3].children[i].children.push(
                    this.getCalendarOffChilObj(sampleCode, sampleInfo.batteryCode, sampleInfo.testStatus, sampleInfo.statusTip, heightColNameList[i].replace(':trim', ''))
                )
              }
            } else {
              tableColumns[3].children.push(
                  this.getCalendarOffChilObj(sampleCode, sampleInfo.batteryCode, sampleInfo.testStatus, sampleInfo.statusTip, item)
              )
              if (item === 'weight') {
                tableColumns[4].children.push(
                    this.getCalendarOffChilObj(sampleCode, sampleInfo.batteryCode, sampleInfo.testStatus, sampleInfo.statusTip, 'weightLossRate')
                )
              }
            }
          })

          tableObj.tableColumns = tableColumns
          tableObj.dataList = ['voltage', 'innerres'].includes(item) ? resultJsonObj.rptTableList : resultJsonObj.rptTableList.filter(rptObj => {
            return storageDaySetMap[item].includes(rptObj.day) && (rptObj.day == 0 || rptObj.beforeOrAfterBox != '中检后')
          })

          tableObjList.push(tableObj)
        }
      })

      return tableObjList
    },
    getCalendarOffChilObj(sampleCode, batteryCode, testStatus, tips, dataIndex) {
      // 前端表头状态转换
      if (!tips) {
        switch (testStatus) {
          case 'earlyEnd': tips = '状态正常-提前结束'; break;
          case 'batteryDisassembly': tips = '状态正常-电池拆解'; break;
          case 'pressureDrop': tips = '掉压失效-终止测试'; break;
          case 'abnormalHot': tips = '异常发热-终止测试'; break;
          case 'openShellAndLeak': tips = '开壳漏液-终止测试'; break;
          case 'shellRust': tips = '壳体生锈-终止测试'; break;
          case 'operationError': tips = '作业错误-终止测试'; break;
          case 'thermalRunaway': tips = '热失控-终止测试'; break;
          case 'acrException': tips = '内阻异常-终止测试'; break;
          default: tips = ''; break;
        }
      }
      if (["earlyEnd", "batteryDisassembly", "pressureDrop", "abnormalHot", "openShellAndLeak", "shellRust", "operationError", "thermalRunaway", "acrException"].includes(testStatus)) {
        testStatus = "Stop"
      } else if (testStatus === "ongoing") {
        testStatus = "Ongoing"
      }

      batteryCode = batteryCode || sampleCode

      let result = {
        title: batteryCode,
        align: "center",
        width: "100px",
        children: [
          {
            title: <a-tooltip title={tips}>{testStatus}</a-tooltip>,
            align: "center",
            width: "100px",
            customRender: (text, record) => {
              return record.primaryObjectMap[sampleCode][dataIndex]
            }
          }
        ]
      }

      if (sampleCode !== batteryCode) {
        result = {
          title: sampleCode,
          align: "center",
          width: "100px",
          children: [
            result
          ]
        }
      }

      return result
    },

    handleDownload(optionIndex) {
      this.optionIndex = optionIndex
      exportOptionMergeReport({
        id: this.id,
        optionIndex: this.optionIndex
      }).then(res => {
        if (res.data.size > 0) {
          const resultJsonObj = this.templateResultObj.resultJsonObjList[optionIndex]
          const fileName = resultJsonObj.fileName ? resultJsonObj.fileName + '.xlsx' : this.templateResultObj.projectName + '.xlsx'
          downloadfile1(res, fileName)
        } else {
          this.$message.warning("暂无数据！")
        }
      }).catch(error => {
        this.$message.warning("导出失败！")
      })
    },

    reExport(rebuildFlag) {
      this.$store.commit('setTaskFilterData', this.data);
      if (rebuildFlag) {
        this.$router.push('/optionMergeBuild');
      } else {
        this.$router.push('/optionMergeBuild?id=' + this.data.id);
      }
    },
  },
}
</script>

<style lang="less" scoped>
.mt10 {
  margin-top: 10px;
}

.mr5 {
  margin-right: 5px;
}

.mr10 {
  margin-right: 10px;
}

.all-wrapper {
  margin: 0 0 0 -40px;
  padding: 10px;
  background-color: #f0f2f5;
}

.flex-sb-center-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 标题 */
.head_title {
  color: #333;
  padding-bottom: 10px;
  font-size: 20px;
  font-weight: 600;
}

.head_title::before {
  width: 8px;
  background: #1890ff;
  margin-right: 8px;
  content: "\00a0"; //填充空格
}

.head_title .subheading {
  font-size: 14px;
  font-weight: 400;
}

.left-content {
  width: calc(595px + 20px);
  margin-right: 10px;
}

.right-content {
  width: calc(100% - 595px - 20px - 10px);
}

.block {
  height: fit-content;
  padding: 10px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
  position: relative;
}

.tab-content {
  display: flex;
  height: calc(100vh - 60px);
  padding: 10px 10px 0 10px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
  overflow: auto; /* 滚动条 */
  &::-webkit-scrollbar {
    height: 12px !important;
  }
}

.option-title {
  flex-grow: 1;
  text-align: center;
}

.reveal-text-opacity {
  /* font-size: 14px; */
  font-family: 'Times New Roman';
  opacity: 0;
  padding: 0;
  margin: 0;
  line-height: 1;
}
</style>