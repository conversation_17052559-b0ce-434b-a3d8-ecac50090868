<template>
  <div class="tree-table-container">
    <table>
      <thead>
        <tr>
          <th>名称</th>
          <th>类型</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(node, index) in treeData" :key="node.id" @dragstart="handleDragStart" :class="{ 'dragging': index === draggingIndex }">
          <td>{{ node.name }}</td>
          <td>{{ node.type }}</td>
          <td>
            <button @click="addChild(node)">添加子节点</button>
            <button @click="deleteNode(node)">删除</button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>
<script>
export default {
  data() {
    return {
      treeData: [
        { id: 1, name: '节点1', type: '类型1', children: [] },
        { id: 2, name: '节点2', type: '类型2', children: [] },
        { id: 3, name: '节点3', type: '类型3', children: [] },
      ],
      draggingIndex: -1, // 当前拖拽的节点索引
      dropIndex: -1, // 放置的位置索引
    };
  },
  methods: {
    // 添加子节点
    addChild(node) {
        console.log(node)
      node.children.push({ id: Date.now(), name: '新节点', type: '类型', children: [] });
      this.treeData.push({ id: Date.now(), name: '新节点', type: '类型', children: [] })
    },
    // 删除节点
    deleteNode(node) {
      const index = this.treeData.indexOf(node);
      if (index !== -1) {
        this.treeData.splice(index, 1);
      }
    },
    // 开始拖拽
    handleDragStart(event, index) {
      event.dataTransfer.effectAllowed = 'move'; // 设置拖拽效果
      this.draggingIndex = index;
    },
    // 拖拽结束
    handleDragEnd() {
      this.draggingIndex = -1;
      this.dropIndex = -1;
    },
    // 拖拽进入目标位置
    handleDragEnter(event, index) {
      event.preventDefault();
      this.dropIndex = index;
    },
    // 拖拽离开目标位置
    handleDragLeave() {
      this.dropIndex = -1;
    },
    // 放置节点
    handleDrop(event) {
      event.preventDefault();
      const draggingNode = this.treeData[this.draggingIndex];
      this.treeData.splice(this.draggingIndex, 1); // 移除拖拽的节点
      this.treeData.splice(this.dropIndex, 0, draggingNode); // 插入到目标位置
      this.dropIndex = -1;
    },
  },
};
</script>
<style>
.tree-table-container {
  max-width: 800px;
  margin: 0 auto;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th,
td {
  padding: 8px;
  border: 1px solid #ddd;
}

th {
  background-color: #f2f2f2;
}

tr.dragging {
  background-color: #eee;
}

button {
  margin-right: 8px;
}
</style>