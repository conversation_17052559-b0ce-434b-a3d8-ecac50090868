<template>
	<div class="container">
		<!-- 主标题 start -->
		<div class="head_title" style="margin-bottom: 8px">测试数据查询</div>
    <div style="position: relative">
      <pbiTabs :tabsList="laboratoryList" :activeKey="queryParam.laboratoryid" @clickTab="handleTabsChange"></pbiTabs>
      <img style="width: 150px;height: 25px;position: absolute;top: 0;right: 0;cursor: pointer" @click='openCalendar'
           src="../../../../assets/images/btn10.png"></img>
    </div>

    <tableIndex
      ref="pbiTableIndex"
      :pageLevel='1'
      :tableTotal='tableTotal'
      :pageTitleShow=false
      :loading='loading'
      :otherHeight="parseInt(126)"
      @paginationChange="handlePageChange"
      @paginationSizeChange="handlePageChange"
    >
      <template #search>
        <pbiSearchContainer>
          <pbiSearchItem :span="3" label='委托单号' >
            <a-input class="input-short" v-model="queryParam.folderno"  @keyup.enter="loadData()"
                     @change="loadData()" />
          </pbiSearchItem>
          <pbiSearchItem :span="3" label='委托人' >
            <a-input class="input-short" v-model="queryParam.createdbyname" @keyup.enter="loadData()"
                     @change="loadData()" />
          </pbiSearchItem>
          <pbiSearchItem :span="3" label='主题' >
            <a-input class="input-short" v-model="queryParam.theme"  @keyup.enter="loadData()"
                     @change="loadData()" />
          </pbiSearchItem>
          <pbiSearchItem :span="3" label='测试型号'>
            <a-input class="input-short" v-model="queryParam.testproducttype" @keyup.enter="loadData()"
                     @change="loadData()"  />
          </pbiSearchItem>
          <pbiSearchItem :span="3" label='测试类别' v-if="queryParam.laboratoryid !== 'HZ_YJ_DL_JM'">
            <a-cascader style="width: 100%" placeholder="" v-model="testCategoryParam" :options="testCategoryOptions" @change="searchByTestCategory()"  optionFilterProp="children" allowClear/>
          </pbiSearchItem>
          <pbiSearchItem :span="4" label='委托单状态' >
            <a-select style="width: 100%" placeholder="" v-model="queryParam.status"  @change="loadData()"  optionFilterProp="children" allowClear>
              <a-select-option :value="item.code" v-for="item in statusDict">
                {{item.value}}
              </a-select-option>>
            </a-select>
          </pbiSearchItem>


          <pbiSearchItem :span="queryParam.laboratoryid !== 'HZ_YJ_DL_JM' ? 5 : 8" type='btn' class="search-container">
            <div class="secondary-btn">
              <a-button @click="loadData()" class="mr12" type="primary">
              查询
              </a-button>
              <a-button type="primary" class="mr12" @click="jumpToLims()">委托申请</a-button>
              <a-button @click="reset()" class="mr10" >
                重置
              </a-button>
            </div>
           <!-- <div class='toggle-btn'>
              <a-button size='small' type='link' @click='showOrHide'>
                {{ isShowAllSearch ? '收起' : '展开' }}
                <span v-if='isShowAllSearch'>
										<a-icon type='double-left'/>
									</span>
                <span v-else>
										<a-icon type='double-right'/>
									</span>
              </a-button>
            </div>-->

          </pbiSearchItem>

        </pbiSearchContainer>
      </template>


      <template #table>
        <ag-grid-vue :style="{height:tableHeight}"
                     class='table ag-theme-balham'
                     :tooltipShowDelay="0"
                     :columnDefs="columns"
                     :rowData='rowData'
                     :gridOptions="gridOptions"
                     @grid-ready="onGridReady"
                     :defaultColDef='defaultColDef'>
        </ag-grid-vue>
      </template>
    </tableIndex>


    <!-- 主标题 end -->

		<!-- 内容 start -->
			<!-- 内容 end -->

		<!-- 弹窗 start -->
		<InquiryModal v-if="isShowModal" :modalData="modalData" @cancel="handleModalCancel"></InquiryModal>

    <test-data ref="testData"/>
		<!-- 弹窗 end -->
	</div>
</template>

<script>
	import InquiryModal from "./components/inquiryModal"
  import {shenghongDataExportTaskPageList, tLimsFolderListPage} from "@/api/modular/system/limsManager";
	import { getMD5Str } from "@/api/modular/system/testProgressManager";
	import { mapActions, mapGetters } from 'vuex'
	import { sysDictTypeDropDown } from "@/api/modular/system/dictManage";
  import pbiTabs from "@/components/pageTool/components/pbiTabs.vue";
  import { Cascader } from 'ant-design-vue';
  import testData from "@/views/system/lims/folder/testData.vue";
	export default {
		name: "OrderInquiry",
		data() {
			return {
        isShowAllSearch:false,
        tableTotal:0,
        rowData:[],

        pageNo: 1,
        pageSize: 20,
        gridApi: null,
        columnApi: null,
        gridOptions: {
          onSelectionChanged: this.onSelectionChanged,
          suppressCellSelection: false,
          suppressDragLeaveHidesColumns: true
        },
        defaultColDef: {
          filter: false,
          floatingFilter: false,
          editable: false,
        },
        tableHeight: document.body.clientHeight - 237 +'px' ,

        // 40 - 20 - 30 - 10 - 20 - 28 - 10 - 39 - 32 - 20
				isShowModal: false,
        laboratoryList:[
          {value:null,label:"全部"},
          {value:"HZ_YJ_DL_JM",label:"精密实验室"},
          {value:"HZ_YJ_DL_AQ",label:"第四实验室"},
          {value:"JM_YJ_DL_CS",label:"第六实验室(JM)"},
          {value:"HZ_YJ_DL_CS",label:"第六实验室(HZ)"},
          {value:"HZ_YJ_DL_CS2",label:"委外测试"},
          {value:"HZ_YJ_DL_CS3",label:"产品认证"}
        ],
        testCategoryOptions: [],
        allTestCategoryOptions: [
          {
            value: '研发测试',
            label: '研发测试',
            children: [
              {
                value: '平台项目',
                label: '平台项目',
              },
              {
                value: '课题测试',
                label: '课题测试',
              },
              {
                value: '非立项',
                label: '非立项',
              },
              {
                value: '竞品测试',
                label: '竞品测试',
              },
            ],
          },
          {
            value: '产品验证测试',
            label: '产品验证测试',
            children: [
              {
                value: '设计验证',
                label: '设计验证',
              },
              {
                value: 'A样',
                label: 'A样',
              },
              {
                value: 'B0样',
                label: 'B0样',
              },
              {
                value: '客户需求验证',
                label: '客户需求验证',
              },
            ],
          },
          {
            value: '产品鉴定测试',
            label: '产品鉴定测试',
            children: [
              {
                value: '里程碑A样',
                label: '里程碑A样',
              },
              {
                value: '里程碑B样',
                label: '里程碑B样',
              },
              {
                value: 'C0样',
                label: 'C0样',
              },
              {
                value: '二元化',
                label: '二元化',
              },
              {
                value: '变更',
                label: '变更',
              },
            ],
          },
          {
            value: '产业化测试',
            label: '产业化测试',
            children: [
              {
                value: '鉴定测试',
                label: '鉴定测试',
              },
              {
                value: '一致性测试',
                label: '一致性测试',
              },
              {
                value: '逐批检验测试',
                label: '逐批检验测试',
              },
            ],
          },
          {
            value: '应用边界测试',
            label: '应用边界测试',
            children: [
              {
                value: '里程碑A样',
                label: '里程碑A样',
              },
              {
                value: '里程碑B样',
                label: '里程碑B样',
              },
              {
                value: 'C0样',
                label: 'C0样',
              },
              {
                value: '二元化',
                label: '二元化',
              },
              {
                value: '变更',
                label: '变更',
              },
            ],
          },
          {
            value: '客户目击测试',
            label: '客户目击测试',
          },
          {
            value: '委外测试',
            label: '委外测试',
          },
          {
            value: '委外认证',
            label: '委外认证',
          },
        ],
        aqTestCategoryOptions: [
          {
            value: '研发测试',
            label: '研发测试',
            children: [
              {
                value: '平台项目',
                label: '平台项目',
              },
              {
                value: '课题测试',
                label: '课题测试',
              },
            ],
          },
          {
            value: '产品验证测试',
            label: '产品验证测试',
            children: [
              {
                value: '设计验证',
                label: '设计验证',
              },
              {
                value: 'A样',
                label: 'A样',
              },
              {
                value: 'B0样',
                label: 'B0样',
              },
              {
                value: '客户需求验证',
                label: '客户需求验证',
              },
            ],
          },
          {
            value: '产品鉴定测试',
            label: '产品鉴定测试',
            children: [
              {
                value: '里程碑A样',
                label: '里程碑A样',
              },
              {
                value: '里程碑B样',
                label: '里程碑B样',
              },
              {
                value: 'C0样',
                label: 'C0样',
              },
              {
                value: '二元化',
                label: '二元化',
              },
              {
                value: '变更',
                label: '变更',
              },
            ],
          },
          {
            value: '应用边界测试',
            label: '应用边界测试',
            children: [
              {
                value: '里程碑A样',
                label: '里程碑A样',
              },
              {
                value: '里程碑B样',
                label: '里程碑B样',
              },
              {
                value: 'C0样',
                label: 'C0样',
              },
              {
                value: '二元化',
                label: '二元化',
              },
              {
                value: '变更',
                label: '变更',
              },
            ],
          },
          {
            value: '客户目击测试',
            label: '客户目击测试',
          },
        ],
        hzTestCategoryOptions: [
          {
            value: '研发测试',
            label: '研发测试',
            children: [
              {
                value: '平台项目',
                label: '平台项目',
              },
              {
                value: '课题测试',
                label: '课题测试',
              },
            ],
          },
          {
            value: '产品验证测试',
            label: '产品验证测试',
            children: [
              {
                value: '设计验证',
                label: '设计验证',
              },
              {
                value: 'A样',
                label: 'A样',
              },
              {
                value: 'B0样',
                label: 'B0样',
              },
              {
                value: '客户需求验证',
                label: '客户需求验证',
              },
            ],
          },
          {
            value: '产品鉴定测试',
            label: '产品鉴定测试',
            children: [
              {
                value: '里程碑A样',
                label: '里程碑A样',
              },
              {
                value: '里程碑B样',
                label: '里程碑B样',
              },
              {
                value: 'C0样',
                label: 'C0样',
              },
              {
                value: '二元化',
                label: '二元化',
              },
              {
                value: '变更',
                label: '变更',
              },
            ],
          },
          {
            value: '应用边界测试',
            label: '应用边界测试',
            children: [
              {
                value: '里程碑A样',
                label: '里程碑A样',
              },
              {
                value: '里程碑B样',
                label: '里程碑B样',
              },
              {
                value: 'C0样',
                label: 'C0样',
              },
              {
                value: '二元化',
                label: '二元化',
              },
              {
                value: '变更',
                label: '变更',
              },
            ],
          },
          {
            value: '委外测试',
            label: '委外测试',
          },
          {
            value: '委外认证',
            label: '委外认证',
          },
        ],
        hbTestCategoryOptions: [
          {
            value: '研发测试',
            label: '研发测试',
            children: [
              {
                value: '平台项目',
                label: '平台项目',
              },
              {
                value: '课题测试',
                label: '课题测试',
              },
              {
                value: '非立项',
                label: '非立项',
              },
              {
                value: '竞品测试',
                label: '竞品测试',
              },
            ],
          },
          {
            value: '产品验证测试',
            label: '产品验证测试',
            children: [
              {
                value: '设计验证',
                label: '设计验证',
              },
              {
                value: '客户需求验证',
                label: '客户需求验证',
              },
            ],
          },
          {
            value: '产品鉴定测试',
            label: '产品鉴定测试',
            children: [
              {
                value: '里程碑A样',
                label: '里程碑A样',
              },
              {
                value: '里程碑B样',
                label: '里程碑B样',
              },
              {
                value: 'C0样',
                label: 'C0样',
              },
              {
                value: '二元化',
                label: '二元化',
              },
              {
                value: '变更',
                label: '变更',
              },
            ],
          },
          {
            value: '产业化测试',
            label: '产业化测试',
            children: [
              {
                value: '鉴定测试',
                label: '鉴定测试',
              },
              {
                value: '一致性测试',
                label: '一致性测试',
              },
              {
                value: '逐批检验测试',
                label: '逐批检验测试',
              },
            ],
          },
          {
            value: '应用边界测试',
            label: '应用边界测试',
            children: [
              {
                value: '里程碑A样',
                label: '里程碑A样',
              },
              {
                value: '里程碑B样',
                label: '里程碑B样',
              },
              {
                value: 'C0样',
                label: 'C0样',
              },
              {
                value: '二元化',
                label: '二元化',
              },
              {
                value: '变更',
                label: '变更',
              },
            ],
          },
          {
            value: '委外测试',
            label: '委外测试',
          },
          {
            value: '委外认证',
            label: '委外认证',
          },
        ],
        testCategoryParam:[],
        laboratoryId:'0',
        loading: false,
				queryParam: {},
				searchParam: {},
				modalData: {},
				statusDict: [],
        columns: [],
        finalColumns: [
					{
						headerName: "序号",
						field: "index",
						width: 60,
            cellRenderer: function (params) {
              return parseInt(params.node.id) + 1
            }
					},
					{
						headerName: "委托单号",
						field: "folderno",
            width: 110,
            cellRenderer: 'folderNo',
            cellRendererParams: {onHandleNo: this.handleNo},
					},{
						headerName: "测试数据",
            width: 110,
            cellRenderer: 'detailData',
            cellRendererParams: {openData: this.openData},
					},
					{
						headerName: "委托单状态",
						field: "status",
            cellRenderer: 'status',
            width: 110,
            cellRendererParams: {onStatusFilter: this.statusFilter},
					},
					{
						headerName: "委托人",
						field: "createdbyname",
            width: 90,
					},
					{
						headerName: "委托部门",
						field: "createdbyorgname",
            width: 150,
            tooltipValueGetter: (p) => p.value,
					},
					{
						headerName: "主题",
						field: "theme",
            minWidth: 200,
            flex: 1,
            cellStyle: () =>  {return {textAlign:'left'}},
            tooltipValueGetter: (p) => p.value,
					},
					{
						headerName: "检测实验室",
						field: "laboratory",
            minWidth: 120,
            flex: 1,
            tooltipValueGetter: (p) => p.value,
					},
					{
						headerName: "产品名称",
						field: "producttype",
            width: 100,
					},
					{
						headerName: "测试型号",
						field: "testproducttype",
            width: 100,
					},
          {
            headerName: "测试类别",
            width: 200,
            cellRenderer: function (params) {
              return (params.data.testtype ? params.data.testtype : "") + ((params.data.testpurposetype && params.data.testpurposetype !== '/') ? "/" + params.data.testpurposetype : "")
            }
          },
          {
            headerName: "送检总数",
            field: "samplenumber",
            width: 100,
          }
				],
        jmDlFinalColumns: [
          {
            headerName: "序号",
            field: "index",
            width: 60,
            cellRenderer: function (params) {
              return parseInt(params.node.id) + 1
            }
          },
          {
            headerName: "委托单号",
            field: "folderno",
            width: 110,
            cellRenderer: 'folderNo',
            cellRendererParams: {onHandleNo: this.handleNo},
          },{
            headerName: "测试数据",
            width: 110,
            cellRenderer: 'detailData',
            cellRendererParams: {openData: this.openData},
          },
          {
            headerName: "委托单状态",
            field: "status",
            cellRenderer: 'status',
            width: 110,
            cellRendererParams: {onStatusFilter: this.statusFilter},
          },
          {
            headerName: "委托人",
            field: "createdbyname",
            width: 90,
          },
          {
            headerName: "委托部门",
            field: "createdbyorgname",
            width: 150,
            tooltipValueGetter: (p) => p.value,
          },
          {
            headerName: "主题",
            field: "theme",
            width: 200,
            flex: 2,
            cellStyle: () =>  {return {textAlign:'left'}},
            tooltipValueGetter: (p) => p.value,
          },
          {
            headerName: "检测实验室",
            field: "laboratory",
            width: 120,
            flex: 1,
            tooltipValueGetter: (p) => p.value,
          },
          {
            headerName: "产品名称",
            field: "producttype",
            width: 100,
          },
          {
            headerName: "测试型号",
            field: "testproducttype",
            width: 100,
          },
          {
            headerName: "测试类别",
            width: 200,
            cellRenderer: function (params) {
              return (params.data.testtype ? params.data.testtype : "") + ((params.data.testpurposetype && params.data.testpurposetype !== '/') ? "/" + params.data.testpurposetype : "")
            }
          },{
            headerName: "测试目的",
            field: "testpurpose",
            width: 100,
          },{
            headerName: "生产线体",
            field: "prodline",
            width: 100,
          },{
            headerName: "送检总数",
            field: "samplenumber",
            width: 100,
          }
        ],
				// tableData: []
			}
		},
		components: {
      testData,
      pbiTabs,
			InquiryModal,
      ACascader: Cascader,
      folderNo:{
        template : `<div><span class="blue hand" style="margin-right: 12px" @click="params.onHandleNo(params.data)">{{ params.value }}</span>
                    </div>`
      },
      detailData:{
        template : `<div><span class="blue hand" @click="params.openData(params.data.id)">测试数据</span>
                    </div>`
      },
      status:{
        template : '<span>{{ params.onStatusFilter(params.value) }}</span>'
      }
		},
		computed: {
			...mapGetters(['userInfo'])
		},
		watch: {},
		created() {
      this.columns = this.finalColumns
      if (this.queryParam.laboratoryid === 'JM_YJ_DL_CS') {
        this.columns = this.jmDlFinalColumns;
      }
      this.testCategoryOptions = this.allTestCategoryOptions
			sysDictTypeDropDown({ code: 'folder_status' }).then((res) => {
				this.statusDict = res.data
			}).finally(() => {
        switch(new URLSearchParams(window.location.search).get('lab')){
					case 'HZ_YJ_DL_AQ':
            this.handleTabsChange('HZ_YJ_DL_AQ')
						break;
					case 'HZ_YJ_DL_CS':
            this.handleTabsChange('HZ_YJ_DL_CS')
						break;
					case 'JM_YJ_DL_CS':
            this.handleTabsChange('JM_YJ_DL_CS')
						break;
          default:
            this.loadData()
        }
      })


		},
		mounted() {

      this.tableHeight =  document.body.clientHeight - 225 - (this.isShowAllSearch?40:0) +'px'

		},
		methods: {
      openData(folderId) {
        this.$refs.testData.query(folderId)
      },
      showOrHide(){
        this.isShowAllSearch = !this.isShowAllSearch
        this.tableHeight =  document.body.clientHeight - 225 - (this.isShowAllSearch?40:0) +'px'
      },
      openCalendar(){
        window.open("/testProgressIndex", "_blank")
      },
      onGridReady(params) {
        this.gridApi = params.api;
        this.columnApi = params.columnApi;
        // params.api.sizeColumnsToFit();
      },
      searchByTestCategory() {
        this.queryParam.testtype = this.testCategoryParam[0]
        this.queryParam.testpurposetype = this.testCategoryParam[1]
        this.loadData()
      },
      reset(){
        this.queryParam = {}
        this.loadData()
      },
      loadData() {
        this.loading = true
        return tLimsFolderListPage({
          ...{
            pageNo: this.pageNo,
            pageSize: this.pageSize
          }, ...this.queryParam
        }).then((res) => {
          if (res.success) {
            this.rowData = res.data.rows
            this.tableTotal = res.data.totalRows

          }
        }).finally(() => {
          if(this.rowData.length == 0 && this.pageNo > 1){
            // this.pageNo -= 1
            this.pageNo = Math.max(1, Math.ceil(this.tableTotal / this.pageSize))
            this.$refs.pbiTableIndex.$refs.pbiPagination.handleWithoutChange(this.pageNo,this.pageSize)
            this.loadData()
          }
          this.loading = false
        })
      },
      handlePageChange(value) {
        let {current, pageSize} = value
        this.pageNo = current
        this.pageSize = pageSize
        this.loadData()

      },
      handleTabsChange(value) {
        this.queryParam.laboratoryid = value
        if (this.queryParam.laboratoryid === 'JM_YJ_DL_CS') {
          this.columns = this.jmDlFinalColumns;
        }
        // this.filterData = ''
        // this.getTodoTaskList()
        this.changeColumnAndSearchCondition()
        this.loadData()
      },
      changeColumnAndSearchCondition () {
        // 过滤表格列
        if (this.queryParam.laboratoryid === 'HZ_YJ_DL_JM') {
          this.columns = this.finalColumns.filter(item => item.headerName !== '测试类别')
          this.queryParam.testtype = null
          this.queryParam.testpurposetype = null
        } else {
          this.columns = this.finalColumns
          this.queryParam.testtype = this.testCategoryParam[0]
          this.queryParam.testpurposetype = this.testCategoryParam[1]
        }
        // 测试类别查询条件值
        if (this.queryParam.laboratoryid === 'HZ_YJ_DL_AQ') {
          this.testCategoryOptions = this.aqTestCategoryOptions
        } else if (this.queryParam.laboratoryid === 'HZ_YJ_DL_CS') {
          this.testCategoryOptions = this.hzTestCategoryOptions
        } else if (this.queryParam.laboratoryid === 'JM_YJ_DL_CS') {
          this.columns = this.jmDlFinalColumns;
          this.testCategoryOptions = this.hbTestCategoryOptions
        } else {
          this.testCategoryOptions = this.allTestCategoryOptions
        }
      },
			statusFilter(status) {
				const values = this.statusDict.filter(item => item.code == status)
				if (values.length > 0) {
					return values[0].value
				}
			},
			jumpToLims() {
				const timestamp = Date.now()
				let param = {
					userId: this.userInfo.account,
					oaAuthKey: '85f6e214d7babcda',
					timestamp: timestamp
				}
				getMD5Str(param).then((res) => {
					if (res.success) {
						var token = res.data
						window.open("http://lims.evebattery.com/module/index/workspaces?loginType=oasso&userId=" + this.userInfo.account +
							"&activeMenuId=***********&token=" + token + "&timestamp=" + timestamp)
					}
				})
			},
			handleNo(record) {
				this.modalData = record
				this.isShowModal = true
			},
			handleModalCancel() {
				this.isShowModal = false
			}
		}
	}
</script>

<style lang="less" scoped>
@import '/src/components/pageTool/style/pbiSearchItem.less';
	:root {
		--height: 372px;
	}

	.container {
		padding: 10px;
	}

	// 主标题
	.head_title {
		color: #333;
		font-size: 18px;
		font-weight: 600;
	}

	.head_title::before {
		width: 8px;
		background: #1890ff;
		margin-right: 8px;
		content: "\00a0"; //填充空格
	}

	// 内容
	.content-wrapper {
		height: calc(100vh - 40px - 30px - 20px - 10px);
		padding: 10px;
		//margin-top: 10px;
		background: #fff;
		border-radius:0 10px 10px 10px;
	}

	// 筛选框
	.filter-wrapper {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.filter-input {
		display: flex;
		align-items: center;
	}

	.filter-input span {
		font-size: 12px;
	}

	// 通用

	.mt10 {
		margin-top: 10px;
	}

	.mr5 {
		margin-right: 5px;
	}

	.mr10 {
		margin-right: 10px;
	}

	.mr20 {
		margin-right: 20px;
	}

	/deep/.ant-btn {
		height: 28px;
	}

	/deep/tr th {
    padding: 5px;
    font-size: 13px;
    color: rgba(0, 0, 0, .85);
    font-weight: 500;
	}

	/deep/tr td {
    padding: 4px;
    color: #333;
    font-size: 12px;
    font-weight: 400;
	}

	/deep/.ant-table-body {
		min-height: var(--height);
	}

	/deep/.ant-table-pagination.ant-pagination {
		margin: 10px 0 0;
	}

	/deep/.ant-pagination-prev,
	/deep/.ant-pagination-next,
	/deep/.ant-pagination-jump-prev,
	/deep/.ant-pagination-jump-next {
		min-width: 25px;
		height: 25px;
		line-height: 25px;
	}

	/deep/.ant-pagination-item {
		min-width: 25px;
		height: 25px;
		line-height: 25px;
	}

	/* *** */
	.filter-input-wrap{
		display: flex;
	}

  /deep/.searchItem .label {
    width:88px;
  }


	/deep/.filter-input .ant-input{
    font-size: 12px;
  }
  /deep/.filter-btn .ant-btn{
    padding: 0 8px;
    font-size: 12px;
  }
	/deep/ .table-wrapper .s-table-tool{
		padding-bottom: 0;
	}

  /deep/.searchItem .content{
    width: calc(100% - 50px);
  }


  /deep/.ant-input {
    padding: 0;
  }

  /deep/.searchItem .btn{
    margin-left: auto;
    width: calc(100% - 0px);
  }
</style>