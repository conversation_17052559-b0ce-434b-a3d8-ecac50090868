<template>
	<div class="product_width">
		<div class="table-page-search-wrapper">
			<a-form layout="inline">
				<a-row :gutter="48">
					<a-col :md="5" :sm="24">
						<a-form-item label="立项日期">
							<a-range-picker :placeholder="['开始日期', '结束日期']" size="small" @change="dateChange" />
						</a-form-item>
					</a-col>
					<a-col :md="5" :sm="24">
						<a-form-item label="产品类别">
							<treeselect
								:limit="1"
								@input="change"
								:max-height="200"
								placeholder="请选择产品类别"
								value-consists-of="BRANCH_PRIORITY"
								v-model="queryparam.cates"
								:multiple="true"
								:options="cate"
							/>
						</a-form-item>
					</a-col>
					<a-col :md="5" :sm="24">
						<a-form-item label="产品状态">
							<treeselect
								:limit="1"
								@input="change"
								:max-height="200"
								placeholder="请选择产品状态"
								value-consists-of="BRANCH_PRIORITY"
								v-model="queryparam.states"
								:multiple="true"
								:options="statuses"
							/>
						</a-form-item>
					</a-col>

					<a-col :md="3" :sm="24">
						<a-form-item label="">
							<a-input
								size="small"
								@keyup.enter.native="change"
								v-model="queryparam.keyword"
								placeholder="请输入产品名称"
							>
								<a-icon slot="suffix" type="search" style="color: rgba(0,0,0,.45)" />
							</a-input>
						</a-form-item>
					</a-col>

					<a-col :md="1" :sm="24" :style="{ float: 'right' }">
						<div class="table-page-search-submitButtons" :style="{ float: 'right' }">
							<a-button size="small" style="margin-left: 120px;" type="primary" @click="query">查询</a-button>
							<a-button size="small" style="margin-left: 20px;margin-top:6px" @click="resetquery">重置</a-button>
							<a-button size="small" type="primary" @click="$refs.addForm.add()" style="margin-left: 8px"
								>新建</a-button
							>
						</div>
					</a-col>
				</a-row>
			</a-form>
		</div>
		<div style="background: #f0f2f5;height:12px"></div>
		<a-table
			ref="table"
			size="small"
			:customRow="customRow"
			:rowKey="record => record.issueId + record.productCate"
			:columns="columns"
			:dataSource="loadData"
			class="components-table-demo-nested"
			:expandIconColumnIndex="14"
			:expandIconAsCell="false"
			:expandIcon="expandIcon"
			:loading="loading"
		>
			<span slot="fixedState" slot-scope="text, record">
				<span v-if="record.fixedState == 2">
					<a-icon :style="{ color: '#91cc75' }" type="check-circle" />
				</span>
				<span v-else></span>
			</span>

			<span slot="productCate" slot-scope="text, record">
				{{ record.productOrProject == 1 ? record.productCateParent + (text != "" ? "->" + text : "") : "" }}
			</span>
			<span slot="productProjectName" slot-scope="text, record">
				{{ record.productOrProject == 1 ? text : "" }}
			</span>
			<span slot="mstatus" slot-scope="text">{{ "product_stage_status" | dictType(text) }}</span>
			<span slot="state" slot-scope="text">{{ "product_state_status" | dictType(text) }}</span>

			<span slot="dept" slot-scope="text, record">{{
				record.departmentOptionList
					.filter(function(e) {
						return 0 == parseInt(e.pid)
					})
					.map(function(e) {
						return e.value
					})[0]
			}}</span>
			<span slot="deptchild" slot-scope="text, record">{{
				record.departmentOptionList
					.filter(function(e) {
						return 0 != parseInt(e.pid)
					})
					.map(function(e) {
						return e.value
					})[0]
			}}</span>

			<!-- 操作 -->
			<span slot="action" slot-scope="text,record">
				<a-icon v-if="record.productOrProject === '1'" class="icon" type="line-chart" @click.stop="handleIcon(record)" />
			</span>
		</a-table>
		<add-form ref="addForm" @ok="callDashboardInfo" />
	</div>
</template>

<script>
import { dashboardInfo } from "@/api/modular/system/dashboardManage"
import { getCatesTree } from "@/api/modular/system/report"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"
import addForm from "./create/addForm"
export default {
	components: {
		Treeselect,
		addForm
	},
	/*  props: {
        issueId: {
            type: Number,
            default: 0
        },
        projectdetail: {
            type: Object,
            default: {}
        }
    }, */
	data() {
		return {
			statuses: [
				{
					id: 0,
					label: "立项讨论"
				},
				{
					id: 1,
					label: "A/B样"
				},
				{
					id: 2,
					label: "C/D样"
				},
				{
					id: 3,
					label: "暂停开发"
				},
				{
					id: 4,
					label: "停产"
				},
				{
					id: 5,
					label: "SOP"
				}
			],
			queryparam: {
				cates: [],
				states: [],
				keyword: null,
				projectId: null
			},
			parentId: null,
			cateId: null,
			projectId: null,
			loading: true,
			columns: [
				{
					title: "序号",
					dataIndex: "no",
					align: "center",
					customRender: (text, record, index) => {
						if (record.productOrProject == 1) {
							return `${index + 1}`
						}
						return ""
					}
				},
				{
					title: "产品类别",
					align: "center",
					dataIndex: "productCate",
					scopedSlots: {
						customRender: "productCate"
					}
				},
				{
					title: "二级部门",
					align: "center",
					dataIndex: "dept",
					scopedSlots: {
						customRender: "dept"
					}
				},

				{
					title: "三级部门",
					align: "center",
					dataIndex: "deptchild",
					scopedSlots: {
						customRender: "deptchild"
					}
				},
				{
					title: "产品名称",
					align: "center",
					dataIndex: "productProjectName",
					scopedSlots: {
						customRender: "productProjectName"
					}
				},
				{
					title: "项目名称",
					align: "center",
					dataIndex: "projectName"
				},
				{
					title: "项目等级",
					align: "center",
					dataIndex: "productLevel",
					customRender: (text, record, index) => {
						return text + "级"
					}
				},
				{
					title: "客户",
					align: "center",
					dataIndex: "customer"
				},
				{
					title: "立项日期",
					align: "center",
					dataIndex: "initiationDate"
				},
				{
					title: "产品状态",
					align: "center",
					dataIndex: "ostate",
					scopedSlots: {
						customRender: "state"
					}
				},
				{
					title: "项目阶段",
					align: "center",
					dataIndex: "mstatus",
					scopedSlots: {
						customRender: "mstatus"
					}
				},
				{
					title: "PD",
					align: "center",
					dataIndex: "productManager"
				},
				{
					title: "RPM",
					align: "center",
					dataIndex: "researchProjectManager"
				},
				{
					title: "定点状态",
					align: "center",
					dataIndex: "fixedState",
					scopedSlots: {
						customRender: "fixedState"
					}
				},
				{
					title: "操作",
					align: "center",
					dataIndex: "action",
					scopedSlots: {
						customRender: "action"
					}
				}
			],
			loadData: [],
			totalData: [],
			cate: []
		}
	},
	methods: {
		customRow(row, index) {
			return {
				on: {
					click: () => {
						this.$router.push({
							path: "/project_overview",
							query: {
								issueId: row.issueId
							}
						})
					}
				}
			}
		},
		query() {
			if (this.parentId != null) {
				let index = this.queryparam["cates"].findIndex(e => e == this.parentId)
				this.queryparam["cates"].splice(index, 1)
				this.parentId = null
			}
			if (this.cateId != null) {
				let index = this.queryparam["cates"].findIndex(e => e == this.cateId)
				this.queryparam["cates"].splice(index, 1)
				this.cateId = null
			}

			if (this.projectId != null) {
				this.queryparam.projectId = null
				this.projectId = null
			}

			this.callFilter()
		},
		change() {
			this.callFilter()
		},
		resetquery() {
			this.queryparam = {}
			let filterData = JSON.parse(JSON.stringify(this.totalData))
			this.loadData = filterData
		},
		callGetTree() {
			// this.loading = true
			getCatesTree()
				.then(res => {
					if (res.result) {
						let cate = []
						for (const item of res.data) {
							let $item = {
								id: parseInt(item.value),
								label: item.title
							}
							if (item.children) {
								$item.children = []
								for (const _item of item.children) {
									$item.children.push({
										id: parseInt(_item.value),
										label: _item.title
									})
								}
							}
							cate.push($item)
						}
						this.cate = cate
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
					// this.loading = false
				})
				.catch(err => {
					// this.loading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		},
		callFilter() {
			let filterData = JSON.parse(JSON.stringify(this.totalData))

			if (this.queryparam.projectId) {
				if (this.queryparam["cates"].length > 0) {
					filterData = filterData.filter(
						item =>
							this.queryparam["cates"].indexOf(parseInt(item.cateId)) > -1 ||
							this.queryparam["cates"].indexOf(parseInt(item.catepid)) > -1
					)
				}
				filterData = filterData.filter(item => item.issueId == this.queryparam.projectId)
				this.loadData = filterData
				return
			}

			if (this.queryparam["cates"].length > 0) {
				filterData = filterData.filter(
					item =>
						this.queryparam["cates"].indexOf(parseInt(item.cateId)) > -1 ||
						this.queryparam["cates"].indexOf(parseInt(item.catepid)) > -1
				)
			}
			/* if (this.queryparam['customers'].length > 0) {
                filterData = filterData.filter(item => this.queryparam['customers'].indexOf(item.customer) > -1)
            }
            if (this.queryparam['levels'].length > 0) {
                filterData = filterData.filter(item => this.queryparam['levels'].indexOf(item.productLevel) > -1)
            } */
			if (this.queryparam["states"].length > 0) {
				filterData = filterData.filter(item => this.queryparam["states"].indexOf(parseInt(item.state)) > -1)
			}
			if (this.queryparam.keyword != null && this.queryparam.keyword != "") {
				filterData = filterData.filter(
					item => item.productProjectName.toLowerCase().indexOf(this.queryparam.keyword.toLowerCase()) > -1
				)
			}

			if (this.queryparam.startDate != null) {
				filterData = filterData.filter(
					item =>
						Date.parse(item.initiationDate) >= this.queryparam.startDate &&
						Date.parse(item.initiationDate) < this.queryparam.endDate
				)
			}

			this.loadData = filterData
		},
		dateChange(date, dateString) {
			if (dateString[0] != null && dateString[0] != "") {
				this.queryparam.startDate = Date.parse(dateString[0])
			} else {
				this.queryparam.startDate = null
			}
			if (dateString[1] != null && dateString[1] != "") {
				this.queryparam.endDate = Date.parse(dateString[1])
			} else {
				this.queryparam.endDate = null
			}
			this.callFilter()
		},
		expandIcon(props) {
			if (props.record.children && props.record.children.length > 0) {
				if (props.expanded) {
					return (
						<a-icon
							type="up"
							onClick={e => {
								props.onExpand(props.record, e)
							}}
						/>
					)
				} else {
					return (
						<a-icon
							type="down"
							onClick={e => {
								props.onExpand(props.record, e)
							}}
						/>
					)
				}
			} else {
				return <span style="padding-left: 21px;" />
			}
		},
		handleOk() {
			this.callDashboardInfo()
		},
		callDashboardInfo() {
			this.loading = true
			dashboardInfo({})
				.then(res => {
					if (res.success) {
						this.totalData = JSON.parse(JSON.stringify(res.data))
						this.loadData = res.data
						if (this.$route.query.parentId) {
							this.projectId = parseInt(this.$route.query.parentId)
							this.queryparam["cates"].push(parseInt(this.$route.query.parentId))
						}
						if (this.$route.query.cateId) {
							this.cateId = parseInt(this.$route.query.cateId)
							this.queryparam["cates"].push(parseInt(this.$route.query.cateId))
						}
						if (this.$route.query.projectId) {
							this.projectId = parseInt(this.$route.query.projectId)
							this.queryparam.projectId = parseInt(this.$route.query.projectId)
						}
						this.callFilter()
					} else {
						this.$message.error(res.message, 1)
					}
					this.loading = false
				})
				.catch(err => {
					this.loading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		},

		// 操作行--图标
		handleIcon(value) {
            console.log(value)
            this.$router.push({
				path: "/product_dashboard",
				query:{cateId:value.cateId,projectId:value.issueId}
			})
		}
	},
	created() {
		this.callDashboardInfo()
		this.callGetTree()
	}
}
</script>

<style lang="less" scoped>
@import "../productoptions/productoption.less";
/deep/.ant-table-tbody > tr:hover:not(.ant-table-expanded-row) > td,
.ant-table-row-hover,
.ant-table-row-hover > td {
	cursor: pointer;
}

.icon {
	color: #1890ff;
	margin-left: 4px;
}
/deep/.ant-table-small > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th{
	padding: 2px;
}
</style>
