<template>
  <div style="background-color: #FFFFFF;padding: 10px;height: 100%">
    <div class="float">
      <div><span class="numTitle">①</span><span class="title">测试项目选择</span></div>
      <a-button type="primary" size="small" style="float: left" @click="deleteData">删除</a-button>
      <a-table :columns="orderColumns" :data-source="orderData" bordered style="margin-top: 20px;"

               childrenColumnName="child"

               :row-selection="{ selectedRowKeys: projectSelectedRowKeys, selectedRows: projectSelectedRows,
                onChange: projectOnChange, columnWidth:30}"
               bordered
               :scroll="{x: false}"
               :rowKey="(record) => record.uuid"
               :pagination="false"
      >

        <template
          slot="celltestcode"
          slot-scope="text, record, index, columns"
        >
          <a @click="openStepData(record)" style="text-align: center" >{{text}}</a>

        </template>
        <template slot="footer" >
          <a-button @click="openTestOrder" style="width: 100%;"><a-icon type="plus"></a-icon></a-button>
        </template>

      </a-table>
      <a-modal title="测试项目选择" :width="1200" :height="600"
               :bodyStyle="{padding:0}"
               :visible="visible" style="padding: 0"
               :maskClosable="false"
               @cancel="handleCancel">

        <div style="float: left;position: relative;z-index: 1;padding-bottom: 5px;width: 65%">
          <a-row :gutter="[8,8]">

            <a-col :span="8">
              <a-form-item label="委托单号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                <a-input v-model="queryParam.folderno" @keyup.enter="$refs.table.refresh()"
                         @change="$refs.table.refresh()"/>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="主题" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                <a-input v-model="queryParam.theme" @keyup.enter="$refs.table.refresh()"
                         @change="$refs.table.refresh()"/>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="测试项名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                <a-input v-model="queryParam.testname" @keyup.enter="$refs.table.refresh()"
                         @change="$refs.table.refresh()"/>
              </a-form-item>
            </a-col>

          </a-row>
        </div>

        <s-table :columns="columns"
                 :data="loadData" bordered

                 :rowKey="(record1) => record1.uuid"
                 ref="table" :row-selection="{ selectedRowKeys: selectedRowKeys, selectedRows: selectedRows,
                 onSelect: onSelectChange, onSelectAll: onSelectAllChange, columnWidth:30}"
        >

          <template
            slot="celltestcode"
            slot-scope="text, record, index, columns"
          >

            <a @click="openStepData(record,true)" style="text-align: center">{{text}}</a>

          </template>



        </s-table>
        <template slot="footer">
          <a-button key="back" @click="handleCancel">
            关闭
          </a-button>
        </template>

      </a-modal>
      <a-modal title="测试数据选择" :width="1000" :height="300"
               :bodyStyle="{padding:0}"
               :visible="visibleFlow" style="padding: 0"
               :maskClosable="false"
               :centered="true"
               @cancel="handleCancelFlow">

        <a-table :columns="flowInfoColumns" :dataSource="flowInfoData" bordered :rowKey="(record) => record._id"
                 ref="table" :pagination="false"
        >


          <template
            slot="celltestcode"
            slot-scope="text, record, index, columns"
          >

            <a @click="openStepData(record)" style="text-align: center">{{text}}</a>

          </template>

          <template
            slot="action"
            slot-scope="text, record, index, columns"
          >

            <a @click="onSelectChangeFlow(record,'选中')" style="text-align: center">选中</a>
            <a-divider type="vertical" />
            <a @click="onSelectChangeFlow(record,'查看')" style="text-align: center">查看</a>

          </template>


        </a-table>
        <template slot="footer">
          <a-button key="back" @click="handleCancelFlow">
            关闭
          </a-button>
        </template>

      </a-modal>
    </div>
    <div class="float1">
      <div><span class="numTitle">②</span><span class="title">数据表选择</span></div>
      <a-select style="width: 120px;margin-top: 20px" v-model="dataType" @change="changeDataType">
        <a-select-option value="step">
          工步数据表
        </a-select-option>
        <a-select-option value="data">
          详细数据表
        </a-select-option>
        <a-select-option value="cyc">
          循环数据表
        </a-select-option>

      </a-select>
    </div>
    <div class="float">
      <div>
        <span class="numTitle">③</span>
        <span class="title">数据选择</span>
        <a-table :columns="filterColumns" :data-source="filterData" bordered style="margin-top: 20px;"
                 bordered
                 :scroll="{x: false}"
                 :pagination="false"
        >
          <template
            slot="action"
            slot-scope="text, record, index, columns"
          >
            <a @click="() => filterData.splice(index,1)" style="text-align: center">删除</a>

          </template>

          <template slot="footer" >
            <a-button @click="addFilterData" style="width: 100%;"><a-icon type="plus"></a-icon></a-button>
          </template>

          <template
            slot="key"
            slot-scope="text, record, index, columns"
          >
            <a-select style="width: 100%" v-model="record[columns.dataIndex]" :allow-clear="true" @change="(value,option) => handleDataSelectChange(value,option,columns.dataIndex)"
              :options="dataType == 'step'?stepOptions:dataType == 'data'?dataOptions:cycOptions"
            />
          </template>

          <template
            slot="value"
            slot-scope="text, record, index, columns"
          >
            <a-input  @paste="copyFromExcel($event,columns.dataIndex,index)" style="width: 100%;text-align: center" v-model="record[columns.dataIndex]"
            />
          </template>

        </a-table>
      </div>
    </div>
    <div class="float1">
      <div class="title"><span class="numTitle">④</span><span class="title">导出项选择</span></div>

      <div :style="{ borderBottom: '1px solid #E9E9E9' }">
        <a-checkbox :indeterminate="indeterminate" :checked="checkAll" @change="onCheckAllChange" style="text-align: center" id="checkAll">
          {{checkAll?'取消':'全选'}}
        </a-checkbox>
      </div>
      <br/>
      <a-checkbox-group v-if="dataType == 'step'" @change="onChange"
                        v-model="stepChecked"
                        :options="stepOptions"
      />
      <a-checkbox-group v-if="dataType == 'data'" @change="onChange"
                        v-model="dataChecked"

                        :options="dataOptions"
      />
      <a-checkbox-group v-if="dataType == 'cyc'" @change="onChange"
                        v-model="cycChecked"

                        :options="cycOptions"
      />
    </div>
    <div style="padding-left: 47%;">
      <a-button type="primary" @click="exportData">导出</a-button>
    </div>

    <a-modal title="导出" :width="800" :height="600"
             :bodyStyle="{padding:0}"
             :visible="visible1" :confirmLoading="confirmLoading" @ok="handleSubmit" style="padding: 0"
             @cancel="handleCancel1">

            <a-form :form="form">

              <a-row :gutter="24">
                <a-col :md="18" :sm="24">
                  <a-form-item label="任务名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                    <a-input placeholder="请输入任务名称"
                             v-decorator="['taskName', {rules: [{required: true, message: '请输入任务名称！'}]}]"/>
                  </a-form-item>
                </a-col>
              </a-row>



              <a-row :gutter="24">
                <a-col :md="18" :sm="24">

                  <a-form-item label="Excel格式" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">

                    <a-select v-decorator="['excelType', {rules: [{required: true, message: '请选择Excel格式！'}]}]" default-value="one"
                              style="width: 100%"  placeholder="请选择Excel格式">
                      <a-select-option value="one">
                        保存于同一个Sheet中
                      </a-select-option>
                      <a-select-option value="more">
                        保存于不同的Sheet中
                      </a-select-option>

                    </a-select>
                  </a-form-item>
                </a-col>


              </a-row>


            </a-form>

    </a-modal>
    <step-data ref="stepData"></step-data>
  </div>



</template>
<script>
  import {
     tLimsTestdataSchedulePageList,shenghongDataFilterExport
  } from '@/api/modular/system/limsManager'
  import {STable} from '@/components'
  import moment from "moment";

  import stepData from '../folder/stepData'

  import {mapGetters} from 'vuex';


  export default {

    components: {
      STable, stepData
    },
    data() {
      return {
        param:null,
        outFlowRecord:null,
        outQueryFlowRecord:null,
        inFlowActionName:null,
        visibleFlow:false,
        flowInfoColumns: [
          {
            title: '数据位置',
            align: 'center',
            width: 200,
            dataIndex: 'dataPath',

          },
          {
            title: '测试编码',
            dataIndex: 'barCode',
            align: 'center',
            width: 100,

          }, {
            title: '设备编号',
            width: 30,
            align: 'center',
            dataIndex: 'unitNum',
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '通道编号',
            width: 30,
            align: 'center',
            dataIndex: 'channelId',
            //scopedSlots: {customRender: 'updateText'},
          },  {
            title: '数据位置',
            width: 30,
            align: 'center',
            dataIndex: 'dataPath',
            ellipsis:true
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '开始时间',
            width: 30,
            align: 'center',
            dataIndex: 'startTime',
            customRender: (text, record, index) =>{
              if(null != text){
                return moment(text).format('YYYY-MM-DD')
              }
              return text
            }
            //
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '结束时间',
            width: 30,
            align: 'center',
            dataIndex: 'endTime',
            customRender: (text, record, index) =>{
              if(null != text){
                return moment(text).format('YYYY-MM-DD')
              }
              return text
            }
          },{
            title: '操作',
            width: 60,
            align: 'center',
            dataIndex: 'action',
            scopedSlots: {customRender: 'action'},
          }
        ],

        flowInfoData:[],
        checkedListStep: [],
        checkedListData: [],
        checkedListCyc: [],
        indeterminate: false,
        checkAll: false,
        stepChecked: [],
        dataChecked: [],
        cycChecked: [],
        filterData: [],
        loadDataList:["*************-4f9e-90fb-aae6608ba8eb"],
        loadData: parameter => {
          this.loadDataList = []
          return tLimsTestdataSchedulePageList(Object.assign(parameter, this.queryParam)).then(res => {
            let datas = res.data.rows
            for (let i = 0; i < datas.length; i++) {
              if(datas[i].children && datas[i].children.length > 0 ){
                //alert(datas[i].uuid)
                this.loadDataList.push(datas[i].uuid)
              }
            }
            return res.data
          }).then(res1 => {return res1})
        },
        dataType: 'step',
        orderData: [],
        visible: false,
        confirmLoading: false,
        visible1: false,
        height: 200,
        stepOptions: [
          {label: '工步序号', value: 'StepNum', key: 'stepNum'},
          {label: '工步号', value: 'StepId', key: 'stepId'},
          {label: '工步名', value: 'StepName', key: 'stepName'},
          {label: '工步时间', value: 'StepTime', key: 'stepTimeString'},

          {label: '循环号', value: 'CycleId', key: 'cycleId'},
          {label: '设备号', value: 'UnitNum', key: 'unitNum'},
          {label: '通道号', value: 'ChannelId', key: 'channelId'},
          {label: '电池条码', value: 'BarCode', key: 'barCode'},


          {label: '容量/Ah', value: 'Capacity', key: 'capacity'},
          {label: '能量/Wh', value: 'Energy', key: 'energy'},
          {label: '起始电压/V', value: 'BeginVoltage', key: 'beginVoltage'},
          {label: '终止电压/V', value: 'EndVoltage', key: 'endVoltage'},
          {label: '起始电流/A', value: 'StartCurrent', key: 'startCurrent'},
          {label: '终止电流/A', value: 'EndCurrent', key: 'endCurrent'},
          /*{label: '充电容量/Ah', value: 'ChargeCapacity', key: 'chargeCapacity'},
          {label: '放电容量/Ah', value: 'DisChargeCapacity', key: 'disChargeCapacity'},
          {label: '充电能量/Wh', value: 'ChargeEnergy', key: 'chargeEnergy'},
          {label: '放电能量/Wh', value: 'DisChargeEnergy', key: 'disChargeEnergy'},*/
          {label: '绝对时间', value: 'AbsoluteTime', key: 'absoluteTime'},
          /*{label: '恒流比', value: 'CCCapacityRate', key: 'cccapacityRate'},*/
          {label: '单体起始温度1', value: 'startTemp1', key: 'startTemp1'},
          {label: '单体结束温度1', value: 'endTemp1', key: 'endTemp1'},
          {label: '单体起始温度2', value: 'startTemp2', key: 'startTemp2'},
          {label: '单体结束温度2', value: 'endTemp2', key: 'endTemp2'},
          {label: '单体起始温度3', value: 'startTemp3', key: 'startTemp3'},
          {label: '单体结束温度3', value: 'endTemp3', key: 'endTemp3'},
          {label: '单体起始温度4', value: 'startTemp4', key: 'startTemp4'},
          {label: '单体结束温度4', value: 'endTemp4', key: 'endTemp4'},
          {label: '起始膨胀位移监测', value: 'StartExForce', key: 'startExForce'},
          {label: '结束膨胀位移监测', value: 'EndExForce', key: 'endExForce'},
          {label: '最大膨胀位移监测', value: 'MaxExForce', key: 'maxExForce'},
          {label: '最小膨胀位移监测', value: 'MinExForce', key: 'minExForce'},
          /*{label: '起始温度', value: 'BeginTemperature', key: 'beginTemperature'},
          {label: '终止温度', value: 'EndTemperature', key: 'endTemperature'},*/
          {label: '恒流比', value: 'CCCapacityRate', key: 'cccapacityRate'},
          {label: '起始膨胀力', value: 'StartPressure', key: 'startPressure'},
          {label: '结束膨胀力', value: 'EndPressure', key: 'endPressure'},
          {label: '最大膨胀力', value: 'MaxPressure', key: 'maxPressure'},
          {label: '最小膨胀力', value: 'MinPressure', key: 'minPressure'},


          /*{label: '开路电压/V', value: 'OpenVoltage', key: 'openVoltage'},*/

        ],
        dataOptions: [
          {label: '记录序号', value: 'RecordId', key: 'recordId'},
          {label: '设备号', value: 'UnitNum', key: 'unitNum'},
          {label: '通道号', value: 'ChannelId', key: 'channelId'},

          {label: '循环号', value: 'CycleId', key: 'cycleId'},
          {label: '工步序号', value: 'StepNum', key: 'stepNum'},
          {label: '工步号', value: 'StepId', key: 'stepId'},
          {label: '工步名', value: 'StepName', key: 'stepName'},
          {label: '电池条码', value: 'BarCode', key: 'barCode'},
          {label: '绝对时间', value: 'AbsoluteTime', key: 'absoluteTime'},
          {label: '记录时间', value: 'RecordTime', key: 'recordTime'},
          {label: '工步时间', value: 'StepTime', key: 'stepTime'},


          {label: '电压/V', value: 'Voltage', key: 'voltage'},
          {label: '电流/A', value: 'Current', key: 'current'},
          {label: '容量/Ah', value: 'Capacity', key: 'capacity'},
          {label: '能量/Wh', value: 'Energy', key: 'energy'},
          {label: '功率/W', value: 'ActivePower', key: 'activePower'},
          /*{label: '内阻/mΩ', value: 'Resistance', key: 'resistance'},*/
          {label: '充电容量/Ah', value: 'ChargeCapacity', key: 'chargeCapacity'},
          {label: '放电容量/Ah', value: 'DisChargeCapacity', key: 'disChargeCapacity'},
          {label: '充电能量/Wh', value: 'ChargeEnergy', key: 'chargeEnergy'},
          {label: '放电能量/Wh', value: 'DisChargeEnergy', key: 'disChargeEnergy'},
         /* {label: '设备温度/℃', value: 'Temperature', key: 'temperature'},*/
          {label: '总容量/Ah', value: 'TotalCapacity', key: 'totalCapacity'},
          {label: '单体温度1', value: 'auxTem1', key: 'auxTem1'},
          {label: '单体温度2', value: 'auxTem2', key: 'auxTem2'},
          {label: '单体温度3', value: 'auxTem3', key: 'auxTem3'},
          {label: '单体温度4', value: 'auxTem4', key: 'auxTem4'},
          {label: '温箱温度/℃', value: 'IncubatorTemp', key: 'incubatorTemp'},
          {label: '温箱湿度/%RH', value: 'IncubatorHum', key: 'incubatorHum'},
          {label: '单体电压串', value: 'AuxVoltageList', key: 'auxVoltageList'},


        ],
        cycOptions: [
          {label: '设备号', value: 'UnitNum', key: 'unitNum'},
          {label: '通道号', value: 'ChannelId', key: 'channelId'},
          {label: '循环号', value: 'CycleId', key: 'cycleId'},
          {label: '工步号', value: 'StepId', key: 'stepId'},
          {label: '电池条码', value: 'BarCode', key: 'barCode'},

          {label: '充电容量/Ah', value: 'ChargeCapacity', key: 'chargeCapacity'},
          {label: '放电容量/Ah', value: 'DisChargeCapacity', key: 'disChargeCapacity'},
          {label: '充电能量/Wh', value: 'ChargeEnergy', key: 'chargeEnergy'},
          {label: '放电能量/Wh', value: 'DisChargeEnergy', key: 'disChargeEnergy'},
          // {label: '放电中值电压/V', value: 'DischargeMeanVoltage', key: 'dischargeMeanVoltage'},
          {label: '充放电效率', value: 'ChargeCapacityRatio', key: 'chargeCapacityRatio'},

          {label: '内阻/mΩ', value: 'Resistance', key: 'resistance'},
          {label: '充电时间', value: 'ChargeTime', key: 'chargeTime'},
          {label: '放电时间', value: 'DischargeTime', key: 'dischargeTime'},
          // {label: '衰减比例/%', value: 'AttenuationRation', key: 'attenuationRation'},
          /*{label: '绝对时间', value: 'AbsoluteTime', value: 'absoluteTime'},*/
        ],
        show: false,
        labelCol: {

          sm: {
            span: 11
          }
        },
        wrapperCol: {

          sm: {
            span: 13
          }
        },
        queryParam: {},
        data: [],
        headData: [],
        allAddress: null,
        // 表头
        orderColumns: [
          {
            title: '序号',
            align: 'center',
            width: 30,
            customRender: (text, record, index) => index + 1
          },
          {
            title: '委托单号',
            dataIndex: 'folderno',
            align: 'center',
            width: 50
          }, {
            title: '测试项目编码',
            width: 70,
            align: 'center',
            dataIndex: 'testcode',
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '测试项名称',
            width: 60,
            align: 'center',
            dataIndex: 'testname',
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '样品编号',
            width: 50,
            align: 'center',
            dataIndex: 'orderno',
          }, {
            title: '测试编码',
            width: 50,
            align: 'center',
            dataIndex: 'celltestcode',
            scopedSlots: {customRender: 'celltestcode'},
          },
          {
            title: '数据位置',
            width: 30,
            align: 'center',
            dataIndex: 'dataPath',
            ellipsis:true
            //scopedSlots: {customRender: 'updateText'},
          },{
            title: '设备编号',
            width: 50,
            align: 'center',
            dataIndex: 'equiptcode',
          }, {
            title: '通道编号',
            width: 50,
            align: 'center',
            dataIndex: 'channelno',
          }
        ],
        filterColumns: [
          {
            title: '操作',
            align: 'center',
            width: 50,
            scopedSlots: {customRender: 'action'},
          },
          {
            title: '参数1',
            dataIndex: 'key1',
            align: 'center',
            width: 90,
            scopedSlots: {customRender: 'key'},
          }, {
            title: '值',
            width: 90,
            align: 'center',
            dataIndex: 'value1',
            scopedSlots: {customRender: 'value'},
          },
          {
            title: '参数2',
            dataIndex: 'key2',
            align: 'center',
            width: 90,
            scopedSlots: {customRender: 'key'},
          }, {
            title: '值',
            width: 90,
            align: 'center',
            dataIndex: 'value2',
            scopedSlots: {customRender: 'value'},
          },
          {
            title: '参数3',
            dataIndex: 'key3',
            align: 'center',
            width: 90,
            scopedSlots: {customRender: 'key'},
          }, {
            title: '值',
            width: 90,
            align: 'center',
            dataIndex: 'value3',
            scopedSlots: {customRender: 'value'},
          }
          /*,
          {
            title: '参数4',
            dataIndex: 'key4',
            align: 'center',
            width: 90,
            scopedSlots: {customRender: 'key'},
          }, {
            title: '值',
            width: 90,
            align: 'center',
            dataIndex: 'value4',
            scopedSlots: {customRender: 'value'},
          }*/
        ],
        columns: [
          {
            title: '序号',
            align: 'center',
            width: 50,
            customRender: (text, record, index) => index + 1
          },
          {
            title: '委托单号',
            dataIndex: 'folderno',
            align: 'center',
            width: 90
          },{
            title: '主题',
            dataIndex: 'theme',
            align: 'center',
            ellipsis:true,
            width: 90
          },
          {
            title: '样品编号',
            width: 90,
            align: 'center',
            dataIndex: 'orderno',
          },{
            title: '测试项目编码',
            width: 90,
            align: 'center',
            dataIndex: 'testcode',
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '测试项名称',
            width: 90,
            align: 'center',
            dataIndex: 'testname',
            //scopedSlots: {customRender: 'updateText'},
          },{
            title: '测试项目别名',
            width: 90,
            align: 'center',
            dataIndex: 'alias',
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '测试编码',
            width: 90,
            align: 'center',
            dataIndex: 'celltestcode',
            scopedSlots: {customRender: 'celltestcode'},
          },{
            title: '数据位置',
            width: 60,
            align: 'center',
            dataIndex: 'dataPath',
            ellipsis:true
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '开始时间',
            width: 90,
            align: 'center',
            dataIndex: 'startTime',
            customRender: (text, record, index) =>{
              if(null != text){
                return moment(text).format('YYYY-MM-DD')
              }
              return text
            }
            //
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '结束时间',
            width: 90,
            align: 'center',
            dataIndex: 'endTime',
            customRender: (text, record, index) =>{
              if(null != text){
                return moment(text).format('YYYY-MM-DD')
              }
              return text
            }
          }, {
            title: '设备编号',
            width: 60,
            align: 'center',
            dataIndex: 'equiptcode',
          }, {
            title: '通道编号',
            width: 60,
            align: 'center',
            dataIndex: 'channelno',
          }
        ],
        columns1: [
          {
            title: '序号',
            align: 'center',
            width: 50,
            customRender: (text, record, index) => index + 1
          },
          {
            title: '委托单号',
            dataIndex: 'folderno',
            align: 'center',
            width: 90
          },
          {
            title: '样品编号',
            width: 90,
            align: 'center',
            dataIndex: 'orderno',
          },{
            title: '测试项目编码',
            width: 90,
            align: 'center',
            dataIndex: 'testcode',
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '测试项名称',
            width: 90,
            align: 'center',
            dataIndex: 'testname',
            //scopedSlots: {customRender: 'updateText'},
          },{
            title: '测试项目别名',
            width: 90,
            align: 'center',
            dataIndex: 'alias',
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '测试编码',
            width: 90,
            align: 'center',
            dataIndex: 'celltestcode',
            scopedSlots: {customRender: 'celltestcode'},
          },{
            title: '开始时间',
            width: 90,
            align: 'center',
            dataIndex: 'startTime',
            customRender: (text, record, index) =>{
              if(null != text){
                return moment(text).format('YYYY-MM-DD')
              }
              return text
            }
            //
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '结束时间',
            width: 90,
            align: 'center',
            dataIndex: 'endTime',
            customRender: (text, record, index) =>{
              if(null != text){
                return moment(text).format('YYYY-MM-DD')
              }
              return text
            }
          }, {
            title: '设备编号',
            width: 60,
            align: 'center',
            dataIndex: 'equiptcode',
          }, {
            title: '通道编号',
            width: 60,
            align: 'center',
            dataIndex: 'channelno',
          }
        ],
        form: this.$form.createForm(this),
        selectedRowKeys: [],
        selectedRows: [],
        projectSelectedRowKeys: [],
        projectSelectedRows: [],
        height: '500px',
        saveParam:null,
        // 存储3数据选择最后输入的select值
        lastDataSelect:{
          'key1':'',
          'key2':'',
          'key3':''
        },

      }
    },
    created() {

    },
    computed: {
      ...mapGetters(['testTaskFilterData']),
    },
    mounted() {

      this.param = this.testTaskFilterData
      this.$store.commit('setTaskFilterData', null);
      if(this.param != null){
        this.dataType = this.param.dataType
        this.stepChecked = this.param.stepChecked
        this.dataChecked = this.param.dataChecked
        this.cycChecked = this.param.cycChecked
        this.filterData = this.param.filterData
        this.orderData = this.param.orderData
        this.stepOptions = this.param.stepOptions
        this.dataOptions = this.param.dataOptions
        this.cycOptions = this.param.cycOptions
      }else{
        document.getElementById("checkAll").click()
      }
    },

    methods: {

      onChange() {

        let num = this.dataType=='step'?this.stepChecked.length:this.dataType=='data'?this.dataChecked.length:this.cycChecked.length
        let allNum = this.dataType=='step'?this.stepOptions.length:this.dataType=='data'?this.dataOptions.length:this.cycOptions.length

        this.indeterminate = num == 0 ?null: 0 < num < allNum;
        this.checkAll = num > 0 && num == allNum
        this.$nextTick(() => {
          if(this.checkAll){
            this.indeterminate = false
          }
        })

      },

      // 3数据选择select改变事件
      handleDataSelectChange(value,option,index){
      this.lastDataSelect[index] = value
      },

      onCheckAllChange(e) {
        Object.assign(this, {
          stepChecked: e.target.checked ?  this.filterValue(this.stepOptions): [],
          dataChecked: e.target.checked ? this.filterValue(this.dataOptions) : [],
          cycChecked: e.target.checked ? this.filterValue(this.cycOptions): [],
          indeterminate: null,
          checkAll: e.target.checked,
        });

      },
      filterValue(list){
        let newList = []
        for (let i = 0; i < list.length; i++) {
          newList.push(list[i].value)
        }
        return newList
      },


      handleSubmit(){
        const {
          form: {
            validateFields
          }
        } = this

        this.confirmLoading = true
        validateFields((errors, values) => {
            if (!errors) {
              shenghongDataFilterExport(Object.assign(values,this.saveParam)).then(res => {
                if (res.success) {
                  this.$message.success('导出任务创建成功')
                  this.$router.push('/testDataHistory');
                } else {
                  this.$message.warn(res.message)
                }
              })
            }
          this.confirmLoading = false
        })
      },

      exportData(){




        let param = {}
        param.dataType = this.dataType
        param.stepChecked = this.stepChecked
        param.dataChecked = this.dataChecked
        param.cycChecked = this.cycChecked
        param.filterData = this.filterData
        param.orderData = this.orderData
        param.stepOptions = this.stepOptions
        param.dataOptions = this.dataOptions
        param.cycOptions = this.cycOptions

        if(param.orderData.length == 0){
          this.$message.warn('请选择测试项目')
          return
        }
        if(param.filterData.length == 0){
          this.$message.warn('请填写数据选择项')
          return
        }
        if(param.dataType == 'stpe' && param.stepChecked.length == 0){
          this.$message.warn('请勾选导出项')
          return
        }
        if(param.dataType == 'data' && param.dataChecked.length == 0){
          this.$message.warn('请勾选导出项')
          return
        }
        if(param.dataType == 'cyc' && param.cycChecked.length == 0){
          this.$message.warn('请勾选导出项')
          return
        }

        this.visible1 = true

        this.saveParam = param


      },
      // 3数据选择--添加按钮事件
      addFilterData(){
        this.filterData.push({
          key1: this.lastDataSelect['key1'] ? this.lastDataSelect['key1'] : 'StepId',value1:null,
          key2:this.lastDataSelect['key2'] ? this.lastDataSelect['key2'] : null,value2:null,
          key3:this.lastDataSelect['key3'] ? this.lastDataSelect['key3'] : null,value3:null,
          key4:null,value4:null,
        })
      },

      expandedRowsChange(expandedRows){
        console.log(expandedRows)
      },
      openStepData(record,flag) {

        this.outQueryFlowRecord = record
        this.outFlowRecord = record

        //历史数据处理
        if(null == record.flowInfoList && !flag){
          this.$refs.stepData.query(record, false)
        }


        if(record.flowId != null){
          this.outQueryFlowRecord.flowId = record.flowId
          this.$refs.stepData.query(this.outQueryFlowRecord, false)
        }else{
          this.$message.warn("测试数据为空")
          return
        }


      },
      onSelectChange(record, selected) {

        this.outFlowRecord = record
        this.outQueryFlowRecord = record

        if (selected) {

          if(record.flowId == null){
            this.$message.warn("测试数据为空")
            return
          }
          /*if(record.flowInfoList.length == 0){
            this.$message.warn("测试数据为空")
            return
          }else if(record.flowInfoList.length > 1){
            this.visibleFlow = true
            this.flowInfoData = record.flowInfoList
            this.inFlowActionName = '选中'
            return
          }else if(record.flowInfoList.length == 1){
            record.flowId = record.flowInfoList[0].flowId
          }*/


          if (!this.selectedRowKeys.includes(record.uuid)) {
            this.selectedRowKeys.push(record.uuid)
            this.selectedRows.push(record)
            this.orderData.push(record)
          }
        } else {
          for (let i = 0; i < this.selectedRowKeys.length; i++) {
            if (this.selectedRowKeys[i] === record.uuid) {
              this.selectedRowKeys.splice(i, 1)
              this.selectedRows.splice(i, 1)
              this.orderData.splice(i, 1)
              break;
            }
          }
        }
      },

      onSelectChangeFlow(record,handle) {

        if(handle == '查看'){
          this.outQueryFlowRecord.flowId = record.flowId
          this.$refs.stepData.query(this.outQueryFlowRecord, false)
          return
        }

        this.outFlowRecord.flowId = record.flowId

        if (!this.selectedRowKeys.includes(this.outFlowRecord.uuid)) {
          this.selectedRowKeys.push(this.outFlowRecord.uuid)
          this.selectedRows.push(this.outFlowRecord)
          this.orderData.push(this.outFlowRecord)
        }

        this.visibleFlow = false

      },
      deleteData() {
        const orderIdArray = this.orderData.map(item => item.uuid)
        this.projectSelectedRow.forEach(item => {
          let index = orderIdArray.indexOf(item.uuid)
          orderIdArray.splice(index, 1)
          this.orderData.splice(index, 1)
          this.selectedRows.splice(index, 1)
          this.selectedRowKeys.splice(index, 1)
        })
        this.projectSelectedRow = []
        this.projectSelectedRowKeys = []
      },
      projectOnChange(selectedRowKeys, selectedRows) {
        this.projectSelectedRow = selectedRows
        this.projectSelectedRowKeys = selectedRowKeys
      },
      onSelectAllChange(selected, selectedRows, changeRows) {

        if (selected) {
          for (let i = 0; i < selectedRows.length; i++) {
            if(selectedRows[i].flowId == null){
              this.$message.warn('序号'+(i+1)+"测试数据为空")
              return
            }
          }
          selectedRows.forEach(item => {
            if (!this.selectedRowKeys.includes(item.uuid)) {
              this.selectedRowKeys.push(item.uuid)
              this.selectedRows.push(item)
              this.orderData.push(item)
            }

            /*if(item.children && item.children.length > 0){
              item.children.forEach(inItem => {
                if (!this.selectedRowKeys.includes(inItem.id)) {
                  this.selectedRowKeys.push(inItem.id)
                  this.selectedRows.push(inItem)
                  this.orderData.push(inItem)
                }
              })
            }*/

          })
        } else {
          for (let i = 0; i < changeRows.length; i++) {
            if (this.selectedRowKeys.includes(changeRows[i].uuid)) {
              let index = this.selectedRowKeys.indexOf(changeRows[i].uuid)
              this.selectedRowKeys.splice(index, 1)
              this.selectedRows.splice(index, 1)
              this.orderData.splice(index, 1)
            }
          }
        }
      },
      handleCancel() {
        this.visible = false
      },
      handleCancelFlow() {
        this.visibleFlow = false
      },
      handleCancel1() {
        this.visible1 = false
      },
      openTestOrder() {
        this.visible = true
        /*this.$nextTick(() => {
          this.$refs.table.refresh()

        })*/
      },

      changeDataType(){
        if(!document.getElementById("checkAll").checked){
          document.getElementById("checkAll").click()
        }
      },

      copyFromExcel(event,column,index){

        let arr = event.clipboardData.getData('text').split("\n")


        if(arr.length > 1){
          for (let i = 1; i < arr.length; i++) {
            if(null != arr[i] && '' != arr[i] && arr[i].length != 0){
              if(this.filterData.length > index+i){
                this.filterData[index+i][column] = arr[i]
              }else{
                this.filterData.push({
                  key1:this.filterData[index].key1,value1:null,
                  key2:this.filterData[index].key2,value2:null,
                  key3:this.filterData[index].key3,value3:null,
                  key4:this.filterData[index].key4,value4:null,
                })
                this.filterData[index+i][column] = arr[i]
              }

            }

          }

        }

        setTimeout(() => {
          this.filterData[index][column] = arr[0]
        }, 10)


      },
      handleOk() {
        this.getList()
      },

      openData(folderId) {
        this.$refs.testData.query(folderId)
      },
      getList(flag) {

        this.$refs.table2.refresh()
      },


    }
  }
</script>
<style lang="less" scoped=''>
  /deep/ .ant-table-thead > tr > th {
    padding: 5px !important;
    font-size: 14px !important;
  }

  /deep/ .ant-table-tbody > tr > td {
    padding: 0px !important;
    height: 32px !important;
    font-size: 12px !important;
  }


  /deep/ .ant-calendar-picker-icon {
    display: none;
  }


  /deep/ .ant-calendar-picker-input.ant-input {
    color: black;
    font-size: 12px;
    border: 0;
    text-align: center;
    padding: 0;
  }

  .red {
    background-color: #ed0000;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .yellow {
    background-color: #ffc000;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .grey {
    background-color: rgba(223, 223, 223, 0.25);
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .ant-modal-body {
    padding: 0;
  }



  /deep/ .ant-btn > i, /deep/ .ant-btn > span {
    display: flex;
    justify-content: center;
  }

  /deep/ .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
    color: #fff;
    background: #1890FF;
  }

  .green {
    background-color: #58a55c;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

  }

  /deep/ #table1 > div > div > div > div > div > div > table > thead {
    height: 64px;
  }

  /deep/ #table1 > .ant-table-wrapper > div > div > ul {
    display: none;
  }


  /deep/ .ant-table-pagination.ant-pagination {
    float: right;
    margin: 0;
  }

  .float {
    width: 36%;
    float: left;
    margin-right: 10px;
    text-align: center;
  }

  .float1 {
    width: 12%;
    float: left;
    margin-right: 10px;
    text-align: center;
  }

  /deep/ .ant-checkbox-group-item {
    display: block;
    width: 100%;
    text-align: left;
  }

  .title {
    font-size: large;
    margin-bottom: 20px;
  }

  .numTitle {
    font-size: xx-large;
  }

  /deep/.ant-table-footer {
   padding: 0;
  }

  /deep/ .ant-table-row-expand-icon {
    margin-right: 0px;
  }
</style>