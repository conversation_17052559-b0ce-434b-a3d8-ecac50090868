<template>
  <a-modal title="原始数据" width="80%" :height="600"
           :bodyStyle="{padding:0}"
           :visible="visible" :confirmLoading="confirmLoading" style="padding: 0"
           :maskClosable="false"
           @cancel="handleCancel">

    <template slot="footer">
      <a-button key="back" @click="handleCancel">
        关闭
      </a-button>
    </template>

   <!-- <a-tabs type="card" @change="getData()" v-model="dataType">-->
      <!--<a-tab-pane key="folderData" tab="委托单信息" >
        <a-table :columns="columns" :data-source="data" bordered
                 style="padding: 5px;height: 280px"
                 bordered
                 :scroll="{y: 240}"
                 :rowKey="(record) => record.id"
                 :pagination="false">
          <template
            slot="celltestcode"
            slot-scope="text, record, index, columns">
            <a @click="openStepData(record)"  style="text-align: center"  >{{text}}</a>
          </template>
        </a-table>
      </a-tab-pane>-->
      <!--<a-tab-pane key="data" tab="原始数据" >-->
        <a-table :columns="columns" :data-source="data" bordered
                 style="padding: 5px;"
                 bordered
                 :scroll="{x: 800}"
                 :rowKey="(record) => record.uuid"
                 :pagination="false">

          <template slot="celltestcode" slot-scope="text, record, index, columns">
            <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
              <template slot="title">
                {{text}}
              </template>
              <a @click="openStepData(record)" v-if="record.flowId != null" style="text-align: center">{{ text }}</a>
              <span v-else style="text-align: center">{{ text }}</span>
            </a-tooltip>
          </template>

          <template
            slot="theme"
            slot-scope="text, record, index, columns">
            <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
              <template slot="title">
                {{text}}
              </template>
              {{text}}
            </a-tooltip>
          </template>
          <template slot="action1" slot-scope="text, record, index, columns">
            <template v-if="record.showHide">
              <a @click="showData(record)" v-if="record.showHide" style="text-align: center">初始化</a>
              <a-divider v-if="record.children != null" type="vertical" />
            </template>

            <a @click="hideData(record)" v-if="record.children != null || record.isChild" style="text-align: center">隐藏</a>
          </template>
          <template
            slot="dataPath"
            slot-scope="text, record, index, columns">
            <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
              <template slot="title">
                {{text?text:record.testcontent}}
              </template>
              {{text?text:record.testcontent}}
            </a-tooltip>
          </template>
        </a-table>

        <a-modal title="测试数据选择" :width="1000" :height="300"
                 :bodyStyle="{padding:0}"
                 :visible="visibleFlow" style="padding: 0"
                 :maskClosable="false"
                 :centered="true"
                 @cancel="handleCancelFlow">

          <a-table :columns="flowInfoColumns" :dataSource="flowInfoData" bordered :rowKey="(record) => record._id"
                   ref="table" :pagination="false"
          >


            <template
              slot="celltestcode"
              slot-scope="text, record, index, columns"
            >

              <a @click="openStepData(record)" style="text-align: center">{{text}}</a>

            </template>

            <template
              slot="action"
              slot-scope="text, record, index, columns"
            >
              <a @click="onSelectChangeFlow(record,'查看')" style="text-align: center">查看</a>

            </template>
            <template
              slot="dataPath"
              slot-scope="text, record, index, columns">
              <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
                <template slot="title">
                  {{text?text:record.testcontent}}
                </template>
                {{text?text:record.testcontent}}
              </a-tooltip>
            </template>

          </a-table>
          <template slot="footer">
            <a-button key="back" @click="handleCancelFlow">
              关闭
            </a-button>
          </template>

        </a-modal>

      <!--</a-tab-pane>-->
      <!--<a-tab-pane key="exportTask" tab="导出任务" >
        <a-table :columns="exportTaskcolumns" :data-source="exportTaskData" bordered
                 style="padding: 5px;height: 280px"
                 bordered
                 :scroll="{y: 240}"
                 :rowKey="(record) => record.id"
                 :pagination="false">
          <template
            slot="taskName"
            slot-scope="text, record, index, columns">
            <a :href="'http://'+record.ip+':82/sysFileInfo/download?id='+record.fileId"
               style="text-align: center" v-if="record.fileStatus == 20">{{text}}</a>
            <span v-else>{{text}}</span>
          </template>
        </a-table>
      </a-tab-pane>-->
    <!--</a-tabs>-->





    <step-data ref="stepData"></step-data>
  </a-modal>
</template>

<script>
  import {
    tLimsTestdataScheduleList,shenghongDataExportTaskList
  } from '@/api/modular/system/limsManager'

  import stepData from './stepData'
  import moment from "moment";
  export default {
    components: {
      stepData
    },

    data() {
      return {
        outQueryFlowRecord:null,
        visibleFlow:false,
        flowInfoColumns: [
          {
            title: '数据位置',
            align: 'center',
            width: 200,
            dataIndex: 'dataPath',
            scopedSlots: {customRender: 'dataPath'},
          },
          {
            title: '测试编码',
            dataIndex: 'barCode',
            align: 'center',
            width: 100,

          }, {
            title: '设备编号',
            width: 30,
            align: 'center',
            dataIndex: 'unitNum',
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '通道编号',
            width: 30,
            align: 'center',
            dataIndex: 'channelId',
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '开始时间',
            width: 30,
            align: 'center',
            dataIndex: 'startTime',
            customRender: (text, record, index) =>{
              if(null != text){
                return moment(text).format('YYYY-MM-DD')
              }
              return text
            }
            //
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '结束时间',
            width: 30,
            align: 'center',
            dataIndex: 'endTime',
            customRender: (text, record, index) =>{
              if(null != text){
                return moment(text).format('YYYY-MM-DD')
              }
              return text
            }
          },{
            title: '操作',
            width: 60,
            align: 'center',
            dataIndex: 'action',
            scopedSlots: {customRender: 'action'},
          }
        ],
        flowInfoData: [],
        dataType:'data',
        data: [],
        exportTaskcolumns: [
          {
            title: '序号',
            align: 'center',
            width: 50,
            customRender: (text, record, index) => index + 1
          }, {
            title: '任务名称',
            dataIndex: 'taskName',
            align: 'center',
            width: 200,
            scopedSlots: {customRender: 'taskName'},
          }, {
            title: '任务状态',
            width: 90,
            align: 'center',
            dataIndex: 'fileStatus',
            customRender: (text, record, index) => {
              if(text == 0){
                return "新建"
              }
              if(text == 10){
                return "导出中"
              }
              if(text == 20){
                return "导出完成"
              }
              if(text == 30){
                //导出失败  展示为 新建
                return "新建"
              }
            }
          },
          {
            title: '排队位置',
            width: 90,
            align: 'center',
            dataIndex: 'ranking',
            //scopedSlots: {customRender: 'updateText'},
          },{
            title: '文件大小',
            width: 90,
            align: 'center',
            dataIndex: 'fileSizeInfo',
            //scopedSlots: {customRender: 'updateText'},
          },  {
            title: '创建时间',
            width: 90,
            align: 'center',
            dataIndex: 'createTime',
            //scopedSlots: {customRender: 'updateText'},
          },  {
            title: '开始时间',
            width: 90,
            align: 'center',
            dataIndex: 'beginTime',
            //scopedSlots: {customRender: 'updateText'},
          },{
            title: '完成时间',
            width: 90,
            align: 'center',
            dataIndex: 'finishTime',
            //scopedSlots: {customRender: 'updateText'},
          }
        ],
        exportTaskData:[],
        columns: [
          {
            title: "操作",
            align: "center",
            width: 105,
            scopedSlots: {customRender: 'action1'},
          },{
            title: "序号",
            align: "center",
            width: 40,
            customRender: (text, record, index) => {
              if (!record.isChild) {
                return index + 1
              }
            }
          },
          {
            title: "委托单号",
            dataIndex: "folderno",
            align: "center",
            width: 90
          },
          {
            title: "主题",
            dataIndex: "theme",
            align: "center",
            ellipsis: true,
            width: 90,
            scopedSlots: {customRender: 'theme'},
          },
          {
            title: "样品编号",
            width: 110,
            align: "center",
            dataIndex: "orderno",
            scopedSlots: {customRender: 'theme'},
          },
          /*{
            title: "测试项目编码",
            width: 90,
            align: "center",
            dataIndex: "testcode"
            //scopedSlots: {customRender: 'updateText'},
          },*/
          {
            title: "测试项目别名",
            width: 100,
            align: "center",
            dataIndex: "alias",
            ellipsis: true,
            scopedSlots: {customRender: 'theme'},
            //scopedSlots: {customRender: 'updateText'},
          },
          {
            title: "测试编码",
            width: 110,
            align: "center",
            dataIndex: "celltestcode",
            ellipsis:true,
            scopedSlots: { customRender: "celltestcode" }
          },
          {
            title: "数据位置",
            width: 80,
            align: "center",
            dataIndex: "dataPath",
            ellipsis: true,
            scopedSlots: {customRender: 'dataPath'},
          },{
            title: "存储天数",
            width: 75,
            align: "center",
            dataIndex: "day"
          },
          {
            title: "温度",
            width: 40,
            align: "center",
            dataIndex: "tem"
          },{
            title: "SOC",
            width: 40,
            align: "center",
            dataIndex: "soc"
          },
          {
            title: "测试状态",
            width: 80,
            align: "center",
            dataIndex: "endstatusflag",
            customRender: (text, record, index) => {
              if (null != text) {
                return text == 0?'测试中':'完成'
              }
              return text
            }
          },
          {
            title: "开始时间",
            width: 80,
            align: "center",
            dataIndex: "startTime",
            customRender: (text, record, index) => {
              if (null != text) {
                return moment(text).format("YYYY-MM-DD")
              }
              return text
            }
            //
            //scopedSlots: {customRender: 'updateText'},
          },
          {
            title: "结束时间",
            width: 80,
            align: "center",
            dataIndex: "endTime",
            customRender: (text, record, index) => {
              if (null != text && record.endstatusflag == 1) {
                return moment(text).format("YYYY-MM-DD")
              }
              return null
            }
          },
          {
            title: "设备编号",
            width: 80,
            align: "center",
            dataIndex: "equiptcode"
          },
          {
            title: "通道编号",
            width: 80,
            align: "center",
            dataIndex: "channelno"
          }
        ],

        labelCol: {
          xs: {
            span: 12
          },
          sm: {
            span: 8
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 14
          }
        },
        visible: false,
        confirmLoading: false,
        folderId:null,
      }
    },

    methods: {
      onSelectChangeFlow(record,handle) {

        if(handle == '查看'){
          this.outQueryFlowRecord.flowId = record.flowId
          this.$refs.stepData.query(this.outQueryFlowRecord, false)
          return
        }
      },
      handleCancelFlow() {
        this.visibleFlow = false
      },
      getData(){
        if(this.dataType == 'data'){
          tLimsTestdataScheduleList({folderid:this.folderId}).then((res) => {

            if (res.success) {
              this.data = res.data
            } else {
              this.$message.error('查询失败：' + res.message)
            }
          }).finally((res) => {
            this.visible = true
          })
        }else{
          shenghongDataExportTaskList({folderId:this.folderId}).then(res => {
            this.exportTaskData = res.data
          })
        }

      },
      query(folderId) {

        this.folderId = folderId

        if(this.dataType == 'data'){
          tLimsTestdataScheduleList({folderid:folderId}).then((res) => {

            if (res.success) {
              this.data = res.data
            } else {
              this.$message.error('查询失败：' + res.message)
            }
          }).finally((res) => {
            this.visible = true
          })
        }else{
          shenghongDataExportTaskList({folderId:this.folderId}).then(res => {
            this.exportTaskData = res.data
            this.visible = true
          })
        }


      },

      openStepData(record) {

        this.outQueryFlowRecord = record

        if(record.flowId != null){
          this.outQueryFlowRecord.flowId = record.flowId
          this.$refs.stepData.query(this.outQueryFlowRecord, false)
        }else{
          this.$message.warn("测试数据为空")
          return
        }

      },

      handleCancel() {
        this.visible = false
      }
    }
  }
</script>
<style lang="less" scoped>
  .ant-form-item {

    margin-bottom: 0px;

  }

  .man_button {
    padding-left: 11px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  /deep/ .ant-modal-body {
    padding: 0 !important;
  }

  /deep/ .ant-table-thead > tr > th, /deep/ .ant-table-tbody > tr > td {
    padding: 3px;
  }

  /deep/ .ant-table-footer {

    padding: 0px;
  }

  /deep/ .ant-table-pagination.ant-pagination {
    margin: 5px 0;
  }

  /deep/ .ant-input-number {
    width: 100%;
  }

  /deep/ .ant-input-number-sm > .ant-input-number-input-wrap > .ant-input-number-input {
    text-align: center;
  }

  /deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
    color: #fff;
    background: #1890FF;
  }

  /deep/ input {
    width: 100%;

  }
  /deep/ input:focus {
    outline: 0;
  }

  /deep/.ant-modal-footer {
    padding: 5px 16px;
  }

</style>
