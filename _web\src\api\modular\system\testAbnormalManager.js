import { axios } from '@/utils/request'



export function earlyWarningCalendarCheck(parameter) {
  return axios({
    url: `/earlyWarning/calendarCheck`,
    method: 'post',
    data: parameter
  })
}

export function earlyWarningTechConfirmCalendar(parameter) {
  return axios({
    url: `/earlyWarning/techConfirmCalendar`,
    method: 'post',
    data: parameter
  })
}

export function earlyWarningEngineerConfirmCalendar(parameter) {
  return axios({
    url: `/earlyWarning/engineerConfirmCalendar`,
    method: 'post',
    data: parameter
  })
}

export function earlyWarningCalendarListPage(parameter) {
  return axios({
    url: `/earlyWarning/calendarListPage`,
    method: 'post',
    data: parameter
  })
}

export function calendarTechNextStep(parameter) {
  return axios({
    url: `/earlyWarning/calendarTechNextStep`,
    method: 'post',
    data: parameter
  })
}

export function calendarAgentPageList(parameter) {
  return axios({
    url: `/testAbnormalAgent/listPage`,
    method: 'post',
    data: parameter
  })
}

export function calendarAgentAdd(parameter) {
  return axios({
    url: `/testAbnormalAgent/add`,
    method: 'post',
    data: parameter
  })
}

export function earlyWarningGet(parameter) {
  return axios({
    url: `/earlyWarning/get`,
    method: 'post',
    data: parameter
  })
}