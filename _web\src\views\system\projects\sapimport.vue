<template>
    <a-modal :title="title" :width="500" :visible="visible" :confirmLoading="confirmLoading" @ok="handleSubmit" @cancel="handleCancel">
        <a-spin :spinning="confirmLoading">
            <a-form :form="form">
                <a-form-item label="" v-if="!this.row.parent_id">
                    <a-select v-model="werk" placeholder="请选择工厂" style="width:100%">
                        <a-select-option v-for="(item,i) in werks" :key="i" :value='item.werks'>
                            {{item.werks}}-{{item.name1}}
                        </a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="">
                    <a-input placeholder="请输入版本号" v-decorator="['version', {rules: [{required: true, message: '请输入版本号'}]}]" />
                </a-form-item>
                <a-form-item label="">
                    <a-input placeholder="请输入BOM序号，如01,02" v-decorator="['num']" />
                </a-form-item>
            </a-form>
        </a-spin>
    </a-modal>
</template>

<script>
    import {
        getWerksOptions,
        sapImport
    } from "@/api/modular/system/bomManage"
    export default {
        data() {
            return {
                title: '选择工厂',
                visible: false,
                confirmLoading: false,
                werks: [],
                werk: '',
                row: {},
                bomId: 0,
                form: this.$form.createForm(this)
            }
        },
        methods: {
            handleSubmit() {
                const {
                    form: {
                        validateFields
                    }
                } = this
                this.confirmLoading = true
                validateFields((errors, values) => {
                    if (!errors) {
                        let $params = {...values, id: this.bomId, treeBomId: this.row.id,werk: this.werk}
                        sapImport($params).then((res) => {
                            if (res.success) {
                                this.$message.success('导入成功')
                                this.$emit('refresh')
                                this.handleCancel()
                            } else {
                                this.$message.error(res.message)
                            }
                            this.confirmLoading = false
                        }).catch((err) => {
                            this.$message.error('错误提示：' + err.message, 1)
                            this.confirmLoading = false
                        })
                    } else {
                        this.confirmLoading = false
                    }
                })
            },
            handleCancel() {
                this.visible = false
                this.confirmLoading = false
                this.werks = []
                this.werk = ''
                this.row = {}
                this.bomId = 0
                this.form.resetFields()
            },
            add(id, row) {
                this.bomId = id
                this.row = row
                if (!row.parent_id) {
                    this.callWerksOptions()
                }
                this.visible = true
            },
            callWerksOptions() {
                this.confirmLoading = true
                getWerksOptions().then((res) => {
                    if (res.success) {
                        this.werks = res.data
                    } else {
                        this.$message.error(res.message)
                    }
                    this.confirmLoading = false
                }).catch((err) => {
                    this.$message.error('错误提示：' + err.message, 1)
                })
            }
        }
    }
</script>

<style>

</style>