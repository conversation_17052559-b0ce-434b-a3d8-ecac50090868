<template>
  <div>
    <div class="right-top-div">
      <div class="mr10" v-if="testProgress && testProgress.onlineReportStatus === 10">在线数据处理中</div>
      <div class="mr10" v-else-if="testProgress && testProgress.onlineReportStatus === 40">更新中</div>
      <div class="mr10" v-else-if="testProgress && testProgress.onlineReportStatus === 30 && tableList.length > 0">更新失败</div>
      <div class="mr10" v-else-if="testProgress && testProgress.onlineReportStatus === 30">暂无数据</div>
      <div class="mr10" v-else>更新时间：{{ testProgress && testProgress.onlineReportEndTime ? testProgress.onlineReportEndTime : "" }}</div>
      <a-tooltip>
        <template slot="title">
          刷新数据
        </template>
        <a-icon class="mr10" type="reload" @click="updateOnlineReport"/>
      </a-tooltip>
      <a-tooltip placement="topRight">
        <template slot="title">
          导出数据
        </template>
        <a-icon class="mr10" type="download" @click="exportOnlineReport"/>
      </a-tooltip>
    </div>

    <div class="all-wrapper">
      <div class="left-content block" :style="{borderRadius: this.$route.query.offFlag == '0' ? '0 10px 10px 10px' : '10px' }">
        <div v-for="(item, index) in onlineEditObjList">
          <div class="flex-sb-center-row">
            <div style="display: flex; align-items: center;">
              <pageComponent :editObj="item" @down="handleDown" @edit="handleEditEcharts"></pageComponent>
              <a-button v-if="index === 1 && hasPermission" size="small" style="margin-left: 10px;" @click="openForecastModal">开始预测</a-button>
            </div>
            <div>
              <a-tooltip>
                <template slot="title">
                  自定义处理逻辑
                </template>
                <a-icon type="edit" @click="editCustomLogic"/>
              </a-tooltip>
              <a-radio-group v-if="item.includes('Rec')" class="ml10" size="small" :value="stepTypeActive"
                             @change="stepTypeChange">
                <a-radio-button value="Custom" v-if="testProgress && testProgress.customLogicFlag === 1">自定义</a-radio-button>
                <a-radio-button value="2nd">DCH-2nd</a-radio-button>
                <a-radio-button value="3rd">DCH-3rd</a-radio-button>
                <a-radio-button value="4th">DCH-4th</a-radio-button>
              </a-radio-group>
            </div>
          </div>
          <div :ref="item" :id="item" class="mt2" :class="{ 'animate__animated animate__pulse': animateObj[item] }"
               style="width: 595px; height: 415px; border: 0.5px solid #ccc;"></div>
        </div>
      </div>

      <div class="right-content block">
        <div v-for="item in onlineEditObjList">
          <div class="flex-sb-center-row" style="height: 32px;">
            <div style="font-size: 16px;font-weight: 500;">{{ onlineTableNameMap[item] }}</div>
            <div>
              <a-tooltip>
                <template slot="title">
                  自定义处理逻辑
                </template>
                <a-icon type="edit" @click="editCustomLogic"/>
              </a-tooltip>
              <a-radio-group v-if="item.includes('Rec')" class="ml10" size="small" :value="stepTypeActive" @change="stepTypeChange">
                <a-radio-button value="Custom" v-if="testProgress && testProgress.customLogicFlag === 1">自定义</a-radio-button>
                <a-radio-button value="2nd">DCH-2nd</a-radio-button>
                <a-radio-button value="3rd">DCH-3rd</a-radio-button>
                <a-radio-button value="4th">DCH-4th</a-radio-button>
              </a-radio-group>
            </div>
          </div>
          <div class="mt2" style="height: 415px;">
            <a-table :data-source="tableList"
                     :columns="getColumns(item)"
                     :rowKey="record => record.day"
                     :pagination="paginationConfig"
                     :loading="onlineTableLoading"
                     bordered>
            </a-table>
          </div>
        </div>
      </div>
    </div>

    <!-- 在线编辑图表 -->
    <div v-if="drawerVisible">
      <PreviewDrawer
        :screenImageId = "screenImageId"
        :templateParam = "reportChartTemplateList[editObj]"
        :calendarLife="true"
        :LegendNameTypeList="chartLegendNameList[editObj]"
        :legendOptions="chartOriginalLegent[editObj]"
        :data="chartOriginalseries[editObj]"
        :original="chartResetOriginal[editObj]"
        :editData="chartEditData[editObj]"
        :checkObj="chartCheckObj[editObj]"
        @submit="handleDrawerSubmit"
        @reset="handleDrawerReset"
        @close="drawerVisible = false"
        @changeTemplate ="handleChangeTemplate"
        @screenshot="handleScreenshot">
      </PreviewDrawer>
    </div>

    <div class="action-bar">
      <a-icon type="menu" @click="handleSuspensionIcon" />
       <pbiReturnTop class="mt10" width="20" height="20" color="#333" @returnTop="handleReturnTop"></pbiReturnTop>
    </div>
    <!-- 快速导航栏 -->
    <div v-if="navigationVisible">
      <calendarThubnail
        :thumbnailList=thumbnailList
        @cancel="navigationVisible = false"
        @choose="handleNavigation"
      ></calendarThubnail>
    </div>

    <a-modal title="日历寿命预测" width="90vw" :bodyStyle="{ padding: '0 10px 10px 10px' }" :footer="null" :visible="isShowForecastModal" @cancel="isShowForecastModal = false">
      <a-spin :spinning="forecastLoading">
        <div class="search-container">
          <div style="position: absolute; top: 8px; right: 10px; z-index: 1; display: flex; flex-direction: column;">
            <a-tooltip v-if="isEditParam" :overlayStyle="{ maxWidth: '260px' }" title="先对多个样本记录值求均值，再根据求得的均值实现半经验公式/AI辅助拟合">
              <a-button type="primary" @click="startForecast(false, false)">
                <span style="display: inline-block; align-items: center;">开始拟合 <a-icon type="question-circle" style="color: #fff;" /></span>
              </a-button>
            </a-tooltip>
            <a-button v-else type="primary" @click="startEditParam">返回上一级</a-button>

            <div v-if="!isEditParam" class="mt10">
              <a-button v-if="fitCheckedType === 'correction'" type="primary" @click="startForecast(true, false)">拟合修正</a-button>
              <a-button v-else type="primary" @click="startForecast(false, true)">拟合优化</a-button>
            </div>
          </div>
        </div>

          <pbiSearchContainer>
            <pbiSearchItem label='产品类型 ' :span="4">
              <a-select v-model="productType" :options="systemOptions" :disabled="!isEditParam"></a-select>
            </pbiSearchItem>
            <pbiSearchItem label='温度 ' :span="4">
              <a-select v-model="temperature" :options="temperatureOptions" :disabled="!isEditParam"></a-select>
            </pbiSearchItem>
            <pbiSearchItem label='SOC ' :span="4">
              <a-select v-model="soc" :options="socOptions" :disabled="!isEditParam"></a-select>
            </pbiSearchItem>
            <pbiSearchItem label='最大预测天数 ' :span="4">
              <a-input-number style="width: 100%;" :min="minInferenceDay" :max="fitting_method === 'semi_empirical_formule_fitting' ? 3000 : 1000" v-model="maxInferenceDay" :disabled="!isEditParam"></a-input-number>
            </pbiSearchItem>
            <a-col :span="4" v-if="fitting_method === 'ai_assisted_fitting'">
              <div class="searchItem">
                <a-tooltip title="预测结果的最低容量恢复率，达到该阈值则不再往后预测">
                  <span class="label">恢复率阈值 <a-icon type="question-circle" style="color: #1890ff;" /> :</span>
                </a-tooltip>
                <div class="content">
                  <a-select v-model="soh_threshold" :options="soh_thresholdOptions" :disabled="!isEditParam"></a-select>
                </div>
              </div>
            </a-col>
          </pbiSearchContainer>
          <a-row class="search-container">
            <a-col :span="4">
              <div class="searchItem">
                <a-tooltip @visibleChange="changeVisible" :overlayStyle="{ maxWidth: '370px' }" placement="topLeft">
                  <template slot="title">
                    <div>
                      ① 半经验公式：<br/>
                      <div ref="formulaContainer3" style="margin: 8px 12px;"></div>
                      ② AI辅助拟合：<br/>
                      <div style="margin: 8px 12px;">借助训练的AI模型来生成预测的数据点，在实际测量和生成预测的数据点的基础上应用半经验公式进行拟合</div>
                    </div>
                  </template>
                  <span class="label">拟合方法 <a-icon type="question-circle" style="color: #1890ff;" /> :</span>
                </a-tooltip>
                <div class="content">
                  <a-select v-model="fitting_method" :options="fitting_methodOptions" :disabled="!isEditParam" @change="changeFittingMethod"></a-select>
                </div>
              </div>
            </a-col>
            <a-col :span="4" v-if="!isEditParam">
              <div class="searchItem">
                <div class="label" ref="formulaContainer1"></div>
                <div class="content">
                  <a-input-number style="width: 80px;" v-model="slope" :precision="5" :step="0.1" :disabled="fitCheckedType === 'optimize'" @keyup.enter="startForecast(true, false)"></a-input-number>
                </div>
              </div>
            </a-col>
            <a-col :span="4" v-if="!isEditParam">
              <div class="searchItem">
                <div class="label" ref="formulaContainer2"></div>
                <div class="content">
                  <a-input-number style="width: 80px;" v-model="intercept" :precision="5" :step="0.1" :disabled="fitCheckedType === 'optimize'" @keyup.enter="startForecast(true, false)"></a-input-number>
                </div>
              </div>
            </a-col>
            <a-col :span="4" v-if="!isEditParam">
              <div style="height: 28px; display: flex; align-items: center;">
                <a-radio-group style="margin-left: 10px; display: flex;" v-model="fitCheckedType">
                  <a-tooltip @visibleChange="changeVisible">
                    <template slot="title">
                      <div style="display: flex; flex-wrap: wrap; align-items: center;">
                        通过改变半经验公式中的参数<span ref="formulaContainer1tip" style="margin: 0px 5px;"></span>和参数<span ref="formulaContainer2tip" style="margin: 0px 5px;"></span>来修正拟合曲线
                      </div>
                    </template>
                    <a-radio value="correction">拟合修正 <a-icon type="question-circle" style="color: #1890ff;" /></a-radio>
                  </a-tooltip>
                  <a-tooltip title="通过结合Nelder-Mead算法，根据MAE、MSE或RMSE的选择来优化参数值">
                    <a-radio v-if="fitting_method === 'semi_empirical_formule_fitting'" value="optimize">拟合优化 <a-icon type="question-circle" style="color: #1890ff;" /></a-radio>
                  </a-tooltip>
                </a-radio-group>
              </div>
            </a-col>
            <a-col :span="6" v-if="!isEditParam && fitCheckedType === 'optimize'">
              <div class="searchItem">
                <div class="label">参数优化标准 :</div>
                <div class="content">
                  <a-radio-group style="display: flex;" size="small" v-model="optimization_schema">
                    <a-radio-button value="mae">MAE</a-radio-button>
                    <a-radio-button value="mse">MSE</a-radio-button>
                    <a-radio-button value="rmse">RMSE</a-radio-button>
                  </a-radio-group>
                </div>
              </div>
            </a-col>
          </a-row>

        <div v-if="!isEditParam" style="height: 80px; display: flex; align-items: center; margin-top: 8px;">
          <div style="width: 50px;" class="flex-center-div">半经验公式拟合 :</div>
          <div style="position:absolute; left: 60px; width: 1080px;">
            <a-row class="search-container">
              <a-col v-for="item in prefixList" :span="8">
                <div style="display: flex; align-items: center; padding: 2px 8px 0px 8px; border-radius: 6px 6px;" :style="{border: fitting_method === 'semi_empirical_formule_fitting' && fitModel === item.value ? '1px solid #1890ff' : 'none'}">
                  <div style="width: 32px;" class="flex-center-div">{{item.label}}</div>
                  <div style="width: 305px; display: flex; flex-wrap: wrap; align-items: center;">
                    <a-tooltip @visibleChange="changeVisible">
                      <template slot="title">
                        <div :ref="`formulaContainer4${item.value}1`"></div>
                      </template>
                      <a-input class="me-input" :value="semiEmpiricalMeanErrorObj[`${item.value}Mae`]" :disabled="true" addonBefore="MAE" suffix="%" />
                    </a-tooltip>
                    <a-tooltip @visibleChange="changeVisible">
                      <template slot="title">
                        <div :ref="`formulaContainer5${item.value}1`"></div>
                      </template>
                      <a-input class="me-input" :value="semiEmpiricalMeanErrorObj[`${item.value}Mse`]" :disabled="true" addonBefore="MSE" suffix="%²" />
                    </a-tooltip>
                    <a-tooltip @visibleChange="changeVisible">
                      <template slot="title">
                        <div :ref="`formulaContainer6${item.value}1`"></div>
                      </template>
                      <a-input class="me-input" :value="semiEmpiricalMeanErrorObj[`${item.value}Rmse`]" :disabled="true" addonBefore="RMSE" suffix="%" />
                    </a-tooltip>
                    <div style="width: 35px; padding-right: 5px; font-size: 12px; color: #333;" :ref="`formulaContainer1${item.value}1`"></div>
                    <a-input style="width: 80px;" v-model="semiEmpiricalMeanErrorObj[`${item.value}Slope`]" :precision="5" :step="0.1" :disabled="true"></a-input>
                    <div style="width: 85px; padding-right: 5px; font-size: 12px; color: #333;" :ref="`formulaContainer2${item.value}1`"></div>
                    <a-input style="width: 80px;" v-model="semiEmpiricalMeanErrorObj[`${item.value}Intercept`]" :precision="5" :step="0.1" :disabled="true"></a-input>
                  </div>
                </div>
              </a-col>
            </a-row>
          </div>
        </div>
        <div v-if="!isEditParam" style="height: 80px; display: flex; align-items: center; margin-top: 8px;">
          <div style="width: 50px;" class="flex-center-div">AI辅助拟合 :</div>
          <div style="position:absolute; left: 60px; width: 1080px;">
            <a-row class="search-container">
              <a-col v-for="(item, index) in prefixList" v-if="index < 2" :span="8">
                <div  style="display: flex; align-items: center; padding: 2px 8px 0px 8px; border-radius: 6px 6px;" :style="{border: fitting_method === 'ai_assisted_fitting' && fitModel === item.value ? '1px solid #1890ff' : 'none'}">
                  <div style="width: 32px;" class="flex-center-div">{{item.label}}</div>
                  <div style="width: 305px; display: flex; flex-wrap: wrap; align-items: center;">
                    <a-tooltip @visibleChange="changeVisible">
                      <template slot="title">
                        <div :ref="`formulaContainer4${item.value}2`"></div>
                      </template>
                      <a-input class="me-input" :value="aiMeanErrorObj[`${item.value}Mae`]" :disabled="true" addonBefore="MAE" suffix="%" />
                    </a-tooltip>
                    <a-tooltip @visibleChange="changeVisible">
                      <template slot="title">
                        <div :ref="`formulaContainer5${item.value}2`"></div>
                      </template>
                      <a-input class="me-input" :value="aiMeanErrorObj[`${item.value}Mse`]" :disabled="true" addonBefore="MSE" suffix="%²" />
                    </a-tooltip>
                    <a-tooltip @visibleChange="changeVisible">
                      <template slot="title">
                        <div :ref="`formulaContainer6${item.value}2`"></div>
                      </template>
                      <a-input class="me-input" :value="aiMeanErrorObj[`${item.value}Rmse`]" :disabled="true" addonBefore="RMSE" suffix="%" />
                    </a-tooltip>
                    <div style="width: 35px; padding-right: 5px; font-size: 12px; color: #333;" :ref="`formulaContainer1${item.value}2`"></div>
                    <a-input style="width: 80px;" v-model="aiMeanErrorObj[`${item.value}Slope`]" :disabled="true"></a-input>
                    <div style="width: 85px; padding-right: 5px; font-size: 12px; color: #333;" :ref="`formulaContainer2${item.value}2`"></div>
                    <a-input style="width: 80px;" v-model="aiMeanErrorObj[`${item.value}Intercept`]" :disabled="true"></a-input>
                  </div>
                </div>
              </a-col>
            </a-row>
          </div>
        </div>

          <div v-if="isEditParam" class="all-wrapper" style="margin-top: 8px;">
            <div class="left-content">
              <div class="flex-sb-center-row" style="height: 32px;">
                <div/>
              </div>
              <div ref="update" id="update" class="mt2" style="width: 595px; height: 415px; border: 0.5px solid #ccc;"></div>
            </div>
            <div class="right-content">
              <div style="min-height: 32px;" class="search-container">
                <div style="display: flex; flex-wrap: wrap;">
                  <a-radio-group class="mr10 mt2" size="small" v-if="!isUpdateData" v-model="stepTypeForecast">
                    <a-radio-button value="Custom" v-if="testProgress && testProgress.customLogicFlag === 1">自定义</a-radio-button>
                    <a-radio-button value="2nd">DCH-2nd</a-radio-button>
                    <a-radio-button value="3rd">DCH-3rd</a-radio-button>
                    <a-radio-button value="4th">DCH-4th</a-radio-button>
                  </a-radio-group>
                  <a-button class="mr10 mt2" size="small" @click="isSampleSelectShow = true">电芯选择</a-button>
                  <a-popconfirm title="确定清空已编辑数据吗?" ok-text="确定" cancel-text="取消" placement="top" @confirm="clearUpdate">
                    <a-button class="mr10 mt2" type="primary" size="small" v-if="!isUpdateData">清空编辑</a-button>
                  </a-popconfirm>
                  <a-button class="mr10 mt2" type="primary" size="small" @click="isUpdateData = true">编辑数据</a-button>
                  <a-popconfirm title="确定提交修改吗?" ok-text="确定" cancel-text="取消" placement="top" @confirm="submitUpdate(true)">
                    <a-button class="mt2" v-if="isUpdateData" type="primary" size="small">提交</a-button>
                  </a-popconfirm>
                </div>
              </div>
              <div class="mt2">
                <a-table :data-source="updateDataList"
                         :columns="updateDataColumns"
                         :rowKey="record => record.day"
                         :pagination="paginationConfig"
                         :rowSelection="{ columnWidth: 100, selectedRowKeys: selectedRowKeys, onChange: onSelectChange, }"
                         bordered>
                  <template slot="rawCapacity" slot-scope="text, record, index, columns">
                    <span>{{ typeof text === 'number' ? text.toFixed(3) : text }}</span>
                  </template>
                  <template slot="showCapacity" slot-scope="text, record, index, columns">
                    <span :style="{ color: record[columns.dataIndex.split('.')[0]][`${columns.dataIndex.split('.')[1]}Edit`] ?  '#66ADF9' : 'black' }">
                      {{ record[columns.dataIndex.split('.')[0]][`${columns.dataIndex.split('.')[1]}Edit`]
                      ? record[columns.dataIndex.split('.')[0]][`${columns.dataIndex.split('.')[1]}Edit`].toFixed(3)
                      : typeof text === 'number' ? text.toFixed(3) : text }}
                    </span>
                  </template>
                  <template slot="updateCapacity" slot-scope="text, record, index, columns">
                    <a-input-number style="width: 100%; text-align: center"
                                    :min="0.001" :precision="3" step="0.1"
                                    v-model="record[columns.dataIndex.split('.')[0]][columns.dataIndex.split('.')[1]]"
                    />
                  </template>
                </a-table>
              </div>
            </div>
          </div>
          <div v-else class="all-wrapper" style="margin-top: 8px;">
            <div class="left-content">
              <div class="flex-sb-center-row" style="height: 32px;">
                <pageComponent editObj="forecast" @down="handleDown" @edit="handleEditEcharts"></pageComponent>
                <a-switch style="margin-right: 20px;" v-model="showForecastData" :checked-children="this.fitting_method === 'ai_assisted_fitting' ? '预测数据' : '拟合数据'" :un-checked-children="this.fitting_method === 'ai_assisted_fitting' ? '非预测数据' : '非拟合数据'" @change="forecastSwitchChange" />
              </div>
              <div ref="forecast" id="forecast" class="mt2" style="width: 595px; height: 415px; border: 0.5px solid #ccc;"></div>
            </div>
            <div class="right-content">
              <div class="flex-sb-center-row" style="height: 32px;">
                <div style="font-size: 16px;font-weight: 500;">
                  恢复容量数据
                </div>
              </div>
              <div class="mt2">
                <a-table :data-source="forecastTableList"
                         :columns="forecastTableColumns"
                         :rowKey="record => record.day"
                         :pagination="paginationConfig"
                         bordered>
                  <template slot="forecastData" slot-scope="text, record, index, columns">
                    <span :style="{ color: record.rowIndex <= (sampleRawDataNum[columns.dataIndex.split('.')[0]] || selectedRowKeys.length - 1)
                    ? (record[columns.dataIndex.split('.')[0]][`${columns.dataIndex.split('.')[1]}EditFlag`] ?  '#66ADF9' : 'black')
                    : (text === '-' ? 'black' : '#ffb794') }">
                      {{ isNaN(parseFloat(text)) ? text : roundToFixed(text, columns.dataIndex.includes('Rate') ? 2 : 3) }}
                    </span>
                  </template>
                  <template slot="fitting_result" slot-scope="text, record, index, columns">
                    <span :style="{ color: fitModel === 'init' ? '#7cfc00' : (fitModel === 'correction' ? '#47BB55' : '#006400') }">{{ text }}</span>
                  </template>
                </a-table>
              </div>
            </div>
          </div>
          <div v-if="!isEditParam && this.fitting_method === 'ai_assisted_fitting'"><p class="describe-tip">预测模型系基于实验室数据进行训练得出，其预测结果仅供参考。</p></div>

      </a-spin>
    </a-modal>

    <!-- 电芯选择弹窗 -->
    <a-modal title="电芯选择" width="50vw" height="50vh" :bodyStyle="{ padding: 0 }" :visible="isSampleSelectShow" style="padding: 0" @cancel="isSampleSelectShow = false">
      <div style="padding: 10px;">
        <a-checkbox-group v-model="checkedSampleCodes" :options="sampleCodeList"></a-checkbox-group>
      </div>
      <template slot="footer">
        <a-button key="back" @click="isSampleSelectShow = false">关闭</a-button>
      </template>
    </a-modal>
  </div>

</template>
<script>
import {
  getOnlineReport,
  updateOnlineReport,
  exportOnlineReport,
} from "@/api/modular/system/testProgressManager";
import {calendarCommon} from "./mixin/calendarCommon.js";
import {chartTemplate} from "@/views/system/vTestReport/mixin/chartTemplate";
import jsonBigint from "json-bigint";
import {downloadfile1} from "@/utils/util";
import _ from "lodash";
import moment from "moment";
import axios from "axios";
import {mapGetters} from "vuex";
import {testReportHistoryGet, testReportHistoryList} from "@/api/modular/system/reportManager";
import {updateCalendarOnlineData} from "@/api/modular/system/testReport";
import Vue from "vue";
import {DICT_TYPE_TREE_DATA} from "@/store/mutation-types";

export default {
  components: {},
  mixins: [calendarCommon,chartTemplate],
  data: function () {
    return {
      testProgress: {},
      resultDataJson: {},

      // 工步类型
      stepTypeActive: "2nd",
      type2ndOrCustom: "2nd",
      // 表格数据
      tableList: [],
      absoluteTimeMap: {},
      dcirTiTleList: [],
      recoveryStepTiTle: '',
      dchCeStepTiTleList: [],
      hasChCeStep: false,

      onlineTableLoading: true,
      // 表头
      capRetColumns: [
        {
          title: "累积天数/Day",
          align: "center",
          width: "100px",
          dataIndex: "day",
        },
        {
          title: "绝对时间",
          align: "center",
          width: "100px",
          dataIndex: "absoluteTime",
          customRender: (text, record) => {
            let date = moment(text, 'YYYY/MM/DD HH:mm:ss.SSS')
            return date.isValid() ? date.format('YYYY-MM-DD HH:mm:ss') : text
          }
        },
        {
          title: "保持容量/Ah",
          align: "center",
        },
        {
          title: "容量保持率/%",
          align: "center",
        }
      ],
      capRecColumns: [
        {
          title: "累积天数/Day",
          align: "center",
          width: "100px",
          dataIndex: "day",
        },
        {
          title: "绝对时间",
          align: "center",
          width: "100px",
          dataIndex: "absoluteTime",
          customRender: (text, record) => {
            let date = moment(text, 'YYYY/MM/DD HH:mm:ss.SSS')
            return date.isValid() ? date.format('YYYY-MM-DD HH:mm:ss') : text
          }
        },
        {
          title: "恢复容量/Ah",
          align: "center",
        },
        {
          title: "容量恢复率/%",
          align: "center",
        }
      ],
      energyRetColumns: [
        {
          title: "累积天数/Day",
          align: "center",
          width: "100px",
          dataIndex: "day",
        },
        {
          title: "绝对时间",
          align: "center",
          width: "100px",
          dataIndex: "absoluteTime",
          customRender: (text, record) => {
            let date = moment(text, 'YYYY/MM/DD HH:mm:ss.SSS')
            return date.isValid() ? date.format('YYYY-MM-DD HH:mm:ss') : text
          }
        },
        {
          title: "保持能量/Wh",
          align: "center",
        },
        {
          title: "能量保持率/%",
          align: "center",
        }
      ],
      energyRecColumns: [
        {
          title: "累积天数/Day",
          align: "center",
          width: "100px",
          dataIndex: "day",
        },
        {
          title: "绝对时间",
          align: "center",
          width: "100px",
          dataIndex: "absoluteTime",
          customRender: (text, record) => {
            let date = moment(text, 'YYYY/MM/DD HH:mm:ss.SSS')
            return date.isValid() ? date.format('YYYY-MM-DD HH:mm:ss') : text
          }
        },
        {
          title: "恢复能量/Wh",
          align: "center",
        },
        {
          title: "能量恢复率/%",
          align: "center",
        }
      ],

      // 在线报告编辑类型
      onlineEditObjList: [
        'capRet',
        'capRec',
        'energyRet',
        'energyRec'
      ],
      onlineThubNameMap: {
        capRet: '日历_容量保持率（GB/T 31486-2015）',
        capRec: '日历_容量恢复率&DCIR增长率',
        energyRet: '日历_能量保持率（GB/T 31486-2015）',
        energyRec: '日历_能量恢复率&DCIR增长率',
      },
      onlineTableNameMap: {
        capRet: '保持容量数据',
        capRec: '恢复容量数据',
        energyRet: '保持能量数据',
        energyRec: '恢复能量数据',
      },
      onlineLineNameMap: {
        capRet: '容量保持率',
        capRec: '容量恢复率',
        energyRet: '能量保持率',
        energyRec: '能量恢复率',
      },

      capRetEchartList: [], // 容量保持率图表数据
      capRecEchartList: [], // 容量恢复率图表数据
      energyRetEchartList: [], // 能量保持率图表数据
      energyRecEchartList: [], // 能量恢复率图表数据
      dcirIncEchartList: [], // DCIR增长率图表数据

      thumbnailList: [], //缩略图参数

      // 日历寿命预测相关
      hasPermission: false,
      isFirstShow: true,
      isShowForecastModal: false,
      forecastLoading: false,

      isEditParam: true,
      stepTypeForecast: "2nd",
      productType: null,
      systemOptions: [],
      temperature: null,
      temperatureOptions: [],
      soc: null,
      socOptions: [],
      minInferenceDay: 1,
      maxInferenceDay: 3000,
      soh_threshold: null,
      soh_thresholdOptions: [],
      fitting_method: 'semi_empirical_formule_fitting',
      fitting_methodOptions: [{value: 'semi_empirical_formule_fitting', label: '半经验公式'}, {value: 'ai_assisted_fitting', label: 'AI辅助拟合'}],
      slope: 0,
      intercept: 0,
      fitCheckedType: 'correction',
      optimization_schema: 'mae',
      prefixList: [
        {value: 'init', label: '初始误差'},
        {value: 'correction', label: '修正误差'},
        {value: 'optimize', label: '优化误差'},
      ],
      fitModel: 'init',

      sampleCodeList: [],
      sampleInfoObj: {},

      forecastParams: {},
      sampleRawDataNum: {},
      rptDayList: [],

      showForecastData: true,
      forecastResult: {},
      forecastSampleCodes: [],
      forecastTableList: [],
      forecastTableColumns: [
        {
          title: "累积天数/Day",
          align: "center",
          width: "100px",
          dataIndex: "day",
        },
        {
          title: "绝对时间",
          align: "center",
          width: "100px",
          dataIndex: "absoluteTime",
          customRender: (text, record) => {
            let date = moment(text, 'YYYY/MM/DD HH:mm:ss.SSS')
            return date.isValid() ? date.format('YYYY-MM-DD HH:mm:ss') : text
          }
        },
        {
          title: "恢复容量/Ah",
          align: "center",
        },
        {
          title: "容量恢复率/%",
          align: "center",
        },
        {
          title: "拟合所使用数据(样本均值)",
          align: "center",
          children: [
            {
              title: "fitting_data",
              align: "center",
              width: "100px",
              dataIndex: "fitting_data",
            },
          ],
        },
        {
          title: "拟合结果/%",
          align: "center",
          children: [
            {
              title: "fitting_result",
              align: "center",
              width: "100px",
              dataIndex: "fitting_result",
              scopedSlots: { customRender: 'fitting_result' }
            },
          ],
        },
      ],
      forecastEchartList: [],
      semiFitEchartList: [],
      aiFitEchartList: [],

      historyId: null,
      isUpdateData: false,
      updateDataList: [],
      updateDataColumns: [
        {
          title: "累积天数/Day",
          align: "center",
          width: "100px",
          dataIndex: "day",
        },
        {
          title: "绝对时间",
          align: "center",
          width: "100px",
          dataIndex: "absoluteTime",
          customRender: (text, record) => {
            let date = moment(text, 'YYYY/MM/DD HH:mm:ss.SSS')
            return date.isValid() ? date.format('YYYY-MM-DD HH:mm:ss') : text
          }
        },
        {
          title: "恢复容量/Ah",
          align: "center",
        },
      ],
      selectedRowKeys: [],
      updateEchartList: [],

      isSampleSelectShow: false,
      checkedSampleCodes: [],

      semiEmpiricalMeanErrorObj: {},
      aiMeanErrorObj: {},
      mathJaxLoaded: false,
      formulas: {
        formulaContainer1: '$$ Z : $$', // 斜率
        formulaContainer1tip: '$$ Z $$', // 斜率
        formulaContainer2: '$$ \\ln A - \\frac{E_a}{RT} : $$', // 截距
        formulaContainer2tip: '$$ \\ln A - \\frac{E_a}{RT} $$', // 截距
        formulaContainer3: '$$ Q_{\\text{loss}} = A \\cdot e^{-\\frac{E_a}{RT}} \\cdot t^Z $$' +
          '$$ \\ln(Q_{\\text{loss}}) = \\ln A - \\frac{E_a}{RT} + Z \\cdot \\ln(t) $$' +
          '$$ Q_{\\text{SOH}} = 1 - Q_{\\text{loss}} = 1 - e^\\left(\\ln A - \\frac{E_a}{RT}\\right) \\cdot t^Z $$' +
          '$$ Q_{\\text{loss}}:容量损失率, ~ T:热力学温度, ~ A:指前因子, $$' +
          '$$ E_a:活化能, ~ R:理想气体常数, ~ Z:幂律因子, ~ t:时间 $$', // 半经验公式说明
        formulaContainer4: '平均绝对误差 : $$ \\text{MAE} = \\frac{1}{n} \\sum_{i=1}^{n} |y_i - \\hat{y}_i| $$' +
          '$$ y:实测值, ~ \\hat{y}:拟合值, ~ n:记录点数 $$', // 平均绝对误差
        formulaContainer5: '均方误差 : $$ \\text{MSE} = \\frac{1}{n} \\sum_{i=1}^{n} (y_i - \\hat{y}_i)^2 $$' +
          '$$ y:实测值, ~ \\hat{y}:拟合值, ~ n:记录点数 $$', // 均方误差
        formulaContainer6: '均方根误差 : $$ \\text{RMSE} = \\sqrt{\\frac{1}{n} \\sum_{i=1}^{n} (y_i - \\hat{y}_i)^2} $$' +
          '$$ y:实测值, ~ \\hat{y}:拟合值, ~ n:记录点数 $$', // 均方根误差
      },
      renderedContainers: new Set(),
    }

  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  watch: {
    isUpdateData(newVal, oldVal) {
      this.initEditModeTable()
    },
    sampleCodeList(newVal, oldVal) {
      this.checkedSampleCodes = newVal
      this.initEditModeTable()
    },
    stepTypeForecast(newVal, oldVal) {
      this.initEditModeTable()
      this.initDataEchart('update')
    },
    isEditParam(newVal, oldVal) {
      if (!newVal && this.mathJaxLoaded) {
        const suffixList = ['', 'init1', 'correction1', 'optimize1', 'init2', 'correction2']
        suffixList.forEach(suffix => {
          this.renderFormula(`formulaContainer1${suffix}`, this.formulas.formulaContainer1)
          this.renderFormula(`formulaContainer2${suffix}`, this.formulas.formulaContainer2)
        })
      }
    },
  },
  mounted() {
    this.getOnlineReport()
    this.checkParamCommittedStatus()
    this.loadMathJax()
  },
  created() {
    // 《开始预测》按钮权限：日历寿命预测角色
    this.hasPermission = this.userInfo.roles.some(item => item.id === "1851898200712404994")

    const items = ['system', 'temperature', 'soc', 'soh_threshold']
    items.forEach(item => {
      this.getOptions(`calendar_forecast_${item}`, item)
    })
  },
  destroyed() {
    window.sessionStorage.removeItem('param-committed')
  },
  methods: {
    async loadMathJax() {
      if (!this.mathJaxLoaded && typeof window.MathJax === 'undefined') {
        await this.importMathJax();
        this.mathJaxLoaded = true;
      }
    },
    importMathJax() {
      return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js';
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
      });
    },
    renderFormula(refName, formula) {
      this.$nextTick(() => {
        console.log("renderFormula: ", refName)
        let container = this.$refs[refName];
        if (Array.isArray(container)) {
          container = container[0]
        }
        if (container && !this.renderedContainers.has(container)) {
          container.innerHTML = `${formula}`;
          if (this.mathJaxLoaded) {
            window.MathJax.typesetPromise([container])
              .then(() => {
                console.log(`Typeset complete for ${refName}`);
                // 将容器添加到已渲染列表中
                this.renderedContainers.add(container);
              })
              .catch(console.error);
          }
        }
      })
    },
    changeVisible(visible) {
      if(visible && this.mathJaxLoaded) {
        this.renderFormula('formulaContainer1tip', this.formulas.formulaContainer1tip)
        this.renderFormula('formulaContainer2tip', this.formulas.formulaContainer2tip)
        this.renderFormula('formulaContainer3', this.formulas.formulaContainer3)

        const suffixList = ['init1', 'correction1', 'optimize1', 'init2', 'correction2']
        for (let i = 4; i <= 6; i++) {
          suffixList.forEach(suffix => {
            this.renderFormula(`formulaContainer${i}${suffix}`, this.formulas[`formulaContainer${i}`])
          })
        }
      }
    },

    getColumns(item) {
      return this[item + 'Columns'];
    },
    getOnlineReport() {
      this.onlineTableLoading = true

      let param = {}
      if (this.$route.query.testProgressId) {
        param = { id: this.$route.query.testProgressId }
      } else if (this.$route.query.id) {
        param = { ordtaskid: this.$route.query.id }
      }
      getOnlineReport(param).then(res => {
        this.testProgress = res.data ? res.data : {};

        // 存在自定义工步则默认展示自定义数据
        if (this.testProgress && this.testProgress.customLogicFlag == 1) {
          this.stepTypeActive = "Custom"
          this.type2ndOrCustom = "Custom"
        }

        this.testCondition = this.convertValue(this.testProgress.t) + "℃"
          + (this.testProgress.humidity ? "_" + this.testProgress.humidity + "%RH" : "")
          + (this.testProgress.soc ? "_" + this.convertValue(this.testProgress.soc) + "%SOC" : "");

        const folderAndProjectName = "委托单号：" + this.convertValue(this.testProgress.testCode)
          + " 测试项目 " + this.testCondition + (this.convertValue(this.testProgress.testPeriod) ? "_" + this.convertValue(this.testProgress.testPeriod) + "天" : "")
          + (this.$route.query.alias ? "_" + this.$route.query.alias : "")

        this.$emit("titleChange", folderAndProjectName)

        this.testCondition = this.testCondition + "_"

        let json = jsonBigint({storeAsString: true})
        if (this.testProgress.resultDataJson) {
          this.resultDataJson = json.parse(this.testProgress.resultDataJson);
          this.dcirTiTleList = Array.isArray(this.resultDataJson.dcirTiTleList) ? this.resultDataJson.dcirTiTleList : []
          this.recoveryStepTiTle = this.resultDataJson.recoveryStepTiTle
          this.dchCeStepTiTleList = Array.isArray(this.resultDataJson.dchCeStepTiTleList) ? this.resultDataJson.dchCeStepTiTleList.filter(item => typeof item === 'string' && item.length > 0) : []
          this.hasChCeStep = this.resultDataJson.hasChCeStep
        }
        this.tableList = this.resultDataJson && this.resultDataJson.tableList ? this.resultDataJson.tableList : []
        for (let j = 0; j < this.tableList.length; j++) {
          const rowObj = this.tableList[j]
          this.absoluteTimeMap[rowObj.day] = rowObj.absoluteTime
        }

        this.initOnlineReport()
      }).then(() => {
        // 获取预测数据修改记录id
        testReportHistoryList({reportId:this.testProgress.id}).then(res => {
          const historyList = res.data
          const history = historyList.find( item => item.type === 'CalendarOnline' && (item.createAccount === this.userInfo.account || item.createName === this.userInfo.name) )
          if (history) {
            this.historyId = history.id
          }
        })
      })
    },
    async initOnlineReport() {
      // 在线报告表格处理
      this.initOnlineTable()
      this.onlineTableLoading = false

      await this.getChartTemplateRelationList(this.$route.query.testProgressId,this.onlineEditObjList)

      // 获取Echarts图初始数据
      this.onlineEditObjList.forEach(v => {
        this.chartEditData[v] = this._getInitData(v, 'edit')
        this.chartResetOriginal[v] = this._getInitData(v)
      })

      // echarts数据
      this.refreshOnlineEchartList()

      // Echarts图初始化
      this.onlineEditObjList.forEach(editObj => {
        this.initNormalEchart(editObj)
      })
    },
    convertValue(value) {
      if (value === null || value === undefined) {
        return "";
      }
      if (typeof value === 'string' && value.endsWith('%')) {
        return value.replace("%", "");
      }
      return value;
    },
    initOnlineTable() {
      // 以初检RPT电芯为准，处理表头
      if (this.tableList.length > 0) {
        const primaryStatusMap = this.resultDataJson.primaryStatusMap || {}

        let primaryObjectList = this.tableList[0].primaryObjectList ? this.tableList[0].primaryObjectList : []

        const dcirCalcType = this.resultDataJson.dcirCalcType || "DongLi"
        const titleList = this.stepTypeActive === "Custom" && dcirCalcType === "V" ? ["U1-DCH/V", "I1-DCH/A", "U2-DCH/V", "I2-DCH/A", "DCIR/mΩ", "DCIR增长率/%"] : ["U1-rest/V", "U2-DCH/V", "I2-DCH/A", "DCIR/mΩ", "DCIR增长率/%"]
        if (this.capRecColumns.length > 4) {
          this.capRecColumns.length = 4
        }
        if (this.energyRecColumns.length > 4) {
          this.energyRecColumns.length = 4
        }

        if (this.stepTypeActive === "Custom") {
          if (this.recoveryStepTiTle) {
            this.capRecColumns[2].title = this.recoveryStepTiTle + '_' + '恢复容量/Ah'
            this.energyRecColumns[2].title = this.recoveryStepTiTle + '_' + '恢复能量/Wh'
          }
          for (let i = 0; i < this.dchCeStepTiTleList.length; i++) {
            this.capRecColumns.push(
                {
                  title: this.dchCeStepTiTleList[i] + "_放电容量/Ah",
                  align: "center",
                  children: []
                }
            )
            this.energyRecColumns.push(
                {
                  title: this.dchCeStepTiTleList[i] + "_放电能量/Wh",
                  align: "center",
                  children: []
                }
            )
          }

          this.dcirTiTleList.forEach(dcirTiTle => {

            const columns = [...titleList.map(title => {
                  return {
                    title: (dcirTiTle ? dcirTiTle + '_' : '') + title,
                    align: "center",
                    children: [],
                  }
                }
            )]

            this.capRecColumns.push(...columns)
            this.energyRecColumns.push(...columns)
          })

          if (this.hasChCeStep) {
            this.capRecColumns.push(
                {
                  title: "充电容量/Ah",
                  align: "center",
                  children: []
                },
                {
                  title: "充电恒流比",
                  align: "center",
                  children: []
                }
            )
            this.energyRecColumns.push(
                {
                  title: "充电能量/Wh",
                  align: "center",
                  children: []
                }
            )
          }
        } else {
          const columns = [...titleList.map(title => {
                return {
                  title: title,
                  align: "center",
                  children: []
                }
              }
          )]
          this.capRecColumns.push(...columns)
          this.energyRecColumns.push(...columns)
        }

        if (primaryObjectList.length > 0) {
          // children初始化
          this.capRetColumns[2].children = []
          this.capRetColumns[3].children = []
          this.energyRetColumns[2].children = []
          this.energyRetColumns[3].children = []
          this.capRecColumns[2].children = []
          this.capRecColumns[3].children = []
          this.energyRecColumns[2].children = []
          this.energyRecColumns[3].children = []
        }

        const keyTypeList = this.stepTypeActive === "Custom" && dcirCalcType === "V" ? ["dchVoltage", "dchCurrent", "dchVoltage2-", "dchCurrent2-", "dcir2-", "dcirIncRate"] : ["restVoltage", "dchVoltage", "dchCurrent", "dcir", "dcirIncRate"]

        for (let i = 0; i < primaryObjectList.length; i++) {
          let primaryObject = primaryObjectList[i]
          let sampleCode = primaryObject.sampleCode
          let batteryCode = primaryObject.batteryCode
          let batteryStatus = primaryStatusMap[sampleCode] || this.testProgress.testStatus

          this.capRetColumns[2].children.push(
            this.getOnlineChildren(sampleCode, batteryCode, batteryStatus, i, "retCap")
          )
          this.capRetColumns[3].children.push(
            this.getOnlineChildren(sampleCode, batteryCode, batteryStatus, i, "capRetRate")
          )

          this.capRecColumns[2].children.push(
            this.getOnlineChildren(sampleCode, batteryCode, batteryStatus, i, "recCap", false)
          )
          this.capRecColumns[3].children.push(
            this.getOnlineChildren(sampleCode, batteryCode, batteryStatus, i, "capRecRate", false)
          )

          this.energyRetColumns[2].children.push(
            this.getOnlineChildren(sampleCode, batteryCode, batteryStatus, i, "retEnergy")
          )
          this.energyRetColumns[3].children.push(
            this.getOnlineChildren(sampleCode, batteryCode, batteryStatus, i, "energyRetRate")
          )

          this.energyRecColumns[2].children.push(
            this.getOnlineChildren(sampleCode, batteryCode, batteryStatus, i, "recEnergy", false)
          )
          this.energyRecColumns[3].children.push(
            this.getOnlineChildren(sampleCode, batteryCode, batteryStatus, i, "energyRecRate", false)
          )

          if (this.stepTypeActive === "Custom") {
            for (let j = 0; j < this.dchCeStepTiTleList.length; j++) {
              this.capRecColumns[4 + j].children.push(
                  this.getOnlineChildren(sampleCode, batteryCode, batteryStatus, i, 'dchCapacity'+j, true, true)
              )
              this.energyRecColumns[4 + j].children.push(
                  this.getOnlineChildren(sampleCode, batteryCode, batteryStatus, i, 'dchEnergy'+j, true, true)
              )
            }

            this.dcirTiTleList.forEach((dcirTiTle, dcirIndex) => {
              keyTypeList.forEach((keyType, keyIndex) => {
                this.capRecColumns[4 + this.dchCeStepTiTleList.length + dcirIndex * keyTypeList.length + keyIndex].children.push(
                    this.getOnlineChildren(sampleCode, batteryCode, batteryStatus, i, keyType + dcirIndex, true, true)
                )
              })
            })

            if (this.hasChCeStep) {
              this.capRecColumns[4 + this.dchCeStepTiTleList.length + this.dcirTiTleList.length * keyTypeList.length].children.push(
                  this.getOnlineChildren(sampleCode, batteryCode, batteryStatus, i, 'chCapacity', true, true)
              )
              this.capRecColumns[5 + this.dchCeStepTiTleList.length + this.dcirTiTleList.length * keyTypeList.length].children.push(
                  this.getOnlineChildren(sampleCode, batteryCode, batteryStatus, i, 'chCccapacityRate', true, true)
              )
              this.energyRecColumns[4 + this.dchCeStepTiTleList.length + this.dcirTiTleList.length * keyTypeList.length].children.push(
                  this.getOnlineChildren(sampleCode, batteryCode, batteryStatus, i, 'chEnergy', true, true)
              )
            }
          } else {
            for (let j = 0; j < keyTypeList.length; j++) {
              this.capRecColumns[j + 4].children.push(
                  this.getOnlineChildren(sampleCode, batteryCode, batteryStatus, i, keyTypeList[j])
              )
            }
          }

        }
      }
    },
    getOnlineChildren(sampleCode, batteryCode, batteryStatus, rptIndex, type, ignore3rdAnd4th = true, ignoreSuffix = false) {
      let tips
      // 前端表头状态转换
      switch (batteryStatus) {
        case 'earlyEnd': tips = '状态正常-提前结束'; break;
        case 'batteryDisassembly': tips = '状态正常-电池拆解'; break;
        case 'pressureDrop': tips = '掉压失效-终止测试'; break;
        case 'abnormalHot': tips = '异常发热-终止测试'; break;
        case 'openShellAndLeak': tips = '开壳漏液-终止测试'; break;
        case 'shellRust': tips = '壳体生锈-终止测试'; break;
        case 'operationError': tips = '作业错误-终止测试'; break;
        case 'thermalRunaway': tips = '热失控-终止测试'; break;
        case 'acrException': tips = '内阻异常-终止测试'; break;
        default: tips = ''; break;
      }
      if (["earlyEnd", "batteryDisassembly", "pressureDrop", "abnormalHot", "openShellAndLeak", "shellRust", "operationError", "thermalRunaway", "acrException"].includes(batteryStatus)) {
        batteryStatus = "Stop"
      } else if (batteryStatus === "ongoing") {
        batteryStatus = "Ongoing"
      }
      let result = {
        title: batteryCode,
        align: "center",
        width: "100px",
        children: [
          {
            title: <a-tooltip title={tips}>{batteryStatus}</a-tooltip>,
            align: "center",
            width: "100px",
            customRender: (text, record) => {
              return ignoreSuffix ? record.primaryObjectList[rptIndex][`${type}`]
                  : ignore3rdAnd4th ? record.primaryObjectList[rptIndex][`${type}${this.type2ndOrCustom}`]
                : record.primaryObjectList[rptIndex][`${type}${this.stepTypeActive}`];
            }
          }
        ]
      }

      if (sampleCode !== batteryCode) {
        result = {
          title: sampleCode,
          align: "center",
          width: "100px",
          children: [
            result
          ]
        }
      }

      return result
    },

    refreshOnlineEchartList() {
      if (this.resultDataJson) {
        this.capRetEchartList = this.resultDataJson[`capRetRate${this.type2ndOrCustom}EchartList`] ?? []
        this.capRecEchartList = this.resultDataJson[`capRecRate${this.stepTypeActive}EchartList`] ?? []
        this.energyRetEchartList = this.resultDataJson[`energyRetRate${this.type2ndOrCustom}EchartList`] ?? []
        this.energyRecEchartList = this.resultDataJson[`energyRecRate${this.stepTypeActive}EchartList`] ?? []
        this.dcirIncEchartList = this.resultDataJson[`dcirIncRate${this.type2ndOrCustom}EchartList`] ?? []

        // 缩略图参数赋值
        this.thumbnailList = this.onlineEditObjList.map(item => {
          return {id: item, subheading: this.onlineThubNameMap[item], show: true, echartList: this[`${item}EchartList`], echartY2List: item.includes('Rec') ? this.dcirIncEchartList : []}
        })
      }
    },

    stepTypeChange(e) {
      this.stepTypeActive = e.target.value
      const preType = this.type2ndOrCustom
      this.type2ndOrCustom = this.stepTypeActive === "Custom" ? "Custom" : "2nd"

      this.initOnlineTable()

      // 刷新echarts数据
      this.refreshOnlineEchartList()

      if (preType !== this.type2ndOrCustom) {
        // 自定义DCIR可能有多个，而且命名不同，type2ndOrCustom变化需重新对存在DCIR的Echarts图初始化
        this.chartFrist.capRec = true
        this.chartFrist.energyRec = true
        this.initNormalEchart('capRec')
        this.initNormalEchart('energyRec')
      } else {
        // 更新echarts数据集
        this.resetEchartsDataset('capRec', this.capRecEchartList, this.dcirIncEchartList)
        this.resetEchartsDataset('energyRec', this.energyRecEchartList, this.dcirIncEchartList)
      }
      // 更新echarts数据集
      this.resetEchartsDataset('capRet', this.capRetEchartList)
      this.resetEchartsDataset('energyRet', this.energyRetEchartList)
    },
    resetEchartsDataset(editObj, echartList, dcirIncList = []) {
      const option = {series: []}

      let series = this.chartEditData[editObj].seriesList

      if (dcirIncList.length > 0) {
        // 双Y轴
        for (let i = 0, j = 0; i < series.length; i += 2, j++) {
          series[i].data = echartList[j].data.map((mapItem, index) => {
            let absoluteTime = ''
            if (Array.isArray(mapItem) && mapItem.length > 0) {
              absoluteTime = this.absoluteTimeMap[mapItem[0]];
            }
            return {id: index, value: mapItem, lineName: this.onlineLineNameMap[editObj], absoluteTime: absoluteTime}
          })
        }
      } else {
        // 单Y轴
        for (let i = 0; i < series.length; i++) {
          series[i].data = echartList[i].data.map((mapItem, index) => {
            let absoluteTime = ''
            if (Array.isArray(mapItem) && mapItem.length > 0) {
              absoluteTime = this.absoluteTimeMap[mapItem[0]];
            }
            return {id: index, value: mapItem, lineName: this.onlineLineNameMap[editObj], absoluteTime: absoluteTime}
          })
        }
      }

      option.series = series

      this.echartObj[editObj].setOption(option)
    },
    editCustomLogic() {
      if (this.testProgress.queryParam !== null) {
        this.$store.commit("setTaskID", this.testProgress.customLogicFlag);
        this.$store.commit("setTaskFilterData", this.testProgress.queryParam);
        this.$router.push({
          path: "/calendarCustomLogic",
          query: {
            testProgressId: this.testProgress.id
          }
        })
      } else {
        this.$message.warning("暂无数据！")
      }
    },
    updateOnlineReport() {
      let param = _.cloneDeep(this.testProgress)
      this.testProgress.onlineReportStatus = 40 // 更新中
      updateOnlineReport(param).then(res => {
        if (res.success) {
          this.testProgress = res.data

          if (this.testProgress.onlineReportStatus == 40 || this.testProgress.onlineReportStatus == 10) {
            return // 更新中或创建中
          }

          // 刷新在线报告数据
          let json = jsonBigint({storeAsString: true})
          if (this.testProgress.resultDataJson) {
            this.resultDataJson = json.parse(this.testProgress.resultDataJson);
            this.dcirTiTleList = Array.isArray(this.resultDataJson.dcirTiTleList) ? this.resultDataJson.dcirTiTleList : []
            this.recoveryStepTiTle = this.resultDataJson.recoveryStepTiTle
            this.dchCeStepTiTleList = Array.isArray(this.resultDataJson.dchCeStepTiTleList) ? this.resultDataJson.dchCeStepTiTleList.filter(item => typeof item === 'string' && item.length > 0) : []
            this.hasChCeStep = this.resultDataJson.hasChCeStep
          }
          this.tableList = this.resultDataJson && this.resultDataJson.tableList ? this.resultDataJson.tableList : []
          for (let j = 0; j < this.tableList.length; j++) {
            const rowObj = this.tableList[j]
            this.absoluteTimeMap[rowObj.day] = rowObj.absoluteTime
          }

          // 更新初始值设置
          this.onlineEditObjList.forEach(editObj => {
            this.chartFrist[editObj] = true
          })
          this.globalXMax = 0 // 防止多一个格子失效

          // 更新在线报告数据
          this.initOnlineReport()
        }
      })
    },
    exportOnlineReport() {
      exportOnlineReport({
        stepTypeActive: this.stepTypeActive,
        type2ndOrCustom: this.type2ndOrCustom,
        testProgressId: this.testProgress.id
      }).then(res => {
        if (res.data.size > 0) {
          const alias = this.testProgress.testAlias ? this.testProgress.testAlias : this.testProgress.testProject
          const fileName = this.testProgress.testCode + '-' + this.testProgress.applicant + '-' + alias + '-' + '在线数据' + '.xlsx'
          downloadfile1(res, fileName)
        } else {
          this.$message.warning("暂无数据！")
        }
      })
    },

    checkParamCommittedStatus() {
      // 使用 setInterval 定期检查提交自定义参数是否处理完成
      const intervalId = setInterval(() => {
        const isCommitted = window.sessionStorage.getItem('param-committed');

        // 无param-committed参数，停止定时器
        if (!isCommitted) {
          clearInterval(intervalId);
          return;
        }

        // 提交自定义参数后返回，有param-committed参数
        if (isCommitted != 'start') {
          // 提交了param-committed参数，且从初始值'start'转为true或false，停止定时器
          clearInterval(intervalId);
          // 请求已完成，可以更新报告
          window.sessionStorage.removeItem('param-committed');
          // 更新初始值设置
          for (let i = 0; i < this.onlineEditObjList.length; i++) {
            this.chartFrist[this.onlineEditObjList[i]] = true;
          }
          this.globalXMax = 0 // 防止多一个格子失效
          this.getOnlineReport();
        }

      }, 1000); // 每秒检查一次
    },

    /**
     * 日历寿命预测相关
     */
    getOptions(code, type) {
      const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
      const filter = dictTypeTree.filter(item => item.code == code)
      this[`${type}Options`] = []
      if (filter.length > 0) {
        this[`${type}Options`] = filter[0].children.map(item => {
          return type === 'system' ? {value: item.code, label: item.code, system: item.name} : {value: item.code, label: item.name}
        })
      }
    },
    openForecastModal() {
      if (this.isFirstShow) {
        // 校验：至少4个记录点，第一个记录点是0天，最后一个记录点>=28天
        if (this.tableList.length < 4) {
          return this.$message.warning('至少需要4个中检天数的恢复容量数据！')
        }
        if (parseInt(this.tableList[0].day) !== 0) {
          return this.$message.warning('暂无初检数据，不符合预测条件！')
        }
        if (parseInt(this.tableList[this.tableList.length - 1].day) < 28) {
          return this.$message.warning('中检天数小于28天，不符合预测条件！')
        }

        /* 化学体系、温度、SOC根据产品信息赋初始值，与可选项不对应则取第一个选项 */
        const productName = this.testProgress.productName || '';
        // 动态生成正则表达式
        const systemCodes = this.systemOptions.map(item => item.value);
        const pattern = new RegExp(`(${systemCodes.join('|')})`); // pattern: /(G11|29V|G26)/
        const match = pattern.exec(productName);
        this.productType = match ? match[0] : systemCodes[0];

        const tempCodes = this.temperatureOptions.map(item => item.value);
        const tempIndex = tempCodes.findIndex(item => parseFloat(item) == parseFloat(this.testProgress.t));
        this.temperature = tempIndex !== -1 ? tempCodes[tempIndex] : tempCodes[0];

        const socCodes = this.socOptions.map(item => item.value);
        const socIndex = socCodes.findIndex(item => parseFloat(item) == parseFloat(this.testProgress.soc));
        this.soc = socIndex !== -1 ? socCodes[socIndex] : socCodes[0];

        const soh_thresholdCodes = this.soh_thresholdOptions.map(item => item.value);
        this.soh_threshold = soh_thresholdCodes.length > 0 ? soh_thresholdCodes[0] : null;

        this.minInferenceDay = parseInt(this.tableList[this.tableList.length - 1].day) + 1

        // 首次进入参数编辑界面，需要根据第一行数据获取排序的电芯列表及电芯表头信息
        this.sampleCodeList = []
        this.sampleInfoObj = {}
        const primaryObjectList = this.tableList[0].primaryObjectList || []
        primaryObjectList.forEach((primaryObj, sampleIndex) => {
          let sampleCode = primaryObj.sampleCode
          this.sampleCodeList.push(sampleCode)
          this.sampleInfoObj[sampleCode] = {batteryCode: primaryObj.batteryCode, sampleIndex: sampleIndex}
        })

        this.selectedRowKeys = this.tableList.map(item => {return parseInt(item.day)})

        // modal内容初始化
        this.isShowForecastModal = true
        this.stepTypeForecast = this.stepTypeActive
        this.startEditParam()

        this.isFirstShow = false
      } else {
        this.isShowForecastModal = true
      }
    },
    startEditParam() {
      this.isEditParam = true
      this.getUpdateDataList()
    },
    getUpdateDataList() {
      this.forecastLoading = true

      // 根据tableList更新表格数据，可能与history存的数据不同（不含预测数据）
      this.updateDataList = []
      this.tableList.forEach((rptDataObj, index) => {
        const day = parseInt(rptDataObj.day)
        const list = rptDataObj.primaryObjectList || []
        let updateRptObj = {rowIndex: index, day: day, absoluteTime: rptDataObj.absoluteTime}
        this.sampleCodeList.forEach((sampleCode, codeIndex) => {
          updateRptObj[sampleCode] = _.cloneDeep(list[codeIndex])
        })
        this.updateDataList.push(updateRptObj)
      })

      if (this.historyId) {
        testReportHistoryGet({id:this.historyId}).then(res => {
          const history = res.data
          if (history && history.updateDataJson) {
            // 存在编辑记录，将修改值赋给updateDataList
            const oldUpdateList = JSON.parse(history.updateDataJson)
            oldUpdateList.forEach(oldRptObj => {
              const rptIndex = this.updateDataList.findIndex(item => item.day == oldRptObj.day)
              if (rptIndex !== -1) {
                this.sampleCodeList.forEach(sampleCode => {
                  if (sampleCode in oldRptObj) {
                    const filterKeys = Object.keys(oldRptObj[sampleCode]).filter(key => key.includes('Edit'))
                    filterKeys.forEach(filterKey => {
                      this.$set(this.updateDataList[rptIndex][sampleCode], filterKey, oldRptObj[sampleCode][filterKey])
                      // this.updateDataList[rptIndex][sampleCode][filterKey] = rptObj[sampleCode][filterKey]
                    })
                  }
                })
              }
            })
          }

          // 初始化容量数据echarts图表
          this.initDataEchart('update')
        }).finally(() => {
          this.forecastLoading = false
        })
      } else {
        // 初始化容量数据echarts图表
        this.initDataEchart('update')
        this.forecastLoading = false
      }
    },
    initEditModeTable() {
      this.forecastLoading = true

      this.updateDataColumns[2].children = []
      const primaryStatusMap = this.resultDataJson.primaryStatusMap || {}
      this.sampleCodeList.forEach(sampleCode => {
        let batteryStatus = primaryStatusMap[sampleCode] || this.testProgress.testStatus
        let batteryCode = this.sampleInfoObj[sampleCode].batteryCode || sampleCode
        this.updateDataColumns[2].children.push(
          this.getForecastChildren(sampleCode, batteryCode, batteryStatus, `recCap${this.stepTypeForecast}`, true)
        )
      })

      this.forecastLoading = false
    },
    initDataEchart(targetObj) {
      // 检查是否存在 ECharts 实例 并清除
      if (this.echartObj[targetObj]) {
        this.echartObj[targetObj].dispose();
      }

      let seriesDataList = []
      this.sampleCodeList.forEach(sampleCode => {
        seriesDataList.push({sampleCode: sampleCode, data:[]})
      })
      for (let i = 0; i < this.updateDataList.length; i++) {
        const rptObj = this.updateDataList[i]
        this.sampleCodeList.forEach((sampleCode, sampleIndex) => {
          const recCap = rptObj[sampleCode][`recCap${this.stepTypeForecast}Edit`] || rptObj[sampleCode][`recCap${this.stepTypeForecast}`]
          seriesDataList[sampleIndex].data.push([rptObj.day, typeof recCap === 'number' ? recCap.toFixed(3) : recCap])
        })
      }

      let legendList = []
      let seriesList = []
      for (let i = 0; i < seriesDataList.length; i++) {
        const dataObj = seriesDataList[i]
        let series = {
          index: i + 1,
          id: dataObj.sampleCode,
          name: dataObj.sampleCode,
          type: "line",
          barGap: 0,
          markPoint: {
            data: []
          },
          symbol: 'rect',
          symbolSize: 5,
          lineStyle: {
            width: 1,
            type: 'solid',
            color: this.echartsColor[i % this.echartsColor.length]
          },
          itemStyle: {
            color: this.echartsColor[i % this.echartsColor.length]
          },
          emphasis: {
            focus: "series"
          },
          data: dataObj.data.map((mapItem, index) => {
            let absoluteTime = ''
            if (Array.isArray(mapItem) && mapItem.length > 0) {
              absoluteTime = this.absoluteTimeMap[mapItem[0]];
            }
            return {id: index, value: mapItem, absoluteTime: absoluteTime}
          }),
        }
        seriesList.push(series)
        legendList.push(dataObj.sampleCode)
      }

      const options = {
        backgroundColor: '#ffffff',
        animationDuration: 2000,
        textStyle: {
          fontFamily: "Times New Roman"
        },
        tooltip: {
          trigger: "axis",
          confine: true,
          enterable: true,
          hideDelay: 300,
          extraCssText: 'max-height: 400px; overflow-y: auto; scrollbar-width: thin; scrollbar-color: #888 #f1f1f1; pointer-events: auto;',
          formatter: function (params) {
            // 添加 在线报告绝对时间
            let absoluteTime = ''
            if (params[0].data.absoluteTime || params[1].data.absoluteTime) {
              let date = moment(params[0].data.absoluteTime, 'YYYY/MM/DD HH:mm:ss.SSS')
              absoluteTime = date.isValid() ? date.format('YYYY-MM-DD HH:mm:ss') : params[0].data.absoluteTime
            }

            var result = params[0].axisValue + '<div style="width:20px;display: inline-block;"></div>' + absoluteTime + "<br>" // 添加 x 轴的数值
            params.forEach(function (item, dataIndex) {
              result +=
                item.marker +
                item.seriesName +
                '恢复容量' +
                '<div style="width:20px;display: inline-block;"></div>' +
                (item.value[1] !== undefined && item.value[1] !== null && item.value[1] !== '-' ? item.value[1] : "") +
                "<br>" // 添加每个系列的数值
            })
            // 直接返回内容，滚动由 extraCssText 控制
            return result
          }
        },

        title: {
          text: this.testCondition + "Calendar Life",
          left: "center",
          top: 8,
          textStyle: {
            fontSize: 18,
            fontWeight: 500,
            color: '#000'
          }
        },
        grid: {
          show: true,
          top: 40,
          left: 77,
          bottom: 70,
          borderWidth: 0.5,
        },
        legend: {
          data: legendList.splice(0, 6),
          itemWidth: 20,
          itemHeight: 5,
          itemGap: 5,
          orient: 'vertical',
          right: 70,
          bottom: 80,
          padding: [0, 0],
          textStyle: {
            fontSize: 12,
            color: "#000000"
          }
        },
        xAxis: [
          {
            name: 'Storage Time / D',
            type: 'value',
            splitLine: {
              show: true, // 显示分隔线
              lineStyle: {
                type: "solid",
                width: 0.5
              }
            },
            axisLabel: {
              show: true,
              width: 0.5,
              textStyle: {
                fontSize: "15",
                color: "#000000"
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              },
              onZero: false, // 次Y轴为数值轴且包含0刻度, 确保X轴的轴线不在次Y轴的0刻度上
            },
            nameLocation: "middle", // 将名称放在轴线的中间位置
            nameGap: 35,
            nameTextStyle: {
              fontSize: 14,
              color: "#000000" // 可以根据需要调整字体大小
            },
            minInterval: 1,
          }
        ],
        yAxis: [
          {
            name: 'Recovery Capacity / Ah',
            type: 'value',
            position: "left",
            splitLine: {
              show: true, // 显示分隔线
              lineStyle: {
                type: "solid",
                width: 0.5
              }
            },
            axisTick: {
              show: true // 显示刻度
            },
            axisLabel: {
              show: true,
              width: 0.5,
              textStyle: {
                fontSize: "15",
                color: "#000000"
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              }
            },
            nameLocation: "middle", // 将名称放在轴线的起始位置
            nameGap: 38,
            nameRotate: 90, // 旋转角度，使名称竖排
            nameTextStyle: {
              fontSize: 16, // 可以根据需要调整字体大小
              fontWeight: 500,
              color: "#000000"
            }
          }
        ],
        series: seriesList
      }

      this.$nextTick(() => {
        // this.echartObj[targetObj] = this.echarts.init(document.getElementById(targetObj), 'walden', { renderer: "svg" }) // 测试
        this.echartObj[targetObj] = this.echarts.init(document.getElementById(targetObj), 'walden', {devicePixelRatio: 2})
        this.echartObj[targetObj].clear()
        this.echartObj[targetObj].setOption(options)
      })

    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      // 第一行必选
      const firstDay = parseInt(this.tableList[0].day)
      if (!this.selectedRowKeys.includes(firstDay)) {
        this.$message.warn("第一行必选！")
        this.selectedRowKeys.push(firstDay)
      }
    },
    submitUpdate(calcFlag = false) {
      this.forecastLoading = true

      // 删除编辑属性及重新计算容量恢复率
      if (calcFlag && this.updateDataList.length > 0) {
        let initCapObj = {}
        this.sampleCodeList.forEach((sampleCode, index) => {
          if (this.updateDataList[0][sampleCode][`recCap${this.stepTypeForecast}Edit`]) {
            // 电芯编辑了第一行的容量，所有行的恢复率均需重新计算
            const newInitCap = this.updateDataList[0][sampleCode][`recCap${this.stepTypeForecast}Edit`]
            this.updateDataList.forEach(rptObj => {
              let cap = rptObj[sampleCode][`recCap${this.stepTypeForecast}Edit`] ? rptObj[sampleCode][`recCap${this.stepTypeForecast}Edit`] : rptObj[sampleCode][`recCap${this.stepTypeForecast}`]
              rptObj[sampleCode][`capRecRate${this.stepTypeForecast}Edit`] = cap && newInitCap ? this.roundToFixed(cap / newInitCap * 100, 2) : '-'
            })
          } else {
            // 为空值时需要删除编辑属性
            delete this.updateDataList[0][sampleCode][`recCap${this.stepTypeForecast}Edit`]
            this.updateDataList.forEach(rptObj => {
              delete rptObj[sampleCode][`capRecRate${this.stepTypeForecast}Edit`]
            })
            // 保存未编辑的初始容量数据
            initCapObj[sampleCode] = this.updateDataList[0][sampleCode][`recCap${this.stepTypeForecast}`]
          }
        })

        for (let i = 1; i < this.updateDataList.length; i++) {
          const rptObj = this.updateDataList[i]
          this.sampleCodeList.forEach((sampleCode, index) => {
            if (rptObj[sampleCode][`recCap${this.stepTypeForecast}Edit`]) {
              if (sampleCode in initCapObj) {
                let cap = rptObj[sampleCode][`recCap${this.stepTypeForecast}Edit`]
                rptObj[sampleCode][`capRecRate${this.stepTypeForecast}Edit`] = this.roundToFixed(cap / initCapObj[sampleCode] * 100, 2)
              }
            } else {
              // 为空值时需要删除编辑属性
              delete rptObj[sampleCode][`recCap${this.stepTypeForecast}Edit`]
              if (sampleCode in initCapObj) {
                delete rptObj[sampleCode][`capRecRate${this.stepTypeForecast}Edit`]
              }
            }
          })
        }
      }

      updateCalendarOnlineData(this.updateDataList, this.historyId ? this.historyId : this.testProgress.id)
        .then(res => {
          if(res.data) {
            this.$message.success("修改成功")
            this.historyId = res.data.id

            // 更新echarts图
            this.initDataEchart('update')
          }
        }).finally(() => {
          this.forecastLoading = false
          this.isUpdateData = false
        })
    },
    clearUpdate() {
      // 清除所有编辑数据
      this.updateDataList.forEach(rptObj => {
        this.sampleCodeList.forEach(sampleCode => {
          if (sampleCode in rptObj) {
            const filterKeys = Object.keys(rptObj[sampleCode]).filter(key => key.includes('Edit'))
            filterKeys.forEach(filterKey => {
              delete rptObj[sampleCode][filterKey]
            })
          }
        })
      })

      this.submitUpdate(false)
    },
    changeFittingMethod() {
      this.maxInferenceDay = this.fitting_method === 'semi_empirical_formule_fitting' ? 3000 : 1000
    },

    forecastSwitchChange() {
      this.forecastLoading = true
      this.chartFrist.forecast = true
      this.initNormalEchart('forecast')
      this.forecastLoading = false
    },
    startForecast(is_fit_correction = false, is_fit_optimize = false) {
      if (!is_fit_optimize) {
        this.fitCheckedType = 'correction'
      }

      if (this.isUpdateData) {
        return this.$message.warn("正在编辑数据，请提交后预测")
      }

      // 校验：至少4个记录点，第一个记录点是0天，最后一个记录点>=28天
      if (this.updateDataList.length < 4 || this.selectedRowKeys.length < 4) {
        return this.$message.warning('至少需要4个中检天数的恢复容量数据！')
      }
      this.selectedRowKeys.sort((a, b) => a - b)
      if (this.selectedRowKeys[0] !== parseInt(this.updateDataList[0].day)) {
        return this.$message.warning('第一行必选！')
      }
      if (this.selectedRowKeys[this.selectedRowKeys.length - 1] < 28) {
        return this.$message.warning('中检天数小于28天，不符合预测条件！')
      }

      let sample_data_raw = {}
      this.checkedSampleCodes.forEach(sampleCode => {
        sample_data_raw[sampleCode] = {storage_days:[], recovery_capacities:[]}
      })

      // 将数据存入JSON
      this.rptDayList = []
      for (let i = 0; i < this.updateDataList.length; i++) {
        const rptDataObj = this.updateDataList[i]
        const day = parseInt(rptDataObj.day)

        if (!this.selectedRowKeys.includes(day)) {
          continue;
        }

        for (let j = 0; j < this.checkedSampleCodes.length; j++) {
          let sampleCode = this.checkedSampleCodes[j]
          if (rptDataObj[sampleCode][`recCap${this.stepTypeForecast}Edit`] || rptDataObj[sampleCode][`recCap${this.stepTypeForecast}`]) {
            sample_data_raw[sampleCode].storage_days.push(day)
            sample_data_raw[sampleCode].recovery_capacities.push(rptDataObj[sampleCode][`recCap${this.stepTypeForecast}Edit`] || rptDataObj[sampleCode][`recCap${this.stepTypeForecast}`])
          }
        }
        this.rptDayList.push(day)
      }
      this.minInferenceDay = this.rptDayList[this.rptDayList.length - 1] + 1

      // 过滤数据不符合需求的电芯
      for (let sampleCode in sample_data_raw) {
        if (sample_data_raw[sampleCode].storage_days.length < 4) {
          delete sample_data_raw[sampleCode]
          continue
        }
        if (sample_data_raw[sampleCode].storage_days[sample_data_raw[sampleCode].storage_days.length - 1] < 28) {
          delete sample_data_raw[sampleCode]
          continue
        }
        if (sample_data_raw[sampleCode].storage_days[0] !== 0) {
          delete sample_data_raw[sampleCode]
        }
      }
      // 没有符合要求的电芯
      if (Object.keys(sample_data_raw).length === 0) {
        return this.$message.warning("没有符合预测条件的电芯")
      }

      // 保存所有预测电芯真实数据的截止索引
      this.sampleRawDataNum = {}
      for (let sampleCode in sample_data_raw) {
        const storageDays = sample_data_raw[sampleCode].storage_days
        this.sampleRawDataNum[sampleCode] = this.rptDayList.findIndex(day => day === storageDays[storageDays.length - 1])
      }

      const find = this.systemOptions.find(item => item.value === this.productType)
      const system = find.system
      this.forecastParams = {
        product_type: this.productType,
        chemistry_system: system,
        temperature: parseFloat(this.temperature),
        soc: parseFloat(this.soc),
        max_prediction_days: this.fitting_method === 'semi_empirical_formule_fitting' ? 3000 : 1000,
        predict_soh_threshold: parseInt(this.soh_threshold),
        fitting_method: this.fitting_method,
        apply_correction: is_fit_correction,
        apply_optimize: is_fit_optimize,
        optimization_schema: this.optimization_schema,
        slope: this.slope,
        intercept: this.intercept,
        raw_sample_data: sample_data_raw
      }

      this.isEditParam = false
      this.sendForecastRequest(is_fit_correction, is_fit_optimize)
    },
    sendForecastRequest(is_fit_correction = false, is_fit_optimize = false) {
      this.forecastLoading = true
      axios.post('http://10.100.1.101:8008/predict', this.forecastParams)
        .then(response => {
          this.forecastResult = response.data
          if (this.forecastResult.success) {
            this.showForecastData = true
            this.forecastResultHandle(is_fit_correction, is_fit_optimize)
          } else {
            this.$message.warning(this.forecastResult.message)
            this.isEditParam = true
          }
        })
        .catch(error => {
          console.log("error: ", error)
          this.$message.warning('预测失败')
          this.isEditParam = true
        })
        .finally(() => {
          this.forecastLoading = false
        })
    },
    forecastResultHandle(is_fit_correction = false, is_fit_optimize = false) {
      let prefix = 'init'
      if (is_fit_correction) {
        prefix = 'correction'
      } else if (is_fit_optimize) {
        prefix = 'optimize'
      }
      this.fitModel = prefix

      const isSemi = this.fitting_method === 'semi_empirical_formule_fitting'

      this.slope = this.forecastResult.slope || 0
      this.intercept = this.forecastResult.intercept || 0
      if (isSemi) {
        if (Object.keys(this.forecastResult.predicted_sample_data).length === 0) {
          this.forecastResult.predicted_sample_data = _.cloneDeep(this.forecastParams.predicted_sample_data)
        }
        this.semiEmpiricalMeanErrorObj[`${prefix}Mae`] = typeof this.forecastResult.mean_absolute_error === 'number' ? this.forecastResult.mean_absolute_error.toFixed(5) : null
        this.semiEmpiricalMeanErrorObj[`${prefix}Mse`] = typeof this.forecastResult.mean_square_error === 'number' ? this.forecastResult.mean_square_error.toFixed(5) : null
        this.semiEmpiricalMeanErrorObj[`${prefix}Rmse`] = typeof this.forecastResult.root_mean_square_error === 'number' ? this.forecastResult.root_mean_square_error.toFixed(5) : null
        this.semiEmpiricalMeanErrorObj[`${prefix}Slope`] = this.slope ? this.slope.toFixed(5) : null
        this.semiEmpiricalMeanErrorObj[`${prefix}Intercept`] = this.intercept ? this.intercept.toFixed(5) : null
      } else {
        this.aiMeanErrorObj[`${prefix}Mae`] = typeof this.forecastResult.mean_absolute_error === 'number' ? this.forecastResult.mean_absolute_error.toFixed(5) : null
        this.aiMeanErrorObj[`${prefix}Mse`] = typeof this.forecastResult.mean_square_error === 'number' ? this.forecastResult.mean_square_error.toFixed(5) : null
        this.aiMeanErrorObj[`${prefix}Rmse`] = typeof this.forecastResult.root_mean_square_error === 'number' ? this.forecastResult.root_mean_square_error.toFixed(5) : null
        this.aiMeanErrorObj[`${prefix}Slope`] = this.slope ? this.slope.toFixed(5) : null
        this.aiMeanErrorObj[`${prefix}Intercept`] = this.intercept ? this.intercept.toFixed(5) : null
      }

      const forecastDataObj = this.forecastResult.predicted_sample_data || {}
      const fitting_result = this.forecastResult.fitting_result || {}

      // 天数计算填充
      if (this.rptDayList.length > 0) {
        const mostFrequentInterval = this.findMostFrequentInterval(this.rptDayList)
        let maxStorageDay = Math.max(...Object.values(forecastDataObj).map(item => item.storage_days[item.storage_days.length - 1])) || this.minInferenceDay
        if (Array.isArray(fitting_result.storage_days) && fitting_result.storage_days.length > 0) {
          maxStorageDay = Math.max(fitting_result.storage_days[fitting_result.storage_days.length - 1], maxStorageDay)
        }
        maxStorageDay = Math.min(this.maxInferenceDay, maxStorageDay)

        for (let i = this.rptDayList[this.rptDayList.length - 1] + mostFrequentInterval; i <= maxStorageDay; i += mostFrequentInterval) {
          this.rptDayList.push(i)
        }

        if (this.rptDayList[this.rptDayList.length - 1] !== maxStorageDay) {
          this.rptDayList.push(maxStorageDay)
        }
      }

      // 预测结果的电芯列表、表格数据、echarts数据赋值
      this.forecastSampleCodes = Object.keys(forecastDataObj)
      this.forecastTableList = []
      this.rptDayList.forEach((day, index) => {
        const rptObj = this.tableList.find(item => day === parseInt(item.day))
        let rptDataObj = {rowIndex: index, day: day, absoluteTime: rptObj ? rptObj.absoluteTime : '-'}
        this.sampleCodeList.forEach(sampleCode => {
          rptDataObj[sampleCode] = {}
        })
        this.forecastTableList.push(rptDataObj)
      })
      this.forecastEchartList = []
      // 拟合曲线数据存储
      const methodNamePrefix = isSemi ? 'semi' : 'ai'
      const lineName = prefix === 'init' ? '初始拟合' : prefix === 'correction' ? '拟合修正' : '拟合优化'
      let findIndex = this[`${methodNamePrefix}FitEchartList`].findIndex(item => item.lineKey === prefix + '_fitting_result')
      if (findIndex === -1) {
        this[`${methodNamePrefix}FitEchartList`].unshift({lineKey: prefix + '_fitting_result', sampleCode: lineName, batteryCode: lineName, data: []})
        findIndex = 0
      } else {
        this[`${methodNamePrefix}FitEchartList`][findIndex] = {lineKey: prefix + '_fitting_result', sampleCode: lineName, batteryCode: lineName, data: []}
      }
      this.forecastEchartList.push(...this[`${methodNamePrefix}FitEchartList`])
      this.sampleCodeList.forEach(sampleCode => {
        this.forecastEchartList.push({sampleCode: sampleCode, batteryCode: this.sampleInfoObj[sampleCode].batteryCode || sampleCode, data: []})
      })

      // 填充拟合曲线数据
      if (Array.isArray(fitting_result.storage_days) && fitting_result.storage_days.length > 0) {
        for (let i = 0; i < fitting_result.storage_days.length; i++) {
          const storage_day = fitting_result.storage_days[i];
          // 过滤后加入echarts
          let index = this.rptDayList.findIndex(day => day == storage_day)
          if (index !== -1) {
            const capRecRate = fitting_result.recovery_rates.length > i ? this.roundToFixed(fitting_result.recovery_rates[i] * 100, 2) : null;
            this.forecastEchartList[findIndex].data.push([storage_day, capRecRate])
            this.forecastTableList[index].fitting_result = capRecRate
          }
        }
      }
      const len = this[`${methodNamePrefix}FitEchartList`].length

      const fitting_data = this.forecastResult.fitting_data || {}
      const data_storage_days = fitting_data.storage_data || []
      const data_recovery_capacities = fitting_data.recovery_capacities || []
      for (let i = 0; i < this.forecastTableList.length; i++) {
        // 填充fitting_data
        if (isSemi && data_storage_days.length > i && data_recovery_capacities.length > i && data_storage_days[i] == this.rptDayList[i]) {
          this.forecastTableList[i].fitting_data = this.roundToFixed(data_recovery_capacities[i], 3)
        } else {
          this.forecastTableList[i].fitting_data = '-'
        }
      }

      // 天数过滤，转换为table需要的数据格式[{},{}]，以及echarts需要的数据格式
      this.sampleCodeList.forEach((sampleCode, sampleIndex) => {

        // 填充原始数据
        const rawDataIndex = this.sampleRawDataNum[sampleCode] || this.selectedRowKeys.length - 1
        for (let i = 0; i <= rawDataIndex; i++) {
          const capRecRate = this.updateDataList[i][sampleCode][`capRecRate${this.stepTypeForecast}Edit`] || this.updateDataList[i][sampleCode][`capRecRate${this.stepTypeForecast}`]
          const recCap = this.updateDataList[i][sampleCode][`recCap${this.stepTypeForecast}Edit`] || this.updateDataList[i][sampleCode][`recCap${this.stepTypeForecast}`]
          this.forecastEchartList[sampleIndex + len].data.push([this.rptDayList[i], capRecRate === undefined || capRecRate === '-' ? null : typeof capRecRate === 'number' ? capRecRate.toFixed(2) : capRecRate])
          this.forecastTableList[i][sampleCode].recCap = recCap
          this.forecastTableList[i][sampleCode].recCapEditFlag = `recCap${this.stepTypeForecast}Edit` in this.updateDataList[i][sampleCode]
          this.forecastTableList[i][sampleCode].capRecRate = capRecRate
          this.forecastTableList[i][sampleCode].capRecRateEditFlag = `capRecRate${this.stepTypeForecast}Edit` in this.updateDataList[i][sampleCode]
        }

        if (this.forecastSampleCodes.includes(sampleCode)) {
          const sampleDataObj = forecastDataObj[sampleCode]

          for (let i = 0; i < sampleDataObj.storage_days.length; i++) {
            if (sampleDataObj.storage_days[i] > this.maxInferenceDay) {
              break;
            }

            // 在已中检的天数内，已填充原数据；大于已中检天数，取预测数据
            if (sampleDataObj.storage_days[i] <= this.rptDayList[rawDataIndex]) {
              continue;
            }

            // 过滤后加入echarts和表格数据
            let index = this.rptDayList.findIndex(day => day == sampleDataObj.storage_days[i])
            if (index !== -1) {
              const capRecRate = sampleDataObj.recovery_rates.length > i ? this.roundToFixed(sampleDataObj.recovery_rates[i] * 100, 2) : null;
              this.forecastEchartList[sampleIndex + len].data.push([sampleDataObj.storage_days[i], capRecRate])
              this.forecastTableList[index][sampleCode].recCap = sampleDataObj.recovery_capacities.length > i ? this.roundToFixed(sampleDataObj.recovery_capacities[i], 3) : null
              this.forecastTableList[index][sampleCode].capRecRate = capRecRate
            }
          }
        } else {
          for (let i = this.selectedRowKeys.length; i < this.rptDayList.length; i++) {
            this.forecastTableList[i][sampleCode].recCap = '-'
            this.forecastTableList[i][sampleCode].capRecRate = '-'
          }
        }
      })

      // 表头处理
      this.initForecastTable()

      // 获取Echarts图初始数据
      this.chartEditData.forecast = this._getInitData('forecast', 'edit')
      this.chartResetOriginal.forecast = this._getInitData('forecast')
      this.chartFrist.forecast = true
      this.$nextTick(() => {
        // echarts图初始化
        this.initNormalEchart('forecast')
      })
    },
    roundToFixed(num, precision) {
      let factor = Math.pow(10, precision);
      let roundedNum = Math.round(num * factor) / factor;
      return roundedNum.toFixed(precision); // 确保结果具有指定位数的小数
    },
    findMostFrequentInterval(rptDayList) {
      if (rptDayList.length < 2) {
        return 1; // 如果列表长度小于2，则返回默认值1
      }

      const intervalCounts = new Map();
      let mostFrequentInterval = 1;
      let maxCount = 0; // 记录出现次数最多的间隔的次数

      // 计算每对相邻元素之间的间隔，并记录出现次数
      for (let i = rptDayList.length - 1; i > 0; i--) {
        const interval = rptDayList[i] - rptDayList[i - 1];
        const count = intervalCounts.get(interval) || 0;
        intervalCounts.set(interval, count + 1);

        // 检查当前间隔是否是最频繁的
        if (count + 1 > maxCount) {
          maxCount = count + 1;
          mostFrequentInterval = interval;
        }
      }

      return mostFrequentInterval;
    },
    initForecastTable() {
      if (this.forecastTableList.length > 0) {
        this.forecastTableColumns[2].children = []
        this.forecastTableColumns[3].children = []

        const primaryStatusMap = this.resultDataJson.primaryStatusMap || {}
        this.sampleCodeList.forEach(sampleCode => {
          let batteryStatus = primaryStatusMap[sampleCode] || this.testProgress.testStatus
          let batteryCode = this.sampleInfoObj[sampleCode].batteryCode || sampleCode
          this.forecastTableColumns[2].children.push(
            this.getForecastChildren(sampleCode, batteryCode, batteryStatus, 'recCap')
          )
          this.forecastTableColumns[3].children.push(
            this.getForecastChildren(sampleCode, batteryCode, batteryStatus, 'capRecRate')
          )
        })
      }
    },
    getForecastChildren(sampleCode, batteryCode, batteryStatus, dataIndex, isEditFlag = false) {
      let tips
      // 前端表头状态转换
      switch (batteryStatus) {
        case 'earlyEnd': tips = '状态正常-提前结束'; break;
        case 'batteryDisassembly': tips = '状态正常-电池拆解'; break;
        case 'pressureDrop': tips = '掉压失效-终止测试'; break;
        case 'abnormalHot': tips = '异常发热-终止测试'; break;
        case 'openShellAndLeak': tips = '开壳漏液-终止测试'; break;
        case 'shellRust': tips = '壳体生锈-终止测试'; break;
        case 'operationError': tips = '作业错误-终止测试'; break;
        case 'thermalRunaway': tips = '热失控-终止测试'; break;
        case 'acrException': tips = '内阻异常-终止测试'; break;
        default: tips = ''; break;
      }
      if (["earlyEnd", "batteryDisassembly", "pressureDrop", "abnormalHot", "openShellAndLeak", "shellRust", "operationError", "thermalRunaway", "acrException"].includes(batteryStatus)) {
        batteryStatus = "Stop"
      } else if (batteryStatus === "ongoing") {
        batteryStatus = "Ongoing"
      }

      let children = []
      if (this.isUpdateData) {
        children = [
          {
            title: "原始值",
            align: "center",
            width: "100px",
            dataIndex: `${sampleCode}.${dataIndex}`,
            scopedSlots: { customRender: 'rawCapacity' }
          },
          {
            title: "修改值",
            align: "center",
            width: "100px",
            dataIndex: `${sampleCode}.${dataIndex}Edit`,
            scopedSlots: { customRender: 'updateCapacity' }
          },
        ]
      } else {
        children = [
          {
            title: <a-tooltip title={tips}>{batteryStatus}</a-tooltip>,
            align: "center",
            width: "100px",
            dataIndex: `${sampleCode}.${dataIndex}`,
            scopedSlots: { customRender: isEditFlag ? 'showCapacity' : 'forecastData' }
          }
        ]
      }

      let result = {
        title: batteryCode,
        align: "center",
        width: "100px",
        children: children,
      }


      if (sampleCode !== batteryCode) {
        result = {
          title: sampleCode,
          align: "center",
          width: "100px",
          children: [
            result
          ]
        }
      }

      return result
    },
    // 处理图表原始数据
    _handleForecastData(targetObj, dataList, checkData) {
      let seriesOriginal = []
      let checkDataOriginal = []
      let legendList = []
      let yAxisList = []
      let seriesList = []


      let lineColorList = []
      const isCheck = checkData.length === 0
      const templateParam = this.reportChartTemplateList[targetObj].templateParamJson

      for (let i = 0; i < dataList.length; i++) {
        const sampleCode = dataList[i].sampleCode
        const batteryCode = dataList[i].batteryCode
        const lineKey = dataList[i].lineKey
        const isFittingLine = lineKey && lineKey.includes('fitting_result')

        let legendNameType = dataList[i][this.chartEditData[targetObj].legendNameType]
        let templateContent = templateParam.checkData.length > 0 ? (templateParam.checkData.filter(item => item.id === sampleCode)[0] || {}) : {}
        let editContent = checkData[checkData.findIndex(findItem => findItem.id == sampleCode)]

        if (!this.showForecastData && isFittingLine) {
          continue; // 不展示预测数据时去掉拟合曲线
        }

        const isHaveColor = lineColorList.find(findItem => findItem.name === sampleCode)
        if (!isFittingLine && isHaveColor == undefined) {
          lineColorList.push({name: sampleCode, color: this.echartsColor[lineColorList.length]})
        }

        let color = isFittingLine ? (lineKey.includes('init') ? '#7cfc00' : lineKey.includes('correction') ? '#47BB55' : '#006400') : lineColorList[lineColorList.findIndex(v => v.name === sampleCode)].color

        if (isCheck) { this.chartLegendNameList[targetObj].push({sampleCode,batteryCode})}

        const rawDataIndex =  this.sampleRawDataNum[sampleCode] || this.selectedRowKeys.length - 1

        let series = {
          id: sampleCode,
          index: i + 1,
          name: legendNameType,
          soc: legendNameType,
          type: "line",
          barGap: 0,
          markPoint: {
            data: []
          },
          emphasis: {
            focus: "series"
          },
          large: true,
          sampling: 'lttb',
          connectNulls: templateContent.connectNulls ?? (isCheck ? false : Boolean(Number(editContent.connectNulls))),
          symbol: templateContent.symbol ?? (isCheck ? (this.showForecastData ? "none" : "rect") : editContent.symbol),
          symbolSize: templateContent.symbolSize ?? (isCheck ? 5 : editContent.symbolSize),
          lineStyle: {
            width: templateContent.lineWidth ??  (isCheck ? isFittingLine ? 3 : 2 : editContent.lineWidth),
            type: templateContent.lineType ?? (isCheck ? "solid" : editContent.lineType),
            color: templateContent.lineColor ?? (isCheck ? color : editContent.lineColor)
          },
          itemStyle: {
            color: templateContent.itemColor ?? (isCheck ? color : editContent.itemColor)
          },
          data: dataList[i].data.filter((item, index) => index <= (isFittingLine ? dataList[i].data.length - 1 : rawDataIndex)).map((mapItem, index) => {
            let absoluteTime = ''
            if (Array.isArray(mapItem) && mapItem.length > 0) {
              absoluteTime = this.absoluteTimeMap[mapItem[0]];
            }
            return {id: index, value: mapItem, lineName: isFittingLine ? '容量恢复率拟合值' : '容量恢复率', absoluteTime: absoluteTime}
          }),
        }

        // 设置最大最小值
        if (!isCheck && editContent.maxPoint || templateContent.maxPoint) {
          series.markPoint.data.push({type: "max", name: "Max"})
        }
        if (!isCheck && editContent.minPoint || templateContent.minPoint) {
          series.markPoint.data.push({type: "min", name: "Min"})
        }

        seriesOriginal.push({
          id: sampleCode,
          index: i + 1,
          name: legendNameType,
          soc: legendNameType,
          connectNulls: false,
          dataName:legendNameType,
          synchronization: templateContent.synchronization ?? (isCheck ? i : editContent.synchronization),
          maxPoint: templateContent.maxPoint ?? (isCheck ? false : editContent.maxPoint),
          minPoint: templateContent.minPoint ?? (isCheck ? false : editContent.minPoint),
          symbol: templateContent.symbol ?? (isCheck ? (this.showForecastData ? "none" : "rect") : editContent.symbol),
          symbolSize: templateContent.symbolSize ??  (isCheck ? 5 : editContent.symbolSize),
          itemColor: templateContent.itemColor ?? (isCheck ? color : editContent.itemColor),
          lineType: templateContent.lineType ?? (isCheck ? "solid" : editContent.lineType),
          lineWidth: templateContent.lineWidth ?? (isCheck ? isFittingLine ? 3 : 2 : editContent.lineWidth),
          lineColor: templateContent.lineColor ?? (isCheck ? color : editContent.lineColor)
        })

        // 原始值
        checkDataOriginal.push({
          id: sampleCode,
          index: i + 1,
          name: legendNameType,
          soc: legendNameType,
          connectNulls: false,
          synchronization:i,
          maxPoint:false,
          minPoint:false,
          symbol: this.showForecastData ? "none" : "rect",
          symbolSize: 5,
          itemColor: color,
          lineType: "solid",
          lineWidth: 2,
          lineColor: color
        })

        // 在线报告和离线报告的数据结构不一致
        yAxisList.push(...dataList[i].data.map(mapItem => Number(mapItem[1])))

        legendList.push(legendNameType)
        seriesList.push(series)

        if (!isFittingLine && this.showForecastData && dataList[i].data.length > rawDataIndex + 1) {
          templateContent = templateParam.checkData.length > 0 ? (templateParam.checkData.filter(item => item.id === sampleCode + "_forecast")[0] || {}) : {}
          editContent = checkData[checkData.findIndex(findItem => findItem.id == sampleCode + "_forecast")]
          let series2 = {
            index: i + 1,
            id: sampleCode + '_forecast',
            name: legendNameType,
            soc: legendNameType,
            type: "line",
            barGap: 0,
            markPoint: {
              data: []
            },
            emphasis: {
              focus: "series"
            },
            large: true,
            sampling: 'lttb',
            connectNulls: templateContent.connectNulls ?? (isCheck ? false : Boolean(Number(editContent.connectNulls))),
            symbol: templateContent.symbol ?? (isCheck ? "none" : editContent.symbol),
            symbolSize: templateContent.symbolSize ?? (isCheck ? 5 : editContent.symbolSize),
            lineStyle: {
              width: templateContent.lineWidth ?? (isCheck ? 2 : editContent.lineWidth),
              type: templateContent.lineType ?? (isCheck ? [6, 10] : editContent.lineType),
              color: templateContent.lineColor ?? (isCheck ? color : editContent.lineColor),
              opacity: 0.6,
            },
            
            itemStyle: {
              color: templateContent.itemColor ??  (isCheck ? color : editContent.itemColor),
            },
            data: dataList[i].data.filter((item, index) => index >= rawDataIndex).map((mapItem, index) => {
              let absoluteTime = ''
              if (Array.isArray(mapItem) && mapItem.length > 0) {
                absoluteTime = this.absoluteTimeMap[mapItem[0]];
              }
              // 在线报告和离线报告的数据结构不一致
              return {id: index, value: mapItem, lineName: '容量恢复率预测值', absoluteTime: absoluteTime, symbol: isCheck ? 'none' : index === 0 ? checkData[temIndex].symbol : editContent.symbol}
            }),
          }
          // 设置最大最小值
          if (!isCheck && editContent.maxPoint || templateContent.maxPoint) {
            series2.markPoint.data.push({type: "max", name: "Max"})
          }
          if (!isCheck && editContent.minPoint || templateContent.minPoint) {
            series2.markPoint.data.push({type: "min", name: "Min"})
          }
          seriesList.push(series2)

          // 编辑图表：数据标签
          seriesOriginal.push({
            index: i + 1,
            id: sampleCode + '_forecast',
            name: legendNameType,
            soc: legendNameType,
            connectNulls: false,
            dataName:legendNameType + '_容量恢复率预测值',
            synchronization: templateContent.synchronization ?? (isCheck ? i : editContent.synchronization),
            maxPoint: templateContent.maxPoint ?? (isCheck ? false : editContent.maxPoint),
            minPoint: templateContent.minPoint ?? (isCheck ? false : editContent.minPoint),
            symbol: templateContent.symbol ?? (isCheck ? "none" : editContent.symbol),
            symbolSize: templateContent.symbolSize ?? (isCheck ? 5 : editContent.symbolSize),
            itemColor: templateContent.itemColor ?? (isCheck ? color : editContent.itemColor),
            lineType: templateContent.lineType ?? (isCheck ? [6, 10] : editContent.lineType),
            lineWidth: templateContent.lineWidth ?? (isCheck ? 2 : editContent.lineWidth),
            lineColor: templateContent.lineColor ?? (isCheck ? color : editContent.lineColor)
          })

          // 原始值
          checkDataOriginal.push({
            id: sampleCode + '_forecast',
            index: i + 1,
            name: legendNameType,
            soc: legendNameType,
            connectNulls: false,
            synchronization:i,
            maxPoint:false,
            minPoint:false,
            symbol: "none",
            symbolSize: 5,
            itemColor: color,
            lineType: [6, 10],
            lineWidth: 2,
            lineColor: color
          })
        }
      }

      return [seriesOriginal, checkDataOriginal, seriesList, legendList, [], Math.max.apply(null, yAxisList), Math.min.apply(null, yAxisList)]
    },

  }
}
</script>
<style lang="less" scoped>
@import "./css/calendar.less";
@import '/src/components/pageTool/style/pbiSearchItem.less';

.ml20 {
  margin-left: 20px;
}

/* 固定列 */
/deep/ .right-content .ant-table-thead tr:nth-child(1) th:nth-child(1),
/deep/ .right-content .ant-table-tbody tr td:nth-child(1){
  position: sticky;
  left: 0;
  z-index: 11;
}
/deep/ .right-content .ant-table-thead tr:nth-child(1) th:nth-child(2),
/deep/ .right-content .ant-table-tbody tr td:nth-child(2){
  position: sticky;
  left: 100px;
  z-index: 11;
}
/* 固定列数据背景颜色 */
/deep/ .right-content .ant-table-tbody tr td:nth-child(1),
/deep/ .right-content .ant-table-tbody tr td:nth-child(2) {
  background-color: #FFFFFF;
}

.describe-tip {
  color: #8f91a8;
  font-size: 11px;
  height: 12px;
  line-height: 12px;
  margin: 0;
  overflow: hidden;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

.searchItem {
  display: flex;
  align-items: center;
  height: 28px;
  margin-bottom: 12px;
}

/deep/ .searchItem .label {
  display: inline-block;
  width: 100px;
  padding-right: 8px;
  text-align: right;
  font-size: 12px;
  color: #333;
}

/deep/ .searchItem .content {
  width: calc(100% - 100px);
}

/deep/ .MathJax {
  margin: 0.5em 0 !important;
}

/deep/ .searchItem .MathJax {
  text-align: right;
}

.me-input {
  width: 100px;
  font-size: 12px;
  color: #333;
}

/deep/ .me-input .ant-input,
/deep/ .me-input .ant-input-group-addon {
  font-size: 12px;
  padding: 0 3px;
  color: #333;
}

/deep/ .me-input .ant-input {
  background-color: #FFFFFF;
}

/deep/ .me-input .ant-input-suffix {
  color: #333;
  right: 3px;
}

.ant-radio-wrapper, .ant-radio-group {
  font-size: 12px;
  color: #333;
}

.flex-center-div {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #333;
}
</style>