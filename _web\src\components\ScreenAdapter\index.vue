<template>
    <div
      class="ScreenAdapter"
      :style="style"
    >
      <slot />
    </div>
  </template>
  <script>
  export default {
    name: '',
    //参数注入
    props: {
      width: {
        type: String,
        default: '1240' 
      },
      height: {
        type: String,
        default: '569' 
      }
    },
    data() {
      return {
        style: {
          width: this.width + 'px',
          height: this.height + 'px',
          transform: 'scale(1)'
        }
      }
    },
    mounted() {
      this.setScale()
      window.onresize = this.Debounce(this.setScale, 500)
    },
    methods: {
      Debounce: (fn, t) => {
        const delay = t || 500
        let timer
        return function() {
          const args = arguments
          if (timer) {
            clearTimeout(timer)
          }
          const context = this
          timer = setTimeout(() => {
            timer = null
            fn.apply(context, args)
          }, delay)
        }
      },
      // 获取放大缩小比例
      getScale() {
        const w = (window.innerWidth - 40) / this.width
        const h = (window.innerHeight - 40) / this.height
        return [w,h]
      },
      // 设置比例
      setScale() {
        this.style.transform = 'scale(' + this.getScale()[0] + ',' + this.getScale()[1] + ')'
      }
    }
  }
  </script>
  <style lang="less" scoped>
  .ScreenAdapter {
    transform-origin: 0 0;
    position: absolute;
    transition: 0.3s;
  }
  </style>