<template>
    <div>
      <a-drawer
        :bodyStyle="{ height: '100%' }"
        placement="right"
        :closable="false"
        width="80%"
        :visible="visible1"
        @close="onClose1"
      >
        <iframe :src="pdfUrl+'#view=FitH,top'" width="100%" height="100%" ></iframe>
      </a-drawer>

      <a-drawer placement="right" :closable="false" width="80%" :visible="visible2" @close="onClose2" :destroyOnClose="true">
        <checkhistory :param="param"></checkhistory>
      </a-drawer>

        <a-spin :spinning="vloading">
            <s-table :scroll="{ x: 1300, y: windowHeight }" size="small" ref="table" :rowKey="(record) => record.id" :columns="columns" :data="loadData">
                <span slot="bomRelateStatus" slot-scope="text, record">
                            {{mapEndStatus[record.bomRelateStatus]}}
                    </span>
                <template slot="bomLines" slot-scope="text, record">
                    <label v-if="record.bomLines != '[]'">
                        <label  v-for="(item, i) in JSON.parse(record.bomLines)" :key="i">
                            <span v-if="i < JSON.parse(record.bomLines).length -1">
                                {{dataLines[item]}}，
                            </span>
                            <span v-else>
                                {{dataLines[item]}}
							</span>
                        </label>
                    </label>
                    <a v-else-if="record.bomRelateStatus == 2 || record.bomRelateStatus == 0 || record.bomRelateStatus == 4" @click="showLine(record)" >设置产线</a>
                </template>



                <template slot="id" slot-scope="text, record">
                    <a-dropdown>
                            <a class="ant-dropdown-link">
                              更多<a-icon  type="down" />
                            </a>
                            <a-menu v-if="record.bomRelateStatus == 0 || record.bomRelateStatus == 4" slot="overlay">
                              <a-menu-item>
                                <a @click="callBomEndRelate(record)" >提交</a>
                              </a-menu-item>
                              <a-menu-item>
                                <a @click="showsapimport(record)" >从sap导入</a>
                              </a-menu-item>
                              <a-menu-item>
                                <a v-if="record.bomLines != '[]'" @click="showLine(record)" >添加产线</a>
                                <a v-else @click="showLine(record)" >设置产线</a>
                              </a-menu-item>
                              <a-menu-item>
                                <a @click="showDelLine(record)" >删除产线</a>
                              </a-menu-item>
                              <a-menu-item>
                                <a @click="$refs.checkhistory2.edit(record)" >审核记录</a>
                              </a-menu-item>
                              <a-menu-item>
                                <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => callDelBomEnd(record)">
                                  <a>删除</a>
                                </a-popconfirm>
                              </a-menu-item>
                            </a-menu>
                            <a-menu v-if="record.bomRelateStatus == 2" slot="overlay">
                              <a-menu-item>
                                <a v-if="record.bomLines != '[]'" @click="showLine(record)" >添加产线</a>
                                <a v-else @click="showLine(record)" >设置产线</a>
                              </a-menu-item>
                              <a-menu-item>
                                <a @click="showDelLine(record)" >删除产线</a>
                              </a-menu-item>
                              <a-menu-item>
                                <a @click="$refs.checkhistory2.edit(record)" >审核记录</a>
                              </a-menu-item>
                              <a-menu-item>
                                <a @click="preview1(text)">预览文件</a>
                              </a-menu-item>
                            </a-menu>
                            <a-menu v-if="record.bomRelateStatus == 3 || record.bomRelateStatus == 7" slot="overlay">
                              <!-- <a-menu-item>
                                <a @click="callBomEndRelate(record)" >重试</a>
                              </a-menu-item> -->
                              <a-menu-item>
                                <a @click="callBomEndError(record)" >查看</a>
                              </a-menu-item>
                              <a-menu-item>
                                <a @click="$refs.checkhistory2.edit(record)" >审核记录</a>
                              </a-menu-item>
                            </a-menu>

                            <a-menu v-if="record.bomRelateStatus == 1 || record.bomRelateStatus == 5 || record.bomRelateStatus == 6" slot="overlay">
                              
                              <a-menu-item>
                                <a @click="$refs.checkhistory2.edit(record)" >审核记录</a>
                              </a-menu-item>
                            </a-menu>
                          </a-dropdown>
                      <!-- <a @click="$refs.checkhistory2.edit(record)" >审核记录</a>
                      <a-divider v-if="record.bomRelateStatus == 2" type="vertical" />
                      <a v-if="record.bomRelateStatus == 2" @click="preview1(text)">预览文件</a> -->
                </template>
                <template slot="expandedRowRender" slot-scope="record" style="margin: 0">
                    <div style="display:none">{{treeData = {
                            columns: treeColumns,
                            lists: record.bomData ? JSON.parse(record.bomData) :[]
                        }
                    }}</div>
                    <dragTreeTable
                        :data='treeData'
					    id="dtrees"
                        :isdraggable="false"
                        style="width:90%;margin:0;"
                    >
                        <template #version="{row}">
                            <template v-if="row.version">
                                <div  v-for="(item,i) in JSON.parse(row.version)" :key="i">
                                    {{i}}-{{item}}
                                </div>
                            </template>
                        </template>
                    </dragTreeTable>
                </template>
            </s-table>
        </a-spin>

      <checkhistory2 ref="checkhistory2" @preview1="preview1" />
      <!--<checkhistory ref="checkhistory"  />-->

       <a-modal :title="addWerkTitle" :width="600" :visible="lineModalVisible" @ok="ok" @cancel="cancel">
          <div>
            <p style="margin:0">请选择成品BOM适用产线 ({{endBomVersoin}}):</p>
            <a-checkbox-group v-model="checkedList" :options="options" />
          </div>
          <div>
            <p style="margin:0;margin-top:10px">包装BOM适用产线:</p>
            <span v-for="(item,i) in packLineIds" :key="i">{{dataLines[item]}}&nbsp;&nbsp;&nbsp;&nbsp;</span>
          </div>
        </a-modal>

        <a-modal title="删除产线" :width="600" :visible="dellineModalVisible" @ok="delok" @cancel="delcancel">
          <div>
            <p style="margin:0">请选择成品BOM适用产线 ({{endBomVersoin}}):</p>
            <a-checkbox-group v-model="delcheckedList" :options="deloptions" />
          </div>
        </a-modal>

        <a-modal title="输入sap版本" :width="200" :visible="sapvisible" @ok="sapok" @cancel="sapcancel">
          <div>
            <a-input v-model="sapverison" />
          </div>
        </a-modal>

        <a-modal title="失败原因" :width="400" :visible="errorsVisible" @cancel="handelcancel">
          <template slot="footer">
            <a-button key="back" @click="handelcancel">
                  关闭
            </a-button>
          </template>
          <p>{{errors}}</p>
        </a-modal>
    </div>
</template>

<script>
    import {
        getBomEndPage,
        getwerklines,
        endBomPreview,
        bomEndRelate,
        getBomPackLineIds,
        bomEndSetLines,
        getBomLineIds,
        bomEndDelLines,
        getBomEndError,
        delBomEnd,
        sapImportBomEnd
    } from "@/api/modular/system/bomManage"
    import {
        STable
    } from '@/components'

    import dragTreeTable from "drag-tree-table";
    import checkhistory2 from './checkhistory2'
    import checkhistory from './checkhistory'
    export default {
        components: {
            checkhistory2,
            checkhistory,
            STable,
            dragTreeTable
        },
        props: {
            issueId: {
                type: Number,
                default: 0
            }
        },
        data() {
            return {
                sapverison:'',
                sapvisible:false,
                windowHeight: document.documentElement.clientHeight - 265,
                id:0,
                lineModalVisible:false,
                addWerkTitle:'',
                lineflag:0,
                packLineIds:[],
                endBomVersoin:'',
                options:[],
                checkedList :[],
                dellineModalVisible:false,
                delcheckedList:[],
                deloptions:[],
                errorsVisible: false,
                errors: '',
                bomVisible : false,

                param: {},
                show:false,
                visible1: false,
                visible2: false,
                pdfUrl: '',
                treeColumns: [{
                        type: 'selection',
                        field: 'id',
                        title: '物料名称',
                        width: 350,
                        align: 'left',
                        formatter: (item) => {
                            return '<a id=' + item.id + ' name=' + item.partName + '>' + item.partName + '</a>'
                        }
                    },
                    {
                        field: 'sapNumber',
                        title: '物料代码',
                        width: 150,
                        align: 'center',
                    },
                    {
                        field: 'partDescription',
                        title: '物料规格',
                        width: 400,
                        align: 'left'
                    },
                    {
                        field: 'partUnit',
                        title: '单位',
                        width: 110,
                        align: 'center'
                    },
                    {
                        field: 'partUse',
                        title: '理论用量(B0)',
                        width: 168,
                        align: 'center'
                    },
                    {
                        field: 'partLoss',
                        title: '设计损耗(B1)',
                        width: 168,
                        align: 'center'
                    },
                    {
                        type: 'version',
                        field: 'version',
                        title: '工厂版本号',
                        width: 150,
                        align: 'center'
                    },
                    {
                        field: 'posnr',
                        title: '行号',
                        width: 100,
                        align: 'center'
                    },
                    {
                        field: 'desc',
                        title: '备注',
                        width: 150,
                        align: 'center'
                    },
                    
                ],
                dataLines: {},
                vloading: false,
                historyBomId:'',
                mapEndStatus: ['编辑中', '审核中', '已审核', '失败中', '被驳回','新增工厂申请','删除工厂申请','废弃'],
                loadData: parameter => {
                    parameter = { ...parameter,
                        ...{
                            bomIssueId: this.issueId
                        }
                    }
                    return getBomEndPage(Object.assign(parameter, this.queryParam)).then((res) => {
                        return res.data
                    })
                },
                columns: [{
                        title: '成品BOM代码',
                        dataIndex: 'bomCode',
                        width: 120,
                    },{
                        title: '成品BOM编号',
                        dataIndex: 'bomVersion',
                        width: 250
                    },
                    {
                        title: '成品物料',
                        dataIndex: 'bomPartName',
                        width: 400,
                        customRender: (text, record, index) => {
                          return null != record.bomData?JSON.parse(record.bomData)[0].partDescription:''
                        },
                    },
                    
                    {
                        title: '产线',
                        dataIndex: 'bomLines',
                        scopedSlots: {
                            customRender: 'bomLines'
                        }
                    },
                    {
                        title: '关联状态',
                        dataIndex: 'bomRelateStatus',
                        scopedSlots: {
                            customRender: 'bomRelateStatus'
                        }
                    },
                    {
                        title: '操作',
                        dataIndex: 'id',

                        scopedSlots: {
                          customRender: 'id'
                        }
                    },
                ]
            }
        },
        created() {
            this.callWerkLines()
        },
        methods: {
            showsapimport(record){
                this.id = record.id
                this.sapvisible = true
            },
            sapcancel(){
                this.id = 0
                this.sapverison = ''
                this.sapvisible = false
            },
            sapok(){
                this.vloading = true
                sapImportBomEnd({
                    id: this.id,
                    sapVersion: this.sapverison
                })
                .then((res) => {
                    if (res.success) {
                    let index = this.treeData.lists.findIndex(item => item.id == this.id)
                    this.treeData.lists[index].bomRelateStatus = 6
                    this.$message.info('已导入', 1);
                    this.sapcancel()
                    } else {
                    this.$message.error(res.message, 1);
                    }
                    this.vloading = false
                })
                .catch((err) => {
                    this.vloading = false
                    this.$message.error('错误提示：' + err.message, 1)
                });
            },
            preview(id, processId) {
                this.historyBomId = id
                this.param.historyBomId = id
                this.param.processId = processId
                this.visible2 = !this.visible2
            },
            preview1(id) {
                this.vloading = true
                endBomPreview({
                id: id
                }).then((res) => {
                if (res.success) {
                    this.pdfUrl = process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id=' + res.data.fileId
                    this.visible1 = true
                    this.vloading = false
                }else{
                    this.$message.error(res.message)
                    this.vloading = false
                }
                })
            },

          onClose1() {
            this.visible1 = false;
          },
          onClose2() {
            this.visible2 = false;
          },
            callWerkLines() {
                this.vloading = true
                getwerklines().then((res) => {
                    if (res.success) {
                        let mapline = {}
                        for (var key in res.data) {
                            for (const _item of res.data[key]) {
                                mapline[_item.id] = _item.namecode ? _item.namecode + '--' + _item.lineName : _item.werkNo + '->' + _item.lineName
                            }
                        }
                        this.dataLines = mapline
                    } else {
                        this.$message.error(res.message)
                    }
                    this.vloading = false
                }).catch((err) => {
                    this.$message.error('错误：' + err.message)
                    this.vloading = false
                })
            },

            callBomEndRelate(record) {
                this.vloading = true
                bomEndRelate({
                    id: record.id
                }).then((res) => {
                if (res.success) {
                    
                    this.$message.success('关联成功')
                } else {
                    
                    this.$message.error('关联失败：' + res.message)
                }
                this.$refs.table.refresh()
                this.vloading = false
                }).finally((res) => {
                    this.vloading = false
                })
            },

            showLine(record) {
                this.id = record.id
                this.lineModalVisible = true
                this.addWerkTitle = record.bomLines == '[]' ? '设置产线' : '添加产线'
                this.callGetBomPackLineIds(record)
            },
            callGetBomPackLineIds(record) {
                getBomPackLineIds({
                    id: record.id
                })
                .then((res) => {
                    if (res.success) {
                    this.lineflag = res.data.lineflag
                    this.packLineIds = res.data.packlines
                    this.endBomVersoin = res.data.endBomVersoin
                    for (const _item of res.data.bomlines) {
                        this.options.push({
                        label: this.dataLines[_item],
                        value: _item
                        })
                    } 
                    } else {
                        this.$message.error(res.message, 1);
                    }
                })
                .catch((err) => {
                    this.$message.error('错误提示：' + err.message, 1)
                });
            },
            ok() {
                if (this.checkedList.length <=0 ) {
                this.$message.error('请选择产线')
                return false
                }
                this.vloading = true
                bomEndSetLines({
                    id: this.id,
                    bomLines: JSON.stringify(this.checkedList)
                })
                .then((res) => {
                    if (res.success) {
                        this.$refs.table.refresh()
                        this.cancel()
                    } else {
                        this.$message.error(res.message, 1);
                    }
                        this.vloading = false
                })
                .catch((err) => {
                    this.vloading = false
                    this.$message.error('错误提示：' + err.message, 1)
                });
            },
             cancel() {
                this.id = 0
                this.options = []
                this.checkedList = []
                this.lineModalVisible = false
            },

            showDelLine(record){
                this.id = record.id
                this.dellineModalVisible = true
                this.callGetBomLineIds(record)
            },
            callGetBomLineIds(record) {
                getBomLineIds({
                    id: record.id
                })
                .then((res) => {
                    if (res.success) {
                    this.lineflag = res.data.lineflag
                    this.endBomVersoin = res.data.endBomVersoin
                    for (const _item of res.data.bomlines) {
                        this.deloptions.push({
                        label: this.dataLines[_item],
                        value: _item
                        })
                    }
                    } else {
                    this.$message.error(res.message, 1);
                    }
                })
                .catch((err) => {
                    this.$message.error('错误提示：' + err.message, 1)
                });
            },

            delok(){
                this.vloading = true
                bomEndDelLines({
                    id: this.id,
                    bomLines: JSON.stringify(this.delcheckedList)
                })
                .then((res) => {
                    if (res.success) {
                    this.$refs.table.refresh()
                    this.delcancel()
                    } else {
                    this.$message.error(res.message, 1);
                    }
                    this.vloading = false
                })
                .catch((err) => {
                    this.vloading = false
                    this.$message.error('错误提示：' + err.message, 1)
                });
            },

            delcancel(){
                this.id = 0
                this.delcheckedList = []
                this.deloptions = []
                this.dellineModalVisible = false
            },

            handelcancel() {
                this.errors = ''
                this.errorsVisible = false
                this.bomVisible = false
            },

            callBomEndError(record) {
                this.vloading = true
                getBomEndError({
                    id: record.id
                })
                .then((res) => {
                    if (res.success) {
                    this.errors = res.data.erros
                    this.errorsVisible = true
                    } else {
                    this.$message.error(res.message, 1);
                    }
                    this.vloading = false
                })
                .catch((err) => {
                    this.vloading = false
                    this.$message.error('错误提示：' + err.message, 1)
                });
            },

            callDelBomEnd(record){
                this.vloading = true
                delBomEnd({
                id: record.id
                }).then((res) => {
                if (res.success) {
                    this.$refs.table.refresh()
                    this.$message.success('删除成功')
                } else {
                    this.$message.error('删除失败：' + res.message)
                }
                this.vloading = false
                }).finally((res) => {
                this.vloading = false
                })
            },

        }
    }
</script>

<style lang="less" scoped=''>
/deep/.ant-table{
    margin: 0 2px;
    margin-top:2px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}
/deep/.ant-table-thead > tr > th{
    font-weight: bold;
    background: #f3f3f3 !important;
}
/deep/.ant-table-small > .ant-table-content > .ant-table-body{
    margin: 0;
}
/deep/.table-page-search-wrapper .ant-form-inline .ant-form-item {
    margin-bottom: 14px;
  }
  /deep/.table-page-search-wrapper .ant-form-inline .ant-form-item .ant-form-item-control {
    height: auto;
  }/deep/.tree-column{
		padding: 2px 0 !important;
	}/deep/.tree-row{
		line-height: initial !important;
	}/deep/.drag-tree-table-header{
    height: initial !important;
  }
  /deep/.drag-tree-table-header {
		background: #fafafa;
		border-bottom: 1px solid #e8e8e8;
        line-height: inherit;
	}
  /deep/.drag-tree-table {
		margin: 0;
	}
</style>
