/* 
 * 全局CSS变量
 * 用于统一管理颜色、尺寸等样式变量
 */

:root {
  /* 主题颜色 */
  --primary-color: #1890ff;
  --primary-color-hover: #40a9ff;
  --primary-color-active: #096dd9;
  --primary-color-outline: rgba(24, 144, 255, 0.2);
  
  /* 功能色 */
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --info-color: #1890ff;
  
  /* 中性色 */
  --text-color-primary: rgba(0, 0, 0, 0.85);
  --text-color-secondary: rgba(0, 0, 0, 0.65);
  --text-color-disabled: rgba(0, 0, 0, 0.45);
  --border-color: #d9d9d9;
  --border-color-split: #f0f0f0;
  --background-color-base: #f5f5f5;
  --background-color-light: #fafafa;
  
  /* 尺寸 */
  --font-size-base: 14px;
  --font-size-sm: 12px;
  --font-size-lg: 16px;
  --line-height-base: 1.5;
  
  /* 圆角 */
  --border-radius-base: 4px;
  --border-radius-sm: 2px;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* 阴影 */
  --box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
  --box-shadow-card: 0 1px 2px rgba(0, 0, 0, 0.05);
  
  /* 动画 */
  --animation-duration-base: 0.2s;
  --animation-duration-slow: 0.3s;
  --animation-timing-function: cubic-bezier(0.645, 0.045, 0.355, 1);
}

/* 高对比度模式支持 */
@media (forced-colors: active) {
  :root {
    --primary-color: CanvasText;
    --primary-color-hover: CanvasText;
    --primary-color-active: CanvasText;
    --primary-color-outline: CanvasText;
    
    --success-color: CanvasText;
    --warning-color: CanvasText;
    --error-color: CanvasText;
    --info-color: CanvasText;
    
    --text-color-primary: CanvasText;
    --text-color-secondary: CanvasText;
    --text-color-disabled: GrayText;
    --border-color: CanvasText;
    --border-color-split: CanvasText;
    --background-color-base: Canvas;
    --background-color-light: Canvas;
    
    --box-shadow-base: none;
    --box-shadow-card: none;
  }
}
