<template>
  <div>
  <a-tabs type="card" @change="callback">
    <a-tab-pane v-if="showJMTab" key="HZ_YJ_DL_JM" tab="精密实验室">
      <div style="float: left;padding:15px 0px 10px 10px;width: 900px;">
        <a-row :gutter="[8,8]">
          <a-col :span="8">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="审核人"  has-feedback>
              <a-input style="z-index: 10" v-model="queryAuditMan" @keyup.enter="getAssignTaskList" @change="getAssignTaskList"/>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="测试项目"  has-feedback>
              <a-input style="z-index: 10" v-model="queryTestName" @keyup.enter="getAssignTaskList" @change="getAssignTaskList"/>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="委托单号"  has-feedback>
              <a-input style="z-index: 10" v-model="queryFolderno" @keyup.enter="getAssignTaskList" @change="getAssignTaskList"/>
            </a-form-item>
          </a-col>
          <a-col :span="10" style="margin-top: 10px">
            <a-button type="primary" style="width:100px;z-index: 10" @click="assgin()">分配检测人</a-button>
          </a-col>
        </a-row>
      </div>

      <div style="background-color: #FFFFFF;padding: 10px" class="table-scroll">
        <div class="box" ref="box">
          <div class="mid">
            <s-table :columns="columns"
                     :data="loadData"
                     :expandedRowKeys="expandedRowKeys"
                     @expand="onTableExpand"
                     bordered
                     :rowKey="(record) => record.id"
                     :row-selection="{
                  selectedRowKeys: selectedRowKeys, selectedRows: selectedRows,
                  onSelectAll: onSelectAll,
                  onSelect:onSelect, columnWidth:40}"
                     ref="table2">
              <template
                slot="sorter"
                slot-scope="text, record, index, columns">
                <a v-if="record.children" @click="handleNo(record.id)" style="text-align: left" >
                  委托单号：{{text}} {{record.createdbyname}}
                </a>
                <p v-else style="text-align: center">{{ text }}</p>
              </template>
            </s-table>
          </div>
        </div>

        <a-modal id="miModalId" title="分配检测人" centered :width="1000" :visible="personVisible && laboratoryId === 'HZ_YJ_DL_JM'" @cancel="personHandleCancel" @ok="personSubmit">
          <div style="margin: 0px 0px 20px 100px">
            <a-row :gutter="[8,8]">
              <a-col :span="10">
                <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="姓名">
                  <a-input v-model="searchTesterParam.USERNAME" @keyup.enter="searchTesters" @change="searchTesters"/>
                </a-form-item>
              </a-col>
              <a-col :span="10">
                <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="工号">
                  <a-input v-model="searchTesterParam.ID" @keyup.enter="searchTesters" @change="searchTesters"/>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <a-spin :spinning="personIsLoading">
            <a-table :columns="personColumns"
                     :data-source="personResultData"
                     bordered
                     :rowKey="(record) => record.ID"
                     :row-selection="{
                  selectedRowKeys: personSelectedRowKeys,
                  selectedRows: personSelectedRows,
                  onChange: personOnSelect,
                  columnWidth:20
                 }"
                     ref="personTable">
            <template
              slot="ORDTASKNUMBER"
              slot-scope="text, record, index, columns">
              <a v-if="record.ORDTASKNUMBER > 0" @click="getOrdTasksByTesterCode(record)" style="text-align: left" >
                {{text}}
              </a>
              <p v-else style="text-align: center">{{ text }}</p>
            </template>
            </a-table>
          </a-spin>
        </a-modal>

        <a-modal id="testingModalId" title="详情" :width="1280" :visible="testingVisible"  @cancel="testingCancel">
          <a-tabs type="card" @change="testingCallback">
            <a-tab-pane key="TESTING_PROJECT" tab="在测测试项目">
              <a-table
                id="testingTableId"
                :columns="testingColumns"
                :data-source="testingDataSource"
                :row-key="record => record.id"
                bordered>
              </a-table>
            </a-tab-pane>
            <a-tab-pane key="GATHER_TEST_PROJECT" tab="汇总">
              <a-table
                id="gatherTestProjectId"
                style="width: 800px;"
                :columns="gatherTestProjectColumns"
                :data-source="gatherTestProjectDataSource"
                :row-key="record => record.id"
                bordered>
              </a-table>
            </a-tab-pane>
          </a-tabs>
          <template slot="footer">
            <a-button @click="testingCancel">关闭</a-button>
          </template>
        </a-modal>

        <a-modal title="分配负责人及计划时间" :width="400" :visible="planTimeVisible  && laboratoryId === 'HZ_YJ_DL_JM'" :confirmLoading="confirmLoading" @cancel="planTimeCancel">
          <template slot="footer">
            <a-button @click="planTimeCancel">
              取消
            </a-button>
            <a-button type="primary" @click="planTimeSubmit" :disabled="isSubmitFlag">
              确定
            </a-button>
          </template>
          <a-spin :spinning="confirmLoading">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="负责人" v-show="personSelectedRows.length > 1"  has-feedback>
              <a-select style="width: 80%" :options="personSelectedOptions" v-model="tester"/>
            </a-form-item>
            <a-form :form="timeForm">
              <a-form-item label="计划时间" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                <a-range-picker format="YYYY-MM-DD"  v-model="timeRange">
                </a-range-picker>
              </a-form-item>
            </a-form>
          </a-spin>
        </a-modal>
      </div>
    </a-tab-pane>
    <a-tab-pane v-if="showCSTab" key="HZ_YJ_DL_CS" tab="第六实验室(HZ)">
      <div style="float: left;padding:10px 0px 10px 10px;">
        <a-button v-if="hasPerm('tLimsOrdtask:updateOrdtaskStatus')" type="primary" style="width:100px;z-index: 10" @click="assgin()">分配检测人</a-button>
        <a-button v-if="hasPerm('testProjectTodoTask:assignPlanStartDate')" type="primary" style="width:120px;z-index: 10;margin-left: 5px" @click="assginPlanStartDate()">分配计划开始日期</a-button>
      </div>
      <div class="box table-scroll" ref="box">
        <div class="mid">
          <s-table :columns="columns"
                   :data="loadData"
                   bordered
                   :rowKey="(record) => record.id"
                   :row-selection="{
                  selectedRowKeys: selectedRowKeys, selectedRows: selectedRows,
                  onSelectAll: onSelectAll,
                  onSelect:onSelect, columnWidth:40}"
                   ref="table3">
            <template
              slot="sorter"
              slot-scope="text, record, index, columns">
              <a v-if="record.children" @click="handleNo(record.id)" style="text-align: left" >
                委托单号：{{text}} {{record.createdbyname}}
              </a>
              <p v-else style="text-align: center">{{ text }}</p>
            </template>
          </s-table>
        </div>
      </div>

      <a-modal id="miModalId" title="分配检测人" centered :width="1000" :visible="personVisible && laboratoryId === 'HZ_YJ_DL_CS'" @cancel="personHandleCancel">
        <template slot="footer">
          <a-button @click="personHandleCancel">
            取消
          </a-button>
          <a-button type="primary" @click="personSubmit" :disabled="isSubmitFlag">
            确定
          </a-button>
        </template>
        <div style="margin: 0px 0px 20px 100px">
          <a-row :gutter="[8,8]">
            <a-col :span="10">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="姓名">
                <a-input v-model="searchTesterParam.USERNAME" @keyup.enter="searchTesters" @change="searchTesters"/>
              </a-form-item>
            </a-col>
            <a-col :span="10">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="工号">
                <a-input v-model="searchTesterParam.ID" @keyup.enter="searchTesters" @change="searchTesters"/>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <a-spin :spinning="personIsLoading">
          <a-table :columns="personColumns"
                   :data-source="personResultData"
                   :customRow="customRowEvent"
                   bordered
                   :rowKey="(record) => record.ID"
                   :row-selection="{
                  type: 'radio',
                  selectedRowKeys: personSelectedRowKeys,
                  selectedRows: personSelectedRows,
                  onChange: personOnSelect,
                  columnWidth:20
                 }"
                   ref="personTable">
          </a-table>
        </a-spin>
      </a-modal>

      <a-modal title="分配计划开始日期" :width="800" :visible="planStartDateVisible" :confirmLoading="confirmLoading">
        <a-spin :spinning="confirmLoading">
          <a-form>
            <a-table :columns="middleCheckInfoColumns"
                     :data-source="middleCheckInfoList"
                     bordered
                     :pagination="false">
            </a-table>
            <a-form-item style="margin: 30px 0px 0px 0px" label="计划开始日期" :labelCol="inBoxLabelCol" :wrapperCol="inBoxWrapperCol" has-feedback>
              <a-date-picker v-model="planStartDates" />
            </a-form-item>
          </a-form>
        </a-spin>
        <template slot="footer">
          <div>
            <a-button @click="planStartDateCancel">取消</a-button>
            <a-popconfirm v-if="selectedRows.length > 1"
              title="确认将以上测试项目分配同一个计划开始日期？"
              ok-text="确认"
              cancel-text="取消"
              @confirm="planStartDateSubmit"
              @cancel="handlePopCancel">
            <a-button type="primary">确定</a-button>
            </a-popconfirm>
            <a-button v-else type="primary" @click="planStartDateSubmit">确定</a-button>
          </div>
        </template>
      </a-modal>
    </a-tab-pane>

    <a-tab-pane v-if="showAQTab" key="HZ_YJ_DL_AQ" tab="第四实验室">
      <taskAssignmentOfSafety ref="safetyTable">
      </taskAssignmentOfSafety>
    </a-tab-pane>

    <a-tab-pane v-if="showAQRLTab" key="HZ_YJ_DL_AQ_RL" tab="第四实验室-日历寿命">
      <div style="float: left;padding:15px 0px 10px 10px;width: 50%;">
        <a-row :gutter="[8,8]">
          <a-col :span="10">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="分配情况"  has-feedback>
              <a-select show-search option-filter-prop="children" style="width: 150px;z-index: 10" @change="getAssignTaskList" v-model="searchAssignDateParam">
                <a-select-option value="all">
                  所有
                </a-select-option>
                <a-select-option value="alreadyAssignOfRL">
                  已分配计划时间
                </a-select-option>
                <a-select-option value="notAssign">
                  未分配计划时间
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="10" style="margin-top: 10px">
            <a-button v-if="hasPerm('tLimsOrdtask:updateOrdtaskStatus')" type="primary" style="width:100px;z-index: 10" @click="assgin()">分配检测人</a-button>
            <a-button v-if="hasPerm('testProjectTodoTask:assignPlanStartDate')" type="primary" style="width:120px;z-index: 10;margin-left: 5px" @click="assginPlanStartDate()">分配计划开始日期</a-button>
          </a-col>
        </a-row>
      </div>
      <div class="box table-scroll" ref="box">
        <div class="mid">
          <s-table :columns="columns"
                   :data="loadData"
                   :expandedRowKeys="expandedRowKeys"
                   @expand="onTableExpand"
                   bordered
                   :rowKey="(record) => record.id"
                   :row-selection="{
                  selectedRowKeys: selectedRowKeys, selectedRows: selectedRows,
                  onSelectAll: onSelectAll,
                  onSelect:onSelect, columnWidth:40}"
                   ref="table5">
            <template
              slot="sorter"
              slot-scope="text, record, index, columns">
              <a v-if="record.children" @click="handleNo(record.id)" style="text-align: left" >
                委托单号：{{text}} {{record.createdbyname}}
              </a>
              <p v-else style="text-align: center">{{ text }}</p>
            </template>
          </s-table>
        </div>
      </div>

      <a-modal id="miModalId" title="分配检测人" centered :width="1000" :visible="personVisible && laboratoryId === 'HZ_YJ_DL_AQ_RL'" @cancel="personHandleCancel">
        <template slot="footer">
          <a-button @click="personHandleCancel">
            取消
          </a-button>
          <a-button type="primary" @click="personSubmit" :disabled="isSubmitFlag">
            确定
          </a-button>
        </template>
        <div style="margin: 0px 0px 20px 100px">
          <a-row :gutter="[8,8]">
            <a-col :span="10">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="姓名">
                <a-input v-model="searchTesterParam.USERNAME" @keyup.enter="searchTesters" @change="searchTesters"/>
              </a-form-item>
            </a-col>
            <a-col :span="10">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="工号">
                <a-input v-model="searchTesterParam.ID" @keyup.enter="searchTesters" @change="searchTesters"/>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <a-spin :spinning="personIsLoading">
          <a-table :columns="personColumns"
                   :data-source="personResultData"
                   :customRow="customRowEvent"
                   bordered
                   :rowKey="(record) => record.ID"
                   :row-selection="{
                  type: 'radio',
                  selectedRowKeys: personSelectedRowKeys,
                  selectedRows: personSelectedRows,
                  onChange: personOnSelect,
                  columnWidth:20
                 }"
                   ref="personTable">
          </a-table>
        </a-spin>
      </a-modal>

      <a-modal title="分配计划开始日期" :width="800" :visible="planStartDateVisible" :confirmLoading="confirmLoading">
        <a-spin :spinning="confirmLoading">
          <a-form>
            <a-table :columns="middleCheckInfoColumns"
                     :data-source="middleCheckInfoList"
                     bordered
                     :pagination="false">
            </a-table>
            <a-form-item style="margin: 30px 0px 0px 0px" label="计划开始日期" :labelCol="inBoxLabelCol" :wrapperCol="inBoxWrapperCol" has-feedback>
              <a-date-picker v-model="planStartDates" />
            </a-form-item>
          </a-form>
        </a-spin>
        <template slot="footer">
          <div>
            <a-button @click="planStartDateCancel">取消</a-button>
            <a-popconfirm v-if="selectedRows.length > 1"
                          title="确认将以上测试项目分配同一个计划开始日期？"
                          ok-text="确认"
                          cancel-text="取消"
                          @confirm="planStartDateSubmit"
                          @cancel="handlePopCancel">
              <a-button type="primary">确定</a-button>
            </a-popconfirm>
            <a-button v-else type="primary" @click="planStartDateSubmit">确定</a-button>
          </div>
        </template>
      </a-modal>
    </a-tab-pane>
    <a-tab-pane v-if="showJMCSTab" key="JM_YJ_DL_CS" tab="第六实验室(JM)">
      <div style="float: left;padding:10px 0px 10px 10px;">
        <a-button v-if="hasPerm('tLimsOrdtask:updateOrdtaskStatus')" type="primary" style="width:100px;z-index: 10" @click="assgin()">分配检测人</a-button>
        <a-button v-if="hasPerm('testProjectTodoTask:assignPlanStartDate')" type="primary" style="width:120px;z-index: 10;margin-left: 5px" @click="assginPlanStartDate()">分配计划开始日期</a-button>
      </div>
      <div class="box table-scroll" ref="box">
        <div class="mid">
          <s-table :columns="columns"
                   :data="loadData"
                   bordered
                   :rowKey="(record) => record.id"
                   :row-selection="{
                  selectedRowKeys: selectedRowKeys, selectedRows: selectedRows,
                  onSelectAll: onSelectAll,
                  onSelect:onSelect, columnWidth:40}"
                   ref="table_jmcs">
            <template
              slot="sorter"
              slot-scope="text, record, index, columns">
              <a v-if="record.children" @click="handleNo(record.id)" style="text-align: left" >
                委托单号：{{text}} {{record.createdbyname}}
              </a>
              <p v-else style="text-align: center">{{ text }}</p>
            </template>
          </s-table>
        </div>
      </div>

      <a-modal id="jmModalId" title="分配检测人" centered :width="1000" :visible="personVisible && laboratoryId === 'JM_YJ_DL_CS'" @cancel="personHandleCancel">
        <template slot="footer">
          <a-button @click="personHandleCancel">
            取消
          </a-button>
          <a-button type="primary" @click="personSubmit" :disabled="isSubmitFlag">
            确定
          </a-button>
        </template>
        <div style="margin: 0px 0px 20px 100px">
          <a-row :gutter="[8,8]">
            <a-col :span="10">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="姓名">
                <a-input v-model="searchTesterParam.USERNAME" @keyup.enter="searchTesters" @change="searchTesters"/>
              </a-form-item>
            </a-col>
            <a-col :span="10">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="工号">
                <a-input v-model="searchTesterParam.ID" @keyup.enter="searchTesters" @change="searchTesters"/>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <a-spin :spinning="personIsLoading">
          <a-table :columns="personColumns"
                   :data-source="personResultData"
                   :customRow="customRowEvent"
                   bordered
                   :rowKey="(record) => record.ID"
                   :row-selection="{
                  type: 'radio',
                  selectedRowKeys: personSelectedRowKeys,
                  selectedRows: personSelectedRows,
                  onChange: personOnSelect,
                  columnWidth:20
                 }"
                   ref="personTable">
          </a-table>
        </a-spin>
      </a-modal>

      <a-modal title="分配计划开始日期" :width="800" :visible="planStartDateVisible" :confirmLoading="confirmLoading">
        <a-spin :spinning="confirmLoading">
          <a-form>
            <a-table :columns="middleCheckInfoColumns"
                     :data-source="middleCheckInfoList"
                     bordered
                     :pagination="false">
            </a-table>
            <a-form-item style="margin: 30px 0px 0px 0px" label="计划开始日期" :labelCol="inBoxLabelCol" :wrapperCol="inBoxWrapperCol" has-feedback>
              <a-date-picker v-model="planStartDates" />
            </a-form-item>
          </a-form>
        </a-spin>
        <template slot="footer">
          <div>
            <a-button @click="planStartDateCancel">取消</a-button>
            <a-popconfirm v-if="selectedRows.length > 1"
                          title="确认将以上测试项目分配同一个计划开始日期？"
                          ok-text="确认"
                          cancel-text="取消"
                          @confirm="planStartDateSubmit"
                          @cancel="handlePopCancel">
              <a-button type="primary">确定</a-button>
            </a-popconfirm>
            <a-button v-else type="primary" @click="planStartDateSubmit">确定</a-button>
          </div>
        </template>
      </a-modal>
    </a-tab-pane>
  </a-tabs>
  <!-- 弹窗 start -->
  <InquiryModal v-if="isShowModal" :modalData="modalData" @cancel="handleModalCancel"></InquiryModal>
  <!-- 弹窗 end -->
  </div>
</template>
<script>
import {
  assignTask, getFolderByFolderNo,
  getLimsOrdtaskList, getOrdTasksByTesterCode, getTestPerson
} from '@/api/modular/system/limsManager'
  import { STable } from '@/components'
  import taskAssignmentOfSafety from "../task/taskAssignmentOfSafety.vue"
import moment from "moment/moment";
import {
  assignPlanStartDate,
  getMidCheckInfoByOrdTaskIds
} from "@/api/modular/system/testProgressManager";
import { mapActions, mapGetters } from 'vuex'
import InquiryModal from "@/views/system/testProgress/orderInquiry/components/inquiryModal.vue";

  export default {
    components: {
     STable,InquiryModal,taskAssignmentOfSafety
    },
    data() {
      return {
        loadData: parameter => {
          let labId = "HZ_YJ_DL_JM"
          let testName = null
          if (this.laboratoryId === "HZ_YJ_DL_CS"||this.laboratoryId === "JM_YJ_DL_CS") {
            labId = this.laboratoryId
            testName = "日历寿命"
          } else if (this.laboratoryId === "HZ_YJ_DL_AQ") {
            labId = this.laboratoryId
          } else if (this.laboratoryId === "HZ_YJ_DL_AQ_RL") {
            labId = "HZ_YJ_DL_AQ"
            testName = "日历寿命"
          }
          return getLimsOrdtaskList(Object.assign(parameter, { status: "Assign", laboratoryId: labId, testName: testName,
            queryTestName: this.queryTestName, queryAuditMan: this.queryAuditMan, queryFolderno: this.queryFolderno, searchAssignDateParam: this.searchAssignDateParam })).then((res) => {
            this.resultData = res.data
            let parentData = JSON.parse(JSON.stringify(this.resultData.rows))
            let folderNoSet = [...new Set(parentData.map(item => item.folderno))];
            this.resultData.rows = []
            for (let i = 0; i < folderNoSet.length; i++) {
              this.resultData.rows[i] = {
                  id: folderNoSet[i],
                  sorter: folderNoSet[i],
                  isParent: true,
                  children: []
                }
                parentData.forEach((item) => {
                  if (item.folderno === folderNoSet[i]) {
                    this.resultData.rows[i].folderid = item.folderid
                    this.resultData.rows[i].createdbyname = item.createdbyname
                    this.resultData.rows[i].children.push(item)
                  }
                })
            }
            this.expandedRowKeys = folderNoSet
            return this.resultData
          })
        },
        queryTestName: null,
        queryAuditMan: null,
        queryFolderno: null,
        searchAssignDateParam: null,
        isShowModal: false,
        isSubmitFlag: false,
        personVisible: false,
        confirmLoading: false,
        planTimeVisible: false,
        testingVisible: false,
        testingDataSource: [],
        gatherTestProjectDataSource: [],
        middleCheckInfoList: [],
        allPersonResultData: [],
        searchTesterParam: {},
        planStartDates: null,
        planStartDateVisible: false,
        planDateVisible: false,
        timeRange: null,
        timeForm: this.$form.createForm(this,{ name: 'timeForm' }),
        personIsLoading: false,
        height: 200,
        address: this.hasPerm('progress:all') ? 'all' : 'none',
        show: false,
        inBoxLabelCol: {
          sm: {
            span: 11
          }
        },
        inBoxWrapperCol: {
          sm: {
            span: 9
          }
        },
        labelCol: {
          sm: {
            span: 6
          }
        },
        wrapperCol: {
          sm: {
            span: 15
          }
        },
        queryParam: {},
        data: [],
        resultData: [],
        personResultData: [],
        headData: [],
        allAddress: null,
        gatherTestProjectColumns: [
          {
            title: '序号',
            dataIndex: 'index',
            align: 'center',
            width: '20%',
            ellipsis: true,
            customRender: (text, record, index) => `${index + 1}`
          }, {
            title: '测试员或参与人',
            width: '20%',
            align: 'center',
            dataIndex: 'TESTER',
          }, {
            title: '测试项目名称',
            dataIndex: 'TESTNAME',
            align: 'center',
            width: '40%',
          }, {
            title: '在测样品数量',
            dataIndex: 'TESTSAMPLECOUNT',
            align: 'center',
            width: '20%',
          }
        ],
        testingColumns: [
          {
            title: '序号',
            dataIndex: 'index',
            align: 'center',
            width: 20,
            ellipsis: true,
            customRender: (text, record, index) => `${index + 1}`
          }, {
            title: '测试员',
            width: 30,
            align: 'center',
            dataIndex: 'tester',
          }, {
            title: '参与人',
            width: 30,
            align: 'center',
            dataIndex: 'participator',
            customRender: text => text || "-"
          }, {
            title: '委托单号',
            width: 40,
            align: 'center',
            dataIndex: 'folderno',
          }, {
            title: '委托人工号',
            dataIndex: 'createdbyid',
            align: 'center',
            width: 30,
          }, {
            title: '委托人',
            dataIndex: 'createdbyname',
            align: 'center',
            width: 30,
          }, {
            title: '委托部门',
            dataIndex: 'createdbyorgname',
            align: 'center',
            width: 40,
          }, {
            title: '委托时间',
            dataIndex: 'createdtime',
            align: 'center',
            width: 50,
          }, {
            title: '测试项目编号',
            dataIndex: 'testcode',
            align: 'center',
            width: 35,
          }, {
            title: '测试项目名称',
            dataIndex: 'testname',
            align: 'center',
            width: 70,
          }, {
            title: '测试数量',
            dataIndex: 'testnumber',
            align: 'center',
            width: 25,
            customRender: text => text || "-"
          }, {
            title: '一级分类',
            dataIndex: 'firstcategory',
            align: 'center',
            width: 25,
          }, {
            title: '二级分类',
            dataIndex: 'secondcategory',
            align: 'center',
            width: 25,
          }, {
            title: '计划开始时间',
            dataIndex: 'planstarttime',
            align: 'center',
            width: 50,
          }, {
            title: '计划结束时间',
            dataIndex: 'planendtime',
            align: 'center',
            width: 50,
          }, {
            title: '检测时长',
            dataIndex: 'testduration',
            align: 'center',
            width: 30,
            customRender: (text, record, index) => {
              return text + record.timeunit
            }
          }
        ],
        middleCheckInfoColumns: [
          {
            title: '序号',
            dataIndex: 'index',
            align: 'center',
            width: 30,
            ellipsis: true,
            customRender: (text, record, index) => `${index + 1}`
          },
          {
            title: '委托单号',
            dataIndex: 'folderNo',
            align: 'center',
            width: 40,
          },
          {
            title: '测试项目别名',
            dataIndex: 'alias',
            align: 'center',
            width: 40,
          },
          {
            title: '中检温度',
            dataIndex: 'middleCheckTemp',
            align: 'center',
            width: 50,
          },
          {
            title: '设备量程',
            dataIndex: 'equipRange',
            align: 'center',
            width: 50,
          },
          {
            title: '初始阶段测试上柜类型',
            dataIndex: 'loadingType',
            align: 'center',
            width: 60,
          },
          {
            title: '产品名称',
            dataIndex: 'productName',
            align: 'center',
            width: 60,
          },
          {
            title: '测试类型',
            dataIndex: 'testType',
            align: 'center',
            width: 60,
          }
        ],
        personColumns: [
          {
            title: '序号',
            dataIndex: 'index',
            align: 'center',
            width: 30,
            ellipsis: true,
            customRender: (text, record, index) => `${index + 1}`
          },
          {
            title: '工号',
            dataIndex: 'ID',
            align: 'center',
            width: 40,
          },
          {
            title: '姓名',
            dataIndex: 'USERNAME',
            align: 'center',
            width: 40,
          },
          {
            title: '部门',
            dataIndex: 'ORGNAME',
            align: 'center',
            width: 50,
          },
          {
            title: '在测测试项目数量',
            dataIndex: 'ORDTASKNUMBER',
            align: 'center',
            width: 50,
            scopedSlots: { customRender: 'ORDTASKNUMBER' },
          },
          {
            title: '在测样品数量',
            dataIndex: 'ORDERNUMBER',
            align: 'center',
            width: 50,
          },
          {
            title: '在测委托单数量',
            dataIndex: 'FOLDERNUMBER',
            align: 'center',
            width: 50,
          },
        ],
        columns: [
          {
            title: '试验顺序',
            dataIndex: 'sorter',
            // align: 'center',
            width: 250,
            scopedSlots: { customRender: 'sorter' },
            // customRender: (text, record, index) => `${index + 1}`
          }, {
            title: '流程状态',
            width: 80,
            align: 'center',
            dataIndex: 'status',
            customRender: (text, record, index) => {
              switch (text) {
                case 'Examine': return '结果审核'
                case 'Outtestassign': return '委外测试任务分配'
                case 'Result_return': return '退回'
                case 'Draft': return '新建'
                case 'assignPlanStartDate': return 'PMC分配计划开始时间'
                case 'Preschedule': return '待排程'
                case 'Assign': return '任务分配'
                case 'Result': return '结果录入'
                case 'Review': return '结果复核'
                case 'Report': return '报告编制'
                case 'Done': return '完成'
                case 'Outsource': return '任务委外'
                case 'Backfill': return '数据回填'
                case 'Testreport': return '测试报告上传'
                case 'Herbaceousapprove': return '草本审核'
                case 'Herbaceousaudit': return '草本审批'
                case 'Originalupload': return '正本上传'
                case 'Originalapprove': return '正本审核'
                case 'Originalaudit': return '正本审批'
                case 'HerbaceousapproveReject': return '草本审核退回'
                case 'HerbaceousauditReject': return '草本审批退回'
                case 'OriginalapproveReject': return '正本审核退回'
                case 'Cancel': return '已取消'
                case 'Inreview': return '报告审核中'
                case 'OriginalauditReject': return '正本审批退回'
                case 'Cfmschedule': return '排程确认'
                case 'Testassign': return '测试任务分配'
                case "OrdtaskUnderChange": return '委托变更中'
                default: return ''
              }
          }
          },{
            title: '委托单号',
            width: 120,
            align: 'center',
            dataIndex: 'folderno',
          },{
            title: '测试项目编号',
            width: 100,
            align: 'center',
            dataIndex: 'testcode',
          }, {
            title: '测试数量',
            width: 120,
            align: 'center',
            dataIndex: 'testnumber',
          },  {
            title: '测试项目名称',
            width: 120,
            align: 'center',
            dataIndex: 'testname',
          }, {
            title: '测试项目别名',
            width: 120,
            align: 'center',
            dataIndex: 'alias',
          },  {
            title: '审核人',
            width: 120,
            align: 'center',
            dataIndex: 'otherProperties.auditor',
          },  {
            title: '标准编号',
            width: 70,
            align: 'center',
            dataIndex: 'methodcode',
          }, {
            title: '标准名称',
            width: 100,
            align: 'center',
            dataIndex: 'methodname',
          }, {
            title: '计划开始时间',
            width: 120,
            align: 'center',
            dataIndex: 'planstarttime',
          }, {
            title: '计划结束时间',
            width: 120,
            align: 'center',
            dataIndex: 'planendtime',
          }, {
            title: '检测内容',
            width: 200,
            align: 'center',
            dataIndex: 'testcontent',
          }, {
            title: '判定标准',
            width: 200,
            align: 'center',
            dataIndex: 'judgebasis',
          }, {
            title: '测试步骤',
            width: 150,
            align: 'center',
            dataIndex: 'teststep',
          }, {
            title: '备注',
            width: 100,
            align: 'center',
            dataIndex: 'remark',
          }
        ],
        selectedRowKeys: [],
        expandedRowKeys: [],
        personSelectedRowKeys: [],
        selectedRows: [],
        laboratoryId: null,
        showJMTab:false,
        showAQTab:false,
        showCSTab:false,
        showJMCSTab:false,
        showAQRLTab:false,
        personSelectedRows: [],
        personSelectedOptions: [],
        scrollY: document.documentElement.clientHeight > 700 ? 630 : 290,
        tester: null
      }
    },
    created() {
      document.documentElement.style.setProperty(`--height`, `${this.scrollY}px`)
      console.log('this.userInfo',this.userInfo)
      if (this.userInfo.account === "superAdmin") {
        this.laboratoryId = "HZ_YJ_DL_JM"
        this.showJMTab = true
        this.showCSTab = true
        this.showJMCSTab = true
        this.showAQRLTab = true
        this.showAQTab = true
        this.personLoadData()
      } else {
        // "精密实验室-测试组长"和"精密实验室-计划管理员"角色才能看到【精密实验室】标签页
        let jmList = this.userInfo.roles.filter(item => item.id === "1712686842365419522" || item.id === "1720008384229163010")
        // "研发检测中心-测试组长"和"研发检测中心-PMC"角色才能看到【研发检测中心】标签页
        let csList = this.userInfo.roles.filter(item => item.id === "1676772241413427202" || item.id === "1722493545281904641")
        // "第六实验室(JM)-测试组长"和"第六实验室(JM)-PMC"角色才能看到【第六实验室(JM)】标签页
        let jmcsList = this.userInfo.roles.filter(item => item.id === "1839274430373945345" || item.id === "1839275601205518338")
        // "第四实验室-测试组长"和"第四实验室-PMC"角色才能看到【第四实验室】标签页
        let aqList = this.userInfo.roles.filter(item => item.id === "1754070159908036609" || item.id === "1773588429358874625")
        // "第四实验室-日历寿命测试组长"和"第四实验室-日历寿命PMC"角色才能看到【第四实验室-日历寿命】标签页
        let aqrlsmList = this.userInfo.roles.filter(item => item.id === "1824309048870113281" || item.id === "1824292015793180673")
        this.showJMTab = jmList.length > 0
        this.showCSTab = csList.length > 0
        this.showJMCSTab = jmcsList.length > 0
        this.showAQTab = aqList.length > 0
        this.showAQRLTab = aqrlsmList.length > 0
        if (this.showJMTab) {
          this.laboratoryId = "HZ_YJ_DL_JM"
        } else if (this.showCSTab) {
          this.laboratoryId = "HZ_YJ_DL_CS"
        } else if (this.showJMCSTab) {
          this.laboratoryId = "JM_YJ_DL_CS"
        } else if (this.showAQTab) {
          this.laboratoryId = "HZ_YJ_DL_AQ"
        } else if (this.showAQRLTab) {
          this.laboratoryId = "HZ_YJ_DL_AQ_RL"
        }
        this.personLoadData()
      }
    },
    computed: {
    ...mapGetters(['userInfo'])
    },
    mounted() {

    },
    methods: {
      handleNo(folderNo) {
        getFolderByFolderNo({folderno: folderNo}).then((res) => {
          if (res.data && res.data.length > 0) {
            this.modalData = res.data[0]
            this.modalData.pageType = 'taskAssignment'
            this.isShowModal = true
          } else {
            this.$message.warning('获取委托单信息失败，请联系管理员！')
          }
        })
      },
      handleModalCancel() {
        this.isShowModal = false
      },
      onTableExpand(expanded, record) {
        if (expanded) {
          this.expandedRowKeys.push(record.id)
        } else {
          this.expandedRowKeys.splice(this.expandedRowKeys.indexOf(record.id), 1)
        }
      },
      getAssignTaskList() {
        if (this.laboratoryId === 'HZ_YJ_DL_JM' && this.$refs.table2) {
          this.$refs.table2.refresh()
        } else if (this.laboratoryId === 'HZ_YJ_DL_AQ_RL' && this.$refs.table5) {
          this.$refs.table5.refresh()
        }
      },
      customRowEvent(record) {
        return {
          on: {
            click: () => {
              this.personSelectedRows = []
              this.personSelectedRowKeys = []
              this.personSelectedRows.push(record)
              this.personSelectedRowKeys.push(record.ID)
            },
          },
        };
      },
      getOrdTasksByTesterCode(record) {
        getOrdTasksByTesterCode(record.ID).then((res) => {
          // console.log(res)
          if (res.success === true) {
            this.testingVisible = true
            this.testingDataSource = res.data.testingProjects
            this.gatherTestProjectDataSource = res.data.gatherTestProjects
            this.gatherTestProjectDataSource.forEach(item => {
              item.TESTER = record.USERNAME
            })
          } else {
            this.$message.warning('获取在测测试项目失败：' + res.message)
          }
        })
      },
      testingCallback(key) {

      },
      callback(key) {
        this.laboratoryId = key
        this.clearOldData()
        this.personLoadData()
        this.refreshData()
        // this.$refs.personTable.refresh()
      },
      handlePopCancel() {
        this.$message.success("取消成功")
      },
      planStartDateSubmit() {
        // console.log('this.planStartDates',this.planStartDates)
        // console.log('this.selectedRows',this.selectedRows)
        if (this.planStartDates == null) {
          this.$message.warning('请选择计划开始日期！')
          return
        }
        var targetDatas = this.selectedRows.filter(item => item.isParent !== true).map(item => ({
          ordTaskId: item.id,
          planStartDate: this.planStartDates
        }))
        // console.log('targetDatas',targetDatas)
         assignPlanStartDate(targetDatas).then((res) => {
           if (res.success) {
             setTimeout(() => {
               this.clearOldData()
               if (this.laboratoryId === 'HZ_YJ_DL_CS' && this.$refs.table3) {
                 this.$refs.table3.refresh()
               }else if (this.laboratoryId === 'JM_YJ_DL_CS' && this.$refs.table_jmcs) {
                   this.$refs.table_jmcs.refresh()
               } else if (this.laboratoryId === 'HZ_YJ_DL_AQ_RL' && this.$refs.table5) {
                 this.$refs.table5.refresh()
               }
               this.$message.success('计划开始日期分配成功！')
             }, 200)
           } else {
             this.$message.error(res.message)
           }
         })
      },
      // planDateSubmit() {
      //   if (this.timeRange === null || this.timeRange.length !== 2) {
      //     this.$message.warning('请选择计划开始和结束时间！')
      //     return
      //   }
      //   var targetDatas = this.selectedRows.filter(item => item.isParent !== true).map(item => ({
      //     ordTaskId: item.id,
      //     planStartDate: moment(this.timeRange[0]).format('YYYY-MM-DD HH:mm:ss'),
      //     planEndDate: moment(this.timeRange[1]).format('YYYY-MM-DD HH:mm:ss')
      //   }))
      //   assignPlanDate(targetDatas).then((res) => {
      //     if (res.success) {
      //       setTimeout(() => {
      //         this.clearOldData()
      //         this.$refs.table4.refresh()
      //         this.$message.success('计划时间分配成功！')
      //       }, 200)
      //     } else {
      //       this.$message.error(res.message)
      //     }
      //   })
      // },
      planTimeSubmit() {
        if (this.personSelectedRows.length > 1 && this.tester == null) {
          this.$message.warning('请选择负责人！')
          return
        }
        if (this.timeRange === null || this.timeRange.length !== 2) {
          this.$message.warning('请选择计划开始和结束时间！')
          return
        }
        this.executeAssignTaskOfJM(this.timeRange)
      },
      // planTimeSubmitOfAQ () {
      //   if (this.personSelectedRows.length > 1 && this.tester == null) {
      //     this.$message.warning('请选择负责人！')
      //     return
      //   }
      //   this.executeAssignTaskOfAQ()
      // },
      clearOldData() {
        this.personSelectedRows = []
        this.personSelectedRowKeys = []
        this.selectedRows = []
        this.selectedRowKeys = []
        this.personVisible = false
        this.personIsLoading = false
        this.planTimeVisible = false
        this.testingVisible = false
        this.planStartDateVisible = false
        this.planDateVisible = false
        this.timeRange = null
        this.planStartDates = null
        this.tester = null
        this.isSubmitFlag = false
      },
      testingCancel() {
        this.testingVisible = false
      },
      planTimeCancel() {
        this.timeRange = null
        this.planTimeVisible = false
        this.tester = null
      },
      planStartDateCancel() {
        this.planStartDates = null
        this.planStartDateVisible = false
      },
      planDateCancel() {
        this.timeRange = null
        this.planDateVisible = false
      },
      personOnSelect(selectedRowKeys, selectedRows) {
        this.personSelectedRows = selectedRows
        this.personSelectedRowKeys = selectedRowKeys
        this.personSelectedOptions = []
        for(let i = 0; i < this.personSelectedRows.length; i++) {
          this.personSelectedOptions.push({
            value: this.personSelectedRows[i].ID,
            label: this.personSelectedRows[i].USERNAME
          })
        }
      },
      personSubmit() {
        if (this.personSelectedRows.length === 0) {
          this.$message.warning('请选择一人进行任务分配！')
        } else if (this.personSelectedRows.length === 1) {
          if(this.laboratoryId === 'HZ_YJ_DL_JM' || this.laboratoryId == null || this.laboratoryId === 'HZ_YJ_DL_AQ') {
            this.planTimeVisible = true
          } else {
            this.executeAssignTaskOfYF()
          }
        } else if (this.personSelectedRows.length >= 2) {
          if (this.laboratoryId === 'HZ_YJ_DL_JM' || this.laboratoryId === 'HZ_YJ_DL_AQ') {
            this.planTimeVisible = true
          }
        }
      },
      executeAssignTaskOfYF() {
        let person = {
          appointTester: "appointTester",
          type: "submit",
          userName: this.personSelectedRows[0].USERNAME,
          operation: "分配检测人",
          userId: this.personSelectedRows[0].ID,
          opinion: "分配检测人"
          // planStartTime: moment(planStartDates).format('YYYY-MM-DD 00:00:00'),
          // planEndTime: moment(planStartDates).format('YYYY-MM-DD 00:00:00')
        }
        this.executeAssignTask(person)
      },
      refreshData() {
        // console.log('this.$refs.table2',this.$refs.table2)
        if (this.laboratoryId === 'HZ_YJ_DL_JM' && this.$refs.table2) {
          this.$refs.table2.refresh()
        } else if (this.laboratoryId === 'HZ_YJ_DL_CS' && this.$refs.table3) {
          this.$refs.table3.refresh()
        } else if (this.laboratoryId === 'HZ_YJ_DL_AQ' && this.$refs.safetyTable) {
          this.$refs.safetyTable.getAssignTaskList()
        } else if (this.laboratoryId === 'HZ_YJ_DL_AQ_RL' && this.$refs.table5) {
          this.$refs.table5.refresh()
        } else if (this.laboratoryId === 'JM_YJ_DL_CS' && this.$refs.table_jmcs) {
          this.$refs.table_jmcs.refresh()
        }
      },
      // executeAssignTaskOfAQ() {
      //   let person = {
      //     appointTester: "appointTester",
      //     type: "submit",
      //     userName: this.personSelectedRows[0].USERNAME,
      //     operation: "分配检测人",
      //     userId: this.personSelectedRows[0].ID,
      //     opinion: "分配检测人"
      //   }
      //   if (this.personSelectedRows.length > 1) {
      //     let participatorArr = []
      //     let participatorCodeArr = []
      //     for (let i = 0; i < this.personSelectedRows.length; i++) {
      //       if(this.personSelectedRows[i].ID === this.tester) { // 负责人
      //         person.userName = this.personSelectedRows[i].USERNAME
      //         person.userId = this.personSelectedRows[i].ID
      //       } else { // 参与人数组
      //         participatorArr.push(this.personSelectedRows[i].USERNAME)
      //         participatorCodeArr.push(this.personSelectedRows[i].ID)
      //       }
      //     }
      //     person.participator = participatorArr.join(',')
      //     person.participatorCode = participatorCodeArr.join(',')
      //   }
      //   this.executeAssignTask(person)
      // },
      executeAssignTaskOfJM(timeRange) {
        let person = {
          appointTester: "appointTester",
          type: "submit",
          userName: this.personSelectedRows[0].USERNAME,
          operation: "分配检测人",
          userId: this.personSelectedRows[0].ID,
          opinion: "分配检测人",
          planStartTime: moment(timeRange[0]).format('YYYY-MM-DD HH:mm:ss'),
          planEndTime: moment(timeRange[1]).format('YYYY-MM-DD HH:mm:ss')
        }
        if (this.personSelectedRows.length > 1) {
          let participatorArr = []
          let participatorCodeArr = []
          for (let i = 0; i < this.personSelectedRows.length; i++) {
            if(this.personSelectedRows[i].ID === this.tester) { // 负责人
              person.userName = this.personSelectedRows[i].USERNAME
              person.userId = this.personSelectedRows[i].ID
            } else { // 参与人数组
              participatorArr.push(this.personSelectedRows[i].USERNAME)
              participatorCodeArr.push(this.personSelectedRows[i].ID)
            }
          }
          person.participator = participatorArr.join(',')
          person.participatorCode = participatorCodeArr.join(',')
        }
        this.executeAssignTask(person)
      },
      executeAssignTask(person) {
        this.personIsLoading = true
        this.isSubmitFlag = true
        let orderTasks = this.selectedRows.filter(item => item.id.length > 12) //过滤掉测试项目的父项
        assignTask({orderTasks:orderTasks, person:person}).then((res) => {
          if (res.success === true) {
            setTimeout(() => {
              this.clearOldData()
              this.refreshData()
              this.$message.success('任务分配成功')
            }, 300)
          } else {
            this.$message.warning('任务分配失败：' + res.message)
            this.personIsLoading = false
            this.isSubmitFlag = false
          }
        })
      },
      personHandleCancel() {
        this.personSelectedRows = []
        this.personSelectedRowKeys = []
        this.personSelectedOptions = []
        this.personVisible = false
      },
      onSelectAll(selected, selectedRows, changeRows) {
        this.selectedRows = selectedRows
        this.selectedRowKeys = selectedRows.map(item => item.id)
      },
      onSelect(record, selected, selectedRows, nativeEvent) {
        let initSelectedRows = JSON.parse(JSON.stringify(selectedRows))
        let selectedRowKeys = initSelectedRows.map(item => item.id)
        this.selectedRowKeys = JSON.parse(JSON.stringify(selectedRowKeys))
        this.selectedRows = selectedRows
        let parentData = this.resultData.rows
        let childData = []
        parentData.forEach(item => {
          item.children.forEach(res => {
            childData.push(res)
          });
        });
        if (selected) { // 判断是选择还是取消
          if (record.isParent) { // 判断是父项还是子项,父项：查询到对应的子项然后push
            let child = childData.filter(obj => obj.folderid === record.folderid);
            child.forEach(object => {
              if (!this.selectedRows.includes(object)) {
                this.selectedRows.push(object)
                this.selectedRowKeys.push(object.id)
              }
            })
          } else { // 如果是子项，判断selectedRows里兄弟项是否被全选中，全选中需要push父项
            let brother = childData.filter(obj => obj.folderid === record.folderid);
            let selectedRows = this.selectedRows.filter(obj => obj.folderid === record.folderid);
            if (brother.length === selectedRows.length) {
              let parent = parentData.filter(obj => obj.folderid === record.folderid);
              this.selectedRows.push(parent[0])
              this.selectedRowKeys.push(parent[0].id)
            }
          }
        } else {
          if (record.isParent) { // 判断是父项还是子项
            let child = childData.filter(obj => obj.folderid === record.folderid);
            child.forEach(object => {
              const indexRow = this.selectedRows.findIndex(data => data.folderid === object.folderid);
              const indexKey = this.selectedRowKeys.findIndex(data => data === object.id);
              if (indexRow > -1) {
                this.selectedRows.splice(indexRow, 1);
              }
              if (indexKey > -1) {
                this.selectedRowKeys.splice(indexKey, 1);
              }
            })
          } else { // 如果是子项，判断selectedRows里兄弟项是否被全选中，全选中需要splice父项
            let brother = childData.filter(obj => obj.folderid === record.folderid);
            let selectedRows = this.selectedRows.filter(obj => obj.folderid === record.folderid);
            if (brother.length === selectedRows.length) {
              let parent = parentData.filter(obj => obj.folderid === record.folderid);
              const indexRow = this.selectedRows.findIndex(data => data.folderid === parent[0].folderid);
              const indexKey = this.selectedRowKeys.findIndex(data => data === parent[0].id);
              if (indexRow > -1) {
                this.selectedRows.splice(indexRow, 1);
              }
              if (indexKey > -1) {
                this.selectedRowKeys.splice(indexKey, 1);
              }
            }
          }
        }
      },
      assgin() {
        if (this.selectedRows.length === 0) {
          this.$message.warning('请至少选择一条数据')
          return
        }
        var needAssignTasks = this.selectedRows.filter(item => item.isParent !== true && !item.planstarttime)
        if (needAssignTasks.length > 0 && (this.laboratoryId === 'HZ_YJ_DL_CS' || this.laboratoryId === 'JM_YJ_DL_CS' || this.laboratoryId === 'HZ_YJ_DL_AQ_RL')) {
          this.$message.warning('没有分配计划开始日期，无法分配检测人！')
          return;
        }
        if (needAssignTasks.length > 0 && this.laboratoryId === 'HZ_YJ_DL_AQ') {
          this.$message.warning('没有分配计划时间，无法分配检测人！')
          return;
        }
        this.personVisible = true
      },
      assginPlanStartDate() {
        if (this.selectedRows.length === 0) {
          this.$message.warning('请至少选择一条数据')
          return
        }
        let orderTasks = this.selectedRows.filter(item => item.id.length > 12) //过滤掉测试项目的父项
        // console.log('orderTasks',orderTasks)
        getMidCheckInfoByOrdTaskIds(orderTasks).then((res) => {
          if (res.success === true) {
            // console.log('res',res)
            this.middleCheckInfoList = res.data
          } else {
            this.$message.warning('获取中检信息失败：' + res.message)
          }
        })
        this.planStartDateVisible = true
      },
      assginPlanDate() {
        if (this.selectedRows.length === 0) {
          this.$message.warning('请至少选择一条数据')
          return
        }
        this.planDateVisible = true
      },
      handleOk() {
      },
      openData(folderId) {
        this.$refs.testData.query(folderId)
      },
      personLoadData() {
        let orgId = "HZ_YJ_DL_JM"
        if (this.laboratoryId === 'HZ_YJ_DL_AQ_RL') {
          orgId = 'HZ_YJ_DL_AQ'
        } else if (this.laboratoryId) {
          orgId = this.laboratoryId
        }
        console.log('this.laboratoryId',this.laboratoryId)
        return getTestPerson({ status: "deprecated", orgId: orgId, roleId: "699586598579968" }).then((res) => {
          this.personResultData = res.data
          this.allPersonResultData = JSON.parse(JSON.stringify(res.data))
        })
      },
      searchTesters() {
        console.log('searchTesterParam',this.searchTesterParam)
        if (this.searchTesterParam.ID) {
          this.personResultData = this.allPersonResultData.filter(item => item.ID.includes(this.searchTesterParam.ID))
        }
        if (this.searchTesterParam.USERNAME) {
          this.personResultData = this.allPersonResultData.filter(item => item.USERNAME.includes(this.searchTesterParam.USERNAME))
        }
        if (!this.searchTesterParam.ID && !this.searchTesterParam.USERNAME) {
          this.personResultData = this.allPersonResultData
        }
      }
    }
  }
</script>
<style lang="less" scoped=''>
  :root{
    --height:600px;
  }
  /deep/ .ant-table-thead > tr > th {
    text-align: center;
    padding: 5px!important;
    font-size: 14px!important;
  }

  /deep/ .ant-table-tbody > tr > td {
    padding: 0px!important;
    height: 32px!important;
    font-size: 12px!important;
  }

  /deep/ .ant-calendar-picker-icon {
    display: none;
  }

  /deep/ .ant-calendar-picker-input.ant-input {
    color: black;
    font-size: 12px;
    text-align: center;
    padding: 0;
  }

  .red {
    background-color: #ed0000;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .yellow {
    background-color: #ffc000;
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .grey {
    background-color: rgba(223, 223, 223, 0.25);
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .ant-modal-body {
    padding: 0;
  }

  /deep/ .ant-col {
    padding: 0 !important;
    height: 40px !important;
  }

  /deep/.ant-btn > i, /deep/.ant-btn > span {
    display: flex;
    justify-content: center;
  }

  /deep/.ant-tabs-bar{
    margin: 0;
  }

  /deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
    color: #fff;
    background: #1890FF;
  }

  .green{
    background-color: #58a55c;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

  }

  /deep/#table1>div>div>div>div>div>div>table>thead{
    height: 64px;
  }

  /deep/#table1>.ant-table-wrapper>div>div>ul{
    display: none;
  }



  /deep/ .ant-modal-header{
    padding: 16px 24px  0;
    border: none;
  }
  /deep/ .ant-modal-body{
    padding: 16px;
  }

/deep/ .ant-modal-footer{
  padding: 0 24px 16px ;
}

/deep/.ant-btn {
  height: 28px;
}
/deep/tr td {
  padding: 4px;
  color: #333;
  font-size: 12px;
  font-weight: 400;
}

/deep/ .table-scroll .ant-table-body{
  width: calc(100vw - 40px - 30px);
  height: var(--height) !important;
  overflow: auto;
}

/deep/.table-scroll .ant-table-thead {
	position: sticky;
	top: 0;
	z-index: 1;
}

/deep/ .ant-table-placeholder {
	border: none !important;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}
</style>