<template>
  <div class="page-main">
    <pbiTabs :tabsList="firstTag" :activeKey="firstTagActiveKey" tabsFontSize="14" @clickTab="firstTagChange"></pbiTabs>
    <div class="second">
      <div v-if="firstTagActiveKey === 'filter'">
        <a-tabs v-model="secondTagActiveKey" @change="secondTagChange">
          <a-tab-pane v-for="item in filterTag" :key="item.value" :tab="item.label">
          </a-tab-pane>
        </a-tabs>
      </div>
      <div  v-if="firstTagActiveKey === 'create'">
        <a-tabs v-model="secondTagActiveKey" @change="secondTagChange">
          <a-tab-pane key="createList" tab="数据建模记录查询"></a-tab-pane>
          <a-tab-pane key="createV" tab="V圆柱测试模型搭建"></a-tab-pane>
          <a-tab-pane key="createDl" tab="动力测试模型搭建"></a-tab-pane>
          <a-tab-pane key="createCustom">
            <template #tab>
              <a-dropdown>
                <span class="ant-dropdown-link">
                  定制测试模型搭建<a-icon style="margin-left: 4px;" type="down" />
                </span>
                <a-menu slot="overlay">
                  <a-menu-item @click="event => createTagChange(event,'createCustom','cycle')">
                    G26循环报告建模
                  </a-menu-item>
                  <a-menu-item @click="event => createTagChange(event,'createCustom','calendar')">
                    G26日历报告建模
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </template>
          </a-tab-pane>
          <a-tab-pane key="createMerge" tab="方案合并模型搭建"></a-tab-pane>
        </a-tabs>
      </div>
      <div>
        <exportTask v-if="filter && filterList" :width="parseInt(85)" padding="0"></exportTask>
        <inline :width="parseInt(0)" v-if="filter && filterInline"></inline>
        <outline :width="parseInt(100)" v-if="filter && filterOutline"></outline>
        <all v-if="filter && filterAll" :width="parseInt(90)"></all>
        <xy  v-if="filter && filterXy"></xy>
        
        <reportList v-if="create && createList" :width="parseInt(80)" padding="0"></reportList>
        <vCreate :isInside="Boolean(true)" v-if="create && createV"></vCreate>
        <dongLiReportBuild v-if="create && createDl" :width="parseInt(80)" padding="0" :type="dongLiType"></dongLiReportBuild>
        <optionMergeBuild v-if="create && createMerge" :width="parseInt(80)" padding="8px"></optionMergeBuild>
        <g26Cycle :isInside="Boolean(true)" v-if="create && createCustom && g26 == 'cycle'"></g26Cycle>
        <g26CalendarAgingReport v-if="create && createCustom && g26 == 'calendar'" :width="parseInt(80)" padding="0 0 0 10px"></g26CalendarAgingReport>
      </div>


    </div>

  </div>
</template>
<script>

import exportTask from "../lims/folder/exportTask.vue";
import inline from "../lims/data/index.vue";
import outline from "../lims/data/indexJM.vue";
import xy from "../lims/data/custom_index.vue";
import all from "../lims/folder/index.vue";
import reportList from "../vTestReport/reportList.vue";
import vCreate from "../vTestReport/index.vue";
import dongLiReportBuild from "../vTestReport/dongLiReportBuild.vue";
import g26Cycle from "../vTestReport/g26Index.vue";
import g26CalendarAgingReport from "../vTestReport/g26CalendarAgingReport.vue";
import optionMergeBuild from "../vTestReport/optionMergeBuild.vue";

import pbiTabs from "@/components/pageTool/components/pbiTabsNew";


export default {
	components: {
    exportTask,
    inline,
    outline,
    all,
    reportList,
    vCreate,
    dongLiReportBuild,
    g26Cycle,
    g26CalendarAgingReport,
    optionMergeBuild,
    xy,
    pbiTabs
  },
	data: function() {
		return {
      filter: true,
      create: false,
      filterList:true,
      filterInline:false,
      filterOutline:false,
      filterAll:false,
      filterXy:false,

      createList:true,
      createV:false,
      createDl:false,
      createCustom:false,
      createMerge:false,

      g26:'cycle',

      filterTab:'list',
      createTab:'list',
      g26Select:null,
		  type:'',
      dongLiType:null,
      firstTag:[{value:'filter',label:'数据提取'},{value:'create',label:'数据建模'}],
      firstTagActiveKey:'filter',
      secondTagActiveKey:'filterList',
      filterTag:[
        {value:'filterList',label:'数据提取记录查询'},
        {value:'filterInline',label:'在线数据提取'},
        {value:'filterOutline',label:'离线数据提取'},
        {value:'filterAll',label:'数据全量提取'},
        {value:'filterXy',label:'在线图表生成'}
      ]
    }
	},
	watch: {},
	created() {},
	computed: {},
	mounted() {
    this.type = this.$route.query.type
    const params = new URLSearchParams(window.location.search);
    if(params.size !== 0){
      const column = params.get('column') // filter  数据提取   create 数据建模
      this.filter = column === 'filter' ? true : false
      this.create = column === 'create' ? true : false
      this.firstTagActiveKey = this.filter ? 'filter'  : 'create'
      this.secondTagActiveKey = this.filter ? 'filterList'  : 'createList'
      const key = params.get('key')
      this.dongLiType = params.get('dongLiType')
      if("g26Cycle" == this.dongLiType){
        this.g26 = "cycle"
      }
      if("g26Calendar" == this.dongLiType){
        this.g26 = "calendar"
      }
      if(key){
        this.secondTagChange(key)
      }
    }
	},

	methods: {
    firstTagChange(value){
      if(this.firstTagActiveKey === value) return
      this.firstTagActiveKey = value
      this.secondTagActiveKey = value === 'filter' ? 'filterList' : 'createList'

      this.filter = value == 'filter' ? true : false
      this.create = value == 'filter' ? false : true
    },
    secondTagChange(value){
      const keys1 = ['filterList','filterInline','filterOutline','filterAll','filterXy']
      const keys2 = ['createList','createV','createDl','createCustom','createMerge']
      const keys = this.firstTagActiveKey === 'filter' ? keys1 : keys2
      keys.forEach(item => {
        this[item] = false
      })

      this[value] = true
      this.secondTagActiveKey = value
    },
    createTagChange(event,column,g26Type){
      if(g26Type){
        this.g26 = g26Type
      }
      this[column] = true
      this.secondTagActiveKey = 'createCustom'
      if(!event){
        return
      }
      let keys = ['createList','createV','createDl','createCustom','createMerge']

      for (let i = 0; i < keys.length; i++) {
        if(column != keys[i]){
          this[keys[i]] = false
        }
      }
    }
  }
}
</script>
<style lang="less" scoped>
  .page-main{
    padding: 8px 12px;
  }
  .second{
    padding-top: 8px;
    background: #fff;
    overflow: auto;
    border-radius: 0 10px 10px;
  }
  /deep/.ant-tabs{
    font-size: 12px;
    padding: 0 12px;
    color: #333;
  }
  /deep/.ant-tabs-bar{
    margin: 0;
  }
  /deep/.ant-tabs-nav .ant-tabs-tab{
    margin: 0 20px 0 0;
    padding: 6px 10px 12px;
    font-size: 12px;
  }
  /deep/.page-main .ant-dropdown-menu-item{
    font-size: 12px;
  }

  .wrapper{
    background-color: transparent;
  }
  .all-wrapper,.all-div{
    background-color: transparent;
  }

  .page-container{
    padding-bottom: 0px;
  }

/deep/ #outTable .ant-table-thead > tr > th {
  padding: 2px 0 !important;
  font-size: 12px !important;
}

</style>
