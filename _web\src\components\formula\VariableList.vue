<template>
  <div v-if="variables.length > 0" class="variable-container">
    <div class="variable-wrapper">
      <h5>变量列表</h5>
      <div class="variable-list">
        <div class="variable-content">
          <div
            v-for="variable in variables"
            :key="variable.name"
            class="variable-item"
          >
            <a-tooltip
              :title="variable.describe || '暂无说明'"
              placement="top"
            >
              <div class="variable-item-content">
                <a-tag color="green" class="variable-tag">
                  <span v-html="renderLatex(variable.name)"></span>
                </a-tag>
                <span v-if="variable.describe" class="variable-description">
                  {{ variable.describe }}
                </span>
              </div>
            </a-tooltip>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { renderLatex, renderMathJax } from '@/utils/mathUtils';

export default {
  name: 'VariableList',
  props: {
    variables: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    renderLatex
  },
  mounted() {
    // 组件挂载后渲染 LaTeX
    this.$nextTick(() => {
      renderMathJax(true);
    });
  },
  watch: {
    variables: {
      handler() {
        // 变量列表变化时重新渲染 LaTeX
        this.$nextTick(() => {
          renderMathJax(true);
        });
      },
      deep: true
    }
  }
};
</script>

<style scoped>
.variable-container {
  margin-bottom: 16px;
}

.variable-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.variable-item {
  width: 100%;
}

.variable-item-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
}

.variable-tag {
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  flex-shrink: 0;
}

.variable-description {
  color: #666;
  font-size: 12px;
  line-height: 1.4;
}

/* 高对比度模式支持 */
@media (forced-colors: active) {
  .variable-wrapper,
  .variable-list {
    forced-color-adjust: auto;
    border: 1px solid CanvasText;
  }

  h5 {
    color: CanvasText;
    border-bottom: 1px solid CanvasText;
  }
}
</style>