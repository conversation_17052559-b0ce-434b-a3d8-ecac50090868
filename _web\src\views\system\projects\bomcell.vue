<template>
    <a-modal
        title="电芯BOM"
        :width="800"
        :visible="visible"
        :confirmLoading="confirmLoading"
        @cancel="handleCancel"
    >
        <template slot="footer">
            <a-button key="back" @click="handleCancel">
                  关闭
            </a-button>
        </template>
        <a-spin :spinning="confirmLoading">
            <s-table
                ref="table"
                :loading="confirmLoading"
                :columns="columns"
                :data="loadData"
                :rowKey="(record) => record.id"
                >
                <span slot="bomStatus" slot-scope="text, record">
                    {{mapStatus[record.bomStatus]}}
                </span>
                <template slot="lines" slot-scope="text, record">
					<label  v-for="(item, i) in record.lines" :key="i">
						{{dataLines[item]}}；
					</label>
				</template>
            </s-table>   
        </a-spin>
    </a-modal>
</template>

<script>
    import {
        STable
    } from '@/components'
    import {
        getwerklines,
        getBomEndBomPage,
    } from "@/api/modular/system/bomManage"
 
    export default {
        components: {
            STable
        },
        props: {
            dataLines: {
                type: Object,
                default: {}
            },
            bomIssueId:{
                type:Number,
                default:0
            }
        },
        data() {
            return {
                queryParam:{
                },
                confirmLoading:false,
                visible:false,
                mapStatus: ['编辑中', '审核中', '已审核', '失败中', '被驳回', '新增工厂申请'],
                loading:false,
                columns: [{
                        title: '序号',
                        dataIndex: 'index',
                        key: 'index',
                        align: 'center',
                        width: 50,
                        customRender: (text, record, index) => `${index+1}`,
                    },
                    {
                        title: '电芯BOM编号',
                        dataIndex: 'bomNo',
                        width: 180,
                    },
                    {
                        title: '电芯BOM代码',
                        dataIndex: 'bomData',
                        width: 120,
                        customRender: (text, record, index) => {
                            let code = ''
                            if (text.length > 2) {
                                code = JSON.parse(text)[0].sapNumber
                            }
                            const obj = {
                                children: code,
                                attrs: {},
                            }
                            return obj
                        },
                    },
                    {
                        title: '状态',
                        dataIndex: 'bomStatus',
                        width: 100,
                        scopedSlots: {
                            customRender: 'bomStatus'
                        }
                    },
                    {
                        title: '产线',
                        dataIndex: 'lines',
                        width: 180,
                        scopedSlots: {
                            customRender: 'lines'
                        }
                    },
                ],
                loadData: parameter => {
					return getBomEndBomPage(Object.assign(parameter, this.queryParam)).then((res) => {
						return res.data
					})
				},
            }
        },
        created() {
        },
        methods: {
            handleCancel(){
                this.queryParam = {}
                this.visible = false
            },
            view(record){
                this.queryParam.bomIssueId = this.bomIssueId
                this.queryParam.id = record.id
                this.queryParam.bomType = record.bomType
                setTimeout(() => {
                    this.$refs.table.refresh()
                }, 100);
                this.visible = true
            },
        },
        watch: {}
    }
</script>

<style>

</style>