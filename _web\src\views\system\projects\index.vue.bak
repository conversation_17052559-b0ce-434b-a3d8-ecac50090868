<template>
    <div>
        <div :class="topshow ? 'topSelect show': 'topSelect'">
            <div class="selectItem">
                <span class="label">分类：</span>
                <a :class="queryParam.cates.length < 1 ? 'active' : '' " @click="toclear('cates')">全部</a>
                <a v-for="(item,i) in cate" :key="i" :class="queryParam.cates.indexOf(item.title) > -1 ? 'active' : '' " @click="tofilter(item.title,'cates')">{{item.title}}</a>
            </div>
            <div class="selectItem">
                <span class="label">等级：</span>
                <a :class="queryParam.levels.length < 1 ? 'active' : '' " @click="toclear('levels')">全部</a>
                <a  v-for="(item,i) in getDict('product_level_status')" :key="i" :class="queryParam.levels.indexOf(item.name) > -1 ? 'active' : '' " @click="tofilter(item.name,'levels')">{{item.name}}</a>
            </div>
            <div class="selectItem">
                <span class="label">客户：</span>
                <a :class="queryParam.customers.length < 1 ? 'active' : '' "  @click="toclear('customers')">全部</a>
                <a v-for="(item,i) in customerFilter" :key="i" :class="queryParam.customers.indexOf(item.title) > -1 ? 'active' : '' "  @click="tofilter(item.title,'customers')">{{item.title}}</a>
            </div>
            <div class="selectItem">
                <span class="label">开发阶段：</span>
                <a :class="queryParam.stages.length < 1 ? 'active' : '' " @click="toclear('stages')">全部</a>
                <a v-for="(item,i) in getDict('product_stage_status')" :key="i" :class="queryParam.stages.indexOf(item.code) > -1 ? 'active' : '' " @click="tofilter(item.code,'stages')">{{item.name}}</a>
            </div>
            <div class="toggole" @click="toToggle(false)"><a-icon type="up" /></div>
        </div>
        <x-card>
            <div slot="content" class="table-page-search-wrapper">
                <a-form layout="inline">
                    <a-row :gutter="48">
                        <!-- <a-col :md="8" :sm="24">
                            <a-form-item label="分类">
                                <a-tree-select @change="this.change" v-model="queryParam.cateId" :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" :tree-data="cate" placeholder="请选择分类" tree-default-expand-all>
                                </a-tree-select>
                            </a-form-item>
                        </a-col> -->
                        <a-col :md="8" :sm="24">
                            <a-form-item label="产品名称">
                                <a-input v-model="queryParam.productProjectName" @keyup.enter.native="()=>callFilter()" />
                            </a-form-item>
                        </a-col>
                        <a-col :md="8" :sm="24">
                            <span class="table-page-search-submitButtons">
                            <!-- <a-button type="primary" @click="callProjects()" >查询</a-button> -->
                                <a-button style="margin-left: 8px" @click="() => reset()">重置</a-button>
                                  <a-button type="primary" @click="$refs.addForm.add()" style="margin-left: 8px" >新建</a-button>
                            </span>
                        </a-col>
                    </a-row>
                </a-form>
                <div class="toggole" @click="toToggle(true)"><a-icon type="down" /></div>
            </div>
            
        </x-card>
            <a-table :customRow="customRow" @expand="onExpand" :expandedRowKeys.sync="expandedRowKeys" :scroll="{ y:windowHeight }" style="background: #fff;padding:12px;" ref="table" size="middle" :rowKey="(record) => record.productCateParent+record.productCate+record.issueKey+record.cateId" :pagination="false" :columns="columns" :dataSource="loadData" :loading="loading" showPagination="false" bordered>
                    <!-- <span slot="productProjectName" slot-scope="text, record">
                        <a v-if="hasPerm('project:jira')" @click="handleToJira(record)">{{text}}</a>
                        <span v-else>{{text}}</span>
                    </span> /project_overview-->
                    <span slot="productProjectName" slot-scope="text">
                        <!-- <a  @click="toOverview(record)">{{text}}</a> -->
                        {{text}}
                    </span>
                    <span slot="fixedState" slot-scope="text, record">
                        <span v-if="record.issueId">
                            {{ 'fix_status' | dictType(text) }}
                        </span>
                        <span v-else></span>
                    </span>
                    <span slot="mstatus" slot-scope="text, record">
                        <span v-if="record.issueId">{{ 'product_stage_status' | dictType(text) }}</span>
                        <span v-else></span>
                    </span>
                    <span slot="state" slot-scope="text, record">
                        <span v-if="record.issueId">
                            {{ 'product_state_status' | dictType(text) }}
                        </span>
                        <span v-else></span>
                    </span>
                    <div slot="productOrProject" slot-scope="text, record">
                        <span v-if="record.productOrProject == 1">产品</span>
                        <span v-else>项目</span>
                    </div>
                    <span slot="action" slot-scope="text, record">
                        <span v-if="record.issueId">
                            <!-- <a v-if="hasPerm('project:view')" @click="toProjectDetail(record)">查看</a> -->
                            <!-- <a-divider v-if="record.productOrProject == 1 && hasPerm('project:bom')" type="vertical" /> -->
                            <!-- <a v-if="record.productOrProject == 1 && hasPerm('project:bom')" @click="toProjectDetailBOM(record)">BOM管理</a> -->
                            <!-- <a-divider v-if="record.productOrProject == 1 && hasPerm('project:materials')" type="vertical" />
                            <a v-if="record.productOrProject == 1 && hasPerm('project:materials')" @click="toProjectMaterial(record)">材料管理</a> -->
                            <!-- <a-divider v-if="record.productOrProject == 1 && hasPerm('project:sample')" type="vertical" />
                            <a v-if="record.productOrProject == 1 && hasPerm('project:sample')" @click="toSample(record)">样品管理</a> -->
                        </span>
                        <span v-else></span>
                    </span>
            </a-table>
            <add-form ref="addForm" @ok="callProjects"/>
    </div>
</template>

<script>

    import { ALL_APPS_MENU } from '@/store/mutation-types'
    import addForm from './create/addForm'
    import _ from 'lodash'
    import {
        DICT_TYPE_TREE_DATA
    } from '@/store/mutation-types'
    import Vue from 'vue'
    import {
        getProjects,
        getCatesTree
    } from "@/api/modular/system/report"
    import {
        mapActions,
        mapGetters
    } from 'vuex'
    import {
        XCard
    } from '@/components'
    export default {
        components: {
            XCard,
            addForm
        },
        data() {
            return {
                topshow:false,
                customerFilter:[],
                windowHeight: document.documentElement.clientHeight - 205,
                loading: true,
                columns: [],
                loadData: [],
                totalData:[],
                queryParam: {
                    cates:[],
                    levels:[],
                    customers:[],
                    stages:[],
                    productProjectName: '',
                },
                cate: [],
                expandedRowKeys:[],
            }
        },
        methods: {
            customRow(row, index) {
                return {
                    on: {
                        click: () => {
                            this.$router.push({
                                path: "/project_overview",
                                query: {
                                    issueId: row.issueId
                                },
                            });
                        }
                    }
                }
            },
            toToggle(flag){
                this.topshow = flag
            },
            reset(){
                this.queryParam = {
                    cates:[],
                    levels:[],
                    customers:[],
                    stages:[],
                    productProjectName: null,
                }
                this.callFilter()
            },
            toclear(tag){
                if (this.queryParam[tag].length > 0) {
                    this.queryParam[tag] = []
                    this.callFilter()
                }
            },
            tofilter(val,tag){
                if(this.queryParam[tag].indexOf(val) > -1){
                    this.queryParam[tag].splice(this.queryParam[tag].indexOf(val),1)
                }else{
                    this.queryParam[tag].push(val)
                }
                this.callFilter()
            },
            callFilter(){
                let filterData = JSON.parse(JSON.stringify(this.totalData))
                if(this.queryParam['cates'].length > 0){
                    filterData = filterData.filter(item => this.queryParam['cates'].indexOf(item.productCateParent) > -1)
                }
                if (this.queryParam['customers'].length > 0) {
                    filterData = filterData.filter(item => this.queryParam['customers'].indexOf(item.customer) > -1)
                }
                if (this.queryParam['levels'].length > 0) {
                    filterData = filterData.filter(item => this.queryParam['levels'].indexOf(item.productLevel) > -1)
                }
                if (this.queryParam['stages'].length > 0) {
                    filterData = filterData.filter(item => this.queryParam['stages'].indexOf(item.mstatus+'') > -1)
                }
                if (this.queryParam.productProjectName != null && this.queryParam.productProjectName != '') {
                    filterData = filterData.filter(item => item.productProjectName.toLowerCase().indexOf(this.queryParam.productProjectName.toLowerCase()) > -1)
                }
                this.loadData = filterData
            },
            getExpandedRowKeys(list){
                list.forEach((item) => {
                    if (item.children && item.children.length) {
                        // 将所有children的父节点取出
                        this.expandedRowKeys.push(item.productCateParent+item.productCate+item.issueKey+item.cateId);
                        this.getExpandedRowKeys(item.children);
                    }
                });
            },
            onExpand(expanded, record){
                if (expanded) {
                    this.expandedRowKeys.push(record.productCateParent+record.productCate+record.issueKey+record.cateId);
                } else {
                    this.expandedRowKeys.splice(this.expandedRowKeys.indexOf(record.productCateParent+record.productCate+record.issueKey+record.cateId), 1);
                }
            },
            /* change(value, label, extra){
                this.callProjects()
            }, */
            handleToJira(row) {
                let _key = row["issueKey"];
                if (!_key) {
                return;
                }
                let $url = `http://jira.evebattery.com/browse/${_key}?auth=` + Vue.ls.get("jtoken");
                window.open($url, "_blank");
            },
            ...mapActions(['MenuChange']),
            switchApp() {
                const apps = Vue.ls.get(ALL_APPS_MENU)
                const _newApps = []
                for (const item of apps) {
                    if (item.code == appCode) {
                        item.active = true
                    }else{
                        item.active = false
                    }
                    _newApps.push(item)
                }
                Vue.ls.set(ALL_APPS_MENU, _newApps)
            },
            toOverview(record){
                this.$router.push({
                    path: "/project_overview",
                    query: {
                        issueId: record.issueId
                    },
                });
            },
            toProjectDetail(record) {
                //this.switchApp()
                this.$router.push({
                    path: "/project_detail",
                    query: {
                        issueId: record.issueId,
                        activeKey: '1'
                    },
                });
            },
            toProjectDetailBOM(record) {
                //this.switchApp()
                this.$router.push({
                    path: "/project_detail",
                    query: {
                        issueId: record.issueId,
                        activeKey: '5'
                    },
                });
            },
            toProjectMaterial(record) {
                //this.switchApp()
                this.$router.push({
                    path: "/project_material",
                    query: {
                        issueId: record.issueId,
                        name:record.productProjectName
                    },
                });
            },
            toSample(record){
                //this.switchApp()
                this.$router.push({
                    path: "/sample_manage",
                    query: {
                        issueId: record.issueId,
                        name:record.productProjectName
                    },
                });
            },
            callGetTree(){
                this.loading = true
                getCatesTree().then((res)=>{
                    if (res.result) {
                        this.cate = res.data
                    } else {
                        this.$message.error('错误提示：' + res.message, 1)
                    }
                    this.loading = false
                }).catch((err) => {
                    this.loading = false
                    this.$message.error('错误提示：' + err.message, 1)
                });
            },

            getDict(code) {
                const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
                return dictTypeTree.filter(item => item.code == code)[0].children
            },
            
            callProjects() {
                this.loading = true
                this.expandedRowKeys = []
                getProjects(this.queryParam).then((res) => {
                    if (res.result) {

                        /* res.data.columns.push({
                            title: '操作',
                            width: 60,
                            dataIndex: 'action',
                            scopedSlots: {
                                customRender: 'action'
                            }
                        }) */

                        /* let spanArr = ['productCateParent','productCate']
                        spanArr.forEach((item) => {
                            for (let i = 0, j = res.data.rows.length; i < j; i++) {
                                let rowSpan = 0;
                                let n = i;
                                while (
                                res.data.rows[n + 1] &&
                                res.data.rows[n + 1][item] == res.data.rows[n][item]
                                ) {
                                rowSpan++;
                                n++;
                                res.data.rows[n].rowSpan = 0;
                                }
                                if (rowSpan) res.data.rows[i][item + "_rowSpan"] = rowSpan + 1;
                                if (!rowSpan) res.data.rows[i][item + "_rowSpan"] = 1;
                                i += rowSpan;
                            }
                        }); */

                        let statusarr = [
                            'productProjectName',
                            "fixedState",
                            "mstatus",
                            "state",
                            'productOrProject'
                        ];
                        let widtharr = [
                            'produceFeedback',
                            'sellFeedback',
                            'supplyFeedback'
                        ]

                        for (const item of res.data.columns) {
                            /* if(spanArr.indexOf(item.key) > -1){
                                item.customRender = (value, row, index) =>{
                                    const obj = {
                                        children: value,
                                        attrs: {},
                                    };
                                    if (row[item.key+'_rowSpan'] > 0) {
                                        obj.attrs.rowSpan = row[item.key+'_rowSpan']
                                        obj.attrs.colSpan = 1
                                    }else{
                                        obj.attrs.rowSpan = row[item.key+'_rowSpan']
                                        obj.attrs.colSpan = 0
                                    }
                                    
                                    return obj;
                                }
                            } */

                            let index = statusarr.indexOf(item.key)
                            if (index > -1) {
                                item.scopedSlots = {
                                    customRender: statusarr[index]
                                }
                            }
                            if (item.width) {
                                item.width = parseInt(item.width)
                            }
                            if (widtharr.indexOf(item.key) > -1) {
                                item.width = 0
                            }
                        }
                        this.columns = res.data.columns
                        
                        this.totalData = JSON.parse(JSON.stringify(res.data.rows))

                        this.loadData = res.data.rows

                        this.customerFilter = _.chain(res.data.rows)
                            .filter(item => item.customer != '')
                            .map(item => item.customer)
                            .uniq()
                            .map(item => ({
                                title: item,
                                value: item,
                            }))
                            .value()

                        if (this.queryParam.cateId != null || (this.queryParam.productProjectName != null && this.queryParam.productProjectName != '')) {
                            this.getExpandedRowKeys(res.data.rows)
                        }else{
                            this.expandedRowKeys = []
                        }
                    } else {
                        this.$message.error('错误提示：' + res.message, 1)
                    }
                    this.loading = false
                }).catch((err) => {
                    this.loading = false
                    this.$message.error('错误提示：' + err.message, 1)
                });
            },
        },
        computed: {
            ...mapGetters(['userInfo'])
        },
        created() {
            this.callProjects()
            this.callGetTree()
        }
    }
</script>

<style lang="less" scoped=''>
/deep/.ant-table-tbody > tr:hover:not(.ant-table-expanded-row) > td,.ant-table-row-hover,.ant-table-row-hover>td{
    cursor: pointer;
}
/deep/.table-page-search-wrapper .ant-form-inline .ant-form-item{
    margin-bottom: 0;
}
/deep/.table-page-search-wrapper .table-page-search-submitButtons{
    margin-bottom: 0;
}
.topSelect{
    transition: all .4s ease;
    position: fixed;
    top: 40px;
    width: 100%;
    z-index: 0;
    background: #fff;
    box-shadow: 0px 2px 8px #8888884f;
    padding: 10px 24px;
    font-size: 12px;
    opacity: 0;
}
.topSelect.show{
    opacity: 1;
    z-index: 8;
}
.selectItem{
    margin: 12px 0;
}
.selectItem .label{
    display: inline-block;
    width: 80px;
    text-align: right;
    padding: 1px 7px;
}
.selectItem a{
    border-radius: 6px;
    display: inline-block;
    padding: 1px 7px;
    margin: 0 4px;
    color: #000;
}
.selectItem a.active,.selectItem a:hover{
    color: rgb(0, 77, 255);
    background: rgb(225, 236, 249);
}
.toggole{
    text-align: center;
    cursor: pointer;
}
</style>