/*
@license

dhtmlxGantt v.6.3.3 Standard

This version of dhtmlxGantt is distributed under GPL 2.0 license and can be legally used in GPL projects.

To use dhtmlxGantt in non-GPL projects (and get Pro version of the product), please obtain Commercial/Enterprise or Ultimate license on our site https://dhtmlx.com/docs/products/dhtmlxGantt/#licensing or contact <NAME_EMAIL>

(c) XB Software Ltd.

*/
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("locale/locale_kr",[],t):"object"==typeof exports?exports["locale/locale_kr"]=t():e["locale/locale_kr"]=t()}(window,function(){return function(e){var t={};function n(o){if(t[o])return t[o].exports;var r=t[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(o,r,function(t){return e[t]}.bind(null,r));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="/codebase/",n(n.s=193)}({193:function(e,t){gantt.locale={date:{month_full:["1월","2월","3월","4월","5월","6월","7월","8월","9월","10월","11월","12월"],month_short:["1월","2월","3월","4월","5월","6월","7월","8월","9월","10월","11월","12월"],day_full:["일요일","월요일","화요일","수요일","목요일","금요일","토요일"],day_short:["일","월","화","수","목","금","토"]},labels:{new_task:"이름없는 작업",icon_save:"저장",icon_cancel:"취소",icon_details:"세부 사항",icon_edit:"수정",icon_delete:"삭제",confirm_closing:"",confirm_deleting:"작업을 삭제하시겠습니까?",section_description:"설명",section_time:"기간",section_type:"Type",column_wbs:"WBS",column_text:"작업명",column_start_date:"시작일",column_duration:"기간",column_add:"",link:"전제",confirm_link_deleting:"삭제 하시겠습니까?",link_start:" (start)",link_end:" (end)",type_task:"작업",type_project:"프로젝트",type_milestone:"마일스톤",minutes:"분",hours:"시간",days:"일",weeks:"주",months:"달",years:"년",message_ok:"OK",message_cancel:"취소",section_constraint:"Constraint",constraint_type:"Constraint type",constraint_date:"Constraint date",asap:"As Soon As Possible",alap:"As Late As Possible",snet:"Start No Earlier Than",snlt:"Start No Later Than",fnet:"Finish No Earlier Than",fnlt:"Finish No Later Than",mso:"Must Start On",mfo:"Must Finish On",resources_filter_placeholder:"type to filter",resources_filter_label:"hide empty"}}}})});
//# sourceMappingURL=locale_kr.js.map