<template>


  <a-modal title="测试数据" width="80%" :height="600" :visible="dataVisible"  @cancel="closeData" >
    <template slot="footer">
      <div>
        <a-button key="back" @click="closeData">关闭</a-button>
      </div>
    </template>


    <dataDetailin ref="dataDetailin" ></dataDetailin>

  </a-modal>

</template>

<script>

  import dataDetailin from "@/views/system/earlyWarning/dataDetailin.vue";

  export default {
    components: {
      dataDetailin
    },
    data() {
      return {

        record: {},
        confirmVisible: false,
        dataVisible: false,
        loading: false,

      }
    },
    computed: {},
    methods: {
      openData(record) {
        this.record = record
        this.$refs.dataDetailin.openData(record)


      },
      closeData() {
        this.dataVisible = false
      }

    }
    ,
    mounted() {

    }

  }
</script>

<style lang='less' scoped="">

  /deep/.ant-modal-body {
    padding: 10px;
  }

</style>