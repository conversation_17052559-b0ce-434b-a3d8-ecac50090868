<template>
  <div class="coefficient-optimization-panel">
    <file-upload-section
      :has-formula="hasFormula"
      :uploading="uploading"
      :file-list="fileList"
      :data-loaded="dataLoaded"
      :temperatures="temperatures"
      :socs="socs"
      :data-points="dataPoints"
      @file-uploaded="handleFileUploaded"
      @file-removed="handleFileRemoved"
    />

    <a-row :gutter="16" class="panel-row">
      <a-col :md="12" class="panel-col">
        <coefficient-range-settings
          :coefficients="coefficients"
          @coefficients-updated="updateCoefficients"
          @group-descriptions-updated="updateGroupDescriptions"
        />
      </a-col>
      <a-col :md="12" class="panel-col">
        <optimization-settings
          :fitting-data="fittingData"
          :weight-config="weightConfig"
          :algorithm-params="algorithmParams"
          :optimizing="optimizing"
          @weight-config-updated="updateWeightConfig"
          @algorithm-params-updated="updateAlgorithmParams"
          @start-optimization="startParameterOptimization"
        />
      </a-col>
    </a-row>

    <a-row :gutter="16" class="panel-row">
      <a-col :md="12" class="panel-col">
        <fitting-settings
          :algorithm-params="algorithmParams"
          :optimizing="optimizing"
          :data-loaded="dataLoaded"
          @algorithm-params-updated="updateAlgorithmParams"
          @start-optimization="startParameterOptimization"
        />
      </a-col>
      <a-col :md="12" class="panel-col">
        <fitting-results
          :fitting-results="fittingResults"
          :data-points="dataPoints"
          :temperatures="temperatures"
          :socs="socs"
          @view-detailed-results="viewDetailedResults"
        />
      </a-col>
    </a-row>

    <div class="formula-info-settings">
      <h5>公式信息</h5>
      <div>
        <a-form-item label="公式ID（必填）">
          <a-input v-model="localFormulaOptId" placeholder="公式ID" @change="emitFormulaIdChange" />
        </a-form-item>
        <a-form-item label="公式说明（选填）">
          <a-textarea
            v-model="localFormulaOptDesc"
            placeholder="请输入公式说明"
            :autoSize="{ minRows: 2, maxRows: 4 }"
            @change="emitFormulaDescChange"
          />
        </a-form-item>
        <div style="text-align: center; margin-top: 12px;">
          <a-button type="primary" @click="saveFormulaOptModel" style="width: 100%">
            保存公式
          </a-button>
        </div>
      </div>
    </div>

    <fitting-result-modal />
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import { api } from '@/api';
import { mapGetters } from 'vuex';
import FileUploadSection from '@/components/common/FileUploadSection.vue';
import OptimizationSettings from '@/components/fitting/OptimizationSettings.vue';
import FittingResultModal from '@/components/fitting/FittingResultModal.vue';
import CoefficientRangeSettings from '@/components/formula/CoefficientRangeSettings.vue';
import FittingSettings from '@/components/fitting/FittingSettings.vue';
import FittingResults from '@/components/fitting/FittingResults.vue';
import fittingMixin from '@/mixins/fittingMixin';
import formulaMixin from '@/mixins/formulaMixin';
import { showSuccess, showWarning, showInfo } from '@/utils/errorUtils';

export default {
  name: 'CoefficientOptimizationPanel',
  mixins: [fittingMixin, formulaMixin],
  components: {
    FileUploadSection,
    OptimizationSettings,
    FittingResultModal,
    CoefficientRangeSettings,
    FittingSettings,
    FittingResults
  },
  props: {
    coefficients: {
      type: Array,
      required: true
    },
    parsedLatex: {
      type: [String, Object],
      default: ''
    },
    parsedParams: {
      type: Array,
      required: true
    },
    formulaOptId: {
      type: String,
      default: ''
    },
    formulaOptDescription: {
      type: String,
      default: ''
    },
    variableDescription: {
      type: String,
      default: ''
    },
    paramDescriptions: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      localFormulaOptId: this.formulaOptId,
      localFormulaOptDesc: this.formulaOptDescription,
      groupDescriptions: {},
      uploading: false,
      fileList: [],
      dataLoaded: false,
      temperatures: [],
      socs: [],
      dataPoints: [],
      algorithmParams: {
        max_iter: 100,
        fit_standard: 'MAE',
        n_last_points: 1,
        n_pred_points: 1,
      },
      weightConfig: {
        selected_temperatures: [],
        temperatures_weight: 1.0,
        selected_socs: [],
        socs_weight: 1.0,
        days_min: 0,
        days_max: 1000,
        days_weight: 1.0
      },
      optimizing: false,
      fittingResults: null
    };
  },
  computed: {
    ...mapGetters(['getFittingData']),
    hasFormula() {
      return !!this.parsedLatex && this.coefficients.length > 0;
    },
    fittingData() {
      return { temperatures: this.temperatures, socs: this.socs, dataPoints: this.dataPoints };
    }
  },
  watch: {
    formulaOptId: 'syncFormulaId',
    formulaOptDescription: 'syncFormulaDesc',
    getFittingData: {
      handler: 'syncFittingData',
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.$nextTick(() => this.renderMathJax(true));
  },
  methods: {
    // 同步方法
    syncFormulaId(newVal) {
      this.localFormulaOptId = newVal;
    },
    syncFormulaDesc(newVal) {
      this.localFormulaOptDesc = newVal;
    },
    syncFittingData(newVal) {
      if (!newVal) return;

      Object.assign(this, {
        temperatures: newVal.temperatures || [],
        socs: newVal.socs || [],
        dataPoints: newVal.dataPoints || [],
        dataLoaded: !!(newVal.dataPoints?.length)
      });

      if (newVal.temperatures?.length) {
        this.weightConfig.selected_temperatures = [...newVal.temperatures];
      }
      if (newVal.socs?.length) {
        this.weightConfig.selected_socs = [...newVal.socs];
      }
    },

    // 事件处理
    emitFormulaIdChange() {
      this.$emit('update:formulaOptId', this.localFormulaOptId);
    },
    emitFormulaDescChange() {
      this.$emit('update:formulaOptDescription', this.localFormulaOptDesc);
    },
    handleFileUploaded(data) {
      Object.assign(this, {
        temperatures: data.temperatures,
        socs: data.socs,
        dataPoints: data.data_points,
        dataLoaded: true,
        fileList: data.fileList
      });

      this.weightConfig.selected_temperatures = [...data.temperatures];
      this.weightConfig.selected_socs = [...data.socs];
    },
    handleFileRemoved() {
      Object.assign(this, {
        temperatures: [],
        socs: [],
        dataPoints: [],
        dataLoaded: false,
        fileList: []
      });

      this.weightConfig.selected_temperatures = [];
      this.weightConfig.selected_socs = [];
    },
    updateWeightConfig(config) {
      Object.assign(this.weightConfig, config);
    },
    updateAlgorithmParams(params) {
      Object.assign(this.algorithmParams, params);
    },

    updateCoefficients(updatedCoefficients) {
      if (!this.parsedParams?.length) return;

      this.parsedParams.forEach(param => {
        if (param.type !== 'coefficient') return;

        const matchingCoef = updatedCoefficients.find(c => c.name === param.name);
        if (!matchingCoef?.optRange) return;

        const { min, max, initial } = matchingCoef.optRange;
        this.$set(param, 'optRange', { min, max, initial });

        if (matchingCoef.describe !== undefined) {
          this.$set(param, 'describe', matchingCoef.describe);
        }
      });
    },

    updateGroupDescriptions(descriptions) {
      Object.assign(this.groupDescriptions, descriptions);
    },

    // 标准化公式格式
    normalizeLatexFormula() {
      let formula = this.$store.getters.getFormula.latex || this.parsedLatex || '';

      if (typeof formula === 'string') {
        return { main_formula: formula, sub_formulas: [] };
      }

      if (typeof formula === 'object') {
        return {
          main_formula: formula.main_formula || formula.toString() || '',
          sub_formulas: Array.isArray(formula.sub_formulas) ? formula.sub_formulas : []
        };
      }

      return { main_formula: '', sub_formulas: [] };
    },

    // 构建参数映射
    buildParamMapping() {
      return this.parsedParams.map(param => {
        const result = { name: param.name, type: param.type };

        if (param.type === 'variable') {
          result.value = {
            min: parseFloat(param.value?.min ?? 0),
            max: parseFloat(param.value?.max ?? 1),
            default: parseFloat(param.value?.default ?? 0.5)
          };
          result.describe = this.paramDescriptions[param.name] || null;
        } else {
          const coef = this.coefficients.find(c => c.name === param.name);
          const optRange = coef?.optRange || param.optRange || { min: 0, max: 1, initial: 0.5 };

          result.value = {
            min: parseFloat(optRange.min),
            max: parseFloat(optRange.max),
            default: parseFloat(optRange.initial)
          };

          result.describe = coef?.describe || param.describe || null;

          const groupLetter = this.findGroupLetterForCoefficient(param.name);
          if (groupLetter && this.groupDescriptions[groupLetter]) {
            result.group_description = this.groupDescriptions[groupLetter];
          }
        }

        return result;
      });
    },

    startParameterOptimization() {
      const storeData = this.getFittingData;
      const storeDataPoints = storeData?.dataPoints || [];
      const dataPoints = this.dataPoints.length > 0 ? this.dataPoints : storeDataPoints;
      const dataLoaded = this.dataLoaded || !!storeDataPoints.length;

      if (!this.validateFittingData(dataLoaded, dataPoints)) return;

      // 同步存储数据
      if (storeDataPoints.length > 0 && !this.dataPoints.length) {
        Object.assign(this, {
          dataPoints: [...storeDataPoints],
          temperatures: [...storeData.temperatures],
          socs: [...storeData.socs],
          dataLoaded: true
        });
      }

      this.optimizing = true;
      message.loading({ content: '参数寻优中...', duration: 0, key: 'optimizing' });

      // 标准化公式格式
      const latexFormula = this.normalizeLatexFormula();
      this.updateCoefficients(this.coefficients);

      const fitRequest = {
        latex_str: latexFormula,
        fit_data: this.dataPoints,
        params: this.buildParamMapping(),
        weight_config: this.generateValidWeightConfig(this.weightConfig, this.dataPoints),
        algorithm_params: {
          max_iter: parseInt(this.algorithmParams.max_iter),
          fit_standard: this.algorithmParams.fit_standard,
          n_last_points: parseInt(this.algorithmParams.n_last_points),
          n_pred_points: parseInt(this.algorithmParams.n_pred_points)
        }
      };

      api.data.fitData(fitRequest)
        .then(response => {
          this.handleOptimizationResponse(response);
        })
        .catch(error => {
          this.handleOptimizationError(error);
        });
    },

    // 处理优化响应
    handleOptimizationResponse(response) {
      this.optimizing = false;
      message.destroy('optimizing');

      if (response.data.success) {
        showSuccess('参数寻优完成！');
        const { params: optimizedParams, metrics } = response.data;

        this.fittingResults = {
          mae: metrics.mae.toFixed(4),
          rmse: metrics.rmse.toFixed(4),
          last_point_error: metrics.last_points_error.toFixed(4),
          pred_point_error: metrics.predict_points_error.toFixed(4),
          optimized_params: optimizedParams.map(param => ({
            name: param.name,
            value: this.extractParamValue(param.value),
            describe: param.describe
          }))
        };
      } else {
        showWarning(`参数寻优失败：${response.data.message}`);
      }
    },

    // 处理优化错误
    handleOptimizationError(error) {
      this.optimizing = false;
      message.destroy('optimizing');
      console.error('参数寻优错误:', error);
      showWarning(`参数寻优失败：${error.message || '未知错误'}`);
    },

    // 提取参数值
    extractParamValue(value) {
      if (typeof value === 'object' && value !== null) {
        return value.default;
      }
      return typeof value === 'string' ? parseFloat(value) : value;
    },

    async viewDetailedResults() {
      if (!this.fittingResults?.optimized_params) {
        showWarning('没有可用的拟合结果数据');
        return;
      }

      if (!this.dataPoints?.length) {
        showWarning('没有可用的数据点，请先上传数据文件');
        return;
      }

      try {
        message.loading({ content: '正在计算拟合结果...', duration: 0, key: 'calculating' });

        const optimizedParams = this.buildOptimizedParams();
        const conditions = this.dataPoints.map(point => [
          point.temperature,
          point.soc,
          Math.max(...point.days)
        ]);

        const response = await api.data.calculateCapacity({
          latex_str: this.parsedLatex,
          params: optimizedParams,
          conditions: conditions
        });

        message.destroy('calculating');

        if (response.data.success && response.data.data_points?.length > 0) {
          this.showFittingResultModal(response.data.data_points);
        } else {
          showWarning('计算拟合结果失败：' + (response.data.message || '未知错误'));
        }
      } catch (error) {
        message.destroy('calculating');
        console.error('计算拟合结果失败:', error);
        showWarning('计算拟合结果失败：' + (error.message || '未知错误'));
      }
    },

    // 构建优化后的参数
    buildOptimizedParams() {
      return this.parsedParams.map(param => {
        const result = { name: param.name, type: param.type, describe: '' };

        if (param.type === 'variable') {
          result.value = param.value;
          result.describe = this.paramDescriptions[param.name] || '';
        } else {
          const optimizedParam = this.fittingResults.optimized_params.find(p => p.name === param.name);
          result.value = optimizedParam ? optimizedParam.value : param.value;
        }

        return result;
      });
    },

    // 显示拟合结果模态框
    showFittingResultModal(calculatedDataPoints) {
      const resultData = {
        temperatures: this.temperatures,
        socs: this.socs,
        dataPoints: this.dataPoints,
        calculatedDataPoints,
        optimized_params: this.fittingResults.optimized_params,
        metrics: {
          mae: this.fittingResults.mae,
          rmse: this.fittingResults.rmse,
          last_point_error: this.fittingResults.last_point_error,
          pred_point_error: this.fittingResults.pred_point_error
        },
        formula: this.parsedLatex
      };

      this.$store.commit('setFittingResultData', resultData);
      this.$store.commit('setFittingResultModalVisible', true);
    },

    async saveFormulaOptModel() {
      if (!this.localFormulaOptId) {
        showWarning('请先输入公式ID');
        return;
      }

      if (!this.fittingResults) {
        showWarning('请先进行参数寻优，获取拟合结果');
        return;
      }

      try {
        const formulaData = this.buildFormulaData();

        if (!this.validateCoefficientValues(formulaData.params)) {
          showInfo("已取消保存");
          return;
        }

        this.$emit('formula-saved', formulaData);
        const response = await api.formula.saveFormula(formulaData);

        if (response.data.success) {
          showSuccess(response.data.message || '公式已保存到服务器');
        } else {
          showWarning(response.data.message || '保存公式到服务器失败');
        }
      } catch (error) {
        console.error('保存公式错误:', error);
        showWarning(`保存公式失败：${error.message || '未知错误'}`);
      }
    },

    // 构建公式数据
    buildFormulaData() {
      return {
        id: this.localFormulaOptId,
        description: this.localFormulaOptDesc,
        latex: this.parsedLatex,
        params: this.parsedParams.map(param => {
          const result = { name: param.name, type: param.type };

          if (param.type === 'variable') {
            result.value = param.value;
            result.describe = this.paramDescriptions[param.name] || '';
          } else {
            const optimizedParam = this.fittingResults.optimized_params.find(p => p.name === param.name);
            result.value = optimizedParam ? optimizedParam.value : param.value;
            result.describe = param.describe || '';
          }

          return result;
        })
      };
    },

    // 验证系数值
    validateCoefficientValues(params) {
      const coefficients = params.filter(p => p.type === 'coefficient');
      const hasInvalidValue = coefficients.slice(0, 10).some(c =>
        c.value === undefined || c.value === null || c.value === 0
      );

      return !hasInvalidValue || confirm("检测到无效的系数值，是否继续保存?");
    }
  }
};
</script>

<style scoped>
.coefficient-optimization-panel .panel-row {
  margin-top: 16px;
  display: flex;
}

.coefficient-optimization-panel .panel-col {
  display: flex;
}

.coefficient-optimization-panel .panel-col > * {
  width: 100%;
}

.formula-info-settings {
  margin-top: 16px;
  padding: 0;
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.formula-info-settings > div {
  padding: 16px;
}
</style>
