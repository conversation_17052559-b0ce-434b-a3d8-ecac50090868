<template>


  <a-modal title="流程记录" width="80%" :height="600" :visible="dataVisible" @ok="closeData"
           @cancel="closeData" @close="closeData">
    <template slot="footer">
      <div>
        <a-button key="back" @click="closeData">关闭</a-button>
      </div>
    </template>
    <div>

        <tableIndex
          :pageLevel='parseInt("2")'
          :tableTotal='tableTotal'
          :pageTitleShow=false
          height="600"
          :paginationShow=false
          :loading='loading'
        >
          <template #table>
            <ag-grid-vue class='table ag-theme-balham'
                         style="height: 300px"
                         :tooltipShowDelay="0"
                         :columnDefs='dataColumnDefs'
                         :rowData='dataRowData'
                         @grid-ready="onGridReady"
                         :defaultColDef='defaultColDef'>
            </ag-grid-vue>
          </template>
        </tableIndex>

    </div>


  </a-modal>

</template>

<script>

  export default {
    components: {},
    data() {
      return {
        dataVisible: false,
        record: {},
        visible: false,
        loading: false,
        gridApi: null,
        columnApi: null,
        tableTotal: 0,
        queryParam: {},
        defaultColDef: {
          flex: 1,
          minWidth: 120,
          filter: false,
          floatingFilter: false,
          editable: false,
        },
        pageNo: 1,
        pageSize: 20,
        dataRowData: [],
        dataColumnDefs: [
          {
            headerName: '操作时间',
            field: 'createTime',
            minWidth: 110,
            width: 110,
          },
          {
            headerName: '操作人',
            field: 'createName',
            minWidth: 110,
            width: 110,
          },
          {
            headerName: '操作内容',
            field: 'handleResult',
            minWidth: 110,
            width: 110,
            cellRenderer: function (params) {
              console.log(params)
              //技师提交
              if(params.data.abnormalStatus == 'techConfirm'){
                if(params.data.orderNumber == 1){
                  return "入箱中检"
                }else if(params.data.isBefore == 1 ){
                  return "出箱中检"
                }else{
                  return "入箱中检"
                }
                //工程师判定
              }else if(params.data.abnormalStatus == 'engineerConfirm'){
                if(params.data.orderNumber == 1){
                  return "入箱中检确认"
                }else if(params.data.isBefore == 1 ){
                  return "出箱中检确认"
                }else{
                  return "入箱中检确认"
                }
                //技师确认
              }else{
                return params.data.handleMsg
              }
            },
          },
          {
            headerName: '意见',
            field: 'abnormalMsg',
            minWidth: 110,
            width: 110,
            cellRenderer: function (params) {
              //工程师判定
              if(params.data.abnormalStatus == 'engineerConfirm'){
                let abnormalKeyValue = {
                  ongoing: '进行中',
                  earlyEnd: '状态正常-提前结束',
                  batteryDisassembly: '状态正常-电池拆解',
                  pressureDrop: '掉压失效-终止测试',
                  abnormalHot: '异常发热-终止测试',
                  openShellAndLeak: '开壳漏液-终止测试',
                  shellRust: '壳体生锈-终止测试',
                  operationError: '作业错误-终止测试',
                  thermalRunaway: '热失控-终止测试',
                  acrException: '内阻异常-终止测试',
                  swelling: '鼓包形变 - 终止测试'
                }

                return abnormalKeyValue[params.data.handleResult]

              }else{
                return params.value
              }
            },
          },
          {
            headerName: '下一节点',
            field: 'handleMsg',
            minWidth: 110,
            width: 200,
            cellRenderer: function (params) {
              //技师提交
              if(params.data.abnormalStatus == 'techConfirm'){
                if(params.data.isGroupCanHandle == 1){
                  return "组长确认"
                }else {
                  return "工程师确认"
                }

                //工程师判定
              }else if(params.data.abnormalStatus == 'engineerConfirm'){
                return params.value
                //技师确认
              }else{

              }
            },
          },
          {
            headerName: '下一节点操作人',
            field: 'handleMsg',
            minWidth: 110,
            width: 110,
            cellRenderer: function (params) {
              //技师提交
              if(params.data.abnormalStatus == 'techConfirm'){
                if(params.data.isGroupCanHandle == 1){
                  return params.data.groupName
                }else {
                  return params.data.engineerName
                }
                // return params.data.engineerName
                //工程师判定
              }else if(params.data.abnormalStatus == 'engineerConfirm'){
                return params.data.testMan
                //技师确认
              }else{

              }
            },
          }
        ]
      }
    },
    computed: {},
    methods: {
      openData(record) {
        this.dataVisible = true
        this.record = record
        this.dataRowData = JSON.parse(record.historyJson)
        console.log(this.dataRowData)
        this.tableTotal = this.dataRowData.length

        this.loading = false

      },
      closeData() {
        this.dataVisible = false
      },

      onGridReady(params) {
        this.gridApi = params.api;
        this.columnApi = params.columnApi;
        let abnormalKeyValue = {
          ongoing: '进行中',
          earlyEnd: '状态正常-提前结束',
          batteryDisassembly: '状态正常-电池拆解',
          pressureDrop: '掉压失效-终止测试',
          abnormalHot: '异常发热-终止测试',
          openShellAndLeak: '开壳漏液-终止测试',
          shellRust: '壳体生锈-终止测试',
          operationError: '作业错误-终止测试',
          thermalRunaway: '热失控-终止测试',
          acrException: '内阻异常-终止测试',
          swelling: '鼓包形变 - 终止测试'
        }

      },
      formatValue(key) {
        return this.abnormalKeyValue[key]
      },

    }
    ,
    mounted() {

    }

  }
</script>

<style lang='less' scoped="">
  @import '/src/components/pageTool/style/pbiSearchItem.less';

  :root {
    --scroll-display: none;
    --scroll-border-bottom: none;
    --scroll-border-bottom-fixed: none;
  }

  /deep/ .ag-body-horizontal-scroll {
    border-bottom: var(--scroll-border-bottom) !important;
  }

  /deep/ .ag-body-horizontal-scroll-viewport {
    display: var(--scroll-display) !important;
    border-bottom: var(--scroll-border-bottom) !important;
  }

  /deep/ .ag-horizontal-left-spacer,
  /deep/ .ag-horizontal-right-spacer {
    border-bottom: var(--scroll-border-bottom-fixed) !important;
  }

  .page-container {
    height: auto;
  }

  /deep/ .code_link {
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
  }
</style>