<template>
  <div>
    <a-drawer :title="'在线编辑图表(' + formData.chartTitle + ')'" placement="right" :visible="true" :width="width"
      @close="handleClose">
      <div class="navigation-wrapper">
        <template v-for="item in tabOptions">
          <div v-if="item.show" class="navigation" :style="{border:isShowContent === item.id ? '1px solid #1890ff' :'1px solid #ccc'}"
             @click="handleChangeValue('isShowContent',item.id)">
            <div><svg :t="item.svgObj.t" class="icon" viewBox="0 0 1024 1024" version="1.1"
                xmlns="http://www.w3.org/2000/svg" :p-id="item.svgObj.pId" width="15" height="15">
                <path :d="item.svgObj.pathT" :p-id="item.svgObj.pathId"
                  :fill="isShowContent === item.id ? '#1890ff' :'#ccc' "></path>
              </svg></div>
            <div :style="{color:isShowContent === item.id ? '#1890ff' :'#ccc'}">{{item.title}}</div>
          </div>
        </template>
        
      </div>
      <div class="form-wrapper" id="previewDrawer">
        <a-form :label-col="labelCol" :wrapper-col="wrapperCol">
          <!-- 用户模板 -->
          <template v-if="isShowContent === 7">
            <div class="action-content">
              <a-input v-model="UserTemplateSearchParam.templateName" placeholder="请输入模板名称" allow-clear style="width: 200px"
                @keyup.enter="getTemplateList">
                <a-icon slot="prefix" type="search" />
              </a-input>
              <div>
                <a-button class="ml10" type="primary" size="small" @click="getTemplateList">查询</a-button>
                <a-button class="ml10" size="small" @click="deleteChartTemplate">删除</a-button>
              </div>
            </div>
            <a-table size="small" bordered :columns="UserTemplateColumns" :dataSource="userTemplateList" :rowKey="(record) => record.id"
              :rowSelection="{ selectedRowKeys: userSelectedRowKeys, onChange: onUserSelectChange }" >
              <span slot="templateName" slot-scope="text, record">
                <a-button size="small" type="link" @click="chartTemplateDetail(record)">{{record.templateName}}</a-button>
              </span>
              <span slot="action" slot-scope="text, record">

                <a-tooltip>
                  <template slot="title">
                    选择使用该模板
                  </template>
                  <a-button type="link" style="font-size: 14px;"  @click="chooseChartTemplate(record)"><a-icon type="select" /></a-button>
                </a-tooltip>
                 <a-tooltip>
                  <template slot="title">
                    传阅该模板给他人
                  </template>
                  <a-button type="link" style="font-size: 14px;" @click="shareChartTemplate(record)"><a-icon type="share-alt" /></a-button>
                </a-tooltip>
              </span>
            
            </a-table>
          </template>

          <!-- 图例 -->
          <a-form-item v-if="isShowContent === 0">
            <span slot="label"><a-icon :type="showObj.isShowLegend ? 'up' : 'down'" class="mr10 "
                @click="handleShow('isShowLegend')" />
              图例</span>
            <br />

            <!-- 图例-数据 -->
            <div class="form-block" style="align-items: flex-start;" v-show="showObj.isShowLegend && this.formData.legendZip === 'false'">
              <span class="chli-title">
                <span style="color: #1890ff;cursor: pointer;"
                  @click="handleShow('isShowLegendData')">{{showObj['isShowLegendData']
                  ? '收起' : '展开'}}</span>
                数据</span>
              <div v-show="showObj.isShowLegendData">
                <div>
                  <a-checkbox :indeterminate="legendIndeterminate" :checked="checkAll" @change="onLegendAllChange">
                    全选
                  </a-checkbox>
                </div>
                <a-checkbox-group v-model="legendList" :options="legendOptions" @change="onLegendChange" />
              </div>
            </div>
             <!-- 图例-名称 -->
             <div class="form-block" style="align-items: flex-start;" v-show="showObj.isShowLegend">
              <span class="chli-title">
                <span style="color: #1890ff;cursor: pointer;"
                  @click="handleShow('isShowLegendName')">{{showObj['isShowLegendName']
                  ? '收起' : '展开'}}</span>
                名称</span>
              <div v-show="showObj.isShowLegendName">
                <div class="sequence-item" v-for="(item,index) in legendEditNameList" :key="index">
                  <div class="item-content mr10">{{item.originName}}</div>
                  <a-input size="small" style="width: 100px;" class="mr10" v-model="item.newName"
                    @blur="handleEditlegendName($event,index)" @pressEnter="handleEditlegendName($event,index)" />
                </div>
              </div>
            </div>
            <!-- 图例顺序 -->
            <div class="form-block" style="align-items: flex-start;" v-show="showObj.isShowLegend">
              <span class="chli-title">
                <span style="color: #1890ff;cursor: pointer;"
                  @click="handleShow('isShowLegendSort')">{{showObj['isShowLegendSort']
                  ? '收起' : '展开'}}</span>
                顺序</span>
              <div v-show="showObj.isShowLegendSort">
                <div class="sequence-item" v-for="(item,index) in legendSortList" :key="index">
                  <div class="item-content">{{item}}</div>
                  <a-button type="link" :disabled="index === 0" @click="moveDataOne(index,'up')"><a-icon
                      type="arrow-up" /></a-button>
                  <a-button type="link" :disabled="index === legendSortList.length - 1"
                    @click="moveDataOne(index,'down')"><a-icon type="arrow-down" /></a-button>
                </div>
              </div>
            </div>
            <!-- 图例显隐 -->
            <div class="form-block" style="align-items: flex-start;" v-show="showObj.isShowLegend && legendRevealList && this.formData.legendZip === 'false'">
              <span class="chli-title">
                <span style="color: #1890ff;cursor: pointer;"
                  @click="handleShow('isShowLegendHide')">{{showObj['isShowLegendHide']
                  ? '收起' : '展开'}}</span>
                显隐</span>
              <div v-show="showObj.isShowLegendHide">
                <div>
                  <a-checkbox :indeterminate="legendRevealIndeterminate" :checked="legendRevealcheckAll" :disabled = "legendList.length == 0"
                    @change="onLegendRevealAllChange">
                    全选
                  </a-checkbox>
                </div>
                <a-checkbox-group v-model="legendRevealList" :options="legendRevealOptions" @change="onLegendRevealChange" />
              </div>
            </div>


            <!-- 背景颜色、图例朝向、图例压缩、图例名称、图例字号、图例距离顶部位置、图例距离左边部位置、图例项宽度、图例项高度、图例项间隔 -->
            <div class="form-block" v-for="htmlSubItem in legendTabHtmlList" :key="htmlSubItem.dataKey" v-show="htmlSubItem.show">
              <span class="chli-title">{{htmlSubItem.label}}</span>
              <a-radio-group v-if="htmlSubItem.inputType === 'orientRadio'" v-model="formData[htmlSubItem.dataKey]" @change="handleSubmit('','','',htmlSubItem.dataKey)">
                <a-radio value="horizontal">
                  水平
                </a-radio>
                <a-radio value="vertical">
                  垂直
                </a-radio>
              </a-radio-group>
              <a-radio-group v-if="htmlSubItem.inputType === 'radio'" v-model="formData[htmlSubItem.dataKey]" @change="handleLegendZip">
                <a-radio value="true">
                    是
                  </a-radio>
                  <a-radio value="false">
                    否
                  </a-radio>
              </a-radio-group>
              <a-radio-group v-if="htmlSubItem.inputType === 'LegendNameRadio'" v-model="formData[htmlSubItem.dataKey]" @change="handleToggleLegendName">
                  <a-radio value="sampleCode">
                    样品编号
                  </a-radio>
                  <a-radio value="batteryCode">
                    电芯编码
                  </a-radio>
                </a-radio-group>
                <a-input-number v-if="htmlSubItem.inputType === 'inputNumber'" v-model="formData[htmlSubItem.dataKey]" @blur="handleSubmit('','','',htmlSubItem.dataKey)" @pressEnter="handleSubmit('','','',htmlSubItem.dataKey)" />
                <input v-if="htmlSubItem.inputType === 'inputColor' && showObj.isShowLegendBgColor" size="small" v-model="formData[htmlSubItem.dataKey]" type="color" class="mr10" @change="handleChangeLegendColor" />
                {{ htmlSubItem.dataKey === 'legendBgColor' && showObj.isShowLegendBgColor ? formData.legendBgColor : '' }}
                
                <a-button v-if="htmlSubItem.dataKey === 'legendBgColor' && !showObj.isShowLegendBgColor" type="link"  size="small" @click="handleShow('isShowLegendBgColor')">修改颜色</a-button>
                <a-button v-else-if="!['legendOrient','legendZip','legendNameType',''].includes(htmlSubItem.dataKey)" class="ml10" size="small" @click="handleSubmit(htmlSubItem.dataKey, original[htmlSubItem.dataKey])">重置</a-button>
                
              </div>
          </a-form-item>

          <a-radio-group v-show="isShowContent === 1" v-model="dataEditType">
            <a-radio-button value="all">
              全部修改
             </a-radio-button>
             <a-radio-button value="alone">
              个性化修改
            </a-radio-button>
          </a-radio-group>

          <!-- 数据标签-单条修改 -->
          <a-form-item v-show="isShowContent === 1 && dataEditType === 'alone'" v-for="(item, index) in dataOptions" :key="index">
            <span slot="label"><a-icon :type="showObj['isShowTag' + index] ? 'up' : 'down'" class="mr10"
                @click="handleShow('isShowTag' + index)" />
              {{  checkData[index].dataName || checkData[index].name }}</span>
            <br />

            <div class="form-block" v-for="htmlSubItem in singleDataTabHtmlList" :key="htmlSubItem.dataKey"  v-show="showObj['isShowTag' + index]">
              <span v-if="htmlSubItem.dataKey === 'synchronization'" class="chli-title" :id="'tag' + index">同步</span>
              <span v-else class="chli-title">{{htmlSubItem.label}}</span>

              <a-select v-if="htmlSubItem.inputType === 'synchronizationBtn'" size="small" :disabled="checkData[index].disabled" v-model="checkData[index][htmlSubItem.dataKey]">
                  <a-select-option v-for="(selectItem, selectIndex) in dataOptions" :key="selectIndex">
                    图表标签{{ selectIndex + 1 }}
                  </a-select-option>
              </a-select>
              <a-switch v-if="htmlSubItem.inputType === 'switch'" size="small" :disabled="checkData[index].disabled" :checked="checkData[index][htmlSubItem.dataKey]"
                @change="$event => handleChangePoint($event, index, htmlSubItem.dataKey)" />
              <a-radio-group v-if="htmlSubItem.inputType === 'radio'" size="small" :disabled="checkData[index].disabled" v-model="checkData[index][htmlSubItem.dataKey]" @change="handleSubmit('','','',htmlSubItem.dataKey,index)">
                <a-radio value="1">
                  连接
                </a-radio>
                <a-radio value="0">
                  不连接
                </a-radio>
              </a-radio-group>
              <a-select v-if="htmlSubItem.inputType === 'select'" size="small" :disabled="checkData[index].disabled" v-model="checkData[index][htmlSubItem.dataKey]"  @change="handleSubmit('','','',htmlSubItem.dataKey,index)">
                <a-select-option v-for="selectItem in htmlSubItem.selectOption" :key="selectItem.key">
                  {{ selectItem.value }}
                </a-select-option>
              </a-select>
              <a-input-number v-if="htmlSubItem.inputType === 'inputNumber'" size="small" :disabled="checkData[index].disabled" v-model="checkData[index][htmlSubItem.dataKey]"
                @blur="handleSubmit('','','',htmlSubItem.dataKey,index)" @pressEnter="handleSubmit('','','',htmlSubItem.dataKey,index)" />
              <input v-if="htmlSubItem.inputType === 'inputColor'" size="small" :disabled="checkData[index].disabled" v-model="checkData[index][htmlSubItem.dataKey]" type="color" class="mr10"
                @change="$event => handleChangeColor($event, index, htmlSubItem.dataKey)" />
              <span v-if="htmlSubItem.inputType === 'inputColor'" :style="{color:checkData[index].disabled ? '#ccc' : '#333'} " >{{ checkData[index][htmlSubItem.dataKey] }}</span>
              <a-button v-if="htmlSubItem.dataKey === 'synchronization'" class="ml10" size="small" :disabled="checkData[index].disabled" @click="handleSynchronization(index)">同步</a-button>
              <a-button v-else-if="!['maxPoint','minPoint','connectNulls'].includes(htmlSubItem.dataKey)" class="ml10" size="small" :disabled="checkData[index].disabled"
              @click="handleSubmit(htmlSubItem.dataKey,original.checkData ? original.checkData[index][htmlSubItem.dataKey]: original.series[index][htmlSubItem.dataKey], index)">重置</a-button>
            </div>
          </a-form-item>

          <!-- 数据标签-全部修改 -->
          <a-form-item v-show="isShowContent === 1 && dataEditType === 'all'">
            <span slot="label"><a-icon :type="showObj.isShowAllDataType ? 'up' : 'down'" class="mr10"
              @click="handleShow('isShowAllDataType')" />
            全部修改</span>
            <br />

            <div class="form-block" v-for="htmlSubItem in dataTabHtmlList" :key="htmlSubItem.dataKey" v-show="htmlSubItem.show">
              <span class="chli-title">{{htmlSubItem.label}}</span>

              <a-radio-group v-if="htmlSubItem.inputType === 'radio'" size="small" v-model="formData.allData[htmlSubItem.dataKey]" @change="handleEditAllData(htmlSubItem.dataKey)">
                  <a-radio value="1">
                    连接
                  </a-radio>
                  <a-radio value="0">
                    不连接
                  </a-radio>
              </a-radio-group>
              <a-select v-if="htmlSubItem.inputType === 'select'" size="small" v-model="formData.allData[htmlSubItem.dataKey]"  @change="handleEditAllData(htmlSubItem.dataKey)">
                <a-select-option v-for="selectItem in htmlSubItem.selectOption" :key="selectItem.key">
                  {{ selectItem.value }}
                </a-select-option>
              </a-select>
              <a-input-number v-if="htmlSubItem.inputType === 'inputNumber'" size="small" v-model="formData.allData[htmlSubItem.dataKey]"
                @blur="handleEditAllData(htmlSubItem.dataKey)" @pressEnter="handleEditAllData(htmlSubItem.dataKey)" />
              <input v-if="htmlSubItem.inputType === 'inputColor'" size="small" :value="formData.allData[htmlSubItem.dataKey]" type="color" class="mr10"
                @change="$event => handleAllDataColor($event, htmlSubItem.dataKey)" />
              <span v-if="htmlSubItem.inputType === 'inputColor' && htmlSubItem.dataKey === 'lineColor'">{{ formData.allData.lineColor || 'Mixed' }}</span>
              <span v-if="htmlSubItem.inputType === 'inputColor' && htmlSubItem.dataKey === 'itemColor'">{{ formData.allData.itemColor || 'Mixed' }}</span>
              <a-button v-if="htmlSubItem.inputType !== 'radio'" class="ml10" size="small" @click="handleEditAllData(htmlSubItem.dataKey,1)">重置</a-button>
            </div>
          </a-form-item>

          <!-- 横坐标坐标轴、纵坐标坐标轴、纵坐标副坐标轴、标题、图表位置 -->
          <div v-for="(htmlItem,axisIndex) in htmlList">
            <a-form-item v-if="htmlItem.displayCondition">
              <span slot="label"><a-icon :type="showObj[htmlItem.iconDirection] ? 'up' : 'down'" class="mr10"
                @click="handleShow(htmlItem.iconDirection)" />
                {{htmlItem.formName }}</span>
              <br />

              <div class="form-block" v-for="htmlSubItem in htmlItem.options" :key="htmlSubItem.dataKey" v-show="htmlSubItem.show">
                <span class="chli-title">{{htmlSubItem.label}}</span>

                <a-select v-if="htmlSubItem.inputType === 'select'" size="small" v-model="formData[htmlSubItem.dataKey]" @change="handleSubmit('','','',htmlSubItem.dataKey)">
                  <a-select-option v-for="selectItem in htmlSubItem.selectOption" :key="selectItem.key">
                    {{ selectItem.value }}
                  </a-select-option>
                </a-select>
                <a-input v-if="htmlSubItem.inputType === 'input'" size="small" v-model="formData[htmlSubItem.dataKey]" @blur="handleSubmit('','','',htmlSubItem.dataKey)" @pressEnter="handleSubmit('','','',htmlSubItem.dataKey)" />
                <a-input-number v-if="htmlSubItem.inputType === 'inputNumber'" size="small" class="mr10" v-model="formData[htmlSubItem.dataKey]" @blur="handleSubmit('','','',htmlSubItem.dataKey)" @pressEnter="handleSubmit('','','',htmlSubItem.dataKey)" />
                
                <a-button class="ml10" size="small" @click="handleSubmit(htmlSubItem.dataKey, original[htmlSubItem.dataKey], htmlSubItem.dataKey === 'yDecimalNum' ? 'yDecimalNum' : '')">重置</a-button>  
            </div>
            </a-form-item>
          </div>

          <!-- 连线 -->
          <a-form-item v-if="calendarLife &&  isShowContent === 5">
            <span slot="label"> 连线</span>
            <br />
            <!-- 连接空值 -->
            <div class="form-block" style="align-items: flex-start;">
              <span class="chli-title">连接空值</span>
              <div>
                <a-radio-group size="small" v-model="formData.allData.connectNulls" @change="handleEditAllData('connectNulls')">
                  <a-radio value="1">
                    连接
                  </a-radio>
                  <a-radio value="0">
                    不连接
                  </a-radio>
                </a-radio-group>
              </div>
            </div>
          </a-form-item>

          <!-- 颜色方案 -->
          <a-form-item v-if=" calendarLife &&  isShowContent === 6">
            <span slot="label"><a-icon :type="showObj.isShowColor ? 'up' : 'down'" class="mr10"
              @click="handleShow('isShowColor')" />
              颜色方案</span>
            <br />
            <!-- 颜色方案 -->
            <div class="form-block" style="align-items: flex-start;" v-show="showObj.isShowColor">
              <span class="chli-title">颜色方案</span>
              <div class="color-content">
                <div class="color-row" v-for="(item,index) in colorOptions" :key="index" @click="handleColorOption(item.children)">
                  <div class="color-block" v-for="(childItem,childIndex) in item.children" :key="childIndex" :style="{'background':childItem}"></div>
                </div>
              </div>
            </div>
          </a-form-item>

        </a-form>
      </div>
      <div class="btn-wrapper">
        <a-button class="mr10" type="primary" @click="handleSaveAsTemplate">
          另存为模板
        </a-button>
        <a-button class="mr10" type="primary" @click="handleOk">
          保存
        </a-button>
        <a-button class="mr10"  @click="handleAllReset">
          重置
        </a-button>
        <a-button  @click="handleClose">
          关闭
        </a-button>

      </div>
    </a-drawer>

    <!-- 另存为模板弹窗 -->
    <a-modal
      :title=" isSaveTemplate ?  '另存为模板' : '模板详情'"
      :visible="isSaveTemplateModalVisible"
      @ok="handleSaveTemplateOk"
      @cancel="handleSaveTemplateCancel"
      :confirmLoading="templateSaving"
      width="800px"
    >
      <a-form :form="saveTemplateForm" :label-col="{ span: 3 }" :wrapper-col="{ span: 19 }">
        <a-form-item label="模板名称">
          <a-input
            v-if="isSaveTemplate"
            v-decorator="[
              'templateName',
              {
                rules: [
                  { required: true, message: '请输入模板名称' },
                  { min: 1, max: 50, message: '模板名称长度为1-50个字符' }
                ]
              }
            ]"
            placeholder="请输入模板名称"
            :maxLength="50"
          />
          <span v-else>{{currentTemplate.templateName}}</span>
        </a-form-item>

        <a-form-item label="模板内容">
            <a-spin :spinning="screenImageLoading" style="min-height: 300px;" tip="Loading...">
              <img :src="screenImage" alt="" style="width: 100%;">
            </a-spin>
          
        </a-form-item>
      </a-form>
    </a-modal>

    <pbiShare v-if="isShare" :currentContent="currentTemplate" @cancel="isShare = false"></pbiShare>


  </div>
</template>

<script>
  import _ from "lodash";
  import Vue from "vue";
  import { mapGetters } from 'vuex'
  import { getChartTemplateList,saveChartTemplate,updateChartTemplate,deleteChartTemplate } from "@/api/modular/system/chartTemplate.js"
  import pbiShare from '@/components/pageTool/components/pbiShare.vue'
  export default {
    props: {
      width: {
        type: Number,
        default: 600
      },
      templateParam:{
        type: Object,
        default: {}
      },
      // isDuplicateData: {
      //   type: Boolean,
      //   default: false,
      // },
      isLegendLeft: {
        type: Boolean,
        default: false,
      },
      calendarLife:{   // 日历寿命测试报告独有
        type: Boolean,
        default: false,
      },
      legendNameTypeShow: {
        type: Boolean,
        default: false,
      },
      LegendNameTypeList: {
        type: Array,
        default: () => ([]),
        required: false
      },
      legendOptions: {
        type: Array,
        default: []
      },
      data: {
        type: Array,
        default: []
      },
      original: {
        type: Object,
        default: {}
      },
      editData: {
        type: Object,
        default: {}
      },
      checkObj: {
        type: Object,
        default: {},
        required: false
      },
      screenImageId:{
        type: String,
        default:''
      }

    },
    components:{
      pbiShare
    },
    data() {
      return {
        screenImage:'',
        screenImageLoading:false,
        isShare:false,
        isSaveTemplate:false,
        isShowContent: 3,  // 0: 图例  1:数据标签(个性化)  2:坐标轴 3:标题  4:图表位置   5:数据标签(全部)    6:颜色   7:用户模板
        dataEditType:'all', //全部修改 or 个性化修改
        showObj: {
          isShowTemplateManage: false,     //模板管理
          isShowTemplatePresets: true,    //模板预设

          isShowLegend: true,       //图例
          isShowLegendData: false,  //图例底下的数据
          isShowLegendName: false,  //图例底下的名称
          isShowLegendSort: false,  //图例底下的顺序
          isShowLegendHide: false,  //图例底下的显隐
          isShowLegendBgColor: false,  //图例底下的背景颜色

          isShowAllDataType: true,       //图例


          isShowX: true,
          isShowY: true,
          isShowY2: true,
          isShowTitle: true,
          isShowGrid: true,

          isShowColor:true
        },

        formData: {
          chartTitle: '', //图表标题
          XTitle: '', //X轴标题
          YTitle: null, //Y轴标题
          YTitle2: null, //Y轴副标题
          titleTop: '',
          yTitleLetf:'',
          yTitleRight:'',

          legendBgColor: '',//图例背景色
          legendOrient: '',//图例朝向
          legendZip:'', //图例压缩
          legendNameType:'',//图例名称类型(样品编号、电芯编号)
          legendFontSize:'',//图例字号
          legendTop: '',//图例距离底部的位置
          legendLeft:'',//图例距离右边的位置
          legendRight:'',//图例距离右边的位置
          legendWidth: 0,
          legendHeight: 0,
          legendGap: 0,

          xType: "",
          xMin: 0,
          xMax: 0,
          xInterval: 0,

          yType: 0,
          yMin: 0,
          yMax: 0,
          yInterval: 0,
          yDecimalNum: 0,

          yType2: 0,
          yMin2: 0,
          yMax2: 0,
          yInterval2: 0,

          gridTop: '',
          gridLeft: '',
          gridRight: '',
          gridBottom: '',
          allData:{
            connectNulls:-1,
            symbol:'',
            symbolSize:'',
            itemColor:'',
            lineType:'',
            lineWidth:'',
            lineColor:''
          },
        },
        rollObj: {},
        legendRevealOptions:[], // 图例--显隐的选择项
        legendIndeterminate: false, //图例-数据--全选
        checkAll: false,

        /*图例显隐：只去除图例，线保留*/
        legendRevealIndeterminate: false,
        legendRevealcheckAll: false,

        checkData: [],
        labelCol: {
          span: 9
        },
        wrapperCol: {
          span: 15
        },
        dataOptions: [], // 图例-数据数组
        legendList: [], // 选中的图例数组
        legendEditNameList: [], // 图例改名数组
        legendSortList: [], // 图例排序数组
        legendRevealList: [], // 选中需要展示的图例数组

        // 用户模板相关数据
        userAllTemplateList: [], // 用户模板列表
        userTemplateList: [], // 用户模板列表
        currentTemplate:{},

        // 另存为模板弹窗相关数据
        isSaveTemplateModalVisible: false, // 另存为模板弹窗显示状态
        templateSaving: false, // 模板保存状态
        saveTemplateForm: this.$form.createForm(this), // 另存为模板表单

        UserTemplateColumns:[
          {
            title: '序号',
            dataIndex: 'index',
            align:'center',
            customRender: (text, record, index) => `${index + 1}`
          },
          {
            title: '模板名称',
            align:'center',
            dataIndex: 'templateName',
            scopedSlots: {customRender: 'templateName'},
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:'center',
            width:100,
            scopedSlots: {customRender: 'action'},
          },

        ],
        userSelectedRows:[],
        userSelectedRowKeys:[],
        UserTemplateSearchParam:{
          templateName:''
        },

        tabOptions: [
          
          {
            id: 3,
            title: "标题",
            svgObj: {
              t: '1711437379542',
              pId: "2992",
              pathT: 'M782.222222 128H241.777778c-31.288889 0-56.888889 25.6-56.888889 56.888889v42.666667c0 15.644444 12.8 28.444444 28.444444 28.444444s28.444444-12.8 28.444445-28.444444v-42.666667h241.777778v654.222222h-56.888889c-15.644444 0-28.444444 12.8-28.444445 28.444445s12.8 28.444444 28.444445 28.444444h184.888889c15.644444 0 28.444444-12.8 28.444444-28.444444s-12.8-28.444444-28.444444-28.444445h-71.111112V184.888889h241.777778v42.666667c0 15.644444 12.8 28.444444 28.444445 28.444444s28.444444-12.8 28.444444-28.444444v-42.666667c0-31.288889-25.6-56.888889-56.888889-56.888889z',
              pathId: '2993'
            },
            show:true
          },
          {
            id: 0,
            title: "图例",
            svgObj: {
              t: '1705036043101',
              pId: "4613",
              pathT: 'M908.49 562.043H754.526c-23.01 114.216-123.222 200.183-243.363 200.183S290.81 676.259 267.8 562.043H113.836c-27.431 0-49.667-22.405-49.667-50.044 0-27.64 22.236-50.044 49.667-50.044H267.8c23.01-114.216 123.222-200.183 243.363-200.183s220.353 85.967 243.363 200.183H908.49c27.431 0 49.666 22.404 49.666 50.044 0 27.64-22.236 50.044-49.666 50.044zM511.163 361.865c-82.291 0-148.998 67.218-148.998 150.135s66.707 150.135 148.998 150.135S660.16 594.917 660.16 512s-66.706-150.135-148.997-150.135z',
              pathId: '4614'
            },
            show:true
          },

          {
            id: 1,
            title: "数据标签",
            svgObj: {
              t: '1705305785161',
              pId: "9148",
              pathT: 'M25.6 537.1392a25.6 25.6 0 1 1 0-51.2h141.1072a25.6 25.6 0 0 0 24.5248-18.2272l118.1184-393.7792a51.2 51.2 0 0 1 98.0992 0L665.6 934.4l118.1184-393.728a76.8 76.8 0 0 1 73.5744-54.784H998.4a25.6 25.6 0 1 1 0 51.2h-141.1072a25.6 25.6 0 0 0-24.5248 18.2272l-118.1184 393.7792a51.2 51.2 0 0 1-98.0992 0L358.4 88.6272 240.2816 482.4064a76.8 76.8 0 0 1-73.5744 54.784H25.6z',
              pathId: '9149'
            },
            show:true
          },
          {
            id: 2,
            title: "坐标轴",
            svgObj: {
              t: '1744951076172',
              pId: "5958",
              pathT: 'M851.727059 901.722353v-83.124706H256v175.284706h-60.235294v-175.284706H30.72v-60.235294h165.044706V171.670588H113.242353L225.882353 30.117647l113.242353 141.552941H256v586.691765h595.727059v-82.522353l142.155294 112.64-142.155294 113.242353z',
              pathId: '5959',
            },
            show:true
          },
          {
            id: 4,
            title: "图表位置",
            svgObj: {
              t: '1714109544019',
              pId: "3883",
              pathT: 'M339.256541 585.016169l-23.25839-23.25839c-128.506263-128.469693-128.469693-336.807821 0.03657-465.314085C444.577554-32.135708 652.915681-32.135708 781.385375 96.370555c128.506263 128.506263 128.469693 336.807821-0.03657 465.350654l-23.33153 23.29496h83.123146a36.569796 36.569796 0 0 1 31.340315 17.772921l219.345637 365.66139c18.833445 31.340315-14.993616 67.873541-47.687014 51.490273l-273.468935-136.771037-201.682424 134.503709a36.569796 36.569796 0 0 1-40.592474 0L326.713101 883.169716l-273.468934 136.771037c-32.693398 16.383269-66.520459-20.113388-47.687014-51.490273l219.345636-365.69796a36.569796 36.569796 0 0 1 31.340315-17.736351h83.013437z m73.139592 73.139592H276.941609l-144.084996 240.26356L312.962858 808.238204a36.569796 36.569796 0 0 1 36.642935 2.303897L548.691763 943.32703l199.049399-132.748359a36.569796 36.569796 0 0 1 36.642936-2.303898l180.179385 90.107978-144.084996-240.22699h-135.600804l-110.367644 110.440784a36.569796 36.569796 0 0 1-51.709692 0l-110.440784-110.440784z m136.25906 32.839677l180.98392-180.983921a255.915432 255.915432 0 1 0-361.894701-361.894701 255.915432 255.915432 0 0 0-0.03657 361.931271l180.947351 180.947351z m-129.237659-232.657042a182.81241 182.81241 0 1 1 258.548458-258.475318 182.81241 182.81241 0 0 1-258.548458 258.475318z m51.709691-51.709692a109.709388 109.709388 0 1 0 155.092505-155.129075 109.709388 109.709388 0 0 0-155.092505 155.129075z',
              pathId: '3884'
            },
            show:true
          },
          {
            id: 6,
            title: "颜色",
            svgObj: {
              t: '1721979531527',
              pId: "5391",
              pathT: 'M465.499307 1021.354667c-21.504 0-44.117333-2.048-68.693334-5.12l-6.144-1.024c-174.336-26.624-293.290667-195.925333-298.410666-203.093334C-76.026027 555.776 8.11264 296.277333 167.08864 152.661333 325.04064 9.045333 588.635307-52.48 819.37664 133.205333c148.736 119.978667 193.877333 286.122667 195.925333 293.290667v2.048c21.504 116.906667 4.096 203.093333-52.309333 258.474667-86.186667 83.114667-228.693333 56.405333-248.234667 52.309333-26.709333-3.072-46.165333 5.12-60.501333 22.528-15.36 19.456-18.432 45.141333-13.312 60.501333 14.336 43.093333 16.384 75.861333 6.144 100.522667-29.781333 64.682667-90.282667 98.474667-181.589333 98.474667z m-65.621334-67.669334l6.144 1.024c99.498667 15.36 160-4.096 184.576-59.477333 0-1.024 5.12-14.336-8.192-55.381333-13.312-36.949333-3.072-85.162667 23.552-117.930667 27.733333-34.901333 68.693333-50.261333 116.906667-45.141333l3.072 1.024c1.024 0 128.170667 27.648 193.877333-35.925334 40.021333-38.997333 52.309333-106.666667 34.901334-201.045333-4.096-13.312-48.213333-157.952-175.36-259.498667C578.309973 17.322667 347.56864 71.68 208.04864 197.802667 79.877973 314.709333-14.500693 537.258667 143.451307 778.325333c1.024 0 108.714667 153.856 256.426666 175.36z m0 0" p-id="5392"></path><path d="M158.89664 538.282667c0 33.962667 27.562667 61.525333 61.525333 61.525333s61.525333-27.562667 61.525334-61.525333-27.562667-61.525333-61.525334-61.525334c-34.048 0-61.525333 27.562667-61.525333 61.525334z m71.765333-184.576c0 33.962667 27.562667 61.525333 61.525334 61.525333s61.525333-27.562667 61.525333-61.525333-27.562667-61.525333-61.525333-61.525334-61.525333 27.562667-61.525334 61.525334z m184.576-102.570667c0 33.962667 27.562667 61.525333 61.525334 61.525333s61.525333-27.562667 61.525333-61.525333-27.562667-61.525333-61.525333-61.525333-61.525333 27.562667-61.525334 61.525333z m205.141334 51.285333c0 33.962667 27.562667 61.525333 61.525333 61.525334s61.525333-27.562667 61.525333-61.525334-27.562667-61.525333-61.525333-61.525333-61.525333 27.562667-61.525333 61.525333z m102.570666 164.096c0 33.962667 27.562667 61.525333 61.525334 61.525334s61.525333-27.562667 61.525333-61.525334-27.562667-61.525333-61.525333-61.525333-61.525333 27.562667-61.525334 61.525333z m0 0',
              pathId: '5393'
            },
            show:this.calendarLife
          },
          {
            id: 5,
            title: "连线",
            svgObj: {
              t: '1716880286112',
              pId: "4259",
              pathT: 'M1024.00324 511.1c0.5 71.1-57 128.9-128 128.9-55.7 0-103.1-35.6-120.7-85.3-2.3-6.4-8.3-10.7-15.1-10.7H263.80324c-6.8 0-12.8 4.3-15.1 10.7-17.6 49.7-65 85.3-120.7 85.3C57.00324 640-0.49676 582.2 0.00324 511.1 0.50324 440.6 59.90324 382.7 130.40324 384c54.7 1 101.1 36.4 118.4 85.4 2.2 6.4 8.3 10.6 15 10.6h496.4c6.7 0 12.8-4.2 15-10.6 17.3-49.1 63.6-84.4 118.4-85.4 70.5-1.3 129.9 56.6 130.4 127.1z',
              pathId: '3884'
            },
            show:this.calendarLife
          },

          {
            id: 7,
            title: "用户模板",
            svgObj: {
              t: '1721979531527',
              pId: "5391",
              pathT: 'M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z m5.2-253.6c0 23.1-5.9 40.8-17.6 53.1s-27.7 18.4-47.8 18.4c-20.1 0-35.7-6.1-46.8-18.4s-16.7-30-16.7-53.1V411.7c0-22.7 5.6-40.1 16.7-52.2s26.7-18.1 46.8-18.1c20.1 0 36.1 6 47.8 18.1s17.6 29.5 17.6 52.2v158.7z m-45.1-253.3c-23.1 0-41.6-18.5-41.6-41.6s18.5-41.6 41.6-41.6 41.6 18.5 41.6 41.6-18.5 41.6-41.6 41.6z',
              pathId: '5393'
            },
            show:true
          },
        ],
        xyTypeOptions: [
          {
            key: "value",
            value: "数值轴"
          },
          {
            key: "category",
            value: "类目轴"
          }
        ],
        xySymbolOptions: [
          {
            key: "emptyCircle",
            value: "空心圆"
          },
          {
            key: "circle",
            value: "实心圆"
          },
          {
            key: "rect",
            value: "方块"
          },
          {
            key: "roundRect",
            value: "圆角方块"
          },
          {
            key: "triangle",
            value: "三角形"
          },
          {
            key: "diamond",
            value: "菱形"
          },
          {
            key: "pin",
            value: "图钉"
          },
          {
            key: "arrow",
            value: "箭头"
          },
          {
            key: "none",
            value: "空"
          }
        ],
        lineTypeOptions: [
          {
            key: "solid",
            value: "实线"
          },
          {
            key: "dashed",
            value: "虚线"
          },
          {
            key: "dotted",
            value: "圆点线"
          }
        ],
        colorOptions:[
          {
            id:1,
            children:['#d87c7c','#919e8b','#d7ab82','#6e7074','#61a0a8','#efa18d','#787464','#cc7e63','#724e58','#4b565b']
          },
          {
            id:2,
            children:['#dd6b66','#759aa0','#e69d87','#8dc1a9','#ea7e53','#eedd78','#73a373','#73b9bc','#7289ab','#91ca8c']
          },
          {
            id:3,
            children:['#c1232b','#27727b','#fcce10','#e87c25','#b5c334','#fe8463','#9bca63','#fad860','#f3a43b','#60c0dd']
          },
          {
            id:4,
            children:['#2ec7c9','#b6a2de','#5ab1ef','#ffb980','#d87a80','#8d98b3','#e5cf0d','#97b552','#95706d','#dc69aa']
          },
          {
            id:5,
            children:['#e01f54','#001852','#f5e8c8','#b8d2c7','#c6b38e','#a4d8c2','#f3d999','#d3758f','#dcc392','#2e4783']
          },
          {
            id:6,
            children:['#fc97af','#87f7cf','#f7f494','#72ccff','#f7c5a0','#d4a4eb','#d2f5a6','#76f2f2']
          },
         
          {
            id:7,
            children:['#c12e34','#e6b600','#0098d9','#2b821d','#005eaa','#339ca8','#cda819','#32a487']
          },
          {
            id:8,
            children:['#418ab3','#a6b727','#f69200','#838383','#fec306','#df5327']
          },
          {
            id:9,
            children:['#516b91','#59c4e6','#edafda','#93b7e3','#a5e7f0','#cbb0e3']
          },
          {
            id:10,
            children:['#893448','#d95850','#eb8146','#ffb248','#f2d643','#ebdba4']
          },
          {
            id:11,
            children:['#4ea397','#22c3aa','#7bd9a5','#d0648a','#f58db2','#f2b3c9']
          },
          {
            id:12,
            children:['#3fb1e3','#6be6c1','#626c91','#a0a7e6','#c4ebad','#96dee8']
          },
         
          {
            id:13,
            children:['#8a7ca8','#e098c7','#8fd3e8','#71669e','#cc70af','#7cb4cc']
          },

          
        ],
        noProperty:['chartTitle','XTitle','YTitle','YTitle2','legendRevealList','legendSort','checkData','legendData'],
        propertyNameList :{
          'titleTop':{
            label:'主标题距顶部距离',
            description:'主标题距外框上侧的距离'
          },
          'yTitleLetf':{
            label:'y轴左侧标题距左边距离',
            description:'左侧坐标轴名称与左侧轴线之间的距离'
          },
          'yTitleRight':{
            label:'y轴右侧标题距右边距离',
            description:'右侧坐标轴名称与右侧轴线之间的距离'
          },
          'legendBgColor':{
            label:'图例背景颜色',
            description:'图例列表块背景色'
          },
          'legendBgColor':{
            label:'图例朝向',
            description:'图例列表的布局朝向'
          },
          'legendTop':{
            label:'图例距顶部距离',
            description:'图例组件离外框上侧的距离。'
          },
          'legendLeft':{
            label:'图例距左边距离',
            description:'图例列表离外框上左的距离。'
          },
          'legendRight':{
            label:'图例距右边距离',
            description:'图例列表与外框右侧之间的距离'
          },
          'legendWidth':{
            label:'图例项宽度',
            description:'单个图例项的宽度'
          },
          'legendHeight':{
            label:'图例项高度',
            description:'单个图例项的高度'
          },
          'legendGap':{
            label:'图例项间隔',
            description:'单个图例项的高度'
          },
          'legendGap':{
            label:'图例项间隔',
            description:'单个图例项的高度'
          },
          'connectNulls':{
            label:'连接空值',
            description:'所有线是否连接空数据'
          },
          'symbol':{
            label:'折点类型',
            description:'所有线标记的图形'
          },
          'symbolSize':{
            label:'折点大小',
            description:'所有线标记的图形大小'
          },
          'itemColor':{
            label:'折点颜色',
            description:'所有线标记的图形颜色'
          },
           'lineType':{
            label:'折线类型',
            description:'所有线的类型'
          },
          'lineWidth':{
            label:'折线宽度',
            description:'所有线的宽度'
          },
          'lineColor':{
            label:'折线颜色',
            description:'所有线的颜色'
          },
          'xType':{
            label:'X轴类型',
            description:'X轴类型'
          },
          'xMin':{
            label:'X轴最小值',
            description:'X轴刻度最小值。'
          },
          'xMax':{
            label:'X轴最大值',
            description:'X轴刻度最大值。'
          },
          'yType':{
            label:'左侧Y轴类型',
            description:'左侧Y轴类型'
          },
          'yMin':{
            label:'左侧Y轴最小值',
            description:'左侧Y轴刻度最小值。'
          },
          'yMax':{
            label:'左侧Y轴最大值',
            description:'左侧Y轴刻度最大值。'
          },
          'yType2':{
            label:'右侧Y轴类型',
            description:'右侧Y轴类型'
          },
          'yMin2':{
            label:'右侧Y轴最小值',
            description:'右侧Y轴刻度最小值。'
          },
          'yMax2':{
            label:'右侧Y轴最大值',
            description:'右侧Y轴刻度最大值。'
          },
          'gridTop':{
            label:'距顶部距离',
            description:'图表距离外框顶部的距离'
          },
          'gridLeft':{
            label:'距左边距离',
            description:'图表距离外框左侧的距离'
          },
          'gridRight':{
            label:'距右边距离',
            description:'图表距离外框右侧的距离'
          },
          'gridBottom':{
            label:'距底部距离',
            description:'图表距离外框底部的距离'
          },

        }
      }
      

    },
    
    computed:{
      ...mapGetters(['userInfo']),
      legendTabHtmlList(){
        return [
          {
            label: '背景颜色',
            dataKey: 'legendBgColor',
            inputType: 'inputColor',
            show: this.showObj.isShowLegend 
          },
          {
            label: '图例朝向',
            dataKey: 'legendOrient',
            inputType: 'orientRadio',
            show: this.showObj.isShowLegend 
          },
          // 图例压缩、图例名称、图例字号 日历寿命独有
          {
            label: '图例压缩',
            dataKey: 'legendZip',
            inputType: 'radio',
            show: this.calendarLife && this.showObj.isShowLegend  
          },
          {
            label: '图例名称',
            dataKey: 'legendNameType',
            inputType: 'LegendNameRadio',
            show: (this.calendarLife || this.legendNameTypeShow) &&  this.showObj.isShowLegend  
          },
          {
            label: '图例字号',
            dataKey: 'legendFontSize',
            inputType: 'inputNumber',
            show: this.calendarLife &&  this.showObj.isShowLegend  
          },
          {
            label: '距顶部距离',
            dataKey: 'legendTop',
            inputType: 'inputNumber',
            show: this.showObj.isShowLegend  
          },
          {
            label: '距左边距离',
            dataKey: 'legendLeft',
            inputType: 'inputNumber',
            show: this.showObj.isShowLegend  && this.isLegendLeft
          },
          {
            label: '距右边距离',
            dataKey: 'legendRight',
            inputType: 'inputNumber',
            show: this.showObj.isShowLegend  && !this.isLegendLeft
          },
          {
            label: '图例项宽度',
            dataKey: 'legendWidth',
            inputType: 'inputNumber',
            show: this.showObj.isShowLegend && this.formData.legendZip === 'false'  
          },
          {
            label: '图例项高度',
            dataKey: 'legendHeight',
            inputType: 'inputNumber',
            show: this.showObj.isShowLegend && this.formData.legendZip === 'false'  
          },
          {
            label: '图例项间隔',
            dataKey: 'legendGap',
            inputType: 'inputNumber',
            show: this.showObj.isShowLegend && this.formData.legendZip === 'false'  
          },

        ]
      },
      dataTabHtmlList(){
        return [
          {
            label: '连接空值',
            dataKey: 'connectNulls',
            inputType: 'radio',
            show: this.showObj.isShowTitle 
          },
          {
            label: '折点类型',
            dataKey: 'symbol',
            inputType: 'select',
            selectOption:this.xySymbolOptions,
            show: this.showObj.isShowAllDataType 
          },
          {
            label: '折点大小',
            dataKey: 'symbolSize',
            inputType: 'inputNumber',
            show: this.showObj.isShowAllDataType 
          },
          {
            label: '折点颜色',
            dataKey: 'itemColor',
            inputType: 'inputColor',
            show: this.showObj.isShowAllDataType 
          },
          {
            label: '折线类型',
            dataKey: 'lineType',
            inputType: 'select',
            selectOption:this.lineTypeOptions,
            show: this.showObj.isShowAllDataType 
          },
          {
            label: '折线宽度',
            dataKey: 'lineWidth',
            inputType: 'inputNumber',
            show: this.showObj.isShowAllDataType 
          },
          {
            label: '折线颜色',
            dataKey: 'lineColor',
            inputType: 'inputColor',
            show: this.showObj.isShowAllDataType 
          },


        ]
      },
      singleDataTabHtmlList(){
        return [
          // {
          //   label: '同步',
          //   dataKey: 'synchronization',
          //   inputType: 'synchronizationBtn'
          // },
          {
            label: '最大值',
            dataKey: 'maxPoint',
            inputType: 'switch'
          },
          {
            label: '最小值',
            dataKey: 'minPoint',
            inputType: 'switch'
          },
          ...this.dataTabHtmlList
        ]
      },
      titleTabHtmlList() {
        return [
          {
            label: '图表标题',
            dataKey: 'chartTitle',
            inputType: 'input',
            show: this.showObj.isShowTitle
          },
          {
            label: 'X轴标题',
            dataKey: 'XTitle',
            inputType: 'input',
            show: this.showObj.isShowTitle
          },
          {
            label: 'Y轴标题',
            dataKey: 'YTitle',
            inputType: 'input',
            show: this.showObj.isShowTitle && this.formData.YTitle !== null && this.formData.YTitle !== undefined
          },
          {
            label: 'Y轴副标题',
            dataKey: 'YTitle2',
            inputType: 'input',
            show: this.showObj.isShowTitle && this.formData.YTitle2 !== null && this.formData.YTitle2 !== undefined
          },
          {
            label: '距顶部距离',
            dataKey: 'titleTop',
            inputType: 'inputNumber',
            show: this.showObj.isShowTitle
          },
          {
            label: '距左边距离',
            dataKey: 'yTitleLetf',
            inputType: 'inputNumber',
            show: this.showObj.isShowTitle
          },
          {
            label: '距右边距离',
            dataKey: 'yTitleRight',
            inputType: 'inputNumber',
            show: this.showObj.isShowTitle && this.formData.YTitle2 !== null && this.formData.YTitle2 !== undefined
          }
        ];
      },
      axisTabHtmlList(){
        return [
          [
            {
              label: '类型',
              dataKey: 'xType',
              inputType: 'select',
              selectOption:this.xyTypeOptions,
              show: this.showObj.isShowX
            },
            {
              label: '最小值',
              dataKey: 'xMin',
              inputType: 'inputNumber',
              show: this.showObj.isShowX && this.formData.xType === 'value'
            },
            {
              label: '最大值',
              dataKey: 'xMax',
              inputType: 'inputNumber',
              show: this.showObj.isShowX && this.formData.xType === 'value'
            },
            {
              label: '间隔值',
              dataKey: 'xInterval',
              inputType: 'inputNumber',
              show: this.showObj.isShowX && this.formData.xType === 'value'
            },
          ],
          [
            {
                label: '类型',
                dataKey: 'yType',
                inputType: 'select',
                selectOption:this.xyTypeOptions,
                show: this.showObj.isShowY
              },
              {
                label: '最小值',
                dataKey: 'yMin',
                inputType: 'inputNumber',
                show: this.showObj.isShowY && this.formData.yType === 'value'
              },
              {
                label: '最大值',
                dataKey: 'yMax',
                inputType: 'inputNumber',
                show: this.showObj.isShowY && this.formData.yType === 'value'
              },
              {
                label: '间隔值',
                dataKey: 'yInterval',
                inputType: 'inputNumber',
                show: this.showObj.isShowY && this.formData.yType === 'value'
              },
              // 小数位数 日历寿命独有的
              {
                label: '小数位数',
                dataKey: 'yDecimalNum',
                inputType: 'inputNumber',
                show: this.calendarLife && this.showObj.isShowY && this.formData.yType === 'value'
              },
            ],
            [
            {
                label: '类型',
                dataKey: 'yType2',
                inputType: 'select',
                selectOption:this.xyTypeOptions,
                show: this.showObj.isShowY2
              },
              {
                label: '最小值',
                dataKey: 'yMin2',
                inputType: 'inputNumber',
                show: this.showObj.isShowY2 && this.formData.yType2 === 'value'
              },
              {
                label: '最大值',
                dataKey: 'yMax2',
                inputType: 'inputNumber',
                show: this.showObj.isShowY2 && this.formData.yType2 === 'value'
              },
              {
                label: '间隔值',
                dataKey: 'yInterval2',
                inputType: 'inputNumber',
                show: this.showObj.isShowY2 && this.formData.yType2 === 'value'
              }
            ]
        ]
      },
      gridTabHtmlList() {
        return [
          {
            label: '距顶部距离',
            dataKey: 'gridTop',
            inputType: 'inputNumber',
            show: this.showObj.isShowGrid
          },
          {
            label: '距左边距离',
            dataKey: 'gridLeft',
            inputType: 'inputNumber',
            show: this.showObj.isShowGrid
          },
          {
            label: '距右边距离',
            dataKey: 'gridRight',
            inputType: 'inputNumber',
            show: this.showObj.isShowGrid
          },
          {
            label: '距底部距离',
            dataKey: 'gridBottom',
            inputType: 'inputNumber',
            show: this.showObj.isShowGrid
          },
        ];
      },

      htmlList(){
        return [
            {
              iconDirection:'isShowX',
              formName:'横坐标坐标轴',
              displayCondition:this.isShowContent === 2,
              options:this.axisTabHtmlList[0]
            },
            {
              iconDirection:'isShowY',
              formName:'纵坐标坐标轴',
              displayCondition:this.isShowContent === 2,
              options:this.axisTabHtmlList[1]
            },
            {
              iconDirection:'isShowY2',
              formName:'纵坐标副坐标轴',
              displayCondition:this.isShowContent === 2  && this.formData.yType2,
              options:this.axisTabHtmlList[2]
            },
            {
              iconDirection:'isShowTitle',
              formName:'标题',
              displayCondition:this.isShowContent === 3,
              options:this.titleTabHtmlList
            },
            {
              iconDirection:'isShowGrid',
              formName:'图表位置',
              displayCondition:this.isShowContent === 4,
              options:this.gridTabHtmlList
            },
        ]
      },
    },
    watch: {
      checkObj: {
        handler(newVal, oldVal) {
          switch (newVal.editObj) {
            case "title":
              this.isShowContent = 3
              break;
            case "legend":
              this.isShowContent = 0
              break;
            case "tag":
              this.isShowContent = 1
              this.showObj["isShowTag" + newVal.tag] = true

              this.$nextTick(() => {
                // 存储最开始的原始值
                if (JSON.stringify(this.rollObj) == '{}') {
                  this.dataOptions.forEach((v, index) => {
                    this.rollObj['tag' + index] = document.getElementById('tag' + index).getBoundingClientRect().top
                  })
                }
                // 目前的位置
                const tagTop = document.getElementById('tag' + newVal.tag).getBoundingClientRect().top
                // 与原始值的滚动范围
                const changeTop = this.rollObj['tag' + newVal.tag] - tagTop
                // 设置最后的位置
                document.getElementsByClassName('form-wrapper')[0].scrollTop = tagTop + changeTop - 55 - 24 - 55 - 40
              })

              this.$forceUpdate()
              break;
            case "axis":
              this.isShowContent = 2
              break;
            case "position":
              this.isShowContent = 4
              break;
          }
        },
        immediate: true,
        deep: true // 开启深度监听，默认是false
      },
      screenImageId:{
        handler(newVal) {
          this.screenImage = '/api/sysFileInfo/preview?Authorization=Bearer ' + Vue.ls.get("Access-Token") + '&id=' + this.screenImageId + "#toolbar=0"

          this.screenImageLoading = false
        }

      }


    },
    created() {
      this.getChartTemplateList()
      this.init()
      this.checkAll = this.editData.checkAll ?? true
      this.legendIndeterminate = this.editData.legendIndeterminate ?? false

      this.legendSortList = this.editData.legendSort
      this.legendList = this.editData.legend
      this.legendRevealList = this.editData.legendRevealList

      if(this.legendRevealList){
        this.legendRevealcheckAll = this.editData.legendRevealcheckAll ?? (this.legendRevealList.length === this.legendOptions.length)
        this.legendRevealIndeterminate = this.editData.legendRevealIndeterminate ?? false
        this.legendRevealOptions = this.editData.legendRevealOptions ??  this.legendOptions.map(mapItem => {
          {return { label:mapItem,value:mapItem,disabled: this.legendList.includes(mapItem) ? false : true }}
        })
      }

      this.dataOptions = this.data.map(v => v.data)
      this.data.forEach((v, index) => {
        // const editNameList = this.legendEditNameList.filter(item =>{
        //   const idLength = item.id.split('').length 
        //   const newId =  v.id.slice(0,idLength)
        //   return this.legendEditName === 'sampleCode' ? item.id === newId : item.id === v.soc
        // })

        this.checkData.push({
          name: this.editData.series[index].name,
          dataName: this.editData.series[index].dataName,
          id: v.id,
          index:v.index,
          soc: v.soc,
          // disabled: this.legendList.includes(v.soc) || this.legendList.includes(editNameList[0].newName) ? false : true,
          disabled: false,
          maxPoint: v.maxPoint,
          minPoint: v.minPoint,
          connectNulls: v.connectNulls ? '1' : '0',
          symbol: v.symbol,
          symbolSize: v.symbolSize,
          itemColor: v.itemColor,
          lineType: v.lineType,
          lineWidth: v.lineWidth,
          lineColor: v.lineColor,
          synchronization: v.synchronization
        })

        this.showObj["isShowTag" + index] = false  //展示大标签
        this.showObj["isShowData" + index] = false //展示大标签里面的数据

      })
    },
    methods: {
      init() {
        Object.keys(this.formData).forEach(item => {

          if(item === 'legendZip') this.formData[item] = this.editData[item] || 'false'
          if(item === 'allData' && this.editData.allData){
            Object.keys(this.editData.allData).forEach(cItem => {
              this.formData.allData[cItem] = this.editData.allData[cItem]
            })
          }
          if(item !== 'legendZip' && item !== 'allData') this.formData[item] = this.editData[item]

          // 如果图例背景色是有颜色的
          if(item === 'legendBgColor' && this.editData.legendBgColor !== 'none'){
            this.showObj.isShowLegendBgColor = true
          }  
        })

        // 图例改名,每次修改后都获取新的值，因为父组件需要进行操作
        this.legendEditNameList = this.editData.legendEditName
      },

      // 获取该用户的用户模板列表
      getChartTemplateList(){
        getChartTemplateList(Number(this.userInfo.account)).then(res => {
          this.userAllTemplateList = res.data
          this.getTemplateList()
        })
      },
      getTemplateList(){
        this.userTemplateList = []
        const list = structuredClone(this.userAllTemplateList)

        list.forEach(item => {
          if(item.templateName.indexOf(this.UserTemplateSearchParam.templateName) !== -1){
            this.userTemplateList.push(item)
          }
        })
        

      },
      saveChartTemplate(params){
        saveChartTemplate(params).then(res => {
          if(res.success){
            this.$message.info('模板添加成功')
            this.getChartTemplateList()
            this.isSaveTemplateModalVisible = false
            this.isSaveTemplate = false
          }
        })
      },

      chooseChartTemplate(value){
        this.$confirm({
          title: '提示',
          content: '确定要选择该图表模板吗 ?',
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            let chartTemplateParams = {}
            const current = JSON.parse(value.templateParamJson)
            current.checkData = []
            current.legendData = {}

            for(let i = 0 ;i < this.original.series.length;i++){
              const currentContent = this.original.series[i]
              current.checkData.push({
                name: currentContent.name,
                dataName: currentContent.dataName,
                id: currentContent.id,
                index:currentContent.index,
                soc: currentContent.soc,
                disabled: false,
                maxPoint: currentContent.maxPoint,
                minPoint: currentContent.minPoint,
                connectNulls: currentContent.connectNulls ? '1' : '0',

                symbol: current.allData.symbol ?? currentContent.symbol,
                symbolSize:current.allData.symbolSize ??  currentContent.symbolSize,
                itemColor:current.allData.itemColor ??  currentContent.itemColor,
                lineType:current.allData.lineType ??  currentContent.lineType,
                lineWidth:current.allData.lineWidth ??  currentContent.lineWidth,
                lineColor:current.allData.lineColor ??  currentContent.lineColor,

                synchronization:currentContent.synchronization
              })
            }
            // 修改
            if(this.templateParam.templateId){
              chartTemplateParams = {
                id:this.templateParam.templateId,
                templateParamJson:JSON.stringify(current)
              }
              updateChartTemplate(chartTemplateParams).then(res => {
                if(res.success){
                  this.$message.info('模板选择成功')
                  this.$emit('changeTemplate',this.templateParam.targetChart)
                }
              })
            }else{
              chartTemplateParams = {
                reportId:this.templateParam.reportId,
                targetChart:this.templateParam.targetChart,
                templateName:value.templateName,
                originalParamJson:JSON.stringify(this.original),
                templateParamJson:JSON.stringify(current),
              }
              if(this.templateParam.optionKey !== undefined) chartTemplateParams.optionKey = this.templateParam.optionKey
              saveChartTemplate(chartTemplateParams).then(res => {
                if(res.success){
                  this.$message.info('模板选择成功')
                  this.$emit('changeTemplate',this.templateParam.targetChart)
                }
              })
            }
          },
          onCancel () {
          }
        })
      },

      chartTemplateDetail(value){
        this.isSaveTemplateModalVisible = true
        this.currentTemplate = value
        this.screenImage = '/api/sysFileInfo/preview?Authorization=Bearer ' + Vue.ls.get("Access-Token") + '&id=' + value.templateImage + "#toolbar=0"
      },

      shareChartTemplate(value){
        this.isShare = true
        this.currentTemplate = value
      },
      
      deleteChartTemplate(){
      if(this.userSelectedRows.length === 0) return this.$message.info('请先选择需要删除的数据')
      this.$confirm({
        title: '提示',
        content: '确定要删除这些图表模板吗 ?',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {

           Promise.all(
            this.userSelectedRows.map(row => deleteChartTemplate(row.id))
           ).then(() => {
            this.$message.success('模板删除成功')
            this.getChartTemplateList()
          }).catch(e => {
            this.$message.warning('模板删除失败，请联系系统管理员')
          });
        },
        onCancel () {
        }
      })
      },

      // 切换图例名称事件 // 样品编号 电芯编号 //目前只有日历寿命测试报告才有
      handleToggleLegendName(e){
        
        this.legendSortList.forEach((v,index) => { // 修改图例排序数组
          const temLegendSortIndex = this.LegendNameTypeList.findIndex(findItem => v == findItem.sampleCode || v == findItem.batteryCode)
          this.legendSortList[index] = this.LegendNameTypeList[temLegendSortIndex][this.formData.legendNameType]
        })
        
        this.legendEditNameList.forEach((v,index) => { //修改图例名称数组
          const temLegendEditNameIndex = this.LegendNameTypeList.findIndex(findItem => v.originName == findItem.sampleCode || v.originName == findItem.batteryCode)
          this.legendEditNameList[index].originName = this.LegendNameTypeList[temLegendEditNameIndex][this.formData.legendNameType]
        })

        this.legendList.forEach((v,index) => {  //修改图例数组
          const temlegendEditIndex = this.LegendNameTypeList.findIndex(findItem => v == findItem.sampleCode || v == findItem.batteryCode)
          if(temlegendEditIndex !== -1) this.legendList[index] = this.LegendNameTypeList[temlegendEditIndex][this.formData.legendNameType]
        })

        this.legendRevealList.forEach((v,index) => {  //修改图例名称数组
          const temlegendRevealIndex = this.LegendNameTypeList.findIndex(findItem => v == findItem.sampleCode || v == findItem.batteryCode)
          if(temlegendRevealIndex !== -1) this.legendRevealList[index] = this.LegendNameTypeList[temlegendRevealIndex][this.formData.legendNameType]
        })

        this.checkData.forEach((v,index) => {
          const temCheckDataIndex = this.LegendNameTypeList.findIndex(findItem => v.soc == findItem.sampleCode || v.soc == findItem.batteryCode)
          this.checkData[index].soc = this.LegendNameTypeList[temCheckDataIndex][this.formData.legendNameType]

          // 如果name不等于上一个类型的值，说明他被改名字了，不更改他的值
          this.checkData[index].name = this.checkData[index].name !==  this.LegendNameTypeList[temCheckDataIndex][this.formData.legendNameType == 'sampleCode' ? 'batteryCode' : 'sampleCode'] ? this.checkData[index].name : this.LegendNameTypeList[temCheckDataIndex][this.formData.legendNameType]
        })

        this.legendRevealOptions.forEach((v,index) => {
          const temlegendRevealIndex2 = this.LegendNameTypeList.findIndex(findItem => v.value == findItem.sampleCode || v.value == findItem.batteryCode)
          this.legendRevealOptions[index].value = this.LegendNameTypeList[temlegendRevealIndex2][this.formData.legendNameType]
          this.legendRevealOptions[index].label = this.LegendNameTypeList[temlegendRevealIndex2][this.formData.legendNameType]
        })

        this.handleSubmit('','','','legendNameType')
        this.$forceUpdate()
      },

      // 图例压缩事件 //目前只有日历寿命测试报告才有
      handleLegendZip(){
        if(this.formData.legendZip === 'true'){
          this.formData.legendWidth = 3
          this.formData.legendHeight = 1
          this.formData.legendGap = 1

          this.legendList = _.cloneDeep(this.legendOptions) 
        }else{
          this.formData.legendWidth = this.original.legendWidth
          this.formData.legendHeight = this.original.legendHeight
          this.formData.legendGap = this.original.legendGap

          this.legendList = _.cloneDeep(this.legendOptions).slice(0,10)
        }
        this.legendRevealList = _.cloneDeep(this.legendOptions)
        this.legendRevealOptions.forEach((v,index) => {
          v.disabled = index >= 10
        })
      
        this.handleSubmit('','','','legendZip')
      },
      
      /* 图例-数据全选 */
      onLegendAllChange(e) {
        Object.assign(this, {
          legendList: e.target.checked ? this.legendOptions : [],
          legendIndeterminate: false,
          checkAll: e.target.checked
        })
        // 图表标签的显隐
        this.checkData.forEach(v => {
          v.disabled = !e.target.checked
        })

        this.legendRevealOptions.forEach(v => {
          v.disabled = !e.target.checked
        })

        this.handleSubmit('','','','legendList')
      },
      /* 图例-数据单条选择 */
      // 通过 checkData 的 disabled 以及 this.legendList 是否包含这个数据 来判断是否显示
      onLegendChange(checkedList) {
        this.legendIndeterminate = !!this.legendList.length && this.legendList.length < this.legendOptions.length
        this.checkAll = this.legendList.length === this.legendOptions.length

        const newCheckedList = []
        checkedList.forEach(v => {
          // 判断这条数据有没有改名字
          const newName = this.legendEditNameList.filter(filterItem => filterItem.originName === v && filterItem.newName)
          newCheckedList.push(newName.length === 0 ? v : newName[0].newName)
        })

        this.checkData.forEach(v => {
          const editNameList = this.legendEditNameList.filter(item =>{
            const idLength = item.id.split('').length 
            const newId =  v.id.slice(0,idLength)
            return item.id === newId
          })
          const newName = editNameList[0].newName === '' ? editNameList[0].originName : editNameList[0].newName
          v.disabled = !newCheckedList.includes(newName)
        })

        this.legendRevealOptions.forEach(v => {
          if(checkedList.includes(v.value)){
            v.disabled = false
          }else{
            const have = this.legendEditNameList.find(findItem => findItem.originName == v.value).newName
            v.disabled = !checkedList.includes(have)
          }
        })
        this.handleSubmit('','','','legendList')
      },
       // 图例修改名字
       handleEditlegendName({ target }, index) {
        
        // target._value  输入的值--图例的名称修改值
        // index 改的第几个图例

        // 误触判断
        if(!target._value && !this.legendEditNameList[index].newName && !this.legendEditNameList[index].previousName){
          return
        }

        // 如果有值,记录修改值
        if (target._value) {
          if(!this.legendList.includes(target._value)) this.legendList.push(target._value)

          this.checkData.forEach(v => {
            if (v.name === this.legendEditNameList[index].originName || v.name === this.legendEditNameList[index].previousName) {
              v.name = target._value
            }
          })

          this.legendEditNameList[index].previousName = target._value

        } else {
          // 如果没有值,说明用户进行清空操作，需判断是否为误判（通过previousName是否有值进行判断）(传到父组件时，父组件进行一个还原操作，此时需要将previousName置空)，
          this.legendEditNameList[index].isReset = this.legendEditNameList[index].previousName ? true : false

          this.checkData.forEach(v => {
            if (v.name === this.legendEditNameList[index].previousName) {
              v.name = this.legendEditNameList[index].originName
            }
          })

          const haveIndex = this.legendList.findIndex(findItem => findItem === this.legendEditNameList[index].previousName)
          if(haveIndex > -1) this.legendList.splice(haveIndex,1)
          if(!this.legendList.includes(this.legendEditNameList[index].originName)) this.legendList.push(this.legendEditNameList[index].originName)

        }
        this.handleSubmit('','','','legendEditName',index)
      },
      

      /* 图例显隐全选事件 */
      onLegendRevealAllChange(e) {
        
        Object.assign(this, {
          legendRevealList: e.target.checked ? this.legendOptions : [],
          legendRevealIndeterminate: false,
          legendRevealcheckAll: e.target.checked
        })

        // 增加改名称的
        const have = this.legendEditNameList.filter(filterItem => filterItem.newName)
        
        if(e.target.checked && have.length > 0){
          have.forEach(forItem => {
            this.legendRevealList.push(forItem.originName)
          })
        }
        this.handleSubmit('','','','legendRevealList')
      },
      /* 图例显隐选择事件 */
      onLegendRevealChange(checkedList) {
        this.legendRevealIndeterminate = !!this.legendRevealList.length && this.legendRevealList.length < this.legendOptions.length
        this.legendRevealcheckAll = this.legendRevealList.length === this.legendOptions.length

        this.handleSubmit('','','','legendRevealList')
      },
      // 数据移动
      moveDataOne(index, action) {

        const m = action === 'up' ? index : index + 1
        const n = action === 'up' ? index - 1 : index

        const tmp1 = this.legendSortList[m]
        this.legendSortList[m] = this.legendSortList[n]
        this.legendSortList[n] = tmp1

        this.$forceUpdate()

        this.handleSubmit('','','','legendSort')
      },
      handleChangeColor(e, index, target) {
        this.checkData[index][target] = e.target.value

        this.handleSubmit('','','',target,index)
      },
      handleChangeLegendColor(e){
        this.formData.legendBgColor = e.target.value
        this.handleSubmit('','','','legendBgColor')
      },
      // 全部修改，颜色修改
      handleAllDataColor(e,target){
        this.formData.allData[target] = e.target.value
        this.handleEditAllData(target)
      },
      handleChangePoint(e, index, target) {
        this.checkData[index][target] = e
        this.handleSubmit('','','',target,index)
      },
      handleChangeValue(target, val) {
        this[target] = val
      },
      handleShow(target) {
        this.showObj[target] = !this.showObj[target]
        this.$forceUpdate()
      },
     // 默认颜色方案
      handleColorOption(colorList){

        this.checkData.forEach((v,index) => {
          v.itemColor = colorList[index % colorList.length]
          v.lineColor = colorList[index % colorList.length]
        })
        this.handleSubmit('','','','colorOption')
      },

     // 图表标签--全部修改
      handleEditAllData(targetEdit,isRest = 0){
        // targetEdit:编辑对象
        this.checkData.forEach((v,index) => {
          v[targetEdit] = isRest ? this.original.checkData ? this.original.checkData[index][targetEdit] : this.original.series[index][targetEdit]   : this.formData.allData[targetEdit]
        })

        if(isRest) this.formData.allData[targetEdit] = ''

        this.handleSubmit('','','',targetEdit,isRest ?'allReset' : 'all')
      },
      // 图标标签--同步
      handleSynchronization(original) {
        if (original === this.checkData[original].synchronization) return
        for (let key in this.checkData[this.checkData[original].synchronization]) {
          const property = ['checkAll','checkedList','indeterminate','id','name','soc','disabled','synchronization','index']
          if (property.includes(key)) continue

          this.checkData[original][key] = this.checkData[this.checkData[original].synchronization][key]
        }

        this.handleSubmit('','','','synchronization',original)
      },
       /*生成*/
      handleSubmit(resetObj, resetValue, resetIndex,editObj = '',editIndex = '') {
        // resetObj : 需要重置的对象
        // resetValue : 重置的数值
        // resetIndex : 需要重置的那条线

        // editObj : 需要修改的对象
        // editIndex : 需要修改的数据标签的位置

        // 颜色点击重置
        if(resetObj == "legendBgColor"){
          this.showObj.isShowLegendBgColor = false
        }

        const judgmentList1 = ['chartTitle','XTitle','YTitle','YTitle2','xType','yType','yType2']
        if(judgmentList1.includes(resetObj)){
          this.formData[resetObj] = resetValue

          if(resetObj == "xType" && resetValue == 'value'){
            this.formData.xMin = this.original.xMin
            this.formData.xMax = this.original.xMax
            this.formData.xInterval = this.original.xInterval
          }

          if(resetObj == "yType" && resetValue == 'value'){
            this.formData.yMin = this.original.yMin
            this.formData.yMax = this.original.yMax
            this.formData.yInterval = this.original.yInterval
            this.formData.yDecimalNum = this.original.yDecimalNum
          }

          if(resetObj == "yType2" && resetValue == 'value'){
            this.formData.yMin2 = this.original.yMin2
            this.formData.yMax2 = this.original.yMax2
            this.formData.yInterval2 = this.original.yInterval2
          }
        }

        const judgmentList2 = ['symbol','symbolSize','itemColor','lineType','lineColor','lineWidth']
        if (judgmentList2.includes(resetObj)) {
          this.checkData[resetIndex][resetObj] = resetValue
        }


        const params = {
          chartTitle: this.formData.chartTitle,
          XTitle: this.formData.XTitle,
          YTitle: this.formData.YTitle,
          YTitle2: this.formData.YTitle2,
          titleTop: resetObj !== "titleTop" ? this.formData.titleTop : resetValue,
          yTitleLetf: resetObj !== "yTitleLetf" ? this.formData.yTitleLetf : resetValue,
          yTitleRight: resetObj !== "yTitleRight" ? this.formData.yTitleRight : resetValue,

          legendList: this.legendList,
          legendRevealList:this.legendRevealList,
          legendSort: this.legendSortList,
          legendEditName: this.legendEditNameList,
          legendBgColor: resetObj !== "legendBgColor" ? this.formData.legendBgColor : resetValue,
          legendOrient: resetObj !== "legendOrient" ? this.formData.legendOrient : resetValue,
          legendZip: resetObj !== "legendZip" ? this.formData.legendZip : resetValue,
          legendNameType: resetObj !== "legendNameType" ? this.formData.legendNameType : resetValue,
          legendFontSize: resetObj !== "legendFontSize" ? this.formData.legendFontSize : resetValue,
          legendTop: resetObj !== "legendTop" ? this.formData.legendTop : resetValue,
          legendLeft: resetObj !== "legendLeft" ? this.formData.legendLeft : resetValue,
          legendRight: resetObj !== "legendRight" ? this.formData.legendRight : resetValue,
          legendWidth: resetObj !== "legendWidth" ? this.formData.legendWidth : resetValue,
          legendHeight: resetObj !== "legendHeight" ? this.formData.legendHeight : resetValue,
          legendGap: resetObj !== "legendGap" ? this.formData.legendGap : resetValue,

          xType: resetObj !== "xType" ? this.formData.xType : resetValue,
          xMin: this.formData.xType !== "category" ? (resetObj !== "xMin" ? this.formData.xMin : resetValue) : "",
          xMax: this.formData.xType !== "category" ? (resetObj !== "xMax" ? this.formData.xMax : resetValue) : "",
          xInterval: this.formData.xType !== "category" ? (resetObj !== "xInterval" ? this.formData.xInterval : resetValue) : "",

          yType: resetObj !== "yType" ? this.formData.yType : resetValue,
          yMin: this.formData.yType !== "category" ? (resetObj !== "yMin" ? this.formData.yMin : resetValue) : "",
          yMax: this.formData.yType !== "category" ? (resetObj !== "yMax" ? this.formData.yMax : resetValue) : "",
          yInterval: this.formData.yType !== "category" ? (resetObj !== "yInterval" ? this.formData.yInterval : resetValue) : "",
          yDecimalNum: this.formData.yType !== "category" ? (resetObj !== "yDecimalNum" ? this.formData.yDecimalNum : resetValue) : "",
          
          yType2: resetObj !== "yType2" ? this.formData.yType2 : resetValue,
          yMin2: this.formData.yType2 !== "category" ? (resetObj !== "yMin2" ? this.formData.yMin2 : resetValue) : "",
          yMax2: this.formData.yType2 !== "category" ? (resetObj !== "yMax2" ? this.formData.yMax2 : resetValue) : "",
          yInterval2: this.formData.yType2 !== "category" ? (resetObj !== "yInterval2" ? this.formData.yInterval2 : resetValue) : "",

          gridTop: resetObj !== "gridTop" ? this.formData.gridTop : resetValue,
          gridLeft: resetObj !== "gridLeft" ? this.formData.gridLeft : resetValue,
          gridRight: resetObj !== "gridRight" ? this.formData.gridRight : resetValue,
          gridBottom: resetObj !== "gridBottom" ? this.formData.gridBottom : resetValue,

          checkData: this.checkData.map(v => {
            return {
              name: v.name,
              id: v.id,
              index:v.index,
              soc: v.soc,
              data: v.checkedList,
              maxPoint: v.maxPoint,
              minPoint: v.minPoint,
              connectNulls:v.connectNulls,
              symbol: v.symbol,
              symbolSize: v.symbolSize,
              itemColor: v.itemColor,
              lineType: v.lineType,
              lineColor: v.lineColor,
              lineWidth: v.lineWidth,
              synchronization: v.synchronization
            }
          }),
        }

        if(editObj) params.targetEditObj = editObj
        if(editIndex !== '') params.targetEditIndex = editIndex
        if(resetObj) params.targetResetObj = resetObj
        if(resetIndex !== '') params.targetResetIndex = resetIndex

        // 修改图例数据--传递legendIndeterminate、checkAll
        if(editObj === 'legendList'){
          params.legendIndeterminate = this.legendIndeterminate
          params.checkAll = this.checkAll
        }

        // 修改图例显隐--传递legendRevealIndeterminate、legendRevealcheckAll、legendRevealOptions
        if(editObj === 'legendRevealList'){
          params.legendRevealIndeterminate = this.legendRevealIndeterminate
          params.legendRevealcheckAll = this.legendRevealcheckAll
          params.legendRevealOptions = this.legendRevealOptions
        }

        // 修改样品编号--传递legendRevealOptions
        if(editObj === 'legendNameType' || editObj === 'legendEditName'){
          params.legendRevealOptions = this.legendRevealOptions
        }

        if(editIndex == 'all') params.allData = this.formData.allData

        if(editIndex == 'allReset'){
          delete params.allData
          delete params.targetEditObj
          delete params.targetEditIndex
          params.targetResetObj = editObj
          params.targetResetIndex = 'all'
        } 
        this.$emit("submit",params)

        // 初始化
        this.init()
      },
      handleOk(){
        this.$message.success('保存成功！')
      },
      handleAllReset() {
        if(!this.templateParam.templateId) return this.$message.info('暂未对图表进行修改')
        this.$emit("reset")
      },

      // 用户模板相关方法
      handleSaveAsTemplate() {
        this.isSaveTemplateModalVisible = true
        this.isSaveTemplate = true

        this.screenImageLoading = true
        
        this.$emit('screenshot')

        setTimeout(() => this.screenImageLoading = false,5000)
      },

      // 另存为模板弹窗处理方法
      handleSaveTemplateCancel() {
        this.isSaveTemplateModalVisible = false
        this.isSaveTemplate = false
        this.saveTemplateForm.resetFields()
      },

      handleSaveTemplateOk() {
        if(!this.isSaveTemplate) return this.isSaveTemplateModalVisible = false
        this.saveTemplateForm.validateFields((err, values) => {
          if (!err) {
            this.templateSaving = true
            // 生成模板内容表格数据
            this.saveAsTemplateWithDetails({templateImage:this.screenImageId,...values})
          }
        })
      },

      async saveAsTemplateWithDetails(templateData) {
        try {
          // 获取当前表单的配置数据
          const currentConfig = JSON.parse(JSON.stringify(this.templateParam.templateParamJson))
          Object.keys(currentConfig).forEach(item => {
            if(this.noProperty.includes(item)){
              delete currentConfig[item]
            }
          })

          const templateParams = {
            userName:this.userInfo.name,
            userAccount:this.userInfo.account,
            templateName: templateData.templateName,
            templateImage:templateData.templateImage,
            templateParamJson: JSON.stringify(currentConfig),
            originalParamJson: JSON.stringify(this.templateParam.originalParamJson),
          }
        this.saveChartTemplate(templateParams)

        } catch (error) {
          this.$message.error('保存模板失败，请重试')
        } finally {
          this.templateSaving = false
        }
      },



      handleClose() {
        this.$emit("close")
      },
      onUserSelectChange(keys,rows){
        this.userSelectedRowKeys = keys
        this.userSelectedRows = rows
      },
      
    }
  }
</script>
<style lang="less" scoped>
  .navigation-wrapper {
    display: flex;
    padding-bottom: 10px;
  }

  /* 导航栏 */
  .navigation {
    width: 52px;
    height: 52px;
    border-radius: 10px;

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin-right: 10px;

    font-size: 12px;

  }

  .form-wrapper {
    /* max-height: calc(100vh - 55px - 24px - 55px - 40px); */
    max-height: calc(100vh - 55px - 24px - 55px - 40px - 10px - 10px - 10px);
    overflow-y: auto;
  }

  .form-block {
    display: flex;
    margin-left: -100px;
    align-items: center;
    font-size: 12px;
  }

  .form-block .chli-title {
    width: 80px;
    flex-shrink: 0;
  }

  .action-content{
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .sequence-item {
    display: flex;
    align-items: center;
  }

  .sequence-item .item-content {
    width: 100%;
    font-size: 12px;
  }

  .btn-wrapper {
    padding-top: 10px;
  }

  /* 颜色方案 */
  .color-content{
    display: flex;
    flex-wrap: wrap;
  }
  .color-row{
    display: flex;
    border-radius: 5px;
    border: 1px solid #eee;
    width: 250px;
    padding: 5px;
    margin-bottom: 5px;

    display: flex;
    justify-content: space-between;
    align-items: center;

    cursor: pointer;
  }
  .color-row .color-block{
    width: 20px;
    height: 20px;
    border-radius: 5px;
  }

  /* 通用 */

  .ml10 {
    margin-left: 10px;
  }

  /deep/.ant-drawer-body {
    padding: 24px 24px 10px;
  }

  /* form */
  /deep/ .ant-form label,
  /deep/ .ant-form-explain, .ant-form-extra{
    font-size: 12px;
  }

  /deep/.ant-form-item-label {
    text-align: left;
    overflow: visible;
  }



  /deep/ .ant-form-item {
    margin: 0;
  }

  /deep/ .ant-input{
    font-size: 12px;
  }

  /deep/.ant-input-number {
    width: 120px;
    font-size: 12px;
    height: 24px;
  }

  /deep/.ant-input-number-input{
    height: 24px;
  }

  /deep/.ant-select {
    width: 120px !important;
    font-size: 12px;
    height: 24px;
  }

  /deep/.ant-select-sm .ant-select-selection--single{
    height: 24px;
  }
  /deep/.ant-select-sm .ant-select-selection__rendered{
    line-height: 22px;
  }

  /deep/.ant-btn{
    font-size: 12px;
    padding: 0 8px;
  }

  /deep/.ant-btn-sm{
    font-size: 12px;
  }

  /deep/.btn-wrapper .ant-btn{
    height: 30px;
    line-height: 30px;
  }

  /* drawer */
  /deep/ .ant-drawer-left.ant-drawer-open,
  .ant-drawer-right.ant-drawer-open {
    width: 500px !important;
  }

  /deep/ .ant-drawer-mask {
    opacity: 0 !important;
  }

  /deep/.ant-radio-button-wrapper{
    height: 28px;
    line-height: 28px;
  }

  /* 用户模板样式 */
  .template-info-section {
    margin-bottom: 16px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
  }

  .template-current-info {
    display: flex;
    align-items: center;
    font-size: 14px;
  }

  .template-label {
    color: #666;
    margin-right: 8px;
  }

  .template-name {
    font-weight: 500;
    color: #1890ff;
  }

  .template-actions {
    margin-bottom: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .template-list-section {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    overflow: hidden;
  }

  .template-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #fafafa;
    border-bottom: 1px solid #d9d9d9;
    font-weight: 500;
  }

  .template-list {
    max-height: 300px;
    overflow-y: auto;
  }

  .template-empty {
    padding: 20px;
    text-align: center;
  }

  .template-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .template-item:hover {
    background-color: #f5f5f5;
  }

  .template-item:last-child {
    border-bottom: none;
  }

  .template-item-active {
    background-color: #e6f7ff;
    border-color: #91d5ff;
  }

  .template-item-content {
    flex: 1;
  }

  .template-item-name {
    font-size: 14px;
    font-weight: 500;
    color: #262626;
    margin-bottom: 4px;
  }

  .template-item-info {
    display: flex;
    gap: 12px;
    font-size: 12px;
    color: #8c8c8c;
  }

  .template-item-time {
    display: flex;
    align-items: center;
  }

  .template-item-chart {
    display: flex;
    align-items: center;
  }

  .template-item-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s;
  }

  .template-item:hover .template-item-actions {
    opacity: 1;
  }

  .template-presets {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .preset-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .preset-item:hover {
    border-color: #1890ff;
    background-color: #f6ffed;
  }

  .preset-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f0f0f0;
    border-radius: 6px;
    margin-right: 12px;
    font-size: 18px;
    color: #666;
  }

  .preset-content {
    flex: 1;
  }

  .preset-name {
    font-size: 14px;
    font-weight: 500;
    color: #262626;
    margin-bottom: 4px;
  }

  .preset-desc {
    font-size: 12px;
    color: #8c8c8c;
  }

  /deep/.modal-table .ant-table-body{
    max-height: 300px;
    overflow-y: auto;
    margin: 0;
  }

  /deep/.modal-table .ant-table-thead{
    position: sticky;
    top: 0;
    background: #FFF;
  }


</style>