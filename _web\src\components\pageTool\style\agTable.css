@import '../../../../node_modules/ag-grid-community/styles/ag-grid.css';
@import '../../../../node_modules/ag-grid-community/styles/ag-theme-balham.css';



.ag-theme-balham {
  --ag-balham-active-color: #1890ff;
  /* 表头颜色 */
  --ag-header-background-color: #fafafa;
  /* 边框颜色 */
  --ag-border-color: #e8e8e8;
  /* 行悬浮的颜色 */
  --ag-row-hover-color: #e6f7ff;
  
  /* 单选框颜色 */
  --ag-checkbox-unchecked-color: #e8e8e8;
  
  --ag-selected-row-background-color: #f0f5ff;
  /* 内容行高 */
  --ag-row-height: 38px;
  /* 标题行高 */
  --ag-header-height: 38px;
  /* 悬浮背景色 */
  --ag-subheader-background-color: #fff;
  /* 表头拖拉线的高度 */
  --ag-header-column-separator-height:100%;
  /* 行的底边框颜色 */
  --ag-row-border-color:#e8e8e8;

  
}

/* 表头字体颜色、字重 */
.ag-theme-balham .ag-header-row {
  font-weight: 500;
  color: #000000D9;
  font-size: 12px;
}


[class*=ag-theme-] {
  /* 边框颜色 */
  --ag-border-color: #e8e8e8;
  /* 圆角 */
  --ag-wrapper-border-radius: 4px;
  /* 行选中的颜色 */
  --ag-selected-row-background-color: #e6f7ff;
}

.ag-root-wrapper{
  border-bottom: none;
  border-right: none;
}

.ag-row{
  background-color:transparent;
  color: #333;
  font-weight: 400;
}

.ag-row-odd{
  background-color: transparent;
}

.ag-ltr .ag-cell {
  border-right: 1px solid var(--ag-border-color);
  /* border-right: none; */
  /* line-height: 45px; */
  text-align: center;
}

/* .ag-header-cell{
  display: flex;
  justify-content: center;
} */

.ag-header-cell::before,
.ag-header-group-cell:not(.ag-header-span-height.ag-header-group-cell-no-group)::before {
  width: none;
  /* height:100%; */
  /* top:0; */
}

/* 表头内容居中 */
.ag-header-group-cell-label, .ag-header-cell-label{
  justify-content:center;
}



/* 表头不要边框线 */
/* .ag-ltr .ag-header-cell::before{
  opacity: 0;
} */

/* 悬浮时，展示表头边框线 */
/* .ag-header-row:hover .ag-header-cell::before{
  opacity: 1;
} */

.ag-header-group-cell-label.ag-sticky-label{
  margin: auto;
}

.ag-tooltip {
  color: #333;
  padding: 8px;
  background-color: #fff;
  z-index: 9999;
  max-width: 600px; /* 设置最大宽度 */
  overflow-wrap: break-word; /* 确保内容换行 */
  white-space: pre-wrap;

}

.ag-unselectable {
  -moz-user-select: text;
  -webkit-user-select: text;
  -ms-user-select: text;
  user-select: text;
}
.ag-header-cell,.ag-header-group-cell{
  user-select: none;
}

/* 空状态 */
.ag-overlay-no-rows-center{
  color: rgba(0, 0, 0, 0);
  width: 62px;
  height: 68px;
  background-image: url(~@/assets/images/empty.png);
  background-size: cover;
}

@media screen and (min-width:900px) and (max-width:1290px) {
  .ag-header-cell::before,
  .ag-header-group-cell:not(.ag-header-span-height.ag-header-group-cell-no-group)::before {
    width: 1px;
    background-color: #e8e8e8;
  }
}


@media screen and (min-width:1290px) and (max-width:3000px) {
  .ag-header-cell::before,
  .ag-header-group-cell:not(.ag-header-span-height.ag-header-group-cell-no-group)::before {
    width: 1px;
    background-color: #e8e8e8;
  }
}


