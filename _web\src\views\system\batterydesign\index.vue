<template>
	<div>
		<a-card :bordered="false">
			<a-tabs default-active-key="null" v-model="structureType" type="card" @change="$refs.table.refresh(true)">
				<a-tab-pane :key="null" tab="全部"> </a-tab-pane>
				<a-tab-pane key="g_cylinder" tab="G圆柱" v-if="auth.haveG == 1"> </a-tab-pane>
				<a-tab-pane key="c_cylinder" tab="C圆柱" v-if="auth.haveC == 1"> </a-tab-pane>
				<a-tab-pane key="v_cylinder" tab="V圆柱" v-if="auth.haveV == 1"> </a-tab-pane>
				<a-tab-pane key="winding" tab="方形卷绕" v-if="auth.haveWinding == 1"> </a-tab-pane>
				<a-tab-pane key="lamination" tab="方形叠片" v-if="auth.haveLamination == 1"> </a-tab-pane>
				<a-tab-pane key="soft_roll" tab="软包" v-if="auth.haveSoft == 1"> </a-tab-pane>
			</a-tabs>

			<a-tabs v-model="type" @change="$refs.table.refresh(true)">
				<a-tab-pane key="product" tab="项目产品"> </a-tab-pane>
				<a-tab-pane key="platform" tab="技术平台"> </a-tab-pane>
			</a-tabs>

			<a-spin :spinning="loading">
				<div style="float: left;position: relative;z-index: 1;padding-bottom: 5px;width:300px">
					<a-input placeholder="请输入搜索内容" v-model="queryParam.keyword" />
					<a-button type="primary" style="width: 80px;position: absolute;right: -100px;" @click="$refs.table.refresh()"
						>搜索</a-button
					>
				</div>

				<div v-show="type == 'platform'" style="float: right;position: relative;z-index: 1;padding-bottom: 5px">
					<a-button type="primary" style="margin-left: 8px;width: 80px" @click="openAdd">新增</a-button>
					<a-button type="primary" style="margin-left: 8px;width: 80px" @click="openEdit">编辑</a-button>
					<a-button type="primary" style="margin-left: 8px;width: 80px" @click="copy">复制</a-button>
					<a-popconfirm
						title="确定删除吗?"
						ok-text="确定"
						cancel-text="取消"
						@confirm="deleteBattery"
						placement="topRight"
						:visible="deleteVisible"
						@cancel="() => (deleteVisible = false)"
					>
						<a-button type="primary" style="margin-left: 8px;width: 80px" @click="deleteBefore">删除</a-button>
					</a-popconfirm>
				</div>

				<a-modal
					title="SOR管理记录"
					:visible="sorVisible"
					:footer="null"
					width="80%"
					@cancel="() => {sorVisible = false}"
				>
					<div style="float: right;position: relative;z-index: 1;padding-bottom: 5px">
						<a-button type="primary" style="margin-left: 8px;width: 80px" @click="addSor">新增</a-button>
						<a-button type="primary" style="margin-left: 8px;width: 80px" @click="copySor">复制</a-button>
						<a-popconfirm
							title="确定删除吗?"
							ok-text="确定"
							cancel-text="取消"
							@confirm="deleteSor"
							placement="topRight"
							:visible="deleteSorVisible"
							@cancel="() => (deleteSorVisible = false)"
						>
							<a-button type="primary" style="margin-left: 8px;width: 80px" @click="deleteSorBefore">删除</a-button>
						</a-popconfirm>
					</div>
					<a-table
						:columns="sorColumns"
						:data-source="sorData"
						:rowKey="record => record.id"
						:row-selection="sorRowSelection"
					>
						<template slot="version" slot-scope="text, record">
							<a-input
								:value="text"
                :disabled="!(record.checkStatus == 0 || record.checkStatus == 80)"
                placeholder="例：V1"
								@change="onSorCellChange(record.id, 'version', $event)"
								style="text-align: center"
							/>
						</template>
						<template slot="checkStatus" slot-scope="text, record">
							<a @click="openCheckRecord(record.id, 'sor')" style="text-align: center">{{
								checkStatus.find(e => e.key == text).value
							}}</a>
						</template>
						<template slot="remark" slot-scope="text, record">
							<a-input
								:value="text"
                :disabled="!(record.checkStatus == 0 || record.checkStatus == 80)"
								@change="onSorCellChange(record.id, 'remark', $event)"
								style="text-align: center"
							/>
						</template>
						<template slot="stage" slot-scope="text, record">
							<a-select
								v-model="text"
                :disabled="!(record.checkStatus == 0 || record.checkStatus == 80)"
								style="width: 100%;text-align: center"
								@change="onSorSelectChange(record.id, 'stage', $event)"
							>
								<a-select-option value="a">
									A样
								</a-select-option>

								<a-select-option value="b">
									B样
								</a-select-option>

								<a-select-option value="c">
									C样
								</a-select-option>
								<a-select-option value="d">
									D样
								</a-select-option>
							</a-select>
						</template>
						<span slot="sor" slot-scope="text, record">
							<a @click="gotoSor(record)">进入</a>
						</span>

						<template slot="submit" slot-scope="text, record1">
							<a-popconfirm
								placement="topRight"
								title="确定提交吗?"
								ok-text="确定"
								cancel-text="取消"
								@confirm="checkRecordSubmit(record1, 'sor')"
								v-if="(record1.checkStatus == 0 || record1.checkStatus == 80) && (record1.stage == 'a' || record1.stage == 'b')"
								:visible="record1.isDefine == true"
								@cancel="() => (record1.isDefine = false)"
							>
                <a-spin size="small" :spinning="record1.submitLoading">
                  <a @click="checkIsDefine(record1)">提交</a>
                </a-spin>

							</a-popconfirm>
							<span v-else>提交</span>
						</template>
					</a-table>
				</a-modal>
				<a-modal
					:title="null"
					:visible="designVisible"
					:footer="null"
					width="80%"
					@cancel="() => (designVisible = false)"
				>
					<a-tabs
						v-model="designType"
						@change="() => changePage(designType == 'design' ? changePageCallback : changeExportPageCallback)"
					>
						<a-tab-pane key="design" tab="研发设计记录">
							<div style="float: right;position: relative;z-index: 1;padding-bottom: 5px">
								<a-button type="primary" style="margin-left: 8px;width: 80px" @click="addDesign">新增</a-button>
								<a-button type="primary" style="margin-left: 8px;width: 80px" @click="$refs.table.refresh(true)"
									>复制</a-button
								>
								<a-button type="primary" style="margin-left: 8px;width: 80px" @click="deleteDesign">删除</a-button>
							</div>
							<a-table
								:columns="designColumns"
								:data-source="designData"
								:rowKey="record => record.id"
								:row-selection="designRowSelection"
								:pagination="{ pageSize: 9 }"
								@change="changePage(changePageCallback)"
								bordered
							>
								<span slot="name" slot-scope="text, record">
									<a @click="gotoManager(record)">{{ text }}</a>
								</span>

								<span slot="submit" slot-scope="text, record">
									<a>提交</a>
								</span>
							</a-table>
						</a-tab-pane>
						<a-tab-pane key="export" tab="制造输出记录">
							<div style="float: right;position: relative;z-index: 1;padding-bottom: 5px">
								<a-button type="primary" style="margin-left: 8px;width: 80px" @click="openExportDesign">新增</a-button>
								<a-button type="primary" style="margin-left: 8px;width: 80px" @click="$refs.table.refresh(true)"
									>复制
								</a-button>
								<a-button type="primary" style="margin-left: 8px;width: 80px" @click="deleteExportDesign"
									>删除</a-button
								>
							</div>

							<a-modal
								title="新增"
								:visible="designAddVisible"
								width="60%"
								@ok="addExportDesign"
								@cancel="() => (designAddVisible = false)"
							>
								<a-table
									:columns="designExportAddColumns"
									:data-source="onlyDesignList"
									:rowKey="record => record.id"
									:row-selection="onlyDesignListRowSelection"
									:pagination="false"
									:rowClassName="() => 'unSet'"
									bordered
								>
								</a-table>
							</a-modal>

							<a-table
								:columns="designExportColumns"
								:data-source="designExportData"
								:rowKey="record => record.id"
								:row-selection="designExportRowSelection"
								:pagination="{ pageSize: 9 }"
								@change="changePage(changeExportPageCallback)"
								bordered
							>
								<span slot="name" slot-scope="text, record">
									<a @click="gotoManager(record)">{{ text }}</a>
								</span>

								<span slot="submit" slot-scope="text, record">
									<a>提交</a>
								</span>
							</a-table>
						</a-tab-pane>
					</a-tabs>
				</a-modal>

				<s-table
					ref="table"
					:columns="
						type == 'product'
							? structureType != null
								? columnsNoType
								: columns
							: structureType != null
							? columnsPlatFormNoType
							: columnsPlatForm
					"
					:data="loadData"
					:alert="false"
					:rowKey="record1 => record1.id"
					:scroll="{ x: 1000 }"
					:row-selection="type == 'product'?null:rowSelection"
				>
					<!-- <div slot="rpmTitle">研发项目经理<br/>(RPM)</div> -->
					<!-- 研发项目经理 -->
					<div slot="rpmTitle">
						<span>RPM</span>
						<a-tooltip placement="topLeft" title="研发项目经理" arrow-point-at-center>
							<a-icon class="tips" type="question-circle" />
						</a-tooltip>
					</div>
					<!-- <div slot="pdTitle">产品经理<br />(PD)</div> -->
					<!-- 产品经理 -->
					<div slot="pdTitle">
						<span>PD</span>
						<a-tooltip placement="topLeft" title="产品经理" arrow-point-at-center>
							<a-icon class="tips" type="question-circle" />
						</a-tooltip>
					</div>

					<!-- <div slot="startDateTitle">项目启动<br />(K0立项评审)</div> -->
					<div slot="startDateTitle">
						<span>立项日期</span>
						<!--<a-tooltip placement="topLeft" title="K0立项评审" arrow-point-at-center>
							<a-icon class="tips" type="question-circle" />
						</a-tooltip>-->
					</div>

					<!-- <div slot="ptTitle">负责人<br />(PT)</div> -->
					<div slot="ptTitle">
						<span>PT</span>
						<a-tooltip placement="topLeft" title="负责人" arrow-point-at-center>
							<a-icon class="tips" type="question-circle" />
						</a-tooltip>
					</div>

					<span slot="sor" slot-scope="text, record1">
						<a @click="openSor(record1)">进入</a>
					</span>

					<span slot="design" slot-scope="text, record1">
						<a @click="openDesign(record1)">进入</a>
					</span>

					<span slot="checkJson" slot-scope="text, record1">
						<a @click="openCheck(record1)">{{ record1.isDefine == 1 ? "定义" : "未定义" }}</a>
					</span>
					<span slot="role" slot-scope="text, record1">
						<a @click="openRole(record1)">定义</a>
					</span>

					<template slot="scenario" slot-scope="text, record1">
						<a-tree-select
							v-model="text"
							@change="changeSelect($event, record1, 'scenario')"
							:defaultExpandAll="true"
							:dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
							:treeData="gData"
							placeholder="应用场景"
						>
						</a-tree-select>
					</template>
					<template slot="grade" slot-scope="text, record1">
						<a-select v-model="text" @change="changeSelect($event, record1, 'grade')">
							<a-select-option value="S">
								S
							</a-select-option>

							<a-select-option value="A">
								A
							</a-select-option>

							<a-select-option value="B">
								B
							</a-select-option>
							<a-select-option value="C">
								C
							</a-select-option>
						</a-select>
					</template>
					<template slot="batteryName" slot-scope="text, record1">
						<a-input :value="text" @change="changeInput($event, record1, 'batteryName')"></a-input>
					</template>
					<template slot="productName" slot-scope="text, record1">
						<a-input :value="text" @change="changeInput($event, record1, 'productName')"></a-input>
					</template>
					<template slot="customer" slot-scope="text, record1">
						<a-input :value="text" @change="changeInput($event, record1, 'customer')"></a-input>
					</template>

					<template slot="pd" slot-scope="text, record1">
						<a-input :value="text" @change="changeInput($event, record1, 'pd')"></a-input>
					</template>

					<template slot="rpm" slot-scope="text, record1">
						<a-input :value="text" @change="changeInput($event, record1, 'rpm')"></a-input>
					</template>
					<template slot="pt" slot-scope="text, record1">
						<a-input :value="text" @change="changeInput($event, record1, 'pt')"></a-input>
					</template>

					<span slot="batterySorStatus" slot-scope="text, record1">
						<a @click="gotoSor(record1)">SOR管理</a>
					</span>
					<span slot="batteryDesignScheme" slot-scope="text, record1">
						<a @click="gotoManager(record1)">方案设计</a>
					</span>
					<span slot="batteryMiSpecs" slot-scope="text, record1">
						<a @click="gotoMi(record1)">MI设计</a>
					</span>
					<span slot="batteryBomSpecs" slot-scope="text, record1">
						<a @click="gotoBom(record1)">电芯BOM设计</a>
					</span>

					<div
						slot="checkMan"
						slot-scope="text, record1"
						:title="record1.manCheckStatus > 0 ? record1.checkManTitle : null"
					>
						{{ record1.manCheckStatus > 0 ? text : "" }}
					</div>
					<span slot="action" slot-scope="text, record1">
						<a
							@click="$refs.editForm.edit(record1)"
							v-if="record1.isOwn == 1"
							:disabled="record1.isOwn == 1 && record1.manCheckStatus == 0 ? false : true"
							>编辑</a
						>
						<a-divider type="vertical" v-if="record1.isOwn == 1" />
						<a-popconfirm placement="topRight" title="确认复制？" @confirm="() => designCopy(record1)">
							<a>复制</a>
						</a-popconfirm>
						<a-divider type="vertical" v-if="record1.isOwn == 1" />
						<a-popconfirm
							placement="topRight"
							title="确认删除？"
							@confirm="() => designDelete(record1.id)"
							v-if="record1.isOwn == 1"
						>
							<a
								:disabled="
									record1.isOwn == 1 &&
									(record1.manCheckStatus == 0 || record1.manCheckStatus == 20 || record1.manCheckStatus == 80)
										? false
										: true
								"
								>删除</a
							>
						</a-popconfirm>
						<a-divider type="vertical" v-if="record1.isOwn == 1" />
						<a-popconfirm
							placement="topRight"
							title="确认传阅？"
							:disabled="record1.isOwn == 0"
							v-if="record1.isOwn == 1"
						>
							<a>传阅</a>
						</a-popconfirm>
						<a-divider type="vertical" v-if="record1.isOwn == 1 && record1.manCheckStatus != 80" />
						<a-popconfirm placement="topRight" title="确认提交？" @confirm="() => submit(record1.id)">
							<a
								v-if="record1.isOwn == 1 && record1.manCheckStatus != 80"
								:disabled="record1.manCheckStatus == 10 || record1.manCheckStatus == 70"
								>{{ record1.manCheckStatus == 0 ? "提交" : "冻结提交" }}</a
							>
						</a-popconfirm>
					</span>
				</s-table>
				<span style="position: absolute;margin-top: -30px;font-size: smaller;z-index: 2;">产品名称和项目名称需遵守：<a @click="() => visible3 = true">《动力电池研究院产品和项目命名规定》</a>
        <br>
          <span>版本号:V1.0.0</span>
        </span>
				<add-form ref="addForm" @ok="handleOk" :type="type" />
				<add-form-platform ref="addFormPlatform" @ok="handleOk" :type="type" />
				<edit-form ref="editForm" @ok="handleOk" />
				<edit-form-platform ref="editFormPlatform" @ok="handleOk" />
				<check-man ref="checkMan" @ok="handleOk" />
				<check-man-pre ref="checkManPre" @ok="handleOk" />
			</a-spin>
		</a-card>
		<check-record ref="checkRecord"></check-record>
		<role ref="role" @ok="handleOk"></role>

    <a-drawer :bodyStyle="{ height: '100%' }" placement="right" :closable="false" width="80%" :visible="visible3" @close="onClose3">
      <iframe :src="'http://*********:89/sysFileInfo/previewPdf?id=1692007867985231873#view=FitH,top&'"  width="100%" height="100%"></iframe>
    </a-drawer>
	</div>
</template>
<script>
import {
	checkRecordGetListByBusinessId,
	checkRecordSubmitCheck
} from "@/api/modular/system/batterydesignCheckRecordManage"

import checkRecord from "./checkRecord"

import { ALL_APPS_MENU } from "@/store/mutation-types"
import Vue from "vue"
import { STable, XCard } from "@/components"
import {
	getBatteryDesignPage,
	sysBatteryDesignEdit,
	sysBatteryDesignCopy,
	getBatteryDesignCheckPage,
	sysBatteryDesignSubmit,
	batteryDesignRelationOnlyDesignList,
	getAuth,
	batteryDesignRelationUpdate,
	batteryDesignRelationUpdateByInBatteryId,
	addSor,
	addDesign,
	batteryDesignRelationDesignList,
	batteryDesignRelationSorList,
	copySor,
	batteryDesignRelationAddExportDesign
} from "@/api/modular/system/batterydesignManage"
import { mapActions, mapGetters } from "vuex"
import editForm from "./editForm"
import addForm from "./addForm"
import checkMan from "./checkMan"
import checkManPre from "./checkManPre"
import addFormPlatform from "./addFormPlatform"
import editFormPlatform from "./editFormPlatform"
import role from "./role"
import moment from "moment"

export default {
	components: {
		XCard,
		STable,
		editForm,
		addForm,
		checkMan,
		checkManPre,
		addFormPlatform,
		editFormPlatform,
		moment,
		checkRecord,
    role
	},
	data() {
		return {
      queryCount:0,
      submitLoading:false,
      visible3: false,
			deleteVisible: false,
			deleteSorVisible: false,
			gData: [
				{
					title: "乘用车",
					key: "乘用车",
					value: "乘用车"
				},
				{
					title: "混动",
					key: "混动",
					value: "混动"
				},
				{
					title: "轻型动力",
					key: "轻型动力",
					value: "轻型动力"
				},
				{
					title: "商用",
					key: "商用",
					value: "商用",
					selectable: false,
					children: [
						{
							title: "客车",
							key: "商用-客车",
							value: "商用-客车"
						},
						{
							title: "重卡",
							key: "商用-重卡",
							value: "商用-重卡"
						},
						{
							title: "物流",
							key: "商用-物流",
							value: "商用-物流"
						},
						{
							title: "工程",
							key: "商用-工程",
							value: "商用-工程"
						}
					]
				},
				{
					title: "储能",
					key: "储能",
					value: "储能",
					selectable: false,
					children: [
						{
							title: "电力",
							key: "储能-电力",
							value: "储能-电力"
						},
						{
							title: "通讯",
							key: "储能-通讯",
							value: "储能-通讯"
						},
						{
							title: "家用",
							key: "储能-家用",
							value: "储能-家用"
						}
					]
				}
			],
			sorData: [],
			designType: "design",
			sorVisible: false,
			checkVisible: { 1673877521532755971: false },
			designAddVisible: false,
			designData: [],
			designExportData: [],
			onlyDesignExportData: [],
			designVisible: false,
			structureType: null,
			selectedRowKeys: [],
			selectedRow: [],

			rowSelection: {
				columnWidth: 30,
				onChange: (selectedRowKeys, selectedRows) => {
					this.selectedRowKeys = selectedRowKeys
					this.selectedRow = selectedRows
				}
			},

			sorSelectedRowKeys: [],
			sorSelectedRow: [],

			sorRowSelection: {
				columnWidth: 40,
				onChange: (selectedRowKeys, selectedRows) => {
					this.sorSelectedRowKeys = selectedRowKeys
					this.sorSelectedRow = selectedRows
				}
			},
			designSelectedRowKeys: [],
			designSelectedRow: [],

			designRowSelection: {
				columnWidth: 40,
				onChange: (selectedRowKeys, selectedRows) => {
					this.designSelectedRowKeys = selectedRowKeys
					this.designSelectedRow = selectedRows
				}
			},

			designExportSelectedRowKeys: [],
			designExportSelectedRow: [],

			designExportRowSelection: {
				columnWidth: 40,
				onChange: (selectedRowKeys, selectedRows) => {
					this.designExportSelectedRowKeys = selectedRowKeys
					this.designExportSelectedRow = selectedRows
				}
			},
			onlyDesignListRowKeys: [],
			onlyDesignListRow: [],
			onlyDesignListRowSelection: {
				columnWidth: 40,
				onChange: (selectedRowKeys, selectedRows) => {
					this.onlyDesignListRowKeys = selectedRowKeys
					this.onlyDesignListRow = selectedRows
				}
			},
			queryParam: {},
			checkNum: "",
			type: "product",
			auth: [],
			showQuery: false,
			record: {},
			showCheck: false,
			onlyDesignData: [],
			onlyDesignList: [],
			width: document.documentElement.clientWidth,
			// 表头
			columns: [
				{
					title: "序号",
					dataIndex: "index",
					align: "center",
					width: 40,
					ellipsis: true,
					customRender: (text, record, index) => `${index + 1}`
				},
				{
					title: "电池结构",
					width: 65,
					dataIndex: "structureType",
					align: "center",
					customRender: (text, record, index) => {
						if (text == "g_cylinder") {
							return "G圆柱"
						}
						if (text == "c_cylinder") {
							return "C圆柱"
						}
						if (text == "v_cylinder") {
							return "V圆柱"
						}
						if (text == "winding") {
							return "方形卷绕"
						}

						if (text == "lamination") {
							return "方形叠片"
						}
						if (text == "soft_roll") {
							return "软包"
						}
						return text
					}
				},

				{
					title: "应用场景",
					width: 70,
					dataIndex: "scenario",
					align: "center"
					/*scopedSlots: {
              customRender: 'scenario'
            }*/
				},
				{
					title: "产品名称",
					width: 70,
					dataIndex: "productName",
					align: "center"
					/* scopedSlots: {
              customRender: 'productName'
            }*/
				},
				{
					title: "项目等级",
					width: 65,
					dataIndex: "grade",
					align: "center"
					/* scopedSlots: {
              customRender: 'grade'
            }*/
				},
				{
					title: "项目名称",
					width: 70,
					dataIndex: "batteryName",
					align: "center"
					/* scopedSlots: {
              customRender: 'batteryName'
            }*/
				},
				{
					title: "客户",
					width: 65,
					dataIndex: "customer",
					align: "center"
					/* scopedSlots: {
              customRender: 'customer'
            }*/
				},
				{
					width: 65,
					dataIndex: "rpm",
					align: "center",
					slots: { title: "rpmTitle" }
				},
				{
					width: 65,
					dataIndex: "pd",
					align: "center",
					slots: { title: "pdTitle" }
				},
				{
					title: "项目阶段",
					width: 75,
					dataIndex: "projectStatus",
					align: "center"
				},
				{
					title: "SOR管理",
					width: 65,
					dataIndex: "sor",
					align: "center",
					scopedSlots: {
						customRender: "sor"
					}
				},
				{
					width: 80,
					dataIndex: "startDate",
					align: "center",
					slots: { title: "startDateTitle" }
				},
				{
					title: "设计开发",
					width: 65,
					dataIndex: "design",
					align: "center",
					scopedSlots: {
						customRender: "design"
					}
				},
				{
					title: "产品状态",
					width: 85,
					ellipsis: true,
					dataIndex: "productDevelopmentStage",
					align: "center",
					customRender: (text, record, index) => {
						if (text == "a") {
							return "A样"
						}
						if (text == "b") {
							// return "B样"
							return "平台课题方案"

						}
						if (text == "c") {
							return "C样"
						}
						if (text == "d") {
							return "D样"
						}
						if (text == "platform") {
							return "平台课题方案"
						}

						return text
					}
				},
			/*	{
					title: "创建时间",
					width: 80,
					dataIndex: "createTime",
					align: "center",
					customRender: (text, record, index) => {
						if (null != text) {
							return moment(text).format("YYYY-MM-DD")
						}
					}
				},
        {
          title: "编辑人",
          width: 65,
          dataIndex: "updateName",
          align: "center"
        },*/
				{
					title: "审核角色",
					width: 65,
					dataIndex: "checkJson",
					align: "center",
					scopedSlots: {
						customRender: "checkJson"
					}
				},{
					title: "支持角色",
					width: 65,
					align: "center",
					scopedSlots: {
						customRender: "role"
					}
				}
			],

			columnsNoType: [
				{
					title: "序号",
					dataIndex: "index",
					align: "center",
					width: 50,
					ellipsis: true,
					customRender: (text, record, index) => `${index + 1}`
				},

				{
					title: "应用场景",
					width: 70,
					dataIndex: "scenario",
					align: "center"
					/* scopedSlots: {
              customRender: 'scenario'
            }*/
				},
				{
					title: "产品名称",
					width: 70,
					dataIndex: "productName",
					align: "center"
					/*scopedSlots: {
              customRender: 'productName'
            }*/
				},
				{
					title: "项目等级",
					width: 70,
					dataIndex: "grade",
					align: "center"
					/*scopedSlots: {
              customRender: 'grade'
            }*/
				},
				{
					title: "项目名称",
					width: 70,
					dataIndex: "batteryName",
					align: "center"
					/*scopedSlots: {
              customRender: 'batteryName'
            }*/
				},
				{
					title: "客户",
					width: 70,
					dataIndex: "customer",
					align: "center"
					/*scopedSlots: {
              customRender: 'customer'
            }*/
				},
				{
					width: 70,
					dataIndex: "rpm",
					align: "center",
					slots: { title: "rpmTitle" }
					/*scopedSlots: {
              customRender: 'rpm'
            }*/
				},
				{
					width: 70,
					dataIndex: "pd",
					align: "center",
					slots: { title: "pdTitle" }
					/*scopedSlots: {
              customRender: 'pd'
            }*/
				},
				{
					title: "项目阶段",
					width: 75,
					dataIndex: "projectStatus",
					align: "center"
				},
				{
					title: "SOR管理",
					width: 70,
					dataIndex: "sor",
					align: "center",
					scopedSlots: {
						customRender: "sor"
					}
				},
				{
					width: 100,
					dataIndex: "startDate",
					align: "center",
					slots: { title: "startDateTitle" }
				},
				{
					title: "设计开发",
					width: 70,
					dataIndex: "design",
					align: "center",
					scopedSlots: {
						customRender: "design"
					}
				},
				{
					title: "产品状态",
					width: 90,
					ellipsis: true,
					dataIndex: "productDevelopmentStage",
					align: "center",
					customRender: (text, record, index) => {
						if (text == "a") {
							return "A样"
						}

						if (text == "b") {
							return "B样"
						}
						if (text == "c") {
							return "C样"
						}
						if (text == "d") {
							return "D样"
						}
						if (text == "platform") {
							return "平台课题方案"
						}
						return text
					}
				},
				/*{
					title: "创建时间",
					width: 90,
					dataIndex: "createTime",
					align: "center",
					customRender: (text, record, index) => {
						if (null != text) {
							return moment(text).format("YYYY-MM-DD")
						}
					}
				},
				{
					title: "创建人",
					width: 90,
					dataIndex: "createName",
					align: "center"
				},*/{
          title: "审核角色",
          width: 65,
          dataIndex: "checkJson",
          align: "center",
          scopedSlots: {
            customRender: "checkJson"
          }
        },{
          title: "支持角色",
          width: 65,
          align: "center",
          scopedSlots: {
            customRender: "role"
          }
        }
			],
			// 预言产品
			columnsPlatForm: [
				{
					title: "序号",
					dataIndex: "index",
					align: "center",
					width: 45,
					ellipsis: true,
					customRender: (text, record, index) => `${index + 1}`
				},
				{
					title: "电池结构",
					width: 65,
					dataIndex: "structureType",
					align: "center",
					customRender: (text, record, index) => {
						if (text == "g_cylinder") {
							return "G圆柱"
						}
						if (text == "c_cylinder") {
							return "C圆柱"
						}
						if (text == "v_cylinder") {
							return "V圆柱"
						}
						if (text == "winding") {
							return "方形卷绕"
						}

						if (text == "lamination") {
							return "方形叠片"
						}
						if (text == "soft_roll") {
							return "软包"
						}
						return text
					}
				},

				{
					title: "应用场景",
					width: 70,
					dataIndex: "scenario",
					align: "center"
					/*scopedSlots: {
              customRender: 'scenario'
            }*/
				},
				{
					title: "产品名称",
					width: 70,
					dataIndex: "productName",
					align: "center"
					/*scopedSlots: {
              customRender: 'productName'
            }*/
				},
				{
					title: "产品等级",
					width: 80,
					dataIndex: "grade",
					align: "center"
					/*scopedSlots: {
              customRender: 'grade'
            }*/
				},
				{
					title: "技术名称",
					width: 80,
					dataIndex: "batteryName",
					align: "center"

					/*scopedSlots: {
              customRender: 'batteryName'
            }*/
				},
				{
					width: 70,
					dataIndex: "pt",
					align: "center",
					slots: { title: "ptTitle" }
					/*scopedSlots: {
              customRender: 'pt'
            }*/
				},
				{
					title: "技术状态",
					width: 80,
					dataIndex: "platformStatus",
					align: "center"
				},
				{
					title: "SOR管理",
					width: 70,
					dataIndex: "sor",
					align: "center",
					scopedSlots: {
						customRender: "sor"
					}
				},
				{
					title: "立项评审",
					width: 70,
					dataIndex: "startDate",
					align: "center"
				},
				{
					title: "设计开发",
					width: 70,
					dataIndex: "design",
					align: "center",
					scopedSlots: {
						customRender: "design"
					}
				},
				/*{
					title: "创建时间",
					width: 90,
					dataIndex: "createTime",
					align: "center",
					customRender: (text, record, index) => {
						if (null != text) {
							return moment(text).format("YYYY-MM-DD")
						}
					}
				},
				{
					title: "创建人",
					width: 90,
					dataIndex: "createName",
					align: "center"
				},*/
				{
					title: "审核角色",
					width: 80,
					dataIndex: "checkJson",
					align: "center",
					scopedSlots: {
						customRender: "checkJson"
					}
				},{
          title: "支持角色",
          width: 80,
          align: "center",
          scopedSlots: {
            customRender: "role"
          }
        }
			],
			columnsPlatFormNoType: [
				{
					title: "序号",
					dataIndex: "index",
					align: "center",
					width: 50,
					ellipsis: true,
					customRender: (text, record, index) => `${index + 1}`
				},

				{
					title: "应用场景",
					width: 70,
					dataIndex: "scenario",
					align: "center"
					/*scopedSlots: {
              customRender: 'scenario'
            }*/
				},
				{
					title: "产品名称",
					width: 70,
					dataIndex: "productName",
					align: "center"
					/*scopedSlots: {
              customRender: 'productName'
            }*/
				},
				{
					title: "产品等级",
					width: 70,
					dataIndex: "grade",
					align: "center"
					/*scopedSlots: {
              customRender: 'grade'
            }*/
				},
				{
					title: "技术名称",
					width: 70,
					dataIndex: "batteryName",
					align: "center"
					/* scopedSlots: {
              customRender: 'batteryName'
            }*/
				},
				{
					width: 70,
					dataIndex: "pt",
					align: "center",
					slots: { title: "ptTitle" }
					/* scopedSlots: {
              customRender: 'pt'
            }*/
				},
				{
					title: "技术状态",
					width: 70,
					dataIndex: "platformStatus",
					align: "center"
				},
				{
					title: "SOR管理",
					width: 70,
					dataIndex: "sor",
					align: "center",
					scopedSlots: {
						customRender: "sor"
					}
				},
				{
					title: "立项评审",
					width: 70,
					dataIndex: "startDate",
					align: "center"
				},
				{
					title: "设计开发",
					width: 70,
					dataIndex: "design",
					align: "center",
					scopedSlots: {
						customRender: "design"
					}
				},
				/*{
					title: "创建时间",
					width: 90,
					dataIndex: "createTime",
					align: "center",
					customRender: (text, record, index) => {
						if (null != text) {
							return moment(text).format("YYYY-MM-DD")
						}
					}
				},
				{
					title: "创建人",
					width: 90,
					dataIndex: "createName",
					align: "center"
				},*/{
          title: "审核角色",
          width: 65,
          dataIndex: "checkJson",
          align: "center",
          scopedSlots: {
            customRender: "checkJson"
          }
        },{
          title: "支持角色",
          width: 65,
          align: "center",
          scopedSlots: {
            customRender: "role"
          }
        }
			],
			//0 待提交 10 审核中 20 批准中 40 归档中  70 已归档  80 已驳回   (30已审批   50 启用中 60 禁用中)
			checkStatus: [
				{ key: 0, value: "待提交" },
				{ key: 10, value: "审核中" },
				{ key: 20, value: "批准中" },
				{ key: 30, value: "已审批" },
				{ key: 40, value: "归档中" },
				{ key: 50, value: "启用中" },
				{ key: 60, value: "禁用中" },
				{ key: 70, value: "已归档" },
				{ key: 80, value: "已驳回" }
			],

			sorColumns: [
				/*{
            title: '序号',
            dataIndex: 'index',
            align: 'center',
            width: 50,
            ellipsis: true,
            customRender: (text, record, index) => `${index + 1}`
          },*/

				{
					title: "产品状态",
					width: 70,
					dataIndex: "stage",
					align: "center",
					scopedSlots: {
						customRender: "stage"
					}
				},
				{
					title: "版本",
					width: 70,
					dataIndex: "version",
					align: "center",
					scopedSlots: {
						customRender: "version"
					}
				},
				{
					title: "SOR管理",
					width: 70,
					dataIndex: "sor",
					align: "center",
					scopedSlots: {
						customRender: "sor"
					}
				},
				{
					title: "变更说明",
					width: 140,
					dataIndex: "remark",
					align: "center",
					scopedSlots: {
						customRender: "remark"
					}
				},
				{
					title: "审批状态",
					width: 70,
					dataIndex: "checkStatus",
					align: "center",
					scopedSlots: {
						customRender: "checkStatus"
					}
				},
				{
					title: "编辑人",
					width: 70,
					dataIndex: "updateName",
					align: "center"
				},
				{
					title: "更新时间",
					width: 70,
					dataIndex: "updateTime",
					align: "center",
					customRender: (text, record, index) => {
						if (null != text) {
							return moment(text).format("YYYY-MM-DD")
						}
					}
				},
				/* {
            title: '审核人',
            width: 70,
            dataIndex: 'checkMan',
            align: 'center',

          }, {
            title: '批准人',
            width: 70,
            dataIndex: 'approveMan',
            align: 'center',

          }, */ {
					title: "审批提交",
					width: 70,
					dataIndex: "submit",
					align: "center",
					scopedSlots: {
						customRender: "submit"
					}
				}
			],
			designColumns: [
				{
					title: "产品状态",
					dataIndex: "stage",
					align: "center",
					width: 70,
					customRender: (text, record, index) => {
						const obj = {
							children: (
								<a-select
									style="width:100%;height:100%;border: 0;"
									v-model={text}
									onChange={$event => this.onDesignSelectChange(record.inBatteryId, "stage", $event)}
								>
									<a-select-option value="a">A样</a-select-option>
									<a-select-option value="b">B样</a-select-option>
									<a-select-option value="c">C样</a-select-option>
									<a-select-option value="d">D样</a-select-option>
								</a-select>
							),
							attrs: {}
						}

						if (index == 0 || index % 3 == 0) {
							obj.attrs.rowSpan = 3
						} else {
							obj.attrs.rowSpan = 0
						}

						return obj
					}
				},
				{
					title: "样品",
					width: 70,
					dataIndex: "sample",
					align: "center",

					customRender: (text, record, index) => {
						const obj = {
							children: (
								<a-input
									value={text}
									onChange={$event => this.onDesignCellChange(record.inBatteryId, "sample", $event)}
									style="text-align: center;width:100%;height:100%;border:0"
								/>
							),
							attrs: {}
						}

						if (index == 0 || index % 3 == 0) {
							obj.attrs.rowSpan = 3
						} else {
							obj.attrs.rowSpan = 0
						}

						return obj
					}
				},
				{
					title: "文件内容",
					width: 70,
					dataIndex: "name",
					align: "center",
					scopedSlots: {
						customRender: "name"
					}
				},
				{
					title: "适应工厂-产线",
					width: 70,
					dataIndex: "factory",
					align: "center",
					customRender: (text, record, index) => {
						const obj = {
							children: (
								<a-input
									value={text}
									onChange={$event => this.onDesignCellChange(record.inBatteryId, "factory", $event)}
									style="text-align: center;width:100%;height:100%;border:0"
								/>
							),
							attrs: {}
						}

						if (index == 0 || index % 3 == 0) {
							obj.attrs.rowSpan = 3
						} else {
							obj.attrs.rowSpan = 0
						}

						return obj
					}
				},
				{
					title: "变更说明",
					width: 140,
					dataIndex: "remark",
					align: "center",
					customRender: (text, record, index) => {
						const obj = {
							children: (
								<a-input
									value={text}
									onChange={$event => this.onDesignCellChange(record.inBatteryId, "remark", $event)}
									style="text-align: center;width:100%;height:100%;border:0"
								/>
							),
							attrs: {}
						}

						if (index == 0 || index % 3 == 0) {
							obj.attrs.rowSpan = 3
						} else {
							obj.attrs.rowSpan = 0
						}

						return obj
					}
				},
				{
					title: "审批状态",
					width: 70,
					dataIndex: "checkStatus",
					align: "center",
					customRender: (text, record, index) => {}
				},
        {
          title: "编辑人",
          width: 70,
          dataIndex: "updateName",
          align: "center"
        },
				{
					title: "更新时间",
					width: 70,
					dataIndex: "updateTime",
					align: "center",
					customRender: (text, record, index) => (null == text ? "" : moment(new Date(text)).format("YYYY-MM-DD"))
				},
				{
					title: "审核人",
					width: 70,
					dataIndex: "checkMan",
					align: "center"
				},
				{
					title: "批准人",
					width: 70,
					dataIndex: "approveMan",
					align: "center"
				},
				{
					title: "审核提交",
					width: 70,
					dataIndex: "submit",
					align: "center",
					scopedSlots: {
						customRender: "submit"
					}
				}
			],
			designExportColumns: [
				{
					title: "基础方案版本",
					dataIndex: "stage",
					align: "center",
					width: 70,
					customRender: (text, record, index) => {
						const obj = {
							children: (
								<a-select
									style="width:100%;height:100%;border: 0;"
									v-model={text}
									onChange={$event => this.onDesignSelectChange(record.inBatteryId, "stage", $event)}
								>
									<a-select-option value="a">A样</a-select-option>
									<a-select-option value="b">B样</a-select-option>
									<a-select-option value="c">C样</a-select-option>
									<a-select-option value="d">D样</a-select-option>
								</a-select>
							),
							attrs: {}
						}

						if (index == 0 || index % 3 == 0) {
							obj.attrs.rowSpan = 3
						} else {
							obj.attrs.rowSpan = 0
						}

						return obj
					}
				},
				{
					title: "分类",
					width: 70,
					dataIndex: "sample",
					align: "center",

					customRender: (text, record, index) => {
						const obj = {
							children: (
								<a-input
									value={text}
									onChange={$event => this.onDesignCellChange(record.inBatteryId, "sample", $event)}
									style="text-align: center;width:100%;height:100%;border:0"
								/>
							),
							attrs: {}
						}

						if (index == 0 || index % 3 == 0) {
							obj.attrs.rowSpan = 3
						} else {
							obj.attrs.rowSpan = 0
						}

						return obj
					}
				},
				{
					title: "文件内容",
					width: 70,
					dataIndex: "name",
					align: "center",
					scopedSlots: {
						customRender: "name"
					}
				},
				{
					title: "适应工厂-产线",
					width: 70,
					dataIndex: "factory",
					align: "center",
					customRender: (text, record, index) => {
						const obj = {
							children: (
								<a-input
									value={text}
									onChange={$event => this.onDesignCellChange(record.inBatteryId, "factory", $event)}
									style="text-align: center;width:100%;height:100%;border:0"
								/>
							),
							attrs: {}
						}

						if (index == 0 || index % 3 == 0) {
							obj.attrs.rowSpan = 3
						} else {
							obj.attrs.rowSpan = 0
						}

						return obj
					}
				},
				{
					title: "变更说明",
					width: 140,
					dataIndex: "remark",
					align: "center",
					customRender: (text, record, index) => {
						const obj = {
							children: (
								<a-input
									value={text}
									onChange={$event => this.onDesignCellChange(record.inBatteryId, "remark  ", $event)}
									style="text-align: center;width:100%;height:100%;border:0"
								/>
							),
							attrs: {}
						}

						if (index == 0 || index % 3 == 0) {
							obj.attrs.rowSpan = 3
						} else {
							obj.attrs.rowSpan = 0
						}

						return obj
					}
				},
				{
					title: "审批状态",
					width: 70,
					dataIndex: "checkStatus",
					align: "center",
					customRender: (text, record, index) => {}
				},
        {
          title: "编辑人",
          width: 70,
          dataIndex: "updateName",
          align: "center"
        },
				{
					title: "更新时间",
					width: 70,
					dataIndex: "updateTime",
					align: "center",
					customRender: (text, record, index) => (null == text ? "" : moment(new Date(text)).format("YYYY-MM-DD"))
				},
				{
					title: "审核人",
					width: 70,
					dataIndex: "checkMan",
					align: "center"
				},
				{
					title: "批准人",
					width: 70,
					dataIndex: "approveMan",
					align: "center"
				},
				{
					title: "审核提交",
					width: 70,
					dataIndex: "submit",
					align: "center",
					scopedSlots: {
						customRender: "submit"
					}
				}
			],
			designExportAddColumns: [
				{
					title: "产品状态",
					dataIndex: "stage",
					align: "center",
					width: 70,
					customRender: (text, record, index) => {
						if (text == "a") {
							return "A样"
						}

						if (text == "b") {
							return "B样"
						}
						if (text == "c") {
							return "C样"
						}
						if (text == "d") {
							return "D样"
						}
						if (text == "platform") {
							return "平台课题方案"
						}
					}
				},
				{
					title: "样品",
					width: 70,
					dataIndex: "sample",
					align: "center"
				},
				{
					title: "适应工厂-产线",
					width: 70,
					dataIndex: "factory",
					align: "center"
				},
				{
					title: "变更说明",
					width: 140,
					dataIndex: "remark",
					align: "center"
				}
			],
			// 加载数据方法 必须为 Promise 对象
			loadData: parameter => {
				this.queryParam.structureType = this.structureType
				this.queryParam.type = this.type
				return getBatteryDesignPage(Object.assign(parameter, this.queryParam)).then(res => {


          this.queryCount++
          if(null != this.$route.query.batteryId && this.queryCount == 1){
            if(null == res.data.rows.find(item => item.id == this.$route.query.batteryId)){
                this.type = 'platform'
                this.queryCount = 0
                this.$refs.table.refresh(true)
                return
            }
            this.openSor(res.data.rows.find(item => item.id == this.$route.query.batteryId))
          }
					return res.data
				})
			},
			loading: false
		}
	},
	created() {
		if(this.$route.query.type){
			this.type = this.$route.query.type
		}

	},
	mounted() {
		getAuth().then(res => {
			this.auth = res.data
		})

		getBatteryDesignCheckPage().then(res => {
			this.checkNum = res.data.totalRows
			if (res.data.totalRows > 0) {
				this.showCheck = true
			}
		})
	},
	computed: {
		...mapGetters(["userInfo"])
	},
	methods: {
		...mapActions(["MenuChange"]),

    changeType(newVal){
		  this.type = newVal
      this.$refs.table.refresh(true)
    },

		checkIsDefine(re) {
			//审核人已定义
			if (this.record.isDefine == 1) {
				re.isDefine = true
			} else {
				re.isDefine = false
				this.$message.warn("审核人未定义")
				return
			}
		},
		checkRecordSubmit(record, type) {
      record.isDefine = false
      record.submitLoading = true
			checkRecordSubmitCheck({ businessId: record.id, batteryId: record.batteryId, businessType: type }).then(res => {
				if (res.success) {
          record.submitLoading = false
					this.$message.success("提交成功")
          this.submitLoading = false
					this.sorRefresh()
				} else {
          record.submitLoading = false
          this.submitLoading = false
					this.$message.error("提交失败：" + res.message)
				}
			})
		},

		openCheckRecord(businessId, type) {
			this.$refs.checkRecord.open(businessId,type)
		},

		handleOk() {
			this.$refs.table.refresh()
		},
		copySor() {
			if (this.sorSelectedRowKeys.length == 0) {
				this.$message.warn("请至少选中一条数据")
				return
			}
			let select = this.sorSelectedRowKeys
			let params = {}
			params.batteryId = this.record.id
			params.copyIds = select

			copySor(params).then(res => {
				this.sorSelectedRowKeys = []
				this.sorSelectedRow = []
				this.openSor(this.record)
			})
		},
		openExportDesign() {
			batteryDesignRelationOnlyDesignList({ batteryId: this.record.id })
				.then(res => (this.onlyDesignList = res.data))
				.finally(() => (this.designAddVisible = true))
		},
		onSorCellChange(id, key, value) {
			let update = {}
			update.id = id
			update[key] = value.target.value
			batteryDesignRelationUpdate(update)
				.then()
				.finally(() => this.openSor(this.record))
		},
		openEdit() {
			if (this.type == "product") {
				let record = null
				let num = 0
				for (let i = 0; i < this.selectedRow.length; i++) {
					if (this.selectedRow[i].type == "product") {
						num++
						record = this.selectedRow[i]
					}
				}

				if (num != 1) {
					this.$message.warn("请选中一条数据编辑")
					return
				} else {
					this.$refs.editForm.edit(record)
				}
			} else {
				//alert('platform')
				let record = null
				let num = 0
				for (let i = 0; i < this.selectedRow.length; i++) {
					if (this.selectedRow[i].type == "platform") {
						record = this.selectedRow[i]
						num++
					}
				}

				if (num != 1) {
					this.$message.warn("请选中一条数据编辑")
					return
				} else {
					this.$refs.editFormPlatform.edit(record)
				}
			}
		},
		onSorSelectChange(id, key, value) {
			let update = {}
			update.id = id
			update[key] = value
			batteryDesignRelationUpdate(update)
				.then()
				.finally(() => this.openSor(this.record))
		},

    onClose3() {
      this.visible3 = false;
    },
		onDesignSelectChange(id, key, value) {
			let update = {}
			update.inBatteryId = id
			update[key] = value
			batteryDesignRelationUpdateByInBatteryId(update)
				.then()
				.finally(() => this.openDesign(this.record))
		},

		onDesignCellChange(id, key, value) {
			let update = {}
			update.inBatteryId = id
			update[key] = value.target.value
			batteryDesignRelationUpdateByInBatteryId(update)
				.then()
				.finally(() => this.openDesign(this.record))
		},
		onDesignCellChangeById(id, key, value) {
			let update = {}
			update.id = id
			update[key] = value.target.value
			batteryDesignRelationUpdate(update)
				.then()
				.finally(() => this.openDesign(this.record))
		},
		addSor() {
			addSor({ id: this.record.id })
				.then()
				.finally(() => this.openSor(this.record))
		},
		addDesign() {
			addDesign({ id: this.record.id })
				.then()
				.finally(() => this.openDesign(this.record))
		},

		addExportDesign() {
			let select = this.onlyDesignListRowKeys
			let params = {}
			params.batteryId = this.record.id
			params.copyIds = select

			batteryDesignRelationAddExportDesign(params).then(res => {
				this.onlyDesignListRowKeys = []
				this.onlyDesignListRow = []
				this.designAddVisible = false
				this.openDesign(this.record)
			})
		},

		deleteSor() {
			let select = this.sorSelectedRow
			for (let i = 0; i < select.length; i++) {
				let update = {}
				update.id = this.sorSelectedRow[i].id
				update["status"] = 1
				batteryDesignRelationUpdate(update)
					.then()
					.finally(() => {
						this.deleteSorVisible = false
						this.openSor(this.record)
					})
			}
			this.sorSelectedRow = []
			this.sorSelectedRowKeys = []
		},
		deleteSorBefore() {
			if (this.sorSelectedRowKeys.length == 0) {
				this.$message.warn("请选中一条数据")
				return
			} else {
        for (let i = 0; i < this.sorSelectedRow.length; i++) {
          if(!this.sorSelectedRow[i].checkStatus == 0){
            this.$message.warn("只有未提交的数据才能删除")
            return
          }
        }


				this.deleteSorVisible = true
			}
		},
		deleteDesign() {
			let select = this.designSelectedRow

			let filter = []
			for (let i = 0; i < select.length; i++) {
				if (select[i].name == "方案设计") {
					filter.push(select[i])
				}
			}

			for (let i = 0; i < filter.length; i++) {
				let update = {}
				update.inBatteryId = filter[i].inBatteryId
				update["status"] = 1
				batteryDesignRelationUpdateByInBatteryId(update)
					.then()
					.finally(() => this.openDesign(this.record))
			}
			this.designSelectedRow = []
			this.designSelectedRowKeys = []
		},
		deleteExportDesign() {
			let select = this.designExportSelectedRow

			let filter = []
			for (let i = 0; i < select.length; i++) {
				if (select[i].name == "方案设计") {
					filter.push(select[i])
				}
			}

			for (let i = 0; i < filter.length; i++) {
				let update = {}
				update.inBatteryId = filter[i].inBatteryId
				update["status"] = 1
				batteryDesignRelationUpdateByInBatteryId(update)
					.then()
					.finally(() => this.openDesign(this.record))
			}
			this.designSelectedRow = []
			this.designSelectedRowKeys = []
		},

		// 打开sor弹窗
		openSor(record) {
			this.record = record
			batteryDesignRelationSorList({ batteryId: record.id })
				.then(res => {
					if (res.success) {
						this.sorData = res.data
					}
				})
				.then(() => (this.sorVisible = true))

				localStorage.setItem('breadcrumb',JSON.stringify({
				batteryName:record.batteryName,
				type:this.type
			}))
		},
		sorRefresh() {
			batteryDesignRelationSorList({ batteryId: this.record.id }).then(res => {
				if (res.success) {
					this.sorData = res.data
				}
			})
		},
		openDesign(record) {

			this.$router.push({
				path: "/design_battery_index",
				query: {
					batteryId: record.id
				}
			})

			localStorage.setItem('breadcrumb',JSON.stringify({
				batteryName:record.batteryName,
				type:this.type
			}))

			/*   this.record = record
        batteryDesignRelationDesignList({batteryId: record.id,designType:'design'}).then(res => {
          if (res.success) {
            this.designData = res.data

            this.onlyDesignData = []
            for (let i = 0; i < res.data.length; i++) {
              if(res.data[i].name == '方案设计'){
                this.onlyDesignData.push(res.data[i])
              }
            }

            batteryDesignRelationDesignList({batteryId: record.id,designType:'export'}).then(res1 => {
              this.designExportData = res1.data
              this.onlyDesignExportData = []
              for (let i = 0; i < res1.data.length; i++) {
                if(res1.data[i].name == '方案设计'){
                  this.onlyDesignExportData.push(res1.data[i])
                }
              }
            }).finally(() => {
              let trs = document.getElementsByTagName('tr')

              for (let i = 0; i < trs.length; i++) {
                let first = false
                if(trs[i]._prevClass != null && trs[i]._prevClass.indexOf("unSet") != -1){
                  continue
                }
                for (let j = 0; j < this.onlyDesignExportData.length; j++) {
                  if(this.onlyDesignExportData[j].id == trs[i].dataset.rowKey){
                    first = true
                  }
                }

                if(first){
                  trs[i].firstChild.setAttribute('rowspan',3)
                }else{
                  if(trs[i]._prevClass != null && trs[i]._prevClass.indexOf('ant-table-row ant-table-row-level-0') != -1  && trs[i].firstChild.rowSpan != 3){
                    trs[i].firstChild.style.display='none'
                  }


                }
              }
            })


          }



        }).finally(res => {
          let trs = document.getElementsByTagName('tr')

          for (let i = 0; i < trs.length; i++) {
            let first = false
            if(trs[i]._prevClass != null && trs[i]._prevClass.indexOf("unSet") != -1){
              continue
            }
            for (let j = 0; j < this.onlyDesignData.length; j++) {
              if(this.onlyDesignData[j].id == trs[i].dataset.rowKey){
                first = true
              }
            }
            if(first){
              trs[i].firstChild.setAttribute('rowspan',3)
            }else{
              if(trs[i]._prevClass != null && trs[i]._prevClass.indexOf('ant-table-row ant-table-row-level-0') != -1  && trs[i].firstChild.rowSpan != 3){
                trs[i].firstChild.style.display='none'
              }


            }
          }
        })


        this.designVisible = true*/
		},

		designTypeChange(type) {
			if (type == "design") {
				this.changePageCallback()
			} else {
				this.changeExportPageCallback()
			}
		},

		changePage(callback) {
			this.$nextTick(() => {
				callback()
			})
		},
		changePageCallback() {
			let trs = document.getElementsByTagName("tr")

			for (let i = 0; i < trs.length; i++) {
				let first = false
				if (trs[i]._prevClass != null && trs[i]._prevClass.indexOf("unSet") != -1) {
					continue
				}
				for (let j = 0; j < this.onlyDesignData.length; j++) {
					if (this.onlyDesignData[j].id == trs[i].dataset.rowKey) {
						first = true
					}
				}
				if (first) {
					trs[i].firstChild.setAttribute("rowspan", 3)
				} else {
					if (
						trs[i]._prevClass != null &&
						trs[i]._prevClass.indexOf("ant-table-row ant-table-row-level-0") != -1 &&
						trs[i].firstChild.rowSpan != 3
					) {
						trs[i].firstChild.style.display = "none"
					}
				}
			}
		},

		changeSelect(value, record, column) {
			let param = {}
			param[column] = value
			param["id"] = record.id
			sysBatteryDesignEdit(param).then(() => this.$refs.table.refresh())
		},
		changeInput(value, record, column) {
			let param = {}
			param[column] = value.target.value
			param["id"] = record.id
			sysBatteryDesignEdit(param).then(() => this.$refs.table.refresh())
		},

		changeExportPageCallback() {
			let trs = document.getElementsByTagName("tr")

			for (let i = 0; i < trs.length; i++) {
				let first = false

				if (trs[i]._prevClass != null && trs[i]._prevClass.indexOf("unSet") != -1) {
					continue
				}

				for (let j = 0; j < this.onlyDesignExportData.length; j++) {
					if (this.onlyDesignExportData[j].id == trs[i].dataset.rowKey) {
						first = true
					}
				}
				if (first) {
					trs[i].firstChild.setAttribute("rowspan", 3)
				} else {
					if (
						trs[i]._prevClass != null &&
						trs[i]._prevClass.indexOf("ant-table-row ant-table-row-level-0") != -1 &&
						trs[i].firstChild.rowSpan != 3
					) {
						trs[i].firstChild.style.display = "none"
					}
				}
			}
		},

		openAdd() {
			if (this.type == "product") {
				this.$refs.addForm.add()
			} else {
				this.$refs.addFormPlatform.add()
			}
		},
		openCheck(record) {
			if (this.type == "product") {
				this.$refs.checkMan.open(record)
			} else {
				this.$refs.checkManPre.open(record)
			}
		},
    openRole(record) {

      this.$refs.role.open(record.id,record)

		},

		deleteBattery() {
			let select = this.selectedRow
			for (let i = 0; i < select.length; i++) {
				if (select[i].type == this.type) {
					this.designDelete(select[i].id)
				}
			}
		},
		deleteBefore() {
			if (this.selectedRowKeys.length == 0) {
				this.$message.warn("请至少选中一条数据")
				return
			} else {
				this.deleteVisible = true
			}
		},
		copy() {
			if (this.selectedRowKeys.length == 0) {
				this.$message.warn("请至少选中一条数据")
				return
			}

			let params = {}
			params.copyIds = this.selectedRowKeys
			sysBatteryDesignCopy(params).then(res => this.$refs.table.refresh())
		},

		designDelete(id) {
			sysBatteryDesignEdit({ id: id, status: 1 }).then(() => {
				this.deleteVisible = false
				this.$refs.table.refresh()
			})
		},

		submit(id) {
			sysBatteryDesignSubmit({ id: id }).then(() => this.$refs.table.refresh())
		},

		designCopy(record) {
			sysBatteryDesignCopy(record).then(() => this.$refs.table.refresh())
		},

		switchApp() {
			const applicationData = Vue.ls.get(ALL_APPS_MENU)
			this.MenuChange(applicationData[0])
				.then(res => {})
				.catch(err => {
					this.$message.error("错误提示：" + err.message, 1)
				})
		},
		gotoSor(record) {
			window.open("/system_battery_design_sor?batteryId=" + record.inBatteryId + "&type=" + record.stage, "_blank")

			const breadcrumb = JSON.parse(localStorage.getItem('breadcrumb'))

			localStorage.setItem('breadcrumb',JSON.stringify({
				...breadcrumb,
				version:record.version || ''
			}))


			/*this.switchApp()
        this.$router.push({
          path: "/system_battery_design_sor",
          name: "SystemBatteryDesignSor",
          query: {
            batteryId: record.inBatteryId
          }
        });*/
		},
		gotoManager(record) {
			if (record.name == "方案设计") {
				window.open("/battery_design_manager?batteryId=" + record.inBatteryId, "_blank")
			}
			if (record.name == "MI设计") {
				window.open("/g_cylinder_mi_standard_manage?batteryId=" + record.inBatteryId, "_blank")
			}
			if (record.name == "电芯BOM设计") {
				window.open("/sys_battery_design_bom?batteryId=" + record.inBatteryId, "_blank")
			}

			/*this.switchApp()
        this.$router.push({
          path: "/battery_design_manager",
          query: {
            batteryId: record.id
          },
        });*/
		},

		gotoMi(record) {
			//this.switchApp()
			this.$router.push({
				path: "/g_cylinder_mi_standard_manage",
				query: {
					batteryId: record.id
				}
			})
		},
		gotoCheck(record) {
			//this.switchApp()
			this.$router.push({
				path: "/batteryCheckIndex"
			})
		},
		gotoBom(record) {
			//this.switchApp()
			this.$router.push({
				path: "/sys_battery_design_bom",
				query: {
					batteryId: record.id
				}
			})
		}
		/* sysAppDelete (record) {
        this.loading = true
        sysAppDelete(record).then((res) => {
          this.loading = false
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败：' + res.message)
          }
        }).catch((err) => {
          this.$message.error('删除错误：' + err.message)
        })
      } */
	}
}
</script>
<style lang="less" scoped>
.table-operator {
	margin-bottom: 18px;
}

button {
	margin-right: 8px;
}

/deep/ .ant-card-body {
	padding: 10px 24px !important;
}

/deep/ .ant-select-selection--single .ant-select-selection__rendered {
	margin: 0px;

	display: flex;
	justify-content: center;
	align-items: baseline;
}
/deep/ .ant-select-arrow .ant-select-arrow-icon svg {
	display: none;
}
/deep/ .table-page-search-wrapper .ant-form-inline .ant-form-item,
/deep/ .table-page-search-wrapper .table-page-search-submitButtons {
	margin-bottom: 0;
}

/deep/ .ant-col-md-8 {
	padding-top: 10px;
}

/deep/ .ant-form-item-label > label::after {
	content: "";
}


// SOR弹窗不需要padding
/deep/.ant-table-tbody tr td {
    padding: 0;
  }

/deep/
	.ant-table-middle
	> .ant-table-content
	> .ant-table-scroll
	> .ant-table-body
	> table
	> .ant-table-tbody
	> tr
	> td {
	padding: 5.5px 5px;
}



/deep/ .ant-table-pagination.ant-pagination {
	margin: 5px 0;
}

/deep/ .ant-select {
	width: 100%;
}





/*/deep/ .ant-table-middle > .ant-table-content > .ant-table-scroll > */
/deep/ .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab {
	background: rgba(232, 232, 232, 0.5);
	border: 1px solid rgba(232, 232, 232, 0.5);
	font-weight: bold;
}
/deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
	height: 40px;
	color: #1890ff;
	background: #fff;
	border-color: #e8e8e8;
	border-bottom: 1px solid #fff;
}
/deep/ .ant-table-thead > tr > th {
	font-weight: bold;
	background-color: rgba(232, 232, 232, 0.5);
}

input {
	text-align: center;
}

.tips {
	color: #1890ff;
	margin-left: 4px;
}

/deep/ .ant-input[disabled] {
  background-color: white;
  color:rgba(0, 0, 0, 0.65);
}

/deep/.ant-select-disabled .ant-select-selection{
  background-color: white;
  color:rgba(0, 0, 0, 0.65);
}
</style>
