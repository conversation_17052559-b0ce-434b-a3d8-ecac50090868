<template>
  <a-spin :spinning="isLoading" class="hide-progress-parent">
  <div ref="wrapper" class="wrapper">
    <div class="flex-sb-center-row mb10">
      <div class="head_title">{{ "日历寿命: " + (data.reportName || '') }}</div>
    </div>

    <pbiTabs :tabsList="tabsList.filter(item => item.show)" :activeKey="activeKey" @clickTab="changeTab"></pbiTabs>

    <div v-show="activeKey === 'offline'" class="all-wrapper">
      <div class="right-top-div ant-btn-div">
        <a-button size="small" @click="exportCalendarReport"><a-icon type="download"/>导出数据</a-button>
      </div>
      <div class="left-content block" style="border-radius: 0 10px 10px 10px;">
        <div v-for="item in offlineChartList.filter(charObj => charObj.show)">
          <div class="flex-sb-center-row mt2">
            <pageComponent :editObj="item.id" @down="handleDown(item.id)" @edit="handleEditEcharts(item.id)"></pageComponent>
            <div class="ant-btn-div">
              <a-tooltip title="编辑建模参数">
                <a-button size="small" @click="reExport(false)"><a-icon type="edit"/></a-button>
              </a-tooltip>
              <a-popconfirm title="确定要重新生成吗?" ok-text="确定" cancel-text="取消" @confirm="reExport(true)">
                <a-tooltip title="重新生成">
                  <a-button class="ml10" size="small"><a-icon type="sync"/></a-button>
                </a-tooltip>
              </a-popconfirm>
            </div>
          </div>
          <div :ref="item.id" :id="item.id" style="width: 595px; height: 415px; border: 0.5px solid #ccc;"></div>
        </div>
      </div>
      <div class="right-content block">
        <div v-for="item in offlineTableObjList">
          <div class="mt2 table-title-div">{{ item.tableName }}</div>
          <div class="offline-table" style="height: 415px;">
            <a-table :data-source="item.dataList"
                     :columns="item.tableColumns"
                     :rowKey="record => record[item.rowKey]"
                     :pagination="paginationConfig"
                     bordered>
            </a-table>
          </div>
        </div>
      </div>
    </div>

    <div v-show="activeKey === 'online'" class="all-wrapper">
      <div class="right-top-div ant-btn-div">
        <a-button size="small" @click="exportCalendarReport"><a-icon type="download"/>导出数据</a-button>
      </div>
      <div class="left-content block" :style="{borderRadius: tabsList.filter(item => item.value === 'offline' && item.show).length === 0 ? '0 10px 10px 10px' : '10px'}">
        <div v-for="item in editObjList">
          <div class="flex-sb-center-row mt2">
            <pageComponent :editObj="item" @down="handleDown(item)" @edit="handleEditEcharts(item)"></pageComponent>
            <div class="ant-btn-div">
              <a-tooltip title="编辑建模参数">
                <a-button size="small" @click="reExport(false)"><a-icon type="edit"/></a-button>
              </a-tooltip>
              <a-popconfirm title="确定要重新生成吗?" ok-text="确定" cancel-text="取消" @confirm="reExport(true)">
                <a-tooltip title="重新生成">
                  <a-button class="ml10" size="small"><a-icon type="sync"/></a-button>
                </a-tooltip>
              </a-popconfirm>
            </div>
          </div>
          <div :ref="item" :id="item" style="width: 595px; height: 415px; border: 0.5px solid #ccc;"></div>
        </div>
      </div>

      <div class="right-content block">
        <div v-for="item in editObjList">
          <div class="mt2 table-title-div">{{ tableNameMap[item] }}</div>
          <div style="height: 415px;">
            <a-table :data-source="tableList"
                     :columns="getColumns(item)"
                     :rowKey="record => record.day"
                     :pagination="paginationConfig"
                     bordered>
            </a-table>
          </div>
        </div>
      </div>
    </div>

    <div v-show="activeKey === 'picture'">
      <div style="border-radius: 10px; background-color: white;">
        <pbiSearchContainer style="padding: 20px 0px 0px 20px">
          <pbiSearchItem label='累积天数' :span="6">
            <a-select dropdown-class-name="dropdownClassName"
                      v-model="searchParam.days"
                      :style="{width:selectWidth}"
                      allowClear
                      placeholder="请选择累积天数"
                      mode="multiple"
                      :maxTagCount="parseInt(3)"
                      @change="searchByParam">
              <a-select-option v-for="item in pictureStorageDays" :value="item">{{item}}</a-select-option>
            </a-select>
          </pbiSearchItem>
          <pbiSearchItem label='样品编号' :span="6">
            <a-select dropdown-class-name="dropdownClassName"
                      v-model="searchParam.sampleCodes"
                      :style="{width:selectWidth}"
                      allowClear
                      placeholder="请选择样品编号"
                      mode="multiple"
                      :maxTagCount="parseInt(3)"
                      @change="searchByParam">
              <a-select-option v-for="item in allSampleCodeList" :value="item">{{item}}</a-select-option>
            </a-select>
          </pbiSearchItem>
          <pbiSearchItem label='照片类型'  :span="6" >
            <a-select dropdown-class-name="dropdownClassName"
                      v-model="searchParam.pictureTypes"
                      :style="{width:selectWidth}"
                      allowClear
                      placeholder="请选择照片类型"
                      mode="multiple"
                      :maxTagCount="parseInt(4)"
                      @change="searchByParam">
              <a-select-option v-for="item in allPictureTypeNameList" :value="item">{{item}}</a-select-option>
            </a-select>
          </pbiSearchItem>
          <pbiSearchItem :span="6" type='btn'>
            <a-button style="float:right;margin-right: 12px;" type="primary" @click="exportPicture">批量导出照片</a-button>
          </pbiSearchItem>
        </pbiSearchContainer>
        <a-table style="padding: 0px 20px 20px 20px;"
                 ref="pictureTable"
                 :columns="pictureColumns"
                 :rowKey="record => record.rowKey"
                 :scroll="{ x: true }"
                 :row-selection="{
                    selectedRowKeys: pictureSelectedRowKeys, selectedRows: pictureSelectedRows,
                    onChange: onPictureSelectedChange, columnWidth:30}"
                 :data-source="pictureDataList"
                 :pagination="paginationConfig"
                 :loading="pictureTableLoading"
                 bordered>
          <template slot="pictureSlot" slot-scope="text, record, index, columns">
            <div v-if="Array.isArray(record.primaryObjectMap[columns.dataIndex.split('.')[1]][columns.dataIndex.split('.')[2]]) && record.primaryObjectMap[columns.dataIndex.split('.')[1]][columns.dataIndex.split('.')[2]].length > 0 && record.primaryObjectMap[columns.dataIndex.split('.')[1]][columns.dataIndex.split('.')[2]][0].uid" >
              <a-upload
                  list-type="picture-card"
                  class="avatar-uploader"
                  :disabled="true"
                  :fileList="record.primaryObjectMap[columns.dataIndex.split('.')[1]][columns.dataIndex.split('.')[2]]"
                  @preview="handlePreview"
              >
              </a-upload>
              <div style="width: 100%; display: inline-block; margin-bottom: 6px">
                <a-icon @click="downloadSinglePic(record.primaryObjectMap[columns.dataIndex.split('.')[1]][columns.dataIndex.split('.')[2]][0])" style="font-size: 18px; color: #0d84ff;" type="download" />
              </div>
            </div>
          </template>
        </a-table>
        <a-modal :visible="previewVisible" :footer="null" @cancel="previewVisible = false">
          <img alt="example" style="width: 100%" :src="previewImage"/>
        </a-modal>
      </div>
    </div>

    <div v-show="activeKey === 'video'">
      <div class="right-top-div">
        <a-button style="font-size: 12px; border-radius: 4px;" size="small" type="primary" @click="exportVideo">导出视频</a-button>
      </div>
      <div style="border-radius: 10px;height: 800px;background-color: white;">
        <div style="width:750px;float:left;">
          <p style="position: absolute;margin-top: 15px;margin-left:30px;z-index: 2;color: black;font-size: 13px;">提示：当视频无法在线播放时，需先将视频下载到本地设备，之后再进行播放。</p>
          <a-table style="padding: 40px 0px 0px 30px"
                   :columns="videoColumns"
                   :rowKey="record => record.rowKey"
                   :row-selection="{
                    selectedRowKeys: videoSelectedRowKeys, selectedRows: videoSelectedRows,
                    onChange: onVideoSelectedChange, columnWidth:30}"
                   :data-source="videoDataList"
                   :pagination="paginationConfig"
                   bordered>
            <template slot="videoControl" slot-scope="text, record, index, columns">
              <a-icon v-if="record.videoId" @click="playVideo(record)" style="color: #0d84ff; font-size: 25px;" type="caret-right"/>
            </template>
          </a-table>
        </div>
        <div class="videoContent" :style="`width:${videoWidth}px;height:${videoHeight}px;`">
          <video width="100%" height="100%" ref="videoPlayer" autoplay controls playsinline>
            <source :src="iframeUrl" type="video/mp4">
          </video>
        </div>
      </div>
    </div>

    <!-- 在线编辑图表 -->
    <div v-if="drawerVisible">
      <PreviewDrawer
        :screenImageId = "screenImageId"
        :templateParam = "reportChartTemplateList[editObj]"
        :legendNameTypeShow="true"
        :LegendNameTypeList = "this.chartLegendNameListObj[editObj]"
        :legendOptions="legendOptions[editObj]"
        :data="editData[editObj].series"
        :original="originalData[editObj]"
        :editData="editData[editObj]"
        :checkObj="chartCheckObj[editObj]"
        @submit="handleDrawerSubmit"
        @reset="handleDrawerReset"
        @close="() => this.drawerVisible = false"
        @changeTemplate ="handleChangeTemplate"
        @screenshot="handleScreenshot">
      </PreviewDrawer>
    </div>

    <div class="action-bar">
      <pbiReturnTop width="20" height="20" color="#333" @returnTop="handleReturnTop"></pbiReturnTop>
    </div>

  </div>
  </a-spin>
</template>
<script>
import {getDongLiCalendarReport, exportDongLiCalendarReport} from "@/api/modular/system/cycleReportManager";
import {optionMergeCommon} from "@/views/system/vTestReport/mixin/optionMergeCommon";
import {chartTemplate} from "@/views/system/vTestReport/mixin/chartTemplate";
import _ from "lodash";
import {Pagination} from "ant-design-vue";
import {downloadfile1, downloadMinioFile, downloadMinioFileList} from "@/utils/util";
import jsonBigint from "json-bigint";
import moment from "moment";
import pbiTabs from "@/components/pageTool/components/pbiTabs.vue";
import {getMinioDownloadUrl, getMinioPreviewUrl} from "@/api/modular/system/fileManage";

function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}

export default {
  components: {
    pbiTabs,
    'a-pagination': Pagination,
  },
  mixins: [optionMergeCommon,chartTemplate],
  data: function () {
    return {
      isLoading: false,

      activeKey: 'offline',
      tabsList: [
        {value: 'offline', label: 'OCV|ACR|weigh|尺寸类', show: false},
        {value: 'online', label: '容量|能量|DCR', show: true},
        {value: 'picture', label: '照片', show: false},
        {value: 'video', label: '视频', show: false},
      ],

      id: null,
      data: {},
      allDataJson: {},
      testCondition: '',
      dcirTiTleList: [],
      recoveryStepTiTle: '',
      dchCeStepTiTleList: [],
      hasChCeStep: false,

      // 在线容量能量属性
      editObjList: ['capRet', 'capRec', 'energyRet', 'energyRec'],

      tableNameMap: {
        capRet: '保持容量数据',
        capRec: '恢复容量数据',
        energyRet: '保持能量数据',
        energyRec: '恢复能量数据',
      },
      // 表格数据
      tableList: [],
      absoluteTimeMap: {},
      // 表头
      capRetColumns: [
        {
          title: "累积天数/Day",
          align: "center",
          width: "100px",
          dataIndex: "day",
        },
        {
          title: "绝对时间",
          align: "center",
          width: "100px",
          dataIndex: "absoluteTime",
          customRender: (text, record) => {
            let date = moment(text, 'YYYY/MM/DD HH:mm:ss.SSS')
            return date.isValid() ? date.format('YYYY-MM-DD HH:mm:ss') : text
          }
        },
        {
          title: "保持容量/Ah",
          align: "center",
        },
        {
          title: "容量保持率/%",
          align: "center",
        }
      ],
      capRecColumns: [
        {
          title: "累积天数/Day",
          align: "center",
          width: "100px",
          dataIndex: "day",
        },
        {
          title: "绝对时间",
          align: "center",
          width: "100px",
          dataIndex: "absoluteTime",
          customRender: (text, record) => {
            let date = moment(text, 'YYYY/MM/DD HH:mm:ss.SSS')
            return date.isValid() ? date.format('YYYY-MM-DD HH:mm:ss') : text
          }
        },
        {
          title: "恢复容量/Ah",
          align: "center",
        },
        {
          title: "容量恢复率/%",
          align: "center",
        },
      ],
      energyRetColumns: [
        {
          title: "累积天数/Day",
          align: "center",
          width: "100px",
          dataIndex: "day",
        },
        {
          title: "绝对时间",
          align: "center",
          width: "100px",
          dataIndex: "absoluteTime",
          customRender: (text, record) => {
            let date = moment(text, 'YYYY/MM/DD HH:mm:ss.SSS')
            return date.isValid() ? date.format('YYYY-MM-DD HH:mm:ss') : text
          }
        },
        {
          title: "保持能量/Wh",
          align: "center",
        },
        {
          title: "能量保持率/%",
          align: "center",
        }
      ],
      energyRecColumns: [
        {
          title: "累积天数/Day",
          align: "center",
          width: "100px",
          dataIndex: "day",
        },
        {
          title: "绝对时间",
          align: "center",
          width: "100px",
          dataIndex: "absoluteTime",
          customRender: (text, record) => {
            let date = moment(text, 'YYYY/MM/DD HH:mm:ss.SSS')
            return date.isValid() ? date.format('YYYY-MM-DD HH:mm:ss') : text
          }
        },
        {
          title: "恢复能量/Wh",
          align: "center",
        },
        {
          title: "能量恢复率/%",
          align: "center",
        },
      ],
      paginationConfig: {
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '30', '40', '50'], // 显示的每页数量选项
        size: "small",
        showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
      },

      titleDataObj: {
        capRet:    {
          chartTitle: 'Calendar Life',
          XTitle: 'Storage Time / D',
          YTitle: 'Capacity Retention Rate / %',
          tooltipPrefix: '容量保持率 ',
          tooltipUnit: ' %',
          hasYTitle2: false,
          YTitle2: null,
        },
        capRec:    {
          chartTitle: 'Calendar Life',
          XTitle: 'Storage Time / D',
          YTitle: 'Capacity Recovery Rate / %',
          tooltipPrefix: '容量恢复率 ',
          tooltipUnit: ' %',
          hasYTitle2: true,
          YTitle2: 'DCIR Increase Rate / %',
          tooltipPrefix2: 'DCIR增长率 ',
          tooltipUnit2: ' %',
        },
        energyRet: {
          chartTitle: 'Calendar Life',
          XTitle: 'Storage Time / D',
          YTitle: 'Energy Retention Rate / %',
          tooltipPrefix: '能量保持率 ',
          tooltipUnit: ' %',
          hasYTitle2: false,
          YTitle2: null,
        },
        energyRec: {
          chartTitle: 'Calendar Life',
          XTitle: 'Storage Time / D',
          YTitle: 'Energy Recovery Rate / %',
          tooltipPrefix: '能量恢复率 ',
          tooltipUnit: ' %',
          hasYTitle2: true,
          YTitle2: 'DCIR Increase Rate / %',
          tooltipPrefix2: 'DCIR增长率 ',
          tooltipUnit2: ' %',
        },
        voltage: {
          chartTitle: 'Calendar Life_OCV',
          XTitle: 'Storage Time / D',
          YTitle: 'OCV / mV',
          tooltipPrefix: '',
          tooltipUnit: '',
          hasYTitle2: false,
          YTitle2: null,
        },
        innerres: {
          chartTitle: 'Calendar Life_ACR',
          XTitle: 'Storage Time / D',
          YTitle: 'ACR / mΩ',
          tooltipPrefix: '',
          tooltipUnit: '',
          hasYTitle2: false,
          YTitle2: null,
        },
        height: {
          chartTitle: 'Calendar Life_Size',
          XTitle: 'Storage Time / D',
          YTitle: 'Size / mm',
          tooltipPrefix: '',
          tooltipUnit: '',
          hasYTitle2: false,
          YTitle2: null,
        },
        volume: {
          chartTitle: 'Calendar Life_Volume',
          XTitle: 'Storage Time / D',
          YTitle: 'Volume / g',
          tooltipPrefix: '',
          tooltipUnit: '',
          hasYTitle2: false,
          YTitle2: null,
        },
        weight: {
          chartTitle: 'Calendar Life_Weight',
          XTitle: 'Storage Time / D',
          YTitle: 'Weight / g',
          tooltipPrefix: '',
          tooltipUnit: '',
          hasYTitle2: true,
          YTitle2: 'Mass Loss Rate / %',
          tooltipPrefix2: '失重率 ',
          tooltipUnit2: ' %',
        },
        isolateres: {
          chartTitle: 'Calendar Life_Insulation Resistance',
          XTitle: 'Storage Time / D',
          YTitle: 'Insulation Resistance / mΩ',
          tooltipPrefix: '',
          tooltipUnit: '',
          hasYTitle2: false,
          YTitle2: null,
        },
      },
      echartsColorList: [
        "#c00000",
        "#0070c0",
        "#808080",
        "#7030a0",
        "#4472c4",
        "#a5a5a5",
        "#ed7d31",
        "#5b9bd5",
        "#70ad47",
        "#000000",
        "#ff9999",
        "#ffc000",
        "#00b050",
      ],
      // 折点类型数组 三角、圆形、矩形、菱形、箭头、图钉、【空心：三角、圆形、矩形、菱形、箭头、图钉】、倒三角、五角星
      echartsSymbolList: [
        'triangle', 'circle', 'rect', 'diamond', 'arrow', 'pin',
        'emptyTriangle', 'emptyCircle', 'emptyRect', 'emptyDiamond', 'emptyArrow', 'emptyPin',
        'path://M0,0 L10,0 L5,10 Z',
        'path://M100,22.4 L78.6,54.6 L44.2,54.6 L72.6,79.4 L62.8,112.6 L100,92.4 L137.2,112.6 L127.4,79.4 L155.8,54.6 L121.4,54.6 Z'
      ],

      // 离线OCV|ACR属性
      offlineTableList: [],
      offlineSampleKeyMap: {},

      offlineTableObjList: [],
      offlineChartList: [
        {id: 'voltage', show: true},
        {id: 'innerres', show: true},
        {id: 'height', show: false},
        {id: 'volume', show: false},
        {id: 'weight', show: false},
        {id: 'isolateres', show: false},
      ],

      // 图片数据属性
      previewVisible: false,
      previewImage: '',
      searchParam: {
        days: [],
        sampleCodes: [],
        pictureTypes: [],
      },
      selectWidth: '260px',
      pictureStorageDays: [],
      allSampleCodeList: [],
      allPictureTypeNameList: [],
      pictureSelectedRowKeys: [],
      pictureSelectedRows: [],
      pictureDataList: [],
      pictureColumns: [],
      pictureTableLoading: false,

      // 视频数据属性
      videoWidth: 0,
      videoHeight: 0,
      iframeUrl: null,
      videoSelectedRowKeys: [],
      videoSelectedRows: [],
      videoDataList: [],
      videoColumns: [
        {
          title: "存储阶段",
          dataIndex: "storeStage",
          width: 80,
          align: "center",
        },
        {
          title: "天数",
          dataIndex: "dayRange",
          width: 60,
          align: "center"
        },
        {
          title: "绝对时间",
          dataIndex: "absoluteDate",
          width: 140,
          align: "center"
        },
        {
          title: "名称",
          dataIndex: "videoName",
          width: 200,
          align: "center"
        },
        {
          title: "操作",
          width: 100,
          align: "center",
          scopedSlots: { customRender: 'videoControl' },
        }
      ],

      echartObj: {},

      editObj: null,
      drawerVisible: false,

      firstInit: {
        capRet: true,
        capRec: true,
        energyRet: true,
        energyRec: true,
        voltage: true,
        innerres: true,
        height: true,
        volume: true,
        weight: true,
        isolateres: true,
      },

      chartLegendNameListObj: {
        capRet: [],
        capRec: [],
        energyRet: [],
        energyRec: [],
        voltage: [],
        innerres: [],
        height: [],
        volume: [],
        weight: [],
        isolateres: [],
      }, //不同类型图例的汇总  合并
      originalLegent: {}, //原始图例
      originalData: {}, //原始数据
      editData: {}, //编辑数据
      chartCheckObj: {
        capRet: {},
        capRec:  {},
        energyRet:  {},
        energyRec:  {},
        voltage:  {},
        innerres:  {},
        height:  {},
        volume:  {},
        weight:  {},
        isolateres:  {},
      },
      legendOptions:{} //图例-数据的选择项
    };
  },
  created() {
    this.initBodySize()
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.isLoading = true
      this.id = this.$route.query.id

      getDongLiCalendarReport({ id: this.id })
        .then(res => {
          if (res.data) {
            this.data = res.data
            let json = jsonBigint({storeAsString: true})
            this.allDataJson = json.parse(res.data.allDataJson)
            this.dcirTiTleList = Array.isArray(this.allDataJson.dcirTiTleList) ? this.allDataJson.dcirTiTleList.filter(item => typeof item === 'string' && item.length > 0) : []
            this.recoveryStepTiTle = this.allDataJson.recoveryStepTiTle
            this.dchCeStepTiTleList = Array.isArray(this.allDataJson.dchCeStepTiTleList) ? this.allDataJson.dchCeStepTiTleList.filter(item => typeof item === 'string' && item.length > 0) : []
            this.hasChCeStep = this.allDataJson.hasChCeStep
            this.tableList = this.allDataJson.tableList
            for (let j = 0; j < this.tableList.length; j++) {
              const rowObj = this.tableList[j]
              this.absoluteTimeMap[rowObj.day] = rowObj.absoluteTime
            }

            this.offlineTableList = this.allDataJson.offlineTableList || []
            this.offlineSampleKeyMap = this.allDataJson.offlineSampleKeyMap || {}

            if (this.allDataJson.hasOfflineData) {
              this.tabsList[0].show = true
            }
            if (this.allDataJson.hasPictureData) {
              const offlineStorageDayMap = this.allDataJson.offlineStorageDayMap || {}
              // 获取图片数据
              this.pictureStorageDays = offlineStorageDayMap.picture.sort((a, b) => {return parseInt(a) - parseInt(b)}) || []
              // 初次获取第一天和最后一天
              if (this.pictureStorageDays.length > 2) {
                this.searchParam.days.push(this.pictureStorageDays[0])
                this.searchParam.days.push(this.pictureStorageDays[this.pictureStorageDays.length - 1])
              }
              this.pictureDataList = this.offlineTableList.filter(rptObj => {
                return this.searchParam.days.includes(rptObj.day) && (rptObj.day == 0 || rptObj.rawDay == 0 || rptObj.hasPicture)
              })
              this.allSampleCodeList = Object.values(this.offlineSampleKeyMap).map(item => item.sampleCode).sort()
              this.allPictureTypeNameList = this.allDataJson.allPictureTypeNameList || []
              this.tabsList[2].show = true
            }
            if (this.allDataJson.hasVideoData) {
              this.videoDataList = this.allDataJson.videoTableList || []
              this.tabsList[3].show = true
            }
            this.activeKey = this.tabsList.filter(item => item.show)[0].value

            this.testCondition = this.allDataJson.testCondition || ''
            if (this.testCondition) {
              Object.values(this.titleDataObj).forEach(echartInfo => {
                echartInfo.chartTitle = this.testCondition + '_' + echartInfo.chartTitle
              })
            }
            const offlineStorageDayMap = this.allDataJson.offlineStorageDayMap || {}
            let verifyList = ['height', 'volume', 'weight', 'isolateres']
            verifyList.forEach((item, index) => {
              if (Array.isArray(offlineStorageDayMap[item]) && offlineStorageDayMap[item].length > 0) {
                this.offlineChartList[index + 2].show = true
              }
            })
          }
        })
        .then(async () => {

          if (this.tabsList[0]) {
            await this.getChartTemplateRelationList(this.$route.query.id,this.offlineChartList.map(item => item.id))
            // 离线数据表格初始化
            this.initOfflineTable()
            // Echarts图初始化
            this.offlineChartList.forEach(item => {
              if (item.show) {
                const editObj = item.id
                this.initEchart(editObj, `${editObj}EchartList`, editObj === 'weight' ? 'weightLossRateEchartList' : null, editObj === 'weight')
              }
            })
          }

          if (this.tabsList[1]) {
            await this.getChartTemplateRelationList(this.$route.query.id,this.editObjList)

            // 容量能量表格数据初始化
            this.initOnlineTable()
            this.editObjList.forEach(editObj => {
              this.initEchart(editObj, `${editObj}RateEchartList`, editObj.includes('Rec') ? 'dcirIncRateEchartList' : null, editObj.includes('Rec'))
            })
          }

          if (this.tabsList[2]) {
            // 图片表格数据初始化
            this.initPictureTable()
          }

          // 字体渲染不正确，重新setOption
          document.fonts.ready.then(() => {
            this.offlineChartList.forEach(item => {
              if (item.show) {
                this.echartObj[item.id].setOption({
                  textStyle: {
                    fontFamily: "Times New Roman"
                  }
                })
              }
            })
            this.editObjList.forEach(editObj => {
              this.echartObj[editObj].setOption({
                textStyle: {
                  fontFamily: "Times New Roman"
                }
              })
            })
          })

        })
        .finally(() => {
          this.isLoading = false
        })
    },
    
    
    initBodySize() {
      let initWidth = document.documentElement.clientWidth // 拿到父元素宽
      let initHeight = document.documentElement.clientHeight // 拿到父元素高
      if (initWidth <= 1280) {
        this.videoWidth = initWidth * 0.35
        this.videoHeight = initHeight * 0.6
        this.selectWidth = '200px'
      } else {
        this.videoWidth = initWidth * 0.56
        this.videoHeight = initHeight * 0.65
        this.selectWidth = '260px'
      }
    },
    changeTab(value) {
      this.activeKey = value
    },

    // 图片tab相关方法
    onPictureSelectedChange (selectedRowKeys, selectedRows) {
      this.pictureSelectedRowKeys = selectedRowKeys
      this.pictureSelectedRows = selectedRows
    },
    searchByParam() {
      // 过滤天数（过滤dataList）、过滤样品、过滤照片类型（重置columns）
      this.initPictureTable()
    },
    async handlePreview(file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj);
      }
      this.previewImage = file.url || file.preview;
      this.previewVisible = true;
    },
    downloadSinglePic(file) {
      getMinioDownloadUrl(file.uid,encodeURIComponent(file.name)).then(res1 => {
        downloadMinioFile(res1.data)
      })
    },
    async exportPicture() {
      if (this.pictureSelectedRows.length === 0) {
        this.$message.warning('请至少选择一条数据')
        return
      }

      // 如果做了筛选，则导出为筛选后的照片
      // 过滤样品、照片类型
      let allSampleCodeList = this.allSampleCodeList
      let allPictureTypeNameList = this.allPictureTypeNameList
      if (Array.isArray(this.searchParam.sampleCodes) && this.searchParam.sampleCodes.length > 0) {
        allSampleCodeList = this.searchParam.sampleCodes
      }
      if (Array.isArray(this.searchParam.pictureTypes) && this.searchParam.pictureTypes.length > 0) {
        allPictureTypeNameList = this.searchParam.pictureTypes
      }
      let downloadPicList = []
      this.pictureSelectedRows.forEach(rowObj => {
        if (rowObj.primaryObjectMap) {
          allSampleCodeList.forEach(sampleCode => {
            let sampleKey = ''
            for (const key in this.offlineSampleKeyMap) {
              if (this.offlineSampleKeyMap[key].sampleCode === sampleCode) {
                sampleKey = key;
                break;
              }
            }

            if (sampleKey !== '' && sampleKey in rowObj.primaryObjectMap) {
              const sampleObj = rowObj.primaryObjectMap[sampleKey]
              allPictureTypeNameList.forEach(pictureType => {
                if (Array.isArray(sampleObj[pictureType]) && sampleObj[pictureType].length > 0 && sampleObj[pictureType][0].uid) {
                  downloadPicList.push(sampleObj[pictureType][0])
                }
              })
            }
          })
        }
      })

      if (downloadPicList.length === 0) {
        this.$message.warning('当前没有可导出的照片')
      } else {
        let urlList = []
        for (let i = 0; i < downloadPicList.length; i++) {
          let v = downloadPicList[i]
          await getMinioDownloadUrl(v.uid,encodeURIComponent(v.name)).then(res1 => {
            urlList.push(res1.data)
          })
        }

        downloadMinioFileList(urlList)
      }
    },

    // 视频tab相关方法
    onVideoSelectedChange (selectedRowKeys, selectedRows) {
      this.videoSelectedRowKeys = selectedRowKeys
      this.videoSelectedRows = selectedRows
    },
    playVideo (record) {
      if (record.videoId) {
        getMinioPreviewUrl(record.videoId).then(res => {
          this.iframeUrl = res.data.replace("http://10.100.1.99:9000/", "/minioDownload/")
          const videoPlayer = this.$refs.videoPlayer;
          if (videoPlayer) {
            videoPlayer.load();
          }
        })
      }
    },
    async exportVideo() {
      if (this.videoSelectedRows.length === 0) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      let urlList = []
      for (let i = 0; i < this.videoSelectedRows.length; i++) {
        let v = this.videoSelectedRows[i]
        if (v.videoId) {
          await getMinioDownloadUrl(v.videoId,encodeURIComponent(v.videoName)).then(res1 => {
            urlList.push(res1.data)
          })
        }
      }
      if (urlList.length > 0) {
        downloadMinioFileList(urlList)
      } else {
        this.$message.warning('当前没有可导出的视频')
      }
    },

    getColumns(item) {
      return this[item + 'Columns'];
    },

    initPictureTable() {
      this.pictureTableLoading = true

      // 过滤天数
      const offlineTableList = this.offlineTableList
      if (Array.isArray(this.searchParam.days) && this.searchParam.days.length > 0) {
        this.pictureDataList = offlineTableList.filter(rptObj => {
          return this.searchParam.days.includes(rptObj.day) && (rptObj.day == 0 || rptObj.rawDay == 0 || rptObj.hasPicture)
        })
      } else {
        this.pictureDataList = offlineTableList.filter(rptObj => {
          return this.pictureStorageDays.includes(rptObj.day) && (rptObj.day == 0 || rptObj.rawDay == 0 || rptObj.hasPicture)
        })
      }
      // 过滤样品、照片类型
      let allSampleCodeList = this.allSampleCodeList
      let allPictureTypeNameList = this.allPictureTypeNameList
      if (Array.isArray(this.searchParam.sampleCodes) && this.searchParam.sampleCodes.length > 0) {
        allSampleCodeList = this.searchParam.sampleCodes
      }
      if (Array.isArray(this.searchParam.pictureTypes) && this.searchParam.pictureTypes.length > 0) {
        allPictureTypeNameList = this.searchParam.pictureTypes
      }

      const offlineSampleKeyMap = this.allDataJson.offlineSampleKeyMap || {}
      this.pictureColumns = [
        {
          title: "累积天数/Day",
          align: "center",
          width: "100px",
          dataIndex: "day"
        },
        {
          title: "绝对时间",
          align: "center",
          width: "100px",
          dataIndex: "absoluteTime"
        },
      ]
      for (let sampleIndex = 0; sampleIndex < allSampleCodeList.length; sampleIndex++) {
        const sampleCode = allSampleCodeList[sampleIndex]
        let sampleKey = ''
        for (const key in offlineSampleKeyMap) {
          if (offlineSampleKeyMap[key].sampleCode === sampleCode) {
            sampleKey = key;
            break;
          }
        }

        if (sampleKey === '') {
          continue;
        }

        const sampleInfo = offlineSampleKeyMap[sampleKey] || {}
        allPictureTypeNameList.forEach((pictureType, typeIndex) => {
          if (typeIndex === 0) {
            this.pictureColumns.push(
                this.getChildren(sampleKey, sampleInfo.sampleCode, sampleInfo.batteryCode, sampleInfo.testStatus, sampleInfo.statusTip, pictureType, pictureType)
            )
          } else {
            let children = sampleInfo.sampleCode === sampleInfo.batteryCode ? this.pictureColumns[2 + sampleIndex].children[0].children : this.pictureColumns[2 + sampleIndex].children[0].children[0].children
            children.push(
                {
                  title: pictureType,
                  dataIndex: `primaryObjectMap.${sampleKey}.${pictureType}`,
                  align: "center",
                  width: "100px",
                  scopedSlots: {
                    customRender: "pictureSlot"
                  }
                }
            )
          }
        })
      }

      this.pictureTableLoading = false
    },

    initOfflineTable() {

      const offlineEditObjList = ['voltage', 'innerres', 'height', 'volume', 'weight', 'isolateres']
      const nameList = ['电压/mV','内阻/mΩ','尺寸/mm','产气量/g','重量/g','绝缘阻值/mΩ']
      const offlineTableList = this.offlineTableList
      const offlineSampleKeyMap = this.allDataJson.offlineSampleKeyMap || {}
      const offlineSampleKeyList = this.allDataJson.offlineSampleKeyList || []
      const offlineStorageDayMap = this.allDataJson.offlineStorageDayMap || {}
      const allHeightColNameList = this.allDataJson.allHeightColNameList || []
      this.offlineTableObjList = []
      offlineEditObjList.forEach((item, index) => {
        if (['voltage', 'innerres'].includes(item) || (Array.isArray(offlineStorageDayMap[item]) && offlineStorageDayMap[item].length > 0)) {
          const tableName = nameList[index].split('/')[0] + '数据'

          let tableObj = {rawKey: 'rowKey', tableName: tableName}

          let tableColumns = [
            {
              title: '累积天数/Day',
              align: "center",
              width: 100,
              dataIndex: 'day',
            },
            {
              title: '出箱后/中检后',
              align: "center",
              width: 100,
              dataIndex: 'beforeOrAfterBox',
            },
            {
              title: '绝对时间',
              align: "center",
              width: 100,
              dataIndex: 'absoluteTime',
            },
            {
              title: nameList[index],
              align: "center",
              children: [],
            }
          ]

          if (item === 'height') {
            allHeightColNameList.forEach(sizeType => {
              // 含有":trim"字样时图例名称去除后缀
              let sizeTypeName = sizeType
              if (sizeType.includes(':trim') && sizeType.lastIndexOf('-') !== -1) {
                sizeTypeName = sizeType.substr(0, sizeType.lastIndexOf('-'))
              }
              tableColumns[3].children.push(
                  {
                    title: sizeTypeName,
                    align: "center",
                    children: []
                  }
              )
            })
          } else if (item === 'weight') {
            tableColumns.push(
                {
                  title: "失重率/%",
                  align: "center",
                  children: [],
                }
            )
          }

          offlineSampleKeyList.forEach(sampleKey => {
            const sampleInfoObj = offlineSampleKeyMap[sampleKey]
            if (item === 'height') {
              for (let i = 0; i < allHeightColNameList.length; i++) {
                tableColumns[3].children[i].children.push(
                    this.getChildren(sampleInfoObj.sampleKey, sampleInfoObj.sampleCode, sampleInfoObj.batteryCode, sampleInfoObj.testStatus, sampleInfoObj.statusTip, allHeightColNameList[i].replace(':trim', ''))
                )
              }
            } else {
              tableColumns[3].children.push(
                  this.getChildren(sampleInfoObj.sampleKey, sampleInfoObj.sampleCode, sampleInfoObj.batteryCode, sampleInfoObj.testStatus, sampleInfoObj.statusTip, item)
              )
              if (item === 'weight') {
                tableColumns[4].children.push(
                    this.getChildren(sampleInfoObj.sampleKey, sampleInfoObj.sampleCode, sampleInfoObj.batteryCode, sampleInfoObj.testStatus, sampleInfoObj.statusTip, 'weightLossRate')
                )
              }
            }
          })

          tableObj.tableColumns = tableColumns
          tableObj.dataList = ['voltage', 'innerres'].includes(item) ? offlineTableList : offlineTableList.filter(rptObj => {
            return offlineStorageDayMap[item].includes(rptObj.day) && (rptObj.day == 0 || rptObj.rawDay == 0 || rptObj.beforeOrAfterBox != '中检后')
          })

          this.offlineTableObjList.push(tableObj)
        }
      })

    },

    initOnlineTable() {
      // 以初检RPT电芯为准，处理表头
      const sampleKeyInfoMap = this.allDataJson.sampleKeyInfoMap || {}
      const offlineSampleKeyMap = this.allDataJson.offlineSampleKeyMap || {}
      const sampleKeyList = this.allDataJson.sampleKeyList || []
      if (this.allDataJson.tableList && this.allDataJson.tableList.length > 0) {

        if (sampleKeyList.length > 0) {
          const dcirCalcType = this.allDataJson.dcirCalcType || "DongLi"
          const titleList = dcirCalcType === "V" ? ["_U1-DCH/V", "_I1-DCH/A", "_U2-DCH/V", "_I2-DCH/A", "_DCIR/mΩ", "_DCIR增长率/%"] : ["_U1-rest/V", "_U2-DCH/V", "_I2-DCH/A", "_DCIR/mΩ", "_DCIR增长率/%"]
          // children初始化
          this.capRetColumns[2].children = []
          this.capRetColumns[3].children = []
          this.capRecColumns[2].children = []
          this.capRecColumns[3].children = []
          this.energyRetColumns[2].children = []
          this.energyRetColumns[3].children = []
          this.energyRecColumns[2].children = []
          this.energyRecColumns[3].children = []

          if (this.recoveryStepTiTle) {
            this.capRecColumns[2].title = this.recoveryStepTiTle + '_' + '恢复容量/Ah'
            this.energyRecColumns[2].title = this.recoveryStepTiTle + '_' + '恢复能量/Wh'
          }
          for (let i = 0; i < this.dchCeStepTiTleList.length; i++) {
            this.capRecColumns.push(
                {
                  title: this.dchCeStepTiTleList[i] + "_放电容量/Ah",
                  align: "center",
                  children: []
                }
            )
            this.energyRecColumns.push(
                {
                  title: this.dchCeStepTiTleList[i] + "_放电能量/Wh",
                  align: "center",
                  children: []
                }
            )
          }

          this.dcirTiTleList.forEach(dcirTiTle => {

            const columns = [...titleList.map(title => {
                  return {
                    title: dcirTiTle + title,
                    align: "center",
                    children: [],
                  }
                }
            )]

            this.capRecColumns.push(...columns)
            this.energyRecColumns.push(...columns)
          })

          if (this.hasChCeStep) {
            this.capRecColumns.push(
                {
                  title: "充电容量/Ah",
                  align: "center",
                  children: []
                },
                {
                  title: "充电恒流比",
                  align: "center",
                  children: []
                }
            )
            this.energyRecColumns.push(
                {
                  title: "充电能量/Wh",
                  align: "center",
                  children: []
                }
            )
          }
        }

        const dcirCalcType = this.allDataJson.dcirCalcType || "DongLi"
        const keyTypeList = dcirCalcType === "V" ? ["dchVoltage", "dchCurrent", "dchVoltage2-", "dchCurrent2-", "dcir2-", "dcirIncRate"] : ["restVoltage", "dchVoltage", "dchCurrent", "dcir", "dcirIncRate"]
        sampleKeyList.forEach(sampleKey => {
          const sampleInfo = sampleKeyInfoMap[sampleKey]
          const statusInfo = offlineSampleKeyMap[sampleKey] || {}

          this.capRetColumns[2].children.push(
              this.getChildren(sampleInfo.sampleKey, sampleInfo.sampleCode, sampleInfo.batteryCode, statusInfo.testStatus, statusInfo.statusTip, "retCap")
          )
          this.capRetColumns[3].children.push(
              this.getChildren(sampleInfo.sampleKey, sampleInfo.sampleCode, sampleInfo.batteryCode, statusInfo.testStatus, statusInfo.statusTip, "capRetRate")
          )

          this.energyRetColumns[2].children.push(
              this.getChildren(sampleInfo.sampleKey, sampleInfo.sampleCode, sampleInfo.batteryCode, statusInfo.testStatus, statusInfo.statusTip, "retEnergy")
          )
          this.energyRetColumns[3].children.push(
              this.getChildren(sampleInfo.sampleKey, sampleInfo.sampleCode, sampleInfo.batteryCode, statusInfo.testStatus, statusInfo.statusTip, "energyRetRate")
          )

          this.capRecColumns[2].children.push(
              this.getChildren(sampleInfo.sampleKey, sampleInfo.sampleCode, sampleInfo.batteryCode, statusInfo.testStatus, statusInfo.statusTip, "recCap")
          )
          this.capRecColumns[3].children.push(
              this.getChildren(sampleInfo.sampleKey, sampleInfo.sampleCode, sampleInfo.batteryCode, statusInfo.testStatus, statusInfo.statusTip, "capRecRate")
          )

          this.energyRecColumns[2].children.push(
              this.getChildren(sampleInfo.sampleKey, sampleInfo.sampleCode, sampleInfo.batteryCode, statusInfo.testStatus, statusInfo.statusTip, "recEnergy")
          )
          this.energyRecColumns[3].children.push(
              this.getChildren(sampleInfo.sampleKey, sampleInfo.sampleCode, sampleInfo.batteryCode, statusInfo.testStatus, statusInfo.statusTip, "energyRecRate")
          )

          for (let j = 0; j < this.dchCeStepTiTleList.length; j++) {
            this.capRecColumns[4 + j].children.push(
                this.getChildren(sampleInfo.sampleKey, sampleInfo.sampleCode, sampleInfo.batteryCode, statusInfo.testStatus, statusInfo.statusTip, 'dchCapacity'+j)
            )
            this.energyRecColumns[4 + j].children.push(
                this.getChildren(sampleInfo.sampleKey, sampleInfo.sampleCode, sampleInfo.batteryCode, statusInfo.testStatus, statusInfo.statusTip, 'dchEnergy'+j)
            )
          }

          this.dcirTiTleList.forEach((dcirTiTle, dcirIndex) => {
            keyTypeList.forEach((keyType, keyIndex) => {
              this.capRecColumns[4 + this.dchCeStepTiTleList.length + dcirIndex * keyTypeList.length  + keyIndex].children.push(
                  this.getChildren(sampleInfo.sampleKey, sampleInfo.sampleCode, sampleInfo.batteryCode, statusInfo.testStatus, statusInfo.statusTip, keyType + dcirIndex)
              )
            })
          })

          if (this.hasChCeStep) {
            this.capRecColumns[4 + this.dchCeStepTiTleList.length + this.dcirTiTleList.length * keyTypeList.length].children.push(
                this.getChildren(sampleInfo.sampleKey, sampleInfo.sampleCode, sampleInfo.batteryCode, statusInfo.testStatus, statusInfo.statusTip, 'chCapacity')
            )
            this.capRecColumns[5 + this.dchCeStepTiTleList.length + this.dcirTiTleList.length * keyTypeList.length].children.push(
                this.getChildren(sampleInfo.sampleKey, sampleInfo.sampleCode, sampleInfo.batteryCode, statusInfo.testStatus, statusInfo.statusTip, 'chCccapacityRate')
            )
            this.energyRecColumns[4 + this.dchCeStepTiTleList.length + this.dcirTiTleList.length * keyTypeList.length].children.push(
                this.getChildren(sampleInfo.sampleKey, sampleInfo.sampleCode, sampleInfo.batteryCode, statusInfo.testStatus, statusInfo.statusTip, 'chEnergy')
            )
          }
        })
      }

    },
    getChildren(sampleKey, sampleCode, batteryCode, batteryStatus, statusTip, columnKey, pictureTypeName = null) {
      // 前端表头状态转换
      if (["earlyEnd", "batteryDisassembly", "pressureDrop", "abnormalHot", "openShellAndLeak", "shellRust", "operationError", "thermalRunaway", "acrException"].includes(batteryStatus)) {
        batteryStatus = "Stop"
      } else if (batteryStatus === "ongoing") {
        batteryStatus = "Ongoing"
      }

      let result
      if (this.allDataJson.hasOfflineData) {
        if (pictureTypeName != null) {
          result = {
            title: batteryCode,
            align: "center",
            width: "100px",
            children: [
              {
                title: <a-tooltip title={statusTip}>{batteryStatus}</a-tooltip>,
                align: "center",
                width: "100px",
                children: [
                  {
                    title: pictureTypeName,
                    dataIndex: `primaryObjectMap.${sampleKey}.${columnKey}`,
                    align: "center",
                    width: "100px",
                    scopedSlots: {
                      customRender: "pictureSlot"
                    }
                  }
                ]
              }
            ]
          }
        } else {
          result = {
            title: batteryCode,
            align: "center",
            width: "100px",
            children: [
              {
                title: <a-tooltip title={statusTip}>{batteryStatus}</a-tooltip>,
                align: "center",
                width: "100px",
                customRender: (text, record) => {
                  return sampleKey in record.primaryObjectMap ? record.primaryObjectMap[sampleKey][columnKey] : ''
                }
              }
            ]
          }
        }
      } else {
        result = {
          title: batteryCode,
          align: "center",
          width: "100px",
          customRender: (text, record) => {
            return sampleKey in record.primaryObjectMap ? record.primaryObjectMap[sampleKey][columnKey] : ''
          }
        }
      }

      if (sampleCode !== batteryCode) {
        result = {
          title: sampleCode,
          align: "center",
          width: "100px",
          children: [
            result
          ]
        }
      }

      return result
    },

    handleInitChart(targetObj) {
      const suffix = ['capRet', 'capRec', 'energyRet', 'energyRec'].includes(targetObj) ? 'RateEchartList' : 'EchartList'
      this.initEchart(targetObj, `${targetObj}${suffix}`, targetObj.includes('Rec') ? 'dcirIncRateEchartList' : targetObj === 'weight' ? 'weightLossRateEchartList' : null, targetObj.includes('Rec') || targetObj === 'weight')
    },
    initEchart(targetObj, yAxisOneListName = null, yAxisTwoListName = null, hasYAxisTwo) {
      // 检查是否存在 ECharts 实例 并清除
      if (this.echartObj[targetObj]) {
        this.echartObj[targetObj].dispose();
      }

      // this.echartObj[targetObj] = this.echarts.init(document.getElementById(targetObj), 'walden', { renderer: "svg" }) // 测试
      this.echartObj[targetObj] = this.echarts.init(document.getElementById(targetObj), 'walden', {devicePixelRatio: 2, width: 593, height:413})

      // 模板数据

      const templateParam = this.reportChartTemplateList[targetObj].templateParamJson
      const originalParam = this.reportChartTemplateList[targetObj].originalParamJson
      
      let seriesList = []

      if (this.firstInit[targetObj]) {  // 首次加载
        let yAxisOneList = _.cloneDeep(this.allDataJson[yAxisOneListName])
        let yAxisTwoList = yAxisTwoListName !== null ? _.cloneDeep(this.allDataJson[yAxisTwoListName]) : []
        seriesList = this._handleCalendarEchartData(targetObj, yAxisOneList, yAxisTwoList, hasYAxisTwo)
      } else {  // 二次加载
        seriesList = this.editData[targetObj].editSeries
      }

      let chartOption = this._handleEchartOptions(targetObj, seriesList, hasYAxisTwo)

      // 如果模板有X轴的最大值、最小值、间隔,如果有就设置
      if(templateParam.xMin){
        chartOption.xAxis[0].min = templateParam.xMin
      }
      if(templateParam.xMax){
        chartOption.xAxis[0].max = templateParam.xMax
      }
      if(templateParam.xInterval){
        chartOption.xAxis[0].interval = templateParam.xInterval
      }

      this.echartObj[targetObj].clear()
      this.echartObj[targetObj].getZr().off('dblclick')
      this.echartObj[targetObj].getZr().on('dblclick', ({target, topTarget}) => {
        this._handleDblclickEchart(target, topTarget, targetObj)
      });
      this.echartObj[targetObj].setOption(chartOption)

      if (this.firstInit[targetObj]) {
        this._handleYAxisValue(targetObj, hasYAxisTwo)
        if (!templateParam.xMax) {
          this.editData[targetObj].xInterval = this.originalData[targetObj].xInterval
          this.editData[targetObj].xMax = this.originalData[targetObj].xMax + this.originalData[targetObj].xInterval
          this.originalData[targetObj].xMax = this.originalData[targetObj].xMax + this.originalData[targetObj].xInterval
          // 最大值重新赋值：多一个格子
          chartOption.xAxis[0].max = this.originalData[targetObj].xMax
          chartOption.xAxis[0].interval =this.originalData[targetObj].xInterval
          this.echartObj[targetObj].setOption(chartOption)
        }

        this.firstInit[targetObj] = false
      }
    },

    // 数据处理
    _handleCalendarEchartData(targetObj, yAxisOneList, yAxisTwoList = [], hasYAxisTwo = false) {
      // 模板数据
      const templateParam = this.reportChartTemplateList[targetObj].templateParamJson
      const originalParam = this.reportChartTemplateList[targetObj].originalParamJson

      yAxisOneList = Array.isArray(yAxisOneList) ? yAxisOneList : []

      let titleData = this._getEchartOriginal(this.titleDataObj[targetObj], targetObj, hasYAxisTwo)

      // 5个图需要计算Y轴最大最小值及左边长度（未处理）
      if (['innerres','height','volume','weight','isolateres'].includes(targetObj)) {
        let yAxisOneDataList = []
        yAxisOneList.forEach(seriesItem => {
          yAxisOneDataList.push(...seriesItem.data.map(mapItem => Number(mapItem[1])))
        })

        const rangeValue = this._getYAxisRadius(Math.max.apply(null, yAxisOneDataList), Math.min.apply(null, yAxisOneDataList))
        let yMax = rangeValue[0]
        let yMin = rangeValue[1]
        let yInterval = null

        // 重量图次Y轴
        if (targetObj === 'weight') {
          // 重新计算主Y轴的最小值和间隔值，使得分割段数为5
          // let newDiff = (yMax - yMin) / 5 > 1 ? Math.ceil((yMax - yMin) / 5) * 5 : 5
          // yMin = yMax - newDiff
          // yInterval = newDiff / 5

          // 例如：重量最大值135.5，向下取整得到130，间隔为10，5个间隔，上2下3，150 140 130 120 110 100
          const number = Math.max.apply(null, yAxisOneDataList);
          const value = isFinite(number) && typeof number === 'number' ? Math.floor(number / 10) * 10 : 0
          yMax = value + 20
          yMin = value - 30
          yInterval = 10
        }

        titleData.yMax = templateParam.yMax ?? yMax
        titleData.yMin = templateParam.yMin ?? yMin
        if (yInterval === null) {
          delete titleData.yInterval
        } else {
          titleData.yInterval = templateParam.yInterval ?? yInterval
        }
        const valueLength = yMax.toString().length * 7 + (yMax >= 4 ? 0 : 9)
        titleData.yTitleLetf = templateParam.yTitleLetf ?? valueLength + 15
        titleData.gridLeft = templateParam.gridLeft ?? valueLength + 15 + 35
      }

      let seriesList = []
      let lineColorObj = {} // 折线颜色
      let lineSymbolObj = {} // 折点类型
      const legendNameType = titleData.legendNameType || 'sampleCode'

      // 定义通用值
      const normalData = {...titleData,series:[]}
      const normalLegent = []

      for (let i = 0; i < yAxisOneList.length; i++) {
        let sampleCode = yAxisOneList[i].sampleCode
        let sizeType = yAxisOneList[i].sizeType

        // 找到对应的模板修改内容

        let yAxisOneLegendId = (targetObj === 'height' ? sizeType + '-' : '') + sampleCode
        let yAxisOneLegendName = (targetObj === 'height' ? sizeType + '-' : '') + yAxisOneList[i][legendNameType]
        let templateContent = (templateParam.checkData && templateParam.checkData.length !== 0) ? (templateParam.checkData.filter(item => item.id === yAxisOneLegendId)[0] || {}) : {}


        this.chartLegendNameListObj[targetObj].push({
          sampleCode:  (targetObj === 'height' ? sizeType + '-' : '') + sampleCode,
          batteryCode: (targetObj === 'height' ? sizeType + '-' : '') + yAxisOneList[i].batteryCode
        })

        if (targetObj === 'height') {
          if (!lineColorObj.hasOwnProperty(sizeType)) {
            lineColorObj[sizeType] = this.echartsColorList[Object.keys(lineColorObj).length % this.echartsColorList.length]
          }
          if (!lineSymbolObj.hasOwnProperty(sampleCode)) {
            lineSymbolObj[sampleCode] = this.echartsSymbolList[Object.keys(lineSymbolObj).length % this.echartsSymbolList.length]
          }
        } else {
          if (!lineColorObj.hasOwnProperty(sampleCode)) {
            lineColorObj[sampleCode] = this.echartsColorList[Object.keys(lineColorObj).length % this.echartsColorList.length]
          }
        }

        const temColor = targetObj === 'height' ? lineColorObj[sizeType] : lineColorObj[sampleCode]
        const temSymbol = targetObj === 'height' ? lineSymbolObj[sampleCode] : 'rect'

        const seriesTemplate = {
          name: yAxisOneLegendName,
          type: 'line',
          sampling: 'lttb',
          large: true,
          barGap: 0,
          symbol: templateContent.symbol ?? temSymbol,
          symbolSize: targetObj === 'height' ? 7 : 5,
          markPoint: {
            data: []
          },
          emphasis: {
            focus: "series"
          },
        }
        
        const seriesOriginalTemplate = {
          name: yAxisOneLegendName,
          index: i + 1,
          soc: yAxisOneLegendName,
          type: 'line',
          sampling: 'lttb',
          large: true,
          barGap: 0,
        }

        const originalSeries = [{ 
          id: yAxisOneLegendId,
          lineType: templateContent.lineType ?? 'solid',
          synchronization:  templateContent.synchronization ?? seriesList.length,
          symbol: templateContent.symbol ?? temSymbol,
          symbolSize:  templateContent.symbolSize ?? (targetObj === 'height' ? 7 : 5),
          maxPoint: templateContent.maxPoint ?? false,
          minPoint: templateContent.minPoint ?? false,
          connectNulls: Boolean(Number(templateContent.connectNulls))  ?? false,
          lineWidth: templateContent.lineWidth ?? 1,
          lineColor: templateContent.lineColor ?? temColor,
          itemColor: templateContent.itemColor ?? temColor,
          dataName:yAxisOneLegendName + '_' +  (titleData.tooltipPrefix || ''),
          ...seriesOriginalTemplate
        }]

         // 判断是否有模板，并且模板是否有该值
         if(templateParam.legendData?.legendList === undefined || templateParam.legendData.legendList.includes(yAxisOneLegendName)){
            if(templateContent.symbolSize){
              seriesTemplate.symbolSize = templateContent.symbolSize
            }
            if(templateContent.connectNulls){
              seriesTemplate.connectNulls = Boolean(Number(templateContent.connectNulls))
            }
            if(templateContent.maxPoint){
              seriesTemplate.markPoint.data.push({type:'max',name:'max'})
            }
            if(templateContent.minPoint){
              seriesTemplate.markPoint.data.push({type:'min',name:'min'})
            }

            
            seriesList.push({
              ...seriesTemplate,
              id: yAxisOneLegendId,
              lineStyle: {
                width: templateContent.lineWidth ?? 1.5,
                type: templateContent.lineType ?? 'solid',
                color: templateContent.lineColor ?? temColor
              },
              itemStyle: {
                color: templateContent.itemColor ?? temColor
              },
              data: yAxisOneList[i].data.map((item, index) => {
                let lineName = titleData.tooltipPrefix || ''
                let absoluteTime = ''
                if (['voltage','innerres','height','volume','weight','isolateres'].includes(targetObj)) {
                  lineName = item[2] + (lineName ? '-' + lineName : '')
                  // 处理异常值
                  if (item.length > 1 && item[1] !== null && item[1] !== undefined && !isNaN(Number(item[1]))) {
                    item[1] = Number(item[1]) >= 10000000000 ? 10000000000 : item[1]
                  }
                } else {
                  absoluteTime = this.absoluteTimeMap[item[0]];
                }
                return {id: index, value: item, lineName: lineName, unit: titleData.tooltipUnit, absoluteTime: absoluteTime}
              }),
              
          })
        }
        if (yAxisTwoList.length > 0) {
          if (targetObj.includes('Rec') && Array.isArray(this.dcirTiTleList) && this.dcirTiTleList.length > 0) {
            for (let j = 0; j < this.dcirTiTleList.length; j++) {
              let findIndex = yAxisTwoList.findIndex(item => item.sampleCode == sampleCode && item.dcirTitleIndex == j)
              
              if (findIndex !== -1) {
                // 由于两条线不同，故id不同，需替换
                templateContent = (templateParam.checkData && templateParam.checkData.length !== 0) ? (templateParam.checkData.filter(item => item.id === yAxisTwoList[findIndex].sampleCode + 'dcir' + yAxisTwoList[findIndex].dcirTitleIndex)[0] || {}) : {}
                if(templateParam.legendData?.legendList === undefined || templateParam.legendData.legendList.includes(yAxisOneLegendName)){
                  seriesList.push(
                      {
                        ...seriesTemplate,
                        id: yAxisTwoList[findIndex].sampleCode + 'dcir' + yAxisTwoList[findIndex].dcirTitleIndex,
                        yAxisIndex: 1,
                        lineStyle: {
                          width: templateContent.lineWidth ?? 1.5,
                          type: templateContent.lineType ?? 'dashed',
                          color: templateContent.lineColor ?? temColor
                        },
                        itemStyle: {
                          color: templateContent.itemColor ?? temColor
                        },
                        data: yAxisTwoList[findIndex].data.map((item, index) => {
                          let lineName2 = (yAxisTwoList[findIndex].dcirTitle || '') + '_DCIR增长率 '
                          return {id: index, value: item, lineName: lineName2, unit: titleData.tooltipUnit2}
                        }),
                      }
                  )
                }
                originalSeries.push(
                      {
                        ...seriesOriginalTemplate,
                        id: yAxisTwoList[findIndex].sampleCode + 'dcir' + yAxisTwoList[findIndex].dcirTitleIndex,
                        lineType:  templateContent.lineType ??  'dashed',
                        synchronization: templateContent.synchronization ?? seriesList.length,
                        symbol: templateContent.symbol ?? temSymbol,
                        symbolSize:  templateContent.symbolSize ?? (targetObj === 'height' ? 7 : 5),
                        maxPoint: templateContent.maxPoint ?? false,
                        minPoint: templateContent.minPoint ?? false,
                        connectNulls: Boolean(Number(templateContent.connectNulls))  ?? false,
                        lineWidth: templateContent.lineWidth ?? 1,
                        lineColor: templateContent.lineColor ?? temColor,
                        itemColor: templateContent.itemColor ?? temColor,
                        dataName:yAxisOneLegendName + '_' + (yAxisTwoList[findIndex].dcirTitle || '') + '_DCIR增长率 '
                      }
                  )
              }
            }
          } else {
            if(templateParam.legendData.legendList === undefined || templateParam.legendData.legendList.includes(yAxisOneLegendName)){
              seriesList.push(
                  {
                    id: yAxisTwoList[i].sampleCode + 'two',
                    yAxisIndex: 1,
                    lineStyle: {
                      width: templateContent.lineWidth ?? 1.5,
                      type: templateContent.lineType ?? 'dashed',
                      color: templateContent.lineColor ?? temColor
                    },
                    itemStyle: {
                      color: templateContent.itemColor ?? temColor
                    },
                    data: yAxisTwoList[i].data.map((item, index) => {
                      let lineName2 = titleData.tooltipPrefix2
                      if (['voltage','innerres','height','volume','weight','isolateres'].includes(targetObj)) {
                        lineName2 = item[2] + (lineName2 ? '-' + lineName2 : '')
                      }
                      return {id: index, value: item, lineName: lineName2, unit: titleData.tooltipUnit2}
                    }),
                    ...seriesTemplate,
                  }
              )

              originalSeries.push(
                  {
                    ...seriesOriginalTemplate,
                    id: yAxisTwoList[i].sampleCode + 'two',
                    lineType: templateContent.lineType ?? 'dashed',
                    synchronization:  templateContent.synchronization ?? seriesList.length,
                    symbol: templateContent.symbol ?? temSymbol,
                        symbolSize:  templateContent.symbolSize ?? (targetObj === 'height' ? 7 : 5),
                        maxPoint: templateContent.maxPoint ?? false,
                        minPoint: templateContent.minPoint ?? false,
                        connectNulls: Boolean(Number(templateContent.connectNulls))  ?? false,
                        lineWidth: templateContent.lineWidth ?? 1,
                        lineColor: templateContent.lineColor ?? temColor,
                        itemColor: templateContent.itemColor ?? temColor,
                        dataName:yAxisOneLegendName + '_' + (yAxisTwoList[i].dcirTitle || '') + '_DCIR增长率 '
                  }
              )
            }
            
          }

        }
        normalData.series.push(...originalSeries)
        normalLegent.push(yAxisOneLegendName)
      }

      if(!this.originalData[targetObj]){
        this.originalData[targetObj] = originalParam ? _.cloneDeep (originalParam) : _.cloneDeep(normalData)
        this.originalData[targetObj].originalSeries = originalParam ? _.cloneDeep(originalParam.originalSeries) : _.cloneDeep(seriesList)
      }
      this.legendOptions[targetObj] = _.cloneDeep(normalLegent)
      this.editData[targetObj] = {
        ..._.cloneDeep(normalData),
        editSeries:_.cloneDeep(seriesList),
        originalSeries:originalParam ? _.cloneDeep(originalParam.originalSeries) : _.cloneDeep(seriesList),
        legend:templateParam.legendData?.legendList ?? _.cloneDeep(normalLegent),
        legendSort:templateParam.legendData?.legendSort ??  _.cloneDeep(normalLegent),
        legendRevealList:templateParam.legendData?.legendRevealList ??  _.cloneDeep(normalLegent).slice(0, 6),
        legendEditName:templateParam.legendData?.legendEditName ??  _.cloneDeep(normalLegent).map(v => {
          return {id: v,originName: v, previousName: '', newName: '', isReset: false}
        }),
        allData:templateParam.allData ?? {},
      }
      for (let key of Object.keys(templateParam)) {
          if(['allData','checkData'].includes(key)) continue
          if(key === 'legendData'){
            // legendIndeterminate、checkAll、legendRevealcheckAll、legendRevealIndeterminate、legendRevealOptions
            for (let key1 of Object.keys(templateParam.legendData)) {
              if(templateParam.legendData?.[key1] !== undefined){
                this.editData[targetObj][key1] = templateParam.legendData[key1]
              } 
            }
          }else{
            this.editData[targetObj][key] = templateParam[key]
          }          
      }
      return seriesList
    },

    _handleInitLegendList(seriesList, legend) {
      if (this.echartObj[this.editObj]) this.echartObj[this.editObj].dispose();
      this.echartObj[this.editObj] = this.echarts.init(document.getElementById(this.editObj), 'walden', { devicePixelRatio: 2 })

      let chartOption = this._handleEchartOptions(this.editObj, seriesList, this.editObj.includes('Rec') || this.editObj === 'weight')

      chartOption.legend.data = legend
      this.echartObj[this.editObj].setOption(chartOption)
    },

    reExport(rebuildFlag) {
      this.$store.commit('setTaskFilterData', this.data);
      if (rebuildFlag) {
        this.$router.push('/dong_li_report_build');
      } else {
        this.$router.push('/dong_li_report_build?id=' + this.data.id);
      }
    },
    exportCalendarReport() {
      this.isLoading = true
      exportDongLiCalendarReport({
        id: this.data.id
      }).then(res => {
        if (res.data.size > 0) {
          const reportName = this.data.reportName
          const fileName = '日历寿命数据_' + reportName + '.xlsx'
          downloadfile1(res, fileName)
        } else {
          this.$message.warning("暂无数据！")
        }
      }).catch(error => {
        console.error(error)
        this.$message.warning("导出失败！")
      }).finally(() => {
        this.isLoading = false
      })
    },
  }
}
</script>
<style lang="less" scoped>
@import "../../css/calendar.less";
@import '/src/components/pageTool/style/pbiSearchItem.less';

/* 固定列 */
/deep/ .right-content .ant-table-thead tr:nth-child(1) th:nth-child(1),
/deep/ .right-content .ant-table-tbody tr td:nth-child(1){
  position: sticky;
  left: 0;
  z-index: 11;
}
/deep/ .right-content .ant-table-thead tr:nth-child(1) th:nth-child(2),
/deep/ .right-content .ant-table-tbody tr td:nth-child(2){
  position: sticky;
  left: 100px;
  z-index: 11;
}
/* 固定列数据背景颜色 */
/deep/ .right-content .ant-table-tbody tr td:nth-child(1),
/deep/ .right-content .ant-table-tbody tr td:nth-child(2) {
  background-color: #FFFFFF;
}

/* 离线固定第三列 */
/deep/ .offline-table .ant-table-thead tr:nth-child(1) th:nth-child(3),
/deep/ .offline-table .ant-table-tbody tr td:nth-child(3){
  position: sticky;
  left: 200px;
  z-index: 11;
}
/deep/ .offline-table .ant-table-tbody tr td:nth-child(3) {
  background-color: #FFFFFF;
}

.right-top-div {
  top: 55px;
  right: 20px;
  height: 30px;
}

/deep/ .ant-btn-div .ant-btn > i,
/deep/ .ant-btn-div .ant-btn > span {
  display: inline-block;
}

/deep/ .ant-upload-picture-card-wrapper {
  zoom: 1;
  display: ruby;
}

/deep/ .ant-upload-list {
  margin-bottom: -8px;
}

.videoContent {
  width: 800px;
  height:500px;
  margin: 40px 0px 0px 40px;
  float: left
}

.table-title-div {
  height: 32px;
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}
</style>