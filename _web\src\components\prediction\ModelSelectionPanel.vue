<template>
  <div class="model-selection-panel">
    <a-alert
      v-if="loading"
      message="正在加载模型列表"
      type="info"
      show-icon
      class="status-alert"
    />

    <a-alert
      v-else-if="error"
      :message="error"
      type="error"
      show-icon
      class="status-alert"
    />

    <a-alert
      v-else-if="models.length === 0"
      message="暂无可用的模型"
      description="请先在公式拟合页面创建并保存模型"
      type="warning"
      show-icon
      class="status-alert"
    />

    <div v-else class="model-list">
      <h3 class="section-title">可用模型列表</h3>

      <a-row :gutter="[16, 16]">
        <a-col v-for="model in models" :key="model.id" :xs="24" :sm="12" :md="8" :lg="6">
          <a-card hoverable class="model-card" @click="selectModel(model)">
            <template slot="title">
              <div class="model-title">{{ model.id }}</div>
            </template>

            <div class="model-desc">{{ model.description || '无描述' }}</div>

            <div class="model-meta">
              <a-tag color="blue">{{ getModelType(model) }}</a-tag>
              <a-tag color="green">{{ getCoefficientsCount(model) }} 系数</a-tag>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
import { api } from '@/api';
import { message } from 'ant-design-vue';
import predictionMixin from '@/mixins/predictionMixin';

export default {
  name: 'ModelSelectionPanel',
  mixins: [predictionMixin],
  data: () => ({
    loading: true,
    error: null,
    models: []
  }),
  methods: {
    async fetchModels() {
      this.loading = true;
      this.error = null;

      try {
        const response = await api.formula.getModels();

        if (response.data.success) {
          this.models = response.data.models || [];
        } else {
          this.error = response.data.message || '获取模型列表失败';
        }
      } catch (error) {
        console.error('获取模型列表失败:', error);
        this.error = '获取模型列表请求失败，请检查网络连接';
      } finally {
        this.loading = false;
      }
    },

    selectModel(model) {
      this.$emit('model-selected', model);
      message.success(`已选择模型: ${model.id}`);
    },

    getModelType(model) {
      return model.coefficientMode === 'optimize' ? '优化模型' : '自定义模型';
    },

    getCoefficientsCount(model) {
      return (model.params || []).filter(p => p.type === 'coefficient').length;
    }
  },
  mounted() {
    this.fetchModels();
  }
};
</script>

<style scoped>
.status-alert {
  margin-bottom: 16px;
}

.model-selection-panel {
  padding: 16px 0;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
}

.model-card {
  transition: all 0.3s;
  height: 100%;
}

.model-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.model-title {
  font-weight: 600;
  color: #1890ff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.model-desc {
  font-size: 14px;
  color: #666;
  height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  margin-bottom: 8px;
}

.model-meta {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
</style>