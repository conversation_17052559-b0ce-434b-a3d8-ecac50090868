.ve-table{
  .ve-table-container{
    .ve-table-content-wrapper{
      table.ve-table-content{
        tbody.ve-table-body{
          tr.ve-table-body-tr{
            td.ve-table-body-td{
              padding: 0 1px;
              font-size: 12px;
              color: #000;
            }
          }
        }

        tr.ve-table-expand-tr {
          td.ve-table-body-td {
            padding: 0 1px;
            font-size: 12px;
            color: #000;
          }
        }
      }
    }
    
  }
}

.ve-table{
  .ve-table-container{
    .ve-table-content-wrapper{
      table.ve-table-content{
        thead.ve-table-header{
          tr.ve-table-header-tr{
            th.ve-table-header-th{
              padding: 0 1px;
              font-size: 12px;
              background-color: #5c81cb !important;
              color: #fff;
            }
          }
        }
      }
    }
    
  }
}

.ve-table.ve-table-border-around {
  border: 1px solid rgb(219, 219, 219);
  border-bottom: none;
  border-right: none;
}

.ve-table{
  .ve-table-container{
    .ve-table-content-wrapper{
      table.ve-table-content{
        tfoot.ve-table-footer{
          tr.ve-table-footer-tr{
            td.ve-table-footer-td{
              padding: 4px !important;
            }
          }
        }
      }
    }
    
  }
}

.ve-table{
  .ve-table-container{
    .ve-table-content-wrapper{
      .ve-table-border-y{
        th,td{
          border-right: 1px solid rgb(219, 219, 219);
        }
      }
    }
    
  }
}
.ve-table.ve-table-border-around{
  .ve-table-container{
    .ve-table-content-wrapper{
      table.ve-table-content.ve-table-border-x{
        tr:last-child > td{
          border-bottom: 1px solid rgb(219, 219, 219);
        }
      }
    }
    
  }
}

.ve-table{
  .ve-table-container{
    .ve-table-content-wrapper{
      .ve-table-border-x{
        th,td{
          border-bottom: 1px solid rgb(219, 219, 219);
        }
      }
    }
    
  }
}

.ve-table.ve-table-border-around{
  .ve-table-container{
    .ve-table-content-wrapper{
      table.ve-table-content.ve-table-border-y{
        th.ve-table-last-column,td:last-child{
          border-right: 1px solid rgb(219, 219, 219);
        }
      }
    }
    
  }

}

.ve-table{
  .ve-table-container{
    .ve-table-content-wrapper{
      table.ve-table-content{
        tfoot.ve-table-footer{
          tr.ve-table-footer-tr{
            height: 27px;
          }
        }
      }
    }
    
  }
}
.ve-table .ve-table-container .ve-table-content-wrapper table.ve-table-content thead.ve-table-header tr.ve-table-header-tr{
  height: 27px;
}

.ve-table .ve-table-container .ve-table-content-wrapper table.ve-table-content tbody.ve-table-body tr.ve-table-body-tr, .ve-table .ve-table-container table.ve-table-content tbody.ve-table-body tr.ve-table-expand-tr{
  height: 27px;
}

.clickcellstyle {
  cursor: pointer;
  color: #0d87d8;
  display: inline-block;
  width: 100%;
}
.clickheadstyle {
  cursor: pointer;
  display: inline-block;
  width: 100%;
}
.clickheadstyle:hover {
  color: #d6d6d6;
}
.tips {
  padding: 2px 0;
  display: flex;
  font-size: 12px;
  justify-content: center;
}
.tips span {
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 20px;
}
.tips label {
  margin-right: 6px;
  height: 18px;
}
.tips label.green,
.tips label.blue,
.tips label.lightgreen,
.tips label.yellow,
.tips label.pink,
.tips label.danger,
.tips label.brown {
  width: 100px;
  display: inline-block;
}
.tips label.danger {
  background: #fc8585;
}
.tips label.lightgreen {
  background: #eaf5e4;
}
.tips label.green {
  background: #b4f7ac;
}

.tips label.blue {
  background: #acd1f7;
}

.tips label.yellow {
  background: #fff5ad;
}
.tips label.brown {
  background: #cacaca;
}
.tips label.pink {
  background: #f7d4d4;
}

.vxe-table--render-wrapper,.vxe-table--main-wrapper{
  border: 0.8px solid rgb(219, 219, 219);
  border-bottom: 0;
}

.vxe-table--render-default.border--default .vxe-table--header-wrapper, 
.vxe-table--render-default.border--full .vxe-table--header-wrapper, 
.vxe-table--render-default.border--outer .vxe-table--header-wrapper{
  background-color: #5c81cb;
}


.vxe-table--render-default.border--full .vxe-body--column,
 .vxe-table--render-default.border--full .vxe-footer--column, 
 .vxe-table--render-default.border--full .vxe-header--column{
  background-image: linear-gradient(rgb(219, 219, 219),rgb(219, 219, 219)),linear-gradient(rgb(219, 219, 219),rgb(219, 219, 219));
  background-repeat: no-repeat;
  background-size: 0.8px 100%,100% 0.8px;
  background-position: 100% 0,100% 100%;
}

.vxe-table--render-default .vxe-body--column:not(.col--ellipsis), 
.vxe-table--render-default .vxe-footer--column:not(.col--ellipsis),
 .vxe-table--render-default .vxe-header--column:not(.col--ellipsis){
  padding: 4px;
}

.vxe-header--column{
  color: #fff;
  font-weight: 500;
}

.vxe-table--render-default .vxe-cell{
  padding: 0;
}
.vxe-body--column .vxe-cell{
  background: #fff;
}
.vxe-table--render-default .vxe-table--footer-wrapper{
  border-top: 1px solid rgb(219, 219, 219);
}
.vxe-table--render-default{
  font-size: 12px;
  color: #000 !important;
}
.vxe-table .vxe-table--header-wrapper .vxe-table--header-border-line{
  border: none;
}
.ve-table .ve-table-container .ve-table-content-wrapper table.ve-table-content thead.ve-table-header .ve-table-header-tr .ve-table-header-th .ve-table-filter .ve-table-filter-icon{
  color: #fff;
}