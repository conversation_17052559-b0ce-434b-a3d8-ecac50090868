<template>
  <a-modal title="新增" :width="600" :visible="visible" :confirmLoading="confirmLoading" @ok="handleSubmit" @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">

        <a-form-item label="电池结构" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-select v-decorator="['structureType', {rules: [{required: true, message: '请选择结构类型！'}]}]">
            <a-select-option value="g_cylinder">
              G圆柱
            </a-select-option>

            <a-select-option value="c_cylinder">
              C圆柱
            </a-select-option>
            <a-select-option value="v_cylinder">
              V圆柱
            </a-select-option>


            <a-select-option value="winding">
              方形卷绕
            </a-select-option>

            <a-select-option value="lamination">
              方形叠片
            </a-select-option>
            <a-select-option value="soft_roll">
              软包
            </a-select-option>

          </a-select>


        </a-form-item>
        <a-form-item label="应用场景" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>

          <a-tree-select
                          v-decorator="['scenario', {rules: [{ required: true, message: '请选择应用场景！' }]}]"
                          :defaultExpandAll="true"
                          :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                          :treeData="gData" placeholder="应用场景">
          </a-tree-select>

        </a-form-item>
        <a-form-item label="产品名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input placeholder="请输入产品名称" v-decorator="['productName', {rules: [{required: true, message: '请输入负极体系！'}]}]" />
        </a-form-item>

        <a-form-item label="项目等级" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-select v-decorator="['grade', {rules: [{required: true, message: '请选择项目等级！'}]}]">

            <a-select-option value="S">
              S
            </a-select-option>


            <a-select-option value="A">
              A
            </a-select-option>

            <a-select-option value="B">
              B
            </a-select-option>
            <a-select-option value="C">
              C
            </a-select-option>

          </a-select>




        </a-form-item>


        <a-form-item label="项目名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input placeholder="请输入项目名称" v-decorator="['batteryName', {rules: [{required: true, message: '请输入项目名称！'}]}]" />
        </a-form-item>
        <a-form-item label="客户" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input placeholder="请输入客户" v-decorator="['customer', {rules: [{required: true, message: '请输入客户！'}]}]" />
        </a-form-item>
        <a-form-item label="研发项目经理(RPM)" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input placeholder="请输入研发项目经理(RPM)" v-decorator="['rpm', {rules: [{required: true, message: '请输入项目经理(RPM)！'}]}]" />
        </a-form-item>
        <a-form-item label="产品经理(PD)" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input placeholder="产品经理(PD)" v-decorator="['pd', {rules: [{required: true, message: '请输入产品经理(PD)！'}]}]" />
        </a-form-item>

        <a-form-item label="项目状态" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-select v-decorator="['projectStatus', {rules: [{required: true, message: '请选择项目状态！'}]}]">

            <a-select-option value="立项讨论">
              立项讨论
            </a-select-option>


            <a-select-option value="立项-未定点">
              立项-未定点
            </a-select-option>

            <a-select-option value="立项-已定点">
              立项-已定点
            </a-select-option>
            <a-select-option value="暂停">
              暂停
            </a-select-option>
            <a-select-option value="终止">
              终止
            </a-select-option>

          </a-select>




        </a-form-item>


        <a-form-item label="项目启动(K0立项评审)" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-date-picker placeholder="请选择项目启动(K0立项评审)日期" @change="onChangeSampleDate" style="width: 100%"
                         v-decorator="['startDate', {rules: [{required: true, message: '请选择项目启动(K0立项评审)日期!'}]}]" />
        </a-form-item>


       <!-- <a-form-item label="产品开发阶段" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-select v-decorator="['productDevelopmentStage', {rules: [{required: true, message: '请选择产品开发阶段！'}]}]">

            <a-select-option value="a">
              A样
            </a-select-option>


            <a-select-option value="b">
              B样
            </a-select-option>

            <a-select-option value="c">
              C样
            </a-select-option>
            <a-select-option value="d">
              D样
            </a-select-option>

          </a-select>




        </a-form-item>-->

      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import {
    sysBatteryDesignAdd
  } from '@/api/modular/system/batterydesignManage'
  import moment from "moment";
  export default {
    props: {
      type: {
        type: String,
        default: ''
      },
    },
    data() {
      return {
        startDate:null,
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 8
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 14
          }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this),
        gData: [
          {
            title: '乘用车',
            key: '乘用车',
            value: '乘用车',
          }, {
            title: '混动',
            key: '混动',
            value: '混动',
          }, {
            title: '轻型动力',
            key: '轻型动力',
            value: '轻型动力',
          }, {
            title: '商用',
            key: '商用',
            value: '商用',
            selectable:false,
            children: [
              {
                title: '客车',
                key: '商用-客车',
                value: '商用-客车'
              }, {
                title: '重卡',
                key: '商用-重卡',
                value: '商用-重卡'
              }, {
                title: '物流',
                key: '商用-物流',
                value: '商用-物流'
              }, {
                title: '工程',
                key: '商用-工程',
                value: '商用-工程'
              }
            ],
          }, {
            title: '储能',
            key: '储能',
            value: '储能',
            selectable:false,
            children: [
              {
                title: '电力',
                key: '储能-电力',
                value: '储能-电力'
              }, {
                title: '通讯',
                key: '储能-通讯',
                value: '储能-通讯'
              }, {
                title: '家用',
                key: '储能-家用',
                value: '储能-家用'
              }
            ],
          }
        ]
      }
    },

    methods: {
      add() {
        this.visible = true
      },
      onChangeSampleDate(date, dateString) {
        if (date == null) {
          this.startDate = ''
        } else {
          this.startDate = moment(date).format('YYYY-MM-DD')
        }
      },
      handleSubmit() {
        const {
          form: {
            validateFields
          }
        } = this

        this.confirmLoading = true
        validateFields((errors, values) => {

          values.startDate = this.startDate
          values.type = this.type

          if (!errors) {
            sysBatteryDesignAdd(values).then((res) => {
              this.confirmLoading = false
              if (res.success) {
                this.$message.success('新增成功')
                this.handleCancel()
                this.$emit('ok', values)
              } else {
                this.$message.error('新增失败：' + res.message)
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel() {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
<style lang="less">
  .ant-form-item {

    margin-bottom: 0px;

  }
</style>
