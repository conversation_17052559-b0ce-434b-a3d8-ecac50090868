<template>
	<div class="container">
		<a-spin :spinning="loading">
			<!-- 面包屑 start -->
			<div class="breadcrumb">
				<a-breadcrumb separator=">">
					<a-breadcrumb-item @click="gohome" class="hand">
						<router-link to="/product_chart"><a-icon class="rollback-icon" type="rollback" />动态监控</router-link>
					</a-breadcrumb-item>
					<a-breadcrumb-item>问题监控</a-breadcrumb-item>
				</a-breadcrumb>
			</div>
			<!-- 面包屑 end -->

			<!-- 主标题 start -->
			<div class="head-title">问题监控</div>
			<!-- 主标题 end -->

			<!-- 内容 start -->
			<div class="content-wrapper" :style="`height:${contentHeight}px`">
				<div class="table-page-search-wrapper mb0">
					<a-form layout="inline">
						<a-row :gutter="32">
							<a-col :md="5" :sm="24">
								<a-form-item label="问题状态">
									<a-select
										@change="handleQuery"
										v-model="queryparam.statusLamp"
										allowClear
										placeholder="请选择问题状态"
									>
										<a-select-option v-for="item in lampOption" :value="item.value">
											<div class="select-box">
												<div class="circle" :style="`background:${item.color}`"></div>
												{{ item.label }}
											</div>
										</a-select-option>
									</a-select>
								</a-form-item>
							</a-col>
							<a-col :md="6" :sm="24">
								<a-form-item label="产品类别">
									<treeselect
										@input="handleQuery"
										v-model="queryparam.productClassification"
										placeholder="请选择产品类别"
										:limit="1"
										:multiple="true"
										:normalizer="normalizer"
										:options="cateOption"
									/>
								</a-form-item>
							</a-col>
							<a-col :md="5" :sm="24">
								<a-form-item label="问题维度">
									<a-select
										@change="handleQuery"
										v-model="queryparam.problemDimension"
										allowClear
										placeholder="请选择问题维度"
									>
										<a-select-option v-for="item in dimensionOption" :value="item.value">
											{{ item.label }}
										</a-select-option>
									</a-select>
								</a-form-item>
							</a-col>

							<!-- <a-col :md="4" :sm="24">
							<a-form-item label="周">
								<a-week-picker placeholder="请选择周" @change="handleSelectWeek" />
							</a-form-item>
						</a-col> -->

							<a-col :md="4" :sm="24">
								<a-form-item label="">
									<a-input
										size="small"
										class="filter-input"
										@keyup.enter.native="handleChange"
										v-model="queryparam.productProjectName"
										placeholder="请输入产品名称"
									>
										<a-icon slot="suffix" type="search" style="color: rgba(0,0,0,.45)" />
									</a-input>
								</a-form-item>
							</a-col>

							<a-col :md="1" :sm="24" :style="{ float: 'right' }">
								<div class="table-page-search-submitButtons" :style="{ float: 'right' }">
									<a-button size="small" style="margin-left: 120px;" type="primary" @click="handleQuery">查询</a-button>
								</div>
							</a-col>
						</a-row>
					</a-form>
				</div>
				<div class="table-wrapper" :style="`height:${tableHeight}px;`">
					<a-table
						ref="table"
						:style="`height:${tableHeight}px;`"
						:rowKey="record => record.issueId + record.issueKey"
						:columns="columns"
						:dataSource="tableData"
					>
						
						<!-- 产品类别 -->
						<span slot="productClassification" slot-scope="text">
							{{ cateData[text] }}
						</span>
						<!-- 产品阶段 -->
						<span slot="productStage" slot-scope="text">
							{{ "product_stage_status" | dictType(text) }}
						</span>
						<!-- 问题维度 -->
						<span slot="problemDimension" slot-scope="text">
							{{ dimensionData[text] }}
						</span>

						<template slot="problemDescription" slot-scope="text">
							<a-tooltip overlayClassName="test">
								<div slot="title" class="a-wrap">
									{{ text }}
								</div>
								<div class="ellipsis-tip">{{ text }}</div>
							</a-tooltip>
						</template>

						<template slot="causeAnalysis" slot-scope="text">
							<a-tooltip>
								<div slot="title" class="a-wrap">
									{{ text }}
								</div>
								<div class="ellipsis-tip">{{ text }}</div>
							</a-tooltip>
						</template>

						<template slot="problemSolving" slot-scope="text">
							<a-tooltip>
								<div slot="title" class="a-wrap">
									{{ text }}
								</div>
								<div class="ellipsis-tip">{{ text }}</div>
							</a-tooltip>
						</template>

						<template slot="productProcess" slot-scope="text">
							<a-tooltip>
								<div slot="title" class="a-wrap">
									{{ text }}
								</div>
								<div class="ellipsis-tip">{{ text }}</div>
							</a-tooltip>
						</template>
					</a-table>
				</div>
			</div>
			<!-- 内容 end -->
		</a-spin>
	</div>
</template>

<script>
import { getCatesTree } from "@/api/modular/system/report"
import { getTroubleList } from "@/api/modular/system/monitor"

import { getWeek } from "@/utils/formatData"

import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"
export default {
	data() {
		return {
			normalizer(node) {
				return {
					id: node.value,
					label: node.label,
					children: node.children && node.children.length > 0 ? node.children : 0
				}
			},
			loading: false,
			// 内容高度
			contentHeight: document.body.clientHeight - 40 - 20 - 21 - 38,
			tableHeight: document.body.clientHeight - 40 - 20 - 21 - 38 - 20 - 48 - 10,
			queryparam: {
				issueId: 0,
				productClassification: []
			},
			cateOption: [
				{ value: "1", label: "预研产品" },
				{ value: "2", label: "A|B新产品" },
				{ value: "3", label: "试产新产品" },
				{ value: "4", label: "量产品" },
				{ value: "5", label: "其他" },
				{ value: "6", label: "停止" }
			],
			cateData: {
				"1": "预研产品",
				"2": "A|B新产品",
				"3": "试产新产品",
				"4": "量产品",
				"5": "其他",
				"6": "停止"
			},
			lampOption: [
				{ value: 1, label: "红灯", color: "#f54747" },
				{ value: 2, label: "黄灯", color: "#efeb73" },
				{ value: 3, label: "绿灯", color: "#91cc75" }
			],
			dimensionOption: [
				{ value: 1, label: "技术" },
				{ value: 2, label: "质量" },
				{ value: 3, label: "交付" },
				{ value: 4, label: "产业化" }
			],
			dimensionData: {
				1: "技术",
				2: "质量",
				3: "交付",
				4: "产业化"
			},
			tableData: [],
			columns: [
				{
					title: "序号",
					width: 45,
					align: "center",
					dataIndex: "no",
					customRender: (text, record, index) => {
						return `${index + 1}`
					}
				},

				{
					title: "产品类别",
					width: 80,
					align: "center",
					dataIndex: "productClassification",
					scopedSlots: {
						customRender: "productClassification"
					}
				},
				{
					title: "产品名称",
					align: "center",
					width: 80,
					dataIndex: "productProjectName"
				},
				{
					title: "项目名称",
					width: 80,
					align: "center",
					dataIndex: "projectName"
				},
				

				{
					title: "产品阶段",
					width: 115,
					align: "center",
					dataIndex: "productStage",
					scopedSlots: {
						customRender: "productStage"
					}
				},
				{
					title: "问题维度",
					width: 90,
					align: "center",
					dataIndex: "problemDimension",
					scopedSlots: {
						customRender: "problemDimension"
					}
				},
				{
					title: "问题描述",
					width: 90,
					align: "center",
					dataIndex: "problemDescription",
					scopedSlots: {
						customRender: "problemDescription"
					}
				},
				{
					title: "原因分析",
					width: 90,
					align: "center",
					// ellipsis: true,
					dataIndex: "causeAnalysis",
					scopedSlots: {
						customRender: "causeAnalysis"
					}
				},
				{
					title: "解决措施",
					width: 90,
					align: "center",
					// ellipsis: true,
					dataIndex: "problemSolving",
					scopedSlots: {
						customRender: "problemSolving"
					}
				},
				{
					title: "问题进展",
					width: 90,
					align: "center",
					// ellipsis: true,
					dataIndex: "productProcess",
					scopedSlots: {
						customRender: "productProcess"
					}
				},
				// {
				// 	title: `CW${getWeek(new Date()) - 1}周问题进展`,
				// 	width: 100,
				// 	align: "center"
				// },
				{
					title: "责任人",
					width: 75,
					align: "center",
					dataIndex: "responsiblePersonName"
				},
				{
					title: "提出时间",
					width: 115,
					align: "center",
					dataIndex: "findDate"
				},
				{
					title: "计划关闭时间",
					width: 90,
					align: "center",
					dataIndex: "plannedCompletionDate"
				},
				{
					title: "实际关闭时间",
					width: 90,
					align: "center",
					dataIndex: "actualCompletionDate"
				}
			]
		}
	},
	components: {
		Treeselect
	},
	watch: {
		tableData(newVal, oldVal) {
			if (this.tableData.length > 0) {
				// 动态修改--height的值
				document.documentElement.style.setProperty(`--height`, `${this.tableHeight - 35}px`)
			} else {
				// 动态修改--height的值
				document.documentElement.style.setProperty(`--height`, "50px")
			}
		}
	},
	created() {
		if (this.$route.query.statusLamp) {
			this.queryparam.statusLamp = Number(this.$route.query.statusLamp)
		}

		//this.initCateTree()
		this.getStageTrouble()

		// 动态修改--height的值
		document.documentElement.style.setProperty(`--height`, `${this.tableHeight - 35}px`)
	},
	methods: {
		initCateTree() {
			this.loading = true
			getCatesTree()
				.then(res => {
					if (res.result) {
						res.data.forEach((v, index) => {
							this.cateOption.push({
								id: Number(v.value),
								label: v.title,
								children:
									v.children &&
									v.children.map(e => ({
										id: parseInt(e.value),
										label: e.title
									}))
							})
							if (this.cateOption[index].children === null) delete this.cateOption[index].children
						})
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
				})
				.catch(err => {
					this.$message.error("错误提示：" + err.message, 1)
				})
				.finally(() => {
					this.loading = false
				})
		},
		getStageTrouble() {
			this.loading = true

			const queryparam = JSON.parse(JSON.stringify(this.queryparam))
			if (this.queryparam.productClassification.length > 0) {
				queryparam.productClassification = queryparam.productClassification.join(",")
			}

			getTroubleList(queryparam)
				.then(res => {
					if (!res.result) return this.$message.error("错误提示：" + res.message, 1)

					this.tableData = JSON.parse(JSON.stringify(res.data))
					this.tableData.sort((a, b) => {
						return a.productClassification - b.productClassification
					})
				})
				.finally(() => {
					this.loading = false
				})
		},
		handleChange(value) {
			this.getStageTrouble()
		},
		// 选择周
		handleSelectWeek(date, dateString) {
			const week = dateString.slice(dateString.indexOf("周") - 2, dateString.indexOf("周"))
			this.weekTime = week
			this.columns[5].title = `CW${this.weekTime}周进展`
			this.callDashboardProcess(this.weekTime)
		},
		// 查询
		handleQuery() {
			this.getStageTrouble()
		},
		gohome() {
			this.$router.push({
				path: "/product_chart"
			})
		}
	}
}
</script>

<style lang="less" scoped>
.container {
	font-size: 16px;
	margin-left: -40px;
	line-height: 1;
}

/* 主标题 */

.head-title {
	color: #333;
	padding: 10px 0;
	font-size: 18px;
	font-weight: 600;
}

.head-title::before {
	width: 8px;
	background: #1890ff;
	margin-right: 8px;
	content: "\00a0"; /* 填充空格 */
}

.content-wrapper {
	background: #fff;
	border-radius: 10px;
	padding: 10px;
}
.filter-input {
	height: 32px !important;
}

/deep/.table-page-search-wrapper .ant-form-inline .ant-form-item {
	margin-bottom: 0;
}
/deep/.vue-treeselect__control {
	display: block;
}

/deep/.vue-treeselect__multi-value-item {
	display: block;
	height: 26px !important;
	line-height: 26px;
	padding: 0;
	border: none;
}
/deep/.vue-treeselect__multi-value-item-container {
	padding: 0;
}
/deep/.vue-treeselect__limit-tip-text {
	height: 26px !important;
	display: block;
	margin: -3px 0 0 4px;
	padding: 0;
}

/deep/.vue-treeselect--has-value .vue-treeselect__multi-value {
	padding: 2px 0;
	height: 30px !important;
}

// table
:root {
	--height: 600px;
}

/deep/.ant-table-body {
	height: var(--height) !important;
	overflow-y: scroll;
}

// 表格padding
/deep/.ant-table-thead > tr > th,
.ant-table-tbody > tr > td {
	padding: 10px;
}

// 分页大小
/deep/.ant-pagination-disabled,
.ant-pagination-next {
	min-width: 20px;
	height: 20px;
	line-height: 20px;
}

/deep/.ant-pagination-prev {
	min-width: 20px;
	height: 20px;
	line-height: 20px;
}
/deep/.ant-pagination-next {
	min-width: 20px;
	height: 20px;
	line-height: 20px;
}
/deep/.ant-pagination-jump-prev {
	min-width: 20px;
	height: 20px;
	line-height: 20px;
}
/deep/.ant-pagination-jump-next {
	min-width: 20px;
	height: 20px;
	line-height: 20px;
}

/deep/.ant-pagination-item {
	min-width: 25px;
	height: 25px;
	line-height: 25px;
}

/deep/.ant-table-thead {
	position: sticky;
	top: 0;
}

/deep/.ant-table-thead tr th {
	background: #f3f3f3 !important;
	color: #333;
}

/deep/.ant-table-placeholder {
	border: none;
}

.mb0 {
	margin-bottom: 0;
}

/deep/.vue-treeselect__multi-value-item {
	background: transparent;
	font-size: 13px;
	vertical-align: initial;
}
/deep/.vue-treeselect {
	/* display: inline-block; */
	min-width: 80%;
	max-width: 95%;
	margin-top: 4px;
}
/deep/.vue-treeselect__control {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 24px;
	overflow: hidden;
	border-radius: initial;
}
/deep/.vue-treeselect__control * {
	padding: 0 !important;
	margin: 0 !important;
	line-height: initial !important;
	white-space: nowrap;
}
/deep/.vue-treeselect__limit-tip-text {
	margin-top: 5px !important;
}
/deep/.vue-treeselect__value-remove {
	color: #e9e9e9;
}
/deep/.vue-treeselect__multi-value-item {
	color: #695959;
}
/deep/.vue-treeselect {
	margin-top: 1px;
}

/deep/.vue-treeselect__multi-value-item-container {
	vertical-align: text-top;
	margin-top: 5px !important;
}
/deep/.ant-table-tbody > tr > td,
/deep/.ant-table-thead > tr > th {
	padding: 4px;
}

/deep/.ant-breadcrumb {
	font-size: 12px;
}
.select-box {
	display: flex;
	align-items: center;
}
.select-box .circle {
	width: 13px;
	height: 13px;
	border-radius: 50%;
	margin-right: 8px;
}
.ellipsis-tip {
	word-break: break-all;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 3; /* 这里是超出几行省略 */
	overflow: hidden;
}
</style>
