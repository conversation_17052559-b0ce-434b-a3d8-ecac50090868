<template>
<div class="classmodal">
    <a-modal  :width="600"  style="overflow-y: scroll" :visible="visible" :confirmLoading="confirmLoading"

             @ok="handleSubmit" @cancel="handleCancel">



            <a-form :form="form">
                <a-row :gutter="24">
                  <a-col :md="20" :sm="24">
                    <a-form-item label="会议日期" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback style="margin-bottom: 0;!important;">
                      <a-date-picker @change="dateChange" v-decorator="['date1', {rules: [{required: true, message: '请选择会议日期！'}]}]"/>
                    </a-form-item>
                  </a-col>

                </a-row>

              <a-row :gutter="24">

                    <a-col :md="20" :sm="24">
                      <a-form-item label="会议主题" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                        <a-textarea :rows="3" style="height: 87px" placeholder="请填写会议主题"
                                    v-decorator="['theme', {rules: [{required: true, message: '请填写会议主题!'}]}]"></a-textarea>

                      </a-form-item>
                    </a-col>
                </a-row>

              <a-row :gutter="24">

                <a-col :md="20" :sm="24">
                  <a-form-item label="会议议程(客户给出)" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                    <a-textarea :rows="7" placeholder="请填写会议议程" v-decorator="['agenda',
               {rules: [{required: true, message: '请填写会议议程!'}]}]"></a-textarea>
                  </a-form-item>
                </a-col>



                </a-row>

              <a-row :gutter="24">

                <a-col :md="20" :sm="24">
                  <a-form-item label="会议结论" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                    <a-textarea :rows="7" placeholder="请填写会议结论" v-decorator="['conclusion',
              {rules: [{required: true, message: '请填写会议结论!'}]}]"></a-textarea>
                  </a-form-item>
                </a-col>
                </a-row>
              <a-row :gutter="24">
              <a-col :md="20" :sm="24">
                <a-form-item label="会议资料" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-upload
                    name="file"
                    :headers="headers" :action="postUrl"
                    :multiple="true"
                    :showUploadList="true"
                    :fileList="fileList"
                    v-decorator="['file1',
                    {rules: [{required: true, message: '请上传会议资料!'}]}]"

                    @change="handleChange"

                  >
                    <a-button> <a-icon type="upload" /> 上传文件 </a-button>
                  </a-upload>
                </a-form-item>
              </a-col>

                </a-row>
              <a-row :gutter="24">

                <a-col :md="20" :sm="24">
                  <a-form-item label="会议纪要" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback style="margin-bottom: 0;!important;">
                    <a-upload
                      name="file"
                      :headers="headers" :action="postUrl"  :multiple="false"
                      :showUploadList="true"
                      :fileList="summaryList"
                      @change="handleChange1"
                      v-decorator="['file2',
              {rules: [{required: true, message: '请上传会议纪要!'}]}]"
                    >
                      <a-button style="height: 20px;"> <a-icon type="upload" /> 上传文件 </a-button>
                      <div class="--mb--rich-text" data-boldtype="0" style="font-family:PingFangSC; font-weight:400; font-size:10px; color:rgba(0,0,0,0.45); font-style:normal; letter-spacing:0px; line-height:14px; text-decoration:none;">支持扩展名：.zip.doc.pdf.jpg...</div>

                    </a-upload>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">

                <a-col :md="20" :sm="24">
                  <a-form-item label="会议录音" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                    <a-upload
                      name="file"
                      @change="handleChange2"
                      :showUploadList="true"
                      :fileList="soundList"
                      :headers="headers" :action="postUrl"  :multiple="false"
                      
                    >
                      <a-button style="height: 20px;"> <a-icon type="upload" /> 上传文件 </a-button>
                      <div class="--mb--rich-text" data-boldtype="0" style="font-family:PingFangSC; font-weight:400; font-size:10px; color:rgba(0,0,0,0.45); font-style:normal; letter-spacing:0px; line-height:14px; text-decoration:none;">支持扩展名：.zip.doc.pdf.jpg...</div>
                    </a-upload>
                  </a-form-item>
                </a-col>

              </a-row>

            </a-form>

    </a-modal>
</div>
</template>

<script>
  import {
    addMeeting
  } from "@/api/modular/system/projectIssue"
  import {
    ACCESS_TOKEN
  } from '@/store/mutation-types'
    import {
        STable
    } from '@/components'
    import Vue from "vue";
    import moment from "moment";
    export default {

        props: {
          issueId: {
              type: Number,
              default: 0
          },
          projectdetail: {
              type: Object,
              default: {}
          }
      },

        components: {
            STable
        },
        data() {
            return {
              postUrl: '/api/sysFileInfo/uploadfile',
              fileIds: [],
              summaryList: [],
              fileList: [],
              soundList: [],
              summaryId: [],
              soundId: [],

                labelCol: {
                    xs: {
                        span: 24
                    },
                    sm: {
                        span: 8
                    }
                },
                wrapperCol: {
                    xs: {
                        span: 24
                    },
                    sm: {
                        span: 16
                    }
                },
                visible: false,
                visible1: false,
                confirmLoading: false,
                form: this.$form.createForm(this),
              saveData:{},
              headers: {
                Authorization: 'Bearer ' + Vue.ls.get(ACCESS_TOKEN),
              },
            }
        },
        methods: {
          handleCancel() {
            this.form.resetFields()
            this.fileList = []
            this.summaryList = []
            this.soundList = []
            this.saveData = {}
            this.defaultFileList = []
            this.visible = false
            this.visible1 = false
          },
          deleteFiles(e,record,index){
            e.stopPropagation();
            let ids =JSON.parse(record.fileIds)
            let names =JSON.parse(record.fileNames)
            let types =JSON.parse(record.fileTypes)

            ids.splice(index,1)
            names.splice(index,1)
            types.splice(index,1)

            let update ={}
            update.id = record.id
            update.fileIds= JSON.stringify(ids),
              update.fileNames= JSON.stringify(names),
              update.fileTypes= JSON.stringify(types)

            updateMeetingById(update).then(res => {
              if(res.success){
                this.callList()
              }else {
                this.$message.error('删除文件失败：' + res.message)
              }
            })


          },
          noClick(e){
            e.stopPropagation();
          },

          deleteFile(e,record,column){


            let update ={}
            update.id = record.id
            update.updateNullColumn = column

            updateMeetingNull(update).then(res => {
              if(res.success){
                this.callList()
              }else {
                this.$message.error('删除文件失败：' + res.message)
              }
            })


          },

          downloadfile (res) {
            var blob = new Blob([res.data], { type: 'application/octet-stream;charset=UTF-8' })
            var contentDisposition = res.headers['content-disposition']
            var patt = new RegExp('filename=([^;]+\\.[^\\.;]+);*')
            var result = patt.exec(contentDisposition)
            var filename = result[1]
            var downloadElement = document.createElement('a')
            var href = window.URL.createObjectURL(blob) // 创建下载的链接
            var reg = /^["](.*)["]$/g
            downloadElement.style.display = 'none'
            downloadElement.href = href
            downloadElement.download = decodeURI(filename.replace(reg, '$1')) // 下载后文件名
            document.body.appendChild(downloadElement)
            downloadElement.click() // 点击下载
            document.body.removeChild(downloadElement) // 下载完成移除元素
            window.URL.revokeObjectURL(href)
          },
          dateChange(date, dateString) {
            if (date == null) {
              this.saveData = ''
            } else {
              this.saveData.meetingDate = moment(date).format('YYYY-MM-DD')
            }
          },

          handleSubmit() {


            const {
              form: {
                validateFields
              }
            } = this
            this.confirmLoading = true

            validateFields((errors, values) => {
              if (!errors) {

                values =  Object.assign(values, this.saveData);

                /**
                 * put(18927L, 1L);//K0 立项评审
                 put(18928L, 2L);//M1 项目规划
                 put(18943L, 3L);//M2 A样方案冻结
                 put(18929L, 4L);//M2 转阶段
                 put(18944L, 5L);//M3 B样方案冻结
                 put(18930L, 6L);//M3 转阶段
                 put(18931L, 7L);//M4 C样方案冻结
                 put(18932L, 8L);//M5 PPAP
                 put(18933L, 9L);//M6 SOP
                 put(18994L, 10L);//结项
                 * @type {*[]}
                 */

                /* let stage = ['','K0 立项评审','M1 项目规划','M2 A样方案冻结','M2 转阶段','M3 B样方案冻结'
                  ,'M3 转阶段','M4 C样方案冻结','M5 PPAP','M6 SOP','结项']


                $params.stage = stage[this.$route.query.mstatus] */
                values.stage = this.projectdetail.state
                values.issueId = this.issueId

                addMeeting(values).then((res) => {
                  if (res.success) {
                    if (res.data) {
                      this.$message.success('新增成功')

                      this.form.resetFields()
                      this.fileList = []
                      this.summaryList = []
                      this.soundList = []
                      this.saveData = {}
                      this.defaultFileList = []
                      this.$emit('ok')
                    } else {
                      this.$message.error('新增失败')
                    }
                  } else {
                    this.$message.error('新增失败：' + res.message)
                  }
                }).finally((res) => {
                  this.confirmLoading = false
                  this.visible = false
                  this.visible1 = false
                })
              } else {
                this.confirmLoading = false
              }
            })
          },
          // 初始化方法
          add() {
            
            console.log(this.projectdetail)
            this.visible = true

          },
          handleChange(info) {
            this.fileList = info.fileList
            if (info.file.status === 'done') {


              let list = info.fileList
              let ids =[]
              let names =[]
              let types =[]
              for (let i = 0; i < list.length; i++) {
                let file = list[i]
                if(file.response.success){
                  ids.push(file.response.data.id)
                  names.push(file.name)
                  types.push(file.type)
                }
              }

              this.saveData.fileIds= JSON.stringify(ids),
                this.saveData.fileNames= JSON.stringify(names),
                this.saveData.fileTypes= JSON.stringify(types)



            } else if (info.file.status === 'error') {
              this.$message.error(`${info.file.name} 文件上传失败`);
            }
          },

          handleChange1(info) {

            if(info.fileList.length == 0){
              this.form.resetFields(['file2'])
            }
            if (info.fileList.length > 1){

              info.fileList.splice(0, 1)
            }

            this.summaryList = info.fileList
            if (info.file.status === 'done') {




              let file = info.file

              if(file.response.success){


                this.saveData.summaryId= file.response.data.id,
                  this.saveData.summaryName= file.name,
                  this.saveData.summaryType=file.type

              }


            } else if (info.file.status === 'error') {
              this.$message.error(`${info.file.name} 文件上传失败`);
            }
          },
          handleChange2(info) {

            if(info.fileList.length == 0){
              this.form.resetFields(['file3'])
            }

            if (info.fileList.length > 1){

              info.fileList.splice(0, 1)
            }
            this.soundList = info.fileList

            let file = info.file

            if (info.file.status === 'done') {


              if(file.response.success){

                this.saveData.soundId= file.response.data.id,
                  this.saveData.soundName= file.name,
                  this.saveData.soundType= file.type

              }


            } else if (info.file.status === 'error') {
              this.$message.error(`${info.file.name} 文件上传失败`);
            }
          },
        },
        created() {

        }
    }
</script>
<style lang='less' scoped=''>
  /deep/.ant-form-item {

    margin-bottom: 2px;

  }
  
</style>
<style>
.classmodal .ant-modal-title{
    font-size: 20px !important;
    font-weight: bolder !important;
  }
</style>