/**
 * 图表工具函数
 * 提供图表相关的通用功能
 */

/**
 * 生成图表颜色列表 - 统一的颜色方案
 * @returns {Array} 颜色数组
 */
export const chartColors = [
  'rgba(24, 144, 255, 1)',    // 蓝色 (主色)
  'rgba(245, 34, 45, 1)',     // 红色
  'rgba(82, 196, 26, 1)',     // 绿色
  'rgba(250, 173, 20, 1)',    // 橙色
  'rgba(114, 46, 209, 1)',    // 紫色
  'rgba(250, 140, 22, 1)',    // 深橙色
  'rgba(19, 194, 194, 1)',    // 青色
  'rgba(47, 84, 235, 1)',     // 深蓝色
  'rgba(144, 19, 254, 1)',    // 亮紫色
  'rgba(38, 166, 154, 1)'     // 青绿色
];

/**
 * 生成图表透明色列表（用于区域填充）
 * @returns {Array} 透明颜色数组
 */
export const chartTransparentColors = chartColors.map(color =>
  color.replace('1)', '0.2)')
);

/**
 * 获取图表颜色
 * @param {Number} index - 颜色索引
 * @returns {String} 颜色值
 */
export function getChartColor(index) {
  return chartColors[index % chartColors.length];
}

/**
 * 获取图表透明色
 * @param {Number} index - 颜色索引
 * @returns {String} 透明颜色值
 */
export function getChartTransparentColor(index) {
  return chartTransparentColors[index % chartTransparentColors.length];
}

/**
 * 创建统一的ECharts图表选项
 * @param {String} title - 图表标题
 * @param {String} xAxisName - X轴名称
 * @param {String} yAxisName - Y轴名称
 * @param {Array} series - 数据系列
 * @param {Object} options - 额外选项
 * @returns {Object} ECharts选项
 */
export function createChartOptions(title, xAxisName, yAxisName, series, options = {}) {
  const {
    min = null,
    max = null,
    legendPosition = 'bottom',
    gridConfig = {},
    showSymbol = true,
    titleConfig = {}
  } = options;

  return {
    title: {
      text: title,
      left: 'center',
      top: 10,
      // 使用新的样式配置替代已弃用的 textStyle
      fontSize: 14,
      fontWeight: 'bold',
      color: '#333',
      ...titleConfig
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: 'rgba(50, 50, 50, 0.8)',
          borderRadius: 4,
          padding: [5, 8],
          fontSize: 12
        }
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#ddd',
      borderWidth: 1,
      color: '#333',
      fontSize: 12,
      padding: [8, 12],
      extraCssText: 'box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); border-radius: 4px;'
    },
    legend: {
      type: 'scroll',
      data: series.map(s => s.name),
      ...(legendPosition === 'bottom' ? { bottom: 10 } : { top: 40 }),
      // 使用新的样式配置替代已弃用的属性
      textStyle: {
        fontSize: 12,
        color: '#666'
      },
      pageTextStyle: {
        color: '#666'
      },
      pageIconColor: '#999',
      pageIconInactiveColor: '#ccc'
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: legendPosition === 'bottom' ? '15%' : '10%',
      top: '15%',
      containLabel: true,
      ...gridConfig
    },
    xAxis: {
      type: 'value',
      name: xAxisName,
      nameLocation: 'middle',
      nameGap: 30,
      // 使用新的样式配置替代已弃用的 nameTextStyle
      nameTextStyle: {
        fontSize: 12,
        fontWeight: 'bold',
        color: '#666'
      },
      axisLine: {
        lineStyle: {
          color: '#ddd'
        }
      },
      axisTick: {
        lineStyle: {
          color: '#ddd'
        }
      },
      axisLabel: {
        fontSize: 11,
        color: '#666',
        formatter: '{value} 天'
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#eee'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: yAxisName,
      nameLocation: 'middle',
      nameGap: 40,
      min: min,
      max: max,
      // 使用新的样式配置替代已弃用的 nameTextStyle
      nameTextStyle: {
        fontSize: 12,
        fontWeight: 'bold',
        color: '#666'
      },
      axisLine: {
        lineStyle: {
          color: '#ddd'
        }
      },
      axisTick: {
        lineStyle: {
          color: '#ddd'
        }
      },
      axisLabel: {
        fontSize: 11,
        color: '#666',
        formatter: '{value}%'
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#eee'
        }
      }
    },
    series: series.map(s => ({
      ...s,
      showSymbol: s.type === 'scatter' ? true : s.showSymbol !== false,
      symbolSize: s.type === 'scatter' ? 8 : (s.symbolSize || 0),
      emphasis: {
        focus: 'series',
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        }
      }
    }))
  };
}

/**
 * 格式化数字，保留指定小数位
 * @param {Number} number - 待格式化的数字
 * @param {Number} digits - 小数位数，默认2位
 * @returns {String} - 格式化后的字符串
 */
export function formatNumber(number, digits = 2) {
  if (isNaN(number)) return '--';
  return Number(number).toFixed(digits);
}

/**
 * 创建散点图数据系列
 * @param {String} name - 系列名称
 * @param {Array} data - 数据点数组，格式为 [[x1, y1], [x2, y2], ...]
 * @param {Number} colorIndex - 颜色索引
 * @param {Object} options - 额外选项
 * @returns {Object} 散点图系列配置
 */
export function createScatterSeries(name, data, colorIndex = 0, options = {}) {
  const color = getChartColor(colorIndex);
  return {
    name: name,
    type: 'scatter',
    data: data,
    symbolSize: 5,
    itemStyle: {
      color: color,
      borderWidth: 1,
      borderColor: '#fff'
    },
    emphasis: {
      itemStyle: {
        borderWidth: 2,
        borderColor: '#fff',
        shadowBlur: 10,
        shadowColor: 'rgba(0, 0, 0, 0.3)'
      }
    },
    ...options
  };
}

/**
 * 创建折线图数据系列
 * @param {String} name - 系列名称
 * @param {Array} data - 数据点数组，格式为 [[x1, y1], [x2, y2], ...]
 * @param {Number} colorIndex - 颜色索引
 * @param {Boolean} smooth - 是否平滑曲线
 * @param {Object} options - 额外选项
 * @returns {Object} 折线图系列配置
 */
export function createLineSeries(name, data, colorIndex = 0, smooth = true, options = {}) {
  const color = getChartColor(colorIndex);
  return {
    name: name,
    type: 'line',
    data: data,
    smooth: smooth,
    symbolSize: 0,
    symbol: 'circle',
    showSymbol: false,
    lineStyle: {
      width: 2.5,
      color: color,
      shadowBlur: 4,
      shadowColor: 'rgba(0, 0, 0, 0.1)'
    },
    itemStyle: {
      color: color
    },
    emphasis: {
      lineStyle: {
        width: 3,
        shadowBlur: 8
      }
    },
    ...options
  };
}

/**
 * 创建容量曲线图表的统一配置
 * @param {String} title - 图表标题
 * @param {Array} series - 数据系列
 * @param {Object} options - 额外选项
 * @returns {Object} ECharts选项
 */
export function createCapacityCurveOptions(title, series, options = {}) {
  return createChartOptions(
    title,
    '存储天数（天）',
    '容量保持率（%）',
    series,
    {
      min: 50,
      max: 100,
      ...options
    }
  );
}

/**
 * 创建统一的图表tooltip格式化函数
 * @param {Array} params - ECharts tooltip参数
 * @returns {String} 格式化后的HTML
 */
export function createTooltipFormatter(params) {
  if (!params || params.length === 0) return '';

  const firstItem = params[0];
  const days = parseFloat(firstItem.value[0]).toFixed(4);

  let result = `<div style="font-weight:bold;margin-bottom:8px;color:#333;">存储天数: ${days}天</div>`;

  params.forEach(param => {
    const color = param.color;
    const value = param.value[1].toFixed(4);
    result += `<div style="display:flex;align-items:center;margin-top:5px;">
      <div style="width:10px;height:10px;background-color:${color};margin-right:8px;border-radius:50%;"></div>
      <span style="color:#666;">${param.seriesName}: </span>
      <span style="font-weight:bold;margin-left:5px;color:#333;">${value}%</span>
    </div>`;
  });

  return result;
}
