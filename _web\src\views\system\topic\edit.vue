<template>
  <a-modal
    title="编辑"
    :width="500"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">

        <a-form-item
          style="display: none;"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-input v-decorator="['issueId']" />
        </a-form-item>


        <a-form-item
          style="display: none;"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-input v-decorator="['secondReview']" />
        </a-form-item>

        <a-form-item
          style="display: none;"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-input v-decorator="['statusId']" />
        </a-form-item>


        <a-form-item
          style="display: none;"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-input v-decorator="['reviewListNum']" />
        </a-form-item>

        <a-form-item
          label="评审结果"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-select
						v-decorator="[
              'reviewResult',
              {
                rules: [
                  { required: true, message: '请选择评审结果'},
                ],
              },
            ]"
						>
						<a-select-option value='1'>通过</a-select-option>
						<a-select-option value='2'>不通过</a-select-option>
            <a-select-option v-if="secondReview == 0" value='3'>再确认</a-select-option>
					</a-select>
        </a-form-item>
        <a-form-item
          label="评审意见"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入评审意见" v-decorator="['reviewOpinion', {rules: [{required: true, message: '请输入评审意见！'}]}]" />
        </a-form-item>
      </a-form>

    </a-spin>
  </a-modal>
</template>

<script>
import { updateReview } from "@/api/modular/system/topic"
export default {
    props:{
      
    },
    data () {
      return {
        secondReview:0,
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 18 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods:{
        open (record,listType) {
          setTimeout(() => {
            this.form.setFieldsValue(
              {
                issueId:record.issueId,
                secondReview:record.secondReview,
                reviewListNum:listType,
                reviewResult:record.secondReview == 0 ? record.reviewResult1 : record.reviewResult2,
                reviewOpinion:record.secondReview == 0 ? record.reviewOpinion1 : record.reviewOpinion2,
              }
            )
          }, 100)
          this.secondReview = record.secondReview
          this.visible = true
        },
        handleCancel () {
            this.form.resetFields()
            this.visible = false
            this.confirmLoading = false
        },
        handleSubmit () {
            const { form: { validateFields } } = this
            this.confirmLoading = true
            validateFields((errors, values) => {
            if (!errors) {
                updateReview(values).then((res) => {
                if (res.success) {
                    this.$message.success('操作成功')
                    this.handleCancel()
                    this.$emit('ok')
                } else {
                    this.$message.error('操作失败：' + res.message)
                }
                }).finally((res) => {
                    this.confirmLoading = false
                })
            } else {
                this.confirmLoading = false
            }
            })
        },
    }
}
</script>

<style>

</style>