<template>
  <a-modal
    :title="title"
    :width="500"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          style="display: none;"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-input v-decorator="['bomId']" />
        </a-form-item>

        <a-form-item
          label="原因"
          v-if="showReason"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-input v-decorator="['lineRemark', {rules: [{required: true, message: '请输入原因！'}]}]"  />
        </a-form-item>

        <a-form-item
          style="display: none;"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-input v-decorator="['bomType']" />
        </a-form-item>

        <a-form-item
          style="display: none;"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-input v-decorator="['bomIssueId']" />
        </a-form-item>

        <a-form-item
          label="工厂"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <treeselect style="width:350px" v-model="lindIds" placeholder="选择产线" :value-consists-of="valueConsistsOf" :multiple="true" :options="options"  :disable-branch-nodes="true"/>
        </a-form-item>

      </a-form>

    </a-spin>
  </a-modal>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import {getbomlines,sysBomAddWerks,getBom} from "@/api/modular/system/bomManage"
  export default {
    components: {
	    Treeselect
    },
    props:{
      werklines:{
        type:Object,
        default:{}
      }
    },
    data () {
      return {
        title:'',
        showReason:false,
        lineflag:0,
        lindIds:[],
        valueConsistsOf: 'ALL',
        labelCol: {
          xs: { span: 24 },
          sm: { span: 3 }
        },
        options:[],
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 18 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this),
        issueId:0
      }
    },
    created(){
    },
    methods: {
      callbomlines(record){
        this.confirmLoading = true
        getbomlines({bomIssueId:record.bomIssueId,bomType:record.bomType,bomId:record.id}).then((res) => {
        if (res.success) {
          this.showReason = res.data.lineflag == 1
          this.lineflag = res.data.lineflag
          let allines = res.data.allLines.map(x => {return x.lineId});
          let disablelines = allines.filter(function (item) {
            return record ? record.lines.indexOf(item) < 0 : false;
          })

          this.record = record
          this.issueId = record.bomIssueId
          this.title = record.lines && record.lines.length > 0 ? '增加工厂-产线' : '设置工厂-产线'
          let _options = []

          for (const key in this.werklines) {
           

            if (this.werklines[key]) {

              let children = []

              for (const item of this.werklines[key]) {

                if (res.data.originLines.indexOf(item.id) > -1) {
                  continue
                }

                if (disablelines.indexOf(item.id) >= 0) {
                  continue
                }

                children.push({
                  id:item.id,
                  label:item.lineName,
                  //isDisabled: disablelines.indexOf(item.id) < 0 ? false:true
                })
              }

              if (children.length > 0) {
                let _parent = {
                  id:key,
                  label: this.werklines[key][0].werkNo+'--'+this.werklines[key][0].werks+'--'+this.werklines[key][0].namecode,
                  children:children
                }
                _options.push(_parent)
              }

              
            }
          }
          setTimeout(() => {
             this.form.setFieldsValue(
              {
                bomIssueId:record.bomIssueId,
                bomId:record.id,
                bomType:record.bomType
              }
            )
            
          }, 100)
          this.options = _options
          this.visible = true

        }else{
          this.$message.error(res.message)
        }
          this.confirmLoading = false
        }).catch((err) => {
          this.$message.error('错误：' + err.message)
          this.confirmLoading = false
        })
      },
      edit (record) {
          /* if ([0,2].indexOf(parseInt(record.bomStatus)) < 0) {
            this.$message.error('BOM状态处于不可选择产线关联')
            return false
          } */


          /* getBom({
            id: record.id
          }).then((res) => {
            if(res.success){


              if(null != res.data.bomRemark){
                this.showReason = true;
              }

            }

            }).finally(()=>{
            this.lindIds = []
            this.callbomlines(record)
          }) */

          this.lindIds = []
          this.callbomlines(record)

          
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            let $params = {...values,lindIds:this.lindIds}
            sysBomAddWerks($params).then((res) => {
              if (res.success) {
                this.$message.success('保存成功')
                this.visible = false
                this.confirmLoading = false
                this.$emit('ok', values)
                this.form.resetFields()
                this.record.lines= [...this.record.lines,...this.lindIds]
                if (this.lineflag == 1) {
                  this.record.bomStatus = 5
                }
              } else {
                this.$message.error('保存失败：' + res.message)
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.lindIds = []
        this.form.resetFields()
        this.visible = false
        this.showReason = false
      }

    }
  }
</script>
<style>
.ant-form-item-control{
  line-height: initial;
}
.vue-treeselect--has-value .vue-treeselect__multi-value{
  margin: 0;
}
</style>
