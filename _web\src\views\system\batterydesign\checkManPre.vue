<template>
  <a-modal title="技术平台审批角色定义" width="80%" :visible="visible" :confirmLoading="confirmLoading" :footer="null"
           @cancel="handleCancel">


    <div style="display: flex">
      <div style="width: 33.3%">
        <div class="title">SOR审批(A/B样):</div>
        <div class="theme">审核:</div>
        <div class="check">
          <div class="select">
            <span class="xing">*</span><span class="label">部门经理:</span>
            <a-select
              show-search
              option-filter-prop="children"
              style="width: 60%"
              
              @search="(value) => handleSearch(value,'bmjlUserList')"
              @change="handleChange"
              v-model="checkMan.bmjl"
            >
              <a-select-option v-for="user in bmjlUserList" :key="user.account + '-' + user.name">
                {{user.account + '-' + user.name}}
              </a-select-option>
            </a-select>
          </div>
          <div class="select">
            <span class="xing">*</span><span class="label">总监:</span>
            <a-select
              show-search
              option-filter-prop="children"
              style="width: 60%"

              @search="(value) => handleSearch(value,'zjUserList')"
              @change="handleChange"
              v-model="checkMan.zj"
            >
              <a-select-option v-for="user in zjUserList" :key="user.account + '-' + user.name">
                {{user.account + '-' + user.name}}
              </a-select-option>
            </a-select>
          </div>


        </div>
        <div class="theme">批准:</div>
        <div class="approve">
          <div class="select">
            <span class="xing">*</span><span class="label">所长:</span>
            <a-select
              show-search
              option-filter-prop="children"
              style="width: 60%"
              
              @search="(value) => handleSearch(value,'szUserList')"
              @change="handleChange"
              v-model="checkMan.sz"
            >
              <a-select-option v-for="user in szUserList" :key="user.account + '-' + user.name">
                {{user.account + '-' + user.name}}
              </a-select-option>
            </a-select>
          </div>

        </div>

      </div>
      <div style="width: 33.3%">
        <div class="title">方案制样审批:</div>
        <div class="theme">审核:</div>
        <div class="check">
          <div class="select">
            <span class="xing">*</span><span class="label">方案设计:</span>
            <a-select
              show-search
              option-filter-prop="children"
              style="width: 60%"
              
              @search="(value) => handleSearch(value,'fasjUserList')"
              @change="handleChange"
              v-model="checkMan.fasj"
            >
              <a-select-option v-for="user in fasjUserList" :key="user.account + '-' + user.name">
                {{user.account + '-' + user.name}}
              </a-select-option>
            </a-select>
          </div>

          <div class="select">
            <span class="xing">*</span><span class="label">MI结构:</span>
            <a-select
              show-search
              option-filter-prop="children"
              style="width: 60%"
              
              @search="(value) => handleSearch(value,'mijgUserList')"
              @change="handleChange"
              v-model="checkMan.mijg"
            >
              <a-select-option v-for="user in mijgUserList" :key="user.account + '-' + user.name">
                {{user.account + '-' + user.name}}
              </a-select-option>
            </a-select>
          </div>

          <div class="select">
            <span class="xing">*</span><span class="label">MI工艺:</span>
            <a-select
              show-search
              option-filter-prop="children"
              style="width: 60%"
              
              @search="(value) => handleSearch(value,'migyUserList')"
              @change="handleChange"
              v-model="checkMan.migy"
            >
              <a-select-option v-for="user in migyUserList" :key="user.account + '-' + user.name">
                {{user.account + '-' + user.name}}
              </a-select-option>
            </a-select>
          </div>

          <div class="select">
            <span class="xing">*</span><span class="label">BOM:</span>
            <a-select
              show-search
              option-filter-prop="children"
              style="width: 60%"
              
              @search="(value) => handleSearch(value,'bomUserList')"
              @change="handleChange"
              v-model="checkMan.bom"
            >
              <a-select-option v-for="user in bomUserList" :key="user.account + '-' + user.name">
                {{user.account + '-' + user.name}}
              </a-select-option>
            </a-select>
          </div>



        </div>
        <div class="theme">批准:</div>
        <div class="approve">
          <div class="select">
            <span class="xing">*</span><span class="label">总监:</span>
            <a-select
              show-search
              option-filter-prop="children"
              style="width: 60%"
              
              @search="(value) => handleSearch(value,'zjUserList')"
              @change="handleChange"
              v-model="checkMan.zj"
            >
              <a-select-option v-for="user in zjUserList" :key="user.account + '-' + user.name">
                {{user.account + '-' + user.name}}
              </a-select-option>
            </a-select>
          </div>

        </div>
        <div class="theme">文件归档:</div>
        <div class="file">
          <div class="select">
            <span class="xing">*</span><span class="label">文控:</span>
            <a-select
              show-search
              option-filter-prop="children"
              style="width: 60%"
              
              @search="(value) => handleSearch(value,'wkUserList')"
              @change="handleChange"
              v-model="checkMan.wk"
            >
              <a-select-option v-for="user in wkUserList" :key="user.account + '-' + user.name">
                {{user.account + '-' + user.name}}
              </a-select-option>
            </a-select>
          </div>
        </div>
      </div>
      <div style="width: 33.3%">
        <div class="title">冻结审批:</div>
        <div class="theme">审核:</div>
        <div class="check">
          <div class="select">
            <span class="xing">*</span><span class="label">方案设计:</span>
            <a-select
              show-search
              option-filter-prop="children"
              style="width: 60%"
              
              @search="(value) => handleSearch(value,'fasjUserList')"
              @change="handleChange"
              v-model="checkMan.fasj"
            >
              <a-select-option v-for="user in fasjUserList" :key="user.account + '-' + user.name">
                {{user.account + '-' + user.name}}
              </a-select-option>
            </a-select>
          </div>

          <div class="select">
            <span class="xing">*</span><span class="label">MI结构:</span>
            <a-select
              show-search
              option-filter-prop="children"
              style="width: 60%"
              
              @search="(value) => handleSearch(value,'mijgUserList')"
              @change="handleChange"
              v-model="checkMan.mijg"
            >
              <a-select-option v-for="user in mijgUserList" :key="user.account + '-' + user.name">
                {{user.account + '-' + user.name}}
              </a-select-option>
            </a-select>
          </div>

          <div class="select">
            <span class="xing">*</span><span class="label">MI工艺:</span>
            <a-select
              show-search
              option-filter-prop="children"
              style="width: 60%"
              
              @search="(value) => handleSearch(value,'migyUserList')"
              @change="handleChange"
              v-model="checkMan.migy"
            >
              <a-select-option v-for="user in migyUserList" :key="user.account + '-' + user.name">
                {{user.account + '-' + user.name}}
              </a-select-option>
            </a-select>
          </div>

          <div class="select">
            <span class="xing">*</span><span class="label">BOM:</span>
            <a-select
              show-search
              option-filter-prop="children"
              style="width: 60%"
              
              @search="(value) => handleSearch(value,'bomUserList')"
              @change="handleChange"
              v-model="checkMan.bom"
            >
              <a-select-option v-for="user in bomUserList" :key="user.account + '-' + user.name">
                {{user.account + '-' + user.name}}
              </a-select-option>
            </a-select>
          </div>

          <div class="select">
            <span class="xing">*</span><span class="label">总监:</span>
            <a-select
              show-search
              option-filter-prop="children"
              style="width: 60%"
              
              @search="(value) => handleSearch(value,'zjUserList')"
              @change="handleChange"
              v-model="checkMan.zj"
            >
              <a-select-option v-for="user in zjUserList" :key="user.account + '-' + user.name">
                {{user.account + '-' + user.name}}
              </a-select-option>
            </a-select>
          </div>


        </div>
        <div class="theme">批准:</div>
        <div class="approve">
          <div class="select">
            <span class="xing">*</span><span class="label">所长:</span>
            <a-select
              show-search
              option-filter-prop="children"
              style="width: 60%"

              @search="(value) => handleSearch(value,'szUserList')"
              @change="handleChange"
              v-model="checkMan.sz"
            >
              <a-select-option v-for="user in szUserList" :key="user.account + '-' + user.name">
                {{user.account + '-' + user.name}}
              </a-select-option>
            </a-select>
          </div>

        </div>
        <div class="theme">文件归档:</div>
        <div class="file">
          <div class="select">
            <span class="xing">*</span><span class="label">文控:</span>
            <a-select
              show-search
              option-filter-prop="children"
              style="width: 60%"
              
              @search="(value) => handleSearch(value,'wkUserList')"
              @change="handleChange"
              v-model="checkMan.wk"
            >
              <a-select-option v-for="user in wkUserList" :key="user.account + '-' + user.name">
                {{user.account + '-' + user.name}}
              </a-select-option>
            </a-select>
          </div>
        </div>
      </div>


    </div>


  </a-modal>
</template>

<script>
  import {
    sysBatteryDesignEdit
  } from '@/api/modular/system/batterydesignManage'


  import {
    getUserAllLists
  } from '@/api/modular/system/userManage'

  export default {
    data() {
      return {
        bmjlUserList: [],
        zjUserList: [],
        szUserList: [],
        yzUserList: [],
        fasjUserList: [],
        mijgUserList: [],
        migyUserList: [],
        bomUserList: [],
        wkUserList: [],
        checkMan: {},
        record: {},
        visible: false,
        confirmLoading: false,
      }
    },

    methods: {
      handleChange(value) {
        this.$nextTick(() => {

          let param = {id: this.record.id, checkJson: JSON.stringify(this.checkMan)}
          if (this.checkMan.zj && this.checkMan.bmjl && this.checkMan.sz
            && this.checkMan.fasj && this.checkMan.mijg && this.checkMan.migy && this.checkMan.bom && this.checkMan.wk) {
            param.isDefine = 1
          }
          sysBatteryDesignEdit(param)
        })
      },
      handleSearch(value, listName) {
        if (value != '' && value != null) {
          getUserAllLists({account: value}).then((res) => {
            this[listName] = res.data
          })
        }

      },

      open(record) {
        this.checkMan = record.checkJson ? JSON.parse(record.checkJson) : {}

        this.$nextTick(() => {
          if (this.checkMan.bmjl) {
            this.bmjlUserList.push({account: this.checkMan.bmjl.split('-')[0], name: this.checkMan.bmjl.split('-')[1]})
          }
          if (this.checkMan.zj) {
            this.zjUserList.push({account: this.checkMan.zj.split('-')[0], name: this.checkMan.zj.split('-')[1]})
          }
          if (this.checkMan.sz) {
            this.szUserList.push({account: this.checkMan.sz.split('-')[0], name: this.checkMan.sz.split('-')[1]})
          }
          /*if (this.checkMan.yz) {
            this.yzUserList.push({account: this.checkMan.yz.split('-')[0], name: this.checkMan.yz.split('-')[1]})
          }*/
          if (this.checkMan.fasj) {
            this.fasjUserList.push({account: this.checkMan.zj.split('-')[0], name: this.checkMan.fasj.split('-')[1]})
          }
          if (this.checkMan.mijg) {
            this.mijgUserList.push({account: this.checkMan.zj.split('-')[0], name: this.checkMan.mijg.split('-')[1]})
          }
          if (this.checkMan.migy) {
            this.migyUserList.push({account: this.checkMan.zj.split('-')[0], name: this.checkMan.migy.split('-')[1]})
          }
          if (this.checkMan.bom) {
            this.bomUserList.push({account: this.checkMan.zj.split('-')[0], name: this.checkMan.bom.split('-')[1]})
          }
          if (this.checkMan.wk) {
            this.wkUserList.push({account: this.checkMan.zj.split('-')[0], name: this.checkMan.wk.split('-')[1]})
          }
        })


        this.record = record
        this.visible = true
      },
      handleCancel() {
        this.zjUserList = []
        this.szUserList = []
        this.yzUserList = []
        this.fasjUserList = []
        this.mijgUserList = []
        this.migyUserList = []
        this.bomUserList = []
        this.wkUserList = []
        this.checkMan = {}
        this.$emit('ok')
        this.visible = false
      }
    }
  }
</script>
<style lang="less" scoped>
  .ant-form-item {
    margin-bottom: 0px;
  }

  .title {
    margin-bottom: 20px;
  }

  .xing {
    color: red;
    font-weight: bolder;
  }

  .label {
    width: 30%;
    display: inline-flex;
  }

  .theme {
    font-size: larger;
    font-weight: bolder;
    margin-bottom: 20px;
  }

  div {
    color: black;
  }

  span {
    color: black;
  }

  .select {
    margin-bottom: 3px;
  }

  .check {
    height: 210px;
  }

  .approve {
    height: 90px;
  }

  /deep/ .ant-modal-title {

    font-weight: bolder;
    font-size: 22px;
    color: #0049b0;
  }

</style>
