<template>
  <div>
    <a-breadcrumb class="breadcrumb" separator=">">
      <a-breadcrumb-item><a @click="gotoIndex">信息对齐表</a></a-breadcrumb-item>
      <a-breadcrumb-item>产品开发进展</a-breadcrumb-item>
    </a-breadcrumb>
    <div class="head" v-if="showDetail">{{ cateTitle }}产品开发进展</div>
    <ve-table
      v-if="showDetail"
      :border-y="true"
      ref="tableRef"
      fixed-header
      :max-height="windowHeight > 380 ? windowHeight : 380"
      :columns="columns"
      :table-data="rows"
      :cell-style-option="cellStyleOption"
      :cell-span-option="cellSpanOption"
      :scroll-width="scrollWidth"
      :footer-data="footerData"
      :fixedFooter="fixedFooter"
      id="loading-detail"
      
    />
  </div>
</template>

<script>
const OtherComp = {
        name: "OtherComp",
        template: `
            <div class="feedback">
                <div v-for='item in arr'>{{item}}</div>
            </div>
        `,
        props: {
            arr: Array,
        },
    }
const Fold = {
        name: "fold",
        template: `
            <span class='flex_3'>
                <a v-if='fold' @click='unfoldok'><a-icon type="plus-square" /></a>
                <a v-else @click='foldok'><a-icon type="minus-square" /></a>
            </span>
        `,
        props: {
            fold: Boolean,
            foldFun:Function,
            unfoldFun:Function
        },
        methods:{
          foldok(){
            this.foldFun()
          },
          unfoldok(){
            this.unfoldFun()
          }
        }
    }
import { getReportProductData } from "@/api/modular/system/report"
import { mapActions, mapGetters } from 'vuex'
import { clamp } from '@/components'
import { ALL_APPS_MENU } from '@/store/mutation-types'
    import Vue from 'vue'
export default {
  name:'detail',
  data() {
    return {
      top: 10,
      fixedFooter:false,
      scrollWidth:null,
      showDetail: false,
      loadingInstance: null,
      cateTitle:"",
      windowHeight: document.documentElement.clientHeight - 85,
      showTitle: {
        showTitle: true,
      },
      planfold:true,
      feedbackfold:true,
      processfold:false,
      infofold:false,
      advancefold:true,
      columns: [],
      planColumns:[],
      feedbackColumns:[],
      processColumns:[],
      infoColumns:[],
      advanceColumns:[],
      rows: [],
      merges: [],
      exmerges: [],
      pmerge:['productProjectName','productLevel'],
      footerData: [{ productName: "" }],
      cellStyleOption: {
        bodyCellClass: ({ row, column, rowIndex }) => {
          let statusarrtxt = [
            "fixedState",
            "ko",
            "m1",
            "m2",
            "m2t",
            "m3",
            "m3t",
            "m4",
            "m5",
            "m6",
            "sop",
          ];

          let statusarr = [
            "fixedState",
            "ko",
            "m1",
            "m2",
            "m2t",
            "m3",
            "m3t",
            "m4",
            "m5",
            "m6",
            "sop",
          ];
          
          let obj = {
            0: '',//"dangerdetail",
            1: '',//"warningdetail",
            2: "successdetail",
            3: "tbd",
            4: "processdetail",
          };
          let index = statusarrtxt.indexOf(column.key);
          if (index > -1) {
            
            if (statusarr[index] == 'm6' && row['state'] == 8) {
              return 'tbd';//"warningdetail";
            }


            if (row[statusarr[index]] == 4) {
              if(row['state'] == 7) {
                return 'tbd';
              }
              if (row["overDays"] < 0) {
                return obj[2];
              }
              if (row["overDays"] == 0 || row["overDays"] < 7) {
                return obj[1];
              }
              if (row["overDays"] >= 7) {
                return obj[0];
              }
            }
            if (row[statusarr[index]] == -1) {
              return obj[1];
            }
            if (statusarr[index] == 'fixedState' && row[statusarr[index]] == 1) {
              return '';//"warningdetail";
            }

            
            
            return obj[row[statusarr[index]]];
          }
        },
      },
      
      cellSpanOption: {
        bodyCellSpan: this.bodyCellSpan,
        footerCellSpan: this.footerCellSpan,
      },
      
    };
  },
  components: {
      clamp,
      Fold
  },
  methods: {
    gotoIndex(){
      /* this.switchApp()
      this.$router.push({
        path: "/report_sum",
      }); */
      this.$router.go(-1)
    },
    tipwidth(){
      this.$nextTick(() => {
        var tips=document.getElementById('tips');
        tips.style.width=document.documentElement.clientWidth;
      })
    },
    renderHeaderCellTop({column}){
      if (column.field == 'ee') {
        return <span class='flex_1'><span class='flex_2'>{column.title}</span><Fold fold={this.infofold} foldFun={this.infoFold} unfoldFun={this.uninfoFold} /></span>
      }
      if (column.field == 'aa') {
        return <span class='flex_1'><span class='flex_2'>{column.title}</span><Fold fold={this.processfold} foldFun={this.processFold} unfoldFun={this.unprocessFold} /></span>
      }
      if (column.field == 'bb') {
        return <span class='flex_1'><span class='flex_2'>{column.title}</span><Fold fold={this.advancefold} foldFun={this.advanceFold} unfoldFun={this.unadvanceFold} /></span>
      }
      
      if (column.field == 'cc') {
        return <span class='flex_1'><span class='flex_2'>{column.title}</span><Fold fold={this.feedbackfold} foldFun={this.feedbackFold} unfoldFun={this.unfeedbackFold} /></span>
      }


       if (column.field == 'ff') {
        return <span class='flex_1'><span class='flex_2'>{column.title}</span><Fold fold={this.planfold} foldFun={this.planFold} unfoldFun={this.unplanFold} /></span>
      }
      return <span>{column.title}</span>
    },
    unplanFold(){
      let $i = this.columns.findIndex(item => item.field == 'ff')
      this.columns.splice($i+1,0,this.planColumns)
      this.planfold = false
    },
    unfeedbackFold(){
      this.columns.push(this.feedbackColumns)
      this.feedbackfold = false
    },
    unprocessFold(){
      let $i = this.columns.findIndex(item => item.field == 'aa')
      this.columns.splice($i+1,0,this.processColumns)
      this.processfold = false
    },
    uninfoFold(){
      let $i = this.columns.findIndex(item => item.field == 'ee')
      this.columns.splice($i+1,0,this.infoColumns)
      this.infofold = false
    },
    unadvanceFold(){
      let $i = this.columns.findIndex(item => item.field == 'bb')
      this.columns.splice($i+1,0,this.advanceColumns)
      this.advancefold = false
    },
    planFold(){
      let columns = this.columns;
      let $i = columns.findIndex(item => item.field == 'cc')
      columns.splice($i,1)
      this.columns = columns
      this.planfold = true
    },
    feedbackFold(){
      let columns = this.columns;
      let $i = columns.findIndex(item => item.field == 'dd')
      columns.splice($i,1)
      this.columns = columns
      this.feedbackfold = true
    },
    processFold(){
      let columns = this.columns;
      let $i = columns.findIndex(item => item.field == 'bb')
      columns.splice($i,1)
      this.columns = columns
      this.processfold = true
    },

    infoFold(){

      let columns = this.columns;
      let $i = columns.findIndex(item => item.field == 'aa')
      columns.splice($i,1)
      this.columns = columns
      this.infofold = true
    },

    advanceFold(){
      let columns = this.columns;
      let $i = columns.findIndex(item => item.field == 'ff')
      columns.splice($i,1)
      this.columns = columns
      this.advancefold = true
    },
    renderHeaderCell({ column }) {
      return (<a-tooltip placement="bottom">
                <template slot="title">
                  <div>圆柱:直径*高</div>
                  <div>方形:宽*厚*高</div>
                </template>
                <span>{column.title}</span>
              </a-tooltip>);
    },
    getCharCount(str,char){
      let count=0;
      while(str.indexOf(char) != -1 ) {
          str = str.replace(char,"")//把已计数的替换为空
          count++;
      }
      return count;
    },
    callReportProductData() {
      this.show()
      getReportProductData(this.$route.query)
        .then((res) => {
          if (res.result) {
            let feedbackArr = ['size',"dischargeRate","processStr","performance","produceFeedback","sellFeedback","supplyFeedback"]
            if(res.data.columndata)
              for (var item of res.data.columndata) {

                item.renderHeaderCell = this.renderHeaderCellTop
              
                if (item.children) {
                  
                  for (var _item of item.children) {
                    _item.renderBodyCell = this.renderBodyCell;

                    if (_item.field == "productName") {
                      _item.renderFooterCell = this.renderFooterCell;
                    }

                    if (feedbackArr.indexOf(_item.field) < 0) {
                      _item.ellipsis = this.showTitle;
                    }

                    if (_item.field == 'size') {
                      _item.renderHeaderCell = this.renderHeaderCell
                    }
                    
                    if (_item.children) {

                      for (const $item of _item.children) {
                        $item.renderBodyCell = this.renderBodyCell;
                      }
                    }
                    
                  }
                }
              }
            if(res.data.merges)
              res.data.merges.forEach((item) => {
              for (let i = 0, j = res.data.rowdata.length; i < j; i++) {
                let rowSpan = 0;
                let n = i;
                while (
                  //res.data.rowdata[n + 1] != '' &&
                  res.data.rowdata[n + 1] &&
                  res.data.rowdata[n + 1][item] == res.data.rowdata[n][item]
                ) {
                  rowSpan++;
                  n++;
                  res.data.rowdata[n].rowSpan = 0;
                }
                if (rowSpan) res.data.rowdata[i][item + "_rowSpan"] = rowSpan + 1;

                if (!rowSpan) res.data.rowdata[i][item + "_rowSpan"] = 1;

                i += rowSpan;
              }
            });

            let columns = []
            for (let i = 0,j = 3; i < j; i++) {
              columns.push(res.data.columndata[i])
            }
            this.columns = columns
            this.planColumns = res.data.columndata[4]
            this.feedbackColumns = res.data.columndata[5]
            this.advanceColumns = res.data.columndata[3]

            this.processColumns = res.data.columndata[2]
            this.infoColumns = res.data.columndata[1]
            this.rows = res.data.rowdata;
            
            this.showDetail = true;
            this.merges = res.data.merges;
            this.exmerges = res.data.exmerges;
            this.scrollWidth = 2720

            //this.columnHiddenOption.defaultHiddenColumnKeys.push(['supplierCustomer','dept' ,'factory','ppm','productLine','productCap','sop','kpcs2022','kpcs2023','produceFeedback','sellFeedback','supplyFeedback'])
          } else {
            this.$message.error('错误提示：' + res.message,1)
          }
          this.close()
        })
        .catch((err) => {
          this.close()
          this.$message.error('错误提示：' + err.message,1)
        });
    },
    bodyCellSpan({ row, column, rowIndex }) {
      if (this.merges.includes(column.key)) {
        const _col = row[column.key + "_rowSpan"] > 0 ? 1 : 0;
        return {
          colspan: _col,
          rowspan: row[column.key + "_rowSpan"],
        };
      }
      if (!this.exmerges.includes(column.key)) {
    

        let $index = this.pmerge.includes(column.key) ? this.merges.length - 2 : this.merges.length - 1

        let dataindex = this.merges[$index];
        const _row = row[dataindex + "_rowSpan"];
        const _col = _row > 0 ? 1 : 0;

        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
    footerCellSpan({ row, column, rowIndex }) {
      if (column.field == "productName") {
        return {
          rowspan: 1,
          colspan: 20,//33
        };
      } else {
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    },

    renderBodyCell({ row, column, rowIndex }) {
      let statusarrtxt = [
        "ko",
        "m1",
        "m2",
        "m2t",
        "m3",
        "m3t",
        "m4",
        "m5",
        "m6",
      ];

      let statusarr = ["fixedState"]//["fixedState", "sop"];

      let index = statusarrtxt.indexOf(column.key);

      if (index > -1) {
        return (
          <span
            class="clickcellstyle"
            onClick={() => {
              this.handleToDetail(row, column,index);
            }}
          >
            {row[column.key + "Date"]}
          </span>
        );
      }
      if (statusarr.indexOf(column.key) > -1) {
        let isfixed = row[column.key]
        if (isfixed == 2) {
          return <span>已定点</span>;
        }else{
          return <span>{row['plannedFixedDate']}</span>;
        }
        
      }
      if ("productProjectName" == column.key) {
        return (
          <span
            class="clickcellstyle"
            onClick={() => {
              this.handleTo(row, column);
            }}
          >
            {row[column.key]}
          </span>
        );
      }
      let classArr = ["productName", "productCateName"];
      if (classArr.indexOf(column.key) > -1) {
        return <span class="vertical-lr">{row[column.key]}</span>;
      }
      let feedbackArr = ["performance","produceFeedback","sellFeedback","supplyFeedback"]
      if (feedbackArr.indexOf(column.key) > -1) {
        //return <div class='feedback'>{row[column.key]}</div>
        return <clamp text={row[column.key]} sourceText={[row[column.key]]}></clamp>
      }
      let rateArr = ["dischargeRate"]
      if (rateArr.indexOf(column.key) > -1) {
        return <span>{row[column.key]}</span>
      }
      let processArr = ["processStr"]
      if (processArr.indexOf(column.key)>-1) {
        let strArr = row[column.key].split('#')
        let str = strArr.join('\n')
        //return <OtherComp arr = {strArr} />
        return <clamp text={str} sourceText={strArr}></clamp>
        
      }

      let sizeArr = ['size']
      if (sizeArr.indexOf(column.key) > -1) {

        let count = this.getCharCount(row[column.key],'*')
        let txt = count > 1 ? '方形:宽*厚*高' : ( count  == 0 ? '' :'圆柱:直径*高')
        return txt ? <a-tooltip placement="top">
                <template slot="title">
                  <span>{txt}</span>
                </template>
                <span>{row[column.key]}</span>
              </a-tooltip> : <div>{row[column.key]}</div>
      }
      return row[column.key];
    },

    /* <span>
              <label>待完成</label>
              <label class="yellow"></label>
            </span>
            <span>
              <label>逾期大于7天</label>
              <label class="danger"></label>
            </span> */
    renderFooterCell({ row, column, rowIndex }) {
      return (
        <div id="tips" class="tips" style="justify-content: center;height:30px;align-items:center;background:#fff;">
            
            <span>
              <label>已完成</label>
              <label class="green"></label>
            </span>
            
            <span>
              <label>暂停</label>
              <label class="brown"></label>
            </span>
          </div>
      );
    },
    handleToDetail(row, column,index) {
      if (row[column.key + "Date"] == '--') {
        return false;
      }
      this.switchApp()
      this.$router.push({
        path: "/stage",
        query: {
          issueId: row["issueId"],
          productProjectName: row["productProjectName"],
          date:row[column.key + "Date"],
          stage:index+1
        },
      });
    },
    handleTo(row, column) {
      this.switchApp()
      this.$router.push({
        path: "/docs",
        query: {
          title: row[column.key],
          issueId: row["issueId"],
          issueKey: row["issueKey"].replace(row["cateId"],""),
          cateId:row['cateId']
        },
      });
    },
    switchApp () {
        const apps = Vue.ls.get(ALL_APPS_MENU)
                const _newApps = []
                for (const item of apps) {
                    
                    _newApps.push(item)
                }
                Vue.ls.set(ALL_APPS_MENU, _newApps)
    },
    show() {
      this.loadingInstance.show();
    },
    close() {
      this.loadingInstance.close();
    },
    ...mapActions(['MenuChange']),
    hideColumns(keys) {
      this.$refs['tableRef'].hideColumnsByKeys(keys);
    },
  },
  /* watch:{
    columns: {
      handler: function (val, oldVal) { 
        this.columns = val
      },
      deep: true,
      immediate: true
    }
  }, */
  computed: {
    ...mapGetters(['userInfo'])
  },
  created() {
    this.loadingInstance = this.$veLoading({
      target: document.querySelector("#loading-detail"),
      name: "flow",
    });
    this.cateTitle=this.$route.query.cateName
    this.callReportProductData()
    
  },
  destroyed() {
    this.loadingInstance.destroy();
  },
};
</script>

<style lang="less">
@import './vetable.less';
.infodetail {
  background: #e9e9eb !important;
  color: #909399;
}
.warningdetail {
  background: #fff5ad !important;
  color: #000;
}
.successdetail {
  background: #b4f7ac !important;
  color: #000;
}
.processdetail {
  background: #eaf5e4 !important;
  color: #000;
}
.dangerdetail {
  background: #fc8585 !important;
  color: #000;
}
.tbd{
  background: #cacaca !important;
  color: #000;
}
.head {
  border: 1px solid #97b1e7;
  border-bottom: none;
  background: #eaf0fa;
  text-align: center;
  padding: 10px 0;
  color: #000;
  font-size: 20px;
}

.vertical-lr{
  display: block;
  writing-mode: vertical-lr;
  margin: auto;
  letter-spacing: 5px;
}

.feedback{
  padding-left:2px;
  text-align: left;
  word-break: break-all;
}

/* .other-comp div{
  vertical-align: baseline;
  padding: 0 5px;
} */
.flex_1 {
  display: flex;
}
.flex_2{
  flex: 1;
}
.flex_3{
  margin-right: 8px;
}
.breadcrumb{
  padding: 5px 0;
  padding-left: 13px;
}.ant-breadcrumb a{
  color: #0d87d8 !important;
}.ant-breadcrumb{
  font-size: 12px !important;
}
</style>