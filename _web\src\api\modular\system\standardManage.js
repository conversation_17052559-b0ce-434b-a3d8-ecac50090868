import { axios } from '@/utils/request'

export function add (parameter) {
  return axios({
    url: '/standard/add',
    method: 'post',
    data: parameter
  })
}

export function edit (parameter) {
  return axios({
    url: '/standard/edit',
    method: 'post',
    data: parameter
  })
}

export function getList (parameter) {
  return axios({
    url: '/standard/list',
    method: 'get',
    params: parameter
  })
}


export function getProductList (parameter) {
  return axios({
    url: '/standard/getProductList',
    method: 'get',
    params: parameter
  })
}

export function getById(parameter) {
  return axios({
    url: '/standard/getById',
    method: 'get',
    params: parameter
  })
}

export function summitJIRA (parameter) {
  return axios({
    url: '/standard/summitJIRA',
    method: 'post',
    data: parameter
  })
}


export function getProducts (parameter) {
  return axios({
    url: '/standard/getProducts',
    method: 'post',
    data: parameter
  })
}


export function getLogListByexecId(parameter) {
  return axios({
    url: '/standard/getLogListByexecId',
    method: 'get',
    params: parameter
  })
}

