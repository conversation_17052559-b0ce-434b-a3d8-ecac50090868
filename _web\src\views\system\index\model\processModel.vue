<template>
	<a-modal
		:title="`${title}项目进度`"
		:visible="true"
		:width="700"
		:centered="true"
		okText="查看详情"
		cancelText="关闭"
		@cancel="handleModelCancel"
		@ok="handleModelOk"
	>
		<div class="model-wrapper">
			<a-spin :spinning="spinning">
				<a-icon slot="indicator" type="loading" style="font-size: 24px" spin />
				<div class="chart-wrapper">
					<a-icon @click="handleShow" class="icon" :class="{ 'no-point': isTotalChart }" type="left" />
					<div v-show="isTotalChart" class="chart_table" ref="total"></div>
					<div v-show="!isTotalChart" class="chart_table" ref="abnormal"></div>
					<a-icon @click="handleShow" class="icon" :class="{ 'no-point': !isTotalChart }" type="right" />
				</div>
			</a-spin>
		</div>
	</a-modal>
</template>

<script>
import { getProjectProcessDetail } from "@/api/modular/system/chartManage"
import { title } from "process"

export default {
	props: {
		title: {
			type: String,
			default: ""
		}
	},
	data() {
		return {
			projectProcessTotal: {},
			projectProcessTotalAxis: [],

			delayMap: {},
			stopMap: {},

			isTotalChart: true,
			spinning: false
		}
	},
	watch: {
		title() {
			this.callProjectProcessDetail()
		}
	},
	mounted() {
		this.callProjectProcessDetail()
	},
	methods: {
		// 获取数据
		callProjectProcessDetail() {
			this.spinning = true
			getProjectProcessDetail({ dept: this.title })
				.then(res => {
					if (res.result) {
						let level = res.data.list ? res.data.list : {}
						for (const i in level) {
							for (let $i = 0, j = level[i].length; $i < j; $i++) {}
						}
						this.projectProcessTotal = level
						this.projectProcessTotalAxis = res.data.axis ? res.data.axis : []
						this.data = res.data.datas

						this.delayMap = res.data.delayMap
						this.stopMap = res.data.stopMap

						this.$nextTick(() => {
							this.initTotal()
							this.initNormal()
						})
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
				})
				.catch(err => {
					this.$message.error("错误提示：" + err.message, 1)
				})
				.finally(() => {
					this.spinning = false
				})
		},

		/**
		 * echarts 图表
		 */

		initTotal() {
			let chart = this.echarts.init(this.$refs.total)
			chart.clear()

			let dataSum = [0, 0, 0, 0, 0]

			for (const k in this.projectProcessTotal) {
				for (let i = 0, j = this.projectProcessTotal[k].length; i < j; i++) {
					dataSum[i] += Number(this.projectProcessTotal[k][i])
				}
			}

			let dataRatio = []

			for (let i = 0, j = this.projectProcessTotal[1].length; i < j; i++) {
				dataRatio.push(dataSum[i] == 0 ? 0 : ((this.projectProcessTotal[1][i] / dataSum[i]) * 100).toFixed(1))
			}
			if (this.projectProcessTotalAxis.length === 0) {
				const options = {
					title: {
						text: "暂无数据",
						x: "center",
						y: "center",
						textStyle: {
							fontSize: 14,
							fontWeight: "normal"
						}
					}
				}
				chart.setOption(options)
				chart.resize()
			} else {
				const options = {
					title: {
						text: "项目进展分析",
						textStyle: {
							fontSize: 16,
							fontWeight: "normal"
						}
					},
					legend: {
						itemWidth: 8,
						itemHeight: 8,
						bottom: "1%"
					},
					grid: {
						left: "3%",
						right: "4%",
						bottom: "12%",
						containLabel: true
					},
					xAxis: {
						type: "category",
						data: this.projectProcessTotalAxis,
						axisLabel: {
							interval: 0,
							textStyle: {
								color: "#000",
								fontSize: "10"
							}
						}
					},
					yAxis: [
						{
							show: false,
							type: "value"
						},
						{
							show: false,
							type: "value"
						}
					],
					series: [
						{
							name: "正常",
							type: "bar",
							stack: "total",
							label: {
								show: false,
								position: "top"
							},
							itemStyle: {
								color: "#5b9bd5"
							},
							barMaxWidth: "10%",
							data: this.projectProcessTotal[1]
						},
						{
							name: "延期",
							type: "bar",
							stack: "total",
							label: {
								show: false,
								position: "top"
							},
							itemStyle: {
								color: "#ed7d31"
							},
							barMaxWidth: "10%",
							data: this.projectProcessTotal[2]
						},
						{
							name: "停止",
							type: "bar",
							stack: "total",
							label: {
								show: false,
								position: "top"
							},
							itemStyle: {
								color: "#a5a5a5"
							},
							barMaxWidth: "10%",
							data: this.projectProcessTotal[3]
						},
						// 进度正常率
						{
							name: "进度正常率",
							type: "line",
							data: dataRatio,
							yAxisIndex: 1,
							itemStyle: {
								normal: {
									color: "#91cc75",
									label: {
										show: true,
										position: "top",
										formatter: (value, index) => {
											return value.data + "%"
										},
										textStyle: {
											color: "#000",
											fontSize: "8"
										}
									}
								}
							}
						}
					]
				}
				chart.setOption(options)
				chart.resize()
			}
		},

		async initNormal() {
			let chart = this.echarts.init(this.$refs.abnormal)
			let depts = Object.keys(this.stopMap)
			const color = ["#ed7d31", "#a5a5a5"]
			const xAxis = ["", "k0", "M1", "M2", "M3", ""]
			const days = Object.keys(this.stopMap)
			const title = []
			const singleAxis = []
			const series = []
			let delayData = []
			let stopData = []

			//数据处理
			await this._handleArray(this.delayMap, delayData)
			await this._handleArray(this.stopMap, stopData, true)

			let options = {
				title: {
					text: "进展异常分析",
					textStyle: {
						fontSize: 16,
						fontWeight: "normal"
					}
				},
				legend: {
					left: "right"
				},
				tooltip: {
					position: "bottom",
					formatter: function(params) {
						return `项目状态：${params.seriesName}\n${params.color === "#ed7d31" ? "延迟天数:" + params.value[2] : ""}`
					}
				},
				grid: {
					left: 2,
					bottom: 10,
					right: 10,
					containLabel: true
				},
				xAxis: {
					type: "category",
					data: xAxis,
					boundaryGap: false,
					axisLine: {
						show: true
					},axisLabel: {
						interval: 0,
						textStyle: {
							color: "#000",
							fontSize: "10"
						}
					}
				},
				yAxis: {
					type: "category",
					data: days,
					axisLine: {
						show: true
					}
				},
				series: [
					{
						name: "延迟",
						type: "scatter",
						color: "#ed7d31",
						symbolSize: function(val) {
							return val[2] * 2
						},
						data: delayData,
						animationDelay: function(idx) {
							return idx * 5
						}
					},
					{
						name: "停止",
						type: "scatter",
						color: "#a5a5a5",
						symbolSize: function(val) {
							return val[2] * 2
						},
						symbol: "diamond",
						data: stopData,
						animationDelay: function(idx) {
							return idx * 5
						}
					}
				]
			}
			chart.setOption(options)
			chart.resize()
		},

		/**
		 * 弹窗事件
		 */
		handleModelCancel() {
			this.$emit("cancel")
			this.isTotalChart = true
		},

		handleModelOk() {
			this.$router.push({
				path: "/project_process",
				query: {
					dept: this.title
				}
			})
		},

		handleShow() {
			this.isTotalChart = !this.isTotalChart
		},

		// 处理数据
		_handleArray(obj, data = [], isStop = false) {
			Object.keys(obj).forEach((value, index) => {
				if (obj[value].length) {
					obj[value].forEach((element, idx) => {
						if (element.length) {
							element.forEach(v => {
								if (isStop) {
									v = Number(v) * 100
								}
								data.push([idx + 1, index, Number(v) / 10])
							})
						}
					})
				}
			})

			// 排序
			data = data.sort(function(x, y) {
				return y[1] - x[1]
			})
			return data
		}
	}
}
</script>

<style lang="less" scoped>
.chart-wrapper {
	display: flex;
}
.chart-wrapper .chart_table {
	width: 532px;
	height: 350px;
}

.chart-wrapper .icon {
	margin: auto 0;
	font-size: 60px;
	color: #595757;
}

.no-point {
	color: #f2f2f2 !important;
}
</style>
