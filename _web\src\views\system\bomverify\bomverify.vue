<template>
    <a-modal title="sap校验" :width="500" :visible="visible" @cancel="handleCancel" :confirmLoading="confirmLoading">
        <template slot="footer">
            <a-button key="back" @click="handleCancel">
                关闭
            </a-button>
        </template>
        <template v-for="(item,i) in proof" >
            <div :key="i">
                <div>{{item.msg}}</div>
                <div>
                    <div v-if="item.type == 1">IDP 组件料号:{{item.treeBom.sapNumber}}-工厂:{{item.werk}}-版本:{{item.version}}-单位:{{item.treeBom.partUnit}}-sap使用量:{{parseFloat(item.treeBom.sapPartUse).toFixed(3)}}-损耗率:{{parseFloat(item.treeBom.partLoss).toFixed(2)}}-行号:{{item.treeBom.posnr}}</div>
                    <div v-else>IDP 主物料号:{{item.treeBom.sapNumber}}-工厂:{{item.werk}}-版本:{{item.version}}-单位:{{item.treeBom.partUnit}}-sap使用量:{{parseFloat(item.treeBom.sapPartUse).toFixed(3)}}</div>
                </div>
                <div v-for="(_item,_i) in item.sapScanParam" :key="_i">
                    <div v-if="item.type == 1">SAP 组件料号:{{_item.IDNRK}}-工厂:{{_item.WERKS}}-版本:{{_item.STLAL}}-单位:{{_item.MEINS}}-sap使用量:{{_item.MENGE}}-损耗率:{{_item.AUSCH}}-行号:{{_item.POSNR}}</div>
                    <div v-else>SAP 主物料号:{{_item.MATNR}}-工厂:{{_item.WERKS}}-版本:{{_item.STLAL}}-单位:{{_item.FMEINS}}-基本用量:{{_item.BMENG}}</div>
                </div>
            </div>
        </template>
    </a-modal>
</template>

<script>
export default {
    data() {
        return{
            visible: false,
            confirmLoading: false,
            proof:[],
        }
    },
    methods: {
        view(proof){
            let _proof = JSON.parse(proof)
            console.log(_proof)
            this.proof = _proof
            this.visible = true
        },
        handleCancel() {
            this.visible = false
            this.proof = []
        },
    }
}
</script>

<style>

</style>