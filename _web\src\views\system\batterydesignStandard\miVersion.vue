<template>
  <div>
    <div style="margin: 35px 0px 10px 15px;">
      <a-breadcrumb
        :routes="routes"
        separator=">">
        <template
          slot="itemRender"
          slot-scope="{ route, routes }">
          <router-link v-if="routes.indexOf(route) === 1" :to="route.path">
            {{ route.breadcrumbName }}
          </router-link>
          <span v-else-if="routes.indexOf(route) === routes.length - 1">
            {{ route.breadcrumbName }}
          </span>
          <router-link
            v-else
            :to="route.path">
            {{ route.breadcrumbName }}
          </router-link>
        </template>
      </a-breadcrumb>
    </div>
    <div>
      <a-table
        class="navigationClass"
        id="navigationId"
        style="margin: 20px;background: #fff;width:100px;height:500px;float: left"
        :data-source="navigationData"
        :pagination="false">
        <a-table-column
          align="center"
          title="快速导航"
          data-index="navigationItem">
          <template slot-scope="text, record">
            <span @click="clickNavigation(record)">{{ text }}</span>
          </template>
        </a-table-column>
      </a-table>
      <a-table
        id="develop"
        style="width:80%;height:150%;float: left"
        :columns="columns"
        :data-source="resultData"
        :row-key="(record) => record.id"
        :pagination="false"
        :scroll="{ y: windowHeight }"
        :customRow="customRowEvent"
        bordered
      >
        <div
          slot="process"
          slot-scope="text,record,index"
          :id="'circle'+index">
          <input
            :value="record.process != null ? text : '/'" style="width: 80%"
            @change="updateData($event,record,'process')"/>
          <a-icon
            type="plus"
            id="displayPlusId"
            style="float: left;padding-top: 7%;display: none"
            @click="addMIVersion(libraryData,index)"/>
        </div>
        <div
          slot="project"
          slot-scope="text,record">
          <input
            :value="record.project != null ? text : '/'"
            @change="updateData($event,record,'project')"/>
        </div>
        <div
          slot="productParam"
          slot-scope="text,record">
          <input
            :value="record.productParam != null ? text : '/'"
            @change="updateData($event,record,'productParam')"/>
        </div>
        <div
          slot="specification1"
          slot-scope="text,record">
          <input
            :value="record.specification1 != null ? text : '/'"
            @change="updateData($event,record,'specification1')"/>
        </div>
        <div
          slot="specification2"
          slot-scope="text,record">
          <input
            :value="record.specification2 != null ? text : '/'"
            @change="updateData($event,record,'specification2')"/>
        </div>
        <div
          slot="specification3"
          slot-scope="text,record">
          <input
            :value="record.specification3 != null ? text : '/'"
            @change="updateData($event,record,'specification3')"/>
        </div>
        <div
          slot="specification4"
          slot-scope="text,record">
          <input
            :value="record.specification4 != null ? text : '/'"
            @change="updateData($event,record,'specification4')"/>
        </div>
        <!--      <div slot="sampleNum" slot-scope="text,record">-->
        <!--        <input :value="record.sampleNum != null ? text : '/'"-->
        <!--               @change="updateData($event,record,'sampleNum')"/>-->
        <!--      </div>-->
        <!--      <div slot="checkMethod" slot-scope="text,record">-->
        <!--        <input :value="record.checkMethod != null ? text : '/'"-->
        <!--               @change="updateData($event,record,'checkMethod')"/>-->
        <!--      </div>-->
        <div
          slot="remark"
          slot-scope="text,record">
          <a-textarea
            v-model="record.remark"
            style="width:92%"
            :auto-size="{ minRows: 1, maxRows: 3 }"
            @blur="updateData($event,record,'remark')"/>
          <a-popconfirm
            placement="topRight"
            title="确认删除？"
            @confirm="() => deleteMiversion(record.id)">
            <a-icon
              type="minus-circle"
              style="color: red;padding-bottom: 2%"
              :style="{paddingLeft: bigClient?'2.1%':'1.85%'}"/>
          </a-popconfirm>
        </div>
        <template slot="title">
          <table>
            <tr class="renderTr">
              <td
                style="width: 18%;"
                rowspan="4"
                colspan="2">
                <img
                  src="/img/logo.53575418.png"
                  alt="logo"
                  style="width: 80px;height: 80px">
              </td>
              <td
                style="width: 49.5%;font: bold normal 18px arial;"
                rowspan="2"
                colspan="6">Manufacturing Istruction</td>
              <td style="width: 8%;font-weight: bold">文件编号：</td>
              <td>
                <input
                  :value="libraryData.documentNo"
                  @change="updateLibData($event, libraryData, 'documentNo')"/>
              </td>
            </tr>
            <tr class="renderTr">
              <td style="width: 6%;font-weight: bold">版本：</td>
              <td>
                <input
                  :value="libraryData.version"
                  @change="updateLibData($event, libraryData, 'version')"/>
              </td>
            </tr>
            <tr class="renderTr">
              <td
                colspan="6"
                rowspan="2">
                <input
                  :value="libraryData.miVersion"
                  style="width: 100%;font: bold normal 16px arial;"
                  @change="updateLibData($event, libraryData, 'miVersion')"/>
              </td>
              <td style="width: 6%;font-weight: bold"">样品阶段：</td>
              <td>
                <input
                  :value="libraryData.sampleStage"
                  @change="updateLibData($event, libraryData, 'sampleStage')"/>
              </td>
            </tr>
            <tr style="height: 28px" class="renderTr">
              <td style="width: 6%;font-weight: bold"">页码：</td>
              <td>
                <input
                  :value="libraryData.page"
                  @change="updateLibData($event, libraryData, 'page')"/>
              </td>
            </tr>
          </table>
        </template>
        <template
          slot="footer"
          slot-scope="currentPageData">
          <table>
            <tr>
              <td style="padding: 0;border: 0;width: 8.95%;"></td>
              <td style="padding: 0;border: 0;width: 8.95%;"></td>
              <td style="padding: 0;border: 0;width: 18.2%;"></td>
              <td style="padding: 0;border: 0;width: 31.5%;"></td>
              <td style="padding: 0;border: 0;width: 8%;"></td>
              <td style="padding: 0;border: 0;width: 24.5%;"></td>
            </tr>
            <tr style="border: 1px solid black;">
              <td id="iconPlusTd">
                <a-icon
                  type="plus"
                  slot="footer"
                  :style="{paddingLeft: bigClient?'2.1%':'1.85%'}"
                  @click="addMIVersion(libraryData)"/>
              </td>
            </tr>
            <tr>
              <td>编制</td>
              <td>
                <input
                  :value="libraryData.mivEstablishment"
                  @change="updateLibData($event, libraryData, 'mivEstablishment')"/>
              </td>
              <td>审核</td>
              <td>
                <input
                  :value="libraryData.mivAudit"
                  @change="updateLibData($event, libraryData, 'mivAudit')"/>
              </td>
              <td>批准</td>
              <td>
                <input
                  :value="libraryData.mivApproval"
                  @change="updateLibData($event, libraryData, 'mivApproval')"/>
              </td>
            </tr>
          </table>
        </template>
      </a-table>
    </div>
  </div>
</template>
<script>
import {
  getMIStandardLibById,
  updateMIStandardLib
} from '@/api/modular/system/gCylinderMILibManage';
import { getMIVersionList, insertMIVersion, updateMIVersion } from "@/api/modular/system/miVersionManage";
import $ from 'jquery';
  export default {
    components: {
    },
    data() {
      return {
        // 表头
        columns: [
          {
            title: '工序',
            dataIndex: 'process',
            scopedSlots: { customRender: 'process' },
            align: 'center',
            width: 74.8,
          },
          {
            title: '项目',
            dataIndex: 'project',
            scopedSlots: { customRender: 'project' },
            align: 'center',
            width: 74.8,
          },
          {
            title: '产品参数',
            dataIndex: 'productParam',
            width: 150,
            align: 'center',
            scopedSlots: { customRender: 'productParam' },
          },
          {
            title: '规格',
            dataIndex: 'specification1',
            align: 'center',
            width: 61,
            scopedSlots: { customRender: 'specification1' },
            colSpan: 4,
          },
          {
            dataIndex: 'specification2',
            align: 'center',
            colSpan: 0,
            width: 40,
            scopedSlots: { customRender: 'specification2' },
          },
          {
            dataIndex: 'specification3',
            align: 'center',
            colSpan: 0,
            width: 80,
            scopedSlots: { customRender: 'specification3' },
          },
          {
            dataIndex: 'specification4',
            align: 'center',
            colSpan: 0,
            width: 80,
            scopedSlots: { customRender: 'specification4' },
          },
          // {
          //   title: '取样数量',
          //   dataIndex: 'sampleNum',
          //   align: 'center',
          //   width: 60,
          //   scopedSlots: { customRender: 'sampleNum' },
          // },
          // {
          //   title: '检验方法',
          //   dataIndex: 'checkMethod',
          //   align: 'center',
          //   width: 60,
          //   scopedSlots: { customRender: 'checkMethod' },
          // },
          {
            title: '备注',
            dataIndex: 'remark',
            align: 'center',
            width: 270,
            scopedSlots: { customRender: 'remark' },
          },
    ],
        visible: false,
        editVisible: false,
        confirmLoading: false,
        mIStandardLibData: {},
        // sortedArray: [],
        bigClient: document.documentElement.clientHeight > 700,
        windowHeight: document.documentElement.clientHeight - 150,
        form: this.$form.createForm(this,{ name: 'form' }),
        editForm: this.$form.createForm(this,{ name: 'editForm' }),
        resultData: [],
        navigationData: [],
        processList: [],
        libraryData: {},
        routes: [
          {
            path: '/batterydesignStandard',
            breadcrumbName: '标准规范',
          },
          // {
          //   breadcrumbName: 'G圆柱',
          // },
          {
            path: '/g_cylinder_mi_library',
            breadcrumbName: 'G圆柱 MI设计与组合',
          },
          {
            breadcrumbName: 'xxx',
          },
        ],
      };
    },
    created() {
      this.routes[2].breadcrumbName = this.$route.query.miVersionName
      this.getMIStandardLib(this.$route.query.libraryId)
      this.getDataByLibraryId(this.$route.query.libraryId)
    },
    mounted() {
      $(".navigationClass").find('th').css('backgroundColor', 'white').css('font','bold normal 18px arial').css('color','DeepSkyBlue').css('border','0')
      document.getElementsByClassName("ant-layout-content")[0].style.backgroundColor = 'white';
    },
    methods: {
      getMIStandardLib(id) {
        getMIStandardLibById({ id: id }).then((res) => {
          if (res.success) {
            this.libraryData = res.data[0]
          } else {
            this.$message.error(res.message)
          }
        })
      },
      customRowEvent(record,index) {
        return {
          on: {
            mouseenter: (event) => {
              $("#circle" + index).find("#displayPlusId").css('display','block')
            }, // 鼠标移入行
            mouseleave: (event) => {
              $("#circle" + index).find("#displayPlusId").css('display','none')
            }
          },
        };
      },
      clickNavigation(record) {
        let row = this.processList.indexOf(record.navigationItem)
        let height = document.documentElement.clientHeight > 700 ? 40 : 39.67
        document.getElementsByClassName("ant-table-body")[1].scrollTop = height * row
        // console.log('document.getElementsByClassName("ant-table-body"):',document.getElementsByClassName("ant-table-body"))
      },
      deleteMiversion(id) {
        updateMIVersion({ id: id, status: 2 }).then(() => this.getDataByLibraryId(this.libraryData.id))
      },
      addMIVersion(data,index) {
        let params = {}
        if (index !== undefined) {
          // console.log('有数据')
          if (this.resultData.length > 1 && index < this.resultData.length - 1) {
            // console.log('多行数据点行的加号时(不包括最后一行)')
            let current = parseInt(this.resultData[index].serialNumber);
            let lastNumber = parseInt(this.resultData[index + 1].serialNumber);
            let subtracted = (lastNumber - current) / 10;
            params.serialNumber = lastNumber - subtracted
          } else if (this.resultData.length > 1 && index === this.resultData.length - 1) {
            params.serialNumber = parseInt(this.resultData[this.resultData.length - 1].serialNumber) + 10000000000000
            // console.log('多行数据点行的加号时(点最后一行)')
          } else {
            params.serialNumber = parseInt(this.resultData[this.resultData.length - 1].serialNumber) + 10000000000000
            // console.log('只有一行数据点行的加号时')
          }
        } else if (this.resultData.length === 0) {
          params.serialNumber = 0
          // console.log('没数据时')
        } else {
          // console.log('点最下面的加号时')
          params.serialNumber = parseInt(this.resultData[this.resultData.length - 1].serialNumber) + 10000000000000
        }
        params.libraryId = data.id
        // console.log('params:',params)
        // console.log('this.resultData[' + index + ']',this.resultData[index])
        insertMIVersion(params).then(() => this.getDataByLibraryId(this.libraryData.id))
      },
      updateData(event, record, column) {
        //修改时禁止输入
        let inputs = document.getElementsByTagName("input");
        let textareas = document.getElementsByTagName("textarea");
        let controlInput = [];
        let controltextarea = [];
        for (let i = 0; i < inputs.length; i++) {
          if (!inputs[i].disabled) {
            controlInput.push(inputs[i])
          }
        }
        for (let i = 0; i < textareas.length; i++) {
          if (!textareas[i].disabled) {
            controltextarea.push(textareas[i])
          }
        }
        for (let i = 0; i < controlInput.length; i++) {
          controlInput[i].disabled = true
        }
        for (let i = 0; i < controltextarea.length; i++) {
          controltextarea[i].disabled = true
        }
        let param = {}
        param[column] = event.target.value
        param['id'] = record.id
        updateMIVersion(param).then((res) => {
          this.$nextTick(() => {
            if (res.success) {
              this.$message.success('保存成功')
              this.getDataByLibraryId(this.libraryData.id)
            } else {
              this.$message.error(res.message)
            }
            for (let i = 0; i < controlInput.length; i++) {
              controlInput[i].disabled = false
            }
            for (let i = 0; i < controltextarea.length; i++) {
              controltextarea[i].disabled = false
            }
          })
        })
      },
      updateLibData(event, record, column) {
        //修改时禁止输入
        let inputs = document.getElementsByTagName("input");
        let textareas = document.getElementsByTagName("textarea");
        let controlInput = [];
        let controltextarea = [];
        for (let i = 0; i < inputs.length; i++) {
          if (!inputs[i].disabled) {
            controlInput.push(inputs[i])
          }
        }
        for (let i = 0; i < textareas.length; i++) {
          if (!textareas[i].disabled) {
            controltextarea.push(textareas[i])
          }
        }
        for (let i = 0; i < controlInput.length; i++) {
          controlInput[i].disabled = true
        }
        for (let i = 0; i < controltextarea.length; i++) {
          controltextarea[i].disabled = true
        }
        let param = {}
        param[column] = event.target.value
        param['id'] = record.id
        updateMIStandardLib(param).then((res) => {
          this.$nextTick(() => {
            if (res.success) {
              this.$message.success('保存成功')
            } else {
              this.$message.error(res.message)
            }
            for (let i = 0; i < controlInput.length; i++) {
              controlInput[i].disabled = false
            }
            for (let i = 0; i < controltextarea.length; i++) {
              controltextarea[i].disabled = false
            }
          })
        })
      },
      getDataByLibraryId(libraryId) {
        getMIVersionList({ libraryId: libraryId, status: 2 }).then(res => {
          this.$nextTick(() => {
            this.resultData = res.data
            // this.sortedArray = res.data
            // this.sortedArray.sort((a, b) => a.serialNumber - b.serialNumber);
            // for (let i = 0; i < this.sortedArray.length; i++) {
            //   this.sortedArray[i].index = i
            // }
            // console.log('this.sortedArray',this.sortedArray)
            let finallyList = []
            this.processList = []
            this.navigationData = []
            this.resultData.forEach(item => {
              if (item.process) {
                this.processList.push(item.process)
              }
            })
            let orderProcessList = [...new Set(this.processList)]
            orderProcessList.forEach(item => {
              finallyList.push({
                navigationItem: item
              })
            })
            this.navigationData = finallyList
            // console.log('orderProcessList:' + orderProcessList)
            // console.log('res.data:' + res.data)
          })
        })
      },
    }
  }
</script>
<style scoped lang="less">
/deep/.ant-table-tbody > tr > td {
  padding: 5px;
  border: none;
}
textarea.ant-input {
  max-width: 100%;
  height: auto;
  min-height: 32px;
  line-height: 1.5;
  vertical-align: bottom;
  -webkit-transition: all 0.3s, height 0s;
  transition: all 0.3s, height 0s;
  border: none;
}
#iconPlusTd {
  border-top: #e8e8e8 solid 0px;
}

#develop {
  font-size: 12px;
  margin: 30px 0px 30px 55px;
  color: #000;
}
// /deep/.ant-breadcrumb a, .ant-breadcrumb span {
//   color: black;
//   font-weight: bold;
// }

// /deep/.ant-breadcrumb > span:last-child {
//   color: black;
//   font-weight: bold;
// }

// /deep/.ant-breadcrumb-separator {
//   color: black;
//   font-weight: bold;
// }
/deep/.ant-table-bordered.ant-table-empty .ant-table-placeholder {
  border: 1px solid black;
}
#navigationId > div > div > div > div > div > table > tbody > tr > td {
  border: 0;
}

/deep/.ant-form-item {
  margin-bottom: 0px;
}

.renderTr td {
  border: #e8e8e8 solid 1px;
  text-align: center;
}

/deep/.ant-table.ant-table-bordered .ant-table-title {
  padding-right: 0px;
  padding-left: 0px;
  border: 1px solid #e8e8e8;
}

/deep/.ant-table-title {
  position: relative;
  padding: 0px;
  border-radius: 2px 2px 0 0;
}

/deep/.ant-table-footer {
  position: relative;
  padding: 0px;
  border-radius: 2px 2px 0 0;
  background-color: white;
}

/deep/.ant-table-footer tr td {
  border-right: #e8e8e8 solid 1px;
  border-top: #e8e8e8 solid 1px;
  text-align: center;
}

input {
  width: 100%;
  height: 25px;
  margin: 0;
  border: 0;
  outline: none;
  text-align: center;
}
/deep/.ant-table-thead > tr > th {
  background: white;
  padding: 12px 8px;
  font-weight: bold;
  border: 1px solid black;
}
/deep/.ant-table-bordered .ant-table-tbody > tr > td {
  border: 1px solid black;
}
/deep/.ant-table-bordered .ant-table-body > table {
  border-collapse: collapse;
  border-spacing: 0;
}
/deep/.ant-table-bordered .ant-table-header > table {
  border-collapse: collapse;
  border-spacing: 0;
}
/deep/.ant-table-thead > tr > th {
  border-bottom: 0;
  border-top: 0;
}
/deep/.ant-table-bordered .ant-table-title > table {
  border-collapse: collapse;
  border-spacing: 0;
}
/deep/.ant-table-bordered .ant-table-title > table > tr > td {
  border: 1px solid black;
}
/deep/.ant-table-bordered .ant-table-footer > table {
  border-collapse: collapse;
  border-spacing: 0;
  margin-top: -2px;
}
/deep/.ant-table-bordered .ant-table-footer > table > tr > td {
  border: 1px solid black;
}
</style>
