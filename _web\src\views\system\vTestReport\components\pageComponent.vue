<template>
  <div>
    <div>
      <a-tooltip>
        <template slot="title">
          下载图表
        </template>
        <a-button type="link" @click="handleDown"><a-icon type="arrow-down" /></a-button>
      </a-tooltip>
      <a-tooltip>
        <template slot="title">
          编辑图表
        </template>
        <a-button type="link" @click="handleEdit"><a-icon type="form" /></a-button>
      </a-tooltip>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      editObj: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
      }
    },
    created(){
    },
    methods: {
      handleDown() {
        this.$emit('down', this.editObj)
      },
      handleEdit() {
        this.$emit('edit', this.editObj)
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/.ant-btn {
    font-size: 16px;
    padding: 0 6px;
  }
</style>