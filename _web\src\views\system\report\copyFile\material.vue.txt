<template>
  <div>
    <a-breadcrumb class="breadcrumb" separator=">" :style="`width:${_elWidth}px;margin:0 auto;`">
      <a-breadcrumb-item><a @click="gotoIndex(-3)">信息对齐表</a></a-breadcrumb-item>
      <a-breadcrumb-item><a @click="gotoIndex(-2)">产品开发进展</a></a-breadcrumb-item>
      <a-breadcrumb-item><a @click="gotoIndex(-1)">产品主要技术文档</a></a-breadcrumb-item>
      <a-breadcrumb-item>材料管理</a-breadcrumb-item>
    </a-breadcrumb>
    <div :style="`position: fixed;width:${elWidth}px;height:${elHeight}px;left: 0;right: 0;margin:0 auto;`">
      <div :style="`width:${_elWidth}px;zoom:${meter_zoom};margin: auto;`" class="head">{{title}}</div>
      <ve-table :cell-style-option="cellStyleOption" :style="`width:${_elWidth}px;zoom:${meter_zoom};margin: auto;`" border-y :show-header="false" :columns="bomColumns" :table-data="bomTableData" :cell-span-option="cellSpanOption" />
      <ve-table :style="`width:${_elWidth}px;zoom:${meter_zoom};margin: auto;`" style="border:none" border-y :columns="columns" :table-data="tableData"  :cell-span-option="dataCellSpanOption"/>
    </div>
  </div>
  
</template>

<script>
  import {
    getBomMaterial
  } from "@/api/modular/system/bomMaterialManage"
  export default {
    data() {
      return {
        type:{},
        title: '',
        issueId: 0,
        elWidth: 0,
        _elWidth: 0,
        elHeight: 0,
        merges:['apply','material_type'],
        cellSpanOption: {
          bodyCellSpan: this.bodyCellSpan,
        },
        cellStyleOption: {
          bodyCellClass: ({ row, column, rowIndex }) => {
            let $arr = ['bom','bom_type']
            if (rowIndex == 1 && $arr.indexOf(column.key) < 0) {
              return 'backColor'
            }
            
          },
        },
        dataCellSpanOption:{
          bodyCellSpan: this.dataBodyCellSpan,
        },
        bomColumns: [{
            title: 'BOM',
            field: 'bom',
            key: 'bom',
            width: 200
          },
          {
            title: 'BOM功能',
            field: 'bom_type',
            key: 'bom_type',
            width: 200
          },
          {
            title: 'B01',
            field: 'b01',
            key: 'b01',
            width: 300,
            renderBodyCell: this.renderBodyCell,
          }
        ],
        bomTableData: [{
            bom: 'BOM',
            bom_type: '适用产品状态',
          },
          {
            bom: 'BOM',
            bom_type: '适用工厂',
          },
          {
            bom: 'BOM',
            bom_type: '工厂产能',
          },
        ],
        columns: [{
            title: '序号',
            field: 'seq',
            key: 'seq',
            width: 40,
            renderBodyCell: ({
              row,
              column,
              rowIndex
            }, h) => {
              return ++rowIndex;
            },
          },
          {
            title: '产品应用',
            field: 'apply',
            key: 'apply',
            width: 160,
            renderBodyCell: this.renderCell,
          },
          {
            title: '材料类别',
            field: 'material_type',
            key: 'material_type',
            width: 100,
            renderBodyCell: this.renderSelectCell
          },
          {
            title: '名称',
            field: 'material_name',
            key: 'material_name',
            width: 100,
          },
          {
            title: 'B01',
            children: [{
                title: '物料代码',
                field: 'material_code_01',
                key: 'material_code_01',
                width: 100,
              },
              {
                title: '型号',
                field: 'material_model_01',
                key: 'material_model_01',
                width: 100,
              },
              {
                title: '供应商',
                field: 'supplyer_01',
                key: 'supplyer_01',
                width: 100,
              }
            ]
          }
        ],
        tableData: []
      }
    },
    methods: {
      gotoIndex(index){
      this.$router.go(index)
    },
      renderCell({ row, column, rowIndex }){
        return this.type[row[column.key]]
      },
      renderBodyCell({ row, column, rowIndex }) {
        if (rowIndex == 0) {
          if (row[column.key] == 0) {
            return <span>非量产</span>
          } else if(row[column.key] == 1) {
            return <span>量产</span>
          }else{
            return <span></span>
          }
        }
        return row[column.key]
      },
      renderSelectCell({ row, column, rowIndex }){
        let _type = { 1:'化学材料', 2:'结构件', 3:'包装材料' }
        return _type[row[column.key]]
      },
      initBodySize() {
        this.initWidth = document.documentElement.clientWidth; // 拿到父元素宽
        this.initHeight = (document.documentElement.clientHeight) * document.documentElement.clientWidth / this.initWidth; // 根据宽计算高实现自适应
        this.elWidth = this.initWidth;
        this._elWidth = this.elWidth * 0.94
        this.elHeight = this.initHeight;
        this.meter_zoom = 1;
    },
      bodyCellSpan({
        row,
        column,
        rowIndex
      }) {
        if (column.field === "bom") {
          if (rowIndex === 0) {
            return {
              rowspan: 3,
              colspan: 1,
            };
          } else {
            return {
              rowspan: 0,
              colspan: 0,
            };
          }
        }
      },

      dataBodyCellSpan({ row, column, rowIndex }) {
      if (this.merges.includes(column.key)) {
        const _col = row[column.key + "_rowSpan"] > 0 ? 1 : 0;
        return {
          colspan: _col,
          rowspan: row[column.key + "_rowSpan"],
        };
      }
    },
      getBomMaterial() {
               this.loading = true
               getBomMaterial({
                   issueId: this.$route.query.issueId
                }).then((res) => {
                   if (res.success) {
                        let type = {}
                        for (const item of res.data.productCateOptionBeans) {
                            type[item.id] = item.value
                        }
                        this.type = type
                        let columnObj = res.data.materialColumns ? JSON.parse(res.data.materialColumns) : null
                        if (columnObj) {
                            for (const item of columnObj.bomColumns) {
                                item.renderBodyCell = this.renderBodyCell
                            }
                            this.bomColumns.push(...columnObj.bomColumns)
                        }
                        if (columnObj) {
                            this.columns.push(...columnObj.columns)
                        }
                        let rowsObj = res.data.materialRows ? JSON.parse(res.data.materialRows): null
                        this.bomTableData = rowsObj ?  rowsObj.bomTableData : this.bomTableData

                        let handle = (property) => {
                            return function(a,b){
                                let val1 = a[property];
                                let val2 = b[property];
                                return val1 - val2;
                            }
                        }
                        let tableData = rowsObj ? rowsObj.tableData: []
                        if (tableData) {
                          tableData.sort(handle('material_type'))
                          this.merges.forEach((item) => {
                            for (let i = 0, j = tableData.length; i < j; i++) {
                              let rowSpan = 0;
                              let n = i;
                              while (
                                tableData[n + 1] &&
                                tableData[n + 1][item] == tableData[n][item]
                              ) {
                                rowSpan++;
                                n++;
                                tableData[n].rowSpan = 0;
                              }
                              if (rowSpan) tableData[i][item + "_rowSpan"] = rowSpan + 1;

                              if (!rowSpan) tableData[i][item + "_rowSpan"] = 1;

                              i += rowSpan;
                            }
                          });
                          this.tableData = tableData
                        }
                        this.vo = res.data
                   } else {
                       this.$message.error(res.message)
                   }
                   this.loading = false
                }).catch((err) => {
                   this.$message.error('错误：' + err.message)
                   this.loading = false
                })
           },
    },
    created() {
      this.issueId = this.$route.query.issueId
      this.title = this.$route.query.title + '产品材料管理'
      this.getBomMaterial()
      this.initBodySize()
    },
    destroyed() {
    },
  }
</script>

<style lang="less" >
  @import './vetable.less';
  .head {
    border: 1px solid #97b1e7;
    border-bottom: none;
    background: #eaf0fa;
    text-align: center;
    padding: 10px 0;
    color: #000;
    font-size: 20px;
}
 .backColor{
    background: #fad7a7 !important;
 }
 .breadcrumb{
  padding: 5px 0;
  padding-left: 13px;
}.ant-breadcrumb a{
  color:#5d90fa !important;
}.ant-breadcrumb{
  font-size: 12px !important;
}
</style>