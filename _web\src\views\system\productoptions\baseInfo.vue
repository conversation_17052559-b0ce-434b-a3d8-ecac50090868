<template>
	<div class="product_width">
		<a-spin :spinning="loading">
			<!-- 筛选区域 start -->
			<div class="table-page-search-wrapper">
				<a-form layout="inline">
					<a-row :gutter="48">
						<a-col :md="6" :sm="24">
							<a-form-item label="立项日期">
								<a-range-picker
									class="filter-form"
									:placeholder="['开始日期', '结束日期']"
									size="small"
									@change="dateChange"
								/>
							</a-form-item>
						</a-col>
						<!-- <a-col :md="4" :sm="24">
						<a-form-item label="产品分类">
							<treeselect
								class="filter-form"
								:limit="1"
								@input="change"
								:max-height="200"
								placeholder="请选择产品分类"
								value-consists-of="BRANCH_PRIORITY"
								v-model="queryparam.productCates"
								:options="typeOptions"
								:multiple="true"
							/>
						</a-form-item>
					</a-col> -->
						<a-col :md="5" :sm="24">
							<a-form-item label="产品类别">
								<div class="filter-box">
									<treeselect
										class="filter-form"
										:limit="1"
										@input="change"
										:max-height="200"
										placeholder="请选择产品类别"
										value-consists-of="BRANCH_PRIORITY"
										v-model="queryparam.cates"
										:multiple="true"
										:options="cate"
									/>
								</div>
							</a-form-item>
						</a-col>
						<a-col :md="5" :sm="24">
							<a-form-item label="产品状态">
								<treeselect
									class="filter-form"
									:limit="1"
									@input="change"
									:max-height="200"
									placeholder="请选择产品状态"
									value-consists-of="BRANCH_PRIORITY"
									v-model="queryparam.states"
									:multiple="true"
									:options="statuses"
								/>
							</a-form-item>
						</a-col>

						<a-col :md="5" :sm="24">
							<a-form-item label="产品部门">
								<treeselect
									class="filter-form"
									:limit="1"
									@input="change"
									:max-height="200"
									placeholder="请选择所属部门"
									:multiple="true"
									:options="departmentCateTreeData"
									value-consists-of="BRANCH_PRIORITY"
									v-model="queryparam.depts"
								>
								</treeselect>
							</a-form-item>
						</a-col>

						<a-col :md="3" :sm="24">
							<a-form-item label="">
								<a-input
									class="filter-form"
									size="small"
									@keyup.enter.native="change"
									v-model="queryparam.keyword"
									placeholder="请输入产品名称"
								>
									<a-icon slot="suffix" type="search" style="color: rgba(0,0,0,.45)" />
								</a-input>
							</a-form-item>
						</a-col>

						<a-col :md="1" :sm="24" :style="{ float: 'right' }">
							<div class="table-page-search-submitButtons" :style="{ float: 'right' }">
								<a-button size="small" style="margin-left: 120px;" type="primary" @click="query">查询</a-button>
								<!-- <a-button size="small" style="margin-left: 20px;margin-top:6px" @click="resetquery">重置</a-button> -->
							</div>
						</a-col>
					</a-row>
				</a-form>
			</div>
			<!-- 筛选区域 end -->

			<!-- 表格 start -->
			<div>
				<a-table
					ref="table"
					:style="`height:${tableHeight}px;`"
					:rowKey="record => record.issueId + record.productCate"
					:columns="columns"
					:dataSource="loadData"
					:expandIconColumnIndex="11"
					:expandIconAsCell="false"
					:expandIcon="expandIcon"
				>
					<span slot="fixedState" slot-scope="text, record">
						<span v-if="record.fixedState == 2">
							<a-icon :style="{ color: '#91cc75' }" type="check-circle" />
						</span>
						<span v-else></span>
					</span>

					<span slot="productCate" slot-scope="text, record">
						{{ record.productOrProject == 1 ? record.productCateParent + (text != "" ? "->" + text : "") : "" }}
					</span>
					<span slot="productProjectName" slot-scope="text, record">
						{{ record.productOrProject == 1 ? text : "" }}
					</span>
					<!-- <span slot="dept" slot-scope="text, record">{{
					record.departmentOptionList
						.filter(function(e) {
							return 0 == parseInt(e.pid)
						})
						.map(function(e) {
							return e.value
						})[0]
				}}</span> -->

					<!-- 部门 -->
					<span slot="deptchild" slot-scope="text, record">
						{{
							record.departmentOptionList
								.filter(function(e) {
									return 0 == parseInt(e.pid)
								})
								.map(function(e) {
									return e.value
								})[0]
						}}-{{
							record.departmentOptionList
								.filter(function(e) {
									return 0 != parseInt(e.pid)
								})
								.map(function(e) {
									return e.value
								})[0]
						}}</span
					>
					<span slot="mstatus" slot-scope="text">{{ "product_stage_status" | dictType(text) }}</span>
				</a-table>
			</div>
			<!-- 表格 end -->
		</a-spin>
	</div>
</template>

<script>
import { dashboardInfo } from "@/api/modular/system/dashboardManage"
import { getCatesTree } from "@/api/modular/system/report"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"

import { classType, statusType, typeOptions } from "@/utils/enum.js"
import { getCateTree } from "@/api/modular/system/topic"

export default {
	components: {
		Treeselect
	},
	data() {
		return {
			departmentCateTreeData: [],
			typeOptions: [
				{
					id: 1,
					label: "预研产品"
				},
				{
					id: 2,
					label: "A|B新产品"
				},
				{
					id: 3,
					label: "试产新产品"
				},
				{
					id: 4,
					label: "量产品"
				},
				{
					id: 5,
					label: "其他"
				},
				{
					id: 6,
					label: "停止"
				}
			],
			statuses: [
				{
					id: 0,
					label: "立项讨论"
				},
				{
					id: 1,
					label: "A/B样"
				},
				{
					id: 2,
					label: "C/D样"
				},
				{
					id: 3,
					label: "暂停开发"
				},
				{
					id: 4,
					label: "停产"
				},
				{
					id: 5,
					label: "SOP"
				}
			],
			queryparam: {
				productCates: [],
				cates: [],
				states: [],
				depts:[],
				keyword: null,
				projectId: null
			},
			parentId: null,
			cateId: null,
			projectId: null,
			loading: true,
			loading1: true,
			columns: [
				{
					title: "序号",
					dataIndex: "no",
					width: 45,
					align: "center",
					customRender: (text, record, index) => {
						if (record.productOrProject == 1) {
							return `${index + 1}`
						}
						return ""
					}
				},
				{
					title: "产品名称",
					align: "center",
					width: 100,
					dataIndex: "productProjectName",
					scopedSlots: {
						customRender: "productProjectName"
					}
				},
				{
					title: "项目名称",
					align: "center",
					width: 100,
					dataIndex: "projectName"
				},

				{
					title: "等级",
					align: "center",
					width: 60,
					dataIndex: "productLevel",
					customRender: (text, record, index) => {
						return text + "级"
					}
				},
				{
					title: "客户",
					width: 60,
					align: "center",
					dataIndex: "customer"
				},
				{
					title: "定点状态",
					align: "center",
					width: 90,
					dataIndex: "fixedState",
					scopedSlots: {
						customRender: "fixedState"
					}
				},
				{
					title: "产品状态",
					align: "center",
					width: 90,
					dataIndex: "state",
					customRender: text => statusType[text]
				},

				{
					title: "PD",
					width: 60,
					align: "center",
					ellipsis: true,
					dataIndex: "productManager"
				},
				{
					title: "RPM",
					width: 60,
					align: "center",
					ellipsis: true,
					dataIndex: "researchProjectManager"
				},
				{
					title: "立项日期",
					align: "center",
					width: 100,
					dataIndex: "initiationDate"
				},
				{
					title: "部门",
					align: "center",
					width: 230,
					ellipsis: true,
					dataIndex: "deptchild",
					scopedSlots: {
						customRender: "deptchild"
					}
				},
				// {
				// 	title: "项目阶段",
				// 	align: "center",
				// 	width: 100,
				// 	dataIndex: "mstatus",
				// 	scopedSlots: {
				// 		customRender: "mstatus"
				// 	}
				// },

				// {
				// 	title: "产品分类",
				// 	align: "center",
				// 	width: 90,
				// 	dataIndex: "productClassification",
				// 	customRender: text => classType[text]
				// },

				// {
				// 	title: "二级部门",
				// 	align: "center",
				// 	width: 120,
				// 	ellipsis: true,
				// 	dataIndex: "dept",
				// 	scopedSlots: {
				// 		customRender: "dept"
				// 	}
				// },

				{
					title: "操作",
					align: "center",
					width: 45,
					dataIndex: "action"
				}
				// {
				// 	title: "产品类别",
				// 	align: "center",
				// 	dataIndex: "productCate",
				// 	scopedSlots: {
				// 		customRender: "productCate"
				// 	}
				// },
			],
			loadData: [],
			totalData: [],
			cate: [],
			tablesScroll: { x: "100%", y: 500 },
			tableScroll: 100
		}
	},
	props: {
		// 表格高度
		tableHeight: {
			type: Number,
			default: 0
		},
		// 表格滚动高度
		scrollHeigh: {
			type: Number,
			default: 0
		}
	},
	watch: {
		loadData(newVal, oldVal) {
			if (this.loadData.length > 0) {
				// 动态修改--height的值
				document.documentElement.style.setProperty(`--height`, `${this.scrollHeigh}px`)
			} else {
				// 动态修改--height的值
				document.documentElement.style.setProperty(`--height`, "50px")
			}
		}
	},
	created() {
		this.callDashboardInfo()
		this.callGetTree()
		this.callGetDepartmentCateTree()
		// 动态修改--height的值
		document.documentElement.style.setProperty(`--height`, `${this.scrollHeigh}px`)
	},
	methods: {
		callGetDepartmentCateTree() {
			this.confirmLoading = true
			getCateTree({
				fieldName: "department"
			})
				.then(res => {
					if (res.success) {
						let cate = []
						for (const item of res.data) {
							let $item = {
								id: parseInt(item.value),
								label: item.title
							}
							
							cate.push($item)
						}
						this.departmentCateTreeData = cate
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
					this.confirmLoading = false
				})
				.catch(err => {
					this.confirmLoading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		},
		query() {
			if (this.parentId != null) {
				let index = this.queryparam["cates"].findIndex(e => e == this.parentId)
				this.queryparam["cates"].splice(index, 1)
				this.parentId = null
			}
			if (this.cateId != null) {
				let index = this.queryparam["cates"].findIndex(e => e == this.cateId)
				this.queryparam["cates"].splice(index, 1)
				this.cateId = null
			}

			if (this.projectId != null) {
				this.queryparam.projectId = null
				this.projectId = null
			}

			this.callFilter()
		},
		// 筛选框改变时间
		change() {
			this.callFilter()
		},
		resetquery() {
			this.queryparam = {
				productCates: [],
				cates: [],
				states: [],
				keyword: null,
				projectId: null
			}
			let filterData = JSON.parse(JSON.stringify(this.totalData))
			this.loadData = filterData
		},
		callGetTree() {
			this.loading1 = true
			getCatesTree()
				.then(res => {
					if (res.result) {
						let cate = []
						for (const item of res.data) {
							let $item = {
								id: parseInt(item.value),
								label: item.title
							}
							if (item.children) {
								$item.children = []
								for (const _item of item.children) {
									$item.children.push({
										id: parseInt(_item.value),
										label: _item.title
									})
								}
							}
							cate.push($item)
						}
						this.cate = cate
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
					this.loading1 = false
				})
				.catch(err => {
					this.loading1 = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		},

		// 数据筛选
		callFilter() {
			let filterData = JSON.parse(JSON.stringify(this.totalData))

			// 产品分类
			if (this.queryparam["productCates"].length > 0) {
				filterData = filterData.filter(
					item => this.queryparam["productCates"].join(",").indexOf(parseInt(item.productClassification)) > -1
				)
			}

			if (this.queryparam.projectId) {
				if (this.queryparam["cates"].length > 0) {
					filterData = filterData.filter(
						item =>
							this.queryparam["cates"].indexOf(parseInt(item.cateId)) > -1 ||
							this.queryparam["cates"].indexOf(parseInt(item.catepid)) > -1
					)
				}
				filterData = filterData.filter(item => item.issueId == this.queryparam.projectId)
				this.loadData = filterData
				return
			}

			if (this.queryparam["cates"].length > 0) {
				filterData = filterData.filter(
					item =>
						this.queryparam["cates"].indexOf(parseInt(item.cateId)) > -1 ||
						this.queryparam["cates"].indexOf(parseInt(item.catepid)) > -1
				)
			}

			/* if (this.queryparam['customers'].length > 0) {
                filterData = filterData.filter(item => this.queryparam['customers'].indexOf(item.customer) > -1)
            }
            if (this.queryparam['levels'].length > 0) {
                filterData = filterData.filter(item => this.queryparam['levels'].indexOf(item.productLevel) > -1)
            } */
			if (this.queryparam["states"].length > 0) {
				filterData = filterData.filter(item => this.queryparam["states"].indexOf(parseInt(item.state)) > -1)
			}

			if (this.queryparam["depts"].length > 0) {
				filterData = filterData.filter(item => this.queryparam["depts"].indexOf(parseInt(item.deptId)) > -1)
			}

			if (this.queryparam.keyword != null && this.queryparam.keyword != "") {
				filterData = filterData.filter(
					item => item.productProjectName.toLowerCase().indexOf(this.queryparam.keyword.toLowerCase()) > -1
				)
			}

			if (this.queryparam.startDate != null) {
				filterData = filterData.filter(
					item =>
						Date.parse(item.initiationDate) >= this.queryparam.startDate &&
						Date.parse(item.initiationDate) < this.queryparam.endDate
				)
			}

			this.loadData = filterData
		},
		dateChange(date, dateString) {
			if (dateString[0] != null && dateString[0] != "") {
				this.queryparam.startDate = Date.parse(dateString[0])
			} else {
				this.queryparam.startDate = null
			}
			if (dateString[1] != null && dateString[1] != "") {
				this.queryparam.endDate = Date.parse(dateString[1])
			} else {
				this.queryparam.endDate = null
			}
			this.callFilter()
		},
		expandIcon(props) {
			if (props.record.children && props.record.children.length > 0) {
				if (props.expanded) {
					return (
						<a-icon
							type="up"
							onClick={e => {
								props.onExpand(props.record, e)
							}}
						/>
					)
				} else {
					return (
						<a-icon
							type="down"
							onClick={e => {
								props.onExpand(props.record, e)
							}}
						/>
					)
				}
			} else {
				return <span style="padding-left: 21px;" />
			}
		},
		handleOk() {
			this.callDashboardInfo()
		},
		// 获取数据
		callDashboardInfo() {
			this.loading = true
			dashboardInfo({})
				.then(res => {
					if (res.success) {
						this.totalData = JSON.parse(JSON.stringify(res.data))
						// this.loadData = res.data
						if (this.$route.query.parentId) {
							this.projectId = parseInt(this.$route.query.parentId)
							this.queryparam["cates"].push(parseInt(this.$route.query.parentId))
						}
						if (this.$route.query.cateId) {
							this.cateId = parseInt(this.$route.query.cateId)
							this.queryparam["cates"].push(parseInt(this.$route.query.cateId))
						}
						if (this.$route.query.projectId) {
							this.projectId = parseInt(this.$route.query.projectId)
							this.queryparam.projectId = parseInt(this.$route.query.projectId)
						}
						this.callFilter()
					} else {
						this.$message.error(res.message, 1)
					}
					this.loading = false
				})
				.catch(err => {
					this.loading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		}
	}
}
</script>

<style lang="less" scoped>
@import "./productoption.less";
:root {
	--height: 200px;
}

/deep/.ant-table-body {
	height: var(--height) !important;
	overflow-y: scroll;
}
</style>
