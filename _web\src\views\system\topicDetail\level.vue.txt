<template>
  <div>
        <div>
            <a-breadcrumb class="breadcrumb" separator=">">
                <a-breadcrumb-item><a @click="goBack">课题看板</a></a-breadcrumb-item>
                <a-breadcrumb-item>课题等级</a-breadcrumb-item>
            </a-breadcrumb>
        </div>
        <x-card class="topic_wid">
            <div slot="content" class="table-page-search-wrapper">
                <a-form layout="inline">
                <a-row :gutter="48">
                    <a-col :md="6" :sm="24">
                        <a-form-item label="时间">
                           <a-range-picker
                                size="small"
                                :placeholder="['开始月份', '结束月份']"
                                v-model="dates"
                                :mode="['month', 'month']"
                                format="YYYY-MM"
                                @panelChange="handlePanelChange"
                                @openChange="handleOpenChange"
                                :open="monthPickShow"
                            >
                                <a-icon slot="suffixIcon" type="calendar" style="color:#d9d9d9" />
                            </a-range-picker>
                        </a-form-item>
                    </a-col>
                    <!-- <a-col :md="8" :sm="24">
                    <a-form-item label="课题分类">
                        <a-tree-select @change="this.change"  v-model="queryParam.cateId" :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" :tree-data="cate" placeholder="请选择分类" tree-default-expand-all>
                        </a-tree-select>
                    </a-form-item>
                    </a-col> -->
                    <a-col :md="6" :sm="24">
                        <a-form-item label="部门">
                            <!-- <a-tree-select multiple size="small"
                        :show-checked-strategy="SHOW_PARENT" @change="this.change"  v-model="queryParam.deptId" :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" :tree-data="depts" placeholder="请选择部门" tree-default-expand-all>
                            </a-tree-select> -->
                            <treeselect :limit="1" @input="change" :max-height="200" placeholder="选择部门" :value-consists-of="ALL" v-model="queryParam.deptId" :multiple="true" :options="depts" :normalizer="normalizer" />
                        </a-form-item>
                    </a-col>
                    <!-- <template v-if="advanced"> -->
                        
                        <a-col :md="6" :sm="24">
                            <a-form-item label="课题等级">
                                <a-select size="small" @change="this.change" v-model="queryParam.level" placeholder="请选择课题状态" >
                                    <a-select-option value="S" >S</a-select-option>
                                    <a-select-option value="A" >A</a-select-option>
                                    <a-select-option value="B" >B</a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <!-- <a-col :md="8" :sm="24">
                            <a-form-item label="关键词">
                                <a-input @keyup.enter.native="change" v-model="queryParam.searchValue" allow-clear placeholder="请输入课题名称"/>
                            </a-form-item>
                        </a-col> -->
                    <!-- </template> -->
                    <a-col :md="!advanced && 6 || 24" :sm="24">
                    <span class="table-page-search-submitButtons" :style="advanced && { float: 'right', overflow: 'hidden' } || {} ">
                       <!--  <a-button type="primary" @click="callGetAllProjects">查询</a-button> -->
                        <a-button size="small" style="margin-left: 8px" @click="resetquery">重置</a-button>
                        <!-- <a @click="toggleAdvanced" style="margin-left: 8px">
                            {{ advanced ? '收起' : '展开' }}
                            <a-icon :type="advanced ? 'up' : 'down'"/>
                        </a> -->
                    </span>
                    </a-col>
                </a-row>
                </a-form>
            </div>
        </x-card>
        <!-- :scroll="{y:windowHeight-320}" -->
        <a-table class="topic_wid" style="background: #fff" :columns="columns" :dataSource="loadData" :loading="loading" :pagination="pagination">
            <span slot="num" slot-scope="text,records,index">
                {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
            </span>
            <template slot="projectTarget" slot-scope="text">
                <clamp :expend="true" :text="text" :sourceText="[text]" :isCenter="true"></clamp>
            </template>
            <template slot="cate" slot-scope="text,record">
                <span v-for="(item,i) in record.projectCateList" :key="i">
                    <label>{{item.value}}</label>
                    <label v-if="item.pid == 1">-</label>
                </span>
            </template>
            <template slot="dept" slot-scope="text,record">
                <span v-for="(item,i) in record.departmentCateList" :key="i">
                    <label>{{item.value}}</label>
                    <label v-if="item.pid == 1">-</label>
                </span>
            </template>
        </a-table>
    </div>
</template>

<script>
import {XCard,clamp} from '@/components'
import {getAllProjects,getCateTree } from "@/api/modular/system/topic"
import moment from 'moment';
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
export default {
    components: {
        XCard,
        clamp,
        Treeselect
    },
    data() {
        return {
            normalizer(node) {
				return {
					id: node.value,
					label: node.title,
					children: node.children,
				}
			},
            dates: [],
            depts:[],
            cate:[],
            monthPickShow: false,
            advanced: false,
            pagination: {
                current: 1,
                pageSize: 15,
                total: 0,
                showSizeChanger: true,
                showQuickJumper: true,
                onChange: (current, size) => {
                    this.pagination.current = current
                    this.pagination.pageSize = size
                },
                onShowSizeChange: (current, pageSize) => {
                    this.pagination.current = 1
                    this.pagination.pageSize = pageSize
                },
            },
            windowHeight: document.documentElement.clientHeight,
            loading: true,
            columns: [
                {
                    title: '序号',
                    width: 40,
                    dataIndex: 'no',
                    align:'center',
                    scopedSlots: {
                        customRender: 'num'
                    }
                },
                {
                    title: '课题分类',
                    width: 120,
                    dataIndex: 'cate',
                    align:'center',
                    scopedSlots: { customRender: 'cate' },
                },
                {
                    title: '课题名称',
                    width: 120,
                    align:'center',
                    dataIndex: 'projectName',
                },
                {
                    title: '研究内容',
                    width: 200,
                    align:'center',
                    dataIndex: 'projectTarget',
                    scopedSlots: { customRender: 'projectTarget' },
                },
                {
                    title: '课题等级',
                    width: 40,
                    align:'center',
                    dataIndex: 'projectLevel',
                },
                {
                    title: '课题负责人',
                    width: 80,
                    align:'center',
                    dataIndex: 'projectLeader',
                },
                {
                    title: '部门',
                    width: 120,
                    align:'center',
                    dataIndex: 'dept',
                    scopedSlots: { customRender: 'dept' },
                },
                {
                    title: '立项评审日期',
                    width: 80,
                    align:'center',
                    dataIndex: 'planInitiationDate',
                },
            ],
            loadData: [],
            queryParam: {},
            loading:false
        }
    },
    methods:{
        goBack(){
            this.$router.push({
                path: "/topic_chart",
            })
        },
        moment,
        resetquery(){
            this.queryParam = {}
            this.callGetAllProjects()
        },
        change(value, label, extra){
            this.callGetAllProjects()
        },
        handlePanelChange(value, mode) {

            if (this.dates[1] && this.dates[1]._d != value[1]._d) {
                this.dates = value
                this.monthPickShow = false;
                this.queryParam.startDate = moment(this.dates[0]._d).format('YYYY-MM')
                this.queryParam.endDate = moment(this.dates[1]._d).format('YYYY-MM')
                this.callGetAllProjects()
            }
            this.dates = value
        },
        handleOpenChange(status) {
            if(status){
                this.monthPickShow = true;
            }else{
                this.monthPickShow = false
            }
        },
        toggleAdvanced () {
            this.advanced = !this.advanced
        },
        callGetAllProjects() {
            this.loading = true
            getAllProjects(this.queryParam).then((res) => {
            if (res.success) {
                this.loadData = res.data.list
                
            } else {
                this.$message.error('错误提示：' + res.message,1)
            }
            this.loading = false
            })
            .catch((err) => {
                this.$message.error('错误提示：' + err.message,1)
                this.loading = false
            });
        },

        callGetDeptTree(){
            getCateTree({
                fieldName:'department',
                flag:1
            }).then((res)=>{
                if (res.success) {
                    this.depts = res.data
                } else {
                    this.$message.error('错误提示：' + res.message, 1)
                }
            }).catch((err) => {
                this.$message.error('错误提示：' + err.message, 1)
            });
        },

        callGetTree(){
            getCateTree({
                fieldName:'projectCate',
                flag:1
            }).then((res)=>{
                if (res.success) {
                    this.cate = res.data
                } else {
                    this.$message.error('错误提示：' + res.message, 1)
                }
            }).catch((err) => {
                this.$message.error('错误提示：' + err.message, 1)
            });
        },
    },
    created(){
        this.callGetTree()
        this.callGetDeptTree()
        this.queryParam.level = this.$route.query.level
        this.callGetAllProjects()
    }
}
</script>

<style lang="less" scoped=''>
@import './topic.less';
.breadcrumb{
    padding: 5px 0;
    padding-left: 13px;
    }.ant-breadcrumb a{
    color:#5d90fa !important;
    }.ant-breadcrumb{
    font-size: 12px !important;
    }
</style>