/**
 * 工具函数索引文件
 * 导出所有工具函数
 */

// 导出数学相关工具函数
export * from './mathUtils';

// 导出图表相关工具函数
export * from './chartUtils';

// 导出公式相关工具函数
export * from './formulaUtils';

/**
 * 深拷贝对象
 * @param {Object} obj - 待拷贝的对象
 * @returns {Object} - 拷贝后的新对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj;
  return JSON.parse(JSON.stringify(obj));
}

/**
 * 生成随机颜色
 * @returns {String} - 随机颜色的十六进制表示
 */
export function randomColor() {
  return '#' + Math.floor(Math.random() * 16777215).toString(16);
}

/**
 * 从文件名中提取后缀
 * @param {String} filename - 文件名
 * @returns {String} - 文件后缀，不含点号
 */
export function getFileExtension(filename) {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
}