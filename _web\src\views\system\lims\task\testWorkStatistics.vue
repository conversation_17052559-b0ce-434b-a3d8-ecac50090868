<template>
  <div style="padding: 10px;">
    <pbiTabs :tabsList="laboratoryList" :activeKey="laboratoryId" @clickTab="callback"></pbiTabs>
    <tableIndex
        :pageLevel='1'
        :tableTotal= 'tableTotal'
        :pageTitleShow=false
        :otherHeight="parseInt(105)"
        :loading='tableLoading'
        @paginationChange="handlePageChange"
        @paginationSizeChange="handlePageChange"
        @tableFocus="tableFocus"
        @tableBlur="tableBlur"
    >
      <template #search>
        <pbiSearchContainer>
          <pbiSearchItem label='科室' :span="4">
            <a-input v-model="queryParam.auditorDepartment" @keyup.enter="getTestWorkStatistics" @change="getTestWorkStatistics"></a-input>
          </pbiSearchItem>
          <pbiSearchItem label='测试员' :span="4">
            <a-input v-model="queryParam.userName" @keyup.enter="getTestWorkStatistics" @change="getTestWorkStatistics"></a-input>
          </pbiSearchItem>
          <pbiSearchItem label='测试项目' :span="4">
            <a-input v-model="queryParam.testProjectName" @keyup.enter="getTestWorkStatistics" @change="getTestWorkStatistics"></a-input>
          </pbiSearchItem>
          <pbiSearchItem label='测试状态' :span="4">
            <a-select v-model="queryParam.testStatusList" style="width: 100%" :allowClear="true" mode="multiple" :maxTagCount="parseInt(2)" @change="getTestWorkStatistics">
              <a-select-option v-for="(item) in testStatusOptions" :value="item">{{item}}</a-select-option>
            </a-select>
          </pbiSearchItem>
          <pbiSearchItem label='时间维度' :span="4" v-if="isShowAllSearch">
            <a-select v-model="queryParam.timeDimension" style="width: 100%" @change="getTestWorkStatistics">
              <a-select-option value="按周">按周</a-select-option>
              <a-select-option value="按月">按月</a-select-option>
            </a-select>
          </pbiSearchItem>
          <pbiSearchItem label='完成日期' :span="6" v-if="isShowAllSearch">
            <a-range-picker :value="timeRange" @change="timeRangeChange"></a-range-picker>
          </pbiSearchItem>
          <pbiSearchItem :span="isShowAllSearch ? 18 : 8" type='btn'>
            <div class="secondary-btn">
              <a-button style="margin-right: 8px;" @click="getTestWorkStatistics" type="primary">查询</a-button>
              <a-button style="margin-right: 8px;" @click="resetParam">重置</a-button>
              <a-button type="primary" @click="exportTestWorkStatistics">导出</a-button>
            </div>
            <div class='toggle-btn'>
              <a-button size='small' type='link' @click='isShowAllSearch = !isShowAllSearch'>
                {{ isShowAllSearch ? '收起' : '展开' }}
                <span v-if='isShowAllSearch'><a-icon type='double-left'/></span>
                <span v-else><a-icon type='double-right'/></span>
              </a-button>
            </div>
          </pbiSearchItem>
        </pbiSearchContainer>
      </template>
      <template #table>
        <ag-grid-vue class='ag-theme-balham'
                     :style="{height:`${tableHeight}px`}"
                     :tooltipShowDelay="0"
                     :columnDefs='columns'
                     :rowData='testWorkStatistics'
                     :defaultColDef='defaultColDef'>
        </ag-grid-vue>
      </template>
    </tableIndex>
  </div>
</template>

<script>
import { downloadfile1 } from "@/utils/util";
import { exportTestWorkStatistics, getTestWorkStatistics } from "@/api/modular/system/testProgressManager";
import pbiTabs from "@/components/pageTool/components/pbiTabs.vue";

export default {
  name: "testWorkStatistics",
  components: {
    pbiTabs
  },
  data() {
    return {
      laboratoryList: [
        {value:'HZ_YJ_DL_JM', label:'精密实验室', show: true}
      ],
      laboratoryId: "HZ_YJ_DL_JM",
      isShowAllSearch: false,
      queryParam: {laboratoryId: "HZ_YJ_DL_JM", timeDimension: "按周"},
      timeRange: [],
      testStatusOptions: ["OA流程中", "样品接收", "任务分配", "结果录入", "结果复核", "复核退回", "已完成"],
      testWorkStatistics: [],
      pageNo: 1,
      pageSize: 20,
      tableTotal: 0,
      tableHeight: 400,
      tableLoading: false,
      defaultColDef: {
        filter: false,
        floatingFilter: false,
        editable: false,
        cellStyle: { 'text-align': 'center' }
      },
      columns: [
        {
          headerName: "序号",
          field: "orderNum",
          width: 50,
        },
        {
          headerName: "科室",
          field: "auditorDepartment",
          flex: 1,
          minWidth: 100,
        },
        {
          headerName: "测试员",
          field: "userName",
          flex: 1,
          minWidth: 100,
        },
        {
          headerName: "测试项目",
          field: "testProjectName",
          flex: 2,
          minWidth: 200,
          tooltipValueGetter: this.pbiTooltip,
        },
        {
          headerName: "测试状态",
          field: "testStatus",
          flex: 1,
          minWidth: 100,
        },
        {
          headerName: "完成日期",
          field: "realEndTimeRange",
          flex: 2,
          minWidth: 200,
          cellRenderer: function (params) {
            return params.value !== "~" ? params.value : ""
          }
        },
        {
          headerName: "完成周期",
          field: "period",
          flex: 1,
          minWidth: 100,
        },
        {
          headerName: "数量",
          field: "ordtaskNum",
          flex: 1,
          minWidth: 100,
        }
      ],
    }
  },
  watch: {
    isShowAllSearch(newVal, oldVal) {
      this.handleHeight()
    }
  },
  created() {
    this.getTestWorkStatistics();
  },
  computed: {},
  mounted() {
    this.handleHeight()
  },
  methods: {
    handleHeight() {
      this.tableHeight = document.body.clientHeight - 105 - 100 - (this.isShowAllSearch ? 40 : 0)
    },
    callback(key) {
      this.laboratoryId = key
    },
    timeRangeChange(a, b) {
      this.queryParam.timeRangeBegin = b[0]
      this.queryParam.timeRangeEnd = b[1]
      this.timeRange = b
      this.getTestWorkStatistics()
    },
    resetParam() {
      this.queryParam = {laboratoryId: "HZ_YJ_DL_JM", timeDimension: "按周"}
      this.timeRange = []
      this.getTestWorkStatistics()
    },
    handlePageChange(value) {
      let {current, pageSize} = value
      this.pageNo = current
      this.pageSize = pageSize
      this.getTestWorkStatistics()
    },
    // 鼠标进入
    tableFocus() {
      this.$el.style.setProperty('--scroll-border-bottom-fixed', 'none');
      this.$el.style.setProperty('--scroll-display', 'unset');
      this.$el.style.setProperty('--scroll-border-bottom', '1px solid #Dee1e8');
    },
    // 鼠标移出
    tableBlur() {
      this.$el.style.setProperty('--scroll-border-bottom-fixed', '1px solid #dee1e8');
      this.$el.style.setProperty('--scroll-display', 'none');
      this.$el.style.setProperty('--scroll-border-bottom', 'none');
    },
    getTestWorkStatistics() {
      this.tableLoading = true
      getTestWorkStatistics({
        ...{
          pageNum: this.pageNo,
          pageSize: this.pageSize
        }, ...this.queryParam
      }).then((res) => {
        if(res.success) {
          res.data.rows.forEach((item, index) => {item.orderNum = (this.pageNo - 1) * this.pageSize + index + 1})
          this.tableTotal = res.data.totalRows
          this.testWorkStatistics = res.data.rows
        }
      }).finally(() => {
        if (this.pageNo > 1 && this.testWorkStatistics.length === 0) {
          this.pageNo = Math.max(1, Math.ceil(this.tableTotal / this.pageSize))
          this.getTestWorkStatistics()
        }
        this.tableLoading = false
      })
    },
    exportTestWorkStatistics() {
      exportTestWorkStatistics(this.queryParam).then((res) => {
        let fileName = "测试工作量统计" + this.formattedDate(new Date()) + ".xlsx"
        if (res) {
          downloadfile1(res, fileName)
        }
      })
    },
    formattedDate(date) {
      const year = date.getFullYear();
      const month = ("0" + (date.getMonth() + 1)).slice(-2);
      const day = ("0" + date.getDate()).slice(-2);
      const hours = ("0" + date.getHours()).slice(-2);
      const minutes = ("0" + date.getMinutes()).slice(-2);
      return `${year}${month}${day}${hours}${minutes}`;
    },
  }
}
</script>

<style lang="less" scoped>
@import '/src/components/pageTool/style/pbiSearchItem.less';

:root {
  --scroll-display: none;
  --scroll-border-bottom: none;
  --scroll-border-bottom-fixed: none;
}
/deep/.ag-body-horizontal-scroll{
  border-bottom: var(--scroll-border-bottom) !important;
}
/deep/.ag-body-horizontal-scroll-viewport {
  display: var(--scroll-display) !important;
  border-bottom: var(--scroll-border-bottom) !important;
}

/deep/.ag-horizontal-left-spacer,
/deep/.ag-horizontal-right-spacer{
  border-bottom: var(--scroll-border-bottom-fixed) !important;
}
</style>