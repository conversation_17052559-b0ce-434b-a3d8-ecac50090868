<template>
  <a-modal title="填写OCV" :width="800" :visible="visible" :confirmLoading="loading" @ok="handleSubmit" @cancel="handleCancel">
    <a-spin :spinning="loading">
      <!-- <a-form :form="form">
          <a-form-item style="display: none;" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['testFailureId']" />
          </a-form-item>
          <a-form-item style="display: none;" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['productId']" />
          </a-form-item>
          <a-form-item label="产品名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input placeholder="请输入产品名称"
                  v-decorator="['productName']"  disabled />
          </a-form-item>
          <a-form-item label="电芯编码" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input placeholder="请输入电芯编码"
                  v-decorator="['cellCode']" disabled/>
          </a-form-item>
          <a-form-item label="内阻" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input placeholder="请输入内阻"
                  v-decorator="['insideRes', {rules: [{required: true, message: '请输入内阻！'}]}]" />
          </a-form-item>
          <a-form-item label="电压" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input placeholder="请输入电压"
                  v-decorator="['voltage', {rules: [{required: true, message: '请输入电压！'}]}]" />
          </a-form-item>
      </a-form> -->
<!--      <a-divider orientation="left">日期</a-divider>-->
      <a-descriptions  >
        <a-descriptions-item label="记录日期">
          <div>
            <a-date-picker @change="getDpvTestFailureList({stockStatusList:['inStore','outStoring']})" v-model="recordDate"
                           :allowClear="false"
                            placeholder="请输入记录日期" />
          </div>
        </a-descriptions-item>
        <a-descriptions-item label="样品编号">
          <div>
<!--            <a-date-picker @change="getDpvTestFailureList({stockStatusList:['inStore','outStoring']})" v-model="orderNo"-->
<!--                           :allowClear="false"-->
<!--                           placeholder="请输入样品编号" />-->
            <a-input @change="orderNoInput({stockStatusList:['inStore','outStoring']})"
                     v-model="orderNo"
                     placeholder="请输入样品编号"  />
          </div>
        </a-descriptions-item>
        <a-descriptions-item >
          <a-button v-if="serialObj.serialPortOpen"  class="mr10" type="primary" size="small" ghost @click="handleCloseConnect">断开电压内阻测试仪</a-button>
          <a-button id="connectBtn" v-else class="mr10" type="primary" size="small" ghost @click="handleOpenConnect">连接电压内阻测试仪</a-button>
        </a-descriptions-item>
      </a-descriptions>
<!--      <a-divider orientation="left">OCV记录填写</a-divider>-->
      <div class="ocvTable">
<!--        <s-table :style="{height:'initial'}"-->
<!--                 :rowKey="record => record.id"-->
<!--                 :loading="loading"-->
<!--                 :columns="tableColumns"-->
<!--                 ref="ocvTable"-->
<!--                 :data="loadData"-->
<!--                 size="middle" bordered>-->
<!--          <template slot="insideRes" slot-scope="text, record, index, column">-->
<!--            <a-input v-model="record.insideRes"-->
<!--                     @keyup.enter="handleWrite(column.dataIndex,index,`first-${column.dataIndex}-${index + 1}`)"-->
<!--                     @blur="value => handleInput(value.target._value, `first-${item.dataIndex}-${index}`, index)"/>-->
<!--          </template>-->
<!--          <template slot="voltage" slot-scope="text, record">-->
<!--            <a-input v-model="record.voltage" />-->
<!--          </template>-->
<!--          -->
<!--        </s-table>-->

        <div class="mt10">
          <a-table :pagination="pagination" :style="{height:'initial'}" :rowKey="record => record.testFailureId" :loading="loading" :columns="tableColumns" :data-source="tableData" size="middle" bordered>
            <template slot="insideRes" slot-scope="text, record, index, column">
              <a-input v-model="tableData[index].insideRes"
                       @keyup.enter="handleWrite(column.dataIndex,index,`first-${column.dataIndex}-${index + 1}`)"
                       @blur="value => handleInput(value.target._value, `first-${column.dataIndex}-${index}`, index, record)"
              />
            </template>
            <template slot="voltage" slot-scope="text, record, index, column">
              <a-input v-model="tableData[index].voltage"
                       @keyup.enter="handleWrite(column.dataIndex,index,`first-${column.dataIndex}-${index + 1}`)"
                       @blur="value => handleInput(value.target._value, `first-${column.dataIndex}-${index}`, index, record)"
              />
            </template>
          </a-table>

        </div>


<!--        <div>-->
<!--          <a-pagination style="margin-top: 20px;float: right;margin-left: auto" v-model="pageNo"-->
<!--                        :page-size-options="pageSizeOptions" :total="totalRows" show-size-changer :page-size="pageSize"-->
<!--                        @change="onPageChange" @showSizeChange="onPageChange">-->
<!--            <template slot="buildOptionText" slot-scope="props">-->
<!--              <span v-if="props.value !== totalRows">{{ props.value }}条/页</span>-->
<!--              <span v-if="props.value === totalRows">全部</span>-->
<!--            </template>-->
<!--          </a-pagination>-->
<!--        </div>-->

      </div>
    </a-spin>
  </a-modal>
</template>

<script>
import {
  addFailureCellOcvRecord, addOrUpdateFailureCellOcvRecord,
  getDpvTestFailureList, getFailureCellOcvFillPageList, getFailureCellOcvRecordPageList
} from "@/api/modular/system/testFailure"
import {clamp, STable} from '@/components'
import moment from "moment";
import { Pagination } from 'ant-design-vue';
export default {
  components: {
    clamp,
    STable,
    'a-pagination': Pagination,
  },
  props: {
    serialObj: {
      type: Object,
      default: function () {
        return  {
          serialPort: {},
          serialReader: {},
          serialWriter:{},
          serialPortOpen:false,
        }
      }
    },
  },
  // mixins: [mixin],
  data() {
    return {
      // visible: false,
      // loading: false,
      record:{},
      recordDate: moment(),
      orderNo: null,
      orderNoTimeout: null,
      queryParam:{},
      loadData: parameter => {
        this.queryParam.recordDate = this.recordDate
        return getFailureCellOcvFillPageList(Object.assign(parameter, this.queryParam)).then((res) => {
          return res.data
        })
      },
      tableColumns:[
        {
          title: '序号',
          dataIndex: 'index',
          customRender: (text, record, index) => `${index+1}`
        },
        {
          title: '产品名称',
          dataIndex: 'productName'
        },
        {
          title: '样品编号',
          dataIndex: 'orderNo'
        },
        {
          title: '电芯编码',
          dataIndex: 'cellCode'
        },
        {
          title: '内阻/mΩ',
          dataIndex: 'insideRes',
          scopedSlots: {
            customRender: 'insideRes'
          },
        },
        {
          title: '电压/mV',
          dataIndex: 'voltage',
          scopedSlots: {
            customRender: 'voltage'
          },
        },
      ],
      tableData:[],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        onChange: (current, size) => {
          this.pagination.current = current
          this.pagination.pageSize = size
        },
        onShowSizeChange: (current, pageSize) => {
          this.pagination.current = 1
          this.pagination.pageSize = pageSize
        },
      },

      visible: false,
      loading: false,
      //form: this.$form.createForm(this)

      //串口读数据
      isFirst :true,

      // 串口
      // serialObj:{
      //   serialPort: {},
      //   serialReader: {},
      //   serialWriter:{},
      //   serialPortOpen:false,
      // },
    }
  },
  methods: {
    // 电压内阻测试仪
    // 串口连接
    async handleOpenConnect() {
      try {
        this.serialObj.serialPort = await navigator.serial.requestPort();
        await this.serialObj.serialPort.open({
          baudRate: 9600,
          dataBits:8,
          stopBits:1,
          parity:'none'
        })

        this.serialObj.serialReader = this.serialObj.serialPort.readable.getReader();
        this.serialObj.serialWriter = this.serialObj.serialPort.writable.getWriter();

        this.serialObj.serialPortOpen = true
        this.$notification.success({
          message: '设备连接成功',
        });
      }catch (error) {
        console.log(error);
        this.$notification.error({
          message: '用户取消连接/设备出错',
        });
      }
    },
    // 串口断开
    async handleCloseConnect() {
      try {
        this.serialObj.serialReader.releaseLock();
        this.serialObj.serialWriter.releaseLock();
        await this.serialObj.serialPort.close();
        this.serialObj.serialPortOpen = false
        this.$notification.success({
          message: '设备连接已断开',
        });
      }catch (error) {
        console.log(error)
        this.$notification.error({
          message: '连接断开失败',
        });
      }
    },
    // 回车写入数据事件
    async handleWrite(dataIndex,index,focusId){
      if(!this.serialObj.serialPort || !this.serialObj.serialReader || !this.serialObj.serialWriter || !this.serialObj.serialPortOpen)  return

      // 如果焦点在的位置是内阻、电压
      if(dataIndex == 'insideRes' || dataIndex == 'voltage'){
        const serialValue = await this.writeToSerial()
        console.log(serialValue);

        // if(dataIndex == 'beforeInnerres' || dataIndex == 'beforeVoltage'){
        //   this.tableData[index].beforeInnerres = Number(serialValue.split(',')[0])
        //   this.tableData[index].beforeVoltage = Number(serialValue.split(',')[1] * 1000)
        // }else{
          this.tableData[index].insideRes = Number(serialValue.split(',')[0])
          this.tableData[index].voltage = Number(serialValue.split(',')[1] * 1000)

        // this.$set(this.tableData[index],'insideRes',Number(serialValue.split(',')[0]))
        // this.$set(this.tableData[index],'voltage',Number(serialValue.split(',')[1] * 1000))

        // this.tableData[index].insideRes = 10
        // this.tableData[index].voltage = 15
        // }

      }

      if(index !== this.tableData.length - 1){
        document.getElementById(focusId).focus();
      }
      console.log(this.tableData)
    },

    async writeToSerial(){
      if(!this.serialObj.serialPort || !this.serialObj.serialReader || !this.serialObj.serialWriter || !this.serialObj.serialPortOpen)  return

      const targerArr = this.isFirst ? [':INITiate:CONTinuous ON',':TRIGger:SOURce IMM',':FETCh?'] : [':FETCh?']
      // 写入设备
      await this._writeToSerial(targerArr)
      if(targerArr.includes(':INITiate:CONTinuous ON')) this.isFirst = false

      // 读取设备数值
      const serialValue =  await this._readerToSerial()

      return serialValue

    },
    _writeToSerial(arr){
      arr.forEach(async v => {
        await this.serialObj.serialWriter.write(new TextEncoder().encode(v +'\n'));
      })
    },
    async _readerToSerial(){
      try {
        let result = []
        while (true) {
          const { value, done } = await this.serialObj.serialReader.read()

          if (done) {
            this.serialObj.serialReader.releaseLock();
            this.serialObj.serialWriter.releaseLock();
            break;
          }
          if(value){
            result += this.Uint8ArrayToString(value)
          }
          // 换行符表示读取数据结束
          if(Array.from(value).includes(10)){
            break
          }
        }
        return result
      } catch (error) {
        console.error(error);
      }
    },

    Uint8ArrayToString(fileData){
      var dataString = "";
      for (var i = 0; i < fileData.length; i++) {
        dataString += String.fromCharCode(fileData[i]);
      }
      return dataString
    },

    handleInput(value, id, row, record) {
      this.$forceUpdate()
      // value:填写的值，id：对应的id，row：那一行数据发生修改
      if (record.id == null) {
        addFailureCellOcvRecord({ocvRecords:[record]}).then(res=>{
          if (!res.success) {
            return
          }
          record.id = res.data;
        });

      }else {
        addOrUpdateFailureCellOcvRecord({ocvRecords:[record]}).then(res=>{
          if (!res.success) {
            return
          }
        });
      }

      // if (value !== "" && document.getElementById(id).style.backgroundColor === "rgb(255, 233, 237)") {
      //   document.getElementById(id).style.backgroundColor = "transparent"
      // }
      // if (id.indexOf('before') !== -1 && value) {
      //   // this.dataInfo[row].timeOfFillInnerres = formatDate(new Date(), true)
      //   this.tableData[row].timeOfFillInnerres = formatDate(new Date(), true)
      // }
      // if (id.indexOf('after') !== -1 && value) {
      //   // this.dataInfo[row].timeOfFillInnerres2 = formatDate(new Date(), true)
      //   this.tableData[row].timeOfFillInnerres2 = formatDate(new Date(), true)
      // }
      // const params = {
      //   id: this.modalData.ordTaskId,
      //   lifeTestRecordDataMap: JSON.parse(JSON.stringify(this.tableData))
      // }
      // this.updateTestProDetail(params)
    },

    //串口读取数据结束




    add(record) {
      this.getDpvTestFailureList({
        pageNo: this.pagination.current ,
        pageSize: this.pagination.pageSize,
        stockStatusList:['inStore','outStoring']
      })
      this.recordDate = moment();
      console.log(this.serialObj)
      this.visible = true
      // this.$nextTick(() => {
      //   this.$refs.ocvTable.refresh()
      // })
    },
    handleSubmit() {
      // for (const item of this.tableData) {
      //   if (!item.insideRes || item.insideRes == '') {
      //     this.$message.error('请输入'+item.cellCode+'的内阻')
      //     return
      //   }
      //   if (!item.voltage || item.voltage == '') {
      //     this.$message.error('请输入'+item.cellCode+'电压')
      //     return
      //   }
      // }
      this.handleCancel();
      // addFailureCellOcvRecord({ocvRecords:this.tableData}).then((res) => {
      //   this.loading = false
      //   if (res.success) {
      //     this.$message.success('新增成功')
      //     this.handleCancel()
      //   } else {
      //     this.$message.error('新增失败：' + res.message)
      //   }
      // }).finally((res) => {
      //   this.loading = false
      // })
    },
    handleCancel() {
      this.recordDate = moment()
      this.orderNo = null
      this.tableData = []
      this.visible = false
    },
    orderNoInput(params) {
      clearTimeout(this.orderNoTimeout)
      this.orderNoTimeout = setTimeout(() => {
        this.getDpvTestFailureList(params)
      }, 500)
    },
    getDpvTestFailureList(params){
      this.queryParam.recordDate = this.recordDate
      this.queryParam.orderNo = this.orderNo
      getFailureCellOcvFillPageList(Object.assign(params, this.queryParam)).then((res) => {
        // return res.data
        if (!res.success) {
          return
        }
        this.tableData = res.data.rows;
        // this.pageNo = res.data.pageNo;
        // this.pageSize = res.data.pageSize;
        this.pagination.current = res.data.pageNo;
        this.pagination.pageSize = res.data.pageSize;
        this.pagination.total = res.data.totalRows;
        // this.totalRows = res.data.totalRows;
        // if (this.pagination.pageSizeOptions.indexOf(this.pagination.total) === -1) {
        //   this.pagination.pageSizeOptions.push(this.pagination.total)
        // }
      }).finally(() => {
        this.loading = false
      })

      // getDpvTestFailureList(params).then(res => {
      //   this.loading = true
      //   if (!res.success) {
      //     return
      //   }
      //   let _tableData = []
      //   for (const item of res.data) {
      //     _tableData.push({
      //       testFailureId:item.id,
      //       productId:item.productId,
      //       productName:item.productName,
      //       cellCode:item.cellCode
      //     })
      //   }
      //   this.tableData = _tableData
      // }).finally(() => {
      //   this.loading = false
      // })
    },
    onPageChange(pageNo, pageSize) {
      this.pagination.current  = pageNo;
      this.pagination.pageSize = pageSize;
      this.getDpvTestFailureList({
        pageNo: this.pagination.current ,
        pageSize: this.pagination.pageSize,
        stockStatusList:['inStore','outStoring']
      })
        // .then(res => {
        //   this.pageData = JSON.parse(res.data.allDataJson).tableList
        //   this.tableList = this.pageData.records
        //   this.pageNo = this.pageData.current
        //   this.pageSize = this.pageData.size
        //   this.totalRows = this.pageData.total
        //   if (this.pageSizeOptions.indexOf(this.totalRows) === -1) {
        //     this.pageSizeOptions.push(this.totalRows)
        //   }
        // })
    },
  }
}
</script>
<style lang="less" scoped=''>
/deep/ .ant-modal-body{
  padding: 12px !important;
}
/deep/.ocvTable .ant-table-body {
  height: initial !important;
  overflow-y: scroll;
}
/deep/.ocvTable .ant-table-placeholder{
  display: none !important;
}
</style>