<template>
	<a-modal
		title="计划表"
		:visible="true"
		width="60%"
		:centered="true"
		:cancel-button-props="{ style: { display: 'none' } }"
		okText="关闭"
		@cancel="handleModelCancel"
		@ok="handleModelCancel"
	>
		<div class="model-wrapper">
			<a-spin :spinning="modalLoading">
				<a-descriptions>
					<a-descriptions-item label="中检次数"> {{ tableData.data.length }} </a-descriptions-item>
					<a-descriptions-item label="进箱开始时间">{{ tableData.zero }}</a-descriptions-item>
				</a-descriptions>
				<a-table
					bordered
					:columns="columns"
					:rowKey="record => record.id"
					:data-source="tableData.data"
					:pagination="false"
				>
					<span slot="originalresult" slot-scope="text, record">
						<a-input
							placeholder="请输入原始结果"
							:disabled="modalData.taskStatus === '已完成' ? true : false"
							v-model="record.originalresult"
							@blur="handleInput(record)"
						/>
					</span>
					<span slot="status" slot-scope="text">
						<a-tag v-if="text" color="green">
							{{ text }}
						</a-tag>
						<span v-else>-</span>
					</span>
				</a-table>
			</a-spin>
		</div>
	</a-modal>
</template>

<script>
import { getTestProgress } from "@/api/modular/system/testProgressManager"

export default {
	name: "ScheduleModal",
	props: {
		modalData: {
			type: Object,
			default: {}
		}
	},
	data() {
		return {
			modalLoading: false,
			columns: [
				{
					title: "存储阶段",
					dataIndex: "index",
					align: "center",
					customRender: (text, record, index) => {
            if (index === 0) {
              return `初始阶段`
            } else {
              return `存储第${index}阶段`
            }
          }
				},
				{
					title: "存储天数",
					dataIndex: "day",
					scopedSlots: {
						customRender: "day"
					}
				},
        {
          title: "总存储天数",
          dataIndex: "totalDay",
          scopedSlots: {
            customRender: "totalDay"
          }
        },
				{
					title: "计划进箱时间",
					dataIndex: "analytegrouphead",
					dataIndex: "inDate"
				},
				{
					title: "实际进箱时间",
					dataIndex: "analytename",
					dataIndex: "actualInDate"
				},
				{
					title: "计划出箱时间",
					dataIndex: "requirement",
					dataIndex: "outDate"
				},
        {
          title: "实际出箱时间",
          dataIndex: "requirement",
          dataIndex: "actualOutDate"
        },
				{
					title: "电芯异动说明",
					dataIndex: "originalresult",
					dataIndex: "saveAddress"
				}
			],
			tableData: {}
		}
	},
	created() {
		this.getTestProgress()
	},
	mounted() {},
	methods: {
		getTestProgress() {
			this.modalLoading = true
			getTestProgress({ id: this.modalData.testProgressId })
				.then(res => {
					if (!res.success) return this.$message.error("错误提示：" + err.message)
					this.tableData = res.data
				})
				.finally(() => {
					this.modalLoading = false
				})
		},

		/**
		 * 弹窗事件
		 */
		handleModelCancel() {
			this.$emit("cancel")
		}
	}
}
</script>

<style lang="less" scoped>
/deep/.ant-modal-header {
	border: none !important;
}
/deep/.ant-modal-body {
	padding: 5px 24px;
}
/deep/.ant-modal-footer {
	border: none !important;
}
/deep/.ant-table-thead {
	z-index: 999;
}
/deep/.ant-table-body {
	min-height: 280px;
	overflow: auto;
}
/deep/.ant-table-placeholder {
	border: none !important;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}
</style>
