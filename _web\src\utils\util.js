import axios from 'axios'

import Pako from 'pako';

export function timeFix() {
  const time = new Date()
  const hour = time.getHours()
  return hour < 9 ? '早上好' : hour <= 11 ? '上午好' : hour <= 13 ? '中午好' : hour < 20 ? '下午好' : '晚上好'
}

export function getQueryVariable(url) {
  if (!url) {
    return {}
  }
  const str = url.substr(url.indexOf('?') + 1)
  // arr每个元素都是完整的参数键值
  const arr = str.split('&')
  // result为存储参数键值的集合
  const result = {}
  for (let i = 0; i < arr.length; i++) {
    // item的两个元素分别为参数名和参数值
    const item = arr[i].split('=')
    result[item[0]] = item[1]
  }
  return result
}

export function welcome() {
  const arr = ['', '', '']
  const index = Math.floor(Math.random() * arr.length)
  return arr[index]
}

/**
 * 触发 window.resize
 */
export function triggerWindowResizeEvent() {
  const event = document.createEvent('HTMLEvents')
  event.initEvent('resize', true, true)
  event.eventType = 'message'
  window.dispatchEvent(event)
}

export function handleScrollHeader(callback) {
  let timer = 0

  let beforeScrollTop = window.pageYOffset
  callback = callback || function () { }
  window.addEventListener(
    'scroll',
    event => {
      clearTimeout(timer)
      timer = setTimeout(() => {
        let direction = 'up'
        const afterScrollTop = window.pageYOffset
        const delta = afterScrollTop - beforeScrollTop
        if (delta === 0) {
          return false
        }
        direction = delta > 0 ? 'down' : 'up'
        callback(direction)
        beforeScrollTop = afterScrollTop
      }, 50)
    },
    false
  )
}

export function isIE() {
  const bw = window.navigator.userAgent
  const compare = (s) => bw.indexOf(s) >= 0
  const ie11 = (() => 'ActiveXObject' in window)()
  return compare('MSIE') || ie11
}

/**
 * Remove loading animate
 * @param id parent element id or class
 * @param timeout
 */
export function removeLoadingAnimate(id = '', timeout = 1500) {
  if (id === '') {
    return
  }
  setTimeout(() => {
    document.body.removeChild(document.getElementById(id))
  }, timeout)
}


// 防抖
export function debounce(fn, wait) {
  var timer = null
  return function () {
    if (timer !== null) {
      clearTimeout(timer)
    }
    timer = setTimeout(fn, wait)
  }
}

/**下载文件 */
export async function downloadfile(res, name) {
  res = res.replace('http://limstest.evebattery.com', '/limsUpload')
  const response = await axios({
    url: res,
    method: 'GET',
    responseType: 'blob', // 必须指定为blob类型才能下载
  });
  const url = window.URL.createObjectURL(new Blob([response.data]));
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', name);
  document.body.appendChild(link);
  link.click();
}

/**
 * minio 下载文件
 **/
export async function downloadMinioFile(url) {
  //代理
  let fileUrl = url.replace("http://10.100.1.99:9000/", "/minioDownload/")
  const link = document.createElement('a');
  link.href = fileUrl;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link)

}


/**
 * minio 下载文件
 **/
export async function downloadMinioFileList(urlList) {
  for (let i = 0; i < urlList.length; i++) {
    let url = urlList[i];
    // 代理
    let fileUrl = url.replace("http://10.100.1.99:9000/", "/minioDownload/");

    const link = document.createElement('a');
    link.href = fileUrl;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 添加延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}




/**下载文件 */
export async function downloadfile1(res, name) {
  const _res = res.data;
  let blob = new Blob([_res]);
  let downloadElement = document.createElement("a");
  //创建下载的链接
  let href = window.URL.createObjectURL(blob);
  downloadElement.href = href;
  //下载后文件名
  downloadElement.download = name;
  document.body.appendChild(downloadElement);
  //点击下载
  downloadElement.click();
  //下载完成移除元素
  document.body.removeChild(downloadElement);
  //释放掉blob对象
  window.URL.revokeObjectURL(href);
}

/**
 * @title 合并表格行
 * @param {*} list 数组
 * @param {*} key 需要合并的字段
 */
export function handleMergeRow(list, key) {
  let temIndex = 0
  let temLength = 0
  list.forEach((v, index) => {
    if (v[key] === null) {
      return (v.rowSpan = 1)
    }

    // 第一条数据时
    if (index === 0 || temLength + temIndex < index || temLength + temIndex === index) {
      temLength = list.filter(e => v[key] === e[key]).length
      v.rowSpan = temLength
      temIndex = index
      return
    }

    if (temLength + temIndex > index)
      return v.rowSpan = 0
  })
}

export function decodeAndDecompress(compressedDataBase64) {
  try {
    // Base64解码
    let compressedBytes = atob(compressedDataBase64);
    // 将字符串转换为Uint8Array，因为pako需要字节数组作为输入
    let buffer = new Uint8Array(compressedBytes.length);
    for (let i = 0; i < compressedBytes.length; i++) {
      buffer[i] = compressedBytes.charCodeAt(i);
    }
    // 使用pako进行inflate解压缩
    let decompressedData = Pako.inflate(buffer, { to: 'string' });
    return decompressedData;
  } catch (error) {
    // console.error('解压失败', error);
    console.error('解压失败');
    //可能是没压缩过的，则直接返回源字符串
    return compressedDataBase64;
  }
}