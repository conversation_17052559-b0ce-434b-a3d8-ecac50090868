<template>
  <div class="coefficient-panel">
    <a-row :gutter="16" class="panel-row">
      <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" class="panel-col">
        <div class="coefficient-range-settings">
          <h5>系数值设定</h5>

          <excel-import-export
            :coefficients="coefficients"
            @coefficients-updated="handleCoefficientsUpdated"
          />

          <div class="tabs-container">
            <a-tabs
              :default-active-key="firstGroupKey"
              :active-key="activeTabKey"
              @change="handleTabChange"
              :force-render="true"
              class="unified-tabs coefficient-tabs"
            >
              <a-tab-pane
                v-for="group in groupedCoefficients"
                :key="`${group.letter}-group`"
                :tab="`${group.letter}组`"
                :force-render="true"
              >
                <coefficient-table
                  :coefficients="group.coefficients"
                  @coefficient-change="handleCoefficientChange"
                  @description-change="handleDescriptionChange"
                  @table-change="handleTableChange"
                  @coefficients-updated="handleTableCoefficientsUpdated"
                />
              </a-tab-pane>
            </a-tabs>
          </div>
        </div>
      </a-col>

      <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" class="panel-col">
        <formula-info-form
          :formulaId.sync="localFormulaId"
          :formulaDescription.sync="localFormulaDesc"
          @save-formula="saveCustomFormula"
        />
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { api } from '@/api';
import formulaMixin from '@/mixins/formulaMixin';
import coefficientMixin from '@/mixins/coefficientMixin';
import { showSuccess, showWarning } from '@/utils/errorUtils';
import CoefficientTable from './CoefficientTable.vue';
import ExcelImportExport from './ExcelImportExport.vue';
import FormulaInfoForm from './FormulaInfoForm.vue';

export default {
  name: 'CustomCoefficientsPanel',
  components: {
    CoefficientTable,
    ExcelImportExport,
    FormulaInfoForm
  },
  mixins: [formulaMixin, coefficientMixin],
  props: {
    coefficients: {
      type: Array,
      required: true
    },
    parsedLatex: {
      type: [String, Object],
      default: ''
    },
    parsedParams: {
      type: Array,
      required: true
    },
    formulaId: {
      type: String,
      default: ''
    },
    formulaDescription: {
      type: String,
      default: ''
    },
    variableDescription: {
      type: String,
      default: ''
    },
    paramDescriptions: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      localFormulaId: this.formulaId,
      localFormulaDesc: this.formulaDescription,
      groupDescriptions: {},
      activeTabKey: null, // 当前激活的标签页
      firstGroupKey: null, // 首组参数的key
    };
  },
  computed: {
    groupedCoefficients() {
      return this.groupCoefficientsByLetter(this.coefficients);
    }
  },
  watch: {
    formulaId(newVal) {
      this.localFormulaId = newVal;
    },
    formulaDescription(newVal) {
      this.localFormulaDesc = newVal;
    },
    coefficients: {
      handler(newCoefs) {
        if (newCoefs?.length) {
          this.$nextTick(() => {
            this.setFirstGroupKey();
            this.renderMathJax(true);
          });
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleGroupDescriptionChange(groupLetter) {
      const group = this.groupedCoefficients.find(g => g.letter === groupLetter);
      if (group) {
        this.groupDescriptions[groupLetter] = group.description;
      }
    },

    handleTabChange(activeKey) {
      this.activeTabKey = activeKey;

      this.$nextTick(() => {
        const activeIndex = this.groupedCoefficients.findIndex(
          group => `${group.letter}-group` === activeKey
        );

        if (activeIndex !== -1) {
          const selector = `.ant-tabs-content .ant-tabs-tabpane:nth-child(${activeIndex + 1})`;
          this.renderMathJax(true, selector);
        } else {
          this.renderMathJax(true, '.coefficient-tabs');
        }
      });
    },

    handleTableChange() {
      this.renderMathJax(false);
    },

    handleCoefficientChange(record) {
      // 子组件已经更新了系数值，这里只需要触发视图更新
      this.$forceUpdate();
    },

    handleDescriptionChange(record) {
      // 子组件已经更新了系数描述，这里只需要触发视图更新
      this.$forceUpdate();
    },

    handleTableCoefficientsUpdated(updatedCoefficients) {
      // 处理来自CoefficientTable的系数更新事件
      // 向父组件发出更新事件
      this.$emit('coefficients-updated', this.coefficients);

      // 强制更新视图
      this.$forceUpdate();
    },

    handleCoefficientsUpdated(updatedCoefficients) {
      // Excel导入后更新系数
      if (updatedCoefficients && Array.isArray(updatedCoefficients)) {
        // 更新当前组件的系数数据
        updatedCoefficients.forEach(updatedCoef => {
          const existingCoef = this.coefficients.find(c => c.name === updatedCoef.name);
          if (existingCoef) {
            // 使用Vue.set确保响应式更新
            this.$set(existingCoef, 'customValue', updatedCoef.customValue);
          }
        });

        // 向父组件发出更新事件
        this.$emit('coefficients-updated', this.coefficients);
      }

      // 强制更新视图并重新渲染MathJax
      this.$forceUpdate();
      this.$nextTick(() => {
        this.renderMathJax(true);
      });
    },

    async saveCustomFormula() {
      const formulaData = this.buildFormulaData();
      this.$emit('formula-saved', formulaData);

      try {
        const response = await api.formula.saveFormula(formulaData);
        if (response.data.success) {
          showSuccess(response.data.message || '自定义公式已保存到服务器');
        } else {
          showWarning(response.data.message || '保存公式到服务器失败');
        }
      } catch (error) {
        console.error('保存公式请求失败:', error);
        showWarning(error.message || '保存公式请求失败');
      }
    },

    buildFormulaData() {
      const validParams = this.parsedParams.map(param => {
        const result = {
          name: param.name,
          type: param.type
        };

        if (param.type === 'variable') {
          result.value = null;
          result.describe = this.paramDescriptions?.[param.name] || null;
        } else {
          const coefficientInstance = this.coefficients.find(c => c.name === param.name);

          if (coefficientInstance?.customValue !== undefined) {
            result.value = coefficientInstance.customValue;
            result.customValue = coefficientInstance.customValue;
          } else {
            result.value = param.customValue !== undefined ? param.customValue : (param.value || 0);
            result.customValue = result.value;
          }

          result.optRange = param.optRange || { min: 0, max: 1, initial: 0.5 };
          result.coefficientMode = 'custom';
          result.describe = param.describe || null;

          const groupLetter = this.findGroupLetterForCoefficient(param.name);
          if (groupLetter) {
            result.groupDescription = this.groupDescriptions[groupLetter] || '';
          }
        }

        return result;
      });

      let latexObj;
      if (typeof this.parsedLatex === 'string') {
        latexObj = {
          main_formula: this.parsedLatex,
          sub_formulas: []
        };
      } else if (this.parsedLatex && typeof this.parsedLatex === 'object') {
        latexObj = this.parsedLatex;
      } else {
        latexObj = {
          main_formula: '',
          sub_formulas: []
        };
      }

      return {
        latex: latexObj,
        params: validParams,
        coefficientMode: 'custom',
        description: this.localFormulaDesc,
        id: this.localFormulaId || `formula_${Date.now()}`,
        variableDescription: this.variableDescription,
        groupDescriptions: this.groupDescriptions
      };
    },

    setFirstGroupKey() {
      if (this.groupedCoefficients?.length) {
        const firstGroup = this.groupedCoefficients[0];
        const newFirstGroupKey = `${firstGroup.letter}-group`;

        if (this.firstGroupKey !== newFirstGroupKey) {
          this.firstGroupKey = newFirstGroupKey;

          const groupKeys = this.groupedCoefficients.map(g => `${g.letter}-group`);
          if (!this.activeTabKey || !groupKeys.includes(this.activeTabKey)) {
            this.activeTabKey = this.firstGroupKey;
          }
        }
      }
    }
  },
  mounted() {
    this.setFirstGroupKey();
    this.$nextTick(() => {
      this.renderMathJax(true);
    });
  }
};
</script>

<style scoped>
.coefficient-panel {
  margin-bottom: 16px;
}

.panel-row {
  display: flex;
  align-items: stretch;
}

.panel-col {
  display: flex;
}

.coefficient-range-settings {
  width: 100%;
  padding: 0;
  background-color: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.tabs-container {
  padding: 16px;
}

h5 {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 0;
  color: #333;
  padding: 10px 0;
  background-color: #f5f5f5;
  border-bottom: 1px solid #f0f0f0;
  text-align: center;
  border-radius: 8px 8px 0 0;
}

:deep(.ant-tabs-tabpane > .ant-table-wrapper) {
  margin: 0 !important;
}

@media (forced-colors: active) {
  .coefficient-panel,
  .coefficient-range-settings {
    forced-color-adjust: auto;
    border: 1px solid CanvasText;
  }

  h5 {
    border-bottom: 1px solid CanvasText;
  }
}
</style>