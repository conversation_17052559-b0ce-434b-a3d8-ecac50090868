/**
 * 修复可访问性问题的样式
 */

/* 修复 aria-hidden 与焦点元素冲突的问题 */
/* 当元素被 aria-hidden 隐藏时，确保其子元素不能获得焦点 */
[aria-hidden="true"] * {
  /* 移除焦点能力，防止与 aria-hidden 冲突 */
  pointer-events: none !important;
  /* 确保不会意外获得焦点 */
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

/* 更强的规则：移除隐藏元素的 tabindex */
[aria-hidden="true"] *[tabindex] {
  tabindex: -1 !important;
}

/* 确保隐藏元素的所有可聚焦子元素都不能获得焦点 */
[aria-hidden="true"] input,
[aria-hidden="true"] button,
[aria-hidden="true"] select,
[aria-hidden="true"] textarea,
[aria-hidden="true"] a,
[aria-hidden="true"] [tabindex],
[aria-hidden="true"] [contenteditable] {
  pointer-events: none !important;
  tabindex: -1 !important;
}

/* 对于需要交互的隐藏元素，使用 visibility 而不是 aria-hidden */
.visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* 确保模态框正确处理焦点 */
.ant-modal-wrap {
  /* 当模态框打开时，确保背景不会获得焦点 */
  pointer-events: auto;
}

.ant-modal-wrap[aria-hidden="true"] {
  /* 当模态框隐藏时，完全阻止交互 */
  pointer-events: none;
}

/* 确保标签页内容正确处理焦点 */
.ant-tabs-tabpane[aria-hidden="true"] {
  /* 隐藏的标签页内容不应该获得焦点 */
  pointer-events: none !important;
}

.ant-tabs-tabpane[aria-hidden="true"] * {
  /* 隐藏标签页内的所有元素都不能获得焦点 */
  pointer-events: none !important;
  tabindex: -1 !important;
}

.ant-tabs-tabpane:not([aria-hidden="true"]) {
  /* 活动的标签页内容可以正常交互 */
  pointer-events: auto;
}

/* 修复 Ant Design 组件的 aria-hidden 焦点冲突 */
/* 当 Select 下拉框隐藏时，确保其内容不会获得焦点 */
.ant-select-dropdown[aria-hidden="true"] * {
  pointer-events: none !important;
  tabindex: -1 !important;
}

/* 当 Tooltip 隐藏时，确保其内容不会获得焦点 */
.ant-tooltip[aria-hidden="true"] * {
  pointer-events: none !important;
  tabindex: -1 !important;
}

/* 当 Popover 隐藏时，确保其内容不会获得焦点 */
.ant-popover[aria-hidden="true"] * {
  pointer-events: none !important;
  tabindex: -1 !important;
}

/* 当 Dropdown 隐藏时，确保其内容不会获得焦点 */
.ant-dropdown[aria-hidden="true"] * {
  pointer-events: none !important;
  tabindex: -1 !important;
}

/* 当 Menu 隐藏时，确保其内容不会获得焦点 */
.ant-menu[aria-hidden="true"] * {
  pointer-events: none !important;
  tabindex: -1 !important;
}

/* 修复可能的焦点陷阱问题 */
[tabindex="0"][aria-hidden="true"] {
  /* 移除隐藏元素的 tabindex */
  tabindex: -1 !important;
}

/* 额外的可访问性修复规则 */
/* 确保所有隐藏的可聚焦元素都不能获得焦点 */
[aria-hidden="true"] [role="button"],
[aria-hidden="true"] [role="link"],
[aria-hidden="true"] [role="menuitem"],
[aria-hidden="true"] [role="tab"],
[aria-hidden="true"] [role="option"] {
  pointer-events: none !important;
  tabindex: -1 !important;
}

/* 修复 Ant Design 表单元素的焦点问题 */
[aria-hidden="true"] .ant-input,
[aria-hidden="true"] .ant-input-number,
[aria-hidden="true"] .ant-select,
[aria-hidden="true"] .ant-checkbox,
[aria-hidden="true"] .ant-radio,
[aria-hidden="true"] .ant-switch,
[aria-hidden="true"] .ant-slider,
[aria-hidden="true"] .ant-rate,
[aria-hidden="true"] .ant-upload,
[aria-hidden="true"] .ant-cascader,
[aria-hidden="true"] .ant-date-picker,
[aria-hidden="true"] .ant-time-picker {
  pointer-events: none !important;
  tabindex: -1 !important;
}

/* 修复 Ant Design 按钮和链接的焦点问题 */
[aria-hidden="true"] .ant-btn,
[aria-hidden="true"] .ant-btn-link,
[aria-hidden="true"] .ant-anchor-link {
  pointer-events: none !important;
  tabindex: -1 !important;
}

/* 确保隐藏的模态框和抽屉不会获得焦点 */
.ant-modal[aria-hidden="true"],
.ant-drawer[aria-hidden="true"] {
  pointer-events: none !important;
}

.ant-modal[aria-hidden="true"] *,
.ant-drawer[aria-hidden="true"] * {
  pointer-events: none !important;
  tabindex: -1 !important;
}
