<template>
<div>
  <a-breadcrumb class="breadcrumb" separator=">" :style="`width:${_elWidth}px;margin:0 auto;`">
      <a-breadcrumb-item><a @click="gotoIndex(-3)">信息对齐表</a></a-breadcrumb-item>
      <a-breadcrumb-item><a @click="gotoIndex(-2)">产品开发进展</a></a-breadcrumb-item>
      <a-breadcrumb-item><a @click="gotoIndex(-1)">质量指标</a></a-breadcrumb-item>
      <a-breadcrumb-item>文件质量达成率</a-breadcrumb-item>
    </a-breadcrumb>
  <vxe-table :height="windowHeight" :loading="loading" show-footer border align="center" :data="list" :footer-span-method="footerColspanMethod" :footer-method="footerMethod" :span-method="mergeRowMethod">
    
    <vxe-column width="90" field="productStage" title="产品阶段">
      
        </vxe-column>
        <vxe-column width="90" field="content" title="内容"></vxe-column>
        <vxe-column width="90" field="actualDate" title="输出文件">
          
        </vxe-column>
        <vxe-column width="180" field="assessCount" title="责任人">
          
        </vxe-column>
        <vxe-column field="asseessResult" width="180" title="标准">
          
        </vxe-column>
        <vxe-column field="assessIdea" width="180" title="审核结果">
          
        </vxe-column>
        <vxe-column field="delayDays" title="说明">
        
        </vxe-column>
        
    </vxe-table>
</div>
  
</template>

<script>
import { clamp } from '@/components'
import {
  } from "@/api/modular/system/report"
  export default {
    components: {
        clamp
    },
    data() {
      return {
        windowHeight: document.documentElement.clientHeight - 35,
        list: [],
        loading: false,
      }
    },
    methods: {
      gotoIndex(index){
        this.$router.go(index)
      },
    },
    created(){
    }
  }
</script>

<style lang="less">
  @import './vetable.less';
  .breadcrumb{
  padding: 5px 0;
  padding-left: 13px;
}.ant-breadcrumb a{
  color:#5d90fa!important;
}.ant-breadcrumb{
  font-size: 12px !important;
}
</style>