<template>
  <div>
    <a-drawer :title="'在线编辑图表(' + chartTitle + ')'" placement="right" :visible="visible" :width="width"
      @close="onClose">
      <div class="navigation-wrapper">
        <template v-for="item in tabOptions">
          <div v-if="item.show" class="navigation" :style="{border:isShowContent === item.id ? '1px solid #1890ff' :'1px solid #ccc'}"
             @click="handleChangeValue('isShowContent',item.id)">
            <div><svg :t="item.svgObj.t" class="icon" viewBox="0 0 1024 1024" version="1.1"
                xmlns="http://www.w3.org/2000/svg" :p-id="item.svgObj.pId" width="15" height="15">
                <path :d="item.svgObj.pathT" :p-id="item.svgObj.pathId"
                  :fill="isShowContent === item.id ? '#1890ff' :'#ccc' "></path>
              </svg></div>
            <div :style="{color:isShowContent === item.id ? '#1890ff' :'#ccc'}">{{item.title}}</div>
          </div>
        </template>
        
      </div>
      <div class="form-wrapper" id="previewDrawer">
        <a-form :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-item v-if="isShowContent === 0">
            <span slot="label"><a-icon :type="showObj.isShowLegend ? 'up' : 'down'" class="mr10 "
                @click="handleShow('isShowLegend')" />
              图例</span>
            <br />
            <!-- 图例 -->
            <div class="form-block" style="align-items: flex-start;" v-show="showObj.isShowLegend && this.legendZip === 'false'">
              <span class="chli-title">
                <span style="color: #1890ff;cursor: pointer;"
                  @click="handleShow('isShowLegendData')">{{showObj['isShowLegendData']
                  ? '收起' : '展开'}}</span>
                数据</span>
              <div v-show="showObj.isShowLegendData">
                <div>
                  <a-checkbox :indeterminate="legendIndeterminate" :checked="checkAll" @change="onLegendAllChange">
                    全选
                  </a-checkbox>
                </div>
                <a-checkbox-group v-model="legendList" :options="legendOptions" @change="onLegendChange" />
              </div>
            </div>
             <!-- 图例名称 -->
             <div class="form-block" style="align-items: flex-start;" v-show="showObj.isShowLegend">
              <span class="chli-title">
                <span style="color: #1890ff;cursor: pointer;"
                  @click="handleShow('isShowLegendName')">{{showObj['isShowLegendName']
                  ? '收起' : '展开'}}</span>
                名称</span>
              <div v-show="showObj.isShowLegendName">
                <div class="sequence-item" v-for="(item,index) in legendEditNameList">
                  <div class="item-content mr10">{{item.originName}}</div>
                  <a-input size="small" style="width: 100px;" class="mr10" v-model="item.newName"
                    @blur="handleEditlegendName($event,index)" @pressEnter="handleEditlegendName($event,index)" />
                </div>
              </div>
            </div>
            <!-- 图例顺序 -->
            <div class="form-block" style="align-items: flex-start;" v-show="showObj.isShowLegend">
              <span class="chli-title">
                <span style="color: #1890ff;cursor: pointer;"
                  @click="handleShow('isShowLegendSort')">{{showObj['isShowLegendSort']
                  ? '收起' : '展开'}}</span>
                顺序</span>
              <div v-show="showObj.isShowLegendSort">
                <div class="sequence-item" v-for="(item,index) in legendSortList">
                  <div class="item-content">{{item}}</div>
                  <a-button type="link" :disabled="index === 0" @click="moveDataOne(index,'up')"><a-icon
                      type="arrow-up" /></a-button>
                  <a-button type="link" :disabled="index === legendSortList.length - 1"
                    @click="moveDataOne(index,'down')"><a-icon type="arrow-down" /></a-button>
                </div>
              </div>
            </div>

            <!-- 图例显隐 -->
            <div class="form-block" style="align-items: flex-start;" v-show="showObj.isShowLegend && legendRevealList && this.legendZip === 'false'">
              <span class="chli-title">
                <span style="color: #1890ff;cursor: pointer;"
                  @click="handleShow('isShowLegendHide')">{{showObj['isShowLegendHide']
                  ? '收起' : '展开'}}</span>
                显隐</span>
              <div v-show="showObj.isShowLegendHide">
                <div>
                  <a-checkbox :indeterminate="legendRevealIndeterminate" :checked="legendRevealcheckAll" :disabled = "legendList.length == 0"
                    @change="onLegendRevealAllChange">
                    全选
                  </a-checkbox>
                </div>
                <a-checkbox-group v-model="legendRevealList" :options="legendRevealOptions" @change="onLegendRevealChange" />
              </div>
            </div>

            <!-- 背景颜色 -->
            <div class="form-block" v-show="showObj.isShowLegend">
              <span class="chli-title">背景颜色</span>
              <input v-show="showObj.isShowLegendBgColor" :value="legendBgColor" type="color" class="mr10"
                @change="handleChangeLegendColor" />
                {{ showObj.isShowLegendBgColor ? legendBgColor : '' }}
              <a-button v-show="showObj.isShowLegendBgColor" class="ml10" size="small" @click="handleSubmit('legendBgColor', original.legendBgColor)">重置</a-button>
              <a-button v-show="!showObj.isShowLegendBgColor" class="ml-7" type="link"  size="small" @click="handleShow('isShowLegendBgColor')">修改颜色</a-button>
            </div>

            <!-- 图例朝向 -->
            <div class="form-block" style="align-items: flex-start;" v-show="showObj.isShowLegend">
              <span class="chli-title">图例朝向</span>
              <div>
                <a-radio-group v-model="legendOrient" @change="handleSubmit('','','','legendOrient')">
                  <a-radio value="horizontal">
                    水平
                  </a-radio>
                  <a-radio value="vertical">
                    垂直
                  </a-radio>
                </a-radio-group>
              </div>
            </div>

             <!-- 图例压缩 日历寿命独有的 -->
             <div class="form-block" style="align-items: flex-start;" v-show="calendarLife && showObj.isShowLegend">
              <span class="chli-title">图例压缩</span>
              <div>
                <a-radio-group v-model="legendZip" @change="handleLegendZip">
                  <a-radio value="true">
                    是
                  </a-radio>
                  <a-radio value="false">
                    否
                  </a-radio>
                </a-radio-group>
              </div>
            </div>

            <!-- 图例名称 日历寿命独有的 -->
            <div class="form-block" style="align-items: flex-start;" v-show="(calendarLife || legendNameTypeShow) && showObj.isShowLegend">
              <span class="chli-title">图例名称</span>
              <div>
                <a-radio-group v-model="legendNameType" @change="handleToggleLegendName">
                  <a-radio value="sampleCode">
                    样品编号
                  </a-radio>
                  <a-radio value="batteryCode">
                    电芯编码
                  </a-radio>
                </a-radio-group>
              </div>
            </div>

            

            <!-- 图例横坐标位置 -->
            <!-- <div class="form-block" style="align-items: flex-start;" v-show="showObj.isShowLegend">
              <span class="chli-title">水平位置</span>
              <div>
                <a-radio-group v-model="legendX" @change="handleSubmit">
                  <a-radio value="left">
                    左
                  </a-radio>
                  <a-radio value="center">
                    中
                  </a-radio>
                  <a-radio value="right">
                    右
                  </a-radio>
                </a-radio-group>
              </div>
            </div> -->

            <!-- 图例字号 日历寿命独有的 -->
            <div class="form-block" v-show="calendarLife && showObj.isShowLegend">
              <span class="chli-title">图例字号</span>
              <a-input-number class="mr10" v-model="legendFontSize" @blur="handleSubmit('','','','legendFontSize')" @pressEnter="handleSubmit('','','','legendFontSize')" />
              <a-button size="small" @click="handleSubmit('legendFontSize', original.legendFontSize)">重置</a-button>
            </div>

            <!-- 图例距离顶部位置 -->
            <div class="form-block" v-show="showObj.isShowLegend">
              <span class="chli-title">距顶部距离</span>
              <a-input-number class="mr10" v-model="legendTop" @blur="handleSubmit('','','','legendTop')" @pressEnter="handleSubmit('','','','legendTop')" />
              <a-button size="small" @click="handleSubmit('legendTop', original.legendTop)">重置</a-button>
            </div>

             <!-- 图例距离左边部位置 -->
             <div class="form-block" v-show="showObj.isShowLegend">
              <span class="chli-title">距左边距离</span>
              <a-input-number class="mr10" v-model="legendLeft" @blur="handleSubmit('','','','legendLeft')" @pressEnter="handleSubmit('','','','legendLeft')" />
              <a-button size="small" @click="handleSubmit('legendLeft', original.legendLeft)">重置</a-button>
            </div>

            <!-- 图例项宽度 -->
            <div class="form-block" v-show="showObj.isShowLegend && this.legendZip === 'false'">
              <span class="chli-title">图例项宽度</span>
              <a-input-number class="mr10" v-model="legendWidth" @blur="handleSubmit('','','','legendWidth')" @pressEnter="handleSubmit('','','','legendWidth')" />
              <a-button size="small" @click="handleSubmit('legendWidth', original.legendWidth)">重置</a-button>
            </div>
            <!-- 图例项高度 -->
            <div class="form-block" v-show="showObj.isShowLegend && this.legendZip === 'false'">
              <span class="chli-title">图例项高度</span>
              <a-input-number class="mr10" v-model="legendHeight" @blur="handleSubmit('','','','legendHeight')" @pressEnter="handleSubmit('','','','legendHeight')" />
              <a-button size="small" @click="handleSubmit('legendHeight', original.legendHeight)">重置</a-button>
            </div>
            <!-- 图例项间隔 -->
            <div class="form-block" v-show="showObj.isShowLegend && this.legendZip === 'false'">
              <span class="chli-title">图例项间隔</span>
              <a-input-number class="mr10" v-model="legendGap" :min="1" @blur="handleSubmit('','','','legendGap')" @pressEnter="handleSubmit('','','','legendGap')" />
              <a-button size="small" @click="handleSubmit('legendGap', original.legendGap)">重置</a-button>
            </div>
          </a-form-item>

          <a-radio-group v-show="isShowContent === 1" v-model="dataEditType">
            <a-radio-button value="all">
              全部修改
             </a-radio-button>
             <a-radio-button value="alone">
              个性化修改
            </a-radio-button>
          </a-radio-group>
          <a-form-item v-show="isShowContent === 1 && dataEditType === 'alone'" v-for="(item, index) in dataOptions">
            <span slot="label"><a-icon :type="showObj['isShowTag' + index] ? 'up' : 'down'" class="mr10"
                @click="handleShow('isShowTag' + index)" />
              {{ checkData[index].name }}</span>
            <br />
            <!-- 同步 -->
            <div class="form-block" style="align-items: flex-start;" v-show="showObj['isShowTag' + index]">
              <span class="chli-title" :id="'tag' + index">同步</span>
              <div>
                <a-select size="small" class="mr10" :disabled="checkData[index].disabled" v-model="checkData[index].synchronization">
                  <a-select-option v-for="(item, index) in dataOptions" :key="index">
                    图表标签{{ index + 1 }}
                  </a-select-option>
                </a-select>
                <a-button size="small" :disabled="checkData[index].disabled" @click="handleSynchronization(index)">同步</a-button>
              </div>
            </div>
            <!-- 图表标签 -->
            <!-- <div class="form-block" style="align-items: flex-start;" v-show="showObj['isShowTag' + index]">
              <span class="chli-title"> <span style="color: #1890ff;cursor: pointer;"
                  @click="handleShow('isShowData' + index)">{{showObj['isShowData' + index]
                  ? '收起' : '展开'}}</span>
                数据</span>
              <div v-show="showObj['isShowData' + index]">
                <div v-if="!isDuplicateData">
                  <a-checkbox :indeterminate="checkData[index].indeterminate" :checked="checkData[index].checkAll"
                    :disabled="checkData[index].disabled" @change="$event => onDataAllChange($event, index)">
                    全选
                  </a-checkbox>
                </div>
                <a-checkbox-group v-if="!isDuplicateData" v-model="checkData[index].checkedList" :options="item"
                  :disabled="checkData[index].disabled" @change="$event => onDataChange($event, index)" />

                <div v-if="isDuplicateData">
                  <a-checkbox :indeterminate="checkData[index].duplicateIndeterminate"
                    :disabled="checkData[index].disabled" :checked="checkData[index].duplicateCheckAll"
                    @change="$event => onDuplicateDataAllChange($event, index)">
                    全选
                  </a-checkbox>
                </div>
                <a-checkbox-group v-if="isDuplicateData" v-model="checkData[index].duplicateCheckedList"
                  :disabled="checkData[index].disabled" :options="duplicateDataOptions[index].data"
                  @change="$event => onDuplicateDataChange($event, index)" />
              </div>
            </div> -->

            <!-- 最大值 -->
            <div class="form-block" v-show="showObj['isShowTag' + index]">
              <span class="chli-title">最大值</span>
              <a-switch size="small" :checked="checkData[index].maxPoint" :disabled="checkData[index].disabled"
                @change="$event => handleChangePoint($event, index, 'maxPoint')" />
            </div>

            <!-- 最小值 -->
            <div class="form-block" v-show="showObj['isShowTag' + index]">
              <span class="chli-title">最小值</span>
              <a-switch size="small" :checked="checkData[index].minPoint" :disabled="checkData[index].disabled"
                @change="$event => handleChangePoint($event, index, 'minPoint')" />
            </div>

            <!-- 连接空值 -->
            <div class="form-block" style="align-items: flex-start;" v-show="showObj['isShowTag' + index]">
              <span class="chli-title">连接空值</span>
              <div>
                <a-radio-group size="small" v-model="checkData[index].connectNulls" :disabled="checkData[index].disabled" @change="handleSubmit('','','','connectNulls',index)">
                  <a-radio value="1">
                    连接
                  </a-radio>
                  <a-radio value="0">
                    不连接
                  </a-radio>
                </a-radio-group>
              </div>
            </div>

            <!-- 折点类型 -->
            <div class="form-block" v-show="showObj['isShowTag' + index]">
              <span class="chli-title">折点类型</span>
              <a-select size="small" class="mr10" :disabled="checkData[index].disabled" v-model="checkData[index].symbol"
                @change="handleSubmit('','','','symbol',index)">
                <a-select-option v-for="item in xySymbolOptions" :key="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
              <a-button size="small" :disabled="checkData[index].disabled"
                @click="handleSubmit('symbol',original.checkData ? original.checkData[index].symbol : original.series[index].symbol, index)">重置</a-button>
            </div>

            <!-- 折点大小 -->
            <div class="form-block" v-show="showObj['isShowTag' + index]">
              <span class="chli-title">折点大小</span>
              <a-input-number size="small" class="mr10" :disabled="checkData[index].disabled" v-model="checkData[index].symbolSize"
                @blur="handleSubmit('','','','symbolSize',index)" @pressEnter="handleSubmit('','','','symbolSize',index)" />
              <a-button size="small" :disabled="checkData[index].disabled"
                @click="handleSubmit('symbolSize',original.checkData ? original.checkData[index].symbolSize : original.series[index].symbolSize, index)">重置</a-button>
            </div>
            <!-- 折点颜色 -->
            <div class="form-block" v-show="showObj['isShowTag' + index]">
              <span class="chli-title">折点颜色</span>
              <input size="small" :value="checkData[index].itemColor" :disabled="checkData[index].disabled" type="color" class="mr10"
                @change="$event => handleChangeColor($event, index, 'itemColor')" />
              <span :style="{color:checkData[index].disabled ? '#ccc' : '#333'} " >{{ checkData[index].itemColor }}</span>
              <a-button class="ml10" size="small" :disabled="checkData[index].disabled"
                @click="handleSubmit('itemColor' , original.checkData ? original.checkData[index].itemColor : original.series[index].itemColor, index)">重置</a-button>
            </div>
            <!-- 折线类型 -->
            <div class="form-block" v-show="showObj['isShowTag' + index]">
              <span class="chli-title">折线类型</span>
              <a-select size="small" class="mr10" :disabled="checkData[index].disabled" v-model="checkData[index].lineType"
                @change="handleSubmit('','','','lineType',index)">
                <a-select-option v-for="item in lineTypeOptions" :key="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
              <a-button size="small" :disabled="checkData[index].disabled"
                @click="handleSubmit('lineType', original.checkData ? original.checkData[index].lineType : original.series[index].lineType, index)">重置</a-button>
            </div>
            <!-- 折线宽度 -->
            <div class="form-block" v-show="showObj['isShowTag' + index]">
              <span class="chli-title">折线宽度</span>
              <a-input-number size="small" class="mr10" :disabled="checkData[index].disabled" v-model="checkData[index].lineWidth"
                @blur="handleSubmit('','','','lineWidth',index)" @pressEnter="handleSubmit('','','','lineWidth',index)" />
              <a-button size="small" :disabled="checkData[index].disabled"
                @click="handleSubmit('lineWidth', original.checkData ? original.checkData[index].lineWidth : original.series[index].lineWidth, index)">重置</a-button>
            </div>
            <!-- 折线颜色 -->
            <div class="form-block" v-show="showObj['isShowTag' + index]">
              <span class="chli-title">折线颜色</span>
              <input size="small" :value="checkData[index].lineColor" :disabled="checkData[index].disabled" type="color" class="mr10"
                @change="$event => handleChangeColor($event, index, 'lineColor')" />
                <span :style="{color:checkData[index].disabled ? '#ccc' : '#333' }" >{{ checkData[index].lineColor }}</span>
              <a-button class="ml10" size="small" :disabled="checkData[index].disabled"
                @click="handleSubmit('lineColor', original.checkData ? original.checkData[index].lineColor : original.series[index].lineColor, index)">重置</a-button>
            </div>
          </a-form-item>
          <a-form-item v-show="isShowContent === 1 && dataEditType === 'all'">
            <span slot="label"><a-icon :type="showObj.isShowAllDataType ? 'up' : 'down'" class="mr10"
              @click="handleShow('isShowAllDataType')" />
            全部修改</span>
            <br />
            <!-- 连接空值 -->
            <div class="form-block" style="align-items: flex-start;" v-show="showObj.isShowAllDataType">
              <span class="chli-title">连接空值</span>
              <div>
                <a-radio-group size="small" v-model="allData.connectNulls" @change="handleEditAllData('connectNulls')">
                  <a-radio value="1">
                    连接
                  </a-radio>
                  <a-radio value="0">
                    不连接
                  </a-radio>
                </a-radio-group>
              </div>
            </div>

            <!-- 折点类型 -->
            <div class="form-block" v-show="showObj.isShowAllDataType">
              <span class="chli-title">折点类型</span>
              <a-select size="small" class="mr10" v-model="allData.symbol"
                @change="handleEditAllData('symbol')">
                <a-select-option v-for="item in xySymbolOptions" :key="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
              <a-button size="small" @click="handleEditAllData('symbol',1)">重置</a-button>
            </div>

            <!-- 折点大小 -->
            <div class="form-block" v-show="showObj.isShowAllDataType">
              <span class="chli-title">折点大小</span>
              <a-input-number size="small" class="mr10" v-model="allData.symbolSize"
                @blur="handleEditAllData('symbolSize')" @pressEnter="handleEditAllData('symbolSize')" />
              <a-button size="small" @click="handleEditAllData('symbolSize',1)">重置</a-button>
            </div>

            <!-- 折点颜色 -->
            <div class="form-block" v-show="showObj.isShowAllDataType">
              <span class="chli-title">折点颜色</span>
              <input size="small" :value="allData.itemColor" type="color" class="mr10"
                @change="$event => handleAllDataColor($event, 'itemColor')" />
              {{ allData.itemColor || 'Mixed' }}
              <a-button size="small" class="ml10" @click="handleEditAllData('itemColor',1)">重置</a-button>
            </div>
            <!-- 折线类型 -->
            <div class="form-block" v-show="showObj.isShowAllDataType">
              <span class="chli-title">折线类型</span>
              <a-select size="small" class="mr10" v-model="allData.lineType"
                @change="handleEditAllData('lineType')">
                <a-select-option v-for="item in lineTypeOptions" :key="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
              <a-button size="small" @click="handleEditAllData('lineType',1)">重置</a-button>
            </div>
            <!-- 折线宽度 -->
            <div class="form-block" v-show="showObj.isShowAllDataType">
              <span class="chli-title">折线宽度</span>
              <a-input-number size="small" class="mr10" v-model="allData.lineWidth"
                @blur="handleEditAllData('lineWidth')" @pressEnter="handleEditAllData('lineWidth')" />
              <a-button size="small" @click="handleEditAllData('lineWidth',1)">重置</a-button>
            </div>
            <!-- 折线颜色 -->
            <div class="form-block" v-show="showObj.isShowAllDataType">
              <span class="chli-title">折线颜色</span>
              <input size="small" :value="allData.lineColor"  type="color" class="mr10"
                @change="$event => handleAllDataColor($event, 'lineColor')" />
              {{ allData.lineColor || 'Mixed' }}
              <a-button size="small" class="ml10" @click="handleEditAllData('lineColor',1)">重置</a-button>
            </div>
          </a-form-item>

          <a-form-item v-if="isShowContent === 2">
            <span slot="label"><a-icon :type="showObj.isShowX ? 'up' : 'down'" class="mr10"
                @click="handleShow('isShowX')" />
              横坐标坐标轴</span>
            <br />
            <div class="form-block" v-show="showObj.isShowX">
              <span class="chli-title">类型</span>
              <a-select size="small" class="mr10" v-model="xType" @change="handleSubmit('','','','xType')">
                <a-select-option v-for="item in xyTypeOptions" :key="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
              <a-button size="small" @click="handleSubmit('xType', original.xType)">重置</a-button>
            </div>

            <div class="form-block" v-show="showObj.isShowX && xType === 'value'">
              <span class="chli-title">最小值</span>
              <a-input-number size="small" class="mr10" v-model="xMin" @blur="handleSubmit('','','','xMin')" @pressEnter="handleSubmit('','','','xMin')" />
              <a-button size="small" @click="handleSubmit('xMin', original.xMin)">重置</a-button>
            </div>

            <div class="form-block" v-show="showObj.isShowX && xType === 'value'">
              <span class="chli-title">最大值</span>
              <a-input-number size="small" class="mr10" v-model="xMax" @blur="handleSubmit('','','','xMax')" @pressEnter="handleSubmit('','','','xMax')" />
              <a-button size="small" @click="handleSubmit('xMax', original.xMax)">重置</a-button>
            </div>

            <div class="form-block" v-show="showObj.isShowX && xType === 'value'">
              <span class="chli-title">间隔值</span>
              <a-input-number size="small" class="mr10" v-model="xInterval" :min="0" @blur="handleSubmit('','','','xInterval')" @pressEnter="handleSubmit('','','','xInterval')" />
              <a-button size="small" @click="handleSubmit('xInterval', original.xInterval)">重置</a-button>
            </div>
          </a-form-item>
          <a-form-item v-if="isShowContent === 2">
            <span slot="label"><a-icon :type="showObj.isShowY ? 'up' : 'down'" class="mr10"
                @click="handleShow('isShowY')" />
              纵坐标坐标轴</span>
            <br />

            <div class="form-block" v-show="showObj.isShowY">
              <span class="chli-title">类型</span>
              <a-select size="small" class="mr10" v-model="yType" @change="handleSubmit('','','','yType')">
                <a-select-option v-for="item in xyTypeOptions" :key="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
              <a-button size="small" @click="handleSubmit('yType', original.yType)">重置</a-button>
            </div>

            <div class="form-block" v-show="showObj.isShowY && yType === 'value'">
              <span class="chli-title">最小值</span>
              <a-input-number size="small" class="mr10" v-model="yMin" @blur="handleSubmit('','','','yMin')" @pressEnter="handleSubmit('','','','yMin')" />
              <a-button size="small" @click="handleSubmit('yMin', original.yMin)">重置</a-button>
            </div>

            <div class="form-block" v-show="showObj.isShowY && yType === 'value'">
              <span class="chli-title">最大值</span>
              <a-input-number size="small" class="mr10" v-model="yMax" @blur="handleSubmit('','','','yMax')" @pressEnter="handleSubmit('','','','yMax')" />
              <a-button size="small" @click="handleSubmit('yMax', original.yMax)">重置</a-button>
            </div>

            <div class="form-block" v-show="showObj.isShowY && yType === 'value'">
              <span class="chli-title">间隔值</span>
              <a-input-number size="small" class="mr10" v-model="yInterval" :min="0" @blur="handleSubmit('','','','yInterval')" @pressEnter="handleSubmit('','','','yInterval')" />
              <a-button size="small" @click="handleSubmit('yInterval', original.yInterval)">重置</a-button>
            </div>

            <!-- 小数位数 日历寿命独有的 -->
            <div class="form-block" v-show="calendarLife && showObj.isShowY && yType === 'value'">
              <span class="chli-title">小数位数</span>
              <a-input-number size="small" class="mr10" v-model="yDecimalNum" @blur="handleSubmit('','','','yDecimalNum')" @pressEnter="handleSubmit('','','','yDecimalNum')" />
              <a-button size="small" @click="handleSubmit('yDecimalNum', original.yDecimalNum,'','yDecimalNum')">重置</a-button>
            </div>
          </a-form-item>
          <a-form-item v-if="isShowContent === 2 && yType2">
            <span slot="label"><a-icon :type="showObj.isShowY2 ? 'up' : 'down'" class="mr10"
                @click="handleShow('isShowY2')" />
              纵坐标副坐标轴</span>
            <br />

            <div class="form-block" v-show="showObj.isShowY2">
              <span class="chli-title">类型</span>
              <a-select size="small" class="mr10" v-model="yType2" @change="handleSubmit('','','','yType2')">
                <a-select-option v-for="item in xyTypeOptions" :key="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
              <a-button size="small" @click="handleSubmit('yType2', original.yType2)">重置</a-button>
            </div>

            <div class="form-block" v-show="showObj.isShowY2 && yType2 === 'value'">
              <span class="chli-title">最小值</span>
              <a-input-number size="small" class="mr10" v-model="yMin2" @blur="handleSubmit('','','','yMin2')" @pressEnter="handleSubmit('','','','yMin2')" />
              <a-button size="small" @click="handleSubmit('yMin2', original.yMin2)">重置</a-button>
            </div>

            <div class="form-block" v-show="showObj.isShowY2 && yType2 === 'value'">
              <span class="chli-title">最大值</span>
              <a-input-number size="small" class="mr10" v-model="yMax2" @blur="handleSubmit('','','','yMax2')" @pressEnter="handleSubmit('','','','yMax2')" />
              <a-button size="small" @click="handleSubmit('yMax2', original.yMax2)">重置</a-button>
            </div>

            <div class="form-block" v-show="showObj.isShowY2 && yType2 === 'value'">
              <span class="chli-title">间隔值</span>
              <a-input-number size="small" class="mr10" v-model="yInterval2" :min="0" @blur="handleSubmit('','','','yInterval2')" @pressEnter="handleSubmit('','','','yInterval2')" />
              <a-button size="small" @click="handleSubmit('yInterval2', original.yInterval2)">重置</a-button>
            </div>
          </a-form-item>

          <a-form-item v-if="isShowContent === 3">
            <span slot="label"><a-icon :type="showObj.isShowTitle ? 'up' : 'down'" class="mr10 "
                @click="handleShow('isShowTitle')" />
              标题</span>
            <br />
            <!-- 图表 -->
            <div class="form-block" v-show="showObj.isShowTitle">
              <span class="chli-title">图表标题</span>
              <a-input size="small" class="mr10" v-model="chartTitle" @blur="handleSubmit('','','','chartTitle')" @pressEnter="handleSubmit('','','','chartTitle')" />
              <a-button size="small" @click="handleSubmit('chartTitle', original.chartTitle)">重置</a-button>
            </div>
            <div class="form-block" v-show="showObj.isShowTitle">
              <span class="chli-title">X轴标题</span>
              <a-input size="small" class="mr10" v-model="XTitle" @blur="handleSubmit('','','','XTitle')" @pressEnter="handleSubmit('','','','XTitle')" />
              <a-button size="small" @click="handleSubmit('XTitle', original.XTitle)">重置</a-button>
            </div>
            <div class="form-block" v-show="showObj.isShowTitle && YTitle !== null  && YTitle !== undefined">
              <span class="chli-title">Y轴标题</span>
              <a-input size="small" class="mr10" v-model="YTitle" @blur="handleSubmit('','','','YTitle')" @pressEnter="handleSubmit('','','','YTitle')" />
              <a-button size="small" @click="handleSubmit('YTitle', original.YTitle)">重置</a-button>
            </div>
            <div class="form-block" v-show="showObj.isShowTitle && YTitle2 !== null  && YTitle2 !== undefined">
              <span class="chli-title">Y轴副标题</span>
              <a-input size="small" class="mr10" v-model="YTitle2" @blur="handleSubmit('','','','YTitle2')" @pressEnter="handleSubmit('','','','YTitle2')" />
              <a-button size="small" @click="handleSubmit('YTitle2', original.YTitle2)">重置</a-button>
            </div>
            <!-- 顶部标题距离顶部位置 -->
            <div class="form-block" v-show="showObj.isShowTitle">
              <span class="chli-title">距顶部距离</span>
              <a-input-number size="small" class="mr10" v-model="titleTop" @blur="handleSubmit('','','','titleTop')" @pressEnter="handleSubmit('','','','titleTop')" />
              <a-button size="small" @click="handleSubmit('titleTop', original.titleTop)">重置</a-button>
            </div>

            <!-- Y标题距左边距离位置 -->
            <div class="form-block" v-show="showObj.isShowTitle">
              <span class="chli-title">距左边距离</span>
              <a-input-number size="small" class="mr10" v-model="yTitleLetf" @blur="handleSubmit('','','','yTitleLetf')" @pressEnter="handleSubmit('','','','yTitleLetf')" />
              <a-button size="small" @click="handleSubmit('yTitleLetf', original.yTitleLetf)">重置</a-button>
            </div>

            <!-- Y标题距右边距离位置 -->
            <div class="form-block" v-show="showObj.isShowTitle && yTitleRight">
              <span class="chli-title">距右边距离</span>
              <a-input-number size="small" class="mr10" v-model="yTitleRight" :min="1" @change="yTitleRight = yTitleRight || 1" @blur="handleSubmit('','','','yTitleRight')" @pressEnter="handleSubmit('','','','yTitleRight')" />
              <a-button size="small" @click="handleSubmit('yTitleRight', original.yTitleRight)">重置</a-button>
            </div>
          </a-form-item>

          <a-form-item v-if="isShowContent === 4">
            <span slot="label"><a-icon :type="showObj.isShowGrid ? 'up' : 'down'" class="mr10"
                @click="handleShow('isShowGrid')" />
              图表位置</span>
            <br />
            <!-- 图例距离顶部位置 -->
            <div class="form-block" v-show="showObj.isShowGrid">
              <span class="chli-title">距顶部距离</span>
              <a-input-number size="small" class="mr10" v-model="gridTop" @blur="handleSubmit('','','','gridTop')" @pressEnter="handleSubmit('','','','gridTop')" />
              <a-button size="small" @click="handleSubmit('gridTop', original.gridTop)">重置</a-button>
            </div>

            <!-- 图例距离左边位置 -->
            <div class="form-block" v-show="showObj.isShowGrid">
              <span class="chli-title">距左边距离</span>
              <a-input-number size="small" class="mr10" v-model="gridLeft" @blur="handleSubmit('','','','gridLeft')" @pressEnter="handleSubmit('','','','gridLeft')" />
              <a-button size="small" @click="handleSubmit('gridLeft', original.gridLeft)">重置</a-button>
            </div>

            <!-- 图例距离右边位置 -->
            <div class="form-block" v-show="showObj.isShowGrid">
              <span class="chli-title">距右边距离</span>
              <a-input-number size="small" class="mr10" v-model="gridRight" @blur="handleSubmit('','','','gridRight')" @pressEnter="handleSubmit('','','','gridRight')" />
              <a-button size="small" @click="handleSubmit('gridRight', original.gridRight)">重置</a-button>
            </div>

            <!-- 图例距离底部位置 -->
            <div class="form-block" v-show="showObj.isShowGrid">
              <span class="chli-title">距底部距离</span>
              <a-input-number size="small" class="mr10" v-model="gridBottom" @blur="handleSubmit('','','','gridBottom')" @pressEnter="handleSubmit('','','','gridBottom')" />
              <a-button size="small" @click="handleSubmit('gridBottom', original.gridBottom)">重置</a-button>
            </div>
          </a-form-item>

          <a-form-item v-if="calendarLife &&  isShowContent === 5">
            <span slot="label"> 连线</span>
            <br />
            <!-- 连接空值 -->
            <div class="form-block" style="align-items: flex-start;">
              <span class="chli-title">连接空值</span>
              <div>
                <a-radio-group size="small" v-model="allData.connectNulls" @change="handleEditAllData('connectNulls')">
                  <a-radio value="1">
                    连接
                  </a-radio>
                  <a-radio value="0">
                    不连接
                  </a-radio>
                </a-radio-group>
              </div>
            </div>
          </a-form-item>

          <a-form-item v-if=" calendarLife &&  isShowContent === 6">
            <span slot="label"><a-icon :type="showObj.isShowColor ? 'up' : 'down'" class="mr10"
              @click="handleShow('isShowColor')" />
              颜色方案</span>
            <br />
            <!-- 颜色方案 -->
            <div class="form-block" style="align-items: flex-start;" v-show="showObj.isShowColor">
              <span class="chli-title">颜色方案</span>
              <div class="color-content">
                <div class="color-row" v-for="item in colorOptions" @click="handleColorOption(item.children)">
                  <div class="color-block" v-for="childItem in item.children" :style="{'background':childItem}"></div>
                </div>
              </div>
            </div>
          </a-form-item>


        </a-form>
      </div>
      <div class="btn-wrapper">
        <a-button class="mr10" type="primary" @click="handleSubmit">
          生成
        </a-button>
        <a-button class="mr10" @click="handleAllReset">
          重置
        </a-button>
        <a-button @click="onClose">
          关闭
        </a-button>
      </div>
    </a-drawer>
  </div>
</template>

<script>
  import _ from "lodash";
  export default {
    props: {
      width: {
        type: Number,
        default: 500
      },
      // isDuplicateData: {
      //   type: Boolean,
      //   default: false,
      // },
      calendarLife:{   // 日历寿命测试报告独有
        type: Boolean,
        default: false,
      },
      legendNameTypeShow: {
        type: Boolean,
        default: false,
      },
      LegendNameTypeList: {
        type: Array,
        default: () => ([]),
        required: false
      },
      legendOptions: {
        type: Array,
        default: []
      },
      data: {
        type: Array,
        default: []
      },
      original: {
        type: Object,
        default: {}
      },
      editData: {
        type: Object,
        default: {}
      },
      checkObj: {
        type: Object,
        default: {},
        required: false
      }

    },
    data() {
      return {
        showObj: {
          isShowLegend: true,       //图例
          isShowLegendData: false,  //图例底下的数据
          isShowLegendName: false,  //图例底下的名称
          isShowLegendSort: false,  //图例底下的顺序
          isShowLegendHide: false,  //图例底下的显隐
          isShowLegendBgColor: false,  //图例底下的背景颜色

          isShowAllDataType: true,       //图例


          isShowX: true,
          isShowY: true,
          isShowY2: true,
          isShowTitle: true,
          isShowGrid: true,

          isShowColor:true
        },

        dataEditType:'all',
        allData:{
          connectNulls:-1,
          symbol:'',
          symbolSize:'',
          itemColor:'',
          lineType:'',
          lineWidth:'',
          lineColor:''
        },


        rollObj: {},
        isShowContent: 3,  // 0: 图例  1:数据标签(个性化)  2:坐标轴 3:标题  4:图表位置   5:数据标签(全部)    6:颜色
        chartTitle: '', //图表标题
        XTitle: '', //X轴标题
        YTitle: '', //Y轴标题
        YTitle2: '', //Y轴副标题
        titleTop: '',
        yTitleLetf:'',
        yTitleRight:'',
        legendRevealOptions:[], // 图例--显隐的选择项
        legendWidth: 0,
        legendHeight: 0,
        legendGap: 0,
        legendOrient: '',//图例朝向
        legendZip:'', //图例压缩
        legendBgColor: '',//图例背景色
        // legendX: '',//图例横坐标位置
        legendTop: '',//图例距离顶部的位置
        legendFontSize:'',
        legendLeft:'',//图例距离左边的位置

        gridTop: '',
        gridLeft: '',
        gridRight: '',
        gridBottom: '',
        xMin: 0,
        xMax: 0,
        xInterval: 0,
        xType: "",
        yMin: 0,
        yMax: 0,
        yInterval: 0,
        yDecimalNum: 0,
        yType: 0,
        yMin2: 0,
        yMax2: 0,
        yInterval2: 0,
        yType2: 0,

        /*图例选中：线+图例一起去除*/
        legendIndeterminate: false,
        checkAll: false,

        /*图例显隐：只去除图例，线保留*/
        legendRevealIndeterminate: false,
        legendRevealcheckAll: false,

        checkData: [],
        visible: true,
        labelCol: {
          span: 9
        },
        wrapperCol: {
          span: 15
        },
        legendList: [], // 选择的图例数组
        legendRevealList: [], // 选中需要展示的图例数组

        legendSortList: [],
        legendEditNameList: [], // 图例改名数组
        dataTagList: [],
        dataOptions: [],
        duplicateDataOptions: [],//重复数据的   数据标签-数据的options
        orientOptions: [
          { label: '水平', value: 'horizontal' },
          { label: '垂直', value: 'vertical' }
        ],
        tabOptions: [
          {
            id: 3,
            title: "标题",
            svgObj: {
              t: '1711437379542',
              pId: "2992",
              pathT: 'M782.222222 128H241.777778c-31.288889 0-56.888889 25.6-56.888889 56.888889v42.666667c0 15.644444 12.8 28.444444 28.444444 28.444444s28.444444-12.8 28.444445-28.444444v-42.666667h241.777778v654.222222h-56.888889c-15.644444 0-28.444444 12.8-28.444445 28.444445s12.8 28.444444 28.444445 28.444444h184.888889c15.644444 0 28.444444-12.8 28.444444-28.444444s-12.8-28.444444-28.444444-28.444445h-71.111112V184.888889h241.777778v42.666667c0 15.644444 12.8 28.444444 28.444445 28.444444s28.444444-12.8 28.444444-28.444444v-42.666667c0-31.288889-25.6-56.888889-56.888889-56.888889z',
              pathId: '2993'
            },
            show:true
          },
          {
            id: 0,
            title: "图例",
            svgObj: {
              t: '1705036043101',
              pId: "4613",
              pathT: 'M908.49 562.043H754.526c-23.01 114.216-123.222 200.183-243.363 200.183S290.81 676.259 267.8 562.043H113.836c-27.431 0-49.667-22.405-49.667-50.044 0-27.64 22.236-50.044 49.667-50.044H267.8c23.01-114.216 123.222-200.183 243.363-200.183s220.353 85.967 243.363 200.183H908.49c27.431 0 49.666 22.404 49.666 50.044 0 27.64-22.236 50.044-49.666 50.044zM511.163 361.865c-82.291 0-148.998 67.218-148.998 150.135s66.707 150.135 148.998 150.135S660.16 594.917 660.16 512s-66.706-150.135-148.997-150.135z',
              pathId: '4614'
            },
            show:true
          },

          {
            id: 1,
            title: "数据标签",
            svgObj: {
              t: '1705305785161',
              pId: "9148",
              pathT: 'M25.6 537.1392a25.6 25.6 0 1 1 0-51.2h141.1072a25.6 25.6 0 0 0 24.5248-18.2272l118.1184-393.7792a51.2 51.2 0 0 1 98.0992 0L665.6 934.4l118.1184-393.728a76.8 76.8 0 0 1 73.5744-54.784H998.4a25.6 25.6 0 1 1 0 51.2h-141.1072a25.6 25.6 0 0 0-24.5248 18.2272l-118.1184 393.7792a51.2 51.2 0 0 1-98.0992 0L358.4 88.6272 240.2816 482.4064a76.8 76.8 0 0 1-73.5744 54.784H25.6z',
              pathId: '9149'
            },
            show:true
          },
          {
            id: 2,
            title: "坐标轴",
            svgObj: {
              t: '1705305858122',
              pId: "10172",
              pathT: 'M874.666667 682.666667H341.333333V149.333333c0-12.8-8.533333-21.333333-21.333333-21.333333h-42.666667c-12.8 0-21.333333 8.533333-21.333333 21.333333v533.333334H149.333333c-12.8 0-21.333333 8.533333-21.333333 21.333333v42.666667c0 12.8 8.533333 21.333333 21.333333 21.333333h106.666667v106.666667c0 12.8 8.533333 21.333333 21.333333 21.333333h42.666667c12.8 0 21.333333-8.533333 21.333333-21.333333v-106.666667h533.333334c12.8 0 21.333333-8.533333 21.333333-21.333333v-42.666667c0-12.8-8.533333-21.333333-21.333333-21.333333z" fill="#297AFF" p-id="10173"></path><path d="M531.2 128l-44.8 83.2L533.333333 298.666667h-53.333333l-21.333333-40.533334-17.066667 40.533334h-53.333333l46.933333-85.333334-44.8-85.333333h53.333333l19.2 38.4 17.066667-38.4h51.2zM896 471.466667l-49.066667 76.8V640h-51.2v-93.866667L746.666667 471.466667h51.2l21.333333 34.133333 23.466667-34.133333H896z',
              pathId: '10174'
            },
            show:true
          },
          {
            id: 4,
            title: "图表位置",
            svgObj: {
              t: '1714109544019',
              pId: "3883",
              pathT: 'M339.256541 585.016169l-23.25839-23.25839c-128.506263-128.469693-128.469693-336.807821 0.03657-465.314085C444.577554-32.135708 652.915681-32.135708 781.385375 96.370555c128.506263 128.506263 128.469693 336.807821-0.03657 465.350654l-23.33153 23.29496h83.123146a36.569796 36.569796 0 0 1 31.340315 17.772921l219.345637 365.66139c18.833445 31.340315-14.993616 67.873541-47.687014 51.490273l-273.468935-136.771037-201.682424 134.503709a36.569796 36.569796 0 0 1-40.592474 0L326.713101 883.169716l-273.468934 136.771037c-32.693398 16.383269-66.520459-20.113388-47.687014-51.490273l219.345636-365.69796a36.569796 36.569796 0 0 1 31.340315-17.736351h83.013437z m73.139592 73.139592H276.941609l-144.084996 240.26356L312.962858 808.238204a36.569796 36.569796 0 0 1 36.642935 2.303897L548.691763 943.32703l199.049399-132.748359a36.569796 36.569796 0 0 1 36.642936-2.303898l180.179385 90.107978-144.084996-240.22699h-135.600804l-110.367644 110.440784a36.569796 36.569796 0 0 1-51.709692 0l-110.440784-110.440784z m136.25906 32.839677l180.98392-180.983921a255.915432 255.915432 0 1 0-361.894701-361.894701 255.915432 255.915432 0 0 0-0.03657 361.931271l180.947351 180.947351z m-129.237659-232.657042a182.81241 182.81241 0 1 1 258.548458-258.475318 182.81241 182.81241 0 0 1-258.548458 258.475318z m51.709691-51.709692a109.709388 109.709388 0 1 0 155.092505-155.129075 109.709388 109.709388 0 0 0-155.092505 155.129075z',
              pathId: '3884'
            },
            show:true
          },
          {
            id: 6,
            title: "颜色",
            svgObj: {
              t: '1721979531527',
              pId: "5391",
              pathT: 'M465.499307 1021.354667c-21.504 0-44.117333-2.048-68.693334-5.12l-6.144-1.024c-174.336-26.624-293.290667-195.925333-298.410666-203.093334C-76.026027 555.776 8.11264 296.277333 167.08864 152.661333 325.04064 9.045333 588.635307-52.48 819.37664 133.205333c148.736 119.978667 193.877333 286.122667 195.925333 293.290667v2.048c21.504 116.906667 4.096 203.093333-52.309333 258.474667-86.186667 83.114667-228.693333 56.405333-248.234667 52.309333-26.709333-3.072-46.165333 5.12-60.501333 22.528-15.36 19.456-18.432 45.141333-13.312 60.501333 14.336 43.093333 16.384 75.861333 6.144 100.522667-29.781333 64.682667-90.282667 98.474667-181.589333 98.474667z m-65.621334-67.669334l6.144 1.024c99.498667 15.36 160-4.096 184.576-59.477333 0-1.024 5.12-14.336-8.192-55.381333-13.312-36.949333-3.072-85.162667 23.552-117.930667 27.733333-34.901333 68.693333-50.261333 116.906667-45.141333l3.072 1.024c1.024 0 128.170667 27.648 193.877333-35.925334 40.021333-38.997333 52.309333-106.666667 34.901334-201.045333-4.096-13.312-48.213333-157.952-175.36-259.498667C578.309973 17.322667 347.56864 71.68 208.04864 197.802667 79.877973 314.709333-14.500693 537.258667 143.451307 778.325333c1.024 0 108.714667 153.856 256.426666 175.36z m0 0" p-id="5392"></path><path d="M158.89664 538.282667c0 33.962667 27.562667 61.525333 61.525333 61.525333s61.525333-27.562667 61.525334-61.525333-27.562667-61.525333-61.525334-61.525334c-34.048 0-61.525333 27.562667-61.525333 61.525334z m71.765333-184.576c0 33.962667 27.562667 61.525333 61.525334 61.525333s61.525333-27.562667 61.525333-61.525333-27.562667-61.525333-61.525333-61.525334-61.525333 27.562667-61.525334 61.525334z m184.576-102.570667c0 33.962667 27.562667 61.525333 61.525334 61.525333s61.525333-27.562667 61.525333-61.525333-27.562667-61.525333-61.525333-61.525333-61.525333 27.562667-61.525334 61.525333z m205.141334 51.285333c0 33.962667 27.562667 61.525333 61.525333 61.525334s61.525333-27.562667 61.525333-61.525334-27.562667-61.525333-61.525333-61.525333-61.525333 27.562667-61.525333 61.525333z m102.570666 164.096c0 33.962667 27.562667 61.525333 61.525334 61.525334s61.525333-27.562667 61.525333-61.525334-27.562667-61.525333-61.525333-61.525333-61.525333 27.562667-61.525334 61.525333z m0 0',
              pathId: '5393'
            },
            show:this.calendarLife
          },
          {
            id: 5,
            title: "连线",
            svgObj: {
              t: '1716880286112',
              pId: "4259",
              pathT: 'M1024.00324 511.1c0.5 71.1-57 128.9-128 128.9-55.7 0-103.1-35.6-120.7-85.3-2.3-6.4-8.3-10.7-15.1-10.7H263.80324c-6.8 0-12.8 4.3-15.1 10.7-17.6 49.7-65 85.3-120.7 85.3C57.00324 640-0.49676 582.2 0.00324 511.1 0.50324 440.6 59.90324 382.7 130.40324 384c54.7 1 101.1 36.4 118.4 85.4 2.2 6.4 8.3 10.6 15 10.6h496.4c6.7 0 12.8-4.2 15-10.6 17.3-49.1 63.6-84.4 118.4-85.4 70.5-1.3 129.9 56.6 130.4 127.1z',
              pathId: '3884'
            },
            show:this.calendarLife
          },
        ],
        xyTypeOptions: [
          {
            key: "value",
            value: "数值轴"
          },
          {
            key: "category",
            value: "类目轴"
          },
          // {
          //   key: "time",
          //   value: "时间轴"
          // },
          // {
          //   key: "log",
          //   value: "对数轴"
          // }
        ],
        xySymbolOptions: [
          {
            key: "emptyCircle",
            value: "空心圆"
          },
          {
            key: "circle",
            value: "实心圆"
          },
          {
            key: "rect",
            value: "方块"
          },
          {
            key: "roundRect",
            value: "圆角方块"
          },
          {
            key: "triangle",
            value: "三角形"
          },
          {
            key: "diamond",
            value: "菱形"
          },
          {
            key: "pin",
            value: "图钉"
          },
          {
            key: "arrow",
            value: "箭头"
          },
          {
            key: "none",
            value: "空"
          }
        ],
        lineTypeOptions: [
          {
            key: "solid",
            value: "实线"
          },
          {
            key: "dashed",
            value: "虚线"
          },
          {
            key: "dotted",
            value: "圆点线"
          }
        ],
        colorOptions:[
          {
            id:1,
            children:['#d87c7c','#919e8b','#d7ab82','#6e7074','#61a0a8','#efa18d','#787464','#cc7e63','#724e58','#4b565b']
          },
          {
            id:2,
            children:['#dd6b66','#759aa0','#e69d87','#8dc1a9','#ea7e53','#eedd78','#73a373','#73b9bc','#7289ab','#91ca8c']
          },
          {
            id:3,
            children:['#c1232b','#27727b','#fcce10','#e87c25','#b5c334','#fe8463','#9bca63','#fad860','#f3a43b','#60c0dd']
          },
          {
            id:4,
            children:['#2ec7c9','#b6a2de','#5ab1ef','#ffb980','#d87a80','#8d98b3','#e5cf0d','#97b552','#95706d','#dc69aa']
          },
          {
            id:5,
            children:['#e01f54','#001852','#f5e8c8','#b8d2c7','#c6b38e','#a4d8c2','#f3d999','#d3758f','#dcc392','#2e4783']
          },
          {
            id:6,
            children:['#fc97af','#87f7cf','#f7f494','#72ccff','#f7c5a0','#d4a4eb','#d2f5a6','#76f2f2']
          },
         
          {
            id:7,
            children:['#c12e34','#e6b600','#0098d9','#2b821d','#005eaa','#339ca8','#cda819','#32a487']
          },
          {
            id:8,
            children:['#418ab3','#a6b727','#f69200','#838383','#fec306','#df5327']
          },
          {
            id:9,
            children:['#516b91','#59c4e6','#edafda','#93b7e3','#a5e7f0','#cbb0e3']
          },
          {
            id:10,
            children:['#893448','#d95850','#eb8146','#ffb248','#f2d643','#ebdba4']
          },
          {
            id:11,
            children:['#4ea397','#22c3aa','#7bd9a5','#d0648a','#f58db2','#f2b3c9']
          },
          {
            id:12,
            children:['#3fb1e3','#6be6c1','#626c91','#a0a7e6','#c4ebad','#96dee8']
          },
         
          {
            id:13,
            children:['#8a7ca8','#e098c7','#8fd3e8','#71669e','#cc70af','#7cb4cc']
          },

          
        ]


      }
    },
    watch: {
      checkObj: {
        handler(newVal, oldVal) {
          switch (newVal.editObj) {
            case "title":
              this.isShowContent = 3
              break;
            case "legend":
              this.isShowContent = 0
              break;
            case "tag":
              this.isShowContent = 1
              this.showObj["isShowTag" + newVal.tag] = true

              this.$nextTick(() => {
                // 存储最开始的原始值
                if (JSON.stringify(this.rollObj) == '{}') {
                  this.dataOptions.forEach((v, index) => {
                    this.rollObj['tag' + index] = document.getElementById('tag' + index).getBoundingClientRect().top
                  })
                }
                // 目前的位置
                const tagTop = document.getElementById('tag' + newVal.tag).getBoundingClientRect().top
                // 与原始值的滚动范围
                const changeTop = this.rollObj['tag' + newVal.tag] - tagTop
                // 设置最后的位置
                document.getElementsByClassName('form-wrapper')[0].scrollTop = tagTop + changeTop - 55 - 24 - 55 - 40
              })

              this.$forceUpdate()
              break;
            case "axis":
              this.isShowContent = 2
              break;
            case "position":
              this.isShowContent = 4
              break;
          }
        },
        immediate: true,
        deep: true // 开启深度监听，默认是false
      },
    },
    created() {
      // 初始化
      this.init()

      // 图例排序
      this.legendSortList = this.editData.legendSort

      // 图例数据是否全选中
      this.checkAll = this._handleCheckAll(this.legendList, this.legendOptions)
      // this.checkAll = this.legendList.length === this.legendOptions.length  

      // 图例显隐是否全选中
      if(this.legendRevealList){
        this.legendRevealcheckAll = this.legendRevealList.length === this.legendOptions.length
        this.legendRevealOptions = this.legendOptions.map(mapItem => {
        {return { label:mapItem,value:mapItem,disabled: this.legendList.includes(mapItem) ? false : true }}
      })
      }
      
      this.dataOptions = this.data.map(v => v.data)

      // 展示数据，只需要获取一次，不会改变
      // if (this.isDuplicateData) {
      //   this.editData.duplicateDataOptions.forEach(forItem => {
      //     this.duplicateDataOptions.push({ id: forItem.id, data: forItem.data.filter(filterItem => filterItem.label !== '') })
      //   })
      // }

      this.data.forEach((v, index) => {
        this.checkData.push({
          name: this.editData.series[index].name,

          // checkAll: this.editData.series[index].data.length === this.data[index].data.length,
          // indeterminate: false,
          // checkedList: this.editData.series[index].data,

          // duplicateCheckAll: this.isDuplicateData && this.editData.duplicateCheckedList[index] ? this.editData.duplicateCheckedList[index].length === this.data[index].data.length : false,
          // duplicateIndeterminate: false,
          // duplicateCheckedList: this.isDuplicateData ? this.editData.duplicateCheckedList[index] : [],

          id: v.id,
          index:v.index,
          soc: v.soc,
          disabled: this.legendList.includes(v.soc) ? false : true,
          maxPoint: v.maxPoint,
          minPoint: v.minPoint,
          connectNulls: v.connectNulls ? '1' : '0',
          symbol: v.symbol,
          symbolSize: v.symbolSize,
          itemColor: v.itemColor,
          lineType: v.lineType,
          lineWidth: v.lineWidth,
          lineColor: v.lineColor,
          synchronization: v.synchronization
        })

        this.showObj["isShowTag" + index] = false  //展示大标签
        this.showObj["isShowData" + index] = false //展示大标签里面的数据

      })

    },
    methods: {
      init() {

        this.chartTitle = this.editData.chartTitle
        this.XTitle = this.editData.XTitle
        this.YTitle = this.editData.YTitle
        this.YTitle2 = this.editData.YTitle2
        this.titleTop = this.editData.titleTop
        this.yTitleLetf = this.editData.yTitleLetf 
        this.yTitleRight = this.editData.yTitleRight 
        this.legendWidth = this.editData.legendWidth
        this.legendHeight = this.editData.legendHeight
        this.legendGap = this.editData.legendGap
        this.legendBgColor = this.editData.legendBgColor
        this.legendOrient = this.editData.legendOrient
        this.legendZip = this.editData.legendZip || 'false'
        this.legendFontSize = this.editData.legendFontSize
        this.legendTop = this.editData.legendTop
        this.legendLeft = this.editData.legendLeft
        this.legendNameType = this.editData.legendNameType

        this.gridTop = this.editData.gridTop
        this.gridLeft = this.editData.gridLeft
        this.gridRight = this.editData.gridRight
        this.gridBottom = this.editData.gridBottom

        this.xMin = this.editData.xMin
        this.xMax = this.editData.xMax
        this.xInterval = this.editData.xInterval
        this.xType = this.editData.xType
        this.yMin = this.editData.yMin
        this.yMax = this.editData.yMax
        this.yInterval = this.editData.yInterval
        this.yDecimalNum = this.editData.yDecimalNum
        this.yType = this.editData.yType
        this.yMin2 = this.editData.yMin2
        this.yMax2 = this.editData.yMax2
        this.yInterval2 = this.editData.yInterval2
        this.yType2 = this.editData.yType2

        this.legendList = this.editData.legend
        this.legendRevealList = this.editData.legendRevealList

        // 图例改名,每次修改后都获取新的值，因为父组件需要进行操作
        this.legendEditNameList = this.editData.legendEditName
      },


      // 切换图例名称事件 // 样品编号 电芯编号 //目前只有日历寿命测试报告才有
      handleToggleLegendName(e){
        this.legendSortList.forEach((v,index) => {   //修改图例排序数组
          const temLegendSortIndex = this.LegendNameTypeList.findIndex(findItem => v == findItem.sampleCode || v == findItem.batteryCode)
          this.legendSortList[index] = this.LegendNameTypeList[temLegendSortIndex][this.legendNameType]
        })
        
        this.legendEditNameList.forEach((v,index) => {  //修改图例名称数组
          const temLegendEditNameIndex = this.LegendNameTypeList.findIndex(findItem => v.originName == findItem.sampleCode || v.originName == findItem.batteryCode)
          this.legendEditNameList[index].originName = this.LegendNameTypeList[temLegendEditNameIndex][this.legendNameType]
        })

        this.legendList.forEach((v,index) => {  //修改图例数组
          const temlegendEditIndex = this.LegendNameTypeList.findIndex(findItem => v == findItem.sampleCode || v == findItem.batteryCode)
          if(temlegendEditIndex !== -1) this.legendList[index] = this.LegendNameTypeList[temlegendEditIndex][this.legendNameType]
        })

        this.legendRevealList.forEach((v,index) => {  //修改图例名称数组
          const temlegendRevealIndex = this.LegendNameTypeList.findIndex(findItem => v == findItem.sampleCode || v == findItem.batteryCode)
          if(temlegendRevealIndex !== -1) this.legendRevealList[index] = this.LegendNameTypeList[temlegendRevealIndex][this.legendNameType]
        })

        this.checkData.forEach((v,index) => {
          const temCheckDataIndex = this.LegendNameTypeList.findIndex(findItem => v.soc == findItem.sampleCode || v.soc == findItem.batteryCode)
          this.checkData[index].id = this.LegendNameTypeList[temCheckDataIndex][this.legendNameType] + v.index
          this.checkData[index].soc = this.LegendNameTypeList[temCheckDataIndex][this.legendNameType]

          // 如果name不等于上一个类型的值，说明他被改名字了，不更改他的值
          this.checkData[index].name = this.checkData[index].name !==  this.LegendNameTypeList[temCheckDataIndex][this.legendNameType == 'sampleCode' ? 'batteryCode' : 'sampleCode'] ? this.checkData[index].name : this.LegendNameTypeList[temCheckDataIndex][this.legendNameType]
        })
        this.legendRevealOptions.forEach((v,index) => {
          const temlegendRevealIndex2 = this.LegendNameTypeList.findIndex(findItem => v.value == findItem.sampleCode || v.value == findItem.batteryCode)
          this.legendRevealOptions[index].value = this.LegendNameTypeList[temlegendRevealIndex2][this.legendNameType]
          this.legendRevealOptions[index].label = this.LegendNameTypeList[temlegendRevealIndex2][this.legendNameType]
        })

        this.handleSubmit()
        this.$forceUpdate()
      },

      // 图例压缩事件 //目前只有日历寿命测试报告才有
      handleLegendZip(){
        if(this.legendZip === 'true'){
          this.legendWidth = 3
          this.legendHeight = 1
          this.legendGap = 1

          this.legendList = _.cloneDeep(this.legendOptions) 
        }else{
          this.legendWidth = this.original.legendWidth
          this.legendHeight = this.original.legendHeight
          this.legendGap = this.original.legendGap

          this.legendList = _.cloneDeep(this.legendOptions).slice(0,10)
        }
        this.legendRevealList = _.cloneDeep(this.legendOptions)
        this.legendRevealOptions.forEach((v,index) => {
          v.disabled = index >= 10
        })
      
        this.handleSubmit()
      },
      
      /* 图例数据全选事件 */
      onLegendAllChange(e) {
        Object.assign(this, {
          legendList: e.target.checked ? this.legendOptions : [],
          legendIndeterminate: false,
          checkAll: e.target.checked
        })
        // 图表标签的显隐
        this.checkData.forEach(v => {
          v.disabled = !e.target.checked
        })

        this.legendRevealOptions.forEach(v => {
          v.disabled = !e.target.checked
        })

        this.handleSubmit('','','','legendList')
      },
      /* 图例数据选择事件 */
      onLegendChange(checkedList) {
        this.legendIndeterminate = !!this.legendList.length && this.legendList.length < this.legendOptions.length
        this.checkAll = this.legendList.length === this.legendOptions.length

        const newCheckedList = []

        // 由于缩略图版的不会修改原图例的数组名称，故需与其他的同步
        checkedList.forEach(v => {
          const newName = this.legendEditNameList.filter(filterItem => filterItem.originName === v && filterItem.newName)
          newCheckedList.push(newName.length === 0 ? v : newName[0].newName)
        })
        this.checkData.forEach(v => {
          v.disabled = !newCheckedList.includes(v.name)
        })
        this.legendRevealOptions.forEach(v => {
          if(checkedList.includes(v.value)){
            v.disabled = false
          }else{
            const have = this.legendEditNameList.find(findItem => findItem.originName == v.value).newName
            v.disabled = !checkedList.includes(have)
          }
        })

        

        this.handleSubmit('','','','legendList')
      },

      /* 图例显隐全选事件 */
      onLegendRevealAllChange(e) {
        
        Object.assign(this, {
          legendRevealList: e.target.checked ? this.legendOptions : [],
          legendRevealIndeterminate: false,
          legendRevealcheckAll: e.target.checked
        })

        // 增加改名称的
        const have = this.legendEditNameList.filter(filterItem => filterItem.newName)
        
        if(e.target.checked && have.length > 0){
          have.forEach(forItem => {
            this.legendRevealList.push(forItem.originName)
          })
        }
        this.handleSubmit('','','','legendRevealList')
      },
      /* 图例显隐选择事件 */
      onLegendRevealChange(checkedList) {
        this.legendRevealIndeterminate = !!this.legendRevealList.length && this.legendRevealList.length < this.legendOptions.length
        this.legendRevealcheckAll = this.legendRevealList.length === this.legendOptions.length

        this.handleSubmit('','','','legendRevealList')
      },
      onDataAllChange(e, index) {
        this.checkData[index].indeterminate = false
        this.checkData[index].checkedList = e.target.checked ? this.dataOptions[index] : []
        this.checkData[index].checkAll = e.target.checked

        this.handleSubmit()
      },
      onDataChange(checkedList, index) {
        this.checkData[index].indeterminate =
          !!this.checkData[index].checkedList.length &&
          this.checkData[index].checkedList.length < this.dataOptions[index].length
        this.checkData[index].checkAll = this.checkData[index].checkedList.length === this.dataOptions[index].length

        this.handleSubmit()
      },
      onDuplicateDataAllChange(e, index) {
        this.checkData[index].duplicateIndeterminate = false
        this.checkData[index].duplicateCheckedList = e.target.checked ? this.duplicateDataOptions[index].data.filter(filterItem => filterItem.label !== '').map(mapItem => mapItem.id) : []
        this.checkData[index].duplicateCheckAll = e.target.checked

        this.handleSubmit()
      },
      onDuplicateDataChange(checkedList, index) {
        this.checkData[index].duplicateIndeterminate =
          !!this.checkData[index].duplicateCheckedList.length &&
          this.checkData[index].duplicateCheckedList.length < this.duplicateDataOptions[index].length
        this.checkData[index].duplicateCheckAll = this.checkData[index].duplicateCheckedList.length === this.duplicateDataOptions[index].data.length

        this.handleSubmit()
      },
      // 数据移动
      moveDataOne(index, action) {

        const m = action === 'up' ? index : index + 1
        const n = action === 'up' ? index - 1 : index

        const tmp1 = this.legendSortList[m]
        this.legendSortList[m] = this.legendSortList[n]
        this.legendSortList[n] = tmp1

        this.$forceUpdate()

        this.handleSubmit('','','','legendSort')
      },
      handleChangeColor(e, index, target) {
        this.checkData[index][target] = e.target.value

        this.handleSubmit('','','',target,index)
      },
      handleChangeLegendColor(e){
        this.legendBgColor = e.target.value

        this.handleSubmit('','','','legendBgColor')
      },
      // 全部修改，颜色修改
      handleAllDataColor(e,target){
        this.allData[target] = e.target.value

        this.handleEditAllData(target)
      },
      handleChangePoint(e, index, target) {
        this.checkData[index][target] = e
        this.handleSubmit('','','',target,index)
      },
      handleChangeValue(target, val) {
        this[target] = val
      },
      handleShow(target) {
        this.showObj[target] = !this.showObj[target]
        this.$forceUpdate()
      },
      handleEditlegendName({ target }, index) {
        // 如果有值,记录修改值
        if (target._value) {
          if(!this.legendList.includes(target._value)) this.legendList.push(target._value)
        
          this.checkData.forEach(v => {
            if (v.name === this.legendEditNameList[index].originName || v.name === this.legendEditNameList[index].previousName) {
              v.name = target._value
            }
          })

          this.legendEditNameList[index].previousName = target._value

        } else {
          // 如果没有值,说明用户进行清空操作，需判断是否为误判（通过previousName是否有值进行判断）(传到父组件时，父组件进行一个还原操作，此时需要将previousName置空)，
          this.legendEditNameList[index].isReset = this.legendEditNameList[index].previousName ? true : false

          this.checkData.forEach(v => {
            if (v.name === this.legendEditNameList[index].previousName) {
              v.name = this.legendEditNameList[index].originName
            }
          })

          const haveIndex = this.legendList.findIndex(findItem => findItem === this.legendEditNameList[index].previousName)
          if(haveIndex > -1) this.legendList.splice(haveIndex,1)

          if(!this.legendList.includes(this.legendEditNameList[index].originName)) this.legendList.push(this.legendEditNameList[index].originName)

        }

        this.handleSubmit('','','','legendEditName',index)
      },
      _handleCheckAll(targetArr,templateArr){
        if(targetArr.length === 0) return false
        if(targetArr.length < templateArr.length  - 1) return false

        for(let i = 0 ; i < templateArr.length - 1 ;i++){
          
          // 如果包含就是有，如果没包含就是有没选中的情况
          const have =  targetArr.includes(templateArr[i])
          if(!have) return false
        }
        return true
      },
      // 默认颜色方案
      handleColorOption(colorList){

        this.checkData.forEach((v,index) => {
          v.itemColor = colorList[index % colorList.length]
          v.lineColor = colorList[index % colorList.length]
        })

        this.handleSubmit()
      },

      /*生成*/
      handleSubmit(resetObj, resetValue, index,targetEditObj = '',targetEditIndex = '') {

        // resetObj : 需要重置的对象
        // resetValue : 重置的数值
        // index : 需要重置的那条线

        // targetEditObj : 需要修改的对象
        // targetEditIndex : 需要修改的数据标签的位置

        // 颜色点击重置
        if(resetObj == "legendBgColor"){
          this.showObj.isShowLegendBgColor = false
        }

        // 如果坐标轴类型为类目轴，应去除最大值最小值，以及间距
        if (resetObj == "xType" || resetObj == "yType" || resetObj == "yType2" || resetObj == "chartTitle" || resetObj == "XTitle" || resetObj == "YTitle" || resetObj == "YTitle2") {
          this[resetObj] = resetValue

          if(resetObj == "xType" && resetValue == 'value'){
            this.xMin = this.original.xMin
            this.xMax = this.original.xMax
            this.xInterval = this.original.xInterval
          }

          if(resetObj == "yType" && resetValue == 'value'){
            this.yMin = this.original.yMin
            this.yMax = this.original.yMax
            this.yInterval = this.original.yInterval
            this.yDecimalNum = this.original.yDecimalNum
          }

          if(resetObj == "yType2" && resetValue == 'value'){
            this.yMin2 = this.original.yMin2
            this.yMax2 = this.original.yMax2
            this.yInterval2 = this.original.yInterval2
          }
        }

        if (
          resetObj == "symbol" ||
          resetObj == "symbolSize" ||
          resetObj == "itemColor" ||
          resetObj == "lineType" ||
          resetObj == "lineColor" ||
          resetObj == "lineWidth"
        ) {
          this.checkData[index][resetObj] = resetValue
        }
        const params = {
          chartTitle: this.chartTitle,
          XTitle: this.XTitle,
          YTitle: this.YTitle,
          YTitle2: this.YTitle2,
          titleTop: resetObj !== "titleTop" ? this.titleTop : resetValue,
          yTitleLetf: resetObj !== "yTitleLetf" ? this.yTitleLetf : resetValue,
          yTitleRight: resetObj !== "yTitleRight" ? this.yTitleRight : resetValue,
          legendList: this.legendList,
          legendRevealList:this.legendRevealList,
          legendSort: this.legendSortList,
          legendEditName: this.legendEditNameList,
          legendWidth: resetObj !== "legendWidth" ? this.legendWidth : resetValue,
          legendHeight: resetObj !== "legendHeight" ? this.legendHeight : resetValue,
          legendGap: resetObj !== "legendGap" ? this.legendGap : resetValue,
          legendOrient: resetObj !== "legendOrient" ? this.legendOrient : resetValue,
          legendZip: resetObj !== "legendZip" ? this.legendZip : resetValue,
          legendBgColor: resetObj !== "legendBgColor" ? this.legendBgColor : resetValue,
          legendFontSize: resetObj !== "legendFontSize" ? this.legendFontSize : resetValue,
          legendTop: resetObj !== "legendTop" ? this.legendTop : resetValue,
          legendLeft: resetObj !== "legendLeft" ? this.legendLeft : resetValue,
          legendNameType: resetObj !== "legendNameType" ? this.legendNameType : resetValue,

          gridTop: resetObj !== "gridTop" ? this.gridTop : resetValue,
          gridLeft: resetObj !== "gridLeft" ? this.gridLeft : resetValue,
          gridRight: resetObj !== "gridRight" ? this.gridRight : resetValue,
          gridBottom: resetObj !== "gridBottom" ? this.gridBottom : resetValue,

          checkData: this.checkData.map(v => {
            return {
              name: v.name,
              id: v.id,
              index:v.index,
              soc: v.soc,
              data: v.checkedList,
              duplicateData: v.duplicateCheckedList,
              maxPoint: v.maxPoint,
              minPoint: v.minPoint,
              connectNulls:v.connectNulls,
              symbol: v.symbol,
              symbolSize: v.symbolSize,
              itemColor: v.itemColor,
              lineType: v.lineType,
              lineColor: v.lineColor,
              lineWidth: v.lineWidth,
              synchronization: v.synchronization
            }
          }),

          xMin: this.xType !== "category" ? (resetObj !== "xMin" ? this.xMin : resetValue) : "",
          xMax: this.xType !== "category" ? (resetObj !== "xMax" ? this.xMax : resetValue) : "",
          xInterval: this.xType !== "category" ? (resetObj !== "xInterval" ? this.xInterval : resetValue) : "",
          xType: resetObj !== "xType" ? this.xType : resetValue,
          yMin: this.yType !== "category" ? (resetObj !== "yMin" ? this.yMin : resetValue) : "",
          yMax: this.yType !== "category" ? (resetObj !== "yMax" ? this.yMax : resetValue) : "",
          yInterval: this.yType !== "category" ? (resetObj !== "yInterval" ? this.yInterval : resetValue) : "",
          yDecimalNum: this.yType !== "category" ? (resetObj !== "yDecimalNum" ? this.yDecimalNum : resetValue) : "",
          yType: resetObj !== "yType" ? this.yType : resetValue,
          yMin2: this.yType2 !== "category" ? (resetObj !== "yMin2" ? this.yMin2 : resetValue) : "",
          yMax2: this.yType2 !== "category" ? (resetObj !== "yMax2" ? this.yMax2 : resetValue) : "",
          yInterval2: this.yType2 !== "category" ? (resetObj !== "yInterval2" ? this.yInterval2 : resetValue) : "",
          yType2: resetObj !== "yType2" ? this.yType2 : resetValue
        }

        if(targetEditObj) params.targetEditObj = targetEditObj
        if(targetEditIndex !== '') params.targetEditIndex = targetEditIndex
        if(resetObj) params.targetResetObj = resetObj
        if(index !== '') params.targetResetIndex = index

        if(targetEditIndex == 'all') params.allData = this.allData

        if(targetEditIndex == 'allReset'){
          delete params.allData
          delete params.targetEditObj
          delete params.targetEditIndex
          params.targetResetObj = targetEditObj
          params.targetResetIndex = 'all'
        } 

        

        this.$emit("submit",params)

        // 初始化
        this.init()
      },
      // 图表标签--全部修改
      handleEditAllData(targetEdit,isRest = 0){
        // targetEdit:编辑对象
        this.checkData.forEach((v,index) => {
          v[targetEdit] = isRest ? this.original.checkData ? this.original.checkData[index][targetEdit] : this.original.series[index][targetEdit]   : this.allData[targetEdit]
        })

        if(isRest) this.allData[targetEdit] = ''

        this.handleSubmit('','','',targetEdit,isRest ?'allReset' : 'all')
      },
      // 图标标签--同步
      handleSynchronization(original) {
        if (original === this.checkData[original].synchronization) return

        for (let key in this.checkData[this.checkData[original].synchronization]) {
          if (key === "checkAll" || key === "checkedList" || key === "indeterminate" || key === "id" || key === "name" || key === "soc" || key === "disabled" || key === 'synchronization' || key === "index") continue
          this.checkData[original][key] = this.checkData[this.checkData[original].synchronization][key]
        }

        this.handleSubmit('','','','synchronization',original)
      },
      // 整体重置
      handleAllReset() {
        this.$emit("reset")
      },
      onClose() {
        this.$emit("close")
      }
    }
  }
</script>
<style lang="less" scoped>
  .navigation-wrapper {
    display: flex;
    padding-bottom: 10px;
  }

  /* 导航栏 */
  .navigation {
    width: 52px;
    height: 52px;
    border-radius: 10px;

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin-right: 10px;

    font-size: 12px;

  }

  .form-wrapper {
    /* max-height: calc(100vh - 55px - 24px - 55px - 40px); */
    max-height: calc(100vh - 55px - 24px - 55px - 40px - 10px - 10px - 10px);
    overflow-y: scroll;
  }

  .form-block {
    display: flex;
    margin-left: -100px;
    align-items: center;
    font-size: 12px;
  }

  .form-block .chli-title {
    width: 80px;
    flex-shrink: 0;
  }

  .sequence-item {
    display: flex;
    align-items: center;
  }

  .sequence-item .item-content {
    width: 100%;
    font-size: 12px;
  }

  .btn-wrapper {
    padding-top: 10px;
  }

  /* 颜色方案 */
  .color-content{
    display: flex;
    flex-wrap: wrap;
  }
  .color-row{
    display: flex;
    border-radius: 5px;
    border: 1px solid #eee;
    width: 250px;
    padding: 5px;
    margin-bottom: 5px;

    display: flex;
    justify-content: space-between;
    align-items: center;

    cursor: pointer;
  }
  .color-row .color-block{
    width: 20px;
    height: 20px;
    border-radius: 5px;
  }

  /* 通用 */

  .ml10 {
    margin-left: 10px;
  }
  .ml-7 {
    margin-left:-7px;
  }

  /deep/.ant-drawer-body {
    padding: 24px 24px 10px;
  }

  /* form */
  /deep/ .ant-form label{
    font-size: 12px;
  }

  /deep/.ant-form-item-label {
    text-align: left;
  }

  

  /deep/ .ant-form-item {
    margin: 0;
  }

  /deep/ .ant-input{
    font-size: 12px;
  }

  /deep/.ant-input-number {
    width: 120px;
    font-size: 12px;
    height: 24px;
  }

  /deep/.ant-input-number-input{
    height: 24px;
  }

  /deep/.ant-select {
    width: 120px !important;
    font-size: 12px;
    height: 24px;
  }

  /deep/.ant-select-sm .ant-select-selection--single{
    height: 24px;
  }
  /deep/.ant-select-sm .ant-select-selection__rendered{
    line-height: 22px;
  }

  /deep/.ant-btn{
    font-size: 12px;
    padding: 0 8px;
  }

  /deep/.ant-btn-sm{
    font-size: 12px;
  }

  /deep/.btn-wrapper .ant-btn{
    height: 30px;
    line-height: 30px;
  }

  /* drawer */
  /deep/ .ant-drawer-left.ant-drawer-open,
  .ant-drawer-right.ant-drawer-open {
    width: 500px !important;
  }

  /deep/ .ant-drawer-mask {
    opacity: 0 !important;
  }

  /deep/.ant-radio-button-wrapper{
    height: 28px;
    line-height: 28px;
  }
</style>