<template>
	<div>
		<a-modal title="新增包装BOM" :width="800" :visible="selectvisible" @ok="handleSubmit" @cancel="handleCancel">
			<!-- <v-selectpage title="包装BOM代码" placeholder="选择包装BOM代码" ref="sp" v-model="sapNumber" :page-size="6" :data="parts" key-field="sapNumber" show-field="sapNumber" :tb-columns="vcolumns">
			</v-selectpage> -->
			<a-dropdown v-model="dropdownvisible" placement="bottomCenter" :trigger="['click']">
				<a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">{{sapNumber ? sapNumber+'-规格：['+sapDec+']' : '包装BOM代码'}}<a-icon type="down" /></a-button>
				<a-menu slot="overlay">
						<a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:800px">
							<a-input-search v-model="queryParam.partNumber" placeholder="搜索包装BOM代码" @change="onSearch"/>
								<s-table
										style="width:100%"
										ref="table"
										:rowKey="(record) => record.sapNumber"
										:columns="vcolumns"
										:data="loadData"
										:customRow="customRow"
										:scroll="{x: 900,y:200}"
										>
								</s-table>
						</a-spin>
				</a-menu>
			</a-dropdown>
		</a-modal>
		<div style="text-align:right">
			<a-button v-if="hasPerm('sysBom:add')" type="primary" @click="addBom" style="margin-bottom:8px;margin-right:8px">
				新增包装BOM
			</a-button>
		</div>
		<a-drawer placement="right" :closable="false" width="80%" :visible="visible1" @close="onClose1" :destroyOnClose="true">
			<checkhistory :param="param"></checkhistory>
		</a-drawer>
		<a-modal :confirm-loading="visible" v-model="retry" title="失败重试" @ok="handleOk1" style="text-align: center">
			<span>确认重试吗？</span>
		</a-modal>
		<a-drawer :bodyStyle="{ height: '100%' }" placement="right" :closable="false" width="80%" :visible="visible2" @close="onClose2">
			<iframe :src="pdfUrl+'#view=FitH,top'" width="100%" height="100%"></iframe>
		</a-drawer>
		<a-spin :spinning="vloading">
			<a-table :pagination="false" :showPagination="false" size="small" ref="wearktable" :columns="wearklinecolumns" :dataSource="wearkloadData" :scroll="{y: windowHeight }" :rowKey="(record) => record.id" >
				<!-- <span slot="bomPartName2" slot-scope="text,record">
													<a-dropdown>
														<a class="ant-dropdown-link">
															{{!record.bomPartName2 ?'00000000':record.bomPartName2}}<a-icon type="down" />
														</a>
														<a-menu slot="overlay">
															<a-menu-item>
																<a @click="$refs.bomupdate.edit(record)">成品代号输入</a>
															</a-menu-item>
														</a-menu>
													</a-dropdown>
								</span> -->
				<div @preview="preview"></div>
				
				<span slot="bomStatus" slot-scope="text, record">
												<a-dropdown>
													<a class="ant-dropdown-link">
														{{mapStatus[record.bomStatus]}}<a-icon type="down" />
													</a>
													<a-menu v-if="record.bomStatus == 1" slot="overlay">
														<a-menu-item v-if="hasPerm('sysBom:get')">
															<a @click="editBom(record)" >查看</a>
														</a-menu-item>
														<a-menu-item  v-if="hasPerm('sysBomPush:list')">
															<a @click="$refs.checkhistory2.edit(record)" >审核记录</a>
														</a-menu-item>
														<a-menu-item v-if="hasPerm('sysBom:history')">
															<a @click="$refs.bomhistory.edit(record)">变更记录</a>
														</a-menu-item>
													</a-menu>
													<a-menu v-if="record.bomStatus == 0 || record.bomStatus == 4" slot="overlay">
														<a-menu-item v-if="hasPerm('sysBom:save')">
															<a @click="editBom(record)" >编辑</a>
														</a-menu-item>
														<a-menu-item v-if="admin_type">
															<a-popconfirm placement="topRight" title="确认升版？" @confirm="() => adminConfirm(record)">
																<a>确认升版</a>
															</a-popconfirm>
														</a-menu-item>
														<a-menu-item v-if="hasPerm('sysBom:save')">
															<a @click="delBom(record)" >删除</a>
														</a-menu-item>
														<a-menu-item v-if="hasPerm('sysBom:commit')">
															<a @click="$refs.bomupgrade.edit(record,projectdetail)">提交</a>
														</a-menu-item>
														<a-menu-item  v-if="hasPerm('sysBomPush:list')">
															<a @click="$refs.checkhistory2.edit(record)" >审核记录</a>
														</a-menu-item>
														<a-menu-item v-if="hasPerm('sysBom:history')">
															<a @click="$refs.bomhistory.edit(record)">变更记录</a>
														</a-menu-item>
														<a-menu-item v-if="hasPerm('sysBomLine:addlines')">
															<a v-if="record.lines && record.lines.length > 0" @click="$refs.bomaddwerk.edit(record)">
																增加工厂
															</a>
															<a v-else @click="$refs.bomaddwerk.edit(record)">
																设置工厂
															</a>
														</a-menu-item>
														<a-menu-item v-if="hasPerm('sysBomLine:deletelines')">
															<a @click="$refs.bomselect.edit(record)">删除工厂</a>
														</a-menu-item>
													</a-menu>
													<a-menu v-if="record.bomStatus == 2" slot="overlay">
														<a-menu-item v-if="hasPerm('sysBom:get')">
															<a @click="editBom(record)" >查看</a>
														</a-menu-item>
														<a-menu-item v-if="hasPerm('sysBom:upgrade')">
															<a-popconfirm placement="topRight" title="确认修订？" @confirm="() => callBomUpgrade(record)">
																<a>修订</a>
															</a-popconfirm>
														</a-menu-item>
														<a-menu-item  v-if="hasPerm('sysBomPush:list')">
															<a @click="$refs.checkhistory2.edit(record)" >审核记录</a>
														</a-menu-item>
														<a-menu-item v-if="hasPerm('sysBom:history')">
															<a @click="$refs.bomhistory.edit(record)">变更记录</a>
														</a-menu-item>
														<a-menu-item v-if="hasPerm('sysBomLine:addlines')">
															<a v-if="record.lines && record.lines.length > 0" @click="$refs.bomaddwerk.edit(record)">
																增加工厂
															</a>
															<a v-else @click="$refs.bomaddwerk.edit(record)">
																设置工厂
															</a>
														</a-menu-item>
														<a-menu-item v-if="hasPerm('sysBomLine:deletelines')">
															<a @click="$refs.bomselect.edit(record)">删除工厂</a>
														</a-menu-item>
														<a-menu-item v-if="record.productState >= 4">
															<a @click="sapVerify(record)">sap校验</a>
														</a-menu-item>
													</a-menu>
													<a-menu v-if="record.bomStatus == 3" slot="overlay">
														<a-menu-item v-if="hasPerm('sysBom:get')">
															<a @click="editBom(record)" >查看</a>
														</a-menu-item>
														<!-- <a-menu-item>
															<a @click="showRetry(record,1)">重试</a>
														</a-menu-item> -->
														<a-menu-item  v-if="hasPerm('sysBomPush:list')">
															<a @click="$refs.checkhistory2.edit(record)" >审核记录</a>
														</a-menu-item>
														<a-menu-item v-if="hasPerm('sysBom:history')">
															<a @click="$refs.bomhistory.edit(record)">变更记录</a>
														</a-menu-item>
													</a-menu>
													<a-menu v-if="record.bomStatus == 5" slot="overlay">
														<a-menu-item v-if="hasPerm('sysBom:get')">
															<a @click="editBom(record)" >查看</a>
														</a-menu-item>
														<a-menu-item  v-if="hasPerm('sysBomPush:list')">
															<a @click="$refs.checkhistory2.edit(record)" >审核记录</a>
														</a-menu-item>
														<a-menu-item v-if="hasPerm('sysBom:history')">
															<a @click="$refs.bomhistory.edit(record)">变更记录</a>
														</a-menu-item>
													</a-menu>
													<a-menu v-if="record.bomStatus == 7" slot="overlay">
														<template v-if="hasPerm('sysBom:copy')">
															<a-menu-item>
																<a @click="editBom(record)" >编辑</a>
															</a-menu-item>
															<a-menu-item>
																<a-popconfirm placement="topRight" title="确认已经导入？" @confirm="() => $refs.upgrade.edit(record)">
																	<a>确认导入</a>
																</a-popconfirm>
															</a-menu-item>
														</template>
														<template v-else>
															<a-menu-item>
																<a @click="editBom(record)" >查看</a>
															</a-menu-item>
														</template>
														<a-menu-item  v-if="hasPerm('sysBomPush:list')">
															<a @click="$refs.checkhistory2.edit(record)" >审核记录</a>
														</a-menu-item>
														<a-menu-item v-if="hasPerm('sysBom:history')">
															<a @click="$refs.bomhistory.edit(record)">变更记录</a>
														</a-menu-item>
													</a-menu>
												</a-dropdown>
							</span>
					<template slot="lines" slot-scope="text, record">
						<label v-if="record.lines && record.lines.length > 0">
										<label  v-for="(item, i) in record.lines" :key="i">
											<span v-if="i < record.lines.length -1">
												{{dataLines[item]}}，
											</span>
											<span v-else>
												{{dataLines[item]}}
											</span>
										</label>
										
						</label>
						<a v-else-if="(record.bomStatus == 2 || record.bomStatus == 0 || record.bomStatus == 4 || record.bomStatus == 7)&& hasPerm('sysBomLine:addlines')" @click="$refs.bomaddwerk.edit(record)">设置工厂</a>
					</template>
					<span slot="action" slot-scope="text, record">
						<!-- <a @click="$refs.bomselect.edit(record)">产线关联</a>
						<a-divider type="vertical" /> -->
						<a @click="$refs.bomcell.view(record)">查看电芯BOM</a>
					</span>
			</a-table>
	</a-spin>
	<bomverify ref="bomverify" @ok="handleOk" />
	<bomend :dataLines="dataLines" ref="bomend" @ok="handleOk"/>
	<bomselect :werklines="werklines" ref="bomselect" @ok="handleOk" />
	<bomaddwerk :werklines="werklines" ref="bomaddwerk" @ok="handleOk" />
	<bomupgrade ref="bomupgrade" @ok="handleOk" />
	<!-- <bomupdate :parts="parts" ref="bomupdate" @ok="handleOk" /> -->
	<bomhistory ref="bomhistory" @ok="handleOk" />
	<checkhistory2 ref="checkhistory2" @ok="handleOk" />
	<bomcell :bomIssueId="issueId" :dataLines="dataLines" ref="bomcell" @ok="handleOk" />
	<upgrade ref="upgrade" @callBomUpgrade="callBomUpgrade" @ok="handleOk"/>
</div>
</template>

<script>
 	import store from '@/store'
	/* import {
		SelectPage
	} from 'v-selectpage' */
	import {
		STable
	} from '@/components'
	import {
		getwerklines,
		getBomList,
		sysBomAdd,
		sysBomDel,
		sysBomUpgrade,
		retrySap,
		sapVerify,
		adminConfirm
	} from "@/api/modular/system/bomManage"
	import {
		getPartList
	} from "@/api/modular/system/partManage"
	import bomselect from './bomselect'
	import bomupgrade from './bomupgrade'
	import bomaddwerk from './bomaddwerk'
	import checkhistory from './checkhistory'
	import bomhistory from './bomhistory'
	import checkhistory2 from './checkhistory2'
	import bomend from './bomend'
	import bomcell from './bomcell'
	import upgrade from './upgrade'
	import bomverify from './bomverify'
	export default {
		components: {
			STable,
			bomselect,
			bomupgrade,
			/* bomupdate, */
			checkhistory,
			bomhistory,
			checkhistory2,
			bomend,
			bomaddwerk,
			bomcell,
			upgrade,
			bomverify
			//'v-selectpage': SelectPage
		},
		props: {
			issueId: {
				type: Number,
				default: 0
			},
			projectdetail: {
				type: Object,
				default: {}
			},
			/* parts: {
				type: Array,
				default: () => []
			}, */
			date: {
				type: Date,
				default: new Date()
			}
		},
		data() {
			return {
				admin_type:false,
				dropdownvisible:false,
				queryParam:{
				},
				form: this.$form.createForm(this),
				selectvisible: false,
				vcolumns: [{
						title: '物料代码',
						dataIndex: 'sapNumber'
					},
					{
						title: '物料',
						dataIndex: 'partName'
					},
					{
						title: '物料规格',
						dataIndex: 'partDescription',
						width:700
					},
				],
				loadData: parameter => {
					parameter = { ...parameter,
						...{
							flag: 0
						}
					}
					return getPartList(Object.assign(parameter, this.queryParam)).then((res) => {
						return res.data
					})
				},
				retry: false,
				pdfUrl: '',
				pdfUrl: '',
				visible1: false,
				visible: false,
				visible2: false,
				mapStatus: ['编辑中', '审核中', '已审核', '失败中', '被驳回','新增工厂申请','删除工厂申请','导入中'],
				wearklinequeryParam: {
					bomIssueId: this.issueId,
					bomType: 1
				},
				dataLines: {},
				windowHeight: document.documentElement.clientHeight - 265,
				vloading: false,
				loading: false,
				sapNumber: '',
				sapDec:'',
				wearklinecolumns: [{
						title: '序号',
						dataIndex: 'index',
						key: 'index',
						align: 'center',
						width: 50,
						customRender: (text, record, index) => `${index+1}`,
					},
					/* {
						title: '成品电池代号',
						dataIndex: 'bomPartName2',
						width: 120,
						scopedSlots: {
							customRender: 'bomPartName2'
						}
					}, */
					{
						title: '包装BOM代码',
						dataIndex: 'bomData',
						width: 120,
						customRender: (text, record, index) =>{
						let code = ''
						if (text.length > 2) {
							code = JSON.parse(text)[0].sapNumber
						}

						const obj = {
							children: code,
							attrs: {},
						}
						return obj
						},
					},
					{
						title: '包装BOM编号',
						dataIndex: 'bomNo',
						width: 240,
					},
          
					{
						title: 'BOM状态',
						dataIndex: 'bomStatus',
						width: 100,
						scopedSlots: {
							customRender: 'bomStatus'
						}
					},


					{
						title: '产线',
						dataIndex: 'lines',
						width: 200,
						scopedSlots: {
							customRender: 'lines'
						}
					},
					{
						title: '操作',
						dataIndex: 'action',
						scopedSlots: {
							customRender: 'action'
						}
					},
				],
				wearkloadData: [],
				werklines: {},
				historyBomId: '',
				param: {},
				retryId: null
			}
		},
		created() {
			this.admin_type = store.getters.admintype == '1'
			this.callBomList()
			this.callWerkLines()
			//this.callPartList()
		},
		methods: {

			adminConfirm(record){
				this.vloading = true
				adminConfirm({
						id: record.id
					})
					.then((res) => {
						if (res.success) {
							this.$message.info('操作成功', 1);
						} else {
							this.$message.error(res.message, 1);
						}
						this.vloading = false
					})
					.catch((err) => {
						this.vloading = false
						this.$message.error('错误提示：' + err.message, 1)
					});
			},

			delBom(record){
				this.vloading = true
				sysBomDel({
						id: record.id
					})
					.then((res) => {
						if (res.success) {
							this.$message.info('删除成功', 1);
							let index = this.wearkloadData.findIndex(item => item.id == record.id)
							this.wearkloadData.splice(index,1)
						} else {
							this.$message.error(res.message, 1);
						}
						this.vloading = false
					})
					.catch((err) => {
						this.vloading = false
						this.$message.error('错误提示：' + err.message, 1)
					});
			},
			onSearch(e){
				this.$refs.table.refresh()
			},
			customRow(row,index){
				return{
					on:{
						click :()=>{
							this.sapNumber = row.sapNumber
							this.sapDec = row.partDescription
							this.dropdownvisible = false
						}
					}
				}
			},
			showRetry(record) {
				this.retryId = record.id
				this.retry = true
			},
			callBomUpgrade(record) {
				this.vloading = true
				sysBomUpgrade({
					id: record.id,
          			bomVersion:record.bomVersion,
					fileId:record.fileId
				}).then((res) => {
					if (res.success) {
						this.$message.success('修订成功')
						/* this.$message.success('修订成功')
						let index = this.wearkloadData.findIndex(item => item.id == record.id)
						this.wearkloadData[index].bomStatus = record.bomStatus != 7 ? 0 : 2
						if (record.bomStatus != 7) {
							this.editBom(record)
						}
						if (record.bomStatus == 7) {
							this.wearkloadData[index].bomVersion = record.bomVersion
						} */
						this.callBomList()
					} else {
						this.$message.error('修订失败：' + res.message)
					}
					this.vloading = false
				}).finally((res) => {
					this.vloading = false
				})
			},
			handleCancel() {
				//this.$refs.sp.remove()
				this.sapNumber = null
				this.sapDec = ''
				this.selectvisible = false
			},
			handleOk() {},
			handleOk1() {
				this.visible = true
				retrySap({
					id: this.retryId
				}).then((res) => {
					if (res.success) {
						this.$message.success("重试成功")
					} else {
						this.$message.error(res.message)
					}
				}).finally((res) => {
					this.retry = false
					this.visible = false
				})
			},
			sapVerify(record){
				this.vloading = true
				sapVerify({
						id: record.id
					})
					.then((res) => {
						if (res.success) {
							if (res.data.length < 1 || !res.data) {
								this.$message.success('sap校验成功');
							}else{
								this.$refs.bomverify.view(res.data)
							}
						} else {
							this.$message.error(res.message, 1);
						}
						this.vloading = false
					})
					.catch((err) => {
						this.vloading = false
						this.$message.error('错误提示：' + err.message, 1)
					});
			},
			/* callPartList() {
				this.vloading = true
				getPartList({
						flag: 0
					})
					.then((res) => {
						if (res.success) {
							this.parts = res.data
						} else {
							this.$message.error(res.message, 1);
						}
						this.vloading = false
					})
					.catch((err) => {
						this.vloading = false
						this.$message.error('错误提示：' + err.message, 1)
					});
			}, */
			callWerkLines() {
				this.confirmLoading = true
				getwerklines().then((res) => {
					if (res.success) {
						let mapline = {}
						for (var key in res.data) {
							for (const _item of res.data[key]) {
								mapline[_item.id] = _item.namecode ? _item.namecode + '--' + _item.lineName : _item.werkNo + '->' + _item.lineName
							}
						}
						this.dataLines = mapline
						this.werklines = res.data;
					} else {
						this.$message.error(res.message)
					}
					this.confirmLoading = false
				}).catch((err) => {
					this.$message.error('错误：' + err.message)
					this.confirmLoading = false
				})
			},
			callBomList() {
				this.vloading = true
				getBomList(this.wearklinequeryParam).then((res) => {
					if (res.success) {
						this.wearkloadData = res.data;
					} else {
						this.$message.error(res.message)
					}
					this.vloading = false
				}).catch((err) => {
					this.$message.error('错误：' + err.message)
					this.vloading = false
				})
			},
			editBom(record) {
				this.$emit('onUpdate', '6', record.id, '4')
			},
			preview(id, processId) {
				this.historyBomId = id
				this.param.historyBomId = id
				this.param.processId = processId
				this.visible1 = !this.visible1
			},
			previewPdf(id) {
				this.pdfUrl = process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id=' + id
				this.visible2 = !this.visible2
			},
			onClose1() {
				this.visible1 = false;
			},
			onClose2() {
				this.visible2 = false;
			},
			addBom() {
				this.selectvisible = true
			},
			handleSubmit() {
				if (!this.sapNumber || this.sapNumber == '') {
					this.$message.error('请选择包装BOM代码')
					return false
				}
				if (JSON.stringify(this.projectdetail) == '{}') {
					this.$message.error('访问jira数据出错,请刷新页面再新增')
					return false
				}
				this.vloading = true
				sysBomAdd({
						bomPartName: this.sapNumber,
						bomIssueId: this.issueId,
						bomType: 1
					})
					.then((res) => {
						if (res.success) {
							this.wearkloadData.push(res.data)
							this.selectvisible = false
							this.sapNumber = ''
							this.sapDec = ''
							//this.$refs.sp.remove()
						} else {
							this.$message.error(res.message, 1);
						}
						this.vloading = false
					})
					.catch((err) => {
						this.vloading = false
						this.$message.error('错误提示：' + err.message, 1)
					});
			}
		},
		watch: {
			date(newVal, oldVal) {
				this.callBomList()
			}
		},
	}
</script>

<style lang='less' scoped=''>
/deep/.ant-table{
    margin: 0 2px;
    margin-top:2px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}
/deep/.ant-table-thead > tr > th{
    font-weight: bold;
    background: #f3f3f3 !important;
}
/deep/.ant-table-small > .ant-table-content > .ant-table-body{
    margin: 0;
}
</style>