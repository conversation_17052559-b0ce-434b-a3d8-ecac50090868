import Vue from 'vue'
import axios from 'axios'
import store from '@/store'
// import router from './router'
import { message, Modal, notification } from 'ant-design-vue' /// es/notification
import { VueAxios } from './axios'
import { ACCESS_TOKEN,ALL_APPS_MENU,DICT_TYPE_TREE_DATA,DEFAULT_LAYOUT_MODE } from '@/store/mutation-types'

// 创建 axios 实例
const service = axios.create({
  baseURL: '/api', // api base_url
  timeout: 10000000 // 请求超时时间
})

const err = (error) => {
  if (error.response) {
    const data = error.response.data
    const token = Vue.ls.get(ACCESS_TOKEN)

    if (error.response.status === 403) {
      //console.log('服务器403啦，要重新登录！')
      notification.error({
        message: 'Forbidden',
        description: data.message
      })
    }
    if (error.response.status === 500) {
      if (data.message.length > 0) {
        message.error(data.message)
      }
    }
    if (error.response.status === 401 && !(data.result && data.result.isLogin)) {
      if (token) {
        store.dispatch('Logout').then(() => {
          setTimeout(() => {
            window.location.reload()
          }, 1500)
        })
      }
    }
  }
  return Promise.reject(error)
}

// request interceptor
service.interceptors.request.use(config => {
  if(config.url.indexOf('/limsUpload') !== -1 || config.url.indexOf('/minioDownload') !== -1){
    config.baseURL = ''
  }
  const token = Vue.ls.get(ACCESS_TOKEN)
  const btoken = Vue.ls.get('token')
  if (token && config.url.indexOf('/minioDownload') == -1) {
    config.headers['Authorization'] = 'Bearer ' + token
  }
  if (btoken && config.url.indexOf('/minioDownload') == -1) {
    config.headers['JiraAuth'] = btoken
  }
  return config
}, err)

/**
 * response interceptor
 * 所有请求统一返回
 */
service.interceptors.response.use((response) => {
  if (response.request.responseType === 'blob') {
    return response
  }
  const resData = response.data
  const code = response.data.code
  if (!store.state.app.hasError) {
    if (code === 1011006 || code === 1011007 || code === 1011008 || code === 1011009) {
      /* Modal.error({
        title: '提示：',
        content: resData.message,
        okText: '重新登录',
        onOk: () => {
          Vue.ls.remove(ACCESS_TOKEN)
          Vue.ls.remove("token")
          Vue.ls.remove("jtoken")
          store.dispatch('SetHasError', false)
          window.location.reload()
        }
      }) */
      Vue.ls.remove(ACCESS_TOKEN)
      Vue.ls.remove("token")
      Vue.ls.remove("jtoken")
      Vue.ls.remove("DEFAULT_THEME");
      Vue.ls.remove("SIDEBAR_TYPE");
      Vue.ls.remove(ALL_APPS_MENU)
      Vue.ls.remove(DICT_TYPE_TREE_DATA)
      Vue.ls.remove(DEFAULT_LAYOUT_MODE)
      store.dispatch('SetHasError', false)
      window.location.reload()
      store.dispatch('SetHasError', true)
    }
    if (code == 401) {
      /* Modal.error({
        title: '提示：',
        content: resData.message,
        okText: '重新登录',
        onOk: () => {
          Vue.ls.remove(ACCESS_TOKEN)
          Vue.ls.remove("token")
          Vue.ls.remove("jtoken")
          store.dispatch('SetHasError', false)
          window.location.reload()
        }
      }) */
      Vue.ls.remove(ACCESS_TOKEN)
      Vue.ls.remove("token")
      Vue.ls.remove("jtoken")
      Vue.ls.remove("DEFAULT_THEME");
      Vue.ls.remove("SIDEBAR_TYPE");
      Vue.ls.remove(ALL_APPS_MENU)
      Vue.ls.remove(DICT_TYPE_TREE_DATA)
      Vue.ls.remove(DEFAULT_LAYOUT_MODE)
      store.dispatch('SetHasError', false)
      window.location.reload()
      store.dispatch('SetHasError', true)
    }
    if (code === 1013002 || code === 1016002 || code === 1015002) {
      message.error(response.data.message)
      return response.data
    }
  }
  return resData
}, err)

const installer = {
  vm: {},
  install (Vue) {
    Vue.use(VueAxios, service)
  }
}

export {
  installer as VueAxios,
  service as axios
}
