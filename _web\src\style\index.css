@import './ant-ui.css';

/* container */
.container {
    font-weight: 400;
    padding: 10px;
    line-height: 1;

}

/* 输入框 */
.input-form {
    width: 350px !important;
    height: 30px !important;
}

.filter-form {
    height: 30px !important;
}


/* 表格 */
/* .table-wrapper{
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
} */

/* 边距 */
.mr16 {
    margin-right: 16px;
}

.mr10 {
    margin-right: 10px;
}

.mb10{
	margin-bottom: 10px;
}

/* 颜色 */
.blue {
    color: #83a4ef;
}

/* 小手指 */
.hand {
    cursor: pointer;
}

/* 面包屑返回icon */
.rollback-icon {
    margin-right: 8px;
}


/*  角标 */
.small-mark{

position: relative;
}
.small-mark::before {
		content: "";
		width:0;
		height: 0;
		border:5px solid transparent;
		border-right:5px solid #d00f2b;
		transform: rotate(135deg);
		position: absolute;
		right: -5px;
		top: -5px;
	}

  .mark{
position: relative;
}
.mark::before {
		content: "";
		width:0;
		height: 0;
		border:10px solid transparent;
		border-right:10px solid #d00f2b;
		transform: rotate(135deg);
		position: absolute;
		right: -10px;
		top: -10px;
    z-index: 999;
	}

/* 换行 */
.a-wrap{
	white-space: pre-wrap;
}

/* 超过省略号 */
.ellipsis-tip {
	word-break: break-all;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 3; /* 这里是超出几行省略 */
	overflow: hidden;
}

.blue {
	color: #1890ff;
}


/* 特效线 */



.pbi-block-line {
	position: relative;
}
.pbi-block-line::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	border: 2px solid;
	border-image: linear-gradient(rgba(0, 216, 247, 0.5) 0%, #00afed 100%) 2 2 2 2;
	border-radius: 10px;
	transition: all 0.5s;
	animation: clippath 5s infinite linear;
}
@keyframes clippath {
	0%,
	100% {
		clip-path: inset(0 0 0 95%);
	}
	25% {
		clip-path: inset(95% 0 0 0);
	}
	50% {
		clip-path: inset(0 95% 0 0);
	}
	75% {
		clip-path: inset(0 0 95% 0);
	}
}

/* 特效光感线 */
@keyframes opacityChange {
	50% {
		opacity: 0.5;
	}
	100% {
		opacity: 1;
	}
}
@keyframes rotate {
	100% {
		transform: rotate(1turn);
	}
}
.pbi-block-light {
	--borderWidth: 10px;
	--bRadius: 10px;
	width: 100%;
	height: 40%;
	position: relative;
	z-index: 0;
	overflow: hidden;
	border-radius: 10px;
}
.pbi-block-light::after,
.pbi-block-light::before {
	box-sizing: border-box;
}
.pbi-block-light::before {
	content: "";
	position: absolute;
	z-index: -2;
	left: -50%;
	top: -50%;
	width: 200%;
	height: 200%;
	background-color: #fff;
	background-repeat: no-repeat;
	background-position: 0 0;
	background-image: conic-gradient(transparent, rgba(168, 239, 255, 1), transparent 30%);
	animation: rotate 4s linear infinite;
}

.pbi-block-light::after {
	content: "";
	position: absolute;
	z-index: -1;
	left: calc(var(--borderWidth) / 2);
	top: calc(var(--borderWidth) / 2);
	width: calc(100% - var(--borderWidth));
	height: calc(100% - var(--borderWidth));
	border-radius: 10px;
	background-color: #fff;
	/* 这句效果打开有助于理解动画 */
	/*animation: opacityChange 5s infinite linear;*/
}

/* 特效按钮 */
.streamer-btn {
	position: relative;
	display: inline-block;

	text-decoration: none;
	text-transform: uppercase;
	transition: 0.5s;
	overflow: hidden;
}

.streamer-btn span {
	position: absolute;
	display: block;
}
.streamer-btn span {
	position: absolute;
	display: block;
}
.streamer-btn span:nth-child(1) {
	top: 0;
	left: 0;
	width: 100%;
	height: 2px;
	background: linear-gradient(90deg, transparent, #fff);
	animation: animate1 2s linear infinite;
	animation-delay: 0s;
}
@keyframes animate1 {
	0% {
		left: -100%;
	}
	50%,
	100% {
		left: 100%;
	}
}
.streamer-btn span:nth-child(2) {
	top: -100%;
	right: 0;
	width: 2px;
	height: 100%;
	background: linear-gradient(180deg, transparent, #fff);
	animation: animate2 2s linear infinite;
	animation-delay: 1s;
}
@keyframes animate2 {
	0% {
		top: -100%;
	}
	50%,
	100% {
		top: 100%;
	}
}
.streamer-btn span:nth-child(3) {
	bottom: 0;
	right: 0;
	width: 100%;
	height: 2px;
	background: linear-gradient(270deg, transparent, #fff);
	animation: animate3 2s linear infinite;
	animation-delay: 1.5s;
}
@keyframes animate3 {
	0% {
		right: -100%;
	}
	50%,
	100% {
		right: 100%;
	}
}
.streamer-btn span:nth-child(4) {
	bottom: -100%;
	left: 0;
	width: 2px;
	height: 100%;
	background: linear-gradient(360deg, transparent, #fff);
	animation: animate4 2s linear infinite;
	animation-delay: 1.5s;
}
@keyframes animate4 {
	0% {
		bottom: -100%;
	}
	50%,
	100% {
		bottom: 100%;
	}
}



/* 特效按钮 */
.plus-btn {
	position: relative;
	display: inline-block;
	/* text-decoration: none;
	text-transform: uppercase; */
	transition: 1.25s;
	overflow: hidden;
}

.plus-btn span {
	position: absolute;
	display: block;
}
.plus-btn span {
	position: absolute;
	display: block;
}
.plus-btn span:nth-child(1) {
	top: 0;
	left: 0;
	width: 100%;
	height: 2px;
	background: linear-gradient(90deg, transparent, #00afed);
	animation: animate1 5s linear infinite;
	animation-delay: 0s;
}
@keyframes animate1 {
	0% {
		left: -100%;
	}
	50%,
	100% {
		left: 100%;
	}
}
.plus-btn span:nth-child(2) {
	top: -100%;
	right: 0;
	width: 2px;
	height: 100%;
	background: linear-gradient(180deg, transparent, #00afed);
	animation: animate2 5s linear infinite;
	animation-delay: 1.25s;
}
@keyframes animate2 {
	0% {
		top: -100%;
	}
	50%,
	100% {
		top: 100%;
	}
}
.plus-btn span:nth-child(3) {
	bottom: 0;
	right: 0;
	width: 100%;
	height: 2px;
	background: linear-gradient(270deg, transparent, #00afed);
	animation: animate3 5s linear infinite;
	animation-delay: 2.5s;
}
@keyframes animate3 {
	0% {
		right: -100%;
	}
	50%,
	100% {
		right: 100%;
	}
}
.plus-btn span:nth-child(4) {
	bottom: -100%;
	left: 0;
	width: 2px;
	height: 100%;
	background: linear-gradient(360deg, transparent, #00afed);
	animation: animate4 5s linear infinite;
	animation-delay: 3.75s;
}
@keyframes animate4 {
	0% {
		bottom: -100%;
	}
	50%,
	100% {
		bottom: 100%;
	}
}

.anima {
	animation-name: likes;
	animation-direction: alternate;
	animation-timing-function: linear;
	animation-delay: 0s;
	animation-iteration-count: infinite;
	animation-duration: 1.5s;
}

@keyframes likes {
	0% {
		transform: scale(1);
	}

	25% {
		transform: scale(0.95);
	}

	50% {
		transform: scale(0.9);
	}

	75% {
		transform: scale(0.95);
	}

	100% {
		transform: scale(1);
	}
}


/* 超过三行，显示省略号 */
.three-text-ellipsis{
	overflow:hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}


/deep/ .ant-table-thead > tr > th {
  padding: 5px!important;
  font-size: 13px!important;
  color: rgba(0, 0, 0, .85)!important;
  font-weight: 500!important;
  text-align: center !important;
}

/deep/ .ant-table-tbody > tr > td {
  padding: 4px!important;
  color: #333!important;
  font-size: 12px!important;
  font-weight: 400!important;
}
