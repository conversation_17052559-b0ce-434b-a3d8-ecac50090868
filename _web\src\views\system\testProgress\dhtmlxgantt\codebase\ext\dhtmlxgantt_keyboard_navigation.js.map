{"version": 3, "sources": ["webpack://[name]/webpack/universalModuleDefinition", "webpack://[name]/webpack/bootstrap", "webpack://[name]/./sources/utils/dom_helpers.js", "webpack://[name]/./sources/ext/keyboard_navigation/core.js", "webpack://[name]/./sources/ext/keyboard_navigation/modals.js", "webpack://[name]/./sources/ext/keyboard_navigation/elements/task_cell.js", "webpack://[name]/./sources/ext/keyboard_navigation/elements/task_row.js", "webpack://[name]/./sources/ext/keyboard_navigation/elements/header_cell.js", "webpack://[name]/./sources/ext/keyboard_navigation/elements/nav_node.js", "webpack://[name]/./sources/ext/keyboard_navigation/elements/gantt_node.js", "webpack://[name]/./sources/ext/keyboard_navigation/common/trap_modal_focus.js", "webpack://[name]/./sources/ext/keyboard_navigation/common/eventhandler.js", "webpack://[name]/./sources/ext/keyboard_navigation/common/keyboard_shortcuts.js", "webpack://[name]/./sources/ext/keyboard_navigation.js", "webpack://[name]/./sources/utils/eventable.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "window", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "elementPosition", "elem", "top", "left", "right", "bottom", "getBoundingClientRect", "box", "body", "document", "doc<PERSON><PERSON>", "documentElement", "parentNode", "scrollTop", "pageYOffset", "scrollLeft", "pageXOffset", "clientTop", "clientLeft", "offsetWidth", "offsetHeight", "parseInt", "offsetTop", "offsetLeft", "offsetParent", "y", "Math", "round", "x", "width", "height", "isVisible", "node", "display", "visibility", "getComputedStyle", "style", "currentStyle", "hasNonNegativeTabIndex", "isNaN", "getAttribute", "<PERSON><PERSON><PERSON><PERSON>", "a", "area", "nodeName", "loLowerCase", "isEnabled", "input", "select", "textarea", "button", "toLowerCase", "hasAttribute", "getClassName", "className", "baseVal", "indexOf", "_trimString", "_slave", "createElement", "getTargetNode", "e", "tagName", "event", "target", "srcElement", "str", "String", "trim", "this", "replace", "apply", "getNodePosition", "getFocusableNodes", "nodes", "querySelectorAll", "join", "nodesArray", "Array", "slice", "length", "splice", "getScrollSize", "div", "cssText", "append<PERSON><PERSON><PERSON>", "clientWidth", "<PERSON><PERSON><PERSON><PERSON>", "addClassName", "removeClassName", "split", "regEx", "RegExp", "insertNode", "newone", "innerHTML", "child", "<PERSON><PERSON><PERSON><PERSON>", "removeNode", "getChildNodes", "css", "ch", "childNodes", "len", "out", "obj", "push", "toNode", "getElementById", "querySelector", "locateClassName", "classname", "strict", "trg", "undefined", "ind", "char<PERSON>t", "locateAttribute", "attribute", "getRelativeEventPosition", "ev", "clientX", "clientY", "isChildOf", "parent", "hasClass", "element", "classList", "contains", "test", "closest", "selector", "matches", "msMatchesSelector", "webkitMatchesSelector", "el", "parentElement", "nodeType", "console", "error", "gantt", "$keyboardNavigation", "dispatcher", "isActive", "activeNode", "globalNode", "GanttNode", "enable", "setActiveNode", "getActiveNode", "disable", "getDefaultNode", "config", "keyboard_navigation_cells", "TaskCell", "TaskRow", "<PERSON><PERSON><PERSON><PERSON>", "fallback", "setDefaultNode", "fromDomElement", "inputs", "<PERSON><PERSON><PERSON><PERSON>", "focusGlobalNode", "blurNode", "focusNode", "focusChanged", "compareTo", "keptFocus", "focus", "blur", "keyDownHandler", "isModal", "defaultPrevented", "ganttNode", "command", "shortcuts", "getCommandFromEvent", "activeElement", "facade", "callEvent", "<PERSON><PERSON><PERSON><PERSON>", "doAction", "_timeout", "awaits<PERSON><PERSON><PERSON>", "delay", "callback", "clearTimeout", "setTimeout", "clear<PERSON>elay", "modalsStack", "afterPopup", "startModal", "eventRemove", "trapFocus", "endModal", "pop", "currentTarget", "isTopModal", "traceLightbox", "getLightbox", "attachEvent", "focusElement", "domHelpers", "taskId", "index", "rootLevel", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "root_id", "columnIndex", "isTaskExists", "getTaskIndex", "_compose", "_handlers", "getGridColumns", "result", "visibleColumns", "locate", "cellElement", "getNode", "isTaskVisible", "show_grid", "row", "$grid", "task_attribute", "getTaskNode", "keys", "up", "nextElement", "prevTask", "getPrev", "moveTo", "down", "nextTask", "getNext", "columns", "end", "home", "pagedown", "getVisibleTaskCount", "getTaskByIndex", "id", "pageup", "bindAll", "KeyNavNode", "header", "nextIndex", "viewWidth", "viewHeight", "pos", "getTaskPosition", "getTask", "row_height", "scroll", "getScrollState", "$task", "inner_width", "$grid_data", "$task_data", "inner_height", "scrollTo", "show_chart", "task_scroll_offset", "shift+down", "<PERSON><PERSON><PERSON><PERSON>", "$open", "open", "shift+up", "close", "shift+right", "is<PERSON><PERSON><PERSON>ly", "prevId", "getPrevSibling", "moveTask", "updateTask", "shift+left", "getParent", "space", "isSelectedTask", "unselectTask", "selectTask", "ctrl+left", "ctrl+right", "delete", "$click", "buttons", "enter", "showLightbox", "ctrl+enter", "createTask", "previousSibling", "$grid_scale", "taskRow", "enter, space", "click", "EventHandler", "b", "canStringifyThis", "toString", "canStringifyThat", "eventFacade", "setAttribute", "$eventAttached", "preventDefault", "$container", "removeAttribute", "scrollHorizontal", "dir", "date", "dateFromPos", "scale", "getScale", "step", "add", "unit", "posFromDate", "scrollVertical", "alt+left", "alt+right", "alt+up", "alt+down", "ctrl+z", "undo", "ctrl+r", "redo", "keyCode", "focusable", "currentFocus", "currentIndex", "shift<PERSON>ey", "lastItem", "firstItem", "hash", "getHash", "handler", "returnValue", "shortcut", "commands", "parse", "unbind", "map", "initKeys", "createCommand", "modifiers", "shift", "alt", "ctrl", "meta", "expr", "getExpressions", "words", "get<PERSON><PERSON>s", "j", "commandKeys", "specialKeys", "charCodeAt", "domEvent", "altKey", "ctrl<PERSON>ey", "metaKey", "which", "printableKey", "fromCharCode", "getHashFromEvent", "parts", "junctionChar", "term", "combinationChar", "backspace", "tab", "esc", "insert", "plus", "f1", "f2", "f3", "f4", "f5", "f6", "f7", "f8", "f9", "f10", "f11", "f12", "eventable", "keyboard_navigation", "arguments", "res", "isTaskFocused", "focusHandler", "$preventDefault", "reFocusActiveNode", "dom<PERSON>lement", "mousedownHandler", "locateTask", "onReady", "detachEvent", "$data", "tasksStore", "currentNode", "_smart_render", "updateRender", "_redrawTasks", "renderers", "items", "focusedItemVisible", "start_date", "item", "nodeConstructor", "oldId", "newId", "interval", "setInterval", "getSelection", "_locate_css", "getScope", "scopes", "headerCell", "taskCell", "findVisibleColumnIndex", "columnName", "clearInterval", "keyNavFacade", "mixin", "addShortcut", "scope", "scopeObject", "getShortcutHandler", "getCommandHandler", "removeShortcut", "type", "constructor", "column", "getScopeName", "ext", "keyboardNavigation", "setupKeyNav", "EventHost", "_connected", "_silent_mode", "_silentStart", "_silentEnd", "createEventStorage", "dhx_catch", "z", "zr", "addEvent", "removeEvent", "eventHost", "catcher", "callObj", "attachAll", "arg0", "handler<PERSON>ame", "concat", "checkEvent", "list", "eventName", "eventId", "detachAllEvents"], "mappings": ";;;;;;;;;;;;CAAA,SAAAA,EAAAC,GACA,iBAAAC,SAAA,iBAAAC,OACAA,OAAAD,QAAAD,IACA,mBAAAG,eAAAC,IACAD,OAAA,yCAAAH,GACA,iBAAAC,QACAA,QAAA,uCAAAD,IAEAD,EAAA,uCAAAC,IARA,CASCK,OAAA,WACD,mBCTA,IAAAC,KAGA,SAAAC,EAAAC,GAGA,GAAAF,EAAAE,GACA,OAAAF,EAAAE,GAAAP,QAGA,IAAAC,EAAAI,EAAAE,IACAC,EAAAD,EACAE,GAAA,EACAT,YAUA,OANAU,EAAAH,GAAAI,KAAAV,EAAAD,QAAAC,IAAAD,QAAAM,GAGAL,EAAAQ,GAAA,EAGAR,EAAAD,QA0DA,OArDAM,EAAAM,EAAAF,EAGAJ,EAAAO,EAAAR,EAGAC,EAAAQ,EAAA,SAAAd,EAAAe,EAAAC,GACAV,EAAAW,EAAAjB,EAAAe,IACAG,OAAAC,eAAAnB,EAAAe,GAA0CK,YAAA,EAAAC,IAAAL,KAK1CV,EAAAgB,EAAA,SAAAtB,GACA,oBAAAuB,eAAAC,aACAN,OAAAC,eAAAnB,EAAAuB,OAAAC,aAAwDC,MAAA,WAExDP,OAAAC,eAAAnB,EAAA,cAAiDyB,OAAA,KAQjDnB,EAAAoB,EAAA,SAAAD,EAAAE,GAEA,GADA,EAAAA,IAAAF,EAAAnB,EAAAmB,IACA,EAAAE,EAAA,OAAAF,EACA,KAAAE,GAAA,iBAAAF,QAAAG,WAAA,OAAAH,EACA,IAAAI,EAAAX,OAAAY,OAAA,MAGA,GAFAxB,EAAAgB,EAAAO,GACAX,OAAAC,eAAAU,EAAA,WAAyCT,YAAA,EAAAK,UACzC,EAAAE,GAAA,iBAAAF,EAAA,QAAAM,KAAAN,EAAAnB,EAAAQ,EAAAe,EAAAE,EAAA,SAAAA,GAAgH,OAAAN,EAAAM,IAAqBC,KAAA,KAAAD,IACrI,OAAAF,GAIAvB,EAAA2B,EAAA,SAAAhC,GACA,IAAAe,EAAAf,KAAA2B,WACA,WAA2B,OAAA3B,EAAA,SAC3B,WAAiC,OAAAA,GAEjC,OADAK,EAAAQ,EAAAE,EAAA,IAAAA,GACAA,GAIAV,EAAAW,EAAA,SAAAiB,EAAAC,GAAsD,OAAAjB,OAAAkB,UAAAC,eAAA1B,KAAAuB,EAAAC,IAGtD7B,EAAAgC,EAAA,aAIAhC,IAAAiC,EAAA,uBCjFA,SAAAC,EAAAC,GACA,IAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EACA,GAAAJ,EAAAK,sBAAA,CACA,IAAAC,EAAAN,EAAAK,wBACAE,EAAAC,SAAAD,KACAE,EAAAD,SAAAE,iBACAF,SAAAD,KAAAI,YACAH,SAAAD,KAEAK,EAAAjD,OAAAkD,aAAAJ,EAAAG,WAAAL,EAAAK,UACAE,EAAAnD,OAAAoD,aAAAN,EAAAK,YAAAP,EAAAO,WACAE,EAAAP,EAAAO,WAAAT,EAAAS,WAAA,EACAC,EAAAR,EAAAQ,YAAAV,EAAAU,YAAA,EACAhB,EAAAK,EAAAL,IAAAW,EAAAI,EACAd,EAAAI,EAAAJ,KAAAY,EAAAG,EAEAd,EAAAK,SAAAD,KAAAW,YAAAZ,EAAAH,MACAC,EAAAI,SAAAD,KAAAY,aAAAb,EAAAF,WACE,CACF,KAAAJ,GACAC,GAAAmB,SAAApB,EAAAqB,UAAA,IACAnB,GAAAkB,SAAApB,EAAAsB,WAAA,IACAtB,IAAAuB,aAGApB,EAAAK,SAAAD,KAAAW,YAAAlB,EAAAkB,YAAAhB,EACAE,EAAAI,SAAAD,KAAAY,aAAAnB,EAAAmB,aAAAlB,EAEA,OAASuB,EAAAC,KAAAC,MAAAzB,GAAA0B,EAAAF,KAAAC,MAAAxB,GAAA0B,MAAA5B,EAAAkB,YAAAW,OAAA7B,EAAAmB,aAAAhB,MAAAsB,KAAAC,MAAAvB,GAAAC,OAAAqB,KAAAC,MAAAtB,IAGT,SAAA0B,EAAAC,GACA,IAAAC,GAAA,EACAC,GAAA,EACA,GAAAtE,OAAAuE,iBAAA,CACA,IAAAC,EAAAxE,OAAAuE,iBAAAH,EAAA,MACAC,EAAAG,EAAA,QACAF,EAAAE,EAAA,gBACEJ,EAAAK,eACFJ,EAAAD,EAAAK,aAAA,QACAH,EAAAF,EAAAK,aAAA,YAEA,cAAAJ,GAAA,UAAAC,EAGA,SAAAI,EAAAN,GACA,OAAAO,MAAAP,EAAAQ,aAAA,gBAAAR,EAAAQ,aAAA,eAGA,SAAAC,EAAAT,GAEA,QADoBU,GAAA,EAAAC,MAAA,GACpBX,EAAAY,SAAAC,kBACAb,EAAAQ,aAAA,QAKA,SAAAM,EAAAd,GAEA,QADmBe,OAAA,EAAAC,QAAA,EAAAC,UAAA,EAAAC,QAAA,EAAAxD,QAAA,GACnBsC,EAAAY,SAAAO,iBACAnB,EAAAoB,aAAA,YA4CA,SAAAC,EAAArB,GACA,IAAAA,EAAA,SAEA,IAAAsB,EAAAtB,EAAAsB,WAAA,GAOA,OANAA,EAAAC,UACAD,IAAAC,SAEAD,EAAAE,UACAF,EAAA,IAEAG,EAAAH,GAgCA,IAAAI,EAAAjD,SAAAkD,cAAA,OA2BA,SAAAC,EAAAC,GAQA,OANAA,EAAAC,QACAD,GAEAA,KAAAjG,OAAAmG,OACAC,QAAAH,EAAAI,WAoBA,SAAAR,EAAAS,GAEA,OADAC,OAAAvE,UAAAwE,MAAA,WAAgD,OAAAC,KAAAC,QAAA,mBAChDC,MAAAL,GA4EAzG,EAAAD,SACAgH,gBAAAxE,EACAyE,kBArNA,SAAAnH,GAgBA,IAfA,IAAAoH,EAAApH,EAAAqH,kBACA,UACA,aACA,QACA,SACA,WACA,SACA,SACA,SACA,QACA,aACA,qBACAC,KAAA,OAEAC,EAAAC,MAAAlF,UAAAmF,MAAA5G,KAAAuG,EAAA,GACA1G,EAAA,EAAeA,EAAA6G,EAAAG,OAAuBhH,IAAA,CACtC,IAAAgE,EAAA6C,EAAA7G,IACAsE,EAAAN,IAAAc,EAAAd,IAAAS,EAAAT,KAAAD,EAAAC,KAEA6C,EAAAI,OAAAjH,EAAA,GACAA,KAGA,OAAA6G,GA8LAK,cA3LA,WACA,IAAAC,EAAA1E,SAAAkD,cAAA,OACAwB,EAAA/C,MAAAgD,QAAA,uIAEA3E,SAAAD,KAAA6E,YAAAF,GACA,IAAAtD,EAAAsD,EAAAhE,YAAAgE,EAAAG,YAGA,OAFA7E,SAAAD,KAAA+E,YAAAJ,GAEAtD,GAoLAwB,eACAmC,aArKA,SAAAxD,EAAAsB,GACAA,IAAA,IAAAtB,EAAAsB,UAAAE,QAAAF,KACAtB,EAAAsB,WAAA,IAAAA,IAoKAmC,gBAhKA,SAAAzD,EAAAzD,GACAA,IAAAmH,MAAA,KACA,QAAA1H,EAAA,EAAgBA,EAAAO,EAAAyG,OAAiBhH,IAAA,CACjC,IAAA2H,EAAA,IAAAC,OAAA,UAAArH,EAAAP,GAAA,mBACAgE,EAAAsB,UAAAtB,EAAAsB,UAAAgB,QAAAqB,EAAA,MA6JAE,WAzIA,SAAA7D,EAAA8D,GACApC,EAAAqC,UAAAD,EACA,IAAAE,EAAAtC,EAAAuC,WAEA,OADAjE,EAAAqD,YAAAW,GACAA,GAsIAE,WAnIA,SAAAlE,GACAA,KAAApB,YACAoB,EAAApB,WAAA2E,YAAAvD,IAkIAmE,cA9HA,SAAAnE,EAAAoE,GAIA,IAHA,IAAAC,EAAArE,EAAAsE,WACAC,EAAAF,EAAArB,OACAwB,KACAxI,EAAA,EAAgBA,EAAAuI,EAASvI,IAAA,CACzB,IAAAyI,EAAAJ,EAAArI,GACAyI,EAAAnD,YAAA,IAAAmD,EAAAnD,UAAAE,QAAA4C,IACAI,EAAAE,KAAAD,GAGA,OAAAD,GAqHAG,OApJA,SAAA3E,GACA,uBAAAA,EACAvB,SAAAmG,eAAA5E,IAAAvB,SAAAoG,cAAA7E,IAAAvB,SAAAD,KAEAwB,GAAAvB,SAAAD,MAiJAsG,gBApFA,SAAAjD,EAAAkD,EAAAC,GACA,IAAAC,EAAArD,EAAAC,GACAuC,EAAA,GAKA,SAHAc,IAAAF,IACAA,GAAA,GAEAC,GAAA,CAEA,GADAb,EAAA/C,EAAA4D,GACA,CACA,IAAAE,EAAAf,EAAA5C,QAAAuD,GACA,GAAAI,GAAA,GACA,IAAAH,EACA,OAAAC,EAGA,IAAA9G,EAAA,IAAAgH,IAAA1D,EAAA2C,EAAAgB,OAAAD,EAAA,IACA/G,EAAA+G,EAAAJ,EAAA/B,QAAAoB,EAAApB,SAAAvB,EAAA2C,EAAAgB,OAAAD,EAAAJ,EAAA/B,SAEA,GAAA7E,GAAAC,EACA,OAAA6G,GAGAA,IAAArG,WAEA,aA4DAyG,gBAzGA,SAAAxD,EAAAyD,GACA,GAAAA,EAAA,CAIA,IAFA,IAAAL,EAAArD,EAAAC,GAEAoD,GAAA,CACA,GAAAA,EAAAzE,cACAyE,EAAAzE,aAAA8E,GACA,OAAAL,EAEAA,IAAArG,WAEA,cA8FAgD,gBACA2D,yBAxDA,SAAAC,EAAAxF,GACA,IAAA1D,EAAAmC,SAAAE,gBACAJ,EAAAP,EAAAgC,GAEA,OACAJ,EAAA4F,EAAAC,QAAAnJ,EAAAyC,WAAAzC,EAAA4C,WAAAX,EAAAqB,EAAAI,EAAAjB,WACAU,EAAA+F,EAAAE,QAAApJ,EAAAuC,UAAAvC,EAAA2C,UAAAV,EAAAkB,EAAAO,EAAAnB,YAmDA8G,UA/CA,SAAA3B,EAAA4B,GACA,IAAA5B,IAAA4B,EACA,SAGA,KAAA5B,MAAA4B,GACA5B,IAAApF,WAGA,OAAAoF,IAAA4B,GAuCAC,SAlKA,SAAAC,EAAAxE,GACA,oBAAAwE,EACAA,EAAAC,UAAAC,SAAA1E,GAEA,IAAAsC,OAAA,MAAAtC,EAAA,OAAA2E,KAAAH,EAAAxE,YA+JA4E,QArCA,SAAAJ,EAAAK,GACA,GAAAL,EAAAI,QACA,OAAAJ,EAAAI,QAAAC,GACE,GAAAL,EAAAM,SAAAN,EAAAO,mBAAAP,EAAAQ,sBAAA,CACF,IAAAC,EAAAT,EACA,IAAArH,SAAAE,gBAAAqH,SAAAO,GAAA,YACA,GAGA,IAFAA,EAAAH,SAAAG,EAAAF,mBAAAE,EAAAD,uBAEAnK,KAAAoK,EAAAJ,GAAA,OAAAI,EACAA,IAAAC,eAAAD,EAAA3H,iBACG,OAAA2H,GAAA,IAAAA,EAAAE,UACH,YAIA,OADAC,QAAAC,MAAA,iCACA,0BClRAlL,EAAAD,QAAA,SAAAoL,GAEAA,EAAAC,oBAAAC,YACAC,UAAA,EACAC,WAAA,KACAC,WAAA,IAAAL,EAAAC,oBAAAK,UAEAC,OAAA,WACA9E,KAAA0E,UAAA,EACA1E,KAAA4E,WAAAE,SACA9E,KAAA+E,cAAA/E,KAAAgF,kBAGAC,QAAA,WACAjF,KAAA0E,UAAA,EACA1E,KAAA4E,WAAAK,WAGAxG,UAAA,WACA,QAAAuB,KAAA0E,UAGAQ,eAAA,WACA,IAAAvH,EAUA,OARAA,EADA4G,EAAAY,OAAAC,0BACA,IAAAb,EAAAC,oBAAAa,SAEA,IAAAd,EAAAC,oBAAAc,SAGAC,YACA5H,IAAA6H,YAEA7H,GAGA8H,eAAA,WACAzF,KAAA+E,cAAA/E,KAAAkF,mBAGAF,cAAA,WACA,IAAArH,EAAAqC,KAAA2E,WAIA,OAHAhH,MAAA4H,YACA5H,IAAA6H,YAEA7H,GAGA+H,eAAA,SAAAlG,GAMA,IALA,IAAAmG,GACApB,EAAAC,oBAAAc,QACAf,EAAAC,oBAAAa,SACAd,EAAAC,oBAAAoB,YAEAjM,EAAA,EAAiBA,EAAAgM,EAAAhF,OAAmBhH,IACpC,GAAAgM,EAAAhM,GAAA4B,UAAAmK,eAAA,CACA,IAAA/H,EAAAgI,EAAAhM,GAAA4B,UAAAmK,eAAAlG,GACA,GAAA7B,EAAA,OAAAA,EAGA,aAGAkI,gBAAA,WACA7F,KAAA8F,SAAA9F,KAAA4E,YACA5E,KAAA+F,UAAA/F,KAAA4E,aAGAG,cAAA,SAAAb,GAEA,IAAA8B,GAAA,EACAhG,KAAA2E,YACA3E,KAAA2E,WAAAsB,UAAA/B,KACA8B,GAAA,GAGAhG,KAAAvB,cACAuH,GACAhG,KAAA8F,SAAA9F,KAAA2E,YAEA3E,KAAA2E,WAAAT,EACAlE,KAAA+F,UAAA/F,KAAA2E,YAAAqB,KAIAD,UAAA,SAAA7B,EAAAgC,GACAhC,KAAAiC,OACAjC,EAAAiC,MAAAD,IAGAJ,SAAA,SAAA5B,GACAA,KAAAkC,MACAlC,EAAAkC,QAIAC,eAAA,SAAA7G,GAEA,IAAA+E,EAAAC,oBAAA8B,WAGAtG,KAAAvB,eAGAe,KAAAjG,OAAAmG,OAGA6G,iBAAA,CAIA,IAAAC,EAAAxG,KAAA4E,WAEA6B,EAAAlC,EAAAC,oBAAAkC,UAAAC,oBAAAnH,GAEAoH,EAAA5G,KAAAgF,iBAEA,IADAT,EAAAC,oBAAAqC,OACAC,UAAA,aAAAL,EAAAjH,MAIAoH,EAEIA,EAAAG,YAAAN,GACJG,EAAAI,SAAAP,EAAAjH,GACIgH,EAAAO,YAAAN,IACJD,EAAAQ,SAAAP,EAAAjH,GAJAQ,KAAAyF,oBAQAwB,SAAA,KACAC,YAAA,WACA,cAAAlH,KAAAiH,UAEAE,MAAA,SAAAC,EAAAD,GAEAE,aAAArH,KAAAiH,UACAjH,KAAAiH,SAAAK,WAAA/C,EAAApJ,KAAA,WACA6E,KAAAiH,SAAA,KACAG,KACIpH,MAAAmH,GAAA,IAGJI,WAAA,WACAF,aAAArH,KAAAiH,gCChJA7N,EAAAD,QAAA,SAAAoL,IAEA,WACA,IAAAiD,KAEA,SAAAlB,IACA,QAAAkB,EAAA7G,OAGA,SAAA8G,EAAAvL,GACAoL,WAAA,WACAhB,KACA/B,EAAA4B,SACI,GAGJ,SAAAuB,EAAAxL,GACAqI,EAAAoD,YAAAzL,EAAA,UAAA0L,GACArD,EAAA7E,MAAAxD,EAAA,UAAA0L,GACAJ,EAAAnF,KAAAnG,GAIA,SAAA2L,IACA,IAAA3L,EAAAsL,EAAAM,MACA5L,GACAqI,EAAAoD,YAAAzL,EAAA,UAAA0L,GAEAH,IASA,SAAAG,EAAAlI,GACA,IACAC,GADAD,KAAAnG,OAAAmG,OACAqI,eANA,SAAA7L,GACA,OAAAA,GAAAsL,IAAA7G,OAAA,IAMAqH,CAAArI,IAEA4E,EAAAC,oBAAAoD,UAAAjI,EAAAD,GAGA,SAAAuI,IACAP,EAAAnD,EAAA2D,eAGA3D,EAAA4D,YAAA,aAAAF,GACA1D,EAAA4D,YAAA,kBAAAN,GACAtD,EAAA4D,YAAA,8BACAN,IACAI,MAIA1D,EAAA4D,YAAA,8BACAV,MAGAlD,EAAA4D,YAAA,0BAAAjM,GAYAkM,EAAAhM,SAAAwK,cAVAc,EAAAxL,KAEAqI,EAAA4D,YAAA,iCACAN,IAWAP,WAAA,WACAc,IACAA,EAAAjC,QACAiC,EAAA,OAEI,KAZJ,IAAAA,EAAA,KAeA7D,EAAAC,oBAAA8B,UAnFA,yBCFAlN,EAAAD,QAAA,SAAAoL,GACA,IAAA8D,EAAA5O,EAAA,GAEA8K,EAAAC,oBAAAa,SAAA,SAAAiD,EAAAC,GACA,IAAAD,EAAA,CACA,IAAAE,EAAAjE,EAAAkE,YAAAlE,EAAAY,OAAAuD,SACAF,EAAA,KACAF,EAAAE,EAAA,IAGAxI,KAAAsI,SACAtI,KAAA2I,YAAAJ,GAAA,EAEAhE,EAAAqE,aAAA5I,KAAAsI,UACAtI,KAAAuI,MAAAhE,EAAAsE,aAAA7I,KAAAsI,UAIA/D,EAAAC,oBAAAa,SAAA9J,UAAAgJ,EAAAuE,SACAvE,EAAAC,oBAAAc,SAEAyD,UAAA,KACAxD,QAAA,WAEA,OAAAhB,EAAAC,oBAAAc,QAAA/J,UAAAgK,QAAAzL,KAAAkG,SAAAuE,EAAAyE,iBAAAhJ,KAAA2I,cAEAnD,SAAA,WAEA,IAAA7H,EAAA4G,EAAAC,oBAAAc,QAAA/J,UAAAiK,SAAA1L,KAAAkG,MACAiJ,EAAAtL,EACA,GAAAA,aAAA4G,EAAAC,oBAAAc,QAAA,CAGA,IAFA,IAAA4D,EAAA3E,EAAAyE,iBACAT,EAAAvI,KAAA2I,YACAJ,GAAA,IACAW,EAAAX,IAEAA,IAEAW,EAAAX,KACAU,EAAA,IAAA1E,EAAAC,oBAAAa,SAAA1H,EAAA2K,OAAAC,IAIA,OAAAU,GAGAvD,eAAA,SAAAxB,GACA,IAAAK,EAAAY,OAAAC,0BACA,YAGA,IAAAkD,EAAA/D,EAAA4E,OAAAjF,GACA,GAAAK,EAAAqE,aAAAN,GAAA,CACA,IAAAC,EAAA,EACAa,EAAAf,EAAArF,gBAAAkB,EAAA,qBAMA,OAJAkF,IACAb,EAAA,EAAAa,EAAAjL,aAAA,sBAGA,IAAAoG,EAAAC,oBAAAa,SAAAiD,EAAAC,GAEA,aAIAc,QAAA,WACA,GAAA9E,EAAAqE,aAAA5I,KAAAsI,SAAA/D,EAAA+E,cAAAtJ,KAAAsI,QAAA,CACA,GAAA/D,EAAAY,OAAAoE,UAAA,CACA,IAAAC,EAAAjF,EAAAkF,MAAAjH,cAAA,cAAA+B,EAAAY,OAAAuE,eAAA,KAAA1J,KAAAsI,OAAA,MACA,OAAAkB,EAEAA,EAAAhH,cAAA,uBAAAxC,KAAA2I,YAAA,MADA,KAGA,OAAApE,EAAAoF,YAAA3J,KAAAsI,UAKAsB,MACAC,GAAA,WAEA,IAAAC,EAAA,KACAC,EAAAxF,EAAAyF,QAAAhK,KAAAsI,QAIAwB,EAHAC,EAGA,IAAAxF,EAAAC,oBAAAa,SAAA0E,EAAA/J,KAAA2I,aAFA,IAAApE,EAAAC,oBAAAoB,WAAA5F,KAAA2I,aAIA3I,KAAAiK,OAAAH,IAEAI,KAAA,WACA,IAAAC,EAAA5F,EAAA6F,QAAApK,KAAAsI,QACA6B,GACAnK,KAAAiK,OAAA,IAAA1F,EAAAC,oBAAAa,SAAA8E,EAAAnK,KAAA2I,eAGA7M,KAAA,WACAkE,KAAA2I,YAAA,GACA3I,KAAAiK,OAAA,IAAA1F,EAAAC,oBAAAa,SAAArF,KAAAsI,OAAAtI,KAAA2I,YAAA,KAGA5M,MAAA,WACA,IAAAsO,EAAA9F,EAAAyE,iBACAhJ,KAAA2I,YAAA0B,EAAA1J,OAAA,GACAX,KAAAiK,OAAA,IAAA1F,EAAAC,oBAAAa,SAAArF,KAAAsI,OAAAtI,KAAA2I,YAAA,KAIA2B,IAAA,WACA,IAAAD,EAAA9F,EAAAyE,iBACAhJ,KAAAiK,OAAA,IAAA1F,EAAAC,oBAAAa,SAAArF,KAAAsI,OAAA+B,EAAA1J,OAAA,KAEA4J,KAAA,WACAvK,KAAAiK,OAAA,IAAA1F,EAAAC,oBAAAa,SAAArF,KAAAsI,OAAA,KAEAkC,SAAA,WACAjG,EAAAkG,uBACAzK,KAAAiK,OAAA,IAAA1F,EAAAC,oBAAAa,SAAAd,EAAAmG,eAAAnG,EAAAkG,sBAAA,GAAAE,GAAA3K,KAAA2I,eAGAiC,OAAA,WACArG,EAAAkG,uBACAzK,KAAAiK,OAAA,IAAA1F,EAAAC,oBAAAa,SAAAd,EAAAmG,eAAA,GAAAC,GAAA3K,KAAA2I,kBAQApE,EAAAC,oBAAAa,SAAA9J,UAAAsP,QAAAtG,EAAAC,oBAAAc,QAAA/J,UAAAqO,MACArF,EAAAC,oBAAAa,SAAA9J,UAAAsP,QAAAtG,EAAAC,oBAAAa,SAAA9J,UAAAqO,0BCpIAxQ,EAAAD,QAAA,SAAAoL,GAEAA,EAAAC,oBAAAc,QAAA,SAAAgD,GACA,IAAAA,EAAA,CACA,IAAAE,EAAAjE,EAAAkE,YAAAlE,EAAAY,OAAAuD,SACAF,EAAA,KACAF,EAAAE,EAAA,IAGAxI,KAAAsI,SACA/D,EAAAqE,aAAA5I,KAAAsI,UACAtI,KAAAuI,MAAAhE,EAAAsE,aAAA7I,KAAAsI,UAIA/D,EAAAC,oBAAAc,QAAA/J,UAAAgJ,EAAAuE,SACAvE,EAAAC,oBAAAsG,YAEA/B,UAAA,KACAxD,QAAA,WACA,OAAAhB,EAAAqE,aAAA5I,KAAAsI,SAAA/D,EAAAsE,aAAA7I,KAAAsI,SAAA,GAEA9C,SAAA,WACA,IAAAjB,EAAAkG,sBAAA,CACA,IAAAM,EAAA,IAAAxG,EAAAC,oBAAAoB,WACA,OAAAmF,EAAAxF,UACAwF,EADA,KAIA,IAAAC,GAAA,EACA,GAAAzG,EAAAmG,eAAA1K,KAAAuI,MAAA,GACAyC,EAAAhL,KAAAuI,MAAA,OACM,GAAAhE,EAAAmG,eAAA1K,KAAAuI,MAAA,GACNyC,EAAAhL,KAAAuI,MAAA,OAGA,IADA,IAAAA,EAAAvI,KAAAuI,MACAA,GAAA,IACA,GAAAhE,EAAAmG,eAAAnC,GAAA,CACAyC,EAAAzC,EACA,MAEAA,IAIA,GAAAyC,GAAA,EACA,WAAAzG,EAAAC,oBAAAc,QAAAf,EAAAmG,eAAAM,GAAAL,KAKAjF,eAAA,SAAAxB,GACA,GAAAK,EAAAY,OAAAC,0BACA,YAGA,IAAAkD,EAAA/D,EAAA4E,OAAAjF,GACA,OAAAK,EAAAqE,aAAAN,GACA,IAAA/D,EAAAC,oBAAAc,QAAAgD,GAEA,MAIAe,QAAA,WACA,GAAA9E,EAAAqE,aAAA5I,KAAAsI,SAAA/D,EAAA+E,cAAAtJ,KAAAsI,QACA,OAAA/D,EAAAY,OAAAoE,UACAhF,EAAAkF,MAAAjH,cAAA,cAAA+B,EAAAY,OAAAuE,eAAA,KAAA1J,KAAAsI,OAAA,MAEA/D,EAAAoF,YAAA3J,KAAAsI,SAKAnC,MAAA,SAAAD,GACA,IAAAA,EAAA,CACA,IAIA+E,EAOAC,EAXAC,EAAA5G,EAAA6G,gBAAA7G,EAAA8G,QAAArL,KAAAsI,SACA7K,EAAA8G,EAAAY,OAAAmG,WACAC,EAAAhH,EAAAiH,iBAIAP,EADA1G,EAAAkH,MACAlH,EAAAkH,MAAA3O,YAEAyO,EAAAG,YAKAR,EADA3G,EAAAoH,YAAApH,EAAAqH,YACArH,EAAAoH,YAAApH,EAAAqH,YAAA7O,aAEAwO,EAAAM,aAGAV,EAAAtP,IAAA0P,EAAAnO,GAAA+N,EAAAtP,IAAA4B,EAAA8N,EAAAnO,EAAA8N,EAEA3G,EAAAuH,SAAA,KAAAX,EAAAtP,IAAA,EAAA4B,GAEM8G,EAAAY,OAAA4G,aAAAZ,EAAArP,KAAAyP,EAAAhO,GAAA4N,EAAArP,KAAAyP,EAAAhO,EAAA0N,IACN1G,EAAAuH,SAAAX,EAAArP,KAAAyI,EAAAY,OAAA6G,oBAKAzH,EAAAC,oBAAAsG,WAAAvP,UAAA4K,MAAAjG,MAAAF,MAAAkG,KAGA0D,MACAY,SAAA,WACAjG,EAAAkG,uBACAzK,KAAAiK,OAAA,IAAA1F,EAAAC,oBAAAc,QAAAf,EAAAmG,eAAAnG,EAAAkG,sBAAA,GAAAE,MAGAC,OAAA,WACArG,EAAAkG,uBACAzK,KAAAiK,OAAA,IAAA1F,EAAAC,oBAAAc,QAAAf,EAAAmG,eAAA,GAAAC,MAGAd,GAAA,WACA,IAAAC,EAAA,KACAC,EAAAxF,EAAAyF,QAAAhK,KAAAsI,QAIAwB,EAHAC,EAGA,IAAAxF,EAAAC,oBAAAc,QAAAyE,GAFA,IAAAxF,EAAAC,oBAAAoB,WAIA5F,KAAAiK,OAAAH,IAEAI,KAAA,WACA,IAAAC,EAAA5F,EAAA6F,QAAApK,KAAAsI,QACA6B,GACAnK,KAAAiK,OAAA,IAAA1F,EAAAC,oBAAAc,QAAA6E,KAIA8B,aAAA,WACA1H,EAAA2H,SAAAlM,KAAAsI,UAAA/D,EAAA8G,QAAArL,KAAAsI,QAAA6D,OACA5H,EAAA6H,KAAApM,KAAAsI,SAGA+D,WAAA,WACA9H,EAAA2H,SAAAlM,KAAAsI,SAAA/D,EAAA8G,QAAArL,KAAAsI,QAAA6D,OACA5H,EAAA+H,MAAAtM,KAAAsI,SAGAiE,cAAA,WACA,IAAAhI,EAAAiI,WAAAxM,MAAA,CAGA,IAAAyM,EAAAlI,EAAAmI,eAAA1M,KAAAsI,QACA,GAAA/D,EAAAqE,aAAA6D,KAAAlI,EAAAjB,UAAAtD,KAAAsI,OAAAmE,GACAlI,EAAA8G,QAAAoB,GACAN,OAAA,GAEA,IADA5H,EAAAoI,SAAA3M,KAAAsI,QAAA,EAAAmE,IAEAlI,EAAAqI,WAAA5M,KAAAsI,UAGAuE,aAAA,WACA,IAAAtI,EAAAiI,WAAAxM,MAAA,CAGA,IAAAuD,EAAAgB,EAAAuI,UAAA9M,KAAAsI,QACA,GAAA/D,EAAAqE,aAAArF,IAEA,IADAgB,EAAAoI,SAAA3M,KAAAsI,OAAA/D,EAAAsE,aAAAtF,GAAA,EAAAgB,EAAAuI,UAAAvJ,KAEAgB,EAAAqI,WAAA5M,KAAAsI,UAKAyE,MAAA,SAAAvN,GACA+E,EAAAyI,eAAAhN,KAAAsI,QAGA/D,EAAA0I,aAAAjN,KAAAsI,QAFA/D,EAAA2I,WAAAlN,KAAAsI,SAOA6E,YAAA,SAAA3N,GACA+E,EAAA+H,MAAAtM,KAAAsI,SAGA8E,aAAA,SAAA5N,GACA+E,EAAA6H,KAAApM,KAAAsI,SAIA+E,OAAA,SAAA7N,GACA+E,EAAAiI,WAAAxM,OAGAuE,EAAA+I,OAAAC,QAAA,OAAAvN,KAAAsI,SAIAkF,MAAA,WACAjJ,EAAAiI,WAAAxM,OAGAuE,EAAAkJ,aAAAzN,KAAAsI,SAIAoF,aAAA,WACAnJ,EAAAiI,WAAAxM,OAGAuE,EAAAoJ,cAAwB3N,KAAAsI,YAKxB/D,EAAAC,oBAAAc,QAAA/J,UAAAsP,QAAAtG,EAAAC,oBAAAc,QAAA/J,UAAAqO,4BCtNAxQ,EAAAD,QAAA,SAAAoL,GACA,IAAA8D,EAAA5O,EAAA,GAEA8K,EAAAC,oBAAAoB,WAAA,SAAA2C,GACAvI,KAAAuI,SAAA,GAGAhE,EAAAC,oBAAAoB,WAAArK,UAAAgJ,EAAAuE,SACAvE,EAAAC,oBAAAsG,YAEA/B,UAAA,KAEAxD,QAAA,WACA,SAAAhB,EAAAY,OAAAoE,WACAhF,EAAAkG,2BAGAlG,EAAAyE,iBAAAhJ,KAAAuI,SAAAhE,EAAAkG,wBAEAjF,SAAA,WACA,IAAAjB,EAAAY,OAAAoE,UACA,OAAAhF,EAAAkG,sBACA,IAAAlG,EAAAC,oBAAAc,QAEA,KAIA,IAFA,IAAA4D,EAAA3E,EAAAyE,iBACAT,EAAAvI,KAAAuI,MACAA,GAAA,IACAW,EAAAX,IAEAA,IAEA,OAAAW,EAAAX,GACA,IAAAhE,EAAAC,oBAAAoB,WAAA2C,GAEA,MAIA7C,eAAA,SAAAxB,GACA,IAAAkF,EAAAf,EAAA5F,gBAAAyB,EAAA,wBACA,GAAAkF,EAAA,CAEA,IADA,IAAAb,EAAA,EACAa,KAAAwE,iBACAxE,IAAAwE,gBACArF,GAAA,EAEA,WAAAhE,EAAAC,oBAAAoB,WAAA2C,GAEA,aAIAc,QAAA,WAEA,OADA9E,EAAAsJ,YAAA5L,WACAjC,KAAAuI,QAIAqB,MAEA9N,KAAA,WACAkE,KAAAuI,MAAA,GACAvI,KAAAiK,OAAA,IAAA1F,EAAAC,oBAAAoB,WAAA5F,KAAAuI,MAAA,KAGAxM,MAAA,WACA,IAAAsO,EAAA9F,EAAAyE,iBACAhJ,KAAAuI,MAAA8B,EAAA1J,OAAA,GACAX,KAAAiK,OAAA,IAAA1F,EAAAC,oBAAAoB,WAAA5F,KAAAuI,MAAA,KAGA2B,KAAA,WACA,IAAA4D,EACAtF,EAAAjE,EAAAkE,YAAAlE,EAAAY,OAAAuD,SACAF,EAAA,KACAsF,EAAAtF,EAAA,IAEAsF,IACAvJ,EAAAY,OAAAC,0BACApF,KAAAiK,OAAA,IAAA1F,EAAAC,oBAAAa,SAAAyI,EAAA9N,KAAAuI,QAEAvI,KAAAiK,OAAA,IAAA1F,EAAAC,oBAAAc,QAAAwI,MAKAxD,IAAA,WACA,IAAAD,EAAA9F,EAAAyE,iBACAhJ,KAAAiK,OAAA,IAAA1F,EAAAC,oBAAAoB,WAAAyE,EAAA1J,OAAA,KAEA4J,KAAA,WACAvK,KAAAiK,OAAA,IAAA1F,EAAAC,oBAAAoB,WAAA,KAKAmI,eAAA,WACA3R,SAAAwK,cACAoH,SAIAN,aAAA,WACAnJ,EAAAiI,WAAAxM,OAGAuE,EAAAoJ,cAAwB3N,KAAAsI,YAMxB/D,EAAAC,oBAAAoB,WAAArK,UAAAsP,QAAAtG,EAAAC,oBAAAoB,WAAArK,UAAAqO,0BClHAxQ,EAAAD,QAAA,SAAAoL,GAEAA,EAAAC,oBAAAsG,WAAA,aAGAvG,EAAAC,oBAAAsG,WAAAvP,UAAAgJ,EAAAuE,SACAvE,EAAAC,oBAAAyJ,cAEA1I,QAAA,WACA,UAEAC,SAAA,WACA,aAGAyE,OAAA,SAAAxG,GACAc,EAAAC,oBAAAC,WAAAM,cAAAtB,IAGAwC,UAAA,SAAAiI,GAEA,IAAAA,EAAA,SACA,QAAAvU,KAAAqG,KAAA,CACA,KAAAA,KAAArG,MAAAuU,EAAAvU,GAAA,SAEA,IAAAwU,KAAAnO,KAAArG,KAAAqG,KAAArG,GAAAyU,UACAC,KAAAH,EAAAvU,KAAAuU,EAAAvU,GAAAyU,UACA,GAAAC,GAAAF,EAAA,SACA,GAAAE,GAAAF,GAGA,GAAAD,EAAAvU,GAAAyU,YAAApO,KAAArG,GAAAyU,WACA,cAHA,GAAAF,EAAAvU,IAAAqG,KAAArG,GAAA,SAMA,UAGA0P,QAAA,aAEAlD,MAAA,WACA,IAAAxI,EAAAqC,KAAAqJ,UACA,GAAA1L,EAAA,CAGA,IAAA2Q,EAAA/J,EAAAC,oBAAAqC,QAEA,IAAAyH,EAAAxH,UAAA,iBAAAnJ,KAIAA,IACAA,EAAA4Q,aAAA,iBACA5Q,EAAA6Q,iBACA7Q,EAAA6Q,gBAAA,EACAjK,EAAA7E,MAAA/B,EAAA,iBAAA6B,GAGA,OAFAA,KAAAE,OACA+O,kBACA,IACO,IAGP9Q,EAAAwI,OAAAxI,EAAAwI,QAEAmI,EAAAxH,UAAA,WAAA9G,KAAAqJ,eAIAjD,KAAA,WACA,IAAAzI,EAAAqC,KAAAqJ,UACA1L,IACA4G,EAAAC,oBAAAqC,OACAC,UAAA,UAAAnJ,IACAA,EAAA4Q,aAAA,yCCzEAnV,EAAAD,QAAA,SAAAoL,GAEAA,EAAAC,oBAAAK,UAAA,aAGAN,EAAAC,oBAAAK,UAAAtJ,UAAAgJ,EAAAuE,SACAvE,EAAAC,oBAAAyJ,cAGA9H,MAAA,WACA5B,EAAA4B,SAGAC,KAAA,aAIAnB,QAAA,WACAV,EAAAmK,WAAAH,aAAA,iBAEAzJ,OAAA,WACAP,EAAAmK,YACAnK,EAAAmK,WAAAC,gBAAA,aAEAlQ,UAAA,WACA,OAAA8F,EAAAmK,WAAA3P,aAAA,aAGA6P,iBAAA,SAAAC,GACA,IAAAC,EAAAvK,EAAAwK,YAAAxK,EAAAiH,iBAAAjO,GACAyR,EAAAzK,EAAA0K,WACAC,EAAAL,EAAA,GAAAG,EAAAE,KAAAF,EAAAE,KACAJ,EAAAvK,EAAAuK,KAAAK,IAAAL,EAAAI,EAAAF,EAAAI,MACA7K,EAAAuH,SAAAvH,EAAA8K,YAAAP,KAGAQ,eAAA,SAAAT,GACA,IAAAhT,EAAA0I,EAAAiH,iBAAApO,EACA8R,EAAA3K,EAAAY,OAAAmG,WACA/G,EAAAuH,SAAA,KAAAjQ,GAAAgT,EAAA,QAAAK,IAGAtF,MACA2F,WAAA,SAAA/P,GACAQ,KAAA4O,kBAAA,IAEAY,YAAA,SAAAhQ,GACAQ,KAAA4O,iBAAA,IAEAa,SAAA,SAAAjQ,GACAQ,KAAAsP,gBAAA,IAEAI,WAAA,SAAAlQ,GACAQ,KAAAsP,eAAA,IAIAK,SAAA,WACApL,EAAAqL,MAAArL,EAAAqL,QAIAC,SAAA,WACAtL,EAAAuL,MAAAvL,EAAAuL,WAMAvL,EAAAC,oBAAAK,UAAAtJ,UAAAsP,QAAAtG,EAAAC,oBAAAK,UAAAtJ,UAAAqO,4BCrEAxQ,EAAAD,QAAA,SAAAoL,IAEA,WACA,IAAA8D,EAAA5O,EAAA,GACA8K,EAAAC,oBAAApE,kBAAAiI,EAAAjI,kBAEAmE,EAAAC,oBAAAoD,UAAA,SAAA3O,EAAAuG,GACA,MAAAA,EAAAuQ,QAAA,SAKA,IAHA,IAAAC,EAAAzL,EAAAC,oBAAApE,kBAAAnH,GACAgX,EAAA7T,SAAAwK,cACAsJ,GAAA,EACAvW,EAAA,EAAkBA,EAAAqW,EAAArP,OAAsBhH,IACxC,GAAAqW,EAAArW,IAAAsW,EAAA,CACAC,EAAAvW,EACA,MAIA,GAAA6F,EAAA2Q,UAEA,GAAAD,GAAA,GAEA,IAAAE,EAAAJ,IAAArP,OAAA,GACA,GAAAyP,EAGA,OAFAA,EAAAjK,QACA3G,EAAAiP,kBACA,QAMA,GAAAyB,GAAAF,EAAArP,OAAA,GAEA,IAAA0P,EAAAL,EAAA,GACA,GAAAK,EAGA,OAFAA,EAAAlK,QACA3G,EAAAiP,kBACA,EAKA,UA1CA,uBCFArV,EAAAD,QAAA,SAAAoL,GAEAA,EAAAC,oBAAAyJ,cACAlF,UAAA,KACAhC,YAAA,SAAAN,GACAzG,KAAA+I,YAAA/I,KAAA+I,cACA,IACAuH,EADA/L,EAAAC,oBAAAkC,UACA6J,QAAA9J,GAEA,OAAAzG,KAAA+I,UAAAuH,IAGAtJ,SAAA,SAAAP,EAAAjH,GACA,IAAAgR,EAAAxQ,KAAA+G,YAAAN,GACA,GAAA+J,EAAA,CAGA,QAFAjM,EAAAC,oBAAAqC,OAEAC,UAAA,kBAAAL,EAAAjH,IACA,OAGAgR,EAAA1W,KAAAkG,KAAAR,GAEAA,EAAAiP,eAAAjP,EAAAiP,iBACAjP,EAAAiR,aAAA,IAIAtV,KAAA,SAAAuV,EAAAF,GACAxQ,KAAA+I,YAAA/I,KAAA+I,cAKA,IAHA,IAAArC,EAAAnC,EAAAC,oBAAAkC,UAEAiK,EAAAjK,EAAAkK,MAAAF,GACA/W,EAAA,EAAkBA,EAAAgX,EAAAhQ,OAAqBhH,IACvCqG,KAAA+I,UAAArC,EAAA6J,QAAAI,EAAAhX,KAAA6W,GAGAK,OAAA,SAAAH,GAIA,IAHA,IAAAhK,EAAAnC,EAAAC,oBAAAkC,UAEAiK,EAAAjK,EAAAkK,MAAAF,GACA/W,EAAA,EAAkBA,EAAAgX,EAAAhQ,OAAqBhH,IACvCqG,KAAA+I,UAAArC,EAAA6J,QAAAI,EAAAhX,aACAqG,KAAA+I,UAAArC,EAAA6J,QAAAI,EAAAhX,MAKAkR,QAAA,SAAAiG,GACA,QAAAnX,KAAAmX,EACA9Q,KAAA7E,KAAAxB,EAAAmX,EAAAnX,KAGAoX,SAAA,WACA/Q,KAAA+I,YACA/I,KAAA+I,cACA/I,KAAA4J,MACA5J,KAAA6K,QAAA7K,KAAA4J,4BC1DAxQ,EAAAD,QAAA,SAAAoL,GAEAA,EAAAC,oBAAAkC,WACAsK,cAAA,WACA,OACAC,WACAC,OAAA,EACAC,KAAA,EACAC,MAAA,EACAC,MAAA,GAEAtB,QAAA,OAGAa,MAAA,SAAAF,GAIA,IAHA,IAAAC,KAEAW,EAAAtR,KAAAuR,eAAAvR,KAAAD,KAAA2Q,IACA/W,EAAA,EAAkBA,EAAA2X,EAAA3Q,OAAiBhH,IAAA,CAKnC,IAJA,IAAA6X,EAAAxR,KAAAyR,SAAAH,EAAA3X,IAEA8M,EAAAzG,KAAAgR,gBAEAU,EAAA,EAAmBA,EAAAF,EAAA7Q,OAAkB+Q,IACrC1R,KAAA2R,YAAAH,EAAAE,IACAjL,EAAAwK,UAAAO,EAAAE,KAAA,EACM1R,KAAA4R,YAAAJ,EAAAE,IACNjL,EAAAsJ,QAAA/P,KAAA4R,YAAAJ,EAAAE,IAEAjL,EAAAsJ,QAAAyB,EAAAE,GAAAG,WAAA,GAIAlB,EAAAtO,KAAAoE,GAEA,OAAAkK,GAGAhK,oBAAA,SAAAmL,GACA,IAAArL,EAAAzG,KAAAgR,gBACAvK,EAAAwK,UAAAC,QAAAY,EAAA3B,SACA1J,EAAAwK,UAAAE,MAAAW,EAAAC,OACAtL,EAAAwK,UAAAG,OAAAU,EAAAE,QACAvL,EAAAwK,UAAAI,OAAAS,EAAAG,QACAxL,EAAAsJ,QAAA+B,EAAAI,OAAAJ,EAAA/B,QAEAtJ,EAAAsJ,SAAA,IAAAtJ,EAAAsJ,SAAA,MAEAtJ,EAAAsJ,SAAA,IAGA,IAAAoC,EAAArS,OAAAsS,aAAA3L,EAAAsJ,SAIA,OAHAoC,IACA1L,EAAAsJ,QAAAoC,EAAArT,cAAA+S,WAAA,IAEApL,GAGA4L,iBAAA,SAAAP,GACA,OAAA9R,KAAAuQ,QAAAvQ,KAAA2G,oBAAAmL,KAGAvB,QAAA,SAAA9J,GACA,IAAA6L,KACA,QAAA3Y,KAAA8M,EAAAwK,UACAxK,EAAAwK,UAAAtX,IACA2Y,EAAAjQ,KAAA1I,GAKA,OAFA2Y,EAAAjQ,KAAAoE,EAAAsJ,SAEAuC,EAAA/R,KAAAP,KAAAuS,eAGAhB,eAAA,SAAAb,GACA,OAAAA,EAAArP,MAAArB,KAAAuS,eAEAd,SAAA,SAAAe,GACA,OAAAA,EAAAnR,MAAArB,KAAAyS,kBAEA1S,KAAA,SAAA2Q,GACA,OAAAA,EAAAzQ,QAAA,WAEAsS,aAAA,IACAE,gBAAA,IACAd,aACAT,MAAA,GACAC,IAAA,GACAC,KAAA,GACAC,MAAA,GAEAO,aACAc,UAAA,EACAC,IAAA,EACAnF,MAAA,GACAoF,IAAA,GACA7F,MAAA,GACAlD,GAAA,GACAK,KAAA,GACApO,KAAA,GACAC,MAAA,GACAwO,KAAA,GACAD,IAAA,GACAM,OAAA,GACAJ,SAAA,GACA6C,OAAA,GACAwF,OAAA,GACAC,KAAA,IACAC,GAAA,IACAC,GAAA,IACAC,GAAA,IACAC,GAAA,IACAC,GAAA,IACAC,GAAA,IACAC,GAAA,IACAC,GAAA,IACAC,GAAA,IACAC,IAAA,IACAC,IAAA,IACAC,IAAA,6BCvHA,WACA,IAAAC,EAAAla,EAAA,IACA,SAAA8K,GACAA,EAAAY,OAAAyO,qBAAA,EACArP,EAAAY,OAAAC,2BAAA,EAEAb,EAAAC,uBAEAD,EAAAuE,SAAA,WAGA,IAFA,IAAAwJ,EAAA7R,MAAAlF,UAAAmF,MAAA5G,KAAA+Z,UAAA,GACAC,KACAna,EAAA,EAAiBA,EAAA2Y,EAAA3R,OAAkBhH,IAAA,CACnC,IAAAyI,EAAAkQ,EAAA3Y,GAKA,QAAA8B,IAJA,mBAAA2G,IACAA,EAAA,IAAAA,GAGAA,EACA0R,EAAArY,GAAA2G,EAAA3G,GAGA,OAAAqY,GAGAra,EAAA,IAAAA,CAAA8K,GACA9K,EAAA,IAAAA,CAAA8K,GACA9K,EAAA,IAAAA,CAAA8K,GACA9K,EAAA,IAAAA,CAAA8K,GACA9K,EAAA,IAAAA,CAAA8K,GACA9K,EAAA,IAAAA,CAAA8K,GACA9K,EAAA,IAAAA,CAAA8K,GACA9K,EAAA,IAAAA,CAAA8K,GACA9K,EAAA,IAAAA,CAAA8K,GACA9K,EAAA,IAAAA,CAAA8K,GAEA,IAAA8D,EAAA5O,EAAA,IAEA,WACA,IAAAgL,EAAAF,EAAAC,oBAAAC,WAEAA,EAAAsP,cAAA,SAAApJ,GACA,IAAAhN,EAAA8G,EAAAE,WACA,OAAAhH,aAAA4G,EAAAC,oBAAAc,SAAA3H,aAAA4G,EAAAC,oBAAAa,WACA1H,EAAA2K,QAAAqC,GAOA,IAAAtE,EAAA,SAAA7G,GACA,GAAA+E,EAAAY,OAAAyO,oBAEA,OAAAnP,EAAA4B,eAAA7G,IAGAwU,EAAA,SAAAxU,GACA,GAAAiF,EAAAwP,gBAGA,OAFAzU,EAAAiP,iBACAlK,EAAAmK,WAAAtI,QACA,EAEK3B,EAAAyC,eAELzC,EAAAoB,mBAKAqO,EAAA,WACA,GAAAzP,EAAAhG,YAAA,CAGA,IAAAkG,EAAAF,EAAAO,gBACA,GAAAL,EAAA,CAGA,IACA9I,EAAAC,EADAqY,EAAAxP,EAAA0E,UAEA8K,KAAA5X,aACAV,EAAAsY,EAAA5X,WAAAC,UACAV,EAAAqY,EAAA5X,WAAAG,YAIAiI,EAAAwB,OAAA,GAEAgO,KAAA5X,aACA4X,EAAA5X,WAAAC,UAAAX,EACAsY,EAAA5X,WAAAG,WAAAZ,MAmCA,SAAAsY,EAAA5U,GACA,IAAA+E,EAAAY,OAAAyO,oBAAA,SAEA,IAAA7N,EACAsO,EAAA5P,EAAAiB,eAAAlG,GACA6U,IAEA5P,EAAAE,sBAAAJ,EAAAC,oBAAAa,UAAAgD,EAAA/E,UAAA9D,EAAAG,OAAA4E,EAAAkH,SACA4I,EAAA,IAAA9P,EAAAC,oBAAAa,SAAAgP,EAAA/L,OAAA7D,EAAAE,WAAAgE,cAEA5C,EAAAsO,GAEAtO,EACAtB,EAAAhG,YAGAgG,EAAA0C,MAAA,WACA1C,EAAAM,cAAAgB,KAHAtB,EAAAE,WAAAoB,GAQAtB,EAAAwP,iBAAA,EACA3M,WAAA,WACA7C,EAAAwP,iBAAA,GACM,MAvDN1P,EAAA4D,YAAA,0BACA5D,EAAAY,OAAAyO,qBACAM,MAGA3P,EAAA4D,YAAA,2BACA5D,EAAAoD,YAAAvL,SAAA,UAAAiK,GACA9B,EAAAoD,YAAApD,EAAAmK,WAAA,QAAAsF,GACAzP,EAAAoD,YAAApD,EAAAmK,WAAA,YAAA0F,GAEA7P,EAAAY,OAAAyO,qBAEArP,EAAA7E,MAAAtD,SAAA,UAAAiK,GACA9B,EAAA7E,MAAA6E,EAAAmK,WAAA,QAAAsF,GACAzP,EAAA7E,MAAA6E,EAAAmK,WAAA,YAAA0F,GACA7P,EAAAmK,WAAAH,aAAA,iBAGAhK,EAAAmK,WAAAC,gBAAA,cAyCA,IAAA2F,EAAA/P,EAAA4D,YAAA,0BAaA,GAXA5D,EAAAgQ,YAAAD,GAEA/P,EAAAiQ,MAAAC,WAAAtM,YAAA,0BAAAwC,GACA,GAAApG,EAAAY,OAAAyO,qBAAAnP,EAAAhG,YAAA,CACA,IAAAiW,EAAAjQ,EAAAO,gBACA0P,KAAApM,QAAAqC,GACAuJ,OAKA3P,EAAAoQ,cAAA,CACA,IAAAC,EAAArQ,EAAAoQ,cAAAE,aACAtQ,EAAAoQ,cAAAE,aAAA,SAAAC,EAAAC,GACA,GAAAxQ,EAAAY,OAAAyO,qBAAAnP,EAAAhG,YAAA,CACA,IAAAiW,EAAAjQ,EAAAO,gBACA,GAAA0P,QAAA7R,IAAA6R,EAAApM,OAAA,CAEA,IADA,IAAA0M,GAAA,EACArb,EAAA,EAAsBA,EAAAob,EAAApU,OAAkBhH,IACxC,GAAAob,EAAApb,GAAAgR,IAAA+J,EAAApM,QAAAyM,EAAApb,GAAAsb,WAAA,CACAD,GAAA,EACA,MAGAA,GACAD,EAAA1S,KAAAkC,EAAA8G,QAAAqJ,EAAApM,UAMA,OAFAsM,EAAA1U,MAAAF,KAAA6T,eASAtP,EAAA4D,YAAA,0BAAAwC,EAAAuK,GACA,IAAA3Q,EAAAY,OAAAyO,oBAAA,SACA,GAAAnP,EAAAhG,YAAA,CAEA,IAAAkK,EAAA,EACAhL,EAAA8G,EAAAE,WACAhH,aAAA4G,EAAAC,oBAAAa,WACAsD,EAAAhL,EAAAgL,aAEA,IAAAwM,EArFA5Q,EAAAY,OAAAC,0BACAb,EAAAC,oBAAAa,SAEAd,EAAAC,oBAAAc,QAoFAb,EAAAM,cAAA,IAAAoQ,EAAAxK,EAAAhC,OAMApE,EAAA4D,YAAA,0BAAAiN,EAAAC,GACA,IAAA9Q,EAAAY,OAAAyO,oBAAA,SAEA,IAAAjW,EAAA8G,EAAAE,WAKA,OAJAF,EAAAsP,cAAAqB,KACAzX,EAAA2K,OAAA+M,IAGA,IAaA,IAAAC,EAAAC,YAAA,WACA,GAAAhR,EAAAY,OAAAyO,oBAAA,CAEA,IAAA9O,EACAsD,EAdA,WAEA,IAAAxB,EAAAxK,SAAAwK,cAKA,OAJAA,IAAAxK,SAAAD,MAAAC,SAAAoZ,eACA5O,EAAAxK,SAAAoZ,eAAAzP,WAAA3J,SAAAD,MAGAyK,EAOA5B,GAEAzB,EAAAgB,EAAAmK,WAEA,IAAAtG,GAAA7D,EAAAkR,YAAArN,EAAA,wBACAtD,GAAA,MACK,CACL,KAAAsD,GAAA7E,GAAA6E,GACAA,IAAA7L,WAIAuI,EADAsD,GAAA7E,EAOAuB,IAAAL,EAAAhG,YACAgG,EAAAK,UACKA,GAAAL,EAAAhG,aACLgG,EAAAQ,YAGI,KAmBJ,SAAAyQ,EAAA5a,GACA,IAAA6a,GACApR,QAAAC,oBAAAK,UACA+Q,WAAArR,EAAAC,oBAAAoB,WACAkI,QAAAvJ,EAAAC,oBAAAc,QACAuQ,SAAAtR,EAAAC,oBAAAa,UAGA,OAAAsQ,EAAA7a,IAAA6a,EAAApR,MAGA,SAAAuR,EAAAC,GAEA,IADA,IAAA1L,EAAA9F,EAAAyE,iBACArP,EAAA,EAAmBA,EAAA0Q,EAAA1J,OAAoBhH,IACvC,GAAA0Q,EAAA1Q,GAAAO,MAAA6b,EACA,OAAApc,EAGA,SAnCA4K,EAAA4D,YAAA,uBACA6N,cAAAV,KAqCA,IAAAW,KACAtC,EAAAsC,GACA1R,EAAA2R,MAAAD,GACAE,YAAA,SAAAzF,EAAAF,EAAA4F,GACA,IAAAC,EAAAX,EAAAU,GACAC,GACAA,EAAA9a,UAAAJ,KAAAuV,EAAAF,IAGA8F,mBAAA,SAAA5F,EAAA0F,GACA,IAAAzF,EAAApM,EAAAC,oBAAAkC,UAAAkK,MAAAF,GACA,GAAAC,EAAAhQ,OACA,OAAAX,KAAAuW,kBAAA5F,EAAA,GAAAyF,IAGAG,kBAAA,SAAA9P,EAAA2P,GACA,IAAAC,EAAAX,EAAAU,GACA,GAAAC,GACA5P,EACA,OAAA4P,EAAA9a,UAAAwL,YAAAN,IAIA+P,eAAA,SAAA9F,EAAA0F,GACA,IAAAC,EAAAX,EAAAU,GACAC,GACAA,EAAA9a,UAAAsV,OAAAH,IAGAvK,MAAA,SAAAhB,GACA,IAEAxH,EAFA8Y,EAAAtR,IAAAsR,KAAA,KACAC,EAAAhB,EAAAe,GAEA,OAAAA,GACA,eACA9Y,EAAA,IAAA+Y,EAAAvR,EAAAwF,GAAAmL,EAAA3Q,EAAAwR,SACA,MACA,cACAhZ,EAAA,IAAA+Y,EAAAvR,EAAAwF,IACA,MACA,iBACAhN,EAAA,IAAA+Y,EAAAZ,EAAA3Q,EAAAwR,SAMAlS,EAAA0C,MAAA,WACAxJ,EACA8G,EAAAM,cAAApH,IAEA8G,EAAAK,SACAL,EAAAO,gBAKAP,EAAAyC,eACAzC,EAAAK,SAJAL,EAAAgB,qBAaAT,cAAA,WACA,GAAAP,EAAAhG,YAAA,CACA,IAAAd,EAAA8G,EAAAO,gBACAoR,EAxGA,SAAAhU,GACA,OAAAA,aAAAmC,EAAAC,oBAAAK,UACA,QACKzC,aAAAmC,EAAAC,oBAAAoB,WACL,aACKxD,aAAAmC,EAAAC,oBAAAc,QACL,UACKlD,aAAAmC,EAAAC,oBAAAa,SACL,WAEA,KA8FAuR,CAAAjZ,GACA0M,EAAA9F,EAAAyE,iBACA,OAAAoN,GACA,eACA,OAAgBK,KAAA,WAAA9L,GAAAhN,EAAA2K,OAAAqO,OAAAtM,EAAA1M,EAAAgL,aAAAzO,MAChB,cACA,OAAgBuc,KAAA,UAAA9L,GAAAhN,EAAA2K,QAChB,iBACA,OAAgBmO,KAAA,aAAAE,OAAAtM,EAAA1M,EAAA4K,OAAArO,OAGhB,eAIAqK,EAAAC,oBAAAqC,OAAAoP,EAEA1R,EAAAsS,IAAAC,mBAAAb,EACA1R,EAAA4B,MAAA,WACA8P,EAAA9P,SAEA5B,EAAA4R,YAAAF,EAAAE,YACA5R,EAAA+R,mBAAAL,EAAAK,mBACA/R,EAAAiS,eAAAP,EAAAO,eAnWA,GAyWAO,CAAAxS,OA9YA,oBCAA,IAAAyS,EAAA,WACAhX,KAAAiX,cACAjX,KAAAkX,cAAA,GAGAF,EAAAzb,WACA4b,aAAA,WACAnX,KAAAkX,cAAA,GAEAE,WAAA,WACApX,KAAAkX,cAAA,IAIA,IAAAG,EAAA,SAAAjV,GACA,IAAAkV,KACAC,EAAA,WAEA,IADA,IAAAzD,GAAA,EACAna,EAAA,EAAiBA,EAAA2d,EAAA3W,OAAsBhH,IACvC,GAAA2d,EAAA3d,GAAA,CACA,IAAA6d,EAAAF,EAAA3d,GAAAuG,MAAAkC,EAAAyR,WACAC,KAAA0D,EAGA,OAAA1D,GAUA,OARAyD,EAAAE,SAAA,SAAAtU,GACA,4BACAmU,EAAAjV,KAAAc,GAAA,GAGAoU,EAAAG,YAAA,SAAA/M,GACA2M,EAAA3M,GAAA,MAEA4M,GAqDAne,EAAAD,QAlDA,SAAAiJ,GAEA,IAAAuV,EAAA,IAAAX,EACA5U,EAAA+F,YAAA,SAAAjO,EAAA0d,EAAAC,GAKA,OAJA3d,EAAA,MAAAA,EAAA4E,cACA6Y,EAAAzd,KACAyd,EAAAzd,GAAAmd,EAAAQ,GAAA7X,OAEA9F,EAAA,IAAAyd,EAAAzd,GAAAud,SAAAG,IAEAxV,EAAA0V,UAAA,SAAA1Q,EAAAyQ,GACA7X,KAAAmI,YAAA,aAAAf,EAAAyQ,IAGAzV,EAAA0E,UAAA,SAAA5M,EAAA6d,EAAAF,GACA,GAAAF,EAAAT,aAAA,SAEA,IAAAc,EAAA,MAAA9d,EAAA4E,cAMA,OAJA6Y,EAAA,eACAA,EAAA,cAAAzX,MAAA2X,GAAA7X,MAAA9F,GAAA+d,OAAAF,KAGAJ,EAAAK,IACAL,EAAAK,GAAA9X,MAAA2X,GAAA7X,KAAA+X,IAGA3V,EAAA8V,WAAA,SAAAhe,GACA,QAAAyd,EAAA,MAAAzd,EAAA4E,gBAEAsD,EAAAmS,YAAA,SAAA5J,GACA,GAAAA,EAAA,CACA,IAAAwN,EAAAxN,EAAAtJ,MAAA,KACA+W,EAAAD,EAAA,GACAE,EAAAF,EAAA,GAEAR,EAAAS,IACAT,EAAAS,GAAAV,YAAAW,KAIAjW,EAAAkW,gBAAA,WACA,QAAApe,KAAAyd,EACA,IAAAzd,EAAAiF,QAAA,eACAwY,EAAAzd", "file": "ext/dhtmlxgantt_keyboard_navigation.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"ext/dhtmlxgantt_keyboard_navigation\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ext/dhtmlxgantt_keyboard_navigation\"] = factory();\n\telse\n\t\troot[\"ext/dhtmlxgantt_keyboard_navigation\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/codebase/\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 238);\n", "//returns position of html element on the page\nfunction elementPosition(elem) {\n\tvar top=0, left=0, right=0, bottom=0;\n\tif (elem.getBoundingClientRect) { //HTML5 method\n\t\tvar box = elem.getBoundingClientRect();\n\t\tvar body = document.body;\n\t\tvar docElem = (document.documentElement ||\n\t\t\tdocument.body.parentNode ||\n\t\t\tdocument.body);\n\n\t\tvar scrollTop = window.pageYOffset || docElem.scrollTop || body.scrollTop;\n\t\tvar scrollLeft = window.pageXOffset || docElem.scrollLeft || body.scrollLeft;\n\t\tvar clientTop = docElem.clientTop || body.clientTop || 0;\n\t\tvar clientLeft = docElem.clientLeft || body.clientLeft || 0;\n\t\ttop  = box.top +  scrollTop - clientTop;\n\t\tleft = box.left + scrollLeft - clientLeft;\n\n\t\tright = document.body.offsetWidth - box.right;\n\t\tbottom = document.body.offsetHeight - box.bottom;\n\t} else { //fallback to naive approach\n\t\twhile(elem) {\n\t\t\ttop = top + parseInt(elem.offsetTop,10);\n\t\t\tleft = left + parseInt(elem.offsetLeft,10);\n\t\t\telem = elem.offsetParent;\n\t\t}\n\n\t\tright = document.body.offsetWidth - elem.offsetWidth - left;\n\t\tbottom = document.body.offsetHeight - elem.offsetHeight - top;\n\t}\n\treturn { y: Math.round(top), x: Math.round(left), width:elem.offsetWidth, height:elem.offsetHeight, right: Math.round(right), bottom: Math.round(bottom) };\n}\n\nfunction isVisible(node){\n\tvar display = false,\n\t\tvisibility = false;\n\tif(window.getComputedStyle){\n\t\tvar style = window.getComputedStyle(node, null);\n\t\tdisplay = style[\"display\"];\n\t\tvisibility = style[\"visibility\"];\n\t}else if(node.currentStyle){\n\t\tdisplay = node.currentStyle[\"display\"];\n\t\tvisibility = node.currentStyle[\"visibility\"];\n\t}\n\treturn (display != \"none\" && visibility != \"hidden\");\n}\n\nfunction hasNonNegativeTabIndex(node){\n\treturn !isNaN(node.getAttribute(\"tabindex\")) && (node.getAttribute(\"tabindex\")*1 >= 0);\n}\n\nfunction hasHref(node){\n\tvar canHaveHref = {\"a\": true, \"area\": true};\n\tif(canHaveHref[node.nodeName.loLowerCase()]){\n\t\treturn !!node.getAttribute(\"href\");\n\t}\n\treturn true;\n}\n\nfunction isEnabled(node){\n\tvar canDisable = {\"input\":true, \"select\":true, \"textarea\":true, \"button\":true, \"object\":true};\n\tif(canDisable[node.nodeName.toLowerCase()]){\n\t\treturn !node.hasAttribute(\"disabled\");\n\t}\n\n\treturn true;\n}\n\nfunction getFocusableNodes(root){\n\tvar nodes = root.querySelectorAll([\n\t\t\"a[href]\",\n\t\t\"area[href]\",\n\t\t\"input\",\n\t\t\"select\",\n\t\t\"textarea\",\n\t\t\"button\",\n\t\t\"iframe\",\n\t\t\"object\",\n\t\t\"embed\",\n\t\t\"[tabindex]\",\n\t\t\"[contenteditable]\"\n\t].join(\", \"));\n\n\tvar nodesArray = Array.prototype.slice.call(nodes, 0);\n\tfor(var i = 0; i < nodesArray.length; i++){\n\t\tvar node = nodesArray[i];\n\t\tvar isValid = (hasNonNegativeTabIndex(node)  || isEnabled(node) || hasHref(node)) && isVisible(node);\n\t\tif(!isValid){\n\t\t\tnodesArray.splice(i, 1);\n\t\t\ti--;\n\t\t}\n\t}\n\treturn nodesArray;\n}\n\nfunction getScrollSize(){\n\tvar div = document.createElement(\"div\");\n\tdiv.style.cssText=\"visibility:hidden;position:absolute;left:-1000px;width:100px;padding:0px;margin:0px;height:110px;min-height:100px;overflow-y:scroll;\";\n\n\tdocument.body.appendChild(div);\n\tvar width = div.offsetWidth-div.clientWidth;\n\tdocument.body.removeChild(div);\n\n\treturn width;\n}\n\nfunction getClassName(node){\n\tif(!node) return \"\";\n\n\tvar className = node.className || \"\";\n\tif(className.baseVal)//'className' exist but not a string - IE svg element in DOM\n\t\tclassName = className.baseVal;\n\n\tif(!className.indexOf)\n\t\tclassName = \"\";\n\n\treturn _trimString(className);\n}\n\nfunction addClassName(node, className){\n\tif (className && node.className.indexOf(className) === -1) {\n\t\tnode.className += \" \" + className;\n\t}\n}\n\nfunction removeClassName(node, name) {\n\tname = name.split(\" \");\n\tfor (var i = 0; i < name.length; i++) {\n\t\tvar regEx = new RegExp(\"\\\\s?\\\\b\" + name[i] + \"\\\\b(?![-_.])\", \"\");\n\t\tnode.className = node.className.replace(regEx, \"\");\n\t}\n}\n\nfunction hasClass(element, className){\n\tif ('classList' in element) {\n\t\treturn element.classList.contains(className);\n\t} else { \n\t\treturn new RegExp(\"\\\\b\" + className + \"\\\\b\").test(element.className);\n\t}\n}\n\nfunction toNode(node) {\n\tif (typeof node === \"string\") {\n\t\treturn (document.getElementById(node) || document.querySelector(node) || document.body);\n\t}\n\treturn node || document.body;\n}\n\nvar _slave = document.createElement(\"div\");\nfunction insert(node, newone) {\n\t_slave.innerHTML = newone;\n\tvar child = _slave.firstChild;\n\tnode.appendChild(child);\n\treturn child;\n}\n\nfunction remove(node) {\n\tif (node && node.parentNode) {\n\t\tnode.parentNode.removeChild(node);\n\t}\n}\n\nfunction getChildren(node, css) {\n\tvar ch = node.childNodes;\n\tvar len = ch.length;\n\tvar out = [];\n\tfor (var i = 0; i < len; i++) {\n\t\tvar obj = ch[i];\n\t\tif (obj.className && obj.className.indexOf(css) !== -1) {\n\t\t\tout.push(obj);\n\t\t}\n\t}\n\treturn out;\n}\n\nfunction getTargetNode(e){\n\tvar trg;\n\tif (e.tagName)\n\t\ttrg = e;\n\telse {\n\t\te=e||window.event;\n\t\ttrg=e.target||e.srcElement;\n\t}\n\treturn trg;\n}\n\nfunction locateAttribute(e, attribute) {\n\tif(!attribute) return;\n\n\tvar trg = getTargetNode(e);\n\n\twhile (trg){\n\t\tif (trg.getAttribute){\t//text nodes has not getAttribute\n\t\t\tvar test = trg.getAttribute(attribute);\n\t\t\tif (test) return trg;\n\t\t}\n\t\ttrg=trg.parentNode;\n\t}\n\treturn null;\n}\n\nfunction _trimString(str){\n\tvar func = String.prototype.trim || function(){ return this.replace(/^\\s+|\\s+$/g, \"\"); };\n\treturn func.apply(str);\n}\n\nfunction locateClassName(e, classname, strict){\n\tvar trg = getTargetNode(e);\n\tvar css = \"\";\n\n\tif(strict === undefined)\n\t\tstrict = true;\n\n\twhile (trg){\n\t\tcss = getClassName(trg);\n\t\tif(css){\n\t\t\tvar ind = css.indexOf(classname);\n\t\t\tif (ind >= 0){\n\t\t\t\tif (!strict)\n\t\t\t\t\treturn trg;\n\n\t\t\t\t//check that we have exact match\n\t\t\t\tvar left = (ind === 0) || (!_trimString(css.charAt(ind - 1)));\n\t\t\t\tvar right = ((ind + classname.length >= css.length)) || (!_trimString(css.charAt(ind + classname.length)));\n\n\t\t\t\tif (left && right)\n\t\t\t\t\treturn trg;\n\t\t\t}\n\t\t}\n\t\ttrg=trg.parentNode;\n\t}\n\treturn null;\n}\n\n/*\nevent position relatively to DOM element\n */\nfunction getRelativeEventPosition(ev, node){\n\tvar d = document.documentElement;\n\tvar box = elementPosition(node);\n\n\treturn {\n\t\tx: ev.clientX + d.scrollLeft - d.clientLeft - box.x + node.scrollLeft,\n\t\ty: ev.clientY + d.scrollTop - d.clientTop - box.y + node.scrollTop\n\t};\n}\n\nfunction isChildOf(child, parent){\n\tif(!child || !parent){\n\t\treturn false;\n\t}\n\n\twhile(child && child != parent) {\n\t\tchild = child.parentNode;\n\t}\n\n\treturn child === parent;\n}\n\nfunction closest(element, selector){\n\tif(element.closest){\n\t\treturn element.closest(selector);\n\t}else if(element.matches || element.msMatchesSelector || element.webkitMatchesSelector){\n\t\tvar el = element;\n\t\tif (!document.documentElement.contains(el)) return null;\n\t\tdo {\n\t\t\tvar method = el.matches || el.msMatchesSelector || el.webkitMatchesSelector;\n\n\t\t\tif (method.call(el, selector)) return el;\n\t\t\tel = el.parentElement || el.parentNode;\n\t\t} while (el !== null && el.nodeType === 1); \n\t\treturn null;\n\t}else{\n\t\t// eslint-disable-next-line no-console\n\t\tconsole.error(\"Your browser is not supported\");\n\t\treturn null;\n\t}\n}\n\nmodule.exports = {\n\tgetNodePosition: elementPosition,\n\tgetFocusableNodes: getFocusableNodes,\n\tgetScrollSize: getScrollSize,\n\tgetClassName: getClassName,\n\taddClassName: addClassName,\n\tremoveClassName: removeClassName,\n\tinsertNode: insert,\n\tremoveNode: remove,\n\tgetChildNodes: getChildren,\n\ttoNode: toNode,\n\tlocateClassName:locateClassName,\n\tlocateAttribute: locateAttribute,\n\tgetTargetNode: getTargetNode,\n\tgetRelativeEventPosition: getRelativeEventPosition,\n\tisChildOf: isChildOf,\n\thasClass: hasClass,\n\tclosest: closest\n};", "module.exports = function(gantt) {\n\n\tgantt.$keyboardNavigation.dispatcher = {\n\t\tisActive: false,\n\t\tactiveNode: null,\n\t\tglobalNode: new gantt.$keyboardNavigation.GanttNode(),\n\n\t\tenable: function () {\n\t\t\tthis.isActive = true;\n\t\t\tthis.globalNode.enable();\n\t\t\tthis.setActiveNode(this.getActiveNode());\n\t\t},\n\n\t\tdisable: function () {\n\t\t\tthis.isActive = false;\n\t\t\tthis.globalNode.disable();\n\t\t},\n\n\t\tisEnabled: function () {\n\t\t\treturn !!this.isActive;\n\t\t},\n\n\t\tgetDefaultNode: function () {\n\t\t\tvar node;\n\t\t\tif (gantt.config.keyboard_navigation_cells) {\n\t\t\t\tnode = new gantt.$keyboardNavigation.TaskCell();\n\t\t\t} else {\n\t\t\t\tnode = new gantt.$keyboardNavigation.TaskRow();\n\t\t\t}\n\n\t\t\tif (!node.isValid()) {\n\t\t\t\tnode = node.fallback();\n\t\t\t}\n\t\t\treturn node;\n\t\t},\n\n\t\tsetDefaultNode: function () {\n\t\t\tthis.setActiveNode(this.getDefaultNode());\n\t\t},\n\n\t\tgetActiveNode: function () {\n\t\t\tvar node = this.activeNode;\n\t\t\tif (node && !node.isValid()) {\n\t\t\t\tnode = node.fallback();\n\t\t\t}\n\t\t\treturn node;\n\t\t},\n\n\t\tfromDomElement: function(e){\n\t\t\tvar inputs = [\n\t\t\t\tgantt.$keyboardNavigation.TaskRow,\n\t\t\t\tgantt.$keyboardNavigation.TaskCell,\n\t\t\t\tgantt.$keyboardNavigation.HeaderCell\n\t\t\t];\n\t\t\tfor(var i = 0; i < inputs.length; i++){\n\t\t\t\tif(inputs[i].prototype.fromDomElement){\n\t\t\t\t\tvar node = inputs[i].prototype.fromDomElement(e);\n\t\t\t\t\tif(node) return node;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn null;\n\t\t},\n\n\t\tfocusGlobalNode: function () {\n\t\t\tthis.blurNode(this.globalNode);\n\t\t\tthis.focusNode(this.globalNode);\n\t\t},\n\n\t\tsetActiveNode: function (el) {\n\t\t\t//console.trace()\n\t\t\tvar focusChanged = true;\n\t\t\tif (this.activeNode) {\n\t\t\t\tif (this.activeNode.compareTo(el)) {\n\t\t\t\t\tfocusChanged = false;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (this.isEnabled()) {\n\t\t\t\tif(focusChanged)\n\t\t\t\t\tthis.blurNode(this.activeNode);\n\n\t\t\t\tthis.activeNode = el;\n\t\t\t\tthis.focusNode(this.activeNode, !focusChanged);\n\t\t\t}\n\t\t},\n\n\t\tfocusNode: function (el, keptFocus) {\n\t\t\tif (el && el.focus) {\n\t\t\t\tel.focus(keptFocus);\n\t\t\t}\n\t\t},\n\t\tblurNode: function (el) {\n\t\t\tif (el && el.blur) {\n\t\t\t\tel.blur();\n\t\t\t}\n\t\t},\n\n\t\tkeyDownHandler: function (e) {\n\n\t\t\tif (gantt.$keyboardNavigation.isModal())\n\t\t\t\treturn;\n\n\t\t\tif (!this.isEnabled())\n\t\t\t\treturn;\n\n\t\t\te = e || window.event;\n\n\n\t\t\tif(e.defaultPrevented){\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tvar ganttNode = this.globalNode;\n\n\t\t\tvar command = gantt.$keyboardNavigation.shortcuts.getCommandFromEvent(e);\n\n\t\t\tvar activeElement = this.getActiveNode();\n\t\t\tvar eventFacade = gantt.$keyboardNavigation.facade;\n\t\t\tif(eventFacade.callEvent(\"onKeyDown\", [command, e]) === false){\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (!activeElement) {\n\t\t\t\tthis.setDefaultNode();\n\t\t\t} else if (activeElement.findHandler(command)) {\n\t\t\t\tactiveElement.doAction(command, e);\n\t\t\t} else if (ganttNode.findHandler(command)) {\n\t\t\t\tganttNode.doAction(command, e);\n\t\t\t}\n\n\t\t},\n\t\t_timeout: null,\n\t\tawaitsFocus: function(){\n\t\t\treturn this._timeout !== null;\n\t\t},\n\t\tdelay: function(callback, delay){\n\n\t\t\tclearTimeout(this._timeout);\n\t\t\tthis._timeout = setTimeout(gantt.bind(function(){\n\t\t\t\tthis._timeout = null;\n\t\t\t\tcallback();\n\t\t\t}, this)  , delay || 1);\n\n\t\t},\n\t\tclearDelay: function(){\n\t\t\tclearTimeout(this._timeout);\n\t\t}\n\t};\n\n};", "module.exports = function(gantt) {\n\n\t(function () {\n\t\tvar modalsStack = [];\n\n\t\tfunction isModal() {\n\t\t\treturn !!modalsStack.length;\n\t\t}\n\n\t\tfunction afterPopup(box) {\n\t\t\tsetTimeout(function () {\n\t\t\t\tif (!isModal())\n\t\t\t\t\tgantt.focus();\n\t\t\t}, 1);\n\t\t}\n\n\t\tfunction startModal(box) {\n\t\t\tgantt.eventRemove(box, \"keydown\", trapFocus);\n\t\t\tgantt.event(box, \"keydown\", trapFocus);\n\t\t\tmodalsStack.push(box);\n\t\t\t//gantt.$keyboardNavigation.dispatcher.disable();\n\t\t}\n\n\t\tfunction endModal() {\n\t\t\tvar box = modalsStack.pop();\n\t\t\tif (box) {\n\t\t\t\tgantt.eventRemove(box, \"keydown\", trapFocus);\n\t\t\t}\n\t\t\tafterPopup(box);\n\n\t\t}\n\n\n\t\tfunction isTopModal(box) {\n\t\t\treturn box == modalsStack[modalsStack.length - 1];\n\t\t}\n\n\t\tfunction trapFocus(event) {\n\t\t\tvar event = event || window.event;\n\t\t\tvar target = event.currentTarget;\n\t\t\tif (!isTopModal(target)) return;\n\n\t\t\tgantt.$keyboardNavigation.trapFocus(target, event);\n\t\t}\n\n\t\tfunction traceLightbox() {\n\t\t\tstartModal(gantt.getLightbox());\n\t\t}\n\n\t\tgantt.attachEvent(\"onLightbox\", traceLightbox);\n\t\tgantt.attachEvent(\"onAfterLightbox\", endModal);\n\t\tgantt.attachEvent(\"onLightboxChange\", function () {\n\t\t\tendModal();\n\t\t\ttraceLightbox();\n\t\t});\n\n\n\t\tgantt.attachEvent(\"onAfterQuickInfo\", function () {\n\t\t\tafterPopup();\n\t\t});\n\n\t\tgantt.attachEvent(\"onMessagePopup\", function (box) {\n\t\t\tsaveFocus();\n\t\t\tstartModal(box);\n\t\t});\n\t\tgantt.attachEvent(\"onAfterMessagePopup\", function () {\n\t\t\tendModal();\n\t\t\trestoreFocus();\n\t\t});\n\n\t\tvar focusElement = null;\n\n\t\tfunction saveFocus() {\n\t\t\tfocusElement = document.activeElement;\n\t\t}\n\n\t\tfunction restoreFocus() {\n\t\t\tsetTimeout(function () {\n\t\t\t\tif (focusElement) {\n\t\t\t\t\tfocusElement.focus();\n\t\t\t\t\tfocusElement = null;\n\t\t\t\t}\n\t\t\t}, 1);\n\t\t}\n\n\t\tgantt.$keyboardNavigation.isModal = isModal;\n\n\n\t})();\n\n};", "module.exports = function(gantt) {\n\tvar domHelpers = require(\"../../../utils/dom_helpers\");\n\n\tgantt.$keyboardNavigation.TaskCell = function (taskId, index) {\n\t\tif (!taskId) {\n\t\t\tvar rootLevel = gantt.getChildren(gantt.config.root_id);\n\t\t\tif (rootLevel[0]) {\n\t\t\t\ttaskId = rootLevel[0];\n\t\t\t}\n\t\t}\n\t\tthis.taskId = taskId;\n\t\tthis.columnIndex = index || 0;\n\t\t// provided task may not exist, in this case node will be detectes as invalid\n\t\tif (gantt.isTaskExists(this.taskId)) {\n\t\t\tthis.index = gantt.getTaskIndex(this.taskId);\n\t\t}\n\t};\n\n\tgantt.$keyboardNavigation.TaskCell.prototype = gantt._compose(\n\t\tgantt.$keyboardNavigation.TaskRow,\n\t\t{\n\t\t\t_handlers: null,\n\t\t\tisValid: function () {\n\n\t\t\t\treturn gantt.$keyboardNavigation.TaskRow.prototype.isValid.call(this) && !!gantt.getGridColumns()[this.columnIndex];\n\t\t\t},\n\t\t\tfallback: function () {\n\n\t\t\t\tvar node = gantt.$keyboardNavigation.TaskRow.prototype.fallback.call(this);\n\t\t\t\tvar result = node;\n\t\t\t\tif (node instanceof gantt.$keyboardNavigation.TaskRow) {\n\t\t\t\t\tvar visibleColumns = gantt.getGridColumns();\n\t\t\t\t\tvar index = this.columnIndex;\n\t\t\t\t\twhile (index >= 0) {\n\t\t\t\t\t\tif (visibleColumns[index])\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tindex--;\n\t\t\t\t\t}\n\t\t\t\t\tif (visibleColumns[index]) {\n\t\t\t\t\t\tresult = new gantt.$keyboardNavigation.TaskCell(node.taskId, index);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn result;\n\t\t\t},\n\n\t\t\tfromDomElement: function(el){\n\t\t\t\tif(!gantt.config.keyboard_navigation_cells){\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\n\t\t\t\tvar taskId = gantt.locate(el);\n\t\t\t\tif(gantt.isTaskExists(taskId)){\n\t\t\t\t\tvar index = 0;\n\t\t\t\t\tvar cellElement = domHelpers.locateAttribute(el, \"data-column-index\");\n\n\t\t\t\t\tif(cellElement){\n\t\t\t\t\t\tindex = cellElement.getAttribute(\"data-column-index\")*1;\n\t\t\t\t\t}\n\n\t\t\t\t\treturn new gantt.$keyboardNavigation.TaskCell(taskId, index);\n\t\t\t\t}else{\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tgetNode: function () {\n\t\t\t\tif (gantt.isTaskExists(this.taskId) && gantt.isTaskVisible(this.taskId)) {\n\t\t\t\t\tif (gantt.config.show_grid) {\n\t\t\t\t\t\tvar row = gantt.$grid.querySelector(\".gantt_row[\" + gantt.config.task_attribute + \"='\" + this.taskId + \"']\");\n\t\t\t\t\t\tif(!row)\n\t\t\t\t\t\t\treturn null;\n\t\t\t\t\t\treturn row.querySelector(\"[data-column-index='\"+this.columnIndex+\"']\");\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn gantt.getTaskNode(this.taskId);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tkeys: {\n\t\t\t\t\"up\": function () {\n\n\t\t\t\t\tvar nextElement = null;\n\t\t\t\t\tvar prevTask = gantt.getPrev(this.taskId);\n\t\t\t\t\tif (!prevTask) {\n\t\t\t\t\t\tnextElement = new gantt.$keyboardNavigation.HeaderCell(this.columnIndex);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tnextElement = new gantt.$keyboardNavigation.TaskCell(prevTask, this.columnIndex);\n\t\t\t\t\t}\n\t\t\t\t\tthis.moveTo(nextElement);\n\t\t\t\t},\n\t\t\t\t\"down\": function () {\n\t\t\t\t\tvar nextTask = gantt.getNext(this.taskId);\n\t\t\t\t\tif (nextTask) {\n\t\t\t\t\t\tthis.moveTo(new gantt.$keyboardNavigation.TaskCell(nextTask, this.columnIndex));\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t\"left\": function () {\n\t\t\t\t\tif (this.columnIndex > 0) {\n\t\t\t\t\t\tthis.moveTo(new gantt.$keyboardNavigation.TaskCell(this.taskId, this.columnIndex - 1));\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t\"right\": function () {\n\t\t\t\t\tvar columns = gantt.getGridColumns();\n\t\t\t\t\tif (this.columnIndex < columns.length - 1) {\n\t\t\t\t\t\tthis.moveTo(new gantt.$keyboardNavigation.TaskCell(this.taskId, this.columnIndex + 1));\n\t\t\t\t\t}\n\t\t\t\t},\n\n\t\t\t\t\"end\": function () {\n\t\t\t\t\tvar columns = gantt.getGridColumns();\n\t\t\t\t\tthis.moveTo(new gantt.$keyboardNavigation.TaskCell(this.taskId, columns.length - 1));\n\t\t\t\t},\n\t\t\t\t\"home\": function () {\n\t\t\t\t\tthis.moveTo(new gantt.$keyboardNavigation.TaskCell(this.taskId, 0));\n\t\t\t\t},\n\t\t\t\t\"pagedown\": function () {\n\t\t\t\t\tif (gantt.getVisibleTaskCount()) {\n\t\t\t\t\t\tthis.moveTo(new gantt.$keyboardNavigation.TaskCell(gantt.getTaskByIndex(gantt.getVisibleTaskCount() - 1).id, this.columnIndex));\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t\"pageup\": function () {\n\t\t\t\t\tif (gantt.getVisibleTaskCount()) {\n\t\t\t\t\t\tthis.moveTo(new gantt.$keyboardNavigation.TaskCell(gantt.getTaskByIndex(0).id, this.columnIndex));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t);\n\n\n\tgantt.$keyboardNavigation.TaskCell.prototype.bindAll(gantt.$keyboardNavigation.TaskRow.prototype.keys);\n\tgantt.$keyboardNavigation.TaskCell.prototype.bindAll(gantt.$keyboardNavigation.TaskCell.prototype.keys);\n\n};", "module.exports = function(gantt) {\n\n\tgantt.$keyboardNavigation.TaskRow = function (taskId) {\n\t\tif (!taskId) {\n\t\t\tvar rootLevel = gantt.getChildren(gantt.config.root_id);\n\t\t\tif (rootLevel[0]) {\n\t\t\t\ttaskId = rootLevel[0];\n\t\t\t}\n\t\t}\n\t\tthis.taskId = taskId;\n\t\tif (gantt.isTaskExists(this.taskId)) {\n\t\t\tthis.index = gantt.getTaskIndex(this.taskId);\n\t\t}\n\t};\n\n\tgantt.$keyboardNavigation.TaskRow.prototype = gantt._compose(\n\t\tgantt.$keyboardNavigation.KeyNavNode,\n\t\t{\n\t\t\t_handlers: null,\n\t\t\tisValid: function () {\n\t\t\t\treturn gantt.isTaskExists(this.taskId) && (gantt.getTaskIndex(this.taskId) > -1);\n\t\t\t},\n\t\t\tfallback: function () {\n\t\t\t\tif (!gantt.getVisibleTaskCount()) {\n\t\t\t\t\tvar header = new gantt.$keyboardNavigation.HeaderCell();\n\t\t\t\t\tif (!header.isValid()) return null;\n\t\t\t\t\telse return header;\n\t\t\t\t} else {\n\n\t\t\t\t\tvar nextIndex = -1;\n\t\t\t\t\tif (gantt.getTaskByIndex(this.index - 1)) {\n\t\t\t\t\t\tnextIndex = this.index - 1;\n\t\t\t\t\t} else if (gantt.getTaskByIndex(this.index + 1)) {\n\t\t\t\t\t\tnextIndex = this.index + 1;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tvar index = this.index;\n\t\t\t\t\t\twhile (index >= 0) {\n\t\t\t\t\t\t\tif (gantt.getTaskByIndex(index)) {\n\t\t\t\t\t\t\t\tnextIndex = index;\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tindex--;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\t\t\t\t\tif (nextIndex > -1) {\n\t\t\t\t\t\treturn new gantt.$keyboardNavigation.TaskRow(gantt.getTaskByIndex(nextIndex).id);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tfromDomElement: function(el){\n\t\t\t\tif(gantt.config.keyboard_navigation_cells){\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\n\t\t\t\tvar taskId = gantt.locate(el);\n\t\t\t\tif(gantt.isTaskExists(taskId)){\n\t\t\t\t\treturn new gantt.$keyboardNavigation.TaskRow(taskId);\n\t\t\t\t}else{\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tgetNode: function () {\n\t\t\t\tif (gantt.isTaskExists(this.taskId) && gantt.isTaskVisible(this.taskId)) {\n\t\t\t\t\tif (gantt.config.show_grid) {\n\t\t\t\t\t\treturn gantt.$grid.querySelector(\".gantt_row[\" + gantt.config.task_attribute + \"='\" + this.taskId + \"']\");\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn gantt.getTaskNode(this.taskId);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tfocus: function (keptFocus) {\n\t\t\t\tif(!keptFocus) {\n\t\t\t\t\tvar pos = gantt.getTaskPosition(gantt.getTask(this.taskId));\n\t\t\t\t\tvar height = gantt.config.row_height;\n\t\t\t\t\tvar scroll = gantt.getScrollState();\n\n\t\t\t\t\tvar viewWidth;\n\t\t\t\t\tif(gantt.$task){\n\t\t\t\t\t\tviewWidth = gantt.$task.offsetWidth;\n\t\t\t\t\t}else{\n\t\t\t\t\t\tviewWidth = scroll.inner_width;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar viewHeight;\n\t\t\t\t\tif(gantt.$grid_data || gantt.$task_data){\n\t\t\t\t\t\tviewHeight = (gantt.$grid_data || gantt.$task_data).offsetHeight;\n\t\t\t\t\t}else{\n\t\t\t\t\t\tviewHeight = scroll.inner_height;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (pos.top < scroll.y || pos.top + height > (scroll.y + viewHeight)) {\n\n\t\t\t\t\t\tgantt.scrollTo(null, pos.top - height * 5);\n\n\t\t\t\t\t} else if (gantt.config.show_chart && (pos.left < scroll.x || pos.left > (scroll.x + viewWidth))) {\n\t\t\t\t\t\tgantt.scrollTo(pos.left - gantt.config.task_scroll_offset);\n\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tgantt.$keyboardNavigation.KeyNavNode.prototype.focus.apply(this, [keptFocus]);\n\t\t\t},\n\n\t\t\tkeys: {\n\t\t\t\t\"pagedown\": function () {\n\t\t\t\t\tif (gantt.getVisibleTaskCount()) {\n\t\t\t\t\t\tthis.moveTo(new gantt.$keyboardNavigation.TaskRow(gantt.getTaskByIndex(gantt.getVisibleTaskCount() - 1).id));\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t\"pageup\": function () {\n\t\t\t\t\tif (gantt.getVisibleTaskCount()) {\n\t\t\t\t\t\tthis.moveTo(new gantt.$keyboardNavigation.TaskRow(gantt.getTaskByIndex(0).id));\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t\"up\": function () {\n\t\t\t\t\tvar nextElement = null;\n\t\t\t\t\tvar prevTask = gantt.getPrev(this.taskId);\n\t\t\t\t\tif (!prevTask) {\n\t\t\t\t\t\tnextElement = new gantt.$keyboardNavigation.HeaderCell();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tnextElement = new gantt.$keyboardNavigation.TaskRow(prevTask);\n\t\t\t\t\t}\n\t\t\t\t\tthis.moveTo(nextElement);\n\t\t\t\t},\n\t\t\t\t\"down\": function () {\n\t\t\t\t\tvar nextTask = gantt.getNext(this.taskId);\n\t\t\t\t\tif (nextTask) {\n\t\t\t\t\t\tthis.moveTo(new gantt.$keyboardNavigation.TaskRow(nextTask));\n\t\t\t\t\t}\n\t\t\t\t},\n\n\t\t\t\t\"shift+down\": function(){\n\t\t\t\t\tif(gantt.hasChild(this.taskId) && !gantt.getTask(this.taskId).$open){\n\t\t\t\t\t\tgantt.open(this.taskId);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t\"shift+up\": function(){\n\t\t\t\t\tif(gantt.hasChild(this.taskId) && gantt.getTask(this.taskId).$open){\n\t\t\t\t\t\tgantt.close(this.taskId);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t\"shift+right\": function() {\n\t\t\t\t\tif (gantt.isReadonly(this)) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tvar prevId = gantt.getPrevSibling(this.taskId);\n\t\t\t\t\tif(gantt.isTaskExists(prevId) && !gantt.isChildOf(this.taskId, prevId)){\n\t\t\t\t\t\tvar parent = gantt.getTask(prevId);\n\t\t\t\t\t\tparent.$open = true;\n\t\t\t\t\t\tvar result = gantt.moveTask(this.taskId, -1, prevId);\n\t\t\t\t\t\tif(result !== false)\n\t\t\t\t\t\t\tgantt.updateTask(this.taskId);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t\"shift+left\": function() {\n\t\t\t\t\tif (gantt.isReadonly(this)) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tvar parent = gantt.getParent(this.taskId);\n\t\t\t\t\tif(gantt.isTaskExists(parent)){\n\t\t\t\t\t\tvar result =  gantt.moveTask(this.taskId, gantt.getTaskIndex(parent) + 1, gantt.getParent(parent));\n\t\t\t\t\t\tif(result !== false)\n\t\t\t\t\t\t\tgantt.updateTask(this.taskId);\n\t\t\t\t\t}\n\t\t\t\t},\n\n\t\t\t\t// select\n\t\t\t\t\"space\": function (e) {\n\t\t\t\t\tif (!gantt.isSelectedTask(this.taskId)) {\n\t\t\t\t\t\tgantt.selectTask(this.taskId);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tgantt.unselectTask(this.taskId);\n\t\t\t\t\t}\n\t\t\t\t},\n\n\t\t\t\t// collapse\n\t\t\t\t\"ctrl+left\": function (e) {\n\t\t\t\t\tgantt.close(this.taskId);\n\t\t\t\t},\n\t\t\t\t// expand\n\t\t\t\t\"ctrl+right\": function (e) {\n\t\t\t\t\tgantt.open(this.taskId);\n\t\t\t\t},\n\n\t\t\t\t// delete task\n\t\t\t\t\"delete\": function (e) {\n\t\t\t\t\tif (gantt.isReadonly(this)) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tgantt.$click.buttons[\"delete\"](this.taskId);\n\t\t\t\t},\n\n\t\t\t\t// open lightbox\n\t\t\t\t\"enter\": function () {\n\t\t\t\t\tif (gantt.isReadonly(this)) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tgantt.showLightbox(this.taskId);\n\t\t\t\t},\n\n\t\t\t\t// add subtask\n\t\t\t\t\"ctrl+enter\": function () {\n\t\t\t\t\tif (gantt.isReadonly(this)) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tgantt.createTask({}, this.taskId);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t);\n\tgantt.$keyboardNavigation.TaskRow.prototype.bindAll(gantt.$keyboardNavigation.TaskRow.prototype.keys);\n\n};", "module.exports = function(gantt) {\n\tvar domHelpers = require(\"../../../utils/dom_helpers\");\n\n\tgantt.$keyboardNavigation.HeaderCell = function (index) {\n\t\tthis.index = index || 0;\n\t};\n\n\tgantt.$keyboardNavigation.HeaderCell.prototype = gantt._compose(\n\t\tgantt.$keyboardNavigation.KeyNavNode,\n\t\t{\n\t\t\t_handlers: null,\n\n\t\t\tisValid: function () {\n\t\t\t\tif (!gantt.config.show_grid) {\n\t\t\t\t\tif (gantt.getVisibleTaskCount())\n\t\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\treturn !!gantt.getGridColumns()[this.index] || !gantt.getVisibleTaskCount();\n\t\t\t},\n\t\t\tfallback: function () {\n\t\t\t\tif (!gantt.config.show_grid) {\n\t\t\t\t\tif (gantt.getVisibleTaskCount()) {\n\t\t\t\t\t\treturn new gantt.$keyboardNavigation.TaskRow();\n\t\t\t\t\t}\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t\tvar visibleColumns = gantt.getGridColumns();\n\t\t\t\tvar index = this.index;\n\t\t\t\twhile (index >= 0) {\n\t\t\t\t\tif (visibleColumns[index])\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tindex--;\n\t\t\t\t}\n\t\t\t\tif (visibleColumns[index]) {\n\t\t\t\t\treturn new gantt.$keyboardNavigation.HeaderCell(index);\n\t\t\t\t} else {\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tfromDomElement: function(el){\n\t\t\t\tvar cellElement = domHelpers.locateClassName(el, \"gantt_grid_head_cell\");\n\t\t\t\tif(cellElement){\n\t\t\t\t\tvar index = 0;\n\t\t\t\t\twhile(cellElement && cellElement.previousSibling){\n\t\t\t\t\t\tcellElement = cellElement.previousSibling;\n\t\t\t\t\t\tindex += 1;\n\t\t\t\t\t}\n\t\t\t\t\treturn new gantt.$keyboardNavigation.HeaderCell(index);\n\t\t\t\t}else{\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tgetNode: function () {\n\t\t\t\tvar cells = gantt.$grid_scale.childNodes;\n\t\t\t\treturn cells[this.index];\n\t\t\t},\n\n\n\t\t\tkeys: {\n\n\t\t\t\t\"left\": function () {\n\t\t\t\t\tif (this.index > 0) {\n\t\t\t\t\t\tthis.moveTo(new gantt.$keyboardNavigation.HeaderCell(this.index - 1));\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t\"right\": function () {\n\t\t\t\t\tvar columns = gantt.getGridColumns();\n\t\t\t\t\tif (this.index < columns.length - 1) {\n\t\t\t\t\t\tthis.moveTo(new gantt.$keyboardNavigation.HeaderCell(this.index + 1));\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t\"down\": function () {\n\t\t\t\t\tvar taskRow;\n\t\t\t\t\tvar rootLevel = gantt.getChildren(gantt.config.root_id);\n\t\t\t\t\tif (rootLevel[0]) {\n\t\t\t\t\t\ttaskRow = rootLevel[0];\n\t\t\t\t\t}\n\t\t\t\t\tif (taskRow) {\n\t\t\t\t\t\tif (gantt.config.keyboard_navigation_cells) {\n\t\t\t\t\t\t\tthis.moveTo(new gantt.$keyboardNavigation.TaskCell(taskRow, this.index));\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.moveTo(new gantt.$keyboardNavigation.TaskRow(taskRow));\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t},\n\n\t\t\t\t\"end\": function () {\n\t\t\t\t\tvar columns = gantt.getGridColumns();\n\t\t\t\t\tthis.moveTo(new gantt.$keyboardNavigation.HeaderCell(columns.length - 1));\n\t\t\t\t},\n\t\t\t\t\"home\": function () {\n\t\t\t\t\tthis.moveTo(new gantt.$keyboardNavigation.HeaderCell(0));\n\t\t\t\t},\n\n\n\t\t\t\t// press header button\n\t\t\t\t\"enter, space\": function () {\n\t\t\t\t\tvar node = document.activeElement;\n\t\t\t\t\tnode.click();\n\t\t\t\t},\n\n\t\t\t\t// add new task\n\t\t\t\t\"ctrl+enter\": function () {\n\t\t\t\t\tif (gantt.isReadonly(this)) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tgantt.createTask({}, this.taskId);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t);\n\n\tgantt.$keyboardNavigation.HeaderCell.prototype.bindAll(gantt.$keyboardNavigation.HeaderCell.prototype.keys);\n\n};", "module.exports = function(gantt) {\n\n\tgantt.$keyboardNavigation.KeyNavNode = function () {\n\t};\n\n\tgantt.$keyboardNavigation.KeyNavNode.prototype = gantt._compose(\n\t\tgantt.$keyboardNavigation.EventHandler,\n\t\t{\n\t\t\tisValid: function () {\n\t\t\t\treturn true;\n\t\t\t},\n\t\t\tfallback: function () {\n\t\t\t\treturn null;\n\t\t\t},\n\n\t\t\tmoveTo: function (element) {\n\t\t\t\tgantt.$keyboardNavigation.dispatcher.setActiveNode(element);\n\t\t\t},\n\n\t\t\tcompareTo: function (b) {\n\t\t\t\t// good enough comparison of two random objects\n\t\t\t\tif (!b) return false;\n\t\t\t\tfor (var i in this) {\n\t\t\t\t\tif (!!this[i] != !!b[i]) return false;\n\n\t\t\t\t\tvar canStringifyThis = !!(this[i] && this[i].toString);\n\t\t\t\t\tvar canStringifyThat = !!(b[i] && b[i].toString);\n\t\t\t\t\tif (canStringifyThat != canStringifyThis) return false;\n\t\t\t\t\tif (!(canStringifyThat && canStringifyThis)) {\n\t\t\t\t\t\tif (b[i] != this[i]) return false;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (b[i].toString() != this[i].toString())\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t},\n\n\t\t\tgetNode: function () {\n\t\t\t},\n\t\t\tfocus: function () {\n\t\t\t\tvar node = this.getNode();\n\t\t\t\tif(!node)\n\t\t\t\t\treturn;\n\n\t\t\t\tvar eventFacade = gantt.$keyboardNavigation.facade;\n\n\t\t\t\tif(eventFacade.callEvent(\"onBeforeFocus\", [node]) === false){\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (node) {\n\t\t\t\t\tnode.setAttribute(\"tabindex\", \"-1\");\n\t\t\t\t\tif(!node.$eventAttached){\n\t\t\t\t\t\tnode.$eventAttached = true;\n\t\t\t\t\t\tgantt.event(node, \"focus\",function(e){\n\t\t\t\t\t\t\te = e || event;\n\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t}, false);\n\t\t\t\t\t}\n\t\t\t\t\t//node.className += \" gantt_focused\";\n\t\t\t\t\tif (node.focus) node.focus();\n\n\t\t\t\t\teventFacade.callEvent(\"onFocus\", [this.getNode()]);\n\t\t\t\t}\n\n\t\t\t},\n\t\t\tblur: function () {\n\t\t\t\tvar node = this.getNode();\n\t\t\t\tif (node) {\n\t\t\t\t\tvar eventFacade = gantt.$keyboardNavigation.facade;\n\t\t\t\t\teventFacade.callEvent(\"onBlur\", [node]);\n\t\t\t\t\tnode.setAttribute(\"tabindex\", \"-1\");\n\t\t\t\t\t//node.className = (node.className || \"\").replace(/ ?gantt_focused/g, \"\");\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t);\n\n};", "module.exports = function(gantt) {\n\n\tgantt.$keyboardNavigation.GanttNode = function () {\n\t};\n\n\tgantt.$keyboardNavigation.GanttNode.prototype = gantt._compose(\n\t\tgantt.$keyboardNavigation.EventHandler,\n\t\t{\n\n\t\t\tfocus: function () {\n\t\t\t\tgantt.focus();\n\t\t\t},\n\n\t\t\tblur: function () {\n\n\t\t\t},\n\n\t\t\tdisable: function () {\n\t\t\t\tgantt.$container.setAttribute(\"tabindex\", \"0\");\n\t\t\t},\n\t\t\tenable: function () {\n\t\t\t\tif (gantt.$container)\n\t\t\t\t\tgantt.$container.removeAttribute(\"tabindex\");\n\t\t\t},\n\t\t\tisEnabled: function () {\n\t\t\t\treturn gantt.$container.hasAttribute(\"tabindex\");\n\t\t\t},\n\n\t\t\tscrollHorizontal: function scrollHorizontal(dir) {\n\t\t\t\tvar date = gantt.dateFromPos(gantt.getScrollState().x);\n\t\t\t\tvar scale = gantt.getScale();\n\t\t\t\tvar step = dir < 0 ? -scale.step : scale.step;\n\t\t\t\tdate = gantt.date.add(date, step, scale.unit);\n\t\t\t\tgantt.scrollTo(gantt.posFromDate(date));\n\t\t\t},\n\n\t\t\tscrollVertical: function scrollVertical(dir) {\n\t\t\t\tvar top = gantt.getScrollState().y;\n\t\t\t\tvar step = gantt.config.row_height;\n\t\t\t\tgantt.scrollTo(null, top + (dir < 0 ? -1 : 1) * step);\n\t\t\t},\n\n\t\t\tkeys: {\n\t\t\t\t\"alt+left\": function (e) {\n\t\t\t\t\tthis.scrollHorizontal(-1);\n\t\t\t\t},\n\t\t\t\t\"alt+right\": function (e) {\n\t\t\t\t\tthis.scrollHorizontal(1);\n\t\t\t\t},\n\t\t\t\t\"alt+up\": function (e) {\n\t\t\t\t\tthis.scrollVertical(-1);\n\t\t\t\t},\n\t\t\t\t\"alt+down\": function (e) {\n\t\t\t\t\tthis.scrollVertical(1);\n\t\t\t\t},\n\n\t\t\t\t// undo\n\t\t\t\t\"ctrl+z\": function () {\n\t\t\t\t\tif (gantt.undo) gantt.undo();\n\t\t\t\t},\n\n\t\t\t\t// redo\n\t\t\t\t\"ctrl+r\": function () {\n\t\t\t\t\tif (gantt.redo) gantt.redo();\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t);\n\n\tgantt.$keyboardNavigation.GanttNode.prototype.bindAll(gantt.$keyboardNavigation.GanttNode.prototype.keys);\n\n};", "module.exports = function(gantt) {\n\n\t(function () {\n\t\tvar domHelpers = require(\"../../../utils/dom_helpers\");\n\t\tgantt.$keyboardNavigation.getFocusableNodes = domHelpers.getFocusableNodes;\n\n\t\tgantt.$keyboardNavigation.trapFocus = function trapFocus(root, e) {\n\t\t\tif (e.keyCode != 9) return false;\n\n\t\t\tvar focusable = gantt.$keyboardNavigation.getFocusableNodes(root);\n\t\t\tvar currentFocus = document.activeElement;\n\t\t\tvar currentIndex = -1;\n\t\t\tfor (var i = 0; i < focusable.length; i++) {\n\t\t\t\tif (focusable[i] == currentFocus) {\n\t\t\t\t\tcurrentIndex = i;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (e.shiftKey) {\n\t\t\t\t// back tab\n\t\t\t\tif (currentIndex <= 0) {\n\t\t\t\t\t// go to the last element if we focused on the first\n\t\t\t\t\tvar lastItem = focusable[focusable.length - 1];\n\t\t\t\t\tif (lastItem) {\n\t\t\t\t\t\tlastItem.focus();\n\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t} else {\n\t\t\t\t// forward tab\n\t\t\t\tif (currentIndex >= focusable.length - 1) {\n\t\t\t\t\t// forward tab from last element should go back to the first element\n\t\t\t\t\tvar firstItem = focusable[0];\n\t\t\t\t\tif (firstItem) {\n\t\t\t\t\t\tfirstItem.focus();\n\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn false;\n\t\t};\n\t})();\n\n};", "module.exports = function(gantt) {\n\n\tgantt.$keyboardNavigation.EventHandler = {\n\t\t_handlers: null,\n\t\tfindHandler: function (command) {\n\t\t\tif (!this._handlers) this._handlers = {};\n\t\t\tvar shortcuts = gantt.$keyboardNavigation.shortcuts;\n\t\t\tvar hash = shortcuts.getHash(command);\n\n\t\t\treturn this._handlers[hash];\n\t\t},\n\n\t\tdoAction: function (command, e) {\n\t\t\tvar handler = this.findHandler(command);\n\t\t\tif (handler) {\n\t\t\t\tvar eventFacade = gantt.$keyboardNavigation.facade;\n\n\t\t\t\tif(eventFacade.callEvent(\"onBeforeAction\", [command, e]) === false){\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\thandler.call(this, e);\n\n\t\t\t\tif (e.preventDefault) e.preventDefault();\n\t\t\t\telse e.returnValue = false;\n\n\t\t\t}\n\t\t},\n\t\tbind: function (shortcut, handler) {\n\t\t\tif (!this._handlers) this._handlers = {};\n\n\t\t\tvar shortcuts = gantt.$keyboardNavigation.shortcuts;\n\n\t\t\tvar commands = shortcuts.parse(shortcut);\n\t\t\tfor (var i = 0; i < commands.length; i++) {\n\t\t\t\tthis._handlers[shortcuts.getHash(commands[i])] = handler;\n\t\t\t}\n\t\t},\n\t\tunbind: function (shortcut) {\n\t\t\tvar shortcuts = gantt.$keyboardNavigation.shortcuts;\n\n\t\t\tvar commands = shortcuts.parse(shortcut);\n\t\t\tfor (var i = 0; i < commands.length; i++) {\n\t\t\t\tif (this._handlers[shortcuts.getHash(commands[i])]) {\n\t\t\t\t\tdelete this._handlers[shortcuts.getHash(commands[i])];\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tbindAll: function (map) {\n\t\t\tfor (var i in map) {\n\t\t\t\tthis.bind(i, map[i]);\n\t\t\t}\n\t\t},\n\t\tinitKeys: function () {\n\t\t\tif (!this._handlers)\n\t\t\t\tthis._handlers = {};\n\t\t\tif (this.keys) {\n\t\t\t\tthis.bindAll(this.keys);\n\t\t\t}\n\t\t}\n\t};\n\n};", "module.exports = function(gantt) {\n\n\tgantt.$keyboardNavigation.shortcuts = {\n\t\tcreateCommand: function () {\n\t\t\treturn {\n\t\t\t\tmodifiers: {\n\t\t\t\t\t\"shift\": false,\n\t\t\t\t\t\"alt\": false,\n\t\t\t\t\t\"ctrl\": false,\n\t\t\t\t\t\"meta\": false\n\t\t\t\t},\n\t\t\t\tkeyCode: null\n\t\t\t};\n\t\t},\n\t\tparse: function (shortcut) {\n\t\t\tvar commands = [];\n\n\t\t\tvar expr = this.getExpressions(this.trim(shortcut));\n\t\t\tfor (var i = 0; i < expr.length; i++) {\n\t\t\t\tvar words = this.getWords(expr[i]);\n\n\t\t\t\tvar command = this.createCommand();\n\n\t\t\t\tfor (var j = 0; j < words.length; j++) {\n\t\t\t\t\tif (this.commandKeys[words[j]]) {\n\t\t\t\t\t\tcommand.modifiers[words[j]] = true;\n\t\t\t\t\t} else if (this.specialKeys[words[j]]) {\n\t\t\t\t\t\tcommand.keyCode = this.specialKeys[words[j]];\n\t\t\t\t\t} else {\n\t\t\t\t\t\tcommand.keyCode = words[j].charCodeAt(0);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tcommands.push(command);\n\t\t\t}\n\t\t\treturn commands;\n\t\t},\n\n\t\tgetCommandFromEvent: function (domEvent) {\n\t\t\tvar command = this.createCommand();\n\t\t\tcommand.modifiers.shift = !!domEvent.shiftKey;\n\t\t\tcommand.modifiers.alt = !!domEvent.altKey;\n\t\t\tcommand.modifiers.ctrl = !!domEvent.ctrlKey;\n\t\t\tcommand.modifiers.meta = !!domEvent.metaKey;\n\t\t\tcommand.keyCode = domEvent.which || domEvent.keyCode;\n\n\t\t\tif(command.keyCode >= 96 && command.keyCode <= 105){\n\t\t\t\t// numpad keys 96-105 -> 48-57\n\t\t\t\tcommand.keyCode -= 48;//convert numpad  number code to regular number code\n\t\t\t}\n\n\t\t\tvar printableKey = String.fromCharCode(command.keyCode);\n\t\t\tif (printableKey) {\n\t\t\t\tcommand.keyCode = printableKey.toLowerCase().charCodeAt(0);\n\t\t\t}\n\t\t\treturn command;\n\t\t},\n\n\t\tgetHashFromEvent: function (domEvent) {\n\t\t\treturn this.getHash(this.getCommandFromEvent(domEvent));\n\t\t},\n\n\t\tgetHash: function (command) {\n\t\t\tvar parts = [];\n\t\t\tfor (var i in command.modifiers) {\n\t\t\t\tif (command.modifiers[i]) {\n\t\t\t\t\tparts.push(i);\n\t\t\t\t}\n\t\t\t}\n\t\t\tparts.push(command.keyCode);\n\n\t\t\treturn parts.join(this.junctionChar);\n\t\t},\n\n\t\tgetExpressions: function (shortcut) {\n\t\t\treturn shortcut.split(this.junctionChar);\n\t\t},\n\t\tgetWords: function (term) {\n\t\t\treturn term.split(this.combinationChar);\n\t\t},\n\t\ttrim: function (shortcut) {\n\t\t\treturn shortcut.replace(/\\s/g, \"\");\n\t\t},\n\t\tjunctionChar: \",\",\n\t\tcombinationChar: \"+\",\n\t\tcommandKeys: {\n\t\t\t\"shift\": 16,\n\t\t\t\"alt\": 18,\n\t\t\t\"ctrl\": 17,\n\t\t\t\"meta\": true\n\t\t},\n\t\tspecialKeys: {\n\t\t\t\"backspace\": 8,\n\t\t\t\"tab\": 9,\n\t\t\t\"enter\": 13,\n\t\t\t\"esc\": 27,\n\t\t\t\"space\": 32,\n\t\t\t\"up\": 38,\n\t\t\t\"down\": 40,\n\t\t\t\"left\": 37,\n\t\t\t\"right\": 39,\n\t\t\t\"home\": 36,\n\t\t\t\"end\": 35,\n\t\t\t\"pageup\": 33,\n\t\t\t\"pagedown\": 34,\n\t\t\t\"delete\": 46,\n\t\t\t\"insert\": 45,\n\t\t\t\"plus\": 107,\n\t\t\t\"f1\": 112,\n\t\t\t\"f2\": 113,\n\t\t\t\"f3\": 114,\n\t\t\t\"f4\": 115,\n\t\t\t\"f5\": 116,\n\t\t\t\"f6\": 117,\n\t\t\t\"f7\": 118,\n\t\t\t\"f8\": 119,\n\t\t\t\"f9\": 120,\n\t\t\t\"f10\": 121,\n\t\t\t\"f11\": 122,\n\t\t\t\"f12\": 123\n\t\t}\n\t};\n};", "(function(){\n\tvar eventable = require(\"../utils/eventable\");\n\tfunction setupKeyNav(gantt){\n\t\tgantt.config.keyboard_navigation = true;\n\t\tgantt.config.keyboard_navigation_cells = false;\n\n\t\tgantt.$keyboardNavigation = {};\n\n\t\tgantt._compose = function(){\n\t\t\tvar parts = Array.prototype.slice.call(arguments, 0);\n\t\t\tvar res = {};\n\t\t\tfor(var i = 0; i < parts.length; i++){\n\t\t\t\tvar obj = parts[i];\n\t\t\t\tif(typeof obj == \"function\"){\n\t\t\t\t\tobj = new obj();\n\t\t\t\t}\n\n\t\t\t\tfor(var p in obj){\n\t\t\t\t\tres[p] = obj[p];\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn res;\n\t\t};\n\n\t\trequire (\"./keyboard_navigation/common/keyboard_shortcuts\")(gantt);\n\t\trequire (\"./keyboard_navigation/common/eventhandler\")(gantt);\n\t\trequire (\"./keyboard_navigation/common/trap_modal_focus\")(gantt);\n\t\trequire (\"./keyboard_navigation/elements/gantt_node\")(gantt);\n\t\trequire (\"./keyboard_navigation/elements/nav_node\")(gantt);\n\t\trequire (\"./keyboard_navigation/elements/header_cell\")(gantt);\n\t\trequire (\"./keyboard_navigation/elements/task_row\")(gantt);\n\t\trequire (\"./keyboard_navigation/elements/task_cell\")(gantt);\n\t\trequire (\"./keyboard_navigation/modals\")(gantt);\n\t\trequire (\"./keyboard_navigation/core\")(gantt);\n\n\t\tvar domHelpers = require(\"../utils/dom_helpers\");\n\n\t\t(function(){\n\t\t\tvar dispatcher = gantt.$keyboardNavigation.dispatcher;\n\n\t\t\tdispatcher.isTaskFocused = function(id){\n\t\t\t\tvar node = dispatcher.activeNode;\n\t\t\t\tif(node instanceof gantt.$keyboardNavigation.TaskRow || node instanceof gantt.$keyboardNavigation.TaskCell) {\n\t\t\t\t\tif (node.taskId == id) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t};\n\n\t\t\tvar keyDownHandler = function(e){\n\t\t\t\tif(!gantt.config.keyboard_navigation) return;\n\n\t\t\t\treturn dispatcher.keyDownHandler(e);\n\t\t\t};\n\n\t\t\tvar focusHandler = function(e){\n\t\t\t\tif(dispatcher.$preventDefault){\n\t\t\t\t\te.preventDefault();\n\t\t\t\t\tgantt.$container.blur();\n\t\t\t\t\treturn false;\n\t\t\t\t// do nothing if key-nav focus is already planned\n\t\t\t\t} else if (!dispatcher.awaitsFocus()) {\n\t\t\t\t\t// otherwise - re-focus key-nav element on gantt focus\n\t\t\t\t\tdispatcher.focusGlobalNode();\n\t\t\t\t}\n\n\t\t\t};\n\n\t\t\tvar reFocusActiveNode = function(){\n\t\t\t\tif(!dispatcher.isEnabled())\n\t\t\t\t\treturn;\n\n\t\t\t\tvar activeNode = dispatcher.getActiveNode();\n\t\t\t\tif(!activeNode)\n\t\t\t\t\treturn;\n\n\t\t\t\tvar domElement = activeNode.getNode();\n\t\t\t\tvar top, left;\n\t\t\t\tif(domElement && domElement.parentNode){\n\t\t\t\t\ttop = domElement.parentNode.scrollTop;\n\t\t\t\t\tleft = domElement.parentNode.scrollLeft;\n\n\t\t\t\t}\n\n\t\t\t\tactiveNode.focus(true);\n\n\t\t\t\tif(domElement && domElement.parentNode){\n\t\t\t\t\tdomElement.parentNode.scrollTop = top;\n\t\t\t\t\tdomElement.parentNode.scrollLeft = left;\n\t\t\t\t}\n\t\t\t};\n\n\n\t\t\tgantt.attachEvent(\"onDataRender\", function(){\n\t\t\t\tif(!gantt.config.keyboard_navigation) return;\n\t\t\t\treFocusActiveNode();\n\t\t\t});\n\n\t\t\tgantt.attachEvent(\"onGanttRender\", function(){\n\t\t\t\tgantt.eventRemove(document, \"keydown\", keyDownHandler);\n\t\t\t\tgantt.eventRemove(gantt.$container, \"focus\", focusHandler);\n\t\t\t\tgantt.eventRemove(gantt.$container, \"mousedown\", mousedownHandler);\n\n\t\t\t\tif(gantt.config.keyboard_navigation){\n\n\t\t\t\t\tgantt.event(document, \"keydown\", keyDownHandler);\n\t\t\t\t\tgantt.event(gantt.$container, \"focus\", focusHandler);\n\t\t\t\t\tgantt.event(gantt.$container, \"mousedown\", mousedownHandler);\n\t\t\t\t\tgantt.$container.setAttribute(\"tabindex\", \"0\");\n\n\t\t\t\t}else{\n\t\t\t\t\tgantt.$container.removeAttribute(\"tabindex\");\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tfunction getTaskNodeConstructor(){\n\t\t\t\tif (gantt.config.keyboard_navigation_cells) {\n\t\t\t\t\treturn gantt.$keyboardNavigation.TaskCell;\n\t\t\t\t} else {\n\t\t\t\t\treturn gantt.$keyboardNavigation.TaskRow;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfunction mousedownHandler(e){\n\t\t\t\tif(!gantt.config.keyboard_navigation) return true;\n\n\t\t\t\tvar focusNode;\n\t\t\t\tvar locateTask = dispatcher.fromDomElement(e);\n\t\t\t\tif(locateTask){\n\t\t\t\t\t//var node = getTaskNodeConstructor();\n\t\t\t\t\tif(dispatcher.activeNode instanceof gantt.$keyboardNavigation.TaskCell && domHelpers.isChildOf(e.target, gantt.$task)){\n\t\t\t\t\t\tlocateTask = new gantt.$keyboardNavigation.TaskCell(locateTask.taskId, dispatcher.activeNode.columnIndex);\n\t\t\t\t\t}\n\t\t\t\t\tfocusNode = locateTask;\n\t\t\t\t}\n\t\t\t\tif (focusNode) {\n\t\t\t\t\tif (!dispatcher.isEnabled()) {\n\t\t\t\t\t\tdispatcher.activeNode = focusNode;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tdispatcher.delay(function () {\n\t\t\t\t\t\t\tdispatcher.setActiveNode(focusNode);\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t// empty click should drop focus from gantt, insert of reselecting default node\n\t\t\t\t\tdispatcher.$preventDefault = true;\n\t\t\t\t\tsetTimeout(function(){\n\t\t\t\t\t\tdispatcher.$preventDefault = false;\n\t\t\t\t\t}, 300);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tvar onReady = gantt.attachEvent(\"onGanttReady\", function(){\n\t\t\t\t// restore focus on repainted tasks\n\t\t\t\tgantt.detachEvent(onReady);\n\n\t\t\t\tgantt.$data.tasksStore.attachEvent(\"onStoreUpdated\", function(id){\n\t\t\t\t\tif (gantt.config.keyboard_navigation && dispatcher.isEnabled()) {\n\t\t\t\t\t\tvar currentNode = dispatcher.getActiveNode();\n\t\t\t\t\t\tif(currentNode && currentNode.taskId == id){\n\t\t\t\t\t\t\treFocusActiveNode();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\tif(gantt._smart_render){\n\t\t\t\t\tvar updateRender = gantt._smart_render._redrawTasks;\n\t\t\t\t\tgantt._smart_render._redrawTasks = function(renderers, items){\n\t\t\t\t\t\tif(gantt.config.keyboard_navigation && dispatcher.isEnabled()){\n\t\t\t\t\t\t\tvar currentNode = dispatcher.getActiveNode();\n\t\t\t\t\t\t\tif(currentNode && currentNode.taskId !== undefined){\n\t\t\t\t\t\t\t\tvar focusedItemVisible = false;\n\t\t\t\t\t\t\t\tfor(var i = 0; i < items.length; i++){\n\t\t\t\t\t\t\t\t\tif(items[i].id == currentNode.taskId && items[i].start_date){\n\t\t\t\t\t\t\t\t\t\tfocusedItemVisible = true;\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif(!focusedItemVisible){\n\t\t\t\t\t\t\t\t\titems.push(gantt.getTask(currentNode.taskId));\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tvar res = updateRender.apply(this, arguments);\n\n\t\t\t\t\t\treturn res;\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t});\n\n\n\n\t\t\tgantt.attachEvent(\"onAfterTaskAdd\", function(id,item){\n\t\t\t\tif(!gantt.config.keyboard_navigation) return true;\n\t\t\t\tif(dispatcher.isEnabled()){\n\n\t\t\t\t\tvar columnIndex = 0;\n\t\t\t\t\tvar node = dispatcher.activeNode;\n\t\t\t\t\tif(node instanceof gantt.$keyboardNavigation.TaskCell){\n\t\t\t\t\t\tcolumnIndex = node.columnIndex;\n\t\t\t\t\t}\n\t\t\t\t\tvar nodeConstructor = getTaskNodeConstructor();\n\n\t\t\t\t\tdispatcher.setActiveNode(new nodeConstructor(id, columnIndex));\n\n\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tgantt.attachEvent(\"onTaskIdChange\", function(oldId, newId){\n\t\t\t\tif(!gantt.config.keyboard_navigation) return true;\n\n\t\t\t\tvar node = dispatcher.activeNode;\n\t\t\t\tif(dispatcher.isTaskFocused(oldId)){\n\t\t\t\t\tnode.taskId = newId;\n\t\t\t\t}\n\n\t\t\t\treturn true;\n\t\t\t});\n\n\t\t\tfunction getActiveNode(){\n\n\t\t\t\tvar activeElement = document.activeElement;\n\t\t\t\tif(activeElement === document.body && document.getSelection){\n\t\t\t\t\tactiveElement = document.getSelection().focusNode || document.body;\n\t\t\t\t}\n\n\t\t\t\treturn activeElement;\n\t\t\t}\n\n\t\t\tvar interval = setInterval(function(){\n\t\t\t\tif(!gantt.config.keyboard_navigation) return;\n\n\t\t\t\tvar enable;\n\t\t\t\tvar focusElement = getActiveNode();\n\n\t\t\t\tvar parent = gantt.$container;\n\t\t\t\t// halt key nav when focus is outside gantt or in quick info popup\n\t\t\t\tif(!focusElement || gantt._locate_css(focusElement, \"gantt_cal_quick_info\")){\n\t\t\t\t\tenable = false;\n\t\t\t\t}else{\n\t\t\t\t\twhile(focusElement != parent &&  focusElement){\n\t\t\t\t\t\tfocusElement = focusElement.parentNode;\n\t\t\t\t\t}\n\n\t\t\t\t\tif(focusElement == parent){\n\t\t\t\t\t\tenable = true;\n\t\t\t\t\t}else{\n\t\t\t\t\t\tenable = false;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif(enable && !dispatcher.isEnabled()){\n\t\t\t\t\tdispatcher.enable();\n\t\t\t\t}else if(!enable && dispatcher.isEnabled()){\n\t\t\t\t\tdispatcher.disable();\n\t\t\t\t}\n\n\t\t\t}, 500);\n\n\t\t\tgantt.attachEvent(\"onDestroy\", function(){\n\t\t\t\tclearInterval(interval);\n\t\t\t});\n\n\t\t\tfunction getScopeName(obj){\n\t\t\t\tif(obj instanceof gantt.$keyboardNavigation.GanttNode){\n\t\t\t\t\treturn \"gantt\";\n\t\t\t\t}else if(obj instanceof gantt.$keyboardNavigation.HeaderCell){\n\t\t\t\t\treturn \"headerCell\";\n\t\t\t\t}else if(obj instanceof gantt.$keyboardNavigation.TaskRow){\n\t\t\t\t\treturn \"taskRow\";\n\t\t\t\t}else if(obj instanceof gantt.$keyboardNavigation.TaskCell){\n\t\t\t\t\treturn \"taskCell\";\n\t\t\t\t}\n\t\t\t\treturn null;\n\t\t\t}\n\n\t\t\tfunction getScope(mode){\n\t\t\t\tvar scopes = {\n\t\t\t\t\t\"gantt\":gantt.$keyboardNavigation.GanttNode,\n\t\t\t\t\t\"headerCell\": gantt.$keyboardNavigation.HeaderCell,\n\t\t\t\t\t\"taskRow\": gantt.$keyboardNavigation.TaskRow,\n\t\t\t\t\t\"taskCell\": gantt.$keyboardNavigation.TaskCell\n\t\t\t\t};\n\n\t\t\t\treturn scopes[mode] || scopes.gantt;\n\t\t\t}\n\n\t\t\tfunction findVisibleColumnIndex(columnName) {\n\t\t\t\tvar columns = gantt.getGridColumns();\n\t\t\t\tfor (var i = 0; i < columns.length; i++){\n\t\t\t\t\tif(columns[i].name == columnName){\n\t\t\t\t\t\treturn i;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn 0;\n\t\t\t}\n\n\t\t\tvar keyNavFacade = {};\n\t\t\teventable(keyNavFacade);\n\t\t\tgantt.mixin(keyNavFacade, {\n\t\t\t\taddShortcut: function(shortcut, handler, scope){\n\t\t\t\t\tvar scopeObject = getScope(scope);\n\t\t\t\t\tif(scopeObject){\n\t\t\t\t\t\tscopeObject.prototype.bind(shortcut, handler);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tgetShortcutHandler: function(shortcut, scope){\n\t\t\t\t\tvar commands = gantt.$keyboardNavigation.shortcuts.parse(shortcut);\n\t\t\t\t\tif(commands.length){\n\t\t\t\t\t\treturn this.getCommandHandler(commands[0], scope);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tgetCommandHandler: function(command, scope){\n\t\t\t\t\tvar scopeObject = getScope(scope);\n\t\t\t\t\tif(scopeObject){\n\t\t\t\t\t\tif(command){\n\t\t\t\t\t\t\treturn scopeObject.prototype.findHandler(command);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tremoveShortcut: function(shortcut, scope){\n\t\t\t\t\tvar scopeObject = getScope(scope);\n\t\t\t\t\tif(scopeObject){\n\t\t\t\t\t\tscopeObject.prototype.unbind(shortcut);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfocus: function(config){\n\t\t\t\t\tvar type = config ? config.type : null;\n\t\t\t\t\tvar constructor = getScope(type);\n\t\t\t\t\tvar node;\n\t\t\t\t\tswitch (type){\n\t\t\t\t\t\tcase \"taskCell\":\n\t\t\t\t\t\t\tnode = new constructor(config.id, findVisibleColumnIndex(config.column));\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase \"taskRow\":\n\t\t\t\t\t\t\tnode = new constructor(config.id);\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase \"headerCell\":\n\t\t\t\t\t\t\tnode = new constructor(findVisibleColumnIndex(config.column));\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tdefault:\n\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tdispatcher.delay(function(){\n\t\t\t\t\t\tif(node){\n\t\t\t\t\t\t\tdispatcher.setActiveNode(node);\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tdispatcher.enable();\n\t\t\t\t\t\t\tif(!dispatcher.getActiveNode()){\n\n\t\t\t\t\t\t\t\tdispatcher.setDefaultNode();\n\t\t\t\t\t\t\t}else{\n\n\t\t\t\t\t\t\t\tif(!dispatcher.awaitsFocus()){\n\t\t\t\t\t\t\t\t\tdispatcher.enable();\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t});\n\t\t\t\t},\n\n\t\t\t\tgetActiveNode: function(){\n\t\t\t\t\tif(dispatcher.isEnabled()){\n\t\t\t\t\t\tvar node = dispatcher.getActiveNode();\n\t\t\t\t\t\tvar scope = getScopeName(node);\n\t\t\t\t\t\tvar columns = gantt.getGridColumns();\n\t\t\t\t\t\tswitch (scope){\n\t\t\t\t\t\t\tcase \"taskCell\":\n\t\t\t\t\t\t\t\treturn {type:\"taskCell\", id:node.taskId, column:columns[node.columnIndex].name};\n\t\t\t\t\t\t\tcase \"taskRow\":\n\t\t\t\t\t\t\t\treturn {type:\"taskRow\", id:node.taskId};\n\t\t\t\t\t\t\tcase \"headerCell\":\n\t\t\t\t\t\t\t\treturn {type:\"headerCell\", column:columns[node.index].name};\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tgantt.$keyboardNavigation.facade = keyNavFacade;\n\n\t\t\tgantt.ext.keyboardNavigation = keyNavFacade;\n\t\t\tgantt.focus = function(){\n\t\t\t\tkeyNavFacade.focus();\n\t\t\t};\n\t\t\tgantt.addShortcut = keyNavFacade.addShortcut;\n\t\t\tgantt.getShortcutHandler = keyNavFacade.getShortcutHandler;\n\t\t\tgantt.removeShortcut = keyNavFacade.removeShortcut;\n\t\t})();\n\n\n\t}\n\n\tsetupKeyNav(gantt);\n\n\n\n})();", "var EventHost = function(){\n\tthis._connected = [];\n\tthis._silent_mode = false;\n};\n\nEventHost.prototype = {\n\t_silentStart: function() {\n\t\tthis._silent_mode = true;\n\t},\n\t_silentEnd: function() {\n\t\tthis._silent_mode = false;\n\t}\n};\n\nvar\tcreateEventStorage = function(obj) {\n\tvar dhx_catch = [];\n\tvar z = function(){\n\t\tvar res = true;\n\t\tfor (var i = 0; i < dhx_catch.length; i++){\n\t\t\tif (dhx_catch[i]){\n\t\t\t\tvar zr = dhx_catch[i].apply(obj, arguments);\n\t\t\t\tres=res&&zr;\n\t\t\t}\n\t\t}\n\t\treturn res;\n\t};\n\tz.addEvent=function(ev){\n\t\tif (typeof (ev) == \"function\")\n\t\t\treturn dhx_catch.push(ev)-1;\n\t\treturn false;\n\t};\n\tz.removeEvent=function(id){\n\t\tdhx_catch[id]=null;\n\t};\n\treturn z;\n};\n\nfunction makeEventable(obj){\n\n\tvar eventHost = new EventHost();\n\tobj.attachEvent=function(name, catcher, callObj){\n\t\tname='ev_'+name.toLowerCase();\n\t\tif (!eventHost[name])\n\t\t\teventHost[name] = createEventStorage(callObj||this);\n\n\t\treturn(name+':'+eventHost[name].addEvent(catcher)); //return ID (event name & event ID)\n\t};\n\tobj.attachAll = function(callback, callObj){\n\t\tthis.attachEvent('listen_all', callback, callObj);\n\t};\n\n\tobj.callEvent=function(name, arg0, callObj){\n\t\tif (eventHost._silent_mode) return true;\n\n\t\tvar handlerName = 'ev_'+name.toLowerCase();\n\n\t\tif (eventHost['ev_listen_all']){\n\t\t\teventHost['ev_listen_all'].apply(callObj || this, [name].concat(arg0));\n\t\t}\n\n\t\tif (eventHost[handlerName])\n\t\t\treturn eventHost[handlerName].apply(callObj || this, arg0);\n\t\treturn true;\n\t};\n\tobj.checkEvent=function(name){\n\t\treturn (!!eventHost['ev_'+name.toLowerCase()]);\n\t};\n\tobj.detachEvent=function(id){\n\t\tif (id){\n\t\t\tvar list = id.split(':');//get EventName and ID\n\t\t\tvar eventName = list[0];\n\t\t\tvar eventId = list[1];\n\n\t\t\tif(eventHost[eventName]){\n\t\t\t\teventHost[eventName].removeEvent(eventId); //remove event\n\t\t\t}\n\t\t}\n\t};\n\tobj.detachAllEvents = function(){\n\t\tfor (var name in eventHost){\n\t\t\tif (name.indexOf(\"ev_\") === 0)\n\t\t\t\tdelete eventHost[name];\n\t\t}\n\t};\n\n}\n\nmodule.exports = makeEventable;"], "sourceRoot": ""}