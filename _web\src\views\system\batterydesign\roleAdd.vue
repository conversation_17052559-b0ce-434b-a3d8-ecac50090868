<template>

    <a-modal title="新增" width="40%" :visible="visible"  @ok="handleSubmit"
             @cancel="handleCancel">

      <a-form :form="form" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="人员">
          <a-select
            show-search
            option-filter-prop="children"
            style="width: 100%"
            v-decorator="['account', { rules: [{ required: true, message: '请选择人员!' }] }]"
            @search="(value) => handleSearch(value,'userList')"
          >
            <a-select-option v-for="user in userList" :key="user.account + '-' + user.name">
              {{user.account + '-' + user.name}}
            </a-select-option>
          </a-select>

        </a-form-item>
        <a-form-item label="角色">
          <a-input placeholder="请输入角色" v-decorator="['roleName', {rules: [{required: true, message: '请输入角色！'}]}]" />
        </a-form-item>

      </a-form>

    </a-modal>

</template>

<script>
  import {
    getUserAllLists
  } from '@/api/modular/system/userManage'

  import {
    batteryDesignRoleAdd
  } from '@/api/modular/system/batterydesignCheckRecordManage'


  export default {
    data() {
      return {
        batteryId:null,
        data:[],
        form: this.$form.createForm(this),
        userList:[],
        visible: false,
        confirmLoading: false,
      }
    },

    methods: {
      handleSearch(value, listName) {
        if (value != '' && value != null) {
          getUserAllLists({account: value}).then((res) => {
            this[listName] = res.data
          })
        }

      },

      open(batteryId) {
        this.batteryId = batteryId
        this.visible = true
      },


      handleCancel() {
        this.userList = []
        this.form.resetFields()
        this.visible = false
      },

      handleSubmit() {
        const {
          form: {
            validateFields
          }
        } = this

        this.confirmLoading = true
        validateFields((errors, values) => {

          if (!errors) {
            let param = {}
            param.batteryId = this.batteryId
            param.account = values.account.split('-')[0]
            param.name = values.account.split('-')[1]
            param.roleName = values.roleName
            batteryDesignRoleAdd(param).then((res) => {
              this.confirmLoading = false
              if (res.success) {
                this.$message.success('新增成功')
                this.handleCancel()
                this.$emit('ok')
              } else {
                this.$message.error('新增失败：' + res.message)
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
    }
  }
</script>
<style lang="less" scoped>
  .ant-form-item {
    margin-bottom: 0px;
  }

  .title {
    margin-bottom: 20px;
  }

  .xing {
    color: red;
    font-weight: bolder;
  }

  .label {
    width: 30%;
    display: inline-flex;
  }

  .theme {
    font-size: larger;
    font-weight: bolder;
    margin-bottom: 20px;
  }

  div {
    color: black;
  }

  span {
    color: black;
  }

  .select {
    margin-bottom: 3px;
  }

  .check {
    height: 210px;
  }

  .approve {
    height: 90px;
  }

  /deep/ .ant-modal-title {

    font-weight: bolder;
    font-size: 22px;
    color: #0049b0;
  }

</style>
