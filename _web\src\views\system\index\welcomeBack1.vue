<template>
  <div class="main">
    <div class="left">
      <div class="item">
        <div class="item_left">
          <div class="head">
            <div class="left"><svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path fill-rule="evenodd" clip-rule="evenodd" d="M19 10C19 12.2091 17.2091 14 15 14C12.7909 14 11 12.2091 11 10C11 7.79086 12.7909 6 15 6C17.2091 6 19 7.79086 19 10ZM15 28C17.2091 28 19 26.2091 19 24C19 21.7909 17.2091 20 15 20C12.7909 20 11 21.7909 11 24C11 26.2091 12.7909 28 15 28ZM15 42C17.2091 42 19 40.2091 19 38C19 35.7909 17.2091 34 15 34C12.7909 34 11 35.7909 11 38C11 40.2091 12.7909 42 15 42Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M37 10C37 12.2091 35.2091 14 33 14C30.7909 14 29 12.2091 29 10C29 7.79086 30.7909 6 33 6C35.2091 6 37 7.79086 37 10ZM33 28C35.2091 28 37 26.2091 37 24C37 21.7909 35.2091 20 33 20C30.7909 20 29 21.7909 29 24C29 26.2091 30.7909 28 33 28ZM33 42C35.2091 42 37 40.2091 37 38C37 35.7909 35.2091 34 33 34C30.7909 34 29 35.7909 29 38C29 40.2091 30.7909 42 33 42Z" fill="#333"></path></g></svg><span>常用模板</span></div>
            <div class="right"><a>查看所有</a></div>
          </div>
          <div class="patterns">
            <div class="pattern">
              <p><img src="~@/assets/images/project.png"/>产品项目报告：</p>
              <p>1.失效分析报告</p>
              <p>2.项目变更申请</p>
              <p>3.G圆柱标准技术资料</p>
            </div>
            <div class="pattern">
              <p><img src="~@/assets/images/topics.png"/>技术课题：</p>
              <p>1.开题报告</p>
              <p>2.技术总结报告</p>
              <p>3.课题变更申请</p>
            </div>
            <div class="pattern">
              <p><img src="~@/assets/images/mem.png"/>行政人事：</p>
              <p>1.XXX</p>
              <p>2.XXX</p>
              <p>3.XXX</p>
            </div>
            <div class="pattern"></div>
          </div>
        </div>
        <div class="item_right">
          <div class="head"><div class="left"><svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path fill-rule="evenodd" clip-rule="evenodd" d="M19 10C19 12.2091 17.2091 14 15 14C12.7909 14 11 12.2091 11 10C11 7.79086 12.7909 6 15 6C17.2091 6 19 7.79086 19 10ZM15 28C17.2091 28 19 26.2091 19 24C19 21.7909 17.2091 20 15 20C12.7909 20 11 21.7909 11 24C11 26.2091 12.7909 28 15 28ZM15 42C17.2091 42 19 40.2091 19 38C19 35.7909 17.2091 34 15 34C12.7909 34 11 35.7909 11 38C11 40.2091 12.7909 42 15 42Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M37 10C37 12.2091 35.2091 14 33 14C30.7909 14 29 12.2091 29 10C29 7.79086 30.7909 6 33 6C35.2091 6 37 7.79086 37 10ZM33 28C35.2091 28 37 26.2091 37 24C37 21.7909 35.2091 20 33 20C30.7909 20 29 21.7909 29 24C29 26.2091 30.7909 28 33 28ZM33 42C35.2091 42 37 40.2091 37 38C37 35.7909 35.2091 34 33 34C30.7909 34 29 35.7909 29 38C29 40.2091 30.7909 42 33 42Z" fill="#333"></path></g></svg><span>常用功能</span></div></div>
          <div class="links link_more"  id="links">
            <div class="link" v-for="(item,i) in links.slice(0,links.length-leftNum)" :key='i'>
              <div class="icon_div">
                <img :src="baseUrl+item.iconId" :onerror="defaultImg" />
              </div>
              <div class="txt_div">{{item.linkName}}</div>
            </div>

            <div class="link" v-if="leftNum == 0">
              <a style="display: inline-block;margin-top: 6px;" @click="$refs.addlink.add()">
                <svg class="fill" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="35" height="35"><defs data-reactroot=""></defs><g><path d="M22.229 16.889h-5.34v5.34c0 0.489-0.398 0.889-0.889 0.889s-0.889-0.4-0.889-0.889v-5.34h-5.34c-0.489 0-0.889-0.398-0.889-0.889s0.4-0.889 0.889-0.889h5.34v-5.34c0-0.489 0.398-0.889 0.889-0.889s0.889 0.4 0.889 0.889v5.34h5.34c0.489 0 0.889 0.398 0.889 0.889s-0.4 0.889-0.889 0.889zM16 1.778c-7.854 0-14.222 6.368-14.222 14.222s6.368 14.222 14.222 14.222c7.854 0 14.222-6.368 14.222-14.222s-6.368-14.222-14.222-14.222z"></path></g></svg>
              </a>
            </div>

            <div style="cursor:pointer" @mouseenter="show_more(true)" @mouseleave="show_more(false)" class="link showmore" v-if="leftNum > 0">
              <div class="icon_div">
                <img src="~@/assets/images/icon_more.png" />
              </div>
              <div class="txt_div">more</div>
            </div>

            <div @mouseenter="show_more(true)" @mouseleave="show_more(false)" v-show="show" class="links_pop" :style="{width:`${boxLength}px`}">
              <div class="link_pop"  v-for="(item,i) in links.slice(links.length-leftNum)" :key='i'>
                <div class="pop_icon_div">
                  <img :src="baseUrl+item.iconId" :onerror="defaultImg" />
                </div>
                <div class="pop_txt_div">{{item.linkName}}</div>
              </div>
              <div class="angle"></div>
            </div>

          </div>
        </div>
      </div>
      <div class="item">
        <div class="head"><div class="left"><svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path fill-rule="evenodd" clip-rule="evenodd" d="M19 10C19 12.2091 17.2091 14 15 14C12.7909 14 11 12.2091 11 10C11 7.79086 12.7909 6 15 6C17.2091 6 19 7.79086 19 10ZM15 28C17.2091 28 19 26.2091 19 24C19 21.7909 17.2091 20 15 20C12.7909 20 11 21.7909 11 24C11 26.2091 12.7909 28 15 28ZM15 42C17.2091 42 19 40.2091 19 38C19 35.7909 17.2091 34 15 34C12.7909 34 11 35.7909 11 38C11 40.2091 12.7909 42 15 42Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M37 10C37 12.2091 35.2091 14 33 14C30.7909 14 29 12.2091 29 10C29 7.79086 30.7909 6 33 6C35.2091 6 37 7.79086 37 10ZM33 28C35.2091 28 37 26.2091 37 24C37 21.7909 35.2091 20 33 20C30.7909 20 29 21.7909 29 24C29 26.2091 30.7909 28 33 28ZM33 42C35.2091 42 37 40.2091 37 38C37 35.7909 35.2091 34 33 34C30.7909 34 29 35.7909 29 38C29 40.2091 30.7909 42 33 42Z" fill="#333"></path></g></svg><span>任务管理</span></div></div>
      </div>
      <div class="item">
        <div class="head"><div class="left"><svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path fill-rule="evenodd" clip-rule="evenodd" d="M19 10C19 12.2091 17.2091 14 15 14C12.7909 14 11 12.2091 11 10C11 7.79086 12.7909 6 15 6C17.2091 6 19 7.79086 19 10ZM15 28C17.2091 28 19 26.2091 19 24C19 21.7909 17.2091 20 15 20C12.7909 20 11 21.7909 11 24C11 26.2091 12.7909 28 15 28ZM15 42C17.2091 42 19 40.2091 19 38C19 35.7909 17.2091 34 15 34C12.7909 34 11 35.7909 11 38C11 40.2091 12.7909 42 15 42Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M37 10C37 12.2091 35.2091 14 33 14C30.7909 14 29 12.2091 29 10C29 7.79086 30.7909 6 33 6C35.2091 6 37 7.79086 37 10ZM33 28C35.2091 28 37 26.2091 37 24C37 21.7909 35.2091 20 33 20C30.7909 20 29 21.7909 29 24C29 26.2091 30.7909 28 33 28ZM33 42C35.2091 42 37 40.2091 37 38C37 35.7909 35.2091 34 33 34C30.7909 34 29 35.7909 29 38C29 40.2091 30.7909 42 33 42Z" fill="#333"></path></g></svg><span>我的项目</span></div></div>
      </div>
    </div>
    <div class="right">
      <div class="item">
        <div class="item_top">
          <div class="head">
            <div class="left"><svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path fill-rule="evenodd" clip-rule="evenodd" d="M19 10C19 12.2091 17.2091 14 15 14C12.7909 14 11 12.2091 11 10C11 7.79086 12.7909 6 15 6C17.2091 6 19 7.79086 19 10ZM15 28C17.2091 28 19 26.2091 19 24C19 21.7909 17.2091 20 15 20C12.7909 20 11 21.7909 11 24C11 26.2091 12.7909 28 15 28ZM15 42C17.2091 42 19 40.2091 19 38C19 35.7909 17.2091 34 15 34C12.7909 34 11 35.7909 11 38C11 40.2091 12.7909 42 15 42Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M37 10C37 12.2091 35.2091 14 33 14C30.7909 14 29 12.2091 29 10C29 7.79086 30.7909 6 33 6C35.2091 6 37 7.79086 37 10ZM33 28C35.2091 28 37 26.2091 37 24C37 21.7909 35.2091 20 33 20C30.7909 20 29 21.7909 29 24C29 26.2091 30.7909 28 33 28ZM33 42C35.2091 42 37 40.2091 37 38C37 35.7909 35.2091 34 33 34C30.7909 34 29 35.7909 29 38C29 40.2091 30.7909 42 33 42Z" fill="#333"></path></g></svg><span>流程审批</span></div>
            <div class="right"><a>查看所有</a></div>
          </div>
          <div class="content">
            <div class="content_left">
              <div class="tip">
                <div class="icon">
                  <span><svg style="fill:#e5f4ff" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 48 48" width="25.82" height="25.82"><defs data-reactroot=""></defs><g><rect width="48" height="48" fill="white" fill-opacity="0.01"></rect><path d="M5 37C5 35.8954 5.89543 35 7 35H41C42.1046 35 43 35.8954 43 37V42C43 43.1046 42.1046 44 41 44H7C5.89543 44 5 43.1046 5 42V37Z" fill="none" stroke="#74c0fc" stroke-width="4"></path><path d="M5 31C5 29.8954 5.89543 29 7 29H41C42.1046 29 43 29.8954 43 31V42C43 43.1046 42.1046 44 41 44H7C5.89543 44 5 43.1046 5 42V31Z" stroke="#74c0fc" stroke-width="4"></path><path d="M18.763 15.6637C18.9051 15.2657 19.2821 15 19.7047 15H28.2953C28.7179 15 29.0949 15.2657 29.237 15.6637L34 29H14L18.763 15.6637Z" stroke="#74c0fc" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path><rect x="15" y="4" width="18" height="10.8" rx="5.4" fill="none" stroke="#74c0fc" stroke-width="4"></rect></g></svg></span>
                </div>
                <div class="text">
                  <div>3</div>
                  <div>待我审批</div>
                </div>
              </div>
              <div class="tip">
                <div class="icon">
                  <span><svg style="fill:#e5f4ff" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 48 48" width="25.82" height="25.82"><defs data-reactroot=""></defs><g><g><rect width="48" height="48" fill="white" fill-opacity="0.01" stroke-linecap="round" stroke-linejoin="round" stroke-width="4" stroke="none" fill-rule="evenodd"></rect><g transform="translate(4.000000, 3.953742)"><g transform="translate(0.000000, 4.046258)"><path d="M36.9898007,0.0264752559 L8.18181818,0.0264752559 C5.45454545,0.0264752559 0,1.550592 0,7.96725867 C0,14.3839253 5.45454545,16 8.18181818,16 L31.9938556,16 C34.7211284,16 40,17.5679494 40,23.9846161 C40,30.4012828 34.7211284,32.0029211 31.9938556,32.0029211 L2.06499237,32.0029211" stroke-linecap="round" stroke-linejoin="round" stroke-width="4" stroke="#74C0FC" fill="none" fill-rule="evenodd"></path><polyline points="4.04568993 27.99261 0.0673983189 32.0591365 4.04568993 36" stroke-linecap="round" stroke-linejoin="round" stroke-width="4" stroke="#74C0FC" fill="none" fill-rule="evenodd"></polyline></g><polyline transform="translate(36.032050, 4.003695) scale(-1, 1) translate(-36.032050, -4.003695) " points="38.0211961 5.32907052e-15 34.0429045 4.06652653 38.0211961 8.00739003" stroke-linecap="round" stroke-linejoin="round" stroke-width="4" stroke="#74C0FC" fill="none" fill-rule="evenodd"></polyline></g></g></g></svg></span>
                </div>
                <div class="text">
                  <div>4</div>
                  <div>我发起的</div>
                </div>
              </div>
            </div>
            <div class="content_right">
              <p>新建流程</p>
              <p>流程待阅(99)</p>
              <p>流程已阅(99)</p>
            </div>
          </div>
        </div>
        <div class="item_bottom">
          <div class="head">
            <div class="left"><svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path fill-rule="evenodd" clip-rule="evenodd" d="M19 10C19 12.2091 17.2091 14 15 14C12.7909 14 11 12.2091 11 10C11 7.79086 12.7909 6 15 6C17.2091 6 19 7.79086 19 10ZM15 28C17.2091 28 19 26.2091 19 24C19 21.7909 17.2091 20 15 20C12.7909 20 11 21.7909 11 24C11 26.2091 12.7909 28 15 28ZM15 42C17.2091 42 19 40.2091 19 38C19 35.7909 17.2091 34 15 34C12.7909 34 11 35.7909 11 38C11 40.2091 12.7909 42 15 42Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M37 10C37 12.2091 35.2091 14 33 14C30.7909 14 29 12.2091 29 10C29 7.79086 30.7909 6 33 6C35.2091 6 37 7.79086 37 10ZM33 28C35.2091 28 37 26.2091 37 24C37 21.7909 35.2091 20 33 20C30.7909 20 29 21.7909 29 24C29 26.2091 30.7909 28 33 28ZM33 42C35.2091 42 37 40.2091 37 38C37 35.7909 35.2091 34 33 34C30.7909 34 29 35.7909 29 38C29 40.2091 30.7909 42 33 42Z" fill="#333"></path></g></svg><span>消息中心</span></div>
            <div class="right"><a>查看所有</a></div>
          </div>
          <div class="notice">
            <p>【公告】3月2日，作息时间调整为12:30</p>
            <p>【重要通知】OA系统维护</p>
          </div>
        </div>
      </div>
      <div class="item">
        <div class="head"><div class="left"><svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path fill-rule="evenodd" clip-rule="evenodd" d="M19 10C19 12.2091 17.2091 14 15 14C12.7909 14 11 12.2091 11 10C11 7.79086 12.7909 6 15 6C17.2091 6 19 7.79086 19 10ZM15 28C17.2091 28 19 26.2091 19 24C19 21.7909 17.2091 20 15 20C12.7909 20 11 21.7909 11 24C11 26.2091 12.7909 28 15 28ZM15 42C17.2091 42 19 40.2091 19 38C19 35.7909 17.2091 34 15 34C12.7909 34 11 35.7909 11 38C11 40.2091 12.7909 42 15 42Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M37 10C37 12.2091 35.2091 14 33 14C30.7909 14 29 12.2091 29 10C29 7.79086 30.7909 6 33 6C35.2091 6 37 7.79086 37 10ZM33 28C35.2091 28 37 26.2091 37 24C37 21.7909 35.2091 20 33 20C30.7909 20 29 21.7909 29 24C29 26.2091 30.7909 28 33 28ZM33 42C35.2091 42 37 40.2091 37 38C37 35.7909 35.2091 34 33 34C30.7909 34 29 35.7909 29 38C29 40.2091 30.7909 42 33 42Z" fill="#333"></path></g></svg><span>日程</span></div></div>
      </div>
    </div>
    <addlink ref="addlink" @ok="handleOk" />
  </div>
  
</template>

<script>
import {getLinks} from "@/api/modular/system/linkManage"
import noneimg from '@/assets/none.svg'
import addlink from './addLink'
export default {
    components: {
        addlink
    },
   data () {
    return {
      defaultImg:'this.src="' + noneimg + '"',
      baseUrl:process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id=',
      show:false,
      boxLength:0,
      leftNum:0,
      link:{
        width:106,
        height:66,
      },
      links:[
        /* {
          imgsrc:require('@/assets/images/Confluence.png'),
          txt:'Confluence'
        }, */
      ],
    }
   },
   methods:{
    
    handleOk(params) {
      this.links.push(params)
      this.initMenus()
    },
    show_more(show){
      this.show = show
    },
    initMenus(){
      this.$nextTick(()=>{
        let boxLength = document.getElementById('links').offsetWidth * 3 //三行
        let allWidth = (this.links.length + 1) * this.link.width
        this.boxLength = boxLength/3
        if (allWidth >= boxLength) {
          this.leftNum = this.links.length - Math.floor(boxLength/this.link.width)+1
        }else{
          this.leftNum = 0
        }
      })
    },
    callGetLinks() {
        getLinks({})
            .then((res) => {
                if (res.success) {
                    this.links = res.data
                    this.initMenus()
                } else {
                    this.$message.error(res.message, 1);
                }
            })
            .catch((err) => {
                this.$message.error('错误提示：' + err.message, 1)
            });
    },
   },
   created(){
    
   },
   mounted(){
    this.callGetLinks()
    window.addEventListener('resize', this.initMenus)
   },
   beforeDestroy(){
    window.removeEventListener('resize',this.initMenus)
  },
   
}
</script>

<style lang="less" scoped=''>
  .main{
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    height: 100%;
  }
  .left{
    width: 70%;
    background: #fff;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    margin-left: 8px;
  }
  .left .item:first-child{
    flex-direction: row;
  }
  .right{
    flex: 1;
    background: #fff;
    margin-left: 8px;
    margin-right: 8px;
  }
  .item{
    display: flex;
    width: 100%;
    flex-direction: column;
    flex-wrap: nowrap;
    min-height: 274px;
    margin-top: 12px;
    background: #eee;
  }
  .item .item_top,.item .item_bottom{
    min-height: 131px;
    width: 100%;
    background: #ead5d5;
  }
  .item .item_bottom{
    margin-top: 12px;
  }

  .item .item_bottom .notice{
    padding: 8px;
  }

  .item .item_bottom .notice p{
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-bottom: 2px;
    font-family: 'PingFangSC';
    font-size: 12px;
    font-weight: 400;
    color: rgb(16, 16, 16);
    letter-spacing: 0px;
    line-height: 17px;
  }
  
  .item .item_left,.item .item_right{
    display: block;
    
  }
  


  .item .item_left{
    width: 40%;
    background: #eee;
  }
  .item .item_right{
    width: 60%;
    background: #e7a0a0;
    margin-left: 8px;
  }

  .item .item_right .links{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    
  }

  .links_pop{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
  }

  .item .item_right .links .link{
    width: 106.5px;
    text-align: center;
    padding-top: 20px;
  }

  .link_pop{
    width: 106.5px;
    text-align: center;
    margin-top: 10px;
    height: 51px;
  }
  .item .item_right .links .link div.icon_div{
    width: 100%;
    overflow: hidden;
    height: 30px;
  }
  .link_pop div.pop_icon_div{
    width: 100%;
    overflow: hidden;
    height: 30px;
  }
  .item .item_right .links .link div.icon_div img{
    max-height: 90%;
  }
  .link_pop div.pop_icon_div img{
    max-height: 70%;
  }
  .right .item .content{
    display: flex;
    margin: 5px;
    flex-direction: row;
    height: 85px;
  }
  .link div.txt_div{
    font-size: 13px;
  }.link_pop div.pop_txt_div{
    font-size: 12px;
  }
  .right .item .content .content_left{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-evenly;
    width: 68%;
    
  }

  .right .item .content .content_right{
    width: 32%;
    padding-left: 16px;
    font-size: 13px;
    border-left: 2px solid #d9d9d940;
  }

  .right .item .content .content_right p{
    margin-bottom: 5px;
    color: #7580cf;
  }

  .right .item .content .content_right p:first-child{
    margin-top: 2px;
  }

  .right .item .content .content_left div.tip{
    display: flex;
    flex-direction: row;
  }

  .right .item .content .content_left div.icon,.right .item .content .content_left div.text{
    width: 46px;
    height: 46px;
    background: #fff;
    text-align: center;
  }

  .right .item .content .content_left div.icon{
    
    font-size: 14px;
    display: flex;
    background: #e5f4ff;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
  }

  .right .item .content .content_left div.text{
    width: 60px;
    font-size: 12px;
    font-weight: bold;
    display: flex;
    flex-direction: column;
    align-items: center;
    letter-spacing: 1px;
    color: #6c6c6c;
    margin-left: 4px;
    font-family: 'oppo-sans';
  }
  .right .item .content .content_left div.text div:first-child{
    font-size: 30px;
    margin-bottom: -7px;
    margin-top: -8px;
  }

  .head {
    color: #000;
    font-size: 16px;
    border-bottom: 1px solid #e5e5e5;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
}
.head svg{
  margin-right:2px ;
}
.head .right{
  font-weight: initial;
  font-size: 13px;
  float: right;
  text-align: right;
}
.head .left{
  display: flex;
  align-items: center;
  margin: 0;
  flex-direction: row;
}
.fill:not([stroke]) {
    fill: rgb(64, 149, 229);
}
.link_more{
  position: relative;
}
.links_pop{
  position: absolute;
  bottom: 50px;
  background: #fff;
  box-shadow: 0 0 7px rgba(0, 0, 0, .15);
  left: 0;
}
.angle{
  width: 11px;
  height: 11px;
  background: #fff;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  position: absolute;
  bottom: -4px;
  right: 48px;
}

.patterns{
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
.pattern{
  width: 50%;
  border: 1px solid #d9d9d9;
  height: 120px;
  padding: 5px;
  font-size: 13px;
}
.pattern p{
  margin-bottom: 8px;
  letter-spacing: 1px;
}
.pattern p:first-child{
  font-size: 15px;
  display: flex;
  align-items: center;
}

.pattern p img{
  width: 20px;
  margin-right: 5px;
}
</style>
