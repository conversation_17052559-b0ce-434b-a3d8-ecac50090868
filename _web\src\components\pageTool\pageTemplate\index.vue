<template>
	<div class="container">
		<!-- 标题 start -->
		<div class="head-title">
			<div class="line mr10"></div>
			<span class="title">标准库</span>
		</div>
		<!-- 标题 end -->
		<!-- 筛选 start -->
		<div class="filter-wrapper mt10">
			<div class="filter-left">
				<div class="filter-block mr10">
					<a-select class="filter-select" placeholder="请选择工序">
						<a-select-option v-for="item in processOption" value="item.value">
							{{ item.label }}
						</a-select-option>
					</a-select>
				</div>
				<div class="filter-block mr10">
					<a-input-search class="filter-input" placeholder="请输入产品型号" />
				</div>
				<div class="filter-block">
					<a-input-search class="filter-input" placeholder="请输入任务单号" />
				</div>
			</div>

			<div class="filter-right">
				<a-button class="mr10">新增</a-button>
				<a-button type="primary">
					导出
				</a-button>
			</div>
		</div>
		<!-- 筛选 end -->
		<!-- 表格 start -->
		<div class="table-wrapper mt10">
			<a-table :columns="tableColumns" :data-source="tableData" size="middle">
				<a slot="name" slot-scope="text">{{ text }}</a>
				<span slot="customTitle"><a-icon type="smile-o" /> Name</span>
				<span slot="tags" slot-scope="tags">
					<a-tag
						v-for="tag in tags"
						:key="tag"
						:color="tag === 'loser' ? 'volcano' : tag.length > 5 ? 'geekblue' : 'green'"
					>
						{{ tag.toUpperCase() }}
					</a-tag>
				</span>
				<span slot="action" slot-scope="text, record">
					<a>Invite 一 {{ record.name }}</a>
					<a-divider type="vertical" />
					<a>Delete</a>
					<a-divider type="vertical" />
					<a class="ant-dropdown-link"> More actions <a-icon type="down" /> </a>
				</span>
			</a-table>
		</div>
		<!-- 表格 end -->
	</div>
</template>
<script>
export default {
	data() {
		return {
			tableHeight: document.documentElement.clientHeight - 40 - 20 - 24 - 10 - 32 - 10 - 20,
			processOption: [
				{
					label: "正极搅拌",
					value: 0
				},
				{
					label: "负极搅拌",
					value: 1
				}
			],
			tableColumns: [
				{
					dataIndex: "name",
					key: "name",
					slots: { title: "customTitle" },
					scopedSlots: { customRender: "name" }
				},
				{
					title: "Age",
					dataIndex: "age",
					key: "age"
				},
				{
					title: "Address",
					dataIndex: "address",
					key: "address"
				},
				{
					title: "Tags",
					key: "tags",
					dataIndex: "tags",
					scopedSlots: { customRender: "tags" }
				},
				{
					title: "Action",
					key: "action",
					scopedSlots: { customRender: "action" }
				}
			],
			tableData: Array(20).fill({
				key: "1",
				name: "John Brown",
				age: 32,
				address: "New York No. 1 Lake Park",
				tags: ["nice", "developer"]
			})
		}
	},
	watch: {
		tableData(newVal, oldVal) {
			const subtrahend = this.tableData.length > 0 ? 45 : 0
			document.documentElement.style.setProperty(`--height`, `${this.tableHeight - subtrahend}px`)
		}
	},
	created() {
	},
	mounted() {
		const subtrahend = this.tableData.length > 0 ? 45 : 0
		document.documentElement.style.setProperty(`--height`, `${this.tableHeight - subtrahend}px`)
	},
	components: {},
	methods: {}
}
</script>
<style lang="less" scoped>
:root {
	--height: 600px;
}
/* 标题 */
.head-title {
	display: flex;
	align-items: center;
}
.head-title .line {
	width: 4px;
	height: 22px;
	background: #3293ff;
	border-radius: 20px;
}
.head-title .title {
	font-size: 16px;
	font-weight: 600;
}
/* 筛选 */
.filter-wrapper {
	display: flex;
	justify-content: space-between;
}
.filter-left {
	display: flex;
}
.filter-right {
	display: flex;
}
/* 表格 */
.table-wrapper {
	padding: 10px;
	background: #fff;
	border-radius: 10px;
}

/* 通用  */

.mr10 {
	margin-right: 10px;
}
.mt10 {
	margin-top: 10px;
}

.filter-select {
	width: 175px;
}
.filter-input {
	width: 175px;
}

/* 表格组件 */
/deep/ .ant-table tr th {
	background: #f4f4f4;
	font-size: 13px;
}

/deep/.ant-table-body {
	height: var(--height) !important;
	overflow-y: scroll;
}

/deep/.ant-table-thead {
	position: sticky;
	top: 0;
	z-index: 666;
}
/deep/ .ant-pagination {
	margin: 10px 0;
}
/deep/ .ant-table-placeholder {
	border: none;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}
</style>
