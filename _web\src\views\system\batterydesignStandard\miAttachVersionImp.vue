<template>
  <div>
    <div style="height: 35px">
      <div style="width: 135px;height:35px;float: right;margin: 10px 0px 0px 0px">
         <span style="padding: 10px 6px 10px 0px"><svg xmlns="http://www.w3.org/2000/svg"
                                                       class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 jZPaJQ svg-icon-path-icon fill"
                                                       viewBox="0 -4 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path
           d="M5 8C5 6.89543 5.89543 6 7 6H19L24 12H41C42.1046 12 43 12.8954 43 14V40C43 41.1046 42.1046 42 41 42H7C5.89543 42 5 41.1046 5 40V8Z"
           fill="none" stroke="rgb(84, 152, 255)" stroke-width="4" stroke-linejoin="round"></path><path
           d="M30 28L23.9933 34L18 28.0134" stroke="rgb(84, 152, 255)" stroke-width="4" stroke-linecap="round"
           stroke-linejoin="round"></path><path d="M24 20V34" stroke="rgb(84, 152, 255)" stroke-width="4"
                                                stroke-linecap="round" stroke-linejoin="round"></path></g></svg></span>
        <a class="exportButtonClass" @click="miExportTable()">MI设计导出</a>
      </div>
      <div style="width: 140px;float: right;margin: 10px 10px 0px 0px">
        <a-icon type="plus" style="font-size:18px;color:dodgerblue;" v-if="canUpdate"/>
        <a style="margin-left:5px;font-size:15px;color:dodgerblue" @click="miReImport()" v-if="canUpdate">MI选型重新导入</a>
      </div>
      <div style="float: left;width: 200px;margin: 10px 0px 0px 30px">
        <div class="block small-mark"></div>
        <div style="width: 120px;float: right;margin: 0px 20px 15px 0px;font-weight: bold;">已发生更改内容项</div>
      </div>
    </div>
    <div>
      <a-spin :spinning="isLoading">
      <a-table
        id="develop"
        :columns="columns"
        :data-source="resultData"
        :row-key="(record) => record.id"
        :pagination="false"
        :scroll="{ y: windowHeight }"
        bordered
      >
      <div slot="description" slot-scope="text,record">
        <a-tooltip placement="topLeft" arrow-point-at-center>
          <template slot="title" v-if="record.oriDescription !== undefined">
            {{ record.oriDescription }}
          </template>
          <a-textarea v-model="record.description" :disabled="!canUpdate"
                      :auto-size="{ minRows: 4, maxRows: 5 }"
                 @blur="updateData($event,record,'description')"/>
        </a-tooltip>

      </div>
        <div slot="attachPicture"
             slot-scope="text,record,index"
             id="attachPictureId">
        <a-upload
          list-type="picture-card"
          class="avatar-uploader"
          :headers="headers"
          :disabled="!canUpdate"
          :action="postUrl"
          :fileList="record.attachPicture"
          @preview="handlePreview"
          @change="handleChange($event,record)"
        >
          <a-icon v-if="record.attachPicture.length < 6 && canUpdate" type="plus"  />
        </a-upload>
        <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel">
          <img alt="example" style="width: 100%" :src="previewImage" />
        </a-modal>
      </div>
      <div slot="remark" slot-scope="text,record">
        <a-tooltip placement="topLeft" arrow-point-at-center>
          <template slot="title" v-if="record.oriRemark !== undefined">
            {{ record.oriRemark }}
          </template>
          <a-textarea v-model="record.remark" style="width:90%" :disabled="!canUpdate"
                      :auto-size="{ minRows: 4, maxRows: 6 }"
                 @blur="updateData($event,record,'remark')"/>
        </a-tooltip>
<!--        <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => deleteMiAttachVersion(record.id)">-->
<!--          <a-icon class="deleteIcon" type="minus-circle" style="color: red" :style="{paddingLeft: bigClient?'2.1%':'1.85%'}"/>-->
<!--        </a-popconfirm>-->
      </div>
      <template slot="title">
        <table>
          <tr class="renderTr">
            <td style="width: 20%;" rowspan="4" colspan="2">
              <img src="/img/logo.53575418.png" alt="logo" style="width: 80px;height: 80px">
            </td>
            <td style="width: 50%;font: bold normal 18px arial;" rowspan="2" colspan="4">Manufacturing Istruction</td>
            <td style="width: 10%;font-weight: bold">文件编号：</td>
            <td style="width: 20%" colspan="3" id="render_documentNo">
              <input :value="libraryData.documentNo"  :disabled="!canUpdate"
                     @change="updateLibData($event, libraryData, 'documentNo')"/>
            </td>
          </tr>
          <tr class="renderTr">
            <td style="font-weight: bold">版本：</td>
            <td colspan="3" id="render_version">
              <input :value="libraryData.version"  :disabled="!canUpdate"
                     @change="updateLibData($event, libraryData, 'version')"/>
            </td>
          </tr>
          <tr class="renderTr">
            <td colspan="4" rowspan="2" id="render_attachVersion">
              <input :value="libraryData.attachVersion"  :disabled="!canUpdate"
                     style="font: bold normal 16px arial;"
                     @change="updateLibData($event, libraryData, 'attachVersion')"/>
            </td>
            <td style="font-weight: bold">样品阶段：</td>
            <td colspan="3" id="render_sampleStage">
              <input :value="libraryData.sampleStage"  :disabled="!canUpdate"
                     @change="updateLibData($event, libraryData, 'sampleStage')"/>
            </td>
          </tr>
          <tr class="renderTr">
            <td style="font-weight: bold">页码：</td>
            <td colspan="3" id="render_page">
              <input :value="libraryData.page"  :disabled="!canUpdate"
                     @change="updateLibData($event, libraryData, 'page')"/>
            </td>
          </tr>
        </table>
      </template>
      <template slot="footer" slot-scope="currentPageData">
        <table>
          <tr>
            <td style="padding: 0;border: 0;width: 10%;"></td>
            <td style="padding: 0;border: 0;width: 10%;"></td>
            <td style="padding: 0;border: 0;width: 20%;"></td>
            <td style="padding: 0;border: 0;width: 20%;"></td>
            <td style="padding: 0;border: 0;width: 20%;"></td>
            <td style="padding: 0;border: 0;width: 20%;"></td>
          </tr>
<!--          <tr style="border: 1px solid black;">-->
<!--            <td id="iconPlusTd">-->
<!--              <a-icon type="plus" slot="footer" :style="{paddingLeft: bigClient?'2.1%':'1.85%'}"-->
<!--                      @click="addMIAttachVersion(libraryData)"/>-->
<!--            </td>-->
<!--          </tr>-->
          <tr>
            <td>编制</td>
            <td id="render_miavEstablishment">
              <input :value="libraryData.miavEstablishment"  :disabled="!canUpdate"
                     @change="updateLibData($event, libraryData, 'miavEstablishment')"/>
            </td>
            <td>审核</td>
            <td id="render_miavAudit">
              <input :value="libraryData.miavAudit"  :disabled="!canUpdate"
                     @change="updateLibData($event, libraryData, 'miavAudit')"/>
            </td>
            <td>批准</td>
            <td id="render_miavApproval">
              <input :value="libraryData.miavApproval"  :disabled="!canUpdate"
                     @change="updateLibData($event, libraryData, 'miavApproval')"/>
            </td>
          </tr>
        </table>
      </template>
    </a-table>
      </a-spin>
    </div>
    <mi-lib-data-dialog ref="miLibDataDialog"/>
  </div>
</template>
<script>
import Vue from "vue";
import { ACCESS_TOKEN } from "@/store/mutation-types";
import $ from 'jquery';
import { message } from "ant-design-vue";
import { getBatteryDesign } from "@/api/modular/system/batterydesignManage";
import { getMIStandardLibCurById, updateMIStandardLibCur } from "@/api/modular/system/gCylinderMILibCurManage";
import {
  getMIAttachVersionCurList,
  insertMIAttachVersionCur,
  updateMIAttachVersionCur
} from "@/api/modular/system/miAttachVersionCurManage";
import { getMIStandardByImpBatteryId, miOutputExportExcel } from "@/api/modular/system/gCylinderMILibManage";
import miLibDataDialog from "@/views/system/batterydesignStandard/miLibDataDialog.vue";
import { EventBus } from "@/api/modular/system/eventBus";
function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}
  export default {
    components: {
      miLibDataDialog
    },
    props: ['deliverImpBatteryId','canUpdate'],
    data() {
      return {
        saveData: {},
        headers: {
          Authorization: 'Bearer ' + Vue.ls.get(ACCESS_TOKEN),
        },
        postUrl: '/api/sysFileInfo/uploadfile',
        previewVisible: false,
        previewImage: '',
        // 表头
        columns: [
          {
            title: '描述',
            dataIndex: 'description',
            scopedSlots: { customRender: 'description' },
            align: 'center',
            width: '20%',
          },
          {
            title: '附图',
            dataIndex: 'attachPicture',
            scopedSlots: { customRender: 'attachPicture' },
            align: 'center',
            width: '60%',
          },
          {
            title: '备注',
            dataIndex: 'remark',
            align: 'center',
            width: '20%',
            scopedSlots: { customRender: 'remark' },
          },
    ],
        visible: false,
        impBatteryId: null,
        libraryId: null,
        isLoading: false,
        editVisible: false,
        confirmLoading: false,
        mIStandardLibData: {},
        design: {},
        bigClient: document.documentElement.clientHeight > 700,
        windowHeight: document.documentElement.clientHeight - 200,
        form: this.$form.createForm(this,{ name: 'form' }),
        editForm: this.$form.createForm(this,{ name: 'editForm' }),
        resultData: [],
        libraryData: {},
      };
    },
    created() {
      getMIStandardByImpBatteryId(this.deliverImpBatteryId).then(res => {
        this.libraryId = res.data
        this.impBatteryId = this.deliverImpBatteryId
        this.getAndRenderLibData(this.libraryId, this.impBatteryId)
        this.getAndRenderDataByLibraryId(this.libraryId, this.impBatteryId)
        EventBus.$on('miAttachVerEvent', (libraryId) => {
          if (libraryId && this.impBatteryId) {
            this.libraryId = libraryId
            this.getAndRenderLibData(libraryId, this.impBatteryId)
            this.getAndRenderDataByLibraryId(libraryId, this.impBatteryId)
          }
        })
      })
    },
    destroyed() {
      EventBus.$off('miAttachVerEvent', () => {
      })
    },
    mounted() {
      $(".ant-breadcrumb-separator").eq(2).css('display','none')
      document.getElementsByClassName("ant-layout-content")[0].style.backgroundColor = 'white';
    },
    methods: {
      getAndRenderLibData(id, impBatteryId) {
        // console.log(id)
        getMIStandardLibCurById({ id: id }, impBatteryId).then(res => {
          const finallyData = res.data;
          this.libraryData = finallyData.curData
          this.renderLibData(finallyData.compareMap.diffList, finallyData.compareMap.sameList)
        })
      },
      renderLibData(diff, same) {
        // console.log('diff',diff)
        // console.log('same',same)
        diff.forEach(item => {
            // $('.attachVerTabClass #render_' + item).css("background-color", "yellow")
            $('.attachVerTabClass #render_' + item).addClass("mark")
        })
        same.forEach(item => {
            // $('.attachVerTabClass #render_' + item).css("background-color", "white")
            $('.attachVerTabClass #render_' + item).removeClass("mark")
        })
      },
      handleCancel() {
        this.previewVisible = false;
      },
      async handlePreview(file) {
        if (!file.url && !file.preview) {
          file.preview = await getBase64(file.originFileObj);
        }
        this.previewImage = file.url || file.preview;
        this.previewVisible = true;
      },
      handleChange(info,record) {
        record.attachPicture = [...info.fileList]
        if (info.file.status === 'done') {
          let targetImgs = [...record.attachPicture].slice(-1);
          let finallyImgs = [...record.attachPicture].slice(0,-1)
          finallyImgs.push({
            uid: targetImgs[0].uid,
            name: targetImgs[0].name,
            status: targetImgs[0].status,
            url: process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id=' + targetImgs[0].response.data.id,
          })
          let res = info.file.response
          if (res.success) {
            let update = {}
            update.id = record.id
            update.attachPicture = JSON.stringify(finallyImgs)
            updateMIAttachVersionCur(update).then(res => {
              if (res.success) {
                this.$message.success(`${info.file.name} 图片上传成功`)
                this.getAndRenderDataByLibraryId(this.libraryData.libraryId, this.impBatteryId)
              } else {
                this.$message.error('图片上传失败：' + res.message)
              }
            })
          } else {
            this.$message.error(res.message)
          }
        } else if (info.file.status === 'removed') {
          let update = {}
          update.id = record.id
          update.attachPicture = JSON.stringify(info.fileList)
          updateMIAttachVersionCur(update).then(res => {
            if (res.success) {
              this.$message.success(`${info.file.name} 图片删除成功`)
              this.getAndRenderDataByLibraryId(this.libraryData.libraryId, this.impBatteryId)
            } else {
              this.$message.error('图片删除失败：' + res.message)
            }
          })
        } else if (info.file.status === 'error') {
          this.$message.error(`${info.file.name} 图片上传失败`);
        }
      },
      updateData(event, record, column) {
        //修改时禁止输入
        this.disableWhenUpdating()
        let param = {}
        param[column] = event.target.value
        param['id'] = record.id
        this.isLoading = true
        updateMIAttachVersionCur(param).then((res) => {
          this.$nextTick(() => {
            if (res.success) {
              this.$message.success('保存成功')
              this.getAndRenderDataByLibraryId(this.libraryData.libraryId, this.impBatteryId)
              setTimeout(() => {
                this.isLoading = false
              },500)
            } else {
              this.$message.error(res.message)
            }
            this.enableWhenUpdated()
          })
        })
      },
      updateLibData(event, record, column) {
        //修改时禁止输入
        this.disableWhenUpdating()
        let param = {}
        param[column] = event.target.value
        param['id'] = record.id
        this.isLoading = true
        updateMIStandardLibCur(param).then((res) => {
          this.$nextTick(() => {
            if (res.success) {
              this.$message.success('保存成功')
              this.getAndRenderLibData(this.libraryData.libraryId, this.impBatteryId)
              setTimeout(() => {
                this.isLoading = false
              },500)
            } else {
              this.$message.error(res.message)
            }
            this.enableWhenUpdated()
          })
        })
      },
      getAndRenderDataByLibraryId(libraryId, impBatteryId) {
        getMIAttachVersionCurList({ libraryId: libraryId, status: 2 }, impBatteryId).then(res => {
            const finallyData = res.data
            this.resultData = finallyData.curData
            this.renderAttachVersionData(finallyData.compareMap.diffList, finallyData.compareMap.sameList)
            for (let i = 0; i < this.resultData.length; i++) {
              if (!this.resultData[i].attachPicture) {
                this.resultData[i].attachPicture = []
              } else {
                this.resultData[i].attachPicture = JSON.parse(this.resultData[i].attachPicture)
              }
              var curItem = finallyData.curData[i]
              var oriItem = finallyData.impData[i]
              this.resultData[i].oriDescription = curItem.description !== oriItem.description ? oriItem.description : undefined
              this.resultData[i].oriRemark = curItem.remark !== oriItem.remark ? oriItem.remark : undefined
            }
          this.loadingPicDesc(this.resultData,libraryId,impBatteryId)
        })
      },
      renderAttachVersionData(diff, same) {
        $(document).ready(function () {
          let $row = $('.attachVerTabClass #develop').find('table').eq(2).find("tbody tr")
          let rowSize = $row.length
          for (let i = 0; i < rowSize; i++) {
            let diffCol = diff[i] // 第i行中与原始列值不同的列名有哪些
            let sameCol = same[i]
            let $col = $row.eq(i).find('td')
            let $colTd = $row.eq(i).find('td')
            // console.log('$colTd',$colTd)
            // diffCol.forEach(function (item, i) {
            //   if (item === 'description') {
            //     $col.eq(0).find('textarea').css("background-color", "yellow")
            //   } else if (item === 'attachPicture') {
            //     $colTd.eq(1).css("background-color", "yellow")
            //   } else if (item === 'remark') {
            //     $col.eq(2).find('textarea').css("background-color", "yellow")
            //   }
            // })
            // sameCol.forEach(function (item, i) {
            //   if (item === 'description') {
            //     $col.eq(0).find('textarea').css("background-color", "white")
            //   } else if (item === 'attachPicture') {
            //     $colTd.eq(1).css("background-color", "white")
            //   } else if (item === 'remark') {
            //     $col.eq(2).find('textarea').css("background-color", "white")
            //   }
            // })

            diffCol.forEach(function (item, i) {
              if (item === 'description') {
                $col.eq(0).addClass("mark")
              } else if (item === 'attachPicture') {
                $colTd.eq(1).addClass("mark")
              } else if (item === 'remark') {
                $col.eq(2).addClass("mark")
              }
            })
            sameCol.forEach(function (item, i) {
              if (item === 'description') {
                $col.eq(0).removeClass("mark")
              } else if (item === 'attachPicture') {
                $colTd.eq(1).removeClass("mark")
              } else if (item === 'remark') {
                $col.eq(2).removeClass("mark")
              }
            })
          }
        })
      },
      loadingPicDesc(res,libraryId,impBatteryId) {
        let canBeUpdate = this.canUpdate
        $(document).ready(function() {
          // 加载事件或者元素
          let $row = $("#attachPictureId > span > div.ant-upload-list") // 获取每行第二列即附图列
          let rowSize = $row.length
          $row.each(function(index, element) {
            $(element).attr("id","rowItem" + index)
          })
          for (let i = 0; i < rowSize; i++) { // 每行
            let $imgDivs = $("#rowItem" + i).find("div.ant-upload-list-picture-card-container > span > div") // 获取每行第二列即附图列的所有img
            // console.log('获取每行第二列即附图列的所有img:',$imgDivs)
            $imgDivs.each(function(index, element) {
              if ($(element).find("#inputItem").length === 0) {
                let div = $(element).append("<div id='inputItem' style='margin-top: 15px'></div>")
                $(div).find("#inputItem").attr("id","inputItem")
              }
            })
            let $inputItem = $imgDivs.find("#inputItem") // 获取每行图片下面的所有input的上层div
            $inputItem.each(function(index, element) {
              if ($(element).find("input").length === 0) {
                let $input = $(element).append("<input/>").find("input").attr("id","input" + index)
                if(canBeUpdate == false){
                  $input.attr('disabled',true)
                }
                $input.val(res[i].attachPicture[index].picDesc)
                $input.change(function () {
                  res[i].attachPicture[index].picDesc = $input.val()
                  //修改时禁止输入
                  $("input").attr("disabled",true)
                  $("textarea").attr("disabled",true)
                  let param = {}
                  let lastItem = res[i].attachPicture[res[i].attachPicture.length - 1];
                  if (lastItem.response) { // 因为新增图片后去触发另一个input的change事件时，res是旧的，所以需要手动修改一下参数
                    let targetImgs = [...res[i].attachPicture].slice(-1);
                    let finallyImgs = [...res[i].attachPicture].slice(0,-1)
                    finallyImgs.push({
                      uid: targetImgs[0].uid,
                      name: targetImgs[0].name,
                      picDesc: targetImgs[0].picDesc,
                      status: targetImgs[0].status,
                      url: process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id=' + targetImgs[0].response.data.id,
                    })
                    res[i].attachPicture = finallyImgs
                    param['attachPicture'] = JSON.stringify(res[i].attachPicture)
                    param['id'] = res[i].id
                  } else {
                    // console.log('$(element)',$inputItem)
                    res[i].attachPicture.forEach(function (item, i) {
                      // console.log('$(element).find("#input" + ' + i + ').val()',$inputItem.find("#input" + i).val())
                      item.picDesc = $inputItem.find("#input" + i).val()
                    })
                    param['attachPicture'] = JSON.stringify(res[i].attachPicture)
                    param['id'] = res[i].id
                  }
                  updateMIAttachVersionCur(param).then((res) => {
                      if (res.success) {
                        // 根据是否更改渲染附图列背景颜色
                        getMIAttachVersionCurList({ libraryId: libraryId, status: 2 }, impBatteryId).then(res => {
                          let diff = res.data.compareMap.diffList
                          let same = res.data.compareMap.sameList
                          $(document).ready(function () {
                            let $row = $('.attachVerTabClass #develop').find('table').eq(2).find("tbody tr")
                            let rowSize = $row.length
                            for (let i = 0; i < rowSize; i++) {
                              let diffCol = diff[i] // 第i行中与原始列值不同的列名有哪些
                              let sameCol = same[i]
                              let $col = $row.eq(i).find('td > div')
                              let $colTd = $row.eq(i).find('td')
                              // diffCol.forEach(function (item, i) {
                              //   if (item === 'description') {
                              //     $col.eq(0).find('textarea').css("background-color", "yellow")
                              //   } else if (item === 'attachPicture') {
                              //     $colTd.eq(1).css("background-color", "yellow")
                              //   } else if (item === 'remark') {
                              //     $col.eq(2).find('textarea').css("background-color", "yellow")
                              //   }
                              // })

                              // sameCol.forEach(function (item, i) {
                              //   if (item === 'description') {
                              //     $col.eq(0).find('textarea').css("background-color", "white")
                              //   } else if (item === 'attachPicture') {
                              //     $colTd.eq(1).css("background-color", "white")
                              //   } else if (item === 'remark') {
                              //     $col.eq(2).find('textarea').css("background-color", "white")
                              //   }
                              // })
                              diffCol.forEach(function (item, i) {
                                if (item === 'description') {
                                  $col.eq(0).addClass("mark")
                                } else if (item === 'attachPicture') {
                                  $colTd.eq(1).addClass("mark")
                                } else if (item === 'remark') {
                                  $col.eq(2).addClass("mark")
                                }
                              })
                              sameCol.forEach(function (item, i) {
                                if (item === 'description') {
                                  $col.eq(0).removeClass("mark")
                                } else if (item === 'attachPicture') {
                                  $colTd.eq(1).removeClass("mark")
                                } else if (item === 'remark') {
                                  $col.eq(2).removeClass("mark")
                                }
                              })
                            }
                          })
                        })
                        message.success('保存成功')
                      } else {
                        message.error(res.message)
                      }
                    $("input").attr("disabled",false)
                    $("textarea").attr("disabled",false)

                  })
                })
              }
            })
          }
        });
      },
      disableWhenUpdating() {
        $("input").attr("disabled",true)
        $("textarea").attr("disabled",true)
      },
      enableWhenUpdated() {
        $("input").attr("disabled",false)
        $("textarea").attr("disabled",false)
      },
      miExportTable() {
        miOutputExportExcel({ impBatteryId: this.impBatteryId, libraryId: this.libraryData.id }).then((res) => {
          const fileName = 'MI设计导出表.xlsx';
          if (!res) return
          const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' }) // 构造一个blob对象来处理数据，并设置文件类型
          if (window.navigator.msSaveOrOpenBlob) { //兼容IE10
            navigator.msSaveBlob(blob, fileName)
          } else {
            const href = URL.createObjectURL(blob) //创建新的URL表示指定的blob对象
            const a = document.createElement('a') //创建a标签
            a.style.display = 'none'
            a.href = href // 指定下载链接
            a.download = fileName //指定下载文件名
            a.click() //触发下载
            URL.revokeObjectURL(a.href) //释放URL对象
          }
        })
      },
      miReImport() {
        this.$refs.miLibDataDialog.miChooseModelImport(this.impBatteryId, this.libraryId)
      },
    }
  }
</script>
<style lang="less" scoped>
/deep/ .ant-table.ant-table-bordered .ant-table-title {
  padding-right: 0px;
  padding-left: 0px;
  border: 1px solid #e8e8e8;
  border-bottom: 0;
}
/deep/ .ant-table.ant-table-bordered .ant-table-footer {
  border: 1px solid #e8e8e8;
  border-top: 0;
}
/deep/ .exportButtonClass {
  font-family: SourceHanSansSC;
  font-weight: 400;
  font-size: 15px;
  color: rgba(0,101,255,0.67);
}
/deep/ .ant-table-bordered .ant-table-footer > table {
  border-collapse: collapse;
  border-spacing: 0;
  margin-top: -2px;
}
/deep/.ant-table-thead > tr > th {
  background: white;
  font-weight: bold;
  padding: 5px 5px;
  border: 1px solid black;
}
/deep/.ant-table-bordered .ant-table-tbody > tr > td {
  border: 1px solid black;
}
/deep/.ant-table-bordered .ant-table-body > table {
  border-collapse: collapse;
  border-spacing: 0;
}
/deep/.ant-table-bordered .ant-table-header > table {
  border-collapse: collapse;
  border-spacing: 0;
}
/deep/.ant-table-thead > tr > th {
  border-bottom: 0;
  border-top: 0;
}
/deep/.ant-table-bordered .ant-table-title > table {
  border-collapse: collapse;
  border-spacing: 0;
  margin-bottom: 0.4px;
}
/deep/.ant-table-bordered .ant-table-title > table > tr > td {
  border: 1px solid black;
}
/deep/.ant-table-bordered .ant-table-footer > table {
  border-collapse: collapse;
  border-spacing: 0;
}
/deep/.ant-table-bordered .ant-table-footer > table > tr > td {
  border: 1px solid black;
}
/deep/.ant-upload-list-picture-card-container {
  width: 280px;
  height: 150px;
}
/deep/.ant-upload-list-picture-card .ant-upload-list-item {
  width: 280px;
  height: 120px;
}
/deep/.avatar-uploader > .ant-upload {
  width: 280px;
  height: 120px;
}
#iconPlusTd {
  border-top: #e8e8e8 solid 0px;
}
#develop {
  font-size: 12px;
  margin: 0px 30px 20px 30px;
  color: #000;
}
/* you can make up upload button and sample style by using stylesheets */
/deep/.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

/deep/.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}

textarea.ant-input {
  max-width: 100%;
  height: auto;
  min-height: 32px;
  line-height: 1.5;
  vertical-align: bottom;
  -webkit-transition: all 0.3s, height 0s;
  transition: all 0.3s, height 0s;
  border: none;
}

.deleteIcon {
  display: inline-block;
  margin-bottom: 35px;
}
/deep/.ant-breadcrumb a, .ant-breadcrumb span {
  color: black;
  font-weight: bold;
}

/deep/.ant-breadcrumb > span:last-child {
  color: black;
  font-weight: bold;
}

/deep/.ant-breadcrumb-separator {
  color: black;
  font-weight: bold;
}
/deep/.ant-table-bordered.ant-table-empty .ant-table-placeholder {
  border: 1px solid black;
}
/deep/.ant-form-item {
    margin-bottom: 0px;
  }
  .renderTr td{
    border: #e8e8e8 solid 1px;
    text-align: center;
  }
  /deep/.ant-table-title {
    position: relative;
    padding: 0px;
    border-radius: 2px 2px 0 0;
  }
  /deep/.ant-table-footer {
    position: relative;
    padding: 0px;
    border-radius: 2px 2px 0 0;
    background-color: white;
  }
  /deep/.ant-table-footer tr td {
    border-right: #e8e8e8 solid 1px;
    border-top: #e8e8e8 solid 1px;
    text-align: center;
  }
  input {
    width: 100%;
    height: 25px;
    margin: 0;
    border: 0;
    outline: none;
    text-align: center;
  }
.tab-title{
  padding: 0 10px;
}
div.tab-head div.active{
  font-size: 24px;
  font-weight: 700;
  color: rgba(0,73,176,1);
  margin-bottom: -4px;
  cursor: text;
}

.block{
  width: 50px;
height: 20px;
float: left;
  border: 1px solid #000;
}

</style>
