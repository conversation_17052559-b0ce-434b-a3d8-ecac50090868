/*
@license

dhtmlxGantt v.6.3.3 Standard

This version of dhtmlxGantt is distributed under GPL 2.0 license and can be legally used in GPL projects.

To use dhtmlxGantt in non-GPL projects (and get Pro version of the product), please obtain Commercial/Enterprise or Ultimate license on our site https://dhtmlx.com/docs/products/dhtmlxGantt/#licensing or contact <NAME_EMAIL>

(c) XB Software Ltd.

*/
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("locale/locale_nl",[],t):"object"==typeof exports?exports["locale/locale_nl"]=t():e["locale/locale_nl"]=t()}(window,function(){return function(e){var t={};function n(o){if(t[o])return t[o].exports;var r=t[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(o,r,function(t){return e[t]}.bind(null,r));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="/codebase/",n(n.s=191)}({191:function(e,t){gantt.locale={date:{month_full:["Januari","Februari","Maart","April","Mei","Juni","Juli","Augustus","September","Oktober","November","December"],month_short:["Jan","Feb","mrt","Apr","Mei","Jun","Jul","Aug","Sep","Okt","Nov","Dec"],day_full:["Zondag","Maandag","Dinsdag","Woensdag","Donderdag","Vrijdag","Zaterdag"],day_short:["Zo","Ma","Di","Wo","Do","Vr","Za"]},labels:{new_task:"Nieuwe taak",dhx_cal_today_button:"Vandaag",day_tab:"Dag",week_tab:"Week",month_tab:"Maand",new_event:"Nieuw item",icon_save:"Opslaan",icon_cancel:"Annuleren",icon_details:"Details",icon_edit:"Bewerken",icon_delete:"Verwijderen",confirm_closing:"",confirm_deleting:"Item zal permanent worden verwijderd, doorgaan?",section_description:"Beschrijving",section_time:"Tijd periode",section_type:"Type",column_wbs:"WBS",column_text:"Taak omschrijving",column_start_date:"Startdatum",column_duration:"Duur",column_add:"",link:"Koppeling",confirm_link_deleting:"zal worden verwijderd",link_start:" (start)",link_end:" (eind)",type_task:"Task",type_project:"Project",type_milestone:"Milestone",minutes:"minuten",hours:"uren",days:"dagen",weeks:"weken",months:"maanden",years:"jaren",message_ok:"OK",message_cancel:"Annuleren",section_constraint:"Constraint",constraint_type:"Constraint type",constraint_date:"Constraint date",asap:"As Soon As Possible",alap:"As Late As Possible",snet:"Start No Earlier Than",snlt:"Start No Later Than",fnet:"Finish No Earlier Than",fnlt:"Finish No Later Than",mso:"Must Start On",mfo:"Must Finish On",resources_filter_placeholder:"type to filter",resources_filter_label:"hide empty"}}}})});
//# sourceMappingURL=locale_nl.js.map