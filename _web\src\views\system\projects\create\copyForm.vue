<template>
  <a-modal title="可选复制列表" :width="240" :visible="visible" :confirmLoading="confirmLoading" @ok="handleSubmit" @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item label="" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-select  placeholder="请选择" v-decorator="['id', {rules: [{required: true, message: '请选择复制选项！'}]}]">
            <a-select-option v-for="(item,i) in copyList" :key="i" :value="item.id">
              {{ stateMap[item.stage] }}
            </a-select-option>
          </a-select >
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import {getProductParamsCopy} from "@/api/modular/system/productParamManage"
  export default {
    data() {
      return {
        stateMap:{
          2:'A样',
          3:'B样',
          4:'C样',
        },
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 24
          }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this),
        copyList:[],
      }
    },
    methods: {
      // 初始化方法
      view(record) {
        this.callProductParamsCopy(record)
        this.visible = true
      },
      callProductParamsCopy(record) {
        let that = this
        that.confirmLoading = true
        getProductParamsCopy({
            issueId:record.issueId,
            stage:record.stage
        }).then((res) => {
            if (res.success) {
                that.copyList = res.data
            } else {
                that.$message.error(res.message, 1);
            }
            that.confirmLoading = false
        })
        .catch((err) => {
          that.confirmLoading = false
          that.$message.error('错误提示：' + err.message, 1)
        });
      },
      handleSubmit() {
        const {form: {validateFields}} = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            let copyItem = this.copyList.find(item=>item.id == values.id)
            this.$emit('copy', copyItem.params)
            this.handleCancel()
            /* sysAppAdd(values).then((res) => {
              this.confirmLoading = false
              if (res.success) {
                this.$message.success('新增成功')
                this.handleCancel()
                this.$emit('ok', values)
              } else {
                this.$message.error('新增失败：' + res.message)
              }
            }).finally((res) => {
              this.confirmLoading = false
            }) */
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel() {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
