<template>
  <div style="background:#fff;overflow: hidden;">
    <a-breadcrumb separator=">" style="padding-left: 15px">
      <a-breadcrumb-item>
        <router-link  :to="{ path: '/batterydesign', query: { type: breadcrumb.type } }">
						    <a-icon type="rollback" class="rollback-icon" />
                {{ breadcrumb.type === 'product' ? '项目产品' : '技术平台' }}设计
					    </router-link>
      </a-breadcrumb-item>
      <a-breadcrumb-item>
        <router-link  :to="{path:'/design_battery_index',query:breadcrumb}">
          {{ breadcrumb.batteryName+'('+ (breadcrumb.designType === 'design' ? '研发' : '冻结') +')' }}
        </router-link>
      </a-breadcrumb-item>
      <!-- <a-breadcrumb-item>
        {{design.productName}} 设计开发
      </a-breadcrumb-item> -->
      <a-breadcrumb-item>
        <a>{{breadcrumb.code}} 电芯BOM设计</a>
        <a-menu slot="overlay">

          <a-menu-item>
            <a target="_blank" @click="gotoDesign">
              方案设计
            </a>
          </a-menu-item>
          <a-menu-item>
            <a target="_blank" @click="gotoMi">
              MI设计
            </a>
          </a-menu-item>
        </a-menu>
      </a-breadcrumb-item>

    </a-breadcrumb>

    <div class="tab-title">
      <div class="tab-head">

        <div class="active">电芯BOM设计</div>
        <div class="sub-title" style="float: right">
          <div >
            <a-spin :spinning="spinning" >
              <span  @click="exportDataMethod()" style="cursor:pointer"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 jZPaJQ svg-icon-path-icon fill" viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path d="M5 8C5 6.89543 5.89543 6 7 6H19L24 12H41C42.1046 12 43 12.8954 43 14V40C43 41.1046 42.1046 42 41 42H7C5.89543 42 5 41.1046 5 40V8Z" fill="none" stroke="rgb(84, 152, 255)" stroke-width="4" stroke-linejoin="round"></path><path d="M30 28L23.9933 34L18 28.0134" stroke="rgb(84, 152, 255)" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path><path d="M24 20V34" stroke="rgb(84, 152, 255)" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path></g></svg></span>
              <a class="tip"  @click="exportDataMethod()" style="cursor:pointer">
                电芯BOM设计导出
              </a>
            </a-spin>
          </div>


        </div>
      </div>
    </div>
    <div class="sub-title">
      <div >
        <span v-if="updateTime != null" style="float:right;">更新时间：{{updateTime}}</span>
        <div>(*为必填项，灰色为自动生成内容)</div>
      </div>


    </div>


    <a-form :form="form" layout="inline" style="padding-left:45px;padding-right: 12px;padding-bottom: 20px">

      <a-row :gutter="24">

        <a-col :span="6">
          <a-form-item :label="fileCode">
            <a-input :disabled="true"
                     @blur="update($event,'fileCode')"
                     @keyup.enter="update($event,'fileCode')"
                     placeholder="质量文件归属审核人填写"
                     v-decorator="['fileCode']"
            />
          </a-form-item>
        </a-col>




        <a-col :span="6">
          <a-form-item :label="version" >
            <a-input @blur="update($event,'version')" :disabled="true"
                     placeholder="自动生成"
                     v-decorator="['version']"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item :label="fileName">
            <a-input @blur="update($event,'fileName')" :disabled="true"
                     placeholder="自动生成"
                     v-decorator="['fileName']" style="width:400px "
            />
          </a-form-item>
        </a-col>


      </a-row>
      <a-row :gutter="24">

        <a-col :span="6">
          <a-form-item :label="productName">
            <a-input @blur="update($event,'productName')" :disabled="true" placeholder="自动生成" disabled
                     v-decorator="['productName']"
            />
          </a-form-item>
        </a-col>

        <a-col :span="6" id="productTypeCol">
          <a-form-item :label="productType">
            <a-input @blur="update($event,'productType')" :disabled="!canUpdate"
                     placeholder="例：INP4695E"
                     v-decorator="['productType']"
            />
          </a-form-item>
        </a-col>

          <a-col :span="6">


          <a-form-item :label="factory">
            <a-tree-select placeholder="选择工厂" style="width: 400px" @change="updateSelectDataFa" :disabled="!canUpdate"
                        :multiple="true" :tree-data="options" v-model="lindIds" tree-checkable
                        :disable-branch-nodes="true"/>

          </a-form-item>
        </a-col>


      </a-row>
      <a-row :gutter="24">

        <a-col :span="6">
          <a-form-item :label="projectStatus">

            <a-select style="width: 100%" v-decorator="['projectStatus']"
                      :disabled="!canUpdate"
                      @change="updateSelectData($event,'projectStatus')">
              <a-select-option value="K0" >
                K0
              </a-select-option>

              <a-select-option value="M1" >
                M1
              </a-select-option>

              <a-select-option value="M2" >
                M2
              </a-select-option>

              <a-select-option value="M3" >
                M3
              </a-select-option>

              <a-select-option value="M4" >
                M4
              </a-select-option>

              <a-select-option value="M5" >
                M5
              </a-select-option>

              <a-select-option value="M6" >
                M6
              </a-select-option>

            </a-select>


          </a-form-item>
        </a-col>

        <a-col :span="6">
          <a-form-item :label="sampleStatus">

            <a-select v-decorator="['sampleStatus']"
                      :disabled="!canUpdate"
                      @change="updateSelectData($event,'sampleStatus')">
              <a-select-option value="A样" >
                A样
              </a-select-option>

              <a-select-option value="B样" >
                B样
              </a-select-option>

              <a-select-option value="C样" >
                C样
              </a-select-option>

              <a-select-option value="D样" >
                D样
              </a-select-option>


            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="6">
          <a-form-item :label="partName">
            <a-input @change="update($event,'partName')" disabled
                     v-decorator="['partName']"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item :label="partCode">
            <a-input @change="update($event,'partCode')" disabled
                     v-decorator="['partCode']"
            />
          </a-form-item>
        </a-col>





      </a-row>
      <a-row :gutter="24">

        <a-col :span="12">
          <a-form-item :label="transport">

            <a-select :defaultValue="bomTransport" mode="multiple" :disabled="!canUpdate" @change="(value) => {update(value,'transport')}">
              <a-select-option value='1'>陆运</a-select-option>
              <a-select-option value='2'>海运</a-select-option>
              <a-select-option value='3'>空运</a-select-option>
              <a-select-option value='4'>其他</a-select-option>
            </a-select>


          </a-form-item>
        </a-col>

        <a-col :span="6">
          <a-form-item :label="batteryNum">
            <a-input @change="update($event,'batteryNum')" disabled
                     v-decorator="['batteryNum']"
            />
          </a-form-item>
        </a-col>

        <a-col :span="6">
          <a-form-item :label="unit">
            <a-input @change="update($event,'unit')" disabled
                     v-decorator="['unit']"
            />
          </a-form-item>
        </a-col>


      </a-row>


    </a-form>




    <div v-if="tipShow" :style="position" id="sgtip" class="moveTip">{{tipContent}}</div>
    <a-spin :spinning="dloading" style="margin-bottom:12px">

    </a-spin>
    <a-card v-if="bom.bomStatus == 3" style="margin-bottom:3px">
      <p class="error">{{errorTips}}</p>
      <div v-for="(item, n) in add_fails" :key="n">
        <p class="error" v-for="(_item,i) in item.IT_DATA" :key="i">
          新增失败: 主物料-{{_item.MATNR}} 子物料-{{_item.IDNRK}} 用量-{{_item.MENGE}} 单位-{{_item.MEINS}} 损耗率-{{_item.AUSCH}}
        </p>
      </div>
      <div v-for="(item, n) in edit_fails" :key="n">
        <p class="error" v-for="(_item,i) in item.IT_DATA" :key="i">
          {{fldelte[_item.FLDELETE]}}失败: 主物料-{{item.IV_MATNR}} 子物料-{{_item.IDNRK}} 用量-{{_item.MENGE}} 损耗率-{{_item.AUSCH}}
        </p>
      </div>
    </a-card>
    <div class="main">
      <div class="left_main">
        <template>
          <a-button v-if="!openStatus && (canUpdate || exportBomCanBeUpdate)" type="primary" @click="showDrawer" style="margin-bottom:8px;margin-right:8px;margin-left: 20px;" icon="menu-fold">
            选择物料
          </a-button>
          <a-button v-else-if="openStatus && (canUpdate || exportBomCanBeUpdate)" type="primary" @click="showDrawer" style="margin-bottom:8px;margin-right:8px;margin-left: 20px;" icon="menu-unfold">
            取消选择
          </a-button>
          <a-button type="primary" v-if="canUpdate" @click="showAdd">
            新增临时物料
          </a-button>
        </template>
        <a-spin :spinning="vloading">

          <dragTreeTable
            style="
            margin-left: 20px;
            border-radius: 5px;
            border: 1px solid #c0c4cc; overflow:hidden"
            :data='treeData'
            :onDrag="onTreeDataChange"
            :beforeDragOver="beforeDragOver"
            :isdraggable="isdraggable" ref="dratree1" resize border>

            <template #action="{row}">
              <div @click.stop v-if="canUpdate || exportBomCanBeUpdate">
                <a v-if="!disabled && optRow && optRow.id == row.id" @click="opt(row)">
                  <a-icon type="check-circle" /></a>
                <a class="uncheck" v-else-if="!disabled" @click="opt(row)">
                <a-icon type="minus-circle" /></a>
                <a v-else-if="!disabled" style="display:inline-block;width:12px;margin-right:16px;"></a>
                <a-divider type="vertical" />
                <a-popconfirm v-if="!disabled" placement="topRight" title="确认删除？" @confirm="() => doDel(row)">
                  <a>删除</a>
                </a-popconfirm>
              </div>
            </template>


            <template #partUse="{row}">
              <div @click.stop v-if="row.parent_id" class="stopdiv">
                <input :disabled="!(canUpdate  || exportBomCanBeUpdate)" class="input" :id="row.id" type="number" style='width:80px'  @change="(e) => {
														const { value } = e.target
														row.validate = validatePrimeNumber(value)
														if(!row.validate){
															return
														}
														if(!equal(row.partUse,parseFloat(value).toFixed(3))){
															row.partUse = parseFloat(value).toFixed(3)
															//getSapPartUse()
															callSysBomSaveForPartUse()
														}
													}" min="0.000" step="0.001" precision="3" :value="row.partUse" />
                <span v-if="!row.validate" style="color:red;display:block;padding-top:6px">必须大于0.000</span>
              </div>
              <div v-else class="stopdiv" @click.stop>
                <input :id="row.id" type="hidden" value="1000">
              </div>
            </template>

            <template #partLoss="{row}">
              <div @click.stop class="stopdiv">
                <input v-if="row.parent_id" style="width:62px" type="number" :disabled="disabled" :value="row.partLoss" @change="(e) => {
														const { value } = e.target
														if(!value){
															$message.error('请输入数值')
															row.partLoss = 0.00
															row.partLoss = parseFloat(0.00).toFixed(2)
															return
														}
														if(!equal(row.partLoss,parseFloat(value).toFixed(2))){
															let val = value
															if(val > 100){
																val = 100
															}
															row.partLoss = val
															row.partLoss = parseFloat(val).toFixed(2)
															callSysBomSave(0)
														}
													}" min="0.00" step="0.01" precision="2" max="100.00" /><span v-if="row.parent_id">%</span>
              </div>
            </template>
            <template #sapNumber="{row}">
              <div class="stopdiv" @click.stop>
                <input class="readonly text-align" :value="row.sapNumber" readonly="readonly" /></div>
            </template>
            <template #partDescription="{row}">
              <div class="stopdiv" @click.stop>
                <input class="readonly" :value="row.partDescription" readonly="readonly" /></div>
            </template>

            <template #sapPartUse="{row}">
              <div class="stopdiv" @click.stop>
                <input class="readonly text-align" :value="row.sapPartUse" readonly="readonly" /></div>
            </template>
            <template #baseUse="{row}" >
              <div class="stopdiv" @click.stop>
                <input v-if="row.parent_id" class="readonly text-align" :value="row.baseUse" readonly="readonly" />
              </div>
            </template>
            <template #count="{row}">
              <div v-if="row.substitute && row.substitute.length > 0" @click.stop class="stopdiv">
                <a @click="$refs.bomreplaceForm.add(row,partGroupArr)">{{row.substitute.length}}</a>
              </div>
              <div v-else @click.stop class="stopdiv">
                <a @click="$refs.bomreplaceForm.add(row,partGroupArr)">0</a>
              </div>
            </template>

            <template #version="{row}">
              <div @click.stop class="stopdiv">
                <template v-if="row.version">
                  <div  v-for="(item,i) in JSON.parse(row.version)" :key="i">
                    {{i}}-{{item}}
                  </div>
                </template>
              </div>
            </template>



            <template #partUnit="{row}">
              <div @click.stop class="stopdiv">

                {{row.partUnit}}


              </div>
              <!-- <div class='stopdiv' v-else @click.stop></div> -->
            </template>

          </dragTreeTable>
          <a-form :form="form" layout="inline" labelAlign="left" style="margin-top:12px;padding:0 20px 0 20px">
            <a-row :gutter="24">

              <a-col :span="6">

              </a-col>


              <a-col :span="6">
                <a-form-item label="编制">
                  <a-input @change="update($event,'organizationMan')" :disabled="!canUpdate"
                           v-decorator="['organizationMan']"
                  />
                </a-form-item>
              </a-col>

              <a-col :span="6">
                <a-form-item label="审核">
                  <a-input @change="update($event,'checkMan')" :disabled="!canUpdate"
                           v-decorator="['checkMan']"
                  />
                </a-form-item>
              </a-col>

              <a-col :span="6">
                <a-form-item label="批准">
                  <a-input @change="update($event,'approveMan')" :disabled="!canUpdate"
                           v-decorator="['approveMan']"
                  />
                </a-form-item>
              </a-col>


            </a-row>

          </a-form>


        </a-spin>
      </div>
      <div
        :class="{ right_main_show: !openStatus}"
        :style="{display:canUpdate || exportBomCanBeUpdate?'unset':'none'}"
        class="right_main"
      >
        <div>
          <div slot="content" class="table-page-search-wrapper">
            <a-form layout="inline">
              <a-row :gutter="48">
                <a-col :md="24" :sm="24">
                  <a-form-item style="height:auto">
                    <!-- <v-selectpage v-model="queryParam.partClass" :data="nodes" key-field="nodeId" show-field="nodeName" >
                      </v-selectpage> -->
                    <treeselect placeholder="选择物料分类" :value-consists-of="valueConsistsOf" v-model="queryParam.partClass" :multiple="true" :options="nodes" :normalizer="normalizer" />
                  </a-form-item>
                </a-col>
                <a-col :md="8" :sm="24">
                  <a-form-item>
                    <a-input @keyup.enter.native="$refs.table.refresh(true)" :disabled="!(canUpdate || exportBomCanBeUpdate)" v-model="queryParam.sapNumber" @pressEnter="$refs.table.refresh(true)" allow-clear placeholder="请输入物料代码"/>
                  </a-form-item>
                </a-col>
                <a-col :md="8" :sm="24">
                  <a-form-item>
                    <a-input @keyup.enter.native="$refs.table.refresh(true)" :disabled="!(canUpdate || exportBomCanBeUpdate)" v-model="queryParam.partDescription" @pressEnter="$refs.table.refresh(true)" allow-clear placeholder="请输入物料规格"/>
                  </a-form-item>
                </a-col>
                <a-col :md="8" :sm="24">
                  <a-form-item>
										<span class="table-page-search-submitButtons">
											<a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
											<a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
										</span>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </div>
        </div>
        <div>
          <a-spin :spinning="loading">
            <s-table
              ref="table"
              :rowKey="(record) => record.partNumber"
              :columns="columns"
              :data="loadData"
            >
							<span slot="partClass" slot-scope="text">
								{{nodeMap.get(text)}}
							</span>
              <span slot="action" slot-scope="text, record">
								<a @click="toBom(record)">选择</a>
							</span>
            </s-table>
          </a-spin>
        </div>
      </div>
    </div>
    <material-add-form ref="materialAddForm" @ok="toBomFromAdd"></material-add-form>
    <a-drawer :bodyStyle="{ height: '100%' }" placement="right" :closable="false" width="80%" :visible="visible3" @close="onClose3">
      <iframe :src="pdfUrl+'#view=FitH,top&'"  width="100%" height="100%"></iframe>
    </a-drawer>
  </div>
</template>

<script>
  import { ALL_APPS_MENU } from '@/store/mutation-types'
  import Vue from 'vue'
  import {
    getPartPage,
    getPartRandom
  } from "@/api/modular/system/partManage"

  import {

    getBom,
    updateBom,
    addBom,
    exportBom,
    getBatteryDesign,
    canBeUpdate
  } from '@/api/modular/system/batterydesignManage'

  import {
    getwerklines
  } from "@/api/modular/system/bomManage"
  import {
    getAllNode
  } from "@/api/modular/system/nodeManage"
  import Treeselect from '@riophae/vue-treeselect'
  import '@riophae/vue-treeselect/dist/vue-treeselect.css'
  import dragTreeTable from "drag-tree-table";
  import {
    STable
  } from '@/components'

  import moment from 'moment';
  import {mapActions, mapGetters} from "vuex";
  import materialAddForm from "./materialAddForm";


  export default {
    components: {
      dragTreeTable,
      STable,
      Treeselect,
      materialAddForm
    },
    props: {



    },

    mounted() {

      this.batteryId = this.$route.query.batteryId

      canBeUpdate({inBatteryId:this.batteryId,type:'design'}).then(res => {
        this.canUpdate = res.data
/*
        if(!this.canUpdate){
          let inputs = document.getElementsByTagName("input");
          let controlInput = [];

          for (let i = 0; i < inputs.length; i++) {
            if(!inputs[i].disabled){
              controlInput.push(inputs[i])
            }
          }

          for (let i = 0; i < controlInput.length; i++) {
            controlInput[i].disabled = true
          }
        }*/

      }).then(() => {
        canBeUpdate({inBatteryId:this.batteryId,type:'bom'}).then((res) => {
          this.exportBomCanBeUpdate = res.data
        })
      })

    },
    data() {
      return {
        exportBomCanBeUpdate:true,
        breadcrumb:{}, // 面包屑
        canUpdate:false,
        bomTransport: [],
        werklines: [],
        options:[],
        lindIds:[],
        pdfUrl:'',
        design:{},
        structureType1:'',
        position: {
          left: '',
          top: ''
        },
        spinning:false,
        id:'',
        batteryId:null,
        form: this.$form.createForm(this),
        tipContent: '',
        tipShow: false,
        sourceId: null,
        copyModal: false,
        visible3: false,
        optRow: null,
        showview: false,
        valueConsistsOf: 'ALL',
        normalizer(node) {
          return {
            id: node.id,
            label: node.name,
            children: node.lists,
          }
        },
        disabled: false,
        isdraggable: true,
        nodes: [],
        bom: {
          bomStatus: -1
        },
        nodeMap: new Map(),
        //wearks: [],
        openStatus: false,
        visible: false,
        visible1: false,
        loadData: parameter => {
          return getPartPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        queryParam: {},
        loading: false,
        vloading: false,
        cloading: false,
        dloading: false,
        pdfUrl: '',
        columns: [
          {
          title: '操作',
          dataIndex: 'action',
          width: 40,
          scopedSlots: {
            customRender: 'action'
          }
        },
          {
            title: '物料名称',
            dataIndex: 'partClass',
            width: 100,
            scopedSlots: {
              customRender: 'partClass'
            }
          },
          {
            title: '物料代码',
            dataIndex: 'sapNumber',
            width: 80
          },
          {
            title: '物料规格',
            dataIndex: 'partDescription',
          },
          {
            title: '单位',
            dataIndex: 'partUnit',
            width: 50
          }
        ],
        factoryNames:'',
        fileCode:(<span>文件编号<span style='color:red;'>*</span></span>),
        fileName:(<span>文件名称</span>),
        version:(<span>版本</span>),
        page:(<span>页次<span style='color:red;'>*</span></span>),
        productName:(<span>产品名称</span>),
        productType:(<span>产品型号<span style='color:red;'>*</span></span>),
        factory:(<span>适用工厂<span style='color:red;'>*</span></span>),
        projectStatus:(<span>项目阶段<span style='color:red;'>*</span></span>),
        sampleStatus:(<span>样品阶段<span style='color:red;'>*</span></span>),
        partName:(<span>物料名称</span>),
        partCode:(<span>物料代码</span>),
        transport:(<span>运输方式<span style='color:red;'>*</span></span>),
        batteryNum:(<span>电芯数量</span>),
        unit:(<span>单位</span>),
        partUse:(<span>用量<span style='color:red;'>*</span></span>),
        treeData: {
          open: false,
          namess: "",
          lists: [],
          columns: [{
            type: 'selection',
            field: 'id',
            title: '物料名称',
            width: 350,
            align: 'left',
            formatter: (item) => {
              return '<a title="' + item.partName + '" class="aid"' + ' data-id=' + item.id + ' name=' + item.partName + '>' +item.partClass + '</a>'
            }
          },

            {
              type: 'sapNumber',
              field: 'sapNumber',
              title: '物料代码',
              width: 350,
              align: 'center',
            },
            {
              type: 'partDescription',
              field: 'partDescription',
              title: '物料规格',
              width: 800,
              align: 'left'
            },
            {
              type: 'partUnit',
              field: 'partUnit',
              title: '单位',
              width: 110,
              align: 'center'
            },
            {
              type: 'partUse',
              field: 'partUse',
              title: '用量',
              width: 168,
              align: 'center'
            },

            {
              type: 'action',
              width: 120,
              align: 'center',
              title: '操作'
            },
          ]
        },

        partGroupArr: [],
        add_fails: [],
        edit_fails: [],
        errorTips: '',
        fldelte: {
          'M': '修改',
          'A': '新增',
          'X': '删除'
        },
        copySource: [],
        timeId: null,
        updateTime:null,
        isOwn:1
      };
    },

    computed: {
      ...mapGetters(['userInfo'])
    },

    methods: {
      ...mapActions(['MenuChange']),
      showAdd(){
        this.$refs.materialAddForm.add()
      },
      gotoDevelop(record) {
        //this.switchApp()
        this.$router.push({
          path: "/batterydesign"
        });
      },
      gotoDesign(record) {
        //this.switchApp()
        this.$router.push({
          path: "/battery_design_manager",
          query: {
            batteryId: this.batteryId
          }
        });
      },
      update(event,column){
        let param = {}
        if(column == 'transport'){
          param[column] = JSON.stringify(event)
        }else{
          param[column] = event.target.value
        }

        if(column == 'fileCode'){
          if(event.target.value.length > 0){
            let value = event.target.value.substring(event.target.value.length -1)
            //A-Z 对应的 Unicode 编码是 65 - 90
            if(value.charCodeAt(0) < 65 || value.charCodeAt(0) > 90){
              this.form.setFieldsValue(
                {
                  fileCode:null
                }
              )
              this.$message.info("请正确填写编码")
              return
            }else{


              if(this.bom.factory){
                this.factoryNames = ''
                let factorys =  JSON.parse(this.bom.factory)

                for (let i = 0; i < factorys.length; i++) {
                  if(i == factorys.length -1){
                    this.factoryNames += factorys[i].name
                  }else{
                    this.factoryNames += factorys[i].name + ','
                  }
                }
              }


              param['version'] = value

              if(this.form.getFieldValue('partCode') != null){
                let fileName = event.target.value + '-电芯BOM文件-' + this.form.getFieldValue('partCode') + '（适用于'+this.factoryNames+'工厂）'
                param['fileName'] = fileName
                this.form.setFieldsValue(
                  {
                    fileName:fileName
                  }
                )
              }


              this.form.setFieldsValue(
                {
                  version:value,
                }
              )
            }
          }
        }




        param['id'] = this.id
        updateBom(param).then((res) => {
          this.$nextTick(() => {
            if (res.success) {

              getBom({batteryId:this.batteryId}).then(res => {
                this.updateTime = res.data.updateTime
              })


            }else {
              this.$message.error(res.message)
            }
          })
        })


      },

      updateSelectData(value,column){
        let param = {}
        if(column == 'factory'){
          param[column] = JSON.stringify(value)
        }else{
          param[column] = value
        }

        param['id'] = this.id

        updateBom(param).then((res) => {
          this.$nextTick(() => {
            if (res.success) {

              getBom({batteryId:this.batteryId}).then(res => {
                this.bomTransport = res.data.transportList
                this.updateTime = res.data.updateTime
              })


            }else {
              this.$message.error(res.message)
            }
          })
        })
      },

      updateSelectDataFa(value, label, extra){

        this.factoryNames = ""

        let list = []
        for (let i = 0; i < value.length; i++) {
          let child = {}
          child.id = value[i]
          child.name = label[i]
          list.push(child)

          if(i == value.length -1){
            this.factoryNames += label[i]
          }else{
            this.factoryNames += label[i] + ','
          }


        }
        let param = {}
        if(!(this.form.getFieldValue('fileCode') == null || this.form.getFieldValue('partCode') == null)){
          let fileName = this.form.getFieldValue('fileCode') + '-电芯BOM文件-' + this.form.getFieldValue('partCode') + '（适用于'+this.factoryNames+'工厂）'
          param['fileName'] = fileName
          this.form.setFieldsValue(
            {
              fileName:fileName
            }
          )
        }

        param['factory'] = JSON.stringify(list)
        param['id'] = this.id

        updateBom(param).then((res) => {
          this.$nextTick(() => {
            if (res.success) {

              getBom({batteryId:this.batteryId}).then(res => {
                this.bomTransport = res.data.transportList
                this.updateTime = res.data.updateTime
              })


            }else {
              this.$message.error(res.message)
            }
          })
        })
      },


      mouseDown() {
        let that = this
        // 鼠标跟随tip
        document.onmousedown = function(e) {
          var ev = e || event;
          var scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
          that.position.left = ev.clientX + 'px';
          that.position.top = ev.clientY - 50 + scrollTop + 'px';
        }
        /* document.onmouseup = function (e) {
          that.tipShow = false
          that.tipContent = ''
        } */
      },
      /* mouseUp(){
        let that = this
        document.onmouseup = function (e) {
          that.tipShow = false
          that.tipContent = ''
        }
      }, */

      getList() {
        this.copySource = []
        getBomList({
          bomIssueId: this.bom.bomIssueId,
          bomType: this.bom.bomType
        }).then((res) => {
          if (res.success) {
            for (let i = 0; i < res.data.length; i++) {
              if (this.bom.id != res.data[i].id && res.data[i].bomData.length > 2) {
                res.data[i].name = (i + 1) + '、' + (res.data[i].bomNo != null ?res.data[i].bomNo:JSON.parse(res.data[i].bomData)[0].partDescription)

                this.copySource.push(res.data[i]);
              }
            }



          }

          console.log(this.copySource)

        }).finally((res) => {
          this.copyModal = true
        })
      },



      callSysBomSaveForPartUse() {

        this.vloading = true
        this.getSapPartUse()

        this.bom.bomData = JSON.stringify(this.treeData.lists)

        this.callSysBomSave()

      },

      exportDataMethod1() {

        exportBom({batteryId:this.batteryId}).then(res => {

          const fileName = 'BOM导出.xlsx';

          if(!res) return
          const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' }) // 构造一个blob对象来处理数据，并设置文件类型

          if (window.navigator.msSaveOrOpenBlob) { //兼容IE10
            navigator.msSaveBlob(blob, fileName)
          } else {
            const href = URL.createObjectURL(blob) //创建新的URL表示指定的blob对象
            const a = document.createElement('a') //创建a标签
            a.style.display = 'none'
            a.href = href // 指定下载链接
            a.download = fileName //指定下载文件名
            a.click() //触发下载
            URL.revokeObjectURL(a.href) //释放URL对象
          }

        })


      },
      exportDataMethod() {
        this.spinning = true
        exportBom({batteryId:this.batteryId}).then(res => {
          this.spinning = false
          this.visible3 = true
          this.pdfUrl = process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/previewPdf?id=' + res.data

        })


      },


      getByClass(parent, cls) {
        if (parent.getElementsByClassName) {
          return Array.from(parent.getElementsByClassName(cls));
        } else {
          var res = [];
          var reg = new RegExp(' ' + cls + ' ', 'i')
          var ele = parent.getElementsByTagName('*');
          for (var i = 0; i < ele.length; i++) {
            if (reg.test(' ' + ele[i].className + ' ')) {
              res.push(ele[i]);
            }
          }
          return res;
        }
      },
      validatePrimeNumber(number) {
        if (this.equal(number, 0.000)) {
          return false
        }
        return true;
      },
      afterVisibleChange(val) {},
      showDrawer1() {
        this.visible1 = true;
      },
      onClose1() {
        this.visible1 = false;
      },
      onClose3() {
        this.visible3 = false;
      },
      preview() {
        if (this.bom.bomData.length < 3) {
          this.$message.warn("请先搭建bom");
          return false
        }
        this.dloading = true
        this.vloading = true
        pdfUpdate({
          id: this.bom.id
        }).then((res) => {
          if (res.success) {
            this.pdfUrl = process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id=' + res.data
            this.visible1 = true
            this.dloading = false
            this.vloading = false
          }
        })
      },
      toMap(list, _map) {
        let arr = []
        for (const item of list) {
          arr.push(item)
          while (arr.length > 0) {
            let _tempItem = arr.shift()
            _map[_tempItem.id] = {
              partNumber: _tempItem.partNumber,
              parent_id: _tempItem.parent_id
            }
            if (_tempItem.lists) {
              arr.push(..._tempItem.lists)
            }
          }
        }
      },
      getParentIds(_map, id, ids) {
        let $map = null
        if (_map[id]) {
          $map = _map[id]
          while ($map) {
            let _tempItem = $map
            $map = null
            ids.push(_tempItem.partNumber)
            if (_tempItem.parent_id) {
              $map = _map[_tempItem.parent_id]
            }
          }
        }
      },
      moment,
      equal(a, b) {
        const floatEpsilon = Math.pow(2, -23)
        return Math.abs(a - b) <= floatEpsilon * Math.max(Math.abs(a), Math.abs(b));
      },
      showDrawer() {
        this.openStatus = !this.openStatus
      },
      beforeDragOver(from, to, where) {
        clearTimeout(this.timeId)
        this.tipContent = from.partName + '将移动到' + to.partName
        this.tipShow = true
        this.timeId = setTimeout(() => {
          this.tipContent = ''
          this.tipShow = false
        }, 1800);
      },
      onTreeDataChange(list, from, to, where) {
        if (this.bom.bomStatus == 7) {
          this.$message.warn('导入状态不允许拖拽')
          return false
        }
        /*if (!to.sapNumber.startsWith('8') && !to.sapNumber.startsWith('9') && where == 'center') {
          this.$message.warn('非8和9物料号不能为BOM')
          return false;
        }*/
        /*if (!from.parent_id && this.treeData.lists.length > 0) {
          this.$message.warn('顶级BOM已存在')
          return false;
        }*/
        let map = {}
        let ids = []

        this.toMap(this.treeData.lists, map)
        this.getParentIds(map, to.id, ids)
        let index = ids.findIndex(item => item == from.partNumber)
        if (index > -1) {
          this.$message.warn('子物料无法与父级物料相同')
          return false;
        }
        this.$message.info(from.partName + "将移动到" + to.partName)
        this.treeData.lists = list;
        this.callSysBomSave(0)
        setTimeout(() => {
          this.getlevelid()
        }, 1000);
      },
      getlevelid() {
        this.$nextTick(() => {
          let items = this.getByClass(document, 'aid')
          for (const e of items) {
            e.innerText = '[' + (this.$refs.dratree1.GetLevelById(e.getAttribute('data-id')) + 1) + ']' + '-' + e.getAttribute('name')
          }
        })
      },
      hightLow(id) {
        this.$nextTick(() => {
          let items = this.getByClass(document, 'tree-row')
          for (const e of items) {
            let $id = e.getAttribute('tree-id')
            if ($id == id) {
              e.classList.add('highlight')
            } else {
              e.classList.remove('highlight')
              e.classList.remove('highlight-row')
            }
          }
        })
      },
      numDiv(num1, num2) {
        num1 = num1 ? num1 : 0.000
        num2 = num2 ? num2 : 0.000
        var baseNum1 = 0,
          baseNum2 = 0;
        var baseNum3, baseNum4;
        try {
          baseNum1 = num1.toString().split(".")[1].length;
        } catch (e) {
          baseNum1 = 0;
        }
        try {
          baseNum2 = num2.toString().split(".")[1].length;
        } catch (e) {
          baseNum2 = 0;
        }
        //with (Math) {
        baseNum3 = Number(num1.toString().replace(".", ""));
        baseNum4 = Number(num2.toString().replace(".", ""));
        return (baseNum3 / baseNum4) * Math.pow(10, baseNum2 - baseNum1);
        //}
      },
      getSapPartUse() {
        /* this.$nextTick(() => {
          let items = this.getByClass(document, 'sapPartUse')
          for (const e of items) {
            if (!e.getAttribute('data-parent_id')) {
              e.innerText = 1000.000
              continue
            }
            let a = document.getElementById(e.getAttribute('data-id')).value
            let b = this.numDiv(document.getElementById(e.getAttribute('data-parent_id')).value,1000)
            let result = this.numDiv(a,b)
            e.innerText = result.toFixed(3)
          }
        }) */
        this.loopSaveBomSap(this.treeData.lists)
      },
      loopSaveBomSap(list) {
        let arr = []
        for (const item of list) {
          arr.push(item)
          while (arr.length > 0) {
            let _tempItem = arr.shift()
            if (_tempItem.lists) {
              for (const _item of _tempItem.lists) {
                let a = _item.partUse
                let b = this.numDiv(_tempItem.partUse, 1000)
                _item.sapPartUse = this.numDiv(a, b).toFixed(3)
                _item.baseUse = parseFloat(a*(1+this.numDiv(_item.partLoss, 100))).toFixed(3)
              }
              arr.push(..._tempItem.lists)
            }
          }
        }
      },
      onSearch(query) {
        this.search = query
        this.offset = 0
      },
      toBom(record) {
        /*if (this.treeData.lists.length < 1 && !record.sapNumber.startsWith('8') && !record.sapNumber.startsWith('9')) {
          this.$message.warn('非8和9物料号不能为BOM')
          return false
        }*/
        if (this.treeData.lists.length > 0 && this.treeData.lists[0].sapNumber == record.sapNumber) {
          this.$message.warn('与一级BOM的料号相同，禁止操作')
          return false
        }
       /* if (this.optRow && this.optRow.partNumber == record.partNumber) {
          this.$message.warn('与父级BOM的料号相同，禁止操作')
          return false
        }*/
        let _list = this.optRow ? this.optRow.lists : (this.treeData.lists.length > 0 ? this.treeData.lists[0].lists : [])
        let index = _list.findIndex(item => item.sapNumber == record.sapNumber)
        let that = this
        if (index > -1) {
          that.$confirm({
            title: '存在相同的物料，确认添加？',
            onOk() {
              that.addBom(record);
            },
            onCancel() {},
            class: 'test',
          });
          return false
        }
        that.addBom(record)
      },

      toBomFromAdd(record) {
        let that = this
        that.addBom(record)
      },


      switchApp() {
        const applicationData = Vue.ls.get(ALL_APPS_MENU)
        this.MenuChange(applicationData[0]).then((res) => {}).catch((err) => {
          this.$message.warn('错误提示：' + err.message, 1)
        })
      },


      onClose() {
        this.visible = false;
      },
      addBom(record) {
        this.vloading = true
        getPartRandom({}).then((res) => {
          if (res.success) {
            if (this.treeData.lists.length < 1) {
              this.treeData.lists.push({
                'id': res.data,
                'open': true,
                'partName': record.partName,
                'partDescription': record.partDescription,
                'partUnit': record.partUnit,
                'partClass': record.partClass,
                'sapNumber': record.sapNumber,
                'partUse': parseFloat(1000).toFixed(3),
                'sapPartUse': parseFloat(1000).toFixed(3),
                'baseUse':parseFloat(1000).toFixed(3),
                'partLoss': parseFloat(0).toFixed(2),
                'partNumber': record.partNumber,
                'partGroup': '',
                'posnr': '',
                'desc': '',
                'version': '',
                'substitute': [],
                'validate': false,
                'lists': []
              })
            } else {
              if (this.optRow) {
                this.optRow.lists.push({
                  'id': res.data,
                  'open': true,
                  'partName': record.partName,
                  'partDescription': record.partDescription,
                  'partUnit': record.partUnit,
                  'partClass': record.partClass,
                  'sapNumber': record.sapNumber,
                  'partUse': null,
                  'sapPartUse': parseFloat(0).toFixed(3),
                  'baseUse':parseFloat(0).toFixed(3),
                  'partLoss': parseFloat(0).toFixed(2),
                  'partNumber': record.partNumber,
                  'partGroup': '',
                  'posnr': '',
                  'desc': '',
                  'version': '',
                  'validate': false,
                  'parent_id': this.optRow.id,
                  'substitute': [],
                  'lists': []
                })
              } else {
                this.treeData.lists[0].lists.push({
                  'id': res.data,
                  'open': true,
                  'partName': record.partName,
                  'partDescription': record.partDescription,
                  'partUnit': record.partUnit,
                  'partClass': record.partClass,
                  'sapNumber': record.sapNumber,
                  'partUse': null,
                  'sapPartUse': parseFloat(0).toFixed(3),
                  'baseUse':parseFloat(0).toFixed(3),
                  'partLoss': parseFloat(0).toFixed(2),
                  'partNumber': record.partNumber,
                  'partGroup': '',
                  'posnr': '',
                  'desc': '',
                  'version': '',
                  'validate': false,
                  'parent_id': this.treeData.lists[0].id,
                  'substitute': [],
                  'lists': []
                })
              }
            }
            this.callSysBomSave(0)
            setTimeout(() => {
              this.getlevelid()
            }, 1000);
          } else {
            this.$message.error(res.message)
          }
          this.vloading = false
        }).catch((err) => {
          this.$message.error('错误：' + err.message)
          this.vloading = false
        })
      },
      refresh() {
        this.$emit('onImport')
      },
      bomUpGrade() {
        this.$refs.bomupgrade.edit(this.bom)
      },
      updateVis() {
        this.disabled = true;
      },
      doDel(row) {
        this.removeTreeListItem(this.treeData.lists, row.id)
      },

      resetOptRow(list,id){
        // 初次进来，新增物料无法拿到物料列表
        if(!this.optRow){
          this.optRow = this.treeData.lists[0]
        }

        if (this.optRow.id == id) {
          this.optRow = this.treeData.lists[0]
          this.hightLow(this.optRow.id)
          return false
        }
        let arr = []
        for (const item of list) {
          arr.push(item)
          while (arr.length > 0) {
            let _tempItem = arr.shift()
            if (_tempItem.id == this.optRow.id) {
              this.optRow = this.treeData.lists[0]
              this.hightLow(this.optRow.id)
              return false
            }
            if (_tempItem.lists) {
              arr.push(..._tempItem.lists)
            }
          }
        }
      },

      removeTreeListItem(treeList, id) { // 根据id属性从数组（树结构）中移除元素
        if (!treeList || !treeList.length) {
          return
        }
        for (let i = 0; i < treeList.length; i++) {
          if (treeList[i].id === id) {
            this.resetOptRow(treeList[i].lists,id)
            treeList.splice(i, 1);
            this.callSysBomSave(0)
            this.hightLow(this.optRow.id)
            return false;
          }
          this.removeTreeListItem(treeList[i].lists, id)
        }
      },
      /* callWearks() {
        this.dloading = true
        getwerks({})
          .then((res) => {
            if (res.success) {
              this.wearks = res.data
            } else {
              this.$message.error(res.message, 1);
            }
            this.dloading = false
          })
          .catch((err) => {
            this.dloading = false
            this.$message.error('错误提示：' + err.message, 1)
          });
      }, */

      gotoSor(){
        //this.switchApp()
        this.$router.push({
          path: "/system_battery_design_sor",
          query: {
            batteryId:this.batteryId
          },
        });
      }, gotoMi(){
        //this.switchApp()
        this.$router.push({
          path: "/g_cylinder_mi_standard_manage",
          query: {
            batteryId: this.batteryId,
          },
        });
      }, gotoManager(){
        //this.switchApp()
        this.$router.push({
          path: "/battery_design_manager",
          query: {
            batteryId:this.batteryId
          },
        });
      },



      opt(row) {
       /* if (!row.sapNumber.startsWith('8') && !row.sapNumber.startsWith('9')) {
          this.$message.error('非8和9物料号不能为BOM')
          return false;
        }*/
        /* if (this.optRow) {
          this.$refs.dratree1.HighlightRow(this.optRow.id, false, false);
        } */
        this.optRow = row;
        this.hightLow(row.id)
        //this.$refs.dratree1.HighlightRow(row.id, true, false);
        this.$message.info('选择成功,将搭建' + row.partNumber + this.nodeMap.get(row.partClass) + "的BOM");
      },

        callSysBomSave(flag) {
        this.bom.bomData = JSON.stringify(this.treeData.lists)



          let param = {}

          param.id = this.id
          param.bomData = this.bom.bomData

          if(this.treeData.lists.length > 0){

            param.partName = this.nodeMap.get(this.treeData.lists[0].partClass)

            param.partCode = this.treeData.lists[0].sapNumber

            param.unit = this.treeData.lists[0].partUnit
          }


          updateBom(param)
          .then((res) => {
            if (res.success) {
              /* if (!this.bom.id) {
                this.onSave(res.data)
              } */

              getBom({batteryId:this.batteryId}).then(res => {
                // console.log(JSON.parse(res.data.bomData))

                this.form.setFieldsValue(res.data)

              })

            } else {
              this.$message.error(res.message, 1);
            }
            if (flag)
              this.dloading = false
            else
              this.vloading = false
          })
          .catch((err) => {
            if (flag)
              this.dloading = false
            else
              this.vloading = false
            this.$message.error('错误提示：' + err.message, 1)
          });
      },
      getPartGoup(list) {
        let arr = []
        for (const item of list) {
          arr.push(item)
          while (arr.length > 0) {
            let _tempItem = arr.shift()
            if (_tempItem.substitute && _tempItem.substitute.length > 0) {
              this.partGroupArr.push(_tempItem.partGroup)
            }
            if (_tempItem.lists) {
              arr.push(..._tempItem.lists)
            }
          }
        }
      },

      callGetBom() {
        this.openStatus = false
        this.dloading = true
        this.vloading = true
        getBom({
          batteryId: this.batteryId
        })
          .then((res) => {

            if(null == res.data){
              addBom({
                batteryId: this.batteryId
              }).then(res => {
                if (res.success) {
                  getBom({
                    batteryId: this.batteryId
                  }).then((res) => {
                    if (res.success) {
                      for (let i = 0; i < res.data.transportList.length; i++) {
                        this.bomTransport.push(res.data.transportList[i])
                      }
                      this.bom = res.data
                      this.lindIds = []

                      if(res.data.factory){
                        let factorys =  JSON.parse(res.data.factory)
                        for (let i = 0; i < factorys.length; i++) {
                          this.lindIds.push(factorys[i].id)
                        }
                      }

                      this.updateTime = res.data.updateTime
                      this.form.setFieldsValue(res.data)
                      this.id = res.data.id

                      if(res.data.productName != this.design.batteryName){
                        this.form.setFieldsValue({productName:this.design.productName})
                        let param = {}
                        param['id'] = this.id
                        param['productName'] = this.design.productName
                        updateBom(param)
                      }



                      let is0or4 = true
                      this.isdraggable = true
                      this.disabled = false
                      this.showview = true

                      let _list = JSON.parse(this.bom.bomData)

                      if(_list.length > 0){
                        if (_list[0].lists.length < 1 && this.bom.bomStatus == 0) {
                          this.openStatus = true
                        }
                        this.getPartGoup(_list)
                        this.treeData.lists = _list

                        setTimeout(() => {
                          this.getlevelid()
                        }, 1000);

                        if (is0or4) {
                          this.optRow = this.treeData.lists[0]
                          this.hightLow(this.optRow.id)
                          //this.$refs.dratree1.HighlightRow(this.treeData.lists[0].id, true, false);
                        }
                      }


                    } else {
                      this.$message.error(res.message, 1);
                    }
                  })
                }
              })
            }else{
              if (res.success) {

                for (let i = 0; i < res.data.transportList.length; i++) {
                  this.bomTransport.push(res.data.transportList[i])
                }



                this.bom = res.data
                this.lindIds = []
                this.updateTime = res.data.updateTime
                this.form.setFieldsValue(res.data)

                if(res.data.factory){
                  let factorys =  JSON.parse(res.data.factory)

                  for (let i = 0; i < factorys.length; i++) {
                    this.lindIds.push(factorys[i].id)
                  }
                }
                this.id = res.data.id


                if(res.data.productName != this.design.productName){
                  this.form.setFieldsValue({productName:this.design.productName})
                  let param = {}
                  param['id'] = this.id
                  param['productName'] = this.design.productName
                  updateBom(param)
                }

                let is0or4 = true
                this.isdraggable = true
                this.disabled = false
                this.showview = true

                let _list = JSON.parse(this.bom.bomData)


                if(_list.length > 0){
                  if (_list[0].lists.length < 1 && this.bom.bomStatus == 0) {
                    this.openStatus = true
                  }
                  this.getPartGoup(_list)
                  this.treeData.lists = _list
                  setTimeout(() => {
                    this.getlevelid()
                  }, 1000);

                  if (is0or4) {
                    this.optRow = this.treeData.lists[0]
                    this.hightLow(this.optRow.id)
                    //this.$refs.dratree1.HighlightRow(this.treeData.lists[0].id, true, false);
                  }
                }


              } else {
                this.$message.error(res.message, 1);
              }
            }


            this.dloading = false
            this.vloading = false
            setTimeout(() => {
              this.getSapPartUse()
            }, 1500);
          })
          .catch((err) => {
            this.dloading = false
            this.vloading = false
            this.$message.error('错误提示：' + err.message, 1)
          });
      },

      callWerkLines() {
        this.confirmLoading = true
        getwerklines().then((res) => {
          if (res.success) {
            let mapline = {}
            for (var key in res.data) {
              for (const _item of res.data[key]) {
                mapline[_item.id] = _item.namecode ? _item.namecode + '--' + _item.lineName : _item.werkNo + '->' + _item.lineName
              }
            }
            this.dataLines = mapline
            this.werklines = res.data;

            let _options = []
            _options.push({
              key:'hz',
              value: 'hz',
              title: '惠州动力电池研究院'
            })
            _options.push({
              key:'jm',
              value: 'jm',
              title: '荆门动力电池研究院'
            })
            for (const key in this.werklines) {


              if (this.werklines[key]) {

                let children = []

                for (const item of this.werklines[key]) {

                  children.push({
                    key:key+'-'+item.id,
                    value:key+'-'+item.id,
                    title:item.lineName,

                    //isDisabled: disablelines.indexOf(item.id) < 0 ? false:true
                  })
                }

                if (children.length > 0) {
                  let _parent = {
                    key:key,
                    value: key,
                    title: this.werklines[key][0].werkNo+'--'+this.werklines[key][0].werks+'--'+this.werklines[key][0].namecode,
                    children:children
                  }
                  _options.push(_parent)
                }


              }
            }




            this.options = _options


          } else {
            this.$message.error(res.message)
          }
          this.confirmLoading = false
        }).catch((err) => {
          this.$message.error('错误：' + err.message)
          this.confirmLoading = false
        })
      },

      getByClass(parent, cls) {
				if (parent.getElementsByClassName) {
					return Array.from(parent.getElementsByClassName(cls));
				} else {
					var res = [];
					var reg = new RegExp(' ' + cls + ' ', 'i')
					var ele = parent.getElementsByTagName('*');
					for (var i = 0; i < ele.length; i++) {
						if (reg.test(' ' + ele[i].className + ' ')) {
							res.push(ele[i]);
						}
					}
					return res;
				}
			},

      initSelect(){
        this.$nextTick(()=>{
          let inputEleWidth = document.getElementById('productType').offsetWidth
          let formItemEleWidth = document.getElementById('productTypeCol').offsetWidth
          let diffItemEleWidth = formItemEleWidth - 70 - inputEleWidth
          let items = this.getByClass(document,'ant-form-item-control-wrapper')
          for (const e of items) {
            e.style.width = inputEleWidth+'px'
          }
          //document.getElementById('factory').style.width = ( inputEleWidth*2 + 70 + diffItemEleWidth) + 'px'
        })
      },

      substitute(partGroup, partGroupAdd, tempgroup) {
        if (partGroup && partGroupAdd && this.partGroupArr.indexOf(partGroup) < 0) {
          this.partGroupArr.push(partGroup)
        }
        if (!partGroup && !partGroupAdd && tempgroup) {
          let index = this.partGroupArr.indexOf(tempgroup)
          this.partGroupArr.splice(index, 1)
        }
        this.callSysBomSave(0)
      },

      setNodeMap(list, map) {
        let arr = []
        for (const item of list) {
          arr.push(item)
          while (arr.length > 0) {
            let _tempItem = arr.shift()
            map.set(_tempItem.id, _tempItem.name)
            if (_tempItem.lists) {
              arr.push(..._tempItem.lists)
            }
          }
        }
      },
      callGetAllNode() {
        this.loading = true
        getAllNode()
          .then((res) => {
            if (res.success) {
              let map = new Map()
              this.setNodeMap(res.data, map)
              this.nodeMap = map;
              this.nodes = res.data
              this.callGetBom()
            } else {
              this.$message.error(res.message, 1);
            }
            this.loading = false
          }).finally((res) => {
          this.dloading = false
          this.vloading = false
          this.copyModal = false
          this.cloading = false
        })
          .catch((err) => {
            this.loading = false
            this.$message.error('错误提示：' + err.message, 1)
          });
      }
    },
    watch: {
      date(newVal, oldVal) {
        this.callGetBom()
      }
    },

    created() {

      this.batteryId = this.$route.query.batteryId
      getBatteryDesign({inBatteryId:this.$route.query.batteryId,type:'bom'}).then(res => {
        this.design = res.data
        switch (res.data.structureType) {
          case 'g_cylinder':
            this.structureType1 = 'G圆柱'
            break
          case 'c_cylinder':
            this.structureType1 = 'C圆柱'
            break
          case 'v_cylinder':
            this.structureType1 = 'V圆柱'
            break
          case 'winding':
            this.structureType1 = '方形卷绕'
            break
          case 'lamination':
            this.structureType1 = '方形叠片'
            break
          case 'soft_roll':
            this.structureType1 = '软包'
            break

        }
      })
      this.callGetAllNode()
      this.callWerkLines()
      this.mouseDown()
      this.initSelect()

      this.breadcrumb = JSON.parse(localStorage.getItem('breadcrumb'))

    },
  }
</script>

<style lang='less' scoped=''>
  /deep/.ant-form-item-control {

    line-height: unset!important;

  }

  .moveTip {
    color: #fff;
    background: rgb(0 0 0 / 69%);
    min-height: 20px;
    min-width: 60px;
    position: fixed;
    border-radius: 2px;
    z-index: 1000;
    padding: 5px;
  }
  /deep/.tree-column {
		padding: 2px 0 !important;

	}
  /deep/.drag-tree-table-header .tree-column{
    color: #fff;
    background: #0049b0;
  }
	/deep/.tree-row {
		line-height: initial !important;
	}
	/deep/.drag-tree-table-header {
		height: initial !important;
	}
	.projectdetail {
		margin-bottom: 12px;
	}
	/deep/.drag-tree-table {
		margin: 0;
	}
	/deep/.drag-tree-table-header,
	/deep/.tree-row {
		height: auto;
		line-height: inherit;
		font-size: 14px;
		font-weight: initial;
		color: #000;
	}
	/deep/.drag-tree-table-header {
		background: #fafafa;
		border-bottom: 1px solid #e8e8e8;
	}
  .main {
    display: flex;
    width: 100%;
  }
  .left_main {
    flex: 1;
    overflow: auto;
  }
  .right_main {
    margin-left: 12px;
    width: 520px;
    display: block;
    overflow: auto;
    transition: width 0.5s;
  }
  .right_main_show {
    width: 0;
    margin: 0;
  }
  .right_main_show * {
    display: none;
  }
  .table-page-search-wrapper .ant-form-inline .ant-form-item {
    margin-bottom: 14px;
  }
  .table-page-search-wrapper .ant-form-inline .ant-form-item .ant-form-item-control {
    height: auto;
  }
  .ant-drawer-body {
    height: 100%;
  }
  /* select {
    color: rgba(0, 0, 0, 0.65);
    display: block;
    height: 23px;
    line-height: 23px;
    font-size: 14px;
    outline: none;
    border: 1px solid #d9d9d9;
  }
  input {
    color: #000;
    height: 23px;
    line-height: 23px;
    width: 60px;
    font-size: 14px;
    border: 1px solid #d9d9d9;
    outline: none;
  } */
  p.error {
    color: rgb(250, 9, 9);
    margin-bottom: 2px;
  }
  .uncheck {
    color: #a7a5a5;
  }
  .vue-treeselect--has-value .vue-treeselect__multi-value {
    margin: 0;
  }
  .ant-descriptions-bordered .ant-descriptions-item-label,
  .ant-descriptions-bordered .ant-descriptions-item-content {
    padding: 5px;
  }
  .ant-descriptions-row>th,
  .ant-descriptions-row>td {
    padding: 0;
  }
  .ant-descriptions-item-content,
  .ant-descriptions-item-label {
    font-size: 12px;
  }
  .stopdiv {
    width: 100%;
    height: 100%;
  }
  .readonly {
    color: #000;
    width: 100%;
    font-size: 12px;
    padding: 0;
    border: 0;
    background: inherit;
  }
  .text-align {
    text-align: center;
  }
  .tree-row.highlight {
    background: #e1ecf7 !important;
  }

  div.tab-head div.active{
    font-size: 24px;
    color: rgba(0,73,176,1);
    margin-bottom: -4px;
    cursor: text;
  }

  .tab-title{
    padding: 0 10px;
  }

  div.tab-head{
    border-bottom: 1px solid #d3d2d2c9;
  }


  .h1{
    font-size: 18px;
    /* margin-bottom: 5px; */
    text-align: center;
    background: #0049b0;
    color: #fff;
  }
  .ant-table-wrapper{
    background: #fff;
  }



  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
  .ant-table-thead > tr > th{
    padding:2px 0 2px 4px;
    border-bottom: 1px solid #d2d4d7;
    border-right: 0;
    font-size: 13px;
    /* font-weight: bold; */
    background: rgba(0, 73, 176, 0.7);
    color: #fff;
  }
  .ant-table-tbody > tr > td {
    padding: 0;
    margin: 0;
    border-bottom: 1px solid #d2d4d7;
    border-right: 1px solid #d2d4d7;
    font-size: 12px;
  }

  .tdcls{
    color: #000;
    background: rgb(239, 239, 239);
    padding: 0px 0 0px 4px !important;
  }

  .td_border_right{
    border-right: 1px solid #d2d4d7;
  }
  .td_width{
    width: 50px;
    padding: 0 !important;
  }
  .div_width{
    width: 30px;
    margin: auto;
  }

  .wrapper{
    width: 50%;
    background: #fff;
    padding: 0 10px;
    overflow: hidden;
    float: left;
  }
  .ant-select-selection{
    border: none;
  }
  .spanstatus{
    display: block;
    padding: 1px 4px;
    border-radius: 2px;
    height:25px;
  }

  .statusbar{
    overflow: hidden;
    height: 32px;
    line-height: 32px;
    font-size: 12px;
    text-align: right;
  }
  .statusbar::after{
    content: ' ';
    display: block;
    height: 0;
    clear: both;
  }
  .statusbar .icon{
    margin-right: 3px;
    font-weight: bold;
  }
  .statusbar .btn{
    /* float: right; */
    margin-left: 20px;
  }
  .statusbar .a-btn{
    border-radius: 2px;
    background: #0049b0;
    color: #fff;
    padding: 2px 15px;
    letter-spacing: 2px;
  }
  .statusbar .txt{
    color: #000;
    font-weight: bold;
  }
  .statusbar .tip{
    float: left;
    /* margin-left: 120px; */
  }
  .tab-title{
    padding: 0 10px;
  }
  .anticon svg {
    font-size: 13px;
  }

  div.tab-head{
    border-bottom: 1px solid #d3d2d2c9;
  }

  div.tab-head div{
    display: inline-block;

    font-weight: 700;
    font-size: 18px;
    color: rgb(128, 128, 128);
    margin-bottom: -6px;
    cursor: pointer;
  }

  div.tab-head div.active{
    font-size: 24px;
    color: rgba(0,73,176,1);
    margin-bottom: -4px;
    cursor: text;
  }

  .drag-tree-table-header .tree-column {
    color: #FFFFFF;
    background: rgba(0, 73, 176, 0.7);
  }

  div.sub-title{
    overflow: hidden;
    padding-right: 10px;
  }


  div.sub-title::after{
    content: ' ';
    display: block;
    height: 0;
    clear: both;
  }

  div.sub-title .tip{
    font-family: SourceHanSansSC;
    font-weight: 400;
    font-size: 15px;
    color: rgba(0,101,255,0.67);
  }
  .ant-table-thead > tr > th:first-child{
    padding: 0;
  }

  .ant-table-tbody > tr.ant-table-row-selected td:first-child{
    background: #fafafa;
  }

  .tdcls1{
    color: #000;
    background: rgb(239, 239, 239);
  }
  .ant-input[disabled] {
     color: rgb(0 0 0 / 67%);
     background-color: #ffffff;
     cursor: not-allowed;
     opacity: 1;
   }
  .ant-select-selection-selected-value{
    text-align: center;
  }
  .ant-select-open .ant-select-selection{
    border: none;
    box-shadow:none;
  }
   .ant-select-selection__rendered{
    margin: auto;
    line-height:initial;
    font-size: 12px;
    text-align: center;
  }
   .ant-select-selection--single{
    height: auto;
  }
  .ant-table-tbody > tr > td {
    height: 25px;
  }

  .input {
		color: #000;
		height: 23px;
		line-height: 23px;
		width: 60px;
		font-size: 14px;
		border: 1px solid #d9d9d9;
		outline: none;
	}
  /deep/.ant-form-item label{
    width: 70px;
    display: inherit;
    text-align: left;
  }
  /deep/.ant-form-item-label > label::after{
    content: '';
  }

  /deep/.ant-input[disabled] {
    color: rgba(0, 0, 0, 0.65);
  }

  /deep/.ant-select-disabled  {
    background: white!important;
  }
  /deep/.ant-select-selection  {
    background: white!important;
  }

</style>