import { axios } from '@/utils/request'

export function getMIAttachVersionList (parameter) {
  return axios({
    url: '/miAttachVersion/getMIAttachVersionList',
    method: 'get',
    params: parameter
  })
}

export function insertMIAttachVersion (parameter) {
  return axios({
    url: '/miAttachVersion/insertMIAttachVersion',
    method: 'post',
    data: parameter
  })
}

export function updateMIAttachVersion (parameter) {
  return axios({
    url: '/miAttachVersion/updateMIAttachVersion',
    method: 'post',
    data: parameter
  })
}
