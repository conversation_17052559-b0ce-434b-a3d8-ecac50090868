<template>
  <div>
    <topiclist  v-if="type ==1" :listType="4"/>
    <yzlist v-else :listType="4"/>
  </div>
</template>

<script>

  import topiclist from './topiclist'
  import yzlist from './yz_list'

  export default {
    name: 'start_list',
    components: {
      topiclist,
      yzlist
    },
    data() {
      return {
        type:null
      }
    },
    mounted() {

      this.type = this.$route.query.type
    },
    methods: {},
    created() {
    }
  }
</script>

<style scoped>

</style>