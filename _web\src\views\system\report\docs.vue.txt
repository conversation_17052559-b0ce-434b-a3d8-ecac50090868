<template>
  <div>
    <a-breadcrumb class="breadcrumb" separator=">">
      <a-breadcrumb-item><a @click="gotoIndex(-2)">信息对齐表</a></a-breadcrumb-item>
      <a-breadcrumb-item><a @click="gotoIndex(-1)">产品开发进展</a></a-breadcrumb-item>
      <a-breadcrumb-item>产品主要技术文档</a-breadcrumb-item>
    </a-breadcrumb>
  <ve-table
      v-if="showDetail"
      :border-y="true"
      fixed-header
      :max-height="windowHeight"
      :columns="columns"
      :table-data="rows"
      :cell-style-option="cellStyleOption"
      :cell-span-option="cellSpanOption"
      id="loading-docs"
    >

  </ve-table>

    <a-drawer placement="right" :closable="false" width="80%" :visible="visible1" @close="onClose2" :destroyOnClose="true">
      <checkhistory :param="param"></checkhistory>
    </a-drawer>

    <a-drawer :bodyStyle="{ height: '100%' }" placement="right" :closable="false" width="80%" height="100%" :visible="visible2" @close="onClose1">
      <iframe :src="pdfUrl+'#view=FitH,top'" width="100%" height="100%"></iframe>
    </a-drawer>
    <checkhistory2 ref="checkhistory2" @ok="handleOk" />
  </div>
</template>

<script>
import { getReportDocsData } from "@/api/modular/system/report"
import { mapActions, mapGetters } from 'vuex'
import { ALL_APPS_MENU } from '@/store/mutation-types'
    import Vue from 'vue'
import checkhistory2 from '../projects/checkhistory2'
import checkhistory from '../projects/checkhistory'
const OtherComp = {
        name: "OtherComp",
        template: `
        <div class="other-comp">
            <div v-for='item in arr'>{{item}}</div>
        </div>
    `,
        props: {
            row: Object,
            column: Object,
            arr: Array,
        },
    }
export default {

  components: {
    checkhistory2,
    checkhistory
  },

    name:'docs',
    data(){
      return{
        showDetail:false,
        merges:['classType'],
        exmerges:['werkLineName'],
        showTitle: {
          showTitle: true,
        },

        historyBomId:'',
        param:{},

        visible1:false,

        visible2: false,
        pdfUrl: '',
        loadingInstance: null,
        windowHeight: document.documentElement.clientHeight - 41,
        columns:[],
        rows:[],
        cellStyleOption:{
          headerCellClass: ({ column, rowIndex }) => {
            if (column.key == "a") {
              return "font-size:14px;padding:15px 0!important;";
            }
            return "font-size:12px";
          },
        },
        cellSpanOption: {
          bodyCellSpan: this.bodyCellSpan,
          //footerCellSpan: this.footerCellSpan,
        },
      }
    },
    methods:{
      gotoIndex(index){
      this.$router.go(index)
    },
      handleOk() {},
      previewPdf(fileId) {
        if(null == fileId){
          this.$message.info('文件为空')
          return
        }

        this.pdfUrl = process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id=' + fileId
        this.visible2 = true;

      },
      onClose1() {
        this.visible2 = false;
      },
      onClose2() {
        this.visible1 = false;
      },
      bodyCellSpan({ row, column, rowIndex }) {
      if (this.merges.includes(column.key)) {
        const _col = row[column.key + "_rowSpan"] > 0 ? 1 : 0;
        return {
          colspan: _col,
          rowspan: row[column.key + "_rowSpan"],
        };
      }
      if (this.exmerges.includes(column.key)) {
        let dataindex = this.merges[this.merges.length - 1];
        const _row = row[dataindex + "_rowSpan"];
        const _col = _row > 0 ? 1 : 0;

        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
      callReportDocsData(){
        this.show()
        getReportDocsData(this.$route.query)
        .then((res)=>{
          if (res.result) {
            res.data.columndata[0].renderHeaderCell = this.renderHeaderCellTop
            let child1 = res.data.columndata[0].children
            for (var item of child1) {
              item.renderHeaderCell = this.renderHeaderCell
            }
            let child2 = child1[0].children
            for (const _item of child2) {
              _item.renderBodyCell = this.renderBodyCell
              /*if (_item.key == 'fileName') {
                 _item.ellipsis = this.showTitle;
              }*/
            }
            this.columns = res.data.columndata

            this.merges.forEach((item) => {
              for (let i = 0, j = res.data.rowdata.length; i < j; i++) {
                let rowSpan = 0;
                let n = i;
                while (
                  res.data.rowdata[n + 1] &&
                  res.data.rowdata[n + 1][item] == res.data.rowdata[n][item]
                ) {
                  rowSpan++;
                  n++;
                  res.data.rowdata[n].rowSpan = 0;
                }
                if (rowSpan) res.data.rowdata[i][item + "_rowSpan"] = rowSpan + 1;

                if (!rowSpan) res.data.rowdata[i][item + "_rowSpan"] = 1;

                i += rowSpan;
              }
            });

            this.rows = res.data.rowdata
            this.showDetail = true
          } else {
            this.$message.error(res.message,1);
          }
          this.close()
        })
        .catch((err)=>{
          this.close()
          this.$message.error('错误提示：' + err.message,1)
        });

      },
      renderHeaderCellTop({column}){
        return(
          <span style='font-size:20px;display:block;padding:10px 0;'>
            {column.title}
          </span>
        )
      },
      renderHeaderCell({ column }) {
        return (
          <div style="text-align:left;padding-left:30px;">
            <span
              class="clickheadstyle"
              onClick={() => {
                this.handleTo();
              }}
            >
              {column.title}
            </span>
          </div>
        );
      },
      renderBodyCell({row, column, rowIndex}){
        if (column.key == 'fileType') {
          if (row["fileType"].startsWith("BOM")) {
            return (
              <span
                class=""
                onClick={() => {
                  this.handleToBom();
                }}
              >
                {row[column.key]}
              </span>
            );
          }
          return (
            <span
              class=""
              onClick={() => {
                this.handleToJira(row);
              }}
            >
              {row[column.key]}
            </span>
          );
        }

        if (column.key == 'fileName') {

            return (
              <span
                class="clickcellstyle"
                onClick={() => {
                  this.previewPdf(row.fileId);
                }}
              >
                {row[column.key]}
              </span>
            );


        }

        if (column.key == 'version') {

            return (
              <span
                class="clickcellstyle"
                onClick={() => {
                    this.$refs.checkhistory2.edit(row)
                }}
              >
                {row[column.key]}
              </span>
            );


        }
        if (column.key == 'werkLineName') {
          const _arr = row[column.key].split(';')
          return <OtherComp row={row} column={column} arr={_arr} />;
        }
        return row[column.key];
      },

      handleTo() {
        /* let _key = this.$route.query.issueKey;
        let $url = `http://jira.evebattery.com/browse/${_key}?auth=` + Vue.ls.get("jtoken");
        window.open($url, "_blank"); */
        this.switchApp()
        this.$router.push({
          path: "/bom_material",
          query: this.$route.query,
        });
      },
      preview(id, processId) {
        this.historyBomId = id
        this.param.historyBomId = id
        this.param.processId = processId
        this.visible1 = !this.visible1
      },
      handleToJira(row) {
        let _key = row["issueKey"];
        if (!_key) {
          return;
        }
        let $url = `http://jira.evebattery.com/browse/${_key}?auth=` + Vue.ls.get("jtoken");
        window.open($url, "_blank");
      },

      handleToBom(){
        this.switchApp()
        let $query = {
          issueId: this.$route.query.issueId,
          title:this.$route.query.title,
        };
        
        this.$router.push({
          path: "/report_bom",
          query: $query,
        });

      },
      switchApp() {
          /* const apps = Vue.ls.get(ALL_APPS_MENU)
                const _newApps = []
                for (const item of apps) {
                    
                    _newApps.push(item)
                }
                Vue.ls.set(ALL_APPS_MENU, _newApps) */
      },
      show() {
        this.loadingInstance.show();
      },
      close() {
        this.loadingInstance.close();
      },
      ...mapActions(['MenuChange']),

    },
    computed: {
      ...mapGetters(['userInfo'])
    },
    created() {
      this.loadingInstance = this.$veLoading({
        target: document.querySelector("#loading-docs"),
        name: "flow",
      });
      this.callReportDocsData()
    },
    destroyed() {
      this.loadingInstance.destroy();
    },
}
</script>

<style lang="less">
@import './vetable.less';

.ant-drawer-body {
  height: 100%!important;
}
.breadcrumb{
  padding: 10px 0;
  padding-left: 13px;
}.ant-breadcrumb a{
  color:#5d90fa !important;
}.ant-breadcrumb{
  font-size: 12px !important;
}
</style>