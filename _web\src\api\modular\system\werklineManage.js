/**
 * 产线
 *
 * @date 2020年4月23日12:10:57
 */
 import { axios } from '@/utils/request'

 export function getWerklineList (parameter) {
    return axios({
      url: '/werkline/list',
      method: 'get',
      params: parameter
    })
  }
 
   /**
   * 获取部件分类
   *
   * @date 2020年7月9日15:05:01
   */
    export function getWerkline (parameter) {
     return axios({
       url: '/werkline/get',
       method: 'get',
       params: parameter
     })
   }
  
  /**
   * 新增部件分类
   *
   * @date 2020年7月9日15:05:01
   */
  export function sysWerklineAdd (parameter) {
    return axios({
      url: '/werkline/add',
      method: 'post',
      data: parameter
    })
  }
  
  /**
   * 编辑部件分类
   *
   * @param parameter
   * @returns {*}
   */
  export function sysWerklineEdit (parameter) {
    return axios({
      url: '/werkline/edit',
      method: 'post',
      data: parameter
    })
  }
  
  /**
   * 删除部件分类
   *
   * @date 2020年7月9日15:05:01
   */
  export function sysWerklineDelete (parameter) {
    return axios({
      url: '/werkline/delete',
      method: 'post',
      data: parameter
    })
  }

  export function sysWerkConfigSave (parameter) {
    return axios({
      url: '/sysConfig/save',
      method: 'post',
      data: parameter
    })
  }