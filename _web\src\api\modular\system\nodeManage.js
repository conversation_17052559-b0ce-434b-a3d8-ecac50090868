/**
 * 部件分类
 *
 * @date 2020年4月23日12:10:57
 */
 import { axios } from '@/utils/request'

 /**
  * 部件分类列表
  *
  * @date 2020年7月9日15:05:01
  */
 export function getNodeList (parameter) {
   return axios({
     url: '/sysNode/list',
     method: 'get',
     params: parameter
   })
 }

 export function getAllNode (parameter) {
  return axios({
    url: '/sysNode/list',
    method: 'get',
    params: parameter
  })
}
 

  /**
  * 获取部件分类
  *
  * @date 2020年7月9日15:05:01
  */
   export function getNode (parameter) {
    return axios({
      url: '/sysNode/get',
      method: 'get',
      params: parameter
    })
  }
 
 /**
  * 新增部件分类
  *
  * @date 2020年7月9日15:05:01
  */
 export function sysNodeAdd (parameter) {
   return axios({
     url: '/sysNode/add',
     method: 'post',
     data: parameter
   })
 }
 
 /**
  * 编辑部件分类
  *
  * @param parameter
  * @returns {*}
  */
 export function sysNodeEdit (parameter) {
   return axios({
     url: '/sysNode/edit',
     method: 'post',
     data: parameter
   })
 }
 
 /**
  * 删除部件分类
  *
  * @date 2020年7月9日15:05:01
  */
 export function sysNodeDelete (parameter) {
   return axios({
     url: '/sysNode/delete',
     method: 'post',
     data: parameter
   })
 }

  /**
  * 同步部件分类
  *
  * @param parameter
  * @returns {*}
  */
   export function sysNodeSync (parameter) {
    return axios({
      url: '/sysNode/sync',
      method: 'post',
      data: parameter
    })
  }

   /**
  * 批量保存部件分类
  *
  * @param parameter
  * @returns {*}
  */
    export function sysNodeSave (parameter) {
      return axios({
        url: '/sysNode/save',
        method: 'post',
        data: parameter
      })
    }
 
