<template>
  <div>
    <div style="margin: 35px 0px 10px 15px;">
      <a-breadcrumb :routes="routes" separator=">">
        <template slot="itemRender" slot-scope="{ route, routes }">
          <router-link v-if="routes.indexOf(route) === 1" :to="route.path">
            {{ route.breadcrumbName }}
          </router-link>
          <span v-else-if="routes.indexOf(route) === routes.length - 1" >
            {{ route.breadcrumbName }}
          </span>
          <router-link v-else :to="route.path">
            {{ route.breadcrumbName }}
          </router-link>
        </template>
      </a-breadcrumb>
    </div>
    <div>
      <p style="font-size: 8px;margin-left: 15px;color: black;font-weight: bold;">填写说明：1.仅限于工艺部门维护</p>
      <p style="font-size: 8px;margin-left: 75px;margin-bottom:15px;color: black;font-weight: bold;">2.附图文件版本需同MI版本一致</p>
    </div>
    <div>
      <a-table
        id="develop"
        :columns="columns"
        :data-source="resultData"
        :row-key="(record) => record.id"
        :pagination="false"
        :scroll="{ y: windowHeight }"
        bordered
      >
      <div slot="description" slot-scope="text,record">
        <a-textarea v-model="record.description"
                    :auto-size="{ minRows: 4, maxRows: 5 }"
               @blur="updateData($event,record,'description')"/>
      </div>
        <div slot="attachPicture"
             slot-scope="text,record,index"
             id="attachPictureId">
        <a-upload
          list-type="picture-card"
          class="avatar-uploader"
          :headers="headers"
          :action="postUrl"
          :fileList="record.attachPicture"
          @preview="handlePreview"
          @change="handleChange($event,record)"
        >
          <a-icon v-if="record.attachPicture.length < 6" type="plus" />
        </a-upload>
        <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel">
          <img alt="example" style="width: 100%" :src="previewImage" />
        </a-modal>
      </div>
      <div slot="remark" slot-scope="text,record">
        <a-textarea v-model="record.remark" style="width:90%"
                    :auto-size="{ minRows: 4, maxRows: 6 }"
               @blur="updateData($event,record,'remark')"/>
        <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => deleteMiAttachVersion(record.id)">
          <a-icon class="deleteIcon" type="minus-circle" style="color: red" :style="{paddingLeft: bigClient?'2.1%':'1.85%'}"/>
        </a-popconfirm>
      </div>
      <template slot="title">
        <table>
          <tr class="renderTr">
            <td style="width: 20%;" rowspan="4" colspan="2">
              <img src="/img/logo.53575418.png" alt="logo" style="width: 80px;height: 80px">
            </td>
            <td style="font: bold normal 18px arial;width: 50%" rowspan="2" colspan="4">Manufacturing Istruction</td>
            <td style="font-weight: bold;width: 10%">文件编号：</td>
            <td style="width: 20%" colspan="3">
              <input :value="libraryData.documentNo"
                     @change="updateLibData($event, libraryData, 'documentNo')"/>
            </td>
          </tr>
          <tr class="renderTr">
            <td style="font-weight: bold">版本：</td>
            <td colspan="3">
              <input :value="libraryData.version"
                     @change="updateLibData($event, libraryData, 'version')"/>
            </td>
          </tr>
          <tr class="renderTr">
            <td colspan="4" rowspan="2">
              <input :value="libraryData.attachVersion" style="font: bold normal 16px arial;"
                     @change="updateLibData($event, libraryData, 'attachVersion')"/>
            </td>
            <td style="font-weight: bold">样品阶段：</td>
            <td colspan="3">
              <input :value="libraryData.sampleStage"
                     @change="updateLibData($event, libraryData, 'sampleStage')"/>
            </td>
          </tr>
          <tr class="renderTr">
            <td style="font-weight: bold">页码：</td>
            <td colspan="3">
              <input :value="libraryData.page"
                     @change="updateLibData($event, libraryData, 'page')"/>
            </td>
          </tr>
        </table>
      </template>
      <template slot="footer" slot-scope="currentPageData">
        <table>
          <tr>
            <td style="padding: 0;border: 0;width: 10%;"></td>
            <td style="padding: 0;border: 0;width: 10%;"></td>
            <td style="padding: 0;border: 0;width: 20%;"></td>
            <td style="padding: 0;border: 0;width: 20%;"></td>
            <td style="padding: 0;border: 0;width: 20%;"></td>
            <td style="padding: 0;border: 0;width: 20%;"></td>
          </tr>
          <tr style="border: 1px solid black;">
            <td id="iconPlusTd">
              <a-icon type="plus" slot="footer" :style="{paddingLeft: bigClient?'2.1%':'1.85%'}"
                      @click="addMIAttachVersion(libraryData)"/>
            </td>
          </tr>
          <tr>
            <td>编制</td>
            <td>
              <input :value="libraryData.miavEstablishment"
                     @change="updateLibData($event, libraryData, 'miavEstablishment')"/>
            </td>
            <td>审核</td>
            <td>
              <input :value="libraryData.miavAudit"
                     @change="updateLibData($event, libraryData, 'miavAudit')"/>
            </td>
            <td>批准</td>
            <td>
              <input :value="libraryData.miavApproval"
                     @change="updateLibData($event, libraryData, 'miavApproval')"/>
            </td>
          </tr>
        </table>
      </template>
    </a-table>
    </div>
  </div>
</template>
<script>
import {
  getMIStandardLibById,
  updateMIStandardLib
} from '@/api/modular/system/gCylinderMILibManage';
import {
  getMIAttachVersionList,
  insertMIAttachVersion,
  updateMIAttachVersion
} from "@/api/modular/system/miAttachVersionManage";
import Vue from "vue";
import { ACCESS_TOKEN } from "@/store/mutation-types";
import $ from 'jquery';
import { message } from "ant-design-vue";
function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}
  export default {
    components: {
    },
    data() {
      return {
        saveData: {},
        headers: {
          Authorization: 'Bearer ' + Vue.ls.get(ACCESS_TOKEN),
        },
        postUrl: '/api/sysFileInfo/uploadfile',
        previewVisible: false,
        previewImage: '',
        // 表头
        columns: [
          {
            title: '描述',
            dataIndex: 'description',
            scopedSlots: { customRender: 'description' },
            align: 'center',
            width: '20%',
          },
          {
            title: '附图',
            dataIndex: 'attachPicture',
            scopedSlots: { customRender: 'attachPicture' },
            align: 'center',
            width: '60%',
          },
          {
            title: '备注',
            dataIndex: 'remark',
            align: 'center',
            width: '20%',
            scopedSlots: { customRender: 'remark' },
          },
    ],
        visible: false,
        editVisible: false,
        confirmLoading: false,
        mIStandardLibData: {},
        bigClient: document.documentElement.clientHeight > 700,
        windowHeight: document.documentElement.clientHeight - 200,
        form: this.$form.createForm(this,{ name: 'form' }),
        editForm: this.$form.createForm(this,{ name: 'editForm' }),
        resultData: [],
        libraryData: {},
        routes: [
          {
            path: '/batterydesignStandard',
            breadcrumbName: '标准规范',
          },
          // {
          //   breadcrumbName: 'G圆柱',
          // },
          {
            path: '/g_cylinder_mi_library',
            breadcrumbName: 'G圆柱 MI设计与组合',
          },
          {
            breadcrumbName: 'xxx',
          },
        ],
      };
    },
    created() {
      this.routes[2].breadcrumbName = this.$route.query.miAttachVersionName
      this.getMIStandardLib(this.$route.query.libraryId)
      this.getDataByLibraryId(this.$route.query.libraryId)
    },
    mounted() {
      document.getElementsByClassName("ant-layout-content")[0].style.backgroundColor = 'white';
    },
    methods: {
      getMIStandardLib(id) {
        getMIStandardLibById({ id: id }).then((res) => {
          if (res.success) {
            this.libraryData = res.data[0]
          } else {
            this.$message.error(res.message)
          }
        })
      },
      handleCancel() {
        this.previewVisible = false;
      },
      async handlePreview(file) {
        if (!file.url && !file.preview) {
          file.preview = await getBase64(file.originFileObj);
        }
        this.previewImage = file.url || file.preview;
        this.previewVisible = true;
      },
      handleChange(info,record) {
        record.attachPicture = [...info.fileList]
        if (info.file.status === 'done') {
          let targetImgs = [...record.attachPicture].slice(-1);
          let finallyImgs = [...record.attachPicture].slice(0,-1)
          finallyImgs.push({
            uid: targetImgs[0].uid,
            name: targetImgs[0].name,
            status: targetImgs[0].status,
            url: process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id=' + targetImgs[0].response.data.id,
          })
          let res = info.file.response
          if (res.success) {
            let update = {}
            update.id = record.id
            update.attachPicture = JSON.stringify(finallyImgs)
            updateMIAttachVersion(update).then(res => {
              if (res.success) {
                this.$message.success(`${info.file.name} 图片上传成功`)
                this.getDataByLibraryId(this.libraryData.id)
              } else {
                this.$message.error('图片上传失败：' + res.message)
              }
            })
          } else {
            this.$message.error(res.message)
          }
        } else if (info.file.status === 'removed') {
          let update = {}
          update.id = record.id
          update.attachPicture = JSON.stringify(info.fileList)
          updateMIAttachVersion(update).then(res => {
            if (res.success) {
              this.$message.success(`${info.file.name} 图片删除成功`)
              this.getDataByLibraryId(this.libraryData.id)
            } else {
              this.$message.error('图片删除失败：' + res.message)
            }
          })
        } else if (info.file.status === 'error') {
          this.$message.error(`${info.file.name} 图片上传失败`);
        }
      },
      deleteMiAttachVersion(id) {
        updateMIAttachVersion({ id: id, status: 2 }).then(() => this.getDataByLibraryId(this.libraryData.id))
      },
      addMIAttachVersion(data) {
        let params = {}
        params.libraryId = data.id
        insertMIAttachVersion(params).then(() => this.getDataByLibraryId(this.libraryData.id))
      },
      updateData(event, record, column) {
        //修改时禁止输入
        this.disableWhenUpdating()
        let param = {}
        param[column] = event.target.value
        param['id'] = record.id
        updateMIAttachVersion(param).then((res) => {
          this.$nextTick(() => {
            if (res.success) {
              this.$message.success('保存成功')
            } else {
              this.$message.error(res.message)
            }
            this.enableWhenUpdated()
          })
        })
      },
      updateLibData(event, record, column) {
        //修改时禁止输入
        this.disableWhenUpdating()
        let param = {}
        param[column] = event.target.value
        param['id'] = record.id
        updateMIStandardLib(param).then((res) => {
          this.$nextTick(() => {
            if (res.success) {
              this.$message.success('保存成功')
            } else {
              this.$message.error(res.message)
            }
            this.enableWhenUpdated()
          })
        })
      },
      getDataByLibraryId(libraryId) {
        getMIAttachVersionList({ libraryId: libraryId, status: 2 }).then(res => {
            this.resultData = res.data
            this.resultData.forEach(item => {
              if (!item.attachPicture) {
                item.attachPicture = []
              } else {
                item.attachPicture = JSON.parse(item.attachPicture)
                // console.log('item.attachPicture',item.attachPicture)
              }
            })
            // console.log('this.resultData:' , this.resultData)
          // console.log('this.resultData:::::::::::',this.resultData)
          this.loadingPicDesc(this.resultData)
        })
      },
      loadingPicDesc(res) {
        $(document).ready(function() {
          // 加载事件或者元素
          let $row = $("#attachPictureId > span > div.ant-upload-list") // 获取每行第二列即附图列
          let rowSize = $row.length
          $row.each(function(index, element) {
            $(element).attr("id","rowItem" + index)
          })
          for (let i = 0; i < rowSize; i++) { // 每行
            let $imgDivs = $("#rowItem" + i).find("div.ant-upload-list-picture-card-container > span > div") // 获取每行第二列即附图列的所有img
            // console.log('获取每行第二列即附图列的所有img:',$imgDivs)
            $imgDivs.each(function(index, element) {
              if ($(element).find("#inputItem").length === 0) {
                let div = $(element).append("<div id='inputItem' style='margin-top: 15px'></div>")
                $(div).find("#inputItem").attr("id","inputItem")
              }
            })
            let $inputItem = $imgDivs.find("#inputItem") // 获取每行图片下面的所有input的上层div
            $inputItem.each(function(index, element) {
              if ($(element).find("input").length === 0) {
                let $input = $(element).append("<input/>").find("input").attr("id","input" + index)
                $input.val(res[i].attachPicture[index].picDesc)
                $input.change(function () {
                  res[i].attachPicture[index].picDesc = $input.val()
                  //修改时禁止输入
                  $("input").attr("disabled",true)
                  $("textarea").attr("disabled",true)
                  let param = {}
                  let lastItem = res[i].attachPicture[res[i].attachPicture.length - 1];
                  if (lastItem.response) { // 因为新增图片后去触发另一个input的change事件时，res是旧的，所以需要手动修改一下参数
                    let targetImgs = [...res[i].attachPicture].slice(-1);
                    let finallyImgs = [...res[i].attachPicture].slice(0,-1)
                    finallyImgs.push({
                      uid: targetImgs[0].uid,
                      name: targetImgs[0].name,
                      picDesc: targetImgs[0].picDesc,
                      status: targetImgs[0].status,
                      url: process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id=' + targetImgs[0].response.data.id,
                    })
                    res[i].attachPicture = finallyImgs
                    param['attachPicture'] = JSON.stringify(res[i].attachPicture)
                    param['id'] = res[i].id
                  } else {
                    console.log('$(element)',$inputItem)
                    res[i].attachPicture.forEach(function (item, i) {
                      console.log('$(element).find("#input" + ' + i + ').val()',$inputItem.find("#input" + i).val())
                      item.picDesc = $inputItem.find("#input" + i).val()
                    })
                    param['attachPicture'] = JSON.stringify(res[i].attachPicture)
                    param['id'] = res[i].id
                  }
                  updateMIAttachVersion(param).then((res) => {
                      if (res.success) {
                        message.success('保存成功')
                      } else {
                        message.error(res.message)
                      }
                    $("input").attr("disabled",false)
                    $("textarea").attr("disabled",false)
                  })
                })
              }
            })
          }
        });
      },
      disableWhenUpdating() {
        $("input").attr("disabled",true)
        $("textarea").attr("disabled",true)
      },
      enableWhenUpdated() {
        $("input").attr("disabled",false)
        $("textarea").attr("disabled",false)
      }
    }
  }
</script>
<style lang="less" scoped>
/deep/.ant-table-tbody > tr > td {
  padding: 16px 16px;
}
/deep/.ant-table-bordered .ant-table-footer > table {
  border-collapse: collapse;
  border-spacing: 0;
  margin-top: -2px;
}
/deep/.ant-table-bordered .ant-table-footer > table > tr > td {
  border: 1px solid black;
}
/deep/.ant-table-thead > tr > th {
  background: white;
  /*padding: 12px 8px;*/
  font-weight: bold;
  border: 1px solid black;
}
/deep/.ant-table-bordered .ant-table-tbody > tr > td {
  border: 1px solid black;
}
/deep/.ant-table-bordered .ant-table-body > table {
  border-collapse: collapse;
  border-spacing: 0;
}
/deep/.ant-table-bordered .ant-table-header > table {
  border-collapse: collapse;
  border-spacing: 0;
}
/deep/.ant-table-thead > tr > th {
  border-bottom: 0;
  border-top: 0;
}
/deep/.ant-table-bordered .ant-table-title > table {
  border-collapse: collapse;
  border-spacing: 0;
}
/deep/.ant-table-bordered .ant-table-title > table > tr > td {
  border: 1px solid black;
}
/deep/.ant-table-bordered .ant-table-footer > table {
  border-collapse: collapse;
  border-spacing: 0;
}
/deep/.ant-table-bordered .ant-table-footer > table > tr > td {
  border: 1px solid black;
}
/deep/.ant-upload-list-picture-card-container {
  width: 280px;
  height: 150px;
}
/deep/.ant-upload-list-picture-card .ant-upload-list-item {
  width: 280px;
  height: 120px;
}
/deep/.avatar-uploader > .ant-upload {
  width: 280px;
  height: 120px;
}
#iconPlusTd {
  border-top: #e8e8e8 solid 0px;
}
#develop {
  font-size: 12px;
  margin: 0px 30px 20px 30px;
  color: #000;
}
/* you can make up upload button and sample style by using stylesheets */
/deep/.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

/deep/.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}

textarea.ant-input {
  max-width: 100%;
  height: auto;
  min-height: 32px;
  line-height: 1.5;
  vertical-align: bottom;
  -webkit-transition: all 0.3s, height 0s;
  transition: all 0.3s, height 0s;
  border: none;
}

.deleteIcon {
  display: inline-block;
  margin-bottom: 35px;
}
// /deep/.ant-breadcrumb a, .ant-breadcrumb span {
//   color: black;
//   font-weight: bold;
// }

// /deep/.ant-breadcrumb > span:last-child {
//   color: black;
//   font-weight: bold;
// }

// /deep/.ant-breadcrumb-separator {
//   color: black;
//   font-weight: bold;
// }
/deep/.ant-table-bordered.ant-table-empty .ant-table-placeholder {
  border: 1px solid black;
}
/deep/.ant-form-item {
    margin-bottom: 0px;
  }
  .renderTr td{
    border: #e8e8e8 solid 1px;
    text-align: center;
  }
  /deep/.ant-table.ant-table-bordered .ant-table-title {
     padding-right: 0px;
     padding-left: 0px;
    border: 1px solid #e8e8e8;
  }
  /deep/.ant-table-title {
    position: relative;
    padding: 0px;
    border-radius: 2px 2px 0 0;
  }
  /deep/.ant-table-footer {
    position: relative;
    padding: 0px;
    border-radius: 2px 2px 0 0;
    background-color: white;
  }
  /deep/.ant-table-footer tr td {
    border-right: #e8e8e8 solid 1px;
    border-top: #e8e8e8 solid 1px;
    text-align: center;
  }
  input {
    width: 100%;
    height: 25px;
    margin: 0;
    border: 0;
    outline: none;
    text-align: center;
  }
</style>
