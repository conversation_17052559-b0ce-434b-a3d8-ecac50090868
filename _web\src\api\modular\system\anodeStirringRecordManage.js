import { axios } from '@/utils/request'

export function getAnodeStirringRecordList (parameter) {
  return axios({
    url: '/anodeStirringRecord/getAnodeStirringRecordList',
    method: 'post',
    data: parameter
  })
}

export function insertAnodeStirringRecord (parameter) {
  return axios({
    url: '/anodeStirringRecord/insertAnodeStirringRecord',
    method: 'post',
    data: parameter
  })
}

export function updateAnodeStirringRecord (parameter) {
  return axios({
    url: '/anodeStirringRecord/updateAnodeStirringRecord',
    method: 'post',
    data: parameter
  })
}
