import { axios } from '@/utils/request'




export function checkRecordGetListByBusinessId(parameter) {
  return axios({
    url: '/batteryDesignCheckRecord/getListByBusinessId',
    method: 'post',
    data: parameter,
  })
}

export function checkRecordSubmitCheck(parameter) {
  return axios({
    url: '/batteryDesignCheckRecord/submitCheck',
    method: 'post',
    data: parameter,
  })
}

export function checkRecordGetRelationByRecordId(parameter) {
  return axios({
    url: '/batteryDesignCheckRecord/getRelationByRecordId',
    method: 'post',
    data: parameter,
  })
}



export function batteryDesignRoleAdd(parameter) {
  return axios({
    url: '/batteryDesignRole/add',
    method: 'post',
    data: parameter,
  })
}


export function batteryDesignRoleList(parameter) {
  return axios({
    url: '/batteryDesignRole/list',
    method: 'post',
    data: parameter,
  })
}




export function batteryDesignRoleDelete(parameter) {
  return axios({
    url: '/batteryDesignRole/delete',
    method: 'post',
    data: parameter,
  })
}


