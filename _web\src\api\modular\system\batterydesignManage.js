import { axios } from '@/utils/request'


export function getBatteryDesignPage (parameter) {
  return axios({
    url: '/batteryDesign/page',
    method: 'get',
    params: parameter
  })
}

export function getBatteryDesign (parameter) {
  return axios({
    url: '/batteryDesign/get',
    method: 'get',
    params: parameter
  })
}


export function getBatteryDesignCheckPage (parameter) {
  return axios({
    url: '/batteryDesign/checkPage',
    method: 'get',
    params: parameter
  })
}


export function sysBatteryDesignAdd (parameter) {
  return axios({
    url: '/batteryDesign/add',
    method: 'post',
    data: parameter
  })
}

export function getAuth () {
  return axios({
    url: '/batteryDesign/getAuth',
    method: 'post'
  })
}


export function sysBatteryDesignSubmit (parameter) {
  return axios({
    url: '/batteryDesign/submit',
    method: 'post',
    data: parameter
  })
}

export function sysBatteryDesignRefuse (parameter) {
  return axios({
    url: '/batteryDesign/refuse',
    method: 'post',
    data: parameter
  })
}

export function sysBatteryDesignPass (parameter) {
  return axios({
    url: '/batteryDesign/pass',
    method: 'post',
    data: parameter
  })
}


export function sysBatteryDesignEdit (parameter) {
  return axios({
    url: '/batteryDesign/edit',
    method: 'post',
    data: parameter
  })
}


export function sysBatteryDesignCopy (parameter) {
  return axios({
    url: '/batteryDesign/copy',
    method: 'post',
    data: parameter
  })
}

export function getBom (parameter) {
  return axios({
    url: '/batteryDesignBom/getByBatteryId',
    method: 'post',
    data: parameter
  })
}
export function addBom (parameter) {
  return axios({
    url: '/batteryDesignBom/add',
    method: 'post',
    data: parameter
  })
}
export function updateBom (parameter) {
  return axios({
    url: '/batteryDesignBom/update',
    method: 'post',
    data: parameter
  })
}

export function addBomDetail (parameter) {
  return axios({
    url: '/batteryDesignBomDetail/add',
    method: 'post',
    data: parameter
  })
}
export function updateBomDetail (parameter) {
  return axios({
    url: '/batteryDesignBomDetail/update',
    method: 'post',
    data: parameter
  })
}


export function exportBom(parameter) {
  return axios({
    url: '/batteryDesignBom/export',
    method: 'get',
    params: parameter,
  })
}


export function exportBom2(parameter) {
  return axios({
    url: '/batteryDesignBom/export2',
    method: 'get',
    params: parameter,
    responseType: 'blob'
  })
}

export function batteryDesignRelationSorList(parameter) {
  return axios({
    url: '/batteryDesignRelation/sorList',
    method: 'post',
    data: parameter,
  })
}

export function batteryDesignRelationDesignList(parameter) {
  return axios({
    url: '/batteryDesignRelation/designList',
    method: 'post',
    data: parameter,
  })
}

export function copySor(parameter) {
  return axios({
    url: '/batteryDesignRelation/copySor',
    method: 'post',
    data: parameter,
  })
}

export function copyDesign(parameter) {
  return axios({
    url: '/batteryDesignRelation/copyDesign',
    method: 'post',
    data: parameter,
  })
}

export function batteryDesignRelationOnlyDesignList(parameter) {
  return axios({
    url: '/batteryDesignRelation/onlyDesignList',
    method: 'post',
    data: parameter,
  })
}

export function batteryDesignRelationAddExportDesign(parameter) {
  return axios({
    url: '/batteryDesignRelation/addExportDesign',
    method: 'post',
    data: parameter,
  })
}

export function batteryDesignRelationUpdate(parameter) {
  return axios({
    url: '/batteryDesignRelation/update',
    method: 'post',
    data: parameter,
  })
}

export function batteryDesignRelationUpdateByInBatteryId(parameter) {
  return axios({
    url: '/batteryDesignRelation/updateByInBatteryId',
    method: 'post',
    data: parameter,
  })
}

export function addSor(parameter) {
  return axios({
    url: '/batteryDesign/addSor',
    method: 'post',
    data: parameter,
  })
}

export function addDesign(parameter) {
  return axios({
    url: '/batteryDesign/addDesign',
    method: 'post',
    data: parameter,
  })
}

export function designStandardCheckAddOrUpdate(parameter) {
  return axios({
    url: '/batteryDesignStandardCheck/addOrUpdate',
    method: 'post',
    data: parameter,
  })
}

export function getDesignStandardCheck(parameter) {
  return axios({
    url: '/batteryDesignStandardCheck/get',
    method: 'post',
    data: parameter,
  })
}

export function canBeUpdate(parameter) {
  return axios({
    url: '/batteryDesign/canUpdate',
    method: 'post',
    data: parameter,
  })
}

/* 
export function sysAppDelete (parameter) {
  return axios({
    url: '/sysApp/delete',
    method: 'post',
    data: parameter
  })
} */

