<template>
<div>
    <div>
        <a-breadcrumb class="breadcrumb" separator=">">
          <a-breadcrumb-item><a @click="gotoIndex($route.query.dept2 != null?-3:$route.query.dept!=null?-2:-1)">首页看板</a></a-breadcrumb-item>

          <a-breadcrumb-item v-if="$route.query.dept != null"><a @click="gotoIndex($route.query.dept2 != null?-2:-1)" >产品风险数据看板</a></a-breadcrumb-item>

          <a-breadcrumb-item v-if="$route.query.dept == null">产品风险数据看板</a-breadcrumb-item>

          <a-breadcrumb-item v-if="$route.query.dept2 != null && $route.query.dept != null"><a @click="gotoIndex(-1)">{{$route.query.dept}}</a></a-breadcrumb-item>

          <a-breadcrumb-item v-if="$route.query.dept2 == null && $route.query.dept != null">{{$route.query.dept}}</a-breadcrumb-item>

          <a-breadcrumb-item v-if="$route.query.dept2 != null">{{$route.query.dept2}}</a-breadcrumb-item>

        </a-breadcrumb>
    </div>
    <div :style="`height:${windowHeight}px;overflow-y: scroll;`">
        <div class="board">
            <div class="col1">
                <div class="item">
                    <div class="head">风险关闭率</div>
                    <div class='chart_table' ref="chart1"></div>
                </div>
            </div>
            <div style="width: 20%">
                <div class="item">
                    <div class="head">高风险项数量</div>
                    <div class='chart_table' style="color: rgba(240,150,150,1);font-family: SourceHanSansSC;
    font-weight: 900;
    font-size: 172px;text-align: center">{{sNum}}</div>
                </div>
            </div>
            <div class="col1">
                <div class="item">
                    <div class="head">风险等级分布</div>
                    <div class='chart_table' ref="chart2"></div>
                </div>
            </div>
        </div>
        <div class="board">
            <div :style="haveA?{width: '40%'}:{width: 'calc(60% + 8px)',marginRight: '-8px'}">
                <div class="item">
                    <div class="head">风险管理进展</div>
                    <div class='chart_table' ref="chart3"></div>
                </div>
            </div>
          <!--<div style="width: 20%" v-if="haveA">
            <div class="item">
              <div class="head">A类问题数量</div>
              <div class='chart_table' style="color: rgba(240,150,150,1);font-family: SourceHanSansSC;
    font-weight: 900;
    font-size: 172px;text-align: center">{{aNum}}</div>
            </div>
          </div>-->

            <div class="col1">
                <div class="item">
                    <div class="head">各类风险分布</div>
                    <div class='chart_table' ref="chart4"></div>
                </div>
            </div>
        </div>
      <div class="board">
        <div class="item1">

                    <div class="head">高风险风险清单概况</div>
        <a-table :rowKey="(record) =>record.issueId" :pagination="false"
                 style="padding-top: 8px;"
                 :data-source="highList" :columns="columns" size="small">
                <span slot="mstatus" slot-scope="text">
                    {{ 'product_stage_status' | dictType(text) }}
                </span>
          <span slot="state" slot-scope="text">
                    {{ 'product_state_status' | dictType(text) }}
                </span>
        </a-table>
        </div>

      </div>

    </div>
</div>
</template>

<script>
import { getProjectProcessDetail } from "@/api/modular/system/chartManage"
import {
  getStageRisk
} from "@/api/modular/system/report"

import {

  getCateTree
} from "@/api/modular/system/topic"

import _ from 'lodash'
import Vue from "vue";
import {DICT_TYPE_TREE_DATA} from "../../../store/mutation-types";
import {mapActions, mapGetters} from "vuex";
export default {
    data(){
        return{
            windowHeight: document.documentElement.clientHeight - 70,
            //column_map:[],
            projectProcessTotal:{},
            projectProcessTotalAxis:[],
            data:[
            ],
            deptFilters:[],
            haveA:false,
            cateFilters:[],
            nameFilters:[],
            pdFilters:[],
            rpmFilters:[],
            custFilters:[],
            dateFilters:[],
            dayFilters:[],
            depts:[],
            source :[],
            sourceType :[],
            deptsName:[],
            productDeptsName:[],
            highList:[],
            closePer:[],
            sNum:0,
            aNum:0,

          /**
           * 问题分类(产品)        stage_problem_category
           问题等级        stage_problem_level
           问题状态        stage_problem_status
           */
          stage_problem_category:[],
          stage_problem_level:[],
          stage_problem_level_children:[],
          stage_problem_status:[],
          bar1:[],
          bar2:[],
          dataAfterHandle:[],
            columns:[
              {
                title: '序号',
                dataIndex: 'seq',
                align:'center',
                width: 50,
                customRender: (text, record, index) => (<span>{index+1}</span>)
              },
              {
                title:'问题编号',
                dataIndex:'dept',
                align:'center',
              },
              {
                title:'问题状态',
                dataIndex:'problemStatus',
                align:'center',
                customRender: (text, record, index) =>
                {


                 var result = this.stage_problem_status.children.find((item,i) => {
                    return text == item.code
                  })

                  return result != null  ?result.name:''
                }

              }, {
                title:'发生日期',
                dataIndex:'findDate',
                align:'center'
              },{
                title:'提出人',
                dataIndex:'presenterName',
                align:'center'
              },{
                title:'问题分类',
                dataIndex:'problemCategories',
                align:'center',
                customRender: (text, record, index) =>
                {


                  var result = this.stage_problem_category.children.find((item,i) => {
                    return text == item.code
                  })

                  return result != null  ?result.name:''

                }
              },{
                title:'问题等级',
                dataIndex:'problemLevel',
                align:'center',
                customRender: (text, record, index) =>
                {


                  var result = this.stage_problem_level.children.find((item,i) => {
                    return text == item.code
                  })

                  return result != null  ?result.name:''
                }
              },{
                title:'问题描述',
                dataIndex:'problemDescription',
                align:'center'
              },
              {
                title:'责任人',
                dataIndex:'responsiblePersonName',
                align:'center'
              },

              {
                title:'计划完成时间',
                dataIndex:'plannedCompletionDate',
                align:'center'
              },

            ]
        }
    },
    methods: {
        gotoIndex(index){
            this.$router.go(index)
        },
        handleSearch(selectedKeys, confirm, dataIndex) {
        confirm();
            //this.searchText = selectedKeys[0];
            //this.searchedColumn = dataIndex;
        },

        handleReset(clearFilters) {
            clearFilters();
            //this.searchText = '';
        },

        callGetDeptTree(){

          getCateTree({
            fieldName:'department'
          }).then((res)=>{
            if (res.success) {
              this.depts = res.data

              //二级看板
              if(this.$route.query.in == null && this.$route.query.dept != null){
                for (let i = 0; i < this.depts.length; i++) {
                  if(this.depts[i].title == this.$route.query.dept){
                    this.depts = this.depts[i].children
                    break
                  }
                }
              }


              this.deptsName = []
              this.productDeptsName = ['product']
              for (let i = 0; i < this.depts.length; i++) {
                this.deptsName.push(this.depts[i].title)
                this.productDeptsName.push(this.depts[i].title)
              }

            } else {
              this.$message.error('错误提示：' + res.message, 1)
            }
            this.confirmLoading = false
          }).catch((err) => {
            this.confirmLoading = false
            this.$message.error('错误提示：' + err.message, 1)
          });
      },
        initTotal(){
            let chart = this.echarts.init(this.$refs.total)
            chart.clear()
            const options = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                    type: 'shadow'
                    }
                },
                color: ['#91cc75', '#f09696', '#cacaca'],
                legend: {
                    top: '6px',
                    itemWidth: 8,
                    itemHeight: 8,
                    show:true
                },
                grid: {
                    left: '3%',
                    right: '6%',
                    top: '12%',
                    bottom:'8%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                },
                yAxis: {
                    type: 'category',
                    data: this.projectProcessTotalAxis
                },
                series: [{
                    name: '正常',
                    type: 'bar',
                    stack: 'total',
                    barWidth: 20,
                    emphasis: {
                        focus: 'series'
                    },
                    label: {
                        show: true,
                        color: '#fff'
                    },
                    data: this.projectProcessTotal['1']
                    },
                    {
                    name: '延期',
                    type: 'bar',
                    stack: 'total',
                    barWidth: 20,
                    emphasis: {
                        focus: 'series'
                    },
                    label: {
                        show: true,
                        color: '#fff'
                    },
                    data: this.projectProcessTotal['2']
                    },
                    {
                    name: '暂停',
                    type: 'bar',
                    stack: 'total',
                    barWidth: 20,
                    emphasis: {
                        focus: 'series'
                    },
                    label: {
                        show: true,
                        color: '#fff'
                    },
                    data: this.projectProcessTotal['3']
                    }
                ]
            }
            chart.setOption(options)
            chart.resize()
        },
      switchApp () {
        const applicationData = this.userInfo.apps.filter(item => item.code === 'system')
        this.MenuChange(applicationData[0]).then((res) => {
        }).catch((err) => {
          this.$message.error('应用切换异常')
        })
      },
      ...mapActions(['MenuChange']),
        initChrat1(){

          let that = this

            let chart = this.echarts.init(this.$refs.chart1)
            chart.clear()
            const options = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                    type: 'shadow'
                    }
                },
                color: ['rgb(137, 215, 127)', 'rgb(247, 231, 113)', 'rgb(172, 209, 247)'],
                legend: {
                    top: '6px',
                    itemWidth: 8,
                    itemHeight: 8,
                    show:true
                },
                grid: {
                    left: '3%',
                    right: '6%',
                    top: '12%',
                    bottom:'8%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                  axisLabel: {
                    formatter: (value, index) => {
                      return value+"%";
                    }
                  }
                },
                yAxis: {
                    type: 'category',
                    data: this.deptsName
                },
                series: [{
                    type: 'bar',
                    stack: 'total',
                    barWidth: 20,
                    emphasis: {
                        focus: 'series'
                    },
                    label: {
                        show: true,
                        color: '#fff',
                        formatter:'{c}%'
                    },
                    data: this.closePer
                    }
                ]
            }
            chart.setOption(options)

          chart.on('click', function(params) {
            //that.switchApp()

            let query= {}
            if(that.$route.query.dept != null){

              return
              query.dept = that.$route.query.dept
              query.dept2 = params.name

            }else{
              query.dept = params.name
            }

              that.$router.push({
                path:'/project_problem_level2',
                query:query})

            return
          });
            chart.resize()
        },
        initChrat2(){
            let chart = this.echarts.init(this.$refs.chart2)

          let series = []
          for (let i = 0; i < this.depts.length; i++) {
            series.push({ type: 'bar' })
          }


            chart.clear()
            const option = {
              color: ['rgb(137, 215, 127)', 'rgb(247, 231, 113)', 'rgb(172, 209, 247)','rgb(204, 204, 204)'],
              legend: {},
              tooltip: {},
              dataset: {
                dimensions: this.productDeptsName,
                source: this.source
              },
              xAxis: { type: 'category' },
              yAxis: {},
              // Declare several bar series, each will be mapped
              // to a column of dataset.source by default.
              series: series
            };
            chart.setOption(option)
            chart.resize()
        },
        initChrat4(){
            let chart = this.echarts.init(this.$refs.chart4)
          let series = []
          for (let i = 0; i < this.depts.length; i++) {
            series.push({ type: 'pie' })
          }
            chart.clear()
            const option = {
              color: ['rgb(137, 215, 127)', 'rgb(247, 231, 113)', 'rgb(172, 209, 247)','rgb(204, 204, 204)'],
              legend: {},
              tooltip: {},
              dataset: {
                dimensions: this.productDeptsName,
                source: this.sourceType
              },
              xAxis: { type: 'category' },
              yAxis: {},
              // Declare several bar series, each will be mapped
              // to a column of dataset.source by default.
              series: series
            };
            chart.setOption(option)
            chart.resize()
        },
        initChrat3(){
            let chart = this.echarts.init(this.$refs.chart3)
            chart.clear()
            const option = {
              color: ['rgb(137, 215, 127)', 'rgb(247, 231, 113)', 'rgb(172, 209, 247)'],
              tooltip: {
                trigger: 'axis',
                axisPointer: {
                  // Use axis to trigger tooltip
                  type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
                }
              },
              legend: {},
              grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
              },
              xAxis: {
                type: 'value'
              },
              yAxis: {
                type: 'category',
                data: this.deptsName
              },
              series: [
                {
                  name: '正常',
                  type: 'bar',
                  stack: 'total',
                  label: {
                    show: true
                  },
                  emphasis: {
                    focus: 'series'
                  },
                  data: this.bar1
                },
                {
                  name: '延期',
                  type: 'bar',
                  stack: 'total',
                  label: {
                    show: true
                  },
                  emphasis: {
                    focus: 'series'
                  },
                  data:this.bar2
                }
              ]
            };
            chart.setOption(option)
            chart.resize()
        },
        callProjectProcessDetail() {



          getCateTree({
            fieldName:'department'
          }).then((res)=>{

            let dict_data = Vue.ls.get(DICT_TYPE_TREE_DATA);
            /**
             * 问题分类(产品)        stage_problem_category
             问题等级        stage_problem_level
             问题状态        stage_problem_status
             */
            for (let i = 0; i < dict_data.length; i++) {
              if(dict_data[i].code == 'stage_risk_category'){
                this.stage_problem_category = dict_data[i]
              }
              if(dict_data[i].code == 'stage_risk_level'){
                this.stage_problem_level = dict_data[i]
              }
              if(dict_data[i].code == 'stage_problem_status'){
                this.stage_problem_status = dict_data[i]
              }
            }

            this.stage_problem_level_children = this.stage_problem_level.children.reverse()



            if (res.success) {
              this.depts = res.data

              //二级看板
              if(this.$route.query.dept2 == null && this.$route.query.dept != null){
                for (let i = 0; i < this.depts.length; i++) {
                  if(this.depts[i].title == this.$route.query.dept){
                    this.depts = this.depts[i].children
                    break
                  }
                }
              }  //三级看板
              if(this.$route.query.dept2 != null && this.$route.query.dept != null){
                for (let i = 0; i < this.depts.length; i++) {
                  if(this.depts[i].title == this.$route.query.dept){
                    let depts = []
                    depts.push(this.depts[i])
                    this.depts = depts
                    break
                  }
                }
              }


              this.deptsName = []
              this.productDeptsName = ['product']
              for (let i = 0; i < this.depts.length; i++) {
                this.deptsName.push(this.depts[i].title)
                this.productDeptsName.push(this.depts[i].title)
              }
              getStageRisk({
                issueId: 0
              })
                .then((res) => {

                  this.highList = []
                  for (let i = 0; i < res.data.length; i++) {
                    if(res.data[i].problemLevel == '1' || res.data[i].problemLevel == '2'){


                      if(this.$route.query.dept != null){
                        let depart = res.data[i].departmentCateList

                        for (let j = 0; j < depart.length; j++) {
                          if(depart[j].value == this.$route.query.dept){
                            this.highList.push((res.data[i]))
                          }
                        }
                      }else{
                        this.highList.push((res.data[i]))
                      }

                    }


                  }

                  this.dataAfterHandle = []
                  this.closePer = []
                  this.bar1 = []
                  this.bar2 = []
                  for (let i = 0; i < this.depts.length; i++) {
                    let data = {}
                    let all = 0;
                    let close = 0;
                    let closePer = null;
                    let normalNum = 0
                    let unNormalNum = 0

                    data.id = this.depts[i].value
                    data.name = this.depts[i].title



                    for (let j = 0; j < res.data.length; j++) {

                      if(res.data[j].departmentCateList.length > 0){
                        if(this.depts[i].value == res.data[j].departmentCateList[0].id || this.depts[i].value == res.data[j].departmentCateList[1].id){
                          all++
                          //问题关闭
                          if(res.data[j].problemStatus == 5 || res.data[j].problemStatus == 3){
                            close++;
                          }




                          let plan = new Date(res.data[j].plannedCompletionDate)


                          if(null == res.data[j].actualCompletionDate || '' == res.data[j].actualCompletionDate){
                              unNormalNum++
                          }else{
                            let act = new Date(res.data[j].actualCompletionDate)

                            if(plan < act){
                              unNormalNum++
                            }else {
                              normalNum++
                            }

                          }

                        }
                      }


                    }

                    this.bar1.push(normalNum)
                    this.bar2.push(unNormalNum)

                    data.all = all
                    data.close = close
                    if(all != 0){
                      data.closePer = (close/all*100).toFixed(0)
                      this.closePer.push((close/all*100).toFixed(0))
                    }else{
                      this.closePer.push(0)
                    }
                    this.dataAfterHandle.push(data)
                  }

                  this.sNum = 0
                  this.aNum = 0

                  if(this.$route.query.dept == null){
                    for (let i = 0; i < res.data.length; i++) {
                      if(res.data[i].problemLevel == 1){
                        this.sNum++
                      }
                    }

                  }else{
                    for (let k = 0; k < res.data.length; k++) {


                        if(this.$route.query.dept == res.data[k].departmentCateList[0].value || this.$route.query.dept == res.data[k].departmentCateList[1].value){
                          if(res.data[k].problemLevel == 1){
                            this.sNum++
                          }
                          if(res.data[k].problemLevel == 2){
                            this.aNum++
                          }
                        }

                    }
                  }




                  this.source = []


                  for (let i = 0; i < this.stage_problem_level_children.length; i++) {
                      let level = []
                      let dict = this.stage_problem_level_children[i]
                      level.push(dict.name)

                    for (let j = 0; j < this.depts.length; j++) {
                      let num = 0
                      for (let k = 0; k < res.data.length; k++) {
                        if(res.data[k].departmentCateList.length > 0){
                          if(this.depts[j].value == res.data[k].departmentCateList[0].id || this.depts[j].value == res.data[k].departmentCateList[1].id){

                            if(res.data[k].problemLevel == dict.code){
                              num++
                            }
                          }
                        }
                      }
                      level.push(num)
                    }


                    this.source.push(level)


                  }

                  this.sourceType = []

                  for (let i = 0; i < this.stage_problem_category.children.length; i++) {
                      let level = []
                      let dict = this.stage_problem_category.children[i]
                      level.push(dict.name)

                    for (let j = 0; j < this.depts.length; j++) {
                      let num = 0
                      for (let k = 0; k < res.data.length; k++) {
                        if(res.data[k].departmentCateList.length > 0){
                          if(this.depts[j].value == res.data[k].departmentCateList[0].id || this.depts[j].value == res.data[k].departmentCateList[1].id){

                            if(res.data[k].problemCategories == dict.code){
                              num++
                            }
                          }
                        }
                      }
                      level.push(num)
                    }


                    this.sourceType.push(level)


                  }

                  console.log(this.sourceType)
                  console.log(this.productDeptsName)



                  
                }).then(()=>{
                this.initChrat1()
                this.initChrat2()
                this.initChrat3()
                this.initChrat4()
              })






            } else {
              this.$message.error('错误提示：' + res.message, 1)
            }
            this.confirmLoading = false
          }).catch((err) => {
            this.confirmLoading = false
            this.$message.error('错误提示：' + err.message, 1)
          });










        },
    },

  computed: {
    ...mapGetters(['userInfo'])
  },

  mounted() {


      this.callProjectProcessDetail()


    }
}
</script>

<style lang="less" scoped=''>
    .board {
        display: flex;
        margin: auto;
        justify-content: center;
        
    }
    .head {
        background: #6c8adf;
        color: #fff;
        text-align: center;
        font-size: 24px;
        font-weight: bold;
    }
    .col1{
        width: 40%;
    }
    .col2{
        width: 60%;
    }
    .item {
        width: 98%;
        margin: 8px auto;
        border-radius: 8px;
        overflow: hidden;
        background: #fff;
        box-shadow: 2px 2px 3px #ccc;
    }.item1 {
        width: 99%;
        margin: 8px auto;
        border-radius: 8px;
        overflow: hidden;
        background: #fff;
        box-shadow: 2px 2px 3px #ccc;
    }
    
    .chart_table {
        width: 100%;
        height: 280px;
    }
    /deep/.ant-table-placeholder{
        padding: 0;
    }
    /deep/.ant-table-thead > tr > th {
      border-bottom: 1px solid #d2d4d7;
      border-right: 1px solid #d2d4d7;
      font-size: 13px;
      background: #e8e8e8 !important;
      color: #000;
      font-weight: bold;
    }
    /deep/.ant-table-tbody{
        background: #fff;
    }
    /deep/.ant-table-thead > tr > th .anticon-filter, /deep/.ant-table-thead > tr > th .ant-table-filter-icon{
        color: #fff;
    }
    /deep/.ant-table-thead > tr > th.ant-table-column-has-actions.ant-table-column-has-filters .anticon-filter.ant-table-filter-open, 
    /deep/.ant-table-thead > tr > th.ant-table-column-has-actions.ant-table-column-has-filters .ant-table-filter-icon.ant-table-filter-open,
    /deep/.ant-table-thead > tr > th.ant-table-column-has-actions.ant-table-column-has-filters:hover .anticon-filter:hover, 
    /deep/.ant-table-thead > tr > th.ant-table-column-has-actions.ant-table-column-has-filters:hover .ant-table-filter-icon:hover{
        background: #fff;
        color: rgb(201, 201, 201);
    }
    /deep/.ant-checkbox-group-item{
        display: block;
        margin: 0 8px;
    }
    
    .breadcrumb{
    padding: 5px 0;
    }.ant-breadcrumb a{
    color:#5d90fa !important;
    }.ant-breadcrumb{
    font-size: 12px !important;
    }
</style>