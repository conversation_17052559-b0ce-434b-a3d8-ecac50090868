<template>
  <a-modal :title="title" :width="1000" :visible="visible" @cancel="handleCancel" @ok="handleCancel" :confirmLoading="confirmLoading">
    <s-table ref="table" :columns="columns" :data="loadData" :alert="false" :rowKey="(record) => record.id">
      <template slot="pushStatus" slot-scope="text,record">
        <a  @click="openProcess(record)" @dblclick="toThird(record)">{{showStatus(text,record)}}</a>
      </template>

      <template slot="url" slot-scope="text,record">
        <a   @click="toThird(record)">{{record.type >= 0 ? 'JIRA':(record.stage == 'A样' || record.stage == 'B样' ? 'JIRA':'OA')}}</a>
      </template>

      <template slot="sys" slot-scope="text,record">
        <a  @click="openPdf(record)">{{showSys(text,record)}}</a>
      </template>

    </s-table>
  </a-modal>
</template>

<script>
  import {
    bomPushList
  } from "@/api/modular/system/bomManage"
  import {
    STable
  } from '@/components'
  export default {
    components: {
      STable
    },
    data() {
      return {
        title: '提交记录',
        queryParam: {},
        visible: false,
        classType:null,
        confirmLoading: false,
        loadData: parameter => {
          return bomPushList(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        columns: [{
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            width: 50,
            customRender: (text, record, index) => `${index+1}`,
          }, {
            title: '项目名称',
            dataIndex: 'name'
          },{
            title: '文件类型',
            dataIndex: 'type',
            customRender: (text, record, index) => {
              /**
               *0:MI  1图纸  2特殊属性清单  3测试验证
               */
              if(record.type == -1 || record.type == null ){
                return 'BOM'
              }
              if(record.type == 0 ){
                return 'MI'
              }
              if(record.type == 1){
                return '图纸'
              }
              if(record.type == 2){
                return '特殊属性清单'
              }
              if(record.type == 3){
                return '测试验证'
              }
              if(record.type == 4){
                return '产品规格书'
              }

              return "BOM"


            }
          }, {
          title: '提交人',
          dataIndex: 'submitter'
        },
          {
            title: '操作类型',
            dataIndex: 'operateType',
            customRender: (text, record, index) => record.bomType + " "+ text,
          },
          {
            title: '样品阶段',
            dataIndex: 'stage'
          },
          {
            title: '状态',
            dataIndex: 'pushStatus',
            scopedSlots: {
              customRender: 'pushStatus'
            }
            /*customRender: (text, record, index) =>{
              //推送状态 0 提交审批  2 OA已审核 3 已提交SAP 4 JIRA已受控
              /!*if(nodeItem._cfg.model.label == 'JIRA'){

                window.open('http://10.2.72.88:2990/jira/browse/' + nodeItem._cfg.model.url, "_blank");
              }
              if(nodeItem._cfg.model.label == 'OA'){

                window.open('https://oa.evebattery.com/km/review/km_review_main/kmReviewMain.do?method=view&fdId=' + nodeItem._cfg.model.url, "_blank");
              }*!/

            }*/
          },

          {
            title: '审核地址',
            dataIndex: 'url',
            scopedSlots: {
              customRender: 'url'
            }
          },
          {
            title: '创建时间',
            dataIndex: 'createTime'

          },
          {
            title: '结束时间',
            dataIndex: 'updateTime',
            customRender: (text, record, index) =>{
              if(record.pushStatus == 4 || record.pushStatus == 1){
                return text
              }else{
                return ''
              }
            }
          },{
            title: '受控文件',
            dataIndex: 'sys',
            scopedSlots: {
              customRender: 'sys'
            }
          },
        ]
      }
    },
    methods: {
      edit(record) {



        if(record.id == null){
          if(record.techStatus == -1){
            this.queryParam.bomId = record.bomId
            if(null == record.processId){
              this.classType = 'bom'
            }

          }else{
            this.classType = null == record.docId?record.classType:null
            this.queryParam.bomId = null != record.docId?record.docId:record.processId
          }


        }else{
          this.queryParam.bomId = record.id
        }


        this.queryParam.isOut = false
        this.visible = true
        setTimeout(() => {
          this.$refs.table.refresh()
        }, 100);

      },
      handleCancel() {
        this.visible = false
      },
      //推送状态 0 提交审批  2 OA已审核 3 已提交SAP 4 JIRA已受控
      /*if(nodeItem._cfg.model.label == 'JIRA'){


      }
      if(nodeItem._cfg.model.label == 'OA'){

        window.open('https://oa.evebattery.com/km/review/km_review_main/kmReviewMain.do?method=view&fdId=' + nodeItem._cfg.model.url, "_blank");
      }*/
      toJIRA(url){
        window.open('http://jira.evebattery.com/browse/' + url, "_blank");
      },

      toOA(url){
        window.open(process.env.VUE_APP_OA_URL+'/km/review/km_review_main/kmReviewMain.do?method=view&fdId=' + url, "_blank");
      },

      toThird(record){

        if(record.type>= 0){
          window.open('http://jira.evebattery.com/browse/' + record.url, "_blank");
        }else{
          if(record.stage == 'A样' || record.stage == 'B样'){
            window.open('http://jira.evebattery.com/browse/' + record.url, "_blank");
          }else{
            window.open(process.env.VUE_APP_OA_URL+'/km/review/km_review_main/kmReviewMain.do?method=view&fdId=' + record.url, "_blank");
          }
        }


      },

      openProcess(record){

        if(this.classType != null){
          if(this.classType == 'bom'){
            this.$parent.$parent.preview(record.bomId,record.processId)
          }else{
            this.$parent.preview(record.bomId,record.processId)
          }

        }else{
          if(record.bomType != '成品' && record.bomType != '包装' && record.bomType != '电芯'){
            this.$parent.$parent.preview(record.bomId,record.processId)
          }else{
            this.$parent.preview(record.bomId,record.processId)
          }
        }



      },

      openPdf(record){

        if(record.pushStatus == 4){


          if(this.classType == 'bom'){
            this.$parent.$parent.previewPdf(record.fileId)
          }else{

            if(null != this.classType){
              this.$parent.previewPdf(record.fileId)
            }else{
              if(record.bomType == '成品'){

                this.handleCancel()
                this.$emit("preview1", record.bomId);

              }else if(record.bomType != '包装' && record.bomType != '电芯'){
                this.$parent.$parent.previewPdf(record.fileId)
              }else{
                this.$parent.previewPdf(record.fileId)
              }
            }


          }




        }
      },

      showSys(text,record){



        if(record.pushStatus == 4){

          if(record.bomType == '成品'){
            return "预览文件"
          }

          return text
        }else{
          return ''
        }
      },

      showStatus(text,record){

        if(0 == text){

          if(record.type >= 0){
            return 'JIRA 待审核'
          }else{
            if(record.stage == 'A样' || record.stage == 'B样'){
              return 'JIRA 待审核'
            }else{
              return 'OA 待审核'
            }
          }


        }

        if(2 == text){
          return 'OA 已审核'
        }

        if(3 == text){
          return '已提交 SAP'
        }

        if(4 == text){
          return '已完成'
        }

        if(1 == text){
          return '已驳回'
        }

        if(6 == text){
          return '推送SAP失败'
        }

      }

    }
  }
</script>

<style>

</style>