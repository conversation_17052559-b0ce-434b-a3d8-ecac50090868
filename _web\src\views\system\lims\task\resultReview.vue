<template>
  <a-tabs type="card" @change="callback">
    <a-tab-pane v-if="showJMTab" key="HZ_YJ_DL_JM" tab="精密实验室">
      <div style="float: left;padding:15px 0px 10px 10px;width: 50%;">
        <a-row :gutter="[8,8]">
          <a-col :span="10">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="审核人"  has-feedback>
              <a-input style="z-index: 10" v-model="queryAuditMan" @keyup.enter="getReviewTestList" @change="getReviewTestList"/>
            </a-form-item>
          </a-col>
          <a-col :span="10">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="测试项目"  has-feedback>
              <a-input style="z-index: 10" v-model="queryTestName" @keyup.enter="getReviewTestList" @change="getReviewTestList"/>
            </a-form-item>
          </a-col>
          <a-col :span="10" style="margin-top: 10px">
              <a-button type="primary" style="z-index: 10" @click="beforeReviewSubmit">
                <a-popconfirm
                  title="确认提交？"
                  @confirm="reviewSubmit()"
                  :visible="beforeReviewSubmitFlag && laboratoryId === 'HZ_YJ_DL_JM'"
                  @cancel="() => beforeReviewSubmitFlag = false"
                  placement="topRight">
                  <a>提交</a>
                </a-popconfirm>
              </a-button>
              <a-button type="danger" style="margin-left:5px;z-index: 10" @click="beforeReviewSendBack">退回</a-button>
          </a-col>
        </a-row>
      </div>
      <div style="background-color: #FFFFFF;padding: 10px">
        <div class="box table-scroll" ref="box">
          <div class="mid">
            <a-spin :spinning="modalLoading">
            <a-table :columns="columns"
                     :dataSource="jmDataSource"
                     :expandedRowKeys="expandedRowKeys"
                     @expand="onTableExpand"
                     bordered
                     :rowKey="(record) => record.id"
                     :row-selection="{
                  selectedRowKeys: selectedRowKeys, selectedRows: selectedRows,
                  onSelectAll: onSelectAll,
                  onSelect:onSelect, columnWidth:40}"
                     ref="table2">
              <template
                slot="sorter"
                slot-scope="text, record, index, columns">
                <a v-if="record.children" @click="openData(record.folderid)" style="text-align: left" >
                  委托单号：{{text}} {{record.createdbyname}}
                </a>
                <p v-else style="text-align: center">{{ text }}</p>
              </template>
              <template
                slot="testcode"
                slot-scope="text, record, index, columns">
                <a @click="checkRecord(record)" style="text-align: left" >
                  {{record.testcode}}
                </a>
              </template>
            </a-table>
            </a-spin>
          </div>
          <!-- 精密实验室 -->
          <DetailsModal v-if="isShowModal" :modalData="modalData" @cancel="handleModalCancel" @refresh="refreshData"></DetailsModal>
          <a-modal title="退回" :width="350" :visible="beforeReviewSendBackFlag && laboratoryId === 'HZ_YJ_DL_JM'" @ok="reviewSendBack()" @cancel="cancelSendBack()">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="备注"  has-feedback>
              <a-textarea style="z-index: 10" v-model="reviewSendBackOpinion" auto-size/>
            </a-form-item>
          </a-modal>
        </div>

        <test-data ref="testData" @ok="handleOk"/>
      </div>
    </a-tab-pane>

<!--    <a-tab-pane key="HZ_YJ_DL_CS" tab="研发检测中心">-->
<!--      <div style="float: left;padding:10px 0px 10px 10px;">-->
<!--        <a-button type="primary" style="width:100px;z-index: 10" @click="assgin()">分配检测人</a-button>-->
<!--      </div>-->
<!--      <div class="box" ref="box">-->
<!--        <div class="mid">-->
<!--          <s-table :columns="columns"-->
<!--                   :data="loadData"-->
<!--                   bordered-->
<!--                   :scroll="{x: 2000}"-->
<!--                   :rowKey="(record) => record.id"-->
<!--                   :row-selection="{-->
<!--                  selectedRowKeys: selectedRowKeys, selectedRows: selectedRows,-->
<!--                  onSelectAll: onSelectAll,-->
<!--                  onSelect:onSelect, columnWidth:40}"-->
<!--                   ref="table2">-->
<!--            <template-->
<!--              slot="sorter"-->
<!--              slot-scope="text, record, index, columns">-->
<!--              <a v-if="record.children" @click="openData(record.folderid)" style="text-align: left" >-->
<!--                委托单号：{{text}} {{record.createdbyname}}-->
<!--              </a>-->
<!--              <p v-else style="text-align: center">{{ text }}</p>-->
<!--            </template>-->
<!--          </s-table>-->
<!--        </div>-->
<!--      </div>-->
<!--    </a-tab-pane>-->
  </a-tabs>

</template>
<script>
import {
  executeReviewSendBack,
  executeReviewSubmit,
  getLimsOrdtasksOfPreView, getTestPerson
} from '@/api/modular/system/limsManager'
  import { STable } from '@/components'
  import DetailsModal from "../../testProgress/workbench/components/detailsModal"
  import testData from '../folder/testData.vue'
import moment from "moment/moment";
import { mapGetters } from "vuex";

  export default {
    components: {
     STable,testData,DetailsModal
    },
    data() {
      return {
        reviewSendBackOpinion: null,
        showJMTab: false,
        modalLoading: false,
        jmDataSource: [],
        queryTestName: null,
        queryAuditMan: null,
        isShowModal: false,
        beforeReviewSubmitFlag: false,
        beforeReviewSendBackFlag: false,
        modalData: {},
        timeForm: this.$form.createForm(this,{ name: 'timeForm' }),
        height: 200,
        address: this.hasPerm('progress:all') ? 'all' : 'none',
        show: false,
        inBoxLabelCol: {
          sm: {
            span: 6
          }
        },
        inBoxWrapperCol: {
          sm: {
            span: 18
          }
        },
        labelCol: {
          sm: {
            span: 6
          }
        },
        wrapperCol: {
          sm: {
            span: 15
          }
        },
        queryParam: {},
        data: [],
        resultData: [],
        personResultData: [],
        headData: [],
        allAddress: null,
        personColumns: [
          {
            title: '序号',
            dataIndex: 'index',
            align: 'center',
            width: 30,
            ellipsis: true,
            customRender: (text, record, index) => `${index + 1}`
          },
          {
            title: '账号',
            dataIndex: 'ID',
            align: 'center',
            width: 40,
          },
          {
            title: '用户名称',
            dataIndex: 'USERNAME',
            align: 'center',
            width: 40,
          },
          {
            title: '单位名称',
            dataIndex: 'ORGNAME',
            align: 'center',
            width: 50,
          },
          {
            title: '在测测试项目数量',
            dataIndex: 'ORDTASKNUMBER',
            align: 'center',
            width: 50,
          },
          {
            title: '在测样品数量',
            dataIndex: 'ORDERNUMBER',
            align: 'center',
            width: 50,
          },
          {
            title: '在测委托单数量',
            dataIndex: 'FOLDERNUMBER',
            align: 'center',
            width: 50,
          },
        ],
        columns: [
          {
            title: '试验顺序',
            dataIndex: 'sorter',
            // align: 'center',
            width: 250,
            scopedSlots: { customRender: 'sorter' },
            // customRender: (text, record, index) => `${index + 1}`
          }, {
            title: '流程状态',
            width: 80,
            align: 'center',
            dataIndex: 'status',
            customRender: (text, record, index) => {
              switch (text) {
                case 'Examine': return '结果审核'
                case 'Outtestassign': return '委外测试任务分配'
                case 'Result_return': return '退回'
                case 'Draft': return '新建'
                case 'Preschedule': return '待排程'
                case 'Assign': return '任务分配'
                case 'Result': return '结果录入'
                case 'Review': return '结果复核'
                case 'Report': return '报告编制'
                case 'Done': return '完成'
                case 'Outsource': return '任务委外'
                case 'Backfill': return '数据回填'
                case 'Testreport': return '测试报告上传'
                case 'Herbaceousapprove': return '草本审核'
                case 'Herbaceousaudit': return '草本审批'
                case 'Originalupload': return '正本上传'
                case 'Originalapprove': return '正本审核'
                case 'Originalaudit': return '正本审批'
                case 'HerbaceousapproveReject': return '草本审核退回'
                case 'HerbaceousauditReject': return '草本审批退回'
                case 'OriginalapproveReject': return '正本审核退回'
                case 'Cancel': return '已取消'
                case 'Inreview': return '报告审核中'
                case 'OriginalauditReject': return '正本审批退回'
                case 'Cfmschedule': return '排程确认'
                case 'Testassign': return '测试任务分配'
                case "OrdtaskUnderChange": return '委托变更中'
                default: return ''
              }
          }
          },{
            title: '委托单号',
            width: 120,
            align: 'center',
            dataIndex: 'folderno',
          },{
            title: '测试项目编号',
            width: 100,
            align: 'center',
            dataIndex: 'testcode',
            scopedSlots: { customRender: 'testcode' },
          }, {
            title: '测试项目名称',
            width: 120,
            align: 'center',
            dataIndex: 'testname',
          }, {
            title: '测试项目别名',
            width: 120,
            align: 'center',
            dataIndex: 'alias',
          }, {
            title: '审核人',
            width: 120,
            align: 'center',
            dataIndex: 'otherProperties.auditor',
          }, {
            title: '标准编号',
            width: 70,
            align: 'center',
            dataIndex: 'methodcode',
          }, {
            title: '标准名称',
            width: 100,
            align: 'center',
            dataIndex: 'methodname',
          }, {
            title: '计划开始时间',
            width: 120,
            align: 'center',
            dataIndex: 'planstarttime',
          }, {
            title: '计划结束时间',
            width: 120,
            align: 'center',
            dataIndex: 'planendtime',
          }, {
            title: '检测内容',
            width: 200,
            align: 'center',
            dataIndex: 'testcontent',
          }, {
            title: '判定标准',
            width: 200,
            align: 'center',
            dataIndex: 'judgebasis',
          }, {
            title: '测试步骤',
            width: 150,
            align: 'center',
            dataIndex: 'teststep',
          }, {
            title: '备注',
            width: 100,
            align: 'center',
            dataIndex: 'remark',
          }
        ],
        selectedRowKeys: [],
        selectedRows: [],
        laboratoryId: null,
        expandedRowKeys: [],
      }
    },
    created() {
      if (this.userInfo.account === "superAdmin") {
        this.laboratoryId = "HZ_YJ_DL_JM"
        this.showJMTab = true
      } else {
        // "精密实验室-测试组长"角色才能看到【精密实验室】标签页
        let jmList = this.userInfo.roles.filter(item => item.id === "1712686842365419522")
        this.showJMTab = jmList.length > 0
        if (this.showJMTab) {
          this.laboratoryId = "HZ_YJ_DL_JM"
        }
      }
      this.getReviewTestList()
    },
    computed: {
      ...mapGetters(['userInfo'])
    },
    mounted() {

    },
    methods: {
      getReviewTestList () {
        this.modalLoading = true
        getLimsOrdtasksOfPreView({
          status: "Review",
          laboratoryId: this.laboratoryId,
          queryTestName: this.queryTestName,
          queryAuditMan: this.queryAuditMan
        }).then((res) => {
          this.resultData = res.data
          let parentData = JSON.parse(JSON.stringify(this.resultData))
          let folderNoSet = [...new Set(parentData.map(item => item.folderno))];
          this.resultData = []
          for (let i = 0; i < folderNoSet.length; i++) {
            this.resultData[i] = {
              id: folderNoSet[i],
              sorter: folderNoSet[i],
              isParent: true,
              children: []
            }
            parentData.forEach((item) => {
              if (item.folderno === folderNoSet[i]) {
                this.resultData[i].folderid = item.folderid
                this.resultData[i].createdbyname = item.createdbyname
                this.resultData[i].children.push(item)
              }
            })
          }
          // 默认展开所有行
          this.expandedRowKeys = folderNoSet
        }).then((res) => {
          this.jmDataSource = this.resultData
          this.modalLoading = false
        })
      },
      checkRecord (record) {
        console.log('record',record)
        this.modalData.ordTaskId = record.id
        this.modalData.testName = record.testname
        this.modalData.folderNo = record.folderno
        this.modalData.taskStatus = '已完成'
        this.modalData.remark = record.otherProperties.folderremark
        this.modalData.testParameter = record.testparameter
        this.modalData.review = true
        this.modalData.taskType = 'jmcs'
        this.isShowModal = true
      },
      refreshData() {
        this.clearSelected()
        this.getReviewTestList()
        this.isShowModal = false
      },
      handleModalCancel() {
        this.isShowModal = false
      },
      callback(key) {
        this.laboratoryId = key
        this.clearSelected()
        this.getReviewTestList()
      },
      clearSelected() {
        this.selectedRows = []
        this.selectedRowKeys = []
      },
      onSelectAll(selected, selectedRows, changeRows) {
        this.selectedRows = selectedRows
        this.selectedRowKeys = selectedRows.map(item => item.id)
      },
      onSelect(record, selected, selectedRows, nativeEvent) {
        let initSelectedRows = JSON.parse(JSON.stringify(selectedRows))
        let selectedRowKeys = initSelectedRows.map(item => item.id)
        this.selectedRowKeys = JSON.parse(JSON.stringify(selectedRowKeys))
        this.selectedRows = selectedRows
        let parentData = this.resultData
        let childData = []
        parentData.forEach(item => {
          item.children.forEach(res => {
            childData.push(res)
          });
        });
        if (selected) { // 判断是选择还是取消
          if (record.isParent) { // 判断是父项还是子项,父项：查询到对应的子项然后push
            let child = childData.filter(obj => obj.folderid === record.folderid);
            child.forEach(object => {
              if (!this.selectedRows.includes(object)) {
                this.selectedRows.push(object)
                this.selectedRowKeys.push(object.id)
              }
            })
          } else { // 如果是子项，判断selectedRows里兄弟项是否被全选中，全选中需要push父项
            let brother = childData.filter(obj => obj.folderid === record.folderid);
            let selectedRows = this.selectedRows.filter(obj => obj.folderid === record.folderid);
            if (brother.length === selectedRows.length) {
              let parent = parentData.filter(obj => obj.folderid === record.folderid);
              this.selectedRows.push(parent[0])
              this.selectedRowKeys.push(parent[0].id)
            }
          }
        } else {
          if (record.isParent) { // 判断是父项还是子项
            let child = childData.filter(obj => obj.folderid === record.folderid);
            child.forEach(object => {
              const indexRow = this.selectedRows.findIndex(data => data.folderid === object.folderid);
              const indexKey = this.selectedRowKeys.findIndex(data => data === object.id);
              if (indexRow > -1) {
                this.selectedRows.splice(indexRow, 1);
              }
              if (indexKey > -1) {
                this.selectedRowKeys.splice(indexKey, 1);
              }
            })
          } else { // 如果是子项，判断selectedRows里兄弟项是否被全选中，全选中需要splice父项
            let brother = childData.filter(obj => obj.folderid === record.folderid);
            let selectedRows = this.selectedRows.filter(obj => obj.folderid === record.folderid);
            if (brother.length === selectedRows.length) {
              let parent = parentData.filter(obj => obj.folderid === record.folderid);
              const indexRow = this.selectedRows.findIndex(data => data.folderid === parent[0].folderid);
              const indexKey = this.selectedRowKeys.findIndex(data => data === parent[0].id);
              if (indexRow > -1) {
                this.selectedRows.splice(indexRow, 1);
              }
              if (indexKey > -1) {
                this.selectedRowKeys.splice(indexKey, 1);
              }
            }
          }
        }
      },
      beforeReviewSendBack() {
        if (this.selectedRows.length === 0) {
          this.$message.warning('请至少选择一条数据')
        } else {
          this.beforeReviewSendBackFlag = true
        }
      },
      beforeReviewSubmit() {
        if (this.selectedRows.length === 0) {
          this.$message.warning('请至少选择一条数据')
        } else {
          this.beforeReviewSubmitFlag = true
        }
      },
      reviewSubmit() {
        let orderTasks = this.selectedRows.filter(item => item.id.length > 12) //过滤掉测试项目的父项
        console.log('orderTasks',orderTasks)
        executeReviewSubmit(orderTasks).then((res) => {
          if (res.success === true) {
            setTimeout(() => {
              this.clearSelected()
              this.getReviewTestList()
              this.beforeReviewSubmitFlag = false
              this.$message.success('结果复核成功')
            }, 200)
          } else {
            this.$message.warning('结果复核失败：' + res.message)
          }
        })
      },
      reviewSendBack() {
        let orderTasks = this.selectedRows.filter(item => item.id.length > 12) //过滤掉测试项目的父项
        console.log('orderTasks',orderTasks)
        executeReviewSendBack({ ordTaskList: orderTasks, opinion: this.reviewSendBackOpinion ? this.reviewSendBackOpinion : "拒绝" }).then((res) => {
          if (res.success === true) {
            setTimeout(() => {
              this.clearSelected()
              this.getReviewTestList()
              this.beforeReviewSendBackFlag = false
              this.$message.success('结果退回成功')
            }, 200)
          } else {
            this.$message.warning('结果退回成功：' + res.message)
          }
        })
      },
      cancelSendBack() {
        this.beforeReviewSendBackFlag = false
        this.reviewSendBackOpinion = null
      },
      handleOk() {
      },
      openData(folderId) {
        this.$refs.testData.query(folderId)
      },
      onTableExpand(expanded, record) {
        if (expanded) {
          this.expandedRowKeys.push(record.id)
        } else {
          this.expandedRowKeys.splice(this.expandedRowKeys.indexOf(record.id), 1)
        }
      },
    }
  }
</script>
<style lang="less" scoped=''>
  /deep/ .ant-table-thead > tr > th {
    text-align: center;
    padding: 5px!important;
    font-size: 14px!important;
  }

  /deep/ .ant-table-tbody > tr > td {
    padding: 0px!important;
    height: 32px!important;
    font-size: 12px!important;
  }

  /deep/ .ant-calendar-picker-icon {
    display: none;
  }

  /deep/ .ant-calendar-picker-input.ant-input {
    color: black;
    font-size: 12px;
    border: 0;
    text-align: center;
    padding: 0;
  }

  .red {
    background-color: #ed0000;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .yellow {
    background-color: #ffc000;
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .grey {
    background-color: rgba(223, 223, 223, 0.25);
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .ant-modal-body {
    padding: 0;
  }

  /deep/ .ant-col {
    padding: 0 !important;
    height: 40px !important;
  }

  /deep/.ant-btn > i, /deep/.ant-btn > span {
    display: flex;
    justify-content: center;
  }

  /deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
    color: #fff;
    background: #1890FF;
  }

  .green{
    background-color: #58a55c;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

  }

  /deep/#table1>div>div>div>div>div>div>table>thead{
    height: 64px;
  }

  /deep/#table1>.ant-table-wrapper>div>div>ul{
    display: none;
  }

  /deep/.ant-tabs-bar{
    margin: 0;
  }

  /deep/ .table-scroll .ant-table-body{
  width: calc(100vw - 40px - 30px);
  height: calc(100vh - 250px) !important;
  overflow: scroll;
}

/deep/.table-scroll .ant-table-thead {
	position: sticky;
	top: 0;
	z-index: 1;
}

/deep/ .ant-table-placeholder {
	border: none !important;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

/deep/.ant-table-pagination.ant-pagination{
  margin-bottom: 0;
}

</style>