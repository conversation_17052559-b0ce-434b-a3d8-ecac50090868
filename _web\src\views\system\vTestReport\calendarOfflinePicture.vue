<template>
  <div>
    <a-spin :spinning="loading">
    <div class="all-wrapper">
      <div style="border-radius: 0 10px 10px 10px;background-color: white;">
        <pbiSearchContainer style="padding: 20px 0px 0px 20px">
          <pbiSearchItem v-if="!safetyTestFlag" label='累积天数'  :span="6" >
            <a-select dropdown-class-name="dropdownClassName"
                      v-model="searchParam.totalDay"
                      :style="{width:selectWidth}"
                      allowClear
                      placeholder="请选择累积天数"
                      mode="multiple"
                      :maxTagCount="parseInt(3)"
                      @change="searchByParam">
              <a-select-option v-for="item in allPicTotalDayList" :value="item">{{item}}</a-select-option>
            </a-select>
          </pbiSearchItem>
          <pbiSearchItem v-if="safetyTestFlag && !beforeAfterFlag" label='累积循环次数'  :span="6" >
            <a-select dropdown-class-name="dropdownClassName"
                      v-model="searchParam.cycleTime"
                      :style="{width:selectWidth}"
                      allowClear
                      placeholder="请选择累积循环次数"
                      mode="multiple"
                      :maxTagCount="parseInt(3)"
                      @change="searchByParam">
              <a-select-option v-for="item in allPicStageList" :value="item">{{item}}</a-select-option>
            </a-select>
          </pbiSearchItem>
          <pbiSearchItem label='样品编号'  :span="6" >
            <a-select dropdown-class-name="dropdownClassName"
                      v-model="searchParam.sampleNo"
                      :style="{width:selectWidth}"
                      allowClear
                      placeholder="请选择样品编号"
                      mode="multiple"
                      :maxTagCount="parseInt(3)"
                      @change="searchByParam">
              <a-select-option v-for="item in allSampleNoList" :value="item">{{item}}</a-select-option>
            </a-select>
          </pbiSearchItem>
          <pbiSearchItem label='照片类型'  :span="6" >
            <a-select dropdown-class-name="dropdownClassName"
                      v-model="searchParam.pictureType"
                      :style="{width:selectWidth}"
                      allowClear
                      placeholder="请选择照片类型"
                      mode="multiple"
                      :maxTagCount="parseInt(4)"
                      @change="searchByParam">
              <a-select-option v-for="item in allPicTypeList" :value="item">{{item}}</a-select-option>
            </a-select>
          </pbiSearchItem>
          <pbiSearchItem :span="beforeAfterFlag ? 12 : 6" type='btn' class="search-container">
            <a-button v-if="!editFlag && hasPerm('testProjectTodoTask:getBatteryInfoById')" style="float:right;margin-right: 8px;" type="primary" @click="handleEdit(true)">启动编辑</a-button>
            <a-button v-if="editFlag && hasPerm('testProjectTodoTask:getBatteryInfoById')" style="float:right;margin-right: 8px;" type="primary" @click="handleEdit(false)">关闭编辑</a-button>
            <a-button style="float:right;margin-right: 12px;" type="primary" @click="exportPicture">批量导出照片</a-button>
          </pbiSearchItem>
        </pbiSearchContainer>
          <a-table style="padding: 0px 20px 20px 20px;"
                   ref="pictureTable"
                   :customRow="customRow"
                   :columns="pictureColumns"
                   :rowKey="record => record.stage"
                   :scroll="{ x: true }"
                   :row-selection="{
                    selectedRowKeys: selectedRowKeys, selectedRows: selectedRows,
                    onChange: onSelectChange, columnWidth:15}"
                   :data-source="pictureDataList"
                   :pagination="paginationConfig"
                   :loading="picTableLoading"
                   bordered>
								<span v-for="item in pictureSlotList" :slot="item.dataIndex" slot-scope="text, record, index">
                  <span>
                    <a-upload
                      v-if="!record[item.dataIndex][0].uid && editFlag"
                      name="file"
                      :headers="headers"
                      :data="uploadData"
                      :action="picOrVidPostUrl"
                      :before-upload="beforeUploadPicture"
                      :multiple="false"
                      :showUploadList="false"
                      @change="uploadPicture($event, item.dataIndex, record)"
                      accept=".jpg,.png,.gif">
                      <a>上传</a>
                    </a-upload>
                    <a-upload v-else
                      list-type="picture-card"
                      class="avatar-uploader"
                      :disabled="!editFlag"
                      @change="removePicture($event, item.dataIndex, record)"
                      :fileList="record[item.dataIndex]"
                      @preview="handlePreview"
                    >
                    </a-upload>
                    <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel">
                      <img alt="example" style="width: 100%" :src="previewImage"/>
                    </a-modal>
                    <div style="width: 100%;display: inline-block;margin-bottom: 6px">
                      <a-icon v-if="record[item.dataIndex][0].thumbUrl && !editFlag" @click="downloadSinglePic(record[item.dataIndex][0])" style="font-size: 18px;color: #0d84ff;" type="download" />
                      <a-upload
                          v-if="record[item.dataIndex][0].thumbUrl && editFlag"
                          name="file"
                          :headers="headers"
                          :data="uploadData"
                          :action="picOrVidPostUrl"
                          :before-upload="beforeUploadPicture"
                          :multiple="false"
                          :showUploadList="false"
                          @change="uploadPicture($event, item.dataIndex, record)"
                          accept=".jpg,.png,.gif">
                      <a>替换</a>
                    </a-upload>
                    </div>
                  </span>
								</span>
          </a-table>
      </div>
    </div>
    </a-spin>
  </div>

</template>
<script>
import {STable} from "@/components";
import {Pagination} from 'ant-design-vue';
import {
  get, getPictureBySafetyTestIds, getPictureByTestProgress, updatePicOrVid, updatePicOrVidOfAq
} from "@/api/modular/system/testProgressManager";
import {calendarCommon} from "./mixin/calendarCommon.js";
import { getMinioDownloadUrl, getMinioPreviewUrl } from "@/api/modular/system/fileManage";
import { downloadMinioFile,downloadMinioFileList} from "@/utils/util";
import Vue from "vue";
import { ACCESS_TOKEN } from "@/store/mutation-types";

function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}

export default {
  components: {
    STable,
    'a-pagination': Pagination
  },
  mixins: [calendarCommon],
  data: function () {
    return {
      data: {},
      iframeUrl: null,
      selectedRowKeys: [],
      selectedRows: [],
      videoWidth: 0,
      videoHeight: 0,
      paginationConfig: {
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '30', '40', '50'], // 显示的每页数量选项
        size: "small",
        showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
      },
      selectWidth: '260px',
      itemWidth: '360px',
      pictureDataList: [],
      allPicTotalDayList: [],
      allPicStageList: [],
      allSafetyTestList: [],
      beforeAfterFlag: false,
      safetyTestFlag: false,
      allSampleNoList: [],
      allPicTypeList: [],
      headerList: [],
      picTypeKeyList: [],
      picTypeValueList: [],
      pictureColumns: [],
      pictureSlotList: [],
      picTableLoading: false,
      testProgress: null,
      previewVisible: false,
      loading: false,
      editFlag: false,
      picOrVidPostUrl: "/api/sysFileInfo/minioUpload",
      uploadData: { bucket: 'safetylab' },
      headers: {
        Authorization: "Bearer " + Vue.ls.get(ACCESS_TOKEN)
      },
      previewImage: '',
      searchParam: {
        cycleTime: [],
        totalDay: [],
        sampleNo: [],
        pictureType: []
      },
    }
  },
  mounted() {
    if (this.$route.query.type === '安全测试') {
      this.getPictureDataOfAq(true)
      this.safetyTestFlag = true
    } else {
      this.getPictureData(true)
    }
  },
  created() {
    this.initSearchSize()
  },
  methods: {
    //直接调用lims接口预览或下载
    async openFileOrDownload(fileId,fileName) {
      //pbi上传的文件
      if (fileId) {
          await getMinioDownloadUrl(fileId,encodeURIComponent(fileName)).then(res1 => {
             downloadMinioFile(res1.data)
          })
      }
    },
    async exportPicture() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      // console.log('this.selectedRows',this.selectedRows)
      // console.log('this.selectedRowKeys',this.selectedRowKeys)
      // console.log('this.picTypeKeyList',this.picTypeKeyList)
      // console.log('this.picTypeValueList',this.picTypeValueList)
      // console.log('this.searchParam.sampleNo',this.searchParam.sampleNo)
      // console.log('this.searchParam.pictureType',this.searchParam.pictureType)
      // 如果做了筛选，则导出为筛选后的照片
      // 样品编号筛选
      let searchSampleIndexList = []
      for (const item of this.searchParam.sampleNo) {
        searchSampleIndexList.push(this.allSampleNoList.indexOf(item) + "")
      }
      // 照片类型筛选
      let allPicTypeFieldList = []
      let searchPicTypeList = []
      Object.getOwnPropertyNames(this.selectedRows[0]).forEach(key => {
        let index = this.picTypeValueList.findIndex(item => key.indexOf(item) > -1)
        if (index > -1) {
          allPicTypeFieldList.push(key)
          if (this.searchParam.pictureType.findIndex(item => this.picTypeKeyList[index] === item) > -1) {
            searchPicTypeList.push(this.picTypeValueList[index])
          }
        }
      });
      let downloadPicList = []
      this.selectedRows.forEach((v, index) => {
        for (const key of allPicTypeFieldList) {
          let sampleIndexAndPicType = key.split('-');
          let sampleIndex = sampleIndexAndPicType[0];
          let picType = sampleIndexAndPicType[1];
          if (v[key][0].uid) {
            if (searchSampleIndexList.length > 0) {
              if (searchSampleIndexList.findIndex(k => k === sampleIndex) > -1) {
                if (searchPicTypeList.length === 0) {
                  downloadPicList.push(v[key][0])
                } else if (searchPicTypeList.length > 0 && searchPicTypeList.findIndex(k => k === picType) > -1) {
                  downloadPicList.push(v[key][0])
                }
              }
            } else {
              if (searchPicTypeList.length === 0) {
                downloadPicList.push(v[key][0])
              } else if (searchPicTypeList.length > 0 && searchPicTypeList.findIndex(k => k === picType) > -1) {
                downloadPicList.push(v[key][0])
              }
            }
          }
        }
      })
      if (downloadPicList.length === 0) {
        this.$message.warning('当前没有可导出的照片')
      } else {
        let urlList = []
        for (let i = 0; i < downloadPicList.length; i++) {
          let v = downloadPicList[i]
          await getMinioDownloadUrl(v.uid,encodeURIComponent(v.name)).then(res1 => {
            urlList.push(res1.data)
          })
        }

        downloadMinioFileList(urlList)

      }
    },
    searchByParam() {
      this.pictureSlotList = []
      this.pictureColumns = []
      this.selectedRows = []
      this.selectedRowKeys = []
      if (this.safetyTestFlag) {
        this.getPictureDataOfAq(false)
      } else {
        this.getPictureData(false)
      }
    },
    initSearchSize() {
      let initWidth = document.documentElement.clientWidth // 拿到父元素宽
      if (initWidth <= 1280) {
        this.itemWidth = '300px'
        this.selectWidth = '200px'
      } else {
        this.itemWidth = '360px'
        this.selectWidth = '260px'
      }
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    customRow(record) {
      return {
        props: {},
        on: { // 事件
          click: (event) => {
            // if (event.srcElement.toString().indexOf('SVGPathElement') === -1 && event.srcElement.cellIndex !== 5) { //  不是点击播放按钮
            //   let index = this.selectedRows.indexOf(record)
            //   if (index > -1) {
            //     this.selectedRows.splice(index, 1);
            //     this.selectedRowKeys.splice(index, 1);
            //   } else {
            //     this.selectedRows.push(record);
            //     this.selectedRowKeys.push(record.videoId);
            //   }
            // }
          },
        },
      };
    },
    initPictureColumnHeader() {
      let firstCoulmn = {
        title: "累积天数/Day",
        align: "center",
        dataIndex: "totalDay",
        width: "100px"
      }
      let firstCoulmnOfAq = {
        title: this.beforeAfterFlag ? "测试阶段" : "累积循环次数",
        align: "center",
        dataIndex: "totalDay",
        width: "100px",
        customRender: (text, record, index) => {
          if (this.beforeAfterFlag) {
            if (record.totalDay === "0") {
              return "测试前"
            } else {
              return "测试后"
            }
          } else {
            return text
          }
        }
      }
      let secondCoulmn = {
        title: "绝对时间",
        align: "center",
        dataIndex: "absoluteTime",
        width: "100px"
      }
      if (this.safetyTestFlag) {
        this.pictureColumns.push(firstCoulmnOfAq)
      } else {
        this.pictureColumns.push(firstCoulmn)
      }
      this.pictureColumns.push(secondCoulmn)
      // 照片报告表头处理
      if (this.headerList && this.headerList.length > 0) {
        let allBatteryCodeList = this.headerList.map(v => v[2])
        let uniqueBatteryCodeList = [...new Set(allBatteryCodeList)];
        let allSampleCategoryList = this.headerList.map(v => v[5])
        this.picTypeKeyList = [...new Set(allSampleCategoryList)];
        let allSampleCategoryFieldList = this.headerList.map(v => v[6])
        this.picTypeValueList = [...new Set(allSampleCategoryFieldList)];
        console.log('uniqueBatteryCodeList',uniqueBatteryCodeList)
        console.log('allSampleCategoryFieldList',allSampleCategoryFieldList)
        console.log('this.headerList',this.headerList)
        for (let k = 0; k < this.allSampleNoList.length; k++) {
          let uniqueSampleCode = this.allSampleNoList[k];
          // 根据选择的电芯编号过滤
          if (this.searchParam.sampleNo.length !== 0 && this.searchParam.sampleNo.findIndex(item => item === uniqueSampleCode) === -1) {
            continue;
          }
          let sampleCategoryChild = []
          for (let n = 0; n < this.picTypeKeyList.length; n++) {
            let picType = this.picTypeKeyList[n]
            if (this.searchParam.pictureType.length !== 0 && this.searchParam.pictureType.findIndex(item => item === picType) === -1) {
              continue;
            }
            let picTypeValue = this.picTypeValueList[n]
            let picItem = {
              title: picType,
              align: "center",
              dataIndex: k + "-" + picTypeValue,
              width: "100px",
              scopedSlots: {
                customRender: k + "-" + picTypeValue
              }
            }
            sampleCategoryChild.push(picItem)
            this.pictureSlotList.push(picItem)
          }
          let uniqueBatteryCode = uniqueBatteryCodeList[k];
          let sampleCodeIndex = this.headerList.findIndex(item => item[1] === uniqueSampleCode);
          let status = this.headerList[sampleCodeIndex][3]
          let tips = this.headerList[sampleCodeIndex][4]
          let result = {
            title: uniqueBatteryCode,
            align: "center",
            children: [
              {
                title: <a-tooltip title={tips}>{status}</a-tooltip>,
                align: "center",
                children: sampleCategoryChild,
              }
            ],
            dataIndex: k
          }
          if (uniqueSampleCode !== uniqueBatteryCode) {
            result = {
              title: uniqueSampleCode,
              align: "center",
              children: [
                result
              ]
            }
          }
          this.pictureColumns.push(result)
        }
      }
      console.log('this.pictureColumns',this.pictureColumns)
      console.log('this.pictureSlotList',this.pictureSlotList)
    },
    getPictureDataByTestProgress(testProgress, initFlag) {
      this.loading = true
      let totalDayParam = this.searchParam.totalDay.length > 0 ? this.searchParam.totalDay.join(",") : "all"
      getPictureByTestProgress(testProgress, totalDayParam).then(res => {
        if (res.success) {
          this.pictureDataList = res.data.bodyDataList
          this.allPicTotalDayList = res.data.allPicTotalDayList
          this.allSampleNoList = res.data.allSampleNoList
          this.allPicTypeList = res.data.allPicTypeList
          this.headerList = res.data.header
          if (initFlag && this.allPicTotalDayList.length > 1) {
            this.searchParam.totalDay.push(this.allPicTotalDayList[0])
            this.searchParam.totalDay.push(this.allPicTotalDayList[this.allPicTotalDayList.length - 1])
            this.pictureDataList.splice(1, this.pictureDataList.length - 2)
          }
          // console.log('this.pictureDataList',this.pictureDataList)
          // console.log('this.allPicTotalDayList',this.allPicTotalDayList)
        } else {
          this.$message.error('获取照片数据失败：' + res.message)
        }
      }).finally(() => {
        this.initPictureColumnHeader()
        setTimeout(() => {
          this.loading = false
        }, 500)
      })
    },
    refreshPictureData() {
      this.loading = true
      let totalDayParam = this.searchParam.totalDay.length > 0 ? this.searchParam.totalDay.join(",") : "all"
      getPictureByTestProgress(this.testProgress, totalDayParam).then(res => {
        if (res.success) {
          this.pictureDataList = res.data.bodyDataList
        } else {
          this.$message.error('获取照片数据失败：' + res.message)
        }
      }).finally(() => {
        setTimeout(() => {
          this.loading = false
        }, 500)
      })
    },
    getPictureData(initFlag) {
      if (this.$route.query.testProgressId) {
        get({ id: this.$route.query.testProgressId }).then(res => {
          if (res.success) {
            this.testProgress = res.data
            this.getPictureDataByTestProgress(this.testProgress, initFlag)
          }
        })
      } else {
        get({ ordtaskid: this.$route.query.id }).then(res => {
          if (res.success) {
            this.testProgress = res.data
            this.getPictureDataByTestProgress(this.testProgress, initFlag)
          }
        })
      }
    },
    refreshPictureDataOfAq() {
      this.loading = true
      let cycleTimeParam = this.searchParam.cycleTime.length > 0 ? this.searchParam.cycleTime.join(",") : "all"
      getPictureBySafetyTestIds({ safetyTestIds: this.$route.query.safetyTestIds, cycleTime: cycleTimeParam }).then(res => {
        if (res.success) {
          this.pictureDataList = res.data.bodyDataList
        } else {
          this.$message.error('获取照片数据失败：' + res.message)
        }
      }).finally(() => {
        setTimeout(() => {
          this.loading = false
        }, 500)
      })
    },
    getPictureDataOfAq(initFlag) {
      this.loading = true
      let cycleTimeParam = this.searchParam.cycleTime.length > 0 ? this.searchParam.cycleTime.join(",") : "all"
      getPictureBySafetyTestIds({ safetyTestIds: this.$route.query.safetyTestIds, cycleTime: cycleTimeParam }).then(res => {
        if (res.success) {
          this.pictureDataList = res.data.bodyDataList
          this.allPicStageList = res.data.allPicStageList
          this.allSampleNoList = res.data.allSampleNoList
          this.allPicTypeList = res.data.allPicTypeList
          this.allSafetyTestList = res.data.allSafetyTestList
          this.headerList = res.data.header
          if (initFlag && this.allPicStageList.length > 1) {
            this.searchParam.cycleTime.push(this.allPicStageList[0])
            this.searchParam.cycleTime.push(this.allPicStageList[this.allPicStageList.length - 1])
            this.pictureDataList.splice(1, this.pictureDataList.length - 2)
          }
          if (this.allSafetyTestList.length > 0 && this.allSafetyTestList[0].testType === 'before_after') {
            this.beforeAfterFlag = true
          }
          // console.log('this.pictureDataList',this.pictureDataList)
          // console.log('this.allPicStageList',this.allPicStageList)
        } else {
          this.$message.error('获取照片数据失败：' + res.message)
        }
      }).finally(() => {
        this.initPictureColumnHeader()
        setTimeout(() => {
          this.loading = false
        }, 500)
      })
    },
    async handlePreview(file) {
      getMinioPreviewUrl(file.uid).then(res => {
        if (res.data) {
          this.previewImage = res.data.replace("http://10.100.1.99:9000/", "/minioDownload/")
          setTimeout(() => {
            this.previewVisible = true
          }, 100)
        } else {
          this.$message.error("服务器错误，请联系管理员！")
        }
      })
    },
    downloadSinglePic(file) {
      getMinioDownloadUrl(file.uid,encodeURIComponent(file.name)).then(res1 => {
        downloadMinioFile(res1.data)
      })
    },
    handleCancel() {
      this.previewVisible = false;
    },
    handleEdit(flag) {
      this.editFlag = flag;
      this.picTableLoading = true;
      setTimeout(() => {
        this.picTableLoading = false;
      }, 300)
    },
    beforeUploadPicture(file) {
      let isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg' || file.type === 'image/gif';
      if (!isJpgOrPng) {
        this.$message.error('格式错误，只能上传jpg、png、gif格式的图片');
        return false;
      }
    },
    uploadPicture(info, indexAndField, record) {
      let index = indexAndField.split('-')[0]
      let field = indexAndField.split('-')[1]
      if (info.file.status === "done") {
        let file = info.file
        let update = {}
        update.id = record.detailId
        update.pictureId = file.response.data
        update.pictureName = file.name
        this.picTableLoading = true
        if (this.safetyTestFlag) {
          if (!update.id) { // 同一个测试项目分配多次任务的情况
            update.ids = this.$route.query.safetyTestIds
            update.sampleNos = this.allSampleNoList[index]
            if (this.beforeAfterFlag) {
              update.stage = record.totalDay
            } else {
              update.totalCycleTime = record.totalDay
            }
          }
          updatePicOrVidOfAq(update, 'add', field, index).then(res => {
            if (res.success) {
              this.refreshPictureDataOfAq()
              this.$message.success(`${info.file.name} 上传成功`)
            } else {
              this.$message.error("上传失败：" + res.message)
            }
            setTimeout(() => {
              this.picTableLoading = false
            },500)
          })
        } else {
          updatePicOrVid(update, 'add', field, index).then(res => {
            if (res.success) {
                this.refreshPictureData()
              this.$message.success(`${info.file.name} 上传成功`)
            } else {
              this.$message.error("上传失败：" + res.message)
            }
            setTimeout(() => {
              this.picTableLoading = false
            },500)
          })
        }
      } else if (info.file.status === "error") {
        this.$message.error(`${info.file.name} 上传失败`)
      }
    },
    removePicture(info, indexAndField, record) {
      if (info.file.status === "removed") {
        let index = indexAndField.split('-')[0]
        let field = indexAndField.split('-')[1]
        let update = {}
        update.id = record.detailId
        this.picTableLoading = true
        if (this.safetyTestFlag) {
          if (!update.id) { // 同一个测试项目分配多次任务的情况
            update.ids = this.$route.query.safetyTestIds
            update.sampleNos = this.allSampleNoList[index]
            if (this.beforeAfterFlag) {
              update.stage = record.totalDay
            } else {
              update.totalCycleTime = record.totalDay
            }
          }
          updatePicOrVidOfAq(update, 'delete', field, index).then(res => {
            if (res.success) {
              this.refreshPictureDataOfAq()
              this.$message.success("删除成功")
            } else {
              this.$message.error("删除失败：" + res.message)
            }
            setTimeout(() => {
              this.picTableLoading = false
            },500)
          })
        } else {
          updatePicOrVid(update, 'delete', field, index).then(res => {
            if (res.success) {
              this.refreshPictureData()
              this.$message.success("删除成功")
            } else {
              this.$message.error("删除失败：" + res.message)
            }
            setTimeout(() => {
              this.picTableLoading = false
            },500)
          })
        }
      } else if (info.file.status === "error") {
        this.$message.error(`${info.file.name} 删除失败`)
      }
    },
  }
}
</script>
<style lang="less" scoped>
@import '/src/components/pageTool/style/pbiSearchItem.less';

.right-top-div {
  position: absolute;
  top: 18px;
  right: 10px;
  height:43.5px;
  display: flex;
  align-items: center;
}
/deep/ .ant-table-thead > tr > th{
  font-size: 13px;
  padding: 5px;
  font-weight: 500;
  overflow-wrap: break-word;
}

/deep/ .ant-table-tbody > tr > td:nth-child(n+4) {
  padding: 10px 0px 0px 8px;
  font-size: 12px;
  font-weight: 400;
  overflow-wrap: break-word;
}

/deep/ .ant-upload-picture-card-wrapper {
  zoom: 1;
  display: ruby;
}

.dropdownClassName {
  .ant-select-dropdown-menu-item {
    text-align: center;
    padding: 0;
    font-size: 12px;
  }
}

/deep/ .ant-upload-list {
  margin-bottom: -8px;
}
</style>