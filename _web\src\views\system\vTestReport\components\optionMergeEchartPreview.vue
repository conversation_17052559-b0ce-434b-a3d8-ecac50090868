<template>
  <div>
    <div v-for="item in chartList.filter(chartObj => chartObj.show)">
      <div class="flex-sb-center-row" style="margin-top: 2px;">
        <pageComponent :editObj="item.id"  @down="handleDown(item.id)" @edit="handleEditEcharts(item.id)"></pageComponent>
        <div>
          <a-tooltip title="编辑建模参数">
            <a-button size="small" @click="reExport(false)"><a-icon type="edit" /></a-button>
          </a-tooltip>
          <a-popconfirm title="确定要重新生成吗?" ok-text="确定" cancel-text="取消" placement="left" @confirm="reExport(true)">
            <a-tooltip title="重新生成">
              <a-button style="margin-left: 10px;" size="small"><a-icon type="sync" /></a-button>
            </a-tooltip>
          </a-popconfirm>
        </div>
      </div>
      <div :ref="optionKey + '-' + item.id" :id="optionKey + '-' + item.id" class="echarts-div"></div>
    </div>

    <!-- 在线编辑图表 -->
    <div v-if="drawerVisible">
      <PreviewDrawer
        :screenImageId = "screenImageId"
        :templateParam = "reportChartTemplateList[editObj]"
        :legendNameTypeShow="true"
        :LegendNameTypeList = "this.chartLegendNameListObj[editObj]"
        :legendOptions="legendOptions[editObj]"
        :data="editData[editObj].series"
        :original="originalData[editObj]"
        :editData="editData[editObj]"
        :checkObj="chartCheckObj[editObj]"
        @submit="handleDrawerSubmit"
        @reset="handleDrawerReset"
        @close="() => this.drawerVisible = false"
        @changeTemplate ="handleChangeTemplate"
        @screenshot="handleScreenshot">
      </PreviewDrawer>
    </div>
  </div>
</template>

<script>
import {optionMergeCommon} from "@/views/system/vTestReport/mixin/optionMergeCommon";
import {chartTemplate} from "@/views/system/vTestReport/mixin/chartTemplate";
import _ from "lodash";
import html2canvas from "html2canvas";

export default {
  components: {
  },
  mixins: [optionMergeCommon,chartTemplate],
  props: {
    resultJsonObj: {
      type: Object,
      default: {}
    },
    optionKey: {
      type: Number,
      default: 0
    },
  },
  data() {
    return {
      id:'',
      chartCheckObj:{
        voltage:{},
        innerres:{},
        height:{},
        volume:{},
        weight:{},
        isolateres:{},
      },
      chartList: [
        {id: 'voltage', show: true},
        {id: 'innerres', show: true},
        {id: 'height', show: false},
        {id: 'volume', show: false},
        {id: 'weight', show: false},
        {id: 'isolateres', show: false},
      ],

      titleDataObj: {
        voltage:    { chartTitle: 'Calendar Life_OCV', XTitle: 'Storage Time / D', YTitle: 'OCV / mV', hasYTitle2: false, YTitle2: null},
        innerres:   { chartTitle: 'Calendar Life_ACR', XTitle: 'Storage Time / D', YTitle: 'ACR / mΩ', hasYTitle2: false, YTitle2: null},
        height:     { chartTitle: 'Calendar Life_Size', XTitle: 'Storage Time / D', YTitle: 'Size / mm', hasYTitle2: false, YTitle2: null},
        volume:     { chartTitle: 'Calendar Life_Volume', XTitle: 'Storage Time / D', YTitle: 'Volume / g', hasYTitle2: false, YTitle2: null},
        weight:     { chartTitle: 'Calendar Life_Weight', XTitle: 'Storage Time / D', YTitle: 'Weight / g', hasYTitle2: true, YTitle2: 'Mass Loss Rate / %', tooltipPrefix2: '失重率 ', tooltipUnit2: ' %'},
        isolateres: { chartTitle: 'Calendar Life_Insulation Resistance', XTitle: 'Storage Time / D', YTitle: 'Insulation Resistance / mΩ', hasYTitle2: false, YTitle2: null},
      },
      legendOptions:{} //图例-数据的选择项

    }
  },
   created() {
    this.init()
  },
  mounted() {
    // 赋值，echarts图初始化
    this.$nextTick(async () => {
      await this.getChartTemplateRelationList(this.$route.query.id,this.editObjList)
      this.editObjList.forEach( (item, index) => {
        if (this.chartList[index].show) {
          this.initEchart(item, this.xAxisType, `${item}EchartList`, item === 'weight' ? 'weightLossRateEchartList' : null)
        }
      })
      this.$emit("echartsReady")
    })
  },
  methods: {
    init() {
      this.id = this.$route.query.id
      this.editObjList = ['voltage','innerres','height','volume','weight','isolateres']
      this.testCondition = this.resultJsonObj ? this.resultJsonObj.testCondition + '_' : ''
      this.editObjList.forEach(item => {
        this.firstInit[item] = true
        this.chartLegendNameListObj[item] = []
        this.titleDataObj[item].chartTitle = this.testCondition + this.titleDataObj[item].chartTitle
      })

      // 是否展示 尺寸、产气量、重量、绝缘阻值
      let storageDaySetMap = this.resultJsonObj ? this.resultJsonObj.storageDaySetMap : {}
      let verifyList = ['height', 'volume', 'weight', 'isolateres']
      verifyList.forEach((item, index) => {
        if (Array.isArray(storageDaySetMap[item]) && storageDaySetMap[item].length > 0) {
          this.chartList[index + 2].show = true
        }
      })

    },
    reExport(rebuildFlag = false) {
      this.$emit('reExport', rebuildFlag);
    },
    handleInitChart(targetObj) {
      this.initEchart(targetObj, this.xAxisType, `${targetObj}EchartList`, targetObj === 'weight' ? 'weightLossRateEchartList' : null)
    },
    initEchart(targetObj, xAxisType = '', yAxisOneListName = null, yAxisTwoListName = null) {

      // 检查是否存在 ECharts 实例 并清除
      if (this.echartObj[targetObj]) {
        this.echartObj[targetObj].dispose();
      }

      // this.echartObj[targetObj] = this.echarts.init(document.getElementById(this.optionKey + '-' + targetObj), 'walden', { renderer: "svg" }) // 测试
      this.echartObj[targetObj] = this.echarts.init(document.getElementById(this.optionKey + '-' + targetObj), 'walden', {devicePixelRatio: 2})

      // 模板数据
      const templateParam = this.reportChartTemplateList[targetObj].templateParamJson
      const originalParam = this.reportChartTemplateList[targetObj].originalParamJson

      let seriesList = []
      if (this.firstInit[targetObj]) {  // 首次加载
        let yAxisOneList = _.cloneDeep(this.resultJsonObj[yAxisOneListName])
        yAxisOneList = Array.isArray(yAxisOneList) ? yAxisOneList : []

        let yAxisTwoList = yAxisTwoListName !== null ? _.cloneDeep(this.resultJsonObj[yAxisTwoListName]) : []
        seriesList = this._handleEchartData(targetObj, xAxisType, yAxisOneList, yAxisTwoList, yAxisTwoListName !== null)

      } else {  // 二次加载
        seriesList = this.editData[targetObj].editSeries
      }

      let chartOption = this._handleEchartOptions(targetObj, seriesList, yAxisTwoListName !== null)

      // 设置离线图X轴，以电压图为准
      if ((this.firstInit[targetObj] || this.editData[targetObj].targetResetObj === 'xMax') && !targetObj.includes('voltage') && !templateParam.xMax) {
        chartOption.xAxis[0].max = this.globalXMax
        chartOption.xAxis[0].interval = this.globalXInterval
      }

      // 如果模板有X轴的最大值、最小值、间隔,如果有就设置
      if(templateParam.xMin){
        chartOption.xAxis[0].min = templateParam.xMin
      }
      if(templateParam.xMax){
        chartOption.xAxis[0].max = templateParam.xMax
      }
      if(templateParam.xInterval){
        chartOption.xAxis[0].interval = templateParam.xInterval
      }

      this.echartObj[targetObj].clear()
      this.echartObj[targetObj].getZr().off('dblclick')
      this.echartObj[targetObj].getZr().on('dblclick', ({target, topTarget}) => {
        this._handleDblclickEchart(target, topTarget, targetObj)
      });
      this.echartObj[targetObj].setOption(chartOption)

      if (this.firstInit[targetObj]) {
        this._handleYAxisValue(targetObj, yAxisTwoListName !== null)

        // 为图例多一个格子：setOption后才能拿到X轴最大值间隔值，设置离线图表的 globalXMax、globalXInterval
        
        if (targetObj.includes('voltage') && !templateParam.xMax) {
          this.editData[targetObj].xMax = this.editData[targetObj].xMax + this.editData[targetObj].xInterval
          this.globalXMax = this.editData[targetObj].xMax
          this.globalXInterval = this.editData[targetObj].xInterval
          this.originalData[targetObj].xMax = this.editData[targetObj].xMax
          this.originalData[targetObj].xInterval = this.editData[targetObj].xInterval

          // 重新设置X轴
          chartOption.xAxis[0].max = this.globalXMax
          chartOption.xAxis[0].interval = this.globalXInterval
          this.echartObj[targetObj].setOption(chartOption)
        }

        this.firstInit[targetObj] = false
      }
    },
    _handleInitLegendList(seriesList, legend) {
      if (this.echartObj[this.editObj]) this.echartObj[this.editObj].dispose();
      this.echartObj[this.editObj] = this.echarts.init(document.getElementById(this.optionKey + '-' + this.editObj), 'walden', { devicePixelRatio: 2 })

      let chartOption = this._handleEchartOptions(this.editObj, seriesList, this.editObj === 'weight')

      chartOption.legend.data = legend
      this.echartObj[this.editObj].setOption(chartOption)
    },
    handleDown(targetObj) {
      const dom = document.getElementById(this.optionKey + '-' + targetObj)

      let fileName = this.resultJsonObj.optionName + '-' + (this.firstInit[targetObj] ? this.originalData[targetObj].chartTitle : this.editData[targetObj].chartTitle)
      if (typeof fileName === 'string') {
        fileName = fileName.replaceAll('.', ' ')
      }

      html2canvas(dom, {
        useCORS: true,
        backgroundColor: "#fff"
      }).then(canvas => {
        let canvasImg = canvas.toDataURL("image/png")
        const blob = this.handleB64toBlob(canvasImg.replace("data:image/png;base64,", ""))
        if (window.navigator.msSaveOrOpenBlob) {
          //兼容IE10
          navigator.msSaveBlob(blob, fileName)
        } else {
          const href = URL.createObjectURL(blob)
          const a = document.createElement("a")
          a.style.display = "none"
          a.href = href // 指定下载链接
          a.download = fileName
          a.click() //触发下载
          URL.revokeObjectURL(a.href)
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>
// 通用
.echarts-div {
  width: 595px;
  height: 415px;
  border: 0.5px solid #ccc;
}

.flex-sb-center-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>