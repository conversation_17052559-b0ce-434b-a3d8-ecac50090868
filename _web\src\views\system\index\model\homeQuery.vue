<template>
	<a-modal
		title="查询"
		:visible="true"
		:width="500"
		:centered="true"
		okText="查看详情"
		cancelText="关闭"
		@cancel="handleModelCancel"
		@ok="handleModelOk"
	>
		<div class="model-wrapper">
			<div class="search">
				<a-auto-complete
					class="global-search"
					size="large"
					style="width: 100%"
					placeholder="input here"
					option-label-prop="title"
					@select="onSelect"
					@search="handleSearch"
				>
					<template slot="dataSource">
						<a-select-option v-for="item in dataSource" :key="item.category" :title="item.category">
							<a :href="`https://s.taobao.com/search?q=${item.query}`" target="_blank" rel="noopener noreferrer">
								{{ item.category }}
							</a>
						</a-select-option>
					</template>
					<a-input>
						<a-icon slot="suffix" type="search" class="certain-category-icon" />
					</a-input>
				</a-auto-complete>
			</div>
		</div>
	</a-modal>
</template>

<script>
export default {
	data() {
		return {}
	},
	methods: {
		/**
		 * 弹窗事件
		 */
		handleModelCancel() {
			this.$emit("cancel")
		}
	}
}
</script>

<style lang="less" scoped>
.chart-wrapper {
	display: flex;
	justify-content: center;
	align-items: center;
}
.search {
	display: flex;
	justify-content: center;
	align-items: center;
}
</style>
