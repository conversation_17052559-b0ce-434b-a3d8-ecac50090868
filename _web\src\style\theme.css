/**
 * 主题美化样式
 * 用于美化前端页面的整体视觉效果
 */

/* 全局样式优化 */
body {
  background-color: #f5f7fa;
  color: #333;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 卡片样式优化 */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.ant-card:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  padding: 0 16px;
}

.ant-card-head-title {
  font-weight: 600;
  font-size: 16px;
  color: #1f2329;
}

.ant-card-body {
  padding: 16px;
}

/* 按钮样式优化 */
.ant-btn {
  border-radius: 4px;
  transition: all 0.3s;
}

.ant-btn-primary {
  background-color: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);
}

/* 表单样式优化 */
.ant-form-item-label > label {
  font-weight: 500;
  color: #1f2329;
}

.ant-input, .ant-input-number, .ant-select-selection {
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s;
}

.ant-input:hover, .ant-input-number:hover, .ant-select-selection:hover {
  border-color: #40a9ff;
}

.ant-input:focus, .ant-input-number:focus, .ant-select-selection:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 表格样式优化 */
.ant-table {
  border-radius: 8px;
  overflow: hidden;
}

.ant-table-thead > tr > th {
  background-color: #f5f7fa;
  font-weight: 600;
  color: #1f2329;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f0f7ff;
}

/* 标签页样式已统一到 formula.css 中 */

/* 菜单样式优化 */
.ant-menu-inline {
  border-right: none;
}

.ant-menu-item {
  margin: 4px 0;
  padding: 0 16px;
  border-radius: 4px;
  transition: all 0.3s;
}

.ant-menu-item:hover {
  background-color: #f0f7ff;
}

.ant-menu-item-selected {
  background-color: #e6f7ff;
  font-weight: 600;
}

.ant-menu-item-selected:after {
  border-right: 3px solid #1890ff;
  border-radius: 0 3px 3px 0;
}

/* 布局样式优化 */
.ant-layout-header {
  background: linear-gradient(135deg, #1890ff, #096dd9);
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-layout-footer {
  background: #f5f7fa;
  padding: 16px;
  color: #666;
}



/* 统一按钮样式 */
/* 查看按钮 */
.view-button {
  color: #1890ff !important;
  transition: all 0.3s;
  border-radius: 4px;
}

.view-button:hover {
  color: #40a9ff !important;
  background-color: #e6f7ff !important;
  border-color: #91d5ff !important;
}

.view-button:active {
  color: #096dd9 !important;
  background-color: #bae7ff !important;
  border-color: #40a9ff !important;
}

.view-button.ant-btn-link {
  padding: 4px 8px;
}

/* 编辑按钮 */
.edit-button {
  color: #52c41a !important;
  transition: all 0.3s;
  border-radius: 4px;
}

.edit-button:hover {
  color: #73d13d !important;
  background-color: #f6ffed !important;
  border-color: #b7eb8f !important;
}

.edit-button:active {
  color: #389e0d !important;
  background-color: #d9f7be !important;
  border-color: #73d13d !important;
}

.edit-button.ant-btn-link {
  padding: 4px 8px;
}

/* 删除按钮 */
.delete-button {
  color: #ff4d4f !important;
  transition: all 0.3s;
  border-radius: 4px;
}

.delete-button:hover {
  color: #ff7875 !important;
  background-color: #fff1f0 !important;
  border-color: #ffa39e !important;
}

.delete-button:active {
  color: #cf1322 !important;
  background-color: #ffccc7 !important;
  border-color: #ff7875 !important;
}

.delete-button.ant-btn-link {
  padding: 4px 8px;
}

.delete-button.ant-btn-link:hover {
  background-color: #fff1f0 !important;
}

.delete-button.ant-btn-danger {
  background-color: #ff4d4f;
  border-color: #ff4d4f;
  color: #fff !important;
}

.delete-button.ant-btn-danger:hover {
  background-color: #ff7875;
  border-color: #ff7875;
  color: #fff !important;
}

/* 标签删除按钮样式 */
.delete-button-tag .ant-tag-close-icon {
  color: #ff4d4f !important;
  transition: all 0.3s;
}

.delete-button-tag .ant-tag-close-icon:hover {
  color: #ff7875 !important;
  background-color: #fff1f0;
  border-radius: 50%;
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #999;
}
