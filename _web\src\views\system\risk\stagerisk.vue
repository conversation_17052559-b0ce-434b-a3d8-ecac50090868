<template>
  <div style="background:#fff">
    <div style="text-align:right">
      <a-button type="primary" @click="add" style="margin:8px 4px">
        新增
      </a-button>
    </div>
    <div>
      <a-table :scroll="{ x: 1800}" style="" ref="table" size="small" :rowKey="(record) => record.issueId" :columns="columns" :dataSource="list" :loading="loading">
        <template slot="transitionBeanList" slot-scope="text,record">
          <a-popconfirm v-for="(item,i) in record.transitionBeanList" :key="i" style="margin-right:3px" placement="topRight" title="确认执行此操作？" @confirm="() => callTransiteStage(record,item.transitionId)">
            <a>{{item.transitionName}}</a>
          </a-popconfirm>
        </template>
        <span slot="productStage" slot-scope="text">
                                    {{ 'product_stage_status' | dictType(text) }}
                                    </span>
        <span slot="riskLevel" slot-scope="text">
                                    {{ 'stage_risk_level' | dictType(text) }}
                                    </span>
        <span slot="problemStatus" slot-scope="text">
                                    {{ 'stage_problem_status' | dictType(text) }}
                                    </span>
        <span slot="riskCategory" slot-scope="text">
                                    {{ 'stage_risk_category' | dictType(text) }}
                                    </span>
        <span slot="action" slot-scope="text,record">
                  <a @click="$refs.editForm.edit(record)">编辑</a>
                  <a-divider type="vertical" />
                  <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => stageDelete(record)">
                    <a>删除</a>
                  </a-popconfirm>
                  <a-divider type="vertical" />
                  <a-dropdown>
                    <a class="ant-dropdown-link">
                      更多 <a-icon type="down" />
                    </a>
                    <a-menu slot="overlay">
                      <a-menu-item>
                        <a @click="handleToJira(record)">填写风险管理措施</a>
                      </a-menu-item>
                      <!-- <a-menu-item >
                        <a @click="handleToJira(record)">填写进展</a>
                      </a-menu-item> -->
                    </a-menu>
                  </a-dropdown>
                </span>
      </a-table>
    </div>
    <add ref="addForm" @ok="handleOk" :issueId="issueId" :productCustomer="productCustomer" />
    <edit ref="editForm" @ok="handleOk" />
  </div>
</template>

<script>
  import Vue from 'vue'
  import {
    getStageRisk,
    stageDel,
    transiteStage
  } from "@/api/modular/system/report"
  import add from './add'
  import edit from './edit'
  export default {
    components: {
      add,
      edit,
    },
    props: {
      issueId: {
        type: Number,
        default: 0
      },
      productCustomer: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        windowHeight: document.documentElement.clientHeight - 90,
        list: [],
        columns: [{
            title: '产品阶段',
            dataIndex: 'productStage',
            
            scopedSlots: {
              customRender: 'productStage'
            }
          },
          {
            title: '客户',
            
            dataIndex: 'productCustomer'
          },
          {
            title: '风险事件',
            dataIndex: 'riskEvent',
            width: 150
          },
          {
            title: '风险发生的原因',
            dataIndex: 'riskReason',
            width: 150
          },
          {
            title: '风险分类',
            
            dataIndex: 'riskCategory',
            scopedSlots: {
              customRender: 'riskCategory'
            }
          },
          {
            title: '风险等级',
            
            dataIndex: 'riskLevel',
            scopedSlots: {
              customRender: 'riskLevel'
            }
          },
          {
            title: '风险管理措施',
            dataIndex: 'riskMeasures',
          },
         
          {
            title: '责任人',
            
            dataIndex: 'responsiblePersonName'
          },
          {
            title: '问题跟进状态',
            
            dataIndex: 'problemStatus',
            scopedSlots: {
              customRender: 'problemStatus'
            }
          },
          {
            title: '确认人',
            
            dataIndex: 'confirmedPersonName'
          },
          {
            title: '实际完成时间',
            
            dataIndex: 'actualCompletionDate'
          },{
            title:'流程处理',
            dataIndex:'transitionBeanList',
            scopedSlots: {
              customRender: 'transitionBeanList'
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            width:200,
            scopedSlots: {
              customRender: 'action'
            }
          },
        ],
        loading: false,
      }
    },
    methods: {
      handleToJira(row) {
        let _key = row["issueKey"];
        if (!_key) {
          return;
        }
        let $url = `http://jira.evebattery.com/browse/${_key}?auth=` + Vue.ls.get("jtoken");
        window.open($url, "_blank");
      },
      stageDelete(record) {
        this.loading = true
        let param = {
          issueId: record.issueId
        }
        stageDel(param).then((res) => {
          this.loading = false
          if (res.result) {
            if (res.data) {
              this.$message.success('删除成功')
              this.callStageProblem()
            } else {
              this.$message.error('删除失败')
            }
          } else {
            this.$message.error('删除失败：' + res.message)
          }
        }).catch((err) => {
          this.$message.error('删除错误：' + err.message)
        })
      },
      add() {
        this.$refs.addForm.add()
      },
      handleOk() {
        this.callStageProblem()
      },
      callStageProblem() {
        this.loading = true
        getStageRisk({
            issueId: this.issueId
          })
          .then((res) => {
            if (res.result) {
              this.list = res.data
            } else {
              this.$message.error(res.message, 1);
            }
            this.loading = false
          })
          .catch((err) => {
            this.loading = false
            this.$message.error('错误提示：' + err.message, 1)
          });
      },

      callTransiteStage(record,transitionId) {
        this.loading = true
        transiteStage({
            issueId: record.issueId,
            transitionId:transitionId
          })
          .then((res) => {
            if (res.result) {
              if (res.data) {
                this.$message.success('操作成功')
              }else{
                this.$message.error('操作失败')
              }
            } else {
              this.$message.error(res.message, 1);
            }
            this.loading = false
          })
          .catch((err) => {
            this.loading = false
            this.$message.error('错误提示：' + err.message, 1)
          });
      },
    },
    created() {
      this.callStageProblem()
    }
  }
</script>


<style lang='less' scoped=''>
/deep/.ant-table{
    margin: 0 2px;
    margin-top:2px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}
/deep/.ant-table-thead > tr > th{
    font-weight: bold;
    background: #f3f3f3 !important;
}
/deep/.ant-table-small > .ant-table-content > .ant-table-body{
    margin: 0;
}
</style>