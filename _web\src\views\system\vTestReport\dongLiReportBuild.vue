<template>
  <div class="all-div hide-progress-parent" :style="{paddingTop: padding, height: `calc(100vh - 40px - ${width}px);`}">
    <div class="flex-sb-center-row">
      <div style="display: flex; align-items: center;">
        <div class="head_title mr10">
          模型搭建
        </div>
        <div class="search-container">
          <a-select style="min-width: 120px;"
                    v-model="reportTaskObj.type" :options="reportTypeOptions" :disabled="hasQueryParamFlag"
                    showSearch optionFilterProp="children" />
        </div>
      </div>
      <div class="ds-flex mt5">
        <a-button v-if="!hasQueryParamFlag" class="mr10" @click="isShowHistorical = true">历史记录</a-button>
        <div style="position: relative;">
          <div class="normal-btn" :class="{ 'streamer-btn anima': verifyList[0] }" @click="handleAllOk">完成模型搭建</div>
        </div>
      </div>
    </div>

    <div class="component-div" style="margin-top: 8px;" :style="{height: `calc(100vh - 40px - ${width}px - 2*8px - 8px - 37px);`}">
      <component :is="currentComponent"
                 :key="currentComponent"
                 :reportQueryParam="reportTaskObj.queryParam"
                 :reportType="reportTaskObj.type"
                 :hasQueryParamFlag="hasQueryParamFlag"
                 @handleVerify="handleVerify"
                 :width="width"
                 :padding="padding"
                 ref="childComponent">
      </component>
    </div>

    <a-modal title="选择建模类型" :width="350" :footer="null" :maskClosable="false" :keyboard="false"
             :visible="isShowTypeSelect && !reportTaskObj.type" @cancel="closeSelectModal">
      <div style="width: 100%; display: flex; justify-content: center; align-items: center;">
        建模类型：
        <div class="search-container">
          <a-select style="min-width: 160px;" v-model="reportTaskObj.type" :options="reportTypeOptions" showSearch optionFilterProp="children" />
        </div>
      </div>
    </a-modal>

    <!-- 完成模型搭建弹窗 -->
    <a-modal title="完成模型搭建" :width="350" :visible="isShowReportName" @cancel="isShowReportName = false">
      <template slot="footer">
        <div class="ds-flex">
          <a-button @click="isShowReportName = false">取消</a-button>
          <a-button type="primary" @click="exportData">完成模型搭建</a-button>
        </div>
      </template>
      <div class="phase-modal">
        <span class="mr10">建模名称:</span>
        <a-input v-model="reportTaskObj.reportName" style="width: 200px;" placeholder="请填写建模名称" @keyup.enter="exportData"/>
      </div>
    </a-modal>

    <!-- 历史记录弹窗 -->
    <a-modal title="历史记录" :width="1200" :height="600" :bodyStyle="{ padding: 0 }" :visible="isShowHistorical"
             style="padding: 0" :maskClosable="false" @cancel="isShowHistorical = false">

      <pbiSearchContainer>
        <pbiSearchItem :span="6" label='建模名称'>
          <a-input v-model="queryHistoryParam.reportName" @keyup.enter="$refs.historyTable.refresh()"/>
        </pbiSearchItem>
        <pbiSearchItem :span="6" label='建模状态'>
          <a-select v-model="queryHistoryParam.fileStatus" @change="$refs.historyTable.refresh()" style="width: 100%" :allow-clear="true">
            <a-select-option value="0">待处理</a-select-option>
            <a-select-option value="10">进行中</a-select-option>
            <a-select-option value="20">已完成</a-select-option>
            <a-select-option value="30">数据异常</a-select-option>
          </a-select>
        </pbiSearchItem>
        <pbiSearchItem :span="6" label='创建人'>
          <a-input v-model="queryHistoryParam.createName" @keyup.enter="$refs.historyTable.refresh()"/>
        </pbiSearchItem>
        <pbiSearchItem :span="6" type='btn'>
          <a-button class="mr8" @click="$refs.historyTable.refresh()" type="primary">查询</a-button>
          <a-button class="mr8" @click="historyReset()" >重置</a-button>
        </pbiSearchItem>
      </pbiSearchContainer>

      <s-table class="historyTable" :columns="historyColumns" :data="historyData" bordered :rowKey="record1 => record1.id" ref="historyTable">
        <template slot="reportName" slot-scope="text, record, index, columns">
          <a target="_blank" @click="pushToReview(record)" v-if="record.fileStatus == 20" style="margin-right: 12px">{{ text }}</a>
        </template>
        <template slot="action" slot-scope="text, record, index, columns">
          <a-button type="link" @click="handleCopyHistory(record)">复制</a-button>
        </template>
      </s-table>
      <template slot="footer">
        <a-button key="back" @click="isShowHistorical = false">关闭</a-button>
      </template>
    </a-modal>

  </div>
</template>

<script>
import {STable} from "@/components";
import cycleReportBuild from "@/views/system/vTestReport/components/dongLiReport/cycleReportBuild";
import calendarReportBuild from "@/views/system/vTestReport/components/dongLiReport/calendarReportBuild";
import hlTempOrCRateBuild from "@/views/system/vTestReport/components/dongLiReport/hlTempOrCRateBuild";
import {testReportPageList} from "@/api/modular/system/limsManager";
import jsonBigint from "json-bigint";
import {commitDongLiParam} from "@/api/modular/system/cycleReportManager";
import {mapGetters} from "vuex";
import {decodeAndDecompress} from "@/utils/util";

export default {
  name: "dongLiReportBuild",
  components: {
    STable,
    cycleReportBuild,
    calendarReportBuild,
    hlTempOrCRateBuild,
  },
  props: {
    width:{
      type: Number,
      default: 0
    },
    padding:{
      type: String,
      default: '8px'
    },
    type:{
      type: String,
      default: null
    }
  },
  data() {
    return {
      isShowTypeSelect: true,
      currentComponent: '', // 初始显示的组件

      reportTypeOptions: [
        {value: 'Cycle', label: '循环寿命'},
        {value: 'Calendar', label: '日历寿命'},
        {value: 'HlTemp', label: '高低温充放电'},
        {value: 'CRate', label: '倍率充放电'},
      ],

      hasQueryParamFlag: false,
      reportTaskObj: {type: ''},
      verifyList: [false, ''],

      labelCol: {
        sm: {
          span: 11
        }
      },
      wrapperCol: {
        sm: {
          span: 13
        }
      },

      isShowHistorical: false,
      queryHistoryParam: {
        type: "DongLi"
      },
      historyColumns: [
        {
          title: '序号',
          align: 'center',
          width: 50,
          customRender: (text, record, index) => index + 1
        },
        {
          title: '建模名称',
          dataIndex: 'reportName',
          align: 'center',
          width: 100,
          scopedSlots: {customRender: 'reportName'},
        },
        {
          title: '操作时间',
          width: 90,
          align: 'center',
          dataIndex: 'createTime',

        },
        {
          title: '操作人',
          width: 90,
          align: 'center',
          dataIndex: 'createName',

        },
        {
          title: "操作",
          align: "center",
          width: 30,
          scopedSlots: {customRender: "action"}
        },
      ],
      historyData: parameter => {
        return testReportPageList(Object.assign(parameter, this.queryHistoryParam)).then((res) => {
          return res.data
        })
      },

      // 完成模型搭建-建模名称填写弹窗
      isShowReportName: false,
    }
  },
  computed: {
    ...mapGetters(["testTaskFilterData"]),
  },
  watch: {
    'reportTaskObj.type'() {
      switch (this.reportTaskObj.type) {
        case 'Cycle':
          this.currentComponent = 'cycleReportBuild';
          break;
        case 'Calendar':
          this.currentComponent = 'calendarReportBuild';
          break;
        case 'HlTemp':
          this.currentComponent = 'hlTempOrCRateBuild';
          break;
        case 'CRate':
          this.currentComponent = 'hlTempOrCRateBuild';
          break;
        // default:
        //   this.currentComponent = 'cycleReportBuild';
      }
      this.queryHistoryParam.type = this.reportTaskObj.type;
      if (this.$refs.historyTable !== undefined) {
        this.$refs.historyTable.refresh();
      }
    },
  },
  created() {
    // 如果是重新生成
    if (this.testTaskFilterData !== null) {
      let json = jsonBigint({storeAsString: true})
      this.testTaskFilterData.queryParam = json.parse(decodeAndDecompress(this.testTaskFilterData.queryParam))
      this.reportTaskObj = this.testTaskFilterData
      this.hasQueryParamFlag = true
      this.$store.commit("setTaskFilterData", null)
    } else {
      // 刷新后清空路由ID
      if (this.$route.query.id) {
        const query = { ...this.$route.query };
        delete query.id;
        this.$router.replace({ query });
      }
    }
  },
  mounted() {
    if(this.type){
      this.reportTaskObj.type = this.type
      this.isShowTypeSelect = false
    }
  },
  destroyed() {
  },
  methods: {
    closeSelectModal() {
      this.$message.warn("请先选择建模类型")
      this.isShowTypeSelect = false
    },
    /*
    * 历史记录按钮事件
    */
    historyReset() {
      this.queryHistoryParam = {type: this.reportTaskObj.type}
      this.$refs.historyTable.refresh()
    },
    pushToReview(record) {
      window.open("/v_report_preview?id=" + record.id + "&type=" + record.type, "_blank")
    },

    handleCopyHistory(record) {
      // 生成新的数据
      let reportTaskObj = {type:record.type, reportName:record.reportName}
      let json = jsonBigint({storeAsString: true})
      reportTaskObj.queryParam = json.parse(decodeAndDecompress(record.queryParam))
      this.reportTaskObj = reportTaskObj
      this.hasQueryParamFlag = true
      this.isShowHistorical = false
    },

    handleVerify(verifyList) {
      this.verifyList = verifyList
    },

    handleAllOk() {
      if (!this.reportTaskObj.type) {
        return this.$message.warn("请先选择建模类型")
      }

      this.verifyList = this.$refs.childComponent._handleVerify()

      if (!this.verifyList[0]) {
        // 如果校验不通过
        return this.$message.warn(this.verifyList[1])
      } else {
        this.reportTaskObj.queryParam = this.verifyList[1]
        return this.isShowReportName = true
      }
    },
    // 完成模型搭建事件
    exportData() {
      if (!this.reportTaskObj.reportName) return this.$message.warn("请正确填写建模名称")

      let param = {type:this.reportTaskObj.type, reportName:this.reportTaskObj.reportName}
      let json = jsonBigint({storeAsString: true})
      param.queryParam = json.stringify(this.reportTaskObj.queryParam)
      if (this.$route.query.id) {
        param.id = this.$route.query.id
      }

      commitDongLiParam(param).then(res => {
        if (res.success) {
          this.$message.success("创建成功")
          // this.$router.push("/v_report_online_manager?type=" + this.reportTaskObj.type)
          this.$router.push("/v_report_online_manager?type=DongLi") // reportList未升级,type=DongLi
        } else {
          this.$message.warn(res.message)
        }
      })
    },
  }
}
</script>

<style lang="less" scoped>
@import '/src/components/pageTool/style/pbiSearchItem.less';

// 通用
.mt5 {
  margin-top: 5px;
}

.mr10 {
  margin-right: 10px;
}

.mr8 {
  margin-right: 8px;
}

.all-div {
  padding: 8px 12px;
  background-color: #f0f2f5;
}

/* 标题 */
.head_title {
  color: #333;
  font-size: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.head_title::before {
  //width: 8px;
  background: #1890ff;
  margin-right: 8px;
  content: "\00a0"; //填充空格
}

.flex-sb-center-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.component-div {
  width: 100%;
  display: flex;
  background-color: #FFFFFF;
  border-radius: 10px;
  box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
}

.ds-flex {
  display: flex;
}

.normal-btn {
  padding: 5px 10px;
  color: #fff;
  background-color: #1890ff;
  letter-spacing: 2px;
  cursor: pointer;
}

/deep/ .historyTable .ant-table-thead > tr > th {
  padding: 5px !important;
  font-size: 13px !important;
  color: rgba(0, 0, 0, .85) !important;
  font-weight: 500 !important;
}

/deep/ .historyTable .ant-table-tbody > tr > td {
  padding: 0 !important;
  font-size: 12px !important;
  color: #333 !important;
  font-weight: 400 !important;
}

/deep/ .historyTable .ant-table-pagination.ant-pagination {
  float: right;
  margin: 8px 0 0;
  font-size: 12px;
}

/deep/ .s-table-tool {
  padding: 0;
}

</style>