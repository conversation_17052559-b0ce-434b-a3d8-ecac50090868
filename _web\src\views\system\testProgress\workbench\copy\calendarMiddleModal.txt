<template>
	<a-modal
		:title="`${modalData.testName}`"
		:visible="true"
		width="85%"
		:centered="true"
		:cancel-button-props="{ style: { display: 'none' } }"
		okText="关闭1"
		@cancel="handleModelCancel"
		@ok="handleModelCancel"
	>
		<div class="modal-wrapper">
			<div>
				<a-steps :current="current" size="small">
					<a-step v-if="middleCheck !== 'normal'" title="大小中检" />
					<a-step title="数据填写" />
					<a-step title="进箱时间填写" />
				</a-steps>
			</div>
			<!-- 第一步 -->
			<div v-if="current === 0 && middleCheck !== 'normal'">
				<a-spin :spinning="modalLoading">
					<!-- top -->
					<!-- <div class="top-table">
						<a-table
							class="mt10"
							bordered
							:columns="allColumns"
							:rowKey="record => record.cellTestCode"
							:rowSelection="{ onChange: handleSelectRow, type: 'radio' }"
							:data-source="tableData.lifeTestRecordDataMap"
							:pagination="false"
						>
							<span slot="cellTestCode" slot-scope="text, record">
								<div class="blue" @click="handleCopy(text)">{{ text }}<a-icon v-if="text" type="copy" /></div>
							</span>
							<span slot="alias" slot-scope="text, record">
								<div class="blue" @click="handleCopy(text)">{{ text }}<a-icon v-if="text" type="copy" /></div>
							</span>
							<span slot="middleCheck" slot-scope="text, record">
								<a-button type="link" @click="chooseMgData(record)">
									{{ text === "small" ? "小中检" : text === "large" ? "大中检" : "-" }}<a-icon type="edit" />
								</a-button>
							</span>

							<span slot="inDate" slot-scope="text, record">
								{{ tableData.inDate }}
							</span>
							<span slot="actualInDate" slot-scope="text, record">
								{{ tableData.actualInDate }}
							</span>
							<span slot="outDate" slot-scope="text, record">
								{{ tableData.outDate }}
							</span>
							<span slot="day" slot-scope="text, record">
								{{ tableData.day }}
							</span>
							<span slot="orderNumber" slot-scope="text, record">
								{{ tableData.orderNumber }}
							</span>
						</a-table>
					</div> -->
					<div>
						<a-descriptions>
							<a-descriptions-item label="测试项目别名">
								Zhou Maomao
							</a-descriptions-item>
							<a-descriptions-item label="大小中检">
								1810000000
							</a-descriptions-item>
							<a-descriptions-item label="计划开始时间">
								{{ tableData.inDate }}
							</a-descriptions-item>
							<a-descriptions-item label="计划结束时间">
								{{ tableData.outDate }}
							</a-descriptions-item>
							<a-descriptions-item label="存储天数">
								{{ tableData.day }}
							</a-descriptions-item>
							<a-descriptions-item label="存储阶段">
								{{ tableData.orderNumber }}
							</a-descriptions-item>
						</a-descriptions>
					</div>
					<!-- <a-button v-if="lifeTestData.length > 0" type="primary" @click="chooseMgData">选择中检数据</a-button> -->

					<!-- 测试数据选择弹窗 start  -->
					<a-modal
						title="测试数据选择"
						width="85%"
						:height="300"
						:bodyStyle="{ padding: 0 }"
						:visible="mgVisible"
						style="padding: 0"
						:maskClosable="false"
						:centered="true"
						@cancel="handleCloseModal"
						destroyOnClose
					>
						<a-table
							:columns="mgColumns"
							:dataSource="mgData"
							bordered
							:rowKey="record => record.flowId"
							:pagination="false"
							:rowSelection="{
								type: 'radio',
								onSelect: selectTestData,
								getCheckboxProps: getCheckboxProps
							}"
						>
							<template slot="celltestcode" slot-scope="text, record, index, columns">
								<a @click="openStepData(record)" style="text-align: center">{{ text }}</a>
							</template>
						</a-table>
						<template slot="footer" slot-scope="text, record">
							<a-button key="back" @click="handleCloseModal">
								关闭
							</a-button>
						</template>
					</a-modal>
					<!-- 测试数据选择弹窗 end  -->

					<a-divider v-if="lifeTestData.length > 0">详细</a-divider>

					<div class="bottom-table" v-if="lifeTestData.length > 0">
						<a-table
							bordered
							:columns="columns"
							:rowKey="record => record.cellTestCode"
							:data-source="lifeTestData"
							:pagination="false"
						>
							<span v-for="item in columns" :slot="item.dataIndex" slot-scope="text, record">
								<span
									v-if="
										item.dataIndex === 'cellTestCode' || item.dataIndex === 'alias' || item.dataIndex === 'middleCheck'
									"
									>{{ text }}</span
								>

								<a-input
									v-else
									:placeholder="`请输入${item.title}`"
									v-model="lifeTestData[0][item.dataIndex]"
									:disabled="modalData.taskStatus === '已完成' ? true : false"
									@blur="handleInput()"
								/>
							</span>
						</a-table>
					</div>
				</a-spin>
			</div>

			<!-- 第二步 -->
			<template v-if="(current === 0 && middleCheck === 'normal') || (current === 1 && middleCheck !== 'normal')">
				<a-spin :spinning="modalLoading">
					<!-- 数据量小且页面小，所以不做分页 -->
					<!-- top -->
					<div class="top-table">
						<a-table
							class="mt10"
							bordered
							:columns="allColumns"
							:rowKey="record => record.cellTestCode"
							:rowSelection="{ onChange: handleSelectRow, type: 'radio' }"
							:data-source="tableData.lifeTestRecordDataMap"
							:pagination="false"
						>
							<span slot="cellTestCode" slot-scope="text, record">
								<div class="blue" @click="handleCopy(text)">{{ text }}<a-icon v-if="text" type="copy" /></div>
							</span>
							<span slot="alias" slot-scope="text, record">
								<div class="blue" @click="handleCopy(text)">{{ text }}<a-icon v-if="text" type="copy" /></div>
							</span>

							<span slot="inDate" slot-scope="text, record">
								{{ tableData.inDate }}
							</span>
							<span slot="actualInDate" slot-scope="text, record">
								{{ tableData.actualInDate }}
							</span>
							<span slot="outDate" slot-scope="text, record">
								{{ tableData.outDate }}
							</span>
							<span slot="day" slot-scope="text, record">
								{{ tableData.day }}
							</span>
							<span slot="orderNumber" slot-scope="text, record">
								{{ tableData.orderNumber }}
							</span>
						</a-table>
					</div>
					<a-divider v-if="lifeTestData.length > 0">详细</a-divider>
					<div class="bottom-table" v-if="lifeTestData.length > 0">
						<a-table
							bordered
							:columns="columns"
							:rowKey="record => record.cellTestCode"
							:data-source="lifeTestData"
							:pagination="false"
						>
							<span v-for="item in columns" :slot="item.dataIndex" slot-scope="text, record">
								<span
									v-if="
										item.dataIndex === 'cellTestCode' || item.dataIndex === 'alias' || item.dataIndex === 'middleCheck'
									"
									>{{ text }}</span
								>

								<a-input
									v-else
									:placeholder="`请输入${item.title}`"
									v-model="lifeTestData[0][item.dataIndex]"
									:disabled="modalData.taskStatus === '已完成' ? true : false"
									@blur="handleInput()"
								/>
							</span>
						</a-table>
					</div>
				</a-spin>
			</template>

			<!-- 第三步 -->
			<div
				class="time-block"
				v-if="(current === 1 && middleCheck === 'normal') || (current === 2 && middleCheck !== 'normal')"
			>
				<span class="mr10">进箱时间：</span>
				<a-date-picker
					:disabled="modalData.taskStatus === '已完成' ? true : false"
					:default-value="actualInDate"
					@change="handleChangeDate"
				/>
			</div>
		</div>
		<template slot="footer">
			<a-button v-if="current !== 0" type="primary" @click="handleStep(-1)">上一步</a-button>
			<a-button
				v-if="(current !== 2 && middleCheck !== 'normal') || (current !== 1 && middleCheck === 'normal')"
				type="primary"
				@click="handleStep(1)"
				>下一步</a-button
			>
			<a-button @click="handleModelCancel">关闭</a-button>
			<a-button type="primary" v-if="current === 2 || (current === 1 && middleCheck === 'normal')" @click="handleOk"
				>完成</a-button
			>
		</template>
		<step-data ref="stepData"></step-data>
	</a-modal>
</template>

<script>
import Vue from "vue"
import moment from "moment"
import stepData from "../../../lims/folder/stepData"
import { tLimsTestdataScheduleList } from "@/api/modular/system/limsManager"
import { getTestProDetailByTaskId, updateTestProDetail } from "@/api/modular/system/testProgressManager"

export default {
	components: {
		stepData
	},
	name: "TechniciansModal",
	props: {
		modalData: {
			type: Object,
			default: {}
		}
	},
	data() {
		return {
			mgVisible: false,
			current: 0,
			middleCheck: "",
			actualInDate: "",
			modalLoading: false,
			tableData: {},
			mgData: [],
			mgColumns: [
				{
					title: "序号",
					align: "center",
					width: 50,
					customRender: (text, record, index) => {
						if (!record.isChild) {
							return index + 1
						}
					}
				},
				{
					title: "委托单号",
					dataIndex: "folderno",
					align: "center",
					width: 90
				},
				{
					title: "主题",
					dataIndex: "theme",
					align: "center",
					ellipsis: true,
					width: 90
				},
				{
					title: "样品编号",
					width: 90,
					align: "center",
					dataIndex: "orderno"
				},
				{
					title: "测试项目编码",
					width: 90,
					align: "center",
					dataIndex: "testcode"
					//scopedSlots: {customRender: 'updateText'},
				},
				{
					title: "测试项名称",
					width: 90,
					align: "center",
					dataIndex: "testname"
					//scopedSlots: {customRender: 'updateText'},
				},
				{
					title: "测试项目别名",
					width: 90,
					align: "center",
					dataIndex: "alias"
					//scopedSlots: {customRender: 'updateText'},
				},
				{
					title: "测试编码",
					width: 90,
					align: "center",
					dataIndex: "celltestcode",
					scopedSlots: { customRender: "celltestcode" }
				},
				{
					title: "数据位置",
					width: 60,
					align: "center",
					dataIndex: "dataPath",
					ellipsis: true
					//scopedSlots: {customRender: 'updateText'},
				},
				{
					title: "开始时间",
					width: 90,
					align: "center",
					dataIndex: "startTime",
					customRender: (text, record, index) => {
						if (null != text) {
							return moment(text).format("YYYY-MM-DD")
						}
						return text
					}
					//
					//scopedSlots: {customRender: 'updateText'},
				},
				{
					title: "结束时间",
					width: 90,
					align: "center",
					dataIndex: "endTime",
					customRender: (text, record, index) => {
						if (null != text) {
							return moment(text).format("YYYY-MM-DD")
						}
						return text
					}
				},
				{
					title: "设备编号",
					width: 60,
					align: "center",
					dataIndex: "equiptcode"
				},
				{
					title: "通道编号",
					width: 60,
					align: "center",
					dataIndex: "channelno"
				}
			],
			allColumns: [
				{
					title: "序号",
					dataIndex: "index",
					align: "center",
					customRender: (text, record, index) => `${index + 1}`
				},
				{
					title: "测试编码",
					dataIndex: "cellTestCode",
					align: "center",
					scopedSlots: {
						customRender: "cellTestCode"
					}
				},
				{
					title: "测试项目别名",
					dataIndex: "alias",
					align: "center",
					scopedSlots: {
						customRender: "alias"
					}
				},
				{
					title: "大小中检",
					dataIndex: "middleCheck",
					align: "center",
					// customRender: value => {
					// 	return value === "small" ? "小中检" : value === "large" ? "大中检" : "-"
					// }
					scopedSlots: {
						customRender: "middleCheck"
					}
				},
				{
					title: "计划开始时间",
					dataIndex: "inDate",
					align: "center",
					scopedSlots: {
						customRender: "inDate"
					}
				},
				{
					title: "计划结束时间",
					dataIndex: "outDate",
					align: "center",
					scopedSlots: {
						customRender: "outDate"
					}
				},
				{
					title: "存储天数",
					dataIndex: "day",
					align: "center",
					scopedSlots: {
						customRender: "day"
					}
				},
				{
					title: "存储阶段",
					dataIndex: "orderNumber",
					align: "center",
					scopedSlots: {
						customRender: "orderNumber"
					}
				}
			],
			columns: [],
			tableNameMenu: {
				beforeVoltage: "电压",
				beforeInnerres: "内阻",
				afterVoltage: "中检后电压",
				afterInnerres: "中检后内阻",
				volume: "产气量",
				weight: "重量",
				isolateres: "绝缘阻值",
				totalHeight: "总高",
				shoulderHeight: "肩高",
				aPointDiameter: "A点直径",
				bPointDiameter: "B点直径",
				cPointDiameter: "C点直径",
				thicknessOne: "厚度1",
				thicknessTwo: "厚度2",
				thicknessThree: "厚度3",
				thicknessFour: "厚度4",
				thicknessFive: "厚度5",
				thickTopLeft: "上左厚度",
				thickTopMiddle: "上中厚度",
				thickTopRight: "上右厚度",
				thickMiddleLeft: "中左厚度",
				thickMiddle: "中心厚度",
				thickMiddleRight: "中右厚度",
				thickBottomLeft: "下左厚度  ",
				thickBottomMiddle: "下中厚度",
				thickBottomRight: "下右厚度",
				alias: "测试项目别名",
				cellTestCode: "测试编码",
				middleCheck: "大小中检"
			},
			selectedRowKeys: [],
			mgSelectedRowKeys: [],
			selectionRows: [],
			mgSelectionRows: [],

			lifeTestData: [],
			outQueryFlowRecord: {},

			// 文件上传
			postUrl: "/api/sysFileInfo/uploadfile",
			headers: {
				Authorization: "Bearer " + Vue.ls.get("Access-Token")
			},
			fileList: [],

			// 选中的大小中检的flowId
			flowId: ""
		}
	},
	created() {
		this.getTestProDetailByTaskId()
	},
	mounted() {},
	methods: {
		getTestProDetailByTaskId() {
			this.modalLoading = true
			getTestProDetailByTaskId(this.modalData.ordTaskId)
				.then(res => {
					if (!res.success) return this.$message.error("错误提示：" + err.message)
					this.tableData = res.data
					// 去除不需要的字段
					this.tableData.lifeTestRecordDataMap.forEach(v => {
						delete v.heightType
					})
					this.middleCheck = this.tableData.lifeTestRecordDataMap[0].middleCheck
					this.actualInDate = this.tableData.actualInDate
						? moment(this.tableData.actualInDate, "YYYY-MM-DD")
						: moment(new Date(), "YYYY-MM-DD")
				})
				.finally(() => {
					this.modalLoading = false
				})
		},

		updateTestProDetail(params) {
			updateTestProDetail(params).then(res => {
				if (!res.success) return this.$message.error("错误提示：" + err.message)
			})
		},
		openStepData(record) {
			this.outQueryFlowRecord = record

			if (record.flowId != null) {
				this.outQueryFlowRecord.flowId = record.flowId
				this.$refs.stepData.query(this.outQueryFlowRecord, false)
			} else {
				this.$message.warn("测试数据为空")
				return
			}
		},
		handleInput() {
			const params = {
				id: this.modalData.ordTaskId,
				lifeTestRecordDataMap: this.tableData.lifeTestRecordDataMap
			}
			this.updateTestProDetail(params)
		},

		// 点击大小中检按钮,查找对应的数据
		chooseMgData(record) {
			//临时用（有数据）
			// 如果已经有checkData的数据，获取flowId
			if (record.checkData) this.flowId = JSON.parse(record.checkData).flowId

			tLimsTestdataScheduleList({ celltestcode: "04QCE34221101HD152126077-202303090041-0001", alias: "日历寿命1" })
				.then(res => {
					//正式情况
					//tLimsTestdataScheduleList({celltestcode:this.lifeTestData[0].cellTestCode,alias:this.lifeTestData[0].alias}).then((res) => {

					if (res.success) {
						this.mgData = res.data
					} else {
						this.$message.error("查询失败：" + res.message)
					}
				})
				.finally(res => {
					this.mgVisible = true
				})
		},

		handleSelectRow(selectedRowKeys) {
			const temIndex = this.tableData.lifeTestRecordDataMap.findIndex(v => v.cellTestCode === selectedRowKeys[0])
			this.lifeTestData = [this.tableData.lifeTestRecordDataMap[temIndex]]
			this.columns = [this.lifeTestData]
			for (let i in this.lifeTestData[0]) {
				const temObj = {
					title: this.tableNameMenu[i],
					dataIndex: i,
					align: "center",
					scopedSlots: {
						customRender: i
					}
				}

				// 不展示字段
				if (i === "middleCheck") continue

				// 标题头
				if (i === "cellTestCode" || i === "alias" || i === "middleCheck") {
					this.columns.unshift(temObj)
					continue
				}

				// 有中检，第一步
				if (this.middleCheck !== "normal" && this.current === 0 && (i === "afterInnerres" || i === "afterVoltage")) {
					continue
				}

				// 有中检，第二步
				if (this.middleCheck !== "normal" && this.current === 1 && i !== "afterInnerres" && i !== "afterVoltage") {
					continue
				}

				this.columns.push(temObj)
			}
		},
		// 选中数据
		selectTestData(record) {
			this.tableData.lifeTestRecordDataMap[
				this.tableData.lifeTestRecordDataMap.findIndex(v => v.cellTestCode === "jkl-202308210002-0004")
			].checkData = JSON.stringify(record)
			//正式情况
			// this.tableData.lifeTestRecordDataMap[
			// 	this.tableData.lifeTestRecordDataMap.findIndex(v => record.cellTestCode === v.cellTestCode)
			// ].checkData = JSON.stringify(record)
			const params = {
				id: this.modalData.ordTaskId,
				lifeTestRecordDataMap: this.tableData.lifeTestRecordDataMap
			}
			this.updateTestProDetail(params)
		},
		// 展示选择的数据
		getCheckboxProps(record) {
			if (!this.flowId) {
				return {
					props: {
						defaultChecked: false
					}
				}
			}
			return {
				props: {
					defaultChecked: record.flowId === this.flowId
				}
			}
		},
		async handleStep(index) {
			this.lifeTestData = []
			if (this.modalData.taskStatus === "已完成") return (this.current += index)
			// 无中检，两步，有中检，三步
			// 第一步,中检信息校验是否填写完成
			if (
				((this.current === 0 && this.middleCheck !== "normal") ||
					(this.current === 1 && this.middleCheck === "normal")) &&
				index === 1
			) {
				const temList = JSON.parse(JSON.stringify(this.tableData.lifeTestRecordDataMap))
				temList.forEach(e => {
					delete e.afterInnerres
					delete e.afterVoltage
				})
				if (!(await this._handleIsNull(temList))) {
					return this.$warning({
						content: "请将数据完整填写在进行下一步"
					})
				} else {
					const that = this
					this.$confirm({
						title: "是否完成大/小中检",
						onOk() {
							that.current += index
						},
						onCancel() {},
						class: "test"
					})
					return
				}
			}
			// 第二步，数据信息校验是否填写完成
			if (
				((this.current === 1 && this.middleCheck !== "normal") ||
					(this.current === 0 && this.middleCheck === "normal")) &&
				index === 1 &&
				!(await this._handleIsNull(this.tableData.lifeTestRecordDataMap))
			) {
				return this.$warning({
					content: "请将数据完整填写在进行下一步"
				})
			}
			this.current += index
		},

		handleChangeDate(date, dateString) {
			const params = {
				id: this.modalData.ordTaskId,
				actualInDate: dateString
			}
			this.updateTestProDetail(params)
		},

		async _handleIsNull(data) {
			let result = 0
			await data.forEach(v => {
				Reflect.ownKeys(v).forEach(e => {
					if (v[e] === null || v[e] === "") result++
				})
			})
			return result === 0
		},

		// 单击复制
		handleCopy(text) {
			var input = document.createElement("input") // 创建input对象
			input.value = text // 设置复制内容
			document.body.appendChild(input) // 添加临时实例
			input.select() // 选择实例内容
			document.execCommand("Copy") // 执行复制
			document.body.removeChild(input) // 删除临时实例
			this.$message.success("复制成功！")
		},

		/**
		 * 弹窗事件
		 */
		handleModelCancel() {
			this.$emit("cancel")
		},
		handleCloseModal() {
			this.mgVisible = false
			this.flowId = ""
		}，或者无中检，current为0// // 处理表头
		// _handleColums(value, current, middleCheck = false) {
		// 	const temColuns = []
		// 	for (let i in value[0]) {
		// 		const temObj = {
		// 			title: this.tableNameMenu[i],
		// 			dataIndex: i,
		// 			align: "center",
		// 			scopedSlots: {
		// 				customRender: i
		// 			}
		// 		}

		// 		if (i === "isMiddleClick") continue

		// 		// 不展示字段
		// 		// 中检，第二步，不展示
		// 		if ((i === "middleCheck" && current === 2) || i === "heightType" || i === "alias") continue

		// 		// 标题头
		// 		if (i === "cellTestCode") {
		// 			temColuns.unshift(temObj)
		// 			continue
		// 		}

		// 		// 有中检，第一步
		// 		if (middleCheck && current === 1 && (i === "afterInnerres" || i === "afterVoltage")) {
		// 			continue
		// 		}

		// 		// 有中检，第二步
		// 		if (middleCheck && current === 2 && i !== "afterInnerres" && i !== "afterVoltage") {
		// 			continue
		// 		}

		// 		temColuns.push(temObj)
		// 	}
		// 	return temColuns
		// },
	}
}
</script>

<style lang="less" scoped>
@import "../style/calendar.less";
</style>
