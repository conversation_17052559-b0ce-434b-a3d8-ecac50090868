<template>

  <div>

    <div style="float: left;position: relative;z-index: 1;width: 65%;height: 50px;margin-top: 10px;">
      <a-row :gutter="[8,8]">
        <a-col :span="6">
          <a-form-item label="定义产品" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryParam.product" @keyup.enter="getList" @change="getList"/>
          </a-form-item>
        </a-col>
      </a-row>

    </div>
    <div style="float: right;position: relative;z-index: 1;margin-top: 10px">
      <a-button type="primary" style="margin-left: 8px;" @click="openAdd">新增</a-button>
      <a-button type="primary" style="margin-left: 8px;margin-right: 10px" @click="openEdit">编辑</a-button>
    </div>
    <s-table ref="table" :columns="columns" :data="loadData" :rowKey="(record) => record.id"
             :rowSelection="rowSelection" :customRow="customRow"
    >
    </s-table>

    <add-form ref="addForm" @ok="getList"></add-form>
    <edit-form ref="editForm" @ok="getList"></edit-form>
  </div>
</template>

<script>
  import {STable} from '@/components'
  import addForm from './addForm'
  import editForm from './editForm'
  import {
    capacityData, listCapacity,listByPageCapacity
  } from "@/api/modular/system/capacityManage"


  export default {
    components: {
      STable,addForm,editForm
    },
    data() {
      return {
        selectedRowKeys: [],
        selectedRows: [],
        loadData: parameter => {
          return listByPageCapacity(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        labelCol: {

          sm: {
            span: 11
          }
        },
        wrapperCol: {

          sm: {
            span: 13
          }
        },
        queryParam:{},
        columns: [
          {
            title: '序号',
            align: 'center',
            width: 60,
            customRender: (text, record, index) => index + 1
          }, {
            title: '产能区分',
            dataIndex: 'capacitySplit',
            align: 'center',
            width: 90
          }, {
            title: '工程区分',
            width: 90,
            align: 'center',
            dataIndex: 'projectSplit',
            //scopedSlots: {customRender: 'updateText'},
          },
          {
            title: '国家',
            width: 90,
            align: 'center',
            dataIndex: 'country',
            //scopedSlots: {customRender: 'updateText'},
          },{
            title: '省份',
            width: 90,
            align: 'center',
            dataIndex: 'province',
            //scopedSlots: {customRender: 'updateText'},
          },{
            title: '城市',
            width: 90,
            align: 'center',
            dataIndex: 'city',
            //scopedSlots: {customRender: 'updateText'},
          },{
            title: '地区',
            width: 90,
            align: 'center',
            dataIndex: 'area',
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '分区',
            width: 90,
            align: 'center',
            dataIndex: 'split',
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '工厂代码',
            width: 90,
            align: 'center',
            dataIndex: 'factoryCode',
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '产线编号',
            width: 90,
            align: 'center',
            dataIndex: 'lineCode',
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '产品类型',
            width: 90,
            align: 'center',
            dataIndex: 'productType',
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '定义产品',
            width: 90,
            align: 'center',
            dataIndex: 'product',
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '单线ppm',
            width: 90,
            align: 'center',
            dataIndex: 'ppm',
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '计算产能',
            width: 90,
            align: 'center',
            dataIndex: 'countCapacity',
            customRender: (text, record, index) => text ? text + 'GWh' : null
          }, {
            title: '标称产能',
            width: 90,
            align: 'center',
            dataIndex: 'standardCapacity',
            customRender: (text, record, index) => text ? text + 'GWh' : null
          }, {
            title: '预测PPAP',
            width: 90,
            align: 'center',
            dataIndex: 'planPpap'
          }, {
            title: '备注',
            width: 90,
            align: 'center',
            dataIndex: 'remark'
          }
        ],
        data: [],

      }
    },
    created() {
    },
    computed:{
      rowSelection(){
        return {
          columnWidth: 30,
          type:'radio',
          selectedRowKeys: this.selectedRows.map((row) => row.id),
          onChange: (selectedRowKeys, selectedRows) => {
            this.selectedRows = selectedRows
          }
        }
      },
    },
    mounted() {


    },
    methods: {
      customRow(record){
        return {
          props: {

          },
          on: { // 事件
            click: (event) => {
              this.selectedRows = []
              this.selectedRows.push(record);

            },
          },

        };
      },

      openAdd() {
        this.$refs.addForm.add()
      },

      openEdit() {
        let num = this.selectedRows.length
        if (num != 1) {
          this.$message.warn('请选中一条数据编辑')
          return
        }
        this.$refs.editForm.edit(this.selectedRows[0])
      },
      getList() {


        this.$refs.table.refresh()
      },

    }
  }
</script>

<style lang="less" scoped>
  .leftCard {
    width: 100%;
    height: 100%;

    #chinaMap {
      width: 100%;
      height: 100%;
    }
  }

  /deep/ .ant-table-tbody > tr > td {
    padding: 2px 0px;

  }

  /deep/ .ant-table-thead > tr > th {
    padding: 2px 0px;

  }
</style>

