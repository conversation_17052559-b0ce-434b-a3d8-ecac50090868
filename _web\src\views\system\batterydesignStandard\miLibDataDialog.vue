<template>
  <div>
    <a-modal id="miModalId" title="MI选型导入" :width="1000" :visible="visible" @cancel="handleCancel">
      <a-spin :spinning="isLoading">
        <a-table id="miStandardId" :data-source="resultData"  bordered>
          <a-table-column align="center" title="MI版本" data-index="miVersionName">
            <template slot-scope="text, record">
              <a @click="checkMiVersion(record)">{{ text }}</a>
            </template>
          </a-table-column>
          <a-table-column align="center" title="MI附图版本" data-index="miAttachVersionName">
            <template slot-scope="text, record">
              <a @click="checkMiAttachVersion(record)">{{ text }}</a>
            </template>
          </a-table-column>
          <a-table-column align="center" title="文件变更履历" data-index="documentChangeHistoryName">
            <template slot-scope="text, record">
              <a @click="checkDocumentChangeHistory(record)">{{ text }}</a>
            </template>
          </a-table-column>
          <a-table-column align="center" title="变更评审资料(PDF)" data-index="changeReviewDataName">
            <template slot-scope="text, record">
            <span v-if="text==null">
              <a>上传</a>
            </span>
              <span v-else><a @click="xxx(record.changeReviewDataId)">{{text}}</a>
             </span>
            </template>
          </a-table-column>
          <a-table-column align="center" title="适用产品名称" data-index="productName" />
          <a-table-column align="center" title="适应工厂-产线" data-index="produceLine" />
          <a-table-column align="center" title="更新日期" data-index="updateTime" :customRender="renderUpdateTime"/>
          <a-table-column align="center" title="创建人" data-index="createName" />
          <a-table-column align="center" key="action" title="操作">
          <template slot-scope="text, record">
            <span>
              <a @click="importMI(record)">导入</a>
            </span>
          </template>
        </a-table-column>
        </a-table>
      </a-spin>
      <template slot="footer">
        <div></div>
      </template>
    </a-modal>
  </div>
</template>
<script>
import {
  getMIStandardLibListDesc, updateAndReImportData,
} from '@/api/modular/system/gCylinderMILibManage'
import { EventBus } from '@/api/modular/system/eventBus'
import moment from "moment/moment";
import Vue from "vue";
import { ACCESS_TOKEN } from "@/store/mutation-types";
  export default {
    components: {},
    data() {
      return {
        visible: false,
        batteryId: null,
        fBatteryId: null,
        isLoading: false,
        resultData: [],
        currentPageLibId: null,
        headers: {
          Authorization: 'Bearer ' + Vue.ls.get(ACCESS_TOKEN),
        },
      };
    },
    created() {
      this.getData()
    },
    mounted() {
      document.getElementsByClassName("ant-layout-content")[0].style.backgroundColor = 'white';
    },
    methods: {
      getData() {
        getMIStandardLibListDesc().then(res => {
          this.resultData = res.data
        })
      },
      importMI(record) {
        let update = {}
        update.id = record.id
        if (!this.currentPageLibId) {
          this.currentPageLibId = 0
        }
        this.isLoading = true
        updateAndReImportData(update,this.currentPageLibId,this.batteryId).then(res => {
          if (res.success) {
            this.$message.success('导入成功')
            this.$emit('importSuccess',false)
            EventBus.$emit('changeHisEvent', record.id)
            EventBus.$emit('miVersionEvent', record.id)
            EventBus.$emit('miAttachVerEvent', record.id)
            setTimeout(() => {
              this.isLoading = false
              this.visible = false
            },500)
          } else {
            this.$message.error('导入失败：' + res.message)
          }
        })
      },
      checkMiVersion(record) {
        // this.$router.push(
        //   {
        //     path: "/g_cylinder_mi_version",
        //     query: {
        //       record: record
        //     },
        //   }
        // )
      },
      checkMiAttachVersion(record) {
        // this.$router.push(
        //   {
        //     path: "/g_cylinder_mi_attach_version",
        //     query: {
        //       record: record
        //     },
        //   }
        // )
      },
      checkDocumentChangeHistory(record) {
        // this.$router.push(
        //   {
        //     path: "/g_cylinder_doc_change_history",
        //     query: {
        //       record: record
        //     },
        //   }
        // )
      },
      renderUpdateTime(text) {
        return text == null ? '' : moment(new Date(text)).format('YYYY-MM-DD')
      },
      miChooseModelImport(impBatteryId, libraryId) {
        this.batteryId = impBatteryId
        this.currentPageLibId = libraryId
        this.getData()
        this.visible = true
      },
      handleCancel() {
        this.visible = false
      },
    }
  }
</script>
<style lang="less" scoped>
/deep/.ant-table-bordered.ant-table-empty .ant-table-placeholder {
  border: 1px solid black;
}
#miStandardId > div > div > div > div > div > table{
  border: 0;
}
#miStandardId > div > div > div > div > div > table > .ant-table-thead > tr > .ant-table-align-center{
  background-color: #F5F5F5;
  border-bottom: 1.5px solid #C0C0C0;
}
#miStandardId > div > div > div > div > div > table > .ant-table-tbody > tr > td{
  border-bottom: 1.5px solid #C0C0C0;
}
#miStandardId > div > div > div > div > div > table > .ant-table-tbody > tr:last-child > td{
  border: 0;
}
#miModalId > div > div > div > div > .ant-modal-title {
  color: dodgerblue;
  font-size: 20px;
  font-weight: bold;
}
/deep/.ant-table-thead > tr > th, .ant-table-tbody > tr > td {
  padding: 10px 10px;
  overflow-wrap: break-word;
}
/deep/.ant-breadcrumb a, .ant-breadcrumb span {
  color: black;
  font-weight: bold;
}
/deep/.ant-table-bordered .ant-table-tbody > tr > td {
  border: 0;
}
/deep/.ant-table-thead > tr > th {
  border: 0;
}
</style>
