<template>
  <div>

    <div class="title">
      <span style="float:left;">立项评审</span>

    </div>
    <div>
      <a-table style="background:#fff" size="middle"
               :rowKey="(record) => record.id" :columns="columns" :dataSource="dataSource" :loading="loading"
               >


        <div slot="result" slot-scope="text,record">
          <a @click="openSee(record)">查阅</a>
        </div>
        <div slot="checkNum" slot-scope="text,record">
          <a @click="openSee(record)">{{text}}</a>
        </div>
        <div slot="action" slot-scope="text,record">
          <div v-if="record.unCheckNum==0" @click="gotoCheck(record)" >评审</div>
          <a v-if="record.unCheckNum!=0" @click="gotoCheck(record)">评审</a>
        </div>

      </a-table>
    </div>

    <a-modal
      :title="date+'月评审清单明细'"
      :width="width"
      v-model="visible"
      :zIndex="parseInt(2)"
      :bodyStyle="{overflow:'hidden',overflowY: 'scroll',maxHeight:clientHeight - 180 + 'px'}"
      @cancel="() => visible=false"
    >


      <template slot="footer">
           <a-button key="back" @click="() => visible=false">
          关闭
        </a-button>

      </template>

      <div>

        <a-table style="background:#fff" size="small"

                 :scroll="{x:bigClient?false:true}"

                 :rowKey="(record) => record.issueId" :columns="columnsIn" :dataSource="inDataSource" :loading="loading"
                 >

          <template slot="cate" slot-scope="text,record">
                <div v-if="text">{{text}}</div>
                <div v-if="record.cate2">-{{record.cate2}}<span v-if="record.cate3">-{{record.cate3}}</span></div>
          </template>

          <span slot-scope="text,record" slot="affiliatedPlatform">
            {{text}}
          </span>

          <div slot="department" slot-scope="text,record" style="text-align:center">
            {{text != null?text+(record.data.department3 != null?'-'+record.data.department3:'') :''}}
          </div>


          <div slot="projectName" slot-scope="text">
            <clamp :text="text" :sourceText="[text]" :isCenter="true"></clamp>
          </div>


          <div slot="projectLevel" slot-scope="text" style="width: 100%;text-align: center">
            {{levels[text]}}

          </div>

          <div slot="researchContent" slot-scope="text">
            <clamp :text="text" :sourceText="[text]" :isCenter="true"></clamp>
          </div>

          <div slot="projectBackGround" slot-scope="text">
            <clamp :text="text" :sourceText="[text]" :isCenter="true"></clamp>
          </div>


          <div slot="cate1">1级<br/>分类</div>
          <div slot="cate2">2级<br/>分类</div>
          <div slot="cate3">3级<br/>分类</div>
          <div slot="projectNameTitle">课题名称</div>
          <div slot="projectLevelTitle">课题等级</div>
          <div slot="projectLeaderTitle">课题负责人</div>
          <div slot="platformAndTopic">平台课题</div>


          <template slot="reviewResult1" slot-scope="text">
            <div style="text-align:center">
              <span class="green" v-if="text == 1">{{reviewRes[text]}}</span>
              <span class="red" v-else-if="text == 2">{{reviewRes[text]}}</span>
              <span class="yellow" v-else-if="text == 3">{{reviewRes[text]}}</span>

            </div>
          </template>

          <template slot="reviewOpinion1" slot-scope="text">
            <clamp :isCenter="true" :text="text" :sourceText="[text]"></clamp>
          </template>

          <template slot="reviewResult2" slot-scope="text">
            <div style="text-align:center">
              <span class="green" v-if="text == 1">{{reviewRes[text]}}</span>
              <span class="red" v-else-if="text == 2">{{reviewRes[text]}}</span>
              <span class="yellow" v-else-if="text == 3">{{reviewRes[text]}}</span>

            </div>
          </template>

          <template slot="reviewOpinion2" slot-scope="text">
            <clamp :text="text" :sourceText="[text]" :isCenter="true"></clamp>
          </template>





        </a-table>
      </div>

    </a-modal>


  </div>
</template>

<script>

  import topiclist from './topiclist'
  import moment from 'moment'
  import {clamp} from '@/components'
  import { ALL_APPS_MENU } from '@/store/mutation-types'
  import {
    getTopicReviewMonthList,getTopicReviewMonthCheckedList
  } from "@/api/modular/system/topic"
  import Vue from "vue";
  import {mapActions} from "vuex";
  import addForm from "./addForm";
  import editForm from "./editForm";

  export default {
    components: {

      clamp
    },
    data() {
      return {
        dataSource: [],
        inDataSource: [],
        width: document.documentElement.clientWidth * 0.8,
        height: document.documentElement.clientHeight * 0.3,
        bigClient: document.documentElement.clientHeight > 700,
        height1: document.documentElement.clientHeight * 0.3 + 100,
        clientHeight: document.documentElement.clientHeight,
        reviewRes: ['/', '通过', '不通过', '再确认'],
        levels: ['', '', 'S', 'A','B'],
        loading: true,
        date: null,
        visible: false,

        columns: [
          {
            title: '序号',
            dataIndex: 'index',
            align: 'center',
            width: 40,
            customRender: (text, record, index) => ( <div
            class = 'divcls div_border_right div_btns' > {index+1} </div>)
          },
          {
            dataIndex: 'dateMonth',
            width: 180,
            align: 'center',
            title: '类别',
            customRender: (text, record, index) => {
              return text + '月 立项申请'
            },
          },
          {
            dataIndex: 'checkStatus',
            width: 120,
            title: '立项评审状态',
            customRender: (text, record, index) => {
              if (text == 1) {
                return '已完成'
              }
              if (text == 0) {
                return '待完成'
              }
            },
            align: 'center',
          }, {
            dataIndex: 'unCheckNum',
            width: 120,
            title: '待评审数',
            align: 'center',
          }, {
            dataIndex: 'checkNum',
            width: 120,
            title: '已评审数',
            scopedSlots: {customRender: 'checkNum'},
            align: 'center',
          }, {
            dataIndex: 'result',
            width: 120,
            title: '评审结果',
            scopedSlots: {customRender: 'result'},
            align: 'center',
          }, {
            dataIndex: 'action',
            width: 120,
            title: '操作',
            scopedSlots: {customRender: 'action'},
            align: 'center',
          },
        ],
        columnsIn: [

          {
            title: '序号',
            dataIndex: 'index',
            align: 'center',
            width: 40,
            customRender: (text, record, index) => ( <div
            class = 'divcls div_border_right div_btns' > {index+1} </div>)
          },
          {
            title: '平台一级分类',
            dataIndex: 'affiliatedPlatform1',
            align: 'center',
            width: 80,
            scopedSlots: {customRender: 'affiliatedPlatform'},
          },
          {
            title: '平台二级分类',
            dataIndex: 'affiliatedPlatform2',
            align: 'center',
            width: 80,
            scopedSlots: {customRender: 'affiliatedPlatform'},
          },
          {
            dataIndex: 'cateName',
            width: 80,
            align: 'center',
            title: '课题分类',
            //scopedSlots: {customRender: 'cate'},
          },
          {
            dataIndex: 'topicName',
            width: 120,
            title: '课题名称',
            align:'center',
            scopedSlots: {customRender: 'projectName'},
          },

          {
            title: '研究内容',
            dataIndex: 'researchContent',
            width: 120,
            align:'center',
            scopedSlots: {customRender: 'researchContent'},
          },
          {
            title: '课题背景',
            dataIndex: 'projectBackground',
            width: 120,
            align:'center',
            scopedSlots: {customRender: 'projectBackGround'},
          },

          {
            slots: {title: 'projectLevelTitle'},
            dataIndex: 'projectLevel',
            scopedSlots: {customRender: 'projectLevel'},
            width: 80,
            align: 'center',

          },
          {

            dataIndex: 'projectLeader',
            slots: {title: 'projectLeaderTitle'},
            width: 80,
            align:'center',
            scopedSlots: {customRender: 'projectLeader'},
          },
          {
            title: '部门',
            dataIndex: 'deptName',
            width: 100,
            align:'center',
            //scopedSlots: {customRender: 'department'},
          },

          {
            title: '评审结果',
            dataIndex: 'reviewResult1',
            width: 80,
            align: 'center',
            scopedSlots: {customRender: 'reviewResult1'},
          },
          {
            title: '评审意见',
            dataIndex: 'reviewOpinion1',
            align: 'center',
            scopedSlots: {customRender: 'reviewOpinion1'},
            width: 100
          },
          {
            title: '二次评审结果',
            dataIndex: 'reviewResult2',
            align: 'center',
            width: 90,
            scopedSlots: {customRender: 'reviewResult2'},
          },
          {
            title: '二次评审意见',
            dataIndex: 'reviewOpinion2',
            width: 120,
            align: 'center',
            scopedSlots: {customRender: 'reviewOpinion2'},
          },

        ],
      }
    },
    methods: {
      openSee(record){
        this.date = record.dateMonth
        this.visible = true
        getTopicReviewMonthCheckedList({dateMonth:record.dateMonth}).then(res => this.inDataSource = res.data)
      },
      getList() {
        getTopicReviewMonthList({}).then(res => {
          this.dataSource = res.data
          this.loading = false
        })
      },
      ...mapActions(['MenuChange']),
      switchApp() {
        const applicationData = Vue.ls.get(ALL_APPS_MENU)
        this.MenuChange(applicationData[0]).then((res) => {}).catch((err) => {
          this.$message.error('错误提示：' + err.message, 1)
        })
      },
      gotoCheck(record){
        //this.switchApp()
        this.$router.push({
          path: "/topic_project",
          query: {
            type:1,
            dateMonth:record.dateMonth
          },
        });
      }
    },
    created() {
    },
    mounted() {
      this.getList()
    }
  }
</script>

<style lang="less" scoped=''>
  /deep/ .ant-table-small > .ant-table-content > .ant-table-header > table > .ant-table-thead > tr > th, 
  /deep/ .ant-table-small > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th,
   /deep/ .ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-header > table > .ant-table-thead > tr > th,
    /deep/ .ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-thead > tr > th,
    /deep/  .ant-table-small > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table > .ant-table-thead > tr > th, 
     /deep/ .ant-table-small > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table > .ant-table-thead > tr > th,
      /deep/ .ant-table-small > .ant-table-content > .ant-table-fixed-left > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-thead > tr > th,
       /deep/ .ant-table-small > .ant-table-content > .ant-table-fixed-right > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-thead > tr > th{
        background-color: #f5f5f5;
  }

  .title {
    background: #fff;
    padding: 15px 24px;
    font-weight: 700;
    font-size: 16px;
    line-height: 1.5;
    text-align: right;
  }


  .red {
    display: inline-block;
    padding: 2px 8px;
    background: #ff3333;
    text-align: center;
    color: #fff;
  }

  .yellow {
    display: inline-block;
    padding: 2px 8px;
    background: #fac858;
    text-align: center;
    color: #fff;
  }

  .green {
    display: inline-block;
    padding: 2px 8px;
    background: #58a55c;
    text-align: center;
    color: #fff;
  }
</style>