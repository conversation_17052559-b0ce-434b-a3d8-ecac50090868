<template>
<div style="background:#fff">
    <a-button type="primary" @click="viewAll" style="margin:8px 0;">
      全部产线
    </a-button>
    <a-button type="primary" @click="$refs.addForm.add(null)" style="margin:8px 0;margin-left:8px">
      新增产线
    </a-button>
    <div class="main">
        <div class="left_main_werk">
            <a-spin :spinning="wearkloading">
                <s-table
                ref="wearktable"
                :pagination="false"
                :showPagination="false"
                :columns="wearkcolumns"
                :data="wearkloadData"
                :scroll="{ y: wHeight }"
                :rowKey="(record) => record.werks"
                >
                <span slot="werks" slot-scope="text, record">
                    <input
                        v-if="record.fresfactory == 1"
					    style='width:60px'
						@change="(e) => {
							const { value } = e.target
							record.werks = value
                            callWerkConfigSave()
						}"
						:value="record.werks"
					/>
                    <span v-else>{{text}}</span>
                </span>
                <span slot="namecode" slot-scope="text, record">
                    <input
					    style='width:60px'
						@change="(e) => {
							const { value } = e.target
							record.namecode = value
                            callWerkConfigSave()
						}"
						:value="record.namecode"
					/>
                </span>
                <span slot="action" slot-scope="text, record">
                    <a @click="$refs.addForm.add(record)">创建产线</a>
                    <a-divider type="vertical" />
                    <a @click="viewline(record)">查看产线</a>
                </span>
            </s-table>
            </a-spin>
        </div>
        <div class="right_main_werk">
            <a-spin :spinning="wearklineloading">
                <s-table
                ref="wearklinetable"
                :columns="wearklinecolumns"
                :data="wearklineloadData"
                :rowKey="(record) => record.id"
                :scroll="{ y: windowHeight }"
                >
                <span slot="action" slot-scope="text, record">
                    <a @click="$refs.editForm.edit(record)">编辑</a>
                    <a-divider type="vertical" />
                    <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => doDel(record)">
                    <a>删除</a>
                    </a-popconfirm>
                </span>
            </s-table>
            </a-spin>
        </div>
    </div>
    <add-form :werks="werks" ref="addForm" @ok="handleOk" />
    <edit-form :werks="werks" ref="editForm" @ok="handleOk" />
</div>
</template>

<script>
import { getWerklineList, sysWerklineDelete,sysWerkConfigSave } from '@/api/modular/system/werklineManage'
import { getwerks } from "@/api/modular/system/bomManage"
import addForm from './addForm'
import editForm from './editForm'
import { STable } from '@/components'
export default {
    components: {
      addForm,
      editForm,
      STable
    },
    data() {
        return {
            werks:[],
            wHeight: document.documentElement.clientHeight - 165,
            windowHeight: document.documentElement.clientHeight - 215,// +60
            wearkloading:false,
            wearklineloading:false,
            wearkqueryParam:{},
            wearklinequeryParam:{},
            wearkcolumns:[
                {
                    title: '工厂代码',
                    dataIndex: 'werks',
                    scopedSlots: {
							customRender: 'werks'
					}
                },
                {
                    title: '工厂代号',
                    dataIndex: 'namecode',
                    scopedSlots: {
							customRender: 'namecode'
					}
                },
                {
                    title: '工厂',
                    dataIndex: 'name1'
                },
                {
                    title: '操作',
                    dataIndex: 'action',
                    scopedSlots: { customRender: 'action' }
                },
            ],
            wearklinecolumns:[
                {
                    title: '工厂代码',
                    dataIndex: 'werkNo'
                },
                {
                    title: '产线',
                    dataIndex: 'lineName'
                },
                {
                    title: '操作',
                    dataIndex: 'action',
                    scopedSlots: { customRender: 'action' }
                },
            ],
            wearkloadData: parameter => {
                return getwerks(Object.assign(parameter, this.wearkqueryParam)).then((res) => {
                    this.werks = res.data
                    return res.data
                })
            },
            wearklineloadData: parameter => {
                return getWerklineList(Object.assign(parameter, this.wearklinequeryParam)).then((res) => {
                    return res.data
                })
            },
        }
    },
    methods: {
        callWerkConfigSave(){
            this.wearkloading = true
            sysWerkConfigSave({code:'WERK_CODE',text:JSON.stringify(this.werks)})
            .then((res)=>{
                if (res.success) {

                } else {
                    this.$message.error(res.message,1);
                }
                this.wearkloading = false
            })
            .catch((err)=>{
                this.wearkloading = false
                this.$message.error('错误提示：' + err.message,1)
            });
		},
        viewAll(){
            this.wearklinequeryParam.werkNo = null
            this.$refs.wearklinetable.refresh()
        },
        viewline(row){
            this.wearklinequeryParam.werkNo = row.werks
            this.$refs.wearklinetable.refresh()
        },
        handleOk(){
            this.$refs.wearklinetable.refresh()
        },
        doDel(row){
            const {id} = row
            this.wearklineloading = true
            sysWerklineDelete({id:id}).then((res) => {
                if (res.success) {
                    this.$message.success('删除成功')
                    this.$refs.wearklinetable.refresh()
                }else{
                    this.$message.error('删除失败：' + res.message)
                }
                setTimeout(() => {
                    this.wearklineloading = false
                }, 1000)
            })
            .catch((err) => {
                setTimeout(() => {
                    this.wearklineloading = false
                }, 1000)
                this.$message.error('出现异常：' + err.message)
            })
        },
    },
    created(){
    }
}
</script>

<style>
.main{
	display: flex;
 	width: 100%;
}
.left_main_werk {
	flex: 1;
	overflow: auto;
}
.right_main_werk{
    flex: 1;
	overflow: auto;
}
input{
	color: rgba(0, 0, 0, 0.65);
	height: 23px;
    line-height: 23px;
	width: 60px;
	font-size: 14px;
    border: 1px solid #d9d9d9;
	outline: none;
}
</style>