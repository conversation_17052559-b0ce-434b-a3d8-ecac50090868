<template>
  <div :id="containerId" v-if="hasProps" class="container">
    <div class="right-btn">
      <div class="pageNum">
        <input v-model.number="currentPage" type="number" class="inputNumber" @input="inputEvent()"> / {{pageCount}}
      </div>
      <div @click="changePdfPage('first')" class="turn">首页</div>
      <div @click="changePdfPage('pre')" class="turn-btn" :style="currentPage===1?'cursor: not-allowed;':''">上一页</div>
      <div @click="changePdfPage('next')" class="turn-btn" :style="currentPage===pageCount?'cursor: not-allowed;':''">下一页</div>
      <div @click="changePdfPage('last')" class="turn">尾页</div>
      <div @click="scaleUp()" class="turn-btn">放大</div>
      <div @click="scaleDown()" class="turn-btn">缩小</div>
      <div @click="rotate()" class="turn-btn">旋转</div>
      <div @click="shift('left')" class="turn-btn">左移</div>
      <div @click="shift('right')" class="turn-btn">右移</div>
      <div @click="shift('top')" class="turn-btn">上移</div>
      <div @click="shift('bottom')" class="turn-btn">下移</div>
      <div @click="downPDF()" class="turn" v-if="isShowDownloadBtn">下载</div>
    </div>

    <div class="pdfArea">
      <pdf :src="src" :ref="pdfRef" :page="currentPage" @num-pages="pageCount=$event"  @page-loaded="currentPage=$event" @loaded="loadPdfHandler" @link-clicked="currentPage = $event" style="display: inline-block;width:100%" />
    </div>
  </div>
</template>

<script>
import pdf from 'vue-pdf'

export default {
  name: "pdfViewer",
  props: {
    // src pdf资源路径
    src: {
      type: String,
      default: () => {
        return null;
      },
    },
    // 该pdf-viewer组件唯一id
    containerId: {
      type: String,
      default: () => {
        return 'fileId';
      },
    },
    // 该pdf-viewer组件唯一ref
    pdfRef: {
      type: String,
      default: () => {
        return 'pdfRef';
      },
    },
    // 是否展示下载按钮
    isShowDownloadBtn: {
      type: Boolean,
      default: () => {
        return false;
      }
    },
  },
  components: {
    pdf
  },
  computed: {
    hasProps() {
      return this.src && this.containerId && this.pdfRef;
    }
  },
  created() { },
  mounted() {
    this.$nextTick(() => {
      this.prohibit();
    })
  },
  data() {
    return {
      scale: 100,  // 开始的时候默认和容器一样大即宽高都是100%
      horizontalDisplacement: 0, // 水平位移
      verticalDisplacement: 0, // 垂直位移
      rotation: 0, // 旋转角度
      currentPage: 0, // 当前页数
      pageCount: 0, // 总页数
    }
  },
  methods: {
    rotate() {
      this.rotation += 90;
      this.$refs[this.pdfRef].$el.style.transform = `rotate(${this.rotation}deg) translateX(${this.horizontalDisplacement}px) translateY(${this.verticalDisplacement}px)`;
      console.log(`当前旋转角度: ${this.rotation}°`);
    },
    // 解决旋转后（比如旋转90° 270°等水平颠倒的状况），再平移会水平垂直颠倒的问题
    shift(dir) {
      if (dir == "left") {
        this.horizontalDisplacement -= 10;
      } else if (dir == "right") {
        this.horizontalDisplacement += 10;
      } else if (dir == "top") {
        this.verticalDisplacement -= 10;
      } else if (dir == "bottom") {
        this.verticalDisplacement += 10;
      }
      const radian = (this.rotation * Math.PI) / 180;
      const newX = Math.cos(radian) * this.horizontalDisplacement + Math.sin(radian) * this.verticalDisplacement;
      const newY = -Math.sin(radian) * this.horizontalDisplacement + Math.cos(radian) * this.verticalDisplacement;
      this.$refs[this.pdfRef].$el.style.transform = `rotate(${this.rotation}deg) translateX(${newX}px) translateY(${newY}px)`;
    },
    // 页面回到顶部
    toTop() {
      document.getElementById(this.containerId).scrollTop = 0
    },
    // 输入页码时校验
    inputEvent() {
      if (this.currentPage > this.pageCount) {
        // 1. 大于max
        this.currentPage = this.pageCount
      } else if (this.currentPage < 1) {
        // 2. 小于min
        this.currentPage = 1
      }
    },
    // 切换页数
    changePdfPage(val) {
      if (val === 'pre' && this.currentPage > 1) {
        // 切换后页面回到顶部
        this.currentPage--
        this.toTop()
      } else if (val === 'next' && this.currentPage < this.pageCount) {
        this.currentPage++
        this.toTop()
      } else if (val === 'first') {
        this.currentPage = 1
        this.toTop()
      } else if (val === 'last' && this.currentPage < this.pageCount) {
        this.currentPage = this.pageCount
        this.toTop()
      }
      this.$refs[this.pdfRef].$el.style.transform = `rotate(0deg) translateX(0px) translateY(0px)`;
      this.rotation = 0;
      this.horizontalDisplacement = 0;
      this.verticalDisplacement = 0;
    },

    // pdf加载时
    loadPdfHandler(e) {
      // 加载的时候先加载第一页
      // console.log(e);
      this.currentPage = 1
    },

    // 禁用鼠标右击、F12 来禁止打印和打开调试工具
    prohibit() {
      let node = document.querySelector(`#${this.containerId}`);
      node.oncontextmenu = function () {
        return false
      }
      node.onkeydown = function (e) {
        console.log("禁用", e);
        if (e.ctrlKey && (e.keyCode === 65 || e.keyCode === 67 || e.keyCode === 73 || e.keyCode === 74 || e.keyCode === 80 || e.keyCode === 83 || e.keyCode === 85 || e.keyCode === 86 || e.keyCode === 117)) {
          return false
        }
        if (e.keyCode === 18 || e.keyCode === 123) {
          return false
        }
      }
    },
    //放大
    scaleUp() {
      if (this.scale == 300) {
        return;
      }

      this.scale += 5;
      this.$refs[this.pdfRef].$el.style.width = parseInt(this.scale) + "%";
      this.$refs[this.pdfRef].$el.style.height = parseInt(this.scale) + "%";
      console.log(`当前缩放倍数: ${this.scale}%`);
    },
    //缩小
    scaleDown() {
      if (this.scale == 30) {
        return;
      }
      this.scale += -5;
      this.$refs[this.pdfRef].$el.style.width = parseInt(this.scale) + "%";
      this.$refs[this.pdfRef].$el.style.height = parseInt(this.scale) + "%";
      console.log(`当前缩放倍数: ${this.scale}%`);
    },
    // 下载
    downPDF() { // 下载 pdf
      var url = this.src
      var tempLink = document.createElement("a");
      tempLink.style.display = "none";
      tempLink.href = url;
      tempLink.setAttribute("download", 'XXX.pdf');
      if (typeof tempLink.download === "undefined") {
        tempLink.setAttribute("target", "_blank");
      }
      document.body.appendChild(tempLink);
      tempLink.click();
      document.body.removeChild(tempLink);
    },
  }
}
</script>

<style lang="less" scoped>
#container {
  overflow: auto;
  height: 800px;
  width: 100%;
  display: flex;
  /* justify-content: center; */
  position: relative;
}

.container {
  position: relative;
}

/* 右侧功能按钮区 */
.right-btn {
  zoom: 0.7;
  position: fixed;
  //position: absolute;
  right: 0%;
  // bottom: 15%;
  top: 5%;
  width: 120px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  z-index: 99;
  user-select: none;
}

.pdf-container {
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  height: 100vh; /* 视窗高度 */
}

.pdfArea {
  width: 95%;
}

/* ------------------- 输入页码 ------------------- */
.pageNum {
  margin: 10px 0;
  font-size: 20px;
}
/*在谷歌下移除input[number]的上下箭头*/
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
  margin: 0;
}

.inputNumber {
  border-radius: 8px;
  border: 1px solid #999999;
  height: 35px;
  font-size: 18px;
  width: 60px;
  text-align: center;
}
.inputNumber:focus {
  border: 1px solid #00aeff;
  background-color: rgba(18, 163, 230, 0.096);
  outline: none;
  transition: 0.2s;
}

/* ------------------- 切换页码 ------------------- */
.turn {
  background-color: #888888;
  //opacity: 0.7;
  color: #ffffff;
  height: 70px;
  width: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 5px 0;
}

.turn-btn {
  background-color: #000000;
  //opacity: 0.6;
  color: #ffffff;
  height: 70px;
  width: 70px;
  border-radius: 50%;
  margin: 5px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.turn-btn:hover,
.turn:hover {
  transition: 0.3s;
  opacity: 0.5;
  cursor: pointer;
}

/* ------------------- 进度条 ------------------- */
.progress {
  position: absolute;
  right: 50%;
  top: 50%;
  text-align: center;
}
.progress > span {
  color: #199edb;
  font-size: 14px;
}
</style>