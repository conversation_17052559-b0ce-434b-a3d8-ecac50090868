import _ from "lodash";
import html2canvas from "html2canvas"
import { sysFileInfoUpload } from '@/api/modular/system/fileManage'
import pageComponent from "@/views/system/vTestReport/components/pageComponent";
import PreviewDrawer from "@/views/system/vTestReport/components/previewDrawer.vue";
import calendarThubnail from "@/views/system/vTestReport/components/calendarThubnail";
import pbiReturnTop from '@/components/pageTool/components/pbiReturnTop.vue'
import moment from "moment";

export const calendarCommon = {
  components: {
    pageComponent,
    PreviewDrawer,
    calendarThubnail,
    pbiReturnTop
  },
  data: function () {
    return {
      id: null,
      screenImageId:'', //传递回去的图片id
      alias: "",
      testCondition: "",

      paginationConfig: {
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '30', '40', '50'], // 显示的每页数量选项
        size: "small",
        showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
      },

      editObj: "", // 选中的图表类型

      navigationVisible: false,
      animateObj: {}, //导航栏动画

      // 全局X轴最大值、间距
      globalXMax:0,
      globalXInterval: 0,

      echartObj: {},
      echartsColor: [
        "#c00000",
        "#0070c0",
        "#808080",
        "#7030a0",
        "#4472c4",
        "#a5a5a5",
        "#ed7d31",
        "#5b9bd5",
        "#70ad47",
        "#000000",
        "#ff9999",
        "#ffc000",
        "#00b050"
      ],

      drawerVisible: false,

      // 编辑图表修改了啥,因为现在是自动算图例位置，如果有改动图例的话，就不自动算，保持用户修改的值
      historyRecord: {
        voltage: [],
        innerres: [],
        height: [],
        volume: [],
        weight: [],
        isolateres: [],
        capRet: [],
        capRec: [],
        energyRet: [],
        energyRec: [],
        forecast: [],
      },

      /* 在线编辑图表 */
      chartEditData: {}, //在线图表-编辑数据  合并
      chartOriginalLegent: {}, //在线图表-原始图例  合并
      chartLegendNameList: {
        voltage: [],
        innerres: [],
        height: [],
        volume: [],
        weight: [],
        isolateres: [],
        capRet: [],
        capRec: [],
        energyRet: [],
        energyRec: [],
        forecast: [],
      }, //不同类型图例的汇总  合并
      chartFrist: {
        voltage: true,
        innerres: true,
        height: true,
        volume: true,
        weight: true,
        isolateres: true,
        capRet: true,
        capRec: true,
        energyRet: true,
        energyRec: true,
        forecast: true,
      }, //首次进入 合并
      chartXYNum: {},
      chartCheckObj: {
        voltage: {},
        innerres: {},
        height: {},
        volume: {},
        weight: {},
        isolateres: {},
        capRet: {},
        capRec: {},
        energyRet: {},
        energyRec: {},
        forecast: {},
      },
      chartOriginalseries: {}, //原始图表数据
      chartResetOriginal: {},
    }

  },
  methods: {
    handleSuspensionIcon() {
      this.navigationVisible = true
    },
    handleNavigation(e) {
      let wrapper = document.getElementsByClassName("wrapper")[0]
      wrapper.scrollTop = e.index === 0 ? 0 : 10 + 40 + (e.index * (10 + 32 + 415))  // 10:边距 40：标题  ( 10:边距   32:操作按钮 415:图的大小 )

      this.animateObj[e.id] = true
      this.navigationVisible = false

      setTimeout(() => {
        this.animateObj[e.id] = false
      }, 2000)
    },
    handleModalCancel(target) {
      this[target] = false
    },

    // 截取保存模板的图表
    handleScreenshot(){
      const dom = document.getElementById(this.editObj)
      setTimeout(() => {
        html2canvas(dom, {
          useCORS: true,
          backgroundColor: "#fff"
        }).then(canvas => {
          let canvasImg = canvas.toDataURL("image/png")
          const blob = this.handleB64toBlob(canvasImg.replace("data:image/png;base64,", ""))

          const formData = new FormData()
          formData.append('file', blob)
          sysFileInfoUpload(formData).then((res) => {
            this.screenImageId = res.data
          })
        })  
      },500)
    },

    // base64 转 blob
    handleB64toBlob(b64Data, contentType = null, sliceSize = null) {
      contentType = contentType || "image/png"
      sliceSize = sliceSize || 512
      let byteCharacters = window.atob(b64Data.substring(b64Data.indexOf(",") + 1))
      let byteArrays = []
      for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
        let slice = byteCharacters.slice(offset, offset + sliceSize)
        let byteNumbers = new Array(slice.length)
        for (let i = 0; i < slice.length; i++) {
          byteNumbers[i] = slice.charCodeAt(i)
        }
        var byteArray = new Uint8Array(byteNumbers)
        byteArrays.push(byteArray)
      }
      return new Blob(byteArrays, { type: contentType })
    },

    // 获取编辑图表数据、原始图表数据
    _getInitData(targetObj, type = 'original') {

      const isEdit = type === 'edit'
      const templateParam = this.reportChartTemplateList[targetObj].templateParamJson

      const options = {
        XTitle: isEdit && templateParam.XTitle ? templateParam.XTitle : 'Storage Time / D',
        titleTop: isEdit && templateParam.titleTop ? templateParam.titleTop : 8,

        legendBgColor: isEdit && templateParam.legendBgColor ? templateParam.legendBgColor : '',
        legendOrient: isEdit && templateParam.legendOrient ? templateParam.legendOrient : 'vertical',
        legendZip: isEdit && templateParam.legendZip ? templateParam.legendZip : 'false',
        legendNameType: isEdit && templateParam.legendNameType ? templateParam.legendNameType : 'sampleCode',
        legendFontSize: isEdit && templateParam.legendFontSize ? templateParam.legendFontSize :  12,
        legendTop:isEdit && templateParam.legendTop ? templateParam.legendTop :  50,
        legendRight: isEdit && templateParam.legendRight ? templateParam.legendRight : (targetObj.includes('Rec') || targetObj === 'weight' ? 90 : 50),
        legendWidth: isEdit && templateParam.legendWidth ? templateParam.legendWidth : 20,
        legendHeight: isEdit && templateParam.legendHeight ? templateParam.legendHeight : (targetObj === 'height' ? 7 : 5),
        legendGap:isEdit && templateParam.legendGap ? templateParam.legendGap :  5,

        

        xType:isEdit && templateParam.xType ? templateParam.xType :  "value",
        xMin:isEdit && templateParam.xMin ? templateParam.xMin :  0,
        xMax: isEdit && templateParam.xMax ? templateParam.xMax : 0,
        xInterval:isEdit && templateParam.xInterval ? templateParam.xInterval :  0,

        yType:isEdit && templateParam.yType ? templateParam.yType :  "value",
        yMin:isEdit && templateParam.yMin ? templateParam.yMin :  0,
        yMax: isEdit && templateParam.yMax ? templateParam.yMax :  0,
        yInterval:isEdit && templateParam.yInterval ? templateParam.yInterval :  0,
        yDecimalNum: isEdit && templateParam.yDecimalNum ? templateParam.yDecimalNum :  0,

        gridTop:isEdit && templateParam.gridTop ? templateParam.gridTop :  40,
        gridRight: isEdit && templateParam.gridRight ? templateParam.gridRight :  (targetObj.includes('Rec') || targetObj === 'weight' ? 80 : 40),
        gridBottom: isEdit && templateParam.gridBottom ? templateParam.gridBottom :  70,
      }

      if (targetObj.includes('Rec') || targetObj === 'weight') {
        // 次Y轴初始值
        options.yTitleRight = isEdit && templateParam.yTitleRight ? templateParam.yTitleRight :  40
        options.yType2 = "value"
        options.yMin2 = 0
        options.yMax2 = 0
        options.yInterval2 = 0
      }

      if (type === 'edit') {
        options.series = []
        options.legend = []
        options.legendSort = []
        options.legendEditName = []
        options.allData = templateParam.allData ?? {}
      }
      if (type === 'original') {
        options.checkData = []
      }

      const titleList = {
        'voltage':{chartTitle:'_OCV',YTitle:'OCV / mV'},
        'innerres':{chartTitle:'_ACR',YTitle:'ACR / mΩ'},
        'height':{chartTitle:'_Size',YTitle:'Size / mm'},
        'volume':{chartTitle:'_Volume',YTitle:'Volume / g'},
        'weight':{chartTitle:'_Weight',YTitle:'Weight / g',YTitle2:'Mass Loss Rate / %'},
        'isolateres':{chartTitle:'_Insulation Resistance',YTitle:'Insulation Resistance / mΩ'},
        'capRet':{chartTitle:'',YTitle:'Capacity Retention Rate / %'},
        'capRec':{chartTitle:'',YTitle:'Capacity Recovery Rate / %',YTitle2:'DCIR Increase Rate / %'},
        'energyRet':{chartTitle:'',YTitle:'Energy Retention Rate / %'},
        'energyRec':{chartTitle:'',YTitle:'Energy Recovery Rate / %',YTitle2:'DCIR Increase Rate / %'},
        'forecast':{chartTitle:'',YTitle:'Capacity Recovery Rate / %'},
      }
     
      options.chartTitle = isEdit && templateParam.chartTitle ? templateParam.chartTitle : this.testCondition + 'Calendar Life' + titleList[targetObj].chartTitle
      options.YTitle = isEdit && templateParam.YTitle ? templateParam.YTitle : titleList[targetObj].YTitle
      if(titleList[targetObj].YTitle2){
        options.YTitle2 = isEdit && templateParam.YTitle2 ? templateParam.YTitle2 : titleList[targetObj].YTitle2
      }

      return options
    },

    // 获取编辑图表数据、原始图表数据
    _getInitDataOfAq(targetObj, type = 'original', safetyTestType) {

      const isEdit = type === 'edit'
      const templateParam = this.reportChartTemplateList[targetObj].templateParamJson

      const options = {
        XTitle:isEdit && templateParam.XTitle ? templateParam.XTitle : (safetyTestType === 'before_after' ? 'Test Stage' : 'Cycle Times'),
        titleTop:isEdit && templateParam.titleTop ? templateParam.titleTop : 8,

        legendWidth:isEdit && templateParam.legendWidth ? templateParam.legendWidth : 20,
        legendHeight:isEdit && templateParam.legendHeight ? templateParam.legendHeight : (targetObj === 'height' ? 7 : 5),
        legendGap:isEdit && templateParam.legendGap ? templateParam.legendGap : 5,//图例间隙
        legendOrient:isEdit && templateParam.legendOrient ? templateParam.legendOrient : 'vertical',
        legendZip: isEdit && templateParam.legendZip ? templateParam.legendZip : 'false',
        legendBgColor:isEdit && templateParam.legendBgColor ? templateParam.legendBgColor : '',
        legendFontSize:isEdit && templateParam.legendFontSize ? templateParam.legendFontSize : 12,
        legendTop: isEdit && templateParam.legendTop ? templateParam.legendTop :  50,
        legendRight:isEdit && templateParam.legendRight ? templateParam.legendRight : (targetObj.includes('Rec') || targetObj === 'weight' ? 90 : 50),
        legendNameType:isEdit && templateParam.legendNameType ? templateParam.legendNameType :  'sampleCode',

        gridTop:isEdit && templateParam.gridTop ? templateParam.gridTop : 40,
        gridRight:isEdit && templateParam.gridRight ? templateParam.gridRight : (targetObj.includes('Rec') || targetObj === 'weight' ? 80 : 40),
        gridBottom: isEdit && templateParam.gridBottom ? templateParam.gridBottom : 70,

        xType:isEdit && templateParam.xType ? templateParam.xType :  (safetyTestType === 'before_after' ? "category" : "value"),
        xMin:isEdit && templateParam.xMin ? templateParam.xMin :  "",
        xMax:isEdit && templateParam.xMax ? templateParam.xMax : "",
        xInterval:isEdit && templateParam.xInterval ? templateParam.xInterval : "",

        yType:isEdit && templateParam.yType ? templateParam.yType : "value",
        yMin:isEdit && templateParam.yMin ? templateParam.yMin : 0,
        yMax:isEdit && templateParam.yMax ? templateParam.yMax : 0,
        yInterval:isEdit && templateParam.yInterval ? templateParam.yInterval : 0,
        yDecimalNum: isEdit && templateParam.yDecimalNum ? templateParam.yDecimalNum : 0
      }

      if (targetObj.includes('Rec') || targetObj === 'weight') {
        // 次Y轴初始值
        options.yTitleRight = isEdit && templateParam.yTitleRight ? templateParam.yTitleRight : 40
        options.yType2 = "value"
        options.yMin2 = 0
        options.yMax2 = 0
        options.yInterval2 = 0
      }

      if (type === 'edit') {
        options.series = []
        options.legend = []
        options.legendSort = []
        options.legendEditName = []
      }
      if (type === 'original') {
        options.checkData = []
      }

      const titleList = {
        'voltage':{chartTitle:'_OCV',YTitle:'OCV / mV'},
        'innerres':{chartTitle:'_ACR',YTitle:'ACR / mΩ'},
        'height':{chartTitle:'_Size',YTitle:'Size / mm'},
        'volume':{chartTitle:'_Volume',YTitle:'Volume / g'},
        'weight':{chartTitle:'_Weight',YTitle:'Weight / g',YTitle2:'Mass Loss Rate / %'},
        'isolateres':{chartTitle:'Insulation Resistance',YTitle:'Insulation Resistance / mΩ'},
        
      }

      options.chartTitle = isEdit && templateParam.chartTitle ? templateParam.chartTitle : this.testCondition + 'Safety Test_' + titleList[targetObj].chartTitle
      options.YTitle = isEdit && templateParam.YTitle ? templateParam.YTitle : titleList[targetObj].YTitle
      if(titleList[targetObj].YTitle2){
        options.YTitle2 = isEdit && templateParam.YTitle2 ? templateParam.YTitle2 : titleList[targetObj].YTitle2
      }
      return options
    },

    // 通用的图表方法
    initNormalEchart(
      targetObj, // 要编译的对象目标 ,接收字符串 ,必填
      legendData = {},
      checkData = [],
      axisData = {},
      titleData = {},
      gridData = {},
    ) {
      // 获取图表实例,
      const normalEchart = this._getEchart(targetObj)
      this.echartObj[targetObj] = normalEchart

      const templateParam = this.reportChartTemplateList[targetObj].templateParamJson

      // 获取数据处理 容量恢复率、能量恢复率是双轴 重量图是双轴
      let processingResult = targetObj !== 'forecast'
        ? this._handleOriginalData(targetObj, _.cloneDeep(this[`${targetObj}EchartList`]), checkData, targetObj.includes('Rec') ? _.cloneDeep(this.dcirIncEchartList) : targetObj === 'weight' ? _.cloneDeep(this.weightLossRateEchartList) : [])
        : this._handleForecastData(targetObj, _.cloneDeep(this[`${targetObj}EchartList`]), checkData)

      // 处理小数位数
      let valueLength = ''
      let RangeValue = ''

      RangeValue = this._getYAxisRadius(processingResult[5], processingResult[6])

      // 图表无数据时Y轴最大最小值处理
      if (Array.isArray(RangeValue) && RangeValue.length > 0 && isNaN(RangeValue[0])) {
        RangeValue = [5, 0]
      }
      if (targetObj === 'weight') {
        // 例如：重量最大值135.5，向下取整得到130，间隔为10，5个间隔，上2下3，150 140 130 120 110 100
        const value = isFinite(processingResult[5]) && typeof processingResult[5] === 'number' ? Math.floor(processingResult[5] / 10) * 10 : 0
        const max = value + 20
        const min = value - 30
        RangeValue = [max, min]
      }


      axisData.yDecimalNum = templateParam.yDecimalNum
      if (!['capRet', 'capRec', 'energyRet', 'energyRec', 'forecast', 'voltage'].includes(targetObj) && typeof axisData.yDecimalNum !== 'number' && RangeValue[0] - RangeValue[1] < 4) {
        this.chartEditData[targetObj].yDecimalNum = 1
        this.chartResetOriginal[targetObj].yDecimalNum = 1
      }
      
      const yDecimalNum = typeof axisData.yDecimalNum === 'number' ? axisData.yDecimalNum : this.chartResetOriginal[targetObj].yDecimalNum
      valueLength = this.handleValueLength(targetObj === 'voltage' ? 4500 : ['capRet', 'capRec', 'energyRet', 'energyRec', 'forecast'].includes(targetObj) ? 110 : RangeValue[0].toFixed(yDecimalNum))

      if (this.chartEditData[targetObj].targetEditObj === 'yDecimalNum') {
        // 保留之前修改的
        this.chartEditData[targetObj].gridLeft = templateParam.gridLeft ?? valueLength + 40 + 15
        gridData.gridLeft = templateParam.gridLeft ?? valueLength + 40 + 15

        this.chartEditData[targetObj].yTitleLetf = templateParam.yTitleLetf ?? valueLength + 15
        titleData.yTitleLetf = templateParam.yTitleLetf ?? valueLength + 15
      }

      // 赋予新值
      this._handleValue(processingResult, targetObj)
      let seriesList = processingResult[2] // 图表数据

      // 把模板的数据拼接进去
      const { 
        titleData: newTitleData, 
        gridData: newGridData, 
        legendData: newLegendData, 
        axisData: newAxisData, 
        legend: newLegend 
      } = this._getTemplateParams(targetObj, titleData, gridData, legendData, axisData, legend);
      titleData = newTitleData;
      gridData = newGridData;
      legendData = newLegendData;
      axisData = newAxisData;
      legend = newLegend;
      
      // 图例的显隐(仅图例) 图例的排序 图例的改名 图例的展示(线+图例)
      const legendResult = this._handleLegend(processingResult, seriesList, legendData, valueLength, targetObj)
      seriesList = legendResult[0]
      

      let legend = legendResult[1]

      // 在线报告Y轴yaxis最大最小值
      if (['capRet', 'capRec', 'energyRet', 'energyRec', 'forecast'].includes(targetObj)) {
        RangeValue = [110, 70]
      }

      const optionParam = {
        titleData,
        gridData,
        legendData,
        axisData,
        legend,
        yDecimalNum,
        yMin: templateParam.yMin ?? (targetObj === 'voltage' ? 3000 : RangeValue[1]),
        yMax: templateParam.yMax ?? (targetObj === 'voltage' ? 4500 : RangeValue[0])
      }
      
      if (targetObj === 'height') optionParam.longestLegend = legend.reduce((a, b) => a.length > b.length ? a : b, "")
      let options = this._getOptions(targetObj, optionParam, seriesList)


      
      /* 处理坐标轴 */
      if ((this.chartFrist[targetObj] || this.chartEditData[targetObj].targetResetObj === 'xMax') && this.globalXMax > 0) {
        if(!templateParam.xMax) options.xAxis[0].max = this.globalXMax
        if(!templateParam.xInterval) options.xAxis[0].interval = this.globalXInterval        
      }

      normalEchart.clear()
      normalEchart.getZr().off('dblclick')
      normalEchart.getZr().on('dblclick', ({target, topTarget}) => {
        this._handleDblclickEchart(target, topTarget, targetObj)
      });
      normalEchart.setOption(options)

      // 如果坐标轴类型为数值轴，则计算出最大值最小值，以及间距 (因为需要有图表才能获取的到,所以放在最后面)
      this._handleXYAxis(options, normalEchart, targetObj)

      // 为图例多一个格子，在线图表、离线图表分别使用各自的 globalXMax、globalXInterval
      if (this.chartFrist[targetObj] && this.globalXMax === 0 &&  !templateParam.xMax) {
        options.xAxis[0].max = this.chartEditData[targetObj].xMax + this.chartEditData[targetObj].xInterval
        options.xAxis[0].interval = this.chartEditData[targetObj].xInterval
        normalEchart.setOption(options)

        this.globalXMax = this.chartEditData[targetObj].xMax + this.chartEditData[targetObj].xInterval
        this.globalXInterval = this.chartEditData[targetObj].xInterval

        this.chartEditData[targetObj].xMax = this.chartEditData[targetObj].xMax + this.chartEditData[targetObj].xInterval
      }

      if(this.chartFrist[targetObj]){
        normalEchart.setOption({
          legend: {
            itemGap: 5
          },
        })
        this.chartFrist[targetObj] = false
      }

      
    },

    // 获得图表实例
    _getEchart(targetObj) {
      const oldEchart = this.echarts.getInstanceByDom(document.getElementById(targetObj)) // 查找图表实例是否存在
      if (oldEchart) {
        return oldEchart
      } else {
        // return this.echarts.init(document.getElementById(targetObj), "walden", { renderer: "svg" }) // 测试
        return this.echarts.init(document.getElementById(targetObj), "walden", {devicePixelRatio: 2}) // 测试
      }
    },
    // 处理图表原始数据
    _handleOriginalData(targetObj, cycleList, checkData, dcirIncList = []) {
      
      let seriesOriginal = []
      let checkDataOriginal = []
      let legendList = []
      let yAxisList = []
      let seriesList = []

      let lineColorList = []
      let haveColor = null
      let lineSymbolList = []
      let haveSymbol= null
      const isCheck = checkData.length === 0
      const templateParam = this.reportChartTemplateList[targetObj].templateParamJson

      for (let i = 0; i < cycleList.length; i++) {
        const sampleCode = cycleList[i].sampleCode
        const batteryCode = cycleList[i].batteryCode
        let legendNameType = cycleList[i][this.chartEditData[targetObj].legendNameType]
        let templateContent = templateParam.checkData.length > 0 ? (templateParam.checkData.filter(item => item.id === sampleCode)[0] || {}) : {}
        let editContent = checkData[checkData.findIndex(findItem => findItem.id == sampleCode)]
        
        const isHaveColor = targetObj === 'height' ? lineColorList.find(findItem => findItem.name === cycleList[i].sizeType) : lineColorList.find(findItem => findItem.name === sampleCode)
        if (isHaveColor == undefined) {
          if (targetObj === 'height') {
            lineColorList.push({
              name: cycleList[i].sizeType,
              color: lineColorList.length < this.echartsColor.length ? this.echartsColor[lineColorList.length] : this.getRandomHexColor()
            })
          } else {
            lineColorList.push({name: sampleCode, color: this.echartsColor[lineColorList.length]})
          }
        }
        haveColor = lineColorList[lineColorList.findIndex(v => v.name === (targetObj === 'height' ? cycleList[i].sizeType : sampleCode))].color

        if (targetObj === 'height') {
          const isHaveSymbol = lineSymbolList.find(v => v.name === cycleList[i].symbolType)
          if (isHaveSymbol == undefined) lineSymbolList.push({
            name: cycleList[i].symbolType,
            symbol: this.echartsSymbolList[lineSymbolList.length % this.echartsSymbolList.length]
          })
          haveSymbol = lineSymbolList[lineSymbolList.findIndex(v => v.name === cycleList[i].symbolType)].symbol
        }

        

        if (isCheck) {this.chartLegendNameList[targetObj].push({ sampleCode,batteryCode })}

        let series = {
          id: sampleCode,
          index: i + 1,
          name: legendNameType,
          soc: legendNameType,
          type: "line",
          barGap: 0,
          markPoint: {
            data: []
          },
           emphasis: {
            focus: "series"
          },
           large: true,
          sampling: 'lttb',
          connectNulls: templateContent.connectNulls ?? (isCheck ?  false : Boolean(Number(editContent.connectNulls))),
          symbol: templateContent.symbol ?? (isCheck ? targetObj === 'height' ? haveSymbol : "rect" : editContent.symbol),
          symbolSize: templateContent.symbolSize ?? (isCheck ? (targetObj === 'height' ? 7 : 5) : editContent.symbolSize),
          lineStyle: {
            width: templateContent.lineWidth ?? (isCheck ? 1 : editContent.lineWidth),
            type: templateContent.lineType ?? (isCheck ? "solid" : editContent.lineType),
            color:templateContent.lineColor ?? (isCheck ? haveColor : editContent.lineColor)
          },
          itemStyle: {
            color:templateContent.itemColor ?? (isCheck ? haveColor: editContent.itemColor)
          },
          data: cycleList[i].data.map((mapItem, index) => {
            let absoluteTime = ''
            if (this.absoluteTimeMap && Array.isArray(mapItem) && mapItem.length > 0) {
              absoluteTime = this.absoluteTimeMap[mapItem[0]];
            }
            // 在线报告和离线报告的数据结构不一致
            return mapItem.value ? {id: index, value: mapItem.value} : {id: index, value: mapItem, lineName: this.onlineLineNameMap[targetObj], absoluteTime: absoluteTime}
          }),
        }

        // 设置最大最小值
        if (!isCheck && editContent.maxPoint || templateContent.maxPoint) {
          series.markPoint.data.push({type: "max", name: "Max"})
        }
        if (!isCheck && editContent.minPoint || templateContent.minPoint) {
          series.markPoint.data.push({type: "min", name: "Min"})
        }

        seriesOriginal.push({
          id: sampleCode,
          index: i + 1,
          name: legendNameType,
          soc: legendNameType,
          connectNulls: false,
          dataName:legendNameType,
          synchronization: templateContent.synchronization ?? (isCheck ? i : editContent.synchronization),
          maxPoint: templateContent.maxPoint ?? (isCheck ? false : editContent.maxPoint),
          minPoint: templateContent.minPoint ?? (isCheck ? false : editContent.minPoint),
          symbol: templateContent.symbol ?? (isCheck ? targetObj === 'height' ? haveSymbol : "rect" : editContent.symbol),
          symbolSize: templateContent.symbolSize ?? (isCheck ? (targetObj === 'height' ? 7 : 5) : editContent.symbolSize),
          itemColor:templateContent.itemColor ?? (isCheck ? haveColor : editContent.itemColor),
          lineType: templateContent.lineType ?? (isCheck ? "solid" : editContent.lineType),
          lineWidth: templateContent.lineWidth ?? (isCheck ? 1 : editContent.lineWidth),
          lineColor:templateContent.lineColor ?? (isCheck ? haveColor: editContent.lineColor)
        })

        // 原始值
        checkDataOriginal.push({
          id: sampleCode,
          index: i + 1,
          name: legendNameType,
          soc: legendNameType,
          connectNulls: false,
          synchronization:i,
          maxPoint:false,
          minPoint:false,
          symbol: targetObj === 'height' ? haveSymbol : "rect",
          symbolSize: targetObj === 'height' ? 7 : 5,
          itemColor: haveColor,
          lineType: "solid",
          lineWidth: 1,
          lineColor: haveColor
        })

        // 在线报告和离线报告的数据结构不一致
        yAxisList.push(...cycleList[i].data.map(mapItem => Number(mapItem.value ? mapItem.value[1] : mapItem[1])))

        legendList.push(legendNameType)
        seriesList.push(series)

        // 添加DCIR增长率/失重率的数据
        if (dcirIncList.length > 0) {
          let idSuffixList = []
          let lineNameList = []
          if (targetObj === "weight") {
            idSuffixList = ['_weightLossRate']
            lineNameList = ['_失重率']
          } else if (this.stepTypeActive !== 'Custom') {
            idSuffixList = ['_dcirInc']
            lineNameList = ['_DCIR增长率']
          } else if (this.stepTypeActive === 'Custom') {
            idSuffixList = this.dcirTiTleList.map((title, idx) => "_dcirInc" + idx)
            lineNameList = this.dcirTiTleList.map(title => '_' + (title ? title + '_' : '') + 'DCIR增长率')
          }
          const lineGroupNum = idSuffixList.length

          idSuffixList.forEach((idSuffix, idx) => {

          legendNameType = dcirIncList[i*lineGroupNum + idx][this.chartEditData[targetObj].legendNameType]
          templateContent = templateParam.checkData.length > 0 ? (templateParam.checkData.filter(item => item.id === sampleCode + idSuffix)[0] || {}) : {}
          editContent = checkData[checkData.findIndex(findItem => findItem.id == sampleCode + idSuffix)]
          
          let series2 = {
            id: sampleCode + idSuffix,
            index: i + 1,
            name: legendNameType,
            soc: legendNameType,
            yAxisIndex: 1,
            type: "line",
            barGap: 0,
            markPoint: {
              data: []
            },
             emphasis: {
              focus: "series"
            },
            large: true,
            sampling: 'lttb',
            connectNulls: templateContent.connectNulls ?? (isCheck ? false : Boolean(Number(editContent.connectNulls))),
            symbol: templateContent.symbol ?? (isCheck ? "rect" : editContent.symbol),
            symbolSize: templateContent.symbolSize ?? (isCheck ? 5 : editContent.symbolSize),
            lineStyle: {
              width: templateContent.lineWidth ?? (isCheck ? 1 : editContent.lineWidth),
              type: templateContent.lineType ?? (isCheck ? "dashed" : editContent.lineType),
              color: templateContent.lineColor ??
                (isCheck
                  ? lineColorList[lineColorList.findIndex(v => v.name === dcirIncList[i*lineGroupNum + idx].sampleCode)].color
                  : editContent.lineColor)
            },
            itemStyle: {
              color:templateContent.itemColor ?? 
                (isCheck
                  ? lineColorList[lineColorList.findIndex(v => v.name === dcirIncList[i*lineGroupNum + idx].sampleCode)].color
                  : editContent.itemColor)
            },
            data: dcirIncList[i*lineGroupNum + idx].data.map((mapItem, index) => {
              return {id: index, value: targetObj === "weight" ? mapItem.value : mapItem, lineName: lineNameList[idx]}
            }),
          }
          // 设置最大最小值
          if (!isCheck && editContent.maxPoint || templateContent.maxPoint) {
            series2.markPoint.data.push({type: "max", name: "Max"})
          }
          if (!isCheck && editContent.minPoint || templateContent.minPoint) {
            series2.markPoint.data.push({type: "min", name: "Min"})
          }
          seriesList.push(series2)

          // 编辑图表：DCIR增长率/失重率线的数据标签
          seriesOriginal.push({
            id: sampleCode + idSuffix,
            index: i + 1,
            name: legendNameType,
            soc: legendNameType,
            connectNulls: false,
            dataName:legendNameType + lineNameList[idx],
            synchronization:templateContent.synchronization ?? (isCheck ? i : editContent.synchronization),
            maxPoint:templateContent.maxPoint ?? (isCheck ? false : editContent.maxPoint),
            minPoint: templateContent.minPoint ?? (isCheck ? false : editContent.minPoint),
            symbol:templateContent.symbol ?? (isCheck ? "rect" : editContent.symbol),
            symbolSize:templateContent.symbolSize ?? (isCheck ? 5 : editContent.symbolSize),
            itemColor:templateContent.itemColor ??
              (isCheck
                ? lineColorList[lineColorList.findIndex(v => v.name === dcirIncList[i*lineGroupNum + idx].sampleCode)].color
                : editContent.itemColor),
            lineType: templateContent.lineType ?? (isCheck ? "dashed" : editContent.lineType),
            lineWidth:templateContent.lineWidth ??  (isCheck ? 1 : editContent.lineWidth),
            lineColor:templateContent.lineColor ??
              (isCheck
                ? lineColorList[lineColorList.findIndex(v => v.name === dcirIncList[i*lineGroupNum + idx].sampleCode)].color
                : editContent.lineColor)
          })

          // 原始值
          checkDataOriginal.push({
            id: sampleCode + idSuffix,
            index: i + 1,
            name: legendNameType,
            soc: legendNameType,
            connectNulls: false,
            synchronization:i,
            maxPoint:false,
            minPoint:false,
            symbol: "rect",
            symbolSize: 5,
            itemColor: lineColorList[lineColorList.findIndex(v => v.name === dcirIncList[i*lineGroupNum + idx].sampleCode)].color,
            lineType: "dashed",
            lineWidth: 1,
            lineColor: lineColorList[lineColorList.findIndex(v => v.name === dcirIncList[i*lineGroupNum + idx].sampleCode)].color
          })

          })

        }
      }

      return [seriesOriginal, checkDataOriginal, seriesList, legendList, [], Math.max.apply(null, yAxisList), Math.min.apply(null, yAxisList)]
    },
    // 存在颜色相近的可能
    getRandomHexColor() {
      var hexChars = '0123456789abcdef';
      var color = '#';
      for (let i = 0; i < 6; i++) {
        color += hexChars[Math.floor(Math.random() * 16)];
      }
      return color;
    },

    handleValueLength(data) {
      const divElement = document.getElementById('revealText15')
      divElement.innerHTML = data

      return divElement.clientWidth
    },
    handleReturnTop(){
      this.$emit('returnTop')
    },
    // 获得y轴最大最小值
    _getYAxisRadius(originalMax, originalMin) {

      const differenceValue = originalMax - originalMin

      const transferMax = Math.trunc(originalMax + differenceValue).toString().split("")
      const transferMin = Math.trunc(originalMin - differenceValue).toString().split("")

      const newMax = Number(transferMax.map((mapItem, mapIndex) => mapIndex == 0 ? (Number(mapItem) + 1).toString() : '0').join(""))
      const newMin = Number(transferMin.map((mapItem, mapIndex) => mapIndex == 0 ? mapItem : '0').join(""))



      return [newMax, newMin]
    },
    // 在线编辑图表
    handleEditEcharts(target) {
      this.editObj = target
      this.drawerVisible = true
    },
    _handleLegend(processingResult, seriesList, legendData, valueLength, targetObj) {
      const originalParam = this.reportChartTemplateList[targetObj].originalParamJson
      const templateParam = this.reportChartTemplateList[targetObj].templateParamJson

      const first = this.chartFrist[targetObj]
      /* 图例显隐赋值 开始 */
      if (!legendData.legendRevealList || (first && templateParam.legendData.legendRevealList)) {
        this.chartEditData[targetObj].legendRevealList =  templateParam.legendData.legendRevealList ??  (first ? _.cloneDeep(processingResult[3]).slice(0, 6) : _.cloneDeep(processingResult[3]))

        this.chartEditData[targetObj].gridLeft = templateParam.gridLeft ?? valueLength + 40 + 15
        this.chartResetOriginal[targetObj].gridLeft = originalParam?.gridLeft ? originalParam.gridLeft : (templateParam.gridLeft ?? valueLength + 40 + 15)

        this.chartEditData[targetObj].yTitleLetf = templateParam.yTitleLetf ?? valueLength + 15
        this.chartResetOriginal[targetObj].yTitleLetf = originalParam?.yTitleLetf ? originalParam.yTitleLetf : (templateParam.yTitleLetf ?? valueLength + 15)
        
      }
      /* 图例显隐赋值 结束 */

      /* 图例排序 开始 */
      if (legendData.legendSort) {
        this.chartEditData[targetObj].legend = _.cloneDeep(legendData.legendSort) // 将页面上的图例数组按照用户设置的顺序排序
      }
      this.chartEditData[targetObj].legendSort =  first && !legendData.legendSort ? _.cloneDeep(this.chartOriginalLegent[targetObj]) : _.cloneDeep(this.chartEditData[targetObj].legend) // 传回给在线编辑图表
      /* 图例排序 结束 */

      /* 图例变更名称 开始 */
      if (legendData.legendEditName) {
        // 遍历图例的变更
        legendData.legendEditName.forEach(v => {
          // 判断有新值，但是不是清空操作,修改对应的数据
          if (v.newName && !v.isReset) {
            // 修改原始图例
            // 先找到原始图例改了新值（原始图例的值等于修改的原始值）（因为每次都在最开头赋予了最开始的原始图例）
            let temIndex1 = this.chartOriginalLegent[targetObj].findIndex(findItem => findItem == v.originName)
            this.chartOriginalLegent[targetObj][temIndex1] = v.newName

            // 修改在线编辑图表的选中值的名称
            let temIndex2 = this.chartEditData[targetObj].legend.findIndex(findItem => findItem == v.originName)
            this.chartEditData[targetObj].legend[temIndex2] = v.newName

            // 修改this.chartEditData[targetObj].series
            this.chartEditData[targetObj].series.forEach(findItem => {
              findItem.name = findItem.name == v.originName ? v.newName : findItem.name
            })

            // 修改图上的图表数据的图例
            seriesList.forEach(findItem => {
              findItem.name = findItem.name == v.originName ? v.newName : findItem.name
            })
          }

          // 如果没有新值，然后是重置的
          if (!v.newName && v.isReset) {
            // 操作
            // 操作完之后isReset设为false
            v.previousName = ''
            v.isReset = false
          }
        })

        // 赋予修改后的图例修改名称数组
        this.chartEditData[targetObj].legendEditName = legendData.legendEditName
      }

      if (this.chartEditData[targetObj].legendEditName.length === 0 || !legendData.legendEditName) {
        this.chartEditData[targetObj].legendEditName = this.chartOriginalLegent[targetObj].map(v => {
          return {id:v,originName: v, previousName: '', newName: '', isReset: false}
        })
      }
      /* 图例变更名称 结束 */

      /* 没选中的图例的移除处理 开始 */
      if (legendData.legendEdit) {

        // 移除页面上的对应的图例
        for (let i = 0; i < this.chartEditData[targetObj].legend.length; i++) {
          if (!legendData.legendEdit.includes(this.chartEditData[targetObj].legend[i])) {
            this.chartEditData[targetObj].legend.splice(i, 1)
            i--
          }
        }

        // 移除页面上的对应的图例的图表数据
        for (let i = 0; i < seriesList.length; i++) {
          if (!legendData.legendEdit.includes(seriesList[i].name)) {
            seriesList.splice(i, 1)
            i--
          }
        }
      }
      /* 没选中的图例的移除处理 结束 */

      /* 图例 开始 */
      let legend = this._getLegend(this.chartEditData[targetObj])
      /* 图例 结束 */

      return [seriesList, legend]

    },
    // 获得要展示的图例(首先遍历图例数组，然后在图例中排除掉图例显隐没有勾选的数据，由于图例会更名，所以还得考虑更名的情况)
    _getLegend(editList) {
      const legend = []
      editList.legend.forEach(v => {
        const conditions1 = editList.legendRevealList.includes(v)

        // 没改名
        if (conditions1) {
          legend.push(v)
        } else {
          // 改名
          const conditions2 = editList.legendEditName.findIndex(findItem => findItem.newName == v)
          if (conditions2 > -1 && editList.legendRevealList.includes(editList.legendEditName[conditions2].originName)) legend.push(v)
        }
      })

      return legend
    },
    // 处理图表原始数据
    _getOptions(targetObj, editData, seriesList) {
      const options = {
        backgroundColor: '#ffffff',
        animationDuration: 2000,
        textStyle: {
          fontFamily: "Times New Roman"
        },
        tooltip: {
          trigger: "axis",
          confine: true,
          enterable: true,
          hideDelay: 300,
          extraCssText: 'max-height: 400px; overflow-y: auto; scrollbar-width: thin; scrollbar-color: #888 #f1f1f1; pointer-events: auto;',
          formatter: function (params) {

            // 添加 在线报告绝对时间
            let absoluteTime = ''
            if (params[0].data.absoluteTime) {
              let date = moment(params[0].data.absoluteTime, 'YYYY/MM/DD HH:mm:ss.SSS')
              absoluteTime = date.isValid() ? date.format('YYYY-MM-DD HH:mm:ss') : params[0].data.absoluteTime
            }

            var result = params[0].axisValue + '<div style="width:20px;display: inline-block;"></div>' + absoluteTime + "<br>" // 添加 x 轴的数值
            if(params[0].value[2] && params[0].value[3]){
              result += params[0].value[2] + ":" + params[0].value[3] +  "<br>" // 添加 出箱后 时间
            }

            if(params[0].value[2] && !params[0].value[3]){
              result += params[0].value[2] +  "<br>" // 添加 出箱后 时间
            }

            if(params.length > 1 && params[0].value[0] == params[1].value[0] && params[0].seriesId == params[1].seriesId){
              result += params[1].value[2] + ":" + (params[1].value[3]?params[1].value[3]:"" )+  "<br>" // 添加 中检后 时间
            }

            // 过滤掉 value[1] 没有值的数据项
            const validParams = params.filter(function (item) {
              // 预测值的特殊处理
              if (targetObj === 'forecast' && item.data.lineName && item.data.lineName.includes('预测值') && item.data.id === 0) {
                return false // 去除预测series的第一个索引的数据
              }

              // 检查 value[1] 是否有效
              return item.value &&
                     item.value[1] !== null &&
                     item.value[1] !== undefined &&
                     item.value[1] !== '' &&
                     item.value[1] !== '-' &&
                     !isNaN(item.value[1])
            })

            validParams.forEach(function (item, dataIndex) {
              result +=
                item.marker +
                item.seriesName +
                (item.value.length > 2 ? item.value[2] + (item.data.lineName ? " " + item.data.lineName : "") : item.data.lineName) +
                '<div style="width:20px;display: inline-block;"></div>' +
                (item.value[1] >= 10000000000 ? "异常值" : item.value[1]) +
                (item.data.lineName ? "%" : "") +
                "<br>" // 添加每个系列的数值
            })

            // 如果没有有效数据，返回空字符串隐藏tooltip
            if (validParams.length === 0) {
              return ''
            }

            // 直接返回内容，滚动由 extraCssText 控制
            return result
          }
        },

        title: {
          text: editData.titleData.chartTitle || this.chartResetOriginal[targetObj].chartTitle,
          left: "center",
          top: editData.titleData.titleTop || 8,
          textStyle: {
            fontSize: 18,
            fontWeight: 500,
            color: '#000'
          }
        },
        grid: {
          show: true,
          top: editData.gridData.gridTop || this.chartResetOriginal[targetObj].gridTop,
          left: editData.gridData.gridLeft || this.chartResetOriginal[targetObj].gridLeft,
          right: editData.gridData.gridRight || this.chartResetOriginal[targetObj].gridRight,
          bottom: editData.gridData.gridBottom || 70,
          borderWidth: 0.5,
        },
        legend: {
          data: editData.legend,
          itemWidth: editData.legendData.legendWidth || 20,
          itemHeight: editData.legendData.legendHeight || (targetObj === 'height' ? 7 : 5),
          itemGap: editData.legendData.legendGap || 5,
          orient: editData.legendData.legendOrient || 'vertical',
          top: editData.legendData.legendTop || this.chartResetOriginal[targetObj].legendTop,
          right: editData.legendData.legendRight || this.chartResetOriginal[targetObj].legendRight,
          padding: [0, 0],
          textStyle: {
            fontSize: editData.legendData.legendFontSize || 12,
            color: "#000000"
          }
        },
        xAxis: [
          {
            name: editData.titleData.XTitle || this.chartResetOriginal[targetObj].XTitle,
            type: editData.axisData.xType || this.chartResetOriginal[targetObj].xType,
            axisTick: {show: false},
            splitLine: {
              show: true, // 显示分隔线
              lineStyle: {
                type: "solid",
                width: 0.5
              }
            },
            axisLabel: {
              show: true,
              width: 0.5,
              textStyle: {
                fontSize: "15",
                color: "#000000"
              },
              formatter: function (value) {
                return value
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              },
              onZero: false, // 次Y轴为数值轴且包含0刻度, 确保X轴的轴线不在次Y轴的0刻度上
            },
            nameLocation: "middle", // 将名称放在轴线的中间位置
            nameGap: 35,
            nameTextStyle: {
              fontSize: 14,
              color: "#000000" // 可以根据需要调整字体大小
            },
            minInterval: 1,
          }
        ],
        yAxis: [
          {
            name: editData.titleData.YTitle || this.chartResetOriginal[targetObj].YTitle,
            type: editData.axisData.yType || this.chartResetOriginal[targetObj].yType,
            position: "left",
            min: editData.yMin || 0,
            max: editData.yMax || 5,
            nameGap: editData.titleData.yTitleLetf || this.chartResetOriginal[targetObj].yTitleLetf,
            splitLine: {
              show: true, // 显示分隔线
              lineStyle: {
                type: "solid",
                width: 0.5
              }
            },
            axisTick: {
              show: true // 显示刻度
            },
            axisLabel: {
              show: true,
              width: 0.5,
              textStyle: {
                fontSize: "15",
                color: "#000000"
              },
              formatter: function (value) {
                return value === 0 ? value : Number(value).toFixed(editData.yDecimalNum)
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              }
            },
            nameLocation: "middle", // 将名称放在轴线的起始位置
            nameRotate: 90, // 旋转角度，使名称竖排
            nameTextStyle: {
              fontSize: 16, // 可以根据需要调整字体大小
              fontWeight: 500,
              color: "#000000"
            }
          }
        ],
        series: seriesList
      }

      // 在线报告Y轴间隔
      if (['capRet', 'capRec', 'energyRet', 'energyRec'].includes(targetObj)) {
        options.yAxis[0].interval = 5
      }
      // 次Y轴
      if (targetObj.includes('Rec') || targetObj === 'weight') {
        options.yAxis.push({
          name: editData.titleData.YTitle2 || this.chartResetOriginal[targetObj].YTitle2,
          type: editData.axisData.yType2 || this.chartResetOriginal[targetObj].yType2,
          position: "right",
          min: targetObj === 'weight' ? -0.4 : -10,
          max: targetObj === 'weight' ? 0.6 : 150,
          interval: targetObj === 'weight' ? 0.2 : 20,
          nameGap: editData.titleData.yTitleRight || this.chartResetOriginal[targetObj].yTitleRight,
          splitLine: {
            show: true, // 显示分隔线
            lineStyle: {
              type: "solid",
              width: 0.5
            }
          },
          axisTick: {
            show: true // 显示刻度
          },
          axisLabel: {
            show: true,
            width: 0.5,
            textStyle: {
              fontSize: "15",
              color: "#000000"
            },
            formatter: function (value) {
              return value === 0 || targetObj !== 'weight' ? value : Number(value).toFixed(1)
            }
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#ccc",
              width: 0.5
            }
          },
          nameLocation: "middle", // 将名称放在轴线的起始位置
          nameRotate: 90, // 旋转角度，使名称竖排
          nameTextStyle: {
            fontSize: 16, // 可以根据需要调整字体大小
            fontWeight: 500,
            color: "#000000"
          }
        })
      }

      // 重量图次Y轴
      // if (targetObj === 'weight') {
      //   // 重新计算主Y轴的最小值和间隔值，使得分割段数为5
      //   let max = options.yAxis[0].max
      //   let min = options.yAxis[0].min
      //   let newDiff = (max - min) / 5 > 1 ? Math.ceil((max - min) / 5) * 5 : 5
      //   options.yAxis[0].min = max - newDiff
      //   options.yAxis[0].interval = newDiff / 5
      // }

      if (targetObj === 'voltage') options.yAxis[0].interval = 300
      if (targetObj === 'height') {
        options.legend.formatter = (name) => {
          let index = -1;
          const regex = /[\u4e00-\u9fa5]/g;
          let match;
          while ((match = regex.exec(name)) !== null) {
            index = match.index;
          }

          const opacityValue = this._getLegendDiscrepancy(editData.longestLegend, name)

          if (index >= 0) {
            // 将0~index 的文字设置为思源黑体12大小，index之后的设置为新罗马字体14大小
            // return `<div style="font-family: 'Source Han Sans CN'; font-size: 12px;">${name.substring(0, index + 1)}</div>` +
            //   `<div style="font-family: 'Times New Roman'; font-size: 14px;">${name.substring(index + 1)}</div>`;
            return `{style1|${name.substring(0, index + 1)}}{style2|${name.substring(index + 1)}}{style3|${opacityValue[0]}}{style4|${opacityValue[1]}}`;
          } else {
            // 如果没有找到中文字符，直接返回原始文本
            return `{style2|${name}}`;
          }
        }
        options.legend.textStyle = {
          color: "#000000",
          rich: {
            style1: {
              fontFamily: 'Source Han Sans',
              fontSize: editData.legendData.legendFontSize ? editData.legendData.legendFontSize - 2 : 12 - 2,
            },
            style2: {
              fontFamily: 'Times New Roman',
              fontSize: editData.legendData.legendFontSize || 12
            },
            style3: {
              fontFamily: 'Source Han Sans',
              fontSize: editData.legendData.legendFontSize ? editData.legendData.legendFontSize - 2 : 12 - 2,
              opacity: 0
            },
            style4: {
              fontFamily: 'Times New Roman',
              fontSize: editData.legendData.legendFontSize || 12,
              opacity: 0
            }
          }
        }
      }

      if (editData.legendData.legendBgColor) {
        options.legend.backgroundColor = editData.legendData.legendBgColor
      }

      if (editData.axisData.xMin !== undefined) {
        options.xAxis[0].min = editData.axisData.xMin
      }
      if (editData.axisData.xMax !== undefined) {
        options.xAxis[0].max = editData.axisData.xMax
      }
      if (editData.axisData.xInterval !== undefined) {
        options.xAxis[0].interval = editData.axisData.xInterval
      }
      if (editData.axisData.yMin !== undefined) {
        options.yAxis[0].min = editData.axisData.yMin
      }
      if (editData.axisData.yMax !== undefined) {
        options.yAxis[0].max = editData.axisData.yMax
      }
      if (editData.axisData.yInterval !== undefined) {
        options.yAxis[0].interval = editData.axisData.yInterval
      }
      // 编辑图表-编辑次Y轴属性赋值
      if (editData.axisData.yMin2 !== undefined) {
        options.yAxis[1].min = editData.axisData.yMin2
      }
      if (editData.axisData.yMax2 !== undefined) {
        options.yAxis[1].max = editData.axisData.yMax2
      }
      if (editData.axisData.yInterval2 !== undefined) {
        options.yAxis[1].interval = editData.axisData.yInterval2
      }


      // 坐标轴类型赋值
      this.chartEditData[targetObj].xType = options.xAxis[0].type
      this.chartEditData[targetObj].yType = options.yAxis[0].type
      this.chartEditData[targetObj].seriesList = options.series

      return options
    },
    // 计算出最大值最小值，以及间距
    _handleXYAxis(options, echarts, targetObj) {
      const originalParam = this.reportChartTemplateList[targetObj].originalParamJson
      if (options.xAxis[0].type === "value") {
        const XAxis = echarts.getModel().getComponent("xAxis").axis.scale
        this.chartEditData[targetObj].xMin = XAxis._extent[0]
        this.chartEditData[targetObj].xMax = XAxis._extent[1]
        this.chartEditData[targetObj].xInterval = XAxis._interval
      }

      if (options.yAxis[0].type === "value") {
        const YAxis = echarts.getModel().getComponent("yAxis").axis.scale
        this.chartEditData[targetObj].yMin = YAxis._extent[0]
        this.chartEditData[targetObj].yMax = YAxis._extent[1]
        this.chartEditData[targetObj].yInterval = YAxis._interval
      }

      if (options.yAxis.length > 1 &&  options.yAxis[1].type === "value") {
        // 次Y轴编辑图表赋值
        const YAxis2 = options.yAxis[1]
        this.chartEditData[targetObj].yMin2 = YAxis2.min
        this.chartEditData[targetObj].yMax2 = YAxis2.max
        this.chartEditData[targetObj].yInterval2 = YAxis2.interval
      }

      if (this.chartXYNum[`${targetObj}XNum`] === undefined) {
        this.chartXYNum[`${targetObj}XNum`] = 1
        this.chartResetOriginal[targetObj].xMin = originalParam?.xMin ?? this.chartEditData[targetObj].xMin
        this.chartResetOriginal[targetObj].xMax = originalParam?.xMax ?? this.chartEditData[targetObj].xMax
        this.chartResetOriginal[targetObj].xInterval = originalParam?.xInterval ?? this.chartEditData[targetObj].xInterval

      }

      if (this.chartXYNum[`${targetObj}YNum`] === undefined) {
        this.chartXYNum[`${targetObj}YNum`] = 1
        this.chartResetOriginal[targetObj].yMin = originalParam?.yMin ?? this.chartEditData[targetObj].yMin
        this.chartResetOriginal[targetObj].yMax = originalParam?.yMax ?? this.chartEditData[targetObj].yMax
        this.chartResetOriginal[targetObj].yInterval = originalParam?.yInterval ?? this.chartEditData[targetObj].yInterval
      }

      if (this.chartXYNum[`${targetObj}YNum2`] === undefined) {
        this.chartXYNum[`${targetObj}YNum2`] = 1
        this.chartResetOriginal[targetObj].yMin2 = originalParam?.yMin2 ?? this.chartEditData[targetObj].yMin2
        this.chartResetOriginal[targetObj].yMax2 = originalParam?.yMax2 ?? this.chartEditData[targetObj].yMax2
        this.chartResetOriginal[targetObj].yInterval2 = originalParam?.yInterval2 ?? this.chartEditData[targetObj].yInterval2
      }

    },
    // 算出图例项的长度（包含前面的线）
    _getLegendBlockWidth(data, lineWidth, fontSize) {

      const divElement = document.getElementById('revealTextNone')
      divElement.innerHTML = data
      divElement.style.fontSize = `${fontSize}px`
      divElement.style.fontFamily = 'Times New Roman'

      // 获得：每个图例最多能占多大位置
      // 注:这里的5是图例前面的线条与图例后面的字体中间的距离
      // 注:这里的1是不知道为什么每次算出来的值都比实际值少1px
      let length = lineWidth + 5 + divElement.clientWidth + 1


      // 判断是否含中文
      const regex = /[\u4e00-\u9fa5]+/g;
      if (regex.test(data)) {
        divElement.innerHTML = data.replace(regex, '')
        const divLength1 = divElement.clientWidth
        divElement.innerHTML = data.match(regex)[0]
        divElement.style.fontSize = `${fontSize - 2}px`   //此处-2：中文比数字字号小两号
        divElement.style.fontFamily = 'Source Han Sans'
        const divLength2 = divElement.clientWidth

        length = lineWidth + 5 + divLength1 + divLength2 + 1
      }

      return length
    },
    _handleValue(processingResult, targetObj) {
      this.chartOriginalseries[targetObj] = processingResult[0] // 原始图表数据
      this.chartResetOriginal[targetObj].checkData = processingResult[1] // 重置数据
      this.chartResetOriginal[targetObj].series = _.cloneDeep(processingResult[1]) // 重置数据
      this.chartOriginalLegent[targetObj] = _.cloneDeep(processingResult[3]) // 原始图例  使用位置：在线编辑图表--图例数据
      this.chartEditData[targetObj].legend = _.cloneDeep(this.chartOriginalLegent[targetObj]) //拿到所有图例
      this.chartEditData[targetObj].series = _.cloneDeep(this.chartOriginalseries[targetObj]) //拿到所有数据
    },
    //算出图例的长度
    _getLegendDiscrepancy(template, original) {
      const regex = /[\u4e00-\u9fa5]+/g;
      const templateChinese = template.match(regex)[0]
      const originalChinese = original.match(regex)[0]

      const templateEnglish = template.replace(regex, '')
      const originalEnglish = original.replace(regex, '')

      const chinese = templateChinese.length - originalChinese.length
      const english = templateEnglish.length - originalEnglish.length

      return [template.split('').slice(0, chinese).join(''), template.replace(regex, '').split('').slice(0, english).join('')]
    },
    // 处理用户双击图表
    _handleDblclickEchart(target, topTarget, targetObj) {
      if (topTarget.z === undefined) return
      // Z: 0:坐标轴,3:折线,4:图例,6:标题,50:折点
      this.drawerVisible = true
      this.editObj = targetObj

      switch (topTarget.z) {
        case 0:
          this.$set(this.chartCheckObj[targetObj], 'editObj', 'axis')
          break;
        case 3:
        case 50:
          const axs = target.parent?.parent?.__ecComponentInfo?.index
          this.$set(this.chartCheckObj[targetObj], 'tag', axs)
          this.$set(this.chartCheckObj[targetObj], 'editObj', 'tag')
          break;
        case 4:
          this.$set(this.chartCheckObj[targetObj], 'editObj', 'legend')
          break;
        case 6:
          this.$set(this.chartCheckObj[targetObj], 'editObj', 'title')
          break;
      }

    },

    // 生成
    handleDrawerSubmit(value) {
      const legendData = {}
      const axisData = {}
      const titleData = {}
      const gridData = {}

      

      const legendKeyList = ['legendEdit', 'legendRevealList', 'legendWidth', 'legendHeight', 'legendGap', 'legendSort', 'legendEditName', 'legendBgColor',
        'legendOrient', 'legendZip', 'legendFontSize', 'legendTop', 'legendRight', 'legendNameType']
      for (let i = 0; i < legendKeyList.length; i++) {
        legendData[legendKeyList[i]] = legendKeyList[i] === 'legendEdit' ? value.legendList : value[legendKeyList[i]]
      }

      const axisKeyList = ['xMin', 'xMax', 'xInterval', 'xType', 'yMin', 'yMax', 'yInterval', 'yDecimalNum', 'yType', 'yMin2', 'yMax2', 'yInterval2', 'yType2']
      for (let i = 0; i < axisKeyList.length; i++) {
        axisData[axisKeyList[i]] = value[axisKeyList[i]]
      }

      const titleKeyList = ['chartTitle', 'XTitle', 'YTitle', 'YTitle2', 'titleTop', 'yTitleLetf', 'yTitleRight']
      for (let i = 0; i < titleKeyList.length; i++) {
        titleData[titleKeyList[i]] = value[titleKeyList[i]]
      }

      const gridKeyList = ['gridTop', 'gridLeft', 'gridRight', 'gridBottom']
      for (let i = 0; i < gridKeyList.length; i++) {
        gridData[gridKeyList[i]] = value[gridKeyList[i]]
      }

      // 记录修改
      if (value.targetEditObj && !this.historyRecord[this.editObj].includes(value.targetEditObj)) {
        this.historyRecord[this.editObj].push(value.targetEditObj)
      }

      // 修改重置,移除记录
      if (value.targetResetObj) {
        const haveIndex = this.historyRecord[this.editObj].findIndex(findItem => findItem === value.targetResetObj)
        this.historyRecord[this.editObj].splice(haveIndex, 1)
      }

      // 赋值的数组
      const assignArr = [
        'chartTitle', 'XTitle', 'YTitle', 'YTitle2', 'titleTop', 'yTitleLetf', 'yTitleRight',
         'legendFontSize','legendRevealList', 'legendWidth', 'legendHeight', 'legendGap', 'legendEditName', 'legendOrient', 'legendZip', 'legendBgColor',
          'legendTop', 'legendRight', 'legendNameType', 'xMin', 'xMax', 'xInterval', 'xType','yMin', 'yMax', 'yInterval', 'yDecimalNum',
           'yType', 'yMin2', 'yMax2', 'yInterval2', 'yType2', 'synchronization', 'gridTop', 'gridLeft', 'gridRight', 'gridBottom', "targetEditObj", "targetResetObj"]

      
      this.chartEditData[this.editObj].series = _.cloneDeep(value.checkData)

      for (let i = 0; i < assignArr.length; i++) { 
        this.chartEditData[this.editObj][assignArr[i]] = value[assignArr[i]]
      }

      // 处理传递给后端的值
      this._handleTemplateParams(value)
      this.initNormalEchart(
        this.editObj,
        legendData,
        value.checkData,
        axisData,
        titleData,
        gridData
      )
      this.$forceUpdate()
      
      // 记录数据到后端
      let chartTemplateParams = {}
      // 如果有templateId，则走修改路线，如果没有则走新建路线
      if(!this.reportChartTemplateList[this.editObj].templateId){
        chartTemplateParams = {
          targetChart:this.editObj,
          templateName:'报告ID修改默认模板',
          reportId:this.$route.query.testProgressId ?? this.$route.query.safetyTestIds.split(",")[0],
          originalParamJson:JSON.stringify(this.chartResetOriginal[this.editObj]),
          templateParamJson:JSON.stringify(this.reportChartTemplateList[this.editObj].templateParamJson),
        }
       this.saveChartTemplate(chartTemplateParams)

      }else{
        chartTemplateParams = {
          id:this.reportChartTemplateList[this.editObj].templateId,
          templateParamJson:JSON.stringify(this.reportChartTemplateList[this.editObj].templateParamJson),
        }
       this.updateChartTemplate(chartTemplateParams)
      }
    },
    // 处理模板值
    _handleTemplateParams(value){
      // status before: 图表执行前 after:图表执行后
      const isEdit = !!value.targetEditObj
      const templateParam = this.reportChartTemplateList[this.editObj].templateParamJson

      // 编辑
      if(isEdit && !['legendList','legendEditName','legendSort','legendRevealList','colorOption'].includes(value.targetEditObj)){
        // 通用属性
        if(value.targetEditIndex === undefined){
          templateParam[value.targetEditObj] = value[value.targetEditObj]
        }else if(value.targetEditIndex === 'all'){
          for(let i = 0; i < value.checkData.length ; i++){
            if(templateParam.checkData[i] === undefined) templateParam.checkData[i] = {}
            templateParam.checkData[i] = {
              ...templateParam.checkData[i],
              id:value.checkData[i].id,
              [value.targetEditObj]:value.checkData[i][value.targetEditObj]
            }
          }
          templateParam.allData[value.targetEditObj] = value.allData[value.targetEditObj]
        }else{
          let haveIndex = templateParam.checkData.findIndex(item => item.id === value.checkData[value.targetEditIndex].id)
          if(haveIndex === -1){
            templateParam.checkData.push({id:value.checkData[value.targetEditIndex].id})
            haveIndex = templateParam.checkData.length - 1
          } 
          templateParam.checkData[haveIndex][value.targetEditObj] = value.checkData[value.targetEditIndex][value.targetEditObj]
        }
      }

      // 重置
      if(!isEdit){
        if (value.targetResetIndex === undefined || value.targetResetIndex === 'yDecimalNum') {
          delete templateParam[value.targetResetObj]
        }else if(value.targetResetIndex === 'all'){
          for(let i = 0; i < value.checkData.length ; i++){
            delete templateParam.checkData[i][value.targetResetObj]
          }
          delete templateParam.allData[value.targetResetObj]
        }else{
          let haveIndex = templateParam.checkData.findIndex(item => item.id === value.checkData[value.targetResetIndex].id)
          delete templateParam.checkData[haveIndex][value.targetResetObj]
        }
      }

      // 图例-数据
      if(value.targetEditObj === 'legendList'){
        templateParam.legendData.legendIndeterminate = value.legendIndeterminate
        templateParam.legendData.checkAll = value.checkAll
        templateParam.legendData.legendList = value.legendList
        templateParam.legendData.legendOptions = templateParam.legendData.legendOptions ?? this.chartOriginalLegent[this.editObj]
      }

      // 图例-名称
      if(value.targetEditObj === 'legendEditName'){
        templateParam.legendData.legendList = value.legendList // 需同步更名后的数组
        templateParam.legendData.legendEditName = value.legendEditName 
        templateParam.legendData.legendRevealList = value.legendRevealList 
        templateParam.legendData.legendRevealOptions = value.legendRevealOptions

        // 找到改名的那根线，存储修改后的线名称
        const haveIndex =  templateParam.checkData.findIndex(item => item.id === value.checkData[value.targetEditIndex].id)
        if(haveIndex === -1){
          templateParam.checkData.push({id:value.checkData[value.targetEditIndex].id,name:value.checkData[value.targetEditIndex].name})
        }else{
          templateParam.checkData[haveIndex].name=value.checkData[value.targetEditIndex].name
        }

      }

      // 如果有图例-排序的修改
      if(value.targetEditObj === 'legendSort'){
        templateParam.legendData.legendSort = value.legendSort 
      }

      if(value.targetEditObj === 'legendNameType'){
        templateParam.legendData.legendSort = value.legendSort 
        templateParam.legendData.legendList = value.legendList
      }

      // 图例-显隐
      if(value.targetEditObj === 'legendRevealList'){
        templateParam.legendData.legendRevealIndeterminate = value.legendRevealIndeterminate
        templateParam.legendData.legendRevealcheckAll = value.legendRevealcheckAll
        templateParam.legendData.legendRevealList = value.legendRevealList
        templateParam.legendData.legendRevealOptions = value.legendRevealOptions
      }

      // 颜色方案
      if(value.targetEditObj === 'colorOption'){
         for(let i = 0; i < value.checkData.length ; i++){
            if(templateParam.checkData[i] === undefined) templateParam.checkData[i] = {}
            templateParam.checkData[i] = {
              ...templateParam.checkData[i],
              id:value.checkData[i].id,
              itemColor:value.checkData[i].itemColor,
              lineColor:value.checkData[i].lineColor
            }
          }
      }
      
    },
    _getTemplateParams(targetObj,titleData,gridData,legendData,axisData,legend){
      const templateParam = this.reportChartTemplateList[targetObj].templateParamJson

      const titleList = ['chartTitle', 'XTitle', 'YTitle', 'YTitle2', 'titleTop', 'yTitleLetf', 'yTitleRight']
      titleList.forEach(item => {
        titleData[item] = templateParam[item] ?? titleData[item]
      })

      const legendList = ['legendBgColor','legendOrient','legendZip','legendFontSize','legendTop', 'legendRight','legendWidth', 'legendHeight', 'legendGap']
      legendList.forEach(item => {
        legendData[item] = templateParam[item] ?? legendData[item]
      })

      const axisList = ['xType','xMin', 'xMax', 'xInterval', 'yType', 'yMin', 'yMax', 'yInterval', 'yType2', 'yMin2', 'yMax2', 'yInterval2']
      axisList.forEach(item => {
         axisData[item] = templateParam[item] ?? axisData[item]
      })

      

      const gridList = ['gridTop', 'gridLeft', 'gridRight', 'gridBottom']
      gridList.forEach(item => {
        gridData[item] = templateParam[item] ?? gridData[item]
      })

      // 如果有图例-数据的修改
      if(templateParam.legendData.legendList){
        legendData.legendEdit = templateParam.legendData.legendList
        if(templateParam.legendData.checkAll !== undefined) this.chartEditData[targetObj].checkAll = templateParam.legendData.checkAll
        if(templateParam.legendData.legendIndeterminate !== undefined) this.chartEditData[targetObj].legendIndeterminate = templateParam.legendData.legendIndeterminate
      }

      // 如果有图例-名称的修改
      if(templateParam.legendData.legendEditName){
        legendData.legendEditName = templateParam.legendData.legendEditName
        legendData.legendRevealList = templateParam.legendData.legendRevealList
      }

      // 如果有图例-名称的修改
      if(templateParam.legendData.legendSort){
        legendData.legendSort = templateParam.legendData.legendSort
      }

       // 如果有图例-显隐的修改
      if(templateParam.legendData.legendRevealList){
        templateParam.legendData.legendRevealList = templateParam.legendData.legendRevealList
        if(templateParam.legendData.legendRevealcheckAll !== undefined) this.chartEditData[targetObj].legendRevealcheckAll = templateParam.legendData.legendRevealcheckAll
        if(templateParam.legendData.legendRevealIndeterminate !== undefined) this.chartEditData[targetObj].legendRevealIndeterminate = templateParam.legendData.legendRevealIndeterminate
        if(templateParam.legendData.legendRevealOptions !== undefined) this.chartEditData[targetObj].legendRevealOptions = templateParam.legendData.legendRevealOptions
      }
      
      
      

      return {titleData,gridData,legendData,axisData,legend}
    },

    // 重置
    handleDrawerReset() {
      this.$confirm({
        title: '请确认是否重置图表?',
        content: '图表重置后，图表修改内容无法恢复',
        okText: '重置',
        cancelText: '取消',
        onOk:async () => {
          await this.deleteChartTemplate({ reportId:this.$route.query.testProgressId ?? this.$route.query.safetyTestIds.split(",")[0],id:this.reportChartTemplateList[this.editObj].templateId,targetChart:this.editObj },false)
          this.chartEditData[this.editObj] = this._getInitData(this.editObj, 'edit')
          this.chartFrist[this.editObj] = true
          this.initNormalEchart(this.editObj)
          this.drawerVisible = false
          this.$message.success("重置成功")
        },
        onCancel() {}
      });
    },

    // 重新选择模板
    async handleChangeTemplate(targetObj){
      await this.getChartTemplateRelationList(this.$route.query.testProgressId ?? this.$route.query.safetyTestIds.split(",")[0],[targetObj])
       this.chartEditData[this.editObj] = this._getInitData(this.editObj, 'edit')
        this.chartFrist[this.editObj] = true
        this.initNormalEchart(this.editObj)
        this.drawerVisible = false
    },

    // 下载
    handleDown(target) {
      let echart = this.echarts.init(document.getElementById(target), "walden", {devicePixelRatio: 2})
      const chartImageBase64 = echart.getConnectedDataURL({
        pixelRatio: 3, // 设置图片的分辨率，这里设置为2表示导出的图片分辨率为原始分辨率的两倍
        backgroundColor: "#fff" // 设置图片的背景颜色
      })
      const blob = this.b64toBlob(chartImageBase64.replace("data:image/png;base64,", ""))
      if (window.navigator.msSaveOrOpenBlob) {
        //兼容IE10
        navigator.msSaveBlob(blob, this.chartResetOriginal[target].chartTitle)
      } else {
        const href = URL.createObjectURL(blob) //创建新的URL表示指定的blob对象
        const a = document.createElement("a") //创建a标签
        a.style.display = "none"
        a.href = href // 指定下载链接
        a.download = this.chartResetOriginal[target].chartTitle //指定下载文件名
        a.click() //触发下载
        URL.revokeObjectURL(a.href) //释放URL对象
      }
    },
    // base64 转 blob
    b64toBlob(b64Data, contentType = null, sliceSize = null) {
      contentType = contentType || "image/png"
      sliceSize = sliceSize || 512
      let byteCharacters = window.atob(b64Data.substring(b64Data.indexOf(",") + 1))
      let byteArrays = []
      for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
        let slice = byteCharacters.slice(offset, offset + sliceSize)
        let byteNumbers = new Array(slice.length)
        for (let i = 0; i < slice.length; i++) {
          byteNumbers[i] = slice.charCodeAt(i)
        }
        var byteArray = new Uint8Array(byteNumbers)
        byteArrays.push(byteArray)
      }
      return new Blob(byteArrays, {type: contentType})
    },

  },
}