<<template>
  <a-modal title="进展编辑" :width="500" :visible="visible" :confirmLoading="confirmLoading" @ok="handleSubmit" @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
         <a-form-item
          style="display: none;"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-input v-decorator="['id']" />
        </a-form-item>
        <a-form-item label="进展日期" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-date-picker @change="dateChange" v-decorator="['weekDate', {rules: [{required: true, message: '请选择进展日期！'}]}]"/>
        </a-form-item>
        <a-form-item label="产品状态" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-select v-decorator="['productState', {rules: [{required: true, message: '请选择产品状态！'}]}]">
            <a-select-option value="1">
              绿灯
            </a-select-option>
            <a-select-option value="2">
              黄灯
            </a-select-option>
            <a-select-option value="3">
              红灯
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="产品进展" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-textarea placeholder="请输入产品进展" v-decorator="['productTxt', {rules: [{required: true, message: '请输入产品进展！'}]}]" />
        </a-form-item>

        <a-form-item label="质量状态" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-select v-decorator="['qualityState', {rules: [{required: true, message: '请选择质量状态！'}]}]">
            <a-select-option value="1">
              绿灯
            </a-select-option>
            <a-select-option value="2">
              黄灯
            </a-select-option>
            <a-select-option value="3">
              红灯
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="质量进展" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-textarea placeholder="请输入质量进展" v-decorator="['qualityTxt', {rules: [{required: true, message: '请输入质量进展！'}]}]" />
        </a-form-item>

        <a-form-item label="交付状态" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-select v-decorator="['summitState', {rules: [{required: true, message: '请选择交付状态！'}]}]">
            <a-select-option value="1">
              绿灯
            </a-select-option>
            <a-select-option value="2">
              黄灯
            </a-select-option>
            <a-select-option value="3">
              红灯
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="交付进展" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-textarea placeholder="请输入交付进展" v-decorator="['summitTxt', {rules: [{required: true, message: '请输入交付进展！'}]}]" />
        </a-form-item>

        <a-form-item label="产业化状态" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-select v-decorator="['industryState', {rules: [{required: true, message: '请选择产业化状态！'}]}]">
            <a-select-option value="1">
              绿灯
            </a-select-option>
            <a-select-option value="2">
              黄灯
            </a-select-option>
            <a-select-option value="3">
              红灯
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="产业化进展" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-textarea placeholder="请输入产业化进展" v-decorator="['industryTxt', {rules: [{required: true, message: '请输入产业化进展！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { weekProcessEdit } from '@/api/modular/system/weekProcessManage'
  import moment from "moment";
  export default {
    data () {
      return {
        weekDate:'',
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 18 }
        },
        visible: false,
        confirmLoading: false,
        visibleDef: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      moment,
      dateChange(date, dateString) {
        if (date == null) {
          this.weekDate = ''
        } else {
          this.weekDate = moment(date).format('YYYY-MM-DD')
        }
      },
      // 初始化方法
      edit (record) {
        this.visible = true
        this.weekDate = record.weekDate
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              weekDate: record.weekDate,
              productState: record.productState,
              qualityState: record.qualityState,
              summitState: record.summitState,
              industryState: record.industryState,
              productTxt : record.productTxt,
              qualityTxt : record.qualityTxt,
              summitTxt : record.summitTxt,
              industryTxt : record.industryTxt
            }
          )
        }, 100)
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            values.weekDate = this.weekDate
            weekProcessEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.visible = false
                this.confirmLoading = false
                this.$emit('ok', values)
              } else {
                this.$message.error('编辑失败：' + res.message)
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
