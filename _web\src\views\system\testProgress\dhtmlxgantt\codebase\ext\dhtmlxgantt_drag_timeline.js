/*
@license

dhtmlxGantt v.6.3.3 Standard

This version of dhtmlxGantt is distributed under GPL 2.0 license and can be legally used in GPL projects.

To use dhtmlxGantt in non-GPL projects (and get Pro version of the product), please obtain Commercial/Enterprise or Ultimate license on our site https://dhtmlx.com/docs/products/dhtmlxGantt/#licensing or contact <NAME_EMAIL>

(c) XB Software Ltd.

*/
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("ext/dhtmlxgantt_drag_timeline",[],e):"object"==typeof exports?exports["ext/dhtmlxgantt_drag_timeline"]=e():t["ext/dhtmlxgantt_drag_timeline"]=e()}(window,function(){return function(t){var e={};function n(o){if(e[o])return e[o].exports;var r=e[o]={i:o,l:!1,exports:{}};return t[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=t,n.c=e,n.d=function(t,e,o){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:o})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)n.d(o,r,function(e){return t[e]}.bind(null,r));return o},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="/codebase/",n(n.s=214)}({2:function(t,e){var n={second:1,minute:60,hour:3600,day:86400,week:604800,month:2592e3,quarter:7776e3,year:31536e3};function o(t,e){var n=[];if(t.filter)return t.filter(e);for(var o=0;o<t.length;o++)e(t[o],o)&&(n[n.length]=t[o]);return n}t.exports={getSecondsInUnit:function(t){return n[t]||n.hour},forEach:function(t,e){if(t.forEach)t.forEach(e);else for(var n=t.slice(),o=0;o<n.length;o++)e(n[o],o)},arrayMap:function(t,e){if(t.map)return t.map(e);for(var n=t.slice(),o=[],r=0;r<n.length;r++)o.push(e(n[r],r));return o},arrayFind:function(t,e){if(t.find)return t.find(e);for(var n=0;n<t.length;n++)if(e(t[n],n))return t[n]},arrayFilter:o,arrayDifference:function(t,e){return o(t,function(t,n){return!e(t,n)})},arraySome:function(t,e){if(0===t.length)return!1;for(var n=0;n<t.length;n++)if(e(t[n],n,t))return!0;return!1},hashToArray:function(t){var e=[];for(var n in t)t.hasOwnProperty(n)&&e.push(t[n]);return e},sortArrayOfHash:function(t,e,n){var o=function(t,e){return t<e};t.sort(function(t,r){return t[e]===r[e]?0:n?o(t[e],r[e]):o(r[e],t[e])})},throttle:function(t,e){var n=!1;return function(){n||(t.apply(null,arguments),n=!0,setTimeout(function(){n=!1},e))}},isArray:function(t){return Array.isArray?Array.isArray(t):t&&void 0!==t.length&&t.pop&&t.push},isDate:function(t){return!(!t||"object"!=typeof t||!(t.getFullYear&&t.getMonth&&t.getDate))},isStringObject:function(t){return t&&"object"==typeof t&&"function String() { [native code] }"===Function.prototype.toString.call(t.constructor)},isNumberObject:function(t){return t&&"object"==typeof t&&"function Number() { [native code] }"===Function.prototype.toString.call(t.constructor)},isBooleanObject:function(t){return t&&"object"==typeof t&&"function Boolean() { [native code] }"===Function.prototype.toString.call(t.constructor)},delay:function(t,e){var n,o=function(){o.$cancelTimeout(),t.$pending=!0;var r=Array.prototype.slice.call(arguments);n=setTimeout(function(){t.apply(this,r),o.$pending=!1},e)};return o.$pending=!1,o.$cancelTimeout=function(){clearTimeout(n),t.$pending=!1},o.$execute=function(){t(),t.$cancelTimeout()},o},objectKeys:function(t){if(Object.keys)return Object.keys(t);var e,n=[];for(e in t)Object.prototype.hasOwnProperty.call(t,e)&&n.push(e);return n},requestAnimationFrame:function(t){var e=window;return(e.requestAnimationFrame||e.webkitRequestAnimationFrame||e.msRequestAnimationFrame||e.mozRequestAnimationFrame||e.oRequestAnimationFrame||function(t){setTimeout(t,1e3/60)})(t)},isEventable:function(t){return t.attachEvent&&t.detachEvent}}},213:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=n(2),r=function(){function t(){var t=this;this._mouseDown=!1,this._calculateDirectionVector=function(){if(t._trace.length>=10){for(var e=t._trace.slice(t._trace.length-10),n=[],o=1;o<e.length;o++)n.push({x:e[o].x-e[o-1].x,y:e[o].y-e[o-1].y});var r={x:0,y:0};return n.forEach(function(t){r.x+=t.x,r.y+=t.y}),{magnitude:Math.sqrt(r.x*r.x+r.y*r.y),angleDegrees:180*Math.atan2(Math.abs(r.y),Math.abs(r.x))/Math.PI}}return null},this._applyDndReadyStyles=function(){t._timeline.$task.classList.add("gantt_timeline_move_available")},this._clearDndReadyStyles=function(){t._timeline.$task.classList.remove("gantt_timeline_move_available")},this._getScrollPosition=function(t){return{x:gantt.$ui.getView(t.$config.scrollX).getScrollState().position,y:gantt.$ui.getView(t.$config.scrollY).getScrollState().position}},this._countNewScrollPosition=function(e){var n=t._calculateDirectionVector(),o=t._startPoint.x-e.x,r=t._startPoint.y-e.y;return n&&(n.angleDegrees<15?r=0:n.angleDegrees>75&&(o=0)),{x:t._scrollState.x+o,y:t._scrollState.y+r}},this._setScrollPosition=function(t,e){o.requestAnimationFrame(function(){gantt.$ui.getView(t.$config.scrollX).scroll(e.x),gantt.$ui.getView(t.$config.scrollY).scroll(e.y)})},this._stopDrag=function(e){t._trace=[],gantt.$root.classList.remove("gantt_noselect"),void 0!==t._originalReadonly&&(gantt.config.readonly=t._originalReadonly),void 0!==t._originAutoscroll&&(gantt.config.autoscroll=t._originAutoscroll);var n=gantt.config.drag_timeline.useKey;n&&!0!==e[n]||t._mouseDown&&(t._mouseDown=!1)},this._startDrag=function(e){t._originAutoscroll=gantt.config.autoscroll,gantt.config.autoscroll=!1,gantt.$root.classList.add("gantt_noselect"),t._originalReadonly=gantt.config.readonly,gantt.config.readonly=!0,t._trace=[],t._mouseDown=!0;var n=t._getScrollPosition(t._timeline),o=n.x,r=n.y;t._scrollState={x:o,y:r},t._startPoint={x:e.clientX,y:e.clientY},t._trace.push(t._startPoint)},this._domEvents=gantt._createDomEventScope(),this._trace=[]}return t.create=function(){return new t},t.prototype.destructor=function(){this._domEvents.detachAll()},t.prototype.attach=function(t){var e=this;this._timeline=t,this._domEvents.attach(t.$task,"mousedown",function(t){var n=gantt.config.drag_timeline,o=n.useKey,r=n.ignore;if(!1!==n.enabled){var i=".gantt_task_line, .gantt_task_link";void 0!==r&&(i=r instanceof Array?r.join(", "):r),i&&gantt.utils.dom.closest(t.target,i)||o&&!0!==t[o]||e._startDrag(t)}}),this._domEvents.attach(document,"keydown",function(t){var n=gantt.config.drag_timeline.useKey;n&&!0===t[n]&&e._applyDndReadyStyles()}),this._domEvents.attach(document,"keyup",function(t){var n=gantt.config.drag_timeline.useKey;n&&!1===t[n]&&(e._clearDndReadyStyles(),e._stopDrag(t))}),this._domEvents.attach(document,"mouseup",function(t){e._stopDrag(t)}),this._domEvents.attach(gantt.$root,"mouseup",function(t){e._stopDrag(t)}),this._domEvents.attach(document,"mouseleave",function(t){e._stopDrag(t)}),this._domEvents.attach(gantt.$root,"mouseleave",function(t){e._stopDrag(t)}),this._domEvents.attach(gantt.$root,"mousemove",function(n){var o=gantt.config.drag_timeline.useKey;if((!o||!0===n[o])&&!0===e._mouseDown){e._trace.push({x:n.clientX,y:n.clientY});var r=e._countNewScrollPosition({x:n.clientX,y:n.clientY});e._setScrollPosition(t,r),e._scrollState=r,e._startPoint={x:n.clientX,y:n.clientY}}})},t}();e.EventsManager=r},214:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=n(213);gantt.ext||(gantt.ext={}),gantt.ext.dragTimeline={create:function(){return o.EventsManager.create()}},gantt.config.drag_timeline={enabled:!0}}})});
//# sourceMappingURL=dhtmlxgantt_drag_timeline.js.map