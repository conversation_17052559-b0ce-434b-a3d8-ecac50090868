<template>
  <div>
    <div class="right-top-div">
      <a-button v-if="!editFlag && hasPerm('testProjectTodoTask:getBatteryInfoById')" style="float:right;margin-right: 8px;font-size: 12px;border-radius: 4px;" size="small" type="primary" @click="handleEdit(true)">启动编辑</a-button>
      <a-button v-if="editFlag && hasPerm('testProjectTodoTask:getBatteryInfoById')" style="float:right;margin-right: 8px;font-size: 12px;border-radius: 4px;" size="small" type="primary" @click="handleEdit(false)">关闭编辑</a-button>
      <a-button style="float:right;margin-right: 20px;font-size: 12px;border-radius: 4px;" type="primary" size="small" @click="exportAttach">导出附件</a-button>
    </div>
    <div class="all-wrapper">
      <div style="border-radius: 0 10px 10px 10px;height: 800px;background-color: white;">
        <div style="width:70%;float:left;">
          <a-spin :spinning="attachLoading">
            <p style="position: absolute;margin-top: 15px;margin-left:30px;z-index: 2;color: black;font-size: 13px;">提示：当视频无法在线播放时，需先将视频下载到本地设备，之后再进行播放。</p>
            <div v-if="newestAttachFlag && editFlag" style="float: left;height: 30px;width: 150px;margin:40px 0px 2px 30px;">
              <a-button style="font-size: 12px;border-radius: 4px;z-index: 2;" size="small" type="primary" @click="() => (chooseCodeVisible = true, selectedRowCodeKeys = [])">上传</a-button>
            </div>
            <a-table style="padding: 40px 0px 0px 30px;"
                     :customRow="customRow"
                     :columns="attachColumns"
                     :rowKey="record => record.id"
                     :row-selection="{
                    selectedRowKeys: selectedRowKeys, selectedRows: selectedRows,
                    onChange: onSelectChange, columnWidth:10}"
                     :data-source="attachDataList"
                     :pagination="paginationConfig"
                     :loading="attachTableLoading"
                     bordered>

              <template slot="attachment" slot-scope="text, record, index, columns">
                <span v-for="fileItem in record.attachment" style="display: flex;justifyContent: center;alignItems: center;margin-top: 3px">
                  <a style="color: #0d84ff;text-align: center;" @click="openFileOrDownload(fileItem.id, fileItem.name)">{{ fileItem.name }}</a>
                  <a-icon style="font-size: 20px;color: #0d84ff;margin-left: 15px" type="download" @click="downloadSingleAttach(fileItem)"/>
                  <a-popconfirm
                    placement="topRight"
                    ok-text="删除"
                    cancel-text="取消"
                    @confirm="removeAttach(record, fileItem)">
                    <template slot="title">确认删除附件"{{ fileItem.name }}"吗？</template>
                    <a-icon v-if="editFlag" style="font-size: 20px;color: #0d84ff;margin-left: 10px" type="delete"/>
                  </a-popconfirm>
                </span>
                <span v-if="(uploadProgress > 0 || uploadProgressShow) && record.stage === uploadingStage" style="display: flex;justifyContent: center;alignItems: center;width: 100%;padding-top: 8px;">
                  <a-progress :strokeWidth="12" :percent="uploadProgress"></a-progress>
                </span>
              </template>

              <template slot="action" slot-scope="text, record, index, columns">
                <a-upload
                  name="file"
                  v-if="!newestAttachFlag && editFlag"
                  :headers="headers"
                  :customRequest="handleUpload"
                  :data="uploadData"
                  :action="picOrVidPostUrl"
                  :multiple="false"
                  :showUploadList="false"
                  @change="uploadAttach($event,record)">
                  <a style="color: #0d84ff;fontSize: 12px;">上传</a>
                </a-upload>
              </template>

              <template slot="newestAttachControl" slot-scope="text, record, index, columns">
                <a v-if="record.attachId && !editFlag" style="color: #0d84ff;fontSize: 12px;" @click="downloadSingleAttach({id: record.attachId, name: record.attachName})">下载</a>
                <a-popconfirm
                  placement="topRight"
                  ok-text="删除"
                  cancel-text="取消"
                  @confirm="deleteCodeAttachment(record)">
                  <template slot="title"> 确认删除附件"{{ record.attachName }}"吗 </template>
                  <a v-if="record.attachId && editFlag" style="color: #0d84ff;fontSize: 12px;">删除</a>
                </a-popconfirm>
              </template>

              <template slot="attachName" slot-scope="text, record, index, columns">
                <a style="color: #0d84ff;text-align: center;" @click="openFileOrDownload(record.attachId, record.attachName)">{{ record.attachName }}</a>
              </template>

            </a-table>
          </a-spin>
          <div v-if="newestAttachFlag && (uploadProgress > 0 || uploadProgressShow)" style="width: 40%;margin-left: 30px;">
            <a-progress :strokeWidth="12" :percent="uploadProgress"></a-progress>
          </div>
        </div>
      </div>
    </div>
    <!-- 预览视频/图片  -->
    <a-drawer
      :bodyStyle="{ height: '100%' }"
      placement="right"
      :closable="false"
      width="70%"
      :visible="filePreviewVisible"
      @close="filePreviewVisible = false"
    >
      <iframe :src="iframeUrl" width="100%" height="100%"></iframe>
    </a-drawer>

    <a-modal
      title="测试编码选择"
      width="30%"
      :height="200"
      :bodyStyle="{ padding: 0 }"
      :visible="chooseCodeVisible"
      style="padding: 0"
      :maskClosable="false"
      @cancel="() => (chooseCodeVisible = false)"
    >
      <div class="child-table">
        <a-table
          :columns="chooseCodeColumns"
          :dataSource="testDataList.filter(item => item.batteryStatus === 'ongoing' || item.batteryStatus === 'testDone')"
          class="mt10"
          bordered
          :rowKey="record => record.cellTestCode"
          :rowSelection="{ selectedRowKeys: selectedRowCodeKeys, onChange: onSelectCodeChange }"
          :pagination="false"
        >
        </a-table>
      </div>
      <template slot="footer" slot-scope="text, record">
        <div>
          <a-button :style="{marginRight: selectedRowCodeKeys.length === 0 ? '0px' : '8px'}" @click="() => (chooseCodeVisible = false)">
            取消
          </a-button>
          <a-button v-if="selectedRowCodeKeys.length === 0" type="primary" @click="chooseAtLeastOneCode">确定</a-button>
          <a-upload
            v-else
            name="file"
            :headers="headers"
            :customRequest="handleUpload"
            :data="uploadData"
            :action="picOrVidPostUrl"
            :multiple="false"
            :disabled="attachLoading"
            :showUploadList="false"
            @change="uploadCodeAttachment($event)">
            <a-spin :spinning="attachLoading">
              <a-button type="primary">确定</a-button>
            </a-spin>
          </a-upload>
        </div>
      </template>
    </a-modal>

  </div>

</template>
<script>
import {STable} from "@/components";
import {Pagination} from 'ant-design-vue';
import {
  get, getAttachBySafetyTestIds, updatePicOrVidOfAq
} from "@/api/modular/system/testProgressManager";
import {calendarCommon} from "./mixin/calendarCommon.js";
import { getMinioDownloadUrl, getMinioPreviewUrl } from "@/api/modular/system/fileManage";
import { downloadMinioFile, downloadMinioFileList } from "@/utils/util";
import Vue from "vue";
import { ACCESS_TOKEN } from "@/store/mutation-types";
import axios from "axios";
import { formatDate } from "@/utils/format";

export default {
  components: {
    STable,
    'a-pagination': Pagination
  },
  mixins: [calendarCommon],
  data: function () {
    return {
      testDataList: [],
      curSafetyTest: null,
      selectedRowCodeKeys: [],
      chooseCodeColumns: [
        {
          title: "序号",
          dataIndex: "index",
          align: "center",
          customRender: (text, record, index) => `${index + 1}`
        },
        {
          title: "测试编码",
          dataIndex: "cellTestCode",
          align: "center",
          scopedSlots: {
            customRender: "cellTestCode"
          }
        }
      ],
      newestAttachColumns: [
        {
          title: "样品编号",
          dataIndex: "sampleNo",
          width: 80,
          align: "center",
        },
        {
          title: "绝对时间",
          dataIndex: "absoluteDate",
          width: 140,
          align: "center"
        },
        {
          title: "名称",
          dataIndex: "attachName",
          width: 200,
          align: "center",
          scopedSlots: { customRender: 'attachName' },
        },
        {
          title: "操作",
          width: 100,
          align: "center",
          scopedSlots: { customRender: 'newestAttachControl' },
        }
      ],
      chooseCodeVisible: false,
      newestAttachFlag: false,
      data: {},
      uploadingStage: null,
      iframeUrl: null,
      selectedRowKeys: [],
      selectedRows: [],
      attachWidth: 0,
      attachHeight: 0,
      paginationConfig: {
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '30', '40', '50'], // 显示的每页数量选项
        size: "small",
        showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
      },
      attachDataList: [],
      attachTableLoading: false,
      uploadProgress: 0,
      uploadProgressShow: false,
      attachLoading: false,
      editFlag: false,
      picOrVidPostUrl: "/api/sysFileInfo/minioUpload",
      uploadData: { bucket: 'safetylab' },
      headers: {
        Authorization: "Bearer " + Vue.ls.get(ACCESS_TOKEN)
      },
      attachColumns: [
        {
          title: "测试阶段",
          dataIndex: "stage",
          width: 80,
          align: "center",
          customRender: (text, record, index) => {
            if (this.beforeAfterFlag) {
              if (record.stage === "0") {
                return "测试前"
              } else {
                return "测试后"
              }
            } else {
              return text
            }
          }
        },
        {
          title: "累积循环次数",
          dataIndex: "totalCycleTime",
          width: 120,
          align: "center"
        },
        {
          title: "绝对时间",
          dataIndex: "absoluteDate",
          width: 120,
          align: "center"
        },
        {
          title: "附件",
          dataIndex: "attachment",
          width: 300,
          align: "center",
          scopedSlots: {
            customRender: "attachment"
          }
        },
      ],
      testProgress: null,
      beforeAfterFlag: false,
      safetyTestFlag: false,
      filePreviewVisible: false,
    }
  },
  mounted() {
    this.getAttachDataOfAq()
    this.safetyTestFlag = true
  },
  created() {
  },
  methods: {
    chooseAtLeastOneCode() {
      return this.$message.warning("请至少选择一个电芯")
    },
    onSelectCodeChange(selectedRowKeys, selectedRows) {
      this.selectedRowCodeKeys = selectedRowKeys
    },
    uploadCodeAttachment(info) {
      this.chooseCodeVisible = false
      this.attachLoading = true
      if (info.file.status === "done") {
        let file = info.file
        let update = {}
        let uploadAttachment = {
          id: file.response.data,
          name: file.name,
          time: formatDate(new Date(), true)
        }
        update.id = this.curSafetyTest.id
        update.safetyTestIdListString = this.curSafetyTest.safetyTestIdListString
        update.attachment = JSON.stringify(uploadAttachment)
        update.cellTestCodes = this.selectedRowCodeKeys.join(',')
        updatePicOrVidOfAq(update, 'add', 'newestAttachment', -1).then(res => {
          if (res.success) {
            this.selectedRowCodeKeys = []
            this.getAttachDataOfAq()
            this.$message.success(`${info.file.name} 上传成功`)
          } else {
            this.$message.error("上传失败：" + res.message)
          }
          setTimeout(() => {
            this.attachLoading = false
          },500)
        })
      } else if (info.file.status === "error") {
        this.attachLoading = false
        this.$message.error(`${info.file.name} 上传失败`)
      } else {
        this.attachLoading = false
      }
    },
    deleteCodeAttachment(battery) {
      let update = {}
      update.id = this.curSafetyTest.id
      update.safetyTestIdListString = this.curSafetyTest.safetyTestIdListString
      update.cellTestCodes = battery.cellTestCodeList
      updatePicOrVidOfAq(update, 'delete', 'newestAttachment', -1).then(res => {
        if (res.success) {
          this.getAttachDataOfAq()
          this.$message.success("删除成功")
        } else {
          this.$message.error("删除失败：" + res.message)
        }
      })
    },
    //直接调用lims接口预览或下载
    async openFileOrDownload(fileId, fileName) {
      //pbi上传的文件
      if (fileId) {
        getMinioPreviewUrl(fileId).then(res => {
          //预览
          let suffixArray = ['.mp4','.MP4','.jpg','.JPG','.png','.PNG','.gif','.GIF','.pdf','.PDF']
          if (fileName && suffixArray.findIndex(item => fileName.indexOf(item) > -1) !== -1) {
            this.iframeUrl = res.data.replace("http://10.100.1.99:9000/", "/minioDownload/")
            this.filePreviewVisible = true
          } else {
            this.$message.warning('该文件格式无法预览，请先下载后查看')
          }
        })
      }
    },
    downloadSingleAttach(file) {
      getMinioDownloadUrl(file.id, encodeURIComponent(file.name)).then(res1 => {
        downloadMinioFile(res1.data)
      })
    },
    async exportAttach() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      let urlList = []
      if (this.newestAttachFlag) {
        for (const attachItem of this.selectedRows) {
          await getMinioDownloadUrl(attachItem.attachId, encodeURIComponent(attachItem.attachName)).then(res1 => {
            urlList.push(res1.data)
          })
        }
      } else {
        for (let i = 0; i < this.selectedRows.length; i++) {
          let attachmentList = this.selectedRows[i].attachment
          if (!attachmentList) {
            continue;
          }
          if (attachmentList.length > 0) {
            for (const attachItem of attachmentList) {
              await getMinioDownloadUrl(attachItem.id, encodeURIComponent(attachItem.name)).then(res1 => {
                urlList.push(res1.data)
              })
            }
          }
        }
      }
      if (urlList.length > 0) {
        downloadMinioFileList(urlList)
      } else {
        this.$message.warning('当前没有可导出的附件')
      }
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    customRow(record) {
      return {
        props: {},
        on: { // 事件
          click: (event) => {
          },
        },
      };
    },
    getAttachDataOfAq() {
      getAttachBySafetyTestIds({ safetyTestIds: this.$route.query.safetyTestIds}).then(res => {
        if (res.success) {
          this.attachDataList = res.data.resultMapList
          let testType = res.data.testType
          if (testType === 'newest_before_after') {
            this.newestAttachFlag = true
            this.attachColumns = this.newestAttachColumns
            this.curSafetyTest = res.data.curSafetyTest
            this.testDataList = JSON.parse(this.curSafetyTest.testData)
          } else if (testType === 'before_after') {
            this.beforeAfterFlag = true
            this.attachColumns = this.attachColumns.filter(o => o.dataIndex !== 'totalCycleTime')
          }
          this.selectedRows = []
          this.selectedRowKeys = []
        } else {
          this.$message.error('获取附件数据失败：' + res.message)
        }
      })
    },
    handleEdit(flag) {
      this.selectedRows = []
      this.selectedRowKeys = []
      this.editFlag = flag;
      this.attachTableLoading = true;
      setTimeout(() => {
        this.attachTableLoading = false;
      }, 300)
      if (!this.newestAttachFlag && this.editFlag) {
        this.attachColumns.push({
          title: "操作",
          dataIndex: "action",
          width: 100,
          align: "center",
          scopedSlots: {
            customRender: "action"
          }
        })
      } else {
        this.attachColumns = this.attachColumns.filter(item => item.dataIndex !== "action")
      }
    },
    removeAttach(curSafetyTest, fileItem) {
      let update = {}
      update.id = curSafetyTest.id
      update.deleteAttachId = fileItem.id
      update.stage = curSafetyTest.stage
      if (!update.id || update.id === 'null') { // 同一个测试项目分配多次任务的情况
        update.ids = this.$route.query.safetyTestIds
      }
      this.attachLoading = true
      updatePicOrVidOfAq(update, 'delete', 'attachmentOfReport', -1).then(res => {
        if (res.success) {
          this.getAttachDataOfAq()
          this.$message.success("删除成功")
        } else {
          this.$message.error("删除失败：" + res.message)
        }
        setTimeout(() => {
          this.attachLoading = false
        },500)
      })
    },
    handleUpload(options) {
      const { file, onSuccess, onError } = options;
      const formData = new FormData();
      formData.append('file', file);
      formData.append('bucket','safetylab');

      axios.post('/api/sysFileInfo/minioUpload', formData, {
        headers: {
          Authorization: 'Bearer ' + Vue.ls.get('Access-Token'),
        },
        onUploadProgress: (progressEvent) => {
          this.attachLoading = true
          this.uploadProgressShow = true
          if (progressEvent.total > 0) {
            this.uploadProgress = Math.round((progressEvent.loaded / progressEvent.total ) * 100)  == 100?99:Math.round((progressEvent.loaded / progressEvent.total ) * 100);
          }
        },
      })
        .then((response) => {
          this.uploadProgressShow = true
          onSuccess(response.data, file);
          this.uploadProgress = 100;
          setTimeout(() => {
            this.attachLoading = false
            this.uploadProgress = 0;
            this.uploadProgressShow = false
          }, 2000)
          // 重置进度条
        })
        .catch((error) => {
          onError(error);
          this.uploadProgress = 0; // 重置进度条
        });
    },
    uploadAttach(info,curSafetyTest) {
      this.uploadingStage = curSafetyTest.stage
      this.attachLoading = true
      if (info.file.status === "done") {
        let file = info.file
        let update = {}
        update.id = curSafetyTest.id
        if (!update.id || update.id === 'null') { // 同一个测试项目分配多次任务的情况
          update.ids = this.$route.query.safetyTestIds
        }
        update.stage = curSafetyTest.stage
        update.addAttachMap = {
          id: file.response.data,
          name: file.name,
          time: formatDate(new Date(), true)
        }
        updatePicOrVidOfAq(update, 'add', 'attachmentOfReport', -1).then(res => {
          if (res.success) {
            this.getAttachDataOfAq()
            this.$message.success(`${info.file.name} 上传成功`)
          } else {
            this.$message.error("上传失败：" + res.message)
          }
          setTimeout(() => {
            this.attachLoading = false
          },1000)
        })
      } else if (info.file.status === "error") {
        this.attachLoading = false
        this.$message.error(`${info.file.name} 上传失败`)
      } else {
        this.attachLoading = false
      }
    },

  }
}
</script>
<style lang="less" scoped>
.right-top-div {
  position: absolute;
  top: 48px;
  right: 10px;
  height:43.5px;
  display: flex;
  align-items: center;
}
/deep/ .ant-table-tbody > tr > td {
  font-size: 12px;
  padding: 4px;
  font-weight: 400;
  overflow-wrap: break-word;
}
/deep/ .ant-table-thead > tr > th{
  font-size: 13px;
  padding: 5px;
  font-weight: 500;
  overflow-wrap: break-word;
}
</style>