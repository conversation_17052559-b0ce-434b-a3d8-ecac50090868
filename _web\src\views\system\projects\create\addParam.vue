<template>
	<div style="background:#fff;overflow: hidden;">
		<a-tabs
			:style="{ marginTop: '12px' }"
			type="card"
			:activeKey="activeKey"
			@change="key => onTabChange(key)"
			destroyInactiveTabPane
		>
			<a-tab-pane v-for="(val, key) in statesMap" :key="key" :tab="val">
				<div :style="{ height: windowHeight + 'px', overflowY: 'scroll' }">
					<addparamForm
						ref="addparamForm"
						:issueId="issueId"
						:isNoEdit="isNoEdit"
						:activeKey="activeKey"
						:projectdetail="projectdetail"
					/>
				</div>
			</a-tab-pane>
			<div slot="tabBarExtraContent">
				<a-button
					v-if="hasPerm('productparam:save') || activeKey == state[projectdetail.state]"
					@click="add"
					:style="{ marginRight: '16px' }"
					type="primary"
				>
					{{ isNoEdit ? "编辑" : "保存" }}
				</a-button>
				<a-button
					v-if="(hasPerm('productparam:save') || activeKey == state[projectdetail.state]) && activeKey !== '2'"
					@click="showCopy"
					:style="{ marginRight: '16px' }"
					type="primary"
				>
					一键复制
				</a-button>
			</div>
		</a-tabs>
	</div>
</template>

<script>
import addparamForm from "./addParamForm"
export default {
	components: {
		addparamForm
	},
	props: {
		issueId: {
			type: Number,
			default: 0
		},
		projectdetail: {
			type: Object,
			default: {}
		}
	},
	data() {
		return {
			state: {
				1: "2",
				2: "2",
				3: "3",
				4: "4",
				5: "4",
				6: "4",
				7: "4",
				8: "4"
			},
			windowHeight: document.documentElement.clientHeight - 110,
			activeKey: "2",
			statesMap: {
				"2": "A样",
				"3": "B样",
				"4": "C样"
			},
			isNoEdit: true,
			isNoEdit1: true,
			isNoEdit2: true,
			isNoEdit3: true
		}
	},
	created() {
		this.activeKey = this.state[this.projectdetail.state]
	},
	methods: {
		onTabChange(key) {
			this.activeKey = key
			this.isNoEdit = key == 2 ? this.isNoEdit1 : key == 3 ? this.isNoEdit2 : this.isNoEdit3
		},
		add() {
			if (this.isNoEdit) {
				if (this.activeKey == 2) this.isNoEdit1 = false
				if (this.activeKey == 3) this.isNoEdit2 = false
				if (this.activeKey == 4) this.isNoEdit3 = false
				this.isNoEdit = false
				return
			}

			this.$refs.addparamForm[0].add()
		},
		showCopy() {
			this.$refs.addparamForm[0].showCopy()
		}
	}
}
</script>

<style lang="less" scoped="">
/deep/.ant-tabs-nav-scroll {
	margin-left: 60px;
}
/deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab {
	margin-right: 20px;
}
</style>
