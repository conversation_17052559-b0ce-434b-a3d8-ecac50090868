<template>



  <div class="container">

    <!-- 标题 start -->
    <div class="head-title">
      <div class="line mr10"></div>
      <span class="title">测试失效分析报告管理</span>
    </div>

<!--    <a-tabs default-active-key="null" v-model="productDepartment" type="card" @change="$refs.table.refresh()">
      <a-tab-pane :key="null" tab="全部"> </a-tab-pane>
      <a-tab-pane v-for="item in productDepartmentList" :key="item.id" :tab="item.customvalue"></a-tab-pane>

    </a-tabs>-->

    <pbiTabs :tabsList="laboratoryList" :activeKey="laboratoryId" @clickTab="handleTabsChange"></pbiTabs>


    <!-- 标题 end -->
    <!-- 筛选 start -->
    <pbiSearchContainer>
      <pbiSearchItem label='产品名称'  :span="6" >
        <a-input-search size='small' class="filter-input" @change="$refs.table.refresh()" v-model="queryParam.productName" placeholder="请输入产品名称" />
      </pbiSearchItem>
      <pbiSearchItem label='文件编号' :span="6" >
        <a-input size='small' class="filter-input" @change="$refs.table.refresh()" v-model="queryParam.fileCode" placeholder="请输入文件编号" />
      </pbiSearchItem>
      <pbiSearchItem label='FA责任人' :span="6" >
        <a-input size='small' class="filter-input" @change="$refs.table.refresh()" v-model="queryParam.faChargeName" placeholder="请输入文件编号" />
      </pbiSearchItem>
      <pbiSearchItem label='FA状态' :span="6" v-if='isShowAllSearch'>
        <a-select mode="multiple" :maxTagCount="1" class="filter-select" @change="$refs.table.refresh()" v-model="queryParam.faStatusList" placeholder="请选择FA状态" allow-clear >
          <a-select-option v-for="(item,index) in faStatusList" :key="index" :value="item.code" >{{ item.value }}</a-select-option>
        </a-select>
      </pbiSearchItem>
      <pbiSearchItem label='测试类别' :span="6" v-if='isShowAllSearch'>
        <a-select mode="multiple" :maxTagCount="1" class="filter-select" @change="$refs.table.refresh()" v-model="queryParam.testCateList" placeholder="请选择测试类别" allow-clear >
          <a-select-option v-for="(item,index) in testCateList" :key="index" :value="item.code" >{{ item.value }}</a-select-option>
        </a-select>
      </pbiSearchItem>
      <pbiSearchItem label='失效类别' :span="6" v-if='isShowAllSearch'>
        <a-select mode="multiple" :maxTagCount="1" class="filter-select" @change="$refs.table.refresh()" v-model="queryParam.failureCateList" placeholder="请选择失效类别" allow-clear >
          <a-select-option v-for="(item,index) in failureCateList" :key="index" :value="item.code" >{{ item.value }}</a-select-option>
        </a-select>
      </pbiSearchItem>


      <pbiSearchItem type='btn' :span="isShowAllSearch ? 12 : 6">
        <div class="secondary-btn">
          <a-button class="mr10" @click="resetSearch">重置</a-button>
        </div>
        <div class='toggle-btn'>
          <a-button size='small' type='link' @click='handleChangeSearch'>
            {{ isShowAllSearch ? '收起' : '展开' }}
            <span v-if='isShowAllSearch'>
										<a-icon type='double-left'/>
									</span>
            <span v-else>
										<a-icon type='double-right'/>
									</span>
          </a-button>
        </div>
      </pbiSearchItem>
    </pbiSearchContainer>
<!--    <div class="filter-wrapper mt5">
      <div class="filter-left">
        <div class="filter-block mr10">
          <a-input class="filter-input" @change="$refs.table.refresh()" v-model="queryParam.productName" placeholder="请输入产品名称" />
        </div>
        <div class="filter-block mr10">
          <a-input class="filter-input" @change="$refs.table.refresh()" v-model="queryParam.fileCode" placeholder="请输入文件编号" />
        </div>
&lt;!&ndash;        <div class="filter-block mr10">&ndash;&gt;
&lt;!&ndash;          <a-input class="filter-input" @change="$refs.table.refresh()" v-model="queryParam.cellCode" placeholder="电芯编码" />&ndash;&gt;
&lt;!&ndash;        </div>&ndash;&gt;
        <div class="filter-block mr10">
          <a-input class="filter-input" @change="$refs.table.refresh()" v-model="queryParam.faChargeName" placeholder="请输入FA责任人" />
        </div>
        <div class="filter-block mr10">
          <a-select mode="multiple" :maxTagCount="1" class="filter-select" @change="$refs.table.refresh()" v-model="queryParam.faStatusList" placeholder="请选择FA状态" allow-clear >
            <a-select-option v-for="(item,index) in faStatusList" :key="index" :value="item.code" >{{ item.value }}</a-select-option>
          </a-select>
        </div>
        <div class="filter-block mr10">
          <a-select mode="multiple" :maxTagCount="1" class="filter-select" @change="$refs.table.refresh()" v-model="queryParam.testCateList" placeholder="请选择测试类别" allow-clear >
            <a-select-option v-for="(item,index) in testCateList" :key="index" :value="item.code" >{{ item.value }}</a-select-option>
          </a-select>
        </div>
        <div class="filter-block mr10">
          <a-select mode="multiple" :maxTagCount="1" class="filter-select" @change="$refs.table.refresh()" v-model="queryParam.failureCateList" placeholder="请选择失效类别" allow-clear >
            <a-select-option v-for="(item,index) in failureCateList" :key="index" :value="item.code" >{{ item.value }}</a-select-option>
          </a-select>
        </div>


        <div class="filter-block mr10">
          <a-button style="" @click="resetSearch">重置</a-button>
        </div>
      </div>

    </div>-->
    <!-- 筛选 end -->
    <!-- 表格 start -->
    <div class="table-wrapper mt10">
      <s-table :columns="tableColumns"
               ref="table"
               :data="loadData"
               :loading="searchLoading"
               :rowKey="(record) => record.id"
               bordered
               size="middle">
        <span slot="simpleText" slot-scope="text,record">
<!--          <clamp :text="text" :sourceText="text?text:'-'" :isCenter="true"></clamp>-->
          {{text?text:'-'}}
        </span>

        <span slot="clampText" slot-scope="text,record">
          <clamp :text="text" :sourceText="text?text.split(/[(\r\n)\r\n]+/):['-']" :isCenter="true" :key="new Date()"></clamp>
        </span>

        <span slot="productDepartment" slot-scope="text,record">
          {{text?(productDepartmentList.filter(e=>e.id==text).length>0?productDepartmentList.filter(e=>e.id==text)[0].customvalue:text):'-'}}
        </span>

        <span slot="testCate" slot-scope="text,record">
          {{text?(testCateList.filter(e=>e.code==text).length>0?testCateList.filter(e=>e.code==text)[0].value:text):'-'}}
        </span>

        <span slot="failureCate" slot-scope="text,record">
          {{text?failureCateList.filter(e=>e.code==text)[0].value:'-'}}
        </span>

        <span slot="reviewStatus" slot-scope="text,record">
          {{text?reviewStatusList.filter(e=>e.code==text)[0].value:'-'}}
        </span>
        <span slot="fileCode" slot-scope="text,record">
          <span v-if="record.fileName == null">{{text}}</span>
          <a v-else-if="record.fileName.includes('pdf') || record.fileName.includes('PDF') || record.fileName.includes('png') || record.fileName.includes('jpg') || record.fileName.includes('jpeg')" @click="previewFile(record.fileId)">{{text}}</a>
          <a v-else @click="callFileInfoDownload(record.fileId)">{{text}}</a>
        </span>
        <span slot="fileName" slot-scope="text,record">
          <span v-if="text == null">{{text}}</span>
          <a v-else-if="text.includes('pdf') || text.includes('PDF') || text.includes('png') || text.includes('jpg') || text.includes('jpeg')" @click="previewFile(record.fileId)">{{text}}</a>
          <a v-else @click="callFileInfoDownload(record.fileId)">{{text}}</a>
        </span>

        <span slot="faBreakReportName" slot-scope="text,record">
          <span v-if="text == null">{{text}}</span>
          <a v-else-if="text.includes('pdf') || text.includes('PDF') || text.includes('png') || text.includes('jpg') || text.includes('jpeg')" @click="previewFile(record.faBreakReportId)">{{text}}</a>
          <a v-else @click="callFileInfoDownload(record.faBreakReportId)">{{text}}</a>
        </span>

        <span slot="faAnalysisReportName" slot-scope="text,record">
          <span v-if="text == null">{{text}}</span>
          <a v-else-if="text.includes('pdf') || text.includes('PDF') || text.includes('png') || text.includes('jpg') || text.includes('jpeg')" @click="previewFile(record.faAnalysisReportId)">{{text}}</a>
          <a v-else @click="callFileInfoDownload(record.faAnalysisReportId)">{{text}}</a>
        </span>

        <span slot="jiraProcess" slot-scope="text,record">
          <span v-if="record.faResponsibleAssignIssueKey==null">指定责任人</span>
          <a v-else @click="handleToJira(record.faResponsibleAssignIssueKey)" >指定责任人</a>
          <a-divider type="vertical" />
          <span v-if="record.faAnalyseReportIssueKey==null">分析报告</span>
          <a v-else @click="handleToJira(record.faAnalyseReportIssueKey)" >分析报告</a>
        </span>

      </s-table>
    </div>
    <!-- 表格 end -->

    <a-drawer :bodyStyle="{ height: '100%' }" placement="right" :closable="false" width="80%" :visible="filePreviewVisible" @close="filePreviewOnClose">
      <iframe :src="fileUrl"  width="100%" height="100%"></iframe>
    </a-drawer>

  </div>
</template>
<script>
  import { clamp,STable } from '@/components'
  import {getUserLists} from "@/api/modular/system/userManage";
  import Vue from "vue";
  import {getDpvTestFailureList,getDpvTestFailureListPage} from "@/api/modular/system/testFailure";
  import {getJiraOptionList} from "@/api/modular/system/jiraCustomTool";
  import {sysFileInfoDownload} from '@/api/modular/system/fileManage'
  import pbiTabs from "@/components/pageTool/components/pbiTabs.vue";

  export default {
    components: {
      pbiTabs,
      clamp,
      STable},
    props: {
      listType: {
        type: Number,
        default: 0
      },
      issueId: {
        type: Number,
        default: 0
      },
    },
    data() {
      return {
        loadData: parameter => {
          // this.queryParam.productDepartment = this.productDepartment
          this.queryParam.reviewStatus = 2//FA显示审核过后的数据
          this.queryParam.laboratoryId = this.laboratoryId

          return getDpvTestFailureListPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        isShowAllSearch:false,
        laboratoryList:[{value:'',label:"全部"},{value:"HZ_YJ_DL_AQ",label:"第四实验室"},{value:"JM_YJ_DL_CS",label:"第六实验室(JM)"},{value:"HZ_YJ_DL_CS",label:"第六实验室(HZ)"}],
        laboratoryId:'',
        productDepartment:null,
        tableHeight: document.documentElement.clientHeight - 40 - 20 - 24 - 10 - 32 - 10 - 120,

        filePreviewVisible: false,
        fileUrl:'',
        previewBaseUrl:'/api/sysFileInfo/preview?Authorization=Bearer '+Vue.ls.get('Access-Token')+'&id=',
        addVisible: false,
        addLoading: false,
        addForm: this.$form.createForm(this, { name: 'addForm' }),

        //{code:'动力圆柱电池研究所',value:'动力圆柱电池研究所'},{code:'方形电池研究所',value:'方形电池研究所'},{code:'新型电池研究所',value:'新型电池研究所'},{code:'V型圆柱电池研究所',value:'V型圆柱电池研究所'},{code:'动力电池研究所',value:'动力电池研究所'},{code:'储能电池研究所',value:'储能电池研究所'}
        productDepartmentList:[],
        orderTypeList:[{code:'G圆柱',value:'G圆柱'},{code:'C圆柱',value:'C圆柱'},{code:'方型',value:'方型'},{code:'软包',value:'软包'},{code:'V圆柱',value:'V圆柱'}],
        projectLevelList:[{code:'S',value:'S'},{code:'A',value:'A'},{code:'B',value:'B'},{code:'C',value:'C'}],
        researchStageList:[{code:'A样',value:'A样'},{code:'B样',value:'B样'},{code:'C样',value:'C样'}],
        faStatusList:[{code:"beStart",value:"待启动"},{code:"onGoing",value:"进行中"},{code:"finish",value:"完成"}],
        testCateList:[{code:"电性能测试",value:"电性能测试"},{code:"循环寿命测试",value:"循环寿命测试"},{code:"日历寿命测试",value:"日历寿命测试"},{code:"安全测试",value:"安全测试"},{code:"精密",value:"精密"}],
        failureCateList:[{code:1,value:"不满足指标"},{code:2,value:"起火"},{code:3,value:"漏液"},{code:4,value:"壳体开裂"},{code:5,value:"其它"}],
        reviewStatusList:[{code:1,value:"审核中"},{code:2,value:"审核完成"}],
        reviewResultList:['请选择', '通过', '驳回'],
        loading:false,
        selectUserLoading: false,
        selectUserColumns: [{
          title: '账号',
          dataIndex: 'account'
        }, {
          title: '姓名',
          dataIndex: 'name'
        }],
        //提出人
        presenterVisible: false,
        userQueryParam: {},
        presenterName: '',
        userLoadData: parameter => {
          return getUserLists(Object.assign(parameter, this.userQueryParam)).then((res) => {
            return res.data
          })
        },

        //待办相关
        showTodoPushBtn: this.hasPerm("oaTodo:productProblem"),
        todoPushConfirmLoading: false,
        todoPushVisible: false,
        form: this.$form.createForm(this),
        dropdownvisible: false,
        userNameDisplay: "",


        queryParam: {
          productName: '',
          projectName: '',
          faChargeName: '',
          faStatusList: [],
          testCateList: [],
          failureCateList: [],
          // keyWord: '',
        },
        labelCol: {
          xs: {span: 24},
          sm: {span: 6}
        },
        wrapperCol: {
          xs: {span: 24},
          sm: {span: 18}
        },
        searchLoading: false,
        searchDataTimer: 0,
        problemDimensionList: [],
        problemStatusList: [],
        problemCateList: [],
        projectStageList: [],
        productList: [],
        tableColumns: [
          {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            align: 'center',
            width: 60,
            customRender: (text, record, index) => record.parentId == 1?`${index+1}`:'',
          },
          {
            title: '流程跳转',
            dataIndex: 'action',
            align: 'center',
            width: 150,
            scopedSlots: { customRender: 'jiraProcess' }
          },
          {
            title: 'FA相关信息',
            children: [
              {
                title: 'FA责任人',
                dataIndex: 'faChargeName',
                align: 'center',
                width: 100,
              },
              {
                title: 'FA期限',
                dataIndex: 'faDeadline',
                align: 'center',
                width: 100,
              },
              {
                title: 'FA状态',
                dataIndex: 'faStatus',
                align: 'center',
                width: 100,
                customRender: (text, record, index) => {
                  if(text == 'finish'){
                    return '完成'
                  }
                  if(text == 'onGoing'){
                    return '进行中'
                  }
                  return '待启动'
                },
              },
              {
                title: '拆解报告',
                dataIndex: 'faBreakReportName',
                align: 'center',
                width: 100,
                scopedSlots: { customRender: 'faBreakReportName' }
              },
              {
                title: '分析报告',
                dataIndex: 'faAnalysisReportName',
                align: 'center',
                width: 100,
                scopedSlots: { customRender: 'faAnalysisReportName' }
              },

            ],
          },
          {
            title: '电芯基本信息',
            children: [
              {
                title: '产品所属研究所',
                dataIndex: 'productDepartment',
                align: 'center',
                width: 150,
                scopedSlots: { customRender: 'productDepartment' }
              },
              {
                title: '样品类型',
                dataIndex: 'orderType',
                align: 'center',
                width: 100,
                scopedSlots: { customRender: 'orderType' }
              },
              {
                title: '产品名称',
                dataIndex: 'productName',
                align: 'center',
                width: 100,
                scopedSlots: { customRender: 'simpleText' }
              },
              {
                title: '项目等级',
                dataIndex: 'projectLevel',
                align: 'center',
                width: 100,
                scopedSlots: { customRender: 'projectLevel' }
              },
              {
                title: '研制阶段',
                dataIndex: 'researchStage',
                align: 'center',
                width: 100,
                scopedSlots: { customRender: 'researchStage' }
              },
              {
                title: '测试样品阶段',
                dataIndex: 'testSampleStage',
                align: 'center',
                width: 100,
                // scopedSlots: { customRender: 'testSampleStage' }
              },
              {
                title: '样品数量',
                dataIndex: 'testSampleNum',
                align: 'center',
                width: 100,
                customRender: (text, record, index)  => {
                  if (record.parentId != 1) {//1为父级，显示数量，否则不显示
                    return '';
                  }
                  if (record.children == null|| record.children.length == 0) {
                    return 1;
                  }
                  return record.children.length + 1;
                }
              },
            ],
          },
          {
            title: '电芯测试信息',
            children: [
              {
                title: '委托单号',
                dataIndex: 'folderNo',
                align: 'center',
                width: 100,
                scopedSlots: { customRender: 'simpleText' }
              },
              {
                title: '测试项目名称',
                dataIndex: 'testProjectName',
                align: 'center',
                width: 125,
                scopedSlots: { customRender: 'clampText' }
              },
              {
                title: '电芯编码',
                dataIndex: 'cellCode',
                align: 'center',
                width: 100,
                scopedSlots: { customRender: 'simpleText' }
              },
              {
                title: '电芯批次',
                dataIndex: 'cellBatch',
                align: 'center',
                width: 100,
                scopedSlots: { customRender: 'simpleText' }
              },
              {
                title: '测试类别',
                dataIndex: 'testCate',
                align: 'center',
                width: 100,
                scopedSlots: { customRender: 'testCate' }
              },
              {
                title: '失效类别',
                dataIndex: 'failureCate',
                align: 'center',
                width: 100,
                scopedSlots: { customRender: 'failureCate' }
              },
              {
                title: '测试失效描述',
                dataIndex: 'testFailureDescription',
                align: 'center',
                width: 100,
                scopedSlots: { customRender: 'clampText' }
              },
            ],
          },
          {
            title: 'DPV测试失效告知书',
            children: [
              {
                title: '发起人',
                dataIndex: 'initiatorName',
                align: 'center',
                width: 100,
                scopedSlots: { customRender: 'simpleText' }
              },
              {
                title: '发起时间',
                dataIndex: 'initiationTime',
                align: 'center',
                width: 100,
              },
              {
                title: '文件编号',
                dataIndex: 'fileCode',
                align: 'center',
                width: 100,
                scopedSlots: { customRender: 'fileCode' }
              },
              // {
              //   title: '附件',
              //   dataIndex: 'fileName',
              //   align: 'center',
              //   width: 100,
              //   scopedSlots: { customRender: 'fileName' }
              // },
            ],
          },
        ],
        tableData: [],
        initData: {},
      }
    },
    watch: {
      tableData(newVal, oldVal) {
        let subtrahend = this.tableData.length > 0 ? 45 : 0
        subtrahend = subtrahend + (this.issueId == 0 ? 0 : 32);
        document.documentElement.style.setProperty(`--height`, `${this.tableHeight - subtrahend}px`)
      }
    },
    created() {
      // this.getJiraOptionList();
      // console.log(this.listType);
      setTimeout(() => {
        this.getJiraOptionList();
        // this.getData(this.queryParam);
      }, 200);
    },
    mounted() {
      let subtrahend = this.tableData.length > 0 ? 45 : 0
      subtrahend = subtrahend + (this.issueId == 0 ? 0 : 32);
      document.documentElement.style.setProperty(`--height`, `${this.tableHeight - subtrahend}px`)
    },
    methods: {

      addOpen() {
        this.$refs.addForm.add();
      },
      editOpen(record){
        // this.searchLoading = true;
        this.$refs.editForm.edit(record);
      },
      handleOk() {
        this.getData(this.queryParam);
      },
      filePreviewOnClose(){
        this.filePreviewVisible = false;
      },
      handleToJira(issueKey) {
        console.log(issueKey);
        if (issueKey == null) {
          return;
        }
        let $url = `http://jira.evebattery.com/browse/` + issueKey + `?auth=` + Vue.ls.get("jtoken");
        console.log($url);
        // let $url = `http://jira.evebattery.com/browse/` + record.issueKey;
        window.open($url, "_blank");
      },
      previewFile(fileId) {
        this.fileUrl = this.previewBaseUrl + fileId;
        this.filePreviewVisible = true;
      },
      callFileInfoDownload (fileId) {
        this.loading = true
        sysFileInfoDownload({ id: fileId }).then((res) => {
          this.loading = false
          this.downloadfile(res)
        }).catch((err) => {
          this.loading = false
          this.$message.error('下载错误：获取文件流错误')
        })
      },
      handleChangeSearch() {
        this.isShowAllSearch = !this.isShowAllSearch;
        this.tableHeight = this.isShowAllSearch ? this.templateHeight - 80 : this.templateHeight - 40
      },
      handleTabsChange(value) {
        this.laboratoryId = value
        // this.filterData = ''
        // this.getTodoTaskList()
        this.$refs.table.refresh()
      },
      downloadfile (res) {
        var blob = new Blob([res.data], { type: 'application/octet-stream;charset=UTF-8' })
        var contentDisposition = res.headers['content-disposition']
        var patt = new RegExp('filename=([^;]+\\.[^\\.;]+);*')
        var result = patt.exec(contentDisposition)
        var filename = result[1]
        var downloadElement = document.createElement('a')
        var href = window.URL.createObjectURL(blob) // 创建下载的链接
        var reg = /^["](.*)["]$/g
        downloadElement.style.display = 'none'
        downloadElement.href = href
        downloadElement.download = decodeURI(filename.replace(reg, '$1')) // 下载后文件名
        document.body.appendChild(downloadElement)
        downloadElement.click() // 点击下载
        document.body.removeChild(downloadElement) // 下载完成移除元素
        window.URL.revokeObjectURL(href)
      },

      resetSearch() {
        this.queryParam.fileCode = null
        this.queryParam.productName = null
        this.queryParam.cellCode = null
        this.queryParam.faChargeName = null
        this.queryParam.faStatusList = []
        this.queryParam.testCateList = []
        this.queryParam.failureCateList = []
        this.$refs.table.refresh()
      },

      getData(queryParam) {
        this.searchLoading = true
        getDpvTestFailureList(queryParam).then(res => {
          this.tableData = res.data
        }).finally(res => {
          this.searchLoading = false
        })
      },
      getJiraOptionList(){
        getJiraOptionList({fieldName:'department'}).then(res => {
          var list=['18846','22101','22105','18863','18711','22269'];
          this.productDepartmentList = []
          for (let i = 0; i < list.length; i++) {
            this.productDepartmentList.push(res.data.find(e => list[i] == e.id))
          }

          // this.productDepartmentList = res.data.filter(e => list.includes(e.id));
          console.log(this.productDepartmentList);
          // this.productDepartmentList = res.data;
        })
      },
      filterOption(input, option) {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        );
      },
    }
  }
</script>
<style lang="less" scoped>
  :root {
    --height: 600px;
  }

  /* 标题 */
  .head-title {
    display: flex;
    align-items: center;
  }
  .head-title .line {
    width: 4px;
    height: 22px;
    background: #3293ff;
    border-radius: 20px;
  }
  .head-title .title {
    font-size: 16px;
    font-weight: 600;
  }
  /* 筛选 */
  .filter-wrapper {
    display: flex;
    justify-content: space-between;
  }
  .filter-left {
    display: flex;
    margin-right: auto;
  }
  .filter-right {
    display: flex;
    margin-left: auto;
  }
  /* 表格 */
  .table-wrapper {
    padding: 10px;
    background: #fff;
    border-radius: 10px;
  }

  .red {
    display: block;
    background: #ff3333;
    text-align: center;
    color: #fff;
  }

  .yellow {
    display: block;
    background: #fac858;
    text-align: center;
    color: #fff;
  }

  .green {
    display: block;
    background: #58a55c;
    text-align: center;
    color: #fff;
  }

  .btn_pn {
    display: block;
    min-height: 18px;
    min-width: 70px;
  }

  /deep/ .problemStatusSelect .ant-select-selection {
    background-color: rgba(255, 255, 255, 0);
    border: none;
  }
  .problem-status-show {
    justify-content: center;
    display: flex;
    align-items: center;
  }
  .problem-status-show .circle {
    width: 13px;
    height: 13px;
    border-radius: 50%;
    margin-right: 8px;
  }

  .select-box {
    display: flex;
    align-items: center;
  }
  .select-box .circle {
    width: 13px;
    height: 13px;
    border-radius: 50%;
    margin-right: 8px;
  }

  /* 通用  */

  .status-lamp {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    margin: auto;
    flex-shrink:0;
  }

  .mr10 {
    margin-right: 10px;
  }
  .mt10 {
    margin-top: 10px;
  }
  .mt5 {
    margin-top: 5px;
  }

  .filter-select {
    width: 200px;
  }
  .filter-input {
    width: 175px;
  }

  /* 表格组件 */
  /deep/ .ant-table tr th {
    background: #f4f4f4;
    font-size: 13px;
  }

  /deep/.ant-table-body {
    height: var(--height) !important;
    overflow-y: scroll;
  }

  /deep/.ant-table-thead {
    position: sticky;
    top: 0;
    z-index: 10;
  }
  /deep/ .ant-pagination {
    margin: 10px 0;
  }
  /deep/ .ant-table-placeholder {
    border: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
</style>
