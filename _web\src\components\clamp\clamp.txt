<template>
    <div class="over-ellipsis">
        <div>
            <span ref="overEllipsis">{{ realText }}</span>
            <a-popover placement="bottom" trigger="click" >
                <template slot="content">
                    
                    <div style="width:300px">
                        <div v-for='(item,i) in sourceText' :key="i">{{item}}</div>
                    </div>
                </template>
                <a v-if="show" href="#">查看</a>
            </a-popover>
        </div>
    </div>
</template>

<script>
    export default {
        props: {
            text: {
                type: String,
                default: ''
            },
            sourceText:{
                type: Array,
                default: []
            },
            maxLines: {
                type: Number,
                default: 3,
            },
        },
        data() {
            return {
                show:false,
                offset: this.text.length,
            }
        },
        methods: {
            calculateOffset(from, to) {
                this.$nextTick(() => {
                    if (Math.abs(from - to) <= 1) {
                        this.offset = this.offset - 5
                        return;
                    }
                    if (this.isOverflow()) {
                        to = this.offset;
                    } else {
                        from = this.offset;
                    }
                    this.offset = Math.floor((from + to) / 2);
                    this.calculateOffset(from, to);
                });
            },
            isOverflow() {
                const {
                    len
                } = this.getLines();
                if (len < this.maxLines) {
                    return false;
                }
                if (this.maxLines) {
                    if (len > this.maxLines) {
                        return true;
                    }
                }
                return false;
            },
            getLines() {
                const clientRects = this.$refs.overEllipsis.getClientRects();
                return {
                    len: clientRects.length,
                    lastWidth: clientRects[clientRects.length - 1].width,
                };
            },
        },
        computed: {
            realText() {
                const isCutOut = this.offset !== this.text.length;
                let realText = this.text;
                if (isCutOut) {
                    this.show = true
                    realText = this.text.slice(0, this.offset) + "...";
                }
                return realText;
            },
        },
        mounted() {
            const {len} = this.getLines()
            if (len > this.maxLines) {
                this.showSlotNode = true
                this.$nextTick(() => {
                    this.calculateOffset(0, this.text.length);
                })
            }
        }
    }
</script>
<style >
.over-ellipsis{
    display: flex;
    align-items: center;
    text-align: left;
    padding: 0 1px;
}
</style>