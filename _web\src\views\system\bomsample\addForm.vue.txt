<template>
    <a-modal title="新增" :width="1200" :visible="visible" :confirmLoading="confirmLoading" @ok="handleSubmit" @cancel="handleCancel">
        <a-spin :spinning="confirmLoading">
            <a-form :form="form">
                <a-row :gutter="24">
                    <a-col :md="8" :sm="24">
                        <a-form-item label="产品阶段" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                            <a-select style="width: 100%" v-decorator="['productStage', {rules: [{required: true, message: '请选择阶段!'}]}]" placeholder="请选择阶段">
                                <a-select-option v-for="(item,i) in getDict('product_stage_status')" :key="i" :value="item.code">{{item.name}}</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :md="8" :sm="24">
                        <a-form-item label="客户是否反馈" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                            <a-select style="width: 100%" v-decorator="['isFeedback']" placeholder="请选择">
                                <a-select-option v-for="(item,i) in getDict('is_or_no')" :key="i" :value="item.code">{{item.name}}</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :md="8" :sm="24">
                        <a-form-item label="批次样品是否合格" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                            <a-select style="width: 100%" v-decorator="['isPass']" placeholder="请选择">
                                <a-select-option v-for="(item,i) in getDict('is_or_no')" :key="i" :value="item.code">{{item.name}}</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row :gutter="24">
                    <a-col :md="8" :sm="24">
                        <a-form-item label="送样日期" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                            <a-date-picker placeholder="请选择送样日期" @change="onChangeSampleDate" style="width: 100%" v-decorator="['sampleDate', {rules: [{required: true, message: '请选择送样日期!'}]}]" />
                        </a-form-item>
                    </a-col>
                    <a-col :md="8" :sm="24">
                        <a-form-item label="客户" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                            <a-input v-decorator="['customer', {rules: [{required: true, message: '请选输入客户！'}]}]" />
                        </a-form-item>
                    </a-col>
                    <a-col :md="8" :sm="24">
                        <a-form-item label="DQE" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                            <a-input v-decorator="['dqe', {rules: [{required: true, message: '请选输入DQE！'}]}]" />
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row :gutter="24">
                    <a-col :md="8" :sm="24">
                       <a-form-item label="送样数量" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                            <a-input-number style="width:100%" :min="1" v-decorator="['sampleCount', {rules: [{required: true, message: '请选输入送样数量！'}]}]" />
                        </a-form-item>
                    </a-col>
                    <a-col :md="8" :sm="24">
                        <a-form-item label="客户反馈" :labelCol="labelCol" :wrapperCol="wrapperCol">
                            <a-textarea :rows="2" placeholder="请输入客户反馈" v-decorator="['feedback']"></a-textarea>
                        </a-form-item>
                    </a-col>
                    <a-col :md="8" :sm="24">
                        <a-form-item label="EVE回复" :labelCol="labelCol" :wrapperCol="wrapperCol">
                            <a-textarea :rows="2" placeholder="请输入EVE回复" v-decorator="['eveFeedback']"></a-textarea>
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row :gutter="24">
                    <a-col :md="8" :sm="24">
                        <a-form-item label="送样批次" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                            <a-input-number style="width:100%" :min="0" v-decorator="['slap', {rules: [{required: true, message: '请选输入送样批次！'}]}]" />
                        </a-form-item>
                    </a-col>
                </a-row>
            </a-form>
        </a-spin>
    </a-modal>
</template>

<script>
    import Vue from 'vue'
    import {
        DICT_TYPE_TREE_DATA
    } from '@/store/mutation-types'
    import moment from 'moment'
    
    import {
        sysSampleAdd
    } from '@/api/modular/system/bomSampleManage'
    
    export default {
        props: {
            issueId: {
                type: Number,
                default: 0
            },
        },
        data() {
            return {
                sampleDate: '',
                loading: false,
                labelCol: {
                    xs: {
                        span: 24
                    },
                    sm: {
                        span: 8
                    }
                },
                wrapperCol: {
                    xs: {
                        span: 24
                    },
                    sm: {
                        span: 16
                    }
                },
                visible: false,
                confirmLoading: false,
                form: this.$form.createForm(this),
            }
        },
        methods: {
            moment,
            getDict(code) {
                const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
                return dictTypeTree.filter(item => item.code == code)[0].children
            },
            // 初始化方法
            add() {
                this.sampleDate = ''
                this.visible = true
            },
            handleSubmit() {
                const {
                    form: {
                        validateFields
                    }
                } = this
                this.confirmLoading = true
                validateFields((errors, values) => {
                    if (!errors) {
                        if (this.sampleDate != '') {
                            values.sampleDate = this.sampleDate
                        }
                        let $params = { ...values,
                            issueId:this.issueId,
                        }
                        sysSampleAdd($params).then((res) => {
                            if (res.success) {
                                this.$message.success('新增成功')
                                this.visible = false
                                this.confirmLoading = false
                                this.$emit('ok')
                                this.handleCancel()
                                
                            } else {
                                this.$message.error('新增失败：' + res.message)
                            }
                        }).finally((res) => {
                            this.confirmLoading = false
                        })
                    } else {
                        this.confirmLoading = false
                    }
                })
            },
            handleCancel() {
                this.sampleDate = ''
                this.form.resetFields()
                this.visible = false
                this.form.getFieldDecorator('sampleDate', {
                    initialValue: null
                })
            },
            onChangeSampleDate(date, dateString) {
                if (date == null) {
                    this.sampleDate = ''
                } else {
                    this.sampleDate = moment(date).format('YYYY-MM-DD')
                }
            },
        }
    }
</script>
