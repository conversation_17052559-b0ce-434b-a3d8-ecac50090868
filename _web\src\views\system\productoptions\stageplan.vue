<template>
	<div class="product_width">
		<a-spin :spinning="loading">
			<!-- 筛选区域 start -->
			<div class="table-page-search-wrapper">
				<a-form layout="inline">
					<a-row :gutter="48">
						<a-col :md="6" :sm="24">
							<a-form-item label="立项日期">
								<a-range-picker
									class="filter-form"
									:placeholder="['开始日期', '结束日期']"
									size="small"
									@change="dateChange"
								/>
							</a-form-item>
						</a-col>
						<a-col :md="5" :sm="24">
							<a-form-item label="产品类别">
								<div class="filter-box">
									<treeselect
										:limit="1"
										class="filter-form"
										@input="change"
										:max-height="200"
										placeholder="请选择产品类别"
										value-consists-of="BRANCH_PRIORITY"
										v-model="queryparam.cates"
										:multiple="true"
										:options="cate"
									/>
								</div>
							</a-form-item>
						</a-col>
						<a-col :md="5" :sm="24">
							<a-form-item label="产品状态">
								<treeselect
									:limit="1"
									class="filter-form"
									@input="change"
									:max-height="200"
									placeholder="请选择产品状态"
									value-consists-of="BRANCH_PRIORITY"
									v-model="queryparam.states"
									:multiple="true"
									:options="statuses"
								/>
							</a-form-item>
						</a-col>

						<a-col :md="5" :sm="24">
							<a-form-item label="产品部门">
								<treeselect
									class="filter-form"
									:limit="1"
									@input="change"
									:max-height="200"
									placeholder="请选择所属部门"
									:multiple="true"
									:options="departmentCateTreeData"
									value-consists-of="BRANCH_PRIORITY"
									v-model="queryparam.depts"
								>
								</treeselect>
							</a-form-item>
						</a-col>

						<a-col :md="3" :sm="24">
							<a-form-item label="">
								<a-input
									class="filter-form"
									size="small"
									@keyup.enter.native="change"
									v-model="queryparam.keyword"
									placeholder="请输入产品名称"
								>
									<a-icon slot="suffix" type="search" style="color: rgba(0,0,0,.45)" />
								</a-input>
							</a-form-item>
						</a-col>

						<a-col :md="1" :sm="24" :style="{ float: 'right' }">
							<div class="table-page-search-submitButtons" :style="{ float: 'right' }">
								<a-button size="small" style="margin-left: 120px;" type="primary" @click="query">查询</a-button>
							</div>
						</a-col>
					</a-row>
				</a-form>
			</div>
			<!-- 筛选区域 start -->

			<div>
				<a-table
					ref="table"
					:style="`height:${tableHeight}px;`"
					:rowKey="record => record.issueId + record.productCate"
					:columns="columns"
					:dataSource="loadData"
					:expandIconColumnIndex="13"
					:expandIconAsCell="false"
					:expandIcon="expandIcon"
				>
					<span slot="productCate" slot-scope="text, record">
						{{ record.productOrProject == 1 ? record.productCateParent + (text != "" ? "->" + text : "") : "" }}
					</span>
					<span slot="productProjectName" slot-scope="text, record">
						{{ record.productOrProject == 1 ? text : "" }}
					</span>

					<span slot="mstatus" slot-scope="text">{{ "product_stage_status" | dictType(text) }}</span>

					<span slot="action" slot-scope="text, record">
						<a v-show="!record.showPlan" @click="showPlan(record)">查看计划</a>
						<a v-show="record.showPlan" @click="showPlan(record)">关闭查看</a>
					</span>
				</a-table>
			</div>
		</a-spin>
	</div>
</template>

<script>
import { dashboardInfo } from "@/api/modular/system/dashboardManage"
import { getCatesTree } from "@/api/modular/system/report"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"
import { getCateTree } from "@/api/modular/system/topic"
export default {
	components: {
		Treeselect
	},
	data() {
		return {
			departmentCateTreeData:[],
			typeOptions: [
				{
					id: 1,
					label: "预研产品"
				},
				{
					id: 2,
					label: "A|B新产品"
				},
				{
					id: 3,
					label: "试产新产品"
				},
				{
					id: 4,
					label: "量产品"
				},
				{
					id: 5,
					label: "其他"
				},
				{
					id: 6,
					label: "停止"
				}
			],
			statuses: [
				{
					id: 0,
					label: "立项讨论"
				},
				{
					id: 1,
					label: "A/B样"
				},
				{
					id: 2,
					label: "C/D样"
				},
				{
					id: 3,
					label: "暂停开发"
				},
				{
					id: 4,
					label: "停产"
				},
				{
					id: 5,
					label: "SOP"
				}
			],
			queryparam: {
				productCates: [], //产品分类
				cates: [],
				states: [],
				depts:[],
				keyword: null,
				projectId: null
			},
			loading: true,
			columns: [
				{
					title: "序号",
					width: 60,
					dataIndex: "no",
					align: "center",
					customRender: (text, record, index) => {
						if (record.productOrProject == 1) {
							return `${index + 1}`
						}
						return ""
					}
				},
				{
					title: "产品名称",
					align: "center",
					dataIndex: "productProjectName",
					scopedSlots: {
						customRender: "productProjectName"
					}
				},
				{
					title: "项目名称",
					align: "center",
					dataIndex: "projectName"
				},
				{
					title: "客户",
					align: "center",
					dataIndex: "customer"
				},
				{
					title: "项目启动",
					align: "center",
					children: [
						{
							title: "K0立项评审",
							align: "center",
							dataIndex: "k0",
							customRender: (text, record, index) => {
								return this.customRenderSpan(record, 0 + 1)
							}
						}
					]
				},
				{
					title: "项目规划",
					align: "center",
					children: [
						{
							title: "M1项目规划",
							align: "center",
							dataIndex: "m1",
							customRender: (text, record, index) => {
								return this.customRenderSpan(record, 1 + 1)
							}
						}
					]
				},
				{
					title: "A样",
					align: "center",
					children: [
						{
							title: "M2 A样数据冻结",
							align: "center",
							dataIndex: "m2a",
							customRender: (text, record, index) => {
								return this.customRenderSpan(record, 2 + 1)
							}
						},
						{
							title: "M2 转阶段",
							align: "center",
							dataIndex: "m2z",
							customRender: (text, record, index) => {
								return this.customRenderSpan(record, 3 + 1)
							}
						}
					]
				},
				{
					title: "B样",
					align: "center",
					children: [
						{
							title: "M3 B样数据冻结",
							align: "center",
							dataIndex: "m3b",
							customRender: (text, record, index) => {
								return this.customRenderSpan(record, 4 + 1)
							}
						},
						{
							title: "M3 转阶段",
							align: "center",
							dataIndex: "m3z",
							customRender: (text, record, index) => {
								return this.customRenderSpan(record, 5 + 1)
							}
						}
					]
				},
				{
					title: "C样",
					align: "center",
					children: [
						{
							title: "M4 C样数据冻结",
							align: "center",
							dataIndex: "m4c",
							customRender: (text, record, index) => {
								return this.customRenderSpan(record, 6 + 1)
							}
						}
					]
				},
				{
					title: "D样",
					align: "center",
					children: [
						{
							title: "M5 PPAP",
							align: "center",
							dataIndex: "m5",
							customRender: (text, record, index) => {
								return this.customRenderSpan(record, 7 + 1)
							}
						}
					]
				},
				{
					title: "SOP",
					align: "center",
					children: [
						{
							title: "M6 SOP",
							align: "center",
							dataIndex: "m6",
							customRender: (text, record, index) => {
								return this.customRenderSpan(record, 8 + 1)
							}
						}
					]
				},
				{
					title: "操作",
					align: "center",
					dataIndex: "action",
					scopedSlots: { customRender: "action" }
				}
			],
			loadData: [],
			totalData: [],
			cate: [],
			parentId: null,
			cateId: null,
			projectId: null
		}
	},
	props: {
		// 图表高度
		tableHeight: {
			type: Number,
			default: 0
		},
		// 表格滚动高度
		scrollHeigh: {
			type: Number,
			default: 0
		}
	},
	watch: {
		loadData(newVal, oldVal) {
			if (this.loadData.length > 0) {
				// 动态修改--height的值
				document.documentElement.style.setProperty(`--height`, `${this.scrollHeigh}px`)
			} else {
				// 动态修改--height的值
				document.documentElement.style.setProperty(`--height`, "50px")
			}
		}
	},
	methods: {
		callGetDepartmentCateTree() {
			this.confirmLoading = true
			getCateTree({
				fieldName: "department"
			})
				.then(res => {
					if (res.success) {
						let cate = []
						for (const item of res.data) {
							let $item = {
								id: parseInt(item.value),
								label: item.title
							}
							
							cate.push($item)
						}
						this.departmentCateTreeData = cate
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
					this.confirmLoading = false
				})
				.catch(err => {
					this.confirmLoading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		},
		query() {
			if (this.parentId != null) {
				let index = this.queryparam["cates"].findIndex(e => e == this.parentId)
				this.queryparam["cates"].splice(index, 1)
				this.parentId = null
			}
			if (this.cateId != null) {
				let index = this.queryparam["cates"].findIndex(e => e == this.cateId)
				this.queryparam["cates"].splice(index, 1)
				this.cateId = null
			}

			if (this.projectId != null) {
				this.queryparam.projectId = null
				this.projectId = null
			}
			this.callFilter()
		},
		showPlan(record) {
			record.showPlan = !record.showPlan
		},
		customRenderSpan(record, num) {
			const overDays = record.productStageItems[num - 1] ? Number(record.productStageItems[num - 1].overDays) : 0
			let reviewDate = "--"
			let actualCompletionDate = "--"
			record.productStageItems.forEach(v => {
				if (v.stage == num) {
					reviewDate = v.reviewDate
					actualCompletionDate = v.actualCompletionDate ? v.actualCompletionDate : "--"
				}
			})

			// 当前阶段
			if (num == record.mstatus) {
				if (record.state == 3) {
					return (
						<span>
							<span class="line radius_first radius_last deep-gray">
								<span class="line radius_first radius_last deep-gray">
									{record.productStageItems[num - 1] && record.productStageItems[num - 1].stage == num
										? record.productStageItems[num - 1].reviewDate
										: "--"}
								</span>
							</span>
						</span>
					)
				}

				if (num == 1) {
					return (
						<span>
							{/* class="line  white2gray radius_first" class="line radius_first radius_last" */}
							<span
								class={
									overDays >= 14
										? "line radius_first red"
										: overDays >= 7
										? "line radius_first yellow"
										: "line radius_first"
								}
							>
								<span
									class={
										overDays >= 14
											? "line radius_first radius_last red"
											: overDays >= 7
											? "line radius_first radius_last yellow"
											: "line radius_first radius_last"
									}
								>
									{record.productStageItems[num - 1] && record.productStageItems[num - 1].stage == num
										? record.productStageItems[num - 1].reviewDate
										: "--"}
								</span>
							</span>
						</span>
					)
				}

				return (
					<span>
						{/* class="line green2gray" class="line radius_first radius_last"  */}
						<span class={overDays >= 14 ? " red" : overDays >= 7 ? " yellow" : "line green2gray"}>
							<span
								class={
									actualCompletionDate == "--"
										? overDays >= 14
											? "line radius_first radius_last red"
											: overDays >= 7
											? "line radius_first radius_last yellow"
											: "line radius_first radius_last"
										: "line radius_first radius_last green"
								}
							>
								{actualCompletionDate == "--" ? reviewDate : ""}
							</span>
						</span>
					</span>
				)
			}

			// 已结束阶段
			if (num < record.mstatus) {
				if (num == 1) {
					return (
						// class="line green radius_first"
						<span class="line green radius_first">{record.showPlan ? reviewDate : ""}</span>
					)
				}
				if (num == 9) {
					return (
						// class="line green radius_last"
						<span class="line green radius_last">{record.showPlan ? reviewDate : ""}</span>
					)
				}
				return (
					// class="line green"
					<span class="line green">{record.showPlan ? reviewDate : ""}</span>
				)
			}

			// 未开始阶段
			if (num > record.mstatus) {
				if (num == 1) {
					return (
						// class="line gray radius_first"
						<span class="line gray radius_first">{record.showPlan ? reviewDate : ""}</span>
					)
				}
				if (num == 9) {
					return (
						// class="line gray radius_last"
						<span class="line gray radius_last">{record.showPlan ? reviewDate : ""}</span>
					)
				}
				return (
					// class="line gray"
					<span class="line gray">{record.showPlan ? reviewDate : ""}</span>
				)
			}
		},
		change() {
			this.callFilter()
		},
		resetquery() {
			this.queryparam = {
				productCates: [],
				cates: [],
				states: [],
				keyword: null,
				projectId: null
			}
			let filterData = JSON.parse(JSON.stringify(this.totalData))
			this.loadData = filterData
		},
		callGetTree() {
			this.loading = true
			getCatesTree()
				.then(res => {
					if (res.result) {
						let cate = []
						for (const item of res.data) {
							let $item = {
								id: parseInt(item.value),
								label: item.title
							}
							if (item.children) {
								$item.children = []
								for (const _item of item.children) {
									$item.children.push({
										id: parseInt(_item.value),
										label: _item.title
									})
								}
							}
							cate.push($item)
						}
						this.cate = cate
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
					this.loading = false
				})
				.catch(err => {
					this.loading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		},

		//数据筛选
		callFilter() {
			let filterData = JSON.parse(JSON.stringify(this.totalData))

			// 产品分类
			if (this.queryparam["productCates"].length > 0) {
				filterData = filterData.filter(
					item => this.queryparam["productCates"].join(",").indexOf(parseInt(item.productClassification)) > -1
				)
			}

			if (this.queryparam.projectId) {
				if (this.queryparam["cates"].length > 0) {
					filterData = filterData.filter(
						item =>
							this.queryparam["cates"].indexOf(parseInt(item.cateId)) > -1 ||
							this.queryparam["cates"].indexOf(parseInt(item.catepid)) > -1
					)
				}
				filterData = filterData.filter(item => item.issueId == this.queryparam.projectId)
				this.loadData = filterData

				return
			}

			if (this.queryparam["cates"].length > 0) {
				filterData = filterData.filter(
					item =>
						this.queryparam["cates"].indexOf(parseInt(item.cateId)) > -1 ||
						this.queryparam["cates"].indexOf(parseInt(item.catepid)) > -1
				)
			}
			if (this.queryparam["states"].length > 0) {
				filterData = filterData.filter(item => this.queryparam["states"].indexOf(parseInt(item.state)) > -1)
			}
			if (this.queryparam["depts"].length > 0) {
				filterData = filterData.filter(item => this.queryparam["depts"].indexOf(parseInt(item.deptId)) > -1)
			}
			if (this.queryparam.keyword != null && this.queryparam.keyword != "") {
				filterData = filterData.filter(
					item => item.productProjectName.toLowerCase().indexOf(this.queryparam.keyword.toLowerCase()) > -1
				)
			}

			if (this.queryparam.startDate != null) {
				filterData = filterData.filter(
					item =>
						Date.parse(item.initiationDate) >= this.queryparam.startDate &&
						Date.parse(item.initiationDate) < this.queryparam.endDate
				)
			}

			this.loadData = filterData
		},
		dateChange(date, dateString) {
			if (dateString[0] != null && dateString[0] != "") {
				this.queryparam.startDate = Date.parse(dateString[0])
			} else {
				this.queryparam.startDate = null
			}
			if (dateString[1] != null && dateString[1] != "") {
				this.queryparam.endDate = Date.parse(dateString[1])
			} else {
				this.queryparam.endDate = null
			}
			this.callFilter()
		},
		expandIcon(props) {
			if (props.record.children && props.record.children.length > 0) {
				if (props.expanded) {
					return (
						<a-icon
							class="icon-style"
							type="up"
							onClick={e => {
								props.onExpand(props.record, e)
							}}
						/>
					)
				} else {
					return (
						<a-icon
							class="icon-style"
							type="down"
							onClick={e => {
								props.onExpand(props.record, e)
							}}
						/>
					)
				}
			} else {
				return <span />
			}
		},
		handleOk() {
			this.callDashboardInfo()
		},
		callDashboardInfo() {
			this.loading = true
			dashboardInfo({})
				.then(res => {
					if (res.success) {
						this.totalData = JSON.parse(JSON.stringify(res.data))
						this.loadData = res.data
						if (this.$route.query.parentId) {
							this.projectId = parseInt(this.$route.query.parentId)
							this.queryparam["cates"].push(parseInt(this.$route.query.parentId))
						}
						if (this.$route.query.cateId) {
							this.cateId = parseInt(this.$route.query.cateId)
							this.queryparam["cates"].push(parseInt(this.$route.query.cateId))
						}
						if (this.$route.query.projectId) {
							this.projectId = parseInt(this.$route.query.projectId)
							this.queryparam.projectId = parseInt(this.$route.query.projectId)
						}
						this.callFilter()
					} else {
						this.$message.error(res.message, 1)
					}
					this.loading = false
				})
				.catch(err => {
					this.loading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		}
	},
	created() {
		this.callDashboardInfo()
		this.callGetTree()
		this.callGetDepartmentCateTree()
		// 动态修改--height的值
		document.documentElement.style.setProperty(`--height`, `${this.scrollHeigh}px`)
	}
}
</script>

<style lang="less" scoped="">
@import "./productoption.less";
:root {
	--height: 600px;
}

/deep/.ant-table-body {
	height: var(--height) !important;
	overflow-y: scroll;
}

/deep/.ant-table-thead > tr > th {
	border-top: 1px solid #dfdbdb;
}
.line {
	display: flex;
	width: 100%;
	height: 13px;
	background: #fff;
	align-items: center;
	justify-content: center;
}
.gray {
	background: #e8e8e8;
}

.deep-gray {
	background: #9f9696;
	color: #fff;
}
.green {
	background: #c4ebad;
}
.yellow {
	background: yellow;
}
.red {
	background: #ff3333;
	color: #fff;
}
.radius_first {
	border-top-left-radius: 8px;
	border-bottom-left-radius: 8px;
}
.radius_last {
	border-top-right-radius: 8px;
	border-bottom-right-radius: 8px;
}
.white2gray {
	background: linear-gradient(to right, #fff 0%, #fff 50%, #e8e8e8 50.01%, #e8e8e8 100%);
}
.green2gray {
	background: linear-gradient(to right, #c4ebad 0%, #c4ebad 50%, #e8e8e8 50.01%, #e8e8e8 100%);
}

// 表头居中
/deep/.ant-table-thead tr th {
	border: 1px solid #dfdbdb;
	padding: 5px;
}
/deep/.ant-table-thead tr th:first-child {
	border-left: none;
}
/deep/.ant-table-thead tr th:last-child {
	border-right: none;
}

/deep/.ant-table-tbody tr td {
	padding: 16px 0 !important;
}
.icon-style {
	margin-right: 8px;
}
</style>
