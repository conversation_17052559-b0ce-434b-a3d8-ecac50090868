
<template>
  <a-modal
    title="评审小结报告"
    :width="500"
  >
    <div style="padding-top: 10px;">


        <div class="content">
          <div class="box1" id="chart1" ref="chart1" style="padding-top: 32px;" ></div>`
        </div>

    </div>

  </a-modal>


</template>

<script>

  export default {
    data() {
      return {
        orgTree: [],
        orgId:null,
        myChart1:null,
        myChart2:null,
        myChart3:null,
        showSpin:true,
        chosetheme:'walden',
        openKey:["1544493062255370241"],
        endMonth:null,
        beginMonth:null,
        beginMonth1:null,
        param:{}
      }
    },
    mounted() {


    },
    watch:{

    },
    methods: {



      getEchartData() {
        this.myChart1 = this.echarts.init(this.$refs.chart1,'walden')
        const option = {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            top: '5%',
            left: 'center'
          },
          series: [
            {
              name: 'Access From',
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: true,
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 40,
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                { value: 1048, name: 'Search Engine' },
                { value: 735, name: 'Direct' },
                { value: 580, name: 'Email' },
                { value: 484, name: 'Union Ads' },
                { value: 300, name: 'Video Ads' }
              ]
            }
          ]
        }



        this.myChart1.setOption(option)





      }

    }

  }
</script>
<style scoped>
.content {
padding-top: 0;
  padding-bottom: 0;
}

.box1 {
width: 100%;
height:450px;
float: left;
}

.box2 {
width: 50%;
height: 300px;
float: left;
}

.box3 {
width: 100%;
height: 400px;
float: left;
}
.box4 {
width: 34%;
height: 360px;
float: left;
}

.box5 {
width: 33%;
height: 360px;
float: left;
}

.box6 {
width: 33%;
height: 360px;
float: left;
}

::v-deep .ant-tabs {

   padding-right: 0px!important;
}


.theme-plan-group {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: auto;
  height: 30px;
  overflow: hidden;
  border: 1px solid #eee;
  padding: 5px;
  border-radius: 4px;
}

.theme-plan-color {
  width: 20px;
  height: 20px;
  margin-bottom: 10px;
  margin-left: 2px;
  margin-right: 2px;
  display: inline-block;
  border-radius: 3px;
}
</style>



