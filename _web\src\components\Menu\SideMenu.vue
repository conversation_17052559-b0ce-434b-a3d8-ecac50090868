<template>
  <a-layout-sider
    :class="['sider', isDesktop() ? null : 'shadow', theme, fixSiderbar ? 'ant-fixed-sidemenu' : null ]"
    width="200px"
    :collapsible="collapsible"
    v-model="collapsed"
    :trigger="null" @mouseenter="mouseenter" @mouseleave="mouseleave">
    <!-- <logo /> -->
    <s-menu
      :inlineIndent="12"
      :collapsed="collapsed"
      :menu="menus"
      :theme="theme"
      :mode="mode"
      @select="onSelect"
       
    ></s-menu>
    <!-- <div class="collapsed_bar" :style="collapsed ? width40 : width187" @click="toggle">
      <a-icon class="collapsed_btn" :type="collapsed ? 'menu-unfold' : 'menu-fold'" />
    </div> -->
  </a-layout-sider>

</template>

<script>
/* import Logo from '@/components/tools/Logo' */
import SMenu from './index'
import { mixin, mixinDevice } from '@/utils/mixin'

export default {
  name: 'SideMenu',
  components: {/*  Logo, */ SMenu },
  mixins: [mixin, mixinDevice],
  props: {
    mode: {
      type: String,
      required: false,
      default: 'vertical'
    },
    theme: {
      type: String,
      required: false,
      default: 'light'
    },
    collapsible: {
      type: Boolean,
      required: false,
      default: false
    },
    collapsed: {
      type: Boolean,
      required: false,
      default: false
    },
    menus: {
      type: Array,
      required: true
    }
  },
  data(){
    return{
      width187:{
        width:'187px',
        textAlign: 'right'
      },
      width40:{
        width:'40px',
        textAlign: 'center'
      }
    }
  },
  methods: {
    onSelect (obj) {
      this.$emit('menuSelect', obj)
    },
    mouseenter () {
      this.$emit('mouseenter')
    },
    mouseleave(){
      this.$emit('mouseleave')

    }
  }
}
</script>
<style lang="less" scoped=''>
/deep/.ant-menu-inline-collapsed{
  width: 40px !important;
}
/deep/.ant-layout-sider-collapsed{
  flex: 0 0 40px !important;
  max-width: 40px !important;
  min-width: 40px !important;
  width: 40px !important;
}
/deep/.ant-menu-inline-collapsed > .ant-menu-item, 
/deep/.ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item, .ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-submenu > .ant-menu-submenu-title, .ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title{
  padding: 0 12px !important;
}
.collapsed_bar{
  position: fixed;
  width: 20px;
  bottom: 0;
  /* top: 0; */
  cursor: pointer;
  
}

/deep/ .ant-menu-vertical .ant-menu-submenu{
  pointer-events: none;
}

/* .collapsed_btn{
  position: absolute;
  top: 45%;
} */
</style>
<style>
.ant-layout-sider-collapsed{
  flex: 0 0 40px !important;
  max-width: 40px !important;
  min-width: 40px !important;
  width: 40px !important;
}
.ant-menu-inline-collapsed > .ant-menu-item, 
.ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item, .ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-submenu > .ant-menu-submenu-title, .ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title{
  padding: 0 12px !important;
}
.ant-menu-vertical .ant-menu-item, 
.ant-menu-vertical-left .ant-menu-item, 
.ant-menu-vertical-right .ant-menu-item, 
.ant-menu-inline .ant-menu-item, 
.ant-menu-vertical .ant-menu-submenu-title, 
.ant-menu-vertical-left .ant-menu-submenu-title,
.ant-menu-vertical-right .ant-menu-submenu-title, 
.ant-menu-inline .ant-menu-submenu-title{
  font-size: 13px;
}
.sider .ant-layout-sider-children:hover,.sider .ant-layout-sider-children{
  overflow-y: scroll !important;
}
.ant-menu-inline-collapsed > .ant-menu-item .anticon, .ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item .anticon, .ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-submenu > .ant-menu-submenu-title .anticon, .ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title .anticon{
  font-size:14px;
  line-height: 30px;
}
.ant-menu-vertical > .ant-menu-item, .ant-menu-vertical-left > .ant-menu-item, .ant-menu-vertical-right > .ant-menu-item, .ant-menu-inline > .ant-menu-item, .ant-menu-vertical > .ant-menu-submenu > .ant-menu-submenu-title, .ant-menu-vertical-left > .ant-menu-submenu > .ant-menu-submenu-title, .ant-menu-vertical-right > .ant-menu-submenu > .ant-menu-submenu-title, .ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title{
  height:30px;
  line-height: 30px
}
.sider .ant-layout-sider-children{
  height: 100vh !important;
}
.ant-menu-inline > .ant-menu-item,
.ant-menu-sub.ant-menu-inline > .ant-menu-item,
.ant-menu-vertical > .ant-menu-submenu > .ant-menu-submenu-title,
.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title{
  height: 30px;
  line-height: 30px;
}
.ant-menu-inline .ant-menu-item{
  margin-bottom: 2px;
}
 
.sider ::-webkit-scrollbar{
	  width: 0px !important;
}


</style>