/**
 * 系统应用
 *
 * <AUTHOR>
 * @date 2020年4月23日12:10:57
 */
import { axios } from '@/utils/request'

/**
 * 系统应用列表
 *
 * <AUTHOR>
 * @date 2020年7月9日15:05:01
 */
export function getList (parameter) {
  return axios({
    url: '/weekprocess/list',
    method: 'get',
    params: parameter
  })
}


/**
 * 新增系统应用
 *
 * <AUTHOR>
 * @date 2020年7月9日15:05:01
 */
export function weekProcessAdd (parameter) {
  return axios({
    url: '/weekprocess/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑系统应用
 *
 * <AUTHOR>
 * @param parameter
 * @returns {*}
 */
export function weekProcessEdit (parameter) {
  return axios({
    url: '/weekprocess/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除系统应用
 *
 * <AUTHOR>
 * @date 2020年7月9日15:05:01
 */
export function weekProcessDelete (parameter) {
  return axios({
    url: '/weekprocess/delete',
    method: 'post',
    data: parameter
  })
}

