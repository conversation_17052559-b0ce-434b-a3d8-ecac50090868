<template>
  <div class="prediction-wrapper">
    <!-- 页面标题 -->
    <a-page-header
      title="公式预测"
      sub-title="选择模型进行数据预测"
      :back-icon="false"
    />

    <div class="prediction-content">
      <a-row :gutter="16">
        <a-col :span="24">
          <a-card title="公式预测" :bordered="false">
            <model-selection-panel
              v-if="!selectedModel"
              @model-selected="handleModelSelected"
            />
            <prediction-panel
              v-else
              :selected-model="selectedModel"
              @reset-selection="resetModelSelection"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 只使用拟合结果弹窗组件，确保与首页一致 -->
    <fitting-result-modal />
  </div>
</template>

<script>
import ModelSelectionPanel from './ModelSelectionPanel.vue';
import PredictionPanel from './PredictionPanel.vue';
import FittingResultModal from '@/components/fitting/FittingResultModal.vue';
import predictionMixin from '@/mixins/predictionMixin';

export default {
  name: 'PredictionWrapper',
  components: {
    ModelSelectionPanel,
    PredictionPanel,
    FittingResultModal
  },
  mixins: [predictionMixin],
  data: () => ({
    selectedModel: null
  }),
  methods: {
    handleModelSelected(model) {
      this.selectedModel = model;
    },
    resetModelSelection() {
      this.selectedModel = null;
    }
  }
};
</script>

<style scoped>
.prediction-wrapper {
  width: 100%;
  //max-width: 1600px;
  margin: 0 auto;
}

.prediction-content {
  margin-top: 10px;
}

:deep(.ant-card) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

:deep(.ant-card-head) {
  padding: 0 16px;
  min-height: 48px;
}

:deep(.ant-card-head-title) {
  padding: 12px 0;
  font-size: 16px;
  font-weight: 600;
}

:deep(.ant-page-header) {
  padding: 10px 0;
  margin-bottom: 10px;
}

:deep(.ant-page-header-heading-title) {
  font-size: 20px;
  font-weight: 700;
}

:deep(.ant-page-header-heading-sub-title) {
  font-size: 14px;
  color: #666;
}
</style>