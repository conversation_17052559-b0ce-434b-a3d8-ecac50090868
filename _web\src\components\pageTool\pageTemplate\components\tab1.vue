<template>
	<div class="tab-wrapper" :style="`height: ${tabHeight}px`">
		<!-- 筛选 start -->
		<div class="filter-wrapper">
			<div class="filter-left">
				<div class="filter-block">
					<a-select class="filter-select" placeholder="请选择工序">
						<a-select-option v-for="item in processOption" value="item.value">
							{{ item.label }}
						</a-select-option>
					</a-select>
				</div>
			</div>

			<div class="filter-right">
				<a-button class="mr10">新增</a-button>
				<a-button type="primary">
					导出
				</a-button>
			</div>
		</div>
		<!-- 筛选 end -->
		<!-- 表格 start -->
		<div class="table-wrapper mt10">
			<a-table :columns="tableColumns" :data-source="tableData" :rowKey="(record, index) => index" size="middle">
				<a slot="name" slot-scope="text">{{ text }}</a>
				<span slot="customTitle"><a-icon type="smile-o" /> Name</span>
				<span slot="tags" slot-scope="tags">
					<a-tag
						v-for="tag in tags"
						:key="tag"
						:color="tag === 'loser' ? 'volcano' : tag.length > 5 ? 'geekblue' : 'green'"
					>
						{{ tag.toUpperCase() }}
					</a-tag>
				</span>
				<span slot="action" slot-scope="text, record">
					<a>Invite 一 {{ record.name }}</a>
					<a-divider type="vertical" />
					<a>Delete</a>
					<a-divider type="vertical" />
					<a class="ant-dropdown-link"> More actions <a-icon type="down" /> </a>
				</span>
			</a-table>
		</div>
		<!-- 表格 end -->
	</div>
</template>
<script>
export default {
	data() {
		return {
			processOption: [
				{
					label: "正极搅拌",
					value: 0
				},
				{
					label: "负极搅拌",
					value: 1
				}
			],
			tableColumns: [
				{
					dataIndex: "name",
					key: "name",
					slots: { title: "customTitle" },
					scopedSlots: { customRender: "name" }
				},
				{
					title: "Age",
					dataIndex: "age",
					key: "age"
				},
				{
					title: "Address",
					dataIndex: "address",
					key: "address"
				},
				{
					title: "Tags",
					key: "tags",
					dataIndex: "tags",
					scopedSlots: { customRender: "tags" }
				},
				{
					title: "Action",
					key: "action",
					scopedSlots: { customRender: "action" }
				}
			],
			tableData: Array(20).fill({
				key: "1",
				name: "John Brown",
				age: 32,
				address: "New York No. 1 Lake Park",
				tags: ["nice", "developer"]
			})
		}
	},
	props: {
		tabHeight: {
			type: Number,
			default: 300
		},
		tableHeight: {
			type: Number,
			default: 300
		}
	},
	watch: {
		tableData(newVal, oldVal) {
			const subtrahend = this.tableData.length > 0 ? 45 : 0
			document.documentElement.style.setProperty(`--height`, `${this.tableHeight - subtrahend}px`)
		}
	},
	created() {
	},
	mounted() {
		const subtrahend = this.tableData.length > 0 ? 45 : 0
		document.documentElement.style.setProperty(`--height`, `${this.tableHeight - subtrahend}px`)
	},
	components: {},
	methods: {}
}
</script>
<style lang="less" scoped>
:root {
	--height: 600px;
}
/* 筛选 */
.filter-wrapper {
	display: flex;
	justify-content: space-between;
}
.filter-left {
	display: flex;
}
.filter-right {
	display: flex;
}
/* tab */
.tab-wrapper {
	background-color: #fff;
	border-radius: 0 10px 10px 10px;
	padding: 10px;
}

/* 通用  */

.mr10 {
	margin-right: 10px;
}
.mt10 {
	margin-top: 10px;
}

.filter-select {
	width: 175px;
}
.filter-input {
	width: 175px;
}

/* 表格组件 */
/deep/ .ant-table tr th {
	background: #f4f4f4;
	font-size: 13px;
}

/deep/.ant-table-body {
	height: var(--height) !important;
	overflow-y: scroll;
}

/deep/.ant-table-thead {
	position: sticky;
	top: 0;
	z-index: 666;
}
/deep/ .ant-pagination {
	margin: 10px 0;
}
/deep/ .ant-table-placeholder {
	border: none;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

</style>
