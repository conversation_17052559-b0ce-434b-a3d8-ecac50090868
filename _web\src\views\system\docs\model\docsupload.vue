<template>
<a-modal
        title="以下文件清单务必上传"
		:visible="visible"
		:width="1000"
		:centered="true"
		@cancel="cancel"
	>
    <template slot="footer">
      <div>
        <a-button key="back" @click="cancel">关闭</a-button>
      </div>
    </template>
    <a-spin :spinning="loading" style="background:#fff">
        <!-- <div class="btns" v-if="isNowStage">
            <span class="count_tip">文档符合率：{{conformity}}</span><span class="count_tip">文档有效性：{{effective}}</span>
            <a-popconfirm v-for="(item,i) in btns" :key="i" style="margin-right:3px" placement="topRight" title="确认执行？" @confirm="() => item.transitionId == '241' ? callDocsCommit(item.transitionId) : callAgreeOrnot(item.transitionId)">
                <a>{{item.transitionName}}</a>
            </a-popconfirm>
        </div> -->
        <div class="docsitem" v-for="(val,key,index) in dosMap" :key="index">
            <div class="title"><!-- {{index+1}}. -->{{key}}</div>
            <a-table class="docTable" :pagination="false" :columns="columns" :data-source="dosMap[key]" :rowKey="(record) => record.id" size='middle'>

               <!--  <div slot="docIsNeed" slot-scope="text,record" style="width: 100%;text-align: center">
                    <a-select v-if="isNowStage && dqePerm" :key="record.docIsNeed" :default-value="text == -1 ? '-': parseInt(text)" :dropdownMatchSelectWidth="false"  @change="changeSelect($event,record,'docIsNeed')">
                        <a-select-option :value="parseInt(-1)">-</a-select-option>
                        <a-select-option :value="parseInt(0)">No</a-select-option>
                        <a-select-option :value="parseInt(1)">Yes</a-select-option>
                    </a-select>
                    <div v-else>
                        <span v-if="parseInt(text) == -1">-</span>
                        <span v-if="parseInt(text) == 1">Yes</span>
                        <span v-if="parseInt(text) == 0">No</span>
                    </div>
                </div> -->
                <!-- <div slot="score" slot-scope="text,record" style="width: 100%;text-align: center">
                    <a-select v-if="isNowStage && dqePerm && record.fileId != '0'" :key="record.score" :default-value="record.score == null ? '-': parseInt(text)" :dropdownMatchSelectWidth="false"  @change="changeSelect($event,record,'score')">
                        <a-select-option :value="parseInt(0)">0</a-select-option>
                        <a-select-option :value="parseInt(2)">2</a-select-option>
                        <a-select-option :value="parseInt(6)">6</a-select-option>
                        <a-select-option :value="parseInt(8)">8</a-select-option>
                        <a-select-option :value="parseInt(10)">10</a-select-option>
                    </a-select>
                    <span v-else>{{text}}</span>
                </div> -->
                <!-- <div slot="remark" slot-scope="text,record">
                    <a-textarea v-if="isNowStage && dqePerm"
                        :auto-size="{ maxRows:3,minRows:1 }"
                        style="width:100%"
                        v-model="record.remark"
                        @blur="(e) => {
                            const { value } = e.target
                            let params = {}
                            params['remark'] = value
                            params['id'] = record.id
                            callSaveDoc(params,record,'remark')
                        }"
                        placeholder="填写备注"
                    />
                    <div v-else>{{text}}</div>
                </div> -->
                
                <span class="action" slot="action" slot-scope="text,record">
                    <span v-if="isNowStage && engeerPerm">
                        <a-popconfirm v-if="record.fileId != '0'" placement="topRight" title="确认删除？" @confirm="() => callFileInfoDelete(record)">
                            <a class="a-upload">删除</a>
                        </a-popconfirm>
                        <a-upload v-else :showUploadList="false" :file-list="fileList" :headers="headers" :action="postUrl" :multiple="false" name="file" @change="value => handleChange(value, record)">
                            <a class="a-upload">上传</a>
                        </a-upload>
                    </span>
                    <span v-else></span>
                </span>

                <span slot="planDate">
                    {{planDate}}
                </span>


                <!-- <div slot="status" slot-scope="text,record">
                    <span v-if="record.score == 0" class="circle red"></span>
                    <span v-else-if="record.score > 0 && record.score < 10" class="circle yellow"></span>
                    <span v-else-if="record.score == 10" class="circle green"></span>
                </div> -->
                <template slot="docName" slot-scope="text,record">
                    <span v-if="record.fileId == '0'">{{text}}</span>
                    <a v-else-if="text.includes('pdf') || text.includes('PDF') || text.includes('png') || text.includes('jpg') || text.includes('jpeg')" @click="topreview(record)">{{text}}</a>
                    <a v-else @click="callFileInfoDownload(record)">{{text}}</a>
                </template>

            </a-table>
        </div>
    </a-spin>
</a-modal>
</template>

<script>
import moment from 'moment'
import Vue from 'vue'
import {ACCESS_TOKEN} from '@/store/mutation-types'
import {getDocList,saveDoc,getTransition,docsCommit,docAgreeOrNot} from "@/api/modular/system/docManage"
import {sysFileInfoDelete,sysFileInfoDownload} from '@/api/modular/system/fileManage'
import { getProjectDetail } from "@/api/modular/system/report"
export default {
    /* props: {
        issueId: {
            type: Number,
            default: 0
        },
        stage:{
            type:String,
            default:''
        },
        projectdetail: {
            type: Object,
            default: {}
        }
    }, */
    data() {
        return {
            planDate:'',
            visible:false,
            /* issueId:0,
            stage:0, */
            projectdetail:{},
            conformity:null,
            effective:null,
            btns:[],
            dqePerm:[],
            engeerPerm:[],
            isNowStage:true,
            postUrl: '/api/sysFileInfo/uploadfile',
            headers: {
                Authorization: 'Bearer ' + Vue.ls.get(ACCESS_TOKEN),
            },
            fileList: [],
            queryParam:{},
            updateParam:{},
            dosMap:{},
            columns:[
                {
                    title: '序号',
                    dataIndex: 'no',
                    width: 40,
                    align: 'center',
                    customRender: (text, record, index) => `${index + 1}`
                },
                {
                    title: '输出资料',
                    dataIndex: 'outputFile',
                    width: 120,
                    align: 'center',
                },
                {
                    title: '责任部门',
                    dataIndex: 'manager',
                    width: 80,
                    align: 'center',
                },
                {
                    title: '计划完成时间',
                    dataIndex: 'planDate',
                    width: 80,
                    align: 'center',
                    scopedSlots: {customRender: 'planDate'},
                },
                {
                    title: '实际完成时间',
                    dataIndex: 'actualDate',
                    width: 80,
                    align: 'center',
                },
                /* {
                    title: '适用情况',
                    dataIndex: 'docIsNeed',
                    width: 80,
                    align: 'center',
                    //scopedSlots: {customRender: 'docIsNeed'},
                }, */
                {
                    title: '文件',
                    dataIndex: 'docName',
                    width: 120,
                    align: 'center',
                    scopedSlots: {customRender: 'docName'},
                },
                {
                    title: '操作',
                    dataIndex: 'action',
                    width: 60,
                    align: 'center',
                    scopedSlots: {customRender: 'action'},
                },
                /* {
                    title: '评分',
                    dataIndex: 'score',
                    width: 80,
                    align: 'center',
                    scopedSlots: {customRender: 'score'},
                },
                {
                    title: '状态灯',
                    dataIndex: 'status',
                    scopedSlots: {customRender: 'status'},
                    width: 80,
                    align: 'center',
                }, 
                {
                    title: '备注',
                    dataIndex: 'remark',
                    width: 120,
                    scopedSlots: {customRender: 'remark'},
                },*/
            ],
            loading: false,
            YoN:['NO','YES'],
        }
    },
    created() {
    },
    methods: {
        
        view(docMap,planDate){
            // this.issueId = issueId
            // this.stage = stage
            //this.callProjectDetail()
            this.dosMap = docMap
            this.planDate = planDate
            this.engeerPerm = this.hasPerm('docs:engeneer') 
            
            this.visible = true
        },
        moment,
        cancel(){
            this.visible = false
        },
        /* callProjectDetail(){
            this.loading = true
            let params = {issueId: this.issueId,title:''}
            getProjectDetail(params)
            .then((res)=>{
                if (res.result) {
                    this.projectdetail = res.data
                    this.queryParam = {
                        issueId:this.issueId,
                        stage: this.stage,
                        docLevel:this.projectdetail.level
                    }
                    this.isNowStage = true
                    this.callDocsData()

                } else {
                this.$message.error(res.message,1);
                }
                this.loading = false
            })
            .catch((err)=>{
                this.loading = false
                this.$message.error('错误提示：' + err.message,1)
            });
        }, */
        callFileInfoDownload (record) {
            this.loading = true
            sysFileInfoDownload({ id: record.fileId }).then((res) => {
                this.loading = false
                this.downloadfile(res)
            }).catch((err) => {
                this.loading = false
                this.$message.error('下载错误：获取文件流错误')
            })
        },
        downloadfile (res) {
            var blob = new Blob([res.data], { type: 'application/octet-stream;charset=UTF-8' })
            var contentDisposition = res.headers['content-disposition']
            var patt = new RegExp('filename=([^;]+\\.[^\\.;]+);*')
            var result = patt.exec(contentDisposition)
            var filename = result[1]
            var downloadElement = document.createElement('a')
            var href = window.URL.createObjectURL(blob) // 创建下载的链接
            var reg = /^["](.*)["]$/g
            downloadElement.style.display = 'none'
            downloadElement.href = href
            downloadElement.download = decodeURI(filename.replace(reg, '$1')) // 下载后文件名
            document.body.appendChild(downloadElement)
            downloadElement.click() // 点击下载
            document.body.removeChild(downloadElement) // 下载完成移除元素
            window.URL.revokeObjectURL(href)
        },
        topreview(record){
            window.open(process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id=' + record.fileId + '#navpanes=0', "_blank");
        },
        callFileInfoDelete (record) {
            this.loading = true
            let $params = {
                id:record.fileId
            }
            sysFileInfoDelete($params).then((res) => {
                if (res.success) {
                    let params ={}
                    params['docName'] = null
                    params['id'] = record.id
                    params['action'] = 3
                    record.fileId = '0'
                    record.score = null
                    record.actualDate = null
                    //this.callCount()
                    this.callSaveDoc(params,record,'docName')

                } else {
                    this.$message.error('删除失败：' + res.message)
                }
            }).catch((err) => {
                this.$message.error('删除错误：' + err.message)
            }).finally((res) => {
                this.loading = false
            })
        },
        handleChange(info,record) {
            this.loading = true
            let fileList = [...info.fileList];
            this.fileList = fileList.slice(-1);

            if (info.file.status !== 'uploading') {
            }
            if (info.file.status === 'done') {
                let res = info.file.response
                if (res.success) {
                    
                    let params ={}
                    params['docName'] = res.data.fileOriginName
                    params['id'] = record.id
                    params['fileId'] = res.data.id
                    params['action'] = 2
                    record.fileId = res.data.id
                    record.actualDate = moment().format('YYYY-MM-DD')
                    //this.callCount()
                    this.callSaveDoc(params,record,'docName')

                } else {
                    this.$message.error(res.message)
                    this.loading = false
                }
            } else if (info.file.status === 'error') {
                this.$message.error(`${info.file.name} 文件上传失败`);
                this.loading = false
            }
        },
        /* changeSelect(val, record, colName){
            let params ={}
            params[colName] = val
            params['id'] = record.id
            if (colName == 'score') {
                params['action'] = 1
                //this.callCount()
            }
            this.callSaveDoc(params,record,colName)
        }, */
        callSaveDoc(params,record,colName) {
            this.loading = true
            saveDoc(params).then((res) => {
                if (res.success) {
                    record[colName] = params[colName]
                } else {
                    this.$message.error('保存失败：' + res.message)
                }
            }).finally((res) => {
                this.loading = false
            })
        },
        /* callDocsData() {
            this.loading = true
            const stage = this.projectdetail.productStageItems.find(e=>e.stage == this.stage)
            
            this.queryParam.planDate = stage ? stage.planReviewDate : null
            getDocList(this.queryParam)
                .then((res) => {
                    if (res.success) {
                        let obj = {}
                        res.data.forEach(item => {
                            if (!obj[item.process]) {
                                obj[item.process] = [];
                            }
                            obj[item.process].push(item);
                        });
                        this.dqePerm = (this.hasPerm('docs:dqe') && res.data[0]['isOver'] != '2')
                        this.engeerPerm = (this.hasPerm('docs:engeneer') && (res.data[0]['isOver'] == '0' || res.data[0]['isOver'] == '3'))
                        this.dosMap = obj


                    } else {
                        this.$message.error(res.message, 1);
                    }
                    this.loading = false
            })
            .catch((err) => {
                this.loading = false
                this.$message.error('错误提示：' + err.message, 1)
            });
        }, */
        /* callGetTransition(params){
            this.loading = true
            getTransition(params)
                .then((res) => {
                    if (res.success) {
                        let btns = []
                        if (this.engeerPerm && !this.dqePerm) {
                            btns = res.data.filter(e=>e.transitionId == '241')
                        }else if (!this.engeerPerm && this.dqePerm) {
                            btns = res.data.filter(e=>e.transitionId != '241')
                        }else if(this.engeerPerm && this.dqePerm){
                            btns = res.data
                        }

                        this.btns = btns
                    } else {
                        this.$message.error(res.message, 1);
                    }
                    this.loading = false
            })
            .catch((err) => {
                this.loading = false
                this.$message.error('错误提示：' + err.message, 1)
            });
        }, */
        /* callDocsCommit(transitionId) {
            this.loading = true
            this.queryParam.transitionId = transitionId
            docsCommit(this.queryParam)
            .then((res) => {
                if (res.success) {
                    this.callDocsData()
                } else {
                    this.$message.error(res.message, 1);
                }
                this.loading = false
            })
            .catch((err) => {
                this.loading = false
                this.$message.error('错误提示：' + err.message, 1)
            });
        }, */
        /* callAgreeOrnot(transitionId){
            this.loading = true
            this.queryParam.transitionId = transitionId
            docAgreeOrNot(this.queryParam)
            .then((res) => {
                if (res.success) {
                    this.callDocsData()
                } else {
                    this.$message.error(res.message, 1);
                }
                this.loading = false
            })
            .catch((err) => {
                this.loading = false
                this.$message.error('错误提示：' + err.message, 1)
            });
        }, */
        /* callCount(){
            let sum = 0
            let fileCount = 0
            let score = 0
            for (const key in this.dosMap) {
                for (const item of this.dosMap[key]) {
                    if (item.docIsNeed == '1') {
                        sum += 1
                        if (item.fileId != '0') {
                            fileCount += 1
                        }
                        if (item.score != null) {
                            //console.log(item.score)
                            score += parseInt(item.score)
                        }
                    }
                }
            }
            this.conformity = ((fileCount/sum)*100).toFixed(2)+'%'
            this.effective = ((score/(sum*10))*100).toFixed(2)+'%'
        } */
    },
}
</script>

<style lang="less" scoped=''>
@import '../docs.less';
/deep/.docTable .ant-table-body{
    height: initial !important;
}
.btns{
    z-index: 900;
}
</style>
<style scoped=''>
@import '../docs.css';
</style>