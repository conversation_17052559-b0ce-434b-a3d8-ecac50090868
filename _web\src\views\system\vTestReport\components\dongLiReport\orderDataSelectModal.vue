<template>
  <div>
    <a-modal title="测试项目选择" width="80%" :bodyStyle="{ padding: 0 }" :visible="visible" style="padding: 0"
             :maskClosable="false" @cancel="visible = false">

      <pbiSearchContainer>
        <pbiSearchItem :span="4" label='委托单号'>
          <a-input v-model="queryParam.folderno" @keyup.enter="$refs.table.refresh()"/>
        </pbiSearchItem>
        <pbiSearchItem :span="4" label='主题'>
          <a-input v-model="queryParam.theme" @keyup.enter="$refs.table.refresh()"/>
        </pbiSearchItem>
        <pbiSearchItem :span="4" label='测试项目别名'>
          <a-input v-model="queryParam.alias" @keyup.enter="$refs.table.refresh()"/>
        </pbiSearchItem>
        <pbiSearchItem :span="4" label='存储天数'>
          <a-input v-model="queryParam.day" @keyup.enter="$refs.table.refresh()"/>
        </pbiSearchItem>
        <pbiSearchItem :span="4" label='测试编码'>
          <a-input v-model="queryParam.celltestcode" @keyup.enter="$refs.table.refresh()"/>
        </pbiSearchItem>
        <pbiSearchItem :span="4" type='btn'>
          <a-button class="mr8" @click="$refs.table.refresh()" type="primary">查询</a-button>
          <a-button class="mr8" @click="tableReset()" >重置</a-button>
        </pbiSearchItem>
      </pbiSearchContainer>

      <s-table class="orderDataTable"
        :columns="columns"
        :data="loadData"
        bordered
        :rowKey="record1 => record1.uuid"
        ref="table"
        :row-selection="{
					selectedRowKeys: selectedRowKeys,
					onSelect: onSelectChange,
					onSelectAll: onSelectAllChange,
					columnWidth: 30
				}"
      >
        <template slot="celltestcode" slot-scope="text, record, index, columns">
          <a @click="openStepData(record)" v-if="record.flowId != null" style="text-align: center">{{ text }}</a>
          <span v-else style="text-align: center">{{ text }}</span>
        </template>

        <template slot="action1" slot-scope="text, record, index, columns">
          <template v-if="record.showHide">
            <a @click="showData(record)" v-if="record.showHide" style="text-align: center">初始化</a>
            <a-divider v-if="record.children != null" type="vertical"/>
          </template>

          <a @click="hideData(record)" v-if="record.children != null || record.isChild" style="text-align: center">隐藏</a>
        </template>

        <template slot="theme" slot-scope="text, record, index, columns">
          <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
            <template slot="title">{{ text }}</template>
            {{ text }}
          </a-tooltip>
        </template>
        <template slot="dataPath" slot-scope="text, record, index, columns">
          <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
            <template slot="title">{{ text ? text : record.testcontent }}</template>
            {{ text ? text : record.testcontent }}
          </a-tooltip>
        </template>
      </s-table>

      <template slot="footer">
        <a-button key="back" @click="visible = false">关闭</a-button>
      </template>
    </a-modal>

    <step-data ref="stepData"></step-data>
  </div>
</template>

<script>
import stepData from "@/views/system/lims/folder/stepData";
import {STable} from "@/components";
import moment from "moment";
import {hideData, showData, tLimsTestdataSchedulePageList} from "@/api/modular/system/limsManager";

export default {
  name: "orderDataSelectModal.vue",
  components: {
    STable,
    stepData
  },
  props: {
    selectedOrderDataList: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {

      // 测试项目选择
      visible: false,
      queryParam: {},
      columns: [
        {
          title: "操作",
          align: "center",
          width: 105,
          scopedSlots: {customRender: 'action1'},
        }, {
          title: "序号",
          align: "center",
          width: 40,
          customRender: (text, record, index) => {
            if (!record.isChild) {
              return index + 1
            }
          }
        },
        {
          title: "委托单号",
          dataIndex: "folderno",
          align: "center",
          width: 90
        },
        {
          title: "主题",
          dataIndex: "theme",
          align: "center",
          ellipsis: true,
          width: 90,
          scopedSlots: {customRender: 'theme'},
        },
        {
          title: "样品编号",
          width: 90,
          align: "center",
          dataIndex: "orderno"
        },
        {
          title: "测试项目别名",
          width: 90,
          align: "center",
          dataIndex: "alias"
        },
        {
          title: "测试编码",
          width: 90,
          align: "center",
          dataIndex: "celltestcode",
          scopedSlots: {customRender: "celltestcode"}
        },
        {
          title: "数据位置",
          width: 80,
          align: "center",
          dataIndex: "dataPath",
          ellipsis: true,
          scopedSlots: {customRender: 'dataPath'},
        }, {
          title: "存储天数",
          width: 75,
          align: "center",
          dataIndex: "day"
        },
        {
          title: "温度",
          width: 40,
          align: "center",
          dataIndex: "tem"
        }, {
          title: "soc",
          width: 40,
          align: "center",
          dataIndex: "soc"
        },
        {
          title: "测试状态",
          width: 80,
          align: "center",
          dataIndex: "endstatusflag",
          customRender: (text, record, index) => {
            if (null != text) {
              return text == 0?'测试中':'完成'
            }
            return text
          }
        },
        {
          title: "开始时间",
          width: 80,
          align: "center",
          dataIndex: "startTime",
          customRender: (text, record, index) => {
            if (null != text) {
              return moment(text).format("YYYY-MM-DD")
            }
            return text
          }
          //
          //scopedSlots: {customRender: 'updateText'},
        },
        {
          title: "结束时间",
          width: 80,
          align: "center",
          dataIndex: "endTime",
          customRender: (text, record, index) => {
            if (null != text && record.endstatusflag == 1) {
              return moment(text).format("YYYY-MM-DD")
            }
            return null
          }
        },
        {
          title: "设备编号",
          width: 60,
          align: "center",
          dataIndex: "equiptcode"
        },
        {
          title: "通道编号",
          width: 60,
          align: "center",
          dataIndex: "channelno"
        }
      ],
      loadData: parameter => {
        this.loadDataList = []
        return tLimsTestdataSchedulePageList(Object.assign(parameter, this.queryParam))
          .then(res => {
            return res.data
          })

      },
      selectedRowKeys: [],
      outQueryFlowRecord: null,
    }
  },
  watch: {
    selectedOrderDataList() {
      this.selectedRowKeys = this.selectedOrderDataList.map(item => item.uuid)
    },
  },
  methods: {
    tableReset() {
      this.queryParam = {}
      this.$refs.table.refresh()
    },
    onSelectChange(record, selected) {
      this.outQueryFlowRecord = record

      if (selected) {
        if (record.flowId == null) {
          this.$message.warn("测试数据为空")
          return
        }
        /*if(record.flowInfoList.length == 0){
            this.$message.warn("测试数据为空")
            return
          }else if(record.flowInfoList.length > 1){
            this.visibleFlow = true
            this.flowInfoData = record.flowInfoList
            this.inFlowActionName = '选中'
            return
          }else if(record.flowInfoList.length == 1){
            record.flowId = record.flowInfoList[0].flowId
          }*/

        if (!this.selectedRowKeys.includes(record.uuid)) {
          this.selectedRowKeys.push(record.uuid)
          this.selectedOrderDataList.push(record)
        }
      } else {
        for (let i = 0; i < this.selectedRowKeys.length; i++) {
          if (this.selectedRowKeys[i] === record.uuid) {
            this.selectedRowKeys.splice(i, 1)
            this.selectedOrderDataList.splice(i, 1)
            break
          }
        }
      }

      this.$emit('selectedOrderDataChange')
    },
    onSelectAllChange(selected, selectedRows, changeRows) {
      if (selected) {
        /*for (let i = 0; i < selectedRows.length; i++) {
          if (selectedRows[i].flowId == null) {
            this.$message.warn("序号" + (i + 1) + "测试数据为空")
            return
          }
        }*/
        selectedRows.forEach(item => {
          if (!this.selectedRowKeys.includes(item.uuid) && item.flowId != null) {
            this.selectedRowKeys.push(item.uuid)
            this.selectedOrderDataList.push(item)
          }

          /*if(item.children && item.children.length > 0){
              item.children.forEach(inItem => {
                if (!this.selectedRowKeys.includes(inItem.id)) {
                  this.selectedRowKeys.push(inItem.id)
                  this.selectedRows.push(inItem)
                  this.selectedOrderDataList.push(inItem)
                }
              })
            }*/
        })
      } else {
        for (let i = 0; i < changeRows.length; i++) {
          if (this.selectedRowKeys.includes(changeRows[i].uuid)) {
            let index = this.selectedRowKeys.indexOf(changeRows[i].uuid)
            this.selectedRowKeys.splice(index, 1)
            this.selectedOrderDataList.splice(index, 1)
          }
        }
      }

      this.$emit('selectedOrderDataChange')
    },
    openStepData(record, flag) {
      this.outQueryFlowRecord = record

      //历史数据处理
      if (null == record.flowInfoList && !flag) {
        this.$refs.stepData.query(record, false)
        return;
      }

      if (record.flowId != null) {
        this.outQueryFlowRecord.flowId = record.flowId
        this.$refs.stepData.query(this.outQueryFlowRecord, false)
      } else {
        this.$message.warn("测试数据为空")
        return
      }
    },
    showData(record) {
      showData({celltestcode: record.celltestcode}).then(res => {
        this.$refs.table.refresh()
      })
    },
    hideData(record) {
      hideData({id: record.id}).then(res => {
        this.$refs.table.refresh()
      })
    },
  }
}
</script>

<style lang="less" scoped>
@import '/src/components/pageTool/style/pbiSearchItem.less';

.mr8 {
  margin-right: 8px;
}

/deep/ .ant-table-thead > tr > th {
  padding: 5px !important;
  font-size: 13px !important;
  color: rgba(0, 0, 0, .85) !important;
  font-weight: 500 !important;
}

/deep/ .ant-table-tbody > tr > td {
  padding: 0px !important;
  font-size: 12px !important;
  color: #333 !important;
  font-weight: 400 !important;
}

/deep/ .orderDataTable .ant-table-pagination.ant-pagination {
  float: right;
  margin: 8px 0 0;
  font-size: 12px;
}

/deep/ .s-table-tool {
  padding: 0;
}
</style>