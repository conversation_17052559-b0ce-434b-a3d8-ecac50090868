<template>
    <div>
        <a-breadcrumb class="breadcrumb" separator=">" style="width:80%;margin:auto;">
            <a-breadcrumb-item><a @click="gotoIndex(-2)">信息对齐表</a></a-breadcrumb-item>
            <a-breadcrumb-item><a @click="gotoIndex(-1)">产品开发进展</a></a-breadcrumb-item>
            <a-breadcrumb-item>质量指标</a-breadcrumb-item>
        </a-breadcrumb>
        <ve-table v-if="showDetail" style="width:80%;margin:auto;" :border-y="true" :columns="columns" :table-data="rows" :cell-style-option="cellStyleOption">
        </ve-table>
    </div>
</template>

<script>
import { ALL_APPS_MENU } from '@/store/mutation-types'
    import Vue from 'vue'
    import {
        getStageRate
    } from "@/api/modular/system/report"
    import {
        mapActions,
        mapGetters
    } from 'vuex'
    export default {
        data() {
            return {
                showDetail:false,
                loadingInstance: null,
                title: '',
                columns: [{
                        field: "projectNo",
                        key: "projectNo",
                        title: "项目代号",
                        width: 100
                    },
                    {
                        renderHeaderCell: ({ column }, h) =>{
                            let _title = this.title
                            return (
                                <span>
                                    {_title}
                                </span>
                            );
                        },
                        field:'info',
                        children: [{
                                field: "label",
                                key: "label",
                                title: "",
                                width: 100,
                                renderBodyCell:({ row, column, rowIndex })=>{
                                    return (
                                        row.flag < 5? (<span
                                            class="clickheadstyle"
                                            onClick={() => {
                                                this.handleTo(row);
                                            }}
                                        >
                                            {row['label']}
                                        </span>):(
                                            <span>{row['label']}</span>
                                        )
                                    );
                                },
                            },
                            {
                                field: "value",
                                key: "value",
                                title: "",
                                width: 100,
                                renderBodyCell:({ row, column, rowIndex })=>{
                                    return (
                                        row.flag == 1 || row.flag == 2 ? (<span
                                            class="clickheadstyle"
                                            onClick={() => {
                                                this.handleTo(row);
                                            }}
                                        >
                                            {row['value']}
                                        </span>):(
                                            <span>{row['value']}</span>
                                        )
                                    );
                                },
                            },
                        ]
                    },
                ],
                rows: [],
                cellStyleOption: {
                    headerCellClass: ({ column, rowIndex }) => {
                        if (column.field === "label" || column.field === "value") {
                            return "hidden-cell";
                        }
                        if (column.field === 'info') {
                            return 'custom-span';
                        }
                    },
                },
            }
        },
        computed: {
            ...mapGetters(['userInfo'])
        },
        methods: {
            gotoIndex(index){
                this.$router.go(index)
            },
            rowspan(){
                this.$nextTick(() => {
                    document.querySelector('.custom-span').setAttribute('rowspan', '2');
                })
            },
            show() {
                this.loadingInstance.show();
            },
            close() {
                this.loadingInstance.close();
            },
            switchApp() {
                /* const apps = Vue.ls.get(ALL_APPS_MENU)
                const _newApps = []
                for (const item of apps) {
                    
                    _newApps.push(item)
                }
                Vue.ls.set(ALL_APPS_MENU, _newApps) */
            },
            handleTo(row) {
                this.switchApp()
                let paths = ['/sample','/stage_trouble','/stage_risk','/assess','/doc_quality']
                //let names = ['Sample','StageTrouble','StageRisk','Assess','DocQuality']
                this.$router.push({

                    path: paths[row.flag],
                    //name: names[row.flag],
                    query: {
                        issueId: row["issueId"],
                        stage: row["stage"],
                    },
                });
            },
            ...mapActions(['MenuChange']),
            callStageRate() {
                this.show()
                let params = {
                    issueId: this.$route.query.issueId,
                    stage: this.$route.query.stage
                }
                getStageRate(params).then((res) => {
                        if (res.result) {
                            let labs = ['一次送样合格率', '阶段问题关闭率', '阶段风险关闭率', '计划评审达成率', '文件质量达成率', '汇总']
                            let statuText = ['K0 立项评审', 'M1 项目规划', 'M2 A样方案冻结', 'M2 转阶段', 'M3 B样方案冻结', 'M3 转阶段', 'M4 C样方案冻结', 'M5 PPAP', 'M6 SOP', '结项']
                            let _resData = res.data
                            let _list = []
                            let i = 0
                            for (const item of labs) {
                                _list.push({
                                    //prev: '产品开发进展-'+statuText[this.$route.query.stage-1]+'阶段完成时间'+this.$route.query.date,
                                    projectNo: this.$route.query.productProjectName,
                                    label: item,
                                    value: _resData[i],
                                    stage: this.$route.query.stage,
                                    issueId: this.$route.query.issueId,
                                    flag: i
                                })
                                i++
                            }
                            this.title = statuText[this.$route.query.stage - 1] + '阶段质量指标'
                            this.rows = _list
                            this.showDetail = true;
                            this.rowspan()
                        } else {
                            this.$message.error(res.message, 1);
                        }
                        this.close()
                    })
                    .catch((err) => {
                        this.close()
                        this.$message.error('错误提示：' + err.message, 1)
                    });
            },
        },
        created() {
            this.loadingInstance = this.$veLoading({
                target: document.querySelector("#loading-detail"),
                name: "flow",
            });
            this.callStageRate()
        },
        destroyed() {
            this.loadingInstance.destroy();
        },
    }
</script>

<style lang="less">
    @import './vetable.less';
    .hidden-cell {
        display: none;
    }
    .clickheadstyle {
        cursor: pointer;
        display: inline-block;
        width: 100%;
    }
    .clickheadstyle:hover {
        color: #d6d6d6;
    }
    .breadcrumb{
        padding: 5px 0;
        padding-left: 13px;
    }.ant-breadcrumb a{
  color:#5d90fa !important;
}.ant-breadcrumb{
  font-size: 12px !important;
}
</style>