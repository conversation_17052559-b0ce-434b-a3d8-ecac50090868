<template>
  <a-modal :visible="true" title="DPV测试申请" :width="700" :confirmLoading="confirmLoading" @ok="handleSubmit"
    @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item label="分类" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-select style="width: 100%"
                v-decorator="['productType', {rules: [{required: true, message: '请选择分类!'}]}]" placeholder="请选择分类">
                <a-select-option v-for="(item,i) in getDict('dpv_test_manage_type')" :key="i"
                  :value="item.code">{{item.name}}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="阶段" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-select style="width: 100%" v-decorator="['phase', {rules: [{required: true, message: '请选择阶段!'}]}]"
                placeholder="请选择阶段">
                <a-select-option v-for="(item,i) in getDict('dpv_test_manage_phase')" :key="i"
                  :value="item.code">{{item.name}}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item label="产品名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-if="isCustom" v-decorator="['productName', {rules: [{required: true, message: '请填写产品名称!'}]}]"
                placeholder="请填写产品名称" />
              <a-select v-else labelInValue  style="width: 100%" placeholder="请选择产品名称" show-search option-filter-prop="children"
                v-decorator="['productName', {rules: [{required: true, message: '请选择产品名称!'}]}]" 
                @change="$event => handleProductNameChange($event)">
                <a-select-option v-for="productName in productNameOptions" :key="productName.key" :value="productName.key"  :label="productName.label">
                  {{ productName.label}}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="项目名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-if="isCustom" v-decorator="['projectName', {rules: [{required: true, message: '请填写项目名称!'}]}]"
                placeholder="请填写项目名称" ></a-input>
              <a-select v-else labelInValue style="width: 100%"
                v-decorator="['projectName', {rules: [{required: true, message: '请选择项目名称!'}]}]" placeholder="请选择项目名称" @change="$event => handleProjectNameChange($event)">
                <a-select-option v-for="projectName in projectNameOptions" :key="projectName.key" :value="projectName.key"  :label="projectName.label">
                  {{ projectName.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

        </a-row>
        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item label="客户" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-decorator="['customer', {rules: [{required: true, message: '请填写客户!'}]}]" placeholder="请填写客户" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <span slot="label"><span class="required-txt">TVE</span></span>
              <a-dropdown style="width: 100%" v-model="tveShow" placement="bottomCenter" :trigger="['click']">
                <a-button class="dropdown-btn">
                  <span v-if="tveList.length !== 0">
                    <span v-for="(item,i) in tveList" :key="i" class="mr8">{{item.name}} <a-icon
                        class="dropdown-btn-icon" type='close' @click.stop="clearTve(i)" /></span>
                  </span>
                  <span style="color:#bfbfbf ; " v-else>请选择TVE</span>
                  <a-icon style="color: rgba(0, 0, 0, 0.25);" type="down" />
                </a-button>
                <a-menu slot="overlay">
                  <a-spin :spinning="tveLoading" style="padding:10px 24px 0 24px;width:100%">
                    <a-input-search v-model="tveAccount" placeholder="搜索..." @change="handleTveSearch" />
                    <s-table style="width:100%;" ref="tveTable" :rowKey="record => record.id" :columns="UserColumns"
                      :data="loadData" :customRow="record => customTveRow(record)" :scroll="{ y: 120, x: 120 }">
                    </s-table>
                  </a-spin>
                </a-menu>
              </a-dropdown>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
    <template slot="footer">

      <a-button type="link" class="choose-btn" @click="handleCustom">{{isCustom ? '点击此处，修改为选择产品名称、项目名称' :
        '未找到相关产品，点击此处，修改为自定义产品名称、项目名称'}} </a-button>
      <a-button @click="handleCancel">
        取消
      </a-button>
      <a-button type="primary" @click="handleSubmit">
        申请
      </a-button>

    </template>
  </a-modal>
</template>

<script>
  import Vue from 'vue'
  import {
    DICT_TYPE_TREE_DATA
  } from '@/store/mutation-types'
  import { STable } from "@/components"
  import { getUserLists } from "@/api/modular/system/userManage"
  import { addTestProgressRecord } from "@/api/modular/system/dpvTestManage"

  export default {
    props: {
      selectedProduct: {
        type: Array,
        default: []
      },
      productNameOptions: {
        type: Array,
        default: []
      },
      projectNameAllOptions: {
        type: Object,
        default: {}
      },

    },
    components: {
      STable
    },
    data() {
      return {
        tveLoading: false,
        tveShow: false,
        isCustom: false,
        confirmLoading: false,
        tveAccount: '',
        issueId: '',
        loadData: parameter => {
          return getUserLists({ searchValue: this.tveAccount || '', }).then(res => {
            return res.data
          })
        },
        form: this.$form.createForm(this),
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 8
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 15
          }
        },
        tveList: [],
        projectNameOptions: [],
        UserColumns: [
          {
            title: "账号",
            dataIndex: "account"
          },
          {
            title: "姓名",
            dataIndex: "name"
          }
        ],

      }
    },
    created() {
      this.projectNameOptions = this.projectNameAllOptions[this.productNameOptions[0].label]

    },
    methods: {
      getDict(code) {
        const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
        return dictTypeTree.filter(item => item.code == code)[0].children
      },
      clearTve(targetIndex) {
        this.tveList.splice(targetIndex, 1)
      },
      handleTveSearch(index) {
        this.$refs.tveTable.refresh()
      },
      customTveRow(row) {
        return {
          on: {
            click: () => {
              const have = this.tveList.filter(filterItem => filterItem.account === row.account)
              if (have.length === 0) this.tveList.push(row)
            }
          }
        }
      },
      handleCustom() {
        this.isCustom = !this.isCustom
        this.form.setFieldsValue({
          productName: undefined,
          projectName: undefined,
          customer: ''
        })

      },

      handleProductNameChange(e) {
        e.label = e.label.trim()
        this.projectNameOptions = this.projectNameAllOptions[e.label]
        const have = this.productNameOptions.filter(filterItem => filterItem.label == e.label && filterItem.key == e.key)
        this.issueId = have[0].key
        this.form.setFieldsValue({
          projectName: this.projectNameAllOptions[e.label][0],
          customer: have[0].customer
        })
      },
      handleProjectNameChange(e){
        e.label = e.label.trim()
        const have = this.projectNameOptions.filter(filterItem => filterItem.label == e.label && filterItem.key == e.key)
        this.issueId = have[0].key
        this.form.setFieldsValue({
          customer: have[0].customer
        })
      },

      handleSubmit() {
        if (this.tveList.length == 0) {
          this.$message.warn('请先选择TVE')
          return
        }
        this.confirmLoading = true
        const {
          form: {
            validateFields
          }
        } = this

        validateFields((errors, values) => {
          values.tve = JSON.stringify(this.tveList.map(mapItem => { return { code: mapItem.account, name: mapItem.name } }))
          if (!this.isCustom) values.issueId = this.issueId
          if(typeof values.productName === 'object')  values.productName = values.productName.label
          if(typeof values.projectName === 'object')  values.projectName = values.projectName.label

          if(this.selectedProduct.includes(this.issueId)){
            this.confirmLoading = false
            return this.$message.error('产品/项目已被选择,请重新选择产品/项目')
          } 
          
          if (!errors) {
            addTestProgressRecord(values).then(res => {
              if (res.success) {
                this.$message.success('申请成功')
                this.confirmLoading = false
                this.$emit('submit')
              } else {
                this.$message.error('申请失败：' + res.message)
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel() {
        this.$emit('cancel')
      }

    }
  }
</script>

<style lang="less" scoped>
  .choose-btn {
    font-size: 12px;
    position: absolute;
    bottom: 24px;
    left: 24px;
  }

  .required-txt::before {
    display: inline-block;
    margin-right: 4px;
    color: #f5222d;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: '*';
  }

  .dropdown-btn {
    padding-left: 10px;
    width: 100%;
    margin-top: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .dropdown-btn-icon {
    pointer-events: auto;
    color: rgba(0, 0, 0, 0.25);
  }

  /deep/ .ant-form label{
    font-size: 12px;
  }
  /deep/ .ant-btn{
    font-size: 12px;
  }
  /deep/ .ant-select{
    font-size: 12px;
  }
  /deep/ .ant-input{
    font-size: 12px;
  }
</style>