<template>
  <div style="padding: 10px">
    <pbiTabs :tabsList="laboratoryList" :activeKey="address" @clickTab="changeTab"></pbiTabs>
    <div style="background-color: #FFFFFF;padding: 10px;border-radius: 5px;">


      <!--    <a-tabs type="card" @change="changeTab" v-model="address">
            <a-tab-pane key="collect" tab="收藏" >
            </a-tab-pane>
            <a-tab-pane key="all" tab="全部" v-if="hasPerm('progress:all')">
            </a-tab-pane>

            <a-tab-pane key="hz" tab="第六实验室(HZ)" v-if="hasPerm('progress:all') || hasPerm('progress:a1_3f') || hasPerm('progress:r2_2f') || hasPerm('progress:r4_4f')">
            </a-tab-pane>

            <a-tab-pane key="JM" tab="第六实验室(JM)" v-if="hasPerm('progress:all') || hasPerm('progress:jm')">
            </a-tab-pane>

            <a-tab-pane key="R3" tab="第四实验室" v-if="hasPerm('progress:all') || hasPerm('progress:r3')">
            </a-tab-pane>


          </a-tabs>-->
      <!-- 操作部分 开始 -->
      <div class="operate-wrap" :style="{ height:show ? '100%' : '28px' }">
        <div class="operate-left search-container">

          <div class="operate-block" v-for="item in operateData.filter(f => f.tab == null || (f.tab != null && address == f.tab))">
            <div class="label mr8">{{item.label}} : </div>

            <div v-if="item.type === 'input'" class="input-short">
              <a-input v-model="queryParam[item.dataIndex]" @keyup.enter="getList" @change="getList"/>
            </div>

            <div v-else-if="item.type === 'select'" class="input-short">
              <a-select v-if="item.dataIndex === 'allAddress' " :dropdown-match-select-width="false" dropdown-class-name="dropdownClassName" v-model="allAddress" style="width: 100%;" allowClear @keyup.enter="getList" @change="getList"  >
                <a-select-option v-for="cItem in item.selectOption" :value="cItem.value">{{cItem.label}}</a-select-option>
              </a-select>

              <a-select v-else v-model="queryParam[item.dataIndex]" :dropdown-match-select-width="false" dropdown-class-name="dropdownClassName" allowClear style="width:100%" @keyup.enter="getList" @change="getList">
                <a-select-option v-for="cItem in item.selectOption" :value="cItem.value">{{cItem.label}}</a-select-option>
              </a-select>
            </div>

            <div v-else-if="item.type === 'multipleSelect'" class="input-long">
              <a-select v-model="status" style="width:100%"  allowClear mode="multiple" :maxTagCount="parseInt(4)" @change="changeStatus" >
                <a-select-option v-for="multipleItem in item.selectOption" :value="multipleItem.value">{{multipleItem.label}}</a-select-option>
              </a-select>
            </div>

            <div v-else-if="item.type === 'rangePicker'" class="input-long">
              <a-range-picker style="width:100%" @change="rangeChange"></a-range-picker>
            </div>
          </div>
        </div>
        <div class="operate-center search-container">
          <div class="secondary-btn">
            <a-button class="mr8" @click="getList(true)">重置</a-button>
            <a-button class="mr8" @click="getList()" type="primary">查询</a-button>
            <a-button type="primary" class="mr8" @click="checkCalendarReport()">查看报告</a-button>

            <a-button v-if="hasPerm('progress:add') && address != 'lims'" type="primary" class="mr8 mb8" @click="openAdd"  >新增</a-button>
            <a-button v-if="hasPerm('progress:edit') && address != 'lims'" type="primary" class="mr8 mb8" @click="openEdit" >编辑</a-button>
            <a-popconfirm
              title="确定要清空测试数据吗?"
              ok-text="确定"
              cancel-text="取消"
              @confirm="clearTestData"
              placement="topRight"
              :visible="clearDataVisible"
              @cancel="() => (clearDataVisible = false)"
            >
              <a-button v-if="hasPerm('testProjectTodoTask:clearCalendarTestData') && address != 'lims'" type="primary" class="mr8 mb8" @click="clearTestDataBefore" >清空测试数据</a-button>
            </a-popconfirm>
            <a-button v-if="hasPerm('progress:editDataInput') && address != 'lims'" type="primary" class="mr8 mb8" @click="openEditDataSelect" >修改数据录入项目</a-button>
            <a-button v-if="hasPerm('progress:editOrderTsk') && address != 'lims'" type="primary" class="mr8 mb8" @click="openChooseOrdTask" >选择测试项目</a-button>
            <a-button v-if="hasPerm('progress:in') && address != 'lims'" type="primary" class="mr8 mb8" @click="openEditActual" >进箱</a-button>
            <a-button v-if="hasPerm('progress:export') && address != 'lims'" type="primary" class="mr8 mb8" @click="exportData" >导出</a-button>
            <a-button v-if="hasPerm('testProjectTodoTask:getBatteryInfoById') && address != 'lims'" type="primary" class="mr8 mb8" @click="editBatteryStatus" >修改电芯状态</a-button>

            <a-popconfirm
              v-if="hasPerm('progress:delete') && address != 'lims'"
              :visible="deleteVisible"
              title="确定删除吗?"
              placement="topRight"
              ok-text="确定"
              cancel-text="取消"
              @confirm="deleteRecord"
              @cancel="() => deleteVisible = false"
            >
              <a-button type="primary" class="mr8 mb8"  @click="deleteBefore">删除</a-button>
            </a-popconfirm>

            <a-popconfirm
              v-if="hasPerm('progress:lims') && address == 'lims'"
              :visible="importVisible"
              title="确定同步吗?"
              placement="topRight"
              ok-text="确定"
              cancel-text="取消"
              @confirm="importLims"
              @cancel="() => importVisible = false"
            >
              <a-button type="primary" class="mr8 mb8" @click="importBefore">确认导入</a-button>
            </a-popconfirm>

            <a-button v-if="hasPerm('testProjectTodoTask:importTestDataExceptHeight')" v-show="!isUpload" type="primary" class="mr8 mb8" @click="beforeUploadCheck">导入数据(除尺寸外)</a-button>
            <a-upload
              v-show="isUpload"
              name="file"
              accept="*"
              :headers="headers"
              :action="`/api/testProjectTodoTask/importTestDataExceptHeight`"
              :fileList="fileList"
              :data="{ testProgressId: importParam.id, folderNo: importParam.testCode }"
              :showUploadList="false"
              @change="importTestData($event)"
            >
              <a-button v-if="hasPerm('testProjectTodoTask:importTestDataExceptHeight')" type="primary" class="mr8 mb8" @click="beforeUploadCheck">导入数据(除尺寸外)</a-button>
            </a-upload>

            <a-button v-if="hasPerm('testProjectTodoTask:importTestDataExceptHeight')" v-show="!isUpload" type="primary" class="mr8 mb8" @click="beforeUploadCheck">导入数据(仅尺寸)</a-button>
            <a-upload
              v-show="isUpload"
              name="file"
              accept="*"
              :headers="headers"
              :action="`/api/testProjectTodoTask/importTestDataOnlyHeight`"
              :fileList="fileList"
              :data="{ testProgressId: importParam.id, folderNo: importParam.testCode }"
              :showUploadList="false"
              @change="importTestData($event)"
            >
              <a-button v-if="hasPerm('testProjectTodoTask:importTestDataExceptHeight')" type="primary" class="mr8 mb8" @click="beforeUploadCheck">导入数据(仅尺寸)</a-button>
            </a-upload>

            <a-button type="primary" class="mr8 mb8" @click="exportHandleResult">导出处理结果</a-button>
            <a-button type="primary" class="mr8 mb8" @click="exportSizeOriData">导出尺寸原始数据</a-button>
            <a-button v-if="hasPerm('testProjectTodoTask:importTestDataExceptHeight')" type="primary" class="mr8 mb8" @click="exportSizeTemplate">导出尺寸模板</a-button>

            <a-button v-if="hasPerm('testProjectTodoTask:importTestDataExceptHeight')" v-show="!isUpload" type="primary" class="mr8 mb8" @click="beforeUploadCheck">导入阶段式数据(除尺寸外)</a-button>
            <a-upload
              v-show="isUpload"
              name="file"
              accept="*"
              :headers="headers"
              :action="`/api/testProjectTodoTask/importStageDataExceptHeight`"
              :fileList="fileList"
              :data="{ testProgressId: importParam.id, folderNo: importParam.testCode }"
              :showUploadList="false"
              @change="importTestData($event)"
            >
              <a-button v-if="hasPerm('testProjectTodoTask:importTestDataExceptHeight')" type="primary" class="mr8 mb8" @click="beforeUploadCheck">导入阶段式数据(除尺寸外)</a-button>
            </a-upload>
            <a-button v-if="hasPerm('testProgress:shareOut')" type="primary" class="mr8 mb8" @click="shareOut">分享至传递平台</a-button>

            <a-button type="primary" class="mr8 mb8" @click="shareTop">置顶</a-button>
          </div>
        </div>

        <div class="search-container">
          <div class='toggle-btn'>
            <a-button size='small' type='link' @click='show=!show' style="display: inline-flex;align-items: center;">
              {{ show ? '收起' : '展开' }}
              <span v-if='show'>
              <a-icon type='double-left'/>
          </span>
              <span v-else>
              <a-icon type='double-right'/>
          </span>
            </a-button>
          </div>
        </div>


      </div>

      <div class="box" ref="box">
        <div class="mid">
          <s-table :columns="columns" :data="loadData" bordered  :rowKey="(record) => record.id" :scroll="{x:true}"
                   :customRow="customRow"
                   :rowSelection="rowSelection"
                   ref="table2"
          >
            <template
              slot="center"
              slot-scope="text, record, index, columns"
            >
              <a @click="openCenter(text.id)" class="grey" style="text-align: center" v-if="text != null && text.day != null
            && record.centerData.length != 0 && checkCenter(record,text)">查看结果</a>
              <div class="grey" v-else-if="text != null && text.day != null">查看结果</div>
              <div class="grey" v-else></div>
            </template>

            <div
              slot="saveAddressTitle"
            >
              <span>存储位置</span>
              <a-tooltip>
                <template slot="title">
                  {{!needData?'查看存储明细':'关闭存储明细'}}
                </template>
                <a-icon type="right" v-if="!needData" @click="dataRefresh" style="margin-left: 10px"/>
                <a-icon type="left" v-else @click="dataRefresh" style="margin-left: 10px"/>
              </a-tooltip>

            </div>

            <template
              slot="centerZero"
              slot-scope="text, record, index, columns"
            >
              <a @click="openCenter(null,record.id)" class="grey" style="text-align: center" v-if="record.centerData.length != 0 &&
            record.centerData.filter(item => item.orderNumber == 0).length > 0">查看详情</a>
              <div class="grey" v-else>查看详情</div>
            </template>

            <template
              slot="testProject"
              slot-scope="text, record, index, columns">
              <a @click="checkCalendarReportByRecord(record)"  style="text-align: left" :title="text" >{{text}}</a>
            </template>

            <template
              slot="collect"
              slot-scope="text, record, index, columns">

              <a-popconfirm
                v-if="text != null"
                title="确定取消收藏吗?"
                ok-text="确定"
                cancel-text="关闭"
                placement="topRight"
                @confirm="cancelCollect(record)"
              >
                <a-icon type="star" style="font-size: 14px;color:#0b57d0" theme="filled" />
              </a-popconfirm>

              <a-icon type="star" v-else theme="outlined" style="font-size: 14px" @click="collectProgress(record)"/>
            </template>

            <!--<template
              v-for="(p, idx) in headData"
              :slot="'data['+idx+'].inDate'"
              slot-scope="text, record, index, columns"
            >
              &lt;!&ndash;<div v-if="(record.type == 'seven' && record.data[idx].day == 28) ||
               (record.type == 'twenty-eight' && (record.data[idx].day == 7 || record.data[idx].day == 14)) ||
               (record.type == 'thirty' && (record.data[idx].day == 7 || record.data[idx].day == 14 || record.data[idx].day == 28)) ">
                /
              </div>&ndash;&gt;
              <div v-if="record.data.length < idx+1"
                   style="background-color: rgba(223, 223, 223, 0.25);width:100%;height:100%"></div>

              <a-date-picker v-else :allowClear="false" placeholder=""
                             :value="null != record.data[idx].inDate ?moment(record.data[idx].inDate, 'YYYY-MM-DD'):null"
                             @change="changeDate($event,record.data[idx])">
              </a-date-picker>


            </template>-->

          </s-table>

        </div>
      </div>
      <test-add-form ref="testAddForm" @ok="handleOk" :address="address"/>
      <test-edit-form ref="testEditForm" @ok="handleOk"/>
      <test-edit-actual-form ref="testEditActualForm" @ok="handleOk"/>
      <test-center-add-form ref="testCenterAddForm" @ok="handleOk"/>
      <test-center-see-form ref="testCenterSeeForm" @ok="handleOk"/>
      <test-center-see-all-form ref="testCenterSeeAllForm" @ok="handleOk"/>
      <test-edit-data-select ref="testEditDataSelect" @ok="handleOk"/>
      <choose-lims-ord-task ref="chooseLimsOrdTask" @ok="handleOk"/>
      <edit-battery-status ref="editBatteryStatus" @ok="handleOk"/>

      <a-modal
        title="传阅"
        :width="500"
        :visible="shareVisible"
        @cancel="() => shareVisible = false"
      >
        <div class="share-modal">
          <div class="title">已传阅人员</div>
          <div class="action-content mt16">
            <a-input v-model="shareUserParam.userName" placeholder="请输入账号/姓名" allow-clear style="width: 200px" @change="getShareUserList(selectedRows[0].id)">
              <a-icon slot="prefix" type="search" />
            </a-input>
            <a-button type="primary"   style="display: ruby;" ghost @click="handleShowAddUser">新增传阅人员<a-icon type="plus"/></a-button>
          </div>

          <div class="mt16">
            <a-table
              :columns="shareUserColumns"
              :data-source="shareUserData"
              :rowKey="(record) => record.userAccount"
            >
              <span slot="action" slot-scope="text, record">
                  <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => shareUserDelete(record)">
                    <a>删除</a>
                  </a-popconfirm>
              </span>

            </a-table>
          </div>
        </div>

        <a-modal
          title="新增传阅人员"
          :visible="isShowAddUser"
          :width="528"
          centered
          @cancel="() => isShowAddUser = false"
        >
          <div class="share-modal">
            <div class="action-content">
              <a-input v-model="addUserParam.account" placeholder="请输入账号/姓名" allow-clear style="width: 200px" @change="getUserList">
                <a-icon slot="prefix" type="search" />
              </a-input>
              <treeselect
                v-model="addUserParam.grantOrgIdList"
                placeholder="请选择部门"
                value-consists-of="BRANCH_PRIORITY"
                :limit="1"
                :multiple="true"
                :max-width="270"
                :options="orgOptions"
                @input="getUserList"
              />
            </div>
            <div class="mt16">
              <a-table
                :columns="addUserColumns"
                :dataSource="addUserData"
                :rowKey="(record) => record.account"
                :rowSelection="{ selectedRowKeys: userSelectedRowKeys, onChange: onUserSelectChange }"
                :pagination="userPagination"
                @change="handleUserTableChange"
              >
              </a-table>
            </div>
          </div>
          <template slot="footer">
            <a-button key="confirm" type="primary" @click="addShareUsers(selectedRows[0].id)">
              <a>传阅</a>
            </a-button>
            <a-button key="back" @click="() => isShowAddUser = false">
              <a>取消传阅</a>
            </a-button>
          </template>
        </a-modal>

        <template slot="footer">
          <a-button key="back" @click="() => shareVisible = false">
            <a>关闭</a>
          </a-button>
        </template>
      </a-modal>

    </div>
  </div>


</template>
<script>
import {
  testProgressAdd,
  testProgressList,
  testProgressUpdate,
  testProgressUpdateOnlyBean,
  testProgressUpdateData,
  testProgressListPage,
  testProgressImport,
  testProgressExport,
  validExportSizeOriData,
  exportHandleResult,
  exportSizeOriData,
  clearCalendarTestData,
  getBatteryInfoById,
  exportSizeTemplate,
  getAllTestProgressDetail,
} from '@/api/modular/system/testProgressManager'
  import {
    tLimsFolderListPage
  } from '@/api/modular/system/limsManager'
  import testAddForm from './testAddForm'
  import testEditForm from './testEditForm'
  import testEditDataSelect from './testEditDataSelect'
  import testEditActualForm from './testEditActualForm'
  import testCenterAddForm from './testCenterAddForm'
  import testCenterSeeForm from './testCenterSeeForm'
  import testCenterSeeAllForm from './testCenterSeeAllForm'
  import chooseLimsOrdTask from './chooseLimsOrdTask'
  import editBatteryStatus from "@/views/system/testProgress/editBatteryStatus.vue"
  import moment from 'moment';
  import { STable } from '@/components'
  import Vue from "vue";
  import { ACCESS_TOKEN } from "@/store/mutation-types";
import { downloadfile1 } from "@/utils/util";
import { mapGetters } from "vuex";

import {
  testReportShareAdd, testReportShareDelete,
  testReportShareList,testReportShareTop
} from '@/api/modular/system/reportManager'
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css"
import {getUserPage} from "@/api/modular/system/userManage";
import {getOrgTree} from "@/api/modular/system/orgManage";
import pbiTabs from "@/components/pageTool/components/pbiTabs.vue";


  export default {
    components: {
      pbiTabs,
      Treeselect,
      editBatteryStatus,
      testEditDataSelect,
      testCenterSeeAllForm,
      chooseLimsOrdTask,
      testAddForm, moment, testEditForm,STable,testEditActualForm,testCenterAddForm,testCenterSeeForm
    },
    data() {
      return {
        laboratoryList:[
          {value:'collect',label:"收藏"},
          {value:'all',label:"全部"},
          {value:"hz",label:"第六实验室(HZ)",show: this.hasPerm('progress:all') || this.hasPerm('progress:a1_3f') || this.hasPerm('progress:r2_2f') || this.hasPerm('progress:r4_4f')},
          {value:"JM",label:"第六实验室(JM)",show: this.hasPerm('progress:all') || this.hasPerm('progress:jm')},
          {value:"R3",label:"第四实验室",show: this.hasPerm('progress:all') || this.hasPerm('progress:r3')},
        ],
        orgOptions:[],
        shareUserParam:{},
        shareVisible:false,
        isShowAddUser:false,
        userSelectedRowKeys:[],
        userSelectedRows:[],
        addUserData:[],
        shareUserData:[],
        addUserParam:{
          grantOrgIdList:[]
        },
        shareUserColumns:[
          {
            title: '账号',
            dataIndex: 'userAccount',
            align:'center'
          },
          {
            title: '姓名',
            dataIndex: 'userName',
            align:'center'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:'center',
            scopedSlots: { customRender: 'action' }
        }],
        userPagination:{
          current:1,
          pageSize:10,
          total:0,
          size:"small"
        },
        addUserColumns: [

          {
            title: '账号',
            dataIndex: 'account',
            align:'center'
          },
          {
            title: '姓名',
            dataIndex: 'name',
            align:'center'
          }
        ],
        // 筛选框
        needData:false,
        operateData:[
          {
            label:'检测实验室',
            dataIndex:'testAddressSix',
            type:'select',
            tab:'hz',
            selectOption:[
              {
                label:'动力电池检测室',
                value:'R4_4F'
              },
              {
                label:'材料验证检测室',
                value:'R2_2F'
              },
              {
                label:'V圆柱检测室',
                value:'A1_3F'
              }
            ],
          },{
            label:'检测实验室',
            dataIndex:'testAddressAll',
            type:'select',
            tab:'all',
            selectOption:[
              {
                label:'第六实验室(HZ)',
                value:'hz'
              },
              {
                label:'第六实验室(JM)',
                value:'JM'
              },
              {
                label:'第四实验室',
                value:'four'
              }
            ],
          },
          {
            label:'测试申请单',
            dataIndex:'testCode',
            type:'input'
          },
          {
            label:'测试状态',
            dataIndex:'status',
            type:'multipleSelect',
            selectOption:[
              {
                label:'Plan',
                value:'Plan'
              },
              {
                label:'Ongoing',
                value:'Ongoing'
              },
              {
                label:'Done',
                value:'Done'
              },
              {
                label:'Stop',
                value:'Stop'
              },
            ]
          },
          {
            label:'申请人',
            dataIndex:'applicant',
            type:'input'
          },
          {
            label:'测试技师',
            dataIndex:'testMan',
            type:'input'
          },
          {
            label:'产品名称',
            dataIndex:'productName',
            type:'input'
          },
          {
            label:'部门',
            dataIndex:'dept',
            type:'input'
          },
          {
            label:'电芯载体',
            dataIndex:'sampleType',
            type:'select',
            selectOption:[
              {
                label:'G圆柱',
                value:'G圆柱'
              },
              {
                label:'C圆柱',
                value:'C圆柱'
              },
              {
                label:'V圆柱',
                value:'V圆柱'
              },
              {
                label:'方型',
                value:'方型'
              },
              {
                label:'软包',
                value:'软包'
              }
            ]
          },
          {
            label:'T/℃',
            dataIndex:'t',
            type:'input'
          },
          {
            label:'测试项目',
            dataIndex:'testProject',
            type:'input'
          },
          {
            label:'样品阶段',
            dataIndex:'productSampleStage',
            type:'input'
          },
          {
            label:'测试地点',
            dataIndex:'allAddress',
            type:'select',
            selectOption:[
              {
                label:'V圆柱检测室',
                value:'A1_3F'
              },
              {
                label:'材料验证检测室',
                value:'R2_2F'
              },
              {
                label:'动力电池检测室',
                value:'R4_4F'
              },
            ]
          },
          {
            label:'存储位置',
            dataIndex:'saveAddress',
            type:'input'
          },
          {
            label:'出箱日期',
            dataIndex:'out',
            type:'select',
            selectOption:[
              {
                label:'今天',
                value:'today'
              },
              {
                label:'0~2天',
                value:'two'
              }
            ],
          },
          {
            label:'出箱范围',
            dataIndex:'rangeBegin',
            type:'rangePicker'
          },
        ],
        loadData: parameter => {

          if(this.address != null && this.address == 'lims'){
            this.queryParam.testAddress = null
            this.queryParam.source = 'lims'
            this.queryParam.statusList = ['Plan']
          } else if(this.address != null && this.address != 'all'){
            this.queryParam.testAddress = this.address
            this.queryParam.source = null
            this.queryParam.statusList = this.status
          }else{
            this.queryParam.testAddress = this.allAddress
            this.queryParam.source = null
            this.queryParam.statusList = this.status
          }




          return testProgressListPage(Object.assign(parameter, this.queryParam) ,this.needData).then((res) => {
            //收藏页为空
            if(this.queryParam.testAddress == 'collect' && res.data.rows.length == 0){
              if(this.hasPerm('progress:all')){
                this.queryParam.testAddress = null
                this.address = 'all'
              }else if( this.hasPerm('progress:a1_3f') || this.hasPerm('progress:r2_2f') || this.hasPerm('progress:r4_4f')){
                this.queryParam.testAddress = 'hz'
                this.address = 'hz'
              }else if( this.hasPerm('progress:jm') ){
                this.queryParam.testAddress = 'JM'
                this.address = 'JM'
              }else if( this.hasPerm('progress:r3') ){
                this.queryParam.testAddress = 'R3'
                this.address = 'R3'
              }
              return testProgressListPage(Object.assign(parameter, this.queryParam) ,this.needData).then((res) => {
                return res
              })
            }else{
              return res
            }


          }).then((res) => {


            this.selectedRowKeys = []
            this.selectedRows = []

            this.data = res.data.rows
            this.height = 32*res.data.rows.length +70

            if(this.needData){
              for (let i = 0; i < this.data.length; i++) {
                if (this.data[i].data.length > this.headData.length) {
                  this.headData = this.data[i].data
                }
              }
            }

            this.columns1=[]
            this.columns = [
              {
                title: '序号',
                dataIndex: 'index',
                align: 'center',
                width: 50,
                fixed:'left',
                customRender: (text, record, index) => `${index + 1}`
              }, {
                title: '收藏',
                width: 50,
                fixed:'left',
                align: 'center',
                dataIndex: 'collectId',
                scopedSlots: { customRender: 'collect' },
              },{
                title: '测试状态',
                width: 70,
                fixed:'left',
                align: 'center',
                dataIndex: 'testStatus',
                ellipsis:true,
              }, {
                title: '测试申请单',
                width: 110,
                fixed:'left',
                align: 'center',
                ellipsis:true,
                dataIndex: 'testCode',
              }, {
                title: '电芯载体',
                width: 70,
                ellipsis:true,
                align: 'center',
                fixed:'left',
                dataIndex: 'sampleType',
              }, {
                title: '样品类型',
                width: 110,
                ellipsis:true,
                align: 'center',
                fixed:'left',
                dataIndex: 'sampleOrderType',
                customRender: (text, record, index) => {
                  switch (text) {
                    case "dx":
                      return "电芯";
                    case "mz":
                      return "模组";
                    case "dxlc":
                      return "电芯-Live Cell";
                    case "dxdc":
                      return "电芯-Dummy Cell";
                    default:
                      return "";
                  }
                }
              }, {
                title: '产品名称',
                width: 100,
                fixed:'left',
                align: 'center',
                dataIndex: 'productName',
              }, {
                title: '产品样品阶段',
                width: 90,
                fixed:'left',
                align: 'center',
                ellipsis:true,
                dataIndex: 'productSampleStage',
              }, {
                title: '测试类型',
                width: 100,
                ellipsis:true,
                align: 'center',
                dataIndex: 'testType',
              }, {
                title: '申请部门',
                width: 100,
                align: 'center',
                ellipsis:true,
                dataIndex: 'dept',
              }, {
                title: '申请人',
                width: 60,
                align: 'center',
                ellipsis:true,
                dataIndex: 'applicant',
              }, {
                title: '测试项目',
                width: 150,
                ellipsis:true,
                align: 'left',
                dataIndex: 'testProject',
                scopedSlots: { customRender: 'testProject' },
              }, {
                title: '测试项目别名',
                width: 100,
                align: 'center',
                ellipsis:true,
                dataIndex: 'testAlias',
              },  {
                title: '测试目的',
                align: 'center',
                dataIndex: 'testPurpose',
                ellipsis:true
              }, {
                title: 'T/℃',
                width: 50,
                align: 'center',

                dataIndex: 't',
              }

            ]

            if(this.address == 'R3'){
              this.columns.push(
                {
                  title:'湿度/%RH',
                  width: 100,
                  dataIndex:'humidity',
                  align: 'center',
                }
              )

            }


            this.columns.push(
              {
                title: 'SOC',
                width: 50,
                align: 'center',

                dataIndex: 'soc',
              }, {
                title: '计划测试天数',
                width: 100,
                align: 'center',

                dataIndex: 'testPeriod',
                /*customRender: (text, record, index) => {
                  return text?text + '天':''
                }*/
              },{
                title: '实际测试天数',
                width: 100,
                align: 'center',
                dataIndex: 'finishDay',
                /*customRender: (text, record, index) => {
                  return null != text ? text + '天' : text
                }*/
              }, {
                title: '数量',
                width: 50,
                align: 'center',

                dataIndex: 'quantity',
              }, {
                title: '测试技师',
                width: 70,
                align: 'center',
                ellipsis:true,
                dataIndex: 'testMan',
              }, {
                title: '测试地点',
                width: 100,
                align: 'center',
                dataIndex: 'testAddress',
                customRender: (text, record, index) => {
                  if(text == 'A1_3F'){
                    return 'V圆柱检测室'
                  }
                  if(text == 'R2_2F'){
                    return '材料验证检测室'
                  }
                  if(text == 'R4_4F'){
                    return '动力电池检测室'
                  }
                  if(text == 'R3'){
                    return '第四实验室'
                  }

                }
              },{
                width: 100,
                align: 'center',
                dataIndex: 'saveAddress',
                ellipsis:true,
                slots:{title:'saveAddressTitle'}
              }
            )

            if(this.needData == true ){this.columns.push({
                title: '在测数量',
                width: 70,
                align: 'center',
                dataIndex: 'testingQuantity',
              },{
              title: '中检次数',
              width: 70,
              align: 'center',

              dataIndex: 'data',
              customRender: (text, record, index) => {
                return text?text.length:null
              }
            }, {
              title: '进箱开始时间',
              width: 100,
              align: 'center',
              dataIndex: 'zero',
              customRender: (text, record, index) => {
                if(!record.data)return null
                return record.data.length > 0?record.data[0].actualInDate != null?record.data[0].actualInDate:
                  record.data[0].inDate:text
              }
            })}


            if (this.needData == true && this.data.length > 0) {
              for (let i = 0; i < this.headData.length; i++) {
                this.columns.push({
                  //title: '第' + res.data[0].data[i].day + '天',
                  title: i == 0 ? '初始阶段':'存储第' +i+ '阶段',
                  align: 'center',

                  children: [
                    {
                      title: '存储天数',
                      width: 80,
                      align: 'center',
                      customRender: (text, record, index) => {
                        //<div v-if="record.data.length < idx-1" style="background-color: rgba(223, 223, 223, 0.25)"></div>
                        if (record.data && record.data.length < i + 1) {
                          return ( < div
                          style = "background-color: rgba(223, 223, 223, 0.25);width:100%;height:100%" > < /div>)
                        }

                        return ( < div
                        style = "background-color: rgba(223, 223, 223, 0.25);width:100%;height:100%;display: flex;align-items: center;justify-content: center;" >
                          {
                            text
                          }
                        天 < /div>)
                      },
                      dataIndex: 'data[' + i + '].day',
                    }, {
                      title: '开始时间',
                      width: 80,
                      align: 'center',
                      /*scopedSlots: {
                        customRender: 'data[' + i + '].inDate'
                      },*/
                      customRender: (text, record, index) => {

                        /*if(record.data[i].actualInDateStatus == 1){
                          return ( < div
                        class
                          = 'green' > {text} < /div>)
                        }
*/
                        if(record.data && record.data.length > i+1 && record.data[i].actualInDate != null){
                            return (<div class='grey'>{record.data[i].actualInDate}</div>)
                        }else if(record.data && record.data.length < i+1){
                          return (<div class='grey'></div>)
                        }else{
                          return (<div class='grey'>{text}</div>)
                        }

                      },
                      dataIndex: 'data[' + i + '].inDate',
                    }, {
                      title: '结束时间',
                      width: 80,
                      align: 'center',
                      dataIndex: 'data[' + i + '].outDate',
                      customRender: (text, record, index) => {
                        if (record.data && record.data.length < i + 1) {
                          return ( < div
                          style = "background-color: rgba(223, 223, 223, 0.25);width:100%;height:100%" > < /div>)
                        }
                        if (null != text) {
                          var m1 = moment(text, 'YYYY-MM-DD')
                          var m2 = moment(new Date(), 'YYYY-MM-DD')
                          if (m1.date() == m2.date() && m1.month() === m2.month() && m1.year() === m2.year()) {
                            return ( < div
                          class
                            = 'red' > {text} </div>)
                          } else if (m1.month() === m2.month() && m1.year() === m2.year() && m1.date() - m2.date() <= 2 && m1.date() - m2.date() >= 0) {
                            return ( < div
                          class
                            = 'yellow' > {text} </div>)
                          } else {
                            return ( < div
                          class
                            = 'grey' > {text} < /div>)
                          }
                        }

                        if ((record.type == 'seven' && record.data[i].day == 28) ||
                          (record.type == 'twenty-eight' && (record.data[i].day == 7 || record.data[i].day == 14)) ||
                          (record.type == 'thirty' && (record.data[i].day == 7 || record.data[i].day == 14 || record.data[i].day == 28))
                        ) {
                          return ( <div> / </div>)

                        }


                      }
                    }
                  ]
                })
              }
            }
            return res.data
          })
        },

        status:['Ongoing','Plan'],
        address: 'collect',
        show: false,
        labelCol: {

          sm: {
            span: 11
          }
        },
        wrapperCol: {

          sm: {
            span: 13
          }
        },
        queryParam: {},
        data: [],
        headData: [],
        allAddress:null,
        // 表头
        columns: [],
        columns1:[],
        selectedRowKeys: [],
        selectedRows: [],
        height:'500px',
        deleteVisible:false,
        clearDataVisible:false,
        importVisible:false,
        importParam: {},
        fileList: [],
        isUpload:false,
        headers: {
          Authorization: "Bearer " + Vue.ls.get(ACCESS_TOKEN)
        }
      }
    },
    created() {
    },
    computed: {
      ...mapGetters(['userInfo']),
      rowSelection(){
        return {
          columnWidth: 30,
          type:'radio',
          selectedRowKeys: this.selectedRows.map((row) => row.id),
          onChange: (selectedRowKeys, selectedRows) => {
            this.selectedRows = selectedRows
            this.isUpload = true
            this.importParam.id = this.selectedRows[0].id
            this.importParam.testCode = this.selectedRows[0].testCode
          }
        }
      },
    },
    mounted() {
      //this.dragControllerDiv()
      /*if(this.hasPerm('progress:all')){
        this.address = 'all'
      }else if(this.hasPerm('progress:a1_3f')){
        this.address = 'A1_3F'
      }else if(this.hasPerm('progress:b4_3f')){
        this.address = 'B4_3F'
      }else if(this.hasPerm('progress:r4_3f_v')){
        this.address = 'R4_3F_V圆柱'
      }else if(this.hasPerm('progress:r4_3f_cc')){
        this.address = 'R4_3F_动力电池'
      }else if(this.hasPerm('progress:r4_4f')){
        this.address = 'R4_4F'
      }else if(this.hasPerm('progress:r4_5f')){
        this.address = 'R4_5F'
      }else{*/
        this.address = 'collect'
      // }

      this.$nextTick(() =>{
        this.getList()

      })

    },
    methods: {
      getShareUserList(recordId,first,record){
        if(first){
          this.checkRecord = record
        }
        this.shareUserParam.reportId = recordId
        testReportShareList(this.shareUserParam).then(res => {
          this.shareUserData = res.data
          this.shareVisible = true
        })
      },
      shareUserDelete (record) {
        testReportShareDelete({id:record.id}).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.getShareUserList(record.reportId)
          } else {
            this.$message.error('删除失败：' + res.message)
          }
        }).catch((err) => {
          this.$message.error('删除错误：' + err.message)
        })
      },
      addShareUsers(reportId){
        for (let i = 0; i < this.userSelectedRows.length; i++) {
          testReportShareAdd({reportId:reportId,
            userAccount:this.userSelectedRows[i].account,userName:this.userSelectedRows[i].name}).then((res) => {
            if (res.success) {
              this.$message.success('传阅成功')
              this.isShowAddUser = false
              testReportShareList(this.shareUserParam).then(res => {
                this.shareUserData = res.data
              })
            } else {
              this.$message.error('传阅失败：' + res.message)
            }
          }).catch((err) => {
            this.$message.error('传阅失败：' + err.message)
          })
        }

      },
      onUserSelectChange(keys,rows){
        this.userSelectedRowKeys = keys
        this.userSelectedRows = rows
      },
      handleShowAddUser(){
        this.isShowAddUser = true
        this.getUserList()
        this.getOrgList()
      },
      handleUserTableChange(pagination, filters, sorter) {
        this.userPagination.current = pagination.current

        this.getUserList()
      },
      getOrgList(){
        getOrgTree({}).then(res => {
          if(res.success){
            this.orgOptions = []
            res.data[0].children.forEach(v => {
              let $item = {
                id: v.id,
                label:v.title
              }
              if(v.children.length !== 0){
                $item.children = []
                v.children.forEach(chilV => {
                  $item.children.push({
                    id:chilV.id,
                    label:chilV.title
                  })
                })
              }
              this.orgOptions.push($item)
            })
          }else{
            this.$message.error('部门信息查询失败：' + res.message)
          }
        })
      },
      getUserList(){
        const params = {
          pageNo:this.userPagination.current,
          pageSize:this.userPagination.pageSize,
          searchValue:this.addUserParam.account || '',
          grantOrgIdList: this.addUserParam.grantOrgIdList.join(','),
        }

        getUserPage(params).then(res => {
          if (res.success) {
            this.userSelectedRows = []
            this.userSelectedRowKeys = []
            this.userPagination.total = res.data.totalRows
            for (let i = 0; i < res.data.rows.length; i++) {
              if(this.shareUserData.find(s => s.userAccount == res.data.rows[i].account) != null){
                this.userSelectedRows.push(res.data.rows[i])
                this.userSelectedRowKeys.push(res.data.rows[i].account)
              }
            }
            this.addUserData = res.data.rows
            this.$forceUpdate()
          } else {
            this.$message.error('查询失败：' + res.message)
          }

        })

      },
      deleteBefore() {
        if(this.selectedRows.length == 0){
          this.$message.warn('请至少选中一条数据')
          return
        }else{
          this.deleteVisible = true
        }
      },
      dataRefresh(){
        this.needData = !this.needData
        this.getList(false)

      },
      importBefore() {
        if(this.selectedRows.length == 0){
          this.$message.warn('请至少选中一条数据')
          return
        }else{
          this.importVisible = true
        }
      },
      importLims() {
        let select = this.selectedRows

        for (let i = 0; i < select.length; i++) {
          testProgressUpdateOnlyBean({id: select[i].id, source: 'lims_import',testStatus:'Ongoing'})
        }
        this.getList()
        this.selectedRows = []
        this.selectedRowKeys = []
        this.importVisible = false


      },
      changeStatus(a){

        this.queryParam.statusList = a

        this.$refs.table2.refresh()
      },
      rangeChange(a,b){


        this.queryParam.rangeBegin = b[0]
        this.queryParam.rangeEnd = b[1]

        this.$refs.table2.refresh()
      },
      checkCenter(record,text){
        return record.centerData.filter(item => item.orderNumber == text.orderNumber).length > 0
      },

      scrollTo(index){
        document.getElementsByClassName("ant-table-body")[0].scrollTop = 25*index.target.value
      },
      moment,
      customRequest (data) {
        this.loading = true
        const formData = new FormData()
        formData.append('file', data.file)
        testProgressImport(formData).then((res) => {
          if (res.success) {
            this.$message.success('导入成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('上传失败：' + res.message)
          }
          this.loading = false
        }).finally(() => {
          this.loading = false
        })
      },
      customRow(record){
        return {
          props: {

          },
          on: { // 事件
            click: (event) => {
              this.selectedRows = []
             /* if (this.selectedRows.includes(record)) {
                this.selectedRows = this.selectedRows.filter((r) => r != record);
              } else {*/
                this.selectedRows.push(record);
                this.isUpload = true
                this.importParam.id = this.selectedRows[0].id
                this.importParam.testCode = this.selectedRows[0].testCode
              //}
            },
          },

        };
      },

      onRowClick(row) {
        alert(row)
        if (this.selectedRows.includes(row)) {
          this.selectedRows = this.selectedRows.filter((r) => r !== row);
        } else {
          this.selectedRows.push(row);
        }
      },
      handleOk() {
        this.getList()
      },
      changeDate(event, record) {

        testProgressUpdateData({
          id: record.id,
          inDate: moment(event).format('YYYY-MM-DD')
        }).then().finally(res => this.getList())


      },
      openAdd() {
        this.$refs.testAddForm.add()
      },
      deleteRecord() {
        let select = this.selectedRows

        for (let i = 0; i < select.length; i++) {
          testProgressUpdateOnlyBean({id: select[i].id, status: 1})
        }
        this.getList()
        this.selectedRows = []
        this.selectedRowKeys = []
        this.deleteVisible = false
      },
      openEdit() {

        let num = this.selectedRows.length


        if (num != 1) {
          this.$message.warn('请选中一条数据编辑')
          return
        } else {

          this.$refs.testEditForm.edit(this.data.filter(d => d.id == this.selectedRows[0].id)[0])
        }

      },
      openEditDataSelect() {

        let num = this.selectedRows.length


        if (num != 1) {
          this.$message.warn('请选中一条数据编辑')
          return
        } else {
          this.$refs.testEditDataSelect.edit(this.data.filter(d => d.id == this.selectedRows[0].id)[0])
        }

      },
      clearTestDataBefore() {
        if (this.selectedRows.length == 0) {
          this.$message.warn('请选中一条数据清空')
        } else {
          this.clearDataVisible = true
        }
      },
      clearTestData() {
        clearCalendarTestData({ id: this.selectedRows[0].id }).then((res) => {
          if (res.success) {
            this.$message.success('清空测试数据成功')
          } else {
            this.$message.error('清空测试数据失败：' + res.message)
          }
          this.clearDataVisible = false
        })
      },
      cancelCollect(record) {
        testReportShareDelete({ id: record.collectId }).then((res) => {
          if (res.success) {
            this.$message.success('取消收藏成功')
          }
        }).finally(() => {
          this.getList()
        })
      },
      editBatteryStatus() {
        if (this.selectedRows.length !== 1) {
          this.$message.warn('请选择一条数据！')
          return
        }
          this.$refs.editBatteryStatus.open(this.selectedRows[0].id)
      },
      openChooseOrdTask() {

        let num = this.selectedRows.length


        if (num != 1) {
          this.$message.warn('请选中一条数据编辑')
          return
        } else {

          this.$refs.chooseLimsOrdTask.open(this.data.filter(d => d.id == this.selectedRows[0].id)[0])
        }

      },
      openAllSee() {

        let num = this.selectedRows.length


        if (num != 1) {
          this.$message.warn('请选中一条数据编辑')
          return
        } else {

          this.$refs.testCenterSeeAllForm.add(this.data.filter(d => d.id == this.selectedRows[0].id)[0])
        }

      },

      openCenter(detailId,id) {

        this.$refs.testCenterSeeForm.add(detailId,id)


      },
      openCenterAdd() {

        let num = this.selectedRows.length


        if (num != 1) {
          this.$message.warn('请选中一条数据')
          return
        } else {

          this.$refs.testCenterAddForm.add(this.data.filter(d => d.id == this.selectedRows[0].id)[0])
        }

      },
      openEditActual() {

        let num = this.selectedRows.length


        if (num != 1) {
          this.$message.warn('请选中一条数据编辑')
          return
        } else {
          this.$refs.testEditActualForm.edit(this.data.filter(d => d.id == this.selectedRows[0].id)[0])
        }

      },
      exportData() {
          testProgressExport({testAddress:this.address}).then(response => {

            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', '存储信息表格导出.xls'); // 设置下载文件的名称和扩展名
            document.body.appendChild(link);
            link.click();

        })

      },
      changeTab(value){
        this.address = value
        this.queryParam.testAddressAll = null
        this.queryParam.testAddressSix = null
        this.getList()
      },
      getList(flag) {

        if(flag==true){
          this.queryParam = {}
          this.allAddress = null
          this.queryParam.rangeBegin = null
          this.queryParam.rangeEnd = null
          this.queryParam.range = null
          this.status = []
        }


        this.$refs.table2.refresh()
      },
      importTestData(info) {
        this.fileList = [...info.fileList]
        if (info.file.response.success) {
          this.$message.success(`${info.file.name} 导入数据成功`)
        } else {
          this.$message.error(`${info.file.name} 导入数据失败:` + info.file.response.message)
        }
      },
      beforeUploadCheck() {
        if (this.selectedRows.length === 0) {
          this.$message.warn('请至少选中一条数据')
        }
      },
      shareOut() {
        if (this.selectedRows.length === 0) {
          this.$message.warn('请至少选中一条数据')
        }
        this.shareUserParam.reportId = this.selectedRows[0].id
        testReportShareList(this.shareUserParam).then(res => {
          this.shareUserData = res.data
          this.shareVisible = true
        })

      },
      shareTop() {
        if (this.selectedRows.length === 0) {
          this.$message.warn('请至少选中一条数据')
        }
        testReportShareTop({reportId:this.selectedRows[0].id,type:'processTop'}).then(res => {
          this.getList()
        })

      },
      validExportSizeOriData(exportType) {
        return validExportSizeOriData({testProgressId: this.selectedRows[0].id, exportType: exportType}).then(res => {
          return Promise.resolve(res);
        })
      },
      exportHandleResult() {
        if (this.selectedRows.length !== 1) {
          this.$message.warning('请选择一条数据')
          return
        }
        let checkAllDataFlag = this.userInfo.roles.filter(item => item.code === "check_all_calendar_report")
        // 管理员或者有【查看所有日历寿命测试项目报告】角色的用户
        if (this.userInfo.account === "superAdmin" || checkAllDataFlag.length > 0 || this.userInfo.name === this.selectedRows[0].applicant || this.userInfo.account == this.selectedRows[0].applicantAccount) {
          this.validExportSizeOriData("handleResult").then(data => {
            if (data.success) {
              exportHandleResult({ testProgressId: this.selectedRows[0].id, alias: this.selectedRows[0].testAlias }).then(res => {
                var alias;
                if (this.selectedRows[0].testAlias) {
                  alias = this.selectedRows[0].testAlias
                } else {
                  alias = this.selectedRows[0].testProject
                }
                var fileName = this.selectedRows[0].testCode + '-' + this.selectedRows[0].applicant + '-' + alias + '-' + '处理结果' + '.xlsx'
                if (res) {
                  downloadfile1(res, fileName)
                }
              })
            } else {
              this.$message.warning(data.message)
            }
          })
        } else {
          this.$message.warning('无权限导出其他申请人的处理结果！')
        }
      },
      exportSizeTemplate() {
        if (this.selectedRows.length !== 1) {
          this.$message.warning('请选择一条数据')
          return
        }
        if (!this.selectedRows[0].cellTestCodes) {
          this.$message.warning('测试编码不存在！请联系管理员')
          return
        }
        let cellTestCodeList = JSON.parse(this.selectedRows[0].cellTestCodes)
        if (cellTestCodeList.length === 0) {
          this.$message.warning('测试编码不存在！请联系管理员')
          return
        }
        getAllTestProgressDetail({ id: this.selectedRows[0].id }).then((res) => {
          if (res.success) {
            let allStageList = res.data
            if (allStageList.findIndex(v => v.height === '1') === -1) {
              this.$message.error('当前测试项目所有存储阶段都未勾选尺寸，请确认')
            } else {
              exportSizeTemplate(this.selectedRows[0].id).then(res => {
                var fileName = this.selectedRows[0].productName + '一' + this.selectedRows[0].testCode + '尺寸一' + this.selectedRows[0].applicant + '.xlsx'
                if (res) {
                  downloadfile1(res, fileName)
                }
              })
            }
          } else {
            this.$message.error('获取所有存储阶段信息失败!')
          }
        })
      },
      exportSizeOriData() {
        if (this.selectedRows.length !== 1) {
          this.$message.warning('请选择一条数据')
          return
        }
        let checkAllDataFlag = this.userInfo.roles.filter(item => item.code === "check_all_calendar_report")
        // 管理员或者有【查看所有日历寿命测试项目报告】角色的用户
        if (this.userInfo.account === "superAdmin" || checkAllDataFlag.length > 0 || this.userInfo.name === this.selectedRows[0].applicant || this.userInfo.account == this.selectedRows[0].applicantAccount) {
          this.validExportSizeOriData("sizeOriData").then(data => {
            if (data.success) {
              exportSizeOriData({ testProgressId: this.selectedRows[0].id, alias: this.selectedRows[0].testAlias }).then(res => {
                var fileName = this.selectedRows[0].testCode + '-' + this.selectedRows[0].applicant + '-' + this.selectedRows[0].testAlias + '-' + '尺寸原始数据' + '.xlsx'
                if (res) {
                  downloadfile1(res, fileName)
                }
              })
            } else {
              this.$message.warning(data.message)
            }
          })
        } else {
          this.$message.warning('无权限导出其他申请人的尺寸原始数据！')
        }
      },

      collectProgress(record){
        let param = {}
        param.reportId = record.id
        param.type = 'collect'
        param.userAccount = this.userInfo.account
        testReportShareAdd(param).then(res => {
          this.$message.success('收藏成功')
        }).finally(() => {
          this.getList()
        })
      },
      checkCalendarReportByRecord(record) {

        let checkAllDataFlag = this.userInfo.roles.filter(item => item.code === "check_all_calendar_report")

        // 管理员或者有【查看所有日历寿命测试项目报告】角色的用户
        if (this.userInfo.account === "superAdmin" || checkAllDataFlag.length > 0 || this.userInfo.name === record.applicant || this.userInfo.account == record.applicantAccount) {
          this.getCalendarReport(record)
        } else {
          tLimsFolderListPage({folderno:record.testCode}).then(res => {
            if(res.data.rows.length == 0){
              this.$message.warning('无权限查看其他申请人的报告！')
            }else{
              this.getCalendarReport(record)
            }

          })

        }
      },
      checkCalendarReport () {
        if (this.selectedRows.length !== 1) {
          this.$message.warning('请选择一条数据')
          return
        }
        let checkAllDataFlag = this.userInfo.roles.filter(item => item.code === "check_all_calendar_report")
        // 管理员或者有【查看所有日历寿命测试项目报告】角色的用户
        if (this.userInfo.account === "superAdmin" || checkAllDataFlag.length > 0 || this.userInfo.name === this.selectedRows[0].applicant || this.userInfo.account == this.selectedRows[0].applicantAccount) {
          this.getCalendarReport(this.selectedRows[0])
        } else {
          tLimsFolderListPage({folderno:this.selectedRows[0].testCode}).then(res => {
            if(res.data.rows.length == 0){
              this.$message.warning('无权限查看其他申请人的报告！')
            }else{
              this.getCalendarReport(this.selectedRows[0])
            }

          })
        }
      },
      getCalendarReport(record) {
        const id = record.id;
        const alias = record.testAlias ? record.testAlias : "";
        validExportSizeOriData({ testProgressId: id, exportType: "handleResult" }).then(res => {
          if (res.success) {
            const videoFlag = res.data.videoFlag === '1' ? 1 : 0
            const pictureFlag = res.data.pictureFlag === '1' ? 1 : 0
            // 有离线数据，通过res.data.resultDataJson判断是否有在线数据
            window.open("/v_report_preview?testProgressId=" + id + "&alias=" + encodeURIComponent(alias) + "&offFlag=1&onlineFlag=" + (res.data.resultDataJson ? "1" : "0")
              + '&videoFlag=' + videoFlag + '&pictureFlag=' + pictureFlag + "&type=日历寿命", "_blank")
          } else {
            // 无离线数据，未返回res.data.resultDataJson，获取testProgress进行判断
            // getOnlineReport({ id: id }).then(res => {
            //   if (res.success) {
            //     res.data.resultDataJson ?
            //     window.open("/v_report_preview?testProgressId=" + id + "&alias=" + encodeURIComponent(alias) + "&offFlag=0&onlineFlag=1" + "&type=日历寿命", "_blank") // 需要展示在线报告
            //     : this.$message.warning("没有可查看的数据")
            //   } else {
            //     this.$message.warning(res.message.replace("导出", "查看"))
            //   }
            // })

            // 无离线数据，通过record.resultDataJson判断在线数据
            if (record.resultDataJson) {
              window.open("/v_report_preview?testProgressId=" + id + "&alias=" + encodeURIComponent(alias) + "&offFlag=0&onlineFlag=1" + "&type=日历寿命", "_blank") // 需要展示在线报告
            } else {
              this.$message.warning(res.message.replace("导出", "查看"))
            }
          }
        })
      },
      dragControllerDiv () {
        var resize = document.getElementsByClassName('resize');
        console.log(resize)
        var left = document.getElementsByClassName('left');
        var mid = document.getElementsByClassName('mid');
        var box = document.getElementsByClassName('box');
        for (let i = 0; i < resize.length; i++) {
          // 鼠标按下事件
          resize[i].onmousedown = function (e) {
            //颜色改变提醒
            resize[i].style.background = '#818181';
            var startX = e.clientX;
            resize[i].left = resize[i].offsetLeft-220;
            // 鼠标拖动事件
            document.onmousemove = function (e) {
              var endX = e.clientX;
              var moveLen = resize[i].left + (endX - startX); // （endx-startx）=移动的距离。resize[i].left+移动的距离=左边区域最后的宽度
              var maxT = box[i].clientWidth - resize[i].offsetWidth; // 容器宽度 - 左边区域的宽度 = 右边区域的宽度

              if (moveLen < 32) moveLen = 32; // 左边区域的最小宽度为32px
              if (moveLen > maxT - 150) moveLen = maxT - 150; //右边区域最小宽度为150px

              resize[i].style.left = moveLen; // 设置左侧区域的宽度

              for (let j = 0; j < left.length; j++) {
                left[j].style.width = moveLen + 'px';
                mid[j].style.width = (box[i].clientWidth - moveLen - 10) + 'px';
              }
            };
            // 鼠标松开事件
            document.onmouseup = function (evt) {
              //颜色恢复
              resize[i].style.background = '#d6d6d6';
              document.onmousemove = null;
              document.onmouseup = null;
              resize[i].releaseCapture && resize[i].releaseCapture(); //当你不在需要继续获得鼠标消息就要应该调用ReleaseCapture()释放掉
            };
            resize[i].setCapture && resize[i].setCapture(); //该函数在属于当前线程的指定窗口里设置鼠标捕获
            return false;
          };
        }
      }


    }
  }
</script>
<style lang="less" scoped=''>
@import '/src/components/pageTool/style/pbiSearchItem.less';
  /deep/ .ant-table-thead > tr > th {
    padding: 5px!important;
    font-size: 13px!important;
    color: rgba(0, 0, 0, .85)!important;
    font-weight: 500!important;
    text-align: center !important;
  }

  /deep/ .ant-table-tbody > tr > td {
    padding: 4px!important;
    color: #333!important;
    font-size: 12px!important;
    font-weight: 400!important;
  }



  /deep/ .ant-calendar-picker-icon {
    display: none;
  }
  /deep/.ant-select-selection__rendered {
    margin-right: 0px;
  }

  /deep/ .ant-calendar-picker-input.ant-input {
    color: black;
    font-size: 12px;
    width: 100%;
    text-align: center;
    padding: 0;
  }
  /deep/ .ant-calendar-range-picker-separator {

     vertical-align: unset;

  }

  .red {
    background-color: #ed0000;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .yellow {
    background-color: #ffc000;
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .grey {
    background-color: rgba(223, 223, 223, 0.25);
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .ant-modal-body {
    padding: 0;
  }

  /deep/ .ant-col {
    padding: 0 !important;
    height: 40px !important;
  }

  /deep/.ant-btn > i, /deep/.ant-btn > span {
    display: flex;
    justify-content: center;
  }

  /deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
    color: #fff;
    background: #1890FF;
  }

  .green{
    background-color: #58a55c;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

  }

  /deep/#table1>div>div>div>div>div>div>table>thead{
    height: 64px;
  }

  /deep/#table1>.ant-table-wrapper>div>div>ul{
    display: none;
  }


  /deep/.ant-table-pagination.ant-pagination {
    float: right;
   margin: 0;
  }

  /* ************ */
  .operate-wrap {
    display: flex;
    justify-content: space-between;

    position: relative;

    overflow: hidden;
  }

  .operate-wrap .operate-left{
    width: 66%;

    display: flex;
    flex-wrap: wrap;

  }

  .operate-wrap .operate-center{
    width: calc(34% - 20px);

    display: flex;
    flex-wrap: wrap;
    // justify-content: flex-end;

    margin-left: 20px;
  }

  .operate-right{

    // font-size: 12px;
    cursor: pointer;
    margin-top:9px;

  }

  .operate-wrap .operate-block{
    display: flex;
    align-items: center;

    margin-bottom: 8px;
  }

  .operate-block .label{
    width: 70px;
    text-align: right;

    font-size: 12px;
    color: #333;
  }

  .operate-block .input-short{
    width: 120px;
  }

  .operate-block .input-long{
    width: 319px;
  }

  .operate-block .input-short{
    width: 120px;
  }

  /deep/.operate-block .ant-input{
    font-size: 12px;
  }
  /deep/.operate-block .ant-select{
    font-size: 12px;
  }
  /deep/.operate-center .ant-btn{
    padding: 0 8px;
    font-size: 12px;
  }




  /* ***************** */
  .mr8{
    margin-right: 8px;
  }
  .mb8{
    margin-bottom: 8px;
  }

  .share-modal .title{
    font-size: 16px;
    font-weight: 600;
    text-align: center;
  }
  .share-modal .action-content{
    display: flex;
    justify-content: space-between;
    align-items: center;

  }
  .mt16{
    margin-top: 16px;
  }

  /deep/.vue-treeselect{
    max-width: 270px;
  }
  /deep/.vue-treeselect--has-value .vue-treeselect__multi-value{
    margin-top: -3px;
    margin-bottom: 0;
  }
  /deep/.vue-treeselect__placeholder{
    line-height: 30px;
  }



</style>

<style lang='less'>
.dropdownClassName {
  .ant-select-dropdown-menu-item {
    text-align: center;
    padding: 0;
    font-size: 12px;
  }
}

</style>