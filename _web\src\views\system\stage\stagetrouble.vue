<template>
  <div style="background:#fff">
    <div style="text-align:right">
      <a-button type="primary" @click="add" style="margin:8px 4px">
        新增
      </a-button>
    </div>
    <div>
      <a-table :scroll="{ x: 1800}" size="small" style="background: #fff;" ref="table" :rowKey="(record) => record.issueId" :columns="columns" :dataSource="list" :loading="loading">
        <template slot="transitionBeanList" slot-scope="text,record">
          <a-popconfirm v-for="(item,i) in record.transitionBeanList" :key="i" style="margin-right:3px" placement="topRight" title="确认执行此操作？" @confirm="() => callTransiteStage(record,item.transitionId)">
            <a>{{item.transitionName}}</a>
          </a-popconfirm>
        </template>

        <span slot="clamptxt" slot-scope="text">
            <clamp :text="text" :sourceText="[text]" ></clamp>
        </span>

        <span slot="productProjectName">
          {{ projectdetail.productProjectName }}
        </span>
        <span slot="projectName">
          {{ projectdetail.projectName }}
        </span>
        <span slot="productStage" slot-scope="text">
                                    {{ 'product_stage_status' | dictType(text) }}
                                    </span>
        <span slot="problemLevel" slot-scope="text">
                                    {{ 'stage_problem_level' | dictType(text) }}
                                    </span>
        <span slot="problemStatus" slot-scope="text">
                                    {{ 'stage_problem_status' | dictType(text) }}
                                    </span>
        <span slot="problemCategories" slot-scope="text">
                                    {{ 'stage_problem_category' | dictType(text) }}
                                    </span>
        
        <span slot="problemDimension" slot-scope="text">
                                    {{ problemDimension[text] }}
                                    </span>

        <span slot="statusLamp" slot-scope="text">
            <div class="sateflex">
                <span :class="['state',text == 1 ? 'red' : (text == 2 ? 'yellow': (text == 3 ? 'green' : ''))]"></span>
            </div>
        </span>
        
        <span slot="action" slot-scope="text,record">
                  <a @click="$refs.editForm.edit(record)">编辑</a>
                  <a-divider type="vertical" />
                  <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => stageDelete(record)">
                    <a>删除</a>
                  </a-popconfirm>
                  <a-divider type="vertical" />
                  <a-dropdown>
                    <a class="ant-dropdown-link">
                      更多 <a-icon type="down" />
                    </a>
                    <a-menu slot="overlay">
                      <a-menu-item>
                        <a @click="handleToJira(record)">填写问题解决措施</a>
                      </a-menu-item>
                      <a-menu-item >
                        <a @click="handleToJira(record)">填写进展</a>
                      </a-menu-item>
                    </a-menu>
                  </a-dropdown>
                </span>
      </a-table>
    </div>
    <add ref="addForm" @ok="handleOk" :issueId="issueId" :projectdetail="projectdetail" />
    <edit ref="editForm" @ok="handleOk" :projectdetail="projectdetail"/>
  </div>
</template>

<script>
  import Vue from 'vue'
  import {
    getStageProblem,
    stageDel,
    transiteStage
  } from "@/api/modular/system/report"
  import {clamp} from '@/components'
  import add from './add'
  import edit from './edit'
  export default {
    components: {
      add,
      edit,
      clamp
    },
    props: {
      issueId: {
        type: Number,
        default: 0
      },
      projectdetail: {
        type: Object,
        default: {}
      }
    },
    data() {
      return {
        problemDimension:{
          1:'技术',
          2:'质量',
          3:'交付',
          4:'产业化'
        },
        windowHeight: document.documentElement.clientHeight - 315,
        list: [],
        columns: [
          {
            title: '产品名称',
            dataIndex: 'productProjectName',
            
            scopedSlots: {
              customRender: 'productProjectName'
            }
          },
          {
            title: "项目名称",
            align: "center",
            dataIndex: "projectName",
            scopedSlots: {
              customRender: 'projectName'
            }
          },
          {
            title: '阶段',
            dataIndex: 'productStage',
            
            scopedSlots: {
              customRender: 'productStage'
            }
          },
          /* {
            title: '客户',
            
            dataIndex: 'productCustomer'
          }, */
          {
            title: '提出时间',
            
            dataIndex: 'findDate'
          },
          {
            title: '状态灯',
            align: 'center',
            dataIndex: 'statusLamp',
            scopedSlots: {
              customRender: 'statusLamp'
            }
          },
          {
            title: '问题维度',
            align: 'center',
            dataIndex: 'problemDimension',
            scopedSlots: {
              customRender: 'problemDimension'
            }
          },
         /*  {
            title: '提出人',
            
            dataIndex: 'presenterName'
          }, */
          {
            title: '问题描述',
            dataIndex: 'problemDescription',
            scopedSlots: {
              customRender: 'clamptxt'
            }
            
          },
          {
            title: '原因分析',
            dataIndex: 'causeAnalysis',
            scopedSlots: {
              customRender: 'clamptxt'
            }
            
          },
          /* {
            title: '问题分类',
            
            dataIndex: 'problemCategories',
            scopedSlots: {
              customRender: 'problemCategories'
            }
          },
          {
            title: '问题等级',
            
            dataIndex: 'problemLevel',
            scopedSlots: {
              customRender: 'problemLevel'
            }
          }, */
          {
            title: '问题解决措施',
            dataIndex: 'problemSolving',
            scopedSlots: {
              customRender: 'clamptxt'
            }
            
          },
          {
            title: '问题进展',
            dataIndex: 'productProcess',
            scopedSlots: {
              customRender: 'clamptxt'
            }
            
          },
          {
            title: '责任人',
            
            dataIndex: 'responsiblePersonName'
          },
          /* {
            title: '问题跟进状态',
            
            dataIndex: 'problemStatus',
            scopedSlots: {
              customRender: 'problemStatus'
            }
          }, */
         /*  {
            title: '确认人',
            
            dataIndex: 'confirmedPersonName'
          }, */
          {
            title:'计划关闭时间',
            dataIndex:'plannedCompletionDate'
          },
          {
            title: '实际关闭时间',
            
            dataIndex: 'actualCompletionDate'
          },/* {
            title:'流程处理',
            dataIndex:'transitionBeanList',
            scopedSlots: {
              customRender: 'transitionBeanList'
            }
          }, */
          {
            title: '操作',
            dataIndex: 'action',
            width:200,
            scopedSlots: {
              customRender: 'action'
            }
          },
        ],
        loading: false,
      }
    },
    methods: {
      handleToJira(row) {
        let _key = row["issueKey"];
        if (!_key) {
          return;
        }
        let $url = `http://jira.evebattery.com/browse/${_key}?auth=` + Vue.ls.get("jtoken");
        window.open($url, "_blank");
      },
      stageDelete(record) {
        this.loading = true
        let param = {
          issueId: record.issueId
        }
        stageDel(param).then((res) => {
          this.loading = false
          if (res.result) {
            if (res.data) {
              this.$message.success('删除成功')
              this.callStageProblem()
            } else {
              this.$message.error('删除失败')
            }
          } else {
            this.$message.error('删除失败：' + res.message)
          }
        }).catch((err) => {
          this.$message.error('删除错误：' + err.message)
        })
      },
      add() {
        this.$refs.addForm.add()
      },
      handleOk() {
        this.callStageProblem()
      },
      callStageProblem() {
        this.loading = true
        getStageProblem({
            issueId: this.issueId
          })
          .then((res) => {
            if (res.result) {
              this.list = res.data
            } else {
              this.$message.error(res.message, 1);
            }
            this.loading = false
          })
          .catch((err) => {
            this.loading = false
            this.$message.error('错误提示：' + err.message, 1)
          });
      },

      callTransiteStage(record,transitionId) {
        this.loading = true
        transiteStage({
            issueId: record.issueId,
            transitionId:transitionId
          })
          .then((res) => {
            if (res.result) {
              if (res.data) {
                this.$message.success('操作成功')
              }else{
                this.$message.error('操作失败')
              }
            } else {
              this.$message.error(res.message, 1);
            }
            this.loading = false
          })
          .catch((err) => {
            this.loading = false
            this.$message.error('错误提示：' + err.message, 1)
          });
      },
    },
    created() {
      this.callStageProblem()
    }
  }
</script>


<style lang='less' scoped=''>
/deep/.ant-table{
    margin: 0 2px;
    margin-top:2px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}
/deep/.ant-table-thead > tr > th{
    font-weight: bold;
    background: #f3f3f3 !important;
}
/deep/.ant-table-small > .ant-table-content > .ant-table-body{
    margin: 0;
}


.state{
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 3px;
    margin-left: 3px;
}
.state.green{
    background: #91cc75;
}
.state.yellow{
    background: #efeb73;
}
.state.red{
    background: #f54747;
}

.sateflex{
    display: flex;
    align-items: center;
    justify-content: center;
}
.sateflex span{
    margin-right: 12px;
    
}
.sateflex div{
    width: 80px;
    text-align: left;
}
</style>