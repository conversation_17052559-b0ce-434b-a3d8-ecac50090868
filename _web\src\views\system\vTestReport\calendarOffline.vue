<template>
  <div>
    <div class="right-top-div">
      <a-button style="float:right;margin-right: 10px;font-size: 12px;border-radius: 4px;" size="small" type="primary"  @click="updateData = false" v-if="history == null && hasPerm('calender:updateData') && !safetyTestFlag">编辑数据</a-button>
      <a-popconfirm
        v-if="!updateData && history == null && hasPerm('calender:updateData')"
        title="确定提交修改吗?"
        ok-text="确定"
        cancel-text="取消"
        @confirm="submitUpdate"
        placement="top">
        <a-button style="float:right;margin-right: 10px;font-size: 12px;border-radius: 4px;" size="small" type="primary">提交</a-button>
      </a-popconfirm>
      <a-button v-if="!safetyTestFlag" style="float:right;margin-right: 10px;font-size: 12px;border-radius: 4px;" size="small" type="primary"  @click="getHistoryList(id?id:$route.query.testProgressId)">变更履历</a-button>
      <a-button style="float:right;margin-right: 10px;font-size: 12px;border-radius: 4px;" size="small" type="primary"  @click="exportSizeOriData">导出尺寸原始数据</a-button>
      <a-button style="float:right;margin-right: 10px;font-size: 12px;border-radius: 4px;" size="small" type="primary"  @click="exportHandleResult">导出处理结果</a-button>
    </div>
    <div class="action-bar">
      <a-icon type="menu" @click="handleSuspensionIcon" />
       <pbiReturnTop class="mt10" width="20" height="20" color="#333" @returnTop="handleReturnTop"></pbiReturnTop>
    </div>
    <div class="all-wrapper">

      <div class="left-content block" style="border-radius: 0 10px 10px 10px;">
        <pageComponent v-if="showVoltage" editObj="voltage"  @down="handleDown" @edit="handleEditEcharts"></pageComponent>
        <div ref="voltage" id="voltage" class="mt2" :class="{ 'animate__animated animate__pulse': animateObj.voltage  }" style="width: 595px;height: 415px;border: 0.5px solid #ccc;" v-if="showVoltage"></div>

        <pageComponent v-if="showInners" class="mt10" editObj="innerres"  @down="handleDown" @edit="handleEditEcharts"></pageComponent>
        <div ref="innerres" id="innerres" class="mt2" :class="{ 'animate__animated animate__pulse': animateObj.innerres  }" style="width: 595px;height: 415px;border: 0.5px solid #ccc;" v-if="showInners"></div>

        <pageComponent v-if="heightDataList.length > 0" class="mt10" editObj="height"  @down="handleDown" @edit="handleEditEcharts"></pageComponent>
        <div ref="height" id="height" class="mt2" :class="{ 'animate__animated animate__pulse': animateObj.height  }" style="width: 595px;height: 415px;border: 0.5px solid #ccc;" v-if="heightDataList.length > 0"></div>

        <pageComponent v-if="volumeDataList.length > 0" class="mt10" editObj="volume" @down="handleDown" @edit="handleEditEcharts"></pageComponent>
        <div ref="volume" id="volume" class="mt2" :class="{ 'animate__animated animate__pulse': animateObj.volume  }" style="width: 595px;height: 415px;border: 0.5px solid #ccc;" v-if="volumeDataList.length > 0"></div>

        <pageComponent v-if="weightDataList.length > 0" class="mt10" editObj="weight"  @down="handleDown" @edit="handleEditEcharts"></pageComponent>
        <div ref="weight" id="weight" class="mt2" :class="{ 'animate__animated animate__pulse': animateObj.weight  }" style="width: 595px;height: 415px;border: 0.5px solid #ccc;" v-if="weightDataList.length > 0"></div>

        <pageComponent v-if="isolateresDataList.length > 0" class="mt10" editObj="isolateres" @down="handleDown" @edit="handleEditEcharts"></pageComponent>
        <div ref="isolateres" id="isolateres" :class="{ 'animate__animated animate__pulse': animateObj.isolateres  }" class="mt2" style="width: 595px;height: 415px;border: 0.5px solid #ccc;" v-if="isolateresDataList.length > 0"></div>

      </div>
      <div class="right-content">
        <div class="block" id="export">

          <div class="flex-column" v-if="showVoltage">
            <div style="font-size: 16px;font-weight: 500;">电压数据</div>
            <div class="mt10" style="height: 415px;">
              <a-table class="voltageTableClass" :columns="voltageColumns" bordered
                       :data-source="bodyDataList" :pagination="paginationConfig" :loading="offlineTableLoading">


                <template slot="updateNum" slot-scope="text, record, index, columns">
                  <a-input-number v-if="!updateData"
                                  @blur="refreshEchart('voltage')"
                                  @keyup.enter="refreshEchart('voltage')"
                                  :disabled="updateData"
                                  :precision="parseInt(1)"
                                  step="0.1"
                                  :title="getAbnormalMsg(record,columns)"
                                  style="width: 100%;text-align: center"
                                  v-model="record[columns.dataIndex]"
                  />
                  <span v-else  :title="getAbnormalMsg(record,columns)" :style="getAbnormalStyle(record,columns)">{{text}}</span>
                </template>

                <template slot="dateRed" slot-scope="text, record, index, columns">
                  <span :title="getDateRedMsg(record,columns)" :style="getDateRed(record,columns)">{{text}}</span>
                </template>

              </a-table>
            </div>
          </div>
          <div class="flex-column" v-if="showInners">
            <div style="font-size: 16px;font-weight: 500;" class="mt10">内阻数据</div>
            <div class="mt10" style="height: 415px;">
              <a-table class="innerresTableClass" :columns="innerresColumns" bordered
                       :data-source="bodyDataList" :pagination="paginationConfig" :loading="offlineTableLoading">

                <template slot="updateNum" slot-scope="text, record, index, columns">
                  <a-input-number v-if="!updateData"
                                  @blur="refreshEchart('innerres')"
                                  @keyup.enter="refreshEchart('innerres')"
                                  :disabled="updateData"
                                  :precision="parseInt(1)"
                                  step="0.1"
                                  :title="getAbnormalMsg(record,columns)"
                                  style="width: 100%;text-align: center"
                                  v-model="record[columns.dataIndex]"
                  />
                  <span v-else  :title="getAbnormalMsg(record,columns)" :style="getAbnormalStyle(record,columns)">{{text}}</span>
                </template>

                <template slot="dateRed" slot-scope="text, record, index, columns">
                  <span :title="getDateRedMsg(record,columns)" :style="getDateRed(record,columns)">{{text}}</span>
                </template>

              </a-table>

            </div>
          </div>
          <div class="flex-column" v-if="heightDataList.length > 0">
            <div style="font-size: 16px;font-weight: 500;" class="mt10">尺寸数据</div>
            <div class="mt10" style="height: 415px;">
              <a-table class="heightTableClass" :columns="heightColumns" bordered
                       :data-source="heightDataList" :pagination="paginationConfig" :loading="offlineTableLoading">
                <template slot="updateNum" slot-scope="text, record, index, columns">
                  <a-input-number
                    @blur="refreshEchart('height')"
                    @keyup.enter="refreshEchart('height')"
                    :disabled="updateData"
                    :precision="parseInt(1)"
                    step="0.1"
                    style="width: 100%;text-align: center"
                    v-model="record[columns.dataIndex]"
                  />
                </template>

                <template slot="dateRed" slot-scope="text, record, index, columns">
                  <span :title="getDateRedMsg(record,columns)" :style="getDateRed(record,columns)">{{text}}</span>
                </template>

              </a-table>
            </div>
          </div>
          <div class="flex-column" v-if="volumeDataList.length > 0">
            <div style="font-size: 16px;font-weight: 500;" class="mt10">产气量数据</div>
            <div class="mt10" style="height: 415px;">
              <a-table class="volumeTableClass" :columns="volumeColumns" bordered
                       :data-source="volumeDataList" :pagination="paginationConfig" :loading="offlineTableLoading">
                <template slot="updateNum" slot-scope="text, record, index, columns">
                  <a-input-number v-if="!updateData"
                                  @blur="refreshEchart('volume')"
                                  @keyup.enter="refreshEchart('volume')"
                                  :disabled="updateData"
                                  :precision="parseInt(1)"
                                  step="0.1"
                                  :title="getAbnormalMsg(record,columns)"
                                  style="width: 100%;text-align: center"
                                  v-model="record[columns.dataIndex]"
                  />
                  <span v-else  :title="getAbnormalMsg(record,columns)" :style="getAbnormalStyle(record,columns)">{{text}}</span>
                </template>
              </a-table>
            </div>
          </div>
          <div class="flex-column" v-if="weightDataList.length > 0">
            <div style="font-size: 16px;font-weight: 500;" class="mt10">重量数据</div>
            <div class="mt10" style="height: 415px;">
              <a-table class="weightTableClass" :columns="weightColumns" bordered
                       :data-source="weightDataList" :pagination="paginationConfig" :loading="offlineTableLoading">
                <template slot="updateNum" slot-scope="text, record, index, columns">
                  <a-input-number v-if="!updateData"
                                  @blur="refreshEchart('weight')"
                                  @keyup.enter="refreshEchart('weight')"
                                  :disabled="updateData"
                                  :precision="parseInt(1)"
                                  step="0.1"
                                  :title="getAbnormalMsg(record,columns)"
                                  style="width: 100%;text-align: center"
                                  v-model="record[columns.dataIndex]"
                  />
                  <span v-else  :title="getAbnormalMsg(record,columns)" :style="getAbnormalStyle(record,columns)">{{text}}</span>
                </template>

                <template slot="dateRed" slot-scope="text, record, index, columns">
                  <span :title="getDateRedMsg(record,columns)" :style="getDateRed(record,columns)">{{text}}</span>
                </template>

              </a-table>
            </div>
          </div>
          <div class="flex-column" v-if="isolateresDataList.length > 0">
            <div style="font-size: 16px;font-weight: 500;" class="mt10">绝缘阻值数据</div>
            <div class="mt10" style="height: 415px;">
              <a-table class="isolateresTableClass" :columns="isolateresColumns" bordered
                       :data-source="isolateresDataList" :pagination="paginationConfig" :loading="offlineTableLoading">
                <template slot="updateNum" slot-scope="text, record, index, columns">
                  <a-input-number
                    @blur="refreshEchart('isolateres')"
                    @keyup.enter="refreshEchart('isolateres')"
                    :disabled="updateData"
                    :precision="parseInt(1)"
                    step="0.1"
                    style="width: 100%;text-align: center"
                    v-model="record[columns.dataIndex]"
                  />
                </template>

                <template slot="dateRed" slot-scope="text, record, index, columns">
                  <span :title="getDateRedMsg(record,columns)" :style="getDateRed(record,columns)">{{text}}</span>
                </template>

              </a-table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 在线编辑图表 -->
    <div v-if="drawerVisible">
      <PreviewDrawer
        :screenImageId = "screenImageId"
        :templateParam = "reportChartTemplateList[editObj]"
        :calendarLife="true"
        :LegendNameTypeList = "chartLegendNameList[editObj]"
        :legendOptions="chartOriginalLegent[editObj]"
        :data="chartOriginalseries[editObj]"
        :original="chartResetOriginal[editObj]"
        :editData="chartEditData[editObj]"
        :checkObj="chartCheckObj[editObj]"
        @submit="handleDrawerSubmit"
        @reset="handleDrawerReset"
        @close="drawerVisible = false"
        @changeTemplate ="handleChangeTemplate"
        @screenshot="handleScreenshot">
      </PreviewDrawer>
    </div>

    <!-- 快速导航栏 -->
    <div v-if="navigationVisible">
      <calendarThubnail
        :thumbnailList="thumbnailList"
        @cancel="navigationVisible = false"
        @choose="handleNavigation"
      ></calendarThubnail>
    </div>

    <a-modal
      title="变更履历"
      :visible="historyVisible"
      width="60%"
      @cancel="() => historyVisible = false"
    >

      <template slot="footer" >
        <a-button key="back" @click="() => historyVisible = false">
          <a>关闭</a>
        </a-button>
      </template>

      <a-table
        :columns="historyColumns"
        :data-source="historyData"
        :rowKey="(record) => record.id"
      >
            <span slot="history" slot-scope="text, record">
              <a @click="historyPushToReview(record,false)">修改前</a>
              <a-divider type="vertical" />
              <a @click="historyPushToReview(record,true)">修改后</a>
            </span>

      </a-table>


    </a-modal>
  </div>

</template>
<script>
import {STable} from "@/components";
import {Pagination} from 'ant-design-vue';

import {
  exportHandleResult, exportSizeOriData,
  get,
  getHandleResult, getHandleResultOfAq,
  validExportSizeOriData,
  updateHandleResult, validSafetyTestExport, exportHandleResultOfAq, exportSizeOriDataOfAq
} from "@/api/modular/system/testProgressManager";
import {
  testReportHistoryList
} from '@/api/modular/system/reportManager'

import {calendarCommon} from "./mixin/calendarCommon.js";
import {chartTemplate} from "@/views/system/vTestReport/mixin/chartTemplate";
import { downloadfile1 } from "@/utils/util";


export default {
  components: {
    STable,
    'a-pagination': Pagination
  },
  mixins: [calendarCommon,chartTemplate],
  data: function () {
    return {
      showVoltage: false,
      showInners: false,
      history:null,
      beforeUpdateVisible:false,
      historyVisible:false,
      historyColumns:[
        {
          title: '序号',
          align: 'center',
          width: 50,
          customRender: (text, record, index) => index + 1
        },{
          title: '操作时间',
          width: 90,
          align: 'center',
          dataIndex: 'createTime',

        },{
          title: '操作人',
          width: 90,
          align: 'center',
          dataIndex: 'createName',

        } ,{
          title: '查看',
          align: 'center',
          width: 200,
          scopedSlots: {customRender: 'history'},
        }
      ],
      historyData:[],

      updateData:true,

      thumbnailList: [],

      // 全局X轴最大值、间距
      globalXMax:0,
      globalXInterval:0,

      data: {},
      testCondition: "",
      headerList: [],
      bodyDataList: [],
      heightDataList: [], // 尺寸表格数据
      volumeDataList: [], // 产气量表格数据
      weightDataList: [], // 重量表格数据
      isolateresDataList: [], // 绝缘阻值表格数据

      offlineTableLoading: true,

      // 折点类型数组 三角、圆形、矩形、菱形、箭头、图钉、【空心：三角、圆形、矩形、菱形、箭头、图钉】、倒三角、五角星
      echartsSymbolList: [
        'triangle', 'circle', 'rect', 'diamond', 'arrow', 'pin',
        'emptyTriangle', 'emptyCircle', 'emptyRect', 'emptyDiamond', 'emptyArrow', 'emptyPin',
        'path://M0,0 L10,0 L5,10 Z',
        'path://M100,22.4 L78.6,54.6 L44.2,54.6 L72.6,79.4 L62.8,112.6 L100,92.4 L137.2,112.6 L127.4,79.4 L155.8,54.6 L121.4,54.6 Z'
      ],

      innerresEchartList: [],
      voltageEchartList: [],
      heightEchartList: [], // 尺寸echarts图表数据
      volumeEchartList: [], // 产气量echarts图表数据
      weightEchartList: [], // 重量echarts图表数据
      weightLossRateEchartList: [], // 失重率echarts图表数据
      isolateresEchartList: [], // 绝缘阻值echarts图表数据

      innerresColumns: [],
      voltageColumns: [],
      heightColumns: [], // 尺寸表头
      volumeColumns: [], // 产气量表头
      weightColumns: [], // 重量表头
      isolateresColumns: [], // 绝缘阻值表头

      testProgress: null,
      safetyTestFlag: false,
      safetyTestType: null,
      safetyTestList: [],
    }

  },
  mounted() {
    if (this.$route.query.type === '安全测试') {
      this.safetyTestFlag = true
      this.initOfSafetyTest()
    } else {
      this.init()
      this.getByTestProgressId()
    }
  },
  methods: {
    getData(columns){
      console.log(columns)
    },
    getAbnormalStyle(record,columns){
      if (this.data.abnormalList) {
        let abnormalList = this.data.abnormalList
        let abnormal = abnormalList.find(a => a.totalDay == record['0'] && a.isBefore == record['1'])
        if(null != abnormal && abnormal[columns.dataIndex.toString()]){
          return {
            height: '100%',
            textAlign: 'center',
            display: 'grid',
            border: '1px solid red',
            justifyContent: 'center',
            alignItems: 'center'
          }
        }else{
          return {

          }
        }
      } else {

      }
    },
    getDateRed(record,columns){

      if(!this.testProgress){
        return
      }

      let data = this.testProgress.data.find(a => a.totalDay == record['0'] && record['1'] == '出箱后' )

      if(data && data.dateRemark != null){
        return {
          height: '100%',
          textAlign: 'center',
          display: 'grid',
          border: '1px solid red',
          justifyContent: 'center',
          alignItems: 'center'
        }
      }else{
        return {

        }
      }

    },
    getDateRedMsg(record,columns){

      if(!this.testProgress){
        return
      }

      let data = this.testProgress.data.find(a => a.totalDay == record['0'] && record['1'] == '出箱后' )

      if(data && data.dateRemark != null){
        return data.dateRemark
      }else{
        return null
      }

    },
    getAbnormalMsg(record,columns){
      if (this.data.abnormalList) {
        let abnormalList = this.data.abnormalList
        let abnormal = abnormalList.find(a => a.totalDay == record['0'] && a.isBefore == record['1'])
        if(null != abnormal && abnormal[columns.dataIndex.toString()]){
          return  abnormal[columns.dataIndex.toString()]
        }
      }
    },
    handleSuspensionIcon(){
      this.navigationVisible = true
    },
    handleNavigation(e){
      let wrapper = document.getElementsByClassName("wrapper")[0]
      wrapper.scrollTop =e.index === 0 ? 0 : 10 + 40 + (e.index * (10 + 32 + 415))  // 10:边距 40：标题  ( 10:边距   32:操作按钮 415:图的大小 )

      this.animateObj[e.id] = true
      this.navigationVisible = false

      setTimeout(() => {
        this.animateObj[e.id] = false
      },2000)
    },
    historyPushToReview(record,ifAfterUpdate){
      this.history = record
      this.init(record.id,ifAfterUpdate)
      this.historyVisible = false
    },
    getHistoryList(recordId){
      testReportHistoryList({reportId:recordId}).then(res => {
        this.historyData = res.data
        this.historyVisible = true
      })
    },
    async refreshEchart(type){
      await this.refreshEchartList()
      this.chartFrist[type] = true
      this.initNormalEchart(type)
    },
    refreshEchartList(){
      // 获取内阻、电压、尺寸、产气量 图表数据
      this.innerresEchartList = [] // [{data: [{"value" : [0, 3.5]}, ...], "sampleCode": "2x", "batteryCode": "4y"}, ...]
      this.voltageEchartList = []
      this.heightEchartList = []
      this.volumeEchartList = []
      this.weightEchartList = []
      this.weightLossRateEchartList = []
      this.isolateresEchartList = []
      let weightColCount = 0
      this.headerList.forEach((head, colIndex) => {
        if (head[1] == "内阻/mΩ") {
          let data = []
          this.bodyDataList.forEach((row, rowIndex) => {
            if (colIndex in row) {
              // 格式化小数位数
              let original = row[colIndex]
              let formatted = typeof original == "number" ? this.roundToFixed(original, 2) : original
              // 表格数据
              this.bodyDataList[rowIndex][colIndex] = formatted
              // echarts图数据
              data.push({value: [row[0], formatted, row[1],row[2]]})
            }
          })
          if (data.length > 0) {
            this.innerresEchartList.push({data: data, sampleCode: head[3], batteryCode: head[4]})
          }
        } else if (head[1] == "电压/mV") {
          let data = []
          this.bodyDataList.forEach((row, rowIndex) => {
            if (colIndex in row) {
              // 格式化小数位数
              let original = row[colIndex]
              let formatted = typeof original == "number" ? this.roundToFixed(original, 1) : original
              // 表格数据
              this.bodyDataList[rowIndex][colIndex] = formatted
              // echarts图数据
              formatted = formatted >= 10000000000 ? 10000000000 : formatted
              data.push({value: [row[0], formatted, row[1],row[2]]})
            }
          })
          if (data.length > 0) {
            this.voltageEchartList.push({data: data, sampleCode: head[3], batteryCode: head[4]})
          }
        } else if (head[1] == "尺寸/mm") {
          let data = []
          this.heightDataList.forEach((row, rowIndex) => {
            if (colIndex in row) {
              // 格式化小数位数
              let original = row[colIndex]
              let formatted = typeof original == "number" ? this.roundToFixed(original, 2) : original
              // 表格数据
              this.heightDataList[rowIndex][colIndex] = formatted
              // echarts图数据
              data.push({value: [row[0], formatted, row[1],row[2]]})
            }
          })
          if (data.length > 0) {
            let sizeType = head[2];
            let lastIndex = sizeType.lastIndexOf("/");
            if (lastIndex !== -1) {
              sizeType = sizeType.substring(0, lastIndex);
            }

            this.heightEchartList.push({data: data, sizeType: sizeType, symbolType: head[3], sampleCode: sizeType + head[3], batteryCode: sizeType + head[4]})
          }
        } else if (head[1] == "产气量/g") {
          let data = []
          this.volumeDataList.forEach((row, rowIndex) => {
            if (colIndex in row) {
              // 格式化小数位数
              let original = row[colIndex]
              let formatted = original
              if (typeof original == "number") {
                if (original < 1000) {
                  formatted = this.roundToFixed(original, 2)
                } else {
                  formatted = this.roundToFixed(original, 1)
                }
              }
              // 表格数据
              this.volumeDataList[rowIndex][colIndex] = formatted
              // echarts图数据
              data.push({value: [row[0], formatted, row[1],row[2]]})
            }
          })
          if (data.length > 0) {
            this.volumeEchartList.push({data: data, sampleCode: head[3], batteryCode: head[4]})
          }
        } else if (head[1] == "重量/g") {
          let data = []
          let data2 = []
          weightColCount++
          this.weightDataList.forEach((row, rowIndex) => {
            if (colIndex in row) {
              // 格式化小数位数
              let original = row[colIndex]
              let formatted = original
              if (typeof original == "number") {
                if (original < 1000) {
                  formatted = this.roundToFixed(original, 2)
                } else {
                  formatted = this.roundToFixed(original, 1)
                }
              }
              // 表格数据
              this.weightDataList[rowIndex][colIndex] = formatted
              // 增加失重率表格数据
              this.weightDataList[rowIndex][`weightLossRate${weightColCount}`] = null
              if (formatted && this.weightDataList[0][colIndex] && this.weightDataList[0][colIndex] != 0) {
                let newRow = _.cloneDeep(row)
                newRow[`weightLossRate${weightColCount}`] = this.roundToFixed((formatted / this.weightDataList[0][colIndex] - 1) * 100, 2)
                this.$set(this.weightDataList, rowIndex, newRow)
                // this.weightDataList[rowIndex][`weightLossRate${weightColCount}`] = this.roundToFixed((formatted / this.weightDataList[0][colIndex] - 1) * 100, 2)
              }
              // echarts图数据
              data.push({value: [row[0], formatted, row[1],row[2]]})
              // 失重率echarts图数据
              data2.push({value: [row[0], this.weightDataList[rowIndex][`weightLossRate${weightColCount}`], row[1],row[2]]})
            }
          })
          if (data.length > 0) {
            this.weightEchartList.push({data: data, sampleCode: head[3], batteryCode: head[4]})
            this.weightLossRateEchartList.push({data: data2, sampleCode: head[3], batteryCode: head[4]})
          }
        } else if (head[1] == "绝缘阻值/mΩ") {
          let data = []
          this.isolateresDataList.forEach((row, rowIndex) => {
            if (colIndex in row) {
              // 格式化小数位数
              let original = row[colIndex]
              let formatted = typeof original == "number" ? this.roundToFixed(original, 2) : original
              // 表格数据
              this.isolateresDataList[rowIndex][colIndex] = formatted
              // echarts图数据
              data.push({value: [row[0], formatted, row[1]]})
            }
          })
          if (data.length > 0) {
            this.isolateresEchartList.push({data: data, sampleCode: head[3], batteryCode: head[4]})
          }
        }
      })
    },
    submitUpdate(){
      updateHandleResult(this.bodyDataList,this.id?this.id:this.$route.query.testProgressId).then(res => {
        if(res.data){
          this.$message.success("修改成功")
        }
      })

    },
    getByTestProgressId () {
      if (this.$route.query.testProgressId) {
        get({ id: this.$route.query.testProgressId }).then(res => {
          if (res.success) {
            this.testProgress = res.data
          }
        })
      } else {
        get({ ordtaskid: this.$route.query.id }).then(res => {
          if (res.success) {
            this.testProgress = res.data
          }
        })
      }
    },
    validExportSizeOriData(exportType) {
      if (!this.testProgress) {
        this.$message.warning("获取测试项目失败，请联系管理员")
        return
      }
      return validExportSizeOriData({ testProgressId: this.testProgress.id, exportType: exportType }).then(res => {
        return Promise.resolve(res);
      })
    },
    exportSizeOriData() {
      if (!this.safetyTestFlag) {
        this.validExportSizeOriData("sizeOriData").then(data => {
          if (data.success) {
            exportSizeOriData({ testProgressId: this.testProgress.id, alias: this.testProgress.testAlias }).then(res => {
              var fileName = this.testProgress.testCode + '-' + this.testProgress.applicant + '-' + this.testProgress.testAlias + '-' + '尺寸原始数据' + '.xlsx'
              if (res) {
                downloadfile1(res, fileName)
              }
            })
          } else {
            this.$message.warning(data.message)
          }
        })
      } else {
        this.validSafetyTestExport("sizeOriData").then(data => {
          if (data.success) {
            let folder = data.data.folders[0]
            exportSizeOriDataOfAq(this.safetyTestList).then(res => {
              var fileName = this.safetyTestList[0].folderNo + '-' + folder.createdbyname + '-' + this.safetyTestList[0].testAlias + '-' + '尺寸原始数据' + '.xlsx'
              if (res) {
                downloadfile1(res, fileName)
              }
            })
          } else {
            this.$message.warning(data.message)
          }
        })
      }
    },
    exportHandleResult() {
      if (!this.safetyTestFlag) {
        this.validExportSizeOriData("handleResult").then(data => {
          if (data.success) {
            exportHandleResult({ testProgressId: this.testProgress.id, alias: this.testProgress.testAlias }).then(res => {
              var alias = this.testProgress.testAlias ? this.testProgress.testAlias : this.testProgress.testProject
              var fileName = this.testProgress.testCode + '-' + this.testProgress.applicant + '-' + alias + '-' + '处理结果' + '.xlsx'
              if (res) {
                downloadfile1(res, fileName)
              }
            })
          } else {
            this.$message.warning(data.message)
          }
        })
      } else {
        this.validSafetyTestExport("handleResult").then(data => {
          if (data.success) {
            let folder = data.data.folders[0]
            exportHandleResultOfAq(this.safetyTestList).then(res => {
              var alias = this.safetyTestList[0].testAlias
              var fileName = this.safetyTestList[0].folderNo + '-' + folder.createdbyname + '-' + alias + '-' + '处理结果' + '.xlsx'
              if (res) {
                downloadfile1(res, fileName)
              }
            })
          } else {
            this.$message.warning(data.message)
          }
        })
      }
    },
    validSafetyTestExport(exportType) {
      if (!this.safetyTestList) {
        this.$message.warning("获取测试项目失败，请联系管理员")
        return
      }
      return validSafetyTestExport({ ordTaskId: this.safetyTestList[0].ordTaskId, exportType: exportType }).then(res => {
        return Promise.resolve(res);
      })
    },
    initOfSafetyTest () {
      let safetyTestIds = this.$route.query.safetyTestIds
      this.alias = this.$route.query.alias
      let param;
      param = { safetyTestIds: safetyTestIds }
      getHandleResultOfAq(param).then(async res => {
        this.data = res.data
        this.safetyTestType = this.data.safetyTestList[0].testType
        this.safetyTestList = this.data.safetyTestList

        this.testCondition = this.data.testCondition ? this.data.testCondition + "_" : ""

        const cycleArr = ['voltage','innerres','height','volume','weight','isolateres']
        await this.getChartTemplateRelationList(this.$route.query.safetyTestIds.split(",")[0],cycleArr)
        cycleArr.forEach(v => {
          this.chartEditData[v] = this._getInitDataOfAq(v,'edit', this.safetyTestType)
          this.chartResetOriginal[v] = this._getInitDataOfAq(v, 'original', this.safetyTestType)
        })

        const folderAndProjectName = (this.data.folderAndProjectName ? this.data.folderAndProjectName : "") + (this.alias ? "_" + this.alias : "")

        this.$emit("titleChange", folderAndProjectName)

        this.headerList = this.data.header ? this.data.header : []

        for (let i = 0; i < this.headerList.length; i++) {
          if (this.headerList[i].findIndex(item => item === '电压/mV') > -1) {
            this.showVoltage = true
          }
          if (this.headerList[i].findIndex(item => item === '内阻/mΩ') > -1) {
            this.showInners = true
          }
        }

        // 表头处理
        this.initOfflineColumnHeader()

        // 电压、内阻 表格数据
        this.bodyDataList = this.data.bodyDataList ? this.data.bodyDataList : []
        // 尺寸、产气量、重量、绝缘阻值 表格数据 对应测试阶段-出箱后的数据 或者 第0天的数据
        this.heightDataList = []
        this.volumeDataList = []
        this.weightDataList = []
        this.isolateresDataList = []
        let heightDayList = this.data.heightDayList ? this.data.heightDayList : []
        let volumeDayList = this.data.volumeDayList ? this.data.volumeDayList : []
        let weightDayList = this.data.weightDayList ? this.data.weightDayList : []
        let isolateresDayList = this.data.isolateresDayList ? this.data.isolateresDayList : []
        if (heightDayList.length > 0 || volumeDayList.length > 0 || weightDayList.length > 0 || isolateresDayList.length > 0) {
          let beforeInsOrAfterOut = this.safetyTestType === "before_after" ? "中检前" : "出箱后"
          this.bodyDataList.forEach(row => {
            if (row[0] || row[0] === 0) {
              if (heightDayList.includes(row[0].toString()) && ("0" === row[0].toString() || (row[1] && (beforeInsOrAfterOut === row[1] || "-" === row[1])))) {
                this.heightDataList.push(row)
              }
              if (volumeDayList.includes(row[0].toString()) && ("0" === row[0].toString() || (row[1] && (beforeInsOrAfterOut === row[1] || "-" === row[1])))) {
                this.volumeDataList.push(row)
              }
              if (weightDayList.includes(row[0].toString()) && ("0" === row[0].toString() || (row[1] && (beforeInsOrAfterOut === row[1] || "-" === row[1])))) {
                this.weightDataList.push(row)
              }
              if (isolateresDayList.includes(row[0].toString()) && ("0" === row[0].toString() || (row[1] && (beforeInsOrAfterOut === row[1] || "-" === row[1])))) {
                this.isolateresDataList.push(row)
              }
            }
          })
        }

        this.offlineTableLoading = false

        this.refreshEchartList()

        this.thumbnailList = [
          {id: 'voltage', subheading: '电压', show: this.showVoltage, echartList: this.voltageEchartList},
          {id: 'innerres', subheading: '内阻', show: this.showInners, echartList: this.innerresEchartList},
          {id: 'height', subheading: '尺寸', show: this.heightDataList.length > 0, echartList: this.heightEchartList},
          {id: 'volume', subheading: '产气量', show: this.volumeDataList.length > 0, echartList: this.volumeEchartList},
          {id: 'weight', subheading: '重量', show: this.weightDataList.length > 0, echartList: this.weightEchartList, echartY2List: this.weightLossRateEchartList},
          {id: 'isolateres', subheading: '绝缘阻值', show: this.isolateresDataList.length > 0, echartList: this.isolateresEchartList},
        ]

      }).then(async res => {
        // 电压
        if (this.showVoltage) {
          this.initNormalEchart('voltage')
        }
        // 内阻
        if (this.showInners) {
          this.initNormalEchart('innerres')
        }
        // 尺寸
        if (this.heightDataList.length > 0) {
          this.initNormalEchart('height')
        }
        // 产气量
        if (this.volumeDataList.length > 0) {
          this.initNormalEchart('volume')
        }
        // 重量
        if (this.weightDataList.length > 0) {
          this.initNormalEchart('weight')
        }
        // 绝缘阻值
        if (this.isolateresDataList.length > 0) {
          this.initNormalEchart('isolateres')
        }
      })
    },
    init(historyId,ifAfterUpdate) {
      this.id = this.$route.query.id
      this.alias = this.$route.query.alias
      let param;
      if (this.id) {
        param = { ordTaskId: this.id }
      } else {
        param = { testProgressId: this.$route.query.testProgressId }
      }
      param.historyId = historyId
      param.ifAfterUpdate = ifAfterUpdate
      getHandleResult(param).then(async res => {
        this.data = res.data

        this.testCondition = this.data.testCondition ? this.data.testCondition + "_" : ""

        const cycleArr = ['voltage','innerres','height','volume','weight','isolateres']
        await this.getChartTemplateRelationList(this.$route.query.testProgressId,cycleArr)
        cycleArr.forEach(v => {
          this.chartEditData[v] = this._getInitData(v,'edit')
          this.chartResetOriginal[v] = this._getInitData(v)
        })

        const folderAndProjectName = (this.data.folderAndProjectName ? this.data.folderAndProjectName : "") + (this.alias ? "_" + this.alias : "")

        this.$emit("titleChange", folderAndProjectName)

        this.headerList = this.data.header ? this.data.header : []
        for (let i = 0; i < this.headerList.length; i++) {
          if (this.headerList[i].findIndex(item => item === '电压/mV') > -1) {
            this.showVoltage = true
          }
          if (this.headerList[i].findIndex(item => item === '内阻/mΩ') > -1) {
            this.showInners = true
          }
        }

        // 表头处理
        this.initOfflineColumnHeader()

        // 电压、内阻 表格数据
        this.bodyDataList = this.data.bodyDataList ? this.data.bodyDataList : []
        // 尺寸、产气量、重量、绝缘阻值 表格数据 对应中检天数-出箱后的数据 或者 第0天的数据
        this.heightDataList = []
        this.volumeDataList = []
        this.weightDataList = []
        this.isolateresDataList = []
        let heightDayList = this.data.heightDayList ? this.data.heightDayList : []
        let volumeDayList = this.data.volumeDayList ? this.data.volumeDayList : []
        let weightDayList = this.data.weightDayList ? this.data.weightDayList : []
        let isolateresDayList = this.data.isolateresDayList ? this.data.isolateresDayList : []
        if (heightDayList.length > 0 || volumeDayList.length > 0 || weightDayList.length > 0 || isolateresDayList.length > 0) {
          this.bodyDataList.forEach(row => {
            if (row[0] || row[0] === 0) {
              if (heightDayList.includes(row[0].toString()) && ("0" === row[0].toString() || row[1] && "出箱后" === row[1])) {
                this.heightDataList.push(row)
              }
              if (volumeDayList.includes(row[0].toString()) && ("0" === row[0].toString() || row[1] && "出箱后" === row[1])) {
                this.volumeDataList.push(row)
              }
              if (weightDayList.includes(row[0].toString()) && ("0" === row[0].toString() || row[1] && "出箱后" === row[1])) {
                this.weightDataList.push(row)
              }
              if (isolateresDayList.includes(row[0].toString()) && ("0" === row[0].toString() || row[1] && "出箱后" === row[1])) {
                this.isolateresDataList.push(row)
              }
            }
          })
        }

        this.offlineTableLoading = false

        this.refreshEchartList()

        this.thumbnailList = [
          {id: 'voltage', subheading: '日历_电压', show: this.showVoltage, echartList: this.voltageEchartList},
          {id: 'innerres', subheading: '日历_内阻', show: this.showInners, echartList: this.innerresEchartList},
          {id: 'height', subheading: '日历_尺寸', show: this.heightDataList.length > 0, echartList: this.heightEchartList},
          {id: 'volume', subheading: '日历_产气量', show: this.volumeDataList.length > 0, echartList: this.volumeEchartList},
          {id: 'weight', subheading: '日历_重量', show: this.weightDataList.length > 0, echartList: this.weightEchartList, echartY2List: this.weightLossRateEchartList},
          {id: 'isolateres', subheading: '日历_绝缘阻值', show: this.isolateresDataList.length > 0, echartList: this.isolateresEchartList},
        ]

      }).then(async res => {

        if(historyId){
          return
        }

        // 电压
        if (this.showVoltage) {
          this.initNormalEchart('voltage')
        }
        // 内阻
        if (this.showInners) {
          this.initNormalEchart('innerres')
        }
        // 尺寸
        if (this.heightDataList.length > 0) {
          this.initNormalEchart('height')
        }
        // 产气量
        if (this.volumeDataList.length > 0) {
          this.initNormalEchart('volume')
        }
        // 重量
        if (this.weightDataList.length > 0) {
          this.initNormalEchart('weight')
        }
        // 绝缘阻值
        if (this.isolateresDataList.length > 0) {
          this.initNormalEchart('isolateres')
        }
      })
    },

    initOfflineColumnHeader() {
      // 离线报告表头处理
      if (this.headerList && this.headerList.length > 0) {
        let list = ['voltage','innerres','height','volume','weight','isolateres']

        // 初始化
        list.forEach(item => {
          this[`${item}Columns`] = []
        })

        let innerresHeader = {
          title: "内阻/mΩ",
          align: "center",
          children: [],
        }

        let voltageHeader = {
          title: "电压/mV",
          align: "center",
          children: [],
        }

        let heightHeader = {
          title: "尺寸/mm",
          align: "center",
          children: [],
        }

        let volumeHeader = {
          title: "产气量/g",
          align: "center",
          children: [],
        }

        let weightHeader = {
          title: "重量/g",
          align: "center",
          children: [],
        }
        let weightColCount = 0
        let weightLossRateHeader = {
          title: "失重率/%",
          align: "center",
          children: [],
        }

        let isolateresHeader = {
          title: "绝缘阻值/mΩ",
          align: "center",
          children: [],
        }

        for (let i = 0; i < this.headerList.length; i++) {
          let headerCol = this.headerList[i]
          let targetType = headerCol[1]
          let sizeType = headerCol[2]
          let sampleCode = headerCol[3]
          let batteryCode = headerCol[4]
          let status = headerCol[6]
          let tips = headerCol.length > 7 ? headerCol[7] : ''

          if (i < 3) {
            let commonHeader = {
              title: targetType,
              align: "center",
              width: "100px",
              dataIndex: i,
              //绝对时间
              scopedSlots: { customRender: i == 2? "dateRed" : null }
            }
            list.forEach(item => {
              this[`${item}Columns`].push(commonHeader)
            })

          } else if (targetType === "内阻/mΩ") {
            innerresHeader.children.push(this.getChildrenObject(sampleCode, batteryCode, status, i, null, tips))
          } else if (targetType === "电压/mV") {
            voltageHeader.children.push(this.getChildrenObject(sampleCode, batteryCode, status, i, null, tips))
          } else if (targetType === "尺寸/mm") {
            // 表头合并尺寸类型
            // 查询第二行表头中当前尺寸类型所在索引
            const index = heightHeader.children.findIndex(item => item.title === sizeType)
            if (index === -1) {
              // 未添加该尺寸类型，heightHeader.children.push 2,3,4,6
              heightHeader.children.push(this.getChildrenObject(sampleCode, batteryCode, status, i, sizeType, tips))
            } else {
              // 已添加该尺寸类型，heightHeader.children[index].children.push 3,4,6
              heightHeader.children[index].children.push(this.getChildrenObject(sampleCode, batteryCode, status, i, null, tips))
            }
          } else if (targetType === "产气量/g") {
            volumeHeader.children.push(this.getChildrenObject(sampleCode, batteryCode, status, i, null, tips))
          } else if (targetType === "重量/g") {
            weightColCount++
            weightHeader.children.push(this.getChildrenObject(sampleCode, batteryCode, status, i, null, tips))
            weightLossRateHeader.children.push(this.getChildrenObject(sampleCode, batteryCode, status, `weightLossRate${weightColCount}`, null, tips))
          } else if (targetType === "绝缘阻值/mΩ") {
            isolateresHeader.children.push(this.getChildrenObject(sampleCode, batteryCode, status, i, null, tips))
          }

        }

        // 处理表格无数据的情况
        if (innerresHeader.children.length === 0) {
          delete innerresHeader.children
        }
        if (voltageHeader.children.length === 0) {
          delete voltageHeader.children
        }
        if (heightHeader.children.length === 0) {
          delete heightHeader.children
        }
        if (volumeHeader.children.length === 0) {
          delete volumeHeader.children
        }
        if (weightHeader.children.length === 0) {
          delete weightHeader.children
        }
        if (weightLossRateHeader.children.length === 0) {
          delete weightLossRateHeader.children
        }
        if (isolateresHeader.children.length === 0) {
          delete isolateresHeader.children
        }

        this.innerresColumns.push(innerresHeader)
        this.voltageColumns.push(voltageHeader)
        this.heightColumns.push(heightHeader)
        this.volumeColumns.push(volumeHeader)
        this.weightColumns.push(weightHeader)
        this.weightColumns.push(weightLossRateHeader)
        this.isolateresColumns.push(isolateresHeader)
      }
    },
    getChildrenObject(sampleCode, batteryCode, status, dataIndex, sizeType, tips) {
      let result = {
        title: batteryCode,
        align: "center",
        width: "100px",
        children: [
          {
            title: <a-tooltip title={tips}>{status}</a-tooltip>,
            align: "center",
            width: "100px",
            dataIndex: dataIndex,
            scopedSlots: { customRender: typeof dataIndex == "number" ? "updateNum" : "" }
          }
        ]
      }

      if (sampleCode !== batteryCode) {
        result = {
          title: sampleCode,
          align: "center",
          width: "100px",
          children: [
            result
          ]
        }
      }

      if (sizeType) {
        result = {
          title: sizeType,
          align: "center",
          children: [
            result
          ]
        }
      }

      return result
    },

    roundToFixed(num, precision) {
      let factor = Math.pow(10, precision);
      let roundedNum = Math.round(num * factor) / factor;
      return roundedNum.toFixed(precision); // 确保结果具有指定位数的小数
    },

  }
}
</script>
<style lang="less" scoped>
@import "./css/calendar.less";

/deep/ .ant-table-tbody > tr > td {
  font-size: 12px !important;;
  padding: 4px !important;;
  font-weight: 400;
  overflow-wrap: break-word;
}
/deep/ .ant-table-thead > tr > th{
  font-size: 13px !important;;
  padding: 5px !important;;
  font-weight: 500;
  overflow-wrap: break-word;
}

/* 固定列 */
/deep/ .right-content .ant-table-thead tr:nth-child(1) th:nth-child(1),
/deep/ .right-content .ant-table-tbody tr td:nth-child(1){
  position: sticky;
  left: 0;
  z-index: 11;
}
/deep/ .right-content .ant-table-thead tr:nth-child(1) th:nth-child(2),
/deep/ .right-content .ant-table-tbody tr td:nth-child(2){
  position: sticky;
  left: 100px;
  z-index: 11;
}
/deep/ .right-content .ant-table-thead tr:nth-child(1) th:nth-child(3),
/deep/ .right-content .ant-table-tbody tr td:nth-child(3){
  position: sticky;
  left: 200px;
  z-index: 11;
}
/* 固定列数据背景颜色 */
/deep/ .right-content .ant-table-tbody tr td:nth-child(1),
/deep/ .right-content .ant-table-tbody tr td:nth-child(2),
/deep/ .right-content .ant-table-tbody tr td:nth-child(3) {
  background-color: #FFFFFF;
}
</style>