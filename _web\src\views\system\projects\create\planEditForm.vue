<template>
  <a-modal title="编辑" :width="800" :height="600"
           :bodyStyle="{padding:0}"
           :visible="visible" :confirmLoading="confirmLoading1"  style="padding: 0"
           :maskClosable="false" @ok="handleSummit"
           @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-row :gutter="24">
          <a-col :md="12" :sm="24">

            <a-form-item label="K0 立项评审计划日期" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker style="width: 150px"
                             :allow-clear="false"
                             :disabled="k0"

                             v-decorator="['productPlannedK0', ]" @change="(date) => changeDate(date,'productPlannedK0')"/>

            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item label="M1 项目计划日期" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker style="width: 150px"
                             :allow-clear="false"
                             :disabled="M1"
                             :disabledDate="(date) => disabledStartDate(date,'productPlannedK0')"
                             @change="(date) => changeDate(date,'productPlannedM1')"
                             v-decorator="['productPlannedM1', ]"/>

            </a-form-item>
          </a-col>

        </a-row>

        <a-row :gutter="24">
          <a-col :md="12" :sm="24">
            <a-form-item label="M2 A样冻结计划日期" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker style="width: 150px"
                             :allow-clear="false"
                             :disabled="M2A"
                             :disabledDate="(date) => disabledStartDate(date,'productPlannedM1')"
                             @change="(date) => changeDate(date,'productPlannedM2A')"
                             v-decorator="['productPlannedM2A', ]"/>

            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item label="M2 转阶段计划日期" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker style="width: 150px"
                             :allow-clear="false"
                             :disabled="M2z"
                             :disabledDate="(date) => disabledStartDate(date,'productPlannedM2A')"
                             @change="(date) => changeDate(date,'productPlannedM2z')"
                             v-decorator="['productPlannedM2z', ]"/>

            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :md="12" :sm="24">
            <a-form-item label="M3 B样冻结计划日期" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker style="width: 150px"
                             :allow-clear="false"
                             :disabled="M3B"
                             :disabledDate="(date) => disabledStartDate(date,'productPlannedM2z')"
                             @change="(date) => changeDate(date,'productPlannedM3B')"
                             v-decorator="['productPlannedM3B', ]"/>

            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item label="M3 转阶段计划时间" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-date-picker style="width: 150px"
                             :allow-clear="false"
                             :disabled="M3z"
                             :disabledDate="(date) => disabledStartDate(date,'productPlannedM3B')"
                             @change="(date) => changeDate(date,'productPlannedM3z')"
                             v-decorator="['productPlannedM3z', ]"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24">
            <a-form-item label="M4 C样冻结计划日期" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-date-picker style="width: 150px"
                             :allow-clear="false"
                             :disabled="M4"
                             :disabledDate="(date) => disabledStartDate(date,'productPlannedM3z')"
                             @change="(date) => changeDate(date,'productPlannedM4')"
                             v-decorator="['productPlannedM4', ]"/>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item label="M5 PPAP计划日期" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-date-picker style="width: 150px"
                             :allow-clear="false"
                             :disabled="M5"
                             :disabledDate="(date) => disabledStartDate(date,'productPlannedM4')"
                             @change="(date) => changeDate(date,'productPlannedM5')"
                             v-decorator="['productPlannedM5', ]"/>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :md="12" :sm="24">
            <a-form-item label="M6 SOP计划日期" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>

              <a-date-picker style="width: 150px"
                             :allow-clear="false"
                             :disabled="M6"
                             :disabledDate="(date) => disabledStartDate(date,'productPlannedM5')"
                             @change="(date) => changeDate(date,'productPlannedM6')"
                             v-decorator="['productPlannedM6', ]"/>
            </a-form-item>
          </a-col>

        </a-row>

      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import moment from "moment";

  import {productMsgToUpdate} from "@/api/modular/system/report";


  export default {
    props: {
      issueId: {
        type: Number,
        default: 0
      },

      projectdetail: {
        type: Object,
        default: {}
      }
    },
    data() {
      return {
        labelCol: {
          xs: {
            span: 12
          },
          sm: {
            span: 10
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 14
          }
        },
        visible: false,
        confirmLoading: false,
        confirmLoading1: false,
        form: this.$form.createForm(this),
        unEdit:true,
        k0:false,
        M1:false,
        M2A:false,
        M2z:false,
        M3B:false,
        M3z:false,
        M4:false,
        M5:false,
        M6:false,

        columns:[
          {key:'productPlannedK0',name:'K0 立项评审计划日期'},
          {key:'productPlannedM1',name:'M1 项目计划日期'},
          {key:'productPlannedM2A',name:'M2 A样冻结计划日期'},
          {key:'productPlannedM2z',name:'M2 转阶段计划日期'},
          {key:'productPlannedM3B',name:'M3 B样冻结计划日期'},
          {key:'productPlannedM3z',name:'M3 转阶段计划日期'},
          {key:'productPlannedM4',name:'M4 C样冻结计划日期'},
          {key:'productPlannedM5',name:'M5 PPAP计划日期'},
          {key:'productPlannedM6',name:'M6 SOP计划日期'}],
        keys:['k0','M1','M2A','M2z','M3B','M3z','M4','M5','M6']
      }
    },
    created() {
      /**
       *   private String productPlannedK0;//K0立项评审计划日期
       private String productPlannedM1;//M1项目计划日期
       private String productPlannedM2A;//M2A样冻结计划日期
       private String productPlannedM2z;//M2转阶段计划日期
       private String productPlannedM3B;//M3B样冻结计划日期
       private String productPlannedM3z;//M3转阶段计划日期
       private String productPlannedM4;//M4C样冻结计划日期
       private String productPlannedM5;//M5 PPAP计划日期
       private String productPlannedM6;//M6 SOP计划日期
       */

    },
    methods: {
      edit(){
        this.visible = true
        let k0 = this.projectdetail.productStageItems.find(e=>e.stage == 1)
        let M1 = this.projectdetail.productStageItems.find(e=>e.stage == 2)
        let M2A = this.projectdetail.productStageItems.find(e=>e.stage == 3)
        let M2z = this.projectdetail.productStageItems.find(e=>e.stage == 4)
        let M3B = this.projectdetail.productStageItems.find(e=>e.stage == 5)
        let M3z = this.projectdetail.productStageItems.find(e=>e.stage == 6)
        let M4 = this.projectdetail.productStageItems.find(e=>e.stage == 7)
        let M5 = this.projectdetail.productStageItems.find(e=>e.stage == 8)
        let M6 = this.projectdetail.productStageItems.find(e=>e.stage == 9)

        if(k0 != null && k0.actualCompletionDate != null){
          this.k0 = true
        }
        if(M1 != null && M1.actualCompletionDate != null){
          this.M1 = true
        }
        if(M2A  != null&& M2A.actualCompletionDate != null){
          this.M2A = true
        }
        if(M2z != null && M2z.actualCompletionDate != null){
          this.M2z = true
        }
        if(M3B != null && M3B.actualCompletionDate != null){
          this.M3B = true
        }
        if(M3z != null && M3z.actualCompletionDate != null){
          this.M3z = true
        }
        if(M4 != null && M4.actualCompletionDate != null){
          this.M4 = true
        }
        if(M5 != null && M5.actualCompletionDate != null){
          this.M5 = true
        }
        if(M6 != null && M6.actualCompletionDate != null){
          this.M6 = true
        }
        //判断有后面阶段的时间，则前面时间不显示
        /*for (let i = 0; i < this.keys.length; i++) {
          for (let j = i+1; j < this.keys.length; j++) {
            if(this[this.keys[j]]){
              this[this.keys[i]] = true
              break
            }
          }
        }*/

        this.$nextTick(() => {
          this.form.setFieldsValue({
            productPlannedK0:this.k0?(k0.actualCompletionDate == null?null:moment(k0.actualCompletionDate)):this.projectdetail.productPlannedK0 == null?null:moment(this.projectdetail.productPlannedK0),
            productPlannedM1:this.M1?(M1.actualCompletionDate == null?null:moment(M1.actualCompletionDate)):this.projectdetail.productPlannedM1 == null?null:moment(this.projectdetail.productPlannedM1),
            productPlannedM2A:this.M2A?(M2A.actualCompletionDate == null?null:moment(M2A.actualCompletionDate)):this.projectdetail.productPlannedM2A == null?null:moment(this.projectdetail.productPlannedM2A),
            productPlannedM2z:this.M2z?(M2z.actualCompletionDate == null?null:moment(M2z.actualCompletionDate)):this.projectdetail.productPlannedM2z == null?null:moment(this.projectdetail.productPlannedM2z),
            productPlannedM3B:this.M3B?(M3B.actualCompletionDate == null?null:moment(M3B.actualCompletionDate)):this.projectdetail.productPlannedM3B == null?null:moment(this.projectdetail.productPlannedM3B),
            productPlannedM3z:this.M3z?(M3z.actualCompletionDate == null?null:moment(M3z.actualCompletionDate)):this.projectdetail.productPlannedM3z == null?null:moment(this.projectdetail.productPlannedM3z),
            productPlannedM4:this.M4?(M4.actualCompletionDate == null?null:moment(M4.actualCompletionDate)):this.projectdetail.productPlannedM4 == null?null:moment(this.projectdetail.productPlannedM4),
            productPlannedM5:this.M5?(M5.actualCompletionDate == null?null:moment(M5.actualCompletionDate)):this.projectdetail.productPlannedM5 == null?null:moment(this.projectdetail.productPlannedM5),
            productPlannedM6:this.M6?(M6.actualCompletionDate == null?null:moment(M6.actualCompletionDate)):this.projectdetail.productPlannedM6 == null?null:moment(this.projectdetail.productPlannedM6)
          })
        })
      },
      handleCancel() {
        this.form.resetFields()
        this.visible = false
      },
      handleSummit() {
        let param = {}
        if(!this.k0 && null != this.form.getFieldValue('productPlannedK0')){
          let flag = this.checkDate('productPlannedK0')
          if(!flag){
            return
          }
          param['productPlannedK0'] = this.form.getFieldValue('productPlannedK0').format("YYYY-MM-DD")
        }
        if(!this.M1 && null != this.form.getFieldValue('productPlannedM1')){
          let flag = this.checkDate('productPlannedM1')
          if(!flag){
            return
          }
          param['productPlannedM1'] = this.form.getFieldValue('productPlannedM1').format("YYYY-MM-DD")
        }
        if(!this.M2A && null != this.form.getFieldValue('productPlannedM2A')){
          let flag = this.checkDate('productPlannedM2A')
          if(!flag){
            return
          }
          param['productPlannedM2A'] = this.form.getFieldValue('productPlannedM2A').format("YYYY-MM-DD")
        }
        if(!this.M2z && null != this.form.getFieldValue('productPlannedM2z')){
          let flag = this.checkDate('productPlannedM2z')
          if(!flag){
            return
          }
          param['productPlannedM2z'] = this.form.getFieldValue('productPlannedM2z').format("YYYY-MM-DD")
        }

        if(!this.M3B && null != this.form.getFieldValue('productPlannedM3B')){
          let flag = this.checkDate('productPlannedM3B')
          if(!flag){
            return
          }
          param['productPlannedM3B'] = this.form.getFieldValue('productPlannedM3B').format("YYYY-MM-DD")
        }


        if(!this.M3z && null != this.form.getFieldValue('productPlannedM3z')){
          let flag = this.checkDate('productPlannedM3z')
          if(!flag){
            return
          }
          param['productPlannedM3z'] = this.form.getFieldValue('productPlannedM3z').format("YYYY-MM-DD")
        }

        if(!this.M4 && null != this.form.getFieldValue('productPlannedM4')){
          let flag = this.checkDate('productPlannedM4')
          if(!flag){
            return
          }
          param['productPlannedM4'] = this.form.getFieldValue('productPlannedM4').format("YYYY-MM-DD")
        }

        if(!this.M5 && null != this.form.getFieldValue('productPlannedM5')){
          let flag = this.checkDate('productPlannedM5')
          if(!flag){
            return
          }
          param['productPlannedM5'] = this.form.getFieldValue('productPlannedM5').format("YYYY-MM-DD")
        }
        if(!this.M6 && null != this.form.getFieldValue('productPlannedM6')){
          let flag = this.checkDate('productPlannedM6')
          if(!flag){
            return
          }
          param['productPlannedM6'] = this.form.getFieldValue('productPlannedM6').format("YYYY-MM-DD")
        }

        param['issueId'] = this.issueId
        this.confirmLoading1 = true
        productMsgToUpdate(param).then((res) => {
          if (res.result) {
            this.$message.success('产品信息更新完成',2)
            this.$emit('ok')
            this.confirmLoading1 = false
            this.visible = false
          } else {
            this.confirmLoading1 = false
            this.$message.error('错误提示：' + res.message, 3)
          }
        }).catch((res) => {
          this.confirmLoading1 = false
          this.$message.error('错误提示：' + res.message, 3)
        })



      },
      disabledStartDate(startValue,column,column2) {

        if(this.form.getFieldValue(column) == null){
          return false
        }
        return startValue < moment(this.form.getFieldValue(column))
        /*if(column == null){
          return startValue >  moment(this.form.getFieldValue(column2))
        }else
        if(column2 == null){
          return startValue < moment(this.form.getFieldValue(column))
        }else{
          return startValue < moment(this.form.getFieldValue(column))|| startValue >  moment(this.form.getFieldValue(column2))

        }*/

      },
      checkDate(column) {

        if(this.form.getFieldValue(column) == null){
          return true
        }
        let columnKeyName = null
        let before = null
        let beforeColumn = null
        let after = null
        let afterColumn = null
        for (let i = 0; i < this.columns.length; i++) {

          if(column == this.columns[i].key){
            columnKeyName = this.columns[i]
            for (let j = 1; j < i; j++) {
              if(this.form.getFieldValue(this.columns[i-j].key) != null){
                before = this.form.getFieldValue(this.columns[i-j].key)
                beforeColumn = this.columns[i-j]
                break
              }
            }
            for (let j = i+1; j < this.columns.length; j++) {
              if(this.form.getFieldValue(this.columns[j].key) != null){
                after = this.form.getFieldValue(this.columns[j].key)
                afterColumn = this.columns[j]
                break
              }
            }
          }
        }

        if(null != before){
          if(this.form.getFieldValue(column) < before){
            this.$message.error(columnKeyName.name + "不能在" + beforeColumn.name + "之前",3)
            return false
          }
        }

        if(null != after){
          if(this.form.getFieldValue(column) > after){
            this.$message.error(columnKeyName.name + "不能在" + afterColumn.name + "之后",3)
            return false
          }
        }

        return true



      },
      changeDate(date,column){

        /*if(null != date){
          let param = {}
          param[column] = moment(date).format("YYYY-MM-DD")
          param['issueId'] = this.issueId
          productMsgToUpdate(param).then((res) => {
            if (res.result) {
              this.$message.success('产品信息更新完成',1)
            } else {
              this.$message.error('错误提示：' + res.message, 1)
            }
          }).catch((res) => {
            this.$message.error('错误提示：' + res.message, 1)
          })
        }*/


      }
    }
  }
</script>
<style lang="less" scoped>
  .ant-form-item {

    margin-bottom: 0px;

  }

  .man_button {
    padding-left: 11px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  /deep/ .ant-modal-body {
    padding: 0 !important;
  }

  /deep/ .ant-table-thead > tr > th, /deep/ .ant-table-tbody > tr > td {
    padding: 3px;
  }

  /deep/ .ant-table-footer {

    padding: 0px;
  }

  /deep/ .ant-table-pagination.ant-pagination {
    margin: 5px 0;
  }

  /deep/ .ant-input-number {
    width: 100%;
  }

  /deep/ .ant-input-number-sm > .ant-input-number-input-wrap > .ant-input-number-input {
    text-align: center;
  }

</style>
