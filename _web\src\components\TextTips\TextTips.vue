<template>
  <!-- 多出一行出现展开、收起按钮 -->
  <div class="top-tips">
    <div :class="isOpen ? '' : 'tipsBox'">
      <span class="f14 potips" :class="isOpen ? '' : 'van-ellipsis opentext'">
        <span class="remekTitle">{{ title ? title+'：' :'' }}</span><span>{{ text }} </span>
        <span v-if="isOpen && showopen" class="f14 inTips" @click="inTips">
            {{ "收起" }}
        </span>
      </span>
      <span v-if="!isOpen && showopen" class="f14 inTips" @click="inTips">
        {{ "展开" }}
      </span>
    </div>
  </div>
</template>

<script>
export default {
  name: "textTips",
  data() {
    return {
      platform:'pc',
      isOpen: false, //默认展开|收起
      showopen: true, //是否显示展开|收起按钮（文字）
    };
  },
  props: {
    // 显示的文字
    text: {
      type: String,
      default: "none",
      required: true,
    },
    // 标题
    title: {
      type: String,
      default: "none",
      required: true,
    },
  },
  created() {
    this.showopen = this.isLineWrap(); // 是否超出一行（超出则显示展开/收起）
  },
  methods: {
    inTips() {
      this.isOpen = !this.isOpen;
    },
      // 判断是否产出一行的函数
    isLineWrap(text = this.text) {
      const getBLen = function (str) {
        if (str == null) return 0;
        if (typeof str != "string") {
          str += "";
        }
        return str.replace(/[^\x00-\xff]/g, "01").length;
      };
      if (this.platform === "pc") {
        const lineWidth = 334;
        const textLine = getBLen(text) * 9.5;
        return textLine > lineWidth;
      } else {
        const fontSizeSty = document.documentElement.style.fontSize;
        const fontSize = Number(fontSizeSty.slice(0, fontSizeSty.length - 2));
        const textLine = fontSize * getBLen(text) * 0.16;
        const lineWidth = 4.56 * fontSize;
        return textLine > lineWidth;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.top-tips {
  display: flex;
  align-items: center;
  // padding: 5px 0;
  position: relative;
  .potips {
    padding: 0;
    padding-left: 4px;
    color: #000;
    min-height: 16px;
    text-align: left;
    display: block;
  }
  .inTips {
    color: rgb(91, 169, 245);
    cursor: pointer;
  }
  .tipsBox {
    // 是否在一行显示，或者自认换行
    display: flex;
    align-items: baseline;
    width: 100%;
  }
}
.opentext {
  min-width: 80%;
  max-width: 38%;
  display: inline-block;
}
.van-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.asdasd {
  display: inline-block;
  width: 40px;
  height: 20px;
}
.flex {
  display: flex;
}
.remekTitle {
  display: inline-block;
  padding-bottom: 3px;
}
</style>