import { axios } from '@/utils/request'

export function getMIVersionCurList (parameter, impBatteryId) {
  return axios({
    url: '/miVersionCur/getMIVersionCurList/' + impBatteryId,
    method: 'get',
    params: parameter
  })
}

export function insertMIVersionCur (parameter) {
  return axios({
    url: '/miVersionCur/insertMIVersionCur',
    method: 'post',
    data: parameter
  })
}

export function updateMIVersionCur (parameter) {
  return axios({
    url: '/miVersionCur/updateMIVersionCur',
    method: 'post',
    data: parameter
  })
}
