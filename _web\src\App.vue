<template>
	<a-config-provider :locale="locale">
		<div id="app" class="app app1">
			<router-view :key="$route.fullPath" class="scrollbar" />
		</div>
	</a-config-provider>
</template>

<script>
import zhCN from "ant-design-vue/lib/locale-provider/zh_CN"
import { AppDeviceEnquire, mixin } from "@/utils/mixin"
import { debounce } from "@/utils/util"

export default {
	mixins: [AppDeviceEnquire, mixin],
	data() {
		return {
			locale: zhCN
		}
	},
	watch: {
		// 监听路由变化
		$route(to, from) {}
	},
	created() {
		// 排除两个用vh做适配的页面
		if (window.location.pathname !== "/" && window.location.pathname !== "/batterydesignStandard") {
			// 适配笔记本屏幕,大部分笔记本是150比例
			if (window.screen.width <= 1280 && window.devicePixelRatio !== 1.5) {
				document.body.style.zoom = document.body.clientWidth / window.screen.width + 0.015
			}

			// 适配大屏
			if (window.screen.width > 1280 && window.devicePixelRatio !== 1) {
				document.body.style.zoom = (document.body.clientWidth * window.devicePixelRatio) / window.screen.width
			}
		}
	},
	mounted() {
		// window.addEventListener(
		// 	"resize",
		// 	debounce(() => {
		// 		alert("调整浏览器比例，会导致页面错乱，请调整完成后，刷新重试，推荐比例：100%")
		// 	}, 1000)
		// )
	}
}
</script>
<style>
.ant-table {
	font-size: 12px;
	color: #000;
}
.ant-table-header {
  display: contents;
}

.drag-tree-table-header,
.tree-row {
	font-size: 12px !important;
}
.drag-tree-table-header {
	height: initial !important;
}
.app {
	overflow: auto;
	border: none;
}
.scrollbar {
	margin: 0 auto;
}

::-webkit-scrollbar{
  	height: 10px !important;
	width: 10px !important;
}

::-webkit-scrollbar-track{
  	border-radius: 0 !important;
	background: #f0f0f0 !important;
}

::-webkit-scrollbar-thumb{
  	border-radius: 0 !important;
	background: #dddbdb !important;
}

/* 滚动条滑块悬停 */
::-webkit-scrollbar-thumb:hover {
  	background: #cdcdcd !important;
}

.pbi-btn-style .ant-btn{
	height: 28px;
	min-width: 50px;
	padding: 0 8px;
	font-size: 12px;
	border-radius: 4px;
}


</style>
