
import { axios } from '@/utils/request'


export function dashboardInfo (parameter) {
  return axios({
    url: '/dashboard/info',
    method: 'post',
    data: parameter
  })
}

export function dashboardProcess (parameter) {
  return axios({
    url: '/dashboard/process',
    method: 'post',
    data: parameter
  })
}

export function dashboardParams (parameter) {
  return axios({
    url: '/dashboard/params',
    method: 'post',
    data: parameter
  })
}


export function dashboardDocs (parameter) {
  return axios({
    url: '/dashboard/docs',
    method: 'post',
    data: parameter
  })
}
