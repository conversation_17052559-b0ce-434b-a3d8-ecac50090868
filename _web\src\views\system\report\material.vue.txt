<template>
<div>
        <a-breadcrumb class="breadcrumb" separator=">" >
            <a-breadcrumb-item><a @click="gotoIndex(-3)">信息对齐表</a></a-breadcrumb-item>
            <a-breadcrumb-item><a @click="gotoIndex(-2)">产品开发进展</a></a-breadcrumb-item>
            <a-breadcrumb-item><a @click="gotoIndex(-1)">产品主要技术文档</a></a-breadcrumb-item>
            <a-breadcrumb-item>材料管理</a-breadcrumb-item>
        </a-breadcrumb>
        <div class="head">{{ title }}</div>
        <a-table
            :showHeader = false
            :scroll="{ x: 1200,y:windowHeight }"
            size="small"
            :columns="columns"
            :data-source="dataSource"
            :pagination="false"
            :loading="loading"
            :rowClassName="addRowColor"
            bordered
        >
        </a-table>
</div>
</template>

<script>
    import {
        getBomMaterial,
    } from "@/api/modular/system/bomMaterialManage"
    export default {
    data() {
        return {
            column_obj:{},
            column_map:[],
            options:[],
            visible:false,
            windowHeight: document.documentElement.clientHeight - 82,
            type:{},
            cap:{0:'非量产',1:'量产'},
            cat:{1:'化学材料',2:'结构件',3:'包装材料'},
            vo: {},
            title:'',
            loading:false,
            dataSource:[
                {
                    seq:'BOM',
                    material_name:'产品应用',
                },
                {
                    seq:'BOM',
                    material_name:'适用产品状态',
                },
                {
                    seq:'BOM',
                    material_name:'适用工厂',
                },
                {
                    seq:'BOM',
                    material_name:'工厂产能',
                },
                {
                    seq:'序号',
                    material_type:'材料类别',
                    material_name:'名称',
                    material_code_01:'B01'
                },
                {
                    seq:'序号',
                    material_type:'材料类别',
                    material_name:'名称',
                    material_code_01:'物料代码',
                    material_model_01:'型号',
                    supplyer_01:'供应商'
                }
            ],
            columns:[
                {
                    title: '序号',
                    dataIndex: 'seq',
                    align: 'center',
                    width: 40,
                    customRender: (value, row, index) => {
                        const obj = {
                            children: value,
                            attrs: {},
                        };
                        if (index < 4) {
                            if (index < 1) {
                                obj.attrs.colSpan = 2;
                                obj.attrs.rowSpan = 4;
                            }else{
                                obj.attrs.colSpan = 0;
                                obj.attrs.rowSpan = 0;
                            }
                        }
                        if (index > 3 && index < 6) {
                            if (index < 5) {
                                obj.attrs.colSpan = 1;
                                obj.attrs.rowSpan = 2;
                            }else{
                                obj.attrs.colSpan = 0;
                                obj.attrs.rowSpan = 0;
                            }
                        }
                        if (index >= 6) {
                            obj.children = (<div>{index-5}</div>)
                        }
                        return obj;
                    },
                },
                {
                    title:'材料类别',
                    dataIndex:'material_type',
                    align:'center',
                    width:80,
                    customRender: (value, row, index) => {
                        const obj = {
                            children: value,
                            attrs: {},
                        };
                        if (index < 4) {
                            obj.attrs.colSpan = 0;
                            obj.attrs.rowSpan = 0;
                        }
                        if (index > 3 && index < 6) {
                            if (index < 5) {
                                obj.attrs.colSpan = 1;
                                obj.attrs.rowSpan = 2;
                            }else{
                                obj.attrs.colSpan = 0;
                                obj.attrs.rowSpan = 0;
                            }
                        }
                        if (index >= 6)  {
                            obj.attrs.rowSpan = row['material_type_rowSpan'] ? row['material_type_rowSpan'] : 0
                            obj.attrs.colSpan = row['material_type_rowSpan'] ? 1 : 0
                            obj.children = this.cat[value]
                        }
                        return obj;
                    },
                },
                {
                    title:'名称',
                    dataIndex:'material_name',
                    align:'center',
                    width:80,
                    customRender: (value, row, index) => {
                        const obj = {
                            children: value,
                            attrs: {},
                        };
                        if (index > 3 && index < 6) {
                            if (index < 5) {
                                obj.attrs.colSpan = 1;
                                obj.attrs.rowSpan = 2;
                                obj.children = this.columns_filter('',value, row, index)
                            }else{
                                obj.attrs.colSpan = 0;
                                obj.attrs.rowSpan = 0;
                            }
                        }
                        return obj;
                    },
                },
                {
                    title:'物料代码',
                    dataIndex:'material_code_01',
                    align:'center',
                    width:80,
                    customRender: (value, row, index) => {
                        return this.material_code_render('material_code_01',value,row,index,true)
                    }
                },
                {
                    title:'型号',
                    dataIndex:'material_model_01',
                    align:'center',
                    width:80,
                    customRender:(value, row, index) => {
                        return this.material_render('material_model_01',value,row,index)
                    }
                },
                {
                    title:'供应商',
                    dataIndex:'supplyer_01',
                    align:'center',
                    width:80,
                    customRender:(value, row, index) => {
                        return this.material_render('supplyer_01',value,row,index)
                    }
                },
            ]
        }
    },
    methods:{
        onChange(value){
            console.log(value)
        },
        getByClass(parent, cls) {
            if (parent.getElementsByClassName) {
                return Array.from(parent.getElementsByClassName(cls));
            } else {
                var res = [];
                var reg = new RegExp(' ' + cls + ' ', 'i')
                var ele = parent.getElementsByTagName('*');
                for (var i = 0; i < ele.length; i++) {
                    if (reg.test(' ' + ele[i].className + ' ')) {
                        res.push(ele[i]);
                    }
                }
                return res;
            }
        },
        init(){
            if (this.columns.length == 9) {
                this.$nextTick(()=>{
                    let _items = this.getByClass(document, 'back_color')
                    for (const $e of _items) {
                        $e.classList.remove('back_color')
                    }
                    let dataSource = this.dataSource
                    for (let i = 0,j= dataSource.length; i < j; i++) {
                        let diff = []
                        for (const e of this.column_map) {
                            let $n = e < 10 ? '0' + e : '' + e
                            let temp = (dataSource[i][`material_code_${$n}`] ? dataSource[i][`material_code_${$n}`]+'' : '' )
                            + (dataSource[i][`material_model_${$n}`] ? dataSource[i][`material_model_${$n}`]+'' : '')
                            + (dataSource[i][`supplyer_${$n}`] ? dataSource[i][`supplyer_${$n}`]+'' : '');
                            if (diff.indexOf(temp) < 0) {
                                diff.push(temp)
                            }
                        }
                        if (diff.length > 1) {
                            let items = this.getByClass(document, 'hlight'+i)
                            for (const $e of items) {
                                var ee = $e.parentNode
                                ee.classList.add('back_color')
                            }
                        }
                    }
                })
            }else{
                this.$nextTick(()=>{
                    let _items = this.getByClass(document, 'back_color')
                    for (const $e of _items) {
                        $e.classList.remove('back_color')
                    }
                })
            }

            this.$nextTick(()=>{
                let _items = this.getByClass(document, 'div_cls')
                for (const $e of _items) {
                    let _e = $e.parentNode
                    _e.classList.add('backcolor')
                }
            })
        },
        gotoIndex(index){
            this.$router.go(index)
        },
        addRowColor(record,index){
            if (index < 6 && index > 3) {
                let cls = `fiexdTr${index}`
                return cls;
            } else {
                return
            }

        },
        material_codes_render(columnkey,value, row, index,col_length,flag){
            const obj = {
                children:value,
                attrs: {},
            };
            if (index < 5) {
                if (index < 1) {
                    obj.attrs.colSpan = flag ? (col_length) : 0
                    obj.attrs.rowSpan = flag ? 1 : 0;
                    obj.children = this.type[value]
                }else{
                    obj.attrs.colSpan = 3;
                    obj.attrs.rowSpan = 1;
                    if (index == 1) {
                        obj.children = this.cap[value]
                    }
                    if (index == 2) {
                        obj.children = (<div class='div_cls'>{value}</div>)
                    }
                }
                
            }
            if (index >= 6) {
                obj.children = (<div class={['hlight'+index, columnkey+'_'+index]} >{value}</div>)
            }
            return obj
        },

        material_code_render(columnkey,value, row, index,flag){
            const obj = {
                children:value,
                attrs: {},
            };
            if (index < 5) {
                if (index < 1) {
                    obj.attrs.colSpan = flag ? (this.columns.length - 3) : 0
                    obj.attrs.rowSpan = flag ? 1 : 0;
                    obj.children = this.type[value]
                }else{
                    obj.attrs.colSpan = 3;
                    obj.attrs.rowSpan = 1;
                    if (index == 1) {
                        obj.children = this.cap[value]
                    }
                    if (index == 2) {
                        obj.children = (<div class='div_cls'>{value}</div>)
                    }
                }
                
            }
            if (index >= 6) {
                obj.children = (<div class={['hlight'+index, columnkey+'_'+index]} >{value}</div>)
            }
            return obj
        },
        material_render(columnkey,value, row, index){
            const obj = {
                children: value,
                attrs: {},
            };
            if (index < 5) {
                obj.attrs.colSpan = 0;
                obj.attrs.rowSpan = 0;
            }
            if (index >= 6) {
                obj.children = (<div class={['hlight'+index,columnkey+'_'+index]} >{value}</div>)
            }
            return obj
        },

        columns_filter(columnkey,value,row,index){
            return (
                <span>
                    <span>{value}</span>
                    <a-popover v-model={this.visible} placement="bottom" trigger="click">
                        <template slot="content">
                        <a-checkbox-group
                            v-model={this.column_map}
                            options={this.options}
                            change={this.onChange}
                        />
                        <div class='popover-btns'>
                            <a onClick={this.hide}>重置</a>
                            <a onClick={this.confirm}>确认</a>
                        </div>
                        </template>
                        <a class='a-filter'><a-icon type="filter" /></a>
                    </a-popover>
                </span>
            )
        },
        hide(){
            let baseCols = this.columns.filter((value, index, array) => {
                return index >= 0 && index < 3;
            });
            let cols = []
            this.column_map = this.options.map((item)=>{
                return item.value;
            })
            let column_map = this.column_map
            let length = column_map.length * 3
            for (const e of column_map) {
                this.column_obj[e][0].customRender = (value, row, index) => {
                    return this.material_codes_render(this.column_obj[e][0].dataIndex,value,row,index,length ,column_map.indexOf(e) == 0 ? true: false)
                }
                cols.push(...this.column_obj[e])
            }
            baseCols.push(...cols)
            this.columns = baseCols
            setTimeout(() => {
                this.init()
            }, 500);
            this.visible = false
            
        },
        confirm() {
            let baseCols = this.columns.filter((value, index, array) => {
                return index >= 0 && index < 3;
            });
            let cols = []
            let column_map = this.column_map
            let length = column_map.length * 3
            for (const e of column_map) {
                this.column_obj[e][0].customRender = (value, row, index) => {
                    return this.material_codes_render(this.column_obj[e][0].dataIndex,value,row,index,length,column_map.indexOf(e) == 0 ? true:false)
                }
                cols.push(...this.column_obj[e])
            }
            baseCols.push(...cols)
            this.columns = baseCols
            setTimeout(() => {
                this.init()
            }, 500);
            this.visible = false
            
        },
        getBomMaterial() {
               this.loading = true
               getBomMaterial({
                   issueId: this.$route.query.issueId
                }).then((res) => {
                   if (res.success) {
                        let type = {}
                        for (const item of res.data.productCateOptionBeans) {
                            type[item.id] = item.value
                        }
                        this.type = type
                        let columnObj = res.data.materialColumns ? JSON.parse(res.data.materialColumns) : null
                        if (columnObj) {
                            let $i = 1
                            for (const item of columnObj) {
                                if ($i == 1) {
                                    item.customRender = (value, row, index) => {
                                        return this.material_code_render(item.dataIndex,value,row,index,false)
                                    }
                                }
                                if ($i == 2) {
                                    item.customRender = (value, row, index) => {
                                        return this.material_render(item.dataIndex,value,row,index)
                                    }
                                }
                                if ($i == 3) {
                                    item.customRender = (value, row, index) => {
                                        return this.material_render(item.dataIndex,value,row,index)
                                    }
                                    $i = 0
                                }
                                $i++
                            }
                        }
                        let rowsObj = res.data.materialRows ? JSON.parse(res.data.materialRows): this.dataSource
                        let _row_data = rowsObj.filter((value, index, array) => {
                                return index < 6;
                        });
                        if (rowsObj.length > 6) {
                            let handle = (property) => {
                                return function(a,b){
                                    let val1 = a[property];
                                    let val2 = b[property];
                                    return val1 - val2;
                                }
                            }
                            let _row = rowsObj.filter((value, index, array) => {
                                return index >= 6;
                            });
                            _row.sort(handle('material_type'))
                            for (const item of ['material_type']) {
                                for (let i = 0, j = _row.length; i < j; i++) {
                                    let rowSpan = 0;
                                    let n = i;
                                    while (
                                        _row[n + 1] &&
                                        _row[n + 1][item] == _row[n][item]
                                    ) {
                                        rowSpan++;
                                        n++;
                                        _row[n].rowSpan = 0;
                                    }
                                    if (rowSpan) _row[i][item + "_rowSpan"] = rowSpan + 1;
                                    if (!rowSpan) _row[i][item + "_rowSpan"] = 1;
                                        i += rowSpan;
                                }
                            }
                            _row_data.push(..._row)
                        }
                        if (columnObj) {
                            this.columns.push(...columnObj)
                        }
                        let options = []
                        let column_obj = {}
                        let val = _row_data[0]['material_code_01']
                        for (let n = 3,m=this.columns.length; n < m; ) {
                            let _n = n/3
                            let $n = _n < 10 ? '0' + _n : '' + _n
                            options.push({
                                label: `B${$n}`,
                                value:_n
                            })
                            column_obj[_n] = this.columns.filter((value, index, array) => {
                                return index >= n && index < n+3;
                            });
                            _row_data[0][`material_code_${$n}`] = val
                            n = n + 3
                        }

                        this.column_obj = column_obj
                        this.options = options
                        this.column_map = options.map((item)=>{
                            return item.value;
                        })
                        this.dataSource = _row_data
                        this.vo = res.data
                        setTimeout(() => {
                            this.init()
                        }, 500);
                   } else {
                       this.$message.error(res.message)
                   }
                   this.loading = false
                }).catch((err) => {
                   this.$message.error('错误：' + err.message)
                   this.loading = false
                })
        },
    },
    created(){
        this.title = this.$route.query.title + '产品材料管理'
        this.getBomMaterial()
    }
}
</script>

<style lang='less' scoped=''>
 /deep/.ant-table-tbody > tr > td{
    padding: 2px !important;
    font-size: 12px !important;
    border-color: rgb(219, 219, 219);
}

/deep/.ant-table-tbody > tr:nth-child(5),/deep/.ant-table-tbody > tr:nth-child(6){
    background: #5c81cb;
    color: #fff;
}
/deep/.fiexdTr4 {
    position: sticky;
    z-index: 1000;
    top: 0;
    background: #fff;
}
/deep/.fiexdTr5 {
    position: sticky;
    z-index: 1000;
    top: 23px;
    background: #fff;
}
.head {
  border: 1px solid #97b1e7;
  background: #5c81cb;
  text-align: center;
  padding: 10px 0;
  color: #fff;
  font-size: 20px;
}
/deep/.ant-table-small > .ant-table-content .ant-table-row:last-child td{
    border-bottom: 1px solid rgb(219, 219, 219);
}
.breadcrumb{
  padding: 5px 0;
  padding-left: 13px;
}.ant-breadcrumb a{
  color:#5d90fa !important;
}.ant-breadcrumb{
  font-size: 12px !important;
}

/deep/.backcolor{
    background: #fad7a7;
}
/deep/.back_color{
    background: #fad7a7;
 }
 .a-filter{
    color: #00000096;
    margin-left: 6px;
 }
 /* .popover-btns{
    display: flex;
    justify-content: space-between;
 } */
 .popover-btns a{
    color: #00000096;
    margin: 0 5px;
 }
 /deep/.ant-checkbox-group-item{
    display: block;
    margin-bottom: 2px;
 }
</style>
<style lang='css'>
.ant-popover-inner-content{
    padding: 5px 8px;
 }
</style>