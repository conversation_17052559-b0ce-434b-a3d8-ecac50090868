import { axios } from '@/utils/request'

export function getDocs(parameter) {
    return axios({
        url: '/doc/list',
        method: 'get',
        params: parameter
    })
}

export function docCommit(parameter) {
    return axios({
        url: '/doc/commit',
        method: 'post',
        data: parameter
    })
}

export function docImport(parameter) {
  return axios({
    url: '/doc/import',
    method: 'post',
    data: parameter
  })
}

export function getTechHistory (parameter) {
    return axios({
      url: '/tech/history',
      method: 'get',
      params: parameter
    })
  }



export function getDocList(parameter) {
    return axios({
        url: '/docs/list',
        method: 'get',
        params: parameter
    })
}

export function saveDoc(parameter) {
    return axios({
        url: '/docs/save',
        method: 'post',
        data: parameter
    })
}

export function addDoc(parameter) {
    return axios({
        url: '/docs/add',
        method: 'post',
        data: parameter
    })
}

export function getTransition(parameter) {
    return axios({
        url: '/docs/transition',
        method: 'get',
        params: parameter
    })
}

export function docsCommit(parameter) {
    return axios({
        url: '/docs/commit',
        method: 'post',
        data: parameter
    })
}

export function docAgreeOrNot(parameter) {
    return axios({
        url: '/docs/agree_or_not',
        method: 'post',
        data: parameter
    })
}