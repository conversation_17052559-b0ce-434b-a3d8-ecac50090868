/**
 * 统一的高对比度模式样式
 * 用于支持Windows高对比度模式和其他辅助功能
 */

/* 高对比度模式支持 */
@media (forced-colors: active) {
  /* 基础UI元素 */
  .ant-radio-checked .ant-radio-inner::after,
  .ant-checkbox-checked .ant-checkbox-inner::after,
  .ant-btn-primary,
  .ant-select-selection,
  .ant-input,
  .ant-input-number,
  .ant-switch,
  .ant-tabs-nav .ant-tabs-tab-active,
  .ant-form-item-required::before,
  .ant-btn-link,
  .ant-tag {
    forced-color-adjust: auto;
    border-color: Canvas;
    background-color: Canvas;
  }
  
  /* 文本元素 */
  h4, h5, h6, .ant-form-item-label > label {
    color: CanvasText;
  }
  
  /* 边框元素 */
  .ant-form-item, .ant-input-number, .ant-input, .ant-select-selection {
    border-color: CanvasText;
  }
  
  /* 卡片和容器 */
  .base-card,
  .formula-editor-content,
  .formula-editor-section,
  .analysis-results-section,
  .form-section {
    forced-color-adjust: auto;
    border: 1px solid CanvasText;
    box-shadow: none;
    background-color: Canvas;
  }
  
  /* 标题和分隔线 */
  .form-section h5,
  h5, h6 {
    color: CanvasText;
    border-bottom: 1px solid CanvasText;
  }
  
  /* 表格元素 */
  .ant-table-small,
  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    border: 1px solid CanvasText;
  }
  
  /* 标签页 */
  .ant-tabs-tab {
    forced-color-adjust: auto;
  }
  
  /* 公式相关元素 */
  .formula-container,
  .formula-preview-content,
  .formula-cell,
  .formula-preview,
  .weight-settings,
  .fitting-settings,
  .coefficient-panel,
  .coefficient-range-settings,
  .formula-info-settings,
  .variable-list {
    forced-color-adjust: auto;
    border: 1px solid CanvasText;
    background-color: Canvas;
  }
  
  /* 文件上传区域 */
  .file-upload-container {
    border: 1px dashed CanvasText;
  }
  
  .file-upload-container:hover {
    border: 2px dashed Highlight;
  }
  
  .upload-icon {
    color: Highlight;
  }
  
  /* 数据相关卡片 */
  .data-import-section,
  .file-operation-card,
  .data-summary-card,
  .data-preview-card {
    border: 1px solid CanvasText;
    box-shadow: none;
  }
  
  /* 图表容器 */
  .chart-wrapper {
    border: 1px solid CanvasText;
    box-shadow: none;
  }
  
  /* 预测和拟合相关元素 */
  .form-section,
  .coefficient-panel,
  .variable-panel,
  .formula-preview-content,
  .prediction-curve-section {
    forced-color-adjust: auto;
    border: 1px solid CanvasText;
  }
  
  /* 公式加载状态 */
  .formula-loading {
    background-color: Canvas;
    border: 1px solid CanvasText;
  }
}
