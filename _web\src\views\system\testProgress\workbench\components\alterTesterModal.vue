<template>
  <div style="background-color: #FFFFFF;padding: 10px">
    <a-modal :title="`${modalData.testName} 更改测试员`" :visible="true" :width="1000" :centered="true" @cancel="personHandleCancel" @ok="personSubmit">
      <div style="margin: 0px 0px 20px 100px">
        <a-row :gutter="[8,8]">
          <a-col :span="10">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="姓名">
              <a-input v-model="searchTesterParam.USERNAME" @keyup.enter="searchTesters" @change="searchTesters"/>
            </a-form-item>
          </a-col>
          <a-col :span="10">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="工号">
              <a-input v-model="searchTesterParam.ID" @keyup.enter="searchTesters" @change="searchTesters"/>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
      <a-spin :spinning="personIsLoading">
        <a-table :columns="personColumns"
                 :data-source="personResultData"
                 bordered
                 :rowKey="(record) => record.ID"
                 :row-selection="{
                    selectedRowKeys: personSelectedRowKeys,
                    selectedRows: personSelectedRows,
                    onChange: personOnSelect,
                    columnWidth:20
                   }"
                 ref="personTable">
        </a-table>
      </a-spin>
    </a-modal>

    <a-modal :title="`分配负责人`" :width="400" :visible="this.chooseTesterVisible" @ok="testerSubmit" @cancel="testerCancel">
      <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="负责人" has-feedback>
        <a-select style="width: 80%" :options="personSelectedOptions" v-model="tester"/>
      </a-form-item>
    </a-modal>
  </div>
</template>

<script>
import {mapGetters} from "vuex";
import {getTestPerson} from "@/api/modular/system/limsManager";
import {executeAlterTesterOrPlanTime} from "@/api/modular/system/testProgressManager";
export default {
  name: "AlterTesterModal",
  props: {
    modalData: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      labelCol: {
        sm: {
          span: 5
        }
      },
      wrapperCol: {
        sm: {
          span: 18
        }
      },
      laboratoryId: null,
      personIsLoading: false,
      personColumns: [
        {
          title: '序号',
          dataIndex: 'index',
          align: 'center',
          width: 30,
          ellipsis: true,
          customRender: (text, record, index) => `${index + 1}`
        },
        {
          title: '工号',
          dataIndex: 'ID',
          align: 'center',
          width: 40,
        },
        {
          title: '姓名',
          dataIndex: 'USERNAME',
          align: 'center',
          width: 40,
        },
        {
          title: '部门',
          dataIndex: 'ORGNAME',
          align: 'center',
          width: 50,
        },
        {
          title: '在测测试项目数量',
          dataIndex: 'ORDTASKNUMBER',
          align: 'center',
          width: 50,
          scopedSlots: { customRender: 'ORDTASKNUMBER' },
        },
        {
          title: '在测样品数量',
          dataIndex: 'ORDERNUMBER',
          align: 'center',
          width: 50,
        },
        {
          title: '在测委托单数量',
          dataIndex: 'FOLDERNUMBER',
          align: 'center',
          width: 50,
        },
      ],
      personResultData: [],
      allPersonResultData: [],
      personSelectedRows: [],
      personSelectedRowKeys: [],
      personSelectedOptions: [],
      searchTesterParam: {},
      confirmLoading: false,
      chooseTesterVisible: false,
      tester: null
    }
  },
  created() {
    console.log('this.userInfo',this.userInfo)
    if (this.userInfo.account === "superAdmin") {
      this.laboratoryId = "HZ_YJ_DL_JM"
      this.personLoadData()
    } else {
      // "第零实验室-测试组长"和"第零实验室-计划管理员"角色才能看到【第零实验室】标签页
      let jmList = this.userInfo.roles.filter(item => item.id === "1712686842365419522" || item.id === "1720008384229163010")
      // "研发检测中心-测试组长"和"研发检测中心-PMC"角色才能看到【研发检测中心】标签页
      let csList = this.userInfo.roles.filter(item => item.id === "1676772241413427202" || item.id === "1722493545281904641")
      // "第六实验室(JM)-测试组长"和"第六实验室(JM)-PMC"角色才能看到【第六实验室(JM)】标签页
      let jmcsList = this.userInfo.roles.filter(item => item.id === "1839274430373945345" || item.id === "1839275601205518338")
      // "第四实验室-测试组长"和"第四实验室-PMC"角色才能看到【第四实验室】标签页
      let aqList = this.userInfo.roles.filter(item => item.id === "1754070159908036609" || item.id === "1773588429358874625")
      if (jmList.length > 0) {
        this.laboratoryId = "HZ_YJ_DL_JM"
      } else if (csList.length > 0) {
        this.laboratoryId = "HZ_YJ_DL_CS"
      } else if (jmcsList.length > 0) {
        this.laboratoryId = "JM_YJ_DL_CS"
      } else if (aqList.length > 0) {
        this.laboratoryId = "HZ_YJ_DL_AQ"
      }
      this.personLoadData()
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  mounted() {
  },
  methods: {
    personLoadData() {
      let orgId = "HZ_YJ_DL_JM"
      if (this.laboratoryId) {
        orgId = this.laboratoryId
      }
      console.log('this.laboratoryId',this.laboratoryId)
      return getTestPerson({ status: "deprecated", orgId: orgId, roleId: "699586598579968" }).then((res) => {
        this.personResultData = res.data
        this.allPersonResultData = JSON.parse(JSON.stringify(res.data)) // 深克隆
      })
    },
    searchTesters() {
      console.log('searchTesterParam',this.searchTesterParam)
      if (this.searchTesterParam.ID) {
        this.personResultData = this.allPersonResultData.filter(item => item.ID.includes(this.searchTesterParam.ID))
      }
      if (this.searchTesterParam.USERNAME) {
        this.personResultData = this.allPersonResultData.filter(item => item.USERNAME.includes(this.searchTesterParam.USERNAME))
      }
      if (!this.searchTesterParam.ID && !this.searchTesterParam.USERNAME) {
        this.personResultData = this.allPersonResultData
      }
    },
    personHandleCancel() {
      this.$emit("cancel")
    },
    personOnSelect(selectedRowKeys, selectedRows) {
      this.personSelectedRows = selectedRows
      this.personSelectedRowKeys = selectedRowKeys
      this.personSelectedOptions = []
      for(let i = 0; i < this.personSelectedRows.length; i++) {
        this.personSelectedOptions.push({
          value: this.personSelectedRows[i].ID,
          label: this.personSelectedRows[i].USERNAME
        })
      }
    },
    personSubmit() {
      if (this.personSelectedRows.length === 0) {
        this.$message.warning('请选择一人进行任务分配！')
      } else if(this.personSelectedRows.length === 1) {
        this.executeAlterTesterOfJMOrAQ()
      } else {
        // 两个及以上测试人，需要选择负责人
        if (this.laboratoryId === 'HZ_YJ_DL_JM' || this.laboratoryId === 'HZ_YJ_DL_AQ') {
          this.chooseTesterVisible = true
        }
      }
    },
    testerCancel() {
      this.tester = null
      this.chooseTesterVisible = false
      this.personIsLoading = false
    },
    testerSubmit() {
      if (this.personSelectedRows.length > 1 && this.tester == null) {
        this.$message.warning('请选择负责人！')
        return
      }
      this.executeAlterTesterOfJMOrAQ()
    },
    executeAlterTesterOfJMOrAQ() {
      let person = {
        opinion: "更改测试人",
        userId: this.personSelectedRows[0].ID,
        userName: this.personSelectedRows[0].USERNAME,
        participator: '',
        participatorCode: ''
      }
      if (this.personSelectedRows.length > 1) {
        let participatorArr = []
        let participatorCodeArr = []
        for (let i = 0; i < this.personSelectedRows.length; i++) {
          if(this.personSelectedRows[i].ID === this.tester) { // 负责人
            person.userName = this.personSelectedRows[i].USERNAME
            person.userId = this.personSelectedRows[i].ID
          } else { // 参与人数组
            participatorArr.push(this.personSelectedRows[i].USERNAME)
            participatorCodeArr.push(this.personSelectedRows[i].ID)
          }
        }
        person.participator = participatorArr.join(',')
        person.participatorCode = participatorCodeArr.join(',')
      }
      this.executeAlterTester(person)
    },
    executeAlterTester(person) {
      this.personIsLoading = true
      let ordTaskId = this.modalData.ordTaskId
      executeAlterTesterOrPlanTime({ordTaskId:ordTaskId, person:person}).then((res) => {
        if (res.success === true) {
          setTimeout(() => {
            this.clearOldData()
            this.$message.success('更改测试人成功！')
          }, 200)
        } else {
          this.$message.warning('更改测试人失败：' + res.message)
          this.personIsLoading = false
        }
      })
    },
    clearOldData() {
      this.personSelectedRows = []
      this.personSelectedRowKeys = []
      this.personIsLoading = false
      this.chooseTesterVisible = false
      this.$emit("cancel")
    },
  }
}
</script>

<style scoped>
/deep/ .ant-table-thead > tr > th {
  text-align: center;
  padding: 5px!important;
  font-size: 14px!important;
}
/deep/ .ant-table-tbody > tr > td {
  padding: 0px!important;
  height: 32px!important;
  font-size: 12px!important;
}

/deep/ .ant-modal-header {
   padding: 16px 24px 0;
   border: none;
 }
/deep/ .ant-modal-body {
  padding: 16px;
}
/deep/ .ant-modal-footer {
  padding: 0 24px 16px ;
}
</style>