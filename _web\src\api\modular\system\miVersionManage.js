import { axios } from '@/utils/request'

export function getMIVersionList (parameter) {
  return axios({
    url: '/miVersion/getMIVersionList',
    method: 'get',
    params: parameter
  })
}

export function insertMIVersion (parameter) {
  return axios({
    url: '/miVersion/insertMIVersion',
    method: 'post',
    data: parameter
  })
}

export function updateMIVersion (parameter) {
  return axios({
    url: '/miVersion/updateMIVersion',
    method: 'post',
    data: parameter
  })
}
