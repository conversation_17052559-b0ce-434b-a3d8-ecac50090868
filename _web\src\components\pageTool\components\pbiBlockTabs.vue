<template>
  <div>
    <a-tabs type="card" @change="onClickTab">
      <a-tab-pane v-for="item in tabsList" :key="item.value" :tab="item.label">
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
  export default {
    name: 'pbiBlockTabs',
    props: {
      tabsList: {
        type: Array,
        default: []
      }
    },
    methods: {
      onClickTab(key) {
        this.$emit('tabClick',key)
      }
    }
  }

</script>

<style lang='less' scoped>
  /deep/.ant-tabs{
    color: #333;
  }
  /deep/.ant-tabs-bar{
    border-bottom: none;
  }
  /deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-nav-container{
    height: 36px;
  }
  /deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab{
    height: 36px;
    line-height: 36px;
    border: none;
    border-radius:4px 4px 0 0;
    margin-right: 4px;
    background:#eee;
  }
  /deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active{
    background-color: #fff;
  }
</style>