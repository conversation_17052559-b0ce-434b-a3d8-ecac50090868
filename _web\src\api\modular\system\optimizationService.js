/**
 * 参数优化服务
 * 提供参数优化相关的API调用和数据处理逻辑
 */
import { api } from '@/api';
import { message } from 'ant-design-vue';
import { showSuccess, showWarning } from '@/utils/errorUtils';

/**
 * 开始参数优化
 * @param {Object} options - 参数优化选项
 * @param {Object} options.latexFormula - LaTeX公式对象
 * @param {Array} options.dataPoints - 数据点数组
 * @param {Array} options.parsedParams - 解析后的参数数组
 * @param {Object} options.weightConfig - 权重配置
 * @param {Object} options.algorithmParams - 算法参数
 * @param {Object} options.coefficients - 系数数组
 * @param {Object} options.paramDescriptions - 参数描述对象
 * @param {Object} options.groupDescriptions - 组描述对象
 * @returns {Promise} 优化结果Promise
 */
export async function startParameterOptimization(options) {
  const {
    latexFormula,
    dataPoints,
    parsedParams,
    weightConfig,
    algorithmParams,
    coefficients,
    paramDescriptions,
    groupDescriptions,
    findGroupLetterForCoefficient,
    generateValidWeightConfig
  } = options;

  try {
    message.loading({ content: '参数寻优中...', duration: 0, key: 'optimizing' });

    // 准备请求参数
    const fitRequest = {
      latex_str: latexFormula,
      fit_data: dataPoints,
      params: parsedParams.map(param => {
        const result = { name: param.name, type: param.type };

        if (param.type === 'variable') {
          result.value = {
            min: param.value?.min !== undefined ? parseFloat(param.value.min) : 0,
            max: param.value?.max !== undefined ? parseFloat(param.value.max) : 1,
            default: param.value?.default !== undefined ? parseFloat(param.value.default) : 0.5
          };
          result.describe = paramDescriptions[param.name] || null;
        } else {
          const coef = coefficients.find(c => c.name === param.name);
          const optRange = (coef?.optRange) || param.optRange || {min: 0, max: 1, initial: 0.5};

          result.value = {
            min: parseFloat(optRange.min),
            max: parseFloat(optRange.max),
            default: parseFloat(optRange.initial)
          };

          result.describe = (coef?.describe) || param.describe || null;

          const groupLetter = findGroupLetterForCoefficient(param.name);
          if (groupLetter && groupDescriptions[groupLetter]) {
            result.group_description = groupDescriptions[groupLetter];
          }
        }

        return result;
      }),
      weight_config: generateValidWeightConfig(weightConfig, dataPoints),
      algorithm_params: {
        max_iter: parseInt(algorithmParams.max_iter),
        fit_standard: algorithmParams.fit_standard,
        n_last_points: parseInt(algorithmParams.n_last_points),
        n_pred_points: parseInt(algorithmParams.n_pred_points)
      }
    };

    // 调用API
    const response = await api.data.fitData(fitRequest);
    message.destroy('optimizing');

    if (response.data.success) {
      showSuccess('参数寻优完成！');

      const optimizedParams = response.data.params;
      const metrics = response.data.metrics;

      return {
        success: true,
        fittingResults: {
          mae: metrics.mae.toFixed(4),
          rmse: metrics.rmse.toFixed(4),
          last_point_error: metrics.last_points_error.toFixed(4),
          pred_point_error: metrics.predict_points_error.toFixed(4),
          optimized_params: optimizedParams.map(param => {
            let paramValue = typeof param.value === 'object' && param.value !== null
              ? param.value.default : param.value;

            const numericValue = typeof paramValue === 'string' ? parseFloat(paramValue) : paramValue;

            return {
              name: param.name,
              value: numericValue,
              describe: param.describe
            };
          })
        }
      };
    } else {
      showWarning(`参数寻优失败：${response.data.message}`);
      return { success: false, message: response.data.message };
    }
  } catch (error) {
    message.destroy('optimizing');
    console.error('参数寻优错误:', error);
    showWarning(`参数寻优失败：${error.message || '未知错误'}`);
    return { success: false, message: error.message || '未知错误' };
  }
}

/**
 * 计算拟合结果详情
 * @param {Object} options - 计算选项
 * @param {Object} options.parsedLatex - 解析后的LaTeX公式
 * @param {Array} options.parsedParams - 解析后的参数数组
 * @param {Array} options.dataPoints - 数据点数组
 * @param {Object} options.fittingResults - 拟合结果
 * @param {Object} options.paramDescriptions - 参数描述对象
 * @returns {Promise} 计算结果Promise
 */
export async function calculateFittingResults(options) {
  const {
    parsedLatex,
    parsedParams,
    dataPoints,
    fittingResults,
    paramDescriptions
  } = options;

  try {
    message.loading({ content: '正在计算拟合结果...', duration: 0, key: 'calculating' });

    const optimizedParams = parsedParams.map(param => {
      const result = {
        name: param.name,
        type: param.type,
        describe: ''
      };

      if (param.type === 'variable') {
        result.value = param.value;
        result.describe = paramDescriptions[param.name] || '';
      } else {
        const optimizedParam = fittingResults.optimized_params.find(p => p.name === param.name);
        result.value = optimizedParam ? optimizedParam.value : param.value;
      }

      if (result.describe === undefined || result.describe === null) {
        result.describe = '';
      }

      return result;
    });

    const conditions = dataPoints.map(point => [
      point.temperature,
      point.soc,
      Math.max(...point.days)
    ]);

    const requestParams = {
      latex_str: parsedLatex,
      params: optimizedParams,
      conditions: conditions
    };

    const response = await api.data.calculateCapacity(requestParams);
    message.destroy('calculating');

    if (response.data.success && response.data.data_points?.length > 0) {
      return {
        success: true,
        calculatedDataPoints: response.data.data_points
      };
    } else {
      showWarning('计算拟合结果失败：' + (response.data.message || '未知错误'));
      return { success: false, message: response.data.message || '未知错误' };
    }
  } catch (error) {
    message.destroy('calculating');
    console.error('计算拟合结果失败:', error);
    showWarning('计算拟合结果失败：' + (error.message || '未知错误'));
    return { success: false, message: error.message || '未知错误' };
  }
}
