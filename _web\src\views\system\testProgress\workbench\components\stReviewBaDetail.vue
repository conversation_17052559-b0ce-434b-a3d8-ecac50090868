<template>
  <div class="detail-container">
    <div class="modal-wrapper">
      <a-descriptions title="详细信息"></a-descriptions>
      <div class="content-wrapper">
        <div v-for="(singleSafetyTest, singleIndex) in originalData" :key="singleSafetyTest.id">
          <a-spin :spinning="modalLoading">
            <div>
              <a-descriptions title="">
                <a-descriptions-item label="测试项目">
                  {{ singleSafetyTest.testAlias }}
                </a-descriptions-item>
                <a-descriptions-item>
                  <template slot="label">
                    <span style="font-weight: bold">中检类型</span>
                  </template>
                  <div style="font-weight: bold;color: black">
                    {{
                      singleSafetyTest.middleCheck === "small"
                        ? "小中检"
                        : singleSafetyTest.middleCheck === "large"
                          ? "大中检"
                          : singleSafetyTest.middleCheck === "recharge"
                            ? "补电"
                            : "-"
                    }}
                  </div>
                </a-descriptions-item>
                <a-descriptions-item label="测试设备">
                  {{ singleSafetyTest.equiptCodeNames || "-" }}
                </a-descriptions-item>
                <a-descriptions-item>
                  <template slot="label">
                    <span style="font-weight: bold">测试阶段</span>
                  </template>
                  <div style="font-weight: bold;color: black">
                    <span v-if="singleSafetyTest.testType === 'before_after'" >
                        {{singleSafetyTest.stage ? (singleSafetyTest.stage === "0" ? "测试前" : "测试后") : "-" }}
                    </span>
                    <span v-else >
                        {{singleSafetyTest.stage ? (singleSafetyTest.stage === "0" ? "初始性能检测" : singleSafetyTest.stage) : "-" }}
                    </span>
                  </div>
                </a-descriptions-item>
              </a-descriptions>
            </div>
            <div class="table-wrapper">

              <div style="margin:10px 0px 15px 0px">
                <a-button :loading="videoLoading" v-if="singleSafetyTest.testType === 'before_after' && singleSafetyTest.videoFlag === '1' && editFlag"
                          class="mr10" type="primary" size="small" ghost
                          @click="() => (chooseCodeVideoVisible = true, selectedRowCodeVideoKeys = [])">导入测试视频</a-button>
                <a-button :loading="attachLoading" v-if="singleSafetyTest.testType === 'before_after' && singleSafetyTest.attachmentFlag === '1' && editFlag"
                          class="mr10" type="primary" size="small" ghost
                          @click="() => (chooseCodeAttachVisible = true, selectedRowCodeAttachKeys = [])">导入过程数据</a-button>
                <a-upload
                  name="file"
                  :headers="headers"
                  :customRequest="$event => handleUploadOfAttach($event, singleSafetyTest, singleIndex)"
                  :data="uploadData"
                  :disabled="!editFlag || 'uploading' === uploadingProgressList[singleIndex].uploadStatusOfAttach"
                  :action="picOrVidPostUrl"
                  :multiple="false"
                  :showUploadList="false"
                  @change="uploadAttachment($event, singleSafetyTest)">
                  <a-button :loading="'uploading' === uploadingProgressList[singleIndex].uploadStatusOfAttach"
                            v-if="singleSafetyTest.testType === 'temp_cycle' && singleSafetyTest.attachmentFlag === '1' && editFlag"
                            class="mr10" type="primary" size="small" ghost @click="">导入过程数据</a-button>
                </a-upload>
                <div :style="{ float: 'right', width: '25%', marginBottom: '15px' }"
                     v-show="singleSafetyTest.testType === 'before_after' && singleSafetyTest.stage === '1' && (uploadProgress > 0 || uploadProgressShow)">
                  测试视频<a-progress :strokeWidth="12" :percent="uploadProgress"></a-progress>
                </div>
                <div :style="{ float: 'right', width: '25%', marginBottom: '15px' }"
                     v-show="singleSafetyTest.testType === 'before_after' && singleSafetyTest.stage === '1'  && 'uploading' === uploadingProgressList[singleIndex].uploadStatusOfAttach">
                  过程数据<a-progress :strokeWidth="12" :percent="uploadingProgressList[singleIndex].percentOfAttach"></a-progress>
                </div>
              </div>

              <div class="auto-table">

                <div v-if="singleSafetyTest.testType === 'temp_cycle' && (singleSafetyTest.videoFlag ==='1' || singleSafetyTest.attachmentFlag ==='1')"
                     style="width: 100%;height: 50px;">
                  <!-- 过程视频 -->
                  <div v-if="singleSafetyTest.videoFlag ==='1'" style="margin-bottom: 0px">
                    <div style="border: 1px solid lightgrey;width: 100px;height:40px;float: left;display: flex;justify-content: center; align-items: center;">
                      <span style="color: black;">测试视频</span>
                    </div>

                    <div style="border: 1px solid lightgrey;width: 280px;height:40px;float: left;display: flex;justify-content: center; align-items: center;">
                    <span v-if="!singleSafetyTest.videoName" style="display: flex;justify-content: center; align-items: center;">
                      <a-upload
                        name="file"
                        :headers="headers"
                        :customRequest="$event => handleTcVideoUpload($event, singleSafetyTest, singleIndex)"
                        :data="uploadData"
                        :action="picOrVidPostUrl"
                        :before-upload="beforeUploadVideo"
                        :multiple="false"
                        :disabled="!editFlag || 'uploading' === uploadingVideoProgressList[singleIndex].uploadStatus"
                        :showUploadList="false"
                        @change="uploadVideo($event, singleSafetyTest)"
                        accept=".mp4,.avi,.wmv,.mov">
                        <a-spin :spinning="'uploading' === uploadingVideoProgressList[singleIndex].uploadStatus"><a v-if="editFlag">上传</a></a-spin>
                      </a-upload>
                    </span>
                    <span v-else style="display: flex;justify-content: center; align-items: center;">
                      <a style="color: green;text-align: center;" @click="openFileOrDownload(singleSafetyTest.videoId, singleSafetyTest.videoName)">{{ singleSafetyTest.videoName }}</a>
                      <a-popconfirm
                        placement="topRight"
                        ok-text="删除"
                        cancel-text="取消"
                        @confirm="deleteVideo($event, singleSafetyTest)">
                        <template slot="title"> 确认删除视频"{{ singleSafetyTest.videoName }}"吗 </template>
                        <a-icon v-if="editFlag"  type="close" style="float: right;padding: 5px 0px 0px 5px" />
                      </a-popconfirm>
                    </span>
                    </div>
                    <div style="float: right;width: 20%;padding-top: 0px;"
                         v-show="singleSafetyTest.testType === 'temp_cycle' && 'uploading' === uploadingVideoProgressList[singleIndex].uploadStatus">
                      测试视频<a-progress :strokeWidth=12 :percent="uploadingVideoProgressList[singleIndex].percent" status="active"></a-progress>
                    </div>
                  </div>
                  <!-- 过程数据 -->
                  <div v-if="singleSafetyTest.attachmentFlag ==='1'" style="margin-bottom: 0px">
                    <div style="border: 1px solid lightgrey;width: 100px;height:40px;float: left;display: flex;justify-content: center; align-items: center;">
                      <span style="color: black;">过程数据</span>
                    </div>
                    <div style="border: 1px solid lightgrey;width: 400px;height:40px;float: left;display: flex;justify-content: center; align-items: center;">
                    <span style="display: flex;justify-content: center; align-items: center;">
                      <span v-for="fileItem in singleSafetyTest.attachment">
                        <a style="color: green;text-align: center;" @click="openFileOrDownload(fileItem.id, fileItem.name)">{{ fileItem.name }}</a>
                        <a-popconfirm
                          placement="topRight"
                          ok-text="删除"
                          cancel-text="取消"
                          @confirm="deleteAttachment($event, singleSafetyTest, fileItem)">
                          <template slot="title"> 确认删除过程数据"{{ fileItem.name }}"吗 </template>
                          <a-icon v-if="editFlag"  type="close" style="float: right;padding: 5px 0px 0px 5px" />
                        </a-popconfirm>
                      </span>
                    </span>
                    </div>
                    <div style="float: right;width: 20%;padding-top: 0px;"
                         v-show="singleSafetyTest.testType === 'temp_cycle' && 'uploading' === uploadingProgressList[singleIndex].uploadStatusOfAttach">
                      过程数据<a-progress :strokeWidth=12 :percent="uploadingProgressList[singleIndex].percentOfAttach" status="active"></a-progress>
                    </div>
                  </div>
                </div>

                <a-table
                  bordered
                  class="mt10"
                  v-if="singleSafetyTest.testData"
                  :columns="columnList[singleIndex]"
                  :rowKey="record => record.cellTestCode"
                  :data-source="singleSafetyTest.testData"
                  :pagination="false"
                  :scroll="{ x: 'max-content' }"
                >
								<span v-for="item in columnDataList[singleIndex]" :key="item.cellTestCode" :slot="item.dataIndex" slot-scope="text, record, index">
                  <span v-if="item.dataIndex === 'cellTestCode'">
										<div :style="{color: text === stReviewBean.cellCode && singleSafetyTest.stage === stReviewBean.stage ? '#1890ff' : 'black'}">{{ text }}</div>
									</span>
									<span v-else-if="item.dataIndex === 'alias'">
										<div>{{ text }}</div>
									</span>
                  <!-- 电芯状态 -->
                  <span v-else-if="item.dataIndex === 'batteryStatus'">
                    <a-select v-model="record.batteryStatus" @change="updateStDataAtReview(singleSafetyTest, record)"
                              :disabled="!editFlag"
                              style="width: 160px;font-size: 14px;">
                      <a-select-option value="ongoing">
                        进行中
                      </a-select-option>
                      <a-select-option value="testDone">
                        状态正常-测试完成
                      </a-select-option>
                      <a-select-option value="earlyEnd">
                        状态正常-提前结束
                      </a-select-option>
                      <a-select-option value="batteryDisassembly">
                        状态正常-电池拆解
                      </a-select-option>
                      <a-select-option value="pressureDrop">
                        掉压失效-终止测试
                      </a-select-option>
                      <a-select-option value="abnormalHot">
                        异常发热-终止测试
                      </a-select-option>
                      <a-select-option value="openShellAndLeak">
                        开壳漏液-终止测试
                      </a-select-option>
                      <a-select-option value="shellRust">
                        壳体生锈-终止测试
                      </a-select-option>
                      <a-select-option value="operationError">
                        作业错误-终止测试
                      </a-select-option>
                      <a-select-option value="thermalRunaway">
                        热失控-终止测试
                      </a-select-option>
                      <a-select-option value="acrException">
                        内阻异常-终止测试
                      </a-select-option>
                    </a-select>
                  </span>

                  <!-- 驳回信息 -->
                  <span v-else-if="item.dataIndex === 'rejectMsg'">
                    <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
                      <template slot="title">
                        {{text}}
                      </template>
                      <div style="max-width: 100px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;color: black;">
                        {{text}}
                      </div>
                    </a-tooltip>
                  </span>

                  <span v-else-if="item.dataIndex === 'sampleVideo'">
                    <a style="color: green;text-align: center;" @click="openFileOrDownload(record.sampleVideo.id, record.sampleVideo.name)">{{ record.sampleVideo.name }}</a>
                    <a-popconfirm
                      placement="topRight"
                      ok-text="删除"
                      cancel-text="取消"
                      @confirm="deleteCodeVideo(singleSafetyTest, record)">
                      <template slot="title"> 确认删除测试视频"{{ record.sampleVideo.name }}"吗 </template>
                      <a-icon v-if="record.sampleVideo.name && editFlag" type="close" style="float: right;padding: 5px 0px 0px 5px" />
                    </a-popconfirm>
									</span>
                  <span v-else-if="item.dataIndex === 'sampleAttachment'">
                    <a style="color: green;text-align: center;" @click="openFileOrDownload(record.sampleAttachment.id, record.sampleAttachment.name)">{{ record.sampleAttachment.name }}</a>
                    <a-popconfirm
                      placement="topRight"
                      ok-text="删除"
                      cancel-text="取消"
                      @confirm="deleteCodeAttachment(singleSafetyTest, record)">
                      <template slot="title"> 确认删除过程数据"{{ record.sampleAttachment.name }}"吗 </template>
                      <a-icon v-if="record.sampleAttachment.name && editFlag" type="close" style="float: right;padding: 5px 0px 0px 5px" />
                    </a-popconfirm>
									</span>

                  <!-- 图片上传 -->
                  <span v-else-if="picOrVidMenu[item.dataIndex]">
                    <span v-if="!editFlag && !record.samplePicture[item.dataIndex].id ">
                      <a style="color: darkgrey;">上传</a>
                    </span>
                    <span v-else-if="!record.samplePicture[item.dataIndex].id && editFlag" style="display: flex;justify-content: center; align-items: center;">
                      <a-upload
                        name="file"
                        :headers="headers"
                        :data="uploadData"
                        :action="picOrVidPostUrl"
                        :before-upload="beforeUploadPicture"
                        :multiple="false"
                        :showUploadList="false"
                        @change="uploadPicture($event, item.dataIndex, index, singleSafetyTest)"
                        accept=".jpg,.png,.gif">
                        <a>上传</a>
                      </a-upload>
                    </span>
                    <span v-else-if="record.samplePicture[item.dataIndex].uid">
                      <a-upload
                        list-type="picture-card"
                        class="avatar-uploader"
                        :disabled="!editFlag"
                        @change="deletePicture(item.dataIndex, index, singleSafetyTest)"
                        :fileList="[record.samplePicture[item.dataIndex]]"
                        @preview="handlePreview">
                      </a-upload>
                    </span>
                    <span v-else>
                      <a style="color: green" @click="openFileOrDownload(record.samplePicture[item.dataIndex].id,record.samplePicture[item.dataIndex].name)">{{ record.samplePicture[item.dataIndex].name }}</a>
                        <a-popconfirm
                          placement="topRight"
                          ok-text="删除"
                          cancel-text="取消"
                          @confirm="deletePicture(item.dataIndex, index, singleSafetyTest)">
                          <template slot="title"> 确认删除文件"{{ record.samplePicture[item.dataIndex].name }}"吗 </template>
                          <a-icon v-if="editFlag" type="close" style="float: right;padding: 5px 0px 0px 5px" />
                        </a-popconfirm>
                     </span>
                  </span>

                  <!-- 输入框上传 -->
                  <a-input v-else
                    v-model="singleSafetyTest.testData[index][item.dataIndex]"
                    :disabled="!editFlag"
                    @blur="updateStDataAtReview(singleSafetyTest, singleSafetyTest.testData[index])"
                  />
								</span>
              </a-table>
              </div>
            </div>
            <a-divider v-if="singleIndex !== originalData.length - 1" style="font-weight: bold;color: black" />

         </a-spin>
        </div>
      </div>
    </div>

    <!-- 测试数据选择弹窗 end  -->
    <!-- 预览视频/图片  -->
    <a-drawer
      :bodyStyle="{ height: '100%' }"
      placement="right"
      :closable="false"
      width="70%"
      :visible="filePreviewVisible"
      @close="filePreviewVisible = false"
    >
      <iframe :src="iframeUrl" width="100%" height="100%"></iframe>
    </a-drawer>

    <div>
      <a-modal :visible="previewVisible" :footer="null" @cancel="handlePreviewCancel">
        <img alt="example" style="width: 100%" :src="previewImage"/>
      </a-modal>

      <!-- 视频的测试编码选择  -->
      <a-modal
        title="测试视频"
        width="30%"
        :height="200"
        :bodyStyle="{ padding: 0 }"
        :visible="chooseCodeVideoVisible"
        style="padding: 0"
        :maskClosable="false"
        :centered="true"
        @cancel="() => (chooseCodeVideoVisible = false)"
      >
        <div class="child-table">
          <p style="margin-left: 8px;color: red">提示：已有测试视频的电芯再次勾选并上传成功后，原有的测试视频会被覆盖</p>
          <a-table
            :columns="chooseCodeColumns"
            :dataSource="originalData[originalData.length-1].testData"
            class="mt10"
            bordered
            :rowKey="record => record.cellTestCode"
            :rowSelection="{ selectedRowKeys: selectedRowCodeVideoKeys, onChange: onSelectCodeVideoChange }"
            :pagination="false"
          >
          </a-table>
        </div>
        <template slot="footer" slot-scope="text, record">
          <a-button :style="{marginRight: selectedRowCodeVideoKeys.length === 0 ? '0px' : '8px'}" @click="() => (chooseCodeVideoVisible = false)">
            取消
          </a-button>
          <a-button v-if="selectedRowCodeVideoKeys.length === 0" type="primary" @click="chooseAtLeastOneCode">确定</a-button>
          <a-upload
            v-else
            name="file"
            :headers="headers"
            :customRequest="handleUpload"
            :data="uploadData"
            :action="picOrVidPostUrl"
            :before-upload="beforeUploadVideo"
            :multiple="false"
            :disabled="videoLoading || !editFlag"
            :showUploadList="false"
            @change="uploadCodeVideo($event, originalData[1])"
            accept=".mp4,.avi,.wmv,.mov">
            <a-spin :spinning="videoLoading">
              <a-button type="primary">确定</a-button>
            </a-spin>
          </a-upload>
        </template>
      </a-modal>
      <!-- 附件的测试编码选择  -->
      <a-modal
        title="过程数据"
        width="30%"
        :height="200"
        :bodyStyle="{ padding: 0 }"
        :visible="chooseCodeAttachVisible"
        style="padding: 0"
        :maskClosable="false"
        :centered="true"
        @cancel="() => (chooseCodeAttachVisible = false)"
      >
        <div class="child-table">
          <p style="margin-left: 8px;color: red">提示：已有过程数据的电芯再次勾选并上传成功后，原有的过程数据会被覆盖</p>
          <a-table
            :columns="chooseCodeColumns"
            :dataSource="originalData[originalData.length-1].testData"
            class="mt10"
            bordered
            :rowKey="record => record.cellTestCode"
            :rowSelection="{ selectedRowKeys: selectedRowCodeAttachKeys, onChange: onSelectCodeAttachChange }"
            :pagination="false"
          >
          </a-table>
        </div>
        <template slot="footer" slot-scope="text, record">
          <a-button :style="{marginRight: selectedRowCodeAttachKeys.length === 0 ? '0px' : '8px'}" @click="() => (chooseCodeAttachVisible = false)">
            取消
          </a-button>
          <a-button v-if="selectedRowCodeAttachKeys.length === 0" type="primary" @click="chooseAtLeastOneCode">确定</a-button>
          <a-upload
            v-else
            name="file"
            :headers="headers"
            :customRequest="$event => handleUploadOfAttach($event, originalData[1], 1)"
            :data="uploadData"
            :action="picOrVidPostUrl"
            :multiple="false"
            :disabled="attachLoading || !editFlag"
            :showUploadList="false"
            @change="uploadCodeAttachment($event, originalData[1])">
            <a-spin :spinning="attachLoading">
              <a-button type="primary">确定</a-button>
            </a-spin>
          </a-upload>
        </template>
      </a-modal>
    </div>

  </div>
</template>

<script>
import moment from "moment"
import { STable } from "@/components"
import { mixin } from "../mixin/index"
import { downloadfile1 } from "@/utils/util"
import { formatDate } from "@/utils/format";
import { getMinioPreviewUrl } from "@/api/modular/system/fileManage";
import axios from "axios";
import Vue from "vue";
import {
  getSubmitData,
  updatePicVidAttAtReview,
  updateStDataAtReview
} from "@/api/modular/system/safetyTestReviewManager";

export default {
  components: {
    STable
  },
  props: {
    stReviewBean: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      chooseCodeColumns: [
        {
          title: "序号",
          dataIndex: "index",
          align: "center",
          customRender: (text, record, index) => `${index + 1}`
        },
        {
          title: "测试编码",
          dataIndex: "cellTestCode",
          align: "center",
          scopedSlots: {
            customRender: "cellTestCode"
          }
        }
      ],
      selectedRowCodeVideoKeys: [],
      selectedRowCodeAttachKeys: [],
      chooseCodeVideoVisible: false,
      chooseCodeAttachVisible: false,
      editFlag: false,
      previewImage: '',
      previewVisible: false,
      originalData:[],
      uploadingVideoProgressList: [],
      columnList:[],
      columnDataList:[],
      showProgressBar:false,
      uploadingStage:null,
    }
  },
  mixins: [mixin],
  created() {},

  methods: {
    loadData(init, editFlag) {
      this.editFlag = editFlag
      // this.modalLoading = true
      getSubmitData(this.stReviewBean)
        .then(res => {
          if (!res.success) return this.$message.error("错误提示：" + res.message)
          this.originalData = res.data

          this.originalData.forEach(o => {
            if (o.testData) {
              o.testData = JSON.parse(o.testData)
            }
            if (o.attachment) {
              o.attachment = JSON.parse(o.attachment)
            } else {
              o.attachment = []
            }
          })

          for (const resKey in this.originalData) {
            let curTestDatas = this.originalData[resKey].testData
            if (!curTestDatas) {
              continue
            }
            curTestDatas.forEach(v => {
              // 图片预览增加token
              if (v.samplePicture) {
                Object.keys(v.samplePicture).forEach(function(key) {
                  let authString = "&Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************.P1JWgtRVk1sTPPLiCgZNuleYyPZRf2ooByC_mmu9scs6SVbpJHgSsKd8AtscjDwg3Fw7D4QN31vgtA5jeedj3g"
                  if (v.samplePicture[key].thumbUrl && v.samplePicture[key].thumbUrl.indexOf(authString) === -1) {
                    v.samplePicture[key].thumbUrl += authString
                  }
                });
              }
            })
          }

          this.originalData.forEach(data => {
            this.columnList.push(data.testData ? this.handleColums(data.testData)[0] : "")
            this.columnDataList.push(data.testData ? this.handleColums(data.testData)[1] : "")
          })

        })
        .finally(() => {
          this.modalLoading = false
          if (init) {
            for (let i = 0; i < this.originalData.length; i++) {
              this.uploadingProgressList.push({
                uploadStatusOfAttach: 'done',
                percentOfAttach: 0,
              })
              this.uploadingVideoProgressList.push({
                uploadStatus: 'done',
                percent: 0,
              })
            }
          }
        })
    },

    // 处理表头新
    handleColums(value) {
      let rejectMsgFlag = value.some(item => item.hasOwnProperty('rejectMsg'));
      let temColuns = []
      const temList = []

      for (let i in value[0]) {
        const temObj = {
          title: this.tableNameMenu[i],
          dataIndex: i,
          width: "100px",
          align: "center",
          scopedSlots: {
            customRender: i
          }
        }

        const childrenObj = {
          title: i.replaceAll(/[^0-9]/g, ""),
          dataIndex: i,
          width: "100px",
          align: "center",
          scopedSlots: {
            customRender: i
          }
        }

        const childrenObj1 = {
          title: this.tableNameMenu[i.replaceAll(/[0-9]/g, "")] + i.replaceAll(/[^0-9]/g, ""),
          dataIndex: i,
          width: "100px",
          align: "center",
          scopedSlots: {
            customRender: i
          }
        }

        const temObj1 = {
          title: this.tableNameMenu[i.replaceAll(/[0-9]/g, "")],
          dataIndex: i.replaceAll(/[0-9]/g, ""),
          width: "100px",
          align: "center",
          children: [childrenObj]
        }

        if (
          i === "heightType" ||
          i === "middleCheck" ||
          i === "isMiddleClick" ||
          i === "checkData" ||
          i === "timeOfFillInnerres" ||
          i === "timeOfFillInnerres2" ||
          i === "sampleVideo" ||
          i === "sampleAttachment" ||
          i === "samplePicture" ||
          i === "rejectMsg" ||
          i === "safetyTestReviewId"
        )
          continue


        if (i.match(/\d/g)) {
          const temIndex = temColuns.findIndex(v => v.dataIndex === i.replaceAll(/[0-9]/g, ""))
          if (temIndex === -1) {
            temColuns.push(temObj1)
            temList.push(childrenObj1)
          } else {
            temColuns[temIndex].children.push(childrenObj)
            temList.push(childrenObj1)
          }
        } else {
          temColuns.push(temObj)
          temList.push(temObj)
        }
      }

      const chilList = []
      const temColuns1 = []
      // 划分尺寸表头
      temColuns.forEach(v => {
        if (v.dataIndex === "timeOfFillInnerres" || v.dataIndex === "timeOfFillInnerres2") {
          return
        }
        if (
          v.dataIndex === "cellTestCode" ||
          v.dataIndex === "alias" ||
          v.dataIndex === "batteryStatus" ||
          v.dataIndex === "beforeVoltage" ||
          v.dataIndex === "beforeInnerres" ||
          v.dataIndex === "afterVoltage" ||
          v.dataIndex === "afterInnerres" ||
          v.dataIndex === "volume" ||
          v.dataIndex === "weight" ||
          v.dataIndex === "heightType" ||
          v.dataIndex === "isolateres"
        ) {
          if (v.dataIndex === "alias" || v.dataIndex === "isolateres" || v.dataIndex === "batteryStatus") {
            v.width = "100px"
          }
          if (v.dataIndex === "cellTestCode") {
            v.width = "175px"
          }
          return temColuns1.push(v)
        }
        chilList.push(v)
      })

      temColuns = temColuns1

      if (chilList.length !== 0) {
        temColuns.push({
          title: "尺寸/mm",
          dataIndex: "dimension",
          align: "center",
          children: chilList
        })
      }

      // 增加样品视频和附件列,并且排在样品照片列前面
      let needTestFields = Object.keys(value[0])
      if (needTestFields.findIndex(v => v === "sampleVideo") > 0) {
        let videoItem = {
          title: "测试视频",
          dataIndex: "sampleVideo",
          width: "100px",
          align: "center",
          scopedSlots: {
            customRender: "sampleVideo"
          }
        }
        temColuns.push(videoItem)
        temList.push(videoItem)
      }
      if (needTestFields.findIndex(v => v === "sampleAttachment") > 0) {
        let attachmentItem = {
          title: "过程数据",
          dataIndex: "sampleAttachment",
          width: "100px",
          align: "center",
          scopedSlots: {
            customRender: "sampleAttachment"
          }
        }
        temColuns.push(attachmentItem)
        temList.push(attachmentItem)
      }

      // 增加样品照片列
      const pictureChilList = []
      if (value[0].samplePicture) {
        for (let i in value[0].samplePicture) {
          const temObj = {
            title: this.picOrVidMenu[i],
            dataIndex: i,
            align: "center",
            width: "100px",
            scopedSlots: {
              customRender: i
            }
          }
          pictureChilList.push(temObj)
          temList.push(temObj)
        }
      }
      if (pictureChilList.length !== 0) {
        temColuns.push({
          title: "样品照片",
          dataIndex: "samplePicture",
          align: "center",
          children: pictureChilList
        })
      }

      // 获取【测试编码】列索引
      let insertIndexC = temColuns.findIndex(item => item.dataIndex === 'cellTestCode');
      let insertIndexL = temList.findIndex(item => item.dataIndex === 'cellTestCode');
      if (insertIndexC !== -1 && insertIndexL !== -1) {
        // 获取【电芯状态】列
        let batteryTitleC = temColuns.filter(item => item.dataIndex === 'batteryStatus');
        let batteryTitleL = temList.filter(item => item.dataIndex === 'batteryStatus');
        // 移除【电芯状态】列
        let indexToRemoveC = temColuns.findIndex(item => item.dataIndex === 'batteryStatus');
        let indexToRemoveL = temList.findIndex(item => item.dataIndex === 'batteryStatus');
        if (indexToRemoveC !== -1) {
          temColuns.splice(indexToRemoveC, 1);
        }
        if (indexToRemoveL !== -1) {
          temList.splice(indexToRemoveL, 1);
        }
        // 将【电芯状态】列添加到【测试编码】列后面
        if (batteryTitleC.length > 0 && batteryTitleL.length > 0) {
          temColuns.splice(insertIndexC + 1, 0, batteryTitleC[0]);
          temList.splice(insertIndexL + 1, 0, batteryTitleL[0]);
          // 在【电芯状态】列后添加【驳回信息】列
          if (rejectMsgFlag) {
            let rejectMsgItem = {
              title: "驳回信息",
              dataIndex: "rejectMsg",
              width: "100px",
              align: "center",
              scopedSlots: {
                customRender: "rejectMsg"
              }
            }
            temColuns.splice(insertIndexC + 2, 0, rejectMsgItem);
            temList.splice(insertIndexL + 2, 0, rejectMsgItem);
          }
        }
      }
      return [temColuns, temList]
    },

    deleteAttachment(event, curSafetyTest, fileItem) {
      curSafetyTest.attachment = curSafetyTest.attachment.filter(o => o.id !== fileItem.id)
      let update = {}
      update.reviewIdList = curSafetyTest.safetyTestReviewIdList
      update.attachment = JSON.stringify(curSafetyTest.attachment)
      updatePicVidAttAtReview(update, 'delete', 'attachment').then(res => {
        if (res.success) {
          this.loadData(false, true)
          this.$message.success("删除成功")
        } else {
          this.$message.error("删除失败：" + res.message)
        }
      })
    },

    chooseAtLeastOneCode() {
      return this.$message.warning("请至少选择一个电芯")
    },
    onSelectCodeVideoChange(selectedRowKeys, selectedRows) {
      this.selectedRowCodeVideoKeys = selectedRowKeys
    },
    onSelectCodeAttachChange(selectedRowKeys, selectedRows) {
      this.selectedRowCodeAttachKeys = selectedRowKeys
    },

    uploadAttachment(info, curSafetyTest) {
      this.uploadingStage = curSafetyTest.stage
      this.attachLoading = true
      if (info.file.status === "done") {
        let file = info.file
        let update = {}
        curSafetyTest.attachment.push({
          id: file.response.data,
          name: file.name,
          time: formatDate(new Date(), true)
        })
        update.reviewIdList = curSafetyTest.safetyTestReviewIdList
        update.attachment = JSON.stringify(curSafetyTest.attachment)
        updatePicVidAttAtReview(update, 'add', 'attachment').then(res => {
          if (res.success) {
            this.loadData(false, true)
            this.$message.success(`${info.file.name} 上传成功`)
          } else {
            this.$message.error("上传失败：" + res.message)
          }
          setTimeout(() => {
            this.attachLoading = false
          },500)
        })
      } else if (info.file.status === "error") {
        this.attachLoading = false
        this.$message.error(`${info.file.name} 上传失败`)
      } else {
        this.attachLoading = false
      }
    },

    handleTcVideoUpload(options, singleSafetyTest, index) {
      const { file, onSuccess, onError } = options;
      const formData = new FormData();
      formData.append('file', file);
      formData.append('bucket','safetylab');

      axios.post('/api/sysFileInfo/minioUpload', formData, {
        headers: {
          // 'Content-Type': 'multipart/form-data;',
          Authorization: 'Bearer ' + Vue.ls.get('Access-Token'),
        },
        // data:this.uploadData,
        onUploadProgress: (progressEvent) => {
          this.videoLoading = true
          this.uploadingVideoProgressList[index].uploadStatus = 'uploading'
          if (progressEvent.total > 0) {
            this.uploadingVideoProgressList[index].percent = Math.round((progressEvent.loaded / progressEvent.total ) * 100)  == 100?99:Math.round((progressEvent.loaded / progressEvent.total ) * 100);
          }
        },
      })
        .then((response) => {
          onSuccess(response.data, file);
          this.uploadingVideoProgressList[index].percent = 100;
          setTimeout(() => {
            this.videoLoading = false
            this.uploadingVideoProgressList[index].percent = 0;
            this.uploadingStage = null
            this.uploadingVideoProgressList[index].uploadStatus = 'done'
          }, 2000)
          // 重置进度条
        })
        .catch((error) => {
          onError(error);
          this.uploadingVideoProgressList[index].percent = 0; // 重置进度条
        });
    },

    uploadVideo(info, curSafetyTest) {
      this.uploadingStage = curSafetyTest.stage
      this.videoLoading = true
      if (info.file.status === "done") {
        let file = info.file
        let update = {}
        update.reviewIdList = curSafetyTest.safetyTestReviewIdList
        update.videoId = file.response.data
        update.videoName = file.name
        updatePicVidAttAtReview(update, 'add', 'video').then(res => {
          if (res.success) {
            this.loadData(false, true)
            this.$message.success(`${info.file.name} 上传成功`)
          } else {
            this.$message.error("上传失败：" + res.message)
          }
          setTimeout(() => {
            this.videoLoading = false
          },500)
        })
      } else if (info.file.status === "error") {
        this.videoLoading = false
        this.$message.error(`${info.file.name} 上传失败`)
      } else {
        this.videoLoading = false
      }
    },

    deleteVideo(event, curSafetyTest) {
      let update = {}
      update.reviewIdList = curSafetyTest.safetyTestReviewIdList
      updatePicVidAttAtReview(update, 'delete', 'video').then(res => {
        if (res.success) {
          this.loadData(false, true)
          this.$message.success("删除成功")
        } else {
          this.$message.error("删除失败：" + res.message)
        }
      })
    },

    async updateStDataAtReview(curSafetyTest, curBatteryTestData) {
      const params = {
        reviewIdList: curSafetyTest.safetyTestReviewIdList,
        cellCode: curBatteryTestData.cellTestCode,
        testData: JSON.stringify([curBatteryTestData])
      }
      updateStDataAtReview(params).then(res => {
        this.loadData(false, true)
        if (res.success) {
          this.$message.success("修改成功")
        } else {
          return this.$error({ content: res.message })
        }
      })
    },

    uploadCodeAttachment(info, curSafetyTest) {
      this.chooseCodeAttachVisible = false
      this.attachLoading = true
      if (info.file.status === "done") {
        let file = info.file
        let update = {}
        let uploadAttachment = {
          id: file.response.data,
          name: file.name,
          time: formatDate(new Date(), true)
        }
        update.reviewIdList = curSafetyTest.safetyTestReviewIdList
        update.attachment = JSON.stringify(uploadAttachment)
        update.cellTestCodes = this.selectedRowCodeAttachKeys.join(',')
        updatePicVidAttAtReview(update, 'add', 'newestAttachment').then(res => {
          if (res.success) {
            this.selectedRowCodeAttachKeys = []
            this.loadData(false, true)
            this.$message.success(`${info.file.name} 上传成功`)
          } else {
            this.$message.error("上传失败：" + res.message)
          }
          setTimeout(() => {
            this.attachLoading = false
          },500)
        })
      } else if (info.file.status === "error") {
        this.attachLoading = false
        this.$message.error(`${info.file.name} 上传失败`)
      } else {
        this.attachLoading = false
      }
    },

    deleteCodeVideo(curSafetyTest, battery) {
      let update = {}
      update.reviewIdList = curSafetyTest.safetyTestReviewIdList
      update.cellTestCodes = battery.cellTestCode
      updatePicVidAttAtReview(update, 'delete', 'newestVideo').then(res => {
        if (res.success) {
          this.loadData(false, true)
          this.$message.success("删除成功")
        } else {
          this.$message.error("删除失败：" + res.message)
        }
      })
    },

    deleteCodeAttachment(curSafetyTest, battery) {
      let update = {}
      update.reviewIdList = curSafetyTest.safetyTestReviewIdList
      update.cellTestCodes = battery.cellTestCode
      updatePicVidAttAtReview(update, 'delete', 'newestAttachment').then(res => {
        if (res.success) {
          this.loadData(false, true)
          this.$message.success("删除成功")
        } else {
          this.$message.error("删除失败：" + res.message)
        }
      })
    },

    uploadCodeVideo(info, curSafetyTest) {
      this.chooseCodeVideoVisible = false
      this.videoLoading = true
      if (info.file.status === "done") {
        let file = info.file
        let update = {}
        update.reviewIdList = curSafetyTest.safetyTestReviewIdList
        update.videoId = file.response.data
        update.videoName = file.name
        update.cellTestCodes = this.selectedRowCodeVideoKeys.join(',')
        updatePicVidAttAtReview(update, 'add', 'newestVideo').then(res => {
          if (res.success) {
            this.selectedRowCodeVideoKeys = []
            this.loadData(false, true)
            this.$message.success(`${info.file.name} 上传成功`)
          } else {
            this.$message.error("上传失败：" + res.message)
          }
          setTimeout(() => {
            this.videoLoading = false
          },500)
        })
      } else if (info.file.status === "error") {
        this.videoLoading = false
        this.$message.error(`${info.file.name} 上传失败`)
      } else {
        this.videoLoading = false
      }
    },

    uploadPicture(info, field, index, curSafetyTest) {
      if (info.file.status === "done") {
        let file = info.file
        let update = {}
        update.reviewIdList = curSafetyTest.safetyTestReviewIdList
        update.cellCode = curSafetyTest.testData[index].cellTestCode
        update.pictureId = file.response.data
        update.pictureName = file.name
        this.modalLoading = true
        updatePicVidAttAtReview(update, 'add', field).then(res => {
          if (res.success) {
            this.loadData(false, true)
            this.$message.success(`${info.file.name} 上传成功`)
          } else {
            this.$message.error("上传失败：" + res.message)
          }
          setTimeout(() => {
            this.modalLoading = false
          },500)
        })
      } else if (info.file.status === "error") {
        this.$message.error(`${info.file.name} 上传失败`)
      }
    },

    deletePicture(field, index, curSafetyTest) {
      let update = {}
      update.reviewIdList = curSafetyTest.safetyTestReviewIdList
      update.cellCode = curSafetyTest.testData[index].cellTestCode
      this.modalLoading = true
      updatePicVidAttAtReview(update, 'delete', field).then(res => {
        if (res.success) {
          this.loadData(false, true)
          this.$message.success("删除成功")
        } else {
          this.$message.error("删除失败：" + res.message)
        }
        setTimeout(() => {
          this.modalLoading = false
        },500)
      })
    },

    handlePreviewCancel() {
      this.previewVisible = false;
    },

    async handlePreview(file) {
      getMinioPreviewUrl(file.id).then(res => {
        if (res.data) {
          this.previewImage = res.data.replace("http://***********:9000/", "/minioDownload/")
          setTimeout(() => {
            this.previewVisible = true
          }, 100)
        } else {
          this.$message.error("服务器错误，请联系管理员！")
        }
      })
    },
  }
}
</script>

<style lang="less" scoped>
@import "../style/calendar.less";

/* 主容器样式 - 添加垂直滚动条 */
.detail-container {
  max-height: 65vh; /* 设置最大高度，适应模态框 */
  overflow-y: auto; /* 垂直滚动条 */
  padding: 0 16px;
}

/* 内容包装器 */
.content-wrapper {
  min-height: 100%;
}

/* 表格包装器 - 添加水平滚动条 */
.table-wrapper {
  overflow-x: auto; /* 水平滚动条 */
  margin: 10px 0;
}

/* 表格容器优化 */
.auto-table {
  min-width: 100%;

  /deep/ .ant-table {
    /* 确保表格可以水平滚动 */
    .ant-table-content {
      overflow-x: auto;
    }

    /* 优化表格在滚动时的显示 */
    .ant-table-body {
      overflow-x: auto;
      overflow-y: visible;
    }
  }
}

/* 滚动条样式优化 */
.detail-container::-webkit-scrollbar,
.table-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.detail-container::-webkit-scrollbar-track,
.table-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.detail-container::-webkit-scrollbar-thumb,
.table-wrapper::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.detail-container::-webkit-scrollbar-thumb:hover,
.table-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/deep/.ant-table-pagination.ant-pagination {
  float: right;
  margin: 50px 0px 20px 0px;
}
/deep/.ant-upload-list-picture-card .ant-upload-list-item {
  width: 80px;
  height: 80px;
  margin: 0px 0px 0px 0px;
}
/deep/.ant-upload-list-picture-card-container {
  width: 80px;
  height: 70px;
  margin: 0px 0px 7px 0px;
  overflow-wrap: break-word;
}
/deep/.ant-upload-picture-card-wrapper {
  zoom: 1;
  display: ruby;
}
</style>
