<template>
  <a-modal :visible="true" centered title="导出字段选择" :width="700" @ok="handleSubmit"
    @cancel="handleCancel">
    <div>
      <a-transfer style="width: 100%;" :data-source="options" :titles="['选择项', '选中']" :target-keys="selectedColList"
        :render="item => item.title" @change="handleChange" />
    </div>
    <template slot="footer">
      <a-button @click="handleCancel">
        取消
      </a-button>
      <a-button type="primary" @click="handleSubmit">
        导出
      </a-button>
    </template>
  </a-modal>

</template>
<script>
  export default {
    data() {
      return {
        selectedColList: [],
        options: [
          { key: 'judgeResult', title: '测试结果判定' },
          { key: 'judgeResultAI', title: 'AI测试结果判定' },
          { key: 'testProjectName', title: '测试项目名称（客户命名）' },
          { key: 'testContent', title: '测试项目' },
          { key: 'tLimsOrdtaskId', title: '测试单号选择' },
          { key: 'firstCategory', title: '一级分类' },
          { key: 'secondCategory', title: '二级分类' },
          { key: 'testName', title: '测试项目名称（内部命名）' },
          { key: 'realTestNumber', title: '实际测试数量/pcs' },
          { key: 'judgeStandard', title: '判定标准' },
          { key: 'testStatus', title: '测试状态' },
          { key: 'testResultData', title: '测试结果' },
          { key: 'remark', title: '备注' },
          { key: 'bolMolEolNumList[0].bol', title: '测试计划电芯样品数（计划）- BOL', title0: '测试计划电芯样品数（计划）', title1: 'BOL' },
          { key: 'bolMolEolNumList[0].mol', title: '测试计划电芯样品数（计划）- MOL', title0: '测试计划电芯样品数（计划）', title1: 'MOL' },
          { key: 'bolMolEolNumList[0].eol', title: '测试计划电芯样品数（计划）- EOL', title0: '测试计划电芯样品数（计划）', title1: 'EOL' },
          { key: 'bolMolEolNumList[1].bol', title: '测试计划电芯样品数（实际）- BOL', title0: '测试计划电芯样品数（实际）', title1: 'BOL' },
          { key: 'bolMolEolNumList[1].mol', title: '测试计划电芯样品数（实际）- MOL', title0: '测试计划电芯样品数（实际）', title1: 'MOL' },
          { key: 'bolMolEolNumList[1].eol', title: '测试计划电芯样品数（实际）- EOL', title0: '测试计划电芯样品数（实际）', title1: 'EOL' },
          { key: 'bolMolEolNumList[2].typeValue', title: '设备通道类型' },
          { key: 'bolMolEolNumList[2].bol', title: '单个测试项目下需求通道数（计划）- BOL', title0: '单个测试项目下需求通道数（计划）', title1: 'BOL' },
          { key: 'bolMolEolNumList[2].mol', title: '单个测试项目下需求通道数（计划）- MOL', title0: '单个测试项目下需求通道数（计划）', title1: 'MOL' },
          { key: 'bolMolEolNumList[2].eol', title: '单个测试项目下需求通道数（计划）- EOL', title0: '单个测试项目下需求通道数（计划）', title1: 'EOL' },
          { key: 'bolMolEolNumList[3].typeValue', title: '温箱通道类型' },
          { key: 'bolMolEolNumList[3].bol', title: '单个测试项目下需求温箱数（计划）- BOL', title0: '单个测试项目下需求温箱数（计划）', title1: 'BOL' },
          { key: 'bolMolEolNumList[3].mol', title: '单个测试项目下需求温箱数（计划）- MOL', title0: '单个测试项目下需求温箱数（计划）', title1: 'MOL' },
          { key: 'bolMolEolNumList[3].eol', title: '单个测试项目下需求温箱数（计划）- EOL', title0: '单个测试项目下需求温箱数（计划）', title1: 'EOL' },
          // { key: 'bolMolEolNumList[4].typeValue', title: '夹具类型' },
          { key: 'bolMolEolNumList[4].bol', title: '夹具数量（计划）- BOL', title0: '夹具数量（计划）', title1: 'BOL' },
          { key: 'bolMolEolNumList[4].mol', title: '夹具数量（计划）- MOL', title0: '夹具数量（计划）', title1: 'MOL' },
          { key: 'bolMolEolNumList[4].eol', title: '夹具数量（计划）- EOL', title0: '夹具数量（计划）', title1: 'EOL' },
          { key: 'cellBatch', title: '批次' },
          { key: 'testLocation', title: '测试地点' },
          { key: 'testDays', title: '测试天数/D' },
          { key: 'planStartTime', title: '计划开始时间' },
          { key: 'planEndTime', title: '计划结束时间' },
          { key: 'realStartTime', title: '测试开始时间' },
          { key: 'realEndTime', title: '测试结束时间' }
        ]
      }
    },
    methods: {

      handleChange(nextTargetKeys, direction, moveKeys) {
        this.selectedColList = nextTargetKeys
      },
      handleSubmit() {
        if(this.selectedColList.length === 0) return this.$message.info('请先从左边的选择项中选中需要导出的字段')
        const params = []
        this.options.forEach(v => {
          if (this.selectedColList.includes(v.key)) {
            const param = {
              dataIndex: v.key,
              title0: v.title,
            }
            if (v.title0) param.title0 = v.title0
            if (v.title1) param.title1 = v.title1

            params.push(param)
          }
        })
        this.$emit('submit', params)
      },
      handleCancel() {
        this.$emit('cancel')
      }
    }
  }
</script>
<style lang="less" scoped>
  /deep/.ant-transfer .ant-transfer-list {
    width: calc(50% - 20px);
    height: 300px;
  }

  /deep/.ant-modal-footer {
    padding-top: 0;
  }
</style>