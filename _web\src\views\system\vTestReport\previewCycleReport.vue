<template>
  <div ref="wrapper" class="wrapper">
    <div class="flex-sb-center-row">
      <div class="head_title">{{ record.type + ": " + record.reportName }}</div>
    </div>

    <div class="all-wrapper">
      <div class="left-content">

        <div class="thumbnail-content">

          <div class="flex-column-center" v-for="(item,index) in cycleArr" @click="handleClickThumbnail(index + 1,'cycle')">
            <div class="thumbnail-block" :class="activeKey === index + 1 ? 'active' : ''">
              <div class="title">{{item.label}}</div>
              <div :id="`${item.id}Thumbnail`" :ref="`${item.id}Thumbnail`" class="normal-thumbnail-charts"></div>
            </div>
            <div v-show="activeKey === index + 1" class="kailong"></div>
          </div>

        </div>

        <div class="block mt10" v-show="activeKey === 1">
          <pageComponent class="mt10" editObj="growth"  @down="handleNormalDown('disChargeCapacity')" @edit="handleEditEcharts('disChargeCapacity')"></pageComponent>
          <div id="disChargeCapacity" ref="disChargeCapacity" class="charts mt10" ></div>
        </div>
       
        <div class="block mt10" v-show="activeKey === 2">
          <pageComponent class="mt10" editObj="growth"  @down="handleNormalDown('capacityHoldRate')" @edit="handleEditEcharts('capacityHoldRate')"></pageComponent>
          <div id="capacityHoldRate" ref="capacityHoldRate" class="charts mt10" ></div>
        </div>
        
        <div class="block mt10" v-show="activeKey === 3">
          <pageComponent class="mt10" editObj="growth"  @down="handleNormalDown('disChargeEnergy')" @edit="handleEditEcharts('disChargeEnergy')"></pageComponent>
          <div id="disChargeEnergy" ref="disChargeEnergy" class="charts mt10" ></div>
        </div>

        <div class="block mt10" v-show="activeKey === 4">
          <pageComponent class="mt10" editObj="growth"  @down="handleNormalDown('energyHoldRate')" @edit="handleEditEcharts('energyHoldRate')"></pageComponent>
          <div id="energyHoldRate" ref="energyHoldRate" class="charts mt10" ></div>
        </div>



<!--        <a-button type="primary" class="mr10 mt10" @click="handleNormalDown('coulombicEfficiency')">下载</a-button>
        <div id="coulombicEfficiency" ref="coulombicEfficiency" class="mt10"
          ></div>-->

      </div>
      <div class="right-content">
        <div class="block" id="export">
          <div class="flex-column">
            <div>
              <div>原始数据</div>
              <!--							<div style="float: right;margin-top: -25px">-->
              <!--								<a-button type="primary" @click="() => (update = true)" v-if="!update">修改数据</a-button>-->
              <!--								<a-button type="primary" @click="cancel" v-if="update" style="margin-right: 20px">取消修改</a-button>-->
              <!--								<a-popconfirm-->
              <!--									placement="topRight"-->
              <!--									ok-text="确认"-->
              <!--									cancel-text="取消"-->
              <!--									@confirm="updateData"-->
              <!--									v-if="update"-->
              <!--								>-->
              <!--									<template slot="title">-->
              <!--										<p>确认提交更改吗</p>-->
              <!--									</template>-->
              <!--									<a-button type="primary">提交数据</a-button>-->
              <!--								</a-popconfirm>-->
              <!--							</div>-->

            </div>

            <div class="mt10">
              <a-table class="originalTableClass" :columns="tableColumns" bordered :data-source="tableList"
                :rowKey="record => record.cycle" :pagination="false" > </a-table>
            </div>
            <a-pagination style="margin-top: 20px;float: right;margin-left: auto" v-model="pageNo"
              :page-size-options="pageSizeOptions" :total="totalRows" show-size-changer :page-size="pageSize"
              @change="onPageChange" @showSizeChange="onPageChange">
              <template slot="buildOptionText" slot-scope="props">
                <span v-if="props.value !== totalRows">{{ props.value }}条/页</span>
                <span v-if="props.value === totalRows">全部</span>
              </template>
            </a-pagination>

          </div>

          <div class="flex-column">
            <strong>保持率</strong>
            <div class="mt10">
              <a-table :columns="clcTableColumns" bordered :data-source="holdRateTableList"
                :rowKey="record => record.cycle" :pagination="false" > </a-table>
            </div>
            <a-pagination style="margin-top: 20px;float: right;margin-left: auto" v-model="holdRatePageNo"
              :page-size-options="holdRatePageSizeOptions" :total="holdRateTotalRows" show-size-changer
              :page-size="holdRatePageSize" @change="holdRateOnPageChange" @showSizeChange="holdRateOnPageChange">
              <template slot="buildOptionText" slot-scope="props">
                <span v-if="props.value !== holdRateTotalRows">{{ props.value }}条/页</span>
                <span v-if="props.value === holdRateTotalRows">全部</span>
              </template>
            </a-pagination>
          </div>

<!--          <div class="flex-column">

            <strong>放电容量</strong>
            <div class="mt10">
              <a-table :columns="disChargeCapacityTableColumns" bordered :data-source="disChargeCapacityTableList"
                :rowKey="record => record.cycle" :pagination="false" :scroll="{ y: 500, x: 1800 }"> </a-table>
            </div>

            <strong class="mt10">容量保持率</strong>
            <div class="mt10">
              <a-table :columns="capacityHoldRateTableColumns" bordered :data-source="capacityHoldRateTableList"
                :rowKey="record => record.cycle" :pagination="false" :scroll="{ y: 500, x: 1800 }"> </a-table>
            </div>

            <strong class="mt10">放电能量</strong>
            <div class="mt10">
              <a-table :columns="disChargeEnergyTableColumns" bordered :data-source="disChargeEnergyTableList"
                :rowKey="record => record.cycle" :pagination="false" :scroll="{ y: 500, x: 1800 }"> </a-table>
            </div>

            <strong class="mt10">能量保持率</strong>
            <div class="mt10">
              <a-table :columns="energyHoldRateTableColumns" bordered :data-source="energyHoldRateTableList"
                :rowKey="record => record.cycle" :pagination="false" :scroll="{ y: 500, x: 1800 }"> </a-table>
            </div>

            <strong class="mt10">库伦效率</strong>
            <div class="mt10">
              <a-table :columns="coulombicEfficiencyTableColumns" bordered :data-source="coulombicEfficiencyTableList"
                :rowKey="record => record.cycle" :pagination="false" :scroll="{ y: 500, x: 1800 }"> </a-table>
            </div>

          </div>-->


        </div>
      </div>
    </div>

    <!-- 在线编辑图表 -->
    <div v-if="drawerVisible">
      <PreviewDrawer 
        :screenImageId = "screenImageId"
        :templateParam = "reportChartTemplateList[editObj]"
        :isLegendLeft =  "true"
        :legendOptions="originalLegent[editObj]" 
        :data="editData[editObj].series"
        :original="originalData[editObj]" 
        :editData="editData[editObj]" 
        :checkObj="chartCheckObj[editObj]"
        @submit="handleDrawerSubmit" 
        @reset="handleDrawerReset" 
        @close="() => drawerVisible = false"
        @changeTemplate ="handleChangeTemplate"
        @screenshot="handleScreenshot"
      >
      </PreviewDrawer>
    </div>
    <!-- <pbiReturnTop v-if="isShowReturnTop" @returnTop="handleReturnTop"></pbiReturnTop> -->


  </div>
</template>
<script>
  import {
    getCycleReport,
    getRateStressTestReport,
    testReportGet,
    testReportUpdateDate
  } from "@/api/modular/system/limsManager"
  import { Pagination } from 'ant-design-vue';
  import { mixin,chart,thumbnail } from "./mixin/index.js"
  import {chartTemplate} from "@/views/system/vTestReport/mixin/chartTemplate";

  export default {
    components: {
      'a-pagination': Pagination
    },
    mixins: [mixin,chart,thumbnail,chartTemplate], 
    data: function () {
      return {

        chartCheckObj:{
          disChargeCapacity:{},
          capacityHoldRate:{},
          disChargeEnergy:{},
          energyHoldRate:{},
        },
        

        disChargeCapacityFristInit:true, // 是否是第一次点击编辑图表，此时加载传递到编辑图表里面的数据
        capacityHoldRateFristInit:true, 
        disChargeEnergyFristInit:true, 
        energyHoldRateFristInit:true, 

        update: false,
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        pageNo: 1,
        pageSize: 10,
        totalRows: 50,
        holdRatePageSizeOptions: ['10', '20', '30', '40', '50'],
        holdRatePageNo: 1,
        holdRatePageSize: 10,
        holdRateTotalRows: 50,
        originColumns: [],
        tableColumns: [],
        clcTableColumns: [],
        disChargeCapacityTableColumns: [],
        capacityHoldRateTableColumns: [],
        disChargeEnergyTableColumns: [],
        energyHoldRateTableColumns: [],
        coulombicEfficiencyTableColumns: [],
        originData: [],
        tableList: [],
        holdRateTableList: [],
        disChargeCapacityTableList: [],
        capacityHoldRateTableList: [],
        disChargeEnergyTableList: [],
        energyHoldRateTableList: [],
        coulombicEfficiencyTableList: [],
        echartCurrent: '',
        disChargeCapacityEchartTitle: '',
        capacityHoldRateEchartTitle: '',
        disChargeEnergyEchartTitle: '',
        energyHoldRateEchartTitle: '',
        coulombicEfficiencyEchartTitle: '',
        id: null,
      };
    },
    async mounted() {
      await this.getChartTemplateRelationList(this.$route.query.id,['disChargeCapacity','capacityHoldRate','disChargeEnergy','energyHoldRate'])
      this.init()
    },
    methods: {
      init() {
        this.id = this.$route.query.id
        getCycleReport({ id: this.id, pageNo: this.pageNo, pageSize: this.pageSize, holdRatePageNo: this.holdRatePageNo, holdRatePageSize: this.holdRatePageSize })
          .then(res => {
            this.record = res.data
            this.allDataJson = JSON.parse(res.data.allDataJson)
            this.queryParam = JSON.parse(res.data.queryParam)

            this.echartCurrent = this.queryParam.reportBasic.charge + '' + this.queryParam.reportBasic.chargeUnit + '/' + this.queryParam.reportBasic.discharge + '' + this.queryParam.reportBasic.dischargeUnit;
            this.disChargeCapacityEchartTitle = this.echartCurrent + ' Cycle Life - Capacity'
            this.capacityHoldRateEchartTitle = this.echartCurrent + ' Cycle Life - Capacity%'
            this.disChargeEnergyEchartTitle = this.echartCurrent + ' Cycle Life - Energy'
            this.energyHoldRateEchartTitle = this.echartCurrent + ' Cycle Life - Energy%'

            // this.coulombicEfficiencyEchartTitle = this.echartCurrent + ' Cycle Life - Coulombic Efficiency%'
            // 原始数据分页
            this.pageData = JSON.parse(res.data.allDataJson).tableList
            if (JSON.parse(res.data.allDataJson).currentVoltageList) {
              this.curVolData = JSON.parse(res.data.allDataJson).currentVoltageList
            }

            this.tableList = this.pageData.records
            this.pageNo = this.pageData.current
            this.pageSize = this.pageData.size
            this.totalRows = this.pageData.total
            
            if (this.pageSizeOptions.indexOf(this.totalRows.toString()) === -1) {
              this.pageSizeOptions.push(this.totalRows.toString())
            }
            //保持率数据分页
            this.holdRatePageData = JSON.parse(res.data.allDataJson).holdRateTableList
            // if (JSON.parse(res.data.allDataJson).currentVoltageList) {
            //   this.curVolData = JSON.parse(res.data.allDataJson).currentVoltageList
            // }
            this.holdRateTableList = this.holdRatePageData.records
            this.holdRatePageNo = this.holdRatePageData.current
            this.holdRatePageSize = this.holdRatePageData.size
            this.holdRateTotalRows = this.holdRatePageData.total
            if (this.holdRatePageSizeOptions.indexOf(this.holdRateTotalRows.toString()) === -1) {
              this.holdRatePageSizeOptions.push(this.holdRateTotalRows.toString())
            }

            // for (let i = 0; i < this.tableList.length; i++) {
            //   this.tableList[i].id = i
            // }

            this.disChargeCapacityTableList = this.allDataJson.disChargeCapacityTableList
            this.capacityHoldRateTableList = this.allDataJson.capacityHoldRateTableList
            this.disChargeEnergyTableList = this.allDataJson.disChargeEnergyTableList
            this.energyHoldRateTableList = this.allDataJson.energyHoldRateTableList
            // this.coulombicEfficiencyTableList = this.allDataJson.coulombicEfficiencyTableList

            this.update = false
          })

          // testReportGet({ id: this.id })
          // 	.then(res => {
          // 		this.record = res.data
          // 		this.allDataJson = JSON.parse(res.data.allDataJson)
          // 		this.queryParam = JSON.parse(res.data.queryParam)
          //     this.tableList = this.allDataJson.tableList
          //     this.disChargeCapacityTableList = this.allDataJson.disChargeCapacityTableList
          //     this.capacityHoldRateTableList = this.allDataJson.capacityHoldRateTableList
          //     this.disChargeEnergyTableList = this.allDataJson.disChargeEnergyTableList
          //     this.energyHoldRateTableList = this.allDataJson.energyHoldRateTableList
          //     this.coulombicEfficiencyTableList = this.allDataJson.coulombicEfficiencyTableList
          //
          // 		this.update = false
          // 	})
          .then(async res => {
            // 右边表格数据
            if (this.tableList.length > 0) {
              this.initTable()
            }

            //左边图表数据
            this.initNormalEchart('cycle','disChargeCapacity','disChargeCapacityEchartList')

            /* 缩略图 */
            for (let i = 0; i < this.cycleArr.length; i++) {
              this.initThumbnailEchart(this.cycleArr[i].id , this.cycleArr[i].id + 'EchartList' , this.cycleArr[i].id === 'disChargeCapacity' ? null : 'disChargeCapacityEchartList' )
            }
          });
      },

      initTable() {
        this.tableColumns = [
          {
            title: "序号",
            dataIndex: "index",
            align: "center",
            width: 40,
            customRender: (text, record, index) => `${this.pageSize*(this.pageNo-1) + index + 1}`
          },
          {
            title: "循环号",
            dataIndex: "cycle",
            align: "center",
            width: 50,
            customRender: (text, record, index) => `${this.tableList[index].dataList[0].cycleId}`
          }
        ]
        for (let i = 0; i < this.tableList[0].dataList.length; i++) {
          this.tableColumns.push({
            title: this.tableList[0].dataList[i].schemeBatteryNum,
            align: "center",
            children: [
              {
                title: "放电容量",
                width: 80,
                align: "center",
                dataIndex: "dataList[" + i + "].disChargeCapacity",
                customRender: (text, record, index) => {
                  const obj = {
                    children: this.update ? (
                      <a-input-number step="0.001" style="width:100%" v-model={record.dataList[i].disChargeCapacity}></a-input-number>
                    ) : (
                      record.dataList[i].disChargeCapacity
                    ),
                    attrs: {}
                  }
                  return obj
                }
              },
              {
                title: "放电能量",
                width: 80,
                align: "center",
                dataIndex: "dataList[" + i + "].disChargeEnergy",
                customRender: (text, record, index) => {
                  const obj = {
                    children: this.update ? (
                      <a-input-number step="0.001" style="width:100%" v-model={record.dataList[i].disChargeEnergy}></a-input-number>
                    ) : (
                      record.dataList[i].disChargeEnergy
                    ),
                    attrs: {}
                  }
                  return obj
                }
              },
              {
                title: "起始温度",
                width: 80,
                align: "center",
                dataIndex: "dataList[" + i + "].startTem",
                customRender: (text, record, index) => {
                  const obj = {
                    children: this.update ? (
                      <a-input-number step="0.1" style="width:100%" v-model={record.dataList[i].startTem}></a-input-number>
                    ) : (
                      record.dataList[i].startTem
                    ),
                    attrs: {}
                  }
                  return obj
                }
              },
              {
                title: "截止温度",
                width: 80,
                align: "center",
                dataIndex: "dataList[" + i + "].endTem",
                customRender: (text, record, index) => {
                  const obj = {
                    children: this.update ? (
                      <a-input-number step="0.1" style="width:100%" v-model={record.dataList[i].endTem}></a-input-number>
                    ) : (
                      record.dataList[i].endTem
                    ),
                    attrs: {}
                  }
                  return obj
                }
              },
/*              {
                title: "充电容量",
                width: 80,
                align: "center",
                dataIndex: "dataList[" + i + "].chargeCapacity",
                customRender: (text, record, index) => {
                  const obj = {
                    children: this.update ? (
                      <a-input-number step="0.001" style="width:100%" v-model={record.dataList[i].chargeCapacity}></a-input-number>
                    ) : (
                      record.dataList[i].chargeCapacity
                    ),
                    attrs: {}
                  }
                  return obj
                }
              }*/
            ]
          })
        }


        this.clcTableColumns = [
          {
            title: "序号",
            dataIndex: "index",
            align: "center",
            width: 40,
            customRender: (text, record, index) => `${this.holdRatePageSize*(this.holdRatePageNo-1) + index + 1}`
          },
          {
            title: "循环号",
            dataIndex: "cycle",
            align: "center",
            width: 50,
            customRender: (text, record, index) => `${this.holdRateTableList[index].dataList[0].cycleId}`
          }
        ]
        for (let i = 0; i < this.holdRateTableList[0].dataList.length; i++) {
          this.clcTableColumns.push({
            title: this.holdRateTableList[0].dataList[i].schemeBatteryNum,
            align: "center",
            children: [
              {
                title: "容量保持率",
                width: 80,
                align: "center",
                dataIndex: "dataList[" + i + "].capacityHoldRate",
              },
              {
                title: "能量保持率",
                width: 80,
                align: "center",
                dataIndex: "dataList[" + i + "].energyHoldRate",
              },
/*              {
                title: "库伦效率",
                width: 60,
                align: "center",
                dataIndex: "dataList[" + i + "].coulombicEfficiency",
              }*/
            ]
          })
        }


/*        this.disChargeCapacityTableColumns = [
          {
            title: "循环号",
            dataIndex: "cycle",
            align: "center",
            width: 60,
          },
          {
            title: "Avg.",
            width: 60,
            align: "center",
            dataIndex: "average"
          }
        ]
        for (let i = 0; i < this.disChargeCapacityTableList[0].dataList.length; i++) {
          this.disChargeCapacityTableColumns.push({
            title: this.disChargeCapacityTableList[0].dataList[i].schemeBatteryNum,
            align: "center",
            width: 60,
            dataIndex: "dataList[" + i + "].disChargeCapacity"
          })
        }

        this.capacityHoldRateTableColumns = [
          {
            title: "循环号",
            dataIndex: "cycle",
            align: "center",
            width: 60,
          },
          {
            title: "Avg.",
            width: 60,
            align: "center",
            dataIndex: "average"
          }
        ]
        for (let i = 0; i < this.capacityHoldRateTableList[0].dataList.length; i++) {
          this.capacityHoldRateTableColumns.push({
            title: this.capacityHoldRateTableList[0].dataList[i].schemeBatteryNum,
            align: "center",
            width: 60,
            dataIndex: "dataList[" + i + "].capacityHoldRate"
          })
        }

        this.disChargeEnergyTableColumns = [
          {
            title: "循环号",
            dataIndex: "cycle",
            align: "center",
            width: 60,
          },
          {
            title: "Avg.",
            width: 60,
            align: "center",
            dataIndex: "average"
          }
        ]
        for (let i = 0; i < this.disChargeEnergyTableList[0].dataList.length; i++) {
          this.disChargeEnergyTableColumns.push({
            title: this.disChargeEnergyTableList[0].dataList[i].schemeBatteryNum,
            align: "center",
            width: 60,
            dataIndex: "dataList[" + i + "].disChargeEnergy"
          })
        }

        this.energyHoldRateTableColumns = [
          {
            title: "循环号",
            dataIndex: "cycle",
            align: "center",
            width: 60,
          },
          {
            title: "Avg.",
            width: 60,
            align: "center",
            dataIndex: "average"
          }
        ]
        for (let i = 0; i < this.energyHoldRateTableList[0].dataList.length; i++) {
          this.energyHoldRateTableColumns.push({
            title: this.energyHoldRateTableList[0].dataList[i].schemeBatteryNum,
            align: "center",
            width: 60,
            dataIndex: "dataList[" + i + "].energyHoldRate"
          })
        }

        this.coulombicEfficiencyTableColumns = [
          {
            title: "循环号",
            dataIndex: "cycle",
            align: "center",
            width: 60,
          },
          {
            title: "Avg.",
            width: 60,
            align: "center",
            dataIndex: "average"
          }
        ]
        for (let i = 0; i < this.coulombicEfficiencyTableList[0].dataList.length; i++) {
          this.coulombicEfficiencyTableColumns.push({
            title: this.coulombicEfficiencyTableList[0].dataList[i].schemeBatteryNum,
            align: "center",
            width: 60,
            dataIndex: "dataList[" + i + "].coulombicEfficiency"
          })
        }*/


      },
      
      // 数据处理
      _handleCycleEchartData(targetObj,cyclicList,echartList = []){
        this.editObj = targetObj

        let normalLegent = []
        const normalSeries = []
        const normalOriginalSeries = []
        const normalSeriesTier = []
        const normalOriginalSeriesTier = []
        const templateParam = this.reportChartTemplateList[targetObj].templateParamJson
        const originalParam = this.reportChartTemplateList[targetObj].originalParamJson
        const echartsColorList =  cyclicList.length <= 2 ? this.echartsColorShortList : this.echartsColorLongList

        let fristTooltip  = {}
        let titleData  = {}
        let lineColorList = []
        let templateContent = {}
        switch(targetObj){
          case 'disChargeCapacity':
            fristTooltip = {title:'容量  ',unit:' Ah'}
            titleData = {
              chartTitle:this.disChargeCapacityEchartTitle,
              YTitle:'Capacity (Ah)',
            }
            break;
          case 'capacityHoldRate':
            fristTooltip = {title:'容量保持率 ',unit:' %'}
            titleData = {
              chartTitle:this.capacityHoldRateEchartTitle,
              YTitle:'Capacity Retention (%)',
            }
            break;
          case 'disChargeEnergy':
            fristTooltip = {title:'容量  ',unit:' Wh'}
            titleData = {
              chartTitle:this.disChargeEnergyEchartTitle,
              YTitle:'Energy (Wh)',
            }
            break;
          case 'energyHoldRate':
            fristTooltip = {title:'能量保持率  ',unit:' %'}
            titleData = {
              chartTitle:this.energyHoldRateEchartTitle,
              YTitle:'Energy Retention (%)',
            }
            break;
        }
        
        for (let i = 0; i < cyclicList.length; i++) {
          const legendId = cyclicList[i].legend
          const series = []
          const originalSeries = []

          if(!lineColorList.some(item => item.id === legendId)){
            lineColorList.push({ id: legendId, color: echartsColorList[lineColorList.length] })
          }
          const temColor =  lineColorList.filter(item => item.id === legendId)[0].color
          const property = ['collect','start','end']
          const lineTypeList = {
            'collect':'solid',
            'start':'dashed',
            'end':'dotted'
          }
          const dataList = {
            'collect':cyclicList[i].echartDataList,
            'start':echartList.length === 0 ? cyclicList[i].startTemEchartDataList : echartList[i].startTemEchartDataList,
            'end':echartList.length === 0 ?  cyclicList[i].endTemEchartDataList : echartList[i].endTemEchartDataList
          }
          
          for(let j = 0;j<property.length;j++){
            templateContent = (templateParam.checkData && templateParam.checkData.length !== 0) ? (templateParam.checkData.filter(item => item.id === legendId +  (i + 1) + property[j])[0] || {}) : {}

            series.push({
              id:legendId +  (i + 1) + property[j],
              name: legendId,
              type: 'line',
              sampling: 'lttb',
              large: true,
              barGap: 0,
              markPoint: {
                data: []
              },
              tooltip: {
                valueFormatter: (value) => {
                  const tooltipReturn = {
                      'collect':fristTooltip.title + value + fristTooltip.unit,
                      'start':'起始温度  ' + value + ' ℃',
                      'end':'结束温度  ' + value + ' ℃'
                    }
                  return tooltipReturn[property[j]];
                }
              },
              symbol: templateContent.symbol ??  "none",
              lineStyle:{
                type:templateContent.lineType ?? lineTypeList[property[j]],
                width:templateContent.lineWidth ?? 1.5,
                color:templateContent.lineColor ?? temColor
              },
              itemStyle:{
                color:templateContent.itemColor ?? temColor
              },
              data: dataList[property[j]],
            })

            if(property[j] !== 'collect') series[j].yAxisIndex = 1
            if(templateContent.symbolSize) series[j].symbolSize = templateContent.symbolSize
            if(templateContent.connectNulls) series[j].connectNulls = templateContent.connectNulls
            if(templateContent.maxPoint) series[j].markPoint.data.push({type:'max',name:'max'})
            if(templateContent.minPoint) series[j].markPoint.data.push({type:'min',name:'min'})

            originalSeries.push({
              id:legendId +  (i + 1) + property[j],
              soc:cyclicList[i].batteryNum + '#',
              index: i + 1,
              name: legendId,
              type: 'line',
              sampling: 'lttb',
              large: true,
              barGap: 0,
              symbol: templateContent.symbol ?? "none",
              symbolSize: templateContent.symbolSize ?? 0,
              maxPoint:templateContent.maxPoint ?? false,
              minPoint:templateContent.minPoint ?? false,
              connectNulls:templateContent.connectNulls ?? false,
              lineType:templateContent.lineType ?? lineTypeList[property[j]],
              lineWidth:templateContent.lineWidth ?? 1.5,
              lineColor:templateContent.lineColor ?? temColor,
              itemColor:templateContent.itemColor ?? temColor
            })
          }
          if(templateParam.legendData?.legendList){
            if(templateParam.legendData.legendList.includes(legendId)){
              normalSeries.push(...originalSeries)
              normalSeriesTier.push(...series)

            }
          }else{
            normalSeries.push(...originalSeries)
            normalSeriesTier.push(...series)
          }

            
            normalLegent.push(legendId)
            normalOriginalSeries.push(...originalSeries)
            normalOriginalSeriesTier.push(...series)


          // 如果有修改，并且包含当前图例，直接赋值，如果没修改，直接赋值


        }


        // 去重
        normalLegent = _.uniq(normalLegent);

        this.originalLegent[targetObj] = _.cloneDeep(normalLegent)
        this.originalData[targetObj] = {
          ...this._getCycleEchartOriginal(titleData),
          series:originalParam?.series ?? _.cloneDeep(normalOriginalSeries),
          originalSeries: originalParam?.originalSeries ?? _.cloneDeep(normalOriginalSeriesTier)
        }

        this.editData[targetObj] = {
          ...this._getCycleEchartOriginal(titleData,'edit'),
          legend:templateParam.legendData?.legendList ?? _.cloneDeep(normalLegent),
          legendSort:templateParam.legendData?.legendSort ?? _.cloneDeep(normalLegent),
          legendRevealList:templateParam.legendData?.legendRevealList ?? _.cloneDeep(normalLegent),
          legendEditName:templateParam.legendData?.legendEditName ??  _.cloneDeep(normalLegent).map(v => { return {id:v, originName: v, previousName: '', newName: '', isReset: false } }),
          series:_.cloneDeep(normalOriginalSeries),
          originalSeries:_.cloneDeep(normalOriginalSeriesTier),
          editSeries:_.cloneDeep(normalSeriesTier),
          allData:templateParam.allData ?? {}
        } 

        // 赋值
        const property2 = ['legendIndeterminate','checkAll','legendRevealIndeterminate','legendRevealcheckAll']

        for(let j = 0;j < property2.length ;j++){
          if(templateParam.legendData[property2[j]]){
            this.editData[targetObj][property2[j]] = templateParam.legendData[property2[j]]
          } 
        }

        return normalSeriesTier
      },
      _handleCycleYAxisValue(targetObj){
        const YAxis1 = this.echartObj[targetObj].getModel().getComponent("yAxis",0).axis.scale
        const YAxis2 = this.echartObj[targetObj].getModel().getComponent("yAxis",1).axis.scale

        const originalObj = this.originalData[targetObj]
        const editItem = this.editData[targetObj]
        const originalParam = this.reportChartTemplateList[targetObj].originalParamJson

        editItem.yMin = YAxis1._extent[0]
        editItem.yMax = YAxis1._extent[1]
        editItem.yInterval = YAxis1._interval

        editItem.yMin2 = YAxis2._extent[0]
        editItem.yMax2 = YAxis2._extent[1]
        editItem.yInterval2 = YAxis2._interval

        if(!originalParam){
          originalObj.yMin = YAxis1._extent[0]
          originalObj.yMax = YAxis1._extent[1]
          originalObj.yInterval = YAxis1._interval

          originalObj.yMin2 = YAxis2._extent[0]
          originalObj.yMax2 = YAxis2._extent[1]
          originalObj.yInterval2 = YAxis2._interval
        }
        
      },
      _handleCycleEchartOptions(targetObj,seriesList){

        let titleData  = {}
        switch(targetObj){
          case 'disChargeCapacity':
            titleData = {
              chartTitle:this.disChargeCapacityEchartTitle,
              YTitle:'Capacity (Ah)',
            }
            break;
          case 'capacityHoldRate':
            titleData = {
              chartTitle:this.capacityHoldRateEchartTitle,
              YTitle:'Capacity Retention (%)',
            }
            break;
          case 'disChargeEnergy':
            titleData = {
              chartTitle:this.disChargeEnergyEchartTitle,
              YTitle:'Energy (Wh)',
            }
            break;
          case 'energyHoldRate':
            titleData = {
              chartTitle:this.energyHoldRateEchartTitle,
              YTitle:'Energy Retention (%)',
            }
            break;
        }

        const templateParam = this.reportChartTemplateList[targetObj].templateParamJson
        const originalParam = this.reportChartTemplateList[targetObj].originalParamJson

        const firstItem = this.fristInit[targetObj] && !originalParam
        const editItem = this.editData[targetObj]

        const options = {
          backgroundColor: '#ffffff',
          animationDuration: 2000,
          textStyle: {
            fontFamily: "Times New Roman"
          },
          tooltip: {
            trigger: 'axis',
            confine: true,
            enterable: true,
            hideDelay: 300,
            extraCssText: 'max-height: 400px; overflow-y: auto; scrollbar-width: thin; scrollbar-color: #888 #f1f1f1; pointer-events: auto;',
          },
          title: {
            text: firstItem ? titleData.chartTitle : editItem.chartTitle,
            left: 'center',
            top: firstItem ? 10 : editItem.titleTop,
            fontSize: 18,
            fontWeight: 500,
            color: "#000"
          },
          grid: {
            show: true,
            top: firstItem ? 45 : editItem.gridTop,
            left: firstItem ? 60 : editItem.gridLeft,
            right: firstItem ? 60 : editItem.gridRight,
            bottom: firstItem ? 55 : editItem.gridBottom,
            borderWidth: 0.5,
            borderColor: "#ccc"
          },
          legend: {
            show: true,
            backgroundColor: firstItem ? "#f5f5f5" : editItem.legendBgColor,
            top: firstItem ? 50 : editItem.legendTop,
            left: firstItem ? 'center' : editItem.legendLeft,
            itemWidth: firstItem ? 20 : editItem.legendWidth,
            itemHeight: firstItem ? 5 : editItem.legendHeight,
            itemGap: firstItem ? 10 : editItem.legendGap,
            
            textStyle:{
              fontSize: 14,
              color: "#000000"
            }
          },
          xAxis: [
            {
              name: firstItem ? 'Cycles (N)' : editItem.XTitle,
              type: firstItem ? 'category' : editItem.xType,
              nameLocation: 'middle', // 将名称放在轴线的中间位置
              nameGap: 30,
              nameTextStyle: {
                fontSize: 14, // 可以根据需要调整字体大小
                fontWeight: 500,
                color: "#000000" // 可以根据需要调整字体大小
              },
              boundaryGap: false,
              axisTick: { show: false },
              axisLabel: {
                show: true,
                width: 0.5,
                textStyle: {
                  fontSize: "15",
                  color: "#000000"
                }
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#ccc",
                  width: 0.5
                }
              },
              splitLine: {
                show: true, // 显示分隔线
                lineStyle: {
                  type: "solid",
                  width: 0.5
                }
              },
            }
          ],
          yAxis: [
            {
              name: firstItem ? titleData.YTitle : editItem.YTitle,
              type: firstItem ? 'value' : editItem.yType,
              nameGap: firstItem ? 35 : editItem.yTitleLetf,
              position: 'left',
              nameLocation: 'middle', // 将名称放在轴线的起始位置
              nameRotate: 90, // 旋转角度，使名称竖排
              nameTextStyle: {
                fontSize: 14, // 可以根据需要调整字体大小
                fontWeight: 500,
                color: "#000000"
              },
              splitLine: {
                show: true,  // 显示分隔线
                lineStyle: {
                  type: 'solid',  // 设置分隔线的样式，比如虚线
                  width: 0.5
                }
              },
              axisTick: {
                show: false,  // 显示刻度
              },
              axisLabel: {
                show: true,
                width: 0.5,
                textStyle: {
                  fontSize: "15",
                  color: "#000000"
                }
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#ccc",
                  width: 0.5
                }
              },
              
            },
            {
              name: firstItem ? 'Temperature (ºC)' : editItem.YTitle2,
              type: firstItem ? 'value' : editItem.yType2,
              position: 'right',
              nameGap: firstItem ? 35 : editItem.yTitleRight,
              nameLocation: 'middle', // 将名称放在轴线的起始位置
              nameRotate: 90, // 旋转角度，使名称竖排
              nameTextStyle: {
                fontSize: 14,
                fontWeight: 500,
                color: "#000000" // 可以根据需要调整字体大小
              },
              splitLine: {
                show: true,  // 显示分隔线
                lineStyle: {
                  type: 'solid'  // 设置分隔线的样式，比如虚线
                }
              },
              axisTick: {
                show: true,  // 显示刻度
              },
              axisLabel: {
                show: true,
                width: 0.5,
                textStyle: {
                  fontSize: "15",
                  color: "#000000"
                }
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#ccc",
                  width: 0.5
                }
              },
              alignTicks: true,
            }
          ],
          series:seriesList
        }
        
        // 非首次加载才需要
        //处理图例
        //图例名称
        if(!firstItem){
          const newSeriesList = []
          _.cloneDeep(seriesList).forEach(v => {
            const haveNameList = editItem.legendEditName.filter(filterItem => filterItem.originName === v.name && filterItem.newName)
            v.name = haveNameList.length === 0 ? v.name : haveNameList[0].newName
            newSeriesList.push(v)
          })
          options.series = newSeriesList

          const legend = []
          editItem.legendSort.forEach(v => {
            if(editItem.legend.includes(v) && editItem.legendRevealList.includes(v)){
              const haveList = editItem.legendEditName.filter(filterItem => filterItem.originName === v && filterItem.newName)
              legend.push(haveList.length === 0 ? v : haveList[0].newName )
            }
          })
          options.legend.data = legend
        }


        // 如果非首次编辑
        // 默认是水平，如果是水平，就不添加，垂直才添加
        if(editItem.legendOrient === 'vertical'){
          options.legend.orient = editItem.legendOrient
        }
        if(editItem.legendLeft !== undefined){
          options.legend.left = editItem.legendLeft
        }
        

        // X轴可能没有最大最小值、间隔
        if(!firstItem && editItem.xInterval && editItem.xType === 'value'){
          options.xAxis[0].min = editItem.xMin
          options.xAxis[0].max = editItem.xMax
          options.xAxis[0].interval = editItem.xInterval
        }
        if(!firstItem && editItem.yType === 'value'){
          options.yAxis[0].min = editItem.yMin
          options.yAxis[0].max = editItem.yMax
          options.yAxis[0].interval = editItem.yInterval
        }
        if(!firstItem && editItem.yType2 === 'value'){
          options.yAxis[1].min = editItem.yMin2
          options.yAxis[1].max = editItem.yMax2
          options.yAxis[1].interval = editItem.yInterval2
        }
        return options
      },
      _getCycleEchartOriginal(titleData,type = 'original'){
        const isEdit = type === 'edit'
        const templateParam = this.reportChartTemplateList[this.editObj].templateParamJson
        const originalParam = this.reportChartTemplateList[this.editObj].originalParamJson

        const options = {
          chartTitle: isEdit && templateParam.chartTitle ? templateParam.chartTitle : titleData.chartTitle,
          XTitle: isEdit && templateParam.XTitle ? templateParam.XTitle : 'Cycles (N)',
          YTitle: isEdit && templateParam.YTitle ? templateParam.YTitle : titleData.YTitle,
          YTitle2: isEdit && templateParam.YTitle2 ? templateParam.YTitle2 : 'Temperature (ºC)',
          titleTop: isEdit && templateParam.titleTop ? templateParam.titleTop : 10,
          yTitleLetf: isEdit && templateParam.yTitleLetf ? templateParam.yTitleLetf : 35,
          yTitleRight: isEdit && templateParam.yTitleRight ? templateParam.yTitleRight : 35,

          legendWidth: isEdit && templateParam.legendWidth ? templateParam.legendWidth : 20,
          legendHeight: isEdit && templateParam.legendHeight ? templateParam.legendHeight : 5,
          legendGap: isEdit && templateParam.legendGap ? templateParam.legendGap : 10,
          legendBgColor: isEdit && templateParam.legendBgColor ? templateParam.legendBgColor : '#f5f5f5',
          legendOrient: isEdit && templateParam.legendOrient ? templateParam.legendOrient : 'horizontal',
          legendTop: isEdit && templateParam.legendTop ? templateParam.legendTop : 50,
          legendLeft: isEdit && templateParam.legendLeft ? templateParam.legendLeft : 'center',

          gridTop: isEdit && templateParam.gridTop ? templateParam.gridTop : 45,
          gridLeft: isEdit && templateParam.gridLeft ? templateParam.gridLeft : 60,
          gridRight: isEdit && templateParam.gridRight ? templateParam.gridRight : 60,
          gridBottom: isEdit && templateParam.gridBottom ? templateParam.gridBottom : 55,

          xType: isEdit && templateParam.xType ? templateParam.xType : 'category',
          yType: isEdit && templateParam.yType ? templateParam.yType : 'value',
          yType2: isEdit && templateParam.yType2 ? templateParam.yType2 : 'value',
        }

        const property = ['xMin','xMax','xInterval','yMin','yMax','yInterval','yMin2','yMax2','yInterval2']

        if (type === 'edit') {
          options.allData = templateParam.allData ?? {}
          for(let i = 0;i < property.length;i++){
            if(templateParam[property[i]]) options[property[i]] = templateParam[property[i]]
          }
        }

        if (type === 'original' && originalParam) {
          for(let i = 0;i < property.length;i++){
            options[property[i]] = originalParam[property[i]]
          }
        }

        return options
      },
      /*
      * 编辑图表
      */

      _getCycleThumbnailSeries(cyclicList,echartList = []){
        let series = []
        cyclicList.forEach((v,index) => {
          // 避免点为空值
          v.echartDataList = v.echartDataList.filter(filter => filter[1] !== '')
          v.startTemEchartDataList = echartList.length === 0 ? v.startTemEchartDataList.filter(filter => filter[1] !== '') : echartList[index].startTemEchartDataList.filter(filter => filter[1] !== '')
          v.endTemEchartDataList = echartList.length === 0 ? v.endTemEchartDataList.filter(filter => filter[1] !== '') : echartList[index].endTemEchartDataList.filter(filter => filter[1] !== '')

          const multiplier1 =Math.trunc(v.echartDataList.length / 10)
          const multiplier2 =Math.trunc(v.startTemEchartDataList.length / 10)
          const multiplier3 =Math.trunc(v.endTemEchartDataList.length / 10)

          const seriesData1 = new Array(10).fill(1).map((mapItem,mapIndex) => v.echartDataList[mapIndex * multiplier1])
          const seriesData2 = new Array(10).fill(1).map((mapItem,mapIndex) => v.startTemEchartDataList[mapIndex * multiplier2])
          const seriesData3 = new Array(10).fill(1).map((mapItem,mapIndex) => v.endTemEchartDataList[mapIndex * multiplier3])

          series.push(
            {
              name: v.legend,
              type:'line',
              symbol:'none',
              data:seriesData1
            },
            {
              name: v.legend,
              type:'line',
              symbol:'none',
              lineStyle: {
                type: "dashed"
              },
              data:seriesData2
            },
            {
              name: v.legend,
              type:'line',
              symbol:'none',
              lineStyle: {
                type: "dotted"
              },
              data:seriesData3
            }
          )
        })
        return series
      },

      handleDrawerReset(){

        this.$confirm({
          title: '请确认是否重置图表?',
          content: '图表重置后，图表修改内容无法恢复',
          okText: '重置',
          cancelText: '取消',
          onOk:async () => {
            await this.deleteChartTemplate({ reportId:this.id,id:this.reportChartTemplateList[this.editObj].templateId,targetChart:this.editObj })
            this.fristInit[this.editObj] = true
            this.drawerVisible = false
            switch(this.editObj){
              case 'disChargeCapacity':
              this.initNormalEchart('cycle','disChargeCapacity','disChargeCapacityEchartList')
                break;
              case 'capacityHoldRate':
                this.initNormalEchart('cycle','capacityHoldRate','capacityHoldRateEchartList','disChargeCapacityEchartList')
                break;
              case 'disChargeEnergy':
                this.initNormalEchart('cycle','disChargeEnergy','disChargeEnergyEchartList','disChargeCapacityEchartList')
                break;
              case 'energyHoldRate':
                this.initNormalEchart('cycle','energyHoldRate','energyHoldRateEchartList','disChargeCapacityEchartList')
                break;
            }
            this.$message.success("重置成功")
          },
          onCancel() {}
        });
      },
      // 重新选择模板
      async handleChangeTemplate(targetObj){
        await this.getChartTemplateRelationList(this.$route.query.id,[targetObj])
        this.fristInit[this.editObj] = true
        this.drawerVisible = false
        switch(this.editObj){
          case 'disChargeCapacity':
          this.initNormalEchart('cycle','disChargeCapacity','disChargeCapacityEchartList')
            break;
          case 'capacityHoldRate':
            this.initNormalEchart('cycle','capacityHoldRate','capacityHoldRateEchartList','disChargeCapacityEchartList')
            break;
          case 'disChargeEnergy':
            this.initNormalEchart('cycle','disChargeEnergy','disChargeEnergyEchartList','disChargeCapacityEchartList')
            break;
          case 'energyHoldRate':
            this.initNormalEchart('cycle','energyHoldRate','energyHoldRateEchartList','disChargeCapacityEchartList')
            break;
        }
      },
      onPageChange(pageNo, pageSize) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        getCycleReport({ id: this.id, pageNo: this.pageNo, pageSize: this.pageSize })
          .then(res => {
            this.pageData = JSON.parse(res.data.allDataJson).tableList
            this.tableList = this.pageData.records
            this.pageNo = this.pageData.current
            this.pageSize = this.pageData.size
            this.totalRows = this.pageData.total
            if (this.pageSizeOptions.indexOf(this.totalRows.toString()) === -1) {
              this.pageSizeOptions.push(this.totalRows.toString())
            }
          })
      },
      holdRateOnPageChange(holdRatePageNo, holdRatePageSize) {
        // this.pageNo = pageNo;
        // this.pageSize = pageSize;
        this.holdRatePageNo = holdRatePageNo;
        this.holdRatePageSize = holdRatePageSize;
        getCycleReport({ id: this.id, pageNo: this.pageNo, pageSize: this.pageSize, holdRatePageNo: this.holdRatePageNo, holdRatePageSize: this.holdRatePageSize })
          .then(res => {
            this.holdRatePageData = JSON.parse(res.data.allDataJson).holdRateTableList
            this.holdRateTableList = this.holdRatePageData.records
            this.holdRatePageNo = this.holdRatePageData.current
            this.holdRatePageSize = this.holdRatePageData.size
            this.holdRateTotalRows = this.holdRatePageData.total
            if (this.holdRatePageSizeOptions.indexOf(this.holdRateTotalRows.toString()) === -1) {
              this.holdRatePageSizeOptions.push(this.holdRateTotalRows.toString())
            }
          })
      },

      updateData() {
        testReportUpdateDate(this.originData, this.id).then(res => {
          this.init()
        })
      },

    }
  }
</script>
<style lang="less" scoped>
  @import "./css/preview.less";
  @import "./css/thumbnail.less";

  .charts{
    width: 598px;
    height: 417px;
    border: 0.5px solid #ccc;
  }
  

  /deep/ .block .ant-btn{
    font-size: 16px;
    padding: 0 6px;
  }

  
</style>