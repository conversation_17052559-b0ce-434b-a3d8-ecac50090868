<template>
  <div>
    <!-- head start -->
    <div class="head-wrapper">
      <div style="float: left;width: 200px;height:20px;">
        <div class="block small-mark"></div>
        <div style="font-weight: bold;">已发生更改内容项</div>
      </div>
      <div class="btn" :style="`margin-right: ${mrWidth}px;`">
        <div style="width: 140px;height:30px;">
         <span style="padding: 10px 6px 10px 0px"><svg xmlns="http://www.w3.org/2000/svg"
                                                       class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 jZPaJQ svg-icon-path-icon fill"
                                                       viewBox="0 -4 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path
           d="M5 8C5 6.89543 5.89543 6 7 6H19L24 12H41C42.1046 12 43 12.8954 43 14V40C43 41.1046 42.1046 42 41 42H7C5.89543 42 5 41.1046 5 40V8Z"
           fill="none" stroke="rgb(84, 152, 255)" stroke-width="4" stroke-linejoin="round"></path><path
           d="M30 28L23.9933 34L18 28.0134" stroke="rgb(84, 152, 255)" stroke-width="4" stroke-linecap="round"
           stroke-linejoin="round"></path><path d="M24 20V34" stroke="rgb(84, 152, 255)" stroke-width="4"
                                                stroke-linecap="round" stroke-linejoin="round"></path></g></svg></span>
        <a class="exportButtonClass" @click="miExportTable()">MI设计导出</a>
        </div>
        <div style="width: 140px;">
        <a-icon type="plus" style="font-size:18px;color:dodgerblue;"  v-if="canUpdate"/>
        <a style="margin-left:5px;font-size:15px;color:dodgerblue" @click="miReImport()"  v-if="canUpdate">MI选型重新导入</a>
      </div>
      </div>
    </div>
    <!-- head end -->

    <div>
      <a-spin :spinning="isLoading">
        <!-- 快速导航 start -->
      <a-table
        class="navigationClass"
        id="navigationId"
        style="margin: 0 20px;background: #fff;width:100px;height:500px;float: left"
        :data-source="navigationData"
        :pagination="false">
        <a-table-column
          align="center"
          title="快速导航"
          data-index="navigationItem">
          <template slot-scope="text, record">
            <span @click="clickNavigation(record)">{{ text }}</span>
          </template>
        </a-table-column>
      </a-table>
        <!-- 快速导航 end -->

      <a-table
        id="develop"
        style="width:80%;height:150%;float: left"
        :columns="columns"
        :data-source="resultData"
        :row-key="(record) => record.id"
        :pagination="false"
        :scroll="{ y: windowHeight }"
        :customRow="customRowEvent"
        bordered
      >
      <!-- 工序 -->
        <div
          slot="process"
          slot-scope="text,record,index"
          :id="'circle'+index">
            <input
            :value="record.process != null ? text : '/'" style="width: 78%"
            @change="updateData($event,record,'process')"/>
        </div>

        <!-- 项目 -->
        <div
          slot="project"
          slot-scope="text,record">
          <input :disabled="!canUpdate"
            :value="record.project != null ? text : '/'"
            @change="updateData($event,record,'project')"/>

        </div>

        <!-- 产品参数 -->
        <div
          slot="productParam"
          slot-scope="text,record">
          <a-tooltip placement="topLeft" arrow-point-at-center>
            <template slot="title" v-if="record.oriProductParam !== undefined">
              {{ record.oriProductParam }}
            </template>
               <input  :disabled="!canUpdate"
              :value="record.productParam != null ? text : '/'"
              @change="updateData($event,record,'productParam')"/>
          </a-tooltip>
        </div>

        <!-- 规格1 -->
        <div
          slot="specification1"
          slot-scope="text,record">
          <a-tooltip placement="topLeft" arrow-point-at-center>
            <template slot="title" v-if="record.oriSpecification1 !== undefined">
              {{ record.oriSpecification1 }}
            </template>
          <input class="mark"  :disabled="!canUpdate"
            :value="record.specification1 != null ? text : '/'"
            @change="updateData($event,record,'specification1')"/>
          </a-tooltip>

        </div>

        <!-- 规格2 -->
        <div
          slot="specification2"
          slot-scope="text,record">
          <a-tooltip placement="topLeft" arrow-point-at-center>
            <template slot="title" v-if="record.oriSpecification2 !== undefined">
              {{ record.oriSpecification2 }}
            </template>
          <input
            :value="record.specification2 != null ? text : '/'" :disabled="!canUpdate"
            @change="updateData($event,record,'specification2')"/>
          </a-tooltip>
        </div>

        <!-- 规格 -->
        <div
          slot="specification3"
          slot-scope="text,record">
          <a-tooltip placement="topLeft" arrow-point-at-center>
            <template slot="title" v-if="record.oriSpecification3 !== undefined">
              {{ record.oriSpecification3 }}
            </template>
          <input :disabled="!canUpdate"
            :value="record.specification3 != null ? text : '/'"
            @change="updateData($event,record,'specification3')"/>
          </a-tooltip>
        </div>

        <!-- 规格4 -->
        <div
          slot="specification4"
          slot-scope="text,record">
          <a-tooltip placement="topLeft" arrow-point-at-center>
            <template slot="title" v-if="record.oriSpecification4 !== undefined">
              {{ record.oriSpecification4 }}
            </template>
          <input :disabled="!canUpdate"
            :value="record.specification4 != null ? text : '/'"
            @change="updateData($event,record,'specification4')"/>
          </a-tooltip>
        </div>

        <!-- 备注 -->
        <div
          slot="remark"
          slot-scope="text,record">
          <a-textarea :disabled="!canUpdate"
            v-model="record.remark"
            style="width:92%"
            :auto-size="{ minRows: 1, maxRows: 3 }"
            @blur="updateData($event,record,'remark')"/>
        </div>

        <!-- 页头 -->
        <template slot="title">
          <table>
            <tr class="renderTr">
              <td
                style="width: 18%;"
                rowspan="4"
                colspan="2">
                <img
                  src="/img/logo.53575418.png"
                  alt="logo"
                  style="width: 80px;height: 80px">
              </td>
              <td
                style="width: 49.5%;font: bold normal 18px arial;"
                rowspan="2"
                colspan="6">Manufacturing Istruction
              </td>
              <td style="width: 8%;font-weight: bold">文件编号：</td>
              <td id="render_documentNo">
                <input  :disabled="!canUpdate"
                       :value="libraryData.documentNo"
                       @change="updateLibData($event, libraryData, 'documentNo')"/>
              </td>
            </tr>
            <tr class="renderTr">
              <td style="width: 6%;font-weight: bold">版本：</td>
              <td id="render_version">
                <input :disabled="!canUpdate"
                  :value="libraryData.version" 
                  @change="updateLibData($event, libraryData, 'version')"/>
              </td>
            </tr>
            <tr class="renderTr">
              <td
                colspan="6"
                rowspan="2" id="render_miVersion">
                <input  :disabled="!canUpdate"
                       :value="libraryData.miVersion"
                       style="width: 100%;font: bold normal 16px arial;"
                       @change="updateLibData($event, libraryData, 'miVersion')"/>
              </td>
              <td style="width: 6%;font-weight: bold">样品阶段：</td>
              <td id="render_sampleStage">
                <input :disabled="!canUpdate"
                  :value="libraryData.sampleStage" 
                  @change="updateLibData($event, libraryData, 'sampleStage')"/>
              </td>
            </tr>
            <tr style="height: 28px" class="renderTr">
              <td style="width: 6%;font-weight: bold">页码：</td>
              <td id="render_page">
                <input :disabled="!canUpdate"
                  :value="libraryData.page" 
                  @change="updateLibData($event, libraryData, 'page')"/>
              </td>
            </tr>
          </table>
        </template>

        <!-- 页尾 -->
        <template
          slot="footer"
          slot-scope="currentPageData">
          <table>
            <tr>
              <td style="padding: 0;border: 0;width: 8.95%;"></td>
              <td style="padding: 0;border: 0;width: 8.95%;"></td>
              <td style="padding: 0;border: 0;width: 18.2%;"></td>
              <td style="padding: 0;border: 0;width: 31.5%;"></td>
              <td style="padding: 0;border: 0;width: 8%;"></td>
              <td style="padding: 0;border: 0;width: 24.5%;"></td>
            </tr>
<!--            <tr style="border: 1px solid black;">-->
<!--              <td id="iconPlusTd">-->
<!--                <a-icon-->
<!--                  type="plus"-->
<!--                  slot="footer"-->
<!--                  :style="{paddingLeft: bigClient?'2.1%':'1.85%'}"-->
<!--                  @click="addMIVersion(libraryData)"/>-->
<!--              </td>-->
<!--            </tr>-->
            <tr>
              <td>编制</td>
              <td id="render_mivEstablishment">
                <input :disabled="!canUpdate"
                  :value="libraryData.mivEstablishment"
                  @change="updateLibData($event, libraryData, 'mivEstablishment')"/>
              </td>
              <td>审核</td>
              <td id="render_mivAudit">
                <input :disabled="!canUpdate"
                  :value="libraryData.mivAudit"
                  @change="updateLibData($event, libraryData, 'mivAudit')"/>
              </td>
              <td>批准</td>
              <td id="render_mivApproval">
                <input :disabled="!canUpdate"
                  :value="libraryData.mivApproval"
                  @change="updateLibData($event, libraryData, 'mivApproval')"/>
              </td>
            </tr>
          </table>
        </template>
      </a-table>
      </a-spin>
    </div>
    <mi-lib-data-dialog ref="miLibDataDialog"/>
  </div>
</template>
<script>
import $ from 'jquery';
import { getMIStandardLibCurById, updateMIStandardLibCur } from "@/api/modular/system/gCylinderMILibCurManage";
import { getBatteryDesign,canBeUpdate } from "@/api/modular/system/batterydesignManage";
import { getMIVersionCurList, insertMIVersionCur, updateMIVersionCur } from "@/api/modular/system/miVersionCurManage";
import { getMIStandardByImpBatteryId, miOutputExportExcel } from "@/api/modular/system/gCylinderMILibManage";
import miLibDataDialog from "@/views/system/batterydesignStandard/miLibDataDialog.vue";
import { EventBus } from '@/api/modular/system/eventBus'

export default {
  components: { miLibDataDialog },
  props: ['deliverImpBatteryId','canUpdate'],
  data() {
    return {
      // 表头
      columns: [
        {
          title: '工序',
          dataIndex: 'process',
          // scopedSlots: { customRender: 'process' },
          align: 'center',
          width: 74.8,
          customRender: (value, row, index) => {
            const obj = {
              children: value,
              attrs: {},
            };
            obj.attrs.rowSpan = this.myArrayProcess[index];
            return obj
          }
        },
        {
          title: '项目',
          dataIndex: 'project',
          // scopedSlots: { customRender: 'project' },
          align: 'center',
          width: 74.8,
          customRender: (value, row, index) => {
            const obj = {
              children: value,
              attrs: {},
            };
            obj.attrs.rowSpan = this.myArrayProject[index];
            return obj
          }
        },
        {
          title: '产品参数',
          dataIndex: 'productParam',
          width: 150,
          align: 'center',
          scopedSlots: { customRender: 'productParam' },
        },
        {
          title: '规格',
          dataIndex: 'specification1',
          align: 'center',
          width: 61,
          scopedSlots: { customRender: 'specification1' },
          colSpan: 4,
        },
        {
          dataIndex: 'specification2',
          align: 'center',
          colSpan: 0,
          width: 40,
          scopedSlots: { customRender: 'specification2' },
        },
        {
          dataIndex: 'specification3',
          align: 'center',
          colSpan: 0,
          width: 80,
          scopedSlots: { customRender: 'specification3' },
        },
        {
          dataIndex: 'specification4',
          align: 'center',
          colSpan: 0,
          width: 80,
          scopedSlots: { customRender: 'specification4' },
        },
        {
          title: '备注',
          dataIndex: 'remark',
          align: 'center',
          width: 270,
          // scopedSlots: { customRender: 'remark' },
          customRender: (value, row, index) => {
            const obj = {
              children: value,
              attrs: {},
            };
            obj.attrs.rowSpan = this.myArrayRemark[index];
            return obj
          }
        },
      ],
      impBatteryId: null,
      libraryId: null,
      isLoading: false,
      visible: false,
      editVisible: false,
      confirmLoading: false,
      mIStandardLibData: {},
      design: {},
      bigClient: document.documentElement.clientHeight > 700,
      windowHeight: document.documentElement.clientHeight - 150,
      form: this.$form.createForm(this, { name: 'form' }),
      editForm: this.$form.createForm(this, { name: 'editForm' }),
      resultData: [],
      navigationData: [],
      processList: [],
      myArrayProcess: [],
      myArrayProject: [],
      myArrayRemark: [],
      libraryData: {},

      mrWidth:document.body.clientWidth - ((document.body.clientWidth - 200) * 0.8) - 140 - 55 - 200,
    };
  },
  created() {
    getMIStandardByImpBatteryId(this.deliverImpBatteryId).then(res => {
      this.libraryId = res.data
      this.impBatteryId = this.deliverImpBatteryId
      this.getAndRenderLibData(this.libraryId, this.impBatteryId)
      this.getAndRenderDataByLibraryId(this.libraryId, this.impBatteryId)
      EventBus.$on('miVersionEvent', (libraryId) => {
        if (libraryId && this.impBatteryId) {
          this.libraryId = libraryId
          this.getAndRenderLibData(libraryId, this.impBatteryId)
          this.getAndRenderDataByLibraryId(libraryId, this.impBatteryId)
        }
      })
    })
  },
  destroyed() {
    EventBus.$off('miVersionEvent', () => {
    })
  },
  mounted() {
    $(".ant-breadcrumb-separator").eq(2).css('display','none')
    $(".navigationClass").find('th').css('backgroundColor', 'white').css('font', 'bold normal 18px arial').css('color', 'DeepSkyBlue').css('border', '0')
    document.getElementsByClassName("ant-layout-content")[0].style.backgroundColor = 'white';
  },
  methods: {
    // 设置每一行的rowSpan
    setRowSpan(data, columnName, rowSpanArray) {
      let myArray = rowSpanArray;
      //保存上一个name
      var lastName = "";
      //相同name出现的次数
      var count = 0;
      //该name第一次出现的位置
      var startindex = 0;
      for (var i = 0; i < data.length; i++) {
        //这里是合并columnName列，根据各自情况大家可以自己完善
        var val = data[(i)][columnName];
        if (!val) { //如果列值为空就不合并
          count = 1;
          lastName = val;
          startindex = i;
          myArray[i] = 1
        } else if (i === 0) {
          lastName = val;
          count = 1;
          myArray[0] = 1
        } else {
          if (val === lastName) {
            count++;
            myArray[startindex] = count;
            myArray[i] = 0
          } else {
            count = 1;
            lastName = val;
            startindex = i;
            myArray[i] = 1
          }
        }
      }
      // console.log(myArray);
    },
    miExportTable() {
      miOutputExportExcel({ impBatteryId: this.impBatteryId, libraryId: this.libraryData.id }).then((res) => {
        const fileName = 'MI设计导出表.xlsx';
        if (!res) return
        const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' }) // 构造一个blob对象来处理数据，并设置文件类型
        if (window.navigator.msSaveOrOpenBlob) { //兼容IE10
          navigator.msSaveBlob(blob, fileName)
        } else {
          const href = URL.createObjectURL(blob) //创建新的URL表示指定的blob对象
          const a = document.createElement('a') //创建a标签
          a.style.display = 'none'
          a.href = href // 指定下载链接
          a.download = fileName //指定下载文件名
          a.click() //触发下载
          URL.revokeObjectURL(a.href) //释放URL对象
        }
      })
    },
    miReImport() {
      this.$refs.miLibDataDialog.miChooseModelImport(this.impBatteryId, this.libraryId)
    },
    getAndRenderDataByLibraryId(libraryId, impBatteryId) {
      getMIVersionCurList({ libraryId: libraryId, status: 2 }, impBatteryId).then(res => {
        this.$nextTick(() => {
          const finallyData = res.data
          this.resultData = finallyData.curData
          this.setRowSpan(this.resultData, 'process', this.myArrayProcess);
          this.setRowSpan(this.resultData, 'project', this.myArrayProject);
          this.setRowSpan(this.resultData, 'remark', this.myArrayRemark);
          let finallyList = []
          this.processList = []
          this.navigationData = []
          for (let i = 0; i < this.resultData.length; i++) {
            if (this.resultData[i].process) {
              this.processList.push(this.resultData[i].process)
            }
            var curItem = finallyData.curData[i]
            var oriItem = finallyData.impData[i]
            this.resultData[i].oriProductParam = curItem.productParam !== oriItem.productParam ? oriItem.productParam : undefined
            this.resultData[i].oriSpecification1 = curItem.specification1 !== oriItem.specification1 ? oriItem.specification1 : undefined
            this.resultData[i].oriSpecification2 = curItem.specification2 !== oriItem.specification2 ? oriItem.specification2 : undefined
            this.resultData[i].oriSpecification3 = curItem.specification3 !== oriItem.specification3 ? oriItem.specification3 : undefined
            this.resultData[i].oriSpecification4 = curItem.specification4 !== oriItem.specification4 ? oriItem.specification4 : undefined
          }
          let orderProcessList = [...new Set(this.processList)]
          orderProcessList.forEach(item => {
            finallyList.push({
              navigationItem: item
            })
          })
          this.navigationData = finallyList
          this.renderMIVersionData(finallyData.compareMap.diffList, finallyData.compareMap.sameList, this.resultData)
        })
      })
    },
    renderMIVersionData(diff, same, impData) {
      $(document).ready(function () {
        let $row = $('.miTabClass #develop').find('table').eq(2).find("tbody tr")

        let rowSize = $row.length

        const temArr=['productParam','specification1','specification2','specification3','specification4']

        for (let i = 0; i < rowSize; i++) {
          let $col = $row.eq(i).find('td')
          let temIndex = 0
          for(let j = 0; j <$col.length;j++){
            if($col.eq(j).find('input').length > 0){
              $col.eq(j).attr("id", temArr[temIndex] + impData[i].id)
              temIndex++
            }
          }

          // $col.eq(2).attr("id", "productParam" + impData[i].id)
          // $col.eq(3).attr("id", "specification1" + impData[i].id)
          // $col.eq(4).attr("id", "specification2" + impData[i].id)
          // $col.eq(5).attr("id", "specification3" + impData[i].id)
          // $col.eq(6).attr("id", "specification4" + impData[i].id)
        }
        for (let i = 0; i < rowSize; i++) {
          let diffCol = diff[impData[i].id] // 指定id的行中与原始列值不同的列名有哪些

          // diffCol.forEach(function (item) {
          //   if (item === 'productParam') {
          //     $('.miTabClass #productParam' + impData[i].id).css("background-color", "yellow")
          //   }
          //   if (item === 'specification1') {
          //     $('.miTabClass #specification1' + impData[i].id).css("background-color", "yellow")
          //   }
          //   if (item === 'specification2') {
          //     $('.miTabClass #specification2' + impData[i].id).css("background-color", "yellow")
          //   }
          //   if (item === 'specification3') {
          //     $('.miTabClass #specification3' + impData[i].id).css("background-color", "yellow")
          //   }
          //   if (item === 'specification4') {
          //     $('.miTabClass #specification4' + impData[i].id).css("background-color", "yellow")
          //   }
          // })

          diffCol.forEach(function (item) {
            if (item === 'productParam') {
              $('.miTabClass #productParam' + impData[i].id).addClass("mark")
            }
            if (item === 'specification1') {
              $('.miTabClass #specification1' + impData[i].id).addClass("mark")

            }
            if (item === 'specification2') {
              $('.miTabClass #specification2' + impData[i].id).addClass("mark")

            }
            if (item === 'specification3') {
              $('.miTabClass #specification3' + impData[i].id).addClass("mark")

            }
            if (item === 'specification4') {
              $('.miTabClass #specification4' + impData[i].id).addClass("mark")

            }
          })
        }
        for (let i = 0; i < rowSize; i++) {
          let sameCol = same[impData[i].id] // 指定id的行中与原始列值相同的列名有哪些
          // sameCol.forEach(function (item) {
          //   if (item === 'productParam') {
          //     $('.miTabClass #productParam' + impData[i].id).css("background-color", "white")
          //   }
          //   if (item === 'specification1') {
          //     $('.miTabClass #specification1' + impData[i].id).css("background-color", "white")
          //   }
          //   if (item === 'specification2') {
          //     $('.miTabClass #specification2' + impData[i].id).css("background-color", "white")
          //   }
          //   if (item === 'specification3') {
          //     $('.miTabClass #specification3' + impData[i].id).css("background-color", "white")
          //   }
          //   if (item === 'specification4') {
          //     $('.miTabClass #specification4' + impData[i].id).css("background-color", "white")
          //   }
          // })
          sameCol.forEach(function (item) {
            if (item === 'productParam') {
              $('.miTabClass #productParam' + impData[i].id).removeClass("mark")
            }
            if (item === 'specification1') {
              $('.miTabClass #specification1' + impData[i].id).removeClass("mark")
            }
            if (item === 'specification2') {
              $('.miTabClass #specification2' + impData[i].id).removeClass("mark")
            }
            if (item === 'specification3') {
              $('.miTabClass #specification3' + impData[i].id).removeClass("mark")
            }
            if (item === 'specification4') {
              $('.miTabClass #specification4' + impData[i].id).removeClass("mark")
            }
          })
        }
      })
    },
    getAndRenderLibData(id, impBatteryId) {
      getMIStandardLibCurById({ id: id }, impBatteryId).then(res => {
        const finallyData = res.data;
        this.libraryData = finallyData.curData
        this.renderLibData(finallyData.compareMap.diffList, finallyData.compareMap.sameList)
      })
    },
    renderLibData(diff, same) {
      diff.forEach(item => {
        if (item === "documentStatus") {
          // $('.miTabClass #render_documentStatus' + this.libraryData.documentStatus).css("background-color", "yellow")
          $('.miTabClass #render_documentStatus' + this.libraryData.documentStatus).addClass("mark")
          if (this.libraryData.documentStatus === 0) {
            // $('.miTabClass #render_documentStatus1').css("background-color", "white")
            // $('.miTabClass #render_documentStatus2').css("background-color", "white")

            $('.miTabClass #render_documentStatus1').removeClass("mark")
            $('.miTabClass #render_documentStatus2').removeClass("mark")
          } else if (this.libraryData.documentStatus === 1) {
            // $('.miTabClass #render_documentStatus0').css("background-color", "white")
            // $('.miTabClass #render_documentStatus2').css("background-color", "white")

            $('.miTabClass #render_documentStatus0').removeClass("mark")
            $('.miTabClass #render_documentStatus2').removeClass("mark")
          } else {
            // $('.miTabClass #render_documentStatus0').css("background-color", "white")
            // $('.miTabClass #render_documentStatus1').css("background-color", "white")

            $('.miTabClass #render_documentStatus0').removeClass("mark")
            $('.miTabClass #render_documentStatus1').removeClass("mark")
          }
        } else if (item === "documentLevel") {
          // $('.miTabClass #render_documentLevel' + this.libraryData.documentLevel).css("background-color", "yellow")
          $('.miTabClass #render_documentLevel' + this.libraryData.documentLevel).addClass("mark")
          if (this.libraryData.documentLevel === 0) {
            // $('.miTabClass #render_documentLevel1').css("background-color", "white")
            $('.miTabClass #render_documentLevel1').removeClass("mark")
          } else {
            // $('.miTabClass #render_documentLevel0').css("background-color", "white")
            $('.miTabClass #render_documentLevel0').removeClass("mark")
          }
        } else {
          // $('.miTabClass #render_' + item).css("background-color", "yellow")
          $('.miTabClass #render_' + item).addClass("mark")
        }
      })
      same.forEach(item => {
        if (item === "documentStatus") {
          // $('.miTabClass .miTabClass #render_documentStatus0').css("background-color", "white")
          // $('.miTabClass .miTabClass #render_documentStatus1').css("background-color", "white")
          // $('.miTabClass .miTabClass #render_documentStatus2').css("background-color", "white")

          $('.miTabClass .miTabClass #render_documentStatus0').removeClass("mark")
          $('.miTabClass .miTabClass #render_documentStatus1').removeClass("mark")
          $('.miTabClass .miTabClass #render_documentStatus2').removeClass("mark")
        } else if (item === "documentLevel") {
          // $('.miTabClass .miTabClass #render_documentLevel0').css("background-color", "white")
          // $('.miTabClass .miTabClass #render_documentLevel1').css("background-color", "white")

          $('.miTabClass .miTabClass #render_documentLevel0').removeClass("mark")
          $('.miTabClass .miTabClass #render_documentLevel1').removeClass("mark")
        } else {
          // $('.miTabClass #render_' + item).css("background-color", "white")

          $('.miTabClass #render_' + item).removeClass("mark")

        }
      })
    },
    customRowEvent(record, index) {
      return {
        on: {
          mouseenter: (event) => {
            // $("#circle" + index).find("#displayPlusId").css('display', 'block')
          }, // 鼠标移入行
          mouseleave: (event) => {
            // $("#circle" + index).find("#displayPlusId").css('display', 'none')
          }
        },
      };
    },
    clickNavigation(record) {
      let row = this.processList.indexOf(record.navigationItem)
      let height = document.documentElement.clientHeight > 700 ? 40 : 39.67
      document.getElementsByClassName("ant-table-body")[2].scrollTop = height * row
    },
    // deleteMiversion(id) {
    //   updateMIVersionCur({ id: id, status: 2 }).then(() => this.getAndRenderDataByLibraryId(this.libraryId))
    // },
    // addMIVersion(data, index) {
    //   let params = {}
    //   if (index !== undefined) {
    //     // console.log('有数据')
    //     if (this.resultData.length > 1 && index < this.resultData.length - 1) {
    //       // console.log('多行数据点行的加号时(不包括最后一行)')
    //       let current = parseInt(this.resultData[index].serialNumber);
    //       let lastNumber = parseInt(this.resultData[index + 1].serialNumber);
    //       let subtracted = (lastNumber - current) / 10;
    //       params.serialNumber = lastNumber - subtracted
    //     } else if (this.resultData.length > 1 && index === this.resultData.length - 1) {
    //       params.serialNumber = parseInt(this.resultData[this.resultData.length - 1].serialNumber) + 10000000000000
    //       // console.log('多行数据点行的加号时(点最后一行)')
    //     } else {
    //       params.serialNumber = parseInt(this.resultData[this.resultData.length - 1].serialNumber) + 10000000000000
    //       // console.log('只有一行数据点行的加号时')
    //     }
    //   } else if (this.resultData.length === 0) {
    //     params.serialNumber = 0
    //     // console.log('没数据时')
    //   } else {
    //     // console.log('点最下面的加号时')
    //     params.serialNumber = parseInt(this.resultData[this.resultData.length - 1].serialNumber) + 10000000000000
    //   }
    //   params.libraryId = data.id
    //   // console.log('params:',params)
    //   // console.log('this.resultData[' + index + ']',this.resultData[index])
    //   insertMIVersionCur(params).then(() => this.getAndRenderDataByLibraryId(this.libraryId))
    // },
    updateData(event, record, column) {
      //修改时禁止输入
      let inputs = document.getElementsByTagName("input");
      let textareas = document.getElementsByTagName("textarea");
      let controlInput = [];
      let controltextarea = [];
      for (let i = 0; i < inputs.length; i++) {
        if (!inputs[i].disabled) {
          controlInput.push(inputs[i])
        }
      }
      for (let i = 0; i < textareas.length; i++) {
        if (!textareas[i].disabled) {
          controltextarea.push(textareas[i])
        }
      }
      for (let i = 0; i < controlInput.length; i++) {
        controlInput[i].disabled = true
      }
      for (let i = 0; i < controltextarea.length; i++) {
        controltextarea[i].disabled = true
      }
      let param = {}
      param[column] = event.target.value
      param['id'] = record.id
      this.isLoading = true
      updateMIVersionCur(param).then((res) => {
        this.$nextTick(() => {
          if (res.success) {
            this.$message.success('保存成功')
            this.getAndRenderDataByLibraryId(this.libraryData.libraryId, this.impBatteryId)
            setTimeout(() => {
              this.isLoading = false
            },500)
          } else {
            this.$message.error(res.message)
          }
          for (let i = 0; i < controlInput.length; i++) {
            controlInput[i].disabled = false
          }
          for (let i = 0; i < controltextarea.length; i++) {
            controltextarea[i].disabled = false
          }
        })
      })
    },
    updateLibData(event, record, column) {
      //修改时禁止输入
      let inputs = document.getElementsByTagName("input");
      let textareas = document.getElementsByTagName("textarea");
      let controlInput = [];
      let controltextarea = [];
      for (let i = 0; i < inputs.length; i++) {
        if (!inputs[i].disabled) {
          controlInput.push(inputs[i])
        }
      }
      for (let i = 0; i < textareas.length; i++) {
        if (!textareas[i].disabled) {
          controltextarea.push(textareas[i])
        }
      }
      for (let i = 0; i < controlInput.length; i++) {
        controlInput[i].disabled = true
      }
      for (let i = 0; i < controltextarea.length; i++) {
        controltextarea[i].disabled = true
      }
      let param = {}
      param[column] = event.target.value
      param['id'] = record.id
      this.isLoading = true
      updateMIStandardLibCur(param).then((res) => {
        this.$nextTick(() => {
          if (res.success) {
            this.$message.success('保存成功')
            this.getAndRenderLibData(this.libraryData.libraryId, this.impBatteryId)
            setTimeout(() => {
              this.isLoading = false
            },500)
          } else {
            this.$message.error(res.message)
          }
          for (let i = 0; i < controlInput.length; i++) {
            controlInput[i].disabled = false
          }
          for (let i = 0; i < controltextarea.length; i++) {
            controltextarea[i].disabled = false
          }
        })
      })
    },
  }
}
</script>
<style lang="less" scoped>

// head
.head-wrapper{
  display: flex;
  justify-content: space-between;
  height: 30px;
  margin: 15px 0 5px 195px;

}

.head-wrapper .btn{
  display: flex;
}

/deep/.ant-table.ant-table-bordered .ant-table-title {
  padding-right: 0px;
  padding-left: 0px;
  border: 1px solid #e8e8e8;
  border-bottom: 0;
}
/deep/.ant-table.ant-table-bordered .ant-table-footer {
  border: 1px solid #e8e8e8;
  border-top: 0;
}
/deep/.exportButtonClass {
  font-family: SourceHanSansSC;
  font-weight: 400;
  font-size: 15px;
  color: rgba(0,101,255,0.67);
}
textarea.ant-input {
  max-width: 100%;
  height: auto;
  min-height: 32px;
  line-height: 1.5;
  vertical-align: bottom;
  -webkit-transition: all 0.3s, height 0s;
  transition: all 0.3s, height 0s;
  border: none;
}

#iconPlusTd {
  border-top: #e8e8e8 solid 0px;
}

#develop {
  font-size: 12px;
  margin: 0 0px 30px 55px;
  color: #000;
}
/deep/.ant-breadcrumb a, .ant-breadcrumb span {
  color: black;
  font-weight: bold;
}

/deep/.ant-breadcrumb > span:last-child {
  color: black;
  font-weight: bold;
}

/deep/.ant-breadcrumb-separator {
  color: black;
  font-weight: bold;
}

/deep/.ant-table-bordered.ant-table-empty .ant-table-placeholder {
  border: 1px solid black;
}

#navigationId > div > div > div > div > div > table > tbody > tr > td {
  border: 0;
}

/deep/.ant-form-item {
  margin-bottom: 0px;
}

.renderTr td {
  border: #e8e8e8 solid 1px;
  text-align: center;
}

/deep/.ant-table-title {
  position: relative;
  padding: 0px;
  border-radius: 2px 2px 0 0;
  margin-top: -0.5px;
}

/deep/.ant-table-footer {
  position: relative;
  padding: 0px;
  border-radius: 2px 2px 0 0;
  background-color: white;
}

/deep/.ant-table-footer tr td {
  border-right: #e8e8e8 solid 1px;
  border-top: #e8e8e8 solid 1px;
  text-align: center;
}

input {
  width: 100%;
  height: 25px;
  margin: 0;
  border: 0;
  outline: none;
  text-align: center;
}
#tooltip{
  position:absolute;
  border:1px solid #333;
  background:#f7f5d1;
  padding:1px;
  color:#333;
  display:none;
}

.tab-title{
  padding: 0 10px;
}
div.tab-head div.active{
  font-size: 24px;
  font-weight: 700;
  color: rgba(0,73,176,1);
  margin-bottom: -4px;
  cursor: text;
}

.block{
  width: 50px;
height: 20px;
float: left;
  border: 1px solid #000;
  margin-right: 8px;
}

/deep/.ant-table-tbody > tr > td {
  padding: 5px;
}

/deep/.ant-table-thead > tr > th {
  background: white;
  padding: 12px 8px;
  font-weight: bold;
  border: 1px solid black;
}

/deep/.ant-table-bordered .ant-table-tbody > tr > td {
  border: 1px solid black;
}

/deep/.ant-table-bordered .ant-table-body > table {
  border-collapse: collapse;
  border-spacing: 0;
}

/deep/.ant-table-bordered .ant-table-header > table {
  border-collapse: collapse;
  border-spacing: 0;
}

/deep/.ant-table-thead > tr > th {
  border-bottom: 0;
  border-top: 0;
}

/deep/.ant-table-bordered .ant-table-title > table {
  border-collapse: collapse;
  border-spacing: 0;
}

/deep/.ant-table-bordered .ant-table-title > table > tr > td {
  border: 1px solid black;
}

/deep/.ant-table-bordered .ant-table-footer > table {
  border-collapse: collapse;
  border-spacing: 0;
  margin-top: -2px;
}

/deep/.ant-table-bordered .ant-table-footer > table > tr > td {
  border: 1px solid black;
}


</style>
