<template>
  <div class="container">

    <pbiTabs :tabsList="reviewStatusList" :activeKey="queryParam.status" @clickTab="handleTabsChange"></pbiTabs>

    <tableIndex
      :pageLevel='1'
      :tableTotal='tableTotal'
      :otherHeight="parseInt(100)"
      :pageTitleShow=false
      :loading='loading'
      @paginationChange="handlePageChange"
      @paginationSizeChange="handlePageChange"
    >
      <template #search>
        <pbiSearchContainer>
          <pbiSearchItem label='委托单号' :span="4">
            <a-input size='small' @keyup.enter.native='loadData' v-model='queryParam.folderNo'>
            </a-input>
          </pbiSearchItem>

          <pbiSearchItem label='测试项目' :span="4">
            <a-input size='small' @keyup.enter.native='loadData' v-model='queryParam.testAlias'>
            </a-input>
          </pbiSearchItem>

          <pbiSearchItem label='项目类型' :span="4">
            <a-select size='small' v-model='queryParam.projectType' @change='loadData' placeholder="请选择项目类型" allowClear>
              <a-select-option value="before_after">非温度循环</a-select-option>
              <a-select-option value="temp_cycle">温度循环</a-select-option>
            </a-select>
          </pbiSearchItem>

          <pbiSearchItem label='测试阶段' :span="4">
            <a-input size='small' @keyup.enter.native='loadData' v-model='queryParam.searchStage'>
            </a-input>
          </pbiSearchItem>

          <pbiSearchItem label='委托人' :span="4">
            <a-input size='small' @keyup.enter.native='loadData' v-model='queryParam.applicant'>
            </a-input>
          </pbiSearchItem>

          <pbiSearchItem :span="4" type='btn' class="search-container">
            <div class="secondary-btn">
              <a-button class="mr12" @click="loadData" type="primary">查询</a-button>
            </div>
            <div class="secondary-btn">
              <a-button class="mr12" @click="batchApprove" type="primary">批量通过</a-button>
            </div>
            <div class="secondary-btn">
              <a-button @click="resetSearch">重置</a-button>
            </div>
          </pbiSearchItem>
        </pbiSearchContainer>
      </template>

      <template #table>
        <ag-grid-vue :style="{height:tableHeight}"
                     class='table ag-theme-balham review-table'
                     :tooltipShowDelay="0"
                     :columnDefs='columnDefs'
                     :rowData='rowData'
                     rowSelection="multiple"
                     :gridOptions="gridOptions"
                     @grid-ready="onGridReady"
                     :defaultColDef='defaultColDef'>
        </ag-grid-vue>
      </template>
    </tableIndex>

    <a-modal title="测试数据" width="80%" :visible="dataVisible" @cancel="closeData" :style="{ top: '20px' }" :bodyStyle="{ maxHeight: '80vh', overflow: 'auto', padding: '0' }">
      <template slot="footer">
        <div>
          <a-button key="back" @click="closeData">关闭</a-button>
        </div>
      </template>
      <stReviewBaDetail v-if="dataVisible" :stReviewBean="stReviewBean" ref="stReviewBaDetail" ></stReviewBaDetail>
    </a-modal>

    <!-- 驳回记录模态框 -->
    <a-modal title="驳回记录" width="80%" :visible="rejectHistoryVisible" @cancel="closeRejectHistory" :style="{ top: '20px' }" :bodyStyle="{ maxHeight: '80vh', overflow: 'auto', padding: '24px' }">
      <template slot="footer">
        <div>
          <a-button key="back" @click="closeRejectHistory">关闭</a-button>
        </div>
      </template>
      <div>
        <tableIndex
          :pageLevel='parseInt("2")'
          :tableTotal='rejectHistoryTotal'
          :pageTitleShow=false
          height="600"
          :paginationShow=false
          :loading='rejectHistoryLoading'
        >
          <template #table>
            <ag-grid-vue class='table ag-theme-balham'
                         style="height: 390px"
                         :tooltipShowDelay="0"
                         :columnDefs='rejectHistoryColumnDefs'
                         :rowData='rejectHistoryRowData'
                         @grid-ready="onRejectHistoryGridReady"
                         :defaultColDef='defaultColDef'>
            </ag-grid-vue>
          </template>
        </tableIndex>
      </div>
    </a-modal>

    <!-- 驳回理由模态框 -->
    <a-modal title="确认驳回" :width="500" :visible="rejectModalVisible" @ok="confirmReject" @cancel="cancelReject">
      <a-form-item label="驳回理由" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-textarea
          v-model="rejectReason"
          placeholder="请输入驳回理由"
          :rows="4"
          :maxLength="500"
          show-count
        />
      </a-form-item>
    </a-modal>
  </div>
</template>

<script>
  import {validSafetyTestExport} from "@/api/modular/system/testProgressManager";
  import {mapGetters} from "vuex";
  import Treeselect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css"
  import pbiTabs from "@/components/pageTool/components/pbiTabs.vue";
  import stReviewBaDetail from "@/views/system/testProgress/workbench/components/stReviewBaDetail.vue";
  import {approveStReview, rejectStReview, safetyTestReviewListPage, batchApproveStReview} from "@/api/modular/system/safetyTestReviewManager";

  export default {
    components: {
      stReviewBaDetail,
      pbiTabs,
      Treeselect,
      showData: {
        template: "<a class='code_link' @click='params.onClick(params.data)'>{{params.value}}</a>"
      },
      showLog: {
        template: "<a class='code_link' @click='params.onClick(params.data)'>{{params.value == '1' ? '已复核' : '未复核'}}</a>"
      },
      reviewButtons: {
        template: `<div style="display: flex; gap: 12px; justify-content: center; align-items: center;">
          <a class="code_link" :style="{color: params.data.status == '1' ? '#d9d9d9' : '#1890ff'}" :disabled="params.data.status == '1'" v-if="hasPerm('safetyTestReview:approveStReview')" @click="params.onApprove(params.data)">通过</a>
          <a class="code_link" :style="{color: params.data.status == '1' ? '#d9d9d9' : '#ff4d4f'}" :disabled="params.data.status == '1'" v-if="hasPerm('safetyTestReview:approveStReview')" @click="params.onReject(params.data)">驳回</a>
          <a class="code_link" :style="{color: '#1890ff'}" v-if="hasPerm('safetyTestReview:editData')" @click="params.onEdit(params.data)">修改</a>
        </div>`
      }
    },
    data() {
      return {
        stReviewBean: {},
        reviewStatusList:[
          {value:null,label:"全部"},
          {value:"0",label:"未复核"},
          {value:"1",label:"已复核"}
        ],
        gridOptions: {
          onSelectionChanged: this.onSelectionChanged,
          suppressCellSelection: false
        },
        selectedRows:[],
        abnormalKeyValue: {
          ongoing: '进行中',
          testDone: '状态正常-测试完成',
          earlyEnd: '状态正常-提前结束',
          batteryDisassembly: '状态正常-电池拆解',
          pressureDrop: '掉压失效-终止测试',
          abnormalHot: '异常发热-终止测试',
          openShellAndLeak: '开壳漏液-终止测试',
          shellRust: '壳体生锈-终止测试',
          operationError: '作业错误-终止测试',
          thermalRunaway: '热失控-终止测试',
          acrException: '内阻异常-终止测试',
          swelling: '鼓包形变-终止测试'
        },
        dataVisible:false,
        record: {},
        rejectModalVisible: false,
        rejectReason: '',
        currentRejectRecord: null,
        // 驳回记录相关数据
        rejectHistoryVisible: false,
        rejectHistoryLoading: false,
        rejectHistoryTotal: 0,
        rejectHistoryRowData: [],
        rejectHistoryGridApi: null,
        rejectHistoryColumnDefs: [
          {
            headerName: '序号',
            field: 'id',
            width: 50,
            cellRenderer: function (params) {
              return parseInt(params.node.id) + 1
            },
          },
          {
            headerName: '操作时间',
            field: 'rejectTime',
            minWidth: 140,
            width: 140,
          },
          {
            headerName: '操作人',
            field: 'rejectUserName',
            minWidth: 100,
            width: 100,
          },
          {
            headerName: '操作内容',
            field: 'content',
            minWidth: 120,
            width: 120,
          },
          {
            headerName: '下一节点',
            field: 'nextTaskName',
            minWidth: 120,
            width: 120,
          },
          {
            headerName: '下一节点操作人',
            field: 'nextUserName',
            minWidth: 120,
            width: 120,
          },
          {
            headerName: '驳回信息',
            field: 'opinion',
            minWidth: 120,
            width: 120,
            tooltipValueGetter: (p) => p.value,
          },
          {
            headerName: '修改内容',
            field: 'updateContent',
            flex: 1,
            minWidth: 200,
            width: 200,
            tooltipValueGetter: (p) => p.value,
          }
        ],
        loading: false,
        gridApi: null,
        columnApi: null,
        tableTotal: 0,
        queryParam: {status:'0'},
        rowData: [],
        defaultColDef: {
          filter: false,
          floatingFilter: false,
          editable: false,
        },
        tableHeight: document.body.clientHeight - 201 +'px' ,
        pageNo: 1,
        pageSize: 20,
        columnDefs: [
          {
            width: 40,
            checkboxSelection: true,
            headerCheckboxSelection:true
          },
          {
            headerName: '序号',
            field: 'id',
            width: 60,
            cellRenderer: function (params) {
              return parseInt(params.node.id) + 1
            },
          },
          // {
          //   headerName: '项目类型',
          //   field: 'projectType',
          //   width: 100,
          //   cellRenderer: function (params) {
          //     if(params.data.projectType == 'before_after'){
          //       return '非温度循环'
          //     }else {
          //       return '温度循环'
          //     }
          //   },
          // },
          {
            headerName: '委托单号',
            field: 'folderNo',
            width: 120,
          },
          {
            headerName: '测试项目',
            field: 'testAlias',
            width: 180,
            cellRenderer: 'showData',
            cellRendererParams: {onClick: this.checkSafetyTestReport},
            tooltipValueGetter: (p) => p.value,
          },
          {
            headerName: '测试阶段',
            field: 'stage',
            width: 100,
            cellRenderer: (params) => {
              return this.formatStageDisplay(params.data);
            },
          },
          {
            headerName: '测试编码',
            field: 'cellCode',
            flex: 1,
            minWidth: 200,
            cellRenderer: 'showData',
            cellStyle: () =>  {return {textAlign:'left'}},
            cellRendererParams: {onClick: this.openData},
            tooltipValueGetter: (p) => p.value,
          },
          {
            headerName: '委托人',
            field: 'applicant',
            width: 80,
          },
          {
            headerName: '产品名称',
            field: 'productName',
            flex: 0.8,
            minWidth: 120,
          },
          {
            headerName: '测试类型',
            field: 'testType',
            flex: 0.8,
            minWidth: 120,
          },
          {
            headerName: '测试员',
            field: 'testMan',
            width: 80,
          },
          {
            headerName: '技师提交时间',
            field: 'createTime',
            width: 140,
          },
          {
            headerName: '责任人',
            field: 'engineerName',
            width: 100,
          },
          {
            headerName: '复核状态',
            field: 'status',
            width: 80,
            cellRenderer: 'showLog',
            cellRendererParams: {onClick: this.openLog},
          },
          {
            headerName: '操作',
            field: 'dataReview',
            cellRenderer: 'reviewButtons',
            cellRendererParams: {onApprove: this.handleApprove, onReject: this.handleReject, onEdit: this.handleEdit},
            cellStyle: () => {return {textAlign:'center'}},
            width: 150,
          }
        ],
      }
    },
    computed:{
      ...mapGetters(['userInfo']),
    },
    mounted() {
      this.loadData()
    },
    methods: {
      loadData() {
        this.loading = true
        safetyTestReviewListPage({
          ...{
            pageNo: this.pageNo,
            pageSize: this.pageSize
          }, ...this.queryParam
        }).then((res) => {
          if (res.success) {
            this.rowData = res.data.rows
            this.tableTotal = res.data.totalRows
          }
        }).finally(() => {
          if(this.rowData.length == 0 && this.pageNo > 1){
            this.pageNo = Math.max(1, Math.ceil(this.tableTotal / this.pageSize))
            this.loadData()
          }
          this.loading = false
        })
      },
      onGridReady(params) {
        this.gridApi = params.api;
        this.columnApi = params.columnApi;
      },
      // 测试阶段显示转换方法
      formatStageDisplay(record) {
        if (record.projectType == 'before_after') {
          if (record.stage == 0) {
            return '测试前';
          } else {
            return '测试后';
          }
        } else {
          if (record.stage == 0) {
            return '初始性能检测';
          } else {
            return record.stage || '-';
          }
        }
      },
      batchApprove() {
        // 检查是否有选中的行
        if (!this.selectedRows || this.selectedRows.length === 0) {
          this.$message.warning('请先选择要批量通过的数据');
          return;
        }

        // 过滤出未复核的数据（status != '1'）
        const pendingReviewRows = this.selectedRows.filter(row => row.status !== '1');

        if (pendingReviewRows.length === 0) {
          this.$message.warning('所选数据中没有待复核的记录');
          return;
        }

        // 如果选中的数据中有已复核的，提示用户
        if (pendingReviewRows.length < this.selectedRows.length) {
          this.$message.info(`已过滤掉 ${this.selectedRows.length - pendingReviewRows.length} 条已复核的数据`);
        }

        // 构建确认对话框内容
        const displayRows = pendingReviewRows; // 显示所有数据，通过滚动条控制

        this.$confirm({
          title: '批量复核通过确认',
          width: 800,
          content: (h) => h('div', { style: 'padding: 10px 0;' }, [
            // 统计信息
            h('div', {
              style: 'background: #f0f9ff; border: 1px solid #bae6fd; border-radius: 6px; padding: 12px; margin-bottom: 16px; color: #0369a1;'
            }, [
              h('div', { style: 'font-weight: 600; font-size: 14px;' }, `📊 共选择 ${pendingReviewRows.length} 条待复核数据`)
            ]),

            // 数据表格
            h('div', { style: 'border: 1px solid #e5e7eb; border-radius: 6px; overflow: hidden; margin-bottom: 16px;' }, [
              // 表头
              h('div', {
                style: 'background: #f9fafb; border-bottom: 1px solid #e5e7eb; padding: 8px 12px; display: flex; font-weight: 600; color: #374151;'
              }, [
                h('div', { style: 'width: 60px; text-align: center;' }, '序号'),
                h('div', { style: 'width: 170px; padding-left: 12px;' }, '委托单号'),
                h('div', { style: 'width: 120px; padding-left: 12px;' }, '测试项目'),
                h('div', { style: 'width: 100px; padding-left: 12px;' }, '测试阶段'),
                h('div', { style: 'flex: 1; padding-left: 12px;' }, '测试编码')
              ]),

              // 数据行容器（带滚动条，固定高度显示5行）
              h('div', {
                style: 'max-height: 200px; overflow-y: auto; overflow-x: hidden;'
              }, [
                ...displayRows.map((row, index) =>
                  h('div', {
                    style: `border-bottom: 1px solid #f3f4f6; padding: 8px 12px; display: flex; align-items: center; ${index % 2 === 0 ? 'background: #fafafa;' : 'background: white;'}`
                  }, [
                    h('div', { style: 'width: 60px; text-align: center; color: #6b7280; font-weight: 500;' }, index + 1),
                    h('div', { style: 'width: 170px; padding-left: 12px; color: #1f2937; font-weight: 500;' }, row.folderNo || '-'),
                    h('div', {
                      style: 'width: 120px; padding-left: 12px; color: #6b7280; font-size: 12px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;'
                    }, row.testAlias || '-'),
                    h('div', {
                      style: 'width: 100px; padding-left: 12px; color: #6b7280; font-size: 12px;'
                    }, this.formatStageDisplay(row)),
                    h('div', { style: 'flex: 1; padding-left: 12px; color: #1f2937;' }, row.cellCode || '-')
                  ])
                )
              ])
            ]),

            // 确认提示
            h('div', {
              style: 'background: #fef3c7; border: 1px solid #fbbf24; border-radius: 6px; padding: 12px; color: #92400e;'
            }, [
              h('div', { style: 'display: flex; align-items: center;' }, [
                h('span', { style: 'margin-right: 8px; font-size: 16px;' }, '⚠️'),
                h('span', { style: 'font-weight: 600;' }, '确定要批量复核通过这些数据吗？')
              ])
            ])
          ]),
          onOk: () => {
            // 提取ID列表
            const reviewIds = pendingReviewRows.map(row => row.id);
            // 调用批量审批API
            batchApproveStReview({ reviewIds }).then(res => {
              if (res.success) {
                this.$message.success(`批量复核通过成功，共处理 ${pendingReviewRows.length} 条数据`);
                // 清空选中状态
                this.selectedRows = [];
                if (this.gridApi) {
                  this.gridApi.deselectAll();
                }
                // 刷新数据
                this.loadData();
              } else {
                this.$message.error('批量复核失败：' + res.message);
              }
            }).catch(error => {
              console.error('批量复核请求失败:', error);
              this.$message.error('批量复核请求失败，请稍后重试');
            });
          }
        });
      },
      onSelectionChanged(event) {
        // 获取当前选中的行
        const selectedNodes = this.gridApi.getSelectedNodes();
        const selectedData = selectedNodes.map(node => node.data);
        // 更新选中的行数据
        this.selectedRows = selectedData;
      },
      openData(record) {
        this.stReviewBean = record
        this.dataVisible = true
        this.$nextTick(() => {
            this.$refs.stReviewBaDetail.loadData(true,false)
        })
      },
      handleEdit (record) {
        this.stReviewBean = record
        this.dataVisible = true
        this.$nextTick(() => {
            this.$refs.stReviewBaDetail.loadData(true,true)
        })
      },
      checkSafetyTestReport(stReviewBean) {
        const id = stReviewBean.ordTaskId;
        const alias = stReviewBean.testAlias ? stReviewBean.testAlias : "";
        validSafetyTestExport({ordTaskId: id, exportType: "handleResult"}).then(res => {
          if (res.success) {
            let safetyTestList = res.data.safetyTestList
            let safetyTestIds = (safetyTestList.map(v => v.id)).join(',')
            let videoFlag = safetyTestList.findIndex(v => v.videoFlag === '1') !== -1 ? 1 : 0
            let pictureFlag = safetyTestList.findIndex(v => v.pictureFlag === '1') !== -1 ? 1 : 0
            let attachmentFlag = safetyTestList.findIndex(v => v.attachmentFlag === '1') !== -1 ? 1 : 0
            window.open("/v_report_preview?safetyTestIds=" + encodeURIComponent(safetyTestIds) + "&alias=" + encodeURIComponent(alias) + "&offFlag=1&onlineFlag=0"
              + '&videoFlag=' + videoFlag + '&pictureFlag=' + pictureFlag + '&attachmentFlag=' + attachmentFlag + "&type=安全测试", "_blank")
          } else {
            this.$message.warning("没有可查看的数据")
          }
        })
      },
      openLog(record) {
        // 查看驳回记录
        this.rejectHistoryVisible = true;
        this.rejectHistoryLoading = true;
        this.rejectHistoryRowData = record.rejectHisMsg ? JSON.parse(record.rejectHisMsg) : [];
        this.rejectHistoryTotal = this.rejectHistoryRowData.length;
        this.rejectHistoryLoading = false;
      },
      closeData() {
        this.dataVisible = false
      },
      handlePageChange(value) {
        let {current, pageSize} = value
        this.pageNo = current
        this.pageSize = pageSize
        this.loadData()
      },
      resetSearch() {
        this.queryParam = {status:'0'}
        this.loadData()
      },
      handleTabsChange(value) {
        this.queryParam.status = value
        this.loadData()
      },
      handleApprove(record) {
        this.$confirm({
          title: '确认通过',
          content: (h) => h('div', [
            h('div', `委托单：${record.folderNo}`),
            h('div', `测试项目：${record.testAlias}`),
            h('div', `测试阶段：${this.formatStageDisplay(record)}`),
            h('div', `测试编码：${record.cellCode}`),
            h('div', [h('strong', '确定要复核通过该数据吗？')])
          ]),
          onOk: () => {
            approveStReview(record).then(res => {
              if (res.success) {
                this.$message.success('数据复核通过');
                this.loadData();
              } else {
                this.$message.error('操作失败：' + res.message);
              }
            });
          }
        });
      },
      handleReject(record) {
        this.currentRejectRecord = record;
        this.rejectReason = '';
        this.rejectModalVisible = true;
      },
      confirmReject() {
        if (!this.rejectReason || this.rejectReason.trim() === '') {
          this.$message.warning('请输入驳回理由');
          return;
        }

        const params = {
          id: this.currentRejectRecord.id,
          safetyTestId: this.currentRejectRecord.safetyTestId,
          cellCode: this.currentRejectRecord.cellCode,
          todoTaskId: this.currentRejectRecord.todoTaskId,
          rejectHisMsg: this.currentRejectRecord.rejectHisMsg,
          testData: this.currentRejectRecord.testData,
          ordTaskId: this.currentRejectRecord.ordTaskId,
          rejectMsg: this.rejectReason.trim()
        };

        rejectStReview(params).then(res => {
          if (res.success) {
            this.$message.success('数据复核已驳回');
            this.loadData();
            this.rejectModalVisible = false;
            this.rejectReason = '';
            this.currentRejectRecord = null;
          } else {
            this.$message.error('操作失败：' + res.message);
          }
        });
      },
      cancelReject() {
        this.rejectModalVisible = false;
        this.rejectReason = '';
        this.currentRejectRecord = null;
      },
      // 关闭驳回记录模态框
      closeRejectHistory() {
        this.rejectHistoryVisible = false;
        this.rejectHistoryRowData = [];
        this.rejectHistoryTotal = 0;
      },
      onRejectHistoryGridReady(params) {
        this.rejectHistoryGridApi = params.api;
      },
    }
  }
</script>

<style lang='less' scoped>
  @import '/src/components/pageTool/style/pbiSearchItem.less';

</style>