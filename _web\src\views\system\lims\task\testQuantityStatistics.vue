<template>
  <div style="padding: 10px;">
    <pbiTabs :tabsList="laboratoryList" :activeKey="laboratoryId" @clickTab="callback"></pbiTabs>
    <div style="background-color: #FFFFFF; border-radius: 4px;">
      <div class="search-container" style="margin: 0px 10px 0px 10px; display: flex; flex-wrap: wrap;">
        <div class="operate-row" v-for="(room, index) in analysisRooms">
          <div class="label">{{room}}人数：</div>
          <a-input-number v-model="deptPeopleNums[index]" :min="1"></a-input-number>
        </div>

        <div class="flex-sb-center-row" :style="{width: clientWidth > 1700 ? '45%' : '100%'}">
          <div class="operate-row">
            <div class="label">时间维度：</div>
            <div style="margin-top: 10px; width: 80px;">
              <a-select style="width: 80px; font-size: 12px;" v-model="timeDimension" @change="getTestQuantityStatistics">
                <a-select-option value="按周">按周</a-select-option>
                <a-select-option value="按月">按月</a-select-option>
              </a-select>
            </div>
            <div class="label">起止日期：</div>
            <div style="margin-top: 10px; width: 200px; font-size: 12px;">
              <a-range-picker :allowClear="false" v-model="timeRange" @change="timeRangeChange"></a-range-picker>
            </div>
          </div>
          <div style="margin-top: 10px;" class="secondary-btn">
            <a-button style="margin-right: 8px;" size="small" type="primary" @click="getTestQuantityStatistics">查询</a-button>
            <a-button style="margin-right: 8px;" size="small" @click="resetParam">重置</a-button>
            <a-button size="small" type="primary" @click="exportTestQuantityStatistics">导出</a-button>
          </div>
        </div>
      </div>

      <div style="padding: 10px">
        <a-table class="statisticsTable"
                 :columns="columns"
                 :data-source="testQuantityStatistics"
                 :loading="isLoading"
                 :pagination="paginationConfig"
                 bordered>
        </a-table>
      </div>
    </div>
  </div>
</template>

<script>
import XLSX from "xlsx/dist/xlsx.full.min.js";
import {saveAs} from "file-saver";
import {getTestQuantityStatistics} from "@/api/modular/system/testProgressManager";
import moment from "moment";
require('moment-timezone');
import pbiTabs from "@/components/pageTool/components/pbiTabs.vue";

export default {
  name: "testQuantityStatistics",
  components: {
    pbiTabs
  },
  data() {
    return {
      clientWidth: document.body.clientWidth,
      laboratoryList: [
        {value:'HZ_YJ_DL_JM', label:'精密实验室', show: true}
      ],
      laboratoryId: "HZ_YJ_DL_JM",
      testQuantityStatistics: [],
      columns: [],
      isLoading: true,
      paginationConfig: {
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '30', '40', '50'], // 显示的每页数量选项
        size: "small",
        showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
      },
      timeRange: [],
      timeRangeBegin: null,
      timeRangeEnd: null,
      timeDimension: "按周",
      deptPeopleNums: [2, 8, 4, 3, 6],
      analysisRooms: ['微观研究部', '化学测试组', '物理测试组', '电化学分析部', '结构分析中心'],
    }
  },
  created() {
    // 默认展示当年数据，获取当年1号、今天日期
    const firstDayOfYear = moment().startOf('year'); // 当年1号
    const today = moment();// 当前日期
    this.timeRange = [firstDayOfYear, today];
    // 传递到后端格式：YYYY-MM-DD
    this.timeRangeBegin = firstDayOfYear.format('YYYY-MM-DD');
    this.timeRangeEnd = today.format('YYYY-MM-DD');

    // 处理表头
    this.columns = []
    this.columns.push(
      {
        title: "周期",
        align: "center",
        width: 80,
        dataIndex: "period"
      },
      ...this.analysisRooms.map((room, roomIndex) => {
        return {
          title: room,
          children: [
            {
              title: "接收数量",
              align: "center",
              width: 95,
              dataIndex: room+".RECEIVECOUNT"
            },
            {
              title: "计划完成数量",
              align: "center",
              width: 95,
              dataIndex: room+".PLANCOUNT"
            },
            {
              title: "实际完成数量",
              align: "center",
              width: 95,
              dataIndex: room+".REALCOUNT"
            },
            {
              title: "人员效率",
              align: "center",
              width: 95,
              customRender: (text, record) => {
                let realCount = record[room].REALCOUNT;
                if (this.deptPeopleNums[roomIndex] > 0) {
                  let peopleEfficiency = realCount / this.deptPeopleNums[roomIndex];
                  return `${this.roundToFixed(peopleEfficiency, 2)}`;
                }
                return '-';
              }
            },
            {
              title: "计划达成率",
              align: "center",
              width: 95,
              customRender: (text, record) => {
                let planCount = record[room].PLANCOUNT;
                if (planCount == 0) {
                  return '-';
                }
                let realCount = record[room].REALCOUNT;
                let realPercentage = (realCount / planCount) * 100;
                return `${this.roundToFixed(realPercentage, 2)}%`;
              }
            },
            {
              title: "测试费用",
              align: "center",
              width: 95,
              dataIndex: room+".PRICESUM"
            }
          ]
        }
      })
    )

    this.getTestQuantityStatistics()
  },
  computed: {},
  methods: {
    resetParam() {
      this.timeDimension = "按周"
      // 默认展示当年数据，获取当年1号、今天日期
      const firstDayOfYear = moment().startOf('year'); // 当年1号
      const today = moment();// 当前日期
      this.timeRange = [firstDayOfYear, today];
      // 传递到后端格式：YYYY-MM-DD
      this.timeRangeBegin = firstDayOfYear.format('YYYY-MM-DD');
      this.timeRangeEnd = today.format('YYYY-MM-DD');

      this.getTestQuantityStatistics()
    },
    callback(key) {
      this.laboratoryId = key
    },
    timeRangeChange(a, b) {
      this.timeRangeBegin = b[0]
      this.timeRangeEnd = b[1]
      this.getTestQuantityStatistics()
    },
    getTestQuantityStatistics() {
      this.isLoading = true
      getTestQuantityStatistics({
        timeRangeBegin: this.timeRangeBegin,
        timeRangeEnd: this.timeRangeEnd,
        timeDimension: this.timeDimension
      }).then((res) => {
        this.testQuantityStatistics = res.data
        this.isLoading = false
      })
    },
    exportTestQuantityStatistics() {
      // 表头
      let mergedHeaderRow = ['']
      this.analysisRooms.forEach(room => {
        mergedHeaderRow.push(room)
        mergedHeaderRow.push(...Array(5).fill(''))
      })
      // 创建具体数据的二维数组
      let rowDatas = [
        mergedHeaderRow,
        ['周期',
          '接收数量', '计划完成数量', '实际完成数量', '人员效率', '计划达成率', '测试费用',
          '接收数量', '计划完成数量', '实际完成数量', '人员效率', '计划达成率', '测试费用',
          '接收数量', '计划完成数量', '实际完成数量', '人员效率', '计划达成率', '测试费用',
          '接收数量', '计划完成数量', '实际完成数量', '人员效率', '计划达成率', '测试费用',
          '接收数量', '计划完成数量', '实际完成数量', '人员效率', '计划达成率', '测试费用'],
        ...this.testQuantityStatistics.map(row => {
          let rowData = [row.period]
          // 科室数据
          this.analysisRooms.forEach((room, index) => {
            let planCount = row[room].PLANCOUNT
            let realCount = row[room].REALCOUNT
            let peopleEfficiency = 0.0;
            if (this.deptPeopleNums[index] > 0) {
              peopleEfficiency = this.roundToFixed(realCount / this.deptPeopleNums[index], 2)
            }
            let realPercentage = '-';
            if (planCount != 0) {
              let tmp = (realCount / planCount) * 100;
              realPercentage = `${this.roundToFixed(tmp, 2)}%`;
            }
            let roomData = [row[room].RECEIVECOUNT, planCount, realCount, peopleEfficiency, realPercentage, row[room].PRICESUM]
            rowData.push(...roomData)
          })
          return rowData
        })
      ]
      // 创建工作簿
      let ws = XLSX.utils.aoa_to_sheet(rowDatas);
      // 处理合并单元格
      mergedHeaderRow.forEach((col, colIndex) => {
        if (col !== '') {
          const startColumn = colIndex; // 起始列索引
          const endColumn = startColumn + 5; // 结束列索引
          const range = {s: {r: 0, c: startColumn}, e: {r: 0, c: endColumn}}; // 行数固定为1，列索引区间
          if (!ws['!merges']) {
            ws['!merges'] = [];
          }
          ws['!merges'].push(range);
        }
      });
      let workbook = {
        Sheets: {['测试统计']: ws},
        SheetNames: ['测试统计']
      };
      // 导出为Excel文件
      let excelBuffer = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
      let blob = new Blob([excelBuffer], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
      saveAs(blob, '测试统计' + this.formattedDate(new Date()) + '.xlsx');
    },
    formattedDate(date) {
      const year = date.getFullYear();
      const month = ("0" + (date.getMonth() + 1)).slice(-2);
      const day = ("0" + date.getDate()).slice(-2);
      const hours = ("0" + date.getHours()).slice(-2);
      const minutes = ("0" + date.getMinutes()).slice(-2);
      return `${year}${month}${day}${hours}${minutes}`;
    },

    roundToFixed(num, precision) {
      let factor = Math.pow(10, precision);
      let roundedNum = Math.round(num * factor) / factor;
      return roundedNum.toFixed(precision); // 确保结果具有指定位数的小数
    },
  }
}
</script>

<style lang="less" scoped>
@import '/src/components/pageTool/style/pbiSearchItem.less';

/deep/ .ant-table-thead > tr > th {
  padding: 5px !important;
  font-size: 13px !important;
  color: rgba(0, 0, 0, .85) !important;
  font-weight: 500 !important;
}

/deep/ .ant-table-tbody > tr > td {
  padding: 4px !important;
  font-size: 12px !important;
  color: #333 !important;
  font-weight: 400 !important;
}

/deep/ .ant-table-body {
  border: 1px solid #e8e8e8;
  overflow-y: auto; /* 垂直滚动条 */
  overflow-x: auto; /* 水平滚动条 */
}

/* /deep/ .ant-table-body::-webkit-scrollbar {
  height: 9px;
  width: 5px;
}

/deep/ .ant-table-body::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;

  background: #dddbdb;
}

/deep/ .ant-table-body::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: #f1f1f1;
} */

/deep/ .ant-table-pagination.ant-pagination {
  float: right;
  margin:5px 0 0;
  font-size: 12px;
}

/deep/ .ant-select-selection-selected-value {
  font-size: 12px;
}

/* 固定列 */
/deep/ .statisticsTable .ant-table-thead tr:nth-child(1) th:nth-child(1),
/deep/ .statisticsTable .ant-table-tbody tr td:nth-child(1) {
  position: sticky;
  left: 0;
  z-index: 11;
}
/* 固定列数据背景颜色 */
/deep/ .statisticsTable .ant-table-tbody tr td:nth-child(1) {
  background-color: #FFFFFF;
}

.flex-sb-center-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.operate-row {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

/deep/ .operate-row .ant-input-number {
  margin-top: 10px;
  width: 80px;
  font-size: 12px;
}

.label {
  margin-top: 10px;
  width: 120px;
  text-align: right;
  font-size: 12px;
  color: #333;
}

/deep/ .ant-input {
  font-size: 12px !important;
}
</style>