<template>
  <div style="padding: 10px">
    <pbiTabs :tabsList="laboratoryList" :activeKey="queryparam.testAddress" @clickTab="changeTab"></pbiTabs>
    <tableIndex
      :pageLevel='1'
      :tableTotal='tableTotal'
      :pageTitleShow=false
      :loading='loading'
      :otherHeight="parseInt(105)"
      @paginationChange="handlePageChange"
      @paginationSizeChange="handlePageChange"
    >
      <template #search>
        <pbiSearchContainer>
          <pbiSearchItem :span="4" label='检测室' v-if="queryparam.testAddress != '第四实验室' && queryparam.testAddress != '第六实验室(JM)'">
            <a-select v-model="queryparam.testAddress1" @change="getList(true)" style="width: 100%;" :allow-clear="true" dropdown-class-name="dropdownClassName">
              <a-select-option value="动力电池检测室">
                动力电池检测室
              </a-select-option>
              <a-select-option value="V圆柱检测室">
                V圆柱检测室
              </a-select-option>
              <a-select-option value="材料验证检测室">
                材料验证检测室
              </a-select-option>
            </a-select>
          </pbiSearchItem>
          <pbiSearchItem :span="4" label='温箱编号' >
            <a-input v-model="queryparam.code" @keyup.enter="getList(true)" @change="getList(true)"/>
          </pbiSearchItem>
          <pbiSearchItem :span="4" label='温箱型号' >
            <a-input v-model="queryparam.model" @keyup.enter="getList(true)" @change="getList(true)"/>
          </pbiSearchItem>
          <pbiSearchItem :span="4" label='温度'>
            <a-input-number v-model="queryparam.tem" @keyup.enter="getList(true)" style="width: 100%" @change="getList(true)"/>
          </pbiSearchItem>
          <pbiSearchItem :span="4" label='温箱类型' v-if='isShowAllSearch || queryparam.testAddress != null'>
            <a-select @focus="clickSelect" v-model="queryparam.type" @change="getList(true)" style="width: 100%" :allow-clear="true" dropdown-class-name="dropdownClassName">
              <a-select-option value="高低温箱">
                高低温箱
              </a-select-option>
              <a-select-option value="高温箱">
                高温箱
              </a-select-option>
              <a-select-option value="412常温存储室">
                412常温存储室
              </a-select-option>
              <a-select-option value="413常温存储室">
                413常温存储室
              </a-select-option>
              <a-select-option value="防爆型恒温恒湿箱">
                防爆型恒温恒湿箱
              </a-select-option>
              <a-select-option value="高低温湿热箱">
                高低温湿热箱
              </a-select-option>
              <a-select-option value="高温试验箱">
                高温试验箱
              </a-select-option>
            </a-select>
          </pbiSearchItem>
          <pbiSearchItem :span="4" label='电池类型' v-if='isShowAllSearch'>
            <a-select @focus="clickSelect" v-model="queryparam.batteryType" @change="getList(true)" style="width: 100%" :allow-clear="true" dropdown-class-name="dropdownClassName">
              <a-select-option value="G圆柱">
                G圆柱
              </a-select-option>
              <a-select-option value="C圆柱">
                C圆柱
              </a-select-option>
              <a-select-option value="V圆柱">
                V圆柱
              </a-select-option>
              <a-select-option value="方形">
                方形
              </a-select-option>
              <a-select-option value="软包">
                软包
              </a-select-option>
              <a-select-option value="G26">
                G26
              </a-select-option>
            </a-select>
          </pbiSearchItem>
          <pbiSearchItem :span="4" label='委托单号' v-if='isShowAllSearch'>
            <a-input v-model="queryparam.folderNo" @keyup.enter="getList(true)" @change="getList(true)"/>
          </pbiSearchItem>
          <pbiSearchItem :span="4" label='产品名称' v-if='isShowAllSearch'>
            <a-input v-model="queryparam.productName" @keyup.enter="getList(true)" @change="getList(true)"/>
          </pbiSearchItem>
          <pbiSearchItem :span="4" label='湿度' v-if='isShowAllSearch && queryparam.testAddress == "第四实验室"'>
            <a-input-number v-model="queryparam.humidity" @keyup.enter="getList(true)" style="width: 100%" @change="getList(true)"/>
          </pbiSearchItem>

          <pbiSearchItem :span="queryparam.testAddress == null ? (isShowAllSearch ? 16 : 8) :
           queryparam.testAddress == '第六实验室(JM)'?(isShowAllSearch ? 20 : 8):
           (isShowAllSearch ? 16 : 8)" type='btn' class="search-container">
            <div class="secondary-btn">
              <a-button @click="getList(true)" class="mr12" type="primary">
                查询
              </a-button>
            </div>
            <div class="secondary-btn">
              <a-button  class="mr12" v-if="(hasPerm('warmBox:fourUpdate') && queryparam.testAddress == '第四实验室') || (queryparam.testAddress == null && hasPerm('warmBox:sixUpdate')) || (hasPerm('warmBox:sixJMUpdate') && queryparam.testAddress == '第六实验室(JM)')" @click="openUpdate" type="primary">{{!updateFlag?'修改':'完成'}}</a-button>
            </div>
            <div class="secondary-btn">
              <a-button  class="mr12" @click="exportData" type="primary" >导出</a-button>
            </div>
            <div class="secondary-btn">
              <a-button  class="mr12" @click="downloadModel" type="primary" >下载模板</a-button>
            </div>
            <div class="secondary-btn">
              <a-upload
                :show-upload-list="false"
                :before-upload="handleImport"
                accept=".xlsx,.xls"
              >
                <a-button type="primary"  class="mr12" >
                  导入
                </a-button>
              </a-upload>
            </div>
            <div class="secondary-btn">
              <a-button  class="mr12" @click="batchDelete" type="danger" :disabled="selectedRows.length === 0">删除</a-button>
            </div>
            <div class="secondary-btn">
              <a-button  @click="reset" >重置</a-button>
            </div>

            <div class='toggle-btn'>
              <a-button size='small' type='link' @click='showOrHide'>
                {{ isShowAllSearch ? '收起' : '展开' }}
                <span v-if='isShowAllSearch'>
										<a-icon type='double-left'/>
									</span>
                <span v-else>
										<a-icon type='double-right'/>
									</span>
              </a-button>
            </div>

          </pbiSearchItem>

        </pbiSearchContainer>
      </template>


      <template #table>
        <ag-grid-vue :style="{height:tableHeight}"
                     class='table ag-theme-balham'
                     :tooltipShowDelay="0"
                     :columnDefs="'第四实验室' == queryparam.testAddress?safeColumns:'第六实验室(JM)' == queryparam.testAddress?jmColumns:columns"
                     :rowData='rowData'
                     :gridOptions="gridOptions"
                     @grid-ready="onGridReady"
                     :defaultColDef='defaultColDef'>
        </ag-grid-vue>
      </template>
    </tableIndex>
    <a-modal title="存放电芯" width="50%" :visible="visible3" :footer="null"
             @cancel="() => visible3 = false">
      <a-row :gutter="[4,4]">
        <a-col :span="6">
          <a-form-item label="产品名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="detailQueryparam.productName" @keyup.enter="getInOutDetailList" @change="getInOutDetailList"/>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="委托单号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="detailQueryparam.folderNo" @keyup.enter="getInOutDetailList" @change="getInOutDetailList"/>
          </a-form-item>
        </a-col>


      </a-row>
      <a-table :columns="'第四实验室' == queryparam.testAddress || '第六实验室(JM)' == queryparam.testAddress?safeLuoInOutDetail:luoInOutDetail" :data-source="batteryInOutData" :rowKey="(record) => record.id" bordered
      >

        <template slot="sampleCodeList" slot-scope="text, record, index, columns">
          <clamp :text="record.sampleCodeList.join(',')" :sourceText="record.sampleCodeList" :is-center="true"></clamp>
        </template>

      </a-table>

    </a-modal>

    <a-modal :title="'温箱型号:'+record.model+ '  温箱编号：'+ record.storeyCode" width="70%" :visible="luoVisible" :footer="null"
             @cancel="() => luoVisible = false">

      <a-row :gutter="[4,4]">
        <a-col :span="6">
          <a-form-item label="规格" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="luoQueryparam.luoType" @keyup.enter="getLuoList" @change="getLuoList"/>
          </a-form-item>
        </a-col>
        <a-button type="primary" style="float: right;width: 80px" @click="openAddLuo(record)">新增</a-button>
      </a-row>
      <a-table :columns="luoColumns" :data-source="luoData" :rowKey="(record) => record.id" bordered
      >
        <template slot="action" slot-scope="text, record, index, columns">
          <a-popconfirm
            title="确认删除，请谨慎操作"
            ok-text="确认"
            cancel-text="取消"
            @confirm="deleteLuo(record)"
          >
            <a v-if="record.luoType == null">删除</a>
          </a-popconfirm>
        </template>


      </a-table>




    </a-modal>

    <addLuo ref="addLuoForm" @ok="afterAddLuo" :record="record" />
  </div>



<!--  <div :style="divStyle">


    &lt;!&ndash;<div style="float: right;position: relative;z-index: 1;padding-bottom: 5px">


    </div>&ndash;&gt;

    <div class="box" ref="box">
      <a-tabs type="card" @change="getList(true)" v-model="queryparam.testAddress">
        <a-tab-pane :key="null" tab="第六实验室(HZ)" v-if="hasPerm('warmBox:six')">
        </a-tab-pane>

        <a-tab-pane key="第六实验室(JM)" tab="第六实验室(JM)" v-if="hasPerm('warmBox:sixJM')">
        </a-tab-pane>


        <a-tab-pane key="第四实验室" tab="第四实验室"  v-if="hasPerm('warmBox:four')">
        </a-tab-pane>

      </a-tabs>
    <div style="float: left;position: relative;z-index: 1;padding-bottom: 5px;width: 90%">
      <a-row :gutter="[4,4]">
        <a-col :span="3" v-show="queryparam.testAddress != '第四实验室' && queryparam.testAddress != '第六实验室(JM)'">
          <a-form-item label="检测室" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback >
            <a-select @focus="clickSelect" v-model="queryparam.testAddress1" @change="getList(true)" style="width: 100%;" :allow-clear="true" dropdown-class-name="dropdownClassName">
              <a-select-option value="动力电池检测室">
                动力电池检测室
              </a-select-option>
              <a-select-option value="V圆柱检测室">
                V圆柱检测室
              </a-select-option>
              <a-select-option value="材料验证检测室">
                材料验证检测室
              </a-select-option>

            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="3">
          <a-form-item label="温箱编号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryparam.code" @keyup.enter="getList(true)" @change="getList(true)"/>
          </a-form-item>
        </a-col>
        <a-col :span="3">
          <a-form-item label="温箱型号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryparam.model" @keyup.enter="getList(true)" @change="getList(true)"/>
          </a-form-item>
        </a-col>
        <a-col :span="3">
          <a-form-item label="温度" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input-number v-model="queryparam.tem" @keyup.enter="getList(true)" @change="getList(true)"/>
          </a-form-item>
        </a-col>
        <a-col :span="3" v-show="queryparam.testAddress == '第四实验室'">
          <a-form-item label="湿度"  :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input-number v-model="queryparam.humidity" @keyup.enter="getList(true)" @change="getList(true)"/>
          </a-form-item>
        </a-col>

        <a-col :span="3">
          <a-form-item label="温箱类型" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-select @focus="clickSelect" v-model="queryparam.type" @change="getList(true)" style="width: 100%" :allow-clear="true" dropdown-class-name="dropdownClassName">
              <a-select-option value="高低温箱">
                高低温箱
              </a-select-option>
              <a-select-option value="高温箱">
                高温箱
              </a-select-option>
              <a-select-option value="412常温存储室">
                412常温存储室
              </a-select-option>
              <a-select-option value="413常温存储室">
                413常温存储室
              </a-select-option>
              <a-select-option value="防爆型恒温恒湿箱">
                防爆型恒温恒湿箱
              </a-select-option>
              <a-select-option value="高低温湿热箱">
                高低温湿热箱
              </a-select-option>
              <a-select-option value="高温试验箱">
                高温试验箱
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="3" v-if="queryparam.testAddress != '第四实验室'">
          <a-form-item label="电池类型" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-select @focus="clickSelect" v-model="queryparam.batteryType" @change="getList(true)" style="width: 100%" :allow-clear="true" dropdown-class-name="dropdownClassName">
              <a-select-option value="G圆柱">
                G圆柱
              </a-select-option>
              <a-select-option value="C圆柱">
                C圆柱
              </a-select-option>
              <a-select-option value="V圆柱">
                V圆柱
              </a-select-option>
              <a-select-option value="方形">
                方形
              </a-select-option>
              <a-select-option value="软包">
                软包
              </a-select-option>
              <a-select-option value="G26">
                G26
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="3">
          <a-form-item label="委托单号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryparam.folderNo" @keyup.enter="getList(true)" @change="getList(true)"/>
          </a-form-item>
        </a-col>


        <a-col :span="3">
          <a-form-item label="产品名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryparam.productName" @keyup.enter="getList(true)" @change="getList(true)"/>
          </a-form-item>
        </a-col>
        &lt;!&ndash;<a-col :span="6"
               v-if="userInfo.account == 'superAdmin' || userInfo.roles.find(r => r.id == 1694647012972855298) != null">
          <a-form-item label="创建人" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryparam.createName" @keyup.enter="getList(true)" @change="getList(true)"/>
          </a-form-item>
        </a-col>&ndash;&gt;

      </a-row>
    </div>
    <div style="float: right;position: relative;z-index: 1;margin-top: 4px">

      <a-button style="margin-left: 8px;" @click="getList(true)" type="primary">刷新</a-button>
      <a-button style="margin-left: 8px;" v-if="hasPerm('warmBox:fourUpdate')" @click="updateFlag = !updateFlag" type="primary">{{!updateFlag?'修改':'完成'}}</a-button>
    </div>

      <div class="mid">
        <s-table :columns="'第四实验室' == queryparam.testAddress?safeColumns:'第六实验室(JM)' == queryparam.testAddress?jmColumns:columns" :data="loadData" bordered :scroll="{x:true}" :rowKey="(record) => record.id"
                 ref="table2">




          <template
            slot="totalPcsInNum"
            slot-scope="text, record, index, columns"
          >
            <a slot="totalPcsInNum" v-if="text > 0" @click="openDetail(record)">{{text}}</a>
            <span slot="totalPcsInNum" v-else>{{text}}</span>

          </template>

          <template
            slot="totalLuoNum"
            slot-scope="text, record, index, columns"
          >
            <a v-if="text > 0" @click="openLuoList(record)">{{text}}</a>

            <a v-else @click="$refs.addLuoForm.add(record)">{{text}}</a>


          </template>

          <template
            slot="luoInNum"
            slot-scope="text, record, index, columns"
          >
            <a v-if="text > 0" @click="openDetail(record)">{{text}}</a>

            <span v-else>{{text}}</span>


          </template>


        </s-table>

      </div>
    </div>



  </div>-->
</template>

<script>
import {
  testWarmBoxNewPage,
  testWarmBoxLuoAdd,
  testWarmBoxLuoList,
  getTestWarmBoxLuoInOutDetail,
  testWarmBoxLuoRemove,
  updateBox,
  warmBoxExport,
  warmBoxImport,
  updateSixBox,
  removeBox
} from '@/api/modular/system/warmBoxManager'
  import {mapGetters} from "vuex";

  import {clamp, STable} from '@/components'

  import addLuo from "./addLuo";
  import {tLimsFolderListPage} from "@/api/modular/system/limsManager";
  import pbiTabs from "@/components/pageTool/components/pbiTabs.vue";
  import {downloadMinioFile} from "@/utils/util";
  import {getMinioDownloadUrl} from "@/api/modular/system/fileManage";

  export default {
    components: {
      pbiTabs,
      clamp,
      STable,addLuo,
      totalLuoNum:{
        template:`<div>
                    <a v-if="params.value > 0" @click="params.openLuoList(params.data)">{{params.value}}</a>
                    <a v-else @click="params.openAddLuo(params.data)">{{params.value}}</a>
                  </div>`
      },
      luoInNum:{
        template:`
                  <div>
                    <a v-if="params.value > 0" @click="params.openDetail(params.data)">{{params.value}}</a>
                    <span v-else>{{params.value}}</span>
                  </div>
        `
      },
      totalPcsInNum:{
        template:`
                  <div>
                    <a v-if="params.value > 0" @click="params.openDetail(params.data)">{{params.value}}</a>
                    <span v-else>{{params.value}}</span>
                  </div>
        `
      },
      tem:{
        template:`<a-input-number class="updateNum" style="width:100%;height:100%;" v-if='params.data.updateFlag'
                      v-model='params.data[params.column.colId]'
                      @change='params.updateBasic(params.data, params.column.colId)'
                      ></a-input-number>
                  <span v-else>{{params.value}}</span>
                `
      },
      // 第六实验室(HZ)专用的可编辑温度字段
      hzTem:{
        template:`<a-input-number class="updateNum" style="width:100%;height:100%;"
                      v-if='params.data.updateFlag && (params.data.luoInNum == "0" || params.data.luoInNum == 0 || !params.data.luoInNum) && (params.data.totalPcsInNum == 0 || !params.data.totalPcsInNum)'
                      v-model='params.data[params.column.colId]'
                      @change='params.updateBasic(params.data, params.column.colId)'
                      ></a-input-number>
                  <span v-else>{{params.value}}</span>
                `
      },
      // 第六实验室(HZ)专用的可编辑电池类型字段
      hzBatteryType:{
        template:`<a-select style="width:100%;height:100%;"
                      v-if='params.data.updateFlag && (params.data.luoInNum == "0" || params.data.luoInNum == 0 || !params.data.luoInNum) && (params.data.totalPcsInNum == 0 || !params.data.totalPcsInNum)'
                      v-model='params.data[params.column.colId]'
                      @change='params.updateBasic(params.data, params.column.colId)'
                      >
                    <a-select-option value="G圆柱">G圆柱</a-select-option>
                    <a-select-option value="C圆柱">C圆柱</a-select-option>
                    <a-select-option value="V圆柱">V圆柱</a-select-option>
                    <a-select-option value="方形">方形</a-select-option>
                    <a-select-option value="软包">软包</a-select-option>
                    <a-select-option value="G26">G26</a-select-option>
                  </a-select>
                  <span v-else>{{params.value}}</span>
                `
      },
      // 第六实验室(HZ)专用的可编辑在测周期字段
      hzPeriodSymbolDesc:{
        template:`<a-select style="width:100%;height:100%;"
                      v-if='params.data.updateFlag && (params.data.luoInNum == "0" || params.data.luoInNum == 0 || !params.data.luoInNum) && (params.data.totalPcsInNum == 0 || !params.data.totalPcsInNum)'
                      v-model='params.data[params.column.colId]'
                      @change='params.updateBasic(params.data, params.column.colId)'
                      >
                    <a-select-option value="<60天">&lt;60天</a-select-option>
                    <a-select-option value="≥60天">≥60天</a-select-option>
                    <a-select-option value="<60天，≥60天">&lt;60天，≥60天</a-select-option>
                  </a-select>
                  <span v-else>{{params.value}}</span>
                `
      },
      // 第六实验室(JM)专用的可编辑温度字段
      jmTem:{
        template:`<a-input-number class="updateNum" style="width:100%;height:100%;"
                      v-if='params.data.updateFlag && (params.data.totalPcsInNum == 0 || !params.data.totalPcsInNum)'
                      v-model='params.data[params.column.colId]'
                      @change='params.updateBasic(params.data, params.column.colId)'
                      ></a-input-number>
                  <span v-else>{{params.value}}</span>
                `
      },
      // 第六实验室(JM)专用的可编辑电池类型字段
      jmBatteryType:{
        template:`<a-select style="width:100%;height:100%;"
                      v-if='params.data.updateFlag && (params.data.totalPcsInNum == 0 || !params.data.totalPcsInNum)'
                      v-model='params.data[params.column.colId]'
                      @change='params.updateBasic(params.data, params.column.colId)'
                      >
                    <a-select-option value="G圆柱">G圆柱</a-select-option>
                    <a-select-option value="C圆柱">C圆柱</a-select-option>
                    <a-select-option value="V圆柱">V圆柱</a-select-option>
                    <a-select-option value="方形">方形</a-select-option>
                    <a-select-option value="软包">软包</a-select-option>
                    <a-select-option value="G26">G26</a-select-option>
                  </a-select>
                  <span v-else>{{params.value}}</span>
                `
      }
    },
    computed: {
      ...mapGetters(['userInfo', 'testTaskFilterData']),
    },
    data() {
      return {
        isShowAllSearch:false,
        laboratoryList:[
          {value:null,label:"第六实验室(HZ)",show: this.hasPerm('warmBox:six')},
          {value:"第六实验室(JM)",label:"第六实验室(JM)",show: this.hasPerm('warmBox:sixJM')},
          {value:"第四实验室",label:"第四实验室",show: this.hasPerm('warmBox:four')},
        ],
        tableTotal:0,
        rowData:[],
        loading:false,
        selectedRows: [], // 选中的行数据
        pageNo: 1,
        pageSize: 20,
        gridApi: null,
        columnApi: null,
        gridOptions: {
          onSelectionChanged: this.onSelectionChanged,
          suppressCellSelection: false,
          rowSelection: 'multiple',
          suppressRowClickSelection: true,
          isRowSelectable: function(rowNode) {
            // 只有当已存放电池数为0时才可以选择
            return rowNode.data.totalPcsInNum === 0 || rowNode.data.totalPcsInNum === '0';
          }
        },
        defaultColDef: {
          filter: false,
          floatingFilter: false,
          editable: false,
        },
        tableHeight: document.body.clientHeight - 205 +'px' ,
        divStyle:{
          background_color: '#FFFFFF',padding: '10px',zoom: window.screen.width > 1500?1:0.8
        },
        updateFlag:false,
        batteryInOutData:[],
        luoData:[],

        selectedRowKeys: [],
        selectedRows: [],
        beforeDeleteFlag: false,
        id: null,
        visible3:false,
        batteryInOutColumns: [
          {
            title: '序号',
            align: 'center',
            dataIndex: 'index',
            width: 50,
            customRender: (text, record, index) => `${index + 1}`
          },{
            title: '存储阶段',
            align: 'center',
            dataIndex: 'progressDetailOrderNumber',
            width: 50,
            customRender: (text, record, index) => `${text -1 }`
          },{
            title: '进箱时间',
            align: 'center',
            dataIndex: 'inDate',
            width: 80,
          },{
            title: '出箱时间',
            align: 'center',
            dataIndex: 'actOutDate',
            width: 80,
          },{
            title: '存放天数',
            align: 'center',
            dataIndex: 'day',
            width: 50,
          },{
            title: '产品名称',
            align: 'center',
            dataIndex: 'productName',
            width: 50,
          },{
            title: '样品编号',
            align: 'center',
            dataIndex: 'sampleCode',
            width: 100
          }


        ],
        luoColumns: [
          {
            title: '序号',
            align: 'center',
            dataIndex: 'index',
            width: 50,
            customRender: (text, record, index) => `${index + 1}`
          },{
            title: '规格',
            align: 'center',
            dataIndex: 'luoType',
            width: 100,
          },{
            title: '木质支架个数',
            align: 'center',
            dataIndex: 'mjNum',
            width: 80,
          },{
            title: '吸塑盒个数',
            align: 'center',
            dataIndex: 'xshNum',
            width: 80,
          },{
            title: '托盘数',
            align: 'center',
            dataIndex: 'tpNum',
            width: 50,
          },{
            title: '方形/软包数',
            align: 'center',
            dataIndex: 'fxRbNum',
            width: 50,
          },{
            title: '操作',
            align: 'center',
            dataIndex: 'action',
            width: 40,
            scopedSlots: {
              customRender: "action"
            }
          }


        ],
        luoInOutDetail: [
          {
            title: '规格',
            align: 'center',
            dataIndex: 'luoType',
            width: 100,
            customRender: (value, row, index) => {
              const obj = {
                children: value,
                attrs: {},
              };
              if (row.isFirst) {
                obj.attrs.rowSpan = row.rowSpan;
              } else {
                obj.attrs.rowSpan = 0;
              }
              return obj;
            }
          },{
            title: '存储阶段',
            align: 'center',
            dataIndex: 'progressDetailOrderNumber',
            width: 50,
            customRender: (text, record, index) => `${text -1 }`
          },{
            title: '进箱时间',
            align: 'center',
            dataIndex: 'inDate',
            width: 80,
          },{
            title: '出箱时间',
            align: 'center',
            dataIndex: 'outDate',
            width: 80,
          },{
            title: '存放天数',
            align: 'center',
            dataIndex: 'day',
            width: 50,
          },{
            title: '产品名称',
            align: 'center',
            dataIndex: 'productName',
            width: 50,
          },{
            title: '样品编号',
            align: 'center',
            dataIndex: 'sampleCodeList',
            width: 100,
            scopedSlots: {
              customRender: "sampleCodeList"
            }
          },{
            title: '是否预留',
            align: 'center',
            dataIndex: 'isNeedReserve',
            width: 50,
            customRender: (text, record, index) => {
              if(text == '1'){
                return '是'
              }else{
                return '否'
              }
            }
          }


        ],
        safeLuoInOutDetail: [
         {
            title: '存储阶段',
            align: 'center',
            dataIndex: 'progressDetailOrderNumber',
            width: 50,
            customRender: (text, record, index) => `${text -1 }`
          },{
            title: '进箱时间',
            align: 'center',
            dataIndex: 'inDate',
            width: 80,
          },{
            title: '出箱时间',
            align: 'center',
            dataIndex: 'outDate',
            width: 80,
          },{
            title: '存放天数',
            align: 'center',
            dataIndex: 'day',
            width: 50,
          },{
            title: '产品名称',
            align: 'center',
            dataIndex: 'productName',
            width: 50,
          },{
            title: '样品编号',
            align: 'center',
            dataIndex: 'sampleCodeList',
            width: 150,
            scopedSlots: {
              customRender: "sampleCodeList"
            }
          }


        ],
        columns: [
          {
            headerName: '',
            field: 'checkbox',
            width: 40,
            checkboxSelection: true,
            suppressMenu: true,
            suppressSorting: true,
            suppressFilter: true,
            pinned: 'left'
          },
          {
            headerName: '序号',
            field: 'index',
            width: 50,
            cellRenderer: function (params) {
              return parseInt(params.node.id) + 1
            }
          },
          {
            headerName: '温箱硬件基础信息',
            children: [
              {
                headerName: '检测室',
                field: 'testAddress',
                minWidth: 120,
                flex:1,
              },  {
                headerName: '温箱类型',
                field: 'type',
                width: 120,
              }, {
                headerName: '设备编号',
                width: 110,
                field: 'deviceCode',
              }, {
                headerName: '温箱编号',
                width: 110,
                field: 'code',
              }, {
                headerName: '温箱型号',
                width: 120,
                field: 'model',
              }, {
                headerName: '温箱容积/L',
                width: 100,
                field: 'volume',
              }, {
                headerName: '温箱样品层架号',
                width: 120,
                field: 'storeyCode',
              }
            ]

          },

          {
            headerName: '温箱测试信息',
            children: [
              {
                headerName: '电池类型',
                width: 100,
                field: 'batteryType',
                cellRenderer: 'hzBatteryType',
                cellRendererParams: {updateBasic: this.updateSixBox},
              }, {
                headerName: '温箱温度/℃',
                width: 120,
                field: 'tem',
                cellRenderer: 'hzTem',
                cellRendererParams: {updateBasic: this.updateSixBox},
              }, {
                headerName: '在测周期',
                width: 120,
                field: 'periodSymbolDesc',
                cellRenderer: 'hzPeriodSymbolDesc',
                cellRendererParams: {updateBasic: this.updateSixBox},
              },
            ]
          },
          {
            headerName: '现状',
            children: [
              {
                headerName: '可放摞数',
                width: 90,
                field: 'totalLuoNum',
                cellRenderer: 'totalLuoNum',
                cellRendererParams: {openLuoList: this.openLuoList,openAddLuo:this.openAddLuo},
              },  {
                headerName: '已放摞数',
                width: 90,
                field: 'luoInNum',
                cellRenderer: 'luoInNum',
                cellRendererParams: {openDetail: this.openDetail},
              }, {
                headerName: '已存放电池数',
                width: 120,
                field: 'totalPcsInNum',
                cellRenderer: 'totalPcsInNum',
                cellRendererParams: {openDetail: this.openDetail},
              }
            ]
          },

        ],
        safeColumns: [
          {
            headerName: '',
            field: 'checkbox',
            width: 50,
            checkboxSelection: true,
            suppressMenu: true,
            suppressSorting: true,
            suppressFilter: true,
            pinned: 'left'
          },
          {
            headerName: '序号',
            field: 'index',
            width: 50,
            cellRenderer: function (params) {
              return parseInt(params.node.id) + 1
            }
          },
          {
            headerName: '温箱硬件基础信息',
            children: [
              {
                headerName: '检测室',
                field: 'testAddress',
                width: 120
              },  {
                headerName: '温箱类型',
                field: 'type',
                width: 120
              }, {
                headerName: '设备编号',
                width: 110,
                field: 'deviceCode'
              }, {
                headerName: '温箱编号',
                width: 110,
                field: 'code'
              }, {
                headerName: '温箱型号',
                width: 120,
                field: 'model'
              }, {
                headerName: '温箱容积/L',
                width: 100,
                field: 'volume'
              }, {
                headerName: '温箱样品层架号',
                width: 120,
                field: 'storeyCode'
              }
            ]

          },

          {
            headerName: '温箱测试信息',
            children: [
              {
                headerName: '温箱温度/℃',
                width: 110,
                field: 'tem',
                cellRenderer: 'tem',
                cellRendererParams: {updateBasic: this.updateBasic},
              }, {
                headerName: '温箱湿度/%RH',
                width: 110,
                field: 'humidity',
                cellRenderer: 'tem',
                cellRendererParams: {updateBasic: this.updateBasic},
              }
            ]
          },
          {
            headerName: '现状',
            children: [
             {
                headerName: '已存放电池数',
                width: 110,
                field: 'totalPcsInNum',
               cellRenderer: 'totalPcsInNum',
               cellRendererParams: {openDetail: this.openDetail},
              }
            ]
          },

        ],
        jmColumns: [
          {
            headerName: '',
            field: 'checkbox',
            width: 50,
            checkboxSelection: true,
            suppressMenu: true,
            suppressSorting: true,
            suppressFilter: true,
            pinned: 'left'
          },
          {
            headerName: '序号',
            field: 'index',
            width: 50,
            cellRenderer: function (params) {
              return parseInt(params.node.id) + 1
            }
          },
          {
            headerName: '温箱硬件基础信息',
            children: [
              {
                headerName: '检测室',
                field: 'testAddress',
                width: 120,
              },  {
                headerName: '温箱类型',
                field: 'type',
                width: 120
              }, {
                headerName: '设备编号',
                width: 110,
                field: 'deviceCode'
              }, {
                headerName: '温箱编号',
                width: 110,
                field: 'code'
              }, {
                headerName: '温箱型号',
                width: 120,
                field: 'model'
              }, {
                headerName: '温箱容积/L',
                width: 100,
                field: 'volume'
              }, {
                headerName: '温箱样品层架号',
                width: 120,
                field: 'storeyCode',
              }
            ]

          },

          {
            headerName: '温箱测试信息',
            children: [
              {
                headerName: '电池类型',
                width: 100,
                field: 'batteryType',
                cellRenderer: 'jmBatteryType',
                cellRendererParams: {updateBasic: this.updateBasic},
              },
              {
                headerName: '温箱温度/℃',
                width: 120,
                field: 'tem',
                cellRenderer: 'jmTem',
                cellRendererParams: {updateBasic: this.updateBasic},
              }

            ]
          },
          {
            headerName: '现状',
            children: [
             {
                headerName: '已存放电池数',
                width: 120,
                field: 'totalPcsInNum',
               cellRenderer: 'totalPcsInNum',
               cellRendererParams: {openDetail: this.openDetail},
             },
              {
                headerName: '可放总数',
                width: 90,
                field: 'totalPcsNum'
              }
            ]
          },

        ],
        record: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 10
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 14
          }
        },
        visible1: false,
        confirmLoading1: false,
        showExport: true,
        queryparam: {testAddress:null},
        detailQueryparam: {},
        luoQueryparam:{},
        luoVisible:false,
        /*headers: {
          //Authorization: 'Bearer ' + Vue.ls.get(ACCESS_TOKEN),
          Authorization: 'Bearer eyJhbGciOiJIUzUxMiJ9.***********************************************************************************************************************************************************************************************************************.LgAC7GwkaFwV5NJD7wrr4gQGYzFMfbJL46hei2EclvHWVE1WGgV8ucyJ5XkuF5Cio_24ZuIqwAyVXyD02snFmQ',
        },*/
      }
    },

    mounted() {


      this.loadData()

    },
    methods: {
      downloadModel(){
        getMinioDownloadUrl("1940296649232666626").then(res1 => {
          downloadMinioFile(res1.data)
        }).finally(() => {

        })
      },
      openUpdate(){
        this.updateFlag = !this.updateFlag
        this.rowData.forEach(r => r.updateFlag = this.updateFlag)
        // 刷新数据
        this.gridApi.setRowData(this.rowData);
      },
      showOrHide(){
        this.isShowAllSearch = !this.isShowAllSearch
        this.tableHeight =  document.body.clientHeight - 205 - (this.isShowAllSearch?40:0) +'px'
      },
      onGridReady(params) {
        this.gridApi = params.api;
        this.columnApi = params.columnApi;
        // params.api.sizeColumnsToFit();
      },
      onSelectionChanged() {
        if (this.gridApi) {
          this.selectedRows = this.gridApi.getSelectedRows();
        }
      },
      loadData() {
        this.loading = true
        if(this.hasPerm('warmBox:four') && !this.hasPerm('warmBox:six') && !this.queryparam.testAddress){
          this.queryparam.testAddress = "第四实验室"
        }else if(this.hasPerm('warmBox:sixJM') && !this.hasPerm('warmBox:six')&& !this.queryparam.testAddress){
          this.queryparam.testAddress = "第六实验室(JM)"
        }
        const param = Object.assign({},this.queryparam)
        if(this.queryparam.testAddress == null && this.queryparam.testAddress1 != null){
          param.testAddress = this.queryparam.testAddress1
        }

        return testWarmBoxNewPage({
          ...{
            pageNo: this.pageNo,
            pageSize: this.pageSize
          }, ...param
        }).then((res) => {
          if (res.success) {
            this.rowData = res.data.rows
            this.tableTotal = res.data.totalRows

          }
        }).finally(() => {
          if(this.rowData.length == 0 && this.pageNo > 1){
            // this.pageNo -= 1
            this.pageNo = Math.max(1, Math.ceil(this.tableTotal / this.pageSize))
            this.loadData()
          }
          this.loading = false
        })
      },
      handlePageChange(value) {
        let {current, pageSize} = value
        this.pageNo = current
        this.pageSize = pageSize
        this.loadData()

      },
      clickSelect(){

        this.$nextTick(() => {
          if(window.screen.width > 1500){
            return
          }
          setTimeout(() => {
            // 获取所有 ant-select-dropdown 元素
            const dropdownElements = document.querySelectorAll('.dropdownClassName');
            // 遍历每个元素并调整 left 和 top
            dropdownElements.forEach((element) => {
              // 获取原始的 left 和 top 值
              const originalLeft = parseFloat(element.style.left || 0);
              const originalTop = parseFloat(element.style.top || 0);

              // 计算新的 left 和 top 值
              const newLeft = originalLeft * 0.8;
              const newTop = originalTop * 0.8;

              // 设置新的 left 和 top
              element.style.left = `${newLeft}px`;
              element.style.top = `${newTop}px`;
            });
          }, 100)
        });




      },

      updateBasic(record,column,$event){
        let param = {}
        param.id = record.id
        param[column] = record[column]
        updateBox(param).then(res => {
          if(res.data == true){
            this.$message.success('更新成功')
          }
        })
      },

      updateSixBox(record,column,$event){
        let param = {}
        param.id = record.id
        param[column] = record[column]
        updateSixBox(param).then(res => {
          if(res.data == true){
            this.$message.success('更新成功')
          }
        })
      },

      deleteLuo(record){
        testWarmBoxLuoRemove(record).then(res => {

        }).then(res => {
          this.getLuoList()
        })
      },

      openAddLuo(record){
        this.$refs.addLuoForm.add(record)
      },
      afterAddLuo(){
        if(this.record.id != null){
          this.getLuoList()
        }
        this.getList(true)
      },

      openDetail(record){
        this.record = record
        getTestWarmBoxLuoInOutDetail({storeyId:record.id}).then(res => {
          this.batteryInOutData = res.data
          this.visible3 = true
        })
      },

      openLuoList(record){
        this.record = record
        testWarmBoxLuoList({storeyId:record.id}).then(res => {
          this.luoData = res.data
          this.luoVisible = true
        })
      },

      getInOutDetailList(){

        this.detailQueryparam.storeyId = this.record.id

        getTestWarmBoxLuoInOutDetail(this.detailQueryparam).then(res => {
          this.batteryInOutData = res.data
          this.visible3 = true
        })
      },

      getLuoList(){

        this.luoQueryparam.storeyId = this.record.id

        testWarmBoxLuoList(this.luoQueryparam).then(res => {
          this.luoData = res.data

        })
      },
      changeTab(value){
        this.queryparam.testAddress = value
        this.getList(true)
      },
      getList(flag) {
        if (flag) {
          this.id = null
        }
        this.loadData()
      },
      reset() {
        this.queryparam =  {}
        this.getList(true)
      },
      exportData() {
        this.$message.loading('正在导出，请稍候...', 0)
        warmBoxExport(this.queryparam).then(res => {
          this.$message.destroy()
          const blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url
          const now = new Date()
          const timestamp = now.getFullYear() +
            String(now.getMonth() + 1).padStart(2, '0') +
            String(now.getDate()).padStart(2, '0') + '_' +
            String(now.getHours()).padStart(2, '0') +
            String(now.getMinutes()).padStart(2, '0') +
            String(now.getSeconds()).padStart(2, '0')
          link.download = `温箱数据_${timestamp}.xlsx`
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)
          this.$message.success('导出成功')
        }).catch(err => {
          this.$message.destroy()
          this.$message.error('导出失败：' + (err.message || '未知错误'))
        })
      },
      handleImport(file) {
        // 验证文件类型
        const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                       file.type === 'application/vnd.ms-excel' ||
                       file.name.endsWith('.xlsx') ||
                       file.name.endsWith('.xls')

        if (!isExcel) {
          this.$message.error('只能上传Excel文件！')
          return false
        }

        // 验证文件大小（限制为10MB）
        const isLt10M = file.size / 1024 / 1024 < 10
        if (!isLt10M) {
          this.$message.error('文件大小不能超过10MB！')
          return false
        }

        this.$message.loading('正在导入，请稍候...', 0)

        warmBoxImport(file).then(res => {
          this.$message.destroy()
          if(res.success){
            this.$message.success('导入成功')
          }else{
            this.$message.error('导入失败：' +res.message)
          }
          // 刷新数据
          this.getList(true)
        }).catch(err => {
          this.$message.destroy()
          this.$message.error('导入失败：' + (err.response?.data?.message || err.message || '未知错误'))
        })

        // 阻止默认上传行为
        return false
      },

      // 批量删除方法
      batchDelete() {
        if (this.selectedRows.length === 0) {
          this.$message.warning('请选择要删除的温箱');
          return;
        }

        // 检查选中的行是否都满足删除条件（已存放电池数为0）
        const invalidRows = this.selectedRows.filter(row =>
          row.totalPcsInNum !== 0 && row.totalPcsInNum !== '0'
        );

        if (invalidRows.length > 0) {
          this.$message.error('只能删除已存放电池数为0的温箱');
          return;
        }

        this.$confirm({
          title: '确认删除',
          content: `确定要删除选中的 ${this.selectedRows.length} 个温箱吗？`,
          okText: '确认',
          cancelText: '取消',
          onOk: () => {
            this.performBatchDelete();
          }
        });
      },

      // 执行批量删除
      async performBatchDelete() {
        this.loading = true;
        let successCount = 0;
        let failCount = 0;

        try {
          // 循环调用 removeBox API
          for (const row of this.selectedRows) {
            try {
              const response = await removeBox({ id: row.id });
              if (response.success) {
                successCount++;
              } else {
                failCount++;
                console.error(`删除温箱 ${row.code} 失败:`, response.message);
              }
            } catch (error) {
              failCount++;
              console.error(`删除温箱 ${row.code} 出错:`, error);
            }
          }

          // 显示删除结果
          if (successCount > 0 && failCount === 0) {
            this.$message.success(`成功删除 ${successCount} 个温箱`);
          } else if (successCount > 0 && failCount > 0) {
            this.$message.warning(`成功删除 ${successCount} 个温箱，${failCount} 个删除失败`);
          } else {
            this.$message.error('删除失败');
          }

          // 清空选中状态
          this.selectedRows = [];
          if (this.gridApi) {
            this.gridApi.deselectAll();
          }

          // 刷新列表
          this.getList(false);

        } catch (error) {
          this.$message.error('批量删除过程中发生错误');
          console.error('批量删除错误:', error);
        } finally {
          this.loading = false;
        }
      }

    }
  }
</script>
<style lang="less" scoped>
@import '/src/components/pageTool/style/pbiSearchItem.less';
  .tips {
    color: #1890ff;
  }


  /deep/.updateNum .ant-input-number-input {
    text-align: center !important;
  }



</style>
<style lang='less'>
  .dropdownClassName{
    .ant-select-dropdown-menu-item {
      padding: 5px;
      font-size: 12px;
    }
  }
</style>