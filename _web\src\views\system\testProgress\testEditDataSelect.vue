<template>
  <a-modal title="修改数据录入项目" :width="900" centered
           :bodyStyle="{padding:0}"
           :visible="visible" :confirmLoading="confirmLoading"  style="padding: 0"
           :maskClosable="false"
           @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-tabs v-model="activeKey" @change="changeTab">
        <a-tab-pane key="basic" tab="基础信息">
          <a-form :form="form">
            <a-form-item style="display: none;" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['id']" />
            </a-form-item>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="测试状态" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>

                  <a-select  v-decorator="['testStatus', {rules: [{ message: '请选择测试状态！'}]}]" disabled
                             style="width: 100%">

                    <a-select-option value="Done">
                      Done
                    </a-select-option>
                    <a-select-option value="Ongoing">
                      Ongoing
                    </a-select-option>
                    <a-select-option value="Plan">
                      Plan
                    </a-select-option>
                    <a-select-option value="Stop">
                      Stop
                    </a-select-option>

                  </a-select>

                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="测试申请单" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input placeholder="请输入测试申请单" disabled
                           v-decorator="['testCode', {rules: [{ message: '请输入测试申请单！'}]}]"/>
                </a-form-item>
              </a-col>

            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="产品名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入产品名称" disabled
                           v-decorator="['productName', {rules: [{ message: '请输入产品名称！'}]}]"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="产品样品阶段" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入产品样品阶段" disabled
                           v-decorator="['productSampleStage', {rules: [{ message: '请输入产品样品阶段！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="测试类型" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-select v-decorator="['testType', {rules: [{ message: '请输入测试类型！'}]}]"  disabled
                            style="width: 100%">

                    <a-select-option value="研发测试">
                      研发测试
                    </a-select-option>
                    <a-select-option value="产品验证测试">
                      产品验证测试
                    </a-select-option>
                    <a-select-option value="产品鉴定测试">
                      产品鉴定测试
                    </a-select-option>

                  </a-select>

                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="申请部门" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入申请部门" disabled
                           v-decorator="['dept', {rules: [{ message: '请输入申请部门！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="申请人" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入申请人" disabled
                           v-decorator="['applicant', {rules: [{ message: '请输入申请人！'}]}]"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="测试项目" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入测试项目" disabled
                           v-decorator="['testProject', {rules: [{ message: '请输入测试项目！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="T/℃" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入T/℃" disabled
                           v-decorator="['t', {rules: [{ message: '请输入T/℃！'}]}]"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="SOC" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入soc" disabled
                           v-decorator="['soc', {rules: [{ message: '请输入soc！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="测试周期" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入存储天数" :min="1"  :precision="0" disabled
                                  v-decorator="['testPeriod', {rules: [{ message: '请输入存储天数！'}]}]"/>

                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="数量" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入数量" :min="1"  :precision="0" disabled
                           v-decorator="['quantity', {rules: [{ message: '请输入数量！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>


            <a-row :gutter="24">
              <a-col :md="12" :sm="24">

                <a-form-item label="测试技师" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入测试技师" disabled
                           v-decorator="['testMan', {rules: [{ message: '请输入测试技师！'}]}]"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="测试地点" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-select v-decorator="['testAddress', {rules: [{ message: '请输入测试地点！'}]}]" disabled
                            style="width: 100%">

                    <a-select-option value="A1_3F">
                      V圆柱检测室
                    </a-select-option>

                    <a-select-option value="R2_2F">
                      材料验证检测室
                    </a-select-option>

                    <a-select-option value="R4_4F">
                      动力电池检测室
                    </a-select-option>

                  </a-select>

                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">


                <a-form-item label="电芯载体" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">

                  <a-select v-decorator="['sampleType', {rules: [{ message: '请输入电芯载体！'}]}]" disabled
                            style="width: 100%">

                    <a-select-option value="G圆柱">
                      G圆柱
                    </a-select-option>
                    <a-select-option value="C圆柱">
                      C圆柱
                    </a-select-option>
                    <a-select-option value="V圆柱">
                      V圆柱
                    </a-select-option>
                    <a-select-option value="方形">
                      方形
                    </a-select-option>
                    <a-select-option value="软包_396389">
                      软包_396389
                    </a-select-option>
                    <a-select-option value="软包_动力">
                      软包_动力
                    </a-select-option>
                    <a-select-option value="模组">
                      模组
                    </a-select-option>

                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">


                <a-form-item label="存储位置" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">

                  <a-input disabled
                           v-decorator="['saveAddress']"/>

                </a-form-item>
              </a-col>

            </a-row>


            <!-- <a-row :gutter="24">

               <a-col :md="12" :sm="24">
                 <a-form-item label="已完成天数" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                   <a-input placeholder="请输入已完成天数"
                            v-decorator="['finishDay', {rules: [{ message: '请输入已完成天数！'}]}]"/>
                 </a-form-item>
               </a-col>
             </a-row>-->
          </a-form>
        </a-tab-pane>
        <a-tab-pane key="test" tab="中检信息">
          <a-form :form="form">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">

                <a-form-item label="中检次数" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input  placeholder="请输入中检次数" :min="1"  :precision="0" disabled
                            v-decorator="['testNum']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">

                <a-form-item label="刷新后续计划日期" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback disabled>
                  <a-checkbox v-model="refreshDateFlag" ></a-checkbox>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
          <a-table :columns="constColumns" :data-source="record.data" bordered
                   style="padding:0 20px 20px 20px "
                    class="test-table"
                   :loading="tableLoading"
                   bordered
                   :pagination="false"
                   :rowKey="(record1) => record1.id"
          >

<!--            <div slot="rechargeTitle">-->
<!--              <span>补电</span>-->
<!--              <a-checkbox @change="checkRecharge($event,'recharge')">-->
<!--              </a-checkbox>-->
<!--            </div>-->
<!--            <div slot="smallinspectionTitle">-->
<!--              <span>小中检</span>-->
<!--              <a-checkbox @change="checkRecharge($event,'smallinspection')">-->
<!--              </a-checkbox>-->
<!--            </div>-->
<!--            <div slot="largeinspectionTitle">-->
<!--              <span>大中检</span>-->
<!--              <a-checkbox @change="checkRecharge($event,'largeinspection')">-->
<!--              </a-checkbox>-->
<!--            </div>-->
<!--            <div slot="innerresTitle">-->
<!--              <span>内阻</span>-->
<!--              <a-checkbox @change="checkRecharge($event,'innerres')">-->
<!--              </a-checkbox>-->
<!--            </div>-->
<!--            <div slot="voltageTitle">-->
<!--              <span>电压</span>-->
<!--              <a-checkbox @change="checkRecharge($event,'voltage')">-->
<!--              </a-checkbox>-->
<!--            </div>-->
<!--            <div slot="heightTitle">-->
<!--              <span>尺寸</span>-->
<!--              <a-checkbox @change="checkRecharge($event,'height')">-->
<!--              </a-checkbox>-->
<!--            </div>-->
<!--            <div slot="volumeTitle">-->
<!--              <span>产气量</span>-->
<!--              <a-checkbox @change="checkRecharge($event,'volume')">-->
<!--              </a-checkbox>-->
<!--            </div>-->
<!--            <div slot="weightTitle">-->
<!--              <span>重量</span>-->
<!--              <a-checkbox @change="checkRecharge($event,'weight')">-->
<!--              </a-checkbox>-->
<!--            </div>-->
<!--            <div slot="isolateresTitle">-->
<!--              <span>绝缘阻值</span>-->
<!--              <a-checkbox @change="checkRecharge($event,'isolateres')">-->
<!--              </a-checkbox>-->
<!--            </div>-->

            <template  slot="checkBox" slot-scope="text, inRecord, index,column">

              <a-checkbox :checked="inRecord[column.dataIndex] == 1"  @change="changeSelectData($event,inRecord,column)">
              </a-checkbox>
            </template>

            <template slot="footer">
              <a @click="addData" id="footerId">
                <a-icon type="plus" style="width: 15px;height: 15px;margin-left: 15px;cursor: pointer"/>
              </a>
            </template>

            <template slot="deleteStage" slot-scope="text, record, index, column">
              <a @click="deleteData(record.id)"  style="text-align: center" >删除</a>
            </template>

            <template  slot="actualInDate" slot-scope="text, inRecord" >
              <a-date-picker v-if="record.stageFlag !== 1 && inRecord.orderNumber !== 1" style="width:100%;" v-model="inRecord.actualInDate" placeholder="" :allow-clear="false" @change="handleDateChange('in', inRecord)"/>
              <a-date-picker v-else-if="record.stageFlag === 1 && inRecord.orderNumber === 2" style="width:100%;" v-model="inRecord.actualInDate" placeholder="" :allow-clear="false" @change="handleDateChange('in', inRecord)"/>
            </template>
            <template  slot="actualOutDate" slot-scope="text, inRecord">
              <a-date-picker v-if="record.stageFlag !== 1 && inRecord.orderNumber !== 1" style="width:100%;" v-model="inRecord.actualOutDate" placeholder="" :allow-clear="false" @change="handleDateChange('out', inRecord)"/>
              <a-date-picker v-else-if="record.stageFlag === 1 && inRecord.orderNumber !== 1" style="width:100%;" v-model="inRecord.actualOutDate" placeholder="" :allow-clear="false" @change="handleDateChange('out', inRecord)"/>
            </template>

            <template  slot="day" slot-scope="text, record">
              <a-input style="text-align: center" :value="text" @blur="changeStoreDay($event, record, 'day')"/>
            </template>

          </a-table>

        </a-tab-pane>
        <a-tab-pane key="sizeInfo" tab="尺寸信息">
          <a-button type="primary" style="margin: 0px 0px 10px 10px" @click="initSizeInfo()">
            初始化尺寸信息
          </a-button>
          <a-button type="primary" style="margin-left: 10px" @click="deleteSizeInfo()">
            删除
          </a-button>
          <a-table
            :columns="sizeColumns"
            :pagination="false"
            :loading="sizeLoading"
            :data-source="sizeData"
            :rowKey="record => record.id"
            :row-selection="{ selectedRowKeys: sizeSelectedRowKeys, selectedRows: sizeSelectedRows, onChange: sizeOnChange, columnWidth:30 }"
            bordered>
            <template  slot="measureTime" slot-scope="text, record1">
              <a-input :disabled="record.sampleType === '软包' || record.sampleType === '模组'" style="text-align: center" v-model="record1.measureTime" type="number"/>
            </template>
            <template slot="calRule" slot-scope="text, record1, index">
              <a-select v-model="record1.calRule" style="width: 250px;"
                        :disabled="record.sampleType === '软包' || record.sampleType === '模组'"
                        :dropdown-style="{
                          fontSize: '14px',
                        }">
                <a-select-option value="去除最大值和最小值，计算平均值">
                  去除最大值和最小值，计算平均值
                </a-select-option>
                <a-select-option value="无需计算">
                  无需计算
                </a-select-option>
                <a-select-option value="取最大值&最小值">
                  取最大值&最小值
                </a-select-option>
                <a-select-option value="取最大值">
                  取最大值
                </a-select-option>
              </a-select>
            </template>
          </a-table>
        </a-tab-pane>
      </a-tabs>


    </a-spin>

    <template slot="footer">

      <a-button key="back" @click="handleCancel">
        关闭
      </a-button>
      <a-button v-if="activeKey === 'sizeInfo'" type="primary" @click="updateTestProgressSizeByType">
        确定
      </a-button>

<!--      <a-popconfirm placement="topRight" ok-text="提交" cancel-text="取消" @confirm="handleSubmit">-->
<!--        <template slot="title">-->
<!--          <p>确定提交吗</p>-->
<!--        </template>-->
<!--        <a-button key="submit" type="primary" v-if="activeKey != 'remark'">-->
<!--          确定-->
<!--        </a-button>-->
<!--      </a-popconfirm>-->

    </template>

  </a-modal>
</template>

<script>
import {
  alterMiddleCheckInfo, deleteTestProgressSize,
  getAllTestProgressDetail,
  getTestProgressSizeById,
  initTestProgressSize,
  syncCalendarLifeTestDate,
  testProgressUpdateDataSelect,
  testProgressUpdateOnlyBean,
  updateTestProgressSize
} from '@/api/modular/system/testProgressManager'
  import moment from "moment";

  export default {
    props: {
      type: {
        type: String,
        default: ''
      },
    },
    data() {
      return {
        // selectedRowKeys: [],
        selectedRows: [],
        sizeLoading: false,
        sizeSelectedRowKeys: [],
        sizeSelectedRows: [],
        sizeData: [],
        sizeColumns: [
          {
            title: '序号',
            width: 30,
            align: 'center',
            dataIndex: 'orderNo'
          }, {
            title: '尺寸类型',
            width: 80,
            align: 'center',
            dataIndex: 'sizeType'
          }, {
            title: '测量次数',
            width: 80,
            align: 'center',
            dataIndex: 'measureTime',
            scopedSlots: {
              customRender: 'measureTime'
            }
          }, {
            title: '计算规则',
            width: 60,
            align: 'center',
            dataIndex: 'calRule',
            scopedSlots: {
              customRender: 'calRule'
            }
          }
        ],
        constColumns: [],
        columns: [
          {
            title: '存储阶段',
            dataIndex: 'index',
            align: 'center',
            width: 60,
            customRender: (text, record, index) => {

              if(this.record.data[0].day == 0){
                if(index == 0){
                  return "初始阶段"
                }else{
                  return `存储第${index}阶段`
                }
              }else{
                return `存储第${index+1}阶段`
              }

            }
          }, {
            title: '存储天数',
            width: 60,
            align: 'center',
            dataIndex: 'day',
            scopedSlots: {
              customRender: 'day'
            },
          }, {
            title: '总存储天数',
            width: 60,
            align: 'center',
            dataIndex: 'totalDay',
            scopedSlots: {
              customRender: 'totalDay'
            },
          }, {
            title: '计划开始时间',
            width: 60,
            align: 'center',
            dataIndex: 'inDate'
          }, {
            title: '结束时间',
            width: 60,
            align: 'center',
            dataIndex: 'outDate'
          },{
            title: '实际进箱时间',
            width: 70,
            align: 'center',
            dataIndex: 'actualInDate',
            scopedSlots: {
              customRender: 'actualInDate'
            }
          },{
            title: '实际出箱时间',
            width: 70,
            align: 'center',
            dataIndex: 'actualOutDate',
            scopedSlots: {
              customRender: 'actualOutDate'
            }
          },{
            title: '补电',
            width: 30,
            align: 'center',
            dataIndex: 'recharge',
            slots: { title: "rechargeTitle" },
            scopedSlots: {
              customRender: 'checkBox'
            }
          },{
            title: '小中检',
            width: 30,
            align: 'center',
            dataIndex: 'smallinspection',
            slots: { title: "smallinspectionTitle" },
            scopedSlots: {
              customRender: 'checkBox'
            }
          },{
            title: '大中检',
            width: 30,
            align: 'center',
            dataIndex: 'largeinspection',
            slots: { title: "largeinspectionTitle" },
            scopedSlots: {
              customRender: 'checkBox'
            }
          },{
            title: '内阻',
            width: 30,
            align: 'center',
            dataIndex: 'innerres',
            slots: { title: "innerresTitle" },
            scopedSlots: {
              customRender: 'checkBox'
            }
          },{
            title: '电压',
            width: 30,
            align: 'center',
            dataIndex: 'voltage',
            slots: { title: "voltageTitle" },
            scopedSlots: {
              customRender: 'checkBox'
            }
          },{
            title: '尺寸',
            width: 30,
            align: 'center',
            dataIndex: 'height',
            slots: { title: "heightTitle" },
            scopedSlots: {
              customRender: 'checkBox'
            }
          },{
            title: '产气量',
            width: 30,
            align: 'center',
            dataIndex: 'volume',
            slots: { title: "volumeTitle" },
            scopedSlots: {
              customRender: 'checkBox'
            }
          },{
            title: '重量',
            width: 30,
            align: 'center',
            dataIndex: 'weight',
            slots: { title: "weightTitle" },
            scopedSlots: {
              customRender: 'checkBox'
            }
          },{
            title: '绝缘阻值',
            width: 30,
            align: 'center',
            dataIndex: 'isolateres',
            slots: { title: "isolateresTitle" },
            scopedSlots: {
              customRender: 'checkBox'
            }
          },{
            title: '照片',
            width: 30,
            align: 'center',
            dataIndex: 'picture',
            scopedSlots: {
              customRender: 'checkBox'
            }
          },{
            title: '过程视频',
            width: 30,
            align: 'center',
            dataIndex: 'video',
            scopedSlots: {
              customRender: 'checkBox'
            }
          },{
            title: "操作",
            width: 60,
            align: 'center',
            dataIndex: "",
            scopedSlots: {customRender: 'deleteStage'},
          },
        ],
        activeKey: 'test',
        startDate: null,
        labelCol: {
          xs: {
            span: 12
          },
          sm: {
            span: 8
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 14
          }
        },
        refreshDateFlag: false,
        visible: false,
        confirmLoading: false,
        tableLoading: false,
        form: this.$form.createForm(this),
        record:{}
      }
    },

    methods: {
      moment,
      updateTestProgressSizeByType () {
        this.sizeLoading = true
        updateTestProgressSize(this.sizeData).then((res) => {
          if (res.success) {
            this.$message.success("修改尺寸信息成功")
            setTimeout(() => {
              this.sizeLoading = false
              this.handleCancel()
            }, 300)
          } else {
            this.sizeLoading = false
            this.$message.error('修改尺寸信息失败：' + res.message)
          }
          this.clearSizeSelectedRow()
        })
      },
      sizeOnChange(selectedRowKeys, selectedRows) {
        this.sizeSelectedRowKeys = selectedRowKeys
        this.sizeSelectedRows = selectedRows
      },
      clearSizeSelectedRow() {
        this.sizeSelectedRowKeys = []
        this.sizeSelectedRows = []
      },
      deleteSizeInfo() {
        if (this.sizeSelectedRows.length === 0) {
          this.$message.warning('请至少选择一条数据')
          return
        }
        if (this.record.sampleType === '软包' || this.record.sampleType === '模组') {
          this.$message.warning('该尺寸类型不能删除')
          return
        }
        this.sizeLoading = true
        deleteTestProgressSize(this.sizeSelectedRows).then((res) => {
          if (res.success) {
            this.getSizeInfoListByProgressId(this.record.id);
            this.$message.success("删除尺寸信息成功")
          } else {
            this.$message.error('删除尺寸信息失败：' + res.message)
          }
        }).finally(() => {
          setTimeout(() => {
            this.clearSizeSelectedRow()
            this.sizeLoading = false
          }, 300)
        })
      },
      initSizeInfo() {
        initTestProgressSize(this.record.id).then((res) => {
          this.sizeLoading = true
          if (res.success) {
            this.getSizeInfoListByProgressId(this.record.id);
          } else {
            this.$message.error('初始化尺寸信息失败：' + res.message)
          }
        }).finally(() => {
          setTimeout(() => {
            this.clearSizeSelectedRow()
            this.sizeLoading = false
          }, 300)
        })
      },
      addData() {
        if (this.record.data.length === 0) {
          this.$message.warning("请先到【编辑】按钮新增2个及以上阶段")
          return
        }
        this.tableLoading = true
        let param = {}
        param["id"] = this.record.data[this.record.data.length - 1].id
        alterMiddleCheckInfo(param,'add').then(res => {
          if (res.success) {
            this.record.data = res.data
            this.form.setFieldsValue({
              testNum: this.record.data.length
            })
            setTimeout(() => {
              this.$message.success("新增成功")
            }, 500)
          } else {
            setTimeout(() => {
              this.$message.error("错误提示：" + res.message)
            }, 500)
          }
        }).finally(() => {
          setTimeout(() => {
            this.tableLoading = false
          }, 500)
        })
      },
      deleteData(id) {
        this.tableLoading = true
        let param = {}
        param["id"] = id
        alterMiddleCheckInfo(param,'delete').then(res => {
          if (res.success) {
            this.record.data = res.data
            this.form.setFieldsValue({
              testNum: this.record.data.length
            })
            setTimeout(() => {
              this.$message.success("删除成功")
            }, 500)
          } else {
            setTimeout(() => {
              this.$message.error("错误提示：" + res.message)
            }, 500)
          }
        }).finally(() => {
          setTimeout(() => {
            this.tableLoading = false
          }, 500)
        })
      },
      handleDateChange(type, inRecord) {
        if (type === 'in') {
          inRecord.actualInDate = moment(inRecord.actualInDate).format('YYYY-MM-DD')
        } else {
          inRecord.actualOutDate = moment(inRecord.actualOutDate).format('YYYY-MM-DD')
        }
        // console.log('type',type)
        // console.log('this.refreshDateFlag',this.refreshDateFlag)
        // console.log('inRecord',inRecord)
        syncCalendarLifeTestDate(this.refreshDateFlag ? 1 : 0, type, inRecord).then((res) => {
          if (res.success) {
            this.refreshAllStageInfo(inRecord.progressId)
            this.$message.success("修改成功")
          } else {
            this.$message.error("错误提示：" + res.message)
          }
        })
      },
      changeStoreDay(value, record, column) {
        this.tableLoading = true
        let param = {}
        param[column] = value.target.value
        param["id"] = record.id
        if (!param[column]) {
          this.$message.warning("存储天数不能为空！")
          this.tableLoading = false
          return
        }
        alterMiddleCheckInfo(param,'day').then(res => {
          if (res.success) {
            record.day = value.target.value
            // console.log('this.record.data',this.record.data)
            // console.log('res.data',res.data)
            this.record.data = res.data
            setTimeout(() => {
              this.$message.success("修改成功")
            }, 500)
          } else {
            setTimeout(() => {
              this.$message.error("错误提示：" + res.message)
            }, 500)
          }
        }).finally(() => {
          setTimeout(() => {
            this.tableLoading = false
          }, 500)
        })
      },
      // checkRecharge(event,dataIndex) {
      //   if (event.target.checked) {
      //     this.record.data.forEach(item => {
      //       if (this.selectedRowKeys.includes(item.id)) {
      //         item[dataIndex] = "1"
      //       }
      //     });
      //   } else {
      //     this.record.data.forEach(item => {
      //       if (this.selectedRowKeys.includes(item.id)) {
      //         item[dataIndex] = "0"
      //       }
      //     });
      //   }
      // },
      // onSelectChange (selectedRowKeys, selectedRows) {
      //   this.selectedRowKeys = selectedRowKeys
      //   this.selectedRows = selectedRows
      // },
      changeSelectData(data,inRecord,column){
        if(data.target.checked){
          inRecord[column.dataIndex] = 1
          if (column.dataIndex === 'video' && inRecord.orderNumber === 1) {
            inRecord[column.dataIndex] = 0
            this.$message.warning('初始性能检测不能勾选过程视频')
            return
          }
        }else{
          if (column.dataIndex === 'innerres' || column.dataIndex === 'voltage') {
            this.$message.warning('内阻/电压必填！')
            return
          }
          inRecord[column.dataIndex] = 0
        }
        this.tableLoading = true
        let param = {}
        param["id"] = inRecord.id
        param[column.dataIndex] = inRecord[column.dataIndex]
        alterMiddleCheckInfo(param,'middleCheckInfo').then(res => {
          if (res.success) {
            this.record.data = res.data
            setTimeout(() => {
              this.$message.success("修改成功")
            }, 500)
          } else {
            setTimeout(() => {
              this.$message.error("错误提示：" + res.message)
            }, 500)
          }
        }).finally(() => {
          setTimeout(() => {
            this.tableLoading = false
          }, 500)
        })

      },

      changeTab(key){
        this.$nextTick(() => {

          if(key == 'basic'){

            if(this.form.getFieldValue('id') == null){
              this.form.setFieldsValue({
                id:this.record.id,
                testStatus:this.record.testStatus,
                testCode:this.record.testCode,
                productName:this.record.productName,
                productSampleStage:this.record.productSampleStage,
                testType:this.record.testType,
                dept:this.record.dept,
                applicant:this.record.applicant,
                testProject:this.record.testProject,
                t:this.record.t,
                soc:this.record.soc,
                testPeriod:this.record.testPeriod,
                quantity:this.record.quantity,
                testMan:this.record.testMan,
                testAddress:this.record.testAddress,
                sampleType:this.record.sampleType,
                saveAddress:this.record.saveAddress,
              })
            }

          } else if (key == 'sizeInfo') {
            this.getSizeInfoListByProgressId(this.record.id);
          } else {
            if(this.form.getFieldValue('zero') == null
              || this.form.getFieldValue('zero') == undefined){
              this.form.setFieldsValue({
                testNum : this.record.data.length
              })
            }
          }

        })


      },
      // changeDay(value, record, column) {
      //   record.day = value.target.value
      //   this.changeInOutDate(record.orderNumber - 1);
      // },
      // changeInOutDate(index) {
      //   if (index == 0) {
      //     if (this.form.getFieldValue('zero') != null) {
      //       this.record[0].inDate = moment(this.form.getFieldValue('zero')).format('YYYY-MM-DD')
      //
      //       if (this.record[0].day != null && this.record[0].inDate != null) {
      //         this.record[0].outDate = moment(this.record[0].inDate).add(this.record[0].day, 'days').format('YYYY-MM-DD')
      //       }
      //     }
      //     index += 1;
      //   }
      //   for (let i = index; i < this.record.length; i++) {
      //     let before = this.record[i - 1]
      //     if (before.outDate != null) {
      //       if(index == 1 && before.day == 0){
      //         this.record[i].inDate = before.outDate
      //       }else{
      //         this.record[i].inDate = moment(before.outDate).add(3, 'days').format('YYYY-MM-DD')
      //       }
      //       if (this.record[i].inDate != null && this.record[i].day != null) {
      //         this.record[i].outDate = moment(this.record[i].inDate).add(this.record[i].day, 'days').format('YYYY-MM-DD')
      //       }
      //     }
      //   }
      // },
      add() {
        this.visible = true
      },
      refreshAllStageInfo (testProgressId) {
        getAllTestProgressDetail({ id: testProgressId }).then((res) => {
          if (res.success) {
            this.record.data = res.data
          } else {
            this.$message.error('获取所有存储阶段信息失败!')
          }
        })
      },
      edit(record) {
        this.record = record
        this.refreshAllStageInfo(record.id)
        if (this.record.testAddress === 'R3') {
          this.constColumns = this.columns.filter(column => column.dataIndex !== 'isolateres' && column.dataIndex !== 'volume')
        } else {
          this.constColumns = this.columns.filter(column => column.dataIndex !== 'picture' && column.dataIndex !== 'video')
        }
        this.visible = true
        setTimeout(() => {

          if(this.activeKey == 'basic'){
            this.form.setFieldsValue({
              id:this.record.id,
              testStatus:this.record.testStatus,
              testCode:this.record.testCode,
              productName:this.record.productName,
              productSampleStage:this.record.productSampleStage,
              testType:this.record.testType,
              dept:this.record.dept,
              applicant:this.record.applicant,
              testProject:this.record.testProject,
              t:this.record.t,
              soc:this.record.soc,
              testPeriod:this.record.testPeriod,
              quantity:this.record.quantity,
              testMan:this.record.testMan,
              testAddress:this.record.testAddress,
              sampleType:this.record.sampleType,
            })
          } else if (this.activeKey == 'sizeInfo') {
            this.getSizeInfoListByProgressId(this.record.id);
          } else {
            this.form.setFieldsValue({
              testNum : this.record.data.length
            })
          }
        }, 100)

      },
      getSizeInfoListByProgressId (progressId) {
        getTestProgressSizeById(progressId).then((res) => {
          if (res.success) {
            this.sizeData = res.data
          } else {
            this.$message.error('获取尺寸信息失败：' + res.message)
          }
        })
      },
      onChangeSampleDate(date, dateString) {
        if (date == null) {
          this.startDate = ''
        } else {
          this.startDate = moment(date).format('YYYY-MM-DD')
        }
      },
      handleSubmit() {
        this.confirmLoading = true
        testProgressUpdateDataSelect(this.record).then((res) => {
          this.confirmLoading = false
          if (res.success) {
            this.$message.success('修改成功')
            this.handleCancel()
            this.$emit('ok', values)
          } else {
            this.$message.error('修改失败：' + res.message)
          }
        }).finally((res) => {
          this.confirmLoading = false
        })


      },
      handleOnlyBeanSubmit() {


          this.confirmLoading = true
            let param = {}
            param.id = this.record.id
            param.remark = this.record.remark
            testProgressUpdateOnlyBean(param).then((res) => {
              this.confirmLoading = false
              if (res.success) {
                this.$message.success('修改成功')
                this.handleCancel()
                this.$emit('ok', values)
              } else {
                this.$message.error('修改失败：' + res.message)
              }
            }).finally((res) => {
              this.confirmLoading = false
            })

      },
      handleCancel() {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
<style lang="less" scoped>
  .ant-form-item {

    margin-bottom: 0px;

  }

  /deep/.ant-modal-body {
    padding: 0!important;
  }


  /deep/ .ant-table-thead > tr > th, /deep/ .ant-table-tbody > tr > td {
    padding: 0px;
  }

  /deep/ .ant-table-footer {

    padding: 0px;
  }

  /deep/ .ant-table-pagination.ant-pagination {
    margin: 5px 0;
  }
  /deep/.ant-input-number {
    width: 100%;
  }

  /deep/.ant-input-number-sm>.ant-input-number-input-wrap>.ant-input-number-input{
    text-align: center;
  }

  /deep/.ant-select-selection--single {
    border: 0;
  }

  /deep/ .ant-input {
    border: 0;
  }
  /deep/ input {
    border: 0;
  }
  /deep/.anticon svg {
    display: none;
  }
  /deep/#footerId i svg {
    display: unset!important;
  }

  /deep/.ant-select-disabled .ant-select-selection{
    background: #f5f5f500!important;
    color: rgba(0, 0, 0, 0.99);
    cursor: default;
  }
  /deep/.ant-input[disabled] {
    color: rgba(0, 0, 0, 0.99);
    background-color: unset;
    cursor: default;

  }
  /deep/.ant-calendar-picker-input{
    text-align: center;
  }

  /deep/.ant-input {

    height: 25px;
    font-size: 12px;
  }

  .green{
    background-color: #58a55c;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 25px;
  }

  /deep/.ant-calendar-picker-input.ant-input {
    color: black;
  }

  /deep/.green>div>input{
    background-color: #58a55c;
  }

  /deep/.test-table .ant-table-thead {
    position: sticky;
    top: 0;
    z-index: 1;
  }

  /deep/ .test-table .ant-table-body {
    overflow: auto;
    max-height: 300px;
  }

  

</style>
