import { axios } from '@/utils/request'

export function getStaffPage(parameter) {
    return axios({
        url: '/sysStaff/page',
        method: 'get',
        params: parameter
    })
}
export function getMessageFromJira(parameter) {
    return axios({
        url: '/sysStaff/getMessageFromJira',
        method: 'post',
        data: parameter
    })
}
export function getProductFromJira(parameter) {
    return axios({
        url: '/sysStaff/getProductFromJira',
        method: 'post',
        data: parameter
    })
}
export function getTestReportFromJira(parameter) {
    return axios({
        url: '/sysStaff/getTestReportFromJira',
        method: 'post',
        data: parameter
    })
}
export function getAchievementManageFromJira(parameter) {
    return axios({
        url: '/sysStaff/getAchievementManageFromJira',
        method: 'post',
        data: parameter
    })
}
export function getTravelReportFromJira(parameter) {
    return axios({
        url: '/sysStaff/getTravelReportFromJira',
        method: 'post',
        data: parameter
    })
}
export function getStaffList(parameter) {
    return axios({
        url: '/sysStaff/list',
        method: 'get',
        params: parameter
    })
}
export function previewFromJIRA(parameter) {
    return axios({
        url: '/previewFromJIRA',
        method: 'get',
        params: parameter,
        responseType:'blob'
    })
}
export function fileType(parameter) {
    return axios({
        url: '/fileType',
        method: 'get',
        params: parameter
    })
}
export function getStaffHoursPage(parameter) {
    return axios({
        url: '/sysStaffHours/page',
        method: 'get',
        params: parameter
    })
}

export function staffEdit(parameter) {
    return axios({
        url: '/sysStaff/edit',
        method: 'post',
        data: parameter
    })
}

export function education(parameter) {
    return axios({
        url: '/sysStaff/education',
        method: 'post',
        data: parameter
    })
}

export function age(parameter) {
    return axios({
        url: '/sysStaff/age',
        method: 'post',
        data: parameter
    })
}

export function numAnalyze(parameter) {
    return axios({
        url: '/sysStaff/numAnalyze',
        method: 'post',
        data: parameter
    })
}

export function numMonthAnalyze(parameter) {
    return axios({
        url: '/sysStaff/numMonthAnalyze',
        method: 'post',
        data: parameter
    })
}

export function rank(parameter) {
    return axios({
        url: '/sysStaff/rank',
        method: 'post',
        data: parameter
    })
}
export function orgEdu(parameter) {
    return axios({
        url: '/sysStaff/orgEdu',
        method: 'post',
        data: parameter
    })
}

export function resignNum(parameter) {
    return axios({
        url: '/sysStaff/resignNum',
        method: 'post',
        data: parameter
    })
}
export function orgNumAge(parameter) {
    return axios({
        url: '/sysStaff/orgNumAge',
        method: 'post',
        data: parameter
    })
}
export function resignRate(parameter) {
    return axios({
        url: '/sysStaff/resignRate',
        method: 'post',
        data: parameter
    })
}
export function quitWorkYears(parameter) {
    return axios({
        url: '/sysStaff/quitWorkYears',
        method: 'post',
        data: parameter
    })
}
export function orgNum(parameter) {
    return axios({
        url: '/sysStaff/orgNum',
        method: 'post',
        data: parameter
    })
}

export function ageAnalysis(parameter) {
    return axios({
        url: '/sysStaff/ageAnalysis',
        method: 'post',
        data: parameter
    })
}

export function peopleNum(parameter) {
    return axios({
        url: '/sysStaff/peopleNum',
        method: 'post',
        data: parameter
    })
}

export function joinYears(parameter) {
    return axios({
        url: '/sysStaff/joinYears',
        method: 'post',
        data: parameter
    })
}

export function totalWorkYear(parameter) {
    return axios({
        url: '/sysStaff/totalWorkYear',
        method: 'post',
        data: parameter
    })
}

export function queryNumByRegion(parameter) {
    return axios({
        url: '/sysStaff/queryNumByRegion',
        method: 'post',
        data: parameter
    })
}


export function hoursChart(parameter) {
    return axios({
        url: '/sysStaffHours/hoursChart',
        method: 'post',
        data: parameter
    })
}

export function hoursChartRanking(parameter) {
    return axios({
        url: '/sysStaffHours/hoursChartRanking',
        method: 'post',
      params: parameter
    })
}

export function hoursChart1(parameter) {
    return axios({
        url: '/sysStaffHours/hoursChart1',
        method: 'post',
        data: parameter
    })
}


export function area(parameter) {
    return axios({
        url: '/sysStaff/area',
        method: 'post',
        data: parameter
    })
}


export function numberMul (arg1, arg2) {
  if(arg1 == undefined){
    return ''
  }
  var m = 0;
  var s1 = arg1.toString();
  var s2 = arg2.toString();
  try {
    m += s1.split(".")[1].length;
  } catch (e) { }
  try {
    m += s2.split(".")[1].length;
  } catch (e) { }
  return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m);
}



export function staffAdd(parameter) {
    return axios({
        url: '/sysStaff/add',
        method: 'post',
        data: parameter
    })
}

export function staffDel(parameter) {
    return axios({
        url: '/sysStaff/delete',
        method: 'post',
        data: parameter
    })
}

export function staffImport (parameter) {
    return axios({
      url: '/sysStaff/import',
      method: 'post',
      data: parameter
    })
  }

export function staffHoursImport (parameter) {
    return axios({
      url: '/sysStaffHours/import',
      method: 'post',
      data: parameter
    })
  }


  export function staffExport () {
    return axios.get('/sysStaff/download',{responseType: 'blob'})
  }
  export function staffHoursExport () {
    return axios.get('/sysStaffHours/download',{responseType: 'blob'})
  }