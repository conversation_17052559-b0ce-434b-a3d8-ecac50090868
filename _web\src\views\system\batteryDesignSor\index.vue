<template>
	<div style="background:#fff;overflow: hidden;height: 100%;">
		<!-- 面包屑 strat -->
		<a-breadcrumb separator=">" v-if="checkPage != 1" style="padding-left: 15px">
			<a-breadcrumb-item>
				<router-link :to="{ path: '/batterydesign', query: { type: breadcrumb.type } }">
					<a-icon type="rollback" class="rollback-icon" />
          {{ breadcrumb.type === 'product' ? '项目产品' : '技术平台' }}设计
				</router-link>
			</a-breadcrumb-item>
			<!-- <a-breadcrumb-item>
				{{ structureType1 }}
			</a-breadcrumb-item> -->
			<a-breadcrumb-item>
				{{ breadcrumb.batteryName + " " + stage + " " + breadcrumb.version + " SOR管理" }}</a-breadcrumb-item
			>
		</a-breadcrumb>
		<!-- 面包屑 end -->

		<div class="tab-title" style="height:40px;width:100%">
			<div class="tab-head">
				<div class="active" style="float: left">SOR管理</div>
				<div class="sub-title" style="float: right">
					<div class="sub-title" style="float: right">
						<div>
							<a-spin :spinning="spinning">
								<span @click="exportDataMethod1()" style="cursor:pointer"
									><svg
										xmlns="http://www.w3.org/2000/svg"
										class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 jZPaJQ svg-icon-path-icon fill"
										viewBox="0 0 48 48"
										width="18"
										height="18"
									>
										<defs data-reactroot=""></defs>
										<g>
											<path
												d="M5 8C5 6.89543 5.89543 6 7 6H19L24 12H41C42.1046 12 43 12.8954 43 14V40C43 41.1046 42.1046 42 41 42H7C5.89543 42 5 41.1046 5 40V8Z"
												fill="none"
												stroke="rgb(84, 152, 255)"
												stroke-width="4"
												stroke-linejoin="round"
											></path>
											<path
												d="M30 28L23.9933 34L18 28.0134"
												stroke="rgb(84, 152, 255)"
												stroke-width="4"
												stroke-linecap="round"
												stroke-linejoin="round"
											></path>
											<path
												d="M24 20V34"
												stroke="rgb(84, 152, 255)"
												stroke-width="4"
												stroke-linecap="round"
												stroke-linejoin="round"
											></path>
										</g></svg></span>
								<a class="tip" @click="exportDataMethod1()" style="cursor:pointer">
									SOR管理导出
								</a>
							</a-spin>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div>
			<span class="tip" style="float:left;padding-right: 20%;padding-left: 10px">★ 数据库关联必填项</span>
			<svg width="20" height="20">
				<circle cx="10" cy="11" r="8" fill="#58a55c" />
			</svg>
			<span style="vertical-align: top;padding-right:10%;padding-left: 5%">满足</span>
			<svg width="20" height="20">
				<circle cx="10" cy="11" r="8" fill="#fac858" />
			</svg>
			<span style="vertical-align: top;padding-right:10%;padding-left: 5%">待确定</span>
			<svg width="20" height="20">
				<circle cx="10" cy="11" r="8" fill="#f33" />
			</svg>
			<span style="vertical-align: top;padding-left: 5%">不满足</span>
		</div>

		<div class="wrapper">
			<div style="height: 25px;display: inline-flex">
				<span style="background: #0d84ff;width: 5px;height: 100%;display: inline-flex"></span
				><span
					style="font-size: 14px;font-weight: bold;padding-left: 15px;color: black;display: flex;justify-content: center;align-items: center;"
					>开发信息<a-icon
						:type="develop ? 'down' : 'up'"
						@click="changeDevelopOpen('develop')"
						style="margin-left: 5px;"
				/></span>
			</div>
			<a-table
				id="develop"
				:columns="columns"
				:data-source="dataSource1"
				:row-key="record => record.id"
				:pagination="false"
				:scroll="{ y: windowHeight }"
				bordered
			>
				<div slot="eveSor" slot-scope="text, record">
					<input :value="text" @change="updateData($event, record, 'eveSor')" />
				</div>

				<div slot="projectName" slot-scope="text, record">
					<input :value="text" v-if="record.code == null" @change="updateData($event, record, 'projectName')" />
					<span v-else>{{ text }}</span>
				</div>
				<div slot="unit" slot-scope="text, record">
					<input :value="text" v-if="record.code == null" @change="updateData($event, record, 'unit')" />
					<span v-else>{{ text }}</span>
				</div>
				<div slot="customerSor" slot-scope="text, record">
					<input :value="text" @change="updateData($event, record, 'customerSor')" />
				</div>
				<template slot="checkStatus" slot-scope="text, record">
					<div name="checkStatus" @click="isOwn == 1 && design.manCheckStatus == 0 && canBeUpdate? edit(record.id) : null">
						<a-select
							v-if="record.editable"
							dropdown-class-name="dropdownClassName"
							style="width: 100%;outline:none;font-size: 2px;"
							@blur="getList(false)"
							:autoFocus="true"
							:open="true"
							:showArrow="false"
							:default-value="text"
							@change="updateSelectData($event, record)"
						>
							<a-select-option :value="parseInt(1)" @click="getList(false)">
								满足
							</a-select-option>
							<a-select-option :value="parseInt(2)" @click="getList(false)">
								不满足
							</a-select-option>
							<a-select-option :value="parseInt(3)" @click="getList(false)">
								TBD
							</a-select-option>
							<a-select-option :value="parseInt(0)" @click="getList(false)">
								/
							</a-select-option>
						</a-select>

						<span v-else>
							<svg width="20" height="20" v-if="text == 1">
								<circle cx="10" cy="11" r="8" fill="#58a55c" />
							</svg>
							<svg width="20" height="20" v-else-if="text == 2">
								<circle cx="10" cy="11" r="8" fill="#f33" />
							</svg>
							<svg width="20" height="20" v-else-if="text == 3">
								<circle cx="10" cy="11" r="8" fill="#fac858" />
							</svg>
							<span v-else>/</span>
						</span>
					</div>
				</template>
				<div slot="remark" slot-scope="text, record">
					<input :value="text" @change="updateData($event, record, 'remark')" />
				</div>

				<div slot="action" slot-scope="text, record">
					<a-popconfirm placement="topRight" title="确认删除？" @confirm="() => designDelete(record.id)">
						<a class="btn a-btn" v-if="record.code == null  && canBeUpdate">删除</a>
					</a-popconfirm>
				</div>

				<div class="divcls" slot="divcls" slot-scope="text">{{ text }}</div>
				<div class="divcls div_border_right" slot="unitcls" slot-scope="text">{{ text }}</div>
				<!--<a-icon type="plus" slot="footer" :style="{paddingLeft: bigClient?'2.1%':'1.85%'}"
                @click="addSor('develop','开发信息')"/>-->
			</a-table>
		</div>

		<div class="wrapper">
			<div style="height: 25px;display: inline-flex">
				<span style="background: #0d84ff;width: 5px;height: 100%;display: inline-flex"></span
				><span
					style="font-size: 14px;font-weight: bold;padding-left: 15px;color: black;display: flex;justify-content: center;align-items: center;"
					>基础性能<a-icon :type="basic ? 'down' : 'up'" @click="changeDevelopOpen('basic')" style="margin-left: 5px;"
				/></span>
				<!--<a class="btn"><a-icon class="icon success1" type="check" /><span class="txt">满足</span></a>
        <a class="btn"><a-icon class="icon warn1" type="exclamation" /><span class="txt">待确定</span></a>
        <a class="btn"><a-icon class="icon fail1" type="close" /><span class="txt">不满足</span></a>-->
			</div>
			<a-table
				id="basic"
				:columns="columns"
				:data-source="dataSourceBasic"
				:row-key="record => record.id"
				:pagination="false"
				:scroll="{ y: windowHeight }"
				bordered
			>
				<div slot="eveSor" slot-scope="text, record">
					<input :value="text" @change="updateData($event, record, 'eveSor')" />
				</div>

				<div slot="projectName" slot-scope="text, record">
					<input :value="text" v-if="record.code == null" @change="updateData($event, record, 'projectName')" />
					<span v-else>{{ text }}</span>
				</div>
				<div slot="unit" slot-scope="text, record">
					<input :value="text" v-if="record.code == null" @change="updateData($event, record, 'unit')" />
					<span v-else>{{ text }}</span>
				</div>
				<div slot="customerSor" slot-scope="text, record">
					<input :value="text" @change="updateData($event, record, 'customerSor')" />
				</div>
				<template slot="checkStatus" slot-scope="text, record">
					<div name="checkStatus" @click="isOwn == 1 && design.manCheckStatus == 0  && canBeUpdate? edit1(record.id) : null">
						<a-select
							v-if="record.editable"
							dropdown-class-name="dropdownClassName"
							style="width: 100%;outline:none;font-size: 2px;"
							@blur="getList(false)"
							:autoFocus="true"
							:open="true"
							:showArrow="false"
							:default-value="text"
							@change="updateSelectData($event, record)"
						>
							<a-select-option :value="parseInt(1)" @click="getList(false)">
								满足
							</a-select-option>
							<a-select-option :value="parseInt(2)" @click="getList(false)">
								不满足
							</a-select-option>
							<a-select-option :value="parseInt(3)" @click="getList(false)">
								TBD
							</a-select-option>
							<a-select-option :value="parseInt(0)" @click="getList(false)">
								/
							</a-select-option>
						</a-select>

						<span v-else>
							<svg width="20" height="20" v-if="text == 1">
								<circle cx="10" cy="11" r="8" fill="#58a55c" />
							</svg>
							<svg width="20" height="20" v-else-if="text == 2">
								<circle cx="10" cy="11" r="8" fill="#f33" />
							</svg>
							<svg width="20" height="20" v-else-if="text == 3">
								<circle cx="10" cy="11" r="8" fill="#fac858" />
							</svg>
							<span v-else>/</span>
							<span v-else>/</span>
						</span>
					</div>
				</template>
				<div slot="remark" slot-scope="text, record">
					<input :value="text" @change="updateData($event, record, 'remark')" />
				</div>

				<div slot="action" slot-scope="text, record">
					<a-popconfirm placement="topRight" title="确认删除？" @confirm="() => designDelete(record.id)">
						<a class="btn a-btn" v-if="record.code == null && canBeUpdate">删除</a>
					</a-popconfirm>
				</div>

				<div class="divcls" slot="divcls" slot-scope="text">{{ text }}</div>
				<div class="divcls div_border_right" slot="unitcls" slot-scope="text">{{ text }}</div>
				<!--<a-icon type="plus" slot="footer" :style="{paddingLeft: bigClient?'2.1%':'1.85%'}"
                @click="addSor('basic','基础性能')"/>-->
			</a-table>
		</div>
		<div class="wrapper">
			<div style="height: 25px;display: inline-flex">
				<span style="background: #0d84ff;width: 5px;height: 100%;display: inline-flex"></span
				><span
					style="font-size: 14px;font-weight: bold;padding-left: 15px;color: black;display: flex;justify-content: center;align-items: center;"
					>电性能<a-icon
						:type="electrical ? 'down' : 'up'"
						@click="changeDevelopOpen('electrical')"
						style="margin-left: 5px;"
				/></span>
				<!--<a class="btn"><a-icon class="icon success1" type="check" /><span class="txt">满足</span></a>
        <a class="btn"><a-icon class="icon warn1" type="exclamation" /><span class="txt">待确定</span></a>
        <a class="btn"><a-icon class="icon fail1" type="close" /><span class="txt">不满足</span></a>-->
			</div>
			<a-table
				id="electrical"
				:columns="columns"
				:data-source="dataSourceElectrical"
				:row-key="record => record.id"
				:pagination="false"
				:scroll="{ y: windowHeight }"
				bordered
			>
				<div slot="eveSor" slot-scope="text, record">
					<input :value="text" @change="updateData($event, record, 'eveSor')" />
				</div>

				<div slot="projectName" slot-scope="text, record">
					<input :value="text" v-if="record.code == null" @change="updateData($event, record, 'projectName')" />
					<span v-else>{{ text }}</span>
				</div>
				<div slot="unit" slot-scope="text, record">
					<input :value="text" v-if="record.code == null" @change="updateData($event, record, 'unit')" />
					<span v-else>{{ text }}</span>
				</div>
				<div slot="customerSor" slot-scope="text, record">
					<input :value="text" @change="updateData($event, record, 'customerSor')" />
				</div>
				<template slot="checkStatus" slot-scope="text, record">
					<div name="checkStatus" @click="isOwn == 1 && design.manCheckStatus == 0 && canBeUpdate ? editElectrical(record.id) : null">
						<a-select
							v-if="record.editable"
							dropdown-class-name="dropdownClassName"
							style="width: 100%;outline:none;font-size: 2px;"
							@blur="getList(false)"
							:autoFocus="true"
							:open="true"
							:showArrow="false"
							:default-value="text"
							@change="updateSelectData($event, record)"
						>
							<a-select-option :value="parseInt(1)" @click="getList(false)">
								满足
							</a-select-option>
							<a-select-option :value="parseInt(2)" @click="getList(false)">
								不满足
							</a-select-option>
							<a-select-option :value="parseInt(3)" @click="getList(false)">
								TBD
							</a-select-option>
							<a-select-option :value="parseInt(0)" @click="getList(false)">
								/
							</a-select-option>
						</a-select>

						<span v-else>
							<svg width="20" height="20" v-if="text == 1">
								<circle cx="10" cy="11" r="8" fill="#58a55c" />
							</svg>
							<svg width="20" height="20" v-else-if="text == 2">
								<circle cx="10" cy="11" r="8" fill="#f33" />
							</svg>
							<svg width="20" height="20" v-else-if="text == 3">
								<circle cx="10" cy="11" r="8" fill="#fac858" />
							</svg>
							<span v-else>/</span>
						</span>
					</div>
				</template>
				<div slot="remark" slot-scope="text, record">
					<input :value="text" @change="updateData($event, record, 'remark')" />
				</div>

				<div slot="action" slot-scope="text, record">
					<a-popconfirm placement="topRight" title="确认删除？" @confirm="() => designDelete(record.id)">
						<a class="btn a-btn" v-if="record.code == null && canBeUpdate">删除</a>
					</a-popconfirm>
				</div>

				<div class="divcls" slot="divcls" slot-scope="text">{{ text }}</div>
				<div class="divcls div_border_right" slot="unitcls" slot-scope="text">{{ text }}</div>
				<a-icon v-if="canBeUpdate"
					type="plus"
					slot="footer"
					:style="{ paddingLeft: bigClient ? '2.1%' : '1.85%' }"
					@click="addSor('electrical', '电性能')"
				/>
			</a-table>
		</div>
		<div class="wrapper">
			<div style="height: 25px;display: inline-flex">
				<span style="background: #0d84ff;width: 5px;height: 100%;display: inline-flex"></span
				><span
					style="font-size: 14px;font-weight: bold;padding-left: 15px;color: black;display: flex;justify-content: center;align-items: center;"
					>寿命性能<a-icon :type="life ? 'down' : 'up'" @click="changeDevelopOpen('life')" style="margin-left: 5px;"
				/></span>
				<!--<a class="btn"><a-icon class="icon success1" type="check" /><span class="txt">满足</span></a>
        <a class="btn"><a-icon class="icon warn1" type="exclamation" /><span class="txt">待确定</span></a>
        <a class="btn"><a-icon class="icon fail1" type="close" /><span class="txt">不满足</span></a>-->
			</div>
			<a-table
				id="life"
				:columns="columns"
				:data-source="dataSourceLife"
				:row-key="record => record.id"
				:pagination="false"
				:scroll="{ y: windowHeight }"
				bordered
			>
				<div slot="eveSor" slot-scope="text, record">
					<input :value="text" @change="updateData($event, record, 'eveSor')" />
				</div>

				<div slot="projectName" slot-scope="text, record">
					<input :value="text" v-if="record.code == null" @change="updateData($event, record, 'projectName')" />
					<span v-else>{{ text }}</span>
				</div>
				<div slot="unit" slot-scope="text, record">
					<input :value="text" v-if="record.code == null" @change="updateData($event, record, 'unit')" />
					<span v-else>{{ text }}</span>
				</div>
				<div slot="customerSor" slot-scope="text, record">
					<input :value="text" @change="updateData($event, record, 'customerSor')" />
				</div>
				<template slot="checkStatus" slot-scope="text, record">
					<div name="checkStatus" @click="isOwn == 1 && design.manCheckStatus == 0 && canBeUpdate ? editLife(record.id) : null">
						<a-select
							v-if="record.editable"
							dropdown-class-name="dropdownClassName"
							style="width: 100%;outline:none;font-size: 2px;"
							@blur="getList(false)"
							:autoFocus="true"
							:open="true"
							:showArrow="false"
							:default-value="text"
							@change="updateSelectData($event, record)"
						>
							<a-select-option :value="parseInt(1)" @click="getList(false)">
								满足
							</a-select-option>
							<a-select-option :value="parseInt(2)" @click="getList(false)">
								不满足
							</a-select-option>
							<a-select-option :value="parseInt(3)" @click="getList(false)">
								TBD
							</a-select-option>
							<a-select-option :value="parseInt(0)" @click="getList(false)">
								/
							</a-select-option>
						</a-select>

						<span v-else>
							<svg width="20" height="20" v-if="text == 1">
								<circle cx="10" cy="11" r="8" fill="#58a55c" />
							</svg>
							<svg width="20" height="20" v-else-if="text == 2">
								<circle cx="10" cy="11" r="8" fill="#f33" />
							</svg>
							<svg width="20" height="20" v-else-if="text == 3">
								<circle cx="10" cy="11" r="8" fill="#fac858" />
							</svg>
							<span v-else>/</span>
						</span>
					</div>
				</template>
				<div slot="remark" slot-scope="text, record">
					<input :value="text" @change="updateData($event, record, 'remark')" />
				</div>

				<div slot="action" slot-scope="text, record">
					<a-popconfirm placement="topRight" title="确认删除？" @confirm="() => designDelete(record.id)">
						<a class="btn a-btn" v-if="record.code == null && canBeUpdate">删除</a>
					</a-popconfirm>
				</div>

				<div class="divcls" slot="divcls" slot-scope="text">{{ text }}</div>
				<div class="divcls div_border_right" slot="unitcls" slot-scope="text">{{ text }}</div>
				<a-icon
					type="plus"
					slot="footer"
          v-if="canBeUpdate"
					:style="{ paddingLeft: bigClient ? '2.1%' : '1.85%' }"
					@click="addSor('life', '寿命性能')"
				/>
			</a-table>
		</div>
		<div class="wrapper">
			<div style="height: 25px;display: inline-flex">
				<span style="background: #0d84ff;width: 5px;height: 100%;display: inline-flex"></span
				><span
					style="font-size: 14px;font-weight: bold;padding-left: 15px;color: black;display: flex;justify-content: center;align-items: center;"
					>安全与环境<a-icon :type="safe ? 'down' : 'up'" @click="changeDevelopOpen('safe')" style="margin-left: 5px;"
				/></span>
				<!--<a class="btn"><a-icon class="icon success1" type="check" /><span class="txt">满足</span></a>
        <a class="btn"><a-icon class="icon warn1" type="exclamation" /><span class="txt">待确定</span></a>
        <a class="btn"><a-icon class="icon fail1" type="close" /><span class="txt">不满足</span></a>-->
			</div>
			<a-table
				id="safe"
				:columns="columns"
				:data-source="dataSourceSafe"
				:row-key="record => record.id"
				:pagination="false"
				:scroll="{ y: windowHeight }"
				bordered
			>
				<div slot="eveSor" slot-scope="text, record">
					<input :value="text" @change="updateData($event, record, 'eveSor')" />
				</div>

				<div slot="projectName" slot-scope="text, record">
					<input :value="text" v-if="record.code == null" @change="updateData($event, record, 'projectName')" />
					<span v-else>{{ text }}</span>
				</div>
				<div slot="unit" slot-scope="text, record">
					<input :value="text" v-if="record.code == null" @change="updateData($event, record, 'unit')" />
					<span v-else>{{ text }}</span>
				</div>
				<div slot="customerSor" slot-scope="text, record">
					<input :value="text" @change="updateData($event, record, 'customerSor')" />
				</div>
				<template slot="checkStatus" slot-scope="text, record">
					<div name="checkStatus" @click="isOwn == 1 && design.manCheckStatus == 0 && canBeUpdate ? editSafe(record.id) : null">
						<a-select
							v-if="record.editable"
							dropdown-class-name="dropdownClassName"
							style="width: 100%;outline:none;font-size: 2px;"
							@blur="getList(false)"
							:autoFocus="true"
							:open="true"
							:showArrow="false"
							:default-value="text"
							@change="updateSelectData($event, record)"
						>
							<a-select-option :value="parseInt(1)" @click="getList(false)">
								满足
							</a-select-option>
							<a-select-option :value="parseInt(2)" @click="getList(false)">
								不满足
							</a-select-option>
							<a-select-option :value="parseInt(3)" @click="getList(false)">
								TBD
							</a-select-option>
							<a-select-option :value="parseInt(0)" @click="getList(false)">
								/
							</a-select-option>
						</a-select>

						<span class="spanstatus" v-else>
							<svg width="20" height="20" v-if="text == 1">
								<circle cx="10" cy="11" r="8" fill="#58a55c" />
							</svg>
							<svg width="20" height="20" v-else-if="text == 2">
								<circle cx="10" cy="11" r="8" fill="#f33" />
							</svg>
							<svg width="20" height="20" v-else-if="text == 3">
								<circle cx="10" cy="11" r="8" fill="#fac858" />
							</svg>
							<span v-else>/</span>
						</span>
					</div>
				</template>
				<div slot="remark" slot-scope="text, record">
					<input :value="text" @change="updateData($event, record, 'remark')" />
				</div>

				<div slot="action" slot-scope="text, record">
					<a-popconfirm placement="topRight" title="确认删除？" @confirm="() => designDelete(record.id)">
						<a class="btn a-btn" v-if="record.code == null && canBeUpdate">删除</a>
					</a-popconfirm>
				</div>

				<div class="divcls" slot="divcls" slot-scope="text">{{ text }}</div>
				<div class="divcls div_border_right" slot="unitcls" slot-scope="text">{{ text }}</div>

				<a-icon
					type="plus"
					slot="footer"
          v-if="canBeUpdate"
					:style="{ paddingLeft: bigClient ? '2.1%' : '1.85%' }"
					@click="addSor('safe', '安全与环境')"
				/>
			</a-table>
		</div>
		<div class="wrapper">
			<div style="height: 25px;display: inline-flex">
				<span style="background: #0d84ff;width: 5px;height: 100%;display: inline-flex"></span
				><span
					style="font-size: 14px;font-weight: bold;padding-left: 15px;color: black;display: flex;justify-content: center;align-items: center;"
					>测试方法与标准<a-icon
						:type="test ? 'down' : 'up'"
						@click="changeDevelopOpen('test')"
						style="margin-left: 5px;"
				/></span>
				<!--<a class="btn"><a-icon class="icon success1" type="check" /><span class="txt">满足</span></a>
        <a class="btn"><a-icon class="icon warn1" type="exclamation" /><span class="txt">待确定</span></a>
        <a class="btn"><a-icon class="icon fail1" type="close" /><span class="txt">不满足</span></a>-->
			</div>
			<a-table
				id="test"
				:columns="columns"
				:data-source="dataSourceTest"
				:row-key="record => record.id"
				:pagination="false"
				:scroll="{ y: windowHeight }"
				bordered
			>
				<div slot="eveSor" slot-scope="text, record">
					<input :value="text" @change="updateData($event, record, 'eveSor')" />
				</div>

				<div slot="projectName" slot-scope="text, record">
					<input :value="text" v-if="record.code == null" @change="updateData($event, record, 'projectName')" />
					<span v-else>{{ text }}</span>
				</div>
				<div slot="unit" slot-scope="text, record">
					<input :value="text" v-if="record.code == null" @change="updateData($event, record, 'unit')" />
					<span v-else>{{ text }}</span>
				</div>
				<div slot="customerSor" slot-scope="text, record">
					<input :value="text" @change="updateData($event, record, 'customerSor')" />
				</div>
				<template slot="checkStatus" slot-scope="text, record">
					<div name="checkStatus" @click="isOwn == 1 && design.manCheckStatus == 0 && canBeUpdate ? editTest(record.id) : null">
						<a-select
							v-if="record.editable"
							dropdown-class-name="dropdownClassName"
							style="width: 100%;outline:none;font-size: 2px;"
							@blur="getList(false)"
							:autoFocus="true"
							:open="true"
							:showArrow="false"
							:default-value="text"
							@change="updateSelectData($event, record)"
						>
							<a-select-option :value="parseInt(1)" @click="getList(false)">
								满足
							</a-select-option>
							<a-select-option :value="parseInt(2)" @click="getList(false)">
								不满足
							</a-select-option>
							<a-select-option :value="parseInt(3)" @click="getList(false)">
								TBD
							</a-select-option>
							<a-select-option :value="parseInt(0)" @click="getList(false)">
								/
							</a-select-option>
						</a-select>

						<span v-else>
							<svg width="20" height="20" v-if="text == 1">
								<circle cx="10" cy="11" r="8" fill="#58a55c" />
							</svg>
							<svg width="20" height="20" v-else-if="text == 2">
								<circle cx="10" cy="11" r="8" fill="#f33" />
							</svg>
							<svg width="20" height="20" v-else-if="text == 3">
								<circle cx="10" cy="11" r="8" fill="#fac858" />
							</svg>
							<span v-else>/</span>
						</span>
					</div>
				</template>
				<div slot="remark" slot-scope="text, record">
					<input :value="text" @change="updateData($event, record, 'remark')" />
				</div>

				<div slot="action" slot-scope="text, record">
					<a-popconfirm placement="topRight" title="确认删除？" @confirm="() => designDelete(record.id)">
						<a class="btn a-btn" v-if="record.code == null && canBeUpdate">删除</a>
					</a-popconfirm>
				</div>

				<div class="divcls" slot="divcls" slot-scope="text">{{ text }}</div>
				<div class="divcls div_border_right" slot="unitcls" slot-scope="text">{{ text }}</div>
				<a-icon
					type="plus"
					slot="footer"
          v-if="canBeUpdate"
					:style="{ paddingLeft: bigClient ? '2.1%' : '1.85%' }"
					@click="addSor('test', '测试方法与标准')"
				/>
			</a-table>

			<a-drawer
				:bodyStyle="{ height: '100%' }"
				placement="right"
				:closable="false"
				width="80%"
				:visible="visible3"
				@close="onClose3"
			>
				<iframe :src="pdfUrl + '#view=FitH,top&'" width="100%" height="100%"></iframe>
			</a-drawer>
		</div>

		<!--<div class="wrapper">
      <div class="h1">
        关键性能管理（自定义输入栏）
      </div>

      <div class="statusbar">
        <a class="btn a-btn" @click="$refs.addForm.add(batteryId)" v-if="isOwn == 1 && (design.manCheckStatus == 0 || design.manCheckStatus == 20 || design.manCheckStatus == 80 )">新增</a>
        <a-popconfirm placement="topRight" title="确认复制？" @confirm="() => designCopy()">
          <a class="btn a-btn" v-if="isOwn == 1 && (design.manCheckStatus == 0 || design.manCheckStatus == 20 || design.manCheckStatus == 80 )">复制</a>
        </a-popconfirm>

        <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => designDelete()">
          <a class="btn a-btn" v-if="isOwn == 1 && (design.manCheckStatus == 0 || design.manCheckStatus == 20 || design.manCheckStatus == 80 )">删除</a>
        </a-popconfirm>
      </div>

      <a-table
        :scroll="{ y: windowHeight }"
        :columns="columns1"
        :data-source="dataSource2"
        :row-key="(record) => record.id"
        :pagination="false"
        :row-selection="rowSelection"
        bordered
      >
        <template slot="batteryName">
          {{batteryName}}管理
        </template>
        <div slot="customerSor" slot-scope="text,record">
          <input :value="text" @change="updateData($event,record,'customerSor')"/>

        </div>
        <div slot="projectCategory" slot-scope="text,record" :title="text"><input :value="text" @change="updateData($event,record,'projectCategory')"/></div>
        <div slot="projectName" slot-scope="text,record" :title="text"><input :value="text" @change="updateData($event,record,'projectName')"/></div>
        <div slot="unit" slot-scope="text,record"><input :value="text" @change="updateData($event,record,'unit')"/></div>
        <div slot="eveSor" slot-scope="text,record"><input :value="text" @change="updateData($event,record,'eveSor')"/></div>
        <template slot="checkStatus" slot-scope="text,record">
          <div class="divcls">

            <a-select v-if="record.editable" style="width: 100%;outline:none" size="small" :default-value="text"
                      @change="updateSelectData($event,record)">
              <a-select-option :value="parseInt(1)">
                满足
              </a-select-option>
              <a-select-option :value="parseInt(2)">
                不满足
              </a-select-option>
              <a-select-option :value="parseInt(3)">
                TBD
              </a-select-option>
              <a-select-option :value="parseInt(0)">
                /
              </a-select-option>

            </a-select>

            <span class="spanstatus" v-else @click="isOwn == 1 && design.manCheckStatus == 0?edit1(record.id):null">
              <a-icon v-if="text==1" class="success1" type="check" />
              <svg t="1669081811549" v-else-if="text==3" class="icon warn1" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3553" width="16" height="14"><path d="M450.602458 665.598073a62.463819 62.463819 0 0 0 122.879645 0L614.441984 102.399704A102.615282 102.615282 0 0 0 512.04228 0 105.256116 105.256116 0 0 0 409.642577 112.639674L450.602458 665.598073z m61.439822 153.599556a102.399704 102.399704 0 1 0 102.399704 102.399703 96.740773 96.740773 0 0 0-102.399704-102.399703z" p-id="3554" fill="#fec303"></path></svg>
              &lt;!&ndash;<a-icon v-else-if="text==3" class="warn1" type="exclamation" />&ndash;&gt;
              <a-icon v-else-if="text == 2" class="fail1" type="close" />
              <span v-else>/</span>
            </span>
          </div>

        </template>
        <div slot="remark" slot-scope="text,record"><input :value="text" @change="updateData($event,record,'remark')"/></div>
        <div class="divcls" slot="divcls" slot-scope="text">{{text}}</div>
        <div class="divcls div_border_right" slot="unitcls" slot-scope="text">{{text}}</div>
      </a-table>


      <add-form ref="addForm" @ok="getList(false)"/>
    </div>-->
	</div>
</template>
<script>
import { list, update, copy, exportExcel, exportExcel1, add } from "@/api/modular/system/batteryDesignSorManage"
import { getBatteryDesign,canBeUpdate } from "@/api/modular/system/batterydesignManage"
import { checkRecordGetRelationByRecordId } from "@/api/modular/system/batterydesignCheckRecordManage"
import addForm from "./addForm"
import { mapActions, mapGetters } from "vuex"
import { ALL_APPS_MENU } from "@/store/mutation-types"
import Vue from "vue"

export default {
	components: {
		addForm
	},
	data() {
		return {
      canBeUpdate:true,
      checkPage: this.$route.query.checkPage,
			spinning: false,
			visible3: false,
			pdfUrl: "",
			design: {},
			structureType1: "",
			stage: "",
			develop: false,
			safe: false,
			test: false,
			life: false,
			basic: false,
			electrical: false,
			checkStatus: { 0: "/", 1: "满足", 2: "不满足", 3: "TBD" },
			windowHeight: document.documentElement.clientHeight - 200,
			sor_project: 0,
			isOwn: 0,

			sor_performance: 0,
			sor_size: 0,
			sor_safety_test: 0,
			sor_warranty: 0,
			sor_hot: 0,
			bigClient: document.documentElement.clientHeight > 700,
			sor_power: 0,
			sor_fast_charging: 0,
			sor_life: 0,
			sor_electrical: 0,
			dataSource1: [],
			selectedRowKeys: [],
			selectedRow: [],
			dataSourceBasic: [],
			dataSourceLife: [],
			dataSourceSafe: [],
			dataSourceTest: [],
			dataSourceElectrical: [],
			rowSelection: {
				columnWidth: 30,
				onChange: (selectedRowKeys, selectedRows) => {
					this.selectedRowKeys = selectedRowKeys
					this.selectedRow = selectedRows
				}
			},

			dataSource2: [],
			batteryName: "",
			batteryId: null,
			// 表头
			columns: [
				{
					title: "序号",
					dataIndex: "index",
					align: "center",
					width: 30,
					customRender: (text, record, index) => `${index + 1}`
				},
				{
					title: "内容",
					dataIndex: "projectName",
					scopedSlots: { customRender: "projectName" },
					align: "center",
					width: 150
				},
				{
					title: "单位",
					dataIndex: "unit",
					width: 50,
					align: "center",
					scopedSlots: { customRender: "unit" }
				},
				{
					title: "客户SOR输入",
					dataIndex: "customerSor",
					align: "center",
					width: 80,
					scopedSlots: { customRender: "customerSor" }
				},
				{
					title: "EVE定义",
					dataIndex: "eveSor",
					align: "center",
					width: 80,
					scopedSlots: { customRender: "eveSor" }
				},

				{
					title: "状态识别",
					dataIndex: "checkStatus",
					align: "center",
					width: 50,
					scopedSlots: { customRender: "checkStatus" }
				},
				{
					title: "备注",
					dataIndex: "remark",
					align: "center",
					width: 100,
					scopedSlots: { customRender: "remark" }
				},
				{
					title: "操作",
					dataIndex: "action",
					align: "center",
					width: 50,
					scopedSlots: { customRender: "action" }
				}
			],
			columns1: [
				{
					title: "序号",
					dataIndex: "index",
					align: "center",
					width: 30,
					customRender: (text, record, index) => <div class="divcls div_border_right"> {index + 1} </div>
				},
				{
					title: "项目",
					dataIndex: "projectCategory",
					align: "center",
					width: 70,
					scopedSlots: { customRender: "projectCategory" }
				},
				{
					title: "内容",
					dataIndex: "projectName",
					scopedSlots: { customRender: "projectName" },
					width: 150
				},
				{
					title: "单位",
					dataIndex: "unit",
					width: 50,
					align: "center",
					scopedSlots: { customRender: "unit" }
				},
				{
					title: "客户SOR输入",
					dataIndex: "customerSor",
					align: "center",
					width: 80,
					scopedSlots: { customRender: "customerSor" }
				},
				{
					title: "EVE定义",
					dataIndex: "eveSor",
					align: "center",
					width: 80,
					scopedSlots: { customRender: "eveSor" }
				},

				{
					title: "状态识别",
					dataIndex: "checkStatus",
					align: "center",
					width: 50,
					scopedSlots: { customRender: "checkStatus" }
				} /* {
                title: '备注',
                dataIndex: 'remark',
                align: 'center',
                width:50,
                scopedSlots: { customRender: 'remark' },
              }, */
			]
		}
	},

	computed: {
		...mapGetters(["userInfo"])
	},

	mounted() {

	},

	created() {
		switch (this.$route.query.type) {
			case "a":
				this.stage = "A样"
				break
			case "b":
				this.stage = "B样"
				break
			case "c":
				this.stage = "C样"
				break
			case "d":
				this.stage = "D样"
				break
		}
		//审核页面
		if(null != this.$route.query.businessId){
      checkRecordGetRelationByRecordId({id:this.$route.query.businessId}).then(res1 => {
        this.batteryId = res1.data.inBatteryId
      }).then(() => {
        getBatteryDesign({ inBatteryId: this.batteryId, type: "sor" }).then(res => {
          this.design = res.data
          switch (res.data.structureType) {
            case "g_cylinder":
              this.structureType1 = "G圆柱"
              break
            case "c_cylinder":
              this.structureType1 = "C圆柱"
              break
            case "v_cylinder":
              this.structureType1 = "V圆柱"
              break
            case "winding":
              this.structureType1 = "方形卷绕"
              break
            case "lamination":
              this.structureType1 = "方形叠片"
              break
            case "soft_roll":
              this.structureType1 = "软包"
              break
          }
        })
      }).then(() => {
        canBeUpdate({inBatteryId:this.batteryId,type:'sor'}).then(res => {
          this.canBeUpdate = res.data
        })
      }).then(() => {
        this.getList(true)
      })
    }else{
		  this.batteryId = this.$route.query.batteryId
      getBatteryDesign({ inBatteryId: this.$route.query.batteryId, type: "sor" }).then(res => {
        this.design = res.data
        switch (res.data.structureType) {
          case "g_cylinder":
            this.structureType1 = "G圆柱"
            break
          case "c_cylinder":
            this.structureType1 = "C圆柱"
            break
          case "v_cylinder":
            this.structureType1 = "V圆柱"
            break
          case "winding":
            this.structureType1 = "方形卷绕"
            break
          case "lamination":
            this.structureType1 = "方形叠片"
            break
          case "soft_roll":
            this.structureType1 = "软包"
            break
        }
      }).then(() => {
        canBeUpdate({inBatteryId:this.batteryId,type:'sor'}).then(res => {
          this.canBeUpdate = res.data
        })
      }).then(() => this.getList(true))
    }



		this.breadcrumb = JSON.parse(localStorage.getItem("breadcrumb"))
	},

	methods: {
		...mapActions(["MenuChange"]),
		gotoDevelop(record) {
			//this.switchApp()
			this.$router.push({
				path: "/batterydesign"
			})
		},
		onClose3() {
			this.visible3 = false
		},
		changeDevelopOpen(id) {
			this[id] = !this[id]
			let develop = document.getElementById(id)

			if (develop.style.display == "none") {
				develop.style.display = "unset"
			} else {
				develop.style.display = "none"
			}
		},
		addSor(type, projectCategory) {
			let params = {}
			params.batteryId = this.batteryId
			params.dataType = type
			params.projectCategory = projectCategory
			add(params).then(() => this.getList())
		},
		exportDataMethod() {
			this.spinning = true
			exportExcel({ batteryId: this.batteryId }).then(res => {
				this.spinning = false
				this.visible3 = true
				this.pdfUrl = process.env.VUE_APP_API_BASE_URL + "/sysFileInfo/previewPdf?id=" + res.data
			})
		},
		exportDataMethod1() {
			exportExcel1({ batteryId: this.batteryId }).then(res => {
				this.spinning = true
				const fileName = "SOR管理导出表.xlsx"
				if (!res) return
				const blob = new Blob([res.data], { type: "application/vnd.ms-excel" }) // 构造一个blob对象来处理数据，并设置文件类型
				if (window.navigator.msSaveOrOpenBlob) {
					//兼容IE10
					navigator.msSaveBlob(blob, fileName)
				} else {
					const href = URL.createObjectURL(blob) //创建新的URL表示指定的blob对象
					const a = document.createElement("a") //创建a标签
					a.style.display = "none"
					a.href = href // 指定下载链接
					a.download = fileName //指定下载文件名
					a.click() //触发下载
					URL.revokeObjectURL(a.href) //释放URL对象
					this.spinning = false
				}
			})
		},
		switchApp() {
			const applicationData = Vue.ls.get(ALL_APPS_MENU)
			this.MenuChange(applicationData[0])
				.then(res => {})
				.catch(err => {
					this.$message.error("错误提示：" + err.message, 1)
				})
		},
		gotoManager() {
			//this.switchApp()
			this.$router.push({
				path: "/battery_design_manager",
				query: {
					batteryId: this.batteryId
				}
			})
		},
		designDelete(id) {
			update({ id: id, status: 1 }).then(() => this.getList())
		},

		designCopy() {
			if (this.selectedRowKeys.length == 0) {
				this.$message.error("请先选中要复制的数据")
			}

			copy(this.selectedRow).then(() => {
				this.getList(false)
			})
		},

		numberHandle(number, n) {
			if (null == number) {
				return ""
			}
			n = n ? parseInt(n) : 0
			if (n <= 0) {
				return Math.round(number)
			}
			number = Math.round(number * Math.pow(10, n)) / Math.pow(10, n) //四舍五入
			number = Number(number).toFixed(n) //补足位数
			return number
		},
		edit1(id) {
			const newData = [...this.dataSourceBasic]
			const target = newData.find(item => id === item.id)
			this.editingKey = id
			if (target) {
				target.editable = true
				this.dataSourceBasic = newData
			}
		},
		edit(id) {
			const newData = [...this.dataSource1]
			const target = newData.find(item => id === item.id)
			this.editingKey = id
			if (target) {
				target.editable = true
				this.dataSource1 = newData
			}
		},
		editTest(id) {
			const newData = [...this.dataSourceTest]
			const target = newData.find(item => id === item.id)
			this.editingKey = id
			if (target) {
				target.editable = true
				this.dataSourceTest = newData
			}
		},
		editSafe(id) {
			const newData = [...this.dataSourceSafe]
			const target = newData.find(item => id === item.id)
			this.editingKey = id
			if (target) {
				target.editable = true
				this.dataSourceSafe = newData
			}
		},
		editElectrical(id) {
			const newData = [...this.dataSourceElectrical]
			const target = newData.find(item => id === item.id)
			this.editingKey = id
			if (target) {
				target.editable = true
				this.dataSourceElectrical = newData
			}
		},
		editLife(id) {
			const newData = [...this.dataSourceLife]
			const target = newData.find(item => id === item.id)
			this.editingKey = id
			if (target) {
				target.editable = true
				this.dataSourceLife = newData
			}
		},
		initBefore() {
      /*setTimeout(() => {
        let inputs = document.getElementsByTagName("input")
        for (let i = 0; i < inputs.length; i++) {
          console.log(inputs[i])
          inputs[i].disabled = true
        }
      },1000)*/



		},
		updateData(event, record, column) {
			//修改时禁止输入

			let inputs = document.getElementsByTagName("input")
			let controlInput = []
			for (let i = 0; i < inputs.length; i++) {
				if (!inputs[i].disabled) {
					controlInput.push(inputs[i])
				}
			}

			for (let i = 0; i < controlInput.length; i++) {
				controlInput[i].disabled = true
			}

			let param = {}
			param[column] = event.target.value
			param["id"] = record.id
			update(param).then(res => {
				this.getList(false)
				this.$nextTick(() => {
					if (res.success) {
						this.$message.success("保存成功")
					} else {
						this.$message.error(res.message)
					}
					for (let i = 0; i < controlInput.length; i++) {
						controlInput[i].disabled = false
					}
				})
			})
		},
		gotoBom() {
			//this.switchApp()
			this.$router.push({
				path: "/sys_battery_design_bom",
				query: {
					batteryId: this.batteryId
				}
			})
		},
		gotoMi() {
			//this.switchApp()
			this.$router.push({
				path: "/g_cylinder_mi_standard_manage",
				query: {
					batteryId: this.batteryId
				}
			})
		},
		updateSelectData(e, record) {
			let param = {}
			param["checkStatus"] = e
			param["id"] = record.id
			update(param).then(res => {
				this.getList(false)
				this.$nextTick(() => {
					if (res.success) {
						this.$message.success("保存成功")
					} else {
						this.$message.error(res.message)
					}
				})
			})
		},

		getByClass(parent, cls) {
			if (parent.getElementsByClassName) {
				return Array.from(parent.getElementsByClassName(cls))
			} else {
				var res = []
				var reg = new RegExp(" " + cls + " ", "i")
				var ele = parent.getElementsByTagName("*")
				for (var i = 0; i < ele.length; i++) {
					if (reg.test(" " + ele[i].className + " ")) {
						res.push(ele[i])
					}
				}
				return res
			}
		},
		init() {
			this.$nextTick(() => {
				let items = this.getByClass(document, "divcls")
				for (const e of items) {
					var _e = e.parentNode
					_e.classList.add("tdcls")
					if (e.classList.contains("div_border_right")) {
						_e.classList.add("td_border_right")
					}
					if (e.classList.contains("div_width")) {
						_e.classList.add("td_width")
					}
				}

				let $items = this.getByClass(document, "ant-layout")
				for (const e of $items) {
					e.setAttribute("style", "min-height:initial")
				}
			})
		},
		getList(update) {
			list({ batteryId: this.batteryId, dataType: "develop" })
				.then(res => {
					this.dataSource1 = res.data
					this.batteryName = res.data[0].batteryName
				})
				.then(() => {
					list({ batteryId: this.batteryId, dataType: "electrical" })
						.then(res => {
							this.dataSourceElectrical = res.data
						})
						.then(() => {
							list({ batteryId: this.batteryId, dataType: "basic" })
								.then(res => {
									this.dataSourceBasic = res.data
								})
								.then(() => {
									list({ batteryId: this.batteryId, dataType: "life" }).then(res => {
										this.dataSourceLife = res.data
									})
								})
								.then(() => {
									list({ batteryId: this.batteryId, dataType: "safe" }).then(res => {
										this.dataSourceSafe = res.data
									})
								})
								.then(() => {
									list({ batteryId: this.batteryId, dataType: "test" }).then(res => {
										this.dataSourceTest = res.data
									})
								})
								.then(() => {
									getBatteryDesign({ inBatteryId: this.batteryId, type: "sor" }).then(res => {
										this.isOwn = res.data.isOwn
										this.design = res.data
										if (res.data.isOwn == 0 || this.design.manCheckStatus == 10 || this.design.manCheckStatus == 70) {
											let inputs = document.getElementsByTagName("input")
											let controlInput = []

											for (let i = 0; i < inputs.length; i++) {
												if (!inputs[i].disabled) {
													controlInput.push(inputs[i])
												}
											}

											for (let i = 0; i < controlInput.length; i++) {
												controlInput[i].disabled = true
											}
										}
									})
								}).finally(() => {
								  if(this.$route.query.checkPage == 1 || !this.canBeUpdate){
                    let inputs = document.getElementsByTagName("input")
                    for (let i = 0; i < inputs.length; i++) {
                      inputs[i].disabled = true
                    }
								  let div = document.getElementsByName("checkStatus")
                    for (let i = 0; i < inputs.length; i++) {
                      inputs[i].disabled = true
                    }
                  }

              })
						})
				})
		}
	}
}
</script>
<style lang="less" scoped="">
.h1 {
	font-size: 16px;
	/* margin-bottom: 5px; */
	text-align: center;
	background: #0049b0;
	color: #fff;
}

/deep/ .ant-table-wrapper {
	background: #fff;
}

/deep/ .table-operator {
	margin-bottom: 16px;
}

button {
	margin-right: 8px;
}

/deep/ .ant-table-thead > tr > th {
	padding: 2px 0 2px 4px;

	font-size: 13px;
	font-weight: bold;
	background: #ffffff;
	color: #000000;
}

/deep/ .ant-table-tbody > tr > td {
	padding: 0;
	margin: 0;

	font-size: 12px;
}

/deep/ .td_width {
	width: 50px;
	padding: 0 !important;
}

.div_width {
	width: 30px;
	margin: auto;
}

input {
	width: 100%;
	height: 25px;
	margin: 0;
	border: 0;
	outline: none;
	text-align: center;
}

.wrapper {
	background: #fff;
	padding: 0 10px;
	margin-bottom: 10px;
	overflow: hidden;
}

/deep/ .ant-select-selection {
	border: none;
}

.spanstatus {
	display: block;
	padding: 1px 4px;
	border-radius: 2px;
	height: 25px;
}

.success1 {
	//background: #66b72a;
	color: #66b72a;
	margin-top: 5px;
}

.warn1 {
	//background: #fec303;
	color: #fec303;
	margin-top: 5px;
}

.fail1 {
	//background: #e05328;
	color: #e05328;
	margin-top: 5px;
}

.statusbar {
	overflow: hidden;
	height: 32px;
	line-height: 32px;
	font-size: 12px;
	text-align: right;
}

.statusbar::after {
	content: " ";
	display: block;
	height: 0;
	clear: both;
}

.statusbar .icon {
	margin-right: 3px;
	font-weight: bold;
}

.statusbar .btn {
	/* float: right; */
	margin-left: 20px;
}

.statusbar .a-btn {
	border-radius: 2px;
	background: #0049b0;
	color: #fff;
	padding: 2px 15px;
	letter-spacing: 2px;
}

.statusbar .txt {
	color: #000;
	font-weight: bold;
}

.tip {
	float: right;
	/* margin-left: 120px; */
}

.tab-title {
	padding: 0 10px;
}

div.sub-title .tip {
	font-family: SourceHanSansSC;
	font-weight: 400;
	font-size: 15px;
	color: rgba(0, 101, 255, 0.67);
}
.anticon svg {
	font-size: 13px;
}

div.tab-head {
	border-bottom: 1px solid #d3d2d2c9;
}

div.tab-head div {
	display: inline-block;
	font-weight: 700;
	font-size: 16px;
	color: rgb(128, 128, 128);
	margin-bottom: -6px;
	cursor: pointer;
}

div.tab-head div.active {
	font-size: 24px;
	color: rgba(0, 73, 176, 1);
	margin-bottom: -4px;
	cursor: text;
}

div.sub-title {
	overflow: hidden;
	padding: 6px 10px;
}

div.sub-title::after {
	content: " ";
	display: block;
	height: 0;
	clear: both;
}

div.sub-title span {
	display: block;
	float: left;
	margin-right: 6px;
}

div.sub-title span:first-child {
	margin-top: 1px;
}

/deep/ .ant-table-thead > tr > th:first-child {
	padding: 0;
}

/deep/ .ant-table-tbody > tr.ant-table-row-selected td:first-child {
	background: #fafafa;
}

/deep/ .tdcls1 {
	color: #000;
	background: rgb(239, 239, 239);
}

/* /deep/td {
    height: 29px;
    vertical-align: middle;
  }

  /deep/td > div{
    height: 100%;
    margin: 0;
    padding: 0;
  } */
/deep/ .ant-select-selection-selected-value {
	text-align: center;
}

/deep/ .ant-select-open .ant-select-selection {
	border: none;
	box-shadow: none;
}

/deep/ .ant-select-selection__rendered {
	margin: auto;
	line-height: initial;
	font-size: 12px;
	text-align: center;
}

/deep/ .ant-select-selection--single {
	height: auto;
}

/deep/ .anticon svg {
	font-size: 16px;
}

/deep/ .ant-table-footer {
	padding: 0;
	background: unset;
}

/deep/ .ant-empty-normal .ant-empty-image {
	display: none;
}

/deep/ .ant-empty-normal {
	margin: 0;
}

/deep/ .ant-table-placeholder {
	padding: 0;
}
.tip {
	color: rgb(255, 0, 0);
}

.dropdownClassName .ant-select-dropdown-menu-item {
		padding: 2px 8px !important;
		font-size: 12px !important;
		text-align: left !important;
		font-weight: 100;
	}


// 表头
/deep/.ant-table-thead tr th {
	padding: 5px 0;
	font-size: 13px;
	background: rgba(0, 73, 176, 0.7) !important;
	color: #fff;
	border: none;
	font-weight: 400;
}

</style>

<style lang="less">
#develop tr td:nth-child(-n + 3) {
	background-color: #efefef;
}

#basic tr td:nth-child(-n + 3) {
	background-color: #efefef;
}
</style>
