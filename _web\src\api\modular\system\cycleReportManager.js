import { axios } from '@/utils/request'

// 循环报告-根据id查询报告任务
export function getCycleById(parameter) {
  return axios({
    url: '/cycleReportTask/getById',
    method: 'post',
    data: parameter
  })
}

// 循环报告-报告任务新增、暂存或实时修改
export function cycleTestReportTask(parameter, id = null) {
  return axios({
    url: id == null ? '/cycleReportTask/addCycle' : '/cycleReportTask/addCycle?id=' + id,
    method: 'post',
    data: parameter
  })
}

// 循环报告-报告任务提交
export function commitCycle(parameter, id) {
  return axios({
    url: '/cycleReportTask/commitCycle?id=' + id,
    method: 'post',
    data: parameter
  })
}

// 循环报告-获取动力循环报告数据
export function getDongLiCycleReport(parameter) {
  return axios({
    url: '/cycleReportTask/getDongLiCycleReport',
    method: 'post',
    data: parameter
  })
}

// 循环报告-导出动力循环报告数据
export function exportDongLiCycleReport(parameter) {
  return axios({
    url: '/cycleReportTask/exportDongLiCycleReport',
    method: 'post',
    data: parameter,
    responseType: 'blob'
  })
}

// 动力测试报告-动力测试报告参数提交
export function commitDongLiParam(parameter) {
  return axios({
    url: '/cycleReportTask/commitDongLiParam',
    method: 'post',
    data: parameter
  })
}

// 日历寿命报告-根据ordtaskid获取RPT参数
export function getRptParamByOrdtaskid(parameter) {
  return axios({
    url: '/cycleReportTask/getRptParamByOrdtaskid',
    method: 'post',
    data: parameter
  })
}


// 日历寿命报告-获取动力日历寿命报告数据
export function getDongLiCalendarReport(parameter) {
  return axios({
    url: '/cycleReportTask/getDongLiCalendarReport',
    method: 'post',
    data: parameter
  })
}

// 日历寿命报告-导出动力日历寿命报告数据
export function exportDongLiCalendarReport(parameter) {
  return axios({
    url: '/cycleReportTask/exportDongLiCalendarReport',
    method: 'post',
    data: parameter,
    responseType: 'blob'
  })
}

// 动力数据建模_根据测试项目ID获取测试内容
export function getTemGradientByOrdtaskId(ordtaskId, parameter) {
  return axios({
    url: 'tLimsTemGradient/getByOrdtaskId/' + ordtaskId,
    method: 'post',
    data: parameter
  })
}

// 动力数据建模-获取动力数据
export function getDongLiData(parameter) {
  return axios({
    url: '/cycleReportTask/getDongLiData',
    method: 'post',
    data: parameter
  })
}

// 动力数据建模-导出动力数据
export function exportDongLiData(parameter) {
  return axios({
    url: '/cycleReportTask/exportDongLiData',
    method: 'post',
    data: parameter,
    responseType: 'blob'
  })
}

// 动力数据建模_根据测试项目ID获取测试条件
export function getConditionsByOrdtaskId(parameter) {
  return axios({
    url: '/tLimsOrdtaskCondition/getByOrdtaskid',
    method: 'post',
    data: parameter
  })
}