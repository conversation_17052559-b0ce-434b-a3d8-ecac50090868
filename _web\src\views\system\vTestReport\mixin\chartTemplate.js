import {getChartTemplateRelationList,saveChartTemplate,updateChartTemplate,deleteChartTemplate} from "@/api/modular/system/chartTemplate.js";

export const chartTemplate = {
    data(){
        return{
            reportChartTemplateList:{},//报告图表模板列表
        }
    },
    methods:{
        // 获得图表的模板
        async getChartTemplateRelationList(id,list){
            await getChartTemplateRelationList(id,{}).then(res => {
                for(let i = 0 ; i < list.length; i++){
                    if(!this.reportChartTemplateList[list[i]]) this.reportChartTemplateList[list[i]] = { reportId:id, targetChart:list[i],optionKey:this.optionKey, templateParamJson:{allData:{},checkData:[],legendData:{}} }

                    // 如果查询到模板，在赋值
                    let haveIndex = -1
                    if(this.optionKey === undefined){
                        haveIndex = res.data.findIndex(item => item.targetChart === list[i])
                    }else{
                        haveIndex = res.data.findIndex(item => item.targetChart === list[i] && item.optionKey === this.optionKey)
                    }

                    if(haveIndex !== -1){
                        this.reportChartTemplateList[list[i]].templateId = res.data[haveIndex].id
                        this.reportChartTemplateList[list[i]].originalParamJson = JSON.parse(res.data[haveIndex].originalParamJson) 
                        this.reportChartTemplateList[list[i]].templateParamJson = JSON.parse(res.data[haveIndex].templateParamJson) 
                    }
                }
            })
        },

        // 新建报告id与模板的关系
        saveChartTemplate(params){
            saveChartTemplate(params).then(res => {
                if(res.success){
                    this.reportChartTemplateList[params.targetChart].templateId = res.data
                    this.reportChartTemplateList[params.targetChart].originalParamJson = JSON.parse(params.originalParamJson) 
                }
            })

        },

        updateChartTemplate(params){
            updateChartTemplate(params).then(res => {
            })
        },

        async deleteChartTemplate(params,isHandle){
            await deleteChartTemplate(params.id).then(res => {
                if(res.data){
                    this.reportChartTemplateList[params.targetChart] = { reportId:params.reportId,targetChart:params.targetChart, templateParamJson:{allData:{},checkData:[],legendData:{} },originalParamJson:undefined}
                    if(params.optionKey !== undefined) this.reportChartTemplateList[params.targetChart].optionKey = params.optionKey
                    if(isHandle) this.handleInitChart(params.targetChart)
                }
            })
        },
        
    }
}