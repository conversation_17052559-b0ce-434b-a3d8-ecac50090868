<template>
  <div slot="search">
    <div style="float: left;width: 1300px;margin: 20px 0px 0px 30px;padding: 0px 0px 0px 0px">
      <a-form layout="inline">
        <a-row :gutter="0">
          <a-col :md="6" :sm="20">
            <a-form-item label="生产单号">
              <a-input v-model="queryParam.productionOrderNum"/>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="20">
            <a-form-item label="产品型号">
              <a-input v-model="queryParam.model"/>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="20">
            <a-form-item label="实验类型">
              <a-select :allowClear="true" v-model="queryParam.experimentType" style="width: 183px">
                <a-select-option value="0">
                  研发验证
                </a-select-option>
                <a-select-option value="1">
                  产品验证
                </a-select-option>
                <a-select-option value="2">
                  产品鉴定
                </a-select-option>
                <a-select-option value="3">
                  客户交付
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="20">
            <a-form-item label="综合判定">
              <a-select :allowClear="true" v-model="queryParam.comprehensiveJudgment" style="width: 183px">
                <a-select-option value="0">
                  合格
                </a-select-option>
                <a-select-option value="1">
                  不合格
                </a-select-option>
                <a-select-option value="2">
                  特采
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="0">
          <a-col :md="6" :sm="24">
            <a-form-item style="margin-left: 11px"/>
            <a-form-item label="状态">
              <a-select :allowClear="true" v-model="queryParam.recordStatus" style="width: 183px">
                <a-select-option value="0">
                  录入中
                </a-select-option>
                <a-select-option value="1">
                  待审核
                </a-select-option>
                <a-select-option value="2">
                  已审核
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="9" :sm="24">
            <a-form-item style="margin-left: 11px"/>
            <a-form-item label="时间">
              <a-range-picker
                v-model="queryParam.searchDate"
                :show-time="{
                      hideDisabledOptions: true,
                    }"
                format="YYYY-MM-DD HH:mm:ss"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div style="float: right;width: 400px;margin:20px 80px 0px 0px">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="4" :sm="24">
            <a-button type="primary" style="" @click="searchData" :loading="searchLoading">查询</a-button>
          </a-col>
          <a-col :md="4" :sm="24">
            <a-button type="primary" style="" @click="resetSearch">重置</a-button>
          </a-col>
          <a-col :md="4" :sm="24">
            <a-button type="primary" style="" @click="openAdd">新建</a-button>
          </a-col>
          <a-col :md="4" :sm="24">
            <a-button type="primary" style="" @click="openEdit">编辑</a-button>
          </a-col>
          <a-col :md="4" :sm="24">
            <a-popconfirm
              title="确定删除吗?"
              ok-text="确定"
              cancel-text="取消"
              @confirm="deleteStandard()"
              :disabled="this.beforeDeleteFlag"
              placement="topRight"
            >
              <a-button type="primary" @click="beforeDelete()">删除</a-button>
            </a-popconfirm>
          </a-col>
        </a-row>
        <a-row :gutter="48" style="margin-top: 10px">
          <a-col :md="4" :sm="24">
            <a-button type="primary" style="" @click="openAdd">导出</a-button>
          </a-col>
          <a-col :md="4" :sm="24">
            <a-button type="primary" style="" @click="reportForExam">报审</a-button>
          </a-col>
          <a-col :md="4" :sm="24">
            <a-button type="primary" style="" @click="auditReport">审核</a-button>
          </a-col>
          <a-col :md="4" :sm="24">
            <a-button type="primary" style="" @click="testDataRecord">测试数据录入</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div style="float: left;width: 100%">
      <a-table style="margin: 20px;background: #fff;" :data-source="resultData" :scroll="{x: 5000}" bordered
               :row-selection="{ selectedRowKeys: selectedRowKeys, selectedRows: selectedRows, onChange: onSelectChange }">
        <a-table-column align="center" title="序号" data-index="serialNum"/>
        <a-table-column align="center" title="时间" data-index="recordTime"/>
        <a-table-column align="center" title="班次" data-index="shift"/>
        <a-table-column align="center" title="型号" data-index="model"/>
        <a-table-column align="center" title="实验类型" data-index="experimentType" :custom-render="renderExperimentType"/>
        <a-table-column align="center" title="生产单号" data-index="productionOrderNum"/>
        <a-table-column align="center" title="缸号" data-index="cylinderNum"/>
        <a-table-column align="center" title="缸数" data-index="cylinderCount"/>
        <a-table-column align="center" title="数量(Kg)" data-index="quantityKg"/>
        <a-table-column-group title="导电胶固含量(%)">
          <a-table-column align="center" title="标准" data-index="condAdhSlctyStdRng"/>
          <a-table-column align="center" title="实际测试" data-index="condAdhSlctyActual"/>
        </a-table-column-group>
        <a-table-column-group title="导电胶细度(μm)">
          <a-table-column align="center" title="标准" data-index="condAdhFinenessStdRng"/>
          <a-table-column align="center" title="实际测试" data-index="condAdhFinenessActual"/>
        </a-table-column-group>
        <a-table-column-group title="PVDF胶粘度(cp)">
          <a-table-column align="center" title="标准" data-index="pvdfViscosityStdRng"/>
          <a-table-column align="center" title="实际测试" data-index="pvdfViscosityActual"/>
        </a-table-column-group>
        <a-table-column-group title="PVDF胶固含量(%)">
          <a-table-column align="center" title="标准" data-index="pvdfSolidContentStdRng"/>
          <a-table-column align="center" title="实际测试" data-index="pvdfSolidContentActual"/>
        </a-table-column-group>
        <a-table-column-group title="浆料粘度(cp)">
          <a-table-column align="center" title="标准" data-index="slurryViscosityRng"/>
          <a-table-column align="center" title="实际测试" data-index="slurryViscosityActual"/>
        </a-table-column-group>
        <a-table-column-group title="浆料固含量(%)">
          <a-table-column align="center" title="标准" data-index="slurrySolidContentRng"/>
          <a-table-column align="center" title="实际测试" data-index="slurrySolidContentActual"/>
        </a-table-column-group>
        <a-table-column-group title="浆料细度(μm)">
          <a-table-column align="center" title="标准" data-index="slurryFinenessRng"/>
          <a-table-column align="center" title="实际测试" data-index="slurryFinenessActual"/>
        </a-table-column-group>
        <a-table-column-group title="浆料磁性物质含量(ppb)">
          <a-table-column align="center" title="标准" data-index="slurryMagneticPpbRng"/>
          <a-table-column align="center" title="实际测试" data-index="slurryMagneticPpbActual"/>
        </a-table-column-group>
        <a-table-column-group title="浆料稳定性-24H浆料粘度(cp)">
          <a-table-column align="center" title="0H" data-index="slurryStability0h"/>
          <a-table-column align="center" title="2H" data-index="slurryStability2h"/>
          <a-table-column align="center" title="4H" data-index="slurryStability4h"/>
          <a-table-column align="center" title="6H" data-index="slurryStability6h"/>
          <a-table-column align="center" title="8H" data-index="slurryStability8h"/>
          <a-table-column align="center" title="10H" data-index="slurryStability10h"/>
          <a-table-column align="center" title="12H" data-index="slurryStability12h"/>
          <a-table-column align="center" title="14H" data-index="slurryStability14h"/>
          <a-table-column align="center" title="16H" data-index="slurryStability16h"/>
          <a-table-column align="center" title="18H" data-index="slurryStability18h"/>
          <a-table-column align="center" title="20H" data-index="slurryStability20h"/>
          <a-table-column align="center" title="22H" data-index="slurryStability22h"/>
          <a-table-column align="center" title="24H" data-index="slurryStability24h"/>
        </a-table-column-group>
        <a-table-column-group title="24H浆料沉降(%)">
          <a-table-column align="center" title="标准" data-index="slurrySettlement24hRng"/>
          <a-table-column align="center" title="实际测试" data-index="slurrySettlement24hActual"/>
        </a-table-column-group>
        <a-table-column-group title="24H上层固含量(%)">
          <a-table-column align="center" title="1" data-index="slurryUpperSolid24hOne"/>
          <a-table-column align="center" title="2" data-index="slurryUpperSolid24hTwo"/>
          <a-table-column align="center" title="3" data-index="slurryUpperSolid24hThree"/>
        </a-table-column-group>
        <a-table-column-group title="24H下层固含量(%)">
          <a-table-column align="center" title="1" data-index="slurryLowerSolid24hOne"/>
          <a-table-column align="center" title="2" data-index="slurryLowerSolid24hTwo"/>
          <a-table-column align="center" title="3" data-index="slurryLowerSolid24hThree"/>
        </a-table-column-group>
        <a-table-column align="center" title="综合判定" data-index="comprehensiveJudgment" :custom-render="renderJudgment"/>
        <a-table-column align="center" title="不良原因" data-index="reasonForFailure"/>
        <a-table-column align="center" title="确认人" data-index="confirmPerson"/>
        <a-table-column align="center" title="审核人" data-index="auditPerson"/>
        <a-table-column align="center" title="状态" data-index="recordStatus" :custom-render="renderRecordStatus"/>
        <a-table-column align="center" title="备注" data-index="remarks"/>
      </a-table>
    </div>
    <a-modal title="新建" :width="1000" :visible="visible" :confirmLoading="confirmLoading" @ok="handleSubmit"
             @cancel="handleCancel">
      <a-spin :spinning="confirmLoading">
        <a-form :form="form">
          <a-form-item class="addFormItem" label="生产单号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input placeholder="请输入生产单号"
                     v-decorator="['productionOrderNum', {rules: [{required: true, message: '请输入生产单号！'}]}]"/>
          </a-form-item>
          <a-form-item class="addFormItem" label="产品型号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input placeholder="请输入产品型号"
                     v-decorator="['model', {rules: [{required: true, message: '请输入产品型号！'}]}]"/>
          </a-form-item>
          <a-form-item class="addFormItem" label="实验类型" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-select :allowClear="true" v-decorator="['experimentType', {rules: [{required: true, message: '请选择实验类型！'}]}]">
              <a-select-option value="0">
                研发验证
              </a-select-option>
              <a-select-option value="1">
                产品验证
              </a-select-option>
              <a-select-option value="2">
                产品鉴定
              </a-select-option>
              <a-select-option value="3">
                客户交付
              </a-select-option>
            </a-select>
          </a-form-item>
              <a-form-item class="addFormItem" label="班次" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                <a-input placeholder="请输入班次"
                         v-decorator="['shift', {rules: [{required: true, message: '请输入班次！'}]}]"/>
              </a-form-item>
              <a-form-item class="addFormItem" label="时间" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                <a-date-picker placeholder="请选择时间" style="width: 100%"
                               :show-time="{ defaultValue: moment(new Date()) }"
                               v-decorator="['recordTime', {rules: [{required: true, message: '请选择时间!'}]}]" />
              </a-form-item>
              <a-form-item class="addFormItem" label="缸号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                <a-input placeholder="请输入缸号"
                         v-decorator="['cylinderNum', {rules: [{required: true, message: '请输入缸号！'}]}]"/>
              </a-form-item>
              <a-form-item class="addFormItem" label="缸数" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                <a-input placeholder="请输入缸数"
                         v-decorator="['cylinderCount', {rules: [{required: true, message: '请输入缸数！'}]}]"/>
              </a-form-item>
              <a-form-item class="addFormItem" label="数量(Kg)" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                <a-input placeholder="请输入数量(Kg)" type="number"
                         v-decorator="['quantityKg', {rules: [{required: true, message: '请输入数量(Kg)！'}]}]"/>
              </a-form-item>
              <a-form-item class="addFormItem" label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                <a-input placeholder="请输入备注"
                         v-decorator="['remarks', {rules: [{required: true, message: '请输入备注！'}]}]"/>
              </a-form-item>
        </a-form>
        <a-table
          id="testDataTable"
          style="margin: 20px;background: #fff;"
          :columns="columns"
          :data-source="testData"
          :row-key="(record) => record.id"
          :pagination="false"
          bordered>
          <div
            slot="standardName"
            slot-scope="text,record">
            <input :value="record.standardName" :disabled="true"/>
          </div>
          <div
            slot="standardRangeName"
            slot-scope="text,record">
            <input style="width: 39%;margin-right: 0.5%" :disabled="record.standardLeftFlag" type="number"
                   @blur="blurEvent($event,record,'standardLeftData')" :value="record.standardLeftData"/>
            <a-select style="width: 21%;" @change="selectStandardRange($event,record)"
                      :value="record.standardRange" v-decorator="['record.standardRange', {rules: [{required: true, message: '请选择标准范围！'}]}]">
              <a-select-option value="≥">
                ≥
              </a-select-option>
              <a-select-option value="≤">
                ≤
              </a-select-option>
              <a-select-option value="±">
                ±
              </a-select-option>
            </a-select>
            <input style="width: 39%;margin-left: 0.5%" :disabled="record.standardRightFlag" type="number"
                   @blur="blurEvent($event,record,'standardRightData')" :value="record.standardRightData"/>
          </div>
        </a-table>
      </a-spin>
    </a-modal>
    <a-modal title="审核" :width="600" :visible="auditVisible" :confirmLoading="aduitLoading" @ok="auditSubmit"
             @cancel="auditCancel">
      <a-spin :spinning="aduitLoading">
        <a-form :form="auditForm">
          <a-form-item class="addFormItem" label="综合判定" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-select v-model="judgment" :allowClear="true" v-decorator="['comprehensiveJudgment', {rules: [{required: true, message: '请选择综合判定！'}]}]">
              <a-select-option value="0">
                合格
              </a-select-option>
              <a-select-option value="1">
                不合格
              </a-select-option>
              <a-select-option value="2">
                特采
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item v-if="judgment === '1'" class="addFormItem" label="不良原因" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input placeholder="请输入不良原因"
                     v-decorator="['reasonForFailure', {rules: [{required: true, message: '请输入不良原因！'}]}]"/>
          </a-form-item>
        </a-form>
      </a-spin>
    </a-modal>
    <a-modal title="测试数据录入"  :width="800" :visible="testRecordVisible" :confirmLoading="testRecordLoading" @ok="testRecordSubmit"
             @cancel="testRecordCancel">
      <a-spin :spinning="testRecordLoading">
        <a-table
          id="testRecordTable"
          style="margin: 20px;background: #fff;height: 515px;overflow-y: auto"
          :columns="recordColumns"
          :data-source="recordData"
          :row-key="(record) => record.id"
          :pagination="false"
          bordered>
          <div
            slot="standardName"
            slot-scope="text,record">
            <input :value="record.standardName" :disabled="true"/>
          </div>
          <div
            slot="standardRangeName"
            slot-scope="text,record">
            <input style="width: 30%;" :disabled="true" :value="record.standardRange"/>
            <input style="width: 69%;margin-left: 1%"  type="number"
                   @blur="blurEvent($event,record,'actualTestResult')" :value="record.actualTestResult"/>
          </div>
        </a-table>
      </a-spin>
    </a-modal>
    <a-modal title="编辑" :width="1000" :visible="editVisible" :confirmLoading="confirmLoading" @ok="editHandleSubmit"
             @cancel="editHandleCancel">
      <a-spin :spinning="confirmLoading">
        <a-form :form="editForm">
          <a-form-item class="addFormItem" label="生产单号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input placeholder="请输入生产单号"
                     v-decorator="['productionOrderNum', {rules: [{required: true, message: '请输入生产单号！'}]}]"/>
          </a-form-item>
          <a-form-item class="addFormItem" label="产品型号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input placeholder="请输入产品型号"
                     v-decorator="['model', {rules: [{required: true, message: '请输入产品型号！'}]}]"/>
          </a-form-item>
          <a-form-item class="addFormItem" label="实验类型" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-select :allowClear="true" v-decorator="['experimentType', {rules: [{required: true, message: '请选择实验类型！'}]}]">
              <a-select-option value="0">
                研发验证
              </a-select-option>
              <a-select-option value="1">
                产品验证
              </a-select-option>
              <a-select-option value="2">
                产品鉴定
              </a-select-option>
              <a-select-option value="3">
                客户交付
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item class="addFormItem" label="班次" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input placeholder="请输入班次"
                     v-decorator="['shift', {rules: [{required: true, message: '请输入班次！'}]}]"/>
          </a-form-item>
          <a-form-item class="addFormItem" label="时间" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-date-picker placeholder="请选择时间" style="width: 100%"
                           :show-time="{ defaultValue: moment(new Date()) }"
                           v-decorator="['recordTime', {rules: [{required: true, message: '请选择时间!'}]}]" />
          </a-form-item>
          <a-form-item class="addFormItem" label="缸号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input placeholder="请输入缸号"
                     v-decorator="['cylinderNum', {rules: [{required: true, message: '请输入缸号！'}]}]"/>
          </a-form-item>
          <a-form-item class="addFormItem" label="缸数" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input placeholder="请输入缸数"
                     v-decorator="['cylinderCount', {rules: [{required: true, message: '请输入缸数！'}]}]"/>
          </a-form-item>
          <a-form-item class="addFormItem" label="数量(Kg)" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input placeholder="请输入数量(Kg)" type="number"
                     v-decorator="['quantityKg', {rules: [{required: true, message: '请输入数量(Kg)！'}]}]"/>
          </a-form-item>
          <a-form-item class="addFormItem" label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input placeholder="请输入备注"
                     v-decorator="['remarks', {rules: [{required: true, message: '请输入备注！'}]}]"/>
          </a-form-item>
        </a-form>
        <a-table
          id="testDataTable"
          style="margin: 20px;background: #fff;"
          :columns="editColumns"
          :data-source="editStandardData"
          :row-key="(record) => record.id"
          :pagination="false"
          bordered>
          <div
            slot="standardName"
            slot-scope="text,record">
            <input :value="record.standardName" :disabled="true"/>
          </div>
          <div
            slot="standardRangeName"
            slot-scope="text,record">
            <input style="width: 39%;margin-right: 0.5%" :disabled="record.standardLeftFlag" type="number"
                   @blur="blurEvent($event,record,'standardLeftData')" :value="record.standardLeftData"/>
            <a-select :default-value="record.standardRange" style="width: 21%;" @change="editSelectStandardRange($event,record)">
              <a-select-option v-for="symbol in symbols" :key="symbol.key">
                {{ symbol.value }}
              </a-select-option>
            </a-select>
            <input style="width: 39%;margin-left: 0.5%" :disabled="record.standardRightFlag" type="number"
                   @blur="blurEvent($event,record,'standardRightData')" v-model="record.standardRightData"/>
          </div>
        </a-table>
      </a-spin>
    </a-modal>
  </div>
</template>
<script>
import {
  getAnodeStirringRecordList,
  insertAnodeStirringRecord,
  updateAnodeStirringRecord
} from '@/api/modular/system/anodeStirringRecordManage'
import moment from "moment/moment";
import Vue from "vue";
import { ACCESS_TOKEN } from "@/store/mutation-types";
import $ from 'jquery';

export default {
  components: {},
  data() {
    return {
      searchLoading: false,
      beforeDeleteFlag: false,
      selectedRowKeys: [],
      editRow: null,
      selectedRows: [],
      symbols: [
        {
          key: '≥',
          value: '≥'
        },
        {
          key: '≤',
          value: '≤'
        },
        {
          key: '±',
          value: '±'
        }],
      queryParam: {
        productionOrderNum: '',
        model: '',
        experimentType: '',
        comprehensiveJudgment: '',
        recordStatus: '',
        beginDate: '',
        endDate: '',
        searchDate: null,
      },
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 8
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      judgment: null,
      visible: false,
      testRecordVisible: false,
      auditVisible: false,
      editVisible: false,
      confirmLoading: false,
      testRecordLoading: false,
      aduitLoading: false,
      form: this.$form.createForm(this, { name: 'form' }),
      editForm: this.$form.createForm(this, { name: 'editForm' }),
      auditForm: this.$form.createForm(this, { name: 'auditForm' }),
      fileList: [],
      uploadFlag: false,
      resultData: [],
      saveData: {},
      columns: [
        {
          title: '标准名称',
          dataIndex: 'standardName',
          scopedSlots: { customRender: 'standardName' },
          align: 'center',
          width: '30%',
        },
        {
          title: '标准',
          dataIndex: 'standardRangeName',
          scopedSlots: { customRender: 'standardRangeName' },
          align: 'center',
          width: '70%',
        },
      ],
      editColumns: [
        {
          title: '标准名称',
          dataIndex: 'standardName',
          scopedSlots: { customRender: 'standardName' },
          align: 'center',
          width: '30%',
        },
        {
          title: '标准',
          dataIndex: 'standardRangeName',
          scopedSlots: { customRender: 'standardRangeName' },
          align: 'center',
          width: '70%',
        },
      ],
      recordColumns: [
        {
          title: '测试项目',
          dataIndex: 'standardName',
          scopedSlots: { customRender: 'standardName' },
          align: 'center',
          width: '30%',
        },
        {
          title: '标准',
          dataIndex: 'standardRangeName',
          scopedSlots: { customRender: 'standardRangeName' },
          align: 'center',
          width: '70%',
        },
      ],
      initTestData: [],
      initTestRecordData: [],
      recordData: [
        {
          standardName: '导电胶固含量(%)',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '导电胶细度(μm)',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: 'PVDF胶粘度(cp)',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: 'PVDF胶固含量(%)',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '浆料粘度(cp)',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '浆料固含量(%)',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '浆料细度(μm)',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '浆料磁性物质含量(ppb)',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '24H浆料沉降(%)',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '浆料稳定性-24H浆料粘度(cp)-0H',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '浆料稳定性-24H浆料粘度(cp)-2H',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '浆料稳定性-24H浆料粘度(cp)-4H',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '浆料稳定性-24H浆料粘度(cp)-6H',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '浆料稳定性-24H浆料粘度(cp)-8H',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '浆料稳定性-24H浆料粘度(cp)-10H',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '浆料稳定性-24H浆料粘度(cp)-12H',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '浆料稳定性-24H浆料粘度(cp)-14H',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '浆料稳定性-24H浆料粘度(cp)-16H',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '浆料稳定性-24H浆料粘度(cp)-18H',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '浆料稳定性-24H浆料粘度(cp)-20H',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '浆料稳定性-24H浆料粘度(cp)-22H',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '浆料稳定性-24H浆料粘度(cp)-24H',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '24H上层固含量(%)-1',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '24H上层固含量(%)-2',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '24H上层固含量(%)-3',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '24H下层固含量(%)-1',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '24H下层固含量(%)-2',
          standardRange: '',
          actualTestResult: '',
        },
        {
          standardName: '24H下层固含量(%)-3',
          standardRange: '',
          actualTestResult: '',
        },
      ],
      editStandardData: [],
      testData: [
        {
          standardName: '导电胶固含量(%)',
          standardLeftData: '',
          standardRange: '',
          standardRightData: '',
          standardRightFlag: true,
          standardLeftFlag: true,
        },
        {
          standardName: '导电胶细度(μm)',
          standardLeftData: '',
          standardRange: '',
          standardRightData: '',
          standardRightFlag: true,
          standardLeftFlag: true,
        },
        {
          standardName: 'PVDF胶粘度(cp)',
          standardLeftData: '',
          standardRange: '',
          standardRightData: '',
          standardRightFlag: true,
          standardLeftFlag: true,
        },
        {
          standardName: 'PVDF胶固含量(%)',
          standardLeftData: '',
          standardRange: '',
          standardRightData: '',
          standardRightFlag: true,
          standardLeftFlag: true,
        },
        {
          standardName: '浆料粘度(cp)',
          standardLeftData: '',
          standardRange: '',
          standardRightData: '',
          standardRightFlag: true,
          standardLeftFlag: true,
        },
        {
          standardName: '浆料固含量(%)',
          standardLeftData: '',
          standardRange: '',
          standardRightData: '',
          standardRightFlag: true,
          standardLeftFlag: true,
        },
        {
          standardName: '浆料细度(μm)',
          standardLeftData: '',
          standardRange: '',
          standardRightData: '',
          standardRightFlag: true,
          standardLeftFlag: true,
        },
        {
          standardName: '浆料磁性物质含量(ppb)',
          standardLeftData: '',
          standardRange: '',
          standardRightData: '',
          standardRightFlag: true,
          standardLeftFlag: true,
        },
        {
          standardName: '24H浆料沉降(%)',
          standardLeftData: '',
          standardRange: '',
          standardRightData: '',
          standardRightFlag: true,
          standardLeftFlag: true,
        },
      ],
      headers: {
        Authorization: 'Bearer ' + Vue.ls.get(ACCESS_TOKEN),
      },
    };
  },
  created() {
     this.initTestData = JSON.parse(JSON.stringify(this.testData))
     this.initTestRecordData = JSON.parse(JSON.stringify(this.recordData))
  },
  mounted() {
    document.getElementsByClassName("ant-layout-content")[0].style.backgroundColor = 'white';
    this.getData({})
  },
  methods: {
    moment,
    blurEvent(event, record, column) {
      record[column] = event.target.value
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    editSelectStandardRange(event, record) {
      // this.editStandardData  editStandardData
      console.log('record',record)
      console.log('event',event)
      console.log('this.editStandardData',this.editStandardData)
      console.log('this.testData',this.testData)
      record['standardRange'] = event
      if (event === '≥') {
        record.standardLeftFlag = true;
        record.standardRightFlag = false;
      } else if (event === '≤') {
        record.standardLeftFlag = true;
        record.standardRightFlag = false;
      } else if (event === '±') {
        record.standardLeftFlag = false;
        record.standardRightFlag = false;
      }
    },
    selectStandardRange(event, record) {
      record['standardRange'] = event
      if (event === '≥') {
        record.standardLeftFlag = true;
        record.standardRightFlag = false;
      } else if (event === '≤') {
        record.standardLeftFlag = true;
        record.standardRightFlag = false;
      } else if (event === '±') {
        record.standardLeftFlag = false;
        record.standardRightFlag = false;
      }
    },
    auditReport() {
      if (this.selectedRows.length !== 1) {
        this.$message.warning('请选择一条数据进行审核！')
        return
      }
      if (this.selectedRows[0].recordStatus !== 1) {
        let itemString = this.selectedRows[0].recordStatus === 0 ? '录入中' : '已审核'
        this.$message.warning('当前数据状态为' + itemString + ',不能审核！')
        return
      }
      this.auditVisible = true
    },
    auditSubmit() {
      const {
        auditForm: {
          validateFields
        }
      } = this
      this.testRecordLoading = true
      let query = this.$route.query
      validateFields((errors, values) => {
        if (!errors) {
          let $params = Object.assign(values, query, { id: this.selectedRows[0].id, recordStatus: 2 });
          updateAnodeStirringRecord($params).then((res) => {
            this.testRecordLoading = false
            if (res.success) {
              setTimeout(() => {
                this.selectedRows = [];
                this.selectedRowKeys = [];
                this.getData({})
                this.auditCancel()
                this.judgment = null
              }, 300);
              this.$message.success('审核成功')
            } else {
              this.$message.error('审核失败：' + res.message)
            }
          }).finally((res) => {
            this.testRecordLoading = false
          })
        } else {
          this.testRecordLoading = false
        }
      })
    },
    reportForExam() {
      if (this.selectedRows.length !== 1) {
        this.$message.warning('请选择一条数据进行报审！')
        return
      }
      if (!this.selectedRows[0].condAdhSlctyActual) {
        this.$message.warning('还未填入测试数据,不能报审!')
        return
      }
      if (this.selectedRows[0].recordStatus !== 0) {
        this.$message.warning('请勿重复报审！')
        return
      }
      updateAnodeStirringRecord({ id: this.selectedRows[0].id, recordStatus: 1 }).then((res) => {
        if (res.success) {
          setTimeout(() => {
            this.selectedRows = [];
            this.selectedRowKeys = [];
            this.getData({})
          }, 300);
          this.$message.success('报审成功！')
        } else {
          this.$message.error('报审失败：' + res.message)
        }
      })
    },
    resetSearch() {
      this.queryParam = {
        productionOrderNum: '',
        model: '',
        experimentType: '',
        comprehensiveJudgment: '',
        recordStatus: '',
        beginDate: '',
        endDate: '',
        searchDate: '',
      }
    },
    searchData() {
      if (this.queryParam.searchDate.length === 2) {
        this.queryParam.beginDate = moment(this.queryParam.searchDate[0]).format('YYYY-MM-DD HH:mm:ss')
        this.queryParam.endDate = moment(this.queryParam.searchDate[1]).format('YYYY-MM-DD HH:mm:ss')
      }
      this.getData(this.queryParam)
    },
    getData(queryParam) {
      this.searchLoading = true
      getAnodeStirringRecordList(queryParam).then(res => {
        this.resultData = res.data
        for (let i = 0; i < this.resultData.length; i++) {
          this.resultData[i].serialNum = i + 1
        }
      }).finally(res => {
        this.searchLoading = false
        this.queryParam.beginDate = ''
        this.queryParam.endDate = ''
      })
    },
    renderIsApproved(text) {
      if (text == 1) {
        return '是'
      } else {
        return '否'
      }
    },
    openAdd() {
      this.visible = true
    },
    beforeDelete() {
      if (this.selectedRows.length !== 1) {
        this.beforeDeleteFlag = true
        this.$message.warning('请选择一条数据进行删除！')
      } else {
        this.beforeDeleteFlag = false
      }
    },
    deleteStandard() {
      updateAnodeStirringRecord({ id: this.selectedRows[0].id, status: 2 }).then((res) => {
        if (res.success) {
          setTimeout(() => {
            this.selectedRows = [];
            this.selectedRowKeys = [];
            this.getData({})
          }, 300);
          this.$message.success('删除成功！')
        } else {
          this.$message.error('删除失败：' + res.message)
        }
      })
    },
    testDataRecord () {
      if (this.selectedRows.length !== 1) {
        this.$message.warning('请选择一条数据进行录入！')
        return
      }
      if (this.selectedRows[0].recordStatus !== 0) {
        this.$message.warning('状态为录入中才能录入测试数据！')
        return
      }
      this.setRecordData()
      this.testRecordVisible = true
    },
    setRecordData() {
      let selectedRecordData = this.selectedRows[0];
      this.recordData[0].standardRange = selectedRecordData.condAdhSlctyStdRng
      this.recordData[0].actualTestResult = selectedRecordData.condAdhSlctyActual
      this.recordData[1].standardRange = selectedRecordData.condAdhFinenessStdRng
      this.recordData[1].actualTestResult = selectedRecordData.condAdhFinenessActual
      this.recordData[2].standardRange = selectedRecordData.pvdfViscosityStdRng
      this.recordData[2].actualTestResult = selectedRecordData.pvdfViscosityActual
      this.recordData[3].standardRange = selectedRecordData.pvdfSolidContentStdRng
      this.recordData[3].actualTestResult = selectedRecordData.pvdfSolidContentActual
      this.recordData[4].standardRange = selectedRecordData.slurryViscosityRng
      this.recordData[4].actualTestResult = selectedRecordData.slurryViscosityActual
      this.recordData[5].standardRange = selectedRecordData.slurrySolidContentRng
      this.recordData[5].actualTestResult = selectedRecordData.slurrySolidContentActual
      this.recordData[6].standardRange = selectedRecordData.slurryFinenessRng
      this.recordData[6].actualTestResult = selectedRecordData.slurryFinenessActual
      this.recordData[7].standardRange = selectedRecordData.slurryMagneticPpbRng
      this.recordData[7].actualTestResult = selectedRecordData.slurryMagneticPpbActual
      this.recordData[8].standardRange = selectedRecordData.slurrySettlement24hRng
      this.recordData[8].actualTestResult = selectedRecordData.slurrySettlement24hActual
      this.recordData[9].actualTestResult = selectedRecordData.slurryStability0h
      this.recordData[10].actualTestResult = selectedRecordData.slurryStability2h
      this.recordData[11].actualTestResult = selectedRecordData.slurryStability4h
      this.recordData[12].actualTestResult = selectedRecordData.slurryStability6h
      this.recordData[13].actualTestResult = selectedRecordData.slurryStability8h
      this.recordData[14].actualTestResult = selectedRecordData.slurryStability10h
      this.recordData[15].actualTestResult = selectedRecordData.slurryStability12h
      this.recordData[16].actualTestResult = selectedRecordData.slurryStability14h
      this.recordData[17].actualTestResult = selectedRecordData.slurryStability16h
      this.recordData[18].actualTestResult = selectedRecordData.slurryStability18h
      this.recordData[19].actualTestResult = selectedRecordData.slurryStability20h
      this.recordData[20].actualTestResult = selectedRecordData.slurryStability22h
      this.recordData[21].actualTestResult = selectedRecordData.slurryStability24h
      this.recordData[22].actualTestResult = selectedRecordData.slurryUpperSolid24hOne
      this.recordData[23].actualTestResult = selectedRecordData.slurryUpperSolid24hTwo
      this.recordData[24].actualTestResult = selectedRecordData.slurryUpperSolid24hThree
      this.recordData[25].actualTestResult = selectedRecordData.slurryLowerSolid24hOne
      this.recordData[26].actualTestResult = selectedRecordData.slurryLowerSolid24hTwo
      this.recordData[27].actualTestResult = selectedRecordData.slurryLowerSolid24hThree
    },
    // updateRecordData() {
    //   this.selectedRows[0].condAdhSlctyStdRng = this.recordData[0].standardRange
    //   this.selectedRows[0].condAdhSlctyActual = this.recordData[0].actualTestResult
    //   this.selectedRows[0].condAdhFinenessStdRng = this.recordData[1].standardRange
    //   this.selectedRows[0].condAdhFinenessActual = this.recordData[1].actualTestResult
    //   this.selectedRows[0].pvdfViscosityStdRng = this.recordData[2].standardRange
    //   this.selectedRows[0].pvdfViscosityActual = this.recordData[2].actualTestResult
    //   this.selectedRows[0].pvdfSolidContentStdRng = this.recordData[3].standardRange
    //   this.selectedRows[0].pvdfSolidContentActual = this.recordData[3].actualTestResult
    //   this.selectedRows[0].slurryViscosityRng = this.recordData[4].standardRange
    //   this.selectedRows[0].slurryViscosityActual = this.recordData[4].actualTestResult
    //   this.selectedRows[0].slurrySolidContentRng = this.recordData[5].standardRange
    //   this.selectedRows[0].slurrySolidContentActual = this.recordData[5].actualTestResult
    //   this.selectedRows[0].slurryFinenessRng = this.recordData[6].standardRange
    //   this.selectedRows[0].slurryFinenessActual = this.recordData[6].actualTestResult
    //   this.selectedRows[0].slurryMagneticPpbRng = this.recordData[7].standardRange
    //   this.selectedRows[0].slurryMagneticPpbActual = this.recordData[7].actualTestResult
    //   this.selectedRows[0].slurrySettlement24hRng = this.recordData[8].standardRange
    //   this.selectedRows[0].slurrySettlement24hActual = this.recordData[8].actualTestResult
    //   this.selectedRows[0].slurryStability0h = this.recordData[9].actualTestResult
    //   this.selectedRows[0].slurryStability2h = this.recordData[10].actualTestResult
    //   this.selectedRows[0].slurryStability4h = this.recordData[11].actualTestResult
    //   this.selectedRows[0].slurryStability6h = this.recordData[12].actualTestResult
    //   this.selectedRows[0].slurryStability8h = this.recordData[13].actualTestResult
    //   this.selectedRows[0].slurryStability10h = this.recordData[14].actualTestResult
    //   this.selectedRows[0].slurryStability12h = this.recordData[15].actualTestResult
    //   this.selectedRows[0].slurryStability14h = this.recordData[16].actualTestResult
    //   this.selectedRows[0].slurryStability16h = this.recordData[17].actualTestResult
    //   this.selectedRows[0].slurryStability18h = this.recordData[18].actualTestResult
    //   this.selectedRows[0].slurryStability20h = this.recordData[19].actualTestResult
    //   this.selectedRows[0].slurryStability22h = this.recordData[20].actualTestResult
    //   this.selectedRows[0].slurryStability24h = this.recordData[21].actualTestResult
    //   this.selectedRows[0].slurryUpperSolid24hOne = this.recordData[22].actualTestResult
    //   this.selectedRows[0].slurryUpperSolid24hTwo = this.recordData[23].actualTestResult
    //   this.selectedRows[0].slurryUpperSolid24hThree = this.recordData[24].actualTestResult
    //   this.selectedRows[0].slurryLowerSolid24hOne = this.recordData[25].actualTestResult
    //   this.selectedRows[0].slurryLowerSolid24hTwo = this.recordData[26].actualTestResult
    //   this.selectedRows[0].slurryLowerSolid24hThree = this.recordData[27].actualTestResult
    // },
    testRecordCancel () {
      this.recordData = JSON.parse(JSON.stringify(this.initTestRecordData))
      this.testRecordVisible = false
    },
    auditCancel() {
      this.auditForm.resetFields()
      this.auditVisible = false
    },
    editHandleCancel() {
      this.editForm.resetFields(['xxx'])
      this.editForm.resetFields()
      this.editVisible = false
    },
    handleCancel() {
      this.form.resetFields()
      this.testData = JSON.parse(JSON.stringify(this.initTestData))
      this.saveData = {}
      this.visible = false
    },
    validateStandardRecord(StandardRecord) {
      for (let prop in StandardRecord) {
        if (!StandardRecord[prop]) {
          this.$message.warning('请输入全部测试数据！')
          return false
        }
      }
      return true
      // if (!StandardRecord.condAdhSlctyActual) {
      //   this.$message.warning('请输入导电胶固含量实际测试值')
      //   return false
      // }
      // if (!StandardRecord.condAdhFinenessActual) {
      //   this.$message.warning('请输入导电胶细度实际测试值')
      //   return false
      // }
      // if (!StandardRecord.pvdfViscosityActual) {
      //   this.$message.warning('请输入PVDF胶粘度实际测试值')
      //   return false
      // }
      // if (!StandardRecord.pvdfSolidContentActual) {
      //   this.$message.warning('请输入PVDF胶固含量实际测试值')
      //   return false
      // }
      // if (!StandardRecord.slurryViscosityActual) {
      //   this.$message.warning('浆料粘度实际测试值')
      //   return false
      // }
      // if (!StandardRecord.slurrySolidContentActual) {
      //   this.$message.warning('浆料固含量实际测试值')
      //   return false
      // }
      // if (!StandardRecord.slurryFinenessActual) {
      //   this.$message.warning('浆料细度实际测试值')
      //   return false
      // }
      // if (!StandardRecord.slurryMagneticPpbActual) {
      //   this.$message.warning('浆料磁性物质含量实际测试值')
      //   return false
      // }
      // if (!StandardRecord.slurrySettlement24hActual) {
      //   this.$message.warning('24H浆料沉降实际测试值')
      //   return false
      // }
      // return true
    },
    validateStandardRange(standardRange) {
      if (standardRange.condAdhSlctyStdRng.length <= 1) {
        this.$message.warning('请输入导电胶固含量(%)标准')
        return false
      }
      if (standardRange.condAdhFinenessStdRng.length <= 1) {
        this.$message.warning('请输入导电胶细度(μm)标准')
        return false
      }
      if (standardRange.pvdfViscosityStdRng.length <= 1) {
        this.$message.warning('请输入PVDF胶粘度(cp)标准')
        return false
      }
      if (standardRange.pvdfSolidContentStdRng.length <= 1) {
        this.$message.warning('请输入PVDF胶固含量(%)标准')
        return false
      }
      if (standardRange.slurryViscosityRng.length <= 1) {
        this.$message.warning('请输入浆料粘度(cp)标准')
        return false
      }
      if (standardRange.slurrySolidContentRng.length <= 1) {
        this.$message.warning('请输入浆料固含量(%)标准')
        return false
      }
      if (standardRange.slurryFinenessRng.length <= 1) {
        this.$message.warning('请输入浆料细度(μm)标准')
        return false
      }
      if (standardRange.slurryMagneticPpbRng.length <= 1) {
        this.$message.warning('请输入浆料磁性物质含量(ppb)标准')
        return false
      }
      if (standardRange.slurrySettlement24hRng.length <= 1) {
        this.$message.warning('请输入24H浆料沉降(%)标准')
        return false
      }
      return true
    },
    testRecordSubmit () {
      const standardRecordResult = {
        id: this.selectedRows[0].id,
        condAdhSlctyActual: this.recordData[0].actualTestResult,
        condAdhFinenessActual: this.recordData[1].actualTestResult,
        pvdfViscosityActual: this.recordData[2].actualTestResult,
        pvdfSolidContentActual: this.recordData[3].actualTestResult,
        slurryViscosityActual: this.recordData[4].actualTestResult,
        slurrySolidContentActual: this.recordData[5].actualTestResult,
        slurryFinenessActual: this.recordData[6].actualTestResult,
        slurryMagneticPpbActual: this.recordData[7].actualTestResult,
        slurrySettlement24hActual: this.recordData[8].actualTestResult,
        slurryStability0h: this.recordData[9].actualTestResult,
        slurryStability2h: this.recordData[10].actualTestResult,
        slurryStability4h: this.recordData[11].actualTestResult,
        slurryStability6h: this.recordData[12].actualTestResult,
        slurryStability8h: this.recordData[13].actualTestResult,
        slurryStability10h: this.recordData[14].actualTestResult,
        slurryStability12h: this.recordData[15].actualTestResult,
        slurryStability14h: this.recordData[16].actualTestResult,
        slurryStability16h: this.recordData[17].actualTestResult,
        slurryStability18h: this.recordData[18].actualTestResult,
        slurryStability20h: this.recordData[19].actualTestResult,
        slurryStability22h: this.recordData[20].actualTestResult,
        slurryStability24h: this.recordData[21].actualTestResult,
        slurryUpperSolid24hOne: this.recordData[22].actualTestResult,
        slurryUpperSolid24hTwo: this.recordData[23].actualTestResult,
        slurryUpperSolid24hThree: this.recordData[24].actualTestResult,
        slurryLowerSolid24hOne: this.recordData[25].actualTestResult,
        slurryLowerSolid24hTwo: this.recordData[26].actualTestResult,
        slurryLowerSolid24hThree: this.recordData[27].actualTestResult,
      }
      if (!this.validateStandardRecord(standardRecordResult)) {
        return
      }
      this.testRecordLoading = true
      let $params = Object.assign({}, standardRecordResult);
      console.log('$params:',$params)
      updateAnodeStirringRecord($params).then((res) => {
        this.testRecordLoading = false
        if (res.success) {
          setTimeout(() => {
            this.selectedRows = [];
            this.selectedRowKeys = [];
            this.getData({})
            this.testRecordCancel()
          }, 300);
          this.$message.success('测试数据录入成功！')
          // this.updateRecordData()
        } else {
          this.$message.error('测试数据录入失败：' + res.message)
        }
      }).finally((res) => {
        this.testRecordLoading = false
      })
    },
    handleSubmit() {
      const {
        form: {
          validateFields
        }
      } = this
      const standardTestResult = {
        condAdhSlctyStdRng: this.testData[0].standardLeftData + this.testData[0].standardRange + this.testData[0].standardRightData + '',
        condAdhFinenessStdRng: this.testData[1].standardLeftData + this.testData[1].standardRange + this.testData[1].standardRightData + '',
        pvdfViscosityStdRng: this.testData[2].standardLeftData + this.testData[2].standardRange + this.testData[2].standardRightData + '',
        pvdfSolidContentStdRng: this.testData[3].standardLeftData + this.testData[3].standardRange + this.testData[3].standardRightData + '',
        slurryViscosityRng: this.testData[4].standardLeftData + this.testData[4].standardRange + this.testData[4].standardRightData + '',
        slurrySolidContentRng: this.testData[5].standardLeftData + this.testData[5].standardRange + this.testData[5].standardRightData + '',
        slurryFinenessRng: this.testData[6].standardLeftData + this.testData[6].standardRange + this.testData[6].standardRightData + '',
        slurryMagneticPpbRng: this.testData[7].standardLeftData + this.testData[7].standardRange + this.testData[7].standardRightData + '',
        slurrySettlement24hRng: this.testData[8].standardLeftData + this.testData[8].standardRange + this.testData[8].standardRightData + '',
      }
      if (!this.validateStandardRange(standardTestResult)) {
        console.log('standardTestResult:',standardTestResult)
        return
      }
      this.confirmLoading = true
      let query = this.$route.query
      validateFields((errors, values) => {
        if (!errors) {
          let $params = Object.assign(values, query);
          this.saveData.recordTime = moment(this.saveData.recordTime).format('YYYY-MM-DD HH:mm:ss')
          $params = Object.assign($params, this.saveData);
          $params = Object.assign($params, standardTestResult);
          console.log('$params:',$params)
          insertAnodeStirringRecord($params).then((res) => {
            this.confirmLoading = false
            if (res.success) {
              this.$message.success('新增成功')
              this.getData({})
              this.handleCancel()
            } else {
              this.$message.error('新增失败：' + res.message)
            }
          }).finally((res) => {
            this.confirmLoading = false
          })
        } else {
          this.confirmLoading = false
        }
      })
    },
    renderJudgment(text) {
      if (text === 0) {
        return '合格'
      } else if (text === 1) {
        return '不合格'
      } else if (text === 2) {
        return '特采'
      } else {
        return ''
      }
    },
    renderExperimentType(text) {
      if (text === 0 || text === '0') {
        return '研发验证'
      } else if (text === 1 || text === '1') {
        return '产品验证'
      } else if (text === 2 || text === '2') {
        return '产品鉴定'
      } else if (text === 3 || text === '3') {
        return '客户交付'
      } else {
        return ''
      }
    },
    renderExperimentName(text) {
      if (text === '研发验证' || text === '0') {
        return 0
      } else if (text === '产品验证' || text === '1') {
        return 1
      } else if (text === '产品鉴定' || text === '2') {
        return 2
      } else if (text === '客户交付' || text === '3') {
        return 3
      } else {
        return ''
      }
    },
    renderRecordStatus(text) {
      if (text === 0) {
        return '录入中'
      } else if (text === 1) {
        return '待审核'
      } else if (text === 2) {
        return '已审核'
      } else {
        return ''
      }
    },
    openEdit() {
      if (this.selectedRows.length !== 1) {
        this.$message.warning('请选择一条数据进行编辑！')
        return
      }
      if (this.selectedRows[0].recordStatus !== 0) {
        this.$message.warning('状态为录入中才能进行编辑！')
        return
      }
      this.editVisible = true
      setTimeout(() => {
        this.editRow = Object.assign({}, this.selectedRows[0]);
        this.editRow.experimentType = this.renderExperimentType(this.editRow.experimentType)
        this.editForm.setFieldsValue(this.editRow)
        this.editStandardData = Object.assign([], this.initTestData);
        this.editStandardData = JSON.parse(JSON.stringify(this.assemblyData(this.selectedRows[0], this.editStandardData)))
      }, 100)
    },
    editHandleSubmit() {
      const {
        editForm: {
          validateFields
        }
      } = this
      // this.confirmLoading = true
      validateFields((errors, values) => {
        if (!errors) {
          let $params = Object.assign({}, values);
          $params.experimentType = this.renderExperimentName($params.experimentType)
          console.log('values', values)
          console.log('$params', $params)
          // updateAnodeStirringRecord(values).then((res) => {
          //   this.confirmLoading = false
          //   if (res.success) {
          //     this.$message.success('编辑成功')
          //     this.getData({})
          //     this.editHandleCancel()
          //   } else {
          //     this.$message.error('编辑失败：' + res.message)
          //   }
          // }).finally((res) => {
          //   this.confirmLoading = false
          // })
        } else {
          this.confirmLoading = false
        }
      })
    },
    assemblyData(selectedRow, editStandardData) {
      editStandardData[0] = this.handleStandardRange(selectedRow.condAdhSlctyStdRng,'导电胶固含量(%)');
      editStandardData[1] = this.handleStandardRange(selectedRow.condAdhFinenessStdRng,'导电胶细度(μm)');
      editStandardData[2] = this.handleStandardRange(selectedRow.pvdfViscosityStdRng,'PVDF胶粘度(cp)');
      editStandardData[3] = this.handleStandardRange(selectedRow.pvdfSolidContentStdRng,'PVDF胶固含量(%)');
      editStandardData[4] = this.handleStandardRange(selectedRow.slurryViscosityRng,'浆料粘度(cp)');
      editStandardData[5] = this.handleStandardRange(selectedRow.slurrySolidContentRng,'浆料固含量(%)');
      editStandardData[6] = this.handleStandardRange(selectedRow.slurryFinenessRng,'浆料细度(μm)');
      editStandardData[7] = this.handleStandardRange(selectedRow.slurryMagneticPpbRng,'浆料磁性物质含量(ppb)');
      editStandardData[8] = this.handleStandardRange(selectedRow.slurrySettlement24hRng,'24H浆料沉降(%)');
      return editStandardData
    },
    handleStandardRange(range,name) {
      let result
      let data = {
        standardName: name,
        standardLeftData: '',
        standardRange: '',
        standardRightData: '',
        standardRightFlag: false,
        standardLeftFlag: false
      }
      if (range.includes('±')) {
        data.standardRange = '±'
        result = range.split('±')
      } else if (range.includes('≥')) {
        data.standardRange = '≥'
        data.standardLeftFlag = true
        result = range.split('≥')
      } else if (range.includes('≤')) {
        data.standardRange = '≤'
        data.standardLeftFlag = true
        result = range.split('≤')
      }
      data.standardLeftData = result[0]
      data.standardRightData = result[1]
      return data
    },
  }
}
</script>
<style lang="less">
.ant-table-bordered.ant-table-empty .ant-table-placeholder {
  border: 1px solid black;
}

.ant-table-thead > tr > th, .ant-table-tbody > tr > td {
  padding: 5px 5px;
  overflow-wrap: break-word;
}

input {
  width: 100%;
  height: 30px;
  margin: 0;
  //border: 1px;
  outline: none;
  text-align: center;
}
</style>
<style scoped>
.addFormItem {
  display: inline-block;
  width: 420px
}
.ant-form-item {
  margin-bottom: 0px;
}

/deep/ .ant-table-thead > tr:nth-child(1) > th {
  background: #D9D9D9;
  padding: 12px 8px;
  font-weight: bold;
  border: 1px solid black;
}

/deep/ .ant-table-thead > tr:nth-child(2) > th {
  background: #D9D9D9;
  padding: 12px 8px;
  font-weight: bold;
  border: 1px solid black;
}

/deep/ .ant-table-bordered .ant-table-tbody > tr > td {
  border: 1px solid black;
}

/deep/ .ant-table-bordered .ant-table-body > table {
  border-collapse: collapse;
  border-spacing: 0;
}
</style>
