import { axios } from '@/utils/request'

export function getMIStandardLibImpList (parameter) {
  return axios({
    url: '/miStandardLibImp/getMIStandardLibImpList/asc',
    method: 'get',
    params: parameter
  })
}

export function getMIStandardLibImpListDesc (parameter) {
  return axios({
    url: '/miStandardLibImp/getMIStandardLibImpList/desc',
    method: 'get',
    params: parameter
  })
}

export function getMIStandardLibCurById (parameter, impBatteryId) {
  return axios({
    url: '/miStandardLibCur/getMIStandardLibCurById/' + impBatteryId,
    method: 'get',
    params: parameter
  })
}

// export function insertMIStandardLibImp (parameter) {
//   return axios({
//     url: '/miStandardLibImp/insertMIStandardLibImp',
//     method: 'post',
//     data: parameter
//   })
// }

export function updateMIStandardLibCur (parameter) {
  return axios({
    url: '/miStandardLibCur/updateMIStandardLibCur',
    method: 'post',
    data: parameter
  })
}

// export function copyMIStandardLibImp (parameter) {
//   return axios({
//     url: '/miStandardLibImp/copyMIStandardLibImp',
//     method: 'post',
//     data: parameter
//   })
// }

export function deleteMIStandardLibImpFile(parameter) {
  return axios({
    url: '/miStandardLibImp/deleteMIStandardLibImpFile',
    method: 'post',
    data: parameter
  })
}
