import request from './request';

/**
 * 解析LaTeX公式
 * @param {Object} latexFormula - 包含主公式和子公式的对象
 * @param {string} latexFormula.main_formula - 主公式
 * @param {Array} latexFormula.sub_formulas - 子公式列表
 * @param {Array} independentParams - 独立参数
 * @returns {Promise} axios响应对象
 */
export const parseLatexFormula = (latexFormula, independentParams = []) => {
  // 确保参数是数组
  const params = Array.isArray(independentParams) ? independentParams : [];

  // 将简单的字符串数组转换为包含元组的数组格式
  const formattedParams = params.map(param => {
    // 如果参数已经是元组格式，直接使用
    if (Array.isArray(param) && param.length === 2) {
      return param;
    }
    // 否则，将单个字符串转换为元组 [变量名, 描述]，描述为空字符串
    return [param, ""];
  });

  // 确保latexFormula是对象
  const latex = typeof latexFormula === 'object' ? latexFormula : {
    main_formula: String(latexFormula),
    sub_formulas: []
  };

  return request.post('/api/parse-latex-formula', {
    latex_str: latex,
    independent_params: formattedParams,
  });
};

/**
 * 保存公式
 * @param {Object} formula - 公式对象
 * @returns {Promise} axios响应对象
 */
export const saveFormula = (formula) => {
  // 调试输出原始参数
  // console.log('保存公式 - 原始参数:', JSON.stringify(formula.params.filter(p => p.type === 'coefficient').slice(0, 5), null, 2));

  // 确保params中包含describe字段并且正确处理值
  const formattedParams = formula.params.map(param => {
    // 确保参数值不丢失，对于coefficient类型特别处理
    let paramValue = param.value;
    // 针对非空值的检查
    if (param.type === 'coefficient' && param.customValue !== undefined) {
      paramValue = param.customValue;
      console.log(`系数 ${param.name}: 使用customValue=${paramValue}`);
    } else if (param.type === 'coefficient') {
      console.log(`系数 ${param.name}: 使用value=${paramValue}`);
    }

    // 数值类型确保是数字而不是字符串，并保留小数点后十位
    if (paramValue !== null) {
      if (typeof paramValue === 'string' && !isNaN(paramValue)) {
        paramValue = parseFloat(paramValue);
      }

      // 对数值类型的参数保留小数点后十位
      if (typeof paramValue === 'number' && !isNaN(paramValue)) {
        paramValue = parseFloat(paramValue.toFixed(10));
      }
    }

    return {
      name: param.name,
      type: param.type,
      value: paramValue,
      describe: param.describe || null
    };
  });

  // 调试输出处理后的参数
  formattedParams.filter(p => p.type === 'coefficient').slice(0, 5).forEach(p => {
    console.log(`${p.name} = ${p.value} (${typeof p.value})`);
  });

  return request.post('/api/save-formula', {
    latex: formula.latex,
    params: formattedParams,
    formula_id: formula.id,
    formula_description: formula.description,
    variable_description: formula.variableDescription
  });
};

/**
 * 查询已保存的公式
 * @param {string} formulaId - 公式ID
 * @returns {Promise} axios响应对象
 */
export const queryFormula = (formulaId) => {
  return request.post('/api/query-formula', {
    formula_id: formulaId
  });
}

/**
 * 删除已保存的公式
 * @param {string} formulaId - 公式ID
 * @returns {Promise} axios响应对象
 */
export const deleteFormula = (formulaId) => {
  return request.post('/api/delete-formula', {
    formula_id: formulaId
  });
}

/**
 * 获取所有保存的模型列表
 * @returns {Promise} axios响应对象
 */
export const getModels = () => {
  return request.get('/api/get-models');
}