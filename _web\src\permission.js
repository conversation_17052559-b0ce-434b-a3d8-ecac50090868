import Vue from 'vue'
import router from './router'
import store from './store'
import { login, getLoginUser, logout, login2 } from '@/api/modular/system/loginManage'

import NProgress from 'nprogress' // progress bar
import '@/components/NProgress/nprogress.less' // progress bar custom style
import { setDocumentTitle, domTitle } from '@/utils/domUtil'
import { ACCESS_TOKEN, ALL_APPS_MENU, DICT_TYPE_TREE_DATA } from '@/store/mutation-types'

import { Modal, notification } from 'ant-design-vue' // NProgress Configuration
import { timeFix, getQueryVariable } from '@/utils/util'/// es/notification

import { sysDictTypeTree } from "@/api/modular/system/dictManage";
NProgress.configure({ showSpinner: false })
const whiteList = ['login', 'login_battery', 'register', 'registerResult'] // no redirect whitelist
// 无默认首页的情况
const defaultRoutePath = '/'

router.beforeEach((to, from, next) => {
  NProgress.start() // start progress bar
  to.meta && (typeof to.meta.title !== 'undefined' && setDocumentTitle(`${to.meta.title} - ${domTitle}`))
  if (Vue.ls.get(ACCESS_TOKEN)) {
    /* has token */
    if (to.path === '/user/login') {
      let redirect = to.query.redirect

      if (redirect) {

        let $params = getQueryVariable(redirect)

        if (JSON.stringify($params) === "{}") {
          next({ path: redirect })
        } else {
          for (const key in to.query) {
            if (key == 'redirect' || key == 'name') {
              continue
            }
            $params[key] = to.query[key]
          }

          let $redirect = redirect.split('?')[0]
          next({ path: $redirect, query: $params })
        }

      } else {
        next({ path: defaultRoutePath })
      }

      NProgress.done()
    } else {
      if (store.getters.roles.length === 0) {
        store
          .dispatch('GetInfo')
          .then(res => {
            if (res.menus.length < 1) {
              Modal.error({
                title: '提示：',
                content: '无菜单权限，请联系蔡嘉伟',
                okText: '确定',
                onOk: () => {
                  store.dispatch('Logout').then(() => {
                    window.location.reload()
                  })
                }
              })
              return
            }
            // eslint-disable-next-line camelcase
            const all_app_menu = Vue.ls.get(ALL_APPS_MENU)
            let antDesignmenus
            // eslint-disable-next-line camelcase
            if (all_app_menu == null) {
              const applocation = []
              antDesignmenus = res.menus
              const redirect = decodeURIComponent(from.query.redirect || to.path)
              let activeMenu = res.menus.find(e => redirect.indexOf(e.path) > -1)
              res.apps.forEach(item => {
                const apps = { 'code': '', 'name': '', 'active': '', 'menu': '' }
                apps.code = item.code
                apps.name = item.name
                apps.active = false
                apps.menu = item.menus
                applocation.push(apps)
              })

              let $i = applocation.findIndex(e => e.code == (activeMenu ? activeMenu.meta.code : ''))
              if ($i > -1) {
                applocation[$i].active = true
              }
              Vue.ls.set(ALL_APPS_MENU, applocation, 7 * 24 * 60 * 60 * 1000)
              // 延迟 1 秒显示欢迎信息
              setTimeout(() => {
                notification.success({
                  message: '欢迎',
                  description: `${timeFix()}，欢迎回来`
                })
              }, 1000)
            } else {


              // 判断是否从jira跳转
              if (window.location.href.indexOf('appCode=') !== -1) {
                let params = new URL(location.href).searchParams;
                const appCode = params.get('appCode')
                all_app_menu.forEach(v => {
                  v.active = v.code == appCode ? true : false
                })
                Vue.ls.set(ALL_APPS_MENU, all_app_menu)
              }

              let apps = all_app_menu
              let menus = []
              for (const item of apps) {

                if (item.menu) {
                  menus.push(...item.menu)
                }
              }
              antDesignmenus = menus
            }
            store.dispatch('GenerateRoutes', { antDesignmenus }).then(() => {
              // 动态添加可访问路由表
              router.addRoutes(store.getters.addRouters)
              // 请求带有 redirect 重定向时，登录自动重定向到该地址
              const redirect = decodeURIComponent(from.query.redirect || to.path)

              if (to.path === redirect) {
                next({ path: redirect })
                // hack方法 确保addRoutes已完成 ,set the replace: true so the navigation will not leave a history record
                next({ ...to, replace: true })
              } else {
                let $params = getQueryVariable(redirect)

                if (JSON.stringify($params) === "{}") {
                  next({ path: redirect })
                } else {
                  for (const key in from.query) {
                    if (key == 'redirect' || key == 'name') {
                      continue
                    }
                    $params[key] = from.query[key]
                  }
                  let $redirect = redirect.split('?')[0]
                  next({ path: $redirect, query: $params })
                }
                // 跳转到目的路由
                //next({ path: redirect })
              }
            })
          })
          .catch((e) => {
            store.dispatch('Logout').then(() => {
              next({ path: '/user/login', query: { redirect: to.fullPath } })
            })
          })
      } else {
        /* let apps = Vue.ls.get(ALL_APPS_MENU)
        let activeMenu
        for (const item of apps) {
          if (item.menu) {
            activeMenu = item.menu.find(e=> to.path.indexOf(e.path) > -1)
            if (activeMenu) {
              break
            }
          }
        }
        
        const $i = apps.findIndex(e=>e.code == (activeMenu ? activeMenu.meta.code : ''))
        if ($i > -1) {
          if (!apps[$i].active) {
            apps[$i].active = true
            const $j = apps.findIndex(e=>e.active == true)
            if ($j > -1) {
              apps[$j].active = false
            }
            Vue.ls.set(ALL_APPS_MENU, apps)
            let antDesignmenus = apps.map((item)=>{
              if (item.menu) {
                return item.menu
              }
            })
            store.dispatch('GenerateRoutes', { antDesignmenus }).then(() => {
              router.addRoutes(store.getters.addRouters)
            })
          }
        } */
        next()
      }
    }
  } else {

    if (to.path.indexOf('/batteryUser/login_battery') > -1) {
      next()
    } else if (to.path.indexOf('/batterydesign') > -1) {
      next({ path: '/batteryUser/login_battery', query: { redirect: to.fullPath } })
    }

    else if (whiteList.includes(to.name)) {
      // 在免登录白名单，直接进入
      next()
    } /*else if (to.path.indexOf('/product_test') > -1 && to.query != null && to.query.type == 'lims') {

      // 使用 Base64 解码进行解密
      const decodedData = new TextDecoder().decode(Uint8Array.from(atob(to.query.id), c => c.charCodeAt(0)));
      console.log('Decoded:', decodedData);


      login2({ account: decodedData }).then(res => {
        Vue.ls.set(ACCESS_TOKEN, res.data.jwtToken, 7 * 24 * 60 * 60 * 1000)
        Vue.ls.set("token", res.data.token)
        sysDictTypeTree().then((data) => {
          if (data.success) {
            const result = data.data
            Vue.ls.set(DICT_TYPE_TREE_DATA, result)
          }
        })
      }).then(res => {
        next({ path: '/product_test' })
      })

    }*/
    else {
      next({ path: '/user/login', query: { redirect: to.fullPath } })
      NProgress.done() // if current page is login will not trigger afterEach hook, so manually handle it
    }
  }
})

router.afterEach(() => {
  NProgress.done() // finish progress bar
})
