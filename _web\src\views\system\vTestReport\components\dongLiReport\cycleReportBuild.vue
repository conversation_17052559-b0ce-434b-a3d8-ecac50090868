<template>
  <div class="cycle-div">
    <div class="left-content block">
      <div class="flex-sb-center-row">
        <h3>一、测试数据选择</h3>
        <div style="float: right">
          <a-popconfirm placement="topRight" title="确认清空？" @confirm="() => {
            this.cycleReportParam.orderDataList = []
            this.deleteSelectedRowKeys = []
            this.orderDataListChange()
            }">
            <a-button class="mr5">清空</a-button>
          </a-popconfirm>
          <a-popconfirm placement="topRight" title="确认删除？" @confirm="deleteSelect">
            <a-button>删除</a-button>
          </a-popconfirm>
        </div>
      </div>
      <div ref="tableContainer">
        <a-table class="mt10"
               bordered
               id="outTable"
               :columns="orderColumns"
               :data-source="cycleReportParam.orderDataList"
               :row-selection="deleteRowSelection"
               childrenColumnName="child"
               :rowKey="record => record.uuid"
               :pagination="false"
      >
        <template slot="celltestcode" slot-scope="text, record, index, columns">
          <a @click="$refs.orderDataSelectModal.openStepData(record)" v-if="record.flowId != null" style="text-align: center">{{ text }}</a>
          <span v-else style="text-align: center">{{ text }}</span>
        </template>
        <template slot="action" slot-scope="text, record, index, columns">
          <a-tooltip placement="top" title="删除" arrow-point-at-center>
            <a @click="deleteDataOne(record, index)" style="text-align: center">
              <a-icon type="delete" style="font-size: large;margin-right: 3px"/>
            </a>
            <a-tooltip placement="top" title="拖拽修改位置" arrow-point-at-center>
              <a-icon style="color: #1890ff; font-size: large; cursor: move;" heignt="18" width="18" class="drag" type="unordered-list"/>
            </a-tooltip>
          </a-tooltip>
<!--          <a-tooltip placement="top" title="上移" arrow-point-at-center v-if="index != 0">
            <a @click="moveUp(cycleReportParam.orderDataList, index)" style="text-align: center">
              <a-icon type="arrow-up" style="font-size: large;margin-right: 3px"/>
            </a>
          </a-tooltip>
          <a-tooltip placement="top" title="下移" arrow-point-at-center v-if="index != cycleReportParam.orderDataList.length - 1">
            <a @click="moveDown(cycleReportParam.orderDataList, index)" style="text-align: center">
              <a-icon type="arrow-down" style="font-size: large"/>
            </a>
          </a-tooltip>-->
        </template>
        <template slot="dataPath" slot-scope="text, record, index, columns">
          <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
            <template slot="title">
              {{ text ? text : record.testcontent }}
            </template>
            {{ text ? text : record.testcontent }}
          </a-tooltip>
        </template>
        <template slot="footer">
          <div class="footer-btn" :class="{ 'plus-btn': Array.isArray(cycleReportParam.orderDataList) && cycleReportParam.orderDataList.length === 0 }" @click="$refs.orderDataSelectModal.visible = true">
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <a-icon type="plus"></a-icon>
          </div>
        </template>
      </a-table>
      </div>
    </div>

    <div class="right-content ml10">
      <div class="block">
        <div class="flex-sb-center-row">
          <h3>二、基本信息填写</h3>
          <a-icon @click="handleClose('isTempClose')" :type="isTempClose ? 'down' : 'up'"/>
        </div>
        <div v-show="!isTempClose" class="info-row-div">
          <div class="label-span">项目名称 :</div>
          <a-tooltip :title="cycleReportParam.projectName">
            <a-input style="width: 100px; margin-top: 10px;" v-model="cycleReportParam.projectName" @blur="handleBlur"/>
          </a-tooltip>
          <div class="label-span">样品阶段 :</div>
          <a-input style="width: 100px; margin-top: 10px;" v-model="cycleReportParam.phase" @blur="handleBlur"/>
          <div class="label-span">温度 :</div>
          <a-input class="number-input" suffix="℃" v-model="cycleReportParam.temp"
                   @keyup="cycleReportParam.temp = (cycleReportParam.temp + '').replace(/[^0-9.]/g, '')"
                   @blur="handleNumberBlur('temp')"/>
          <div class="label-span">循环机制 :</div>
          <a-tooltip title="示例：可填写0.33C或者3N3F_10min">
            <a-input class="number-input" placeholder="0.33C/3N3F_10min" v-model="cycleReportParam.rate" @blur="handleBlur"/>
          </a-tooltip>
          <div class="info-row-div">
            <div class="label-span">起止SOC :</div>
            <a-input class="number-input" suffix="%" v-model="cycleReportParam.startSoc"
                     @keyup="cycleReportParam.startSoc = (cycleReportParam.startSoc + '').replace(/[^0-9.]/g, '')"
                     @blur="handleNumberBlur('startSoc')"/>
            <span style="margin-top: 10px; margin-left: 4px; margin-right: 4px; display: flex; align-items: center;"> ~ </span>
            <a-input class="number-input" suffix="%" v-model="cycleReportParam.endSoc"
                     @keyup="cycleReportParam.endSoc = (cycleReportParam.endSoc + '').replace(/[^0-9.]/g, '')"
                     @blur="handleNumberBlur('endSoc')"/>
          </div>
        </div>
      </div>

      <div class="block mt10">
        <h3>三、模型生成逻辑</h3>
        <div class="flex-sb-center-row">
          <strong class="mt10">1、Cycle-参数填写</strong>
          <a-icon @click="handleClose('isCycleParamClose')" :type="isCycleParamClose ? 'down' : 'up'"/>
        </div>
        <div v-show="!isCycleParamClose" style="margin-top: 10px; display: flex;">
          <a-popconfirm placement="topRight" title="确认清空？" @confirm="() => {
              this.cycleReportParam.cycleStepParamList = [ {} ]
              this.cycleListDeleteSelectedRowKeys = []
              this.$emit('handleVerify', this._handleVerify())
              }">
            <a-button size="small" class="mr5">清空</a-button>
          </a-popconfirm>
          <a-popconfirm placement="topRight" title="确认删除？" @confirm="deleteSelectedParams('cycle')">
            <a-button size="small">删除</a-button>
          </a-popconfirm>
          <div class="ml10">
            作图使用数据：
            <a-radio-group size="small" v-model="cycleReportParam.mode">
              <a-radio-button value="All"><div class="group-button-div">全部</div></a-radio-button>
              <a-radio-button value="S"><div class="group-button-div">S</div></a-radio-button>
              <a-radio-button value="N"><div class="group-button-div">N</div></a-radio-button>
              <a-radio-button value="F"><div class="group-button-div">F</div></a-radio-button>
            </a-radio-group>
          </div>
        </div>
        <div v-show="!isCycleParamClose" class="mt5">
          <a-table bordered
                   :columns="cycleStepColumns"
                   :data-source="cycleReportParam.cycleStepParamList"
                   :row-selection="{columnWidth: 20, selectedRowKeys: this.cycleListDeleteSelectedRowKeys, onChange: cycleDeleteRowOnChange}"
                   :pagination="false">
            <template slot="action" slot-scope="text, record, index, columns">
              <a v-if="index > 0" @click="deleteParam('cycleStepParamList', index)" style="text-align: center">删除</a>
              <span v-else>删除</span>
            </template>
            <template slot="mode" slot-scope="text, record, index, columns">
              <a-input class="input"
                       v-model="cycleReportParam.cycleStepParamList[index].mode"
                       @keyup="cycleReportParam.cycleStepParamList[index].mode = (cycleReportParam.cycleStepParamList[index].mode + '').replaceAll(/[^SNF]/g, '').charAt(0)"
                       @blur="handleBlur"
                       @paste="copyFromExcel($event, index)"/>
            </template>
            <template slot="groupIndexTitle">
              电芯组别
              <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
                <a-icon type="question-circle" style="color: #1890ff;"/>
                <template slot="title">
								    <span>
								    	1、当一个电芯只有一条数据时，电芯组别可不填写；<br/>
                      2、当一个电芯有多条数据时，电芯组别填写对应编号，并填写对应工步号；<br/>
                      3、当一个电芯有多条数据时，电芯组别不填写，即默认所有组别选择相同工步号
								    </span>
                </template>
              </a-tooltip>
            </template>
            <template slot="groupIndex" slot-scope="text, record, index, columns">
              <a-select style="width: 100%;" allow-clear v-model="cycleReportParam.cycleStepParamList[index].groupIndex" :options="primaryGroupOptions"/>
            </template>
            <template slot="ceStepTitle">
              放电容量&能量工步号
              <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
                <a-icon type="question-circle" style="color: #1890ff;"/>
                <template slot="title">
                    <span>
                      请输入放电容量&能量工步号；<br/>
                      如是多个工步号，需以逗号,隔开，计算加和
                    </span>
                </template>
              </a-tooltip>
            </template>
            <template slot="ceStep" slot-scope="text, record, index, columns">
              <a-input class="input"
                       v-model="cycleReportParam.cycleStepParamList[index].ceStep"
                       @keyup="cycleReportParam.cycleStepParamList[index].ceStep = (cycleReportParam.cycleStepParamList[index].ceStep + '').replaceAll(/[\t\n\s]/g, ',').replaceAll(/[^0-9,，]/g, '').replaceAll(/[，]/g, ',')"
                       @blur="handleBlur('cycleStepParamList', index, 'ceStep')"/>
            </template>
            <template slot="chCeStepTitle">
              充电容量&能量工步号
              <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
                <a-icon type="question-circle" style="color: #1890ff;"/>
                <template slot="title">
                    <span>
                      充电容量&能量工步号非必填；<br/>
                      如是多个工步号，需以逗号,隔开，计算加和
                    </span>
                </template>
              </a-tooltip>
            </template>
            <template slot="chCeStep" slot-scope="text, record, index, columns">
              <a-input class="input"
                       v-model="cycleReportParam.cycleStepParamList[index].chCeStep"
                       @keyup="cycleReportParam.cycleStepParamList[index].chCeStep = (cycleReportParam.cycleStepParamList[index].chCeStep + '').replaceAll(/[\t\n\s]/g, ',').replaceAll(/[^0-9,，]/g, '').replaceAll(/[，]/g, ',')"
                       @blur="handleBlur('cycleStepParamList', index, 'chCeStep')"/>
            </template>
            <template slot="footer">
              <div class="footer-btn" @click="addParam('cycleStepParamList')">
                <span></span>
                <span></span>
                <span></span>
                <span></span>
                <a-icon type="plus"></a-icon>
              </div>
            </template>
          </a-table>
          <div class="mt5">
            <a-tooltip placement="topRight" arrow-point-at-center title="以建模结果的循环号为准">
              保持率基准循环号 <a-icon type="question-circle" style="color: #1890ff;"/> ：
            </a-tooltip>
            <a-input-number size="small" :min="1" v-model="cycleReportParam.standardCycleNum" />
          </div>
        </div>

        <div class="flex-sb-center-row">
          <strong class="mt10">2、RPT(中检)-参数填写</strong>
          <a-icon @click="handleClose('isRptParamClose')" :type="isRptParamClose ? 'down' : 'up'"/>
        </div>
        <div v-show="!isRptParamClose">
          <div class="mt10">①X轴：循环圈数填写</div>
          <div style="width: 188px;">
            <div class="mt10 footer-btn" :class="{ 'plus-btn': cycleReportParam.rptCycleList.length === 0 && (cycleReportParam.cycleStepParamList.length > 1 || cycleReportParam.cycleStepParamList[0].ceStep || cycleReportParam.cycleStepParamList[0].chCeStep || cycleReportParam.cycleStepParamList[0].groupIndex) }"
                 @click="handleOpen('isShowCompute')">
              <span></span>
              <span></span>
              <span></span>
              <span></span>
              <span style="font-size: 14px; color: rgba(0, 0, 0, 0.65); font-weight: 400;">循环规则输入</span>
            </div>
          </div>

          <div class="mt10">②Y轴：工步信息填写</div>
          <div style="margin-top: 10px; width: 100%;">
            <a-popconfirm placement="topRight" title="确认清空？" @confirm="() => {
              this.cycleReportParam.rptStepParamList = [ { dcirStepParamList:[], dchCeStepStrList: [] } ]
              this.rptListDeleteSelectedRowKeys = []
              this.cycleReportParam.dcirTiTleList = ['']
              this.dcirNum = 1
              this.changeRptParamColumns(false)
              }">
              <a-button size="small" class="mr5">清空</a-button>
            </a-popconfirm>
            <a-popconfirm placement="topRight" title="确认删除？" @confirm="deleteSelectedParams('rpt')">
              <a-button size="small">删除</a-button>
            </a-popconfirm>
          </div>
          <a-table class="mt5"
                   bordered
                   :columns="rptParamColumns"
                   :data-source="cycleReportParam.rptStepParamList"
                   :row-selection="{columnWidth: 20, selectedRowKeys: this.rptListDeleteSelectedRowKeys, onChange: rptDeleteRowOnChange}"
                   :pagination="false">
            <template slot="action" slot-scope="text, record, index, columns">
              <a v-if="index > 0" @click="deleteParam('rptStepParamList', index)" style="text-align: center">删除</a>
              <span v-else>删除</span>
            </template>
            <template slot="groupIndexTitle">
              电芯组别
              <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
                <a-icon type="question-circle" style="color: #1890ff;"/>
                <template slot="title">
								    <span>
								    	1、当一个电芯只有一条数据时，电芯组别可不填写；<br/>
                      2、当一个电芯有多条数据时，电芯组别填写对应编号，并填写对应工步号；<br/>
                      3、当一个电芯有多条数据时，电芯组别不填写，即默认所有组别选择相同工步号
								    </span>
                </template>
              </a-tooltip>
            </template>
            <template slot="groupIndex" slot-scope="text, record, index, columns">
              <a-select style="width: 100%;" allow-clear v-model="cycleReportParam.rptStepParamList[index].groupIndex" :options="primaryGroupOptions"/>
            </template>
            <template slot="ceStepTitle">
              <a-input class="input" v-model="cycleReportParam.ceStepTiTle" placeholder="示例：2C倍率" @blur="handleBlur"/>
              放电容量&能量工步号
              <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
                <a-icon type="question-circle" style="color: #1890ff;"/>
                <template slot="title">
                    <span>
                      请输入放电容量&能量工步号；<br/>
                      如是多个工步号，需以逗号,隔开，计算平均值
                    </span>
                </template>
              </a-tooltip>
              <a-tooltip v-if="dchCeStepNum === 0">
                <template slot="title">增加放电容量&能量工步号列</template>
                <a-icon class="ml10" type="plus" style="color: #1890ff;" @click="changedchCeStepStrList(false)"/>
              </a-tooltip>
            </template>
            <template slot="ceStep" slot-scope="text, record, index, columns">
              <a-input class="input"
                       v-model="cycleReportParam.rptStepParamList[index].ceStep"
                       @keyup="cycleReportParam.rptStepParamList[index].ceStep = (cycleReportParam.rptStepParamList[index].ceStep + '').replaceAll(/[\t\n\s]/g, ',').replaceAll(/[^0-9,，]/g, '').replaceAll(/[，]/g, ',')"
                       @blur="handleBlur('rptStepParamList', index, 'ceStep')"/>
            </template>
            <template v-for="(item, i) in dchCeStepIndexArr" :slot="`dchCeStepTitle_${i}`">
              <a-input class="input" v-model="cycleReportParam.dchCeStepTiTleList[i]" placeholder="示例：2C倍率" @blur="handleBlur"/>
              放电容量&能量工步号
              <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
                <a-icon type="question-circle" style="color: #1890ff;"/>
                <template slot="title">
                    <span>
                      请输入放电容量&能量工步号；<br/>
                      如是多个工步号，需以逗号,隔开，计算平均值
                    </span>
                </template>
              </a-tooltip>
              <a-tooltip v-if="i === cycleReportParam.dchCeStepTiTleList.length - 1">
                <template slot="title">增加放电容量&能量工步号列</template>
                <a-icon class="ml10" type="plus" style="color: #1890ff;" @click="changedchCeStepStrList(false)"/>
              </a-tooltip>
              <a-tooltip v-if="i === cycleReportParam.dchCeStepTiTleList.length - 1">
                <template slot="title">删除放电容量&能量工步号列</template>
                <a-icon class="ml10" type="minus" style="color: #1890ff;" @click="changedchCeStepStrList(true)"/>
              </a-tooltip>
            </template>
            <template slot="dchCeStep" slot-scope="text, record, index, columns">
              <a-input class="input"
                       v-model="record.dchCeStepStrList[columns.dataIndex.replace('dchCeStepStrList[','').charAt(0)]"
                       @keyup="record.dchCeStepStrList[columns.dataIndex.replace('dchCeStepStrList[','').charAt(0)] = (record.dchCeStepStrList[columns.dataIndex.replace('dchCeStepStrList[','').charAt(0)] + '').replaceAll(/[\t\n\s]/g, ',').replaceAll(/[^0-9,，]/g, '').replaceAll(/[，]/g, ',')"
                       @blur="handleBlur('rptStepParamList', index, null, record)"/>
            </template>
            <template v-for="(item, i) in dcirTitleIndexArr" :slot="`dcirTitle_${i}`">
              <div class="flex-sb-center-row">
                <a-input class="input" v-model="cycleReportParam.dcirTiTleList[i]" placeholder="示例：XX SOC XX s" @blur="handleBlur"/>
                <a-tooltip v-if="i === cycleReportParam.dcirTiTleList.length - 1 && i < 3">
                  <template slot="title">增加DCIR参数组</template>
                  <a-icon class="ml10" type="plus" style="color: #1890ff;" @click="changeDcirParamList(false)"/>
                </a-tooltip>
                <a-tooltip v-if="i === cycleReportParam.dcirTiTleList.length - 1 && i > 0">
                  <template slot="title">删除DCIR参数组</template>
                  <a-icon class="ml10" type="minus" style="color: #1890ff;" @click="changeDcirParamList(true)"/>
                </a-tooltip>
              </div>
            </template>
            <template slot="dcirStepParam" slot-scope="text, record, index, columns">
              <a-input class="input"
                       v-model="record.dcirStepParamList[columns.dataIndex.replace('dcirStepParamList[','').charAt(0)][columns.dataIndex.replace('dcirStepParamList[','').substring(3)]"
                       @keyup="record.dcirStepParamList[columns.dataIndex.replace('dcirStepParamList[','').charAt(0)][columns.dataIndex.replace('dcirStepParamList[','').substring(3)] = (record.dcirStepParamList[columns.dataIndex.replace('dcirStepParamList[','').charAt(0)][columns.dataIndex.replace('dcirStepParamList[','').substring(3)] + '').replaceAll(columns.dataIndex.includes('dchStepTime') ? /[^0-9:.]/g : /[^0-9]/g, '')"
                       @blur="handleBlur('rptStepParamList', index, null, record)"/>
            </template>
            <template slot="dchStepTimeTitle">
              放电工步时间
              <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
                <a-icon type="question-circle" style="color: #1890ff;"/>
                <template slot="title">
                  <span>
                    未填写放电工步时间默认取工步数据表数据；<br/>
                    填写了放电工步时间则取详细数据表数据；<br/>
                    格式：HH:mm:ss.SSS，精确匹配；例如：0:00:10.000
                  </span>
                </template>
              </a-tooltip>
            </template>
            <template slot="chCeStepTitle">
              充电容量&恒流比工步号
              <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
                <a-icon type="question-circle" style="color: #1890ff;"/>
                <template slot="title">
                    <span>
                      充电容量&恒流比工步号非必填；<br/>
                      如是多个工步号，需以逗号,隔开，计算平均值
                    </span>
                </template>
              </a-tooltip>
            </template>
            <template slot="chCeStep" slot-scope="text, record, index, columns">
              <a-input class="input"
                       v-model="cycleReportParam.rptStepParamList[index].chCeStep"
                       @keyup="cycleReportParam.rptStepParamList[index].chCeStep = (cycleReportParam.rptStepParamList[index].chCeStep + '').replaceAll(/[\t\n\s]/g, ',').replaceAll(/[^0-9,，]/g, '').replaceAll(/[，]/g, ',')"
                       @blur="handleBlur('rptStepParamList', index, 'chCeStep')"/>
            </template>
            <template slot="footer">
              <div class="footer-btn" @click="addParam('rptStepParamList')">
                <span></span>
                <span></span>
                <span></span>
                <span></span>
                <a-icon type="plus"></a-icon>
              </div>
            </template>
          </a-table>
        </div>

        <div class="flex-sb-center-row">
          <strong class="mt10">
            3、ETP(累计能量)-参数填写
            <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
              <a-icon type="question-circle" style="color: #1890ff;"/>
              <template slot="title">
                <span>若需要以ETP为X轴生成图表，请填写ETP参数</span>
              </template>
            </a-tooltip>
          </strong>
          <a-icon @click="handleClose('isEtpParamClose')" :type="isEtpParamClose ? 'down' : 'up'"/>
        </div>
        <div v-show="!isEtpParamClose">
          <div class="mt10">
            <span class="mr10">ETP计算规则 : </span>
            <a-radio-group name="radioGroup" buttonStyle="solid" :value="cycleReportParam.etpType" @change="etpTypeChange">
              <a-radio value="dch">累计放电能量</a-radio>
              <a-radio value="chAndDch">累计充放电能量</a-radio>
            </a-radio-group>
            <a-popconfirm placement="topRight" title="确认清除所有ETP参数吗？" @confirm="cleanEtpParam">
              <a-button style="float: right;">清除</a-button>
            </a-popconfirm>
          </div>

          <div class="mt10">①Cycle：ETP起始及结束工步输入</div>
          <a-table class="mt10"
                   bordered
                   :columns="cycleEtpColumns"
                   :data-source="cycleReportParam.cycleStepParamList"
                   :pagination="false">
            <template slot="etpStartStep" slot-scope="text, record, index, columns">
              <a-input class="input" :disabled="!cycleReportParam.etpType"
                       v-model="cycleReportParam.cycleStepParamList[index].etpStartStep"
                       @keyup="cycleReportParam.cycleStepParamList[index].etpStartStep = (cycleReportParam.cycleStepParamList[index].etpStartStep + '').replaceAll(/[^0-9]/g, '')"
                       @blur="handleBlur('cycleStepParamList', index, null, record)"/>
            </template>
            <template slot="etpEndStep" slot-scope="text, record, index, columns">
              <a-input class="input" :disabled="!cycleReportParam.etpType"
                       v-model="cycleReportParam.cycleStepParamList[index].etpEndStep"
                       @keyup="cycleReportParam.cycleStepParamList[index].etpEndStep = (cycleReportParam.cycleStepParamList[index].etpEndStep + '').replaceAll(/[^0-9]/g, '')"
                       @blur="handleBlur('cycleStepParamList', index, null, record)"/>
            </template>
          </a-table>

          <div class="mt10">
            <span class="mr10">RPT层ETP : </span>
            <a-radio-group name="radioGroup" buttonStyle="solid" :value="cycleReportParam.needRptEtp" @change="needRptEtpChange">
              <a-radio value="no">不计算中检ETP</a-radio>
              <a-radio value="yes">加上中检ETP</a-radio>
            </a-radio-group>
          </div>
          <div v-if="cycleReportParam.needRptEtp === 'yes'">
            <div class="mt10">②RPT：ETP结束工步输入</div>
            <a-table class="mt10"
                     bordered
                     :columns="rptEtpColumns"
                     :data-source="cycleReportParam.rptStepParamList"
                     :pagination="false">
              <template slot="etpEndStep" slot-scope="text, record, index, columns">
                <a-input class="input" :disabled="!cycleReportParam.etpType"
                         v-model="cycleReportParam.rptStepParamList[index].etpEndStep"
                         @keyup="cycleReportParam.rptStepParamList[index].etpEndStep = (cycleReportParam.rptStepParamList[index].etpEndStep + '').replaceAll(/[^0-9]/g, '')"
                         @blur="handleBlur"/>
              </template>
            </a-table>
          </div>
        </div>
      </div>
    </div>

    <orderDataSelectModal ref="orderDataSelectModal"
                          :selectedOrderDataList="cycleReportParam.orderDataList"
                          @selectedOrderDataChange="orderDataListChange">
    </orderDataSelectModal>

    <!-- 圈数计算规则输入弹窗 -->
    <a-modal title="循环规则输入" :width="1200" :height="600" :bodyStyle="{ padding: 0 }" :visible="isShowCompute"
             style="padding: 0" :maskClosable="false" @cancel="handleCancel('isShowCompute')">
      <div class="mt10">
        <a-button class="ml10 mr10" size="small" @click="addParam('rptCycComList')">
          <a-icon type="plus"/>
          增加循环阶段
        </a-button>
        <a-button class="mr10" size="small" @click="computeCycles">
          <a-icon type="calculator"/>
          计算
        </a-button>
        <a-button size="small" @click="handleOpen('isShowRptCycleList')">
          <a-icon type="ordered-list"/>
          详情
        </a-button>
      </div>
      <a-table id="cycComputeTable" class="mt10"
               bordered
               :columns="rptCycComColumns"
               :data-source="cycleReportParam.rptCycComList"
               :pagination="false">
        <template slot="action" slot-scope="text, record, index, columns">
          <a @click="deleteParam('rptCycComList', index)" style="text-align: center">删除</a>
        </template>
        <template slot="cycStart" slot-scope="text, record, index, columns">
          <a-input-number class="input" :min="0" :precision="0" v-model="cycleReportParam.rptCycComList[index].cycStart"/>
        </template>
        <template slot="cycEnd" slot-scope="text, record, index, columns">
          <a-input-number class="input" :min="0" :precision="0" v-model="cycleReportParam.rptCycComList[index].cycEnd"/>
        </template>
        <template slot="cycInterval" slot-scope="text, record, index, columns">
          <a-input-number class="input" :min="0" :precision="0" v-model="cycleReportParam.rptCycComList[index].cycInterval"/>
        </template>
      </a-table>
      <template slot="footer">
        <a-button key="back" @click="handleCancel('isShowCompute')">关闭</a-button>
      </template>
    </a-modal>

    <!-- 圈数详情展示弹窗 -->
    <a-modal title="详情" :width="600" :height="1200" :bodyStyle="{ padding: 0 }" :visible="isShowRptCycleList"
             style="padding: 0" :maskClosable="false" @cancel="handleCancel('isShowRptCycleList')">
      <a-table class="mt10"
               bordered
               :columns="rptCycleColumns"
               :data-source="cycleReportParam.rptCycleList"
               :pagination="false">
      </a-table>
      <template slot="footer">
        <a-button key="back" @click="handleCancel('isShowRptCycleList')">关闭</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script>
import {STable} from "@/components";
import orderDataSelectModal from "@/views/system/vTestReport/components/dongLiReport/orderDataSelectModal";
import stepData from "@/views/system/lims/folder/stepData";
import moment from "moment";
import Sortable from 'sortablejs';

export default {
  name: "cycleReportBuild",
  components: {
    STable,
    orderDataSelectModal,
    stepData,
  },
  props: {
    reportQueryParam: {
      type: Object,
    },
    hasQueryParamFlag: {
      type: Boolean,
      default: false,
    },
    reportType: {
      type: String,
    },
    width:{
      type: Number,
      default: 0
    },
    padding:{
      type: String,
      default: '8px'
    }
  },
  data() {
    return {
      // 测试数据列表
      orderColumns: [
        {
          title: "操作",
          align: "center",
          width: 40,
          scopedSlots: {customRender: "action"}
        },
        {
          title: "序号",
          align: "center",
          width: 30,
          customRender: (text, record, index) => index + 1
        },
        {
          title: "委托单号",
          dataIndex: "folderno",
          align: "center",
          width: 45
        },
        {
          title: "测试项目别名",
          width: 65,
          align: "center",
          dataIndex: "alias"
        },
        {
          title: "样品编号",
          width: 50,
          align: "center",
          dataIndex: "orderno"
        },
        {
          title: "测试编码",
          width: 50,
          align: "center",
          dataIndex: "celltestcode",
          scopedSlots: {customRender: "celltestcode"}
        },
        {
          title: "数据位置",
          width: 50,
          align: "center",
          dataIndex: "dataPath",
          ellipsis: true,
          scopedSlots: {customRender: 'dataPath'},
        },
        /*{
          title: "设备编号",
          width: 45,
          align: "center",
          dataIndex: "equiptcode",
          customRender: (text, record, index) => {
            return (null != text ? text : "") + "-" + (null != record.channelno ? record.channelno : "")
          }
        },*/
        {
          title: "开始时间",
          width: 50,
          align: "center",
          dataIndex: "startTime",
          customRender: (text, record, index) => {
            if (null != text) {
              return moment(text).format("YYYY-MM-DD")
            }
            return text
          }
        },
        {
          title: "结束时间",
          width: 50,
          align: "center",
          dataIndex: "endTime",
          customRender: (text, record, index) => {
            if (null != text) {
              return moment(text).format("YYYY-MM-DD")
            }
            return text
          }
        },
        {
          title: "电芯组别",
          width: 45,
          align: "center",
          dataIndex: "groupIndex",
          scopedSlots: {customRender: 'groupIndex'},
        },
      ],
      deleteSelectedRowKeys: [],
      deleteRowSelection: {
        columnWidth: 20,
        onChange: (selectedRowKeys, selectedRows) => {
          this.deleteSelectedRowKeys = selectedRowKeys
        }
      },

      visible: false,

      isTempClose: false,
      isCycleParamClose: false,
      isRptParamClose: false,
      isEtpParamClose: true,

      cycleReportParam: {
        reportName: '',
        temp: '',
        etpType: '',
        orderDataList: [], // 选择的测试数据
        mode: 'All',
        standardCycleNum: 1,
        cycleStepParamList: [ {} ], // Cycle工步参数列表
        rptCycComList: [], // RPT圈数计算列表
        rptCycleList: [], // RPT循环圈数列表
        rptStepParamList: [ { dcirStepParamList:[], dchCeStepStrList: [] } ], // RPT工步参数列表
        dcirTiTleList: [""], // DCIR标题列表
        dchCeStepTiTleList: [], // 放电工步标题信息列表
      },
      cycleListDeleteSelectedRowKeys: [],
      rptListDeleteSelectedRowKeys: [],

      primaryGroupOptions: [],
      // cycle工步参数表头
      cycleStepColumns: [
        {
          title: "操作",
          align: "center",
          width: 40,
          scopedSlots: {customRender: "action"}
        },
        {
          title: "序号",
          align: "center",
          width: 40,
          customRender: (text, record, index) => index + 1
        },
        {
          dataIndex: "groupIndex",
          align: "center",
          width: 100,
          scopedSlots: {
            title: "groupIndexTitle",
            customRender: "groupIndex"
          }
        },
        {
          dataIndex: "ceStep",
          align: "center",
          width: 120,
          scopedSlots: {
            title: "ceStepTitle",
            customRender: "ceStep"
          }
        },
        {
          dataIndex: "chCeStep",
          align: "center",
          width: 120,
          scopedSlots: {
            title: "chCeStepTitle",
            customRender: "chCeStep"
          }
        },
        {
          title: <a-tooltip arrow-point-at-center><template slot="title"><span>机制填写，非必填，只能填写S或N或F，超出无法识别<br/>S=Slow，N=Normal，F=Fast</span></template>机制 <a-icon type="question-circle" style="color: #1890ff;"/></a-tooltip>,
          align: "center",
          width: 100,
          scopedSlots: {customRender: "mode"}
        },
      ],
      // cycleETP参数表头
      cycleEtpColumns: [
        {
          title: "序号",
          align: "center",
          width: 40,
          customRender: (text, record, index) => index + 1
        },
        {
          title: "电芯组别",
          width: 100,
          align: "center",
          customRender: (text, record, index) => typeof record.groupIndex === 'number' ? record.groupIndex + 1 : ''
        },
        {
          title: "ETP起始工步号",
          dataIndex: "etpStartStep",
          align: "center",
          width: 100,
          scopedSlots: {customRender: "etpStartStep"}
        },
        {
          title: "ETP结束工步号",
          dataIndex: "etpEndStep",
          align: "center",
          width: 100,
          scopedSlots: {customRender: "etpEndStep"}
        },
      ],

      isShowCompute: false,
      // rpt圈数计算列表表头
      rptCycComColumns: [
        {
          title: "操作",
          align: "center",
          width: 40,
          scopedSlots: {customRender: "action"}
        },
        {
          title: "序号",
          align: "center",
          width: 40,
          customRender: (text, record, index) => index + 1
        },
        {
          title: "开始循环圈数",
          dataIndex: "cycStart",
          align: "center",
          width: 100,
          scopedSlots: {customRender: "cycStart"}
        },
        {
          title: "结束循环圈数",
          dataIndex: "cycEnd",
          align: "center",
          width: 100,
          scopedSlots: {customRender: "cycEnd"}
        },
        {
          title: "中检间隔圈数",
          dataIndex: "cycInterval",
          align: "center",
          width: 100,
          scopedSlots: {customRender: "cycInterval"}
        },
        {
          title: "圈数列表",
          dataIndex: "cycleList",
          align: "center",
          width: 200,
          customRender: (text, record, index, column) => {
            let cycles = record.cycleList || []
            return cycles.join(',')
          }
        },
        {
          title: "圈数数量",
          dataIndex: "cycleNum",
          align: "center",
          width: 100
        },
      ],

      isShowRptCycleList: false,
      // rpt循环圈数表头
      rptCycleColumns: [
        {
          title: "序号",
          align: "center",
          width: 40,
          customRender: (text, record, index) => index + 1
        },
        {
          title: "圈数",
          dataIndex: "cycle",
          align: "center",
          width: 100,
          scopedSlots: {customRender: "cycle"}
        },
      ],

      dcirNum: 1,
      dcirTitleIndexArr: [],
      dchCeStepNum: 0,
      dchCeStepIndexArr: [],
      // rpt工步参数表头
      rptParamColumns: [],
      // rptETP参数表头
      rptEtpColumns: [
        {
          title: "序号",
          align: "center",
          width: 40,
          customRender: (text, record, index) => index + 1
        },
        {
          title: "电芯组别",
          width: 100,
          align: "center",
          customRender: (text, record, index) => typeof record.groupIndex === 'number' ? record.groupIndex + 1 : ''
        },
        {
          title: "ETP结束工步号",
          dataIndex: "etpEndStep",
          align: "center",
          width: 100,
          scopedSlots: {customRender: "etpEndStep"}
        },
      ],
    }
  },
  watch: {
    hasQueryParamFlag(newVal, oldVal) {
      this.init()
    }
  },
  created() {
    this.init()
  },
  mounted() {
    this.changeRptParamColumns(true)
    this.handleHeight()
    this.$nextTick(() => {
      let tableContainer = this.$refs.tableContainer
      this.rowDrop(tableContainer)
    })
  },
  destroyed() {
  },
  methods: {
    handleHeight() {
      const minHeight = document.body.clientHeight - 42 - this.width - 2*8 - 8 - 37 - 2*8
      document.documentElement.style.setProperty(`--height`, `${minHeight}px`)
    },
    rowDrop(dom) {
      new Sortable.create(dom.querySelector('.ant-table>.ant-table-content .ant-table-tbody'), {
        handle: '.drag', // 按钮拖拽
        animation: 150,
        onEnd: ({newIndex, oldIndex}) => {
          // 拖拽后回调
          const currRow = this.cycleReportParam.orderDataList.splice(oldIndex, 1)[0]
          this.cycleReportParam.orderDataList.splice(newIndex, 0, currRow)
        }
      })
    },
    init() {
      if (this.hasQueryParamFlag && this.reportType === 'Cycle') {
        this.cycleReportParam = this.reportQueryParam
        if (this.cycleReportParam.etpType) {
          this.isEtpParamClose = false
        }
        // 历史建模默认赋值
        if (this.cycleReportParam.etpType && !this.cycleReportParam.needRptEtp) {
          this.$set(this.cycleReportParam, 'needRptEtp', 'yes')
        }
        if (!this.cycleReportParam.mode) {
          this.$set(this.cycleReportParam, 'mode', 'All')
        }
        if (!this.cycleReportParam.standardCycleNum) {
          this.$set(this.cycleReportParam, 'standardCycleNum', 1)
        }
        this.orderDataListChange()
        this.changeRptParamColumns(true)
      }
    },

    deleteSelect() {
      this.cycleReportParam.orderDataList = this.cycleReportParam.orderDataList.filter(item => !this.deleteSelectedRowKeys.includes(item.uuid));
      this.deleteSelectedRowKeys = []
      this.orderDataListChange()
    },
    deleteDataOne(record, index) {
      this.cycleReportParam.orderDataList.splice(index, 1)
      const findIndex = this.deleteSelectedRowKeys.findIndex(rowKey => rowKey == record.uuid)
      if (findIndex !== -1) {
        this.deleteSelectedRowKeys.splice(findIndex, 1)
      }
      this.orderDataListChange()
    },
    moveUp(arr, index) {
      if (arr.length > 1 && index > 0) { // 确保数组至少有两个元素，且索引有效
        arr[index] = arr.splice(index - 1, 1, arr[index])[0]; // 移除元素后立即插入到前一个位置
      }
      this.orderDataListChange()
    },
    moveDown(arr, index) {
      if (arr.length > 1 && index < arr.length - 1) { // 确保数组至少有两个元素，且索引有效
        arr[index] = arr.splice(index + 1, 1, arr[index])[0]; // 移除元素后立即插入到后一个位置
      }
      this.orderDataListChange()
    },
    orderDataListChange() {
      if (Array.isArray(this.cycleReportParam.orderDataList) && this.cycleReportParam.orderDataList.length > 0) {
        this.cycleReportParam.temp = this.cycleReportParam.orderDataList[0].tem
      }
      this.primaryGroupChange()

      this.$emit('handleVerify', this._handleVerify())
    },
    primaryGroupChange() {
      // 根据电芯编码分组，组内编号
      const groupedByOrderno = this.cycleReportParam.orderDataList.reduce((acc, curr) => {
        const orderno = curr.orderno
        if (!acc[orderno]) {
          acc[orderno] = []
        }
        curr.groupIndex = acc[orderno].length + 1
        acc[orderno].push(curr)
        return acc
      }, {})
      // 将分组后的结果展平成一个新的列表
      this.cycleReportParam.orderDataList = [].concat(...Object.values(groupedByOrderno))

      // 找到最大的分组大小
      const maxGroupSize = Object.values(groupedByOrderno).reduce((maxGroupSize, group) => Math.max(maxGroupSize, group.length), 0)

      // 分组数量变化
      if (maxGroupSize !== this.primaryGroupOptions.length) {
        this.maxGroupSizeChange(maxGroupSize)
      }
    },
    maxGroupSizeChange(maxGroupSize = 0) {
      this.primaryGroupOptions = []
      for (let i = 0; i < maxGroupSize; i++) {
        this.primaryGroupOptions.push({value:i, label:i+1})
      }

      const clearAll = this.cycleReportParam.orderDataList.length === 0 || this.primaryGroupOptions.length === 0
      for (let i = 0; i < this.cycleReportParam.cycleStepParamList.length; i++) {
        let row = this.cycleReportParam.cycleStepParamList[i]
        if ('groupIndex' in row && (clearAll || row.groupIndex > this.primaryGroupOptions.length - 1)) {
          delete row.groupIndex
        }
      }
      for (let i = 0; i < this.cycleReportParam.rptStepParamList.length; i++) {
        let row = this.cycleReportParam.rptStepParamList[i]
        if ('groupIndex' in row && (clearAll || row.groupIndex > this.primaryGroupOptions.length - 1)) {
          delete row.groupIndex
        }
      }
    },

    handleOpen(target) {
      this[target] = true
    },
    handleCancel(target) {
      this[target] = false
    },
    handleClose(index) {
      this[index] = !this[index]
    },
    cycleDeleteRowOnChange(selectedRowKeys, selectedRows) {
      this.cycleListDeleteSelectedRowKeys = selectedRowKeys
    },
    rptDeleteRowOnChange(selectedRowKeys, selectedRows) {
      this.rptListDeleteSelectedRowKeys = selectedRowKeys
    },
    deleteSelectedParams(targetObj) {
      // 按照索引顺序降序排列，避免删除元素影响后续索引
      this[`${targetObj}ListDeleteSelectedRowKeys`].sort((a, b) => b - a)
      this[`${targetObj}ListDeleteSelectedRowKeys`].forEach(item => this.cycleReportParam[`${targetObj}StepParamList`].splice(item, 1))
      this[`${targetObj}ListDeleteSelectedRowKeys`] = []
      // 全删除需要保留一行
      if(this.cycleReportParam[`${targetObj}StepParamList`].length === 0) {
        this.cycleReportParam[`${targetObj}StepParamList`] = targetObj === 'cycle' ? [ {} ] : [ { dcirStepParamList:[], dchCeStepStrList: [] } ]
        if (targetObj === 'rpt') {
          this.cycleReportParam.dcirTiTleList = ['']
          this.dcirNum = 1
          this.changeRptParamColumns(false)
          return
        }
      }

      this.$emit('handleVerify', this._handleVerify())
    },

    changedchCeStepStrList(isDelete = false) {
      if (isDelete) {
        if (this.dchCeStepNum > 0) {
          this.dchCeStepNum--
          this.cycleReportParam.dchCeStepTiTleList.splice(this.dchCeStepNum, 1)
        } else {
          return
        }
      } else {
        if (this.dchCeStepNum < 9) {
          this.dchCeStepNum++
          this.cycleReportParam.dchCeStepTiTleList.push("")
        } else {
          return this.$message.warn('最多10列放电容量&能量工步号，无法增加')
        }
      }

      this.changeRptParamColumns(false)
    },
    changeDcirParamList(isDelete = false) {
      if (isDelete) {
        if (this.dcirNum > 1) {
          this.dcirNum--
          this.cycleReportParam.dcirTiTleList.splice(this.dcirNum, 1)
        } else {
          return this.$message.warn('至少一组DCIR参数，无法减少')
        }
      } else {
        if (this.dcirNum < 4) {
          this.dcirNum++
          this.cycleReportParam.dcirTiTleList.push("")
        } else {
          return this.$message.warn('最多四组DCIR参数，无法增加')
        }
      }

      this.changeRptParamColumns(false)
    },
    changeRptParamColumns(isFirst = false) {
      if (isFirst) {
        if (Array.isArray(this.cycleReportParam.dcirTiTleList) && this.cycleReportParam.dcirTiTleList.length > 0) {
          this.dcirNum = this.cycleReportParam.dcirTiTleList.length
        } else {
          this.cycleReportParam.dcirTiTleList = [""]
          this.dcirNum = 1
        }
        if (Array.isArray(this.cycleReportParam.dchCeStepTiTleList) && this.cycleReportParam.dchCeStepTiTleList.length > 0) {
          this.dchCeStepNum = this.cycleReportParam.dchCeStepTiTleList.length
        } else {
          this.cycleReportParam.dchCeStepTiTleList = []
          this.dchCeStepNum = 0
        }
      }

      for (let i = 0; i < this.cycleReportParam.rptStepParamList.length; i++) {
        const rptStepParam = this.cycleReportParam.rptStepParamList[i];

        rptStepParam.dcirStepParamList = rptStepParam.dcirStepParamList || []
        // DCIR参数组赋值 如果长度小于目标长度，则用空对象填充
        const dcirStepLen = rptStepParam.dcirStepParamList.length;
        if (dcirStepLen < this.dcirNum) {
          rptStepParam.dcirStepParamList.push(...Array.from({ length: this.dcirNum }, () => ({})));
        }
        // 删除DCIR需要清空参数组 如果长度大于目标长度，则截断数组
        if (dcirStepLen > this.dcirNum) {
          rptStepParam.dcirStepParamList.length = this.dcirNum;
        }

        rptStepParam.dchCeStepStrList = rptStepParam.dchCeStepStrList || []
        // 放电工步号赋值 如果长度小于目标长度，则用空字符串填充
        const dchStepLen = rptStepParam.dchCeStepStrList.length;
        if (dchStepLen < this.dchCeStepNum) {
          rptStepParam.dchCeStepStrList.push(...new Array(this.dchCeStepNum - dchStepLen).fill(''));
        }
        // 删除放电工步号列需要清除放电工步 如果长度大于目标长度，则截断数组
        if (dchStepLen > this.dchCeStepNum) {
          rptStepParam.dchCeStepStrList.length = this.dchCeStepNum;
        }
      }

      this.rptParamColumns =  [
        {
          title: "操作",
          align: "center",
          width: 40,
          scopedSlots: {customRender: "action"}
        },
        {
          title: "序号",
          align: "center",
          width: 40,
          customRender: (text, record, index) => index + 1
        },
        {
          dataIndex: "groupIndex",
          align: "center",
          width: 100,
          scopedSlots: {
            title: "groupIndexTitle",
            customRender: "groupIndex"
          }
        },
        {
          dataIndex: "ceStep",
          align: "center",
          width: 130,
          scopedSlots: {
            title: "ceStepTitle",
            customRender: "ceStep"
          }
        },
      ]

      // 添加放电容量能量工步号列表
      this.dchCeStepIndexArr = []
      for (let i = 0; i < this.dchCeStepNum; i++) {
        this.rptParamColumns.push(
            {
              dataIndex: "dchCeStepStrList[" + i + "]",
              align: "center",
              width: 130,
              scopedSlots: {
                title: "dchCeStepTitle_" + i,
                customRender: "dchCeStep"
              }
            }
        )

        this.dchCeStepIndexArr.push(i)
      }

      this.dcirTitleIndexArr = []
      for (let i = 0; i < this.dcirNum; i++) {
        this.rptParamColumns.push(
          {
            children: [
              {
                title: "搁置工步号",
                width: 100,
                align: "center",
                dataIndex: "dcirStepParamList[" + i + "].restStep",
                scopedSlots: {customRender: "dcirStepParam"}
              },
              {
                title: "放电工步号",
                width: 100,
                align: "center",
                dataIndex: "dcirStepParamList[" + i + "].dchStep",
                scopedSlots: {customRender: "dcirStepParam"}
              },
              {
                width: 130,
                align: "center",
                dataIndex: "dcirStepParamList[" + i + "].dchStepTime",
                scopedSlots: {
                  title: "dchStepTimeTitle",
                  customRender: "dcirStepParam"
                }
              }
            ],
            scopedSlots: {
              title: "dcirTitle_" + i
            }
          }
        )

        this.dcirTitleIndexArr.push(i)
      }

      // 添加充电容量&恒流比工步号
      this.rptParamColumns.push(
          {
            dataIndex: "chCeStep",
            align: "center",
            width: 130,
            scopedSlots: {
              title: "chCeStepTitle",
              customRender: "chCeStep"
            }
          }
      )

      if (!isFirst) {
        this.$emit('handleVerify', this._handleVerify())
      }
    },

    addParam(targetList) {
      if (targetList === 'rptStepParamList') {
        let dcirStepParamList = Array.from({ length: this.dcirNum }, () => ({}))
        let dchCeStepStrList = new Array(this.dchCeStepNum).fill('')
        this.cycleReportParam[targetList].push({ dcirStepParamList:dcirStepParamList, dchCeStepStrList:dchCeStepStrList })
      } else {
        this.cycleReportParam[targetList].push({})
      }

      this.$emit('handleVerify', this._handleVerify())
    },
    deleteParam(targetList, index) {
      this.cycleReportParam[targetList].splice(index, 1)

      this.$emit('handleVerify', this._handleVerify())
    },
    handleNumberBlur(target = null) {
      if (target && this.cycleReportParam[target]) {
        this.cycleReportParam[target] = Number.parseFloat((this.cycleReportParam[target]+'').replaceAll(/\.+/g, '.').replaceAll(/^\.|\.$/g, ''))
      }

      this.$emit('handleVerify', this._handleVerify())
    },
    handleBlur(targetList = null, index = null, key = null, record = null) {
      if (targetList && typeof index == 'number' && ['ceStep', 'chCeStep'].includes(key)) {
        this.cycleReportParam[targetList][index][key] = this.cycleReportParam[targetList][index][key] === undefined ? '' : (this.cycleReportParam[targetList][index][key] + '').replaceAll(/,+/g, ',') // .replaceAll(/^,|,$/g, '')
        // this.cycleReportParam[targetList][index][`${[key]}List`] = this.cycleReportParam[targetList][index][key] !== '' ? this.cycleReportParam[targetList][index][key].replaceAll(/^,|,$/g, '').split(',') : []
        this.$set(this.cycleReportParam[targetList], index, this.cycleReportParam[targetList][index])
      }

      // 解决无法感知列表参数变化的问题
      if (typeof index == 'number' && record !== null) {
        this.$set(this.cycleReportParam[targetList], index, record)
      }

      this.$emit('handleVerify', this._handleVerify())
    },
    copyFromExcel(event, index) {
      // excel复制末尾会有换行符，split后数组多一个空串，先去除
      let rows = event.clipboardData.getData("text").replace(/[\n]$/, "").split("\n")

      let firstData
      // 起始行：rptIndex，结束行：math.min(rptIndex + rows.length, this.calendarParam.rptStepParamList.length - 1)
      for (let i = index; i < this.cycleReportParam.cycleStepParamList.length && i < index + rows.length; i++) {
        let rowList = rows[i-index].split("\t")
        if (i === index) {
          firstData = rowList[0].replaceAll(/[^SNF]/g, "").charAt(0)
        }

        // 只取第一列
        this.cycleReportParam.cycleStepParamList[i].mode = rowList[0].replaceAll(/[^SNF]/g, "").charAt(0)
      }

      // 解决第一个单元格被覆盖的问题
      setTimeout(() => {
        this.cycleReportParam.cycleStepParamList[index].mode = firstData

        this.$emit('handleVerify', this._handleVerify())
      }, 10)
    },
    etpTypeChange(event) {
      this.cycleReportParam.etpType = event.target.value

      this.$emit('handleVerify', this._handleVerify())
    },
    needRptEtpChange(event) {
      this.cycleReportParam.needRptEtp = event.target.value

      this.$emit('handleVerify', this._handleVerify())
    },
    cleanEtpParam() {
      if (!this.cycleReportParam) {
        return
      }

      this.cycleReportParam.etpType = ''
      delete this.cycleReportParam.needRptEtp
      let cycleStepParamList = this.cycleReportParam.cycleStepParamList
      if (Array.isArray(cycleStepParamList) && cycleStepParamList.length > 0) {
        cycleStepParamList.forEach(item => {
          delete item.etpStartStep
          delete item.etpEndStep
        })
      }
      let rptStepParamList = this.cycleReportParam.rptStepParamList
      if (Array.isArray(rptStepParamList) && rptStepParamList.length > 0) {
        rptStepParamList.forEach(item => {
          delete item.etpEndStep
        })
      }

      this.$emit('handleVerify', this._handleVerify())
    },
    computeCycles() {
      if (!Array.isArray(this.cycleReportParam.rptCycComList) || this.cycleReportParam.rptCycComList.length === 0) {
        return this.$message.warn('请填写数据')
      }

      if (this.cycleReportParam.rptCycComList[0].cycStart != 0) {
        return this.$message.warn('请填写正确数据，中检天数须由0开始！')
      }

      //校验
      for (let i = 0; i < this.cycleReportParam.rptCycComList.length; i++) {
        let row = this.cycleReportParam.rptCycComList[i]

        if (typeof row.cycStart !== 'number' || typeof row.cycEnd !== 'number' || typeof row.cycInterval !== 'number') {
          return this.$message.warn('请填写完整数据')
        }

        if ( (row.cycEnd - row.cycStart) != row.cycInterval && (row.cycEnd - row.cycStart) % row.cycInterval != 0 ) {
          return this.$message.warn('请填写正确数据，开始与结束的差值需为间隔天数的倍数')
        }

        if (i + 1 < this.cycleReportParam.rptCycComList.length && row.cycEnd != this.cycleReportParam.rptCycComList[i + 1].cycStart) {
          return this.$message.warn('请填写正确数据，开始中检天数请与上一阶段的结束中检天数对应')
        }
      }

      // 计算
      for (let i = 0; i < this.cycleReportParam.rptCycComList.length; i++) {
        let row = this.cycleReportParam.rptCycComList[i]

        let cycleList = [row.cycStart]
        let sum = row.cycStart + row.cycInterval
        while (sum < row.cycEnd) {
          cycleList.push(sum)
          sum += row.cycInterval
        }

        if (i == this.cycleReportParam.rptCycComList.length - 1) {
          if (row.cycStart != row.cycEnd) {
            cycleList.push(row.cycEnd)
          }
        }

        this.$set(this.cycleReportParam.rptCycComList[i], 'cycleList', cycleList)
        this.$set(this.cycleReportParam.rptCycComList[i], 'cycleNum', cycleList.length)
      }

      // 赋值给循环报告参数
      this.refreshRptCycleList()

      this.$emit('handleVerify', this._handleVerify())
    },
    refreshRptCycleList() {
      this.cycleReportParam.rptCycleList = []
      for (let i = 0; i < this.cycleReportParam.rptCycComList.length; i++) {
        let row = this.cycleReportParam.rptCycComList[i]
        this.cycleReportParam.rptCycleList.push(
          ...row.cycleList.map((item, index) => {
            return {cycle: item}
          })
        )
      }
    },

    // 校验
    _handleVerify() {
      this.$forceUpdate()

      // ---------------------- 校验：测试数据选择 ------------------------
      let orderDataList = this.cycleReportParam.orderDataList
      if (!Array.isArray(orderDataList) || orderDataList.length === 0) {
        return [false, '请选择测试数据']
      }

      // ---------------------- 校验：标题信息填写 ------------------------
      if (!this.cycleReportParam.projectName) {
        return [false, '请填写项目名称']
      }
      if (!this.cycleReportParam.phase) {
        return [false, '请填写样品阶段']
      }
      if (!this.cycleReportParam.temp && this.cycleReportParam.temp !== 0) {
        return [false, '请填写温度']
      }
      if (!this.cycleReportParam.rate && this.cycleReportParam.rate !== 0) {
        return [false, '请填写循环机制']
      }
      if (!this.cycleReportParam.startSoc && this.cycleReportParam.startSoc !== 0) {
        return [false, '请填写起始SOC']
      }
      if (!this.cycleReportParam.endSoc && this.cycleReportParam.endSoc !== 0) {
        return [false, '请填写结束SOC']
      }


      // ---------------------- 校验：RPT参数填写 非必填 ------------------------
      let rptStepParamList = this.cycleReportParam.rptStepParamList
      // 中检工步信息可不填。DCIR可不填，若任意行填写了DCIR参数，需填写完整容量&能量工步号和dcirTiTle
      if (Array.isArray(rptStepParamList) && rptStepParamList.length > 0) {
        for (let i = 0; i < rptStepParamList.length; i++) {
          if (!rptStepParamList[i].ceStep && rptStepParamList[i].dcirStepParamList.some(item => item.restStep || item.dchStep)) {
            return [false, '请填写RPT工步信息-第 ' + (i+1) + ' 行容量&能量工步号']
          }
        }

        const dchCeStepTiTleList = this.cycleReportParam.dchCeStepTiTleList
        if (this.dchCeStepNum > 0) {
          for (let i = 0; i < dchCeStepTiTleList.length; i++) {
            if (!dchCeStepTiTleList[i]) {
              return [false, '请填写RPT工步信息-第 ' + (i+1) + ' 个放电容量&能量工步号标题']
            }
          }
        }

        const hasDcirParam = rptStepParamList.some(rptStepParam => rptStepParam.dcirStepParamList.some(item => item.restStep || item.dchStep))
        const dcirTiTleList = this.cycleReportParam.dcirTiTleList
        if (hasDcirParam) {
          for (let i = 0; i < dcirTiTleList.length; i++) {
            if (!dcirTiTleList[i]) {
              return [false, '请填写RPT工步信息-第 ' + (i+1) + ' 组DCIR参数组标题']
            }
          }
        }
      }

      let rptCycleList = this.cycleReportParam.rptCycleList
      let hasRptStepParam = Array.isArray(rptStepParamList) && rptStepParamList.some(rptStepParam => rptStepParam.ceStep)
      if (hasRptStepParam && (!Array.isArray(rptCycleList) || rptCycleList.length === 0)) {
        return [false, '请填写RPT循环圈数']
      }

      // let rptStepParamList = this.cycleReportParam.rptStepParamList
      // if (!Array.isArray(rptStepParamList) || rptStepParamList.length === 0) {
      //   return [false, '请填写RPT工步信息']
      // }

      // rptStepParamList校验
      // let isDcirParamFull = false // DCIR参数组校验：任意一行的DCIR参数列表完整即可
      // for(let i = 0; i < rptStepParamList.length; i++) {
      //   let row = rptStepParamList[i]
      //   if (!row.ceStep) {
      //     return [false, '请填写RPT工步信息-第 ' + (i+1) + ' 行容量&能量工步号']
      //   }
      //
      //   if (row.dcirStepParamList.every(item => item.restStep && item.dchStep)) {
      //     isDcirParamFull = true
      //   }
      // }
      // if (!isDcirParamFull) {
      //   return [false, '请填写完整任意一行RPT的DCIR参数列表']
      // }

      // ---------------------- 校验：DCIR参数组标题填写 ------------------------
      // let dcirTiTleList = this.cycleReportParam.dcirTiTleList
      // if (!Array.isArray(dcirTiTleList) || dcirTiTleList.length === 0) {
      //   return [false, '请填写RPT工步信息-DCIR参数组标题']
      // }
      //
      // for (let i = 0; i < dcirTiTleList.length; i++) {
      //   if (!dcirTiTleList[i]) {
      //     return [false, '请填写RPT工步信息-第 ' + (i+1) + ' 组DCIR参数组标题']
      //   }
      // }


      // ---------------------- 校验：Cycle工步参数填写 非必填 ------------------------
      let hasCycStepParam = false
      let cycStepParamList = this.cycleReportParam.cycleStepParamList
      hasCycStepParam = Array.isArray(cycStepParamList) && cycStepParamList.some(cycStepParam => cycStepParam.ceStep)
      if (hasCycStepParam || !hasRptStepParam) { // Cycle和RPT必须填一个
        if (!Array.isArray(cycStepParamList) || cycStepParamList.length === 0) {
          return [false, '请填写Cycle参数']
        }

        if (!this.cycleReportParam.mode) {
          return [false, '请选择作图使用数据']
        }

        for(let i = 0; i < cycStepParamList.length; i++) {
          if (!cycStepParamList[i].ceStep) {
            return [false, '请填写Cycle参数-第 ' + (i+1) + ' 行放电容量&能量工步号']
          }
        }

        if (!this.cycleReportParam.standardCycleNum) {
          return [false, '请填写保持率基准循环号']
        }

        hasCycStepParam = true
      }

      this.cycleReportParam.hasCycStepParam = hasCycStepParam ? 'yes' :'no'
      this.cycleReportParam.hasRptStepParam = hasRptStepParam ? 'yes' :'no'

      // ---------------------- 校验：ETP参数填写：选择了ETP类型，才能填写ETP参数；选择了ETP类型则必须填写参数列表 ------------------------
      let etpType = this.cycleReportParam.etpType
      if (etpType === 'dch' || etpType === 'chAndDch') {
        for (let i = 0; i < cycStepParamList.length; i++) {
          if (!cycStepParamList[i].etpStartStep || !cycStepParamList[i].etpEndStep) {
            return [false, '请填写完整Cycle的ETP参数']
          }
        }

        if (!this.cycleReportParam.needRptEtp) {
          return [false, '请选择RPT层ETP计算规则']
        }

        if (this.cycleReportParam.needRptEtp === 'yes') {
          for (let i = 0; i < rptStepParamList.length; i++) {
            if (rptStepParamList[i].ceStep && !rptStepParamList[i].etpEndStep) {
              return [false, '请填写完整RPT的ETP参数']
            }
          }
        }
      }

      this.handleStepList()

      return [true, this.cycleReportParam]
    },
    handleStepList() {
      const allowedModes = ['S', 'N', 'F']
      let cycleStepParamList = this.cycleReportParam.cycleStepParamList
      if (Array.isArray(cycleStepParamList) && cycleStepParamList.length > 0) {
        for (let i = 0; i < cycleStepParamList.length; i++) {
          if (cycleStepParamList[i].ceStep) {
            cycleStepParamList[i].ceStep = (cycleStepParamList[i].ceStep + "")
              .replaceAll(/[\t\n\s]/g, ',').replaceAll(/[^0-9,]/g, ',') // 替换所有非法字符为英文逗号
              .replaceAll(/,+/g, ",") // 将连续的逗号替换为单个逗号
              // .replaceAll(/^,|,$/g, "") // 去除字符串开头和结尾的逗号

            cycleStepParamList[i].ceStepList = cycleStepParamList[i].ceStep.replaceAll(/^,|,$/g, "").split(",")
          } else {
            cycleStepParamList[i].ceStepList = []
          }
          if (cycleStepParamList[i].chCeStep) {
            cycleStepParamList[i].chCeStep = (cycleStepParamList[i].chCeStep + "")
              .replaceAll(/[\t\n\s]/g, ',').replaceAll(/[^0-9,]/g, ',') // 替换所有非法字符为英文逗号
              .replaceAll(/,+/g, ",") // 将连续的逗号替换为单个逗号
              // .replaceAll(/^,|,$/g, "") // 去除字符串开头和结尾的逗号

            cycleStepParamList[i].chCeStepList = cycleStepParamList[i].chCeStep.replaceAll(/^,|,$/g, "").split(",")
          } else {
            cycleStepParamList[i].chCeStepList = []
          }
          if (cycleStepParamList[i].mode && !allowedModes.includes(cycleStepParamList[i].mode)) {
            cycleStepParamList[i].mode = (cycleStepParamList[i].mode + "").replaceAll(/[^SNF]/g, '').charAt(0)
          }
        }
      }

      if(Array.isArray(this.cycleReportParam.rptStepParamList) && this.cycleReportParam.rptStepParamList.length > 0) {
        for (let i = 0; i < this.cycleReportParam.rptStepParamList.length; i++) {
          const rptStepParam = this.cycleReportParam.rptStepParamList[i]
          if (rptStepParam.ceStep) {
            rptStepParam.ceStep = (rptStepParam.ceStep + "")
              .replaceAll(/[\t\n\s]/g, ',').replaceAll(/[^0-9,]/g, ',') // 替换所有非法字符为英文逗号
              .replaceAll(/,+/g, ",") // 将连续的逗号替换为单个逗号
              // .replaceAll(/^,|,$/g, "") // 去除字符串开头和结尾的逗号

            rptStepParam.ceStepList = rptStepParam.ceStep.replaceAll(/^,|,$/g, "").split(",")
          } else {
            rptStepParam.ceStepList = []
          }

          if (Array.isArray(rptStepParam.dchCeStepStrList)) {
            rptStepParam.dchCeStepList = Array.from({length:rptStepParam.dchCeStepStrList.length}, () => ([]))
            for (let j = 0; j < rptStepParam.dchCeStepStrList.length; j++) {
              rptStepParam.dchCeStepStrList[j] = (rptStepParam.dchCeStepStrList[j] + "")
                  .replaceAll(/[\t\n\s]/g, ',').replaceAll(/[^0-9,]/g, ',') // 替换所有非法字符为英文逗号
                  .replaceAll(/,+/g, ",") // 将连续的逗号替换为单个逗号
                  // .replaceAll(/^,|,$/g, "") // 去除字符串开头和结尾的逗号

              rptStepParam.dchCeStepList[j] = rptStepParam.dchCeStepStrList[j].replaceAll(/^,|,$/g, "").split(",")
            }
          } else {
            rptStepParam.dchCeStepList = []
          }
          if (rptStepParam.chCeStep) {
            rptStepParam.chCeStep = (rptStepParam.chCeStep + "")
                .replaceAll(/[\t\n\s]/g, ',').replaceAll(/[^0-9,]/g, ',') // 替换所有非法字符为英文逗号
                .replaceAll(/,+/g, ",") // 将连续的逗号替换为单个逗号
                // .replaceAll(/^,|,$/g, "") // 去除字符串开头和结尾的逗号

            rptStepParam.chCeStepList = rptStepParam.chCeStep.replaceAll(/^,|,$/g, "").split(",")
          } else {
            rptStepParam.chCeStepList = []
          }
        }
      }
    },

  }
}
</script>

<style lang="less" scoped>
:root {
  --height: calc(100vh - 42px - 8*2px - 8px - 37px - 2*8px);;
}

// 通用
.mt5 {
  margin-top: 5px;
}

.mr5 {
  margin-right: 5px;
}

.mt10 {
  margin-top: 10px;
}

.ml10 {
  margin-left: 10px;
}

.mr10 {
  margin-right: 10px;
}

h3 {
  font-size: 15px;
  font-weight: bold;
  padding: 0;
  margin: 0;
}

.flex-sb-center-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cycle-div {
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  width: calc(100vw - 40px - 12*2px);
}

.left-content {
  min-height: var(--height);
  width: 50%;
}

/deep/ .left-content .ant-table-body {
  height: calc(var(--height) - 2*10px - 32px - 10px - 26px - 35px + 30px);
  overflow: auto;
}

.right-content {
  width: 50%;
}

/deep/ .right-content .ant-table-body {
  min-height: 60px;
  overflow: auto;
  overflow-y: hidden;
}

.block {
  height: fit-content;
  padding: 10px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
  position: relative;
}

/deep/ .ant-table-thead > tr > th {
  padding: 5px !important;
  font-size: 13px !important;
}

/deep/ .ant-table-tbody > tr > td {
  padding: 0px !important;
  height: 24px !important;
  font-size: 12px !important;
}

/deep/ .left-content .ant-table-placeholder {
  border: none !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 0;
}

/deep/ .ant-empty-normal {
  margin: -2px 0;
}

/deep/ .ant-empty-image {
  display: none;
}

/deep/ .ant-table-row-expand-icon {
  margin-right: 0px;
}

/deep/ .ant-table-footer {
  padding: 0;
}

/*
/deep/ .right-content .ant-table-body::-webkit-scrollbar {
  height: 8px;
  width: 6px;
}
*/

/* /deep/ .left-content .ant-table-body::-webkit-scrollbar {
  height: 8px;
  width: 6px;
}

/deep/ .right-content .ant-table-body::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;

  background: #dddbdb;
}

/deep/ .right-content .ant-table-body::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: #f1f1f1;
} */

.footer-btn {
  width: 100%;
  height: 32px;
  border: 1px solid #e8e8e8;
  background: #fff;
  color: #999;
  font-size: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.footer-btn:hover {
  color: #1890ff;
}

/deep/ .ant-form-item {
  margin-bottom: 0;
}

/deep/ .flex-sb-center-row .ant-input-number {
  width: 130px;
}

.input {
  width: 100%;
  text-align: center;
  font-size: 12px;
  height: 28px;
  border-radius: 4px;
}

/deep/ #cycComputeTable .ant-input-number .ant-input-number-input {
  text-align: center;
}

.info-row-div {
  display: flex;
  flex-wrap: wrap;
}

.label-span {
  width: 80px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-right: 10px;
  margin-top: 10px;
}

.number-input {
  width: 100px;
  text-align: left;
  margin-top: 10px;
}

/deep/ #outTable .ant-table-thead > tr > th {
  padding: 2px 0 !important;
  font-size: 12px !important;
}

/* /deep/.left-content .ant-table-header colgroup col:last-child {

  min-width: 48.3px !important;
} */
/deep/.left-content .ant-table-body {
  border: 1px solid #e8e8e8;
}

.group-button-div {
  min-width: 20px;
  text-align: center;
}

/deep/.ant-table-thead {
	position: sticky;
	top: 0;
	z-index: 1;
}

/deep/ .ant-table-placeholder {
	border: none !important;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}
</style>