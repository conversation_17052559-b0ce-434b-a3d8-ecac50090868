<template>
  <div class="fitting-results">
    <h5>拟合结果</h5>

    <a-empty v-if="!fittingResults" description="暂无拟合结果" />

    <template v-else>
      <h6 class="metrics-title">拟合误差</h6>
      <div class="metrics-cards">
        <a-card class="metric-card" size="small">
          <template slot="title">
            <span class="metric-title">MAE</span>
          </template>
          <div class="metric-value">{{ formatMetricValue(fittingResults.mae) }}%</div>
        </a-card>

        <a-card class="metric-card" size="small">
          <template slot="title">
            <span class="metric-title">RMSE</span>
          </template>
          <div class="metric-value">{{ formatMetricValue(fittingResults.rmse) }}%</div>
        </a-card>

        <a-card class="metric-card" size="small">
          <template slot="title">
            <span class="metric-title">最后1个实测值的平均误差</span>
          </template>
          <div class="metric-value">{{ formatMetricValue(fittingResults.last_point_error) }}%</div>
        </a-card>

        <a-card class="metric-card" size="small">
          <template slot="title">
            <span class="metric-title">预测1个数据点的平均误差</span>
          </template>
          <div class="metric-value">{{ formatMetricValue(fittingResults.pred_point_error) }}%</div>
        </a-card>
      </div>

      <div v-if="fittingResults.optimized_params" class="params-section">
        <h6 class="params-title">拟合参数详情</h6>
        <a-tabs :defaultActiveKey="firstGroupKey" class="unified-tabs coefficient-tabs fitting-tabs" @change="handleFittingTabChange">
          <a-tab-pane
            v-for="(group, groupIndex) in groupedFittingParams"
            :key="groupIndex"
            :tab="`${group.letter}组`"
          >
            <a-table
              :columns="fittedParamsColumns"
              :dataSource="group.params"
              :rowKey="record => record.key"
              size="small"
              :pagination="false"
            >
              <template slot="name" slot-scope="text">
                <div class="latex-cell formula-container">
                  <div class="formula-loading"></div>
                  <div v-html="renderLatex(text)"></div>
                </div>
              </template>
              <template slot="value" slot-scope="text">
                <div class="coefficient-value">{{ text }}</div>
              </template>
            </a-table>
          </a-tab-pane>
        </a-tabs>

        <div class="action-button">
          <a-button type="primary" @click="viewDetailedResults" block>
            查看详细结果
          </a-button>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import { renderMathJax } from '@/utils/mathUtils';
import fittingMixin from '@/mixins/fittingMixin';

export default {
  name: 'FittingResults',
  mixins: [fittingMixin],
  props: {
    fittingResults: {
      type: Object,
      default: null
    },
    dataPoints: {
      type: Array,
      default: () => []
    },
    temperatures: {
      type: Array,
      default: () => []
    },
    socs: {
      type: Array,
      default: () => []
    }
  },
  data: () => ({
    fittedParamsColumns: [
      {
        title: '系数',
        dataIndex: 'name',
        key: 'name',
        width: '30%',
        scopedSlots: { customRender: 'name' },
      },
      {
        title: '拟合值',
        dataIndex: 'value',
        key: 'value',
        width: '70%',
        scopedSlots: { customRender: 'value' },
      }
    ]
  }),
  computed: {
    groupedFittingParams() {
      if (!this.fittingResults?.optimized_params) return [];

      const params = this.fittingResults.optimized_params.map(param => ({
        ...param,
        name: this.convertToLatex(param.name)
      }));

      const groups = {};
      params.forEach(param => {
        const letter = param.name.charAt(0).toUpperCase();
        if (!groups[letter]) {
          groups[letter] = { letter, params: [] };
        }
        groups[letter].params.push({
          key: `${letter}_${param.name}_${groups[letter].params.length}`,
          name: param.name,
          value: param.value
        });
      });

      return Object.values(groups).sort((a, b) => a.letter.localeCompare(b.letter));
    },
    firstGroupKey() {
      return '0';
    }
  },
  watch: {
    groupedFittingParams() {
      this.$nextTick(renderMathJax);
    },
    'fittingResults.optimized_params': {
      handler() {
        this.$nextTick(renderMathJax);
      },
      immediate: true
    }
  },
  methods: {
    handleFittingTabChange() {
      this.$nextTick(renderMathJax);
    },
    viewDetailedResults() {
      if (!this.fittingResults?.optimized_params) {
        this.$message.warning('没有可用的拟合结果数据');
        return;
      }
      if (!this.dataPoints?.length) {
        this.$message.warning('没有可用的数据点，请先上传数据文件');
        return;
      }
      if (!this.temperatures?.length || !this.socs?.length) {
        this.$message.warning('温度或SOC数据不完整，请确保数据文件包含有效数据');
        return;
      }
      this.$emit('view-detailed-results');
    }
  },
  mounted() {
    this.$nextTick(renderMathJax);
  }
};
</script>

<style scoped>
.fitting-results {
  width: 100%;
}

.metrics-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.metric-card {
  text-align: center;
}

.metric-title {
  font-weight: 500;
  font-size: 14px;
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
}

.params-section {
  margin-top: 16px;
}

.params-title, .metrics-title {
  font-weight: 600;
  margin-bottom: 8px;
  text-align: center;
  font-size: 16px;
}

.metrics-title {
  margin-top: 20px;
  color: #333;
}

.action-button {
  text-align: center;
  margin-top: 16px;
}

.latex-cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.coefficient-value {
  min-height: 24px;
  display: flex;
  align-items: center;
  padding: 4px 0;
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
}
</style>