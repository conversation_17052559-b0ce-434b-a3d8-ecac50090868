<template>
  <div class="wrapper" :style="{padding: padding}">
    <div class="flex-sb-center-row">
      <div class="head_title">G26日历寿命报告建模{{ reportName ? "《" + reportName + "》" : '' }}</div>
      <div class="btn-wrap mt5">
        <div>
          <a-dropdown @visibleChange="handleScheduleBtn">
            <a-menu slot="overlay">
              <a-menu-item v-for="(item,index) in menuOptions" :key="item.key"
                           v-show="item.percent > 0 || index <= completeNum" style="width: 400px;">
                <div class="menu-content">
                  <span style="width: 170px;">{{ item.title }}</span>
                  <a-progress :percent="item.percent" style="width: 170px;"/>
                  <a-button :disabled="index === templateActivity" type="link"
                            @click="handleChangeTemplate(index)">填写
                  </a-button>
                </div>
              </a-menu-item>
            </a-menu>
            <a-button class="mr10">填写进度</a-button>
          </a-dropdown>
        </div>
        <a-button class="mr10" @click="handleOpen('isShowHistorical')">历史记录</a-button>
        <a-button class="mr10" @click="handleOpen('isShowDrafts')">草稿箱</a-button>
        <div style="position: relative;">
          <div class="normal-btn" :class="{'streamer-btn anima': isAllOk}" @click="handleAllOk">
            完成模型搭建
          </div>
        </div>
      </div>
    </div>

    <div class="center-wrapper block mt5">
      <div class="percent-content" v-if="!isCloseFour">
        <div class="percent-block" v-for="(item,index) in menuOptions" :key="item.key"
             v-show="item.percent > 0 || index <= completeNum"
             :class="{ 'percent-block-check': index === templateActivity}" @click="handleChangeTemplate(index)">
          <a-progress type="circle" :percent="item.percent" :width="70"/>
          <div class="mt5">{{ item.title }}</div>
        </div>
        <div class="shrink-btn" @click="handleClose(4)">收起<a-icon style="transform: rotate(90deg);margin-left: 4px;" type='double-left'/></div>
      </div>
      <div class="percent-content" v-else>
        <a-progress :percent="percentTotal" class="progress-block" />
        <div class="shrink-btn" @click="handleClose(4)">展开<a-icon style="transform: rotate(90deg);margin-left: 4px;" type='double-right'/></div>
      </div>
    </div>

    <div class="bottom-wrapper mt10">
      <div class="block" style="width: 100%;">
        <div class="flex-sb-center-row">
          <a-icon @click="handleClose(1)" :type="isCloseOne ? 'down' : 'up'"/>
        </div>
        <div class="flex-sb-center-row" v-show="!isCloseOne">
          <div class="top-wrapper ml10">
            <a-form-item label="温度：" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <div class="top-wrapper">
                <a-input-number placeholder="请输入..."
                                style="width: 90px; border-radius: 4px;"
                                v-model="reportData.templateList[templateActivity].temperature"
                                @change="handleInput" @blur="$event => handleBlur($event)"/>
                <span style="margin-left: 4px;">℃</span>
              </div>
            </a-form-item>
            <div style="width: 50px;"></div>
            <a-form-item label="SOC：" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <div class="top-wrapper">
                <a-input-number placeholder="请输入..."
                                style="width: 90px; border-radius: 4px;"
                                v-model="reportData.templateList[templateActivity].soc"
                                @change="handleInput" @blur="$event => handleBlur($event)"/>
                <span style="margin-left: 4px;">%</span>
              </div>
            </a-form-item>
          </div>
          <div class="normal-btn mr10" @click="clearSample">清空样品数据</div>
        </div>

        <a-table v-show="!isCloseOne" class="rpt-table mt10" bordered :columns="rptColumns" :rowKey="record => record.rpt"
                 :data-source="reportData.templateList[templateActivity].rptData"
                 :pagination="paginationConfig"
                 ref="rptDataTable">

          <template slot="day" slot-scope="text, record, index, columns">
            <a-input-number class="input-number"
                            v-model="reportData.templateList[templateActivity].rptData[record.rpt].day" allow-clear
                            @change="handleInput" @blur="$event => handleBlur($event)"/>
          </template>
          <template slot="rechargeDay" slot-scope="text, record, index, columns">
            <a-input-number class="input-number"
                            v-model="reportData.templateList[templateActivity].rptData[record.rpt].rechargeDay" allow-clear
                            @change="handleInput" @blur="$event => handleBlur($event)"/>
          </template>
          <template slot="select" slot-scope="text, record, index, columns">
            <a-tooltip title="选择">
              <a-icon class="btn-icon ml2" type="edit" @click="selectSample(record.rpt)"/>
            </a-tooltip>
            <!--            <a-tooltip title="删除">-->
            <!--              <a-icon class="btn-icon ml2" type="delete" @click="deleteSample(record.rpt)"/>-->
            <!--            </a-tooltip>-->
          </template>
          <template slot="sample1" slot-scope="text, record, index, columns">
            <a @click="openStepData(record.sample1)" style="text-align: center" v-show="record.sample1">{{
                record.sample1 ?
                  record.sample1.celltestcode + (record.sample1.day ? " (" + record.sample1.day + ")" : "") : ""
              }}</a>
          </template>
          <template slot="sample2" slot-scope="text, record, index, columns">
            <a @click="openStepData(record.sample2)" style="text-align: center" v-show="record.sample2">{{
                record.sample2 ?
                  record.sample2.celltestcode + (record.sample2.day ? " (" + record.sample2.day + ")" : "") : ""
              }}</a>
          </template>
          <template slot="voltageStep" slot-scope="text, record, index, columns">
            <a-input-number v-if="record.rpt === 0" class="input-number" :disabled="record.rpt > 0"
                            v-model="reportData.templateList[templateActivity].voltageStep" allow-clear
                            @change="handleInput" @blur="$event => handleBlur($event)"/>
            <a-input-number v-else class="input-number" :disabled="record.rpt > 0"/>
          </template>
          <template slot="capacityEnergyStep" slot-scope="text, record, index, columns">
            <a-input class="input-number"
                     v-model="reportData.templateList[templateActivity].rptData[record.rpt].capacityEnergyStep"
                     @change="handleInput" @blur="$event => handleBlur($event)"
                     @keyup="verifyStepNumber(record.rpt, 'capacityEnergyStep')"
                     @paste="copyFromExcel($event, columns.dataIndex, record.rpt)"/>
          </template>
          <template slot="holdStep" slot-scope="text, record, index, columns">
            <a-input class="input-number"
                     v-model="reportData.templateList[templateActivity].rptData[record.rpt].holdStep"
                     @change="handleInput" @blur="$event => handleBlur($event)"
                     @keyup="verifyStepNumber(record.rpt, 'holdStep')"
                     @paste="copyFromExcel($event, columns.dataIndex, record.rpt)"/>
          </template>
          <template slot="dischargeStep" slot-scope="text, record, index, columns">
            <a-input class="input-number"
                     v-model="reportData.templateList[templateActivity].rptData[record.rpt].dischargeStep"
                     @change="handleInput" @blur="$event => handleBlur($event)"
                     @keyup="verifyStepNumber(record.rpt, 'dischargeStep')"
                     @paste="copyFromExcel($event, columns.dataIndex, record.rpt)"/>
          </template>
          <template slot="dchStepTime" slot-scope="text, record, index, columns">
            <a-input class="input-number"
                     v-model="reportData.templateList[templateActivity].rptData[record.rpt].dchStepTime"
                     @blur="$event => handleBlur($event)"
                     @keyup="verifyStepNumber(record.rpt, 'dchStepTime')"
                     @paste="copyFromExcel($event, columns.dataIndex, record.rpt)"/>
          </template>
          <template slot="dchStepTimeTitle">
            放电工步时间
            <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
              <a-icon type="question-circle" style="color: #1890ff;"/>
              <template slot="title">
                  <span>
                    未填写放电工步时间默认取工步数据表数据；<br/>
                    填写了放电工步时间则取详细数据表数据；<br/>
                    格式：HH:mm:ss.SSS；例如：0:00:10.000
                  </span>
              </template>
            </a-tooltip>
          </template>

          <template slot="standardStep" slot-scope="text, record, index, columns">
            <a-radio :checked="record.rpt == reportData.templateList[templateActivity].standardStepIndex" @change="onRadioChange(record)"></a-radio>
          </template>

          <template slot="standardStepTitle">
            基准工步
            <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
              <a-icon class="tips" type="question-circle" />
              <template slot="title">
								<span>
									未填写的工步号以选中的为基准进行数据建模；<br />
                  工步号可从Excel复制（结构需与PBI一致，为四列信息）
								</span>
              </template>
            </a-tooltip>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 完成模型搭建弹窗 -->
    <a-modal title="完成模型搭建" :width="350" :visible="isShowReportName" @cancel="handleCancel('isShowReportName')">
      <template slot="footer">
        <div class="btn-wrap">
          <a-button @click="handleCancel('isShowReportName')">
            取消
          </a-button>
          <a-button type="primary" @click="exportData">
            完成模型搭建
          </a-button>
        </div>
      </template>
      <div class="phase-modal">
        <span class="mr10">报告名称:</span>
        <a-input v-model="reportName" style="width: 200px;" placeholder="请填写报告名称" @keyup.enter="exportData" />
      </div>
    </a-modal>

    <!-- 测试项目选择弹窗 -->
    <a-modal title="测试项目选择" :width="1200" :height="600" :bodyStyle="{ padding: 0 }" :visible="visible"
             style="padding: 0" :maskClosable="false" @cancel="handleCancel('visible')">

      <pbiSearchContainer>
        <pbiSearchItem :span="4" label='委托单号'>
          <a-input v-model="queryParam.folderno" @keyup.enter="$refs.table.refresh()"
                   @change="$refs.table.refresh()"/>
        </pbiSearchItem>
        <pbiSearchItem :span="4" label='主题'>
          <a-input v-model="queryParam.theme" @keyup.enter="$refs.table.refresh()"
                   @change="$refs.table.refresh()"/>
        </pbiSearchItem>
        <pbiSearchItem :span="4" label='测试项目别名'>
          <a-input v-model="queryParam.alias" @keyup.enter="$refs.table.refresh()"
                   @change="$refs.table.refresh()"/>
        </pbiSearchItem>
        <pbiSearchItem :span="4" label='存储天数'>
          <a-input v-model="queryParam.day" @keyup.enter="$refs.table.refresh()"
                   @change="$refs.table.refresh()"/>
        </pbiSearchItem>
        <pbiSearchItem :span="4" label='测试编码'>
          <a-input v-model="queryParam.celltestcode" @keyup.enter="$refs.table.refresh()"
                   @change="$refs.table.refresh()"/>
        </pbiSearchItem>
        <pbiSearchItem :span="4" type='btn'>
          <a-button class="mr8" @click="$refs.table.refresh()" type="primary">查询</a-button>
          <a-button class="mr8" @click="tableReset()" >重置</a-button>
        </pbiSearchItem>
      </pbiSearchContainer>

      <s-table :columns="columns" :data="loadData" bordered :rowKey="record1 => record1.uuid" ref="table"
               :row-selection="{
					columnWidth: 30,
					selectedRows: selectedRows,
					selectedRowKeys: selectedRowKeys,
					onSelect: onSelectChange,
				}"
      >
        <template slot="celltestcode" slot-scope="text, record, index, columns">
          <a @click="openStepData(record)" v-if="record.flowId != null" style="text-align: center">{{ text }}</a>
          <span v-else style="text-align: center">{{ text }}</span>
        </template>

        <template slot="action1" slot-scope="text, record, index, columns">
          <template v-if="record.showHide">
            <a @click="showData(record)" v-if="record.showHide" style="text-align: center">初始化</a>
            <a-divider v-if="record.children != null" type="vertical"/>
          </template>

          <a @click="hideData(record)" v-if="record.children != null || record.isChild"
             style="text-align: center">隐藏</a>
        </template>

        <template
          slot="theme"
          slot-scope="text, record, index, columns">
          <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
            <template slot="title">
              {{ text }}
            </template>
            {{ text }}
          </a-tooltip>
        </template>
        <template
          slot="dataPath"
          slot-scope="text, record, index, columns">
          <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
            <template slot="title">
              {{ text ? text : record.testcontent }}
            </template>
            {{ text ? text : record.testcontent }}
          </a-tooltip>
        </template>
      </s-table>
      <template slot="footer">
        <a-button type="primary" @click="handleCancel('visible')">
          确认
        </a-button>
        <a-button key="back" @click="handleCancel('visible')">
          关闭
        </a-button>
      </template>
    </a-modal>

    <!-- 历史记录弹窗 -->
    <a-modal title="历史记录" :width="1200" :height="600" :bodyStyle="{ padding: 0 }" :visible="isShowHistorical"
             style="padding: 0" :maskClosable="false" @cancel="handleCancel('isShowHistorical')">

      <pbiSearchContainer>
        <pbiSearchItem :span="6" label='报告状态'>
          <a-select v-model="queryHistoryParam.fileStatus" @change="$refs.historyTable.refresh()"
                    style="width: 100%" :allow-clear="true">
            <a-select-option value="0">
              待处理
            </a-select-option>
            <a-select-option value="10">
              进行中
            </a-select-option>
            <a-select-option value="20">
              已完成
            </a-select-option>
            <a-select-option value="30">
              数据异常
            </a-select-option>
          </a-select>
        </pbiSearchItem>
        <pbiSearchItem :span="6" label='创建人'>
          <a-input v-model="queryHistoryParam.createName" @keyup.enter="$refs.historyTable.refresh()"
                   @change="$refs.historyTable.refresh()"/>
        </pbiSearchItem>
        <pbiSearchItem :span="12" type='btn'>
          <a-button class="mr8" @click="$refs.historyTable.refresh()" type="primary">查询</a-button>
          <a-button class="mr8" @click="historyReset()" >重置</a-button>
        </pbiSearchItem>
      </pbiSearchContainer>

      <s-table :columns="historyColumns" :data="historyData" bordered :rowKey="record1 => record1.id"
               ref="historyTable">

        <template slot="action" slot-scope="text, record, index, columns">
          <a-button type="link" @click="handleCopyHistory(record)">复制</a-button>
        </template>

        <template slot="template" slot-scope="text, record, index, columns">
          <a style="text-align: center" v-if="record.fileStatus == 20"
             @click="g26DownloadFile(record,(record.reportName ? record.reportName + ' ' : '') + 'Calendar Aging template')">
            <a-spin :spinning="record.status == 1" tip="正在下载中" size="small">
              Calendar Aging template
            </a-spin>
          </a>
        </template>

        <template slot="reportData" slot-scope="text, record, index, columns">
          <a style="text-align: center" v-if="record.dataFileStatus == 20" :href="'http://***********:8282/sysFileInfo/download?id='+record.dataFileId
              +'&userAccount='+userInfo.account+'&userName='+userInfo.name" download>
            raw data
          </a>
        </template>

      </s-table>
      <template slot="footer">
        <a-button key="back" @click="handleCancel('isShowHistorical')">
          关闭
        </a-button>
      </template>
    </a-modal>

    <!-- 草稿箱弹窗 -->
    <a-modal title="草稿箱" :width="1200" :height="600" :bodyStyle="{ padding: 0 }" :visible="isShowDrafts"
             style="padding: 0" :maskClosable="false" @cancel="handleCancel('isShowDrafts')">

      <pbiSearchContainer>
        <pbiSearchItem :span="6" label='创建人'>
          <a-input v-model="queryDraftParam.createName" @keyup.enter="$refs.draftTable.refresh()"
                   @change="$refs.draftTable.refresh()"/>
        </pbiSearchItem>
        <pbiSearchItem :span="18" type='btn'>
          <a-button class="mr8" @click="$refs.draftTable.refresh()" type="primary">查询</a-button>
          <a-button class="mr8" @click="draftReset()" >重置</a-button>
        </pbiSearchItem>
      </pbiSearchContainer>

      <s-table :columns="draftColumns" :data="draftData" bordered :rowKey="record1 => record1.id"
               ref="draftTable">
        <template slot="action" slot-scope="text, record, index, columns">
          <a-button type="link" @click="handleDraf(record)">编辑</a-button>
        </template>
      </s-table>
      <template slot="footer">
        <a-button key="back" @click="handleCancel('isShowDrafts')">
          关闭
        </a-button>
      </template>
    </a-modal>

    <step-data ref="stepData"></step-data>

  </div>
</template>
<script>
import {mapGetters} from "vuex"
import moment from "moment"
import jsonBigint from 'json-bigint'

import stepData from "../lims/folder/stepData"
import {STable} from "@/components"

import {
  tLimsTestdataSchedulePageList,
  testReportPageList,
  hideData,
  showData,
  getTemGradientsByOrderIdAndOrdtaskId
} from "@/api/modular/system/limsManager"

import {
  commitG26Calendar, g26CalendarTestReportTask, getG26ById
} from "@/api/modular/system/g26Manager"
import axios from "axios";


export default {
  components: {
    STable,
    stepData
  },
  props: {
    width:{
      type: Number,
      default: 0
    },
    padding:{
      type: String,
      default: '0px 12px'
    }
  },
  data() {
    return {
      isShowReportName: false, // 完成模型搭建-报告名称填写弹窗
      isShowHistorical: false, //历史记录弹窗
      isShowDrafts: false, //草稿箱弹窗
      visible: false,
      confirmLoading: false,

      isCloseOne: false,
      isCloseTwo: false,
      isCloseThree: false,
      isCloseFour: false,
      isCloseFive: false,
      isCloseSix: false,
      isCloseSeven: false,
      isCloseEight: false,
      isCloseNine: false,

      testReportID: null, //该份测试报告的ID

      isReportNameType: 1,  // 1：添加  2：修改

      reportNameCheck: '',//弹窗中的报告名称
      templateActivity: 0,  //当前测试条件
      percentTotal: 0,//总的进度百分比
      outQueryFlowRecord: null,
      saveParam: null,

      menuOptions: [
        {key: 1, title: '℃ SOC', percent: 0},
        {key: 2, title: '℃ SOC', percent: 0},
        {key: 3, title: '℃ SOC', percent: 0},
        {key: 4, title: '℃ SOC', percent: 0},
        {key: 5, title: '℃ SOC', percent: 0},
        {key: 6, title: '℃ SOC', percent: 0},
        {key: 7, title: '℃ SOC', percent: 0},
        {key: 8, title: '℃ SOC', percent: 0},
        {key: 9, title: '℃ SOC', percent: 0},
        {key: 10, title: '℃ SOC', percent: 0},
        {key: 11, title: '℃ SOC', percent: 0},
        {key: 12, title: '℃ SOC', percent: 0},
        {key: 13, title: '℃ SOC', percent: 0},
        {key: 14, title: '℃ SOC', percent: 0},
        {key: 15, title: '℃ SOC', percent: 0},
        {key: 16, title: '℃ SOC', percent: 0},
        {key: 17, title: '℃ SOC', percent: 0},
        {key: 18, title: '℃ SOC', percent: 0},
        {key: 19, title: '℃ SOC', percent: 0},
        {key: 20, title: '℃ SOC', percent: 0},
        {key: 21, title: '℃ SOC', percent: 0},
        {key: 22, title: '℃ SOC', percent: 0},
      ],
      completeNum: 0,
      paginationConfig: {
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '50'], // 显示的每页数量选项
      },
      windowHeight: document.documentElement.clientHeight,
      windowWidth: document.documentElement.clientWidth,
      selectedRecordIndex: 0, // 当前rpt行索引
      sampleNum: 1, // 样品1或样品2
      daySampleList: [], // 待填充中检天数和样品列表
      rechargeDayList: [], // 待填充的补电天数列表

      reportData: {reportName: '', templateList: []}, // 报告生成数据
      reportName: '',
      reportTemplate: [], // 初始对象

      loadData: parameter => {
        this.loadDataList = []
        return tLimsTestdataSchedulePageList(Object.assign(parameter, this.queryParam)).then(res => {
          return res.data
        })
      },

      draftColumns: [
        {
          title: '序号',
          align: 'center',
          width: 50,
          customRender: (text, record, index) => index + 1
        },
        {
          title: '报告名称',
          dataIndex: 'reportName',
          align: 'center',
          width: 200,
          scopedSlots: { customRender: 'reportName' },
        },
        /*{
          title: '报告类型',
          width: 90,
          align: 'center',
          dataIndex: 'type'
        },
        {
          title: '操作类型',
          width: 90,
          align: 'center',
          dataIndex: 'operateType',
          customRender: (text, record, index) => {
            if (record.operateType == 'add') {
              return "初次创建"
            }
            if (record.operateType == 'update') {
              return "更新数据"
            }
            if (record.operateType == 'refresh') {
              return "刷新数据"
            }
          }
        },*/
        {
          title: '操作时间',
          width: 90,
          align: 'center',
          dataIndex: 'createTime',

        },
        {
          title: '操作人',
          width: 90,
          align: 'center',
          dataIndex: 'createName',

        },
        {
          title: "操作",
          align: "center",
          width: 30,
          scopedSlots: {customRender: "action"}
        },
      ],
      historyColumns: [
        {
          title: '序号',
          align: 'center',
          width: 50,
          customRender: (text, record, index) => index + 1
        },
        {
          title: '报告名称',
          dataIndex: 'reportName',
          align: 'center',
          width: 100,
        }, {
          title: 'Template',
          dataIndex: 'template',
          align: 'center',
          width: 100,
          scopedSlots: {customRender: 'template'},
        }, {
          title: '原始数据',
          dataIndex: 'reportData',
          align: 'center',
          width: 100,
          scopedSlots: {customRender: 'reportData'},
        },
        /*{
          title: '报告名称',
          dataIndex: 'reportName',
          align: 'center',
          width: 200,
          scopedSlots: { customRender: 'reportName' },
        },
        {
          title: '报告类型',
          width: 90,
          align: 'center',
          dataIndex: 'type'
        },
        {
          title: '操作类型',
          width: 90,
          align: 'center',
          dataIndex: 'operateType',
          customRender: (text, record, index) => {
            if (record.operateType == 'add') {
              return "初次创建"
            }
            if (record.operateType == 'update') {
              return "更新数据"
            }
            if (record.operateType == 'refresh') {
              return "刷新数据"
            }
          }
        },*/
        {
          title: '操作时间',
          width: 90,
          align: 'center',
          dataIndex: 'createTime',

        },
        {
          title: '操作人',
          width: 90,
          align: 'center',
          dataIndex: 'createName',

        },
        {
          title: "操作",
          align: "center",
          width: 30,
          scopedSlots: {customRender: "action"}
        },
      ],
      historyData: parameter => {
        return testReportPageList(Object.assign(parameter, this.queryHistoryParam)).then((res) => {
          return res.data
        })
      },
      draftData: parameter => {
        return testReportPageList(Object.assign(parameter, this.queryDraftParam)).then((res) => {
          return res.data
        })
      },

      labelCol: {
        sm: {
          span: 6
        }
      },
      wrapperCol: {
        sm: {
          span: 16
        }
      },
      queryParam: {},
      queryHistoryParam: {
        type: "G26Calendar"
      },
      queryDraftParam: {
        fileStatus: -10,
        type: 'G26Calendar'
      },
      data: [],

      // rpt数据填写表头
      rptColumns: [
        {
          title: "RPT",
          align: "center",
          width: "5%",
          dataIndex: "rpt",
          // customRender: (text, record, index) => index
        },
        {
          title: "Days",
          align: "center",
          width: "8%",
          dataIndex: "day",
          scopedSlots: {customRender: "day"}
        },
        {
          title: "Recharge Days",
          align: "center",
          width: "8%",
          dataIndex: "rechargeDay",
          scopedSlots: {customRender: "rechargeDay"}
        },
        {
          title: "样品选择",
          align: "center",
          children: [
            {
              title: "选择",
              align: "center",
              width: "5%",
              scopedSlots: {customRender: "select"}
            },
            {
              title: "样品1",
              align: "center",
              width: "14%",
              scopedSlots: {customRender: "sample1"}
            },
            {
              title: "样品2",
              align: "center",
              width: "14%",
              scopedSlots: {customRender: "sample2"}
            }
          ]
        },
        {
          title: "电压工步号",
          align: "center",
          width: "8%",
          scopedSlots: {customRender: "voltageStep"}
        },
        {
          title: "Capacity/Energy工步号",
          align: "center",
          width: "8%",
          dataIndex: "capacityEnergyStep",
          scopedSlots: {customRender: "capacityEnergyStep"}
        },
        {
          title: "DCIR搁置工步号",
          align: "center",
          width: "8%",
          dataIndex: "holdStep",
          scopedSlots: {customRender: "holdStep"}
        },
        {
          title: "DCIR放电工步号",
          align: "center",
          width: "8%",
          dataIndex: "dischargeStep",
          scopedSlots: {customRender: "dischargeStep"}
        },
        {
          align: "center",
          width: "8%",
          dataIndex: "dchStepTime",
          scopedSlots: {
            customRender: "dchStepTime",
            title: "dchStepTimeTitle"
          }
        },
        {
          align: "center",
          width: "6%",
          scopedSlots: {
            customRender: "standardStep",
            title: "standardStepTitle"
          }
        },
      ],
      columns: [
        {
          title: "操作",
          align: "center",
          width: 70,
          scopedSlots: {customRender: "action1"}
        },
        {
          title: "序号",
          align: "center",
          width: 40,
          customRender: (text, record, index) => {
            if (!record.isChild) {
              return index + 1
            }
          }
        },
        {
          title: "委托单号",
          dataIndex: "folderno",
          align: "center",
          width: 90
        },
        {
          title: "主题",
          dataIndex: "theme",
          align: "center",
          ellipsis: true,
          width: 90,
          scopedSlots: {customRender: "theme"}
        },
        {
          title: "样品编号",
          width: 90,
          align: "center",
          dataIndex: "orderno"
        },
        {
          title: "测试项目编码",
          width: 90,
          align: "center",
          dataIndex: "testcode"
          //scopedSlots: {customRender: 'updateText'},
        },

        {
          title: "测试项目别名",
          width: 90,
          align: "center",
          dataIndex: "alias"
          //scopedSlots: {customRender: 'updateText'},
        },
        {
          title: "测试编码",
          width: 90,
          align: "center",
          dataIndex: "celltestcode",
          scopedSlots: {customRender: "celltestcode"}
        },
        {
          title: "数据位置",
          width: 60,
          align: "center",
          dataIndex: "dataPath",
          ellipsis: true,
          scopedSlots: {customRender: "dataPath"}
        },
        {
          title: "开始时间",
          width: 80,
          align: "center",
          dataIndex: "startTime",
          customRender: (text, record, index) => {
            if (null != text) {
              return moment(text).format("YYYY-MM-DD")
            }
            return text
          }
          //
          //scopedSlots: {customRender: 'updateText'},
        },
        {
          title: "结束时间",
          width: 80,
          align: "center",
          dataIndex: "endTime",
          customRender: (text, record, index) => {
            if (null != text) {
              return moment(text).format("YYYY-MM-DD")
            }
            return text
          }
        },
        {
          title: "设备编号",
          width: 60,
          align: "center",
          dataIndex: "equiptcode"
        },
        {
          title: "通道编号",
          width: 60,
          align: "center",
          dataIndex: "channelno"
        },
        {
          title: "存储天数",
          width: 75,
          align: "center",
          dataIndex: "day"
        },
        {
          title: "温度",
          width: 40,
          align: "center",
          dataIndex: "tem"
        },{
          title: "soc",
          width: 40,
          align: "center",
          dataIndex: "soc"
        },
      ],
      form: this.$form.createForm(this),
      selectedRowKeys: [],
      selectedRows: [],
    }
  },
  computed: {
    ...mapGetters(["testTaskFilterData", "testTaskId", "userInfo"]),
    isAllOk() {
      return this._handleVerify(this.reportData)[0]
    }
  },
  mounted() {
  },
  created() {
    this.reportTemplate = []
    let rptData = []
    for (let i = 0; i < 31; i++) {
      rptData.push({rpt: i})
    }
    for (let i = 0; i < 22; i++) {
      this.reportTemplate.push({
        rptData: rptData
      })
    }

    // 刷新页面
    if (window.sessionStorage.getItem('testReportID')) {
      this.testReportID = window.sessionStorage.getItem('testReportID')
      return this.getG26ById({id: window.sessionStorage.getItem('testReportID')})
    }

    // 在测试报告窗口新增
    if (this.testTaskId !== null) {
      this.reportData = {
        reportName: this.testTaskId,
        templateList: JSON.parse(JSON.stringify(this.reportTemplate))
      }
      this.g26CalendarTestReportTask(this.reportData)
      this.$store.commit("setTaskID", null)
      return
    }

    // 如果是重新生成
    if (this.testTaskFilterData !== null) {
      this.reportName = this.testTaskFilterData.reportName
      let json = jsonBigint({storeAsString: true})
      this.g26CalendarTestReportTask(json.parse(this.testTaskFilterData.queryParam))
      this.$store.commit("setTaskFilterData", null)
      return
    }

    // 如果没有数据，则初始化
    this.reportData = {reportName: '', templateList: JSON.parse(JSON.stringify(this.reportTemplate))}

  },
  destroyed() {
    window.sessionStorage.removeItem('testReportID')
  },

  methods: {

    historyReset() {
      this.queryHistoryParam = {type: 'G26Calendar'}
      this.$refs.historyTable.refresh()
    },
    draftReset() {
      this.queryDraftParam = {fileStatus: -10, type: 'G26Calendar'}
      this.$refs.draftTable.refresh()
    },
    tableReset() {
      this.queryParam = {}
      this.$refs.table.refresh()
    },
    g26DownloadFile(record, name, fileId, suffix) {
      record.status = 1
      let url = 'http://***********:8282/sysFileInfo/downloadWithAuth'
      axios({
        url: url,
        method: 'POST',
        //headers:this.headers,
        data: {
          id: fileId != null ? fileId : record.fileId,
          userAccount: this.userInfo.account,
          userName: this.userInfo.name
        },
        responseType: 'blob' // 设置响应类型为blob
      })
        .then(response => {
          if (response.data.size < 500) {
            location.reload()
            return
          }
          const url = window.URL.createObjectURL(new Blob([response.data]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', name + (suffix != null ? suffix : '.xlsx')); // 设置下载文件的名称和扩展名
          document.body.appendChild(link);
          link.click();
          record.status = 0
        })
        .catch(error => {
          record.status = 0
        });
    },

    // G26-日历寿命报告任务新增、暂存或实时修改
    g26CalendarTestReportTask(parameter, id = null) {
      // 如果没有id就是新增
      // 如果有id就是暂存或实时修改
      let json = jsonBigint({storeAsString: true})
      const params = json.parse(JSON.stringify(parameter))
      g26CalendarTestReportTask(params, id).then(res => {
        // 新建
        if (id == null) {
          this.testReportID = res.data.id
          window.sessionStorage.setItem("testReportID", this.testReportID)

          this.getG26ById({
            id: this.testReportID
          })
        }
      })
    },

    // G26-根据id查询报告任务
    getG26ById(parameter) {
      getG26ById(parameter).then(res => {
        let json = jsonBigint({storeAsString: true})
        this.reportData = json.parse(res.data.queryParam)
        this.reportName = this.reportData.reportName

        this._handleProgress()
      })
    },

    handleInput() {
      this._handleProgress()
    },

    verifyStepNumber(rptIndex, type) {
      if (this.reportData.templateList[this.templateActivity].rptData[rptIndex][type]) {
        this.reportData.templateList[this.templateActivity].rptData[rptIndex][type] = (this.reportData.templateList[this.templateActivity].rptData[rptIndex][type] + "").replaceAll(type == "dchStepTime" ? /[^0-9:.]/g : /[^0-9]/g, "")
      }
    },

    handleBlur() {
      // 实时保存
      this.g26CalendarTestReportTask(this.reportData, this.testReportID)
    },

    copyFromExcel(event, dataIndex, rptIndex) {
      // excel复制末尾会有换行符，split后数组多一个空串，先去除
      let rows = event.clipboardData.getData("text").replace(/[\n]$/, "").split("\n")

      let startColumn // 起始列
      let dcirColumn // DCIR放电工步时间所在列
      this.rptColumns.forEach((column, index) => {
        // dataIndex所在列
        if (column.dataIndex == dataIndex) {
          startColumn = index
        }
        // DCIR放电工步时间所在列
        if (column.dataIndex == "dchStepTime") {
          dcirColumn = index
        }
      })

      let firstData
      // 起始行：rptIndex，结束行：math.min(rptIndex + text.length, 31)
      for (let i = rptIndex; i <= 30 && i < rptIndex + rows.length; i++) {

        let rowList = rows[i-rptIndex].split("\t")
        if (i == rptIndex) {
          firstData = rowList[0].replaceAll(startColumn == dcirColumn ? /[^0-9:.]/g : /[^0-9]/g, "")
        }
        // 起始列：dataIndex所在列，结束列：math.min(dataIndex所在列+rowList.length, DCIR放电工步时间所在列)
        for (let j = startColumn; j <= dcirColumn && j < startColumn + rowList.length; j++) {
          this.reportData.templateList[this.templateActivity].rptData[i][this.rptColumns[j].dataIndex] = rowList[j-startColumn].replaceAll(j == dcirColumn ? /[^0-9:.]/g : /[^0-9]/g, "")
        }
      }

      // 解决第一个单元格被覆盖的问题
      setTimeout(() => {
        this.reportData.templateList[this.templateActivity].rptData[rptIndex][dataIndex] = firstData
        // 强制刷新页面
        this.$forceUpdate()
      }, 10)
    },

    onRadioChange(record) {
      // 提示当前行未选择样品
      // if (!record.sample1) {
      //   return this.$message.warn("请先选择当前行的样品数据")
      // }

      // 选择当前行索引为基准工步索引
      this.reportData.templateList[this.templateActivity].standardStepIndex = record.rpt

      this._handleProgress()
      // 实时保存
      this.g26CalendarTestReportTask(this.reportData, this.testReportID)
    },

    /*
    * 填写进度按钮事件
    */

    // 进度按钮触发事件
    handleScheduleBtn(value) {
      if (value) {
        this._handleProgress()
      }
    },

    /*
    * 历史记录按钮事件
    */
    // 复制
    handleCopyHistory(record) {
      // 生成新的数据
      let json = jsonBigint({storeAsString: true})
      this.g26CalendarTestReportTask(json.parse(record.queryParam))
      this.isShowHistorical = false
      this.templateActivity = 0
    },
    handleOpenHistory() {
      this.isShowHistorical = true
    },
    handleOpenDraf() {
      this.isShowDrafts = true
    },

    /*
    * 草稿箱按钮事件
    */
    handleDraf(record) {
      this.testReportID = record.id
      this.getG26ById({id: record.id})
      window.sessionStorage.setItem("testReportID", record.id)
      this.isShowDrafts = false
      this.templateActivity = 0
    },
    /*
    *暂存事件
    */
    handleSave() {
      this.$message.success('保存成功')
    },

    /*
    *测试条件事件
    */
    // 点击测试条件事件
    handleChangeTemplate(target) {
      this.templateActivity = target
    },


    showData(record) {
      showData({celltestcode: record.celltestcode}).then(res => {
        this.$refs.table.refresh()
      })
    },
    hideData(record) {
      hideData({id: record.id}).then(res => {
        this.$refs.table.refresh()
      })
    },

    handleAllOk() {
      const temList = this._handleVerify(this.reportData)

      // 如果校验不通过
      if (!temList[0]) return this.$message.warn("请正确填写 " + temList[1] + " 测试条件的" + (typeof temList[2] === 'number' ? "RPT " + temList[2] + " 的" : "") + temList[3])

      if (temList[0]) return this.isShowReportName = true
    },

    // 完成模型搭建事件
    exportData() {
      if(!this.reportName) return this.$message.warn("请正确填写测试报告名称")

      this.reportData.reportName = this.reportName
      let json = jsonBigint({storeAsString: true})
      const params = json.parse(JSON.stringify(this.reportData))

      commitG26Calendar(params, this.testReportID).then(res => {
        if (res.success) {
          this.$message.success("创建成功")
          this.$router.push("/v_report_online_manager?type=G26Calendar")
        } else {
          this.$message.warn(res.message)
        }
      })
    },

    // 单条选中
    onSelectChange(record, selected, selectedRows) {

      if (selected) {
        if (record.flowId == null) {
          this.$message.warn("测试数据为空")
          return
        }

        // 在选择页面时校验选择数量
        if (this.selectedRows.length >= 2) {
          this.$message.warn("最多选择两条数据")
          return
        }

        if (!this.selectedRowKeys.includes(record.uuid)) {
          const newRecord = JSON.parse(JSON.stringify(record))
          this.selectedRows.push(newRecord)
          this.selectedRowKeys.push(newRecord.uuid)
        }
      } else {
        for (let i = 0; i < this.selectedRowKeys.length; i++) {
          if (this.selectedRowKeys[i] == record.uuid) {
            this.selectedRows.splice(i, 1)
            this.selectedRowKeys.splice(i, 1)
            break
          }
        }
      }

      this.handleSampleChange()
    },

    openStepData(record, flag) {
      this.outQueryFlowRecord = record

      //历史数据处理
      if (null == record.flowInfoList && !flag) {
        this.$refs.stepData.query(record, false)
        return;
      }

      if (record.flowId != null) {
        this.outQueryFlowRecord.flowId = record.flowId
        this.$refs.stepData.query(this.outQueryFlowRecord, false)
      } else {
        this.$message.warn("测试数据为空")
        return
      }
    },

    removeOrderno(celltestcode) {
      let parts = celltestcode.split("-")
      return parts.slice(0, -2).join("-")
    },

    handleCancel(target) {
      if (target == 'visible') {
        this._handleProgress()
      }
      this[target] = false

    },
    handleOpen(target) {
      this[target] = true
    },

    selectSample(index) {
      this.selectedRecordIndex = index

      this.selectedRows = []
      this.selectedRowKeys = []
      if (this.reportData.templateList[this.templateActivity].rptData[index].sample1) {
        this.selectedRows.push(this.reportData.templateList[this.templateActivity].rptData[index].sample1)
        this.selectedRowKeys.push(this.reportData.templateList[this.templateActivity].rptData[index].sample1.uuid)
      }
      if (this.reportData.templateList[this.templateActivity].rptData[index].sample2) {
        this.selectedRows.push(this.reportData.templateList[this.templateActivity].rptData[index].sample2)
        this.selectedRowKeys.push(this.reportData.templateList[this.templateActivity].rptData[index].sample2.uuid)
      }

      this.handleOpen('visible')
    },
    deleteSample(index) {
      delete this.reportData.templateList[this.templateActivity].rptData[index].sample1
      delete this.reportData.templateList[this.templateActivity].rptData[index].sample2
      this._handleProgress()
      // 实时保存
      this.g26CalendarTestReportTask(this.reportData, this.testReportID)
    },

    // 清空样品数据
    clearSample() {
      for (let i = 0; i <= 30; i++) {
        delete this.reportData.templateList[this.templateActivity].rptData[i].day
        delete this.reportData.templateList[this.templateActivity].rptData[i].rechargeDay
        delete this.reportData.templateList[this.templateActivity].rptData[i].sample1
        delete this.reportData.templateList[this.templateActivity].rptData[i].sample2
      }

      this._handleProgress()
      // 实时保存
      this.g26CalendarTestReportTask(this.reportData, this.testReportID)
    },

    async handleSampleChange() {

      // 选择了第一行RPT样品，补充/删除大小中检天数、补电天数、样品数据
      if (this.selectedRecordIndex == 0) {

        // 有两个样品，一定是新增了第2个样品，补充样品2数据即可
        if (this.selectedRows.length > 1) {
          // 先删除样品2数据
          for (let i = 0; i <= 30; i++) {
            delete this.reportData.templateList[this.templateActivity].rptData[i].sample2
          }
          // 补充样品2数据
          await this.supplySamples(this.selectedRows[1].celltestcode, 2)
        } else {
          // 删除补充的数据
          for (let i = 0; i <= 30; i++) {
            delete this.reportData.templateList[this.templateActivity].rptData[i].day
            delete this.reportData.templateList[this.templateActivity].rptData[i].rechargeDay
            delete this.reportData.templateList[this.templateActivity].rptData[i].sample1
            delete this.reportData.templateList[this.templateActivity].rptData[i].sample2
          }
          // 如果有样品1，补充数据
          if (this.selectedRows.length === 1) {
            // 补充大小中检天数、补电天数
            await this.supplyDaysAndRechargeDays(this.selectedRows[0].folderid, this.selectedRows[0].ordtaskid)
            // 补充样品1数据
            await this.supplySamples(this.selectedRows[0].celltestcode, 1)
          }
        }

      } else {
        // 赋值该行样品1、样品2
        delete this.reportData.templateList[this.templateActivity].rptData[this.selectedRecordIndex].sample1
        delete this.reportData.templateList[this.templateActivity].rptData[this.selectedRecordIndex].sample2
        if (this.selectedRows.length > 0) {
          this.reportData.templateList[this.templateActivity].rptData[this.selectedRecordIndex].sample1 = this.selectedRows[0]
        }
        if (this.selectedRows.length > 1) {
          this.reportData.templateList[this.templateActivity].rptData[this.selectedRecordIndex].sample2 = this.selectedRows[1]
        }
      }

      // 如果删除了 基准工步索引行/第一行 样品数据，需要清空基准工步索引
      // if (this.selectedRecordIndex == this.reportData.templateList[this.templateActivity].standardStepIndex || this.selectedRecordIndex == 0) {
      //   if (this.selectedRowKeys.length == 0) {
      //     delete this.reportData.templateList[this.templateActivity].standardStepIndex
      //   }
      // }

      // 使用Promise.all等待所有异步操作完成，promises里面的函数并行执行
      // Promise.all(promises).then(() => {
      //   this._handleProgress()
      //   // 实时保存
      //   this.g26CalendarTestReportTask(this.reportData, this.testReportID)
      // })

      this._handleProgress()
      // 实时保存
      this.g26CalendarTestReportTask(this.reportData, this.testReportID)

    },

    supplyDaysAndRechargeDays(folderid, ordtaskid) {
      // 中检天数、补电天数 测试环境数据库-测试数据
      // console.log(folderid, ordtaskid)
      // folderid = 1337813866181728
      // ordtaskid = 1337813866181729

      return getTemGradientsByOrderIdAndOrdtaskId(null, folderid, ordtaskid).then((res) => {
        if (res.success) {
          let dayList = []
          let rechargeDayList = []

          let temGradients = res.data
          temGradients.forEach(row => {
            if (row.recharge == '1') {
              rechargeDayList.push(parseInt(row.totalstoragedays))
            } else {
              dayList.push(parseInt(row.totalstoragedays))
            }
          })

          // 补充大小中检天数
          for (let i = 0; i < dayList.length && i <= 30; i++) {
            this.reportData.templateList[this.templateActivity].rptData[i].day = dayList[i]
          }
          // 补充补电天数
          for (let i = 0; i < rechargeDayList.length && i <= 30; i++) {
            this.reportData.templateList[this.templateActivity].rptData[i].rechargeDay = rechargeDayList[i]
          }

        }
      })
    },

    supplySamples(celltestcode, type) {
      return tLimsTestdataSchedulePageList({pageNo: 1, pageSize: 31, celltestcode: celltestcode}).then(res => {
        if (res.data.rows.length > 0) {
          let samples = []
          let parentSample = res.data.rows[0] // { children: sampleList, otherproperties: ''... }
          samples.push(JSON.parse(JSON.stringify(parentSample)))
          if (parentSample.children) {
            parentSample.children.forEach(child => {
              samples.push(JSON.parse(JSON.stringify(child)))
            })
          }

          // 补充样品列表
          if (type == 1) {
            // 补充样品1，样品存储天数需要匹配对应行的中检天数
            this.reportData.templateList[this.templateActivity].rptData.forEach((rpt, i) => {
              if (typeof rpt.day === 'number') {
                for (let j = 0; j < samples.length; j++) {
                  if (rpt.day == samples[j].day) {
                    this.reportData.templateList[this.templateActivity].rptData[i].sample1 = samples[j]
                    break
                  }
                }
              }
            })
          } else if (type == 2) {
            // 补充样品2，样品存储天数需要匹配对应行的中检天数
            this.reportData.templateList[this.templateActivity].rptData.forEach((rpt, i) => {
              if (typeof rpt.day === 'number') {
                for (let j = 0; j < samples.length; j++) {
                  if (rpt.day == samples[j].day) {
                    this.reportData.templateList[this.templateActivity].rptData[i].sample2 = samples[j]
                    break
                  }
                }
              }
            })
          }

        }
      })
    },

    handleClose(index) {
      switch (index) {
        case 1:
          this.isCloseOne = !this.isCloseOne
          break
        case 2:
          this.isCloseTwo = !this.isCloseTwo
          break
        case 3:
          this.isCloseThree = !this.isCloseThree
          break
        case 4:
          this.isCloseFour = !this.isCloseFour
          break
        case 5:
          this.isCloseFive = !this.isCloseFive
          break
        case 6:
          this.isCloseSix = !this.isCloseSix
          break
        case 7:
          this.isCloseSeven = !this.isCloseSeven
          break
        case 8:
          this.isCloseEight = !this.isCloseEight
          break
        case 9:
          this.isCloseNine = !this.isCloseNine
          break
      }

      // 如果是进度块
      if (index == 4) {
        this._handleProgress()
      }
    },

    // 处理测试报告填写进度
    _handleProgress() {
      this.percentTotal = 0
      this.completeNum = 0
      // 填写了内容的测试条件总数
      let totalNum = 0
      this.reportData.templateList.forEach((v, index) => {

        const testReport = this.menuOptions[index]
        testReport.title = (typeof v.temperature === 'number' ? v.temperature : "") + "℃ " + (typeof v.soc === 'number' ? v.soc + "%" : "") + "SOC"
        testReport.percent = 0

        // 温度填写 10%
        if (typeof v.temperature === 'number') {
          testReport.percent += 10
          this.percentTotal += 10
        }

        // SOC填写 10%
        if (typeof v.soc === 'number') {
          testReport.percent += 10
          this.percentTotal += 10
        }

        // RPT数据校验：第一行：样品1，电压工步号，基准工步行：三个工步号，所有行：填了样品就一定要填Days
        // 选择第一行样品1 20%
        let flag = false
        if (v.rptData[0].sample1) {
          testReport.percent += 20
          this.percentTotal += 20
          flag = true
        }

        // 电压工步号填写 10%
        if (v.voltageStep) {
          testReport.percent += 10
          this.percentTotal += 10
        }

        // 基准工步选择 10%
        if (typeof v.standardStepIndex === 'number') {
          testReport.percent += 10
          this.percentTotal += 10

          // Capacity/Energy工步号填写 10%
          if (v.rptData[v.standardStepIndex].capacityEnergyStep) {
            testReport.percent += 10
            this.percentTotal += 10
          }
          // DCIR搁置工步号填写 10%
          if (v.rptData[v.standardStepIndex].holdStep) {
            testReport.percent += 10
            this.percentTotal += 10
          }
          // DCIR放电工步号填写 10%
          if (v.rptData[v.standardStepIndex].dischargeStep) {
            testReport.percent += 10
            this.percentTotal += 10
          }
        }

        // 所有行中检天数填写：填了样品就一定要填Days 10%
        for (let i = 0; i < v.rptData.length; i++) {
          if (v.rptData[i].sample1) {
            if (typeof v.rptData[i].day !== 'number') {
              flag = false
              break
            }
          }
        }
        if (flag) {
          testReport.percent += 10
          this.percentTotal += 10
        }

        if (testReport.percent > 0) {
          // 只要当前测试条件填写了内容，就需要填完
          totalNum += 1
        }

        if (testReport.percent >= 100) {
          // 已填写完的测试条件数量，据此展示下一个圆圈
          this.completeNum += 1
        }
      })

      this.percentTotal = totalNum == 0 ? 0 : Math.round(this.percentTotal / totalNum)
    },

    // 校验
    _handleVerify(data) {

      // 1个条件都没有填写
      // const notEmptyTemplates = data.templateList.filter(template => template.rptData[0].sample1)
      if (this.percentTotal == 0) {
        return [false, '至少一个', '', '所有数据']
      }

      // 逐个测试条件校验 (不要用forEach遍历)
      for (let index = 0; index < data.templateList.length; index++) {

        let percent = this.menuOptions[index].percent

        // persent>0，表示填了内容，则需要校验
        if (percent > 0) {
          let v = data.templateList[index]
          let templateName = this.menuOptions[index].title

          // ---------------------- 校验：温度，SOC，第一行：样品1，电压工步号，基准工步行：三个工步号(样品：选了样品才能选择)，所有行：填了样品就一定要填Days ------------------------
          if (typeof v.temperature !== 'number') {
            return [false, templateName, '', '温度']
          }
          if (typeof v.soc !== 'number') {
            return [false, templateName, '', 'SOC']
          }
          // 校验第一行样品1
          if (!v.rptData[0].sample1) {
            return [false, templateName, 0, '样品数据']
          }
          if (!v.voltageStep) {
            return [false, templateName, '', '电压工步号']
          }
          // 校验基准工步选择
          if (typeof v.standardStepIndex !== 'number') {
            return [false, templateName, '', '基准工步']
          } else {
            if (!v.rptData[v.standardStepIndex].capacityEnergyStep) {
              return [false, templateName, v.standardStepIndex, 'Capacity/Energy工步号']
            }
            if (!v.rptData[v.standardStepIndex].holdStep) {
              return [false, templateName, v.standardStepIndex, 'DCIR搁置工步号']
            }
            if (!v.rptData[v.standardStepIndex].dischargeStep) {
              return [false, templateName, v.standardStepIndex, 'DCIR放电工步号']
            }
          }
          // 所有行存储天数填写：填了样品就一定要填Days
          for (let i = 0; i < v.rptData.length; i++) {
            if (v.rptData[i].sample1) {
              if (typeof v.rptData[i].day !== 'number') {
                return [false, templateName, i, '存储天数']
              }
            }
          }

        }
      }

      return [true, '', '', '']
    },

  }
}
</script>
<style lang="less" scoped>
@import '/src/components/pageTool/style/pbiSearchItem.less';

.wrapper {
  padding: 0px 12px;
  background-color: #f0f2f5;
}

.head_title {
  color: #333;
  padding: 10px 0;
  font-size: 20px;
  font-weight: 600;
}

.head_title::before {
  width: 8px;
  background: #1890ff;
  margin-right: 8px;
  content: "\00a0"; //填充空格
}

.head_title .subheading {
  font-size: 14px;
  font-weight: 400;
}

.menu-content {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.btn-wrap {
  display: flex;
}

.normal-btn {
  padding: 5px 10px;
  color: #fff;
  background-color: #1890ff;
  letter-spacing: 2px;
  cursor: pointer;
}

.block {
  height: fit-content;
  padding: 10px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
  position: relative;
}

.top-wrapper {
  display: flex;
  align-items: center;
}

.btn-icon {
  font-size: 12px;
  color: #999;
}

.btn-icon:hover {
  color: #1890ff;
  border: 0;
}

.percent-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: start;
  align-items: center;
  position: relative;
}

.percent-content .percent-block {
  width: 8.8%;
  cursor: pointer;
  padding: 6px;
  font-size: 12px;
  text-align: center;
  border-radius: 5px;
}

.percent-content .percent-block-check {
  box-shadow: 0 0 2px rgb(24, 144, 255) inset, 0 0 2px rgb(24, 144, 255);
}

.percent-content .shrink-btn {
  color: #1890ff;
  cursor: pointer;
  font-size: 12px;
  position: absolute;
  top: 0;
  right: -10px;
}

.percent-content .progress-block {
  width: calc(100% - 50px);
}


.center-wrapper {
  padding-right: 20px;
}


.bottom-wrapper {
  padding: 0 0 10px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

/deep/ .ant-table-thead > tr > th {
  padding: 5px !important;
  font-size: 13px !important;
  color: rgba(0, 0, 0, .85) !important;
  font-weight: 500 !important;
}

/deep/ .ant-table-tbody > tr > td {
  padding: 0px !important;
  font-size: 12px !important;
  color: #333 !important;
  font-weight: 400 !important;
}

/deep/ .s-table-tool {
  padding: 0;
}

.ant-modal-body {
  padding: 0;
}

/deep/ .ant-btn > i,
/deep/ .ant-btn > span {
  display: flex;
  justify-content: center;
}

/deep/ .ant-table-pagination.ant-pagination {
  float: right;
  margin: 0;
}

.title {
  font-size: large;
  margin-bottom: 20px;
}

/deep/ .ant-table-footer {
  padding: 0;
}


// 通用
.mt5 {
  margin-top: 5px;
}

.mt10 {
  margin-top: 10px;
}

.ml2 {
  margin-left: 2px;
}

.ml10 {
  margin-left: 10px;
}

.mr10 {
  margin-right: 10px;
}

.mr8 {
  margin-right: 8px;
}

/* 标题 */
.flex-sb-center-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

// 组件

/deep/ .bottom-wrapper .ant-table-thead {
  position: sticky;
  top: 0;
  z-index: 2;
}

/deep/ .bottom-wrapper .ant-table-placeholder {
  border: none !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 0;
  z-index: 0;
}

/deep/ .ant-empty-image {
  display: none;
}

/deep/ .ant-radio-inner {
  top: 1px;
  left: 1px;
}

/deep/ .ant-table-body::-webkit-scrollbar {
  height: 10px;
  width: 5px;
}

/deep/ .ant-table-body::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;

  background: #dddbdb;
}

/deep/ .ant-table-body::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: #f1f1f1;
}

/deep/ .ant-select {
  font-size: 12px;
}

/deep/ .ant-select-selection__rendered {
  margin-right: 0px;
}

/deep/ .ant-form-item {
  margin-bottom: 0;
  font-size: 12px;
}

/deep/ .ant-popover-buttons {
  display: flex !important;
  flex-direction: column !important;
  margin-bottom: 15px;
}

.tips {
  color: #1890ff;
}

/deep/ .ant-table-thead > tr > th .ant-table-header-column {
  width: 100%;
}

.input-number {
  width: 100%;
  text-align: center;
  border: 0;
}

/deep/ .input-number .ant-input-number-input {
  text-align: center;
}

/deep/ .ant-pagination-options-size-changer.ant-select {
  min-width: 90px; /* 调整选择器宽度 */
}

/deep/ .ant-pagination-options-size-changer.ant-select .ant-select-selection__rendered {
  text-align: center; /* 选择器文字居中 */
}
</style>