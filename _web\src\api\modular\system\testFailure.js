import { axios } from '@/utils/request'

export function getDpvTestFailureList(parameter) {
  return axios({
    url: '/dpvTestFailureRecord/list',
    method: 'post',
    data: parameter
  })
}
export function getDpvTestFailureTreeData(parameter) {
  return axios({
    url: '/dpvTestFailureRecord/treeData',
    method: 'post',
    data: parameter
  })
}

export function getDpvTestFailureListPage(parameter) {
  return axios({
    url: '/dpvTestFailureRecord/listPage',
    method: 'post',
    data: parameter
  })
}

export function getDpvTestFailureStockList(parameter) {
  return axios({
    url: '/dpvTestFailureRecord/stockList',
    method: 'post',
    data: parameter
  })
}

export function saveTestFailureRecord(parameter) {
  return axios({
    url: '/dpvTestFailureRecord/save',
    method: 'post',
    data: parameter
  })
}

export function getCellMsgByCellCode(parameter) {
  return axios({
    url: '/dpvTestFailureRecord/getCellMsg',
    method: 'post',
    data: parameter
  })
}

export function getFolderMsgByFolderNo(parameter) {
  return axios({
    url: '/dpvTestFailureRecord/getFolderMsg',
    method: 'post',
    data: parameter
  })
}

export function getOrderByOrdtask(parameter) {
  return axios({
    url: '/dpvTestFailureRecord/getOrderByOrdtask',
    method: 'post',
    data: parameter
  })
}

export function failureCellInStore(parameter) {
  return axios({
    url: '/dpvTestFailureRecord/inStore',
    method: 'post',
    data: parameter
  })
}

export function failureCellOutStore(parameter) {
  return axios({
    url: '/dpvTestFailureRecord/outStore',
    method: 'post',
    data: parameter
  })
}

export function getFailureCellOcvRecordList(parameter) {
  return axios({
    url: '/failureCellOcvRecord/list',
    method: 'post',
    data: parameter
  })
}

export function getFailureCellOcvRecordPageList(parameter) {
  return axios({
    url: '/failureCellOcvRecord/listPage',
    method: 'post',
    data: parameter
  })
}

export function getFailureCellOcvFillPageList(parameter) {
  return axios({
    url: '/failureCellOcvRecord/listOcvFillPage',
    method: 'post',
    data: parameter
  })
}

export function addFailureCellOcvRecord(parameter) {
  return axios({
    url: '/failureCellOcvRecord/add',
    method: 'post',
    data: parameter
  })
}

export function addOrUpdateFailureCellOcvRecord(parameter) {
  return axios({
    url: '/failureCellOcvRecord/addOrUpdate',
    method: 'post',
    data: parameter
  })
}

export function failureCellGetOcvConfigDict(parameter) {
  return axios({
    url: '/failureCellOcvRecord/getOcvConfigDict',
    method: 'post',
    data: parameter
  })
}

export function failureCellUpdateOcvCron(parameter) {
  return axios({
    url: '/failureCellOcvRecord/updateOcvCron',
    method: 'post',
    data: parameter
  })
}

export function exportOcvTemplate(parameter) {
  return axios({
    url: '/dpvTestFailureRecord/exportOcvTemplate',
    method: 'post',
    responseType: 'blob',
    data: parameter
  })
}

export function importOcvTemplate(parameter) {
  return axios({
    url: '/dpvTestFailureRecord/importOcvTemplate',
    method: 'post',
    data: parameter
  })
}