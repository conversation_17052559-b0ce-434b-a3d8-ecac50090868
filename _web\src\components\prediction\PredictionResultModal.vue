<template>
  <a-modal
    :visible="visible"
    :width="1200"
    :footer="null"
    :destroy-on-close="true"
    @cancel="closeModal"
    wrap-class-name="prediction-result-modal"
  >
    <template slot="title">
      <div class="modal-title">预测结果详情</div>
    </template>

    <div v-if="!resultData" class="empty-content">
      <a-empty description="暂无预测结果数据" />
    </div>

    <div v-else class="result-content">
      <!-- 显示模型信息 -->
      <div class="model-info-section">
        <a-descriptions title="模型信息" bordered size="small">
          <a-descriptions-item label="模型ID">{{ resultData.modelInfo && resultData.modelInfo.id || '未知' }}</a-descriptions-item>
          <a-descriptions-item label="模型描述" :span="2">{{ resultData.modelInfo && resultData.modelInfo.description || '无描述' }}</a-descriptions-item>
          <a-descriptions-item label="公式" :span="3">
            <div class="formula-text">{{ resultData.modelInfo && resultData.modelInfo.formula || '无公式' }}</div>
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 布局控制 -->
      <div class="control-panel">
        <a-radio-group v-model="columnsCount" buttonStyle="solid" size="small">
          <a-radio-button :value="1">一列</a-radio-button>
          <a-radio-button :value="2">两列</a-radio-button>
          <a-radio-button :value="3">三列</a-radio-button>
          <a-radio-button :value="4">四列</a-radio-button>
        </a-radio-group>
      </div>

      <!-- 预测曲线图表容器 -->
      <div class="temperature-charts-grid" :class="`columns-${columnsCount}`">
        <template v-for="(temperature, index) in temperatures">
          <div :key="index" class="chart-container">
            <a-card :title="`${temperature}°C 容量衰减预测曲线`" size="small" :bordered="true">
              <div :ref="`tempChart_${index}`" class="chart-wrapper"></div>
            </a-card>
          </div>
        </template>
      </div>

      <!-- 如果没有图表数据，显示错误提示 -->
      <div v-if="temperatures.length === 0 || socs.length === 0" class="error-message">
        <a-alert
          message="数据加载错误"
          description="未能加载温度或SOC数据，请确保上传的Excel文件包含有效数据。"
          type="error"
          show-icon
        />
      </div>
    </div>
  </a-modal>
</template>

<script>
import * as echarts from 'echarts';
import { mapGetters } from 'vuex';
import { chartColors, createCapacityCurveOptions, createScatterSeries, createLineSeries, createTooltipFormatter } from '@/utils/chartUtils';
import predictionMixin from '@/mixins/predictionMixin';

export default {
  name: 'PredictionResultModal',
  mixins: [predictionMixin],
  data: () => ({
    temperatureCharts: [],
    columnsCount: 3, // 默认三列布局
    dataPoints: [], // 本地存储数据点
    colors: chartColors
  }),
  computed: {
    ...mapGetters({
      resultData: 'getFittingResultData',
      visible: 'getFittingResultModalVisible'
    }),
    temperatures() {
      return this.resultData?.temperatures || [];
    },
    socs() {
      return this.resultData?.socs || [];
    }
  },
  watch: {
    visible(newVal) {
      if (newVal && this.resultData) {
        // 本地保存数据点，避免引用问题
        this.dataPoints = this.resultData.dataPoints || [];
        this.$nextTick(this.initTemperatureCharts);
      }
    },
    columnsCount() {
      this.$nextTick(() => {
        // 销毁所有现有图表
        this.temperatureCharts.forEach(chart => {
          if (chart) chart.dispose();
        });
        this.temperatureCharts = [];

        // 重新初始化图表
        setTimeout(this.initTemperatureCharts, 300);
      });
    },
    resultData: {
      handler(newVal) {
        if (newVal) {
          this.dataPoints = newVal.dataPoints || [];
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    closeModal() {
      this.$store.commit('setFittingResultModalVisible', false);
    },

    // 为每个温度初始化图表
    initTemperatureCharts() {
      // 销毁之前的图表实例
      this.temperatureCharts.forEach(chart => {
        if (chart) chart.dispose();
      });
      this.temperatureCharts = [];

      // 获取数据
      const temperatures = this.temperatures;
      const socs = this.socs;
      const dataPoints = this.dataPoints;

      if (!temperatures.length || !socs.length || !dataPoints || dataPoints.length === 0) {
        return;
      }

      // 为每个温度创建图表
      temperatures.forEach((temp, index) => {
        this.$nextTick(() => {
          const chartRefs = this.$refs[`tempChart_${index}`];
          if (!chartRefs || chartRefs.length === 0) return;

          const chartDom = chartRefs[0];
          const chart = echarts.init(chartDom);
          this.temperatureCharts.push(chart);

          // 处理数据点
          const series = this.prepareSeries(temp, socs, dataPoints);

          // 配置图表选项
          const option = createCapacityCurveOptions(
            `${temp}°C`,
            series,
            {
              legendPosition: 'bottom',
              gridConfig: {
                bottom: 80,
                top: 60
              },
              tooltip: {
                formatter: createTooltipFormatter
              }
            }
          );

          // 设置图表选项
          chart.setOption(option);
        });
      });

      // 监听窗口大小变化以调整图表大小
      window.addEventListener('resize', this.resizeCharts);
    },

    // 准备图表系列数据
    prepareSeries(temperature, _socs, allDataPoints) {
      const series = [];
      const tempValue = typeof temperature === 'string' ? parseFloat(temperature) : temperature;

      // 筛选该温度的数据点
      const tempDataPoints = allDataPoints.filter(point => {
        const pointTemp = typeof point.temperature === 'string' ? parseFloat(point.temperature) : point.temperature;
        return Math.abs(pointTemp - tempValue) < 0.01;
      });

      if (tempDataPoints.length === 0) return series;

      // 获取该温度下所有唯一的SOC值
      const tempSOCs = Array.from(new Set(tempDataPoints.map(point => {
        const pointSoc = typeof point.soc === 'string' ? parseFloat(point.soc) : point.soc;
        return pointSoc;
      }))).sort((a, b) => a - b);

      // 处理每个SOC值的数据
      tempSOCs.forEach(socValue => {
        // 筛选该温度和SOC的数据点
        const socPoints = tempDataPoints.filter(point => {
          const pointSoc = typeof point.soc === 'string' ? parseFloat(point.soc) : point.soc;
          return Math.abs(pointSoc - socValue) < 0.01;
        });

        if (socPoints.length > 0) {
          const colorIndex = Math.floor(socValue * 10) % this.colors.length;

          // 原始测量数据系列
          const originalDataSeries = createScatterSeries(
            `${socValue * 100}% SOC (实测)`,
            [],
            colorIndex,
            {
              symbolSize: 4,
              itemStyle: {
                color: this.getColorBySoc(socValue, 1.0)
              }
            }
          );

          // 预测数据系列
          const predictedDataSeries = createLineSeries(
            `${socValue * 100}% SOC (预测)`,
            [],
            colorIndex,
            true,
            {
              symbolSize: 0,
              showSymbol: false,
              lineStyle: {
                width: 2,
                color: this.getColorBySoc(socValue, 0.8)
              }
            }
          );

          socPoints.forEach(point => {
            try {
              // 处理原始测量数据
              if (point.original_days && point.original_capacities &&
                  Array.isArray(point.original_days) && Array.isArray(point.original_capacities)) {

                const days = point.original_days.map(d => typeof d === 'string' ? parseFloat(d) : d);
                const capacities = point.original_capacities.map(c => typeof c === 'string' ? parseFloat(c) : c);
                let initialCapacity = null;

                // 找到第一个有效的容量值作为初始容量
                for (let i = 0; i < capacities.length; i++) {
                  if (capacities[i] !== null && capacities[i] !== undefined && !isNaN(capacities[i])) {
                    initialCapacity = capacities[i];
                    break;
                  }
                }

                if (initialCapacity !== null) {
                  // 处理原始测量数据点
                  for (let i = 0; i < Math.min(days.length, capacities.length); i++) {
                    const day = days[i];
                    const capacity = capacities[i];

                    if (day !== null && day !== undefined && !isNaN(day) &&
                        capacity !== null && capacity !== undefined && !isNaN(capacity)) {
                      const retention = parseFloat(((capacity / initialCapacity) * 100).toFixed(4));
                      originalDataSeries.data.push([day, retention]);
                    }
                  }
                }
              }

              // 处理预测数据
              if (point.days && point.capacities &&
                  Array.isArray(point.days) && Array.isArray(point.capacities)) {

                const days = point.days.map(d => typeof d === 'string' ? parseFloat(d) : d);
                const capacities = point.capacities.map(c => typeof c === 'string' ? parseFloat(c) : c);
                let initialCapacity = null;

                // 尝试使用原始数据的初始容量
                if (point.original_capacities && Array.isArray(point.original_capacities) && point.original_capacities.length > 0) {
                  for (let i = 0; i < point.original_capacities.length; i++) {
                    const cap = point.original_capacities[i];
                    if (cap !== null && cap !== undefined && !isNaN(cap)) {
                      initialCapacity = cap;
                      break;
                    }
                  }
                }

                // 如果没有找到原始初始容量，使用预测数据的第一个值
                if (initialCapacity === null) {
                  for (let i = 0; i < capacities.length; i++) {
                    if (capacities[i] !== null && capacities[i] !== undefined && !isNaN(capacities[i])) {
                      initialCapacity = capacities[i];
                      break;
                    }
                  }
                }

                if (initialCapacity !== null) {
                  // 处理预测数据点
                  for (let i = 0; i < Math.min(days.length, capacities.length); i++) {
                    const day = days[i];
                    const capacity = capacities[i];

                    if (day !== null && day !== undefined && !isNaN(day) &&
                        capacity !== null && capacity !== undefined && !isNaN(capacity)) {
                      const retention = parseFloat(((capacity / initialCapacity) * 100).toFixed(4));
                      predictedDataSeries.data.push([day, retention]);
                    }
                  }
                }
              }
            } catch (error) {
              console.error("处理数据点时出错:", error);
            }
          });

          // 按天数排序数据并添加到系列
          if (originalDataSeries.data.length > 0) {
            originalDataSeries.data.sort((a, b) => a[0] - b[0]);
            series.push(originalDataSeries);
          }

          if (predictedDataSeries.data.length > 0) {
            predictedDataSeries.data.sort((a, b) => a[0] - b[0]);
            series.push(predictedDataSeries);
          }
        }
      });

      return series;
    },

    resizeCharts() {
      this.$nextTick(() => {
        this.temperatureCharts.forEach(chart => {
          if (chart) {
            chart.resize({
              width: 'auto',
              height: 'auto'
            });
          }
        });
      });
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeCharts);
    this.temperatureCharts.forEach(chart => {
      if (chart) chart.dispose();
    });
  }
};
</script>

<style scoped>
.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.empty-content {
  padding: 40px 0;
  text-align: center;
}

.result-content {
  max-height: 80vh;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 8px;
}

.model-info-section {
  margin-bottom: 24px;
}

.formula-text {
  font-family: monospace;
  white-space: pre-wrap;
  word-break: break-all;
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  max-height: 120px;
  overflow-y: auto;
}

.control-panel {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.temperature-charts-grid {
  display: grid;
  gap: 16px;
  width: 100%;
}

.temperature-charts-grid.columns-1 {
  grid-template-columns: 1fr;
}

.temperature-charts-grid.columns-2 {
  grid-template-columns: repeat(2, 1fr);
}

.temperature-charts-grid.columns-3 {
  grid-template-columns: repeat(3, 1fr);
}

.temperature-charts-grid.columns-4 {
  grid-template-columns: repeat(4, 1fr);
}



.chart-container {
  margin-bottom: 8px;
  width: 100%;
}

.chart-wrapper {
  width: 100%;
  height: 360px;
}

.error-message {
  margin-top: 16px;
}
</style>

<style>
.prediction-result-modal .ant-modal {
  top: 24px;
  width: 1200px !important;
}

.prediction-result-modal .ant-modal-content {
  width: 100%;
}

.prediction-result-modal .ant-modal-body {
  padding: 16px;
  overflow-x: hidden;
  width: 100%;
}

.prediction-result-modal .ant-modal-header {
  padding: 12px 16px;
}

.prediction-result-modal .ant-card-head {
  min-height: 36px;
}

.prediction-result-modal .ant-card-head-title {
  font-weight: 500;
  font-size: 14px;
  padding: 8px 0;
}

.prediction-result-modal .ant-card-body {
  padding: 12px;
  overflow: hidden;
}

.prediction-result-modal .ant-radio-button-wrapper {
  padding: 0 8px;
}

/* 确保ECharts图表适应容器 */
.prediction-result-modal .ant-card-body canvas {
  width: 100% !important;
}
</style>