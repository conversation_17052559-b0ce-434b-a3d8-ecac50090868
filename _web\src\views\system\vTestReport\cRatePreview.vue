<template>
  <div ref="wrapper" class="wrapper">
    <div class="flex-sb-center-row">
      <div class="head_title">{{ record.type + ": " + record.reportName }}</div>
    </div>
    <div class="all-wrapper">
      <div class="left-content">

        <div class="thumbnail-content">

          <div class="flex-column-center" v-for="(item,index) in cRateArr"
            @click="handleClickThumbnail(index + 1,'cRate')">
            <div class="thumbnail-block" :class="activeKey === index + 1 ? 'active' : ''">
              <div class="title">{{item.label}}</div>
              <div :id="`${item.id}Thumbnail`" :ref="`${item.id}Thumbnail`" class="normal-thumbnail-charts"></div>
            </div>
            <div v-show="activeKey === index + 1" class="kailong"></div>
          </div>

        </div>

        <div class="block mt10" v-show="activeKey === 1">
          <pageComponent class="mt10" editObj="capacity" @down="handleNormalRefDown('capacity')"
            @edit="handleEditEcharts('capacity')"></pageComponent>
          <div ref="capacity" id="capacity" class="normal-charts mt10"></div>
        </div>

        <div class="block mt10" v-show="activeKey === 2">
          <pageComponent class="mt10" editObj="tempRise" @down="handleNormalRefDown('tempRise')"
            @edit="handleEditEcharts('tempRise')"></pageComponent>
          <div ref="tempRise" id="tempRise" class="normal-charts mt10"></div>
        </div>

        <div class="block mt10" v-show="activeKey === 3">
          <pageComponent class="mt10" editObj="capacityVoltage" @down="handleNormalRefDown('capacityVoltage')"
            @edit="handleEditEcharts('capacityVoltage')"></pageComponent>
          <div ref="capacityVoltage" id="capacityVoltage" class="normal-charts mt10"></div>
        </div>

      </div>

      <div class="right-content">
        <div class="block" id="export">
          <div class="flex-column">
            <div>
              <div>原始数据</div>
              <div style="float: right;margin-top: -25px">
                <a-button type="primary" @click="cancel" v-if="update" style="margin-right: 20px">取消修改</a-button>
                <a-button type="primary" @click="setFormula" v-if="update" style="margin-right: 20px">设置公式</a-button>
              </div>
            </div>
            <div class="mt10">
              <a-table v-if="!update" class="originalTableClass" :columns="originColumns" bordered
                :data-source="originData" :pagination="false" :scroll="{ y: 500, x: 5000 }">
                <div v-for="item in thirdHeaderList" :slot=item>
                  <span v-if="item.indexOf('voltage')!==-1">电压/V</span>
                  <span v-if="item.indexOf('capacity')!==-1">容量/Ah</span>
                  <span v-if="item.indexOf('energy')!==-1">能量/Wh</span>
                  <span v-if="item.indexOf('auxTem1')!==-1">温度/℃</span>
                </div>
              </a-table>
              <a-table v-if="update" class="originalTableClass" :columns="originColumns" bordered
                :rowKey="(record) => record.id" :row-selection="{
                  selectedRowKeys: selectedRowKeys, selectedRows: selectedRows,
                  onSelectAll: onSelectAll,
                  onSelect:onSelect, columnWidth:40}" :data-source="originData" :pagination="false"
                :scroll="{ y: 500, x: 5000 }">
                <div v-for="item in thirdHeaderList" :slot=item>
                  <a-checkbox v-if="item.indexOf('voltage')!==-1"
                    @change="checkHeaderName($event,item)">电压/V</a-checkbox>
                  <a-checkbox v-if="item.indexOf('capacity')!==-1"
                    @change="checkHeaderName($event,item)">容量/Ah</a-checkbox>
                  <a-checkbox v-if="item.indexOf('energy')!==-1"
                    @change="checkHeaderName($event,item)">能量/Wh</a-checkbox>
                  <a-checkbox v-if="item.indexOf('auxTem1')!==-1"
                    @change="checkHeaderName($event,item)">温度/℃</a-checkbox>
                </div>
              </a-table>
              <a-pagination style="margin-top: 20px;float: right" v-model="pageNo" :page-size-options="pageSizeOptions"
                :total="totalRows" show-size-changer :page-size="pageSize" @change="onPageChange"
                @showSizeChange="onPageChange">
                <template slot="buildOptionText" slot-scope="props">
                  <span v-if="props.value !== totalRows">{{ props.value }}条/页</span>
                  <span v-if="props.value === totalRows">全部</span>
                </template>
              </a-pagination>
            </div>
          </div>
          <div class="flex-column2" style="margin-bottom: 50px">
            <div>
              <div style="margin-top: 20px">温升数据</div>
            </div>
            <div class="mt10">
              <a-table class="originalTempRiseClass" :columns="originTempRiseColumns" bordered
                :data-source="originTempRiseData" :pagination="false" :scroll="{ y: 500, x: 2000 }"> </a-table>
              <a-pagination style="margin-top: 20px;float: right" v-model="tempRisePageNo"
                :page-size-options="tempRisePageSizeOptions" :total="tempRiseTotalRows" show-size-changer
                :page-size="tempRisePageSize" @change="onTempRisePageChange" @showSizeChange="onTempRisePageChange">
                <template slot="buildOptionText" slot-scope="props">
                  <span v-if="props.value !== tempRiseTotalRows">{{ props.value }}条/页</span>
                  <span v-if="props.value === tempRiseTotalRows">全部</span>
                </template>
              </a-pagination>
            </div>
          </div>
          <div class="flex-column3">
            <div>
              <div style="margin-top: 20px">容量数据</div>
            </div>
            <div class="mt10">
              <a-table class="capacityClass" :columns="capacityColumns" bordered :data-source="capacityData"
                :pagination="false" :scroll="{ y: 500, x: 1000 }"> </a-table>
            </div>
          </div>
          <div class="flex-column4">
            <div>
              <div style="margin-top: 20px">能量数据</div>
            </div>
            <div class="mt10">
              <a-table class="energyClass" :columns="energyColumns" bordered :data-source="energyData"
                :pagination="false" :scroll="{ y: 500, x: 1000 }"> </a-table>
            </div>
          </div>
          <div class="flex-column5">
            <div>
              <div style="margin-top: 20px">最大温升数据</div>
            </div>
            <div class="mt10">
              <a-table class="maxTempRiseClass" :columns="maxTempRiseColumns" bordered :data-source="maxTempRiseData"
                :pagination="false" :scroll="{ y: 500, x: 1000 }"> </a-table>
            </div>
          </div>
        </div>
      </div>
    </div>
    <a-modal title="设置公式" :visible="setFormulaFlag" @ok="handleSetFormulaModalOk"
      @cancel="handleModalCancel('setFormulaFlag')">
      <a-form :label-col="{ span: 5 }" :wrapper-col="{ span: 13 }">
        <a-form-item label="公式">
          <span style="font-weight: bold">选中的数据</span>
          <a-select v-model="symbol" placeholder="请选择计算符" style="width:60px;margin-left: 10px">
            <a-select-option value="+">
              +
            </a-select-option>
            <a-select-option value="-">
              -
            </a-select-option>
            <a-select-option value="x">
              x
            </a-select-option>
            <a-select-option value="÷">
              ÷
            </a-select-option>
          </a-select>
          <a-input-number style="width:100px;margin-left: 10px" v-model="symboledNumber" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 在线编辑图表 -->
    <div v-if="drawerVisible">
      <PreviewDrawer :screenImageId = "screenImageId" :templateParam = "reportChartTemplateList[editObj]" :isLegendLeft =  "true" :legendOptions="originalLegent[editObj]" :data="editData[editObj].series"
        :original="originalData[editObj]" :editData="editData[editObj]" :checkObj="chartCheckObj[editObj]"
        @submit="handleDrawerSubmit" @reset="handleDrawerReset" @close="() => drawerVisible = false" @changeTemplate ="handleChangeTemplate" @screenshot="handleScreenshot">
      </PreviewDrawer>
    </div>
    <!-- <pbiReturnTop v-if="isShowReturnTop" @returnTop="handleReturnTop"></pbiReturnTop> -->
  </div>
</template>
<script>
  import $ from 'jquery';
  import _ from "lodash"

  import {
    getCRateTestReport,
    updateCRateData
  } from "@/api/modular/system/limsManager"

  import { STable } from "@/components";
  import { Pagination } from 'ant-design-vue';

  import { mixin, chart, thumbnail } from "./mixin/index.js"
  import {chartTemplate} from "@/views/system/vTestReport/mixin/chartTemplate";

  export default {
    components: {
      STable,
      'a-pagination': Pagination
    },
    mixins: [mixin, chart, thumbnail,chartTemplate],
    data: function () {
      return {
        editObj: "", // 选中的图表类型
        id: null,
        update: false,

        originData: [],
        originColumns: [],
        selectedRows: [],
        selectedRowKeys: [],
        thirdHeaderList: [],

        pageNo: 1,
        pageSize: 10,
        totalRows: 50,
        pageData: null,
        pageSizeOptions: ['10', '20', '30', '40', '50'],

        originTempRiseData: [],
        originTempRiseColumns: [],

        tempRisePageNo: 1,
        tempRisePageSize: 10,
        tempRiseTotalRows: 50,
        tempRisePageData: null,
        tempRisePageSizeOptions: ['10', '20', '30', '40', '50'],

        capacityData: [],
        capacityColumns: [],

        energyData: [],
        energyColumns: [],

        maxTempRiseData: [],
        maxTempRiseColumns: [],

        symbol: null,
        symboledNumber: null,
        setFormulaFlag: false,
        selectedHeaderNameRowKeys: [],

        // 第一张图--选择数据
        capacityOriginalList: [],//图例数据
        capacityCheckboxList: [], //选中的数据

        // 第一张图
        isEditCapacityXNum: 0,
        isEditCapacityYNum: 0,

        // 第二张图--选择数据
        tROriginalList: [],//图例数据
        tRCheckboxList: [], //选中的数据

        // 第二张图
        isEditTRXNum: 0,
        isEditTRYNum: 0,

        // 第三张图--选择数据
        cVOriginalList: [],//图例数据
        cVCheckboxList: [], //选中的数据

        // 第三张图
        isEditCVXNum: 0,
        isEditCVYNum: 0,

        chartCheckObj:{
          capacity:{},
          tempRise:{},
          capacityVoltage:{},
        }
      }

    },
    async mounted() {
      await this.getChartTemplateRelationList(this.$route.query.id,['capacity','tempRise','capacityVoltage'])
      this.init()
      // const box = this.$refs.wrapper
      // box.addEventListener("scroll", e => {
      //   if (e.target.scrollTop > 100 && !this.isShowReturnTop) {
      //     this.isShowReturnTop = true
      //   }
      //   if(e.target.scrollTop < 100 && this.isShowReturnTop){
      //     this.isShowReturnTop = false
      //   }
      // })
    },
    methods: {
      init() {
        this.setFormulaFlag = false
        this.selectedHeaderNameRowKeys = []
        this.selectedRows = []
        this.selectedRowKeys = []
        this.id = this.$route.query.id
        getCRateTestReport({ id: this.id, pageNo: this.pageNo, pageSize: this.pageSize, tempRisePageNo: this.tempRisePageNo, tempRisePageSize: this.tempRisePageSize })
          .then(res => {
            this.record = res.data
            this.allDataJson = JSON.parse(res.data.allDataJson)


            this.queryParam = JSON.parse(res.data.queryParam)

            // 原始数据分页
            this.pageData = JSON.parse(res.data.allDataJson).tableList
            this.originData = this.pageData.records
            this.pageNo = this.pageData.current
            this.pageSize = this.pageData.size
            this.totalRows = this.pageData.total
            if (this.pageSizeOptions.indexOf(this.totalRows.toString()) === -1) {
              this.pageSizeOptions.push(this.totalRows.toString())
            }

            // 温升数据分页
            this.tempRisePageData = JSON.parse(res.data.allDataJson).tempRiseRowList
            this.originTempRiseData = this.tempRisePageData.records
            this.tempRisePageNo = this.tempRisePageData.current
            this.tempRisePageSize = this.tempRisePageData.size
            this.tempRiseTotalRows = this.tempRisePageData.total
            if (this.tempRisePageSizeOptions.indexOf(this.tempRiseTotalRows.toString()) === -1) {
              this.tempRisePageSizeOptions.push(this.tempRiseTotalRows.toString())
            }

            for (let i = 0; i < this.originData.length; i++) {
              this.originData[i].id = i
            }

            // 容量数据
            this.capacityData = JSON.parse(res.data.allDataJson).capacityList

            // 能量数据
            this.energyData = JSON.parse(res.data.allDataJson).energyList

            // 最大温升数据
            this.maxTempRiseData = JSON.parse(res.data.allDataJson).maxTempRiseList
            this.update = false
          })
          .then(async res => {
            let properties = Object.getOwnPropertyNames(this.originData[0]);
            properties = properties.filter(item => item !== '__ob__' && item !== 'id');

            let tempRiseProperties = Object.getOwnPropertyNames(this.originTempRiseData[0]);
            tempRiseProperties = tempRiseProperties.filter(item => item !== '__ob__');

            let capacityProperties = Object.getOwnPropertyNames(this.capacityData[0]);
            capacityProperties = capacityProperties.filter(item => item !== '__ob__');

            let energyProperties = Object.getOwnPropertyNames(this.energyData[0]);
            energyProperties = energyProperties.filter(item => item !== '__ob__');

            let maxTempRiseProperties = Object.getOwnPropertyNames(this.maxTempRiseData[0]);
            maxTempRiseProperties = maxTempRiseProperties.filter(item => item !== '__ob__');

            let firstHeaderList = this.allDataJson.exportTableHeaderList[0]
            let secondHeaderList = this.allDataJson.exportTableHeaderList[1]
            this.thirdHeaderList = properties

            let firstTempRiseHeaderList = this.allDataJson.tempRiseRowHeaderList[0]
            let secondTempRiseHeaderList = this.allDataJson.tempRiseRowHeaderList[1]
            let thirdTempRiseHeaderList = this.allDataJson.tempRiseRowHeaderList[2]

            // 表头处理
            if (this.originData.length > 0) {
              for (let i = 0; i < properties.length; i++) {
                let finalHeader = {
                  title: firstHeaderList[i],
                  align: "center",
                  width: "100px",
                  children: [
                    {
                      title: secondHeaderList[i],
                      align: "center",
                      width: "100px",
                      children: [
                        {
                          align: "center",
                          width: "100px",
                          dataIndex: properties[i],
                          slots: { title: this.thirdHeaderList[i] },
                        }
                      ]
                    },
                  ]
                }
                this.originColumns.push(finalHeader)
              }
              for (let i = 0; i < tempRiseProperties.length; i++) {
                let finalHeader = {
                  title: firstTempRiseHeaderList[i],
                  width: "100px",
                  align: "center",
                  children: [
                    {
                      title: secondTempRiseHeaderList[i],
                      width: "100px",
                      align: "center",
                      children: [
                        {
                          title: thirdTempRiseHeaderList[i],
                          width: "100px",
                          align: "center",
                          dataIndex: tempRiseProperties[i],
                        }
                      ]
                    },
                  ]
                }
                this.originTempRiseColumns.push(finalHeader)
              }

              this.capacityColumns = [{
                title: "电芯",
                dataIndex: "batteryNoOrAverage",
                align: "center",
              }]
              for (let i = 1; i < capacityProperties.length; i++) {
                let finalHeader = {
                  title: "容量/Ah",
                  align: "center",
                  children: [
                    {
                      title: capacityProperties[i],
                      align: "center",
                      dataIndex: capacityProperties[i],
                    },
                  ]
                }
                this.capacityColumns.push(finalHeader)
              }

              this.energyColumns = [{
                title: "电芯",
                width: '100px',
                dataIndex: "batteryNoOrAverage",
                align: "center",
              }]
              for (let i = 1; i < energyProperties.length; i++) {
                let finalHeader = {
                  title: "能量/Wh",
                  width: '100px',
                  align: "center",
                  children: [
                    {
                      title: energyProperties[i],
                      align: "center",
                      width: '100px',
                      dataIndex: energyProperties[i],
                    },
                  ]
                }
                this.energyColumns.push(finalHeader)
              }

              this.maxTempRiseColumns = [{
                title: "电芯",
                dataIndex: "batteryNoOrAverage",
                align: "center",
              }]
              for (let i = 1; i < maxTempRiseProperties.length; i++) {
                let finalHeader = {
                  title: "最大温升/℃",
                  align: "center",
                  children: [
                    {
                      title: maxTempRiseProperties[i],
                      align: "center",
                      dataIndex: maxTempRiseProperties[i],
                    },
                  ]
                }
                this.maxTempRiseColumns.push(finalHeader)
              }
              const temHeight = document.body.clientHeight - document.getElementById("export").offsetHeight - 610
              document.documentElement.style.setProperty(`--height`, `${temHeight}px`)
            }

            // 第一张图--Rate Discharge
            await this.initNormalEchart('cRate', 'capacity', 'capacityEchartsDataList')

            /* 缩略图 */
            for (let i = 0; i < this.cRateArr.length; i++) {
              this.initThumbnailEchart(this.cRateArr[i].id, this.cRateArr[i].id === 'capacityVoltage' ? 'capacityTempEchartsDataList' : null, this.cRateArr[i].id === 'capacityVoltage' ? 'capacityEchartsDataList' : null)
            }

          }).then(res => {
            let originalTableClass = $(".originalTableClass").find('.ant-table-header').find('.ant-table-thead').find('tr:eq(1)').find('th')
            originalTableClass.each(function (index) {
              var element = $(this);
              // 访问元素的索引位置
              let realIndex = index + 1;
              if (realIndex % 4 === 0) {
                element.attr('colspan', 4);
              } else {
                element.css("display", "none");
              }

            });


          })
      },

      _handleCRateEchartData(target, cyclicList, echartList = [], extraLegendName = ['']) {
        this.editObj = target

        let normalLegent = []
        const normalSeries = []
        const normalOriginalSeries = []
        const normalSeriesTier = []
        const normalOriginalSeriesTier = []
        const templateParam = this.reportChartTemplateList[target].templateParamJson
        const originalParam = this.reportChartTemplateList[target].originalParamJson
        const echartsColorList = cyclicList.length <= 2 ? this.echartsColorShortList : this.echartsColorLongList
        
        let lineColorList = [] // 折线颜色

        const result1 = this._getCRateSeriesAndLegent(cyclicList, echartsColorList, lineColorList, extraLegendName[0])
        normalLegent.push(...result1[0])
        normalSeries.push(...result1[1])
        normalOriginalSeries.push(...result1[4])
        normalSeriesTier.push(...result1[2])
        normalOriginalSeriesTier.push(...result1[3])

        if (echartList.length > 0) {
          const result2 = this._getCRateSeriesAndLegent(echartList, echartsColorList, lineColorList, extraLegendName[1],true)
          normalLegent.push(...result2[0])
          normalSeries.push(...result2[1])
          normalOriginalSeries.push(...result2[4])
          normalSeriesTier.push(...result2[2])
          normalOriginalSeriesTier.push(...result2[3])
        }

        // 去重
        normalLegent = _.uniq(normalLegent);

        this.originalLegent[target] = _.cloneDeep(normalLegent)
        this.originalData[target] = {
          ...this._getCRateEchartOriginal(target),
          series:originalParam?.series ?? _.cloneDeep(normalOriginalSeries),
          originalSeries:originalParam?.originalSeries ?? _.cloneDeep(normalOriginalSeriesTier)
          
        }

        this.editData[target] = {
          ...this._getCRateEchartOriginal(target,'edit'),
          legend:templateParam.legendData?.legendList ?? _.cloneDeep(normalLegent),
          legendSort:templateParam.legendData?.legendSort ?? _.cloneDeep(normalLegent),
          legendRevealList:templateParam.legendData?.legendRevealList ?? _.cloneDeep(normalLegent),
          legendEditName:templateParam.legendData?.legendEditName ??  _.cloneDeep(normalLegent).map(v => { return {id:v, originName: v, previousName: '', newName: '', isReset: false } }),
          series:_.cloneDeep(normalOriginalSeries),
          originalSeries:_.cloneDeep(normalOriginalSeriesTier),
          editSeries:_.cloneDeep(normalSeriesTier),
          allData:templateParam.allData ?? {}
        }

        const property = ['legendIndeterminate','checkAll','legendRevealIndeterminate','legendRevealcheckAll']

        for(let j = 0;j < property.length ;j++){
          if(templateParam.legendData[property[j]]){
            this.editData[target][property[j]] = templateParam.legendData[property[j]]
          } 
        }

        return normalSeriesTier
      },
      _getCRateEchartOriginal(target,type = 'original') {   
        const isEdit = type === 'edit'
        const templateParam = this.reportChartTemplateList[target].templateParamJson
        const originalParam = this.reportChartTemplateList[target].originalParamJson


        const options = {
          chartTitle:isEdit && templateParam.chartTitle ? templateParam.chartTitle : (target === 'tempRise' ? 'Rate Discharge of Temperature Rise' : 'Rate Discharge'),
          XTitle:isEdit && templateParam.XTitle ? templateParam.XTitle : 'Capacity (Ah)',
          YTitle:isEdit && templateParam.YTitle ? templateParam.YTitle : (target === 'tempRise' ? 'Temperature rise (℃)' : 'Voltage (V)'),
          titleTop:isEdit && templateParam.titleTop ? templateParam.titleTop :  10,
          yTitleLetf:isEdit && templateParam.yTitleLetf ? templateParam.yTitleLetf :  35,
          
          legendWidth:isEdit && templateParam.legendWidth ? templateParam.legendWidth :  20,
          legendHeight: isEdit && templateParam.legendHeight ? templateParam.legendHeight : 5,
          legendGap: isEdit && templateParam.legendGap ? templateParam.legendGap : 10,
          legendBgColor: isEdit && templateParam.legendBgColor ? templateParam.legendBgColor : '#f5f5f5',
          legendOrient: isEdit && templateParam.legendOrient ? templateParam.legendOrient : 'horizontal',
          legendTop: isEdit && templateParam.legendTop ? templateParam.legendTop : 50,
          legendLeft: isEdit && templateParam.legendLeft ? templateParam.legendLeft : 'center',

          gridTop: isEdit && templateParam.gridTop ? templateParam.gridTop : 45,
          gridLeft: isEdit && templateParam.gridLeft ? templateParam.gridLeft : 60,
          gridRight: isEdit && templateParam.gridRight ? templateParam.gridRight : 60,
          gridBottom: isEdit && templateParam.gridBottom ? templateParam.gridBottom : 55,

          xType: isEdit && templateParam.xType ? templateParam.xType : 'value',
          yType: isEdit && templateParam.yType ? templateParam.yType : 'value',
        }

        if(target === 'capacityVoltage'){
            options.yType2 = isEdit && templateParam.yType2 ? templateParam.yType2 : 'value',
            options.YTitle2 =isEdit && templateParam.YTitle2 ? templateParam.YTitle2 :  'Temperature (℃)'
            options.yTitleRight= isEdit && templateParam.yTitleRight ? templateParam.yTitleRight :  35   
        }

        const property = ['xMin','xMax','xInterval','yMin','yMax','yInterval','yMin2','yMax2','yInterval2']
        if (type === 'edit') {
          options.allData = templateParam.allData ?? {}
          for(let i = 0;i < property.length;i++){
            if(templateParam[property[i]]) options[property[i]] = templateParam[property[i]]
          }
        }

        if (type === 'original' && originalParam) {
          for(let i = 0;i < property.length;i++){
            options[property[i]] = originalParam[property[i]]
          }
        }


        return options
      },
      _getCRateSeriesAndLegent(cyclicList, echartsColorList, lineColorList, extraLegendName,yRight = false) {
        // yRight 右边坐标轴
        const normalLegent = []
        const normalSeries = []
        const normalOriginalSeries = []
        const normalSeriesTier = []
        const normalOriginalSeriesTier = []
        const templateParam = this.reportChartTemplateList[this.editObj].templateParamJson
        for (let i = 0; i < cyclicList.length; i++) {
          const legendId = cyclicList[i].curAndBatNo + extraLegendName
          const series = []
          const originalSeries = []

          if(!lineColorList.some(item => item.id === legendId)){
            lineColorList.push({ id: legendId, color: echartsColorList[lineColorList.length] })
          }
          const temColor = lineColorList[lineColorList.findIndex(v => v.id === legendId)].color
          const templateContent = (templateParam.checkData && templateParam.checkData.length !== 0) ? (templateParam.checkData.filter(item => item.id === legendId + (i + 1))[0] || {}) : {}
           
          series.push({
            id: legendId + (i + 1),
            name: legendId,
            type: 'line',
            sampling: 'lttb',
            large: true,
            barGap: 0,
            markPoint: {
              data: []
            },
            symbol: templateContent.symbol ??  "none",
            lineStyle: {
              width: templateContent.lineWidth ?? 1.5,
              type: templateContent.lineType ?? 'solid',
              color: templateContent.lineColor ?? temColor
            },
            itemStyle: {
              color: templateContent.itemColor ?? temColor
            },
            data: cyclicList[i].data,
          })
          if(yRight)  series[series.length - 1].yAxisIndex = 1
          if(templateContent.symbolSize) series[series.length - 1].symbolSize = templateContent.symbolSize
          if(templateContent.connectNulls) series[series.length - 1].connectNulls = templateContent.connectNulls
          if(templateContent.maxPoint) series[series.length - 1].markPoint.data.push({type:'max',name:'max'})
          if(templateContent.minPoint) series[series.length - 1].markPoint.data.push({type:'min',name:'min'})

          originalSeries.push({
            id: legendId + (i + 1),
            soc: legendId,
            index: i + 1,
            name: legendId,
            type: 'line',
            sampling: 'lttb',
            large: true,
            barGap: 0,
            symbol: templateContent.symbol ?? "none",
            symbolSize: templateContent.symbolSize ?? 0,
            maxPoint:templateContent.maxPoint ?? false,
            minPoint:templateContent.minPoint ?? false,
            connectNulls:templateContent.connectNulls ?? false,
            lineType:templateContent.lineType ?? 'solid',
            lineWidth:templateContent.lineWidth ?? 1.5,
            lineColor:templateContent.lineColor ?? temColor,
            itemColor:templateContent.itemColor ?? temColor
          })

           if(templateParam.legendData?.legendList){
            if(templateParam.legendData.legendList.includes(legendId)){
              normalSeries.push(...originalSeries)
              normalSeriesTier.push(...series)
            }
          }else{
            normalSeries.push(...originalSeries)
            normalSeriesTier.push(...series)
          }
          normalLegent.push(legendId)
          normalOriginalSeries.push(...originalSeries)
          normalOriginalSeriesTier.push(...series)
        }

        return [normalLegent, normalSeries, normalSeriesTier,normalOriginalSeriesTier,normalOriginalSeries]
      },

      _handleCRateEchartOptions(target, seriesList) {

        const templateParam = this.reportChartTemplateList[target].templateParamJson
        const originalParam = this.reportChartTemplateList[target].originalParamJson

        const firstItem = this.fristInit[target]  && !originalParam
        const editItem = this.editData[target]
        const originalItem = this.originalData[target]

        const options = {
          backgroundColor: '#ffffff',
          animationDuration: 2000,
          title: {
            text: firstItem ? originalItem.chartTitle : editItem.chartTitle,
            left: 'center',
            top: firstItem ? originalItem.titleTop : editItem.titleTop,
            fontSize: 18,
            fontWeight: 500,
            color: "#000"
          },
          grid: {
            show: true,
            top: firstItem ? originalItem.gridTop : editItem.gridTop,
            left: firstItem ? originalItem.gridLeft : editItem.gridLeft,
            right: firstItem ? originalItem.gridRight : editItem.gridRight,
            bottom: firstItem ? originalItem.gridBottom : editItem.gridBottom,
            borderWidth: 0.5,
            borderColor: "#ccc"
          },
          textStyle: {
            fontFamily: "Times New Roman"
          },
          tooltip: {
            trigger: "axis",
            confine: true,
            enterable: true,
            hideDelay: 300,
            extraCssText: 'max-height: 400px; overflow-y: auto; scrollbar-width: thin; scrollbar-color: #888 #f1f1f1; pointer-events: auto;',
            formatter: function (params) {
              var result = params[0].axisValue + "<br>" // 添加 x 轴的数值
              params.forEach(function (item, dataIndex) {
                result +=
                  item.marker +
                  item.seriesName +
                  '<div style="width:20px;display: inline-block;"></div><div style="width:150px;display: inline-block;">' +
                  "</div>" +
                  item.value[1] +
                  "<br>" // 添加每个系列的数值
              })
              // 直接返回内容，滚动由 extraCssText 控制
              return result
            }
          },
          legend: {
            show: true,
            backgroundColor: firstItem ? originalItem.legendBgColor : editItem.legendBgColor,
            top: firstItem ? originalItem.legendTop : editItem.legendTop,
            left: firstItem ? 'center' : editItem.legendLeft,
            itemWidth: firstItem ? originalItem.legendWidth : editItem.legendWidth,
            itemHeight: firstItem ? originalItem.legendHeight : editItem.legendHeight,
            itemGap: firstItem ? originalItem.legendGap : editItem.legendGap,
            textStyle: {
              fontSize: 14,
              color: "#000000"
            }
          },
          xAxis: [
            {
              name: firstItem ? originalItem.XTitle : editItem.XTitle,
              type: firstItem ? originalItem.xType : editItem.xType,
              nameLocation: 'middle', // 将名称放在轴线的中间位置
              nameGap: 30,
              nameTextStyle: {
                fontSize: 14, // 可以根据需要调整字体大小
                fontWeight: 500,
                color: "#000000" // 可以根据需要调整字体大小
              },
              boundaryGap: false,
              axisTick: { show: false },
              axisLabel: {
                show: true,
                width: 0.5,
                textStyle: {
                  fontSize: "15",
                  color: "#000000"
                }
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#ccc",
                  width: 0.5
                }
              },
              splitLine: {
                show: true, // 显示分隔线
                lineStyle: {
                  type: "solid",
                  width: 0.5
                }
              },
            }
          ],
          yAxis: [
            {
              name: firstItem ? originalItem.YTitle : editItem.YTitle,
              type: firstItem ? originalItem.yType : editItem.yType,
              nameGap: firstItem ? originalItem.yTitleLetf : editItem.yTitleLetf,
              position: 'left',
              nameLocation: 'middle', // 将名称放在轴线的起始位置
              nameRotate: 90, // 旋转角度，使名称竖排
              nameTextStyle: {
                fontSize: 14, // 可以根据需要调整字体大小
                fontWeight: 500,
                color: "#000000"
              },
              splitLine: {
                show: true,  // 显示分隔线
                lineStyle: {
                  type: 'solid',  // 设置分隔线的样式，比如虚线
                  width: 0.5
                }
              },
              axisTick: {
                show: false,  // 显示刻度
              },
              axisLabel: {
                show: true,
                width: 0.5,
                textStyle: {
                  fontSize: "15",
                  color: "#000000"
                }
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#ccc",
                  width: 0.5
                }
              },

            }
          ],
          series: seriesList
        }

        // 只有第三张图有双Y轴
        if (target === 'capacityVoltage') {
          options.yAxis.push({
            name: firstItem ? originalItem.YTitle2 : editItem.YTitle2,
            type: firstItem ? originalItem.yType2 : editItem.yType2,
            position: 'right',
            nameGap: firstItem ? originalItem.yTitleRight : editItem.yTitleRight,
            nameLocation: 'middle', // 将名称放在轴线的起始位置
            nameRotate: 90, // 旋转角度，使名称竖排
            nameTextStyle: {
              fontSize: 14,
              fontWeight: 500,
              color: "#000000" // 可以根据需要调整字体大小
            },
            yAxisIndex: 1,
            splitLine: {
              show: true,  // 显示分隔线
              lineStyle: {
                type: 'solid'  // 设置分隔线的样式，比如虚线
              }
            },
            axisTick: {
              show: true,  // 显示刻度
            },
            axisLabel: {
              show: true,
              width: 0.5,
              textStyle: {
                fontSize: "15",
                color: "#000000"
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              }
            },
            alignTicks: true,
          })
        }

        // 非首次加载才需要
        //处理图例
        //图例名称
        if (!firstItem) {
          const newSeriesList = []
          _.cloneDeep(seriesList).forEach(v => {
            const haveNameList = editItem.legendEditName.filter(filterItem => filterItem.originName === v.name && filterItem.newName)
            v.name = haveNameList.length === 0 ? v.name : haveNameList[0].newName
            newSeriesList.push(v)
          })
          options.series = newSeriesList

          const legend = []
          editItem.legendSort.forEach(v => {
            if (editItem.legend.includes(v) && editItem.legendRevealList.includes(v)) {
              const haveList = editItem.legendEditName.filter(filterItem => filterItem.originName === v && filterItem.newName)
              legend.push(haveList.length === 0 ? v : haveList[0].newName)
            }
          })
          options.legend.data = legend
        }


        // 如果非首次编辑
        // 默认是水平，如果是水平，就不添加，垂直才添加
        if (editItem.legendOrient === 'vertical') {
          options.legend.orient = editItem.legendOrient
        }
        if (editItem.legendLeft !== undefined) {
          options.legend.left = editItem.legendLeft
        }


        // X轴可能没有最大最小值、间隔
        if (!firstItem && editItem.xInterval && editItem.xType === 'value') {
          options.xAxis[0].min = editItem.xMin
          options.xAxis[0].max = editItem.xMax
          options.xAxis[0].interval = editItem.xInterval
        }
        if (!firstItem && editItem.yType === 'value') {
          options.yAxis[0].min = editItem.yMin
          options.yAxis[0].max = editItem.yMax
          options.yAxis[0].interval = editItem.yInterval
        }
        // 双Y轴才需要
        if (!firstItem && editItem.yType2 === 'value' && ['capacityVoltage'].includes(target)) {
          options.yAxis[1].min = editItem.yMin2
          options.yAxis[1].max = editItem.yMax2
          options.yAxis[1].interval = editItem.yInterval2
        }
        return options
      },
      _handleCRateYAxisValue(target) {

        const XAxis = this.echartObj[target].getModel().getComponent("xAxis").axis.scale
        const YAxis1 = this.echartObj[target].getModel().getComponent("yAxis", 0).axis.scale
        let YAxis2 = null

        const editItem = this.editData[target]
        const originalObj = this.originalData[target]
        const originalParam = this.reportChartTemplateList[target].originalParamJson


        editItem.xMin = XAxis._extent[0]
        editItem.xMax = XAxis._extent[1]
        editItem.xInterval = XAxis._interval

        editItem.yMin = YAxis1._extent[0]
        editItem.yMax = YAxis1._extent[1]
        editItem.yInterval = YAxis1._interval

        

        

        if (target === 'capacityVoltage') {
          YAxis2 = this.echartObj[target].getModel().getComponent("yAxis", 1).axis.scale
          editItem.yMin2 = YAxis2._extent[0]
          editItem.yMax2 = YAxis2._extent[1]
          editItem.yInterval2 = YAxis2._interval

          if(!originalParam){
           originalObj.yMin2 = YAxis2._extent[0]
            originalObj.yMax2 = YAxis2._extent[1]
            originalObj.yInterval2 = YAxis2._interval
          }
        }

        if(!originalParam){
          originalObj.xMin = XAxis._extent[0]
          originalObj.xMax = XAxis._extent[1]
          originalObj.xInterval = XAxis._interval

          originalObj.yMin = YAxis1._extent[0]
          originalObj.yMax = YAxis1._extent[1]
          originalObj.yInterval = YAxis1._interval
        }


      },

      _getCRateThumbnailSeries(cyclicList, dataName, legendName, extraLegendName = '') {
        // cyclicList  : 循环数组
        // dataName : 循环数组中存数据的数组名称
        // legendName : 循环数组中图例的名称
        // extraLegendName : 循环数组中图例的额外名称
        let series = []
        cyclicList.forEach(v => {
          // 避免点为空值
          v[dataName] = v[dataName].filter(filter => filter[1] !== '')

          const multiplier = Math.trunc(v[dataName].length / 10)

          const seriesData = v[dataName].length > 10 ? new Array(10).fill(1).map((mapItem, mapIndex) => v[dataName][mapIndex * multiplier]) : v[dataName]

          series.push(
            {
              name: v[legendName] + extraLegendName,
              type: 'line',
              symbol: 'none',
              data: seriesData
            }
          )
        })
        return series
      },

      // 重置
      handleDrawerReset() {
        this.$confirm({
          title: '请确认是否重置图表?',
          content: '图表重置后，图表修改内容无法恢复',
          okText: '重置',
          cancelText: '取消',
          onOk:async () => {
            await this.deleteChartTemplate({ reportId:this.id,id:this.reportChartTemplateList[this.editObj].templateId,targetChart:this.editObj })

            this.fristInit[this.editObj] = true
            this.drawerVisible = false
            this.initNormalEchart('cRate', this.editObj, this.editObj === 'capacityVoltage' ? 'capacityTempEchartsDataList' : `${this.editObj}EchartsDataList`, this.editObj === 'capacityVoltage' ? 'capacityEchartsDataList' : null)
            this.$message.success("重置成功")
          },
          onCancel() {}
        });
      },

      // 重新选择模板
      async handleChangeTemplate(targetObj){
        await this.getChartTemplateRelationList(this.$route.query.id,[targetObj])
        this.fristInit[targetObj] = true
        this.drawerVisible = false
        this.initNormalEchart('cRate', targetObj, targetObj === 'capacityVoltage' ? 'capacityTempEchartsDataList' : `${targetObj}EchartsDataList`, targetObj === 'capacityVoltage' ? 'capacityEchartsDataList' : null)
      },

      onTempRisePageChange(tempRisePageNo, tempRisePageSize) {
        this.tempRisePageNo = tempRisePageNo;
        this.tempRisePageSize = tempRisePageSize;
        getCRateTestReport({ id: this.id, tempRisePageNo: this.tempRisePageNo, tempRisePageSize: this.tempRisePageSize })
          .then(res => {
            this.tempRisePageData = JSON.parse(res.data.allDataJson).tempRiseRowList
            this.originTempRiseData = this.tempRisePageData.records
            this.tempRisePageNo = this.tempRisePageData.current
            this.tempRisePageSize = this.tempRisePageData.size
            this.tempRiseTotalRows = this.tempRisePageData.total
            if (this.tempRisePageSizeOptions.indexOf(this.tempRiseTotalRows.toString()) === -1) {
              this.tempRisePageSizeOptions.push(this.tempRiseTotalRows.toString())
            }
          })
      },
      onPageChange(pageNo, pageSize) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        getCRateTestReport({ id: this.id, pageNo: this.pageNo, pageSize: this.pageSize })
          .then(res => {
            this.pageData = JSON.parse(res.data.allDataJson).tableList
            this.originData = this.pageData.records
            this.pageNo = this.pageData.current
            this.pageSize = this.pageData.size
            this.totalRows = this.pageData.total
            if (this.pageSizeOptions.indexOf(this.totalRows.toString()) === -1) {
              this.pageSizeOptions.push(this.totalRows.toString())
            }
          })
      },
      setFormula() {
        if (this.selectedRows.length < 1) {
          this.$message.warning("请选择行数据")
          return
        }
        if (this.selectedHeaderNameRowKeys.length < 1) {
          this.$message.warning("请选择列数据")
          return
        }
        this.setFormulaFlag = true
      },
      handleSetFormulaModalOk() {
        if (!this.symbol) {
          this.$message.warning("请选择计算符号")
          return
        }
        if (!this.symboledNumber) {
          this.$message.warning("请填写数字")
          return
        }
        let param = {
          row: this.selectedRowKeys,
          column: this.selectedHeaderNameRowKeys,
          symbol: this.symbol,
          symboledNumber: this.symboledNumber,
        }
        updateCRateData(param, this.id).then(res => {
          if (res.success) {
            this.$message.success('修改成功')
            this.init()
          } else {
            this.$message.error('修改失败：' + res.message)
          }
        })
      },
      handleModalCancel(target) {
        this[target] = false
      },
      onSelect(record, selected, selectedRows, nativeEvent) {
        this.selectedRows = selectedRows
        this.selectedRowKeys = selectedRows.map(item => item.id)
      },
      onSelectAll(selected, selectedRows, changeRows) {
        this.selectedRows = selectedRows
        this.selectedRowKeys = selectedRows.map(item => item.id)
      },
      checkHeaderName(event, dataIndex) {
        if (event.target.checked) {
          if (!this.selectedHeaderNameRowKeys.includes(dataIndex)) {
            this.selectedHeaderNameRowKeys.push(dataIndex)
          }
        } else {
          if (this.selectedHeaderNameRowKeys.includes(dataIndex)) {
            this.selectedHeaderNameRowKeys = this.selectedHeaderNameRowKeys.filter(item => item !== dataIndex);
          }
        }
      },
      cancel() {
        this.init()
      },


    }
  }
</script>
<style lang="less" scoped>
  @import './css/thumbnail.less';

  :root {
    --height: 600px;
  }

  /deep/ .ant-table-thead>tr>th {
    padding: 5px !important;
    font-size: 13px !important;
  }

  /deep/ .ant-table-tbody>tr>td {
    padding: 0px !important;
    height: 32px !important;
    font-size: 12px !important;
  }

  /deep/ .ant-calendar-picker-icon {
    display: none;
  }

  /deep/ .ant-calendar-picker-input.ant-input {
    color: black;
    font-size: 12px;
    border: 0;
    text-align: center;
    padding: 0;
  }

  .red {
    background-color: #ed0000;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .yellow {
    background-color: #ffc000;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .grey {
    background-color: rgba(223, 223, 223, 0.25);
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .ant-modal-body {
    padding: 0;
  }

  /deep/ .ant-btn>i,
  /deep/ .ant-btn>span {
    display: flex;
    justify-content: center;
  }

  /deep/ .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
    color: #fff;
    background: #1890ff;
  }

  .green {
    background-color: #58a55c;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  /deep/ #table1>div>div>div>div>div>div>table>thead {
    height: 64px;
  }

  /deep/ #table1>.ant-table-wrapper>div>div>ul {
    display: none;
  }

  /deep/ .ant-table-pagination.ant-pagination {
    float: right;
    margin: 0;
  }

  .float {
    padding-bottom: 10px;
  }

  .float1 {
    width: 12%;
    float: left;
    margin-right: 10px;
    text-align: center;
  }

  /deep/ .ant-checkbox-group-item {
    display: block;
    width: 100%;
    text-align: left;
  }

  .title {
    font-size: large;
    margin-bottom: 20px;
  }

  .numTitle {
    font-size: xx-large;
  }

  /deep/ .ant-table-footer {
    padding: 0;
  }

  /deep/ .ant-table-row-expand-icon {
    margin-right: 0px;
  }

  .wrapper {
    background: #fff;
    height: 100vh;
    padding: 6px 16px 16px;
    margin: 0 0 0 -40px;
    background-color: #f0f2f5;

    overflow: scroll;
  }

  .head_title {
    color: #333;
    padding: 10px 0;
    font-size: 20px;
    font-weight: 600;
  }

  .head_title::before {
    width: 8px;
    background: #1890ff;
    margin-right: 8px;
    content: "\00a0"; //填充空格
  }

  .export-btn {
    position: fixed;
    bottom: 10px;
    right: 10px;
    width: 15%;
  }

  .all-wrapper {
    padding: 0 0 10px;
    display: flex;
    justify-content: space-between;
  }

  .btn-wrap {
    text-align: right;
  }

  .example-icon {
    width: 20px;
    height: 20px;
    color: #1890ff;
    vertical-align: middle;
    margin-left: 3px;
    margin-top: 3px;
  }

  // 通用
  .mt10 {
    margin-top: 10px;
  }

  .mr5 {
    margin-right: 5px;
  }

  .mb5 {
    margin-bottom: 5px;
  }

  .mr10 {
    margin-right: 10px;
  }

  .title-line {
    width: 8px;
    height: 30px;
    background: #1890ff;
    border-radius: 2px;
    margin-right: 8px;
    content: "\00a0"; //填充空格

    position: absolute;
    top: 8px;
    left: -4px;
  }

  .flex-column {
    display: flex;
    flex-direction: column;
  }

  .flex-sb-center-row {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .strong {
    background-size: 100% 100%;
    width: 210px;
    height: 25px;
    color: #333;
    display: flex;
    align-items: center;
    padding: 5px;
  }

  .block {
    height: fit-content;
    padding: 10px;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
    position: relative;
  }

  .left-content {
    width: 618px;
    margin-right: 10px;
  }

  .right-content {
    width: calc(100% - 628px);
  }

  .right-content .all-checkbox {
    padding-bottom: 5px;
    margin-bottom: 5px;
    border-bottom: 1px solid #e9e9e9;
  }

  .normal-btn {
    padding: 5px 10px;
    color: #fff;
    background-color: #1890ff;
    letter-spacing: 2px;
    cursor: pointer;
  }

  .footer-btn {
    width: 100%;
    height: 32px;
    border: 1px solid #e8e8e8;
    background: #fff;
    color: #999;
    font-size: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }

  .footer-btn:hover {
    color: #1890ff;
  }

  // 组件
  /deep/ .ant-steps {
    padding: 15px 50px;
  }

  /deep/ .left-content .ant-table-body {
    border: 1px solid #e8e8e8;
    overflow: auto;
  }

  /deep/ .right-content .ant-table-body {
    border: 1px solid #e8e8e8;
    overflow: auto;
  }

  /deep/ .all-wrapper .ant-table-thead {
    position: sticky;
    top: 0;
    z-index: 2;
  }

  /deep/ .all-wrapper .ant-table-placeholder {
    border: none !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 0;
  }

  /deep/ .right-content .ant-empty-normal {
    margin: -2px 0;
  }

  /deep/ .ant-empty-image {
    display: none;
  }

  /deep/ .right-content .ant-input {
    border: none;
  }

  /deep/ .ant-checkbox-group {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
  }

  /deep/ .ant-checkbox-group-item {
    font-size: 12px;
    width: 23%;
  }

  /deep/ .ant-radio-inner {
    top: 1px;
    left: 1px;
  }

  /deep/ .ant-table-body::-webkit-scrollbar {
    height: 10px;
    width: 5px;
  }

  /deep/ .ant-table-body::-webkit-scrollbar-thumb {
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;

    background: #dddbdb;
  }

  /deep/ .ant-table-body::-webkit-scrollbar-track {
    -webkit-box-shadow: 0;
    border-radius: 0;
    background: #f1f1f1;
  }

  /deep/ .ant-select-selection__rendered {
    margin-right: 0px;
  }

  /deep/ .ant-form-item {
    margin-bottom: 0;
  }

  /deep/ .ant-popover-buttons {
    display: flex !important;
    flex-direction: column !important;
    margin-bottom: 15px;
  }

  .tips {
    color: #1890ff;
  }

  .button-tips {
    display: flex;
    flex-direction: column;
  }

  /deep/ .ant-select-selection-selected-value {
    width: 80px;
  }
</style>