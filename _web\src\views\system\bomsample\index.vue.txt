<template>
    <div style="background:#fff">
        <!-- <x-card>
          <div slot="content" class="table-page-search-wrapper" >
            <a-form layout="inline">
              <a-row :gutter="48">
                <a-col :md="8" :sm="24">
                  <a-form-item label="参数名称">
                    <a-input v-model="queryParam.name" allow-clear placeholder="请输入参数名称"/>
                  </a-form-item>
                </a-col>
                <a-col :md="!advanced && 8 || 24" :sm="24" >
                  <span class="table-page-search-submitButtons" :style="advanced && { float: 'right', overflow: 'hidden' } || {} ">
                    <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                    <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                  </span>
                </a-col>
              </a-row>
            </a-form>
          </div>
        </x-card> -->
        <a-card :bordered="false" :title="title">
            <s-table size='small' ref="table" :columns="columns" :data="loadData" :alert="false" :rowKey="(record) => record.id">
                <template slot="operator">
                    <div style="text-align:right;margin-bottom:6px">
                        <a-button @click="$refs.addForm.add()" icon="plus" type="primary">新增</a-button>
                    </div>
                </template>
                <span slot="productStage" slot-scope="text">
                    {{ 'product_stage_status' | dictType(text) }}
                </span>
                <span slot="isFeedback" slot-scope="text">
                    {{ 'is_or_no' | dictType(text) }}
                </span>
                <span slot="isPass" slot-scope="text">
                    {{ 'is_or_no' | dictType(text) }}
                </span>
                <span slot="action" slot-scope="text, record">
                    <a @click="$refs.editForm.edit(record)">编辑</a>
                    <a-divider type="vertical" />
                    <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => sampleDelete(record)">
                        <a>删除</a>
                    </a-popconfirm> 
                </span>
      </s-table>
      <add-form ref="addForm" :issueId="issueId" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
    import {
        STable,
        Ellipsis,
        XCard
    } from '@/components'
    import {
        getSamplePage,
        sysSampleDelete
    } from '@/api/modular/system/bomSampleManage'
    import addForm from './addForm'
    import editForm from './editForm'
    export default {
        props: {
            issueId: {
                type: Number,
                default: 0
            },
        },
        components: {
            XCard,
            STable,
            Ellipsis,
            addForm,
            editForm
        },
        
        data() {
            return {
                issueId:0,
                title:'',
                // 查询参数
                queryParam: {
                },
                // 表头
                columns: [
                    {
                        title: '产品阶段',
                        dataIndex: 'productStage',
                        scopedSlots: {
                            customRender: 'productStage'
                        }
                    },
                    {
                        title: '送样批次',
                        dataIndex: 'slap',
                    },
                    {
                        title: '客户',
                        dataIndex: 'customer',
                    },
                    {
                        title: 'DQE',
                        dataIndex: 'dqe',
                    },
                    {
                        title: '送样日期',
                        dataIndex: 'sampleDate',
                    },
                    {
                        title: '送样数量',
                        dataIndex: 'sampleCount',
                    },
                    {
                        title: '客户是否反馈',
                        dataIndex: 'isFeedback',
                        scopedSlots: {
                            customRender: 'isFeedback'
                        }
                    },
                    {
                        title: '客户反馈',
                        dataIndex: 'feedback',
                    },
                    {
                        title: 'EVE回复',
                        dataIndex: 'eveFeedback',
                    },
                    {
                        title: '批次样品是否合格',
                        dataIndex: 'isPass',
                        scopedSlots: {
                            customRender: 'isPass'
                        }
                    },
                    {
                        title: '操作',
                        width: '150px',
                        dataIndex: 'action',
                        scopedSlots: {
                            customRender: 'action'
                        }
                    }
                ],
                loadData: parameter => {
                    
                    return getSamplePage(Object.assign(parameter, this.queryParam)).then((res) => {
                        return res.data
                    })
                },
            }
        },
        created() {
            //this.issueId = parseInt(this.$route.query.issueId)
            this.queryParam.issueId = this.issueId
            //this.title = this.$route.query.name + '产品样品管理'
        },
        methods: {
            sampleDelete(record) {
                sysSampleDelete({id:record.id}).then((res) => {
                    if (res.success) {
                        this.$message.success('删除成功')
                        this.$refs.table.refresh()
                    } else {
                        this.$message.error('删除失败：' + res.message)
                    }
                }).catch((err) => {
                    this.$message.error('删除错误：' + err.message)
                })
            },
            handleOk() {
                this.$refs.table.refresh()
            }
        }
    }
</script>
<style lang="less" scoped=''>
    .table-operator {
        margin-bottom: 18px;
    }
    button {
        margin-right: 8px;
    }
    /deep/.ant-card-body{
        padding: 24px 0;
    }
    /deep/.ant-table{
        margin: 0 2px;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
    }
    /deep/.ant-table-thead > tr > th{
        font-weight: bold;
        background: #f3f3f3 !important;
    }
    /deep/.ant-table-small > .ant-table-content > .ant-table-body{
        margin: 0;
    }
</style>
