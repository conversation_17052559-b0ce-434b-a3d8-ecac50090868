<template>
  <div style="background:#fff">
    <!--<div style="display: flex">
      <a-breadcrumb class="breadcrumb" separator=">">
        &lt;!&ndash;<a-breadcrumb-item><a @click="gotoIndex(-3)">首页看板</a></a-breadcrumb-item>
        <a-breadcrumb-item><a @click="gotoIndex(-2)">产品对齐表</a></a-breadcrumb-item>
        <a-breadcrumb-item><a @click="gotoIndex(-1)">{{$route.query.parentTitle}}</a></a-breadcrumb-item>
        <a-breadcrumb-item><a @click="gotoIndex(-1)">{{$route.query.title}}</a></a-breadcrumb-item>
        <a-breadcrumb-item>客户议题管理</a-breadcrumb-item>&ndash;&gt;
      </a-breadcrumb>

    </div>-->
    <div style="text-align:right">
    <a-button style="margin-right: 25px;margin-bottom: 10px;margin-top: 10px" @click="$refs.addForm.add()" type="primary">新增</a-button>
    </div>
    <div>
      <!--<a-form :form="form" layout="vertical">
        <a-row :gutter="24">
          <a-col :md="4" :sm="4" style="padding-left: 30px;!important;">
            <a-form-item label="会议日期" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback style="margin-bottom: 0;!important;">
              <a-date-picker @change="dateChange" v-decorator="['date1', {rules: [{required: true, message: '请选择会议日期！'}]}]"/>
            </a-form-item>
            <a-form-item label="会议主题" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-textarea :rows="3" style="height: 87px" placeholder="请填写会议主题"
                          v-decorator="['theme', {rules: [{required: true, message: '请填写会议主题!'}]}]"></a-textarea>

            </a-form-item>
          </a-col>
          <a-col :md="5" :sm="5">
            <a-form-item label="会议议程(客户给出)" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-textarea :rows="7" placeholder="请填写会议议程" v-decorator="['agenda',
               {rules: [{required: true, message: '请填写会议议程!'}]}]"></a-textarea>
            </a-form-item>
          </a-col>
          <a-col :md="5" :sm="5">
            <a-form-item label="会议结论" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-textarea :rows="7" placeholder="请填写会议结论" v-decorator="['conclusion',
              {rules: [{required: true, message: '请填写会议结论!'}]}]"></a-textarea>
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="4">
            <a-form-item label="会议资料" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-upload
                name="file"
                :headers="headers" :action="postUrl"
                :multiple="true"
                :showUploadList="true"
                :fileList="fileList"
                v-decorator="['file1',
              {rules: [{required: true, message: '请上传会议资料!'}]}]"

                @change="handleChange"

              >
                <a-button> <a-icon type="upload" /> 上传文件 </a-button>
              </a-upload>
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="4">
            <a-form-item label="会议纪要" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback style="margin-bottom: 0;!important;">
              <a-upload
                name="file"
                :headers="headers" :action="postUrl"  :multiple="false"
                :showUploadList="true"
                :fileList="summaryList"
                @change="handleChange1"
                v-decorator="['file2',
              {rules: [{required: true, message: '请上传会议纪要!'}]}]"
              >
                <a-button style="height: 20px;"> <a-icon type="upload" /> 上传文件 </a-button>
                <div class="&#45;&#45;mb&#45;&#45;rich-text" data-boldtype="0" style="font-family:PingFangSC; font-weight:400; font-size:10px; color:rgba(0,0,0,0.45); font-style:normal; letter-spacing:0px; line-height:14px; text-decoration:none;">支持扩展名：.zip.doc.pdf.jpg...</div>

              </a-upload>
            </a-form-item>
            <a-form-item label="会议录音" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-upload
                name="file"
                @change="handleChange2"
                :showUploadList="true"
                :fileList="soundList"
                :headers="headers" :action="postUrl"  :multiple="false"
                v-decorator="['file3',
              {rules: [{required: true, message: '请上传会议录音!'}]}]"
              >
                <a-button style="height: 20px;"> <a-icon type="upload" /> 上传文件 </a-button>
                <div class="&#45;&#45;mb&#45;&#45;rich-text" data-boldtype="0" style="font-family:PingFangSC; font-weight:400; font-size:10px; color:rgba(0,0,0,0.45); font-style:normal; letter-spacing:0px; line-height:14px; text-decoration:none;">支持扩展名：.zip.doc.pdf.jpg...</div>
              </a-upload>
            </a-form-item>
          </a-col>
          <a-col :md="1" :sm="1">
            <a-button style="height: 180px;width: 100px;
    margin-left: -30px;" @click="handleSubmit">提交</a-button>
          </a-col>
          <a-col :md="1" :sm="1">
            <a-button style="height: 180px;width: 100px;
    margin-left: -30px;" @click="$refs.addForm.add()">新增</a-button>
          </a-col>

        </a-row>





      </a-form>-->




    </div>
    <div class="board">
      <div class="item">

        <!-- <div class="head">{{$route.query.title}}项目议题管理</div> -->
        <a-table :rowKey="(record) =>record.id" :pagination="false"
                 style="padding-top: 8px;"
                 :data-source="list" :columns="columns" size="small">



          <template slot="theme" slot-scope="text">
            <clamp :text="text" :sourceText="[text]"></clamp>
          </template>

          <template slot="agenda" slot-scope="text">
            <clamp :text="text" :sourceText="[text]"></clamp>
          </template>
          <template slot="conclusion" slot-scope="text">
           <clamp :text="text" :sourceText="[text]"></clamp>
          </template>

          <span slot="stage" slot-scope="text,record">{{ 'product_state_status' | dictType(record.stage) }}</span>

          <div slot="fileNames" slot-scope="text,record">
            <span v-if="text==null"></span>
            <span v-else>
              <a v-for="(item,index) in JSON.parse(text)"
              :key="index"
                 @click="sysFileInfoDownload(JSON.parse(record.fileIds)[index])"
              >{{item}}
              <!--  <a-popconfirm placement="topRight" ok-text="删除" cancel-text="取消" @confirm="deleteFiles($event,record,index)">
                  <template slot="title">
                    确认删除文件{{item}}吗
                  </template>
                  <a-icon type="close" style="float: right;color: black;padding-top: 5px" @click="noClick"/>
                </a-popconfirm> -->

                <br v-if="index != JSON.parse(text).length"/></a>
              <!-- <a-upload
                name="file"
                :headers="headers" :action="postUrl"  :multiple="false"
                @change="uploadFiles($event,record)"
                :showUploadList="false"
              >
                <a-button style="height: 22px"> <a-icon type="upload" /> 上传文件 </a-button>
              </a-upload> -->
            </span>
          </div>

          <div slot="summaryName" slot-scope="text,record">
            <span v-if="text==null">
              <!-- <a-upload
                name="file"
                :headers="headers" :action="postUrl"  :multiple="false"
                :showUploadList="false"
                @change="uploadFile($event,record,'summaryId')"
              >
                <a-button style="height: 22px"> <a-icon type="upload" /> 上传文件 </a-button>
              </a-upload> -->
            </span>
            <span v-else><a @click="sysFileInfoDownload(record.summaryId)">{{text}}</a>


              <!-- <a-popconfirm placement="topRight" ok-text="删除" cancel-text="取消" @confirm="deleteFile($event,record,'summaryId')">
                  <template slot="title">
                    确认删除文件{{text}}吗
                  </template>

                 <a-icon type="close" style="float: right;padding-top: 5px"/>
                </a-popconfirm> -->

             </span>

          </div>
          <div slot="soundName" slot-scope="text,record">
            <span v-if="text==null">
              <!-- <a-upload
                name="file"
                :headers="headers" :action="postUrl"  :multiple="false"
                :showUploadList="false"
                @change="uploadFile($event,record,'soundId')"
              >
                <a-button style="height: 22px"> <a-icon type="upload" /> 上传文件 </a-button>
              </a-upload> -->
            </span>
            <span v-else><a @click="sysFileInfoDownload(record.soundId)">{{text}}</a>
              <!-- <a-popconfirm placement="topRight" ok-text="删除" cancel-text="取消" @confirm="deleteFile($event,record,'soundId')">
                  <template slot="title">
                    确认删除文件{{text}}吗
                  </template>
                 <a-icon type="close" style="float: right;padding-top: 5px"/>
                </a-popconfirm> -->
            </span>
            
          </div>
          <div slot="action" slot-scope="text,record">
              <a @click="$refs.editForm.edit(record)">编辑</a>
          </div>
        </a-table>
      </div>
      <add-form :issueId="issueId" :projectdetail="projectdetail" ref="addForm" @ok="callList" />
      <edit-form :issueId="issueId" :projectdetail="projectdetail" ref="editForm" @ok="callList" />
    </div>
  </div>
</template>

<script>

  import addForm from './meeting/addForm'
   import editForm from './meeting/editForm'
  import {
    getMeetingListByIssueId,
    getMeetingById,
    updateMeetingById,
    updateMeetingNull,
    addMeeting
  } from "@/api/modular/system/projectIssue"
  import {  sysFileInfoDownload } from '@/api/modular/system/fileManage'
  import Vue from "vue";
  import {
    ACCESS_TOKEN
  } from '@/store/mutation-types'
  import Iframe from "../../../../layouts/Iframe";
  import moment from "moment";
  import { clamp } from '@/components'
  export default {
    props: {
        issueId: {
            type: Number,
            default: 0
        },
        projectdetail: {
            type: Object,
            default: {}
        }
    },
    components: {Iframe,clamp,addForm,editForm},
    data(){
      return{
        windowHeight: document.documentElement.clientHeight - 120,
        windowWidth: document.documentElement.clientWidth - 100,
        defaultFileList:[],
        postUrl: '/api/sysFileInfo/uploadfile',
        imgUrl: '/api/sysFileInfo/preview?id=',
        form: this.$form.createForm(this),
        saveData:{},
        list:[],
        projectIssue: {},
        fileIds: [],
        summaryList: [],
        fileList: [],
        soundList: [],
        summaryId: [],
        soundId: [],
        columns:[
          {
            title: '序号',
            dataIndex: 'seq',
            align:'center',
            width: 50,
            customRender: (text, record, index) => (<span>{index+1}</span>)
          },
          {
            title:'日期',
            dataIndex:'meetingDate',
            align:'center',
            width: 100,
          },
          {
            title:'项目阶段',
            dataIndex:'stage',
            align:'center',
            width: 100,
            scopedSlots: { customRender: 'stage' },

          }, {
            title:'会议主题',
            dataIndex:'theme',
            align:'center',
            width: 100,
            scopedSlots: { customRender: 'theme' },
          },{
            title:'会议议程(客户给出)',
            dataIndex:'agenda',
            align:'center',
            width: 150,
            scopedSlots: { customRender: 'agenda' },
          },{
            title:'会议结论',
            dataIndex:'conclusion',
            align:'center',
            width: 150,
            scopedSlots: { customRender: 'conclusion' },
          },{
            title:'会议资料',
            dataIndex:'fileNames',
            align:'center',
            scopedSlots: { customRender: 'fileNames' }

          },{
            title:'会议纪要',
            dataIndex:'summaryName',
            align:'center',
            scopedSlots: { customRender: 'summaryName' }
          },
          {
            title:'会议录音',
            dataIndex:'soundName',
            align:'center',
            scopedSlots: { customRender: 'soundName' }
          },{
            title:'操作',
            dataIndex:'action',
            align:'center',
            width: 80,
            scopedSlots: { customRender: 'action' }
          }

        ],
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 24
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 24
          }
        },
        showImg:true,
        showPdf:false,
        headers: {
          Authorization: 'Bearer ' + Vue.ls.get(ACCESS_TOKEN),
        },
      }
    },
    methods: {

      sysFileInfoDownload (id) {
        sysFileInfoDownload({ id: id }).then((res) => {
          this.cardLoading = false
          this.downloadfile(res)
          // eslint-disable-next-line handle-callback-err
        }).catch((err) => {
          this.$message.error('下载错误：获取文件流错误')
        })
      },

      deleteFiles(e,record,index){
        e.stopPropagation();
        let ids =JSON.parse(record.fileIds)
        let names =JSON.parse(record.fileNames)
        let types =JSON.parse(record.fileTypes)

        ids.splice(index,1)
        names.splice(index,1)
        types.splice(index,1)

        let update ={}
        update.id = record.id
        update.fileIds= JSON.stringify(ids),
        update.fileNames= JSON.stringify(names),
        update.fileTypes= JSON.stringify(types)

        updateMeetingById(update).then(res => {
          if(res.success){
            this.callList()
          }else {
            this.$message.error('删除文件失败：' + res.message)
          }
        })


      },
      noClick(e){
        e.stopPropagation();
      },

      deleteFile(e,record,column){


        let update ={}
        update.id = record.id
        update.updateNullColumn = column

        updateMeetingNull(update).then(res => {
          if(res.success){
            this.callList()
          }else {
            this.$message.error('删除文件失败：' + res.message)
          }
        })


      },

      downloadfile (res) {
        var blob = new Blob([res.data], { type: 'application/octet-stream;charset=UTF-8' })
        var contentDisposition = res.headers['content-disposition']
        var patt = new RegExp('filename=([^;]+\\.[^\\.;]+);*')
        var result = patt.exec(contentDisposition)
        var filename = result[1]
        var downloadElement = document.createElement('a')
        var href = window.URL.createObjectURL(blob) // 创建下载的链接
        var reg = /^["](.*)["]$/g
        downloadElement.style.display = 'none'
        downloadElement.href = href
        downloadElement.download = decodeURI(filename.replace(reg, '$1')) // 下载后文件名
        document.body.appendChild(downloadElement)
        downloadElement.click() // 点击下载
        document.body.removeChild(downloadElement) // 下载完成移除元素
        window.URL.revokeObjectURL(href)
      },
      dateChange(date, dateString) {
        if (date == null) {
          this.saveData = ''
        } else {
          this.saveData.meetingDate = moment(date).format('YYYY-MM-DD')
        }
      },

      beforeUpload(file, e) {
        let isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'|| file.type === 'image/jpg' || file.type === 'application/pdf';
        if (!isJpgOrPng) {
          this.$message.error('格式错误，只能上传图片或pdf文件');
          return false;
        }
      },

      gotoIndex(index){
        this.$router.go(index)
      },

      callList(){
        getMeetingListByIssueId({issueId:this.issueId,stage:this.projectdetail.state}).then((res) => {
          if(res.success){
            this.list = res.data
          }
        })
      },

      handleSubmit() {


        const {
          form: {
            validateFields
          }
        } = this
        this.confirmLoading = true
        let query = {issueId:this.issueId,stage:this.projectdetail.state}
        validateFields((errors, values) => {
          if (!errors) {

            let $params =  Object.assign(values, query);
            $params =  Object.assign($params, this.saveData);

            /**
             * put(18927L, 1L);//K0 立项评审
             put(18928L, 2L);//M1 项目规划
             put(18943L, 3L);//M2 A样方案冻结
             put(18929L, 4L);//M2 转阶段
             put(18944L, 5L);//M3 B样方案冻结
             put(18930L, 6L);//M3 转阶段
             put(18931L, 7L);//M4 C样方案冻结
             put(18932L, 8L);//M5 PPAP
             put(18933L, 9L);//M6 SOP
             put(18994L, 10L);//结项
             * @type {*[]}
             */

            /* let stage = ['','K0 立项评审','M1 项目规划','M2 A样方案冻结','M2 转阶段','M3 B样方案冻结'
              ,'M3 转阶段','M4 C样方案冻结','M5 PPAP','M6 SOP','结项']


            $params.stage = stage[{issueId:this.issueId,stage:this.projectdetail.state}.mstatus] */

            addMeeting($params).then((res) => {
              if (res.success) {
                if (res.data) {
                  this.$message.success('新增成功')

                  this.form.resetFields()
                  this.fileList = []
                  this.summaryList = []
                  this.soundList = []
                  this.saveData = {}
                  this.defaultFileList = []

                  this.callList()
                } else {
                  this.$message.error('新增失败')
                }
              } else {
                this.$message.error('新增失败：' + res.message)
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },

      uploadFiles(info,record) {

        if (info.file.status === 'done') {

          let ids =[]
          let names =[]
          let types =[]
          if(record.fileIds != null){
            ids = JSON.parse(record.fileIds)
            names = JSON.parse(record.fileNames)
            types = JSON.parse(record.fileTypes)
          }

          let file =  info.file
          if(file.response.success){
            ids.push(file.response.data.id)
            names.push(file.name)
            types.push(file.type)
          }

          let update = {}
          update.id = record.id
          update.fileIds= JSON.stringify(ids),
          update.fileNames= JSON.stringify(names),
          update.fileTypes= JSON.stringify(types)

          updateMeetingById(update).then(res => {
            if(res.success){
              this.callList()
            }else {
              this.$message.error('上传文件失败：' + res.message)
            }
          })

        } else if (info.file.status === 'error') {
          this.$message.error(`${info.file.name} 文件上传失败`);
        }
      },
      uploadFile(info,record,column) {



        if (info.file.status === 'done') {

          let file = info.file

          let update = {}
          update.id = record.id

          if('summaryId' == column){
            update.summaryId = file.response.data.id
            update.summaryName= file.name
            update.summaryType= file.type
          }else if('soundId' == column){
            update.soundId = file.response.data.id
            update.soundName= file.name
            update.soundType= file.type
          }



          updateMeetingById(update).then(res => {
            if(res.success){
              this.callList()
            }else {
              this.$message.error('上传文件失败：' + res.message)
            }
          })

        } else if (info.file.status === 'error') {
          this.$message.error(`${info.file.name} 文件上传失败`);
        }
      },
      handleChangeFile(info) {
        if (info.file.status === 'done') {

          this.fileList = info.fileList
          let list = info.fileList
          let ids =[]
          let names =[]
          let types =[]
          for (let i = 0; i < list.length; i++) {
            let file = list[i]
            if(file.response.success){
              ids.push(file.response.data.id)
              names.push(file.name)
              types.push(file.type)
            }
          }

          this.saveData.fileIds= JSON.stringify(ids),
          this.saveData.fileNames= JSON.stringify(names),
          this.saveData.fileTypes= JSON.stringify(types)



        } else if (info.file.status === 'error') {
          this.$message.error(`${info.file.name} 文件上传失败`);
        }
      },
      handleChange(info) {
        this.fileList = info.fileList
        if (info.file.status === 'done') {


          let list = info.fileList
          let ids =[]
          let names =[]
          let types =[]
          for (let i = 0; i < list.length; i++) {
            let file = list[i]
            if(file.response.success){
              ids.push(file.response.data.id)
              names.push(file.name)
              types.push(file.type)
            }
          }

          this.saveData.fileIds= JSON.stringify(ids),
          this.saveData.fileNames= JSON.stringify(names),
          this.saveData.fileTypes= JSON.stringify(types)



        } else if (info.file.status === 'error') {
          this.$message.error(`${info.file.name} 文件上传失败`);
        }
      },

      handleChange1(info) {

        if(info.fileList.length == 0){
          this.form.resetFields(['file2'])
        }
        if (info.fileList.length > 1){

          info.fileList.splice(0, 1)
        }

        this.summaryList = info.fileList
        if (info.file.status === 'done') {




          let file = info.file

          if(file.response.success){


            this.saveData.summaryId= file.response.data.id,
              this.saveData.summaryName= file.name,
              this.saveData.summaryType=file.type

          }


        } else if (info.file.status === 'error') {
          this.$message.error(`${info.file.name} 文件上传失败`);
        }
      },
      handleChange2(info) {

        if(info.fileList.length == 0){
          this.form.resetFields(['file3'])
        }

        if (info.fileList.length > 1){

          info.fileList.splice(0, 1)
        }
        this.soundList = info.fileList

        let file = info.file

        if (info.file.status === 'done') {


          if(file.response.success){

            this.saveData.soundId= file.response.data.id,
            this.saveData.soundName= file.name,
            this.saveData.soundType= file.type

          }


        } else if (info.file.status === 'error') {
          this.$message.error(`${info.file.name} 文件上传失败`);
        }
      },
    },



    mounted() {
      this.callList()
    }
  }
</script>

<style lang="less" scoped=''>
/deep/.ant-table-small > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th{
  background: #f3f3f3; 
}
/deep/.ant-table-thead > tr > th{
  font-weight: bold;
  
}
/deep/.ant-table-small > .ant-table-content > .ant-table-body{
  margin:0;
}
/deep/.ant-table{
  margin: 0 2px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  /* */
}
  /* .breadcrumb{
    padding: 5px 0;
    padding-left: 13px;
  }.ant-breadcrumb a{
     color:#5d90fa !important;
   }.ant-breadcrumb{
      font-size: 12px !important;
    }

  hr {
    margin: 0;
  }

  .board {
    display: block;
    margin: auto;
    justify-content: center;

  }
  .head {
    background: #6c8adf;
    color: #fff;
    text-align: center;
    font-size: 24px;
    font-weight: bold;
  }
  .item {
    width: 98%;
    margin: 8px auto;
    border-radius: 8px;
    overflow: hidden;
    background: #fff;
    box-shadow: 2px 2px 3px #ccc;
  }

  /deep/.ant-form-item {

    margin-bottom: 0px;

  } */

</style>
