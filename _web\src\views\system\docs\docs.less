/deep/.ant-table-thead>tr>th {
    background: #f3f3f3 !important;
}

/deep/td,
/deep/th {
    padding: 2px !important;
    font-size: 12px !important;
}

/deep/ .ant-select-selection__rendered,
/deep/ .ant-select-selection--single {
    height: 100%;
    line-height: 100%;
    font-size: 12px;
    margin: 0;
    border: 0;
    background: transparent;
    text-align: center;
    display: flex;
    flex-direction: row;
    justify-content: center;
}

/deep/ .ant-select {
    width: 100%;
}

/deep/ .ant-select-dropdown-menu-item {
    text-align: center;
}

/deep/ .ant-select-dropdown-menu {
    text-align: center !important;
}

/deep/ .ant-select-selection-selected-value {
    color: black;
}

/deep/.ant-select-selection-selected-value {
    overflow: initial;
    white-space: initial;
}

/deep/.ant-select-arrow .ant-select-arrow-icon svg {
    display: none;
}

/deep/.ant-select-dropdown-menu {
    text-align: center !important;
}

/deep/.ant-select-dropdown-menu-item {
    font-size: 12px;
    padding: 4px 8px;
    text-align: center;
}
/deep/.ant-select-dropdown-menu-item {
    padding: 2px;
}

.docsitem{
    margin-bottom: 20px;
}

.title{
    margin: 0 8px;
    font-weight: bold;
    font-size: 12px !important;
}
.a-upload{
    font-size: 12px;
    display: inline-block;
    width: 25px;
    margin-right: 5px;
}
/deep/.ant-upload-list{
    display: none;
}
/deep/.ant-input{
    padding: 1px;
}
.circle{
    border-radius: 50%;
    display: inline-block;
    width: 10px;
    height: 10px;
}
.yellow{
    background: #fac858;
}
.green{
    background: #70ad47;
}
.red{
    background: #ad4747;
}
.btns {
    text-align: center;
    position: sticky;
    top: 0;
    z-index: 90;
    background: #fff;
    padding-bottom: 12px;
}
.btns a{
    display: inline-block;
    padding: 5px 8px;
    margin-left: 5px;
    color: #fff;
    background: #1890ff;
    float: right;
}
.count_tip{
    font-weight: 400;
    font-size: 18px;
    color: rgb(16, 16, 16);
    margin: auto 6px;
}

// 红色提醒
.redTips{
    color: #ad4747;
}