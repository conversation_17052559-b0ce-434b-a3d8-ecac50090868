<template>
  <a-modal title="文件管理" width="80%" :visible="fileVisible" closable @cancel="fileVisible = false" :footer="null">

    <div>
      <div style="display: flex">
        <a-breadcrumb class="breadcrumb" separator=">">

          <template v-for="position in filePosition">

            <a-breadcrumb-item v-if="position.name == '产品认证管理'"><a  @click="backIndex">产品认证管理</a></a-breadcrumb-item>
            <a-breadcrumb-item v-else >
              <a @click="backPosition(position)">{{position.fileName || position.type || position.phase || position.productName }}</a>
            </a-breadcrumb-item>

          </template>
        </a-breadcrumb>
        <a-input-search v-model="keyword" style="width: 200px; margin-left: auto;" @search="onSearch"></a-input-search>
        <a-tooltip :title="show?'列表':'大图标'" @click="changeShow">
          <a-icon type="appstore" style="font-size: 32px;margin-left: 10px;cursor: pointer"/>
        </a-tooltip>
      </div>
      <a-divider/>
      <template v-if="show">
        <div ref="folderContainer" v-if="first">
          <div class="folder-container">

            <div v-for="folder in folders" :key="folder.id" class="folder" @dblclick="openSecond(folder)" @contextmenu.prevent="openMenu($event,folder)">
              <template >
                <div class="folder-icon">
                  <svg t="1733186268538" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5379" id="mx_n_1733186268538" width="150" height="100">
                    <path d="M848.8576 199.1936H415.7568c0-26.5728-21.5424-48.128-48.128-48.128H175.1424c-26.5728 0-48.128 21.5424-48.128 48.128V343.5648c0 26.5984 21.5424 48.1408 48.128 48.1408h673.728c26.5728 0 48.128-21.5424 48.128-48.1408v-96.2432c-0.0128-26.5856-21.5552-48.128-48.1408-48.128z" fill="#CCA352" p-id="5380"></path>
                    <path d="M800.7424 247.3088H223.2576c-26.5728 0-48.128 21.5424-48.128 48.128v48.128c0 26.5984 21.5424 48.1408 48.128 48.1408h577.472c26.5728 0 48.128-21.5424 48.128-48.1408v-48.128c0-26.5728-21.5424-48.128-48.1152-48.128z" fill="#FFFFFF" p-id="5381"></path>
                    <path d="M848.8576 295.4368H175.1424c-26.5728 0-48.128 21.5424-48.128 48.128v481.2544c0 26.5472 21.5424 48.128 48.128 48.128h673.728c26.5728 0 48.128-21.568 48.128-48.128V343.552c-0.0128-26.5728-21.5552-48.1152-48.1408-48.1152z" fill="#FFCC66" p-id="5382"></path>
                  </svg>
                </div>
                <div class="folder-name">
                  {{ folder.productName }}
                </div>
              </template>

            </div>
          </div>
        </div>

        <div ref="secondFolderContainer" v-if="second">
          <div class="folder-container">

            <div v-for="file in secondFiles" :key="file.id" class="folder" @dblclick="openSecond(file)" @contextmenu.prevent="openMenu($event,file)">
              <template v-if="!file.fileId && !file.fileUrl">
                <div class="folder-icon">
                  <svg t="1733186268538" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5379" id="mx_n_1733186268538" width="150" height="100">
                    <path d="M848.8576 199.1936H415.7568c0-26.5728-21.5424-48.128-48.128-48.128H175.1424c-26.5728 0-48.128 21.5424-48.128 48.128V343.5648c0 26.5984 21.5424 48.1408 48.128 48.1408h673.728c26.5728 0 48.128-21.5424 48.128-48.1408v-96.2432c-0.0128-26.5856-21.5552-48.128-48.1408-48.128z" fill="#CCA352" p-id="5380"></path>
                    <path d="M800.7424 247.3088H223.2576c-26.5728 0-48.128 21.5424-48.128 48.128v48.128c0 26.5984 21.5424 48.1408 48.128 48.1408h577.472c26.5728 0 48.128-21.5424 48.128-48.1408v-48.128c0-26.5728-21.5424-48.128-48.1152-48.128z" fill="#FFFFFF" p-id="5381"></path>
                    <path d="M848.8576 295.4368H175.1424c-26.5728 0-48.128 21.5424-48.128 48.128v481.2544c0 26.5472 21.5424 48.128 48.128 48.128h673.728c26.5728 0 48.128-21.568 48.128-48.128V343.552c-0.0128-26.5728-21.5552-48.1152-48.1408-48.1152z" fill="#FFCC66" p-id="5382"></path>
                  </svg>
                </div>
                <div class="folder-name">
                  {{ file.type || file.phase}}
                </div>
              </template>
              <template v-if="file.fileId || file.fileUrl">
                <div class="folder-icon">
                  <svg t="1733187029740" v-if="file.fileName.indexOf('pdf') > -1 || file.fileName.indexOf('PDF') > -1" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15201" width="150" height="80"><path d="M870.4 1024h-716.8A51.2 51.2 0 0 1 102.4 972.8v-921.6A51.2 51.2 0 0 1 153.6 0h569.685333a42.666667 42.666667 0 0 1 30.037334 12.288l155.989333 155.989333a42.666667 42.666667 0 0 1 12.288 30.037334V972.8a51.2 51.2 0 0 1-51.2 51.2zM153.6 34.133333a17.066667 17.066667 0 0 0-17.066667 17.066667v921.6a17.066667 17.066667 0 0 0 17.066667 17.066667h716.8a17.066667 17.066667 0 0 0 17.066667-17.066667V198.314667a7.168 7.168 0 0 0-2.389334-5.802667l-155.989333-155.989333a7.168 7.168 0 0 0-5.802667-2.389334z" fill="#4D4D4D" p-id="15202"></path><path d="M904.533333 204.8h-170.666666a17.066667 17.066667 0 0 1-17.066667-17.066667v-170.666666h34.133333V170.666667h153.6z" fill="#4D4D4D" p-id="15203"></path><path d="M204.8 170.666667h443.733333v34.133333H204.8zM204.8 307.2h614.4v34.133333H204.8zM204.8 443.733333h614.4v34.133334H204.8zM204.8 580.266667h614.4v34.133333H204.8zM204.8 853.333333h614.4v34.133334H204.8zM204.8 716.8h614.4v34.133333H204.8z" fill="#B3B3B3" p-id="15204"></path><path d="M51.2 460.8m17.066667 0l887.466666 0q17.066667 0 17.066667 17.066667l0 273.066666q0 17.066667-17.066667 17.066667l-887.466666 0q-17.066667 0-17.066667-17.066667l0-273.066666q0-17.066667 17.066667-17.066667Z" fill="#F33958" p-id="15205"></path><path d="M955.733333 477.866667v273.066666H68.266667v-273.066666h887.466666m0-34.133334H68.266667a34.133333 34.133333 0 0 0-34.133334 34.133334v273.066666a34.133333 34.133333 0 0 0 34.133334 34.133334h887.466666a34.133333 34.133333 0 0 0 34.133334-34.133334v-273.066666a34.133333 34.133333 0 0 0-34.133334-34.133334z" fill="#C42E47" p-id="15206"></path><path d="M348.16 530.090667a55.978667 55.978667 0 1 1 0 111.616H307.2v57.002666h-24.917333v-168.618666zM307.2 618.837333h34.133333c22.528 0 35.84-11.605333 35.84-32.426666s-12.970667-34.133333-35.84-34.133334H307.2zM509.952 530.090667A74.410667 74.410667 0 0 1 589.141333 614.4a75.093333 75.093333 0 0 1-79.189333 84.992h-60.757333v-169.301333z m-34.133333 144.725333h31.744A52.906667 52.906667 0 0 0 562.858667 614.4a53.248 53.248 0 0 0-55.637334-60.074667h-31.744zM636.586667 698.709333v-168.618666h105.130666v23.893333h-78.848v51.882667h72.021334v22.869333h-72.021334v68.266667z" fill="#FFFFFF" p-id="15207"></path></svg>
                  <!--                    <a-icon type="file-pdf" v-if="file.fileName == 'pdf'"/>-->
                  <svg t="1733187630326" v-else-if="file.fileName == 'xlsx' || file.fileName == 'xls'"  class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="18213" width="150" height="80"><path d="M682.666667 42.666667H298.666667c-25.6 0-42.666667 17.066667-42.666667 42.666666v213.333334l426.666667 213.333333 170.666666 64 170.666667-64V298.666667l-341.333333-256z" fill="#21A366" p-id="18214"></path><path d="M256 298.666667h426.666667v213.333333H256z" fill="#107C41" p-id="18215"></path><path d="M1024 85.333333v213.333334h-341.333333V42.666667h298.666666c21.333333 0 42.666667 21.333333 42.666667 42.666666z" fill="#33C481" p-id="18216"></path><path d="M682.666667 512H256v426.666667c0 25.6 17.066667 42.666667 42.666667 42.666666h682.666666c25.6 0 42.666667-17.066667 42.666667-42.666666v-213.333334l-341.333333-213.333333z" fill="#185C37" p-id="18217"></path><path d="M588.8 256H256v597.333333h324.266667c29.866667 0 59.733333-29.866667 59.733333-59.733333V307.2c0-29.866667-21.333333-51.2-51.2-51.2z" opacity=".5" p-id="18218"></path><path d="M546.133333 810.666667H51.2C21.333333 810.666667 0 789.333333 0 759.466667V264.533333C0 234.666667 21.333333 213.333333 51.2 213.333333h499.2c25.6 0 46.933333 21.333333 46.933333 51.2v499.2c0 25.6-21.333333 46.933333-51.2 46.933334z" fill="#107C41" p-id="18219"></path><path d="M145.066667 682.666667L256 512 153.6 341.333333h81.066667l55.466666 106.666667c8.533333 12.8 8.533333 21.333333 12.8 25.6l12.8-25.6L375.466667 341.333333h76.8l-102.4 170.666667 106.666666 170.666667h-85.333333l-64-119.466667c0-4.266667-4.266667-8.533333-8.533333-17.066667 0 4.266667-4.266667 8.533333-8.533334 17.066667L226.133333 682.666667H145.066667z" fill="#FFFFFF" p-id="18220"></path><path d="M682.666667 512h341.333333v213.333333h-341.333333z" fill="#107C41" p-id="18221"></path></svg>
                  <!--                    <a-icon type="file-excel" v-else-if="file.fileName == 'xlxs'"/>-->
                  <svg t="1733187718798" v-else-if="file.fileName == 'pptx'" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19328" width="120" height="80"><path d="M538.731891 0h65.98683v107.168391c124.387582 0.722484 248.895579-1.324553 373.28316 0a40.699906 40.699906 0 0 1 45.034808 46.118533c2.047037 222.404516 0 444.929445 1.204139 667.454374-1.204139 24.082785 2.287865 50.694262-11.198495 72.248354-16.978363 12.041392-39.014111 10.957667-59.002822 12.041392-116.319849-0.60207-232.639699 0-349.200376 0V1023.518344h-72.248354C355.100659 990.886171 177.490122 960.662277 0 928.752587V95.488241C179.537159 63.698965 359.074318 31.30762 538.731891 0z" fill="#D24625" p-id="19329"></path><path d="M604.718721 142.931326H988.598307v726.216369H604.718721v-95.247413h279.239887v-47.563499H604.718721v-60.206962h279.239887v-46.96143H604.839135v-69.960489c46.118532 14.570085 98.619003 14.208843 139.800564-14.088429 44.553151-27.093133 67.793039-78.630292 71.646284-130.047036H663.119473c0-51.777987 0.60207-103.555974-0.963311-155.213547-19.145814 3.732832-38.171214 7.826905-57.196614 12.041392z" fill="#FFFFFF" p-id="19330"></path><path d="M686.35936 224.69238a165.689558 165.689558 0 0 1 153.16651 156.5381c-51.055503 0.60207-102.111007 0-153.286924 0 0.120414-52.380056 0.120414-104.278457 0.120414-156.5381z" fill="#D24625" p-id="19331"></path><path d="M186.64158 314.521167c63.21731 3.130762 139.680151-25.527752 192.662277 22.878645 50.092192 62.374412 36.84666 176.888053-37.44873 214.095955-26.370649 13.847601-56.714958 12.041392-85.373471 10.957667v139.68015l-69.238006-5.900282c-1.806209-127.157103-2.047037-254.434619-0.60207-381.712135z" fill="#FFFFFF" p-id="19332"></path><path d="M255.759172 378.942615c22.878645-0.963311 51.296331-5.298213 66.709313 16.737536a87.902164 87.902164 0 0 1 1.565381 78.148635c-13.245532 24.082785-43.228598 22.035748-66.468485 24.925682-2.408278-39.857008-2.167451-79.714017-1.806209-119.811853z" fill="#D24625" p-id="19333"></path></svg>
                  <svg t="1733187804936" class="icon" v-else-if="file.fileName == 'doc' || file.fileName == 'docx'" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="20412" width="150" height="80"><path d="M950.272 843.776H527.36c-16.384 0-29.696-13.312-29.696-29.696V210.944c0-16.384 13.312-29.696 29.696-29.696h422.912c16.384 0 29.696 13.312 29.696 29.696v603.136c0 16.384-13.312 29.696-29.696 29.696z" fill="#E8E8E8" p-id="20413"></path><path d="M829.44 361.472H527.36c-16.384 0-29.696-13.312-29.696-29.696s13.312-29.696 29.696-29.696H829.44c16.384 0 29.696 13.312 29.696 29.696 0 15.36-13.312 29.696-29.696 29.696z m0 120.832H527.36c-16.384 0-29.696-13.312-29.696-29.696s13.312-29.696 29.696-29.696H829.44c16.384 0 29.696 13.312 29.696 29.696s-13.312 29.696-29.696 29.696z m0 119.808H527.36c-16.384 0-29.696-13.312-29.696-29.696s13.312-29.696 29.696-29.696H829.44c16.384 0 29.696 13.312 29.696 29.696s-13.312 29.696-29.696 29.696z m0 120.832H527.36c-16.384 0-29.696-13.312-29.696-29.696s13.312-29.696 29.696-29.696H829.44c16.384 0 29.696 13.312 29.696 29.696s-13.312 29.696-29.696 29.696z" fill="#B2B2B2" p-id="20414"></path><path d="M607.232 995.328l-563.2-107.52V135.168l563.2-107.52v967.68z" fill="#0D47A1" p-id="20415"></path><path d="M447.488 696.32h-71.68l-47.104-236.544c-3.072-13.312-4.096-27.648-4.096-40.96h-1.024c-1.024 16.384-3.072 30.72-5.12 40.96L269.312 696.32H194.56l-74.752-368.64h70.656l39.936 245.76c2.048 10.24 3.072 24.576 4.096 41.984h1.024c0-13.312 3.072-27.648 6.144-43.008l51.2-244.736h68.608l47.104 247.808c2.048 9.216 3.072 22.528 4.096 39.936h1.024c1.024-13.312 2.048-26.624 4.096-40.96l39.936-245.76H522.24L447.488 696.32z" fill="#FFFFFF" p-id="20416"></path></svg>
                  <svg t="1733188117407" class="icon"  v-else-if="file.fileName == 'jpg' || file.fileName == 'JPG' || file.fileName == 'jepg' || file.fileName == 'png'|| file.fileName == 'PNG'" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="39515" width="150" height="80"><path d="M917.09952 84.6848H114.41152c-32.83968 0-59.45856 26.624-59.45856 59.45856v772.96128c0 32.83456 26.624 59.45344 59.45856 59.45344h802.688c32.83456 0 59.45344-26.61888 59.45344-59.45344V144.13824c0-32.82944-26.61888-59.45344-59.45344-59.45344z" fill="" p-id="39516"></path><path d="M917.09952 54.95296H114.41152c-32.83968 0-59.45856 26.624-59.45856 59.45856V887.35232c0 32.8448 26.624 59.46368 59.45856 59.46368h802.688c32.83456 0 59.45344-26.61888 59.45344-59.46368V114.41152c0-32.83456-26.61888-59.45856-59.45344-59.45856z" fill="#ECEAE0" p-id="39517"></path><path d="M872.50432 114.41152H159.00672a44.5952 44.5952 0 0 0-44.5952 44.5952V590.07488h802.688V159.00672a44.5952 44.5952 0 0 0-44.5952-44.5952z" fill="#98DCF0" p-id="39518"></path><path d="M613.63712 411.55584l-154.94144 178.51904h309.86752z" fill="#699B54" p-id="39519"></path><path d="M586.82368 590.07488l-206.53568-237.9776-206.5408 237.9776H114.41152V694.12352a44.5952 44.5952 0 0 0 44.5952 44.5952h713.4976a44.5952 44.5952 0 0 0 44.5952-44.5952v-104.05376h-330.27584z" fill="#80BB67" p-id="39520"></path><path d="M768.44544 263.05536m-59.45856 0a59.45856 59.45856 0 1 0 118.91712 0 59.45856 59.45856 0 1 0-118.91712 0Z" fill="#FFE68E" p-id="39521"></path></svg>
                  <svg t="1733188265050" class="icon" v-else viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="41415" width="150" height="80"><path d="M847.488 1008.896H61.504V117.056h491.968l294.016 229.952z" fill="#F0A221" p-id="41416"></path><path d="M956.48 901.888H170.496V10.048h556.032l230.016 226.176v665.664zM729.984 102.016v144h144l-144-144z m-104.96 81.984H286.016v57.984h339.008v-57.984z m216.96 164.992H284.992v57.984h556.992v-57.984z m0 169.984H284.992v57.984h556.992v-57.984z m0 165.056H284.992v57.984h556.992v-57.984z" fill="#F1C84C" p-id="41417"></path></svg>
                  <!--                    <a-icon type="file-ppt" v-else-if="file.fileSuffix == 'pptx'"/>-->
                  <!--                    <a-icon type="file-word" v-else-if="file.fileSuffix == 'doc'"/>-->

                </div>
                <div class="folder-name">
                  {{ file.fileName }}
                </div>
              </template>

            </div>
          </div>
        </div>
      </template>
      <template v-if="!show">
        <div ref="folderContainer" v-if="first">
          <div class="folder-container" style="display: block">

            <div v-for="folder in folders" style="width: 50%;display: inline-flex;justify-content:left;flex-direction: unset;" :key="folder.id" class="folder" @dblclick="openSecond(folder)" @contextmenu.prevent="openMenu($event,folder)">
              <template >
                <div class="folder-icon-list">
                  <svg t="1733186268538" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5379" id="mx_n_1733186268538" width="20" height="20">
                    <path d="M848.8576 199.1936H415.7568c0-26.5728-21.5424-48.128-48.128-48.128H175.1424c-26.5728 0-48.128 21.5424-48.128 48.128V343.5648c0 26.5984 21.5424 48.1408 48.128 48.1408h673.728c26.5728 0 48.128-21.5424 48.128-48.1408v-96.2432c-0.0128-26.5856-21.5552-48.128-48.1408-48.128z" fill="#CCA352" p-id="5380"></path>
                    <path d="M800.7424 247.3088H223.2576c-26.5728 0-48.128 21.5424-48.128 48.128v48.128c0 26.5984 21.5424 48.1408 48.128 48.1408h577.472c26.5728 0 48.128-21.5424 48.128-48.1408v-48.128c0-26.5728-21.5424-48.128-48.1152-48.128z" fill="#FFFFFF" p-id="5381"></path>
                    <path d="M848.8576 295.4368H175.1424c-26.5728 0-48.128 21.5424-48.128 48.128v481.2544c0 26.5472 21.5424 48.128 48.128 48.128h673.728c26.5728 0 48.128-21.568 48.128-48.128V343.552c-0.0128-26.5728-21.5552-48.1152-48.1408-48.1152z" fill="#FFCC66" p-id="5382"></path>
                  </svg>
                </div>
                <div class="folder-name-list">
                  {{ folder.productName }}
                </div>
              </template>

            </div>
          </div>
        </div>

        <div ref="secondFolderContainer" v-if="second">
          <div class="folder-container" style="display: block">

            <div v-for="file in secondFiles" :key="file.id" class="folder" style="width: 50%;display: inline-flex;justify-content:left;flex-direction: unset;" @dblclick="openSecond(file)" @contextmenu.prevent="openMenu($event,file)">
              <template v-if="!file.fileId && !file.fileUrl">
                <div class="folder-icon-list">
                  <svg t="1733186268538" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5379" id="mx_n_1733186268538" width="20" height="20">
                    <path d="M848.8576 199.1936H415.7568c0-26.5728-21.5424-48.128-48.128-48.128H175.1424c-26.5728 0-48.128 21.5424-48.128 48.128V343.5648c0 26.5984 21.5424 48.1408 48.128 48.1408h673.728c26.5728 0 48.128-21.5424 48.128-48.1408v-96.2432c-0.0128-26.5856-21.5552-48.128-48.1408-48.128z" fill="#CCA352" p-id="5380"></path>
                    <path d="M800.7424 247.3088H223.2576c-26.5728 0-48.128 21.5424-48.128 48.128v48.128c0 26.5984 21.5424 48.1408 48.128 48.1408h577.472c26.5728 0 48.128-21.5424 48.128-48.1408v-48.128c0-26.5728-21.5424-48.128-48.1152-48.128z" fill="#FFFFFF" p-id="5381"></path>
                    <path d="M848.8576 295.4368H175.1424c-26.5728 0-48.128 21.5424-48.128 48.128v481.2544c0 26.5472 21.5424 48.128 48.128 48.128h673.728c26.5728 0 48.128-21.568 48.128-48.128V343.552c-0.0128-26.5728-21.5552-48.1152-48.1408-48.1152z" fill="#FFCC66" p-id="5382"></path>
                  </svg>
                </div>
                <div class="folder-name-list">
                  {{ file.type || file.phase}}
                </div>
              </template>
              <template v-if="file.fileId || file.fileUrl">
                <div class="folder-icon-list">
                  <svg t="1733187029740" v-if="file.fileName.indexOf('pdf') > -1 || file.fileName.indexOf('PDF') > -1" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15201" width="20" height="20"><path d="M870.4 1024h-716.8A51.2 51.2 0 0 1 102.4 972.8v-921.6A51.2 51.2 0 0 1 153.6 0h569.685333a42.666667 42.666667 0 0 1 30.037334 12.288l155.989333 155.989333a42.666667 42.666667 0 0 1 12.288 30.037334V972.8a51.2 51.2 0 0 1-51.2 51.2zM153.6 34.133333a17.066667 17.066667 0 0 0-17.066667 17.066667v921.6a17.066667 17.066667 0 0 0 17.066667 17.066667h716.8a17.066667 17.066667 0 0 0 17.066667-17.066667V198.314667a7.168 7.168 0 0 0-2.389334-5.802667l-155.989333-155.989333a7.168 7.168 0 0 0-5.802667-2.389334z" fill="#4D4D4D" p-id="15202"></path><path d="M904.533333 204.8h-170.666666a17.066667 17.066667 0 0 1-17.066667-17.066667v-170.666666h34.133333V170.666667h153.6z" fill="#4D4D4D" p-id="15203"></path><path d="M204.8 170.666667h443.733333v34.133333H204.8zM204.8 307.2h614.4v34.133333H204.8zM204.8 443.733333h614.4v34.133334H204.8zM204.8 580.266667h614.4v34.133333H204.8zM204.8 853.333333h614.4v34.133334H204.8zM204.8 716.8h614.4v34.133333H204.8z" fill="#B3B3B3" p-id="15204"></path><path d="M51.2 460.8m17.066667 0l887.466666 0q17.066667 0 17.066667 17.066667l0 273.066666q0 17.066667-17.066667 17.066667l-887.466666 0q-17.066667 0-17.066667-17.066667l0-273.066666q0-17.066667 17.066667-17.066667Z" fill="#F33958" p-id="15205"></path><path d="M955.733333 477.866667v273.066666H68.266667v-273.066666h887.466666m0-34.133334H68.266667a34.133333 34.133333 0 0 0-34.133334 34.133334v273.066666a34.133333 34.133333 0 0 0 34.133334 34.133334h887.466666a34.133333 34.133333 0 0 0 34.133334-34.133334v-273.066666a34.133333 34.133333 0 0 0-34.133334-34.133334z" fill="#C42E47" p-id="15206"></path><path d="M348.16 530.090667a55.978667 55.978667 0 1 1 0 111.616H307.2v57.002666h-24.917333v-168.618666zM307.2 618.837333h34.133333c22.528 0 35.84-11.605333 35.84-32.426666s-12.970667-34.133333-35.84-34.133334H307.2zM509.952 530.090667A74.410667 74.410667 0 0 1 589.141333 614.4a75.093333 75.093333 0 0 1-79.189333 84.992h-60.757333v-169.301333z m-34.133333 144.725333h31.744A52.906667 52.906667 0 0 0 562.858667 614.4a53.248 53.248 0 0 0-55.637334-60.074667h-31.744zM636.586667 698.709333v-168.618666h105.130666v23.893333h-78.848v51.882667h72.021334v22.869333h-72.021334v68.266667z" fill="#FFFFFF" p-id="15207"></path></svg>
                  <!--                    <a-icon type="file-pdf" v-if="file.fileName == 'pdf'"/>-->
                  <svg t="1733187630326" v-else-if="file.fileName == 'xlsx' || file.fileName == 'xls'"  class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="18213" width="20" height="20"><path d="M682.666667 42.666667H298.666667c-25.6 0-42.666667 17.066667-42.666667 42.666666v213.333334l426.666667 213.333333 170.666666 64 170.666667-64V298.666667l-341.333333-256z" fill="#21A366" p-id="18214"></path><path d="M256 298.666667h426.666667v213.333333H256z" fill="#107C41" p-id="18215"></path><path d="M1024 85.333333v213.333334h-341.333333V42.666667h298.666666c21.333333 0 42.666667 21.333333 42.666667 42.666666z" fill="#33C481" p-id="18216"></path><path d="M682.666667 512H256v426.666667c0 25.6 17.066667 42.666667 42.666667 42.666666h682.666666c25.6 0 42.666667-17.066667 42.666667-42.666666v-213.333334l-341.333333-213.333333z" fill="#185C37" p-id="18217"></path><path d="M588.8 256H256v597.333333h324.266667c29.866667 0 59.733333-29.866667 59.733333-59.733333V307.2c0-29.866667-21.333333-51.2-51.2-51.2z" opacity=".5" p-id="18218"></path><path d="M546.133333 810.666667H51.2C21.333333 810.666667 0 789.333333 0 759.466667V264.533333C0 234.666667 21.333333 213.333333 51.2 213.333333h499.2c25.6 0 46.933333 21.333333 46.933333 51.2v499.2c0 25.6-21.333333 46.933333-51.2 46.933334z" fill="#107C41" p-id="18219"></path><path d="M145.066667 682.666667L256 512 153.6 341.333333h81.066667l55.466666 106.666667c8.533333 12.8 8.533333 21.333333 12.8 25.6l12.8-25.6L375.466667 341.333333h76.8l-102.4 170.666667 106.666666 170.666667h-85.333333l-64-119.466667c0-4.266667-4.266667-8.533333-8.533333-17.066667 0 4.266667-4.266667 8.533333-8.533334 17.066667L226.133333 682.666667H145.066667z" fill="#FFFFFF" p-id="18220"></path><path d="M682.666667 512h341.333333v213.333333h-341.333333z" fill="#107C41" p-id="18221"></path></svg>
                  <!--                    <a-icon type="file-excel" v-else-if="file.fileName == 'xlxs'"/>-->
                  <svg t="1733187718798" v-else-if="file.fileName == 'pptx'" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19328" width="120" height="80"><path d="M538.731891 0h65.98683v107.168391c124.387582 0.722484 248.895579-1.324553 373.28316 0a40.699906 40.699906 0 0 1 45.034808 46.118533c2.047037 222.404516 0 444.929445 1.204139 667.454374-1.204139 24.082785 2.287865 50.694262-11.198495 72.248354-16.978363 12.041392-39.014111 10.957667-59.002822 12.041392-116.319849-0.60207-232.639699 0-349.200376 0V1023.518344h-72.248354C355.100659 990.886171 177.490122 960.662277 0 928.752587V95.488241C179.537159 63.698965 359.074318 31.30762 538.731891 0z" fill="#D24625" p-id="19329"></path><path d="M604.718721 142.931326H988.598307v726.216369H604.718721v-95.247413h279.239887v-47.563499H604.718721v-60.206962h279.239887v-46.96143H604.839135v-69.960489c46.118532 14.570085 98.619003 14.208843 139.800564-14.088429 44.553151-27.093133 67.793039-78.630292 71.646284-130.047036H663.119473c0-51.777987 0.60207-103.555974-0.963311-155.213547-19.145814 3.732832-38.171214 7.826905-57.196614 12.041392z" fill="#FFFFFF" p-id="19330"></path><path d="M686.35936 224.69238a165.689558 165.689558 0 0 1 153.16651 156.5381c-51.055503 0.60207-102.111007 0-153.286924 0 0.120414-52.380056 0.120414-104.278457 0.120414-156.5381z" fill="#D24625" p-id="19331"></path><path d="M186.64158 314.521167c63.21731 3.130762 139.680151-25.527752 192.662277 22.878645 50.092192 62.374412 36.84666 176.888053-37.44873 214.095955-26.370649 13.847601-56.714958 12.041392-85.373471 10.957667v139.68015l-69.238006-5.900282c-1.806209-127.157103-2.047037-254.434619-0.60207-381.712135z" fill="#FFFFFF" p-id="19332"></path><path d="M255.759172 378.942615c22.878645-0.963311 51.296331-5.298213 66.709313 16.737536a87.902164 87.902164 0 0 1 1.565381 78.148635c-13.245532 24.082785-43.228598 22.035748-66.468485 24.925682-2.408278-39.857008-2.167451-79.714017-1.806209-119.811853z" fill="#D24625" p-id="19333"></path></svg>
                  <svg t="1733187804936" class="icon" v-else-if="file.fileName == 'doc' || file.fileName == 'docx'" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="20412" width="20" height="20"><path d="M950.272 843.776H527.36c-16.384 0-29.696-13.312-29.696-29.696V210.944c0-16.384 13.312-29.696 29.696-29.696h422.912c16.384 0 29.696 13.312 29.696 29.696v603.136c0 16.384-13.312 29.696-29.696 29.696z" fill="#E8E8E8" p-id="20413"></path><path d="M829.44 361.472H527.36c-16.384 0-29.696-13.312-29.696-29.696s13.312-29.696 29.696-29.696H829.44c16.384 0 29.696 13.312 29.696 29.696 0 15.36-13.312 29.696-29.696 29.696z m0 120.832H527.36c-16.384 0-29.696-13.312-29.696-29.696s13.312-29.696 29.696-29.696H829.44c16.384 0 29.696 13.312 29.696 29.696s-13.312 29.696-29.696 29.696z m0 119.808H527.36c-16.384 0-29.696-13.312-29.696-29.696s13.312-29.696 29.696-29.696H829.44c16.384 0 29.696 13.312 29.696 29.696s-13.312 29.696-29.696 29.696z m0 120.832H527.36c-16.384 0-29.696-13.312-29.696-29.696s13.312-29.696 29.696-29.696H829.44c16.384 0 29.696 13.312 29.696 29.696s-13.312 29.696-29.696 29.696z" fill="#B2B2B2" p-id="20414"></path><path d="M607.232 995.328l-563.2-107.52V135.168l563.2-107.52v967.68z" fill="#0D47A1" p-id="20415"></path><path d="M447.488 696.32h-71.68l-47.104-236.544c-3.072-13.312-4.096-27.648-4.096-40.96h-1.024c-1.024 16.384-3.072 30.72-5.12 40.96L269.312 696.32H194.56l-74.752-368.64h70.656l39.936 245.76c2.048 10.24 3.072 24.576 4.096 41.984h1.024c0-13.312 3.072-27.648 6.144-43.008l51.2-244.736h68.608l47.104 247.808c2.048 9.216 3.072 22.528 4.096 39.936h1.024c1.024-13.312 2.048-26.624 4.096-40.96l39.936-245.76H522.24L447.488 696.32z" fill="#FFFFFF" p-id="20416"></path></svg>
                  <svg t="1733188117407" class="icon"  v-else-if="file.fileName == 'jpg' || file.fileName == 'JPG' || file.fileName == 'jepg' || file.fileName == 'png'|| file.fileName == 'PNG'" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="39515" width="20" height="20"><path d="M917.09952 84.6848H114.41152c-32.83968 0-59.45856 26.624-59.45856 59.45856v772.96128c0 32.83456 26.624 59.45344 59.45856 59.45344h802.688c32.83456 0 59.45344-26.61888 59.45344-59.45344V144.13824c0-32.82944-26.61888-59.45344-59.45344-59.45344z" fill="" p-id="39516"></path><path d="M917.09952 54.95296H114.41152c-32.83968 0-59.45856 26.624-59.45856 59.45856V887.35232c0 32.8448 26.624 59.46368 59.45856 59.46368h802.688c32.83456 0 59.45344-26.61888 59.45344-59.46368V114.41152c0-32.83456-26.61888-59.45856-59.45344-59.45856z" fill="#ECEAE0" p-id="39517"></path><path d="M872.50432 114.41152H159.00672a44.5952 44.5952 0 0 0-44.5952 44.5952V590.07488h802.688V159.00672a44.5952 44.5952 0 0 0-44.5952-44.5952z" fill="#98DCF0" p-id="39518"></path><path d="M613.63712 411.55584l-154.94144 178.51904h309.86752z" fill="#699B54" p-id="39519"></path><path d="M586.82368 590.07488l-206.53568-237.9776-206.5408 237.9776H114.41152V694.12352a44.5952 44.5952 0 0 0 44.5952 44.5952h713.4976a44.5952 44.5952 0 0 0 44.5952-44.5952v-104.05376h-330.27584z" fill="#80BB67" p-id="39520"></path><path d="M768.44544 263.05536m-59.45856 0a59.45856 59.45856 0 1 0 118.91712 0 59.45856 59.45856 0 1 0-118.91712 0Z" fill="#FFE68E" p-id="39521"></path></svg>
                  <svg t="1733188265050" class="icon" v-else viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="41415" width="20" height="20"><path d="M847.488 1008.896H61.504V117.056h491.968l294.016 229.952z" fill="#F0A221" p-id="41416"></path><path d="M956.48 901.888H170.496V10.048h556.032l230.016 226.176v665.664zM729.984 102.016v144h144l-144-144z m-104.96 81.984H286.016v57.984h339.008v-57.984z m216.96 164.992H284.992v57.984h556.992v-57.984z m0 169.984H284.992v57.984h556.992v-57.984z m0 165.056H284.992v57.984h556.992v-57.984z" fill="#F1C84C" p-id="41417"></path></svg>
                  <!--                    <a-icon type="file-ppt" v-else-if="file.fileSuffix == 'pptx'"/>-->
                  <!--                    <a-icon type="file-word" v-else-if="file.fileSuffix == 'doc'"/>-->

                </div>
                <div class="folder-name-list">
                  {{ file.fileName }}
                </div>
              </template>

            </div>
          </div>
        </div>
      </template>

      <ul v-show="visible" :style="{left:left+'px',top:top+'px'}" class="contextmenu">
        <li @click="openSecond(currentFile)">打开</li>
        <li v-if="currentFile && (currentFile.fileId || currentFile.fileUrl)" @click="downloadFile(currentFile)">下载</li>
      </ul>


      <pbi-preview-new ref="pbiPreviewNew" width="80%" :show-file-name="true"/>

    </div>

  </a-modal>

</template>

<script>

import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import PbiPreviewNew from "@/components/pageTool/components/pbiPreviewNew";
import Sortable from 'sortablejs';
import _ from 'lodash';
export default {
  components: {
    PbiPreviewNew
  },
  data() {
    return {
      show:true,
      filePosition:[{name:"产品认证管理"}],
      fileVisible: false,
      folders: [],
      second:false,
      secondFiles:[],
      first:true,
      visible: false,
      top: 0,
      left: 0,
      currentFile:null,
      keyword:null,
      sourceFolders:[],
      sourceSecondFiles:[],
    }
  },
  watch: {
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  },
  methods: {
    changeShow(){
      this.show = !this.show
      this.$nextTick(() => {
        let container = this.$refs.folderContainer
        this.changePosition(container)
      })
    },
    onSearch() {
      if (this.first) {
        if (this.sourceFolders.length === 0) {
          // 使用 Lodash 的 cloneDeep 方法进行深拷贝
          this.sourceFolders = _.cloneDeep(this.folders);
        }

        // 清空条件
        if (this.keyword == null || this.keyword === '') {
          this.folders = _.cloneDeep(this.sourceFolders);
        } else {
          this.folders = this.sourceFolders.filter(f => f.productName.indexOf(this.keyword) > -1);
        }
      }else{
        if (this.sourceSecondFiles.length === 0) {
          // 使用 Lodash 的 cloneDeep 方法进行深拷贝
          this.sourceSecondFiles = _.cloneDeep(this.secondFiles);
        }

        // 清空条件
        if (this.keyword == null || this.keyword === '') {
          this.secondFiles = _.cloneDeep(this.sourceSecondFiles);
        } else {
          this.secondFiles = this.sourceSecondFiles.filter(f => (f.fileName && f.fileName.indexOf(this.keyword) > -1) ||
              (f.phase && f.phase.indexOf(this.keyword) > -1) || (f.type && f.type.indexOf(this.keyword) > -1));
        }
      }
    },
    changePosition(dom) {

      new Sortable.create(dom.querySelector('.folder-container'), {
        handle: '.folder', // 行元素
        animation: 150,
        onEnd: ({newIndex, oldIndex}) => {
          // 拖拽后回调
          this.folders.splice(newIndex, 0, this.folders.splice(oldIndex, 1)[0]);
          // this.orderData.splice(newIndex, 0, currRow)
        }
      })
    },
    open(folders) {
      this.fileVisible = true
      this.folders = folders
      this.sourceFolders = _.cloneDeep(this.folders);
      this.$nextTick(() => {
        let container = this.$refs.folderContainer
        this.changePosition(container)
      })
    },
    backIndex(){
      this.keyword = null
      this.folders = _.cloneDeep(this.sourceFolders);
      this.filePosition=[{name:"产品认证管理"}]
      this.first = true
      this.second = false
    },
    async openSecond(folder,flag){

      this.sourceSecondFiles = _.cloneDeep(this.secondFiles);

      this.keyword = null

      if (folder.fileId) {
        this.$refs.pbiPreviewNew.init(folder.fileId)
        return
      } else if (folder.fileUrl) {
        this.$refs.pbiPreviewNew.init(null,folder.fileName,folder.fileUrl)
        return
      }
      if(!flag){
        this.filePosition.push(folder)
      }
      this.first = false
      this.second = true
      this.secondFiles = folder.list
    },
    downloadFile(currentFile){
      this.$refs.pbiPreviewNew.downloadFile(currentFile.fileId,currentFile.fileName,currentFile.fileUrl)
    },
    //start
    openMenu(e,file) {
      this.currentFile = file
      var x = e.pageX;
      var y = e.pageY;
      this.top = y;
      this.left = x;
      this.visible = true;//在这里控制右键菜单的打开
    },
    //close
    closeMenu() {
      this.visible = false;
    },
    async backPosition(position){
      // 找到目标对象的索引
      const targetIndex = this.filePosition.findIndex(item => _.isEqual(item, position));
      // 如果找到目标对象，截取从开始到目标对象的部分
      if (targetIndex !== -1) {
        this.filePosition = this.filePosition.slice(0, targetIndex+1);
      } else {
      }
      this.openSecond(position,true)
    }


  },
  created() {

  },
  mounted() {

  }
}
</script>


<style lang="less" scoped="">
//@import './topic.less';
@import '/src/components/pageTool/style/pbiSearchItem.less';

:root {
  --scroll-display: none;
  --scroll-border-bottom: none;
  --scroll-border-bottom-fixed: none;
}

/deep/ .ag-body-horizontal-scroll {
  border-bottom: var(--scroll-border-bottom) !important;
}

/deep/ .ag-body-horizontal-scroll-viewport {
  display: var(--scroll-display) !important;
  border-bottom: var(--scroll-border-bottom) !important;
}

/deep/ .ag-horizontal-left-spacer,
/deep/ .ag-horizontal-right-spacer {
  border-bottom: var(--scroll-border-bottom-fixed) !important;
}

/deep/ .ant-breadcrumb {
  font-size: 14px!important;
  margin-bottom: 10px;
}

/deep/ .left .ant-input,
/deep/ .left .ant-btn {
  height: 28px;
  font-size: 12px;
  border-radius: 4px;
}

/deep/ .ant-btn > .anticon + span,
/deep/ .ant-btn > span + .anticon {
  margin-left: 4px;
}

/deep/ .ant-radio-group {
  font-size: 12px;
  color: #999;
}

/deep/ .ant-radio-button-wrapper {
  border: 1px solid #1890ff;
  height: 24px;
  line-height: 23px;
  padding: 0 12px;
}

/deep/ .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  color: #fff;
  background-color: #1890ff;
}

/deep/ .ant-radio-button-wrapper:first-child {
  border-radius: 4px 0 0 4px;
}

/deep/ .ant-radio-button-wrapper:last-child {
  border-radius: 0 4px 4px 0;
}


.page-container {
  height: calc(70% - 12px) !important;
  border-radius: 4px;
  margin: 0 !important;
  padding: 12px;
}

/deep/ .ant-modal-footer {
  padding: 0;
}

.container {
  height: calc(100vh - 400px);
  background: #f4f5fc;
  color: #333;
}

.content {
  height: calc(100% - 57px);
  display: flex;
}

/* 主标题 */

.head-title {
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.head-headerName::before {
  width: 8px;
  background: #1890ff;
  margin-right: 8px;
  content: "\00a0"; /* 填充空格 */

  color: #5aaef4;
}

.head-content {
  padding: 0 0 10px;
}

.left {
  width: 300px;
  background: #fff;
  text-align: center;
  border-radius: 4px;
  padding: 12px;
}

.left-top {
  width: 100%;
  margin-bottom: 12px;
}

.right-top {
  background: #fff;
  border-radius: 100px;
  height: calc(30%);
  margin-bottom: 12px;
}

.flex-sb-center-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
}

.left-bottom {
  width: 100%;
  height: calc(100% - 80px);
  position: relative;
  overflow: auto;
}

.right {
  width: calc(100% - 300px);
  border-radius: 10px;
}

.top {
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 50px;
}

.center {
  width: 100%;
  display: flex;
  justify-content: space-between;
  height: calc((65% - 10px) * 0.4);
}

.empty-block {
  height: calc(100% - 64px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.empty-block-left {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-25%, -75%);
}

.bottom {
  height: calc(100% - 60px);
}

.block {
  width: 100%;
  height: 100%;
  text-align: center;
  background: #fff;
  border-radius: 10px;
  padding: 10px;
}

.block span,
.overview-block {
  width: 100%;
  height: 100%;
  text-align: center;
  background: #FFF;
  border-radius: 4px;
  padding: 12px;
}

.left-top span {
  font-size: 14px;
  /* font-weight: 600; */
}

.dept {
  display: flex;
  justify-content: space-between;
}

.dept span {
  font-size: 14px;
  font-weight: 500;
  color: #1890ff;
}

.chart_table {
  height: calc(100% - 20px);
}

.chart_bar_table {
  height: calc(100% - 24px);
  margin-top: calc(24px);
}

.table-wrap {
  height: calc(100% - 30px);
  overflow: auto;
}

.table-wrap button {
  z-index: 88;
}

.status-lamp {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  margin: auto;
}

.mt10 {
  margin-top: 10px;
}

.mr10 {
  margin-right: 10px;
}

/deep/ .pbi-title .title {
  font-size: 18px;
}

/deep/ .ant-pagination-options-size-changer.ant-select {
  display: inline-block;
}

/deep/ .search-container .vue-treeselect__multi-value-label {
  white-space: nowrap;
  max-width: 41px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/deep/ .search-container .vue-treeselect--searchable.vue-treeselect--multi.vue-treeselect--has-value .vue-treeselect__input-container {
  display: none;
}

/deep/ .search-container .vue-treeselect__limit-tip-text {
  margin: 0;
  margin-top: 4px;
  font-weight: initial;
  text-indent: -44px;
  overflow: hidden;
  display: none;
}

/deep/ .ant-tree li {
  width: 100px
}

/deep/ .ant-tree-title {
  font-size: 12px;
}

.circle {
  width: 5px; /* 圆的直径 */
  height: 5px; /* 圆的直径 */
  border-radius: 50%; /* 将正方形变为圆形 */
}

.folder-container {
  display: flex;
  flex-wrap: wrap; // 允许换行
  gap: 10px; // 文件夹之间的间距
}

.folder {
  width: 150px;
  height: auto; /* 高度自适应，以包含图标和名称 */
  border: none;
  display: flex;
  flex-direction: column; /* 垂直排列子元素 */
  justify-content: center;
  align-items: center;
  gap: 5px; /* 图标和名称之间的间距 */
}

.folder-list{
  width: 50%;
  display: inline-flex;

}

.folder-icon {
  width: 100%;
  height: 100px; /* 固定图标高度 */
}
.folder-icon-list{
  position: relative;
  bottom: -2px;
}

.folder-name {
  text-align: center; /* 名称居中显示 */
  width: 100%;
  position: relative;
  top: -10px;
}
.folder-name-list {

}
.folder:hover {
  background-color: #e0e0e0;
  cursor: default;
  /* 鼠标悬停时的背景色，可根据需要调整 */
}
.folder-list:hover {
  background-color: #e0e0e0;
  cursor: default;
    /* 鼠标悬停时的背景色，可根据需要调整 */
}
.contextmenu {
  position: fixed;
  margin: 0;
  background: #fff;
  z-index: 3000;
  list-style-type: none;
  padding: 5px 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #333;
  box-shadow: 1px 1px 3px 3px rgba(0, 0, 0, 0.3);
}

.contextmenu li {
  margin: 0;
  padding: 7px 16px;
  cursor: pointer;
}
.contextmenu li:hover {
  background: #eee;
}
/deep/.ant-divider-horizontal {
  display: block;
  clear: both;
  width: 100%;
  min-width: 100%;
  height: 1px;
  margin: 12px 0px;
}

</style>