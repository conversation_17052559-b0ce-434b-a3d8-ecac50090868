<template>
  <div class="page-header">
    <h1 class="title">{{ title }}</h1>
    <p v-if="subtitle" class="subtitle">{{ subtitle }}</p>
  </div>
</template>

<script>
export default {
  name: 'PageHeader',
  props: {
    title: {
      type: String,
      required: true
    },
    subtitle: {
      type: String,
      default: ''
    }
  }
};
</script>

<style scoped>
.page-header {
  margin-bottom: 24px;
  padding: 16px 20px;
  border-radius: 8px;
  background: linear-gradient(135deg, #f5f7fa, #e4e7eb);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6px;
  height: 100%;
  background: linear-gradient(to bottom, #1890ff, #096dd9);
  border-radius: 3px 0 0 3px;
}

.title {
  margin-bottom: 8px;
  color: #1f2329;
  font-weight: 600;
  font-size: 24px;
  line-height: 32px;
}

.subtitle {
  margin: 0;
  color: #666;
  font-size: 14px;
}
</style>