<template>
<div>
  <!--<div class="title">所长审核评审清单</div>
   <a-tabs  hide-add type="editable-card" v-model="activeKey" :default-active-key="1" tab-position="top" style="background: #fff;padding: 24px; 0;padding-top: 0;">
    <a-tab-pane  key="1" tab="所长审核" :closable="false"><topiclist :listType="1" /></a-tab-pane>
    <a-tab-pane  key="2" tab="项目管理部审核" :closable="false"><topiclist :listType="2" /></a-tab-pane>
    <a-tab-pane  key="4" tab="院长审核" :closable="false"><topiclist :listType="4" /></a-tab-pane>
  </a-tabs> -->

  <topiclist :listType="3"/>
</div>
</template>

<script>

import topiclist from './topiclist'

export default {
    name:'start_list',
    components: {
        topiclist
    },
    data(){
        return {
            activeKey:'1'
        }
    },
    methods:{
    },
    created () {
    }
}
</script>

<style scoped>

</style>