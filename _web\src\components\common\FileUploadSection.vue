<template>
  <div class="data-import-section">
    <div class="file-operation-card">
      <h5>文件操作</h5>

      <div>
        <a-alert
          v-if="!hasFormula"
          message="请先在公式编辑区域设置并保存公式"
          type="warning"
          show-icon
          class="formula-warning"
        />

        <div class="upload-section">
          <div class="template-download">
            <a-button type="link" size="small" @click="downloadTemplate" icon="download">
              下载模板文件
            </a-button>
          </div>

          <div class="file-upload-container">
            <a-upload
              :fileList="fileList"
              :beforeUpload="beforeUpload"
              :disabled="uploading || !hasFormula"
              :remove="handleRemove"
              :showUploadList="false"
              :multiple="false"
            >
              <div class="upload-dragger-area">
                <a-icon type="inbox" class="upload-icon" />
                <p>点击或拖拽文件到此区域上传</p>
                <p class="upload-hint">
                  <b>注意：</b>请上传符合模板格式的Excel文件，包含温度、SOC和容量数据。
                  文件格式填写错误可能导致数据无法正确解析
                </p>
              </div>
            </a-upload>
          </div>

          <div v-if="uploading" class="upload-loading">
            <a-spin tip="上传处理中..." />
          </div>

          <div v-if="fileList.length > 0" class="file-list">
            <a-list size="small" bordered>
              <a-list-item>
                <a-list-item-meta>
                  <template slot="title">
                    <div class="file-name-container">
                      <a-icon type="file-excel" theme="twoTone" twoToneColor="#52c41a" class="file-icon" />
                      <span class="file-name">{{ fileList[0].name }}</span>
                    </div>
                  </template>
                </a-list-item-meta>
                <template slot="actions">
                  <a-button type="link" size="small" @click="handleRemove" icon="delete">删除</a-button>
                </template>
              </a-list-item>
            </a-list>
          </div>
        </div>
      </div>
    </div>

    <a-row :gutter="16" v-if="dataLoaded">
      <!-- 数据统计摘要 -->
      <a-col :xs="24" :sm="24" :md="6" :lg="6" :xl="6">
        <div class="data-summary-card">
          <h5>数据统计摘要</h5>
          <div>
            <div class="summary-item">
              <div class="summary-label">温度</div>
              <div class="summary-value">
                <a-tag v-for="temp in temperatures" :key="temp" color="blue" class="data-tag">
                  {{ temp }}°C
                </a-tag>
              </div>
            </div>
            <div class="summary-item">
              <div class="summary-label">SOC</div>
              <div class="summary-value">
                <a-tag v-for="soc in socs" :key="soc" color="green" class="data-tag">
                  {{ soc * 100 }}%
                </a-tag>
              </div>
            </div>
          </div>
        </div>
      </a-col>

      <!-- 数据预览 -->
      <a-col :xs="24" :sm="24" :md="18" :lg="18" :xl="18">
        <div v-if="dataPoints.length > 0" class="data-preview-card">
          <h5>数据预览</h5>
          <div>
            <a-tabs v-if="temperatures.length > 0" @change="handleTabChange">
              <a-tab-pane v-for="temp in temperatures" :key="temp" :tab="`${temp}°C`">
                <div class="chart-content">
                  <div class="chart-wrapper">
                    <div :ref="`tempChart_${temp}`" class="echarts-container"></div>
                  </div>
                </div>
              </a-tab-pane>
            </a-tabs>
          </div>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { api } from '@/api';
import { mapActions } from 'vuex';
import { validateFile } from '@/utils/validationUtils';
import { handleUploadError, showSuccess, showInfo, showWarning } from '@/utils/errorUtils';
import { createScatterSeries, createCapacityCurveOptions, createTooltipFormatter, getChartColor } from '@/utils/chartUtils';

export default {
  name: 'FileUploadSection',
  props: {
    hasFormula: {
      type: Boolean,
      required: true
    },
    uploading: {
      type: Boolean,
      default: false
    },
    fileList: {
      type: Array,
      default: () => []
    },
    dataLoaded: {
      type: Boolean,
      default: false
    },
    temperatures: {
      type: Array,
      default: () => []
    },
    socs: {
      type: Array,
      default: () => []
    },
    dataPoints: {
      type: Array,
      default: () => []
    }
  },
  data: () => ({
    fileToUpload: null,
    localUploading: false,
    temperatureCharts: []
  }),
  mounted() {
    if (this.dataLoaded && this.temperatures.length > 0) {
      this.$nextTick(this.renderTemperatureCharts);
    }
  },
  watch: {
    dataPoints(newVal) {
      if (newVal.length > 0 && this.temperatures.length > 0) {
        this.$nextTick(this.renderTemperatureCharts);
      }
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeCharts);
    this.temperatureCharts.forEach(chart => {
      if (chart) chart.dispose();
    });
  },
  methods: {
    ...mapActions(['updateFittingData']),

    downloadTemplate() {
      const link = document.createElement('a');
      link.href = '/模板文件.xlsx';
      link.download = '数据导入模板.xlsx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      showSuccess('模板文件下载已开始');
    },

    handleRemove() {
      this.fileToUpload = null;
      this.updateFittingData({ temperatures: [], socs: [], dataPoints: [] });
      this.$emit('file-removed');
      showInfo('文件已删除');
      return true;
    },

    beforeUpload(file) {
      if (!validateFile(file)) return false;
      this.fileToUpload = file;
      this.$nextTick(this.uploadFile);
      return false;
    },

    async uploadFile() {
      if (!this.fileToUpload) {
        showWarning('请先选择要上传的文件');
        return;
      }

      const formData = new FormData();
      formData.append('file', this.fileToUpload);

      this.localUploading = true;
      this.$emit('update:uploading', true);

      const newFileList = [{
        uid: Date.now().toString(),
        name: this.fileToUpload.name,
        status: 'uploading',
        size: this.fileToUpload.size,
        type: this.fileToUpload.type,
        originFileObj: this.fileToUpload
      }];

      try {
        const response = await api.data.uploadFile(formData);

        if (response.data.success) {
          const { temperatures, socs, data_points } = response.data;

          this.updateFittingData({
            temperatures,
            socs,
            dataPoints: data_points,
          });

          newFileList[0].status = 'done';
          this.$emit('file-uploaded', {
            temperatures, socs, data_points, fileList: newFileList
          });
          showSuccess('数据上传成功');
        } else {
          newFileList[0].status = 'error';
          this.$emit('update:fileList', newFileList);
          showWarning(response.data.message || '数据上传失败');
        }
      } catch (error) {
        newFileList[0].status = 'error';
        this.$emit('update:fileList', newFileList);
        handleUploadError(error);
      } finally {
        this.localUploading = false;
        this.$emit('update:uploading', false);
      }
    },

    renderTemperatureCharts() {
      // 清理现有图表
      this.temperatureCharts.forEach(chart => {
        if (chart) chart.dispose();
      });
      this.temperatureCharts = [];

      // 检查数据有效性
      if (!this.temperatures.length || !this.socs.length || !this.dataPoints.length) return;

      // 为每个温度创建图表
      this.temperatures.forEach(temp => {
        this.$nextTick(() => {
          const chartRefs = this.$refs[`tempChart_${temp}`];
          if (!chartRefs || chartRefs.length === 0) return;

          // 初始化图表
          const chartDom = chartRefs[0];
          const echarts = require('echarts');
          const chart = echarts.init(chartDom);
          this.temperatureCharts.push(chart);

          // 准备数据系列
          const series = [];
          const tempDataPoints = this.dataPoints.filter(point => point.temperature === temp);

          tempDataPoints.forEach(point => {
            const days = point.days || [];
            const capacities = point.capacities || [];
            const dataPoints = days.map((day, i) => [
              parseFloat(day.toFixed(4)),
              parseFloat((capacities[i] * 100).toFixed(4))
            ]);
            const socLabel = `${(point.soc * 100).toFixed(0)}%SOC`;

            series.push(createScatterSeries(
              socLabel,
              dataPoints,
              this.socs.indexOf(point.soc),
              { symbolSize: 5 }
            ));
          });

          // 设置图表选项
          const option = createCapacityCurveOptions(`${temp}°C 容量保持率曲线`, series, {
            legendPosition: 'bottom',
            gridConfig: { bottom: 80, top: 50 },
            tooltip: { formatter: createTooltipFormatter },
            titleConfig: { top: 5 }
          });

          chart.setOption(option);
        });
      });

      window.addEventListener('resize', this.resizeCharts);
    },

    resizeCharts() {
      this.$nextTick(() => {
        this.temperatureCharts.forEach(chart => {
          if (chart) chart.resize();
        });
      });
    },

    getColorBySoc(soc, alpha = 1) {
      const socIndex = this.socs.indexOf(soc);
      const color = getChartColor(socIndex);
      return alpha !== 1 ? color.replace(/[\d.]+\)$/, `${alpha})`) : color;
    },

    handleTabChange() {
      this.$nextTick(this.renderTemperatureCharts);
    }
  }
};
</script>

<style scoped>
.data-import-section {
  margin-bottom: 16px;
}

.file-operation-card,
.data-summary-card,
.data-preview-card {
  padding: 0;
  background-color: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.data-summary-card {
  height: 100%;
}

.file-operation-card > div:not(h5),
.data-summary-card > div:not(h5),
.data-preview-card > div:not(h5) {
  padding: 16px;
}

.formula-warning {
  margin-bottom: 16px;
}

.template-download {
  text-align: right;
  margin-bottom: 8px;
}

.upload-section {
  margin-bottom: 16px;
}

.file-upload-container {
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s;
  padding: 12px;
}

.file-upload-container:hover {
  border-color: #1890ff;
}

.upload-dragger-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 80px;
}

.upload-icon {
  font-size: 32px;
  color: #40a9ff;
  margin-bottom: 8px;
}

.upload-hint {
  font-size: 12px;
  color: #888;
}

.upload-loading {
  margin-top: 8px;
  text-align: center;
}

.file-list {
  margin-top: 12px;
}

.file-name-container {
  display: flex;
  align-items: center;
  background-color: #f6ffed;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid #52c41a;
}

.file-name {
  font-weight: 500;
  color: #333;
  font-size: 14px;
  word-break: break-all;
}

.summary-item {
  margin-bottom: 16px;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-label {
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
  text-align: center;
  background-color: #f0f0f0;
}

.summary-value {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.data-tag {
  margin: 2px;
}

.chart-wrapper {
  background-color: #fff;
  border-radius: 4px;
  padding: 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  height: 460px;
  margin-bottom: 2px;
}

.chart-content {
  padding: 5px;
}

.echarts-container {
  height: 100%;
  width: 100%;
}

h5 {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 0;
  color: #333;
  padding: 10px 0;
  background-color: #f5f5f5;
  border-bottom: 1px solid #f0f0f0;
  text-align: center;
  border-radius: 8px 8px 0 0;
}

/* 高对比度模式支持 */
@media (forced-colors: active) {
  .file-operation-card,
  .data-summary-card,
  .data-preview-card {
    border: 1px solid CanvasText;
    box-shadow: none;
  }

  .file-upload-container {
    border: 1px dashed CanvasText;
  }

  .file-upload-container:hover {
    border: 2px dashed Highlight;
  }

  .upload-icon {
    color: Highlight;
  }

  h5 {
    color: CanvasText;
    border-bottom: 1px solid CanvasText;
  }

  .chart-wrapper {
    border: 1px solid CanvasText;
    box-shadow: none;
  }
}
</style>