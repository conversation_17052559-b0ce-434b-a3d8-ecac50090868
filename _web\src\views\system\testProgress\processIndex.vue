<template>
    <div class="layout-main">
        <div @mouseenter.stop="mouseenter" @mouseleave.stop="mouseleave" class="slide" :style="{ overflowY: 'hiden' ,position: 'fixed', left:0}">
            <a-menu
            :inlineIndent="12"
            style="width: 200px;"
            :default-selected-keys="[navTag]"
            mode="inline"
            theme="light"
            :inline-collapsed="collapsed"
            >



                <a-sub-menu key="80" @titleClick="showView(8)" style="padding-left: 0px">
                  <span slot="title"><a-icon type="calendar" /><span>日历寿命测试</span></span>
                  <a-sub-menu key="8" @titleClick="showView(8)" >
                    <span slot="title"><a-icon type="calculator" /><span>日历寿命测试计划</span></span>
                    <a-menu-item key="9" @click="showView(9)">
                      <a-icon type="bar-chart" />
                      <span>出箱甘特图</span>
                    </a-menu-item>
                  </a-sub-menu>




              </a-sub-menu>

        <a-sub-menu key="999" @titleClick="showView(10)" style="padding-left: 0px">
          <span slot="title"><a-icon type="appstore" /><span>数据处理工具</span></span>
          <a-menu-item key="90" @click="showView(10)" v-if="hasPerm('testDataExportTask:query')">
            <a-icon type="sliders"/>
            <span>数据全量提取</span>
          </a-menu-item>

          <a-menu-item key="100" @click="showView(11)" v-if="hasPerm('testDataExportTask:dataHandle')">
            <a-icon type="rise"/>
            <span>在线数据处理</span>
          </a-menu-item>

          <a-menu-item key="150" @click="showView(15)" v-if="hasPerm('testDataExportTask:dataHandle')">
            <a-icon type="file-excel"/>
            <span>离线数据处理</span>
          </a-menu-item>

          <a-menu-item key="110" @click="showView(12)" v-if="hasPerm('testDataExportTask:list')">
            <a-icon type="rocket"/>
            <span>处理记录查询</span>
          </a-menu-item>
          <a-menu-item key="160" @click="showView(18)" v-if="hasPerm('testDataExportTask:list')">
            <a-icon type="control"/>
            <span>常用模型管理</span>
          </a-menu-item>
        </a-sub-menu>


        <!-- 技师工作台 -->
        <a-menu-item key="10" @click="showView(13)" v-if="hasPerm('technician:staging')">
          <a-icon type="desktop"/>
          <span>工作台</span>
        </a-menu-item>

        <!-- 任务分配 -->
        <a-menu-item key="11" @click="showView(14)" v-if="hasPerm('technician:staging')">
          <a-icon type="control"/>
          <span>任务分配</span>
        </a-menu-item>

        <!-- 结果复核 -->
        <a-menu-item key="12" @click="showView(16)" v-if="hasPerm('technician:staging')">
          <a-icon type="control"/>
          <span>结果复核</span>
        </a-menu-item>

        <!-- 测试台账 -->
        <a-menu-item key="13" @click="showView(19)" v-if="hasPerm('technician:staging')">
          <a-icon type="control"/>
          <span>测试台账</span>
        </a-menu-item>
      </a-menu>
      <div class="collapsed_bar" :style="collapsed ? left40 : left20" @click="toggleCollapsed">
        <a-icon class="collapsed_btn" :type="collapsed ? 'double-right' : 'double-left'"/>
      </div>
    </div>
    <div class="wrap" :style="{ padding: '4px 8px', overflowX: 'auto' }">
      <testProgressIndex v-if="navTag == '8'"/>
      <testProgressGant v-if="navTag == '9'"/>
      <TechniciansWorkbench v-if="navTag == '13'"/>
      <testAssignment v-if="navTag == '14'"/>
      <resultReview v-if="navTag == '16'"/>
      <testingLedger v-if="navTag == '19'"/>
      <folderIndex v-if="navTag == '10'"/>
      <dataIndex v-if="navTag == '11'" :param="param"/>
      <dataIndexJM v-if="navTag == '15'" :param="param"/>
      <exportTask v-if="navTag == '12'"/>
      <black v-if="navTag == '18'"/>
    </div>
  </div>

</template>

<script>

  import { mixin } from '@/utils/mixin'
  import { mapActions } from 'vuex'
  import TechniciansWorkbench from "./workbench/technicians"
  import testProgressIndex from './index'
  import testProgressGant from './gant'
  import folderIndex from '../lims/folder/index'
  import exportTask from '../lims/folder/exportTask'
  import dataIndex from '../lims/data/index'
  import testAssignment from '../lims/task/taskAssignment'
  import testingLedger from '../lims/task/testingLedger'
  import resultReview from '../lims/task/resultReview'
  import dataIndexJM from '../lims/data/indexJM'
  import black from '../batterydesign/black'

  export default {
    mixins: [mixin],
    components: {
      testProgressIndex,
      testProgressGant,
      exportTask,
      dataIndex,
      dataIndexJM,
      folderIndex,
      testAssignment, // 任务分配
      resultReview, // 结果复核
      testingLedger, // 测试台账
      black,
      TechniciansWorkbench // 技师工作台
    },
     watch: {
      sidebarOpened (val) {
        this.collapsed = !val
      },
     },
    created(){
        if (window.status != '') {

            this.navTag = window.status
        }
        this.collapsed = !this.sidebarOpened

    },
    data() {
        return {
            param:null,
            navTag:'8',
            collapsed: false,
            width187:{
              width:'187px',
              textAlign: 'right'
            },
            width40:{
              width:'40px',
              textAlign: 'center'
            },
            left20: {
              left: "187px"
            },
            left40: {
              left: "40px"
            }
        };
    },
    methods: {
      ...mapActions(['setSidebar']),
      mouseenter() {
        this.collapsed = true
        this.setSidebar(this.collapsed)
      },
      mouseleave() {
        this.collapsed = false
        this.setSidebar(this.collapsed)
      },


      toggleCollapsed() {
        this.collapsed = !this.collapsed
      },
      showView(tg,param) {
        this.param = param
        this.navTag = tg + ""
        window.status = tg + ""
      }
    },
    mounted() {
      if (window.status == "") {
        window.status = this.navTag
      } else if (window.status != "") {
        this.navTag = window.status
      }
      if (this.$route.query.navTag != null) {
        this.navTag = this.$route.query.navTag
      }
    },
    beforeDestory() {
        window.status = '';
    }
}
</script>

<style lang='less' scoped=''>
.layout-main{
    display: flex;
    flex-direction: row;
}
.wrap{
    flex: 1;
}
.slide{
    height: calc(100vh - 40px);
    max-width: 200px;
    background-color: #fff;
    box-shadow: 2px 0px 8px 0px rgba(29, 35, 41, 5%);
    z-index: 100;
}
.collapsed_bar{
  position: fixed;
  width: 20px;
  bottom: 0;
  /* top: 0; */
  cursor: pointer;

}
/* .collapsed_btn{
  position: absolute;
  top: 45%;
} */
/deep/.ant-menu-light{
    border-right-color: transparent;
}
/deep/.ant-menu-inline-collapsed{
  width: 40px !important;
}
/deep/.ant-layout-sider-collapsed{
  flex: 0 0 40px !important;
  max-width: 40px !important;
  min-width: 40px !important;
  width: 40px !important;
}
/deep/.ant-menu-inline-collapsed > .ant-menu-item,
/deep/.ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item, .ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-submenu > .ant-menu-submenu-title, .ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title{
  padding: 0 12px !important;
}
</style>
<style>
.ant-layout-sider-collapsed{
  flex: 0 0 40px !important;
  max-width: 40px !important;
  min-width: 40px !important;
  width: 40px !important;
}
.ant-menu-inline-collapsed > .ant-menu-item,
.ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item, .ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-submenu > .ant-menu-submenu-title, .ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title{
  padding: 0 12px !important;
}
.ant-menu-vertical .ant-menu-item,
.ant-menu-vertical-left .ant-menu-item,
.ant-menu-vertical-right .ant-menu-item,
.ant-menu-inline .ant-menu-item,
.ant-menu-vertical .ant-menu-submenu-title,
.ant-menu-vertical-left .ant-menu-submenu-title,
.ant-menu-vertical-right .ant-menu-submenu-title,
.ant-menu-inline .ant-menu-submenu-title{
  font-size: 13px;
}
</style>