<template>
  <div class="pbi-tabs">
    <div class="pbi-tab-item" v-for="item in tabsList" :key="item.value"
      :class="activeKey == item.value ?  'pbi-tab-active' : ''" @click="handleClickTab(item.value)">{{item.label}}</div>
  </div>
</template>
<script>

  /*
  格式：
        tabsList : [{value:'点击时，返回的值',label:'页面上展示的值'}] 
  例：
        template
          <pbiTabs :tabsList="tabsList" :activeKey="activeKey" @clickTab="handleTabsChange2"></pbiTabs>
          
        script
          import pbiTabs from '@/components/pageTool/components/pbiTabs.vue'
          components : {
            pbiTabs
          },
          data(){
            return{
              tabsList:[{value:'2',label:'A样'}]
              activeKey:'2'
            }
          },
          methods:{
            handleClickTab(e) {
              this.activeKey = e
            },
          }
  */
  export default {
    props: {
      tabsList: {
        type: Array,
        default: []
      },
      activeKey:{
        type: [Number,String],
        required: false,
      }
    },
    methods:{
      handleClickTab(value){
        this.$emit('clickTab',value)
      }
    }
  }
</script>
<style lang='less' scoped>
  .pbi-tabs {
    display: flex;
    align-items: center;
    font-size: 12px;
  }

  .pbi-tab-item {
    padding: 12px 30px;
    color: #333;
    cursor: pointer;
  }

  .pbi-tab-item:hover {
    color: #1890FF;
  }

  .pbi-tab-active {
    position: relative;
    background: #fff;
    border-radius: 10px 10px 0 0;
    transition: .2s;
    color: #1890FF;
    /* font-size: 13px; */
  }

  .pbi-tab-active:not(:first-child)::before,
  .pbi-tab-active::after {
    position: absolute;
    bottom: 0;
    content: '';
    width: 20px;
    height: 20px;
    border-radius: 100%;
    box-shadow: 0 0 0 40px #fff;
    /*使用box-shadow不影响尺寸*/
    transition: .2s;
  }

  .pbi-tab-active:not(:first-child)::before {
    left: -20px;
    clip-path: inset(50% -10px 0 50%);
  }

  .pbi-tab-active::after {
    right: -20px;
    clip-path: inset(50% 50% 0 -10px);
  }
</style>