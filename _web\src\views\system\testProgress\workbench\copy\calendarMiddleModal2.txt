<template>
	<a-modal
		:title="`${modalData.testName}`"
		:visible="true"
		width="85%"
		:centered="true"
		:cancel-button-props="{ style: { display: 'none' } }"
		okText="关闭1"
		@cancel="handleModelCancel"
		@ok="handleModelCancel"
	>
		<div class="modal-wrapper">
			<!-- step -->
			<div>
				<a-steps :current="current" size="small">
					<a-step v-if="middleCheck !== 'normal'" title="大小中检" />
					<a-step title="数据填写" />
					<a-step title="进箱时间填写" />
				</a-steps>
			</div>
			<!-- 第一步  -->
			<div v-if="current === 0 && middleCheck !== 'normal'">
				<a-spin :spinning="modalLoading">
					<div>
						<a-descriptions title="详细信息">
							<a-descriptions-item label="测试项目别名">
								{{ middleData[0].alias }}
							</a-descriptions-item>
							<a-descriptions-item label="大小中检">
								{{
									middleData[0].middleCheck === "small"
										? "小中检"
										: middleData[0].middleCheck === "large"
										? "大中检"
										: "-"
								}}
							</a-descriptions-item>
							<a-descriptions-item label="计划开始时间">
								{{ details.inDate || "-" }}
							</a-descriptions-item>
							<a-descriptions-item label="计划结束时间">
								{{ details.outDate || "-" }}
							</a-descriptions-item>
							<a-descriptions-item label="总存储天数">
								{{ details.totalDay || "-" }}
							</a-descriptions-item>
							<a-descriptions-item label="存储阶段">
								{{ details.orderNumber ? (details.orderNumber === 1 ? '初始性能检测' : details.orderNumber - 1)  : "-" }}
							</a-descriptions-item>
						</a-descriptions>
					</div>
					<div class="bottom-table">
						<a-descriptions title="详细信息"></a-descriptions>
						<div>
							<a-upload
								:disabled="modalData.taskStatus === '已完成' ? true : false"
								:headers="headers"
								:action="`/api/testProjectTodoTask/import`"
								name="file"
								:fileList="fileList"
								:data="{ ordTaskId: modalData.ordTaskId, middleCheckStage: 1 }"
								:showUploadList="false"
								accept="*"
								@change="handleUploadFile($event)"
							>
								<a-button
									:disabled="modalData.taskStatus === '已完成' ? true : false"
									type="primary"
									size="small"
									class="mr10"
									ghost
									>导入数据</a-button
								>
							</a-upload>
							<a-button type="primary" size="small" ghost @click="handleDownload($event)">导出模板</a-button>
						</div>
						<a-table
							bordered
							class="mt10"
							:columns="middleColums"
							:rowKey="record => record.cellTestCode"
							:data-source="middleData"
							:pagination="false"
						>
							<span
								v-for="item in middleList"
								:key="item.cellTestCode"
								:slot="item.dataIndex"
								slot-scope="text, record, index"
							>
								<span v-if="item.dataIndex === 'cellTestCode'">
									<div class="blue hand" @click="handleCopy(text)">{{ text }}<a-icon type="copy" /></div>
								</span>
								<span v-else-if="item.dataIndex === 'alias'">
									<div class="blue hand" @click="handleAliasCopy">{{ text }}<a-icon type="copy" /></div>
								</span>
								<!-- class:one:第一步，middleCheck：对应的字段，index：那行,为了未填写标红 -->
								<span :id="`one-middleCheck-${index}`" v-else-if="item.dataIndex === 'middleCheck'">
									<a-button
										v-if="details.middleCheck !== 'normal'"
										type="link"
										:style="record.isMiddleClick ? 'color:green' : ''"
										@click="() => chooseMgData(record, `one-middleCheck-${index}`)"
									>
										{{ record.isMiddleClick ? (text === "large" ? "大中检" : "小中检") : "请选择数据"
										}}<a-icon v-if="record.isMiddleClick" type="edit" />
									</a-button>
									<span v-else type="link">-</span>
								</span>

								<a-input
									v-else
									:id="`one-${item.dataIndex}-${index}`"
									:placeholder="`请输入${item.title}`"
									v-model="middleData[index][item.dataIndex]"
									:disabled="modalData.taskStatus === '已完成' ? true : false"
									@paste="copyFromExcel($event, middleColums, middleData, index, item.dataIndex)"
									@blur="value => handleInput(value.target._value, `one-${item.dataIndex}-${index}`, index)"
								/>
							</span>
						</a-table>
					</div>

					<!-- 测试数据选择弹窗 start  -->
					<a-modal
						title="测试数据选择"
						width="90%"
						:height="300"
						:bodyStyle="{ padding: 0 }"
						:visible="mgVisible"
						style="padding: 0"
						:maskClosable="false"
						:centered="true"
						@cancel="handleCloseModal"
						destroyOnClose
					>
					<div class="child-table">
						<a-table
							:columns="mgColumns"
							:dataSource="mgData"
							class="mt10"
							bordered
							:rowKey="record => record.flowId"
							:pagination="false"
							:rowSelection="{
								type: 'radio',
								onSelect: selectTestData,
								getCheckboxProps: getCheckboxProps
							}"
						>
							<template slot="celltestcode" slot-scope="text, record, index, columns">
								<a @click="openStepData(record)" style="text-align: center">{{ text }}</a>
							</template>
						</a-table>
					</div>
						<template slot="footer" slot-scope="text, record">
							<a-button key="back" @click="handleCloseModal">
								关闭
							</a-button>
						</template>
					</a-modal>
					<!-- 测试数据选择弹窗 end  -->

					<!-- <a-divider>详细</a-divider> -->
				</a-spin>
			</div>

			<!-- 第二步 -->
			<template v-if="(current === 0 && middleCheck === 'normal') || (current === 1 && middleCheck !== 'normal')">
				<a-spin :spinning="modalLoading">
					<!-- top -->
					<div>
						<a-descriptions title="详细信息">
							<a-descriptions-item label="测试项目别名">
								{{middleData[0].alias}}
							</a-descriptions-item>
							<a-descriptions-item label="大小中检">
								{{
									middleData[0].middleCheck === "small"
										? "小中检"
										: middleData[0].middleCheck === "large"
										? "大中检"
										: "-"
								}}
							</a-descriptions-item>
							<a-descriptions-item label="计划开始时间">
								{{ details.inDate || "-" }}
							</a-descriptions-item>
							<a-descriptions-item label="计划结束时间">
								{{ details.outDate || "-" }}
							</a-descriptions-item>
							<a-descriptions-item label="总存储天数">
								{{ details.totalDay || "-" }}
							</a-descriptions-item>
							<a-descriptions-item label="存储阶段">
								{{ details.orderNumber ? (details.orderNumber === 1 ? '初始性能检测' : details.orderNumber - 1)  : "-" }}
							</a-descriptions-item>
						</a-descriptions>
					</div>
					<div class="bottom-table">
						<a-descriptions title="详细信息"></a-descriptions>
						<div>
							<a-upload
								:disabled="modalData.taskStatus === '已完成' ? true : false"
								:headers="headers"
								:action="`/api/testProjectTodoTask/import`"
								name="file"
								:fileList="fileList"
								:data="{ ordTaskId: modalData.ordTaskId , middleCheckStage: current + 1 }"
								:showUploadList="false"
								accept="*"
								@change="handleUploadFile($event)"
							>
								<a-button
									:disabled="modalData.taskStatus === '已完成' ? true : false"
									type="primary"
									size="small"
									class="mr10"
									ghost
									>导入数据</a-button
								>
							</a-upload>
							<a-button type="primary" size="small" ghost @click="handleDownload($event, current + 1)"
								>导出模板</a-button
							>
						</div>
						<a-table
							bordered
							class="mt10"
							:columns="dataColums"
							:rowKey="record => record.cellTestCode"
							:data-source="dataInfo"
							:pagination="false"
						>
							<span
								v-for="item in dataList"
								:key="item.cellTestCode"
								:slot="item.dataIndex"
								slot-scope="text, record, index"
							>
								<span v-if="item.dataIndex === 'cellTestCode'">
									<div class="blue hand" @click="handleCopy(text)">{{ text }}<a-icon type="copy" /></div>
								</span>
								<span v-else-if="item.dataIndex === 'alias'">
									<div class="blue hand" @click="handleAliasCopy">{{ text }}<a-icon type="copy" /></div>
								</span>
								<a-input
									v-else
									:id="`two-${item.dataIndex}-${index}`"
									:placeholder="`请输入${item.title}`"
									v-model="dataInfo[index][item.dataIndex]"
									:disabled="modalData.taskStatus === '已完成' ? true : false"
									@paste="copyFromExcel($event, dataColums, dataInfo, index, item.dataIndex)"
									@blur="value => handleInput(value.target._value, `two-${item.dataIndex}-${index}`,index)"
								/>
							</span>
						</a-table>
					</div>
				</a-spin>
			</template>

			<!-- 第三步 -->
			<div
				class="time-block"
				v-if="(current === 1 && middleCheck === 'normal') || (current === 2 && middleCheck !== 'normal')"
			>
        <div>
          <span class="mr10">进箱时间：</span>
          <a-date-picker
            :disabled="modalData.taskStatus === '已完成' ? true : false"
            :disabled-date="disabledDate"
            :default-value="actualInDate"
            @change="handleChangeDate"
          />
        </div>
        <div style="margin-top: 50px">
          <span class="mr10">进箱位置：</span>
          <a-button type="primary" @click="setInboxPosition">设置进箱位置</a-button>
          <a-table
            class="mt10"
            bordered
            style="width: 80%;"
            :columns="inBoxColumns"
            :rowKey="record => record.cellTestCode"
            :data-source="middleData"
            :pagination="false"
            :row-selection="{
                  selectedRowKeys: cellSelectedRowKeys,
                  selectedRows: cellSelectedRows,
                  onChange: cellOnSelect,
                  columnWidth:20
                 }"
          >
          </a-table>
        </div>
			</div>
		</div>
		<!-- 底部按钮 -->
		<template slot="footer">
			<a-button v-if="current !== 0" type="primary" @click="handleStep(-1)">上一步</a-button>
			<a-button
				v-if="(current !== 2 && middleCheck !== 'normal') || (current !== 1 && middleCheck === 'normal')"
				type="primary"
				@click="handleStep(1)"
				>下一步</a-button
			>
			<a-popconfirm
				v-if="
					((current === 2 && middleCheck !== 'normal') || (current === 1 && middleCheck === 'normal')) &&
						modalData.taskStatus !== '已完成'
				"
				placement="top"
				ok-text="确认"
				cancel-text="取消"
				@confirm="handleSubmit"
			>
				<template slot="title">
					<p>确认完成吗？</p>
				</template>
				<a-button type="primary">完成</a-button>
			</a-popconfirm>
			<a-button @click="handleModelCancel">关闭</a-button>
		</template>
		<step-data ref="stepData"></step-data>
		<div>
			<a-modal title="条码启动天数输入" :visible="isShowDays" @ok="handleModalOk" @cancel="handleModalCancel">
				<a-form :label-col="{ span: 11 }" :wrapper-col="{ span: 13 }">
					<a-form-item label="启动天数">
						<a-input-number v-model="startDay" :precision="0" :min="1" />
					</a-form-item>
				</a-form>
			</a-modal>
      <a-modal :width="800" title="设置进箱位置" :visible="isShowPositions" @ok="handlePositionOk" @cancel="handlePositionCancel">
        <s-table
          bordered
          :columns="inBoxPositionColumns"
          :rowKey="record => record.rackNumber"
          :data="positionLoadData"
          :row-selection="{
                  type: 'radio',
                  selectedRowKeys: positionSelectedRowKeys,
                  selectedRows: positionSelectedRows,
                  onChange: positionOnSelect,
                  columnWidth:20
                 }"
        >
        </s-table>
      </a-modal>
		</div>
	</a-modal>
</template>

<script>
import moment from "moment"
import { STable } from '@/components'
import { getTestProDetailByTaskId } from "@/api/modular/system/testProgressManager"

import { mixin } from "../mixin/index"

import { debounce } from "@/utils/util"

export default {
  components: {
    STable
  },
	mixins: [mixin],
	created() {
		this.getTestProDetailByTaskId()
		this.getTestProgress()
	},
	methods: {
		getTestProDetailByTaskId(current = 0) {
			this.modalLoading = true
			getTestProDetailByTaskId(this.modalData.ordTaskId)
				.then(res => {
					if (!res.success) return this.$message.error("错误提示：" + res.message)

					this.details = res.data


					if (this.modalData.taskStatus !== "已完成" && current === 0 ) {
						this.current = res.data.currentStep ? Number(res.data.currentStep) : 0
					}

					// 删除不要的字段
					res.data.lifeTestRecordDataMap.forEach(v => {
						// 有大小中检，且没有选择数据
						v.isMiddleClick =
							(v.middleCheck !== "normal" && v.checkData) ||
							(v.middleCheck !== "normal" && res.data.currentStep !== null) ||
							this.modalData.taskStatus === "已完成"
								? true
								: false
						// delete v.heightType
					})

					// 大小中检
					this.middleData = res.data.lifeTestRecordDataMap
					this.middleColums = this._handleColums(res.data.lifeTestRecordDataMap, 1, true)[0]
					this.middleList = this._handleColums(res.data.lifeTestRecordDataMap, 1, true)[1]


					// 数据填写
					this.dataInfo = res.data.lifeTestRecordDataMap
					this.dataColums = this._handleColums(
						res.data.lifeTestRecordDataMap,
						2,
						res.data.lifeTestRecordDataMap[0].middleCheck === "normal" ? false : true
					)[0]
					this.dataList = this._handleColums(
						res.data.lifeTestRecordDataMap,
						2,
						res.data.lifeTestRecordDataMap[0].middleCheck === "normal" ? false : true
					)[1]



					// 是否处于大小中检
					this.middleCheck = this.middleData[0].middleCheck

					// 进箱时间
					this.actualInDate = res.data.actualInDate
						? moment(res.data.actualInDate, "YYYY-MM-DD")
						: moment(new Date(), "YYYY-MM-DD")

					if (!res.data.actualInDate && ((current === 1 && middleCheck === 'normal') || (current === 2 && middleCheck !== 'normal'))) {
						this.handleChangeDate("", String(moment(new Date()).format("yyyy-MM-DD")))
					}
				})
				.finally(() => {
					this.modalLoading = false
				})
		},

		// 处理表头新
		_handleColums(value, current, middleCheck = false) {
			let temColuns = []
			const temList = []

			for (let i in value[0]) {
				const temObj = {
					title: this.tableNameMenu[i],
					dataIndex: i,
					width: '12vw',
					align: "center",
					scopedSlots: {
						customRender: i
					}
				}

				const childrenObj = {
					title: i.replaceAll(/[^0-9]/g, ""),
					dataIndex: i,
					width: '12vw',
					align: "center",
					scopedSlots: {
						customRender: i
					}
				}

				const childrenObj1 = {
					title: this.tableNameMenu[i.replaceAll(/[0-9]/g, "")] + i.replaceAll(/[^0-9]/g, ""),
					dataIndex: i,
					width: '12vw',
					align: "center",
					scopedSlots: {
						customRender: i
					}
				}

				const temObj1 = {
					title: this.tableNameMenu[i.replaceAll(/[0-9]/g, "")],
					dataIndex: i.replaceAll(/[0-9]/g, ""),
					width: '12vw',
					align: "center",
					children: [childrenObj]
				}



				if (
					(i === "middleCheck" && current === 2) ||
					i === "heightType" ||
					i === "isMiddleClick" ||
					i === "checkData" ||
					i === "timeOfFillInnerres" ||
          i === "timeOfFillInnerres2" ||
          i === "inBoxPosition"
				)
					continue

				// 有中检，第一步
				if (middleCheck && current === 1 && (i === "afterInnerres" || i === "afterVoltage")) {
					continue
				}


				// 有中检，第二步
				if (middleCheck && current === 2 && i !== "afterInnerres" && i !== "afterVoltage" &&  i !== "alias" && i !== "cellTestCode") {
					continue
				}


				if (i.match(/\d/g)) {
					const temIndex = temColuns.findIndex(v => v.dataIndex === i.replaceAll(/[0-9]/g, ""))
					if (temIndex === -1) {
						temColuns.push(temObj1)
						temList.push(childrenObj1)
					} else {
						temColuns[temIndex].children.push(childrenObj)
						temList.push(childrenObj1)
					}
				} else {
					temColuns.push(temObj)
					temList.push(temObj)
				}
			}

			const chilList = []
			const temColuns1 = []
			// 划分尺寸表头
			temColuns.forEach(v => {
				if (v.dataIndex === "timeOfFillInnerres" || v.dataIndex === "timeOfFillInnerres2") {
                                        return
                                }
				if (
					v.dataIndex === "cellTestCode" ||
					v.dataIndex === "alias" ||
					v.dataIndex === "middleCheck" ||
					v.dataIndex === "beforeVoltage" ||
					v.dataIndex === "beforeInnerres" ||
					v.dataIndex === "afterVoltage" ||
					v.dataIndex === "afterInnerres" ||
					v.dataIndex === "volume" ||
					v.dataIndex === "weight" ||
					v.dataIndex === "heightType" ||
					v.dataIndex === "isolateres"
				) {
					if ((v.dataIndex === "cellTestCode" || v.dataIndex === "alias") && temColuns.length > 8) {
						v.fixed = "left"
					}
					return temColuns1.push(v)
				}
				chilList.push(v)
			})

			temColuns = temColuns1

			if (chilList.length !== 0) {
				temColuns.push({
					title: "尺寸/mm",
					dataIndex: "dimension",
					align: "center",
					children: chilList
				})
			}

			return [temColuns, temList]
		},
		// 上一步，下一步
		async handleStep(index) {
			if (this.modalData.taskStatus === "已完成") return (this.current += index)

			// 无中检，两步，有中检，三步

			// 第一步,中检信息校验是否填写完成
			if (
				((this.current === 0 && this.middleCheck !== "normal") ||
					(this.current === 1 && this.middleCheck === "normal")) &&
				index === 1
			) {
				const temList = JSON.parse(JSON.stringify(this.middleData))
				temList.forEach(e => {
					delete e.afterInnerres
					delete e.afterVoltage
				})
				// 没填写完成
				if (!(await this._handleIsNull(temList))[0] || this.middleData.findIndex(v => !v.isMiddleClick) !== -1) {
					this._handleSetBGC(temList, "one")

					return this.$warning({
						content: "请将数据完整填写在进行下一步"
					})
					// 填写完成
				} else {
					const that = this
					const temTitle = (await this._handleIsNull(temList))[1]
						? "是否完成大/小中检?"
						: "尺寸测量数据不完整，是否完成大/小中检?"
					this.$confirm({
						title: temTitle,
						onOk() {
							that.current += index
							// 更新阶段，更新为第一阶段
							const params = {
								id: that.modalData.ordTaskId,
								currentStep: 1
							}
							that.updateTestProDetail(params)
						},
						onCancel() {},
						class: "test"
					})
					return
				}
			}

			// 第二步，数据信息校验是否填写完成
			if (
				((this.current === 1 && this.middleCheck !== "normal") ||
					(this.current === 0 && this.middleCheck === "normal")) &&
				index === 1 &&
				!(await this._handleIsNull(this.dataInfo))[0]
			) {
				const temList = JSON.parse(JSON.stringify(this.dataInfo))
				this._handleSetBGC(temList, "two")

				return this.$warning({
					content: "请将数据完整填写在进行下一步"
				})
			} else if (
				// 无中检、数据填写 ,且没有把点填完
				this.current === 0 &&
				this.middleCheck === "normal" &&
				index === 1 &&
				!(await this._handleIsNull(this.dataInfo))[1]
			) {
				const that = this
				this.$confirm({
					title: "尺寸测量数据不完整，是否继续进行下一步操作？",
					onOk() {
						that.current += index
						const params = {
							id: that.modalData.ordTaskId,
							currentStep: that.middleCheck !== "normal" ? 2 : 1
						}
						that.updateTestProDetail(params)
					},
					onCancel() {},
					class: "test"
				})
				return
			}

			this.current += index

			
			const params = {
				id: this.modalData.ordTaskId,
				currentStep: this.middleCheck !== "normal" ? 2 : 1
			}

			if (!this.details.actualInDate && ((this.current === 1 && this.middleCheck === 'normal') || (this.current === 2 && this.middleCheck !== 'normal'))) {
				this.handleChangeDate("", String(moment(new Date()).format("yyyy-MM-DD")))
			}

			this.updateTestProDetail(params)
		},
		/**
		 * 附件事件
		 */
		handleUploadFile(info) {
			this.fileList = [...info.fileList]
			if (info.file.response.success) {
				this.getTestProDetailByTaskId(this.current)
				this.$message.success(`${info.file.name} 数据导入成功`)
      } else {
        this.$message.error(`${info.file.name} 数据导入失败:` + info.file.response.message)
      }
			this.$forceUpdate()
		}
	}
}
</script>

<style lang="less" scoped>
@import "../style/calendar.less";
</style>
