<template>
    <a-modal title="填写" :width="400" :visible="visible" :confirmLoading="confirmLoading" @ok="handleSubmit" @cancel="handleCancel">
        <a-spin :spinning="confirmLoading">
            <a-form :form="form">
                <a-form-item style="display: none;" has-feedback>
                    <a-input v-decorator="['issueId']" />
                </a-form-item>
                <a-form-item style="display: none;" has-feedback>
                    <a-input v-decorator="['feedbackType']" />
                </a-form-item>
                <a-form-item label="" has-feedback>
                    <a-textarea :rows="4" placeholder="请输入反馈内容" v-decorator="['feedbackContent', {rules: [{required: true, message: '请输入反馈内容！'}]}]"></a-textarea>
                </a-form-item>
            </a-form>
        </a-spin>
    </a-modal>
</template>

<script>
    import {
        feedBackAdd
    } from '@/api/modular/system/feedbackManage'
    export default {
        data() {
            return {
                form: this.$form.createForm(this),
                visible: false,
                confirmLoading: false
            }
        },
        methods: {
            add(record, type) {
                setTimeout(() => {
                    let feeback = type == 1 ? record.produceFeedback : ( type == 2 ? record.sellFeedback : record.supplyFeedback)
                    this.form.setFieldsValue({
                        feedbackType: type,
                        issueId: record.issueId,
                        feedbackContent:feeback
                    })
                }, 100)
                this.visible = true
            },
            handleCancel() {
                this.form.resetFields()
                this.confirmLoading = false
                this.visible = false
            },
            handleSubmit() {
                const {
                    form: {
                        validateFields
                    }
                } = this
                this.confirmLoading = true
                validateFields((errors, values) => {
                    if (!errors) {
                        feedBackAdd(values).then((res) => {
                            this.confirmLoading = false
                            if (res.success) {
                                this.$message.success('提交成功')
                                this.handleCancel()
                                this.$emit('ok')
                            } else {
                                this.$message.error('提交失败：' + res.message)
                            }
                        }).finally((res) => {
                            this.confirmLoading = false
                        })
                    } else {
                        this.confirmLoading = false
                    }
                })
            },
        }
    }
</script>

<style>

</style>