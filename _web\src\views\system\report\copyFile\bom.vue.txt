<template>
<div>
    <div class="head" v-if="showDetail">BOM 信息状态管理</div>
    <ve-table
      :border-y="true"
      fixed-header
      :max-height="windowHeight > 380 ? windowHeight : 380"
      :columns="columns"
      :table-data="rows"
      :cell-style-option="cellStyleOption"
      :cell-span-option="cellSpanOption"
      :scroll-width="scrollWidth"
      id="loading-bom"
      
    />
</div>
</template>

<script>
import { getBomData } from "@/api/modular/system/report"
import { mapActions, mapGetters } from 'vuex'
import { ALL_APPS_MENU } from '@/store/mutation-types'
    import Vue from 'vue'
export default {
    data() {
        return {
            showDetail: false,
            scrollWidth:null,
            windowHeight: document.documentElement.clientHeight,
            loadingInstance: null,
            columns: [],
            rows: [],
            merges: ['productType','material',],
            cellStyleOption: {
                bodyCellClass: ({ row, column, rowIndex }) => {
                    let year = (new Date()).getFullYear();
                    let renderInputArr = ['material','supplierType','materialNo','supplierCode','materialDemand'+year,'materialDemand'+(year+1),'materialSupply'+year,'materialSupply'+(year+1)]
                    let obj = {
                        0: "dangerdetail",
                        1: "successdetail"
                    };
                    let index = renderInputArr.indexOf(column.key);
                    if (index < 0) {
                        return obj[row[column.key]];
                    }
                },
            },
            cellSpanOption: {
                bodyCellSpan: this.bodyCellSpan
            },
        }
    },
    methods: {
        getTreeLeaf(treeData, arr) {
			if (Array.isArray(treeData)) {
				treeData.forEach(item => {
					if (item.children && item.children.length > 0) {
						this.getTreeLeaf(item.children, arr)
					} else {
						if(arr.indexOf(item.key)<0){
							
							item.renderBodyCell = ({ row, column, rowIndex }) => {
								return <span></span>;
							}
						}
					}
				})
			} 
		},
        switchApp () {
            const applicationData = Vue.ls.get(ALL_APPS_MENU)
            this.MenuChange(applicationData[0]).then((res) => {
            }).catch((err) => {
            this.$message.error('错误提示：' + err.message,1)
            })
        },
        show() {
            this.loadingInstance.show();
        },
        close() {
            this.loadingInstance.close();
        },
        ...mapActions(['MenuChange']),
        callReportBomData(){
            this.show()
            getBomData(this.$route.query)
            .then((res) => {
                if (res.result) {
                    if (res.data.rowdata)
                        this.merges.forEach((item) => {
                        for (let i = 0, j = res.data.rowdata.length; i < j; i++) {
                            let rowSpan = 0;
                            let n = i;
                            while (
                                res.data.rowdata[n + 1] &&
                                res.data.rowdata[n + 1][item] == res.data.rowdata[n][item]
                            ) {
                                rowSpan++;
                                n++;
                                res.data.rowdata[n].rowSpan = 0;
                            }
                            if (rowSpan) res.data.rowdata[i][item + "_rowSpan"] = rowSpan + 1;

                            if (!rowSpan) res.data.rowdata[i][item + "_rowSpan"] = 1;

                            i += rowSpan;
                        }
                    });
                    this.rows = !res.data.rowdata ? [] : res.data.rowdata;
                    let year = (new Date()).getFullYear();
                    let renderInputArr = ['productType', 'material','supplierType','materialNo','supplierCode','materialDemand'+year,'materialDemand'+(year+1),'materialSupply'+year,'materialSupply'+(year+1)]
                    this.getTreeLeaf(res.data.columndata,renderInputArr)
                    this.columns = res.data.columndata;
                    this.scrollWidth = 2160;
                    this.showDetail = true;
                    
                }else{
                    this.$message.error('错误提示：' + res.message,1)
                }
                this.close()
            }).catch((err) => {
                this.close()
                this.$message.error('错误提示：' + err.message,1)
            });
        },
        bodyCellSpan({ row, column, rowIndex }) {
            if (this.merges.includes(column.key)) {
                const _col = row[column.key + "_rowSpan"] > 0 ? 1 : 0;
                return {
                colspan: _col,
                rowspan: row[column.key + "_rowSpan"],
                };
            }
        },
    },
    computed: {
        ...mapGetters(['userInfo'])
    },
    created() {
        this.loadingInstance = this.$veLoading({
            target: document.querySelector("#loading-bom"),
            name: "flow",
        });
        this.callReportBomData();
    },
    destroyed() {
        this.loadingInstance.destroy();
    },
}
</script>

<style lang="less">
@import './vetable.less';
.head {
  border: 1px solid #97b1e7;
  border-bottom: none;
  background: #eaf0fa;
  text-align: center;
  padding: 10px 0;
  font-size: 20px;
}
.successdetail {
  background: #b4f7ac !important;
  color: #000;
}
.dangerdetail {
  background: #fc8585 !important;
  color: #000;
}
</style>