<template>
	<div class="container">
		<!-- 标题 start -->
		<div class="head-title">
			<div class="line mr10"></div>
			<span class="title">DPV测试失效登记</span>
		</div>
		<!-- 标题 end -->
		<!-- 筛选 start -->
		<div class="filter-wrapper mt5">
      <div class="filter-left">
        <div v-if="issueId==0" class="filter-block mr10">
          <a-input-search class="filter-input" @change="searchData" v-model="queryParam.productName" placeholder="请输入产品名称" />
        </div>

<!--        <div class="filter-block mr10">-->
<!--          <a-select mode="multiple" :maxTagCount="1" class="filter-select" @change="searchData" v-model="queryParam.problemDimensionList" placeholder="请选择问题维度">-->
<!--            <a-select-option v-for="(item,i) in this.problemDimensionList" :key="i" :value="parseInt(item.id)">{{ item.customvalue }}</a-select-option>-->
<!--          </a-select>-->
<!--        </div>-->



        <div class="filter-block mr10">
          <a-button style="" @click="resetSearch">重置</a-button>
        </div>
      </div>

			<div class="filter-right">
				<a-button type="primary" class="mr10" @click="addOpen">发起申请</a-button>
			</div>
		</div>
		<!-- 筛选 end -->
		<!-- 表格 start -->
		<div class="table-wrapper mt10">
			<a-table :columns="tableColumns"
               :data-source="tableData"
               :loading="searchLoading"
               :rowKey="(record) => record.id"
               bordered
               size="middle">
        <span slot="simpleText" slot-scope="text,record">
<!--          <clamp :text="text" :sourceText="text?text:'-'" :isCenter="true"></clamp>-->
          {{text?text:'-'}}
        </span>

        <span slot="clampText" slot-scope="text,record">
          <clamp :text="text" :sourceText="text?text.split(/[(\r\n)\r\n]+/):['-']" :isCenter="true" :key="new Date()"></clamp>
        </span>

        <span slot="productDepartment" slot-scope="text,record">
          {{text?(productDepartmentList.filter(e=>e.id==text).length>0?productDepartmentList.filter(e=>e.id==text)[0].customvalue:text):'-'}}
        </span>

        <span slot="testCate" slot-scope="text,record">
          {{text?testCateList.filter(e=>e.code==text)[0].value:'-'}}
        </span>

        <span slot="failureCate" slot-scope="text,record">
          {{text?failureCateList.filter(e=>e.code==text)[0].value:'-'}}
        </span>

        <span slot="reviewStatus" slot-scope="text,record">
          {{text?reviewStatusList.filter(e=>e.code==text)[0].value:'-'}}
        </span>

        <span slot="fileName" slot-scope="text,record">
          <a @click="previewFile(record)">{{ text }}</a>
        </span>

			</a-table>
		</div>
		<!-- 表格 end -->

    <!-- 弹窗选择待办推送人-->
    <div class="classmodal">
      <a-modal title="选择待办人" :width="600" :height="400" :visible="todoPushVisible"
               :confirmLoading="todoPushConfirmLoading" @ok="pushReviewTodo" @cancel="handleCancel">
        <a-spin :spinning="todoPushConfirmLoading">
          <a-form :form="form">
            <a-row :gutter="24">
              <a-col :md="20" :sm="24">
                <a-form-item label="待办推送人" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input type="hidden"
                           v-decorator="['userName', { rules: [{ required: true, message: '请选择待办推送人！' }] }]" />
                  <a-dropdown v-model="dropdownvisible" placement="bottomCenter" :trigger="['click']">
                    <a-button
                      style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;">{{
                        userNameDisplay ? userNameDisplay : "选择待办推送人" }}

                      <a-icon type="down" /></a-button>
                    <a-menu slot="overlay">
                      <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                        <a-input-search v-model="userQueryParam.searchValue" placeholder="搜索..." @change="todoOnSearch" />
                        <s-table style="width:100%;" ref="todoTablePeople" :rowKey="record => record.id"
                                 :columns="selectUserColumns" :data="userLoadData" :customRow="todoPushCustomRow"
                                 :scroll="{ y: 120, x: 120 }">>
                        </s-table>
                      </a-spin>
                    </a-menu>
                  </a-dropdown>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-spin>
      </a-modal>
    </div>

    <!-- 新建 start -->
    <add ref="addForm" @ok="handleOk" :productDepartmentList="productDepartmentList"/>

<!--    <edit ref="editForm" @ok="handleOk" :productList="productList" :problemStatusList="problemStatusList" :problemDimensionList="problemDimensionList"/>-->
    <!-- 新建 end -->
    <a-drawer :bodyStyle="{ height: '100%' }" placement="right" :closable="false" width="80%" :visible="filePreviewVisible" @close="filePreviewOnClose">
      <iframe :src="fileUrl"  width="100%" height="100%"></iframe>
    </a-drawer>

	</div>
</template>
<script>
import { clamp,STable } from '@/components'
import {getUserLists} from "@/api/modular/system/userManage";
// import {getJiraOptionList} from "@/api/modular/system/jiraCustomTool";
import Vue from "vue";
import {DICT_TYPE_TREE_DATA} from "@/store/mutation-types";
import moment from "moment";
import add from './add'
// import edit from './edit'
// import {
//   getProductProblemList,
//   getProductList,
//   updateProductProblem,
//   updateProblemReviewResult,
//   problemReviewResultToJira,
//   oaTodoPushProductProblem,
//   oaTodoDonePushProductProblem,
//   oaTodoCancelPushProductProblem
// } from "@/api/modular/system/productProblem";
import {getStageProblem} from "@/api/modular/system/report";
import {getDpvTestFailureList} from "@/api/modular/system/testFailure";
import {getJiraOptionList} from "@/api/modular/system/jiraCustomTool";

export default {
  components: {
    add,
    // edit,
    clamp,
    STable},
  props: {
    listType: {
      type: Number,
      default: 0
    },
    issueId: {
      type: Number,
      default: 0
    },
  },
	data() {
		return {
			tableHeight: document.documentElement.clientHeight - 40 - 20 - 24 - 10 - 32 - 20,

      filePreviewVisible: false,
      fileUrl:'',
      previewBaseUrl:process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id=',
      addVisible: false,
      addLoading: false,
      addForm: this.$form.createForm(this, { name: 'addForm' }),

      //{code:'动力圆柱电池研究所',value:'动力圆柱电池研究所'},{code:'方形电池研究所',value:'方形电池研究所'},{code:'新型电池研究所',value:'新型电池研究所'},{code:'V型圆柱电池研究所',value:'V型圆柱电池研究所'},{code:'动力电池研究所',value:'动力电池研究所'},{code:'储能电池研究所',value:'储能电池研究所'}
      productDepartmentList:[],
      orderTypeList:[{code:'G圆柱',value:'G圆柱'},{code:'C圆柱',value:'C圆柱'},{code:'方型',value:'方型'},{code:'软包',value:'软包'},{code:'V圆柱',value:'V圆柱'}],
      projectLevelList:[{code:'S',value:'S'},{code:'A',value:'A'},{code:'B',value:'B'},{code:'C',value:'C'}],
      researchStageList:[{code:'A样',value:'A样'},{code:'B样',value:'B样'},{code:'C样',value:'C样'}],
      testCateList:[{code:1,value:"电性能"},{code:2,value:"寿命"},{code:3,value:"安全"},{code:4,value:"其它"}],
      failureCateList:[{code:1,value:"不满足指标"},{code:2,value:"起火"},{code:3,value:"漏液"},{code:4,value:"壳体开裂"},{code:5,value:"其它"}],
      reviewStatusList:[{code:1,value:"审核中"},{code:2,value:"审核完成"}],

      reviewResultList:['请选择', '通过', '驳回'],
      loading:false,

      selectUserLoading: false,
      selectUserColumns: [{
        title: '账号',
        dataIndex: 'account'
      }, {
        title: '姓名',
        dataIndex: 'name'
      }],
      //提出人
      presenterVisible: false,
      userQueryParam: {},
      presenterName: '',
      userLoadData: parameter => {
        return getUserLists(Object.assign(parameter, this.userQueryParam)).then((res) => {
          return res.data
        })
      },

      //待办相关
      showTodoPushBtn: this.hasPerm("oaTodo:productProblem"),
      todoPushConfirmLoading: false,
      todoPushVisible: false,
      form: this.$form.createForm(this),
      dropdownvisible: false,
      userNameDisplay: "",


      queryParam: {
        listType:this.listType,
        issueId:this.issueId,
        productName: '',
        projectName: '',
        problemDimensionList: [],
        problemStatusList: [],
        // keyWord: '',
      },
      labelCol: {
        xs: {span: 24},
        sm: {span: 6}
      },
      wrapperCol: {
        xs: {span: 24},
        sm: {span: 18}
      },
      searchLoading: false,
      searchDataTimer: 0,
      problemDimensionList: [],
      problemStatusList: [],
      problemCateList: [],
      projectStageList: [],
      productList: [],
			tableColumns: [
        {
          title: '序号',
          dataIndex: 'index',
          key: 'index',
          align: 'center',
          width: 50,
          customRender: (text, record, index) => `${index+1}`,
        },
        {
          title: '电芯基本信息',
          children: [
            {
              title: '产品所属研究所',
              dataIndex: 'productDepartment',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'productDepartment' }
            },
            {
              title: '样品类型',
              dataIndex: 'orderType',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'orderType' }
            },
            {
              title: '产品名称',
              dataIndex: 'productName',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '项目等级',
              dataIndex: 'projectLevel',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'projectLevel' }
            },
            {
              title: '研制阶段',
              dataIndex: 'researchStage',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'researchStage' }
            },
            {
              title: '测试样品阶段',
              dataIndex: 'testSampleStage',
              align: 'center',
              width: 100,
              // scopedSlots: { customRender: 'testSampleStage' }
            },
          ],
        },
        {
          title: '电芯测试信息',
          children: [
            {
              title: '委托单号',
              dataIndex: 'folderNo',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '测试项目名称',
              dataIndex: 'testProjectName',
              align: 'center',
              width: 125,
              scopedSlots: { customRender: 'clampText' }
            },
            {
              title: '电芯编码',
              dataIndex: 'cellCode',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '电芯批次',
              dataIndex: 'cellBatch',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '测试类别',
              dataIndex: 'testCate',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'testCate' }
            },
            {
              title: '失效类别',
              dataIndex: 'failureCate',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'failureCate' }
            },
            {
              title: '测试失效描述',
              dataIndex: 'testFailureDescription',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'clampText' }
            },
          ],
        },
        {
          title: 'DPV测试失效告知书',
          children: [
            {
              title: '发起人',
              dataIndex: 'initiatorName',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '发起时间',
              dataIndex: 'initiationTime',
              align: 'center',
              width: 100,
            },
            {
              title: '文件编号',
              dataIndex: 'fileCode',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '附件',
              dataIndex: 'fileName',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'fileName' }
            },
          ],
        },
        {
          title: '审核状态',
          dataIndex: 'reviewStatus',
          align: 'center',
          width: 100,
          scopedSlots: { customRender: 'reviewStatus' }
        },
      ],
      tableData: [],
      initData: {},
		}
	},
	watch: {
		tableData(newVal, oldVal) {
      let subtrahend = this.tableData.length > 0 ? 45 : 0
      subtrahend = subtrahend + (this.issueId == 0 ? 0 : 32);
			document.documentElement.style.setProperty(`--height`, `${this.tableHeight - subtrahend}px`)
		}
	},
	created() {
    // this.getJiraOptionList();
    // console.log(this.listType);
    setTimeout(() => {
      this.getJiraOptionList();
      this.getData(this.queryParam);
    }, 200);
	},
	mounted() {
		let subtrahend = this.tableData.length > 0 ? 45 : 0
    subtrahend = subtrahend + (this.issueId == 0 ? 0 : 32);
		document.documentElement.style.setProperty(`--height`, `${this.tableHeight - subtrahend}px`)
	},
	methods: {

    addOpen() {
      this.$refs.addForm.add();
    },
    editOpen(record){
      // this.searchLoading = true;
      this.$refs.editForm.edit(record);
    },
    handleOk() {
      this.getData(this.queryParam);
    },
    filePreviewOnClose(){
      this.filePreviewVisible = false;
    },
    previewFile(record) {
      this.fileUrl = this.previewBaseUrl + record.fileId;
      //判断，pdf进行预览，非pdf触发下载
      console.log(this.fileUrl);
      this.filePreviewVisible = true;
    },

    // getJiraOptionList() {
    //   getJiraOptionList({fieldName:'problemDimension'}).then(res => {
    //     this.problemDimensionList = res.data;
    //   })
    //   getJiraOptionList({fieldName:'problemStatus'}).then(res => {
    //     this.problemStatusList = res.data;
    //   })
    //   // getJiraOptionList({fieldName:'problemCate'}).then(res => {
    //   //   this.problemCateList = res.data;
    //   // })
    //   getJiraOptionList({fieldName:'projectStage'}).then(res => {
    //     this.projectStageList = res.data;
    //   })
    //
    //   getProductList({productOrProject:1}).then(res => {
    //     if (res.success) {
    //       this.productList = res.data;
    //     } else {
    //       this.productList = [];
    //     }
    //   })
    // },
    resetSearch() {
      this.queryParam = {
        listType:this.listType,
        issueId:this.issueId,
        productName: '',
        projectName: '',
        problemDimensionList: [],
        problemStatusList: [],
        // keyWord: '',
      }
      this.searchData();
    },
    searchData() {
      console.log(this.queryParam);
      if (this.searchDataTimer === 0 ) {//首次调用，设置定时器
        this.searchDataTimer = setTimeout (() => {
          this.getData(this.queryParam)//  调用数据请求方法
        }, 600)
      } else {
        clearTimeout(this.searchDataTimer)//多次调用，取消定时器，重新设置
        this.searchDataTimer = setTimeout (() => {
          this.getData(this.queryParam)//  调用数据请求方法
        }, 600)
      }
    },
    selectProduct(value, label, extra) {
      var product = this.productList.filter(item => item.issueId == value)[0];
      this.addForm.setFieldsValue({
        productName : product.productName
      })
    },

    editReviewResult(id) {
      // console.log(id);
      const $i = this.tableData.findIndex(item => id === item.id);
      this.tableData[$i].editable = true
      // console.log(this.tableData);
    },

    selectReviewTodoUser() {
      this.todoPushVisible = true
    },
    pushReviewTodo() {
      const {
        form: { validateFields }
      } = this
      this.todoPushConfirmLoading = true
      validateFields((errors, values) => {
        if (!errors) {
          let $params = { ...values }
          oaTodoPushProductProblem($params)
            .then(res => {
              console.log(res);
              if (res.success) {
                if (res.data.result == "1") {
                  this.$message.success(res.data.message)
                  this.visible = false
                  this.todoPushConfirmLoading = false
                  this.handleCancel()
                } else if (res.data.result == "0") {
                  //弹出确认框
                  this.$confirm({
                    // iconClass: 'el-icon-question', //自定义图标样式
                    title: "提示",
                    content: res.data.message,
                    // confirmButtonText: '确认', //确认按钮文字更换
                    // cancelButtonText: '取消', //取消按钮文字更换
                    // showClose: true, //是否显示右上角关闭按钮
                    type: "warning", //提示类型  success/info/warning/error
                    onOk: () => {
                      $params = {
                        ...values,
                        repeatPush: 1
                      }
                      oaTodoPushProductProblem($params).then(res => {
                        if (res.success) {
                          this.$message.success(res.data.message)
                          this.visible = false
                          this.todoPushConfirmLoading = false
                          this.handleCancel()
                        } else {
                          this.$message.error("待办推送失败" + res.message)
                        }
                      })
                    }
                  })
                } else {
                  this.$message.error("待办推送失败：" + res.message)
                }
              } else {
                this.$message.error("待办推送失败：" + res.message)
              }
            })
            .finally(res => {
              this.todoPushConfirmLoading = false
            })
        } else {
          this.todoPushConfirmLoading = false
          this.todoPushVisible = false
        }
      })
    },
    doneReviewTodo() {
      oaTodoDonePushProductProblem({})
        .then(res => {
          console.log(res);
        })
        .finally(() => { })
    },
    cancelReviewTodo() {
      oaTodoCancelPushProductProblem({})
        .then(res => {
          console.log(res);
          if (res.success) {
            this.$message.success(res.data.message)
          } else {
            this.$message.error("待办取消失败：" + res.message)
          }
        })
        .finally(() => { })
    },
    //待办用户选择
    todoPushCustomRow(row, index) {
      return {
        on: {
          click: () => {
            this.form.setFieldsValue({
              userName: row.account
            })
            this.userNameDisplay = row.name
            this.dropdownvisible = false
          }
        }
      }
    },
    todoOnSearch(e) {
      this.$refs.todoTablePeople.refresh()
    },


    handleCancel() {
      this.selectvisible = false
      this.dropdownvisible = false
      this.userQueryParam.searchValue = null
      this.todoPushVisible = false
      this.form.setFieldsValue({
        userName: ""
      })
      this.userNameDisplay = ""
    },
    handleToJira(record) {
      if (record.issueKey == null) {
        return;
      }
      let $url = `http://jira.evebattery.com/browse/` + record.issueKey + `?auth=` + Vue.ls.get("jtoken");
      // let $url = `http://jira.evebattery.com/browse/` + record.issueKey;
      window.open($url, "_blank");
    },

    handleDetail() {
      // this.$router.push({path: "/problemDetail"})
      this.$router.push({
        path: "/problemDetail",
        query: {}
      })
      // system/productQualityPlatform/problemBoard/problemDetail
    },

    getData(queryParam) {
      this.searchLoading = true
      getDpvTestFailureList(queryParam).then(res => {
        this.tableData = res.data
      }).finally(res => {
        this.searchLoading = false
      })
    },
    getJiraOptionList(){
      getJiraOptionList({fieldName:'department'}).then(res => {
        var list=['18863','22101','18846','22105','18711','22269'];
        this.productDepartmentList = res.data.filter(e => list.includes(e.id));
        console.log(this.productDepartmentList);
        // this.productDepartmentList = res.data;
      })
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
  }
}
</script>
<style lang="less" scoped>
:root {
	--height: 600px;
}

/* 标题 */
.head-title {
	display: flex;
	align-items: center;
}
.head-title .line {
	width: 4px;
	height: 22px;
	background: #3293ff;
	border-radius: 20px;
}
.head-title .title {
	font-size: 16px;
	font-weight: 600;
}
/* 筛选 */
.filter-wrapper {
	display: flex;
	justify-content: space-between;
}
.filter-left {
	display: flex;
  margin-right: auto;
}
.filter-right {
	display: flex;
  margin-left: auto;
}
/* 表格 */
.table-wrapper {
	padding: 10px;
	background: #fff;
	border-radius: 10px;
}

.red {
  display: block;
  background: #ff3333;
  text-align: center;
  color: #fff;
}

.yellow {
  display: block;
  background: #fac858;
  text-align: center;
  color: #fff;
}

.green {
  display: block;
  background: #58a55c;
  text-align: center;
  color: #fff;
}

.btn_pn {
  display: block;
  min-height: 18px;
  min-width: 70px;
}

/deep/ .problemStatusSelect .ant-select-selection {
  background-color: rgba(255, 255, 255, 0);
  border: none;
}
.problem-status-show {
  justify-content: center;
  display: flex;
  align-items: center;
}
.problem-status-show .circle {
  width: 13px;
  height: 13px;
  border-radius: 50%;
  margin-right: 8px;
}

.select-box {
  display: flex;
  align-items: center;
}
.select-box .circle {
  width: 13px;
  height: 13px;
  border-radius: 50%;
  margin-right: 8px;
}

/* 通用  */

.status-lamp {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  margin: auto;
  flex-shrink:0;
}

.mr10 {
	margin-right: 10px;
}
.mt10 {
	margin-top: 10px;
}
.mt5 {
  margin-top: 5px;
}

.filter-select {
	width: 200px;
}
.filter-input {
	width: 175px;
}

/* 表格组件 */
/deep/ .ant-table tr th {
	background: #f4f4f4;
	font-size: 13px;

  padding: 8px 5px !important;
}

/deep/ .ant-table tr td {
  padding: 8px 5px !important;
}

/deep/.ant-table-body {
	height: var(--height) !important;
	overflow-y: scroll;
}

/deep/.ant-table-thead {
  position: sticky;
  top: 0;
  z-index: 10;
}
/deep/ .ant-pagination {
	margin: 10px 0 0;
}
/deep/ .ant-table-placeholder {
	border: none !important; 
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}
</style>
