<template>

  <a-modal title="传阅" :visible="true" :width="500" centered @cancel="handleCancel">
    <div class="share-modal">
      <div class="title">已传阅人员</div>
      <div class="action-content mt16">
        <a-input v-model="shareUserParam.userName" placeholder="请输入工号/姓名" allow-clear style="width: 200px"
          @change="filterShareUser">
          <a-icon slot="prefix" type="search" />
        </a-input>
        <a-button type="primary" style="display: ruby;" ghost @click="handleShowAddUser">新增传阅人员<a-icon
            type="plus" /></a-button>
      </div>

      <div class="mt16">
        <a-table :columns="shareUserColumns" :data-source="shareUserData" :rowKey="(record) => record.userAccount">
          <span slot="action" slot-scope="text, record">
            <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => shareUserDelete(record)">
              <a>删除</a>
            </a-popconfirm>
          </span>

        </a-table>
      </div>
    </div>

    <!-- 新增传阅弹窗 -->
    <a-modal title="新增传阅人员" :visible="isShowAddUser" :width="528" centered @cancel="handleCancel">
      <div class="share-modal">
        <div class="action-content">
          <a-input v-model="addUserParam.account" placeholder="请输入账号/姓名" allow-clear style="width: 200px"
            @change="getUserList">
            <a-icon slot="prefix" type="search" />
          </a-input>
          <treeselect v-model="addUserParam.grantOrgIdList" placeholder="请选择部门" value-consists-of="BRANCH_PRIORITY"
            :limit="1" :multiple="true" :max-width="270" :options="orgOptions" @input="getUserList" />
        </div>
        <div class="mt16">
          <a-table :columns="addUserColumns" :dataSource="addUserData" :rowKey="(record) => record.account"
            :rowSelection="{ selectedRowKeys: userSelectedRowKeys, onChange: onUserSelectChange }"
            :pagination="userPagination" @change="handleUserTableChange">
          </a-table>
        </div>
      </div>
      <template slot="footer">
        <a-button key="confirm" type="primary" @click="addShareUsers">
          <a>传阅</a>
        </a-button>
        <a-button key="back" @click="() => isShowAddUser = false">
          <a>取消传阅</a>
        </a-button>
      </template>
    </a-modal>

    <template slot="footer">
      <a-button key="back" @click="handleCancel">
        <a>关闭</a>
      </a-button>
    </template>
  </a-modal>
</template>
<script>
  import { getOrgTree } from "@/api/modular/system/orgManage";
  import { getUserPage } from "@/api/modular/system/userManage";
  import { getShareTemplateList,saveChartTemplate,deleteChartTemplate } from "@/api/modular/system/chartTemplate.js";
  import Treeselect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css"
  export default {
    props: {
      
      currentContent: {
        type: Object,
        default: () => {}
      },
      type: {
        type: String,
        default: 'chartTemplate'
      },
    },
    components: {
      Treeselect,
    },
    data() {
      return {
        isShowAddUser: false,
        shareUserParam: {},
        shareAllUserData: [],
        shareUserData: [],
        shareUserColumns: [
          {
            title: '账号',
            dataIndex: 'userAccount',
            align: 'center'
          },
          {
            title: '姓名',
            dataIndex: 'userName',
            align: 'center'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            scopedSlots: { customRender: 'action' }
          }],
        addUserParam: {
          grantOrgIdList: []
        },
        userPagination: {
          current: 1,
          pageSize: 10,
          total: 0,
          size: "small"
        },
        orgOptions: [],
        addUserData: [],
        userSelectedRows: [],
        userSelectedRowKeys: [],
        addUserColumns: [

          {
            title: '账号',
            dataIndex: 'account',
            align: 'center'
          },
          {
            title: '姓名',
            dataIndex: 'name',
            align: 'center'
          }
        ],
      }
    },
    created() {
      this.getShareUserList()
    },
    methods: {

      getShareUserList() {
        getShareTemplateList(this.currentContent.id).then(res => {
          this.shareAllUserData = res.data
          this.filterShareUser()  
        })
      },

      filterShareUser(){
        this.shareUserData = []
        const list = structuredClone(this.shareAllUserData)

        list.forEach(item => {
          if(item.userAccount.indexOf(this.shareUserParam.userName) !== -1 || item.userName.indexOf(this.shareUserParam.userName) !== -1 || !this.shareUserParam.userName){
            this.shareUserData.push(item)
          }
        })
      },

      getOrgList() {
        getOrgTree({}).then(res => {
          if (res.success) {
            this.orgOptions = []
            res.data[0].children.forEach(v => {
              let $item = {
                id: v.id,
                label: v.title
              }
              if (v.children.length !== 0) {
                $item.children = []
                v.children.forEach(chilV => {
                  $item.children.push({
                    id: chilV.id,
                    label: chilV.title
                  })
                })
              }
              this.orgOptions.push($item)
            })
          } else {
            this.$message.error('部门信息查询失败：' + res.message)
          }
        })
      },
      getUserList() {
        const params = {
          pageNo: this.userPagination.current,
          pageSize: this.userPagination.pageSize,
          searchValue: this.addUserParam.account || '',
          grantOrgIdList: this.addUserParam.grantOrgIdList.join(','),
        }

        getUserPage(params).then(res => {
          if (res.success) {
            this.userSelectedRows = []
            this.userSelectedRowKeys = []
            this.userPagination.total = res.data.totalRows

            this.addUserData = res.data.rows
            this.$forceUpdate()
          } else {
            this.$message.error('查询失败：' + res.message)
          }

        })

      },
      shareUserDelete(record){
        deleteChartTemplate(record.id).then(res => {
          this.$message.success('删除成功')
          this.getShareUserList()
        })
      },
      handleShowAddUser() {
        this.isShowAddUser = true
        this.getOrgList()
        this.getUserList()
      },
      onUserSelectChange(keys, rows) {
        this.userSelectedRowKeys = keys
        this.userSelectedRows = rows
      },
      handleUserTableChange(pagination, filters, sorter) {
        this.userPagination.current = pagination.current
        this.getUserList()
      },
      addShareUsers() {

        Promise.all(
          this.userSelectedRows.map(row => 
            saveChartTemplate({
              copyFromId: this.currentContent.id,
              userName: row.name,
              userAccount: row.account,
              templateName: this.currentContent.templateName,
              templateParamJson: this.currentContent.templateParamJson,
              originalParamJson: this.currentContent.originalParamJson
            })
          )
        ).then(() => {
              this.getShareUserList()
              this.isShowAddUser = false
              this.$message.success('模板传阅成功')
        }).catch(e => {
          this.$message.error('模板传阅失败，请联系系统管理员')
        })
      },
      handleCancel() {
        this.$emit('cancel')
      }
    }
  }
</script>

<style lang="less" scoped>
  .share-modal .title {
    font-size: 16px;
    font-weight: 600;
    text-align: center;
  }

  .share-modal .action-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .mt16 {
    margin-top: 16px;
  }

  /deep/.vue-treeselect {
    max-width: 270px;
  }

  /deep/.vue-treeselect--has-value .vue-treeselect__multi-value {
    margin-top: -3px;
    margin-bottom: 0;
  }

  /deep/.vue-treeselect__placeholder {
    line-height: 30px;
  }

  /deep/.share-modal .ant-table-tbody tr td,
  /deep/.share-modal .ant-table-thead tr th {
    padding: 8px !important;
  }
</style>