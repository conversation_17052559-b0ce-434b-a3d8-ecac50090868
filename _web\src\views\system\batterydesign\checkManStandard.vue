<template>
  <a-modal title="标准规范审批角色定义" width="80%" :visible="visible" :confirmLoading="confirmLoading" :footer="null"
           @cancel="handleCancel">


    <div style="display: flex">
      <div style="width: 50%">
        <div class="title">标准规范审批(惠州):</div>
        <div class="theme">审核:</div>
        <div class="check">
          <div class="select">
            <span class="xing">*</span><span class="label">部门经理:</span>
            <a-select
              show-search
              option-filter-prop="children"
              style="width: 60%"

              @search="(value) => handleSearch(value,'hzbmjlUserList')"
              @change="handleChange"
              v-model="checkMan.hzbmjl"
            >
              <a-select-option v-for="user in hzbmjlUserList" :key="user.account + '-' + user.name">
                {{user.account + '-' + user.name}}
              </a-select-option>
            </a-select>
          </div>
          <div class="select">
            <span class="xing">*</span><span class="label">总监:</span>
            <a-select
              show-search
              option-filter-prop="children"
              style="width: 60%"

              @search="(value) => handleSearch(value,'hzzjUserList')"
              @change="handleChange"
              v-model="checkMan.hzzj"
            >
              <a-select-option v-for="user in hzzjUserList" :key="user.account + '-' + user.name">
                {{user.account + '-' + user.name}}
              </a-select-option>
            </a-select>
          </div>


        </div>
        <div class="theme">批准:</div>
        <div class="approve">
          <div class="select">
            <span class="xing">*</span><span class="label">所长或指定负责人:</span>
            <a-select
              show-search
              option-filter-prop="children"
              style="width: 60%"

              @search="(value) => handleSearch(value,'hzszUserList')"
              @change="handleChange"
              v-model="checkMan.hzsz"
            >
              <a-select-option v-for="user in hzszUserList" :key="user.account + '-' + user.name">
                {{user.account + '-' + user.name}}
              </a-select-option>
            </a-select>
          </div>

        </div>

      </div>
      <div style="width: 50%">
        <div class="title">标准规范审批(湖北):</div>
        <div class="theme">审核:</div>
        <div class="check">
          <div class="select">
            <span class="xing">*</span><span class="label">部门经理:</span>
            <a-select
              show-search
              option-filter-prop="children"
              style="width: 60%"

              @search="(value) => handleSearch(value,'hbbmjlUserList')"
              @change="handleChange"
              v-model="checkMan.hbbmjl"
            >
              <a-select-option v-for="user in hbbmjlUserList" :key="user.account + '-' + user.name">
                {{user.account + '-' + user.name}}
              </a-select-option>
            </a-select>
          </div>
          <div class="select">
            <span class="xing">*</span><span class="label">总监:</span>
            <a-select
              show-search
              option-filter-prop="children"
              style="width: 60%"

              @search="(value) => handleSearch(value,'hbzjUserList')"
              @change="handleChange"
              v-model="checkMan.hbzj"
            >
              <a-select-option v-for="user in hbzjUserList" :key="user.account + '-' + user.name">
                {{user.account + '-' + user.name}}
              </a-select-option>
            </a-select>
          </div>


        </div>
        <div class="theme">批准:</div>
        <div class="approve">
          <div class="select">
            <span class="xing">*</span><span class="label">所长或指定负责人:</span>
            <a-select
              show-search
              option-filter-prop="children"
              style="width: 60%"

              @search="(value) => handleSearch(value,'hbszUserList')"
              @change="handleChange"
              v-model="checkMan.hbsz"
            >
              <a-select-option v-for="user in hbszUserList" :key="user.account + '-' + user.name">
                {{user.account + '-' + user.name}}
              </a-select-option>
            </a-select>
          </div>

        </div>

      </div>


    </div>


  </a-modal>
</template>

<script>
  import {
    designStandardCheckAddOrUpdate, getDesignStandardCheck
  } from '@/api/modular/system/batterydesignManage'


  import {
    getUserAllLists
  } from '@/api/modular/system/userManage'

  export default {
    data() {
      return {
        hzbmjlUserList: [],
        hzzjUserList: [],
        hzszUserList: [],
        hbbmjlUserList: [],
        hbzjUserList: [],
        hbszUserList: [],

        checkMan: {},
        record: {},
        visible: false,
        confirmLoading: false,
      }
    },

    methods: {
      handleChange() {
        this.$nextTick(() => {

          let param = {
            id: this.record.id,
            checkJson: JSON.stringify(this.checkMan),
            structureType: 'g_cylinder',
            dataType: 'mi'
          }
          if (this.checkMan.hzzj && this.checkMan.hzbmjl && this.checkMan.hzsz && this.checkMan.hbzj && this.checkMan.hbbmjl && this.checkMan.hbsz) {
            param.isDefine = 1
          }
          designStandardCheckAddOrUpdate(param)
        })
      },
      handleSearch(value, listName) {
        if (value != '' && value != null) {
          getUserAllLists({account: value}).then((res) => {
            this[listName] = res.data
          })
        }

      },

      open() {
        getDesignStandardCheck({structureType: 'g_cylinder', dataType: 'mi'}).then(res => {
          if (res.data != null) {
            this.record = res.data
            this.checkMan = res.data.checkJson ? JSON.parse(res.data.checkJson) : {}
          }
        })

        this.$nextTick(() => {
          if (this.checkMan.hzbmjl) {
            this.hzbmjlUserList.push({
              account: this.checkMan.hzbmjl.split('-')[0],
              name: this.checkMan.hzbmjl.split('-')[1]
            })
          }
          if (this.checkMan.hzzj) {
            this.hzzjUserList.push({account: this.checkMan.hzzj.split('-')[0], name: this.checkMan.hzzj.split('-')[1]})
          }
          if (this.checkMan.hzsz) {
            this.hzszUserList.push({account: this.checkMan.hzsz.split('-')[0], name: this.checkMan.hzsz.split('-')[1]})
          }
          if (this.checkMan.hbbmjl) {
            this.hbbmjlUserList.push({
              account: this.checkMan.hbbmjl.split('-')[0],
              name: this.checkMan.hbbmjl.split('-')[1]
            })
          }
          if (this.checkMan.hbzj) {
            this.hbzjUserList.push({account: this.checkMan.hbzj.split('-')[0], name: this.checkMan.hbzj.split('-')[1]})
          }
          if (this.checkMan.hbsz) {
            this.hbszUserList.push({account: this.checkMan.hbsz.split('-')[0], name: this.checkMan.hbsz.split('-')[1]})
          }

        })


        this.visible = true
      },
      handleCancel() {
        this.hzbmjlUserList = []
        this.hzzjUserList = []
        this.hzszUserList = []
        this.hbbmjlUserList = []
        this.hbzjUserList = []
        this.hbszUserList = []
        this.visible = false
      }
    }
  }
</script>
<style lang="less" scoped>
  .ant-form-item {
    margin-bottom: 0px;
  }

  .title {
    margin-bottom: 20px;
  }

  .xing {
    color: red;
    font-weight: bolder;
  }

  .label {
    width: 30%;
    display: inline-flex;
  }

  .theme {
    font-size: larger;
    font-weight: bolder;
    margin-bottom: 20px;
  }

  div {
    color: black;
  }

  span {
    color: black;
  }

  .select {
    margin-bottom: 3px;
  }

  .check {
    height: 100px;
  }

  .approve {
    height: 90px;
  }

  /deep/ .ant-modal-title {

    font-weight: bolder;
    font-size: 22px;
    color: #0049b0;
  }

</style>
