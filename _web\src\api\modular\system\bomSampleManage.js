
 import { axios } from '@/utils/request'

 
 export function getSamplePage (parameter) {
   return axios({
     url: '/bomSample/page',
     method: 'get',
     params: parameter
   })
 }
 
 
 export function sysSampleAdd (parameter) {
   return axios({
     url: '/bomSample/add',
     method: 'post',
     data: parameter
   })
 }
 
 export function sysSampleEdit (parameter) {
   return axios({
     url: '/bomSample/edit',
     method: 'post',
     data: parameter
   })
 }
 
 /**
  * 删除系统应用
  *
  * <AUTHOR>
  * @date 2020年7月9日15:05:01
  */
 export function sysSampleDelete (parameter) {
   return axios({
     url: '/bomSample/delete',
     method: 'get',
     params: parameter
   })
 }
 