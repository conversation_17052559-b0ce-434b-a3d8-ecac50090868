import { axios } from '@/utils/request'

export function testReportShareList(parameter) {
  return axios({
    url: '/testReportShare/list',
    method: 'post',
    data: parameter
  })
}

export function testReportShareAdd(parameter) {
  return axios({
    url: '/testReportShare/add',
    method: 'post',
    data: parameter
  })
}

export function testReportShareDelete(parameter) {
  return axios({
    url: '/testReportShare/delete',
    method: 'post',
    data: parameter
  })
}

export function testReportShareTop(parameter) {
  return axios({
    url: '/testReportShare/top',
    method: 'post',
    data: parameter
  })
}

export function testReportHistoryList(parameter) {
  return axios({
    url: '/testReportHistory/list',
    method: 'post',
    data: parameter
  })
}

export function testReportHistoryGet(parameter) {
  return axios({
    url: '/testReportHistory/get',
    method: 'post',
    data: parameter
  })
}