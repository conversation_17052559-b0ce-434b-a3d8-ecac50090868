<template>
	<div>
		<!-- head start -->
		<div style="height: 35px">
			<div style="float: left;width: 200px;margin: 10px 0px 0px 30px">
				<div class="block small-mark"></div>
				<div style="width: 120px;float: right;margin: 0px 20px 15px 0px;font-weight: bold;">已发生更改内容项</div>
			</div>
		</div>
		<!-- head end -->

		<!-- table start -->
		<div style="margin-bottom: 30px">
			<a-spin :spinning="isLoading">
				<a-table
					id="develop"
					:columns="columns"
					:data-source="resultData"
					:row-key="record => record.id"
					:pagination="false"
					bordered
				>
					<!-- 版本 -->
					<div slot="version" slot-scope="text, record">
						<a-tooltip placement="topLeft" arrow-point-at-center>
							<template slot="title" v-if="record.oriVersion !== undefined">
								{{ record.oriVersion }}
							</template>
							<input :value="record.version != null ? text : '/'" @change="updateData($event, record, 'version')" />
						</a-tooltip>
					</div>

					<!-- 更改内容 -->
					<div slot="changeContent" slot-scope="text, record">
						<a-tooltip placement="topLeft" arrow-point-at-center>
							<template slot="title" v-if="record.oriChangeContent !== undefined">
								{{ record.oriChangeContent }}
							</template>
							<a-textarea
								v-model="record.changeContent"
								:auto-size="{ minRows: 1, maxRows: 25 }"
								style="text-align: center"
								@blur="updateData($event, record, 'changeContent')"
							/>
						</a-tooltip>
					</div>

					<!-- 更改原因 -->
					<div slot="changeReason" slot-scope="text, record">
						<a-tooltip placement="topLeft" arrow-point-at-center>
							<template slot="title" v-if="record.oriChangeReason !== undefined">
								{{ record.oriChangeReason }}
							</template>
							<input
								:value="record.changeReason != null ? text : '/'"
								@change="updateData($event, record, 'changeReason')"
							/>
						</a-tooltip>
					</div>

					<!-- 日期 -->
					<div slot="changeDate" slot-scope="text, record">
						<a-tooltip placement="topLeft" arrow-point-at-center>
							<template slot="title" v-if="record.oriChangeDate !== undefined">
								{{ record.oriChangeDate }}
							</template>
							<input
								:value="record.changeDate != null ? text : '/'"
								@change="updateData($event, record, 'changeDate')"
							/>
						</a-tooltip>
					</div>

					<!-- 变更人  -->
					<div slot="changeBy" slot-scope="text, record">
						<a-tooltip placement="topLeft" arrow-point-at-center>
							<template slot="title" v-if="record.oriChangeBy !== undefined">
								{{ record.oriChangeBy }}
							</template>
							<input
								:value="record.changeBy != null ? text : '/'"
								style="width:80%"
								@change="updateData($event, record, 'changeBy')"
							/>
						</a-tooltip>
					</div>

					<!-- 表头 -->
					<template slot="title">
						<table>
							<tr>
								<td style="padding: 0;border: 0;width: 10.2%;"></td>
								<td style="padding: 0;border: 0;width: 10%;"></td>
								<td style="padding: 0;border: 0;width: 9%;"></td>
								<td style="padding: 0;border: 0;width: 7.8%;"></td>
								<td style="padding: 0;border: 0;width: 7.8%;"></td>
								<td style="padding: 0;border: 0;width: 7.8%;"></td>
								<td style="padding: 0;border: 0;width: 7.8%;"></td>
								<td style="padding: 0;border: 0;width: 8%;"></td>
								<td style="padding: 0;border: 0;width: 5%;"></td>
								<td style="padding: 0;border: 0;width: 9%;"></td>
								<td style="padding: 0;border: 0;width: 5%;"></td>
								<td style="padding: 0;border: 0;width: 8%;"></td>
								<td style="padding: 0;border: 0;width: 9%;"></td>
							</tr>
							<tr class="renderTr">
								<td colspan="13">
									<img src="/img/logo.53575418.png" alt="logo" style="float:left;width: 80px;height: 50px" />
								</td>
							</tr>
							<tr class="renderTr">
								<td style="color: black;font-weight: bold;">文件名称</td>
								<td colspan="6" id="render_documentName">
									<a-tooltip placement="topLeft" arrow-point-at-center>
										<template slot="title" v-if="libraryData.oriDocumentName !== undefined">
											{{ libraryData.oriDocumentName }}
										</template>
										<input
											:value="libraryData.documentName"
											
											@change="updateLibData($event, libraryData, 'documentName')"
										/>
									</a-tooltip>
								</td>
								<td style="color: black;font-weight: bold;">受控号</td>
								<td colspan="3" id="render_controlledNumber">
									<a-tooltip placement="topLeft" arrow-point-at-center>
										<template slot="title" v-if="libraryData.oriControlledNumber !== undefined">
											{{ libraryData.oriControlledNumber }}
										</template>
										<input
											:value="libraryData.controlledNumber"
											
											@change="updateLibData($event, libraryData, 'controlledNumber')"
										/>
									</a-tooltip>
								</td>
								<td style="color: black;font-weight: bold; ">页次</td>
								<td id="render_page">
									<a-tooltip placement="topLeft" arrow-point-at-center>
										<template slot="title" v-if="libraryData.oriPage !== undefined">
											{{ libraryData.oriPage }}
										</template>
										<input
											:value="libraryData.page"
											
											@change="updateLibData($event, libraryData, 'page')"
										/>
									</a-tooltip>
								</td>
							</tr>
							<tr class="renderTr" id="render_documentNo">
								<td style="color: black;font-weight: bold;">文件编号</td>
								<td colspan="6">
									<a-tooltip placement="topLeft" arrow-point-at-center>
										<template slot="title" v-if="libraryData.oriDocumentNo !== undefined">
											{{ libraryData.oriDocumentNo }}
										</template>
										<input
											:value="libraryData.documentNo"
											
											@change="updateLibData($event, libraryData, 'documentNo')"
										/>
									</a-tooltip>
								</td>
								<td style="color: black;font-weight: bold;">实施日期</td>
								<td colspan="3" id="render_implementDate">
									<a-tooltip placement="topLeft" arrow-point-at-center>
										<template slot="title" v-if="libraryData.oriImplementDate !== undefined">
											{{ libraryData.oriImplementDate }}
										</template>
										<input
											:value="libraryData.implementDate"
											
											@change="updateLibData($event, libraryData, 'implementDate')"
										/>
									</a-tooltip>
								</td>
								<td style="color: black;font-weight: bold;">版本</td>
								<td id="render_version">
									<a-tooltip placement="topLeft" arrow-point-at-center>
										<template slot="title" v-if="libraryData.oriVersion !== undefined">
											{{ libraryData.oriVersion }}
										</template>
										<input
											:value="libraryData.version"
											
											@change="updateLibData($event, libraryData, 'version')"
										/>
									</a-tooltip>
								</td>
							</tr>
							<tr class="renderTr">
								<td style="color: black;font-weight: bold;">文件状态</td>
								<td id="render_documentStatus0">
									<a-checkbox
										@change="getCheckBox(0)"
										:checked="libraryData.documentStatus == 0 ? true : false"
									></a-checkbox>
								</td>
								<td style="color: black;font-weight: bold;">制定</td>
								<td id="render_documentStatus1">
									<a-checkbox
										@change="getCheckBox(1)"
										:checked="libraryData.documentStatus == 1 ? true : false"
									></a-checkbox>
								</td>
								<td style="color: black;font-weight: bold;">修订</td>
								<td id="render_documentStatus2">
									<a-checkbox
										@change="getCheckBox(2)"
										:checked="libraryData.documentStatus == 2 ? true : false"
									></a-checkbox>
								</td>
								<td style="color: black;font-weight: bold;">作废</td>
								<td style="color: black;font-weight: bold;">文件等级</td>
								<td id="render_documentLevel1">
									<a-checkbox
										@change="getCheckBox(3)"
										:checked="libraryData.documentLevel == 1 ? true : false"
									></a-checkbox>
								</td>
								<td style="color: black;font-weight: bold;">管制</td>
								<td id="render_documentLevel0">
									<a-checkbox
										@change="getCheckBox(4)"
										:checked="libraryData.documentLevel == 0 ? true : false"
									></a-checkbox>
								</td>
								<td style="color: black;font-weight: bold;" colspan="2">非管制</td>
							</tr>
							<tr class="renderTr">
								<td colspan="13" style="font: bold normal 13px arial;height: 30px;border-bottom:0">
									文件变更履历
								</td>
							</tr>
						</table>
					</template>

					<!-- 页尾 -->
					<template slot="footer" slot-scope="currentPageData">
						<table>
							<tr>
								<td style="padding: 0;border: 0;width: 9.95%;"></td>
								<td style="padding: 0;border: 0;width: 16.67%;"></td>
								<td style="padding: 0;border: 0;width: 16.67%;"></td>
								<td style="padding: 0;border: 0;width: 16.67%;"></td>
								<td style="padding: 0;border: 0;width: 14%;"></td>
								<td style="padding: 0;border: 0;width: 13.02%;"></td>
								<td style="padding: 0;border: 0;width: 13.02%;"></td>
							</tr>
							<tr>
								<td colspan="13" style="height: 30px">
									<span style="float:left"
										>注：“更改原因”栏填制更改的证据。例①：技术变更，填写***变更，详见技术图纸编号***。例②：工艺变更，填写***变更，详见报告编号***。
										例③：物料变更，填写***变更，详见ECN编号***。例④：其它变更，如更改作业图片等，可直接在更改原因栏注明。</span
									>
								</td>
							</tr>
							<tr>
								<td>发放范围</td>
								<td id="render_distributionScope1">
									<input
										:value="libraryData.distributionScope1"
										@change="updateLibData($event, libraryData, 'distributionScope1')"
									/>
								</td>
								<td id="render_distributionScope2">
									<input
										:value="libraryData.distributionScope2"
										@change="updateLibData($event, libraryData, 'distributionScope2')"
									/>
								</td>
								<td id="render_distributionScope3">
									<input
										:value="libraryData.distributionScope3"
										@change="updateLibData($event, libraryData, 'distributionScope3')"
									/>
								</td>
								<td id="render_distributionScope4">
									<input
										:value="libraryData.distributionScope4"
										@change="updateLibData($event, libraryData, 'distributionScope4')"
									/>
								</td>
								<td id="render_distributionScope5">
									<input
										:value="libraryData.distributionScope5"
										@change="updateLibData($event, libraryData, 'distributionScope5')"
									/>
								</td>
								<td id="render_distributionScope6">
									<input
										:value="libraryData.distributionScope6"
										@change="updateLibData($event, libraryData, 'distributionScope6')"
									/>
								</td>
							</tr>
							<tr>
								<td>发放份数</td>
								<td id="render_distributionNum1">
									<input
										:value="libraryData.distributionNum1"
										@change="updateLibData($event, libraryData, 'distributionNum1')"
									/>
								</td>
								<td id="render_distributionNum2">
									<input
										:value="libraryData.distributionNum2"
										@change="updateLibData($event, libraryData, 'distributionNum2')"
									/>
								</td>
								<td id="render_distributionNum3">
									<input
										:value="libraryData.distributionNum3"
										@change="updateLibData($event, libraryData, 'distributionNum3')"
									/>
								</td>
								<td id="render_distributionNum4">
									<input
										:value="libraryData.distributionNum4"
										@change="updateLibData($event, libraryData, 'distributionNum4')"
									/>
								</td>
								<td id="render_distributionNum5">
									<input
										:value="libraryData.distributionNum5"
										@change="updateLibData($event, libraryData, 'distributionNum5')"
									/>
								</td>
								<td id="render_distributionNum6">
									<input
										:value="libraryData.distributionNum6"
										@change="updateLibData($event, libraryData, 'distributionNum6')"
									/>
								</td>
							</tr>
						</table>
					</template>
				</a-table>
				<p style="margin:5px 0px 0px 75px;font: bold normal 13px arial;color: black">表单编号：EP-IATF-16-45A</p>
			</a-spin>
		</div>
		<!-- table end -->
	</div>
</template>
<script>
import { getBatteryDesign } from "@/api/modular/system/batterydesignManage"
import {
	getMIChangeHistoryCurList,
	insertMIChangeHistoryCur,
	updateMIChangeHistoryCur
} from "@/api/modular/system/miChangeHistoryCurManage"
import { getMIStandardLibCurById, updateMIStandardLibCur } from "@/api/modular/system/gCylinderMILibCurManage"
import $ from "jquery"
import { getMIStandardByImpBatteryId, miOutputExportExcel } from "@/api/modular/system/gCylinderMILibManage"
import { EventBus } from "@/api/modular/system/eventBus"

export default {
	props: ["deliverImpBatteryId"],
	data() {
		return {
			// 表头
			columns: [
				{
					title: "版本",
					dataIndex: "version",
					scopedSlots: { customRender: "version" },
					align: "center",
					width: "10%"
				},
				{
					title: "更改内容",
					dataIndex: "changeContent",
					scopedSlots: { customRender: "changeContent" },
					align: "center",
					width: "50%"
				},
				{
					title: "更改原因",
					dataIndex: "changeReason",
					width: "14%",
					align: "center",
					scopedSlots: { customRender: "changeReason" }
				},
				{
					title: "日期",
					dataIndex: "changeDate",
					align: "center",
					width: "13%",
					scopedSlots: { customRender: "changeDate" }
				},
				{
					title: "变更人",
					dataIndex: "changeBy",
					align: "center",
					width: "13%",
					scopedSlots: { customRender: "changeBy" }
				}
			],
			visible: false,
			editVisible: false,
			confirmLoading: false,
			impBatteryId: null,
			impFBatteryId: null,
			libraryId: null,
			isLoading: false,
			mIStandardLibData: {},
			design: {},
			bigClient: document.documentElement.clientHeight > 700,
			windowHeight: document.documentElement.clientHeight - 200,
			form: this.$form.createForm(this, { name: "form" }),
			editForm: this.$form.createForm(this, { name: "editForm" }),
			resultData: [],
			libraryData: {},
			routes: [
				{
					path: "/batterydesignStandard",
					breadcrumbName: "标准库"
				},
				{
					path: "/g_cylinder_mi_library",
					breadcrumbName: "G圆柱 MI设计与组合"
				},
				{
					breadcrumbName: "xxx"
				}
			]
		}
	},
	created() {
		getMIStandardByImpBatteryId(this.deliverImpBatteryId).then(res => {
			if (res.data) {
				this.impBatteryId = this.deliverImpBatteryId
				if (!this.libraryId) {
					this.libraryId = res.data
				}
				this.getAndRenderLibData(this.libraryId, this.impBatteryId)
				this.getAndRenderDataByLibraryId(this.libraryId, this.impBatteryId)
			}
			EventBus.$on("changeHisEvent", libraryId => {
				this.libraryId = libraryId
				if (libraryId && this.impBatteryId) {
					this.getAndRenderLibData(libraryId, this.impBatteryId)
					this.getAndRenderDataByLibraryId(libraryId, this.impBatteryId)
				}
			})
		})
	},
	destroyed() {
		EventBus.$off("changeHisEvent", () => {})
	},
	mounted() {
		$(".ant-breadcrumb-separator")
			.eq(2)
			.css("display", "none")
		document.getElementsByClassName("ant-layout-content")[0].style.backgroundColor = "white"
	},
	methods: {
		getAndRenderLibData(id, impBatteryId) {
			getMIStandardLibCurById({ id: id }, impBatteryId).then(res => {
				const finallyData = res.data
				this.libraryData = finallyData.curData
				var curItem = res.data.curData
				var oriItem = res.data.impData
				this.libraryData.oriDocumentName =
					curItem.documentName !== oriItem.documentName ? oriItem.documentName : undefined
				this.libraryData.oriControlledNumber =
					curItem.controlledNumber !== oriItem.controlledNumber ? oriItem.controlledNumber : undefined
				this.libraryData.oriPage = curItem.page !== oriItem.page ? oriItem.page : undefined
				this.libraryData.oriDocumentNo = curItem.documentNo !== oriItem.documentNo ? oriItem.documentNo : undefined
				this.libraryData.oriImplementDate =
					curItem.implementDate !== oriItem.implementDate ? oriItem.implementDate : undefined
				this.libraryData.oriVersion = curItem.version !== oriItem.version ? oriItem.version : undefined
				this.renderLibData(finallyData.compareMap.diffList, finallyData.compareMap.sameList)
			})
		},
		renderChangeHisData(diff, same) {

			$(document).ready(function() {
				let $row = $("#develop")
					.find("table")
					.eq(1)
					.find("tbody tr")
				let rowSize = $row.length
				for (let i = 0; i < rowSize; i++) {
					let diffCol = diff[i] // 第i行中与原始列值不同的列名有哪些
					let sameCol = same[i]
					let $col = $row.eq(i).find("td")
					diffCol.forEach(function(item, i) {
						if (item === "version") {
							$col.eq(0).addClass("mark")
						} else if (item === "changeContent") {
							$col.eq(1).addClass("mark")
						} else if (item === "changeReason") {
							$col.eq(2).addClass("mark")
						} else if (item === "changeDate") {
							$col.eq(3).addClass("mark")
						} else if (item === "changeBy") {
							$col.eq(4).addClass("mark")
						}
					})
					sameCol.forEach(function(item, i) {
						if (item === "version") {
							$col.eq(0).removeClass("mark")
						} else if (item === "changeContent") {
							$col.eq(1).removeClass("mark")
						} else if (item === "changeReason") {
							$col.eq(2).removeClass("mark")
						} else if (item === "changeDate") {
							$col.eq(3).removeClass("mark")
						} else if (item === "changeBy") {
							$col.eq(4).removeClass("mark")
						}
					})
				}
			})
		},
		renderLibData(diff, same) {
			diff.forEach(item => {
				if (item === "documentStatus") {
					$("#render_documentStatus" + this.libraryData.documentStatus).addClass("mark")
					if (this.libraryData.documentStatus === 0) {
						$("#render_documentStatus1").removeClass("mark")
						$("#render_documentStatus2").removeClass("mark")
					} else if (this.libraryData.documentStatus === 1) {
						$("#render_documentStatus0").removeClass("mark")
						$("#render_documentStatus2").removeClass("mark")
					} else {
						$("#render_documentStatus0").removeClass("mark")
						$("#render_documentStatus1").removeClass("mark")
					}
				} else if (item === "documentLevel") {
					$("#render_documentLevel" + this.libraryData.documentLevel).addClass("mark")
					if (this.libraryData.documentLevel === 0) {
						$("#render_documentLevel1").removeClass("mark")
					} else {
						$("#render_documentLevel0").removeClass("mark")
					}
				} else {
					console.log(item)
					$("#render_" + item).addClass("mark")
				}
			})
			same.forEach(item => {
				if (item === "documentStatus") {
					$("#render_documentStatus0").removeClass("mark")
					$("#render_documentStatus1").removeClass("mark")
					$("#render_documentStatus2").removeClass("mark")
				} else if (item === "documentLevel") {
					$("#render_documentLevel0").removeClass("mark")
					$("#render_documentLevel1").removeClass("mark")
				} else {
					$("#render_" + item).removeClass("mark")
				}
			})
		},
		miExportTable() {
			miOutputExportExcel({ impBatteryId: this.impBatteryId, libraryId: this.libraryData.id }).then(res => {
				const fileName = "MI设计导出表.xlsx"
				if (!res) return
				const blob = new Blob([res.data], { type: "application/vnd.ms-excel" }) // 构造一个blob对象来处理数据，并设置文件类型
				if (window.navigator.msSaveOrOpenBlob) {
					//兼容IE10
					navigator.msSaveBlob(blob, fileName)
				} else {
					const href = URL.createObjectURL(blob) //创建新的URL表示指定的blob对象
					const a = document.createElement("a") //创建a标签
					a.style.display = "none"
					a.href = href // 指定下载链接
					a.download = fileName //指定下载文件名
					a.click() //触发下载
					URL.revokeObjectURL(a.href) //释放URL对象
				}
			})
		},
		miReImport() {
			this.$refs.miLibDataDialog.miChooseModelImport(this.impBatteryId, this.libraryId)
		},
		getCheckBox(option) {
			if (option === 0) {
				this.libraryData.documentStatus = 0
			} else if (option === 1) {
				this.libraryData.documentStatus = 1
			} else if (option === 2) {
				this.libraryData.documentStatus = 2
			} else if (option === 3) {
				this.libraryData.documentLevel = 1
			} else {
				this.libraryData.documentLevel = 0
			}
			let param = {}
			param["documentStatus"] = this.libraryData.documentStatus
			param["documentLevel"] = this.libraryData.documentLevel
			param["id"] = this.libraryData.id
			this.isLoading = true
			updateMIStandardLibCur(param).then(res => {
				this.$nextTick(() => {
					if (res.success) {
						this.$message.success("保存成功")
						this.getAndRenderLibData(this.libraryData.id, this.impBatteryId)
						setTimeout(() => {
							this.isLoading = false
						}, 500)
					} else {
						this.$message.error(res.message)
					}
				})
			})
		},
		updateData(event, record, column) {
			//修改时禁止输入
			let inputs = document.getElementsByTagName("input")
			let textareas = document.getElementsByTagName("textarea")
			let controlInput = []
			let controltextarea = []
			for (let i = 0; i < inputs.length; i++) {
				if (!inputs[i].disabled) {
					controlInput.push(inputs[i])
				}
			}
			for (let i = 0; i < textareas.length; i++) {
				if (!textareas[i].disabled) {
					controltextarea.push(textareas[i])
				}
			}
			// 重置
			for (let i = 0; i < controlInput.length; i++) {
				controlInput[i].disabled = true
			}
			for (let i = 0; i < controltextarea.length; i++) {
				controltextarea[i].disabled = true
			}
			let param = {}
			param[column] = event.target.value
			param["id"] = record.id
			this.isLoading = true
			updateMIChangeHistoryCur(param).then(res => {
				this.$nextTick(() => {
					if (res.success) {
						this.$message.success("保存成功")
						this.getAndRenderDataByLibraryId(this.libraryData.id, this.impBatteryId)
						setTimeout(() => {
							this.isLoading = false
						}, 500)
					} else {
						this.$message.error(res.message)
					}
					for (let i = 0; i < controlInput.length; i++) {
						controlInput[i].disabled = false
					}
					for (let i = 0; i < controltextarea.length; i++) {
						controltextarea[i].disabled = false
					}
				})
			})
		},

		updateLibData(event, record, column) {
			//修改时禁止输入
			let inputs = document.getElementsByTagName("input")
			let textareas = document.getElementsByTagName("textarea")

			let controlInput = []
			let controltextarea = []

			for (let i = 0; i < inputs.length; i++) {
				if (!inputs[i].disabled) {
					controlInput.push(inputs[i])
				}
			}
			for (let i = 0; i < textareas.length; i++) {
				if (!textareas[i].disabled) {
					controltextarea.push(textareas[i])
				}
			}
			for (let i = 0; i < controlInput.length; i++) {
				controlInput[i].disabled = true
			}
			for (let i = 0; i < controltextarea.length; i++) {
				controltextarea[i].disabled = true
			}
			let param = {}
			param[column] = event.target.value
			param["id"] = record.id
			this.isLoading = true
			updateMIStandardLibCur(param).then(res => {
				this.$nextTick(() => {
					if (res.success) {
						this.$message.success("保存成功")
						this.getAndRenderLibData(this.libraryData.id, this.impBatteryId)
						setTimeout(() => {
							this.isLoading = false
						}, 500)
					} else {
						this.$message.error(res.message)
					}
					for (let i = 0; i < controlInput.length; i++) {
						controlInput[i].disabled = false
					}
					for (let i = 0; i < controltextarea.length; i++) {
						controltextarea[i].disabled = false
					}
				})
			})
		},
		getAndRenderDataByLibraryId(libraryId, impBatteryId) {
			getMIChangeHistoryCurList({ libraryId: libraryId, status: 2 }, impBatteryId).then(res => {
				const finallyData = res.data
				this.resultData = finallyData.curData
				for (let i = 0; i < this.resultData.length; i++) {
					var curItem = finallyData.curData[i]
					var oriItem = finallyData.impData[i]
					this.resultData[i].oriVersion = curItem.version !== oriItem.version ? oriItem.version : undefined
					this.resultData[i].oriChangeContent =
						curItem.changeContent !== oriItem.changeContent ? oriItem.changeContent : undefined
					this.resultData[i].oriChangeReason =
						curItem.changeReason !== oriItem.changeReason ? oriItem.changeReason : undefined
					this.resultData[i].oriChangeDate = curItem.changeDate !== oriItem.changeDate ? oriItem.changeDate : undefined
					this.resultData[i].oriChangeBy = curItem.changeBy !== oriItem.changeBy ? oriItem.changeBy : undefined
				}
				this.renderChangeHisData(finallyData.compareMap.diffList, finallyData.compareMap.sameList)
			})
		}
	}
}
</script>
<style lang="less" scoped>
/deep/.exportButtonClass {
	font-family: SourceHanSansSC;
	font-weight: 400;
	font-size: 15px;
	color: rgba(0, 101, 255, 0.67);
}

/deep/.ant-table-thead > tr > th {
	background: white;
	padding: 12px 8px;
	font-weight: bold;
	border: 1px solid black;
}

/deep/.ant-table-bordered .ant-table-tbody > tr > td {
	border: 1px solid black;
}

/deep/.ant-table-bordered .ant-table-body > table {
	border-collapse: collapse;
	border-spacing: 0;
}

/deep/.ant-table-bordered .ant-table-header > table {
	border-collapse: collapse;
	border-spacing: 0;
}

/deep/.ant-table-thead > tr > th {
	border-bottom: 0;
	border-top: 0;
}

/deep/.ant-table-bordered .ant-table-title > table {
	border-collapse: collapse;
	border-spacing: 0;
}

/deep/.ant-table-bordered .ant-table-title > table > tr > td {
	border: 1px solid black;
}

/deep/.ant-table-bordered .ant-table-footer > table {
	border-collapse: collapse;
	border-spacing: 0px;
	margin-top: -2px;
}

/deep/.ant-table-bordered .ant-table-footer > table > tr > td {
	border: 1px solid black;
}

#develop {
	font-size: 12px;
	margin: 0px 30px 20px;
	color: #000;
}

textarea.ant-input {
	max-width: 100%;
	height: auto;
	min-height: 32px;
	line-height: 1.5;
	vertical-align: bottom;
	-webkit-transition: all 0.3s, height 0s;
	transition: all 0.3s, height 0s;
	border: none;
}
/deep/.ant-table-bordered.ant-table-empty .ant-table-placeholder {
	border: 1px solid black;
}

/deep/.ant-form-item {
	margin-bottom: 0px;
}

.renderTr td {
	border: #e8e8e8 solid 1px;
	text-align: center;
}

/deep/.ant-table.ant-table-bordered .ant-table-title {
	padding-right: 0px;
	padding-left: 0px;
	border: 1px solid #e8e8e8;
}

/deep/.ant-table-title {
	position: relative;
	padding: 0px;
	border-radius: 2px 2px 0 0;
}

/deep/.ant-table-footer {
	position: relative;
	padding: 0px;
	border-radius: 2px 2px 0 0;
	background-color: white;
}

/deep/.ant-table-footer tr td {
	border-right: #e8e8e8 solid 1px;
	border-top: #e8e8e8 solid 1px;
	text-align: center;
}

input {
	width: 100%;
	height: 25px;
	margin: 0;
	border: 0;
	outline: none;
	text-align: center;
}
.tab-title {
	padding: 0 10px;
}
div.tab-head div.active {
	font-size: 24px;
	font-weight: 700;
	color: rgba(0, 73, 176, 1);
	margin-bottom: -4px;
	cursor: text;
}

.block {
	width: 50px;
	height: 20px;
	float: left;
	border: 1px solid #000;
}

/deep/td {
	pointer-events: none;
}
</style>
