<template>
  <a-modal title="新增临时物料" :width="600" :visible="visible" :confirmLoading="confirmLoading" @ok="handleSubmit" @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">


        <a-form-item label="物料名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input placeholder="请输入物料名称" v-decorator="['partName', {rules: [{required: true, message: '请输入物料名称！'}]}]" />
        </a-form-item>
        <a-form-item label="物料规格" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input placeholder="请输入物料规格" v-decorator="['partDescription', {rules: [{required: true, message: '请输入物料规格！'}]}]" />
        </a-form-item>
        <a-form-item label="单位" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input placeholder="请输入单位" v-decorator="['partUnit', {rules: [{required: true, message: '请输入单位！'}]}]" />
        </a-form-item>

      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import {
    sysBatteryDesignAdd
  } from '@/api/modular/system/batterydesignManage'
  import moment from "moment";
  export default {
    props: {
      type: {
        type: String,
        default: ''
      },
    },
    data() {
      return {
        startDate:null,
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 8
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 14
          }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this),
      }
    },

    methods: {
      add() {
        this.visible = true
      },
      handleSubmit() {
        const {
          form: {
            validateFields
          }
        } = this

        this.confirmLoading = true
        validateFields((errors, values) => {

          if (!errors) {
            values.sapNumber = ""
            this.$emit("ok",values)
            this.confirmLoading = false
            this.visible = false
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel() {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
<style lang="less">
  .ant-form-item {

    margin-bottom: 0px;

  }
</style>
