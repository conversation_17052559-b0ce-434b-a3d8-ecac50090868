import { axios } from '@/utils/request'

export function getStatusNum(parameter) {
  return axios({
    url: '/testReport/submitStatus',
    method: 'post',
    data: parameter
  })
}

export function getSubmitStatus(parameter) {
    return axios({
      url: '/testReport/department/submitStatus',
      method: 'post',
      data: parameter
    })
  }

export function getDelayStatus(parameter) {
    return axios({
      url: '/testReport/delayStatus',
      method: 'post',
      data: parameter
    })
  }

  export function getReportList(parameter) {
    return axios({
      url: '/testReport/delay/list',
      method: 'post',
      data: parameter
    })
  }

// 日历寿命在线报告_修改容量数据
export function updateCalendarOnlineData(parameter, id) {
  return axios({
    url: `/testReportHistory/updateCalendarOnlineData/` + id,
    method: 'post',
    data: parameter
  })
}