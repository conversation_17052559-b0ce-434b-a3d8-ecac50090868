<template>
<div>
 <!--  <div class="title">{{projectdetail.productProjectName}}</div> -->
  <a-tabs  hide-add type="editable-card" v-model="activeKey" :default-active-key="1" tab-position="top" style="background: #fff;padding: 24px 4px" @edit="onEdit">
    <!-- <a-tab-pane v-if="hasPerm('productjira:detail')" key="1" tab="产品详情" :closable="false"><detailview :issueId="issueId" :projectdetail="projectdetail" :loading="loading" /></a-tab-pane> -->
    <!-- <a-tab-pane v-if="hasPerm('doc:list') && projectdetail.productOrProject == 1" key="2" tab="技术文档" :closable="false"><docs :issueId="issueId" /></a-tab-pane> -->
    <!-- <a-tab-pane v-if="hasPerm('productjira:supplier')" key="3" tab="产品工厂" :closable="false"><suppliers :issueId="issueId" /></a-tab-pane> -->
    <!-- <a-tab-pane key="4" tab="物料录入" :closable="false"><bomsuppliers :issueId="issueId" /></a-tab-pane> -->
    <!-- <a-tab-pane key="4" :closable="false" tab="产线管理"><werkline /></a-tab-pane> -->
    <a-tab-pane key="4" tab="包装BOM管理" :closable="false"><bompack :issueId="issueId" :projectdetail="projectdetail"  @onUpdate="onUpdate" :date ="now" /></a-tab-pane><!-- @onshow="onshow" :parts="parts"-->
    <!-- <a-tab-pane v-if="hasPerm('sysBom:list') && projectdetail.productOrProject == 1" key="5" tab="电芯BOM管理" :closable="false"><bommanage :issueId="issueId" :projectdetail="projectdetail"  @onUpdate="onUpdate" :date ="now" /></a-tab-pane> --><!--  @onshow="onshow" :parts="parts"-->
    <!-- <a-tab-pane v-if="hasPerm('sysBomEnd:page') && projectdetail.productOrProject == 1" key="7" tab="成品BOM列表" :closable="false"><endbom  :issueId="issueId" /></a-tab-pane> -->
    <a-tab-pane key="6" tab="BOM搭建" :closable="true" v-if="show"><bom :issueId="issueId" :projectdetail="projectdetail" :bomId="bomId" :date ="now" @onImport="onImport"  /></a-tab-pane>  <!-- @onSave="onSave" -->

    <!-- <a-tab-pane  key="8" tab="产品问题" :closable="false"><stagetrouble :issueId="issueId" :productCustomer="projectdetail.customer" /></a-tab-pane> 
    <a-tab-pane  key="9" tab="产品风险" :closable="false"><stagerisk :issueId="issueId" :productCustomer="projectdetail.customer" /></a-tab-pane>  -->
  </a-tabs>
</div>
</template>

<script>
/* import docs from '../techdoc/index'
import stagetrouble from '../stage/stagetrouble'
import stagerisk from '../risk/stagerisk' */
/* import suppliers from './suppliers' */
/* import detailview from './detailview' */
/* import bomsuppliers from './bomsuppliers' */
/* import bommanage from './bommanage' */
import bompack from './bompack'
/* import werkline from '../werkline/index' */
import bom from './bom'
/* import endbom from './endbom' */
import { getProjectDetail } from "@/api/modular/system/report"

export default {
    name:'product_detail',
    props: {
		issueId: {
			type: Number,
			default: 0
		},
		
		projectdetail: {
			type: Object,
			default: {}
		},
	},
    components: {
        //docs,
        //suppliers,
        //detailview,
        //bomsuppliers,
        //bommanage,
        bompack,
        bom,
        //werkline,
        //endbom,
        //stagetrouble,
        //stagerisk
    },
    data(){
        return {
            now:null,
            bomId:null,
            activeKey:'4',
            //issueId : 0,
            show:false,
            //projectdetail:{},
            loading:true,
            backActiveKey:''
        }
    },
    methods:{
        /* callPartList() {
				//this.vloading = true
				getPartList({
						flag: 0
					})
					.then((res) => {
						if (res.success) {
							this.parts = res.data.row
						} else {
							this.$message.error(res.message, 1);
						}
						//this.vloading = false
					})
					.catch((err) => {
						//this.vloading = false
						this.$message.error('错误提示：' + err.message, 1)
					});
			}, */
        onEdit(targetKey, action) {
            if (targetKey != '6') {
                return false;
            }
            this.show = !this.show
            if(!this.show){
                this.activeKey = this.backActiveKey
            }else{
                this.activeKey = targetKey
            }
            
        },
        /* onshow(targetKey,record){
            if (this.show) {
                this.$message.error('BOM在搭建，此次操作禁止')
                return false
            }
            this.bomId = null
            this.show = true
            this.activeKey = targetKey+""
        }, */
        onUpdate(targetKey,id,backKey){
            this.bomId = id
            this.show = true
            this.activeKey = targetKey+""
            this.backActiveKey = backKey+""
            this.now = new Date()
        },
        onImport(){
            this.now = new Date()
        },
        callProjectDetail(){
            this.loading = true
            let params = {issueId: this.issueId,title:''}
            getProjectDetail(params)
            .then((res)=>{
                if (res.result) {
                    this.projectdetail = res.data
                } else {
                    this.$message.error(res.message,1);
                }
                this.loading = false
                })
            .catch((err)=>{
                this.loading = false
                this.$message.error('错误提示：' + err.message,1)
            });
        },
    },
    created () {
        /* this.issueId = parseInt(this.$route.query.issueId)
        setTimeout(() => {
            this.activeKey = this.$route.query.activeKey ? this.$route.query.activeKey : '5'
        }, 100);
        this.callProjectDetail() */
    }
}
</script>

<style lang='less' scoped=''>
.title{
    background: #fff;
    padding: 15px 0;
    font-weight: 700;
    font-size: 16px;
    line-height: 1.5;
}
</style>