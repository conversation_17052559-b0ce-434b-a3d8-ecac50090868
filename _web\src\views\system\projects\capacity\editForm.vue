<template>
  <a-modal title="新增" :width="800" :height="600"
           :bodyStyle="{padding:0}"
           :visible="visible" :confirmLoading="confirmLoading" @ok="handleSubmit" style="padding: 0"
           :maskClosable="false"
           @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-row :gutter="24">
          <a-col :md="12" :sm="24">

            <a-form-item label="国家" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder="请输入国家"
                       v-decorator="['country', {rules: [{required: true, message: '请输入国家！'}]}]"/>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item label="省份" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder="请输入省份"
                       v-decorator="['province', {rules: [{required: true, message: '请输入省份！'}]}]"/>
            </a-form-item>
          </a-col>

        </a-row>

        <a-row :gutter="24">
          <a-col :md="12" :sm="24">
            <a-form-item label="城市" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder="请输入城市"
                       v-decorator="['city', {rules: [{required: true, message: '请输入城市！'}]}]"/>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item label="地区" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder="请输入地区"
                       v-decorator="['area', {rules: [{required: true, message: '请输入地区！'}]}]"/>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :md="12" :sm="24">
            <a-form-item label="产能区分" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder="请输入产能区分"
                       v-decorator="['capacitySplit', {rules: [{required: true, message: '请输入产能区分！'}]}]"/>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item label="工程区分" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input placeholder="请输入工程区分"
                       v-decorator="['projectSplit', {rules: [{required: true, message: '请输入工程区分！'}]}]"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24">
            <a-form-item label="分区" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input placeholder="请输入分区"
                       v-decorator="['split', {rules: [{required: true, message: '请输入分区！'}]}]"/>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item label="工厂代码" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input placeholder="请输入工厂代码"
                       v-decorator="['factoryCode', {rules: [{required: true, message: '请输入工厂代码！'}]}]"/>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :md="12" :sm="24">
            <a-form-item label="产线编号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input placeholder="请输入产线编号"
                       v-decorator="['lineCode', {rules: [{required: true, message: '请输入产线编号！'}]}]"/>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item label="产品类型" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input placeholder="请输入产品类型"
                       v-decorator="['productType', {rules: [{required: true, message: '请输入产品类型！'}]}]"/>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :md="12" :sm="24">
            <a-form-item label="定义产品" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input placeholder="请输入定义产品"
                       v-decorator="['product', {rules: [{required: true, message: '请输入定义产品！'}]}]"/>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item label="单线ppm" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input placeholder="请输入单线ppm"
                       v-decorator="['ppm', {rules: [{required: true, message: '请输入单线ppm！'}]}]"/>
            </a-form-item>
          </a-col>
        </a-row>


        <a-row :gutter="24">
          <a-col :md="12" :sm="24">
            <a-form-item label="计算产能" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input placeholder="请输入计算产能"
                       v-decorator="['countCapacity', {rules: [{required: true, message: '请输入计算产能！'}]}]"/>
            </a-form-item>

          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item label="标称产能" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input placeholder="请输入标称产能"
                       v-decorator="['standardCapacity', {rules: [{required: true, message: '请输入标称产能！'}]}]"/>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :md="12" :sm="24">


            <a-form-item label="预测PPAP" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input placeholder="请输入预测PPAP"
                       v-decorator="['planPpap', {rules: [{required: true, message: '请输入预测PPAP！'}]}]"/>
            </a-form-item>
          </a-col>

          <a-col :md="12" :sm="24">


            <a-form-item label="备注" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">

              <a-input placeholder="请输入备注"
                       v-decorator="['remark', {rules: [{required: true, message: '请输入备注！'}]}]"/>

            </a-form-item>
            <a-form-item style="display: none;" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['id']"/>
            </a-form-item>
          </a-col>

        </a-row>


      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import {
    editCapacity
  } from "@/api/modular/system/capacityManage"


  export default {

    data() {
      return {
        labelCol: {
          xs: {
            span: 12
          },
          sm: {
            span: 8
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 14
          }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this),
        record: {}
      }
    },

    methods: {


      ontestManSearch(e) {
        this.$refs.testManTable.refresh()
      },


      edit(record) {

        this.visible = true

        this.record = record
        setTimeout(() => {


          this.form.setFieldsValue(this.record)


        }, 100)
      },

      handleSubmit() {
        const {
          form: {
            validateFields
          }
        } = this

        this.confirmLoading = true
        validateFields((errors, values) => {

          if (!errors) {
            editCapacity(values).then((res) => {
              this.confirmLoading = false
              if (res.success) {
                this.$message.success('修改成功')
                this.handleCancel()
                this.$emit('ok')
              } else {
                this.$message.error('修改失败：' + res.message)
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }

        })
      },
      handleCancel() {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
<style lang="less" scoped>
  .ant-form-item {

    margin-bottom: 0px;

  }

  .man_button {
    padding-left: 11px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  /deep/ .ant-modal-body {
    padding: 0 !important;
  }

  /deep/ .ant-table-thead > tr > th, /deep/ .ant-table-tbody > tr > td {
    padding: 3px;
  }

  /deep/ .ant-table-footer {

    padding: 0px;
  }

  /deep/ .ant-table-pagination.ant-pagination {
    margin: 5px 0;
  }

  /deep/ .ant-input-number {
    width: 100%;
  }

  /deep/ .ant-input-number-sm > .ant-input-number-input-wrap > .ant-input-number-input {
    text-align: center;
  }

</style>
