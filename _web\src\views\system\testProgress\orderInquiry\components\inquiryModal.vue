<template>
	<a-modal
		title="委托单信息"
		:visible="true"
		width="90%"
		:centered="true"
		:cancel-button-props="{ style: { display: 'none' } }"
		okText="关闭"
		@cancel="handleModelCancel"
		@ok="handleModelCancel"
	>
		<div class="modal-wrapper">
			<div class="tabs-wrapper">
				<a-tabs type="card" :activeKey="activeKey" @change="handleTabsChange">
					<a-tab-pane v-for="(item, index) in tabsMenu" :key="index" :tab="item"> </a-tab-pane>
				</a-tabs>
			</div>
			<div class="tab-wrapper" v-if="activeKey === 0">
				<a-descriptions title="主题">
					<a-descriptions-item label="主题">
            {{folderInfo.theme}}
					</a-descriptions-item>
				</a-descriptions>
				<a-descriptions title="委托人信息">
					<a-descriptions-item label="委托人联系方式">
            {{folderInfo.contact}}
					</a-descriptions-item>
					<a-descriptions-item label="委托人">
            {{folderInfo.createdbyname}}
					</a-descriptions-item>
					<a-descriptions-item label="委托部门">
            {{ folderInfo.createdbyorgname }}
					</a-descriptions-item>
					<a-descriptions-item label="委托时间">
            {{ folderInfo.createdtime }}
					</a-descriptions-item>
					<a-descriptions-item label="委托日期">
            {{ folderInfo.entrusttime }}
					</a-descriptions-item>
				</a-descriptions>
				<a-descriptions title="基本信息">
					<a-descriptions-item label="委托单号">
            {{ folderInfo.folderno }}
					</a-descriptions-item>
					<a-descriptions-item label="检测实验室">
            {{ folderInfo.laboratory }}
					</a-descriptions-item>
					<a-descriptions-item label="成本中心">
            {{ folderInfo.costcenter }}
					</a-descriptions-item>
					<a-descriptions-item label="项目名称">
            {{ folderInfo.projectname }}
					</a-descriptions-item>
					<a-descriptions-item label="产品经理">
            {{ folderInfo.projectleader }}
					</a-descriptions-item>
					<a-descriptions-item label="项目负责人">
            {{ folderInfo.projectowner }}
					</a-descriptions-item>
					<a-descriptions-item label="测试费用/元">
            {{ folderInfo.estimatedcost }}
					</a-descriptions-item>
          <a-descriptions-item label="是否委外" v-if="folderInfo.laboratoryid!=='HZ_YJ_DL_AQ' && folderInfo.laboratoryid!=='HZ_YJ_DL_JM'">
            {{ folderInfo.outsourceflag === '0' ? '否' : '是'}}
          </a-descriptions-item>
          <a-descriptions-item label="是否跟单" v-if="folderInfo.laboratoryid==='HZ_YJ_DL_AQ'">
            {{ folderInfo.ordertrackingflag === '0' ? '否' : '是'}}
          </a-descriptions-item>
          <a-descriptions-item label="测试类别" v-if="folderInfo.laboratoryid!=='HZ_YJ_DL_JM'">
            {{ folderInfo.testtype ? folderInfo.testtype : ''}}{{(folderInfo.testpurposetype && folderInfo.testpurposetype !== '/') ? '/' + folderInfo.testpurposetype : ''}}
          </a-descriptions-item>
					<a-descriptions-item label="测试区域" v-if="folderInfo.laboratoryid!=='HZ_YJ_DL_AQ'">
            {{ folderInfo.testarea }}
					</a-descriptions-item>
					<a-descriptions-item label="送检总数">
            {{ folderInfo.samplenumber }}
					</a-descriptions-item>
          <a-descriptions-item label="单体规格" v-if="folderInfo.laboratoryid==='HZ_YJ_DL_JM'">
            {{ folderInfo.monomerspecific }}
          </a-descriptions-item>
					<a-descriptions-item label="送检数量单位">
            {{ folderInfo.samplenunit }}
					</a-descriptions-item>
          <a-descriptions-item label="是否加急" v-if="folderInfo.laboratoryid==='JM_YJ_DL_CS'">
            {{ folderInfo.reportflag === '1' ? '是' : '否' }}
          </a-descriptions-item>
					<a-descriptions-item label="样品类型" v-if="folderInfo.laboratoryid!=='HZ_YJ_DL_JM'">
            {{ transSampleType(folderInfo.sampletype) }}
					</a-descriptions-item>
          <a-descriptions-item label="样品类型" v-if="folderInfo.laboratoryid==='HZ_YJ_DL_JM'">
            {{ transSampleType(folderInfo.sampletype) }}
          </a-descriptions-item>
          <a-descriptions-item label="测试天数" v-if="folderInfo.laboratoryid!=='HZ_YJ_DL_AQ' && folderInfo.laboratoryid!=='HZ_YJ_DL_JM'">
            {{ folderInfo.testdays }}
          </a-descriptions-item>
          <a-descriptions-item label="产品属性" v-if="folderInfo.laboratoryid==='JM_YJ_DL_CS'">
            {{ folderInfo.productproperty }}
          </a-descriptions-item>
          <a-descriptions-item label="负极克容量" v-if="folderInfo.laboratoryid!=='HZ_YJ_DL_JM'">
            {{ folderInfo.negcap }} mAh/g
          </a-descriptions-item>
          <a-descriptions-item label="是否高风险" v-if="folderInfo.laboratoryid!=='HZ_YJ_DL_JM'">
            {{ folderInfo.ishighrisk }}
          </a-descriptions-item>
          <a-descriptions-item label="生产单号" v-if="folderInfo.laboratoryid!=='HZ_YJ_DL_JM'">
            {{ folderInfo.productno }}
          </a-descriptions-item>
          <a-descriptions-item label="测试目的" v-if="folderInfo.laboratoryid!=='HZ_YJ_DL_JM'">
            {{ folderInfo.testpurpose }}
          </a-descriptions-item>
					<a-descriptions-item label="测试依据" v-if="folderInfo.laboratoryid!=='HZ_YJ_DL_JM'">
            {{ folderInfo.matter }}
					</a-descriptions-item>
          <a-descriptions-item label="测试后样品处置" v-if="folderInfo.laboratoryid==='HZ_YJ_DL_JM'">
            {{ transSampleProcessOfJM(folderInfo.sampleprocess) }}
          </a-descriptions-item>
          <a-descriptions-item label="安全防护说明" v-if="folderInfo.laboratoryid==='HZ_YJ_DL_JM'">
            {{ folderInfo.safetymsg }}
          </a-descriptions-item>
          <a-descriptions-item label="备注">
            {{ folderInfo.remark }}
					</a-descriptions-item>
				</a-descriptions>
        <a-descriptions title="样品信息" v-if="folderInfo.laboratoryid!=='HZ_YJ_DL_JM'">
          <a-descriptions-item label="产品名称">
            {{ folderInfo.producttype }}
          </a-descriptions-item>
          <a-descriptions-item label="测试型号">
            {{ folderInfo.testproducttype }}
          </a-descriptions-item>
          <a-descriptions-item label="其它值" v-if="folderInfo.testproducttype==='other'">
            {{ folderInfo.otherfillvalue }}
          </a-descriptions-item>
          <a-descriptions-item label="产品技术状态">
            {{ folderInfo.technicalstatus }}
          </a-descriptions-item>
          <a-descriptions-item label="技术状态编号">
            {{ folderInfo.technicalstatusnumber }}
          </a-descriptions-item>
          <a-descriptions-item label="批号">
            {{ folderInfo.batchnumber }}
          </a-descriptions-item>
          <a-descriptions-item label="额定容量">
            {{ folderInfo.ratedcapacity }} Ah
          </a-descriptions-item>
          <a-descriptions-item label="电压范围" v-if="folderInfo.laboratoryid==='HZ_YJ_DL_AQ'">
            {{ folderInfo.voltagerange }} V
          </a-descriptions-item>
        </a-descriptions>
        <a-descriptions title="样品尺寸" v-if="folderInfo.laboratoryid!=='HZ_YJ_DL_JM'">
          <a-descriptions-item label="电芯载体">
            {{ folderInfo.cylindercellflag }}
          </a-descriptions-item>
          <a-descriptions-item label="直径" v-if="folderInfo.laboratoryid==='HZ_YJ_DL_AQ' && folderInfo.cylindercellflag && folderInfo.cylindercellflag.indexOf('圆柱') !== -1">
            {{ folderInfo.diameter }}  mm
          </a-descriptions-item>
          <a-descriptions-item label="高度" v-if="folderInfo.laboratoryid==='HZ_YJ_DL_AQ' && folderInfo.cylindercellflag && folderInfo.cylindercellflag.indexOf('圆柱') !== -1">
            {{ folderInfo.height }}  mm
          </a-descriptions-item>
          <a-descriptions-item label="长" v-if="folderInfo.laboratoryid==='HZ_YJ_DL_AQ' && folderInfo.cylindercellflag && folderInfo.cylindercellflag.indexOf('圆柱') === -1">
            {{ folderInfo.length }}  mm
          </a-descriptions-item>
          <a-descriptions-item label="宽" v-if="folderInfo.laboratoryid==='HZ_YJ_DL_AQ' && folderInfo.cylindercellflag && folderInfo.cylindercellflag.indexOf('圆柱') === -1">
            {{ folderInfo.width }}  mm
          </a-descriptions-item>
          <a-descriptions-item label="厚度" v-if="folderInfo.laboratoryid==='HZ_YJ_DL_AQ' && folderInfo.cylindercellflag && folderInfo.cylindercellflag.indexOf('圆柱') === -1">
            {{ folderInfo.height }}  mm
          </a-descriptions-item>
        </a-descriptions>
        <a-descriptions title="处置方式" v-if="folderInfo.laboratoryid==='HZ_YJ_DL_AQ'">
          <a-descriptions-item label="测试后样品处置">
            {{ folderInfo.sampleprocessofaq === 'scrap' ? '报废' : '自取' }}
          </a-descriptions-item>
        </a-descriptions>
			</div>
			<div class="tab-wrapper" v-if="activeKey === 1 && storeCategoryFlag">
        <a-button type="primary" v-if="!safetyTestFlag" style="margin: 5px 0px 10px 0px;" @click="exportHandleResult">导出处理结果</a-button>
        <a-button type="primary" v-if="!safetyTestFlag" style="margin: 5px 0px 10px 10px;" @click="exportSizeOriData">导出尺寸原始数据</a-button>
        <a-button type="primary" v-if="!safetyTestFlag" style="margin: 5px 0px 10px 10px;" @click="checkInspectionData">查看中检数据</a-button>
        <tableIndex
          :pageLevel='1'
          :pageTitleShow=false
          :paginationShow=false
          :loading='loading'
          :otherHeight="parseInt(400)"
        >
          <template #table>
            <ag-grid-vue :style="{height:tableHeight}"
                         class='table ag-theme-balham'
                         :tooltipShowDelay="0"
                         :columnDefs="columnsOfCalendar"
                         :rowData='testProjectTableData'
                         rowSelection="multiple"
                         :gridOptions="gridOptions"
                         @grid-ready="onGridReady"
                         :defaultColDef='defaultColDef'>
            </ag-grid-vue>
          </template>
        </tableIndex>
			</div>
      <div class="tab-wrapper" v-if="activeKey === 1 && !storeCategoryFlag">

        <tableIndex
          :pageLevel='1'
          :pageTitleShow=false
          :paginationShow=false
          :loading='loading'
          :otherHeight="parseInt(400)"
        >
          <template #table>
            <ag-grid-vue :style="{height:tableHeight}"
                         class='table ag-theme-balham'
                         :tooltipShowDelay="0"
                         :columnDefs="columns11"
                         :rowData='testProjectTableData'
                         rowSelection="multiple"
                         :gridOptions="gridOptions"
                         @grid-ready="onGridReady"
                         :defaultColDef='defaultColDef'>
            </ag-grid-vue>
          </template>
        </tableIndex>
      </div>
			<div class="tab-wrapper" v-if="activeKey === 2">
        <test-report-info :folderInfo="folderInfo" ref="testReportInfo"/>
			</div>
			<div class="tab-wrapper" v-if="activeKey === 3">
				<a-table :columns="columns1" :data-source="tableData">
					<span slot="no" slot-scope="text, record, index">
						<div class="blue" @click="handleNo(record)">{{ text }}</div>
					</span>
				</a-table>
			</div>
			<div class="tab-wrapper" v-if="activeKey === 4">
				<a-table :columns="columns2" :data-source="tableData">
					<span slot="no" slot-scope="text, record, index">
						<div class="blue" @click="handleNo(record)">{{ text }}</div>
					</span>
				</a-table>
			</div>
			<!-- <div class="tab-wrapper" v-if="activeKey === 5">
				<a-table :columns="columns3" :data-source="tableData">
					<span slot="no" slot-scope="text, record, index">
						<div class="blue" @click="handleNo(record)">{{ text }}</div>
					</span>
				</a-table>
			</div> -->
			<div class="tab-wrapper" v-if="activeKey === 5">
				<a-table :columns="columns4" :data-source="tableData">
					<span slot="no" slot-scope="text, record, index">
						<div class="blue" @click="handleNo(record)">{{ text }}</div>
					</span>
				</a-table>
				<a-divider>详情</a-divider>
				<a-descriptions title="中检">
					<a-descriptions-item label="测试编号">
						Zhou Maomao
					</a-descriptions-item>
					<a-descriptions-item label="测试数据">
						<a-button @click="isShowDetail = true" type="link">
							查看数据
						</a-button>
					</a-descriptions-item>
					<a-descriptions-item label="测试项目别名">
						1810000000
					</a-descriptions-item>
					<a-descriptions-item label="电压">
						Hangzhou, Zhejiang
					</a-descriptions-item>
					<a-descriptions-item label="内阻">
						empty
					</a-descriptions-item>
				</a-descriptions>
				<a-descriptions v-if="isShowDetail" title="测试数据">
					<a-descriptions-item label="委托单号">
						Zhou Maomao
					</a-descriptions-item>
					<a-descriptions-item label="主题">
						主题
					</a-descriptions-item>
					<a-descriptions-item label="样品编号">
						1810000000
					</a-descriptions-item>
					<a-descriptions-item label="测试项目编号">
						Hangzhou, Zhejiang
					</a-descriptions-item>
					<a-descriptions-item label="测试项名称">
						empty
					</a-descriptions-item>
					<a-descriptions-item label="测试项目别名">
						Zhou Maomao
					</a-descriptions-item>
					<a-descriptions-item label="测试编码">
						主题
					</a-descriptions-item>
					<a-descriptions-item label="数据位置">
						1810000000
					</a-descriptions-item>
					<a-descriptions-item label="开始时间">
						Hangzhou, Zhejiang
					</a-descriptions-item>
					<a-descriptions-item label="结束时间">
						empty
					</a-descriptions-item>
					<a-descriptions-item label="设备编号">
						Hangzhou, Zhejiang
					</a-descriptions-item>
					<a-descriptions-item label="通道编号">
						empty
					</a-descriptions-item>
				</a-descriptions>
				<a-descriptions title="数据">
					<a-descriptions-item label="测试编号">
						Zhou Maomao
					</a-descriptions-item>
					<a-descriptions-item label="测试项目别名">
						1810000000
					</a-descriptions-item>
					<a-descriptions-item label="中检后电压">
						Hangzhou, Zhejiang
					</a-descriptions-item>
					<a-descriptions-item label="中检后内阻">
						empty
					</a-descriptions-item>
				</a-descriptions>
				<a-descriptions title="进箱时间">
					<a-descriptions-item label="进箱子时间">
						Zhou Maomao
					</a-descriptions-item>
				</a-descriptions>
			</div>
		</div>
    <!-- 测试数据选择弹窗 start  -->
    <div>
      <a-modal
        title="中检数据"
        width="90%"
        :height="300"
        :bodyStyle="{ padding: 0 }"
        :visible="mgVisible"
        style="padding: 0"
        bordered
        :maskClosable="false"
        @cancel="handleCloseModal"
        destroyOnClose
      >
        <div class="child-table">
          <a-table
            :columns="mgColumns"
            :dataSource="mgData"
            class="mt10"
            bordered
            :rowKey="record => record.flowId"
          >
            <template slot="celltestcode" slot-scope="text, record, index, columns">
              <a @click="openStepData(record)" style="text-align: center">{{ text }}</a>
            </template>
          </a-table>
        </div>
        <template slot="footer" slot-scope="text, record">
          <a-button key="back" @click="handleCloseModal">
            关闭
          </a-button>
        </template>
      </a-modal>
    </div>
    <!-- 测试数据选择弹窗 end  -->
    <step-data ref="stepData"></step-data>
	</a-modal>
</template>

<script>
import {
  exportHandleResult,
  exportModel,
  exportSizeOriData,
  getTestProDetailByTaskId, batchValidExportSizeOriData, validExportSizeOriData, getOnlineReport, validSafetyTestExport
} from "@/api/modular/system/testProgressManager";
import {
  getFolderInfoByParam,
  getLimsOrdtaskListByParam,
  tLimsTestdataScheduleList
} from "@/api/modular/system/limsManager";
import { STable } from "@/components";
import { downloadfile1 } from "@/utils/util";
import moment from "moment/moment";
import stepData from "../../../lims/folder/stepData.vue"
import testReportInfo from "@/views/system/testProgress/orderInquiry/components/testReportInfo.vue";
import {earlyWarningCalendarCheck} from "@/api/modular/system/testAbnormalManager";

export default {
	name: "InquiryModal",
  components: {
    STable,
    testReportInfo,
    stepData,
    status: {
      template: `<div>{{params.onStatus(params.data.status)}}</div>`
    },
    chart: {
      template: `<div><a @click="params.onGetReport(params.data)">图表</a></div>`
    },
    equipment:{
      template:`
        <template v-if="params.data.equiptUsageRecords.length == 1">
            <a v-if="params.data.equiptUsageRecords[0].id != null" target="_blank" :href="'/lab-asset-detail?id='+params.data.equiptUsageRecords[0].id">{{params.data.equiptUsageRecords[0].testAlias}} - {{params.data.equiptUsageRecords[0].orderNo}}</a>
            <span v-else>{{params.data.equiptCodeAndName}}</span>
        </template>
        <template v-else-if="params.data.equiptUsageRecords.length > 1">
            <a-popover :title="null">
              <template slot="content">
                <p v-for="lab in params.data.equiptUsageRecords">
                <a target="_blank" v-if="lab.id != null" :href="'/lab-asset-detail?id='+lab.id">{{lab.testAlias}} - {{lab.orderNo}}</a>
                <span v-else>{{params.data.equiptCodeAndName}}</span>
                </p>
              </template>

              <a v-if="params.data.equiptUsageRecords[0].id != null" target="_blank" :href="'/lab-asset-detail?id='+params.data.equiptUsageRecords[0].id">{{params.data.equiptUsageRecords[0].testAlias}} - {{params.data.equiptUsageRecords[0].orderNo}}</a>
              <span v-else>{{params.data.equiptCodeAndName}}</span>
              <span>...</span>

            </a-popover>
        </template>
      `
    }

  },
	props: {
    width: {
      type: Number,
      default: 0
    },
    padding: {
      type: String,
      default: '8px'
    },
		modalData: {
			type: Object,
			default: {}
		}
	},
	data() {
		return {
      selectedRowKeys: [],
      selectedRows: [],
			activeKey: 0,
      loading: false,
      tableHeight: document.body.clientHeight - 405 - this.width + 'px' ,
      gridOptions: {
        onSelectionChanged: this.onSelectionChanged,
        suppressCellSelection: false
      },
      gridApi: null,
      columnApi: null,
      defaultColDef: {
        filter: false,
        floatingFilter: false,
        editable: false,
      },
      mgVisible: false,
			isShowDetail: false,
			tabsMenu: ["委托单信息", "测试项目", "检测报告"/*, "样品信息", "试验矩阵", "附件", "检测数据"*/],
      outQueryFlowRecord: {},
      mgData: [],
      mgColumns: [
        {
          title: "序号",
          align: "center",
          width: 50,
          customRender: (text, record, index) => {
            if (!record.isChild) {
              return index + 1
            }
          }
        },
        {
          title: "委托单号",
          dataIndex: "folderno",
          align: "center",
          width: 90
        },
        {
          title: "主题",
          dataIndex: "theme",
          align: "center",
          ellipsis: true,
          width: 90
        },
        {
          title: "样品编号",
          width: 90,
          align: "center",
          dataIndex: "orderno"
        },
        {
          title: "测试项目编码",
          width: 90,
          align: "center",
          dataIndex: "testcode"
        },
        {
          title: "测试项名称",
          width: 90,
          align: "center",
          dataIndex: "testname"
        },
        {
          title: "测试项目别名",
          width: 90,
          align: "center",
          dataIndex: "alias"
        },
        {
          title: "测试编码",
          width: 90,
          align: "center",
          dataIndex: "celltestcode",
          scopedSlots: { customRender: "celltestcode" }
        },
        {
          title: "数据位置",
          width: 60,
          align: "center",
          dataIndex: "dataPath",
          ellipsis: true
        },
        {
          title: "开始时间",
          width: 90,
          align: "center",
          dataIndex: "startTime",
          customRender: (text, record, index) => {
            if (null != text) {
              return moment(text).format("YYYY-MM-DD")
            }
            return text
          }
        },
        {
          title: "结束时间",
          width: 90,
          align: "center",
          dataIndex: "endTime",
          customRender: (text, record, index) => {
            if (null != text) {
              return moment(text).format("YYYY-MM-DD")
            }
            return text
          }
        },
        {
          title: "设备编号",
          width: 60,
          align: "center",
          dataIndex: "equiptcode"
        },
        {
          title: "通道编号",
          width: 60,
          align: "center",
          dataIndex: "channelno"
        }
      ],
			columns: [
				{
					title: "序号",
					dataIndex: "index",
					width: 60,
					customRender: (text, record, index) => `${index + 1}`
				},
				{
					title: "状态",
					dataIndex: "no",
					scopedSlots: { customRender: "no" }
				},
				{
					title: "样品编号",
					dataIndex: "people"
				},
				{
					title: "测试编码",
					dataIndex: "dept"
				},
				{
					title: "样品名称",
					dataIndex: "theme"
				},
				{
					title: "样品状态",
					dataIndex: "address"
				},
				{
					title: "样品类型",
					dataIndex: "model"
				},
				{
					title: "料号",
					dataIndex: "test"
				},
				{
					title: "批次号",
					dataIndex: "test"
				},
				{
					title: "备注",
					dataIndex: "test"
				}
			],
      columnsOfCalendar: [
        {
          width: 40,
          checkboxSelection: true,
          headerCheckboxSelection: true
        },
        {
          headerName: "",
          field: "chart",
          width: 50,
          cellRenderer: "chart",
          cellRendererParams: { onGetReport: this.getReport },
        },
        {
          headerName: '试验顺序',
          width: 80,
          field: 'sorter',
        },
        {
          headerName: "流程状态",
          field: "status",
          width: 120,
          cellRenderer: "status",
          cellRendererParams: { onStatus: this.statusValue },
        },
        {
          headerName: '测试项目别名',
          width: 120,
          field: 'alias',
          tooltipValueGetter: (p) => p.value,
        },{
          headerName: '测试员',
          width: 80,
          field: 'tester',
        }, {
          headerName: '温度(℃)',
          width: 110,
          field: 'tem',
        }, {
          headerName: '储存SOC(％)',
          width: 110,
          field: 'soc',
        }, {
          headerName: '测试设备',
          width: 220,
          field: 'equiptUsageRecords',
          cellRenderer: 'equipment'
        }, {
          headerName: '实际开始时间',
          width: 145,
          field: 'realstarttime',
        }, {
          headerName: '实际结束时间',
          width: 145,
          field: 'realendtime',
        },{
          headerName: '计划开始时间',
          width: 145,
          field: 'planstarttime',
        }, {
          headerName: '计划结束时间',
          width: 145,
          field: 'planendtime',
        }, {
          headerName: '检测内容',
          width: 200,
          field: 'testcontent',
          tooltipValueGetter: (p) => p.value,
        }, {
          headerName: '测试参数',
          width: 200,
          field: 'testparameter',
          tooltipValueGetter: (p) => p.value,
        }, {
          headerName: '检测时长/h',
          width: 100,
          field: 'testduration',
        }, {
          headerName: '测试费用/元',
          width: 100,
          field: 'price',
        }, {
          headerName: '一级分类',
          width: 80,
          field: 'firstcategory',
          tooltipValueGetter: (p) => p.value,
        }, {
          headerName: '二级分类',
          width: 80,
          field: 'secondcategory',
          tooltipValueGetter: (p) => p.value,
        }, {
          headerName: '测试项目编号',
          width: 120,
          field: 'testcode',
          tooltipValueGetter: (p) => p.value,
        }, {
          headerName: '测试项目名称',
          width: 120,
          field: 'testname',
          tooltipValueGetter: (p) => p.value,
        }
      ],
      columns11: [
        {
          width: 40,
          checkboxSelection: true,
          headerCheckboxSelection: true
        }, {
          headerName: '试验顺序',
          width: 80,
          field: 'sorter',
        },
        {
          headerName: "流程状态",
          field: "status",
          width: 120,
          cellRenderer: "status",
          cellRendererParams: { onStatus: this.statusValue },
        },
        {
          headerName: '一级分类',
          width: 80,
          field: 'firstcategory',
          tooltipValueGetter: (p) => p.value,
        }, {
          headerName: '二级分类',
          width: 80,
          field: 'secondcategory',
          tooltipValueGetter: (p) => p.value,
        }, {
          headerName: '测试员',
          width: 80,
          field: 'tester',
        }, {
          headerName: '参与人',
          width: 80,
          field: 'participator',
          tooltipValueGetter: (p) => p.value,
        }, {
          headerName: '测试项目编号',
          width: 120,
          field: 'testcode',
          tooltipValueGetter: (p) => p.value,
        }, {
          headerName: '测试项目名称',
          width: 120,
          field: 'testname',
          tooltipValueGetter: (p) => p.value,
        }, {
          headerName: '测试项目别名',
          width: 120,
          field: 'alias',
          tooltipValueGetter: (p) => p.value,
        }, {
          headerName: '标准编号',
          width: 120,
          field: 'methodcode',
          tooltipValueGetter: (p) => p.value,
        }, {
          headerName: '标准名称',
          width: 80,
          field: 'methodname',
          tooltipValueGetter: (p) => p.value,
        }, {
          headerName: '计划开始时间',
          width: 145,
          field: 'planstarttime',
        }, {
          headerName: '计划结束时间',
          width: 145,
          field: 'planendtime',
        }, {
          headerName: '实际开始时间',
          width: 145,
          field: 'realstarttime',
        }, {
          headerName: '实际结束时间',
          width: 145,
          field: 'realendtime',
        }, {
          headerName: '检测内容',
          width: 200,
          field: 'testcontent',
          tooltipValueGetter: (p) => p.value,
        }, {
          headerName: '测试参数',
          width: 200,
          field: 'testparameter',
          tooltipValueGetter: (p) => p.value,
        }, {
          headerName: '检测时长/h',
          width: 100,
          field: 'testduration',
        }, {
          headerName: '测试费用/元',
          width: 100,
          field: 'price',
        },
      ],
			columns1: [
				{
					title: "序号",
					dataIndex: "index",
					width: 60,
					customRender: (text, record, index) => `${index + 1}`
				},
				{
					title: "试验顺序",
					dataIndex: "no",
					scopedSlots: { customRender: "no" }
				},
				{
					title: "测试项目编号",
					dataIndex: "people"
				},
				{
					title: "测试项目别名",
					dataIndex: "dept"
				},
				{
					title: "标准编号",
					dataIndex: "theme"
				},
				{
					title: "标准名称",
					dataIndex: "address"
				},
				{
					title: "#1",
					dataIndex: "model"
				},
				{
					title: "#2",
					dataIndex: "test"
				}
			],
			columns2: [
				{
					title: "序号",
					dataIndex: "index",
					width: 60,
					customRender: (text, record, index) => `${index + 1}`
				},
				{
					title: "文件名称",
					dataIndex: "no",
					scopedSlots: { customRender: "no" }
				},
				{
					title: "上传人",
					dataIndex: "people"
				},
				{
					title: "上传时间",
					dataIndex: "dept"
				},
				{
					title: "操作",
					dataIndex: "theme"
				}
			],
			columns3: [
				{
					title: "序号",
					dataIndex: "index",
					width: 60,
					customRender: (text, record, index) => `${index + 1}`
				},
				{
					title: "样品编号",
					dataIndex: "no",
					scopedSlots: { customRender: "no" }
				},
				{
					title: "数据类型",
					dataIndex: "people"
				},
				{
					title: "分析项",
					dataIndex: "dept"
				},
				{
					title: "技术规格",
					dataIndex: "theme"
				},
				{
					title: "原始结果",
					dataIndex: "theme"
				},
				{
					title: "单位",
					dataIndex: "theme"
				},
				{
					title: "状态",
					dataIndex: "theme"
				},
				{
					title: "录入人员工号",
					dataIndex: "theme"
				}
			],
			columns3: [
				{
					title: "序号",
					dataIndex: "index",
					width: 60,
					customRender: (text, record, index) => `${index + 1}`
				},
				{
					title: "测试编码",
					dataIndex: "no",
					scopedSlots: { customRender: "no" }
				},
				{
					title: "测试项目别名",
					dataIndex: "people"
				},
				{
					title: "中检",
					dataIndex: "dept"
				},
				{
					title: "计划开始时间",
					dataIndex: "theme"
				},
				{
					title: "计划结束时间",
					dataIndex: "theme"
				},
				{
					title: "存储天数",
					dataIndex: "theme"
				},
				{
					title: "存储阶段",
					dataIndex: "theme"
				}
			],
			tableData: [],
			testProjectTableData: [],
			storeCategoryFlag: false,
			safetyTestFlag: false,
			folderInfo: {}
		}
	},
	created() {
    console.log('this.modalData',this.modalData)
    this.getFolderInfoByParam()
    this.getLimsOrdtaskListByParam()
    if (this.modalData.pageType && (this.modalData.pageType === 'taskAssignmentOfSafety' || this.modalData.pageType === 'taskAssignment')) {
      this.tabsMenu = this.tabsMenu.filter(o => o !== "测试项目")
    }
  },
	mounted() {},
	methods: {
    getReport(record) {
      if (this.safetyTestFlag) {
        this.checkSafetyTestReport(record)
      } else {
        this.checkCalendarReport(record)
      }
    },
    transSampleType(sampleType) {
      switch (sampleType) {
        case "jccl":
          return "基础材料";
        case "xcl":
          return "新材料";
        case "jgj":
          return "结构件";
        case "dx":
          return "电芯";
        case "mz":
          return "模组";
        case "dxlc":
          return "电芯-Live Cell";
        case "dxdc":
          return "电芯-Dummy Cell";
        default:
          return "";
      }
    },
    transSampleProcessOfJM(sampleType) {
      switch (sampleType) {
        case "scrap":
          return "报废";
        case "retrieve":
          return "自取";
        case "stay":
          return "自留";
        default:
          return "";
      }
    },
    statusValue(text){
      switch (text) {
        case "Result_return":
          return '退回'
        case "Draft":
          return '新建'
        case "Preschedule":
          return '待排程'
        case "Cfmschedule":
          return '排程确认'
        case "TaskAllocation":
          return '任务分配'
        case "Testassign":
          return '测试任务分配'
        case "Analysis":
          return '测试流程解析'
        case "TestInitiation":
          return '测试启动'
        case "TestExecution":
          return '测试执行'
        case "Overdue":
          return '超期处理'
        case "TestResultEntry":
          return '测试结果录入'
        case "TestResultReview":
          return '测试结果复核'
        case "TestCompleted":
          return '测试结束'
        case "SampleConfirmation":
          return '样品接收'
        case "Target":
          return '待测'
        case "Assign":
          return '任务分配'
        case "Result":
          return '结果录入'
        case "Examine":
          return '结果审核'
        case "Review":
          return '结果复核'
        case "Report":
          return '报告编制'
        case "Inreview":
          return '报告审核中'
        case "Done":
          return '完成'
        case "Outsource":
          return '任务委外'
        case "Outtestassign":
          return '委外测试任务分配'
        case "OutTestExecution":
          return '委外测试执行'
        case "Backfill":
          return '数据回填'
        case "Testreport":
          return '测试报告上传'
        case "Cancel":
          return '取消'
        case "OrdtaskUnderChange":
          return '委托变更中'
      }
    },
    handleCloseModal() {
      this.mgVisible = false
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    exportHandleResult() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      this.batchValidExportSizeOriData("handleResult").then(data => {
        if (data.success) {
          let testProgressList = data.data
          if (testProgressList.length > 0) {
            let testProgressIds = [];
            testProgressList.forEach(item => {
              testProgressIds.push(item.id)
            })
            exportHandleResult({ testProgressIds: testProgressIds }).then(res => {
              var fileName = this.modalData.folderno + '-' + this.modalData.createdbyname + '-' + '处理结果' + '.xlsx'
              if (res) {
                downloadfile1(res, fileName)
              }
            })
          } else {
            this.$message.warning('没有可导出的数据')
          }
        } else {
          this.$message.warning(data.message)
        }
      })
    },

    validExportSizeOriData(exportType) {
      return validExportSizeOriData({ordTaskId: this.selectedRows[0].id, exportType: exportType}).then(res => {
        return Promise.resolve(res);
      })
    },
    batchValidExportSizeOriData(exportType) {
      let ordTaskIds = []
      this.selectedRows.forEach(item => {
        ordTaskIds.push(item.id)
      })
      return batchValidExportSizeOriData({ordTaskIds: ordTaskIds, exportType: exportType}).then(res => {
        return Promise.resolve(res);
      })
    },
    checkSafetyTestReport(ordTaskBean) {
      const id = ordTaskBean.id;
      const alias = ordTaskBean.alias ? ordTaskBean.alias : "";
      validSafetyTestExport({ordTaskId: id, exportType: "handleResult"}).then(res => {
        if (res.success) {
          let safetyTestList = res.data.safetyTestList
          let safetyTestIds = (safetyTestList.map(v => v.id)).join(',')
          let videoFlag = safetyTestList.findIndex(v => v.videoFlag === '1') !== -1 ? 1 : 0
          let pictureFlag = safetyTestList.findIndex(v => v.pictureFlag === '1') !== -1 ? 1 : 0
          let attachmentFlag = safetyTestList.findIndex(v => v.attachmentFlag === '1') !== -1 ? 1 : 0
          // 有离线数据，通过res.data.resultDataJson判断是否有在线数据
          window.open("/v_report_preview?safetyTestIds=" + encodeURIComponent(safetyTestIds) + "&alias=" + encodeURIComponent(alias) + "&offFlag=1&onlineFlag=0"
            + '&videoFlag=' + videoFlag + '&pictureFlag=' + pictureFlag + '&attachmentFlag=' + attachmentFlag + "&type=安全测试", "_blank")
        } else {
          this.$message.warning("没有可查看的数据")
          // 没有离线数据，没有返回res.data.resultDataJson，获取testProgress进行判断
          // getOnlineReport({ ordtaskid: id }).then(res => {
          //   if (res.success) {
          //     res.data.resultDataJson ?
          //       window.open("/v_report_preview?id=" + id + "&alias=" + encodeURIComponent(alias) + "&offFlag=0&onlineFlag=1" + "&type=日历寿命", "_blank") // 需要展示在线报告
          //       : this.$message.warning("没有可查看的数据")
          //   } else {
          //     this.$message.warning(res.message.replace("导出", "查看"))
          //   }
          // })
        }
      })
    },
    checkCalendarReport (record) {
      const id = record.id;
      const alias = record.alias ? record.alias : "";
      validExportSizeOriData({ordTaskId: id, exportType: "handleResult"}).then(res => {
        if (res.success) {
          const videoFlag = res.data.videoFlag === '1' ? 1 : 0
          const pictureFlag = res.data.pictureFlag === '1' ? 1 : 0
          // 有离线数据，通过res.data.resultDataJson判断是否有在线数据
          window.open("/v_report_preview?testProgressId=" + res.data.id + "&alias=" + encodeURIComponent(alias) + "&offFlag=1&onlineFlag=" + (res.data.resultDataJson ? "1" : "0")
            + '&videoFlag=' + videoFlag + '&pictureFlag=' + pictureFlag + "&type=日历寿命", "_blank")
        } else {
          // 没有离线数据，没有返回res.data.resultDataJson，获取testProgress进行判断
          getOnlineReport({ ordtaskid: id }).then(res => {
            if (res.success) {
              res.data.resultDataJson ?
                window.open("/v_report_preview?id=" + id + "&alias=" + encodeURIComponent(alias) + "&offFlag=0&onlineFlag=1" + "&type=日历寿命", "_blank") // 需要展示在线报告
                : this.$message.warning("没有可查看的数据")
            } else {
              this.$message.warning(res.message.replace("导出", "查看"))
            }
          })
        }
      })
    },
    checkInspectionData() {
      if (this.selectedRows.length !== 1) {
        this.$message.warning('请选择一条数据')
        return
      }
      this.mgVisible = true
      console.log('this.selectedRows', this.selectedRows)
      tLimsTestdataScheduleList({ ordtaskid: this.selectedRows[0].id })
        .then(res => {
          if (res.success) {
            this.mgData = res.data
          } else {
            this.$message.error("查询失败：" + res.message)
          }
        })
    },
    exportSizeOriData() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      this.batchValidExportSizeOriData("sizeOriData").then(data => {
        if (data.success) {
          let testProgressList = data.data
          if (testProgressList.length > 0) {
            let testProgressIds = [];
            testProgressList.forEach(item => {
              testProgressIds.push(item.id)
            })
            exportSizeOriData({ testProgressIds: testProgressIds }).then(res => {
            var fileName = this.modalData.folderno + '-' + this.modalData.createdbyname + '-' + '尺寸原始数据' + '.xlsx'
              if (res) {
                downloadfile1(res, fileName)
              }
            })
          } else {
            this.$message.warning('没有可导出的尺寸数据')
          }
        } else {
          this.$message.warning(data.message)
        }
      })
    },

    getLimsOrdtaskListByParam() {
      let param = {}
      param.folderno = this.modalData.folderno
      getLimsOrdtaskListByParam(param).then(res => {
        this.testProjectTableData = res.data
        this.testProjectTableData.forEach(item => {
          if (((this.modalData.laboratoryid === 'HZ_YJ_DL_CS' || this.modalData.laboratoryid === 'JM_YJ_DL_CS') && item.firstcategory === '电性能' && item.secondcategory === '储存') ||
            (this.modalData.laboratoryid === 'HZ_YJ_DL_AQ')) {
            this.storeCategoryFlag = true
          }
          if (this.modalData.laboratoryid === 'HZ_YJ_DL_AQ') {
            if (!(item.firstcategory === '安全' && item.secondcategory === '储存')) {
              this.safetyTestFlag = true
            }
          }
        })
      })
    },

    getFolderInfoByParam() {
      let param = {}
      param.folderno = this.modalData.folderno
      getFolderInfoByParam(param).then(res => {
        this.folderInfo = res.data
        if (this.folderInfo.laboratoryid !== 'HZ_YJ_DL_JM') {
          this.columns11 = this.columns11.filter(item => item.field !== 'participator')
        }
        console.log('this.folderInfo',this.folderInfo)
      })
    },

    handleNo(record) {
      console.log('record',record)
      // this.modalData = record
    },

    onGridReady(params) {
      this.gridApi = params.api;
      this.columnApi = params.columnApi;
      // params.api.sizeColumnsToFit();
    },
    onSelectionChanged(event) {
      // 获取当前选中的行
      const selectedNodes = this.gridApi.getSelectedNodes();
      const selectedData = selectedNodes.map(node => node.data);
      // 更新选中的行数据
      this.selectedRows = selectedData;
      this.selectedRowKeys = selectedData;
    },

		handleTabsChange(key) {
			this.activeKey = key
		},

		getTestProDetailByTaskId() {
			this.modalLoading = true
			getTestProDetailByTaskId(this.modalData.ordTaskId)
				.then(res => {
					if (!res.success) return this.$message.error("错误提示：" + err.message)
					this.tableData = res.data
					// 去除不需要的字段
					this.tableData.lifeTestRecordDataMap.forEach(v => {
						delete v.heightType
					})
					this.middleCheck = this.tableData.lifeTestRecordDataMap[0].middleCheck
					this.actualInDate = this.tableData.actualInDate ? moment(this.tableData.actualInDate, "YYYY-MM-DD") : ""
				})
				.finally(() => {
					this.modalLoading = false
          earlyWarningCalendarCheck({id : this.modalData.id}).then(res => {
            this.abnormalList = res.data.abnormalList
            this.isAbnormal = res.data.isAbnormal
          })
				})
		},

		updateTestProDetail(params) {
			updateTestProDetail(params).then(res => {
				if (!res.success) return this.$message.error("错误提示：" + err.message)
			})
		},

		handleSelectRow(selectedRowKeys) {
			const temIndex = this.tableData.lifeTestRecordDataMap.findIndex(v => v.cellTestCode === selectedRowKeys[0])
			if (this.tableData.lifeTestRecordDataMap[temIndex]) {
				this.lifeTestData = [this.tableData.lifeTestRecordDataMap[temIndex]]
			} else {
				this.$message.info("暂无数据")
			}
			this.columns = []
			for (let i in this.lifeTestData[0]) {
				const temObj = {
					title: this.tableNameMenu[i],
					dataIndex: i,
					align: "center",
					scopedSlots: {
						customRender: i
					}
				}
				if (i === "middleCheck") continue

				if (i === "cellTestCode" || i === "alias") {
					this.columns.unshift(temObj)
				} else {
					this.columns.push(temObj)
				}
			}
		},

		handleInput() {
			const params = {
				id: this.modalData.ordTaskId,
				lifeTestRecordDataMap: this.tableData.lifeTestRecordDataMap
			}
			this.updateTestProDetail(params)
		},

		async handleStep(index) {
			if (this.modalData.taskStatus === "已完成") return (this.current += index)
			// 无中检，两步，有中检，三步
			if (this.current === 0 && this.middleCheck !== "normal") {
				const that = this
				this.$confirm({
					title: "是否完成中检",
					onOk() {
						that.current += index
					},
					onCancel() {},
					class: "test"
				})
				return
			}

			if (
				((this.current === 1 && this.middleCheck !== "normal") ||
					(this.current === 0 && this.middleCheck === "normal")) &&
				index === 1 &&
				!(await this._handleIsNull(this.tableData.lifeTestRecordDataMap))
			) {
				return this.$warning({
					content: "请将数据完整填写在进行下一步"
				})
			}
			this.current += index
		},

		handleChangeDate(date, dateString) {
			const params = {
				id: this.modalData.ordTaskId,
				actualInDate: dateString
			}
			this.updateTestProDetail(params)
		},

		async _handleIsNull(data) {
			let result = 0
			await data.forEach(v => {
				Reflect.ownKeys(v).forEach(e => {
					if (v[e] === null || v[e] === "") result++
				})
			})
			return result === 0
		},
		// 点击大小中检按钮,查找对应的数据
		chooseMgData(record) {
			//临时用（有数据）
			// 如果已经有checkData的数据，获取flowId
			if (record.checkData) this.flowId = JSON.parse(record.checkData).flowId

			// tLimsTestdataScheduleList({ celltestcode: "04QCE34221101HD152126077-202303090041-0001", alias: "日历寿命1" })
			// 	.then(res => {
			//正式情况
			tLimsTestdataScheduleList({ celltestcode: record.cellTestCode || String(new Date()), alias: record.alias })
				.then(res => {
					if (res.success) {
						this.mgData = res.data
					} else {
						this.$message.error("查询失败：" + res.message)
					}
				})
				.catch(err => {
					this.$message.error("查询失败：" + err.message)
				})
				.finally(res => {
					this.mgVisible = true
				})
		},
    openStepData(record) {
      this.outQueryFlowRecord = record
      if (record.flowId != null) {
        this.outQueryFlowRecord.flowId = record.flowId
        this.$refs.stepData.query(this.outQueryFlowRecord, false)
      } else {
        this.$message.warn("测试数据为空")
      }
    },
		// 选中数据
		selectTestData(record) {
			// this.tableData.lifeTestRecordDataMap[
			// 	this.tableData.lifeTestRecordDataMap.findIndex(v => v.sampleCode === "202308080005-0003")
			// ].checkData = JSON.stringify(record)
			//正式情况
			// 修改选中的数据，添加或修改checkData
			this.tableData.lifeTestRecordDataMap[
				this.tableData.lifeTestRecordDataMap.findIndex(v => record.celltestcode === v.cellTestCode)
			].checkData = JSON.stringify(record)
			const params = {
				id: this.modalData.ordTaskId,
				lifeTestRecordDataMap: this.tableData.lifeTestRecordDataMap
			}
			this.updateTestProDetail(params)
		},
		// 展示选择的数据
		getCheckboxProps(record) {
			if (!this.flowId) {
				return {
					props: {
						defaultChecked: false
					}
				}
			}
			return {
				props: {
					defaultChecked: record.flowId === this.flowId
				}
			}
		},
		// 单击复制
		handleCopy(text) {
			var input = document.createElement("input") // 创建input对象
			input.value = text // 设置复制内容
			document.body.appendChild(input) // 添加临时实例
			input.select() // 选择实例内容
			document.execCommand("Copy") // 执行复制
			document.body.removeChild(input) // 删除临时实例
			this.$message.success("复制成功！")
		},

		/**
		 * 弹窗事件
		 */
		handleModelCancel() {
			this.$emit("cancel")
		}
	}
}
</script>

<style lang="less" scoped>
.tab-wrapper {
	padding: 5px;
	border: 1px solid #e8e8e8;
	border-top: none;
	border-radius: 0 8px 8px 8px;
	overflow: scroll;
}

/* 组件 */
/deep/.ant-tabs-bar {
	margin: 0;
	border-radius: 8px;
}

/deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab {
	border-radius: 8px 8px 0 0;
	font-size: 15px;
}

// tabs块级颜色
/deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab {
	background-color: rgba(232, 232, 232, 0.5);
	margin-right: 4px;
}
/deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
	background-color: #fff;
}
/deep/.ant-modal-header {
	border: none;
}
/deep/.ant-descriptions-title {
	margin: 20px 0 0;
}

/* 表格 */
/deep/ .ant-table-body {
	min-height: 40vh !important;
	width: 100%;
	overflow-x: scroll;
}
/deep/ .ant-table-placeholder {
	border: none;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

/deep/.ant-modal-body{
  padding: 0 24px;
}
/deep/.ant-modal-footer {
  padding: 8px 24px;
}
</style>
