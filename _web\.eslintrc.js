module.exports = {
  root: true,
  env: {
    node: true
  },
  'extends': [
    'plugin:vue/strongly-recommended',
    '@vue/standard'
  ],
  rules: {
    'no-console': 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'generator-star-spacing': 0,
    'no-mixed-operators': 0,
    "no-unused-vars": 0,
    /* 'vue/max-attributes-per-line': [
      2,
      {
        'singleline': 5,
        'multiline': {
          'max': 1,
          'allowFirstLine': false
        }
      }
    ], */
    'comma-dangle':0,
    'vue/attribute-hyphenation': 0,
    'vue/html-self-closing': 0,
    'vue/component-name-in-template-casing': 0,
    'vue/html-closing-bracket-spacing': 0,
    'vue/singleline-html-element-content-newline': 0,
    'vue/no-unused-components': 0,
    'vue/multiline-html-element-content-newline': 0,
    'vue/no-use-v-if-with-v-for': 0,
    'vue/html-closing-bracket-newline': 0,
    'vue/no-parsing-error': 0,
    //'no-tabs': 0,
    /* 'quotes': [
      2,
      'single',
      {
        'avoidEscape': true,
        'allowTemplateLiterals': true
      }
    ], */
    'quotes': 0,
    'semi': 0,
    'comma-spacing':0,
    'eol-last':0,
    //'no-delete-var': 2,
    /* 'prefer-const': [
      2,
      {
        'ignoreReadBeforeAssign': false
      }
    ], */
    'spaced-comment':0,
    'eqeqeq':0,
    'template-curly-spacing': 0,
    'indent': 0,
    "space-before-function-paren": 0,
    //'no-multi-spaces': 2, //不能用多余的空格
  },
  parserOptions: {
    parser: 'babel-eslint'
  },
  overrides: [
    {
      files: [
        '**/__tests__/*.{j,t}s?(x)',
        '**/tests/unit/**/*.spec.{j,t}s?(x)'
      ],
      env: {
        jest: true
      }
    }
  ]
}
