{"version": 3, "sources": ["webpack://[name]/webpack/universalModuleDefinition", "webpack://[name]/webpack/bootstrap", "webpack://[name]/./sources/ext/quick_info.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "window", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "getContainer", "container", "gantt", "$task_data", "offsetHeight", "offsetWidth", "parent", "viewport", "$task", "$grid_data", "$grid", "$layout", "config", "quickinfo_buttons", "quick_info_detached", "show_quick_info", "attachEvent", "id", "setTimeout", "showQuickInfo", "events", "hiding_function", "_hideQuickInfo", "length", "clearQuickInfo", "hideQuickInfo", "_quick_info_box", "templates", "quick_info_title", "start", "end", "ev", "text", "substr", "quick_info_content", "details", "quick_info_date", "task_time", "quick_info_class", "task", "this", "_quick_info_box_id", "utils", "dom", "isChildOf", "document", "body", "pos", "_get_event_counter_part", "_init_quick_info", "_quick_info_task", "className", "_prepare_quick_info_classname", "_fill_quick_data", "_show_quick_info", "callEvent", "forced", "qi", "taskId", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "style", "right", "left", "event", "e", "keyCode", "offset", "nodeName", "toLowerCase", "append<PERSON><PERSON><PERSON>", "width", "height", "scrolls", "getScrollState", "screenWidth", "x", "Math", "min", "max", "dx", "top", "dy", "$root", "getTask", "css", "template", "start_date", "end_date", "_quick_info_readonly", "is<PERSON><PERSON><PERSON>ly", "createElement", "_wai<PERSON><PERSON>", "quickInfoAttr", "html", "ariaAttr", "quickInfoHeaderAttrString", "buttons", "is_editor", "icon_delete", "icon_edit", "quickInfoButtonAttrString", "locale", "labels", "innerHTML", "_qi_button_click", "target", "srcElement", "code", "which", "node", "box", "mask", "indexOf", "$click", "split", "replace", "domEv", "getTaskNode", "getTaskRowNode", "offsetTop", "offsetLeft", "offsetParent", "scroll", "$container", "y", "header", "content", "date", "titleContent", "<PERSON><PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "quickInfoHeader", "join"], "mappings": ";;;;;;;;;;;;CAAA,SAAAA,EAAAC,GACA,iBAAAC,SAAA,iBAAAC,OACAA,OAAAD,QAAAD,IACA,mBAAAG,eAAAC,IACAD,OAAA,gCAAAH,GACA,iBAAAC,QACAA,QAAA,8BAAAD,IAEAD,EAAA,8BAAAC,IARA,CASCK,OAAA,WACD,mBCTA,IAAAC,KAGA,SAAAC,EAAAC,GAGA,GAAAF,EAAAE,GACA,OAAAF,EAAAE,GAAAP,QAGA,IAAAC,EAAAI,EAAAE,IACAC,EAAAD,EACAE,GAAA,EACAT,YAUA,OANAU,EAAAH,GAAAI,KAAAV,EAAAD,QAAAC,IAAAD,QAAAM,GAGAL,EAAAQ,GAAA,EAGAR,EAAAD,QA0DA,OArDAM,EAAAM,EAAAF,EAGAJ,EAAAO,EAAAR,EAGAC,EAAAQ,EAAA,SAAAd,EAAAe,EAAAC,GACAV,EAAAW,EAAAjB,EAAAe,IACAG,OAAAC,eAAAnB,EAAAe,GAA0CK,YAAA,EAAAC,IAAAL,KAK1CV,EAAAgB,EAAA,SAAAtB,GACA,oBAAAuB,eAAAC,aACAN,OAAAC,eAAAnB,EAAAuB,OAAAC,aAAwDC,MAAA,WAExDP,OAAAC,eAAAnB,EAAA,cAAiDyB,OAAA,KAQjDnB,EAAAoB,EAAA,SAAAD,EAAAE,GAEA,GADA,EAAAA,IAAAF,EAAAnB,EAAAmB,IACA,EAAAE,EAAA,OAAAF,EACA,KAAAE,GAAA,iBAAAF,QAAAG,WAAA,OAAAH,EACA,IAAAI,EAAAX,OAAAY,OAAA,MAGA,GAFAxB,EAAAgB,EAAAO,GACAX,OAAAC,eAAAU,EAAA,WAAyCT,YAAA,EAAAK,UACzC,EAAAE,GAAA,iBAAAF,EAAA,QAAAM,KAAAN,EAAAnB,EAAAQ,EAAAe,EAAAE,EAAA,SAAAA,GAAgH,OAAAN,EAAAM,IAAqBC,KAAA,KAAAD,IACrI,OAAAF,GAIAvB,EAAA2B,EAAA,SAAAhC,GACA,IAAAe,EAAAf,KAAA2B,WACA,WAA2B,OAAA3B,EAAA,SAC3B,WAAiC,OAAAA,GAEjC,OADAK,EAAAQ,EAAAE,EAAA,IAAAA,GACAA,GAIAV,EAAAW,EAAA,SAAAiB,EAAAC,GAAsD,OAAAjB,OAAAkB,UAAAC,eAAA1B,KAAAuB,EAAAC,IAGtD7B,EAAAgC,EAAA,aAIAhC,IAAAiC,EAAA,yBCeA,SAAAC,IACA,IAAAC,EAAAC,MAAAC,WACA,OAAAF,KAAAG,cAAAH,EAAAI,aAEAC,OAAAL,EACAM,SAAAL,MAAAM,QAGAP,EAAAC,MAAAO,aACAR,EAAAG,cAAAH,EAAAI,aAEAC,OAAAL,EACAM,SAAAL,MAAAQ,QAKAJ,OAAAJ,MAAAS,QACAJ,SAAAL,MAAAS,SAnHAT,MAAAU,OAAAC,mBAAA,2BACAX,MAAAU,OAAAE,qBAAA,EACAZ,MAAAU,OAAAG,iBAAA,EAEAb,MAAAc,YAAA,uBAAAC,GAKA,OAJAC,WAAA,WACAhB,MAAAiB,cAAAF,IACE,IAEF,IAGA,WAMA,IALA,IAAAG,GAAA,gFACAC,EAAA,WAEA,OADAnB,MAAAoB,kBACA,GAEAtD,EAAA,EAAcA,EAAAoD,EAAAG,OAAiBvD,IAC/BkC,MAAAc,YAAAI,EAAApD,GAAAqD,GAPA,GAUA,WACA,SAAAG,IAGA,OAFAtB,MAAAuB,eAAA,GACAvB,MAAAwB,gBAAA,MACA,EAEAxB,MAAAc,YAAA,eAAAQ,GACAtB,MAAAc,YAAA,YAAAQ,GAPA,GAUAtB,MAAAyB,UAAAC,iBAAA,SAAAC,EAAAC,EAAAC,GAA4D,OAAAA,EAAAC,KAAAC,OAAA,OAC5D/B,MAAAyB,UAAAO,mBAAA,SAAAL,EAAAC,EAAAC,GAA8D,OAAAA,EAAAI,SAAAJ,EAAAC,MAC9D9B,MAAAyB,UAAAS,gBAAA,SAAAP,EAAAC,EAAAC,GACA,OAAA7B,MAAAyB,UAAAU,UAAAR,EAAAC,EAAAC,IAEA7B,MAAAyB,UAAAW,iBAAA,SAAAT,EAAAC,EAAAS,GAA8D,UAG9DrC,MAAAiB,cAAA,SAAAF,GACA,IACAA,GAAAuB,KAAAC,qBACAvC,MAAAwC,MAAAC,IAAAC,UAAAJ,KAAAd,gBAAAmB,SAAAC,QACAN,KAAA5B,OAAAG,gBAHA,CAOAyB,KAAAf,eAAA,GACA,IACAxB,EAAAD,IACA+C,EAAAP,KAAAQ,wBAAA/B,EAFA,EAEAhB,EAAAM,UAEAwC,IACAP,KAAAd,gBAAAc,KAAAS,iBAAAF,EAAA9B,GACAuB,KAAAU,iBAAAjC,EACAuB,KAAAd,gBAAAyB,UAAAjD,MAAAkD,8BAAAnC,GAEAuB,KAAAa,iBAAApC,GACAuB,KAAAc,iBAAAP,EAVA,GAWAP,KAAAe,UAAA,eAAAtC,OAGAf,MAAAoB,eAAA,WACApB,MAAAuB,iBAEAvB,MAAAuB,cAAA,SAAA+B,GACA,IAAAC,EAAAjB,KAAAd,gBACAc,KAAAC,mBAAA,EACA,IAAAiB,EAAAlB,KAAAU,iBAGA,GAFAV,KAAAU,iBAAA,KAEAO,KAAAE,WAAA,CAEA,GAAAzD,MAAAU,OAAAE,oBAEA,OADA0B,KAAAe,UAAA,oBAAAG,IACAD,EAAAE,WAAAC,YAAAH,GAGAA,EAAAN,WAAA,mBACA,QAAAM,EAAAI,MAAAC,MACAL,EAAAI,MAAAE,KAAA,SAEAN,EAAAI,MAAAC,MAAA,SAEAN,IACAC,EAAAI,MAAAE,KAAAN,EAAAI,MAAAC,MAAA,GACAL,EAAAE,WAAAC,YAAAH,IAEAjB,KAAAe,UAAA,oBAAAG,MAGAxD,MAAA8D,MAAApG,OAAA,mBAAAqG,GACA,IAAAA,EAAAC,SACAhE,MAAAuB,kBAyBAvB,MAAAoD,iBAAA,SAAAP,EAAAoB,GACA,IAAAV,EAAAvD,MAAAwB,gBACA,GAAAxB,MAAAU,OAAAE,oBAAA,CACA,IAAAb,EAAAD,IACAyD,EAAAE,YACA,sBAAAF,EAAAE,WAAAS,SAAAC,eACApE,EAAAK,OAAAgE,YAAAb,GACA,IAAAc,EAAAd,EAAApD,YACAmE,EAAAf,EAAArD,aAEAqE,EAAAvE,MAAAwE,iBAEAC,EADA1E,EAAAM,SACAF,YAAAoE,EAAAG,EAAAL,EAIAd,EAAAI,MAAAE,KAAAc,KAAAC,IAAAD,KAAAE,IAAAN,EAAAG,EAAA7B,EAAAgB,KAAAhB,EAAAiC,IAAAT,EAAAxB,EAAAwB,QAAAI,GAAA,KACAlB,EAAAI,MAAAoB,IAAAlC,EAAAkC,KAAAlC,EAAAmC,GAAAV,EAAAzB,EAAAyB,OAAA,EAAAL,EAAA,aAEAV,EAAAI,MAAAoB,IAAA,OACA,GAAAlC,EAAAiC,IACAvB,EAAAI,MAAAC,MAAA,OACAL,EAAAI,MAAAE,KAAA,SAEA7C,WAAA,WACAuC,EAAAI,MAAAE,KAAA,QACI,KAEJN,EAAAI,MAAAE,KAAA,OACAN,EAAAI,MAAAC,MAAA,SAEA5C,WAAA,WACAuC,EAAAI,MAAAC,MAAA,QACI,IAEJL,EAAAN,WAAA,iBAAAJ,EAAAiC,GAAA,gBACA9E,MAAAiF,MAAAb,YAAAb,IAGAvD,MAAAkD,8BAAA,SAAAnC,GACA,IAAAsB,EAAArC,MAAAkF,QAAAnE,GAEAoE,EAAA,uBACAC,EAAA9C,KAAAb,UAAAW,iBAAAC,EAAAgD,WAAAhD,EAAAiD,SAAAjD,GAKA,OAHA+C,IACAD,GAAA,IAAAC,GAEAD,GAGAnF,MAAA+C,iBAAA,SAAAF,EAAA9B,GACA,IAAAsB,EAAArC,MAAAkF,QAAAnE,GAUA,GATA,kBAAAuB,KAAAiD,sBACAjD,KAAAkD,WAAAnD,KAAAC,KAAAiD,uBACAvF,MAAAuB,eAAA,GACAe,KAAAd,gBAAA,MAIAc,KAAAiD,qBAAAjD,KAAAkD,WAAAnD,IAEAC,KAAAd,gBAAA,CACA,IAAA+B,EAAAjB,KAAAd,gBAAAmB,SAAA8C,cAAA,OAEAnD,KAAAoD,SAAAC,cAAApC,GAIA,IACAqC,EAAA,oCADAC,EAAA7F,MAAA0F,SAAAI,6BACA,kIAMAF,GAAA,sCAKA,IAJA,IAAAG,EAAA/F,MAAAU,OAAAC,kBAEAqF,GAAmBC,aAAA,EAAAC,WAAA,GAEnBpI,EAAA,EAAiBA,EAAAiI,EAAA1E,OAAoBvD,IACrC,IAAAwE,KAAAiD,uBAAAS,EAAAD,EAAAjI,IAAA,CAGA,IAAA+H,EAAA7F,MAAA0F,SAAAS,0BAAAnG,MAAAoG,OAAAC,OAAAN,EAAAjI,KAEA8H,GAAA,iCAAAG,EAAAjI,GAAA,YAAAkC,MAAAoG,OAAAC,OAAAN,EAAAjI,IAAA,KAAA+H,EAAA,gCAAAE,EAAAjI,GAAA,gBAAAkC,MAAAoG,OAAAC,OAAAN,EAAAjI,IAAA,eAEA8H,GAAA,SAEArC,EAAA+C,UAAAV,EAiBA,GAVA5F,MAAA8D,MAAAP,EAAA,QALA,SAAA1B,GACAA,KAAAiC,MACA9D,MAAAuG,iBAAA1E,EAAA2E,QAAA3E,EAAA4E,cAIAzG,MAAA8D,MAAAP,EAAA,oBAAAQ,GAEA,IAAA2C,GADA3C,KAAAD,OACA6C,OAAA7C,MAAAE,QACA,IAAA0C,GAAA,IAAAA,GACA1F,WAAA,WACAhB,MAAAuG,iBAAAxC,EAAAyC,QAAAzC,EAAA0C,aACK,KAGLzG,MAAAU,OAAAE,oBAAA,CACA,IAAAb,EAAAD,IACAE,MAAA8D,MAAA/D,EAAA,oBAAiDC,MAAAuB,mBAIjD,OAAAe,KAAAd,iBAGAxB,MAAAuG,iBAAA,SAAAK,GACA,IAAAC,EAAA7G,MAAAwB,gBACA,GAAAoF,MAAAC,EAAA,CAEA,IAAAC,EAAAF,EAAA3D,UACA,OAAA6D,EAAAC,QAAA,UACA,IAAAhG,EAAAf,MAAAuC,mBACAvC,MAAAgH,OAAAjB,QAAAe,EAAAG,MAAA,QAAAC,QAAA,aAAAnG,QAEAf,MAAAuG,iBAAAK,EAAAnD,cAEAzD,MAAA8C,wBAAA,SAAA/B,EAAAkD,EAAA5D,GACA,IAAA8G,EAAAnH,MAAAoH,YAAArG,GACA,IAAAoG,KACAA,EAAAnH,MAAAqH,eAAAtG,IAEA,YAGA,IAAA8C,EAAA,EACAkB,EAAAd,EAAAkD,EAAAG,UAAAH,EAAAjH,aAEA0G,EAAAO,EAEA,GAAA7E,KAAAE,MAAAC,IAAAC,UAAAkE,EAAAvG,GACA,KAAAuG,OAAAvG,GACAwD,GAAA+C,EAAAW,WACAX,IAAAY,aAIA,IAAAC,EAAAnF,KAAAkC,iBAEA,OAAAoC,GAIU/C,OAAAkB,MAAAD,GAHVjB,EAAAsD,EAAAhH,YAAA,EAAAsH,EAAA/C,EAAA1E,MAAA0H,WAAAvH,YAAA,MAGU6E,GAFVD,EAAAoC,EAAAjH,aAAA,EAAAuH,EAAAE,EAAA3H,MAAA0H,WAAAxH,aAAA,MAGAmE,MAAA8C,EAAAhH,YAAAmE,OAAA6C,EAAAjH,cAEA,MAGAF,MAAAmD,iBAAA,SAAApC,GACA,IAAAc,EAAA7B,MAAAkF,QAAAnE,GACAwC,EAAAvD,MAAAwB,gBAEAxB,MAAAuC,mBAAAxB,EAIA,IAAA6G,GACAC,QAAA7H,MAAAyB,UAAAC,iBAAAG,EAAAwD,WAAAxD,EAAAyD,SAAAzD,GACAiG,KAAA9H,MAAAyB,UAAAS,gBAAAL,EAAAwD,WAAAxD,EAAAyD,SAAAzD,IAEAkG,EAAAxE,EAAAyE,sBACAD,EAAAzB,UAAAsB,EAAAC,QACAE,EAAAE,YACA3B,UAAAsB,EAAAE,KAGA9H,MAAA0F,SAAAwC,gBAAA3E,GAAAqE,EAAAC,QAAAD,EAAAE,MAAAK,KAAA,MAGA5E,EAAAyE,WAAAC,YACA3B,UAAAtG,MAAAyB,UAAAO,mBAAAH,EAAAwD,WAAAxD,EAAAyD,SAAAzD", "file": "ext/dhtmlxgantt_quick_info.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"ext/dhtmlxgantt_quick_info\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ext/dhtmlxgantt_quick_info\"] = factory();\n\telse\n\t\troot[\"ext/dhtmlxgantt_quick_info\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/codebase/\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 225);\n", "gantt.config.quickinfo_buttons = [\"icon_delete\",\"icon_edit\"];\ngantt.config.quick_info_detached = true;\ngantt.config.show_quick_info = true;\n\ngantt.attachEvent(\"onTaskClick\", function(id){\n\tsetTimeout(function() {\n\t\tgantt.showQuickInfo(id);\n\t}, 0);\n\n\treturn true;\n});\n\n(function(){\n\tvar events = [\"onEmptyClick\", \"onViewChange\", \"onLightbox\", \"onBeforeTaskDelete\", \"onBeforeDrag\"];\n\tvar hiding_function = function(){\n\t\tgantt._hideQuickInfo();\n\t\treturn true;\n\t};\n\tfor (var i=0; i<events.length; i++)\n\t\tgantt.attachEvent(events[i], hiding_function);\n})();\n\n(function () {\n\tfunction clearQuickInfo() {\n\t\tgantt.hideQuickInfo(true);\n\t\tgantt._quick_info_box = null;\n\t\treturn true;\n\t}\n\tgantt.attachEvent(\"onGanttReady\", clearQuickInfo);\n\tgantt.attachEvent(\"onDestroy\", clearQuickInfo);\n})();\n\ngantt.templates.quick_info_title = function(start, end, ev){ return ev.text.substr(0,50); };\ngantt.templates.quick_info_content = function(start, end, ev){ return ev.details || ev.text; };\ngantt.templates.quick_info_date = function(start, end, ev){\n\t\treturn gantt.templates.task_time(start, end, ev);\n};\ngantt.templates.quick_info_class = function(start, end, task){ return \"\"; };\n\n\ngantt.showQuickInfo = function(id){\n\tif ((\n\t\tid == this._quick_info_box_id &&\n\t\tgantt.utils.dom.isChildOf(this._quick_info_box, document.body)\n\t) || !this.config.show_quick_info) {\n\t\t// not show if the quick info is already displayed for this task, or if it shouldn't be displayed\n\t\treturn;\n\t}\n\tthis.hideQuickInfo(true);\n\tvar offset = 6; // offset TASK <> QI-BOX in 'px'\n\tvar container = getContainer();\n\tvar pos = this._get_event_counter_part(id, offset, container.viewport);\n\n\tif (pos){\n\t\tthis._quick_info_box = this._init_quick_info(pos, id);\n\t\tthis._quick_info_task = id;\n\t\tthis._quick_info_box.className = gantt._prepare_quick_info_classname(id);\n\n\t\tthis._fill_quick_data(id);\n\t\tthis._show_quick_info(pos, offset);\n\t\tthis.callEvent(\"onQuickInfo\", [id]);\n\t}\n};\ngantt._hideQuickInfo = function(){\n\tgantt.hideQuickInfo();\n};\ngantt.hideQuickInfo = function(forced){\n\tvar qi = this._quick_info_box;\n\tthis._quick_info_box_id = 0;\n\tvar taskId = this._quick_info_task;\n\tthis._quick_info_task = null;\n\n\tif (qi && qi.parentNode){\n\n\t\tif (gantt.config.quick_info_detached) {\n\t\t\tthis.callEvent(\"onAfterQuickInfo\", [taskId]);\n\t\t\treturn qi.parentNode.removeChild(qi);\n\t\t}\n\n\t\tqi.className += \" gantt_qi_hidden\";\n\t\tif (qi.style.right == \"auto\")\n\t\t\tqi.style.left = \"-350px\";\n\t\telse\n\t\t\tqi.style.right = \"-350px\";\n\n\t\tif (forced) {\n\t\t\tqi.style.left = qi.style.right = \"\";\n\t\t\tqi.parentNode.removeChild(qi);\n\t\t}\n\t\tthis.callEvent(\"onAfterQuickInfo\", [taskId]);\n\t}\n};\ngantt.event(window, \"keydown\", function(e){\n\tif (e.keyCode == 27)\n\t\tgantt.hideQuickInfo();\n});\n\nfunction getContainer() {\n\tvar container = gantt.$task_data;\n\tif (container && container.offsetHeight && container.offsetWidth) {\n\t\treturn {\n\t\t\tparent: container,\n\t\t\tviewport: gantt.$task\n\t\t};\n\t}\n\tcontainer = gantt.$grid_data;\n\tif (container && container.offsetHeight && container.offsetWidth) {\n\t\treturn {\n\t\t\tparent: container,\n\t\t\tviewport: gantt.$grid\n\t\t};\n\t}\n\n\treturn {\n\t\tparent: gantt.$layout,\n\t\tviewport: gantt.$layout\n\t};\n}\n\ngantt._show_quick_info = function(pos, offset){\n\tvar qi = gantt._quick_info_box;\n\tif (gantt.config.quick_info_detached) {\n\t\tvar container = getContainer();\n\t\tif (!qi.parentNode ||\n\t\t\tqi.parentNode.nodeName.toLowerCase() == \"#document-fragment\")//IE8\n\t\t\tcontainer.parent.appendChild(qi);\n\t\tvar width = qi.offsetWidth;\n\t\tvar height = qi.offsetHeight;\n\n\t\tvar scrolls = gantt.getScrollState();\n\t\tvar viewPort = container.viewport;\n\t\tvar screenWidth = viewPort.offsetWidth + scrolls.x - width;\n\n\t\t//pos.dy = (pos.top + height - scrolls.y > (gantt._y - gantt.config.scale_height)*0.96) ? 1 : 0; // uncomment to show QI at the bottom of task always\n\n\t\tqi.style.left = Math.min(Math.max(scrolls.x, pos.left - pos.dx*(width - pos.width)), screenWidth) + \"px\";\n\t\tqi.style.top = pos.top - (pos.dy ? (height + pos.height + 2*offset) : 0) + \"px\";\n\t} else {\n\t\tqi.style.top = 20 + \"px\";\n\t\tif (pos.dx == 1){\n\t\t\tqi.style.right = \"auto\";\n\t\t\tqi.style.left = \"-300px\";\n\n\t\t\tsetTimeout(function(){\n\t\t\t\tqi.style.left = \"10px\";\n\t\t\t},1);\n\t\t} else {\n\t\t\tqi.style.left = \"auto\";\n\t\t\tqi.style.right = \"-300px\";\n\n\t\t\tsetTimeout(function(){\n\t\t\t\tqi.style.right = \"10px\";\n\t\t\t},1);\n\t\t}\n\t\tqi.className += \" gantt_qi_\"+(pos.dx == 1 ? \"left\" : \"right\");\n\t\tgantt.$root.appendChild(qi);\n\t}\n};\ngantt._prepare_quick_info_classname = function(id){\n\tvar task = gantt.getTask(id);\n\n\tvar css = \"gantt_cal_quick_info\",\n\t\ttemplate = this.templates.quick_info_class(task.start_date, task.end_date, task);\n\n\tif(template){\n\t\tcss += \" \" + template;\n\t}\n\treturn css;\n};\n\ngantt._init_quick_info = function(pos, id){\n\tvar task = gantt.getTask(id);\n\tif(typeof this._quick_info_readonly == \"boolean\"){\n\t\tif(this.isReadonly(task) !== this._quick_info_readonly){\n\t\t\tgantt.hideQuickInfo(true);\n\t\t\tthis._quick_info_box = null;\n\t\t}\n\t}\n\n\tthis._quick_info_readonly = this.isReadonly(task);\n\n\tif (!this._quick_info_box){\n\t\tvar qi = this._quick_info_box = document.createElement(\"div\");\n\n\t\tthis._waiAria.quickInfoAttr(qi);\n\n\n\t//title\n\t\tvar ariaAttr = gantt._waiAria.quickInfoHeaderAttrString();\n\t\tvar html = \"<div class=\\\"gantt_cal_qi_title\\\" \"+ariaAttr+\">\" +\n\t\t\t\"<div class=\\\"gantt_cal_qi_tcontent\\\"></div><div  class=\\\"gantt_cal_qi_tdate\\\"></div>\" +\n\t\t\t\"</div>\" +\n\t\t\t\"<div class=\\\"gantt_cal_qi_content\\\"></div>\";\n\n\t//buttons\n\t\thtml += \"<div class=\\\"gantt_cal_qi_controls\\\">\";\n\t\tvar buttons = gantt.config.quickinfo_buttons;\n\n\t\tvar is_editor = {\"icon_delete\":true,\"icon_edit\":true};\n\n\t\tfor (var i = 0; i < buttons.length; i++){\n\t\t\tif(this._quick_info_readonly && is_editor[buttons[i]])\n\t\t\t\tcontinue;\n\n\t\t\tvar ariaAttr = gantt._waiAria.quickInfoButtonAttrString(gantt.locale.labels[buttons[i]]);\n\n\t\t\thtml += \"<div class=\\\"gantt_qi_big_icon \"+buttons[i]+\"\\\" title=\\\"\"+gantt.locale.labels[buttons[i]]+\"\\\" \" + ariaAttr +\"><div class='gantt_menu_icon \" + buttons[i] + \"'></div><div>\"+gantt.locale.labels[buttons[i]]+\"</div></div>\";\n\t\t}\n\t\thtml += \"</div>\";\n\n\t\tqi.innerHTML = html;\n\n\t\tvar buttonClick = function(ev){\n\t\t\tev = ev || event;\n\t\t\tgantt._qi_button_click(ev.target || ev.srcElement);\n\t\t};\n\n\t\tgantt.event(qi, \"click\", buttonClick);\n\t\tgantt.event(qi, \"keypress\", function(e){\n\t\t\te = e || event;\n\t\t\tvar code = e.which||event.keyCode;\n\t\t\tif (code == 13 || code == 32){\n\t\t\t\tsetTimeout(function(){\n\t\t\t\t\tgantt._qi_button_click(e.target || e.srcElement);\n\t\t\t\t},1);\n\t\t\t}\n\t\t});\n\t\tif (gantt.config.quick_info_detached) {\n\t\t\tvar container = getContainer();\n\t\t\tgantt.event(container, \"scroll\", function () { gantt.hideQuickInfo(); });\n\t\t}\n\t}\n\n\treturn this._quick_info_box;\n};\n\ngantt._qi_button_click = function(node){\n\tvar box = gantt._quick_info_box;\n\tif (!node || node == box) return;\n\n\tvar mask = node.className;\n\tif (mask.indexOf(\"_icon\")!=-1){\n\t\tvar id = gantt._quick_info_box_id;\n\t\tgantt.$click.buttons[mask.split(\" \")[1].replace(\"icon_\",\"\")](id);\n\t} else\n\t\tgantt._qi_button_click(node.parentNode);\n};\ngantt._get_event_counter_part = function(id, offset, viewport){\n\tvar domEv = gantt.getTaskNode(id);\n\tif (!domEv) {\n\t\tdomEv = gantt.getTaskRowNode(id);\n\t\tif (!domEv) {\n\t\t\treturn null;\n\t\t}\n\t}\n\tvar left = 0;\n\tvar top = offset + domEv.offsetTop + domEv.offsetHeight;\n\n\tvar node = domEv;\n\n\tif (this.utils.dom.isChildOf(node, viewport)) {\n\t\twhile (node && node !== viewport){\n\t\t\tleft += node.offsetLeft;\n\t\t\tnode = node.offsetParent;\n\t\t}\n\t}\n\n\tvar scroll = this.getScrollState();\n\n\tif(node){\n\t\tvar dx = (left + domEv.offsetWidth/2) - scroll.x > (gantt.$container.offsetWidth/2) ? 1 : 0;\n\t\tvar dy = (top + domEv.offsetHeight/2) - scroll.y > (gantt.$container.offsetHeight/2) ? 1 : 0;\n\n\t\treturn { left:left, top:top, dx:dx, dy:dy,\n\t\t\twidth:domEv.offsetWidth, height:domEv.offsetHeight };\n\t}\n\treturn null;\n};\n\ngantt._fill_quick_data  = function(id){\n\tvar ev = gantt.getTask(id);\n\tvar qi = gantt._quick_info_box;\n\n\tgantt._quick_info_box_id = id;\n\n//title content\n\n\tvar header = {\n\t\tcontent: gantt.templates.quick_info_title(ev.start_date, ev.end_date, ev),\n\t\tdate: gantt.templates.quick_info_date(ev.start_date, ev.end_date, ev)\n\t};\n\tvar titleContent = qi.firstChild.firstChild;\n\ttitleContent.innerHTML = header.content;\n\tvar titleDate = titleContent.nextSibling;\n\ttitleDate.innerHTML = header.date;\n\n\n\tgantt._waiAria.quickInfoHeader(qi, [header.content, header.date].join(\" \"));\n\n//main content\n\tvar main = qi.firstChild.nextSibling;\n\tmain.innerHTML = gantt.templates.quick_info_content(ev.start_date, ev.end_date, ev);\n};\n"], "sourceRoot": ""}