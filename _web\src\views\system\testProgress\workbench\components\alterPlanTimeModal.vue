<template>
    <a-modal :title="`${modalData.testName} 更改计划时间`" :visible="true" :width="400" :centered="true" @ok="planTimeSubmit" @cancel="planTimeCancel">
        <a-form :form="timeForm">
          <a-form-item label="计划时间" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-range-picker format="YYYY-MM-DD" v-model="timeRange">
            </a-range-picker>
          </a-form-item>
          <a-form-item v-if="modalData.taskType === 'jmcs'" label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-textarea v-model="alterPlanTimeOpinion" auto-size/>
          </a-form-item>
        </a-form>
    </a-modal>
</template>

<script>
import moment from "moment/moment";
import {executeAlterTesterOrPlanTime} from "@/api/modular/system/testProgressManager";
export default {
  name: "AlterPlanTimeModal",
  props: {
    modalData: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      labelCol: {
        sm: {
          span: 5
        }
      },
      wrapperCol: {
        sm: {
          span: 18
        }
      },
      alterPlanTimeOpinion: '',
      timeRange: null,
      timeForm: this.$form.createForm(this,{ name: 'timeForm' })
    }
  },
  methods: {
    planTimeCancel() {
      this.$emit("cancel")
    },
    planTimeSubmit() {
      if (this.timeRange === null || this.timeRange.length !== 2) {
        this.$message.warning('请选择计划开始和结束时间！')
        return
      }
      this.executeAlterPlanTimeOfJMOrAQ(this.timeRange)
    },
    executeAlterPlanTimeOfJMOrAQ(timeRange) {
      let ordTaskId = this.modalData.ordTaskId
      let person = {
        opinion: "更改计划时间",
        alterPlanTimeOpinion: this.alterPlanTimeOpinion,
        planStartTime: moment(timeRange[0]).format('YYYY-MM-DD HH:mm:ss'),
        planEndTime: moment(timeRange[1]).format('YYYY-MM-DD HH:mm:ss')
      }
      executeAlterTesterOrPlanTime({ordTaskId:ordTaskId, person:person}).then((res) => {
        if (res.success === true) {
          setTimeout(() => {
            this.clearOldData()
            this.$message.success('更改计划时间成功！')
          }, 200)
        } else {
          this.$message.warning('更改计划时间失败：' + res.message)
        }
      })
    },
    clearOldData() {
      this.timeRange = null
      this.$emit("cancel")
    },
  }
}
</script>

<style scoped>
</style>