
import { getModels } from '@/api/modular/formula/formula';


const formula = {
  state: {
    formula: {
      latex: '',
      params: []
    },
    formulaList: [],
    fittingResults: {
      params: null,
      metrics: null,
    },
    fittingResultData: null,
    fittingResultModalVisible: false,
    fittingData: {
      temperatures: [],
      socs: [],
      dataPoints: []
    }
  },
  mutations: {
    SET_FORMULA(state, formula) {
      state.formula = formula;
    },
    SET_FORMULA_LIST(state, list) {
      state.formulaList = list;
    },
    SET_FITTING_RESULTS(state, { params, metrics }) {
      state.fittingResults.params = params || null;
      state.fittingResults.metrics = metrics || null;
    },
    setFittingResultData(state, data) {
      state.fittingResultData = data;
    },
    setFittingResultModalVisible(state, visible) {
      state.fittingResultModalVisible = visible;
    },
    SET_FITTING_DATA(state, { temperatures, socs, dataPoints }) {
      state.fittingData.temperatures = temperatures || [];
      state.fittingData.socs = socs || [];
      state.fittingData.dataPoints = dataPoints || [];
    }
  },
  actions: {
    // 更新公式
    updateFormula({ commit }, formula) {
      commit('SET_FORMULA', formula);
    },
    // 获取公式列表
    async fetchFormulaList({ commit }) {
      try {
        const response = await getModels();
        if (response && response.data && response.data.success) {
          commit('SET_FORMULA_LIST', response.data.models || []);
        } else {
          console.error('获取公式列表失败:', response);
          commit('SET_FORMULA_LIST', []);
        }
      } catch (error) {
        console.error('获取公式列表异常:', error);
        commit('SET_FORMULA_LIST', []);
      }
    },
    // 更新拟合结果
    updateFittingResults({ commit }, resultsData) {
      commit('SET_FITTING_RESULTS', resultsData);
    },
    // 保存公式
    saveFormula({ commit }, formula) {
      commit('SET_FORMULA', formula);
    },
    // 保存拟合结果
    saveFittingResults({ commit }, results) {
      commit('SET_FITTING_RESULTS', results);
    },
    // 更新拟合数据
    updateFittingData({ commit }, fittingData) {
      commit('SET_FITTING_DATA', fittingData);
    },
    // 删除公式
    async deleteFormula({ dispatch }, formulaId) {
      try {
        const { deleteFormula } = require('@/api/modular/formula/formula');
        const response = await deleteFormula(formulaId);
        if (response && response.data && response.data.success) {
          // 删除成功后刷新公式列表
          await dispatch('fetchFormulaList');
          return { success: true, message: response.data.message };
        } else {
          console.error('删除公式失败:', response);
          return { success: false, message: response.data?.message || '删除公式失败' };
        }
      } catch (error) {
        console.error('删除公式异常:', error);
        return { success: false, message: error.message || '删除公式异常' };
      }
    }
  }
}

export default formula