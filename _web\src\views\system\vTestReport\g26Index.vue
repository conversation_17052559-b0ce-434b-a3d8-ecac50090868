<template>
	<div class="wrapper" :style="`height:${isInside ?  '' : 'calc(100vh - 40px)'  } ;`">

		<div class="flex-sb-center-row">
			<div class="head_title">G26循环报告建模{{reportName ? "《" + reportName + "》" : ''}} <span
					class="subheading">(正在填写《{{this.menuOptions[testReportActivity].title}}测试报告》的{{phaseDatas.length ==
					0
					? '--' : this.phaseDatas[phaseActivity].label}}阶段)</span>
			</div>
			<div class="btn-wrap">
				<div>
					<a-dropdown @visibleChange="handleScheduleBtn">
						<a-menu slot="overlay">
							<a-menu-item v-for="(item,index) in menuOptions" :key="item.key" style="width: 400px;">
								<div class="menu-content">
									<span style="width: 170px;">{{item.title}}</span>
									<a-progress :percent="item.percent" style="width: 170px;" />
									<a-button :disabled="index === testReportActivity" type="link"
										@click="handleChangeTestReport(index)">填写</a-button>
								</div>
							</a-menu-item>
						</a-menu>
						<a-button class="mr10">填写进度</a-button>
					</a-dropdown>
				</div>
				<a-button class="mr10" @click="handleOpen('isShowHistorical')">历史记录</a-button>
				<a-button class="mr10" @click="handleOpen('isShowDrafts')">草稿箱</a-button>
				<!-- <a-button class="mr10" @click="handleSave">暂存</a-button> -->
				<div style="position: relative;">
					<div class="normal-btn" :class="{'streamer-btn anima': isAllOk}" @click="handleAllOk">
						<span></span>
						<span></span>
						<span></span>
						<span></span>
						完成模型搭建
					</div>
				</div>
			</div>
		</div>

		<div class="top-wrapper">
			<div class="phase-btn-wrapper">
				<div v-for="(item,index) in phaseDatas" class="phase-btn" :class="item.checked ? 'phase-check' : ''"
					@click="handleClicklPhase(index)">
					{{item.label}}<a-icon v-if="!item.checked" class="btn-icon ml2" type="edit"
						@click.stop="handleEditPhase(index)" />
				</div>
			</div>
			<div class="phase-btn" style="background: #fff;" @click="handleAddPhase">
				添加阶段<a-icon class="btn-icon ml2" type="plus" />
			</div>
		</div>

		<div class="center-wrapper block mt10">
			<div class="percent-content" v-if="!isCloseFour">
				<div class="percent-block" v-for="(item,index) in menuOptions" :key="item.key"
					:class="{ 'percent-block-check': index === testReportActivity}" @click="handleChangeTestReport(index)">
					<a-progress type="circle" :percent="item.percent" :width="70" />
					<div class="mt5">{{item.title}}</div>
				</div>
			</div>
			<div class="percent-content" v-else>
				<a-progress :percent="percentTotal" class="progress-block" />
			</div>
      <div class="shrink-btn" @click="handleClose(4)">{{isCloseFour ? '展开' : '收起'}}</div>
    </div>

		<div class="bottom-wrapper mt10">
			<div class="left-content">
				<div class="block">
					<div class="flex-sb-center-row">
						<strong>一、测试数据选择</strong>
            <a-radio-group button-style="solid" v-if="reportDatas[phaseActivity].templateList[testReportActivity].landType" v-model="reportDatas[phaseActivity].templateList[testReportActivity].landType" @change="handleInput">
              <a-radio-button value="reference">
                Reference
              </a-radio-button>
              <a-radio-button value="hot">
                Hot
              </a-radio-button>
              <a-radio-button value="cold">
                Cold
              </a-radio-button>
            </a-radio-group>
            <div style="float: right">
              <a-popconfirm placement="topRight" title="确认删除？" @confirm="deleteSelect">
                <a-button style="margin-top: -5px;margin-right: 15px;">删除</a-button>
              </a-popconfirm>

              <a-icon @click="handleClose(1)" :type="isCloseOne ? 'down' : 'up'" />
            </div>

					</div>
          <div ref="tableContainer">
					  <a-table  id="outTable" v-show="!isCloseOne" class="mt10" bordered :columns="orderColumns"
                   :row-selection="deleteRowSelection"
						:data-source="reportDatas[phaseActivity].templateList[testReportActivity].landType?reportDatas[phaseActivity].templateList[testReportActivity].orderData.filter(o => o.landType &&  o.landType == reportDatas[phaseActivity].templateList[testReportActivity].landType):reportDatas[phaseActivity].templateList[testReportActivity].orderData"
						childrenColumnName="child" :rowKey="record => record.uuid" :pagination="false">
						<template slot="celltestcode" slot-scope="text, record, index, columns">
							<a @click="openStepData(record)" v-if="record.flowId != null" style="text-align: center">{{
								text }}</a>
							<span v-else style="text-align: center">{{ text }}</span>
						</template>

						<template slot="dataPath" slot-scope="text, record, index, columns">
							<a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
								<template slot="title">
									{{ text ? text : record.testcontent }}
								</template>
								{{ text ? text : record.testcontent }}
							</a-tooltip>
						</template>

						<template slot="action" slot-scope="text, record, index, columns">


              <a-tooltip placement="top" title="删除" arrow-point-at-center>
                <a @click="deleteDataOneWithType(record, index)" style="text-align: center">
                  <a-icon type="delete" style="font-size: large;margin-right: 3px"/>
                </a>
              </a-tooltip>

<!--              <a-tooltip placement="top" title="上移" arrow-point-at-center v-if="index != 0">
                <a @click="reportDatas[phaseActivity].templateList[testReportActivity].landType?moveDataOneHaveLand(record.uuid,'up'):moveDataOne(index,'up')" style="text-align: center">
                  <a-icon type="arrow-up" style="font-size: large;margin-right: 3px"/>
                </a>
              </a-tooltip>
              <a-tooltip placement="top" title="下移" arrow-point-at-center v-if="index != orderData.length -1">
                <a @click="reportDatas[phaseActivity].templateList[testReportActivity].landType?moveDataOneHaveLand(record.uuid,'down'):moveDataOne(index,'down')" style="text-align: center">
                  <a-icon type="arrow-down" style="font-size: large"/>
                </a>
              </a-tooltip>-->
              <a-tooltip placement="top" title="拖拽修改位置" arrow-point-at-center>
                <a-icon style="color: #1890ff; font-size: large; cursor: move;" heignt="18" width="18" class="drag" type="unordered-list"/>
              </a-tooltip>

						</template>

						<template slot="remark" slot-scope="text, record, index, columns">
							<div class="remark-input">
								<a-textarea v-model="record.comment" allow-clear @blur="$event => handleBlur($event)" />
							</div>
						</template>
						<template slot="kValue" slot-scope="text, record, index, columns">
							<div class="remark-input">
								<a-textarea v-model="record.kvalue" allow-clear @blur="$event => handleBlur($event)" />
							</div>
						</template>

						<template slot="footer">
							<div class="footer-btn" :class="{
									'plus-btn': reportDatas[phaseActivity].templateList[testReportActivity].orderData.length === 0
								}" @click="handleOpen('visible')">
								<span></span>
								<span></span>
								<span></span>
								<span></span>
								<a-icon type="plus"></a-icon>
							</div>
						</template>
					</a-table>
          </div>
				</div>
			</div>
			<div class="right-content">
				<div class="block" id="export"
					:class="{ 'pbi-block-line': reportDatas[phaseActivity].templateList[testReportActivity].orderData.length !== 0 &&  !isSecondOK  }">
					<div class="flex-sb-center-row">
						<strong>二、基本信息填写</strong>
						<a-icon @click="handleClose(2)" :type="isCloseTwo ? 'down' : 'up'" />
					</div>
					<div v-show="!isCloseTwo" class="mt10">
						<a-form-item v-if="!menuOptions[testReportActivity].isRPT"
							:label="menuOptions[testReportActivity].templateType == 'eol' ? 'Test': 'Rate'" :labelCol="labelCol"
							:wrapperCol="wrapperCol">
							<a-input :placeholder="menuOptions[testReportActivity].templateType == 'eol' ? '请输入Test': '请输入Range'"
								style="width: 100%;" v-model="reportDatas[phaseActivity].templateList[testReportActivity].rate"
								@change="handleInput" @blur="$event => handleBlur($event)" />
						</a-form-item>

						<a-form-item v-if="!menuOptions[testReportActivity].isRPT" label="Voltage Range" :labelCol="labelCol"
							:wrapperCol="wrapperCol">
							<a-input placeholder="请输入Voltage Range" style="width: 100%;"
								v-model="reportDatas[phaseActivity].templateList[testReportActivity].voltageRange" @change="handleInput"
								@blur="$event => handleBlur($event)" />
						</a-form-item>

						<a-form-item v-if="menuOptions[testReportActivity].isRPT" label="Test" :labelCol="labelCol"
							:wrapperCol="wrapperCol">
							<a-input placeholder="请输入Test" style="width: 100%;"
								v-model="reportDatas[phaseActivity].templateList[testReportActivity].test" @change="handleInput"
								@blur="$event => handleBlur($event)" />
						</a-form-item>
					</div>
				</div>


				<div class="block mt10"
					:class="{ 'pbi-block-line': reportDatas[phaseActivity].templateList[testReportActivity].orderData.length !== 0 &&  isSecondOK && !isThreeOK  }">
					<div class="flex-sb-center-row">
						<strong>三、报告生成逻辑</strong>
						<a-icon @click="handleClose(3)" :type="isCloseThree ? 'down' : 'up'" />
					</div>
					<div v-show="!isCloseThree" class="mt10">

						<!-- ---------------------------------------- 非RPT报告 -------------------------------------- -->
						<div v-if="!menuOptions[testReportActivity].isRPT">
							<a-form-item label="工步号1" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-input placeholder="请输入工步号1;多个工步号以英文逗号','隔开" style="width: 100%;"
									v-model="reportDatas[phaseActivity].templateList[testReportActivity].normalParam.ceStepNo"
									@change="handleInput" @blur="$event => handleBlur($event)" />
							</a-form-item>
							<a-form-item label="ETP/Wh 工步号1" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-input placeholder="请输入ETP/Wh 工步号1;多个工步号以英文逗号','隔开" style="width: 100%;"
									v-model="reportDatas[phaseActivity].templateList[testReportActivity].normalParam.etpStepNoList"
									@change="handleInput"
									@keyup="reportDatas[phaseActivity].templateList[testReportActivity].normalParam.etpStepNoList = reportDatas[phaseActivity].templateList[testReportActivity].normalParam.etpStepNoList.replace(/[^0-9,\s\u4e00-\u9fa5]/g, '')"
									@blur="$event => handleBlur($event,'normalParam','etpStepNoList')" />
							</a-form-item>

							<a-form-item v-if="menuOptions[testReportActivity].templateType === 'fastCharge'" label="工步号2"
								:labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-input-number placeholder="请输入工步号2" style="width: 100%;"
									v-model="reportDatas[phaseActivity].templateList[testReportActivity].normalParam.ceStepNo2"
									@change="handleInput" @blur="$event => handleBlur($event)" />
							</a-form-item>
							<a-form-item v-if="menuOptions[testReportActivity].templateType === 'fastCharge'" label="ETP/Wh 工步号2"
								:labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-input placeholder="请输入ETP/Wh 工步号2;多个工步号以英文逗号','隔开" style="width: 100%;"
									v-model="reportDatas[phaseActivity].templateList[testReportActivity].normalParam.etpStepNoList2"
									@change="handleInput"
									@keyup="reportDatas[phaseActivity].templateList[testReportActivity].normalParam.etpStepNoList2 = reportDatas[phaseActivity].templateList[testReportActivity].normalParam.etpStepNoList2.replace(/[^0-9,\s\u4e00-\u9fa5]/g, '')"
									@blur="$event => handleBlur($event,'normalParam','etpStepNoList2')" />
							</a-form-item>
						</div>

						<!-- ---------------------------------------- RPT报告 -------------------------------------- -->

						<div v-if="menuOptions[testReportActivity].isRPT">

							<a-form-item label="间隔" :labelCol="labelCol" :wrapperCol="wrapperCol" v-if="!reportDatas[phaseActivity].templateList[testReportActivity].landType">
								<a-input-number placeholder="请输入间隔" style="width: 100%;"
									v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.rptInterval"
									@change="handleInput" @blur="$event => handleBlur($event)" />
							</a-form-item>
							<a-form-item label="RPT类别数量" :labelCol="labelCol" :wrapperCol="wrapperCol">
								<a-input-number :min="1" :max="4" placeholder="请输入RPT类别数量" style="width: 100%;"
									v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.rptTypeNum"
									@change="handleInput" @blur="$event => handleBlur($event)" />
							</a-form-item>


							<div v-if="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.rptTypeNum >= 1">

								<a-form-item label="容量能量工步号1" :labelCol="labelCol" :wrapperCol="wrapperCol">
									<a-input-number placeholder="请输入容量能量工步号1" style="width: 100%;"
										v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.firstCeStepNo"
										@change="handleInput" @blur="$event => handleBlur($event)" />
								</a-form-item>
								<a-form-item label="搁置工步号1" :labelCol="labelCol" :wrapperCol="wrapperCol">
									<a-input-number placeholder="搁置工步号1" style="width: 100%;"
										v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.firstRestStepNo"
										@change="handleInput" @blur="$event => handleBlur($event)" />
								</a-form-item>
								<a-form-item label="放电工步号1" :labelCol="labelCol" :wrapperCol="wrapperCol">
									<a-input-number placeholder="请输入放电工步号1" style="width: 100%;"
										v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.firstDischargeStepNo"
										@change="handleInput" @blur="$event => handleBlur($event)" />
								</a-form-item>
								<a-form-item label="工步时间1" :labelCol="labelCol" :wrapperCol="wrapperCol">
									<a-input placeholder="请输入工步时间1" style="width: 100%;"
										v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.firstStepTime"
										@change="handleInput" @blur="$event => handleBlur($event)" />
								</a-form-item>
								<a-form-item label="ETP工步号1" :labelCol="labelCol" :wrapperCol="wrapperCol">
									<a-input placeholder="请输入ETP工步号1;多个工步号以英文逗号','隔开" style="width: 100%;"
										v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.firstEtpStepNoList"
										@change="handleInput"
										@keyup="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.firstEtpStepNoList = reportDatas[phaseActivity].templateList[testReportActivity].rptParam.firstEtpStepNoList.replace(/[^0-9,\s\u4e00-\u9fa5]/g, '')"
										@blur="$event => handleBlur($event,'rptParam','firstEtpStepNoList')" />
								</a-form-item>
                <a-form-item label="100%SOC搁置工步号" :labelCol="labelCol" :wrapperCol="wrapperCol" v-if="reportDatas[phaseActivity].templateList[testReportActivity].landType">
                  <a-input-number placeholder="100%SOC搁置工步号" style="width: 100%;"
                                  v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.firstHunRestStepNo"
                                  @change="handleInput" @blur="$event => handleBlur($event)" />
                </a-form-item>
                <a-form-item label="100%SOC放电工步号" :labelCol="labelCol" :wrapperCol="wrapperCol" v-if="reportDatas[phaseActivity].templateList[testReportActivity].landType">
                  <a-input-number placeholder="100%SOC放电工步号" style="width: 100%;"
                                  v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.firstHunDischargeStepNo"
                                  @change="handleInput" @blur="$event => handleBlur($event)" />
                </a-form-item>
                <a-form-item label="100%SOC工步时间" :labelCol="labelCol" :wrapperCol="wrapperCol" v-if="reportDatas[phaseActivity].templateList[testReportActivity].landType">
                  <a-input placeholder="100%SOC工步时间" style="width: 100%;"
                           v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.firstHunStepTime"
                           @change="handleInput" @blur="$event => handleBlur($event)" />
                </a-form-item>

							</div>

							<div v-if="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.rptTypeNum >= 2">

								<a-form-item label="容量能量工步号2" :labelCol="labelCol" :wrapperCol="wrapperCol">
									<a-input-number placeholder="请输入容量能量工步号2" style="width: 100%;"
										v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.secondCeStepNo"
										@change="handleInput" @blur="$event => handleBlur($event)" />
								</a-form-item>
								<a-form-item label="搁置工步号2" :labelCol="labelCol" :wrapperCol="wrapperCol">
									<a-input-number placeholder="搁置工步号2" style="width: 100%;"
										v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.secondRestStepNo"
										@change="handleInput" @blur="$event => handleBlur($event)" />
								</a-form-item>
								<a-form-item label="放电工步号2" :labelCol="labelCol" :wrapperCol="wrapperCol">
									<a-input-number placeholder="请输入放电工步号2" style="width: 100%;"
										v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.secondDischargeStepNo"
										@change="handleInput" @blur="$event => handleBlur($event)" />
								</a-form-item>
                <a-form-item label="工步时间2" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input placeholder="请输入工步时间2" style="width: 100%;"
                           v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.secondStepTime"
                           @change="handleInput" @blur="$event => handleBlur($event)" />
                </a-form-item>
								<a-form-item label="ETP工步号2" :labelCol="labelCol" :wrapperCol="wrapperCol">
									<a-input placeholder="请输入ETP工步号2;多个工步号以英文逗号','隔开" style="width: 100%;"
										v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.secondEtpStepNoList"
										@change="handleInput"
										@keyup="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.secondEtpStepNoList = reportDatas[phaseActivity].templateList[testReportActivity].rptParam.secondEtpStepNoList.replace(/[^0-9,\s\u4e00-\u9fa5]/g, '')"
										@blur="$event => handleBlur($event,'rptParam','secondEtpStepNoList')" />
								</a-form-item>

                <a-form-item label="100%SOC搁置工步号" :labelCol="labelCol" :wrapperCol="wrapperCol" v-if="reportDatas[phaseActivity].templateList[testReportActivity].landType">
                  <a-input-number placeholder="100%SOC搁置工步号" style="width: 100%;"
                                  v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.secondHunRestStepNo"
                                  @change="handleInput" @blur="$event => handleBlur($event)" />
                </a-form-item>
                <a-form-item label="100%SOC放电工步号" :labelCol="labelCol" :wrapperCol="wrapperCol" v-if="reportDatas[phaseActivity].templateList[testReportActivity].landType">
                  <a-input-number placeholder="100%SOC放电工步号" style="width: 100%;"
                                  v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.secondHunDischargeStepNo"
                                  @change="handleInput" @blur="$event => handleBlur($event)" />
                </a-form-item>
                <a-form-item label="100%SOC工步时间" :labelCol="labelCol" :wrapperCol="wrapperCol" v-if="reportDatas[phaseActivity].templateList[testReportActivity].landType">
                  <a-input placeholder="100%SOC工步时间" style="width: 100%;"
                           v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.secondHunStepTime"
                           @change="handleInput" @blur="$event => handleBlur($event)" />
                </a-form-item>

							</div>

							<div v-if="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.rptTypeNum >= 3">

								<a-form-item label="容量能量工步号3" :labelCol="labelCol" :wrapperCol="wrapperCol">
									<a-input-number placeholder="请输入容量能量工步号3" style="width: 100%;"
										v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.thirdCeStepNo"
										@change="handleInput" @blur="$event => handleBlur($event)" />
								</a-form-item>
								<a-form-item label="搁置工步号3" :labelCol="labelCol" :wrapperCol="wrapperCol">
									<a-input-number placeholder="搁置工步号3" style="width: 100%;"
										v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.thirdRestStepNo"
										@change="handleInput" @blur="$event => handleBlur($event)" />
								</a-form-item>
								<a-form-item label="放电工步号3" :labelCol="labelCol" :wrapperCol="wrapperCol">
									<a-input-number placeholder="请输入放电工步号3" style="width: 100%;"
										v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.thirdDischargeStepNo"
										@change="handleInput" @blur="$event => handleBlur($event)" />
								</a-form-item>
                <a-form-item label="工步时间3" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input placeholder="请输入工步时间3" style="width: 100%;"
                           v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.thirdStepTime"
                           @change="handleInput" @blur="$event => handleBlur($event)" />
                </a-form-item>
								<a-form-item label="ETP工步号3" :labelCol="labelCol" :wrapperCol="wrapperCol">
									<a-input placeholder="请输入ETP工步号3;多个工步号以英文逗号','隔开" style="width: 100%;"
										v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.thirdEtpStepNoList"
										@change="handleInput"
										@keyup="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.thirdEtpStepNoList = reportDatas[phaseActivity].templateList[testReportActivity].rptParam.thirdEtpStepNoList.replace(/[^0-9,\s\u4e00-\u9fa5]/g, '')"
										@blur="$event => handleBlur($event,'rptParam','thirdEtpStepNoList')" />
								</a-form-item>

                <a-form-item label="100%SOC搁置工步号" :labelCol="labelCol" :wrapperCol="wrapperCol" v-if="reportDatas[phaseActivity].templateList[testReportActivity].landType">
                  <a-input-number placeholder="100%SOC搁置工步号" style="width: 100%;"
                                  v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.thirdHunRestStepNo"
                                  @change="handleInput" @blur="$event => handleBlur($event)" />
                </a-form-item>
                <a-form-item label="100%SOC放电工步号" :labelCol="labelCol" :wrapperCol="wrapperCol" v-if="reportDatas[phaseActivity].templateList[testReportActivity].landType">
                  <a-input-number placeholder="100%SOC放电工步号" style="width: 100%;"
                                  v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.thirdHunDischargeStepNo"
                                  @change="handleInput" @blur="$event => handleBlur($event)" />
                </a-form-item>
                <a-form-item label="100%SOC工步时间" :labelCol="labelCol" :wrapperCol="wrapperCol" v-if="reportDatas[phaseActivity].templateList[testReportActivity].landType">
                  <a-input placeholder="100%SOC工步时间" style="width: 100%;"
                           v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.thirdHunStepTime"
                           @change="handleInput" @blur="$event => handleBlur($event)" />
                </a-form-item>

							</div>

							<div v-if="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.rptTypeNum >= 4">

								<a-form-item label="容量能量工步号4" :labelCol="labelCol" :wrapperCol="wrapperCol">
									<a-input-number placeholder="请输入容量能量工步号4" style="width: 100%;"
										v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.fourthCeStepNo"
										@change="handleInput" @blur="$event => handleBlur($event)" />
								</a-form-item>
								<a-form-item label="搁置工步号4" :labelCol="labelCol" :wrapperCol="wrapperCol">
									<a-input-number placeholder="搁置工步号4" style="width: 100%;"
										v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.fourthRestStepNo"
										@change="handleInput" @blur="$event => handleBlur($event)" />
								</a-form-item>
								<a-form-item label="放电工步号4" :labelCol="labelCol" :wrapperCol="wrapperCol">
									<a-input-number placeholder="请输入放电工步号4" style="width: 100%;"
										v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.fourthDischargeStepNo"
										@change="handleInput" @blur="$event => handleBlur($event)" />
								</a-form-item>
                <a-form-item label="工步时间4" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input placeholder="请输入工步时间4" style="width: 100%;"
                           v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.fourthStepTime"
                           @change="handleInput" @blur="$event => handleBlur($event)" />
                </a-form-item>
								<a-form-item label="ETP工步号4" :labelCol="labelCol" :wrapperCol="wrapperCol">
									<a-input placeholder="请输入ETP工步号4;多个工步号以英文逗号','隔开" style="width: 100%;"
										v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.fourthEtpStepNoList"
										@change="handleInput"
										@keyup="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.fourthEtpStepNoList = reportDatas[phaseActivity].templateList[testReportActivity].rptParam.fourthEtpStepNoList.replace(/[^0-9,\s\u4e00-\u9fa5]/g, '')"
										@blur="$event => handleBlur($event,'rptParam','fourthEtpStepNoList')" />
								</a-form-item>

                <a-form-item label="100%SOC搁置工步号" :labelCol="labelCol" :wrapperCol="wrapperCol" v-if="reportDatas[phaseActivity].templateList[testReportActivity].landType">
                  <a-input-number placeholder="100%SOC搁置工步号" style="width: 100%;"
                                  v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.fourthHunRestStepNo"
                                  @change="handleInput" @blur="$event => handleBlur($event)" />
                </a-form-item>
                <a-form-item label="100%SOC放电工步号" :labelCol="labelCol" :wrapperCol="wrapperCol" v-if="reportDatas[phaseActivity].templateList[testReportActivity].landType">
                  <a-input-number placeholder="100%SOC放电工步号" style="width: 100%;"
                                  v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.fourthHunDischargeStepNo"
                                  @change="handleInput" @blur="$event => handleBlur($event)" />
                </a-form-item>
                <a-form-item label="100%SOC工步时间" :labelCol="labelCol" :wrapperCol="wrapperCol" v-if="reportDatas[phaseActivity].templateList[testReportActivity].landType">
                  <a-input placeholder="100%SOC工步时间" style="width: 100%;"
                           v-model="reportDatas[phaseActivity].templateList[testReportActivity].rptParam.fourthHunStepTime"
                           @change="handleInput" @blur="$event => handleBlur($event)" />
                </a-form-item>

							</div>
						</div>
					</div>
				</div>

			</div>
		</div>

		<!-- 添加阶段弹窗 -->
		<a-modal :title="isPhaseType == 1 ? '样品阶段添加': '样品阶段编辑'" :width="350" :visible="isShowAddPhase"
			@cancel="handleCancel('isShowAddPhase')">
			<template slot="footer">
				<div class="btn-wrap">
					<a-button v-if="phaseDatas.length > 0" @click="handleCancel('isShowAddPhase')">
						取消
					</a-button>
					<a-button v-else @click="handleCancel('isShowAddPhase')">
						关闭
					</a-button>
					<a-button v-if="isPhaseType !== 1" type="danger" @click="handleDeletePhase">
						删除
					</a-button>
					<a-button v-if="phaseDatas.length == 0" @click="handleOpenHistory">
						历史记录
					</a-button>
					<a-button v-if="phaseDatas.length == 0" @click="handleOpenDraf">
						草稿箱
					</a-button>
					<a-button type="primary" @click="handlePhase">
						{{isPhaseType == 1 ? '新增': '确认'}}
					</a-button>

				</div>
			</template>
			<div class="phase-modal">
				<span class="mr10">样品阶段名称:</span><a-input v-model="phaseNameCheck" style="width: 200px;" placeholder="请填写样品阶段名称"
					@keyup.enter="handlePhase" />
			</div>

		</a-modal>

		<!-- 完成模型搭建弹窗 -->
		<a-modal title="完成模型搭建" :width="350" :visible="isShowReportName" @cancel="handleCancel('isShowReportName')">
			<template slot="footer">
				<div class="btn-wrap">
					<a-button @click="handleCancel('isShowReportName')">
						取消
					</a-button>
					<a-button type="primary" @click="exportData">
						完成模型搭建
					</a-button>

				</div>
			</template>
			<div class="phase-modal">
				<span class="mr10">报告名称:</span><a-input v-model="reportName" style="width: 200px;" placeholder="请填写报告名称"
					@keyup.enter="exportData" />
			</div>
		</a-modal>

		<!-- 测试项目选择弹窗 -->
		<a-modal title="测试项目选择" :width="1200" :height="600" :bodyStyle="{ padding: 0 }" :visible="visible"
			style="padding: 0" :maskClosable="false" @cancel="handleCancel('visible')">
			<div style="float: left;position: relative;z-index: 1;padding-bottom: 5px;width: 65%">
				<a-row :gutter="[8, 8]">
					<a-col :span="8">
						<a-form-item label="委托单号" :labelCol="labelCol1" :wrapperCol="wrapperCol1" has-feedback>
							<a-input v-model="queryParam.folderno" @keyup.enter="$refs.table.refresh()"
								@change="$refs.table.refresh()" />
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="主题" :labelCol="labelCol1" :wrapperCol="wrapperCol1" has-feedback>
							<a-input v-model="queryParam.theme" @keyup.enter="$refs.table.refresh()"
								@change="$refs.table.refresh()" />
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="测试项目别名" :labelCol="labelCol1" :wrapperCol="wrapperCol1" has-feedback>
							<a-input v-model="queryParam.alias" @keyup.enter="$refs.table.refresh()"
								@change="$refs.table.refresh()" />
						</a-form-item>
					</a-col>
				</a-row>
			</div>

			<s-table :columns="columns" :data="loadData" bordered :rowKey="record1 => record1.uuid" ref="table"
				:row-selection="{
					selectedRowKeys: reportDatas[phaseActivity].templateList[testReportActivity].selectedRowKeys, //指定选中项的 key 数组
					selectedRows: reportDatas[phaseActivity].templateList[testReportActivity].selectedRows,
					onSelect: onSelectChange,
					onSelectAll: onSelectAllChange,
					columnWidth: 30
				}">
				<template slot="celltestcode" slot-scope="text, record, index, columns">
					<a @click="openStepData(record)" v-if="record.flowId != null" style="text-align: center">{{ text
						}}</a>
					<span v-else style="text-align: center">{{ text }}</span>
				</template>

				<template slot="theme" slot-scope="text, record, index, columns">
					<a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
						<template slot="title">
							{{ text }}
						</template>
						{{ text }}
					</a-tooltip>
				</template>

				<template slot="dataPath" slot-scope="text, record, index, columns">
					<a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
						<template slot="title">
							{{ text ? text : record.testcontent }}
						</template>
						{{ text ? text : record.testcontent }}
					</a-tooltip>
				</template>


				<template slot="action1" slot-scope="text, record, index, columns">
					<a @click="showData(record)" v-if="record.showHide" style="text-align: center">初始化</a>
					<a @click="hideData(record)" v-else-if="!record.showHide && record.children != null"
						style="text-align: center">隐藏</a>
					<a @click="hideData(record)" v-else-if="record.isChild" style="text-align: center">隐藏</a>
				</template>
			</s-table>
			<template slot="footer">
				<a-button key="back" @click="handleCancel('visible')">
					关闭
				</a-button>
			</template>
		</a-modal>

		<!-- 历史记录弹窗 -->
		<a-modal title="历史记录" :width="1200" :height="600" :bodyStyle="{ padding: 0 }" :visible="isShowHistorical"
			style="padding: 0" :maskClosable="false" @cancel="handleCancel('isShowHistorical')">

			<div style="float: left;position: relative;z-index: 1;padding-bottom: 5px;width: 65%">
				<a-row :gutter="[8, 8]">
					<a-col :span="8">
						<a-form-item label="报告状态" :labelCol="labelCol1" :wrapperCol="wrapperCol1" has-feedback>
							<a-select v-model="queryHistoryParam.fileStatus" @change="$refs.historyTable.refresh()"
								style="width: 100%" :allow-clear="true">
								<a-select-option value="0">
									待处理
								</a-select-option>
								<a-select-option value="10">
									进行中
								</a-select-option>
								<a-select-option value="20">
									已完成
								</a-select-option>
								<a-select-option value="30">
									数据异常
								</a-select-option>
							</a-select>
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="创建人" :labelCol="labelCol1" :wrapperCol="wrapperCol1" has-feedback>
							<a-input v-model="queryHistoryParam.createName" @keyup.enter="$refs.historyTable.refresh()"
								@change="$refs.historyTable.refresh()" />
						</a-form-item>
					</a-col>
				</a-row>
			</div>

			<s-table :columns="historyColumns" :data="historyData" bordered :rowKey="record1 => record1.id"
				ref="historyTable">
				<template slot="action" slot-scope="text, record, index, columns">
					<a-button type="link" @click="handleCopyHistory(record)">复制</a-button>
				</template>

				<template slot="template" slot-scope="text, record, index, columns">
					<a style="text-align: center" v-if="record.fileStatus == 20"
						@click="g26DownloadFile(record,'Service life test data summary template')">
						<a-spin :spinning="record.status == 1" tip="正在下载中" size="small">
							Service life test data summary template
						</a-spin>
					</a>

				</template>

				<template slot="reportData" slot-scope="text, record, index, columns">
					<a style="text-align: center" v-if="record.dataFileStatus == 20" :href="'http://***********:8282/sysFileInfo/download?id='+record.dataFileId
              +'&userAccount='+userInfo.account+'&userName='+userInfo.name" download>
						raw data
					</a>

				</template>

			</s-table>
			<template slot="footer">
				<a-button key="back" @click="handleCancel('isShowHistorical')">
					关闭
				</a-button>
			</template>
		</a-modal>

		<!-- 草稿箱弹窗 -->
		<a-modal title="草稿箱" :width="1200" :height="600" :bodyStyle="{ padding: 0 }" :visible="isShowDrafts"
			style="padding: 0" :maskClosable="false" @cancel="handleCancel('isShowDrafts')">

			<div style="float: left;position: relative;z-index: 1;padding-bottom: 5px;width: 65%">
				<a-row :gutter="[8, 8]">
					<a-col :span="8">
						<a-form-item label="创建人" :labelCol="labelCol1" :wrapperCol="wrapperCol1" has-feedback>
							<a-input v-model="queryDraftParam.createName" @keyup.enter="$refs.draftTable.refresh()"
								@change="$refs.draftTable.refresh()" />
						</a-form-item>
					</a-col>
				</a-row>
			</div>

			<s-table :columns="draftColumns" :data="draftData" bordered :rowKey="record1 => record1.id" ref="draftTable">
				<template slot="action" slot-scope="text, record, index, columns">
					<a-button type="link" @click="handleDraf(record)">编辑</a-button>
				</template>
			</s-table>
			<template slot="footer">
				<a-button key="back" @click="handleCancel('isShowDrafts')">
					关闭
				</a-button>
			</template>
		</a-modal>

		<!-- 导出 -->
		<a-modal title="导出" :width="800" :height="600" :bodyStyle="{ padding: 0 }" :visible="visible1"
			:confirmLoading="confirmLoading" @ok="handleSubmit" style="padding: 0" @cancel="handleCancel('visible1')">
			<a-form :form="form">
				<a-row :gutter="24">
					<a-col :md="18" :sm="24">
						<a-form-item label="任务名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
							<a-input placeholder="请输入任务名称"
								v-decorator="['taskName', { rules: [{ required: true, message: '请输入任务名称！' }] }]" />
						</a-form-item>
					</a-col>
				</a-row>

				<a-row :gutter="24">
					<a-col :md="18" :sm="24">
						<a-form-item label="Excel格式" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-select v-decorator="['excelType', { rules: [{ required: true, message: '请选择Excel格式！' }] }]"
								default-value="one" style="width: 100%" placeholder="请选择Excel格式">
								<a-select-option value="one">
									保存于同一个Sheet中
								</a-select-option>
								<a-select-option value="more">
									保存于不同的Sheet中
								</a-select-option>
							</a-select>
						</a-form-item>
					</a-col>
				</a-row>
			</a-form>
		</a-modal>

		<a-modal title="测试数据选择" :width="1000" :height="300" :bodyStyle="{ padding: 0 }" :visible="visibleFlow"
			style="padding: 0" :maskClosable="false" :centered="true" @cancel="handleCancel('visibleFlow')">
			<a-table :columns="flowInfoColumns" :dataSource="flowInfoData" bordered :rowKey="record => record.uuid"
				ref="table" :pagination="false">
				<!-- 测试编码 -->
				<template slot="celltestcode" slot-scope="text, record, index, columns">
					<a @click="openStepData(record)" v-if="record.flowId != null" style="text-align: center">{{ text
						}}</a>
					<span v-else style="text-align: center">{{ text }}</span>
				</template>

				<!-- 数据位置 -->
				<template slot="dataPath" slot-scope="text, record, index, columns">
					<a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
						<template slot="title">
							{{ text ? text : record.testcontent }}
						</template>
						{{ text ? text : record.testcontent }}
					</a-tooltip>
				</template>

				<!-- 操作 -->
				<template slot="action" slot-scope="text, record, index, columns">
					<a @click="onSelectChangeFlow(record, '选中')" style="text-align: center">选中</a>
					<a-divider type="vertical" />
					<a @click="onSelectChangeFlow(record, '查看')" style="text-align: center">查看</a>
				</template>

			</a-table>

			<!-- 页脚 -->
			<template slot="footer">
				<a-button key="back" @click="handleCancel('visibleFlow')">
					关闭
				</a-button>
			</template>
		</a-modal>

		<step-data ref="stepData"></step-data>

		<a-drawer :bodyStyle="{ height: '100%' }" placement="right" :closable="false" width="70%" :visible="isShowExample"
			@close="handleCancel('isShowExample')">
			<iframe :src="iframeUrl" width="100%" height="100%"></iframe>
		</a-drawer>
	</div>
</template>
<script>
	import { mapGetters } from "vuex"
	import moment from "moment"
	import jsonBigint from 'json-bigint'

	import stepData from "../lims/folder/stepData"
	import { STable } from "@/components"
  import Sortable from 'sortablejs';

	import {
		tLimsTestdataSchedulePageList,
		shenghongDataFilterExport,
		testReportPageList,
		hideData,
		showData
	} from "@/api/modular/system/limsManager"

	import {
		g26TestReportTask,
		commitG26,
		getG26ById
	} from "@/api/modular/system/g26Manager"
	import axios from "axios";


	export default {
    props: {
      isInside:{
        type: Boolean,
        default: false
      }
    },
		components: {
			STable,
			stepData
		},
		data() {
			return {
				isShowAddPhase: false, //样品阶段弹窗
				isShowReportName: false, //报告名称弹窗
				isShowHistorical: false, //历史记录弹窗
				isShowDrafts: false, //草稿箱弹窗
				visible: false,
				confirmLoading: false,
				visible1: false,

				isCloseOne: false,
				isCloseTwo: false,
				isCloseThree: false,
				isCloseFour: false,
				isCloseFive: false,
				isCloseSix: false,
				isCloseSeven: false,
				isCloseEight: false,
				isCloseNine: false,

				visibleFlow: false,
				isShowExample: false,

				testReportID: null, //该份测试报告的ID

				time: null, //防抖
				isPhaseType: 1,  // 1：添加  2：修改

				reportName: '',//报告名称


				phaseNameCheck: '',//选中编辑的样品阶段名称
				phaseCheck: '0',//选中编辑的样品阶段
				phaseActivity: 0, // 当前样品阶段
				testReportActivity: 0,  //当前测试报告阶段
				percentTotal: 0,//总的进度百分比
				issueId: null,
				iframeUrl: "",
				outFlowRecord: null,
				outQueryFlowRecord: null,
				cycle: null,
				saveParam: null,

				menuOptions: [
					{ key: 1, title: '25℃ 1C', templateType: 'oneC', groupName: 'oneC', isRPT: false, percent: 0 },
					{ key: 2, title: '25℃ 1C(RPT)', templateType: 'oneCRPT', groupName: 'oneC', isRPT: true, percent: 0 },
					{ key: 3, title: '25℃ 0.33C', templateType: 'oneThirdC', groupName: 'oneThirdC', isRPT: false, percent: 0 },
					{ key: 4, title: '25℃ 0.33C(RPT)', templateType: 'oneThirdCRPT', groupName: 'oneThirdC', isRPT: true, percent: 0 },
					{ key: 5, title: 'Fast Charge Cycle', templateType: 'fastCharge', groupName: 'fastCharge', isRPT: false, percent: 0 },
					{ key: 6, title: 'Fast Charge Cycle(RPT)', templateType: 'fastChargeRPT', groupName: 'fastCharge', isRPT: true, percent: 0 },
					{ key: 7, title: 'EOL Making', templateType: 'eol', groupName: 'eol', isRPT: false, percent: 0 },
					{ key: 8, title: 'EOL Making(RPT)', templateType: 'eolRPT', groupName: 'eol', isRPT: true, percent: 0 },
					{ key: 9, title: 'High Performance(Large DOD)', templateType: 'High Performance(Large DOD)', groupName: 'High Performance(Large DOD)', isRPT: true, percent: 0 },
					{ key: 10, title: 'High Performance(Small DOD)', templateType: 'High Performance(Small DOD)', groupName: 'High Performance(Small DOD)', isRPT: true, percent: 0 },
					{ key: 11, title: 'Long Distance(Large DOD)', templateType: 'Long Distance(Large DOD)', groupName: 'Long Distance(Large DOD)', isRPT: true, percent: 0 },
					{ key: 12, title: 'Long Distance(Small DOD)', templateType: 'Long Distance(Small DOD)', groupName: 'Long Distance(Small DOD)', isRPT: true, percent: 0 },
					{ key: 13, title: 'Reference Usage(Large DOD)', templateType: 'Reference Usage(Large DOD)', groupName: 'Reference Usage(Large DOD)', isRPT: true, percent: 0 },
					{ key: 14, title: 'Reference Usage(Small DOD)', templateType: 'Reference Usage(Small DOD)', groupName: 'Reference Usage(Small DOD)', isRPT: true, percent: 0 },
					{ key: 15, title: 'Short Distance(Large DOD)', templateType: 'Short Distance(Large DOD)', groupName: 'Short Distance(Large DOD)', isRPT: true, percent: 0 },
					{ key: 16, title: 'Short Distance(Small DOD)', templateType: 'Short Distance(Small DOD)', groupName: 'Short Distance(Small DOD)', isRPT: true, percent: 0 },
				],

				phaseDatas: [],

				// 报告生成数据
				reportDatas: [],
				phaseTemplate: [
					{
						reportName: '25℃ 1C',
						templateType: 'oneC',
						groupName: 'oneC',
						rate: '25℃ 1C',
						orderData: [],
						selectedRowKeys: [],
						selectedRows: [],
						normalParam: {}
					},
					{
						reportName: '25℃ 1C(RPT)',
						templateType: 'oneCRPT',
						groupName: 'oneC',
						test: "25℃ 1C(RPT)",
						orderData: [],
						selectedRowKeys: [],
						selectedRows: [],
						rptParam: {
							rptTypeNum: 1
						}
					},
					{
						reportName: '25℃ 0.33C',
						templateType: 'oneThirdC',
						groupName: 'oneThirdC',
						rate: '25℃ 0.33C',
						orderData: [],
						selectedRowKeys: [],
						selectedRows: [],
						normalParam: {}
					},
					{
						reportName: '25℃ 0.33C(RPT)',
						templateType: 'oneThirdCRPT',
						groupName: 'oneThirdC',
						test: "25℃ 0.33C(RPT)",
						orderData: [],
						selectedRowKeys: [],
						selectedRows: [],
						rptParam: {
							rptTypeNum: 1
						}
					},
					{
						reportName: 'Fast Charge Cycle',
						templateType: 'fastCharge',
						groupName: 'fastCharge',
						rate: 'Fast Charge Cycle',
						orderData: [],
						selectedRowKeys: [],
						selectedRows: [],
						normalParam: {}
					},
					{
						reportName: 'Fast Charge Cycle(RPT)',
						templateType: 'fastChargeRPT',
						groupName: 'fastCharge',
						test: "Fast Charge Cycle(RPT)",
						orderData: [],
						selectedRowKeys: [],
						selectedRows: [],
						rptParam: {
							rptTypeNum: 1
						}
					},
					{
						reportName: 'EOL Making',
						templateType: 'eol',
						groupName: 'eol',
						rate: 'EOL Making',
						orderData: [],
						selectedRowKeys: [],
						selectedRows: [],
						normalParam: {}
					},
					{
						reportName: 'EOL Making(RPT)',
						templateType: 'eolRPT',
						groupName: 'eol',
						test: "EOL Making(RPT)",
						orderData: [],
						selectedRowKeys: [],
						selectedRows: [],
						rptParam: {
							rptTypeNum: 1
						}
					},
					{
						reportName: 'High Performance(Large DOD)',
						templateType: 'High Performance(Large DOD)',
						groupName: 'High Performance(Large DOD)',
						test: "High Performance(Large DOD)",
						orderData: [],
						selectedRowKeys: [],
						selectedRows: [],
            landType: 'reference',
						rptParam: {
							rptTypeNum: 1
						}
					},
					{
						reportName: 'High Performance(Small DOD)',
						templateType: 'High Performance(Small DOD)',
						groupName: 'High Performance(Small DOD)',
						test: "High Performance(Small DOD)",
						orderData: [],
						selectedRowKeys: [],
						selectedRows: [],
            landType: 'reference',
						rptParam: {
							rptTypeNum: 1
						}
					},
					{
						reportName: 'Long Distance(Large DOD)',
						templateType: 'Long Distance(Large DOD)',
						groupName: 'Long Distance(Large DOD)',
						test: "Long Distance(Large DOD)",
						orderData: [],
						selectedRowKeys: [],
						selectedRows: [],
            landType: 'reference',
						rptParam: {
							rptTypeNum: 1
						}
					},
					{
						reportName: 'Long Distance(Small DOD)',
						templateType: 'Long Distance(Small DOD)',
						groupName: 'Long Distance(Small DOD)',
						test: "Long Distance(Small DOD)",
						orderData: [],
						selectedRowKeys: [],
						selectedRows: [],
            landType: 'reference',
						rptParam: {
							rptTypeNum: 1
						}
					},
					{
						reportName: 'Reference Usage(Large DOD)',
						templateType: 'Reference Usage(Large DOD)',
						groupName: 'Reference Usage(Large DOD)',
						test: "Reference Usage(Large DOD)",
						orderData: [],
						selectedRowKeys: [],
						selectedRows: [],
            landType: 'reference',
						rptParam: {
							rptTypeNum: 1
						}
					},
					{
						reportName: 'Reference Usage(Small DOD)',
						templateType: 'Reference Usage(Small DOD)',
						groupName: 'Reference Usage(Small DOD)',
						test: "Reference Usage(Small DOD)",
						orderData: [],
						selectedRowKeys: [],
						selectedRows: [],
            landType: 'reference',
						rptParam: {
							rptTypeNum: 1
						}
					},
					{
						reportName: 'Short Distance(Large DOD)',
						templateType: 'Short Distance(Large DOD)',
						groupName: 'Short Distance(Large DOD)',
						test: "Short Distance(Large DOD)",
						orderData: [],
						selectedRowKeys: [],
						selectedRows: [],
            landType: 'reference',
						rptParam: {
							rptTypeNum: 1
						}
					},
					{
						reportName: 'Short Distance(Small DOD)',
						templateType: 'Short Distance(Small DOD)',
						groupName: 'Short Distance(Small DOD)',
						test: "Short Distance(Small DOD)",
						orderData: [],
						selectedRowKeys: [],
						selectedRows: [],
            landType: 'reference',
						rptParam: {
							rptTypeNum: 1
						}
					}
				],

				flowInfoColumns: [
					{
						title: "测试编码",
						dataIndex: "barCode",
						align: "center",
						width: 100
					},
					{
						title: "设备编号",
						width: 30,
						align: "center",
						dataIndex: "unitNum"
						//scopedSlots: {customRender: 'updateText'},
					},
					{
						title: "通道编号",
						width: 30,
						align: "center",
						dataIndex: "channelId"
						//scopedSlots: {customRender: 'updateText'},
					},
					{
						title: "数据位置",
						width: 30,
						align: "center",
						dataIndex: "dataPath",
						scopedSlots: { customRender: "dataPath" }
					},
					{
						title: "开始时间",
						width: 30,
						align: "center",
						dataIndex: "startTime",
						customRender: (text, record, index) => {
							if (null != text) {
								return moment(text).format("YYYY-MM-DD")
							}
							return text
						}
						//
						//scopedSlots: {customRender: 'updateText'},
					},
					{
						title: "结束时间",
						width: 30,
						align: "center",
						dataIndex: "endTime",
						customRender: (text, record, index) => {
							if (null != text) {
								return moment(text).format("YYYY-MM-DD")
							}
							return text
						}
					},
					{
						title: "操作",
						width: 60,
						align: "center",
						dataIndex: "action",
						scopedSlots: { customRender: "action" }
					}
				],
				flowInfoData: [],
				loadData: parameter => {
					this.loadDataList = []
					return tLimsTestdataSchedulePageList(Object.assign(parameter, this.queryParam)).then(res => {
						return res.data
					})
				},
				draftColumns: [
					{
						title: '序号',
						align: 'center',
						width: 50,
						customRender: (text, record, index) => index + 1
					},
					{
						title: '报告名称',
						dataIndex: 'reportName',
						align: 'center',
						width: 200,
						scopedSlots: { customRender: 'reportName' },
					},
          /*{
            title: '报告类型',
            width: 90,
            align: 'center',
            dataIndex: 'type'
          },
          {
            title: '操作类型',
            width: 90,
            align: 'center',
            dataIndex: 'operateType',
            customRender: (text, record, index) => {
              if (record.operateType == 'add') {
                return "初次创建"
              }
              if (record.operateType == 'update') {
                return "更新数据"
              }
              if (record.operateType == 'refresh') {
                return "刷新数据"
              }
            }
          },*/
					{
						title: '操作时间',
						width: 90,
						align: 'center',
						dataIndex: 'createTime',

					},
					{
						title: '操作人',
						width: 90,
						align: 'center',
						dataIndex: 'createName',

					},
					{
						title: "操作",
						align: "center",
						width: 30,
						scopedSlots: { customRender: "action" }
					},
				],
				historyColumns: [
					{
						title: '序号',
						align: 'center',
						width: 50,
						customRender: (text, record, index) => index + 1
					},
          {
            title: '报告名称',
            dataIndex: 'reportName',
            align: 'center',
            width: 100,
          },
					{
						title: 'Template',
						dataIndex: 'template',
						align: 'center',
						width: 100,
						scopedSlots: { customRender: 'template' },
					}, {
						title: '原始数据',
						dataIndex: 'reportData',
						align: 'center',
						width: 100,
						scopedSlots: { customRender: 'reportData' },
					},
					/*{
						title: '报告名称',
						dataIndex: 'reportName',
						align: 'center',
						width: 200,
						scopedSlots: { customRender: 'reportName' },
					},
					{
						title: '报告类型',
						width: 90,
						align: 'center',
						dataIndex: 'type'
					},
					{
						title: '操作类型',
						width: 90,
						align: 'center',
						dataIndex: 'operateType',
						customRender: (text, record, index) => {
							if (record.operateType == 'add') {
								return "初次创建"
							}
							if (record.operateType == 'update') {
								return "更新数据"
							}
							if (record.operateType == 'refresh') {
								return "刷新数据"
							}
						}
					},*/
					{
						title: '操作时间',
						width: 90,
						align: 'center',
						dataIndex: 'createTime',

					},
					{
						title: '操作人',
						width: 90,
						align: 'center',
						dataIndex: 'createName',

					},
					{
						title: "操作",
						align: "center",
						width: 30,
						scopedSlots: { customRender: "action" }
					},
				],
				historyData: parameter => {
					return testReportPageList(Object.assign(parameter, this.queryHistoryParam)).then((res) => {
						return res.data
					})
				},
				draftData: parameter => {
					return testReportPageList(Object.assign(parameter, this.queryDraftParam)).then((res) => {
						return res.data
					})
				},
				orderData: [],
				labelCol: {
					sm: {
						span: 6
					}
				},
				wrapperCol: {
					sm: {
						span: 16
					}
				},
				labelCol1: {
					sm: {
						span: 11
					}
				},
				wrapperCol1: {
					sm: {
						span: 13
					}
				},
				queryParam: {},
				queryHistoryParam: {
					type: "G26"
				},
				queryDraftParam: {
					fileStatus: -10,
					type: 'G26'
				},
				data: [],
        deleteSelectedRowKeys: [],

				// 测试项目选择表头
				orderColumns: [
					{
						title: "操作",
						align: "center",
						width: 60,
						scopedSlots: { customRender: "action" }
					},
					{
						title: "序号",
						align: "center",
						width: 30,
						customRender: (text, record, index) => index + 1
					},
					{
						title: "委托单号",
						dataIndex: "folderno",
						align: "center",
						width: 45
					},
					// {
					// 	title: "测试项目编码",
					// 	width: 70,
					// 	align: "center",
					// 	dataIndex: "testcode"
					// 	//scopedSlots: {customRender: 'updateText'},
					// },
					{
						title: "测试项目别名",
						width: 65,
						align: "center",
						dataIndex: "alias"
						//scopedSlots: {customRender: 'updateText'},
					},

					{
						title: "测试编码",
						width: 50,
						align: "center",
						dataIndex: "celltestcode",
						scopedSlots: { customRender: "celltestcode" }
					},
					{
						title: "数据位置",
						width: 50,
						align: "center",
						dataIndex: "dataPath",
						ellipsis: true,
						scopedSlots: { customRender: "dataPath" }
					},
					{
						title: "设备编号",
						width: 45,
						align: "center",
						dataIndex: "equiptcode"
					},
					{
						title: "通道编号",
						width: 45,
						align: "center",
						dataIndex: "channelno"
					},
					{
						title: "备注",
						width: 45,
						align: "center",
						dataIndex: "remark",
						scopedSlots: { customRender: "remark" }
					},
					{
						title: "K值",
						width: 45,
						align: "center",
						dataIndex: "kValue",
						scopedSlots: { customRender: "kValue" }
					}
				],
				columns: [
					{
						title: "操作",
						align: "center",
						width: 70,
						scopedSlots: { customRender: "action1" }
					},
					{
						title: "序号",
						align: "center",
						width: 50,
						customRender: (text, record, index) => {
							if (!record.isChild) {
								return index + 1
							}
						}
					},
					{
						title: "委托单号",
						dataIndex: "folderno",
						align: "center",
						width: 90
					},
					{
						title: "主题",
						dataIndex: "theme",
						align: "center",
						ellipsis: true,
						width: 90,
						scopedSlots: { customRender: "theme" }
					},
					{
						title: "样品编号",
						width: 90,
						align: "center",
						dataIndex: "orderno"
					},
					{
						title: "测试项目编码",
						width: 90,
						align: "center",
						dataIndex: "testcode"
						//scopedSlots: {customRender: 'updateText'},
					},
					{
						title: "测试项目别名",
						width: 90,
						align: "center",
						dataIndex: "alias"
						//scopedSlots: {customRender: 'updateText'},
					},
					{
						title: "测试编码",
						width: 90,
						align: "center",
						dataIndex: "celltestcode",
						scopedSlots: { customRender: "celltestcode" }
					},
					{
						title: "数据位置",
						width: 60,
						align: "center",
						dataIndex: "dataPath",
						ellipsis: true,
						scopedSlots: { customRender: "dataPath" }
					},
					{
						title: "开始时间",
						width: 90,
						align: "center",
						dataIndex: "startTime",
						customRender: (text, record, index) => {
							if (null != text) {
								return moment(text).format("YYYY-MM-DD")
							}
							return text
						}
						//
						//scopedSlots: {customRender: 'updateText'},
					},
					{
						title: "结束时间",
						width: 90,
						align: "center",
						dataIndex: "endTime",
						customRender: (text, record, index) => {
							if (null != text) {
								return moment(text).format("YYYY-MM-DD")
							}
							return text
						}
					},
					{
						title: "设备编号",
						width: 60,
						align: "center",
						dataIndex: "equiptcode"
					},
					{
						title: "通道编号",
						width: 60,
						align: "center",
						dataIndex: "channelno"
					}
				],
				form: this.$form.createForm(this),
				selectedRowKeys: [],
				selectedRows: [],
			}
		},
		computed: {
			...mapGetters(["testTaskFilterData", "testTaskId", "userInfo"]),
      deleteRowSelection() {
			  return {
          columnWidth: 20,
          selectedRowKeys:this.deleteSelectedRowKeys,
          onChange: (selectedRowKeys, selectedRows) => {
            this.deleteSelectedRowKeys = selectedRowKeys
          }
        }

      },
			isSecondOK() {
				const temLIst = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity]
				const temOK = this.menuOptions[this.testReportActivity].isRPT ? temLIst.test : (temLIst.rate && temLIst.voltageRange)
				return temOK
			},
			isThreeOK() {
				const temLIst = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity]
				let result = 0
				if (this.menuOptions[this.testReportActivity].isRPT) {
					const numberArr = ['first', 'second', 'third', 'fourth']
					for (let i = 0; i < temLIst.rptParam.rptTypeNum; i++) {
						if (!temLIst.rptParam[numberArr[i] + 'CeStepNo']) result++
						if (!temLIst.rptParam[numberArr[i] + 'RestStepNo']) result++
						if (!temLIst.rptParam[numberArr[i] + 'DischargeStepNo']) result++
						if (!temLIst.rptParam[numberArr[i] + 'EtpStepNoList']) result++
					}
				} else {
					if (!temLIst.normalParam.ceStepNo) result++
					if (!temLIst.normalParam.etpStepNoList) result++
					// 报告Fast Charge Cycle，ceStepNo2有数据时，etpStepNoList2为必填
					if (this.menuOptions[this.testReportActivity].templateType === 'fastCharge' && temLIst.normalParam.ceStepNo2 && !temLIst.normalParam.etpStepNoList2) result++
				}
				return result == 0
			},
			isAllOk() {
				return this._handleVerify(this.reportDatas)[0]
			}
		},
		created() {
			this.time = new Date().getTime()

			// 刷新页面
			if (window.sessionStorage.getItem('cyclicTestReportID')) {
				this.testReportID = window.sessionStorage.getItem('cyclicTestReportID')
				return this.getG26ById({ id: window.sessionStorage.getItem('cyclicTestReportID') })
			}

			// 由测试报告新增
			if (this.testTaskId !== null) {
        let json = jsonBigint({ storeAsString: true })
				this.reportDatas.push({ samplePhase: this.testTaskId, templateList: json.parse(json.stringify(this.phaseTemplate)) })
				this.g26TestReportTask(this.reportDatas)
				this.$store.commit("setTaskID", null)
				return
			}


			// 如果是重新生成
			if (this.testTaskFilterData !== null) {
        let json = jsonBigint({ storeAsString: true })
				this.reportName = this.testTaskFilterData.reportName
				this.g26TestReportTask(json.parse(this.testTaskFilterData.queryParam),null,this.reportName)
				this.$store.commit("setTaskFilterData", null)
				return
			}


			// 如果没有数据，则弹出弹窗让他填写
			if (this.phaseDatas.length === 0) {
        let json = jsonBigint({ storeAsString: true })
				this.reportDatas.push({ samplePhase: '', templateList: json.parse(json.stringify(this.phaseTemplate)) })
				this.handleAddPhase()
			}



		},
		destroyed() {
			window.sessionStorage.removeItem('cyclicTestReportID')
		},

    watch:{
      phaseActivity(newVal, oldVal) {
        this.bindDrag()
      },
      testReportActivity(newVal, oldVal) {
        this.bindDrag()
      },
      reportDatas(newVal, oldVal) {
        this.bindDrag()
      }
    },

		methods: {
      rowDrop(dom) {
        new Sortable.create(dom.querySelector('.ant-table>.ant-table-content .ant-table-tbody'), {
          handle: '.drag', // 按钮拖拽
          animation: 150,
          onEnd: ({newIndex, oldIndex}) => {
            // 拖拽后回调
            try {

              //DOD
              if(this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].landType){

                let thisList = this.reportDatas[this.phaseActivity]
                  .templateList[this.testReportActivity]
                  .orderData.filter(o => o.landType &&  o.landType == this.reportDatas[this.phaseActivity]
                  .templateList[this.testReportActivity].landType)

                let otherList = this.reportDatas[this.phaseActivity]
                  .templateList[this.testReportActivity]
                  .orderData.filter(o => o.landType &&  o.landType != this.reportDatas[this.phaseActivity]
                    .templateList[this.testReportActivity].landType)

                let list = [...thisList,...otherList]

                this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData = list
                const currRow = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData.splice(oldIndex, 1)[0]
                this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData.splice(newIndex, 0, currRow)

              }else{
                const currRow = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData.splice(oldIndex, 1)[0]
                this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData.splice(newIndex, 0, currRow)

                const temLIst = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity]
                const temIndex = this.reportDatas[this.phaseActivity].templateList.findIndex(v => v.groupName === temLIst.groupName && v.templateType !== temLIst.templateType)

                if(temIndex != null){
                  this.reportDatas[this.phaseActivity].templateList[temIndex].orderData = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData
                  this.reportDatas[this.phaseActivity].templateList[temIndex].selectedRowKeys = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].selectedRowKeys
                  this.reportDatas[this.phaseActivity].templateList[temIndex].selectedRows = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].selectedRows
                }

              }
              this.$forceUpdate()
              // 实时保存
              this.g26TestReportTask(this.reportDatas, this.testReportID)



            }catch (error){

            }
          }
        })
      },
			g26DownloadFile(record, name, fileId, suffix) {
				record.status = 1
				let url = 'http://***********:8282/sysFileInfo/downloadWithAuth'
				axios({
					url: url,
					method: 'POST',
					//headers:this.headers,
					data: { id: fileId != null ? fileId : record.fileId, userAccount: this.userInfo.account, userName: this.userInfo.name },
					responseType: 'blob' // 设置响应类型为blob
				})
					.then(response => {
						if (response.data.size < 500) {
							location.reload()
							return
						}
						const url = window.URL.createObjectURL(new Blob([response.data]));
						const link = document.createElement('a');
						link.href = url;
						link.setAttribute('download', name + (suffix != null ? suffix : '.xlsx')); // 设置下载文件的名称和扩展名
						document.body.appendChild(link);
						link.click();
						record.status = 0
					})
					.catch(error => {
						record.status = 0
					});
			},

			/*
			* 接口文档
			*/

			// G26-报告任务新增、暂存或实时修改
			g26TestReportTask(parameter, id = null) {
        let json = jsonBigint({ storeAsString: true })
				// 如果没有穿id就是新增
				// 如果有穿id就是暂存或实时修改
				const params = json.parse(json.stringify(parameter))
        params.forEach(v => {
          let hpld = v.templateList.find(t => t.templateType == 'High Performance(Large DOD)')
          if(!hpld){
            v.templateList.push({
              reportName: 'High Performance(Large DOD)',
              templateType: 'High Performance(Large DOD)',
              groupName: 'High Performance(Large DOD)',
              test: "High Performance(Large DOD)",
              orderData: [],
              selectedRowKeys: [],
              selectedRows: [],
              landType: 'reference',
              rptParam: {
                rptTypeNum: 1
              }
            })
          }
          let hpsd = v.templateList.find(t => t.templateType == 'High Performance(Small DOD)')
          if(!hpsd){
            v.templateList.push({
              reportName: 'High Performance(Small DOD)',
              templateType: 'High Performance(Small DOD)',
              groupName: 'High Performance(Small DOD)',
              test: "High Performance(Small DOD)",
              orderData: [],
              selectedRowKeys: [],
              selectedRows: [],
              landType: 'reference',
              rptParam: {
                rptTypeNum: 1
              }
            })
          }
          let ldld = v.templateList.find(t => t.templateType == 'Long Distance(Large DOD)')
          if(!ldld){
            v.templateList.push({
              reportName: 'Long Distance(Large DOD)',
              templateType: 'Long Distance(Large DOD)',
              groupName: 'Long Distance(Large DOD)',
              test: "Long Distance(Large DOD)",
              orderData: [],
              selectedRowKeys: [],
              selectedRows: [],
              landType: 'reference',
              rptParam: {
                rptTypeNum: 1
              }
            })
          }
          let ldsd = v.templateList.find(t => t.templateType == 'Long Distance(Small DOD)')
          if(!ldsd){
            v.templateList.push({
              reportName: 'Long Distance(Small DOD)',
              templateType: 'Long Distance(Small DOD)',
              groupName: 'Long Distance(Small DOD)',
              test: "Long Distance(Small DOD)",
              orderData: [],
              selectedRowKeys: [],
              selectedRows: [],
              landType: 'reference',
              rptParam: {
                rptTypeNum: 1
              }
            })
          }
          let ruld = v.templateList.find(t => t.templateType == 'Reference Usage(Large DOD)')
          if(!ruld){
            v.templateList.push({
              reportName: 'Reference Usage(Large DOD)',
              templateType: 'Reference Usage(Large DOD)',
              groupName: 'Reference Usage(Large DOD)',
              test: "Reference Usage(Large DOD)",
              orderData: [],
              selectedRowKeys: [],
              selectedRows: [],
              landType: 'reference',
              rptParam: {
                rptTypeNum: 1
              }
            })
          }
          let rusd = v.templateList.find(t => t.templateType == 'Reference Usage(Small DOD)')
          if(!rusd){
            v.templateList.push({
              reportName: 'Reference Usage(Small DOD)',
              templateType: 'Reference Usage(Small DOD)',
              groupName: 'Reference Usage(Small DOD)',
              test: "Reference Usage(Small DOD)",
              orderData: [],
              selectedRowKeys: [],
              selectedRows: [],
              landType: 'reference',
              rptParam: {
                rptTypeNum: 1
              }
            })
          }
          let sdld = v.templateList.find(t => t.templateType == 'Short Distance(Large DOD)')
          if(!sdld){
            v.templateList.push({
              reportName: 'Short Distance(Large DOD)',
              templateType: 'Short Distance(Large DOD)',
              groupName: 'Short Distance(Large DOD)',
              test: "Short Distance(Large DOD)",
              orderData: [],
              selectedRowKeys: [],
              selectedRows: [],
              landType: 'reference',
              rptParam: {
                rptTypeNum: 1
              }
            })
          }
          let sdsd = v.templateList.find(t => t.templateType == 'Short Distance(Small DOD)')
          if(!sdsd){
            v.templateList.push({
              reportName: 'Short Distance(Small DOD)',
              templateType: 'Short Distance(Small DOD)',
              groupName: 'Short Distance(Small DOD)',
              test: "Short Distance(Small DOD)",
              orderData: [],
              selectedRowKeys: [],
              selectedRows: [],
              landType: 'reference',
              rptParam: {
                rptTypeNum: 1
              }
            })
          }
        })



				params.forEach(v => {
					v.templateList.forEach(e => {


						// 传递到后端，需要传递数组
						// 非RPT
						// if (e.normalParam) {
						// 	// 判断是否有值，如果没有值就给他一个空数组
						// 	e.normalParam.etpStepNoList = e.normalParam.etpStepNoList || []
						// 	// 判断是否是数组，如果不是数组并且有值，转为数组传给后端
						// 	e.normalParam.etpStepNoList = e.normalParam.etpStepNoList && !Array.isArray(e.normalParam.etpStepNoList) ? e.normalParam.etpStepNoList.replaceAll(/，/ig, ',').split(',') : []

						// 	e.normalParam.etpStepNoList2 = e.normalParam.etpStepNoList2 || []
						// 	e.normalParam.etpStepNoList2 = e.normalParam.etpStepNoList2 && !Array.isArray(e.normalParam.etpStepNoList2) ? e.normalParam.etpStepNoList2.replaceAll(/，/ig, ',').split(',') : []
						// }

						// //  RPT
						// if (e.rptParam) {
						// 	e.rptParam.firstEtpStepNoList = e.rptParam.firstEtpStepNoList || []
						// 	e.rptParam.firstEtpStepNoList = e.rptParam.firstEtpStepNoList && !Array.isArray(e.rptParam.firstEtpStepNoList) ? e.rptParam.firstEtpStepNoList.replaceAll(/，/ig, ',').split(',') : []

						// 	e.rptParam.secondEtpStepNoList = e.rptParam.secondEtpStepNoList || []
						// 	e.rptParam.secondEtpStepNoList = e.rptParam.secondEtpStepNoList && !Array.isArray(e.rptParam.secondEtpStepNoList) ? e.rptParam.secondEtpStepNoList.replaceAll(/，/ig, ',').split(',') : []

						// 	e.rptParam.thirdEtpStepNoList = e.rptParam.thirdEtpStepNoList || []
						// 	e.rptParam.thirdEtpStepNoList = e.rptParam.thirdEtpStepNoList && !Array.isArray(e.rptParam.thirdEtpStepNoList) ? e.rptParam.thirdEtpStepNoList.replaceAll(/，/ig, ',').split(',') : []

						// 	e.rptParam.fourthEtpStepNoList = e.rptParam.fourthEtpStepNoList || []
						// 	e.rptParam.fourthEtpStepNoList = e.rptParam.fourthEtpStepNoList && !Array.isArray(e.rptParam.fourthEtpStepNoList) ? e.rptParam.fourthEtpStepNoList.replaceAll(/，/ig, ',').split(',') : []
						// }


						if (e.normalParam && !e.normalParam.etpStepNoList) {
							e.normalParam.etpStepNoList = []
						}
						if (e.normalParam && e.normalParam.etpStepNoList && !Array.isArray(e.normalParam.etpStepNoList)) {
							e.normalParam.etpStepNoList = e.normalParam.etpStepNoList.replaceAll(/，/ig, ',')
							e.normalParam.etpStepNoList = e.normalParam.etpStepNoList.split(',')
						}

						if (e.normalParam && !e.normalParam.etpStepNoList2) {
							e.normalParam.etpStepNoList2 = []
						}
						if (e.normalParam && e.normalParam.etpStepNoList2 && !Array.isArray(e.normalParam.etpStepNoList2)) {
							e.normalParam.etpStepNoList2 = e.normalParam.etpStepNoList2.replaceAll(/，/ig, ',')
							e.normalParam.etpStepNoList2 = e.normalParam.etpStepNoList2.split(',')
						}

						if (e.rptParam && !e.rptParam.firstEtpStepNoList) {
							e.rptParam.firstEtpStepNoList = []
						}
						if (e.rptParam && e.rptParam.firstEtpStepNoList && !Array.isArray(e.rptParam.firstEtpStepNoList)) {
							e.rptParam.firstEtpStepNoList = e.rptParam.firstEtpStepNoList.replaceAll(/，/ig, ',')
							e.rptParam.firstEtpStepNoList = e.rptParam.firstEtpStepNoList.split(',')
						}

						if (e.rptParam && !e.rptParam.secondEtpStepNoList) {
							e.rptParam.secondEtpStepNoList = []
						}
						if (e.rptParam && e.rptParam.secondEtpStepNoList && !Array.isArray(e.rptParam.secondEtpStepNoList)) {
							e.rptParam.secondEtpStepNoList = e.rptParam.secondEtpStepNoList.replaceAll(/，/ig, ',')
							e.rptParam.secondEtpStepNoList = e.rptParam.secondEtpStepNoList.split(',')
						}

						if (e.rptParam && !e.rptParam.thirdEtpStepNoList) {
							e.rptParam.thirdEtpStepNoList = []
						}
						if (e.rptParam && e.rptParam.thirdEtpStepNoList && !Array.isArray(e.rptParam.thirdEtpStepNoList)) {
							e.rptParam.thirdEtpStepNoList = e.rptParam.thirdEtpStepNoList.replaceAll(/，/ig, ',')
							e.rptParam.thirdEtpStepNoList = e.rptParam.thirdEtpStepNoList.split(',')
						}

						if (e.rptParam && !e.rptParam.fourthEtpStepNoList) {
							e.rptParam.fourthEtpStepNoList = []
						}
						if (e.rptParam && e.rptParam.fourthEtpStepNoList && !Array.isArray(e.rptParam.fourthEtpStepNoList)) {
							e.rptParam.fourthEtpStepNoList = e.rptParam.fourthEtpStepNoList.replaceAll(/，/ig, ',')
							e.rptParam.fourthEtpStepNoList = e.rptParam.fourthEtpStepNoList.split(',')
						}

					})
				})

				g26TestReportTask(params, id, this.reportName).then(res => {
					// 新建
					if (id == null) {
						this.phaseDatas.push(res.data)
						this.phaseDatas[0].label = this.$route.query.phaseNameActivity
						this.testReportID = res.data.id
						window.sessionStorage.setItem("cyclicTestReportID", this.testReportID)

						this.getG26ById({
							id: this.testReportID
						})
						// 修改
					} else {
						json.parse(res.data.queryParam).forEach(v => {
							const haveId = this.reportDatas.find(findItem => findItem.id && (findItem.id == v.id))
							// 如果当前id没有存在
							if (!haveId) {
								const havePhaseIndex = this.reportDatas.findIndex(findItem => !findItem.id && (findItem.samplePhase == v.samplePhase))
								this.reportDatas[havePhaseIndex].id = v.id
							}
						})
					}




				})
			},

			// G26-根据id查询报告任务
			getG26ById(parameter) {
        let json = jsonBigint({ storeAsString: true })
				getG26ById(parameter).then(res => {
					this.reportName = res.data.reportName
					// this.reportDatas = json.parse(res.data.queryParam)

					let json = jsonBigint({ storeAsString: true })
					this.reportDatas = json.parse(res.data.queryParam)

					this.reportDatas.forEach(v => {
						v.templateList.forEach((e, index) => {
							const testReport = this.menuOptions.find(menuItem => menuItem.templateType == e.templateType)

							// 给予初始值
							if (testReport && testReport.isRPT && !e.rptParam) {
								e.rptParam = {
									rptTypeNum: 1
								}
							}
							if (testReport && !testReport.isRPT && !e.normalParam) {
								e.normalParam = {}
							}

							// 前端接收，需要数组转字符串
							// 非RPT
							if (e.normalParam) {
								e.normalParam.etpStepNoList = (e.normalParam.etpStepNoList && e.normalParam.etpStepNoList.length !== 0) ? e.normalParam.etpStepNoList.join(',') : ''
								e.normalParam.etpStepNoList2 = (e.normalParam.etpStepNoList2 && e.normalParam.etpStepNoList2.length !== 0) ? e.normalParam.etpStepNoList2.join(',') : ''
							}
							// RPT
							if (e.rptParam) {
								e.rptParam.firstEtpStepNoList = (e.rptParam.firstEtpStepNoList && e.rptParam.firstEtpStepNoList.length !== 0) ? e.rptParam.firstEtpStepNoList.join(',') : ''
								e.rptParam.secondEtpStepNoList = (e.rptParam.secondEtpStepNoList && e.rptParam.secondEtpStepNoList.length !== 0) ? e.rptParam.secondEtpStepNoList.join(',') : ''
								e.rptParam.thirdEtpStepNoList = (e.rptParam.thirdEtpStepNoList && e.rptParam.thirdEtpStepNoList.length !== 0) ? e.rptParam.thirdEtpStepNoList.join(',') : ''
								e.rptParam.fourthEtpStepNoList = (e.rptParam.fourthEtpStepNoList && e.rptParam.fourthEtpStepNoList.length !== 0) ? e.rptParam.fourthEtpStepNoList.join(',') : ''
							}


							// if (e.normalParam && e.normalParam.etpStepNoList) {
							// 	e.normalParam.etpStepNoList = e.normalParam.etpStepNoList.length !== 0 ? e.normalParam.etpStepNoList.join(',') : ''
							// }
							// if (e.normalParam && e.normalParam.etpStepNoList2) {
							// 	e.normalParam.etpStepNoList2 = e.normalParam.etpStepNoList2.length !== 0 ? e.normalParam.etpStepNoList2.join(',') : ''
							// }

							// if (e.rptParam && e.rptParam.firstEtpStepNoList) {
							// 	e.rptParam.firstEtpStepNoList = e.rptParam.firstEtpStepNoList.length !== 0 ? e.rptParam.firstEtpStepNoList.join(',') : ''
							// }
							// if (e.rptParam && e.rptParam.secondEtpStepNoList) {
							// 	e.rptParam.secondEtpStepNoList = e.rptParam.secondEtpStepNoList.length !== 0 ? e.rptParam.secondEtpStepNoList.join(',') : ''
							// }
							// if (e.rptParam && e.rptParam.thirdEtpStepNoList) {
							// 	e.rptParam.thirdEtpStepNoList = e.rptParam.thirdEtpStepNoList.length !== 0 ? e.rptParam.thirdEtpStepNoList.join(',') : ''
							// }
							// if (e.rptParam && e.rptParam.fourthEtpStepNoList) {
							// 	e.rptParam.fourthEtpStepNoList = e.rptParam.fourthEtpStepNoList.length !== 0 ? e.rptParam.fourthEtpStepNoList.join(',') : ''
							// }

							if (e.orderData.length > 0) {
								e.selectedRowKeys = json.parse(json.stringify(e.orderData)).map(chil => chil.uuid + '')
								e.selectedRows = json.parse(json.stringify(e.orderData))
							} else {
								e.selectedRowKeys = []
								e.selectedRows = []
							}
						})
					})

					this.phaseDatas = []
					this.reportDatas.forEach((v, index) => {
						this.phaseDatas.push({
							id: index + 1,
							label: v.samplePhase,
							checked: index == 0 ? true : false
						})
					})
					this._handleProgress()


				})
			},

			handleInput() {

				if (this.phaseDatas.length === 0) return this.$message.warn('请先添加阶段')

				const now = new Date().getTime()
				if (now - this.time > 500) {
					this._handleProgress()
					this.time = new Date().getTime()
				}
			},
			handleBlur({ target }, targetType = '', targetName = '') {
				if (this.phaseDatas.length === 0) return this.$message.warn('请先添加阶段')
				if (targetName && target._value) {
					this.reportDatas[this.phaseActivity].templateList[this.testReportActivity][targetType][targetName] = target._value.replace(/\s+/g, ',')
				}
				// 实时保存
				this.g26TestReportTask(this.reportDatas, this.testReportID)
			},

			/*
			* 填写进度按钮事件
			*/

			// 进度按钮触发事件
			handleScheduleBtn(value) {
				if (value) {
					this._handleProgress()
				}
			},

			/*
			* 历史记录按钮事件
			*/
			// 复制
			handleCopyHistory(record) {
        let json = jsonBigint({ storeAsString: true })
				// 生成新的数据
				this.g26TestReportTask(json.parse(record.queryParam))
				this.isShowHistorical = false
			},
			handleOpenHistory() {
				this.isShowAddPhase = false
				this.isShowHistorical = true
			},

			handleOpenDraf() {
				this.isShowAddPhase = false
				this.isShowDrafts = true
			},

			/*
			* 草稿箱按钮事件
			*/
			handleDraf(record) {
				this.testReportID = record.id
				this.getG26ById({ id: record.id })
				window.sessionStorage.setItem("cyclicTestReportID", record.id)
				this.isShowDrafts = false
			},
			/*
			*暂存事件
			*/
			handleSave() {
				this.$message.success('保存成功')
			},

			/*
			*测试报告事件
			*/
			// 点击测试报告事件
			handleChangeTestReport(target) {
				this.testReportActivity = target

			},

      bindDrag(){
          this.$nextTick(() => {
            if (this.phaseActivity != null  && this.testReportActivity != null && this.reportDatas[this.phaseActivity] && this.reportDatas[this.phaseActivity].templateList) {
              let tableContainer = this.$refs.tableContainer
              this.rowDrop(tableContainer)
            }
          })
      },


			/*
			*  样品阶段事件
			*/

			// 点击阶段事件
			handleClicklPhase(target) {
				if (this.phaseActivity == target) return
				this.phaseActivity = target
				this.phaseDatas.forEach(v => {
					v.checked = v.id === target + 1 ? true : false
				})
				this.menuOptions.forEach(e => {
					e.percent = 0
				})
				this.testReportActivity = 0
				this._handleProgress()
			},
			// 编辑阶段事件
			handleAddPhase() {
				this.isPhaseType = 1
				this.isShowAddPhase = true
			},
			// 编辑阶段事件
			handleEditPhase(target) {
				this.isPhaseType = 2
				this.phaseCheck = target
				this.phaseNameCheck = this.phaseDatas[target].label
				this.isShowAddPhase = true
			},
			// 删除阶段事件
			handleDeletePhase() {
				if (this.phaseActivity > 0) this.phaseActivity--
				this.phaseDatas.splice(this.phaseCheck, 1)
				this.isShowAddPhase = false
				this.phaseCheck = -1
				this.phaseNameCheck = ''
			},
			// 阶段弹窗事件
			handlePhase() {
        let json = jsonBigint({ storeAsString: true })
				// 添加
				if (this.isPhaseType == 1) {
					if (!this.phaseNameCheck) {
						return this.$message.warn('请正确填写样品阶段名称')
					}

					const hand = this.phaseDatas.length > 0

					if (this.testReportID) {
						this.phaseDatas.push({
							id: this.phaseDatas.length + 1,
							label: this.phaseNameCheck,
							checked: hand ? false : true
						})
					}

					// 添加数据
					// 如果第一条数据是空的,新建
					if (this.reportDatas[0].samplePhase == '') {
						this.reportDatas[0] = { samplePhase: this.phaseNameCheck, templateList: json.parse(json.stringify(this.phaseTemplate)) }
					} else {
						this.reportDatas.push({ samplePhase: this.phaseNameCheck, templateList: json.parse(json.stringify(this.phaseTemplate)) })
					}

					// 修改
				} else if (this.isPhaseType == 2) {
					this.phaseDatas[this.phaseCheck].label = this.phaseNameCheck

					this.reportDatas[this.phaseCheck].samplePhase = this.phaseNameCheck
				}

				this.phaseNameCheck = ''
				this.isShowAddPhase = false

				this.g26TestReportTask(this.reportDatas, this.testReportID)

			},

			showData(record) {
				showData({ celltestcode: record.celltestcode }).then(res => {
					this.$refs.table.refresh()
				})
			},
			hideData(record) {
				hideData({ id: record.id }).then(res => {
					this.$refs.table.refresh()
				})
			},

			handleSubmit() {
				const {
					form: { validateFields }
				} = this

				this.confirmLoading = true
				validateFields((errors, values) => {
					if (!errors) {
						shenghongDataFilterExport(Object.assign(values, this.saveParam)).then(res => {
							if (res.success) {
								this.$message.success("导出任务创建成功")
								this.$router.push("/testDataHistory")
							} else {
								this.$message.warn(res.message)
							}
						})
					}
					this.confirmLoading = false
				})
			},

			handleAllOk() {
				const temList = this._handleVerify(this.reportDatas)
				// 如果校验不通过
				if (!temList[0]) return this.$message.warn("请正确填写《" + temList[1] + "测试报告》的" + temList[2] + "的" + temList[3])

				if (temList[0]) return this.isShowReportName = true
			},


			// 完成模型搭建事件
			exportData() {
        let json = jsonBigint({ storeAsString: true })
				if(!this.reportName) return this.$message.warn("请正确填写测试报告名称")
				const params = json.parse(json.stringify(this.reportDatas))
				params.forEach(v => {
					v.templateList.forEach(e => {
						const testReport = this.menuOptions.find(menuItem => menuItem.templateType == e.templateType)

						// 传递到后端，需要传递数组
						// 非RPT
						if (e.normalParam) {
							// 判断是否有值，如果没有值就给他一个空数组
							e.normalParam.etpStepNoList = e.normalParam.etpStepNoList || []
							// 判断是否是数组，如果不是数组并且有值，转为数组传给后端
							e.normalParam.etpStepNoList = e.normalParam.etpStepNoList && !Array.isArray(e.normalParam.etpStepNoList) ? e.normalParam.etpStepNoList.replaceAll(/，/ig, ',').split(',') : []

							e.normalParam.etpStepNoList2 = e.normalParam.etpStepNoList2 || []
							e.normalParam.etpStepNoList2 = e.normalParam.etpStepNoList2 && !Array.isArray(e.normalParam.etpStepNoList2) ? e.normalParam.etpStepNoList2.replaceAll(/，/ig, ',').split(',') : []
						}

						//  RPT
						if (e.rptParam) {
							e.rptParam.firstEtpStepNoList = e.rptParam.firstEtpStepNoList || []
							e.rptParam.firstEtpStepNoList = e.rptParam.firstEtpStepNoList && !Array.isArray(e.rptParam.firstEtpStepNoList) ? e.rptParam.firstEtpStepNoList.replaceAll(/，/ig, ',').split(',') : []

							e.rptParam.secondEtpStepNoList = e.rptParam.secondEtpStepNoList || []
							e.rptParam.secondEtpStepNoList = e.rptParam.secondEtpStepNoList && !Array.isArray(e.rptParam.secondEtpStepNoList) ? e.rptParam.secondEtpStepNoList.replaceAll(/，/ig, ',').split(',') : []

							e.rptParam.thirdEtpStepNoList = e.rptParam.thirdEtpStepNoList || []
							e.rptParam.thirdEtpStepNoList = e.rptParam.thirdEtpStepNoList && !Array.isArray(e.rptParam.thirdEtpStepNoList) ? e.rptParam.thirdEtpStepNoList.replaceAll(/，/ig, ',').split(',') : []

							e.rptParam.fourthEtpStepNoList = e.rptParam.fourthEtpStepNoList || []
							e.rptParam.fourthEtpStepNoList = e.rptParam.fourthEtpStepNoList && !Array.isArray(e.rptParam.fourthEtpStepNoList) ? e.rptParam.fourthEtpStepNoList.replaceAll(/，/ig, ',').split(',') : []
						}

						// if (e.normalParam && !e.normalParam.etpStepNoList) {
						// 	e.normalParam.etpStepNoList = []
						// }
						// if (e.normalParam && e.normalParam.etpStepNoList && !Array.isArray(e.normalParam.etpStepNoList)) {
						// 	e.normalParam.etpStepNoList = e.normalParam.etpStepNoList.replaceAll(/，/ig, ',')
						// 	e.normalParam.etpStepNoList = e.normalParam.etpStepNoList.split(',')
						// }

						// if (e.normalParam && !e.normalParam.etpStepNoList2) {
						// 	e.normalParam.etpStepNoList2 = []
						// }
						// if (e.normalParam && e.normalParam.etpStepNoList2 && !Array.isArray(e.normalParam.etpStepNoList2)) {
						// 	e.normalParam.etpStepNoList2 = e.normalParam.etpStepNoList2.replaceAll(/，/ig, ',')
						// 	e.normalParam.etpStepNoList2 = e.normalParam.etpStepNoList2.split(',')
						// }

						// if (e.rptParam && !e.rptParam.firstEtpStepNoList) {
						// 	e.rptParam.firstEtpStepNoList = []
						// }
						// if (e.rptParam && e.rptParam.firstEtpStepNoList && !Array.isArray(e.rptParam.firstEtpStepNoList)) {
						// 	e.rptParam.firstEtpStepNoList = e.rptParam.firstEtpStepNoList.replaceAll(/，/ig, ',')
						// 	e.rptParam.firstEtpStepNoList = e.rptParam.firstEtpStepNoList.split(',')
						// }

						// if (e.rptParam && !e.rptParam.secondEtpStepNoList) {
						// 	e.rptParam.secondEtpStepNoList = []
						// }
						// if (e.rptParam && e.rptParam.secondEtpStepNoList && !Array.isArray(e.rptParam.secondEtpStepNoList)) {
						// 	e.rptParam.secondEtpStepNoList = e.rptParam.secondEtpStepNoList.replaceAll(/，/ig, ',')
						// 	e.rptParam.secondEtpStepNoList = e.rptParam.secondEtpStepNoList.split(',')
						// }

						// if (e.rptParam && !e.rptParam.thirdEtpStepNoList) {
						// 	e.rptParam.thirdEtpStepNoList = []
						// }
						// if (e.rptParam && e.rptParam.thirdEtpStepNoList && !Array.isArray(e.rptParam.thirdEtpStepNoList)) {
						// 	e.rptParam.thirdEtpStepNoList = e.rptParam.thirdEtpStepNoList.replaceAll(/，/ig, ',')
						// 	e.rptParam.thirdEtpStepNoList = e.rptParam.thirdEtpStepNoList.split(',')
						// }

						// if (e.rptParam && !e.rptParam.fourthEtpStepNoList) {
						// 	e.rptParam.fourthEtpStepNoList = []
						// }
						// if (e.rptParam && e.rptParam.fourthEtpStepNoList && !Array.isArray(e.rptParam.fourthEtpStepNoList)) {
						// 	e.rptParam.fourthEtpStepNoList = e.rptParam.fourthEtpStepNoList.replaceAll(/，/ig, ',')
						// 	e.rptParam.fourthEtpStepNoList = e.rptParam.fourthEtpStepNoList.split(',')
						// }
					})
				})

				commitG26(params, this.testReportID,this.reportName).then(res => {
					if (res.success) {
						this.$message.success("创建成功")
						this.$router.push("/v_report_online_manager?type=G26")
					} else {
						this.$message.warn(res.message)
					}
				})
			},


			// 单条选中
			onSelectChange(record, selected) {

			  console.log(this.reportDatas)

        if(this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].landType){
          record.landType = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].landType
        }
				this.outFlowRecord = record
				this.outQueryFlowRecord = record
				// 当前测试报告
				const temLIst = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity]
				// 与当前测试报告同组别的另一份测试报告的位置
				const temIndex = this.reportDatas[this.phaseActivity].templateList.findIndex(v => v.groupName === temLIst.groupName && v.templateType !== temLIst.templateType)
				// 与当前测试报告同组别的另一份测试报告
				const temLIst2 = this.reportDatas[this.phaseActivity].templateList[temIndex]
        let json = jsonBigint({ storeAsString: true })
				// 因为数组push的是浅拷贝
				const record1 = json.parse(json.stringify(record))
				const record2 = json.parse(json.stringify(record))

				if (selected) {
					if (record.flowId == null) {
						this.$message.warn("测试数据为空")
						return
					}
					if (!temLIst.selectedRowKeys.includes(record.uuid)) {

						// 当前测试的增加
						temLIst.selectedRowKeys.push(record1.uuid)
						temLIst.selectedRows.push(record1)
						// temLIst.orderData.push(record1)


						// 与当前测试报告同一组别的增加
            if(temLIst2){
              temLIst2.selectedRowKeys.push(record2.uuid)
              temLIst2.selectedRows.push(record2)
              temLIst2.orderData = temLIst.orderData
            }else{
              temLIst.orderData.push(record1)
            }

					}
				} else {
					for (let i = 0; i < temLIst.selectedRowKeys.length; i++) {
						if (temLIst.selectedRowKeys[i] === record.uuid) {
							// 当前测试的删除
							temLIst.selectedRowKeys.splice(i, 1)
							temLIst.selectedRows.splice(i, 1)
							// temLIst.orderData.splice(i, 1)

							// 与当前测试报告同一组别的删除
              if(temLIst2){
                temLIst2.selectedRowKeys.splice(i, 1)
                temLIst2.selectedRows.splice(i, 1)
                temLIst2.orderData = temLIst.orderData
              }else{
                temLIst.orderData = temLIst.orderData.filter(o => o.uuid != record.uuid)
              }

							break
						}
					}
				}

				// 实时保存
				this.g26TestReportTask(this.reportDatas, this.testReportID)


			},

			// 当前页全部选中
			onSelectAllChange(selected, selectedRows, changeRows) {
        let json = jsonBigint({ storeAsString: true })
				const temLIst = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity]
				// 与当前测试报告同组别的另一份测试报告的位置
				const temIndex = this.reportDatas[this.phaseActivity].templateList.findIndex(v => v.groupName === temLIst.groupName && v.templateType !== temLIst.templateType)
				// 与当前测试报告同组别的另一份测试报告
				const temLIst2 = this.reportDatas[this.phaseActivity].templateList[temIndex]

				if (selected) {
					selectedRows.forEach(item => {
						if (!temLIst.selectedRowKeys.includes(item.uuid) && item.flowId != null) {

              if(this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].landType){
                item.landType = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].landType
              }

							// 因为数组push的是浅拷贝
							const item1 = json.parse(json.stringify(item))
							const item2 = json.parse(json.stringify(item))

							temLIst.selectedRowKeys.push(item1.uuid)
							temLIst.selectedRows.push(item1)
							// temLIst.orderData.push(item1)
              if(temLIst2){
                temLIst2.selectedRowKeys.push(item2.uuid)
                temLIst2.selectedRows.push(item2)
              }else{
                temLIst.orderData.push(item1)
              }


						}
					})

				} else {
					for (let i = 0; i < changeRows.length; i++) {
						if (temLIst.selectedRowKeys.includes(changeRows[i].uuid)) {
							let index = temLIst.selectedRowKeys.indexOf(changeRows[i].uuid)
							temLIst.selectedRowKeys.splice(index, 1)
							temLIst.selectedRows.splice(index, 1)
							// temLIst.orderData.splice(index, 1)

              if(!temLIst2){
                temLIst.orderData = temLIst.orderData.filter(o => o.uuid != changeRows[i].uuid)
              }
						}
					}

				}
        if(temLIst2){
          temLIst2.orderData = temLIst.orderData
        }

				// 实时保存
				this.g26TestReportTask(this.reportDatas, this.testReportID)
			},

			openStepData(record, flag) {
				this.outQueryFlowRecord = record
				this.outFlowRecord = record

				//历史数据处理
				if (null == record.flowInfoList && !flag) {
					this.$refs.stepData.query(record, false)
          return;
				}

				if (record.flowId != null) {
					this.outQueryFlowRecord.flowId = record.flowId
					this.$refs.stepData.query(this.outQueryFlowRecord, false)
				} else {
					this.$message.warn("测试数据为空")
					return
				}
			},




			onSelectChangeFlow(record, handle) {
				if (handle == "查看") {
					this.outQueryFlowRecord.flowId = record.flowId
					this.$refs.stepData.query(this.outQueryFlowRecord, false)
					return
				}

				this.outFlowRecord.flowId = record.flowId

				if (!this.selectedRowKeys.includes(this.outFlowRecord.uuid)) {
					this.selectedRowKeys.push(this.outFlowRecord.uuid)
					this.selectedRows.push(this.outFlowRecord)
					this.orderData.push(this.outFlowRecord)
				}

				this.visibleFlow = false
			},


      deleteSelect(){

        let deleteKeys = this.deleteSelectedRowKeys

        let temLIst = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity]

        this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].selectedRows =
          this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].selectedRows.filter(item => !deleteKeys.includes(item.uuid));

        this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].selectedRowKeys =
          this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].selectedRowKeys.filter(item => !deleteKeys.includes(item));

        this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData =
          this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData.filter(item => !deleteKeys.includes(item.uuid));

        const temIndex = this.reportDatas[this.phaseActivity].templateList.findIndex(v => v.groupName === temLIst.groupName && v.templateType !== temLIst.templateType)

        this.reportDatas[this.phaseActivity].templateList[temIndex].selectedRows =
          this.reportDatas[this.phaseActivity].templateList[temIndex].selectedRows.filter(item => !deleteKeys.includes(item.uuid));

        this.reportDatas[this.phaseActivity].templateList[temIndex].selectedRowKeys =
          this.reportDatas[this.phaseActivity].templateList[temIndex].selectedRowKeys.filter(item => !deleteKeys.includes(item));

        this.reportDatas[this.phaseActivity].templateList[temIndex].orderData =
          this.reportDatas[this.phaseActivity].templateList[temIndex].orderData.filter(item => !deleteKeys.includes(item.uuid));

        if (this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData.length === 0) {
          this._handleProgress()
        }
        // 实时保存
        this.g26TestReportTask(this.reportDatas, this.testReportID)
        this.deleteSelectedRowKeys = []

      },
			// 测试数据删除
			deleteDataOne(record, index) {
				const temLIst = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity]
				// 与当前测试报告同组别的另一份测试报告的位置
				const temIndex = this.reportDatas[this.phaseActivity].templateList.findIndex(v => v.groupName === temLIst.groupName && v.templateType !== temLIst.templateType)
				// 与当前测试报告同组别的另一份测试报告
				const temLIst2 = this.reportDatas[this.phaseActivity].templateList[temIndex]

				temLIst.selectedRows.splice(index, 1)
				temLIst.selectedRowKeys.splice(index, 1)
				temLIst.orderData.splice(index, 1)

				temLIst2.selectedRows.splice(index, 1)
				temLIst2.selectedRowKeys.splice(index, 1)
				temLIst2.orderData.splice(index, 1)

				if (temLIst.orderData.length === 0) {
					this._handleProgress()
				}

				// 实时保存
				this.g26TestReportTask(this.reportDatas, this.testReportID)

			},
      // 测试数据删除
			deleteDataOneWithType(record, index) {
        this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData.filter(o => o.uuid != record.uuid)
        this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].selectedRows = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].selectedRows.filter(o => o.uuid != record.uuid)
        this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].selectedRowKeys = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].selectedRowKeys.filter(o => o != record.uuid)

        const temLIst = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity]
        const temIndex = this.reportDatas[this.phaseActivity].templateList.findIndex(v => v.groupName === temLIst.groupName && v.templateType !== temLIst.templateType)
        if(temIndex != null){
          this.reportDatas[this.phaseActivity].templateList[temIndex].orderData = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData
          this.reportDatas[this.phaseActivity].templateList[temIndex].selectedRowKeys = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].selectedRowKeys
          this.reportDatas[this.phaseActivity].templateList[temIndex].selectedRows = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].selectedRows
        }

        this.$forceUpdate()
        // 实时保存
        this.g26TestReportTask(this.reportDatas, this.testReportID)
			},

			// 测试数据移动
			moveDataOne(index, action) {
				const temLIst = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity]
				const temIndex = this.reportDatas[this.phaseActivity].templateList.findIndex(v => v.groupName === temLIst.groupName && v.templateType !== temLIst.templateType)

				const m = action === 'up' ? index : index + 1
				const n = action === 'up' ? index - 1 : index
				const arr = []

				arr[0] = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData
				arr[1] = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].selectedRowKeys
				arr[2] = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].selectedRows

				arr[3] = this.reportDatas[this.phaseActivity].templateList[temIndex].orderData
				arr[4] = this.reportDatas[this.phaseActivity].templateList[temIndex].selectedRowKeys
				arr[5] = this.reportDatas[this.phaseActivity].templateList[temIndex].selectedRows

				for (let i = 0; i < 6; i++) {
					const tmp1 = arr[i][m]
					arr[i][m] = arr[i][n]
					arr[i][n] = tmp1
				}

				this.$forceUpdate()

				// 实时保存
				this.g26TestReportTask(this.reportDatas, this.testReportID)

			},
			moveDataOneHaveLand(uuid, action) {

        //当前index
				let index = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData
          .findIndex(o => o.uuid == uuid)

        let otherIndex = null;
        //查找上一个index
        if(action === 'up'){
          for (let i = 0; i <  index; i++) {
            let o = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData[i]
            if(o.landType &&  o.landType == this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].landType && o.uuid != uuid){
              otherIndex = i;
            }
          }
        }else{
          for (let i = index ; i <  this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData.length; i++) {
            let o = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData[i]
            if(o.landType &&  o.landType == this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].landType && o.uuid != uuid){
              otherIndex = i;
              break
            }
          }
        }

        if(otherIndex != null){
          [this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData[index],
            this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData[otherIndex]] =
            [this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData[otherIndex],
              this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData[index]]
        }

				this.$forceUpdate()

				// 实时保存
				this.g26TestReportTask(this.reportDatas, this.testReportID)

			},
			handleCancel(target) {
				if (target == 'isShowAddPhase') {
					this.phaseNameCheck = ''
				}

				if (target == 'visible') {
					this._handleProgress()
				}
				this[target] = false

			},
			handleReturn() {
				this.$router.go(-1)
			},
			handleOpen(target) {
				if (target == 'visible' && this.phaseDatas.length === 0) return this.$message.warn('请先添加阶段')
        if(this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].landType){
          this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].selectedRows = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData.filter(o => o.landType && o.landType == this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].landType)
          this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].selectedRowKeys = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData.filter(o => o.landType && o.landType == this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].landType).map(item => item.uuid)
        }else{
          this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].selectedRows = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData
          this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].selectedRowKeys = this.reportDatas[this.phaseActivity].templateList[this.testReportActivity].orderData.map(item => item.uuid)
        }
        this[target] = true
			},
			handleClose(index) {
				switch (index) {
					case 1:
						this.isCloseOne = !this.isCloseOne
						break
					case 2:
						this.isCloseTwo = !this.isCloseTwo
						break
					case 3:
						this.isCloseThree = !this.isCloseThree
						break
					case 4:
						this.isCloseFour = !this.isCloseFour
						break
					case 5:
						this.isCloseFive = !this.isCloseFive
						break
					case 6:
						this.isCloseSix = !this.isCloseSix
						break
					case 7:
						this.isCloseSeven = !this.isCloseSeven
						break
					case 8:
						this.isCloseEight = !this.isCloseEight
						break
					case 9:
						this.isCloseNine = !this.isCloseNine
						break
				}

				// 如果是进度块
				if (index == 4) {
					this._handleProgress()
				}
			},
			// 处理测试报告填写进度
			_handleProgress() {
				this.percentTotal = 0
				this.reportDatas[this.phaseActivity].templateList.forEach(v => {

					const testReport = this.menuOptions.find(e => e.templateType == v.templateType)
					testReport.percent = 0

					// 测试数据选择 40%
					if (v.orderData.length > 0) {
						testReport.percent += 40
						this.percentTotal += 40
					}




					if (testReport.isRPT) {

						// 基本信息填写 30%
						if (v.test) {
							testReport.percent += 30
							this.percentTotal += 30
						}

						// 报告生成逻辑 30%
						let result = 0
						// 如果有没填写的值，result 不等于0
						const numberArr = ['first', 'second', 'third', 'fourth']

						for (let i = 0; i < v.rptParam.rptTypeNum; i++) {
							if (!v.rptParam[numberArr[i] + 'CeStepNo']) result++
							if (!v.rptParam[numberArr[i] + 'RestStepNo']) result++
							if (!v.rptParam[numberArr[i] + 'DischargeStepNo']) result++
							if (!v.rptParam[numberArr[i] + 'EtpStepNoList']) result++
						}

						// 全部填完
						if (v.rptParam.rptInterval && v.rptParam.rptTypeNum && result == 0) {
							testReport.percent += 30
							this.percentTotal += 30
						}

					} else {

						// 基本信息填写 30%
						if (v.rate && v.voltageRange) {
							testReport.percent += 30
							this.percentTotal += 30
						}

						// 报告生成逻辑 30%
						if (testReport.templateType !== 'fastCharge' && v.normalParam.ceStepNo && v.normalParam.etpStepNoList) {
							testReport.percent += 30
							this.percentTotal += 30
						}
						if (testReport.templateType === 'fastCharge' && v.normalParam.ceStepNo && v.normalParam.etpStepNoList) {
							if (v.normalParam.ceStepNo2 && v.normalParam.etpStepNoList2) {
								testReport.percent += 30
								this.percentTotal += 30
							}
							if (!v.normalParam.ceStepNo2) {
								testReport.percent += 30
								this.percentTotal += 30
							}
						}

					}
				})

				this.percentTotal = ((this.percentTotal / 800) * 100).toFixed(0)
			},

			// 校验
			_handleVerify(datas) {
				// 没有填写样品阶段
				if (this.phaseDatas.length === 0) return [false, '', '所有阶段', '所有数据']
				let temCount = 0

				// // 一份报告的测试数据都没选
				for (let i = 0; i < datas.length; i++) {
					const have = datas[i].templateList.filter(filterItem => filterItem.orderData.length > 0)
					if (have.length !== 0) temCount++
				}
				if (temCount === 0) return [false, '', '所有阶段', '所有数据']


				for (let i = 0; i < datas.length; i++) {
					for (let j = 0; j < datas[i].templateList.length; j++) {

						const testReport = this.menuOptions.find(menuItem => menuItem.templateType == datas[i].templateList[j].templateType)
						const testReportName = this.menuOptions[j].title
						const phaseName = this.phaseDatas[i].label

						/* 如果orderData有填数据，其他就必填，如果orderData没数据，其他可以不填 */
						// ------------------------ RPT ------------------------
						if (datas[i].templateList[j].orderData.length > 0 && testReport.isRPT) {

							// 基本信息填写
							if (datas[i].templateList[j].test == '' || datas[i].templateList[j].test == null) {
								return [false, testReportName, phaseName, 'Test']

							}

							if ((datas[i].templateList[j].rptParam.rptInterval == '' || datas[i].templateList[j].rptParam.rptInterval == null) && !datas[i].templateList[j].landType) {
								return [false, testReportName, phaseName, 'RPT间隔']

							}

							if (datas[i].templateList[j].rptParam.rptTypeNum == '' || datas[i].templateList[j].rptParam.rptTypeNum == null) {
								return [false, testReportName, phaseName, 'RPT类别数量']

							}

							// 报告生成逻辑
							const numberArr = ['first', 'second', 'third', 'fourth']

							for (let k = 0; k < datas[i].templateList[j].rptParam.rptTypeNum; k++) {
								if (!datas[i].templateList[j].rptParam[numberArr[k] + 'CeStepNo']) return [false, testReportName, phaseName, '容量能量工步号' + (k + 1)]
								if (!datas[i].templateList[j].rptParam[numberArr[k] + 'RestStepNo']) return [false, testReportName, phaseName, '搁置工步号' + (k + 1)]
								if (!datas[i].templateList[j].rptParam[numberArr[k] + 'DischargeStepNo']) return [false, testReportName, phaseName, '放电工步号' + (k + 1)]
								if (!datas[i].templateList[j].rptParam[numberArr[k] + 'EtpStepNoList']) return [false, testReportName, phaseName, 'ETP工步号' + (k + 1)]
							}


						}
						// ------------------------ 非RPT ------------------------
						if (datas[i].templateList[j].orderData.length > 0 && !testReport.isRPT) {
							// 基本信息填写
							if (datas[i].templateList[j].rate == '' || datas[i].templateList[j].rate == null) {
								return [false, testReportName, phaseName, 'Rate']
							}

							if (datas[i].templateList[j].voltageRange == '' || datas[i].templateList[j].voltageRange == null) {
								return [false, testReportName, phaseName, 'Voltage Range']
							}

							// 报告生成逻辑
							if (datas[i].templateList[j].normalParam.ceStepNo == '' || datas[i].templateList[j].normalParam.ceStepNo == null) {
								return [false, testReportName, phaseName, '工步号1']
							}

							if (datas[i].templateList[j].normalParam.etpStepNoList == '' || datas[i].templateList[j].normalParam.etpStepNoList == null) {
								return [false, testReportName, phaseName, 'ETP/Wh 工步号1']
							}

							// 报告Fast Charge Cycle，ceStepNo2有数据时，etpStepNoList2为必填
							if (testReport.templateType === 'fastCharge' && datas[i].templateList[j].normalParam.ceStepNo2 && !datas[i].templateList[j].normalParam.etpStepNoList2) {
								return [false, testReportName, phaseName, 'ETP/Wh 工步号2']
							}
						}
					}
				}
				return [true, '', '', '']
			},


		}
	}
</script>
<style lang="less" scoped>
	.wrapper {
		padding: 0 10px 10px;
		background-color: #f0f2f5;
	}

	.head_title {
		color: #333;
		padding: 10px 0;
		font-size: 20px;
		font-weight: 600;
	}

	.head_title::before {
		width: 8px;
		background: #1890ff;
		margin-right: 8px;
		content: "\00a0"; //填充空格
	}

	.head_title .subheading {
		font-size: 14px;
		font-weight: 400;
	}

	.menu-content {
		display: flex;
		justify-content: space-around;
		align-items: center;
	}

	.btn-wrap {
		display: flex;
	}

	.btn-wrap1 {
		display: flex;
		font-size: 12px;
	}

	.btn-wrap1 .btn1 {
		text-align: center;
		margin-right: 3px;
	}

	/deep/ .btn-wrap1 .ant-btn {
		padding: 0;
		font-size: 12px;
	}

	.normal-btn {
		padding: 5px 10px;
		color: #fff;
		background-color: #1890ff;
		letter-spacing: 2px;
		cursor: pointer;
	}

	.block {
		height: fit-content;
		padding: 10px;
		background: #fff;
		border-radius: 10px;
		box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
		position: relative;
    //overflow-x: auto;
	}

	.pbi-block-line {
		position: relative;
	}

	.pbi-block-line::before {
		content: "";
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		border: 2px solid;
		border-image: linear-gradient(rgba(0, 216, 247, 0.5) 0%, #00afed 100%) 2 2 2 2;
		border-radius: 10px;
		transition: all 0.5s;
		animation: clippath 5s infinite linear;
	}

	@keyframes clippath {

		0%,
		100% {
			clip-path: inset(0 0 0 95%);
		}

		25% {
			clip-path: inset(95% 0 0 0);
		}

		50% {
			clip-path: inset(0 95% 0 0);
		}

		75% {
			clip-path: inset(0 0 95% 0);
		}
	}

	.top-wrapper {
		display: flex;
	}

	.phase-btn-wrapper {
		display: flex;
	}

	.phase-btn {
		padding: 2px 16px;
		margin-right: 8px;
		cursor: pointer;
		background-color: #eceff7;
		border: 2px solid #fff;
		border-radius: 5px;

		display: flex;
		justify-content: center;
		align-items: center;
	}

	.phase-check {
		background-color: #1890FF;
		color: #fff;
		border: none;
		padding: 2px 23px;
	}

	.btn-icon {
		font-size: 12px;
		color: #999;
	}

	.phase-modal {
		display: flex;
		align-items: center;

	}

	.percent-content {
		display: flex;
		justify-content: space-around;
		align-items: center;
		position: relative;
    overflow-x: auto;
	}

	.all-percent-content {
		position: absolute;
	}

	.percent-content .percent-block {
		cursor: pointer;
		padding: 10px;
		font-size: 12px;
		text-align: center;
		border-radius: 5px;
    height: 140px;
	}

	.percent-content .percent-block-check {
		box-shadow: 0 0 2px rgb(24, 144, 255) inset, 0 0 2px rgb(24, 144, 255);
	}

	.center-wrapper .shrink-btn {
		color: #1890ff;
		cursor: pointer;
		font-size: 12px;
		position: absolute;
		top: 2px;
		right: 10px;
    z-index: 11; /* 确保按钮显示在最上方 */
	}

	.percent-content .progress-block {
		width: calc(100% - 50px);
	}

	.percent-content .open-btn {
		color: #1890ff;
		cursor: pointer;
		font-size: 12px;
		line-height: 1;
		margin-top: 2px;
	}


	.center-wrapper {
		padding-right: 20px;
	}


	.bottom-wrapper {
		padding: 0 0 10px;
		display: flex;
		justify-content: space-between;
	}

	/deep/.ant-tabs-bar {
		margin: 0;
		border: none;
	}

	/deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab {
		margin-right: 8px;
	}

	/deep/.right-content input:not([type="range"]) {
		text-align: center;
	}

	/deep/ input:not([type="range"]) {
		font-size: 12px;
	}

	/deep/ textarea.ant-input {
		font-size: 12px;
	}

	/deep/ .ant-table-thead>tr>th {
		padding: 5px !important;
		font-size: 13px !important;
	}

	/deep/ .ant-table-tbody>tr>td {
		padding: 0px !important;
		height: 32px !important;
		font-size: 12px !important;
	}

	/deep/ .ant-calendar-picker-icon {
		display: none;
	}

	/deep/ .ant-calendar-picker-input.ant-input {
		color: black;
		font-size: 12px;
		border: 0;
		text-align: center;
		padding: 0;
	}

	.ant-modal-body {
		padding: 0;
	}

	/deep/ .ant-btn>i,
	/deep/ .ant-btn>span {
		display: flex;
		justify-content: center;
	}

	/deep/ .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
		color: #fff;
		background: #1890ff;
	}

	.green {
		background-color: #58a55c;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	/deep/ #table1>div>div>div>div>div>div>table>thead {
		height: 64px;
	}

	/deep/ #table1>.ant-table-wrapper>div>div>ul {
		display: none;
	}

	/deep/ .ant-table-pagination.ant-pagination {
		float: right;
		margin: 0;
	}

	.float {
		// width: 36%;
		// float: left;
		// margin-right: 10px;
		// text-align: center;
		padding-bottom: 10px;
	}

	/deep/ .ant-checkbox-group-item {
		display: block;
		width: 100%;
		text-align: left;
	}

	.title {
		font-size: large;
		margin-bottom: 20px;
	}

	/deep/ .ant-table-footer {
		padding: 0;
	}

	/deep/ .ant-table-row-expand-icon {
		margin-right: 0px;
	}



	// 通用
	.mt5 {
		margin-top: 5px;
	}

	.mt10 {
		margin-top: 10px;
	}

	.ml2 {
		margin-left: 2px;
	}

	.ml10 {
		margin-left: 10px;
	}

	.mr2 {
		margin-right: 2px;
	}

	.mr5 {
		margin-right: 5px;
	}

	.mb5 {
		margin-bottom: 5px;
	}

	.mr10 {
		margin-right: 10px;
	}

	.fs14 {
		font-size: 14px;
	}

	.none {
		pointer-events: none
	}

	/* 标题 */
	.flex-sb-center-row {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.left-content {
		width: 60%;
		margin-right: 10px;
	}

	/deep/.left-content .remark-input .ant-input {
		padding: 2px;
	}

	.right-content {
		width: 40%;
	}

	.right-content .all-checkbox {
		padding-bottom: 5px;
		margin-bottom: 5px;
		border-bottom: 1px solid #e9e9e9;
	}



	.footer-btn {
		width: 100%;
		height: 32px;
		border: 1px solid #e8e8e8;
		background: #fff;
		color: #999;
		font-size: 16px;
		display: flex;
		justify-content: center;
		align-items: center;
		cursor: pointer;
	}

	.footer-btn:hover {
		color: #1890ff;
	}

	// 组件
	/deep/ .ant-steps {
		padding: 15px 50px;
	}

	/* 左边表格 */
	/deep/ .left-content .ant-table-body {
		min-height: calc(100vh - 220px - 181px);
		border: 1px solid #e8e8e8;
		overflow: auto;
	}

	/* 右边表格 */
	/deep/ .right-content .ant-table-body {
		min-height: calc((100vh - 290px) / 2);
		border: 1px solid #e8e8e8;
		overflow: auto;
	}

	/deep/ .bottom-wrapper .ant-table-thead {
		position: sticky;
		top: 0;
		z-index: 2;
	}

	/deep/ .bottom-wrapper .ant-table-placeholder {
		border: none !important;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		padding: 0;
		z-index: 0;
	}

	/deep/ .right-content .ant-empty-normal {
		margin: -2px 0;
	}

	/deep/ .ant-empty-image {
		display: none;
	}

	/deep/ .ant-checkbox-group {
		width: 100%;
		display: flex;
		flex-wrap: wrap;
	}

	/deep/ .ant-checkbox-group-item {
		font-size: 12px;
		width: 23%;
	}

	/deep/ .ant-radio-inner {
		top: 1px;
		left: 1px;
	}

	/deep/ .ant-table-body::-webkit-scrollbar {
		height: 10px;
		width: 5px;
	}

	/deep/ .ant-table-body::-webkit-scrollbar-thumb {
		-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
		border-radius: 0;

		background: #dddbdb;
	}

	/deep/ .ant-table-body::-webkit-scrollbar-track {
		-webkit-box-shadow: 0;
		border-radius: 0;
		background: #f1f1f1;
	}

	/deep/ .ant-select {
		font-size: 12px;
	}

	/deep/ .ant-select-selection__rendered {
		margin-right: 0px;
	}

	/deep/ .ant-form-item {
		margin-bottom: 0;
		font-size: 12px;
	}

	/deep/ .ant-popover-buttons {
		display: flex !important;
		flex-direction: column !important;
		margin-bottom: 15px;
	}

	.tips {
		color: #1890ff;
	}

	.button-tips {
		display: flex;
		flex-direction: column;
	}

	/* 霓虹灯按钮 */

	.btn {
		width: 100%;
		height: 32px;
		border: 1px solid;
		background-color: transparent;
		text-transform: uppercase;
		font-size: 12px;
	}

	.btn:hover {
		color: white;
		border: 0;
	}

	/deep/ .ant-table-thead>tr>th .ant-table-header-column {
		width: 100%;
	}

  /deep/ #outTable .ant-table-thead > tr > th {
    padding: 2px 0 !important;
    font-size: 12px !important;
  }

</style>