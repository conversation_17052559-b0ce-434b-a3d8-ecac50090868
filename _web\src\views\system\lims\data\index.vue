<template>
	<div class="wrapper">
		<!-- <div class="export-btn">
			<a-button style="width: 100%;" type="primary" @click="exportData">导出</a-button>
		</div> -->

		<div class="flex-sb-center-row">
			<div class="head_title">在线数据提取</div>
			<!-- <a-button
				type="primary"
				:class="{
					'animate__animated animate__pulse animate__infinite': orderData.length !== 0 && filterData.length !== 0
				}"
				@click="exportData"
				>开始任务</a-button
			> -->
			<div
				class="normal-btn"
				:class="{
					'streamer-btn anima': orderData.length !== 0 && filterData.length !== 0
				}"
				@click="exportData"
			>
				<span></span>
				<span></span>
				<span></span>
				<span></span>
				开始任务
			</div>
		</div>

		<div class="all-wrapper">
			<div class="left-content block">
				<div class="flex-sb-center-row">
					<strong>一、测试数据选择</strong>
          <div style="float: right" class="pbi-btn-style">
            <a-popconfirm placement="topRight" title="确认清空？" @confirm="() => {
            orderData = [];
            selectedRowKeys = [];
            selectedRows = []
          }">
              <a-button style="margin-top: -5px;margin-right: 5px">清空</a-button>
            </a-popconfirm>

            <a-popconfirm placement="topRight" title="确认删除？" @confirm="deleteSelect">
              <a-button style="margin-top: -5px;">删除</a-button>
            </a-popconfirm>
          </div>
				</div>
        <div  ref="tableContainer">

          <a-table
            class="mt10"
            bordered
            id="outTable"
            :columns="orderColumns"
            :data-source="orderData"
            :row-selection="deleteRowSelection"
            childrenColumnName="child"
            :rowKey="record => record.uuid"
            :pagination="false"
          >

            <template slot="action" slot-scope="text, record, index, columns">
              <a-tooltip placement="top" title="删除" arrow-point-at-center>
                <a @click="deleteDataOne(record, index)" style="text-align: center">
                  <a-icon type="delete" style="font-size: large;margin-right: 5px"/>
                </a>
              </a-tooltip>

              <a-tooltip placement="top" title="拖拽修改位置" arrow-point-at-center >
                <a-icon :style="setMultStyle" style="color: #1890ff;font-size: large;" heignt="18" width="18" class="drag" type="unordered-list"/>
              </a-tooltip>


            </template>

            <template slot="celltestcode" slot-scope="text, record, index, columns">
              <a @click="openStepData(record)" v-if="record.flowId != null" style="text-align: center">{{ text }}</a>
              <span v-else style="text-align: center">{{ text }}</span>
            </template>
            <!--					<template slot="action" slot-scope="text, record, index, columns">
                        <a-tooltip placement="top" title="删除" arrow-point-at-center>
                          <a @click="deleteDataOne(record, index)" style="text-align: center">
                            <a-icon type="delete" style="font-size: large;margin-right: 3px"/>
                          </a>
                        </a-tooltip>

                        <a-tooltip placement="top" title="上移" arrow-point-at-center v-if="index != 0">
                          <a @click="moveUp(orderData,index)" style="text-align: center">
                            <a-icon type="arrow-up" style="font-size: large;margin-right: 3px"/>
                          </a>
                        </a-tooltip>
                        <a-tooltip placement="top" title="下移" arrow-point-at-center v-if="index != orderData.length -1">
                          <a @click="moveDown(orderData,index)" style="text-align: center">
                            <a-icon type="arrow-down" style="font-size: large"/>
                          </a>
                        </a-tooltip>
                      </template>-->
            <template
              slot="dataPath"
              slot-scope="text, record, index, columns">
              <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
                <template slot="title">
                  {{text?text:record.testcontent}}
                </template>
                {{text?text:record.testcontent}}
              </a-tooltip>
            </template>
            <!-- <template slot="footer">
              <a-button
                @click="openTestOrder"
                style="width: 100%;"
                ><a-icon type="plus"></a-icon
              ></a-button>
            </template> -->
            <template slot="footer">
              <div
                class="footer-btn"
                :class="{
								'plus-btn': orderData.length === 0
							}"
                @click="openTestOrder"
              >
                <span></span>
                <span></span>
                <span></span>
                <span></span>
                <a-icon type="plus"></a-icon>
              </div>

              <!-- <a-button
                @click="openTestOrder"

                ><a-icon type="plus"></a-icon
              ></a-button> -->
            </template>
          </a-table>
        </div>
			</div>
			<div class="right-content">
				<div class="block" id="export">
					<div class="flex-column">
						<strong>二、输出结果定义</strong>
						<div class="mt10">
							<a-radio-group name="radioGroup" buttonStyle="solid" v-model="dataType" @change="changeDataType">
								<a-radio v-for="item in radioMenu" :key="item.value" :value="item.value">
									{{ item.name }}
								</a-radio>
							</a-radio-group>
						</div>
						<div class="all-checkbox mt10">
							<a-checkbox
								:indeterminate="indeterminate"
								:checked="checkAll"
								@change="onCheckAllChange"
								style="text-align: center"
								id="checkAll"
							>
								{{ checkAll ? "取消" : "全选" }}
							</a-checkbox>
						</div>
						<div class="checkbox">
							<a-checkbox-group
								v-if="dataType == 'step'"
								@change="onChange"
								v-model="stepChecked"
								:options="stepOptions"
							/>
							<a-checkbox-group
								v-if="dataType == 'data'"
								@change="onChange"
								v-model="dataChecked"
								:options="dataOptions"
							/>
							<a-checkbox-group
								v-if="dataType == 'cyc'"
								@change="onChange"
								v-model="cycChecked"
								:options="cycOptions"
							/>
              <a-checkbox-group
								v-if="dataType == 'stepInfo'"
								@change="onChange"
								v-model="stepInfoChecked"
								:options="stepInfoOptions"
							/>
						</div>
					</div>
				</div>
				<div class="block mt10">
					<div>
						<strong>三、数据处理逻辑配置 </strong>
            <div style="float: right" class="pbi-btn-style">
              <a-popconfirm placement="topRight" title="确认清空？" @confirm="() => {
            filterData = [];
            deleteLogicSelectedRowKeys = []
          }">
                <a-button style="margin-top: -5px;margin-right: 5px">清空</a-button>
              </a-popconfirm>

              <a-popconfirm placement="topRight" title="确认删除？" @confirm="deleteLogicSelect">
                <a-button style="margin-top: -5px;">删除</a-button>
              </a-popconfirm>
            </div>
						<a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
							<a-icon class="tips" type="question-circle" />
							<template slot="title">
								<span>
									1、【值】列可从Excel复制一列数据，会自动生成多行数据<br />
									2、每一行的参数1，参数2之间是“与” 的关系<br />
									3、行与行之间是“或”的关系
								</span>
							</template>
						</a-tooltip>

						<!-- <a-popconfirm
							title="请选择需要查看的示例"
							ok-text="循环容量&能量处理逻辑示例"
							cancel-text="DCR处理逻辑示例"
							@confirm="handleCheckFile(1)"
							@cancel="handleCheckFile(0)"
						>
							<a-button style="font-size:12px;float:right;height:100%" type="link">
								查看示例
							</a-button>
						</a-popconfirm> -->

						<a-popover placement="topLeft">
							<template slot="title">
								<div class="button-tips">
									<p>请选择需要查看的示例</p>
								<a-button class="mb5" @click="handleCheckFile(0)" type="primary">
									DCR处理逻辑示例
								</a-button>
								<a-button @click="handleCheckFile(1)" type="primary">
									循环容量&能量处理逻辑示例
								</a-button>
								</div>

							</template>
							<a-button style="font-size:12px;float:right;height:24px" type="link">
								查看示例
							</a-button>
						</a-popover>

						<div class="mt10">
							<a-table :row-selection="{ selectedRowKeys: deleteLogicSelectedRowKeys,
                onChange: deleteLogicRowOnChange, columnWidth:20}" class="mt10" bordered :row-key="record => record.id"
                       :columns="filterColumns" :data-source="filterData" :pagination="false">
								<template slot="action" slot-scope="text, record, index, columns">
									<a @click="() => filterData.splice(index, 1)" style="text-align: center">删除</a>
								</template>

								<template slot="key" slot-scope="text, record, index, columns">
									<a-select
										style="width: 100%"
										v-model="record[columns.dataIndex]"
										:allow-clear="true"
										@change="(value, option) => handleDataSelectChange(value, option, columns.dataIndex, record)"
										:options="dataType == 'step' ? stepOptions : dataType == 'data' ? dataOptions : dataType == 'cyc'?cycOptions:stepInfoOptions"
									/>
								</template>

								<template slot="value" slot-scope="text, record, index, columns">
									<a-input
										@paste="copyFromExcel($event, columns.dataIndex, index)"
										style="width: 100%;text-align: center"
										v-model="record[columns.dataIndex]"
									/>
								</template>
								<template slot="footer">
									<!-- <a-button @click="addFilterData" style="width: 100%;z-index: 2"
										><a-icon type="plus"></a-icon
									></a-button> -->
									<div
										class="footer-btn"
										:class="{
											'plus-btn': orderData.length !== 0 && filterData.length === 0
										}"
										@click="addFilterData"
									>
										<span></span>
										<span></span>
										<span></span>
										<span></span>
										<a-icon type="plus"></a-icon>
									</div>
								</template>
							</a-table>
						</div>
					</div>
					<!-- <div class="mt10">
					<strong >五、点击导出</strong>
					<a-button class="mt10" style="width: 100%;" type="primary" @click="exportData">开始执行</a-button>
				</div> -->
				</div>
			</div>
		</div>

		<a-modal
			title="导出"
			:width="800"
			:height="600"
			:bodyStyle="{ padding: 0 }"
			:visible="visible1"
			:confirmLoading="confirmLoading"
			@ok="handleSubmit"
			style="padding: 0"
			@cancel="handleCancel1"
		>
			<a-form :form="form">
				<a-row :gutter="24">
					<a-col :md="18" :sm="24">
						<a-form-item label="任务名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
							<a-input
								placeholder="请输入任务名称"
								v-decorator="['taskName', { rules: [{ required: true, message: '请输入任务名称！' }] }]"
							/>
						</a-form-item>
					</a-col>
				</a-row>

				<a-row :gutter="24">
					<a-col :md="18" :sm="24">
						<a-form-item label="Excel格式" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-select
								v-decorator="['excelType', { rules: [{ required: true, message: '请选择Excel格式！' }] }]"
								default-value="one"
								style="width: 100%"
								placeholder="请选择Excel格式"
							>
								<a-select-option value="one">
									保存于同一个Sheet中
								</a-select-option>
								<a-select-option value="more">
									保存于不同的Sheet中
								</a-select-option>
							</a-select>
						</a-form-item>
					</a-col>
				</a-row>
			</a-form>
		</a-modal>

		<a-modal
			title="测试数据选择"
			width="90%"
			:height="600"
			centered
			:bodyStyle="{ padding: 0 }"
			:visible="visible"
			style="padding: 0"
			:maskClosable="false"
			@cancel="handleCancel"
		>

      <pbiSearchContainer>
        <pbiSearchItem :span="4" label='委托单号' >
          <a-input class="input-short" v-model="queryParam.folderno"  @keyup.enter="$refs.table.refresh()"
                    />
        </pbiSearchItem>
        <pbiSearchItem :span="4" label='主题' >
          <a-input class="input-short" v-model="queryParam.theme"  @keyup.enter="$refs.table.refresh()"
                    />
        </pbiSearchItem>
				<pbiSearchItem :span="4" label='测试编码'>
					<a-input class="input-short" v-model="queryParam.celltestcode" @keyup.enter="$refs.table.refresh()"
					/>
				</pbiSearchItem>
        <pbiSearchItem :span="3" label='测试项目别名'>
          <a-input class="input-short" v-model="queryParam.alias" @keyup.enter="$refs.table.refresh()"
                     />
        </pbiSearchItem>
        <pbiSearchItem :span="3" label='存储天数' >
          <a-input class="input-short" style="width: 95%" v-model="queryParam.day" @keyup.enter="$refs.table.refresh()"
                     />
        </pbiSearchItem>
        <pbiSearchItem :span="3" label='测试状态' >
          <a-select v-model="queryParam.endstatusflag" @change="$refs.table.refresh()" style="width: 100%" :allow-clear="true" dropdown-class-name="dropdownClassName">
            <a-select-option value="0">
              测试中
            </a-select-option>
            <a-select-option value="1">
              完成
            </a-select-option>
          </a-select>
        </pbiSearchItem>
        <pbiSearchItem :span="3" type='btn' class="search-container">
          <div class="secondary-btn">
            <a-button @click="$refs.table.refresh()" class="mr12" type="primary">
              查询
            </a-button>
          </div>
          <div class="secondary-btn">
            <a-button @click="reset()" class="mr12">
              重置
            </a-button>
          </div>
<!--          <div class='toggle-btn'>
            <a-button size='small' type='link' @click='isShowAllSearch = !isShowAllSearch'>
              {{ isShowAllSearch ? '收起' : '展开' }}
              <span v-if='isShowAllSearch'>
                <a-icon type='double-left' />
              </span>
              <span v-else>
                <a-icon type='double-right'/>
              </span>
            </a-button>
          </div>-->
        </pbiSearchItem>

      </pbiSearchContainer>
			<s-table
				:columns="columns"
				:data="loadData"
				bordered
				:rowKey="record1 => record1.uuid"
				ref="table"
        :scroll="{x:800}"
				:row-selection="{
					selectedRowKeys: selectedRowKeys,
					selectedRows: selectedRows,
					onSelect: onSelectChange,
					onSelectAll: onSelectAllChange,
					columnWidth: 30
				}"
			>
				<template slot="celltestcode" slot-scope="text, record, index, columns">
          <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
            <template slot="title">
              {{text}}
            </template>
            <a @click="openStepData(record)" v-if="record.flowId != null" style="text-align: center">{{ text }}</a>
            <span v-else style="text-align: center">{{ text }}</span>
          </a-tooltip>
				</template>

        <template slot="action1" slot-scope="text, record, index, columns">
          <template v-if="record.showHide">
            <a @click="showData(record)" v-if="record.showHide" style="text-align: center">初始化</a>
            <a-divider v-if="record.children != null" type="vertical" />
          </template>

          <a @click="hideData(record)" v-if="record.children != null || record.isChild" style="text-align: center">隐藏</a>
				</template>

        <template
          slot="theme"
          slot-scope="text, record, index, columns">
          <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
            <template slot="title">
              {{text}}
            </template>
            {{text}}
          </a-tooltip>
        </template>
        <template
          slot="dataPath"
          slot-scope="text, record, index, columns">
          <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
            <template slot="title">
              {{text?text:record.testcontent}}
            </template>
            {{text?text:record.testcontent}}
          </a-tooltip>
        </template>
			</s-table>
			<template slot="footer">
				<a-button key="back" @click="handleCancel">
					关闭
				</a-button>
			</template>
		</a-modal>

		<a-modal
			title="测试数据选择"
			:width="1000"
			:height="300"
			:bodyStyle="{ padding: 0 }"
			:visible="visibleFlow"
			style="padding: 0"
			:maskClosable="false"
			:centered="true"
			@cancel="handleCancelFlow"
		>
			<a-table
				:columns="flowInfoColumns"
				:dataSource="flowInfoData"
				bordered
				:rowKey="record => record.uuid"
				ref="table"
				:pagination="false"
			>
				<template slot="celltestcode" slot-scope="text, record, index, columns">

          <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
            <template slot="title">
              {{text}}
            </template>
            <a @click="openStepData(record)" v-if="record.flowId != null" style="text-align: center">{{ text }}</a>
            <span v-else style="text-align: center">{{ text }}</span>
          </a-tooltip>


				</template>

				<template slot="action" slot-scope="text, record, index, columns">
					<a @click="onSelectChangeFlow(record, '选中')" style="text-align: center">选中</a>
					<a-divider type="vertical" />
					<a @click="onSelectChangeFlow(record, '查看')" style="text-align: center">查看</a>
				</template>
        <template
          slot="dataPath"
          slot-scope="text, record, index, columns">
          <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
            <template slot="title">
              {{text?text:record.testcontent}}
            </template>
            {{text?text:record.testcontent}}
          </a-tooltip>
        </template>
			</a-table>
			<template slot="footer">
				<a-button key="back" @click="handleCancelFlow">
					关闭
				</a-button>
			</template>
		</a-modal>

		<step-data ref="stepData"></step-data>

		<a-drawer
			:bodyStyle="{ height: '100%' }"
			placement="right"
			:closable="false"
			width="70%"
			:visible="isShowExample"
			@close="handleCloseExample"
		>
			<iframe :src="iframeUrl" width="100%" height="100%"></iframe>
		</a-drawer>


	</div>
</template>
<script>
import { tLimsTestdataSchedulePageList, shenghongDataFilterExport,hideData,showData } from "@/api/modular/system/limsManager"
import { STable } from "@/components"
import moment from "moment"

import stepData from "../folder/stepData"

import { mapGetters } from "vuex"
import Sortable from 'sortablejs';

export default {
	components: {
		STable,
		stepData
	},
  props: {
    width:{
      type: Number,
      default: 0
    }
  },
	data() {
		return {
      enabled:true,
      isShowAllSearch:false,
			iframeUrl: "",
			param: null,
			outFlowRecord: null,
			outQueryFlowRecord: null,
			inFlowActionName: null,
			visibleFlow: false,
			isShowExample: false,
			current: 0,
			stepStatus: ["wait", "wait", "wait", "wait"],
			radioMenu: [
				{ name: "工步数据表", value: "step" },
				{ name: "详细数据表", value: "data" },
				{ name: "循环数据表", value: "cyc" },
				{ name: "工步信息表", value: "stepInfo" },
			],
			flowInfoColumns: [

				{
					title: "测试编码",
					dataIndex: "barCode",
					align: "center",
					width: 100
				},
				{
					title: "设备编号",
					width: 30,
					align: "center",
					dataIndex: "unitNum"
					//scopedSlots: {customRender: 'updateText'},
				},
				{
					title: "通道编号",
					width: 30,
					align: "center",
					dataIndex: "channelId"
					//scopedSlots: {customRender: 'updateText'},
				},
				{
					title: "数据位置",
					width: 30,
					align: "center",
					dataIndex: "dataPath",
					scopedSlots: {customRender: 'dataPath'},
				},
				{
					title: "开始时间",
					width: 30,
					align: "center",
					dataIndex: "startTime",
					customRender: (text, record, index) => {
						if (null != text) {
							return moment(text).format("YYYY-MM-DD")
						}
						return text
					}
					//
					//scopedSlots: {customRender: 'updateText'},
				},
				{
					title: "结束时间",
					width: 30,
					align: "center",
					dataIndex: "endTime",
					customRender: (text, record, index) => {
						if (null != text) {
							return moment(text).format("YYYY-MM-DD")
						}
						return text
					}
				},
				{
					title: "操作",
					width: 60,
					align: "center",
					dataIndex: "action",
					scopedSlots: { customRender: "action" }
				}
			],
			flowInfoData: [],
			checkedListStep: [],
			checkedListData: [],
			checkedListCyc: [],
			indeterminate: false,
			checkAll: false,
			stepChecked: [],
			dataChecked: [],
			cycChecked: [],
      stepInfoChecked: [],
			filterData: [],
      currentRequestId:null,
			loadData: parameter => {
				this.loadDataList = []
        // 生成当前请求的唯一标识
        const currentRequestId = Date.now();
        this.currentRequestId = currentRequestId;
				return tLimsTestdataSchedulePageList(Object.assign(parameter, this.queryParam))
					.then(res => {
            // 只处理最新请求的响应
            if (this.currentRequestId === currentRequestId) {
              return res.data;
            }
            // 返回一个永远不会resolve的Promise来忽略旧请求
            return new Promise(() => {});
					})

			},
			dataType: "step",
			orderData: [],
			visible: false,
			confirmLoading: false,
			visible1: false,
			height: 200,
			stepOptions: [
				{ label: "工步序号", value: "StepNum", key: "stepNum" },
				{ label: "工步号", value: "StepId", key: "stepId" },
				{ label: "工步名", value: "StepName", key: "stepName" },
				{ label: "工步时间", value: "StepTime", key: "stepTimeString" },

				{ label: "循环号", value: "CycleId", key: "cycleId" },
				{ label: "设备号", value: "UnitNum", key: "unitNum" },
				{ label: "通道号", value: "ChannelId", key: "channelId" },
				{ label: "电池条码", value: "BarCode", key: "barCode" },

				{ label: "容量/Ah", value: "Capacity", key: "capacity" },
				{ label: "能量/Wh", value: "Energy", key: "energy" },
				{ label: "起始电压/V", value: "BeginVoltage", key: "beginVoltage" },
				{ label: "终止电压/V", value: "EndVoltage", key: "endVoltage" },
				{ label: "起始电流/A", value: "StartCurrent", key: "startCurrent" },
				{ label: "终止电流/A", value: "EndCurrent", key: "endCurrent" },
				{label: '累计充电容量/Ah', value: 'CumulativeChargeCapacity', key: 'cumulativeChargeCapacity'},
        {label: '累计放电容量/Ah', value: 'CumulativeDischargeCapacity', key: 'cumulativeDischargeCapacity'},
        {label: '累计充电能量/Wh', value: 'CumulativeChargeEnergy', key: 'cumulativeChargeEnergy'},
        {label: '累计放电能量/Wh', value: 'CumulativeDischargeEnergy', key: 'cumulativeDischargeEnergy'},
				{ label: "绝对时间", value: "AbsoluteTime", key: "absoluteTime" },
				/*{label: '恒流比', value: 'CCCapacityRate', key: 'cccapacityRate'},*/
				{ label: "单体起始温度1", value: "startTemp1", key: "startTemp1" },
				{ label: "单体结束温度1", value: "endTemp1", key: "endTemp1" },
				{ label: "单体起始温度2", value: "startTemp2", key: "startTemp2" },
				{ label: "单体结束温度2", value: "endTemp2", key: "endTemp2" },
				{ label: "单体起始温度3", value: "startTemp3", key: "startTemp3" },
				{ label: "单体结束温度3", value: "endTemp3", key: "endTemp3" },
				{ label: "单体起始温度4", value: "startTemp4", key: "startTemp4" },
				{ label: "单体结束温度4", value: "endTemp4", key: "endTemp4" },
				/*{ label: "起始膨胀位移监测", value: "StartExForce", key: "startExForce" },
				{ label: "结束膨胀位移监测", value: "EndExForce", key: "endExForce" },
				{ label: "最大膨胀位移监测", value: "MaxExForce", key: "maxExForce" },
				{ label: "最小膨胀位移监测", value: "MinExForce", key: "minExForce" },*/
				/*{label: '起始温度', value: 'BeginTemperature', key: 'beginTemperature'},
          {label: '终止温度', value: 'EndTemperature', key: 'endTemperature'},*/
				{ label: "恒流比", value: "CCCapacityRate", key: "cccapacityRate" },
				{ label: "起始SOC", value: "StartSocCalculate", key: "startSocCalculate" },
				{ label: "终止SOC", value: "EndSocCalculate", key: "endSocCalculate" },
				{ label: "起始膨胀力", value: "StartPressure", key: "startPressure" },
				{ label: "结束膨胀力", value: "EndPressure", key: "endPressure" },
				{ label: "最大膨胀力", value: "MaxPressure", key: "maxPressure" },
				{ label: "最小膨胀力", value: "MinPressure", key: "minPressure" },
        { label: "所有温度", value: "allTem", key: "allTem" },
        { label: "中值电压", value: "MeanVoltage", key: "meanVoltage" }
				/*{label: '开路电压/V', value: 'OpenVoltage', key: 'openVoltage'},*/
			],
      stepInfoOptions: [
				{ label: "工步号", value: "StepID", key: "stepId" },
				{ label: "工步名称", value: "StepName", key: "stepName" },
				{ label: "参数", value: "StepPara", key: "stepPara" },
				{ label: "截止参数", value: "CutoffCondition", key: "cutoffCondition" },
				{ label: "记录条件", value: "Recordcond", key: "recordcond" },
				{ label: "创建时间", value: "CreateTime", key: "createTime" }


			],
			dataOptions: [
				{ label: "记录序号", value: "RecordId", key: "recordId" },
				{ label: "设备号", value: "UnitNum", key: "unitNum" },
				{ label: "通道号", value: "ChannelId", key: "channelId" },

				{ label: "循环号", value: "CycleId", key: "cycleId" },
				{ label: "工步序号", value: "StepNum", key: "stepNum" },
				{ label: "工步号", value: "StepId", key: "stepId" },
				{ label: "工步名", value: "StepName", key: "stepName" },
				{ label: "电池条码", value: "BarCode", key: "barCode" },
				{ label: "绝对时间", value: "AbsoluteTime", key: "absoluteTime" },
				{ label: "记录时间", value: "RecordTime", key: "recordTime" },
				{ label: "工步时间", value: "StepTime", key: "stepTime" },

				{ label: "电压/V", value: "Voltage", key: "voltage" },
				{ label: "电流/A", value: "Current", key: "current" },
				{ label: "容量/Ah", value: "Capacity", key: "capacity" },
				{ label: "能量/Wh", value: "Energy", key: "energy" },
				{ label: "功率/W", value: "ActivePower", key: "activePower" },
				/*{label: '内阻/mΩ', value: 'Resistance', key: 'resistance'},*/
        { label: "累计充电容量/Ah", value: "CycChagerCapacity", key: "cycChagerCapacity" },
        { label: "累计放电容量/Ah", value: "CycDischagerCapacity", key: "cycDischagerCapacity" },
        { label: "累计充电能量/Wh", value: "CycChagerEnergy", key: "cycChagerEnergy" },
        { label: "累计放电能量/Wh", value: "CycDischagerEnergy", key: "cycDischagerEnergy" },
				/* {label: '设备温度/℃', value: 'Temperature', key: 'temperature'},*/
				{ label: "总容量/Ah", value: "TotalCapacity", key: "totalCapacity" },
				{ label: "单体温度1", value: "auxTem1", key: "auxTem1" },
				{ label: "单体温度2", value: "auxTem2", key: "auxTem2" },
				{ label: "单体温度3", value: "auxTem3", key: "auxTem3" },
				{ label: "单体温度4", value: "auxTem4", key: "auxTem4" },
				{ label: "总能量", value: "TotalEnergy", key: "totalEnergy" },
				{ label: "总能量-ETP", value: "TotalEnergyEtp", key: "totalEnergyEtp" },
				{ label: "SOC", value: "SocCalculate", key: "socCalculate" },
				{ label: "温箱温度/℃", value: "IncubatorTemp", key: "incubatorTemp" },
				{ label: "温箱湿度/%RH", value: "IncubatorHum", key: "incubatorHum" },
				{ label: "单体压力1(Kg)", value: "AuxPresureList", key: "auxPresureList" },
				{ label: "所有温度", value: "allTem", key: "allTem" }
				/*{ label: "单体电压串", value: "AuxVoltageList", key: "auxVoltageList" }*/
			],
			cycOptions: [
				{ label: "设备号", value: "UnitNum", key: "unitNum" },
				{ label: "通道号", value: "ChannelId", key: "channelId" },
				{ label: "循环号", value: "CycleId", key: "cycleId" },
				/*{ label: "工步号", value: "StepId", key: "stepId" },*/
				{ label: "电池条码", value: "BarCode", key: "barCode" },

				{ label: "充电容量/Ah", value: "ChargeCapacity", key: "chargeCapacity" },
				{ label: "放电容量/Ah", value: "DisChargeCapacity", key: "disChargeCapacity" },
				{ label: "充电能量/Wh", value: "ChargeEnergy", key: "chargeEnergy" },
				{ label: "放电能量/Wh", value: "DisChargeEnergy", key: "disChargeEnergy" },
				// { label: "放电中值电压/V", value: "DischargeMeanVoltage", key: "dischargeMeanVoltage" },
				{ label: "充放电效率", value: "ChargeCapacityRatio", key: "chargeCapacityRatio" },

				/*{ label: "内阻/mΩ", value: "Resistance", key: "resistance" },*/
				{ label: "充电时间", value: "ChargeTime", key: "chargeTimeString" },
				{ label: "放电时间", value: "DischargeTime", key: "dischargeTimeString" },
				// { label: "衰减比例/%", value: "AttenuationRation", key: "attenuationRation" }
				/*{label: '绝对时间', value: 'AbsoluteTime', value: 'absoluteTime'},*/
			],
			show: false,
			labelCol: {
				sm: {
					span: 11
				}
			},
			wrapperCol: {
				sm: {
					span: 13
				}
			},
			queryParam: {},
			data: [],
			headData: [],
			allAddress: null,
			// 测试项目选择表头
			orderColumns: [
				{
					title: "操作",
					align: "center",
					width: 40,
					scopedSlots: { customRender: "action" }
				},
				{
					title: "序号",
					align: "center",
					width: 20,
					customRender: (text, record, index) => index + 1
				},
				{
					title: "委托单号",
					dataIndex: "folderno",
					align: "center",
					width: 45
				},
				// {
				// 	title: "测试项目编码",
				// 	width: 70,
				// 	align: "center",
				// 	dataIndex: "testcode"
				// 	//scopedSlots: {customRender: 'updateText'},
				// },
				{
					title: "测试项目别名",
					width: 65,
					align: "center",
					dataIndex: "alias"
					//scopedSlots: {customRender: 'updateText'},
				},
				{
					title: "样品编号",
					width: 50,
					align: "center",
					dataIndex: "orderno"
				},
				{
					title: "测试编码",
					width: 50,
					align: "center",
					dataIndex: "celltestcode",
					scopedSlots: { customRender: "celltestcode" }
				},
				{
					title: "数据位置",
					width: 50,
					align: "center",
					dataIndex: "dataPath",
					ellipsis: true,
					scopedSlots: {customRender: 'dataPath'},
				},
				{
					title: "通道编号",
					width: 45,
					align: "center",
					dataIndex: "equiptcode",
          customRender: (text, record, index) => {
					  return (null != text?text:"")+"-"+ (null != record.channelno?record.channelno:"")
          }
				},
        {
          title: "存储天数",
          width: 50,
          align: "center",
          dataIndex: "day"
        },
			],
			filterColumns: [

				{
					title: "序号",
					align: "center",
					width: 40,
					customRender: (text, record, index) => {
					  record.id = index + 1
					  return index + 1
          }
				},
				{
					title: "参数1",
					dataIndex: "key1",
					align: "center",
					width: 100,
					scopedSlots: { customRender: "key" }
				},
				{
					title: "值",
					width: 100,
					align: "center",
					dataIndex: "value1",
					scopedSlots: { customRender: "value" }
				},
				{
					title: "参数2",
					dataIndex: "key2",
					align: "center",
					width: 100,
					scopedSlots: { customRender: "key" }
				},
				{
					title: "值",
					width: 100,
					align: "center",
					dataIndex: "value2",
					scopedSlots: { customRender: "value" }
				},
				{
					title: "参数3",
					dataIndex: "key3",
					align: "center",
					width: 100,
					scopedSlots: { customRender: "key" }
				},
				{
					title: "值",
					width: 100,
					align: "center",
					dataIndex: "value3",
					scopedSlots: { customRender: "value" }
				}

				/*,
          {
            title: '参数4',
            dataIndex: 'key4',
            align: 'center',
            width: 90,
            scopedSlots: {customRender: 'key'},
          }, {
            title: '值',
            width: 90,
            align: 'center',
            dataIndex: 'value4',
            scopedSlots: {customRender: 'value'},
          }*/
			],
			columns: [
				{
					title: "操作",
					align: "center",
					width: 105,
          scopedSlots: {customRender: 'action1'},
				},{
					title: "序号",
					align: "center",
					width: 40,
					customRender: (text, record, index) => {
						if (!record.isChild) {
							return index + 1
						}
					}
				},
				{
					title: "委托单号",
					dataIndex: "folderno",
					align: "center",
					width: 90
				},
				{
					title: "主题",
					dataIndex: "theme",
					align: "center",
					ellipsis: true,
					width: 90,
          scopedSlots: {customRender: 'theme'},
				},
				{
					title: "样品编号",
					width: 110,
					align: "center",
					dataIndex: "orderno",
          scopedSlots: {customRender: 'theme'},
				},
				/*{
					title: "测试项目编码",
					width: 90,
					align: "center",
					dataIndex: "testcode"
					//scopedSlots: {customRender: 'updateText'},
				},*/
				{
					title: "测试项目别名",
					width: 100,
					align: "center",
					dataIndex: "alias",
          ellipsis: true,
          scopedSlots: {customRender: 'theme'},
					//scopedSlots: {customRender: 'updateText'},
				},
				{
					title: "测试编码",
					width: 110,
					align: "center",
					dataIndex: "celltestcode",
          ellipsis:true,
					scopedSlots: { customRender: "celltestcode" }
				},
				{
					title: "数据位置",
					width: 80,
					align: "center",
					dataIndex: "dataPath",
					ellipsis: true,
					scopedSlots: {customRender: 'dataPath'},
				},{
					title: "存储天数",
					width: 75,
					align: "center",
					dataIndex: "day"
				},
        {
          title: "温度",
          width: 40,
          align: "center",
          dataIndex: "tem"
        },{
          title: "SOC",
          width: 40,
          align: "center",
          dataIndex: "soc"
        },{
          title: "测试状态",
          width: 80,
          align: "center",
          dataIndex: "endstatusflag",
          customRender: (text, record, index) => {
            if (null != text) {
              return text == 0?'测试中':'完成'
            }
            return text
          }
        },
				{
					title: "开始时间",
					width: 80,
					align: "center",
					dataIndex: "startTime",
					customRender: (text, record, index) => {
						if (null != text) {
							return moment(text).format("YYYY-MM-DD")
						}
						return text
					}
					//
					//scopedSlots: {customRender: 'updateText'},
				},
				{
					title: "结束时间",
					width: 80,
					align: "center",
					dataIndex: "endTime",
					customRender: (text, record, index) => {
						if (null != text && record.endstatusflag == 1) {
							return moment(text).format("YYYY-MM-DD")
						}
						return null
					}
				},
				{
					title: "设备编号",
					width: 80,
					align: "center",
					dataIndex: "equiptcode"
				},
				{
					title: "通道编号",
					width: 80,
					align: "center",
					dataIndex: "channelno"
				}
			],
			columns1: [
				{
					title: "序号",
					align: "center",
					width: 50,
					customRender: (text, record, index) => index + 1
				},
				{
					title: "委托单号",
					dataIndex: "folderno",
					align: "center",
					width: 90
				},
				{
					title: "样品编号",
					width: 90,
					align: "center",
					dataIndex: "orderno"
				},
				{
					title: "测试项目编码",
					width: 90,
					align: "center",
					dataIndex: "testcode"
					//scopedSlots: {customRender: 'updateText'},
				},
				{
					title: "测试项目别名",
					width: 90,
					align: "center",
					dataIndex: "alias"
					//scopedSlots: {customRender: 'updateText'},
				},
				{
					title: "测试编码",
					width: 100,
					align: "center",
					dataIndex: "celltestcode",
          ellipsis:true,
					scopedSlots: { customRender: "celltestcode" }
				},
				{
					title: "开始时间",
					width: 90,
					align: "center",
					dataIndex: "startTime",
					customRender: (text, record, index) => {
						if (null != text) {
							return moment(text).format("YYYY-MM-DD")
						}
						return text
					}
					//
					//scopedSlots: {customRender: 'updateText'},
				},
				{
					title: "结束时间",
					width: 90,
					align: "center",
					dataIndex: "endTime",
					customRender: (text, record, index) => {
						if (null != text) {
							return moment(text).format("YYYY-MM-DD")
						}
						return text
					}
				},
				{
					title: "设备编号",
					width: 60,
					align: "center",
					dataIndex: "equiptcode"
				},
				{
					title: "通道编号",
					width: 60,
					align: "center",
					dataIndex: "channelno"
				}
			],
			form: this.$form.createForm(this),
			selectedRowKeys: [],
			selectedRows: [],
      deleteRowSelection: {
        columnWidth: 20,
        onChange: (selectedRowKeys, selectedRows) => {
          this.deleteSelectedRowKeys = selectedRowKeys
          this.deleteSelectedRow = selectedRows
        }
      },
      deleteSelectedRowKeys:[],
      deleteSelectedRow:[],
      deleteLogicSelectedRowKeys:[],
      deleteLogicSelectedRow:[],
			projectSelectedRowKeys: [],
			projectSelectedRows: [],
			saveParam: null,
			// 存储3数据选择最后输入的select值
			lastDataSelect: {
				key1: "",
				key2: "",
				key3: ""
			}
		}
	},
	watch: {
		dataType(newValue, oldValue) {
			this.$nextTick(() => {
				document.documentElement.style.setProperty(
					`--height`,
					`${document.body.clientHeight - document.getElementById("export").offsetHeight - 229 - this.width + 30.5}px`
				)

        document.documentElement.style.setProperty(
					`--width`,
					this.width + 'px'
				)
			})
		}
	},
	created() {},
	computed: {
		...mapGetters(["testTaskFilterData"]),
    setMultStyle() {
      return {
        cursor: this.enabled ? 'move' : 'default'
      }
    }
	},
	mounted() {
		this.param = this.testTaskFilterData
		this.$store.commit("setTaskFilterData", null)
		if (this.param != null) {
			this.dataType = this.param.dataType
			this.stepChecked = this.param.stepChecked
			this.dataChecked = this.param.dataChecked
			this.cycChecked = this.param.cycChecked
			this.stepInfoChecked = this.param.stepInfoChecked
			this.filterData = this.param.filterData
			this.orderData = this.param.orderData
			/*this.stepOptions = this.param.stepOptions
			this.dataOptions = this.param.dataOptions
			this.cycOptions = this.param.cycOptions*/
		} else {
			/*document.getElementById("checkAll").click()*/
		}

		const temHeight = document.body.clientHeight - document.getElementById("export").offsetHeight - 229 - this.width + 30.5
		document.documentElement.style.setProperty(`--height`, `${temHeight}px`)
		document.documentElement.style.setProperty(
		`--width`,
		this.width + 'px'
		)

    this.$nextTick(() => {
      let tableContainer = this.$refs.tableContainer
      this.rowDrop(tableContainer)
    })
	},

	methods: {
    rowDrop(dom) {

      new Sortable.create(dom.querySelector('.ant-table>.ant-table-content .ant-table-tbody'), {
        handle: '.drag', // 行元素
        animation: 150,
        onEnd: ({newIndex, oldIndex}) => {
          // 拖拽后回调
          const currRow = this.orderData.splice(oldIndex, 1)[0]
          this.orderData.splice(newIndex, 0, currRow)
        }
      })
    },
    reset(){
      this.queryParam = {}
      this.$refs.table.refresh()
    },
		onChange() {
			let num =
				this.dataType == "step"
					? this.stepChecked.length
					: this.dataType == "data"
					? this.dataChecked.length
					: this.dataType == "cyc"?this.cycChecked.length:this.stepInfoChecked.length
			let allNum =
				this.dataType == "step"
					? this.stepOptions.length
					: this.dataType == "data"
					? this.dataOptions.length
					: this.dataType == "cyc"?this.cycOptions.length:this.stepInfoOptions.length

			this.indeterminate = num == 0 ? null : 0 < num < allNum
			this.checkAll = num > 0 && num == allNum
			this.$nextTick(() => {
				if (this.checkAll) {
					this.indeterminate = false
				}
			})
		},

    showData(record) {
      showData({celltestcode:record.celltestcode}).then(res => {
        this.$refs.table.refresh()
      })
		},
    hideData(record) {
      hideData({id:record.id}).then(res => {
        this.$refs.table.refresh()
      })
		},

		// 3数据选择select改变事件
		handleDataSelectChange(value, option, index, record) {
			let flag = true
			if (index.indexOf("1") > -1) {
				if (value == record.key2) {
					flag = false
					this.$message.warn("请选择不同参数")
					record.key2 = null
				} else if (value == record.key3) {
					flag = false
					this.$message.warn("请选择不同参数")
					record.key3 = null
				}
			}

			if (index.indexOf("2") > -1) {
				if (record.key1 == value) {
					flag = false
					this.$message.warn("请选择不同参数")
					record.key2 = null
				} else if (value == record.key3) {
					flag = false
					this.$message.warn("请选择不同参数")
					record.key3 = null
				}
			}
			if (index.indexOf("3") > -1) {
				if (record.key1 == value) {
					flag = false
					this.$message.warn("请选择不同参数")
					record.key3 = null
				} else if (record.key2 == value) {
					flag = false
					this.$message.warn("请选择不同参数")
					record.key3 = null
				}
			}

			if (flag) {
				this.lastDataSelect[index] = value
			}
		},

		onCheckAllChange(e) {
			Object.assign(this, {
				stepChecked: e.target.checked ? this.filterValue(this.stepOptions) : [],
				dataChecked: e.target.checked ? this.filterValue(this.dataOptions) : [],
				cycChecked: e.target.checked ? this.filterValue(this.cycOptions) : [],
				stepInfoChecked: e.target.checked ? this.filterValue(this.stepInfoOptions) : [],
				indeterminate: null,
				checkAll: e.target.checked
			})
		},
		filterValue(list) {
			let newList = []
			for (let i = 0; i < list.length; i++) {
				newList.push(list[i].value)
			}
			return newList
		},

		handleSubmit() {
			const {
				form: { validateFields }
			} = this

			this.confirmLoading = true
			validateFields((errors, values) => {
				if (!errors) {
					shenghongDataFilterExport(Object.assign(values, this.saveParam)).then(res => {
						if (res.success) {
							this.$message.success("导出任务创建成功")
							this.$router.push("/testDataHistory")
						} else {
							this.$message.warn(res.message)
						}
					})
				}
				this.confirmLoading = false
			})
		},

		exportData() {
			let param = {}
			param.dataType = this.dataType
			param.stepChecked = this.stepChecked
			param.dataChecked = this.dataChecked
			param.cycChecked = this.cycChecked
			param.stepInfoChecked = this.stepInfoChecked
			param.filterData = this.filterData
			param.orderData = this.orderData
			param.stepOptions = this.stepOptions
			param.stepInfoOptions = this.stepInfoOptions
			param.dataOptions = this.dataOptions
			param.cycOptions = this.cycOptions

			if (param.orderData.length == 0) {
				this.$message.warn("请选择测试项目")
				return
			}
			if (param.filterData.length == 0) {
				this.$message.warn("请填写数据选择项")
				return
			}
			if (param.dataType == "stpe" && param.stepChecked.length == 0) {
				this.$message.warn("请勾选导出项")
				return
			}
			if (param.dataType == "data" && param.dataChecked.length == 0) {
				this.$message.warn("请勾选导出项")
				return
			}
			if (param.dataType == "cyc" && param.cycChecked.length == 0) {
				this.$message.warn("请勾选导出项")
				return
			}
			if (param.dataType == "stepInfo" && param.stepInfoChecked.length == 0) {
				this.$message.warn("请勾选导出项")
				return
			}

			this.visible1 = true

			this.saveParam = param
		},
		// 3数据选择--添加按钮事件
		addFilterData() {
			this.filterData.push({
				key1: this.lastDataSelect["key1"] ? this.lastDataSelect["key1"] : this.dataType == 'stepInfo'?"StepID":"StepId",
				value1: null,
				key2: this.lastDataSelect["key2"] ? this.lastDataSelect["key2"] : null,
				value2: null,
				key3: this.lastDataSelect["key3"] ? this.lastDataSelect["key3"] : null,
				value3: null,
				key4: null,
				value4: null
			})
		},

		expandedRowsChange(expandedRows) {
		},
		openStepData(record, flag) {
			this.outQueryFlowRecord = record
			this.outFlowRecord = record

			//历史数据处理
			if (null == record.flowInfoList && !flag) {
				this.$refs.stepData.query(record, false)
        return;
			}

			if (record.flowId != null) {
				this.outQueryFlowRecord.flowId = record.flowId
				this.$refs.stepData.query(this.outQueryFlowRecord, false)
			} else {
				this.$message.warn("测试数据为空")
				return
			}
		},
		onSelectChange(record, selected) {
			this.outFlowRecord = record
			this.outQueryFlowRecord = record

			if (selected) {
				if (record.flowId == null) {
					this.$message.warn("测试数据为空")
					return
				}
				/*if(record.flowInfoList.length == 0){
            this.$message.warn("测试数据为空")
            return
          }else if(record.flowInfoList.length > 1){
            this.visibleFlow = true
            this.flowInfoData = record.flowInfoList
            this.inFlowActionName = '选中'
            return
          }else if(record.flowInfoList.length == 1){
            record.flowId = record.flowInfoList[0].flowId
          }*/

				if (!this.selectedRowKeys.includes(record.uuid)) {
					this.selectedRowKeys.push(record.uuid)
					this.selectedRows.push(record)
					// this.orderData.push(record)
				}
			} else {
				for (let i = 0; i < this.selectedRowKeys.length; i++) {
					if (this.selectedRowKeys[i] === record.uuid) {
						this.selectedRowKeys.splice(i, 1)
						this.selectedRows.splice(i, 1)
						// this.orderData.splice(i, 1)
						break
					}
				}
			}
		},

		onSelectChangeFlow(record, handle) {
			if (handle == "查看") {
				this.outQueryFlowRecord.flowId = record.flowId
				this.$refs.stepData.query(this.outQueryFlowRecord, false)
				return
			}

			this.outFlowRecord.flowId = record.flowId

			if (!this.selectedRowKeys.includes(this.outFlowRecord.uuid)) {
				this.selectedRowKeys.push(this.outFlowRecord.uuid)
				this.selectedRows.push(this.outFlowRecord)
				this.orderData.push(this.outFlowRecord)
			}

			this.visibleFlow = false
		},
		deleteData() {
			const orderIdArray = this.orderData.map(item => item.uuid)
			this.projectSelectedRow.forEach(item => {
				let index = orderIdArray.indexOf(item.uuid)
				orderIdArray.splice(index, 1)
				this.orderData.splice(index, 1)
				this.selectedRows.splice(index, 1)
				this.selectedRowKeys.splice(index, 1)
			})
			this.projectSelectedRow = []
			this.projectSelectedRowKeys = []
		},
		deleteDataOne(record, index) {
      console.log("删除")
      console.log(index)
			// this.selectedRows.splice(index, 1)
			this.selectedRowKeys.splice(index, 1)
			this.orderData.splice(index, 1)
		},
    moveDown(arr, index) {
      if (arr.length > 1 && index < arr.length - 1) { // 确保数组至少有两个元素，且索引有效
        arr[index] = arr.splice(index + 1, 1, arr[index])[0]; // 移除元素后立即插入到后一个位置
      }
    },

    moveUp(arr, index) {
      if (arr.length > 1 && index > 0) { // 确保数组至少有两个元素，且索引有效
        arr[index] = arr.splice(index - 1, 1, arr[index])[0]; // 移除元素后立即插入到前一个位置
      }
    },
		projectOnChange(selectedRowKeys, selectedRows) {
			this.projectSelectedRow = selectedRows
			this.projectSelectedRowKeys = selectedRowKeys
		},
		deleteLogicRowOnChange(selectedRowKeys, selectedRows) {
			this.deleteLogicSelectedRowKeys = selectedRowKeys
		},
		onSelectAllChange(selected, selectedRows, changeRows) {
			if (selected) {
				/*for (let i = 0; i < selectedRows.length; i++) {
					if (selectedRows[i].flowId == null) {
						this.$message.warn("序号" + (i + 1) + "测试数据为空")
						return
					}
				}*/
				selectedRows.forEach(item => {
					if (!this.selectedRowKeys.includes(item.uuid) && item.flowId != null) {
						this.selectedRowKeys.push(item.uuid)
						this.selectedRows.push(item)
						// this.orderData.push(item)
					}

					/*if(item.children && item.children.length > 0){
              item.children.forEach(inItem => {
                if (!this.selectedRowKeys.includes(inItem.id)) {
                  this.selectedRowKeys.push(inItem.id)
                  this.selectedRows.push(inItem)
                  this.orderData.push(inItem)
                }
              })
            }*/
				})
			} else {
				for (let i = 0; i < changeRows.length; i++) {
					if (this.selectedRowKeys.includes(changeRows[i].uuid)) {
						let index = this.selectedRowKeys.indexOf(changeRows[i].uuid)
						this.selectedRowKeys.splice(index, 1)
						this.selectedRows.splice(index, 1)
						// this.orderData.splice(index, 1)
					}
				}
			}
		},
		handleCancel() {
			this.visible = false
		},
		handleCancelFlow() {
			this.visibleFlow = false
		},
		handleCancel1() {
			this.visible1 = false
		},
		openTestOrder() {
      this.selectedRows = this.orderData
      this.selectedRowKeys = this.orderData.map(item => item.uuid);
			this.visible = true
			/*this.$nextTick(() => {
          this.$refs.table.refresh()

        })*/
		},

		changeDataType($event) {
      if($event.target.value != 'stepInfo'){
        for (let i = 0; i < this.filterData.length; i++) {
          if (this.filterData[i].key1 == "StepID" || this.filterData[i].key2 == "StepID" || this.filterData[i].key3 == "StepID") {
            this.filterData.splice(i, 1)
            break
          }
        }
      }

			/*if (!document.getElementById("checkAll").checked) {
				document.getElementById("checkAll").click()
			}*/
		},

		copyFromExcel(event, column, index) {
			let arr = event.clipboardData.getData("text").split("\n")

			if (arr.length > 1) {
				for (let i = 1; i < arr.length; i++) {
					if (null != arr[i] && "" != arr[i] && arr[i].length != 0) {
						if (this.filterData.length > index + i) {
							this.filterData[index + i][column] = arr[i]
						} else {
							this.filterData.push({
								key1: this.filterData[index].key1,
								value1: null,
								key2: this.filterData[index].key2,
								value2: null,
								key3: this.filterData[index].key3,
								value3: null,
								key4: this.filterData[index].key4,
								value4: null
							})
							this.filterData[index + i][column] = arr[i]
						}
					}
				}
			}

			setTimeout(() => {
				this.filterData[index][column] = arr[0]
			}, 10)
		},
		handleOk() {
			this.getList()
		},

		openData(folderId) {
			this.$refs.testData.query(folderId)
		},
		getList(flag) {
			this.$refs.table2.refresh()
		},
		handleStepChange(current) {
			this.stepStatus.forEach((v, index) => {
				if (v === "finish") return
				this.stepStatus[index] = current === index ? "process" : "wait"
			})
		},
		// 下一页
		nextPage(current) {
			this.current++
			this.stepStatus[current] = "finish"
			if (this.stepStatus[current + 1] !== "finish") this.stepStatus[current + 1] = "process"
		},
		handleCheckFile(index) {
			this.isShowExample = true
      this.iframeUrl = index
        ? "/api/sysFileInfo/previewPdf?id=1696770190973132802&Authorization=Bearer "+Vue.ls.get('Access-Token')
        : "/api/sysFileInfo/previewPdf?id=1696770532343341058&Authorization=Bearer "+Vue.ls.get('Access-Token')
		},
		handleCloseExample() {
			this.isShowExample = false
		},
    deleteSelect(){
      this.orderData = this.orderData.filter(item => !this.deleteSelectedRowKeys.includes(item.uuid));
      this.selectedRows = this.selectedRows.filter(item => !this.deleteSelectedRowKeys.includes(item.uuid));
      this.selectedRowKeys = this.selectedRowKeys.filter(item => !this.deleteSelectedRowKeys.includes(item));
      this.deleteSelectedRowKeys = []
      this.deleteSelectedRow = []
    },
    deleteLogicSelect(){
      this.filterData = this.filterData.filter(item => !this.deleteLogicSelectedRowKeys.includes(item.id));
      this.deleteLogicSelectedRowKeys = []
      this.deleteLogicSelectedRow = []
    }
	}
}
</script>
<style lang="less" scoped>
@import '/src/components/pageTool/style/pbiSearchItem.less';

:root {
	--height: 600px;
}
/deep/ .ant-table-thead > tr > th {
	padding: 5px !important;
	font-size: 13px !important;
}

/deep/ .ant-table-tbody > tr > td {
	padding: 0px !important;
	height: 32px !important;
	font-size: 12px !important;
}

/deep/ .ant-calendar-picker-icon {
	display: none;
}

/deep/ .ant-calendar-picker-input.ant-input {
	color: black;
	font-size: 12px;
	border: 0;
	text-align: center;
	padding: 0;
}

.red {
	background-color: #ed0000;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}

.yellow {
	background-color: #ffc000;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}

.grey {
	background-color: rgba(223, 223, 223, 0.25);
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}

.ant-modal-body {
	padding: 0;
}



/deep/ .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
	color: #fff;
	background: #1890ff;
}

.green {
	background-color: #58a55c;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}

/deep/ #table1 > div > div > div > div > div > div > table > thead {
	height: 64px;
}

/deep/ #table1 > .ant-table-wrapper > div > div > ul {
	display: none;
}

/deep/ .ant-table-pagination.ant-pagination {
	float: right;
	margin: 8px 0 0 0;;
}

.float {
	// width: 36%;
	// float: left;
	// margin-right: 10px;
	// text-align: center;
	padding-bottom: 10px;
}

.float1 {
	width: 12%;
	float: left;
	margin-right: 10px;
	text-align: center;
}

/deep/ .ant-checkbox-group-item {
	display: block;
	width: 100%;
	text-align: left;
}

.title {
	font-size: large;
	margin-bottom: 20px;
}

.numTitle {
	font-size: xx-large;
}

/deep/.ant-table-footer {
	padding: 0;
}

/deep/ .ant-table-row-expand-icon {
	margin-right: 0px;
}

.wrapper {
	background: #fff;
	height: calc(100vh - 40px - var(--width));
	padding: 0 10px 10px;
	background-color: #f0f2f5;
}

.head_title {
	color: #333;
	padding: 10px 0;
	font-size: 20px;
	font-weight: 600;
}
.head_title::before {
	width: 8px;
	background: #1890ff;
	margin-right: 8px;
	content: "\00a0"; //填充空格
}

.export-btn {
	position: fixed;
	bottom: 10px;
	right: 10px;
	width: 15%;
}

.all-wrapper {
	padding: 0 0 10px;
	display: flex;
	justify-content: space-between;
}
.btn-wrap {
	text-align: right;
}

.example-icon {
	width: 20px;
	height: 20px;
	color: #1890ff;
	vertical-align: middle;
	margin-left: 3px;
	margin-top: 3px;
}

// 通用
.mt10 {
	margin-top: 10px;
}
.mr5 {
	margin-right: 5px;
}
.mb5 {
	margin-bottom: 5px;
}
.mr10 {
	margin-right: 10px;
}

.title-line {
	width: 8px;
	height: 30px;
	background: #1890ff;
	border-radius: 2px;
	margin-right: 8px;
	content: "\00a0"; //填充空格

	position: absolute;
	top: 8px;
	left: -4px;
}

.flex-column {
	display: flex;
	flex-direction: column;
}

.flex-sb-center-row {
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.strong {
	// background-image: url("../../../../assets/icons/Rectangle.png");
	background-size: 100% 100%;
	width: 210px;
	height: 25px;
	color: #333;
	display: flex;
	align-items: center;
	padding: 5px;
}

.block {
	height: fit-content;
	padding: 10px;
	background: #fff;
	border-radius: 10px;
	box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
	position: relative;
}

.left-content {
	width: 50%;
	margin-right: 10px;
}
.right-content {
	width: 50%;
}
.right-content .all-checkbox {
	padding-bottom: 5px;
	margin-bottom: 5px;
	border-bottom: 1px solid #e9e9e9;
}

.normal-btn {
	padding: 5px 10px;
	color: #fff;
	background-color: #1890ff;
	letter-spacing: 2px;
	cursor: pointer;
}
.footer-btn {
	width: 100%;
	height: 32px;
	border: 1px solid #e8e8e8;
	background: #fff;
	color: #999;
	font-size: 16px;
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
}

.footer-btn:hover {
	color: #1890ff;
}

// 组件
/deep/.ant-steps {
	padding: 15px 50px;
}
/deep/.left-content .ant-table-body {
  	height: calc(100vh - 220px + 26.5px - var(--width))!important;
	border: 1px solid #e8e8e8;
	overflow: auto;
}

/deep/.right-content .ant-table-body {
	min-height: var(--height) !important;
	max-height: 300px !important;
	border: 1px solid #e8e8e8;
	overflow: auto;
}
/deep/.all-wrapper .ant-table-thead {
	position: sticky;
	top: 0;
	z-index: 2;
}
/deep/.all-wrapper .ant-table-placeholder {
	border: none !important;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	padding: 0;
}

/deep/.right-content .ant-empty-normal {
	margin: -2px 0;
}

/deep/.ant-empty-image {
	display: none;
}

/deep/.right-content .ant-select-selection {
	border: none;
}
/deep/.right-content .ant-input {
	border: none;
}

/deep/.ant-checkbox-group {
	width: 100%;
	display: flex;
	flex-wrap: wrap;
}
/deep/.ant-checkbox-group-item {
	font-size: 12px;
	width: 23%;
}
/deep/.ant-radio-inner {
	top: 1px;
	left: 1px;
}
/* /deep/ .ant-table-body::-webkit-scrollbar {
	height: 10px;
	width: 5px;
}
/deep/ .ant-table-body::-webkit-scrollbar-thumb {
	-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	border-radius: 0;

	background: #dddbdb;
}
/deep/ .ant-table-body::-webkit-scrollbar-track {
	-webkit-box-shadow: 0;
	border-radius: 0;
	background: #f1f1f1;
} */

/deep/.ant-select-selection__rendered {
	margin-right: 0px;
}

/deep/.ant-popover-buttons {
	display: flex !important;
	flex-direction: column !important;
	margin-bottom: 15px;
}

/* // /deep/.ant-popover-buttons .ant-btn-sm{
// 	margin-bottom: 5px;
// 	background-color: #1890ff;
// 	color: #fff;
// } */

/deep/ #outTable .ant-table-thead > tr > th {
  padding: 2px 0 !important;
  font-size: 12px !important;
}


.tips {
	color: #1890ff;
}

.button-tips{
	display: flex;
	flex-direction: column;
}

/deep/.s-table-tool{
  padding-bottom: 0;
}
/deep/.ant-modal-footer {
  padding: 5px 16px;
}

/deep/ .search-container .ant-input {
  padding: 4px;
}
/* @media screen and (min-width:900px) and (max-width:1970px) {
	/deep/.left-content .ant-table-header colgroup col:last-child {
		width: 58px !important; 
		min-width: 58px !important;
	}
}

@media screen and (min-width:1980px) and (max-width:3000px) {
	/deep/.left-content .ant-table-header colgroup col:last-child {
		width: 55px !important;
		min-width: 55px !important;
	}
}


/deep/.right-content .ant-table-header colgroup col:last-child {
  width: 104px !important; 
  min-width: 104px !important;
} */



</style>
