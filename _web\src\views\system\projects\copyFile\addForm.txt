<template>
	<a-modal
		title="新增"
		:width="1000"
		:height="700"
		:bodyStyle="{ padding: 0 }"
		:maskClosable="false"
		:visible="visible"
		:confirmLoading="confirmLoading"
		style="padding: 0"
		@cancel="handleCancel"
	>
		<a-spin :spinning="confirmLoading">
			<a-tabs v-model="activeKey" @change="changeTab">
				<a-tab-pane key="basic" :tab="tab1">
					<a-form :form="form1">
						<a-row :gutter="24">
							<a-col :md="24" :sm="24">
								<a-form-item label="产品分类" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-select
										v-decorator="[
											'productClassification',
											{ rules: [{ required: true, message: '请选择产品分类！' }] }
										]"
										style="width: 100%"
									>
										<a-select-option v-for="item in classOptions" :value="item.key">
											{{ item.label }}
										</a-select-option>
									</a-select>
								</a-form-item>
							</a-col>
						</a-row>
						<a-row :gutter="24">
							<a-col :md="24" :sm="24">
								<a-form-item label="产品类别" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-tree-select
										allow-clear
										:dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
										:multiple="true"
										style="width: 100%"
										:tree-data="cateList"
										placeholder="请选择产品类别"
										tree-default-expand-all
										v-decorator="['productMultiCate', { rules: [{ required: true, message: '请选择产品类别！' }] }]"
									>
									</a-tree-select>
								</a-form-item>
							</a-col>
						</a-row>

						<a-row :gutter="24">
							<a-col :md="24" :sm="24">
								<a-form-item label="产品型号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-input
										v-decorator="['productType', { rules: [{ required: true, message: '请输入产品型号！' }] }]"
									/>
								</a-form-item>
							</a-col>
						</a-row>

						<a-row :gutter="24">
							<a-col :md="24" :sm="24">
								<a-form-item label="产品名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-input
										v-model="productName"
										v-decorator="['productName', { rules: [{ required: true, message: '请输入产品名称！' }] }]"
									/>
								</a-form-item>
							</a-col>
						</a-row>

						<a-row :gutter="24">
							<a-col :md="24" :sm="24">
								<a-form-item label="项目名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-input
										v-decorator="['projectName', { rules: [{ required: true, message: '请输入项目名称！' }] }]"
									/>
								</a-form-item>
							</a-col>
						</a-row>

						<a-row :gutter="24">
							<a-col :md="24" :sm="24">
								<a-form-item label="所属部门" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-tree-select
										allow-clear
										:dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
										style="width: 100%"
										:tree-data="deptList"
										placeholder="请选择所属部门"
										tree-default-expand-all
										v-decorator="['department', { rules: [{ required: true, message: '请选择所属部门！' }] }]"
									>
									</a-tree-select>
								</a-form-item>
							</a-col>
						</a-row>

            <a-row :gutter="24">
              <a-col :md="24" :sm="24">
                <a-form-item label="项目等级" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>

                  <a-select v-decorator="['projectLevel', {rules: [{required: true, message: '请选择项目等级！'}]}]"
                            style="width: 100%">

<!--                    <a-select-option :value="parseInt(1)">-->
<!--                      S+-->
<!--                    </a-select-option>-->

										<a-select-option :value="parseInt(2)">
											S
										</a-select-option>

										<a-select-option :value="parseInt(3)">
											A
										</a-select-option>

										<a-select-option :value="parseInt(4)">
											B
										</a-select-option>

										<a-select-option :value="parseInt(5)">
											C
										</a-select-option>
									</a-select>
								</a-form-item>
							</a-col>
						</a-row>
						<a-row :gutter="24">
							<a-col :md="24" :sm="24">
								<a-form-item label="客户" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-input v-decorator="['customer', { rules: [{ required: true, message: '请输入客户！' }] }]" />
								</a-form-item>
							</a-col>
						</a-row>
						<a-row :gutter="24">
							<a-col :md="24" :sm="24">
								<a-form-item label="定点状态" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-select
										v-decorator="['fixedState', { rules: [{ required: true, message: '请选择定点状态！' }] }]"
										style="width: 100%"
									>
										<a-select-option :value="parseInt(2)">
											是
										</a-select-option>

										<a-select-option :value="parseInt(1)">
											否
										</a-select-option>
									</a-select>
								</a-form-item>
							</a-col>
						</a-row>

						<a-row :gutter="24">
							<a-col :md="24" :sm="24">
								<a-form-item label="计划定点日期" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-date-picker
										:allowClear="false"
										placeholder=""
										style="width: 100%"
										v-decorator="['plannedFixedDate']"
									>
									</a-date-picker>
								</a-form-item>
							</a-col>
						</a-row>
					</a-form>
				</a-tab-pane>
				<a-tab-pane key="framework" :tab="tab2">
					<a-form :form="form2">
						<a-row :gutter="24">
							<a-col :md="12" :sm="12">
								<a-form-item label="院长" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-input
										type="hidden"
										v-decorator="['president', { rules: [{ required: true, message: '请选择院长！' }] }]"
									/>
									<a-dropdown v-model="yzDownVisible" placement="bottomCenter" :trigger="['click']">
										<a-button
											style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
											>{{ yz ? yz : "选择院长" }} <a-icon type="down"
										/></a-button>
										<a-menu slot="overlay">
											<a-spin :spinning="yzLoading" style="padding:10px 24px 0 24px;width:100%">
												<a-input-search v-model="yzQueryParam.searchValue" placeholder="搜索..." @change="onYzSearch" />
												<s-table
													style="width:100%;"
													ref="yzTable"
													:rowKey="record => record.id"
													:columns="vColumns"
													:data="loadYzData"
													:customRow="customYzRow"
													:scroll="{ y: 120, x: 120 }"
												>
												</s-table>
											</a-spin>
										</a-menu>
									</a-dropdown>
								</a-form-item>
							</a-col>
						</a-row>
						<a-row :gutter="24">
							<a-col :md="24" :sm="24">
								<a-form-item label="所长" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-input
										type="hidden"
										v-decorator="['headOfTheInstitute', { rules: [{ required: true, message: '请选择所长！' }] }]"
									/>
									<a-dropdown v-model="szDownVisible" placement="bottomCenter" :trigger="['click']">
										<a-button
											style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
											>{{ sz ? sz : "选择所长" }} <a-icon type="down"
										/></a-button>
										<a-menu slot="overlay">
											<a-spin :spinning="szLoading" style="padding:10px 24px 0 24px;width:100%">
												<a-input-search v-model="szQueryParam.searchValue" placeholder="搜索..." @change="onSzSearch" />
												<s-table
													style="width:100%;"
													ref="szTable"
													:rowKey="record => record.id"
													:columns="vColumns"
													:data="loadSzData"
													:customRow="customSzRow"
													:scroll="{ y: 120, x: 120 }"
												>
												</s-table>
											</a-spin>
										</a-menu>
									</a-dropdown>
								</a-form-item>
							</a-col>
						</a-row>
						<a-row :gutter="24">
							<a-col :md="24" :sm="24">
								<a-form-item label="信息专员" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-input
										type="hidden"
										v-decorator="['infoCommissioner', { rules: [{ required: true, message: '请选择信息专员！' }] }]"
									/>
									<a-dropdown v-model="xxzyDownVisible" placement="bottomCenter" :trigger="['click']">
										<a-button
											style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
											>{{ xxzy ? xxzy : "选择信息专员" }} <a-icon type="down"
										/></a-button>
										<a-menu slot="overlay">
											<a-spin :spinning="xxzyLoading" style="padding:10px 24px 0 24px;width:100%">
												<a-input-search
													v-model="xxzyQueryParam.searchValue"
													placeholder="搜索..."
													@change="onXxzySearch"
												/>
												<s-table
													style="width:100%;"
													ref="xxzyTable"
													:rowKey="record => record.id"
													:columns="vColumns"
													:data="loadXxzyData"
													:customRow="customXxzyRow"
													:scroll="{ y: 120, x: 120 }"
												>
												</s-table>
											</a-spin>
										</a-menu>
									</a-dropdown>
								</a-form-item>
							</a-col>
						</a-row>
						<a-row :gutter="24">
							<a-col :md="12" :sm="12">
								<a-form-item label="产品中心经理" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-input
										type="hidden"
										v-decorator="[
											'productCenterManager',
											{ rules: [{ required: true, message: '请选择产品中心经理！' }] }
										]"
									/>
									<a-dropdown v-model="cpzxjlDownVisible" placement="bottomCenter" :trigger="['click']">
										<a-button
											style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
											>{{ cpzxjl ? cpzxjl : "选择产品中心经理" }} <a-icon type="down"
										/></a-button>
										<a-menu slot="overlay">
											<a-spin :spinning="cpzxjlLoading" style="padding:10px 24px 0 24px;width:100%">
												<a-input-search
													v-model="cpzxjlQueryParam.searchValue"
													placeholder="搜索..."
													@change="oncpzxjlSearch"
												/>
												<s-table
													style="width:100%;"
													ref="cpzxjlTable"
													:rowKey="record => record.id"
													:columns="vColumns"
													:data="loadcpzxjlData"
													:customRow="customcpzxjlRow"
													:scroll="{ y: 120, x: 120 }"
												>
												</s-table>
											</a-spin>
										</a-menu>
									</a-dropdown>
								</a-form-item>
							</a-col>
							<a-col :md="12" :sm="12">
								<a-form-item label="产品总监" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-input
										type="hidden"
										v-decorator="['productTechMajordomo', { rules: [{ required: true, message: '请选择产品总监！' }] }]"
									/>
									<a-dropdown v-model="cpzjDownVisible" placement="bottomCenter" :trigger="['click']">
										<a-button
											style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
											>{{ cpzj ? cpzj : "选择产品总监" }} <a-icon type="down"
										/></a-button>
										<a-menu slot="overlay">
											<a-spin :spinning="cpzjLoading" style="padding:10px 24px 0 24px;width:100%">
												<a-input-search
													v-model="cpzjQueryParam.searchValue"
													placeholder="搜索..."
													@change="oncpzjSearch"
												/>
												<s-table
													style="width:100%;"
													ref="cpzjTable"
													:rowKey="record => record.id"
													:columns="vColumns"
													:data="loadcpzjData"
													:customRow="customcpzjRow"
													:scroll="{ y: 120, x: 120 }"
												>
												</s-table>
											</a-spin>
										</a-menu>
									</a-dropdown>
								</a-form-item>
							</a-col>
						</a-row>
						<a-row :gutter="24">
							<a-col :md="12" :sm="12">
								<a-form-item label="产品经理" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-input
										type="hidden"
										v-decorator="['productManager', { rules: [{ required: true, message: '请选择产品经理！' }] }]"
									/>
									<a-dropdown v-model="cpjlDownVisible" placement="bottomCenter" :trigger="['click']">
										<a-button
											style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
											>{{ cpjl ? cpjl : "选择产品经理" }} <a-icon type="down"
										/></a-button>
										<a-menu slot="overlay">
											<a-spin :spinning="cpjlLoading" style="padding:10px 24px 0 24px;width:100%">
												<a-input-search
													v-model="cpjlQueryParam.searchValue"
													placeholder="搜索..."
													@change="oncpjlSearch"
												/>
												<s-table
													style="width:100%;"
													ref="cpjlTable"
													:rowKey="record => record.id"
													:columns="vColumns"
													:data="loadcpjlData"
													:customRow="customcpjlRow"
													:scroll="{ y: 120, x: 120 }"
												>
												</s-table>
											</a-spin>
										</a-menu>
									</a-dropdown>
								</a-form-item>
							</a-col>
							<a-col :md="12" :sm="12">
								<a-form-item label="产品工程师" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-input
										type="hidden"
										v-decorator="['productEnginMulti', { rules: [{ required: true, message: '请选择产品工程师！' }] }]"
									/>
									<a-dropdown v-model="cpgcsDownVisible" placement="bottomCenter" :trigger="['click']">
										<a-button
											style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
											>{{ cpgcs ? cpgcs : "选择产品工程师" }} <a-icon type="down"
										/></a-button>
										<a-menu slot="overlay">
											<a-spin :spinning="cpgcsLoading" style="padding:10px 24px 0 24px;width:100%">
												<a-input-search
													v-model="cpgcsQueryParam.searchValue"
													placeholder="搜索..."
													@change="oncpgcsSearch"
												/>
												<s-table
													style="width:100%;"
													ref="cpgcstable"
													:rowKey="record => record.id"
													:rowSelection="cpgcsrowSelection"
													:columns="vColumns"
													:data="loadcpgcsData"
													:customRow="customcpgcsRow"
													:scroll="{ y: 120, x: 120 }"
												>
												</s-table>
											</a-spin>
										</a-menu>
									</a-dropdown>
								</a-form-item>
							</a-col>
						</a-row>
						<a-row :gutter="24">
							<a-col :md="12" :sm="12">
								<a-form-item label="项目总监" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-input
										type="hidden"
										v-decorator="['projectMajordomo', { rules: [{ required: true, message: '请选择项目总监！' }] }]"
									/>
									<a-dropdown v-model="xmzjDownVisible" placement="bottomCenter" :trigger="['click']">
										<a-button
											style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
											>{{ xmzj ? xmzj : "选择项目总监" }} <a-icon type="down"
										/></a-button>
										<a-menu slot="overlay">
											<a-spin :spinning="xmzjLoading" style="padding:10px 24px 0 24px;width:100%">
												<a-input-search
													v-model="xmzjQueryParam.searchValue"
													placeholder="搜索..."
													@change="onxmzjSearch"
												/>
												<s-table
													style="width:100%;"
													ref="xmzjTable"
													:rowKey="record => record.id"
													:columns="vColumns"
													:data="loadxmzjData"
													:customRow="customxmzjRow"
													:scroll="{ y: 120, x: 120 }"
												>
												</s-table>
											</a-spin>
										</a-menu>
									</a-dropdown>
								</a-form-item>
							</a-col>
							<a-col :md="12" :sm="12">
								<a-form-item label="研发项目经理" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-input
										type="hidden"
										v-decorator="['productRPM', { rules: [{ required: true, message: '请选择研发项目经理！' }] }]"
									/>
									<a-dropdown v-model="yfxmjlDownVisible" placement="bottomCenter" :trigger="['click']">
										<a-button
											style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
											>{{ yfxmjl ? yfxmjl : "选择研发项目经理" }} <a-icon type="down"
										/></a-button>
										<a-menu slot="overlay">
											<a-spin :spinning="yfxmjlLoading" style="padding:10px 24px 0 24px;width:100%">
												<a-input-search
													v-model="yfxmjlQueryParam.searchValue"
													placeholder="搜索..."
													@change="onyfxmjlSearch"
												/>
												<s-table
													style="width:100%;"
													ref="yfxmjlTable"
													:rowKey="record => record.id"
													:columns="vColumns"
													:data="loadyfxmjlData"
													:customRow="customyfxmjlRow"
													:scroll="{ y: 120, x: 120 }"
												>
												</s-table>
											</a-spin>
										</a-menu>
									</a-dropdown>
								</a-form-item>
							</a-col>
						</a-row>
						<a-row :gutter="24">
							<a-col :md="12" :sm="12">
								<a-form-item label="工程中心经理" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-input
										type="hidden"
										v-decorator="[
											'engineCenterManager',
											{ rules: [{ required: true, message: '请选择工程中心经理！' }] }
										]"
									/>
									<a-dropdown v-model="gczxjlDownVisible" placement="bottomCenter" :trigger="['click']">
										<a-button
											style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
											>{{ gczxjl ? gczxjl : "选择工程中心经理" }} <a-icon type="down"
										/></a-button>
										<a-menu slot="overlay">
											<a-spin :spinning="gczxjlLoading" style="padding:10px 24px 0 24px;width:100%">
												<a-input-search
													v-model="gczxjlQueryParam.searchValue"
													placeholder="搜索..."
													@change="ongczxjlSearch"
												/>
												<s-table
													style="width:100%;"
													ref="gczxjlTable"
													:rowKey="record => record.id"
													:columns="vColumns"
													:data="loadgczxjlData"
													:customRow="customgczxjlRow"
													:scroll="{ y: 120, x: 120 }"
												>
												</s-table>
											</a-spin>
										</a-menu>
									</a-dropdown>
								</a-form-item>
							</a-col>
							<a-col :md="12" :sm="12">
								<a-form-item label="工程代表" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-input
										type="hidden"
										v-decorator="[
											'engineCenterRepresentMulti',
											{ rules: [{ required: true, message: '请选择工程代表！' }] }
										]"
									/>
									<a-dropdown v-model="gcdbDownVisible" placement="bottomCenter" :trigger="['click']">
										<a-button
											style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
											>{{ gcdb ? gcdb : "选择工程代表" }} <a-icon type="down"
										/></a-button>
										<a-menu slot="overlay">
											<a-spin :spinning="gcdbLoading" style="padding:10px 24px 0 24px;width:100%">
												<a-input-search
													v-model="gcdbQueryParam.searchValue"
													placeholder="搜索..."
													@change="ongcdbSearch"
												/>
												<s-table
													style="width:100%;"
													ref="gcdbTable"
													:rowKey="record => record.id"
													:columns="vColumns"
													:data="loadgcdbData"
													:rowSelection="gcdbrowSelection"
													:customRow="customgcdbRow"
													:scroll="{ y: 120, x: 120 }"
												>
												</s-table>
											</a-spin>
										</a-menu>
									</a-dropdown>
								</a-form-item>
							</a-col>
						</a-row>
						<a-row :gutter="24">
							<a-col :md="12" :sm="12">
								<a-form-item label="结构中心经理" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-input
										type="hidden"
										v-decorator="[
											'structureCenterManager',
											{ rules: [{ required: true, message: '请选择结构中心经理！' }] }
										]"
									/>
									<a-dropdown v-model="jgzxjlDownVisible" placement="bottomCenter" :trigger="['click']">
										<a-button
											style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
											>{{ jgzxjl ? jgzxjl : "选择结构中心经理" }} <a-icon type="down"
										/></a-button>
										<a-menu slot="overlay">
											<a-spin :spinning="jgzxjlLoading" style="padding:10px 24px 0 24px;width:100%">
												<a-input-search
													v-model="jgzxjlQueryParam.searchValue"
													placeholder="搜索..."
													@change="onjgzxjlSearch"
												/>
												<s-table
													style="width:100%;"
													ref="jgzxjlTable"
													:rowKey="record => record.id"
													:columns="vColumns"
													:data="loadjgzxjlData"
													:customRow="customjgzxjlRow"
													:scroll="{ y: 120, x: 120 }"
												>
												</s-table>
											</a-spin>
										</a-menu>
									</a-dropdown>
								</a-form-item>
							</a-col>
							<a-col :md="12" :sm="12">
								<a-form-item label="结构代表" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-input
										type="hidden"
										v-decorator="[
											'structureTechnologyRepresent',
											{ rules: [{ required: true, message: '请选择结构代表！' }] }
										]"
									/>
									<a-dropdown v-model="jgdbDownVisible" placement="bottomCenter" :trigger="['click']">
										<a-button
											style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
											>{{ jgdb ? jgdb : "选择结构代表" }} <a-icon type="down"
										/></a-button>
										<a-menu slot="overlay">
											<a-spin :spinning="jgdbLoading" style="padding:10px 24px 0 24px;width:100%">
												<a-input-search
													v-model="jgdbQueryParam.searchValue"
													placeholder="搜索..."
													@change="onjgdbSearch"
												/>
												<s-table
													style="width:100%;"
													ref="jgdbTable"
													:rowKey="record => record.id"
													:columns="vColumns"
													:data="loadjgdbData"
													:customRow="customjgdbRow"
													:scroll="{ y: 120, x: 120 }"
												>
												</s-table>
											</a-spin>
										</a-menu>
									</a-dropdown>
								</a-form-item>
							</a-col>
						</a-row>
						<a-row :gutter="24">
							<a-col :md="12" :sm="12">
								<a-form-item label="共性中心经理" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-input
										type="hidden"
										v-decorator="[
											'commonCenterManager',
											{ rules: [{ required: true, message: '请选择共性中心经理！' }] }
										]"
									/>
									<a-dropdown v-model="gxzxjlDownVisible" placement="bottomCenter" :trigger="['click']">
										<a-button
											style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
											>{{ gxzxjl ? gxzxjl : "选择共性中心经理" }} <a-icon type="down"
										/></a-button>
										<a-menu slot="overlay">
											<a-spin :spinning="gxzxjlLoading" style="padding:10px 24px 0 24px;width:100%">
												<a-input-search
													v-model="gxzxjlQueryParam.searchValue"
													placeholder="搜索..."
													@change="ongxzxjlSearch"
												/>
												<s-table
													style="width:100%;"
													ref="gxzxjlTable"
													:rowKey="record => record.id"
													:columns="vColumns"
													:data="loadgxzxjlData"
													:customRow="customgxzxjlRow"
													:scroll="{ y: 120, x: 120 }"
												>
												</s-table>
											</a-spin>
										</a-menu>
									</a-dropdown>
								</a-form-item>
							</a-col>
							<a-col :md="12" :sm="12">
								<a-form-item label="共性代表" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-input
										type="hidden"
										v-decorator="[
											'commonCenterRepresent',
											{ rules: [{ required: true, message: '请选择共性代表！' }] }
										]"
									/>
									<a-dropdown v-model="gxdbDownVisible" placement="bottomCenter" :trigger="['click']">
										<a-button
											style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
											>{{ gxdb ? gxdb : "选择共性代表" }} <a-icon type="down"
										/></a-button>
										<a-menu slot="overlay">
											<a-spin :spinning="gxdbLoading" style="padding:10px 24px 0 24px;width:100%">
												<a-input-search
													v-model="gxdbQueryParam.searchValue"
													placeholder="搜索..."
													@change="ongxdbSearch"
												/>
												<s-table
													style="width:100%;"
													ref="gxdbTable"
													:rowKey="record => record.id"
													:columns="vColumns"
													:data="loadgxdbData"
													:customRow="customgxdbRow"
													:scroll="{ y: 120, x: 120 }"
												>
												</s-table>
											</a-spin>
										</a-menu>
									</a-dropdown>
								</a-form-item>
							</a-col>
						</a-row>
						<a-row :gutter="24">
							<a-col :md="12" :sm="12">
								<a-form-item label="办公室经理" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-input
										type="hidden"
										v-decorator="['officeManager', { rules: [{ required: true, message: '请选择办公室经理！' }] }]"
									/>
									<a-dropdown v-model="bgsjlDownVisible" placement="bottomCenter" :trigger="['click']">
										<a-button
											style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
											>{{ bgsjl ? bgsjl : "选择办公室经理" }} <a-icon type="down"
										/></a-button>
										<a-menu slot="overlay">
											<a-spin :spinning="bgsjlLoading" style="padding:10px 24px 0 24px;width:100%">
												<a-input-search
													v-model="bgsjlQueryParam.searchValue"
													placeholder="搜索..."
													@change="onbgsjlSearch"
												/>
												<s-table
													style="width:100%;"
													ref="bgsjlTable"
													:rowKey="record => record.id"
													:columns="vColumns"
													:data="loadbgsjlData"
													:customRow="custombgsjlRow"
													:scroll="{ y: 120, x: 120 }"
												>
												</s-table>
											</a-spin>
										</a-menu>
									</a-dropdown>
								</a-form-item>
							</a-col>
							<a-col :md="12" :sm="12">
								<a-form-item label="DQE" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-input
										type="hidden"
										v-decorator="['productDQE', { rules: [{ required: true, message: '请选择DQE！' }] }]"
									/>
									<a-dropdown v-model="dqeDownVisible" placement="bottomCenter" :trigger="['click']">
										<a-button
											style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
											>{{ dqe ? dqe : "选择DQE" }} <a-icon type="down"
										/></a-button>
										<a-menu slot="overlay">
											<a-spin :spinning="dqeLoading" style="padding:10px 24px 0 24px;width:100%">
												<a-input-search
													v-model="dqeQueryParam.searchValue"
													placeholder="搜索..."
													@change="ondqeSearch"
												/>
												<s-table
													style="width:100%;"
													ref="dqeTable"
													:rowKey="record => record.id"
													:columns="vColumns"
													:data="loaddqeData"
													:customRow="customdqeRow"
													:scroll="{ y: 120, x: 120 }"
												>
												</s-table>
											</a-spin>
										</a-menu>
									</a-dropdown>
								</a-form-item>
							</a-col>
						</a-row>
						<a-row :gutter="24">
							<a-col :md="12" :sm="12">
								<a-form-item label="测试经理" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-input
										type="hidden"
										v-decorator="['testManager', { rules: [{ required: true, message: '请选择测试经理！' }] }]"
									/>
									<a-dropdown v-model="csjlDownVisible" placement="bottomCenter" :trigger="['click']">
										<a-button
											style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
											>{{ csjl ? csjl : "选择测试经理" }} <a-icon type="down"
										/></a-button>
										<a-menu slot="overlay">
											<a-spin :spinning="csjlLoading" style="padding:10px 24px 0 24px;width:100%">
												<a-input-search
													v-model="csjlQueryParam.searchValue"
													placeholder="搜索..."
													@change="oncsjlSearch"
												/>
												<s-table
													style="width:100%;"
													ref="csjlTable"
													:rowKey="record => record.id"
													:columns="vColumns"
													:data="loadcsjlData"
													:customRow="customcsjlRow"
													:scroll="{ y: 120, x: 120 }"
												>
												</s-table>
											</a-spin>
										</a-menu>
									</a-dropdown>
								</a-form-item>
							</a-col>
							<a-col :md="12" :sm="12">
								<a-form-item label="测试代表" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-input
										type="hidden"
										v-decorator="[
											'testTechnologyRepresent',
											{ rules: [{ required: true, message: '请选择测试代表！' }] }
										]"
									/>
									<a-dropdown v-model="csdbDownVisible" placement="bottomCenter" :trigger="['click']">
										<a-button
											style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;"
											>{{ csdb ? csdb : "选择测试代表" }} <a-icon type="down"
										/></a-button>
										<a-menu slot="overlay">
											<a-spin :spinning="csdbLoading" style="padding:10px 24px 0 24px;width:100%">
												<a-input-search
													v-model="csdbQueryParam.searchValue"
													placeholder="搜索..."
													@change="oncsdbSearch"
												/>
												<s-table
													style="width:100%;"
													ref="csdbTable"
													:rowKey="record => record.id"
													:columns="vColumns"
													:data="loadcsdbData"
													:customRow="customcsdbRow"
													:scroll="{ y: 120, x: 120 }"
												>
												</s-table>
											</a-spin>
										</a-menu>
									</a-dropdown>
								</a-form-item>
							</a-col>
						</a-row>
					</a-form>
				</a-tab-pane>
				<a-tab-pane key="plan" :tab="tab3">
					<a-form :form="form3">
						<a-row :gutter="24">
							<a-col :md="12" :sm="24">
								<a-form-item label="K0 立项评审计划日期" has-feedback :labelCol="labelCol2" :wrapperCol="wrapperCol2">
									<a-date-picker
										style="width: 150px"
										:allow-clear="false"
										v-decorator="[
											'productPlannedK0',
											{ rules: [{ required: true, message: '请选择K0 立项评审计划日期' }] }
										]"
									/>
								</a-form-item>
							</a-col>
							<a-col :md="12" :sm="24">
								<a-form-item label="M1 项目计划日期" has-feedback :labelCol="labelCol2" :wrapperCol="wrapperCol2">
									<a-date-picker
										style="width: 150px"
										:disabledDate="date => disabledStartDate(date, 'productPlannedK0')"
										v-decorator="[
											'productPlannedM1',
											{ rules: [{ required: true, message: '请选择M1 项目计划日期' }] }
										]"
									/>
								</a-form-item>
							</a-col>
						</a-row>

						<a-row :gutter="24">
							<a-col :md="12" :sm="24">
								<a-form-item label="M2 A样冻结计划日期" has-feedback :labelCol="labelCol2" :wrapperCol="wrapperCol2">
									<a-date-picker
										style="width: 150px"
										:allow-clear="false"
										:disabledDate="date => disabledStartDate(date, 'productPlannedM1')"
										v-decorator="[
											'productPlannedM2A',
											{ rules: [{ required: true, message: '请选择M2 A样冻结计划日期' }] }
										]"
									/>
								</a-form-item>
							</a-col>
							<a-col :md="12" :sm="24">
								<a-form-item label="M2 转阶段计划日期" has-feedback :labelCol="labelCol2" :wrapperCol="wrapperCol2">
									<a-date-picker
										style="width: 150px"
										:allow-clear="false"
										:disabledDate="date => disabledStartDate(date, 'productPlannedM2A')"
										v-decorator="[
											'productPlannedM2z',
											{ rules: [{ required: true, message: '请选择M2 转阶段计划日期' }] }
										]"
									/>
								</a-form-item>
							</a-col>
						</a-row>

						<a-row :gutter="24">
							<a-col :md="12" :sm="24">
								<a-form-item label="M3 B样冻结计划日期" has-feedback :labelCol="labelCol2" :wrapperCol="wrapperCol2">
									<a-date-picker
										style="width: 150px"
										:allow-clear="false"
										:disabledDate="date => disabledStartDate(date, 'productPlannedM2z')"
										v-decorator="[
											'productPlannedM3B',
											{ rules: [{ required: true, message: '请选择M3 B样冻结计划日期' }] }
										]"
									/>
								</a-form-item>
							</a-col>
							<a-col :md="12" :sm="24">
								<a-form-item label="M3 转阶段计划时间" :labelCol="labelCol2" :wrapperCol="wrapperCol2" has-feedback>
									<a-date-picker
										style="width: 150px"
										:allow-clear="false"
										:disabledDate="date => disabledStartDate(date, 'productPlannedM3B')"
										v-decorator="[
											'productPlannedM3z',
											{ rules: [{ required: true, message: '请选择M3 转阶段计划时间' }] }
										]"
									/>
								</a-form-item>
							</a-col>
						</a-row>
						<a-row :gutter="24">
							<a-col :md="12" :sm="24">
								<a-form-item label="M4 C样冻结计划日期" :labelCol="labelCol2" :wrapperCol="wrapperCol2" has-feedback>
									<a-date-picker
										style="width: 150px"
										:allow-clear="false"
										:disabledDate="date => disabledStartDate(date, 'productPlannedM3z')"
										v-decorator="[
											'productPlannedM4',
											{ rules: [{ required: true, message: '请选择M4 C样冻结计划日期' }] }
										]"
									/>
								</a-form-item>
							</a-col>
							<a-col :md="12" :sm="24">
								<a-form-item label="M5 PPAP计划日期" :labelCol="labelCol2" :wrapperCol="wrapperCol2" has-feedback>
									<a-date-picker
										style="width: 150px"
										:allow-clear="false"
										v-decorator="[
											'productPlannedM5',
											{ rules: [{ required: true, message: '请选择M5 PPAP计划日期' }] }
										]"
										:disabledDate="date => disabledStartDate(date, 'productPlannedM4')"
									/>
								</a-form-item>
							</a-col>
						</a-row>

						<a-row :gutter="24">
							<a-col :md="12" :sm="24">
								<a-form-item label="M6 SOP计划日期" :labelCol="labelCol2" :wrapperCol="wrapperCol2" has-feedback>
									<a-date-picker
										style="width: 150px"
										:allow-clear="false"
										v-decorator="['productPlannedM6', { rules: [{ required: true, message: '请选择M6 SOP计划日期' }] }]"
										:disabledDate="date => disabledStartDate(date, 'productPlannedM5')"
									/>
								</a-form-item>
							</a-col>
						</a-row>
					</a-form>
				</a-tab-pane>
				<a-tab-pane key="confirm" :tab="tab4" disabled>
					<div style="padding: 50px;text-align: center;font-size: x-large;color: black;">
						已完成创建{{ productName }}项目，稍后将跳转至产品管理首页
					</div>
				</a-tab-pane>
			</a-tabs>
		</a-spin>

		<template slot="footer">
			<a-button key="back" @click="handleCancel">
				关闭
			</a-button>
			<a-button type="primary" v-if="activeKey == 'basic'" key="confirm" @click="handleSubmit1('framework')">
				下一步
			</a-button>
			<a-button type="primary" v-else-if="activeKey == 'framework'" key="confirm" @click="handleSubmit2('plan')">
				下一步
			</a-button>
			<a-button
				type="primary"
				v-else-if="activeKey == 'plan'"
				key="confirm"
				@click="handleSubmit"
				:loading="submitLoading"
			>
				创建
			</a-button>
		</template>
	</a-modal>
</template>

<script>
import moment from "moment"
import { productCreate } from "@/api/modular/system/report"
import { getUserLists } from "@/api/modular/system/userManage"
import { STable } from "@/components"

import { getCateTree } from "@/api/modular/system/topic"

export default {
	props: {
		type: {
			type: String,
			default: ""
		}
	},
	components: {
		STable
	},
	data() {
		return {
			columns1: [
				{ key: "productPlannedK0", name: "K0 立项评审计划日期" },
				{ key: "productPlannedM1", name: "M1 项目计划日期" },
				{ key: "productPlannedM2A", name: "M2 A样冻结计划日期" },
				{ key: "productPlannedM2z", name: "M2 转阶段计划日期" },
				{ key: "productPlannedM3B", name: "M3 B样冻结计划日期" },
				{ key: "productPlannedM3z", name: "M3 转阶段计划日期" },
				{ key: "productPlannedM4", name: "M4 C样冻结计划日期" },
				{ key: "productPlannedM5", name: "M5 PPAP计划日期" },
				{ key: "productPlannedM6", name: "M6 SOP计划日期" }
			],
			classOptions: [
				{ key: 1, label: "预研产品" },
				{ key: 2, label: "A|B新产品" },
				{ key: 3, label: "试产新产品" },
				{ key: 4, label: "量产品" },
				{ key: 5, label: "其他" },
				{ key: 6, label: "停止" }
			],
			submitLoading: false,
			loadYzData: parameter => {
				return getUserLists(Object.assign(parameter, this.yzQueryParam)).then(res => {
					return res.data
				})
			},
			cateList: [],
			deptList: [],
			yz: null,
			yzLoading: false,
			yzQueryParam: {}, //院长
			yzDownVisible: false, //院长

			loadSzData: parameter => {
				return getUserLists(Object.assign(parameter, this.szQueryParam)).then(res => {
					return res.data
				})
			},

			sz: null,
			szLoading: false,
			szQueryParam: {}, //所长
			szDownVisible: false, //所长

			loadXxzyData: parameter => {
				return getUserLists(Object.assign(parameter, this.xxzyQueryParam)).then(res => {
					return res.data
				})
			},

			xxzy: null, //信息专员
			xxzyLoading: false,
			xxzyQueryParam: {},
			xxzyDownVisible: false,

			loadcpzxjlData: parameter => {
				return getUserLists(Object.assign(parameter, this.cpzxjlQueryParam)).then(res => {
					return res.data
				})
			},

			cpzxjl: null, //产品中心经理
			cpzxjlLoading: false,
			cpzxjlQueryParam: {},
			cpzxjlDownVisible: false,

			loadxmzjData: parameter => {
				return getUserLists(Object.assign(parameter, this.xmzjQueryParam)).then(res => {
					return res.data
				})
			},

			xmzj: null, //项目总监
			xmzjLoading: false,
			xmzjQueryParam: {},
			xmzjDownVisible: false,

			loadgczxjlData: parameter => {
				return getUserLists(Object.assign(parameter, this.gczxjlQueryParam)).then(res => {
					return res.data
				})
			},

			gczxjl: null, //工程中心经理
			gczxjlLoading: false,
			gczxjlQueryParam: {},
			gczxjlDownVisible: false,

			loadjgzxjlData: parameter => {
				return getUserLists(Object.assign(parameter, this.jgzxjlQueryParam)).then(res => {
					return res.data
				})
			},

			jgzxjl: null, //结构中心经理
			jgzxjlLoading: false,
			jgzxjlQueryParam: {},
			jgzxjlDownVisible: false,

			loadgxzxjlData: parameter => {
				return getUserLists(Object.assign(parameter, this.gxzxjlQueryParam)).then(res => {
					return res.data
				})
			},

			gxzxjl: null, //共性中心经理
			gxzxjlLoading: false,
			gxzxjlQueryParam: {},
			gxzxjlDownVisible: false,

			loadbgsjlData: parameter => {
				return getUserLists(Object.assign(parameter, this.bgsjlQueryParam)).then(res => {
					return res.data
				})
			},

			bgsjl: null, //办公室经理
			bgsjlLoading: false,
			bgsjlQueryParam: {},
			bgsjlDownVisible: false,

			loadcsjlData: parameter => {
				return getUserLists(Object.assign(parameter, this.csjlQueryParam)).then(res => {
					return res.data
				})
			},

			csjl: null, //测试经理
			csjlLoading: false,
			csjlQueryParam: {},
			csjlDownVisible: false,

			loadcpzjData: parameter => {
				return getUserLists(Object.assign(parameter, this.cpzjQueryParam)).then(res => {
					return res.data
				})
			},

			cpzj: null, //产品总监
			cpzjLoading: false,
			cpzjQueryParam: {},
			cpzjDownVisible: false,

			loadcpjlData: parameter => {
				return getUserLists(Object.assign(parameter, this.cpjlQueryParam)).then(res => {
					return res.data
				})
			},

			cpjl: null, //产品经理
			cpjlLoading: false,
			cpjlQueryParam: {},
			cpjlDownVisible: false,

			loadyfxmjlData: parameter => {
				return getUserLists(Object.assign(parameter, this.yfxmjlQueryParam)).then(res => {
					return res.data
				})
			},

			yfxmjl: null, //研发项目经理
			yfxmjlLoading: false,
			yfxmjlQueryParam: {},
			yfxmjlDownVisible: false,

			loadgcdbData: parameter => {
				return getUserLists(Object.assign(parameter, this.gcdbQueryParam)).then(res => {
					return res.data
				})
			},

			gcdb: null, //工程代表
			gcdbLoading: false,
			gcdbQueryParam: {},
			gcdbDownVisible: false,

			loadjgdbData: parameter => {
				return getUserLists(Object.assign(parameter, this.jgdbQueryParam)).then(res => {
					return res.data
				})
			},

			jgdb: null, //结构代表
			jgdbLoading: false,
			jgdbQueryParam: {},
			jgdbDownVisible: false,

			loadgxdbData: parameter => {
				return getUserLists(Object.assign(parameter, this.gxdbQueryParam)).then(res => {
					return res.data
				})
			},

			gxdb: null, //共性代表
			gxdbLoading: false,
			gxdbQueryParam: {},
			gxdbDownVisible: false,

			loaddqeData: parameter => {
				return getUserLists(Object.assign(parameter, this.dqeQueryParam)).then(res => {
					return res.data
				})
			},

			dqe: null, //DQE
			dqeLoading: false,
			dqeQueryParam: {},
			dqeDownVisible: false,

			loadcsdbData: parameter => {
				return getUserLists(Object.assign(parameter, this.csdbQueryParam)).then(res => {
					return res.data
				})
			},

			csdb: null, //测试代表
			csdbLoading: false,
			csdbQueryParam: {},
			csdbDownVisible: false,

			loadcpgcsData: parameter => {
				return getUserLists(Object.assign(parameter, this.cpgcsQueryParam)).then(res => {
					return res.data
				})
			},

			cpgcs: null, //测试代表
			cpgcsLoading: false,
			cpgcsQueryParam: {},
			cpgcsDownVisible: false,
			cpgcsselectedRowKeys: [],
			cpgcsselectedRow: [],
			gcdbselectedRowKeys: [],
			gcdbselectedRow: [],

			tab1: null,
			tab2: null,
			tab3: null,
			tab4: null,
			data: [],
			productName: null,
			vColumns: [
				{
					title: "账号",
					dataIndex: "account"
				},
				{
					title: "姓名",
					dataIndex: "name"
				}
			],
			columns: [
				{
					title: "中检次数",
					dataIndex: "index",
					align: "center",
					width: 50,
					customRender: (text, record, index) => `第${index + 1}次中检`
				},
				{
					title: "第x天",
					width: 90,
					align: "center",
					dataIndex: "day",
					scopedSlots: {
						customRender: "day"
					}
				},
				{
					title: "进",
					width: 90,
					align: "center",
					dataIndex: "inDate"
				},
				{
					title: "出",
					width: 90,
					align: "center",
					dataIndex: "outDate"
				}
			],
			activeKey: "basic",
			labelCol: {
				xs: {
					span: 8
				},
				sm: {
					span: 8
				}
			},
			wrapperCol: {
				xs: {
					span: 24
				},
				sm: {
					span: 14
				}
			},
			labelCol2: {
				xs: {
					span: 12
				},
				sm: {
					span: 10
				}
			},
			wrapperCol2: {
				xs: {
					span: 24
				},
				sm: {
					span: 14
				}
			},
			visible: false,
			confirmLoading: false,
			form1: this.$form.createForm(this),
			form2: this.$form.createForm(this),
			form3: this.$form.createForm(this)
		}
	},
	computed: {
		cpgcsrowSelection: {
			get() {
				// 在这里定义 getter 方法中的逻辑
			},
			set() {
				// 在这里定义 setter 方法中的逻辑（如果需要使用 setter 方法）
			},
			columnWidth: 30,
			onChange: (selectedRowKeys, selectedRows) => {
				this.cpgcsselectedRowKeys = selectedRowKeys
				this.cpgcsselectedRow = selectedRows

				for (let i = 0; i < this.cpgcsselectedRow.length; i++) {
					if (i == 0) {
						this.cpgcs = this.cpgcsselectedRow[i].name
					} else {
						this.cpgcs += "," + this.cpgcsselectedRow[i].name
					}
				}

				this.form2.setFieldsValue({
					productEnginMulti: this.cpgcsselectedRowKeys.join(",")
				})
			}
		},
		gcdbrowSelection: {
			get() {
				// 在这里定义 getter 方法中的逻辑
			},
			set() {
				// 在这里定义 setter 方法中的逻辑（如果需要使用 setter 方法）
			},
			columnWidth: 30,
			onChange: (selectedRowKeys, selectedRows) => {
				this.gcdbselectedRowKeys = selectedRowKeys
				this.gcdbselectedRow = selectedRows

				for (let i = 0; i < this.gcdbselectedRow.length; i++) {
					if (i == 0) {
						this.gcdb = this.gcdbselectedRow[i].name
					} else {
						this.gcdb += "," + this.gcdbselectedRow[i].name
					}
				}

				this.form2.setFieldsValue({
					engineCenterRepresentMulti: this.gcdbselectedRowKeys.join(",")
				})
			}
		}
	},
	created() {
		getCateTree({
			fieldName: "productCate"
		})
			.then(res => (this.cateList = res.data))
			.then(res => {
				getCateTree({
					fieldName: "department"
				}).then(res => (this.deptList = res.data))
			})

		this.init()
	},
	methods: {
		checkDate(column) {
			if (this.form3.getFieldValue(column) == null) {
				return true
			}
			let columnKeyName = null
			let before = null
			let beforeColumn = null
			let after = null
			let afterColumn = null
			for (let i = 0; i < this.columns1.length; i++) {
				if (column == this.columns1[i].key) {
					columnKeyName = this.columns1[i]
					for (let j = 1; j < i; j++) {
						if (this.form3.getFieldValue(this.columns1[i - j].key) != null) {
							before = this.form3.getFieldValue(this.columns1[i - j].key)
							beforeColumn = this.columns1[i - j]
							break
						}
					}
					for (let j = i + 1; j < this.columns1.length; j++) {
						if (this.form3.getFieldValue(this.columns1[j].key) != null) {
							after = this.form3.getFieldValue(this.columns1[j].key)
							afterColumn = this.columns1[j]
							break
						}
					}
				}
			}

			if (null != before) {
				if (this.form3.getFieldValue(column) < before) {
					this.$message.error(columnKeyName.name + "不能在" + beforeColumn.name + "之前", 3)
					return false
				}
			}

			if (null != after) {
				if (this.form3.getFieldValue(column) > after) {
					this.$message.error(columnKeyName.name + "不能在" + afterColumn.name + "之后", 3)
					return false
				}
			}

			return true
		},

		disabledStartDate(startValue, column) {
			return startValue < moment(this.form3.getFieldValue(column))
		},

		onYzSearch(e) {
			this.$refs.yzTable.refresh()
		},
		customYzRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							president: row.account
						})
						this.yz = row.name
						this.yzDownVisible = false
					}
				}
			}
		},
		onSzSearch(e) {
			this.$refs.szTable.refresh()
		},
		customSzRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							headOfTheInstitute: row.account
						})
						this.sz = row.name
						this.szDownVisible = false
					}
				}
			}
		},
		onXxzySearch(e) {
			this.$refs.xxzyTable.refresh()
		},
		customXxzyRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							infoCommissioner: row.account
						})
						this.xxzy = row.name
						this.xxzyDownVisible = false
					}
				}
			}
		},
		oncpzxjlSearch(e) {
			this.$refs.cpzxjlTable.refresh()
		},
		customcpzxjlRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							productCenterManager: row.account
						})
						this.cpzxjl = row.name
						this.cpzxjlDownVisible = false
					}
				}
			}
		},
		onxmzjSearch(e) {
			this.$refs.xmzjTable.refresh()
		},
		customxmzjRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							projectMajordomo: row.account
						})
						this.xmzj = row.name
						this.xmzjDownVisible = false
					}
				}
			}
		},
		ongczxjlSearch(e) {
			this.$refs.gczxjlTable.refresh()
		},
		customgczxjlRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							engineCenterManager: row.account
						})
						this.gczxjl = row.name
						this.gczxjlDownVisible = false
					}
				}
			}
		},
		onjgzxjlSearch(e) {
			this.$refs.jgzxjlTable.refresh()
		},
		customjgzxjlRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							structureCenterManager: row.account
						})
						this.jgzxjl = row.name
						this.jgzxjlDownVisible = false
					}
				}
			}
		},
		ongxzxjlSearch(e) {
			this.$refs.gxzxjlTable.refresh()
		},
		customgxzxjlRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							commonCenterManager: row.account
						})
						this.gxzxjl = row.name
						this.gxzxjlDownVisible = false
					}
				}
			}
		},
		onbgsjlSearch(e) {
			this.$refs.bgsjlTable.refresh()
		},
		custombgsjlRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							officeManager: row.account
						})
						this.bgsjl = row.name
						this.bgsjlDownVisible = false
					}
				}
			}
		},
		oncsjlSearch(e) {
			this.$refs.csjlTable.refresh()
		},
		customcsjlRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							testManager: row.account
						})
						this.csjl = row.name
						this.csjlDownVisible = false
					}
				}
			}
		},
		oncpzjSearch(e) {
			this.$refs.cpzjTable.refresh()
		},
		customcpzjRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							productTechMajordomo: row.account
						})
						this.cpzj = row.name
						this.cpzjDownVisible = false
					}
				}
			}
		},
		oncpjlSearch(e) {
			this.$refs.cpjlTable.refresh()
		},
		customcpjlRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							productManager: row.account
						})
						this.cpjl = row.name
						this.cpjlDownVisible = false
					}
				}
			}
		},
		onyfxmjlSearch(e) {
			this.$refs.yfxmjlTable.refresh()
		},
		customyfxmjlRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							productRPM: row.account
						})
						this.yfxmjl = row.name
						this.yfxmjlDownVisible = false
					}
				}
			}
		},
		ongcdbSearch(e) {
			this.$refs.gcdbTable.refresh()
		},
		customgcdbRow(row, index) {
			return {
				on: {
					click: () => {
						if (this.gcdbselectedRow.includes(row)) {
							this.gcdbselectedRow = this.gcdbselectedRow.filter(r => r != row)
							this.gcdbselectedRowKeys = this.gcdbselectedRow.filter(r => r != row.id)
						} else {
							this.gcdbselectedRow.push(row)
							this.gcdbselectedRowKeys.push(row.id)
						}

						let ids = ""
						this.gcdb = ""
						for (let i = 0; i < this.gcdbselectedRow.length; i++) {
							if (i == 0) {
								this.gcdb = this.gcdbselectedRow[i].name
								ids += this.gcdbselectedRow[i].account
							} else {
								this.gcdb += "," + this.gcdbselectedRow[i].name
								ids += "," + this.gcdbselectedRow[i].account
							}
						}

						this.form2.setFieldsValue({
							engineCenterRepresentMulti: ids
						})
					}
				}
			}
		},
		onjgdbSearch(e) {
			this.$refs.jgdbTable.refresh()
		},
		customjgdbRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							structureTechnologyRepresent: row.account
						})
						this.jgdb = row.name
						this.jgdbDownVisible = false
					}
				}
			}
		},
		ongxdbSearch(e) {
			this.$refs.gxdbTable.refresh()
		},
		customgxdbRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							commonCenterRepresent: row.account
						})
						this.gxdb = row.name
						this.gxdbDownVisible = false
					}
				}
			}
		},
		ondqeSearch(e) {
			this.$refs.dqeTable.refresh()
		},
		customdqeRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							productDQE: row.account
						})
						this.dqe = row.name
						this.dqeDownVisible = false
					}
				}
			}
		},
		oncsdbSearch(e) {
			this.$refs.csdbTable.refresh()
		},
		customcsdbRow(row, index) {
			return {
				on: {
					click: () => {
						this.form2.setFieldsValue({
							testTechnologyRepresent: row.account
						})
						this.csdb = row.name
						this.csdbDownVisible = false
					}
				}
			}
		},
		oncpgcsSearch(e) {
			this.$refs.cpgcstable.refresh()
		},
		customcpgcsRow(row, index) {
			return {
				on: {
					click: () => {
						if (this.cpgcsselectedRow.includes(row)) {
							this.cpgcsselectedRow = this.cpgcsselectedRow.filter(r => r != row)
							this.cpgcsselectedRowKeys = this.cpgcsselectedRow.filter(r => r != row.id)
						} else {
							this.cpgcsselectedRow.push(row)
							this.cpgcsselectedRowKeys.push(row.id)
						}

						let ids = ""
						this.cpgcs = ""
						for (let i = 0; i < this.cpgcsselectedRow.length; i++) {
							if (i == 0) {
								this.cpgcs = this.cpgcsselectedRow[i].name
								ids += this.cpgcsselectedRow[i].account
							} else {
								this.cpgcs += "," + this.cpgcsselectedRow[i].name
								ids += "," + this.cpgcsselectedRow[i].account
							}
						}

						this.form2.setFieldsValue({
							productEnginMulti: ids
						})
					}
				}
			}
		},

		changeTab(key) {
			if ("framework" == key) {
				this.handleSubmit1()
			} else if ("plan" == key) {
				this.handleSubmit2()
				this.$nextTick(() => {
					this.handleSubmit1()
				})
			} else if ("confirm" == key) {
				this.handleSubmit3()
				this.$nextTick(() => {
					this.handleSubmit2()
				})
				this.$nextTick(() => {
					this.handleSubmit1()
				})
			} else if (key == "confirm") {
				this.activeKey = "plan"
			}
		},
		init() {
			/**
			 *
			 * @type {string}
			 */
			let className1 = this.activeKey == "basic" ? "active" : "not_active"
			let className2 = this.activeKey == "framework" ? "active" : "not_active"
			let className3 = this.activeKey == "plan" ? "active" : "not_active"
			let className4 = this.activeKey == "confirm" ? "active" : "not_active"

			this.tab1 = (
				<div>
					<span class={className1}> 1 </span>
					<div>
						<div class="out_text">输入产品项目信息</div>
						<div class="in_text">根据项目实际情况填写</div>
					</div>
				</div>
			)
			this.tab2 = (
				<div>
					<span class={className2}> 2 </span>
					<div>
						<div class="out_text">创建组织架构</div>
						<div class="in_text">根据项目实际情况选择人员</div>
					</div>
				</div>
			)
			this.tab3 = (
				<div>
					<span class={className3}> 3 </span>
					<div>
						<div class="out_text">填写开发计划</div>
						<div class="in_text">选择相应时间节点</div>
					</div>
				</div>
			)
			this.tab4 = (
				<div>
					<span class={className4}> 4 </span>
					<div>
						<div class="out_text">完成创建</div>
						<div class="in_text">创建成功</div>
					</div>
				</div>
			)
		},

		add() {
			this.visible = true
		},
		onChangeSampleDate(date, dateString) {
			if (date == null) {
				this.startDate = ""
			} else {
				this.startDate = moment(date).format("YYYY-MM-DD")
			}
		},
		handleSubmit1(tab) {
			const {
				form1: { validateFields }
			} = this

			validateFields((errors, values) => {
				if (!errors) {
					if (null != tab) {
						this.activeKey = tab
					}

					if (values.fixedState == 0 && values.plannedFixedDate == null) {
						this.activeKey = "basic"
						this.$message.warn("请填写计划定点日期")
					}
				} else {
					this.activeKey = "basic"
				}
				this.init()
			})
		},
		handleSubmit2(tab) {
			const {
				form2: { validateFields }
			} = this

			validateFields((errors, values) => {
				if (!errors) {
					if (null != tab) {
						this.activeKey = tab
					}
				} else {
					this.activeKey = "framework"
				}
				this.init()
			})
		},
		handleSubmit3(tab) {
			const {
				form3: { validateFields }
			} = this

			validateFields((errors, values) => {
				if (!errors) {
					for (let i = 0; i < this.columns1.length; i++) {
						let flag = this.checkDate(this.columns1[i].key)
						if (!flag) {
							this.activeKey = "plan"
							return
						}
					}

					if (null != tab) {
						this.activeKey = tab
					}
				} else {
					this.activeKey = "plan"
				}

				this.init()
			})
		},
		handleSubmit() {
			this.$nextTick(() => {
				this.handleSubmit2()
			})
			this.$nextTick(() => {
				this.handleSubmit1()
			})

			this.$nextTick(() => {
				if (this.activeKey == "plan") {
					const {
						form3: { validateFields }
					} = this

					validateFields((errors, values) => {
						if (!errors) {
							for (let i = 0; i < this.columns1.length; i++) {
								let flag = this.checkDate(this.columns1[i].key)
								if (!flag) {
									this.activeKey = "plan"
									return
								}
							}

							let form1 = this.form1.getFieldsValue()
							let form2 = this.form2.getFieldsValue()
							let params = Object.assign(form1, form2, values)

							params.productMultiCate = params.productMultiCate.join(",")
							params.productPlannedK0 = moment(params.productPlannedK0).format("YYYY-MM-DD")
							params.productPlannedM1 = moment(params.productPlannedM1).format("YYYY-MM-DD")
							params.productPlannedM2A = moment(params.productPlannedM2A).format("YYYY-MM-DD")
							params.productPlannedM2z = moment(params.productPlannedM2z).format("YYYY-MM-DD")
							params.productPlannedM3B = moment(params.productPlannedM3B).format("YYYY-MM-DD")
							params.productPlannedM3z = moment(params.productPlannedM3z).format("YYYY-MM-DD")
							params.productPlannedM4 = moment(params.productPlannedM4).format("YYYY-MM-DD")
							params.productPlannedM5 = moment(params.productPlannedM5).format("YYYY-MM-DD")
							params.productPlannedM6 = moment(params.productPlannedM6).format("YYYY-MM-DD")
							if (null != params.plannedFixedDate) {
								params.plannedFixedDate = moment(params.plannedFixedDate).format("YYYY-MM-DD")
							}

							this.submitLoading = true
							productCreate(params).then(res => {
								if (res.success) {
									this.$message.success("保存成功")
									this.submitLoading = false
									this.$nextTick(() => {
										this.activeKey = "confirm"
										setTimeout(() => {
											this.handleCancel()
										}, 3000)
									})
								} else {
									this.submitLoading = false
									this.$message.error(res.message)
								}
							}) /*.finally(res => this.submitLoading = false)*/
						} else {
							return
						}
					})
				}
			})
		},
		handleCancel() {
			this.form1.resetFields()
			this.form2.resetFields()
			this.form3.resetFields()
			this.visible = false
			this.$emit("ok")
		}
	}
}
</script>
<style lang="less" scoped="">
.ant-form-item {
	margin-bottom: 0px;
}

/deep/ .ant-modal-body {
	padding: 0 !important;
}

/deep/ .ant-table-thead > tr > th,
/deep/ .ant-table-tbody > tr > td {
	padding: 3px;
}

/deep/ .ant-table-footer {
	padding: 0px;
}

/deep/ .ant-table-pagination.ant-pagination {
	margin: 5px 0;
}

/deep/ .ant-input-number {
	width: 100%;
}

/deep/ .ant-input-number-sm > .ant-input-number-input-wrap > .ant-input-number-input {
	text-align: center;
}

/deep/ .ant-tabs-nav-scroll {
	display: flex;
	justify-content: center;
}

/deep/ .ant-tabs-tab-active {
	color: black;
	font-weight: bolder;
}

.active {
	color: white;
	border-radius: 20px;
	align-items: center;
	background: #1890ff;
	margin-right: 15px;
	width: 25px;
	height: 25px;
	margin-top: 3px;
	display: inline-flex;
	justify-content: center;
	position: relative;
	float: left;
	font-size: large;
}

.not_active {
	color: black;
	border-radius: 20px;
	align-items: center;
	background: lightgrey;
	margin-right: 15px;
	width: 25px;
	height: 25px;
	margin-top: 3px;
	display: inline-flex;
	justify-content: center;
	position: relative;
	float: left;
	font-size: large;
}

.in_text {
	margin-left: 35px;
	color: grey;
	font-size: xx-small;
}

.out_text {
	font-size: large;
}
.Timeline {
	margin-top: 40px;
	.timeaxis {
		height: 50px;
		margin-top: 80px;
		margin-left: 4em;
		display: flex;
		.timeaxis-box {
			width: 100px;
			.circular {
				width: 12px;
				height: 12px;
				border-radius: 16px;
				background: #4ef72d;
				margin-bottom: -4px;
				position: relative;
				top: -8px;
			}
			.circular1 {
				width: 12px;
				height: 12px;
				border-radius: 16px;
				background: #23a9d5;
				margin-bottom: -4px;
				position: relative;
				top: -8px;
			}
			.timeaxis-topText {
				border-bottom: 2px solid #c9cdd4;
				position: relative;
				.text {
					position: absolute;
					top: -60px;
				}
				.tiem {
					position: absolute;
					top: -35px;
					font-size: 14px;
					color: #9ba3ad;
				}
			}
			.bd-empty {
				border: 0;
			}
			.timeaxis-bootomText {
				position: relative;
				.text {
					position: absolute;
					top: 10px;
					font-size: 14px;
				}
			}
		}
		.timeaxis-box1 {
			width: 35px;
			.circular {
				width: 12px;
				height: 12px;
				border-radius: 16px;
				background: #0ef753;
				margin-bottom: -4px;
				position: relative;
				top: -8px;
			}
			.circular1 {
				width: 12px;
				height: 12px;
				border-radius: 16px;
				background: #23a9d5;
				margin-bottom: -4px;
				position: relative;
				top: -30px;
			}
			.timeaxis-topText {
				border-top: 2px solid #c9cdd4;
				position: relative;
				.text {
					position: absolute;
					top: -60px;
				}
				.tiem {
					position: absolute;
					top: -35px;
					font-size: 14px;
					color: #9ba3ad;
				}
			}
			.bd-empty {
				border: 0;
			}
			.timeaxis-bootomText {
				position: relative;
				.text {
					position: absolute;
					top: 10px;
					font-size: 14px;
				}
			}
		}
	}
}

.downText {
	position: absolute;
	top: 5px;
	color: black;
	font-weight: bolder;
}
.downTime {
	position: absolute;
	top: 25px;
}
.downMsg {
	position: absolute;
	top: 60px;
	width: 120px;
}

.upText {
	position: absolute;
	top: -100px;
	color: black;
	font-weight: bolder;
}
.upTime {
	position: absolute;
	top: -75px;
}
.upMsg {
	position: absolute;
	top: -40px;
}

/deep/.ant-tabs-tab-disabled,
/deep/.ant-tabs-tab-disabled:hover {
	color: rgb(135 135 135);
}
</style>
