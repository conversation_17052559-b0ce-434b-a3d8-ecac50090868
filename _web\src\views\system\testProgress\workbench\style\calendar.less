.time-block {
	margin: 50px 0;
	text-align: center;
}

// 通用
.mt10 {
	margin-top: 10px;
}

.mr10 {
	margin-right: 10px;
}


/deep/ .ant-descriptions-view tr th{
	padding:4px 8px !important;
}
/deep/ .ant-descriptions-view tr td{
	padding:4px 8px !important;
}

/deep/tr th {
	padding:8px !important;
	font-size: 13px !important;
	font-weight: 500;
}

/deep/tr td {
	padding:8px !important;
	background: #fff;
	font-size: 12px !important;
	font-weight: 400;
}

// 固定前两列
/deep/ .auto-table .ant-table-thead tr:nth-child(1) th:nth-child(1){
	position: sticky;
	left: 0;
	z-index: 100;
}
/deep/ .auto-table .ant-table-thead tr:nth-child(1) th:nth-child(2){
	position: sticky;
	left: 100px;
	z-index: 100;
}

/deep/ .auto-table .ant-table-tbody tr td:nth-child(1){
	position: sticky;
	left: 0;
	z-index: 100;
}
/deep/ .auto-table .ant-table-tbody tr td:nth-child(2){
	position: sticky;
	left: 100px;
	z-index: 100;
}

// 表格
/deep/ .ant-table-body {
	height: 100% !important;
}

/deep/.ant-table-thead {
	position: sticky;
	top: 0;
	z-index: 999;
}

/deep/.ant-table-placeholder {
	border: none !important;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
  display: contents;
}

/deep/ .child-table .ant-table-placeholder {
	border: none !important;
	position: static;
	transform: translate(0%, 0%);
}

/deep/.ant-modal-header {
	border: none !important;
	padding: 10px 24px 0;
}

/deep/.ant-modal-body {
	padding: 5px 16px !important;
}

/deep/.ant-modal-footer {
	padding: 0 16px 10px;
	border: none !important;
}

/deep/.ant-descriptions-title {
	margin: 5px 0;
	text-align: center;
}

/deep/.ant-steps-small .ant-steps-item-icon {
	width: 30px;
	height: 30px;
	font-size: 16px;
	line-height: 30px;
	border-radius: 50%;
}

/deep/.ant-steps-item-finish .ant-steps-item-icon {
	background-color: rgb(52, 224, 158);
	border: 2px solid rgba(52, 224, 158, 0.15);
}

/deep/.ant-steps-item-finish .ant-steps-item-icon .ant-steps-icon {
	color: #fff;
}
