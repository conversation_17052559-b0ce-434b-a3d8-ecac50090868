<template>
  <div class="pbi-title">
    <div class="line mr8"></div>
    <span class="title" :style="`fontSize:${fontSize}px`">{{title}}</span>
  </div>
</template>
<script>
  export default {
    props: {
      title: {
        type: String,
        default: ''
      },
      fontSize:{
        type:Number,
        default:16
      }
    },
    created() {
      document.documentElement.style.setProperty(`--lineHeight`, `${this.fontSize + 6}px`)
    },
  }
</script>
<style lang='less' scoped>
:root {
  --lineHeight: 20px;
}
  /* 标题 */
  .pbi-title {
    display: flex;
    align-items: center;
    color: #333;
  }

  .pbi-title .line {
    width: 4px;
    height: var(--lineHeight);
    background: #1890ff;
    border-radius: 10px;
  }

  .pbi-title .title {
    font-weight: 600;
  }

  .mr8{
    margin-right: 8px;
  }
</style>