<template>
  <div>
    <div style="margin: 35px 0px 10px 15px;">
      <a-breadcrumb separator=">">
        <a-breadcrumb-item>
          <router-link  :to="{ path: '/batterydesign', query: { type: breadcrumb.type } }">
						    <a-icon type="rollback" class="rollback-icon" />
            {{ breadcrumb.type === 'product' ? '项目产品' : '技术平台' }}设计
					    </router-link>
        </a-breadcrumb-item>
        <a-breadcrumb-item>
          <router-link  :to="{path:'/design_battery_index',query:breadcrumb}">
          {{ breadcrumb.batteryName+'('+ (breadcrumb.designType === 'design' ? '研发' : '冻结') +')' }}
        </router-link>
        </a-breadcrumb-item>
        <a-breadcrumb-item>
          {{breadcrumb.code}} MI设计
          <a-menu slot="overlay">
            <a-menu-item>
              <a target="_blank" @click="gotoDesgin">
                方案设计
              </a>
            </a-menu-item>
            <a-menu-item>
              <a target="_blank" @click="gotoBom">
                电芯BOM设计
              </a>
            </a-menu-item>
          </a-menu>
        </a-breadcrumb-item>
      </a-breadcrumb>
    </div>

    <div class="tab-title">
      <div class="tab-head">
        <div class="active">MI设计</div>
      </div>
    </div>

    <div v-if="!needImportFlag">
      <a-tabs @change="tabCallback" v-model="activeKey">
        <a-tab-pane class="changeHisTabClass" key="changeHistoryTab" tab="文件目录修订清单">
          <doc-change-history-imp :deliverImpBatteryId="impBatteryId" :canUpdate="canUpdate" ref="changeHisRef"/>
        </a-tab-pane>
        <a-tab-pane class="miTabClass" key="miTab" tab="MI">
          <mi-version-imp :deliverImpBatteryId="impBatteryId" :canUpdate="canUpdate" ref="miVersionRef"/>
        </a-tab-pane>
        <a-tab-pane class="attachVerTabClass" key="attachVersionTab" tab="附图">
          <mi-attach-version-imp :deliverImpBatteryId="impBatteryId" :canUpdate="canUpdate" ref="attachVerRef"/>
        </a-tab-pane>
      </a-tabs>
    </div>

    <div v-else style="margin-top: 10px">
      <a-icon type="plus" :style="{paddingLeft: bigClient?'2.1%':'1.85%'}" v-if="canUpdate"
              style="font-size:18px;color:dodgerblue"/>
      <span style="margin-left:5px;font-size:15px;color:dodgerblue"><a @click="miChooseModelImport()" v-if="canUpdate">MI选型导入</a></span>
    </div>
    <mi-lib-data-dialog :impBatteryId="impBatteryId" @importSuccess="importSuccess" ref="miLibDataDialog"/>
  </div>
</template>
<script>
import {
  getMIStandardByImpBatteryId,
} from '@/api/modular/system/gCylinderMILibManage'
import moment from "moment/moment";
import Vue from "vue";
import { ACCESS_TOKEN } from "@/store/mutation-types";
import { getBatteryDesign,canBeUpdate } from "@/api/modular/system/batterydesignManage";
import miLibDataDialog from "@/views/system/batterydesignStandard/miLibDataDialog.vue";
import docChangeHistoryImp from "@/views/system/batterydesignStandard/docChangeHistoryImp.vue";
import miVersionImp from "@/views/system/batterydesignStandard/miVersionImp.vue";
import miAttachVersionImp from "@/views/system/batterydesignStandard/miAttachVersionImp.vue";
  export default {
    components: { miLibDataDialog, docChangeHistoryImp, miVersionImp, miAttachVersionImp },
    data() {
      return {
        canUpdate:false,
        isOwn:0,
        breadcrumb:{}, // 面包屑
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 8
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 14
          }
        },
        visible: false,
        activeKey: 'changeHistoryTab',
        needImportFlag: false,
        libraryId: null,
        confirmLoading: false,
        form: this.$form.createForm(this,{ name: 'form' }),
        bigClient: document.documentElement.clientHeight > 700,
        structureType1: '',
        design: {},
        productName1: '',
        impBatteryId: null,
        headers: {
          Authorization: 'Bearer ' + Vue.ls.get(ACCESS_TOKEN),
        },
      };
    },
    created() {
      this.impBatteryId = this.$route.query.batteryId
      canBeUpdate({inBatteryId:this.$route.query.batteryId,type:'design'}).then(res => {
        this.canUpdate = res.data
        this.isOwn = res.data?1:0
      })
      this.getData()
      this.getBatteryDesignMethod(this.impBatteryId)

      this.breadcrumb = JSON.parse(localStorage.getItem('breadcrumb'))
    },
    mounted() {




      document.getElementsByClassName("ant-layout-content")[0].style.backgroundColor = 'white';
    },
    methods: {
      tabCallback(key) {
      },
      getBatteryDesignMethod(id) {
        getBatteryDesign({ inBatteryId: id, type: 'design' }).then(res => {
          this.design = res.data
          this.productName1 = this.design.productName
          switch (res.data.structureType) {
            case 'g_cylinder':
              this.structureType1 = 'G圆柱'
              break
            case 'c_cylinder':
              this.structureType1 = 'C圆柱'
              break
            case 'v_cylinder':
              this.structureType1 = 'V圆柱'
              break
            case 'winding':
              this.structureType1 = '方形卷绕'
              break
            case 'lamination':
              this.structureType1 = '方形叠片'
              break
            case 'soft_roll':
              this.structureType1 = '软包'
              break
          }
        })
      },
      getData() {
        getMIStandardByImpBatteryId(this.impBatteryId).then(res => {
          this.libraryId = res.data
          this.needImportFlag = !this.libraryId
        })
      },
      importSuccess(flag) {
        this.needImportFlag = flag
      },
      gotoBom() {
        //this.switchApp()
        this.$router.push({
          path: "/sys_battery_design_bom",
          query: {
            batteryId: this.impBatteryId
          },
        });
      },
      gotoDesgin() {
        //this.switchApp()
        this.$router.push({
          path: "/battery_design_manager",
          query: {
            batteryId: this.impBatteryId
          },
        });
      },
      gotoDevelop() {
        this.$router.push({
          path: "/batterydesign"
        });
      },
      checkMiVersion(record) {
        // this.$router.push(
        //   {
        //     path: "/g_cylinder_mi_version",
        //     query: {
        //       record: record
        //     },
        //   }
        // )
      },
      checkMiAttachVersion(record) {
        // this.$router.push(
        //   {
        //     path: "/g_cylinder_mi_attach_version",
        //     query: {
        //       record: record
        //     },
        //   }
        // )
      },
      checkDocumentChangeHistory(record) {
        // this.$router.push(
        //   {
        //     path: "/g_cylinder_doc_change_history",
        //     query: {
        //       record: record
        //     },
        //   }
        // )
      },
      renderUpdateTime(text) {
        return text == null ? '' : moment(new Date(text)).format('YYYY-MM-DD')
      },
      miChooseModelImport() {
        this.$refs.miLibDataDialog.miChooseModelImport(this.impBatteryId, this.libraryId)
      },
      handleCancel() {
        this.visible = false
      },
    }
  }
</script>
<style lang="less" scoped>

// 标题
.tab-title{
    padding: 0 10px;
  }
  .tab-head{
    border-bottom: 1px solid #d3d2d2c9;
  }

  .tab-head div:first-child{
    display: inline-block;

    font-weight: 700;
    font-size: 18px;
    color: rgb(128, 128, 128);
    margin-bottom: -6px;
    cursor: pointer;
  }

  .tab-head div:first-child.active{
    font-size: 24px;
    color: rgba(0,73,176,1);
    margin-bottom: -4px;
    cursor: text;
  }

/deep/.ant-table-bordered.ant-table-empty .ant-table-placeholder {
  border: 1px solid black;
}
#miStandardId > div > div > div > div > div > table{
  border: 0;
}
#miStandardId > div > div > div > div > div > table > .ant-table-thead > tr > .ant-table-align-center{
  background-color: #F5F5F5;
  border-bottom: 1.5px solid #C0C0C0;
}
#miStandardId > div > div > div > div > div > table > .ant-table-tbody > tr > td{
  border-bottom: 1.5px solid #C0C0C0;
}
#miStandardId > div > div > div > div > div > table > .ant-table-tbody > tr:last-child > td{
  border: 0;
}
#miModalId > div > div > div > div > .ant-modal-title {
  color: dodgerblue;
  font-size: 20px;
  font-weight: bold;
}
/deep/.ant-table-thead > tr > th, .ant-table-tbody > tr > td {
  padding: 10px 10px;
  overflow-wrap: break-word;
}
// /deep/.ant-breadcrumb a, .ant-breadcrumb span {
//   color: black;
//   font-weight: bold;
// }
/deep/.ant-table-bordered .ant-table-tbody > tr > td {
  border: 0;
}
/deep/.ant-table-thead > tr > th {
  border: 0;
}
/deep/ .exportButtonClass {
  font-family: SourceHanSansSC;
  font-weight: 400;
  font-size: 15px;
  color: rgba(0,101,255,0.67);
}

/deep/ .ant-table-thead > tr > th {
  background: white;
  padding: 12px 8px;
  font-weight: bold;
  border: 1px solid black;
  border-bottom: 0;
}

/deep/ .ant-table-bordered .ant-table-tbody > tr > td {
  border: 1px solid black;
}

/deep/ .ant-table-bordered .ant-table-body > table {
  border-collapse: collapse;
  border-spacing: 0;
}

/deep/ .ant-table-bordered .ant-table-header > table {
  border-collapse: collapse;
  border-spacing: 0;
}

/deep/ .ant-table-bordered .ant-table-title > table {
  border-collapse: collapse;
  border-spacing: 0;
  border-bottom: 0;
}

/deep/ .ant-table-bordered .ant-table-title > table > tr > td {
  border: 1px solid black;
}

/deep/ .ant-table-bordered .ant-table-footer > table {
  border-collapse: collapse;
  border-spacing: 0px;
}

/deep/ .ant-table-bordered .ant-table-footer > table > tr > td {
  border: 1px solid black;
}

#develop {
  font-size: 12px;
  margin: 0px 70px 0px 70px;
  color: #000;
}

textarea.ant-input {
  max-width: 100%;
  height: auto;
  min-height: 32px;
  line-height: 1.5;
  vertical-align: bottom;
  -webkit-transition: all 0.3s, height 0s;
  transition: all 0.3s, height 0s;
  border: none;
}
</style>
