<template>
  <a-modal title="测试失效申请" :width="600" :visible="visible" :confirmLoading="confirmLoading" @ok="handleSubmit"
           @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
<!--        <div style="font-size: 20px;font-weight: bolder;">一、电芯基本信息</div>-->
<!--        <a-row :gutter="24">-->

<!--        </a-row>-->
        <div style="font-size: 20px;font-weight: bolder;">一、电芯信息</div>
        <a-row :gutter="24">
<!--          <a-col :md="20" :sm="24">-->
<!--            <a-form-item label="产品名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>-->
<!--              <a-select show-search style="width: 100%"-->
<!--                        v-decorator="['issueId', {rules: [{required: true, message: '请选择产品名称!'}]}]"-->
<!--                        option-filter-prop="children"-->
<!--                        :filter-option="filterOption"-->
<!--                        placeholder="请选择产品名称"-->
<!--                        @change="selectProduct"-->
<!--                        :disabled="disable">-->
<!--                <a-select-option v-for="(item,i) in this.productList" :key="i" :value="parseInt(item.issueId)">-->
<!--                  {{ item.productName }}-->
<!--                </a-select-option>-->
<!--              </a-select>-->
<!--            </a-form-item>-->
<!--          </a-col>-->

          <a-col :md="20" :sm="24">
            <a-form-item label="电芯编码" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-decorator="['cellCode', {rules: [{required: true, message: '请输入电芯编码!'}]}]" placeholder="请输入电芯编码获取电芯信息" @focus="focusCellCode" @blur="blurCellCode"/>
            </a-form-item>
          </a-col>

          <a-col :md="20" :sm="24">
            <a-form-item label="产品所属研究所" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
<!--              <a-input v-decorator="['productDepartment', {rules: [{required: true, message: '请选择产品所属研究所!'}]}]" placeholder="请选择产品所属研究所" />-->
              <a-select style="width: 100%"
                        v-decorator="['productDepartment', {rules: [{required: true, message: '请选择产品所属研究所!'}]}]"
                        placeholder="请选择产品所属研究所">
                <a-select-option v-for="(item,i) in productDepartmentList" :key="i" :value="item.id">{{ item.customvalue }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :md="20" :sm="24">
            <a-form-item label="样品类型" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
<!--              <a-input v-decorator="['orderType', {rules: [{required: true, message: '请选择样品类型!'}]}]" placeholder="请选择样品类型" />-->
              <a-select style="width: 100%"
                        v-decorator="['orderType', {rules: [{required: true, message: '请选择样品类型!'}]}]"
                        placeholder="请选择样品类型">
                <a-select-option v-for="(item,i) in orderTypeList" :key="i" :value="item.code">{{ item.value }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :md="20" :sm="24">
            <a-form-item label="产品名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-decorator="['productName', {rules: [{required: true, message: '请输入产品名称!'}]}]" placeholder="请输入产品名称" />
            </a-form-item>
          </a-col>

          <a-col :md="20" :sm="24">
            <a-form-item label="项目等级" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
<!--              <a-input v-decorator="['projectLevel', {rules: [{required: true, message: '请选择项目等级!'}]}]" placeholder="请选择项目等级" />-->
              <a-select style="width: 100%"
                        v-decorator="['projectLevel', {rules: [{required: true, message: '请选择项目等级!'}]}]"
                        placeholder="请选择项目等级">
                <a-select-option v-for="(item,i) in projectLevelList" :key="i" :value="item.code">{{ item.value }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :md="20" :sm="24">
            <a-form-item label="研制阶段" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
<!--              <a-input v-decorator="['researchStage', {rules: [{required: true, message: '请选择研制阶段!'}]}]" placeholder="请选择研制阶段" />-->
              <a-select style="width: 100%"
                        v-decorator="['researchStage', {rules: [{required: true, message: '请选择研制阶段!'}]}]"
                        placeholder="请选择研制阶段">
                <a-select-option v-for="(item,i) in researchStageList" :key="i" :value="item.code">{{ item.value }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :md="20" :sm="24">
            <a-form-item label="测试样品阶段" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-decorator="['testSampleStage', {rules: [{required: true, message: '请输入测试样品阶段!'}]}]" placeholder="请输入测试样品阶段" />
            </a-form-item>
          </a-col>
        </a-row>
        <div style="font-size: 20px;font-weight: bolder;">二、测试信息</div>
        <a-row :gutter="24">

          <a-col :md="20" :sm="24">
            <a-form-item label="委托单号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-decorator="['folderNo', {rules: [{required: true, message: '请输入委托单号!'}]}]" placeholder="请输入委托单号" />
            </a-form-item>
          </a-col>

          <a-col :md="20" :sm="24">
            <a-form-item label="测试项目名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-decorator="['testProjectName', {rules: [{required: true, message: '请输入测试项目名称!'}]}]" placeholder="请输入测试项目名称" />
            </a-form-item>
          </a-col>

          <a-col :md="20" :sm="24">
            <a-form-item label="电芯批次" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-decorator="['cellBatch', {rules: [{required: true, message: '请输入电芯批次!'}]}]" placeholder="请输入电芯批次" />
            </a-form-item>
          </a-col>

          <a-col :md="20" :sm="24">
            <a-form-item label="测试类别" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-select style="width: 100%"
                        v-decorator="['testCate', {rules: [{required: true, message: '请选择测试类别!'}]}]"
                        placeholder="请选择测试类别">
                <a-select-option v-for="(item,i) in testCateList" :key="i" :value="item.code">{{item.value}}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :md="20" :sm="24">
            <a-form-item label="失效类别" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-select style="width: 100%"
                        v-decorator="['failureCate', {rules: [{required: true, message: '请选择失效类别!'}]}]"
                        placeholder="请选择失效类别">
                <a-select-option v-for="(item,i) in failureCateList" :key="i" :value="item.code">{{ item.value }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :md="20" :sm="24">
            <a-form-item label="测试失效描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea :rows="2" placeholder="请输入测试失效描述"
                          v-decorator="['testFailureDescription', {rules: [{required: true, message: '请输入测试失效描述!'}]}]"></a-textarea>
            </a-form-item>
          </a-col>

          <a-col :md="20" :sm="24">
            <a-form-item label="DPV失效告知书" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-upload-dragger :file-list="fileList" :headers="headers" :action="postUrl" :multiple="false" name="file" @change="handleChange">
                <p class="ant-upload-drag-icon">
                  <a-icon type="inbox" />
                </p>
                <p class="ant-upload-text">
                  点击或拖动文档至此处上传
                </p>
              </a-upload-dragger>
            </a-form-item>
          </a-col>

        </a-row>

        <div style="font-size: 20px;font-weight: bolder;">三、流程审核</div>

        <a-row :gutter="24">

          <a-col :md="20" :sm="24">
            <a-form-item label="样品管理员" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input type='hidden'
                       v-decorator="['sampleManager', {rules: [{required: true, message: '请选择样品管理员！'}]}]"/>
              <a-dropdown v-model="sampleManagerShow" placement="bottomCenter" :trigger="['click']">
                <a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">
                  {{ sampleManagerName ? sampleManagerName : '选择样品管理员' }}
                  <a-icon type="down"/>
                </a-button>
                <a-menu slot="overlay">
                  <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                    <a-input-search v-model="sampleManagerQueryParam.searchValue" placeholder="搜索..." @change="sampleManagerSearch"/>
                    <s-table style="width:100%;" ref="sampleManagerTable" :rowKey="(record) => record.id" :columns="userColumns"
                             :data="sampleManagerLoadData" :customRow="sampleManagerRow" :scroll="{ y: 120,x:120}">
                    </s-table>
                  </a-spin>
                </a-menu>
              </a-dropdown>
            </a-form-item>
          </a-col>

          <a-col :md="20" :sm="24">
            <a-form-item label="实验室负责人" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input type='hidden'
                       v-decorator="['laboratoryResponsible', {rules: [{required: true, message: '请选择实验室负责人！'}]}]"/>
              <a-dropdown v-model="laboratoryResponsibleShow" placement="bottomCenter" :trigger="['click']">
                <a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">
                  {{ laboratoryResponsibleName ? laboratoryResponsibleName : '选择实验室负责人' }}
                  <a-icon type="down"/>
                </a-button>
                <a-menu slot="overlay">
                  <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                    <a-input-search v-model="laboratoryResponsibleQueryParam.searchValue" placeholder="搜索..." @change="laboratoryResponsibleSearch"/>
                    <s-table style="width:100%;" ref="laboratoryResponsibleTable" :rowKey="(record) => record.id" :columns="userColumns"
                             :data="laboratoryResponsibleLoadData" :customRow="laboratoryResponsibleRow" :scroll="{ y: 120,x:120}">
                    </s-table>
                  </a-spin>
                </a-menu>
              </a-dropdown>
            </a-form-item>
          </a-col>

          <a-col :md="20" :sm="24">
            <a-form-item label="部门经理" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input type='hidden'
                       v-decorator="['departmentManager', {rules: [{required: true, message: '请选择部门经理！'}]}]"/>
              <a-dropdown v-model="departmentManagerShow" placement="bottomCenter" :trigger="['click']">
                <a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">
                  {{ departmentManagerName ? departmentManagerName : '选择部门经理' }}
                  <a-icon type="down"/>
                </a-button>
                <a-menu slot="overlay">
                  <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                    <a-input-search v-model="departmentManagerQueryParam.searchValue" placeholder="搜索..." @change="departmentManagerSearch"/>
                    <s-table style="width:100%;" ref="departmentManagerTable" :rowKey="(record) => record.id" :columns="userColumns"
                             :data="departmentManagerLoadData" :customRow="departmentManagerRow" :scroll="{ y: 120,x:120}">
                    </s-table>
                  </a-spin>
                </a-menu>
              </a-dropdown>
            </a-form-item>
          </a-col>

          <a-col :md="20" :sm="24">
            <a-form-item label="产品经理" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input type='hidden'
                       v-decorator="['productManager', {rules: [{required: true, message: '请选产品经理！'}]}]"/>
              <a-dropdown v-model="productManagerShow" placement="bottomCenter" :trigger="['click']">
                <a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">
                  {{ productManagerName ? productManagerName : '选择产品经理' }}
                  <a-icon type="down"/>
                </a-button>
                <a-menu slot="overlay">
                  <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                    <a-input-search v-model="productManagerQueryParam.searchValue" placeholder="搜索..." @change="productManagerSearch"/>
                    <s-table style="width:100%;" ref="productManagerTable" :rowKey="(record) => record.id" :columns="userColumns"
                             :data="productManagerLoadData" :customRow="productManagerRow" :scroll="{ y: 120,x:120}">
                    </s-table>
                  </a-spin>
                </a-menu>
              </a-dropdown>
            </a-form-item>
          </a-col>

          <a-col :md="20" :sm="24">
            <a-form-item label="产品总监" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input type='hidden'
                       v-decorator="['productMajordomo', {rules: [{required: true, message: '请选择产品总监！'}]}]"/>
              <a-dropdown v-model="productMajordomoShow" placement="bottomCenter" :trigger="['click']">
                <a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">
                  {{ productMajordomoName ? productMajordomoName : '选择产品总监' }}
                  <a-icon type="down"/>
                </a-button>
                <a-menu slot="overlay">
                  <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                    <a-input-search v-model="productMajordomoQueryParam.searchValue" placeholder="搜索..." @change="productMajordomoSearch"/>
                    <s-table style="width:100%;" ref="productMajordomoTable" :rowKey="(record) => record.id" :columns="userColumns"
                             :data="productMajordomoLoadData" :customRow="productMajordomoRow" :scroll="{ y: 120,x:120}">
                    </s-table>
                  </a-spin>
                </a-menu>
              </a-dropdown>
            </a-form-item>
          </a-col>

          <a-col :md="20" :sm="24">
            <a-form-item label="DQE" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input type='hidden'
                       v-decorator="['DQE', {rules: [{required: true, message: '请选择DQE！'}]}]"/>
              <a-dropdown v-model="DQEShow" placement="bottomCenter" :trigger="['click']">
                <a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">
                  {{ DQEName ? DQEName : '选择DQE' }}
                  <a-icon type="down"/>
                </a-button>
                <a-menu slot="overlay">
                  <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                    <a-input-search v-model="DQEQueryParam.searchValue" placeholder="搜索..." @change="DQESearch"/>
                    <s-table style="width:100%;" ref="DQETable" :rowKey="(record) => record.id" :columns="userColumns"
                             :data="DQELoadData" :customRow="DQERow" :scroll="{ y: 120,x:120}">
                    </s-table>
                  </a-spin>
                </a-menu>
              </a-dropdown>
            </a-form-item>
          </a-col>

          <a-col :md="20" :sm="24">
            <a-form-item label="所长" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input type='hidden'
                       v-decorator="['headOfTheInstitute', {rules: [{required: true, message: '请选择所长！'}]}]"/>
              <a-dropdown v-model="headOfTheInstituteShow" placement="bottomCenter" :trigger="['click']">
                <a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">
                  {{ headOfTheInstituteName ? headOfTheInstituteName : '选择所长' }}
                  <a-icon type="down"/>
                </a-button>
                <a-menu slot="overlay">
                  <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                    <a-input-search v-model="headOfTheInstituteQueryParam.searchValue" placeholder="搜索..." @change="headOfTheInstituteSearch"/>
                    <s-table style="width:100%;" ref="headOfTheInstituteTable" :rowKey="(record) => record.id" :columns="userColumns"
                             :data="headOfTheInstituteLoadData" :customRow="headOfTheInstituteRow" :scroll="{ y: 120,x:120}">
                    </s-table>
                  </a-spin>
                </a-menu>
              </a-dropdown>
            </a-form-item>
          </a-col>

          <a-col :md="20" :sm="24">
            <a-form-item label="副院长" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input type='hidden'
                       v-decorator="['vicePresident', {rules: [{required: true, message: '请选择副院长！'}]}]"/>
              <a-dropdown v-model="vicePresidentShow" placement="bottomCenter" :trigger="['click']">
                <a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">
                  {{ vicePresidentName ? vicePresidentName : '选择副院长' }}
                  <a-icon type="down"/>
                </a-button>
                <a-menu slot="overlay">
                  <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                    <a-input-search v-model="vicePresidentQueryParam.searchValue" placeholder="搜索..." @change="vicePresidentSearch"/>
                    <s-table style="width:100%;" ref="vicePresidentTable" :rowKey="(record) => record.id" :columns="userColumns"
                             :data="vicePresidentLoadData" :customRow="vicePresidentRow" :scroll="{ y: 120,x:120}">
                    </s-table>
                  </a-spin>
                </a-menu>
              </a-dropdown>
            </a-form-item>
          </a-col>

          <a-col :md="20" :sm="24">
            <a-form-item label="院长" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input type='hidden'
                       v-decorator="['president', {rules: [{required: true, message: '请选择院长！'}]}]"/>
              <a-dropdown v-model="presidentShow" placement="bottomCenter" :trigger="['click']">
                <a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">
                  {{ presidentName ? presidentName : '选择院长' }}
                  <a-icon type="down"/>
                </a-button>
                <a-menu slot="overlay">
                  <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                    <a-input-search v-model="presidentQueryParam.searchValue" placeholder="搜索..." @change="presidentSearch"/>
                    <s-table style="width:100%;" ref="presidentTable" :rowKey="(record) => record.id" :columns="userColumns"
                             :data="presidentLoadData" :customRow="presidentRow" :scroll="{ y: 120,x:120}">
                    </s-table>
                  </a-spin>
                </a-menu>
              </a-dropdown>
            </a-form-item>
          </a-col>

<!--          <a-col :md="20" :sm="24">
            <a-form-item label="知悉人员" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input type='hidden'
                       v-decorator="['awarePerson', {rules: [{required: true, message: '请选择知悉人员！'}]}]"/>
              <a-dropdown v-model="awarePersonShow" placement="bottomCenter" :trigger="['click']">
                <a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">
                  {{ awarePersonName ? awarePersonName : '选择知悉人员' }}
                  <a-icon type="down"/>
                </a-button>
                <a-menu slot="overlay">
                  <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                    <a-input-search v-model="awarePersonQueryParam.searchValue" placeholder="搜索..." @change="awarePersonSearch"/>
                    <s-table style="width:100%;" ref="awarePersonTable" :rowKey="(record) => record.id" :columns="userColumns"
                             :data="awarePersonLoadData" :customRow="awarePersonRow" :scroll="{ y: 120,x:120}">
                    </s-table>
                  </a-spin>
                </a-menu>
              </a-dropdown>
            </a-form-item>
          </a-col>-->
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import Vue from 'vue'
import {ACCESS_TOKEN, DICT_TYPE_TREE_DATA} from '@/store/mutation-types'
import moment from 'moment'
import {getUserLists} from '@/api/modular/system/userManage'
import {STable} from '@/components'
import {getCellMsgByCellCode, saveTestFailureRecord} from "@/api/modular/system/testFailure";
// import {saveProductProblem} from "@/api/modular/system/productProblem";

export default {
  props: {
    productDepartmentList:{
      type: Array,
      default: []
    }
  },
  components: {
    STable
  },
  data() {
    return {
      disable: false,
      projectdetail: {},
      // findDate: '',
      plannedCompletionDate: '',
      loading: false,
      userList:[],
      userColumns: [{
        title: '账号',
        dataIndex: 'account'
      },
        {
          title: '姓名',
          dataIndex: 'name'
        },
      ],
      sampleManagerQueryParam: {},
      sampleManagerShow: false,
      sampleManagerName: '',
      sampleManagerLoadData: parameter => {
        return getUserLists(Object.assign(parameter, this.sampleManagerQueryParam)).then((res) => {
          return res.data
        })
      },

      laboratoryResponsibleQueryParam: {},
      laboratoryResponsibleShow: false,
      laboratoryResponsibleName: '',
      laboratoryResponsibleLoadData: parameter => {
        return getUserLists(Object.assign(parameter, this.laboratoryResponsibleQueryParam)).then((res) => {
          return res.data
        })
      },

      departmentManagerQueryParam: {},
      departmentManagerShow: false,
      departmentManagerName: '',
      departmentManagerLoadData: parameter => {
        return getUserLists(Object.assign(parameter, this.departmentManagerQueryParam)).then((res) => {
          return res.data
        })
      },

      productManagerQueryParam: {},
      productManagerShow: false,
      productManagerName: '',
      productManagerLoadData: parameter => {
        return getUserLists(Object.assign(parameter, this.productManagerQueryParam)).then((res) => {
          return res.data
        })
      },

      productMajordomoQueryParam: {},
      productMajordomoShow: false,
      productMajordomoName: '',
      productMajordomoLoadData: parameter => {
        return getUserLists(Object.assign(parameter, this.productMajordomoQueryParam)).then((res) => {
          return res.data
        })
      },

      DQEQueryParam: {},
      DQEShow: false,
      DQEName: '',
      DQELoadData: parameter => {
        return getUserLists(Object.assign(parameter, this.DQEQueryParam)).then((res) => {
          return res.data
        })
      },

      headOfTheInstituteQueryParam: {},
      headOfTheInstituteShow: false,
      headOfTheInstituteName: '',
      headOfTheInstituteLoadData: parameter => {
        return getUserLists(Object.assign(parameter, this.headOfTheInstituteQueryParam)).then((res) => {
          return res.data
        })
      },

      vicePresidentQueryParam: {},
      vicePresidentShow: false,
      vicePresidentName: '',
      vicePresidentLoadData: parameter => {
        return getUserLists(Object.assign(parameter, this.vicePresidentQueryParam)).then((res) => {
          return res.data
        })
      },

      presidentQueryParam: {},
      presidentShow: false,
      presidentName: '',
      presidentLoadData: parameter => {
        return getUserLists(Object.assign(parameter, this.presidentQueryParam)).then((res) => {
          return res.data
        })
      },


      awarePersonQueryParam: {},
      awarePersonShow: false,
      awarePersonName: '',
      awarePersonSelectedRow:[],
      awarePersonSelectedRowKeys:[],
      awarePersonLoadData: parameter => {
        return getUserLists(Object.assign(parameter, this.awarePersonQueryParam)).then((res) => {
          return res.data
        })
      },
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 8
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this),


      oldCellCode:'',
      cellData:{},
      // productDepartmentList:[{code:'动力圆柱电池研究所',value:'动力圆柱电池研究所'},{code:'方形电池研究所',value:'方形电池研究所'},{code:'新型电池研究所',value:'新型电池研究所'},{code:'V型圆柱电池研究所',value:'V型圆柱电池研究所'},{code:'动力电池研究所',value:'动力电池研究所'},{code:'储能电池研究所',value:'储能电池研究所'}],
      orderTypeList:[{code:'G圆柱',value:'G圆柱'},{code:'C圆柱',value:'C圆柱'},{code:'方型',value:'方型'},{code:'软包',value:'软包'},{code:'V圆柱',value:'V圆柱'}],
      projectLevelList:[{code:'S',value:'S'},{code:'A',value:'A'},{code:'B',value:'B'},{code:'C',value:'C'}],
      researchStageList:[{code:'A样',value:'A样'},{code:'B样',value:'B样'},{code:'C样',value:'C样'}],
      testCateList:[{code:1,value:"电性能"},{code:2,value:"寿命"},{code:3,value:"安全"},{code:4,value:"其它"}],
      failureCateList:[{code:1,value:"不满足指标"},{code:2,value:"起火"},{code:3,value:"漏液"},{code:4,value:"壳体开裂"},{code:5,value:"其它"}],
      testFailureRecord:{},
      postUrl: '/api/sysFileInfo/uploadfile',
      headers: {
        Authorization: 'Bearer ' + Vue.ls.get(ACCESS_TOKEN),
      },
      fileList: [],
    }
  },
  methods: {
    moment,
    getDict(code) {
      const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
      return dictTypeTree.filter(item => item.code == code)[0].children
    },
    // 初始化方法
    add() {
      // this.findDate = ''
      // this.plannedCompletionDate = ''
      this.visible = true
      getUserLists({}).then((res) => {
        this.userList = res.data;
      })
      // if (this.issueId != 0) {
      //   this.disable = true;
      //   setTimeout(() => {
      //     this.selectProduct(this.issueId)//选中产品
      //   }, 200);
      // }
      // console.log(this.productList);
    },
    handleChange(info) {
      let fileList = [...info.fileList];
      fileList = fileList.slice(-1);
      this.fileList = fileList;
      if (info.file.status !== 'uploading') {
        console.log(info.file, info.fileList);
      }
      if (info.file.status === 'done') {
        let res = info.file.response
        if (res.success) {
          this.$message.success(`${info.file.name} 文件上传成功`)
          this.testFailureRecord = { ...this.testFailureRecord,
            fileName: res.data.fileOriginName,
            fileId: res.data.id
          }
        } else {
          this.$message.error(res.message)
        }
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 文件上传失败`);
      }
    },
    //用户选择方法开始
    sampleManagerSearch(e) {
      this.$refs.sampleManagerTable.refresh()
    },
    sampleManagerRow(row, index) {
      return {
        on: {
          click: () => {
            this.form.setFieldsValue({
              sampleManager: row.account
            })
            this.sampleManagerName = row.name
            this.sampleManagerShow = false
          }
        }
      }
    },

    laboratoryResponsibleSearch(e) {
      this.$refs.laboratoryResponsibleTable.refresh()
    },
    laboratoryResponsibleRow(row, index) {
      return {
        on: {
          click: () => {
            this.form.setFieldsValue({
              laboratoryResponsible: row.account
            })
            this.laboratoryResponsibleName = row.name
            this.laboratoryResponsibleShow = false
          }
        }
      }
    },

    departmentManagerSearch(e) {
      this.$refs.departmentManagerTable.refresh()
    },
    departmentManagerRow(row, index) {
      return {
        on: {
          click: () => {
            this.form.setFieldsValue({
              departmentManager: row.account
            })
            this.departmentManagerName = row.name
            this.departmentManagerShow = false
            // this.queryParam.searchValue = '';
          }
        }
      }
    },

    productManagerSearch(e) {
      this.$refs.productManagerTable.refresh()
    },
    productManagerRow(row, index) {
      return {
        on: {
          click: () => {
            this.form.setFieldsValue({
              productManager: row.account
            })
            this.productManagerName = row.name
            this.productManagerShow = false
          }
        }
      }
    },

    productMajordomoSearch(e) {
      this.$refs.productMajordomoTable.refresh()
    },
    productMajordomoRow(row, index) {
      return {
        on: {
          click: () => {
            this.form.setFieldsValue({
              productMajordomo: row.account
            })
            this.productMajordomoName = row.name
            this.productMajordomoShow = false
          }
        }
      }
    },

    DQESearch(e) {
      this.$refs.DQETable.refresh()
    },
    DQERow(row, index) {
      return {
        on: {
          click: () => {
            this.form.setFieldsValue({
              DQE: row.account
            })
            this.DQEName = row.name
            this.DQEShow = false
          }
        }
      }
    },

    headOfTheInstituteSearch(e) {
      this.$refs.headOfTheInstituteTable.refresh()
    },
    headOfTheInstituteRow(row, index) {
      return {
        on: {
          click: () => {
            this.form.setFieldsValue({
              headOfTheInstitute: row.account
            })
            this.headOfTheInstituteName = row.name
            this.headOfTheInstituteShow = false
          }
        }
      }
    },

    vicePresidentSearch(e) {
      this.$refs.vicePresidentTable.refresh()
    },
    vicePresidentRow(row, index) {
      return {
        on: {
          click: () => {
            this.form.setFieldsValue({
              vicePresident: row.account
            })
            this.vicePresidentName = row.name
            this.vicePresidentShow = false
          }
        }
      }
    },

    presidentSearch(e) {
      this.$refs.presidentTable.refresh()
    },
    presidentRow(row, index) {
      return {
        on: {
          click: () => {
            this.form.setFieldsValue({
              president: row.account
            })
            this.presidentName = row.name
            this.presidentShow = false
          }
        }
      }
    },



    awarePersonSearch(e) {
      this.$refs.awarePersonTable.refresh()
    },
    awarePersonRow(row, index) {
      return {
        on: {
          click: () => {
            if (this.awarePersonSelectedRowKeys.includes(row.id)) {
              this.awarePersonSelectedRow = this.awarePersonSelectedRow.filter(r => r != row)
              this.awarePersonSelectedRowKeys = this.awarePersonSelectedRowKeys.filter(r => r != row.id)
            } else {
              this.awarePersonSelectedRow.push(row)
              this.awarePersonSelectedRowKeys.push(row.id)
            }

            let ids = ""
            this.awarePersonName = ""
            for (let i = 0; i < this.awarePersonSelectedRow.length; i++) {
              if (i == 0) {
                this.awarePersonName = this.awarePersonSelectedRow[i].name
                ids += this.awarePersonSelectedRow[i].account
              } else {
                this.awarePersonName += "," + this.awarePersonSelectedRow[i].name
                ids += "," + this.awarePersonSelectedRow[i].account
              }
            }

            this.form.setFieldsValue({
              awarePerson: ids
            })

            // this.form.setFieldsValue({
            //   awarePerson: row.account
            // })
            // this.awarePersonName = row.name
            // this.awarePersonShow = false
            // this.queryParam.searchValue = '';
          }
        }
      }
    },
    //用户选择方法结束
    //申请提交
    handleSubmit() {
      if(this.fileList == null || this.fileList.length == 0){
        this.$message.warn('请先上传文件')
        return
      }

      const {
        form: {
          validateFields
        }
      } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        if (!errors) {
          // if (this.findDate != '') {
          //   values.findDate = this.findDate
          // }
          // if (this.plannedCompletionDate != '') {
          //   values.plannedCompletionDate = this.plannedCompletionDate
          // }
          let $params = {
            ...values,
            fileName: this.testFailureRecord.fileName,
            fileId: this.testFailureRecord.fileId,
            productId: this.cellData.productId,
          };
          // console.log($params);
          saveTestFailureRecord($params).then((res) => {
            if (res.success) {
              // if (res.data) {
                this.$message.success('新增成功')
                this.visible = false
                this.confirmLoading = false
                this.$emit('ok')
                this.handleCancel()
              // } else {
              //   this.$message.error('新增失败')
              // }
            } else {
              this.$message.error('新增失败：' + res.message)
            }
          }).finally((res) => {
            this.confirmLoading = false
          })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleCancel() {
      this.awarePersonSelectedRow = [];
      this.awarePersonSelectedRowKeys = [];
      this.sampleManagerName = ''
      this.departmentManagerName = ''
      this.laboratoryResponsibleName = ''
      this.productManagerName = ''
      this.productMajordomoName = ''
      this.DQEName = ''
      this.headOfTheInstituteName = ''
      this.vicePresidentName = ''
      this.presidentName = ''
      this.awarePersonName = ''
      // this.findDate = ''
      // this.plannedCompletionDate = ''
      this.form.resetFields()
      this.visible = false
      // this.form.getFieldDecorator('findDate', {
      //   initialValue: null
      // })
      // this.form.getFieldDecorator('plannedCompletionDate', {
      //   initialValue: null
      // })
    },
    focusCellCode(value,value1,value2,value3){
      // console.log("focus", value);
      this.oldCellCode = value.target._value;
    },
    blurCellCode(value,value1,value2,value3){
      // console.log("this.oldCellCode", this.oldCellCode);
      // console.log("blurValue", value.target._value);
      //根据电芯编码查询信息，设置到页面上
      if (this.oldCellCode != value.target._value) {
        getCellMsgByCellCode({cellCode:value.target._value}).then(res=>{
          console.log("res", res);
          if (res.success) {
            if (res.data.cellCode == null) {
              this.$message.warn('不存在该编号的电芯！')
            } else {
              console.log("res.data", res.data);
              this.cellData = res.data;
              this.cellData.productName;
              if (this.form.getFieldValue("productDepartment") == null && this.productDepartmentList.find(e=>e.value==this.cellData.productDepartment)) {
                this.form.setFieldsValue({productDepartment: this.cellData.productDepartment});
              }
              if (this.form.getFieldValue("orderType") == null && this.orderTypeList.find(e=>e.value==this.cellData.orderType)) {
                this.form.setFieldsValue({orderType: this.cellData.orderType});
              }
              if (this.form.getFieldValue("productName") == null) {
                this.form.setFieldsValue({productName: this.cellData.productName});
              }
              if (this.form.getFieldValue("projectLevel") == null && this.projectLevelList.find(e=>e.value==this.cellData.projectLevel)) {
                this.form.setFieldsValue({projectLevel: this.cellData.projectLevel});
              }
              if (this.form.getFieldValue("researchStage") == null) {
                this.form.setFieldsValue({researchStage: this.cellData.researchStage});
              }
              if (this.form.getFieldValue("testSampleStage") == null) {
                this.form.setFieldsValue({testSampleStage: this.cellData.testSampleStage});
              }
              if (this.form.getFieldValue("folderNo") == null) {
                this.form.setFieldsValue({folderNo: this.cellData.folderNo});
              }
              if (this.form.getFieldValue("testProjectName") == null) {
                this.form.setFieldsValue({testProjectName: this.cellData.testProjectName});
              }
              if (this.form.getFieldValue("cellCode") == null) {
                this.form.setFieldsValue({cellCode: this.cellData.cellCode});
              }
              if (this.form.getFieldValue("cellBatch") == null) {
                this.form.setFieldsValue({cellBatch: this.cellData.cellBatch});
              }
              //用户
              if (this.form.getFieldValue("departmentManager") == null && this.cellData.departmentManager) {
                getUserLists({searchValue: this.cellData.departmentManager}).then((res) => {
                  if (res.data.rows.length > 0) {
                    this.departmentManagerName = res.data.rows[0].name;
                    this.form.setFieldsValue({departmentManager: this.cellData.departmentManager});
                  }
                });
              }
              if (this.form.getFieldValue("productManager") == null && this.cellData.productManager) {
                getUserLists({searchValue: this.cellData.productManager}).then((res) => {
                  if (res.data.rows.length > 0) {
                    this.productManagerName = res.data.rows[0].name;
                    this.form.setFieldsValue({productManager: this.cellData.productManager});
                  }
                });
              }
              if (this.form.getFieldValue("productMajordomo") == null && this.cellData.productMajordomo) {
                getUserLists({searchValue:this.cellData.productMajordomo}).then((res) => {
                  if (res.data.rows.length > 0) {
                    this.productMajordomoName = res.data.rows[0].name;
                    this.form.setFieldsValue({productMajordomo: this.cellData.productMajordomo});
                  }
                })
              }
              if (this.form.getFieldValue("DQE") == null && this.cellData.DQE) {
                getUserLists({searchValue:this.cellData.DQE}).then((res) => {
                  if (res.data.rows.length > 0) {
                    this.DQEName = res.data.rows[0].name;
                    this.form.setFieldsValue({DQE: this.cellData.DQE});
                  }
                })
              }
              if (this.form.getFieldValue("headOfTheInstitute") == null && this.cellData.headOfTheInstitute) {
                getUserLists({searchValue:this.cellData.headOfTheInstitute}).then((res) => {
                  if (res.data.rows.length > 0) {
                    this.headOfTheInstituteName = res.data.rows[0].name;
                    this.form.setFieldsValue({headOfTheInstitute: this.cellData.headOfTheInstitute});
                  }
                })
              }
              if (this.form.getFieldValue("vicePresident") == null) {
                getUserLists({searchValue: '032745'}).then((res) => {
                  if (res.data.rows.length > 0) {
                    this.vicePresidentName = res.data.rows[0].name;
                    this.form.setFieldsValue({vicePresident: '032745'});
                  }
                })
              }
              if (this.form.getFieldValue("president") == null) {
                getUserLists({searchValue: '029026'}).then((res) => {
                  if (res.data.rows.length > 0) {
                    this.presidentName = res.data.rows[0].name;
                    this.form.setFieldsValue({president: '029026'});
                  }
                })
              }
            }
          } else {
            this.$message.error('电芯信息获取失败：' + res.message)
          }
        })
      }
    },
    selectProduct(value, label, extra) {
      this.projectdetail = this.productList.filter(item => item.id == value)[0];

      this.form.setFieldsValue(
        {
          issueId: value,
          // productName: this.projectdetail.productName,
          productStateName: this.projectdetail.productStateName,
        }
      )
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
  }
}
</script>
