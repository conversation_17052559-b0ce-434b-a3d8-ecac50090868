<template>
  <div>
        <div class="topic_width">
            <a-breadcrumb class="breadcrumb" separator=">">
                <a-breadcrumb-item><a @click="goBack">课题看板</a></a-breadcrumb-item>
                <a-breadcrumb-item>课题等级</a-breadcrumb-item>
            </a-breadcrumb>
        </div>
        
        <x-card class="topic_width">
            <div slot="content" class="table-page-search-wrapper">
                <a-form layout="inline">
                    <a-row :gutter="48">
                        <a-col :md="8" :sm="24">
                            <a-form-item label="立项申请">
                               <a-range-picker
                                    size="small"
                                    :placeholder="['开始月份', '结束月份']"
                                    v-model="dates"
                                    :mode="['month', 'month']"
                                    format="YYYY-MM"
                                    @panelChange="handlePanelChange"
                                    @openChange="handleOpenChange"
                                    :open="monthPickShow"
                                >
                                    <a-icon slot="suffixIcon" type="calendar" style="color:#d9d9d9" />
                                </a-range-picker>
                            </a-form-item>
                        </a-col>
                        <a-col :md="3" :sm="24">
                            <a-form-item label="">
                                <treeselect :limit="1" @input="change" :max-height="200" placeholder="请选择部门" :value-consists-of="ALL" v-model="queryParam.deptId" :multiple="true" :options="depts" :normalizer="normalizer" />
                            </a-form-item>
                        </a-col>
                        
                        <a-col :md="1" :sm="24">
                            <span class="table-page-search-submitButtons">
                            <a-button size="small" style="margin-left: 8px" @click="resetquery">重置</a-button>
                        </span>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
        </x-card>
        <div class="topic_width head2" >技术课题等级</div>
        <a-table class="topic_width"  style="background: #fff" :pagination="false" :columns="columns" :dataSource="loadData" :loading="loading">
            <!-- <template slot="footer">
                合计： <span class="foot">总数:{{sum}}</span> <span class="foot">S级:{{sCount}}</span> <span class="foot">A级:{{aCount}}</span> <span class="foot">S级 I A级占比率:{{(avgCount*100).toFixed(1)+'%'}}</span>
            </template> -->
            <template slot="childDeptAvgCount" slot-scope="text,record">
                <span v-if="record.childDeptCount == 0">0</span>
                <span v-else>{{(record.childDeptKtCount/record.childDeptCount).toFixed(1)}}</span>
            </template>
            <div class="divcls" slot="divcls" slot-scope="text">{{text}}</div>
        </a-table>
    <a-modal
      title="明细"
      :width="width"
      v-model="visible"
      :zIndex="parseInt(2)"
      :bodyStyle="{overflow:'hidden',overflowY: 'scroll',maxHeight:clientHeight - 180 + 'px'}"
      @cancel="() => visible=false"
    >


      <template slot="footer">
        <a-button key="back" @click="() => visible=false">
          关闭
        </a-button>

      </template>

      <div>

        <a-table style="background:#fff" size="small"

                 :scroll="{x:bigClient?false:true}"

                 :rowKey="(record) => record.issueId" :columns="columnsIn" :dataSource="detailList" :loading="loading"
        >


          <div slot="topicName" slot-scope="text">
            <clamp :text="text" :sourceText="[text]" :isCenter="true"></clamp>
          </div>


          <div slot="projectLevel" slot-scope="text" style="width: 100%;text-align: center">
            {{levels[text]}}

          </div>

          <div slot="researchContent" slot-scope="text">
            <clamp :text="text" :sourceText="[text]" :isCenter="true"></clamp>
          </div>

          <div slot="projectBackGround" slot-scope="text">
            <clamp :text="text" :sourceText="[text]" :isCenter="true"></clamp>
          </div>


          <div slot="cate1">1级<br/>分类</div>
          <div slot="cate2">2级<br/>分类</div>
          <div slot="cate3">3级<br/>分类</div>
          <div slot="projectNameTitle">课题名称</div>
          <div slot="projectLevelTitle">课题等级</div>
          <div slot="projectLeaderTitle">课题负责人</div>
          <div slot="platformAndTopic">平台课题</div>


          <template slot="reviewResult1" slot-scope="text">
            <div style="text-align:center">
              <span class="green" v-if="text == 1">{{reviewRes[text]}}</span>
              <span class="red" v-else-if="text == 2">{{reviewRes[text]}}</span>
              <span class="yellow" v-else-if="text == 3">{{reviewRes[text]}}</span>

            </div>
          </template>

          <template slot="reviewOpinion1" slot-scope="text">
            <clamp :isCenter="true" :text="text" :sourceText="[text]"></clamp>
          </template>

          <template slot="reviewResult2" slot-scope="text">
            <div style="text-align:center">
              <span class="green" v-if="text == 1">{{reviewRes[text]}}</span>
              <span class="red" v-else-if="text == 2">{{reviewRes[text]}}</span>
              <span class="yellow" v-else-if="text == 3">{{reviewRes[text]}}</span>

            </div>
          </template>

          <template slot="reviewOpinion2" slot-scope="text">
            <clamp :text="text" :sourceText="[text]"></clamp>
          </template>





        </a-table>
      </div>

    </a-modal>
    </div>
</template>

<script>
import {XCard} from '@/components'
import moment from 'moment';
import {clamp} from '@/components'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import {getLevelProjects,getCateTree,getPlatformTopicsFromView} from "@/api/modular/system/topic"
export default {
    
    components: {
        XCard,
        Treeselect,
      clamp
    },
    data() {
        return {
            normalizer(node) {
				return {
					id: node.value,
					label: node.title,
					children: node.children && node.children.length > 0 ? node.children: 0,
				}
			},
            dates: [],
            monthPickShow: false,
            /* sum:0,
            sCount:0,
            aCount:0,
            avgCount:0, */
            depts:[],
            merges: ['parentDept'],
            windowHeight: document.documentElement.clientHeight,
            loading: true,
            columns: [
                {
                    title: '二级部门',
                    width: 120,
                    dataIndex: 'parentDept',
                    align:'center',
                    customRender: (text, row, index) => {
                        return {
                            children: text,
                            attrs: {
                                rowSpan: row['parentDept_rowSpan'] ? row['parentDept_rowSpan'] : 0
                            }
                        }
                    },
                },
                {
                    title: 'S级',
                    width: 80,
                    align:'center',
                    dataIndex: 'parentDeptSCount',
                    customRender: (text, row, index) => {
                      if (index == this.loadData.length -1) {
                        return text
                      }
                        return {
                          children: (<a onClick={() => this.openDetail(1,row.parentDept,null,2)}>{text}</a>),

                            attrs: {
                                rowSpan: row['parentDept_rowSpan'] ? row['parentDept_rowSpan'] : 0
                            }
                        }
                    },
                },
                {
                    title: 'A级',
                    width: 80,
                    align:'center',
                    dataIndex: 'parentDeptACount',
                    customRender: (text, row, index) => {
                      if (index == this.loadData.length -1) {
                        return text
                      }
                        return {

                          children: (<a onClick={() => this.openDetail(1,row.parentDept,null,3)}>{text}</a>),

                      attrs: {
                                rowSpan: row['parentDept_rowSpan'] ? row['parentDept_rowSpan'] : 0
                            }
                        }
                    },
                },
                {
                    title: 'B级',
                    width: 80,
                    align:'center',
                    dataIndex: 'parentDeptBCount',
                    customRender: (text, row, index) => {
                      if (index == this.loadData.length -1) {
                        return text
                      }
                        return {

                          children: (<a onClick={() => this.openDetail(1,row.parentDept,null,4)}>{text}</a>),

                      attrs: {
                                rowSpan: row['parentDept_rowSpan'] ? row['parentDept_rowSpan'] : 0
                            }
                        }
                    },
                },
                
                {
                    title: 'S级 I A级占比率',
                    width: 80,
                    align:'center',
                    dataIndex: 'parentDeptAvgCount',
                    customRender: (text, row, index) => {
                        return {
                            children: (((row['parentDeptSCount']+row['parentDeptACount'])/(row['parentDeptSCount']+row['parentDeptACount']+row['parentDeptBCount']))*100).toFixed(1)+'%',
                            attrs: {
                                rowSpan: row['parentDept_rowSpan'] ? row['parentDept_rowSpan'] : 0
                            }
                        }
                    },
                },
                {
                    title: '三级部门',
                    width: 120,
                    align:'center',
                    dataIndex: 'childDept',
                    scopedSlots: { customRender: 'divcls' },
                },
                {
                    title: 'S级',
                    width: 80,
                    align:'center',
                    dataIndex: 'childDeptSCount',
                    customRender: (text, row, index) => {
                      if (index == this.loadData.length -1) {
                        return ''
                      }
                      return {
                        children: (<a onClick={() => this.openDetail(1,null,row.childDept,2)}>{text}</a>),
                      }
                    },
                },
                {
                    title: 'A级',
                    width: 80,
                    align:'center',
                    dataIndex: 'childDeptACount',
                  customRender: (text, row, index) => {
                    if (index == this.loadData.length -1) {
                        return ''
                      }
                    return {
                      children: (<a onClick={() => this.openDetail(1,null,row.childDept,3)}>{text}</a>),
                  }
                  },
                },
                {
                    title: 'B级',
                    width: 80,
                    align:'center',
                    dataIndex: 'childDeptBCount',
                  customRender: (text, row, index) => {
                    if (index == this.loadData.length -1) {
                        return ''
                      }
                    return {
                      children: (<a onClick={() => this.openDetail(1,null,row.childDept,4)}>{text}</a>),
                  }
                  },
                },
                {
                    title: 'S级 I A级占比率',
                    width: 80,
                    align:'center',
                    dataIndex: 'childDeptAvgCount',
                    customRender: (text, row, index) => {
                        if (index == this.loadData.length -1) {
                          return ''
                        }
                        return {
                            children: (((row['childDeptSCount']+row['childDeptACount'])/(row['childDeptSCount']+row['childDeptACount']+row['childDeptBCount']))*100).toFixed(1)+'%',
                        }
                    },
                },
            ],
            loadData: [],
            queryParam: {},
          columnsIn: [

            {
              title: '序号',
              dataIndex: 'index',
              align: 'center',
              width: 40,
              customRender: (text, record, index) => ( <div
              class = 'divcls div_border_right div_btns' > {index+1} </div>)
            },
            {
              title: '平台一级分类',
              dataIndex: 'affiliatedPlatform1',
              align: 'center',
              width: 80,
            },
            {
              title: '平台二级分类',
              dataIndex: 'affiliatedPlatform2',
              align: 'center',
              width: 80,
            },
            {
              dataIndex: 'cateName',
              width: 80,
              align: 'center',
              title: '课题分类',
            },
            {
              dataIndex: 'topicName',
              width: 120,
              title: '课题名称',
              align:'center',
              scopedSlots: {customRender: 'topicName'},
            },

            {
              title: '研究内容',
              dataIndex: 'researchContent',
              width: 120,
              align:'center',
              scopedSlots: {customRender: 'researchContent'},
            },
            {
              title: '课题背景',
              dataIndex: 'projectBackground',
              width: 120,
              align:'center',
              scopedSlots: {customRender: 'projectBackGround'},
            },

            {
              slots: {title: 'projectLevelTitle'},
              dataIndex: 'projectLevel',
              scopedSlots: {customRender: 'projectLevel'},
              width: 50,
              align:'center',

            },
            {

              dataIndex: 'projectLeader',
              slots: {title: 'projectLeaderTitle'},
              width: 80,
              align:'center',
              scopedSlots: {customRender: 'projectLeader'},
            },
            {
              title: '部门',
              dataIndex: 'deptName',
              width: 100,
              align:'center',
            },

            {
              title: '评审结果',
              dataIndex: 'approvalReviewResult',
              width: 80,
              align: 'center',
              scopedSlots: {customRender: 'reviewResult1'},
            },
            {
              title: '当前节点',
              dataIndex: 'issuestatusName',
              width: 80,
              align: 'center'
            },{
              title: '立项评审日期',
              dataIndex: 'reviewDate',
              width: 80,
              align: 'center',
              customRender: (text, record, index) => null == text ?'':moment(new Date(text)).format('YYYY-MM-DD')
            },

          ],
          reviewRes: ['/', '通过', '不通过', '再确认'],
          levels: ['', '', 'S', 'A','B'],
          detailList:[],
          visible: false,
          width: document.documentElement.clientWidth * 0.8,
          height: document.documentElement.clientHeight * 0.3,
          bigClient: document.documentElement.clientHeight > 700,
          height1: document.documentElement.clientHeight * 0.3 + 100,
          clientHeight: document.documentElement.clientHeight,
        }
    },
    methods:{
        getByClass(parent, cls) {
			if (parent.getElementsByClassName) {
				return Array.from(parent.getElementsByClassName(cls));
			} else {
				var res = [];
				var reg = new RegExp(' ' + cls + ' ', 'i')
				var ele = parent.getElementsByTagName('*');
				for (var i = 0; i < ele.length; i++) {
					if (reg.test(' ' + ele[i].className + ' ')) {
						res.push(ele[i]);
					}
				}
				return res;
			}
		},
        init(){
            this.$nextTick(() => {
                let items = this.getByClass(document, 'divcls')
                for (const e of items) {
                    var _e = e.parentNode 
                    _e.classList.add('tdcls')
                }
            })
        },
        goBack(){
            this.$router.push({
                path: "/topic_chart",
            })
        },
        moment,
        resetquery(){
            this.queryParam = {}
            this.dates = [moment().subtract(1, 'year').startOf('year'),moment()],

            this.queryParam.startDate = moment(this.dates[0]._d).format('YYYY-MM')
            this.queryParam.endDate = moment(this.dates[1]._d).format('YYYY-MM')
            this.callGetLevelProjects()
        },
        change(value, label, extra){
            this.callGetLevelProjects()
        },
        handlePanelChange(value, mode) {
            if (this.dates[1] && this.dates[1]._d != value[1]._d) {
                this.dates = value
                this.monthPickShow = false;
                this.queryParam.startDate = moment(this.dates[0]._d).format('YYYY-MM')
                this.queryParam.endDate = moment(this.dates[1]._d).format('YYYY-MM')
                this.callGetLevelProjects()
            }
            this.dates = value
        },
        handleOpenChange(status) {
            if(status){
                this.monthPickShow = true;
            }else{
                this.monthPickShow = false
            }
        },
        callGetLevelProjects() {
            this.loading = true
            getLevelProjects(this.queryParam).then((res) => {
            if (res.success) {
                
                //let sum = 0
                let sCount = 0
                let aCount = 0
                let bCount = 0
                let depts = []
                for (const item of res.data.list) {
                    
                    if(depts.indexOf(item.parentDept) > -1){
                        continue
                    }

                    //sum += (item.parentDeptSCount+item.parentDeptACount+item.parentDeptBCount)
                    sCount += item.parentDeptSCount
                    aCount += item.parentDeptACount
                    bCount += item.parentDeptBCount
                    depts.push(item.parentDept)
                }
               /*  this.sum = sum
                this.sCount = sCount
                this.aCount = aCount

                this.avgCount = ((sCount+aCount)/sum) */

                res.data.list.push({
                  parentDept:'合计',
                  parentDeptSCount:sCount,
                  parentDeptACount:aCount,
                  parentDeptBCount:bCount
                })

                this.merges.forEach((item) => {
                    for (let i = 0, j = res.data.list.length; i < j; i++) {
                        let rowSpan = 0;
                        let n = i;
                        while (
                            res.data.list[n + 1] &&
                            res.data.list[n + 1][item] == res.data.list[n][item]
                        ) {
                            rowSpan++;
                            n++;
                            res.data.list[n].rowSpan = 0;
                        }
                        if (rowSpan) res.data.list[i][item + "_rowSpan"] = rowSpan + 1;
                        if (!rowSpan) res.data.list[i][item + "_rowSpan"] = 1;
                        i += rowSpan;
                    }
                });
                
                this.loadData = res.data.list
                this.init()
            } else {
                this.$message.error('错误提示：' + res.message,1)
            }
            this.loading = false
            })
            .catch((err) => {
                this.$message.error('错误提示：' + err.message,1)
                this.loading = false
            });
        },
      openDetail(pass,parentDeptName,childDeptName,projectLevel){
        getPlatformTopicsFromView({reviewDateBegin:this.queryParam.startDate,childDeptName:childDeptName,
          reviewDateEnd:this.queryParam.endDate,pass:pass,parentDeptName:parentDeptName
        ,projectLevel:projectLevel}).then((res) => {
          this.detailList = res.data
          this.visible = true
        })
      },
        callGetDeptTree(){
            getCateTree({
                fieldName:'department',
                flag:1
            }).then((res)=>{
                if (res.success) {
                    this.depts = res.data
                } else {
                    this.$message.error('错误提示：' + res.message, 1)
                }
            }).catch((err) => {
                this.$message.error('错误提示：' + err.message, 1)
            });
        },

        /* callGetTree(){
            getCateTree({
                fieldName:'projectCate'
            }).then((res)=>{
                if (res.success) {
                    this.cate = res.data
                } else {
                    this.$message.error('错误提示：' + res.message, 1)
                }
            }).catch((err) => {
                this.$message.error('错误提示：' + err.message, 1)
            });
        }, */
    },
    created(){
        this.queryParam.startDate = this.$route.query.startDate
        this.queryParam.endDate = this.$route.query.endDate
        this.dates =[moment(this.$route.query.startDate, 'YYYY-MM'),moment(this.$route.query.endDate, 'YYYY-MM')]
        this.callGetDeptTree()
        //this.queryParam.status = parseInt(this.$route.query.status)
        this.callGetLevelProjects()
    }
}
</script>



<style lang="less" scoped=''>
@import './topic.less';
.red {
  display: inline-block;
  padding: 2px 8px;
  background: #ff3333;
  text-align: center;
  color: #fff;
}

.yellow {
  display: inline-block;
  padding: 2px 8px;
  background: #fac858;
  text-align: center;
  color: #fff;
}

.green {
  display: inline-block;
  padding: 2px 8px;
  background: #58a55c;
  text-align: center;
  color: #fff;
}

a {
  color: black;
}
span.foot{
    margin-right: 12px;
}
/deep/.topic_width .ant-table-tbody > tr:last-child{
    background: #fafafa;
    font-size: 14px;
}
</style>