<template>
  <div>
    <a-modal title="提交技术文档" :width="500" :visible="visible1" :confirmLoading="confirmLoading" @ok="handleSubmit" @cancel="handleCancel">
      <a-spin :spinning="confirmLoading">
        <a-form :form="form">
          <a-form-item label="文件" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-upload-dragger :file-list="fileList" :headers="headers" :action="postUrl" :multiple="false" name="file" @change="handleChange">
              <p class="ant-upload-drag-icon">
                <a-icon type="inbox" />
              </p>
              <p class="ant-upload-text">
                点击或拖动pdf文档至此处上传
              </p>
            </a-upload-dragger>
          </a-form-item>
          <a-form-item label="原因" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-textarea :rows="4" placeholder="请输入原因" v-decorator="['remark',{rules: [{required: true, message: '请输入原因！'}]}]"></a-textarea>
          </a-form-item>
          <a-form-item label="内容" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-textarea :rows="4" placeholder="请输入内容" v-decorator="['content', {rules: [{required: true, message: '请输入内容！'}]}]"></a-textarea>
          </a-form-item>
          <a-form-item v-if="record.techType == 4" label="审批人" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input :rows="4" placeholder="请输入审批人" v-decorator="['reviewer', {rules: [{required: true, message: '请输入审批人！'}]}]"></a-input>
          </a-form-item>
          <a-form-item label="批准人" v-if="record.techType == 4"  :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input :rows="4" placeholder="请输入批准人" v-decorator="['approver', {rules: [{required: true, message: '请输入批准人！'}]}]"></a-input>
          </a-form-item>
        </a-form>
      </a-spin>
    </a-modal>



    <a-modal title="导入技术文档" :width="500" :visible="importDocsVisible" :confirmLoading="confirmLoading" @ok="handleSubmitImport" @cancel="handleCancel">
      <a-spin :spinning="confirmLoading">
        <a-form :form="importForm">
          <!--        <a-form-item label="文件" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>-->
          <!--          <a-upload-dragger :file-list="fileList" :headers="headers" :action="postUrl" :multiple="false" name="file" @change="handleChange">-->
          <!--            <p class="ant-upload-drag-icon">-->
          <!--              <a-icon type="inbox" />-->
          <!--            </p>-->
          <!--            <p class="ant-upload-text">-->
          <!--              点击或拖动pdf文档至此处上传-->
          <!--            </p>-->
          <!--          </a-upload-dragger>-->
          <!--        </a-form-item>-->
          <!--        <a-form-item label="原因" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>-->
          <!--          <a-textarea :rows="4" placeholder="请输入原因" v-decorator="['remark',{rules: [{required: true, message: '请输入原因！'}]}]"></a-textarea>-->
          <!--        </a-form-item>-->
          <!--        <a-form-item label="内容" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>-->
          <!--          <a-textarea :rows="4" placeholder="请输入内容" v-decorator="['content', {rules: [{required: true, message: '请输入内容！'}]}]"></a-textarea>-->
          <!--        </a-form-item>-->

          <a-form-item  label="审批人" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input :rows="4" placeholder="请输入审批人" v-decorator="['reviewer', {rules: [{required: true, message: '请输入审批人！'}]}]"></a-input>
          </a-form-item>
          <a-form-item label="批准人"  :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input :rows="4" placeholder="请输入批准人" v-decorator="['approver', {rules: [{required: true, message: '请输入批准人！'}]}]"></a-input>
          </a-form-item>
        </a-form>
      </a-spin>
    </a-modal>
  </div>

</template>

<script>
import {
  docCommit, docImport
} from "@/api/modular/system/docManage"
    import Vue from 'vue'
    import {
        ACCESS_TOKEN
    } from '@/store/mutation-types'
    import {
        DICT_TYPE_TREE_DATA
    } from '@/store/mutation-types'
    import moment from 'moment'
    export default {
        data() {
            return {
                postUrl: '/api/sysFileInfo/uploadfile',
                headers: {
                    Authorization: 'Bearer ' + Vue.ls.get(ACCESS_TOKEN),
                },
                form: this.$form.createForm(this),
                visible1: false,
                importForm: this.$form.createForm(this),
                importDocsVisible : false,
                confirmLoading: false,
                fileList: [],
                record: {},
                wrapperCol: {},
                labelCol: {},
            }
        },
        methods: {
            moment,
            handleChange(info) {
                let fileList = [...info.fileList];
                fileList = fileList.slice(-1);
                this.fileList = fileList;
                if (info.file.status !== 'uploading') {
                    console.log(info.file, info.fileList);
                }
                if (info.file.status === 'done') {
                    let res = info.file.response
                    if (res.success) {
                        this.$message.success(`${info.file.name} 文件上传成功`)
                        this.record = { ...this.record,
                            techName: res.data.fileOriginName,
                            techDocFileId: res.data.id
                        }
                    } else {
                        this.$message.error(res.message)
                    }
                } else if (info.file.status === 'error') {
                    this.$message.error(`${info.file.name} 文件上传失败`);
                }
            },
            add(record) {
                this.visible1 = true
              console.log("点击提交" + this.visible1);
                this.record = {
                    bomId: record.bomId,
                    techType: record.techType
                }
            },
            docsImport(record) {
              this.importDocsVisible = true
              this.record = {
                bomId: record.bomId,
                techType: record.techType,
              }
            },
            getDict(code) {
                const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
                return dictTypeTree.filter(item => item.code == code)[0].children
            },
            handleCancel() {
                this.form.resetFields()
                this.importForm.resetFields()
                this.confirmLoading = false
                this.importDocsVisible = false
                this.visible1 = false
                this.fileList = []
                this.form.getFieldDecorator('time', {
                    initialValue: null
                })
            },
            handleSubmit() {
                const {
                    form: {
                        validateFields
                    }
                } = this
                this.confirmLoading = true
                validateFields((errors, values) => {
                    if (!errors) {
                        let $params = { ...values,
                            ...this.record
                        }
                        docCommit($params).then((res) => {
                            if (res.success) {
                                this.$message.success('提交成功')
                                this.handleCancel()
                                this.$emit('ok')
                            } else {
                                this.$message.error('提交失败：' + res.message)
                            }
                            this.confirmLoading = false
                        }).finally((res) => {
                            this.confirmLoading = false
                        })
                    } else {
                        this.confirmLoading = false
                    }
                })
            },
          handleSubmitImport() {
            const {
              importForm: {
                validateFields
              }
            } = this
            this.confirmLoading = true
            validateFields((errors, values) => {
              if (!errors) {
                let $params = { ...values,
                  ...this.record
                }
                console.log(values);
                console.log(this.record);
                console.log($params);
                console.log(this.importForm);
                //替换后台接口，改为导入
                docImport($params).then((res) => {
                  if (res.success) {
                    this.$message.success('提交成功')
                    this.handleCancel()
                    this.$emit('ok')
                  } else {
                    this.$message.error('提交失败：' + res.message)
                  }
                  this.confirmLoading = false
                }).finally((res) => {
                  this.confirmLoading = false
                })
              } else {
                this.confirmLoading = false
              }
            })
          },
        }
    }
</script>

<style>

</style>