import Vue from "vue"
import moment from "moment"
import { ACCESS_TOKEN } from "@/store/mutation-types"

import { downloadfile1, downloadMinioFile } from "@/utils/util"
import { formatDate } from "@/utils/format"

import Decimal from 'decimal.js'





import {
  getTestProgress,
  finishCalLifeTodoTask,
  updateTestProDetail,
  importModel,
  exportModel,
  aliasCopy,
  getInBoxPositionList,
  updateInBoxPosition,
  updateXsh, updatePicOrVid, handleSaveCalendarTestData
} from "@/api/modular/system/testProgressManager"
import { tLimsTestdataScheduleList } from "@/api/modular/system/limsManager"
import {
  calendarTechNextStep,
  earlyWarningCalendarCheck,
  earlyWarningTechConfirmCalendar
} from "@/api/modular/system/testAbnormalManager"

import {
    getCanInBoxStoreyOnlyBatteryType,testWarmGetCanInWarmBoxLuo,getTestWarmBoxLuoInOutDetail
} from '@/api/modular/system/warmBoxManager'

import stepData from "../../../lims/folder/stepData.vue"
import { getMinioDownloadUrl, getMinioPreviewUrl, sysFileInfoDownload } from "@/api/modular/system/fileManage";
import axios from "axios";


export const mixin = {

    components: {
        stepData
    },
    props: {
        modalData: {
            type: Object,
            default: () => ({})
        },

        serialObj: {
            type: Object,
            default: () => ({})
        },

    },
    data() {
        return{
            abnormalList:[],
            abnormalStatus:'create',
            isEngineerConfirmNotChange:0,
            isAbnormal:false,
            uploadProgress:0,
            uploadProgressShow:false,
            uploadingProgressList: [],
            progress:{},
            isFrist :true,
            originalData: {},//原始数据
            transferBoxInfo: [],
            initTransferBoxInfo: [],
            searchCellCode: "",
            loadBoxData: parameter => {
                this.inBoxPositionParam.id = this.originalData.id
                return getCanInBoxStoreyOnlyBatteryType(Object.assign(parameter, this.inBoxPositionParam)).then((res) => {
                    return res.data
                })
            },

            //首次中检阶段
            firstData: [],
            firstColums: [],
            firstList: [],
            positionResultData: [],
            canInLuoData: [],
            allPositionResultData: [],
            positionSelectedRowKeys: [],
            cellSelectedRowKeys: [],
            positionSelectedRows: [],
            cellSelectedRows: [],
            current: 0,
            mgVisible: false,
            modalLoading: false,
            videoLoading: false,
            attachLoading: false,
            attachmentList: [],
            currentSafetyTest: null,
            currentTestData: null,
            iframeUrl: '',
            previewImage: '',
            previewVisible: false,
            filePreviewVisible: false,
            pictureOrVideo: false,
            picOrVidColumn: [],
            picOrVidList: [],
            isShowDays: false,
            isShowActTransferDates: false,
            actTransferDate: null,
            isShowPositions: false,
            isShowPositionsSafe: false,
            selectLuoVisible: false,
            selectLuoVisibleSafe: false,
            xshSelectSampleVisible: false,
            xshSelectIndex: null,
            xshRecordList: null,
            inBoxPositionParam: {},
            luoQueryparam: {},
            labelCol: {
                xs: {
                    span: 10
                },
                sm: {
                    span: 10
                }
            },
            wrapperCol: {
                xs: {
                    span: 14
                },
                sm: {
                    span: 14
                }
            },
            startDay: '',
            middleCheck: "",
            actualInDate: "",
            actualOutDate: "",
            // 选中的中检的flowId
            flowId: "",
            // 最近的一条进箱时间
            lastActualInDate: "",
            lastActualOutDate: "",
            tableData: {},
            outQueryFlowRecord: {},

            mgData: [],
            xshData: [],
            xshOptions: [
                {label:'G26-未焊接12EA',value:'G26-未焊接12EA'},
                {label:'G圆柱-焊接4EA',value:'G圆柱-焊接4EA'},
                {label:'G圆柱-支架5EA',value:'G圆柱-支架5EA'},
                {label:'G圆柱-未焊接10EA',value:'G圆柱-未焊接10EA'},
                {label:'C圆柱-6EA',value:'C圆柱-6EA'},
                {label:'V圆柱-18650',value:'V圆柱-18650'},
                {label:'V圆柱-21700',value:'V圆柱-21700'},
                {label:'396389-24EA',value:'396389-24EA'},
                {label:'HP01',value:'HP01'}
            ],
            xshSelectedRowKeys: [],
            columns: [],
            selectedRowKeys: [],
            picOrVidMenu: {
              squareTop: "顶部",
              squareBottom: "底部",
              squareLarge1: "大面1",
              squareLarge2: "大面2",
              squareSide1: "侧面1",
              squareSide2: "侧面2",
              squareAssemblyTop: "工装装配顶部",
              squareAssemblyFront: "工装装配正面",
              squareAssemblyWhole: "工装装配整体",
              softLarge1: "大面1",
              softLarge2: "大面2",
              softAssemblyTop: "工装装配顶部",
              softAssemblyFront: "工装装配正面",
              softAssemblyWhole: "工装装配整体",
              mzTop: "顶部",
              mzBottom: "底部",
              mzLarge1: "大面1",
              mzLarge2: "大面2",
              mzSide1: "侧面1",
              mzSide2: "侧面2",
              cylTerminalTop: "端子面",
              cylVentBottom: "vent面",
              cylLarge1: "大面1",
              cylLarge2: "大面2",
              cylAssemblyTop: "工装装配顶部",
              cylAssemblyFront: "工装装配正面",
              cylAssemblyWhole: "工装装配整体",
            },
            tableNameMenu: {
                alias: "测试项目别名",
                cellTestCode: "测试编码",
                middleCheck: "中检类型",
                batteryStatus: "电芯状态",
                beforeVoltage: "电压/mV",
                beforeInnerres: "内阻/mΩ",
                afterVoltage: "中检后电压/mV",
                afterInnerres: "中检后内阻/mΩ",
                volume: "产气量/g",
                weight: "重量/g",
                isolateres: "绝缘阻值/mΩ",
                gTopPointDiameter: "上部直径/mm",
                gMiddlePointDiameter: "中部直径/mm",
                gBottomPointDiameter: "下部直径/mm",
                gTotalHeight: "端高/mm",
                gShoulderHeight: "肩高/mm",
                gCoverInnerRing: "盖板内圈φ/mm",
                gCoverOuterRing: "盖板外圈φ/mm",
                cTopPointDiameter: "上部直径/mm",
                cBottomPointDiameter: "下部直径/mm",
                cTotalHeight: "总高/mm",
                cShoulderHeight: "肩高/mm",
                sThickTopLeft: "上左厚度/mm",
                sThickTopMiddle: "上中厚度/mm",
                sThickTopRight: "上右厚度/mm",
                sThickMiddleLeft: "中左厚度/mm",
                sThickMiddle: "中心厚度/mm",
                sThickMiddleRight: "中右厚度/mm",
                sThickBottomLeft: "下左厚度/mm",
                sThickBottomMiddle: "下中厚度/mm",
                sThickBottomRight: "下右厚度/mm",
                sTopPointLength: "上部长度/mm",
                sMiddlePointLength: "中部长度/mm",
                sBottomPointLength: "下部长度/mm",
                sLeftPointHeight: "左部高度/mm",
                sMiddlePointHeight: "中部高度/mm",
                sRightPointHeight: "右部高度/mm",
                vTopPoint: "顶点直径/mm",
                vOneThird: "1/3处直径/mm",
                vOneSecond: "1/2处直径/mm",
                vSecondThird: "2/3处直径/mm",
                vBottom: "尾部直径/mm",
                vTotalHeight: "总高/mm",
                vShoulderHeight: "肩高/mm",
                vBottomHump: "底部凸起/mm",
                oThicknessOne: "厚度1/mm",
                oThicknessTwo: "厚度2/mm",
                oThicknessThree: "厚度3/mm",
                oThicknessFour: "厚度4/mm",
                oThicknessFive: "厚度5/mm"
            },
            stageColumns: [
            {
              title: '出箱测试编码',
              dataIndex: 'cellTestCode'
            },
            {
                title: '电芯状态',
                dataIndex: 'batteryStatus',
                customRender: (text, record, index) => {
                  switch (text) {
                    case "ongoing":
                      return "进行中"
                    case "earlyEnd":
                      return "状态正常-提前结束"
                    case "batteryDisassembly":
                      return "状态正常-电池拆解"
                    case "pressureDrop":
                      return "掉压失效-终止测试"
                    case "abnormalHot":
                      return "异常发热-终止测试"
                    case "openShellAndLeak":
                      return "开壳漏液-终止测试"
                    case "shellRust":
                      return "壳体生锈-终止测试"
                    case "operationError":
                      return "作业错误-终止测试"
                    case "thermalRunaway":
                      return "热失控-终止测试"
                    case "acrException":
                      return "内阻异常-终止测试"
                    case "swelling":
                      return "鼓包形变-终止测试"
                  }
                }
            }
            ],
            inBoxPositionColumns: [
                {
                    title: '序号',
                    dataIndex: 'index',
                    align: 'center',
                    width: 30,
                    ellipsis: true,
                    customRender: (text, record, index) => `${index + 1}`
                },
                {
                    title: '温箱编号',
                    dataIndex: 'code',
                    align: 'center',
                    width: 40,
                },
                {
                    title: '温箱样品架层号',
                    dataIndex: 'storeyCode',
                    align: 'center',
                    width: 50,
                },
                {
                    title: '温箱类型',
                    dataIndex: 'type',
                    align: 'center',
                    width: 40,
                }, {
                    title: '温度',
                    dataIndex: 'tem',
                    align: 'center',
                    width: 40,
                },
                {
                    title: '温箱型号',
                    dataIndex: 'model',
                    align: 'center',
                    width: 40,
                },
                {
                    title: '温箱容积/L',
                    dataIndex: 'volume',
                    align: 'center',
                    width: 40,
                },
                {
                    title: '总存储天数',
                    dataIndex: 'period',
                    align: 'center',
                    width: 40,
                    customRender: (text, record, index) => {
                        return record.periodSymbol + text
                    }
                },
            ],
            luoXshRecord: {},
            visible3:false,
            luoInOutDetail: [
            {
              title: '规格',
              align: 'center',
              dataIndex: 'luoType',
              width: 100,
              customRender: (value, row, index) => {
                const obj = {
                  children: value,
                  attrs: {},
                };
                if (row.isFirst) {
                  obj.attrs.rowSpan = row.rowSpan;
                } else {
                  obj.attrs.rowSpan = 0;
                }
                return obj;
              }
            },{
              title: '存储阶段',
              align: 'center',
              dataIndex: 'progressDetailOrderNumber',
              width: 50,
              customRender: (text, record, index) => `${text -1 }`
            },{
              title: '进箱时间',
              align: 'center',
              dataIndex: 'inDate',
              width: 80,
            },{
              title: '出箱时间',
              align: 'center',
              dataIndex: 'outDate',
              width: 80,
            },{
              title: '存放天数',
              align: 'center',
              dataIndex: 'day',
              width: 50,
            },{
              title: '产品名称',
              align: 'center',
              dataIndex: 'productName',
              width: 50,
            },{
              title: '样品编号',
              align: 'center',
              dataIndex: 'sampleCodeList',
              width: 100,
              scopedSlots: {
                customRender: "sampleCodeList"
              }
            },{
              title: '是否预留',
              align: 'center',
              dataIndex: 'isNeedReserve',
              width: 50,
              customRender: (text, record, index) => {
                if(text == '1'){
                  return '是'
                }else{
                  return '否'
                }
              }
            }


          ],
            detailQueryparam:{},
            batteryInOutData:[],
            canInLuoColumns: [
                {
                    title: '序号',
                    dataIndex: 'index',
                    align: 'center',
                    width: 30,
                    ellipsis: true,
                    customRender: (text, record, index) => `${index + 1}`
                },
                {
                    title: '摞类型',
                    dataIndex: 'luoType',
                    align: 'center',
                    width: 40,
                },  {
                    title: '摞顺序',
                    dataIndex: 'luoIndex',
                    align: 'center',
                    width: 40,
                }, {
                    title: '是否上次位置',
                    dataIndex: 'isReserve',
                    align: 'center',
                    width: 40,
                    customRender: (text, record, index) => {
                      if(1 == text){
                        return '是'
                      }
                      return '否'
                    }
                },   {
                    title: '已放吸塑盒数量',
                    dataIndex: 'inXshTpFxRbNum',
                    align: 'center',
                    width: 40,
                    scopedSlots: {
                      customRender: "inXshTpFxRbNum"
                    }
                },{
                    title: '可放吸塑盒数量',
                    dataIndex: 'canInNum',
                    align: 'center',
                    width: 40,
                },  {
                    title: '温箱编号',
                    dataIndex: 'code',
                    align: 'center',
                    width: 40,
                },
                {
                    title: '温箱样品架层号',
                    dataIndex: 'storeyCode',
                    align: 'center',
                    width: 50,
                },
                {
                    title: '温箱类型',
                    dataIndex: 'type',
                    align: 'center',
                    width: 40,
                }, {
                    title: '温度',
                    dataIndex: 'tem',
                    align: 'center',
                    width: 40,
                },
                {
                    title: '温箱型号',
                    dataIndex: 'model',
                    align: 'center',
                    width: 40,
                },
                {
                    title: '温箱容积/L',
                    dataIndex: 'volume',
                    align: 'center',
                    width: 40,
                },
                {
                    title: '总存储天数',
                    dataIndex: 'periodSymbolDesc',
                    align: 'center',
                    width: 40
                },
            ],
            safeCanInLuoColumns: [
            {
              title: '序号',
              dataIndex: 'index',
              align: 'center',
              width: 30,
              ellipsis: true,
              customRender: (text, record, index) => `${index + 1}`
            },
            {
              title: '已放电芯数量',
              dataIndex: 'luoInNum',
              align: 'center',
              width: 40,
              scopedSlots: {
                customRender: "inXshTpFxRbNum"
              }
            },  {
              title: '温箱编号',
              dataIndex: 'code',
              align: 'center',
              width: 40,
            },
            {
              title: '温箱样品架层号',
              dataIndex: 'storeyCode',
              align: 'center',
              width: 50,
            },
            {
              title: '温箱类型',
              dataIndex: 'type',
              align: 'center',
              width: 40,
            }, {
              title: '温度',
              dataIndex: 'tem',
              align: 'center',
              width: 40,
            }, {
              title: '湿度',
              dataIndex: 'humidity',
              align: 'center',
              width: 40,
            },
            {
              title: '温箱型号',
              dataIndex: 'model',
              align: 'center',
              width: 40,
            },
            {
              title: '温箱容积/L',
              dataIndex: 'volume',
              align: 'center',
              width: 40,
            }
          ],
          jmCanInLuoColumns: [
            {
              title: '序号',
              dataIndex: 'index',
              align: 'center',
              width: 30,
              ellipsis: true,
              customRender: (text, record, index) => `${index + 1}`
            },
            {
              title: '已放电芯数量',
              dataIndex: 'luoInNum',
              align: 'center',
              width: 40,
              scopedSlots: {
                customRender: "inXshTpFxRbNum"
              }
            },  {
              title: '可放总数',
              dataIndex: 'totalPcsNum',
              align: 'center',
              width: 40
            },  {
              title: '温箱编号',
              dataIndex: 'code',
              align: 'center',
              width: 40,
            },
            {
              title: '温箱样品架层号',
              dataIndex: 'storeyCode',
              align: 'center',
              width: 50,
            },
            {
              title: '温箱类型',
              dataIndex: 'type',
              align: 'center',
              width: 40,
            }, {
              title: '温度',
              dataIndex: 'tem',
              align: 'center',
              width: 40,
            },
            {
              title: '温箱型号',
              dataIndex: 'model',
              align: 'center',
              width: 60,
            },
            {
              title: '温箱容积/L',
              dataIndex: 'volume',
              align: 'center',
              width: 40,
            }
          ],
            xshColumns: [
                {
                  title: '序号',
                  dataIndex: 'index',
                  align: 'center',
                  width: 50,
                },
                {
                    title: '类型',
                    dataIndex: 'type',
                    align: 'center',
                    width: 150,
                    scopedSlots: {
                      customRender: "type"
                    }
                },
                {
                    title: '电芯',
                    dataIndex: 'code',
                    align: 'center',
                    width: 300,
                    colSpan: 2,
                    scopedSlots: {
                      customRender: "code"
                    }
                },
                {
                    title: '选择电芯',
                    dataIndex: 'action',
                    align: 'center',
                    width: 70,
                    colSpan: 0,
                    scopedSlots: {
                      customRender: "action"
                    }
                },
                {
                    title: '摞',
                    dataIndex: 'luoName',
                    align: 'center',
                    width: 100,
                    colSpan: 2,
                },
                {
                    title: '选择摞',
                    dataIndex: 'action2',
                    align: 'center',
                    width: 70,
                    colSpan: 0,
                    scopedSlots: {
                      customRender: "action2"
                    }
                }
            ],
            safeXshColumns: [
                {
                  title: '序号',
                  dataIndex: 'index',
                  align: 'center',
                  width: 50,
                },
                {
                    title: '电芯',
                    dataIndex: 'code',
                    align: 'center',
                    width: 300,
                    colSpan: 2,
                    scopedSlots: {
                      customRender: "code"
                    }
                },
                {
                    title: '选择电芯',
                    dataIndex: 'action',
                    align: 'center',
                    width: 70,
                    colSpan: 0,
                    scopedSlots: {
                      customRender: "action"
                    }
                },
                {
                    title: '温箱',
                    dataIndex: 'luoName',
                    align: 'center',
                    width: 100,
                    colSpan: 2,
                },
                {
                    title: '选择温箱',
                    dataIndex: 'action2',
                    align: 'center',
                    width: 70,
                    colSpan: 0,
                    scopedSlots: {
                      customRender: "action2"
                    }
                }
            ],
            stageInBoxColumns: [
              {
                title: "序号",
                dataIndex: "index",
                align: "center",
                customRender: (text, record, index) => `${index + 1}`
              },
              {
                title: "测试编码",
                dataIndex: "cellTestCode",
                align: "center",
                scopedSlots: {
                  customRender: "cellTestCode"
                }
              },
              {
                title: "初始温箱",
                dataIndex: "originInBoxPosition",
                align: "center",
                scopedSlots: {
                  customRender: "originInBoxPosition"
                }
              },
              {
                title: "转移后温箱",
                dataIndex: "inBoxPosition",
                align: "center",
                scopedSlots: {
                  customRender: "inBoxPosition"
                }
              },
              {
                title: "实际转移时间",
                dataIndex: "actTransferBatteryTime",
                align: "center",
                scopedSlots: {
                  customRender: "actTransferBatteryTime"
                }
              },
              {
                title: "转移时间",
                dataIndex: "transferBatteryTime",
                align: "center",
                scopedSlots: {
                  customRender: "transferBatteryTime"
                }
              }
            ],
            inBoxColumns: [
                {
                    title: "序号",
                    dataIndex: "index",
                    align: "center",
                    customRender: (text, record, index) => `${index + 1}`
                },
                {
                    title: "测试编码",
                    dataIndex: "cellTestCode",
                    align: "center",
                    scopedSlots: {
                        customRender: "cellTestCode"
                    }
                },
                {
                  title: "电芯状态",
                  dataIndex: "batteryStatus",
                  align: "center",
                  customRender: (text, record, index) => {
                    switch (text) {
                      case "ongoing":
                        return "进行中"
                      case "earlyEnd":
                        return "状态正常-提前结束"
                      case "batteryDisassembly":
                        return "状态正常-电池拆解"
                      case "pressureDrop":
                        return "掉压失效-终止测试"
                      case "abnormalHot":
                        return "异常发热-终止测试"
                      case "openShellAndLeak":
                        return "开壳漏液-终止测试"
                      case "shellRust":
                        return "壳体生锈-终止测试"
                      case "operationError":
                        return "作业错误-终止测试"
                      case "thermalRunaway":
                        return "热失控-终止测试"
                      case "acrException":
                        return "内阻异常-终止测试"
                    }
                  }
                },
                {
                    title: "进箱位置",
                    dataIndex: "inBoxPosition",
                    align: "center",
                    scopedSlots: {
                        customRender: "inBoxPosition"
                    }
                },
            ],
            selectSampleColumns: [
                {
                    title: "序号",
                    dataIndex: "index",
                    align: "center",
                    customRender: (text, record, index) => `${index + 1}`
                },
                {
                    title: "测试编码",
                    dataIndex: "cellTestCode",
                    align: "center",
                    scopedSlots: {
                        customRender: "cellTestCode"
                    }
                }
            ],
            allColumns: [
                {
                    title: "序号",
                    dataIndex: "index",
                    align: "center",
                    customRender: (text, record, index) => `${index + 1}`
                },
                {
                    title: "测试编码",
                    dataIndex: "cellTestCode",
                    align: "center",
                    scopedSlots: {
                        customRender: "cellTestCode"
                    }
                },
                {
                    title: "中检类型",
                    dataIndex: "middleCheck",
                    align: "center",
                    scopedSlots: {
                        customRender: "middleCheck"
                    }
                }
                // {
                // 	title: "计划开始时间",
                // 	dataIndex: "inDate",
                // 	align: "center",
                // 	scopedSlots: {
                // 		customRender: "inDate"
                // 	}
                // },
                // {
                // 	title: "计划结束时间",
                // 	dataIndex: "outDate",
                // 	align: "center",
                // 	scopedSlots: {
                // 		customRender: "outDate"
                // 	}
                // },
                // {
                // 	title: "存储天数",
                // 	dataIndex: "day",
                // 	align: "center",
                // 	scopedSlots: {
                // 		customRender: "day"
                // 	}
                // },
                // {
                // 	title: "存储阶段",
                // 	dataIndex: "orderNumber",
                // 	align: "center",
                // 	scopedSlots: {
                // 		customRender: "orderNumber"
                // 	}
                // }
            ],
            mgColumns: [
                {
                    title: "序号",
                    align: "center",
                    width: 50,
                    customRender: (text, record, index) => {
                        if (!record.isChild) {
                            return index + 1
                        }
                    }
                },
                {
                    title: "委托单号",
                    dataIndex: "folderno",
                    align: "center",
                    width: 90
                },
                {
                    title: "主题",
                    dataIndex: "theme",
                    align: "center",
                    ellipsis: true,
                    width: 90
                },
                {
                    title: "样品编号",
                    width: 90,
                    align: "center",
                    dataIndex: "orderno"
                },
                {
                    title: "测试项目编码",
                    width: 90,
                    align: "center",
                    dataIndex: "testcode"
                },
                {
                    title: "测试项名称",
                    width: 90,
                    align: "center",
                    dataIndex: "testname"
                },
                {
                    title: "测试项目别名",
                    width: 90,
                    align: "center",
                    dataIndex: "alias"
                },
                {
                    title: "测试编码",
                    width: 90,
                    align: "center",
                    dataIndex: "celltestcode",
                    scopedSlots: { customRender: "celltestcode" }
                },
                {
                    title: "数据位置",
                    width: 60,
                    align: "center",
                    dataIndex: "dataPath",
                    ellipsis: true
                },
                {
                    title: "存储天数",
                    width: 60,
                    align: "center",
                    dataIndex: "day"
                },{
                    title: "温度",
                    width: 40,
                    align: "center",
                    dataIndex: "tem"
                },{
                    title: "SOC",
                    width: 60,
                    align: "center",
                    dataIndex: "soc"
                },
                {
                    title: "开始时间",
                    width: 90,
                    align: "center",
                    dataIndex: "startTime",
                    customRender: (text, record, index) => {
                        if (null != text) {
                            return moment(text).format("YYYY-MM-DD")
                        }
                        return text
                    }
                },
                {
                    title: "结束时间",
                    width: 90,
                    align: "center",
                    dataIndex: "endTime",
                    customRender: (text, record, index) => {
                        if (null != text) {
                            return moment(text).format("YYYY-MM-DD")
                        }
                        return text
                    }
                },
                {
                    title: "设备编号",
                    width: 60,
                    align: "center",
                    dataIndex: "equiptcode"
                },

                {
                    title: "通道编号",
                    width: 60,
                    align: "center",
                    dataIndex: "channelno"
                }
            ],

            //中检阶段
            middleColums: [],
            middleData: [],
            middleList: [],

            // 数据填写阶段
            dataColums: [],
            dataInfo: [],
            dataList: [],

            // 最后一个阶段
            lastColums: [],
            lastData: [],
            lastList: [],

            mgSelectedRowKeys: [],
            selectionRows: [],
            mgSelectionRows: [],

            // 文件上传
            fileNames: "",
            fileList: [],
            picOrVidPostUrl: "/api/sysFileInfo/minioUpload",
            uploadData: { bucket: 'safetylab' },
            postUrl: "/limsUpload/open/basemodule/sys/files/upload",
            headers: {
                Authorization: "Bearer " + Vue.ls.get(ACCESS_TOKEN)
            }
        }
    },
    methods: {

      techConfirmCalendar(){
        earlyWarningTechConfirmCalendar({id:this.modalData.id}).then(res => {
          this.abnormalList = res.data.abnormalList
          this.isAbnormal = res.data.isAbnormal
          this.abnormalStatus = res.data.abnormalStatus
          this.isEngineerConfirmNotChange = res.data.isEngineerConfirmNotChange
          if (res.success) {
            this.$message.success("提交成功")
          } else {
            this.$message.error("提交异常")
          }
        })
      },
      handleUpload(options) {
        const { file, onSuccess, onError } = options;
        const formData = new FormData();
        formData.append('file', file);
        formData.append('bucket','safetylab');

        axios.post('/api/sysFileInfo/minioUpload', formData, {
          headers: {
            // 'Content-Type': 'multipart/form-data;',
            Authorization: 'Bearer ' + Vue.ls.get('Access-Token'),
          },
          // data:this.uploadData,
          onUploadProgress: (progressEvent) => {
            this.videoLoading = true
            this.uploadProgressShow = true
            if (progressEvent.total > 0) {
              this.uploadProgress = Math.round((progressEvent.loaded / progressEvent.total ) * 100)  == 100?99:Math.round((progressEvent.loaded / progressEvent.total ) * 100);
            }
          },
        })
          .then((response) => {
            this.uploadProgressShow = true
            onSuccess(response.data, file);
            this.uploadProgress = 100;
            setTimeout(() => {
              this.videoLoading = false
              this.uploadProgress = 0;
              this.uploadProgressShow = false
            }, 2000)
            // 重置进度条
          })
          .catch((error) => {
            onError(error);
            this.uploadProgress = 0; // 重置进度条
          });
      },
      handleUploadOfAttach(options, singleSafetyTest, index) {
        const { file, onSuccess, onError } = options;
        const formData = new FormData();
        formData.append('file', file);
        formData.append('bucket','safetylab');

        axios.post('/api/sysFileInfo/minioUpload', formData, {
          headers: {
            // 'Content-Type': 'multipart/form-data;',
            Authorization: 'Bearer ' + Vue.ls.get('Access-Token'),
          },
          // data:this.uploadData,
          onUploadProgress: (progressEvent) => {
            this.attachLoading = true
            this.uploadingProgressList[index].uploadStatusOfAttach = 'uploading'
            if (progressEvent.total > 0) {
              this.uploadingProgressList[index].percentOfAttach = Math.round((progressEvent.loaded / progressEvent.total ) * 100)  == 100?99:Math.round((progressEvent.loaded / progressEvent.total ) * 100);
            }
          },
        })
          .then((response) => {
            onSuccess(response.data, file);
            this.uploadingProgressList[index].percentOfAttach = 100;
            setTimeout(() => {
              this.attachLoading = false
              this.uploadingProgressList[index].percentOfAttach = 0;
              this.uploadingStage = null
              this.uploadingProgressList[index].uploadStatusOfAttach = 'done'
            }, 2000)
            // 重置进度条
          })
          .catch((error) => {
            onError(error);
            this.uploadingProgressList[index].percentOfAttach = 0; // 重置进度条
          });
      },
      // 电压内阻测试仪
      // 串口连接
      async handleOpenConnect() {
        this.$emit('openPort')
      },
      // 串口断开
      async handleCloseConnect() {
        this.$emit('closePort')
      },
      cancel(){},

      // 回车写入数据事件
      async handleWrite(dataIndex,index,focusId){
        if(!this.serialObj.serialPort || !this.serialObj.serialReader || !this.serialObj.serialWriter || !this.serialObj.serialPortOpen)  return

        // 如果焦点在的位置是内阻、电压
        if(dataIndex == 'beforeInnerres' || dataIndex == 'beforeVoltage' || dataIndex == 'afterInnerres' || dataIndex == 'afterVoltage'){
          const serialValue = await this.writeToSerial()

          if(dataIndex == 'beforeInnerres' || dataIndex == 'beforeVoltage'){
            this.firstData[index].beforeInnerres = Number(serialValue.split(',')[0] * 1000)//由于之前工作的excel模板上的单位为欧，所以测试设备输出的数据，自动除于1000，故此处需要还原  1mΩ === 0.001Ω
            this.firstData[index].beforeVoltage = Number(serialValue.split(',')[1] * 1000)
            this.firstData[index].timeOfFillInnerres = formatDate(new Date(), true)
          }else{
            this.firstData[index].afterInnerres = Number(serialValue.split(',')[0] * 1000)  //由于之前工作的excel模板上的单位为欧，所以测试设备输出的数据，自动除于1000，故此处需要还原  1mΩ === 0.001Ω
            this.firstData[index].afterVoltage = Number(serialValue.split(',')[1] * 1000)
            this.firstData[index].timeOfFillInnerres2 = formatDate(new Date(), true)
          }
          const params = {
            id: this.modalData.ordTaskId,
            lifeTestRecordDataMap: JSON.parse(JSON.stringify(this.firstData))
          }
          this.updateTestProDetail(params)
        }

        if(index !== this.firstData.length - 1){
          document.getElementById(focusId).focus();
        }
      },

      async writeToSerial(){
        if(!this.serialObj.serialPort || !this.serialObj.serialReader || !this.serialObj.serialWriter || !this.serialObj.serialPortOpen)  return

        const targerArr = this.isFrist ? [':INITiate:CONTinuous ON',':TRIGger:SOURce IMM',':FETCh?'] : [':FETCh?']
        // 写入设备
        await this._writeToSerial(targerArr)
        if(targerArr.includes(':INITiate:CONTinuous ON')) this.isFrist = false

        // 读取设备数值
        const serialValue =  await this._readerToSerial()


        return serialValue

      },
      _writeToSerial(arr){
        arr.forEach(async v => {
          await this.serialObj.serialWriter.write(new TextEncoder().encode(v +'\n'));
        })
      },
      async _readerToSerial(){
        try {
          let result = []
          while (true) {
            const { value, done } = await this.serialObj.serialReader.read()

            if (done) {
              this.serialObj.serialReader.releaseLock();
              this.serialObj.serialWriter.releaseLock();
              break;
            }
            if(value){
              result += this.Uint8ArrayToString(value)
            }
            // 换行符表示读取数据结束
            if(Array.from(value).includes(10)){
              break
            }
          }
          return result
        } catch (error) {
          console.error(error);
        }
      },

      Uint8ArrayToString(fileData){
        var dataString = "";
        for (var i = 0; i < fileData.length; i++) {
          dataString += String.fromCharCode(fileData[i]);
        }
        return dataString
      },

        // 获取最后一个进箱时间/最后一个出箱时间
        getTestProgress() {
            getTestProgress({ id: this.modalData.testProgressId }).then(res => {
                this.progress = res.data

                if (!res.data.data) return
                res.data.data.forEach(v => {
                    // 最后一个进箱时间
                    if (this.originalData.orderNumber - 1 === v.orderNumber) {
                        this.lastActualInDate = v.actualOutDate
                    }
                    // 最后一个出箱时间
                    if (this.originalData.orderNumber === v.orderNumber) {
                        this.lastActualOutDate = v.actualInDate
                    }
                })
            })
        },
        // 置灰
        disabledDate(current) {
            return current && current < moment(this.lastActualInDate)
        },
        disabledOutDate(current) {
            return current && current < moment(this.lastActualOutDate)
        },
        calendarTechNextStep(){
          calendarTechNextStep({id:this.modalData.id})
        },

        updateTestProDetail(params) {

            let oldCurrentStep =  this.modalData.currentStep
            updateTestProDetail(params).then(res => {
                if (!res.success) return this.$message.error("错误提示：" + res.message)
            }).finally(() => {
              earlyWarningCalendarCheck({id : this.modalData.id}).then(res =>  {
                this.abnormalList = res.data.abnormalList
                this.isAbnormal = res.data.isAbnormal
                this.abnormalStatus = res.data.abnormalStatus
                this.isEngineerConfirmNotChange = res.data.isEngineerConfirmNotChange
              })

              if(this.modalData.currentStep != null && this.modalData.currentStep < this.current){
                this.calendarTechNextStep()
              }

            })
        },
        openStepData(record) {
            this.outQueryFlowRecord = record

            if (record.flowId != null) {
                this.outQueryFlowRecord.flowId = record.flowId
                this.$refs.stepData.query(this.outQueryFlowRecord, false)
            } else {
                this.$message.warn("测试数据为空")
                return
            }
        },

        openSelectSampleData(record){
            this.xshSelectSampleVisible = true
            this.xshSelectIndex = record.index

            if(record.code){
              this.cellSelectedRowKeys = record.code
            }

        },
        openSelectLuo(record){
            if(record.type == null){
              this.$message.warn("请先选择类型")
              return
            }
            this.positionSelectedRowKeys = []
            this.positionSelectedRows = []
            this.luoQueryparam.id = this.originalData.id
            this.luoQueryparam.luoType = record.type
            testWarmGetCanInWarmBoxLuo(this.luoQueryparam).then(res => {
              this.canInLuoData = res.data

              let selectedLuo = this.canInLuoData.find(c => c.id == record.luoId)

              if(null != selectedLuo){
                this.positionSelectedRowKeys.push(record.luoId)
                this.positionSelectedRows.push(selectedLuo)
              }
            })

            this.xshRecordList = []
            this.xshRecordList.push(record)

            this.selectLuoVisible = true
        },
        openSelectLuoSafe(record){
            this.positionSelectedRowKeys = []
            this.positionSelectedRows = []
            this.luoQueryparam.id = this.originalData.id
            this.luoQueryparam.luoType = record.type
            testWarmGetCanInWarmBoxLuo(this.luoQueryparam).then(res => {
              this.canInLuoData = res.data

              let selectedLuo = this.canInLuoData.find(c => c.id == record.luoId)

              if(null != selectedLuo){
                this.positionSelectedRowKeys.push(record.luoId)
                this.positionSelectedRows.push(selectedLuo)
              }
            })

            this.xshRecordList = []
            this.xshRecordList.push(record)

            this.selectLuoVisibleSafe = true
        },
        openListSelectLuo(){
            if(this.xshSelectedRowKeys.length < 1){
              this.$message.warn("请先选择再操作")
              return
            }
            let selectRows = this.xshData.filter(x => this.xshSelectedRowKeys.includes(x.index))

            for (let i = 0; i < selectRows.length; i++) {
                if(selectRows[i].code == null || selectRows[i].code.length < 1){
                  this.$message.warn("请先选择电芯再操作")
                  return
                }
            }

            if(selectRows[0].type == null){
              this.$message.warn("请先选择类型")
              return
            }

            this.positionSelectedRowKeys = []
            this.positionSelectedRows = []
            this.luoQueryparam.id = this.originalData.id
            this.luoQueryparam.luoType = selectRows[0].type
            testWarmGetCanInWarmBoxLuo(this.luoQueryparam).then(res => {
              this.canInLuoData = res.data
            })

            this.xshRecordList = []
            this.xshRecordList = selectRows

            this.selectLuoVisible = true
        },

        refreshCanInLuoData(){

            testWarmGetCanInWarmBoxLuo(this.luoQueryparam).then(res => {
              this.canInLuoData = res.data
            })
        },




        handleInput(value, id, row) {
            // value:填写的值，id：对应的id，row：那一行数据发生修改

            if (value !== "" && document.getElementById(id).style.backgroundColor === "rgb(255, 233, 237)") {
                document.getElementById(id).style.backgroundColor = "transparent"
            }
            if (id.indexOf('before') !== -1 && value) {
                // this.dataInfo[row].timeOfFillInnerres = formatDate(new Date(), true)
                this.firstData[row].timeOfFillInnerres = formatDate(new Date(), true)
            }
            if (id.indexOf('after') !== -1 && value) {
                // this.dataInfo[row].timeOfFillInnerres2 = formatDate(new Date(), true)
                this.firstData[row].timeOfFillInnerres2 = formatDate(new Date(), true)
            }
            let property = Object.getOwnPropertyNames(this.firstData[row])
            let testDataColumn = ['beforeVoltage','afterVoltage','beforeInnerres','afterInnerres']
            if (property.findIndex(item => testDataColumn.indexOf(item) > -1) === -1) { //如果没有电压内阻和中检后电压内阻（即只有尺寸重量）
              if (property.findIndex(item => item === 'weight') === -1) { // 如果测试内容只有尺寸，timeOfFillInnerres取填写尺寸的时间
                if (value) {
                  this.firstData[row].timeOfFillInnerres = formatDate(new Date(), true)
                }
              } else { // 如果测试内容为①只有重量和尺寸②只有重量，timeOfFillInnerres取填写重量的时间
                if (id.indexOf('weight') !== -1 && value) {
                  this.firstData[row].timeOfFillInnerres = formatDate(new Date(), true)
                }
              }
            }
            const params = {
                id: this.modalData.ordTaskId,
                lifeTestRecordDataMap: JSON.parse(JSON.stringify(this.firstData))
            }
            this.updateTestProDetail(params)

        },

        /**
         * 修改电芯状态
         */
        changeBatteryStatus() {
          const params = {
            id: this.modalData.ordTaskId,
            lifeTestRecordDataMap: JSON.parse(JSON.stringify(this.firstData))
          }
          this.updateTestProDetail(params)
        },

        /**
         * 中检弹窗
         * @param {*} record
         */
        // 选中数据
        selectTestData(record) {
            // 测试数据
            // this.originalData.lifeTestRecordDataMap[
            //     this.originalData.lifeTestRecordDataMap.findIndex(v => v.cellTestCode === "3-202308310010-0003")
            // ].checkData = JSON.stringify(record)
            // this.originalData.lifeTestRecordDataMap[
            //     this.originalData.lifeTestRecordDataMap.findIndex(v => v.cellTestCode === "3-202308310010-0003")
            // ].isMiddleClick = true

            //正式情况
            this.originalData.lifeTestRecordDataMap[
                this.originalData.lifeTestRecordDataMap.findIndex(v => v.cellTestCode === record.celltestcode)
            ].checkData = JSON.stringify(record)
            this.originalData.lifeTestRecordDataMap[
                this.originalData.lifeTestRecordDataMap.findIndex(v => v.cellTestCode === record.celltestcode)
            ].isMiddleClick = true

            const params = {
                id: this.modalData.ordTaskId,
                lifeTestRecordDataMap: JSON.parse(JSON.stringify(this.originalData.lifeTestRecordDataMap))
            }
            this.updateTestProDetail(params)
        },

        onXshSelectChange(keys,rows){
          this.xshSelectedRowKeys = keys
        },

        addXsh(){


          let xsh = {
            index:this.xshData.length +1,
          }

          if(this.progress.productName.includes('G26')){
            xsh.type='G26-未焊接12EA'
          }else
          if(this.progress.sampleType == 'C圆柱'){
            xsh.type='C圆柱-6EA'
          }/*else
          if(this.progress.testAddress == 'R2_2F' && this.progress.sampleType == '软包'){
            xsh.type='396389-24EA'
          }*/
          let fullXsh = this.xshData.find(x => x.type != null)
          if(fullXsh){
            xsh.type = fullXsh.type
          }

          this.xshData.push(xsh)
        },
        addXshSafe(){


          let xsh = {
            index:this.xshData.length +1,
          }
          this.xshData.push(xsh)
        },
        deleteXsh(){
          if(this.xshSelectedRowKeys.length < 1){
            this.$message.warn("请先选择后再操作")
            return
          }
          let deleteXsh = null
          if(this.transferBoxInfo && this.transferBoxInfo.length > 0){
            deleteXsh = this.transferBoxInfo.filter(item => this.xshSelectedRowKeys.includes(item.index))
          }else{
            deleteXsh = this.xshData.filter(item => this.xshSelectedRowKeys.includes(item.index))
          }


          for (let i = 0; i < deleteXsh.length; i++) {
              if(deleteXsh[i].code && deleteXsh[i].code.length > 0){
                updateInBoxPosition({
                  id: this.modalData.ordTaskId,
                  inBoxPosition: null,
                  inBoxPositionId: null,
                  cellTestCodeList: deleteXsh[i].code.join(',')
                })

                for (let j = 0; j < deleteXsh[i].code.length; j++) {
                  let sample = null
                  if(this.transferBoxInfo && this.transferBoxInfo.length > 0){
                    sample = this.transferBoxInfo.find(f => f.cellTestCode == deleteXsh[i].code[j])
                  }else{
                    sample = this.firstData.find(f => f.cellTestCode == deleteXsh[i].code[j])
                  }

                    sample.inBoxPosition = null
                    sample.inBoxPositionId = null
                }

              }
          }
          this.xshData = this.xshData.filter(item => !this.xshSelectedRowKeys.includes(item.index))

          for (let i = 0; i < this.xshData.length; i++) {
              this.xshData.index = i+1
          }


          this.updateXsh(true)
        },
        deleteXshSafe(){
          if(this.xshSelectedRowKeys.length < 1){
            this.$message.warn("请先选择后再操作")
            return
          }
          let deleteXsh = null
          if(this.transferBoxInfo && this.transferBoxInfo.length > 0){
            deleteXsh = this.transferBoxInfo.filter(item => this.xshSelectedRowKeys.includes(item.index))
          }else{
            deleteXsh = this.xshData.filter(item => this.xshSelectedRowKeys.includes(item.index))
          }


          for (let i = 0; i < deleteXsh.length; i++) {
              if(deleteXsh[i].code && deleteXsh[i].code.length > 0){
                updateInBoxPosition({
                  id: this.modalData.ordTaskId,
                  inBoxPosition: null,
                  inBoxPositionId: null,
                  cellTestCodeList: deleteXsh[i].code.join(',')
                })

                for (let j = 0; j < deleteXsh[i].code.length; j++) {
                  let sample = null
                  if(this.transferBoxInfo && this.transferBoxInfo.length > 0){
                    sample = this.transferBoxInfo.find(f => f.cellTestCode == deleteXsh[i].code[j])
                  }else{
                    sample = this.firstData.find(f => f.cellTestCode == deleteXsh[i].code[j])
                  }

                    sample.inBoxPosition = null
                    sample.inBoxPositionId = null
                }

              }
          }
          this.xshData = this.xshData.filter(item => !this.xshSelectedRowKeys.includes(item.index))

          for (let i = 0; i < this.xshData.length; i++) {
              this.xshData.index = i+1
          }


          this.updateXshSafe(true)
        },

        // 展示选择的数据
        getCheckboxProps(record) {
            return {
                props: {
                    defaultChecked: this.flowId ? record.flowId === this.flowId : false,
                    disabled: this.modalData.taskStatus === "已完成" ? true : false
                }
            }
        },
        setCheckboxStatus(record) {
          return {
            props: {
              disabled: record.batteryStatus !== "ongoing"
            }
          }
        },
        // 点击中检按钮,查找对应的数据
        chooseMgData(record, dataIndex) {
            if (document.getElementById(dataIndex).style.backgroundColor === "rgb(255, 233, 237)") {
                document.getElementById(dataIndex).style.backgroundColor = "transparent"
            }

            // 如果已经有checkData的数据，获取flowId
            if (record.checkData) this.flowId = JSON.parse(record.checkData).flowId

            // 测试数据
            // tLimsTestdataScheduleList({ celltestcode: "04QCE34221101HD152126077-202303090041-0001", alias: "日历寿命1" })
            //正式情况
            tLimsTestdataScheduleList({ celltestcode: record.cellTestCode || String(new Date()), alias: record.alias })
                .then(res => {
                    if (res.success) {
                        this.mgData = res.data
                        if (res.data.length === 0 || res.data[0].flowId === '' || res.data[0].flowId === null) {
                            record.isMiddleClick = true
                            const params = {
                              id: this.modalData.ordTaskId,
                              lifeTestRecordDataMap: JSON.parse(JSON.stringify(this.firstData))
                            }
                            this.updateTestProDetail(params)
                        }
                    } else {
                        this.$message.error("查询失败：" + res.message)
                    }
                })
                .finally(() => {
                    this.mgVisible = true
                })
        },
        // excel  ->  table
        copyFromExcel(event, tableColumn, tableData, row, text) {
            // row:那行
            // column:那列
            let outArr = event.clipboardData.getData("text").split("\n")


            // 处理单个复制的情况，后续需优化
            if(outArr.length === 1){
                outArr[0] += '\r'
                outArr[1] += ''
            }


            let inArr = []
            let column // 从哪个位置开始复制
            let chilColumn // 那个位置为尺寸开始
            let chilColumn1 = -1 // 如果从尺寸位置开始粘贴，记录那个尺寸
            let chilColumn2 = -1 // 如果从尺寸位置开始粘贴，记录那个点位
            let firstData = ""
            let temOut = -1, temIn = -1
            tableColumn.forEach((v, index) => {
                if (v.dataIndex === text) {
                    column = index
                }
                if (v.children) {
                    chilColumn = index
                    v.children.forEach((chil, cIndex) => {
                        if (chil.children) {
                            if (chil.dataIndex === text.replaceAll(/[0-9]/g, "")) {
                                chilColumn1 = cIndex
                            }
                            chil.children.forEach((chil1, cIndex1) => {
                                if (chil1.dataIndex === text) {
                                    column = index
                                    chilColumn2 = cIndex1 - 1
                                }
                            })
                        }

                    })
                }
            })

            if (outArr.length > 1) {
                // 列
                for (let i = 0; i < outArr.length - 1; i++) {
                    // 置空
                    temOut = chilColumn1
                    temIn = chilColumn2
                    // 行
                    inArr = outArr[i].split("\t")
                    for (let j = 0; j < inArr.length; j++) {
                        if (inArr[j] !== null && inArr[j] !== "") {
                            if (i === 0 && j === 0) firstData = inArr[j]

                            // 尺寸单独处理
                            // tableColumn[chilColumn] 尺寸数组
                            if ((temOut > -1 && temOut < tableColumn[chilColumn].children.length) || (tableColumn[column + j] && tableColumn[column + j].dataIndex === "dimension")) {

                                temIn++

                                if (temOut === -1) {
                                    temOut++
                                }

                                if (tableData[row + i] && tableColumn[chilColumn].children[temOut].children) {
                                    tableData[row + i][tableColumn[chilColumn].children[temOut].children[temIn].dataIndex] = inArr[j].replace("\r", "").replaceAll(" ", "")
                                }
                                if (temIn === tableColumn[chilColumn].children[temOut].children.length - 1) {
                                    temOut++
                                    temIn = -1
                                }
                                continue
                            }

                            // // 其他非尺寸
                            if (tableData[row + i] && tableColumn[column + j]) {
                                let result = new Decimal(Number(inArr[j].replace("\r", "").replaceAll(" ", ""))).mul(new Decimal(1000))
                                tableData[row + i][tableColumn[column + j].dataIndex] = tableColumn[column + j].dataIndex === 'beforeVoltage' || tableColumn[column + j].dataIndex === 'afterVoltage' ||
                                tableColumn[column + j].dataIndex === 'beforeInnerres' || tableColumn[column + j].dataIndex === 'afterInnerres' ? result.toString() : inArr[j].replace("\r", "").replaceAll(" ", "")
                                // 如果是内阻，记录时间
                                if(tableColumn[column + j].dataIndex === 'beforeInnerres') tableData[row + i].timeOfFillInnerres = formatDate(new Date(), true)
                                if(tableColumn[column + j].dataIndex === 'afterInnerres') tableData[row + i].timeOfFillInnerres2 = formatDate(new Date(), true)
                            }

                        }
                    }
                }
            }
            setTimeout(() => {
                if (chilColumn1 === -1) {
                    let result = new Decimal(Number(firstData.replace("\r", "").replaceAll(" ", ""))).mul(new Decimal(1000))

                    tableData[row][tableColumn[column].dataIndex] = tableColumn[column].dataIndex === 'beforeVoltage' || tableColumn[column].dataIndex === 'afterVoltage' ||
                    tableColumn[column].dataIndex === 'beforeInnerres' || tableColumn[column].dataIndex === 'afterInnerres' ? result.toString()   : firstData.replace("\r", "").replaceAll(" ", "")

                    // 如果是内阻，记录时间
                    if(tableColumn[column].dataIndex === 'beforeInnerres') tableData[row][tableColumn[column].dataIndex].timeOfFillInnerres = formatDate(new Date(), true)
                    if(tableColumn[column].dataIndex === 'afterInnerres') tableData[row][tableColumn[column].dataIndex].timeOfFillInnerres2 = formatDate(new Date(), true)
                } else {
                    tableData[row][tableColumn[chilColumn].children[chilColumn1].children[chilColumn2 + 1].dataIndex] = firstData.replace("\r", "").replaceAll(" ", "")
                }
            }, 10)
        },


        handleChangeDate(dateString, status = 'in') {
            const params = {
                id: this.modalData.ordTaskId
            }
            if (status === 'out') {
                params.actualOutDate = dateString
                this.actualOutDate = dateString
                this.originalData.actualOutDate = dateString

            }
            if (status === 'in') {
                params.actualInDate = dateString
                this.actualInDate = dateString
                this.originalData.actualInDate = dateString
            }
            this.updateTestProDetail(params)
        },
        // 单击复制
        handleCopy(text) {
            var input = document.createElement("input") // 创建input对象
            input.value = text // 设置复制内容
            document.body.appendChild(input) // 添加临时实例
            input.select() // 选择实例内容
            document.execCommand("Copy") // 执行复制
            document.body.removeChild(input) // 删除临时实例
            this.$message.success(`复制成功:${text}`)
        },
        async _handleIsNull(data) {
            let result = 0
            let poinResult = 0
            await data.forEach(v => {
              if (v.batteryStatus === 'ongoing' || v.batteryStatus === 'testDone') {
                Reflect.ownKeys(v).forEach(e => {
                  if (e === 'checkData' || e === 'heightType' || e === 'inBoxPositionId' || e === 'inBoxPosition' || e === 'timeOfFillInnerres' || e === 'timeOfFillInnerres2' || e === 'safetyTestReviewId') return


                  if (e.replaceAll(/[^0-9]/g, "") > 1) {
                    if (v[e] === null || v[e] === "") poinResult++
                    return
                  }
                  if (v[e] === null || v[e] === "") {
                    result++
                  }
                })
              }
            })
            return [result === 0, poinResult === 0,]
        },
        _handleSetBGC(data, num) {
            data.forEach((v, index) => {
                if (v.batteryStatus === 'ongoing' || v.batteryStatus === 'testDone') {
                  Reflect.ownKeys(v).forEach(e => {
                    const classElement = document.getElementById(`${num}-${e}-${index}`)
                    if (!classElement) return
                    // 中检
                    if (e === "middleCheck") {
                      return (classElement.style.backgroundColor = v.isMiddleClick ? "transparent" : "#ffe9ed")
                    }
                    classElement.style.backgroundColor = v[e] === null || v[e] === "" ? "#ffe9ed" : "transparent"
                  })
                }
            })
        },

        // 下载处理
        handleDownload($event, index = 1) {
            exportModel({ ordTaskId: this.modalData.ordTaskId, middleCheckStage: index }).then(res => {
                const fileName = `${this.modalData.folderNo}-${this.modalData.wtrName}-${this.modalData.testName}-${this.originalData.orderNumber === 1 ? '初始性能检测' : '存储阶段' + (this.originalData.orderNumber - 1)}${this.middleCheck === "small"
                    ? "-小中检"
                    : this.middleCheck === "large"
                    ? "-大中检"
                    : this.middleCheck === "recharge" ? "-补电" : ""}.xlsx`
                if (res) {
                    downloadfile1(res, fileName)
                }
            })
        },

        /**
         * 弹窗事件
         */
        async handleModelCancel() {
            this.$emit("cancel")
        },
        handleCloseModal() {
            this.mgVisible = false
            this.flowId = ""
        },
        /*
         * 提交事件
         */
        handleSubmit() {

            // 校验
            if (!this.actualInDate && this.modalData.taskType.indexOf('last') === -1) {
              if (this.firstData.findIndex(v => v.batteryStatus === 'ongoing') !== -1) {
                return this.$message.warning("进箱时间未填写")
              }
            }
            let result = 0
            this.firstData.forEach(v => {
              if (v.batteryStatus === 'ongoing') {
                if (!v.inBoxPosition) result++
              }
            })
            if (result > 0 && this.modalData.taskType.indexOf('last') === -1) return this.$message.warning("进箱位置未填写")

            finishCalLifeTodoTask({ id: this.modalData.ordTaskId }).then(res => {
                if (!res.success) {
                    this.$message.error("错误提示：" + res.message)
                    return
                }
                this.$message.success("完成成功")
                this.$emit("submit")
            })
        },
      stageHandleSuccess () {
          finishCalLifeTodoTask({ id: this.modalData.ordTaskId }).then(res => {
            if (!res.success) {
              this.$message.error("错误提示：" + res.message)
              return
            }
            this.$message.success("完成成功")
            this.$emit("submit")
          })
        },
        // 阶段式日历寿命测试-完成按钮事件
        stageHandleSubmit() {
          let transferBoxData = this.originalData
          if (transferBoxData.transferBoxFlag === 1 || transferBoxData.transferBoxFlag === 2) {
            let transferBoxInfoData = JSON.parse(transferBoxData.transferBoxInfo)
            let untransferBoxCount = 0
            transferBoxInfoData.forEach(v => {
              if (v.batteryStatus === 'ongoing') {
                Reflect.ownKeys(v).forEach(e => {
                  //初始温箱不判断
                  if ((e != 'originInBoxPosition' && e != 'originInBoxPositionId') && (v[e] === null || v[e] === "")) {
                    untransferBoxCount++
                  }
                })
              }
            })
            if (untransferBoxCount !== 0) {
              return this.$warning({
                content: "完成待办任务前请先将电芯转移"
              })
            }
          }
          let result = 0
          let poinResult = 0
          this.lastData.forEach(v => {
            if (v.batteryStatus === 'ongoing') {
              Reflect.ownKeys(v).forEach(e => {
                if (e === 'checkData' || e === 'heightType' || e === 'inBoxPositionId' || e === 'inBoxPosition' || e === "timeOfFillInnerres" || e === 'timeOfFillInnerres2') return
                if (e.replaceAll(/[^0-9]/g, "") > 1) {
                  if (v[e] === null || v[e] === "") poinResult++
                  return
                }
                if (v[e] === null || v[e] === "") {
                  result++
                }
                if (e === 'isMiddleClick' && !v[e]) {
                  result++
                }
              })
            }
          })
          // 尺寸信息：×，需填写信息： √
          if (poinResult !== 0 && result === 0) {
            return this.$warning({
              content: "请先将尺寸数据填写完整在进行下一步"
            })
            // 需填写信息： √
          } else if (result === 0) {
            this.stageHandleSuccess()
            // 剩余情况
          } else {
            const temList = JSON.parse(JSON.stringify(this.lastData))
            let num = this.modalData.haveInspection === 0 ? "first" : "last"
            this._handleSetBGC(temList, num)
            return this.$warning({
              content: "完成待办任务前请先将数据填写完成"
            })
          }
        },
       validPictureAndVideo() {
         let noFillVideo = false
         let noFillPicture = false
         if (this.originalData.video === "1") {
           if (!this.originalData.videoId && this.firstData.findIndex(v => v.batteryStatus === 'ongoing') !== -1) {
             noFillVideo = true
           }
         }
         if (this.originalData.picture === "1") {
           var allSamplePictureList = [];
           this.originalData.lifeTestRecordDataMap.forEach(item => {
             const keys = Object.keys(item.samplePicture);
             var samplePictureList = [];
             for (const index in keys) {
               let picture = item.samplePicture[keys[index]]
               if (item.batteryStatus === 'ongoing') {
                 samplePictureList.push(picture);
               }
             }
             if (item.batteryStatus === 'ongoing') {
               allSamplePictureList.push(samplePictureList);
             }
           })
           for (const i in allSamplePictureList) {
             if (allSamplePictureList[i].findIndex(item => item.id && item.name) === -1) {
               noFillPicture = true
             }
           }
         }
         return [noFillVideo, noFillPicture]
       },
        // 最后一个阶段-完成按钮事件
        handleLastSubmit() {
          // 有拍照步骤
          if (this.pictureOrVideo) {
            let noFillVideo = this.validPictureAndVideo()[0]
            let noFillPicture = this.validPictureAndVideo()[1]
            if (noFillVideo) {
              return this.$warning({
                content: "请先上传视频再进行下一步"
              })
            } else if (noFillPicture) {
              return this.$warning({
                content: "每个电芯至少需要上传一张照片"
              })
            } else {
              this.handleSubmit()
            }
          } else {
            // 无拍照步骤
            let result = 0
            let poinResult = 0
            this.lastData.forEach(v => {
                if (v.batteryStatus === 'ongoing') {
                  Reflect.ownKeys(v).forEach(e => {
                    if (e === 'checkData' || e === 'heightType' || e === 'inBoxPositionId' || e === 'inBoxPosition' || e === "timeOfFillInnerres" || e === 'timeOfFillInnerres2') return
                    if (e.replaceAll(/[^0-9]/g, "") > 1) {
                      if (v[e] === null || v[e] === "") poinResult++
                      return
                    }
                    if (v[e] === null || v[e] === "") {
                      result++
                    }
                    if (e === 'isMiddleClick' && !v[e]) {
                      result++
                    }
                  })
                }
            })



            // 尺寸信息：×，需填写信息： √
            if (poinResult !== 0 && result === 0) {
              return this.$warning({
                content: "请先将尺寸数据填写完整在进行下一步"
              })

                // 需填写信息： √
            } else if (result === 0) {
                this.handleSubmit()
                // 剩余情况
            } else {
                const temList = JSON.parse(JSON.stringify(this.lastData))
                this._handleSetBGC(temList, "last")
                return this.$warning({
                    content: "完成待办任务前请先将数据填写完成"
                })
            }
          }
        },
        // 弹窗
        // 测试别名复制
        handleAliasCopy() {
            this.isShowDays = true
            this.startDay = this.originalData.totalDay

        },
        handleModalOk() {
            if (this.startDay >= 0) {
                aliasCopy({ ordTaskId: this.modalData.ordTaskId, startDay: Number(this.startDay) }).then(res => {
                    this.handleCopy(res.data)
                })
            }
            this.isShowDays = false
        },
        handleModalCancel() {
            this.isShowDays = false
        },
        handleActTransferCancel() {
          this.isShowActTransferDates = false
          this.actTransferDate = null
        },
        async handleActTransferOk() {
          if (!this.actTransferDate) {
            this.$message.warning('请填写实际转移时间！')
            return
          }
          for (const x of this.xshData) {
             await updateInBoxPosition({
              id: this.modalData.ordTaskId,
              inBoxPosition: x.luoName,
              inBoxPositionId: x.index,
              actTransferBatteryTime: moment(this.actTransferDate).format('YYYY-MM-DD'),
              cellTestCodeList: x.code.join(",")
            }).then(res => {
              this.$message.success('设置成功')

            })
          }
          this.getTestProDetailByTaskId()
          this.isShowPositions = false
          this.isShowActTransferDates = false
          this.actTransferDate = null

        },
        openDetail(record){
          this.luoXshRecord = record
          getTestWarmBoxLuoInOutDetail({luoId:record.id}).then(res => {
            this.batteryInOutData = res.data
            this.visible3 = true
          })
        },
        getInOutDetailList(){

          this.detailQueryparam.luoId = this.luoXshRecord.id

          getTestWarmBoxLuoInOutDetail(this.detailQueryparam).then(res => {
            this.batteryInOutData = res.data
            this.visible3 = true
          })
        },
        positionOnSelect(positionSelectedRowKeys, positionSelectedRows) {
            this.positionSelectedRows = positionSelectedRows
            this.positionSelectedRowKeys = positionSelectedRowKeys
        },
        cellOnSelect(cellSelectedRowKeys, cellSelectedRows) {
          let xsh = this.xshData.find(x => x.index == this.xshSelectIndex)
          if(xsh.type == null){
            this.$message.warning('请先选择吸塑盒类型！')
            return
          }
          let num = this.getXshSampleNum(xsh.type)
          if(cellSelectedRowKeys.length > num){
            this.$message.warning('电芯数量不能超过最大容量，请添加吸塑盒！')
            this.cellSelectedRowKeys = cellSelectedRowKeys.splice(0,num)
          }else{
            this.cellSelectedRows = cellSelectedRows
            this.cellSelectedRowKeys = cellSelectedRowKeys
          }


        },
        cellOnSelectSafe(cellSelectedRowKeys, cellSelectedRows) {

          this.cellSelectedRows = cellSelectedRows
          this.cellSelectedRowKeys = cellSelectedRowKeys

        },

        getXshSampleNum(type){
          let num = 1
          switch (type) {
            case 'G26-未焊接12EA':
              num = 12
              break;
            case 'G圆柱-焊接4EA':
              num = 4
              break;
            case 'G圆柱-支架5EA':
              num = 5
              break;
            case 'G圆柱-未焊接10EA':
              num = 10
              break;
            case 'C圆柱-6EA':
              num = 6
              break;
            case 'V圆柱-18650':
              num = 96
              break;
            case 'V圆柱-21700':
              num = 63
              break;
            case '396389-24EA':
              num = 24
              break;
            case 'HP01':
              num = 2
              break;
          }

          return num
        },

        sampleSelectAll(selected, selectedRows,changeRows) {
            let xsh = this.xshData.find(x => x.index == this.xshSelectIndex)
            if(xsh.type == null){
              this.$message.warning('请先选择吸塑盒类型！')
              return
            }
            let num = this.getXshSampleNum(xsh.type)
            if(selectedRows.length > num){
                this.$message.warning('电芯数量不能超过最大容量，请添加吸塑盒！')
                this.cellSelectedRowKeys = selectedRows.splice(0,num).map(s => s.cellTestCode)
            }
        },
        sampleSelectAllSafe(selected, selectedRows,changeRows) {

        },
        setInboxPosition() {

            /*if (this.cellSelectedRows.length < 1) {
                this.$message.warning('请至少选择一条数据')
                return
            }*/

            getCanInBoxStoreyOnlyBatteryType({ pageNo: 1, pageSize: 10, id: this.originalData.id }).then((res) => {
                if (res.data.rows.length === 0) {
                  return this.$warning({
                    content: "无温箱可选时，请联系相应组长整合信息反馈给黄辉宁处理",
                    centered: true
                  })
                } else {
                  this.isShowPositions = true
                  this.$refs.boxTable.refresh();
                }
            })
        },
        changeXshType(value){
            this.xshData.forEach(x => {
              x.type = value
            })

        },
        openSelectBox() {
            //回写位置信息
            if(this.xshData.length > 0){
                this.xshOk(true)
            }


            if(this.progress.sampleType == 'G圆柱' && !this.progress.productName.includes('G26')){
              this.xshOptions =  [
                {label:'G圆柱-焊接4EA',value:'G圆柱-焊接4EA'},
                {label:'G圆柱-支架5EA',value:'G圆柱-支架5EA'},
                {label:'G圆柱-未焊接10EA',value:'G圆柱-未焊接10EA'}
              ]
              if(this.xshData.length == 0){
                this.xshData.push({index:1})
              }
            }else

            if(this.progress.productName.includes('G26')){
              this.xshOptions =  [
                {label:'G26-未焊接12EA',value:'G26-未焊接12EA'}
              ]
              if(this.xshData.length == 0){
                this.xshData.push({index:1,type:'G26-未焊接12EA'})
              }
            }else
            if(this.progress.sampleType == 'C圆柱'){
              this.xshOptions =  [
                {label:'C圆柱-6EA',value:'C圆柱-6EA'}
              ]
              if(this.xshData.length == 0){
                this.xshData.push({index:1,type:'C圆柱-6EA'})
              }
            }else

            if(this.progress.sampleType == 'V圆柱'){
              this.xshOptions =  [
                {label:'V圆柱-18650',value:'V圆柱-18650'},
                {label:'V圆柱-21700',value:'V圆柱-21700'}
              ]
              if(this.xshData.length == 0){
                this.xshData.push({index:1})
              }
            }else


            if(this.progress.testAddress == 'R2_2F' && this.progress.sampleType == '软包'){
              this.xshOptions =  [
                {label:'396389-24EA',value:'396389-24EA'},
                {label:'HP01',value:'HP01'}
              ]
              if(this.xshData.length == 0){
                this.xshData.push({index:1,type:'396389-24EA'})
              }
            }else

            if(['方形','方型','软包'].includes(this.progress.sampleType)){
              this.xshOptions =  [
                {label:'方形/软包',value:'方形/软包'}
              ]
              if(this.xshData.length == 0){
                for (let i = 0; i < this.firstData.length; i++) {
                  this.xshData.push({index:i+1,type:'方形/软包',code:[this.firstData[i].cellTestCode]})
                }
              }
            }



            this.isShowPositions = true

            if(this.transferBoxInfo && this.transferBoxInfo.length > 0){
               this.isShowActTransferDates = true
            }


        },
        openSelectBoxSafe() {
            //回写位置信息
            if(this.xshData.length > 0){
                this.xshOkSafe(true)
            }

            if(this.xshData.length == 0){
              this.xshData.push({index:1})
            }

            this.isShowPositionsSafe = true


        },
        handlePositionCancel() {
            this.isShowPositions = false
        },
        handleSelectSampleCancel() {
            this.xshSelectSampleVisible = false
            this.cellSelectedRowKeys = []
        },
        filterCellCodeList() {
          this.cellSelectedRows = []
          this.cellSelectedRowKeys = []
          if (this.searchCellCode) {
            this.transferBoxInfo = this.initTransferBoxInfo.filter(item => item.cellTestCode.includes(this.searchCellCode))
          } else {
            this.transferBoxInfo = this.initTransferBoxInfo
          }
        },
        handlePositionOk() {
          if (this.positionSelectedRows.length !== 1) {
            this.$message.warning('请选择温箱！')
            return
          }
          if (this.originalData.transferBoxFlag === 1 || this.originalData.transferBoxFlag === 2) {
            this.isShowActTransferDates = true
          } else {
            const cellTestCodeList = this.cellSelectedRows.filter(o => o.batteryStatus === 'ongoing').map(item => item.cellTestCode)
            updateInBoxPosition({
              id: this.modalData.ordTaskId,
              inBoxPosition: this.positionSelectedRows[0].storeyCode,
              inBoxPositionId: this.positionSelectedRows[0].id,
              cellTestCodeList: cellTestCodeList.join(",")
            }).then(res => {
              this.$message.success('设置成功')
              this.getTestProDetailByTaskId()
            })
            this.isShowPositions = false
          }
        },
        handleLuoPositionOk() {
          if (this.positionSelectedRows.length !== 1) {
            this.$message.warning('请选择摞！')
            return
          }

          let luo = this.positionSelectedRows[0]
          let luoName = luo.storeyCode + '(第'+luo.luoIndex+'摞)'

          if(this.xshData.filter(f => this.positionSelectedRows[0].id == f.luoId)
            .filter(xs => !this.xshRecordList.map(x => x.index).includes(xs.index) ).length + this.xshRecordList.length > this.positionSelectedRows[0].canInNum){
            this.$message.warning('当前摞可放吸塑盒数量不足，请重新选择摞')
            return;
          }

          for (let i = 0; i < this.xshRecordList.length; i++) {
            let xsh = this.xshData.find(x => this.xshRecordList[i].index == x.index)
            xsh.luoId = luo.id
            xsh.luoName = luoName

            let code = xsh.code
            for (let j = 0; j < code.length; j++) {
              let sample = null
              if(this.transferBoxInfo && this.transferBoxInfo.length > 0){
                sample = this.transferBoxInfo.find(f => code[j] == f.cellTestCode)
              }else{
                sample = this.firstData.find(f => code[j] == f.cellTestCode)
              }

              sample.inBoxPositionId = xsh.index
              sample.inBoxPosition = luoName
              updateInBoxPosition({
                id: this.modalData.ordTaskId,
                inBoxPosition: luoName,
                inBoxPositionId: xsh.index,
                cellTestCodeList: sample.cellTestCode
              }).then(res => {
                this.updateXsh(true)
              }).then(res => {
                // this.getTestProDetailByTaskId()
              })
            }

          }

          this.selectLuoVisible = false

        },
        handleLuoPositionOkSafe() {
          if (this.positionSelectedRows.length !== 1) {
            this.$message.warning('请选择温箱！')
            return
          }

          let luo = this.positionSelectedRows[0]
          let luoName = luo.storeyCode


          for (let i = 0; i < this.xshRecordList.length; i++) {
            let xsh = this.xshData.find(x => this.xshRecordList[i].index == x.index)
            let code = xsh.code
            //第六实验室（JM）控制最大数量
            if("JM" == this.progress.testAddress && code.length > luo.totalPcsNum - luo.totalPcsInNum){
                this.$message.warn("温箱可容纳数量不足，请重新选择")
                return;
            }

            xsh.luoId = luo.id
            xsh.luoName = luoName
            for (let j = 0; j < code.length; j++) {
              let sample = null
              if(this.transferBoxInfo && this.transferBoxInfo.length > 0){
                sample = this.transferBoxInfo.find(f => code[j] == f.cellTestCode)
              }else{
                sample = this.firstData.find(f => code[j] == f.cellTestCode)
              }

              sample.inBoxPositionId = xsh.index
              sample.inBoxPosition = luoName
              updateInBoxPosition({
                id: this.modalData.ordTaskId,
                inBoxPosition: luoName,
                inBoxPositionId: xsh.index,
                cellTestCodeList: sample.cellTestCode
              }).then(res => {
                this.updateXsh(true)
              }).then(res => {
                // this.getTestProDetailByTaskId()
              })
            }

          }

          this.selectLuoVisibleSafe = false

        },

        xshOk(flag) {

          let codeList = []
          for (let i = 0; i < this.xshData.length; i++) {

              if(this.xshData[i].code && this.xshData[i].code.length > 0){
                let num = this.getXshSampleNum(this.xshData[i].type)
                if(num < this.xshData[i].code.length){
                  this.xshData[i].code = this.xshData[i].code.splice(0,num)
                  this.$message.warning('电芯数量不能超过最大容量，请添加吸塑盒！')
                  return
                }
                codeList = codeList.concat(this.xshData[i].code)

                for (let j = 0; j < this.xshData[i].code.length; j++) {
                  let sample = null
                  if(this.transferBoxInfo && this.transferBoxInfo.length > 0){
                    sample = this.transferBoxInfo.find(f => f.cellTestCode == this.xshData[i].code[j])
                  }else{
                    sample = this.firstData.find(f => f.cellTestCode == this.xshData[i].code[j])
                  }
                  sample.inBoxPosition = this.xshData[i].luoName
                  sample.inBoxPositionId = this.xshData[i].index
                }

                updateInBoxPosition({
                  id: this.modalData.ordTaskId,
                  inBoxPosition: this.xshData[i].luoName?this.xshData[i].luoName:null,
                  inBoxPositionId: this.xshData[i].index,
                  cellTestCodeList: this.xshData[i].code.join(",")
                })
              }
          }
          let unCheckCode = null
          if(this.transferBoxInfo && this.transferBoxInfo.length > 0){
            unCheckCode = this.transferBoxInfo.filter(f => 'ongoing' == f.batteryStatus).filter(f => !codeList.includes(f.cellTestCode))
          }else{
            unCheckCode = this.firstData.filter(f => 'ongoing' == f.batteryStatus).filter(f => !codeList.includes(f.cellTestCode))
          }

          let codeString = ''
          if(unCheckCode.length > 0){

            for (let j = 0; j < unCheckCode.length; j++) {
              let sample = null
              if(this.transferBoxInfo && this.transferBoxInfo.length > 0){
                sample = this.transferBoxInfo.find(f => f.cellTestCode == unCheckCode[j].cellTestCode)
              }else{
                sample = this.firstData.find(f => f.cellTestCode == unCheckCode[j].cellTestCode)
              }
              sample.inBoxPosition = null
              sample.inBoxPositionId = null
            }

            codeString = unCheckCode.map(u => u.cellTestCode).join(",")
            updateInBoxPosition({
              id: this.modalData.ordTaskId,
              inBoxPosition: null,
              inBoxPositionId: null,
              cellTestCodeList: codeString
            }).then(() => {

              this.$message.warn(codeString+'未设置')
            })

          }else{
            this.updateXsh(false)
          }




        },
        xshOkSafe(flag) {

          let codeList = []
          for (let i = 0; i < this.xshData.length; i++) {

              if(this.xshData[i].code && this.xshData[i].code.length > 0){

                codeList = codeList.concat(this.xshData[i].code)

                for (let j = 0; j < this.xshData[i].code.length; j++) {
                  let sample = null
                  if(this.transferBoxInfo && this.transferBoxInfo.length > 0){
                    sample = this.transferBoxInfo.find(f => f.cellTestCode == this.xshData[i].code[j])
                  }else{
                    sample = this.firstData.find(f => f.cellTestCode == this.xshData[i].code[j])
                  }
                  sample.inBoxPosition = this.xshData[i].luoName
                  sample.inBoxPositionId = this.xshData[i].index
                }

                updateInBoxPosition({
                  id: this.modalData.ordTaskId,
                  inBoxPosition: this.xshData[i].luoName?this.xshData[i].luoName:null,
                  inBoxPositionId: this.xshData[i].index,
                  cellTestCodeList: this.xshData[i].code.join(",")
                })
              }
          }
          let unCheckCode = null
          if(this.transferBoxInfo && this.transferBoxInfo.length > 0){
            unCheckCode = this.transferBoxInfo.filter(f => 'ongoing' == f.batteryStatus).filter(f => !codeList.includes(f.cellTestCode))
          }else{
            unCheckCode = this.firstData.filter(f => 'ongoing' == f.batteryStatus).filter(f => !codeList.includes(f.cellTestCode))
          }

          let codeString = ''
          if(unCheckCode.length > 0){

            for (let j = 0; j < unCheckCode.length; j++) {
              let sample = null
              if(this.transferBoxInfo && this.transferBoxInfo.length > 0){
                sample = this.transferBoxInfo.find(f => f.cellTestCode == unCheckCode[j].cellTestCode)
              }else{
                sample = this.firstData.find(f => f.cellTestCode == unCheckCode[j].cellTestCode)
              }
              sample.inBoxPosition = null
              sample.inBoxPositionId = null
            }

            codeString = unCheckCode.map(u => u.cellTestCode).join(",")
            updateInBoxPosition({
              id: this.modalData.ordTaskId,
              inBoxPosition: null,
              inBoxPositionId: null,
              cellTestCodeList: codeString
            }).then(() => {

              this.$message.warn(codeString+'未设置')
            })

          }else{
            this.updateXshSafe(false)
          }




        },

        updateXsh(unClose){

          for (let i = 0; i < this.xshData.length; i++) {
            if(this.xshData[i].luoId != null){

              let luo = this.canInLuoData.find(c => c.id == this.xshData[i].luoId)

              if(luo && this.xshData.filter(f => this.xshData[i].luoId == f.luoId).length  > luo.canInNum){
                this.$message.warning('当前摞可放吸塑盒数量不足，请重新选择摞')
                return
              }
            }

          }

          let param = {}
          param.id = this.modalData.ordTaskId
          param.boxInfo = JSON.stringify(this.xshData)
          updateXsh(param)
          if(!unClose){
            this.isShowPositions = false
          }

        },
        updateXshSafe(unClose){

          let param = {}
          param.id = this.modalData.ordTaskId
          param.boxInfo = JSON.stringify(this.xshData)
          updateXsh(param)
          if(!unClose){
            this.isShowPositionsSafe = false
          }

        },
        handleSelectSampleOk() {

          let xsh = this.xshData.find(x => x.index == this.xshSelectIndex)
          xsh.code = this.cellSelectedRowKeys
          if(this.transferBoxInfo && this.transferBoxInfo.length > 0){
            this.transferBoxInfo.filter(f => f.inBoxPositionId == this.xshSelectIndex).forEach( fi => fi.inBoxPositionId = null)
          }else{
            this.firstData.filter(f => f.inBoxPositionId == this.xshSelectIndex).forEach( fi => fi.inBoxPositionId = null)
          }


          for (let i = 0; i < this.cellSelectedRowKeys.length; i++) {
            if(this.transferBoxInfo && this.transferBoxInfo.length > 0){
              this.transferBoxInfo.find(f => f.cellTestCode == this.cellSelectedRowKeys[i]).inBoxPositionId = this.xshSelectIndex
            }else{
              this.firstData.find(f => f.cellTestCode == this.cellSelectedRowKeys[i]).inBoxPositionId = this.xshSelectIndex
            }

          }


          this.handleSelectSampleCancel()

        },
        positionLoadData() {
            return getInBoxPositionList().then((res) => {
                this.positionResultData = res.data
                this.allPositionResultData = JSON.parse(JSON.stringify(res.data))
            })
        },
        searchPositionData() {
            this.$refs.boxTable.refresh();

        },
        customRowEvent(record) {
            return {
                on: {
                    click: () => {
                        this.positionSelectedRows = []
                        this.positionSelectedRowKeys = []
                        this.positionSelectedRows.push(record)
                        this.positionSelectedRowKeys.push(record.rackNumber)
                    },
                },
            };
        },

        // 处理选中大小中检、补电
        async _handleMiddleCheck(){
            if(this.firstData.length === 0) return
            await this.firstData.forEach(async v => {
                // 如果本来就选择了
                if(v.checkData || v.isMiddleClick) return
                await tLimsTestdataScheduleList({ celltestcode: v.cellTestCode || String(new Date()), alias: v.alias }).then(res => {
                    console.log(v)

                    // 没有数据，默认选中
                    if(res.data.length === 0){
                        v.isMiddleClick = true
                    }else{
                        // 没有选择的，就把存储天数相同的第一条赋给(父层级)
                        const have = res.data.filter(filterItem => filterItem.day == this.originalData.totalDay)
                        if(have.length > 0){
                            // 只有一条的时候，默认勾选上,多条需手动匹配
                            v.isMiddleClick = have.length == 1 ? true : false
                            if(have.length == 1) v.checkData = JSON.stringify(have[0])
                          //匹配层级数据
                        }else{
                          console.log(res.data)

                          // 是否有多条的数据的情况，多条数据的情况，需要工程师自动勾选
                          const isHave = false

                          // 找父层级底下的子层级
                          for (let i = 0; i < res.data.length; i++) {
                            const da = res.data[i]
                            console.log(da)
                            if(da.children && da.children.length > 0){
                              const haveChild = da.children.filter(child => child.day == this.originalData.totalDay)

                              // 多条的情况，需要工程师自动勾选
                              if(haveChild.length > 1){
                                console.log('需自动匹配')
                                isHave = true
                                break
                              }

                              // 只有一条的时候，默认勾选上
                              if(haveChild.length === 1) {
                                console.log("匹配上了")
                                console.log(haveChild)
                                v.isMiddleClick = true
                                v.checkData = JSON.stringify(haveChild[0])
                                break
                              }
                            }
                          }

                          // 所有都找完后，都没有就自动勾选上
                          if(!isHave && !v.isMiddleClick){
                            console.log('都没匹配上')
                            v.isMiddleClick = true
                          }

                        }
                    }

                    const params = {
                      id: this.modalData.ordTaskId,
                      lifeTestRecordDataMap: JSON.parse(JSON.stringify(this.firstData))
                    }

                    updateTestProDetail(params).then(res => {
                        if (!res.success) return this.$message.error("错误提示：" + res.message)
                    })

                })
            })


            // this.updateTestProDetail({
            //
            // })


        },
        handleResetCheckData(){
            this.firstData.forEach(v => {
                v.checkData = null
                v.isMiddleClick = false
            })

            const params = {
                id: this.modalData.ordTaskId,
                lifeTestRecordDataMap: JSON.parse(JSON.stringify(this.firstData))
            }
            this.updateTestProDetail(params)
        },
        // 处理拍照步骤的表头
        handlePicOrVidColums(value) {
          let temColuns = []
          const temList = []
          for (let i in value[0]) {
            const temObj = {
              title: this.tableNameMenu[i],
              dataIndex: i,
              align: "center",
              width: "82px",
              scopedSlots: {
                customRender: i
              }
            }
            if (i !== "alias" && i !== "cellTestCode") continue
            temColuns.push(temObj)
            temList.push(temObj)
          }
          const chilList = []
          if (value[0].samplePicture) {
            for (let i in value[0].samplePicture) {
              const temObj = {
                title: this.picOrVidMenu[i],
                dataIndex: i,
                align: "center",
                width: "82px",
                scopedSlots: {
                  customRender: i
                }
              }
              chilList.push(temObj)
              temList.push(temObj)
            }
          }
          if (chilList.length !== 0) {
            temColuns.push({
              title: "样品照片",
              dataIndex: "samplePicture",
              align: "center",
              children: chilList
            })
          }
          let batteryStatusItem = {
            title: '电芯状态',
            dataIndex: 'batteryStatus',
            align: "center",
            width: "50px",
            customRender: (text, record, index) => {
              switch (text) {
                case "ongoing":
                  return "进行中"
                case "earlyEnd":
                  return "状态正常-提前结束"
                case "batteryDisassembly":
                  return "状态正常-电池拆解"
                case "pressureDrop":
                  return "掉压失效-终止测试"
                case "abnormalHot":
                  return "异常发热-终止测试"
                case "openShellAndLeak":
                  return "开壳漏液-终止测试"
                case "shellRust":
                  return "壳体生锈-终止测试"
                case "operationError":
                  return "作业错误-终止测试"
                case "thermalRunaway":
                  return "热失控-终止测试"
                case "acrException":
                  return "内阻异常-终止测试"
              }
            }
          }
          temColuns.splice(2, 0, batteryStatusItem);
          temList.splice(2, 0, batteryStatusItem);
          return [temColuns, temList]
        },
        //直接调用lims接口预览或下载
        async openFileOrDownload(fileId, fileName) {
          //pbi上传的文件
          if (fileId) {
            getMinioPreviewUrl(fileId).then(res => {
              //预览
              let suffixArray = ['.mp4','.MP4','.jpg','.JPG','.png','.PNG','.gif','.GIF','.pdf','.PDF']
              if (fileName && suffixArray.findIndex(item => fileName.indexOf(item) > -1) !== -1) {
                this.iframeUrl = res.data.replace("http://10.100.1.99:9000/", "/minioDownload/")
                this.filePreviewVisible = true
              } else {
                //下载
                getMinioDownloadUrl(fileId).then(res1 => {
                  downloadMinioFile(res1.data)
                })
              }
            })
          }
        },
        downloadVideoOfAq() {
          if (this.originalData.videoId) {
            sysFileInfoDownload({ id: this.originalData.videoId })
              .then(res => {
                this.downloadfile(res)
              })
              .catch(err => {
                this.$message.error("下载错误：获取文件流错误")
              })
          }
        },
        downloadPictureOfAq(pictureId) {
          if (pictureId) {
            sysFileInfoDownload({ id: pictureId })
              .then(res => {
                this.downloadfile(res)
              })
              .catch(err => {
                this.$message.error("下载错误：获取文件流错误")
              })
          }
        },
        downloadfile(res) {
          var blob = new Blob([res.data], { type: "application/octet-stream;charset=UTF-8" })
          var contentDisposition = res.headers["content-disposition"]
          var patt = new RegExp("filename=([^;]+\\.[^\\.;]+);*")
          var result = patt.exec(contentDisposition)
          var filename = result[1]
          var downloadElement = document.createElement("a")
          var href = window.URL.createObjectURL(blob) // 创建下载的链接
          var reg = /^["](.*)["]$/g
          downloadElement.style.display = "none"
          downloadElement.href = href
          downloadElement.download = decodeURI(filename.replace(reg, "$1")) // 下载后文件名
          document.body.appendChild(downloadElement)
          downloadElement.click() // 点击下载
          document.body.removeChild(downloadElement) // 下载完成移除元素
          window.URL.revokeObjectURL(href)
        },
        deleteVideo() {
          let update = {}
          update.id = this.originalData.id
          this.modalLoading = true
          updatePicOrVid(update, 'delete', 'video', -1).then(res => {
            if (res.success) {
              this.getTestProDetailByTaskId(this.current)
              this.$message.success("删除成功")
            } else {
              this.$message.error("删除失败：" + res.message)
            }
            setTimeout(() => {
              this.modalLoading = false
            },500)
          })
        },
        uploadVideo(info) {
          this.videoLoading = true
          if (info.file.status === "done") {
            let file = info.file
            let update = {}
            update.id = this.originalData.id
            update.videoId = file.response.data
            update.videoName = file.name
            updatePicOrVid(update, 'add', 'video', -1).then(res => {
              if (res.success) {
                this.getTestProDetailByTaskId(this.current)
                this.$message.success(`${info.file.name} 上传成功`)
              } else {
                this.$message.error("上传失败：" + res.message)
              }
              setTimeout(() => {
                this.videoLoading = false
              },500)
            })
          } else if (info.file.status === "error") {
            this.videoLoading = false
            this.$message.error(`${info.file.name} 上传失败`)
          } else {
            this.videoLoading = false
          }
        },
        uploadPicture(info, field, index) {
          if (info.file.status === "done") {
            let file = info.file
            let update = {}
            update.id = this.originalData.id
            update.pictureId = file.response.data
            update.pictureName = file.name
            this.modalLoading = true
            updatePicOrVid(update, 'add', field, index).then(res => {
              console.log('res',res)
              if (res.success) {
                this.getTestProDetailByTaskId(this.current)
                this.$message.success(`${info.file.name} 上传成功`)
              } else {
                this.$message.error("上传失败：" + res.message)
              }
              setTimeout(() => {
                this.modalLoading = false
              },500)
            })
          } else if (info.file.status === "error") {
            this.$message.error(`${info.file.name} 上传失败`)
          }
        },
        deletePicture(field, index) {
          let update = {}
          update.id = this.originalData.id
          this.modalLoading = true
          updatePicOrVid(update, 'delete', field, index).then(res => {
            if (res.success) {
              this.getTestProDetailByTaskId(this.current)
              this.$message.success("删除成功")
            } else {
              this.$message.error("删除失败：" + res.message)
            }
            setTimeout(() => {
              this.modalLoading = false
            },500)
          })
        },
        beforeUploadVideo(file) {
          let isJpgOrPng = file.type === 'video/mp4' || file.type === 'video/avi' || file.type === 'video/x-ms-wmv' || file.type === 'video/quicktime';
          if (!isJpgOrPng) {
            this.$message.error('格式错误，只能上传mp4、avi、wmv、mov格式的视频');
            return false;
          }
        },
        beforeUploadPicture(file) {
          let isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg' || file.type === 'image/gif';
          if (!isJpgOrPng) {
            this.$message.error('格式错误，只能上传jpg、png、gif格式的图片');
            return false;
          }
        },

        handlePreviewCancel() {
          this.previewVisible = false;
        },

        async handlePreview(file) {
          getMinioPreviewUrl(file.id).then(res => {
            if (res.data) {
              this.previewImage = res.data.replace("http://10.100.1.99:9000/", "/minioDownload/")
              setTimeout(() => {
                this.previewVisible = true
              }, 100)
            } else {
              this.$message.error("服务器错误，请联系管理员！")
            }
          })
        },

        handleSaveTestData () {
          const params = {
            id: this.modalData.ordTaskId,
            lifeTestRecordDataMap: JSON.parse(JSON.stringify(this.firstData))
          }
          handleSaveCalendarTestData(params).then(res => {
            if (res.success) {
              this.$message.success("保存成功！")
            } else {
              this.$message.error("错误提示：" + res.message)
            }
          }).finally(() => {
            earlyWarningCalendarCheck({id : this.modalData.id}).then(res =>  {
              this.abnormalList = res.data.abnormalList
              this.isAbnormal = res.data.isAbnormal
              this.abnormalStatus = res.data.abnormalStatus
              this.isEngineerConfirmNotChange = res.data.isEngineerConfirmNotChange
            })
            if(this.modalData.currentStep != null && this.modalData.currentStep < this.current){
              this.calendarTechNextStep()
            }
          })
        }
    }
}