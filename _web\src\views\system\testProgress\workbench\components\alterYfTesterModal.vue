<template>
  <div style="background-color: #FFFFFF;padding: 10px">
    <a-modal :title="`${modalData.testName} 更改测试员`" :visible="true" :width="1000" :centered="true" @cancel="personHandleCancel" @ok="personSubmit">
      <div style="margin: 0px 0px 20px 100px">
        <a-row :gutter="[8,8]">
          <a-col :span="10">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="姓名">
              <a-input v-model="searchTesterParam.USERNAME" @keyup.enter="searchTesters" @change="searchTesters"/>
            </a-form-item>
          </a-col>
          <a-col :span="10">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="工号">
              <a-input v-model="searchTesterParam.ID" @keyup.enter="searchTesters" @change="searchTesters"/>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
      <a-spin :spinning="personIsLoading">
        <a-table :columns="personColumns"
                 :data-source="personResultData"
                 bordered
                 :rowKey="(record) => record.ID"
                 :row-selection="{
                    type: 'radio',
                    selectedRowKeys: personSelectedRowKeys,
                    selectedRows: personSelectedRows,
                    onChange: personOnSelect,
                    columnWidth:20
                   }"
                 ref="personTable">
        </a-table>
      </a-spin>
    </a-modal>
  </div>
</template>

<script>
import {mapGetters} from "vuex";
import { getFolderByFolderNo, getTestPerson } from "@/api/modular/system/limsManager";
import { executeAlterTesterOrPlanTime, executeAlterYfTester } from "@/api/modular/system/testProgressManager";
export default {
  name: "AlterYfTesterModal",
  props: {
    modalData: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      labelCol: {
        sm: {
          span: 5
        }
      },
      wrapperCol: {
        sm: {
          span: 18
        }
      },
      personIsLoading: false,
      personColumns: [
        {
          title: '序号',
          dataIndex: 'index',
          align: 'center',
          width: 30,
          ellipsis: true,
          customRender: (text, record, index) => `${index + 1}`
        },
        {
          title: '工号',
          dataIndex: 'ID',
          align: 'center',
          width: 40,
        },
        {
          title: '姓名',
          dataIndex: 'USERNAME',
          align: 'center',
          width: 40,
        },
        {
          title: '部门',
          dataIndex: 'ORGNAME',
          align: 'center',
          width: 50,
        },
        {
          title: '在测测试项目数量',
          dataIndex: 'ORDTASKNUMBER',
          align: 'center',
          width: 50,
          scopedSlots: { customRender: 'ORDTASKNUMBER' },
        },
        {
          title: '在测样品数量',
          dataIndex: 'ORDERNUMBER',
          align: 'center',
          width: 50,
        },
        {
          title: '在测委托单数量',
          dataIndex: 'FOLDERNUMBER',
          align: 'center',
          width: 50,
        },
      ],
      personResultData: [],
      allPersonResultData: [],
      personSelectedRows: [],
      personSelectedRowKeys: [],
      searchTesterParam: {},
      tester: null
    }
  },
  created() {
      this.personLoadData()
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  mounted() {
  },
  methods: {
    personLoadData() {
      getFolderByFolderNo({folderno: this.modalData.folderNo}).then((res) => {
        if (res.data && res.data.length > 0) {
          let orgId = res.data[0].laboratoryid
          return getTestPerson({ status: "deprecated", orgId: orgId, roleId: "699586598579968" }).then((res) => {
            this.personResultData = res.data
            this.allPersonResultData = JSON.parse(JSON.stringify(res.data)) // 深克隆
          })
        }
      })
    },
    searchTesters() {
      if (this.searchTesterParam.ID) {
        this.personResultData = this.allPersonResultData.filter(item => item.ID.includes(this.searchTesterParam.ID))
      }
      if (this.searchTesterParam.USERNAME) {
        this.personResultData = this.allPersonResultData.filter(item => item.USERNAME.includes(this.searchTesterParam.USERNAME))
      }
      if (!this.searchTesterParam.ID && !this.searchTesterParam.USERNAME) {
        this.personResultData = this.allPersonResultData
      }
    },
    personHandleCancel() {
      this.$emit("cancel")
    },
    personOnSelect(selectedRowKeys, selectedRows) {
      this.personSelectedRows = selectedRows
      this.personSelectedRowKeys = selectedRowKeys
    },
    personSubmit() {
      if (this.personSelectedRows.length === 0) {
        this.$message.warning('请选择一人进行任务分配！')
      } else {
        let person = {
          userId: this.personSelectedRows[0].ID,
          userName: this.personSelectedRows[0].USERNAME,
        }
        this.executeAlterTester(person)
      }
    },
    executeAlterTester(person) {
      this.personIsLoading = true
      let progressDetailId = this.modalData.ordTaskId
      executeAlterYfTester({id:this.modalData.id ,progressDetailId:progressDetailId, person:person}).then((res) => {
        if (res.success === true) {
          setTimeout(() => {
            this.clearOldData()
            this.$message.success('更改测试人成功！')
          }, 200)
        } else {
          this.$message.warning('更改测试人失败：' + res.message)
          this.personIsLoading = false
        }
      })
    },
    clearOldData() {
      this.personSelectedRows = []
      this.personSelectedRowKeys = []
      this.personIsLoading = false
      this.$emit("cancel")
    },
  }
}
</script>

<style scoped>
/deep/ .ant-table-thead > tr > th {
  text-align: center;
  padding: 5px!important;
  font-size: 14px!important;
}
/deep/ .ant-table-tbody > tr > td {
  padding: 0px!important;
  height: 32px!important;
  font-size: 12px!important;
}

/deep/ .ant-modal-header {
   padding: 16px 24px 0;
   border: none;
 }
/deep/ .ant-modal-body {
  padding: 16px;
}
/deep/ .ant-modal-footer {
  padding: 0 24px 16px ;
}
</style>