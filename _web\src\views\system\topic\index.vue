<template>
<div style="padding-top:3px">
    <div class="head_title">研发技术课题管理平台</div>
    <div class="main">
        
        <div class="left">
            <div class="top">
                <div class="item" style="position:relative">
                    <div class="head"><div class="left"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 iKfgJk svg-icon-path-icon fill" viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path fill-rule="evenodd" clip-rule="evenodd" d="M19 10C19 12.2091 17.2091 14 15 14C12.7909 14 11 12.2091 11 10C11 7.79086 12.7909 6 15 6C17.2091 6 19 7.79086 19 10ZM15 28C17.2091 28 19 26.2091 19 24C19 21.7909 17.2091 20 15 20C12.7909 20 11 21.7909 11 24C11 26.2091 12.7909 28 15 28ZM15 42C17.2091 42 19 40.2091 19 38C19 35.7909 17.2091 34 15 34C12.7909 34 11 35.7909 11 38C11 40.2091 12.7909 42 15 42Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M37 10C37 12.2091 35.2091 14 33 14C30.7909 14 29 12.2091 29 10C29 7.79086 30.7909 6 33 6C35.2091 6 37 7.79086 37 10ZM33 28C35.2091 28 37 26.2091 37 24C37 21.7909 35.2091 20 33 20C30.7909 20 29 21.7909 29 24C29 26.2091 30.7909 28 33 28ZM33 42C35.2091 42 37 40.2091 37 38C37 35.7909 35.2091 34 33 34C30.7909 34 29 35.7909 29 38C29 40.2091 30.7909 42 33 42Z" fill="#333"></path></g></svg><span>立项通过率</span></div></div>
                    <div class="date">
                        <a-range-picker
                            size='small'
                            :placeholder="['开始月份', '结束月份']"
                            v-model="value"
                            :mode="['month', 'month']"
                            format="YYYY-MM"
                            @panelChange="handlePanelChange"
                            @openChange="handleOpenChange"
                            :open="monthPickShow"
                        >
                            <a-icon slot="suffixIcon" type="calendar" style="color:#d9d9d9" />
                        </a-range-picker>
                    </div>
                    <a-spin :spinning="assessLoading">
                        <div class="chart_table" :style="{height:(clientHeight/2-85) + 'px'}" ref="assess"></div>
                    </a-spin>
                </div>
            
                <div class="item">
                    <div class="head"><div class="left"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 iKfgJk svg-icon-path-icon fill" viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path fill-rule="evenodd" clip-rule="evenodd" d="M19 10C19 12.2091 17.2091 14 15 14C12.7909 14 11 12.2091 11 10C11 7.79086 12.7909 6 15 6C17.2091 6 19 7.79086 19 10ZM15 28C17.2091 28 19 26.2091 19 24C19 21.7909 17.2091 20 15 20C12.7909 20 11 21.7909 11 24C11 26.2091 12.7909 28 15 28ZM15 42C17.2091 42 19 40.2091 19 38C19 35.7909 17.2091 34 15 34C12.7909 34 11 35.7909 11 38C11 40.2091 12.7909 42 15 42Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M37 10C37 12.2091 35.2091 14 33 14C30.7909 14 29 12.2091 29 10C29 7.79086 30.7909 6 33 6C35.2091 6 37 7.79086 37 10ZM33 28C35.2091 28 37 26.2091 37 24C37 21.7909 35.2091 20 33 20C30.7909 20 29 21.7909 29 24C29 26.2091 30.7909 28 33 28ZM33 42C35.2091 42 37 40.2091 37 38C37 35.7909 35.2091 34 33 34C30.7909 34 29 35.7909 29 38C29 40.2091 30.7909 42 33 42Z" fill="#333"></path></g></svg><span>课题类别</span></div></div>
                    <a-spin :spinning="chartLoading">
                        <div class="chart_table" :style="{height:(clientHeight/2-85) + 'px'}" ref="classic"></div>
                    </a-spin>
                </div>
                <div class="item">
                    <div class="head"><div class="left"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 iKfgJk svg-icon-path-icon fill" viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path fill-rule="evenodd" clip-rule="evenodd" d="M19 10C19 12.2091 17.2091 14 15 14C12.7909 14 11 12.2091 11 10C11 7.79086 12.7909 6 15 6C17.2091 6 19 7.79086 19 10ZM15 28C17.2091 28 19 26.2091 19 24C19 21.7909 17.2091 20 15 20C12.7909 20 11 21.7909 11 24C11 26.2091 12.7909 28 15 28ZM15 42C17.2091 42 19 40.2091 19 38C19 35.7909 17.2091 34 15 34C12.7909 34 11 35.7909 11 38C11 40.2091 12.7909 42 15 42Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M37 10C37 12.2091 35.2091 14 33 14C30.7909 14 29 12.2091 29 10C29 7.79086 30.7909 6 33 6C35.2091 6 37 7.79086 37 10ZM33 28C35.2091 28 37 26.2091 37 24C37 21.7909 35.2091 20 33 20C30.7909 20 29 21.7909 29 24C29 26.2091 30.7909 28 33 28ZM33 42C35.2091 42 37 40.2091 37 38C37 35.7909 35.2091 34 33 34C30.7909 34 29 35.7909 29 38C29 40.2091 30.7909 42 33 42Z" fill="#333"></path></g></svg><span>课题等级</span></div></div>
                    <a-spin :spinning="chartLoading">
                        <div class="chart_table" :style="{height:(clientHeight/2-85) + 'px'}" ref="level"></div>
                    </a-spin>
                </div>
                <div class="item">
                    <div class="head"><div class="left"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 iKfgJk svg-icon-path-icon fill" viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path fill-rule="evenodd" clip-rule="evenodd" d="M19 10C19 12.2091 17.2091 14 15 14C12.7909 14 11 12.2091 11 10C11 7.79086 12.7909 6 15 6C17.2091 6 19 7.79086 19 10ZM15 28C17.2091 28 19 26.2091 19 24C19 21.7909 17.2091 20 15 20C12.7909 20 11 21.7909 11 24C11 26.2091 12.7909 28 15 28ZM15 42C17.2091 42 19 40.2091 19 38C19 35.7909 17.2091 34 15 34C12.7909 34 11 35.7909 11 38C11 40.2091 12.7909 42 15 42Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M37 10C37 12.2091 35.2091 14 33 14C30.7909 14 29 12.2091 29 10C29 7.79086 30.7909 6 33 6C35.2091 6 37 7.79086 37 10ZM33 28C35.2091 28 37 26.2091 37 24C37 21.7909 35.2091 20 33 20C30.7909 20 29 21.7909 29 24C29 26.2091 30.7909 28 33 28ZM33 42C35.2091 42 37 40.2091 37 38C37 35.7909 35.2091 34 33 34C30.7909 34 29 35.7909 29 38C29 40.2091 30.7909 42 33 42Z" fill="#333"></path></g></svg><span>课题进展</span></div></div>
                    <a-spin :spinning="chartLoading">
                        <div class="chart_table" :style="{height:(clientHeight/2-85) + 'px'}" ref="evo"></div>
                    </a-spin>
                </div>
            </div>
            <div class="bottom">
                <div class="item">
                    <div class="head"><div class="left"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 iKfgJk svg-icon-path-icon fill" viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path fill-rule="evenodd" clip-rule="evenodd" d="M19 10C19 12.2091 17.2091 14 15 14C12.7909 14 11 12.2091 11 10C11 7.79086 12.7909 6 15 6C17.2091 6 19 7.79086 19 10ZM15 28C17.2091 28 19 26.2091 19 24C19 21.7909 17.2091 20 15 20C12.7909 20 11 21.7909 11 24C11 26.2091 12.7909 28 15 28ZM15 42C17.2091 42 19 40.2091 19 38C19 35.7909 17.2091 34 15 34C12.7909 34 11 35.7909 11 38C11 40.2091 12.7909 42 15 42Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M37 10C37 12.2091 35.2091 14 33 14C30.7909 14 29 12.2091 29 10C29 7.79086 30.7909 6 33 6C35.2091 6 37 7.79086 37 10ZM33 28C35.2091 28 37 26.2091 37 24C37 21.7909 35.2091 20 33 20C30.7909 20 29 21.7909 29 24C29 26.2091 30.7909 28 33 28ZM33 42C35.2091 42 37 40.2091 37 38C37 35.7909 35.2091 34 33 34C30.7909 34 29 35.7909 29 38C29 40.2091 30.7909 42 33 42Z" fill="#333"></path></g></svg><span>成果转化率</span></div></div>
                    <a-spin :spinning="chartLoading">
                        <div class="chart_table" :style="{height:(clientHeight/2-85) + 'px'}" ref="gain"></div>
                    </a-spin>
                </div>
            </div>
        </div>
        <div class="right">
            <div class="item">
                <div class="head"><div class="left"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 iKfgJk svg-icon-path-icon fill" viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path fill-rule="evenodd" clip-rule="evenodd" d="M19 10C19 12.2091 17.2091 14 15 14C12.7909 14 11 12.2091 11 10C11 7.79086 12.7909 6 15 6C17.2091 6 19 7.79086 19 10ZM15 28C17.2091 28 19 26.2091 19 24C19 21.7909 17.2091 20 15 20C12.7909 20 11 21.7909 11 24C11 26.2091 12.7909 28 15 28ZM15 42C17.2091 42 19 40.2091 19 38C19 35.7909 17.2091 34 15 34C12.7909 34 11 35.7909 11 38C11 40.2091 12.7909 42 15 42Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M37 10C37 12.2091 35.2091 14 33 14C30.7909 14 29 12.2091 29 10C29 7.79086 30.7909 6 33 6C35.2091 6 37 7.79086 37 10ZM33 28C35.2091 28 37 26.2091 37 24C37 21.7909 35.2091 20 33 20C30.7909 20 29 21.7909 29 24C29 26.2091 30.7909 28 33 28ZM33 42C35.2091 42 37 40.2091 37 38C37 35.7909 35.2091 34 33 34C30.7909 34 29 35.7909 29 38C29 40.2091 30.7909 42 33 42Z" fill="#333"></path></g></svg><span>人均课题数</span></div></div>
                <a-spin :spinning="chartLoading">
                    <div class="chart_table" :style="{height:(clientHeight-127) + 'px'}" ref="capita"></div>
                </a-spin>
            </div>
        </div>
    </div>
</div>
</template>

<script>
import moment from 'moment';
import {getChartProjects,getChartPassTopics} from "@/api/modular/system/topic"
import { ALL_APPS_MENU } from '@/store/mutation-types'
import Vue from 'vue'
import { mapActions } from 'vuex'
export default {
    data(){
        return {
            assessLoading:false,
            chartLoading:false,
            monthPickShow: false,
            value: [moment().subtract(1, 'year').startOf('year'),moment()],
            clientHeight:document.documentElement.clientHeight,

            topicClassic:{},
            topicResult:{},
            topicConclue:{},
            topicPasses:{},
            topicCurMonths:{},
            topicLevel:{},
            topicEvo:{},

            deptMap:{},
            cateMap:{}
        }
    },
    methods:{
        moment,
        handlePanelChange(value, mode) {
            if (this.value[1] && this.value[1]._d != value[1]._d) {
                this.value = value
                this.monthPickShow = false;
                this.callChartTopicsPass()
                this.callChartProjects()
            }
            this.value = value
        },
        handleOpenChange(status) {
            if(status){
                this.monthPickShow = true;
            }else{
                this.monthPickShow = false
            }
        },
        ...mapActions(['MenuChange']),
        switchApp() {
            const applicationData = Vue.ls.get(ALL_APPS_MENU)
            this.MenuChange(applicationData[0]).then((res) => {
            }).catch((err) => {
                this.$message.error('应用切换异常')
            })
        },

        /**
         * echarts 图表
         */
        initTopicLevel() {
            let chart = this.echarts.init(this.$refs.level)
            chart.off("click")
            let datas = []
            
            for (const key in this.topicLevel) {
            datas.push({
                name:key,
                value:parseInt(this.topicLevel[key])
            })
            }
            chart.clear()
            const options = {
            tooltip: {
                trigger: 'item'
            },
            legend: {
                show: true,
                itemWidth: 8,
                itemHeight: 8,
                y:'bottom', 
                x:'center',
                textStyle: {
                    fontSize: 10,
                },
            },
            color: ['#ed7d31', '#70ad47',  '#5b9bd5'],
            grid: {
                containLabel: true
            },
            series: [{
                type: 'pie',
                minAngle:10,
                radius: ['40%', '60%'],
                center: ['50%', '48%'],
                itemStyle: {
                    borderRadius: 0,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: true,
                    position: 'center',
                    color:'#000',
                    formatter: `等级`,
                    fontSize: '15',
                    lineHeight: 30,
                    fontWeight:'bold'
                },
                labelLine: {
                    show: false,
                },
                data: datas
                },{
                type: 'pie',
                minAngle:10,
                radius: ['40%', '60%'],
                center: ['50%', '48%'],
                itemStyle: {
                    borderRadius: 0,
                    borderColor: '#fff',
                    borderWidth: 2
                },

                label: {
                    show: true,
                    color: "#000",
                    fontSize: 8,
                    formatter: function(data){
                        return `${data.name}\n${data.percent.toFixed(1)}%`
                    }
                    ,
                },
                tooltip:{
                    formatter: function(data){
                    return  `<span style="display:inline-block;margin-right:6px;border-radius:10px;width:10px;height:10px;background-color:${data.color};"></span>
                        ${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
                    }
                },
                labelLine: {
                    show: true,
                    length:15
                },
                data: datas
                },
            ]
            }
            chart.setOption(options)
            let that = this
            chart.on('click', function(params) {
                let levels = ['S','A','B']
                that.$router.push({
                    path:'/project_level',///project_gain
                    query:{
                        startDate:moment(that.value[0]._d).format('YYYY-MM'),
                        endDate:moment(that.value[1]._d).format('YYYY-MM'),
                    }
                    /* query:{
                        level:levels[params.dataIndex]
                    } */
                })
            });
            chart.resize()
            this.classicspinning = false
        },
        initTopicEvo(){
            let chart = this.echarts.init(this.$refs.evo)
            chart.off("click")
            let datas = []
            
            for (const key in this.topicEvo) {
            datas.push({
                name:key,
                value:parseInt(this.topicEvo[key])
            })
            }

            chart.clear()
            const options = {
            tooltip: {
                trigger: 'item'
            },
            legend: {
                show: true,
                itemWidth: 8,
                itemHeight: 8,
                y:'bottom', 
                x:'center',
                textStyle: {
                    fontSize: 10,
                },
            },
            color: ['#5b9bd5', '#ed7d31', '#a5a5a5','#70ad47'],
            grid: {
                containLabel: true
            },
            series: [{
                type: 'pie',
                minAngle:10,
                radius: ['40%', '60%'],
                center: ['50%', '48%'],
                itemStyle: {
                    borderRadius: 0,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: true,
                    position: 'center',
                    color:'#000',
                    formatter: `进展`,
                    fontSize: '15',
                    lineHeight: 30,
                    fontWeight:'bold'
                },
                labelLine: {
                    show: false,
                },
                data: datas
                },{
                type: 'pie',
                minAngle:10,
                radius: ['40%', '60%'],
                center: ['50%', '48%'],
                itemStyle: {
                    borderRadius: 0,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: true,
                    //offset: [0, -5],
                    color: "#000",
                    fontSize: 8,
                    formatter: function(data){
                        return `${data.name}\n${data.percent.toFixed(1)}%`
                    }
                    ,
                },
                tooltip:{
                    formatter: function(data){
                    return  `<span style="display:inline-block;margin-right:6px;border-radius:10px;width:10px;height:10px;background-color:${data.color};"></span>
                        ${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
                    }
                },
                labelLine: {
                    show: true,
                    length:6
                },
                data: datas
                },
            ]
            }
            chart.setOption(options)
            let that = this
            chart.on('click', function(params) {
                //let status = [1,2,3,4]
                that.$router.push({
                    path:'/project_evo',///project_gain
                    query:{
                        startDate:moment(that.value[0]._d).format('YYYY-MM'),
                        endDate:moment(that.value[1]._d).format('YYYY-MM'),
                    }
                    /* query:{
                        status:status[params.dataIndex]
                    } */
                })
            });
            chart.resize()
        },
        initTopicClassic() {
            let chart = this.echarts.init(this.$refs.classic)
            chart.off("click")
            let datas = []
            let keys = ['成本类', '质保类' ,'功能类', '质量改善类', '标准化类', '知识产权类']
            for (const key of keys) {
            datas.push({
                name:key,
                value:parseInt(this.topicClassic[key])
            })
            }
            let sum = datas.reduce((sum, e) => sum + Number(e.value || 0), 0)

            chart.clear()
            let that = this
            const options = {
            tooltip: {
                trigger: 'item'
            },
            legend: {
                show: true,
                itemWidth: 8,
                itemHeight: 8,
                y:'bottom', 
                x:'center',
                textStyle: {
                    fontSize: 10,
                },
                /* formatter: function(data){
                    return `${data}`
                }, */
            },
            color: ['#5b9bd5', '#ed7d31',  '#a5a5a5', '#ffc000', '#4472c4','#70ad47'],
            grid: {
            },
            series: [{
                type: 'pie',
                minAngle:10,
                radius: ['40%', '60%'],
                center: ['50%', '48%'],
                itemStyle: {
                    borderRadius: 0,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: true,
                    position: 'center',
                    color:'#000',
                    formatter: `立项总数\n${sum}`,
                    fontSize: '15',
                    lineHeight: 30,
                    fontWeight:'bold'
                },
                labelLine: {
                    show: false,
                    length:0.0001
                },
                data: datas
                },{
                type: 'pie',
                minAngle:10,
                radius: ['40%', '60%'],
                center: ['50%', '48%'],
                itemStyle: {
                    borderRadius: 0,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: false,
                    formatter: function(data){
                    if (data.percent<1) {
                        return `${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
                    }else{
                        return ''
                    }
                    
                    }
                    ,
                },
                tooltip:{
                    formatter: function(data){
                    return  `<span style="display:inline-block;margin-right:6px;border-radius:10px;width:10px;height:10px;background-color:${data.color};"></span>
                        ${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
                    }
                },
                labelLine: {
                    show: true,
                    length:0.0001
                },
                data: datas
                },{
                type: 'pie',
                minAngle:10,
                radius: ['40%', '60%'],
                center: ['50%', '48%'],
                itemStyle: {
                    borderRadius: 0,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: false,
                    formatter: function(data){
                    return `${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
                    }
                    ,
                },
                tooltip:{
                    formatter: function(data){
                    return  `<span style="display:inline-block;margin-right:6px;border-radius:10px;width:10px;height:10px;background-color:${data.color};"></span>
                        ${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
                    }
                },
                labelLine: {
                    show: false,
                    length:0.0001
                },
                data: datas
                },
            ]
            }
            chart.setOption(options)
            chart.on('click', function(params) {
                that.$router.push({
                    path:'/project_class',///project_gain
                    query:{
                        cateId:that.cateMap[params.name],
                        startDate:moment(that.value[0]._d).format('YYYY-MM'),
                        endDate:moment(that.value[1]._d).format('YYYY-MM'),
                    }
                })
            });
            chart.resize()
            this.classicspinning = false
        },
        initGain() {
            let chart = this.echarts.init(this.$refs.gain)
            chart.clear()
            let _source = []
            _source.push(['product', '成果', '结题项目','转化率'])

            let passCount = 0
            let resultCount = 0

            let that = this

            for (const key in this.topicResult) {
                /* let index = key.indexOf('研究所') > -1 ? key.indexOf('研究所') : key.indexOf('中心')
                let tmpsuffx = key.indexOf('研究所') > -1 ? '研究所' : '中心' //key.substring(0,index)+`\n`+tmpsuffx */
              let _tmp = [key, this.topicResult[key], this.topicConclue[key], (this.topicConclue[key] == 0 ? 0 : (this.topicResult[key] / this.topicConclue[key]) * 100).toFixed(1)];
                _source.push(_tmp)
                passCount += this.topicPasses[key]
                resultCount += this.topicResult[key]
            }

            this.avgResult = ((resultCount/passCount)*100).toFixed(1)

            const options = {
                    grid:{
                        show:false,
                        top:'11%',
                        bottom:'18%',
                        left:'8%',
                        right:'8%'
                    },
                    color:['#89d77f','#f7e771'],
                    legend: {
                        show: true,
                        itemWidth: 8,
                        itemHeight: 8,
                        y:'bottom',
                        textStyle: {
                          color:"#000",
                            fontSize: 10,
                        },
                    },
                    tooltip: {},
                    dataset: {
                        source:_source 
                    },
                    xAxis: { 
                        type: 'category',
                        axisLabel: {
                          showMinLabel:true,
                          interval: 0,
                          textStyle:{
                            color:"#000",
                            fontSize:'10',
                          }
                        },
                    },
                    yAxis: [{
                        type: 'value',
                        splitLine:{show: false},
                      axisLabel:{
                        textStyle:{
                          color:"#000",
                          fontSize:'10',
                        }
                      }
                    },
                    {
                        type: 'value',
                        splitLine:{show: false},
                        axisLabel: {
                        formatter: (value, index) => {
                            return value+"%";
                        },
                          textStyle:{
                            color:"#000",
                            fontSize:'10',
                          }
                        }
                    }],
                    series: [
                        {
                            type: 'bar',
                            barWidth:20,
                            itemStyle: {
                                normal: {
                                    color: "#70ad47", //柱状颜色
                                    label: {
                                    show: false, //是否开启数值显示
                                    position: "top", //在上方显示
                                    textStyle: {
                                        color: "#000",
                                        fontSize: 8,
                                    },
                                    },
                                },
                            },
                            tooltip: {
                              formatter: function (data) {
                                return (
                                  data.dimensionNames[1] +
                                  `<br/><span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${data.color};"></span>` +
                                  data.value[0] + "：" + data.value[1]
                                );
                              },
                            },
                        }, 
                        {
                            type: 'bar',
                            barWidth:20,
                            itemStyle: {
                                normal: {
                                    color: "#5b9bd5",
                                    label: {
                                    show: false,
                                    position: "top",
                                    textStyle: {
                                        color: "#000",
                                        fontSize: 12,
                                    },
                                    },
                                },
                            },
                          tooltip: {
                            formatter: function (data) {
                              return (
                                data.dimensionNames[2] +
                                `<br/><span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${data.color};"></span>` +
                                data.value[0] + "：" + data.value[2]
                              );
                            },
                          },
                        },
                        {
                            type: 'line',
                            yAxisIndex: 1,
                            itemStyle: {
                                normal: {
                                    color: "#ed8137",
                                    label: {
                                    show: true,
                                    position: "top",
                                    formatter: (value, index) => {
                                        return value.data[3]+"%";
                                    },
                                    textStyle: {
                                        color: "#000",
                                        fontSize: '8',
                                    },
                                    },
                                },
                            },
                          tooltip: {
                            formatter: function (data) {
                              return (
                                data.dimensionNames[3] +
                                `<br/><span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${data.color};"></span>` +
                                data.value[0] + "：" + data.value[3] + "%"
                              );
                            },
                          },
                        }
                    ]
            };
            chart.setOption(options)
            chart.on('click', function(params) {
                console.log(params)
                if (params.seriesIndex == 2) {
                    return
                }
                let $query = null
                //that.switchApp()
                if (params.seriesIndex == 0) {
                    $query = {
                        deptId:that.deptMap[params.name],
                        startDate:moment(that.value[0]._d).format('YYYY-MM'),
                        endDate:moment(that.value[1]._d).format('YYYY-MM'),
                    }
                }else{
                    $query = {
                        deptId:that.deptMap[params.name],
                        statuses:'5',
                        startDate:moment(that.value[0]._d).format('YYYY-MM'),
                        endDate:moment(that.value[1]._d).format('YYYY-MM'),
                    }
                }
                that.$router.push({
                    path: params.seriesIndex == 0 ? '/project_gain' : 'project_class',
                    query:$query
                })
            });
            chart.resize()
        },
        initCapita() {
            /*let data = str.sort(function (a, b) {
                return b.age - a.age;
            }); */
            let chart = this.echarts.init(this.$refs.capita)
            chart.clear()
            let dataAxis = []
            let datas = []
            let _datas = []
            let people_sum = 0
            let project_sum = 0
            for (const key in this.topicLevelsZ) {
                //dataAxis.push(key)

                if (parseInt(this.topicLevelsAvg[key]) == 0) {
                    _datas.push({
                        label:key,
                        num:0
                    })
                }else{
                    _datas.push({
                        label:key,
                        num:(this.topicLevelsZ[key]/this.topicLevelsAvg[key]).toFixed(1)
                    })
                }
                people_sum += this.topicLevelsAvg[key]
                project_sum += this.topicLevelsZ[key]
            }

            let $data = _datas.sort(function (a, b) {
                return b.num - a.num;
            });

            for (const item of $data) {
                dataAxis.push(item.label)
                datas.push(item.num)
            }

            this.avgCount = (project_sum/people_sum).toFixed(1)

            let topicLevel = this.topicLevelsAvg
            let topicpass = this.topicLevelsZ
            
            //topicLevel.reverse()
            //topicpass.reverse()
            dataAxis.reverse()
            datas.reverse()

            const options = {
            grid:{
                show:false,
                left:'28%',
                top:'6%',
                bottom:'6%'
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                type: 'shadow'
                },formatter: function (params) {
                
                return (
                    params[0].name +
                    `<br/><span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:#4472c4;"></span>` +
                    "部门人数："+topicLevel[params[0].name]
                    +`<br/><span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:#4472c4;"></span>`
                    +'立项通过数：'+topicpass[params[0].name]
                    
                    );
                },
            },
                legend: {
                    show:false,
                    data:['人均技术课题数'],
                    textStyle: {
                      color: "#000",
                      fontSize: 10,
                    },
                },
                xAxis: {
                    type: 'value',
                    show: false,
                    splitLine:{show: false}
                    
                },
                yAxis: {
                    type: 'category',
                    axisLabel: {
                      interval: 0,
                      textStyle:{
                        color: "#000",
                        fontSize:'10'
                      }
                    },
                    data: dataAxis,
                    
                },
                series: [
                    {   
                        name:'人均技术课题数',
                        data: datas,
                        barWidth:13,
                        type: 'bar',
                        itemStyle: {
                                normal: {
                                    color: "#4472c4",
                                    barBorderRadius :15,
                                    label: {
                                        show: true, //开启数值显示
                                        position: "right", //在上方显示
                                        formatter:function(params) {
                                            if (params.value > 0) {
                                                return params.value
                                            }else{
                                                return ''
                                            }
                                        },
                                        textStyle: {
                                            color: "#4472c4",
                                            fontSize: '10',
                                        },
                                    },
                                },
                        },
                    }
                ]
            }
            chart.setOption(options) ///project_captia
            let that = this
            chart.on('click', function(params) {
                //that.switchApp()
                that.$router.push({
                    path:'/project_captia',
                    query:{
                        deptId:that.deptMap[params.name],
                        startDate:moment(that.value[0]._d).format('YYYY-MM'),
                        endDate:moment(that.value[1]._d).format('YYYY-MM'),
                    }
                })
            });
            chart.resize()
        },
        initAssess() {
            let chart = this.echarts.init(this.$refs.assess)
            chart.off("click")
            let datas = []
            for (const key in this.topicCurMonths) {
                datas.push({
                    name:key,
                    value:parseInt(this.topicCurMonths[key])
                })
            }
            let sum = datas.reduce((sum, e) => sum + Number(e.value || 0), 0)
            let divide = ((this.topicCurMonths['通过']/sum)*100).toFixed(1)+'%'
            chart.clear()
            const options = {
            tooltip: {
                trigger: 'item'
            },
            legend: {
                show: true,
                itemWidth: 8,
                itemHeight: 8,
                y:'bottom', 
                x:'center',
                textStyle: {
                    fontSize: 10,
                },
                data:['通过','不通过']
            },
            color: [ '#ed7d31','#5b9bd5'],
            grid: {
            },
            series: [{
                type: 'pie',
                minAngle:10,
                radius: ['40%', '60%'],
                center: ['50%', '48%'],
                itemStyle: {
                    borderRadius: 0,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: true,
                    position: 'center',
                    color:'#000',
                    formatter: `立项通过率\n${divide}`,
                    fontSize: '15',
                    lineHeight: 30,
                    fontWeight:'bold'
                },
                labelLine: {
                    show: false
                },
                data: datas
                },{
                type: 'pie',
                minAngle:10,
                radius: ['40%', '60%'],
                center: ['50%', '48%'],
                itemStyle: {
                    borderRadius: 0,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: false,
                    formatter: function(data){
                        if (data.percent<1) {
                            return `${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
                        }else{
                            return ''
                        }
                    },
                },
                tooltip:{
                    formatter: function(data){
                    return  `<span style="display:inline-block;margin-right:6px;border-radius:10px;width:10px;height:10px;background-color:${data.color};"></span>
                        ${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
                    }
                },
                labelLine: {
                    show: true,
                    length:0.0001
                },
                data: datas
                },{
                type: 'pie',
                minAngle:10,
                radius: ['40%', '60%'],
                center: ['50%', '48%'],
                itemStyle: {
                    borderRadius: 0,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: false,
                    formatter: function(data){
                    return `${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
                    }
                    ,
                },
                tooltip:{
                    formatter: function(data){
                    return  `<span style="display:inline-block;margin-right:6px;border-radius:10px;width:10px;height:10px;background-color:${data.color};"></span>
                        ${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
                    }
                },
                labelLine: {
                    show: false
                },
                data: datas
                },
            ]
            }
            chart.setOption(options)
            let that = this
            chart.on('click', function(params) {
                let status = [1,2]
                that.$router.push({
                    path:'/project_pass',
                    query: {
                        startDate:moment(that.value[0]._d).format('YYYY-MM'),
                        endDate:moment(that.value[1]._d).format('YYYY-MM'),
                        //status:status[params.dataIndex]
                    },
                })
            });
            chart.resize()
        },
        callChartTopicsPass() {
            this.assessLoading = true
            getChartPassTopics({
                startDate:moment(this.value[0]._d).format('YYYY-MM'),
                endDate:moment(this.value[1]._d).format('YYYY-MM'),
            }).then((res) => {
                if (res.success) {
                    this.topicCurMonths = res.data.cur ? res.data.cur : {}
                    this.$nextTick(()=>{
                        this.initAssess()
                    })
                } else {
                    this.$message.error('错误提示：' + res.message,1)
                }
                this.assessLoading = false
            })
            .catch((err) => {
                this.$message.error('错误提示：' + err.message,1)
                this.assessLoading = false
            });
        },
        callChartProjects() {
            this.chartLoading = true
            getChartProjects({
                startDate:moment(this.value[0]._d).format('YYYY-MM'),
                endDate:moment(this.value[1]._d).format('YYYY-MM'),
            }).then((res) => {
                if (res.success) {
                    this.topicClassic = res.data.cateCount ? res.data.cateCount : {}
                    this.topicResult = res.data.result ? res.data.result : {}
                    this.topicConclue = res.data.conclue ? res.data.conclue : {}
                    this.topicPasses = res.data.pass ? res.data.pass : {}
                    this.topicLevelsZ = res.data.Z ? res.data.Z : {}
                    this.topicLevelsAvg = res.data.avg ? res.data.avg : {}
                    this.topicLevel = res.data.level ? res.data.level : {}
                    this.topicEvo = res.data.evo ? res.data.evo : {}
                    this.deptMap = res.data.deptmap ? res.data.deptmap : {}
                    this.cateMap = res.data.catemap ? res.data.catemap : {}
                    
                    this.$nextTick(()=>{
                        this.initTopicClassic()
                        this.initTopicLevel()
                        this.initGain()
                        this.initTopicEvo()
                        this.initCapita()
                    })
                } else {
                    this.$message.error('错误提示：' + res.message,1)
                }
                this.chartLoading = false
            }).catch((err) => {
                this.chartLoading = false
                this.$message.error('错误提示：' + err.message,1)
        });
      },
    },
    mounted() {
      this.callChartProjects()
      this.callChartTopicsPass()
    },
}
</script>

<style lang="less" scoped=''>
.main{
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    height: 100%;
    
}
.left{
    flex: 1;
    height: 100%;
    margin-top: 8px;
    margin-left: 5px;
}
.left .top{
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
}
.left .item{
    flex: 1;
    margin-right: 5px;
    background: #fff;
    //box-shadow: 2px 2px 3px #ccc;
}

.left .top .item{
    border-top: 8px solid rgb(92 129 203);
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}
.left .bottom .item{
    margin-top: 8px;
}

.left .chart_table{
    min-height: 200px;
}
.right{
    margin-top: 8px;
    width: 20%;
    height: 100%;
}

.right .item{
    background: #fff;
    border-top: 8px solid rgb(92 129 203);
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    //box-shadow: 2px 2px 3px #ccc;
}
.right .chart_table{
    min-height: 450px;
}


.head {
    color: #000;
    font-size: 16px;
    border-bottom: 1px solid #e5e5e5;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.head svg{
  margin-right:2px ;
}
.head .right{
  font-weight: initial;
  font-size: 13px;
  float: right;
}
.head .left{
  display: flex;
  align-items: center;
  margin: 0;
}

.date{
    position: absolute;
    top: 36px;
    left:0;
    width: 140px;
    z-index: 1;
}
/deep/.ant-input{
    border: none;
    border-bottom: 1px solid #e5e5e5;
}
/deep/.ant-input-sm {
    height: 24px;
    padding: 1px 0px;
}
/deep/.ant-calendar-range-picker-input{
    width: 40%;
    font-size: 12px;
    color: #000;
    text-align: left;
}
/deep/.ant-calendar-picker:hover .ant-calendar-picker-input:not(.ant-input-disabled){
    border-color: #e5e5e5;
}
.head_title{
    height: 30px;
    background: rgb(92 129 203);
    margin-left: 5px;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 18px;
}
</style>>