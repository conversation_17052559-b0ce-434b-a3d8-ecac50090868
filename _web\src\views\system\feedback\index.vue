<template>
    <div>
        <x-card>
            <div slot="content" class="table-page-search-wrapper">
                <a-form layout="inline">
                    <a-row :gutter="48">
                        <a-col :md="8" :sm="24">
                            <a-form-item label="分类">
                                <a-tree-select @change="this.change" v-model="queryParam.cateId" :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" :tree-data="cate" placeholder="请选择分类" tree-default-expand-all>
                                </a-tree-select>
                            </a-form-item>
                        </a-col>
                        <a-col :md="8" :sm="24">
                            <a-form-item label="产品名称">
                                <a-input v-model="queryParam.productProjectName" @keyup.enter.native="()=>callProjects()" />
                            </a-form-item>
                        </a-col>
                        <a-col :md="8" :sm="24">
                            <span class="table-page-search-submitButtons">
                                            <a-button type="primary" @click="callProjects" >查询</a-button>
                                            <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                                        </span>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
        </x-card>
            <a-table  @expand="onExpand" :expandedRowKeys.sync="expandedRowKeys" :scroll="{  y:windowHeight }" style="background: #fff;padding:12px;" ref="table" size="middle" :rowKey="(record) => record.productCateParent+record.productCate+record.issueKey+record.cateId" :pagination="false" :columns="columns" :dataSource="loadData" :loading="loading" showPagination="false" bordered>
                
                <span slot="fixedState" slot-scope="text, record">
                        <span v-if="record.issueId">
                            {{ 'fix_status' | dictType(text) }}
                        </span>
                        <span v-else></span>
                    </span>
                    <span slot="mstatus" slot-scope="text, record">
                        <span v-if="record.issueId">{{ 'product_stage_status' | dictType(text) }}</span>
                        <span v-else></span>
                    </span>
                    <span slot="state" slot-scope="text, record">
                        <span v-if="record.issueId">
                            {{ 'product_state_status' | dictType(text) }}
                        </span>
                        <span v-else></span>
                    </span>
                <!-- <div slot="expandedRowRender" slot-scope="record" style="margin: 0">
                    <p>{{ record.produceFeedback ? '制造反馈：'+ record.produceFeedback:'' }}</p>
                    <p>{{ record.sellFeedback ? '销售反馈：'+ record.sellFeedback:'' }}</p>
                    <p>{{ record.supplyFeedback ? '供应链反馈：'+ record.supplyFeedback:'' }}</p>
                </div> -->
                <!-- <div slot="produceFeedback" slot-scope="text,record">
                    <a  class="clickheadstyle" @click="$refs.addForm.add(record,1)">{{text}}</a>
                </div>
                <div slot="sellFeedback" slot-scope="text,record">
                    <a  class="clickheadstyle" @click="$refs.addForm.add(record,2)">{{text}}</a>
                </div>
                <div slot="supplyFeedback" slot-scope="text,record">
                    <a  class="clickheadstyle" @click="$refs.addForm.add(record,3)">{{text}}</a>
                </div> -->
                <div class="btns" slot="made" slot-scope="text, record">
                    <span v-if="record.issueId">
                        <a v-if="hasPerm('feedback:made')" @click="$refs.addForm.add(record,1)">填写</a>
                        <a v-if="hasPerm('feedback:madehistorys')" @click="$refs.historys.view(record,1)">查看</a>
                    </span>
                    <span v-else></span>
                </div>
                <div class="btns" slot="sale" slot-scope="text, record">
                    <span v-if="record.issueId">
                        <a v-if="hasPerm('feedback:sale')" @click="$refs.addForm.add(record,2)">填写</a>
                        <a v-if="hasPerm('feedback:salehistorys')" @click="$refs.historys.view(record,2)">查看</a>
                    </span>
                    <span v-else></span>
                </div>
                <div class="btns" slot="supply" slot-scope="text, record">
                    <span v-if="record.issueId">
                        <a v-if="hasPerm('feedback:supply')" @click="$refs.addForm.add(record,3)">填写</a>
                        <a v-if="hasPerm('feedback:supplyhistorys')" @click="$refs.historys.view(record,3)">查看</a>
                    </span>
                    <span v-else></span>    
                </div>
                <!-- <div slot="productCateOptionBeans" slot-scope="text, record">
                    <span v-for="(item,i) in record.productCateOptionBeans" :key="item.id">
                                        <div v-if="  i < record.productCateOptionBeans.length -1">
                                            {{ item.value }}
                                        </div>
                                        <div v-else>
                                            {{ item.value }}
                                        </div>
                                    </span>
                </div> -->
            </a-table>
        <add ref="addForm" @ok="handleOk" />
        <historys ref="historys"/>
    </div>
</template>

<script>
    import { TextTips } from '@/components'
    import add from './add'
    import historys from './historys'
    import {
        //getProjectHead,
        getProjects,
        getCatesTree
    } from "@/api/modular/system/report"
    import {
        XCard
    } from '@/components'
    export default {
        components: {
            XCard,
            add,
            historys,
            TextTips
        },
        data() {
            return {
                expandedRowKeys:[],
                windowHeight: document.documentElement.clientHeight - 205,
                loading: true,
                columns: [],
                loadData: [],
                queryParam: {},
                cate: []
            }
        },
        methods: {
            getExpandedRowKeys(list){
                list.forEach((item) => {
                    if (item.children && item.children.length) {
                        // 将所有children的父节点取出
                        this.expandedRowKeys.push(item.productCateParent+item.productCate+item.issueKey+item.cateId);
                        this.getExpandedRowKeys(item.children);
                    }
                });
            },
            onExpand(expanded, record){
                if (expanded) {
                    this.expandedRowKeys.push(record.productCateParent+record.productCate+record.issueKey+record.cateId);
                } else {
                    this.expandedRowKeys.splice(this.expandedRowKeys.indexOf(record.productCateParent+record.productCate+record.issueKey+record.cateId), 1);
                }
            },
            change(value, label, extra){
                this.callProjects()
            },
            handleOk() {
                this.loadData = []
                this.callProjects()
            },
            callGetTree() {
                this.loading = true
                getCatesTree().then((res) => {
                    if (res.result) {
                        this.cate = res.data
                    } else {
                        this.$message.error('错误提示：' + res.message, 1)
                    }
                    this.loading = false
                }).catch((err) => {
                    this.loading = false
                    this.$message.error('错误提示：' + err.message, 1)
                });
            },
            callProjects() {
                this.loading = true
                this.expandedRowKeys = []
                getProjects(this.queryParam).then((res) => {
                    if (res.result) {
                        /* let spanArr = ['productCateParent','productCate']
                        spanArr.forEach((item) => {
                            for (let i = 0, j = res.data.rows.length; i < j; i++) {
                                let rowSpan = 0;
                                let n = i;
                                while (
                                res.data.rows[n + 1] &&
                                res.data.rows[n + 1][item] == res.data.rows[n][item]
                                ) {
                                rowSpan++;
                                n++;
                                res.data.rows[n].rowSpan = 0;
                                }
                                if (rowSpan) res.data.rows[i][item + "_rowSpan"] = rowSpan + 1;
                                if (!rowSpan) res.data.rows[i][item + "_rowSpan"] = 1;
                                i += rowSpan;
                            }
                        }); */
                        let statusarr = [
                            "fixedState",
                            "mstatus",
                            "productCateOptionBeans",
                            "state",
                            
                        ];
                        let widtharr = [
                            'productManager',
                            'customer',
                            'fixedState'
                        ]
                        let feedArr = [
                            'produceFeedback',
                            'sellFeedback',
                            'supplyFeedback'
                        ]
                        let priArr = [
                            'feedback:made',
                            'feedback:sale',
                            'feedback:supply'
                        ]
                        for (const item of res.data.columns) {
                            /* if(spanArr.indexOf(item.key) > -1){
                                item.customRender = (value, row, index) =>{
                                    const obj = {
                                        children: value,
                                        attrs: {},
                                    };
                                    if (row[item.key+'_rowSpan'] > 0) {
                                        obj.attrs.rowSpan = row[item.key+'_rowSpan']
                                        obj.attrs.colSpan = 1
                                    }else{
                                        obj.attrs.rowSpan = row[item.key+'_rowSpan']
                                        obj.attrs.colSpan = 0
                                    }
                                    
                                    return obj;
                                }
                            } */
                            let index = statusarr.indexOf(item.key)
                            if (index > -1) {
                                item.scopedSlots = {
                                    customRender: statusarr[index]
                                }
                            }
                            if (item.width) {
                                item.width = parseInt(item.width)
                            }
                            if (widtharr.indexOf(item.key) > -1) {
                                item.width = 0
                            }

                            if (feedArr.indexOf(item.key) > -1) {
                                let i = feedArr.indexOf(item.key)
                                if (this.hasPerm(priArr[i])) {
                                    item.width = 0
                                }else{
                                    item.width = 0
                                }
                                
                            }
                        }

                        let $i = res.data.columns.findIndex(item => item.dataIndex == 'produceFeedback')
                        if (this.hasPerm('feedback:made') || this.hasPerm('feedback:madehistorys')) {
                            res.data.columns.splice($i +1,0,{
                                title: '制造反馈',
                                width: '80px',
                                dataIndex: 'made',
                                scopedSlots: {
                                    customRender: 'made'
                                }
                            })
                        }

                        $i  = res.data.columns.findIndex(item => item.dataIndex == 'sellFeedback')
                        if (this.hasPerm('feedback:sale') || this.hasPerm('feedback:salehistorys')) {
                            res.data.columns.splice($i +1,0,{
                                title: '销售反馈',
                                width: '80px',
                                dataIndex: 'sale',
                                scopedSlots: {
                                    customRender: 'sale'
                                }
                            })
                        }

                        $i  = res.data.columns.findIndex(item => item.dataIndex == 'supplyFeedback')
                        if (this.hasPerm('feedback:supply') || this.hasPerm('feedback:supplyhistorys')) {
                            
                            res.data.columns.splice($i +1,0,{
                                title: '供应链反馈',
                                width: '80px',
                                dataIndex: 'supply',
                                scopedSlots: {
                                    customRender: 'supply'
                                }
                            })
                        }
                        
                        this.columns = res.data.columns
                        this.loadData = res.data.rows
                        if (this.queryParam.cateId != null || (this.queryParam.productProjectName != null && this.queryParam.productProjectName != '')) {
                            this.getExpandedRowKeys(res.data.rows)
                        }else{
                            this.expandedRowKeys = []
                        }
                    } else {
                        this.$message.error('错误提示：' + res.message, 1)
                    }
                    this.loading = false
                }).catch((err) => {
                    this.loading = false
                    this.$message.error('错误提示：' + err.message, 1)
                });
            },
        },
        created() {
            this.callProjects()
            this.callGetTree()
        }
    }
</script>

<style>
.btns a{
    padding-left: 4px;
    margin-right: 8px;
}
.btns a:last-child{
    padding: 0;
    margin: 0;
}
.clickheadstyle {
  cursor: pointer;
  display: inline-block;
  width: 100%;
}

.clickheadstyle:hover {
  color: #0d87d8;
}
</style>