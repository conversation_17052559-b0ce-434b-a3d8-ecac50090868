<template>
  <a-modal :visible="true" title="实际测试数量" :width="750" centered  :confirmLoading="spinning" @cancel="handleCancel">
    <a-spin :spinning="spinning">
      <a-table :columns="columns"  :data-source="loadData" bordered :rowKey="(record) => record.id" :pagination="{ size:'small' }">
        <span slot="judgeResult" slot-scope="text,record,index">
          <a-select v-model="record.judgeResult" style="width: 120px"
            @change="$event => handleInputBlur($event,record,'judgeResult')">
            <a-select-option v-for="(item,i) in getDict('dev_test_manage_judge_result')" :key="i"
              :value="item.code">{{item.name}}</a-select-option>
          </a-select>
        </span>
        <span slot="testResult" slot-scope="text,record,index">
          <div v-if="text">
            <a-tooltip>
              <template slot="title">
                {{text}}
              </template>
              <a-textarea v-model="record.testResult" placeholder="请输入测试结果" :auto-size="{ minRows: 1, maxRows: 3 }"
                @blur="$event => handleInputBlur($event,record,'testResult')" />
            </a-tooltip>
          </div>
          <div v-else>
            <a-textarea v-model="record.testResult" placeholder="请输入测试结果" :auto-size="{ minRows: 1, maxRows: 3 }"
              @blur="$event => handleInputBlur($event,record,'testResult')" />
          </div>
        </span>
      </a-table>
    </a-spin>
    <template slot="footer">
      <a-button @click="handleCancel">
        关闭
      </a-button>
    </template>

  </a-modal>
</template>

<script>
  import Vue from 'vue'
  import {
    DICT_TYPE_TREE_DATA
  } from '@/store/mutation-types'
  import { getUserLists } from "@/api/modular/system/userManage"
  import { getTestResultList,updateTestResultData } from "@/api/modular/system/dpvTestManage"

  export default {
    props: {
      testProjectStructureDataId: {
        type: String,
        default: ""
      }
    },
    data() {
      return {
        spinning: false,
        loadData: [],
        columns: [
          {
            title: '序号',
            align: 'center',
            width: 45,
            customRender: (text, record, index) => index + 1
          }, {
            title: '测试编码',
            align: 'center',
            dataIndex: 'cellTestCode',
          }, {
            title: '批次',
            align: 'center',
            dataIndex: 'cellBatch',
          }, {
            title: '实际开始时间',
            align: 'center',
            dataIndex: 'realStartTime',
          }, {
            title: '实际结束时间',
            align: 'center',
            dataIndex: 'realEndTime',
            customRender: (text, record, index) => text || '-'
          }, {
            title: '测试结果',
            align: 'center',
            dataIndex: 'testResult',
            scopedSlots: { customRender: 'testResult' },
          }, {
            title: '测试结果判定',
            align: 'center',
            dataIndex: 'judgeResult',
            scopedSlots: { customRender: 'judgeResult' },
          }
        ]
      }
    },
    created() {
      this.getTestResultList()
    },
    methods: {
      getDict(code) {
        const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
        return dictTypeTree.filter(item => item.code == code)[0].children
      },
      getTestResultList() {
        this.spinning = true
        getTestResultList({ testProjectStructureDataId: this.testProjectStructureDataId }).then(res => {
          this.loadData = res.data
        }).finally(() => {
          this.spinning = false
        })
      },
      updateTestResultData(params) {
        updateTestResultData(params).then(res => {
          if (!res.success) this.$message.warn('更新失败')
        })
      },

      handleInputBlur(e, record, target) {
        const params = {
          id: record.id,
          [target]: record[target]
        }
        this.updateTestResultData(params)
      },
      handleCancel() {
        this.$emit('cancel')
      }

    }
  }
</script>
<style lang="less" scoped>
  /deep/.ant-modal-body{
    padding: 24px 24px 0 ;
  }
  /deep/ .ant-modal-footer{
    padding: 0 24px 24px ;
  }
  
</style>