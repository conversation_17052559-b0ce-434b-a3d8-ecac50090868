<template>
	<div class="container">
		<!-- Breadcrumb 面包屑 start -->
		<div>
			<a-breadcrumb class="breadcrumb" separator=">">
				<a-breadcrumb-item
					><a @click="gotoIndex(1)"
						><span class="" style="width: 100%; height: 100%; min-width: 14px; min-height: 14px; margin-right:4px"
							><svg
								xmlns="http://www.w3.org/2000/svg"
								class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 xuJzg svg-icon-path-icon fill"
								viewBox="0 0 48 48"
								width="14"
								height="14"
							>
								<defs data-reactroot=""></defs>
								<g>
									<rect width="48" height="48" fill="white" fill-opacity="0.01"></rect>
									<path
										fill-rule="evenodd"
										clip-rule="evenodd"
										d="M44 40.8361C39.1069 34.8632 34.7617 31.4739 30.9644 30.6682C27.1671 29.8625 23.5517 29.7408 20.1182 30.303V41L4 23.5453L20.1182 7V17.167C26.4667 17.2172 31.8638 19.4948 36.3095 24C40.7553 28.5052 43.3187 34.1172 44 40.8361Z"
										fill="none"
										stroke="#333"
										stroke-width="4"
										stroke-linejoin="round"
									></path>
								</g></svg></span
						>首页看板</a
					></a-breadcrumb-item
				>
				<a-breadcrumb-item>{{ $route.query.dept }}变更管理</a-breadcrumb-item>
			</a-breadcrumb>
		</div>
		<!-- Breadcrumb 面包屑 start -->

		<!-- 主标题 start -->
		<div class="head-title">{{ $route.query.dept || "动力电池研究院" }}</div>
		<!-- 主标题 end -->

		<div>
			<!-- 图表 start -->
			<!-- <div class="board">
				<div class="col1">
					<div class="item mr10">
						<strong class="head">变更分析</strong>
						<a-spin :spinning="loading">
							<div class="chart_table" ref="total"></div>
						</a-spin>
					</div>
				</div>
				<div class="col2">
					<div class="item">
						<strong class="head">变更阶段分析</strong>
						<a-spin :spinning="loading">
							<div class="chart_table" ref="abnormal"></div>
						</a-spin>
					</div>
				</div>
			</div> -->
			<!-- 图表 end -->

			<!-- 表格 start -->
			<div class="table-wrapper">
				<a-table
					:style="`height:${tableHeight}px`"
					sticky
					:data-source="data"
					:columns="columns"
					size="middle"
					:pagination="pagination"
					:loading="loading"
				>
					<span slot="num" slot-scope="text, records, index">
						{{ index+1 }}
					</span>

					<!-- 变更内容 -->
					<template slot="alterContent" slot-scope="text">
						<!-- <div style="display:none">{{ (strarr = spilt(text)) }}</div>
						<div style="display:none">{{ (str = combine(text)) }}</div> -->
						<!-- <clamp :expend="true" :text="str" :sourceText="strarr"></clamp> -->
						<div class="ellipsis" @click="info(spilt(text))">{{combine(text)}}</div>
					</template>

					<!-- 变更原因 -->
					<template slot="alterReason" slot-scope="text">
						<a-tooltip>
							<template slot="title">
								{{ text }}
							</template>
							<div class="ellipsis-tip">{{ text }}</div>
						</a-tooltip>
					</template>
				</a-table>
			</div>
			<!-- 表格 end -->
		</div>
	</div>
</template>

<script>
import { getProjectAltersBydept } from "@/api/modular/system/chartManage"
import { clamp } from "@/components"
import _ from "lodash"
export default {
	components: {
		clamp
	},
	data() {
		return {
			loading: false,
			pagination: {
				current: 1,
				pageSize: 10,
				total: 0,
				//pageSizeOptions: ['10', '20', '50', '100'],
				showSizeChanger: true,
				showQuickJumper: true,
				onChange: (current, size) => {
					this.pagination.current = current
					this.pagination.pageSize = size
				},
				onShowSizeChange: (current, pageSize) => {
					this.pagination.current = 1
					this.pagination.pageSize = pageSize
				}
			},
			tableHeight:
				document.documentElement.clientHeight - 40 - 20 - 47 - 20,

			projectProcessTotal: {},
			projectProcessTotalAxis: [],
			data: [],
			alterTypeFilters: [],
			alterProductFilters: [],
			columns: [],
			dataPie: {},
			bomAlter: {},
			mIAlter: {},
			mapAlter: {},
			stateAlterTypeCount: {},
			depts: [],
			stateCount: {}
		}
	},
	methods: {
		getByClass(parent, cls) {
			if (parent.getElementsByClassName) {
				return Array.from(parent.getElementsByClassName(cls));
			} else {
				var res = [];
				var reg = new RegExp(' ' + cls + ' ', 'i')
				var ele = parent.getElementsByTagName('*');
				for (var i = 0; i < ele.length; i++) {
					if (reg.test(' ' + ele[i].className + ' ')) {
						res.push(ele[i]);
					}
				}
				return res;
			}
		},
		initMain() {
			let that = this
			that.$nextTick(() => {
				let items = that.getByClass(document, 'ant-layout-content')
				for (const e of items) {
					e.style.paddingLeft = 0
				}
			})
		},
		info(strArr) {
			const h = this.$createElement;
			let msgs = []
			for (let msg of strArr) {
				msgs.push(h('p',msg))
			}
			console.log(strArr)
			this.$info({
				title: '变更详情',
				content: h('div', {},msgs),
				onOk() {},
			});
		},
		spilt(str) {
			if (str == "{}") {
				return []
			}
			let bomChangeJson = JSON.parse(str)
			if (!bomChangeJson.changes) {
				return []
			}
			let arr = []
			if (bomChangeJson.changes != undefined) {
				for (const item of bomChangeJson.changes) {
					arr.push(
						`主物料:${item.mSapNumber}${item.flag}子物料:${item.sapNumber}-${item.partDescription}-使用量:${item.partUse}-变更前使用量:${item.prePartUse}`
					)
				}
			}
			return arr
		},
		combine(str) {
		  if(null == str){
		    return str
      }
		  if(!str.startsWith("{")){
		    return str
      }
			if (str == "{}") {
				return ""
			}
			let bomChangeJson = JSON.parse(str)
			if (!bomChangeJson.changes) {
				return ""
			}
			let strarr = ""
			if (bomChangeJson.changes != undefined) {
				let arr = bomChangeJson.changes.slice(0, bomChangeJson.changes.length > 3 ? 3 : bomChangeJson.changes.length)
				for (const item of arr) {
					strarr += `主物料:${item.mSapNumber}${item.flag}子物料:${item.sapNumber}-${item.partDescription}-使用量:${item.partUse}-变更前使用量:${item.prePartUse}\n`
				}
			}
			return strarr
		},
		resize() {
			setTimeout(() => {
				this.initTotal()
				this.initAbnormal()
			}, 500)
		},
		gotoIndex(index) {
			this.$router.push("/product_chart")
		},
		handleSearch(selectedKeys, confirm, dataIndex) {
			confirm()
		},

		handleReset(clearFilters) {
			clearFilters()
		},
		/**
		 * echarts
		 */
		initTotal() {
			let chart = this.echarts.init(this.$refs.total)
			chart.clear()

			let axis = []
			let dataBom = []
			let dataMi = []
			let dataMap = []

			for (const key in this.bomAlter) {
				axis.push(key)
				dataBom.push(this.bomAlter[key])
				dataMi.push(this.mIAlter[key])
				dataMap.push(this.mapAlter[key])
			}

			const options = {
				legend: {
					itemWidth: 8,
					itemHeight: 8,
					bottom: "1%"
				},
				grid: {
					left: "3%",
					right: "4%",
					bottom: "12%",
					containLabel: true
				},
				xAxis: {
					type: "category",
					data: [Object.keys(this.bomAlter)[0], Object.keys(this.bomAlter)[1]]
				},
				yAxis: {
					show: false
				},
				series: [
					{
						name: "BOM",
						type: "bar",
						label: {
							show: true,
							position: "top"
						},
						itemStyle: {
							color: "#5b9bd5",
							barBorderRadius: [5, 5, 0, 0]
						},
						barWidth: "10%",
						data: dataBom
					},
					{
						name: "MI",
						type: "bar",
						label: {
							show: true,
							position: "top"
						},
						itemStyle: {
							color: "#ed7d31",
							barBorderRadius: [5, 5, 0, 0]
						},
						barWidth: "10%",
						data: dataMi
					},
					{
						name: "图纸",
						type: "bar",
						label: {
							show: true,
							position: "top"
						},
						itemStyle: {
							color: "#a5a5a5",
							barBorderRadius: [5, 5, 0, 0]
						},
						barMaxWidth: "10%",
						data: dataMap
					}
				]
			}

			chart.setOption(options)
			chart.resize()
		},
		initAbnormal() {
			let chart = this.echarts.init(this.$refs.abnormal)

			let map = {
				0: 2,
				1: 3,
				2: 4
			}

			let depts = this.depts
			let stateCount = this.stateCount
			let stateAlterCount = this.stateAlterTypeCount

			chart.clear()
			let option = {
				color: ["#5b9bd5", "#4472c4", "#a5a5a5", "#f6d530", "#ed7d31"],
				title: {
					show: false
				},
				tooltip: {
					trigger: "axis",
					axisPointer: {
						type: "shadow"
					},
					formatter(params) {
						let bomNum = stateAlterCount[params[0].seriesName + "#" + map[params[0].dataIndex] + "$2"]
							? stateAlterCount[params[0].seriesName + "#" + map[params[0].dataIndex] + "$2"]
							: 0
						let mINum = stateAlterCount[params[0].seriesName + "#" + map[params[0].dataIndex] + "$0"]
							? stateAlterCount[params[0].seriesName + "#" + map[params[0].dataIndex] + "$0"]
							: 0
						let tzNum = stateAlterCount[params[0].seriesName + "#" + map[params[0].dataIndex] + "$1"]
							? stateAlterCount[params[0].seriesName + "#" + map[params[0].dataIndex] + "$1"]
							: 0
						return (
							params[0].seriesName +
							params[0].name +
							"<br/>" +
							"BOM数量:" +
							bomNum +
							"<br/>" +
							"MI数量:" +
							mINum +
							"<br/>" +
							"图纸数量:" +
							tzNum
						)
					}
				},
				legend: {
					itemWidth: 8,
					itemHeight: 8,
					bottom: "1%"
				},
				grid: {
					left: "3%",
					right: "4%",
					top: "4%",
					bottom: "12%",
					containLabel: true
				},
				xAxis: {
					type: "category",
					data: ["A样", "B样", "C样以后"],
					axisTick: {
						//y轴刻度线
						show: false
					},
					splitLine: {
						//网格线
						show: false
					},
					axisLine: {
						//y轴
						show: true
					}
				},
				yAxis: {
					splitLine: {
						show: false
					},
					axisTick: {
						show: false
					},
					gridIndex: 0,
					axisLine: {
						//y轴
						show: false
					},
					type: "value",
					show: false,
					boundaryGap: [0, 0.01]
				},
				series: []
			}
			for (const item of depts) {
				option.series.push({
					name: item,
					type: "bar",
					barMaxWidth: "22",
					itemStyle: {
						barBorderRadius: [5, 5, 0, 0]
					},
					data: [
						stateCount.hasOwnProperty(item + "#2") ? stateCount[item + "#2"] : 0,
						stateCount.hasOwnProperty(item + "#3") ? stateCount[item + "#3"] : 0,
						stateCount.hasOwnProperty(item + "#4") ? stateCount[item + "#4"] : 0
					]
				})
			}
			chart.setOption(option)
			chart.resize()
		},
		callProjectAlterBydept() {
			this.loading = true
			getProjectAltersBydept(this.$route.query)
				.then(res => {
					if (res.result) {
						this.data = res.data.list ? res.data.list : []
						this.dataPie = res.data.pie ? res.data.pie : {}
						this.bomAlter = res.data.BOMCOUNT ? res.data.BOMCOUNT : {}
						this.mIAlter = res.data.MICOUNT ? res.data.MICOUNT : {}
						this.mapAlter = res.data.MAPCOUNT ? res.data.MAPCOUNT : {}
						this.stateAlterTypeCount = res.data.stateAlterTypeCount ? res.data.stateAlterTypeCount : {}
						this.stateCount = res.data.stateCount ? res.data.stateCount : {}
						this.depts = res.data.depts

						this.alterProductFilters = _.chain(res.data.list)
							.map(item => item.product)
							.uniq()
							.map(item => ({
								text: item,
								value: item
							}))
							.value()

						this.alterTypeFilters = _.chain(res.data.list)
							.map(item => item.alterType)
							.uniq()
							.map(item => ({
								text: item,
								value: item
							}))
							.value()

						this.columns = [
							{
								title: "序号",
								dataIndex: "seq",
								align: "center",
								width: 50,
								scopedSlots: {
									customRender: "num"
								}
							},
							{
								title: "产品名称",
								dataIndex: "product",
								align: "center",
								filters: this.alterProductFilters,
								onFilter: (value, record) => record.product == value + ""
							},
							{
								title: "变更类型",
								dataIndex: "alterType",
								align: "center",
								filters: this.alterTypeFilters,
								onFilter: (value, record) => record.alterType == value + ""
							},
							{
								title: "发起时间",
								dataIndex: "alterDate",
								align: "center"
							},
							{
								title: "变更原因",
								dataIndex: "alterReason",
								width: 150,
								align: "center",
								scopedSlots: {
									customRender: "alterReason"
								}
							},
							{
								title: "变更内容",
								dataIndex: "alterContent",
								width: 200,
								align: "center",
								scopedSlots: {
									customRender: "alterContent"
								}
							},
							{
								title: "变更评估部门",
								dataIndex: "alterDept",
								align: "center"
							},
							{
								title: "变更负责人",
								dataIndex: "alterCharge",
								align: "center"
							},
              				{
								title: "审批状态",
								dataIndex: "pushStatus",
								align: "center",
								customRender: (text, row, index) => {
												if(text == 4){
													return "已审核"
								}
												return "审核中"
								}
							},
							{
								title: "实施时间",
								dataIndex: "alterProcessDate",
								align: "center"
							}
						]
						this.$nextTick(() => {
							this.initTotal()
							this.initAbnormal()
						})
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
					this.loading = false
				})
				.catch(err => {
					this.loading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		}
	},
	mounted() {
		this.callProjectAlterBydept()
		window.addEventListener("resize", this.resize)

		// 动态修改--height的值
		document.documentElement.style.setProperty(`--height`, `${this.tableHeight - 63}px`)
		this.initMain()
	},
	destroyed() {
		window.removeEventListener("resize", this.resize)
	}
}
</script>

<style lang="less" scoped>
// 面包屑
.ant-breadcrumb a {
	color: #5d90fa !important;
}
.ant-breadcrumb {
	font-size: 12px !important;
}

.board {
	display: flex;
	margin-bottom: 10px;
}
.head {
	position: absolute;
	top: 12px;
	left: 12px;
}
.col1 {
	width: 50%;
}
.col2 {
	width: 50%;
}
.item {
	padding: 8px;
	border-radius: 10px;
	overflow: hidden;
	background: #fff;
	box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
	position: relative;
}

.chart_table {
	width: 100%;
	height: 35vh;
}
/deep/.ant-table-placeholder {
	padding: 0;
}

// 表头
/deep/.ant-table-thead > tr > th {
	font-size: 13px;
	background: #f5f5f5 !important;
	color: #333;
}
/deep/.ant-table-tbody {
	background: #fff;
	color: #666;
}
/deep/.ant-table-thead > tr > th .anticon-filter,
/deep/.ant-table-thead > tr > th .ant-table-filter-icon {
	color: #999;
}
/deep/.ant-table-thead
	> tr
	> th.ant-table-column-has-actions.ant-table-column-has-filters
	.anticon-filter.ant-table-filter-open,
/deep/.ant-table-thead
	> tr
	> th.ant-table-column-has-actions.ant-table-column-has-filters
	.ant-table-filter-icon.ant-table-filter-open,
/deep/.ant-table-thead > tr > th.ant-table-column-has-actions.ant-table-column-has-filters:hover .anticon-filter:hover,
/deep/.ant-table-thead
	> tr
	> th.ant-table-column-has-actions.ant-table-column-has-filters:hover
	.ant-table-filter-icon:hover {
	background: #fff;
	color: rgb(201, 201, 201);
}
/deep/.ant-checkbox-group-item {
	display: block;
	margin: 0 8px;
}

/deep/.ant-table-small .ant-table-content .ant-table-body {
	margin: 0;
}

// 表格
.table-wrapper {
	border-radius: 10px;
	overflow: hidden;
	box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
}

:root {
	--height: 600px;
}

/deep/.ant-table-body {
	height: var(--height) !important;
	overflow-y: scroll;
}

// 表头固定
/deep/.ant-table-thead {
	position: sticky;
	top: 0;
	z-index: 666;
}

/* 主标题 */

.head-title {
	color: #333;
	padding: 10px 0;
	font-size: 18px;
	font-weight: 600;
}

.head-title::before {
	width: 8px;
	background: #1890ff;
	margin-right: 8px;
	content: "\00a0"; /* 填充空格 */
}

.ellipsis-tip {
	width: 200px;
	word-break: break-all;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 3; /* 这里是超出几行省略 */
	overflow: hidden;
}
.ellipsis {
	cursor: pointer;
	font-size: 12px;
    text-align: left;
    padding-left: 2px;
    word-break: break-all;
    line-height: 1.5em;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 100%;
    background: initial;
	text-align: left;
    overflow: hidden;
    display: -webkit-inline-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
}
/deep/tr th:hover {
	background-color: #e8e8e8 !important;
	color: #333 !important;
}
</style>
