{"name": "vue-antd-pro", "version": "2.1.0", "private": true, "scripts": {"serve": "SET NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit", "build:preview": "vue-cli-service build --mode preview", "postinstall": "opencollective-postinstall"}, "dependencies": {"@antv/data-set": "^0.11.8", "@antv/g6": "^4.6.11", "@antv/util": "^3.2.0", "@riophae/vue-treeselect": "^0.4.0", "@vue-office/excel": "^1.7.11", "@vue-office/pdf": "^2.0.2", "@vue/composition-api": "^1.7.2", "ag-grid-vue": "^31.3.4", "animate.css": "^4.1.1", "ant-design-vue": "1.7.8", "axios": "0.19.0", "babel-polyfill": "^6.26.0", "bignumber.js": "^9.1.2", "clipboard": "^2.0.6", "clipboard-polyfill": "^4.0.1", "compression-webpack-plugin": "5.0.1", "core-js": "^3.1.2", "crypto-js": "^4.0.0", "decimal.js": "^10.4.3", "default-passive-events": "^1.0.10", "dhtmlx-gantt": "^8.0.1", "echarts": "^5.3.3", "enquire.js": "^2.1.6", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "html2canvas": "^1.4.1", "jquery": "^3.5.1", "json-bigint": "^1.0.0", "lodash.clonedeep": "^4.5.0", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "md5": "^2.2.1", "mockjs2": "1.0.8", "moment": "^2.29.3", "nprogress": "^0.2.0", "pako": "^2.1.0", "pdfjs-dist": "^4.10.38", "print-js": "^1.0.63", "raphael": "^2.3.0", "react": "latest", "react-dom": "latest", "screenfull": "^5.1.0", "sortablejs": "^1.15.6", "v-distpicker": "^1.3.1", "v-selectpage": "^2.1.4", "viser-vue": "^2.4.6", "vue": "2.6.10", "vue-clipboard2": "^0.2.1", "vue-codemirror-lite": "^1.0.4", "vue-cropper": "0.4.9", "vue-draggable-resizable": "^2.3.0", "vue-easytable": "^2.20.2", "vue-gantt-schedule-timeline-calendar": "^3.0.44", "vue-ls": "^3.2.1", "vue-mathjax": "^0.1.1", "vue-pdf": "^4.3.0", "vue-quill-editor": "^3.0.6", "vue-router": "3.1.3", "vue-svg-component-runtime": "^1.0.1", "vue-template-compiler": "2.6.10", "vue-tree-color": "^2.3.2", "vue-virtual-scroll-list": "^2.3.5", "vuedraggable": "^2.23.2", "vuex": "3.1.1", "vxe-table": "^3.6.6", "vxe-utils": "^2.0.1", "wangeditor": "^3.1.1", "xe-utils": "^3.5.6", "xlsx": "^0.18.5"}, "devDependencies": {"@ant-design/colors": "^3.2.1", "@vue/cli-plugin-babel": "^4.0.4", "@vue/cli-plugin-eslint": "^4.0.4", "@vue/cli-plugin-router": "^4.0.4", "@vue/cli-plugin-unit-jest": "^4.0.4", "@vue/cli-plugin-vuex": "^4.0.4", "@vue/cli-service": "^4.0.4", "@vue/eslint-config-prettier": "^5.0.0", "@vue/eslint-config-standard": "^4.0.0", "@vue/test-utils": "^1.0.0-beta.29", "babel-eslint": "^10.0.1", "babel-plugin-import": "^1.13.0", "babel-plugin-transform-remove-console": "^6.9.4", "drag-tree-table": "^2.2.0", "eslint": "^5.2.3", "eslint-plugin-html": "^5.0.0", "eslint-plugin-prettier": "^3.1.0", "eslint-plugin-vue": "^5.2.3", "less": "^3.13.1", "less-loader": "^4.1.0", "moment-timezone": "^0.5.45", "opencollective": "^1.0.3", "opencollective-postinstall": "^2.0.2", "prettier": "^1.18.2", "sass-loader": "^10.5.2", "vue-svg-icon-loader": "^2.1.1", "vue2-org-tree": "^1.3.6", "webpack-theme-color-replacer": "1.3.18"}, "collective": {"type": "opencollective", "url": "https://opencollective.com/ant-design-pro-vue"}, "main": ".eslintrc.js", "directories": {"test": "tests"}, "keywords": [], "author": "", "license": "ISC", "description": ""}