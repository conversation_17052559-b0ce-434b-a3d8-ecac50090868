<template>
    <div class="page-container">
        <!-- 标题 start -->
        <pbiTitle v-if="pageTitleShow" :title="pageTitle" :fontSize="pageTitleFontSize"></pbiTitle>
        <slot name="title"></slot>
        <!-- 标题 end -->
         
        <!-- 搜索框 start -->
        <slot name="search"></slot>
        <!-- 搜索框 end -->
         
        <!-- 表格 start -->
         <div @mouseenter="tableFocus" @mouseleave="tableBlur">
            <a-spin :spinning="loading">
                <ag-grid-vue
                    :style="{height:'500px'}"
                    class='table ag-theme-balham' 
                    :defaultColDef="tableDefaultColDef"
                    :grid-options="tableGridOptions"
                    :columnDefs='tableColumnDefs'
                    :rowData='rowData'
                    :tooltipShowDelay="0"
                    :suppressDragLeaveHidesColumns="true"
                    :suppressMoveWhenColumnDragging="true"
                >
                </ag-grid-vue>
            </a-spin>    
         </div>
        <!-- 表格 end -->
    </div>
</template>
<script>
import _ from "lodash";
  export default {
    props: {
      // 页面标题
      pageTitleShow: {  
        type: Boolean,
        default: true
      },
      pageTitle: {
        type: String,
        default: ''
      },
      pageTitleFontSize:{
        type:Number,
        default:16
      },
      // 表格
      loading: {
        type: Boolean,
        default: true
      },
      defaultColDef: {
        type: Object,
        default:() => {}
      },
      gridOptions: {
        type: Object,
        default:() => {}
      },
      columnDefs:{
        type: Array,
        default: () => [],
      },
      rowData:{
        type: Array,
        default: () => [],
      }
    },
    data(){
        return {
            tableDefaultColDef:{
                tooltipValueGetter: this.pbiTooltip
            },
            tableGridOptions:{
                onColumnResized: _.debounce(this.pbiRefreshCells,500)
            },
            tableColumnDefs:[]
        }
    },
    mounted(){
        if(!this.isEmptyObject(this.defaultColDef)) this.tableDefaultColDef.push(...this.defaultColDef)
        if(!this.isEmptyObject(this.gridOptions)) this.tableGridOptions.push(...this.gridOptions)
        
        this.handleColumn(this.columnDefs) 
        this.$el.style.setProperty('--scroll-border-bottom-fixed', '1px solid #dee1e8');
        this.$el.style.setProperty('--scroll-display', 'none');
        this.$el.style.setProperty('--scroll-border-bottom', 'none');
    },
    components:{
        columnsImage: {
                    template: '<div><img v-if="params.imageSrc" :style="`width:${params.width}px;height:${params.height}px`" :src="params.imageSrc" alt=""><span v-else>-</span></div>',
                },
        // columnsTag: {
        //             template: '<div><a-tag v-if="params.judgmentResult" :style="`backgroundColor:${params.background1},color:${params.color1},borderColor:${params.borderColor1}}`">有效日期</a-tag><a-tag v-else :style="`backgroundColor:${params.background2},color:${params.color2},borderColor:${params.borderColor2}}`">失效日期</a-tag></div>',
        //         },
    },
    
    methods: {
        handleColumn(column){
            this.tableColumnDefs = []   
            for(let i = 0 ; i < column.length ; i++){
                
                // 序号
                if(column[i].pbiKey && column[i].pbiKey === 'serialNumber'){
                    this.tableColumnDefs.push({headerName: '序号',  field: 'serialNumber' , width: column[i].width || 50, cellRenderer: p => p.node.rowIndex + 1})
                    
                }
                
                // 单选框
                if(column[i].pbiKey && column[i].pbiKey === 'singleSerialNumber'){
                    this.tableColumnDefs.push({headerName: '序号', field: 'singleSerialNumber', width: column[i].width || 50, checkboxSelection: true})
                    this.tableGridOptions.rowSelection = "single"
                }
                
                // 多选框
                if(column[i].pbiKey && column[i].pbiKey === 'multipleSerialNumber'){
                    this.tableColumnDefs.push({headerName: '序号', field: 'multipleSerialNumber', width: column[i].width || 50, headerCheckboxSelection: true, checkboxSelection: true})
                    this.tableGridOptions.rowSelection = "multiple"
                }
                
                // 图片类
                if(column[i].pbiKey && column[i].pbiKey === 'image'){
                    this.tableColumnDefs.push({
                        headerName: column[i].headerName || '图片', 
                        field: column[i].field, 
                        width: column[i].width || 70,
                        cellRenderer: 'columnsImage',
                        cellRendererParams: params => {
                            return {
                                imageSrc: params.data[column[i].field],
                                width:params.data[column[i].imageWidth] || 28,
                                height:params.data[column[i].imageHeight] || 28,
                            };
                        },
                        tooltipValueGetter : params => ''
                    })
                }
                // 一维类型展示
                if(column[i].pbiKey && column[i].pbiKey === 'oneDimensional'){
                    this.tableColumnDefs.push({
                        headerName: column[i].headerName || '类别', 
                        field: column[i].field,
                        width: column[i].width || 70,
                        cellRenderer: params => {
                            return `<div>${column[i].typeOptions[params.data[column[i].type1]]}</div>`;
                        }
                    })
                }
                
                // 二维类型展示
                if(column[i].pbiKey && column[i].pbiKey === 'twoDimensional'){
                    this.tableColumnDefs.push({
                        headerName: column[i].headerName || '类别', 
                        field: column[i].field,
                        width: column[i].width || 70,
                        cellRenderer: params => {
                            return `<div>${column[i].typeOptions[params.data[column[i].type1]][params.data[column[i].type2]]}</div>`;
                        }
                    })
                }
                
                // 长文本
                if(column[i].pbiKey && column[i].pbiKey === 'LongText'){
                    this.tableColumnDefs.push({
                        headerName: column[i].headerName || '备注', 
                        field: column[i].field,
                        width: column[i].maxWord * 12 + 24,
                    })
                }
                
                // 功能按键
                if(column[i].pbiKey && column[i].pbiKey === 'actionBtn'){
                    this.tableColumnDefs.push({
                        headerName: column[i].headerName || '操作', 
                        field: column[i].field,
                        cellRenderer:'pbiTableActionBtn',
                        cellRendererParams:{
                            btnList:column[i].btnList,
                            handleClickBtn:(type,params) =>  this.$emit('handleActionBtn',{type,params}) 
                        }
                    })
                }
                
                
                if(!column[i].pbiKey){
                    this.tableColumnDefs.push({...column[i]})
                }
                
                
                
            }
        },
        handleActionBtn(type,params){
            console.log(type,params)
        },
        isEmptyObject(obj) {
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    return false;
                }
            }
            return true;
        },
        tableFocus() {
            this.$el.style.setProperty('--scroll-border-bottom-fixed', 'none');
            this.$el.style.setProperty('--scroll-display', 'unset');
            this.$el.style.setProperty('--scroll-border-bottom', '1px solid #Dee1e8');
        },
        // 鼠标移出
        tableBlur() {
            this.$el.style.setProperty('--scroll-border-bottom-fixed', '1px solid #dee1e8');
            this.$el.style.setProperty('--scroll-display', 'none');
            this.$el.style.setProperty('--scroll-border-bottom', 'none');
        },
    }
  }
</script>
<style lang="less" scoped>
@import './style/tableInIndex.css';
:root {
    --scroll-display: none;
    --scroll-border-bottom: none;
    --scroll-border-bottom-fixed: 1px solid #dee1e8;
}
/deep/.ag-body-horizontal-scroll{
    border-bottom: var(--scroll-border-bottom) !important;
}
/deep/.ag-body-horizontal-scroll-viewport {
    display: var(--scroll-display) !important;
    border-bottom: var(--scroll-border-bottom) !important;
}

/deep/.ag-horizontal-left-spacer,
/deep/.ag-horizontal-right-spacer{
    border-bottom: var(--scroll-border-bottom-fixed) !important;
}
</style>
  