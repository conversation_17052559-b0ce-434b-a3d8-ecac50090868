<template>
  <div class="pbi-head-title mt10">
    <div class="line mr10"></div>
    <span class="title">{{title}}</span>
  </div>
</template>
<script>
  export default {
    props: {
      title: {
        type: String,
        default: ''
      }
    },
  }
</script>
<style lang='less' scoped>
  /* 标题 */
  .pbi-head-title {
    display: flex;
    align-items: center;
  }

  .pbi-head-title .line {
    width: 4px;
    height: 22px;
    background: #3293ff;
    border-radius: 20px;
  }

  .pbi-head-title .title {
    font-size: 16px;
    font-weight: 600;
  }

  .mr10{
    margin-right: 10px;
  }
</style>