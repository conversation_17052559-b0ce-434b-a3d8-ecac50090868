<template>
	<a-modal
		title="添加"
		:visible="true"
		:width="700"
		:centered="true"
		okText="查看详情"
		cancelText="关闭"
		@cancel="handleModelCancel"
		@ok="handleModelOk"
	>
		<div class="model-wrapper">
			<a-form :form="form" :label-col="{ span: 6 }" :wrapper-col="{ span: 12 }">
				<a-form-item label="app名称">
					<a-input />
				</a-form-item>
				<a-form-item label="app地址">
					<a-select placeholder="Select a option and change input text above">
						<a-select-option value="male">
							male
						</a-select-option>
						<a-select-option value="female">
							female
						</a-select-option>
					</a-select>
				</a-form-item>
				<a-form-item label="app封面">
					<a-upload name="file" :beforeUpload="beforeUpload" :showUploadList="false">
						<div v-if="!form.img" class="upload upload-add">
							<a-icon type="plus" />
						</div>
						<img class="upload" v-else :src="form.img" alt="" />
					</a-upload>
				</a-form-item>
			</a-form>
		</div>
	</a-modal>
</template>

<script>
export default {
	data() {
		return {
			form: {
				img: ""
			},
			labelCol: { span: 10 },
			wrapperCol: { span: 10 }
		}
	},
	methods: {
		handleModelCancel() {
			this.$emit("cancel")
		},
		beforeUpload(file) {
			this.fileList = file
			const reader = new FileReader()
			reader.readAsDataURL(file)
			reader.onload = () => {
				this.form.img = reader.result
				console.log(this.form.img)
			}
			return false
		}
	}
}
</script>

<style lang="less" scoped>
.chart-wrapper {
	display: flex;
}

.upload {
	width: 90px;
	height: 90px;
	border-radius: 5px;
}
.upload-add {
	border: 1px dotted #333;

	font-size: 30px;

	display: flex;
	justify-content: center;
	align-items: center;
}
</style>
