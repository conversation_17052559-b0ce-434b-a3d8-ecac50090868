<template>
  <div style="padding: 10px;">
    <pbiTabs :tabsList="chartList" :activeKey="chartType" @clickTab="chartTypeChange"></pbiTabs>
    <div style="padding-bottom: 8px; background-color: #FFFFFF; overflow: auto; display: flex; flex-direction: column;" :style="{borderRadius: chartType === '计划达成率' ? '0 4px 4px 4px' : '4px'}">
        <div class="search-container" style="margin: 0px 10px 0px 10px; display: flex; flex-wrap: wrap;">
          <div class="operate-row" v-for="(room, index) in analysisRooms" v-if="chartType === '人员效率'">
            <div class="label">{{room}}人数：</div>
            <a-input-number v-model="deptPeopleNums[index]" :min="1" @change="deptPeopleNumChange"></a-input-number>
          </div>

          <div class="operate-row">
            <div class="label">时间维度：</div>
            <div style="margin-top: 10px; width: 80px;">
              <a-select style="width: 80px; font-size: 12px;" v-model="timeDimension" @change="timeDimensionChange">
                <a-select-option value="按周">按周</a-select-option>
                <a-select-option value="按月">按月</a-select-option>
              </a-select>
            </div>
            <div class="label">起止日期：</div>
            <div style="margin-top: 10px; width: 200px; font-size: 12px;">
              <a-range-picker :allowClear="false" v-model="timeRange" @change="timeRangeChange"></a-range-picker>
            </div>
          </div>
        </div>

      <div style="flex: 1;">
        <div class="chart-row">
          <div class="chart-item">
            <a-spin :spinning="chartLoading">
              <div ref="chartSEM" class="chart-div"></div>
            </a-spin>
          </div>
          <div class="chart-item">
            <a-spin :spinning="chartLoading">
              <div ref="chartCF" class="chart-div"></div>
            </a-spin>
          </div>
          <div class="chart-item">
            <a-spin :spinning="chartLoading">
              <div ref="chartWH" class="chart-div"></div>
            </a-spin>
          </div>
        </div>
        <div class="chart-row">
          <div class="chart-item">
            <a-spin :spinning="chartLoading">
              <div ref="chartDHX" class="chart-div"></div>
            </a-spin>
          </div>
          <div class="chart-item">
            <a-spin :spinning="chartLoading">
              <div ref="chartJG" class="chart-div"></div>
            </a-spin>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {getTestQuantityStatistics} from "@/api/modular/system/testProgressManager";
import * as echarts from "echarts";
import moment from "moment";
require('moment-timezone');
import pbiTabs from "@/components/pageTool/components/pbiTabs.vue";

export default {
  name: "statisticsTrendChart",
  components: {
    pbiTabs
  },
  data() {
    return {
      chartList: [
        {value:'计划达成率', label:'计划达成率', show: true},
        {value:'人员效率', label:'人员效率', show: true},
        {value:'测试及时率', label:'测试及时率', show: true},
      ],
      chartType: "计划达成率",
      testQuantityStatistics: [],
      timeRange: [],
      timeRangeBegin: null,
      timeRangeEnd: null,
      timeDimension: "按周",
      chartLoading: true,
      deptPeopleNums: [2, 8, 4, 3, 6],
      analysisRooms: ['微观研究部', '化学测试组', '物理测试组', '电化学分析部', '结构分析中心'],
      containerRefs: ['chartSEM', 'chartCF', 'chartWH', 'chartDHX', 'chartJG'],
      charts: [],
    }
  },
  created() {
    // 默认展示当年数据，获取当年1号、今天日期
    const firstDayOfYear = moment().startOf('year'); // 当年1号
    const today = moment();// 当前日期
    this.timeRange = [firstDayOfYear, today];
    // 传递到后端格式：YYYY-MM-DD
    this.timeRangeBegin = firstDayOfYear.format('YYYY-MM-DD');
    this.timeRangeEnd = today.format('YYYY-MM-DD');

    this.getTestQuantityStatistics()
  },
  computed: {},
  mounted() {
    this.initCharts()
    //窗口尺寸改变
    window.addEventListener("resize", () => {
      this.chartsResize()
    })
  },
  methods: {
    chartTypeChange(value) {
      this.chartType = value
      this.chartLoading = true
      this.resetChartsOption()
      this.chartLoading = false
    },
    timeRangeChange(a, b) {
      this.timeRangeBegin = b[0]
      this.timeRangeEnd = b[1]
      this.getTestQuantityStatistics()
    },
    timeDimensionChange() {
      this.getTestQuantityStatistics()
    },
    deptPeopleNumChange() {
      this.chartLoading = true
      this.resetChartsOption()
      this.chartLoading = false
    },
    initCharts() {
      this.analysisRooms.forEach((room, index) => {
        let containerRef = this.containerRefs[index]
        let chart = echarts.init(this.$refs[containerRef])

        let option = {
          dataset: {
            source: []
          },
          toolbox: {
            feature: {
              saveAsImage: {}
            }
          },
          grid: {
            left: '6%',
            right: '8%',
            bottom: '10%',
            containLabel: true
          },
          tooltip: {
            trigger: 'axis',
            formatter: function (params) {
              let data = params[0];
              return (
                `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${data.color};"></span>` +
                data.value[0] + "：" + data.value[index + 1]
              )
            },
          },
          title: {
            text: room + this.chartType,
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal'
            },
            top: 5
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            axisLabel: {
              fontSize: 11,
            }
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              fontSize: 12
            }
          },
          series: [
            {
              type: 'line',
              encode: {
                x: 'period',
                y: room
              },
            }
          ],
        }
        chart.setOption(option)
        this.charts.push(chart)
      })
    },
    chartsResize() {
      this.charts.forEach((chart) => {
        chart.resize()
      })
    },
    resetChartsOption() {
      let datasource = []
      datasource.push(['period', ...this.analysisRooms])
      if (this.chartType === "计划达成率") {
        this.testQuantityStatistics.forEach(item => {
          let tmp = [item.period]
          this.analysisRooms.forEach(room => {
            tmp.push(this.roundToFixed(item[room].PLANCOUNT == 0 ? 0 : item[room].REALCOUNT / item[room].PLANCOUNT, 3))
          })
          datasource.push(tmp)
        })
      } else if (this.chartType === "人员效率") {
        this.testQuantityStatistics.forEach(item => {
          let tmp = [item.period]
          this.analysisRooms.forEach((room, index) => {
            tmp.push(this.roundToFixed(this.deptPeopleNums[index] == 0 ? 0 : item[room].REALCOUNT / this.deptPeopleNums[index], 1))
          })
          datasource.push(tmp)
        })
      } else if (this.chartType === "测试及时率") {
        this.testQuantityStatistics.forEach(item => {
          let tmp = [item.period]
          this.analysisRooms.forEach(room => {
            tmp.push(this.roundToFixed(item[room].REALCOUNT == 0 ? 0 : item[room].INTIMECOUNT / item[room].REALCOUNT, 3))
          })
          datasource.push(tmp)
        })
      }
      // 计划达成率、测试及时率趋势图需要修改样式，tooltip提示内容中加 % 百分号
      let isShowingPercent = this.chartType !== "人员效率"
      this.charts.forEach((chart, index) => {
        let option = {
          dataset: {
            source: datasource
          },
          tooltip: {
            trigger: 'axis',
            formatter: function (params) {
              let data = params[0];
              return (
                `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${data.color};"></span>` +
                data.value[0] + "：" + (isShowingPercent ? (data.value[index + 1] * 100).toFixed(1) + "%" : data.value[index + 1])
              )
            },
          },
          title: {
            text: this.analysisRooms[index] + this.chartType,
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal'
            },
            top: 5
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              fontSize: 12
            }
          },
          series: [
            {
              type: 'line',
              encode: {
                x: 'period',
                y: this.analysisRooms[index]
              },
            }
          ]
        }
        chart.setOption(option)
      })
    },
    getTestQuantityStatistics() {
      this.chartLoading = true
      getTestQuantityStatistics({
        timeRangeBegin: this.timeRangeBegin,
        timeRangeEnd: this.timeRangeEnd,
        timeDimension: this.timeDimension
      }).then((res) => {
        this.testQuantityStatistics = res.data
        this.resetChartsOption()
        this.chartLoading = false
      })
    },
    roundToFixed(num, precision) {
      let factor = Math.pow(10, precision);
      let roundedNum = Math.round(num * factor) / factor;
      return roundedNum.toFixed(precision); // 确保结果具有指定位数的小数
    },
  }
}
</script>

<style lang="less" scoped>
@import '/src/components/pageTool/style/pbiSearchItem.less';

.chart-row {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  height: 50%;
}

.chart-item {
  border-top: 8px solid rgb(92 129 203);
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  border-left: 1px solid #e5e5e5;
  border-right: 1px solid #e5e5e5;
  border-bottom: 1px solid #e5e5e5;
  margin: 12px 6px 0px 6px;
  width: 32%;
  min-width: 395px;
}

.chart-div {
  min-height: 294px;
}

.operate-row {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

/deep/ .operate-row .ant-input-number {
  margin-top: 10px;
  width: 80px;
  font-size: 12px;
}

.label {
  margin-top: 10px;
  width: 120px;
  text-align: right;
  font-size: 12px;
  color: #333;
}

/deep/ .ant-input {
  font-size: 12px !important;
}
</style>