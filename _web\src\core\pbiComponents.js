import Vue from 'vue'

// //引入ag-grid-vue
import { AgGridVue } from 'ag-grid-vue';

// 引入ag-grid的样式文件
import '/src/components/pageTool/style/agTable.css'

// 通用简单的表格模板
import tableIndex from '/src/components/pageTool/pageTemplate/tableIndex.vue'

// 通用简单的tabs模板（侧栏）
import tabsIndex from '/src/components/pageTool/pageTemplate/tabsIndex.vue'

// 通用简单的tabs模板（横线版本）
import lineTabsIndex from '/src/components/pageTool/pageTemplate/lineTabsIndex.vue'

// 标题
import pbiTitle from '@/components/pageTool/components/pbiTitle.vue'

// 按钮类型的tabs
//import pbiBtnTabs from '@/components/pageTool/components/pbiBtnTabs.vue'

// tabs
//import pbiTabs from '@/components/pageTool/components/pbiTabs1.vue'

// 表格操作列
import pbiTableActionBtn from '@/components/pageTool/components/pbiTableActionBtn.vue'
// 表格有处理事件的列
import pbiTableActionLink from '@/components/pageTool/components/pbiTableActionLink.vue'


// 分页
import pbiPagination from '@/components/pageTool/components/pbiPagination.vue'

//  筛选框
import pbiSearchContainer from '/src/components/pageTool/components/pbiSearchContainer.vue'
import pbiSearchItem from '/src/components/pageTool/components/pbiSearchItem.vue'

import pbiEmpty from '/src/components/pageTool/components/pbiEmpty.vue'
Vue.component('ag-grid-vue',AgGridVue)
Vue.component('tabsIndex',tabsIndex)
Vue.component('tableIndex',tableIndex)
Vue.component('lineTabsIndex',lineTabsIndex)
Vue.component('pbiTitle',pbiTitle)
//Vue.component('pbiTabs',pbiTabs)
//Vue.component('pbiBtnTabs',pbiBtnTabs)
Vue.component('pbiTableActionBtn',pbiTableActionBtn)
Vue.component('pbiTableActionLink',pbiTableActionLink)
Vue.component('pbiPagination',pbiPagination)
Vue.component('pbiSearchContainer',pbiSearchContainer)
Vue.component('pbiSearchItem',pbiSearchItem)
Vue.component('pbiEmpty',pbiEmpty)

// ag-grid  文字提示处理
Vue.prototype.pbiTooltip = (params) =>{
  // console.log(params.value)
  if(params.value == undefined || params.value == 'null' || params.value == '') return params.value
  const nowWidth = params.column.getActualWidth()
  const width = params.value.toString().split('').length * 12 + 24
  if(width > nowWidth){
      return params.value
  }else{
      return null
  }
}

// ag-grid  重新渲染表格
Vue.prototype.pbiRefreshCells = (params) => {
  params.api.refreshCells();
}

