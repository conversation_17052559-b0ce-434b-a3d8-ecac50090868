/**
 * 系数处理相关的通用方法
 */
export default {
  methods: {
    /**
     * 将参数名转换为LaTeX格式
     * @param {string} paramName 参数名
     * @returns {string} LaTeX格式的参数名
     */
    convertToLatex(paramName) {
      if (paramName.includes('$') || paramName.includes('_{')) {
        return paramName;
      }

      const match = paramName.match(/^([A-Za-z]+)(\d+)$/);
      if (match) {
        const letter = match[1];
        const number = match[2];
        return `${letter}_{${number}}`;
      }

      return paramName;
    },

    /**
     * 渲染LaTeX公式
     * @param {string} formula 公式文本
     * @returns {string} 渲染后的HTML
     */
    renderLatex(formula) {
      if (!formula) return '';

      let tex = String(formula).trim();

      if (tex.startsWith('$') && tex.endsWith('$')) {
        tex = tex.substring(1, tex.length - 1);
      }

      if (!tex.includes('_{')) {
        tex = tex.replace(/([A-Za-z])_([A-Za-z0-9]+)/g, '$1_{$2}');
      }

      return `$${tex}$`;
    },

    /**
     * 查找系数所属的组别字母
     * @param {string} name 系数名称
     * @returns {string} 组别字母
     */
    findGroupLetterForCoefficient(name) {
      if (!name) return '';

      if (name.includes('_{')) {
        return name.charAt(0).toUpperCase();
      }

      if (name.includes('_')) {
        return name.split('_')[0].charAt(0).toUpperCase();
      }

      return name.charAt(0).toUpperCase();
    },

    /**
     * 按字母对系数进行分组
     * @param {Array} coefficients 系数数组
     * @returns {Array} 分组后的系数数组
     */
    groupCoefficientsByLetter(coefficients) {
      if (!coefficients?.length) return [];

      const groupMap = {};

      coefficients.forEach(coef => {
        if (coef.type === 'coefficient' && coef.name) {
          const groupLetter = this.findGroupLetterForCoefficient(coef.name);

          if (!groupMap[groupLetter]) {
            groupMap[groupLetter] = {
              letter: groupLetter,
              coefficients: []
            };
          }

          groupMap[groupLetter].coefficients.push(coef);
        }
      });

      return Object.values(groupMap).sort((a, b) => a.letter.localeCompare(b.letter));
    }
  }
};
