<template>
  <a-empty :image="require('@/assets/images/others/empty.png')" :description="false"></a-empty>
</template>

<script>
  export default {
    name: 'pbiEmpty',
    data() {
      return {}
    },
    props:{
      height:{
        type:[ String , Number ],
        default: "200"
      }
    },
    mounted() {
      document.documentElement.style.setProperty(`--height`, `${this.height}px`)
    }
  }
</script>

<style lang="less" scoped>
  :root {
    --height: 200px;
  }
  /deep/.ant-empty-image{
    height: var(--height);
  }
</style>