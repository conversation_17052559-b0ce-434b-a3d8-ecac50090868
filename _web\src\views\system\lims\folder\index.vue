<template>
  <div>
    <tableIndex
      :pageLevel='1'
      :tableTotal='tableTotal'
      :pageTitleShow=false
      :loading='loading'
      :otherHeight="parseInt(50 + width)"
      @paginationChange="handlePageChange"
      @paginationSizeChange="handlePageChange"
    >
      <template #search>
        <pbiSearchContainer>
          <pbiSearchItem :span="6" label='委托单号'>
            <a-input v-model="queryParam.folderno" @keyup.enter="getList()" @change="getList()"/>
          </pbiSearchItem>
          <pbiSearchItem :span="6" label='委托人'>
            <a-input v-model="queryParam.createdbyname" @keyup.enter="getList()" @change="getList()"/>
          </pbiSearchItem>
          <pbiSearchItem :span="6" label='主题' >
            <a-input v-model="queryParam.theme" @keyup.enter="getList()" @change="getList()"/>
          </pbiSearchItem>

          <pbiSearchItem :span="6" type='btn'>
            <a-button style="margin-right: 12px;" @click="getList()" type="primary">查询</a-button>
            <a-button @click="reset" >重置</a-button>
          </pbiSearchItem>

        </pbiSearchContainer>
      </template>


      <template #table>
        <ag-grid-vue :style="{height:tableHeight}"
                     class='table ag-theme-balham'
                     :tooltipShowDelay="0"
                     :columnDefs="columns"
                     :rowData='rowData'
                     rowSelection="multiple"
                     :gridOptions="gridOptions"
                     @grid-ready="onGridReady"
                     :defaultColDef='defaultColDef'>
        </ag-grid-vue>
      </template>
    </tableIndex>
    <test-data ref="testData" @ok="handleOk"/>
  </div>
</template>
<script>
import {
  tLimsFolderListPage
} from '@/api/modular/system/limsManager'

  import testData from './testData'

  export default {
    components: {
      testData,
      folderNo:{
       template:'<a @click="params.openData(params.data.id)" >{{params.value}}</a>'
      }
    },
    props: {
      width:{
        type: Number,
        default: 0
      },
      padding:{
        type: String,
        default: '8px'
      }
    },
    data() {
      return {
        tableHeight: (document.body.clientHeight - 100) +'px' ,
        rowData:[],
        tableTotal:0,
        record:{},
        loading: false,
        pageNo: 1,
        pageSize: 20,
        gridApi: null,
        columnApi: null,
        gridOptions: {
          suppressCellSelection: false
        },
        defaultColDef: {
          filter: false,
          floatingFilter: false,
          editable: false,
        },
        address: this.hasPerm('progress:all')?'all':'none',
        show: false,
        labelCol: {

          sm: {
            span: 11
          }
        },
        wrapperCol: {

          sm: {
            span: 13
          }
        },
        queryParam: {},
        data: [],
        headData: [],
        allAddress:null,
        // 表头
        columns: [
          {
            headerName: '序号',
            field: 'index',
            width: 60,
            cellRenderer: function (params) {
              return parseInt(params.node.id) + 1
            }
          }, {
            headerName: '委托单号',
            width: 130,
            field: 'folderno',
            cellRenderer: 'folderNo',
            cellRendererParams: {openData: this.openData},
          }, {
            headerName: '委托人',
            width: 130,
            field: 'createdbyname',
          }, {
            headerName: '委托部门',
            width: 160,
            field: 'createdbyorgname',
          }, {
            headerName: '主题',
            flex:1,
            field: 'theme',
            cellStyle: () =>  {return {textAlign:'left'}},
          }, {
            headerName: '检测实验室',
            width: 160,
            field: 'laboratory',
          }, {
            headerName: '产品型号',
            width: 150,
            field: 'producttype',
          }, {
            headerName: '测试型号',
            width: 150,
            field: 'testproducttype',
          }

        ],

        selectedRowKeys: [],
        selectedRows: [],
        height:'500px'

      }
    },
    created() {
    },
    computed: {

    },
    mounted() {
      this.getList()
      this.tableHeight =  (document.body.clientHeight - 150 - this.width) +'px'
    },
    methods:{
      onGridReady(params) {
        this.gridApi = params.api;
        this.columnApi = params.columnApi;
        // params.api.sizeColumnsToFit();
      },
      reset(){
        this.queryParam = {}
        this.loadData()
      },
      handlePageChange(value) {
        let {current, pageSize} = value
        this.pageNo = current
        this.pageSize = pageSize
        this.loadData()
      },
      loadData() {
        this.loading = true
        return tLimsFolderListPage({
          ...{
            pageNo: this.pageNo,
            pageSize: this.pageSize
          }, ...this.queryParam
        }).then((res) => {
          if (res.success) {
            this.rowData = res.data.rows
            this.tableTotal = res.data.totalRows

          }
        }).finally(() => {
          if(this.rowData.length == 0 && this.pageNo > 1){
            // this.pageNo -= 1
            this.pageNo = Math.max(1, Math.ceil(this.tableTotal / this.pageSize))
            this.loadData()
          }
          this.loading = false
        })
      },
      handleOk() {
        this.getList()
      },

      openData(folderId) {
        this.$refs.testData.query(folderId)
      },
      getList(flag) {

        this.loadData()
      },


    }
  }
</script>
<style lang="less" scoped=''>
@import '/src/components/pageTool/style/pbiSearchItem.less';

  .red {
    background-color: #ed0000;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .yellow {
    background-color: #ffc000;
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .grey {
    background-color: rgba(223, 223, 223, 0.25);
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .ant-modal-body {
    padding: 0;
  }




  .green{
    background-color: #58a55c;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

  }



</style>