<template>
  <a-modal title="测试项目选择" :width="1200" :height="600"
         :bodyStyle="{padding:0}"
         :visible="visible"
           style="padding: 0"
         :maskClosable="false"
         @cancel="handleCancel">

    <template slot="footer">

      <a-button key="back" @click="handleCancel">
        取消
      </a-button>

      <a-popconfirm key="primary" placement="topRight" ok-text="提交" cancel-text="取消" @confirm="handleSubmit">
        <template slot="title">
          <p>确定提交吗</p>
        </template>
        <a-button key="submit" type="primary">
          确定
        </a-button>
      </a-popconfirm>

    </template>

  <a-table :columns="columns"
           :data-source="data" bordered
           :rowKey="(record1) => record1.id"
           :row-selection="{ selectedRowKeys: selectedRowKeys, onChange:changeOrdTask,selectedRows: selectedRows,type:'radio', columnWidth:30}"
  >




  </a-table>

  </a-modal>
</template>

<script>
  import {
    getLimsOrdtaskListByParam
  } from '@/api/modular/system/limsManager'
  import {
    testProgressUpdateOnlyBean
  } from '@/api/modular/system/testProgressManager'

  export default {
    props: {

    },
    data() {
      return {
        folderNo:null,
        progressId:null,

        selectedRowKeys:[],
        selectedRows:[],
        data:[],
        loading:false,
        visible:false,
        columns: [
          {
            title: '序号',
            align: 'center',
            width: 30,
            customRender: (text, record, index) => index + 1
          }, {
            title: '委托单号',
            width: 60,
            align: 'center',
            dataIndex: 'folderno',
          }, {
            title: '一级分类',
            width: 60,
            align: 'center',
            dataIndex: 'firstcategory',
          }, {
            title: '二级分类',
            width: 60,
            align: 'center',
            dataIndex: 'secondcategory',
          }, {
            title: '测试员',
            width: 60,
            align: 'center',
            dataIndex: 'tester',
          },  {
            title: '测试项目编号',
            width: 60,
            align: 'center',
            dataIndex: 'testcode',
          }, {
            title: '测试项目名称',
            width: 90,
            align: 'center',
            dataIndex: 'testname'
          },{
            title: '测试项目别名',
            width: 90,
            align: 'center',
            dataIndex: 'alias'
          },{
            title: '温度',
            width: 60,
            align: 'center',
            dataIndex: 'tem'
          },{
            title: 'soc',
            width: 60,
            align: 'center',
            dataIndex: 'soc'
          },{
            title: '储存时间',
            width: 60,
            align: 'center',
            dataIndex: 'totalDay'
          }
        ],
        confirmLoading: false
      }
    },

    methods: {


      open(record) {
        if(null != record.ordtaskid){
          this.selectedRowKeys.push(record.ordtaskid)
        }
        this.folderNo = record.testCode
        this.progressId = record.id
        this.visible = true
        let param = {}
        param.folderno = record.testCode
        getLimsOrdtaskListByParam(param).then(res => {
          this.data = res.data
        })
      },
      changeOrdTask(selectedRowKeys, selectedRows){
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      },
      handleSubmit() {

        if(this.selectedRowKeys.length == 0){
          this.$message.error('请选择测试项目')
          return
        }

        this.confirmLoading = true

        let param = {}
        param.id = this.progressId
        param.ordtaskid = this.selectedRowKeys[0]
        testProgressUpdateOnlyBean(param).then((res) => {
          this.confirmLoading = false
          if (res.success) {
            this.$message.success('选择成功')
            this.$emit('ok')
            this.handleCancel()
          } else {
            this.$message.error('选择失败：' + res.message)
          }
        }).finally((res) => {
          this.confirmLoading = false
        })

      },
      handleCancel() {
        this.selectedRows =[]
        this.selectedRowKeys =[]
        this.visible = false
      }
    }
  }
</script>
<style lang="less" scoped>
  .ant-form-item {

    margin-bottom: 0px;

  }

  .man_button{
    padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;
  }

  /deep/ .ant-modal-body {
    padding: 0 !important;
  }

  /deep/ .ant-table-thead > tr > th, /deep/ .ant-table-tbody > tr > td {
    padding: 3px;
  }

  /deep/ .ant-table-footer {

    padding: 0px;
  }

  /deep/ .ant-table-pagination.ant-pagination {
    margin: 5px 0;
  }

  /deep/ .ant-input-number {
    width: 100%;
  }

  /deep/ .ant-input-number-sm > .ant-input-number-input-wrap > .ant-input-number-input {
    text-align: center;
  }

</style>