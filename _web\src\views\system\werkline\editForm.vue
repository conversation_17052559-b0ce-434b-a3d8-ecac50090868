<template>
  <a-modal
    title="分类编辑"
    :width="500"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">

        <a-form-item
          style="display: none;"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-input v-decorator="['id']" />
        </a-form-item>

        <a-form-item
          label="工厂"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-select
            disabled="disabled"
						v-decorator="[
              'werkNo',
              {
                rules: [
                  { required: true, message: '请选择工厂'},
                ],
              },
            ]"
						>
						<a-select-option value=''>选择工厂</a-select-option>
						<a-select-option v-for="(item, index) in werks" :key="index" :value="item.werks" >{{item.name1}}</a-select-option>
					</a-select>
        </a-form-item>
        <a-form-item
          label="产线"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入产线名" v-decorator="['lineName', {rules: [{required: true, message: '请输入产线名！'}]}]" />
        </a-form-item>

      </a-form>

    </a-spin>
  </a-modal>
</template>

<script>
import { sysWerklineEdit } from '@/api/modular/system/werklineManage'
  export default {
    props:{
      werks:{
        type:Array,
        default:[]
      }
    },
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 18 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      edit (record) {
         setTimeout(() => {
             this.form.setFieldsValue(
              {
                id: record.id,
                werkNo: record.werkNo,
                lineName:record.lineName
              }
            )
          }, 100)
        

          this.visible = true
      },

      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            sysWerklineEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.visible = false
                this.confirmLoading = false
                this.$emit('ok', values)
                this.form.resetFields()
              } else {
                this.$message.error('编辑失败：' + res.message)
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
