{"version": 3, "sources": ["webpack://[name]/webpack/universalModuleDefinition", "webpack://[name]/webpack/bootstrap", "webpack://[name]/./sources/utils/utils.js", "webpack://[name]/./sources/utils/dom_helpers.js", "webpack://[name]/./sources/utils/dom_event_scope.js", "webpack://[name]/./sources/utils/helpers.js", "webpack://[name]/./sources/ext/tooltip/tooltip.ts", "webpack://[name]/./sources/ext/tooltip/tooltipManager.ts", "webpack://[name]/./sources/ext/tooltip/index.ts"], "names": ["root", "factory", "exports", "module", "define", "amd", "window", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "seed", "helpers", "copy", "result", "isDate", "Date", "isArray", "Array", "length", "isStringObject", "String", "isNumberObject", "Number", "isBooleanObject", "Boolean", "apply", "defined", "obj", "mixin", "target", "source", "force", "f", "undefined", "uid", "valueOf", "functor", "arguments", "event", "el", "handler", "capture", "addEventListener", "attachEvent", "eventRemove", "removeEventListener", "detachEvent", "elementPosition", "elem", "top", "left", "right", "bottom", "getBoundingClientRect", "box", "body", "document", "doc<PERSON><PERSON>", "documentElement", "parentNode", "scrollTop", "pageYOffset", "scrollLeft", "pageXOffset", "clientTop", "clientLeft", "offsetWidth", "offsetHeight", "parseInt", "offsetTop", "offsetLeft", "offsetParent", "y", "Math", "round", "x", "width", "height", "isVisible", "node", "display", "visibility", "getComputedStyle", "style", "currentStyle", "hasNonNegativeTabIndex", "isNaN", "getAttribute", "<PERSON><PERSON><PERSON><PERSON>", "a", "area", "nodeName", "loLowerCase", "isEnabled", "input", "select", "textarea", "button", "toLowerCase", "hasAttribute", "getClassName", "className", "baseVal", "indexOf", "_trimString", "_slave", "createElement", "getTargetNode", "e", "tagName", "srcElement", "str", "trim", "this", "replace", "getNodePosition", "getFocusableNodes", "nodes", "querySelectorAll", "join", "nodesArray", "slice", "splice", "getScrollSize", "div", "cssText", "append<PERSON><PERSON><PERSON>", "clientWidth", "<PERSON><PERSON><PERSON><PERSON>", "addClassName", "removeClassName", "split", "regEx", "RegExp", "insertNode", "newone", "innerHTML", "child", "<PERSON><PERSON><PERSON><PERSON>", "removeNode", "getChildNodes", "css", "ch", "childNodes", "len", "out", "push", "toNode", "getElementById", "querySelector", "locateClassName", "classname", "strict", "trg", "ind", "char<PERSON>t", "locateAttribute", "attribute", "getRelativeEventPosition", "ev", "clientX", "clientY", "isChildOf", "parent", "hasClass", "element", "classList", "contains", "test", "closest", "selector", "matches", "msMatchesSelector", "webkitMatchesSelector", "parentElement", "nodeType", "console", "error", "utils", "createScope", "addEvent", "removeEvent", "handlers", "eventScope", "attach", "callback", "detach", "detachAll", "staticArray", "extend", "scopes", "units", "second", "minute", "hour", "day", "week", "month", "quarter", "year", "arrayFilter", "arr", "filter", "getSecondsInUnit", "unit", "for<PERSON>ach", "workArray", "arrayMap", "map", "resArray", "arrayFind", "find", "arrayDifference", "item", "arraySome", "hashToArray", "hash", "sortArrayOfHash", "field", "desc", "compare", "b", "sort", "throttle", "timeout", "wait", "setTimeout", "pop", "getFullYear", "getMonth", "getDate", "Function", "toString", "constructor", "delay", "timer", "$cancelTimeout", "$pending", "args", "clearTimeout", "$execute", "objectKeys", "keys", "requestAnimationFrame", "w", "webkitRequestAnimationFrame", "msRequestAnimationFrame", "mozRequestAnimationFrame", "oRequestAnimationFrame", "cb", "isEventable", "domHelpers", "<PERSON><PERSON><PERSON>", "getNode", "_tooltipNode", "gantt", "_wai<PERSON><PERSON>", "tooltipAttr", "setViewport", "_root", "show", "container", "hide", "_isLikeMouseEvent", "position", "_calculateTooltipPosition", "tooltipVisibleAttr", "tooltipHiddenAttr", "<PERSON><PERSON><PERSON><PERSON>", "html", "_getViewPort", "viewport", "_getViewPortSize", "tooltipNode", "tooltip", "offsetX", "config", "tooltip_offset_x", "offsetY", "tooltip_offset_y", "mouse", "containerPos", "scrollY", "pos", "scrollX", "$task_data", "$task", "domEventsScope", "tooltip_1", "TooltipManager", "_listeners", "_domEvents", "_initDelayedFunctions", "destructor", "hideTooltip", "delayHide", "_this", "global", "$root", "watchable<PERSON>arget", "eventTarget", "targetNode", "doOnMouseEnter", "onmouseenter", "<PERSON><PERSON><PERSON><PERSON>", "onmouseleave", "listener", "tooltipFor", "cloneDomEvent", "clone", "createEvent", "delayShow", "callEvent", "tooltip_timeout", "tooltip_hide_timeout", "tooltipManager", "ext", "tooltips", "task_attribute", "touch", "touch_tooltip", "targetTaskId", "locate", "isTaskExists", "task", "getTask", "templates", "tooltip_text", "start_date", "end_date", "getState", "link_source_id"], "mappings": ";;;;;;;;;;;;CAAA,SAAAA,EAAAC,GACA,iBAAAC,SAAA,iBAAAC,OACAA,OAAAD,QAAAD,IACA,mBAAAG,eAAAC,IACAD,OAAA,6BAAAH,GACA,iBAAAC,QACAA,QAAA,2BAAAD,IAEAD,EAAA,2BAAAC,IARA,CASCK,OAAA,WACD,mBCTA,IAAAC,KAGA,SAAAC,EAAAC,GAGA,GAAAF,EAAAE,GACA,OAAAF,EAAAE,GAAAP,QAGA,IAAAC,EAAAI,EAAAE,IACAC,EAAAD,EACAE,GAAA,EACAT,YAUA,OANAU,EAAAH,GAAAI,KAAAV,EAAAD,QAAAC,IAAAD,QAAAM,GAGAL,EAAAQ,GAAA,EAGAR,EAAAD,QA0DA,OArDAM,EAAAM,EAAAF,EAGAJ,EAAAO,EAAAR,EAGAC,EAAAQ,EAAA,SAAAd,EAAAe,EAAAC,GACAV,EAAAW,EAAAjB,EAAAe,IACAG,OAAAC,eAAAnB,EAAAe,GAA0CK,YAAA,EAAAC,IAAAL,KAK1CV,EAAAgB,EAAA,SAAAtB,GACA,oBAAAuB,eAAAC,aACAN,OAAAC,eAAAnB,EAAAuB,OAAAC,aAAwDC,MAAA,WAExDP,OAAAC,eAAAnB,EAAA,cAAiDyB,OAAA,KAQjDnB,EAAAoB,EAAA,SAAAD,EAAAE,GAEA,GADA,EAAAA,IAAAF,EAAAnB,EAAAmB,IACA,EAAAE,EAAA,OAAAF,EACA,KAAAE,GAAA,iBAAAF,QAAAG,WAAA,OAAAH,EACA,IAAAI,EAAAX,OAAAY,OAAA,MAGA,GAFAxB,EAAAgB,EAAAO,GACAX,OAAAC,eAAAU,EAAA,WAAyCT,YAAA,EAAAK,UACzC,EAAAE,GAAA,iBAAAF,EAAA,QAAAM,KAAAN,EAAAnB,EAAAQ,EAAAe,EAAAE,EAAA,SAAAA,GAAgH,OAAAN,EAAAM,IAAqBC,KAAA,KAAAD,IACrI,OAAAF,GAIAvB,EAAA2B,EAAA,SAAAhC,GACA,IAAAe,EAAAf,KAAA2B,WACA,WAA2B,OAAA3B,EAAA,SAC3B,WAAiC,OAAAA,GAEjC,OADAK,EAAAQ,EAAAE,EAAA,IAAAA,GACAA,GAIAV,EAAAW,EAAA,SAAAiB,EAAAC,GAAsD,OAAAjB,OAAAkB,UAAAC,eAAA1B,KAAAuB,EAAAC,IAGtD7B,EAAAgC,EAAA,aAIAhC,IAAAiC,EAAA,yBClFA,IAgDAC,EAhDAC,EAAAnC,EAAA,GAiFAL,EAAAD,SACA0C,KAhFA,SAAAA,EAAAR,GACA,IAAA1B,EAAAmC,EAEA,GAAAT,GAAA,iBAAAA,EAEA,WACA,KAAAO,EAAAG,OAAAV,GACAS,EAAA,IAAAE,KAAAX,GACA,MACA,KAAAO,EAAAK,QAAAZ,GAEA,IADAS,EAAA,IAAAI,MAAAb,EAAAc,QACAxC,EAAA,EAAcA,EAAA0B,EAAAc,OAAmBxC,IACjCmC,EAAAnC,GAAAkC,EAAAR,EAAA1B,IAEA,MACA,KAAAiC,EAAAQ,eAAAf,GACAS,EAAA,IAAAO,OAAAhB,GACA,MACA,KAAAO,EAAAU,eAAAjB,GACAS,EAAA,IAAAS,OAAAlB,GACA,MACA,KAAAO,EAAAY,gBAAAnB,GACAS,EAAA,IAAAW,QAAApB,GACA,MACA,QAEA,IAAA1B,KADAmC,KACAT,EACAhB,OAAAkB,UAAAC,eAAAkB,MAAArB,GAAA1B,MACAmC,EAAAnC,GAAAkC,EAAAR,EAAA1B,KAKA,OAAAmC,GAAAT,GAgDAsB,QAvCA,SAAAC,GACA,mBAuCAC,MA9CA,SAAAC,EAAAC,EAAAC,GACA,QAAAC,KAAAF,QACAG,IAAAJ,EAAAG,IAAAD,KAAAF,EAAAG,GAAAF,EAAAE,IACA,OAAAH,GA4CAK,IApCA,WAKA,OAJAxB,IACAA,GAAA,IAAAK,MAAAoB,aAEAzB,GAiCAR,KA5BA,SAAAkC,EAAAhC,GACA,OAAAgC,EAAAlC,KACAkC,EAAAlC,KAAAE,GAEA,WAAoB,OAAAgC,EAAAX,MAAArB,EAAAiC,aAyBpBC,MAtBA,SAAAC,EAAAD,EAAAE,EAAAC,GACAF,EAAAG,iBACAH,EAAAG,iBAAAJ,EAAAE,OAAAP,IAAAQ,MAEAF,EAAAI,aACAJ,EAAAI,YAAA,KAAAL,EAAAE,IAkBAI,YAfA,SAAAL,EAAAD,EAAAE,EAAAC,GACAF,EAAAM,oBACAN,EAAAM,oBAAAP,EAAAE,OAAAP,IAAAQ,MAEAF,EAAAO,aACAP,EAAAO,YAAA,KAAAR,EAAAE,sBC7EA,SAAAO,EAAAC,GACA,IAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EACA,GAAAJ,EAAAK,sBAAA,CACA,IAAAC,EAAAN,EAAAK,wBACAE,EAAAC,SAAAD,KACAE,EAAAD,SAAAE,iBACAF,SAAAD,KAAAI,YACAH,SAAAD,KAEAK,EAAAtF,OAAAuF,aAAAJ,EAAAG,WAAAL,EAAAK,UACAE,EAAAxF,OAAAyF,aAAAN,EAAAK,YAAAP,EAAAO,WACAE,EAAAP,EAAAO,WAAAT,EAAAS,WAAA,EACAC,EAAAR,EAAAQ,YAAAV,EAAAU,YAAA,EACAhB,EAAAK,EAAAL,IAAAW,EAAAI,EACAd,EAAAI,EAAAJ,KAAAY,EAAAG,EAEAd,EAAAK,SAAAD,KAAAW,YAAAZ,EAAAH,MACAC,EAAAI,SAAAD,KAAAY,aAAAb,EAAAF,WACE,CACF,KAAAJ,GACAC,GAAAmB,SAAApB,EAAAqB,UAAA,IACAnB,GAAAkB,SAAApB,EAAAsB,WAAA,IACAtB,IAAAuB,aAGApB,EAAAK,SAAAD,KAAAW,YAAAlB,EAAAkB,YAAAhB,EACAE,EAAAI,SAAAD,KAAAY,aAAAnB,EAAAmB,aAAAlB,EAEA,OAASuB,EAAAC,KAAAC,MAAAzB,GAAA0B,EAAAF,KAAAC,MAAAxB,GAAA0B,MAAA5B,EAAAkB,YAAAW,OAAA7B,EAAAmB,aAAAhB,MAAAsB,KAAAC,MAAAvB,GAAAC,OAAAqB,KAAAC,MAAAtB,IAGT,SAAA0B,EAAAC,GACA,IAAAC,GAAA,EACAC,GAAA,EACA,GAAA3G,OAAA4G,iBAAA,CACA,IAAAC,EAAA7G,OAAA4G,iBAAAH,EAAA,MACAC,EAAAG,EAAA,QACAF,EAAAE,EAAA,gBACEJ,EAAAK,eACFJ,EAAAD,EAAAK,aAAA,QACAH,EAAAF,EAAAK,aAAA,YAEA,cAAAJ,GAAA,UAAAC,EAGA,SAAAI,EAAAN,GACA,OAAAO,MAAAP,EAAAQ,aAAA,gBAAAR,EAAAQ,aAAA,eAGA,SAAAC,EAAAT,GAEA,QADoBU,GAAA,EAAAC,MAAA,GACpBX,EAAAY,SAAAC,kBACAb,EAAAQ,aAAA,QAKA,SAAAM,EAAAd,GAEA,QADmBe,OAAA,EAAAC,QAAA,EAAAC,UAAA,EAAAC,QAAA,EAAA7F,QAAA,GACnB2E,EAAAY,SAAAO,iBACAnB,EAAAoB,aAAA,YA4CA,SAAAC,EAAArB,GACA,IAAAA,EAAA,SAEA,IAAAsB,EAAAtB,EAAAsB,WAAA,GAOA,OANAA,EAAAC,UACAD,IAAAC,SAEAD,EAAAE,UACAF,EAAA,IAEAG,EAAAH,GAgCA,IAAAI,EAAAjD,SAAAkD,cAAA,OA2BA,SAAAC,EAAAC,GAQA,OANAA,EAAAC,QACAD,GAEAA,KAAAtI,OAAAgE,OACAT,QAAA+E,EAAAE,WAoBA,SAAAN,EAAAO,GAEA,OADA3F,OAAAd,UAAA0G,MAAA,WAAgD,OAAAC,KAAAC,QAAA,mBAChDzF,MAAAsF,GA4EA5I,EAAAD,SACAiJ,gBAAApE,EACAqE,kBArNA,SAAApJ,GAgBA,IAfA,IAAAqJ,EAAArJ,EAAAsJ,kBACA,UACA,aACA,QACA,SACA,WACA,SACA,SACA,SACA,QACA,aACA,qBACAC,KAAA,OAEAC,EAAAvG,MAAAX,UAAAmH,MAAA5I,KAAAwI,EAAA,GACA3I,EAAA,EAAeA,EAAA8I,EAAAtG,OAAuBxC,IAAA,CACtC,IAAAqG,EAAAyC,EAAA9I,IACA2G,EAAAN,IAAAc,EAAAd,IAAAS,EAAAT,KAAAD,EAAAC,KAEAyC,EAAAE,OAAAhJ,EAAA,GACAA,KAGA,OAAA8I,GA8LAG,cA3LA,WACA,IAAAC,EAAApE,SAAAkD,cAAA,OACAkB,EAAAzC,MAAA0C,QAAA,uIAEArE,SAAAD,KAAAuE,YAAAF,GACA,IAAAhD,EAAAgD,EAAA1D,YAAA0D,EAAAG,YAGA,OAFAvE,SAAAD,KAAAyE,YAAAJ,GAEAhD,GAoLAwB,eACA6B,aArKA,SAAAlD,EAAAsB,GACAA,IAAA,IAAAtB,EAAAsB,UAAAE,QAAAF,KACAtB,EAAAsB,WAAA,IAAAA,IAoKA6B,gBAhKA,SAAAnD,EAAA9F,GACAA,IAAAkJ,MAAA,KACA,QAAAzJ,EAAA,EAAgBA,EAAAO,EAAAiC,OAAiBxC,IAAA,CACjC,IAAA0J,EAAA,IAAAC,OAAA,UAAApJ,EAAAP,GAAA,mBACAqG,EAAAsB,UAAAtB,EAAAsB,UAAAa,QAAAkB,EAAA,MA6JAE,WAzIA,SAAAvD,EAAAwD,GACA9B,EAAA+B,UAAAD,EACA,IAAAE,EAAAhC,EAAAiC,WAEA,OADA3D,EAAA+C,YAAAW,GACAA,GAsIAE,WAnIA,SAAA5D,GACAA,KAAApB,YACAoB,EAAApB,WAAAqE,YAAAjD,IAkIA6D,cA9HA,SAAA7D,EAAA8D,GAIA,IAHA,IAAAC,EAAA/D,EAAAgE,WACAC,EAAAF,EAAA5H,OACA+H,KACAvK,EAAA,EAAgBA,EAAAsK,EAAStK,IAAA,CACzB,IAAAiD,EAAAmH,EAAApK,GACAiD,EAAA0E,YAAA,IAAA1E,EAAA0E,UAAAE,QAAAsC,IACAI,EAAAC,KAAAvH,GAGA,OAAAsH,GAqHAE,OApJA,SAAApE,GACA,uBAAAA,EACAvB,SAAA4F,eAAArE,IAAAvB,SAAA6F,cAAAtE,IAAAvB,SAAAD,KAEAwB,GAAAvB,SAAAD,MAiJA+F,gBApFA,SAAA1C,EAAA2C,EAAAC,GACA,IAAAC,EAAA9C,EAAAC,GACAiC,EAAA,GAKA,SAHA5G,IAAAuH,IACAA,GAAA,GAEAC,GAAA,CAEA,GADAZ,EAAAzC,EAAAqD,GACA,CACA,IAAAC,EAAAb,EAAAtC,QAAAgD,GACA,GAAAG,GAAA,GACA,IAAAF,EACA,OAAAC,EAGA,IAAAvG,EAAA,IAAAwG,IAAAlD,EAAAqC,EAAAc,OAAAD,EAAA,IACAvG,EAAAuG,EAAAH,EAAArI,QAAA2H,EAAA3H,SAAAsF,EAAAqC,EAAAc,OAAAD,EAAAH,EAAArI,SAEA,GAAAgC,GAAAC,EACA,OAAAsG,GAGAA,IAAA9F,WAEA,aA4DAiG,gBAzGA,SAAAhD,EAAAiD,GACA,GAAAA,EAAA,CAIA,IAFA,IAAAJ,EAAA9C,EAAAC,GAEA6C,GAAA,CACA,GAAAA,EAAAlE,cACAkE,EAAAlE,aAAAsE,GACA,OAAAJ,EAEAA,IAAA9F,WAEA,cA8FAgD,gBACAmD,yBAxDA,SAAAC,EAAAhF,GACA,IAAA/F,EAAAwE,SAAAE,gBACAJ,EAAAP,EAAAgC,GAEA,OACAJ,EAAAoF,EAAAC,QAAAhL,EAAA8E,WAAA9E,EAAAiF,WAAAX,EAAAqB,EAAAI,EAAAjB,WACAU,EAAAuF,EAAAE,QAAAjL,EAAA4E,UAAA5E,EAAAgF,UAAAV,EAAAkB,EAAAO,EAAAnB,YAmDAsG,UA/CA,SAAAzB,EAAA0B,GACA,IAAA1B,IAAA0B,EACA,SAGA,KAAA1B,MAAA0B,GACA1B,IAAA9E,WAGA,OAAA8E,IAAA0B,GAuCAC,SAlKA,SAAAC,EAAAhE,GACA,oBAAAgE,EACAA,EAAAC,UAAAC,SAAAlE,GAEA,IAAAgC,OAAA,MAAAhC,EAAA,OAAAmE,KAAAH,EAAAhE,YA+JAoE,QArCA,SAAAJ,EAAAK,GACA,GAAAL,EAAAI,QACA,OAAAJ,EAAAI,QAAAC,GACE,GAAAL,EAAAM,SAAAN,EAAAO,mBAAAP,EAAAQ,sBAAA,CACF,IAAAtI,EAAA8H,EACA,IAAA7G,SAAAE,gBAAA6G,SAAAhI,GAAA,YACA,GAGA,IAFAA,EAAAoI,SAAApI,EAAAqI,mBAAArI,EAAAsI,uBAEAhM,KAAA0D,EAAAmI,GAAA,OAAAnI,EACAA,IAAAuI,eAAAvI,EAAAoB,iBACG,OAAApB,GAAA,IAAAA,EAAAwI,UACH,YAIA,OADAC,QAAAC,MAAA,iCACA,2BClRA,IAAAC,EAAA1M,EAAA,GA+CAL,EAAAD,QA7CA,SAAAiN,EAAAC,EAAAC,GACAD,KAAAF,EAAA5I,MACA+I,KAAAH,EAAAtI,YAEA,IAAA0I,KAEAC,GACAC,OAAA,SAAAjJ,EAAAD,EAAAmJ,EAAAhJ,GACA6I,EAAApC,MAAkBmB,QAAA9H,EAAAD,QAAAmJ,WAAAhJ,YAClB2I,EAAA7I,EAAAD,EAAAmJ,EAAAhJ,IAEAiJ,OAAA,SAAAnJ,EAAAD,EAAAmJ,EAAAhJ,GACA4I,EAAA9I,EAAAD,EAAAmJ,EAAAhJ,GACA,QAAA/D,EAAA,EAAiBA,EAAA4M,EAAApK,OAAqBxC,IAAA,CACtC,IAAA8D,EAAA8I,EAAA5M,GACA8D,EAAA6H,UAAA9H,GAAAC,EAAAF,WAAAE,EAAAiJ,cAAAjJ,EAAAC,cACA6I,EAAA5D,OAAAhJ,EAAA,GACAA,OAIAiN,UAAA,WAGA,IAFA,IAAAC,EAAAN,EAAA7D,QAEA/I,EAAA,EAAkBA,EAAAkN,EAAA1K,OAAwBxC,IAAA,CAC1C,IAAA8D,EAAAoJ,EAAAlN,GACA6M,EAAAG,OAAAlJ,EAAA6H,QAAA7H,EAAAF,MAAAE,EAAAiJ,SAAAjJ,EAAAC,SACA8I,EAAAG,OAAAlJ,EAAA6H,QAAA7H,EAAAF,MAAAE,EAAAiJ,cAAAxJ,GACAsJ,EAAAG,OAAAlJ,EAAA6H,QAAA7H,EAAAF,MAAAE,EAAAiJ,UAAA,GACAF,EAAAG,OAAAlJ,EAAA6H,QAAA7H,EAAAF,MAAAE,EAAAiJ,UAAA,GAEAH,EAAA5D,OAAA,EAAA4D,EAAApK,SAEA2K,OAAA,WACA,OAAAV,EAAAlE,KAAA3E,MAAA2E,KAAArE,eAQA,OAJAtE,OAAAwN,SACAxN,OAAAwN,WAEAxN,OAAAwN,OAAA5C,KAAAoC,GACAC,oBC5CA,IAAAQ,GACAC,OAAA,EACAC,OAAA,GACAC,KAAA,KACAC,IAAA,MACAC,KAAA,OACAC,MAAA,OACAC,QAAA,OACAC,KAAA,SAgFA,SAAAC,EAAAC,EAAAhB,GACA,IAAA5K,KAEA,GAAA4L,EAAAC,OACA,OAAAD,EAAAC,OAAAjB,GAEA,QAAA/M,EAAA,EAAiBA,EAAA+N,EAAAvL,OAAgBxC,IACjC+M,EAAAgB,EAAA/N,QACAmC,IAAAK,QAAAuL,EAAA/N,IAGA,OAAAmC,EAkHA1C,EAAAD,SACAyO,iBA5MA,SAAAC,GACA,OAAAb,EAAAa,IAAAb,EAAAG,MA4MAW,QAzMA,SAAAJ,EAAAhB,GACA,GAAAgB,EAAAI,QACAJ,EAAAI,QAAApB,QAGA,IADA,IAAAqB,EAAAL,EAAAhF,QACA/I,EAAA,EAAiBA,EAAAoO,EAAA5L,OAAsBxC,IACvC+M,EAAAqB,EAAApO,OAoMAqO,SA/LA,SAAAN,EAAAhB,GACA,GAAAgB,EAAAO,IACA,OAAAP,EAAAO,IAAAvB,GAKA,IAHA,IAAAqB,EAAAL,EAAAhF,QACAwF,KAEAvO,EAAA,EAAiBA,EAAAoO,EAAA5L,OAAsBxC,IACvCuO,EAAA/D,KAAAuC,EAAAqB,EAAApO,OAEA,OAAAuO,GAsLAC,UAjLA,SAAAT,EAAAhB,GACA,GAAAgB,EAAAU,KACA,OAAAV,EAAAU,KAAA1B,GAEA,QAAA/M,EAAA,EAAiBA,EAAA+N,EAAAvL,OAAgBxC,IACjC,GAAA+M,EAAAgB,EAAA/N,MACA,OAAA+N,EAAA/N,IA4KA8N,cACAY,gBA7FA,SAAAX,EAAAhB,GACA,OAAAe,EAAAC,EAAA,SAAAY,EAAA3O,GACA,OAAA+M,EAAA4B,EAAA3O,MA4FA4O,UAzGA,SAAAb,EAAAhB,GACA,OAAAgB,EAAAvL,OAAA,SAEA,QAAAxC,EAAA,EAAgBA,EAAA+N,EAAAvL,OAAgBxC,IAChC,GAAA+M,EAAAgB,EAAA/N,KAAA+N,GACA,SAGA,UAkGAc,YAtHA,SAAAC,GACA,IAAA3M,KAEA,QAAAZ,KAAAuN,EACAA,EAAAjN,eAAAN,IACAY,EAAAqI,KAAAsE,EAAAvN,IAIA,OAAAY,GA8GA4M,gBAlDA,SAAAhB,EAAAiB,EAAAC,GACA,IAAAC,EAAA,SAAAnI,EAAAoI,GACA,OAAApI,EAAAoI,GAGApB,EAAAqB,KAAA,SAAArI,EAAAoI,GACA,OAAApI,EAAAiI,KAAAG,EAAAH,GAAA,EAEAC,EAAAC,EAAAnI,EAAAiI,GAAAG,EAAAH,IAAAE,EAAAC,EAAAH,GAAAjI,EAAAiI,OA2CAK,SA3FA,SAAAtC,EAAAuC,GACA,IAAAC,GAAA,EAEA,kBACAA,IACAxC,EAAAhK,MAAA,KAAAY,WACA4L,GAAA,EACAC,WAAA,WACAD,GAAA,GACID,MAmFJhN,QA3KA,SAAAW,GACA,OAAAV,MAAAD,QACAC,MAAAD,QAAAW,GAGAA,QAAAM,IAAAN,EAAAT,QAAAS,EAAAwM,KAAAxM,EAAAuH,MAuKApI,OAjJA,SAAAa,GACA,SAAAA,GAAA,iBAAAA,KACAA,EAAAyM,aAAAzM,EAAA0M,UAAA1M,EAAA2M,WAgJAnN,eAnKA,SAAAQ,GACA,OAAAA,GAAA,iBAAAA,GACA,wCAAA4M,SAAAjO,UAAAkO,SAAA3P,KAAA8C,EAAA8M,cAkKApN,eA9JA,SAAAM,GACA,OAAAA,GAAA,iBAAAA,GACA,wCAAA4M,SAAAjO,UAAAkO,SAAA3P,KAAA8C,EAAA8M,cA6JAlN,gBAzJA,SAAAI,GACA,OAAAA,GAAA,iBAAAA,GACA,yCAAA4M,SAAAjO,UAAAkO,SAAA3P,KAAA8C,EAAA8M,cAwJAC,MAnFA,SAAAjD,EAAAuC,GACA,IAAAW,EAEA9N,EAAA,WACAA,EAAA+N,iBACAnD,EAAAoD,UAAA,EACA,IAAAC,EAAA7N,MAAAX,UAAAmH,MAAA5I,KAAAwD,WACAsM,EAAAT,WAAA,WACAzC,EAAAhK,MAAAwF,KAAA6H,GACAjO,EAAAgO,UAAA,GACGb,IAaH,OAVAnN,EAAAgO,UAAA,EACAhO,EAAA+N,eAAA,WACAG,aAAAJ,GACAlD,EAAAoD,UAAA,GAEAhO,EAAAmO,SAAA,WACAvD,IACAA,EAAAmD,kBAGA/N,GA6DAoO,WA9CA,SAAAtN,GACA,GAAAvC,OAAA8P,KACA,OAAA9P,OAAA8P,KAAAvN,GAEA,IACA1B,EADAY,KAEA,IAAAZ,KAAA0B,EACAvC,OAAAkB,UAAAC,eAAA1B,KAAA8C,EAAA1B,IACAY,EAAAqI,KAAAjJ,GAGA,OAAAY,GAoCAsO,sBAjCA,SAAA1D,GACA,IAAA2D,EAAA9Q,OAOA,OANA8Q,EAAAD,uBACAC,EAAAC,6BACAD,EAAAE,yBACAF,EAAAG,0BACAH,EAAAI,wBACA,SAAAC,GAAmBvB,WAAAuB,EAAA,UACnBhE,IA0BAiE,YAvBA,SAAA/N,GACA,OAAAA,EAAAgB,aAAAhB,EAAAmB,iGClNA,IAAA6M,EAAAnR,EAAA,GAgBAoR,EAAA,oBAAAA,KAsJA,OAlJCA,EAAAtP,UAAAuP,QAAA,WAMC,OALK5I,KAAK6I,eACT7I,KAAK6I,aAAetM,SAASkD,cAAc,OAC3CO,KAAK6I,aAAazJ,UAAY,gBAC9B0J,MAAMC,SAASC,YAAYhJ,KAAK6I,eAE1B7I,KAAK6I,cAGbF,EAAAtP,UAAA4P,YAAA,SAAYnL,GAEX,OADAkC,KAAKkJ,MAAQpL,EACNkC,MAKR2I,EAAAtP,UAAA8P,KAAA,SAAKlN,EAA2BD,GAC/B,IAAMoN,EAAY7M,SAASD,KACrBwB,EAAOkC,KAAK4I,UAOlB,GALIF,EAAWzF,UAAUnF,EAAMsL,KAC9BpJ,KAAKqJ,OACLD,EAAUvI,YAAY/C,IAGnBkC,KAAKsJ,kBAAkBrN,GAAO,CACjC,IAAMsN,EAAWvJ,KAAKwJ,0BAA0BvN,GAChDD,EAAMuN,EAASvN,IACfC,EAAOsN,EAAStN,KAOjB,OAJA6B,EAAKI,MAAMlC,IAAMA,EAAM,KACvB8B,EAAKI,MAAMjC,KAAOA,EAAO,KAEzB6M,MAAMC,SAASU,mBAAmB3L,GAC3BkC,MAER2I,EAAAtP,UAAAgQ,KAAA,WACC,IAAMvL,EAAOkC,KAAK4I,UAKlB,OAJG9K,GAAQA,EAAKpB,YACfoB,EAAKpB,WAAWqE,YAAYjD,GAE7BgL,MAAMC,SAASW,kBAAkB5L,GAC1BkC,MAGR2I,EAAAtP,UAAAsQ,WAAA,SAAWC,GAGV,OAFa5J,KAAK4I,UACbrH,UAAYqI,EACV5J,MAIA2I,EAAAtP,UAAAiQ,kBAAR,SAA0BjO,GACzB,MAAO,YAAaA,GAAS,YAAaA,GAGnCsN,EAAAtP,UAAAwQ,aAAR,WACC,OAAO7J,KAAKkJ,OAAS3M,SAASD,MAIvBqM,EAAAtP,UAAAmQ,0BAAR,SAAkCnO,GAGjC,IAAMyO,EAAY9J,KAAK+J,mBACjBC,EAAchK,KAAK4I,UACnBqB,GACLjO,IAAI,EACJC,KAAM,EACN0B,MAAOqM,EAAY/M,YACnBW,OAAQoM,EAAY9M,aACpBf,OAAQ,EACRD,MAAO,GAGFgO,EAAUpB,MAAMqB,OAAOC,iBACvBC,EAAUvB,MAAMqB,OAAOG,iBAEvBlB,EAAY7M,SAASD,KACrBiO,EAAQ7B,EAAW7F,yBAAyBxH,EAAO+N,GACnDoB,EAAe9B,EAAWxI,gBAAgBkJ,GAChDmB,EAAMhN,GAAKiN,EAAajN,EAExB0M,EAAQjO,IAAMuO,EAAMhN,EACpB0M,EAAQhO,KAAOsO,EAAM7M,EACrBuM,EAAQjO,KAAOqO,EACfJ,EAAQhO,MAAQiO,EAChBD,EAAQ9N,OAAS8N,EAAQjO,IAAMiO,EAAQrM,OACvCqM,EAAQ/N,MAAQ+N,EAAQhO,KAAOgO,EAAQtM,MAEvC,IAAMhB,EAAYtF,OAAOoT,QAAUrB,EAAUzM,UA4B7C,OA1BGsN,EAAQjO,IAAM8N,EAAS9N,IAAMW,GAC/BsN,EAAQjO,IAAM8N,EAAS9N,IACvBiO,EAAQ9N,OAAS8N,EAAQjO,IAAMiO,EAAQrM,QAC/BqM,EAAQ9N,OAAS2N,EAAS3N,SAClC8N,EAAQ9N,OAAS2N,EAAS3N,OAC1B8N,EAAQjO,IAAMiO,EAAQ9N,OAAS8N,EAAQrM,QAGrCqM,EAAQhO,KAAO6N,EAAS7N,MAC1BgO,EAAQhO,KAAO6N,EAAS7N,KACxBgO,EAAQ/N,MAAQ4N,EAAS7N,KAAOgO,EAAQtM,OAChCsM,EAAQ/N,MAAQ4N,EAAS5N,QACjC+N,EAAQ/N,MAAQ4N,EAAS5N,MACzB+N,EAAQhO,KAAOgO,EAAQ/N,MAAQ+N,EAAQtM,OAGrC4M,EAAM7M,GAAKuM,EAAQhO,MAAQsO,EAAM7M,GAAKuM,EAAQ/N,QAChD+N,EAAQhO,KAAOsO,EAAM7M,EAAIuM,EAAQtM,MAAQuM,EACzCD,EAAQ/N,MAAQ+N,EAAQhO,KAAOgO,EAAQtM,OAGrC4M,EAAMhN,GAAK0M,EAAQjO,KAAOuO,EAAMhN,GAAK0M,EAAQ9N,SAC/C8N,EAAQjO,IAAMuO,EAAMhN,EAAI0M,EAAQrM,OAASyM,EACzCJ,EAAQ9N,OAAS8N,EAAQjO,IAAMiO,EAAQrM,QAGjCqM,GAGAtB,EAAAtP,UAAA0Q,iBAAR,WACC,IAIIW,EAJEtB,EAAYpJ,KAAK6J,eACnBC,EAAWV,EACXzM,EAAYtF,OAAOoT,QAAUlO,SAASD,KAAKK,UAC3CE,EAAaxF,OAAOsT,QAAUpO,SAASD,KAAKO,WAWhD,OARGuM,IAAcN,MAAM8B,YACtBd,EAAWhB,MAAM+B,MACjBlO,EAAY,EACZE,EAAa,EACb6N,EAAMhC,EAAWxI,gBAAgB4I,MAAM+B,QAEvCH,EAAMhC,EAAWxI,gBAAgB4J,IAGjC7N,KAAKyO,EAAIhN,EAAIb,EACbb,IAAK0O,EAAInN,EAAIZ,EACbgB,MAAO+M,EAAI/M,MACXC,OAAQ8M,EAAI9M,OACZzB,OAAQuO,EAAInN,EAAImN,EAAI9M,OAASjB,EAC7BT,MAAOwO,EAAIhN,EAAIgN,EAAI/M,MAAQd,IAG9B8L,EAtJA,GAAa1R,EAAA0R,6FChBb,IAAAmC,EAAAvT,EAAA,IACAmR,EAAAnR,EAAA,GACAmC,EAAAnC,EAAA,GACAwT,EAAAxT,EAAA,KAkBAyT,EAAA,WAOC,SAAAA,IANAhL,KAAAiK,QAAmB,IAAIc,EAAApC,QAEf3I,KAAAiL,cAKPjL,KAAKkL,WAAaJ,IAClB9K,KAAKmL,wBA4HP,OAzHCH,EAAA3R,UAAA+R,WAAA,WACCpL,KAAKiK,QAAQZ,OACbrJ,KAAKkL,WAAWxG,aAEjBsG,EAAA3R,UAAAgS,YAAA,WACCrL,KAAKsL,aAENN,EAAA3R,UAAAkL,OAAA,SAAO4F,GAAP,IAAAoB,EAAAvL,KACKjJ,EAAOwF,SAASD,KAChB6N,EAAOqB,SACVzU,EAAO+R,MAAM2C,OAGd,IAAIC,EAAkB,KAChBnQ,EAAU,SAACF,GAChB,IAAMsQ,EAAcjD,EAAWhJ,cAAcrE,GACvCuQ,EAAalD,EAAWlF,QAAQmI,EAAaxB,EAAO1G,UAC1D,IAAGiF,EAAWzF,UAAU0I,EAAaJ,EAAKtB,QAAQrB,WAAlD,CAIA,IAAMiD,EAAiB,WACtBH,EAAkBE,EAClBzB,EAAO2B,aAAazQ,EAAOuQ,IAGzBF,EACCE,GAAcA,IAAeF,EAC/BvB,EAAO4B,YAAY1Q,EAAOuQ,IAE1BzB,EAAO6B,aAAa3Q,EAAOqQ,GAC3BA,EAAkB,KAEfE,GAAcA,IAAeF,GAC/BG,KAICD,GACFC,MAKH7L,KAAKyE,OAAO0F,EAAO1G,UACnBzD,KAAKkL,WAAW3G,OAAOxN,EAAM,YAAawE,GAC1CyE,KAAKiL,WAAWd,EAAO1G,WACtB3F,KAAM/G,EACNwE,QAAOA,IAITyP,EAAA3R,UAAAoL,OAAA,SAAOhB,GACN,IAAMwI,EAAWjM,KAAKiL,WAAWxH,GAC9BwI,GACFjM,KAAKkL,WAAWzG,OAAOwH,EAASnO,KAAM,YAAamO,EAAS1Q,UAI9DyP,EAAA3R,UAAA6S,WAAA,SAAW/B,GAAX,IAAAoB,EAAAvL,KACOmM,EAAgB,SAAC9Q,GACtB,IAAI+Q,EAAQ/Q,EAOZ,OAJGkB,SAA4B,oBAAMA,SAAS8P,cAE7CD,EAAQ7P,SAA4B,kBAAElB,IAEhC+Q,GAERpM,KAAKmL,wBACLnL,KAAKuE,QACJd,SAAU0G,EAAO1G,SACjB+H,OAAQrB,EAAOqB,OACfM,aAAa,SAACzQ,EAAmByC,GAChC,IAAM8L,EAAOO,EAAOP,KAAKvO,EAAOyC,GAC7B8L,GACF2B,EAAKe,UAAUH,EAAc9Q,GAAQuO,IAGvCmC,YAAY,SAAC1Q,EAAmByC,GAC/B,IAAM8L,EAAOO,EAAOP,KAAKvO,EAAOyC,GAC7B8L,EACF2B,EAAKe,UAAUH,EAAc9Q,GAAQuO,IAErC2B,EAAKe,UAAU3E,iBACf4D,EAAKD,cAGPU,aAAa,WACZT,EAAKe,UAAU3E,iBACf4D,EAAKD,gBAKAN,EAAA3R,UAAA8R,sBAAR,eAAAI,EAAAvL,KAEIA,KAAKsM,WACPtM,KAAKsM,UAAU3E,iBAEb3H,KAAKsL,WACPtL,KAAKsL,UAAU3D,iBAEhB3H,KAAKiK,QAAQZ,OAEbrJ,KAAKsM,UAAY5S,EAAQ+N,MAAM,SAACpM,EAAmBuO,IACC,IAAhDd,MAAMyD,UAAU,mBAAoBlR,IACtCkQ,EAAKtB,QAAQZ,QAEbkC,EAAKtB,QAAQN,WAAWC,GACxB2B,EAAKtB,QAAQd,KAAK9N,KAEjByN,MAAMqB,OAAOqC,iBAAmB,GAEnCxM,KAAKsL,UAAY5R,EAAQ+N,MAAM,WAC9B8D,EAAKe,UAAU3E,iBACf4D,EAAKtB,QAAQZ,QACXP,MAAMqB,OAAOsC,sBAAwB,IAG1CzB,EArIA,GAAa/T,EAAA+T,oGCnBblC,MAAMqB,OAAOqC,gBAAkB,GAC/B1D,MAAMqB,OAAOG,iBAAmB,GAChCxB,MAAMqB,OAAOC,iBAAmB,GAChCtB,MAAMqB,OAAOsC,qBAAuB,GAEpC,IAEMC,EAAiB,IAFvBnV,EAAA,KAE2ByT,gBAE3BlC,MAAM6D,IAAIC,SAAWF,EAErB5D,MAAMpN,YAAY,eAAgB,WAEjCgR,EAAeR,YACdzI,SAAU,IAAIqF,MAAMqB,OAAO0C,eAAe,yBAC1CjD,KAAM,SAACvO,GACN,IAAIyN,MAAMqB,OAAO2C,OAAUhE,MAAMqB,OAAO4C,cAAxC,CAIA,IAAMC,EAAelE,MAAMmE,OAAO5R,GAClC,GAAGyN,MAAMoE,aAAaF,GAAc,CACnC,IAAMG,EAAOrE,MAAMsE,QAAQJ,GAC3B,OAAOlE,MAAMuE,UAAUC,aAAaH,EAAKI,WAAYJ,EAAKK,SAAUL,GAErE,OAAO,OAER3B,QAAQ,MAIV1C,MAAMpN,YAAY,YAAa,WAC9BgR,EAAetB,eAGhBtC,MAAMpN,YAAY,aAAc,WAC/BgR,EAAerB,gBAMhBvC,MAAMpN,YAAY,kBAAmB,WACpC,GAJcoN,MAAM2E,WACLC,eAId,OAAO,IAIT5E,MAAMpN,YAAY,gBAAiB,WAClCgR,EAAerB", "file": "ext/dhtmlxgantt_tooltip.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"ext/dhtmlxgantt_tooltip\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ext/dhtmlxgantt_tooltip\"] = factory();\n\telse\n\t\troot[\"ext/dhtmlxgantt_tooltip\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/codebase/\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 223);\n", "var helpers = require(\"./helpers\");\n\nfunction copy(object) {\n\tvar i, result; // iterator, types array, result\n\n\tif (object && typeof object == \"object\") {\n\n\t\tswitch (true){\n\t\t\tcase (helpers.isDate(object)):\n\t\t\t\tresult = new Date(object);\n\t\t\t\tbreak;\n\t\t\tcase (helpers.isArray(object)):\n\t\t\t\tresult = new Array(object.length);\n\t\t\t\tfor(i = 0; i < object.length; i++){\n\t\t\t\t\tresult[i] = copy(object[i]);\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase (helpers.isStringObject(object)):\n\t\t\t\tresult = new String(object);\n\t\t\t\tbreak;\n\t\t\tcase (helpers.isNumberObject(object)):\n\t\t\t\tresult = new Number(object);\n\t\t\t\tbreak;\n\t\t\tcase (helpers.isBooleanObject(object)):\n\t\t\t\tresult = new Boolean(object);\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tresult = {};\n\t\t\t\tfor (i in object) {\n\t\t\t\t\tif (Object.prototype.hasOwnProperty.apply(object, [i]))\n\t\t\t\t\t\tresult[i] = copy(object[i]);\n\t\t\t\t}\n\t\t\tbreak;\n\t\t}\n\t}\n\treturn result || object;\n}\n\nfunction mixin (target, source, force){\n\tfor (var f in source)\n\t\tif (((target[f] === undefined) || force)) target[f]=source[f];\n\treturn target;\n}\n\nfunction defined(obj) {\n\treturn typeof(obj) != \"undefined\";\n}\n\nvar seed;\nfunction uid() {\n\tif (!seed)\n\t\tseed = (new Date()).valueOf();\n\n\tseed++;\n\treturn seed;\n}\n\n//creates function with specified \"this\" pointer\nfunction bind(functor, object){\n\tif(functor.bind)\n\t\treturn functor.bind(object);\n\telse\n\t\treturn function(){ return functor.apply(object,arguments); };\n}\n\nfunction event(el, event, handler, capture){\n\tif (el.addEventListener)\n\t\tel.addEventListener(event, handler, capture === undefined ? false : capture);\n\n\telse if (el.attachEvent)\n\t\tel.attachEvent(\"on\"+event, handler);\n}\n\nfunction eventRemove(el, event, handler, capture){\n\tif (el.removeEventListener)\n\t\tel.removeEventListener(event, handler, capture === undefined ? false : capture);\n\n\telse if (el.detachEvent)\n\t\tel.detachEvent(\"on\"+event, handler);\n}\n\nmodule.exports = {\n\tcopy: copy,\n\tdefined: defined,\n\tmixin: mixin,\n\tuid: uid,\n\tbind: bind,\n\tevent: event,\n\teventRemove: eventRemove\n};", "//returns position of html element on the page\nfunction elementPosition(elem) {\n\tvar top=0, left=0, right=0, bottom=0;\n\tif (elem.getBoundingClientRect) { //HTML5 method\n\t\tvar box = elem.getBoundingClientRect();\n\t\tvar body = document.body;\n\t\tvar docElem = (document.documentElement ||\n\t\t\tdocument.body.parentNode ||\n\t\t\tdocument.body);\n\n\t\tvar scrollTop = window.pageYOffset || docElem.scrollTop || body.scrollTop;\n\t\tvar scrollLeft = window.pageXOffset || docElem.scrollLeft || body.scrollLeft;\n\t\tvar clientTop = docElem.clientTop || body.clientTop || 0;\n\t\tvar clientLeft = docElem.clientLeft || body.clientLeft || 0;\n\t\ttop  = box.top +  scrollTop - clientTop;\n\t\tleft = box.left + scrollLeft - clientLeft;\n\n\t\tright = document.body.offsetWidth - box.right;\n\t\tbottom = document.body.offsetHeight - box.bottom;\n\t} else { //fallback to naive approach\n\t\twhile(elem) {\n\t\t\ttop = top + parseInt(elem.offsetTop,10);\n\t\t\tleft = left + parseInt(elem.offsetLeft,10);\n\t\t\telem = elem.offsetParent;\n\t\t}\n\n\t\tright = document.body.offsetWidth - elem.offsetWidth - left;\n\t\tbottom = document.body.offsetHeight - elem.offsetHeight - top;\n\t}\n\treturn { y: Math.round(top), x: Math.round(left), width:elem.offsetWidth, height:elem.offsetHeight, right: Math.round(right), bottom: Math.round(bottom) };\n}\n\nfunction isVisible(node){\n\tvar display = false,\n\t\tvisibility = false;\n\tif(window.getComputedStyle){\n\t\tvar style = window.getComputedStyle(node, null);\n\t\tdisplay = style[\"display\"];\n\t\tvisibility = style[\"visibility\"];\n\t}else if(node.currentStyle){\n\t\tdisplay = node.currentStyle[\"display\"];\n\t\tvisibility = node.currentStyle[\"visibility\"];\n\t}\n\treturn (display != \"none\" && visibility != \"hidden\");\n}\n\nfunction hasNonNegativeTabIndex(node){\n\treturn !isNaN(node.getAttribute(\"tabindex\")) && (node.getAttribute(\"tabindex\")*1 >= 0);\n}\n\nfunction hasHref(node){\n\tvar canHaveHref = {\"a\": true, \"area\": true};\n\tif(canHaveHref[node.nodeName.loLowerCase()]){\n\t\treturn !!node.getAttribute(\"href\");\n\t}\n\treturn true;\n}\n\nfunction isEnabled(node){\n\tvar canDisable = {\"input\":true, \"select\":true, \"textarea\":true, \"button\":true, \"object\":true};\n\tif(canDisable[node.nodeName.toLowerCase()]){\n\t\treturn !node.hasAttribute(\"disabled\");\n\t}\n\n\treturn true;\n}\n\nfunction getFocusableNodes(root){\n\tvar nodes = root.querySelectorAll([\n\t\t\"a[href]\",\n\t\t\"area[href]\",\n\t\t\"input\",\n\t\t\"select\",\n\t\t\"textarea\",\n\t\t\"button\",\n\t\t\"iframe\",\n\t\t\"object\",\n\t\t\"embed\",\n\t\t\"[tabindex]\",\n\t\t\"[contenteditable]\"\n\t].join(\", \"));\n\n\tvar nodesArray = Array.prototype.slice.call(nodes, 0);\n\tfor(var i = 0; i < nodesArray.length; i++){\n\t\tvar node = nodesArray[i];\n\t\tvar isValid = (hasNonNegativeTabIndex(node)  || isEnabled(node) || hasHref(node)) && isVisible(node);\n\t\tif(!isValid){\n\t\t\tnodesArray.splice(i, 1);\n\t\t\ti--;\n\t\t}\n\t}\n\treturn nodesArray;\n}\n\nfunction getScrollSize(){\n\tvar div = document.createElement(\"div\");\n\tdiv.style.cssText=\"visibility:hidden;position:absolute;left:-1000px;width:100px;padding:0px;margin:0px;height:110px;min-height:100px;overflow-y:scroll;\";\n\n\tdocument.body.appendChild(div);\n\tvar width = div.offsetWidth-div.clientWidth;\n\tdocument.body.removeChild(div);\n\n\treturn width;\n}\n\nfunction getClassName(node){\n\tif(!node) return \"\";\n\n\tvar className = node.className || \"\";\n\tif(className.baseVal)//'className' exist but not a string - IE svg element in DOM\n\t\tclassName = className.baseVal;\n\n\tif(!className.indexOf)\n\t\tclassName = \"\";\n\n\treturn _trimString(className);\n}\n\nfunction addClassName(node, className){\n\tif (className && node.className.indexOf(className) === -1) {\n\t\tnode.className += \" \" + className;\n\t}\n}\n\nfunction removeClassName(node, name) {\n\tname = name.split(\" \");\n\tfor (var i = 0; i < name.length; i++) {\n\t\tvar regEx = new RegExp(\"\\\\s?\\\\b\" + name[i] + \"\\\\b(?![-_.])\", \"\");\n\t\tnode.className = node.className.replace(regEx, \"\");\n\t}\n}\n\nfunction hasClass(element, className){\n\tif ('classList' in element) {\n\t\treturn element.classList.contains(className);\n\t} else { \n\t\treturn new RegExp(\"\\\\b\" + className + \"\\\\b\").test(element.className);\n\t}\n}\n\nfunction toNode(node) {\n\tif (typeof node === \"string\") {\n\t\treturn (document.getElementById(node) || document.querySelector(node) || document.body);\n\t}\n\treturn node || document.body;\n}\n\nvar _slave = document.createElement(\"div\");\nfunction insert(node, newone) {\n\t_slave.innerHTML = newone;\n\tvar child = _slave.firstChild;\n\tnode.appendChild(child);\n\treturn child;\n}\n\nfunction remove(node) {\n\tif (node && node.parentNode) {\n\t\tnode.parentNode.removeChild(node);\n\t}\n}\n\nfunction getChildren(node, css) {\n\tvar ch = node.childNodes;\n\tvar len = ch.length;\n\tvar out = [];\n\tfor (var i = 0; i < len; i++) {\n\t\tvar obj = ch[i];\n\t\tif (obj.className && obj.className.indexOf(css) !== -1) {\n\t\t\tout.push(obj);\n\t\t}\n\t}\n\treturn out;\n}\n\nfunction getTargetNode(e){\n\tvar trg;\n\tif (e.tagName)\n\t\ttrg = e;\n\telse {\n\t\te=e||window.event;\n\t\ttrg=e.target||e.srcElement;\n\t}\n\treturn trg;\n}\n\nfunction locateAttribute(e, attribute) {\n\tif(!attribute) return;\n\n\tvar trg = getTargetNode(e);\n\n\twhile (trg){\n\t\tif (trg.getAttribute){\t//text nodes has not getAttribute\n\t\t\tvar test = trg.getAttribute(attribute);\n\t\t\tif (test) return trg;\n\t\t}\n\t\ttrg=trg.parentNode;\n\t}\n\treturn null;\n}\n\nfunction _trimString(str){\n\tvar func = String.prototype.trim || function(){ return this.replace(/^\\s+|\\s+$/g, \"\"); };\n\treturn func.apply(str);\n}\n\nfunction locateClassName(e, classname, strict){\n\tvar trg = getTargetNode(e);\n\tvar css = \"\";\n\n\tif(strict === undefined)\n\t\tstrict = true;\n\n\twhile (trg){\n\t\tcss = getClassName(trg);\n\t\tif(css){\n\t\t\tvar ind = css.indexOf(classname);\n\t\t\tif (ind >= 0){\n\t\t\t\tif (!strict)\n\t\t\t\t\treturn trg;\n\n\t\t\t\t//check that we have exact match\n\t\t\t\tvar left = (ind === 0) || (!_trimString(css.charAt(ind - 1)));\n\t\t\t\tvar right = ((ind + classname.length >= css.length)) || (!_trimString(css.charAt(ind + classname.length)));\n\n\t\t\t\tif (left && right)\n\t\t\t\t\treturn trg;\n\t\t\t}\n\t\t}\n\t\ttrg=trg.parentNode;\n\t}\n\treturn null;\n}\n\n/*\nevent position relatively to DOM element\n */\nfunction getRelativeEventPosition(ev, node){\n\tvar d = document.documentElement;\n\tvar box = elementPosition(node);\n\n\treturn {\n\t\tx: ev.clientX + d.scrollLeft - d.clientLeft - box.x + node.scrollLeft,\n\t\ty: ev.clientY + d.scrollTop - d.clientTop - box.y + node.scrollTop\n\t};\n}\n\nfunction isChildOf(child, parent){\n\tif(!child || !parent){\n\t\treturn false;\n\t}\n\n\twhile(child && child != parent) {\n\t\tchild = child.parentNode;\n\t}\n\n\treturn child === parent;\n}\n\nfunction closest(element, selector){\n\tif(element.closest){\n\t\treturn element.closest(selector);\n\t}else if(element.matches || element.msMatchesSelector || element.webkitMatchesSelector){\n\t\tvar el = element;\n\t\tif (!document.documentElement.contains(el)) return null;\n\t\tdo {\n\t\t\tvar method = el.matches || el.msMatchesSelector || el.webkitMatchesSelector;\n\n\t\t\tif (method.call(el, selector)) return el;\n\t\t\tel = el.parentElement || el.parentNode;\n\t\t} while (el !== null && el.nodeType === 1); \n\t\treturn null;\n\t}else{\n\t\t// eslint-disable-next-line no-console\n\t\tconsole.error(\"Your browser is not supported\");\n\t\treturn null;\n\t}\n}\n\nmodule.exports = {\n\tgetNodePosition: elementPosition,\n\tgetFocusableNodes: getFocusableNodes,\n\tgetScrollSize: getScrollSize,\n\tgetClassName: getClassName,\n\taddClassName: addClassName,\n\tremoveClassName: removeClassName,\n\tinsertNode: insert,\n\tremoveNode: remove,\n\tgetChildNodes: getChildren,\n\ttoNode: toNode,\n\tlocateClassName:locateClassName,\n\tlocateAttribute: locateAttribute,\n\tgetTargetNode: getTargetNode,\n\tgetRelativeEventPosition: getRelativeEventPosition,\n\tisChildOf: isChildOf,\n\thasClass: hasClass,\n\tclosest: closest\n};", "var utils = require(\"./utils\");\n\nfunction createScope(addEvent, removeEvent) {\n\taddEvent = addEvent || utils.event;\n\tremoveEvent = removeEvent || utils.eventRemove;\n\n\tvar handlers = [];\n\n\tvar eventScope = {\n\t\tattach: function(el, event, callback, capture){\n\t\t\thandlers.push({element: el, event:event, callback: callback, capture: capture});\n\t\t\taddEvent(el, event, callback, capture);\n\t\t},\n\t\tdetach: function(el, event, callback, capture){\n\t\t\tremoveEvent(el, event, callback, capture);\n\t\t\tfor(var i = 0; i < handlers.length; i++){\n\t\t\t\tvar handler = handlers[i];\n\t\t\t\tif (handler.element === el && handler.event === event && handler.callback === callback && handler.capture === capture) {\n\t\t\t\t\thandlers.splice(i, 1);\n\t\t\t\t\ti--;\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tdetachAll: function () {\n\t\t\tvar staticArray = handlers.slice();\n\t\t\t// original handlers array can be spliced on every iteration\n\t\t\tfor (var i = 0; i < staticArray.length; i++){\n\t\t\t\tvar handler = staticArray[i];\n\t\t\t\teventScope.detach(handler.element, handler.event, handler.callback, handler.capture);\n\t\t\t\teventScope.detach(handler.element, handler.event, handler.callback, undefined);\n\t\t\t\teventScope.detach(handler.element, handler.event, handler.callback, false);\n\t\t\t\teventScope.detach(handler.element, handler.event, handler.callback, true);\n\t\t\t}\n\t\t\thandlers.splice(0, handlers.length);\n\t\t},\n\t\textend: function(){\n\t\t\treturn createScope(this.event, this.eventRemove);\n\t\t}\n\t};\n\n\tif (!window.scopes) {\n\t\twindow.scopes = [];\n\t}\n\twindow.scopes.push(handlers);\n\treturn eventScope;\n}\n\nmodule.exports = createScope;", "var units = {\n\t\"second\": 1,\n\t\"minute\": 60,\n\t\"hour\": 60 * 60,\n\t\"day\": 60 * 60 * 24,\n\t\"week\": 60 * 60 * 24 * 7,\n\t\"month\": 60 * 60 * 24 * 30,\n\t\"quarter\": 60 * 60 * 24 * 30 * 3,\n\t\"year\": 60 * 60 * 24 * 365\n};\nfunction getSecondsInUnit(unit){\n\treturn units[unit] || units.hour;\n}\n\nfunction forEach(arr, callback) {\n\tif (arr.forEach) {\n\t\tarr.forEach(callback);\n\t} else {\n\t\tvar workArray = arr.slice();\n\t\tfor (var i = 0; i < workArray.length; i++) {\n\t\t\tcallback(workArray[i], i);\n\t\t}\n\t}\n}\n\nfunction arrayMap(arr, callback) {\n\tif (arr.map) {\n\t\treturn arr.map(callback);\n\t} else {\n\t\tvar workArray = arr.slice();\n\t\tvar resArray = [];\n\n\t\tfor (var i = 0; i < workArray.length; i++) {\n\t\t\tresArray.push(callback(workArray[i], i));\n\t\t}\n\t\treturn resArray;\n\t}\n}\n\n\nfunction arrayFind(arr, callback) {\n\tif (arr.find) {\n\t\treturn arr.find(callback);\n\t} else {\n\t\tfor (var i = 0; i < arr.length; i++) {\n\t\t\tif (callback(arr[i], i)) {\n\t\t\t\treturn arr[i];\n\t\t\t}\n\t\t}\n\t}\n}\n\n// iframe-safe array type check instead of using instanceof\nfunction isArray(obj){\n\tif(Array.isArray){\n\t\treturn Array.isArray(obj);\n\t}else{\n\t\t// close enough\n\t\treturn (obj && obj.length !== undefined && obj.pop && obj.push);\n\t}\n}\n\n// non-primitive string object, e.g. new String(\"abc\")\nfunction isStringObject(obj){\n\treturn obj && typeof obj === \"object\"\n\t\t&& Function.prototype.toString.call(obj.constructor) === \"function String() { [native code] }\";\n}\n\n// non-primitive number object, e.g. new Number(5)\nfunction isNumberObject(obj){\n\treturn obj && typeof obj === \"object\"\n\t\t&& Function.prototype.toString.call(obj.constructor) === \"function Number() { [native code] }\";\n}\n\n// non-primitive number object, e.g. new Boolean(true)\nfunction isBooleanObject(obj){\n\treturn obj && typeof obj === \"object\"\n\t\t&& Function.prototype.toString.call(obj.constructor) === \"function Boolean() { [native code] }\";\n}\n\nfunction isDate(obj) {\n\tif (obj && typeof obj === \"object\") {\n\t\treturn !!(obj.getFullYear && obj.getMonth && obj.getDate);\n\t} else {\n\t\treturn false;\n\t}\n}\n\nfunction arrayFilter(arr, callback) {\n\tvar result = [];\n\n\tif (arr.filter) {\n\t\treturn arr.filter(callback);\n\t} else {\n\t\tfor (var i = 0; i < arr.length; i++) {\n\t\t\tif (callback(arr[i], i)) {\n\t\t\t\tresult[result.length] = arr[i];\n\t\t\t}\n\t\t}\n\t\treturn result;\n\t}\n}\n\nfunction hashToArray(hash) {\n\tvar result = [];\n\n\tfor (var key in hash) {\n\t\tif (hash.hasOwnProperty(key)) {\n\t\t\tresult.push(hash[key]);\n\t\t}\n\t}\n\n\treturn result;\n}\n\nfunction arraySome(arr, callback) {\n\tif (arr.length === 0) return false;\n\n\tfor (var i = 0; i < arr.length; i++) {\n\t\tif (callback(arr[i], i, arr)) {\n\t\t\treturn true;\n\t\t}\n\t}\n\treturn false;\n}\n\nfunction arrayDifference(arr, callback) {\n\treturn arrayFilter(arr, function(item, i) {\n\t\treturn !callback(item, i);\n\t});\n}\n\nfunction throttle (callback, timeout) {\n\tvar wait = false;\n\n\treturn function () {\n\t\tif (!wait) {\n\t\t\tcallback.apply(null, arguments);\n\t\t\twait = true;\n\t\t\tsetTimeout(function () {\n\t\t\t\twait = false;\n\t\t\t}, timeout);\n\t\t}\n\t};\n}\n\nfunction delay (callback, timeout){\n\tvar timer;\n\n\tvar result = function() {\n\t\tresult.$cancelTimeout();\n\t\tcallback.$pending = true;\n\t\tvar args = Array.prototype.slice.call(arguments);\n\t\ttimer = setTimeout(function(){\n\t\t\tcallback.apply(this, args);\n\t\t\tresult.$pending = false;\n\t\t}, timeout);\n\t};\n\t\n\tresult.$pending = false;\n\tresult.$cancelTimeout = function(){\n\t\tclearTimeout(timer);\n\t\tcallback.$pending = false;\n\t};\n\tresult.$execute = function(){\n\t\tcallback();\n\t\tcallback.$cancelTimeout();\n\t};\n\n\treturn result;\n}\n\nfunction sortArrayOfHash(arr, field, desc) {\n\tvar compare = function(a, b) {\n\t\treturn a < b;\n\t};\n\n\tarr.sort(function(a, b) {\n\t\tif (a[field] === b[field]) return 0;\n\n\t\treturn desc ? compare(a[field], b[field]) : compare(b[field], a[field]);\n\t});\n}\n\nfunction objectKeys(obj) {\n\tif (Object.keys) {\n\t\treturn Object.keys(obj);\n\t}\n\tvar result = [];\n\tvar key;\n\tfor (key in obj) {\n\t\tif (Object.prototype.hasOwnProperty.call(obj, key)) {\n\t\t\tresult.push(key);\n\t\t}\n\t}\n\treturn result;\n}\n\nfunction requestAnimationFrame(callback) {\n\tvar w = window;\n\tvar foundRequestAnimationFrame = w.requestAnimationFrame\n\t\t|| w.webkitRequestAnimationFrame\n\t\t|| w.msRequestAnimationFrame\n\t\t|| w.mozRequestAnimationFrame\n\t\t|| w.oRequestAnimationFrame\n\t\t|| function(cb) { setTimeout(cb, 1000/60); };\n\treturn foundRequestAnimationFrame(callback);\n}\n\nfunction isEventable(obj) {\n\treturn obj.attachEvent && obj.detachEvent;\n}\n\nmodule.exports = {\n\tgetSecondsInUnit: getSecondsInUnit,\n\tforEach: forEach,\n\tarrayMap: arrayMap,\n\tarrayFind: arrayFind,\n\tarrayFilter: arrayFilter,\n\tarrayDifference: arrayDifference,\n\tarraySome: arraySome,\n\thashToArray: hashToArray,\n\tsortArrayOfHash: sortArrayOfHash,\n\tthrottle: throttle,\n\tisArray: isArray,\n\tisDate: isDate,\n\tisStringObject: isStringObject,\n\tisNumberObject: isNumberObject,\n\tisBooleanObject: isBooleanObject,\n\tdelay: delay,\n\tobjectKeys: objectKeys,\n\trequestAnimationFrame: requestAnimationFrame,\n\tisEventable: isEventable\n};", "import * as domHelpers from \"../../utils/dom_helpers\";\n\ndeclare var gantt: any;\n\ninterface IViewPosition{\n\ttop: number;\n\tleft: number;\n}\n\ninterface IViewBox extends IViewPosition{\n\twidth: number;\n\theight: number;\n\tbottom: number;\n\tright: number;\n}\n\nexport class Tooltip {\n\tprivate _root: HTMLElement;\n\tprivate _tooltipNode: HTMLElement;\n\n\tgetNode() : HTMLElement {\n\t\tif (!this._tooltipNode){\n\t\t\tthis._tooltipNode = document.createElement(\"div\");\n\t\t\tthis._tooltipNode.className = \"gantt_tooltip\";\n\t\t\tgantt._waiAria.tooltipAttr(this._tooltipNode);\n\t\t}\n\t\treturn this._tooltipNode;\n\t}\n\n\tsetViewport(node: HTMLElement):Tooltip{\n\t\tthis._root = node;\n\t\treturn this;\n\t}\n\n\tshow(left: number, top: number): Tooltip;\n\tshow(event: MouseEvent): Tooltip;\n\tshow(left: number | MouseEvent, top?: number): Tooltip {\n\t\tconst container = document.body;\n\t\tconst node = this.getNode();\n\n\t\tif(!domHelpers.isChildOf(node, container)){\n\t\t\tthis.hide();\n\t\t\tcontainer.appendChild(node);\n\t\t}\n\n\t\tif (this._isLikeMouseEvent(left)) {\n\t\t\tconst position = this._calculateTooltipPosition(left as MouseEvent);\n\t\t\ttop = position.top;\n\t\t\tleft = position.left;\n\t\t}\n\n\t\tnode.style.top = top + \"px\";\n\t\tnode.style.left = left + \"px\";\n\n\t\tgantt._waiAria.tooltipVisibleAttr(node);\n\t\treturn this;\n\t}\n\thide() : Tooltip{\n\t\tconst node = this.getNode();\n\t\tif(node && node.parentNode){\n\t\t\tnode.parentNode.removeChild(node);\n\t\t}\n\t\tgantt._waiAria.tooltipHiddenAttr(node);\n\t\treturn this;\n\t}\n\n\tsetContent(html: string) : Tooltip{\n\t\tconst node = this.getNode();\n\t\tnode.innerHTML = html;\n\t\treturn this;\n\t}\n\n\t// it is for salesforce, because it proxies event to it own events\n\tprivate _isLikeMouseEvent(event: any): boolean {\n\t\treturn \"clientX\" in event && \"clientY\" in event;\n\t}\n\n\tprivate _getViewPort() : HTMLElement {\n\t\treturn this._root || document.body;\n\t}\n\n\n\tprivate _calculateTooltipPosition(event: MouseEvent): IViewPosition{\n\n\t\t// top/left coordinates inside the viewport by mouse position\n\t\tconst viewport =  this._getViewPortSize();\n\t\tconst tooltipNode = this.getNode();\n\t\tconst tooltip: IViewBox = {\n\t\t\ttop:0,\n\t\t\tleft: 0,\n\t\t\twidth: tooltipNode.offsetWidth,\n\t\t\theight: tooltipNode.offsetHeight,\n\t\t\tbottom: 0,\n\t\t\tright: 0\n\t\t};\n\n\t\tconst offsetX = gantt.config.tooltip_offset_x;\n\t\tconst offsetY = gantt.config.tooltip_offset_y;\n\n\t\tconst container = document.body;\n\t\tconst mouse = domHelpers.getRelativeEventPosition(event, container);\n\t\tconst containerPos = domHelpers.getNodePosition(container);\n\t\tmouse.y += containerPos.y; // to fix margin collapsing\n\n\t\ttooltip.top = mouse.y;\n\t\ttooltip.left = mouse.x;\n\t\ttooltip.top += offsetY;\n\t\ttooltip.left += offsetX;\n\t\ttooltip.bottom = tooltip.top + tooltip.height;\n\t\ttooltip.right = tooltip.left + tooltip.width;\n\n\t\tconst scrollTop = window.scrollY + container.scrollTop; // to fix margin collapsing\n\t\t// edge cases when the tooltip element can be partially hidden by edges of the viewport\n\t\tif(tooltip.top < viewport.top - scrollTop){\n\t\t\ttooltip.top = viewport.top;\n\t\t\ttooltip.bottom = tooltip.top + tooltip.height;\n\t\t}else if(tooltip.bottom > viewport.bottom){\n\t\t\ttooltip.bottom = viewport.bottom;\n\t\t\ttooltip.top = tooltip.bottom - tooltip.height;\n\t\t}\n\n\t\tif(tooltip.left < viewport.left){\n\t\t\ttooltip.left = viewport.left;\n\t\t\ttooltip.right = viewport.left + tooltip.width;\n\t\t}else if(tooltip.right > viewport.right){\n\t\t\ttooltip.right = viewport.right;\n\t\t\ttooltip.left = tooltip.right - tooltip.width;\n\t\t}\n\n\t\tif(mouse.x >= tooltip.left && mouse.x <= tooltip.right) {\n\t\t\ttooltip.left = mouse.x - tooltip.width - offsetX;\n\t\t\ttooltip.right = tooltip.left + tooltip.width;\n\t\t}\n\n\t\tif(mouse.y >= tooltip.top && mouse.y <= tooltip.bottom) {\n\t\t\ttooltip.top = mouse.y - tooltip.height - offsetY;\n\t\t\ttooltip.bottom = tooltip.top + tooltip.height;\n\t\t}\n\n\t\treturn tooltip;\n\t}\n\n\tprivate _getViewPortSize() : IViewBox {\n\t\tconst container = this._getViewPort();\n\t\tlet viewport = container;\n\t\tlet scrollTop = window.scrollY + document.body.scrollTop;\n\t\tlet scrollLeft = window.scrollX + document.body.scrollLeft;\n\t\tlet pos;\n\t\t// support for the initial tooltip mode where the tooltip element was attached to the data area of gantt\n\t\tif(container === gantt.$task_data){\n\t\t\tviewport = gantt.$task;\n\t\t\tscrollTop = 0;\n\t\t\tscrollLeft = 0;\n\t\t\tpos = domHelpers.getNodePosition(gantt.$task);\n\t\t}else{\n\t\t\tpos = domHelpers.getNodePosition(viewport);\n\t\t}\n\t\treturn {\n\t\t\tleft:pos.x + scrollLeft,\n\t\t\ttop: pos.y + scrollTop,\n\t\t\twidth: pos.width,\n\t\t\theight: pos.height,\n\t\t\tbottom: pos.y + pos.height + scrollTop,\n\t\t\tright: pos.x + pos.width + scrollLeft\n\t\t};\n\t}\n}\n", "import * as domEventsScope from \"../../utils/dom_event_scope\";\nimport * as domHelpers from \"../../utils/dom_helpers\";\nimport * as helpers from \"../../utils/helpers\";\nimport { Tooltip } from \"./tooltip\";\n\ninterface ITrackerTarget {\n\tselector: string;\n\tonmouseenter: (event: MouseEvent, node: HTMLElement) => void;\n\tonmousemove: (event: MouseEvent, node: HTMLElement) => void;\n\tonmouseleave: (event: MouseEvent, node: HTMLElement) => void;\n\tglobal: boolean;\n}\n\ninterface ITooltipConfig {\n\tselector: string;\n\thtml: (event: MouseEvent, node: HTMLElement) => string;\n\tglobal: boolean;\n}\n\ndeclare var gantt:any;\n\nexport class TooltipManager{\n\ttooltip: Tooltip = new Tooltip();\n\tprotected _domEvents: any;\n\tprivate _listeners: object = {};\n\tprivate delayShow: any;\n\tprivate delayHide: any;\n\n\tconstructor() {\n\t\tthis._domEvents = domEventsScope();\n\t\tthis._initDelayedFunctions();\n\t}\n\n\tdestructor(): void{\n\t\tthis.tooltip.hide();\n\t\tthis._domEvents.detachAll();\n\t}\n\thideTooltip(): void{\n\t\tthis.delayHide();\n\t}\n\tattach(config: ITrackerTarget): void {\n\t\tlet root = document.body;\n\t\tif(!config.global){\n\t\t\troot = gantt.$root;\n\t\t}\n\n\t\tlet watchableTarget = null;\n\t\tconst handler = (event) => {\n\t\t\tconst eventTarget = domHelpers.getTargetNode(event);\n\t\t\tconst targetNode = domHelpers.closest(eventTarget, config.selector);\n\t\t\tif(domHelpers.isChildOf(eventTarget, this.tooltip.getNode())){\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst doOnMouseEnter = () => {\n\t\t\t\twatchableTarget = targetNode;\n\t\t\t\tconfig.onmouseenter(event, targetNode);\n\t\t\t};\n\n\t\t\tif(watchableTarget){\n\t\t\t\tif(targetNode && targetNode === watchableTarget){\n\t\t\t\t\tconfig.onmousemove(event, targetNode);\n\t\t\t\t}else{\n\t\t\t\t\tconfig.onmouseleave(event, watchableTarget);\n\t\t\t\t\twatchableTarget = null;\n\n\t\t\t\t\tif(targetNode && targetNode !== watchableTarget){\n\t\t\t\t\t\tdoOnMouseEnter();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}else{\n\t\t\t\tif(targetNode){\n\t\t\t\t\tdoOnMouseEnter();\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\n\t\tthis.detach(config.selector);\n\t\tthis._domEvents.attach(root, \"mousemove\", handler);\n\t\tthis._listeners[config.selector] = {\n\t\t\tnode: root,\n\t\t\thandler\n\t\t};\n\t}\n\n\tdetach(selector: string): void {\n\t\tconst listener = this._listeners[selector];\n\t\tif(listener){\n\t\t\tthis._domEvents.detach(listener.node, \"mousemove\", listener.handler);\n\t\t}\n\t}\n\n\ttooltipFor(config: ITooltipConfig): void {\n\t\tconst cloneDomEvent = (event: MouseEvent) => {\n\t\t\tlet clone = event;\n\t\t\t// making events survive timeout in ie\n\t\t\t// tslint:disable-next-line no-string-literal\n\t\t\tif(document[\"createEventObject\"] && !document.createEvent){\n\t\t\t\t// tslint:disable-next-line no-string-literal\n\t\t\t\tclone = document[\"createEventObject\"](event);\n\t\t\t}\n\t\t\treturn clone;\n\t\t};\n\t\tthis._initDelayedFunctions();\n\t\tthis.attach({\n\t\t\tselector: config.selector,\n\t\t\tglobal: config.global,\n\t\t\tonmouseenter:(event: MouseEvent, node: HTMLElement) => {\n\t\t\t\tconst html = config.html(event, node);\n\t\t\t\tif(html){\n\t\t\t\t\tthis.delayShow(cloneDomEvent(event), html);\n\t\t\t\t}\n\t\t\t},\n\t\t\tonmousemove:(event: MouseEvent, node: HTMLElement) => {\n\t\t\t\tconst html = config.html(event, node);\n\t\t\t\tif(html){\n\t\t\t\t\tthis.delayShow(cloneDomEvent(event), html);\n\t\t\t\t}else{\n\t\t\t\t\tthis.delayShow.$cancelTimeout();\n\t\t\t\t\tthis.delayHide();\n\t\t\t\t}\n\t\t\t},\n\t\t\tonmouseleave:() => {\n\t\t\t\tthis.delayShow.$cancelTimeout();\n\t\t\t\tthis.delayHide();\n\t\t\t},\n\t\t});\n\t}\n\n\tprivate _initDelayedFunctions(){\n\t\t// reset delayed functions in order to apply current values of tooltip_timeout\n\t\tif(this.delayShow){\n\t\t\tthis.delayShow.$cancelTimeout();\n\t\t}\n\t\tif(this.delayHide){\n\t\t\tthis.delayHide.$cancelTimeout();\n\t\t}\n\t\tthis.tooltip.hide();\n\n\t\tthis.delayShow = helpers.delay((event: MouseEvent, html: string) => {\n\t\t\tif(gantt.callEvent(\"onBeforeTooltip\", [event]) === false) {\n\t\t\t\tthis.tooltip.hide();\n\t\t\t} else {\n\t\t\t\tthis.tooltip.setContent(html);\n\t\t\t\tthis.tooltip.show(event);\n\t\t\t}\n\t\t}, gantt.config.tooltip_timeout || 1);\n\n\t\tthis.delayHide = helpers.delay(() => {\n\t\t\tthis.delayShow.$cancelTimeout();\n\t\t\tthis.tooltip.hide();\n\t\t}, gantt.config.tooltip_hide_timeout || 1);\n\t}\n\n}", "declare var gantt;\n\ngantt.config.tooltip_timeout = 30;\ngantt.config.tooltip_offset_y = 20;\ngantt.config.tooltip_offset_x = 10;\ngantt.config.tooltip_hide_timeout = 30;\n\nimport {TooltipManager} from \"./tooltipManager\";\n\nconst tooltipManager = new TooltipManager();\n\ngantt.ext.tooltips = tooltipManager;\n\ngantt.attachEvent(\"onGanttReady\", function(){\n\n\ttooltipManager.tooltipFor({\n\t\tselector: \"[\"+gantt.config.task_attribute+\"]:not(.gantt_task_row)\",\n\t\thtml: (event: MouseEvent) => {\n\t\t\tif (gantt.config.touch && !gantt.config.touch_tooltip) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst targetTaskId = gantt.locate(event);\n\t\t\tif(gantt.isTaskExists(targetTaskId)){\n\t\t\t\tconst task = gantt.getTask(targetTaskId);\n\t\t\t\treturn gantt.templates.tooltip_text(task.start_date, task.end_date, task);\n\t\t\t}\n\t\t\treturn null;\n\t\t},\n\t\tglobal: false\n\t});\n});\n\ngantt.attachEvent(\"onDestroy\", function() {\n\ttooltipManager.destructor();\n});\n\ngantt.attachEvent(\"onLightbox\", function() {\n\ttooltipManager.hideTooltip();\n});\nconst isLinkCreate = () => {\n\tconst state = gantt.getState();\n\treturn !!state.link_source_id;\n};\ngantt.attachEvent(\"onBeforeTooltip\", function() {\n\tif (isLinkCreate()){\n\t\treturn false;\n\t}\n});\n\ngantt.attachEvent(\"onGanttScroll\", function(){\n\ttooltipManager.hideTooltip();\n});\n"], "sourceRoot": ""}