import { axios } from '@/utils/request'

export function testWarmBoxPage(parameter) {
  return axios({
    url: '/testWarmBox/page',
    method: 'post',
    data: parameter
  })
}

export function testWarmBoxNewPage(parameter) {
  return axios({
    url: '/testWarmBoxNew/page',
    method: 'post',
    data: parameter
  })
}

export function testWarmGetCanInWarmBoxLuo(parameter) {
  return axios({
    url: '/testWarmBoxNew/getCanInWarmBoxLuo',
    method: 'post',
    data: parameter
  })
}

export function testWarmBoxLuoAdd(parameter) {
  return axios({
    url: '/testWarmBoxLuo/add',
    method: 'post',
    data: parameter
  })
}

export function testWarmBoxLuoRemove(parameter) {
  return axios({
    url: '/testWarmBoxLuo/remove',
    method: 'post',
    data: parameter
  })
}
export function testWarmBoxLuoList(parameter) {
  return axios({
    url: '/testWarmBoxLuo/list',
    method: 'post',
    data: parameter
  })
}

export function getTestWarmBoxLuoInOutDetail(parameter) {
  return axios({
    url: '/testWarmBoxLuo/getInOutDetail',
    method: 'post',
    data: parameter
  })
}

export function testWarmBoxBasicPage(parameter) {
  return axios({
    url: '/testWarmBox/pageByBasic',
    method: 'post',
    data: parameter
  })
}

export function testWarmBoxGanttPage(parameter) {
  return axios({
    url: '/testWarmBox/ganttPage',
    method: 'post',
    data: parameter
  })
}

export function getCanInBoxStoreyOnlyBatteryType(parameter) {
  return axios({
    url: '/testWarmBox/getCanInBoxStoreyOnlyBatteryType',
    method: 'post',
    data: parameter
  })
}


export function inOutDetailList(parameter) {
  return axios({
    url: '/testWarmBox/inOutDetailList',
    method: 'post',
    data: parameter
  })
}


export function updateBox(parameter) {
  return axios({
    url: '/testWarmBoxNew/update',
    method: 'post',
    data: parameter
  })
}

export function updateSixBox(parameter) {
  return axios({
    url: '/testWarmBoxNew/updateSix',
    method: 'post',
    data: parameter
  })
}

export function removeBox(parameter) {
  return axios({
    url: '/testWarmBoxNew/remove',
    method: 'post',
    data: parameter
  })
}

/**
 * 导出温箱数据
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
export function warmBoxExport(parameter) {
  return axios({
    url: '/testWarmBoxNew/export',
    method: 'post',
    data: parameter,
    responseType: 'blob'
  })
}

/**
 * 导入温箱数据
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
export function warmBoxImport(file) {
  const formData = new FormData()
  formData.append('file', file)
  return axios({
    url: '/testWarmBox/importNew',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
