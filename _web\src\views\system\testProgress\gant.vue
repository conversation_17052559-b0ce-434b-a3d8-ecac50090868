<template>
  <div style="background-color: #FFFFFF;padding: 20px 20px 55px;">
    <a-tabs type="card" @change="init" v-model="address">
      <a-tab-pane key="all" tab="全部" v-if="hasPerm('progress:all')">
      </a-tab-pane>

      <a-tab-pane key="hz" tab="第六实验室(HZ)" v-if="hasPerm('progress:all') || hasPerm('progress:a1_3f') || hasPerm('progress:r2_2f') || hasPerm('progress:r4_4f')">
      </a-tab-pane>

      <a-tab-pane key="JM" tab="第六实验室(JM)" v-if="hasPerm('progress:all') || hasPerm('progress:jm')">
      </a-tab-pane>

      <a-tab-pane key="R3" tab="第四实验室" v-if="hasPerm('progress:all') || hasPerm('progress:r3')">
      </a-tab-pane>

    </a-tabs>

    <div style="float: left;position: relative;z-index: 1;padding-bottom: 5px;width: 65%">
      <a-row :gutter="[8,8]">
        <a-col :span="6">
          <a-form-item label="测试申请单" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryParam.testCode" @keyup.enter="init" @change="init"/>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="出箱日期" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-select v-model="queryParam.out" style="width: 100%;" @change="init" :allowClear="true">

              <a-select-option value="today">
                今天
              </a-select-option>
              <a-select-option value="two">
                0~2天
              </a-select-option>

            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="6">
          <a-form-item label="申请人" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryParam.applicant" @keyup.enter="init" @change="init"/>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="测试技师" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryParam.testMan" @keyup.enter="init" @change="init"
            />
          </a-form-item>

        </a-col>
      </a-row>
      <a-row :gutter="[8,8]" v-if="show">
        <a-col :span="6">
          <a-form-item label="产品名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryParam.productName" @keyup.enter="init" @change="init"/>
          </a-form-item>
        </a-col>

        <a-col :span="6">
          <a-form-item label="部门" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryParam.dept" @keyup.enter="init" @change="init"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="电芯载体" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-select v-model="queryParam.sampleType" @keyup.enter="init" @change="init" :allowClear="true"
                      style="width: 100%;">

              <a-select-option value="G圆柱">
                G圆柱
              </a-select-option>
              <a-select-option value="C圆柱">
                C圆柱
              </a-select-option>
              <a-select-option value="V圆柱">
                V圆柱
              </a-select-option>
              <a-select-option value="方形">
                方形
              </a-select-option>
              <a-select-option value="软包_396389">
                软包_396389
              </a-select-option>
              <a-select-option value="软包_动力">
                软包_动力
              </a-select-option>
              <a-select-option value="模组">
                模组
              </a-select-option>

            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">

          <a-form-item label="测试状态" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-select  :allowClear="true" mode="multiple" :maxTagCount="parseInt(3)" @change="changeStatus" v-model="statusList"  style="width: 260px">


              <a-select-option value="Plan">
                Plan
              </a-select-option>
              <a-select-option value="Ongoing">
                Ongoing
              </a-select-option>
              <a-select-option value="Done">
                Done
              </a-select-option>
              <a-select-option value="Stop">
                Stop
              </a-select-option>

            </a-select>
          </a-form-item>
          <!--<a-form-item label="测试地点" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-select @keyup.enter="getList" @change="getList" v-model="queryParam.testAddress" :allow-clear="true"
                      style="width: 100%">

              <a-select-option value="A1_3F">
                A1_3F
              </a-select-option>
              <a-select-option value="B4_3F">
                B4_3F
              </a-select-option>
              <a-select-option value="R2_2F">
                R2_2F
              </a-select-option>
              <a-select-option value="R4_3F">
                R4_3F
              </a-select-option>
              <a-select-option value="R4_4F">
                R4_4F
              </a-select-option>
              <a-select-option value="R4_5F">
                R4_5F
              </a-select-option>

            </a-select>

          </a-form-item>-->
        </a-col>
      </a-row>
      <a-row :gutter="[8,8]" v-if="show">
        <a-col :span="6">
          <a-form-item label="T/℃" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryParam.t" @keyup.enter="init" @change="init"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="测试项目" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryParam.testProject" @keyup.enter="init" @change="init"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="样品阶段" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryParam.productSampleStage" @keyup.enter="init" @change="init"
            />
          </a-form-item>

        </a-col>

        <a-col :span="6">
          <a-form-item label="出箱范围" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-range-picker @change="rangeChange"  v-model="queryParam.range"  style="width: 260px">
            </a-range-picker>
          </a-form-item>
        </a-col>


      </a-row>
      <a-row :gutter="[8,8]" v-if="show">
        <a-col :span="6" v-if="address == 'all'">
          <a-form-item label="测试地点" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-select v-model="allAddress" @keyup.enter="init" @change="init" :allowClear="true"
                      style="width: 100%;">


              <a-select-option value="A1_3F">
                V圆柱检测室
              </a-select-option>

              <a-select-option value="R2_2F">
                材料验证检测室
              </a-select-option>

              <a-select-option value="R4_4F">
                动力电池检测室
              </a-select-option>

            </a-select>
          </a-form-item>

        </a-col>

        <a-col :span="6">
          <a-form-item label="存储位置" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryParam.saveAddress" @keyup.enter="init" @change="init"/>
          </a-form-item>
        </a-col>

      </a-row>
    </div>

    <div style="float: right;position: relative;z-index: 1;padding-bottom: 5px;">
      <a-icon :type="!show?'down':'up'" style="margin-right: 8px;cursor: pointer" @click="() => show=!show"/>
      <a-button  style="margin-left: 8px;width: 50px" @click="init(true)">重置</a-button>
      切换：<a-radio-group :options="plainOptions" defaultValue="month" :v-model="timeRange" @change="changeRange" />



    </div>
    <div id="gantt" ref="gantt" :style="{height: height+'px'}"></div>

    <div style="height: 45px">
      <a-pagination @change="changePageSize" @showSizeChange="changePageSize"
        style="position: absolute;bottom: 20px;right: 20px;"
        :total="page.totalRows"
        :show-total="(total, range) => `${range[0]}-${range[1]} 共 ${total} 条`"
        show-size-changer
        :page-size="page.pageSize"
        :pageSizeOptions="['10', '20', '30', '40']"
        size="small"
        :default-current="1"
      />
    </div>


  </div>

</template>

<script>
  import {gantt} from './dhtmlxgantt/codebase/dhtmlxgantt';
  import "./dhtmlxgantt/codebase/dhtmlxgantt.css";
  import "./dhtmlxgantt/codebase/ext/dhtmlxgantt_drag_timeline.js";
  import "./dhtmlxgantt/codebase/locale/locale_cn.js";
  import "./dhtmlxgantt/codebase/ext/dhtmlxgantt_marker.js";
  import "./dhtmlxgantt/codebase/ext/dhtmlxgantt_tooltip.js";
  import { Pagination } from 'ant-design-vue';
  import moment from 'moment';

  import {
    testProgressGantList
  } from '@/api/modular/system/testProgressManager'

  export default {
    components: {
      'a-pagination': Pagination
    },
    name: 'outGant',

    data() {
      return {
        allAddress:null,
        statusList:['Plan','Ongoing'],
        address:'all',
        page:{pageNo:1,pageSize:10,totalRows:10,totalPage:1},
        show: false,
        labelCol: {

          sm: {
            span: 10
          }
        },
        wrapperCol: {

          sm: {
            span: 13
          }
        },
        queryParam: {},
        timeRange: 'month',
        data: [],
        height: document.documentElement.clientHeight - 220,
        plainOptions:[{ label: '年', value: 'year' },
          { label: '季度', value: 'quarter' },
          { label: '月', value: 'month' },
          { label: '周', value: 'week' },
          { label: '日', value: 'day' }],
        columns: [
          {
            label: '序号',
            name: 'index',
            align: 'center',
            width: 40,
            fixed:'left',
          }, {
            label: '测试申请单',
            width: 100,

            fixed:'left',
            align: 'center',
            name: 'testCode',
          }, {
            label: '电芯载体',
            width: 70,

            resizable:false,
            align: 'center',
            name: 'sampleType',
          }, {
            label: '产品名称',
            width: 70,
            resize:true,
            align: 'center',
            name: 'productName',
          }, {
            label: '产品样品阶段',
            width: 100,

            align: 'center',
            name: 'productSampleStage',
          }, {
            label: '测试类型',
            width: 120,

            align: 'center',
            name: 'testType',
          }, {
            label: '申请部门',
            width: 120,
            align: 'center',

            name: 'dept',
          }, {
            label: '申请人',
            width: 70,

            align: 'center',
            name: 'applicant',
          }, {
            label: '测试项目',
            width: 120,
            align: 'center',

            name: 'testProject',
          },   {
            label: '测试项目别名',
            width: 80,
            align: 'center',
            name: 'testAlias',
          },{
            label: '测试目的',
            width: 80,
            align: 'center',

            name: 'testPurpose',
          }, {
            label: 'T/℃',
            width: 70,
            align: 'center',

            name: 't',
          }, {
            label: 'SOC',
            width: 70,
            align: 'center',

            name: 'soc',
          },{
            label: '中检次数',
            width: 70,
            align: 'center',
            template:function(task){

              return task.data.length

            },
            name: 'data.length',
          }, {
            label: '测试周期',
            width: 70,
            align: 'center',

            name: 'testPeriod',
          }, {
            label: '数量',
            width: 70,
            align: 'center',

            name: 'quantity',
          }, {
            label: '测试技师',
            width: 70,
            align: 'center',

            name: 'testMan',
          }, {
            label: '测试地点',
            width: 80,
            align: 'center',
            name: 'testAddress',
            template:function(task){
              if(task.testAddress == 'A1_3F'){
                return 'V圆柱检测室'
              }
              if(task.testAddress == 'R2_2F'){
                return '材料验证检测室'
              }
              if(task.testAddress == 'R4_4F'){
                return '动力电池检测室'
              }

            },
          },{
            label: '存储位置',
            width: 70,
            align: 'center',

            name: 'saveAddress',
          }, {
            label: '测试状态',
            width: 70,
            align: 'center',

            name: 'testStatus',
          }, {
            label: '已存储天数',
            width: 100,
            align: 'center',

            name: 'finishDay',
          }, {
            label: '进箱开始时间',
            width: 90,
            align: 'center',
            template:function(task){
              if(task.zero != null){
                return moment(task.zero).format("YYYY-MM-DD")
              }
            },
            name: 'zero',
          },


        ]
      };
    },

    mounted() {

      if(this.hasPerm('progress:all')){
        this.address = 'all'
      }else if(this.hasPerm('progress:a1_3f')){
        this.address = 'hz'
      }else if(this.hasPerm('progress:b4_3f')){
        this.address = 'hz'
      }else if(this.hasPerm('progress:r4_3f_v')){
        this.address = 'hz'
      }else if(this.hasPerm('progress:r4_3f_cc')){
        this.address = 'hz'
      }else if(this.hasPerm('progress:r4_4f')){
        this.address = 'hz'
      }else if(this.hasPerm('progress:r4_5f')){
        this.address = 'hz'
      }else if(this.hasPerm('progress:jm')){
        this.address = 'JM'
      }else if(this.hasPerm('progress:r3')){
        this.address = 'R3'
      }else{
        this.address = 'none'
      }
      this.init()
    },
    destroyed() {
      gantt.clearAll()
    },
    methods: {
      rangeChange(a,b){


        this.queryParam.rangeBegin = b[0]
        this.queryParam.rangeEnd = b[1]

        this.$nextTick(() => {
          this.init()
        })
      },
      changePageSize(a,b){
        this.page.pageSize = b
        this.page.pageNo = a
        this.$nextTick(() => {
          this.init()
        })

      },
      changeRange(e) {
        this.timeRange = e.target.value
        gantt.ext.zoom.setLevel(this.timeRange);
        var today = gantt.date.date_part(new Date());
        gantt.showDate(today);
      },

      changeStatus(a){

        this.queryParam.statusList = a

        this.init()
      },

      init(flag) {

        if(flag==true){
          this.queryParam = {}
          this.allAddress = null
          this.queryParam.rangeBegin = null
          this.queryParam.rangeEnd = null
          this.queryParam.range = null
          this.queryParam.statusList = null
          this.statusList = []
        }

       /* if(this.address != null && this.address != 'all'){
          this.queryParam.testAddress = this.address
        }else{
          this.queryParam.testAddress = null
        }*/

       /* if(this.address != null && this.address == 'lims'){
          this.queryParam.testAddress = null
          this.queryParam.source = 'lims'
          this.queryParam.statusList = ['Plan']
        } else*/ if(this.address != null && this.address != 'all'){
          this.queryParam.testAddress = this.address
          this.queryParam.source = null
          this.queryParam.statusList = this.status
        }else{
          this.queryParam.testAddress = this.allAddress
          this.queryParam.source = null
          this.queryParam.statusList = this.status
        }


        this.queryParam.statusList = this.statusList

        gantt._clear_data()

        this.queryParam.pageSize = this.page.pageSize
        this.queryParam.pageNo = this.page.pageNo
        let _that = this
        testProgressGantList(this.queryParam).then(res => {
            this.data = res.data.rows
            this.page = res.data
            this.page.rows = null
          }).then(res => {
            gantt.config.show_errors = false;
            gantt.config.autoscroll = true;
            //gantt.config.sort = true;
            gantt.config.scroll_on_click = false;
            gantt.config.rtl = false;
            gantt.config.row_height = 35;
            gantt.config.task_height = 24;
            gantt.config.bar_height = 24;
            gantt.config.open_tree_initially = true;
            gantt.config.drag_links = true;
            gantt.config.drag_resize = false;
            gantt.config.details_on_dblclick = false;
            gantt.config.drag_lightbox = true;
            gantt.config.drag_progress = true;
            gantt.config.drag_move = true;//能否调整
            gantt.config.preserve_scroll = true;//图表刷新后，滚动条的位置跟原来保持一致
            //gantt.config.resize_rows = true;
            gantt.config.min_task_grid_row_height = 30;

            gantt.config.scroll_size = 10;
            var labelYear = "年";
            var labelMonth = "月";
            var labelDay = "日";
            var labelWeek = "周";
            var labelOrder = "第";

            var zoomConfig = {
              levels: [{
                name: "hour",
                scale_height: 80,
                min_column_width: 60,
                scales: [
                  {
                    unit: "day", step: 1, format: "%Y" + labelYear + "%n" + labelMonth + "%d" + labelDay,
                  }, {
                    unit: "hour", step: 1, format: "%H" + ":00"
                  }
                ]
              }, {
                name: "day",
                scale_height: 80,
                min_column_width: 50,
                scales: [
                  // {unit: "day", step: 1, format: "%n" + labelMonth + "%d" + labelDay}
                  {unit: "month", format: "%Y" + labelYear + "%n" + labelMonth},
                  {
                    unit: "day", step: 1, format: "%d"

                  }
                ]
              }, {
                name: "week",
                scale_height: 80,
                min_column_width: 52,
                scales: [
                  {
                    unit: "week", step: 1, format: function (date) {
                      var dateToStr = gantt.date.date_to_str("%n" + labelMonth + "%d" + labelDay);
                      var endDate = gantt.date.add(date, 6, "day");
                      var weekNum = gantt.date.date_to_str("%W")(date);
                      return "W" + weekNum + "&nbsp;&nbsp;" + dateToStr(date) + " - " + dateToStr(endDate);
                    }
                  },
                  // {unit: "day", step: 1, format: "%j %D"}
                  {
                    unit: "day", step: 1, format: function (date) {
                      var dateToStr = gantt.date.date_to_str("%d");
                      var weekToStr = gantt.date.date_to_str("%D");
                      return "<div style='line-height: 20px;'>" + dateToStr(date) + "<br /><span class='cell-text'>" + weekToStr(date) + "</span></div>";
                    }
                  }
                ]
              }, {
                name: "month",
                scale_height: 80,
                min_column_width: 60,
                scales: [
                  {unit: "month", format: "%Y" + labelYear + "%n" + labelMonth},
                  // {unit: "week", format: labelOrder + "%W " + labelWeek}
                  {
                    unit: "week", format: function (date) {
                      var weekNum = gantt.date.date_to_str("%W")(date);
                      return "W" + weekNum;
                    }
                  }
                ]
              }, {
                name: "quarter",
                height: 50,
                min_column_width: 90,
                scales: [
                  {unit: "month", step: 1, format: "%Y" + labelYear + "%n" + labelMonth},
                  {
                    unit: "quarter", step: 1, format: function (date) {
                      var dateToStr = gantt.date.date_to_str("%Y年%n月");
                      var endDate = gantt.date.add(gantt.date.add(date, 3, "month"), -1, "day");
                      return dateToStr(date) + " - " + dateToStr(endDate);
                    }
                  }
                ]
              }, {
                name: "year",
                scale_height: 80,
                min_column_width: 50,
                scales: [
                  {unit: "year", step: 1, format: "%Y" + labelYear},
                  {unit: "month", step: 1, format: "%n" + labelMonth}
                ]
              }]
            };

            gantt.ext.zoom.init(zoomConfig);

            gantt.ext.zoom.setLevel(this.timeRange);


            gantt.config.xml_date = "%Y-%m-%d";
            gantt.config.scale_height = 80 //设置甘特图的表头高度
            //鼠标移入展示信息
            /*gantt.plugins({
              tooltip: true
            })*/
            //时间展示 2021-10-11 07:22
            gantt.templates.tooltip_date_format = gantt.date.date_to_str("%Y-%m-%d")
            //鼠标移入展示信息
            gantt.config.readonly = true //甘蔗图只读属性
            gantt.config.round_dnd_dates = false //将任务开始时间和结束时间自动“四舍五入'
            gantt.config.root_id = "root"
            //添加taba栏
            gantt.config.columns = this.columns
            gantt.config.layout = {
              css: "gantt_container",
              cols: [
                {
                  width:400,
                  min_width: 300,
                  left_column_width:150,
                  // adding horizontal scrollbar to the grid via the scrollX attribute
                  rows:[
                    {view: "grid", scrollX: "gridScroll", scrollable: true, scrollY: "scrollVer"},
                    {view: "scrollbar", id: "gridScroll"}
                  ]
                },
                {resizer: true, width: 1},
                {
                  rows:[
                    {view: "timeline", scrollX: "scrollHor", scrollY: "scrollVer"},
                    {view: "scrollbar", id: "scrollHor"}
                  ]
                },
                {view: "scrollbar", id: "scrollVer"}
              ]
            };

            gantt.getMarker(gantt.addMarker({
              start_date: new Date(),
              css: 'marker',
              text: '今天',
            }));

            gantt.templates.tooltip_text = function(start, end, task) {
              var testCode = '测试申请单';
              var sampleType = '电芯载体';
              var productName = '产品名称';
              var productSampleStage = '产品样品阶段';
              var testType = '测试类型';
              var dept = '申请部门';
              var applicant = '申请人';
              var testProject = '测试项目';
              var t = 'T/℃';
              var soc = 'SOC';
              var testPeriod = '测试周期';
              var quantity = '数量';
              var testMan = '测试技师';
              var testAddress = '测试地点';
              var testStatus = '测试状态';
              var finishDay = '已完成天数';
              var zero = '进箱开始时间';
              var testNum = '中检次数';
              var realStarttime = '开始时间';
              var realEndtime = '结束时间';
              var str = ''
              if (task.parent != 'root') {
                str =
                  '<span class="tooltip-scale-text">存储天数：</span><b>'+task.text+'</b><br/>' +
                  '<span class="tooltip-scale-text">开始时间：</span><b>' + moment(task.start_date).format("YYYY-MM-DD")  + '</b><br/>'+
                  '<span class="tooltip-scale-text">结束时间：</span><b>' + moment(task.end_date).subtract(1, 'days').format("YYYY-MM-DD") + '</b><br/>';
              } else {
                str = '<div style="width: 350px"><div style="width: 50%;float: left"><span class="tooltip-scale-text">' + testCode + '：</span><b>' + task.testCode + '</b></div>' +
                  '<div style="width: 50%;float: left"><span class="tooltip-scale-text">' + sampleType + '：</span><b>' + task.sampleType + '</b></div></div>' +
                  '<div style="width: 350px"><div style="width: 50%;float: left"><span class="tooltip-scale-text">' + productName + '：</span><b>' + task.productName + '</b></div>' +
                  '<div style="width: 50%;float: left"><span class="tooltip-scale-text">' + productSampleStage + '：</span><b>' + task.productSampleStage + '</b></div></div>'+
                  '<div style="width: 350px"><div style="width: 50%;float: left"><span class="tooltip-scale-text">' + testType + '：</span><b>' + task.testType + '</b></div>'+
                  '<div style="width: 50%;float: left"><span class="tooltip-scale-text">' + dept + '：</span><b>' + task.dept + '</b></div></div>'+
                  '<div style="width: 350px"><div style="width: 50%;float: left"><span class="tooltip-scale-text">' + applicant + '：</span><b>' + task.applicant + '</b></div>'+
                  '<div style="width: 50%;float: left"><span class="tooltip-scale-text">' + testProject + '：</span><b>' + task.testProject + '</b></div></div>'+
                  '<div style="width: 350px"><div style="width: 50%;float: left"><span class="tooltip-scale-text">' + t + '：</span><b>' + task.t + '</b></div>'+
                  '<div style="width: 50%;float: left"><span class="tooltip-scale-text">' + soc + '：</span><b>' + task.soc + '</b></div></div>'+
                  '<div style="width: 350px"><div style="width: 50%;float: left"><span class="tooltip-scale-text">' + testPeriod + '：</span><b>' + task.testPeriod + '</b></div>'+
                  '<div style="width: 50%;float: left"><span class="tooltip-scale-text">' + quantity + '：</span><b>' + task.quantity + '</b></div></div>'+
                  '<div style="width: 350px"><div style="width: 50%;float: left"><span class="tooltip-scale-text">' + testMan + '：</span><b>' + task.testMan + '</b></div>'+
                  '<div style="width: 50%;float: left"><span class="tooltip-scale-text">' + testAddress + '：</span><b>' + task.testAddress + '</b></div></div>'+

                  '<div style="width: 350px"><div style="width: 50%;float: left"><span class="tooltip-scale-text">存储位置：</span><b>' + task.saveAddress + '</b></div>'+

                  '<div style="width: 350px"><div style="width: 50%;float: left"><span class="tooltip-scale-text">' + testStatus + '：</span><b>' + task.testStatus + '</b></div>'+
                  '<div style="width: 50%;float: left"><span class="tooltip-scale-text">' + finishDay + '：</span><b>' + (task.finishDay==null?'':task.finishDay) + '</b></div></div>'+
                  '<div style="width: 350px"><div style="width: 50%;float: left"><span class="tooltip-scale-text">' +  testNum + '：</span><b>' +task.data.length  + '</b></div>'+
                  '<div style="width: 50%;float: left"><span class="tooltip-scale-text">' + zero + '：</span><b>' + moment(task.zero).format("YYYY-MM-DD")  + '</b></div></div>'+

                  '<div style="width: 350px"><div style="width: 50%;float: left"><span class="tooltip-scale-text">' + realStarttime + '：</span><b>' +moment(task.start_date).format("YYYY-MM-DD")  + '</b></div>'+
                  '<div style="width: 50%;float: left"><span class="tooltip-scale-text">' + realEndtime + '：</span><b>' + moment(task.end_date).subtract(1, 'days').format("YYYY-MM-DD") + '</b></div></div>';
              }
              if (!gantt.ext.tooltips.tooltip._isTooltipVisible) {
                // 启动倒计时关闭tooltip
                setTimeout(function() {
                  gantt.ext.tooltips.tooltip.hide();
                }, 5000); // 设置延迟时间，单位为毫秒
              }
              return str

            };


            gantt.init(this.$refs.gantt)

            gantt.parse({data: this.data})
            let today = gantt.date.date_part(new Date());

            gantt.showDate(today);
            gantt.init(this.$refs.gantt)


          })




      },
    },
  };
</script>

<style lang='less' scoped=''>

  /deep/.gantt_task_cell {
    color: #000000;
    border-right: 0;
  }
  /deep/ .gantt_cell {
    color: #000000;
    border-right: 1px solid #ebebeb;
  }
  /deep/ .gantt_grid_head_cell {
    color: #000000;
    border-right: 1px solid #ebebeb!important;;
  }
  /deep/.gantt_task {
    color: #000000!important;
  }

 /deep/.gantt_task_scale  {
    color: #000000!important;
  }

  /deep/.gantt_scale_cell {
    color: #000000!important;
  }
  /deep/ .gantt_task_line {
    border-radius: 10px;
  }
  /deep/ .gantt_resizer {
    cursor: e-resize;
    position: relative;
  }

 /* /deep/ .gantt_task_line {
    background: url("caps-unlock-filling.png") left / 25px 25px no-repeat,url("caps-unlock-filling - 副本.png") right / 25px 25px no-repeat;
  }*/

  /deep/ .gantt_container {

    box-shadow: rgb(126 139 169 / 20%) 2px 2px 13px 2px;
    border-radius: 6px;
  }
  /deep/ .gantt_grid_data .gantt_row.gantt_selected, .gantt_grid_data .gantt_row.odd.gantt_selected, .gantt_task_row.gantt_selected {
    background-color: unset;
  }
  /deep/.gantt_task_row.gantt_selected .gantt_task_cell {
    border-right-color: unset;
  }
  /deep/.gantt_selected {
    background-color: unset;
  }
  /deep/.gantt_row.gantt_row_task:hover{
    background-color: #00000000;
  }
  /deep/span.ant-radio + * {
    padding-right: 0px;
    padding-left: 0px;
  }
  /deep/ .ant-col {
    padding: 0 !important;
    height: 40px !important;
  }
  /deep/.ant-btn > i, /deep/.ant-btn > span {
    display: flex;
    justify-content: center;
  }

  /deep/.gantt_task_drag {
    cursor: ew-resize;
  }
  /deep/.gantt_task_resize.gantt_task_resize_right:before {
    width: 10px;
  }


  /deep/.gantt_layout_cell {
    border-top: 0;
    border-right: 0;
    box-shadow: -1px 4px 6px 5px rgba(126,139,169,0.07);
    border-radius: 6px 0 0 6px;
  }

  /deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
    color: #fff;
    background: #1890FF;
  }

  /deep/ .gantt_container {
    border: 1px solid #bcbcbc;
    border-radius: 10px;
  }

  /deep/ .gantt_fixed_column {
    width: 200px; /* 固定左侧 2 列 */
    pointer-events: none; /* 禁止拖动 */
  }

</style>