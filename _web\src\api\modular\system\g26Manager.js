import { axios } from '@/utils/request'

// G26-报告任务新增、暂存或实时修改
export function g26TestReportTask (parameter,id = null,reportName = null) {
  return axios({
    url: id == null ? '/g26TestReportTask/addG26?reportName=' + reportName : '/g26TestReportTask/addG26?id=' + id ,
    method: 'post',
    data: parameter
  })
}

// G26-报告任务提交
export function commitG26(parameter,id,reportName) {
  reportName = reportName.replaceAll("%","").replaceAll("&","")
  return axios({
    url: '/g26TestReportTask/commitG26?id=' + id + '&reportName=' + reportName,
    method: 'post',
    data: parameter
  })
}

// G26-根据id查询报告任务
export function getG26ById(parameter) {
  return axios({
    url: '/g26TestReportTask/getById',
    method: 'post',
    data: parameter
  })
}

// G26日历寿命-报告任务新增、暂存或实时修改
export function g26CalendarTestReportTask (parameter, id = null) {
  return axios({
    url: id == null ? '/g26TestReportTask/addG26Calendar' : '/g26TestReportTask/addG26Calendar?id=' + id,
    method: 'post',
    data: parameter
  })
}

// G26日历寿命-报告任务提交
export function commitG26Calendar(parameter, id) {
  return axios({
    url: '/g26TestReportTask/commitG26Calendar?id=' + id,
    method: 'post',
    data: parameter
  })
}