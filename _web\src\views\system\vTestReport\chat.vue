<template>
  <div class="wrapper">
    问题：
    <a-textarea v-model="question" @keyup.enter="chat"></a-textarea>
    回答：
    <a-textarea :value="messages" style="height: 500px"></a-textarea>
  </div>
</template>

<script>


import {aiAddQuestion} from "@/api/modular/system/ai";

export default {

  data: function () {
    return {
      messages:"",
      question:"",
      eventSource:null,
      questionId:null
    }

  },

  created() {
    let a = [
      {
        "createTime": "2024-05-23 16:30:31",
        "createUser": null,
        "notInsert": null,
        "notUpdate": null,
        "createAccount": "superAdmin",
        "createName": "-",
        "updateTime": null,
        "updateUser": null,
        "updateAccount": null,
        "updateName": null,
        "deleteStatus": 0,
        "pageSize": 20,
        "pageNo": 1,
        "id": "1793560126780178781",
        "corpNo": "1500",
        "yearTime": "2019",
        "monthTime": "12",
        "zcoStatus": 1,
        "countType": 1,
        "coProof": "**********",
        "taxProof": "**********",
        "subject": "**********",
        "amount": 15000,
        "feeDesc": "创能研发分摊（201901到201911）",
        "closeAccountType": 0,
        "receiveAccount": null,
        "receiveDesc": null,
        "proofNo": "***********",
        "comeFrom": 2,
        "proofNoDesc": "M11（创能）",
        "subjectDesc": "工资-劳务人员费用",
        "zcoTag": 0,
        "zcoStageTag": 0,
        "profitCenter": "**********",
        "functionArea": "3000",
        "rollInOut": 1,
        "corpNos": null,
        "subjects": null,
        "closeAccountTypes": null,
        "keyword": null,
        "proofNos": null,
        "ids": null,
        "tagIds": null,
        "tagNames": null,
        "tagIdsStr": null,
        "zcoTags": null,
        "flag": null,
        "total": null,
        "year": "2024",
        "month": "1",
        "yearMonth": null
      },
      {
        "createTime": "2024-05-23 16:30:31",
        "createUser": null,
        "notInsert": null,
        "notUpdate": null,
        "createAccount": "superAdmin",
        "createName": "-",
        "updateTime": null,
        "updateUser": null,
        "updateAccount": null,
        "updateName": null,
        "deleteStatus": 0,
        "pageSize": 20,
        "pageNo": 1,
        "id": "1793560126780178742",
        "corpNo": "1500",
        "yearTime": "2019",
        "monthTime": "12",
        "zcoStatus": 1,
        "countType": 1,
        "coProof": "**********",
        "taxProof": "**********",
        "subject": "**********",
        "amount": -15000,
        "feeDesc": "创能研发分摊（201901到201911）",
        "closeAccountType": 0,
        "receiveAccount": null,
        "receiveDesc": null,
        "proofNo": "***********",
        "comeFrom": 2,
        "proofNoDesc": "M11（创能）",
        "subjectDesc": "工资-劳务人员费用",
        "zcoTag": 0,
        "zcoStageTag": 0,
        "profitCenter": "**********",
        "functionArea": "3000",
        "rollInOut": 1,
        "corpNos": null,
        "subjects": null,
        "closeAccountTypes": null,
        "keyword": null,
        "proofNos": null,
        "ids": null,
        "tagIds": null,
        "tagNames": null,
        "tagIdsStr": null,
        "zcoTags": null,
        "flag": null,
        "total": null,
        "year": "2024",
        "month": "1",
        "yearMonth": null
      },
      {
        "createTime": "2024-05-23 16:30:31",
        "createUser": null,
        "notInsert": null,
        "notUpdate": null,
        "createAccount": "superAdmin",
        "createName": "-",
        "updateTime": null,
        "updateUser": null,
        "updateAccount": null,
        "updateName": null,
        "deleteStatus": 0,
        "pageSize": 20,
        "pageNo": 1,
        "id": "1793560126780178709",
        "corpNo": "1500",
        "yearTime": "2019",
        "monthTime": "12",
        "zcoStatus": 1,
        "countType": 1,
        "coProof": "**********",
        "taxProof": "**********",
        "subject": "**********",
        "amount": 15000,
        "feeDesc": "创能研发分摊（201901到201911）",
        "closeAccountType": 0,
        "receiveAccount": null,
        "receiveDesc": null,
        "proofNo": "***********",
        "comeFrom": 2,
        "proofNoDesc": "M11（创能）",
        "subjectDesc": "工资-劳务人员费用",
        "zcoTag": 0,
        "zcoStageTag": 0,
        "profitCenter": "**********",
        "functionArea": "3000",
        "rollInOut": 1,
        "corpNos": null,
        "subjects": null,
        "closeAccountTypes": null,
        "keyword": null,
        "proofNos": null,
        "ids": null,
        "tagIds": null,
        "tagNames": null,
        "tagIdsStr": null,
        "zcoTags": null,
        "flag": null,
        "total": null,
        "year": "2024",
        "month": "1",
        "yearMonth": null
      },
      {
        "createTime": "2024-05-23 16:30:21",
        "createUser": null,
        "notInsert": null,
        "notUpdate": null,
        "createAccount": "superAdmin",
        "createName": "-",
        "updateTime": null,
        "updateUser": null,
        "updateAccount": null,
        "updateName": null,
        "deleteStatus": 0,
        "pageSize": 20,
        "pageNo": 1,
        "id": "1793560082526077483",
        "corpNo": "1500",
        "yearTime": "2020",
        "monthTime": "9",
        "zcoStatus": 1,
        "countType": 1,
        "coProof": "**********",
        "taxProof": "**********",
        "subject": "**********",
        "amount": 20044.57,
        "feeDesc": "创能202009研发分摊",
        "closeAccountType": 0,
        "receiveAccount": null,
        "receiveDesc": null,
        "proofNo": "***********",
        "comeFrom": 2,
        "proofNoDesc": "LF230开发项目",
        "subjectDesc": "工资-劳务人员费用",
        "zcoTag": 0,
        "zcoStageTag": 0,
        "profitCenter": "**********",
        "functionArea": "3000",
        "rollInOut": 1,
        "corpNos": null,
        "subjects": null,
        "closeAccountTypes": null,
        "keyword": null,
        "proofNos": null,
        "ids": null,
        "tagIds": null,
        "tagNames": null,
        "tagIdsStr": null,
        "zcoTags": null,
        "flag": null,
        "total": null,
        "year": "2024",
        "month": "1",
        "yearMonth": null
      },
      {
        "createTime": "2024-05-23 16:30:21",
        "createUser": null,
        "notInsert": null,
        "notUpdate": null,
        "createAccount": "superAdmin",
        "createName": "-",
        "updateTime": null,
        "updateUser": null,
        "updateAccount": null,
        "updateName": null,
        "deleteStatus": 0,
        "pageSize": 20,
        "pageNo": 1,
        "id": "1793560082526077301",
        "corpNo": "1500",
        "yearTime": "2019",
        "monthTime": "8",
        "zcoStatus": 1,
        "countType": 1,
        "coProof": "**********",
        "taxProof": "**********",
        "subject": "**********",
        "amount": 25000,
        "feeDesc": "调整Q7工厂创能生产部费用",
        "closeAccountType": 0,
        "receiveAccount": null,
        "receiveDesc": null,
        "proofNo": "***********",
        "comeFrom": 2,
        "proofNoDesc": "LF230开发项目",
        "subjectDesc": "工资-劳务人员费用",
        "zcoTag": 0,
        "zcoStageTag": 0,
        "profitCenter": "**********",
        "functionArea": "3000",
        "rollInOut": 1,
        "corpNos": null,
        "subjects": null,
        "closeAccountTypes": null,
        "keyword": null,
        "proofNos": null,
        "ids": null,
        "tagIds": null,
        "tagNames": null,
        "tagIdsStr": null,
        "zcoTags": null,
        "flag": null,
        "total": null,
        "year": "2024",
        "month": "1",
        "yearMonth": null
      }
    ]

    console.log(JSON.stringify(a))

    aiAddQuestion({}).then(res => {
      this.questionId = res.data
    })
  },

  methods: {

    chat(){
      let url = 'http://10.5.69.22:82/open/chat?question='+this.question
      if(this.questionId){
        url += '&questionId='+this.questionId
      }

      this.messages = ""
      this.eventSource  = new EventSource(url)
      this.eventSource.onmessage = (event) => {
        const message = event.data;
        this.messages+=message;
        this.messages = this.messages.replace("。","\n")
        this.messages = this.messages.replace("？","\n")
        this.messages = this.messages.replace("*","")
      };

      this.eventSource.onerror = (error) => {
        if (this.eventSource) {
          this.eventSource.close();
        }
      };
    },

  }
}
</script>
<style lang="less" scoped>
:root {
  --height: 600px;
}

</style>