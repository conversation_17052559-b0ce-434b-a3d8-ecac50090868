<template>
  <a-card :title="title" :bordered="bordered" class="base-card" :body-style="cardBodyStyle">
    <slot></slot>
  </a-card>
</template>

<script>
export default {
  name: 'BaseCard',
  props: {
    title: {
      type: String,
      default: ''
    },
    bordered: {
      type: Boolean,
      default: true
    },
    padded: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    cardBodyStyle() {
      return { padding: this.padded ? '24px' : '0' };
    }
  }
};
</script>

<style scoped>
.base-card {
  margin-bottom: var(--spacing-lg, 24px);
  background-color: #fff;
  border-radius: var(--border-radius-base, 8px);
  box-shadow: var(--box-shadow-card, 0 2px 8px rgba(0, 0, 0, 0.05));
  transition: all 0.3s ease;
  border: 1px solid var(--border-color-split, #f0f0f0);
  overflow: hidden;
}

.base-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

:deep(.ant-card-head) {
  min-height: 48px;
  padding: 0 var(--spacing-lg, 24px);
  font-weight: 500;
  font-size: var(--font-size-lg, 16px);
  border-bottom: 1px solid var(--border-color-split, #f0f0f0);
}

:deep(.ant-card-head-title) {
  font-weight: 600;
  padding: 12px 0;
}

:deep(.ant-card-body) {
  transition: all 0.3s ease;
}
</style>
