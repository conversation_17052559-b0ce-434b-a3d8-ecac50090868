<template>
  <a-modal  :width="1200" :height="600"
           :bodyStyle="{padding:0}"
           :visible="visible1"  style="padding: 0;"
           :zIndex="parseInt(1001)"
           :maskClosable="false"
           :confirmLoading="confirmLoading1"
           @cancel="handleCancel">

    <template slot="title">
      <span>{{record.testcode+' '+record.testname + ' '+ record.celltestcode +' 原始数据'}}</span>
      <a-radio-group v-model="db" style="margin:8px" @change="getData" button-style="solid" >
        <a-radio-button value="doris">
          数据源1
        </a-radio-button>
        <a-radio-button value="mongodb">
          数据源2
        </a-radio-button>
      </a-radio-group>

    </template>
    <template slot="footer">
      <a-button key="back" @click="handleCancel">
        关闭
      </a-button>
    </template>
    <a-button type="primary" @click="exportData" style=" position: absolute; margin-top: 5px;right: 5px; z-index: 1;">导出</a-button>
    <a-button :loading="syncLoading" type="primary" @click="syncData" v-if="hasPerm('data:sync')" style=" position: absolute; margin-top: 5px;right: 5px; z-index: 1;">同步</a-button>

    <a-tabs type="card" @change="getData()" v-model="dataType">
      <a-tab-pane key="step" tab="工步数据" >
        <pbiSearchContainer>
          <pbiSearchItem :span="4" label='工步号' >
            <a-input-number  v-model="queryParam.stepId"  @keyup.enter="$refs.table2.refresh()"  />
          </pbiSearchItem>
          <pbiSearchItem :span="6" label='工步号范围' >
            <a-input-group compact>

              <a-input-number v-model="queryParam.stepIdBefore"  @keyup.enter="$refs.table2.refresh()"   style=" width: 70px; text-align: center" placeholder="Min" />
              <a-input
                style=" width: 30px; border-left: 0; pointer-events: none; backgroundColor: #fff"
                placeholder="~"
                disabled
              />
              <a-input-number v-model="queryParam.stepIdAfter"  @keyup.enter="$refs.table2.refresh()"   style="width: 70px; text-align: center; border-left: 0" placeholder="Max" />
            </a-input-group>
          </pbiSearchItem>
          <pbiSearchItem :span="4" label='工步名称'>
            <a-input v-model="queryParam.stepName" @keyup.enter="$refs.table2.refresh()" />
          </pbiSearchItem>
          <pbiSearchItem :span="4" label='工步时间'>
            <a-input v-model="queryParam.stepTimeString" @keyup.enter="$refs.table2.refresh()" />
          </pbiSearchItem>
          <pbiSearchItem :span="4" label='循环号'>
            <a-input-number  v-model="queryParam.cycleId"  @keyup.enter="$refs.table2.refresh()"  />
          </pbiSearchItem>
          <pbiSearchItem :span="2" type='btn' class="search-container">
            <div class="secondary-btn">
              <a-button @click="$refs.table2.refresh()" class="mr10" type="primary">
                查询
              </a-button>
            </div>
            <div class="secondary-btn">
              <a-button @click="reset()" class="mr10">
                重置
              </a-button>
            </div>
          </pbiSearchItem>
        </pbiSearchContainer>
        <s-table :columns="db == 'doris' || record.eptfactoryname == 'JM' ?columnsDoris:columns" :data="loadData" bordered :scroll="{x:true}" :rowKey="(record) => record._id"
                 ref="table2"
        >

        </s-table>
      </a-tab-pane>
      <a-tab-pane key="data" tab="详细数据" >
        <pbiSearchContainer>
          <pbiSearchItem :span="4" label='工步号' >
            <a-input-number  v-model="dataQueryParam.stepId"  @keyup.enter="$refs.table1.refresh()"  />
          </pbiSearchItem>
          <pbiSearchItem :span="6" label='工步号范围' >
            <a-input-group compact >

              <a-input-number v-model="dataQueryParam.stepIdBefore"  @keyup.enter="$refs.table1.refresh()"   style=" width: 70px; text-align: center" placeholder="Min" />
              <a-input
                style=" width: 30px; border-left: 0; pointer-events: none; backgroundColor: #fff"
                placeholder="~"
                disabled
              />
              <a-input-number v-model="dataQueryParam.stepIdAfter"  @keyup.enter="$refs.table1.refresh()"   style="width: 70px; text-align: center; border-left: 0" placeholder="Max" />
            </a-input-group>
          </pbiSearchItem>
          <pbiSearchItem :span="4" label='工步名称'>
            <a-input v-model="dataQueryParam.stepName" @keyup.enter="$refs.table1.refresh()" />
          </pbiSearchItem>
          <pbiSearchItem :span="4" label='循环号'>
            <a-input-number  v-model="dataQueryParam.cycleId"  @keyup.enter="$refs.table1.refresh()"  />
          </pbiSearchItem>
          <pbiSearchItem :span="6" type='btn' class="search-container">
            <div class="secondary-btn">
              <a-button @click="$refs.table1.refresh()" class="mr10" type="primary">
                查询
              </a-button>
            </div>
            <div class="secondary-btn">
              <a-button @click="detailReset()" class="mr10">
                重置
              </a-button>
            </div>
          </pbiSearchItem>
        </pbiSearchContainer>
        <s-table :columns="db == 'doris' || record.eptfactoryname == 'JM' ?dataColumnsDoris:dataColumns" :data="loadData1" bordered :scroll="{x:true}" :rowKey="(record) => record._id"
                 ref="table1"
        >

        </s-table>
      </a-tab-pane>
      <a-tab-pane key="cyc" tab="循环数据" >
        <s-table :columns="db == 'doris' || record.eptfactoryname == 'JM' ?cycColumnsDoris:cycColumns" :data="loadDataCyc" bordered :scroll="{x:true}" :rowKey="(record) => record._id"
                 ref="table3"
        >

        </s-table>
      </a-tab-pane>
      <a-tab-pane key="stepInfo" tab="工步信息" >
        <pbiSearchContainer>
          <pbiSearchItem :span="4" label='工步号' >
            <a-input-number  v-model="queryParam1.stepId"  @keyup.enter="$refs.table4.refresh()"  />
          </pbiSearchItem>
          <pbiSearchItem :span="6" label='工步号范围' >
            <a-input-group compact >

              <a-input-number v-model="queryParam1.stepIdBefore"  @keyup.enter="$refs.table4.refresh()"  :max="queryParam1.stepIdAfter" style=" width: 70px; text-align: center" placeholder="Min" />
              <a-input
                style=" width: 30px; border-left: 0; pointer-events: none; backgroundColor: #fff"
                placeholder="~"
                disabled
              />
              <a-input-number v-model="queryParam1.stepIdAfter"  @keyup.enter="$refs.table4.refresh()"  :min="queryParam1.stepIdBefore" style="width: 70px; text-align: center; border-left: 0" placeholder="Max" />
            </a-input-group>
          </pbiSearchItem>
          <pbiSearchItem :span="4" label='工步名称'>
            <a-input v-model="queryParam1.stepName" @keyup.enter="$refs.table4.refresh()" />
          </pbiSearchItem>
          <pbiSearchItem :span="10" type='btn' class="search-container">
            <div class="secondary-btn">
              <a-button @click="$refs.table4.refresh()" class="mr10" type="primary">
                查询
              </a-button>
            </div>
            <div class="secondary-btn">
              <a-button @click="stepInfoReset()" class="mr10">
                重置
              </a-button>
            </div>
          </pbiSearchItem>
        </pbiSearchContainer>

        <s-table id="stepInfo" :columns="db == 'doris' || record.eptfactoryname == 'JM' ?stepInfoColumnsDoris:stepInfoColumns" :scroll="{x:true}" :data="loadDataStepInfo" bordered :rowKey="(record) => record._id"
                 ref="table4"
        >

        </s-table>
      </a-tab-pane>
      <a-tab-pane key="log" tab="日志" >
				<pbiSearchContainer>
					<pbiSearchItem :span="6" label='类型' >
						<a-input  v-model="queryParamLog.recordType"  @keyup.enter="$refs.table5.refresh()"  />
					</pbiSearchItem>
					<pbiSearchItem :span="6" label='描述' >
						<a-input  v-model="queryParamLog.description"  @keyup.enter="$refs.table5.refresh()"  />
					</pbiSearchItem>
					<pbiSearchItem :span="12" type='btn' class="search-container">
						<div class="secondary-btn">
							<a-button @click="$refs.table5.refresh()" class="mr10" type="primary">
								查询
							</a-button>
						</div>
						<div class="secondary-btn">
							<a-button @click="logReset()" class="mr10">
								重置
							</a-button>
						</div>
					</pbiSearchItem>
				</pbiSearchContainer>
        <s-table :columns="logColumns" :data="loadLogData" bordered  :rowKey="(record) => record.recordId"
                 ref="table5"
        >
        </s-table>
      </a-tab-pane>
      <!--<a-tab-pane key="exportTask" tab="导出任务" >
        <a-table :columns="exportTaskcolumns" :data-source="exportTaskData" bordered
                 style="padding: 5px;height: 280px"
                 bordered
                 :scroll="{y: 240}"
                 :rowKey="(record) => record.id"
                 :pagination="false"
        >

          <template
            slot="taskName"
            slot-scope="text, record, index, columns"
          >
            <a :href="'http://'+record.ip+':82/sysFileInfo/download?id='+record.fileId"
               style="text-align: center" v-if="record.fileStatus == 20">{{text}}</a>
            <span v-else>{{text}}</span>
          </template>


        </a-table>
      </a-tab-pane>-->

    </a-tabs>


  </a-modal>
</template>

<script>
  import {
    shenghongStepListPage,shenghongDataListPage,shenghongDataExport,shenghongDataExportTaskList,shenghongLogPage
    ,shenghongCycListPage,shenghongStepInfoPage,syncDorisData
  } from '@/api/modular/system/limsManager'
  import { STable } from '@/components'

  export default {
    components: {
      STable
    },

    data() {
      return {
				queryParamLog:{},
        syncLoading:false,
        db:'doris',
        queryParam1:{},
        queryParam:{},
        dataQueryParam:{},
        dataType:'step',
        loadData: parameter => {

          return shenghongStepListPage(Object.assign(parameter, Object.assign(this.queryParam,{_id: this.record.id,flowId:this.record.flowId,barCode:this.db})) ).then((res) => {
              this.visible1 = true
              this.confirmLoading1 = false
              if(res.data.pageNo > res.data.totalPage){
                res.data.pageNo = res.data.totalPage +1
              }
              return res.data
          })
        },
        loadData1: parameter => {
          return shenghongDataListPage(Object.assign(parameter, Object.assign(this.dataQueryParam,{_id: this.record.id,flowId:this.record.flowId,barCode:this.db})) ).then((res) => {
              this.visible1 = true
              this.confirmLoading1 = false
              if(res.data.pageNo > res.data.totalPage){
                res.data.pageNo = res.data.totalPage+1
              }
              return res.data
          })
        },
        loadDataCyc: parameter => {
          return shenghongCycListPage(Object.assign(parameter, {_id: this.record.id,flowId:this.record.flowId,barCode:this.db}) ).then((res) => {
              this.visible1 = true
              this.confirmLoading1 = false
              if(res.data.pageNo > res.data.totalPage){
                res.data.pageNo = res.data.totalPage +1
              }
              return res.data
          })
        },
        loadDataStepInfo: parameter => {
          return shenghongStepInfoPage(Object.assign(parameter, Object.assign(this.queryParam1,{_id: this.record.id,flowId:this.record.flowId,barCode:this.db})) ).then((res) => {
              this.visible1 = true
              this.confirmLoading1 = false
              if(res.data.pageNo > res.data.totalPage){
                res.data.pageNo = res.data.totalPage+1
              }
              return res.data
          })
        },
        loadLogData: parameter => {
          return shenghongLogPage(Object.assign(parameter, Object.assign(this.queryParamLog,{_id: this.record.id,flowId:this.record.flowId,barCode:this.db})) ).then((res) => {
              this.visible1 = true
              this.confirmLoading1 = false
              if(res.data.pageNo > res.data.totalPage){
                res.data.pageNo = res.data.totalPage+1
              }
              return res.data
          })
        },

        exportTaskData:[],
        data: [],
        columns: [
          {
          title: '序号',
          align: 'center',
          width: 50,
          fixed:true,
          customRender: (text, record, index) => index + 1
          }, {
            title: '工步序号',
            dataIndex: 'stepNum',
            align: 'center',
            width: 90,
            fixed:true
          }, {
            title: '循环号',
            width: 90,
            align: 'center',
            dataIndex: 'cycleId',
            fixed:true,
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '工步号',
            width: 90,
            align: 'center',
            dataIndex: 'stepId',
            fixed:true,
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '工步名称',
            width: 90,
            align: 'center',
            dataIndex: 'stepName',
            fixed:true,
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '工步时间',
            width: 90,
            align: 'center',
            dataIndex: 'stepTimeString',
          }, {
            title: '绝对时间',
            width: 180,
            align: 'center',
            dataIndex: 'absoluteTime',
          }, {
            title: '容量/Ah',
            width: 90,
            align: 'center',
            dataIndex: 'capacity',
          }, {
            title: '能量/Wh',
            width: 90,
            align: 'center',
            dataIndex: 'energy',
          }, {
            title: '起始电压/V',
            width: 90,
            align: 'center',
            dataIndex: 'beginVoltage',
          }, {
            title: '终止电压/V',
            width: 90,
            align: 'center',
            dataIndex: 'endVoltage',
          }, {
            title: '起始电流/A',
            width: 90,
            align: 'center',
            dataIndex: 'startCurrent',
          }, {
            title: '终止电流/A',
            width: 90,
            align: 'center',
            dataIndex: 'endCurrent',
          }, /*{
            title: '起始温度',
            width: 90,
            align: 'center',
            dataIndex: 'beginTemperature',
          }, {
            title: '终止温度',
            width: 90,
            align: 'center',
            dataIndex: 'endTemperature',
          }, */{
            title: '恒流比',
            width: 90,
            align: 'center',
            dataIndex: 'cccapacityRate',
          }, {
            title: '累计充电容量/Ah',
            width: 120,
            align: 'center',
            dataIndex: 'cumulativeChargeCapacity'
          }, {
            title: '累计放电容量/Ah',
            width: 120,
            align: 'center',
            dataIndex: 'cumulativeDischargeCapacity'
          }, {
            title: '累计充电能量/Wh',
            width: 120,
            align: 'center',
            dataIndex: 'cumulativeChargeEnergy'
          }, {
            title: '累计放电能量/Wh',
            width: 120,
            align: 'center',
            dataIndex: 'cumulativeDischargeEnergy'
          }, {
            title: '单体起始温度1',
            width: 110,
            align: 'center',
            dataIndex: 'startTemp1',
          }, {
            title: '单体结束温度1',
            width: 110,
            align: 'center',
            dataIndex: 'endTemp1',
          }, {
            title: '单体起始温度2',
            width: 110,
            align: 'center',
            dataIndex: 'startTemp2'
          },  {
            title: '单体结束温度2',
            width: 110,
            align: 'center',
            dataIndex: 'endTemp2',
          }, {
            title: '单体起始温度3',
            width: 110,
            align: 'center',
            dataIndex: 'startTemp3'
          },  {
            title: '单体结束温度3',
            width: 110,
            align: 'center',
            dataIndex: 'endTemp3',
          }, {
            title: '单体起始温度4',
            width: 110,
            align: 'center',
            dataIndex: 'startTemp4'
          },  {
            title: '单体结束温度4',
            width: 110,
            align: 'center',
            dataIndex: 'endTemp4',
          }, {
            title: '起始SOC',
            width: 90,
            align: 'center',
            dataIndex: 'startSocCalculate'
          }, {
            title: '终止SOC',
            width: 90,
            align: 'center',
            dataIndex: 'endSocCalculate'
          }, {
            title: '起始膨胀力',
            width: 90,
            align: 'center',
            dataIndex: 'startPressure'
          }, {
            title: '结束膨胀力',
            width: 90,
            align: 'center',
            dataIndex: 'endPressure'
          }, {
            title: '最大膨胀力',
            width: 90,
            align: 'center',
            dataIndex: 'maxPressure'
          }, {
            title: '最小膨胀力',
            width: 90,
            align: 'center',
            dataIndex: 'minPressure'
          },{
            title: '单体起始温度',
            width: 110,
            align: 'center',
            dataIndex: 'startTemps',
            ellipsis:true
          },{
            title: '单体结束温度',
            width: 110,
            align: 'center',
            dataIndex: 'endTemps',
            ellipsis:true
          },{
            title: '中值电压',
            width: 80,
            align: 'center',
            dataIndex: 'meanVoltage'
          }
        ],
        columnsDoris: [
          {
          title: '序号',
          align: 'center',
          width: 50,
          fixed:true,
          customRender: (text, record, index) => index + 1
          }, {
            title: '工步序号',
            dataIndex: 'stepNum',
            align: 'center',
            width: 90,
            fixed:true
          }, {
            title: '循环号',
            width: 90,
            align: 'center',
            dataIndex: 'cycleId',
            fixed:true,
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '工步号',
            width: 90,
            align: 'center',
            dataIndex: 'stepId',
            fixed:true,
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '工步名称',
            width: 90,
            align: 'center',
            dataIndex: 'stepName',
            fixed:true,
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '工步时间',
            width: 90,
            align: 'center',
            dataIndex: 'stepTime',
          }, {
            title: '绝对时间',
            width: 180,
            align: 'center',
            dataIndex: 'absoluteTime',
          }, {
            title: '容量/Ah',
            width: 90,
            align: 'center',
            dataIndex: 'capacity',
          }, {
            title: '能量/Wh',
            width: 90,
            align: 'center',
            dataIndex: 'energy',
          }, {
            title: '起始电压/V',
            width: 90,
            align: 'center',
            dataIndex: 'beginVoltage',
          }, {
            title: '终止电压/V',
            width: 90,
            align: 'center',
            dataIndex: 'endVoltage',
          }, {
            title: '起始电流/A',
            width: 90,
            align: 'center',
            dataIndex: 'beginCurrent',
          }, {
            title: '终止电流/A',
            width: 90,
            align: 'center',
            dataIndex: 'endCurrent',
          }, /*{
            title: '起始温度',
            width: 90,
            align: 'center',
            dataIndex: 'beginTemperature',
          }, {
            title: '终止温度',
            width: 90,
            align: 'center',
            dataIndex: 'endTemperature',
          }, */{
            title: '恒流比',
            width: 90,
            align: 'center',
            dataIndex: 'cccapacityRate',
          }, {
            title: '累计充电容量/Ah',
            width: 120,
            align: 'center',
            dataIndex: 'accumulateChargeCapacity'
          }, {
            title: '累计放电容量/Ah',
            width: 120,
            align: 'center',
            dataIndex: 'accumulateDischargeCapacity'
          }, {
            title: '累计充电能量/Wh',
            width: 120,
            align: 'center',
            dataIndex: 'accumulateChargeEnergy'
          }, {
            title: '累计放电能量/Wh',
            width: 120,
            align: 'center',
            dataIndex: 'accumulateDischargeEnergy'
          }, {
            title: '单体起始温度1',
            width: 110,
            align: 'center',
            dataIndex: 'startTemp1',
          }, {
            title: '单体结束温度1',
            width: 110,
            align: 'center',
            dataIndex: 'endTemp1',
          }, {
            title: '单体起始温度2',
            width: 110,
            align: 'center',
            dataIndex: 'startTemp2'
          },  {
            title: '单体结束温度2',
            width: 110,
            align: 'center',
            dataIndex: 'endTemp2',
          }, {
            title: '单体起始温度3',
            width: 110,
            align: 'center',
            dataIndex: 'startTemp3'
          },  {
            title: '单体结束温度3',
            width: 110,
            align: 'center',
            dataIndex: 'endTemp3',
          }, {
            title: '单体起始温度4',
            width: 110,
            align: 'center',
            dataIndex: 'startTemp4'
          },  {
            title: '单体结束温度4',
            width: 110,
            align: 'center',
            dataIndex: 'endTemp4',
          }, {
            title: '起始SOC',
            width: 90,
            align: 'center',
            dataIndex: 'startSocCalculate'
          }, {
            title: '终止SOC',
            width: 90,
            align: 'center',
            dataIndex: 'endSocCalculate'
          }, {
            title: '起始膨胀力',
            width: 90,
            align: 'center',
            dataIndex: 'startPressure'
          }, {
            title: '结束膨胀力',
            width: 90,
            align: 'center',
            dataIndex: 'endPressure'
          }, {
            title: '最大膨胀力',
            width: 90,
            align: 'center',
            dataIndex: 'maxPressure'
          }, {
            title: '最小膨胀力',
            width: 90,
            align: 'center',
            dataIndex: 'minPressure'
          },{
            title: '单体起始温度',
            width: 110,
            align: 'center',
            dataIndex: 'startTempList',
            ellipsis:true
          },{
            title: '单体结束温度',
            width: 110,
            align: 'center',
            dataIndex: 'endTempList',
            ellipsis:true
          },{
            title: '中值电压',
            width: 80,
            align: 'center',
            dataIndex: 'middleVoltage',

          }
        ],
        dataColumns: [
          {
          title: '序号',
          align: 'center',
          width: 50,
            fixed:true,
          customRender: (text, record, index) => index + 1
          }, {
            title: '记录序号',
            dataIndex: 'recordId',
            align: 'center',
            width: 90,
            fixed:true,
          }, {
            title: '工步序号',
            width: 90,
            align: 'center',
            dataIndex: 'stepNum',
            fixed:true,
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '循环号',
            width: 90,
            align: 'center',
            dataIndex: 'cycleId',
            fixed:true,
            //scopedSlots: {customRender: 'updateText'},
          },  {
            title: '工步号',
            width: 90,
            align: 'center',
            dataIndex: 'stepId',
            fixed:true,
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '工步名称',
            width: 90,
            align: 'center',
            dataIndex: 'stepName',
            fixed:true,
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '工步时间',
            width: 90,
            align: 'center',
            dataIndex: 'stepTime',
          },{
            title: '记录时间',
            width: 90,
            align: 'center',
            dataIndex: 'recordTime',
          }, {
            title: '绝对时间',
            width: 180,
            align: 'center',
            dataIndex: 'absoluteTime',
          }, {
            title: '容量/Ah',
            width: 90,
            align: 'center',
            dataIndex: 'capacity',
          }, {
            title: '能量/Wh',
            width: 90,
            align: 'center',
            dataIndex: 'energy',
          }, {
            title: '电压/V',
            width: 90,
            align: 'center',
            dataIndex: 'voltage',
          }, {
            title: '电流/A',
            width: 90,
            align: 'center',
            dataIndex: 'current',
          },{
            title: '功率/W',
            width: 90,
            align: 'center',
            dataIndex: 'activePower',
          },{
            title: '累计充电容量/Ah',
            width: 120,
            align: 'center',
            dataIndex: 'cycChagerCapacity',
          },{
            title: '累计放电容量/Ah',
            width: 120,
            align: 'center',
            dataIndex: 'cycDischagerCapacity',
          },{
            title: '累计充电能量/Wh',
            width: 120,
            align: 'center',
            dataIndex: 'cycChagerEnergy',
          }, {
            title: '累计放电能量/Wh',
            width: 120,
            align: 'center',
            dataIndex: 'cycDischagerEnergy',
          },/*{
            title: '温度',
            width: 90,
            align: 'center',
            dataIndex: 'Temperature',
          },*/ {
            title: '单体温度1',
            width: 90,
            align: 'center',
            dataIndex: 'auxTem1',
          }, {
            title: '单体温度2',
            width: 90,
            align: 'center',
            dataIndex: 'auxTem2'
          }, {
            title: '单体温度3',
            width: 90,
            align: 'center',
            dataIndex: 'auxTem3'
          }, {
            title: '单体温度4',
            width: 90,
            align: 'center',
            dataIndex: 'auxTem4'
          },{
            title: '总能量',
            width: 90,
            align: 'center',
            dataIndex: 'totalEnergy'
          },{
            title: '总能量-ETP',
            width: 90,
            align: 'center',
            dataIndex: 'totalEnergyEtp'
          },{
            title: 'SOC',
            width: 90,
            align: 'center',
            dataIndex: 'socCalculate'
          },{
            title: '温箱温度/℃',
            width: 90,
            align: 'center',
            dataIndex: 'incubatorTemp'
          },{
            title: '温箱湿度/%RH',
            width: 110,
            align: 'center',
            dataIndex: 'incubatorHum'
          },{
            title: '单体压力1(Kg)',
            width: 110,
            align: 'center',
            dataIndex: 'auxPresureList'
          },{
            title: '单体温度',
            width: 110,
            align: 'center',
            dataIndex: 'auxTemList',
            ellipsis:true
          }
        ],
        dataColumnsDoris: [
          {
          title: '序号',
          align: 'center',
          width: 50,
            fixed:true,
          customRender: (text, record, index) => index + 1
          }, {
            title: '记录序号',
            dataIndex: 'recordId',
            align: 'center',
            width: 90,
            fixed:true,
          }, {
            title: '工步序号',
            width: 90,
            align: 'center',
            dataIndex: 'stepNum',
            fixed:true,
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '循环号',
            width: 90,
            align: 'center',
            dataIndex: 'cycleId',
            fixed:true,
            //scopedSlots: {customRender: 'updateText'},
          },  {
            title: '工步号',
            width: 90,
            align: 'center',
            dataIndex: 'stepId',
            fixed:true,
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '工步名称',
            width: 90,
            align: 'center',
            dataIndex: 'stepName',
            fixed:true,
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '工步时间',
            width: 90,
            align: 'center',
            dataIndex: 'stepTime',
          }, {
            title: '记录时间',
            width: 90,
            align: 'center',
            dataIndex: 'recordTime',
          },{
            title: '绝对时间',
            width: 180,
            align: 'center',
            dataIndex: 'absoluteTime',
          }, {
            title: '容量/Ah',
            width: 90,
            align: 'center',
            dataIndex: 'capacity',
          }, {
            title: '能量/Wh',
            width: 90,
            align: 'center',
            dataIndex: 'energy',
          }, {
            title: '电压/V',
            width: 90,
            align: 'center',
            dataIndex: 'voltage',
          }, {
            title: '电流/A',
            width: 90,
            align: 'center',
            dataIndex: 'current1',
          },{
            title: '功率/W',
            width: 90,
            align: 'center',
            dataIndex: 'power',
          },{
            title: '累计充电容量/Ah',
            width: 120,
            align: 'center',
            dataIndex: 'accumulateChargeCapacity',
          },{
            title: '累计放电容量/Ah',
            width: 120,
            align: 'center',
            dataIndex: 'accumulateDischargeCapacity',
          },{
            title: '累计充电能量/Wh',
            width: 120,
            align: 'center',
            dataIndex: 'accumulateChargeEnergy',
          }, {
            title: '累计放电能量/Wh',
            width: 120,
            align: 'center',
            dataIndex: 'accumulateDischargeEnergy',
          },/*{
            title: '温度',
            width: 90,
            align: 'center',
            dataIndex: 'Temperature',
          },*/ {
            title: '单体温度1',
            width: 90,
            align: 'center',
            dataIndex: 'auxTem1',
          }, {
            title: '单体温度2',
            width: 90,
            align: 'center',
            dataIndex: 'auxTem2'
          }, {
            title: '单体温度3',
            width: 90,
            align: 'center',
            dataIndex: 'auxTem3'
          }, {
            title: '单体温度4',
            width: 90,
            align: 'center',
            dataIndex: 'auxTem4'
          },{
            title: '总能量',
            width: 90,
            align: 'center',
            dataIndex: 'totalEnergy'
          },{
            title: '总能量-ETP',
            width: 90,
            align: 'center',
            dataIndex: 'totalEnergyEtp'
          },{
            title: 'SOC',
            width: 90,
            align: 'center',
            dataIndex: 'socCalculate'
          },{
            title: '温箱温度/℃',
            width: 90,
            align: 'center',
            dataIndex: 'incubatorTemp'
          },{
            title: '温箱湿度/%RH',
            width: 110,
            align: 'center',
            dataIndex: 'incubatorHum'
          },{
            title: '单体压力1(Kg)',
            width: 110,
            align: 'center',
            dataIndex: 'pressure'
          },{
            title: '单体温度',
            width: 110,
            align: 'center',
            dataIndex: 'auxTemList',
            ellipsis:true
          }
        ],
        cycColumns: [
          {
          title: '序号',
          align: 'center',
          width: 50,
          customRender: (text, record, index) => index + 1
          },  {
            title: '循环号',
            width: 90,
            align: 'center',
            dataIndex: 'cycleId',
            //scopedSlots: {customRender: 'updateText'},
          }, /* {
            title: '工步号',
            width: 90,
            align: 'center',
            dataIndex: 'stepId',
            //scopedSlots: {customRender: 'updateText'},
          },  */ {
            title: '绝对时间',
            width: 180,
            align: 'center',
            dataIndex: 'absoluteTime',
          }, {
            title: '充电容量/Ah',
            width: 90,
            align: 'center',
            dataIndex: 'chargeCapacity',
          },{
            title: '放电容量/Ah',
            width: 90,
            align: 'center',
            dataIndex: 'disChargeCapacity',
          }, {
            title: '充电能量/Wh',
            width: 90,
            align: 'center',
            dataIndex: 'chargeEnergy',
          }, {
            title: '放电能量/Wh',
            width: 90,
            align: 'center',
            dataIndex: 'disChargeEnergy',
          }, /*{
            title: '内阻/mΩ',
            width: 90,
            align: 'center',
            dataIndex: 'resistance',
          }, */{
            title: '放电时间',
            width: 90,
            align: 'center',
            dataIndex: 'dischargeTimeString',
          }, {
            title: '充电时间',
            width: 90,
            align: 'center',
            dataIndex: 'chargeTimeString',
          }, {
            title: '充放电效率',
            width: 90,
            align: 'center',
            dataIndex: 'chargeCapacityRatio',
          },/* {
            title: '放电中值电压/V',
            width: 100,
            align: 'center',
            dataIndex: 'dischargeMeanVoltage',
          }, {
            title: '衰减比例/%',
            width: 90,
            align: 'center',
            dataIndex: 'attenuationRation',
          },*/
          {
            title: '最低温度',
            width: 90,
            align: 'center',
            dataIndex: 'minTemp',
          }, {
            title: '最高温度',
            width: 90,
            align: 'center',
            dataIndex: 'maxTemp',
          },
        ],
        cycColumnsDoris: [
          {
          title: '序号',
          align: 'center',
          width: 50,
          customRender: (text, record, index) => index + 1
          },  {
            title: '循环号',
            width: 90,
            align: 'center',
            dataIndex: 'cycleId',
            //scopedSlots: {customRender: 'updateText'},
          }, /* {
            title: '工步号',
            width: 90,
            align: 'center',
            dataIndex: 'stepId',
            //scopedSlots: {customRender: 'updateText'},
          },  */ {
            title: '绝对时间',
            width: 180,
            align: 'center',
            dataIndex: 'absoluteTime',
          }, {
            title: '充电容量/Ah',
            width: 90,
            align: 'center',
            dataIndex: 'chargeCapacity',
          },{
            title: '放电容量/Ah',
            width: 90,
            align: 'center',
            dataIndex: 'dischargeCapacity',
          }, {
            title: '充电能量/Wh',
            width: 90,
            align: 'center',
            dataIndex: 'chargeEnergy',
          }, {
            title: '放电能量/Wh',
            width: 90,
            align: 'center',
            dataIndex: 'dischargeEnergy',
          }, /*{
            title: '内阻/mΩ',
            width: 90,
            align: 'center',
            dataIndex: 'resistance',
          }, */{
            title: '放电时间',
            width: 90,
            align: 'center',
            dataIndex: 'dischargeTime',
          }, {
            title: '充电时间',
            width: 90,
            align: 'center',
            dataIndex: 'chargeTime',
          }, {
            title: '充放电效率',
            width: 90,
            align: 'center',
            dataIndex: 'chargeCapacityRatio',
          },/* {
            title: '放电中值电压/V',
            width: 100,
            align: 'center',
            dataIndex: 'dischargeMeanVoltage',
          }, {
            title: '衰减比例/%',
            width: 90,
            align: 'center',
            dataIndex: 'attenuationRation',
          },*/
          {
            title: '最低温度',
            width: 90,
            align: 'center',
            dataIndex: 'minTemp',
          }, {
            title: '最高温度',
            width: 90,
            align: 'center',
            dataIndex: 'maxTemp',
          },
        ],
        stepInfoColumns: [
          {
          title: '序号',
          align: 'center',
          width: 50,
          customRender: (text, record, index) => index + 1
          },  {
            title: '工步号',
            width: 90,
            align: 'center',
            dataIndex: 'stepId',
            //scopedSlots: {customRender: 'updateText'},
          },  {
            title: '工步名称',
            width: 90,
            align: 'center',
            dataIndex: 'stepName',
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '参数',
            width: 200,
            align: 'center',
            dataIndex: 'stepPara',
          }, {
            title: '截止参数',
            width: 200,
            align: 'center',
            dataIndex: 'cutoffCondition',
          }, {
            title: '记录条件',
            width: 200,
            align: 'center',
            dataIndex: 'recordcond',
          },{
            title: '创建时间',
            width: 200,
            align: 'center',
            dataIndex: 'createTime',
          }
        ],
        logColumns: [
          {
          title: '序号',
          align: 'center',
          width: 50,
          customRender: (text, record, index) => index + 1
          },  {
            title: '类型',
            width: 90,
            align: 'center',
            dataIndex: 'recordType',
            //scopedSlots: {customRender: 'updateText'},
          },  {
            title: '描述',
            width: 90,
            align: 'center',
            dataIndex: 'description',
            //scopedSlots: {customRender: 'updateText'},
          },  {
            title: '截止参数',
            width: 150,
            align: 'center',
            dataIndex: 'cutOffConditions',
            //scopedSlots: {customRender: 'updateText'},
          },{
            title: '时间',
            width: 90,
            align: 'center',
            dataIndex: 'absoluteTime',
          }
        ],
        stepInfoColumnsDoris: [
          {
          title: '序号',
          align: 'center',
          width: 50,
          customRender: (text, record, index) => index + 1
          },  {
            title: '工步号',
            width: 90,
            align: 'center',
            dataIndex: 'stepId',
            //scopedSlots: {customRender: 'updateText'},
          },  {
            title: '工步名称',
            width: 90,
            align: 'center',
            dataIndex: 'stepName',
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '参数',
            width: 200,
            align: 'center',
            dataIndex: 'stepPara',
          }, {
            title: '截止参数',
            width: 200,
            align: 'center',
            dataIndex: 'cutoffCondition',
          }, {
            title: '记录条件',
            width: 200,
            align: 'center',
            dataIndex: 'recordCondition',
          },{
            title: '创建时间',
            width: 200,
            align: 'center',
            dataIndex: 'createTime',
          }
        ],
        exportTaskcolumns: [
          {
          title: '序号',
          align: 'center',
          width: 50,
          customRender: (text, record, index) => index + 1
          }, {
            title: '任务名称',
            dataIndex: 'taskName',
            align: 'center',
            width: 200,
            scopedSlots: {customRender: 'taskName'},
          }, {
            title: '任务状态',
            width: 90,
            align: 'center',
            dataIndex: 'fileStatus',
            customRender: (text, record, index) => {
              if(text == 0){
                return "新建"
              }
              if(text == 10){
                return "导出中"
              }
              if(text == 20){
                return "导出完成"
              }
              if(text == 30){
                //导出失败  展示为 新建
                return "新建"
              }
            }
          }, {
            title: '排队位置',
            width: 90,
            align: 'center',
            dataIndex: 'ranking',
            //scopedSlots: {customRender: 'updateText'},
          },
          {
            title: '文件大小',
            width: 90,
            align: 'center',
            dataIndex: 'fileSizeInfo',
            //scopedSlots: {customRender: 'updateText'},
          },  {
            title: '创建时间',
            width: 90,
            align: 'center',
            dataIndex: 'createTime',
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '开始时间',
            width: 90,
            align: 'center',
            dataIndex: 'beginTime',
            //scopedSlots: {customRender: 'updateText'},
          },{
            title: '完成时间',
            width: 90,
            align: 'center',
            dataIndex: 'finishTime',
            //scopedSlots: {customRender: 'updateText'},
          }
        ],
        record:{},
        labelCol: {
          xs: {
            span: 12
          },
          sm: {
            span: 8
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 13
          }
        },
        visible1: false,
        confirmLoading1: false,
        showExport:true,
        firstOpen:0
      }
    },

    methods: {
			logReset(){
				this.queryParamLog = {}
				this.$refs.table5.refresh()
			},
      reset(){
        this.queryParam = {}
        this.$refs.table2.refresh()
      },
      detailReset(){
        this.dataQueryParam = {}
        this.$refs.table1.refresh()
      },
      stepInfoReset(){
        this.queryParam1 = {}
        this.$refs.table4.refresh()
      },
      exportData(){
        shenghongDataExport(this.record).then(res => {
          if (res.success) {
            this.$message.success('导出任务创建成功')

          } else {
            this.$message.warn(res.message)
          }


        })
      },

      syncData(){
        this.syncLoading = true
        syncDorisData(this.record).then(res => {
          if (res.success) {
            this.$message.success('同步成功')

          } else {
            this.$message.warn(res.message)
          }

          this.syncLoading = false
        })
      },
      getData(){
        if(this.dataType == 'step'){
          this.$refs.table2.refresh()
        }else if(this.dataType == 'data'){
          this.$refs.table1.refresh()
        }else if(this.dataType == 'cyc'){
          this.$refs.table3.refresh()
        }else if(this.dataType == 'log'){
          this.$refs.table5.refresh()
        }else{
          this.$refs.table4.refresh()
        }

      },
      async query(record,haveExport) {
        this.showExport = haveExport
        this.record = record
        this.queryparam = record.queryparam

        this.visible1 = true
        if(this.firstOpen == 0){
          this.firstOpen++
          return
        }
        this.$nextTick(() => {
          if(this.dataType == 'step'){
            this.$refs.table2.refresh()
          }else if(this.dataType == 'data'){
            this.$refs.table1.refresh()
          }else if(this.dataType == 'cyc'){
            this.$refs.table3.refresh()
          }else if(this.dataType == 'stepInfo'){
            this.$refs.table4.refresh()
          }else{
            this.$refs.table5.refresh()
          }

        })

      },

      getList(){
        this.getData()
      },

      handleCancel() {
        this.visible1 = false
      }
    }
  }
</script>
<style lang="less" scoped>
@import '/src/components/pageTool/style/pbiSearchItem.less';
  .ant-form-item {

    margin-bottom: 0px;

  }

  .man_button {
    padding-left: 11px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  /deep/ .ant-modal-body {
    padding: 0 !important;
  }

  /deep/ .ant-table-thead > tr > th, /deep/ .ant-table-tbody > tr > td {
    padding: 3px;
  }

  /deep/ .ant-table-footer {

    padding: 0px;
  }

  /deep/ .ant-table-pagination.ant-pagination {
    margin: 5px 0;
  }

  /deep/ .ant-input-number {
    width: 100%;
    height: 28px!important;
  }

  /deep/ .ant-input-number-sm > .ant-input-number-input-wrap > .ant-input-number-input {
    text-align: center;
  }
  /deep/.ant-input-number-input {
    height: 28px!important;
  }

  /deep/ .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
    color: #fff;
    background: #1890FF;
  }

  /deep/ input {
    width: 100%;
  }

  /deep/ input:focus {
    outline: 0;
  }


  /deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
    color: #fff;
    background: #1890FF;
  }

  /deep/.ant-table-tbody > tr > td {
    padding: 2px!important;
  }

/deep/.searchItem .label {
  width: 80px;
}


/* /deep/ #stepInfo .ant-table-header colgroup col:last-child {
  width: 204.3px !important; /* 强制覆盖其他样式 */
  /* min-width: 204.3px !important; */
/* } */ 

</style>
