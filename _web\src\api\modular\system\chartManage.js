import { axios } from '@/utils/request'

export const getChartProductStatus = (params) => {
  return axios({
    url: '/chart/product_status',
    method: 'get',
    params: params
  })
}

export const getChartProductClassification = (params) => {
  return axios({
    url: '/chart/product_classification',
    method: 'get',
    params: params
  })
}


/* export const getChartProjectLevel = (params) => {
  return axios({
    url: '/chart/project_level',
    method: 'get',
    params: params
  })
} */


export const getChartProjectLevels = (params) => {
  return axios({
    url: '/chart/project_levels',
    method: 'get',
    params: params
  })
}


/* export const getProjectsCountByDept = (params) => {
  return axios({
    url: '/chart/project_count_by_dept',
    method: 'get',
    params: params
  })
} */




/* export const getProjectsStatus = (params) => {
  return axios({
    url: '/chart/project_status',
    method: 'get',
    params: params
  })
} */


export const getProjectProcess = (params) => {
  return axios({
    url: '/chart/project_process',
    method: 'get',
    params: params
  })
}


export const getProjectProcessDetail = (params) => {
  return axios({
    url: '/chart/project_process_detail',
    method: 'get',
    params: params
  })
}

export const getProjectAlters = (params) => {
  return axios({
    url: '/chart/project_alters',
    method: 'get',
    params: params
  })
}

export const getProjectAltersBydept = (params) => {
  return axios({
    url: '/chart/project_alter_bydept',
    method: 'get',
    params: params
  })
}

export const getDashStage = () => {
  return axios({
    url: '/report/dash_stage_count_by_lamp',
    method: 'get'
  })
}


export const getProjectStageDetail = (params) => {
  return axios({
    url: '/chart/project_stage_detail',
    method: 'get',
    params: params
  })
}

export const getProjectStageCount = (params) => {
  return axios({
    url: '/chart/project_stage_count',
    method: 'get',
    params: params
  })
}

