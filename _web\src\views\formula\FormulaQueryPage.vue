<template>
  <div class="formula-query-page">
    <div class="search-section">
      <a-form layout="inline">
        <div class="search-container">
          <!-- 公式ID（左端） -->
          <div class="search-item">
            <a-form-item label="公式ID" class="form-item-with-label">
              <a-input v-model="searchParams.id" placeholder="输入公式ID搜索" allow-clear />
            </a-form-item>
          </div>

          <!-- 公式描述（中间） -->
          <div class="search-item">
            <a-form-item label="公式描述" class="form-item-with-label">
              <a-input v-model="searchParams.description" placeholder="输入关键词搜索" allow-clear />
            </a-form-item>
          </div>

          <!-- 搜索和重置按钮（右端） -->
          <div class="search-buttons">
            <a-button type="primary" @click="searchFormulas" class="search-button">
              <a-icon type="search" />搜索
            </a-button>
            <a-button @click="resetSearch" class="reset-button">
              <a-icon type="reload" />重置
            </a-button>
          </div>
        </div>
      </a-form>
    </div>

    <div class="formula-list-section">
      <a-spin :spinning="loading">
        <a-table
          :columns="columns"
          :data-source="formulas"
          :pagination="{ pageSize: 10 }"
          row-key="id"
          :scroll="{ x: false }"
          :table-layout="'fixed'"
        >
          <template slot="latex" slot-scope="text, record">
            <div class="formula-preview-card formula-container formula-table-cell">
              <!-- 加载指示器 - 无文字 -->
              <div class="formula-loading"></div>

              <!-- 主公式渲染 -->
              <div class="formula-section-title">主公式</div>
              <div class="formula-preview-content main-formula">
                <div class="formula-cell-wrapper">
                  <div class="formula-cell-inner" v-html="renderLatexFormulaHtml(getMainFormula(text))"></div>
                </div>
              </div>

              <!-- 子公式渲染（如果有） -->
              <template v-if="getSubFormulas(text).length > 0">
                <div class="formula-section-title">子公式</div>
                <!-- 每个子公式单独一行并带有滚动条，居中对齐 -->
                <template v-for="(subFormula, index) in getSubFormulas(text)">
                  <div :key="index" class="formula-preview-content sub-formula centered-formula">
                    <div class="formula-cell-wrapper">
                      <div class="formula-cell-inner" v-html="renderLatexFormulaHtml(subFormula)"></div>
                    </div>
                  </div>
                </template>
              </template>
            </div>
          </template>
          <template slot="action" slot-scope="text, record">
            <a-button type="link" @click="viewFormulaDetail(record)" icon="eye" class="view-button">查看</a-button>
            <a-button type="link" @click="editFormula(record)" icon="edit" class="edit-button">编辑</a-button>
            <a-popconfirm
              title="确定要删除这个公式吗？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="deleteFormula(record.id)"
            >
              <a-button type="link" class="delete-button" icon="delete">删除</a-button>
            </a-popconfirm>
          </template>
        </a-table>
      </a-spin>
    </div>

    <!-- 公式详情对话框 -->
    <a-modal
      :title="`公式详情 - ${selectedFormula.id || ''}`"
      :visible="formulaDetailVisible"
      @cancel="closeFormulaDetail"
      :footer="null"
      width="1000px"
    >
      <div v-if="selectedFormula.id" class="formula-detail">
        <a-descriptions bordered>
          <a-descriptions-item label="公式ID" :span="3">
            {{ selectedFormula.id }}
          </a-descriptions-item>
          <a-descriptions-item label="公式描述" :span="3">
            {{ selectedFormula.description }}
          </a-descriptions-item>
        </a-descriptions>

        <a-card class="formula-card" :bordered="true" title="公式表达式">
          <div class="formula-preview-card detail-formula formula-container">
            <!-- 加载指示器 - 无文字 -->
            <div class="formula-loading"></div>

            <!-- 主公式渲染 -->
            <div class="formula-section-title">主公式</div>
            <div class="formula-preview-content main-formula">
              <div class="formula-cell-wrapper">
                <div class="formula-cell-inner" v-html="renderLatexFormulaHtml(getMainFormula(selectedFormula.latex))"></div>
              </div>
            </div>

            <!-- 子公式渲染（如果有） -->
            <template v-if="getSubFormulas(selectedFormula.latex).length > 0">
              <div class="formula-section-title">子公式</div>
              <!-- 每个子公式单独一行并带有滚动条，居中对齐 -->
              <template v-for="(subFormula, index) in getSubFormulas(selectedFormula.latex)">
                <div :key="index" class="formula-preview-content sub-formula centered-formula">
                  <div class="formula-cell-wrapper">
                    <div class="formula-cell-inner" v-html="renderLatexFormulaHtml(subFormula)"></div>
                  </div>
                </div>
              </template>
            </template>
          </div>
        </a-card>

        <div class="params-editor">
          <a-divider orientation="center">参数信息</a-divider>

          <!-- 变量和系数并排显示 -->
          <a-row :gutter="16">
            <!-- 变量部分 - 左侧 -->
            <a-col :span="10" v-if="getVariables(selectedFormula.params).length > 0">
              <a-card class="param-card" title="变量" :bordered="true">
                <a-table
                  :columns="variableColumns"
                  :data-source="getVariables(selectedFormula.params)"
                  :pagination="false"
                  row-key="name"
                  :scroll="{ x: false }"
                  :table-layout="'fixed'"
                >
                  <template slot="name" slot-scope="text, record">
                    <a-tooltip :title="record.describe || '暂无说明'" placement="top">
                      <div class="latex-cell formula-container">
                        <!-- 加载指示器 - 无文字 -->
                        <div class="formula-loading"></div>
                        <div class="formula-cell-inner" v-html="renderLatexFormulaHtml(text)"></div>
                      </div>
                    </a-tooltip>
                  </template>
                  <template slot="type" slot-scope="text">
                    <a-tag color="green">变量</a-tag>
                  </template>
                </a-table>
              </a-card>
            </a-col>

            <!-- 系数部分 - 右侧 -->
            <a-col :span="14" v-if="getCoefficients(selectedFormula.params).length > 0">
              <a-card class="param-card" title="系数" :bordered="true">
                <a-tabs :defaultActiveKey="firstGroupKey()" class="unified-tabs coefficient-tabs" @change="handleTabChange">
                  <a-tab-pane
                    v-for="group in groupCoefficients(selectedFormula.params)"
                    :key="`${group.letter}-group`"
                    :tab="`${group.letter}组`"
                    :force-render="true"
                  >
                    <a-table
                      :columns="paramColumns"
                      :data-source="group.coefficients"
                      :pagination="false"
                      row-key="name"
                      :scroll="{ x: false }"
                      :table-layout="'fixed'"
                    >
                      <template slot="name" slot-scope="text">
                        <div class="latex-cell formula-container">
                          <!-- 加载指示器 - 无文字 -->
                          <div class="formula-loading"></div>
                          <div class="formula-cell-inner" v-html="renderLatexFormulaHtml(text)"></div>
                        </div>
                      </template>
                      <template slot="type" slot-scope="text">
                        <a-tag color="blue">系数</a-tag>
                      </template>
                    </a-table>
                  </a-tab-pane>
                </a-tabs>
              </a-card>
            </a-col>
          </a-row>
        </div>

        <div class="detail-actions">
          <a-button @click="closeFormulaDetail">关闭</a-button>
        </div>
      </div>
    </a-modal>

    <!-- 公式编辑对话框 -->
    <a-modal
      :title="`编辑公式 - ${editingFormula.id || ''}`"
      :visible="formulaEditVisible"
      @cancel="closeFormulaEdit"
      :confirmLoading="editLoading"
      @ok="saveFormulaEdit"
      width="1000px"
      :maskClosable="false"
    >
      <div v-if="editingFormula.id" class="formula-edit-content">
        <a-form :form="form" layout="vertical">
          <!-- 公式基本信息 -->
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="公式ID">
                <a-input v-model="editingFormula.id" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="16">
              <a-form-item label="公式描述">
                <a-input v-model="editingFormula.description" placeholder="请输入公式描述" />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 公式编辑器 -->
          <a-form-item label="公式表达式">
            <formula-editor
              :initial-formula="editingFormula.latex"
              :initial-params="extractIndependentParams()"
              @update:formula="editingFormula.latex = $event"
              @update:params="handleParamsUpdate"
              @formula-parsed="handleFormulaParsed"
              :hide-parse-button="true"
            />
          </a-form-item>

          <!-- 参数编辑区域 -->
          <div v-if="parsedParams.length > 0" class="params-editor">
            <a-divider orientation="center">参数信息</a-divider>

            <!-- 变量和系数并排显示 -->
            <a-row :gutter="16">
              <!-- 变量部分 - 左侧 -->
              <a-col :span="10" v-if="getVariables(parsedParams).length > 0">
                <a-card class="param-card" title="变量" :bordered="true">
                  <a-table
                    :columns="editVariableColumns"
                    :data-source="getVariables(parsedParams)"
                    :pagination="false"
                    row-key="name"
                    :scroll="{ x: false }"
                    :table-layout="'fixed'"
                  >
                    <template slot="name" slot-scope="text, record">
                      <a-tooltip :title="record.describe || '暂无说明'" placement="top">
                        <div class="latex-cell formula-container">
                          <!-- 加载指示器 - 无文字 -->
                          <div class="formula-loading"></div>
                          <div class="formula-cell-inner" v-html="renderLatexFormulaHtml(text)"></div>
                        </div>
                      </a-tooltip>
                    </template>
                    <template slot="type" slot-scope="text">
                      <a-tag color="green">变量</a-tag>
                    </template>
                    <template slot="description" slot-scope="text, record">
                      <a-input
                        v-model="record.describe"
                        placeholder="参数描述"
                      />
                    </template>
                  </a-table>
                </a-card>
              </a-col>

              <!-- 系数部分 - 右侧 -->
              <a-col :span="14" v-if="getCoefficients(parsedParams).length > 0">
                <a-card class="param-card" title="系数" :bordered="true">
                  <a-tabs :defaultActiveKey="firstGroupKey()" class="unified-tabs coefficient-tabs" @change="handleTabChange">
                    <a-tab-pane
                      v-for="group in groupCoefficients(parsedParams)"
                      :key="`${group.letter}-group`"
                      :tab="`${group.letter}组`"
                      :force-render="true"
                    >
                      <a-table
                        :columns="editParamColumns"
                        :data-source="group.coefficients"
                        :pagination="false"
                        row-key="name"
                        :scroll="{ x: false }"
                        :table-layout="'fixed'"
                      >
                        <template slot="name" slot-scope="text">
                          <div class="latex-cell formula-container">
                            <!-- 加载指示器 - 无文字 -->
                            <div class="formula-loading"></div>
                            <div class="formula-cell-inner" v-html="renderLatexFormulaHtml(text)"></div>
                          </div>
                        </template>
                        <template slot="type" slot-scope="text">
                          <a-tag color="blue">系数</a-tag>
                        </template>
                        <template slot="valueInput" slot-scope="text, record">
                          <a-input-number
                            v-model="record.value"
                            style="width: 100%"
                            :formatter="formatNumberDisplay"
                            :parser="parseNumberInput"
                          />
                        </template>
                        <template slot="description" slot-scope="text, record">
                          <a-input
                            v-model="record.describe"
                            placeholder="参数描述"
                          />
                        </template>
                      </a-table>
                    </a-tab-pane>
                  </a-tabs>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import FormulaEditor from '@/components/formula/FormulaEditor.vue';
import { cloneDeep } from 'lodash';
import { saveFormula } from '@/api/modular/formula/formula';
import { api } from '@/api/modular/formula';
import { renderMathJax } from '@/utils/mathUtils';

export default {
  name: 'FormulaQueryPage',
  components: {
    FormulaEditor
  },
  data() {
    return {
      form: this.$form.createForm(this),
      searchParams: {
        id: '',
        description: ''
      },
      loading: false,
      editLoading: false,
      formulaDetailVisible: false,
      formulaEditVisible: false,
      selectedFormula: {},
      editingFormula: {
        id: '',
        description: '',
        latex: '',
        params: []
      },
      originalFormula: null,
      parsedParams: [],
      parsedLatex: '',
      independentParams: [],
      columns: [
        { title: '公式ID', dataIndex: 'id', key: 'id', width: 100, className: 'column-id' },
        { title: '公式描述', dataIndex: 'description', key: 'description', width: 150, className: 'column-description' },
        { title: '公式表达式', dataIndex: 'latex', key: 'latex', scopedSlots: { customRender: 'latex' }, width: 600, className: 'column-latex' },
        { title: '操作', key: 'action', scopedSlots: { customRender: 'action' }, width: 70, className: 'column-action' }
      ],
      // 查看模式 - 变量表格列定义（不包含参数值列）
      variableColumns: [
        { title: '名称', dataIndex: 'name', key: 'name', scopedSlots: { customRender: 'name' }, width: 60, className: 'column-param-name' },
        { title: '类型', dataIndex: 'type', key: 'type', scopedSlots: { customRender: 'type' }, width: 60, className: 'column-param-type' },
        { title: '描述', dataIndex: 'describe', key: 'describe', width: 180, className: 'column-param-describe' }
      ],
      // 查看模式 - 系数表格列定义
      paramColumns: [
        { title: '名称', dataIndex: 'name', key: 'name', scopedSlots: { customRender: 'name' }, width: 60, className: 'column-param-name' },
        { title: '类型', dataIndex: 'type', key: 'type', scopedSlots: { customRender: 'type' }, width: 60, className: 'column-param-type' },
        { title: '值', dataIndex: 'value', key: 'value', width: 200, className: 'column-param-value' },
        { title: '描述', dataIndex: 'describe', key: 'describe', width: 120, className: 'column-param-describe' }
      ],
      // 编辑模式 - 变量表格列定义（不包含参数值列）
      editVariableColumns: [
        { title: '名称', dataIndex: 'name', key: 'name', scopedSlots: { customRender: 'name' }, width: 60, className: 'column-param-name' },
        { title: '类型', dataIndex: 'type', key: 'type', scopedSlots: { customRender: 'type' }, width: 60, className: 'column-param-type' },
        { title: '描述', key: 'describe', scopedSlots: { customRender: 'description' }, width: 180, className: 'column-param-describe' }
      ],
      // 编辑模式 - 系数表格列定义
      editParamColumns: [
        { title: '名称', dataIndex: 'name', key: 'name', scopedSlots: { customRender: 'name' }, width: 60, className: 'column-param-name' },
        { title: '类型', dataIndex: 'type', key: 'type', scopedSlots: { customRender: 'type' }, width: 60, className: 'column-param-type' },
        { title: '值', key: 'value', scopedSlots: { customRender: 'valueInput' }, width: 200, className: 'column-param-value' },
        { title: '描述', key: 'describe', scopedSlots: { customRender: 'description' }, width: 120, className: 'column-param-describe' }
      ]
    };
  },
  computed: {
    ...mapGetters(['getFormulaList']),

    formulas() {
      if (!this.getFormulaList) return [];

      // 根据搜索条件过滤
      return this.getFormulaList.filter(formula => {
        const idMatch = !this.searchParams.id ||
                         formula.id.toLowerCase().includes(this.searchParams.id.toLowerCase());
        const descMatch = !this.searchParams.description ||
                         (formula.description &&
                          formula.description.toLowerCase().includes(this.searchParams.description.toLowerCase()));
        return idMatch && descMatch;
      });
    }
  },
  methods: {
    ...mapActions(['fetchFormulaList', 'updateFormula', 'deleteFormula']),

    // 获取主公式
    getMainFormula(latex) {
      if (!latex) return '';

      try {
        // 尝试解析JSON格式
        const parsed = typeof latex === 'string' ? JSON.parse(latex) : latex;
        return parsed.main_formula || latex;
      } catch (e) {
        // 如果不是JSON格式，直接返回原字符串
        return latex;
      }
    },

    // 获取子公式数组
    getSubFormulas(latex) {
      if (!latex) return [];

      try {
        // 尝试解析JSON格式
        const parsed = typeof latex === 'string' ? JSON.parse(latex) : latex;
        return parsed.sub_formulas || [];
      } catch (e) {
        // 如果不是JSON格式，返回空数组
        return [];
      }
    },

    // 渲染LaTeX公式为HTML
    renderLatexFormulaHtml(formula) {
      if (!formula) return '';

      // 确保公式有正确的LaTeX定界符
      let tex = String(formula).trim();

      // 如果没有定界符，则添加 $...$
      if (!tex.startsWith('$') && !tex.endsWith('$')) {
        tex = `$${tex}$`;
      }

      return tex;
    },

    // 获取变量参数
    getVariables(params) {
      if (!params || !Array.isArray(params)) return [];
      return params.filter(param => param.type === 'variable');
    },

    // 获取系数参数
    getCoefficients(params) {
      if (!params || !Array.isArray(params)) return [];
      return params.filter(param => param.type === 'coefficient');
    },

    // 按字母分组系数
    groupCoefficients(params) {
      const coefficients = this.getCoefficients(params);
      const groups = {};

      coefficients.forEach(coef => {
        // 提取字母部分（第一个字符）
        const letter = coef.name.charAt(0).toUpperCase();

        if (!groups[letter]) {
          groups[letter] = {
            letter,
            coefficients: []
          };
        }

        groups[letter].coefficients.push(coef);
      });

      // 转换为数组并按字母排序
      return Object.values(groups).sort((a, b) => a.letter.localeCompare(b.letter));
    },

    // 获取首组参数的key
    firstGroupKey() {
      const groups = this.groupCoefficients(this.selectedFormula.params || this.parsedParams || []);
      if (groups && groups.length > 0) {
        return `${groups[0].letter}-group`;
      }
      return '0';
    },

    // 处理标签页切换
    handleTabChange(activeKey) {
      // 标签页切换后，等待DOM更新完成再渲染
      this.$nextTick(() => {
        // 获取当前激活的标签页的索引
        const groups = this.groupCoefficients(this.selectedFormula.params || this.parsedParams || []);
        const activeIndex = groups.findIndex(
          group => `${group.letter}-group` === activeKey
        );

        if (activeIndex !== -1) {
          // 使用更可靠的选择器：通过类名和索引选择当前激活的标签页
          const selector = `.ant-tabs-content .ant-tabs-tabpane:nth-child(${activeIndex + 1})`;
          // 强制立即渲染当前标签页内的LaTeX内容
          this.refreshMathJax(true, selector);
        } else {
          // 如果找不到匹配的标签页，渲染整个标签页容器
          this.refreshMathJax(true, '.coefficient-tabs');
        }
      });
    },

    // 在组件更新后刷新MathJax渲染
    refreshMathJax(immediate = false, selector = null) {
      this.$nextTick(() => {
        // 使用immediate参数控制是否立即渲染，使用selector参数限制渲染范围
        renderMathJax(immediate, selector);
      });
    },

    // 格式化数值显示，避免科学计数法
    formatNumberDisplay(value) {
      if (value === null || value === undefined || value === '') return '';

      const numValue = typeof value === 'string' ? parseFloat(value) : value;
      if (isNaN(numValue)) return '';

      // 使用toFixed避免科学计数法，然后移除末尾的0
      return numValue.toFixed(20).replace(/\.?0+$/, '');
    },

    // 解析用户输入的数值
    parseNumberInput(value) {
      if (!value) return null;
      const numValue = parseFloat(value);
      return isNaN(numValue) ? null : numValue;
    },

    searchFormulas() {
      this.loading = true;
      this.fetchFormulaList().finally(() => {
        this.loading = false;
        // 使用immediate=true确保立即渲染一次
        this.refreshMathJax(true);
      });
    },

    resetSearch() {
      this.searchParams = {
        id: '',
        description: ''
      };
      this.searchFormulas();
    },

    viewFormulaDetail(formula) {
      this.selectedFormula = { ...formula };
      this.formulaDetailVisible = true;
      this.$nextTick(() => {
        // 使用immediate=true确保立即渲染一次
        this.refreshMathJax(true);
        // 再次延迟渲染，确保标签页内容已加载
        setTimeout(() => {
          this.refreshMathJax(true);
        }, 100);
      });
    },

    closeFormulaDetail() {
      this.formulaDetailVisible = false;
      this.selectedFormula = {};
    },

    editFormula(formula) {
      this.selectedFormula = cloneDeep(formula);
      this.editingFormula = cloneDeep(formula);
      this.originalFormula = cloneDeep(formula);
      this.formulaEditVisible = true;

      // 确保参数已经加载
      if (formula.params && formula.params.length > 0) {
        this.parsedParams = cloneDeep(formula.params);
      }

      this.$nextTick(() => {
        // 使用immediate=true确保立即渲染一次
        this.refreshMathJax(true);
        // 再次延迟渲染，确保标签页内容已加载
        setTimeout(() => {
          this.refreshMathJax(true);
        }, 100);
      });
    },

    closeFormulaEdit() {
      this.formulaEditVisible = false;
      this.editingFormula = {
        id: '',
        description: '',
        latex: '',
        params: []
      };
      this.parsedParams = [];
    },

    extractIndependentParams() {
      if (!this.editingFormula.params) return [];
      return this.editingFormula.params
        .filter(param => param.type === 'variable')
        .map(param => param.name);
    },

    handleParamsUpdate(params) {
      // 更新独立变量列表
      this.independentParams = params;
    },

    handleFormulaParsed(result) {
      this.parsedLatex = result.parsedLatex;
      this.parsedParams = result.parsedParams;

      // 如果已有公式参数值，保留现有值
      if (this.editingFormula.params && this.editingFormula.params.length > 0) {
        this.parsedParams.forEach(param => {
          const existingParam = this.editingFormula.params.find(p => p.name === param.name);
          if (existingParam) {
            param.value = existingParam.value;
            param.describe = existingParam.describe || '';
          }
        });
      }
    },

    // 自动解析公式
    async parseFormulaAutomatically(latex) {
      try {
        // 提取独立变量
        const independentParams = this.extractIndependentParams();

        // 解析latex格式
        let latexStr = latex;
        if (typeof latex === 'string' && latex.includes('main_formula')) {
          try {
            const parsed = JSON.parse(latex);
            latexStr = parsed;
          } catch (e) {
            latexStr = latex;
          }
        }

        const response = await api.formula.parseLatexFormula(latexStr, independentParams);

        if (response.data && response.data.success) {
          this.parsedParams = response.data.params || [];

          // 如果已有公式参数值，保留现有值
          if (this.editingFormula.params && this.editingFormula.params.length > 0) {
            this.parsedParams.forEach(param => {
              const existingParam = this.editingFormula.params.find(p => p.name === param.name);
              if (existingParam) {
                param.value = existingParam.value;
                param.describe = existingParam.describe || '';
              }
            });
          }

          // 显示编辑对话框
          this.formulaEditVisible = true;
        } else {
          this.$message.error('解析公式失败：' + (response.data?.message || '未知错误'));
        }
      } catch (error) {
        console.error('解析公式异常:', error);
        this.$message.error('解析公式失败: ' + (error.message || '未知错误'));
      }
    },

    async saveFormulaEdit() {
      if (!this.editingFormula.id || !this.editingFormula.latex) {
        this.$message.error('公式ID和表达式不能为空');
        return;
      }

      this.editLoading = true;

      const formulaData = {
        id: this.editingFormula.id,
        description: this.editingFormula.description,
        latex: this.editingFormula.latex,
        params: this.parsedParams
      };

      try {
        const res = await saveFormula(formulaData);
        if (res.data && res.data.success) {
          this.$message.success('公式已更新');
          this.updateFormula(formulaData);
          this.closeFormulaEdit();
          this.searchFormulas(); // 刷新列表
        } else {
          this.$message.error(res.data?.message || '保存失败');
        }
      } catch (error) {
        console.error('保存公式异常:', error);
        this.$message.error('保存失败: ' + (error.message || '未知错误'));
      } finally {
        this.editLoading = false;
      }
    },

    // 删除公式
    async deleteFormula(formulaId) {
      if (!formulaId) {
        this.$message.error('公式ID不能为空');
        return;
      }

      this.loading = true;

      try {
        // 调用Vuex action删除公式
        const result = await this.$store.dispatch('deleteFormula', formulaId);

        if (result.success) {
          this.$message.success(result.message || '公式已成功删除');
          // 刷新公式列表
          this.searchFormulas();
        } else {
          this.$message.error(result.message || '删除公式失败');
        }
      } catch (error) {
        console.error('删除公式异常:', error);
        this.$message.error('删除公式失败: ' + (error.message || '未知错误'));
      } finally {
        this.loading = false;
      }
    }
  },
  mounted() {
    this.searchFormulas();
  },
  // 移除updated钩子，避免每次组件更新都触发渲染
  // 我们已经在需要渲染的地方显式调用refreshMathJax
};
</script>

<style scoped>
.formula-query-page {
  height: 100%;
  overflow: auto;
}

.search-section {
  margin-bottom: 24px;
  padding: 24px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #f0f0f0;
}

/* 搜索表单样式 */
.search-section :deep(.ant-form) {
  width: 100%;
}

/* 搜索区域容器 */
.search-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  width: 100%;
}

/* 搜索区域各部分样式 */
.search-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  flex: 1;
  min-width: 200px;
}

.search-buttons {
  display: flex;
  align-items: center;
  margin-left: auto; /* 将按钮推到最右侧 */
}

.search-button {
  margin-right: 16px;
}

.reset-button {
  margin-left: 0;
}

/* 表单项带标签样式 */
.form-item-with-label {
  display: flex;
  align-items: center;
  width: 100%;
}

.form-item-with-label :deep(.ant-form-item-label) {
  flex: none;
  width: auto;
  min-width: 70px;
  text-align: right;
  padding-right: 8px;
}

.form-item-with-label :deep(.ant-form-item-control-wrapper) {
  flex: 1;
}

/* 表单项样式优化 */
.search-section :deep(.ant-form-item) {
  margin-bottom: 0;
  width: 100%;
  display: flex;
}

.search-section :deep(.ant-form-item-label) {
  line-height: 32px;
  font-weight: 500;
  white-space: nowrap;
}

.search-section :deep(.ant-form-item-control-wrapper) {
  flex: 1;
}

.search-section :deep(.ant-input) {
  height: 32px;
  border-radius: 4px;
}

/* 调整inline表单的间距 */
.search-section :deep(.ant-form-inline .ant-form-item) {
  margin-right: 0;
}



.formula-list-section {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #f0f0f0;
}

.formula-preview-card {
  background-color: #fff;
  padding: 8px;
  margin-bottom: 0;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  max-height: 300px; /* 限制最大高度 */
  overflow-y: auto; /* 添加垂直滚动条 */
  display: flex;
  flex-direction: column;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
  width: 100%; /* 固定宽度为100% */
  box-sizing: border-box;
}

.formula-preview-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.formula-preview-content {
  min-height: 36px;
  display: flex;
  flex-direction: column;
  padding: 12px 16px;
  background-color: #fff;
  border-radius: 6px;
  width: 100%;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  overflow-x: hidden; /* 隐藏外层水平滚动条 */
}

.formula-preview-content.main-formula {
  border: 1px solid #e8e8e8;
  background-color: #fafafa;
}

.formula-preview-content.sub-formula {
  border: 1px solid #e8e8e8;
  background-color: #fafafa;
}

/* 居中对齐的公式样式 */
.centered-formula {
  text-align: center !important;
}

.centered-formula .formula-cell-wrapper {
  display: flex;
  justify-content: center;
  border: none;
  background: transparent;
}

.centered-formula .formula-cell-inner {
  display: inline-block;
  text-align: center !important;
  min-width: auto;
}

.centered-formula :deep(.MathJax),
.centered-formula :deep(.MathJax_Display) {
  text-align: center !important;
  margin: 0 auto !important;
  display: inline-block !important;
}

.sub-formula-item {
  margin-bottom: 10px;
  padding-left: 12px;
  border-left: 2px solid #1890ff;
}

.sub-formula-item:last-child {
  margin-bottom: 0;
}

.formula-detail {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
}

.formula-card {
  margin: 20px 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
}

.detail-formula {
  border: none;
  padding: 0;
  max-width: 100%;
  overflow-x: auto;
  min-height: 200px;
  max-height: 400px;
}

.formula-edit-content {
  padding: 16px;
  max-height: 70vh;
  overflow-y: auto;
  background-color: #fff;
  border-radius: 8px;
}

.formula-edit-preview {
  max-width: 100%;
  overflow-x: auto;
  white-space: nowrap;
  margin-bottom: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
}

.params-editor {
  margin-top: 24px;
  margin-bottom: 24px;
  background-color: #fafafa;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

/* 参数信息分隔线样式 */
.params-editor :deep(.ant-divider-inner-text) {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
}

.params-editor :deep(.ant-divider-horizontal) {
  margin: 16px 0 24px;
}

/* 参数卡片样式 */
.param-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.param-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

/* 参数卡片统一样式 */
.param-card :deep(.ant-card-head) {
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

/* 卡片内表格样式 */
.param-card :deep(.ant-table-wrapper) {
  margin-top: 8px;
}

.param-card :deep(.ant-table) {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

/* 卡片内表格表头统一样式 */
.param-card :deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
  color: rgba(0, 0, 0, 0.85);
}

/* 标签页样式优化 */
.param-card :deep(.ant-tabs-bar) {
  margin-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-actions {
  margin-top: 24px;
  text-align: center;
}

/* 表格样式优化 */
:deep(.ant-table-thead > tr > th) {
  background-color: #f5f7fa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f0f7ff;
}

/* 公式单元格包装器样式 */
.formula-cell-wrapper {
  width: 100%;
  overflow-x: auto;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  background-color: #fff;
  padding: 0;
  margin: 0;
  position: relative;
}

/* 公式单元格内部样式 */
.formula-cell-inner {
  white-space: nowrap;
  padding: 8px;
  display: inline-block;
  min-width: max-content;
  text-align: left;
  overflow-x: visible;
  position: relative;
  left: 0;
}

/* 确保公式内容从左边开始显示 */
:deep(.formula-cell-inner .MathJax) {
  margin-left: 0 !important;
  text-align: left !important;
  display: inline-block !important;
}

/* 公式表格单元格样式 */
.formula-table-cell {
  width: 100%;
  overflow-x: hidden; /* 隐藏外层滚动条 */
  white-space: normal;
  padding: 0;
  text-align: left;
}

/* 可滚动公式容器 */
.scrollable-formula {
  max-width: 100%;
  overflow-x: hidden; /* 隐藏外层滚动条 */
  position: relative;
  border: 1px solid var(--border-color-split, #f0f0f0);
  border-radius: var(--border-radius-base, 8px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  padding: 8px;
  margin: 0;
  box-sizing: border-box;
  text-align: left;
}

/* 公式预览内容样式 */
.formula-preview-content {
  justify-content: flex-start !important;
  overflow: visible; /* 确保内容可见 */
  padding: 4px;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 8px;
  text-align: left;
}

/* 确保公式表格单元格内容可以滚动 */
:deep(.ant-table-cell) {
  overflow: visible;
  word-break: normal;
  white-space: normal;
  padding: 8px;
}

/* 特别处理公式表达式列的单元格 */
:deep(.ant-table-tbody > tr > td.column-latex) {
  padding: 0;
  overflow: visible;
}

/* 确保表格在展开/折叠状态下都能正确渲染 */
:deep(.ant-table-wrapper) {
  width: 100%;
  overflow-x: hidden; /* 隐藏表格水平滚动条 */
}

:deep(.ant-table-container) {
  width: 100%;
}

:deep(.ant-table-content) {
  width: 100%;
}

:deep(.ant-table-body) {
  overflow-x: hidden !important; /* 隐藏表格内容水平滚动条 */
}

/* 调整表格列宽 */
:deep(.ant-table-thead > tr > th.ant-table-column-has-sorters) {
  overflow-wrap: break-word;
  word-wrap: break-word;
}

:deep(.ant-table-tbody > tr > td) {
  overflow-wrap: break-word;
  word-wrap: break-word;
  vertical-align: top;
}

/* 公式表达式列样式 */
:deep(.ant-table-tbody > tr > td:nth-child(3)) {
  padding: 0;
  overflow: visible;
  width: 500px;
}

/* 确保表格列宽固定 */
:deep(.ant-table-thead > tr > th) {
  table-layout: fixed;
}

:deep(.ant-table) {
  table-layout: fixed;
}

/* 列宽样式 */
:deep(.column-id) {
  width: 120px !important;
  min-width: 120px !important;
}

:deep(.column-description) {
  width: 180px !important;
  min-width: 180px !important;
}

:deep(.column-latex) {
  width: 500px !important;
  min-width: 500px !important;
  padding: 0 !important;
  overflow: visible !important;
  text-align: left !important;
}

/* 确保公式表达式列内容从左边开始显示 */
:deep(.column-latex > div) {
  text-align: left !important;
  margin-left: 0 !important;
  padding-left: 0 !important;
}

:deep(.column-action) {
  width: 120px !important;
  min-width: 120px !important;
}

:deep(.column-param-name) {
  width: 150px !important;
  min-width: 150px !important;
}

:deep(.column-param-type) {
  width: 100px !important;
  min-width: 100px !important;
}

:deep(.column-param-value) {
  width: 180px !important;
  min-width: 180px !important;
}

:deep(.column-param-describe) {
  width: 180px !important;
  min-width: 180px !important;
}

/* 表格样式 */
.formula-query-page :deep(.ant-table-wrapper) {
  width: 100%;
}

/* 参数表格容器样式 */
:deep(.ant-table-wrapper .ant-table-container) {
  width: 100%;
}

/* 表格内容样式 */
.formula-detail :deep(.ant-table-wrapper),
.formula-edit-content :deep(.ant-table-wrapper) {
  width: 100%;
}

/* 添加滚动提示 */
.scrollable-formula::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 100%;
  background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,0.8));
  pointer-events: none;
}



/* 按钮样式优化 */
:deep(.ant-btn-primary) {
  height: 32px;
  font-weight: 500;
  transition: all 0.3s;
  border-radius: 4px;
}

:deep(.ant-btn-primary:hover) {
  opacity: 0.9;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);
}

/* 全局公式样式 - 确保所有公式内容从左边开始显示 */
:deep(.MathJax),
:deep(.MathJax_Display),
:deep(.MathJax_SVG),
:deep(.MathJax_SVG_Display) {
  text-align: left !important;
  margin-left: 0 !important;
  padding-left: 0 !important;
  display: inline-block !important;
  position: static !important;
}


</style>