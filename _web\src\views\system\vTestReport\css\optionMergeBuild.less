// 通用
.mt5 {
  margin-top: 5px;
}

.mt10 {
  margin-top: 10px;
}

.mr5 {
  margin-right: 5px;
}

.mr8 {
  margin-right: 8px;
}

.mr10 {
  margin-right: 10px;
}

.ml3 {
  margin-left: 3px;
}

.ml10 {
  margin-left: 10px;
}

.fs14 {
  font-size: 14px;
}

h3 {
  font-size: 15px;
  font-weight: bold;
  padding: 0;
  margin: 0;
}

.flex-sb-center-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.wrapper {
  display: flex;
  flex-wrap: wrap;
}

.block {
  height: fit-content;
  padding: 10px 10px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
  position: relative;
}

.normal-btn {
  padding: 2px 10px;
  color: #fff;
  background-color: #1890ff;
  letter-spacing: 2px;
  cursor: pointer;
  top: -5px;
  position: relative;
}

.btn-icon {
  font-size: 14px;
}

.btn-icon:hover {
  color: #1890ff;
}

/deep/ .block .ant-table-body {
  border: 1px solid #e8e8e8;
  overflow: auto; /* 滚动条 */
}

/deep/ .block .ant-table-thead > tr > th {
  padding: 6px 6px;
  font-size: 13px;
}

/deep/ .block .ant-table-tbody > tr > td {
  padding: 0px 0px;
  font-size: 12px;
}

/deep/ .ant-table-body::-webkit-scrollbar {
  height: 8px;
  width: 6px;
}

/deep/ .ant-table-body::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;

  background: #dddbdb;
}

/deep/ .ant-table-body::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: #f1f1f1;
}

/deep/ .ant-table-footer {
  padding: 0;
}

.footer-btn {
  width: 100%;
  height: 32px;
  border: 1px solid #e8e8e8;
  background: #fff;
  color: #999;
  font-size: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.footer-btn:hover {
  color: #1890ff;
}