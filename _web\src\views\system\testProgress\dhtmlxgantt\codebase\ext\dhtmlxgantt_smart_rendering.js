/*
@license

dhtmlxGantt v.6.3.3 Standard

This version of dhtmlxGantt is distributed under GPL 2.0 license and can be legally used in GPL projects.

To use dhtmlxGantt in non-GPL projects (and get Pro version of the product), please obtain Commercial/Enterprise or Ultimate license on our site https://dhtmlx.com/docs/products/dhtmlxGantt/#licensing or contact <NAME_EMAIL>

(c) XB Software Ltd.

*/
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("ext/dhtmlxgantt_smart_rendering",[],e):"object"==typeof exports?exports["ext/dhtmlxgantt_smart_rendering"]=e():t["ext/dhtmlxgantt_smart_rendering"]=e()}(window,function(){return function(t){var e={};function n(a){if(e[a])return e[a].exports;var i=e[a]={i:a,l:!1,exports:{}};return t[a].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=t,n.c=e,n.d=function(t,e,a){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:a})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var a=Object.create(null);if(n.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)n.d(a,i,function(e){return t[e]}.bind(null,i));return a},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="/codebase/",n(n.s=224)}({224:function(t,e){gantt.config.smart_rendering=!0,gantt._smart_render={getViewPort:function(){var t=gantt.$ui.getView("timeline"),e=gantt.$ui.getView("grid"),n=gantt.$layout;t&&t.isVisible()?n=t:e&&e.isVisible()&&(n=e);var a=n.getSize(),i=gantt.getScrollState();return{y:i.y,y_end:i.y+a.y,x:i.x,x_end:i.x+a.x}},getScrollSizes:function(){var t=gantt.getScrollState();return t.x=t.x||0,t.y=t.y||gantt.getVisibleTaskCount()*gantt.config.row_height,t},isInViewPort:function(t,e){return!!(t.y<e.y_end&&t.y_end>e.y)},isTaskDisplayed:function(t,e){return!(!gantt.$keyboardNavigation||!gantt.$keyboardNavigation.dispatcher.isTaskFocused(t))||this.isInViewPort(this.getTaskPosition(t),this.getViewPort())},isLinkDisplayed:function(t,e){return this.isInViewPort(this.getLinkPosition(t,e),this.getViewPort())},getTaskPosition:function(t){var e=gantt.getTaskTop(t);return{y:e,y_end:e+gantt.config.row_height}},getLinkPosition:function(t,e){var n=gantt.getTaskTop(e.source),a=gantt.getTaskTop(e.target);return{y:Math.min(n,a),y_end:Math.max(n,a)+gantt.config.row_height}},getRange:function(t){t=t||0;for(var e=this.getViewPort(),n=Math.floor(Math.max(0,e.y)/gantt.config.row_height)-t,a=Math.ceil(Math.max(0,e.y_end)/gantt.config.row_height)+t,i=gantt.$data.tasksStore.getIndexRange(n,a),r=[],o=0;o<i.length;o++)r.push(i[o].id);return r},_redrawItems:function(t,e){for(var n={},a=0;a<e.length;a++)n[e[a].id]=!0;for(var i={},r=0;r<t.length;r++){var o=t[r];for(var s in o.rendered)if(n[s]){var c=o.rendered[s];c&&c.parentNode&&(i[s]=!0)}else o.hide(s);for(a=0;a<e.length;a++)i[e[a].id]||o.restore(e[a])}},_getVisibleTasks:function(){for(var t=this.getRange(),e=[],n=0;n<t.length;n++){var a=gantt.getTask(t[n]);a.$index=n,gantt.resetProjectDates(a),e.push(a)}return e},_getVisibleLinks:function(){for(var t=[],e=gantt.$data.linksStore.getIndexRange(),n=0;n<e.length;n++)this.isLinkDisplayed(e[n].id,e[n])&&t.push(e[n]);return t},_recalculateLinkedProjects:function(t){for(var e={},n=0;n<t.length;n++)e[t[n].source]=!0,e[t[n].target]=!0;for(var n in e)gantt.isTaskExists(n)&&gantt.resetProjectDates(gantt.getTask(n))},updateRender:function(){gantt.callEvent("onBeforeSmartRender",[]);var t=this._getVisibleTasks(),e=this._getVisibleLinks();this._recalculateLinkedProjects(e);var n=gantt.$services.getService("layers"),a=n.getDataRender("task"),i=n.getDataRender("link");this._redrawTasks(a.getLayers(),t),this._redrawItems(i.getLayers(),e),gantt.callEvent("onSmartRender",[])},_redrawTasks:function(t,e){this._redrawItems(t,e)},cached:{},_takeFromCache:function(t,e,n){this.cached[n]||(this.cached[n]=null);var a=this.cached[n];return void 0!==t?(a||(a=this.cached[n]={}),void 0===a[t]&&(a[t]=e(t)),a[t]):(a||(a=e()),a)},initCache:function(){for(var t=["getLinkPosition","getTaskPosition","isTaskDisplayed","isLinkDisplayed","getViewPort","getScrollSizes"],e=0;e<t.length;e++){var n=t[e],a=gantt.bind(this[n],this);this[n]=function(t,e){return function(n){return this._takeFromCache(n,t,e)}}(a,n)}this.invalidateCache(),this.initCache=function(){}},invalidateCache:function(){var t=this;function e(){t.cached.getViewPort=null,t.cached.getScrollSizes=null,t.cached.isTaskDisplayed=null,t.cached.isLinkDisplayed=null}function n(){e(),t.cached.isTaskDisplayed=null,t.cached.isLinkDisplayed=null,t.cached.getLinkPosition=null,t.cached.getTaskPosition=null}gantt.attachEvent("onClear",function(){n()}),gantt.attachEvent("onParse",function(){n()}),gantt.attachEvent("onAfterLinkUpdate",function(e){t.cached.isLinkDisplayed&&(t.cached.isLinkDisplayed[e]=void 0),t.cached.getLinkPosition&&(t.cached.getLinkPosition[e]=void 0)}),gantt.attachEvent("onAfterTaskAdd",n),gantt.attachEvent("onAfterTaskDelete",n),gantt.attachEvent("onAfterTaskUpdate",function(e){t.cached.isTaskDisplayed&&(t.cached.isTaskDisplayed[e]=void 0),t.cached.getTaskPosition&&(t.cached.getTaskPosition[e]=void 0)}),gantt.attachEvent("onGanttScroll",e),gantt.attachEvent("onDataRender",n),this.invalidateCache=function(){}}},gantt.attachEvent("onGanttScroll",function(t,e,n,a){gantt.config.smart_rendering&&(e==a&&t!=n||gantt._smart_render.updateRender())}),gantt.attachEvent("onDataRender",function(){gantt.config.smart_rendering&&gantt._smart_render.updateRender()}),function(){var t=gantt.attachEvent("onGanttReady",function(){var e=gantt.$services.getService("layers");e.getDataRender("task").filters.push(function(t,e){return!gantt.config.smart_rendering||!!gantt._smart_render.isTaskDisplayed(t,e)}),e.getDataRender("link").filters.push(function(t,e){return!gantt.config.smart_rendering||!!gantt._smart_render.isLinkDisplayed(t,e)}),gantt.detachEvent(t)})}()}})});
//# sourceMappingURL=dhtmlxgantt_smart_rendering.js.map