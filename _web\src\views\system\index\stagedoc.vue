<template>
	<div class="container">
		<!-- Breadcrumb 面包屑 start -->
		<div>
			<a-breadcrumb class="breadcrumb" separator=">">
				<a-breadcrumb-item
					><a @click="gotoIndex(1)"
						><span class="" style="width: 100%; height: 100%; min-width: 14px; min-height: 14px; margin-right:4px"
							><svg
								xmlns="http://www.w3.org/2000/svg"
								class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 xuJzg svg-icon-path-icon fill"
								viewBox="0 0 48 48"
								width="14"
								height="14"
							>
								<defs data-reactroot=""></defs>
								<g>
									<rect width="48" height="48" fill="white" fill-opacity="0.01"></rect>
									<path
										fill-rule="evenodd"
										clip-rule="evenodd"
										d="M44 40.8361C39.1069 34.8632 34.7617 31.4739 30.9644 30.6682C27.1671 29.8625 23.5517 29.7408 20.1182 30.303V41L4 23.5453L20.1182 7V17.167C26.4667 17.2172 31.8638 19.4948 36.3095 24C40.7553 28.5052 43.3187 34.1172 44 40.8361Z"
										fill="none"
										stroke="#333"
										stroke-width="4"
										stroke-linejoin="round"
									></path>
								</g></svg></span
						>首页看板</a
					></a-breadcrumb-item
				>
				<a-breadcrumb-item>转阶段文件达成率</a-breadcrumb-item>
			</a-breadcrumb>
		</div>
		<!-- Breadcrumb 面包屑 start -->

		<!-- 主标题 start -->
		<div class="head-title">{{ "动力电池研究院" }}</div>
		<!-- 主标题 end -->

		<div>
			<!-- 表格 start -->
			<div class="table-wrapper">
				<a-table
					:style="`height:${tableHeight}px`"
					sticky
					:data-source="data"
					:columns="columns"
					size="middle"
					:pagination="pagination"
					:loading="loading"
				>
                    <span slot="mstatus" slot-scope="text">
						{{ "product_stage_status" | dictType(text) }}
					</span>

                    <span slot="deptchild" slot-scope="text, record">{{
                        record.departmentOptionList
                            .filter(function(e) {
                                return 0 != parseInt(e.pid)
                            })
                            .map(function(e) {
                                return e.value
                            })[0]
                        }}
                    </span>

					<span slot="k0reach" slot-scope="text,record">
						<span v-if="text == 0">--</span>
						<a v-else @click="toDetail(record,1)">{{(text *100)+'%'}}</a>
					</span>
					<span slot="m1reach" slot-scope="text,record">
						<span v-if="text == 0">--</span>
						<a v-else @click="toDetail(record,2)">{{(text *100)+'%'}}</a>
					</span>
					<span slot="m2reach" slot-scope="text,record">
						<span v-if="text == 0">--</span>
						<a v-else @click="toDetail(record,4)">{{(text *100)+'%'}}</a>
					</span>
					<span slot="m3reach" slot-scope="text,record">
						<span v-if="text == 0">--</span>
						<a v-else @click="toDetail(record,6)">{{(text *100)+'%'}}</a>
					</span>
				</a-table>
			</div>
			<!-- 表格 end -->
		</div>
		<docsModel ref="docsModel" />
	</div>
	
</template>

<script>
import { getProjectStageDetail } from "@/api/modular/system/chartManage"
import docsModel from "./model/docsModel.vue"
import _ from "lodash"
export default {
	components: {
		docsModel
	},
	data() {
		return {
			loading: false,
			pagination: {
				current: 1,
				pageSize: 10,
				total: 0,
				showSizeChanger: true,
				showQuickJumper: true,
				onChange: (current, size) => {
					this.pagination.current = current
					this.pagination.pageSize = size
				},
				onShowSizeChange: (current, pageSize) => {
					this.pagination.current = 1
					this.pagination.pageSize = pageSize
				}
			},
			tableHeight:
				document.documentElement.clientHeight - 40 - 20 - 47 - 20,

			
			data: [],
			
			columns: [],
		}
	},
	methods: {
		toDetail(record,stage){
			this.$refs.docsModel.view(record.issueId,stage)
			/* this.$router.push({
				path: "/project_overview",
				query: {
					issueId: record.issueId,
					open:'C',
					nav:'3',
					stage:stage
				}
			}) */
		},
		getByClass(parent, cls) {
			if (parent.getElementsByClassName) {
				return Array.from(parent.getElementsByClassName(cls));
			} else {
				var res = [];
				var reg = new RegExp(' ' + cls + ' ', 'i')
				var ele = parent.getElementsByTagName('*');
				for (var i = 0; i < ele.length; i++) {
					if (reg.test(' ' + ele[i].className + ' ')) {
						res.push(ele[i]);
					}
				}
				return res;
			}
		},
		initMain() {
			let that = this
			that.$nextTick(() => {
				let items = that.getByClass(document, 'ant-layout-content')
				for (const e of items) {
					e.style.paddingLeft = 0
				}
			})
		},
		info(strArr) {
			const h = this.$createElement;
			let msgs = []
			for (let msg of strArr) {
				msgs.push(h('p',msg))
			}
			console.log(strArr)
			this.$info({
				title: '变更详情',
				content: h('div', {},msgs),
				onOk() {},
			});
		},
		
		gotoIndex(index) {
			this.$router.push("/product_chart")
		},
		handleSearch(selectedKeys, confirm, dataIndex) {
			confirm()
		},

		handleReset(clearFilters) {
			clearFilters()
		},
		
		callProjectStageDetail() {
			this.loading = true
			getProjectStageDetail({})
				.then(res => {
					if (res.result) {
						this.data = res.data
						this.columns = [
							{
								title: "产品名称",
								dataIndex: "productProjectName",
								align: "center",
							},
                            {
								title: "项目名称",
								dataIndex: "projectName",
								align: "center",
							},
							{
								title: "项目等级",
								dataIndex: "productLevel",
								align: "center",
							},
							{
								title: "项目阶段",
								dataIndex: "mstatus",
								align: "center",
                                scopedSlots: { customRender: "mstatus" }
							},
							{
								title: "项目启动",
								align: "center",
								children:[
                                    {
                                        title: "K0",
                                        dataIndex: "k0Reach",
                                        align: "center",
										scopedSlots: {
											customRender: "k0reach"
										}
                                    },
                                ]
							},
							{
								title: "项目规划",
								align: "center",
								children:[
                                    {
                                        title: "M1",
                                        dataIndex: "m1Reach",
                                        align: "center",
										scopedSlots: {
											customRender: "m1reach"
										}
                                    },
                                ]
							},
							{
								title: "A样",
								align: "center",
								children:[
                                    {
                                        title: "M2",
                                        dataIndex: "m2Reach",
                                        align: "center",
										scopedSlots: {
											customRender: "m2reach"
										}
                                    },
                                ]
							},
							{
								title: "B样",
								align: "center",
								children:[
                                    {
                                        title: "M3",
                                        dataIndex: "m3Reach",
                                        align: "center",
										scopedSlots: {
											customRender: "m3reach"
										}
                                    },
                                ]
							},
              				{
								title: "PD",
								dataIndex: "productManager",
								align: "center",
							},
							{
								title: "RPM",
                                align: "center",
                                dataIndex: "researchProjectManager"
							},
                            {
								title: "产品中心",
                                align: "center",
                                dataIndex: "deptchild",
                                scopedSlots: {
                                    customRender: "deptchild"
                                }
							}
						]
						
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
					this.loading = false
				})
				.catch(err => {
					this.loading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		}
	},
	mounted() {
		this.callProjectStageDetail()

		// 动态修改--height的值
		document.documentElement.style.setProperty(`--height`, `${this.tableHeight - 63}px`)
		this.initMain()
	},
	destroyed() {
	}
}
</script>

<style lang="less" scoped>
// 面包屑
.ant-breadcrumb a {
	color: #5d90fa !important;
}
.ant-breadcrumb {
	font-size: 12px !important;
}

.board {
	display: flex;
	margin-bottom: 10px;
}
.head {
	position: absolute;
	top: 12px;
	left: 12px;
}
.col1 {
	width: 50%;
}
.col2 {
	width: 50%;
}
.item {
	padding: 8px;
	border-radius: 10px;
	overflow: hidden;
	background: #fff;
	box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
	position: relative;
}

.chart_table {
	width: 100%;
	height: 35vh;
}
/deep/.ant-table-placeholder {
	padding: 0;
}

// 表头
/deep/.ant-table-thead > tr > th {
	font-size: 13px;
	background: #f5f5f5 !important;
	color: #333;
}
/deep/.ant-table-tbody {
	background: #fff;
	color: #666;
}
/deep/.ant-table-thead > tr > th .anticon-filter,
/deep/.ant-table-thead > tr > th .ant-table-filter-icon {
	color: #999;
}
/deep/.ant-table-thead
	> tr
	> th.ant-table-column-has-actions.ant-table-column-has-filters
	.anticon-filter.ant-table-filter-open,
/deep/.ant-table-thead
	> tr
	> th.ant-table-column-has-actions.ant-table-column-has-filters
	.ant-table-filter-icon.ant-table-filter-open,
/deep/.ant-table-thead > tr > th.ant-table-column-has-actions.ant-table-column-has-filters:hover .anticon-filter:hover,
/deep/.ant-table-thead
	> tr
	> th.ant-table-column-has-actions.ant-table-column-has-filters:hover
	.ant-table-filter-icon:hover {
	background: #fff;
	color: rgb(201, 201, 201);
}
/deep/.ant-checkbox-group-item {
	display: block;
	margin: 0 8px;
}

/deep/.ant-table-small .ant-table-content .ant-table-body {
	margin: 0;
}

// 表格
.table-wrapper {
	border-radius: 10px;
	overflow: hidden;
	box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
}

:root {
	--height: 600px;
}

/deep/.ant-table-body {
	height: var(--height) !important;
	overflow-y: scroll;
}

// 表头固定
/deep/.ant-table-thead {
	position: sticky;
	top: 0;
	z-index: 666;
}

/* 主标题 */

.head-title {
	color: #333;
	padding: 10px 0;
	font-size: 18px;
	font-weight: 600;
}

.head-title::before {
	width: 8px;
	background: #1890ff;
	margin-right: 8px;
	content: "\00a0"; /* 填充空格 */
}

.ellipsis-tip {
	width: 200px;
	word-break: break-all;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 3; /* 这里是超出几行省略 */
	overflow: hidden;
}
.ellipsis {
	cursor: pointer;
	font-size: 12px;
    text-align: left;
    padding-left: 2px;
    word-break: break-all;
    line-height: 1.5em;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 100%;
    background: initial;
	text-align: left;
    overflow: hidden;
    display: -webkit-inline-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
}
/deep/tr th:hover {
	background-color: #e8e8e8 !important;
	color: #333 !important;
}
/deep/.ant-table-middle > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th{
	    padding: 1px 2px;
}
</style>
