import _ from "lodash";
import html2canvas from "html2canvas"
import PreviewDrawer from "../components/previewDrawer"
import pageComponent from "../components/pageComponent";
import { sysFileInfoUpload } from '@/api/modular/system/fileManage'
import pbiReturnTop from '@/components/pageTool/components/pbiReturnTop.vue'

export const mixin = {
  props: {
  },
  data() {
    return {
      /* 接口返回来的数据 */
      record: {},
      allDataJson: {},
      queryParam: {},

      echartsColor: [
        "#c00000",
        "#0070c0",
        "#808080",
        "#7030a0",
        "#4472c4",
        "#a5a5a5",
        "#ed7d31",
        "#5b9bd5",
        "#70ad47",
        "#000000",
        "#ff9999",
        "#ffc000",
        "#00b050"
      ],
      echartsColorShortList: [
        "#0070c0",
        "#c00000"
      ],
      echartsColorLongList: [
        "#808080",
        "#0070c0",
        "#c00000",
        "#00b050",
        "#ffc000",
        "#ff9999",
        "#7030a0",
        "#000000",
        "#70ad47",
        "#4472c4",
        "#ffc000",
        "#a5a5a5",
        "#ed7d31",
        "#5b9bd5",
        "#44546a"
      ],
      echartsTypeList: [
        "solid",
        "dashed",
        "dotted"
      ],
      menuOptions: [
        {
          label: '标题',
          editType: 'title'
        },
        {
          label: '图例',
          editType: 'legend'
        },
        {
          label: '数据',
          editType: 'tag'
        },
        {
          label: '坐标轴',
          editType: 'axis'
        },
        {
          label: '图表位置',
          editType: 'position'
        },
      ],

    }
  },
  methods: {
    handleRevealNum(width, data) {
      // 计算每行能容纳多少个图例   30:图例前面那条线的宽度  10: 间隔   14*this.allDataJson.legendList[0].split('').length : 每个字符占14
      // 默认只展示两行
      return (width / (30 + 10 + (14 * data[0].split('').length))).toFixed(0) * 2
    },

    // 计算每行能容纳多少个图例
    handleLegendRevealNum(data, totalLength, lineWidth, interval, isNeedBlockWidth = false) {
      //data ： 数据 totalLength ： 图表总长度  lineWidth ： 每个图例前面的线长度   legendGap:间隔值  isNeedBlockWidth:是否需要个
      const divElement = document.getElementById('revealText14')
      divElement.innerHTML = data
      return isNeedBlockWidth ? [Math.trunc(totalLength / (lineWidth + divElement.clientWidth + interval + 16 - 10)), lineWidth + divElement.clientWidth + interval + 16 - 10]
        : Math.trunc(totalLength / (lineWidth + divElement.clientWidth + interval + 16 - 10))
    },

    handleValueLength(data) {
      const divElement = document.getElementById('revealText15')
      divElement.innerHTML = data

      return divElement.clientWidth
    },
    handleValue14Length(data) {
      const divElement = document.getElementById('revealText14')
      divElement.innerHTML = data

      return divElement.clientWidth
    },

    // 处理用户双击图表
    _handleDblclickEchart(target, topTarget, targetObj) {
      if (topTarget.z === undefined) return
      // Z: 0:坐标轴,3:折线,4:图例,6:标题,50:折点
      this.drawerVisible = true
      this.editObj = targetObj

      switch (topTarget.z) {
        case 0:
          this.$set(this.chartCheckObj[targetObj], 'editObj', 'axis')
          break;
        case 3:
        case 50:
          const axs = target.parent?.parent?.__ecComponentInfo?.index
          this.$set(this.chartCheckObj[targetObj], 'tag', axs)
          this.$set(this.chartCheckObj[targetObj], 'editObj', 'tag')
          break;
        case 4:
          this.$set(this.chartCheckObj[targetObj], 'editObj', 'legend')
          break;
        case 6:
          this.$set(this.chartCheckObj[targetObj], 'editObj', 'title')
          break;
      }

    },

    

    // 获得要展示的图例(首先遍历图例数组，然后在图例中排除掉图例显隐没有勾选的数据，由于图例会更名，所以还得考虑更名的情况)
    _getLegend(editList) {
      const legend = []
      editList.legend.forEach(v => {
        const conditions1 = editList.legendRevealList.includes(v)
        if (conditions1) {
          legend.push(v)
        } else {
          const conditions2 = editList.legendEditName.findIndex(findItem => findItem.newName == v)
          if (conditions2 > -1 && editList.legendRevealList.includes(editList.legendEditName[conditions2].originName)) legend.push(v)
        }
      })

      return legend
    },
    // 获得y轴最大最小值
    _getYAxisRadius(originalMax, originalMin) {

      const differenceValue = originalMax - originalMin

      const transferMax = Math.trunc(originalMax + differenceValue).toString().split("")
      const transferMin = Math.trunc(originalMin - differenceValue).toString().split("")

      const newMax = Number(transferMax.map((mapItem, mapIndex) => mapIndex == 0 ? (Number(mapItem) + 1).toString() : '0').join(""))
      const newMin = Number(transferMin.map((mapItem, mapIndex) => mapIndex == 0 ? mapItem : '0').join(""))



      return [newMax, newMin]
    },
    _getNewSocLegend(value) {
      const measureLength = value.replace("%SOC", "").split("").length
      let newName = ''

      switch (measureLength) {
        case 1:
          newName = value + '    '
          break;
        case 2:
          newName = value + '  '
          break;
        case 3:
          newName = value
          break;
      }

      return newName
    },
    // 算出图例两边的Padding
    _getSocPadding(totalLength, gridLeft, gridRight, legendNum, dcrRevealNum, blockWidth) {
      // totalLength:总长度   legendNum:图例数量   dcrRevealNum  :每行可容纳数量（已计算用户的修改值，例如间隔，宽度等） blockWidth:每个块的宽度

      // 先判断图例能放几行,如果legendNum 大于或等于 dcrRevealNum,不止一行，可以按照dcrRevealNum 算宽度  ;如果 legendNum 不大于 dcrRevealNum,则按照dcrRevealNum 算宽度
      const num = legendNum >= dcrRevealNum ? dcrRevealNum : legendNum

      return (totalLength - gridLeft - gridRight - blockWidth * num) / 2
    },

    // 得到页面图例列表
    _getLegendList(list, targetObj) {
      const lists = []
      list.forEach(v => {
        lists.push(v[targetObj])
      })
      return _.uniq(lists)
    }



  }
}

export const chart = {
  data() {
    return {
      editObj: "", // 选中的图表类型
      screenImageId:'', //传递回去的图片id
      drawerVisible: false,
      isShowReturnTop:false,
    }
  },
  components: {
    PreviewDrawer,
    pageComponent,
    pbiReturnTop
  },
  
  methods: {
    /*
     * 下载
     */
    handleNormalDown(target) {
      const dom = document.getElementById(target)

      let fileName = ''
      switch (target) {
        case 'dcr':
          fileName = "DCR@" + this.queryParam.reportBasic.current + "A Cycle Life"
          break;
        case 'growth':
          fileName = "DCR Growth@" + this.queryParam.reportBasic.current + "A Cycle Life"
          break;
        case 'resDcir':
          fileName = "DCR vs. Temperature"
          break;
        case 'charge':
          fileName = this.allDataJson.chargeTitle
          break;
        case 'discharge':
          fileName = this.allDataJson.dischargeTitle
          break;

        case 'disChargeCapacity':
          fileName = this.disChargeCapacityEchartTitle
          break;
        case 'capacityHoldRate':
          fileName = this.capacityHoldRateEchartTitle
          break;
        case 'disChargeEnergy':
          fileName = this.disChargeEnergyEchartTitle
          break;
        case 'energyHoldRate':
          fileName = this.energyHoldRateEchartTitle
          break;
        case 'coulombicEfficiency':
          fileName = this.coulombicEfficiencyEchartTitle
          break;

      }

      html2canvas(dom, {
        useCORS: true,
        backgroundColor: "#fff"
      }).then(canvas => {
        let canvasImg = canvas.toDataURL("image/png")
        const blob = this.handleB64toBlob(canvasImg.replace("data:image/png;base64,", ""))
        if (window.navigator.msSaveOrOpenBlob) {
          //兼容IE10
          navigator.msSaveBlob(blob, fileName)
        } else {
          const href = URL.createObjectURL(blob)
          const a = document.createElement("a")
          a.style.display = "none"
          a.href = href // 指定下载链接
          a.download = fileName
          a.click() //触发下载
          URL.revokeObjectURL(a.href)
        }
      })
    },

    // 倍率  --  ref
    handleNormalRefDown(target) {
      let capacityEchart = this.echarts.init(this.$refs[target], "walden")

      let fileName = ''
      switch (target) {
        case 'capacity':
          fileName = "Rate Discharge1"
          break;
      }

      const chartImageBase64 = capacityEchart.getConnectedDataURL({
        pixelRatio: 3, // 设置图片的分辨率，这里设置为2表示导出的图片分辨率为原始分辨率的两倍
        backgroundColor: "#fff" // 设置图片的背景颜色
      })
      const blob = this.handleB64toBlob(chartImageBase64.replace("data:image/png;base64,", ""))
      if (window.navigator.msSaveOrOpenBlob) {
        //兼容IE10
        navigator.msSaveBlob(blob, fileName)
      } else {
        const href = URL.createObjectURL(blob) //创建新的URL表示指定的blob对象
        const a = document.createElement("a") //创建a标签
        a.style.display = "none"
        a.href = href // 指定下载链接
        a.download = fileName //指定下载文件名
        a.click() //触发下载
        URL.revokeObjectURL(a.href) //释放URL对象
      }
    },

    // base64 转 blob
    handleB64toBlob(b64Data, contentType = null, sliceSize = null) {
      contentType = contentType || "image/png"
      sliceSize = sliceSize || 512
      let byteCharacters = window.atob(b64Data.substring(b64Data.indexOf(",") + 1))
      let byteArrays = []
      for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
        let slice = byteCharacters.slice(offset, offset + sliceSize)
        let byteNumbers = new Array(slice.length)
        for (let i = 0; i < slice.length; i++) {
          byteNumbers[i] = slice.charCodeAt(i)
        }
        var byteArray = new Uint8Array(byteNumbers)
        byteArrays.push(byteArray)
      }
      return new Blob(byteArrays, { type: contentType })
    },

    /*
    * 编辑图表
    */
    handleEditEcharts(target) {
      this.editObj = target
      this.drawerVisible = true
    },

    // 截取保存模板的图表
    handleScreenshot(){
      console.log(this.editObj)
      const dom = document.getElementById(this.editObj)

      console.log(dom)
      setTimeout(() => {
        html2canvas(dom, {
          useCORS: true,
          backgroundColor: "#fff"
        }).then(canvas => {
          let canvasImg = canvas.toDataURL("image/png")
          const blob = this.handleB64toBlob(canvasImg.replace("data:image/png;base64,", ""))

          const formData = new FormData()
          formData.append('file', blob)
          sysFileInfoUpload(formData).then((res) => {
            this.screenImageId = res.data
          })
        })  
      },500)
    },

    /*
     *  取消 
     */
    cancel() {
      this.init()
    },

    // 返回顶部
    // 返回顶部
    // handleReturnTop() {
      // let timer = setInterval(() => {
      //   const topHeight = this.$refs.wrapper.scrollTop
      //   if (topHeight == 0) {
      //     clearInterval(timer)
      //     this.isShowReturnTop = false
      //     return
      //   }
      //   const speed = Math.ceil(topHeight / 10)
      //   this.$refs.wrapper.scrollTop = topHeight - speed
      // }, 50)
    //   this.$refs.wrapper.scrollTop = 0
    // }
  }
}

// 日历寿命专属
export const calendar = {
  data() {
    return {
      // 折点类型数组 三角、圆形、矩形、菱形、箭头、图钉、【空心：三角、圆形、矩形、菱形、箭头、图钉】、倒三角、五角星
      echartsSymbolList: [
        'triangle', 'circle', 'rect', 'diamond', 'arrow', 'pin',
        'emptyTriangle', 'emptyCircle', 'emptyRect', 'emptyDiamond', 'emptyArrow', 'emptyPin',
        'path://M0,0 L10,0 L5,10 Z',
        'path://M100,22.4 L78.6,54.6 L44.2,54.6 L72.6,79.4 L62.8,112.6 L100,92.4 L137.2,112.6 L127.4,79.4 L155.8,54.6 L121.4,54.6 Z'
      ],
    }
  },
  components: {
    PreviewDrawer,
    pageComponent,
  },
  methods: {
    // 获取编辑图表数据、原始图表数据
    _getInitData(tagetObj, type = 'original') {
      const options = {
        XTitle: 'Storage Time / D',
        titleTop: 8,

        legendWidth: 20,
        legendHeight: tagetObj === 'height' ? 7 : 5,
        legendGap: 5,//图例间隙
        legendOrient: 'vertical',
        legendZip: 'false',
        legendBgColor: '',
        legendFontSize: 12,
        legendTop: 40,
        legendNameType: 'sampleCode',

        gridTop: 40,
        gridRight: 40,
        gridBottom: 70,

        xType: "value",
        xMin: 0,
        xMax: 0,
        xInterval: 0,

        yType: "value",
        yMin: 0,
        yMax: 0,
        yInterval: 0,
        yDecimalNum: 0
      }
      if (type === 'edit') {
        options.series = []
        options.legend = []
        options.legendSort = []
        options.legendEditName = []
      }
      if (type === 'original') {
        options.checkData = []
      }

      switch (tagetObj) {
        case 'voltage':
          options.chartTitle = this.testCondition + "Calendar Life_OCV"
          options.YTitle = 'OCV / mV'
          break;
        case 'innerres':
          options.chartTitle = this.testCondition + "Calendar Life_ACR"
          options.YTitle = 'ACR / mΩ'
          break;
        case 'height':
          options.chartTitle = this.testCondition + "Calendar Life_Size"
          options.YTitle = 'Size / mm'
          break;
        case 'volume':
          options.chartTitle = this.testCondition + "Calendar Life_Volume"
          options.YTitle = 'Volume / g'
          break;
        case 'weight':
          options.chartTitle = this.testCondition + "Calendar Life_Weight"
          options.YTitle = 'Weight / g'
          break;
        case 'isolateres':
          options.chartTitle = this.testCondition + "Calendar Life_Insulation Resistance"
          options.YTitle = 'Insulation Resistance / mΩ'
          break;
      }

      return options
    },
    // 处理图表原始数据
    _handleOriginalData(targetObj, cycleList, checkData) {
      let lineColorList = []
      let seriesOriginal = []
      let checkDataOriginal = []
      let legendList = []
      let yAxisList = []
      let seriesList = []
      let lineSymbolList = []


      for (let i = 0; i < cycleList.length; i++) {
        const have = targetObj === 'height' ? lineColorList.find(findItem => findItem.name === cycleList[i].sizeType) : lineColorList.find(findItem => findItem.name === cycleList[i].sampleCode)
        if (have == undefined) {
          if (targetObj === 'height') {
            lineColorList.push({ name: cycleList[i].sizeType, color: lineColorList.length < this.echartsColor.length ? this.echartsColor[lineColorList.length] : this.getRandomHexColor() })
          } else {
            lineColorList.push({ name: cycleList[i].sampleCode, color: this.echartsColor[lineColorList.length] })
          }
        }

        if (targetObj === 'height') {
          const haveSymbol = lineSymbolList.find(v => v.name === cycleList[i].symbolType)
          if (haveSymbol == undefined) lineSymbolList.push({ name: cycleList[i].symbolType, symbol: this.echartsSymbolList[lineSymbolList.length % this.echartsSymbolList.length] })
        }

        const temIndex = checkData.findIndex(findItem => findItem.soc == cycleList[i].sampleCode || findItem.soc == cycleList[i].batteryCode)

        if (checkData.length === 0) {
          this.chartLegendNameList[targetObj] = []
          this.chartLegendNameList[targetObj].push({ sampleCode: cycleList[i].sampleCode, batteryCode: cycleList[i].batteryCode })
        }



        let series = {
          index: i + 1,
          id: cycleList[i][this.chartEditData[targetObj].legendNameType] + (i + 1),
          name: cycleList[i][this.chartEditData[targetObj].legendNameType],
          soc: cycleList[i][this.chartEditData[targetObj].legendNameType],
          type: "line",
          barGap: 0,
          markPoint: {
            data: []
          },
          connectNulls: checkData.length === 0 ? false : Boolean(Number(checkData[temIndex].connectNulls)),
          symbol: checkData.length === 0
            ? targetObj === 'height' ? lineSymbolList[lineSymbolList.findIndex(v => v.name === cycleList[i].symbolType)].symbol : "rect"
            : checkData[temIndex].symbol,
          symbolSize: checkData.length === 0 ? (targetObj === 'height' ? 7 : 5) : checkData[temIndex].symbolSize,
          lineStyle: {
            width: checkData.length === 0 ? 1 : checkData[temIndex].lineWidth,
            type: checkData.length === 0 ? "solid" : checkData[temIndex].lineType,
            color:
              checkData.length === 0
                ? lineColorList[lineColorList.findIndex(v => v.name === (targetObj === 'height' ? cycleList[i].sizeType : cycleList[i].sampleCode))].color
                : checkData[temIndex].lineColor
          },
          itemStyle: {
            color:
              checkData.length === 0
                ? lineColorList[lineColorList.findIndex(v => v.name === (targetObj === 'height' ? cycleList[i].sizeType : cycleList[i].sampleCode))].color
                : checkData[temIndex].itemColor
          },
          emphasis: {
            focus: "series"
          },
          large: true,
          sampling: 'lttb',
          data: cycleList[i].data.map((mapItem, index) => {
            return { id: index, value: mapItem.value }
          }),
        }

        // 设置最大最小值
        if (checkData.length > 0 && checkData[temIndex].maxPoint) {
          series.markPoint.data.push({ type: "max", name: "Max" })
        }
        if (checkData.length > 0 && checkData[temIndex].minPoint) {
          series.markPoint.data.push({ type: "min", name: "Min" })
        }

        seriesOriginal.push({
          index: i + 1,
          id: cycleList[i][this.chartEditData[targetObj].legendNameType] + (i + 1),
          name: cycleList[i][this.chartEditData[targetObj].legendNameType],
          soc: cycleList[i][this.chartEditData[targetObj].legendNameType],
          synchronization: checkData.length === 0 ? i : checkData[temIndex].synchronization,
          maxPoint: checkData.length === 0 ? false : checkData[temIndex].maxPoint,
          minPoint: checkData.length === 0 ? false : checkData[temIndex].minPoint,
          connectNulls: false,
          symbol: checkData.length === 0
            ? targetObj === 'height' ? lineSymbolList[lineSymbolList.findIndex(v => v.name === cycleList[i].symbolType)].symbol : "rect"
            : checkData[temIndex].symbol,
          symbolSize: checkData.length === 0 ? (targetObj === 'height' ? 7 : 5) : checkData[temIndex].symbolSize,
          itemColor:
            checkData.length === 0
              ? lineColorList[lineColorList.findIndex(v => v.name === (targetObj === 'height' ? cycleList[i].sizeType : cycleList[i].sampleCode))].color
              : checkData[temIndex].itemColor,
          lineType: checkData.length === 0 ? "solid" : checkData[temIndex].lineType,
          lineWidth: checkData.length === 0 ? 1 : checkData[temIndex].lineWidth,
          lineColor:
            checkData.length === 0
              ? lineColorList[lineColorList.findIndex(v => v.name === (targetObj === 'height' ? cycleList[i].sizeType : cycleList[i].sampleCode))].color
              : checkData[temIndex].lineColor
        })

        // 原始值
        checkDataOriginal.push({
          soc: cycleList[i][this.chartEditData[targetObj].legendNameType],
          symbol: targetObj === 'height' ? lineSymbolList[lineSymbolList.findIndex(v => v.name === cycleList[i].symbolType)].symbol : "rect",
          symbolSize: targetObj === 'height' ? 7 : 5,
          itemColor: lineColorList[lineColorList.findIndex(v => v.name === (targetObj === 'height' ? cycleList[i].sizeType : cycleList[i].sampleCode))].color,
          lineType: "solid",
          lineWidth: 1,
          lineColor: lineColorList[lineColorList.findIndex(v => v.name === (targetObj === 'height' ? cycleList[i].sizeType : cycleList[i].sampleCode))].color
        })

        yAxisList.push(...cycleList[i].data.map(mapItem => Number(mapItem.value[1])))

        legendList.push(cycleList[i][this.chartEditData[targetObj].legendNameType])
        seriesList.push(series)
      }

      return [seriesOriginal, checkDataOriginal, seriesList, legendList, [], Math.max.apply(null, yAxisList), Math.min.apply(null, yAxisList)]
    },
    // 获得图表实例
    _getEchart(targetObj) {
      const oldEchart = this.echarts.getInstanceByDom(document.getElementById(targetObj)) // 查找图表实例是否存在
      if (oldEchart) {
        return oldEchart
      } else {
        // return this.echarts.init(this.$refs[targetObj], "walden", { renderer: "svg" }) // 测试
        return this.echarts.init(this.$refs[targetObj], "walden", { devicePixelRatio: 2 }) // 测试
      }
    },
    // 计算出最大值最小值，以及间距
    _handleXYAxis(options, echarts, targetObj) {
      if (options.xAxis[0].type === "value") {
        const XAxis = echarts.getModel().getComponent("xAxis").axis.scale
        this.chartEditData[targetObj].xMin = XAxis._extent[0]
        this.chartEditData[targetObj].xMax = XAxis._extent[1]
        this.chartEditData[targetObj].xInterval = XAxis._interval
      }

      if (options.yAxis[0].type === "value") {
        const YAxis = echarts.getModel().getComponent("yAxis").axis.scale
        this.chartEditData[targetObj].yMin = YAxis._extent[0]
        this.chartEditData[targetObj].yMax = YAxis._extent[1]
        this.chartEditData[targetObj].yInterval = YAxis._interval
      }

      if (this.chartXYNum[`${targetObj}XNum`] === undefined) {
        this.chartXYNum[`${targetObj}XNum`] = 1
        this.chartResetOriginal[targetObj].xMin = this.chartEditData[targetObj].xMin
        this.chartResetOriginal[targetObj].xMax = this.chartEditData[targetObj].xMax
        this.chartResetOriginal[targetObj].xInterval = this.chartEditData[targetObj].xInterval
        
      }

      if (this.chartXYNum[`${targetObj}YNum`] === undefined) {
        this.chartXYNum[`${targetObj}YNum`] = 1
        this.chartResetOriginal[targetObj].yMin = this.chartEditData[targetObj].yMin
        this.chartResetOriginal[targetObj].yMax = this.chartEditData[targetObj].yMax
        this.chartResetOriginal[targetObj].yInterval = this.chartEditData[targetObj].yInterval
      }

    },

    _handleLegend(processingResult, seriesList, legendData, gridData, valueLength, targetObj) {
      /* 图例显隐赋值 开始 */
      if (!legendData.legendRevealList) {
        this.chartEditData[targetObj].legendRevealList = this.chartFrist[targetObj] ? _.cloneDeep(processingResult[3]).slice(0, 6) : _.cloneDeep(processingResult[3])

        this.chartEditData[targetObj].gridLeft = valueLength + 40 + 15
        this.chartResetOriginal[targetObj].gridLeft = valueLength + 40 + 15

        this.chartEditData[targetObj].yTitleLetf = valueLength + 15
        this.chartResetOriginal[targetObj].yTitleLetf = valueLength + 15
      }
      /* 图例显隐赋值 结束 */

      /* 图例排序 开始 */
      if (legendData.legendSort) {
        this.chartEditData[targetObj].legend = _.cloneDeep(legendData.legendSort) // 将页面上的图例数组按照用户设置的顺序排序
      }
      this.chartEditData[targetObj].legendSort = this.chartFrist[targetObj] ? _.cloneDeep(this.chartOriginalLegent[targetObj]) : _.cloneDeep(this.chartEditData[targetObj].legend) // 传回给在线编辑图表
      /* 图例排序 结束 */

      /* 图例变更名称 开始 */
      if (legendData.legendEditName) {
        // 遍历图例的变更
        legendData.legendEditName.forEach(v => {
          // 判断有新值，但是不是清空操作,修改对应的数据
          if (v.newName && !v.isReset) {
            // 修改原始图例
            // 先找到原始图例改了新值（原始图例的值等于修改的原始值）（因为每次都在最开头赋予了最开始的原始图例）
            let temIndex1 = this.chartOriginalLegent[targetObj].findIndex(findItem => findItem == v.originName)
            this.chartOriginalLegent[targetObj][temIndex1] = v.newName

            // 修改在线编辑图表的选中值的名称
            let temIndex2 = this.chartEditData[targetObj].legend.findIndex(findItem => findItem == v.originName)
            this.chartEditData[targetObj].legend[temIndex2] = v.newName

            // 修改this.chartEditData[targetObj].series
            this.chartEditData[targetObj].series.forEach(findItem => {
              findItem.name = findItem.name == v.originName ? v.newName : findItem.name
            })

            // 修改图上的图表数据的图例
            seriesList.forEach(findItem => {
              findItem.name = findItem.name == v.originName ? v.newName : findItem.name
            })
          }

          // 如果没有新值，然后是重置的
          if (!v.newName && v.isReset) {
            // 操作
            // 操作完之后isReset设为false
            v.previousName = ''
            v.isReset = false
          }
        })

        // 赋予修改后的图例修改名称数组
        this.chartEditData[targetObj].legendEditName = legendData.legendEditName
      }

      if (this.chartEditData[targetObj].legendEditName.length === 0 || !legendData.legendEditName) {
        this.chartEditData[targetObj].legendEditName = this.chartOriginalLegent[targetObj].map(v => {
          return { originName: v, previousName: '', newName: '', isReset: false }
        })
      }
      /* 图例变更名称 结束 */

      /* 没选中的图例的移除处理 开始 */
      if (legendData.legendEdit) {

        // 移除页面上的对应的图例
        for (let i = 0; i < this.chartEditData[targetObj].legend.length; i++) {
          if (!legendData.legendEdit.includes(this.chartEditData[targetObj].legend[i])) {
            this.chartEditData[targetObj].legend.splice(i, 1)
            i--
          }
        }

        // 移除页面上的对应的图例的图表数据
        for (let i = 0; i < seriesList.length; i++) {
          if (!legendData.legendEdit.includes(seriesList[i].name)) {
            seriesList.splice(i, 1)
            i--
          }
        }
      }
      /* 没选中的图例的移除处理 结束 */

      /* 图例 开始 */
      let legend = this._getLegend(this.chartEditData[targetObj])
      /* 图例 结束 */

      // 如果不是编辑图例距离左边,且没有编辑过图例距离左边,自动算 
      if ((this.chartEditData[targetObj].targetEditObj !== 'legendLeft' && !this.historyRecord[targetObj].includes('legendLeft')) || this.chartFrist[targetObj]) {

        const longestLegend = legend.reduce((a, b) => a.length > b.length ? a : b, "");
        let leftValue = 595 - (gridData.gridRight || 40) - 10 - this._getLegendBlockWidth(longestLegend, legendData.legendWidth || 20, legendData.legendFontSize || 12)  // 图例距离左边的值 : 总长 - 图表距离右边的距离(gridRight) - 边距10 - 自身长度

        this.chartEditData[targetObj].legendLeft = leftValue

        if (this.chartResetOriginal[targetObj].legendLeft === undefined) this.chartResetOriginal[targetObj].legendLeft = leftValue   // 只有第一次才赋值

        legendData.legendLeft = leftValue
      }

      // 如果不是编辑图例距离高度,且没有编辑过图例距离高度,自动算
      if ((this.chartEditData[targetObj].targetEditObj !== 'legendTop' && !this.historyRecord[targetObj].includes('legendTop')) || this.chartFrist[targetObj]) {
        let topValue = 415 - 70 - 5 - (legend.length * ((legendData.legendGap || 5) + (legendData.legendFontSize || 12) + 2) - (legendData.legendGap || 5))   // 总长 - 图表距离右边的距离(gridBottom) - 边距10 - 自身高度(2:实际会比字号多出2px)

        if (targetObj === 'height') topValue = topValue + 5

        this.chartEditData[targetObj].legendTop = topValue

        if (this.chartResetOriginal[targetObj].legendTop === undefined) this.chartResetOriginal[targetObj].legendTop = topValue  // 只有第一次才赋值

        legendData.legendTop = topValue
      }

      return [seriesList, legend]

    },
    // 算出图例项的长度（包含前面的线）
    _getLegendBlockWidth(data, lineWidth, fontSize) {

      const divElement = document.getElementById('revealTextNone')
      divElement.innerHTML = data
      divElement.style.fontSize = `${fontSize}px`
      divElement.style.fontFamily = 'Times New Roman'

      // 获得：每个图例最多能占多大位置
      // 注:这里的5是图例前面的线条与图例后面的字体中间的距离
      // 注:这里的1是不知道为什么每次算出来的值都比实际值少1px
      let length = lineWidth + 5 + divElement.clientWidth + 1


      // 判断是否含中文
      const regex = /[\u4e00-\u9fa5]+/g;
      if (regex.test(data)) {
        divElement.innerHTML = data.replace(regex, '')
        const divLength1 = divElement.clientWidth
        divElement.innerHTML = data.match(regex)[0]
        divElement.style.fontSize = `${fontSize - 2}px`   //此处-2：中文比数字字号小两号
        divElement.style.fontFamily = 'Source Han Sans'
        const divLength2 = divElement.clientWidth

        length = lineWidth + 5 + divLength1 + divLength2 + 1
      }

      return length
    },

    _handleValue(processingResult, targetObj) {
      this.chartOriginalseries[targetObj] = processingResult[0] // 原始图表数据
      this.chartResetOriginal[targetObj].checkData = processingResult[1] // 重置数据
      this.chartOriginalLegent[targetObj] = _.cloneDeep(processingResult[3]) // 原始图例  使用位置：在线编辑图表--图例数据
      this.chartEditData[targetObj].legend = _.cloneDeep(this.chartOriginalLegent[targetObj]) //拿到所有图例
      this.chartEditData[targetObj].series = _.cloneDeep(this.chartOriginalseries[targetObj]) //拿到所有数据
    },
    // 处理图表原始数据
    _getOptions(targetObj, editData, seriesList) {
      const options = {
        backgroundColor: '#ffffff',
        animationDuration: 2000,
        textStyle: {
          fontFamily: "Times New Roman"
        },
        tooltip: {
          trigger: "axis",
          confine: true,
          formatter: function (params) {
            var result = params[0].axisValue + "<br>" // 添加 x 轴的数值
            params.forEach(function (item, dataIndex) {
              result +=
                item.marker +
                item.seriesName +
                item.value[2] +
                '<div style="width:20px;display: inline-block;"></div>' +
                item.value[1] +
                "<br>" // 添加每个系列的数值
            })
            return result
          }
        },

        title: {
          text: editData.titleData.chartTitle || this.chartResetOriginal[targetObj].chartTitle,
          left: "center",
          top: editData.titleData.titleTop || 8,
          textStyle: {
            fontSize: 18,
            fontWeight: 500,
            color: '#000'
          }
        },
        grid: {
          show: true,
          top: editData.gridData.gridTop || this.chartResetOriginal[targetObj].gridTop,
          left: editData.gridData.gridLeft || this.chartResetOriginal[targetObj].gridLeft,
          right: editData.gridData.gridRight || 40,
          bottom: editData.gridData.gridBottom || 70,
          borderWidth: 0.5,
        },
        legend: {
          data: editData.legend,
          itemWidth: editData.legendData.legendWidth || 20,
          itemHeight: editData.legendData.legendHeight || (targetObj === 'height' ? 7 : 5),
          itemGap: editData.legendData.legendGap || 5,
          orient: editData.legendData.legendOrient || 'vertical',
          top: editData.legendData.legendTop || 40,
          padding: [0, 0],
          left: editData.legendData.legendLeft || this.chartResetOriginal[targetObj].legendLeft,
          textStyle: {
            fontSize: editData.legendData.legendFontSize || 12,
            color: "#000000"
          }
        },
        xAxis: [
          {
            name: editData.titleData.XTitle || this.chartResetOriginal[targetObj].XTitle,
            type: editData.axisData.xType || this.chartResetOriginal[targetObj].xType,
            axisTick: { show: false },
            splitLine: {
              show: true, // 显示分隔线
              lineStyle: {
                type: "solid",
                width: 0.5
              }
            },
            axisLabel: {
              show: true,
              width: 0.5,
              textStyle: {
                fontSize: "15",
                color: "#000000"
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              }
            },
            nameLocation: "middle", // 将名称放在轴线的中间位置
            nameGap: 35,
            nameTextStyle: {
              fontSize: 14,
              color: "#000000" // 可以根据需要调整字体大小
            },
            minInterval: 1,
          }
        ],
        yAxis: [
          {
            name: editData.titleData.YTitle || this.chartResetOriginal[targetObj].YTitle,
            type: editData.axisData.yType || this.chartResetOriginal[targetObj].yType,
            position: "left",
            min: editData.yMin,
            max: editData.yMax,
            nameGap: editData.titleData.yTitleLetf || this.chartResetOriginal[targetObj].yTitleLetf,
            splitLine: {
              show: true, // 显示分隔线
              lineStyle: {
                type: "solid",
                width: 0.5
              }
            },
            axisTick: {
              show: true // 显示刻度
            },
            axisLabel: {
              show: true,
              width: 0.5,
              textStyle: {
                fontSize: "15",
                color: "#000000"
              },
              formatter: function (value) {
                return Number(value).toFixed(editData.yDecimalNum)
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              }
            },
            nameLocation: "middle", // 将名称放在轴线的起始位置
            nameRotate: 90, // 旋转角度，使名称竖排
            nameTextStyle: {
              fontSize: 16, // 可以根据需要调整字体大小
              fontWeight: 500,
              color: "#000000"
            }
          }
        ],
        series: seriesList
      }
      if (targetObj === 'voltage') options.yAxis[0].interval = 300
      if (targetObj === 'height') {
        options.legend.formatter = (name) => {
          let index = -1;
          const regex = /[\u4e00-\u9fa5]/g;
          let match;
          while ((match = regex.exec(name)) !== null) {
            index = match.index;
          }

          const opacityValue = this._getLegendDiscrepancy(editData.longestLegend, name)

          if (index >= 0) {
            // 将0~index 的文字设置为思源黑体12大小，index之后的设置为新罗马字体14大小
            // return `<div style="font-family: 'Source Han Sans CN'; font-size: 12px;">${name.substring(0, index + 1)}</div>` +
            //   `<div style="font-family: 'Times New Roman'; font-size: 14px;">${name.substring(index + 1)}</div>`;
            return `{style1|${name.substring(0, index + 1)}}{style2|${name.substring(index + 1)}}{style3|${opacityValue[0]}}{style4|${opacityValue[1]}}`;
          } else {
            // 如果没有找到中文字符，直接返回原始文本
            return `{style2|${name}}`;
          }
        }
        options.legend.textStyle = {
          color: "#000000",
          rich: {
            style1: {
              fontFamily: 'Source Han Sans',
              fontSize: editData.legendData.legendFontSize ? editData.legendData.legendFontSize - 2 : 12 - 2,
            },
            style2: {
              fontFamily: 'Times New Roman',
              fontSize: editData.legendData.legendFontSize || 12
            },
            style3: {
              fontFamily: 'Source Han Sans',
              fontSize: editData.legendData.legendFontSize ? editData.legendData.legendFontSize - 2 : 12 - 2,
              opacity: 0
            },
            style4: {
              fontFamily: 'Times New Roman',
              fontSize: editData.legendData.legendFontSize || 12,
              opacity: 0
            }
          }
        }
      }

      if (editData.legendData.legendBgColor) {
        options.legend.backgroundColor = editData.legendData.legendBgColor
      }

      if (editData.axisData.xMin !== undefined) {
        options.xAxis[0].min = editData.axisData.xMin
      }
      if (editData.axisData.xMax !== undefined) {
        options.xAxis[0].max = editData.axisData.xMax
      }
      if (editData.axisData.xInterval !== undefined) {
        options.xAxis[0].interval = editData.axisData.xInterval
      }
      if (editData.axisData.yMin !== undefined) {
        options.yAxis[0].min = editData.axisData.yMin
      }
      if (editData.axisData.yMax !== undefined) {
        options.yAxis[0].max = editData.axisData.yMax
      }
      if (editData.axisData.yInterval !== undefined) {
        options.yAxis[0].interval = editData.axisData.yInterval
      }


      // 坐标轴类型赋值
      this.chartEditData[targetObj].xType = options.xAxis[0].type
      this.chartEditData[targetObj].yType = options.yAxis[0].type
      this.chartEditData[targetObj].seriesList = options.series

      return options
    },
    //算出图例的长度
    _getLegendDiscrepancy(template, original) {
      const regex = /[\u4e00-\u9fa5]+/g;
      const templateChinese = template.match(regex)[0]
      const originalChinese = original.match(regex)[0]

      const templateEnglish = template.replace(regex, '')
      const originalEnglish = original.replace(regex, '')

      const chinese = templateChinese.length - originalChinese.length
      const english = templateEnglish.length - originalEnglish.length

      return [template.split('').slice(0, chinese).join(''), template.replace(regex, '').split('').slice(0, english).join('')]
    },
    // 处理用户双击图表
    _handleDblclickEchart(target, topTarget, targetObj) {
      if (topTarget.z === undefined) return
      // Z: 0:坐标轴,3:折线,4:图例,6:标题,50:折点
      this.drawerVisible = true
      this.editObj = targetObj

      switch (topTarget.z) {
        case 0:
          this.$set(this.chartCheckObj[targetObj], 'editObj', 'axis')
          break;
        case 3:
        case 50:
          const axs = target.parent?.parent?.__ecComponentInfo?.index
          this.$set(this.chartCheckObj[targetObj], 'tag', axs)
          this.$set(this.chartCheckObj[targetObj], 'editObj', 'tag')
          break;
        case 4:
          this.$set(this.chartCheckObj[targetObj], 'editObj', 'legend')
          break;
        case 6:
          this.$set(this.chartCheckObj[targetObj], 'editObj', 'title')
          break;
      }

    },
  }
}

// 缩略图专属
export const thumbnail = {
  data() {
    return {
      activeKey: 1,
      // cRateArr:['capacity','tempRise','capacityVoltage'],
      echartObj: {},
      fristInit: { 
        // 倍率
        capacity: true,
        tempRise: true,
        capacityVoltage: true,

        // 循环
        disChargeCapacity:true,
        capacityHoldRate:true,
        disChargeEnergy:true,
        energyHoldRate:true
      },
      
      originalLegent: {}, //原始图例
      originalData: {}, //原始数据
      editData: {}, //编辑数据
      checkObj: {}, //暂时无用属性

      cycleArr:[
        {
          id: 'disChargeCapacity',
          label: 'Cycle Life - Capacity'
        },
        {
          id: 'capacityHoldRate',
          label: 'Cycle Life - Capacity%'
        },
        {
          id: 'disChargeEnergy',
          label: 'Cycle Life - Energy'
        },
        {
          id: 'energyHoldRate',
          label: 'Cycle Life - Energy%'
        }
      ],

      cRateArr: [
        {
          id: 'capacity',
          label: 'Rate Discharge'
        },
        {
          id: 'tempRise',
          label: 'Temperature Rise'
        },
        {
          id: 'capacityVoltage',
          label: 'Rate Discharge'
        },
      ]
    }
  },
  methods: {
    handleClickThumbnail(targetKey,targetReport) {
      this.activeKey = targetKey
      if (this.drawerVisible) this.drawerVisible = false

      // 倍率
      if(targetReport === 'cRate'){
        if(this.activeKey === 2 && !this.echartObj.tempRise) this.initNormalEchart('cRate','tempRise','tempRiseEchartsDataList')
        if(this.activeKey === 3 && !this.echartObj.capacityVoltage) this.initNormalEchart('cRate','capacityVoltage','capacityTempEchartsDataList','capacityEchartsDataList')
      }

      // 循环
      if(targetReport === 'cycle'){
        if(this.activeKey === 2 && !this.echartObj.capacityHoldRate) this.initNormalEchart('cycle','capacityHoldRate','capacityHoldRateEchartList','disChargeCapacityEchartList')
        if(this.activeKey === 3 && !this.echartObj.disChargeEnergy) this.initNormalEchart('cycle','disChargeEnergy','disChargeEnergyEchartList','disChargeCapacityEchartList')
        if(this.activeKey === 4 && !this.echartObj.energyHoldRate) this.initNormalEchart('cycle','energyHoldRate','energyHoldRateEchartList','disChargeCapacityEchartList')
      }
    },

    initNormalEchart(reportType,target,cyclicListName = null,echartListName = null){
      this.echartObj[target] = this.echarts.init(this.$refs[target], 'walden',{ devicePixelRatio: 2 })
      let seriesList = []
      let echartList = []

      let cyclicList = cyclicListName === null ? _.cloneDeep(this.allDataJson[`${target}EchartsDataList`]) : _.cloneDeep(this.allDataJson[cyclicListName])
      if(echartListName !== null) echartList = _.cloneDeep(this.allDataJson[echartListName])

      if(this.fristInit[target]){  // 首次加载
        seriesList = reportType === 'cRate' ? this._handleCRateEchartData(target,cyclicList,echartList,target === 'capacityVoltage' ? ['-Temp','-Voltage'] : ['']) :
                                              this._handleCycleEchartData(target,cyclicList,echartList)
      }else{  // 二次加载
        seriesList = this.editData[target].editSeries
      }

      let chartOption = reportType === 'cRate' ? this._handleCRateEchartOptions(target,seriesList) : 
                                                 this._handleCycleEchartOptions(target,seriesList)  

      this.echartObj[target].clear()
      this.echartObj[target].getZr().off('dblclick')
      const targetObj = target
      this.echartObj[target].getZr().on('dblclick', ({target, topTarget}) => {
      this._handleDblclickEchart(target, topTarget, targetObj )
      });
      this.echartObj[target].setOption(chartOption)
      
      if(this.fristInit[target]){
        if(reportType === 'cRate') this._handleCRateYAxisValue(target)
        if(reportType === 'cycle') this._handleCycleYAxisValue(target)
        this.fristInit[target] = false          
      }
    },

    handleDrawerSubmit(value) {
      const isEdit = !!value.targetEditObj
      const templateParam = this.reportChartTemplateList[this.editObj].templateParamJson

       // 数据编辑
      if(isEdit){
        if (value.targetEditIndex === undefined) {
          this.editData[this.editObj][value.targetEditObj] = value[value.targetEditObj]
        }else if(value.targetEditIndex === 'all' || value.targetEditObj === '"synchronization"' || value.targetEditObj === 'legendEditName'){

        }else {
          this.editData[this.editObj].series[value.targetEditIndex][value.targetEditObj] = value.checkData[value.targetEditIndex][value.targetEditObj]
        }
      }

      if(isEdit){
        if(value.targetEditIndex === undefined){
          if(value.targetEditObj && !['legendList'].includes(value.targetEditObj)){
            templateParam[value.targetEditObj] = value[value.targetEditObj]
          }
        }else if(value.targetEditIndex === 'all'){
          for(let i = 0; i < value.checkData.length ; i++){
            if(templateParam.checkData[i] === undefined) templateParam.checkData[i] = {}
            templateParam.checkData[i] = {
              ...templateParam.checkData[i],
              id:value.checkData[i].id,
              [value.targetEditObj]:value.checkData[i][value.targetEditObj]
            }
          }
          templateParam.allData[value.targetEditObj] = value.allData[value.targetEditObj]
        }else if(value.targetEditIndex !== 'legendEditName'){
            let haveIndex = templateParam.checkData.findIndex(item => item.id === value.checkData[value.targetEditIndex].id)
            if(haveIndex === -1){
              templateParam.checkData.push({id:value.checkData[value.targetEditIndex].id})
              haveIndex = templateParam.checkData.length - 1
            }
            // 如果是同步标签，则需要同步property中的所有属性
            if(value.targetEditObj === 'synchronization'){
              const property = ['maxPoint','minPoint','connectNulls','symbol','symbolSize','itemColor','lineType','lineWidth','lineColor','synchronization']
              for(let i = 0; i < property.length ; i++){
                templateParam.checkData[haveIndex][property[i]] = value.checkData[value.targetEditIndex][property[i]]
              }
            }else{
              templateParam.checkData[haveIndex][value.targetEditObj] = value.checkData[value.targetEditIndex][value.targetEditObj]
            }
          }
      }

      // 数据重置
      if(!isEdit){
        if (value.targetResetIndex === undefined) {
          this.editData[this.editObj][value.targetResetObj] = value[value.targetResetObj]
        } else if (value.targetResetIndex === 'all' || value.targetResetIndex === '"synchronization"' || value.targetResetIndex === 'legendEditName') {
        } else {
          this.editData[this.editObj].series[value.targetResetIndex][value.targetResetObj] = value.checkData[value.targetResetIndex][value.targetResetObj]
        }

        if(value.targetResetIndex === undefined && ['legendList'].includes(value.targetEditObj)){
          templateParam[value.targetResetObj] = value[value.targetResetObj]
        }
      }

      if(!isEdit){
        if(value.targetResetIndex === undefined){
          if(value.targetResetObj && !['legendList'].includes(value.targetResetObj)){
            delete templateParam[value.targetResetObj]
          }
        }else if(value.targetResetIndex === 'all'){
          for(let i = 0; i < value.checkData.length ; i++){
            delete templateParam.checkData[i][value.targetResetObj]
          }
          delete templateParam.allData[value.targetResetObj]
        }else{
          let haveIndex = templateParam.checkData.findIndex(item => item.id === value.checkData[value.targetResetIndex].id)
          delete templateParam.checkData[haveIndex][value.targetResetObj]
        }
      }

      // 执行内容更改
      this.handleChartContent(value,isEdit)

      
      // 图例-数据
      if(value.targetEditObj === 'legendList'){
        templateParam.legendData.legendIndeterminate = value.legendIndeterminate
        templateParam.legendData.checkAll = value.checkAll
        templateParam.legendData.legendList = value.legendList
        templateParam.legendData.legendOptions = templateParam.legendData.legendOptions ?? this.originalLegent[this.editObj]
      }

      // 图例-名称
      if(value.targetEditObj === 'legendEditName'){
        templateParam.legendData.legendList = value.legendList // 需同步更名后的数组
        templateParam.legendData.legendEditName = value.legendEditName 
        templateParam.legendData.legendRevealList = value.legendRevealList 

        // 找到改名的那根线，存储修改后的线名称
        const haveIndex =  templateParam.checkData.findIndex(item => item.id === value.checkData[value.targetEditIndex].id)
        if(haveIndex === -1){
          templateParam.checkData.push({id:value.checkData[value.targetEditIndex].id,name:value.checkData[value.targetEditIndex].name})
        }else{
          templateParam.checkData[haveIndex].name=value.checkData[value.targetEditIndex].name
        }
      }

      // 图例-排序
      if(value.targetEditObj === 'legendSort'){
        templateParam.legendData.legendSort = value.legendSort 
      }

       // 图例-显隐
      if(value.targetEditObj === 'legendRevealList'){
        templateParam.legendData.legendRevealIndeterminate = value.legendRevealIndeterminate
        templateParam.legendData.legendRevealcheckAll = value.legendRevealcheckAll
        templateParam.legendData.legendRevealList = value.legendRevealList
        templateParam.legendData.legendRevealOptions = templateParam.legendData.legendRevealOptions
      }

      // 记录数据到后端
      let chartTemplateParams = {}
      // 如果有templateId，则走修改路线，如果没有则走新建路线
      if(!this.reportChartTemplateList[this.editObj].templateId){
        chartTemplateParams = {
          templateName:'报告ID修改默认模板',
          originalParamJson:JSON.stringify(this.originalData[this.editObj]),
          templateParamJson:JSON.stringify(this.reportChartTemplateList[this.editObj].templateParamJson),
          reportId:this.$route.query.id,
          targetChart:this.editObj
        }

        this.reportChartTemplateList[this.editObj].originalParamJson = this.originalData[this.editObj]
        this.saveChartTemplate(chartTemplateParams)
      }else{
        chartTemplateParams = {
          id:this.reportChartTemplateList[this.editObj].templateId,
          templateParamJson:JSON.stringify(this.reportChartTemplateList[this.editObj].templateParamJson),
        }

        this.updateChartTemplate(chartTemplateParams)
      }
    },

    handleChartContent(value,isEdit){
      const titleOptions = ['chartTitle', 'titleTop']
      const xAxisOptions = ['XTitle', 'xType', 'xMin', 'xMax', 'xInterval']
      const yAxisOptions = ['YTitle', 'YTitle2', 'yType', 'yType2', 'yMin', 'yMin2', 'yMax', 'yMax2', 'yInterval', 'yInterval2', 'yTitleLetf', 'yTitleRight']
      const gridOptions = ['gridTop', 'gridLeft', 'gridRight', 'gridBottom']
      const legendOptions = ['legendBgColor', 'legendOrient', 'legendTop', 'legendLeft', 'legendWidth', 'legendHeight', 'legendGap']
      const legendListOptions = ['legendList', 'legendEditName', 'legendSort', 'legendRevealList']
      const dataLabelOptions = ['maxPoint', 'minPoint', 'connectNulls', 'symbol', 'symbolSize', 'itemColor', 'lineType', 'lineWidth', 'lineColor']

      let targetObj = isEdit ? value.targetEditObj : value.targetResetObj  // 需要修改或者重置的目标值
      let targetContentText = isEdit ? 'targetEditObj' : 'targetResetObj'   // 目标内容文本

      if (legendListOptions.includes(value.targetEditObj)) {
        return this._handleEchartsLegend(value.targetEditObj, value[value.targetEditObj])
      }

      // 标题 修改 targetEditObj  // 标题  重置 targetResetObj
      if (titleOptions.includes(value[targetContentText])) {
        if (value[targetContentText] === 'chartTitle') targetObj = 'text'
        if (value[targetContentText] === 'titleTop') targetObj = 'top'
        return this._handleEchartsNormal('title', targetObj, value[value[targetContentText]])
      }

      // 图例 修改 targetEditObj  // 图例  重置 targetResetObj
      if (legendOptions.includes(value[targetContentText])) {
        if (value[targetContentText] === 'legendBgColor') targetObj = 'backgroundColor'
        if (value[targetContentText] === 'legendOrient') targetObj = 'orient'
        if (value[targetContentText] === 'legendTop') targetObj = 'top'
        if (value[targetContentText] === 'legendLeft') targetObj = 'left'
        if (value[targetContentText] === 'legendWidth') targetObj = 'itemWidth'
        if (value[targetContentText] === 'legendHeight') targetObj = 'itemHeight'
        if (value[targetContentText] === 'legendGap') targetObj = 'itemGap'
        return this._handleEchartsNormal('legend', targetObj, value.targetResetObj === 'legendLeft' ? 'center' : value[value[targetContentText]])
      }

      // X轴 修改 targetEditObj  // X轴  重置 targetResetObj
      if (xAxisOptions.includes(value[targetContentText])) {
        if (value[targetContentText] === 'XTitle') targetObj = 'name'
        if (value[targetContentText] === 'xType') targetObj = 'type'
        if (value[targetContentText] === 'xMin') targetObj = 'min'
        if (value[targetContentText] === 'xMax') targetObj = 'max'
        if (value[targetContentText] === 'xInterval') targetObj = 'interval'
        return this._handleEchartsXAxis(targetObj, value[value[targetContentText]])
      }

      // Y轴 修改 targetEditObj  // Y轴  重置 targetResetObj
      if (yAxisOptions.includes(value[targetContentText])) {
        if (value[targetContentText] === 'YTitle' || value[targetContentText] === 'YTitle2') targetObj = 'name'
        if (value[targetContentText] === 'yTitleLetf' || value[targetContentText] === 'yTitleRight') targetObj = 'nameGap'
        if (value[targetContentText] === 'yType' || value[targetContentText] === 'yType2') targetObj = 'type'
        if (value[targetContentText] === 'yMin' || value[targetContentText] === 'yMin2') targetObj = 'min'
        if (value[targetContentText] === 'yMax' || value[targetContentText] === 'yMax2') targetObj = 'max'
        if (value[targetContentText] === 'yInterval' || value[targetContentText] === 'yInterval2') targetObj = 'interval'
        return this._handleEchartsYAxis(value[targetContentText], targetObj, value[value[targetContentText]])
      }

      // 数据标签 修改 targetEditObj // 数据标签  重置 targetResetObj
      if (value.targetEditIndex === 'all' || value.targetResetIndex === 'all') {
        if (value[targetContentText] === 'lineType') targetObj = 'type'
        if (value[targetContentText] === 'lineWidth') targetObj = 'width'
        if (value[targetContentText] === 'itemColor' || value[targetContentText] === 'lineColor') targetObj = 'color'
        return this._handleEchartsAllDataLabel(value[targetContentText], targetObj, value.targetEditIndex === 'all' ? value.allData[value[targetContentText]] : 'resetAll')
      }
      if (dataLabelOptions.includes(value[targetContentText])) {
        if (value[targetContentText] === 'maxPoint') targetObj = 'max'
        if (value[targetContentText] === 'minPoint') targetObj = 'min'
        if (value[targetContentText] === 'lineType') targetObj = 'type'
        if (value[targetContentText] === 'lineWidth') targetObj = 'width'
        if (value[targetContentText] === 'itemColor' || value[targetContentText] === 'lineColor') targetObj = 'color'

        return this._handleEchartsDataLabel(value[targetContentText], targetObj, value.checkData[isEdit ? value.targetEditIndex : value.targetResetIndex][value[targetContentText]], isEdit ? value.targetEditIndex : value.targetResetIndex)
      }

      if (value.targetEditObj === 'synchronization') {
        return this._handleEchartsSynchronization(value.targetEditIndex, value.checkData[value.targetEditIndex].synchronization)
      }

      // 图表位置 修改 targetEditObj  // 图表位置  重置 targetResetObj
      if (gridOptions.includes(value[targetContentText])) {
        if (value[targetContentText] === 'gridTop') targetObj = 'top'
        if (value[targetContentText] === 'gridLeft') targetObj = 'left'
        if (value[targetContentText] === 'gridRight') targetObj = 'right'
        if (value[targetContentText] === 'gridBottom') targetObj = 'bottom'
        return this._handleEchartsNormal('grid', targetObj, value[value[targetContentText]])
      }

      // 修改图例名称
      if (this.editData[this.editObj].legendNameType !== value.legendNameType) {
        this.drawerVisible = false
        this.fristInit[this.editObj] = true
        this.titleDataObj[this.editObj].legendNameType = value.legendNameType
        this.handleInitChart(this.editObj)
      }
    },

    _handleEchartsLegend(targetObj, targetValue, targetIndex) {
      const option = { legend: {}, series: [] }

      let legend = []

      // 名称
      if (targetObj === 'legendEditName') {
        this.editData[this.editObj].legendSort.forEach(v => {
          // 处理 legend
          // 条件1 : 数据勾选
          const conditions1 = this.editData[this.editObj].legend.includes(v)
          // 条件2 : 显隐勾选
          const conditions2 = this.editData[this.editObj].legendRevealList.includes(v)
          // 是否改名 如果改了名就用改的名，如果没改，就用原来的名
          const name = this.editData[this.editObj].legendEditName.filter(filterItem => filterItem.newName && (v === filterItem.originName))
          if (conditions1 && conditions2) legend.push(name.length === 0 ? v : name[0].newName)
        })

        _.cloneDeep(this.editData[this.editObj].editSeries).forEach((v, vIndex) => {
          // 是否改名
          const name = this.editData[this.editObj].legendEditName.filter(filterItem => filterItem.newName && (v.name === filterItem.originName))
          option.series[vIndex] = v
          option.series[vIndex].name = name.length === 0 ? v.name : name[0].newName
        })

        option.legend.data = legend
        return this.echartObj[this.editObj].setOption(option)
      }

      // 数据
      if (targetObj === 'legendList') {
        const seriesList = []
        this.editData[this.editObj].legendSort.forEach(v => {
          // 处理 series
          if (targetValue.includes(v)) {
            let haveList = this.editData[this.editObj].editSeries.filter(filterItem => filterItem.name === v)
            if (haveList.length === 0) {
              haveList = _.cloneDeep(this.originalData[this.editObj].originalSeries).filter(filterItem => filterItem.name === v)
              // 同步之前的修改样式
              haveList.forEach(forItem => {
                const have = this.originalData[this.editObj].series.filter(filterItem => filterItem.id === forItem.id)
                console.log(have)
                forItem.symbol = have[0].symbol
                forItem.symbolSize = have[0].symbolSize

                forItem.connectNulls = Boolean(Number(have[0].connectNulls))

                forItem.itemStyle.color = have[0].itemColor

                forItem.lineStyle.type = have[0].lineType
                forItem.lineStyle.width = have[0].lineWidth
                forItem.lineStyle.color = have[0].lineColor

                forItem.markPoint = { data: [] }
                if (have[0].maxPoint) forItem.markPoint.data.push({ type: "max", name: "Max" })
                if (have[0].minPoint) forItem.markPoint.data.push({ type: "min", name: "Min" })
              })
            }

            seriesList.push(...haveList)
          }
          // 处理 legend
          // 条件1 : 数据勾选
          const conditions1 = targetValue.includes(v)
          // 条件2 : 显隐勾选
          const conditions2 = this.editData[this.editObj].legendRevealList.includes(v)

          const name = this.editData[this.editObj].legendEditName.filter(filterItem => filterItem.originName === v && filterItem.newName)
          if (conditions1 && conditions2) legend.push(name.length === 0 ? v : name[0].newName)
        })
        this.editData[this.editObj].legend = targetValue

        this.editData[this.editObj].editSeries = _.cloneDeep(seriesList)   //这个需要保留原值

        // 是否改名
        seriesList.forEach((v, vIndex) => {
          const name = this.editData[this.editObj].legendEditName.filter(filterItem => filterItem.originName === v.name && filterItem.newName)
          seriesList[vIndex].name = name.length === 0 ? v.name : name[0].newName
        })


        // 重新生成图表
        if (this.echartObj[this.editObj]) this.echartObj[this.editObj].dispose();
        this.echartObj[this.editObj] = this.echarts.init(['cycCap','cycEng','rptCap','rptEng'].includes(this.editObj) ? document.getElementById(this.editObj) : this.$refs[this.editObj], 'walden', { devicePixelRatio: 2 })
        
        let chartOption = ['capacity','tempRise','capacityVoltage'].includes(this.editObj) 
                          ?  this._handleCRateEchartOptions(this.editObj, seriesList)
                          : ['cycCap','cycEng','rptCap','rptEng'].includes(this.editObj) ? this._handleCycleEchartOptions(this.editObj, this.xAxisType, seriesList) : this._handleCycleEchartOptions(this.editObj,seriesList)

        chartOption.legend.data = legend
        return this.echartObj[this.editObj].setOption(chartOption)
      }

      // 排序 
      if (targetObj === 'legendSort') {
        this.editData[this.editObj].legendSort.forEach(v => {
          // 是否改名
          const name = this.editData[this.editObj].legendEditName.filter(filterItem => filterItem.originName === v && filterItem.newName)
          if (this.editData[this.editObj].legendRevealList.includes(v)) legend.push(name.length === 0 ? v : name[0].newName)
        })
      }

      // 显隐
      if (targetObj === 'legendRevealList') {
        this.editData[this.editObj].legendSort.forEach(v => {
          // 是否改名
          const name = this.editData[this.editObj].legendEditName.filter(filterItem => filterItem.originName === v && filterItem.newName)
          if (targetValue.includes(v)) legend.push(name.length === 0 ? v : name[0].newName)
        })
      }

      option.legend.data = legend
      this.echartObj[this.editObj].setOption(option)
    },
    _handleEchartsNormal(editObj, targetObj, targetValue) {
      this.echartObj[this.editObj].setOption({
        [editObj]: {
          [targetObj]: targetValue
        }
      })
    },
    _handleEchartsXAxis(targetObj, targetValue) {
      const option = { xAxis: [{}] }
      // 保留设置用户上一次留下的值,最大最小以及间隔值
      if (targetObj === 'type' && targetValue === 'value' && this.editData[this.editObj].xInterval) {
        option.xAxis[0].min = this.editData[this.editObj].xMin
        option.xAxis[0].max = this.editData[this.editObj].xMax
        option.xAxis[0].interval = this.editData[this.editObj].xInterval
      }
      option.xAxis[0][targetObj] = targetValue;

      // 样式
      option.xAxis[0].axisTick = { show: false }
      option.xAxis[0].axisLabel = { show: true, width: 0.5, fontSize: "15", color: "#000000" }
      option.xAxis[0].axisLine = { show: true, lineStyle: { color: "#ccc", width: 0.5 } }
      option.xAxis[0].splitLine = { show: true, lineStyle: { type: "solid", width: 0.5 } }
      option.xAxis[0].nameTextStyle = { fontSize: 14, fontWeight: 500, color: "#000000" }
      option.xAxis[0].nameLocation = 'middle'
      option.xAxis[0].nameGap = 30
      option.xAxis[0].boundaryGap = false

      if (targetObj !== 'name') option.xAxis[0].name = this.editData[this.editObj].XTitle



      this.echartObj[this.editObj].setOption(option)

      // X轴首次由类目轴修改为数值轴，获取最大最小值、间隔
      if (targetObj === 'type' && targetValue === 'value' && !this.editData[this.editObj].xInterval) {
        const XAxis = this.echartObj[this.editObj].getModel().getComponent("xAxis").axis.scale

        this.editData[this.editObj].xMin = XAxis._extent[0]
        this.editData[this.editObj].xMax = XAxis._extent[1]
        this.editData[this.editObj].xInterval = XAxis._interval

        this.originalData[this.editObj].xMin = XAxis._extent[0]
        this.originalData[this.editObj].xMax = XAxis._extent[1]
        this.originalData[this.editObj].xInterval = XAxis._interval
      }
    },
    _handleEchartsYAxis(originalValue, targetObj, targetValue) {
      const option = { yAxis: [{}, { axisLine: { show: true, lineStyle: { color: "#ccc", width: 0.5 } } }] }
      const yAxisOptions = ['YTitle', 'yType', 'yMin', 'yMax', 'yInterval', 'yTitleLetf']
      const targetIndex = yAxisOptions.includes(originalValue) ? 0 : 1
      // 保留设置用户上一次留下的值,最大最小以及间隔值
      if (targetObj === 'type' && targetValue === 'value') {
        option.yAxis[targetIndex].min = this.editData[this.editObj][`yMin${yAxisOptions.includes(originalValue) ? '' : '2'}`]
        option.yAxis[targetIndex].max = this.editData[this.editObj][`yMax${yAxisOptions.includes(originalValue) ? '' : '2'}`]
        option.yAxis[targetIndex].interval = this.editData[this.editObj][`yInterval${yAxisOptions.includes(originalValue) ? '' : '2'}`]
      }
      option.yAxis[targetIndex][targetObj] = targetValue;

      // 样式
      option.yAxis[targetIndex].axisTick = { show: targetObj ? false : true }
      option.yAxis[targetIndex].axisLabel = { show: true, width: 0.5, fontSize: "15", color: "#000000" }
      option.yAxis[targetIndex].axisLine = { show: true, lineStyle: { color: "#ccc", width: 0.5 } }
      option.yAxis[targetIndex].splitLine = { show: true, lineStyle: { type: "solid", width: 0.5 } }
      option.yAxis[targetIndex].nameTextStyle = { fontSize: 14, fontWeight: 500, color: "#000000" }
      option.yAxis[targetIndex].nameLocation = 'middle'
      option.yAxis[targetIndex].nameGap = this.editData[this.editObj][`${yAxisOptions.includes(originalValue) ? 'yTitleLetf' : 'yTitleRight'}`]
      option.yAxis[targetIndex].boundaryGap = false

      if (targetObj !== 'name') option.yAxis[targetIndex].name = this.editData[this.editObj][`YTitle${yAxisOptions.includes(originalValue) ? '' : '2'}`]

      this.echartObj[this.editObj].setOption(option)
    },
    _handleEchartsDataLabel(originalValue, targetObj, targetValue, targetIndex) {
      const option = { series: [] }
      const arr = ['connectNulls', 'symbol', 'symbolSize']
      const arr1 = ['lineWidth', 'lineType', 'lineColor']

      // 找到要修改的线的位置
      const haveIndex = this.editData[this.editObj].editSeries.findIndex(findItem => findItem.id === this.editData[this.editObj].originalSeries[targetIndex].id)

      // 拿到要修改的那根线
      const newSeries = _.cloneDeep(this.editData[this.editObj].editSeries[haveIndex])

      // 最大值、最小值
      if ((originalValue === 'maxPoint' || originalValue === 'minPoint') && targetValue) {
        newSeries.markPoint.data.push({ type: targetObj, name: targetObj })
      }
      if ((originalValue === 'maxPoint' || originalValue === 'minPoint') && !targetValue) {
        const haveIndex1 = newSeries.markPoint.data.findIndex(findItem => findItem.type === targetObj)
        newSeries.markPoint.data.splice(haveIndex1, 1)
      }

      // 连线、折点类型、折点大小
      if (arr.includes(originalValue)) {
        newSeries[targetObj] = targetObj === 'connectNulls' ? Boolean(Number(targetValue)) : targetValue
      }

      // 折点颜色
      if (originalValue === 'itemColor') {
        newSeries.itemStyle[targetObj] = targetValue
      }

      // 折线类型、折线宽度、折现颜色
      if (arr1.includes(originalValue)) {
        newSeries.lineStyle[targetObj] = targetValue
      }

      option.series[targetIndex] = newSeries;
      this.editData[this.editObj].editSeries[haveIndex] = newSeries
      this.echartObj[this.editObj].setOption(option)
    },
    _handleEchartsSynchronization(originalValue, targetValue) {
      // originalValue 要修改的原始值   targetValue 需要修改过后的值
      const option = { series: [] }

      const selectIndex = this.editData[this.editObj].editSeries.findIndex(findItem => findItem.id === this.editData[this.editObj].originalSeries[originalValue].id)
      const templateIndex = this.editData[this.editObj].editSeries.findIndex(findItem => findItem.id === this.editData[this.editObj].originalSeries[targetValue].id)

      const newSeries = { ...this.editData[this.editObj].editSeries[selectIndex] }
      const templateSeries = _.cloneDeep(this.editData[this.editObj].editSeries[templateIndex])
      const arr = ['symbol', 'symbolSize', 'markPoint', 'lineStyle', 'itemStyle', 'connectNulls']  // 需要修改的值

      Object.keys(templateSeries).forEach(key => {
        if (arr.includes(key)) {
          newSeries[key] = key === 'connectNulls' ? Boolean(Number(templateSeries[key])) : templateSeries[key]

          if (key === 'lineStyle') {
            this.editData[this.editObj].series[selectIndex].lineType = this.editData[this.editObj].series[templateIndex].lineType
            this.editData[this.editObj].series[selectIndex].lineWidth = this.editData[this.editObj].series[templateIndex].lineWidth
            this.editData[this.editObj].series[selectIndex].lineColor = this.editData[this.editObj].series[templateIndex].lineColor
          } else if (key === 'itemStyle') {
            this.editData[this.editObj].series[selectIndex].itemColor = this.editData[this.editObj].series[templateIndex].itemColor
          } else {
            this.editData[this.editObj].series[selectIndex][key] = this.editData[this.editObj].series[templateIndex][key]
          }
        }
      });
      // 最大最小值
      this.editData[this.editObj].series[selectIndex].maxPoint = this.editData[this.editObj].series[templateIndex].maxPoint
      this.editData[this.editObj].series[selectIndex].minPoint = this.editData[this.editObj].series[templateIndex].minPoint

      option.series[selectIndex] = newSeries;
      this.editData[this.editObj].editSeries[selectIndex] = newSeries
      this.echartObj[this.editObj].setOption(option)

    },
    _handleEchartsAllDataLabel(originalValue, targetObj, targetValue) {

      const option = { series: [] }
      const arr = ['lineWidth', 'lineType', 'lineColor']

      // 拿到所有的线（有层级）
      const newSeries = [...this.editData[this.editObj].editSeries]  

      // 修改值
      newSeries.forEach((v, index) => {
        if (arr.includes(originalValue)) {
          v.lineStyle[targetObj] = targetValue == 'resetAll' ? this.originalData[this.editObj].originalSeries.filter(filterItem => filterItem.id === v.id)[0].lineStyle[targetObj] : targetValue
        } else if (originalValue === 'itemColor') {
          v.itemStyle[targetObj] = targetValue == 'resetAll' ? this.originalData[this.editObj].originalSeries.filter(filterItem => filterItem.id === v.id)[0].itemStyle[targetObj] : targetValue
        } else {
          v[targetObj] = targetValue == 'resetAll' ? this.originalData[this.editObj].originalSeries.filter(filterItem => filterItem.id === v.id)[0][targetObj] : targetValue
        }

        // 判断是否改名
        const currentNameList = this.editData[this.editObj].legendEditName.filter(item =>{
          const idLength = item.id.split('').length 
          const newId =  v.id.slice(0,idLength)
          return item.id === newId
        })

         // 需同步最新的名字
        v.name = currentNameList[0]?.newName ? currentNameList[0].newName : v.name

        // 编辑数据更新
        this.editData[this.editObj].series[index][originalValue] = targetValue == 'resetAll' ? this.originalData[this.editObj].series.filter(filterItem => filterItem.id === v.id)[0][originalValue] : targetValue
      })

      option.series = newSeries;
      this.echartObj[this.editObj].setOption(option)
    },

    // 缩略图
    initThumbnailEchart(targetObj, cyclicListName = null, chartListName = null) {
      // cyclicListName / chartListName  
      const thumbnailEchart = this.echarts.init(document.getElementById(`${targetObj}Thumbnail`))
      let cyclicList = cyclicListName === null ? _.cloneDeep(this.allDataJson[`${targetObj}EchartsDataList`]) : _.cloneDeep(this.allDataJson[cyclicListName]) // this.allDataJson.capacityEchartsDataList

      let chartList = []   // capacityVoltage
      if (chartListName !== null) chartList = _.cloneDeep(this.allDataJson[chartListName])

      let series = []

      // 循环
      if(['disChargeCapacity','capacityHoldRate','disChargeEnergy','energyHoldRate'].includes(targetObj)){
        series = this._getCycleThumbnailSeries(cyclicList, chartList)
      }

      // 倍率
      if(['capacity','tempRise','capacityVoltage'].includes(targetObj)){
        series = this._getCRateThumbnailSeries(cyclicList, 'data', 'curAndBatNo', chartList.length > 0 ? '-Temp' : '')
        if (chartList.length > 0) series.push(...this._getCRateThumbnailSeries(cyclicList, 'data', 'curAndBatNo', '-Voltage'))
      }

      const options = {
        grid: {
          show: true,
          top: 1,
          left: 1,
          right: 1,
          bottom: 4,
        },
        xAxis: {
          type: 'value',
          axisLabel: { show: false },
          axisTick: { show: false }
        },
        yAxis: {
          type: 'value',
          axisLabel: { show: false },
          axisTick: { show: false }
        },
        series
      }
      thumbnailEchart.setOption(options)
    },
    
    
  }
}

