<template>
	<div>
		<a-modal title="选择物料" :width="500" :visible="selectvisible" @ok="handleSubmit" @cancel="handleCancel">
			<v-selectpage ref="sp" v-model="sapNumber" :page-size="6" :data="parts" key-field="sapNumber" show-field="sapNumber" :tb-columns="vcolumns">
			</v-selectpage>
		</a-modal>
		<a-button type="primary" @click="addBom" style="margin-bottom:8px;margin-right:8px">
			新增包装BOM
		</a-button>
		<a-drawer placement="right" :closable="false" width="80%" :visible="visible1" @close="onClose1" :destroyOnClose="true">
			<checkhistory :param="param"></checkhistory>
		</a-drawer>

		<a-drawer
		placement="right"
		:closable="false"
		width="80%"
		:visible="visible2"
		@close="onClose2"
		:bodyStyle="{ height: '100%' }"
		>
		<iframe  :src="pdfUrl+'#view=FitH,top'" width="100%" height="100%" ></iframe>
		</a-drawer>

		<a-spin :spinning="vloading">
			<a-table size="small" ref="wearktable" :columns="wearklinecolumns" :dataSource="wearkloadData" :scroll="{y: windowHeight }" :rowKey="(record) => record.id" :pagination="false" :showPagination="false">
        		<div @preview="preview"></div>
				<span slot="bomStatus" slot-scope="text, record">
									
									<a-dropdown>
										<a class="ant-dropdown-link">
											{{mapStatus[record.bomStatus]}}<a-icon type="down" />
										</a>
										<a-menu v-if="record.bomStatus == 1" slot="overlay">
											<a-menu-item>
												<a @click="editBom(record)" >查看</a>
											</a-menu-item>

											<a-menu-item>
												<a @click="$refs.checkhistory2.edit(record)" >审核记录</a>

											</a-menu-item>
											<a-menu-item>
												<a @click="$refs.bomhistory.edit(record)">变更记录</a>
											</a-menu-item>
										</a-menu>

										<a-menu v-if="record.bomStatus == 0 || record.bomStatus == 4" slot="overlay">
											<a-menu-item>
												<a @click="editBom(record)" >编辑</a>
											</a-menu-item>
											<a-menu-item>
												<a @click="$refs.bomupgrade.edit(record)">提交</a>
											</a-menu-item>

											<a-menu-item>
												<a @click="$refs.checkhistory2.edit(record)" >审核记录</a>

											</a-menu-item>
											<a-menu-item>
												<a @click="$refs.bomhistory.edit(record)">变更记录</a>
											</a-menu-item>
										</a-menu>

										<a-menu v-if="record.bomStatus == 2" slot="overlay">
											<a-menu-item>
												<a @click="editBom(record)" >查看</a>
											</a-menu-item>
											<a-menu-item>
												<a @click="callBomUpgrade(record)">修订</a>
											</a-menu-item>
											<a-menu-item>
												<a @click="$refs.checkhistory2.edit(record)" >审核记录</a>

											</a-menu-item>
											<a-menu-item>
												<a @click="$refs.bomhistory.edit(record)">变更记录</a>
											</a-menu-item>
										</a-menu>

										<a-menu v-if="record.bomStatus == 3" slot="overlay">
											<a-menu-item>
												<a @click="editBom(record)" >查看</a>
											</a-menu-item>
											<!--<a-menu-item>
												<a @click="$refs.bomupgrade.edit(record)">重试</a>
											</a-menu-item>-->
											<a-menu-item>
												<a @click="$refs.checkhistory2.edit(record)" >审核记录</a>
											</a-menu-item>
											<a-menu-item>
												<a @click="$refs.bomhistory.edit(record)">变更记录</a>
											</a-menu-item>
										</a-menu>

									</a-dropdown>
				</span>
			</a-table>
	</a-spin>
	<bomupgrade ref="bomupgrade" @ok="handleOk" />
	<bomhistory ref="bomhistory" @ok="handleOk" />
	<checkhistory2 ref="checkhistory2" @ok="handleOk" />
</div>
</template>

<script>
	import { SelectPage } from 'v-selectpage'
	import {
		STable
	} from '@/components'
	import {
		getwerklines,
		getBomList,
		sysBomAdd,
		sysBomUpgrade,
	} from "@/api/modular/system/bomManage"
	import bomupgrade from './bomupgrade'
	import checkhistory from './checkhistory'
	import bomhistory from './bomhistory'
	import checkhistory2 from './checkhistory2'
	export default {
		components: {
			STable,
			bomupgrade,
			checkhistory,
			bomhistory,
      		checkhistory2,
			'v-selectpage': SelectPage
		},
		props: {
			parts: {
				type: Array,
				default: () => []
			}
		},
		data() {
			return {
				selectvisible: false,
				sapNumber:'',
				vcolumns:[
					{
						title:'物料代码',
						data:'sapNumber'
					},
					{
						title:'物料',
						data:'partName'
					},
				],
        		pdfUrl:'',
				visible1: false,
				visible2: false,
				mapStatus: ['编辑中', '审核中', '已审核', '失败中','被驳回'],
				wearklinequeryParam: {
					//bomIssueId: this.issueId,
                    bomType:1
				},
				//dataLines: {},
				windowHeight: document.documentElement.clientHeight - 265,
				vloading: false,
				loading: false,
				wearklinecolumns: [{
						title: '序号',
						dataIndex: 'index',
						key: 'index',
						align: 'center',
						customRender: (text, record, index) => `${index+1}`,
					},
          {
            title: '包装BOM编号',
            dataIndex: 'bomNo',
            width: 180,
          },
					{
						title: '包装BOM',
						dataIndex: 'bomStatus',
						scopedSlots: {
							customRender: 'bomStatus'
						}
					},


					{
						title: 'BOM版本',
						dataIndex: 'bomVersion',
					},
				],
				wearkloadData: [],
				werklines: {},
				historyBomId: '',
				param: {}
			}
		},
		created() {
			this.callBomList()
			//this.callWerkLines()
		},
		methods: {
			callBomUpgrade(record) {
				this.vloading = true
				sysBomUpgrade({
					id: record.id
				}).then((res) => {
					if (res.success) {

						let index = this.wearkloadData.findIndex(item => item.id == record.id)
						this.wearkloadData[index].bomStatus = 0

						this.$message.success('修订成功')
					} else {
						this.$message.error('修订失败：' + res.message)
					}
					this.vloading = false
				}).finally((res) => {
					this.vloading = false
				})
			},
			handleOk() {},
			/* callPartList() {
				this.vloading = true
				getPartList({
						flag: 0
					})
					.then((res) => {
						if (res.success) {
							this.parts = res.data
						} else {
							this.$message.error(res.message, 1);
						}
						this.vloading = false
					})
					.catch((err) => {
						this.vloading = false
						this.$message.error('错误提示：' + err.message, 1)
					});
			}, */
			/* callWerkLines() {
				this.confirmLoading = true
				getwerklines().then((res) => {
					if (res.success) {
						let mapline = {}
						for (var key in res.data) {
							for (const _item of res.data[key]) {
								mapline[_item.id] = _item.namecode ? _item.namecode + '->' + _item.lineName : _item.werkNo+'->' + _item.lineName
							}
						}
						this.dataLines = mapline
						this.werklines = res.data;
					} else {
						this.$message.error(res.message)
					}
					this.confirmLoading = false
				}).catch((err) => {
					this.$message.error('错误：' + err.message)
					this.confirmLoading = false
				})
			}, */
			callBomList() {
				this.vloading = true
				getBomList(this.wearklinequeryParam).then((res) => {
					if (res.success) {
						this.wearkloadData = res.data;
					} else {
						this.$message.error(res.message)
					}
					this.vloading = false
				}).catch((err) => {
					this.$message.error('错误：' + err.message)
					this.vloading = false
				})
			},
			editBom(record) {
				this.$emit('onUpdate', '6', record.id,'1')
			},
			preview(id,processId) {
				this.historyBomId = id
        		this.param.historyBomId = id
        		this.param.processId = processId
				this.visible1 = !this.visible1
			},
			previewPdf(id) {
				this.pdfUrl = process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id='+  id
				this.visible2 = !this.visible2
			},
			onClose1() {
				this.visible1 = false;
			},
			onClose2() {
				this.visible2 = false;
			},
			addBom() {
				this.selectvisible = true
			},
			handleCancel() {
				this.$refs.sp.remove()
				this.sapNumber = null
				this.selectvisible = false
			},
			handleSubmit() {
				if (!this.sapNumber || this.sapNumber == '') {
					this.$message.error('请选择物料')
					return false
				}
				this.vloading = true
				sysBomAdd({
						bomPartName:this.sapNumber,
						bomType:1
					})
					.then((res) => {
						if (res.success) {
							this.wearkloadData.push(res.data)
							this.selectvisible = false
							this.sapNumber = ''
							this.$refs.sp.remove()
						} else {
							this.$message.error(res.message, 1);
						}
						this.vloading = false
					})
					.catch((err) => {
						this.vloading = false
						this.$message.error('错误提示：' + err.message, 1)
					});
			}
		},
		watch: {}
	}
</script>

<style>

</style>