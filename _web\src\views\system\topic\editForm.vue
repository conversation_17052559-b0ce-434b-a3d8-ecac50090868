<template>
  <a-modal  :width="600"  style="overflow-y: scroll"
           :visible="visible" :confirmLoading="confirmLoading"
           :bodyStyle="{overflowY: 'scroll',height:clientHeight - 220 + 'px'}"

           @ok="handleSubmit" @cancel="handleCancel">
    <template slot="title">
        <div style="font-size: 20px;font-weight: bolder;">修改立项申请信息</div>
      </template>

    <template slot="footer">
      <a-button key="back" @click="handleCancel">
        取消
      </a-button>

      <a-popconfirm key="submit" placement="topRight" title="请确认立项申请信息是否无误，点击确定后将提交立项审批流程"
                    :visible="visible1"
                    @cancel="handleCancel"
                    @confirm="handleSubmit" >
        <a-button type="primary" @click="handleSubmitBefore" :loading ="visible2">
          确认
        </a-button>
      </a-popconfirm>

    </template>



    <div style="font-size: 20px;font-weight: bolder;">1、技术课题信息输入</div>

    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-row :gutter="24">
          <a-col :md="20" :sm="24">
            <a-form-item label="课题分类" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input type='hidden'  v-decorator="['projectCate', {rules: [{required: true, message: '请选择课题分类!'}]}]" />
              <a-tree-select 
                                    labelInValue
                                     :autoExpandParent="false"
                                     :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                                     @change="selectTree"
                                     :value="treeValue"
                                     :tree-data="cate" placeholder="请选择分类" >
                      </a-tree-select>
            </a-form-item>
          </a-col>

        </a-row>

        <a-row :gutter="24">

          <a-col :md="20" :sm="24">
            <a-form-item label="课题名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input  placeholder="请输入课题名称" v-decorator="['projectName', {rules: [{required: true, message: '请输入课题名称!'}]}]"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
<!--
        <a-row :gutter="24">

          <a-col :md="20" :sm="24">
            <a-form-item label="平台/课题" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback >
              <a-select v-decorator="['platformAndTopic', {rules: [{required: true, message: '请选择平台/课题！'}]}]"
                        @change="checklevel"
                        placeholder="请选择课题分类">
                <a-select-option value="1">
                  平台
                </a-select-option>
                <a-select-option value="2">
                  课题
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>



        </a-row>-->

        <a-row :gutter="24"  v-if="showLevel">

          <a-col :md="20" :sm="24">
            <a-form-item label="课题等级" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-select v-decorator="['projectLevel', {rules: [{required: true, message: '请选择课题等级！'}]}]" placeholder="请选择课题等级">


                <a-select-option value="2">
                  S
                </a-select-option>
                <a-select-option value="3">
                  A
                </a-select-option>

                <a-select-option value="4">
                  B
                </a-select-option>

              </a-select>
            </a-form-item>
          </a-col>
        </a-row> <a-row :gutter="24">
        <a-col :md="20" :sm="24">
          <a-form-item label="课题负责人" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input type='hidden' v-decorator="['projectLeaderId', {rules: [{required: true, message: '请选择课题负责人！'}]}]" />
            <a-dropdown v-model="usvisible" placement="bottomCenter" :trigger="['click']">
              <a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">{{projectLeader ? projectLeader : '选择课题负责人'}}
                <a-icon type="down" /></a-button>
              <a-menu slot="overlay">
                <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                  <a-input-search v-model="usqueryParam.searchValue" placeholder="搜索..." @change="onusSearch" />
                  <s-table style="width:100%;" ref="ustable" :rowKey="(record) => record.id" :columns="vcolumns" :data="loadusData" :customRow="customusRow" :scroll="{ y: 120,x:120}">>
                  </s-table>
                </a-spin>
              </a-menu>
            </a-dropdown>
          </a-form-item>
        </a-col>

      </a-row>
        <a-row :gutter="24">

          <a-col :md="20" :sm="24">
            <a-form-item label="所属部门" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input type='hidden'  v-decorator="['departmentCate', {rules: [{required: true, message: '请选择部门！'}]}]" />
              <a-tree-select
                labelInValue  
                :autoExpandParent="false"
                :value="deptValue"
                @change="changeDept"  
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" 
                :tree-data="depts"
                placeholder="请选择部门">
              </a-tree-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">

          <a-col :md="20" :sm="24">
            <a-form-item label="课题背景" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea :rows="3" placeholder="请输入课题背景" v-decorator="['projectBackGround', {rules: [{required: true, message: '请填写课题背景!'}]}]"></a-textarea>
            </a-form-item>
          </a-col>

        </a-row>
        <a-row :gutter="24">


          <a-col :md="20" :sm="24">
            <a-form-item label="研究内容" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea :rows="3" placeholder="请输入研究内容" v-decorator="['researchContent', {rules: [{required: true, message: '请填写研究内容!'}]}]"></a-textarea>
            </a-form-item>
          </a-col>
        </a-row>




        <div style="font-size: 20px;font-weight: bolder;">2、角色维护</div>


        <a-row :gutter="24">
          <a-col :md="20" :sm="24">
            <a-form-item label="项目管理员" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input type='hidden' v-decorator="['projectManagerId', {rules: [{required: true, message: '请选择项目管理员！'}]}]" />
              <a-dropdown v-model="dropdownvisible" placement="bottomCenter" :trigger="['click']">
                <a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">{{projectManager ? projectManager : '选择项目管理员'}}
                  <a-icon type="down" /></a-button>
                <a-menu slot="overlay">
                  <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                    <a-input-search v-model="queryParam.searchValue" placeholder="搜索..." @change="onSearch" />
                    <s-table style="width:100%;" ref="table" :rowKey="(record) => record.id" :columns="vcolumns" :data="loadData" :customRow="customRow" :scroll="{ y: 120,x:120}">>
                    </s-table>
                  </a-spin>
                </a-menu>
              </a-dropdown>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="20" :sm="24">
            <a-form-item label="直属领导" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input type='hidden' v-decorator="['directLeaderId', {rules: [{required: true, message: '请选择直属领导！'}]}]" />
              <a-dropdown v-model="ddropdownvisible" placement="bottomCenter" :trigger="['click']">
                <a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">{{directLeader ? directLeader : '选择直属领导'}}
                  <a-icon type="down" /></a-button>
                <a-menu slot="overlay">
                  <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                    <a-input-search v-model="dqueryParam.searchValue" placeholder="搜索..." @change="ondSearch" />
                    <s-table style="width:100%;" ref="dtable" :rowKey="(record) => record.id" :columns="vcolumns" :data="loaddData" :customRow="customdRow" :scroll="{ y: 120,x:120}">>
                    </s-table>
                  </a-spin>
                </a-menu>
              </a-dropdown>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :md="20" :sm="24">
            <a-form-item label="部门长/总监" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input type='hidden' v-decorator="['inspectorGeneralId', {rules: [{required: true, message: '请选择部门长/总监！'}]}]" />
              <a-dropdown v-model="idropdownvisible" placement="bottomCenter" :trigger="['click']">
                <a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">{{inspectorGeneral ? inspectorGeneral : '选择部门长/总监'}}
                  <a-icon type="down" /></a-button>
                <a-menu slot="overlay">
                  <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                    <a-input-search v-model="iqueryParam.searchValue" placeholder="搜索..." @change="oniSearch" />
                    <s-table style="width:100%;" ref="itable" :rowKey="(record) => record.id" :columns="vcolumns" :data="loadiData" :customRow="customiRow" :scroll="{ y: 120,x:120}">>
                    </s-table>
                  </a-spin>
                </a-menu>
              </a-dropdown>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- <a-row :gutter="24">
          <a-col :md="20" :sm="24">
            <a-form-item label="副所长" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input type='hidden' v-decorator="['deputyHeadOfTheInstituteId']" />
              <a-dropdown v-model="deputyHeadvisible" placement="bottomCenter" :trigger="['click']">
                <a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">{{deputyHeadOfTheInstitute ?
                  deputyHeadOfTheInstitute : '选择副所长'}}
                  <a-icon type="down" /></a-button>
                <a-menu slot="overlay">
                  <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                    <a-input-search v-model="dequeryParam.searchValue" placeholder="搜索..." @change="ondeSearch" />
                    <s-table style="width:100%;" ref="detable" :rowKey="(record) => record.id" :columns="vcolumns" :data="loaddeData" :customRow="customdeRow" :scroll="{ y: 120,x:120}">>
                    </s-table>
                  </a-spin>
                </a-menu>
              </a-dropdown>
            </a-form-item>
          </a-col>
        </a-row> -->

         <a-row :gutter="24">
          <a-col :md="20" :sm="24">
            <a-form-item label="所长" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input type='hidden' v-decorator="['headOfTheInstituteId', {rules: [{required: true, message: '请选择所长！'}]}]" />
              <a-dropdown v-model="hdropdownvisible" placement="bottomCenter" :trigger="['click']">
                <a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">{{headOfTheInstitute ? headOfTheInstitute : '选择所长'}}
                  <a-icon type="down" /></a-button>
                <a-menu slot="overlay">
                  <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                    <a-input-search v-model="hqueryParam.searchValue" placeholder="搜索..." @change="onhSearch" />
                    <s-table style="width:100%;" ref="htable" :rowKey="(record) => record.id" :columns="vcolumns" :data="loadhData" :customRow="customhRow" :scroll="{ y: 120,x:120}">>
                    </s-table>
                  </a-spin>
                </a-menu>
              </a-dropdown>
            </a-form-item>
          </a-col>

        </a-row>

      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import {
    getCateTree,
    addOrUpdateReview,
    updateReviewUncheck,
    updateTransition
  } from '@/api/modular/system/topic'
  import {
    getUserLists
  } from '@/api/modular/system/userManage'
  import {
    STable
  } from '@/components'
  export default {
    props: {
      issueId: {
        type: Number,
        default: 0
      },
    },
    components: {
      STable
    },
    data() {
      return {
        queryParam: {},
        showLevel: true,
        clientHeight:document.documentElement.clientHeight,
        dqueryParam: {},//直属领导
        hqueryParam:{},//所长
        iqueryParam:{},//总监
        dequeryParam:{},//副所长
        pqueryParam:{},//院长
        usqueryParam:{},
        visible2: false,
        loading: false,

        cate:[],
        depts:[],

        dropdownvisible: false,
        usvisible: false,
        ddropdownvisible: false,//直属领导
        hdropdownvisible:false,//所长
        idropdownvisible:false,//总监
        deputyHeadvisible:false,//副总监
        pdropdownvisible:false,//院长

        projectCate:null,
        departmentCate:null,

        treeValue: {
          label:'请选择'
        },
        deptValue:{
          label:'请选择'
        },

        vcolumns: [{
          title: '账号',
          dataIndex: 'account'
        },
          {
            title: '姓名',
            dataIndex: 'name'
          },
        ],
        loadData: parameter => {
          return getUserLists(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },

        //直属领导
        loaddData: parameter => {
          return getUserLists(Object.assign(parameter, this.dqueryParam)).then((res) => {
            return res.data
          })
        },
        //所长
        loadhData: parameter => {
          return getUserLists(Object.assign(parameter, this.hqueryParam)).then((res) => {
            return res.data
          })
        },
        loadusData: parameter => {
          return getUserLists(Object.assign(parameter, this.usqueryParam)).then((res) => {
            return res.data
          })
        },
        //总监
        loadiData: parameter => {
          return getUserLists(Object.assign(parameter, this.iqueryParam)).then((res) => {
            return res.data
          })
        },
        //副所长
        loaddeData: parameter => {
          return getUserLists(Object.assign(parameter, this.dequeryParam)).then((res) => {
            return res.data
          })
        },

        //院长
        loadpData: parameter => {
          return getUserLists(Object.assign(parameter, this.pqueryParam)).then((res) => {
            return res.data
          })
        },

        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 8
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          }
        },
        visible: false,
        record: null,
        visible1: false,
        confirmLoading: false,
        form: this.$form.createForm(this),
        projectManager: '',//项目责任人
        directLeader: '',//直属领导
        headOfTheInstitute:'',//所长
        inspectorGeneral:'',//总监
        deputyHeadOfTheInstitute:'',//副所长
        president:'',//院长
        projectLeaderId:'',
      }
    },
    methods: {
      checklevel(e){
        if(e == 2){
          this.showLevel = false
        }else{
          this.showLevel = true
        }
      } ,
      onSearch(e) {
        this.$refs.table.refresh()
      },

      //直属领导
      ondSearch(e) {
        this.$refs.dtable.refresh()
      },
      //所长
      onhSearch(e) {
        this.$refs.htable.refresh()
      },
      //总监
      oniSearch(e) {
        this.$refs.itable.refresh()
      },
      ondeSearch(e) {
        this.$refs.detable.refresh()
      },
      onusSearch(e) {
        this.$refs.ustable.refresh()
      },
      //院长
      onpSearch(e) {
        this.$refs.ptable.refresh()
      },

      // 初始化方法
      edit(record) {

        let _tile = this.getSelectedItem(record.projectCate,this.cate)
          this.treeValue = {
            label : _tile,
            value : record.projectCate
          }


        let $title = this.getSelectedItem(record.departmentCate,this.depts)
        this.deptValue = {
            label : $title,
            value : record.departmentCate
          }

        this.record = record

        setTimeout(() => {
          this.form.setFieldsValue(
            record
          )

          this.projectCate = record.projectCate
          this.departmentCate = record.departmentCate
          this.directLeader = record.directLeader
          this.headOfTheInstitute = record.headOfTheInstitute
          this.inspectorGeneral = record.inspectorGeneral
          this.deputyHeadOfTheInstitute = record.deputyHeadOfTheInstitute

          this.projectManager = record.projectManager
          this.projectLeader = record.projectLeader
          
          /**
           *  projectManagerName: '',//项目责任人
           directLeaderName: '',//直属领导
           headOfTheInstituteName:'',//所长
           inspectorGeneralName:'',//总监
           deputyHeadOfTheInstituteName:'',//副所长
           presidentName:'',//院长
           userName:'',
           */

        }, 100)
        this.visible = true
      },
      customRow(row, index) {
        return {
          on: {
            click: () => {
              this.form.setFieldsValue({
                projectManagerId: row.account
              })
              this.projectManager = row.name
              this.dropdownvisible = false
            }
          }
        }
      },

      customusRow(row, index) {
        return {
          on: {
            click: () => {
              this.form.setFieldsValue({
                projectLeaderId: row.account
              })
              this.projectLeader = row.name
              this.usvisible = false
            }
          }
        }
      },

      //直属领导
      customdRow(row, index) {
        return {
          on: {
            click: () => {
              this.form.setFieldsValue({
                directLeaderId: row.account
              })
              this.directLeader = row.name
              this.ddropdownvisible = false
            }
          }
        }
      },
      //所长
      customhRow(row, index) {
        return {
          on: {
            click: () => {
              this.form.setFieldsValue({
                headOfTheInstituteId: row.account
              })
              this.headOfTheInstitute = row.name
              this.hdropdownvisible = false
            }
          }
        }
      },
      //总监
      customiRow(row, index) {
        return {
          on: {
            click: () => {
              this.form.setFieldsValue({
                inspectorGeneralId: row.account
              })
              this.inspectorGeneral = row.name
              this.idropdownvisible = false
            }
          }
        }
      },
      customdeRow(row, index) {
        return {
          on: {
            click: () => {
              this.form.setFieldsValue({
                deputyHeadOfTheInstituteId: row.account
              })
              this.deputyHeadOfTheInstitute = row.name
              this.deputyHeadvisible = false
            }
          }
        }
      },
      //院长
      custompRow(row, index) {
        return {
          on: {
            click: () => {
              this.form.setFieldsValue({
                presidentId: row.account
              })
              this.president = row.name
              this.pdropdownvisible = false
            }
          }
        }
      },

      callUpdateTransition(){
        updateTransition({
          issueId:this.record.issueId
        }).then((res) => {
          if (res.success) {
           if (res.data.result) {
              this.$message.success('修改成功')
              this.$emit('ok')
              this.handleCancel()
           }else{
            this.$message.error(res.data.message)
           }
          } else {
            this.$message.error('保存失败：' + res.message)
          }
        }).finally((res) => {
          this.confirmLoading = false
        })
      },

      handleSubmit() {
        this.visible1 = false
        const {
          form: {
            validateFields
          }
        } = this
        this.confirmLoading = true
        validateFields((errors, values) => {

          values.issueType = values.platformAndTopic


          if (!errors) {
            let $params = { ...values,
              issueId:this.record.issueId,
            }


            if($params.projectLevel == 4){
              $params.issueType == 2
            }else{
              $params.issueType == 1
            }
            updateReviewUncheck($params).then((res) => {
              if (res.success) {
                if (res.data) {
                  this.callUpdateTransition()
                } else {
                  this.$message.error('修改失败')
                }
              } else {
                this.$message.error('修改失败：' + res.data.message)
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleSubmitBefore() {
        const {
          form: {
            validateFields
          }
        } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            this.visible1 = true
            this.visible2 = true
          }
          this.confirmLoading = false
        })
      },


      handleCancel() {
        this.confirmLoading = false
        /* this.projectManagerName = ''
        this.directLeaderName = ''// 直属领导
        this.headOfTheInstituteName = ''//所长
        this.inspectorGeneralName = ''//总监
        this.presidentName = ''//院长 */
        this.form.resetFields()
        this.visible = false
        this.visible1 = false
        this.visible2 = false
        this.projectCate = null
        this.departmentCate = null
        
      },

      callGetDeptTree(){
        this.confirmLoading = true
        getCateTree({
          fieldName:'department'
        }).then((res)=>{
          if (res.success) {
            this.depts = res.data
          } else {
            this.$message.error('错误提示：' + res.message, 1)
          }
          this.confirmLoading = false
        }).catch((err) => {
          this.confirmLoading = false
          this.$message.error('错误提示：' + err.message, 1)
        });
      },

      callGetTree(){
        this.confirmLoading = true
        getCateTree({
          fieldName:'projectCate'
        }).then((res)=>{
          if (res.success) {
            this.cate = res.data
          } else {
            this.$message.error('错误提示：' + res.message, 1)
          }
          this.confirmLoading = false
        }).catch((err) => {
          this.confirmLoading = false
          this.$message.error('错误提示：' + err.message, 1)
        });
      },

      changeCate(value, label, extra){
        this.form.setFieldsValue({
          projectCate: value
        })
        this.projectCate = value
      },

      changeDept(nodeValue){

        let _title = this.getSelectedItem(nodeValue.value, this.depts)
        this.$nextTick(()=>{
          this.deptValue = {
              label: _title,
              value: nodeValue.value
          }
          this.form.setFieldsValue({
              departmentCate: nodeValue.value
          })
          this.departmentCate = nodeValue.value
        })
      },

      selectTree(nodeValue){
              
                let _title = this.getSelectedItem(nodeValue.value, this.cate)
                this.$nextTick(()=>{
                    this.projectCate = nodeValue.value
                    this.form.setFieldsValue({
                        projectCate: nodeValue.value
                    })
                    this.treeValue = {
                        label: _title,
                        value: nodeValue.value
                    }
                })
            },
            /**
             * @description 当前选中对象的title(拼接所有父级title)
             */
            getSelectedItem(value, data, title){
                let $title = ''
                for(const item of data){
                    //在根节点找到对应选项
                    if (!title && item.value == value ) {
                        $title = item.title
                    }
                    //根节点未找到继续递归查找
                    else if(!title && item.children){
                        $title = item.title
                        //this.getSelectedItem(value, item.children, $title);
                        for (const e of item.children) {
                          if (e.value == value) {
                            $title = $title + '-' + e.title
                            return $title
                          }
                        }
                    }
                    
                    //在子节点找到对应选项
                    if (title && item.value == value){
                        $title = title + '-' + item.title
                    }
                    //当前子节点未找到继续递归向下查找
                    else if(title && item.children){
                        /* $title = title + '-' + item.title
                        this.getSelectedItem(value, item.children, $title); */
                        for (const e of item.children) {
                          if (e.value == value) {
                            $title = $title + '-' + e.title
                            return $title
                          }
                        }
                    }
                }
            },

    },
    created() {
      this.callGetTree()
      this.callGetDeptTree()
    }
  }
</script>