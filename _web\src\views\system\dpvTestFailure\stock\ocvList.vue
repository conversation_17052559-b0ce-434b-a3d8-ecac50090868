<template>
  <div>
    <a-modal title="OCV&ACR" :width="1200" :visible="visible" :loading="loading" @cancel="handleCancel"
            :dialog-style="{ top: '20px' }"
    >
      <template slot="footer">
        <a-button key="back" @click="handleCancel">关闭</a-button>
      </template>
      <a-descriptions title="电芯信息">
        <a-descriptions-item label="产品名称">
          {{ record.productName }}
        </a-descriptions-item>
        <a-descriptions-item label="电芯编码">
          {{ record.cellCode }}
        </a-descriptions-item>
      </a-descriptions>
    <!--	<div class="ocvTable">-->
    <!--		<a-table :style="{height:'initial'}" :rowKey="record => record.id" :pagination="false" :loading="loading" :columns="tableColumns" :data-source="tableData" size="middle" bordered>-->
    <!--		</a-table>-->
    <!--	</div>-->
    <!--  图表-->
      <div class="all-wrapper">
        <div class="block left-content ">
          <div class="edit-icon" @click="handleEditChart">
            <svg t="1717382880632" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3614" width="25" height="25"><path d="M192 160c4.4032 0 8 3.6032 8 8v624h688c4.4032 0 8 3.6032 8 8v56c0 4.4032-3.6032 8-8 8H136c-4.4032 0-8-3.6032-8-8V168c0-4.4032 3.6032-8 8-8z m152 110.4c4.4032 0 8 9.9392 8 22.08v397.44c0 12.1472-3.6032 22.08-8 22.08H288c-4.4032 0-8-9.9328-8-22.08V292.48c0-12.1472 3.6032-22.08 8-22.08z m152 364.8c4.4032 0 8 0.8256 8 1.8304v73.1392c0 1.0048-3.6032 1.8304-8 1.8304h-56c-4.4032 0-8-0.8256-8-1.8304V637.0304c0-1.0048 3.6032-1.8304 8-1.8304z m152-64c4.4032 0 8 1.9648 8 4.3648v132.0704c0 2.4-3.6032 4.3648-8 4.3648H592c-4.4032 0-8-1.9648-8-4.3648V575.5648c0-2.4 3.6032-4.3648 8-4.3648z m152-198.4c4.4032 0 8 2.9376 8 6.528v326.144c0 3.5904-3.6032 6.528-8 6.528h-56c-4.4032 0-8-2.9376-8-6.528V379.328c0-3.5904 3.6032-6.528 8-6.528zM717.184 160c1.4848 0 2.8992 0.5056 4.032 1.664l87.936 89.7408a5.888 5.888 0 0 1 0 8.1664L514.9056 559.4432a5.8048 5.8048 0 0 1-3.8208 1.664L427.6032 563.2h-0.2816c-6.2976 0-11.392-5.2736-11.328-11.7056l1.3504-85.9136c0-1.5168 0.64-2.9632 1.6256-4.0448L713.152 161.664a5.792 5.792 0 0 1 4.032-1.664z m-0.0192 70.6816L468.256 484.4032l-0.416 26.5664 23.4688-0.5888 250.1376-254.912-24.2816-24.7872z" fill="#000000A6" p-id="3615"></path></svg>
          </div>
          <div id="ocvEchart" ref="ocvEchart"
              style="width: 600px;height: 417px;border: 0.5px solid #ccc;"
          ></div>
        </div>
    <!--  </div>-->
    <!--  <div class="all-wrapper">-->
        <div class="block right-content " id="export">
          <div >
            <div style="display: flex;justify-content: center;font-weight: bold;font-size: 18px">OCV&ACR数据</div>
            <div class="ocvTable">
              <s-table :columns="tableColumns"
                      ref="ocvTable"
                      :data="loadData"
                      :loading="loading"
                      bordered
                      size="middle">

              </s-table>
    <!--          <a-table :style="{height:'initial'}" :rowKey="record => record.id" :pagination="false" :loading="loading" :columns="tableColumns" :data-source="tableData" size="middle" bordered>-->
    <!--          </a-table>-->
            </div>
          </div>
        </div>
      </div>


    </a-modal>
    <div v-if="drawerVisible">
      <PreviewDrawer 
        :legendOptions="originalLegend"
        :data="originalSeries"
        :original="resetOriginal"
        :editData="editData"
        :checkObj="checkObj" 
        @submit="handleDrawerSubmit"
        @reset="handleDrawerReset" 
        @close="handleDrawerClose">
      </PreviewDrawer>
    </div>
  </div>
</template>

<script>
import {clamp, STable} from '@/components'
import {
  getFailureCellOcvRecordList,
  getFailureCellOcvRecordPageList
} from "@/api/modular/system/testFailure"

import PreviewDrawer from "../../vTestReport/components/previewDrawerTemporary"
export default {
  components: {
    clamp,
    STable,
    PreviewDrawer
  },
	data() {
		return{
			visible: false,
			loading: false,
      drawerVisible:false,
      
      record:{},
      queryParam:{},
      loadData: parameter => {
        this.queryParam.testFailureId = this.record.id
        return getFailureCellOcvRecordPageList(Object.assign(parameter, this.queryParam)).then((res) => {
          return res.data
        })
      },
			tableColumns:[
				{
          title: '序号',
          dataIndex: 'index',
          align: 'center',
          customRender: (text, record, index) => `${index+1}`
        },
				{
					title: '产品名称',
					dataIndex: 'productName',
          align: 'center',
				},
				{
					title: '电芯编码',
					dataIndex: 'cellCode',
          align: 'center',
				},
				{
					title: '内阻/mΩ',
					dataIndex: 'insideRes',
          align: 'center',
				},
				{
					title: '电压/mV',
					dataIndex: 'voltage',
          align: 'center',
				},
				{
					title: '记录日期',
					dataIndex: 'recordDate',
          align: 'center',
				},
			],
			tableData:[],

      // 在线编辑图表
      disChargeCapacityEchart:null,
      isEditXNum: 0,
      isEditYNum: 0,
      originalLegend:[],
      originalSeries:[],
      checkObj:{},

      editData:{
        chartTitle:'OCV&ACR曲线',
        XTitle: 'Date', 
        titleTop: 10,
        yTitleLetf: 60,
        yTitleRight: 60,

        legendWidth: 20,
        legendHeight: 5,
        legendGap: 10,//图例间隙
        legendOrient: 'horizontal',
        legendTop: 40,
        legendLeft:229,
        legendBgColor:'',

        gridTop: 70,
        gridLeft: 90,
        gridRight: 90,
        gridBottom: 70,

        xMin: 0,
        xMax: 0,
        xInterval: 0,
        xType: "category",
        yMin: 0,
        yMax: 0,
        yInterval: 0,
        yType: "value",
        yMin2: 0,
        yMax2: 0,
        yInterval2: 0,
        yType2: "value",
        legend: [], // 修改值
        legendSort: [], //图例排序
        legendEditName: [], //图例更名
        series: [] // 修改值
      },
      resetOriginal:{
        chartTitle:'OCV&ACR曲线',
        XTitle: 'Date', 
        titleTop: 10,
        yTitleLetf: 60,
        yTitleRight: 60,

        legendWidth: 20,
        legendHeight: 5,
        legendGap: 10,//图例间隙
        legendOrient: 'horizontal',
        legendTop: 40,
        legendLeft:229,
        legendBgColor:'',

        gridTop: 70,
        gridLeft: 90,
        gridRight: 90,
        gridBottom: 70,

        xMin: 0,
        xMax: 0,
        xInterval: 0,
        xType: "category",
        yMin: 0,
        yMax: 0,
        yInterval: 0,
        yType: "value",
        yMin2: 0,
        yMax2: 0,
        yInterval2: 0,
        yType2: "value",
        legend: [], // 修改值
        legendSort: [], //图例排序
        legendEditName: [], //图例更名
        series: [] // 修改值
      },

		}
	},
  // mounted() {
  //   // this.initdisChargeCapacityEchart()
  // },
  methods: {
		view(record){
      this.record = record;
			this.getFailureCellOcvRecordList({testFailureId:record.id})
      // this.initdisChargeCapacityEchart()
			this.visible = true
      this.$nextTick(() => {
        this.$refs.ocvTable.refresh()
      })
		},
    // 放电容量图表
    initdisChargeCapacityEchart(
      legendData = {},
      checkData = [], //选中的数据
      axisData = {},
      titleData = {},
      gridData = {}
    ) {
      if (this.disChargeCapacityEchart) this.disChargeCapacityEchart.dispose();
      this.disChargeCapacityEchart = this.echarts.init(this.$refs.ocvEchart, 'walden', { devicePixelRatio: 2 })

      let disChargeCapacityEchartList = {innerResDataList: [], voltageDataList: []}
      for (let i = 0 ,j = 0; i < this.tableData.length; i++){
        var record1 = this.tableData[i];
        var recordDate = record1.recordDate;
        var insideRes = record1.insideRes
        var voltage = record1.voltage
        if (isNaN(insideRes) || isNaN(voltage)) {
          continue
        }
        disChargeCapacityEchartList.innerResDataList[j] = [recordDate, insideRes];
        disChargeCapacityEchartList.voltageDataList[j] = [recordDate, voltage];
        j++
      }

      const voltageIndex  = checkData.findIndex(findItem => findItem.soc == 'Voltage')  
      const acrIndex  = checkData.findIndex(findItem => findItem.soc == 'ACR')  

      let seriesList = [
        {
          name: 'Voltage',
          index:0,
          soc:'Voltage',
          id: 'Voltage',
          type: "line",
          barGap: 0,
          markPoint: {
            data: []
          },
          connectNulls:checkData.length === 0 ? false : Boolean(Number(checkData[voltageIndex].connectNulls)),
          symbol: checkData.length === 0 ? "emptyCircle" : checkData[voltageIndex].symbol,
          symbolSize: checkData.length === 0 ? 5 : checkData[voltageIndex].symbolSize,
          lineStyle: {
            width: checkData.length === 0 ? 2 : checkData[voltageIndex].lineWidth,
            type: checkData.length === 0 ? "solid" : checkData[voltageIndex].lineType,
            color:checkData.length === 0 ? '#5470c6' : checkData[voltageIndex].lineColor
          },
          itemStyle: {
            color:checkData.length === 0 ? '#5470c6' : checkData[voltageIndex].itemColor
          },
          emphasis: {
            focus: "series"
          },
          tooltip: {
            valueFormatter: function (value) {
              return '  ' + value + ' mV';
            }
          },
          data: disChargeCapacityEchartList.voltageDataList.map((mapItem, index) => { return { id: index, value: mapItem } })
        },
        {
          name: 'ACR',
          index:1,
          soc:'ACR',
          id: 'ACR',
          type: "line",
          barGap: 0,
          markPoint: {
            data: []
          },
          yAxisIndex: 1,
          connectNulls:checkData.length === 0 ? false : Boolean(Number(checkData[acrIndex].connectNulls)),
          symbol: checkData.length === 0 ? "emptyCircle" : checkData[acrIndex].symbol,
          symbolSize: checkData.length === 0 ? 5 : checkData[acrIndex].symbolSize,
          lineStyle: {
            width: checkData.length === 0 ? 2 : checkData[acrIndex].lineWidth,
            type: checkData.length === 0 ? "dotted" : checkData[acrIndex].lineType,
            color:checkData.length === 0 ? '#91cc75' : checkData[acrIndex].lineColor
          },
          itemStyle: {
            color:checkData.length === 0 ? '#91cc75' : checkData[acrIndex].itemColor
          },
          emphasis: {
            focus: "series"
          },
          tooltip: {
            valueFormatter: function (value) {
              return '  ' + value + ' mΩ';
            }
          },
          data: disChargeCapacityEchartList.innerResDataList.map((mapItem, index) => { return { id: index, value: mapItem } }),
        }
      ];

      // 设置最大最小值
      if (checkData.length > 0 && checkData[voltageIndex].maxPoint) {
        seriesList[0].markPoint.data.push({ type: "max", name: "Max" })
      }
      if (checkData.length > 0 && checkData[voltageIndex].minPoint) {
        seriesList[0].markPoint.data.push({ type: "min", name: "Min" })
      }
      if (checkData.length > 0 && checkData[acrIndex].maxPoint) {
        seriesList[1].markPoint.data.push({ type: "max", name: "Max" })
      }
      if (checkData.length > 0 && checkData[acrIndex].minPoint) {
        seriesList[1].markPoint.data.push({ type: "min", name: "Min" })
      }

      this.originalSeries = [
        {
          name: 'Voltage',
          index:0,
          soc:'Voltage',
          id: 'Voltage',
          synchronization: checkData.length === 0 ? 0 : checkData[voltageIndex].synchronization,
          maxPoint: checkData.length === 0 ? false : checkData[voltageIndex].maxPoint,
          minPoint: checkData.length === 0 ? false : checkData[voltageIndex].minPoint,
          connectNulls:checkData.length === 0 ? false : Boolean(Number(checkData[voltageIndex].connectNulls)),
          symbol: checkData.length === 0 ? "emptyCircle" : checkData[voltageIndex].symbol,
          symbolSize: checkData.length === 0 ? 5 : checkData[voltageIndex].symbolSize,
          lineWidth: checkData.length === 0 ? 2 : checkData[voltageIndex].lineWidth,
          lineType: checkData.length === 0 ? "solid" : checkData[voltageIndex].lineType,
          lineColor:checkData.length === 0 ? '#5470c6' : checkData[voltageIndex].lineColor,
          itemColor:checkData.length === 0 ? '#5470c6' : checkData[voltageIndex].itemColor,
          // data:disChargeCapacityEchartList.voltageDataList.map(mapItem => mapItem[1].toString() )
        },
        {
          name: 'ACR',
          index:1,
          soc:'ACR',
          id: 'ACR',
          synchronization: checkData.length === 0 ? 0 : checkData[voltageIndex].synchronization,
          maxPoint: checkData.length === 0 ? false : checkData[voltageIndex].maxPoint,
          minPoint: checkData.length === 0 ? false : checkData[voltageIndex].minPoint,
          connectNulls:checkData.length === 0 ? false : Boolean(Number(checkData[voltageIndex].connectNulls)),
          symbol: checkData.length === 0 ? "emptyCircle" : checkData[voltageIndex].symbol,
          symbolSize: checkData.length === 0 ? 5 : checkData[voltageIndex].symbolSize,
          lineWidth: checkData.length === 0 ? 2 : checkData[voltageIndex].lineWidth,
          lineType: checkData.length === 0 ? "dotted" : checkData[voltageIndex].lineType,
          lineColor:checkData.length === 0 ? '#91cc75' : checkData[voltageIndex].lineColor,
          itemColor:checkData.length === 0 ? '#91cc75' : checkData[voltageIndex].itemColor,
          // data:disChargeCapacityEchartList.voltageDataList.map(mapItem => mapItem[1].toString() )
        }
      ]

      this.resetOriginal.checkData = [
        {
          soc: 'Voltage',
          symbol: "emptyCircle",
          symbolSize: 5,
          itemColor: '#5470c6',
          lineType: "solid",
          lineWidth: 2,
          lineColor: '#5470c6'
        },
        {
          soc: 'ACR',
          symbol: "emptyCircle",
          symbolSize: 5,
          itemColor: '#91cc75',
          lineType: "dotted",
          lineWidth: 2,
          lineColor: '#91cc75'
        }
      ]

      // this.editData.duplicateDataOptions=[
      //   {
      //     id: 'Voltage',
      //     data: disChargeCapacityEchartList.voltageDataList.map((mapItem, index) => { return { id: index, value: index, label: mapItem[1].toString() } })
      //   },
      //   {
      //     id: 'ACR',
      //     data: disChargeCapacityEchartList.innerResDataList.map((mapItem, index) => { return { id: index, value: index, label: mapItem[1].toString() } })
      //   }
      // ]
      this.originalLegend = ['Voltage','ACR']
      this.editData.legend = ['Voltage','ACR']
      this.editData.series = _.cloneDeep(this.originalSeries)

      

      /* 图例排序 开始 */
      if (legendData.legendSort) {
        this.editData.legend = _.cloneDeep(legendData.legendSort)
      }
      this.editData.legendSort = _.cloneDeep(this.editData.legend)
      /* 图例排序 结束 */

       /* 图例变更名称 开始 */
      if (legendData.legendEditName) {
        legendData.legendEditName.forEach(v => {
          if (v.newName && !v.isReset) {
            let temIndex1 = this.originalLegend.findIndex(findItem => findItem == v.originName)
            this.originalLegend[temIndex1] = v.newName

            let temIndex2 = this.editData.legend.findIndex(findItem => findItem == v.originName)
            this.editData.legend[temIndex2] = v.newName

            this.editData.series.forEach(findItem => {
              findItem.name = findItem.name == v.originName ? v.newName : findItem.name
            })

            seriesList.forEach(findItem => {
              findItem.name = findItem.name == v.originName ? v.newName : findItem.name
            })
          }

          if (!v.newName && v.isReset) {
            v.previousName = ''
            v.isReset = false
          }
        })
        this.editData.legendEditName = legendData.legendEditName
      }
      if (this.editData.legendEditName.length === 0) {
        this.editData.legendEditName = this.originalLegend.map(v => { return { originName: v, previousName: '', newName: '', isReset: false } })
      }
      /* 图例变更名称 结束 */

      /* 图例选中处理 开始 */
      if (legendData.legendEdit) {
        for (let i = 0; i < this.editData.legend.length; i++) {
          if (!legendData.legendEdit.includes(this.editData.legend[i])) {
            this.editData.legend.splice(i, 1)
            i--
          }
        }

        for (let i = 0; i < seriesList.length; i++) {
          if (!legendData.legendEdit.includes(seriesList[i].name)) {
            seriesList.splice(i, 1)
            i--
          }
        }

        for (let i = 0; i < checkData.length; i++) {
          if (!legendData.legendEdit.includes(checkData[i].name)) {
            checkData.splice(i, 1)
            i--
          }
        }
      }
      /* 图例选中处理 结束 */

      /* 处理选中折线数据 开始 */
      // if (checkData.length > 0) {
      //   seriesList.forEach((v, index) => {
      //     const handIndex = checkData.findIndex(findItem => findItem.id == v.id)
      //     for (let i = 0; i < v.data.length; i++) {
      //       if (!checkData[handIndex].duplicateData.includes(v.data[i].id)  && v.data[i].value[1] !== '') {
      //         v.data.splice(i, 1)
      //         i--
      //       }
      //     }
      //   })
      // }
      /* 处理选中折线数据 结束 */

      // 如果X轴是类目轴，数字转为字符串
      if(!axisData.xType || axisData.xType === 'category'){
        seriesList.forEach(forItem => {
          forItem.data = forItem.data.map(mapItem => { return { id:mapItem.id,name:mapItem.name,value: [mapItem.value[0].toString(),mapItem.value[1]] } } )
        })
      }

      if(!axisData.yType || axisData.yType === 'category'){
        seriesList.forEach(forItem => {
          forItem.data = forItem.data.map(mapItem => { return { id:mapItem.id,name:mapItem.name,value: [mapItem.value[0],mapItem.value[1].toString()] } } )
        })
      }

      
      let options = {
        animationDuration: 2000,
        title: {
          text: titleData.chartTitle || 'OCV&ACR曲线',
          top:titleData.titleTop || 10,
          left: 'center',
          fontSize: 16,
          color: "#333"
        },
        grid: {
          show: true,
          borderWidth: 1,
          top:gridData.gridTop || 70,
          left:gridData.gridLeft || 90,
          right:gridData.gridRight || 90,
          bottom: gridData.gridBottom || 70,
          borderColor: '#ccc'
        },
        textStyle: {
          fontFamily: "Times New Roman"
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          top:  legendData.legendTop || 40,
          left: legendData.legendLeft || 229,
          itemWidth: legendData.legendWidth || 20,
          itemHeight: legendData.legendHeight || 5,
          itemGap: legendData.legendGap || 10,
          backgroundColor: legendData.legendBgColor || "#fff",
          orient: legendData.legendOrient || 'horizontal',
          fontSize: 14,
          color: "#333",
          data: this.editData.legend,
        },

        xAxis: [
          {
            type: axisData.xType || 'category',
            axisTick: { show: false },
            axisLabel: {
              show: true,
              width: 0.5,
              fontSize: 14,
              color: "#333",
              margin:15
            },
            name: titleData.XTitle ||  'Date',
            nameLocation: 'middle', // 将名称放在轴线的中间位置
            nameGap: 35,
            nameTextStyle: {
              fontSize: 14, // 可以根据需要调整字体大小
              color: "#333"
            },
          }
        ],
        
        yAxis: [
          {
            type: axisData.yType || 'value',
            name: 'Voltage / mV',
            position: 'left',
            alignTicks: true,
            nameGap: titleData.yTitleLetf || 60,
            min:0,
            max:5000,
            interval:1000,
            splitLine: {
              show: true,  // 显示分隔线
              lineStyle: {
                type: 'solid',  // 设置分隔线的样式，比如虚线
                width: 0.5
              }
            },
            axisTick: {
              show: true,  // 显示刻度
            },
            axisLabel: {
              show: true,
              width: 0.5,
              fontSize: 14,
              color: "#333"
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              }
            },
            nameLocation: 'middle', // 将名称放在轴线的起始位置
            nameRotate: 90, // 旋转角度，使名称竖排
            nameTextStyle: {
              fontSize: 14, // 可以根据需要调整字体大小
              color: "#333"
            },
            // splitNumber: 5,
          },
          {
            type: axisData.yType2 || 'value',
            name: 'ACR / mΩ',
            position: 'right',
            nameGap: titleData.yTitleRight || 60,
            min:0,
            max:300,
            interval:60,
            splitLine: {
              show: true,  // 显示分隔线
              lineStyle: {
                type: 'solid',  // 设置分隔线的样式，比如虚线
                width: 0.5
              }
            },
            axisTick: {
              show: true,  // 显示刻度
            },
            axisLabel: {
              show: true,
              width: 0.5,
              fontSize: 14,
              color: "#333"
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              }
            },
            nameLocation: 'middle', // 将名称放在轴线的起始位置
            nameRotate: 90, // 旋转角度，使名称竖排
            nameTextStyle: {
              fontSize: 14, // 可以根据需要调整字体大小
              color: "#333"
            },
            // splitNumber: 5,
          }
        ],
        series:seriesList,
      }

      // 传回给在线编辑图表，当前图标上有的点
      // this.editData.duplicateCheckedList = seriesList.map(mapItem => mapItem.data.filter(filterItem => filterItem.value[1] !== '').map(mapItem2 => mapItem2.id))

      // 处理坐标轴
      if (axisData.xMin) {
        options.xAxis[0].min = axisData.xMin
      }
      if (axisData.xMax) {
        options.xAxis[0].max = axisData.xMax
      }
      if (axisData.xInterval) {
        options.xAxis[0].interval = axisData.xInterval
      }
      
      if (axisData.yMin || axisData.yMin == 0) {
        options.yAxis[0].min = axisData.yMin
      }
      if (axisData.yMax || axisData.yMax == 0) {
        options.yAxis[0].max = axisData.yMax
      }

      if (axisData.yMin2 || axisData.yMin2 == 0)  {
        options.yAxis[1].min = axisData.yMin2
      }
      if (axisData.yMax2 || axisData.yMax2 == 0) {
        options.yAxis[1].max = axisData.yMax2
      }

      if (axisData.yInterval) {
        options.yAxis[0].interval = axisData.yInterval
      }

      if (axisData.yInterval2) {
        options.yAxis[1].interval = axisData.yInterval2
      }

      if(['yMin', 'yMax', 'yMax2','yMin2','yInterval', 'yInterval2'].includes(this.editData.targetEditObj) ||
         ['yMin', 'yMax', 'yMax2','yMin2','yInterval', 'yInterval2'].includes(this.editData.targetResetObj)){
          const axisIndex = ['yMin', 'yMax','yInterval'].includes(this.editData.targetEditObj) || ['yMin', 'yMax','yInterval'].includes(this.editData.targetResetObj) ? '' : 2   //自身
          const axisIndex1 = ['yMin', 'yMax','yInterval'].includes(this.editData.targetEditObj) || ['yMin', 'yMax','yInterval'].includes(this.editData.targetResetObj) ? 2 : ''  //另一边


          const oppositeMax = this.editData[`yMax${axisIndex1}`];
          const oppositeMin = this.editData[`yMin${axisIndex1}`];
          const oppositeInterval = this.editData[`yInterval${axisIndex1}`];

          const oneselfMax = this.editData[`yMax${axisIndex}`];
          const oneselfMin = this.editData[`yMin${axisIndex}`];
          const oneselfInterval = this.editData[`yInterval${axisIndex}`];

          // 算出对面的格子
          const temNum = (oppositeMax - oppositeMin) / oppositeInterval
          const temInterval = ['yInterval', 'yInterval2'].includes(this.editData.targetEditObj) || ['yInterval', 'yInterval2'].includes(this.editData.targetResetObj) ?  oneselfInterval :  Math.ceil((oneselfMax - oneselfMin) / temNum)

          // 赋值间隔
          options.yAxis[axisIndex == '' ? 0 : 1].interval = temInterval;
          this.editData[`yInterval${axisIndex}`] = temInterval;

          // 赋值最大值
          if (['yMin', 'yMin2','yInterval','yInterval2'].includes(this.editData.targetEditObj) ||
              ['yMin', 'yMin2','yInterval','yInterval2'].includes(this.editData.targetResetObj)) {
            const temMax = this.editData[`yMin${axisIndex}`] + temNum * temInterval;
            options.yAxis[axisIndex == '' ? 0 : 1].max = temMax;
            this.editData[`yMax${axisIndex}`] = temMax;
          }

          // 赋值最小值
          if (['yMax', 'yMax2'].includes(this.editData.targetEditObj) ||
              ['yMax', 'yMax2'].includes(this.editData.targetResetObj)) {
            const temMin = this.editData[`yMax${axisIndex}`] - temNum * temInterval;
            options.yAxis[axisIndex == '' ? 0 : 1].min = temMin;
            this.editData[`yMin${axisIndex}`] = temMin;
          }
         }

      // 坐标轴类型赋值
      this.editData.xType = options.xAxis[0].type
      this.editData.yType = options.yAxis[0].type
      this.editData.yType2 = options.yAxis[1].type


      this.disChargeCapacityEchart.clear()
      this.disChargeCapacityEchart.setOption(options)

      // 如果坐标轴类型为数值轴，则计算出最大值最小值，以及间距
      if (options.xAxis[0].type === "value") {
        const XAxis = this.disChargeCapacityEchart.getModel().getComponent("xAxis").axis.scale._extent
        this.editData.xMin = XAxis[0]
        this.editData.xMax = XAxis[1]
      }

      // 首次进入
      if (this.isEditYNum === 0 && options.yAxis[0].type === "value") {
        // const YAxis1 = this.disChargeCapacityEchart.getModel().getComponent("yAxis",0).axis.scale
        // const YAxis2 = this.disChargeCapacityEchart.getModel().getComponent("yAxis",1).axis.scale

        // this.editData.yMin = YAxis1._extent[0]
        // this.editData.yMax = YAxis1._extent[1]
        // this.editData.yInterval = YAxis1._interval 


        // this.resetOriginal.yMin =  YAxis1._extent[0]
        // this.resetOriginal.yMax =  YAxis1._extent[1]
        // this.resetOriginal.yInterval = YAxis1._interval

        this.editData.yMin = 0
        this.editData.yMax = 5000
        this.editData.yInterval = 1000


        this.resetOriginal.yMin =  0
        this.resetOriginal.yMax =  5000
        this.resetOriginal.yInterval = 1000

        // this.editData.yMin2 = YAxis2._extent[0]
        // this.editData.yMax2 = YAxis2._extent[1]
        // this.editData.yInterval2 = YAxis2._interval 

        // this.resetOriginal.yMin2 =  YAxis2._extent[0]
        // this.resetOriginal.yMax2 =  YAxis2._extent[1]
        // this.resetOriginal.yInterval2 = YAxis2._interval
        this.editData.yMin2 = 0
        this.editData.yMax2 = 300
        this.editData.yInterval2 = 60

        this.resetOriginal.yMin2 =  0
        this.resetOriginal.yMax2 =  300
        this.resetOriginal.yInterval2 = 60
        this.isEditYNum++

      }

      if (this.isEditXNum === 0 && axisData.xType === "value") {
        this.isEditXNum++
        this.resetOriginal.xMin = this.editData.xMin
        this.resetOriginal.xMax = this.editData.xMax
      }
    },
		getFailureCellOcvRecordList(params){
			getFailureCellOcvRecordList(params).then(res => {
				this.loading = true
				if (!res.success) {
					return
				}
				this.tableData = res.data
        // this.$nextTick(() => {
        //   this.initdisChargeCapacityEchart()
        // });
        setTimeout(this.initdisChargeCapacityEchart(), 1500)
			}).finally(() => {
				this.loading = false
			})
		},
		handleEditChart(){
      this.drawerVisible = true
    },
    // 生成
    handleDrawerSubmit(value) {

        const legendData = {
          legendEdit: value.legendList,
          legendWidth: value.legendWidth,
          legendHeight: value.legendHeight,
          legendGap: value.legendGap,
          legendSort: value.legendSort, // 图例排序
          legendEditName: value.legendEditName,
          legendBgColor: value.legendBgColor,
          legendOrient: value.legendOrient,
          legendTop: value.legendTop,
          legendLeft: value.legendLeft,
        }

        const axisData = {
          xMin: value.xMin,
          xMax: value.xMax,
          xInterval: value.xInterval,
          xType: value.xType,

          yMin: value.yMin,
          yMax: value.yMax,
          yInterval: value.yInterval,
          yType: value.yType,

          yMin2: value.yMin2,
          yMax2: value.yMax2,
          yInterval2: value.yInterval2,
          yType2: value.yType2
        }

        const titleData = {
          chartTitle: value.chartTitle,
          XTitle: value.XTitle,
          titleTop: value.titleTop,
          yTitleLetf: value.yTitleLetf,
          yTitleRight: value.yTitleRight,
        }

        const gridData = {
          gridTop: value.gridTop,
          gridLeft: value.gridLeft,
          gridRight: value.gridRight,
          gridBottom: value.gridBottom,
        }

        // 赋值的数组
        const assignArr = ['chartTitle', 'XTitle', 'titleTop','yTitleLetf','yTitleRight', 'legendWidth', 'legendHeight', 'legendGap', 'legendEditName', 'legendOrient','legendBgColor', 'legendTop', 'legendX', 'xMin', 'xMax', 'xInterval', 'xType',
          'yMin', 'yMax', 'yInterval', 'yType', 'yMin2', 'yMax2', 'yInterval2', 'yType2','synchronization', 'gridTop', 'gridLeft', 'gridRight', 'gridBottom',"targetEditObj","targetResetObj"]

        this.editData.series = _.cloneDeep(value.checkData)

        for (let i = 0; i < assignArr.length; i++) {
          this.editData[assignArr[i]] = value[assignArr[i]]
        }

        this.initdisChargeCapacityEchart(
          legendData,
          value.checkData,
          axisData,
          titleData,
          gridData
        )

        this.$forceUpdate()
      },

    // 重置
    handleDrawerReset() {

      this.editData.series = _.cloneDeep(this.originalSeries)

      this.editData.legendEditName = []
      this.editData.legendWidth = 20
      this.editData.legendHeight = 5
      this.editData.legendGap = 10
      this.editData.legendTop = 40

      this.editData.gridTop = 70
      this.editData.gridLeft = 90
      this.editData.gridRight = 90
      this.editData.gridBottom = 70

      this.editData.xMin = 0
      this.editData.xMax = 0
      this.editData.xInterval = 0
      this.editData.yMin = this.resetOriginal.yMin
      this.editData.yMax = this.resetOriginal.yMax
      this.editData.yInterval = this.resetOriginal.yInterval
      this.editData.yMin2 = this.resetOriginal.yMin2
      this.editData.yMax2 = this.resetOriginal.yMax2
      this.editData.yInterval2 = this.resetOriginal.yInterval2

      this.editData.chartTitle = 'OCV&ACR曲线'
      this.editData.XTitle = 'Date'
      this.editData.titleTop = 10
      this.editData.yTitleLetf = 60
      this.editData.yTitleRight = 60
      this.initdisChargeCapacityEchart()
    
      this.handleDrawerClose()
    },

    // 关闭
    handleDrawerClose() {
      this.drawerVisible = false
    },

    handleCancel(){
			this.tableData = []
			this.visible = false
		},
	}
}
</script>
<style lang="less" scoped=''>

.all-wrapper{
  display: flex;
  justify-content: center;
  overflow: scroll;
}

.block {
  height: fit-content;
  padding: 10px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
  position: relative;
}
.block-content {
  width: 720px;
  margin-top: 10px;
  margin-bottom: 5px;
}

.left-content {
  width: 620px;
  margin-top: 15px;
  margin-bottom: 5px;
  margin-left: 5px;
  margin-right: 10px;
}

.left-content .edit-icon{
  position: absolute;
  top: 16px;
  right: 16px;
  cursor: pointer;

  z-index: 99;
}

.right-content {
  width: calc(100% - 650px);
  margin-top: 15px;
  margin-bottom: 5px;
  margin-right: 5px;
}

.mt10 {
  margin-top: 10px;
}

.mb10 {
  margin-bottom: 10px;
}

/deep/ .ant-modal-body{
  padding: 12px !important;
  //display: flex;
  //justify-content: space-around;
  height: calc(100vh - 200px);
  //height: 600px;
  overflow-y: auto;
  &::-webkit-scrollbar {
    width: 8px;
    height: 2px;
    background: #dee4e9;
    border-radius: 6px; /*外层轨道*/
  }
  &::-webkit-scrollbar-thumb {
    display: block;
    width: 8px;
    margin: 0 auto;
    border-radius: 6px;
    background: #aaaaaa; /*内层轨道*/
  }
}
/deep/.ocvTable .ant-table-body {
	height: initial !important;
	overflow-y: scroll;
}

/deep/ .ant-descriptions-row:last-child{
  display: flex;
  justify-content: space-evenly;
}

/deep/.ant-descriptions-title{
  text-align: center;
  color: #333;
  font-size: 18px;
}

/deep/.ocvTable .ant-table-placeholder{
	display: none !important;
}
</style>