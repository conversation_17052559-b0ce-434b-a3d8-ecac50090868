{"version": 3, "sources": ["webpack://[name]/webpack/universalModuleDefinition", "webpack://[name]/webpack/bootstrap", "webpack://[name]/./sources/ext/fullscreen/index.ts"], "names": ["root", "factory", "exports", "module", "define", "amd", "window", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "isExpanded", "element", "document", "fullscreenElement", "mozFullScreenElement", "webkitFullscreenElement", "msFullscreenElement", "body", "gantt", "$services", "getService", "registerProvider", "fullscreen", "backupBodyPadding", "overflow", "padding", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "backupElementSizes", "width", "height", "top", "left", "position", "zIndex", "modified", "backupPositioning", "updateSizes", "source", "target", "expandGantt", "onFullScreenChange", "event", "$container", "ext", "getFullscreenElement", "style", "parent", "parentNode", "positions", "push", "originalPositioning", "resetParentPositioning", "setFullScreenSizes", "for<PERSON>ach", "record", "restoreParentPositioning", "restoreSizes", "setTimeout", "render", "callEvent", "cantFullscreen", "fullscreenEnabled", "webkitFullscreenEnabled", "mozFullScreenEnabled", "msFullscreenEnabled", "console", "warning", "log", "expand", "this", "requestArguments", "webkitRequestFullscreen", "Element", "ALLOW_KEYBOARD_INPUT", "requestFullscreen", "msRequestFullscreen", "mozRequestFullScreen", "apply", "collapse", "requestExitFullscreen", "msExitFullscreen", "mozCancelFullScreen", "webkitExitFullscreen", "exitFullscreen", "toggle", "$root", "attachEvent"], "mappings": ";;;;;;;;;;;;CAAA,SAAAA,EAAAC,GACA,iBAAAC,SAAA,iBAAAC,OACAA,OAAAD,QAAAD,IACA,mBAAAG,eAAAC,IACAD,OAAA,gCAAAH,GACA,iBAAAC,QACAA,QAAA,8BAAAD,IAEAD,EAAA,8BAAAC,IARA,CASCK,OAAA,WACD,mBCTA,IAAAC,KAGA,SAAAC,EAAAC,GAGA,GAAAF,EAAAE,GACA,OAAAF,EAAAE,GAAAP,QAGA,IAAAC,EAAAI,EAAAE,IACAC,EAAAD,EACAE,GAAA,EACAT,YAUA,OANAU,EAAAH,GAAAI,KAAAV,EAAAD,QAAAC,IAAAD,QAAAM,GAGAL,EAAAQ,GAAA,EAGAR,EAAAD,QA0DA,OArDAM,EAAAM,EAAAF,EAGAJ,EAAAO,EAAAR,EAGAC,EAAAQ,EAAA,SAAAd,EAAAe,EAAAC,GACAV,EAAAW,EAAAjB,EAAAe,IACAG,OAAAC,eAAAnB,EAAAe,GAA0CK,YAAA,EAAAC,IAAAL,KAK1CV,EAAAgB,EAAA,SAAAtB,GACA,oBAAAuB,eAAAC,aACAN,OAAAC,eAAAnB,EAAAuB,OAAAC,aAAwDC,MAAA,WAExDP,OAAAC,eAAAnB,EAAA,cAAiDyB,OAAA,KAQjDnB,EAAAoB,EAAA,SAAAD,EAAAE,GAEA,GADA,EAAAA,IAAAF,EAAAnB,EAAAmB,IACA,EAAAE,EAAA,OAAAF,EACA,KAAAE,GAAA,iBAAAF,QAAAG,WAAA,OAAAH,EACA,IAAAI,EAAAX,OAAAY,OAAA,MAGA,GAFAxB,EAAAgB,EAAAO,GACAX,OAAAC,eAAAU,EAAA,WAAyCT,YAAA,EAAAK,UACzC,EAAAE,GAAA,iBAAAF,EAAA,QAAAM,KAAAN,EAAAnB,EAAAQ,EAAAe,EAAAE,EAAA,SAAAA,GAAgH,OAAAN,EAAAM,IAAqBC,KAAA,KAAAD,IACrI,OAAAF,GAIAvB,EAAA2B,EAAA,SAAAhC,GACA,IAAAe,EAAAf,KAAA2B,WACA,WAA2B,OAAA3B,EAAA,SAC3B,WAAiC,OAAAA,GAEjC,OADAK,EAAAQ,EAAAE,EAAA,IAAAA,GACAA,GAIAV,EAAAW,EAAA,SAAAiB,EAAAC,GAAsD,OAAAjB,OAAAkB,UAAAC,eAAA1B,KAAAuB,EAAAC,IAGtD7B,EAAAgC,EAAA,aAIAhC,IAAAiC,EAAA,yBCvCA,SAASC,IACR,IAAMC,EAAYC,SAAuBC,mBACvCD,SAAuBE,sBACvBF,SAAuBG,yBACvBH,SAAuBI,oBACzB,SAAUL,GAAWA,IAAYC,SAASK,MAU7BC,MAAMC,UAAUC,WAAW,SACnCC,iBAAiB,aAAc,WACpC,OAASC,WAAYZ,OAGtB,IAAIa,GACHC,SAAU,KACVC,QAAS,KACTC,WAAY,KACZC,aAAc,KACdC,cAAe,KACfC,YAAa,MAGRC,GACLC,MAAO,KACPC,OAAQ,KACRC,IAAK,KACLC,KAAM,KACNC,SAAU,KACVC,OAAQ,KACRC,UAAU,GAGPC,EAAoB,KAmGxB,SAASC,EAAYC,EAAgBC,GACpCA,EAAOV,MAAQS,EAAOT,MACtBU,EAAOT,OAASQ,EAAOR,OACvBS,EAAOR,IAAMO,EAAOP,IACpBQ,EAAOP,KAAOM,EAAON,KACrBO,EAAON,SAAWK,EAAOL,SACzBM,EAAOL,OAASI,EAAOJ,OAYxB,IAAIM,GAAc,EAClB,SAASC,IAKR,IAAIC,EAJC1B,MAAM2B,aAKYnC,IAGlBgC,IACHE,EAAQ,WA1GX,WACC,IAAM5E,EAAOkD,MAAM4B,IAAIxB,WAAWyB,uBAC5B9B,EAAOL,SAASK,KACtBsB,EAAYvE,EAAKgF,MAAOlB,GACxBP,GACCC,SAAUP,EAAK+B,MAAMxB,SACrBC,QAASR,EAAK+B,MAAMvB,QAAUR,EAAK+B,MAAMvB,QAAU,KACnDC,WAAYT,EAAK+B,MAAMtB,WAAaT,EAAK+B,MAAMtB,WAAa,KAC5DC,aAAcV,EAAK+B,MAAMrB,aAAeV,EAAK+B,MAAMrB,aAAe,KAClEC,cAAeX,EAAK+B,MAAMpB,cAAgBX,EAAK+B,MAAMpB,cAAgB,KACrEC,YAAaZ,EAAK+B,MAAMnB,YAAcZ,EAAK+B,MAAMnB,YAAc,MAG5DZ,EAAK+B,MAAMvB,UACdR,EAAK+B,MAAMvB,QAAU,KAElBR,EAAK+B,MAAMtB,aACdT,EAAK+B,MAAMtB,WAAa,KAErBT,EAAK+B,MAAMrB,eACdV,EAAK+B,MAAMrB,aAAe,KAEvBV,EAAK+B,MAAMpB,gBACdX,EAAK+B,MAAMpB,cAAgB,KAExBX,EAAK+B,MAAMnB,cACdZ,EAAK+B,MAAMnB,YAAc,KAG1BZ,EAAK+B,MAAMxB,SAAW,SAEtBxD,EAAKgF,MAAMjB,MAAQ,QACnB/D,EAAKgF,MAAMhB,OAAS,QACpBhE,EAAKgF,MAAMf,IAAM,MACjBjE,EAAKgF,MAAMd,KAAO,MAClBlE,EAAKgF,MAAMb,SAAW,WACtBnE,EAAKgF,MAAMZ,OAAS,EACpBN,EAAmBO,UAAW,EAC9BC,EA3DD,SAAgCtE,GAG/B,IAFA,IAAIiF,EAASjF,EAAKkF,WACZC,KACAF,GAAUA,EAAOD,OACtBG,EAAUC,MACTzC,QAASsC,EACTI,oBAAqBJ,EAAOD,MAAMb,WAEnCc,EAAOD,MAAMb,SAAW,SACxBc,EAASA,EAAOC,WAEjB,OAAOC,EAgDaG,CAAuBtF,GAqEzCuF,IAESb,IACVA,GAAc,EACdE,EAAQ,aAtEV,WACC,IAAM5E,EAAOkD,MAAM4B,IAAIxB,WAAWyB,uBAC5B9B,EAAOL,SAASK,KAClBa,EAAmBO,WAClBd,EAAkBE,UACrBR,EAAK+B,MAAMvB,QAAUF,EAAkBE,SAEpCF,EAAkBG,aACrBT,EAAK+B,MAAMtB,WAAaH,EAAkBG,YAEvCH,EAAkBI,eACrBV,EAAK+B,MAAMrB,aAAeJ,EAAkBI,cAEzCJ,EAAkBK,gBACrBX,EAAK+B,MAAMpB,cAAgBL,EAAkBK,eAE1CL,EAAkBM,cACrBZ,EAAK+B,MAAMnB,YAAcN,EAAkBM,aAG5CZ,EAAK+B,MAAMxB,SAAWD,EAAkBC,SACxCD,GACCC,SAAU,KACVC,QAAS,KACTC,WAAY,KACZC,aAAc,KACdC,cAAe,KACfC,YAAa,MAEdU,EAAYT,EAAoB9D,EAAKgF,OACrClB,EAAmBO,UAAW,GA9EhC,SAAkCc,GACjCA,EAAUK,QAAQ,SAAAC,GACjBA,EAAO9C,QAAQqC,MAAMb,SAAWsB,EAAOJ,sBA8ExCK,CAAyBpB,GACzBA,EAAoB,KAsCnBqB,IAEDC,WAAW,WACV1C,MAAM2C,WAEPD,WAAW,WACV1C,MAAM4C,UAAUlB,GAAQ1B,MAAM4B,IAAIxB,WAAWyB,4BAI/C,SAASgB,IACR,OAAK7C,MAAM2B,cAGN3B,MAAM4B,IAAIxB,WAAWyB,0BAlLlBnC,SAAuBoD,mBAC9BpD,SAAuBqD,yBACvBrD,SAAuBsD,sBACvBtD,SAAuBuD,wBAoLPC,QAAqBC,SAAWD,QAAQE,KACjD,wFACA,IAKTpD,MAAM4B,IAAIxB,YACTiD,OAAA,WACC,IAAIR,MAIArD,KAICQ,MAAM4C,UAAU,kBAAmBU,KAAKzB,yBAA7C,CAGAL,GAAc,EAId,IAAM/B,EAAUC,SAASK,KACnBwD,EAAmB9D,EAAQ+D,yBAC9BC,QAAgCC,yBAE7BC,EAAoBlE,EAAQmE,qBACjCnE,EAAQoE,sBACRpE,EAAQ+D,yBACR/D,EAAQkE,kBAELA,GACHA,EAAkBG,MAAMrE,EAAS8D,KAGnCQ,SAAA,WACC,IAAIlB,KAICrD,KAIAQ,MAAM4C,UAAU,oBAAqBU,KAAKzB,yBAA/C,CAIA,IAAMmC,EAAyBtE,SAAuBuE,kBACpDvE,SAAuBwE,qBACvBxE,SAAuByE,sBACvBzE,SAAuB0E,eAErBJ,GACHA,EAAsBF,MAAMpE,YAG9B2E,OAAA,WACKxB,MAGCrD,IAGJ8D,KAAKS,WAFLT,KAAKD,WAMPxB,qBAAA,WACC,OAAO7B,MAAMsE,QAIftE,MAAMqD,OAAS,WACdrD,MAAM4B,IAAIxB,WAAWiD,UAGtBrD,MAAM+D,SAAW,WAChB/D,MAAM4B,IAAIxB,WAAW2D,YAGtB/D,MAAMuE,YAAY,eAhIlB,WACCvE,MAAM0B,MAAMhC,SAAU,yBAA0B+B,GAChDzB,MAAM0B,MAAMhC,SAAU,sBAAuB+B,GAC7CzB,MAAM0B,MAAMhC,SAAU,qBAAsB+B,GAE5CzB,MAAM0B,MAAMhC,SAAU,mBAAoB+B,GAC1CzB,MAAM0B,MAAMhC,SAAU,mBAAoB+B", "file": "ext/dhtmlxgantt_fullscreen.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"ext/dhtmlxgantt_fullscreen\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ext/dhtmlxgantt_fullscreen\"] = factory();\n\telse\n\t\troot[\"ext/dhtmlxgantt_fullscreen\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/codebase/\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 239);\n", "declare const gantt;\n\ninterface IBody extends HTMLElement {\n\tmsRequestFullscreen?: () => void;\n\tmozRequestFullScreen?: () => void;\n\twebkitRequestFullscreen?: (ALLOW_KEYBOARD_INPUT: any) => void;\n}\n\n\ninterface IConsole extends Console {\n\twarning?: (str: string) => void;\n}\n\ninterface IDocument extends Document {\n\tfullscreenElement?: Element;\n\tmozFullScreenElement?: Element;\n\twebkitFullscreenElement?: Element;\n\tmsFullscreenElement?: Element;\n\twebkitFullscreenEnabled?: boolean;\n\tmozFullScreenEnabled?: boolean;\n\tmsFullscreenEnabled?: boolean;\n\tmsExitFullscreen?: () => void;\n\tmozCancelFullScreen?: () => void;\n\twebkitExitFullscreen?: () => void;\n}\n\ninterface IElement extends Element {\n\tALLOW_KEYBOARD_INPUT?: boolean;\n}\n\ninterface IElementSizes extends ISizes {\n\tmodified: boolean;\n}\n\ninterface ISizes {\n\twidth: null | string;\n\theight: null | string;\n\ttop: null | string;\n\tleft: null | string;\n\tposition: null | string;\n\tzIndex: null | number;\n}\n\nfunction isExpanded() {\n\tconst element = ((document as IDocument).fullscreenElement ||\n\t\t(document as IDocument).mozFullScreenElement ||\n\t\t(document as IDocument).webkitFullscreenElement ||\n\t\t(document as IDocument).msFullscreenElement);\n\treturn !!(element && element === document.body);\n}\n\nfunction isFullscreenAvailable() {\n\treturn (document as IDocument).fullscreenEnabled ||\n\t(document as IDocument).webkitFullscreenEnabled ||\n\t(document as IDocument).mozFullScreenEnabled ||\n\t(document as IDocument).msFullscreenEnabled;\n}\n\nconst state = gantt.$services.getService(\"state\");\nstate.registerProvider(\"fullscreen\", () => {\n\treturn { fullscreen: isExpanded() };\n});\n\nlet backupBodyPadding = {\n\toverflow: null,\n\tpadding: null,\n\tpaddingTop: null,\n\tpaddingRight: null,\n\tpaddingBottom: null,\n\tpaddingLeft: null\n};\n\nconst backupElementSizes: IElementSizes = {\n\twidth: null,\n\theight: null,\n\ttop: null,\n\tleft: null,\n\tposition: null,\n\tzIndex: null,\n\tmodified: false\n};\n\nlet backupPositioning = null;\nfunction resetParentPositioning(root){\n\tlet parent = root.parentNode;\n\tconst positions = [];\n\twhile(parent && parent.style){\n\t\tpositions.push({\n\t\t\telement: parent,\n\t\t\toriginalPositioning: parent.style.position\n\t\t});\n\t\tparent.style.position = \"static\";\n\t\tparent = parent.parentNode;\n\t}\n\treturn positions;\n}\n\nfunction restoreParentPositioning(positions: any[]){\n\tpositions.forEach(record => {\n\t\trecord.element.style.position = record.originalPositioning;\n\t});\n}\n\n// expand gantt root element to fullscreen automatically\nfunction setFullScreenSizes() {\n\tconst root = gantt.ext.fullscreen.getFullscreenElement();\n\tconst body = document.body;\n\tupdateSizes(root.style, backupElementSizes);\n\tbackupBodyPadding = {\n\t\toverflow: body.style.overflow,\n\t\tpadding: body.style.padding ? body.style.padding : null,\n\t\tpaddingTop: body.style.paddingTop ? body.style.paddingTop : null,\n\t\tpaddingRight: body.style.paddingRight ? body.style.paddingRight : null,\n\t\tpaddingBottom: body.style.paddingBottom ? body.style.paddingBottom : null,\n\t\tpaddingLeft: body.style.paddingLeft ? body.style.paddingLeft : null\n\t};\n\n\tif (body.style.padding) {\n\t\tbody.style.padding = \"0\";\n\t}\n\tif (body.style.paddingTop) {\n\t\tbody.style.paddingTop = \"0\";\n\t}\n\tif (body.style.paddingRight) {\n\t\tbody.style.paddingRight = \"0\";\n\t}\n\tif (body.style.paddingBottom) {\n\t\tbody.style.paddingBottom = \"0\";\n\t}\n\tif (body.style.paddingLeft) {\n\t\tbody.style.paddingLeft = \"0\";\n\t}\n\n\tbody.style.overflow = \"hidden\";\n\n\troot.style.width = \"100vw\";\n\troot.style.height = \"100vh\";\n\troot.style.top = \"0px\";\n\troot.style.left = \"0px\";\n\troot.style.position = \"absolute\";\n\troot.style.zIndex = 1;\n\tbackupElementSizes.modified = true;\n\tbackupPositioning = resetParentPositioning(root);\n}\n\nfunction restoreSizes() {\n\tconst root = gantt.ext.fullscreen.getFullscreenElement();\n\tconst body = document.body;\n\tif (backupElementSizes.modified) {\n\t\tif (backupBodyPadding.padding) {\n\t\t\tbody.style.padding = backupBodyPadding.padding;\n\t\t}\n\t\tif (backupBodyPadding.paddingTop) {\n\t\t\tbody.style.paddingTop = backupBodyPadding.paddingTop;\n\t\t}\n\t\tif (backupBodyPadding.paddingRight) {\n\t\t\tbody.style.paddingRight = backupBodyPadding.paddingRight;\n\t\t}\n\t\tif (backupBodyPadding.paddingBottom) {\n\t\t\tbody.style.paddingBottom = backupBodyPadding.paddingBottom;\n\t\t}\n\t\tif (backupBodyPadding.paddingLeft) {\n\t\t\tbody.style.paddingLeft = backupBodyPadding.paddingLeft;\n\t\t}\n\n\t\tbody.style.overflow = backupBodyPadding.overflow;\n\t\tbackupBodyPadding = {\n\t\t\toverflow: null,\n\t\t\tpadding: null,\n\t\t\tpaddingTop: null,\n\t\t\tpaddingRight: null,\n\t\t\tpaddingBottom: null,\n\t\t\tpaddingLeft: null\n\t\t};\n\t\tupdateSizes(backupElementSizes, root.style);\n\t\tbackupElementSizes.modified = false;\n\t}\n\trestoreParentPositioning(backupPositioning);\n\tbackupPositioning = null;\n}\n\nfunction updateSizes(source: ISizes, target: ISizes) {\n\ttarget.width = source.width;\n\ttarget.height = source.height;\n\ttarget.top = source.top;\n\ttarget.left = source.left;\n\ttarget.position = source.position;\n\ttarget.zIndex = source.zIndex;\n}\n\nfunction addDOMEvents() {\n\tgantt.event(document, \"webkitfullscreenchange\", onFullScreenChange);\n\tgantt.event(document, \"mozfullscreenchange\", onFullScreenChange);\n\tgantt.event(document, \"MSFullscreenChange\", onFullScreenChange);\n\t// For IE on Win 10\n\tgantt.event(document, \"fullscreenChange\", onFullScreenChange);\n\tgantt.event(document, \"fullscreenchange\", onFullScreenChange);\n}\n\nlet expandGantt = false;\nfunction onFullScreenChange() {\n\tif (!gantt.$container) {\n\t\t// do nothing if gantt is not yet initialized\n\t\treturn;\n\t}\n\tlet event: \"onExpand\" | \"onCollapse\";\n\tconst isBodyExpanded = isExpanded();\n\n\tif (isBodyExpanded) {\n\t\tif (expandGantt) {\n\t\t\tevent = \"onExpand\";\n\t\t\tsetFullScreenSizes();\n\t\t}\n\t} else if (expandGantt) {\n\t\texpandGantt = false;\n\t\tevent = \"onCollapse\";\n\t\trestoreSizes();\n\t}\n\tsetTimeout(() => {\n\t\tgantt.render();\n\t});\n\tsetTimeout(() => {\n\t\tgantt.callEvent(event, [gantt.ext.fullscreen.getFullscreenElement()]);\n\t});\n}\n\nfunction cantFullscreen() {\n\tif (!gantt.$container) { // check is gantt initialized or not\n\t\treturn true;\n\t}\n\tif (!gantt.ext.fullscreen.getFullscreenElement()) {\n\t\treturn true;\n\t}\n\tif (!isFullscreenAvailable()) {\n\t\t// tslint:disable-next-line: no-console\n\t\tconst method = (console as IConsole).warning || console.log;\n\t\tmethod(\"The `fullscreen` feature not being allowed, or full-screen mode not being supported\");\n\t\treturn true;\n\t}\n\treturn false;\n}\n\ngantt.ext.fullscreen = {\n\texpand(): void {\n\t\tif (cantFullscreen()) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (isExpanded()) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (!gantt.callEvent(\"onBeforeExpand\", [this.getFullscreenElement()])) {\n\t\t\treturn;\n\t\t}\n\t\texpandGantt = true;\n\n\t\t// we switch body to fullscreen and then expand fullscreen element to viewport\n\t\t// we do it to correct display common elements: lightboxes, tooltip etc.\n\t\tconst element = document.body as IBody;\n\t\tconst requestArguments = element.webkitRequestFullscreen ?\n\t\t\t[(Element as unknown as IElement).ALLOW_KEYBOARD_INPUT] : [];\n\n\t\tconst requestFullscreen = element.msRequestFullscreen ||\n\t\t\telement.mozRequestFullScreen ||\n\t\t\telement.webkitRequestFullscreen ||\n\t\t\telement.requestFullscreen;\n\n\t\tif (requestFullscreen) {\n\t\t\trequestFullscreen.apply(element, requestArguments);\n\t\t}\n\t},\n\tcollapse(): void {\n\t\tif (cantFullscreen()) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (!isExpanded()) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (!gantt.callEvent(\"onBeforeCollapse\", [this.getFullscreenElement()])) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst requestExitFullscreen = (document as IDocument).msExitFullscreen ||\n\t\t\t(document as IDocument).mozCancelFullScreen ||\n\t\t\t(document as IDocument).webkitExitFullscreen ||\n\t\t\t(document as IDocument).exitFullscreen;\n\n\t\tif (requestExitFullscreen) {\n\t\t\trequestExitFullscreen.apply(document);\n\t\t}\n\t},\n\ttoggle(): void {\n\t\tif (cantFullscreen()) {\n\t\t\treturn;\n\t\t}\n\t\tif (!isExpanded()) {\n\t\t\tthis.expand();\n\t\t} else {\n\t\t\tthis.collapse();\n\t\t}\n\n\t},\n\tgetFullscreenElement(): HTMLElement {\n\t\treturn gantt.$root;\n\t},\n};\n\ngantt.expand = function() {\n\tgantt.ext.fullscreen.expand();\n};\n\ngantt.collapse = function(){\n\tgantt.ext.fullscreen.collapse();\n};\n\ngantt.attachEvent(\"onGanttReady\", addDOMEvents);"], "sourceRoot": ""}