<template>
  <div class="fitting-settings">
    <h5>拟合设置</h5>

    <div class="fitting-form-container">
      <a-form layout="horizontal">
        <a-form-item label="拟合标准" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" class="form-item">
          <a-radio-group
            v-model="localAlgorithmParams.fit_standard"
            buttonStyle="solid"
            @change="updateParams"
            class="radio-group"
          >
            <a-radio-button value="MAE">MAE</a-radio-button>
            <a-radio-button value="RMSE">RMSE</a-radio-button>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="最大迭代次数" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" class="form-item">
          <a-input-number
            v-model="localAlgorithmParams.max_iter"
            :min="1"
            :max="3000"
            :step="100"
            class="input-number"
            placeholder="1200"
            @change="updateParams"
          />
        </a-form-item>

        <a-form-item label="实测值偏差数量" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" class="form-item">
          <a-input-number
            v-model="localAlgorithmParams.n_last_points"
            :min="1"
            :max="20"
            :step="1"
            class="input-number"
            placeholder="1"
            @change="updateParams"
          />
        </a-form-item>

        <a-form-item label="预测数据点数量" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" class="form-item">
          <a-input-number
            v-model="localAlgorithmParams.n_pred_points"
            :min="1"
            :max="10"
            :step="1"
            class="input-number"
            placeholder="1"
            @change="updateParams"
          />
        </a-form-item>
      </a-form>

      <div class="action-button-container">
        <a-button
          type="primary"
          @click="startOptimization"
          :loading="optimizing"
          class="start-optimization-button"
          icon="play-circle"
        >
          启动参数寻优
        </a-button>
      </div>
    </div>
  </div>
</template>

<script>
import fittingMixin from '@/mixins/fittingMixin';
import { mapGetters } from 'vuex';

export default {
  name: 'FittingSettings',
  mixins: [fittingMixin],
  props: {
    algorithmParams: {
      type: Object,
      default: () => ({
        max_iter: 100,
        fit_standard: 'MAE',
        n_last_points: 1,
        n_pred_points: 1
      })
    },
    optimizing: {
      type: Boolean,
      default: false
    },
    dataLoaded: {
      type: Boolean,
      default: false
    }
  },
  data: () => ({
    localAlgorithmParams: null
  }),
  computed: {
    ...mapGetters(['getFittingData'])
  },
  created() {
    this.localAlgorithmParams = { ...this.algorithmParams };
  },
  watch: {
    algorithmParams: {
      handler(newParams) {
        this.localAlgorithmParams = { ...newParams };
      },
      deep: true
    }
  },
  methods: {
    updateParams() {
      this.$emit('algorithm-params-updated', { ...this.localAlgorithmParams });
    },
    startOptimization() {
      const storeData = this.getFittingData;
      const storeDataPoints = storeData.dataPoints || [];
      const dataLoaded = this.dataLoaded || (storeDataPoints && storeDataPoints.length > 0);

      if (!this.validateFittingData(dataLoaded, storeDataPoints)) return;
      this.$emit('start-optimization');
    }
  }
};
</script>

<style scoped>
.fitting-settings {
  width: 100%;
}

.fitting-form-container {
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

:deep(.ant-form) {
  width: 100%;
  max-width: 400px;
  margin-bottom: 16px;
}

.form-item {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  text-align: right;
  line-height: 32px;
  font-weight: 500;
  color: #333;
}

.input-number {
  width: 100%;
  max-width: 160px;
}

.radio-group {
  display: flex;
  justify-content: flex-start;
}

:deep(.ant-radio-button-wrapper) {
  min-width: 80px;
  text-align: center;
}

.action-button-container {
  text-align: center;
  margin-top: 8px;
  width: 100%;
}

.start-optimization-button {
  width: 180px;
  height: 40px;
  font-size: 15px;
  font-weight: 500;
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);
  transition: all 0.3s;
}

.start-optimization-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

@media (max-width: 768px) {
  :deep(.ant-form-item-label) {
    text-align: left;
  }

  .input-number {
    max-width: 100%;
  }
}
</style>