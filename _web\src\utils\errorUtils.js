/**
 * 错误处理工具函数
 * 提供统一的错误处理方法，减少组件中的重复代码
 */
import { message } from 'ant-design-vue';

/**
 * 处理API请求错误
 * @param {Error} error - 错误对象
 * @param {string} defaultMessage - 默认错误消息
 * @param {Function} callback - 错误处理后的回调函数
 */
export function handleApiError(error, defaultMessage = '请求失败', callback = null) {
  console.error(defaultMessage, error);
  
  let errorMessage = defaultMessage;
  
  if (error.response) {
    // 服务器返回了错误状态码
    const status = error.response.status;
    const data = error.response.data;
    
    if (data && data.message) {
      errorMessage = `${defaultMessage}: ${data.message}`;
    } else {
      errorMessage = `${defaultMessage}: ${status}`;
    }
  } else if (error.request) {
    // 请求已发送但没有收到响应
    errorMessage = '未收到服务器响应，请检查网络连接';
  } else {
    // 请求设置时出错
    errorMessage = `${defaultMessage}: ${error.message}`;
  }
  
  // 显示错误消息
  message.error(errorMessage);
  
  // 如果提供了回调函数，则执行
  if (callback && typeof callback === 'function') {
    callback(error, errorMessage);
  }
}

/**
 * 处理文件上传错误
 * @param {Error} error - 错误对象
 * @param {Function} callback - 错误处理后的回调函数
 */
export function handleUploadError(error, callback = null) {
  handleApiError(error, '文件上传失败', callback);
}

/**
 * 处理表单验证错误
 * @param {Object} errors - 表单验证错误对象
 * @param {string} defaultMessage - 默认错误消息
 */
export function handleFormError(errors, defaultMessage = '表单验证失败') {
  if (!errors || !errors.errorFields || errors.errorFields.length === 0) {
    message.error(defaultMessage);
    return;
  }
  
  // 获取第一个错误字段的错误信息
  const firstError = errors.errorFields[0];
  const fieldName = firstError.name[0];
  const errorMsg = firstError.errors[0];
  
  message.error(`${fieldName}: ${errorMsg}`);
}

/**
 * 处理公式解析错误
 * @param {Error} error - 错误对象
 * @param {string} formula - 公式字符串
 */
export function handleFormulaError(error, formula) {
  console.error('公式解析错误:', error, formula);
  message.error(`公式解析失败: ${error.message || '未知错误'}`);
}

/**
 * 处理Excel导入/导出错误
 * @param {Error} error - 错误对象
 * @param {string} operation - 操作类型（'import'或'export'）
 */
export function handleExcelError(error, operation = 'import') {
  const operationText = operation === 'export' ? '导出' : '导入';
  console.error(`Excel${operationText}错误:`, error);
  message.error(`Excel${operationText}失败: ${error.message || '未知错误'}`);
}

/**
 * 显示成功消息
 * @param {string} msg - 成功消息
 */
export function showSuccess(msg) {
  message.success(msg);
}

/**
 * 显示警告消息
 * @param {string} msg - 警告消息
 */
export function showWarning(msg) {
  message.warning(msg);
}

/**
 * 显示信息消息
 * @param {string} msg - 信息消息
 */
export function showInfo(msg) {
  message.info(msg);
}
