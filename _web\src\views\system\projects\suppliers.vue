<template>
<div style="background:#fff">
    <a-table
        ref="table"
        size="small"
        :rowKey="(record) => record.no"
        :pagination="false"
        :columns="columns"
        :dataSource="loadData"
        :loading="loading"
        showPagination="false">
        <span slot="sop" slot-scope="text">
          {{ 'sop_status' | dictType(text) }}
        </span>
    </a-table>
</div>
</template>

<script>
import { getSuppliers } from "@/api/modular/system/report"
export default {
    props: {
        issueId: {
            type: Number,
            default: 0
        }
    },
    data () {
        return {
            loading: true,
            columns: [],
            loadData: []
        }
    },
    methods:{
        callSupplierData(){
            this.loading = true
            let params = {issueId: this.issueId,title:''}
            getSuppliers(params)
            .then((res)=>{
                if (res.result) {
                    let statusarr = [
                        "sop"
                    ];
                    for (const item of res.data.columndata) {
                        let index = statusarr.indexOf(item.key)
                        if ( index >  -1 ) {
                            item.scopedSlots = {customRender: statusarr[index]}
                        }
                    }
                    this.columns = res.data.columndata
                    this.loadData = res.data.rowdata
                } else {
                    this.$message.error(res.message,1);
                }
                this.loading = false
                })
            .catch((err)=>{
                this.loading = false
                this.$message.error('错误提示：' + err.message,1)
            });
        },
    },
    created () {
        this.callSupplierData()
    }
}
</script>

<style lang="less" scoped=''>
/deep/.ant-table{
    margin: 0 2px;
    margin-top:2px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}
/deep/.ant-table-thead > tr > th{
    font-weight: bold;
    background: #f3f3f3 !important;
}
/deep/.ant-table-small > .ant-table-content > .ant-table-body{
    margin: 0;
}
</style>