<template>
  <a-modal title="编辑" :width="600" :visible="visible" :confirmLoading="confirmLoading" @ok="handleSubmit" @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item style="display: none;" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['id']" />
        </a-form-item>

        <a-form-item label="电池结构" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-select v-decorator="['structureType', {rules: [{required: true, message: '请选择结构类型！'}]}]">
            <a-select-option value="g_cylinder">
              G圆柱
            </a-select-option>

            <a-select-option value="c_cylinder">
              C圆柱
            </a-select-option>
            <a-select-option value="v_cylinder">
              V圆柱
            </a-select-option>


            <a-select-option value="winding">
              方形卷绕
            </a-select-option>

            <a-select-option value="lamination">
              方形叠片
            </a-select-option>
            <a-select-option value="soft_roll">
              软包
            </a-select-option>

          </a-select>


        </a-form-item>
        <a-form-item label="应用场景" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>

          <a-tree-select
            v-decorator="['scenario', {rules: [{ required: true, message: '请选择应用场景！' }]}]"
            :defaultExpandAll="true"
            :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
            :treeData="gData" placeholder="应用场景">
          </a-tree-select>

        </a-form-item>
        <a-form-item label="产品名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input placeholder="请输入产品名称" v-decorator="['productName', {rules: [{required: true, message: '请输入负极体系！'}]}]" />
        </a-form-item>

        <a-form-item label="产品等级" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-select
            placeholder="请选择产品等级"
            v-decorator="['grade', {rules: [{required: true, message: '请选择产品等级！'}]}]">

            <a-select-option value="S">
              S
            </a-select-option>


            <a-select-option value="A">
              A
            </a-select-option>

            <a-select-option value="B">
              B
            </a-select-option>
            <a-select-option value="C">
              C
            </a-select-option>

          </a-select>




        </a-form-item>


        <a-form-item label="技术名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input placeholder="请输入技术名称" v-decorator="['batteryName',
          {rules: [{required: true, message: '请输入技术名称！'}]}]" />
        </a-form-item>

        <a-form-item label="负责人(PT)" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-select
              show-search
              option-filter-prop="children"
              style="width: 100%"
              v-decorator="['pt', { rules: [{ required: true, message: '请选择负责人(PT)!' }] }]"
              @search="(value) => handleSearch(value,'userList')"
            >
              <a-select-option v-for="user in userList" :key="user.account + '-' + user.name">
                {{user.account + '-' + user.name}}
              </a-select-option>
            </a-select>

        </a-form-item>

        <a-form-item label="技术状态" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-select
            placeholder="请选择技术状态"
            v-decorator="['platformStatus', {rules: [{required: true, message: '请选择技术状态！'}]}]">

            <a-select-option value="未立项">
              未立项
            </a-select-option>

            <a-select-option value="已立项">
              已立项
            </a-select-option>

            <a-select-option value="结题">
              结题
            </a-select-option>
            <a-select-option value="暂停">
              暂停
            </a-select-option>


          </a-select>
        </a-form-item>
        <a-form-item label="立项评审" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-date-picker placeholder="请选择立项评审日期" @change="onChangeSampleDate" style="width: 100%"
                         :default-value="moment(startDate, 'YYYY-MM-DD')"
                         v-decorator="['startDate', {rules: [{required: true, message: '请选择立项评审日期!'}]}]" />
        </a-form-item>

      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import {
    sysBatteryDesignEdit
  } from '@/api/modular/system/batterydesignManage'
  import {
    getUserAllLists
  } from '@/api/modular/system/userManage'
  import moment from "moment";
  export default {
    data() {
      return {
        userList:[],
        startDate:null,
        gData: [
          {
            title: '乘用车',
            key: '乘用车',
            value: '乘用车',
          }, {
            title: '混动',
            key: '混动',
            value: '混动',
          }, {
            title: '轻型动力',
            key: '轻型动力',
            value: '轻型动力',
          }, {
            title: '商用',
            key: '商用',
            value: '商用',
            selectable:false,
            children: [
              {
                title: '客车',
                key: '商用-客车',
                value: '商用-客车'
              }, {
                title: '重卡',
                key: '商用-重卡',
                value: '商用-重卡'
              }, {
                title: '物流',
                key: '商用-物流',
                value: '商用-物流'
              }, {
                title: '工程',
                key: '商用-工程',
                value: '商用-工程'
              }
            ],
          }, {
            title: '储能',
            key: '储能',
            value: '储能',
            selectable:false,
            children: [
              {
                title: '电力',
                key: '储能-电力',
                value: '储能-电力'
              }, {
                title: '通讯',
                key: '储能-通讯',
                value: '储能-通讯'
              }, {
                title: '家用',
                key: '储能-家用',
                value: '储能-家用'
              }
            ],
          }
        ],
        labelCol: {
          xs: {
            span: 16
          },
          sm: {
            span: 8
          }
        },
        wrapperCol: {
          xs: {
            span: 20
          },
          sm: {
            span: 16
          }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      handleSearch(value, listName) {
        if (value != '' && value != null) {
          getUserAllLists({account: value}).then((res) => {
            this[listName] = res.data
          })
        }

      },
      moment,
      // 初始化方法
      edit(record) {
        this.visible = true
        this.startDate = record.startDate
        setTimeout(() => {

          this.form.setFieldsValue(record)
          if(null != record.ptId){
            this.userList.push({account: record.ptId, name: record.pt})
            this.form.setFieldsValue({pt:record.ptId+'-'+record.pt})
          }

        }, 100)
      },
      handleSubmit() {
        const {
          form: {
            validateFields
          }
        } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            values.startDate = this.startDate

            values.ptId = values.pt.split('-')[0]
            values.pt = values.pt.split('-')[1]
            sysBatteryDesignEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.visible = false
                this.confirmLoading = false
                this.$emit('ok', values)
              } else {
                this.$message.error('编辑失败：' + res.message)
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangeSampleDate(date, dateString) {
        if (date == null) {
          this.startDate = ''
        } else {
          this.startDate = moment(date).format('YYYY-MM-DD')
        }
      },
      handleCancel() {
        this.userList = []
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
