<template>
    <div class="m_text">
        <div ref="temp" :class="['temp',needExpend ? 'ellipsis':'']">
            
            <a-popover placement="bottom" v-if="needExpend">
                <template slot="content" >
                    <div style="width:300px">
                        <div v-for="(item,i) in sourceText" :key="i" v-html="item"></div>
                    </div>
                </template>
                <textarea v-model="text"></textarea>
                <!-- <span class="collapse" v-if="needExpend">...</span> -->
            </a-popover>
            <div v-else v-html="text"></div>
        </div>
  </div>
</template>

<script>
    /* eslint-disable */
    export default {
        props: {
            // 文本内容
            text: {
                type: String,
                default: '',
            },
            sourceText: {
                type: Array,
                default: []
            },
        },
        data() {
            return {
                // 是否需要展开
                needExpend: false,
                // 收起时的文本
                shortText: '',
            }
        },
        mounted() {
            this.init()
        },
        watch: {
            text() {
                this.init()
            },
        },
        methods: {
            // 展开
            showMore() {
                this.expend = true
            },
            // 收起
            showLess() {
                this.expend = false
            },
            init() {
                let text = this.text //.replace(/\r|\n/ig, "")
                // 一行的宽度
                const height = parseInt(window.getComputedStyle(this.$refs.temp).height)
                //alert(height)
                // 字体的大小
                const fontSize = parseInt(
                    window.getComputedStyle(this.$refs.temp).fontSize
                )
                if (height > (1.5 * fontSize * 3)) {
                    this.needExpend = true
                } else {
                    this.needExpend = false
                }
            },
        },
    }
</script>

<style lang="less" scoped>
    .m_text {
        font-size: 12px;
        text-align: left;
        padding-left: 2px;
        word-break: break-all;
        line-height: 1.5em;
        align-items: center;
        display: flex;
        height: 100%;
        background: initial;
        
        .temp {
            width: 100%;
            position: relative;
            background: inherit;
        }
        .collapse {
            position: absolute;
            right: 0;
            bottom: 0;
            line-height: 1.5em;
            cursor: pointer;
            background: inherit;
            letter-spacing: 2px;
            .view {
                color: rgb(121, 199, 244);
            }
        }
    }
    .ellipsis {
        overflow: hidden;
        display: -webkit-inline-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
    }
</style>