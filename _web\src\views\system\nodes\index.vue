<template>
<div style="padding-top:8px">
    <a-button type="primary" @click="doSync" style="margin-bottom:8px;margin-right:8px" :loading="syncloading">
      同步
    </a-button>
    <a-button type="primary" @click="$refs.addForm.add()" style="margin-bottom:8px;margin-right:8px">
      新增
    </a-button>
    <!-- <a-button type="primary" @click="doSave" style="margin-bottom:8px" :loading="saveloading">
      保存
    </a-button> -->
    <a-spin :spinning="loading">
        <dragTreeTable
                :data='treeData'
                id="datatree "
                :onDrag="onTreeDataChange"
                :fixed="true"
                :isdraggable="true" ref="datatree">
                <template #action="{row}">
                    <div @click.stop>
                        <a @click="$refs.editForm.edit(row)">编辑</a>
                        &nbsp;
                        <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => doDel(row)">
                            <a>删除</a>
                        </a-popconfirm>
                    </div>
                </template>
        </dragTreeTable>
    </a-spin>
    <add-form ref="addForm" @ok="handleOk" />
    <edit-form ref="editForm" @ok="handleOk" />
</div>
</template>

<script>
import dragTreeTable from "drag-tree-table";
import { getNodeList, sysNodeDelete, sysNodeSync, sysNodeSave } from '@/api/modular/system/nodeManage'
import addForm from './addForm'
import editForm from './editForm'
export default {
    components: {
      dragTreeTable,
      addForm,
      editForm,
    },
    data() {
        return {
            loading:true,
            syncloading:false,
            saveloading:false,
            //from:[],
            treeData: {
                columns: [
					{
					type: 'selection',
					field: 'name',
					title: '类名',
					width: 200,
					align: 'left',
					formatter: (item) => {
						return '<a>'+item.name+'</a>'
					}
					},
					{
						field: 'id',
						title: '类名id',
						width: 200,
						align: 'center',
					},
					{
						type: 'action',
						width: 350,
						align: 'center',
						title: '操作'
					},
				],
                lists: [],
            }
        }
    },
    methods: {
        onTreeDataChange(list, from, to, where) {
           /*  console.log(from)
            console.log(to) */
            //this.from = from
            this.treeData.lists = list;
            this.doSave(from)
        },
        
        doDel(row){
            const {id,nodeId} = row
            this.loading = true
            sysNodeDelete({id:id,nodeId:nodeId}).then((res) => {
                if (res.success) {
                    this.callNodeList()
                }else{
                    this.$message.error('删除失败：' + res.message)
                }
                setTimeout(() => {
                    this.loading = false
                }, 1000)
            })
            .catch((err) => {
                setTimeout(() => {
                    this.loading = false
                }, 1000)
                this.$message.error('出现异常：' + err.message)
            })
        },
        callNodeList() {
            this.loading = true
            getNodeList({}).then((res) => {
                if (res.success) {
                    this.treeData.lists = res.data
                }else{
                    this.$message.error('获取失败：' + res.message)
                }
                this.loading = false
            })
            .catch((err) => {
                 this.loading = false
                this.$message.error('出现异常：' + err.message)
            })
	    },
        doSync(){
            this.loading = true
            this.syncloading = true
            sysNodeSync({}).then((res) => {
                if (res.success) {
                    this.callNodeList()
                }else{
                    this.$message.error('获取失败：' + res.message)
                }
                setTimeout(() => {
                    this.loading = false
                     this.syncloading = false
                }, 3000)
            })
            .catch((err) => {
                setTimeout(() => {
                    this.loading = false
                    this.syncloading = false
                }, 3000)
                this.$message.error('出现异常：' + err.message)
            })
        },
        doSave(from){
            if (!from) {
                return false;
            }
            this.loading = true
            this.saveloading = true
            const {nodeId,parent_id} = from
            sysNodeSave({nodeId:nodeId,parent_id:parent_id}).then((res) => {
                if (res.success) {
                    //this.callNodeList()
                }else{
                    this.$message.error('保存失败：' + res.message)
                }
                setTimeout(() => {
                    this.loading = false
                    this.saveloading = false
                }, 3000)
            })
            .catch((err) => {
                setTimeout(() => {
                    this.loading = false
                     this.saveloading = false
                }, 3000)
                this.$message.error('出现异常：' + err.message)
            })
        },
        handleOk () {
            this.callNodeList()
        }
    },
    created(){
        this.callNodeList()
    }
}
</script>

<style lang='less' scoped=''>
/deep/.drag-tree-table{
    margin: 0;
}
/deep/.drag-tree-table-header,.tree-row{
    height: auto;
    line-height: inherit;
	font-size: 14px;
	font-weight: initial;
	color: #000;
}
.drag-tree-table-header{
	background: #fafafa;
	border-bottom: 1px solid #e8e8e8;
}.tree-column{
	padding: 2px 0 !important;
}.tree-row {
		line-height: initial !important;
	}.drag-tree-table-header{
    height: initial !important;
  }
</style>