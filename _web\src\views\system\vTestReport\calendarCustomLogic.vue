<template>
  <div class="wrapper">
    <!-- 面包屑start -->
    <a-breadcrumb separator=">" class="fs12">
      <a-breadcrumb-item>
        <a-popconfirm
          title="未提交当前处理逻辑，确认返回吗？"
          okText="确定"
          cancelText="取消"
          @confirm="goBackPreviousPath">
          <a><a-icon type="rollback" class="rollback-icon"/>日历寿命在线测试报告</a>
        </a-popconfirm>
      </a-breadcrumb-item>
      <a-breadcrumb-item>自定义处理逻辑</a-breadcrumb-item>
    </a-breadcrumb>
    <!-- 面包屑end -->

    <div class="flex-sb-center-row">
      <div class="head-title">
        <div class="line mr10"></div>
        <span class="title">{{ title }}</span>
      </div>
      <div style="position: relative; display: flex; align-items: center;">
        <div class="mr10" style="align-items: center;">
          DCIR计算方案：
          <a-radio-group size="small" :value="calendarParam.dcirCalcType || 'DongLi'" @change="dcirCalcTypeChange">
            <a-radio-button value="DongLi">动力</a-radio-button>
            <a-radio-button value="V">V圆柱</a-radio-button>
          </a-radio-group>
        </div>
        <div class="normal-btn fs12" :class="{'streamer-btn anima': verifyPassed}" @click="handleAllOk">
          完成模型搭建
        </div>
      </div>
    </div>

    <div class="table-wrapper">
      <a-table class="rpt-table"
               :data-source="calendarParam.rptParamList"
               :columns="rptParamColumns"
               :pagination="paginationConfig"
               :rowKey="record => record.rpt"
               bordered>

<!--        <template slot="select" slot-scope="text, record, index, columns">-->
<!--          <a-tooltip title="选择">-->
<!--            <a-icon class="btn-icon ml2" type="edit" @click="selectSample(record.rpt)"/>-->
<!--          </a-tooltip>-->
<!--          <a-tooltip title="删除">-->
<!--            <a-icon class="btn-icon ml2" type="delete" @click="deleteSample(record.rpt)"/>-->
<!--          </a-tooltip>-->
<!--        </template>-->

        <template slot="orderDataList" slot-scope="text, record, index, columns">
          <!-- 全展示 -->
          <div  v-if="record.orderDataList.length <= 2 || record.isUnfolded">
            <div class="samples-block" v-for="(item, index) in record.orderDataList"
                 :style="{ borderBottom : index === record.orderDataList.length - 1 ? 'none' :  '1px solid #e8e8e8' }">
              <a v-if="item.flowId" style="text-align: center" @click="openStepData(item)">
                {{ item ? item.celltestcode + (item.day ? " (" + item.day + ")" : "") : "" }}
              </a>
              <span v-else style="text-align: center">
                {{ item ? item.celltestcode + (item.day ? " (" + item.day + ")" : "") : "" }}
              </span>
            </div>
            <div v-if="record.orderDataList.length > 2" class="shrink-btn" @click="handleFold(record)">
              {{record.isUnfolded ? '收起' : `${'+'}${record.orderDataList.length - 2} 展开`}}
            </div>
          </div>
          <!-- 展示前2个 -->
          <div v-else>
            <div class="samples-block" v-for="(item, index) in record.orderDataList.slice(0, 2)"
                 :style="{ borderBottom : index === record.orderDataList.length - 1 ? 'none' :  '1px solid #e8e8e8' }">
              <a v-if="item.flowId" style="text-align: center" @click="openStepData(item)">
                {{ item ? item.celltestcode + (item.day ? " (" + item.day + ")" : "") : "" }}
              </a>
              <span v-else style="text-align: center">
                {{ item ? item.celltestcode + (item.day ? " (" + item.day + ")" : "") : "" }}
              </span>
            </div>
            <div v-if="record.orderDataList.length > 2" class="shrink-btn" @click="handleFold(record)">
              {{record.isUnfolded ? '收起' : `${'+'}${record.orderDataList.length - 2} 展开`}}
            </div>
          </div>
        </template>
        <template slot="retentionStep" slot-scope="text, record, index, columns">
          <a-input class="input"
                   v-model="record.retentionStep"
                   @keyup="verifyStepNumber(record.rpt, 'retentionStep')"
                   @paste="copyFromExcel($event, record.rpt, 4)"/>
        </template>
        <template slot="retentionStepTitle">
          容量&能量保持率工步号
          <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
            <a-icon type="question-circle" style="color: #1890ff;"/>
            <template slot="title">该工步号的第零天的容量&能量数据会乘以存储SOC值</template>
          </a-tooltip>
        </template>
        <template slot="recoveryStep" slot-scope="text, record, index, columns">
          <a-input class="input"
                   v-model="record.recoveryStep"
                   @keyup="verifyStepNumber(record.rpt, 'recoveryStep')"
                   @paste="copyFromExcel($event, record.rpt, 5)"/>
        </template>
        <template slot="recoveryStepTitle">
          <a-input class="input" v-model="calendarParam.recoveryStepTiTle" placeholder="示例：2C倍率" @blur="_handleVerify"/>
          容量&能量恢复率工步号
          <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
            <a-icon type="question-circle" style="color: #1890ff;"/>
            <template slot="title">
              <span>请输入容量&能量恢复率工步号；<br/>如是多个工步号，需以逗号,隔开，计算平均值</span>
            </template>
          </a-tooltip>
          <a-tooltip v-if="dchCeStepNum === 0">
            <template slot="title">增加放电容量&能量工步号列</template>
            <a-icon class="ml10" type="plus" style="color: #1890ff;" @click="changeDchCeStepStrList(false)"/>
          </a-tooltip>
        </template>
        <template v-for="(item, i) in dchCeStepIndexArr" :slot="`dchCeStepTitle_${i}`">
          <a-input class="input" v-model="calendarParam.dchCeStepTiTleList[i]" placeholder="示例：2C倍率" @blur="_handleVerify"/>
          放电容量&能量工步号
          <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
            <a-icon type="question-circle" style="color: #1890ff;"/>
            <template slot="title">
              <span>请输入放电容量&能量工步号；<br/>如是多个工步号，需以逗号,隔开，计算平均值</span>
            </template>
          </a-tooltip>
          <a-tooltip v-if="i === calendarParam.dchCeStepTiTleList.length - 1">
            <template slot="title">增加放电容量&能量工步号列</template>
            <a-icon class="ml10" type="plus" style="color: #1890ff;" @click="changeDchCeStepStrList(false)"/>
          </a-tooltip>
          <a-tooltip v-if="i === calendarParam.dchCeStepTiTleList.length - 1">
            <template slot="title">删除放电容量&能量工步号列</template>
            <a-icon class="ml10" type="minus" style="color: #1890ff;" @click="changeDchCeStepStrList(true)"/>
          </a-tooltip>
        </template>
        <template slot="dchCeStep" slot-scope="text, record, index, columns">
          <a-input class="input"
                   v-model="record.dchCeStepStrList[columns.dataIndex.replace('dchCeStepStrList[','').charAt(0)]"
                   @keyup="record.dchCeStepStrList[columns.dataIndex.replace('dchCeStepStrList[','').charAt(0)] = (record.dchCeStepStrList[columns.dataIndex.replace('dchCeStepStrList[','').charAt(0)] + '').replaceAll(/[\t\n\s]/g, ',').replaceAll(/[^0-9,，]/g, '').replaceAll(/[，]/g, ',')"
                   @blur="_handleVerify"
                   @paste="copyFromExcel($event, record.rpt, 6 + Number(columns.dataIndex.replace('dchCeStepStrList[','').charAt(0)))"/>
        </template>
        <template v-for="(item, i) in dcirTitleIndexArr" :slot="`dcirTitle_${i}`">
          <div class="flex-sb-center-row">
            <a-input class="input" style="height: 100%;padding: 0" v-model="calendarParam.dcirTiTleList[i]" @blur="_handleVerify" placeholder="示例：XX SOC XX s" />
            <a-tooltip v-if="i === calendarParam.dcirTiTleList.length - 1 && i < 3">
              <template slot="title">增加DCIR参数组</template>
              <a-icon class="ml10" type="plus" style="color: #1890ff;" @click="changeDcirParamList(false)"/>
            </a-tooltip>
            <a-tooltip v-if="i === calendarParam.dcirTiTleList.length - 1 && i > 0">
              <template slot="title">删除DCIR参数组</template>
              <a-icon class="ml10" type="minus" style="color: #1890ff;" @click="changeDcirParamList(true)"/>
            </a-tooltip>
          </div>
        </template>
        <template slot="dcirStepParam" slot-scope="text, record, index, columns">
          <a-input class="input"
                   v-model="record.dcirStepParamList[columns.dataIndex.replace('dcirStepParamList[','').charAt(0)][columns.dataIndex.replace('dcirStepParamList[','').substring(3)]"
                   @keyup="record.dcirStepParamList[columns.dataIndex.replace('dcirStepParamList[','').charAt(0)][columns.dataIndex.replace('dcirStepParamList[','').substring(3)] = (record.dcirStepParamList[columns.dataIndex.replace('dcirStepParamList[','').charAt(0)][columns.dataIndex.replace('dcirStepParamList[','').substring(3)] + '').replaceAll(columns.dataIndex.includes('dchStepTime') ? /[^0-9:.]/g : /[^0-9]/g, '')"
                   @blur="_handleVerify"
                   @paste="copyFromExcel($event, record.rpt, null, columns.dataIndex)"/>
        </template>
        <template slot="dchStepTimeTitle">
          放电工步时间
          <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
            <a-icon type="question-circle" style="color: #1890ff;"/>
            <template slot="title">
                  <span>
                    未填写放电工步时间默认取工步数据表数据；<br/>
                    填写了放电工步时间则取详细数据表数据；<br/>
                    格式：HH:mm:ss.SSS，精确匹配；例如：0:00:10.000
                  </span>
            </template>
          </a-tooltip>
        </template>
        <template slot="standardStep" slot-scope="text, record, index, columns">
          <a-radio :checked="record.rpt === calendarParam.standardRptIndex" @change="onRadioChange(record.rpt)"></a-radio>
        </template>
        <template slot="standardStepTitle">
          基准工步
          <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
            <a-icon type="question-circle" style="color: #1890ff;"/>
            <template slot="title">
								<span>
									未填写的工步号以选中的行为基准进行数据建模；<br/>
                  工步号可从Excel复制（结构需与PBI一致，为五列信息）
								</span>
            </template>
          </a-tooltip>
        </template>
        <template slot="chCeStep" slot-scope="text, record, index, columns">
          <a-input class="input"
                   v-model="calendarParam.rptParamList[record.rpt].chCeStep"
                   @keyup="calendarParam.rptParamList[record.rpt].chCeStep = (calendarParam.rptParamList[record.rpt].chCeStep + '').replaceAll(/[\t\n\s]/g, ',').replaceAll(/[^0-9,，]/g, '').replaceAll(/[，]/g, ',')"
                   @blur="_handleVerify"/>
        </template>
        <template slot="chCeStepTitle">
          充电容量&恒流比工步号
          <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
            <a-icon type="question-circle" style="color: #1890ff;"/>
            <template slot="title">
              <span>充电容量&恒流比工步号非必填；<br/>如是多个工步号，需以逗号,隔开，计算平均值</span>
            </template>
          </a-tooltip>
        </template>
      </a-table>
    </div>

    <step-data ref="stepData"></step-data>

  </div>
</template>
<script>
import stepData from "../lims/folder/stepData";
import {mapGetters} from "vuex";
import {get, commitQueryParam} from "@/api/modular/system/testProgressManager";
import jsonBigint from "json-bigint";

export default {
  name: "calendarCustomLogic",
  components: {
    stepData
  },
  data() {
    return {
      // ordTaskId 或 testProgressId
      id: null,
      // 标题
      title: '自定义处理逻辑',
      // 分页器设置
      paginationConfig: {
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '30', '40', '50'], // 显示的每页数量选项
        size: "small",
        showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
      },
      dcirNum: 1,
      dcirTitleIndexArr: [0],
      dchCeStepNum: 0,
      dchCeStepIndexArr: [],
      // rpt数据表头
      rptParamColumns: [
        {
          title: "RPT",
          align: "center",
          width: 100,
          dataIndex: "rpt",
        },
        {
          title: "Days",
          align: "center",
          width: 100,
          dataIndex: "day",
          scopedSlots: {customRender: "day"}
        },
        {
          title: "Recharge Days",
          align: "center",
          width: 100,
          dataIndex: "rechargeDay",
          scopedSlots: {customRender: "rechargeDay"}
        },
        {
          title: "样品",
          align: "center",
          width: 230,
          dataIndex: "orderDataList",
          scopedSlots: {customRender: "orderDataList"}
        },
        {
          align: "center",
          width: 100,
          dataIndex: "retentionStep",
          scopedSlots: {customRender: "retentionStep", title: "retentionStepTitle"}
        },
        {
          align: "center",
          width: 120,
          dataIndex: "recoveryStep",
          scopedSlots: {customRender: "recoveryStep", title: "recoveryStepTitle"}
        },
        {
          children: [
            {
              title: "DCIR搁置工步号",
              width: 100,
              align: "center",
              dataIndex: "dcirStepParamList[0].restStep",
              scopedSlots: {customRender: "dcirStepParam"}
            },
            {
              title: "DCIR放电工步号",
              width: 100,
              align: "center",
              dataIndex: "dcirStepParamList[0].dchStep",
              scopedSlots: {customRender: "dcirStepParam"}
            },
            {
              width: 130,
              align: "center",
              dataIndex: "dcirStepParamList[0].dchStepTime",
              scopedSlots: {
                title: "dchStepTimeTitle",
                customRender: "dcirStepParam"
              }
            }
          ],
          scopedSlots: {
            title: "dcirTitle_0"
          }
        },
        {
          align: "center",
          width: 100,
          scopedSlots: {
            customRender: "standardStep",
            title: "standardStepTitle"
          }
        },
        {
          dataIndex: "chCeStep",
          align: "center",
          width: 120,
          scopedSlots: {
            title: "chCeStepTitle",
            customRender: "chCeStep"
          }
        },
      ],
      // 日历寿命测试管理对象ID
      testProgressId: null,
      // 是否已存在自定义建模
      customLogicFlag: null,
      // 日历寿命在线报告查询参数
      calendarParam: {
        rptParamList: [], // RPT工步参数列表
        dcirTiTleList: [""], // DCIR标题列表
        standardRptIndex: null,
        dchCeStepTiTleList: [], // 放电工步标题信息列表
      },
      verifyPassed: false,
      // 查询记录
      outQueryFlowRecord: null,
    }
  },
  computed: {
    // 从 Vuex store 中获取参数
    ...mapGetters(["testTaskFilterData", "testTaskId"]),
  },
  created() {
    this.getCalendarParam()
  },
  mounted() {
  },
  destroyed() {
    window.sessionStorage.removeItem('testProgressId')
  },

  methods: {
    getCalendarParam() {
      let json = jsonBigint({storeAsString: true})

      // 从在线报告页面进入
      if (this.testTaskFilterData != null) {
        this.customLogicFlag = this.testTaskId
        this.calendarParam = json.parse(this.testTaskFilterData)
        this.initStepData()

        this.$store.commit("setTaskID", null)
        this.$store.commit("setTaskFilterData", null)
      } else {
        // 刷新页面需要重新查询testProgress对象
        get({ id: this.$route.query.testProgressId }).then(res => {
          if (res.success) {
            this.customLogicFlag = res.data.customLogicFlag
            this.calendarParam = json.parse(res.data.queryParam)
            this.initStepData()
          }
        })
      }

    },
    initStepData() {
      // 为防止DCIR参数组列渲染错误，先补充dcirStepParamList
      this.changeRptParamColumns(true)

      let rptParamList = this.calendarParam.rptParamList
      if (this.customLogicFlag !== 1) {
        // 暂无自定义参数，给第一第二行工步数据赋值
        if (rptParamList.length > 0) {
          rptParamList[0].retentionStep = rptParamList[0].retentionStepMap['2nd']
          rptParamList[0].recoveryStep = rptParamList[0].recoveryStepMap['2nd']
          if (rptParamList[0].holdStepMap['2nd'] || rptParamList[0].dischargeStepMap['2nd']) {
            rptParamList[0].dcirStepParamList = [{restStep: rptParamList[0].holdStepMap['2nd'], dchStep: rptParamList[0].dischargeStepMap['2nd'], dchStepTime: rptParamList[0].dischargeStepTimeMap['2nd']}]
          }
        }
        if (rptParamList.length > 1) {
          this.calendarParam.standardRptIndex = 1
          rptParamList[1].retentionStep = rptParamList[1].retentionStepMap['2nd']
          rptParamList[1].recoveryStep = rptParamList[1].recoveryStepMap['2nd']
          if (rptParamList[1].holdStepMap['2nd'] || rptParamList[1].dischargeStepMap['2nd']) {
            rptParamList[1].dcirStepParamList = [{restStep: rptParamList[1].holdStepMap['2nd'], dchStep: rptParamList[1].dischargeStepMap['2nd'], dchStepTime: rptParamList[1].dischargeStepTimeMap['2nd']}]
          }
        }
      } else {
        const isV = this.calendarParam.dcirCalcType === 'V';
        // 历史建模参数赋值 注意历史结构参数赋值
        for (let i = 0; i < rptParamList.length; i++) {
          let rptParam = rptParamList[i]
          if (!rptParam.retentionStep && rptParam.retentionStepMap['custom']) {
            rptParam.retentionStep = rptParam.retentionStepMap['custom']
          }
          if (!rptParam.recoveryStep && rptParam.recoveryStepMap['custom']) {
            rptParam.recoveryStep = rptParam.recoveryStepMap['custom']
          }
          if (rptParam.dcirStepParamList.length === 1 && !rptParam.dcirStepParamList[0].dchStep) {
            rptParam.dcirStepParamList[0] = {
              restStep: rptParam.holdStepMap['custom'],
              dchStep: isV ? rptParam.dchStep1 : rptParam.dischargeStepMap['custom'],
              dchStepTime: isV ? rptParam.dchStepTime1 : rptParam.dischargeStepTimeMap['custom'],
              dchStep2: rptParam.dchStep2,
              dchStepTime2: rptParam.dchStepTime2
            }
          }
        }
      }
    },
    commitCustomLogic() {
      // 规范 工步号列表
      this.handleRecoveryStep()

      window.sessionStorage.setItem('param-committed', 'start');
      // 提交参数
      commitQueryParam(this.calendarParam, this.$route.query.testProgressId)
        .then((res) => {
        // 请求成功后设置 sessionStorage 标志
        window.sessionStorage.setItem('param-committed', res.data);
        })
        .catch(() => {
        // 请求失败处理
        window.sessionStorage.setItem('param-committed', 'false');
        });

      // 返回在线报告界面
      this.goBackPreviousPath()
    },
    handleRecoveryStep() {
      let rptParamList = this.calendarParam.rptParamList
      if (Array.isArray(rptParamList) && rptParamList.length > 0) {
        for (let i = 0; i < rptParamList.length; i++) {
          let rptStepParam = rptParamList[i]
          if (rptStepParam.recoveryStep) {
            rptStepParam.recoveryStep = (rptStepParam.recoveryStep + "")
                .replaceAll(/[\t\n\s]/g, ',').replaceAll(/[^0-9,]/g, ',') // 替换所有非法字符为英文逗号
                .replaceAll(/,+/g, ",") // 将连续的逗号替换为单个逗号
                .replaceAll(/^,|,$/g, "") // 去除字符串开头和结尾的逗号

            rptStepParam.recoveryStepList = rptStepParam.recoveryStep.split(",")
          } else {
            rptStepParam.recoveryStepList = []
          }

          if (Array.isArray(rptStepParam.dchCeStepStrList)) {
            rptStepParam.dchCeStepList = Array.from({length:rptStepParam.dchCeStepStrList.length}, () => ([]))
            for (let j = 0; j < rptStepParam.dchCeStepStrList.length; j++) {
              rptStepParam.dchCeStepStrList[j] = (rptStepParam.dchCeStepStrList[j] + "")
                  .replaceAll(/[\t\n\s]/g, ',').replaceAll(/[^0-9,]/g, ',') // 替换所有非法字符为英文逗号
                  .replaceAll(/,+/g, ",") // 将连续的逗号替换为单个逗号
                  .replaceAll(/^,|,$/g, "") // 去除字符串开头和结尾的逗号

              rptStepParam.dchCeStepList[j] = rptStepParam.dchCeStepStrList[j].split(",")
            }
          } else {
            rptStepParam.dchCeStepList = []
          }
          if (rptStepParam.chCeStep) {
            rptStepParam.chCeStep = (rptStepParam.chCeStep + "")
                .replaceAll(/[\t\n\s]/g, ',').replaceAll(/[^0-9,]/g, ',') // 替换所有非法字符为英文逗号
                .replaceAll(/,+/g, ",") // 将连续的逗号替换为单个逗号
                .replaceAll(/^,|,$/g, "") // 去除字符串开头和结尾的逗号

            rptStepParam.chCeStepList = rptStepParam.chCeStep.split(",")
          } else {
            rptStepParam.chCeStepList = []
          }
        }
      }
    },
    goBackPreviousPath() {
      this.$router.go(-1)
    },
    handleFold(record) {
      // 折叠状态变更
      record.isUnfolded = !record.isUnfolded
      // 强制刷新页面
      this.$forceUpdate()
    },
    verifyStepNumber(rptIndex, type) {
      if (this.calendarParam.rptParamList[rptIndex][type]) {
        this.calendarParam.rptParamList[rptIndex][type] = (this.calendarParam.rptParamList[rptIndex][type] + "").replaceAll(type === "recoveryStep" ? /[^0-9,，]/g : /[^0-9]/g, "")
      }

      this._handleVerify()
    },
    onRadioChange(rpt) {
      // 选择当前行索引为基准工步索引
      this.calendarParam.standardRptIndex = rpt
      // 强制刷新页面
      this.$forceUpdate()
    },

    copyFromExcel(event, rptIndex, startColumnIndex, dcirDataIndex) {
      const dchColNum = Array.isArray(this.calendarParam.dchCeStepTiTleList) ? this.calendarParam.dchCeStepTiTleList.length : 0
      const isV = this.calendarParam.dcirCalcType === 'V';
      // excel复制末尾会有换行符，split后数组多一个空串，先去除
      let rows = event.clipboardData.getData("text").replace(/[\n]$/, "").split("\n")

      let startDcirType
      let startDcirIndex = 0
      if (startColumnIndex === null && dcirDataIndex) {
        startDcirType = dcirDataIndex.replace('dcirStepParamList[','').substring(3)
        let keyIndex
        if (isV) {
          switch (startDcirType) {
            case 'dchStepTime2':
              keyIndex = 4;
              break;
            case 'dchStep2':
              keyIndex = 3;
              break;
            case 'dchStepTime':
              keyIndex = 2;
              break;
            default:
              keyIndex = 1;
          }
        } else {
          keyIndex = startDcirType === 'dchStepTime' ? 3 : startDcirType === 'dchStep' ? 2 : 1
        }

        startDcirIndex = Number(dcirDataIndex.replace('dcirStepParamList[','').charAt(0))
        startColumnIndex = 5 + dchColNum + startDcirIndex * (isV ? 4 : 3) + keyIndex
      }

      let firstData
      // 起始行：rptIndex，结束行：math.min(rptIndex + rows.length, this.calendarParam.rptParamList.length - 1)
      for (let i = rptIndex; i < this.calendarParam.rptParamList.length && i < rptIndex + rows.length; i++) {
        let rowList = rows[i-rptIndex].split("\t")
        if (i === rptIndex) {
          let type = ''
          if (startColumnIndex <= 5 + dchColNum) {
            type = this.rptParamColumns[startColumnIndex].dataIndex
          } else {
            type = startDcirType
          }
          firstData = rowList[0].replaceAll(type === "recoveryStep" || type.includes('dchCeStepStrList') ? /[^0-9,，]/g : type.includes("dchStepTime") ? /[^0-9:.]/g : /[^0-9]/g, "")
        }

        // 起始列：dataIndex所在列，结束列：math.min(startColumnIndex + rowList.length, 5 + dchColNum + (isV ? 4 : 3) * this.dcirNum)
        for (let j = startColumnIndex; j <= (5 + dchColNum + (isV ? 4 : 3) * this.dcirNum) && j < startColumnIndex + rowList.length; j++) {
          if (j <= 5 + dchColNum) {
            const type = this.rptParamColumns[j].dataIndex
            if (type.includes('dchCeStepStrList')) {
              const dchColIndex = Number(type.replace('dchCeStepStrList[','').charAt(0))
              this.calendarParam.rptParamList[i].dchCeStepStrList[dchColIndex] = rowList[j-startColumnIndex].replaceAll(/[^0-9,，]/g, "")
            } else {
              this.calendarParam.rptParamList[i][type] = rowList[j-startColumnIndex].replaceAll(type === "recoveryStep" ? /[^0-9,，]/g : /[^0-9]/g, "")
            }
          } else {
            const dcirIndex = Math.floor((j - 6 - dchColNum) / (isV ? 4 : 3))
            let type = this.rptParamColumns[6 + dchColNum].children[(j - 6 - dchColNum) % (isV ? 4 : 3)].dataIndex
            type = type.replace('dcirStepParamList[','').substring(3)
            // console.log("type: ", type)
            this.calendarParam.rptParamList[i].dcirStepParamList[dcirIndex][type] = rowList[j-startColumnIndex].replaceAll(type.includes("dchStepTime") ? /[^0-9:.]/g : /[^0-9]/g, "")
          }

        }
      }

      // 解决第一个单元格被覆盖的问题
      setTimeout(() => {
        if (startColumnIndex <= 5 + dchColNum) {
          const type = this.rptParamColumns[startColumnIndex].dataIndex
          if (type.includes('dchCeStepStrList')) {
            const dchColIndex = Number(type.replace('dchCeStepStrList[','').charAt(0))
            this.calendarParam.rptParamList[rptIndex].dchCeStepStrList[dchColIndex] = firstData
          } else {
            this.calendarParam.rptParamList[rptIndex][type] = firstData
          }
        } else {
          this.calendarParam.rptParamList[rptIndex].dcirStepParamList[startDcirIndex][startDcirType] = firstData
        }

        this._handleVerify()
      }, 10)
    },

    openStepData(record, flag) {
      this.outQueryFlowRecord = record

      //历史数据处理
      if (null == record.flowInfoList && !flag) {
        this.$refs.stepData.query(record, false)
        return;
      }

      if (record.flowId != null) {
        this.outQueryFlowRecord.flowId = record.flowId
        this.$refs.stepData.query(this.outQueryFlowRecord, false)
      } else {
        this.$message.warn("测试数据为空")
        return
      }
    },

    handleAllOk() {
      const temList = this._handleVerify()
      // 如果校验不通过
      if (!temList[0]) {
        return this.$message.warn("请填写 " + (typeof temList[1] === 'number' ? "RPT " + temList[1] + " 的" : "") + temList[2])
      } else {
        this.commitCustomLogic()
      }
    },

    // 校验
    _handleVerify() {
      this.$forceUpdate()

      this.verifyPassed = false

      // console.log("calendarParam: ", this.calendarParam)
      if (!this.calendarParam) {
        return [false, '', '']
      }

      // ---------------------- 校验：基准工步索引，基准工步行数据 ------------------------
      let standardRptIndex = this.calendarParam.standardRptIndex
      if (typeof standardRptIndex !== 'number') {
        return [false, '', '基准工步']
      } else {
        let standardRptParam = this.calendarParam.rptParamList[standardRptIndex]

        if (!standardRptParam.retentionStep) {
          return [false, standardRptIndex, '容量&能量保持率工步号']
        }
        if (!standardRptParam.recoveryStep) {
          return [false, standardRptIndex, '容量&能量恢复率工步号']
        }

        const dchCeStepTiTleList = this.calendarParam.dchCeStepTiTleList
        if (this.dchCeStepNum > 0) {
          for (let i = 0; i < dchCeStepTiTleList.length; i++) {
            if (!dchCeStepTiTleList[i]) {
              return [false, '', '请填写第 ' + (i+1) + ' 个放电容量&能量工步号标题']
            }
            if (standardRptParam.dchCeStepStrList.length > i && !standardRptParam.dchCeStepStrList[i]) {
              return [false, '', '请填写基准行第 ' + (i+1) + ' 个放电容量&能量工步号']
            }
          }
        }

        // ---------------------- 校验：DCIR参数组标题填写 ------------------------
        let dcirTiTleList = this.calendarParam.dcirTiTleList
        const isV = this.calendarParam.dcirCalcType === 'V'
        const hasDcirParam = this.calendarParam.rptParamList.some(rptStepParam => rptStepParam.dcirStepParamList.some(item => item.dchStep || (!isV && item.restStep) || (isV && item.dchStep2)))
        if (hasDcirParam) {
          for (let i = 0; i < dcirTiTleList.length; i++) {
            if (!dcirTiTleList[i]) {
              return [false, '', '请填写第 ' + (i+1) + ' 组DCIR参数组标题']
            }
          }

          if (isV) {
            for (let i = 0; i < standardRptParam.dcirStepParamList.length; i++) {
              const dcirParam = standardRptParam.dcirStepParamList[i];
              if (!dcirParam.dchStep) {
                return [false, '', '请填写基准行第 ' + (i+1) + ' 组DCIR放电工步号1']
              }
              if (!dcirParam.dchStep2) {
                return [false, '', '请填写基准行第 ' + (i+1) + ' 组DCIR放电工步号2']
              }
            }
          } else {
            for (let i = 0; i < standardRptParam.dcirStepParamList.length; i++) {
              const dcirParam = standardRptParam.dcirStepParamList[i];
              if (dcirParam.dchStep && !dcirParam.restStep) {
                return [false, '', '请填写基准行第 ' + (i+1) + ' 组DCIR搁置工步号']
              }
              if (dcirParam.restStep && !dcirParam.dchStep) {
                return [false, '', '请填写基准行第 ' + (i+1) + ' 组DCIR放电工步号']
              }
            }
          }

        }

        this.verifyPassed = true
        return [true, '', '']
      }
    },

    dcirCalcTypeChange(event) {
      this.$set(this.calendarParam, 'dcirCalcType', event.target.value)
      this.changeRptParamColumns(false)
    },

    changeDchCeStepStrList(isDelete = false) {
      if (isDelete) {
        if (this.dchCeStepNum > 0) {
          this.dchCeStepNum--
          this.calendarParam.dchCeStepTiTleList.splice(this.dchCeStepNum, 1)
        } else {
          return
        }
      } else {
        if (this.dchCeStepNum < 9) {
          this.dchCeStepNum++
          this.calendarParam.dchCeStepTiTleList.push("")
        } else {
          return this.$message.warn('最多10列放电容量&能量工步号，无法增加')
        }
      }

      this.changeRptParamColumns(false)
    },
    changeDcirParamList(isDelete = false) {
      if (isDelete) {
        if (this.dcirNum > 1) {
          this.dcirNum--
          this.calendarParam.dcirTiTleList.splice(this.dcirNum, 1)
        } else {
          return this.$message.warn('至少一组DCIR参数，无法减少')
        }
      } else {
        if (this.dcirNum < 4) {
          this.dcirNum++
          this.calendarParam.dcirTiTleList.push("")
        } else {
          return this.$message.warn('最多四组DCIR参数，无法增加')
        }
      }

      this.changeRptParamColumns(false)
    },
    changeRptParamColumns(isFirst = false) {
      if (isFirst) {
        if (Array.isArray(this.calendarParam.dcirTiTleList) && this.calendarParam.dcirTiTleList.length > 0) {
          this.dcirNum = this.calendarParam.dcirTiTleList.length
        } else {
          this.calendarParam.dcirTiTleList = [""]
          this.dcirNum = 1
        }
        if (Array.isArray(this.calendarParam.dchCeStepTiTleList) && this.calendarParam.dchCeStepTiTleList.length > 0) {
          this.dchCeStepNum = this.calendarParam.dchCeStepTiTleList.length
        } else {
          this.calendarParam.dchCeStepTiTleList = []
          this.dchCeStepNum = 0
        }
      }

      for (let i = 0; i < this.calendarParam.rptParamList.length; i++) {
        let rptStepParam = this.calendarParam.rptParamList[i];

        rptStepParam.dcirStepParamList = rptStepParam.dcirStepParamList || []
        // DCIR参数组赋值 如果长度小于目标长度，则用空对象填充
        const dcirStepLen = rptStepParam.dcirStepParamList.length;
        if (dcirStepLen < this.dcirNum) {
          rptStepParam.dcirStepParamList.push(...Array.from({ length: this.dcirNum }, () => ({})));
        }
        // 删除DCIR需要清空参数组 如果长度大于目标长度，则截断数组
        if (dcirStepLen > this.dcirNum) {
          rptStepParam.dcirStepParamList.length = this.dcirNum;
        }

        rptStepParam.dchCeStepStrList = rptStepParam.dchCeStepStrList || []
        // 放电工步号赋值 如果长度小于目标长度，则用空字符串填充
        const dchStepLen = rptStepParam.dchCeStepStrList.length;
        if (dchStepLen < this.dchCeStepNum) {
          rptStepParam.dchCeStepStrList.push(...new Array(this.dchCeStepNum - dchStepLen).fill(''));
        }
        // 删除放电工步号列需要清除放电工步 如果长度大于目标长度，则截断数组
        if (dchStepLen > this.dchCeStepNum) {
          rptStepParam.dchCeStepStrList.length = this.dchCeStepNum;
        }
      }

      this.rptParamColumns =  [
        {
          title: "RPT",
          align: "center",
          width: 100,
          dataIndex: "rpt",
        },
        {
          title: "Days",
          align: "center",
          width: 100,
          dataIndex: "day",
          scopedSlots: {customRender: "day"}
        },
        {
          title: "Recharge Days",
          align: "center",
          width: 100,
          dataIndex: "rechargeDay",
          scopedSlots: {customRender: "rechargeDay"}
        },
        {
          title: "样品",
          align: "center",
          width: 230,
          dataIndex: "orderDataList",
          scopedSlots: {customRender: "orderDataList"}
        },
        {
          align: "center",
          width: 100,
          dataIndex: "retentionStep",
          scopedSlots: {customRender: "retentionStep", title: "retentionStepTitle"}
        },
        {
          align: "center",
          width: 120,
          dataIndex: "recoveryStep",
          scopedSlots: {customRender: "recoveryStep", title: "recoveryStepTitle"}
        },
      ]

      // 添加放电容量能量工步号列表
      this.dchCeStepIndexArr = []
      for (let i = 0; i < this.dchCeStepNum; i++) {
        this.rptParamColumns.push(
            {
              dataIndex: "dchCeStepStrList[" + i + "]",
              align: "center",
              width: 130,
              scopedSlots: {
                title: "dchCeStepTitle_" + i,
                customRender: "dchCeStep"
              }
            }
        )

        this.dchCeStepIndexArr.push(i)
      }

      this.dcirTitleIndexArr = []
      for (let i = 0; i < this.dcirNum; i++) {
        const children1 = [
          {
            title: "搁置工步号",
            width: 100,
            align: "center",
            dataIndex: "dcirStepParamList[" + i + "].restStep",
            scopedSlots: {customRender: "dcirStepParam"}
          },
          {
            title: "放电工步号",
            width: 100,
            align: "center",
            dataIndex: "dcirStepParamList[" + i + "].dchStep",
            scopedSlots: {customRender: "dcirStepParam"}
          },
          {
            width: 130,
            align: "center",
            dataIndex: "dcirStepParamList[" + i + "].dchStepTime",
            scopedSlots: {
              title: "dchStepTimeTitle",
              customRender: "dcirStepParam"
            }
          }
        ]
        const styleObj = {maxWidth: '600px'};
        const children2 = [
          {
            title: "放电工步号1",
            width: 100,
            align: "center",
            dataIndex: "dcirStepParamList[" + i + "].dchStep",
            scopedSlots: {customRender: "dcirStepParam"}
          },
          {
            title: <a-tooltip overlayStyle={styleObj} arrow-point-at-center>
              <template slot="title">
                <span>
                  未填写放电工步时间默认取工步数据表数据；<br/>
                  填写了放电工步时间则取详细数据表数据；<br/>
                  格式：HH:mm:ss.SSS，精确匹配；例如：0:00:10.000
                </span>
              </template>
              放电工步时间1 <a-icon type="question-circle" style="color: #1890ff;"/>
            </a-tooltip>,
            width: 130,
            align: "center",
            dataIndex: "dcirStepParamList[" + i + "].dchStepTime",
            scopedSlots: {customRender: "dcirStepParam"}
          },
          {
            title: "放电工步号2",
            width: 100,
            align: "center",
            dataIndex: "dcirStepParamList[" + i + "].dchStep2",
            scopedSlots: {customRender: "dcirStepParam"}
          },
          {
            title: <a-tooltip overlayStyle={styleObj} arrow-point-at-center>
              <template slot="title">
                <span>
                  未填写放电工步时间默认取工步数据表数据；<br/>
                  填写了放电工步时间则取详细数据表数据；<br/>
                  格式：HH:mm:ss.SSS，精确匹配；例如：0:00:10.000
                </span>
              </template>
              放电工步时间2 <a-icon type="question-circle" style="color: #1890ff;"/>
            </a-tooltip>,
            width: 130,
            align: "center",
            dataIndex: "dcirStepParamList[" + i + "].dchStepTime2",
            scopedSlots: {customRender: "dcirStepParam"}
          }
        ]

        this.rptParamColumns.push(
            {
              children: this.calendarParam.dcirCalcType === 'V' ? children2 : children1,
              scopedSlots: {
                title: "dcirTitle_" + i
              }
            }
        )

        this.dcirTitleIndexArr.push(i)
      }

      this.rptParamColumns.push({
        align: "center",
        width: 100,
        scopedSlots: {
          customRender: "standardStep",
          title: "standardStepTitle"
        }
      })

      // 添加充电容量&恒流比工步号
      this.rptParamColumns.push(
          {
            dataIndex: "chCeStep",
            align: "center",
            width: 120,
            scopedSlots: {
              title: "chCeStepTitle",
              customRender: "chCeStep"
            }
          }
      )

      this._handleVerify()
    },

  }
}
</script>
<style lang="less" scoped>
// 通用
.mt10 {
  margin-top: 10px;
}

.mr10 {
  margin-right: 10px;
}

.mb10 {
  margin-bottom: 10px;
}

.ml10 {
  margin-left: 10px;
}

.fs12 {
  font-size: 12px;
}

.rollback-icon {
  margin-right: 4px;
}

/* 标题 */
.head-title {
  display: flex;
  align-items: center;
}

.head-title .line {
  width: 4px;
  height: 22px;
  background: #3293ff;
  border-radius: 20px;
}

.head-title .title {
  font-size: 16px;
  font-weight: 600;
}

.wrapper {
  padding: 6px 16px 6px 16px;
  margin: 0 0 0 -40px;
  background-color: #f0f2f5;
}

.flex-sb-center-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.normal-btn {
  padding: 5px 10px;
  margin: 3px 0;
  color: #fff;
  background-color: #1890ff;
  letter-spacing: 2px;
  cursor: pointer;
}

.table-wrapper {
  padding: 10px;
  background: #fff;
  border-radius: 10px;
  width: calc(100vw - 16px - 16px);
  height: calc(100vh - 20px - 18px - 34px);
  overflow: hidden;
}

/deep/ .table-content {
  width: 100%;
  height: calc(100vh - 20px - 18px - 34px - 20px);
  overflow: scroll;
}

/deep/ .rpt-table .ant-table-body {
  border: 1px solid #e8e8e8;
  height: calc(100vh - 20px - 18px - 34px - 20px - 40px) !important;
  overflow-y: scroll; /* 强制显示垂直滚动条 */
  overflow-x: scroll; /* 强制显示水平滚动条 */
}

/deep/ .rpt-table .ant-table-thead {
  position: sticky;
  top: 0;
  z-index: 2;
}

/deep/ .rpt-table .ant-table-thead > tr > th {
  padding: 6px 6px;
  font-size: 13px;
}

/deep/ .rpt-table .ant-table-tbody > tr > td {
  padding: 0px 0px;
  font-size: 12px;
}

/deep/ .rpt-table .ant-table-body::-webkit-scrollbar {
  height: 8px;
  width: 6px;
}

/deep/ .rpt-table .ant-table-body::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;

  background: #dddbdb;
}

/deep/ .rpt-table .ant-table-body::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: #f1f1f1;
}

/deep/ .rpt-table .ant-table-pagination.ant-pagination {
  margin: 6px 0px 0px 0px;
}

/deep/ .rpt-table .ant-pagination-options-size-changer.ant-select {
  min-width: 90px; /* 调整分页器宽度 */
}

/deep/ .rpt-table .ant-pagination-options-size-changer.ant-select .ant-select-selection__rendered {
  text-align: center; /* 分页器文字居中 */
}

/deep/ .ant-tabs-content thead tr {
  height: 50px !important;
}

/deep/ .ant-tabs-content .ant-table-middle > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-thead > tr > th {
  padding: 5px;
}

.input {
  width: 100%;
  text-align: center;
  border: 0;
}

.samples-block {
  padding: 2px 0;
}

.shrink-btn {
  color: #1890ff;
  cursor: pointer;
  font-size: 12px;
}
</style>