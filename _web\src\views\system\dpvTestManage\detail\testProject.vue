<template>
  <a-modal :visible="true" title="实际测试数量" :width="1200" centered @cancel="handleCancel">
    <a-spin :spinning="spinning">
      <a-form>
        <a-form-item label="委托单号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input v-model="folderno" style="width: 200px;" @blur="getLimsOrdtaskListByParam"
            @keyup.enter="getLimsOrdtaskListByParam" />
          <span class="tips-text">注：请输入正确的委托单号进行查询</span>
        </a-form-item>
      </a-form>
      <a-table :columns="columns" :data-source="loadData" bordered :rowKey="(record) => record.id"
        :pagination="{ size:'small' }" :row-selection="{ 
        selectedRowKeys: selectedRowKeys,
        selectedRows: selectedRows,
        onChange:onSelectChange,
        type:'radio',
        columnWidth:30
        }">
      </a-table>
    </a-spin>
    <template slot="footer">
      <a-button @click="handleCancel">
        关闭
      </a-button>
    </template>

  </a-modal>
</template>

<script>
  import { getLimsOrdtaskListByParam } from "@/api/modular/system/limsManager"

  export default {
    props: {
      folderNoCheck: {  //已经选中的folderno
        type: String,
        default: ""
      },
      tlimsOrdtaskIdCheck: {  //已经选中的tlimsOrdtaskId
        type: String,
        default: ""
      },
    },
    data() {
      return {
        spinning: false,
        folderno: '',
        labelCol: {
          sm: {
            span: 2
          }
        },
        wrapperCol: {
          sm: {
            span: 12
          }
        },
        loadData: [],
        selectedRowKeys: [],
        selectedRows: [],
        columns: [
          {
            title: '序号',
            align: 'center',
            width: 30,
            customRender: (text, record, index) => index + 1
          }, {
            title: '委托单号',
            width: 60,
            align: 'center',
            dataIndex: 'folderno',
          }, {
            title: '一级分类',
            width: 60,
            align: 'center',
            dataIndex: 'firstcategory',
          }, {
            title: '二级分类',
            width: 60,
            align: 'center',
            dataIndex: 'secondcategory',
          }, {
            title: '测试员',
            width: 60,
            align: 'center',
            dataIndex: 'tester',
          }, {
            title: '测试项目编号',
            width: 60,
            align: 'center',
            dataIndex: 'testcode',
          }, {
            title: '测试项目名称',
            width: 90,
            align: 'center',
            dataIndex: 'testname'
          }, {
            title: '测试项目别名',
            width: 90,
            align: 'center',
            dataIndex: 'alias'
          }, {
            title: '温度',
            width: 60,
            align: 'center',
            dataIndex: 'tem'
          }, {
            title: 'soc',
            width: 60,
            align: 'center',
            dataIndex: 'soc'
          }, {
            title: '储存时间',
            width: 60,
            align: 'center',
            dataIndex: 'totalDay'
          }
        ]
      }
    },
    created() {
      if (this.tlimsOrdtaskIdCheck && this.folderNoCheck) {
        this.folderno = this.folderNoCheck
        this.selectedRowKeys = [this.tlimsOrdtaskIdCheck]
        this.selectedRows = [{ id: this.tlimsOrdtaskIdCheck }]
        this.getLimsOrdtaskListByParam()
      }
    },
    methods: {
      onSelectChange(selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows


        this.$emit('submit', {
          tLimsOrdtaskId: selectedRowKeys[0],
          folderNo: selectedRows[0].folderno,
          firstcategory:selectedRows[0].firstcategory,
          secondcategory:selectedRows[0].secondcategory,
          testname:selectedRows[0].testname,
        })
      },
      getLimsOrdtaskListByParam() {
        this.spinning = true
        getLimsOrdtaskListByParam({ folderno: this.folderno }).then(res => {
          this.loadData = res.data
        }).finally(() => {
          this.spinning = false
        })
      },
      handleCancel() {
        this.$emit('cancel')
      }

    }
  }
</script>
<style lang="less" scoped>
  .tips-text {
    color: #1890ff;
    font-size: 12px;
    margin-left: 8px;
    vertical-align: sub;
  }
  /deep/ .ant-form-item label{
    font-size: 12px;
  }

  /deep/.ant-form-item {
    margin-bottom: 10px;
  }

  /deep/ .ant-pagination {
    margin: 16px 0 -8px;
  }

  /deep/ .ant-modal-footer {
    padding: 0 24px 24px;
  }
</style>