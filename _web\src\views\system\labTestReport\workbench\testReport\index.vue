<template>
	<div class="container">
		<!-- 标题部分 start -->
		<div class="head-wrapper" id="head">
			<div class="salutation">
				<p>{{ tips }}</p>
				<p>时间:{{ now }}</p>
			</div>

			<div class="circle-block" v-for="(item, index) in dataArr">
				<div class="data-packet" v-if="!item.type">
          <div class="icon" :style="`background: ${item.color};border: 8px solid ${item.borderColor};`">
            <a-icon :type="item.icon"/>
          </div>
					<div class="detail">
						<p>{{ item.num < 10 ? "0" : "" }}{{ item.num }}</p>
								<p>{{ item.detail }}</p>
					</div>
				</div>

				<div v-else class="line"></div>
			</div>
		</div>
		<!-- 标题部分 end -->

		<!-- 内容部分 start -->
		<div class="content-wrapper">
			<div class="left" id="left">
				<div class="options">
					<a-input-search class="mr10" size="small" placeholder="请输入委托单号/产品名称/技术状态/检测实验室/委托人" @search="handleInputSearch" />
				</div>
				<pbiTabs :tabsList="tabsArr" :activeKey="activeKey" @clickTab="handleTabsChange"></pbiTabs>
				<div class="tab" :style="{borderRadius:activeKey == 0 ? '0 10px 10px 10px' : '10px' }">
<!--					<a-table id="outTable" :style="`height:${tableHeight}px`" :columns="columns" :rowKey="record => record.id" :scroll="{x:1200}"-->
<!--						:data-source="tableData" :pagination="pagination" :loading="tableLoading">-->
<!--					</a-table>-->
          <tableIndex
            :pageLevel='1'
            :tableTotal='tableTotal'
            :pageTitleShow=false
            :loading='tableLoading'
            :otherHeight="parseInt(189)"
            @paginationChange="handlePageChange"
            @paginationSizeChange="handlePageChange"
          >
            <template #table>
              <ag-grid-vue :style="{height:tableHeight}"
                           class='table ag-theme-balham'
                           :tooltipShowDelay="0"
                           :columnDefs="columns"
                           :rowData='tableData'
                           rowSelection="multiple"
                           :gridOptions="gridOptions"
                           @grid-ready="onGridReady"
                           :defaultColDef='defaultColDef'>
              </ag-grid-vue>
            </template>
          </tableIndex>
				</div>
			</div>
			<div class="right">
				<div class="block mb12">
					<div class="title">
						<img src="@/assets/icons/bookmark.png" alt="" />
						<span class="text">检测报告责任人员表</span>
					</div>
          <a-table
            style="width: 100%"
            :columns="responsibleColumns"
            :data-source="responsibleData"
            :pagination="false"
            bordered
          ></a-table>
				</div>
				<div class="block bottom-block">
					<div class="title">
						<img src="@/assets/icons/bookmark.png" alt="" />
            <span class="text">委托测试总览</span>
					</div>
          <a-empty class="empty-block" description="正在加急开发中~" />
				</div>
			</div>
		</div>
		<!-- 内容部分 end -->

		<!-- modal start -->
		<div>
      <template>
        <a-modal
          v-if="isShowReUploadReport"
          title="重新上传"
          :visible="true"
          width="20%"
          :centered="true"
          okText="确定"
          @cancel="showReUploadReportCancel"
          @ok="showReUploadReportOk"
        >
          <div>
            <span>确定重新上传检测报告？</span>
          </div>
        </a-modal>
        <a-modal
          v-if="isShowEndingReport"
          title="检测报告完结"
          :visible="true"
          width="20%"
          :centered="true"
          okText="确定"
          @cancel="showEndingReportCancel"
          @ok="showEndingReportOk"
        >
          <div>
            <span>完结后无法重新上传该检测报告，确定完结？</span>
          </div>
        </a-modal>
        <a-modal
          v-if="isShowTaskStatus"
          title="修改任务状态"
          :visible="true"
          width="20%"
          :centered="true"
          okText="确定"
          @cancel="taskStatusModelCancel"
          @ok="taskStatusModelOk"
        >
          <div>
            <span>任务状态：</span>
            <a-select v-model="toUpdateTaskStatus" style="width: 50%">
              <a-select-option value="待处理">待处理</a-select-option>
              <a-select-option value="进行中">进行中</a-select-option>
              <a-select-option value="驳回">驳回</a-select-option>
              <a-select-option value="审核中">审核中</a-select-option>
              <a-select-option value="批准中">批准中</a-select-option>
              <a-select-option value="已完成">已完成</a-select-option>
              <a-select-option value="已完结">已完结</a-select-option>
            </a-select>
          </div>
        </a-modal>
        <a-modal
          v-if="isShowAssignUploader"
          title="分配责任人"
          :visible="true"
          width="20%"
          :centered="true"
          okText="确定"
          @cancel="assignUploaderModelCancel"
          @ok="assignUploaderModelOk"
        >
          <div>
            <span>责任人：</span>
            <a-dropdown style="width: 60%;" v-model="responsibleNameVisible" placement="bottomCenter" :trigger="['click']">
              <a-button class="man_button" :style="{color:responsibleMan?'rgba(0, 0, 0, 0.65)':'#b7b7b7'}">{{responsibleMan ? responsibleMan : '选择责任人'}}
                <a-icon type="down" />
              </a-button>
              <a-menu slot="overlay">
                <a-spin :spinning="responsibleManLoading" style="padding:10px 24px 0 24px;width:100%">
                  <a-input-search v-model="responsibleManQueryParam.searchValue" placeholder="搜索..." @change="onResponsibleManSearch" />
                  <s-table style="width:100%;" ref="responsibleManTable" :rowKey="(record) => record.id" :columns="vColumns" :data="loadResponsibleManData" :customRow="customResponsibleNameRow" :scroll="{ y: 120,x:120}">
                  </s-table>
                </a-spin>
              </a-menu>
            </a-dropdown>
          </div>
        </a-modal>
      </template>
      <UploadTestReport v-if="isShowUploadTestReport" :modalData="modalData" @cancel="handleUploadCancel" @refresh="getTodoTaskList"></UploadTestReport>
      <UploadTestReport v-if="isShowAuditTestReport" :modalData="modalData" @cancel="handleAuditCancel" @refresh="getTodoTaskList"></UploadTestReport>
		</div>
		<!-- modal end -->

	</div>
</template>

<script>
	import Vue from 'vue'
	import { formatDate } from "@/utils/format"
	import { timeFix } from "@/utils/util"
  import {
    getReportTodoTaskList,
    getReportTodoTaskStatistics, reUpdateReportFile, updateReportTaskStatus, updateReportUploader,
  } from "@/api/modular/system/testProgressManager"

	import pbiTabs from '@/components/pageTool/components/pbiTabs.vue'
  import UploadTestReport from "../components/uploadTestReport"
	import _ from "lodash"
	import { mapGetters } from "vuex";
  import AlterYfTesterModal from "@/views/system/testProgress/workbench/components/alterYfTesterModal.vue";
  import { STable } from "@/components";
  import { getUserLists } from "@/api/modular/system/userManage";

	export default {
		name: "Technicians",
    props: {
      width: {
        type: Number,
        default: 0
      }
    },
		data() {
			return {
        loadResponsibleManData: parameter => {
          return getUserLists(Object.assign(parameter, this.responsibleManQueryParam)).then((res) => {
            return res.data
          })
        },
        vColumns: [{
          title: '账号',
          dataIndex: 'account'
        },
          {
            title: '姓名',
            dataIndex: 'name'
          },
        ],
        responsibleManQueryParam: {},
        responsibleColumns: [
          {
            title: '序号',
            align: "center",
            dataIndex: 'sorter'
          },
          {
            title: '岗位',
            align: "center",
            dataIndex: 'post'
          },
          {
            title: '第四实验室',
            align: "center",
            dataIndex: 'labFourPersonName'
          },
          {
            title: '第六实验室(HZ)',
            align: "center",
            dataIndex: 'labSixHzPersonName'
          },
        ],
        responsibleData: [
          {
            sorter: "1",
            post: "编制",
            labFourPersonName: "张诵权",
            labSixHzPersonName: "张文胜,王晓玲,凡善帅",
          },
          {
            sorter: "2",
            post: "审核",
            labFourPersonName: "梁丽娜",
            labSixHzPersonName: "黄辉宁",
          },
          {
            sorter: "3",
            post: "批准",
            labFourPersonName: "夏冬冬",
            labSixHzPersonName: "苗培霜",
          },
        ],
        responsibleManLoading: false,
				now: "",
				tips: "",
				searchValue: "",
				searchTesterValue: "",
				filterData: '',
        modalData: {},
        isShowUploadTestReport: false,
        isShowAuditTestReport: false,
        isShowTaskStatus: false,
        isShowReUploadReport: false,
        isShowEndingReport: false,
        isShowAssignUploader: false,
        responsibleNameVisible: false,
        responsibleMan: null,
        responsibleManCode: null,
        changeTaskStatusItem: null,
        assignResponsibleItem: null,
        toUpdateTaskStatus: null,
        tableHeight: document.body.clientHeight - 258.5 - this.width + 'px' ,
				activeKey: '0',
				tableLoading: false,
				isFirst: true,//初次进入页面
        gridApi: null,
        columnApi: null,
        tableTotal:0,
        gridOptions: {
          onSelectionChanged: this.onSelectionChanged,
          suppressCellSelection: false
        },
        defaultColDef: {
          filter: false,
          floatingFilter: false,
          editable: false,
        },
				// pagination: {
				// 	size:'small',
				// 	current: 1,
				// 	pageSize: 10,
				// 	total: 0,
				// 	showSizeChanger: true,
				// 	showQuickJumper: true,
				// 	pageSizeOptions: ['10', '20', '30', '40', '50'],
				// 	onChange: (current, size) => {
				// 		this.storeList[this.arrName[this.activeKey]].pagination.pageNo = current
				// 		this.getTodoTaskList()
				// 	},
				// 	onShowSizeChange: (current, pageSize) => {
				// 		this.storeList[this.arrName[this.activeKey]].pagination.pageSize = pageSize
				// 		this.getTodoTaskList()
				// 	},
				// },

				storeList:{},
				tabsArr:[
					{value:'0',label:'今日截止'},
					// {value:'1',label:'今日任务'},
					{value:'2',label:'待办清单'},
					{value:'3',label:'逾期任务'},
					{value:'4',label:'任务总览'},
				],

				// tabsArr: ["今日截止", "今日任务", "待办清单", "逾期任务", "任务总览"],
				arrName: ["dueTodayTodo", "todayTodo", "allTodo", "delayTodo", "all"],

				dataArr: [
					//type 0: 数据  1: 线
					{
						num: '--',
						type: 0,
						detail: "今日截止",
						icon: "exclamation",
						color: "#ffb794",
						borderColor: "rgba(255, 183, 148,0.15)"
					},
					// {
					// 	type: 1
					// },
					// {
					// 	num: '--',
					// 	type: 0,
					// 	detail: "今日任务",
					// 	icon: "check",
					// 	color: "#34E09E",
					// 	borderColor: "rgba(52,224,158,0.15)"
					// },
					{
						type: 1
					},
					{
						num: '--',
						type: 0,
						detail: "待办清单",
						icon: "menu",
						color: "#66ADF9",
						borderColor: "rgba(102,173,249,0.15)"
					},
					{
						type: 1
					},
					{
						num: '--',
						type: 0,
						detail: "逾期任务",
						icon: "close",
						color: "#F2AE00",
						borderColor: "rgba(242,174,0,0.15)"
					},
					{
						type: 1
					},
					{
						num: '--',
						type: 0,
						detail: "任务总览",
						icon: "clock-circle",
						color: "#8382F5",
						borderColor: "rgba(131,130,245,0.15)"
					}
				],
				tableData: [],
        columns: [
          {
            headerName: '序号',
            minWidth: 50,
            width: 50,
            pinned: 'left', //固定列   left / right
            cellRenderer: function (params) {
              return params.rowIndex + 1
            },
          },
          {
            headerName: "委托单号",
            field: "folderNo",
            pinned: 'left', //固定列   left / right
            width: 110
          },
          {
            headerName: "测试项目",
            field: "testProjectNames",
            width: 205,
            tooltipValueGetter: (p) => p.value,
          },
          {
            headerName: "产品名称",
            field: "productName",
            width: 110
          },
          {
            headerName: "技术状态",
            field: "technicalStatus",
            width: 90
          },
          {
            headerName: "测试类别",
            field: "testTypeCategory",
            width: 110
          },
          {
            headerName: "检测实验室",
            field: "laboratory",
            width: 120
          },
          {
            headerName: "委托人",
            field: "wtrName",
            width: 90
          },
          {
            headerName: "截止日期",
            field: "endingDate",
            width: 90,
            cellRenderer: function (params) {
              let text = params.data.endingDate
              if (text) {
                return text.split(" ")[0]
              } else {
                return ''
              }
            }
          },
          {
            headerName: "是否逾期",
            field: "isOverdue",
            cellRenderer: 'isOverdue',
            cellRendererParams: { colorRender: this.colorRender },
            width: 80
          },
          {
            headerName: "责任人",
            field: "responsibleName",
            width: 90
          },
          {
            headerName: "审核人",
            field: "auditName",
            width: 90
          },
          {
            headerName: "任务状态",
            field: "taskStatus",
            cellRenderer: 'taskStatus',
            cellRendererParams: { colorRender: this.colorRender, changeTaskStatus: this.changeTaskStatus },
            pinned: 'right', //固定列   left / right
            width: 90
          },
          {
            headerName: "操作",
            field: "control",
            cellRenderer: 'control',
            cellRendererParams: { uploadTestReport: this.uploadTestReport, assignUploader: this.assignUploader, auditReport: this.auditReport, approveReport: this.approveReport, endingReport: this.endingReport },
            flex: 2,
            pinned: 'right', //固定列   left / right
            minWidth: 180
          }
        ],
			}
		},
		components: {
      STable,
      AlterYfTesterModal,
			pbiTabs,
      UploadTestReport,
      taskStatus: {
        template: `<span><a-tag :color="params.colorRender(params.data.taskStatus)" @click="params.changeTaskStatus(params.data)" style="margin: 0">{{ params.value }}</a-tag></span>`
      },
      isOverdue: {
        template: `<span><a-tag :color="params.colorRender(params.data.isOverdue)" style="margin: 0">{{ params.value }}</a-tag></span>`
      },
      control: {
        template: `<div>
                   <a-button v-if="hasPerm('testReportTodoTask:updateReportUploader') && false" type="link" @click="params.assignUploader(params.data)">分配</a-button>
                   <a-button v-if="hasPerm('testReportTodoTask:updateReportFile')" type="link" @click="params.uploadTestReport(params.data)">上传</a-button>
                   <a-button v-if="hasPerm('testReportTodoTask:updateReportFile')" type="link" @click="params.endingReport(params.data)">完结</a-button>
                   <a-button v-if="hasPerm('testReportTodoTask:updateReportTodoTaskData') && false" type="link" @click="params.auditReport(params.data)">审核</a-button>
                   <a-button v-if="hasPerm('testReportTodoTask:reUpdateReportFile') && false" type="link" @click="params.approveReport(params.data)">批准</a-button>
                   </div>`
      }
		},

		watch: {
			tableData(newVal, oldVal) {
			}
		},

		created() {
			this.getTodoTaskList(1)
			this.now = formatDate(new Date())
			this.tips = timeFix()
			setInterval(() => {
				this.now = formatDate(new Date())
			}, 60000)
		},
		computed: {
			...mapGetters(['userInfo'])
		},
		mounted() {
			this.getTodoTaskStatistics()
		},
		methods: {
      onResponsibleManSearch(e) {
        this.$refs.responsibleManTable.refresh()
      },
      onGridReady(params) {
        this.gridApi = params.api;
        this.columnApi = params.columnApi;
        // params.api.sizeColumnsToFit();
      },
      onSelectionChanged(event) {
        // 获取当前选中的行
        const selectedNodes = this.gridApi.getSelectedNodes();
        const selectedData = selectedNodes.map(node => node.data);
        // 更新选中的行数据
        this.selectedRows = selectedData;
        this.selectedRowKeys = selectedData;
      },
			getTodoTaskStatistics(){
        getReportTodoTaskStatistics().then(res => {
					if (!res.success) return this.$message.error("错误提示：" + err.message)
					this.dataArr[0].num = res.data.dueTodayTodoNum
					// this.dataArr[2].num = res.data.todayTodoNum
					this.dataArr[2].num = res.data.allTodoNum
					this.dataArr[4].num = res.data.delayTodoNum
					this.dataArr[6].num = res.data.allNum
				})
			},
			getTodoTaskList(isThShow = 0) {
				this.tableLoading = true

				// searchType   1:今日任务,2:待办清单,3:逾期任务,4:任务总览,5:今日截止      初次进入展示'今日截止  5'
				const params = {
					pageNo: this.storeList[this.arrName[this.activeKey]] ? this.storeList[this.arrName[this.activeKey]].pagination.pageNo : 1,
					pageSize:this.storeList[this.arrName[this.activeKey]] ? this.storeList[this.arrName[this.activeKey]].pagination.pageSize : 20,
					searchValue:this.searchValue,
          searchTesterValue:this.searchTesterValue,
					searchType:this.activeKey == 0 ? 5 : this.activeKey,
				}

        getReportTodoTaskList(params)
				.then(async res => {
					if (!res.success) return this.$message.error("错误提示：" + err.message)

					if(!this.storeList[this.arrName[this.activeKey]]){
						this.storeList[this.arrName[this.activeKey]] = {
							pagination:{
								pageNo:1,
								pageSize:20,
								total:res.data.totalRows
							}
						}
					}

					// 初次进入，如果今日截止无数据，则展示待办清单

					if(this.isFirst && res.data.rows.length === 0){
						this.activeKey = 2
						this.isFirst = false

						// 赋值测试员列表(只需要拿一次)
						return this.getTodoTaskList(1)
					}

					this.tableData = res.data.rows
          this.tableTotal = res.data.totalRows

				}).finally(() => {
					this.tableLoading = false
					this.isFirst = false
				})
			},
			// 搜索框事件
			handleInputSearch(value) {
				this.searchValue = value.trim()

				// 重置页数
				this.storeList[this.arrName[this.activeKey]].pagination.pageNo = 1
				this.storeList[this.arrName[this.activeKey]].pagination.pageSize = 20

				this.getTodoTaskList()
			},
			handleTabsChange(value) {
				this.activeKey = value
				this.filterData = ''
				this.getTodoTaskList()
			},
      colorRender(text) {
        if (text === '已完成' || text === '未逾期') {
          return 'green'
        } else if (text === '待处理') {
          return 'orange'
        } else if (text === '驳回' || text === '已逾期') {
          return 'red'
        } else if (text === '进行中') {
          return 'blue'
        } else if (text === '审核中') {
          return 'purple'
        } else if (text === '批准中') {
          return 'purple'
        } else if (text === '已完结') {
          return 'grey'
        }
      },
      changeTaskStatus(data) {
        if (this.hasPerm('testReportTodoTask:updateReportTaskStatus')) {
          this.isShowTaskStatus = true
          this.changeTaskStatusItem = data
        }
      },
      showReUploadReportOk() {
        this.isShowReUploadReport = false
        reUpdateReportFile(this.modalData).then(res => {
          if (res.success) {
            this.modalData = res.data
            this.modalData.isView = false
            this.isShowUploadTestReport = true
            this.getTodoTaskList()
            this.getTodoTaskStatistics()
          } else {
            return this.$message.error("错误提示：" + res.message)
          }
        })
      },
      showReUploadReportCancel() {
        this.isShowReUploadReport = false
      },
      auditReport(data) {
        if (data.taskStatus !== '审核中') {
          this.$message.warning('任务状态为审核中才能审核！')
          return
        }
        this.modalData = data
        this.modalData.isView = true
        this.isShowAuditTestReport = true
      },
      approveReport(data) {
        if (data.taskStatus !== '批准中') {
          this.$message.warning('任务状态为批准中才能批准！')
          return
        }
        this.modalData = data
        this.modalData.isView = true
        this.isShowAuditTestReport = true
      },
      showEndingReportCancel() {
        this.isShowEndingReport = false
      },
      showEndingReportOk() {
        updateReportTaskStatus({ id: this.modalData.id, taskStatus: "已完结" }).then(res => {
          if (res.success) {
            this.getTodoTaskList()
            this.getTodoTaskStatistics()
            this.$message.success('操作完结成功')
          } else {
            return this.$message.error("错误提示：" + res.message)
          }
        }).finally(() => {
          this.isShowEndingReport = false
        })
      },
      endingReport(data) {
        if (data.taskStatus === '已完结') {
          this.$message.warning('该委托单的检测报告已完结！')
          return
        } else if (data.taskStatus !== '已完成') {
          this.$message.warning('该委托单的检测报告未完成，无法完结！')
          return
        }
        this.modalData = data
        this.isShowEndingReport = true
      },
      uploadTestReport(data) {
        if (data.taskStatus === '已完结') {
          this.$message.warning('该委托单的检测报告已完结，不能重新上传检测报告！')
          return
        }
        if (data.taskStatus !== '进行中' && data.taskStatus !== '驳回' && data.taskStatus !== '已完成') {
          this.$message.warning(data.taskStatus + '的状态不能进行上传')
          return
        }
        if (data.taskStatus === '已完成') {
          this.modalData = data
          this.isShowReUploadReport = true
        } else {
          this.modalData = data
          this.modalData.isView = false
          this.isShowUploadTestReport = true
        }
      },
      assignUploader(data) {
        // if (data.taskStatus !== '待处理') {
        //   this.$message.warning(data.taskStatus + '的状态不能分配责任人')
        //   return
        // }
        this.isShowAssignUploader = true
        this.assignResponsibleItem = data
      },
      handlePageChange(value) {
        let {current, pageSize} = value
        this.storeList[this.arrName[this.activeKey]].pagination.pageNo = current
        this.storeList[this.arrName[this.activeKey]].pagination.pageSize = pageSize
        this.getTodoTaskList()
      },
      assignUploaderModelCancel() {
        this.isShowAssignUploader = false
        this.responsibleMan = null
        this.responsibleManCode = null
        this.assignResponsibleItem = null
        this.responsibleManQueryParam = {}
      },
      assignUploaderModelOk() {
        if (!this.responsibleMan || !this.responsibleManCode) {
          this.$message.warning('请选择责任人')
          return
        }
        updateReportUploader({
          id: this.assignResponsibleItem.id, responsibleName: this.responsibleMan,
          responsibleCode: this.responsibleManCode, taskStatus: "进行中"
        }).then(res => {
          if (res.success) {
            this.getTodoTaskList()
            this.getTodoTaskStatistics()
            this.$message.success('分配负责人成功')
          } else {
            return this.$message.error("错误提示：" + res.message)
          }
        }).finally(() => {
          this.isShowAssignUploader = false
          this.responsibleMan = null
          this.responsibleManCode = null
          this.assignResponsibleItem = null
          this.responsibleManQueryParam = {}
        })
      },
      customResponsibleNameRow(row, index) {
        return {
          on: {
            click: () => {
              this.responsibleMan = row.name
              this.responsibleManCode = row.account
              this.responsibleNameVisible = false
            }
          }
        }
      },
      taskStatusModelCancel() {
        this.isShowTaskStatus = false
        this.changeTaskStatusItem = null
        this.toUpdateTaskStatus = null
      },
      taskStatusModelOk() {
        if (!this.toUpdateTaskStatus) {
          this.$message.warning('请选择任务状态')
          return
        }
        updateReportTaskStatus({ id: this.changeTaskStatusItem.id, taskStatus: this.toUpdateTaskStatus }).then(res => {
          if (res.success) {
            this.getTodoTaskList()
            this.getTodoTaskStatistics()
            this.$message.success('修改任务状态成功')
          } else {
            return this.$message.error("错误提示：" + res.message)
          }
        }).finally(() => {
          this.toUpdateTaskStatus = null
          this.changeTaskStatusItem = null
          this.isShowTaskStatus = false
        })
      },
      handleUploadCancel() {
        this.isShowUploadTestReport = false
      },
      handleAuditCancel() {
        this.isShowAuditTestReport = false
      },
		}
	}
</script>

<style lang="less" scoped>
	:root {
		--height: 600px;
	}

	p {
		margin: 0;
	}

	.container {
		display: flex;
		flex-direction: column;
		height: 100%;
		color: #333333;
		font-size: 16px;
		font-weight: 400;
		line-height: 1;
		padding: 16px;
	}

	// 标题部分
	.head-wrapper {
		display: flex;
		justify-content: space-between;
		height: 68px;
		margin-right: 50px;
	}

	.salutation {
		margin-right: 35px;
	}

	.salutation p:first-child {
		font-size: 35px;
		font-weight: 500;
		margin-bottom: 10px;
	}

	.salutation p:last-child {
		color: #666;
	}

	.head-wrapper .circle-block {
		display: flex;
		justify-content: space-around;
		align-items: center;
	}

	.circle-block .data-packet {
		display: flex;
		align-items: center;
	}

	.circle-block .icon {
		width: 60px;
		height: 60px;
		font-size: 33px;
		color: #fff;
		border-radius: 50%;
		margin-right: 10px;
		box-sizing: content-box;
		background-clip: padding-box !important;

		display: flex;
		justify-content: center;
		align-items: center;
		flex-shrink: 0;
	}

	.circle-block .detail p:first-child {
		font-size: 25px;
		font-weight: 500;
		margin-bottom: 5px;
	}

	.circle-block .detail p:last-child {
		color: #666;
	}

	.circle-block .line {
		margin: 0 20px;
		width: 0;
		height: 55px;
		border: 1px solid #bfbaba;
	}

	//内容部分
	.content-wrapper {
		display: flex;
		flex: 1;
		margin-top: 12px;
	}

	.content-wrapper .left {
		margin-right: 14px;
		width: 75%;
		height: 100%;
		position: relative;
	}

	.left .options {
		display: flex;
		position: absolute;
		top: 0;
		right: 0;
		z-index: 999;
	}

	.left .tab {
		padding: 5px;
		background-color: #fff;
		/* box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.25); */
	}

	.left .icon-btn {
		font-size: 25px;
		margin: auto;
		cursor: pointer;
	}

	// 内容--右边
	.content-wrapper .right {
		height: 100%;
		width: 25%;
	}

	.right .block {
		height: calc((100vh - 40px - 16px - 16px - 68px - 12px - 12px) / 2);
		background-color: #fff;
		border-radius: 10px;
		/* box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.25); */
		position: relative;
	}

	.block .title {
		padding: 12px 0;
		position: sticky;
		top: 0;
	}

	.block .title img {
		width: 20px;
		height: 35px;
		position: absolute;
		top: 0;
		left: 13px;
	}

	.block .title .text {
		display: block;
		line-height: 1;
		margin-left: 40px;
	}

	/* sop */
	.block .title .sop-block {
		margin: 0 20px 0 40px;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.block .empty-block {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}

	.block .box {
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		overflow: auto;
		box-sizing: border-box;
	}

	.block .detail {
		margin-left: 10px;
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-start;
		height: calc(100% - 1.0156vw - 1.25vw);
	}

	.block .detail .detail-block {
		display: flex;
		flex: 1;
		height: 60px;
		width: 50px;
		max-width: 50px;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		font-size: 12px;
		margin: 0 10px 10px 0;
	}

	.block .detail .detail-block img {
		width: 40px;
		height: 40px;
		margin-bottom: 4px;
		border-radius: 50%;
	}

	.block .row-content {
		display: block;
		height: 100%;
		height: calc(100% - 24px - 32px - 8px);
		padding: 0 20px;
		overflow: auto;
	}

	.block .row-content .row {
		width: 100%;
		font-size: 12px;
		padding-bottom: 10px;
		border-bottom: 1px solid #bfbaba;
		margin-top: 15px;
	}

	.block .row-content .row:hover {
		color: #1890ff;
	}

	// 通用
	.mb12 {
		margin-bottom: 12px;
	}

	.mr0 {
		margin-right: 0 !important;
	}

	.mr8 {
		margin-right: 8px;
	}

	.mr10 {
		margin-right: 10px;
	}

	.oneline {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
	/* 表头筛选 */
	.tester-filter-dropdown{
		width: 134px;
		max-height: 220px;
		overflow-y: scroll;
		padding: 0 7px;
	}
	.tester-filter-input{
		width: 120px;
		position: sticky;
		top: 0;
		background-color: #fff;
		z-index: 1;
		padding: 7px 0;
	}

	/* 表头筛选 */

	.filter-dropdown .search-btns {
		width: 60px;
		height: 21px;
	}

	.filter-dropdown .btn-wrapper {
		border-top: 1px solid #e8e8e8;
		margin-top: 7px;
		display: flex;
		justify-content: space-around;
		align-items: center;
		font-size: 14px;
	}


	/deep/ #outTable .ant-table-thead>tr>th {
		font-size: 13px;
    padding: 5px;
    color: rgba(0, 0, 0, .85);
    font-weight: 500;
	}

	/deep/#outTable .ant-table-tbody>tr>td {
    padding: 0px;
    color: #333;
    font-size: 12px;
    font-weight: 400;
	}


  /deep/.ant-table-thead>tr>th {
    line-height: 1;
    font-size: 13px;
    font-weight: 500;
    padding: 8px;
    height: 32px !important;
  }

  /deep/.ant-table-tbody>tr>td {
    font-size: 12px;
    font-weight: 400;
    padding: 8px;
  }

	// table
	/deep/.ant-table-body {
		height: var(--height);
		overflow-y: auto;
	}

	/deep/.ant-table-thead {
		position: sticky;
		top: 0;
		z-index: 1;
	}

	/deep/.ant-table-placeholder {
		border: none;
		position:absolute;
		top:50%;
		left:50%;
		transform:translate(-50%, -50%);
	}
	/deep/.ant-pagination.mini .ant-pagination-options-quick-jumper{
		height: 32px;
		line-height: 32px;
	}
	/deep/.ant-table-pagination.ant-pagination{
		margin: 8px 0;
	}

	/* 右侧：搜索框 + 按钮 */
	/deep/.options .ant-input-affix-wrapper .ant-input {
		height: 32px;
		width: 250px;
	}

	/deep/ .ant-btn-sm {
		height: 32px;
	}

	// table btn
	/deep/.btn-slot .ant-btn {
		padding: 0 5px;
	}

	/deep/.btn-slot .ant-btn span {
		font-size: 12px;
	}


	/deep/.pbi-tabs{
		font-size: 14px;
	}
	/deep/.pbi-tab-item{
		padding: 12px 16px;
	}
	/deep/.pbi-tab-active{
		font-size: 15px;
	}

  /deep/.ant-table-thead > tr > th.ant-table-column-has-actions.ant-table-column-has-filters {
    padding-right: 20px !important;
  }

  /deep/.ant-btn-group .ant-btn {
    height: 28px;
    border: 0
  }
</style>