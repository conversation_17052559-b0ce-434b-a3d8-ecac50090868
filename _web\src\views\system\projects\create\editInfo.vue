<template>
	<a-modal
		title="信息编辑"
		:width="600"
		:visible="visible"
		:confirmLoading="confirmLoading"
		centered
		@ok="handleSubmit"
		@cancel="handleCancel"
	>
		<a-spin :spinning="confirmLoading">
			<a-form :form="form">
				<a-form-item label="产品分类" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
					<a-select
						class="input-form"
						placeholder="请选择产品分类"
						:options="classOption"
						v-decorator="['productClassification', { rules: [{ required: true, message: '请选择产品分类！' }] }]"
					>
					</a-select>
				</a-form-item>
				<a-form-item label="产品类别" :labelCol="labelCol" :wrapperCol="wrapperCol">
					<treeselect
						class="input-form"
						:limit="3"
						@input="productCateSelectTree"
						placeholder="请选择产品类别"
						:multiple="true"
						:options="productCateTreeData"
						:normalizer="normalizer"
						value-consists-of="LEAF_PRIORITY"
						:didepartmentCateSelectTreesable-branch-nodes="true"
						v-decorator="['productCate', { rules: [{ required: true, message: '请选择产品类别！' }] }]"
					>
					</treeselect>
				</a-form-item>
				<a-form-item label="产品型号" :labelCol="labelCol" :wrapperCol="wrapperCol">
					<a-input
						class="input-form"
						placeholder=""
						v-decorator="['productType', { rules: [{ required: true, message: '请输入产品型号！' }] }]"
					/>
				</a-form-item>
				<a-form-item label="产品名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
					<a-input
						class="input-form"
						placeholder=""
						v-decorator="['productName', { rules: [{ required: true, message: '请输入产品名称！' }] }]"
					/>
				</a-form-item>
				<a-form-item label="项目名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
					<a-input
						class="input-form"
						placeholder=""
						v-decorator="['projectName', { rules: [{ required: true, message: '请输入项目名称！' }] }]"
					/>
				</a-form-item>
				<a-form-item label="所属部门" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <treeselect
            class="input-form"
            :limit="3"
            @input="departmentCateSelectTree"
            placeholder="请选择所属部门"
            :multiple="false"
            :options="departmentCateTreeData"
            :normalizer="normalizer"
            value-consists-of="LEAF_PRIORITY"
            :disable-branch-nodes="true"
            :show-count="true"
            v-decorator="['department', { rules: [{ required: true, message: '请选择所属部门！' }] }]"
          >
          </treeselect>
				</a-form-item>
				<a-form-item label="项目等级" :labelCol="labelCol" :wrapperCol="wrapperCol">
					<a-select
						class="input-form"
						style="width: 130px"
						placeholder=""
						:options="productLevelOption"
						v-decorator="['productLevel', { rules: [{ required: true, message: '请选择项目等级！' }] }]"
					>
					</a-select>
				</a-form-item>
				<a-form-item label="客户" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
					<a-input
						class="input-form"
						v-decorator="['customer', { rules: [{ required: true, message: '请输入客户！' }] }]"
					/>
				</a-form-item>
				<a-form-item label="定点状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
					<a-select
						class="input-form"
						placeholder=""
						:options="fixedStateOption"
						v-decorator="['fixedState', { rules: [{ required: true, message: '请选择定点状态！' }] }]"
					>
					</a-select>
				</a-form-item>
				<a-form-item label="计划定点日期" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
					<a-date-picker class="input-form" placeholder="" v-decorator="['plannedFixedDate']" />
				</a-form-item>
			</a-form>
		</a-spin>
	</a-modal>
</template>
<script>
import { productMsgToUpdate } from "@/api/modular/system/report"
import moment from "moment"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"
import { getCateTree } from "@/api/modular/system/topic"
import Vue from "vue"
import { DICT_TYPE_TREE_DATA } from "@/store/mutation-types"
export default {
	components: {
		Treeselect
	},
	data() {
		return {
			issueId: 0,
			normalizer(node) {
				return {
					id: node.value,
					label: node.title,
					children: node.children && node.children.length > 0 ? node.children : 0
				}
			},
			productCateTreeData: [],
			departmentCateTreeData: [],
			fixedStateOption: [],
			productLevelOption: [],
			classOption: [
				{ value: '1', label: "预研产品" },
				{ value: '2', label: "A|B新产品" },
				{ value: '3', label: "试产新产品" },
				{ value: '4', label: "量产品" },
				{ value: '5', label: "其他" },
				{ value: '6', label: "停止" }
			],
			labelCol: {
				xs: { span: 24 },
				sm: { span: 6 }
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: { span: 18 }
			},
			visible: false,
			confirmLoading: false,
			visibleDef: false,
			form: this.$form.createForm(this)
		}
	},
	methods: {
		moment,
    productCateSelectTree(value, label, extra) {
      this.form.setFieldsValue({
        productCate: value
      })
    },
		departmentCateSelectTree(value, label, extra) {
			this.form.setFieldsValue({
        department: value
			})
		},
		callGetDepartmentCateTree() {
			this.confirmLoading = true
			getCateTree({
				fieldName: "department"
			})
				.then(res => {
					if (res.success) {
						this.departmentCateTreeData = res.data
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
					this.confirmLoading = false
				})
				.catch(err => {
					this.confirmLoading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		},
		callGetProductCateTree() {
			this.confirmLoading = true
			getCateTree({
				fieldName: "productCate"
			})
				.then(res => {
					if (res.success) {
						this.productCateTreeData = res.data
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
					this.confirmLoading = false
				})
				.catch(err => {
					this.confirmLoading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		},

		callGetSelectOption() {
			this.fixedStateOption = this.getDict("fix_status").map((item, index) => {
				return Object.assign({}, { value: item.code, label: item.name })
			})
			this.productLevelOption = this.getDict("product_level_status").map((item, index) => {
				return Object.assign({}, { value: item.code, label: item.name })
			})
		},

		getDict(code) {
			const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
			return dictTypeTree.filter(item => item.code == code)[0].children
		},
		getSelectedItem(value, data, title) {
			let $title = ""
			for (const item of data) {
				//在根节点找到对应选项
				if (!title && item.value == value) {
					$title = item.title
				} else if (!title && item.children) {
					//根节点未找到继续递归查找
					$title = item.title
					for (const e of item.children) {
						if (e.value == value) {
							$title = $title + "->" + e.title
							return $title
						}
					}
				}
				//在子节点找到对应选项
				if (title && item.value == value) {
					$title = title + "-" + item.title
				} else if (title && item.children) {
					//当前子节点未找到继续递归向下查找
					for (const e of item.children) {
						if (e.value == value) {
							$title = $title + "->" + e.title
							return $title
						}
					}
				}
			}
		},
		// 初始化方法
		edit(record) {
			this.issueId = record.issueId
			this.callGetDepartmentCateTree()
			this.callGetProductCateTree()
			this.callGetSelectOption()
			this.visible = true
			setTimeout(() => {
				this.form.setFieldsValue({
					productName: record.productProjectName,
					productLevel: record.level <= 0 ? null : record.level + "",
					fixedState: record.fixedState <= 0 ? null : record.fixedState + "",
					productType: record.productType,
					projectName: record.projectName,
					customer: record.customer,
					productClassification: record.productClassification,
          department: record.department,
					productCate: record.productCateOptionBeans.map(item => item.id)
				})
				if (record.plannedFixedDate != "-") {
					this.form.setFieldsValue({ plannedFixedDate: moment(record.plannedFixedDate, "YYYY-MM-DD") })
				}
			}, 100)
		},
		handleSubmit() {
			this.form.validateFields((err, values) => {
				this.confirmLoading = true
				console.log(err)
				if (!err) {
					console.log(err)
					console.log(values)

					const params = {
						issueId: this.issueId,
						productMultiCate: values.productCate + "",
						productType: values.productType,
						productName: values.productName,
						projectName: values.projectName,
						department: values.department,
						projectLevel: values.productLevel,
						fixedState: values.fixedState,
						customer: values.customer,
						productClassification: values.productClassification,
						plannedFixedDate: values.plannedFixedDate ? values.plannedFixedDate.format("YYYY-MM-DD") : ""
					}
					productMsgToUpdate(params)
						.then(res => {
							if (res.result) {
								this.$message.success("产品信息更新完成", 1)
								this.handleCancel()
								this.$emit("ok")
							} else {
								this.$message.error("错误提示：" + res.message, 1)
							}
						})
						.catch(res => {
							this.$message.error("错误提示：" + res.message, 1)
						})
						.finally(() => {
							this.confirmLoading = false
						})
				} else {
					setTimeout(() => {
						this.$message.warn("请将填写信息完整", 1)
						this.confirmLoading = false
					}, 300)
				}
			})
		},
		handleCancel() {
			this.form.resetFields()
			this.visible = false
		}
	}
}
</script>
<style lang="less" scoped="">
/deep/.vue-treeselect__multi-value-item {
	background: transparent;
	font-size: 13px;
	vertical-align: initial;
}
/deep/.vue-treeselect {
	/* display: inline-block; */
	/* min-width: 80%;
    max-width: 90%; */
	margin-top: 8px;
}
/deep/.vue-treeselect__control {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 24px;
	overflow: hidden;
	border-radius: initial;
}
/deep/.vue-treeselect__control * {
	padding: 0 !important;
	margin: 0 !important;
	line-height: initial !important;
	white-space: nowrap;
}
/deep/.vue-treeselect__value-remove {
	color: #e9e9e9;
}
/deep/.vue-treeselect__multi-value-item {
	color: rgba(0, 0, 0, 0.65);
}
/deep/.ant-form-item{
	margin-bottom: 1px;
}
</style>
