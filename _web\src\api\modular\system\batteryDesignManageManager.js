import { axios } from '@/utils/request'

export function list(parameter) {
    return axios({
        url: '/batteryDesignManager/list',
        method: 'post',
        data: parameter
    })
}

export function update(parameter) {
    return axios({
        url: '/batteryDesignManager/update',
        method: 'post',
        data: parameter
    })
}

export function otherList(parameter) {
    return axios({
        url: '/batteryDesignManagerOther/list',
        method: 'post',
        data: parameter
    })
}

export function otherUpdate(parameter) {
    return axios({
        url: '/batteryDesignManagerOther/update',
        method: 'post',
        data: parameter
    })
}

export function exportExcel(parameter) {
  return axios({
    url: '/batteryDesignManager/export',
    method: 'get',
    params: parameter
  })
}

export function exportExcel1(parameter) {
  return axios({
    url: '/batteryDesignManager/export1',
    method: 'get',
    params: parameter,
    responseType: 'blob'
  })
}
