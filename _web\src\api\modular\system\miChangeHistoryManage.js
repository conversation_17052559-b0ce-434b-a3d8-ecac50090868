import { axios } from '@/utils/request'

export function getMIChangeHistoryList (parameter) {
  return axios({
    url: '/miChangeHistory/getMIChangeHistoryList',
    method: 'get',
    params: parameter
  })
}

export function insertMIChangeHistory (parameter) {
  return axios({
    url: '/miChangeHistory/insertMIChangeHistory',
    method: 'post',
    data: parameter
  })
}

export function updateMIChangeHistory (parameter) {
  return axios({
    url: '/miChangeHistory/updateMIChangeHistory',
    method: 'post',
    data: parameter
  })
}
