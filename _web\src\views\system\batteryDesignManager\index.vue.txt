<template>
  <div>
    <div class="wrapper">
      <div class="h1">
        设计配方
      </div>
      <a-table
        :scroll="{ y: windowHeight }"
        :columns="columns"
        :data-source="dataSource"
        :row-key="(record) => record.id"
        :pagination="false"
        :rowClassName="(record,index)=>{ return 'fold'+groupCls.indexOf(record.projectCategory+record.projectCategory2) }"
      >
        <template slot="batteryName">
          {{batteryName}}设计管理
        </template>
        <div slot="design"  slot-scope="text,record"><input :value="text" @change="updateData($event,record,'design')"/></div>
        <div slot="eve"  slot-scope="text,record"><input :value="text" @change="updateData($event,record,'eve')"/></div>
        <div slot="reference"  slot-scope="text,record"><input :value="text" @change="updateData($event,record,'reference')"/></div>
        <div slot="allowableTolerance"  slot-scope="text,record"><input :value="text" @change="updateData($event,record,'allowableTolerance')"/></div>
        <div slot="remark"  slot-scope="text,record"><input :value="text" @change="updateData($event,record,'remark')"/></div>
        <div slot="input"><input /></div>

        <template slot="checkStatus" slot-scope="text,record">

          <a-select v-if="record.editable" style="width: 100%;outline:none" size="small" :default-value="text" @change="updateSelectData($event,record)">
            <a-select-option :value="parseInt(1)">
              满足
            </a-select-option>
            <a-select-option :value="parseInt(2)">
              不满足
            </a-select-option>
            <a-select-option :value="parseInt(3)">
              TBD
            </a-select-option>
            <a-select-option :value="parseInt(0)">
              /
            </a-select-option>
            
          </a-select>

          <span class="spanstatus" :class="{'success1':text == 1,'fail1':text == 2,'warn1':text == 3}" v-else @click="() => edit1(record.id)">{{checkStatus[text]}}</span>

        </template>
        <div class="divcls" slot="divcls" slot-scope="text">{{text}}</div>
        <div class="divcls div_border_right" slot="unitcls" slot-scope="text">{{text}}</div>
      </a-table>
    </div>
  </div>
</template>
<script>

  import { list,update} from '@/api/modular/system/batteryDesignManageManager'
  export default {
    components: {
    },
    data () {
      return {
        checkStatus:{0:'/',1:'满足',2:'不满足',3:'TBD'},
        groupCls:[],
        windowHeight: document.documentElement.clientHeight - 175,
        manager_project:0,
        manager_performance:0,
        manager_size:0,
        manager_positive:0,
        manager_positive_gram_capacity:0,
        manager_positive_formula:0,
        manager_positive_size:0,
        manager_positive_workmanship:0,
        manager_negative:0,
        manager_electrolyte:0,
        manager_electrolyte_component:0,
        manager_diaphragm:0,
        manager_negative_gram_capacity:0,
        manager_negative_formula:0,
        manager_negative_size:0,
        manager_negative_workmanship:0,
        manager_diaphragm_coating:0,
        manager_coiling_core_size:0,
        manager_coiling_overhang:0,
        manager_structural_positive_terminal:0,
        manager_structural_negative_terminal:0,
        manager_structural_negative_rivet1:0,
        manager_structural_cover:0,
        manager_structural_positive_bus_plate1:0,
        manager_structural_positive_rivet_insulating:0,
        manager_structural_negative_terminal_insulating:0,
        manager_structural_negative_rivet_insulating:0,
        manager_structural_positive_bus_plate_insulating:0,
        manager_structural_negative_bus_plate_insulating:0,
        manager_structural_jr_insulating:0,
        manager_structural_insulating:0,
        manager_structural_other:0,
        manager_structural_total:0,
        manager_structural_negative_bus_plate1:0,
        manager_structural_vent:0,
        manager_coiling_rolling_pressing:0,
        manager_diaphragm_size:0,
        manager_diaphragm_porosity:0,
        manager_electrolyte_workmanship:0,
        manager_coiling_needle:0,
        manager_coiling:0,
        manager_coiling_coiling:0,
        manager_structural:0,
        manager_structural_shell:0,
        manager_structural_positive_rivet1:0,
        manager_structural_positive_terminal_insulating:0,
        dataSource:[],
        batteryName:'',
        batteryId:null,
        // 表头
        columns: [
          {
            scopedSlots: {
              title: 'batteryName'
            },
            dataIndex: 'title',
            children:[
              {
                title:'设计管理',
                dataIndex:'designManager',
                align: 'center',
                children:[
                  {
                    title: '序号',
                    dataIndex: 'index',
                    align: 'center',
                    width: 100,
                    customRender: (text, record, index) => (<div class='divcls div_border_right div_btns'><a onClick={$event=>{this.isShow($event)}} class='a-plus hide'></a><a class='a-minus' onClick={$event=>{this.isHide($event)}}></a><span>{index+1}</span></div>)
                    },
                  {
                    title: '项目',
                    dataIndex: 'projectCategory',
                    colSpan: 3,
                    width: 50,
                    align: 'center',
                   customRender: (text, record, index) => {
                      const obj = {
                        children: (<div class='divcls div_border_right'>{text}</div>),
                        attrs: {}
                      }
                      obj.attrs.rowSpan = 1
                      if(record.code == 'manager_project_customer'){
                        obj.attrs.rowSpan = this.manager_project
                        obj.attrs.colSpan = 2
                      }else
                      if(record.code == 'manager_performance_fast_charging_time'){
                        obj.attrs.rowSpan = this.manager_performance
                        obj.attrs.colSpan = 2
                      }else
                      if(record.code == 'manager_size_core_diameter'){
                        obj.attrs.rowSpan = this.manager_size
                        obj.attrs.colSpan = 2
                      }else
                      if(record.code == 'manager_positive_gram_capacity_active'){
                        obj.attrs.rowSpan = this.manager_positive
                      }else
                      if(record.code == 'manager_negative_gram_capacity_active'){
                        obj.attrs.rowSpan = this.manager_negative
                      }else
                      if(record.code == 'manager_electrolyte_component_solvent'){
                        obj.attrs.rowSpan = this.manager_electrolyte
                      }else
                      if(record.code == 'manager_diaphragm_coating_base'){
                        obj.attrs.rowSpan = this.manager_diaphragm
                      }else
                      if(record.code == 'manager_structural_shell_material'){
                        obj.attrs.rowSpan = this.manager_structural
                      }else
                      if(record.code == 'manager_coiling_overhang_al'){
                        obj.attrs.rowSpan = this.manager_coiling
                      }else if(record.code.startsWith('manager_project') ||
                        record.code.startsWith('manager_performance') ||
                        record.code.startsWith('manager_negative') ||
                        record.code.startsWith('manager_diaphragm') ||
                        record.code.startsWith('manager_electrolyte') ||
                        record.code.startsWith('manager_coiling') ||
                        record.code.startsWith('manager_structural') ||
                        record.code.startsWith('manager_size') ||
                        record.code.startsWith('manager_positive')
                      ){
                        obj.attrs.rowSpan = 0
                      }

                      return obj

                    }
                  }, {
                    title: '项目',
                    dataIndex: 'projectCategory2',
                    colSpan: 0,
                    width: 50,
                    align: 'center',
                    customRender: (text, record, index) => {
                      const obj = {
                        children: (<div class='divcls div_border_right'>{text}</div>),
                        attrs: {}
                      }
                      obj.attrs.colSpan = 1
                      if(record.code.startsWith('manager_project')){
                        obj.attrs.colSpan = 0
                      }
                      if(record.code.startsWith('manager_size')){
                        obj.attrs.colSpan = 0
                      }
                      if(record.code.startsWith('manager_performance')){
                        obj.attrs.colSpan = 0
                      }
                      obj.attrs.rowSpan = 1
                      if(record.code == 'manager_positive_gram_capacity_active'){
                        obj.attrs.rowSpan = this.manager_positive_gram_capacity
                      }else
                       if(record.code == 'manager_positive_formula_active'){
                         obj.attrs.rowSpan = this.manager_positive_formula
                      }else
                       if(record.code == 'manager_positive_size_al_foil_model'){
                         obj.attrs.rowSpan = this.manager_positive_size
                      }else
                       if(record.code == 'manager_positive_workmanship_areal_density_a'){
                         obj.attrs.rowSpan = this.manager_positive_workmanship
                      }else
                       if(record.code == 'manager_negative_gram_capacity_active'){
                         obj.attrs.rowSpan = this.manager_negative_gram_capacity
                      }else
                       if(record.code == 'manager_electrolyte_component_solvent'){
                         obj.attrs.rowSpan = this.manager_electrolyte_component
                      }else
                       if(record.code == 'manager_negative_formula_active'){
                         obj.attrs.rowSpan = this.manager_negative_formula
                      }else
                       if(record.code == 'manager_negative_size_cu_foil_model'){
                         obj.attrs.rowSpan = this.manager_negative_size
                      }else
                       if(record.code == 'manager_negative_workmanship_areal_density_a'){
                         obj.attrs.rowSpan = this.manager_negative_workmanship
                      }else
                       if(record.code == 'manager_diaphragm_size_total_thickness'){
                         obj.attrs.rowSpan = this.manager_diaphragm_size
                      }else
                       if(record.code == 'manager_diaphragm_porosity_porosity'){
                         obj.attrs.rowSpan = this.manager_diaphragm_porosity
                      }else
                       if(record.code == 'manager_electrolyte_workmanship_liquid_injection_coefficient'){
                         obj.attrs.rowSpan = this.manager_electrolyte_workmanship
                      }else
                       if(record.code == 'manager_coiling_needle_needle_diameter'){
                         obj.attrs.rowSpan = this.manager_coiling_needle
                      }else
                       if(record.code == 'manager_coiling_coiling_coiling_diameter'){
                         obj.attrs.rowSpan = this.manager_coiling_coiling
                      }else
                       if(record.code == 'manager_coiling_rolling_pressing_positive'){
                         obj.attrs.rowSpan = this.manager_coiling_rolling_pressing
                      }else
                       if(record.code == 'manager_coiling_core_size_positive_terminal_foil'){
                         obj.attrs.rowSpan = this.manager_coiling_core_size
                      }else
                       if(record.code == 'manager_diaphragm_coating_base'){
                         obj.attrs.rowSpan = this.manager_diaphragm_coating
                      }else
                       if(record.code == 'manager_structural_shell_material'){
                         obj.attrs.rowSpan = this.manager_structural_shell
                      }else
                       if(record.code == 'manager_structural_positive_rivet1_material'){
                         obj.attrs.rowSpan = this.manager_structural_positive_rivet1
                      }else
                       if(record.code == 'manager_structural_positive_terminal_insulating_material'){
                         obj.attrs.rowSpan = this.manager_structural_positive_terminal_insulating
                      }else
                       if(record.code == 'manager_coiling_overhang_al'){
                         obj.attrs.rowSpan = this.manager_coiling_overhang
                      }else
                       if(record.code == 'manager_structural_positive_terminal1_material'){
                         obj.attrs.rowSpan = this.manager_structural_positive_terminal
                      }else
                       if(record.code == 'manager_structural_negative_terminal1_material'){
                         obj.attrs.rowSpan = this.manager_structural_negative_terminal
                      }else
                       if(record.code == 'manager_structural_negative_rivet1_material'){
                         obj.attrs.rowSpan = this.manager_structural_negative_rivet1
                      }else
                       if(record.code == 'manager_structural_cover_material'){
                         obj.attrs.rowSpan = this.manager_structural_cover
                      }else
                       if(record.code == 'manager_structural_positive_bus_plate1_material'){
                         obj.attrs.rowSpan = this.manager_structural_positive_bus_plate1
                      }else
                       if(record.code == 'manager_structural_positive_rivet_insulating_material'){
                         obj.attrs.rowSpan = this.manager_structural_positive_rivet_insulating
                      }else
                       if(record.code == 'manager_structural_negative_terminal_insulating_material'){
                         obj.attrs.rowSpan = this.manager_structural_negative_terminal_insulating
                      }else
                       if(record.code == 'manager_structural_negative_rivet_insulating_material'){
                         obj.attrs.rowSpan = this.manager_structural_negative_rivet_insulating
                      }else
                       if(record.code == 'manager_structural_positive_bus_plate_insulating_material'){
                         obj.attrs.rowSpan = this.manager_structural_positive_bus_plate_insulating
                      }else
                       if(record.code == 'manager_structural_negative_bus_plate_insulating_material'){
                         obj.attrs.rowSpan = this.manager_structural_negative_bus_plate_insulating
                      }else
                       if(record.code == 'manager_structural_jr_insulating_material'){
                         obj.attrs.rowSpan = this.manager_structural_jr_insulating
                      }else
                       if(record.code == 'manager_structural_insulating_material'){
                         obj.attrs.rowSpan = this.manager_structural_insulating
                      }else
                       if(record.code == 'manager_structural_other_weight'){
                         obj.attrs.rowSpan = this.manager_structural_other
                      }else
                       if(record.code == 'manager_structural_total_weight'){
                         obj.attrs.rowSpan = this.manager_structural_total
                      }else
                       if(record.code == 'manager_structural_negative_bus_plate1_material'){
                         obj.attrs.rowSpan = this.manager_structural_negative_bus_plate1
                      }else
                       if(record.code == 'manager_structural_vent_material'){
                         obj.attrs.rowSpan = this.manager_structural_vent
                      }else
                       if(record.code.startsWith('manager_positive_gram_capacity') ||
                          record.code.startsWith('manager_positive_formula') ||
                          record.code.startsWith('manager_structural_vent') ||
                          record.code.startsWith('manager_structural_positive_bus_plate1') ||
                          record.code.startsWith('manager_structural_positive_rivet_insulating') ||
                          record.code.startsWith('manager_structural_negative_terminal_insulating') ||
                          record.code.startsWith('manager_structural_negative_bus_plate1') ||
                          record.code.startsWith('manager_structural_negative_rivet_insulating') ||
                          record.code.startsWith('manager_electrolyte_component') ||
                          record.code.startsWith('manager_positive_workmanship') ||
                          record.code.startsWith('manager_negative_gram_capacity') ||
                          record.code.startsWith('manager_coiling_rolling_pressing') ||
                          record.code.startsWith('manager_electrolyte_workmanship') ||
                          record.code.startsWith('manager_positive_workmanship') ||
                          record.code.startsWith('manager_negative_formula') ||
                          record.code.startsWith('manager_negative_size') ||
                          record.code.startsWith('manager_negative_workmanship') ||
                          record.code.startsWith('manager_structural_insulating') ||
                          record.code.startsWith('manager_structural_jr_insulating') ||
                          record.code.startsWith('manager_structural_positive_bus_plate_insulating') ||
                          record.code.startsWith('manager_structural_negative_bus_plate_insulating') ||
                          record.code.startsWith('manager_coiling_core_size') ||
                          record.code.startsWith('manager_diaphragm_coating') ||
                          record.code.startsWith('manager_structural_positive_terminal_insulating') ||
                          record.code.startsWith('manager_electrolyte_workmanship_liquid_injection_volume') ||
                          record.code.startsWith('manager_diaphragm_size') ||
                          record.code.startsWith('manager_diaphragm_porosity') ||
                          record.code.startsWith('manager_coiling_needle') ||
                          record.code.startsWith('manager_coiling_overhang') ||
                          record.code.startsWith('manager_structural_negative_rivet1') ||
                          record.code.startsWith('manager_structural_positive_terminal1') ||
                          record.code.startsWith('manager_structural_negative_terminal1') ||
                          record.code.startsWith('manager_coiling_coiling') ||
                          record.code.startsWith('manager_structural_shell') ||
                          record.code.startsWith('manager_structural_cover') ||
                          record.code.startsWith('manager_structural_positive_rivet1') ||
                          record.code.startsWith('manager_positive_size')
                      ){
                        obj.attrs.rowSpan = 0
                      }
                      return obj

                    }
                  },{
                    title: '项目',
                    dataIndex: 'projectName',
                    colSpan: 0,
                    width: 200,
                    scopedSlots: { customRender: 'divcls' },
                  },
                  {
                    title: '单位',
                    dataIndex: 'unit',
                    align: 'center',
                    scopedSlots: { customRender: 'unitcls' },
                  },
                  {
                    title: '设计信息',
                    dataIndex: 'design',
                    align: 'center',
                    scopedSlots: { customRender: 'design' },
                  },
                  {
                    title: '测试值',
                    dataIndex: 'eve',
                    align: 'center',
                    scopedSlots: { customRender: 'eve' },
                  },

                  {
                    title: '状态识别',
                    dataIndex: 'checkStatus',
                    align: 'center',
                    scopedSlots: { customRender: 'checkStatus' },
                  }
                ]
              },
              {
                title:'设计标准',
                dataIndex:'designStandard',
                align: 'center',
                children:[
                  {
                    title: '基准参考值',
                    dataIndex: 'reference',
                    align: 'center',
                    scopedSlots: { customRender: 'reference' },
                  },{
                    title: '允许公差（±）',
                    dataIndex: 'allowableTolerance',
                    align: 'center',
                    scopedSlots: { customRender: 'allowableTolerance' },
                  },{
                    title: '备注',
                    dataIndex: 'remark',
                    align: 'center',
                    scopedSlots: { customRender: 'remark' },
                  },
                ]
              }
             ]
          },
        ],

      }
    },



    mounted() {
      this.batteryId = this.$route.query.batteryId
      this.getList(true)
    },

    methods: {
      edit1(id){
        const newData = [...this.dataSource];
        const target = newData.find(item => id === item.id);
        this.editingKey = id;
        if (target) {
          target.editable = true;
          this.dataSource = newData;
        }
      },
      isShow(e){
        e.preventDefault()

        this.$nextTick(() => {

          let tr = e.target.parentNode.parentNode.parentNode
          console.log(tr)
          let cls = tr.classList[2]

          let a = e.target
          a.classList.add('hide')
          a.nextElementSibling.classList.remove('hide')

					let items = this.getByClass(document, cls)

          if(tr.children.length > 10){
            let td = a.parentNode.parentNode.nextElementSibling
            td.rowSpan = parseInt(td.rowSpan) + items.length - 1
            if (td.colSpan == 1) {
              let next_td = td.nextElementSibling
              next_td.rowSpan = parseInt(next_td.rowSpan) + items.length - 1
            }
          }

          if (tr.children.length < 11) {
            let td = a.parentNode.parentNode.nextElementSibling
            td.rowSpan = parseInt(td.rowSpan) + items.length - 1
            if (td.colSpan == 1) {
              this.gettr(tr,items.length,false)
            }
          }

					for (let i = 1,j = items.length; i < j; i++) {
            items[i].classList.remove('hide')
          }
          
				})
      },
      isHide(e){
        e.preventDefault()

        this.$nextTick(() => {
          let tr = e.target.parentNode.parentNode.parentNode
          console.log(tr)
          let cls = tr.classList[2]

          let a = e.target
          a.classList.add('hide')
          a.previousElementSibling.classList.remove('hide')

          
					let items = this.getByClass(document, cls)

          if(tr.children.length > 10){
            let td = a.parentNode.parentNode.nextElementSibling
            td.rowSpan = parseInt(td.rowSpan) - items.length + 1
            if (td.colSpan == 1) {
              let next_td = td.nextElementSibling
              next_td.rowSpan = 1
            }
          }

          if (tr.children.length < 11) {
            let td = a.parentNode.parentNode.nextElementSibling
            td.rowSpan = parseInt(td.rowSpan) - items.length + 1
            if (td.colSpan == 1) {
              this.gettr(tr,items.length,true)
            }
          }

					for (let i = 1,j = items.length; i < j; i++) {
            items[i].classList.add('hide')
          }
				})
      },

      gettr(tr,length,flag){
        let _tr = tr.previousElementSibling
        if (_tr.children.length > 10) {
          if (flag) {
            _tr.childNodes[1].rowSpan = parseInt(_tr.childNodes[1].rowSpan) - length + 1
          }else{
            _tr.childNodes[1].rowSpan = parseInt(_tr.childNodes[1].rowSpan) + length - 1
          }
          
          return
        }
        this.gettr(_tr,length,flag)
      },
      getByClass(parent, cls) {
				if (parent.getElementsByClassName) {
					return Array.from(parent.getElementsByClassName(cls));
				} else {
					var res = [];
					var reg = new RegExp(' ' + cls + ' ', 'i')
					var ele = parent.getElementsByTagName('*');
					for (var i = 0; i < ele.length; i++) {
						if (reg.test(' ' + ele[i].className + ' ')) {
							res.push(ele[i]);
						}
					}
					return res;
				}
			},
      init(){
        this.$nextTick(() => {
					let items = this.getByClass(document, 'divcls')
					for (const e of items) {
						var _e = e.parentNode 
            _e.classList.add('tdcls')
            if (e.classList.contains('div_border_right')) {
              _e.classList.add('td_border_right')
            }
            
					}
				})
      },

      hide(){
        this.$nextTick(() => {
					for (let i = 0,j = this.groupCls.length; i < j; i++) {
            let items = this.getByClass(document, 'fold'+i)
            for (let n = 1, m = items.length; n < m; n++) {
              let _items = items[n].getElementsByTagName('a')
              _items[1].classList.add('hide')
            }
          }
				})
      },

      updateData(event,record,column){
        if(event.target.value != '' && event.target.value != null && event.target.value != record[column]){
          let param = {}
          param[column] = event.target.value
          param['id'] = record.id
          update(param).then((res) => {
            this.getList(false)
          })
        }
      },

      updateSelectData(value,record){
          let param = {}
          param['checkStatus'] = value
          param['id'] = record.id
          update(param).then((res) => {
            this.getList(false)
          })
      },


      getList(update){
        list({batteryId:this.batteryId}).then((res) => {
          this.dataSource = res.data
          this.batteryName = res.data[0].batteryName
          if(update){
            for (let i = 0; i < res.data.length; i++) {
              if(res.data[i].code.startsWith('manager_project')){
                this.manager_project++
              }
              if(res.data[i].code.startsWith('manager_performance')){
                this.manager_performance++
              }
              if(res.data[i].code.startsWith('manager_size')){
                this.manager_size++
              }
              if(res.data[i].code.startsWith('manager_positive')){
                this.manager_positive++
              }
              if(res.data[i].code.startsWith('manager_positive_gram_capacity')){
                this.manager_positive_gram_capacity++
              }
              if(res.data[i].code.startsWith('manager_positive_formula')){
                this.manager_positive_formula++
              }
              if(res.data[i].code.startsWith('manager_positive_size')){
                this.manager_positive_size++
              }
              if(res.data[i].code.startsWith('manager_positive_workmanship')){
                this.manager_positive_workmanship++
              }
              if(res.data[i].code.startsWith('manager_negative')){
                this.manager_negative++
              }if(res.data[i].code.startsWith('manager_electrolyte')){
                this.manager_electrolyte++
              }if(res.data[i].code.startsWith('manager_electrolyte_component')){
                this.manager_electrolyte_component++
              }
              if(res.data[i].code.startsWith('manager_diaphragm')){
                this.manager_diaphragm++
              }

              if(res.data[i].code.startsWith('manager_negative_gram_capacity')){
                this.manager_negative_gram_capacity++
              }
              if(res.data[i].code.startsWith('manager_negative_formula')){
                this.manager_negative_formula++
              }
              if(res.data[i].code.startsWith('manager_negative_size')){
                this.manager_negative_size++
              }
              if(res.data[i].code.startsWith('manager_negative_workmanship')){
                this.manager_negative_workmanship++
              }
              if(res.data[i].code.startsWith('manager_diaphragm_coating')){
                this.manager_diaphragm_coating++
              }
              if(res.data[i].code.startsWith('manager_coiling_core_size')){
                this.manager_coiling_core_size++
              }
              if(res.data[i].code.startsWith('manager_coiling_overhang')){
                this.manager_coiling_overhang++
              }
              if(res.data[i].code.startsWith('manager_structural_positive_terminal1')){
                this.manager_structural_positive_terminal++
              }
              if(res.data[i].code.startsWith('manager_structural_negative_terminal1')){
                this.manager_structural_negative_terminal++
              }
              if(res.data[i].code.startsWith('manager_structural_negative_rivet1')){
                this.manager_structural_negative_rivet1++
              }
              if(res.data[i].code.startsWith('manager_structural_cover')){
                this.manager_structural_cover++
              }
              if(res.data[i].code.startsWith('manager_structural_positive_bus_plate1')){
                this.manager_structural_positive_bus_plate1++
              }
              if(res.data[i].code.startsWith('manager_structural_positive_rivet_insulating')){
                this.manager_structural_positive_rivet_insulating++
              }
              if(res.data[i].code.startsWith('manager_structural_negative_terminal_insulating')){
                this.manager_structural_negative_terminal_insulating++
              }
              if(res.data[i].code.startsWith('manager_structural_negative_rivet_insulating')){
                this.manager_structural_negative_rivet_insulating++
              }
              if(res.data[i].code.startsWith('manager_structural_positive_bus_plate_insulating')){
                this.manager_structural_positive_bus_plate_insulating++
              }
              if(res.data[i].code.startsWith('manager_structural_negative_bus_plate_insulating')){
                this.manager_structural_negative_bus_plate_insulating++
              }
              if(res.data[i].code.startsWith('manager_structural_jr_insulating')){
                this.manager_structural_jr_insulating++
              }
              if(res.data[i].code.startsWith('manager_structural_insulating')){
                this.manager_structural_insulating++
              }
              if(res.data[i].code.startsWith('manager_structural_other')){
                this.manager_structural_other++
              }
              if(res.data[i].code.startsWith('manager_structural_total')){
                this.manager_structural_total++
              }
              if(res.data[i].code.startsWith('manager_structural_negative_bus_plate1')){
                this.manager_structural_negative_bus_plate1++
              }
              if(res.data[i].code.startsWith('manager_structural_vent')){
                this.manager_structural_vent++
              }
              if(res.data[i].code.startsWith('manager_coiling_rolling_pressing')){
                this.manager_coiling_rolling_pressing++
              }
              if(res.data[i].code.startsWith('manager_diaphragm_size')){
                this.manager_diaphragm_size++
              }
              if(res.data[i].code.startsWith('manager_diaphragm_porosity')){
                this.manager_diaphragm_porosity++
              }
              if(res.data[i].code.startsWith('manager_electrolyte_workmanship')){
                this.manager_electrolyte_workmanship++
              }
              if(res.data[i].code.startsWith('manager_coiling_needle')){
                this.manager_coiling_needle++
              }
              if(res.data[i].code.startsWith('manager_coiling')){
                this.manager_coiling++
              }
              if(res.data[i].code.startsWith('manager_coiling_coiling')){
                this.manager_coiling_coiling++
              }
              if(res.data[i].code.startsWith('manager_structural')){
                this.manager_structural++
              }
              if(res.data[i].code.startsWith('manager_structural_shell')){
                this.manager_structural_shell++
              }
              if(res.data[i].code.startsWith('manager_structural_positive_rivet1')){
                this.manager_structural_positive_rivet1++
              }
              if(res.data[i].code.startsWith('manager_structural_positive_terminal_insulating')){
                this.manager_structural_positive_terminal_insulating++
              }
            }

            let groupCls = []

            for (const item of res.data) {
              if (groupCls.indexOf(item.projectCategory+item.projectCategory2) < 0) {
                groupCls.push(item.projectCategory+item.projectCategory2)
              }
            }

            this.groupCls = groupCls
          }
          this.init()
          this.hide()
        })
      }
    }
  }
</script>
<style lang="less" scoped="">
  .h1{
    font-size: 18px;
    margin-bottom: 5px;
    padding-left: 6px;
    color: #000;
  }
  /deep/.ant-table-wrapper{
    background: #fff;
  }
  /deep/.table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
  /deep/.ant-table-thead > tr > th{
    padding:2px 0 2px 4px;
    border-bottom: 2px solid #d2d4d7;
    /* border-right: 0; */
    font-size: 13px;
    /* font-weight: bold; */
    background: #f6f6f6;
    color: #000;
  } 
  /deep/.ant-table-tbody > tr > td {
    padding: 0;
    margin: 0;
    border-bottom: 1px solid #d2d4d7;
    font-size: 12px;
  }

  /deep/.tdcls{
    color: #000;
    background: #fff;
    padding: 1px 0 1px 4px !important;
  }

  /deep/.td_border_right{
    border-right: 1px solid #d2d4d7;
  }
  /deep/.td_width{
    width: 50px;
    padding: 0 !important;
  }
  .div_width{
    width: 30px;
    margin: auto;
  }
  input{
    width: 100%;
    border: 0;
    outline: none;
  }
  .wrapper{
    width: 98%;
    border-radius: 20px;
    background: #fff;
    margin-left:15px;
    margin-top: 10px;
    padding: 8px 10px;
    overflow: hidden;
  }
  /deep/.ant-select-selection{
    border: none;
  }
  .spanstatus{
    display: inline-block;
    margin: 2px 0;
    padding: 2px 4px;
    border-radius: 2px;
    width: 50px;
  }
  .success1{
    background: #66b72a;
    color: #fff;
  }
  .warn1 {
    background: #fec303;
    color: #fff;
  }
  .fail1{
    background: #e05328;
    color: #fff;
  }
  a{
    display: inline-block;
    color: rgb(31, 31, 31);
    
    position: relative;
  }
  a::after{
    width: 15px;
    height: 15px;
    display: flex;
    position: absolute;
    border: 1px solid;
    top: -12px;
    left: -20px;
    border-radius: 50%;
    align-content: center;
    justify-content: center;
    align-items: center;
  }
  a.a-plus::after{
    content: '+';
  }
  a.a-minus::after{
    content: '—';
  }
  .div_btns span{
    margin-left: 5px;
    display: inline-block;
    width: 20px;
  }
  /deep/.hide{
    display: none;
  }
</style>
