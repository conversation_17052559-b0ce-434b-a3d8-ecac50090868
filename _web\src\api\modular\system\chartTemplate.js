import { axios } from '@/utils/request'

// 根据reportId查询当前报告所有模版列表
export function getChartTemplateRelationList(reportId, parameter) {
  return axios({
    url: `/editChartTemplate/listByReportId/${reportId}`,
    method: 'get',
    data: parameter
  })
}

// 编辑图表模版-根据数据源模版ID查询模版列表（查询传阅模版库）
export function getShareTemplateList(Id) {
  return axios({
    url: `/editChartTemplate/listByCopyFromId/${Id}`,
    method: 'get'
  })
}

// 编辑图表模版-根据工号查询模版列表（查询用户模版库）
export function getChartTemplateList(userAccount) {
  return axios({
    url: `/editChartTemplate/listByUserAccount/${userAccount}`,
    method: 'get'
  })
}

// 新增模版
export function saveChartTemplate(parameter) {
  return axios({
    url: '/editChartTemplate/save',
    method: 'post',
    data: parameter
  })
}

// 编辑图表模版-根据templateId修改模版
export function updateChartTemplate(parameter) {
  return axios({
    url: '/editChartTemplate/updateById',
    method: 'post',
    data: parameter
  })
}

// 编辑图表模版-根据templateId删除模版
export function deleteChartTemplate(id) {
  return axios({
    url: `/editChartTemplate/deleteById/${id}`,
    method: 'delete'
  })
}


