<template>
  <div style="background:#fff;overflow: hidden;height: 100%;">

    <div class="tab-title">
        <div class="tab-head">
            <div class="active">SOR管理</div>

        </div>
    </div>
    <div class="sub-title">

      <div  @click="exportDataMethod()" style="cursor:pointer">
        <span><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 jZPaJQ svg-icon-path-icon fill" viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path d="M5 8C5 6.89543 5.89543 6 7 6H19L24 12H41C42.1046 12 43 12.8954 43 14V40C43 41.1046 42.1046 42 41 42H7C5.89543 42 5 41.1046 5 40V8Z" fill="none" stroke="rgb(84, 152, 255)" stroke-width="4" stroke-linejoin="round"></path><path d="M30 28L23.9933 34L18 28.0134" stroke="rgb(84, 152, 255)" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path><path d="M24 20V34" stroke="rgb(84, 152, 255)" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path></g></svg></span>
        <a class="tip">
          SOR导出表
        </a>
      </div>

    </div>

    <div class="wrapper">
      <div class="h1">
        基础性能管理 ( 固定定义栏)
      </div>
      <div class="statusbar">
        <span class="tip">★  数据库关联必填项</span>
        <a class="btn"><a-icon class="icon success1" type="check" /><span class="txt">满足</span></a>
        <a class="btn"><a-icon class="icon warn1" type="exclamation" /><span class="txt">待确定</span></a>
        <a class="btn"><a-icon class="icon fail1" type="close" /><span class="txt">不满足</span></a>
      </div>
      <a-table
        :columns="columns"
        :data-source="dataSource1"
        :row-key="(record) => record.id"
        :pagination="false"
        :scroll="{ y: windowHeight }"
        bordered
      >
        <template slot="batteryName">
          {{batteryName}}管理
        </template>

        <div slot="eveSor"  slot-scope="text,record">

          <input
            :value="numberHandle(text,2)"
            v-if="record.code == 'sor_size_core_diameter' ||
                        record.code == 'sor_size_core_high_un' ||
                        record.code == 'sor_size_core_high' ||
                        record.code == 'sor_performance_energy_average' ||
                        record.code == 'sor_performance_energy_min' ||
                        record.code == 'sor_performance_capacity_average' ||
                        record.code == 'sor_performance_capacity_min' ||
                        record.code == 'sor_performance_weight_average' ||
                        record.code == 'sor_performance_energy_density_average' ||
                        record.code == 'sor_performance_energy_density_min'"
            disabled

            class='tdcls'
            @change="updateData($event,record,'eveSor')"/>
          <input
            :value="text"
            v-else
            @change="updateData($event,record,'eveSor')"/>
        </div>
        <div slot="customerSor" slot-scope="text,record"><input :value="text" @change="updateData($event,record,'customerSor')"/></div>
        <template slot="checkStatus" slot-scope="text,record">

          <div class="tdcls1"  @click="isOwn == 1 && design.manCheckStatus == 0?edit(record.id):null">
            <a-select v-if="record.editable"  dropdown-class-name="dropdownClassName" style="width: 100%;outline:none;font-size: 2px;"
                      @blur="getList(false)"
                      :autoFocus="true"
                      :open="true"
                      :showArrow="false"
                      :default-value="text" @change="updateSelectData($event,record)">
              <a-select-option :value="parseInt(1)" @click="getList(false)" >
                满足
              </a-select-option>
              <a-select-option :value="parseInt(2)"  @click="getList(false)">
                不满足
              </a-select-option>
              <a-select-option :value="parseInt(3)"  @click="getList(false)">
                TBD
              </a-select-option>
              <a-select-option :value="parseInt(0)"  @click="getList(false)">
                /
              </a-select-option>

            </a-select>

            <span class="spanstatus" v-else >
              <a-icon v-if="text==1" class="success1" type="check" />
              <svg t="1669081811549" v-else-if="text==3" class="icon warn1" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3553" width="16" height="14"><path d="M450.602458 665.598073a62.463819 62.463819 0 0 0 122.879645 0L614.441984 102.399704A102.615282 102.615282 0 0 0 512.04228 0 105.256116 105.256116 0 0 0 409.642577 112.639674L450.602458 665.598073z m61.439822 153.599556a102.399704 102.399704 0 1 0 102.399704 102.399703 96.740773 96.740773 0 0 0-102.399704-102.399703z" p-id="3554" fill="#fec303"></path></svg>

              <a-icon v-else-if="text == 2" class="fail1" type="close" />
              <span v-else>/</span>
            </span>
          </div>


        </template>
        <div slot="remark" slot-scope="text,record"><input :value="text" @change="updateData($event,record,'remark')"/></div>
        <div class="divcls" slot="divcls" slot-scope="text">{{text}}</div>
        <div class="divcls div_border_right" slot="unitcls" slot-scope="text">{{text}}</div>
      </a-table>

    </div>

    <div class="wrapper">
      <div class="h1">
        关键性能管理（自定义输入栏）
      </div>

      <div class="statusbar">
        <a class="btn a-btn" @click="$refs.addForm.add(batteryId)" v-if="isOwn == 1 && (design.manCheckStatus == 0 || design.manCheckStatus == 20 || design.manCheckStatus == 80 )">新增</a>
        <a-popconfirm placement="topRight" title="确认复制？" @confirm="() => designCopy()">
          <a class="btn a-btn" v-if="isOwn == 1 && (design.manCheckStatus == 0 || design.manCheckStatus == 20 || design.manCheckStatus == 80 )">复制</a>
        </a-popconfirm>

        <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => designDelete()">
          <a class="btn a-btn" v-if="isOwn == 1 && (design.manCheckStatus == 0 || design.manCheckStatus == 20 || design.manCheckStatus == 80 )">删除</a>
        </a-popconfirm>
      </div>

      <a-table
        :scroll="{ y: windowHeight }"
        :columns="columns1"
        :data-source="dataSource2"
        :row-key="(record) => record.id"
        :pagination="false"
        :row-selection="rowSelection"
        bordered
      >
        <template slot="batteryName">
          {{batteryName}}管理
        </template>
        <div slot="customerSor" slot-scope="text,record">
          <input :value="text" @change="updateData($event,record,'customerSor')"/>

        </div>
        <div slot="projectCategory" slot-scope="text,record" :title="text"><input :value="text" @change="updateData($event,record,'projectCategory')"/></div>
        <div slot="projectName" slot-scope="text,record" :title="text"><input :value="text" @change="updateData($event,record,'projectName')"/></div>
        <div slot="unit" slot-scope="text,record"><input :value="text" @change="updateData($event,record,'unit')"/></div>
        <div slot="eveSor" slot-scope="text,record"><input :value="text" @change="updateData($event,record,'eveSor')"/></div>
        <template slot="checkStatus" slot-scope="text,record">
          <div class="divcls">

            <a-select v-if="record.editable" style="width: 100%;outline:none" size="small" :default-value="text"
                      @change="updateSelectData($event,record)">
              <a-select-option :value="parseInt(1)">
                满足
              </a-select-option>
              <a-select-option :value="parseInt(2)">
                不满足
              </a-select-option>
              <a-select-option :value="parseInt(3)">
                TBD
              </a-select-option>
              <a-select-option :value="parseInt(0)">
                /
              </a-select-option>

            </a-select>

            <span class="spanstatus" v-else @click="isOwn == 1 && design.manCheckStatus == 0?edit1(record.id):null">
              <a-icon v-if="text==1" class="success1" type="check" />
              <svg t="1669081811549" v-else-if="text==3" class="icon warn1" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3553" width="16" height="14"><path d="M450.602458 665.598073a62.463819 62.463819 0 0 0 122.879645 0L614.441984 102.399704A102.615282 102.615282 0 0 0 512.04228 0 105.256116 105.256116 0 0 0 409.642577 112.639674L450.602458 665.598073z m61.439822 153.599556a102.399704 102.399704 0 1 0 102.399704 102.399703 96.740773 96.740773 0 0 0-102.399704-102.399703z" p-id="3554" fill="#fec303"></path></svg>
              <!--<a-icon v-else-if="text==3" class="warn1" type="exclamation" />-->
              <a-icon v-else-if="text == 2" class="fail1" type="close" />
              <span v-else>/</span>
            </span>
          </div>

        </template>
        <div slot="remark" slot-scope="text,record"><input :value="text" @change="updateData($event,record,'remark')"/></div>
        <div class="divcls" slot="divcls" slot-scope="text">{{text}}</div>
        <div class="divcls div_border_right" slot="unitcls" slot-scope="text">{{text}}</div>
      </a-table>


      <add-form ref="addForm" @ok="getList(false)"/>
    </div>

  </div>
</template>
<script>

  import { list,update,copy,exportExcel} from '@/api/modular/system/batteryDesignSorManage'
  import { getBatteryDesign} from '@/api/modular/system/batterydesignManage'
  import addForm from './addForm'
  import {
        mapActions,
        mapGetters
    } from 'vuex'
    import { ALL_APPS_MENU } from '@/store/mutation-types'
    import Vue from 'vue'
  export default {


    components: {
      addForm
    },
    data () {
      return {
        checkStatus:{0:'/',1:'满足',2:'不满足',3:'TBD'},
        windowHeight: document.documentElement.clientHeight - 200,
        sor_project:0,
        isOwn:0,
        design:null,
        sor_performance:0,
        sor_size:0,
        sor_safety_test:0,
        sor_warranty:0,
        sor_hot:0,
        sor_power:0,
        sor_fast_charging:0,
        sor_life:0,
        sor_electrical:0,
        dataSource1:[],
        selectedRowKeys:[],
        selectedRow:[],

        rowSelection :{
          columnWidth:30,
          onChange: (selectedRowKeys, selectedRows) => {
            this.selectedRowKeys = selectedRowKeys
            this.selectedRow = selectedRows
          },
        },

        dataSource2:[],
        batteryName:'',
        batteryId:null,
        // 表头
        columns: [
              {
                title: '序号',
                dataIndex: 'index',
                align: 'center',
                width: 30,
                customRender: (text, record, index) => (<div class='divcls div_border_right'>{index+1}</div>)
              },
              {
                title: '项目',
                dataIndex: 'projectCategory',
                align: 'center',
                width:50,
                customRender: (text, record, index) => {
                  const obj = {
                    children: (<div class='divcls div_border_right div_width'>{text}</div>),
                    attrs: {}
                  }
                  obj.attrs.rowSpan = 0

                  if(record.code == null){
                    obj.attrs.rowSpan = 1
                  }

                  if(record.code == 'sor_project_customer'){
                    obj.attrs.rowSpan = this.sor_project
                  }
                  if(record.code == 'sor_performance_fast_charging_time'){
                    obj.attrs.rowSpan = this.sor_performance
                  }
                  if(record.code == 'sor_electrical_soc_ocv'){
                    obj.attrs.rowSpan = this.sor_electrical
                  }
                  if(record.code == 'sor_fast_charging_25'){
                    obj.attrs.rowSpan = this.sor_fast_charging
                  }
                  if(record.code == 'sor_life_45_loop'){
                    obj.attrs.rowSpan = this.sor_life
                  }
                  if(record.code == 'sor_size_core_diameter'){
                    obj.attrs.rowSpan = this.sor_size
                  }
                  if(record.code == 'sor_safety_test_overcharge'){
                    obj.attrs.rowSpan = this.sor_safety_test
                  }
                  if(record.code == 'sor_power_structural_mechanics'){
                    obj.attrs.rowSpan = this.sor_power
                  }
                  if(record.code == 'sor_warranty_eol'){
                    obj.attrs.rowSpan = this.sor_warranty
                  }
                  if(record.code == 'sor_hot_thermal_simulation_parameters'){
                    obj.attrs.rowSpan = this.sor_hot
                  }
                  return obj
                }
              }, {
                title: '内容',
                dataIndex: 'projectName',
                scopedSlots: { customRender: 'divcls' },
                width:150,
              },
              {
                title: '单位',
                dataIndex: 'unit',
                width:50,
                align: 'center',
                scopedSlots: { customRender: 'divcls' },
              },
              {
                title: '客户SOR输入',
                dataIndex: 'customerSor',
                align: 'center',
                width:80,
                scopedSlots: { customRender: 'customerSor' },
              },
              {
                title: 'EVE定义',
                dataIndex: 'eveSor',
                align: 'center',
                width:80,
                scopedSlots: { customRender: 'eveSor' },
              },

              {
                title: '状态识别',
                dataIndex: 'checkStatus',
                align: 'center',
                width:50,
                scopedSlots: { customRender: 'checkStatus' },
              }, /* {
                title: '备注',
                dataIndex: 'remark',
                align: 'center',
                width:50,
                scopedSlots: { customRender: 'remark' },
              }, */],
        columns1: [
              {
                title: '序号',
                dataIndex: 'index',
                align: 'center',
                width: 30,
                customRender: (text, record, index) => (<div class='divcls div_border_right'>{index+1}</div>)
              },
              {
                title: '项目',
                dataIndex: 'projectCategory',
                align: 'center',
                width:70,
                scopedSlots: { customRender: 'projectCategory' },
              }, {
                title: '内容',
                dataIndex: 'projectName',
                scopedSlots: { customRender: 'projectName' },
                width:150,
              },
              {
                title: '单位',
                dataIndex: 'unit',
                width:50,
                align: 'center',
                scopedSlots: { customRender: 'unit' },
              },
              {
                title: '客户SOR输入',
                dataIndex: 'customerSor',
                align: 'center',
                width:80,
                scopedSlots: { customRender: 'customerSor' },
              },
              {
                title: 'EVE定义',
                dataIndex: 'eveSor',
                align: 'center',
                width:80,
                scopedSlots: { customRender: 'eveSor' },
              },

              {
                title: '状态识别',
                dataIndex: 'checkStatus',
                align: 'center',
                width:50,
                scopedSlots: { customRender: 'checkStatus' },
              }, /* {
                title: '备注',
                dataIndex: 'remark',
                align: 'center',
                width:50,
                scopedSlots: { customRender: 'remark' },
              }, */],

      }
    },

    computed: {
            ...mapGetters(['userInfo'])
        },

    mounted() {

      this.batteryId = this.$route.query.batteryId
      this.getList(true)

    },

    created(){

    },

    methods: {
      ...mapActions(['MenuChange']),

      exportDataMethod() {

        exportExcel({batteryId:this.batteryId}).then(res => {

          const fileName = 'SOR管理导出表.xlsx';

          if(!res) return
          const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' }) // 构造一个blob对象来处理数据，并设置文件类型

          if (window.navigator.msSaveOrOpenBlob) { //兼容IE10
            navigator.msSaveBlob(blob, fileName)
          } else {
            const href = URL.createObjectURL(blob) //创建新的URL表示指定的blob对象
            const a = document.createElement('a') //创建a标签
            a.style.display = 'none'
            a.href = href // 指定下载链接
            a.download = fileName //指定下载文件名
            a.click() //触发下载
            URL.revokeObjectURL(a.href) //释放URL对象
          }


       /*

          const _res = res.data;
          let blob = new Blob([_res]);
          let downloadElement = document.createElement("a");
          //创建下载的链接
          let href = window.URL.createObjectURL(blob);
          downloadElement.href = href;
          //下载后文件名
          downloadElement.download = fileName;
          document.body.appendChild(downloadElement);
          //点击下载
          downloadElement.click();
          //下载完成移除元素
          document.body.removeChild(downloadElement);
          //释放掉blob对象
          window.URL.revokeObjectURL(href);*/

        })


      },
      switchApp() {
                const applicationData = Vue.ls.get(ALL_APPS_MENU)
                this.MenuChange(applicationData[0]).then((res) => {}).catch((err) => {
                    this.$message.error('错误提示：' + err.message, 1)
                })
            },
        gotoManager(){
                //this.switchApp()
                this.$router.push({
                    path: "/battery_design_manager",
                    query: {
                        batteryId:this.batteryId
                    },
                });
            },
      designDelete(){
        if(this.selectedRowKeys.length == 0){
          this.$message.error("请先选中要删除的数据");
        }

        for (let i = 0; i < this.selectedRowKeys.length; i++) {
          update({id:this.selectedRowKeys[i],status:1})
          this.getList(false)
        }


      },

      designCopy(){
        if(this.selectedRowKeys.length == 0){
          this.$message.error("请先选中要复制的数据");
        }


        copy(this.selectedRow).then(() => {
          this.getList(false)
        })




      },

      numberHandle(number, n) {
        if(null == number){
          return ''
        }
        n = n ? parseInt(n) : 0;
        if(n <= 0) {
          return Math.round(number);
        }
        number = Math.round(number * Math.pow(10, n)) / Math.pow(10, n); //四舍五入
        number = Number(number).toFixed(n); //补足位数
        return number;
      },
      edit1(id){
        const newData = [...this.dataSource2];
        const target = newData.find(item => id === item.id);
        this.editingKey = id;
        if (target) {
          target.editable = true;
          this.dataSource2 = newData;
        }
      },
      edit(id){
        const newData = [...this.dataSource1];
        const target = newData.find(item => id === item.id);
        this.editingKey = id;
        if (target) {
          target.editable = true;
          this.dataSource1 = newData;
        }
      },
      updateData(event,record,column){

        //修改时禁止输入

        let inputs = document.getElementsByTagName("input");
        let controlInput = [];

        for (let i = 0; i < inputs.length; i++) {
          if(!inputs[i].disabled){
            controlInput.push(inputs[i])
          }
        }

        for (let i = 0; i < controlInput.length; i++) {
          controlInput[i].disabled = true
        }


        let param = {}
        param[column] = event.target.value
        param['id'] = record.id
        update(param).then((res) => {
          this.getList(false)
          this.$nextTick(() => {
            if (res.success) {
              this.$message.success('保存成功')
            }else {
              this.$message.error(res.message)
            }
            for (let i = 0; i < controlInput.length; i++) {
              controlInput[i].disabled = false
            }
          })
        })



      },
      gotoBom(){
        //this.switchApp()
        this.$router.push({
          path: "/sys_battery_design_bom",
          query: {
            batteryId:this.batteryId
          },
        });
      },
      gotoMi(){
        //this.switchApp()
        this.$router.push({
          path: "/g_cylinder_mi_standard_manage",
          query: {
            batteryId: this.batteryId,
          },
        });
      },
      updateSelectData(e,record){

        let param = {}
          param['checkStatus'] = e
          param['id'] = record.id
          update(param).then((res) => {
            this.getList(false)
            this.$nextTick(() => {
              if (res.success) {
                this.$message.success('保存成功')
              }else {
                this.$message.error(res.message)
              }
            })
        })

      },

      getByClass(parent, cls) {
				if (parent.getElementsByClassName) {
					return Array.from(parent.getElementsByClassName(cls));
				} else {
					var res = [];
					var reg = new RegExp(' ' + cls + ' ', 'i')
					var ele = parent.getElementsByTagName('*');
					for (var i = 0; i < ele.length; i++) {
						if (reg.test(' ' + ele[i].className + ' ')) {
							res.push(ele[i]);
						}
					}
					return res;
				}
			},
      init(){
        this.$nextTick(() => {
					let items = this.getByClass(document, 'divcls')
					for (const e of items) {
						var _e = e.parentNode
            _e.classList.add('tdcls')
            if (e.classList.contains('div_border_right')) {
              _e.classList.add('td_border_right')
            }
            if (e.classList.contains('div_width')) {
              _e.classList.add('td_width')
            }
					}

          let $items = this.getByClass(document,'ant-layout')
          for (const e of $items) {
            e.setAttribute("style","min-height:initial");
          }
				})
      },
      getList(update){
        list({batteryId:this.batteryId,dataType:'confirm'}).then((res) => {

          this.dataSource1 = res.data

          this.batteryName = res.data[0].batteryName

          if(update){

            for (let i = 0; i < res.data.length; i++) {
              if(res.data[i].code.startsWith('sor_project')){
                this.sor_project++
              }
              if(res.data[i].code.startsWith('sor_performance')){
                this.sor_performance++
              }
              if(res.data[i].code.startsWith('sor_electrical')){
                this.sor_electrical++
              }
              if(res.data[i].code.startsWith('sor_fast_charging')){
                this.sor_fast_charging++
              }
              if(res.data[i].code.startsWith('sor_life')){
                this.sor_life++
              }if(res.data[i].code.startsWith('sor_size')){
                this.sor_size++
              }
              if(res.data[i].code.startsWith('sor_safety_test')){
                this.sor_safety_test++
              }
              if(res.data[i].code.startsWith('sor_warranty')){
                this.sor_warranty++
              }if(res.data[i].code.startsWith('sor_hot')){
                this.sor_hot++
              }
              if(res.data[i].code.startsWith('sor_power')){
                this.sor_power++
              }
            }


          }

           this.init()

        }).then(() => {

          list({batteryId:this.batteryId,dataType:'add'}).then((res) => {
            this.dataSource2 = res.data
            this.init()
          }).then(() => {
            getBatteryDesign({inBatteryId:this.batteryId,type:'sor'}).then(res =>{

              this.isOwn = res.data.isOwn
              this.design = res.data
              if(res.data.isOwn == 0 || this.design.manCheckStatus == 10 || this.design.manCheckStatus == 70){
                let inputs = document.getElementsByTagName("input");
                let controlInput = [];

                for (let i = 0; i < inputs.length; i++) {
                  if(!inputs[i].disabled){
                    controlInput.push(inputs[i])
                  }
                }

                for (let i = 0; i < controlInput.length; i++) {
                  controlInput[i].disabled = true
                }
              }
            })
          })

        })/* .then(() => this.init()) */
      }
    }
  }
</script>
<style lang="less" scoped="">
  .h1{
    font-size: 18px;
    /* margin-bottom: 5px; */
    text-align: center;
    background: #0049b0;
    color: #fff;
  }
  /deep/.ant-table-wrapper{
    background: #fff;
  }
  /deep/.table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
  /deep/.ant-table-thead > tr > th{
    padding:2px 0 2px 4px;
    border-bottom: 1px solid #d2d4d7;
    border-right: 0;
    font-size: 13px;
    /* font-weight: bold; */
    background: rgba(0, 73, 176, 0.7);
    color: #fff;
  }
  /deep/.ant-table-tbody > tr > td {
    padding: 0;
    margin: 0;
    border-bottom: 1px solid #d2d4d7;
    border-right: 1px solid #d2d4d7;
    font-size: 12px;
  }

  /deep/.tdcls{
    color: #000;
    background: rgb(239, 239, 239);
    padding: 0px 0 0px 4px !important;
  }

  /deep/.td_border_right{
    border-right: 1px solid #d2d4d7;
  }
  /deep/.td_width{
    width: 50px;
    padding: 0 !important;
  }
  .div_width{
    width: 30px;
    margin: auto;
  }
  input{
    width: 100%;
    height: 25px;
    margin: 0;
    border: 0;
    outline: none;
    text-align: center;
  }
  .wrapper{
    width: 50%;
    background: #fff;
    padding: 0 10px;
    overflow: hidden;
    float: left;
  }
  /deep/.ant-select-selection{
    border: none;
  }
  .spanstatus{
    display: block;
    padding: 1px 4px;
    border-radius: 2px;
    height:25px;
  }
  .success1{
    //background: #66b72a;
    color: #66b72a;
    margin-top: 5px;
  }
  .warn1 {
    //background: #fec303;
    color: #fec303;
    margin-top: 5px;
  }
  .fail1{
    //background: #e05328;
    color: #e05328;
    margin-top: 5px;
  }
  .statusbar{
    overflow: hidden;
    height: 32px;
    line-height: 32px;
    font-size: 12px;
    text-align: right;
  }
  .statusbar::after{
    content: ' ';
    display: block;
    height: 0;
    clear: both;
  }
  .statusbar .icon{
    margin-right: 3px;
    font-weight: bold;
  }
  .statusbar .btn{
    /* float: right; */
    margin-left: 20px;
  }
  .statusbar .a-btn{
    border-radius: 2px;
    background: #0049b0;
    color: #fff;
    padding: 2px 15px;
    letter-spacing: 2px;
  }
  .statusbar .txt{
    color: #000;
    font-weight: bold;
  }
  .statusbar .tip{
    float: left;
    /* margin-left: 120px; */
  }
   .tab-title{
        padding: 0 10px;
    }
  .anticon svg {
    font-size: 13px;
  }

    div.tab-head{
        border-bottom: 1px solid #d3d2d2c9;
    }

    div.tab-head div{
        display: inline-block;
        margin-right: 50px;
        font-weight: 700;
        font-size: 18px;
        color: rgb(128, 128, 128);
        margin-bottom: -6px;
        cursor: pointer;
    }

    div.tab-head div.active{
        font-size: 24px;
        color: rgba(0,73,176,1);
        margin-bottom: -4px;
        cursor: text;
    }

    div.sub-title{
      overflow: hidden;
      padding: 6px 10px;
    }

    div.sub-title::after{
      content: ' ';
      display: block;
      height: 0;
      clear: both;
    }

    div.sub-title span{
      display: block;
      float: left;
      margin-right: 6px;
    }
    div.sub-title span:first-child{
      margin-top: 1px;
    }
    div.sub-title .tip{
      font-family: SourceHanSansSC;
      font-weight: 400;
      font-size: 15px;
      color: rgba(0,101,255,0.67);
    }
    /deep/.ant-table-thead > tr > th:first-child{
      padding: 0;
    }
    /deep/.ant-table-tbody > tr > td:first-child{
      background: rgb(239, 239, 239);
    }
    /deep/.ant-table-tbody > tr.ant-table-row-selected td:first-child{
      background: #fafafa;
    }

  /deep/.tdcls1{
    color: #000;
    background: rgb(239, 239, 239);
  }
    /* /deep/td {
      height: 29px;
      vertical-align: middle;
    }

    /deep/td > div{
      height: 100%;
      margin: 0;
      padding: 0;
    } */
  /deep/.ant-select-selection-selected-value{
    text-align: center;
  }
  /deep/.ant-select-open .ant-select-selection{
    border: none;
    box-shadow:none;
  }
  /deep/ .ant-select-selection__rendered{
    margin: auto;
    line-height:initial;
    font-size: 12px;
    text-align: center;
  }
  /deep/ .ant-select-selection--single{
    height: auto;
  }
</style>

<style lang='less'>
  .dropdownClassName{
    .ant-select-dropdown-menu-item {
      padding: 2px 8px !important;
      font-size: 12px !important;
      text-align: left !important;
      font-weight: 100;
    }
  }
</style>
