<template>
<div>
  <div class="dashboard" ref="dashboard">
    <div class="col1">
      <a-spin :spinning="assessspinning">
        <div class="item" style="position:relative" >
          <div class="date"><a-range-picker
            size='small'
            :placeholder="['开始月份', '结束月份']"
            v-model="value"
            :mode="modeType"
            format="YYYY-MM"
            @panelChange="handlePanelChange"
            @openChange="handleOpenChange"
            :open="monthPickShow"
          >
            <a-icon slot="suffixIcon" type="calendar" style="color:#d9d9d9" />
          </a-range-picker>
          </div>
          <div class="head" style="border-color:#fff"><div class="left"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 iKfgJk svg-icon-path-icon fill" viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path fill-rule="evenodd" clip-rule="evenodd" d="M19 10C19 12.2091 17.2091 14 15 14C12.7909 14 11 12.2091 11 10C11 7.79086 12.7909 6 15 6C17.2091 6 19 7.79086 19 10ZM15 28C17.2091 28 19 26.2091 19 24C19 21.7909 17.2091 20 15 20C12.7909 20 11 21.7909 11 24C11 26.2091 12.7909 28 15 28ZM15 42C17.2091 42 19 40.2091 19 38C19 35.7909 17.2091 34 15 34C12.7909 34 11 35.7909 11 38C11 40.2091 12.7909 42 15 42Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M37 10C37 12.2091 35.2091 14 33 14C30.7909 14 29 12.2091 29 10C29 7.79086 30.7909 6 33 6C35.2091 6 37 7.79086 37 10ZM33 28C35.2091 28 37 26.2091 37 24C37 21.7909 35.2091 20 33 20C30.7909 20 29 21.7909 29 24C29 26.2091 30.7909 28 33 28ZM33 42C35.2091 42 37 40.2091 37 38C37 35.7909 35.2091 34 33 34C30.7909 34 29 35.7909 29 38C29 40.2091 30.7909 42 33 42Z" fill="#333"></path></g></svg><span>技术课题立项通过率</span></div></div>
          <div class="pie_chart" >
            <div style="width:100%;height:100%" ref="pie_assess"></div>
            <!-- <div style="font-size:12px;font-weight:bold;text-align:center">2023年02月通过率</div> -->
          </div>
          <div class='chart_table assess' :style="{height:(clientHeight/2-70) + 'px'}" ref="assess">

          </div>
        </div>
      </a-spin>
      <a-spin :spinning="gainspinning">
        <div class="item">
          <div class="head"><div class="left"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 iKfgJk svg-icon-path-icon fill" viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path fill-rule="evenodd" clip-rule="evenodd" d="M19 10C19 12.2091 17.2091 14 15 14C12.7909 14 11 12.2091 11 10C11 7.79086 12.7909 6 15 6C17.2091 6 19 7.79086 19 10ZM15 28C17.2091 28 19 26.2091 19 24C19 21.7909 17.2091 20 15 20C12.7909 20 11 21.7909 11 24C11 26.2091 12.7909 28 15 28ZM15 42C17.2091 42 19 40.2091 19 38C19 35.7909 17.2091 34 15 34C12.7909 34 11 35.7909 11 38C11 40.2091 12.7909 42 15 42Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M37 10C37 12.2091 35.2091 14 33 14C30.7909 14 29 12.2091 29 10C29 7.79086 30.7909 6 33 6C35.2091 6 37 7.79086 37 10ZM33 28C35.2091 28 37 26.2091 37 24C37 21.7909 35.2091 20 33 20C30.7909 20 29 21.7909 29 24C29 26.2091 30.7909 28 33 28ZM33 42C35.2091 42 37 40.2091 37 38C37 35.7909 35.2091 34 33 34C30.7909 34 29 35.7909 29 38C29 40.2091 30.7909 42 33 42Z" fill="#333"></path></g></svg><span>成果转化</span></div><span class="right">成果转化率均值:{{avgResult}}%</span></div>
          <div class='chart_table gain' :style="{height:(clientHeight/2-70) + 'px'}" ref="gain"></div>
        </div>
      </a-spin>

    </div>
    <div class="col2">
      <a-spin :spinning="classicspinning">
      <div class="item" style="position:relative" >
        <div class="head"><div class="left"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 iKfgJk svg-icon-path-icon fill" viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path fill-rule="evenodd" clip-rule="evenodd" d="M19 10C19 12.2091 17.2091 14 15 14C12.7909 14 11 12.2091 11 10C11 7.79086 12.7909 6 15 6C17.2091 6 19 7.79086 19 10ZM15 28C17.2091 28 19 26.2091 19 24C19 21.7909 17.2091 20 15 20C12.7909 20 11 21.7909 11 24C11 26.2091 12.7909 28 15 28ZM15 42C17.2091 42 19 40.2091 19 38C19 35.7909 17.2091 34 15 34C12.7909 34 11 35.7909 11 38C11 40.2091 12.7909 42 15 42Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M37 10C37 12.2091 35.2091 14 33 14C30.7909 14 29 12.2091 29 10C29 7.79086 30.7909 6 33 6C35.2091 6 37 7.79086 37 10ZM33 28C35.2091 28 37 26.2091 37 24C37 21.7909 35.2091 20 33 20C30.7909 20 29 21.7909 29 24C29 26.2091 30.7909 28 33 28ZM33 42C35.2091 42 37 40.2091 37 38C37 35.7909 35.2091 34 33 34C30.7909 34 29 35.7909 29 38C29 40.2091 30.7909 42 33 42Z" fill="#333"></path></g></svg><span>技术课题类别</span></div></div>
        <div class='chart_table classic' :style="{height:(clientHeight/2-70) + 'px'}" ref="classic"></div>
      </div>
      </a-spin>
      <a-spin :spinning="capitaspinning">
      <div class="item" >
        <div class="head"><div class="left"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 iKfgJk svg-icon-path-icon fill" viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path fill-rule="evenodd" clip-rule="evenodd" d="M19 10C19 12.2091 17.2091 14 15 14C12.7909 14 11 12.2091 11 10C11 7.79086 12.7909 6 15 6C17.2091 6 19 7.79086 19 10ZM15 28C17.2091 28 19 26.2091 19 24C19 21.7909 17.2091 20 15 20C12.7909 20 11 21.7909 11 24C11 26.2091 12.7909 28 15 28ZM15 42C17.2091 42 19 40.2091 19 38C19 35.7909 17.2091 34 15 34C12.7909 34 11 35.7909 11 38C11 40.2091 12.7909 42 15 42Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M37 10C37 12.2091 35.2091 14 33 14C30.7909 14 29 12.2091 29 10C29 7.79086 30.7909 6 33 6C35.2091 6 37 7.79086 37 10ZM33 28C35.2091 28 37 26.2091 37 24C37 21.7909 35.2091 20 33 20C30.7909 20 29 21.7909 29 24C29 26.2091 30.7909 28 33 28ZM33 42C35.2091 42 37 40.2091 37 38C37 35.7909 35.2091 34 33 34C30.7909 34 29 35.7909 29 38C29 40.2091 30.7909 42 33 42Z" fill="#333"></path></g></svg><span>人均技术课题数</span></div><span class="right">人均技术课题数:{{avgCount}}</span></div>
        <div class='chart_table capita' :style="{height:(clientHeight/2-70) + 'px'}" ref="capita"></div>
      </div>
      </a-spin>
    </div>
    <div class="col3">

      <a-spin :spinning="topicLevelspinning">
      <div class="item" style="position:relative" >
        <div class="head"><div class="left"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 iKfgJk svg-icon-path-icon fill" viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path fill-rule="evenodd" clip-rule="evenodd" d="M19 10C19 12.2091 17.2091 14 15 14C12.7909 14 11 12.2091 11 10C11 7.79086 12.7909 6 15 6C17.2091 6 19 7.79086 19 10ZM15 28C17.2091 28 19 26.2091 19 24C19 21.7909 17.2091 20 15 20C12.7909 20 11 21.7909 11 24C11 26.2091 12.7909 28 15 28ZM15 42C17.2091 42 19 40.2091 19 38C19 35.7909 17.2091 34 15 34C12.7909 34 11 35.7909 11 38C11 40.2091 12.7909 42 15 42Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M37 10C37 12.2091 35.2091 14 33 14C30.7909 14 29 12.2091 29 10C29 7.79086 30.7909 6 33 6C35.2091 6 37 7.79086 37 10ZM33 28C35.2091 28 37 26.2091 37 24C37 21.7909 35.2091 20 33 20C30.7909 20 29 21.7909 29 24C29 26.2091 30.7909 28 33 28ZM33 42C35.2091 42 37 40.2091 37 38C37 35.7909 35.2091 34 33 34C30.7909 34 29 35.7909 29 38C29 40.2091 30.7909 42 33 42Z" fill="#333"></path></g></svg><span>技术课题等级</span></div></div>
        <div class='chart_table topicLevel' :style="{height:(clientHeight/2-70) + 'px'}"  ref="topicLevel">
        </div>
      </div>
      </a-spin>
        <a-spin :spinning="delayspinning">
      <div class="item" style="position:relative">
        <div class="legend">
          <div class="legend_btns">
            <span class="head_span" @click="onLegendClick('1')">轻度延期</span>
            <span @click="onLegendClick('1')">课题≥1wk 平台≥2wk</span>
            <span class="head_span" @click="onLegendClick('2')">严重延期</span>
            <span @click="onLegendClick('2')">课题≥4wk 平台≥8wk</span>
          </div>
        </div>
        <div class="head"><div class="left"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 iKfgJk svg-icon-path-icon fill" viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path fill-rule="evenodd" clip-rule="evenodd" d="M19 10C19 12.2091 17.2091 14 15 14C12.7909 14 11 12.2091 11 10C11 7.79086 12.7909 6 15 6C17.2091 6 19 7.79086 19 10ZM15 28C17.2091 28 19 26.2091 19 24C19 21.7909 17.2091 20 15 20C12.7909 20 11 21.7909 11 24C11 26.2091 12.7909 28 15 28ZM15 42C17.2091 42 19 40.2091 19 38C19 35.7909 17.2091 34 15 34C12.7909 34 11 35.7909 11 38C11 40.2091 12.7909 42 15 42Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M37 10C37 12.2091 35.2091 14 33 14C30.7909 14 29 12.2091 29 10C29 7.79086 30.7909 6 33 6C35.2091 6 37 7.79086 37 10ZM33 28C35.2091 28 37 26.2091 37 24C37 21.7909 35.2091 20 33 20C30.7909 20 29 21.7909 29 24C29 26.2091 30.7909 28 33 28ZM33 42C35.2091 42 37 40.2091 37 38C37 35.7909 35.2091 34 33 34C30.7909 34 29 35.7909 29 38C29 40.2091 30.7909 42 33 42Z" fill="#333"></path></g></svg><span>技术课题延期率</span></div><span class="right">延期率均值:{{avgDelay}}</span></div>
        <div class='chart_table delay' :style="{height:(clientHeight/2-70) + 'px'}" ref="delay">
        </div>
      </div>
        </a-spin>

    </div>
  </div>
</div>
</template>

<script>
import { ALL_APPS_MENU } from '@/store/mutation-types'
    import Vue from 'vue'
  import {
    getChartProjects,
    getChartPassTopics
  } from "@/api/modular/system/topic"
  import { mapActions, mapGetters } from 'vuex'
  import moment from 'moment';
import topiclistVue from './topiclist.vue';
  export default {
    name: 'topic',
    data() {
      return {
        monthPickShow: false,
        assessspinning:true,
        gainspinning:true,
        classicspinning:true,
        capitaspinning:true,
        topicLevelspinning:true,
        delayspinning:true,
        clientHeight:document.documentElement.clientHeight,
        delayChart:null,
        topicClassic:{},
        topicLevelsA:{},
        topicLevelsS:{},
        topicLevelsZ:{},
        topicLevelsK:{},
        topicLevelsAvg:{},
        topicLightDelay:{},
        topicSeriesDelay:{},
        topicUnder:{},
        avgCount:0,
        avgDelay:0,
        avgResult:0,

        value: [moment().subtract(1, 'year').startOf('year'),moment()],

        topicsPass:{},
        topicsSum:{},
        topicCurMonths:{},

        modeType:['month', 'month'],

        topicResult:{},
        topicConclue:{},
        topicPasses:{}
      }
    },
    methods: {
      moment,
      handlePanelChange(value, mode) {
        if (this.value[1] && this.value[1]._d != value[1]._d) {
          this.value = value
          this.monthPickShow = false;
          this.callChartTopicsPass()
          //console.log(moment(this.value[0]._d).format('YYYY-MM'))
        }
        this.value = value
      },
      handleOpenChange(status) {
        if(status){
          this.monthPickShow = true;
        }else{
          this.monthPickShow = false
        }
      },
      ...mapActions(['MenuChange']),
      switchApp() {
          const applicationData = Vue.ls.get(ALL_APPS_MENU)
          this.MenuChange(applicationData[0]).then((res) => {
          }).catch((err) => {
            this.$message.error('应用切换异常')
          })
      },
      
      getScale(width = 1920,height = 1080){
        let ww = window.innerWidth / width
        let wh = window.innerHeight /height
        return ww < wh ? ww : wh
      },
      resize(){
        this.initChart()
      },
      initChart(){
        setTimeout(() => {
          this.initTopicLevel()
          this.initTopicDelay()
          this.initAssess()
          this.initGain()
          this.initCapita()
          this.initTopicClassic()
        }, 500);
      },

      initAssess() {
        let chart = this.echarts.init(this.$refs.assess)
        let dataAxis = []
        let dataZ = []
        let _datas = []
        let passCount = 0
        let sumCount = 0
        let percent = []

        for (const key in this.topicsPass) {
          dataAxis.push(key)
          _datas.push(this.topicsPass[key])
          passCount += this.topicsPass[key]
        }
        for (const key in this.topicsSum) {
          dataZ.push(this.topicsSum[key])
          sumCount += this.topicsSum[key]
        }

        //倒序
        dataAxis.reverse()
        _datas.reverse()
        dataZ.reverse()

        for (let i = 0; i < _datas.length; i++) {
          percent.push({value:((_datas[i]/dataZ[i])*100).toFixed(1),num:_datas[i]})
        }

        let avgPass = passCount/sumCount
        chart.clear()
        const options = {
            color:['#fff'],
            legend: {
              left: '-6%',
              data: [0==sumCount?'':`平均通过率:${(avgPass*100).toFixed(1)}%`],
              top: '7%',
              itemHeight:4
            },
            xAxis: [
              {
                type: 'value',
                splitLine:{show: false},
                axisLabel: {
                  formatter: (value, index) => {
                    return value+"%";
                  }
                }
              }
            ],
            yAxis: [
              {
                type: 'category',
                axisLabel: { interval: 0 , /* rotate: 30, */textStyle:{
                  fontSize:'10'
                }},
                data: dataAxis,//['研发规划','行政管理','基础开发','工程研究','方形电池','新型电池','动力圆柱','V型电池','混动电池'],
                splitLine: { show: false },
                
              }
            ],
            grid: {
              left: '18%',
              right: '18%',
              top: '20%',
              bottom:'10%',
            },
            series: [
            {
              name: 0==sumCount ?'':`平均通过率:${(avgPass*100).toFixed(1)}%`,
              type: 'bar',
              data: percent,
              stack: '1',
              itemStyle:{
                normal:{
                  color:function(params) {
                    if ( (params.value) >= avgPass*100) {
                      return '#70ad47'
                    }
                    return '#70ad47'
                  }
                }
              },
              label: {
                show: true,
                color: '#000',
                fontSize: '10',
                position:'right',
                formatter:function(params){
                  return 0==sumCount?'':'('+params.data.value+'%'+','+params.data.num+')'
                }
              },

            },

            {
              name: '统计',
              type: 'bar',
              stack: '1',
              emphasis: {
                focus: 'series'
              },
              data: [0, 0, 0, 0, 0, 0, 0, 0, 0],
            }
            
          ]
        };
        var series = options.series

        let data = []

        function getSum (params) {

          let dataValue = 0
          for (let i = 0; i < series.length; i++) {
            dataValue += series[i].data[params.dataIndex].num
          }
          let count = dataZ[params.dataIndex] == 0 ? 0 : ((dataValue/ dataZ[params.dataIndex])*100).toFixed(1)

          return 0==sumCount?'':'('+count+'%'+','+dataValue+')'
        }

        //series[series.length - 1].label.formatter = getSum

        chart.setOption(options)
        chart.resize()
        this.assessspinning = false

        let piechart = this.echarts.init(this.$refs.pie_assess)
        let datas = []// [{name:'待评审',value:2},{name:'通过',value:70},{name:'不通过',value:50}]


        for (const key in this.topicCurMonths) {
          datas.push({
            name:key,
            value:this.topicCurMonths[key]
          })
        }
        piechart.clear()
        const pie_options = {
          tooltip: {
            position: function (point, params, dom, rect, size) {
                return [point[0]-120 , '20%']  //返回x、y（横向、纵向）两个点的位置
            },
            formatter: function (val) {
                    console.log(val)
                    let res=`<div>
                        <span>${val.name}</span>
                        <div>${val.marker} 占比 : ${val.percent}%</div>
                        <div>${val.marker} 数量 : ${val.value}</div>
                    </div>`
                    return res
            }
          },
          color: ['#70ad47','#d95040','#cacaca'],
          grid: {
              left: 0,
              right: 0,
              top: 0,
              bottom:0,
          },
          series:[
            {
                type: 'pie',
                minAngle:10,
                z: 800,
                radius: ['50%', '80%'],
                center: ['50%', '50%'],
                itemStyle: {
                  borderRadius: 0,
                  borderColor: '#fff',
                  borderWidth: 2
                },
                label: {
                  show: true,
                  position: 'center', //将百分比显示在饼图内部
                  color:'#000',
                  formatter: `${moment().year()}-${(moment().month()+1) < 10 ? '0' + (moment().month()+1) : (moment().month()+1)}\n通过率`,
                  fontSize: '10',
                  lineHeight: 10,
                  fontWeight:'bold'
                },
                labelLine: {
                  show: false
                },
                data: datas
              },
              {
                type: 'pie',
                minAngle:10,
                z: 800,
                center: ['50%', '50%'],
                radius: ['50%', '80%'],
                itemStyle: {
                  borderRadius: 0,
                  borderColor: '#fff',
                  borderWidth: 2
                },
                label: {
                  show: true,
                  position: 'inner',
                  formatter: function(data){
                    return `${data.percent.toFixed(1)}%`
                  },
                  fontSize: '9',
                  color:'#fff'
                },
                labelLine: {
                  show: true
                },
                data: datas
            },
          ]
        }
        piechart.setOption(pie_options)
        piechart.resize()
      },
      initGain() {
        let chart = this.echarts.init(this.$refs.gain)
        chart.clear()

        let _source = []
        _source.push(['product', '成果', '结题项目','转化率'])

        let passCount = 0
        let resultCount = 0

        for (const key in this.topicResult) {
          let index = key.indexOf('研究所') > -1 ? key.indexOf('研究所') : key.indexOf('中心')
          let tmpsuffx = key.indexOf('研究所') > -1 ? '研究所' : '中心' //key.substring(0,index)+`\n`+tmpsuffx
          let _tmp = [key,this.topicResult[key],this.topicConclue[key],((this.topicResult[key]/this.topicPasses[key])*100).toFixed(1)]
          _source.push(_tmp)
          passCount += this.topicPasses[key]
          resultCount += this.topicResult[key]
        }

        this.avgResult = ((resultCount/passCount)*100).toFixed(1)

        const options = {
                grid:{
                  show:false,
                  top:'18%',
                  bottom:'18%',
                  right:'13%'
                },
                color:['#f7e771','#89d77f'],
                legend: {
                    show: true,
                },
                tooltip: {},
                dataset: {
                    source:_source /* [
                        ['product', '成果', '结题项目','转化率'],
                        ['混动电池',1, 10,1],
                        ['v型圆柱', 5, 38,17],
                        ['动力圆柱', 3, 44,56],
                        ['新型电池',1, 12,34],
                        ['方形电池',10, 48],
                        ['工程研究所', 4, 18],
                        ['基础研发中心', 1, 11],
                        ['行政管理中心', 0, 3],
                        ['研发规划中心', 9, 5],
                        //_source
                    ] */
                },
                xAxis: { 
                  type: 'category',
                  axisLabel: { showMinLabel:true,interval: 0,  rotate: 15 ,textStyle:{
                  fontSize:'9',
                }},
                },
                yAxis: [{
                  type: 'value',
                  splitLine:{show: false}
                },
                  {
                    type: 'value',
                    splitLine:{show: false},
                    axisLabel: {
                      formatter: (value, index) => {
                        return value+"%";
                      }
                    }
                  }],
                series: [
                    {
                        type: 'bar',
                        itemStyle: {
                            normal: {
                                color: "#5b9bd5", //柱状颜色
                                label: {
                                show: false, //开启数值显示
                                position: "top", //在上方显示
                                textStyle: {
                                    //数值样式
                                    color: "#000",
                                    fontSize: 8,
                                },
                                },
                            },
                        },
                    }, 
                    {
                        type: 'bar',
                        itemStyle: {
                            normal: {
                                color: "#70ad47",
                                label: {
                                show: false,
                                position: "top",
                                textStyle: {
                                    color: "#000",
                                    fontSize: 12,
                                },
                                },
                            },
                        },
                    },
                    {
                        type: 'line',
                        yAxisIndex: 1,
                        itemStyle: {
                            normal: {
                                color: "#acd1f7",
                                label: {
                                show: true,
                                position: "top",
                                  formatter: (value, index) => {
                                    return value.data[3]+"%";
                                  },
                                textStyle: {
                                    color: "#000",
                                    fontSize: '10',
                                },
                                },
                            },
                        },
                    }
                ]
        };
        chart.setOption(options)
        chart.resize()
        this.gainspinning = false
      },
      
      initCapita() {
        let chart = this.echarts.init(this.$refs.capita)
        chart.clear()
        let dataAxis = []
        let datas = []
        let people_sum = 0
        let project_sum = 0
        for (const key in this.topicLevelsZ) {
          //let _i = key.indexOf('研究所') > -1 ? key.indexOf('研究所') : key.indexOf('中心')
          //let tmpsuffx = key.indexOf('研究所') > -1 ? '研究所' : '中心'
          dataAxis.push(key)//key.substring(0,_i)+`\n`+tmpsuffx

          if (parseInt(this.topicLevelsAvg[key]) == 0) {
            datas.push(0)
          }else{
            datas.push((this.topicLevelsZ[key]/this.topicLevelsAvg[key]).toFixed(1))
          }
          people_sum += this.topicLevelsAvg[key]
          project_sum += this.topicLevelsZ[key]
        }
        this.avgCount = (project_sum/people_sum).toFixed(1)

        let topicLevel = this.topicLevelsAvg
        let topicpass = this.topicLevelsZ

        const options = {
          grid:{
            show:false,
            top:'18%',
            bottom:'18%',

          },
           tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },formatter: function (params) {
              
              return (
                params[0].name +
                
                `<br/><span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:#4472c4;"></span>` +
                 "部门人数："+topicLevel[params[0].name]
                 +`<br/><span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:#4472c4;"></span>`
                 +'立项通过数：'+topicpass[params[0].name]
                 
                 );
            },
          },
            legend: {
              /* itemWidth: 8,
              itemHeight: 8, */
              show:true,
              data:['人均技术课题数']
            },
            xAxis: {
                type: 'category',
                axisLabel: { interval: 0, rotate: 15,textStyle:{
                  fontSize:'9'
                } },
                data: dataAxis,
                
            },
            yAxis: {
                type: 'value',
                splitLine:{show: false}
            },
            series: [
                {   
                    name:'人均技术课题数',
                    data: datas,
                    type: 'bar',
                    itemStyle: {
                            normal: {
                                color: "#4472c4",
                                label: {
                                    show: true, //开启数值显示
                                    position: "top", //在上方显示
                                    textStyle: {
                                        //数值样式
                                        color: "#4472c4",
                                        fontSize: '12',
                                    },
                                },
                            },
                    },
                }
            ]
        }
        chart.setOption(options)
        chart.resize()
        this.capitaspinning = false
      },

      
      initTopicClassic() {
        let chart = this.echarts.init(this.$refs.classic)
        chart.off("click")
        let datas = []
        let keys = ['成本类', '质保类' ,'功能类', '质量改善类', '标准化类', '知识产权类']
        /* for (const key in this.topicClassic) {
          datas.push({
            name:key,
            value:parseInt(this.topicClassic[key])
          })
        } */
        for (const key of keys) {
          datas.push({
            name:key,
            value:parseInt(this.topicClassic[key])
          })
        }
        let sum = datas.reduce((sum, e) => sum + Number(e.value || 0), 0)

        chart.clear()
        const options = {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            show: true,
            itemWidth: 8,
            itemHeight: 8,
          },
          // 质保类 成本类 功能类 标准化类 知识产权类  质量改善类
          // 成本类 质保类 功能类 质量改善类 标准化类 知识产权类
          color: ['#68bbc4', '#5087ec',  '#58a55c', '#4472c4', '#f2bd42','#a5a5a5'],
          grid: {
            left: '3%',
            right: '4%',
            top: '40%',
            bottom:'4%',
          },
          series: [{
              type: 'pie',
              minAngle:10,
              radius: ['50%', '80%'],
              center: ['50%', '55%'],
              itemStyle: {
                borderRadius: 0,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: true,
                position: 'center', //将百分比显示在饼图内部
                color:'#000',
                formatter: `立项通过\n${sum}`,
                fontSize: '20',
                lineHeight: 30,
                fontWeight:'bold'
              },
              labelLine: {
                show: false
              },
              data: datas
            },{
              type: 'pie',
              minAngle:10,
              radius: ['50%', '80%'],
              center: ['50%', '55%'],
              itemStyle: {
                borderRadius: 0,
                borderColor: '#fff',
                borderWidth: 2
              },
             label: {
                show: false,
                formatter: function(data){
                  if (data.percent<1) {
                    return `${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
                  }else{
                    return ''
                  }
                  
                }
                ,
              },
              tooltip:{
                formatter: function(data){
                  return  `<span style="display:inline-block;margin-right:6px;border-radius:10px;width:10px;height:10px;background-color:${data.color};"></span>
                    ${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
                }
              },
              labelLine: {
                show: true
              },
              data: datas
            },{
              type: 'pie',
              minAngle:10,
              radius: ['50%', '80%'],
              center: ['50%', '55%'],
              itemStyle: {
                borderRadius: 0,
                borderColor: '#fff',
                borderWidth: 2
              },
             label: {
                show: false,
                formatter: function(data){
                  return `${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
                }
                ,
              },
              tooltip:{
                formatter: function(data){
                  return  `<span style="display:inline-block;margin-right:6px;border-radius:10px;width:10px;height:10px;background-color:${data.color};"></span>
                    ${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
                }
              },
              labelLine: {
                show: false
              },
              data: datas
            },
          ]
        }
        chart.setOption(options)
        chart.on('click', function(params) {
          
        });
        chart.resize()
        this.classicspinning = false
      },

      callChartProjects() {
        getChartProjects({type:1}).then((res) => {
          if (res.success) {
            this.topicClassic = res.data.cateCount ? res.data.cateCount : {}
            this.topicLevelsA = res.data.A ? res.data.A : {}
            this.topicLevelsS = res.data.S ? res.data.S : {}
            this.topicLevelsZ = res.data.Z ? res.data.Z : {}
            this.topicLevelsK = res.data.K ? res.data.K : {}
            this.topicLevelsAvg = res.data.avg ? res.data.avg : {}
            this.topicLightDelay = res.data.lightdelay ? res.data.lightdelay:{}
            this.topicSeriesDelay = res.data.seriesDelay ? res.data.seriesDelay : {}
            this.topicUnder = res.data.under ? res.data.under : {}

            this.topicResult = res.data.result ? res.data.result : {}
            this.topicConclue = res.data.conclue ? res.data.conclue : {}
            this.topicPasses = res.data.pass ? res.data.pass : {}

            this.$nextTick(()=>{
              this.initTopicClassic()
              this.initTopicLevel()
              this.initCapita()
              this.initTopicDelay()
              this.initGain()
            })
          } else {
            this.$message.error('错误提示：' + res.message,1)
          }

          /*setTimeout(() => {
            getChartProjects({type:0}).then((res) => {
              if (res.success) {
                this.topicClassic = res.data.cateCount ? res.data.cateCount : {}
                this.topicLevelsA = res.data.A ? res.data.A : {}
                this.topicLevelsS = res.data.S ? res.data.S : {}
                this.topicLevelsZ = res.data.Z ? res.data.Z : {}
                this.topicLevelsAvg = res.data.avg ? res.data.avg : {}
                this.topicLightDelay = res.data.lightdelay ? res.data.lightdelay:{}
                this.topicSeriesDelay = res.data.seriesDelay ? res.data.seriesDelay : {}
                this.topicUnder = res.data.under ? res.data.under : {}

                this.topicResult = res.data.result ? res.data.result : {}
                this.topicConclue = res.data.conclue ? res.data.conclue : {}
                this.topicPasses = res.data.pass ? res.data.pass : {}

                this.$nextTick(()=>{
                  this.initTopicClassic()
                  this.initTopicLevel()
                  this.initCapita()
                  this.initTopicDelay()
                  this.initGain()
                })
              } else {
                this.$message.error('错误提示：' + res.message,1)
              }})

          }, 500)*/

        })
        .catch((err) => {
          this.$message.error('错误提示：' + err.message,1)
        });
      },

      callChartTopicsPass() {
        getChartPassTopics({
          startDate:moment(this.value[0]._d).format('YYYY-MM'),
          endDate:moment(this.value[1]._d).format('YYYY-MM'),
        }).then((res) => {
          if (res.success) {
            this.topicsPass = res.data.pass ? res.data.pass : {}
            this.topicsSum = res.data.sum ? res.data.sum :{}
            this.topicCurMonths = res.data.cur ? res.data.cur : {}
            this.$nextTick(()=>{
              this.initAssess()
            })
          } else {
            this.$message.error('错误提示：' + res.message,1)
          }
        })
        .catch((err) => {
          this.$message.error('错误提示：' + err.message,1)
        });
      },

      initTopicLevel() {
        let chart = this.echarts.init(this.$refs.topicLevel)
        chart.clear()
        let dataA = []
        let dataS = []
        let dataZ = []
        let dataAxis = []

        let sumA = 0
        let sumS = 0
        let sumZ = 0
        for (const key in this.topicLevelsA) {
          dataA.push(-this.topicLevelsA[key]*4)
          dataAxis.push(key)
          sumA += this.topicLevelsA[key]
        }
        for (const key in this.topicLevelsS) {
          dataS.push(-this.topicLevelsS[key]*4)
          sumS += this.topicLevelsS[key]
        }
        for (const key in this.topicLevelsK) {
          dataZ.push(this.topicLevelsK[key])
          sumZ += this.topicLevelsK[key]
        }
        
        dataAxis.reverse()
        dataA.reverse()
        dataS.reverse()
        dataZ.reverse()

        let datas = []
        for (const key in this.topicClassic) {
          datas.push({
            name:key,
            value:parseInt(this.topicClassic[key])
          })
        }
        let sum = datas.reduce((sum, e) => sum + Number(e.value || 0), 0)

        const options = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            formatter: function (params) {

              let value1 = params[0].value < 0 ?-params[0].value/4:params[0].value
              let value2 = params[1].value < 0 ?-params[1].value/4:params[1].value
              let value3 = params[2].value < 0 ?-params[2].value/4:params[2].value
              value1 = (value1/sum*100).toFixed(2)
              value2 = (value2/sum*100).toFixed(2)
              value3 = (value3/sum*100).toFixed(2)
              return (
                params[0].name +
                `</br> <span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:#77a1fd;"></span>` +
                `技术S级 : ` +
                value1 + '%' +
                `</br> <span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:#4472c4;"></span>` +
                `平台A级 : ` +
                value2 + '%' +
                `</br> <span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:#4472c4;"></span>` +
                `课题 : ` + value3 + '%'
                 );
            },

          },
          color: ['#77a1fd','#4472c4', '#44546a'],
          legend: {
            top: '1%',
            itemWidth: 8,
            itemHeight: 8,
            show:true,
          },
          grid: {
            left: '1%',
            right: '8%',
            bottom: '4%',
            top:'12%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            splitLine:{show: false},
            axisLabel: {
              formatter: (value, index) => {
                if(value < 0){
                  return (-value/4).toFixed(0)
                }
                return value
              }
            }
          },
          yAxis: {
             axisLine:{
                show: true
            },//坐标轴线
            axisLabel: { interval: 0,textStyle:{
                  fontSize:'10'
                } },
            type: 'category',
            data: dataAxis
          },
          series: [{
              name: `平台S级${(sumS/sum*100).toFixed(1)}%`,
              type: 'bar',
              stack: 'total',
              barWidth: 15,
              label: {
                normal:{
                  show: true,
                  color: '#fff',
                  position:'inside',
                  fontSize:'10',
                  formatter: function (params) {
                    if (params.value > 0) {
                      return params.value;
                    } else if(params.value < 0){
                      return -params.value/4;
                    }else {
                      return '';
                    }
                  },
                }
              },
              emphasis: {
                focus: 'series'
              },
              data: dataS
            },
            {
              name: `平台A级${(sumA/sum*100).toFixed(1)}%`,
              type: 'bar',
              stack: 'total',
              barWidth: 15,
              label: {
                normal:{
                  show: true,
                  color: '#fff',
                  position:'inside',
                  fontSize:'10',
                  formatter: function (params) {
                    if (params.value > 0) {
                      return params.value;
                    } else if(params.value < 0){
                      return -params.value/4;
                    }else {
                      return '';
                    }
                  },
                }
              },
              emphasis: {
                focus: 'series'
              },
              data: dataA
            },
            {
              name: `课题${(sumZ/sum*100).toFixed(1)}%`,
              type: 'bar',
              stack: 'total',
              barWidth: 15,
              label: {
                
                normal:{
                  show: true,
                  color: '#fff',
                  position:'inside',
                  fontSize:'10',
                  formatter: function (params) {
                    if (params.value > 0) {
                      return params.value;
                    } else {
                      return '';
                    }
                  },
                }
              },
              emphasis: {
                focus: 'series'
              },
              data: dataZ
            }
          ]
        }
        chart.setOption(options)
        chart.resize()

        this.topicLevelspinning = false

      },

      initTopicDelay() {
        this.delayChart = this.echarts.init(this.$refs.delay)
        let data1 = []
        let data2 = []
        let dataAxis = []
        let dataZ = []
        let delayCount = 0
        let totalCount = 0
        let percent = []
        for (const key in this.topicLightDelay) {
          data1.push(this.topicLightDelay[key])
          delayCount += this.topicLightDelay[key]
          dataAxis.push(key)
        }
        for (const key in this.topicSeriesDelay) {
          delayCount += this.topicSeriesDelay[key]
          data2.push(this.topicSeriesDelay[key])
        }
        for (const key in this.topicUnder) {
          dataZ.push(this.topicUnder[key])
          totalCount += this.topicUnder[key]
        }

        data1.reverse()
        dataAxis.reverse()
        data2.reverse()
        dataZ.reverse()

        let newData1 = []
        let newData2 = []

        for (let i = 0; i < data1.length; i++) {


          let per = ((data1[i]+data2[i])/dataZ[i]*100).toFixed(1)

          newData1.push({value:data1[i]/(data1[i]+data2[i])*per,num:data1[i]})
          newData2.push({value:data2[i]/(data1[i]+data2[i])*per,num:data2[i]})


          percent.push({value:((data1[i]+data2[i])/dataZ[i]*100).toFixed(1),num:data1[i]+data2[i]})

        }

        this.delayChart.clear()
        const options = {
          /* tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          }, */
          color: ['#77a1fd','#4472c4'],//['#ffdb6d','#ffc000'],
          legend: {
            data:['课题>1wk平台>2wk','课题>4wk平台>8wk'],
            show:false
          },
          grid: {
            left: 0,
            right: '20%',
            bottom: '0%',
            top:'13%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            splitLine:{show: false},
            axisLabel: {
              formatter: (value, index) => {
                return value+"%";
              }
            }
          },
          yAxis: {
            axisLabel: { interval: 0, /* rotate: 30, */textStyle:{
                  fontSize:'10'
                } },
            type: 'category',
            data: dataAxis
          },
          series: [{
              name: '1',
              type: 'bar',
              stack: 'total',
              barWidth: 15,
              label: {
                  show: true,
                  color: '#fff',
                  fontSize:'10',
                  position:'inside',
                  formatter:function(params){
                    if (params.data.num > 0) {
                      return params.data.num;
                    } else {
                      return '';
                    }
                  },

              },
              emphasis: {
                focus: 'series'
              },
              data: newData1
            },
            {
              name: '2',
              type: 'bar',
              stack: 'total',
              barWidth: 15,
              label: {
                normal:{
                  show: true,
                  color: '#fff',
                  position:'inside',
                  fontSize:'10',
                  formatter: function (params) {
                    if (params.data.num > 0) {
                      return params.data.num;
                    } else {
                      return '';
                    }
                  },
                }
              },
              emphasis: {
                focus: 'series'
              },
              data: newData2,
            },
            {
              name: '统计',
              type: 'bar',
              stack: 'total',
              label: {
                show: true,
                color: '#000',
                position:'right',
                fontSize: '10',
                formatter: function (params) {
                  return '('+percent[params.dataIndex].value+'%'+','+percent[params.dataIndex].num+')'

                }
              },
              emphasis: {
                focus: 'series'
              },
              data: [0, 0, 0, 0, 0, 0, 0, 0, 0],
            }
          ]
        }
        var series = options.series
        function getSum (params) {
          let dataValue = 0
          for (let i = 0; i < series.length; i++) {
            dataValue += series[i].data[params.dataIndex]
          }
          let count = dataZ[params.dataIndex] == 0 ? 0 : ((dataValue/ dataZ[params.dataIndex])*100).toFixed(1)
          return '('+count+'%'+','+dataValue+')'
        }
        //series[series.length - 1].label.formatter = getSum
        this.avgDelay = totalCount == 0 ? 0 : ((delayCount/totalCount)*100).toFixed(1)+'%'
        this.delayChart.setOption(options)
        this.delayChart.resize()

        this.delayspinning = false

      },
      onLegendClick(name){
        this.delayChart.dispatchAction({
          type: "legendToggleSelect",
          name: name,
        });
      }
    },
    computed: {
      ...mapGetters(['userInfo'])
    },
    mounted() {

      this.$refs.dashboard.style.zoom = 1
      this.$refs.dashboard.style.width = '100%'
      this.callChartProjects()
      this.callChartTopicsPass()
      window.addEventListener('resize',this.resize)
    },
    destroyed(){
      window.removeEventListener('resize',this.resize)
    }
  }
</script>

<style lang="less" scoped=''>
  /deep/.ant-input{
    border: none;
    border-bottom: 2px solid #e5e5e5;
  }
  /deep/.ant-input-sm {
    height: 24px;
    padding: 1px 0px;
  }
  .dashboard {
    display: flex;
    margin: auto;
    justify-content: center;
  }
  .col1,
  .col3 {
    width: 32%;
    max-width: 600px;
  }
  .col2 {
    width: 36%;
    max-width: 600px;
  }
  .item {
    width: 96%;
    margin: 8px auto;
    border-radius: 8px;
    overflow: hidden;
    background: #fff;
    box-shadow: 2px 2px 3px #ccc;
  }
  .col1 .item,
  .col3 .item {
    width: 100%;
  }
  .head {
    color: #000;
    font-size: 16px;
    border-bottom: 2px solid #e5e5e5;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .head svg{
    margin-right:2px ;
  }
  .head .right{
    font-weight: initial;
    font-size: 13px;
    float: right;
  }
  .head .left{
    display: flex;
    align-items: center;
  }
  .chart_table {
    width: 100%;
  }
  .chart_table.assess, .chart_table.classic, .chart_table.topicLevel{
    min-height: initial;

  }
  .dropdown{
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 1000;
    color: #000000aa;
  }
  .dropdown.prop{
    top: 2px;
    left: 8px;
  }

  .pie_chart{
    position: absolute;
    right: 0;
    top: 0;
    height: 90px;
    width: 90px;
    z-index: 100;
    
    background-color: rgb(255 255 255 / 0%);
    /* z-index: 100; */
  }
  .legend{
    width: 95%;
    position: absolute;
    top: 40px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 100;
    text-align: center;
  }
  .legend_btns{
    font-size: 12px;
  }
  .legend_btns span{
    margin: auto 2px;
    cursor: pointer;
    color: #000;
  } //'#77a1fd','#4472c4'
  .legend_btns span:first-child{
    background: #77a1fd;//#ffc000;
    color: #fff;
    padding: 2px 5px;
    border-radius: 4px;
  }
  .legend_btns span:nth-child(3){
    background: #4472c4;//#d95040;
    color: #fff;
    padding: 2px 5px;
    border-radius: 4px;
  }
  .date{
    position: absolute;
    top: 28px;
    width: 160px;
    left:3%;
    z-index: 1;
  }
  /deep/.ant-calendar-range-picker-input{
    width: 40%;
    font-size: 12px;
    color: #000;
    text-align: left;
  }
  /deep/.ant-calendar-picker:hover .ant-calendar-picker-input:not(.ant-input-disabled){
    border-color: #e5e5e5;
  }
</style>

