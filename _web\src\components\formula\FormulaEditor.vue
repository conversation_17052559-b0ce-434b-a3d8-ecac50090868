<template>
  <div>
    <a-row :gutter="16" class="editor-row">
      <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" class="editor-col">
        <div class="form-section">
          <h5>LaTeX公式（主公式）</h5>
          <div>
            <a-textarea
              v-model="mainFormula"
              placeholder="请输入LaTeX格式的主公式，例如：Q = Q_0 * (1 - a * t^b)"
              :autoSize="{ minRows: 2, maxRows: 8 }"
              class="full-width"
              @input="handleFormulaInput"
            />
          </div>
        </div>
      </a-col>

      <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" class="editor-col">
        <div class="form-section">
          <h5>
            子公式（可选）
            <a-tooltip placement="right">
              <template slot="title">
                子公式用于将公式拆分成多个部分，主公式中的变量可以在子公式中定义
              </template>
              <a-icon type="info-circle" class="info-icon" />
            </a-tooltip>
          </h5>
          <div>
            <div v-for="(_, index) in subFormulas" :key="index" class="sub-formula-item">
              <div class="sub-formula-input">
                <a-textarea
                  v-model="subFormulas[index]"
                  placeholder="请输入子公式，例如：a = k * T + m"
                  :autoSize="{ minRows: 1, maxRows: 1 }"
                  class="sub-formula-textarea"
                  @input="handleFormulaInput"
                />
                <a-button
                  type="danger"
                  icon="delete"
                  size="small"
                  @click="removeSubFormula(index)"
                  class="delete-btn delete-button"
                />
              </div>
            </div>
            <a-button type="dashed" class="add-formula-btn" @click="addSubFormula">
              <a-icon type="plus" /> 添加子公式
            </a-button>
          </div>
        </div>
      </a-col>
    </a-row>

    <a-row :gutter="16" class="editor-row">
      <a-col :span="24" class="editor-col">
        <div v-if="mainFormula" class="form-section">
          <h5>公式预览</h5>
          <div class="formula-preview formula-container">
            <div class="formula-loading"></div>

            <div class="formula-section-title">主公式</div>
            <div class="formula-preview-content main-formula">
              <div ref="mainFormulaPreview"></div>
            </div>

            <template v-if="subFormulas.length > 0">
              <div class="formula-section-title formula-section-title-sub">子公式</div>
              <div
                v-for="(_, index) in subFormulas"
                :key="index"
                class="formula-preview-content sub-formula"
              >
                <div :ref="`subFormulaPreview_${index}`"></div>
              </div>
            </template>
          </div>
        </div>
      </a-col>
    </a-row>

    <a-row :gutter="16" class="editor-row">
      <a-col :span="24" class="editor-col">
        <div class="form-section">
          <h5>
            自变量定义
            <a-tooltip placement="right">
              <template slot="title">
                自变量是指公式中需要提供实际数值的变量，如时间t、温度T、SOC等
              </template>
              <a-icon type="info-circle" class="info-icon" />
            </a-tooltip>
          </h5>
          <div>
            <a-row :gutter="16" type="flex" align="middle">
              <a-col :span="4">
                <div class="input-label">自变量</div>
              </a-col>
              <a-col :span="7">
                <div class="variable-display" v-if="independentParams.length > 0">
                  <a-tooltip
                    v-for="(param, index) in independentParams"
                    :key="index"
                    :title="paramDescriptions[param] || '暂无说明'"
                    placement="top"
                  >
                    <a-tag
                      color="blue"
                      closable
                      @close="removeParam(index)"
                      class="param-tag delete-button-tag"
                    >
                      {{ param }}
                    </a-tag>
                  </a-tooltip>
                </div>
                <div class="variable-display empty-tip" v-else>
                  尚未添加自变量
                </div>
              </a-col>
              <a-col :span="5">
                <a-input
                  v-model="newParam"
                  placeholder="输入自变量名称"
                  size="default"
                  @pressEnter="addParam"
                />
              </a-col>
              <a-col :span="5">
                <a-input
                  v-model="variableDescription"
                  placeholder="自变量说明"
                  size="default"
                />
              </a-col>
              <a-col :span="3">
                <a-button type="primary" @click="addParam" class="full-width">
                  <a-icon type="plus" /> 添加
                </a-button>
              </a-col>
            </a-row>
          </div>
        </div>
      </a-col>
    </a-row>

    <a-row v-if="!hideParseButton" :gutter="16" class="editor-row">
      <a-col :span="24">
        <a-button
          type="primary"
          @click="parseFormula"
          :loading="parsing"
          class="parse-button"
        >
          <a-icon type="calculator" /> 解析公式
        </a-button>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import formulaMixin from '@/mixins/formulaMixin';
import { showWarning, showSuccess } from '@/utils/errorUtils';

export default {
  name: 'FormulaEditor',
  mixins: [formulaMixin],
  props: {
    initialFormula: {
      type: [String, Object],
      default: () => ({
        main_formula: 'Q_{capacity} = 1 - \\exp\\Bigg(  A_0 + A_1 * S + A_2 * S^2  + \\frac{B_1}{T_0}   + \\frac{B_2}{T_0^2}   + \\frac{B_3}{T_0^3}  + \\Big( C_0 + C_1 * T_0  + C_2 * T_0^2  + D_1 * S + D_2 * S^2  + E\\Big) * \\ln(t)  \\Bigg)',
        sub_formulas: [
          'E = E_0 * \\exp\\big( E_1 * T_0 + E_2 * S + E_3 * t \\big)',
          'T_0 = F_3 + F_2 * T + F_1 * T^2 + F_0 * T^3'
        ]
      })
    },
    initialParams: {
      type: Array,
      default: () => ['t', 'T', 'S']
    },
    hideParseButton: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      mainFormula: typeof this.initialFormula === 'object'
        ? (this.initialFormula.main_formula || '')
        : this.initialFormula,
      subFormulas: typeof this.initialFormula === 'object' && this.initialFormula.sub_formulas
        ? [...this.initialFormula.sub_formulas]
        : [],
      independentParams: [...this.initialParams],
      newParam: '',
      variableDescription: '',
      paramDescriptions: this.getDefaultParamDescriptions(),
      parsing: false
    };
  },
  computed: {
    fullFormulaForPreview() {
      let formula = this.mainFormula;
      if (this.subFormulas.length > 0) {
        formula += '\\quad \\\\ \\text{其中:} \\\\ \\quad ';
        formula += this.subFormulas.join('\\\\ \\quad ');
      }
      return formula;
    },
    subFormulasForPreview() {
      if (this.subFormulas.length === 0) return '';
      return '\\\\ \\quad ' + this.subFormulas.join('\\\\ \\quad ');
    }
  },
  watch: {
    mainFormula() {
      this.debouncedUpdateFormulaPreview(
        this.mainFormula,
        this.subFormulas,
        this.updateFormulaPreview
      );
    },
    subFormulas: {
      handler() {
        this.debouncedUpdateFormulaPreview(
          this.mainFormula,
          this.subFormulas,
          this.updateFormulaPreview
        );
      },
      deep: true
    }
  },
  methods: {
    // 获取默认参数说明信息
    getDefaultParamDescriptions() {
      return {
        't': '时间变量，表示存储时间（天）',
        'T': '温度变量，表示存储温度（℃）',
        'S': 'SOC变量，表示荷电状态（%）'
      };
    },

    // 初始化参数说明信息
    initializeParamDescriptions() {
      const defaultDescriptions = this.getDefaultParamDescriptions();
      this.independentParams.forEach(param => {
        if (!this.paramDescriptions[param] && defaultDescriptions[param]) {
          this.paramDescriptions[param] = defaultDescriptions[param];
        }
      });
      // 触发更新事件
      this.$emit('update:paramDescriptions', { ...this.paramDescriptions });
    },

    handleFormulaInput() {
      this.$emit('update:formula', {
        main_formula: this.mainFormula,
        sub_formulas: this.subFormulas
      });
    },

    handleVariableDescChange() {
      this.$emit('update:variableDescription', this.variableDescription);
    },

    addSubFormula() {
      this.subFormulas.push('');
      this.handleFormulaInput();
    },

    removeSubFormula(index) {
      this.subFormulas.splice(index, 1);
      this.handleFormulaInput();
    },

    addParam() {
      if (this.newParam.trim() === '') return;

      this.independentParams.push(this.newParam);
      this.$emit('update:params', [...this.independentParams]);

      // 如果用户输入了说明，使用用户输入的说明；否则使用默认说明
      const description = this.variableDescription || this.getDefaultParamDescriptions()[this.newParam] || '';

      if (description) {
        this.paramDescriptions[this.newParam] = description;
        this.$emit('update:paramDescriptions', {
          ...this.paramDescriptions
        });
      }

      this.newParam = '';
      this.variableDescription = '';
    },

    removeParam(index) {
      this.independentParams.splice(index, 1);
      this.$emit('update:params', [...this.independentParams]);
    },

    updateFormulaPreview() {
      if (!this.mainFormula) return;

      if (this.$refs.mainFormulaPreview) {
        const mainFormulaLatex = this.renderLatex(this.mainFormula);
        this.$refs.mainFormulaPreview.innerHTML = mainFormulaLatex;
      }

      if (this.subFormulas.length > 0) {
        this.subFormulas.forEach((subFormula, index) => {
          const refName = `subFormulaPreview_${index}`;
          if (this.$refs[refName]?.[0]) {
            const subFormulaLatex = this.renderLatex(subFormula);
            this.$refs[refName][0].innerHTML = subFormulaLatex;
          }
        });
      }

      this.renderMathJax(true);
    },

    async parseFormula() {
      this.parsing = true;

      try {
        const result = await this.parseLatexFormula(
          this.mainFormula,
          this.subFormulas,
          this.independentParams
        );

        if (result.success) {
          await this.handleSuccessfulParse(result);
        } else {
          showWarning(result.message || '公式解析失败');
        }
      } catch (error) {
        console.error('解析公式出错:', error);
        showWarning(error.message || '公式解析请求失败');
      } finally {
        this.parsing = false;
      }
    },

    async handleSuccessfulParse(data) {
      const params = data.parsedParams || [];

      const parsedParams = Array.isArray(params) ? params.map(param => {
        const processedParam = {
          ...param,
          value: param.type === 'coefficient' ? 1.0 : null
        };

        if (processedParam.type === 'coefficient' && processedParam.name) {
          if (!processedParam.name.includes('_{') && processedParam.name.includes('_')) {
            processedParam.name = processedParam.name.replace(/([A-Za-z])_([A-Za-z0-9]+)/g, '$1_{$2}');
          }
        }

        if (this.paramDescriptions?.[param.name]) {
          processedParam.describe = this.paramDescriptions[param.name];
        }

        return processedParam;
      }) : [];

      this.$emit('formula-parsed', {
        parsedParams,
        parsedLatex: data.parsedLatex,
        variableDescription: this.variableDescription,
        paramDescriptions: this.paramDescriptions
      });

      await this.$nextTick();
      await this.renderMathJax(true);

      showSuccess('公式解析成功');
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.updateFormulaPreview();
      this.initializeParamDescriptions();
    });
  }
};
</script>

<style scoped>
.editor-row {
  display: flex;
  margin-bottom: 16px;
  align-items: stretch;
}

.editor-col {
  display: flex;
}

.form-section {
  width: 100%;
  padding: 0;
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.form-section > div:not(h5) {
  padding: 16px;
}

.formula-preview {
  background-color: #fff;
  padding: 0;
  margin-bottom: 0;
  border: none;
  border-radius: 0;
  overflow-x: auto;
  display: flex;
  flex-direction: column;
}

.formula-preview-content {
  min-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 16px;
  background-color: #fff;
  border-radius: 4px;
  min-width: 100%;
  margin-bottom: 8px;
}

.formula-preview-content.main-formula {
  border-bottom: 1px dashed #f0f0f0;
}

.formula-preview-content.sub-formula {
  border-top: 1px dashed #f0f0f0;
  background-color: #fafafa;
}

.formula-section-title-sub {
  margin-top: 16px;
}

.variable-display {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 4px;
  min-height: 38px;
  background-color: #fff;
}

.empty-tip {
  font-size: 12px;
  color: #888;
  padding: 8px;
  justify-content: center;
}

h5 {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 0;
  color: #333;
  padding: 10px 0;
  background-color: #f5f5f5;
  border-bottom: 1px solid #f0f0f0;
  text-align: center;
  border-radius: 8px 8px 0 0;
}

.input-label {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  text-align: right;
  padding-right: 10px;
}

.info-icon {
  margin-left: 5px;
}

.full-width {
  width: 100%;
}

.sub-formula-textarea {
  width: calc(100% - 40px);
}

.add-formula-btn {
  width: 100%;
  margin-top: 8px;
}

.param-tag {
  margin: 4px;
  font-size: 14px;
  padding: 4px 8px;
}

.parse-button {
  width: 100%;
  height: 42px;
  font-size: 16px;
}

.sub-formula-item {
  margin-bottom: 8px;
}

.sub-formula-input {
  display: flex;
  align-items: center;
}

.delete-btn {
  margin-left: 8px;
}

:deep(.ant-btn-primary) {
  height: 38px;
  font-weight: 500;
  transition: all 0.3s;
  border-radius: 4px;
}

:deep(.ant-btn-primary:hover) {
  opacity: 0.9;
}

:deep(.ant-form-item-label > label) {
  font-weight: 500;
  color: #333;
}

:deep(.ant-input), :deep(.ant-input-number) {
  border-radius: 4px;
}

:deep(.ant-tag) {
  border-radius: 4px;
}
</style>

<style>
@media (forced-colors: active) {
  .formula-preview,
  .formula-preview-content {
    forced-color-adjust: auto;
    border: 1px solid CanvasText;
  }

  :deep(.ant-tag) {
    forced-color-adjust: auto;
    border: 1px solid CanvasText;
  }

  :deep(.ant-btn),
  :deep(.ant-input),
  :deep(.ant-input-number) {
    forced-color-adjust: auto;
  }
}
</style>