/*
@license

dhtmlxGantt v.6.3.3 Standard

This version of dhtmlxGantt is distributed under GPL 2.0 license and can be legally used in GPL projects.

To use dhtmlxGantt in non-GPL projects (and get Pro version of the product), please obtain Commercial/Enterprise or Ultimate license on our site https://dhtmlx.com/docs/products/dhtmlxGantt/#licensing or contact <NAME_EMAIL>

(c) XB Software Ltd.

*/
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("locale/locale_pl",[],t):"object"==typeof exports?exports["locale/locale_pl"]=t():e["locale/locale_pl"]=t()}(window,function(){return function(e){var t={};function n(o){if(t[o])return t[o].exports;var i=t[o]={i:o,l:!1,exports:{}};return e[o].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(o,i,function(t){return e[t]}.bind(null,i));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="/codebase/",n(n.s=189)}({189:function(e,t){gantt.locale={date:{month_full:["Styczeń","Luty","Marzec","Kwiecień","Maj","Czerwiec","Lipiec","Sierpień","Wrzesień","Październik","Listopad","Grudzień"],month_short:["Sty","Lut","Mar","Kwi","Maj","Cze","Lip","Sie","Wrz","Paź","Lis","Gru"],day_full:["Niedziela","Poniedziałek","Wtorek","Środa","Czwartek","Piątek","Sobota"],day_short:["Nie","Pon","Wto","Śro","Czw","Pią","Sob"]},labels:{new_task:"Nowe zadanie",dhx_cal_today_button:"Dziś",day_tab:"Dzień",week_tab:"Tydzień",month_tab:"Miesiąc",new_event:"Nowe zdarzenie",icon_save:"Zapisz",icon_cancel:"Anuluj",icon_details:"Szczegóły",icon_edit:"Edytuj",icon_delete:"Usuń",confirm_closing:"",confirm_deleting:"Zdarzenie zostanie usunięte na zawsze, kontynuować?",section_description:"Opis",section_time:"Okres czasu",section_type:"Typ",column_wbs:"WBS",column_text:"Nazwa zadania",column_start_date:"Początek",column_duration:"Czas trwania",column_add:"",link:"Link",confirm_link_deleting:"zostanie usunięty",link_start:" (początek)",link_end:" (koniec)",type_task:"Zadanie",type_project:"Projekt",type_milestone:"Milestone",minutes:"Minuty",hours:"Godziny",days:"Dni",weeks:"Tydzień",months:"Miesiące",years:"Lata",message_ok:"OK",message_cancel:"Anuluj",section_constraint:"Constraint",constraint_type:"Constraint type",constraint_date:"Constraint date",asap:"As Soon As Possible",alap:"As Late As Possible",snet:"Start No Earlier Than",snlt:"Start No Later Than",fnet:"Finish No Earlier Than",fnlt:"Finish No Later Than",mso:"Must Start On",mfo:"Must Finish On",resources_filter_placeholder:"type to filter",resources_filter_label:"hide empty"}}}})});
//# sourceMappingURL=locale_pl.js.map