<template>
	<div class="container">
		<!-- 标题 start -->
		<div class="head-title mb10">
      {{ design.batteryName + ' '}}{{relation.code}}{{typeDate[relation.issueType]}}
		</div>
		<!-- 标题 end -->

		<!-- 内容 start -->
		<div class="content-wrapper">
			<table :border="1">
				<tr>
					<th colspan="6"><span class="th-title">基础信息</span></th>
				</tr>
				<tr>
					<th>产品名称</th>
					<td>{{ design.productName }}</td>
					<th>样品阶段</th>
					<td>{{ relation.sample }}</td>
					<th>方案编号</th>
					<td>{{ relation.code }}</td>
				</tr>
				<tr colspan="6">
					<td style="padding: 0;" colspan="2">
						<tr>
							<th class="none-b bottom-b">试验线</th>
							<td class="none-b left-b">{{relation.factory}}</td>
						</tr>
						<tr>
							<th class="none-b">提交人</th>
							<td class="none-b left-b top-b">{{relation.createName}}</td>
						</tr>
					</td>

					<th>方案描述</th>
					<td colspan="3">{{relation.remark}}</td>
				</tr>
				<!--<tr>
					<th>项目研发经理（RPM）</th>
					<td>{{ design.rpm }}</td>
					<th>产品经理（PD）</th>
					<td>{{ design.pd }}</td>
					<th>提交人</th>
					<td></td>
				</tr>-->
				<tr>
					<th colspan="6"><span class="th-title">待审批内容</span></th>
				</tr>
			</table>
		</div>
		<!-- 内容 end -->

		<!-- tabs start -->
		<div class="tabs-wrapper">
			<a-tabs type="card">
				<a-tab-pane key="1" tab="方案设计">
					<div class="tab-wrapper">
						<Programme :inBatteryId="inBatteryId" />
					</div>
				</a-tab-pane>
				<a-tab-pane key="2" tab="电芯BOM设计">
					<div class="tab-wrapper">
						<BOM :inBatteryId="inBatteryId" />
					</div>
				</a-tab-pane>
				<a-tab-pane key="3" tab="MI设计">
					<MI :inBatteryId="inBatteryId" />
				</a-tab-pane>
			</a-tabs>
		</div>
		<!-- tabs end -->
	</div>
</template>

<script>
import Programme from "./components/programme"
import BOM from "./components/bom"
import MI from "./components/mi"
import { checkRecordGetRelationByRecordId } from "@/api/modular/system/batterydesignCheckRecordManage"

import { getBatteryDesign } from "@/api/modular/system/batterydesignManage"

export default {
	data() {
		return {
      inBatteryId:null,
			design: {},
      relation:{},
      typeDate:{
			  "1":"SOR审批(A/B样)",
			  "2":"冻结审批",
			  "3":"冻结审批",
			  "4":"方案制样审批",
			  "5":"SOR审批",
			  "6":"冻结审批",
			  "7":"方案制样审批",
      },
      sampleData:{
			  "a":"A样",
			  "b":"B样",
			  "c":"C样",
			  "d":"D样",
      }
		}
	},
	components: {
		Programme,
		BOM,
		MI
	},
	created() {

		// if (window.location.href.indexOf('appCode=') !== -1) {
        //         let params = new URL(location.href).searchParams;
        //         const appCode = params.get('appCode')
        // }


    //审核页面
    if(null != this.$route.query.businessId){
      checkRecordGetRelationByRecordId({id:this.$route.query.businessId}).then(res1 => {
        this.relation = res1.data
        this.inBatteryId = res1.data.inBatteryId
        getBatteryDesign({ inBatteryId: res1.data.inBatteryId, type: "design" }).then(res => {
          this.design = res.data
        })
      })
    }

		/*// 获取信息
		getBatteryDesign({ inBatteryId: "1682262835397701633", type: "design" }).then(res => {
			this.list = res.data
		})*/
	}
}
</script>

<style lang="less" scoped>
.container {
	font-size: 12px;
	font-weight: 400;
	background-color: #fff;
	display: flex;
	flex-direction: column;
	align-items: center;

	height: 100vh;
	overflow: auto;
}

.head-title {
	text-align: center;
	font-size: 20px;
	font-weight: 600;
	color: #000;
}
.content-wrapper {
	display: flex;
	justify-content: center;
}

.content-wrapper table th {
	background: #f6f6f6;
	text-align: center;
}

th,
td {
	width: 200px;
	height: 30px;
	padding: 0;
	font-size: 13px;
	font-weight: 400;
}
td{
	padding: 0 10px;
}

// 表格标题
.th-title {
	font-size: 16px;
	color: #000;
	font-weight: 600 !important;
}

.tabs-wrapper {
	width: calc(200px * 6);
	padding-top: 10px;
	border: 1px solid;
	border-top: none;
}

.tab-wrapper {
	width: calc(200px * 6);
	margin-bottom: 100px;
}

// 去除边框
.none-b {
	border: none;
}

.left-b {
	border-left: 1px solid;
}
.top-b {
	border-top: 1px solid;
}
.bottom-b {
	border-bottom: 1px solid;
}

/**组件 */
/deep/.ant-tabs-tab {
	padding: 12px 40px;
	background: #f6f6f6 !important;
}

/deep/.ant-tabs-tab-active{
	background: #fff !important;
}

</style>
