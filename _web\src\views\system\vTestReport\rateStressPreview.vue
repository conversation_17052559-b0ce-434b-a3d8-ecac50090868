<template>
  <div ref="wrapper" class="wrapper">
    <!-- <div class="export-btn">
      <a-button style="width: 100%;" type="primary" @click="exportData">导出</a-button>
    </div> -->
    <div class="flex-sb-center-row">
      <div class="head_title">{{ record.type + ": " + record.reportName }}</div>
    </div>

    <div class="all-wrapper">
      <div class="left-content block" v-if="record.type === '倍率-脉放map'">
        <pageComponent editObj="timeVolTempCurPic"  @down="handleDown('timeVolTempCurPic')" @edit="handleEditEcharts('timeVolTempCurPic')"></pageComponent>
        <div ref="timeVolTempCurPic" id="timesVolTempCurPic" class="mt10" style="width: 598px;height: 498px;border: 0.5px solid #ccc;"></div>
      </div>
      <div class="left-content block" v-else>
        <pageComponent editObj="voltageTime"  @down="handleDown(record.type)" @edit="handleEditEcharts('voltageTime')"></pageComponent>
        <div ref="voltageTime" id="voltageTime" class="mt10" style="width: 598px;height: 498px;border: 0.5px solid #ccc;"></div>
      </div>
      <div class="right-content">
        <div class="block" id="export">
          <div class="flex-column">
            <div>
              <div>原始数据</div>
              <div style="float: right;margin-top: -25px">
                <!--                <a-button type="primary" @click="() => update = true" v-if="!update">修改数据</a-button>-->
                <a-button type="primary" @click="cancel" v-if="update" style="margin-right: 20px">取消修改</a-button>
                <a-button type="primary" @click="setFormula" v-if="update" style="margin-right: 20px">设置公式</a-button>
                <!--                <a-popconfirm placement="topRight" ok-text="确认" cancel-text="取消" @confirm="updateData" v-if="update">-->
                <!--                  <template slot="title">-->
                <!--                    <p>确认提交更改吗</p>-->
                <!--                  </template>-->
                <!--                  <a-button type="primary">提交数据</a-button>-->
                <!--                </a-popconfirm>-->

              </div>
            </div>
            <div class="mt10">
              <a-table v-if="!update" class="originalTableClass" :columns="originColumns" bordered
                :data-source="originData" :pagination="false" :scroll="{ y: 500, x: 1800 }">
                <div v-for="item in thirdHeaderList" :slot=item>
                  <span v-if="item.indexOf('1#-totalStepTime')!==-1">时间</span>
                  <span v-if="item.indexOf('voltage')!==-1">电压</span>
                  <span v-if="item.indexOf('current')!==-1">电流</span>
                  <span v-if="item.indexOf('auxTem1')!==-1">温度</span>
                </div>
              </a-table>
              <a-table v-if="update" class="originalTableClass" :columns="originColumns" bordered
                :rowKey="(record) => record.id" :row-selection="{
                  selectedRowKeys: selectedRowKeys, selectedRows: selectedRows,
                  onSelectAll: onSelectAll,
                  onSelect:onSelect, columnWidth:10}" :data-source="originData" :pagination="false"
                :scroll="{ y: 500, x: 1000 }">
                <div v-for="item in thirdHeaderList" :slot=item>
                  <a-checkbox v-if="item.indexOf('1#-totalStepTime')!==-1"
                    @change="checkHeaderName($event,item)">时间</a-checkbox>
                  <a-checkbox v-if="item.indexOf('voltage')!==-1" @change="checkHeaderName($event,item)">电压</a-checkbox>
                  <a-checkbox v-if="item.indexOf('current')!==-1" @change="checkHeaderName($event,item)">电流</a-checkbox>
                  <a-checkbox v-if="item.indexOf('auxTem1')!==-1" @change="checkHeaderName($event,item)">温度</a-checkbox>
                </div>
              </a-table>
              <a-pagination style="margin-top: 20px;float: right" v-model="pageNo" :page-size-options="pageSizeOptions"
                :total="totalRows" show-size-changer :page-size="pageSize" @change="onPageChange"
                @showSizeChange="onPageChange">
                <template slot="buildOptionText" slot-scope="props">
                  <span v-if="props.value !== totalRows">{{ props.value }}条/页</span>
                  <span v-if="props.value === totalRows">全部</span>
                </template>
              </a-pagination>
            </div>
          </div>
          <div class="flex-column3" v-if="curVolData.length > 0">
            <div>
              <div style="margin-top: 20px">电流-电压数据</div>
            </div>
            <div class="mt10">
              <a-table class="capacityClass" :columns="curVolColumns" bordered :data-source="curVolData"
                :pagination="false" :scroll="{ y: 500, x: 1000 }"> </a-table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="drawerVisible">
      <PreviewDrawer :screenImageId = "screenImageId" :templateParam = "reportChartTemplateList[editObj]" :isLegendLeft =  "true" :legendOptions="originalLegent" :data="originalseries"
        :original="resetOriginal" :editData="editData" :checkObj="chartCheckObj[editObj]" @submit="handleDrawerSubmit"
        @reset="handleDrawerReset" @close="drawerVisible = false" @changeTemplate ="handleChangeTemplate" @screenshot="handleScreenshot"></PreviewDrawer>
    </div>

    <a-modal title="设置公式" :visible="setFormulaFlag" @ok="handleSetFormulaModalOk" @cancel="handleSetFormulaModalCancel">
      <a-form :label-col="{ span: 5 }" :wrapper-col="{ span: 13 }">
        <a-form-item label="公式">
          <span style="font-weight: bold">选中的数据</span>
          <a-select v-model="symbol" placeholder="请选择计算符" style="width:60px;margin-left: 10px">
            <a-select-option value="+">
              +
            </a-select-option>
            <a-select-option value="-">
              -
            </a-select-option>
            <a-select-option value="x">
              x
            </a-select-option>
            <a-select-option value="÷">
              ÷
            </a-select-option>
          </a-select>
          <a-input-number style="width:100px;margin-left: 10px" v-model="symboledNumber" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- <pbiReturnTop v-if="isShowReturnTop" @returnTop="handleReturnTop"></pbiReturnTop> -->
  </div>
</template>
<script>
  import {
    getRateStressTestReport,
    updateCRateData
  } from "@/api/modular/system/limsManager"

  import $ from 'jquery';

  import { STable } from "@/components";
  import { Pagination } from 'ant-design-vue';

  import { mixin,chart } from "./mixin/index.js"
  import {chartTemplate} from "@/views/system/vTestReport/mixin/chartTemplate";

  export default {
    components: {
      STable,
      'a-pagination': Pagination
    },
    mixins: [mixin,chart,chartTemplate],
    data: function () {
      return {
        id: null,
        update: false,
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        pageNo: 1,
        pageSize: 10,
        totalRows: 50,
        pageData: null,
        echartsLineType: ["solid", "dashed", "dotted"],
        originColumns: [],
        thirdHeaderList: [],
        selectedRowKeys: [],
        selectedHeaderNameRowKeys: [],
        selectedRows: [],
        setFormulaFlag: false,
        symbol: null,
        symboledNumber: null,
        curVolColumns: [],
        originData: [],
        curVolData: [],
        voltageTimeEchart: null,

        timeVolTempCurPicEchart: null,

        isEditXNum: 0,
        isEditYNum: 0,
        // checkObj: {}, //点击图标的选中对象  editObj: axis 坐标轴  tag 线 legend 图例  tag: axs 选中的那条线
        originalLegent: [], //原始图例
        originalseries: [], //原始图表数据
        editData: {},
        resetOriginal: {},
        chartCheckObj:{
          timeVolTempCurPic:{},
          voltageTime:{}
        }
      }
    },
    async mounted() {
      await this.getChartTemplateRelationList(this.$route.query.id,['timeVolTempCurPic','voltageTime'])
      this.init()
      // const box = this.$refs.wrapper
      // box.addEventListener("scroll", e => {
      //   if (e.target.scrollTop > 100 && !this.isShowReturnTop) {
      //     this.isShowReturnTop = true
      //   }
      //   if(e.target.scrollTop < 100 && this.isShowReturnTop){
      //     this.isShowReturnTop = false
      //   }
      // })
    },
    methods: {
      init() {
        this.setFormulaFlag = false
        this.selectedHeaderNameRowKeys = []
        this.selectedRows = []
        this.originColumns = []
        this.selectedRowKeys = []
        this.id = this.$route.query.id
        getRateStressTestReport({ id: this.id, pageNo: this.pageNo, pageSize: this.pageSize })
          .then(res => {
            this.record = res.data
            this.allDataJson = JSON.parse(res.data.allDataJson)
            this.queryParam = JSON.parse(res.data.queryParam)
            // 原始数据分页
            this.pageData = JSON.parse(res.data.allDataJson).tableList
            if (JSON.parse(res.data.allDataJson).currentVoltageList) {
              this.curVolData = JSON.parse(res.data.allDataJson).currentVoltageList
            }
            this.originData = this.pageData.records
            this.pageNo = this.pageData.current
            this.pageSize = this.pageData.size
            this.totalRows = this.pageData.total
            if (this.pageSizeOptions.indexOf(this.totalRows.toString()) === -1) {
              this.pageSizeOptions.push(this.totalRows.toString())
            }
            for (let i = 0; i < this.originData.length; i++) {
              this.originData[i].id = i
            }
            this.update = false
          })
          .then(res => {
            let properties = Object.getOwnPropertyNames(this.originData[0]);
            properties = properties.filter(item => item !== '__ob__' && item !== 'id' && item !== 'stepId' && item !== '1#-stepTime');
            properties.sort((a, b) => {
              // 第一优先级：totalStepTime始终在最前面
              // -1：表示a小于b，因此a会出现在b前面;0：表示a等于b，它们的位置保持不变（或任意顺序，因为相等性优先）;1：表示a大于b，因此b会出现在a前面
              if (this.queryParam.reportType !== '倍率-stress') {
                if (a.includes('sequenceNumber')) return -1;
                if (b.includes('sequenceNumber')) return 1;
              }
              if (a.includes('-totalStepTime')) return -1;
              if (b.includes('-totalStepTime')) return 1;
              // 第二优先级：按数字前缀排序
              const aNumber = parseInt(a.split('#')[0]);
              const bNumber = parseInt(b.split('#')[0]);
              if (aNumber < bNumber) return -1;
              if (aNumber > bNumber) return 1;
              // 第三优先级：voltage在auxTem1前面
              if (a.endsWith('-voltage')) return -1;
              if (b.endsWith('-voltage')) return 1;
              if (this.queryParam.reportType === '倍率-脉放map') {
                if (a.endsWith('-current')) return -1;
                if (b.endsWith('-current')) return 1;
              }
              // 如果以上条件都不满足，则视为两者相等（或无需交换）
              return 0;
            })
            let curvolProperties = []
            if (this.curVolData.length > 0) {
              curvolProperties = Object.getOwnPropertyNames(this.curVolData[0]);
              curvolProperties = curvolProperties.filter(item => item !== '__ob__');
              curvolProperties.sort((a, b) => {
                if (a.includes('current')) return -1;
                if (b.includes('current')) return 1;
                if (a.includes('average')) return -1;
                if (b.includes('average')) return 1;
                const aNumber = parseInt(a.split('#')[0]);
                const bNumber = parseInt(b.split('#')[0]);
                if (aNumber < bNumber) return -1;
                if (aNumber > bNumber) return 1;
                return 0;
              })
            }
            let firstHeaderList = ['电芯']
            for (let i = 0; i < this.queryParam.orderData.length; i++) {
              let batteryNo = i + 1
              firstHeaderList.push(batteryNo + "#")
            }
            this.thirdHeaderList = properties
            let firstColumn = {
              title: firstHeaderList[0],
              align: "center",
              width: "10px",
              children: []
            }

            if (this.queryParam.reportType !== '倍率-stress') {
              firstColumn.children.unshift({
                align: "center",
                width: "10px",
                dataIndex: 'sequenceNumber',
                title: '记录序号'
              })
            } else {
              firstColumn.children.unshift({
                align: "center",
                width: "10px",
                dataIndex: this.thirdHeaderList[0],
                slots: { title: this.thirdHeaderList[0] }
              })
            }
            this.originColumns.push(firstColumn)
            if (this.originData.length > 0) {
              for (let i = 1; i < firstHeaderList.length; i++) {
                let finalHeader = {
                  title: firstHeaderList[i],
                  align: "center",
                  width: "10px",
                  children: []
                }
                let beginIndex;
                let endIndex;
                if (this.queryParam.reportType === '倍率-脉放map') {
                  beginIndex = (i - 1) * 3 + 1
                  endIndex = beginIndex + 3
                } else if (this.queryParam.reportType === '倍率-脉冲放电') {
                  beginIndex = i * 2 - 1
                  endIndex = beginIndex + 2
                } else { // 倍率-stress
                  beginIndex = i * 2 - 1
                  endIndex = beginIndex + 2
                }
                for (let k = beginIndex; k < endIndex; k++) {
                  finalHeader.children.push({
                    align: "center",
                    width: "10px",
                    dataIndex: properties[k],
                    slots: { title: this.thirdHeaderList[k] }
                  })
                }
                this.originColumns.push(finalHeader)
              }
              if (this.curVolData.length > 0) {
                this.curVolColumns = [{
                  title: "电流",
                  dataIndex: "current",
                  width: "10px",
                  align: "center",
                }]
                let finalHeader = {
                  title: "电压(V)",
                  align: "center",
                  width: "10px",
                  children: []
                }
                for (let i = 1; i < curvolProperties.length; i++) {
                  finalHeader.children.push({
                    title: curvolProperties[i] === "average" ? "Avg." : curvolProperties[i].split("-")[0],
                    align: "center",
                    width: "10px",
                    dataIndex: curvolProperties[i],
                  })
                }
                this.curVolColumns.push(finalHeader)
              }
              const temHeight = document.body.clientHeight - document.getElementById("export").offsetHeight - 610
              document.documentElement.style.setProperty(`--height`, `${temHeight}px`)
            }

            // 生成【倍率-脉放map】图
            if (this.allDataJson.timeVoltageEchartsDataList) {
              this.editData = this._getInitData('timeVolTempCurPic','edit')
              this.resetOriginal = this._getInitData('timeVolTempCurPic')

              // timeVolTempCurPic
              this.initTimeVoltageEcharts()
            } else if (this.allDataJson.pdTimeTempEchartsDataList) {
              this.editData = this._getInitData('voltageTime','edit')
              this.resetOriginal = this._getInitData('voltageTime')

              // 生成【倍率-脉冲放电】图  voltageTime
              this.initPdTimeTempEcharts()
            } else {
              this.editData = this._getInitData('voltageTime','edit')
              this.resetOriginal = this._getInitData('voltageTime')
              // 生成【倍率-stress】图 voltageTime
              this.initStressTestEcharts()
            }
          })
      },

      // 【倍率-脉放map】数据   废弃
      initTimeVoltageData(checkData = []) {
        let timeVoltageData = _.cloneDeep(this.allDataJson.timeVoltageEchartsDataList)
        let timeTempData = _.cloneDeep(this.allDataJson.timeTempEchartsDataList)
        let timeCurrentData = _.cloneDeep(this.allDataJson.timeCurrentEchartsDataList)

        let lineColorList = [] // 折线颜色

        let legendList = []
        let seriesList = []
        let seriesOriginal = []
        let checkDataOriginal = []

        let duplicateDataOptions = []

        for (let i = 0; i < timeTempData.length; i++) {
          // 设置折线的颜色
          const have = lineColorList.find(v => v.name === 'Temp-' + timeTempData[i].batteryNo)
          if (have == undefined) {
            lineColorList.push({ name: 'Temp-' + timeTempData[i].batteryNo, color: this.echartsColor[lineColorList.length] })
          }

          const temIndex = checkData.findIndex(findItem => findItem.soc == 'Temp-' + timeTempData[i].batteryNo)

          let series = {
            id: 'Temp-' + timeTempData[i].batteryNo + (i + 1),
            name: 'Temp-' + timeTempData[i].batteryNo,
            soc: 'Temp-' + timeTempData[i].batteryNo,
            sampling: 'lttb',
            type: "line",
            barGap: 0,
            markPoint: {
              data: []
            },
            symbol: checkData.length === 0 ? "rect" : checkData[temIndex].symbol,
            symbolSize: checkData.length === 0 ? 0.2 : checkData[temIndex].symbolSize,
            lineStyle: {
              width: checkData.length === 0 ? 1 : checkData[temIndex].lineWidth,
              type: checkData.length === 0 ? "solid" : checkData[temIndex].lineType,
              color:
                checkData.length === 0
                  ? lineColorList[lineColorList.findIndex(v => v.name === 'Temp-' + timeTempData[i].batteryNo)].color
                  : checkData[temIndex].lineColor
            },
            itemStyle: {
              color:
                checkData.length === 0
                  ? lineColorList[lineColorList.findIndex(v => v.name === 'Temp-' + timeTempData[i].batteryNo)].color
                  : checkData[temIndex].itemColor
            },

            emphasis: {
              focus: "series"
            },
            large: true,
            data: timeTempData[i].data.map((mapItem, index) => { return { id: index, value: mapItem.value } }),
          }

          // 设置最大最小值
          if (checkData.length > 0 && checkData[temIndex].maxPoint) {
            series.markPoint.data.push({ type: "max", name: "Max" })
          }
          if (checkData.length > 0 && checkData[temIndex].minPoint) {
            series.markPoint.data.push({ type: "min", name: "Min" })
          }

          seriesOriginal.push({
            id: 'Temp-' + timeTempData[i].batteryNo + (i + 1),
            name: 'Temp-' + timeTempData[i].batteryNo,
            soc: 'Temp-' + timeTempData[i].batteryNo,
            data: timeTempData[i].data.map(v => v.value[1].toString()).filter(filterItem => filterItem !== ''),
            synchronization: checkData.length === 0 ? i : checkData[temIndex].synchronization,
            maxPoint: checkData.length === 0 ? false : checkData[temIndex].maxPoint,
            minPoint: checkData.length === 0 ? false : checkData[temIndex].minPoint,
            symbol: checkData.length === 0 ? "none" : checkData[temIndex].symbol,
            symbolSize: checkData.length === 0 ? 5 : checkData[temIndex].symbolSize,
            itemColor:
              checkData.length === 0
                ? lineColorList[lineColorList.findIndex(v => v.name === 'Temp-' + timeTempData[i].batteryNo)].color
                : checkData[temIndex].itemColor,
            lineType: checkData.length === 0 ? "solid" : checkData[temIndex].lineType,
            lineWidth: checkData.length === 0 ? 1 : checkData[temIndex].lineWidth,
            lineColor:
              checkData.length === 0
                ? lineColorList[lineColorList.findIndex(v => v.name === 'Temp-' + timeTempData[i].batteryNo)].color
                : checkData[temIndex].lineColor
          })

          // 原始值
          checkDataOriginal.push({
            soc: 'Temp-' + timeTempData[i].batteryNo,
            symbol: "none",
            symbolSize: 5,
            itemColor: lineColorList[lineColorList.findIndex(v => v.name === 'Temp-' + timeTempData[i].batteryNo)].color,
            lineType: "solid",
            lineWidth: 1,
            lineColor: lineColorList[lineColorList.findIndex(v => v.name === 'Temp-' + timeTempData[i].batteryNo)].color
          })

          duplicateDataOptions.push({
            id: timeTempData[i].batteryNo,
            data: timeTempData[i].data.map((mapItem, index) => { return { id: index, value: index, label: mapItem.value[1].toString() } })
          })

          seriesList.push(series)
          legendList.push('Temp-' + timeTempData[i].batteryNo)
        }


        for (let i = 0; i < timeCurrentData.length; i++) {
          // 设置折线的颜色
          const have = lineColorList.find(v => v.name === 'Current-' + timeCurrentData[i].batteryNo)
          if (have == undefined) {
            lineColorList.push({ name: 'Current-' + timeCurrentData[i].batteryNo, color: this.echartsColor[lineColorList.length] })
          }

          const temIndex = checkData.findIndex(findItem => findItem.soc == 'Current-' + timeCurrentData[i].batteryNo)

          let series = {
            id: 'Current-' + timeCurrentData[i].batteryNo + (i + 1),
            name: 'Current-' + timeCurrentData[i].batteryNo,
            soc: 'Current-' + timeCurrentData[i].batteryNo,
            sampling: 'lttb',
            type: "line",
            barGap: 0,
            markPoint: {
              data: []
            },
            symbol: checkData.length === 0 ? "rect" : checkData[temIndex].symbol,
            symbolSize: checkData.length === 0 ? 0.2 : checkData[temIndex].symbolSize,
            lineStyle: {
              width: checkData.length === 0 ? 1 : checkData[temIndex].lineWidth,
              type: checkData.length === 0 ? "solid" : checkData[temIndex].lineType,
              color:
                checkData.length === 0
                  ? lineColorList[lineColorList.findIndex(v => v.name === 'Current-' + timeCurrentData[i].batteryNo)].color
                  : checkData[temIndex].lineColor
            },
            itemStyle: {
              color:
                checkData.length === 0
                  ? lineColorList[lineColorList.findIndex(v => v.name === 'Current-' + timeCurrentData[i].batteryNo)].color
                  : checkData[temIndex].itemColor
            },
            emphasis: {
              focus: "series"
            },
            large: true,
            data: timeCurrentData[i].data.map((mapItem, index) => { return { id: index, value: mapItem.value } }),
          }

          // 设置最大最小值
          if (checkData.length > 0 && checkData[temIndex].maxPoint) {
            series.markPoint.data.push({ type: "max", name: "Max" })
          }
          if (checkData.length > 0 && checkData[temIndex].minPoint) {
            series.markPoint.data.push({ type: "min", name: "Min" })
          }

          seriesOriginal.push({
            id: 'Current-' + timeCurrentData[i].batteryNo + (i + 1),
            name: 'Current-' + timeCurrentData[i].batteryNo,
            soc: 'Current-' + timeCurrentData[i].batteryNo,
            synchronization: checkData.length === 0 ? i : checkData[temIndex].synchronization,
            maxPoint: checkData.length === 0 ? false : checkData[temIndex].maxPoint,
            minPoint: checkData.length === 0 ? false : checkData[temIndex].minPoint,
            symbol: checkData.length === 0 ? "none" : checkData[temIndex].symbol,
            symbolSize: checkData.length === 0 ? 5 : checkData[temIndex].symbolSize,
            itemColor:
              checkData.length === 0
                ? lineColorList[lineColorList.findIndex(v => v.name === 'Current-' + timeCurrentData[i].batteryNo)].color
                : checkData[temIndex].itemColor,
            lineType: checkData.length === 0 ? "solid" : checkData[temIndex].lineType,
            lineWidth: checkData.length === 0 ? 1 : checkData[temIndex].lineWidth,
            lineColor:
              checkData.length === 0
                ? lineColorList[lineColorList.findIndex(v => v.name === 'Current-' + timeCurrentData[i].batteryNo)].color
                : checkData[temIndex].lineColor,
            data: timeCurrentData[i].data.map(v => v.value[1].toString()).filter(filterItem => filterItem !== ''),
          })

          // 原始值
          checkDataOriginal.push({
            soc: 'Current-' + timeCurrentData[i].batteryNo,
            symbol: "none",
            symbolSize: 5,
            itemColor: lineColorList[lineColorList.findIndex(v => v.name === 'Current-' + timeCurrentData[i].batteryNo)].color,
            lineType: "solid",
            lineWidth: 1,
            lineColor: lineColorList[lineColorList.findIndex(v => v.name === 'Current-' + timeCurrentData[i].batteryNo)].color
          })

          duplicateDataOptions.push({
            id: timeCurrentData[i].batteryNo,
            data: timeCurrentData[i].data.map((mapItem, index) => { return { id: index, value: index, label: mapItem.value[1].toString() } })
          })

          seriesList.push(series)
          legendList.push('Current-' + timeCurrentData[i].batteryNo)
        }


        for (let i = 0; i < timeVoltageData.length; i++) {
          const have = lineColorList.find(v => v.name === 'Voltage-' + timeVoltageData[i].batteryNo)
          if (have == undefined) {
            lineColorList.push({ name: 'Voltage-' + timeVoltageData[i].batteryNo, color: this.echartsColor[lineColorList.length] })
          }
          const temIndex = checkData.findIndex(findItem => findItem.soc == 'Voltage-' + timeVoltageData[i].batteryNo)


          let series = {
            id: 'Voltage-' + timeVoltageData[i].batteryNo + (i + 1),
            name: 'Voltage-' + timeVoltageData[i].batteryNo,
            soc: 'Voltage-' + timeVoltageData[i].batteryNo,
            sampling: 'lttb',
            type: "line",
            yAxisIndex: 1,
            barGap: 0,
            markPoint: {
              data: []
            },
            symbol: checkData.length === 0 ? "rect" : checkData[temIndex].symbol,
            symbolSize: checkData.length === 0 ? 0.2 : checkData[temIndex].symbolSize,
            lineStyle: {
              width: checkData.length === 0 ? 1 : checkData[temIndex].lineWidth,
              type: checkData.length === 0 ? "solid" : checkData[temIndex].lineType,
              color:
                checkData.length === 0
                  ? lineColorList[lineColorList.findIndex(v => v.name === 'Voltage-' + timeVoltageData[i].batteryNo)].color
                  : checkData[temIndex].lineColor
            },
            itemStyle: {
              color:
                checkData.length === 0
                  ? lineColorList[lineColorList.findIndex(v => v.name === 'Voltage-' + timeVoltageData[i].batteryNo)].color
                  : checkData[temIndex].itemColor
            },
            emphasis: {
              focus: "series"
            },
            large: true,
            data: timeVoltageData[i].data.map((mapItem, index) => { return { id: index, value: mapItem.value } }),
          }

          // 设置最大最小值
          if (checkData.length > 0 && checkData[temIndex].maxPoint) {
            series.markPoint.data.push({ type: "max", name: "Max" })
          }
          if (checkData.length > 0 && checkData[temIndex].minPoint) {
            series.markPoint.data.push({ type: "min", name: "Min" })
          }

          seriesOriginal.push({
            id: 'Voltage-' + timeVoltageData[i].batteryNo + (i + 1),
            name: 'Voltage-' + timeVoltageData[i].batteryNo,
            soc: 'Voltage-' + timeVoltageData[i].batteryNo,
            synchronization: checkData.length === 0 ? i : checkData[temIndex].synchronization,
            maxPoint: checkData.length === 0 ? false : checkData[temIndex].maxPoint,
            minPoint: checkData.length === 0 ? false : checkData[temIndex].minPoint,
            symbol: checkData.length === 0 ? "none" : checkData[temIndex].symbol,
            symbolSize: checkData.length === 0 ? 5 : checkData[temIndex].symbolSize,
            itemColor:
              checkData.length === 0
                ? lineColorList[lineColorList.findIndex(v => v.name === 'Voltage-' + timeVoltageData[i].batteryNo)].color
                : checkData[temIndex].itemColor,
            lineType: checkData.length === 0 ? "solid" : checkData[temIndex].lineType,
            lineWidth: checkData.length === 0 ? 1 : checkData[temIndex].lineWidth,
            lineColor:
              checkData.length === 0
                ? lineColorList[lineColorList.findIndex(v => v.name === 'Voltage-' + timeVoltageData[i].batteryNo)].color
                : checkData[temIndex].lineColor,
            data: timeVoltageData[i].data.map(v => v.value[1].toString()).filter(filterItem => filterItem !== ''),
          })

          // 原始值
          checkDataOriginal.push({
            soc: 'Voltage-' + timeVoltageData[i].batteryNo,
            symbol: "none",
            symbolSize: 5,
            itemColor: lineColorList[lineColorList.findIndex(v => v.name === 'Voltage-' + timeVoltageData[i].batteryNo)].color,
            lineType: "solid",
            lineWidth: 1,
            lineColor: lineColorList[lineColorList.findIndex(v => v.name === 'Voltage-' + timeVoltageData[i].batteryNo)].color
          })

          duplicateDataOptions.push({
            id: timeVoltageData[i].batteryNo,
            data: timeVoltageData[i].data.map((mapItem, index) => { return { id: index, value: index, label: mapItem.value[1].toString() } })
          })

          seriesList.push(series)
          legendList.push('Voltage-' + timeVoltageData[i].batteryNo)
        }

        return [seriesOriginal, checkDataOriginal, seriesList, legendList, duplicateDataOptions]
      },

      // 【倍率-脉放map】图  废弃
      initTimeVoltageEcharts(
        legendData = {},
        checkData = [], //选中的数据
        axisData = {},
        titleData = {},
        gridData = {}

      ) {
        if (this.timeVolTempCurPicEchart) this.timeVolTempCurPicEchart.dispose();

        this.timeVolTempCurPicEchart = this.echarts.init(this.$refs.timeVolTempCurPic, "walden", {
          renderer: 'webgl'
        })

        const templateParam = this.reportChartTemplateList['timeVolTempCurPic'].templateParamJson

        // 把模板的数据拼接进去
        const {
          titleData: newTitleData,
          gridData: newGridData,
          legendData: newLegendData,
          axisData: newAxisData,
          legend: newLegend
        } = this._getTemplateParams('timeVolTempCurPic', titleData, gridData, legendData, axisData, legend);
        titleData = newTitleData;
        gridData = newGridData;
        legendData = newLegendData;
        axisData = newAxisData;

        // 处理结果数据
        const processingResult = this.initTimeVoltageData(checkData)

        // 原始图例  使用位置：在线编辑图表--图例数据
        this.originalLegent = _.cloneDeep(processingResult[3])

        // 原始图表数据
        this.originalseries = processingResult[0]

        // 在线编辑图标：数据标签-数据
        this.editData.duplicateDataOptions = processingResult[4]

        // 重置数据
        this.resetOriginal.checkData = processingResult[1]
        this.resetOriginal.series = _.cloneDeep(processingResult[1]) 

        // 图表数据
        let seriesList = processingResult[2]

         // 首次渲染,所有图例都显示
         if(!legendData.legendRevealList || templateParam.legendData.legendRevealList){
          const revealNum = this.handleRevealNum(520,this.originalLegent)
          this.editData.legendRevealList = templateParam.legendData.legendRevealList ?? _.cloneDeep(this.originalLegent).slice(0,revealNum)
        }

        //页面上有哪些图例 使用位置：在线编辑图表--选中的图例数据（默认全部选中）
        this.editData.legend = _.cloneDeep(this.originalLegent)
        this.editData.series = _.cloneDeep(this.originalseries)

        /* 图例排序 开始 */
        if (legendData.legendSort) {
          this.editData.legend = _.cloneDeep(legendData.legendSort) // 将页面上的图例数组按照用户设置的顺序排序
        }
        this.editData.legendSort = !legendData.legendSort ? _.cloneDeep(this.originalLegent) : _.cloneDeep(this.editData.legend)
        /* 图例排序 结束 */

        /* 图例变更名称 开始 */
        if (legendData.legendEditName) {
          legendData.legendEditName.forEach(v => {
            if (v.newName && !v.isReset) {
              let temIndex1 = this.originalLegent.findIndex(findItem => findItem == v.originName)
              this.originalLegent[temIndex1] = v.newName
              let temIndex2 = this.editData.legend.findIndex(findItem => findItem == v.originName)
              this.editData.legend[temIndex2] = v.newName
              this.editData.series.forEach(findItem => {
                findItem.name = findItem.name == v.originName ? v.newName : findItem.name
              })
              seriesList.forEach(findItem => {
                findItem.name = findItem.name == v.originName ? v.newName : findItem.name
              })
            }

            if (!v.newName && v.isReset) {
              v.previousName = ''
              v.isReset = false
            }
          })

          // 赋予修改后的图例修改名称数组
          this.editData.legendEditName = legendData.legendEditName
        }

        // 图例修改名称数组  使用位置：在线编辑图表--图例--名称 ,首次进入，将图例的值给图例修改名称数组
        // originName 原始值 newName 新值 previousName 上一个值（用于清空的情况下使用）isReset 是否重置（用于清空的时候使用）
        if (this.editData.legendEditName.length === 0) {
          this.editData.legendEditName = this.originalLegent.map(v => { return {id:v, originName: v, previousName: '', newName: '', isReset: false } })
        }
        /* 图例变更名称 结束 */

        /* 没选中的图例的移除处理 */
        if (legendData.legendEdit) {
          // 移除页面上的对应的图例
          for (let i = 0; i < this.editData.legend.length; i++) {
            if (!legendData.legendEdit.includes(this.editData.legend[i])) {
              this.editData.legend.splice(i, 1)
              i--
            }
          }

          // 移除页面上的对应的图例的图表数据
          for (let i = 0; i < seriesList.length; i++) {
            if (!legendData.legendEdit.includes(seriesList[i].name)) {
              seriesList.splice(i, 1)
              i--
            }
          }


          // 判断依据
          for (let i = 0; i < checkData.length; i++) {
            if (!legendData.legendEdit.includes(checkData[i].name)) {
              checkData.splice(i, 1)
              i--
            }
          }
        }

        /* 没选中的数据的移除处理 */
        // if (checkData.length > 0) {
        //   seriesList.forEach((v, index) => {
        //     const handIndex = checkData.findIndex(findItem => findItem.id == v.id)
        //     for (let i = 0; i < v.data.length; i++) {
        //       if (!checkData[handIndex].duplicateData.includes(v.data[i].id)) {
        //         v.data.splice(i, 1)
        //         i--
        //       }
        //     }
        //   })
        // }


        // let title = 'Over Load map@' + this.queryParam.reportBasic.current + 'A'

        // 结合图例数据（线+图例都存在）和图例显隐（只存在线，剔除图例）
        const legend = []
        this.editData.legend.forEach(v => {
          const conditions1 = this.editData.legendRevealList.includes(v)
          if(conditions1){
            legend.push(v)
          }else{
            const conditions2 = this.editData.legendEditName.findIndex(findItem => findItem.newName == v)
            if(conditions2 > -1 && this.editData.legendRevealList.includes(this.editData.legendEditName[conditions2].originName)) legend.push(v)
          }
        })

        let options = {
          animationDuration: 2000,
          title: {
            text: titleData.chartTitle || 'Over Load map@' + this.queryParam.reportBasic.current + 'A',
            left: "center",
            top: titleData.titleTop ||  8,
            fontSize: 18,
            fontWeight: 500,
            color: '#000'
          },
          grid: {
            show: true,
            top: gridData.gridTop ||  100,
            left: gridData.gridLeft || 80,
            right: gridData.gridRight || 70,
            bottom: gridData.gridBottom || 70,
            borderWidth: 0.5,
            borderColor: "#ccc"
          },
          textStyle: {
            fontFamily: "Times New Roman"
          },
          color: this.echartsColor,
          tooltip: {
            trigger: "axis",
            formatter: function (params) {
              var result = params[0].axisValue + "<br>" // 添加 x 轴的数值
              params.forEach(function (item, dataIndex) {
                result +=
                  item.marker +
                  item.seriesName +
                  '<div style="width:20px;display: inline-block;"></div><div style="width:150px;display: inline-block;">' +
                  "</div>" +
                  item.value[1] +
                  "<br>" // 添加每个系列的数值
              })
              return result
            }
          },
          legend: {
            position: "inside",
            top: 40,
            fontSize: 14,
            color: "#000000",
            backgroundColor: legendData.legendBgColor || "#f5f5f5",
            itemWidth: legendData.legendWidth || 30,
            itemHeight: legendData.legendHeight || 5,
            itemGap: legendData.legendGap || 20,
            orient: legendData.legendOrient || 'horizontal',
            // position: "inside",
            top: legendData.legendTop || 35,
            left: legendData.legendLeft || 'center',
            // data: this.editData.legend,
            data: legend,
          },
          xAxis: [
            {
              name: titleData.XTitle || "Time",
              type: axisData.xType || "value",
              axisTick: { show: false },
              splitLine: {
                show: true, // 显示分隔线
                lineStyle: {
                  type: "solid",
                  width: 0.5
                }
              },
              axisLabel: {
                show: true,
                width: 0.5,
                fontSize: "15",
                color: "#000000"
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#ccc",
                  width: 0.5
                }
              },

              nameLocation: "middle", // 将名称放在轴线的中间位置
              nameGap: 35,
              nameTextStyle: {
                fontSize: 14,
                color: "#000000" // 可以根据需要调整字体大小
              }
            }
          ],
          yAxis: [
            {
              name: titleData.YTitle || "Temperature(℃)/Currentr(A)",
              type: titleData.yType || "value",
              position: "left",
              nameGap:titleData.yTitleLetf ||  40,
              splitLine: {
                show: true, // 显示分隔线
                lineStyle: {
                  type: "solid",
                  width: 0.5
                }
              },
              axisTick: {
                show: true // 显示刻度
              },
              axisLabel: {
                show: true,
                width: 0.5,
                fontSize: "15",
                color: "#000000"
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#ccc",
                  width: 0.5
                }
              },
              nameLocation: "middle", // 将名称放在轴线的起始位置
              nameRotate: 90, // 旋转角度，使名称竖排
              nameTextStyle: {
                fontSize: 16, // 可以根据需要调整字体大小
                fontWeight: 500,
                color: "#000000"
              }
            },
            {
              name: titleData.YTitle2 || "Voltage (V)",
              type: titleData.yType2 || "value",
              position: "right",
              nameGap: 40,
              splitLine: {
                show: true, // 显示分隔线
                lineStyle: {
                  type: "solid",
                  width: 0.5
                }
              },
              axisTick: {
                show: true // 显示刻度
              },
              axisLabel: {
                show: true,
                width: 0.5,
                fontSize: "15",
                color: "#000000"
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#ccc",
                  width: 0.5
                }
              },
              nameLocation: "middle", // 将名称放在轴线的起始位置
              nameRotate: 90, // 旋转角度，使名称竖排
              nameTextStyle: {
                fontSize: 16, // 可以根据需要调整字体大小
                fontWeight: 500,
                color: "#000000"
              }
            }
          ],
          series: seriesList
        }

        // 传回给在线编辑图表，当前图标上有的点
        this.editData.duplicateCheckedList = seriesList.map(mapItem => mapItem.data.filter(filterItem => filterItem.value[1] !== '').map(mapItem2 => mapItem2.id))

        /* 处理坐标轴 */
        if (axisData.xMin) {
          options.xAxis[0].min = axisData.xMin
        }
        if (axisData.xMax) {
          options.xAxis[0].max = axisData.xMax
        }
        if (axisData.xInterval) {
          options.xAxis[0].interval = axisData.xInterval
        }
        if (axisData.yMin) {
          options.yAxis[0].min = axisData.yMin
        }
        if (axisData.yMax) {
          options.yAxis[0].max = axisData.yMax
        }
        if (axisData.yInterval) {
          options.yAxis[0].interval = axisData.yInterval
        }
        if (axisData.yMin2) {
          options.yAxis[1].min = axisData.yMin2
        }
        if (axisData.yMax2) {
          options.yAxis[1].max = axisData.yMax2
        }
        if (axisData.yInterval2) {
          options.yAxis[1].interval = axisData.yInterval2
        }

        // 坐标轴类型赋值
        this.editData.xType = options.xAxis[0].type
        this.editData.yType = options.yAxis[0].type
        this.editData.yType2 = options.yAxis[1].type


        this.timeVolTempCurPicEchart.clear()

        // this.timeVolTempCurPicEchart.getZr().off('click')
        // this.timeVolTempCurPicEchart.getZr().on('click', params => {
        //   const { target, topTarget } = params

        //   // Z 0:坐标轴
        //   if (topTarget?.z === 0 && this.drawerVisible) {
        //     this.$set(this.checkObj, 'editObj', 'axis')
        //   }
        //   // Z 3:线
        //   if (topTarget?.z === 3 && this.drawerVisible) {
        //     const axs = target.parent?.parent?.__ecComponentInfo?.index
        //     this.$set(this.checkObj, 'tag', axs)
        //     this.$set(this.checkObj, 'editObj', 'tag')
        //   }
        //   // Z 4:图例
        //   if (topTarget?.z === 4 && this.drawerVisible) {
        //     const axs = target.parent?.__legendDataIndex
        //     this.$set(this.checkObj, 'editObj', 'legend')
        //   }
        // });
        this.timeVolTempCurPicEchart.getZr().off('dblclick')
				this.timeVolTempCurPicEchart.getZr().on('dblclick', ({target, topTarget}) => {
				  this._handleDblclickEchart(target, topTarget, 'timeVolTempCurPic')
				});
        this.timeVolTempCurPicEchart.setOption(options)

        // 如果坐标轴类型为数值轴，则计算出最大值最小值，以及间距
        if (options.xAxis[0].type === "value") {
          const xAxis = this.timeVolTempCurPicEchart.getModel().getComponent("xAxis").axis.scale
          this.editData.xMin = xAxis._extent[0]
          this.editData.xMax = xAxis._extent[1]
          this.editData.xInterval = xAxis._interval
        }

        if (options.yAxis[0].type === "value") {
          const yAxis = this.timeVolTempCurPicEchart.getModel().getComponent("yAxis").axis.scale
          this.editData.yMin = yAxis._extent[0]
          this.editData.yMax = yAxis._extent[1]
          this.editData.yInterval = yAxis._interval
        }

        if (this.isEditXNum === 0 && axisData.xType === "value") {
          this.isEditXNum++
          this.resetOriginal.xMin = this.editData.xMin
          this.resetOriginal.xMax = this.editData.xMax
          this.resetOriginal.xInterval = this.editData.xInterval
        }

        if (this.isEditYNum === 0 && axisData.yType === "value") {
          this.isEditYNum++
          this.resetOriginal.yMin = this.editData.yMin
          this.resetOriginal.yMax = this.editData.yMax
          this.resetOriginal.yInterval = this.editData.yInterval
        }

        const originalParam = this.reportChartTemplateList['timeVolTempCurPic'].originalParamJson
        if(originalParam?.xMax > 0){
          this.resetOriginal.xMin = originalParam.xMin
          this.resetOriginal.xMax = originalParam.xMax
          this.resetOriginal.xInterval = originalParam.xInterval
        }
        if(originalParam?.yMax > 0){
          this.resetOriginal.yMin = originalParam.yMin
          this.resetOriginal.yMax = originalParam.yMax
          this.resetOriginal.yInterval = originalParam.yInterval
        }

      },

      // 【倍率-脉冲放电】数据
      initPdTimeTempData(checkData = []) {
        let stressTestData = _.cloneDeep(this.allDataJson.stressTestEchartsDataList)
        let pdTimeTempData = _.cloneDeep(this.allDataJson.pdTimeTempEchartsDataList)

        stressTestData = stressTestData.sort((item1,item2) => { return  Number(item1.batteryNo.replace("#","")) -  Number(item2.batteryNo.replace("#","")) })
        pdTimeTempData = pdTimeTempData.sort((item1,item2) => { return  Number(item1.batteryNo.replace("#","")) -  Number(item2.batteryNo.replace("#","")) })

        const templateParam = this.reportChartTemplateList['voltageTime'].templateParamJson
        let lineColorList = [] // 折线颜色
        const echartsColorList = this._getLegendList(stressTestData,'batteryNo').length <= 2 ? this.echartsColorShortList : this.echartsColorLongList
        const isCheck = checkData.length === 0

        let legendList = []
        let seriesList = []
        let seriesOriginal = []
        let checkDataOriginal = []
        let duplicateDataOptions = []

        for (let i = 0; i < stressTestData.length; i++) {
          // 设置折线的颜色
          const have = lineColorList.find(v => v.name === 'Voltage-' + stressTestData[i].batteryNo)
          if (have == undefined) {
            lineColorList.push({ name: 'Voltage-' + stressTestData[i].batteryNo, color: echartsColorList[lineColorList.length % echartsColorList.length] })
          }

          const templateContent = templateParam.checkData.length > 0 ? (templateParam.checkData.filter(item => item.id === 'Voltage-' + stressTestData[i].batteryNo + (i + 1))[0] || {}) : {}
          const editContentIndex = checkData.findIndex(findItem => findItem.id == 'Voltage-' + stressTestData[i].batteryNo + (i + 1))
          const editContent = editContentIndex !== -1 ? checkData[editContentIndex] : {}

          let series = {
            id: 'Voltage-' + stressTestData[i].batteryNo + (i + 1),
            name: 'Voltage-' + stressTestData[i].batteryNo,
            soc: 'Voltage-' + stressTestData[i].batteryNo,
            sampling: 'lttb',
            type: "line",
            barGap: 0,
            markPoint: {
              data: []
            },
            connectNulls: templateContent.connectNulls ?? (isCheck ? false : Boolean(Number(editContent.connectNulls))),
            symbol: templateContent.symbol ?? (isCheck ? "rect" : editContent.symbol),
            symbolSize: templateContent.symbolSize ?? (isCheck ? 0.2 : editContent.symbolSize),
            lineStyle: {
              width: templateContent.lineWidth ?? (isCheck ? 1 : editContent.lineWidth),
              type: templateContent.lineType ?? (isCheck ? "solid" : editContent.lineType),
              color: templateContent.lineColor ?? (
                isCheck
                  ? lineColorList[lineColorList.findIndex(v => v.name === 'Voltage-' + stressTestData[i].batteryNo)].color
                  : editContent.lineColor
              )
            },
            itemStyle: {
              color: templateContent.itemColor ?? (
                isCheck
                  ? lineColorList[lineColorList.findIndex(v => v.name === 'Voltage-' + stressTestData[i].batteryNo)].color
                  : editContent.itemColor
              )
            },

            emphasis: {
              focus: "series"
            },
            large: true,
            data: stressTestData[i].data.map((mapItem, index) => { return { id: index, value: mapItem.value } }),
          }

          // 设置最大最小值
          if (templateContent.maxPoint || (!isCheck && editContent.maxPoint)) {
            series.markPoint.data.push({ type: "max", name: "Max" })
          }
          if (templateContent.minPoint || (!isCheck && editContent.minPoint)) {
            series.markPoint.data.push({ type: "min", name: "Min" })
          }

          seriesOriginal.push({
            id: 'Voltage-' + stressTestData[i].batteryNo + (i + 1),
            index: i + 1,
            name: 'Voltage-' + stressTestData[i].batteryNo,
            soc: 'Voltage-' + stressTestData[i].batteryNo,
            data: stressTestData[i].data.map(v => v.value[1].toString()).filter(filterItem => filterItem !== ''),
            duplicateData: stressTestData[i].data.map((mapItem, index) => { return { id: index, name: 'Voltage-' + stressTestData[i].batteryNo, value: mapItem.value } }),
            synchronization: templateContent.synchronization ?? (isCheck ? i : editContent.synchronization),
            maxPoint: templateContent.maxPoint ?? (isCheck ? false : editContent.maxPoint),
            minPoint: templateContent.minPoint ?? (isCheck ? false : editContent.minPoint),
            connectNulls: templateContent.connectNulls ?? false,
            symbol: templateContent.symbol ?? (isCheck ? "none" : editContent.symbol),
            symbolSize: templateContent.symbolSize ?? (isCheck ? 5 : editContent.symbolSize),
            itemColor: templateContent.itemColor ?? (
              isCheck
                ? lineColorList[lineColorList.findIndex(v => v.name === 'Voltage-' + stressTestData[i].batteryNo)].color
                : editContent.itemColor
            ),
            lineType: templateContent.lineType ?? (isCheck ? "solid" : editContent.lineType),
            lineWidth: templateContent.lineWidth ?? (isCheck ? 1 : editContent.lineWidth),
            lineColor: templateContent.lineColor ?? (
              isCheck
                ? lineColorList[lineColorList.findIndex(v => v.name === 'Voltage-' + stressTestData[i].batteryNo)].color
                : editContent.lineColor
            )
          })

          // 原始值
          checkDataOriginal.push({
            id: 'Voltage-' + stressTestData[i].batteryNo + (i + 1),
            index: i + 1,
            soc: 'Voltage-' + stressTestData[i].batteryNo,
            name: 'Voltage-' + stressTestData[i].batteryNo,
            connectNulls: false,
            synchronization: i,
            maxPoint: false,
            minPoint: false,
            symbol: "none",
            symbolSize: 5,
            itemColor: lineColorList[lineColorList.findIndex(v => v.name === 'Voltage-' + stressTestData[i].batteryNo)].color,
            lineType: "solid",
            lineWidth: 1,
            lineColor: lineColorList[lineColorList.findIndex(v => v.name === 'Voltage-' + stressTestData[i].batteryNo)].color
          })

          duplicateDataOptions.push({
            id: stressTestData[i].batteryNo,
            data: stressTestData[i].data.map((mapItem, index) => { return { id: index, value: index, label: mapItem.value[1].toString() } })
          })

          seriesList.push(series)
          legendList.push('Voltage-' + stressTestData[i].batteryNo)
        }
        for (let i = 0; i < pdTimeTempData.length; i++) {
          // 设置折线的颜色
          const have = lineColorList.find(v => v.name === 'Temp-' + pdTimeTempData[i].batteryNo)
          if (have == undefined) {
            lineColorList.push({ name: 'Temp-' + pdTimeTempData[i].batteryNo, color: echartsColorList[lineColorList.length % echartsColorList.length] })
          }

          const templateContent = templateParam.checkData.length > 0 ? (templateParam.checkData.filter(item => item.id === 'Temp-' + pdTimeTempData[i].batteryNo + (i + 1))[0] || {}) : {}
          const editContentIndex = checkData.findIndex(findItem => findItem.id == 'Temp-' + pdTimeTempData[i].batteryNo + (i + 1))
          const editContent = editContentIndex !== -1 ? checkData[editContentIndex] : {}

          let series = {
            id: 'Temp-' + pdTimeTempData[i].batteryNo + (i + 1),
            name: 'Temp-' + pdTimeTempData[i].batteryNo,
            soc: 'Temp-' + pdTimeTempData[i].batteryNo,
            sampling: 'lttb',
            type: "line",
            yAxisIndex: 1,
            barGap: 0,
            markPoint: {
              data: []
            },
            connectNulls: templateContent.connectNulls ?? (isCheck ? false : Boolean(Number(editContent.connectNulls))),
            symbol: templateContent.symbol ?? (isCheck ? "rect" : editContent.symbol),
            symbolSize: templateContent.symbolSize ?? (isCheck ? 0.2 : editContent.symbolSize),
            lineStyle: {
              width: templateContent.lineWidth ?? (isCheck ? 1 : editContent.lineWidth),
              type: templateContent.lineType ?? (isCheck ? "solid" : editContent.lineType),
              color: templateContent.lineColor ?? (
                isCheck
                  ? lineColorList[lineColorList.findIndex(v => v.name === 'Temp-' + pdTimeTempData[i].batteryNo)].color
                  : editContent.lineColor
              )
            },
            itemStyle: {
              color: templateContent.itemColor ?? (
                isCheck
                  ? lineColorList[lineColorList.findIndex(v => v.name === 'Temp-' + pdTimeTempData[i].batteryNo)].color
                  : editContent.itemColor
              )
            },
            emphasis: {
              focus: "series"
            },
            large: true,
            data: pdTimeTempData[i].data.map((mapItem, index) => { return { id: index, value: mapItem.value } }),
          }

          // 设置最大最小值
          if (templateContent.maxPoint || (!isCheck && editContent.maxPoint)) {
            series.markPoint.data.push({ type: "max", name: "Max" })
          }
          if (templateContent.minPoint || (!isCheck && editContent.minPoint)) {
            series.markPoint.data.push({ type: "min", name: "Min" })
          }

          seriesOriginal.push({
            id: 'Temp-' + pdTimeTempData[i].batteryNo + (i + 1),
            index: i + 1,
            name: 'Temp-' + pdTimeTempData[i].batteryNo,
            soc: 'Temp-' + pdTimeTempData[i].batteryNo,
            data: pdTimeTempData[i].data.map(v => v.value[1].toString()).filter(filterItem => filterItem !== ''),
            duplicateData: pdTimeTempData[i].data.map((mapItem, index) => { return { id: index, name: 'Temp-' + pdTimeTempData[i].batteryNo, value: mapItem.value } }),
            synchronization: templateContent.synchronization ?? (isCheck ? i : editContent.synchronization),
            maxPoint: templateContent.maxPoint ?? (isCheck ? false : editContent.maxPoint),
            minPoint: templateContent.minPoint ?? (isCheck ? false : editContent.minPoint),
            connectNulls: templateContent.connectNulls ?? false,
            symbol: templateContent.symbol ?? (isCheck ? "none" : editContent.symbol),
            symbolSize: templateContent.symbolSize ?? (isCheck ? 5 : editContent.symbolSize),
            itemColor: templateContent.itemColor ?? (
              isCheck
                ? lineColorList[lineColorList.findIndex(v => v.name === 'Temp-' + pdTimeTempData[i].batteryNo)].color
                : editContent.itemColor
            ),
            lineType: templateContent.lineType ?? (isCheck ? "solid" : editContent.lineType),
            lineWidth: templateContent.lineWidth ?? (isCheck ? 1 : editContent.lineWidth),
            lineColor: templateContent.lineColor ?? (
              isCheck
                ? lineColorList[lineColorList.findIndex(v => v.name === 'Temp-' + pdTimeTempData[i].batteryNo)].color
                : editContent.lineColor
            ),
          })

          // 原始值
          checkDataOriginal.push({
            id: 'Temp-' + pdTimeTempData[i].batteryNo + (i + 1),
            index: i + 1,
            soc: 'Temp-' + pdTimeTempData[i].batteryNo,
            name: 'Temp-' + pdTimeTempData[i].batteryNo,
            connectNulls: false,
            synchronization: i,
            maxPoint: false,
            minPoint: false,
            symbol: "none",
            symbolSize: 5,
            itemColor: lineColorList[lineColorList.findIndex(v => v.name === 'Temp-' + pdTimeTempData[i].batteryNo)].color,
            lineType: "solid",
            lineWidth: 1,
            lineColor: lineColorList[lineColorList.findIndex(v => v.name === 'Temp-' + pdTimeTempData[i].batteryNo)].color
          })

          duplicateDataOptions.push({
            id: pdTimeTempData[i].batteryNo,
            data: pdTimeTempData[i].data.map((mapItem, index) => { return { id: index, value: index, label: mapItem.value[1].toString() } })
          })

          seriesList.push(series)
          legendList.push('Temp-' + pdTimeTempData[i].batteryNo)
        }

        return [seriesOriginal, checkDataOriginal, seriesList, legendList, duplicateDataOptions]
      },

      // 生成【倍率-脉冲放电】图
      initPdTimeTempEcharts(
        legendData = {},
        checkData = [], //选中的数据
        axisData = {},
        titleData = {},
        gridData = {}

      ) {
        if (this.voltageTimeEchart) this.voltageTimeEchart.dispose();

        this.voltageTimeEchart = this.echarts.init(this.$refs.voltageTime, "walden", {
          renderer: 'webgl'
        })

        const templateParam = this.reportChartTemplateList['voltageTime'].templateParamJson
        const originalParam = this.reportChartTemplateList['voltageTime'].originalParamJson

        // 把模板的数据拼接进去
        const {
          titleData: newTitleData,
          gridData: newGridData,
          legendData: newLegendData,
          axisData: newAxisData,
          legend: newLegend
        } = this._getTemplateParams('voltageTime', titleData, gridData, legendData, axisData, legend);
        titleData = newTitleData;
        gridData = newGridData;
        legendData = newLegendData;
        axisData = newAxisData;

        // 处理结果数据
        const processingResult = this.initPdTimeTempData(checkData)

        // 原始图例  使用位置：在线编辑图表--图例数据
        this.originalLegent = _.cloneDeep(processingResult[3])

        // 原始图表数据
        this.originalseries = processingResult[0]

        // 在线编辑图标：数据标签-数据
        this.editData.duplicateDataOptions = processingResult[4]

        // 重置数据
        this.resetOriginal.checkData = processingResult[1]
        this.resetOriginal.series = _.cloneDeep(processingResult[1]) 

        // 图表数据
        let seriesList = processingResult[2]

        // 首次渲染,所有图例都显示
        if(!legendData.legendRevealList || templateParam.legendData.legendRevealList){
          const revealNum = this.handleRevealNum(520,this.originalLegent)
          this.editData.legendRevealList = templateParam.legendData.legendRevealList ?? _.cloneDeep(this.originalLegent).slice(0,revealNum)
        }

        //页面上有哪些图例 使用位置：在线编辑图表--选中的图例数据（默认全部选中）
        this.editData.legend = _.cloneDeep(this.originalLegent)
        this.editData.series = _.cloneDeep(this.originalseries)

        /* 图例排序 开始 */
        if (legendData.legendSort) {
          this.editData.legend = _.cloneDeep(legendData.legendSort) // 将页面上的图例数组按照用户设置的顺序排序
        }
        this.editData.legendSort = !legendData.legendSort ? _.cloneDeep(this.originalLegent) : _.cloneDeep(this.editData.legend)
        /* 图例排序 结束 */

        /* 图例变更名称 开始 */
        if (legendData.legendEditName) {
          legendData.legendEditName.forEach(v => {
            if (v.newName && !v.isReset) {
              let temIndex1 = this.originalLegent.findIndex(findItem => findItem == v.originName)
              this.originalLegent[temIndex1] = v.newName
              let temIndex2 = this.editData.legend.findIndex(findItem => findItem == v.originName)
              this.editData.legend[temIndex2] = v.newName
              this.editData.series.forEach(findItem => {
                findItem.name = findItem.name == v.originName ? v.newName : findItem.name
              })
              seriesList.forEach(findItem => {
                findItem.name = findItem.name == v.originName ? v.newName : findItem.name
              })
            }

            if (!v.newName && v.isReset) {
              v.previousName = ''
              v.isReset = false
            }
          })

          // 赋予修改后的图例修改名称数组
          this.editData.legendEditName = legendData.legendEditName
        }

        // 图例修改名称数组  使用位置：在线编辑图表--图例--名称 ,首次进入，将图例的值给图例修改名称数组
        // originName 原始值 newName 新值 previousName 上一个值（用于清空的情况下使用）isReset 是否重置（用于清空的时候使用）
        if (this.editData.legendEditName.length === 0) {
          this.editData.legendEditName = this.originalLegent.map(v => { return {id:v, originName: v, previousName: '', newName: '', isReset: false } })
        }
        /* 图例变更名称 结束 */

        /* 没选中的图例的移除处理 */
        if (legendData.legendEdit) {
          // 移除页面上的对应的图例
          for (let i = 0; i < this.editData.legend.length; i++) {
            if (!legendData.legendEdit.includes(this.editData.legend[i])) {
              this.editData.legend.splice(i, 1)
              i--
            }
          }

          // 移除页面上的对应的图例的图表数据
          for (let i = 0; i < seriesList.length; i++) {
            if (!legendData.legendEdit.includes(seriesList[i].name)) {
              seriesList.splice(i, 1)
              i--
            }
          }


          // 判断依据
          for (let i = 0; i < checkData.length; i++) {
            if (!legendData.legendEdit.includes(checkData[i].name)) {
              checkData.splice(i, 1)
              i--
            }
          }
        }

        /* 没选中的数据的移除处理 */
        // if (checkData.length > 0) {
        //   seriesList.forEach((v, index) => {
        //     const handIndex = checkData.findIndex(findItem => findItem.id == v.id)
        //     for (let i = 0; i < v.data.length; i++) {
        //       if (!checkData[handIndex].duplicateData.includes(v.data[i].id)) {
        //         v.data.splice(i, 1)
        //         i--
        //       }
        //     }
        //   })
        // }

        // 结合图例数据（线+图例都存在）和图例显隐（只存在线，剔除图例）
        const legend = []
        this.editData.legend.forEach(v => {
          const conditions1 = this.editData.legendRevealList.includes(v)
          if(conditions1){
            legend.push(v)
          }else{
            const conditions2 = this.editData.legendEditName.findIndex(findItem => findItem.newName == v)
            if(conditions2 > -1 && this.editData.legendRevealList.includes(this.editData.legendEditName[conditions2].originName)) legend.push(v)
          }
        })


        let options = {
          animationDuration: 2000,
          title: {
            text: titleData.chartTitle || 'Pulse Discharge@' + this.queryParam.reportBasic.temp + '℃/' + this.queryParam.reportBasic.current + 'A',
            left: "center",
            top: titleData.titleTop ||  8,
            fontSize: 18,
            fontWeight: 500,
            color: '#000'
          },
          grid: {
            show: true,
            top: gridData.gridTop ||  100,
            left: gridData.gridLeft || 80,
            right: gridData.gridRight || 70,
            bottom: gridData.gridBottom || 70,
            borderWidth: 0.5,
            borderColor: "#ccc"
          },
          textStyle: {
            fontFamily: "Times New Roman"
          },
          color: this.echartsColor,
          tooltip: {
            trigger: "axis",
            formatter: function (params) {
              var result = params[0].axisValue + "<br>" // 添加 x 轴的数值
              params.forEach(function (item, dataIndex) {
                result +=
                  item.marker +
                  item.seriesName +
                  '<div style="width:20px;display: inline-block;"></div><div style="width:150px;display: inline-block;">' +
                  "</div>" +
                  item.value[1] +
                  "<br>" // 添加每个系列的数值
              })
              return result
            }
          },
          legend: {
            position: "inside",
            top: 40,
            fontSize: 14,
            backgroundColor: legendData.legendBgColor || "#f5f5f5",
            itemWidth: legendData.legendWidth || 30,
            itemHeight: legendData.legendHeight || 5,
            itemGap: legendData.legendGap || 20,
            orient: legendData.legendOrient || 'horizontal',
            // position: "inside",
            top: legendData.legendTop || 35,
            left: legendData.legendLeft || 'center',
            // data: this.editData.legend,
            data: legend,
          },
          xAxis: [
            {
              name: titleData.XTitle || "Time",
              type: axisData.xType || "value",
              axisTick: { show: false },
              splitLine: {
                show: true, // 显示分隔线
                lineStyle: {
                  type: "solid",
                  width: 0.5
                }
              },
              axisLabel: {
                show: true,
                width: 0.5,
                fontSize: "15",
                color: "#000000"
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#ccc",
                  width: 0.5
                }
              },
              nameLocation: "middle", // 将名称放在轴线的中间位置
              nameGap: 35,
              nameTextStyle: {
                fontSize: 14,
                color: "#000000" // 可以根据需要调整字体大小
              }
            }
          ],
          yAxis: [
            {
              name: titleData.YTitle || "Voltage (V)",
              type: titleData.yType || "value",
              position: "left",
              nameGap: titleData.yTitleLetf ||  40,
              splitLine: {
                show: true, // 显示分隔线
                lineStyle: {
                  type: "solid",
                  width: 0.5
                }
              },
              axisTick: {
                show: true // 显示刻度
              },
              axisLabel: {
                show: true,
                width: 0.5,
                fontSize: "15",
                color: "#000000"
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#ccc",
                  width: 0.5
                }
              },
              nameLocation: "middle", // 将名称放在轴线的起始位置
              nameRotate: 90, // 旋转角度，使名称竖排
              nameTextStyle: {
                fontSize: 16, // 可以根据需要调整字体大小
                fontWeight: 500,
                color: "#000000"
              }
            },
            {
              name: titleData.YTitle2 || "Temperature(℃)",
              type: titleData.yType2 || "value",
              position: "right",
              nameGap: titleData.yTitleRight ||  40,
              splitLine: {
                show: true, // 显示分隔线
                lineStyle: {
                  type: "solid",
                  width: 0.5
                }
              },
              axisTick: {
                show: true // 显示刻度
              },
              axisLabel: {
                show: true,
                width: 0.5,
                textStyle: {
                  fontSize: "15",
                  color: "#000000"
                }
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#ccc",
                  width: 0.5
                }
              },
              nameLocation: "middle", // 将名称放在轴线的起始位置
              nameRotate: 90, // 旋转角度，使名称竖排
              nameTextStyle: {
                fontSize: 16, // 可以根据需要调整字体大小
                fontWeight: 500,
                color: "#000000"
              }
            }
          ],
          series: seriesList
        }

        // 传回给在线编辑图表，当前图标上有的点
        this.editData.duplicateCheckedList = seriesList.map(mapItem => mapItem.data.filter(filterItem => filterItem.value[1] !== '').map(mapItem2 => mapItem2.id))


        /* 处理坐标轴 */
        if (axisData.xMin) {
          options.xAxis[0].min = axisData.xMin
        }
        if (axisData.xMax) {
          options.xAxis[0].max = axisData.xMax
        }
        if (axisData.xInterval) {
          options.xAxis[0].interval = axisData.xInterval
        }
        if (axisData.yMin) {
          options.yAxis[0].min = axisData.yMin
        }
        if (axisData.yMax) {
          options.yAxis[0].max = axisData.yMax
        }
        if (axisData.yInterval) {
          options.yAxis[0].interval = axisData.yInterval
        }
        if (axisData.yMin2) {
          options.yAxis[1].min = axisData.yMin2
        }
        if (axisData.yMax2) {
          options.yAxis[1].max = axisData.yMax2
        }
        if (axisData.yInterval2) {
          options.yAxis[1].interval = axisData.yInterval2
        }

        // 坐标轴类型赋值
        this.editData.xType = options.xAxis[0].type
        this.editData.yType = options.yAxis[0].type
        this.editData.yType2 = options.yAxis[1].type

        this.voltageTimeEchart.clear()

        // this.voltageTimeEchart.getZr().off('click')
        // this.voltageTimeEchart.getZr().on('click', params => {
        //   const { target, topTarget } = params

        //   // Z 0:坐标轴
        //   if (topTarget?.z === 0 && this.drawerVisible) {
        //     this.$set(this.checkObj, 'editObj', 'axis')
        //   }
        //   // Z 3:线
        //   if (topTarget?.z === 3 && this.drawerVisible) {
        //     const axs = target.parent?.parent?.__ecComponentInfo?.index
        //     this.$set(this.checkObj, 'tag', axs)
        //     this.$set(this.checkObj, 'editObj', 'tag')
        //   }
        //   // Z 4:图例
        //   if (topTarget?.z === 4 && this.drawerVisible) {
        //     const axs = target.parent?.__legendDataIndex
        //     this.$set(this.checkObj, 'editObj', 'legend')
        //   }
        // });

        this.voltageTimeEchart.getZr().off('dblclick')
				this.voltageTimeEchart.getZr().on('dblclick', ({target, topTarget}) => {
				  this._handleDblclickEchart(target, topTarget, 'voltageTime')
				});
        this.voltageTimeEchart.setOption(options)

        // 如果坐标轴类型为数值轴，则计算出最大值最小值，以及间距
        if (options.xAxis[0].type === "value") {
          const xAxis = this.voltageTimeEchart.getModel().getComponent("xAxis").axis.scale
          this.editData.xMin = xAxis._extent[0]
          this.editData.xMax = xAxis._extent[1]
          this.editData.xInterval = xAxis._interval
        }

        if (options.yAxis[0].type === "value") {
          const yAxis = this.voltageTimeEchart.getModel().getComponent("yAxis").axis.scale
          this.editData.yMin = yAxis._extent[0]
          this.editData.yMax = yAxis._extent[1]
          this.editData.yInterval = yAxis._interval
        }

        if (options.yAxis[1].type === "value") {
          const yAxis2 = this.voltageTimeEchart.getModel().getComponent("yAxis", 1).axis.scale
          this.editData.yMin2 = yAxis2._extent[0]
          this.editData.yMax2 = yAxis2._extent[1]
          this.editData.yInterval2 = yAxis2._interval
        }

        if (this.isEditXNum === 0 && axisData.xType === "value") {
          this.isEditXNum++
          this.resetOriginal.xMin = this.editData.xMin
          this.resetOriginal.xMax = this.editData.xMax
          this.resetOriginal.xInterval = this.editData.xInterval
        }

        if (this.isEditYNum === 0 && axisData.yType === "value") {
          this.isEditYNum++
          this.resetOriginal.yMin = this.editData.yMin
          this.resetOriginal.yMax = this.editData.yMax
          this.resetOriginal.yInterval = this.editData.yInterval

          this.resetOriginal.yMin2 = this.editData.yMin2
          this.resetOriginal.yMax2 = this.editData.yMax2
          this.resetOriginal.yInterval2 = this.editData.yInterval2
        }

        if(originalParam?.xMax > 0){
					this.resetOriginal.xMin = originalParam.xMin
					this.resetOriginal.xMax = originalParam.xMax
					this.resetOriginal.xInterval = originalParam.xInterval
				}
        if(originalParam?.yMax > 0){
          this.resetOriginal.yMin = originalParam.yMin
          this.resetOriginal.yMax = originalParam.yMax
          this.resetOriginal.yInterval = originalParam.yInterval
        }
        if(originalParam?.yMax2 > 0){
          this.resetOriginal.yMin2 = originalParam.yMin2
          this.resetOriginal.yMax2 = originalParam.yMax2
          this.resetOriginal.yInterval2 = originalParam.yInterval2
        }
      },

      initStressTestData(checkData = []) {
        let stressTestData = _.cloneDeep(this.allDataJson.stressTestEchartsDataList)
        stressTestData = stressTestData.sort((item1,item2) => { return  Number(item1.batteryNo.replace("#","")) -  Number(item2.batteryNo.replace("#","")) })

        const templateParam = this.reportChartTemplateList['voltageTime'].templateParamJson
        let lineColorList = [] // 折线颜色
        const echartsColorList = this._getLegendList(stressTestData,'batteryNo').length <= 2 ? this.echartsColorShortList : this.echartsColorLongList
        const isCheck = checkData.length === 0

        let legendList = []
        let seriesList = []
        let seriesOriginal = []
        let checkDataOriginal = []
        let duplicateDataOptions = []

        for (let i = 0; i < stressTestData.length; i++) {
          // 设置折线的颜色
          const have = lineColorList.find(v => v.name === stressTestData[i].batteryNo)
          if (have == undefined) {
            lineColorList.push({ name: stressTestData[i].batteryNo, color: echartsColorList[lineColorList.length % echartsColorList.length] })
          }

          const templateContent = templateParam.checkData.length > 0 ? (templateParam.checkData.filter(item => item.id === stressTestData[i].batteryNo + (i + 1))[0] || {}) : {}
          const editContentIndex = checkData.findIndex(findItem => findItem.id == stressTestData[i].batteryNo + (i + 1))
          const editContent = editContentIndex !== -1 ? checkData[editContentIndex] : {}

          let series = {
            id: stressTestData[i].batteryNo + (i + 1),
            name: stressTestData[i].batteryNo,
            soc: stressTestData[i].batteryNo,
            sampling: 'lttb',
            type: "line",
            barGap: 0,
            markPoint: {
              data: []
            },
            connectNulls: templateContent.connectNulls ?? (isCheck ? false : Boolean(Number(editContent.connectNulls))),
            symbol: templateContent.symbol ?? (isCheck ? "rect" : editContent.symbol),
            symbolSize: templateContent.symbolSize ?? (isCheck ? 0.2 : editContent.symbolSize),
            lineStyle: {
              width: templateContent.lineWidth ?? (isCheck ? 1 : editContent.lineWidth),
              type: templateContent.lineType ?? (isCheck ? "solid" : editContent.lineType),
              color: templateContent.lineColor ?? (
                isCheck
                  ? lineColorList[lineColorList.findIndex(v => v.name === stressTestData[i].batteryNo)].color
                  : editContent.lineColor
              )
            },
            itemStyle: {
              color: templateContent.itemColor ?? (
                isCheck
                  ? lineColorList[lineColorList.findIndex(v => v.name === stressTestData[i].batteryNo)].color
                  : editContent.itemColor
              )
            },

            emphasis: {
              focus: "series"
            },
            large: true,
            data: stressTestData[i].data.map((mapItem, index) => { return { id: index, value: mapItem.value } }),
          }

          // 设置最大最小值
          if (templateContent.maxPoint || (!isCheck && editContent.maxPoint)) {
            series.markPoint.data.push({ type: "max", name: "Max" })
          }
          if (templateContent.minPoint || (!isCheck && editContent.minPoint)) {
            series.markPoint.data.push({ type: "min", name: "Min" })
          }

          seriesOriginal.push({
            id: stressTestData[i].batteryNo + (i + 1),
            index: i + 1,
            name: stressTestData[i].batteryNo,
            soc: stressTestData[i].batteryNo,
            data: stressTestData[i].data.map(v => v.value[1].toString()).filter(filterItem => filterItem !== ''),
            duplicateData: stressTestData[i].data.map((mapItem, index) => { return { id: index, name: stressTestData[i].batteryNo, value: mapItem.value } }),
            synchronization: templateContent.synchronization ?? (isCheck ? i : editContent.synchronization),
            maxPoint: templateContent.maxPoint ?? (isCheck ? false : editContent.maxPoint),
            minPoint: templateContent.minPoint ?? (isCheck ? false : editContent.minPoint),
            connectNulls: templateContent.connectNulls ?? false,
            symbol: templateContent.symbol ?? (isCheck ? "none" : editContent.symbol),
            symbolSize: templateContent.symbolSize ?? (isCheck ? 5 : editContent.symbolSize),
            itemColor: templateContent.itemColor ?? (
              isCheck
                ? lineColorList[lineColorList.findIndex(v => v.name === stressTestData[i].batteryNo)].color
                : editContent.itemColor
            ),
            lineType: templateContent.lineType ?? (isCheck ? "solid" : editContent.lineType),
            lineWidth: templateContent.lineWidth ?? (isCheck ? 1 : editContent.lineWidth),
            lineColor: templateContent.lineColor ?? (
              isCheck
                ? lineColorList[lineColorList.findIndex(v => v.name === stressTestData[i].batteryNo)].color
                : editContent.lineColor
            )
          })

          // 原始值
          checkDataOriginal.push({
            id: stressTestData[i].batteryNo + (i + 1),
            index: i + 1,
            soc: stressTestData[i].batteryNo,
            name: stressTestData[i].batteryNo,
            connectNulls: false,
            synchronization: i,
            maxPoint: false,
            minPoint: false,
            symbol: "none",
            symbolSize: 5,
            itemColor: lineColorList[lineColorList.findIndex(v => v.name === stressTestData[i].batteryNo)].color,
            lineType: "solid",
            lineWidth: 1,
            lineColor: lineColorList[lineColorList.findIndex(v => v.name === stressTestData[i].batteryNo)].color
          })

          duplicateDataOptions.push({
            id: stressTestData[i].batteryNo,
            data: stressTestData[i].data.map((mapItem, index) => { return { id: index, value: index, label: mapItem.value[1].toString() } })
          })

          seriesList.push(series)
          legendList.push(stressTestData[i].batteryNo)
        }

        return [seriesOriginal, checkDataOriginal, seriesList, legendList, duplicateDataOptions]
      },

      initStressTestEcharts(
        legendData = {},
        checkData = [], //选中的数据
        axisData = {},
        titleData = {},
        gridData = {}
      ) {
        if (this.voltageTimeEchart) this.voltageTimeEchart.dispose();

        this.voltageTimeEchart = this.echarts.init(this.$refs.voltageTime, "walden", {
          renderer: 'webgl'
        })

        const templateParam = this.reportChartTemplateList['voltageTime'].templateParamJson

        // 把模板的数据拼接进去
        const {
          titleData: newTitleData,
          gridData: newGridData,
          legendData: newLegendData,
          axisData: newAxisData,
          legend: newLegend
        } = this._getTemplateParams('voltageTime', titleData, gridData, legendData, axisData, legend);
        titleData = newTitleData;
        gridData = newGridData;
        legendData = newLegendData;
        axisData = newAxisData;

        // 处理结果数据
        const processingResult = this.initStressTestData(checkData)

        // 原始图例  使用位置：在线编辑图表--图例数据
        this.originalLegent = _.cloneDeep(processingResult[3])

        // 原始图表数据
        this.originalseries = processingResult[0]

        // 在线编辑图标：数据标签-数据
        this.editData.duplicateDataOptions = processingResult[4]

        // 重置数据
        this.resetOriginal.checkData = processingResult[1]
        this.resetOriginal.series = _.cloneDeep(processingResult[1]) 

        // 图表数据
        let seriesList = processingResult[2]

        // 首次渲染,所有图例都显示
        if(!legendData.legendRevealList || templateParam.legendData.legendRevealList){
          const revealNum = this.handleRevealNum(520,this.originalLegent)
          this.editData.legendRevealList = templateParam.legendData.legendRevealList ?? _.cloneDeep(this.originalLegent).slice(0,revealNum)
        }

        //页面上有哪些图例 使用位置：在线编辑图表--选中的图例数据（默认全部选中）
        this.editData.legend = _.cloneDeep(this.originalLegent)
        this.editData.series = _.cloneDeep(this.originalseries)

        /* 图例排序 开始 */
        if (legendData.legendSort) {
          this.editData.legend = _.cloneDeep(legendData.legendSort) // 将页面上的图例数组按照用户设置的顺序排序
        }
        this.editData.legendSort = !legendData.legendSort ? _.cloneDeep(this.originalLegent) : _.cloneDeep(this.editData.legend)
        /* 图例排序 结束 */

        /* 图例变更名称 开始 */
        if (legendData.legendEditName) {
          legendData.legendEditName.forEach(v => {
            if (v.newName && !v.isReset) {
              let temIndex1 = this.originalLegent.findIndex(findItem => findItem == v.originName)
              this.originalLegent[temIndex1] = v.newName
              let temIndex2 = this.editData.legend.findIndex(findItem => findItem == v.originName)
              this.editData.legend[temIndex2] = v.newName
              this.editData.series.forEach(findItem => {
                findItem.name = findItem.name == v.originName ? v.newName : findItem.name
              })
              seriesList.forEach(findItem => {
                findItem.name = findItem.name == v.originName ? v.newName : findItem.name
              })
            }

            if (!v.newName && v.isReset) {
              v.previousName = ''
              v.isReset = false
            }
          })

          // 赋予修改后的图例修改名称数组
          this.editData.legendEditName = legendData.legendEditName
        }

        // 图例修改名称数组  使用位置：在线编辑图表--图例--名称 ,首次进入，将图例的值给图例修改名称数组
        // originName 原始值 newName 新值 previousName 上一个值（用于清空的情况下使用）isReset 是否重置（用于清空的时候使用）
        if (this.editData.legendEditName.length === 0) {
          this.editData.legendEditName = this.originalLegent.map(v => { return {id:v, originName: v, previousName: '', newName: '', isReset: false } })
        }
        /* 图例变更名称 结束 */

        /* 没选中的图例的移除处理 */
        if (legendData.legendEdit) {
          // 移除页面上的对应的图例
          for (let i = 0; i < this.editData.legend.length; i++) {
            if (!legendData.legendEdit.includes(this.editData.legend[i])) {
              this.editData.legend.splice(i, 1)
              i--
            }
          }

          // 移除页面上的对应的图例的图表数据
          for (let i = 0; i < seriesList.length; i++) {
            if (!legendData.legendEdit.includes(seriesList[i].name)) {
              seriesList.splice(i, 1)
              i--
            }
          }


          // 判断依据
          for (let i = 0; i < checkData.length; i++) {
            if (!legendData.legendEdit.includes(checkData[i].name)) {
              checkData.splice(i, 1)
              i--
            }
          }
        }

        /* 没选中的数据的移除处理 */
        // if (checkData.length > 0) {
        //   seriesList.forEach((v, index) => {
        //     const handIndex = checkData.findIndex(findItem => findItem.id == v.id)
        //     for (let i = 0; i < v.data.length; i++) {
        //       if (!checkData[handIndex].duplicateData.includes(v.data[i].id)) {
        //         v.data.splice(i, 1)
        //         i--
        //       }
        //     }
        //   })
        // }

        // 结合图例数据（线+图例都存在）和图例显隐（只存在线，剔除图例）
        const legend = []
        this.editData.legend.forEach(v => {
          const conditions1 = this.editData.legendRevealList.includes(v)
          if(conditions1){
            legend.push(v)
          }else{
            const conditions2 = this.editData.legendEditName.findIndex(findItem => findItem.newName == v)
            if(conditions2 > -1 && this.editData.legendRevealList.includes(this.editData.legendEditName[conditions2].originName)) legend.push(v)
          }
        })

        let options = {
          animationDuration: 2000,
          title: {
            text: titleData.chartTitle || 'Stress Test',
            left: "center",
            top: titleData.titleTop ||  8,
            fontSize: 18,
            fontWeight: 500,
            color: '#000'
          },
          grid: {
            show: true,
            top: gridData.gridTop ||  100,
            left: gridData.gridLeft || 80,
            right: gridData.gridRight || 70,
            bottom: gridData.gridBottom || 70,
            borderWidth: 0.5,
            borderColor: "#ccc"
          },
          textStyle: {
            fontFamily: "Times New Roman"
          },
          color: this.echartsColor,
          tooltip: {
            trigger: "axis",
            formatter: function (params) {
              var result = params[0].axisValue + "<br>" // 添加 x 轴的数值
              params.forEach(function (item, dataIndex) {
                result +=
                  item.marker +
                  item.seriesName +
                  '<div style="width:20px;display: inline-block;"></div><div style="width:150px;display: inline-block;">' +
                  "</div>" +
                  item.value[1] +
                  "<br>" // 添加每个系列的数值
              })
              return result
            }
          },
          legend: {
            position: "inside",
            top: 40,
            fontSize: 14,
            color: "#000000",
            backgroundColor: legendData.legendBgColor || "#f5f5f5",
            itemWidth: legendData.legendWidth || 30,
            itemHeight: legendData.legendHeight || 5,
            itemGap: legendData.legendGap || 20,
            orient: legendData.legendOrient || 'horizontal',
            // position: "inside",
            top: legendData.legendTop || 35,
            left: legendData.legendLeft || 'center',
            // data: this.editData.legend,
            data: legend,
          },
          xAxis: [
            {
              name: titleData.XTitle || "Time/s",
              type: axisData.xType || "value",
              axisTick: { show: false },
              splitLine: {
                show: true, // 显示分隔线
                lineStyle: {
                  type: "solid",
                  width: 0.5
                }
              },
              axisLabel: {
                show: true,
                width: 0.5,
                textStyle: {
                  fontSize: "15",
                  color: "#000000"
                }
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#ccc",
                  width: 0.5
                }
              },
              nameLocation: "middle", // 将名称放在轴线的中间位置
              nameGap: 35,
              nameTextStyle: {
                fontSize: 14,
                color: "#000000" // 可以根据需要调整字体大小
              }
            }
          ],
          yAxis: [
            {
              name: titleData.YTitle || "Voltage (V)",
              type: axisData.yType || "value",
              position: "left",
              nameGap: titleData.yTitleLetf ||  40,
              splitLine: {
                show: true, // 显示分隔线
                lineStyle: {
                  type: "solid",
                  width: 0.5
                }
              },
              axisTick: {
                show: true // 显示刻度
              },
              axisLabel: {
                show: true,
                width: 0.5,
                textStyle: {
                  fontSize: "15",
                  color: "#000000"
                }
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#ccc",
                  width: 0.5
                }
              },
              nameLocation: "middle", // 将名称放在轴线的起始位置
              nameRotate: 90, // 旋转角度，使名称竖排
              nameTextStyle: {
                fontSize: 16, // 可以根据需要调整字体大小
                fontWeight: 500,
                color: "#000000"
              }
            }
          ],
          series: seriesList
        }

        // 传回给在线编辑图表，当前图标上有的点
        this.editData.duplicateCheckedList = seriesList.map(mapItem => mapItem.data.filter(filterItem => filterItem.value[1] !== '').map(mapItem2 => mapItem2.id))

        /* 处理坐标轴 */
        if (axisData.xMin) {
          options.xAxis[0].min = axisData.xMin
        }
        if (axisData.xMax) {
          options.xAxis[0].max = axisData.xMax
        }
        if (axisData.xInterval) {
          options.xAxis[0].interval = axisData.xInterval
        }
        if (axisData.yMin) {
          options.yAxis[0].min = axisData.yMin
        }
        if (axisData.yMax) {
          options.yAxis[0].max = axisData.yMax
        }
        if (axisData.yInterval) {
          options.yAxis[0].interval = axisData.yInterval
        }

        // 坐标轴类型赋值
        this.editData.xType = options.xAxis[0].type
        this.editData.yType = options.yAxis[0].type

        this.voltageTimeEchart.clear()

        // this.voltageTimeEchart.getZr().off('click')
        // this.voltageTimeEchart.getZr().on('click', params => {
        //   const { target, topTarget } = params

        //   // Z 0:坐标轴
        //   if (topTarget?.z === 0 && this.drawerVisible) {
        //     this.$set(this.checkObj, 'editObj', 'axis')
        //   }
        //   // Z 3:线
        //   if (topTarget?.z === 3 && this.drawerVisible) {
        //     const axs = target.parent?.parent?.__ecComponentInfo?.index
        //     this.$set(this.checkObj, 'tag', axs)
        //     this.$set(this.checkObj, 'editObj', 'tag')
        //   }
        //   // Z 4:图例
        //   if (topTarget?.z === 4 && this.drawerVisible) {
        //     const axs = target.parent?.__legendDataIndex
        //     this.$set(this.checkObj, 'editObj', 'legend')
        //   }
        // });
        this.voltageTimeEchart.getZr().off('dblclick')
				this.voltageTimeEchart.getZr().on('dblclick', ({target, topTarget}) => {
				  this._handleDblclickEchart(target, topTarget, 'voltageTime')
				});
        this.voltageTimeEchart.setOption(options)

        // 如果坐标轴类型为数值轴，则计算出最大值最小值，以及间距
        if (options.xAxis[0].type === "value") {
          const xAxis = this.voltageTimeEchart.getModel().getComponent("xAxis").axis.scale
          this.editData.xMin = xAxis._extent[0]
          this.editData.xMax = xAxis._extent[1]
          this.editData.xInterval = xAxis._interval
        }

        if (options.yAxis[0].type === "value") {
          const yAxis = this.voltageTimeEchart.getModel().getComponent("yAxis").axis.scale
          this.editData.yMin = yAxis._extent[0]
          this.editData.yMax = yAxis._extent[1]
          this.editData.yInterval = yAxis._interval
        }

        if (this.isEditXNum === 0 && axisData.xType === "value") {
          this.isEditXNum++
          this.resetOriginal.xMin = this.editData.xMin
          this.resetOriginal.xMax = this.editData.xMax
          this.resetOriginal.xInterval = this.editData.xInterval
        }

        if (this.isEditYNum === 0 && axisData.yType === "value") {
          this.isEditYNum++
          this.resetOriginal.yMin = this.editData.yMin
          this.resetOriginal.yMax = this.editData.yMax
          this.resetOriginal.yInterval = this.editData.yInterval
        }

        const originalParam = this.reportChartTemplateList['voltageTime'].originalParamJson
        if(originalParam?.xMax > 0){
					this.resetOriginal.xMin = originalParam.xMin
					this.resetOriginal.xMax = originalParam.xMax
					this.resetOriginal.xInterval = originalParam.xInterval
				}
        if(originalParam?.yMax > 0){
          this.resetOriginal.yMin = originalParam.yMin
          this.resetOriginal.yMax = originalParam.yMax
          this.resetOriginal.yInterval = originalParam.yInterval
        }
      },

      // 生成
      handleDrawerSubmit(value) {
        const legendData = {
          legendEdit: value.legendList,
          legendRevealList: value.legendRevealList,
          legendWidth: value.legendWidth,
          legendHeight: value.legendHeight,
          legendGap: value.legendGap,
          legendSort: value.legendSort, // 图例排序
          legendEditName: value.legendEditName,
          legendOrient: value.legendOrient,
          legendTop:value.legendTop,
          legendBgColor: value.legendBgColor,
          legendLeft: value.legendLeft,
        }

        const axisData = {
          xMin: value.xMin,
          xMax: value.xMax,
          xInterval: value.xInterval,
          xType: value.xType,

          yMin: value.yMin,
          yMax: value.yMax,
          yInterval: value.yInterval,
          yType: value.yType,

          yMin2: value.yMin2,
          yMax2: value.yMax2,
          yInterval2: value.yInterval2,
          yType2: value.yType2
        }

        const titleData = {
          chartTitle: value.chartTitle,
          XTitle: value.XTitle,
          YTitle: value.YTitle,
          YTitle2: value.YTitle2,
          titleTop: value.titleTop,
          yTitleLetf: value.yTitleLetf,
          yTitleRight:value.yTitleRight,
        }

        const gridData = {
          gridTop: value.gridTop,
          gridLeft: value.gridLeft,
          gridRight: value.gridRight,
          gridBottom: value.gridBottom,
        }

        // 赋值的数组
        const assignArr = ['chartTitle', 'XTitle', 'YTitle','titleTop','yTitleLetf','yTitleRight', 'YTitle2','legendRevealList', 'legendWidth', 'legendHeight', 'legendGap', 'legendEditName', 'legendSort','legendBgColor','legendOrient','legendTop','legendX', 'xMin', 'xMax', 'xInterval', 'xType',
          'yMin', 'yMax', 'yInterval', 'yType', 'yMin2', 'yMax2', 'yInterval2', 'yType2', 'synchronization','gridTop','gridLeft','gridRight','gridBottom',"targetEditObj"]

        this.editData.series = _.cloneDeep(value.checkData)

        for (let i = 0; i < assignArr.length; i++) {
          this.editData[assignArr[i]] = value[assignArr[i]]
        }

        // 处理模板参数
        this._handleTemplateParams(value)

        if (this.queryParam.reportType === '倍率-脉放map') {
          this.initTimeVoltageEcharts(
            legendData,
            value.checkData,
            axisData,
            titleData,
            gridData
          )
        } else if (this.queryParam.reportType === '倍率-脉冲放电') {
          this.initPdTimeTempEcharts(
            legendData,
            value.checkData,
            axisData,
            titleData,
            gridData
          )
        } else if (this.queryParam.reportType === '倍率-stress') {
          this.initStressTestEcharts(
            legendData,
            value.checkData,
            axisData,
            titleData,
            gridData
          )
        }


        this.$forceUpdate()



        // 确定目标图表类型
        const targetChart = this.queryParam.reportType === '倍率-脉放map' ? 'timeVolTempCurPic' : 'voltageTime'

        // 记录数据到后端
        let chartTemplateParams = {}
        if(!this.reportChartTemplateList[targetChart].templateId){
          chartTemplateParams = {
            targetChart: targetChart,
            templateName:'报告ID修改默认模板',
            reportId:this.$route.query.id,
            originalParamJson:JSON.stringify(this.resetOriginal),
            templateParamJson:JSON.stringify(this.reportChartTemplateList[targetChart].templateParamJson),
          }
          this.reportChartTemplateList[targetChart].originalParamJson = this.resetOriginal

          this.saveChartTemplate(chartTemplateParams)
        }else{
          chartTemplateParams = {
            id:this.reportChartTemplateList[targetChart].templateId,
            templateParamJson:JSON.stringify(this.reportChartTemplateList[targetChart].templateParamJson),
          }
          if(((this.isEditXNum === 1) || (this.isEditYNum === 1)) && this.reportChartTemplateList[targetChart].originalParamJson.xMax == 0 ){
            chartTemplateParams.originalParamJson = JSON.stringify(this.resetOriginal)
            this.reportChartTemplateList[targetChart].originalParamJson = this.resetOriginal
          }
          this.updateChartTemplate(chartTemplateParams)
        }
      },

      // 重置
      handleDrawerReset() {
        this.$confirm({
          title: '请确认是否重置图表?',
          content: '图表重置后，图表修改内容无法恢复',
          okText: '重置',
          cancelText: '取消',
          onOk:async () => {
            const targetChart = this.queryParam.reportType === '倍率-脉放map' ? 'timeVolTempCurPic' : 'voltageTime'
            await this.deleteChartTemplate({ reportId:this.$route.query.id,id:this.reportChartTemplateList[this.editObj].templateId,targetChart: targetChart },false)

            this.editData = this._getInitData(targetChart,'edit')
            this.editData.series = _.cloneDeep(this.originalseries)

            if (this.queryParam.reportType === '倍率-脉放map') {

              this.initTimeVoltageEcharts()
            } else if (this.queryParam.reportType === '倍率-脉冲放电') {

              this.initPdTimeTempEcharts()
            } else if (this.queryParam.reportType === '倍率-stress') {

              this.initStressTestEcharts()
            }

            this.drawerVisible = false
            this.$message.success("重置成功")
          },
          onCancel() {}
        });
      },

      // 重新选择模板
      async handleChangeTemplate(targetObj){
        await this.getChartTemplateRelationList(this.$route.query.id,[targetObj])
         this.editData = this._getInitData(targetObj,'edit')
          this.editData.series = _.cloneDeep(this.originalseries)
          if (this.queryParam.reportType === '倍率-脉放map') {
            this.initTimeVoltageEcharts()
          } else if (this.queryParam.reportType === '倍率-脉冲放电') {
            this.initPdTimeTempEcharts()
          } else if (this.queryParam.reportType === '倍率-stress') {
            this.initStressTestEcharts()
          }
          this.drawerVisible = false
      },

      _getInitData(targetObj,type = 'original'){
        const isEdit = type === 'edit'
        const templateParam = this.reportChartTemplateList[targetObj].templateParamJson

        const options = {
          titleTop: isEdit && templateParam.titleTop ? templateParam.titleTop : 8,
          yTitleLetf: isEdit && templateParam.yTitleLetf ? templateParam.yTitleLetf : 40,
          yTitleRight: isEdit && templateParam.yTitleRight ? templateParam.yTitleRight : 40,

          legendWidth: isEdit && templateParam.legendWidth ? templateParam.legendWidth : 30,
          legendHeight: isEdit && templateParam.legendHeight ? templateParam.legendHeight : 5,
          legendGap: isEdit && templateParam.legendGap ? templateParam.legendGap : 20,
          legendBgColor: isEdit && templateParam.legendBgColor ? templateParam.legendBgColor : '#f5f5f5',
          legendOrient: isEdit && templateParam.legendOrient ? templateParam.legendOrient : 'horizontal',
          legendBgColor:isEdit && templateParam.legendBgColor ? templateParam.legendBgColor : '#f5f5f5',
          legendTop: isEdit && templateParam.legendTop ? templateParam.legendTop : 35,

          gridTop: isEdit && templateParam.gridTop ? templateParam.gridTop : 100,
          gridLeft: isEdit && templateParam.gridLeft ? templateParam.gridLeft : 80,
          gridRight: isEdit && templateParam.gridRight ? templateParam.gridRight : 70,
          gridBottom: isEdit && templateParam.gridBottom ? templateParam.gridBottom : 70,

          xType: isEdit && templateParam.xType ? templateParam.xType : "value",
          xMin: isEdit && templateParam.xMin ? templateParam.xMin : 0,
          xMax:isEdit && templateParam.xMax ? templateParam.xMax :  0,
          xInterval:isEdit && templateParam.xInterval ? templateParam.xInterval :  0,

          yType:isEdit && templateParam.yType ? templateParam.yType :  "value",
          yMin:isEdit && templateParam.yMin ? templateParam.yMin :  0,
          yMax:isEdit && templateParam.yMax ? templateParam.yMax :  0,
          yInterval:isEdit && templateParam.yInterval ? templateParam.yInterval :  0,
        }

        if(type === 'edit'){
          options.series = []
          options.legend = []
          options.legendSort = []
          options.legendEditName = []
          options.allData = templateParam.allData ?? {}
          if(templateParam.legendLeft) options.legendLeft = templateParam.legendLeft
        }
        if(type === 'original'){
          options.checkData = []
        }

        let chartTitle = ''
        let XTitle = ''
        let YTitle = ''
        let YTitle2 = ''

        // 【倍率-脉放map】
        if(this.allDataJson.timeVoltageEchartsDataList){
          chartTitle = 'Over Load map@' + this.queryParam.reportBasic.current + 'A'
          XTitle = "Time"
          YTitle = "Temperature(℃)/Currentr(A)"
          YTitle2 = "Voltage (V)"
        }else if(this.allDataJson.pdTimeTempEchartsDataList){
          chartTitle = 'Pulse Discharge@' + this.queryParam.reportBasic.temp + '℃/' + this.queryParam.reportBasic.current + 'A'
          XTitle = "Time"
          YTitle = "Voltage (V)"
          YTitle2 = "Temperature(℃)"
        }else{
          chartTitle = 'Stress Test'
          XTitle = "Time/s"
          YTitle = "Voltage (V)"
        }

        options.chartTitle = isEdit && templateParam.chartTitle ? templateParam.chartTitle :  chartTitle
        options.XTitle = isEdit && templateParam.XTitle ? templateParam.XTitle :  XTitle
        options.YTitle = isEdit && templateParam.YTitle ? templateParam.YTitle :  YTitle
        if(YTitle2 !== '') options.YTitle2 = isEdit && templateParam.YTitle2 ? templateParam.YTitle2 :  YTitle2

        return options
      },

      onPageChange(pageNo, pageSize) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        getRateStressTestReport({ id: this.id, pageNo: this.pageNo, pageSize: this.pageSize })
          .then(res => {
            this.pageData = JSON.parse(res.data.allDataJson).tableList
            this.originData = this.pageData.records
            this.pageNo = this.pageData.current
            this.pageSize = this.pageData.size
            this.totalRows = this.pageData.total
            if (this.pageSizeOptions.indexOf(this.totalRows.toString()) === -1) {
              this.pageSizeOptions.push(this.totalRows.toString())
            }
          })
      },
      setFormula() {
        if (this.selectedRows.length < 1) {
          this.$message.warning("请选择行数据")
          return
        }
        if (this.selectedHeaderNameRowKeys.length < 1) {
          this.$message.warning("请选择列数据")
          return
        }
        this.setFormulaFlag = true
      },
      handleSetFormulaModalOk() {
        if (!this.symbol) {
          this.$message.warning("请选择计算符号")
          return
        }
        if (!this.symboledNumber) {
          this.$message.warning("请填写数字")
          return
        }
        let param = {
          row: this.selectedRowKeys,
          column: this.selectedHeaderNameRowKeys,
          symbol: this.symbol,
          symboledNumber: this.symboledNumber,
        }
        updateCRateData(param, this.id).then(res => {
          if (res.success) {
            this.$message.success('修改成功')
            this.init()
          } else {
            this.$message.error('修改失败：' + res.message)
          }
        })
      },
      handleSetFormulaModalCancel() {
        this.setFormulaFlag = false
      },
      onSelect(record, selected, selectedRows, nativeEvent) {
        this.selectedRows = selectedRows
        this.selectedRowKeys = selectedRows.map(item => item.id)
      },
      onSelectAll(selected, selectedRows, changeRows) {
        this.selectedRows = selectedRows
        this.selectedRowKeys = selectedRows.map(item => item.id)
      },
      checkHeaderName(event, dataIndex) {
        if (event.target.checked) {
          if (!this.selectedHeaderNameRowKeys.includes(dataIndex)) {
            this.selectedHeaderNameRowKeys.push(dataIndex)
          }
        } else {
          if (this.selectedHeaderNameRowKeys.includes(dataIndex)) {
            this.selectedHeaderNameRowKeys = this.selectedHeaderNameRowKeys.filter(item => item !== dataIndex);
          }
        }
      },
       // 下载
      handleDown(target){
        let title = target === 'timeVolTempCurPic' ? 'Over Load map@' + this.queryParam.reportBasic.current + 'A' :
        target === '倍率-stress' ? 'Stress Test' : 'Pulse Discharge@' + this.queryParam.reportBasic.temp + '℃/' + this.queryParam.reportBasic.current + 'A'

        const chartImageBase64 = this[`${target === 'timeVolTempCurPic' ?  target : 'voltageTime'}Echart`].getConnectedDataURL({
          pixelRatio: 3, // 设置图片的分辨率，这里设置为2表示导出的图片分辨率为原始分辨率的两倍
          backgroundColor: "#fff" // 设置图片的背景颜色
        })
        const blob = this.handleB64toBlob(chartImageBase64.replace("data:image/png;base64,", ""))
        if (window.navigator.msSaveOrOpenBlob) {
          //兼容IE10
          navigator.msSaveBlob(blob, title)
        } else {
          const href = URL.createObjectURL(blob) //创建新的URL表示指定的blob对象
          const a = document.createElement("a") //创建a标签
          a.style.display = "none"
          a.href = href // 指定下载链接
          a.download = title //指定下载文件名
          a.click() //触发下载
          URL.revokeObjectURL(a.href) //释放URL对象
        }
      },

      // updateData(){
      //   testReportUpdateDate(this.originData,this.id).then(res => {
      //     this.init()
      //   })
      // },

      // 处理模板值
      _handleTemplateParams(value){
        const isEdit = !!value.targetEditObj
        const targetChart = this.queryParam.reportType === '倍率-脉放map' ? 'timeVolTempCurPic' : 'voltageTime'
        const templateParam = this.reportChartTemplateList[targetChart].templateParamJson

        if(isEdit && !['legendList','legendEditName','legendSort','legendRevealList'].includes(value.targetEditObj)){
          if(value.targetEditIndex === undefined){
            templateParam[value.targetEditObj] = value[value.targetEditObj]
          }else if(value.targetEditIndex === 'all'){
            for(let i = 0; i < value.checkData.length ; i++){
              if(templateParam.checkData[i] === undefined) templateParam.checkData[i] = {}
              templateParam.checkData[i] = {
                ...templateParam.checkData[i],
                id:value.checkData[i].id,
                [value.targetEditObj]:value.checkData[i][value.targetEditObj]
              }
            }
            templateParam.allData[value.targetEditObj] = value.allData[value.targetEditObj]
          }else{
            let haveIndex = templateParam.checkData.findIndex(item => item.id === value.checkData[value.targetEditIndex].id)
            if(haveIndex === -1){
              templateParam.checkData.push({id:value.checkData[value.targetEditIndex].id})
              haveIndex = templateParam.checkData.length - 1
            }
            templateParam.checkData[haveIndex][value.targetEditObj] = value.checkData[value.targetEditIndex][value.targetEditObj]
          }
        }

        if(!isEdit){
          if (value.targetResetIndex === undefined || value.targetResetIndex === 'yDecimalNum') {
            delete templateParam[value.targetResetObj]

            // 如果是XY轴的类型重置，需同步去除最大最小值
            if(value.targetResetObj === 'xType' && value.xType === 'category'){
              delete templateParam.xMin
              delete templateParam.xMax
              delete templateParam.xInterval
            }
            if(value.targetResetObj === 'yType' && value.yType === 'value'){
              delete templateParam.yMin
              delete templateParam.yMax
              delete templateParam.yInterval
            }
          } else if (value.targetResetIndex === 'all') {
            for(let i = 0; i < templateParam.checkData.length ; i++){
              delete templateParam.checkData[i][value.targetResetObj]
            }
            delete templateParam.allData[value.targetResetObj]
          } else {
            let haveIndex = templateParam.checkData.findIndex(item => item.id === value.checkData[value.targetResetIndex].id)
            if(haveIndex !== -1){
              delete templateParam.checkData[haveIndex][value.targetResetObj]
            }
          }
        }

        // 图例-数据
        if(value.targetEditObj === 'legendList'){
          templateParam.legendData.legendIndeterminate = value.legendIndeterminate
          templateParam.legendData.checkAll = value.checkAll
          templateParam.legendData.legendList = value.legendList
          templateParam.legendData.legendOptions = templateParam.legendData.legendOptions ?? this.editData.legend
        }

        // 图例-名称
        if(value.targetEditObj === 'legendEditName'){
          templateParam.legendData.legendList = value.legendList
          templateParam.legendData.legendEditName = value.legendEditName
          templateParam.legendData.legendRevealList = value.legendRevealList
          templateParam.legendData.legendRevealOptions = value.legendRevealOptions

          const haveIndex =  templateParam.checkData.findIndex(item => item.id === value.checkData[value.targetEditIndex].id)
          if(haveIndex === -1){
            templateParam.checkData.push({id:value.checkData[value.targetEditIndex].id})
            templateParam.checkData[templateParam.checkData.length - 1].name = value.checkData[value.targetEditIndex].name
          }else{
            templateParam.checkData[haveIndex].name = value.checkData[value.targetEditIndex].name
          }
        }

        // 如果有图例-排序的修改
        if(value.targetEditObj === 'legendSort'){
          templateParam.legendData.legendSort = value.legendSort
        }

        if(value.targetEditObj === 'legendNameType'){
          templateParam.legendData.legendSort = value.legendSort
          templateParam.legendData.legendList = value.legendList
        }

        // 图例-显隐
        if(value.targetEditObj === 'legendRevealList'){
          templateParam.legendData.legendRevealIndeterminate = value.legendRevealIndeterminate
          templateParam.legendData.legendRevealcheckAll = value.legendRevealcheckAll
          templateParam.legendData.legendRevealList = value.legendRevealList
          templateParam.legendData.legendRevealOptions = value.legendRevealOptions
        }

        // 图例重置相关的处理
        if(['legendList','legendEditName','legendSort','legendRevealList'].includes(value.targetResetObj)){
          if(value.targetResetObj === 'legendList'){
            delete templateParam.legendData.legendList
            delete templateParam.legendData.checkAll
            delete templateParam.legendData.legendIndeterminate
          }
          if(value.targetResetObj === 'legendEditName'){
            delete templateParam.legendData.legendEditName
            delete templateParam.legendData.legendRevealList
          }
          if(value.targetResetObj === 'legendSort'){
            delete templateParam.legendData.legendSort
          }
          if(value.targetResetObj === 'legendRevealList'){
            delete templateParam.legendData.legendRevealList
          }
        }
      },

      // 获取模板参数
      _getTemplateParams(targetObj,titleData,gridData,legendData,axisData,legend){
        const templateParam = this.reportChartTemplateList[targetObj].templateParamJson

        const titleList = ['chartTitle', 'XTitle', 'YTitle', 'YTitle2', 'titleTop', 'yTitleLetf', 'yTitleRight']
        titleList.forEach(item => {
          titleData[item] = templateParam[item] ?? titleData[item]
        })

        const legendList = ['legendBgColor','legendOrient','legendTop', 'legendLeft','legendWidth', 'legendHeight', 'legendGap','legendX']
        legendList.forEach(item => {
          legendData[item] = templateParam[item] ?? legendData[item]
        })

        const gridList = ['gridTop', 'gridLeft', 'gridRight', 'gridBottom']
        gridList.forEach(item => {
          gridData[item] = templateParam[item] ?? gridData[item]
        })

        const axisList = ['xType','xMin', 'xMax', 'xInterval', 'yType', 'yMin', 'yMax', 'yInterval', 'yType2', 'yMin2', 'yMax2', 'yInterval2']
        axisList.forEach(item => {
          axisData[item] = templateParam[item] ?? axisData[item]
        })
        // 如果有图例-数据的修改
        if(templateParam.legendData.legendList){
          legendData.legendEdit = templateParam.legendData.legendList
          if(templateParam.legendData.checkAll !== undefined) this.editData.checkAll = templateParam.legendData.checkAll
          if(templateParam.legendData.legendIndeterminate !== undefined) this.editData.legendIndeterminate = templateParam.legendData.legendIndeterminate
        }

        // 如果有图例-名称的修改
        if(templateParam.legendData.legendEditName){
          legendData.legendEditName = templateParam.legendData.legendEditName
          legendData.legendRevealList = templateParam.legendData.legendRevealList
        }

        // 如果有图例-排序的修改
        if(templateParam.legendData.legendSort){
          legendData.legendSort = templateParam.legendData.legendSort
        }

        // 如果有图例-显隐的修改
        if(templateParam.legendData.legendRevealList){
          legendData.legendRevealList = templateParam.legendData.legendRevealList
          if(templateParam.legendData.legendRevealcheckAll !== undefined) this.editData.legendRevealcheckAll = templateParam.legendData.legendRevealcheckAll
          if(templateParam.legendData.legendRevealIndeterminate !== undefined) this.editData.legendRevealIndeterminate = templateParam.legendData.legendRevealIndeterminate
          if(templateParam.legendData.legendRevealOptions !== undefined) this.editData.legendRevealOptions = templateParam.legendData.legendRevealOptions
        }

        return { titleData, gridData, legendData, axisData, legend }
      },
    }
  }
</script>
<style lang="less" scoped>
  :root {
    --height: 600px;
  }

  /deep/ .ant-table-thead>tr>th {
    padding: 5px !important;
    font-size: 13px !important;
  }

  /deep/ .ant-table-tbody>tr>td {
    padding: 0px !important;
    height: 32px !important;
    font-size: 12px !important;
  }

  /deep/ .ant-calendar-picker-icon {
    display: none;
  }

  /deep/ .ant-calendar-picker-input.ant-input {
    color: black;
    font-size: 12px;
    border: 0;
    text-align: center;
    padding: 0;
  }

  .red {
    background-color: #ed0000;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .yellow {
    background-color: #ffc000;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .grey {
    background-color: rgba(223, 223, 223, 0.25);
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .ant-modal-body {
    padding: 0;
  }

  /deep/ .ant-btn>i,
  /deep/ .ant-btn>span {
    display: flex;
    justify-content: center;
  }

  /deep/ .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
    color: #fff;
    background: #1890ff;
  }

  .green {
    background-color: #58a55c;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  /deep/ #table1>div>div>div>div>div>div>table>thead {
    height: 64px;
  }

  /deep/ #table1>.ant-table-wrapper>div>div>ul {
    display: none;
  }

  /deep/ .ant-table-pagination.ant-pagination {
    float: right;
    margin: 0;
  }

  .float {
    // width: 36%;
    // float: left;
    // margin-right: 10px;
    // text-align: center;
    padding-bottom: 10px;
  }

  .float1 {
    width: 12%;
    float: left;
    margin-right: 10px;
    text-align: center;
  }

  /deep/ .ant-checkbox-group-item {
    display: block;
    width: 100%;
    text-align: left;
  }

  .title {
    font-size: large;
    margin-bottom: 20px;
  }

  .numTitle {
    font-size: xx-large;
  }

  /deep/ .ant-table-footer {
    padding: 0;
  }

  /deep/ .ant-table-row-expand-icon {
    margin-right: 0px;
  }

  .wrapper {
    background: #fff;
    height: 100vh;
    padding: 6px 16px 16px;
    margin: 0 0 0 -40px;
    background-color: #f0f2f5;

    overflow: scroll;
  }

  .head_title {
    color: #333;
    padding: 10px 0;
    font-size: 20px;
    font-weight: 600;
    /* font-family: 'Times New Roman'; */
  }

  .head_title::before {
    width: 8px;
    background: #1890ff;
    margin-right: 8px;
    content: "\00a0"; //填充空格
  }

  .export-btn {
    position: fixed;
    bottom: 10px;
    right: 10px;
    width: 15%;
  }

  .all-wrapper {
    padding: 0 0 10px;
    display: flex;
    justify-content: space-between;
  }

  .btn-wrap {
    text-align: right;
  }

  .example-icon {
    width: 20px;
    height: 20px;
    color: #1890ff;
    vertical-align: middle;
    margin-left: 3px;
    margin-top: 3px;
  }

  // 通用
  .mt10 {
    margin-top: 10px;
  }

  .mr5 {
    margin-right: 5px;
  }

  .mb5 {
    margin-bottom: 5px;
  }

  .mr10 {
    margin-right: 10px;
  }

  .title-line {
    width: 8px;
    height: 30px;
    background: #1890ff;
    border-radius: 2px;
    margin-right: 8px;
    content: "\00a0"; //填充空格

    position: absolute;
    top: 8px;
    left: -4px;
  }

  .flex-column {
    display: flex;
    flex-direction: column;
  }

  .flex-sb-center-row {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .strong {
    // background-image: url("../../../../assets/icons/Rectangle.png");
    background-size: 100% 100%;
    width: 210px;
    height: 25px;
    color: #333;
    display: flex;
    align-items: center;
    padding: 5px;
  }

  .block {
    height: fit-content;
    padding: 10px;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
    position: relative;
  }

  .left-content {
    width: 618px;
    margin-right: 10px;
  }

  .right-content {
    width: calc(100% - 628px);
  }

  .right-content .all-checkbox {
    padding-bottom: 5px;
    margin-bottom: 5px;
    border-bottom: 1px solid #e9e9e9;
  }

  .normal-btn {
    padding: 5px 10px;
    color: #fff;
    background-color: #1890ff;
    letter-spacing: 2px;
    cursor: pointer;
  }

  .footer-btn {
    width: 100%;
    height: 32px;
    border: 1px solid #e8e8e8;
    background: #fff;
    color: #999;
    font-size: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }

  .footer-btn:hover {
    color: #1890ff;
  }

  // 组件
  /deep/ .ant-steps {
    padding: 15px 50px;
  }

  /deep/ .left-content .ant-table-body {
    /* min-height: calc(100vh - 460px); */
    border: 1px solid #e8e8e8;
    overflow: auto;
  }

  /deep/ .right-content .ant-table-body {
    /* min-height: var(--height) !important; */
    border: 1px solid #e8e8e8;
    overflow: auto;
  }

  /deep/ .all-wrapper .ant-table-thead {
    position: sticky;
    top: 0;
    z-index: 2;
  }

  /deep/ .all-wrapper .ant-table-placeholder {
    border: none !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 0;
  }

  /deep/ .right-content .ant-empty-normal {
    margin: -2px 0;
  }

  /deep/ .ant-empty-image {
    display: none;
  }

  /deep/ .right-content .ant-input {
    border: none;
  }

  /deep/ .ant-checkbox-group {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
  }

  /deep/ .ant-checkbox-group-item {
    font-size: 12px;
    width: 23%;
  }

  /deep/ .ant-radio-inner {
    top: 1px;
    left: 1px;
  }

  /deep/ .ant-table-body::-webkit-scrollbar {
    height: 10px;
    width: 5px;
  }

  /deep/ .ant-table-body::-webkit-scrollbar-thumb {
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;

    background: #dddbdb;
  }

  /deep/ .ant-table-body::-webkit-scrollbar-track {
    -webkit-box-shadow: 0;
    border-radius: 0;
    background: #f1f1f1;
  }

  /deep/ .ant-select-selection__rendered {
    margin-right: 0px;
  }

  /deep/ .ant-form-item {
    margin-bottom: 0;
  }

  /deep/ .ant-popover-buttons {
    display: flex !important;
    flex-direction: column !important;
    margin-bottom: 15px;
  }

  // /deep/.ant-popover-buttons .ant-btn-sm{
  // 	margin-bottom: 5px;
  // 	background-color: #1890ff;
  // 	color: #fff;
  // }

  .tips {
    color: #1890ff;
  }

  .button-tips {
    display: flex;
    flex-direction: column;
  }

  /deep/ .ant-select-selection-selected-value {
    width: 80px;
  }


  /deep/.ant-table-fixed-header .ant-table-scroll .ant-table-header {
    margin-bottom: unset!important;
    overflow: hidden!important;
  }


</style>