<template>
  <a-modal
    title="填写成品代码"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          style="display: none;"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-input v-decorator="['id']" />
        </a-form-item>

        <a-form-item
          style="display: none;"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-input v-decorator="['bomId']" />
        </a-form-item>


        <a-form-item
          style="display: none;"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-input v-decorator="['bomPackId']" />
        </a-form-item>

        <a-form-item
          label=""
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
            <!-- <v-selectpage title="成品代码" placeholder="选择成品代码" ref="sp" @values="singleValues" v-model="bomCode" :page-size="6" :data="parts" key-field="sapNumber" show-field="sapNumber" :tb-columns="columns" >
  		      </v-selectpage> -->

            <a-dropdown v-model="dropdownvisible" placement="bottomCenter" :trigger="['click']">
              <a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">{{bomCode ? bomCode+'-规格：['+sapDec+']' : '成品BOM代码'}}<a-icon type="down" /></a-button>
              <a-menu slot="overlay">
                  <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:800px">
                    <a-input-search v-model="queryParam.partNumber" placeholder="搜索成品BOM代码" @change="onSearch"/>
                      <s-table
                          style="width:100%"
                          ref="table"
                          :rowKey="(record) => record.sapNumber"
                          :columns="vcolumns"
                          :data="loadData"
                          :customRow="customRow"
                          :scroll="{x: 900,y:200}"
                          >
                      </s-table>
                  </a-spin>
              </a-menu>
            </a-dropdown>
           
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import {bomEndSave} from "@/api/modular/system/bomManage"
import {
		getPartList
	} from "@/api/modular/system/partManage"
/* import { SelectPage } from 'v-selectpage' */
import {
		STable
	} from '@/components'
  export default {
    components: {
      STable
	  //'v-selectpage': SelectPage
    },
    /* props:{
      parts:{
        type:Array,
        default:()=>[]
      }
    }, */
    data () {
      return {
        sapDec:'',
        dropdownvisible:false,
        queryParam:{
				},
        bomCode:'',
        loading:false,
        vcolumns:[
          {
            title:'物料代码',
            dataIndex:'sapNumber'
          },
          {
            title:'物料',
            dataIndex:'partName'
          },
          {
						title: '物料规格',
						dataIndex: 'partDescription',
            width:700
					},
        ],
        loadData: parameter => {
					parameter = { ...parameter,
						...{
							flag: 1
						}
					}
					return getPartList(Object.assign(parameter, this.queryParam)).then((res) => {
						return res.data
					})
				},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 24 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    created(){
    },
    methods: {
      onSearch(e){
				this.$refs.table.refresh()
			},
			customRow(row,index){
				return{
					on:{
						click :()=>{
							this.bomCode = row.sapNumber
              this.bomPartName = row.partName
              this.sapDec = row.partDescription
              this.dropdownvisible = false
						}
					}
				}
			},
      /* singleValues (data) {
        if (data[0]) {
          this.bomPartName = data[0].partName
        }
      }, */
      edit (record) {
          setTimeout(() => {
             this.form.setFieldsValue(
              {
                id: record.id,
                bomId:record.bomId,
                bomPackId:record.bomPackId
              }
            )
            this.record = record
            this.bomPartName = this.record.bomPartName
            this.bomCode = this.record.bomCode
          }, 100)
          this.visible = true
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            let $params = {...values,bomCode:this.bomCode,bomPartName:this.bomPartName}
            bomEndSave($params).then((res) => {
              if (res.success) {
                this.$message.success('保存成功')
                this.visible = false
                this.confirmLoading = false
                this.record.bomPartName = this.bomPartName
                this.record.bomCode = this.bomCode
                this.form.resetFields()
              } else {
                this.$message.error('保存失败：' + res.message)
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        //this.$refs.sp.remove()
        this.bomCode = null
        this.sapDec = ''
        this.visible = false
        this.form.resetFields()
      }

    }
  }
</script>
