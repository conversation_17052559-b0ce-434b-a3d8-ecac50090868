/**
 * 系统应用
 *
 * <AUTHOR>
 * @date 2020年4月23日12:10:57
 */
 import { axios } from '@/utils/request'

 /**
  *
  * <AUTHOR>
  * @date 2020年7月9日15:05:01
  */
 export function getBomList (parameter) {
   return axios({
     url: '/sysBom/list',
     method: 'get',
     params: parameter
   })
 }
 /**
  *
  * <AUTHOR>
  * @date 2020年7月9日15:05:01
  */
 export function endBomPreview (parameter) {
   return axios({
     url: '/sysBom/endBomPreview',
     method: 'post',
     data: parameter
   })
 }
/**
  *
  * <AUTHOR>
  * @date 2020年7月9日15:05:01
  */
 export function copyBom (parameter) {
   return axios({
     url: '/sysBom/copy',
     method: 'post',
     data: parameter
   })
 }



 export function retrySap (parameter) {
   return axios({
     url: '/sysBom/retry2sap',
     method: 'post',
     data: parameter
   })
 }

/**
  *
  */
 export function bomPushQuery (parameter) {
   return axios({
     url: '/sysBomPush/query',
     method: 'post',
     data: parameter
   })
 }


/**
  *
  */
 export function bomPushList (parameter) {
   return axios({
     url: '/sysBomPush/list',
     method: 'get',
     params: parameter
   })
 }


 export function pdfUpdate (parameter) {
   return axios({
     url: '/sysBom/pdfUpdate',
     method: 'post',
     data: parameter
   })
 }

 export function pdfUpdateAndCode (parameter) {
   return axios({
     url: '/sysBom/pdfUpdateAndCode',
     method: 'post',
     data: parameter
   })
 }

 export function getBom (parameter) {
    return axios({
      url: '/sysBom/get',
      method: 'get',
      params: parameter
    })
  }

  export function getBomError (parameter) {
    return axios({
      url: '/sysBom/error',
      method: 'get',
      params: parameter
    })
  }

  export function sysBomSave (parameter) {
    return axios({
      url: '/sysBom/save',
      method: 'post',
      data: parameter
    })
  }

  export function sysBomDeletelines (parameter) {
    return axios({
      url: '/sysBomLine/deletelines',
      method: 'post',
      data: parameter
    })
  }

  export function sysBomAddWerks (parameter) {
    return axios({
      url: '/sysBomLine/addlines',
      method: 'post',
      data: parameter
    })
  }

  export function sysBomAdd (parameter) {
    return axios({
      url: '/sysBom/add',
      method: 'post',
      data: parameter
    })
  }


  export function sysAddBom (parameter) {
    return axios({
      url: '/sysBom/addBom',
      method: 'post',
      data: parameter
    })
  }

  export function sysBomDel (parameter) {
    return axios({
      url: '/sysBom/del',
      method: 'post',
      data: parameter
    })
  }

  export function sysBomCommit (parameter) {
    return axios({
      url: '/sysBom/commit',
      method: 'post',
      data: parameter
    })
  }

  export function sysBomUpgrade (parameter) {
    return axios({
      url: '/sysBom/upgrade',
      method: 'post',
      data: parameter
    })
  }

  export function sysBomUpdate (parameter) {
    return axios({
      url: '/sysBom/update',
      method: 'post',
      data: parameter
    })
  }

  export function getwerks (parameter) {
    return axios({
      url: '/sysBom/getwerks',
      method: 'get',
      params: parameter
    })
  }

  export function getwerklines (parameter) {
    return axios({
      url: '/sysBom/getWerkLines',
      method: 'get',
      params: parameter
    })
  }

  export function getbomlines (parameter) {
    return axios({
      url: '/sysBom/bomlines',
      method: 'get',
      params: parameter
    })
  }

  ///sysBomEnd/packLineIds
  export function getBomPackLineIds (parameter) {
    return axios({
      url: '/sysBomEnd/packLineIds',
      method: 'get',
      params: parameter
    })
  }
  // /sysBomEnd/bomLineIds
  export function getBomLineIds (parameter) {
    return axios({
      url: '/sysBomEnd/bomLineIds',
      method: 'get',
      params: parameter
    })
  }
  
  export function getBomHistory (parameter) {
    return axios({
      url: '/sysBom/history',
      method: 'get',
      params: parameter
    })
  }

  export function getBomLastHistory (parameter) {
    return axios({
      url: '/sysBom/lasthistory',
      method: 'get',
      params: parameter
    })
  }

  export function getBomPage (parameter) {
    return axios({
      url: '/sysBom/page',
      method: 'post',
      data: parameter
    })
  }

  export function getBomEndPage (parameter) {
    return axios({
      url: '/sysBomEnd/page',
      method: 'post',
      data: parameter
    })
  }

  export function getBomEndBomPage (parameter) {
    return axios({
      url: '/sysBomEnd/bompage',
      method: 'post',
      data: parameter
    })
  }
  
  export function getBomEnd (parameter) {
    return axios({
      url: '/sysBomEnd/list',
      method: 'get',
      params: parameter
    })
  }

  export function getBomEndError (parameter) {
    return axios({
      url: '/sysBomEnd/get',
      method: 'get',
      params: parameter
    })
  }
  ///sysBomEnd/relate
  export function bomEndSave (parameter) {
    return axios({
      url: '/sysBomEnd/save',
      method: 'post',
      data: parameter
    })
  }

  export function bomEndSetLines (parameter) {
    return axios({
      url: '/sysBomEnd/setbomlines',
      method: 'post',
      data: parameter
    })
  }

  export function bomEndDelLines (parameter) {
    return axios({
      url: '/sysBomEnd/delBomLines',
      method: 'post',
      data: parameter
    })
  }

  export function bomEndRelate (parameter) {
    return axios({
      url: '/sysBomEnd/relate',
      method: 'post',
      data: parameter
    })
  }

  export function delBomEnd (parameter) {
    return axios({
      url: '/sysBomEnd/del',
      method: 'post',
      data: parameter
    })
  }
  
  export function sapImportVerify (parameter) {
    return axios({
      url: '/sysBom/sapImportVerify',
      method: 'post',
      data: parameter
    })
  }

  export function updatePLMBomData (parameter) {
    return axios({
      url: '/sysBom/updateBomData',
      method: 'get',
      params: parameter
    })
  }

  export function getWerksOptions (parameter) {
    return axios({
      url: '/sysBom/getWerksOptions',
      method: 'get',
      params: parameter
    })
  }

  export function sapImport (parameter) {
    return axios({
      url: '/sysBom/sapImport',
      method: 'post',
      data: parameter
    })
  }


  export function sapVerify (parameter) {
    return axios({
      url: '/sysBom/sapVerify',
      method: 'get',
      params: parameter
    })
  }

  export function getSapVerifyPage (parameter) {
    return axios({
      url: '/bomSapVerify/page',
      method: 'get',
      params: parameter
    })
  }


  export function sapImportBomEnd (parameter) {
    return axios({
      url: '/sysBomEnd/sapimport',
      method: 'post',
      data: parameter
    })
  }


  export function adminConfirm (parameter) {
    return axios({
      url: '/sysBom/confirm',
      method: 'get',
      params: parameter
    })
  }