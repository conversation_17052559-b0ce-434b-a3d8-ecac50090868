<template>
  <div style="background:#fff;overflow: hidden;">
    <div class="tab-title">
      <div class="tab-head">
        <div @click="gotoManager">方案设计</div>
        <div @click="gotoBom">电芯BOM设计</div>
        <div class="active">MI设计</div>
      </div>
      <a-button v-if="show" style="margin-right: 8px;z-index: 1;float: right;height: 20px;font-size: 12px;margin-bottom: 5px;position: relative;margin-top: 5px;"
                 @click="close()" >返回</a-button>
    </div>
    <div v-if="!show">
      <div class="sub-title">

        <div style="padding: 15px 0px 0px 20px;float: left;font-size: 15px;">
          上传规则说明：<br/>
          1.上传文件必须为已审核的PDF格式文件<br/>
          2.单次仅限上传一个文件<br/>
          3.多个文件可分批次上传<br/>
          4.文件更新需重新上传（建议删除已有文件）<br/>
        </div>


        <a-upload
          :beforeUpload="beforeFileUpload"
          accept=".pdf" :showUploadList="false" name="file" :data="{batteryId:batteryId}" action="/api/batteryDesignMi/upload"
                  :multiple="false" @change="handleChange"
                  :headers="headers">
          <a-button type="primary" v-if="isOwn == 1 && (design.manCheckStatus == 0 || design.manCheckStatus == 20 || design.manCheckStatus == 80 )"
                    >本地上传</a-button>
        </a-upload>


        <a-table
          :columns="columns"
          :data-source="dataSource"
          :row-key="(record) => record.id"
          :pagination="false"
          bordered
          style="padding: 5px 10px"
        >

         <span slot="action" slot-scope="text, record" style="display: contents;">
                        <a @click="openPdf(record.fileId)">查看</a>
                        <a-divider type="vertical" />
                        <a @click="downloadPdf(record.fileId)">下载</a>
                        <a-divider type="vertical" v-if="isOwn == 1 && (design.manCheckStatus == 0 || design.manCheckStatus == 20 || design.manCheckStatus == 80 )"
                                  />

                        <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => designDelete(record.id)"
                                      v-if="isOwn == 1 && (design.manCheckStatus == 0 || design.manCheckStatus == 20 || design.manCheckStatus == 80 )"
                                     >
                            <a >删除</a>
                        </a-popconfirm>
                  </span>


          <div slot="checkMan"  slot-scope="text,record">

            <input

              :value="text"  @change="updateData($event,record,'checkMan')" />

          </div>

          <div slot="remark"  slot-scope="text,record">

            <input

              :value="text"  @change="updateData($event,record,'remark')" />

          </div>

          <div slot="checkStatus"  slot-scope="text,record">

            <a-select style="width: 100%" :value="text"
                      :disabled="isOwn == 0 || design.manCheckStatus == 20 || design.manCheckStatus == 70"
                      @change="updateSelectData($event,record)">
              <a-select-option :value="parseInt(1)" >
                是
              </a-select-option>
              <a-select-option :value="parseInt(0)" >
                否
              </a-select-option>

            </a-select>

          </div>


        </a-table>





      </div>
    </div>
    <iframe :src="pdfUrl+'#view=FitH,top'" width="100%" height="100%" v-if="show"></iframe>
  </div>
</template>


<script>

  import { batteryDesignMiList,batteryDesignMiUpdate} from '@/api/modular/system/batteryDesignSorManage'
  import { getBatteryDesign} from '@/api/modular/system/batterydesignManage';
  import {  sysFileInfoDownload } from '@/api/modular/system/fileManage'
  import {
        mapActions,
        mapGetters
    } from 'vuex'
  import { ALL_APPS_MENU } from '@/store/mutation-types'
    import Vue from 'vue'
  export default {


    components: {

    },
    data () {
      return {
        isOwn:0,
        design:null,
        headers: {
          Authorization: 'Bearer ' + Vue.ls.get('Access-Token'),
        },
        pdfUrl:'/sysFileInfo/previewPdf?id=',
        dataSource:[],
        isOwn:0,
        design:null,
        show:false,
        batteryId:null,
        // 表头

        columns: [
              {
                title: '序号',
                dataIndex: 'index',
                align: 'center',
                width: 30,
                customRender: (text, record, index) => index+1
              },
              {
                title: '文件名称',
                dataIndex: 'name',
                align: 'center',
                width:120,
              }, {
                title: '是否已审核',
                dataIndex: 'checkStatus',
            scopedSlots: { customRender: 'checkStatus' },
                width:50,
            align: 'center',
              },

              {
                title: '已审核人',
                dataIndex: 'checkMan',
                align: 'center',
                width:80,
                scopedSlots: { customRender: 'checkMan' },
              },
              {
                title: '备注说明',
                dataIndex: 'remark',
                align: 'center',
                width:100,
                scopedSlots: { customRender: 'remark' },
              },

              {
                title: '上传人',
                dataIndex: 'createName',
                align: 'center',
                width:80,

              },

              {
                title: '上传时间',
                dataIndex: 'createTime',
                align: 'center',
                width:120,

              },

          {
            title: '操作',
            dataIndex: 'action',
            width: 120,
            align: 'center',
            scopedSlots: {
              customRender: 'action'
            }
          }],

      }
    },

    computed: {
            ...mapGetters(['userInfo'])
        },




    mounted() {

      this.batteryId = this.$route.query.batteryId
      this.getList()
      
    },

    created(){
      
    },

    methods: {
      ...mapActions(['MenuChange']),

      close(){
        this.show = false
      },

      beforeFileUpload(file, fileList) {


        var fileNames = file.name.split('.')
        var fileType = fileNames[fileNames.length - 1].toLocaleLowerCase()
        var extList = ['pdf']
        if (!extList.find((item) => item == fileType)) {
          this.$message.error('只能上传pdf文件')
          return false
        }

        return true;

      },


      openPdf(fileId){
        this.pdfUrl=process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/previewPdf?id=' + fileId
        this.show = true
      },




      downloadPdf (fileId) {
        sysFileInfoDownload({ id: fileId }).then((res) => {
          this.downloadfile(res)

        }).catch((err) => {
          this.cardLoading = false
        })
      },

      downloadfile (res) {
        var blob = new Blob([res.data], { type: 'application/octet-stream;charset=UTF-8' })
        var contentDisposition = res.headers['content-disposition']
        var patt = new RegExp('filename=([^;]+\\.[^\\.;]+);*')
        var result = patt.exec(contentDisposition)
        var filename = result[1]
        var downloadElement = document.createElement('a')
        var href = window.URL.createObjectURL(blob) // 创建下载的链接
        var reg = /^["](.*)["]$/g
        downloadElement.style.display = 'none'
        downloadElement.href = href
        downloadElement.download = decodeURI(filename.replace(reg, '$1')) // 下载后文件名
        document.body.appendChild(downloadElement)
        downloadElement.click() // 点击下载
        document.body.removeChild(downloadElement) // 下载完成移除元素
        window.URL.revokeObjectURL(href)
      },

      handleChange(info) {
        let fileList = [...info.fileList];
        fileList = fileList.slice(-1);
        if (info.file.status === 'done') {
          let res = info.file.response
          if (res.success) {
            this.$message.success(`${info.file.name} 文件上传成功`)
            this.getList()
            this.record = { ...this.record,
              techName: res.data.fileOriginName,
              techDocFileId: res.data.id
            }
          } else {
            this.$message.error(res.message)
          }
        } else if (info.file.status === 'error') {
          this.$message.error(`${info.file.name} 文件上传失败`);
        }
      },


      exportDataMethod() {

        exportExcel({batteryId:this.batteryId}).then(res => {

          const fileName = 'SOR管理导出表.xlsx';

          if(!res) return
          const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' }) // 构造一个blob对象来处理数据，并设置文件类型

          if (window.navigator.msSaveOrOpenBlob) { //兼容IE10
            navigator.msSaveBlob(blob, fileName)
          } else {
            const href = URL.createObjectURL(blob) //创建新的URL表示指定的blob对象
            const a = document.createElement('a') //创建a标签
            a.style.display = 'none'
            a.href = href // 指定下载链接
            a.download = fileName //指定下载文件名
            a.click() //触发下载
            URL.revokeObjectURL(a.href) //释放URL对象
          }

        })


      },
      switchApp() {
                const applicationData = Vue.ls.get(ALL_APPS_MENU)
                this.MenuChange(applicationData[0]).then((res) => {}).catch((err) => {
                    this.$message.error('错误提示：' + err.message, 1)
                })
            },
        gotoManager(){
                //this.switchApp()
                this.$router.push({
                    path: "/battery_design_manager",
                    query: {
                        batteryId:this.batteryId
                    },
                });
            }, gotoBom(){
                //this.switchApp()
                this.$router.push({
                    path: "/sys_battery_design_bom",
                    query: {
                        batteryId:this.batteryId
                    },
                });
            },

      gotoSor(){
                //this.switchApp()
                this.$router.push({
                    path: "/system_battery_design_sor",
                    query: {
                        batteryId:this.batteryId
                    },
                });
            },
      designDelete(id){
        batteryDesignMiUpdate({id:id,status:1}).then(res =>{
          if (res.success) {
            this.$message.success('删除成功')
          } else {
            this.$message.error('删除失败：' + res.message)
          }
          this.getList()
        })

      },

      designCopy(){
        if(this.selectedRowKeys.length == 0){
          this.$message.error("请先选中要复制的数据");
        }


        copy(this.selectedRow).then(() => {
          this.getList(false)
        })




      },

      numberHandle(number, n) {
        if(null == number){
          return ''
        }
        n = n ? parseInt(n) : 0;
        if(n <= 0) {
          return Math.round(number);
        }
        number = Math.round(number * Math.pow(10, n)) / Math.pow(10, n); //四舍五入
        number = Number(number).toFixed(n); //补足位数
        return number;
      },
      edit1(id){
        const newData = [...this.dataSource2];
        const target = newData.find(item => id === item.id);
        this.editingKey = id;
        if (target) {
          target.editable = true;
          this.dataSource2 = newData;
        }
      },
      edit(id){
        const newData = [...this.dataSource1];
        const target = newData.find(item => id === item.id);
        this.editingKey = id;
        if (target) {
          target.editable = true;
          this.dataSource1 = newData;
        }
      },
      updateData(event,record,column){


        let param = {}
        param[column] = event.target.value
        param['id'] = record.id
        batteryDesignMiUpdate(param).then((res) => {
          this.getList(false)
          this.$nextTick(() => {
            if (res.success) {
              this.$message.success('保存成功')
            }else {
              this.$message.error(res.message)
            }
            for (let i = 0; i < controlInput.length; i++) {
              controlInput[i].disabled = false
            }
          })
        })



      },
      updateSelectData(e,record){

        let param = {}
          param['checkStatus'] = e
          param['id'] = record.id
        batteryDesignMiUpdate(param).then((res) => {
            this.getList(false)
            this.$nextTick(() => {
              if (res.success) {
                this.$message.success('保存成功')
              }else {
                this.$message.error(res.message)
              }
            })
        })

      },

      getByClass(parent, cls) {
				if (parent.getElementsByClassName) {
					return Array.from(parent.getElementsByClassName(cls));
				} else {
					var res = [];
					var reg = new RegExp(' ' + cls + ' ', 'i')
					var ele = parent.getElementsByTagName('*');
					for (var i = 0; i < ele.length; i++) {
						if (reg.test(' ' + ele[i].className + ' ')) {
							res.push(ele[i]);
						}
					}
					return res;
				}
			},
      init(){
        this.$nextTick(() => {
					let items = this.getByClass(document, 'divcls')
					for (const e of items) {
						var _e = e.parentNode 
            _e.classList.add('tdcls')
            if (e.classList.contains('div_border_right')) {
              _e.classList.add('td_border_right')
            }
            if (e.classList.contains('div_width')) {
              _e.classList.add('td_width')
            }
					}

          let $items = this.getByClass(document,'ant-layout')
          for (const e of $items) {
            e.setAttribute("style","min-height:initial");
          }
				})
      },
      getList(){
        batteryDesignMiList({batteryId:this.batteryId}).then((res) => {

          this.dataSource = res.data
          getBatteryDesign({inBatteryId:this.batteryId,type:'design'}).then(res =>{

            this.isOwn = res.data.isOwn
            this.design = res.data
            if(res.data.isOwn == 0 || this.design.manCheckStatus == 10 || this.design.manCheckStatus == 70){
              let inputs = document.getElementsByTagName("input");
              let controlInput = [];

              for (let i = 0; i < inputs.length; i++) {
                if(!inputs[i].disabled){
                  controlInput.push(inputs[i])
                }
              }

              for (let i = 0; i < controlInput.length; i++) {
                controlInput[i].disabled = true
              }
            }
          })
        })
      }
    }
  }
</script>
<style scoped>
  .h1{
    font-size: 18px;
    /* margin-bottom: 5px; */
    text-align: center;
    background: #0049b0;
    color: #fff;
  }
  /deep/.ant-table-wrapper{
    background: #fff;
  }



  /deep/.table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
  /deep/.ant-table-thead > tr > th{
    padding:2px 0 2px 4px;
    border-bottom: 1px solid #d2d4d7;
    border-right: 0;
    font-size: 13px;
    /* font-weight: bold; */
    background: rgba(0, 73, 176, 0.7);
    color: #fff;
  } 
  /deep/.ant-table-tbody > tr > td {
    padding: 0;
    margin: 0;
    border-bottom: 1px solid #d2d4d7;
    border-right: 1px solid #d2d4d7;
    font-size: 12px;
  }

  /deep/.tdcls{
    color: #000;
    background: rgb(239, 239, 239);
    padding: 0px 0 0px 4px !important;
  }

  /deep/.td_border_right{
    border-right: 1px solid #d2d4d7;
  }
  /deep/.td_width{
    width: 50px;
    padding: 0 !important;
  }
  .div_width{
    width: 30px;
    margin: auto;
  }
  input{
    width: 100%;
    height: 25px;
    margin: 0;
    border: 0;
    outline: none;
    text-align: center;
  }
  .wrapper{
    width: 50%;
    background: #fff;
    padding: 0 10px;
    overflow: hidden;
    float: left;
  }
  /deep/.ant-select-selection{
    border: none;
  }
  .spanstatus{
    display: block;
    padding: 1px 4px;
    border-radius: 2px;
    height:25px;
  }
  .success1{
    //background: #66b72a;
    color: #66b72a;
    margin-top: 5px;
  }
  .warn1 {
    //background: #fec303;
    color: #fec303;
    margin-top: 5px;
  }
  .fail1{
    //background: #e05328;
    color: #e05328;
    margin-top: 5px;
  }
  .statusbar{
    overflow: hidden;
    height: 32px;
    line-height: 32px;
    font-size: 12px;
    text-align: right;
  }
  .statusbar::after{
    content: ' ';
    display: block;
    height: 0;
    clear: both;
  }
  .statusbar .icon{
    margin-right: 3px;
    font-weight: bold;
  }
  .statusbar .btn{
    /* float: right; */
    margin-left: 20px;
  }
  .statusbar .a-btn{
    border-radius: 2px;
    background: #0049b0;
    color: #fff;
    padding: 2px 15px;
    letter-spacing: 2px;
  }
  .statusbar .txt{
    color: #000;
    font-weight: bold;
  }
  .statusbar .tip{
    float: left;
    /* margin-left: 120px; */
  }
   .tab-title{
        padding: 0 10px;
    }
  .anticon svg {
    font-size: 13px;
  }

    div.tab-head{
        border-bottom: 1px solid #d3d2d2c9;
    }

    div.tab-head div{
        display: inline-block;
        margin-right: 50px;
        font-weight: 700;
        font-size: 18px;
        color: rgb(128, 128, 128);
        margin-bottom: -6px;
        cursor: pointer;
    }

    div.tab-head div.active{
        font-size: 24px;
        color: rgba(0,73,176,1);
        margin-bottom: -4px;
        cursor: text;
    }
    
    div.sub-title{
      overflow: hidden;
      padding: 6px 10px;
    }


    div.sub-title::after{
      content: ' ';
      display: block;
      height: 0;
      clear: both;
    }

    div.sub-title span{
      display: block;
      float: right;
      margin-right: 6px;
      margin-bottom: 10px;
      margin-top: 100px;
      z-index: 1;
      position: relative;
    }
    div.sub-title span:first-child{
      margin-top: 1px;
    }
    div.sub-title .tip{
      font-family: SourceHanSansSC;
      font-weight: 400;
      font-size: 15px;
      color: rgba(0,101,255,0.67);
    }
    /deep/.ant-table-thead > tr > th:first-child{
      padding: 0;
    }

    /deep/.ant-table-tbody > tr.ant-table-row-selected td:first-child{
      background: #fafafa;
    }

  /deep/.tdcls1{
    color: #000;
    background: rgb(239, 239, 239);
  }
    /* /deep/td {
      height: 29px;
      vertical-align: middle;
    }

    /deep/td > div{
      height: 100%;
      margin: 0;
      padding: 0;
    } */
  /deep/.ant-select-selection-selected-value{
    text-align: center;
  }
  /deep/.ant-select-open .ant-select-selection{
    border: none;
    box-shadow:none;
  }
  /deep/ .ant-select-selection__rendered{
    margin: auto;
    line-height:initial;
    font-size: 12px;
    text-align: center;
  }
  /deep/ .ant-select-selection--single{
    height: auto;
  }
  /deep/.ant-select-selection__rendered {

    display: inline-block;

  }


  /deep/ .ant-table-tbody > tr > td {
    height: 25px;
  }

  /deep/ .ant-layout-content {
    background-color: #fff!important;
  }

  /deep/.ant-input[disabled] {
    color: black;
  }
  /deep/.ant-select-selection-selected-value {
    color: black;
  }
  /deep/.ant-input[disabled] {

    background-color: #ffffff;

  }
  /deep/.ant-select-disabled .ant-select-selection {
    background: #ffffff;
  }

</style>

<style lang='less'>
  .dropdownClassName{
    .ant-select-dropdown-menu-item {
      padding: 2px 8px !important;
      font-size: 12px !important;
      text-align: left !important;
      font-weight: 100;
    }
  }



</style>
