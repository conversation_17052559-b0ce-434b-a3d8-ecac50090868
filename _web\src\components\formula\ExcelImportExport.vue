<template>
  <div class="import-export-buttons">
    <div class="title-text">系数值设定</div>
    <a-button-group>
      <a-button type="primary" size="small" @click="downloadExcelTemplate" class="excel-button">
        <a-icon type="download" /> 下载模板
      </a-button>
      <a-upload
        :beforeUpload="handleExcelImport"
        :showUploadList="false"
        accept=".xlsx,.xls"
      >
        <a-button type="primary" size="small" class="excel-button">
          <a-icon type="upload" /> 导入Excel
        </a-button>
      </a-upload>
    </a-button-group>
  </div>
</template>

<script>
import * as XLSX from 'xlsx';
import { message } from 'ant-design-vue';
import { showSuccess, showWarning } from '@/utils/errorUtils';
import coefficientMixin from '@/mixins/coefficientMixin';

export default {
  name: 'ExcelImportExport',
  mixins: [coefficientMixin],
  props: {
    coefficients: {
      type: Array,
      required: true
    }
  },
  methods: {
    downloadExcelTemplate() {
      try {
        const wb = XLSX.utils.book_new();
        const headers = ['参数名', '参数值'];
        const data = [headers];

        const defaultValues = {
          'A_{0}': -89.17615505,
          'A_{1}': 2.166946848,
          'A_{2}': -1.928038619,
          'B_{1}': 67270.15547,
          'B_{2}': -18141699.14,
          'B_{3}': 1636621132,
          'C_{0}': 0.469330164,
          'C_{1}': -2.63962E-06,
          'C_{2}': 2.99293E-09,
          'D_{1}': 0.000702269,
          'D_{2}': 0.15008641,
          'E_{0}': 0.133777457,
          'E_{1}': -0.009581869,
          'E_{2}': 0.859137502,
          'E_{3}': -0.000296284,
          'F_{0}': -0.000573508,
          'F_{1}': 0.001636494,
          'F_{2}': 0.158579135,
          'F_{3}': 348.7920689
        };

        const groupedCoefficients = {};

        this.coefficients.forEach(coef => {
          const latexName = this.convertToLatex(coef.name);
          const letter = this.findGroupLetterForCoefficient(coef.name);

          if (!groupedCoefficients[letter]) {
            groupedCoefficients[letter] = [];
          }

          const value = coef.customValue !== undefined ? coef.customValue :
                      (defaultValues[latexName] !== undefined ? defaultValues[latexName] : 0);

          groupedCoefficients[letter].push({
            name: latexName,
            value: value
          });
        });

        Object.keys(groupedCoefficients).sort().forEach(letter => {
          const groupData = [...data];

          groupedCoefficients[letter]
            .sort((a, b) => a.name.localeCompare(b.name))
            .forEach(coef => {
              groupData.push([
                coef.name,
                coef.value
              ]);
            });

          const ws = XLSX.utils.aoa_to_sheet(groupData);
          ws['!cols'] = [{ wch: 15 }, { wch: 15 }];
          XLSX.utils.book_append_sheet(wb, ws, `${letter}组`);
        });

        XLSX.writeFile(wb, '系数值设定模板.xlsx');
        showSuccess('模板下载成功');
      } catch (error) {
        console.error('下载模板失败:', error);
        showWarning('下载模板失败: ' + error.message);
      }
    },

    handleExcelImport(file) {
      try {
        const reader = new FileReader();

        reader.onload = (e) => {
          try {
            const data = new Uint8Array(e.target.result);
            const wb = XLSX.read(data, { type: 'array' });
            const importedData = {};

            wb.SheetNames.forEach(sheetName => {
              const ws = wb.Sheets[sheetName];
              const sheetData = XLSX.utils.sheet_to_json(ws, { header: 1 });

              if (sheetData.length <= 1) return;

              for (let i = 1; i < sheetData.length; i++) {
                const row = sheetData[i];
                if (row.length < 2) continue;

                const paramName = String(row[0]).trim();
                const paramValue = parseFloat(row[1]);

                if (paramName && !isNaN(paramValue)) {
                  importedData[paramName] = paramValue;
                }
              }
            });

            this.updateCoefficientsFromImport(importedData);
            message.success('Excel导入成功');
          } catch (error) {
            console.error('解析Excel文件失败:', error);
            message.error('解析Excel文件失败: ' + error.message);
          }
        };

        reader.onerror = () => {
          message.error('读取文件失败');
        };

        reader.readAsArrayBuffer(file);
        return false;
      } catch (error) {
        console.error('导入Excel失败:', error);
        showWarning('导入Excel失败: ' + error.message);
        return false;
      }
    },

    updateCoefficientsFromImport(importedData) {
      if (!importedData || Object.keys(importedData).length === 0) {
        showWarning('导入的Excel文件中没有有效数据');
        return;
      }

      let updatedCount = 0;
      const updatedCoefficients = [...this.coefficients];

      updatedCoefficients.forEach(coef => {
        const latexName = this.convertToLatex(coef.name);

        if (importedData[latexName] !== undefined) {
          // 确保数值类型正确
          const value = parseFloat(importedData[latexName]);
          if (!isNaN(value)) {
            coef.customValue = value;
            updatedCount++;
          }
        }
      });

      if (updatedCount > 0) {
        this.$emit('coefficients-updated', updatedCoefficients);
        message.success(`成功更新了${updatedCount}个系数的值`);
      } else {
        showWarning('没有找到匹配的系数进行更新，请检查Excel文件中的参数名格式');
      }
    }
  }
};
</script>

<style scoped>
.import-export-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px 8px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #f0f0f0;
}

.title-text {
  font-size: 14px;
  font-weight: 600;
  color: #666;
}

.excel-button {
  margin-right: 8px;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.excel-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.excel-button:last-child {
  margin-right: 0;
}
</style>
