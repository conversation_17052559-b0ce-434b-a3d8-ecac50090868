<template>
  <div style="padding: 10px;">
    <pbiTabs :tabsList="laboratoryList" :activeKey="laboratoryId" @clickTab="callback"></pbiTabs>
    <tableIndex
        :pageLevel='1'
        :tableTotal= 'tableTotal'
        :pageTitleShow=false
        :otherHeight="parseInt(105)"
        :loading='tableLoading'
        @paginationChange="handlePageChange"
        @paginationSizeChange="handlePageChange"
        @tableFocus="tableFocus"
        @tableBlur="tableBlur"
    >
      <template #search>
        <pbiSearchContainer>
          <pbiSearchItem label='委托单号' :span="4">
            <a-input v-model="queryParam.folderNo" @keyup.enter="getTestingLedgerList" @change="getTestingLedgerList"/>
          </pbiSearchItem>
          <pbiSearchItem label='样品名称' :span="4">
            <a-input v-model="queryParam.sampleName" @keyup.enter="getTestingLedgerList" @change="getTestingLedgerList"/>
          </pbiSearchItem>
          <pbiSearchItem label='测试项目' :span="4">
            <a-input v-model="queryParam.testProjectName" @keyup.enter="getTestingLedgerList" @change="getTestingLedgerList"/>
          </pbiSearchItem>
          <pbiSearchItem label='料号' :span="4">
            <a-input v-model="queryParam.partNo" @keyup.enter="getTestingLedgerList" @change="getTestingLedgerList"/>
          </pbiSearchItem>
          <pbiSearchItem label='批号' :span="4" v-if="isShowAllSearch">
            <a-input v-model="queryParam.batchNo" @keyup.enter="getTestingLedgerList" @change="getTestingLedgerList"/>
          </pbiSearchItem>
          <pbiSearchItem label='委托人' :span="4" v-if="isShowAllSearch">
            <a-input v-model="queryParam.wtrName" @keyup.enter="getTestingLedgerList" @change="getTestingLedgerList"/>
          </pbiSearchItem>
          <pbiSearchItem label='委托部门' :span="4" v-if="isShowAllSearch">
            <a-input v-model="queryParam.wtbmName" @keyup.enter="getTestingLedgerList" @change="getTestingLedgerList"/>
          </pbiSearchItem>
          <pbiSearchItem label='测试状态' :span="4" v-if="isShowAllSearch">
            <a-select v-model="queryParam.statusList" :allowClear="true" mode="multiple" :maxTagCount="parseInt(2)" @change="getTestingLedgerList" style="width: 100%;">
              <a-select-option value="任务分配">任务分配</a-select-option>
              <a-select-option value="结果录入">结果录入</a-select-option>
              <a-select-option value="复核退回">复核退回</a-select-option>
              <a-select-option value="结果复核">结果复核</a-select-option>
              <a-select-option value="样品接收">样品接收</a-select-option>
              <a-select-option value="已完成">已完成</a-select-option>
            </a-select>
          </pbiSearchItem>
          <pbiSearchItem label='审核人' :span="4" v-if="isShowAllSearch">
            <a-input v-model="queryParam.auditor" @keyup.enter="getTestingLedgerList" @change="getTestingLedgerList"/>
          </pbiSearchItem>
          <pbiSearchItem label='科室' :span="4" v-if="isShowAllSearch">
            <a-input v-model="queryParam.auditorDepartment" @keyup.enter="getTestingLedgerList" @change="getTestingLedgerList"/>
          </pbiSearchItem>
          <pbiSearchItem label='OA流程结束时间' :span="6" v-if="isShowAllSearch">
            <a-range-picker :value="oaEndTimeRange" @change="oaEndTimeRangeChange" ></a-range-picker>
          </pbiSearchItem>
          <pbiSearchItem label='收样时间' :span="6" v-if="isShowAllSearch">
            <a-range-picker :value="receiveTimeRange" @change="receiveTimeRangeChange"  ></a-range-picker>
          </pbiSearchItem>
          <pbiSearchItem label='计划开始时间' :span="6" v-if="isShowAllSearch">
            <a-range-picker :value="planStartTimeRange" @change="planStartTimeRangeChange" ></a-range-picker>
          </pbiSearchItem>
          <pbiSearchItem label='计划结束时间' :span="6" v-if="isShowAllSearch">
            <a-range-picker :value="planEndTimeRange" @change="planEndTimeRangeChange" ></a-range-picker>
          </pbiSearchItem>
          <pbiSearchItem label='实际完成时间' :span="6" v-if="isShowAllSearch">
            <a-range-picker :value="actualFinishTimeRange" @change="actualFinishTimeRangeChange" ></a-range-picker>
          </pbiSearchItem>

          <pbiSearchItem :span="isShowAllSearch ? 24 : 8" type='btn'>
            <div class="secondary-btn">
              <a-button style="margin-right: 8px;" @click="getTestingLedgerList()" type="primary">查询</a-button>
              <a-button style="margin-right: 8px;" @click="resetList()">重置</a-button>
              <a-button type="primary" class="export-btn" @click="modalVisible = true" :loading="exportLoading">导出</a-button>
            </div>
            <div class='toggle-btn'>
              <a-button size='small' type='link' @click='isShowAllSearch = !isShowAllSearch'>
                {{ isShowAllSearch ? '收起' : '展开' }}
                <span v-if='isShowAllSearch'><a-icon type='double-left'/></span>
                <span v-else><a-icon type='double-right'/></span>
              </a-button>
            </div>
          </pbiSearchItem>
        </pbiSearchContainer>
      </template>
      <template #table>
        <ag-grid-vue class='ag-theme-balham'
                     :style="{height:`${tableHeight}px`}"
                     :tooltipShowDelay="0"
                     :columnDefs='columns'
                     :rowData='testingLedgerList'
                     :defaultColDef='defaultColDef'>
        </ag-grid-vue>
      </template>
    </tableIndex>

    <a-modal :visible="modalVisible" centered title="选择导出列" :width="380" @ok="handleExport" @cancel="modalVisible = false">
      <ag-grid-vue class='ag-theme-balham'
                   style="height: 500px;"
                   :tooltipShowDelay="0"
                   :rowData='columns'
                   :columnDefs='columnDefs'
                   @grid-ready="onGridReady"
                   rowSelection="multiple"
                   :gridOptions="gridOptions"
                   :defaultColDef='defaultColDef'>
      </ag-grid-vue>
      <template slot="footer">
        <a-button @click="modalVisible = false">取消</a-button>
        <a-button type="primary" @click="handleExport">导出</a-button>
      </template>
    </a-modal>
  </div>
</template>
<script>
import { STable } from '@/components'
import testData from '../folder/testData.vue'
import { exportTestingLedgerList, getTestingLedgerList } from "@/api/modular/system/testProgressManager";
import { downloadfile1 } from "@/utils/util";
import pbiTabs from "@/components/pageTool/components/pbiTabs.vue";

export default {
  components: {
    STable, testData, pbiTabs
  },
  data() {
    return {
      modalVisible: false,
      columnDefs: [
        {
          headerName: '',
          field: 'checkbox',
          width: 40,
          sortable: false,
          checkboxSelection: true,
          headerCheckboxSelection:true
        },
        {
          headerName: '序号',
          width: 50,
          sortable: false,
          cellRenderer: function (params) {
            return params.node.rowIndex + 1
          },
        },
        {
          headerName: "列名称",
          field: "headerName",
          flex: 1,
          minWidth: 120,
        },
      ],
      selectedRows: [],
      gridApi: null,
      gridOptions: {
        onSelectionChanged: this.onSelectionChanged,
        rowMultiSelectWithClick: true
      },
      laboratoryList: [
        {value:'HZ_YJ_DL_JM', label:'精密实验室', show: true}
      ],
      isShowAllSearch: false,
      exportLoading: false,
      loadData: parameter => {
        return getTestingLedgerList(Object.assign(parameter, this.queryParam)).then((res) => {
            // this.testingLedgerList = res.data
            return res.data
        })
      },
      pageNo: 1,
      pageSize: 20,
      tableTotal: 0,
      tableHeight: 400,
      tableLoading: false,
      defaultColDef: {
        filter: false,
        floatingFilter: false,
        editable: false,
        cellStyle: { 'text-align': 'center' }
      },
      testingLedgerList: [],
      confirmLoading: false,
      height: 200,
      address: this.hasPerm('progress:all') ? 'all' : 'none',
      labelCol: {
        sm: {
          span: 8
        }
      },
      wrapperCol: {
        sm: {
          span: 15
        }
      },
      queryParam: {},
      columns: [
        {
          headerName: "序号",
          field: "orderNum",
          width: 50
        },
        {
          headerName: "委托单号",
          field: "folderNo",
          width: 120
        },
        {
          headerName: "样品编号",
          field: "sampleNo",
          width: 150
        },
        {
          headerName: "样品名称",
          field: "sampleName",
          width: 120
        },
        {
          headerName: "测试项目",
          field: "testProjectName",
          width: 200,
          tooltipValueGetter: this.pbiTooltip,
        },
        {
          headerName: "风险等级",
          field: "riskLevel",
          width: 100,
        },
        {
          headerName: "试验类型",
          field: "testType",
          width: 100,
        },
        {
          headerName: "样品类型",
          field: "sampleType",
          width: 100,
        },
        {
          headerName: "测试单价",
          field: "checkPrice",
          width: 100
        },
        {
          headerName: "料号",
          field: "partNo",
          width: 100
        },
        {
          headerName: "批号",
          field: "batchNo",
          width: 100
        },
        {
          headerName: "委托部门",
          field: "wtbmName",
          width: 200
        },
        {
          headerName: "委托人",
          field: "wtrName",
          width: 120
        },
        {
          headerName: "OA流程结束时间",
          field: "oaEndTime",
          width: 130
        },
        {
          headerName: "收样时间",
          field: "receiveTime",
          width: 120
        },
        {
          headerName: "计划开始时间",
          field: "planStartTime",
          width: 120
        },
        {
          headerName: "计划结束时间",
          field: "planEndTime",
          width: 120
        },
        {
          headerName: "实际完成时间",
          field: "actualFinishTime",
          width: 120
        },
        {
          headerName: "测试状态",
          field: "testStatus",
          width: 120
        },
        {
          headerName: "测试后样品处置",
          field: "sampleProcess",
          width: 120
        },
        {
          headerName: "测试员",
          field: "testMan",
          width: 120
        },
        {
          headerName: "参与人",
          field: "participator",
          width: 120
        },
        {
          headerName: "审核人",
          field: "auditor",
          width: 120
        },
        {
          headerName: "科室",
          field: "auditorDepartment",
          width: 120
        },
        {
          headerName: "一级分类",
          field: "firstCategory",
          width: 120
        },
        {
          headerName: "二级分类",
          field: "secondCategory",
          width: 120
        },
        {
          headerName: "备注：材料-作业指导书/结构-设备名称",
          field: "remark",
          width: 250,
          tooltipValueGetter: this.pbiTooltip,
        },
      ],
      laboratoryId: 'HZ_YJ_DL_JM',
      planEndTimeRange: [],
      oaEndTimeRange: [],
      receiveTimeRange: [],
      planStartTimeRange: [],
      actualFinishTimeRange: []
    }
  },
  watch: {
    isShowAllSearch(newVal, oldVal) {
      this.handleHeight()
    }
  },
  created() {
    this.queryParam = {}
    this.getTestingLedgerList();
  },
  computed: {},
  mounted() {
    this.handleHeight()
  },
  methods: {
    handleHeight() {
      this.tableHeight = document.body.clientHeight - 105 - 100 - (this.isShowAllSearch ? 120 : 0)
    },
    handlePageChange(value) {
      let {current, pageSize} = value
      this.pageNo = current
      this.pageSize = pageSize
      this.getTestingLedgerList()
    },
    // 鼠标进入
    tableFocus() {
      this.$el.style.setProperty('--scroll-border-bottom-fixed', 'none');
      this.$el.style.setProperty('--scroll-display', 'unset');
      this.$el.style.setProperty('--scroll-border-bottom', '1px solid #Dee1e8');
    },
    // 鼠标移出
    tableBlur() {
      this.$el.style.setProperty('--scroll-border-bottom-fixed', '1px solid #dee1e8');
      this.$el.style.setProperty('--scroll-display', 'none');
      this.$el.style.setProperty('--scroll-border-bottom', 'none');
    },
    resetList() {
      this.queryParam = {}
      this.planEndTimeRange = []
      this.oaEndTimeRange = []
      this.receiveTimeRange = []
      this.planStartTimeRange = []
      this.actualFinishTimeRange = []
      this.getTestingLedgerList()
      // this.$refs.table2.refresh()
    },
    planEndTimeRangeChange(a, b) {
      this.queryParam.planEndTimeRangeBegin = b[0]
      this.queryParam.planEndTimeRangeEnd = b[1]
      this.planEndTimeRange = b
      this.getTestingLedgerList()
      // this.$refs.table2.refresh()
    },
    oaEndTimeRangeChange(a, b) {
      this.queryParam.oaEndTimeRangeBegin = b[0]
      this.queryParam.oaEndTimeRangeEnd = b[1]
      this.oaEndTimeRange = b
      this.getTestingLedgerList()
      // this.$refs.table2.refresh()
    },
    receiveTimeRangeChange(a, b) {
      this.queryParam.receiveTimeRangeBegin = b[0]
      this.queryParam.receiveTimeRangeEnd = b[1]
      this.receiveTimeRange = b
      this.getTestingLedgerList()
      // this.$refs.table2.refresh()
    },
    planStartTimeRangeChange(a, b) {
      this.queryParam.planStartTimeRangeBegin = b[0]
      this.queryParam.planStartTimeRangeEnd = b[1]
      this.planStartTimeRange = b
      this.getTestingLedgerList()
      // this.$refs.table2.refresh()
    },
    actualFinishTimeRangeChange(a, b) {
      this.queryParam.actualFinishTimeRangeBegin = b[0]
      this.queryParam.actualFinishTimeRangeEnd = b[1]
      this.actualFinishTimeRange = b
      this.getTestingLedgerList()
      // this.$refs.table2.refresh()
    },
    getTestingLedgerList() {
      this.tableLoading = true
      getTestingLedgerList({
        ...{
          pageNo: this.pageNo,
          pageSize: this.pageSize
        }, ...this.queryParam
      }).then((res) => {
        if (res.success) {
          this.tableTotal = res.data.totalRows
          this.testingLedgerList = res.data.rows
        }
      }).finally(() => {
        if (this.pageNo > 1 && this.testingLedgerList.length === 0) {
          this.pageNo = Math.max(1, Math.ceil(this.tableTotal / this.pageSize))
          this.getTestingLedgerList()
        }
        this.tableLoading = false
      })
    },
    onGridReady(params) {
      this.gridApi = params.api;
    },
    onSelectionChanged(event) {
      // 获取当前选中的行
      const selectedNodes = this.gridApi.getSelectedNodes();
      // console.log("selectedNodes: ", selectedNodes)
      // 更新选中的行数据
      this.selectedRows = selectedNodes.map(node => node.data);
    },
    handleExport() {
      if (!Array.isArray(this.selectedRows) || this.selectedRows.length === 0) {
        return this.$message.warn("请选择需要导出的列！")
      }

      const exportFields = []
      for (let i = 0; i < this.columns.length; i++) {
        let column = this.columns[i]
        let findIndex = this.selectedRows.findIndex(item => item.field === column.field)
        if (findIndex !== -1) {
          exportFields.push({key:column.field, title:column.headerName, width: column.width})
        }
      }
      this.exportTestingLedgerList(JSON.stringify(exportFields))
      this.modalVisible = false
    },
    exportTestingLedgerList(exportFields) {
      this.exportLoading = true
      exportTestingLedgerList({...this.queryParam, exportFields: exportFields}).then((res) => {
        var fileName = "测试台账" + this.formattedDate(new Date()) + ".xls"
        if (res) {
          downloadfile1(res, fileName)
        }
        this.exportLoading = false
      })
    },
    formattedDate(date) {
      const year = date.getFullYear();
      const month = ("0" + (date.getMonth() + 1)).slice(-2);
      const day = ("0" + date.getDate()).slice(-2);
      const hours = ("0" + date.getHours()).slice(-2);
      const minutes = ("0" + date.getMinutes()).slice(-2);
      return `${year}${month}${day}${hours}${minutes}`;
    },
    callback(key) {
      this.laboratoryId = key
    },
    handleOk() {
    }
  }
}
</script>
<style lang="less" scoped>
@import '/src/components/pageTool/style/pbiSearchItem.less';

:root {
  --scroll-display: none;
  --scroll-border-bottom: none;
  --scroll-border-bottom-fixed: none;
}
/deep/.ag-body-horizontal-scroll{
  border-bottom: var(--scroll-border-bottom) !important;
}
/deep/.ag-body-horizontal-scroll-viewport {
  display: var(--scroll-display) !important;
  border-bottom: var(--scroll-border-bottom) !important;
}

/deep/.ag-horizontal-left-spacer,
/deep/.ag-horizontal-right-spacer{
  border-bottom: var(--scroll-border-bottom-fixed) !important;
}
</style>