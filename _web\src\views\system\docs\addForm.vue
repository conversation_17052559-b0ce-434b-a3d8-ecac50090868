<template>
  <a-modal title="新增应用" :width="500" :visible="visible" :confirmLoading="confirmLoading" @ok="handleSubmit" @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item label="过程" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input placeholder="请输入过程" v-decorator="['process', {rules: [{required: true, message: '请输入过程！'}]}]" />
        </a-form-item>
        <a-form-item label="输出文件" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input placeholder="请输入输出文件" v-decorator="['outputFile', {rules: [{required: true, message: '请输入输出文件！'}]}]" />
        </a-form-item>
        <a-form-item label="完成日期" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input placeholder="请输入完成日期" v-decorator="['finishedDate', {rules: [{required: true, message: '请输入完成日期！'}]}]" />
        </a-form-item>
        <a-form-item label="负责部门" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input placeholder="请输入负责部门" v-decorator="['manager', {rules: [{required: true, message: '请输入负责部门！'}]}]" />
        </a-form-item>
        <a-form-item label="表单编号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input placeholder="请输入表单编号" v-decorator="['docNo', {rules: [{required: true, message: '请输入表单编号！'}]}]" />
        </a-form-item>
        <a-form-item label="适用情况" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            
            <a-switch v-model="docIsNeed" />
        </a-form-item>
        <a-form-item label="排序" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input-number :min="0" v-decorator="['sort', {rules: [{required: true, message: '请输入排序！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import {addDoc} from "@/api/modular/system/docManage"
  export default {
    props: {
        issueId: {
            type: Number,
            default: 0
        },
        stage:{
            type:String,
            default:''
        },
        projectdetail: {
            type: Object,
            default: {}
        }
    },
    data() {
      return {
        docIsNeed:false,
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 18
          }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      add() {
        this.visible = true
      },
      handleSubmit() {
        const {
          form: {
            validateFields
          }
        } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            values.issueId = this.issueId
            values.stage = this.stage
            values.docIsNeed = this.docIsNeed ? 1 : 0
            const stage = this.projectdetail.productStageItems.find(e=>e.stage == this.stage)
            values.planDate = stage ? stage.planReviewDate : null
            values.docLevel = this.projectdetail.level
            addDoc(values).then((res) => {
              this.confirmLoading = false
              if (res.success) {
                this.$message.success('新增成功')
                this.handleCancel()
                this.$emit('ok')
              } else {
                this.$message.error('新增失败：' + res.message)
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel() {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
