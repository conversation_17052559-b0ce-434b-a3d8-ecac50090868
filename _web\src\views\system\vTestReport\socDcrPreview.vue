<template>
  <div ref="wrapper" class="wrapper">
    <div class="flex-sb-center-row">
      <div class="head_title">{{ record.type + ": " + record.reportName }}</div>
      <span id="revealText14" class="reveal-text-opacity" style="font-size: 14px;"></span> 
      <span id="revealText15" class="reveal-text-opacity" style="font-size: 15px;"></span> 
    </div>
    <span id="revealText14" class="reveal-text-opacity" style="font-size: 14px;"></span> 
    <span id="revealText15" class="reveal-text-opacity" style="font-size: 15px;"></span> 
    <div class="all-wrapper">
      <div class="left-content block">
        <pageComponent editObj="dcr"  @down="handleNormalDown('dcr')" @edit="handleEditEcharts('dcr')"></pageComponent>
        <div id="dcr" ref="dcr" class="mt10" style="width: 598px;height: 417px;border: 0.5px solid #ccc;"></div>

        <pageComponent class="mt10" editObj="growth"  @down="handleNormalDown('growth')" @edit="handleEditEcharts('growth')"></pageComponent>
        <div id="growth" ref="growth" class="mt10" style="width: 598px;height: 417px;border: 0.5px solid #ccc;">
        </div>
      </div>
      <div class="right-content">
        <div class="block" id="export">
          <div class="flex-column">
            <div>
              <div class="flex-center">
                <div @dblclick="() => (update = true)">
                  <svg t="1704333150396" class="icon" viewBox="0 0 1024 1024" version="1.1"
                    xmlns="http://www.w3.org/2000/svg" p-id="7872" width="20" height="20">
                    <path
                      d="M936.96 0v844.8h-665.6V0h665.6zM327.68 783.36h547.84V665.6H476.16v-61.44h399.36V476.16H476.16V409.6h399.36V271.36h-225.28V199.68h225.28V71.68H327.68v711.68z"
                      fill="#606266" p-id="7873"></path>
                    <path d="M143.36 250.88v711.68h578.56V1024H87.04V250.88z" fill="#606266" p-id="7874"></path>
                  </svg>
                </div>

                <strong class="ml10">原始数据</strong>
              </div>

            </div>

            <div class="mt10">
              <a-table :columns="originColumns" bordered :data-source="originData" :pagination="false">
              </a-table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="drawerVisible">
      <PreviewDrawer
        :screenImageId = "screenImageId"
        :templateParam = "reportChartTemplateList[editObj]"
        :isLegendLeft =  "true"
        :legendOptions=" editObj === 'dcr' ? dcrOriginalLegend : growthOriginalLegend "
        :data="editObj === 'dcr' ? dcrOriginalSeries : growthOriginalSeries"
        :original="editObj === 'dcr' ? dcrResetOriginal : growthResetOriginal"
        :editData="editObj === 'dcr' ? dcrEditData : growthEditData"
        :checkObj="chartCheckObj[editObj]"
        @submit="handleDrawerSubmit"
        @reset="handleDrawerReset" 
        @close="drawerVisible = false"
        @changeTemplate ="handleChangeTemplate"
        @screenshot="handleScreenshot"
      >
      </PreviewDrawer>
    </div>
    <!-- <pbiReturnTop v-if="isShowReturnTop" @returnTop="handleReturnTop"></pbiReturnTop> -->
  </div>
</template>
<script>
  import { testReportGet, testReportUpdateDate } from "@/api/modular/system/limsManager"
  import { mixin,chart } from "./mixin/index.js"
  import {chartTemplate} from "@/views/system/vTestReport/mixin/chartTemplate";


  export default {
    mixins: [mixin,chart,chartTemplate],
    data: function () {
      return {
        id: null, // 请求接口的id
        update: false,

        /* 图表 */
        dcrEchart: null,
        growthEchart: null,
        isEditDcrXNum: 0,
        isEditGrowthXNum: 0,

        /* 右边表格 */
        originColumns: [],
        originData: [],

        dcrColumns: [],
        dcrGrowthColumns: [],
        dcrData: [],

        /* 在线编辑图表 */
        dcrRevealNum:0,
        chartCheckObj:{
          dcr:{},
          growth:{}
        },

        // 第一张图表
        // dcrCheckObj: {}, //点击图标的选中对象  editObj: axis 坐标轴  tag 线 legend 图例  tag: axs 选中的那条线
        dcrOriginalLegend: [], // 图例的原始值
        dcrOriginalSeries: [], // 图表数据原始值
        dcrEditData: {},
        dcrResetOriginal: {},

        growthRevealNum:0,

        // 第二张图
        // growthCheckObj: {},
        growthOriginalLegend: [],
        growthOriginalSeries: [],
        growthEditData: {},
        growthResetOriginal: {}

      }
    },
    async mounted() {
      await this.getChartTemplateRelationList(this.$route.query.id,['dcr','growth'])
      this.init()

      // const box = this.$refs.wrapper
      // box.addEventListener("scroll", e => {
      //   if (e.target.scrollTop > 100 && !this.isShowReturnTop) {
      //     this.isShowReturnTop = true
      //   }
      //   if(e.target.scrollTop < 100 && this.isShowReturnTop){
      //     this.isShowReturnTop = false
      //   }
      // })
    },

    methods: {
      init() {
        this.id = this.$route.query.id
        testReportGet({ id: this.id })
          .then(res => {
            this.record = res.data
            this.allDataJson = JSON.parse(res.data.allDataJson)

            // 图例排序
            this.allDataJson.legendList = this.allDataJson.legendList.sort((item1,item2) => { return  Number(item1.replace("℃","")) -  Number(item2.replace("℃","")) })

            this.queryParam = JSON.parse(res.data.queryParam)

            this.originData = this.allDataJson.tableList
            // this.dcrData = this.allDataJson.dcrList

            this.update = false
          })
          .then(async res => {
            // 右边表格数据
            if (this.originData.length > 0) {
              this.initTable()
            }

            this.dcrEditData = this._getInitData('dcr','edit')
            this.dcrResetOriginal = this._getInitData('dcr')

            this.growthEditData = this._getInitData('growth','edit')
            this.growthResetOriginal = this._getInitData('growth')

            await this.initDcr()
            await this.initGrowth()
            

          })
      },

      initTable() {
        this.originColumns = []

        for (let i = 0; i < this.originData[0].socOcvDcrList.length; i++) {
          let temp = this.originData[0].socOcvDcrList[i]
          this.originColumns.push({
            title: temp.temp + '℃',
            align: "center",
            children: [
              {
                title: "SOC/%",
                width: 60,
                align: "center",
                dataIndex: "socOcvDcrList[" + i + "].soc",
              },
              {
                title: "OCV/V",
                width: 60,
                align: "center",
                dataIndex: "socOcvDcrList[" + i + "].ocv"
              },
              {
                title: "DCR/mΩ",
                width: 70,
                align: "center",
                dataIndex: "socOcvDcrList[" + i + "].dcr"
              }
            ]
          })
        }

      },

      initDcrOriginalData(checkData = []) {
        let dcrEchartsList = _.cloneDeep(this.allDataJson.socDcrEcharts)
        let lineColorList = [] // 折线颜色

        let seriesList = []
        let dcrOriginalSeries = []
        let dcrOriginalCheckData = []
        const isCheck = checkData.length === 0
        const templateParam = this.reportChartTemplateList['dcr'].templateParamJson

        dcrEchartsList.sort((item1,item2) => { return item1.temp -  item2.temp })
        const echartsColorList = this.allDataJson.legendList.length <= 2 ? this.echartsColorShortList : this.echartsColorLongList

        for (let i = 0; i < dcrEchartsList.length; i++) {
          const temp = dcrEchartsList[i].temp
          const have = lineColorList.find(v => v.name === dcrEchartsList[i].temp)
          if (have == undefined) {
            lineColorList.push({ name: dcrEchartsList[i].temp, color: echartsColorList[lineColorList.length % echartsColorList.length] })
          }

          const templateContent = templateParam.checkData.length > 0 ? (templateParam.checkData.filter(item => item.id === temp + "℃" + (i + 1))[0] || {}) : {}
          const editContent = checkData[checkData.findIndex(findItem => findItem.id === temp + "℃" + (i + 1))]

          let series = {
            name: temp + "℃",
            index:i + 1,
            soc: temp,
            id: temp + "℃" + (i + 1),
            type: "line",
            barGap: 0,
            markPoint: {
              data: []
            },
            connectNulls:templateContent.connectNulls ?? ( isCheck ? false : Boolean(Number(editContent.connectNulls))),
            symbol:templateContent.symbol ?? ( isCheck ? "rect" : editContent.symbol),
            symbolSize:templateContent.symbolSize ?? ( isCheck ? 5 : editContent.symbolSize),
            lineStyle: {
              width:templateContent.lineWidth ?? ( isCheck ? 1 : editContent.lineWidth),
              type:templateContent.lineType ?? ( isCheck ? "solid" : editContent.lineType),
              color:
              templateContent.lineColor ?? 
                (isCheck
                  ? lineColorList[lineColorList.findIndex(v => v.name === temp)].color
                  : editContent.lineColor)
            },
            itemStyle: {
              color:
              templateContent.itemColor ?? 
                (isCheck
                  ? lineColorList[lineColorList.findIndex(v => v.name === temp)].color
                  : editContent.itemColor)
            },
            emphasis: {
              focus: "series"
            },

            batteryNum: dcrEchartsList[i].batteryNum,
            data: dcrEchartsList[i].data.map((mapItem, index) => { return { id: index, name: mapItem.name, value: mapItem.value } }),
          }

          // 设置最大最小值
          if (checkData.length > 0 && editContent.maxPoint || templateContent.maxPoint) {
            series.markPoint.data.push({ type: "max", name: "Max" })
          }
          if (checkData.length > 0 && editContent.minPoint || templateContent.maxPoint) {
            series.markPoint.data.push({ type: "min", name: "Min" })
          }

          dcrOriginalSeries.push({
            id: temp + "℃" + (i + 1),
            index:i + 1,
            soc: temp + "℃",
            name: temp + "℃",
            synchronization:templateContent.synchronization ?? (isCheck ? i : editContent.synchronization),
            maxPoint:templateContent.maxPoint ?? (isCheck ? false : editContent.maxPoint),
            minPoint:templateContent.minPoint ?? (isCheck ? false : editContent.minPoint),
            connectNulls:templateContent.connectNulls ?? false,
            symbol:templateContent.symbol ?? (isCheck ? "rect" : editContent.symbol),
            symbolSize:templateContent.symbolSize ?? (isCheck ? 5 : editContent.symbolSize),
            itemColor:
            templateContent.itemColor ??
              (isCheck
                ? lineColorList[lineColorList.findIndex(v => v.name === temp)].color
                : editContent.itemColor),
            lineType:templateContent.lineType ?? (isCheck ? "solid" : editContent.lineType),
            lineWidth:templateContent.lineWidth ?? (isCheck ? 1 : editContent.lineWidth),
            lineColor:
            templateContent.lineColor ??
              (isCheck
                ? lineColorList[lineColorList.findIndex(v => v.name === temp)].color
                : editContent.lineColor)
          })
          // 原始值
          dcrOriginalCheckData.push({
            id: temp + "℃" + (i + 1),
            index:i + 1,
            soc: temp + "℃",
            name: temp + "℃",
            connectNulls: false,
            synchronization:i,
            maxPoint:false,
            minPoint:false,
            symbol: "rect",
            symbolSize: 5,
            itemColor: lineColorList[lineColorList.findIndex(v => v.name === temp)].color,
            lineType: "solid",
            lineWidth: 1,
            lineColor: lineColorList[lineColorList.findIndex(v => v.name === temp)].color
          })


          // duplicateDataOptions.push({
          //   id: dcrEchartsList[i].soc + "℃",
          //   data: dcrEchartsList[i].data.map((mapItem, index) => { return { id: index, name: mapItem.name, value: index, label: mapItem.value[1].toString() } })
          // })

          seriesList.push(series)
        }

        // return [dcrOriginalSeries, dcrOriginalCheckData, seriesList, duplicateDataOptions]
        return [dcrOriginalSeries, dcrOriginalCheckData, seriesList]

      },

      initDcr(
        legendData = {},
        checkData = [], //选中的数据
        axisData = {},
        titleData = {},
        gridData = {}
      ) {
        if (this.dcrEchart) this.dcrEchart.dispose();
        this.dcrEchart = this.echarts.init(this.$refs.dcr, "walden",{ devicePixelRatio: 2 })

        const templateParam = this.reportChartTemplateList['dcr'].templateParamJson
        const originalParam = this.reportChartTemplateList['dcr'].originalParamJson
        const processingResult = this.initDcrOriginalData(checkData)
        const legendRevealNumResult = this.handleLegendRevealNum(this._getNewSocLegend(this.allDataJson.legendList[0]),501,legendData.legendWidth || 20,legendData.legendGap || 10,true)
        
        this.dcrRevealNum = legendRevealNumResult[0]
        this.dcrOriginalLegend = _.uniq(_.cloneDeep(this.allDataJson.legendList))

        const { 
          titleData: newTitleData, 
          gridData: newGridData, 
          legendData: newLegendData, 
          axisData: newAxisData, 
          legend: newLegend 
        } = this._getTemplateParams('dcr', titleData, gridData, legendData, axisData, legend);
        titleData = newTitleData;
        gridData = newGridData;
        legendData = newLegendData;
        axisData = newAxisData;

        if(!legendData.legendRevealList || templateParam.legendData.legendRevealList) this.dcrEditData.legendRevealList = templateParam.legendData.legendRevealList ?? _.cloneDeep(this.allDataJson.legendList).slice(0,this.dcrRevealNum * 2)
   

        this.dcrOriginalSeries = processingResult[0]
        this.dcrResetOriginal.checkData = processingResult[1]
        this.dcrResetOriginal.series = _.cloneDeep(processingResult[1]) 
        let dcrSeries = processingResult[2]
        this.dcrEditData.legend = _.cloneDeep(this.allDataJson.legendList)
        this.dcrEditData.series = _.cloneDeep(this.dcrOriginalSeries)

        if (legendData.legendSort) {
          this.dcrEditData.legend = _.cloneDeep(legendData.legendSort) // 将页面上的图例数组按照用户设置的顺序排序
        }
        this.dcrEditData.legendSort = !legendData.legendSort ? _.cloneDeep(this.allDataJson.legendList) : _.cloneDeep(this.dcrEditData.legend) // 传回给在线编辑图表

        if (legendData.legendEditName) {
          legendData.legendEditName.forEach(v => {
            if (v.newName && !v.isReset) {
              let temIndex1 = this.dcrOriginalLegend.findIndex(findItem => findItem == v.originName)
              this.dcrOriginalLegend[temIndex1] = v.newName
              let temIndex2 = this.dcrEditData.legend.findIndex(findItem => findItem == v.originName)
              this.dcrEditData.legend[temIndex2] = v.newName
              this.dcrEditData.series.forEach(findItem => {
                findItem.name = findItem.name == v.originName ? v.newName : findItem.name
              })
              dcrSeries.forEach(findItem => {
                findItem.name = findItem.name == v.originName ? v.newName : findItem.name
              })
            }

            if (!v.newName && v.isReset) {
              v.previousName = ''
              v.isReset = false
            }
          })

          // 赋予修改后的图例修改名称数组
          this.dcrEditData.legendEditName = legendData.legendEditName
        }

        // 图例修改名称数组  使用位置：在线编辑图表--图例--名称 ,首次进入，将图例的值给图例修改名称数组
        // originName 原始值 newName 新值 previousName 上一个值（用于清空的情况下使用）isReset 是否重置（用于清空的时候使用）
        if (this.dcrEditData.legendEditName.length === 0) {
          this.dcrEditData.legendEditName = this.dcrOriginalLegend.map(v => { return {id:v, originName: v, previousName: '', newName: '', isReset: false } })
        }
        /* 图例变更名称 开始 */

        /* 没选中的数据的移除处理 */
        if (legendData.legendEdit) {

          // 移除页面上的对应的图例
          for (let i = 0; i < this.dcrEditData.legend.length; i++) {
            if (!legendData.legendEdit.includes(this.dcrEditData.legend[i])) {
              this.dcrEditData.legend.splice(i, 1)
              i--
            }
          }

          // 移除页面上的对应的图例的图表数据
          for (let i = 0; i < dcrSeries.length; i++) {
            if (!legendData.legendEdit.includes(dcrSeries[i].name)) {
              dcrSeries.splice(i, 1)
              i--
            }
          }


          // 判断依据
          for (let i = 0; i < checkData.length; i++) {
            if (!legendData.legendEdit.includes(checkData[i].name)) {
              checkData.splice(i, 1)
              i--
            }
          }
        }

        // // 处理选中折线数据
        // if (checkData.length > 0) {
        //   dcrSeries.forEach((v, index) => {
        //     const handIndex = checkData.findIndex(findItem => findItem.id == v.id)
        //     for (let i = 0; i < v.data.length; i++) {
        //       if (!checkData[handIndex].duplicateData.includes(v.data[i].id)  && v.data[i].value[1] !== '') {
        //         v.data.splice(i, 1)
        //         i--
        //       }
        //     }
        //   })
        // }

        // 如果X轴是类目轴，数字转为字符串
        if(!axisData.xType || axisData.xType === 'category'){
          dcrSeries.forEach(forItem => {
            forItem.data = forItem.data.map(mapItem => { return { id:mapItem.id,name:mapItem.name,value: [mapItem.value[0].toString(),mapItem.value[1]] } } )
          })
        }

        if(!axisData.yType || axisData.yType === 'category'){
          dcrSeries.forEach(forItem => {
            forItem.data = forItem.data.map(mapItem => { return { id:mapItem.id,name:mapItem.name,value: [mapItem.value[0],mapItem.value[1].toString()] } } )
          })
        }


        // 结合图例数据（线+图例都存在）和图例显隐（只存在线，剔除图例）
        const legend = this._getLegend(this.dcrEditData)

        // option
        let dcrOption = {
          backgroundColor: '#ffffff',
          animationDuration: 2000,
          title: {
            text: titleData.chartTitle || this.queryParam.reportBasic.reportName + ' SOC-DCR',
            left: "center",
            top: titleData.titleTop || 10,
            fontSize: 18,
            fontWeight: 500,
            color: "#000"
          },
          grid: {
            show: true,
            top: gridData.gridTop || 45,
            left: gridData.gridLeft || 55,
            right: gridData.gridRight || 30,
            bottom: gridData.gridBottom || 50,
            borderWidth: 0.5,
            borderColor: "#ccc"
          },
          textStyle: {
            fontFamily: "Times New Roman"
          },
          tooltip: {
            trigger: "axis",
            formatter: function (params) {
              var result = params[0].axisValue + "%<br>" // 添加 x 轴的数值
              params.forEach(function (item, dataIndex) {
                var type = " "
                result +=
                  item.marker +
                  item.seriesName +
                  '<div style="width:20px;display: inline-block;"></div><div style="display: inline-block;">' +
                  type +
                  "</div>" +
                  item.value[1] +
                  "mΩ<br>" // 添加每个系列的数值
              })
              return result
            }
          },

          legend: {
            backgroundColor: legendData.legendBgColor || "#f5f5f5",
            data: legend,
            itemWidth: legendData.legendWidth || 20,
            itemHeight: legendData.legendHeight || 5,
            itemGap: legendData.legendGap || 10,
            orient: legendData.legendOrient || 'horizontal',
            top: legendData.legendTop || 50,
            left: legendData.legendLeft || 'center',
            textStyle: {
              fontSize: 14,
              color: "#000000"
            },
            /*formatter: name => {
              return this._getNewSocLegend(name)
            },*/
          },

          xAxis: [
            {
              type: axisData.xType || 'category',
              axisTick: { show: false },
              boundaryGap: false,
              splitLine: {
                show: true, // 显示分隔线
                lineStyle: {
                  type: "solid",
                  width: 0.5
                }
              },
              axisLabel: {
                show: true,
                width: 0.5,
                textStyle: {
                  fontSize: "15",
                  color: "#000000"
                }
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#ccc",
                  width: 0.5
                }
              },
              name: titleData.XTitle || "SOC/%",
              nameLocation: "middle", // 将名称放在轴线的中间位置
              nameGap: 25,
              nameTextStyle: {
                fontSize: 14,
                fontWeight: 500,
                color: "#000000" // 可以根据需要调整字体大小
              }
            }
          ],
          yAxis: [
            {
              type: axisData.yType || 'value',
              name: titleData.YTitle || "DCR (mΩ)",
              position: "left",
              nameGap: titleData.yTitleLetf || 30,
              splitLine: {
                show: true, // 显示分隔线
                lineStyle: {
                  type: "solid",
                  width: 0.5
                }
              },
              axisTick: {
                show: true // 显示刻度
              },
              axisLabel: {
                show: true,
                width: 0.5,
                textStyle: {
                  fontSize: "15",
                  color: "#000000"
                }
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#ccc",
                  width: 0.5
                }
              },
              nameLocation: "middle", // 将名称放在轴线的起始位置
              nameRotate: 90, // 旋转角度，使名称竖排
              nameTextStyle: {
                fontSize: 14, // 可以根据需要调整字体大小
                fontWeight: 500,
                color: "#000000"
              }
            }
          ],

          series: dcrSeries
        }

        // 传回给在线编辑图表，当前图标上有的点
        // this.dcrEditData.duplicateCheckedList = dcrSeries.map(mapItem => mapItem.data.filter(filterItem => filterItem.value[1] !== '').map(mapItem2 => mapItem2.id))

        // 处理坐标轴
        if (axisData.xMin) {
          dcrOption.xAxis[0].min = axisData.xMin
        }
        if (axisData.xMax) {
          dcrOption.xAxis[0].max = axisData.xMax
        }
        if (axisData.xInterval) {
          dcrOption.xAxis[0].interval = axisData.xInterval
        }
        if (axisData.yMin) {
          dcrOption.yAxis[0].min = axisData.yMin
        }
        if (axisData.yMax) {
          dcrOption.yAxis[0].max = axisData.yMax
        }
        if (axisData.yInterval) {
          dcrOption.yAxis[0].interval = axisData.yInterval
        }

        // 坐标轴类型赋值
        this.dcrEditData.xType = dcrOption.xAxis[0].type
        this.dcrEditData.yType = dcrOption.yAxis[0].type

        this.dcrEchart.clear()
        // this.dcrEchart.getZr().off('click')
        // this.dcrEchart.getZr().on('click', params => {
        //   const { target, topTarget } = params

        //   // Z 0:坐标轴
        //   if (topTarget?.z === 0 && this.drawerVisible) {
        //     this.$set(this.dcrCheckObj, 'editObj', 'axis')
        //   }
        //   // Z 3:线 
        //   if (topTarget?.z === 3 && this.drawerVisible) {
        //     const axs = target.parent?.parent?.__ecComponentInfo?.index
        //     this.$set(this.dcrCheckObj, 'tag', axs)
        //     this.$set(this.dcrCheckObj, 'editObj', 'tag')
        //   }
        //   // Z 4:图例
        //   if (topTarget?.z === 4 && this.drawerVisible) {
        //     const axs = target.parent?.__legendDataIndex
        //     this.$set(this.dcrCheckObj, 'editObj', 'legend')
        //   }
        // });
        this.dcrEchart.getZr().off('dblclick')
        this.dcrEchart.getZr().on('dblclick', ({target, topTarget}) => {
          this._handleDblclickEchart(target, topTarget, 'dcr')
        });
        this.dcrEchart.setOption(dcrOption)

        // 如果坐标轴类型为数值轴，则计算出最大值最小值，以及间距
        if (dcrOption.xAxis[0].type === "value") {
          const dcrXAxis = this.dcrEchart.getModel().getComponent("xAxis").axis.scale
          this.dcrEditData.xMin = dcrXAxis._extent[0]
          this.dcrEditData.xMax = dcrXAxis._extent[1]
          this.dcrEditData.xInterval = dcrXAxis._interval
        }

        if (dcrOption.yAxis[0].type === "value") {
          const dcrYAxis = this.dcrEchart.getModel().getComponent("yAxis").axis.scale
          this.dcrEditData.yMin = dcrYAxis._extent[0]
          this.dcrEditData.yMax = dcrYAxis._extent[1]
          this.dcrEditData.yInterval = dcrYAxis._interval

          this.dcrResetOriginal.yMin = dcrYAxis._extent[0]
          this.dcrResetOriginal.yMax = dcrYAxis._extent[1]
          this.dcrResetOriginal.yInterval = dcrYAxis._interval
        }

        if (this.isEditDcrXNum === 0 && axisData.xType === "value") {
          this.isEditDcrXNum++
          this.dcrResetOriginal.xMin = this.dcrEditData.xMin
          this.dcrResetOriginal.xMax = this.dcrEditData.xMax
          this.dcrResetOriginal.xInterval = this.dcrEditData.xInterval
        }

        if(originalParam?.xMax > 0){
					this.dcrResetOriginal.xMin = originalParam.xMin
					this.dcrResetOriginal.xMax = originalParam.xMax
					this.dcrResetOriginal.xInterval = originalParam.xInterval
				}
        if(originalParam?.yMax > 0){
          this.dcrResetOriginal.yMin = originalParam.yMin
          this.dcrResetOriginal.yMax = originalParam.yMax
          this.dcrResetOriginal.yInterval = originalParam.yInterval
        }
      },

      initGrowthOriginalData(checkData = []) {

        let growthEchartsList = _.cloneDeep(this.allDataJson.socOcvEcharts)
        let dcrEchartsList  = _.cloneDeep(this.allDataJson.socDcrEcharts)

        let lineColorList = []
        let seriesList = []
        let seriesOriginal = []
        let checkDataOriginal = []
        const isCheck = checkData.length === 0
        const templateParam = this.reportChartTemplateList['growth'].templateParamJson

        // let duplicateDataOptions = []

        growthEchartsList.sort((item1,item2) => { return item1.temp -  item2.temp })
        const echartsColorList = this.allDataJson.legendList.length <= 2 ? this.echartsColorShortList : this.echartsColorLongList


        for (let i = 0; i < growthEchartsList.length; i++) {
          const temp = growthEchartsList[i].temp

          const have = lineColorList.find(v => v.name === temp)
          if (have == undefined) {
            lineColorList.push({ name: temp, color: echartsColorList[lineColorList.length % echartsColorList.length] })
          }

          const templateContent = templateParam.checkData.length > 0 ? (templateParam.checkData.filter(item => item.id === temp + "℃" + (i + 1))[0] || {}) : {}
          const editContent = checkData[checkData.findIndex(findItem => findItem.id === temp + "℃" + (i + 1))]

          let series = {
            name: temp + "℃",
            index:i + 1,
            temp: temp,
            id: temp + "℃" + (i + 1),
            type: "line",
            barGap: 0,
            markPoint: {
              data: []
            },
            connectNulls:templateContent.connectNulls ??  (isCheck ? false : Boolean(Number(editContent.connectNulls))) ,
            symbol:templateContent.symbol ??  (isCheck ? "rect" : editContent.symbol),
            symbolSize:templateContent.symbolSize ?? ( isCheck ? 5 : editContent.symbolSize),
            lineStyle: {
              width:templateContent.lineWidth ??  (isCheck ? 1 : editContent.lineWidth),
              type:templateContent.lineType ??  (isCheck ? "solid" : editContent.lineType),
              color:
              templateContent.lineColor ?? 
                (isCheck
                  ? lineColorList[lineColorList.findIndex(v => v.name === temp)].color
                  : editContent.lineColor)
            },
            itemStyle: {
              color:
              templateContent.itemColor ??
                (isCheck
                  ? lineColorList[lineColorList.findIndex(v => v.name === temp)].color
                  : editContent.itemColor)
            },
            batteryNum: growthEchartsList[i].batteryNum,
            emphasis: {
              focus: "series"
            },
            data: growthEchartsList[i].data.map((mapItem, index) => { return { id: index, name: mapItem.name, value: mapItem.value } })
          }


          if (checkData.length > 0 && editContent.maxPoint || templateContent.maxPoint) {
            series.markPoint.data.push({ type: "max", name: "Max" })
          }
          if (checkData.length > 0 && editContent.minPoint || templateContent.minPoint) {
            series.markPoint.data.push({ type: "min", name: "Min" })
          }

          seriesOriginal.push({
            id: temp + "℃" + (i + 1),
            index:i + 1,
            soc: temp + "℃",
            name: temp + "℃",
            synchronization:templateContent.synchronization ??  (isCheck ? i : editContent.synchronization),
            maxPoint:templateContent.maxPoint ??  (isCheck ? false : editContent.maxPoint),
            minPoint:templateContent.minPoint ??  (isCheck ? false : editContent.minPoint),
            connectNulls:templateContent.connectNulls ?? false,
            symbol:templateContent.symbol ??  (isCheck ? "rect" : editContent.symbol),
            symbolSize:templateContent.symbolSize ??  (isCheck ? 5 : editContent.symbolSize),
            itemColor:
            templateContent.itemColor ?? 
              (isCheck
                ? lineColorList[lineColorList.findIndex(v => v.name === temp)].color
                : editContent.itemColor),
            lineType:templateContent.lineType ??  (isCheck ? "solid" : editContent.lineType),
            lineWidth:templateContent.lineWidth ??  (isCheck ? 1 : editContent.lineWidth),
            lineColor:
            templateContent.lineColor ?? 
              (isCheck
                ? lineColorList[lineColorList.findIndex(v => v.name === temp)].color
                : editContent.lineColor)
          })
          checkDataOriginal.push({
            id: temp + "℃" + (i + 1),
            index:i + 1,
            soc: temp + "℃",
            name: temp + "℃",
            connectNulls: false,
            synchronization:i,
            maxPoint:false,
            minPoint:false,
            symbol: "rect",
            symbolSize: 5,
            itemColor: lineColorList[lineColorList.findIndex(v => v.name === temp)].color,
            lineType: "solid",
            lineWidth: 1,
            lineColor: lineColorList[lineColorList.findIndex(v => v.name === temp)].color
          })

          // duplicateDataOptions.push({
          //   id: growthEchartsList[i].soc + "℃",
          //   data: growthEchartsList[i].data.map((mapItem, index) => { return { id: index, name: mapItem.name, value: index, label: mapItem.value[1].toString() } })
          // })

          seriesList.push(series)
        }

        // return [seriesOriginal, checkDataOriginal, seriesList, duplicateDataOptions]
        return [seriesOriginal, checkDataOriginal, seriesList]

      },

      initGrowth(
        legendData = {},
        checkData = [],
        axisData = {},
        titleData = {},
        gridData = {}
      ) {
        if (this.growthEchart) this.growthEchart.dispose();
        this.growthEchart = this.echarts.init(this.$refs.growth, "walden",{ devicePixelRatio: 2 })

        const templateParam = this.reportChartTemplateList['growth'].templateParamJson
        const originalParam = this.reportChartTemplateList['growth'].originalParamJson
        const processingResult = this.initGrowthOriginalData(checkData)
        const legendRevealNumResult = this.handleLegendRevealNum(this._getNewSocLegend(this.allDataJson.legendList[0]),501,legendData.legendWidth || 20,legendData.legendGap || 10,true)
        
        this.growthRevealNum = legendRevealNumResult[0]
        this.growthOriginalLegend = _.uniq(_.cloneDeep(this.allDataJson.legendList))

        // 把模板的数据拼接进去
        const { 
          titleData: newTitleData, 
          gridData: newGridData, 
          legendData: newLegendData, 
          axisData: newAxisData, 
          legend: newLegend 
        } = this._getTemplateParams('growth', titleData, gridData, legendData, axisData, legend);
        titleData = newTitleData;
        gridData = newGridData;
        legendData = newLegendData;
        axisData = newAxisData;

        // 首次渲染,所有图例都显示
        if(!legendData.legendRevealList || templateParam.legendData.legendRevealList) this.growthEditData.legendRevealList = templateParam.legendData.legendRevealList ??  _.cloneDeep(this.allDataJson.legendList).slice(0,this.growthRevealNum * 2)
        
        this.growthOriginalSeries = processingResult[0]
        this.growthResetOriginal.checkData = processingResult[1]
        this.growthResetOriginal.series = _.cloneDeep(processingResult[1]) 
        let growthSeries = processingResult[2]
        this.growthEditData.legend = _.cloneDeep(this.allDataJson.legendList)
        this.growthEditData.series = _.cloneDeep(this.growthOriginalSeries)

        if (legendData.legendSort) {
          this.growthEditData.legend = _.cloneDeep(legendData.legendSort)
        }
        this.growthEditData.legendSort = !legendData.legendSort ? _.cloneDeep(this.allDataJson.legendList) : _.cloneDeep(this.growthEditData.legend)

        if (legendData.legendEditName) {
          legendData.legendEditName.forEach(v => {
            if (v.newName && !v.isReset) {
              let temIndex1 = this.growthOriginalLegend.findIndex(findItem => findItem == v.originName)
              this.growthOriginalLegend[temIndex1] = v.newName

              let temIndex2 = this.growthEditData.legend.findIndex(findItem => findItem == v.originName)
              this.growthEditData.legend[temIndex2] = v.newName

              this.growthEditData.series.forEach(findItem => {
                findItem.name = findItem.name == v.originName ? v.newName : findItem.name
              })

              growthSeries.forEach(findItem => {
                findItem.name = findItem.name == v.originName ? v.newName : findItem.name
              })
            }

            if (!v.newName && v.isReset) {
              v.previousName = ''
              v.isReset = false
            }
          })
          this.growthEditData.legendEditName = legendData.legendEditName
        }
        if (this.growthEditData.legendEditName.length === 0) {
          this.growthEditData.legendEditName = this.growthOriginalLegend.map(v => { return { originName: v, previousName: '', newName: '', isReset: false } })
        }

        if (legendData.legendEdit) {
          for (let i = 0; i < this.growthEditData.legend.length; i++) {
            if (!legendData.legendEdit.includes(this.growthEditData.legend[i])) {
              this.growthEditData.legend.splice(i, 1)
              i--
            }
          }

          for (let i = 0; i < growthSeries.length; i++) {
            if (!legendData.legendEdit.includes(growthSeries[i].name)) {
              growthSeries.splice(i, 1)
              i--
            }
          }

          for (let i = 0; i < checkData.length; i++) {
            if (!legendData.legendEdit.includes(checkData[i].name)) {
              checkData.splice(i, 1)
              i--
            }
          }
        }

        // if (checkData.length > 0) {
        //   growthSeries.forEach((v, index) => {
        //     const handIndex = checkData.findIndex(findItem => findItem.id == v.id)
        //     for (let i = 0; i < v.data.length; i++) {
        //       if (!checkData[handIndex].duplicateData.includes(v.data[i].id) && v.data[i].value[1] !== '') {
        //         v.data.splice(i, 1)
        //         i--
        //       }
        //     }
        //   })
        // }

        // 结合图例数据（线+图例都存在）和图例显隐（只存在线，剔除图例）
        const legend = this._getLegend(this.growthEditData)

        let growthOption = {
          backgroundColor: '#ffffff',
          animationDuration: 2000,
          title: {
            text:titleData.chartTitle || this.queryParam.reportBasic.reportName + ' SOC-DCR',
            left: "center",
            top: titleData.titleTop || 10,
            fontSize: 18,
            fontWeight: 500,
            color: "#000"
          },
          grid: {
            show: true,
            top: gridData.gridTop || 45,
            left: gridData.gridLeft || 55,
            right: gridData.gridRight || 30,
            bottom: gridData.gridBottom || 50,
            borderWidth: 0.5,
            borderColor: "#ccc"
          },
          textStyle: {
            fontFamily: "Times New Roman"
          },
          tooltip: {
            trigger: "axis",
            formatter: function (params) {
              var result = params[0].axisValue + "%<br>" // 添加 x 轴的数值
              params.forEach(function (item, dataIndex) {
                var type = " "
                result +=
                  item.marker +
                  item.seriesName +
                  '<div style="width:20px;display: inline-block;"></div><div style="width:10px;display: inline-block;">' +
                  type +
                  "</div>" +
                  item.value[1] +
                  "V<br>" // 添加每个系列的数值
              })
              return result
            }
          },

          legend: {
            data: legend,
            backgroundColor: legendData.legendBgColor || "#f5f5f5",
            itemWidth: legendData.legendWidth || 20,
            itemHeight: legendData.legendHeight || 5,
            itemGap: legendData.legendGap || 10,
            orient: legendData.legendOrient || 'horizontal',
            top: legendData.legendTop || 50,
            left: legendData.legendLeft || 'center',
            textStyle: {
              fontSize: 14,
              color: "#000000"
            },
            /*formatter: name => {
              return this._getNewSocLegend(name)
            },*/
          },

          xAxis: [
            {
              type: axisData.xType || 'category',
              boundaryGap: false,
              axisTick: { show: false },
              splitLine: {
                show: true, // 显示分隔线
                lineStyle: {
                  type: "solid",
                  width: 0.5
                }
              },
              axisLabel: {
                show: true,
                textStyle: {
                  fontSize: "13",
                  color: "#000000"
                }
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#ccc",
                  width: 0.5
                }
              },
              name: titleData.XTitle || "SOC/%",
              nameLocation: "middle", // 将名称放在轴线的中间位置
              nameGap: 25,
              nameTextStyle: {
                fontSize: 14, // 可以根据需要调整字体大小
                fontWeight: 500,
                color: "#000000"
              }
            }
          ],
          yAxis: [
            {
              type: axisData.yType || 'value',
              name: titleData.YTitle || "OCV/V",
              position: "left",
              nameGap: titleData.yTitleLetf || 30,
              splitLine: {
                show: true, // 显示分隔线
                lineStyle: {
                  type: "solid",
                  width: 0.5
                }
              },
              min:3,
              max:4.5,
              interval:0.3,
              axisTick: {
                show: true // 显示刻度
              },
              axisLabel: {
                show: true,
                textStyle: {
                  fontSize: "13",
                  color: "#000000"
                }
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#ccc",
                  width: 0.5
                }
              },
              nameLocation: "middle", // 将名称放在轴线的起始位置
              nameRotate: 90, // 旋转角度，使名称竖排
              nameTextStyle: {
                fontSize: 14, // 可以根据需要调整字体大小
                fontWeight: 500,
                color: "#000000"
              }
            }
          ],
          series: growthSeries
        }

        // 传回给在线编辑图表，当前图标上有的点
        // this.growthEditData.duplicateCheckedList = growthSeries.map(mapItem => mapItem.data.filter(filterItem => filterItem.value[1] !== '').map(mapItem2 => mapItem2.id))


        if (axisData.xMin) {
          growthOption.xAxis[0].min = axisData.xMin
        }
        if (axisData.xMax) {
          growthOption.xAxis[0].max = axisData.xMax
        }
        if (axisData.xInterval) {
          growthOption.xAxis[0].interval = axisData.xInterval
        }
        if (axisData.yMin) {
          growthOption.yAxis[0].min = axisData.yMin
        }
        if (axisData.yMax) {
          growthOption.yAxis[0].max = axisData.yMax
        }
        if (axisData.yInterval) {
          growthOption.yAxis[0].interval = axisData.yInterval
        }

        this.growthEditData.xType = growthOption.xAxis[0].type
        this.growthEditData.yType = growthOption.yAxis[0].type

        this.growthEchart.clear()

        // this.growthEchart.getZr().off('click')
        // this.growthEchart.getZr().on('click', params => {
        //   const { target, topTarget } = params

        //   if (topTarget?.z === 0 && this.drawerVisible) {
        //     this.$set(this.growthCheckObj, 'editObj', 'axis')
        //   }
        //   if (topTarget?.z === 3 && this.drawerVisible) {
        //     const axs = target.parent?.parent?.__ecComponentInfo?.index
        //     this.$set(this.growthCheckObj, 'tag', axs)
        //     this.$set(this.growthCheckObj, 'editObj', 'tag')
        //   }
        //   if (topTarget?.z === 4 && this.drawerVisible) {
        //     const axs = target.parent?.__legendDataIndex
        //     this.$set(this.growthCheckObj, 'editObj', 'legend')
        //   }
        // });

        this.growthEchart.getZr().off('dblclick')
        this.growthEchart.getZr().on('dblclick', ({target, topTarget}) => {
          this._handleDblclickEchart(target, topTarget, 'growth')
        });
        this.growthEchart.setOption(growthOption)

        if (growthOption.xAxis[0].type === "value") {
          const growthXAxis = this.growthEchart.getModel().getComponent("xAxis").axis.scale
          this.growthEditData.xMin = growthXAxis._extent[0]
          this.growthEditData.xMax = growthXAxis._extent[1]
          this.growthEditData.xInterval = growthXAxis._interval
        }

        if (growthOption.yAxis[0].type === "value") {
          const growthYAxis = this.growthEchart.getModel().getComponent("yAxis").axis.scale
          this.growthEditData.yMin = growthYAxis._extent[0]
          this.growthEditData.yMax = growthYAxis._extent[1]
          this.growthEditData.yInterval = growthYAxis._interval

          this.growthResetOriginal.yMin = growthYAxis._extent[0]
          this.growthResetOriginal.yMax = growthYAxis._extent[1]
          this.growthResetOriginal.yInterval = growthYAxis._interval
        }
        if (this.isEditGrowthXNum === 0 && axisData.xType === "value") {
          this.isEditGrowthXNum++
          this.growthResetOriginal.xMin = this.growthEditData.xMin
          this.growthResetOriginal.xMax = this.growthEditData.xMax
          this.growthResetOriginal.xInterval = this.growthEditData.xInterval
        }
        if(originalParam?.xMax > 0){
					this.growthResetOriginal.xMin = originalParam.xMin
					this.growthResetOriginal.xMax = originalParam.xMax
					this.growthResetOriginal.xInterval = originalParam.xInterval
				}

        if(originalParam?.yMax > 0){
          this.growthResetOriginal.yMin = originalParam.yMin
          this.growthResetOriginal.yMax = originalParam.yMax
          this.growthResetOriginal.yInterval = originalParam.yInterval
        }
      },

      updateData() {
        testReportUpdateDate(this.originData, this.id).then(res => {
          this.init()
        })
      },

      // 生成
      handleDrawerSubmit(value) {
        const legendData = {
          legendEdit: value.legendList,
          legendRevealList: value.legendRevealList,
          legendWidth: value.legendWidth,
          legendHeight: value.legendHeight,
          legendGap: value.legendGap,
          legendSort: value.legendSort, // 图例排序
          legendEditName: value.legendEditName,
          legendBgColor: value.legendBgColor,
          legendOrient: value.legendOrient,
          legendTop: value.legendTop,
          legendLeft: value.legendLeft,
        }

        const axisData = {
          xMin: value.xMin,
          xMax: value.xMax,
          xInterval: value.xInterval,
          xType: value.xType,

          yMin: value.yMin,
          yMax: value.yMax,
          yInterval: value.yInterval,
          yType: value.yType
        }

        const titleData = {
          chartTitle: value.chartTitle,
          XTitle: value.XTitle,
          YTitle: value.YTitle,
          titleTop: value.titleTop,
          yTitleLetf: value.yTitleLetf,
        }

        const gridData = {
          gridTop: value.gridTop,
          gridLeft: value.gridLeft,
          gridRight: value.gridRight,
          gridBottom: value.gridBottom,
        }

        // 赋值的数组
        const assignArr = ['chartTitle', 'XTitle', 'YTitle', 'titleTop','yTitleLetf','legendRevealList', 'legendWidth', 'legendHeight', 'legendGap', 'legendEditName', 'legendOrient','legendBgColor', 'legendTop', 'legendX', 'xMin', 'xMax', 'xInterval', 'xType',
          'yMin', 'yMax', 'yInterval', 'yType', 'synchronization', 'gridTop', 'gridLeft', 'gridRight', 'gridBottom',"targetEditObj"]

        this._handleTemplateParams(value)
        
        if (this.editObj === "dcr") {
          this.dcrEditData.series = _.cloneDeep(value.checkData)

          for (let i = 0; i < assignArr.length; i++) {
            this.dcrEditData[assignArr[i]] = value[assignArr[i]]
          }

          this.initDcr(
            legendData,
            value.checkData,
            axisData,
            titleData,
            gridData
          )
        } else if (this.editObj === "growth") {
          this.growthEditData.series = _.cloneDeep(value.checkData)

          for (let i = 0; i < assignArr.length; i++) {
            this.growthEditData[assignArr[i]] = value[assignArr[i]]
          }

          this.initGrowth(
            legendData,
            value.checkData,
            axisData,
            titleData,
            gridData
          )
        }

        this.$forceUpdate()

        let chartTemplateParams = {}
        if(!this.reportChartTemplateList[this.editObj].templateId){
          chartTemplateParams = {
            targetChart:this.editObj,
            templateName:'报告ID修改默认模板',
            reportId:this.$route.query.id,
            originalParamJson:JSON.stringify(this.editObj === 'dcr' ? this.dcrResetOriginal : this.growthResetOriginal),
            templateParamJson:JSON.stringify(this.reportChartTemplateList[this.editObj].templateParamJson),
          }
          this.reportChartTemplateList[this.editObj].originalParamJson = this.editObj === 'dcr' ? this.dcrResetOriginal : this.growthResetOriginal
          this.saveChartTemplate(chartTemplateParams)
        }else{
          chartTemplateParams = {
            id:this.reportChartTemplateList[this.editObj].templateId,
            templateParamJson:JSON.stringify(this.reportChartTemplateList[this.editObj].templateParamJson),
          }
          if((this.isEditDcrXNum === 1 ||  this.isEditGrowthXNum === 1) && this.reportChartTemplateList[this.editObj].originalParamJson.xMax == 0 ){
						chartTemplateParams.originalParamJson = JSON.stringify(this.editObj === 'dcr' ? this.dcrResetOriginal : this.growthResetOriginal)
						this.reportChartTemplateList[this.editObj].originalParamJson = this.editObj === 'dcr' ? this.dcrResetOriginal : this.growthResetOriginal
					}
        this.updateChartTemplate(chartTemplateParams)
        }
      },

      _handleTemplateParams(value){
        const isEdit = !!value.targetEditObj
        const templateParam = this.reportChartTemplateList[this.editObj].templateParamJson

        if(isEdit && !['legendList','legendEditName','legendSort','legendRevealList'].includes(value.targetEditObj)){
          if(value.targetEditIndex === undefined){
            templateParam[value.targetEditObj] = value[value.targetEditObj]
          }else if(value.targetEditIndex === 'all'){
            for(let i = 0; i < value.checkData.length ; i++){
              if(templateParam.checkData[i] === undefined) templateParam.checkData[i] = {}
              templateParam.checkData[i] = {
                ...templateParam.checkData[i],
                id:value.checkData[i].id,
                [value.targetEditObj]:value.checkData[i][value.targetEditObj]
              }
            }
            templateParam.allData[value.targetEditObj] = value.allData[value.targetEditObj]
          }else{
            let haveIndex = templateParam.checkData.findIndex(item => item.id === value.checkData[value.targetEditIndex].id)
            if(haveIndex === -1){
              templateParam.checkData.push({id:value.checkData[value.targetEditIndex].id})
              haveIndex = templateParam.checkData.length - 1
            } 
            templateParam.checkData[haveIndex][value.targetEditObj] = value.checkData[value.targetEditIndex][value.targetEditObj]
          }
        }

        
        if(!isEdit){
          if (value.targetResetIndex === undefined || value.targetResetIndex === 'yDecimalNum') {
            delete templateParam[value.targetResetObj]

            // 如果是XY轴的类型重置，需同步去除最大最小值
            if(value.targetResetObj === 'xType' && value.xType === 'category'){
              delete templateParam.xMin
              delete templateParam.xMax
              delete templateParam.xInterval
            }
            if(value.targetResetObj === 'yType' && value.yType === 'category'){
              delete templateParam.yMin
              delete templateParam.yMax
              delete templateParam.yInterval
            }
          }else if(value.targetResetIndex === 'all'){
            for(let i = 0; i < value.checkData.length ; i++){
              delete templateParam.checkData[i][value.targetResetObj]
            }
            delete templateParam.allData[value.targetResetObj]
          }else{
            let haveIndex = templateParam.checkData.findIndex(item => item.id === value.checkData[value.targetResetIndex].id)
            delete templateParam.checkData[haveIndex][value.targetResetObj]
          }
        }

        // 图例-数据
      if(value.targetEditObj === 'legendList'){
        templateParam.legendData.legendIndeterminate = value.legendIndeterminate
        templateParam.legendData.checkAll = value.checkAll
        templateParam.legendData.legendList = value.legendList
        templateParam.legendData.legendOptions = templateParam.legendData.legendOptions ?? (this.editObj === 'dcr' ? this.dcrOriginalLegend : this.growthOriginalLegend)
      }

      // 图例-名称
      if(value.targetEditObj === 'legendEditName'){
        templateParam.legendData.legendList = value.legendList // 需同步更名后的数组
        templateParam.legendData.legendEditName = value.legendEditName 
        templateParam.legendData.legendRevealList = value.legendRevealList 
        templateParam.legendData.legendRevealOptions = value.legendRevealOptions

        // 找到改名的那根线，存储修改后的线名称
        const haveIndex =  templateParam.checkData.findIndex(item => item.id === value.checkData[value.targetEditIndex].id)
        if(haveIndex === -1){
          templateParam.checkData.push({id:value.checkData[value.targetEditIndex].id,name:value.checkData[value.targetEditIndex].name})
        }else{
          templateParam.checkData[haveIndex].name=value.checkData[value.targetEditIndex].name
        }

      }

      // 如果有图例-排序的修改
      if(value.targetEditObj === 'legendSort'){
        templateParam.legendData.legendSort = value.legendSort 
      }

      if(value.targetEditObj === 'legendNameType'){
        templateParam.legendData.legendSort = value.legendSort 
        templateParam.legendData.legendList = value.legendList
      }

      // 图例-显隐
      if(value.targetEditObj === 'legendRevealList'){
        templateParam.legendData.legendRevealIndeterminate = value.legendRevealIndeterminate
        templateParam.legendData.legendRevealcheckAll = value.legendRevealcheckAll
        templateParam.legendData.legendRevealList = value.legendRevealList
        templateParam.legendData.legendRevealOptions = value.legendRevealOptions
      }


      },

      _getTemplateParams(targetObj,titleData,gridData,legendData,axisData,legend){
        const templateParam = this.reportChartTemplateList[targetObj].templateParamJson

        const titleList = ['chartTitle', 'XTitle', 'YTitle', 'YTitle2', 'titleTop', 'yTitleLetf', 'yTitleRight']
        titleList.forEach(item => {
          titleData[item] = templateParam[item] ?? titleData[item]
        })

        const legendList = ['legendBgColor','legendOrient','legendTop', 'legendLeft','legendWidth', 'legendHeight', 'legendGap']
        legendList.forEach(item => {
          legendData[item] = templateParam[item] ?? legendData[item]
        })

        const gridList = ['gridTop', 'gridLeft', 'gridRight', 'gridBottom']
        gridList.forEach(item => {
          gridData[item] = templateParam[item] ?? gridData[item]
        })

        const axisList = ['xType','xMin', 'xMax', 'xInterval', 'yType', 'yMin', 'yMax', 'yInterval', 'yType2', 'yMin2', 'yMax2', 'yInterval2']
        axisList.forEach(item => {
          axisData[item] = templateParam[item] ?? axisData[item]
        })
        // 如果有图例-数据的修改
      if(templateParam.legendData.legendList){
        legendData.legendEdit = templateParam.legendData.legendList
        if(templateParam.legendData.checkAll !== undefined) this[`${targetObj}EditData`].checkAll = templateParam.legendData.checkAll
        if(templateParam.legendData.legendIndeterminate !== undefined) this[`${targetObj}EditData`].legendIndeterminate = templateParam.legendData.legendIndeterminate
      }

      // 如果有图例-名称的修改
      if(templateParam.legendData.legendEditName){
        legendData.legendEditName = templateParam.legendData.legendEditName
        legendData.legendRevealList = templateParam.legendData.legendRevealList
      }

      // 如果有图例-名称的修改
      if(templateParam.legendData.legendSort){
        legendData.legendSort = templateParam.legendData.legendSort
      }

       // 如果有图例-显隐的修改
      if(templateParam.legendData.legendRevealList){
        templateParam.legendData.legendRevealList = templateParam.legendData.legendRevealList
        if(templateParam.legendData.legendRevealcheckAll !== undefined) this[`${targetObj}EditData`].legendRevealcheckAll = templateParam.legendData.legendRevealcheckAll
        if(templateParam.legendData.legendRevealIndeterminate !== undefined) this[`${targetObj}EditData`].legendRevealIndeterminate = templateParam.legendData.legendRevealIndeterminate
        if(templateParam.legendData.legendRevealOptions !== undefined) this[`${targetObj}EditData`].legendRevealOptions = templateParam.legendData.legendRevealOptions
      }

        return {titleData,gridData,legendData,axisData,legend}
      },

      // 重置
      handleDrawerReset(editObj = null) {

        if(editObj !== null) this.editObj = editObj

        this.$confirm({
          title: '请确认是否重置图表?',
          content: '图表重置后，图表修改内容无法恢复',
          okText: '重置',
          cancelText: '取消',
          onOk:async () => {
            await this.deleteChartTemplate({ reportId:this.$route.query.id,id:this.reportChartTemplateList[this.editObj].templateId,targetChart:this.editObj },false)
            if (this.editObj === "dcr") {
              this.dcrEditData = this._getInitData('dcr','edit')
              this.dcrEditData.series = _.cloneDeep(this.dcrOriginalSeries)
              this.initDcr()
            }else{
              this.growthEditData = this._getInitData('growth','edit')
              this.growthEditData.series = _.cloneDeep(this.growthOriginalSeries)
              this.initGrowth()
            }
            this.drawerVisible = false
            this.$message.success("重置成功")
          },
          onCancel() {}
        });
      },

      // 重新选择模板
      async handleChangeTemplate(targetObj){
        await this.getChartTemplateRelationList(this.$route.query.id,[targetObj])
        if (targetObj === "dcr") {
              this.dcrEditData = this._getInitData('dcr','edit')
              this.dcrEditData.series = _.cloneDeep(this.dcrOriginalSeries)
              this.initDcr()
            }else{
              this.growthEditData = this._getInitData('growth','edit')
              this.growthEditData.series = _.cloneDeep(this.growthOriginalSeries)
              this.initGrowth()
            }
            this.drawerVisible = false
      },


      // 获取编辑图表数据、原始图表数据
      _getInitData(targetObj,type = 'original'){

        const isEdit = type === 'edit'
        const templateParam = this.reportChartTemplateList[targetObj].templateParamJson 

        const options = {
          
          XTitle:isEdit && templateParam.XTitle ? templateParam.XTitle :   'SOC/%', //X轴标题
          titleTop:isEdit && templateParam.titleTop ? templateParam.titleTop :  10,
          yTitleLetf:isEdit && templateParam.yTitleLetf ? templateParam.yTitleLetf :  30,

          legendBgColor:isEdit && templateParam.legendBgColor ? templateParam.legendBgColor : '#f5f5f5',
          legendOrient:isEdit && templateParam.legendOrient ? templateParam.legendOrient :  'horizontal',
          legendTop:isEdit && templateParam.legendTop ? templateParam.legendTop :  50,
          legendWidth:isEdit && templateParam.legendWidth ? templateParam.legendWidth :  20,
          legendHeight:isEdit && templateParam.legendHeight ? templateParam.legendHeight :  5,
          legendGap:isEdit && templateParam.legendGap ? templateParam.legendGap :  10,//图例间隙

          gridTop:isEdit && templateParam.gridTop ? templateParam.gridTop :  45,
          gridLeft:isEdit && templateParam.gridLeft ? templateParam.gridLeft :  55,
          gridRight:isEdit && templateParam.gridRight ? templateParam.gridRight :  30,
          gridBottom:isEdit && templateParam.gridBottom ? templateParam.gridBottom :  50,

          xType:isEdit && templateParam.xType ? templateParam.xType :  "category",
          xMin:isEdit && templateParam.xMin ? templateParam.xMin :  0,
          xMax:isEdit && templateParam.xMax ? templateParam.xMax :  0,
          xInterval:isEdit && templateParam.xInterval ? templateParam.xInterval :  0,

          yType:isEdit && templateParam.yType ? templateParam.yType :  "value",
          yMin:isEdit && templateParam.yMin ? templateParam.yMin :  0,
          yMax:isEdit && templateParam.yMax ? templateParam.yMax :  0,
          yInterval:isEdit && templateParam.yInterval ? templateParam.yInterval :  0,
        }
        if(type === 'edit'){
          options.series = []
          options.legend = []
          options.legendSort = []
          options.legendEditName = []
          options.allData = templateParam.allData ?? {}
          if(templateParam.legendLeft) options.legendLeft = templateParam.legendLeft
        }
        if(type === 'original'){
          options.checkData = []
        }
        options.chartTitle = isEdit && templateParam.chartTitle ? templateParam.chartTitle : (this.queryParam.reportBasic.reportName + (targetObj === 'dcr' ? " SOC-DCR" : " SOC-OCV"))
        options.YTitle =isEdit && templateParam.YTitle ? templateParam.YTitle : (targetObj === 'dcr' ? "DCR (mΩ)" : "OCV/V")

        return options
      },
    }
  }
</script>
<style lang="less" scoped>
  @import "./css/preview.less";
</style>