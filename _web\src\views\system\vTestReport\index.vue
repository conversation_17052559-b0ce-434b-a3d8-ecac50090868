<template>
	<div class="wrapper" :style="`height:${isInside ?  '' : 'calc(100vh - 40px)'  } ;`">
		<div class="flex-sb-center-row">
			<div class="head_title">V圆柱测试模型搭建</div>
			<div style="position: relative;">
				<div class="normal-btn" :class="{
						'streamer-btn anima':
							orderData.length !== 0 && isBasicInfo && socDcirCycleData.length !== 0 && socDcirFilterData.length !== 0
					}" @click="exportData">
					<span></span>
					<span></span>
					<span></span>
					<span></span>
					完成模型搭建
				</div>
			</div>
		</div>

		<div class="all-wrapper">
			<div class="left-content">
				<div class="block " id="export">
					<div class="flex-sb-center-row">
						<strong>一、测试报告选择</strong>
						<a-icon @click="handleClose(3)" :type="isCloseThree ? 'down' : 'up'" />
					</div>
					<div v-show="!isCloseThree" class="mt10">
						<a-form-item label="报告类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-select show-search option-filter-prop="children" style="width: 100%"
								:filter-option="filterOption" v-model="reportType">
                <a-select-option value="循环DCR">
									循环DCR
								</a-select-option>
                <a-select-option value="SOC-DCR">
                  SOC-DCR
                </a-select-option>
								<a-select-option value="内阻-DCR">
									内阻-DCR
								</a-select-option>
								<!-- <a-select-option value="SOC-OCV map">
									SOC-OCV map
								</a-select-option> -->
								<a-select-option value="SOC-OCV map charge">
									SOC-OCV map charge
								</a-select-option>
								<a-select-option value="SOC-OCV map discharge">
									SOC-OCV map discharge
								</a-select-option>
								<a-select-option value="循环">
									循环
								</a-select-option>
								<a-select-option value="倍率-倍率充放电&恒功率放电">
									倍率-倍率充放电&恒功率放电
								</a-select-option>
								<a-select-option value="倍率-stress">
									倍率-stress
								</a-select-option>
								<a-select-option value="倍率-脉冲放电">
									倍率-脉冲放电
								</a-select-option>
							</a-select>
						</a-form-item>

						<a-form-item label="产品" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-select style="width: 100%" placeholder="请选择产品" @change="changeProduct" v-model="issueId"
								:filter-option="filterOption" show-search>
								<a-select-option v-for="(item, i) in productList" :key="item.issueId"
									:value="item.issueId">
									{{ item.productName }}
								</a-select-option>
							</a-select>
						</a-form-item>

						<a-form-item label="电流" :labelCol="labelCol" :wrapperCol="wrapperCol"
							v-if="reportType == '循环DCR' || reportType == '倍率-脉冲放电' || reportType == '倍率-脉放map'">
							<a-input-number placeholder="请输入电流" style="width: 100%;" v-model="reportBasic.current" />
						</a-form-item>
						<a-form-item label="充电" :labelCol="labelCol" :wrapperCol="wrapperCol" v-if="reportType == '循环'">
							<a-input-number placeholder="请输入充电电流/充电倍率" style="width: 80%;"
								v-model="reportBasic.charge" />
							<a-select style="width: 20%" placeholder="单位" v-model="reportBasic.chargeUnit">
								<a-select-option value="A">
									A
								</a-select-option>
								<a-select-option value="C">
									C
								</a-select-option>
							</a-select>
						</a-form-item>
						<a-form-item label="放电" :labelCol="labelCol" :wrapperCol="wrapperCol" v-if="reportType == '循环'">
							<a-input-number placeholder="请输入充电电流/充电倍率" style="width: 80%;"
								v-model="reportBasic.discharge" />
							<a-select style="width: 20%" placeholder="单位" v-model="reportBasic.dischargeUnit">
								<a-select-option value="A">
									A
								</a-select-option>
								<a-select-option value="C">
									C
								</a-select-option>
								<a-select-option value="W">
									W
								</a-select-option>
							</a-select>
						</a-form-item>
						<a-form-item label="单位" :labelCol="labelCol" :wrapperCol="wrapperCol"
							v-if="reportType == '倍率-倍率充放电&恒功率放电'">
							<a-select show-search option-filter-prop="children" style="width: 100%"
								v-model="reportBasic.cRateUnit" @change="cRateUnitChange">
								<a-select-option value="A">
									A
								</a-select-option>
								<a-select-option value="W">
									W
								</a-select-option>
							</a-select>
						</a-form-item>
						<a-form-item label="测试数量" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-input placeholder="请输入测试数量" v-model="reportBasic.num" />
						</a-form-item>
						<a-form-item label="测试温度" :labelCol="labelCol" :wrapperCol="wrapperCol" v-if="reportType !='SOC-DCR'">
							<a-input placeholder="请输入测试温度" v-model="reportBasic.temp" />
						</a-form-item>
						<a-form-item label="报告名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-input placeholder="材料体系（INR/ICR） +  电芯尺寸 + / + 电芯型号" v-model="reportBasic.reportName" />
						</a-form-item>

						<a-form-item label="测试方法" :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-textarea placeholder="请输入测试方法" v-model="reportBasic.testMethod" />
						</a-form-item>
					</div>
				</div>
				<div class="block mt10">
					<div class="flex-sb-center-row">
						<strong>二、测试数据选择</strong>

            <div style="float: right">
              <a-popconfirm placement="topRight" title="确认删除？" @confirm="deleteSelect">
                <a-button style="margin-top: -5px;margin-right: 15px;">删除</a-button>
              </a-popconfirm>

              <a-icon @click="handleClose(1)" :type="isCloseOne ? 'down' : 'up'" />
            </div>

					</div>
          <div ref="tableContainer">


					  <a-table v-show="!isCloseOne" class="mt10" bordered :columns="orderColumns" :data-source="orderData"
                   :row-selection="deleteRowSelection"

						childrenColumnName="child" :rowKey="record => record.uuid" :pagination="false">
						<template slot="celltestcode" slot-scope="text, record, index, columns">
							<a @click="openStepData(record)" v-if="record.flowId != null" style="text-align: center">{{
								text }}</a>
							<span v-else style="text-align: center">{{ text }}</span>
						</template>


            <template slot="action" slot-scope="text, record, index, columns">
              <a-tooltip placement="top" title="删除" arrow-point-at-center>
                <a @click="deleteDataOne(record, index)" style="text-align: center">
                  <a-icon type="delete" style="font-size: large;margin-right: 3px"/>
                </a>
              </a-tooltip>

              <a-tooltip placement="top" title="拖拽修改位置" arrow-point-at-center>
                <a-icon style="color: #1890ff; font-size: large; cursor: move;" heignt="18" width="18" class="drag" type="unordered-list"/>
              </a-tooltip>
<!--              <a-tooltip placement="top" title="上移" arrow-point-at-center v-if="index != 0">
                <a @click="moveUp(orderData,index)" style="text-align: center">
                  <a-icon type="arrow-up" style="font-size: large;margin-right: 3px"/>
                </a>
              </a-tooltip>
              <a-tooltip placement="top" title="下移" arrow-point-at-center v-if="index != orderData.length -1">
                <a @click="moveDown(orderData,index)" style="text-align: center">
                  <a-icon type="arrow-down" style="font-size: large"/>
                </a>
              </a-tooltip>-->
            </template>

						<template slot="dataPath" slot-scope="text, record, index, columns">
							<a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
								<template slot="title">
									{{ text ? text : record.testcontent }}
								</template>
								{{ text ? text : record.testcontent }}
							</a-tooltip>
						</template>

						<template slot="footer">
							<div class="footer-btn" :class="{
									'plus-btn': orderData.length === 0
								}" @click="openTestOrder">
								<span></span>
								<span></span>
								<span></span>
								<span></span>
								<a-icon type="plus"></a-icon>
							</div>
						</template>
					</a-table>

          </div>
        </div>
			</div>
			<div class="right-content">
				<div class="block">
					<div>
						<strong>三、报告生成逻辑 </strong>
					</div>
					<div class="mt10">
						<div v-if="reportType == '循环DCR'">
							<div class="flex-sb-center-row">
								<div>①循环周数-循环号填写</div>
								<div>
									<a-button class="mr10" size="small" @click="handleSynchronization">同步</a-button>
									<a-icon @click="handleClose(4)" :type="isCloseFour ? 'down' : 'up'" />
								</div>
							</div>

							<a-table v-if="!isCloseFour" class="mt10" bordered :columns="socDcirCylclesColumns"
								:data-source="socDcirCycleData" :pagination="false">
								<template slot="action" slot-scope="text, record, index, columns">
									<a @click="deleteCycle(record)">删除</a>
								</template>
								<template slot="cycleId" slot-scope="text, record, index, columns">
									<a-input style="width: 100%" v-model="record[columns.dataIndex]"
										@paste="handleCycleIdPaste($event, index)"
										@blur="handleCycleIdInput($event, index)" />
								</template>
								<template slot="cycle" slot-scope="text, record, index, columns">
									<a-input style="width: 100%" v-model="record[columns.dataIndex]"
										@paste="handleCycleIdPaste($event, index,1)"
										@blur="handleCycleIdInput($event, index, 1)" />
								</template>
								<template slot="footer">
									<div class="footer-btn">
										<div class="footer-btn" :class="{
												'plus-btn': orderData.length !== 0 && socDcirCycleData.length === 0
											}" @click="addSocDcirCycleData">
											<span></span>
											<span></span>
											<span></span>
											<span></span>
											<a-icon type="plus"></a-icon>
										</div>
									</div>
								</template>
							</a-table>

							<!-- SOC-DCR -->
							<div class="flex-sb-center-row mt10">
								<div>②数据获取逻辑</div>
								<a-icon @click="handleClose(5)" :type="isCloseFive ? 'down' : 'up'" />
							</div>
							<div class="mt10">
								<span>排序规则 : </span>
								<a-radio-group v-model="socDcirSort" @change="handleSort">
									<a-radio :value="1">
										正序
									</a-radio>
									<a-radio :value="2">
										倒序
									</a-radio>
								</a-radio-group>
							</div>
							<a-table v-if="!isCloseFive" class="mt10" bordered :columns="socDcirColumns"
								:data-source="socDcirFilterData" :pagination="false">
								<template slot="tableTitle">
									<a-select v-model="allTable" class="tableTitle"
										@change="handleAllTableSelect($event)" :options="socDcirTableType" />
								</template>

								<div slot="filterDropdown"
									slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
									style="padding: 8px">
									<a-select v-model="allTable" placeholder="数据表选择"
										style="width: 188px; margin-bottom: 8px; display: block;"
										@change="handleAllTableSelect($event)" :options="socDcirTableType" />
								</div>
								<a-icon slot="filterIcon" slot-scope="filtered" type="down"
									:style="{ color: filtered ? '#108ee9' : undefined }" />
								<template slot="footer">
									<div class="footer-btn" style="justify-content: flex-start;">
										<div class="footer-btn" :class="{
												'plus-btn':
													orderData.length !== 0 && socDcirCycleData.length !== 0 && socDcirFilterData.length === 0
											}" @click="addSocDcirFilterData">
											<span></span>
											<span></span>
											<span></span>
											<span></span>
											<a-icon type="plus"></a-icon>
										</div>

									</div>
								</template>

								<template slot="table" slot-scope="text, record, index, columns">
									<a-select style="width: 100%;text-align: center" v-model="record[columns.dataIndex]"
										@change="handleTableSelect($event, record, index)"
										:options="socDcirTableType" />
								</template>

								<template slot="stepId" slot-scope="text, record, index, columns">
									<a-input style="width: 100%" v-model="record[columns.dataIndex]"
										@paste="handleSocPaste($event, index)" />
								</template>

								<template slot="stepTime" slot-scope="text, record, index, columns">
									<a-input :placeholder="record.table === 'step' ? '选填' : ''" style="width: 100%"
										v-model="record[columns.dataIndex]" @paste="handleSocPaste($event, index, 1)" />
								</template>
							</a-table>
						</div>
						<div v-if="reportType == 'SOC-DCR'">
							<div class="flex-sb-center-row">
								<div>①SOC-循环号填写</div>
								<div>
									<a-icon @click="handleClose(4)" :type="isCloseFour ? 'down' : 'up'" />
								</div>
							</div>

							<a-table v-if="!isCloseFour" class="mt10" bordered :columns="socDcrColumns"
								:data-source="socDcrData" :pagination="false">
								<template slot="action" slot-scope="text, record, index, columns">
									<a @click="deleteSoc(record)">删除</a>
								</template>
								<template slot="cycleId" slot-scope="text, record, index, columns">
									<a-input style="width: 100%" v-model="record[columns.dataIndex]"
										@paste="handleSocDcrPaste($event, index)"/>
								</template>
								<template slot="soc" slot-scope="text, record, index, columns">
									<a-input style="width: 100%" v-model="record[columns.dataIndex]"
										@paste="handleSocDcrPaste($event, index,1)"/>
								</template>
								<template slot="footer">
									<div class="footer-btn">
										<div class="footer-btn" :class="{
												'plus-btn': orderData.length !== 0 && socDcirCycleData.length === 0
											}" @click="addSocCycleData">
											<span></span>
											<span></span>
											<span></span>
											<span></span>
											<a-icon type="plus"></a-icon>
										</div>
									</div>
								</template>
							</a-table>

							<!-- SOC-DCR -->
							<div class="flex-sb-center-row mt10">
								<div>②数据获取逻辑</div>
								<a-icon @click="handleClose(5)" :type="isCloseFive ? 'down' : 'up'" />
							</div>
							<div class="mt10">
								<span>排序规则 : </span>
								<a-radio-group v-model="socDcirSort" @change="handleSocDcrSort">
									<a-radio :value="1">
										正序
									</a-radio>
									<a-radio :value="2">
										倒序
									</a-radio>
								</a-radio-group>
							</div>
							<a-table v-if="!isCloseFive" class="mt10" bordered :columns="socDcrDataColumns"
								:data-source="orderData" :pagination="false">

                <template slot="socDcrStepId1" slot-scope="text, record, index, columns">
                  <a-input-number style="width: 100%" v-model="record[columns.dataIndex]" @change="$event => socDcrStepId1Change($event,index)"/>
                </template>
                <template slot="socDcrStepId2" slot-scope="text, record, index, columns">
                  <a-input-number style="width: 100%" v-model="record[columns.dataIndex]" @change="$event => socDcrStepId2Change($event,index)"/>
                </template>
							</a-table>
						</div>

						<div v-if="reportType == '内阻-DCR'">
							<div class="flex-sb-center-row">
								<div>①测试数据温度填写</div>
								<div>
									<a-icon @click="handleClose(4)" :type="isCloseFour ? 'down' : 'up'" />
								</div>
							</div>

							<a-table v-if="!isCloseFour" class="mt10" bordered childrenColumnName="child"
								:columns="resDcirTempColumns" :data-source="resDcirTempData" :pagination="false">
								<template slot="temp" slot-scope="text, record, index, columns">
									<a-input-number :formatter="value => `${value}℃`"
										:parser="value => value.replace('℃', '')" :step="1" :min="-273" :max="1000"
										:precision="0" style="width: 100%" v-model="record[columns.dataIndex]" />
								</template>
							</a-table>

							<div class="flex-sb-center-row mt10">
								<div>②数据获取逻辑</div>
								<a-icon @click="handleClose(5)" :type="isCloseFive ? 'down' : 'up'" />
							</div>
							<a-table v-if="!isCloseFive" class="mt10" bordered :columns="resDcirColumns"
								:data-source="resDcirFilterData" :pagination="false">

								<template slot="stepId" slot-scope="text, record, index, columns">
									<a-input-number style="width: 100%" v-model="record[columns.dataIndex]" />
								</template>

								<template slot="stepTime" slot-scope="text, record, index, columns">
									<a-input :placeholder="record.table === 'step' ? '选填' : ''" style="width: 100%"
										v-model="record[columns.dataIndex]" @blur="handleResDcirBlur($event,index)" />
								</template>

								<template slot="additionalCondition" slot-scope="text, record, index, columns">
									<a-select v-model="record[columns.dataIndex]" allow-clear style="width: 100%"
										@change="handleResDcirChange($event,index)">
										<a-select-option value="first">
											取第一条
										</a-select-option>
										<a-select-option value="last">
											取最后一条
										</a-select-option>
									</a-select>
								</template>
							</a-table>
						</div>

						<div v-if="reportType == 'SOC-OCV map'">
							<div class="flex-sb-center-row">
								<div>①测试数据充放电选择</div>
								<div>
									<a-icon @click="handleClose(4)" :type="isCloseFour ? 'down' : 'up'" />
								</div>
							</div>

							<a-table v-if="!isCloseFour" class="mt10" bordered childrenColumnName="child"
								:columns="socOcvTempColumns" :data-source="socOcvTypeData" :pagination="false">
								<template slot="dataType" slot-scope="text, record, index, columns">
									<a-select style="width: 100%;text-align: center" v-model="record.dataType"
										:options="socOcvDataTypeOptions" />
								</template>
							</a-table>

							<div class="flex-sb-center-row mt10">
								<div>②充电SOC-OCV数据获取逻辑</div>
								<div style="display: flex;align-items: center">
									<a-input v-model="socOcvChartParam.chargeTitle" placeholder="输入图表标题" class="mr10"
										size="small"></a-input>
									<a-icon @click="handleClose(5)" :type="isCloseFive ? 'down' : 'up'" />
								</div>
							</div>
							<div class="mt10">
								<span>排序规则 : </span>
								<a-radio-group v-model="socOcvChargingSort" @change="handleChargingSort">
									<a-radio :value="1">
										正序
									</a-radio>
									<a-radio :value="2">
										倒序
									</a-radio>
								</a-radio-group>
							</div>
							<a-table v-if="!isCloseFive" class="mt10" bordered :columns="socOcvColumns"
								:data-source="socOcvFilterDataCharge" :pagination="false">
								<template slot="action" slot-scope="text, record, index, columns">
									<a-button type="link"
										@click="deleteSocOcvFilterDataCharge(text, record, index, columns)">删除</a-button>
								</template>

								<template slot="stepId" slot-scope="text, record, index, columns">
									<a-input style="width: 100%" v-model="record.stepId"
										@paste="handleChargingPaste($event, index)"
										@blur="handleChargingInput($event, index)" />
								</template>

								<template slot="cycleId" slot-scope="text, record, index, columns">
									<a-input style="width: 100%" v-model="record.cycleId"
										@paste="handleChargingPaste($event, index,1)"
										@blur="handleChargingInput($event, index)" />
								</template>

								<template slot="footer">
									<div class="footer-btn">
										<div class="footer-btn" :class="{
												'plus-btn': orderData.length !== 0 && socOcvFilterDataCharge.length === 0
											}" @click="addSocOcvChargeData">
											<span></span>
											<span></span>
											<span></span>
											<span></span>
											<a-icon type="plus"></a-icon>
										</div>
									</div>
								</template>

							</a-table>

							<div class="flex-sb-center-row mt10">
								<div>③放电SOC-OCV数据获取逻辑</div>
								<div style="display: flex;align-items: center">
									<a-input v-model="socOcvChartParam.dischargeTitle" placeholder="输入图表标题" class="mr10"
										size="small"></a-input>
									<a-icon @click="handleClose(6)" :type="isCloseSix ? 'down' : 'up'" />
								</div>
							</div>
							<div class="mt10">
								<span>排序规则 : </span>
								<a-radio-group v-model="socOcvDischargingSort" @change="handleDischargingSort">
									<a-radio :value="1">
										正序
									</a-radio>
									<a-radio :value="2">
										倒序
									</a-radio>
								</a-radio-group>
							</div>
							<a-table v-if="!isCloseSix" class="mt10" bordered :columns="socOcvColumns"
								:data-source="socOcvFilterDataDischarge" :pagination="false">
								<template slot="action" slot-scope="text, record, index, columns">
									<a-button type="link"
										@click="deleteSocOcvFilterDataDischarge(text, record, index, columns)">删除</a-button>
								</template>

								<template slot="stepId" slot-scope="text, record, index, columns">
									<a-input style="width: 100%" v-model="record.stepId"
										@paste="handleDischargingPaste($event, index)"
										@blur="handleDischargingInput($event, index)" />
								</template>

								<template slot="cycleId" slot-scope="text, record, index, columns">
									<a-input style="width: 100%" v-model="record.cycleId"
										@paste="handleDischargingPaste($event, index,1)"
										@blur="handleDischargingInput($event, index)" />
								</template>

								<template slot="footer">
									<div class="footer-btn">
										<div class="footer-btn" :class="{
												'plus-btn': orderData.length !== 0 && socOcvFilterDataDischarge.length === 0
											}" @click="addSocOcvDischargeData">
											<span></span>
											<span></span>
											<span></span>
											<span></span>
											<a-icon type="plus"></a-icon>
										</div>
									</div>
								</template>

							</a-table>
						</div>

						<div v-if="reportType == 'SOC-OCV map charge'">
							<div class="flex-sb-center-row">
								<div>①测试数据充电选择</div>
								<div>
									<a-icon @click="handleClose(10)" :type="isCloseTen ? 'down' : 'up'" />
								</div>
							</div>

							<a-table v-if="!isCloseTen" class="mt10" bordered childrenColumnName="child"
								:columns="socOcvTempColumns" :data-source="socOcvTypeData" :pagination="false">
								<template slot="dataType">充电数据</template>
							</a-table>

							<div class="flex-sb-center-row mt10">
								<div>②充电SOC-OCV数据获取逻辑</div>
								<div style="display: flex;align-items: center">
									<a-input v-model="socOcvChartParam.chargeTitle" placeholder="输入图表标题" class="mr10"
										size="small"></a-input>
									<a-icon @click="handleClose(11)" :type="isCloseEleven ? 'down' : 'up'" />
								</div>
							</div>
							<div class="mt10">
								<span>排序规则 : </span>
								<a-radio-group v-model="socOcvChargingSort" @change="handleChargingSort">
									<a-radio :value="1">
										正序
									</a-radio>
									<a-radio :value="2">
										倒序
									</a-radio>
								</a-radio-group>
							</div>
							<a-table v-if="!isCloseEleven" class="mt10" bordered :columns="socOcvColumns"
								:data-source="socOcvFilterDataCharge" :pagination="false">
								<template slot="action" slot-scope="text, record, index, columns">
									<a-button type="link"
										@click="deleteSocOcvFilterDataCharge(text, record, index, columns)">删除</a-button>
								</template>

								<template slot="stepId" slot-scope="text, record, index, columns">
									<a-input style="width: 100%" v-model="record.stepId"
										@blur="handleChargingInput($event, index)" />
								</template>

								<template slot="cycleId" slot-scope="text, record, index, columns">
									<a-input style="width: 100%" v-model="record.cycleId"
										@blur="handleChargingInput($event, index , 1)" />
								</template>

								<template slot="footer">
									<div class="footer-btn">
										<div class="footer-btn" :class="{
												'plus-btn': orderData.length !== 0 && socOcvFilterDataCharge.length === 0
											}" @click="addSocOcvChargeData">
											<span></span>
											<span></span>
											<span></span>
											<span></span>
											<a-icon type="plus"></a-icon>
										</div>
									</div>
								</template>
							</a-table>
						</div>

						<div v-if="reportType == 'SOC-OCV map discharge'">
							<div class="flex-sb-center-row">
								<div>①测试数据放电选择</div>
								<div>
									<a-icon @click="handleClose(12)" :type="isCloseTwelve ? 'down' : 'up'" />
								</div>
							</div>

							<a-table v-if="!isCloseTwelve" class="mt10" bordered childrenColumnName="child"
								:columns="socOcvTempColumns" :data-source="socOcvTypeData" :pagination="false">
								<template slot="dataType">放电数据</template>
							</a-table>

							<div class="flex-sb-center-row mt10">
								<div>②放电SOC-OCV数据获取逻辑</div>
								<div style="display: flex;align-items: center">
									<a-input v-model="socOcvChartParam.dischargeTitle" placeholder="输入图表标题" class="mr10"
										size="small"></a-input>
									<a-icon @click="handleClose(13)" :type="isCloseThirteen ? 'down' : 'up'" />
								</div>
							</div>
							<div class="mt10">
								<span>排序规则 : </span>
								<a-radio-group v-model="socOcvDischargingSort" @change="handleDischargingSort">
									<a-radio :value="1">
										正序
									</a-radio>
									<a-radio :value="2">
										倒序
									</a-radio>
								</a-radio-group>
							</div>
							<a-table v-if="!isCloseThirteen" class="mt10" bordered :columns="socOcvColumns"
								:data-source="socOcvFilterDataDischarge" :pagination="false">
								<template slot="action" slot-scope="text, record, index, columns">
									<a-button type="link"
										@click="deleteSocOcvFilterDataDischarge(text, record, index, columns)">删除</a-button>
								</template>

								<template slot="stepId" slot-scope="text, record, index, columns">
									<a-input-number style="width: 100%" v-model="record.stepId"
										@blur="handleDischargingInput($event, index)" />
								</template>

								<template slot="cycleId" slot-scope="text, record, index, columns">
									<a-input-number style="width: 100%" v-model="record.cycleId"
										@blur="handleDischargingInput($event, index,1)" />
								</template>

								<template slot="footer">
									<div class="footer-btn">
										<div class="footer-btn" :class="{
												'plus-btn': orderData.length !== 0 && socOcvFilterDataDischarge.length === 0
											}" @click="addSocOcvDischargeData">
											<span></span>
											<span></span>
											<span></span>
											<span></span>
											<a-icon type="plus"></a-icon>
										</div>
									</div>
								</template>

							</a-table>
						</div>

						<div v-if="reportType == '循环'">
							<div class="flex-sb-center-row">
								<div>①数据表选择</div>
							</div>
              <a-select v-model="tableType" style="width: 150px;margin-top: 10px;margin-bottom: 10px">
                <a-select-option value="cyc">
                  循环表
                </a-select-option>
                <a-select-option value="step">
                  工步表
                </a-select-option>
              </a-select>
              <div class="flex-sb-center-row">
								<div>②工步号填写</div>
								<div>
									<a-icon @click="handleClose(4)" :type="isCloseFour ? 'down' : 'up'" />
								</div>
							</div>
							<a-table v-if="!isCloseFour" class="mt10" bordered :columns="cyclesFillColumns"
								:data-source="cycleReportStepData" :pagination="false">

                <div
                  slot="stepIdTitle"
                >
                  <span>填写内容 </span>
                  <a-tooltip placement="topLeft" title="循环表：填写1个工步号用于提取工步数据的温度；
                  工步表：可填写1个或多个用英文逗号隔开工步号用于提取工步数据" arrow-point-at-center>
                    <a-icon class="tips" type="question-circle" />
                  </a-tooltip>
                </div>
								<template slot="cycleId" slot-scope="text, record, index, columns">
									<a-input style="width: 100%" v-model="record[columns.dataIndex]"
										@change="handleVerifyInput(cycleReportStepData,index,columns.dataIndex)" />
								</template>
							</a-table>
						</div>

						<div v-if="reportType == '倍率-倍率充放电&恒功率放电'">
							<div class="flex-sb-center-row">
								<div>①工步号-充/放电电流填写</div>
								<a-icon @click="handleClose(4)" :type="isCloseFour ? 'down' : 'up'" />
							</div>

							<a-table v-if="!isCloseFour" class="mt10" bordered :columns="cRateColumns"
								:data-source="cRateData" :pagination="false">
								<template slot="action" slot-scope="text, record, index, columns">
									<a @click="deleteCRate(record)">删除</a>
								</template>
								<template slot="stepId" slot-scope="text, record, index, columns">
									<a-input style="width: 100%" v-model="record[columns.dataIndex]"
										@paste="handleCRatePaste($event, index)"
										@blur="handleCRateInput($event, index)" />
								</template>
								<template slot="rateCurrent" slot-scope="text, record, index, columns">
									<a-input style="width: 100%" v-model="record[columns.dataIndex]"
										@paste="handleCRatePaste($event, index,1)"
										@blur="handleCRateInput($event, index)" />
								</template>
								<template slot="footer">
									<div class="footer-btn">
										<div class="footer-btn" :class="{
												'plus-btn': orderData.length !== 0 && cRateData.length === 0
											}" @click="addCRateData">
											<span></span>
											<span></span>
											<span></span>
											<span></span>
											<a-icon type="plus"></a-icon>
										</div>
									</div>
								</template>
							</a-table>
						</div>

						<div v-if="reportType == '倍率-stress'">
							<div class="flex-sb-center-row">
								<div>①工步号-放电电流填写</div>
								<a-icon @click="handleClose(7)" :type="isCloseSeven ? 'down' : 'up'" />
							</div>

							<a-table v-if="!isCloseSeven" class="mt10" bordered :columns="rateStressColumns"
								:data-source="rateStressData" :pagination="false">
								<template slot="action" slot-scope="text, record, index, columns">
									<a @click="deleteRateStress(record)">删除</a>
								</template>
								<template slot="stepId" slot-scope="text, record, index, columns">
									<a-input style="width: 100%" v-model="record[columns.dataIndex]"
										@paste="handleRateStressPaste($event, index)"
										@blur="handleRateStressInput($event, index)" />
								</template>
								<template slot="rateCurrent" slot-scope="text, record, index, columns">
									<a-input style="width: 100%" v-model="record[columns.dataIndex]"
										@paste="handleRateStressPaste($event, index, 1)"
										@blur="handleRateStressInput($event, index)" />
								</template>
								<template slot="footer">
									<div class="footer-btn">
										<div class="footer-btn" :class="{
												'plus-btn': orderData.length !== 0 && rateStressData.length === 0
											}" @click="addRateStressData">
											<span></span>
											<span></span>
											<span></span>
											<span></span>
											<a-icon type="plus"></a-icon>
										</div>
									</div>
								</template>
							</a-table>
						</div>

						<div v-if="reportType == '倍率-脉冲放电'">
							<div class="flex-sb-center-row">
								<div>①工步号填写</div>
								<a-icon @click="handleClose(8)" :type="isCloseEight ? 'down' : 'up'" />
							</div>

							<a-table v-if="!isCloseEight" class="mt10" bordered :columns="pulseDischargeMColumns"
								:data-source="pulseDischargeMData" :pagination="false">
								<template slot="stepId" slot-scope="text, record, index, columns">
									<a-input style="width: 100%" v-model="record[columns.dataIndex]"
										@change="handleVerifyInput(pulseDischargeMData,index,columns.dataIndex)"
										@blur="handlePulseDischargeMInput($event, index)" />
								</template>
							</a-table>
						</div>

						<!-- <div v-if="reportType == '倍率-脉放map'">
							<div class="flex-sb-center-row">
								<div>①工步号填写</div>
								<a-icon @click="handleClose(9)" :type="isCloseNine ? 'down' : 'up'" />
							</div>
							<a-table v-if="!isCloseNine" class="mt10" bordered :columns="pulseDischargeMapColumns"
								:data-source="pulseDischargeMapData" :pagination="false">
								<template slot="stepId" slot-scope="text, record, index, columns">
									<a-input style="width: 100%" v-model="record[columns.dataIndex]"
										@change="handleVerifyInput(pulseDischargeMapData,index,columns.dataIndex)" />
								</template>
							</a-table>
						</div> -->
					</div>
				</div>
			</div>
		</div>

		<a-modal title="导出" :width="800" :height="600" :bodyStyle="{ padding: 0 }" :visible="visible1"
			:confirmLoading="confirmLoading" @ok="handleSubmit" style="padding: 0" @cancel="handleCancel1">
			<a-form :form="form">
				<a-row :gutter="24">
					<a-col :md="18" :sm="24">
						<a-form-item label="任务名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
							<a-input placeholder="请输入任务名称"
								v-decorator="['taskName', { rules: [{ required: true, message: '请输入任务名称！' }] }]" />
						</a-form-item>
					</a-col>
				</a-row>

				<a-row :gutter="24">
					<a-col :md="18" :sm="24">
						<a-form-item label="Excel格式" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">
							<a-select
								v-decorator="['excelType', { rules: [{ required: true, message: '请选择Excel格式！' }] }]"
								default-value="one" style="width: 100%" placeholder="请选择Excel格式">
								<a-select-option value="one">
									保存于同一个Sheet中
								</a-select-option>
								<a-select-option value="more">
									保存于不同的Sheet中
								</a-select-option>
							</a-select>
						</a-form-item>
					</a-col>
				</a-row>
			</a-form>
		</a-modal>

		<a-modal title="测试项目选择" :width="1200" :height="600" :bodyStyle="{ padding: 0 }" :visible="visible"
			style="padding: 0" :maskClosable="false" @cancel="handleCancel">
			<div style="float: left;position: relative;z-index: 1;padding-bottom: 5px;width: 65%">
				<a-row :gutter="[8, 8]">
					<a-col :span="8">
						<a-form-item label="委托单号" :labelCol="labelCol1" :wrapperCol="wrapperCol1" has-feedback>
							<a-input v-model="queryParam.folderno" @keyup.enter="$refs.table.refresh()"
								@change="$refs.table.refresh()" />
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="主题" :labelCol="labelCol1" :wrapperCol="wrapperCol1" has-feedback>
							<a-input v-model="queryParam.theme" @keyup.enter="$refs.table.refresh()"
								@change="$refs.table.refresh()" />
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="测试项目别名" :labelCol="labelCol1" :wrapperCol="wrapperCol1" has-feedback>
							<a-input v-model="queryParam.alias" @keyup.enter="$refs.table.refresh()"
								@change="$refs.table.refresh()" />
						</a-form-item>
					</a-col>
				</a-row>
			</div>

			<s-table :columns="columns" :data="loadData" bordered :rowKey="record1 => record1.uuid" ref="table"
				:row-selection="{
					selectedRowKeys: selectedRowKeys,
					selectedRows: selectedRows,
					onSelect: onSelectChange,
					onSelectAll: onSelectAllChange,
					columnWidth: 30
				}">
				<template slot="celltestcode" slot-scope="text, record, index, columns">
					<a @click="openStepData(record)" v-if="record.flowId != null" style="text-align: center">{{ text
						}}</a>
					<span v-else style="text-align: center">{{ text }}</span>
				</template>

				<template slot="action1" slot-scope="text, record, index, columns">
					<a @click="showData(record)" v-if="record.showHide" style="text-align: center">初始化</a>
					<a @click="hideData(record)" v-else-if="!record.showHide && record.children != null"
						style="text-align: center">隐藏</a>
					<a @click="hideData(record)" v-else-if="record.isChild" style="text-align: center">隐藏</a>
				</template>

				<template slot="theme" slot-scope="text, record, index, columns">
					<a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
						<template slot="title">
							{{ text }}
						</template>
						{{ text }}
					</a-tooltip>
				</template>
				<template slot="dataPath" slot-scope="text, record, index, columns">
					<a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
						<template slot="title">
							{{ text ? text : record.testcontent }}
						</template>
						{{ text ? text : record.testcontent }}
					</a-tooltip>
				</template>
			</s-table>
			<template slot="footer">
				<a-button key="back" @click="handleCancel">
					关闭
				</a-button>
			</template>
		</a-modal>

		<a-modal title="测试数据选择" :width="1000" :height="300" :bodyStyle="{ padding: 0 }" :visible="visibleFlow"
			style="padding: 0" :maskClosable="false" :centered="true" @cancel="handleCancelFlow">
			<a-table :columns="flowInfoColumns" :dataSource="flowInfoData" bordered :rowKey="record => record.uuid"
				ref="table" :pagination="false">
				<template slot="celltestcode" slot-scope="text, record, index, columns">
					<a @click="openStepData(record)" v-if="record.flowId != null" style="text-align: center">{{ text
						}}</a>
					<span v-else style="text-align: center">{{ text }}</span>
				</template>

				<template slot="action" slot-scope="text, record, index, columns">
					<a @click="onSelectChangeFlow(record, '选中')" style="text-align: center">选中</a>
					<a-divider type="vertical" />
					<a @click="onSelectChangeFlow(record, '查看')" style="text-align: center">查看</a>
				</template>
				<template slot="dataPath" slot-scope="text, record, index, columns">
					<a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
						<template slot="title">
							{{ text ? text : record.testcontent }}
						</template>
						{{ text ? text : record.testcontent }}
					</a-tooltip>
				</template>
			</a-table>
			<template slot="footer">
				<a-button key="back" @click="handleCancelFlow">
					关闭
				</a-button>
			</template>
		</a-modal>

		<step-data ref="stepData"></step-data>

		<a-drawer :bodyStyle="{ height: '100%' }" placement="right" :closable="false" width="70%"
			:visible="isShowExample" @close="handleCloseExample">
			<iframe :src="iframeUrl" width="100%" height="100%"></iframe>
		</a-drawer>
	</div>
</template>
<script>
	import { mapGetters } from "vuex"
	import moment from "moment"
  import Sortable from 'sortablejs';

	import {
		tLimsTestdataSchedulePageList,
		shenghongDataFilterExport,
		hideData,
		showData,
		getProductList,
		testReportSave
	} from "@/api/modular/system/limsManager"

	import { STable } from "@/components"
	import stepData from "../lims/folder/stepData"

	export default {
		components: {
			STable,
			stepData
		},
		props: {
			isInside:{
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				issueId: null,
        tableType:'step',
				allTable: "数据表选择",
				isOne: false,
				reportBasic: {},
				reportType: "循环DCR",
				showCycAdd: false,

				isCloseOne: false,
				isCloseTwo: false,
				isCloseThree: false,
				isCloseFour: false,
				isCloseFive: false,
				isCloseSix: false,
				isCloseSeven: false,
				isCloseEight: false,
				isCloseNine: false,
				isCloseTen: false,
				isCloseEleven: false,
				isCloseTwelve: false,
				isCloseThirteen: false,

				addCycNum: 351,
				iframeUrl: "",
				param: null,
				outFlowRecord: null,
				outQueryFlowRecord: null,
				inFlowActionName: null,
				visibleFlow: false,
				isShowExample: false,
				current: 0,
				stepStatus: ["wait", "wait", "wait", "wait"],
				radioMenu: [
					{ name: "工步数据表", value: "step" },
					{ name: "详细数据表", value: "data" },
					{ name: "循环数据表", value: "cyc" },
					{ name: "工步信息表", value: "stepInfo" }
				],
				flowInfoColumns: [
					{
						title: "测试编码",
						dataIndex: "barCode",
						align: "center",
						width: 100
					},
					{
						title: "设备编号",
						width: 30,
						align: "center",
						dataIndex: "unitNum"
					},
					{
						title: "通道编号",
						width: 30,
						align: "center",
						dataIndex: "channelId"
					},
					{
						title: "数据位置",
						width: 30,
						align: "center",
						dataIndex: "dataPath",
						scopedSlots: { customRender: "dataPath" }
					},
					{
						title: "开始时间",
						width: 30,
						align: "center",
						dataIndex: "startTime",
						customRender: (text, record, index) => {
							if (null != text) {
								return moment(text).format("YYYY-MM-DD")
							}
							return text
						}
					},
					{
						title: "结束时间",
						width: 30,
						align: "center",
						dataIndex: "endTime",
						customRender: (text, record, index) => {
							if (null != text) {
								return moment(text).format("YYYY-MM-DD")
							}
							return text
						}
					},
					{
						title: "操作",
						width: 60,
						align: "center",
						dataIndex: "action",
						scopedSlots: { customRender: "action" }
					}
				],
				flowInfoData: [],
				checkedListStep: [],
				checkedListData: [],
				checkedListCyc: [],
				indeterminate: false,
				checkAll: true,
				socDcirCyclesChecked: [1, 51, 101, 151, 201, 251, 301],
				dataChecked: [],
				cycChecked: [],
				stepInfoChecked: [],
				filterData: [],
				socDcirFilterData: [
					// { soc: "100%SOC", table: "step", stepId: 11, stepTime: "", orderNumber: 1, socNum: 100 },
					// { soc: "100%SOC", table: "step", stepId: 12, stepTime: "", orderNumber: 2, socNum: 100 },
					// { soc: "95%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 1, socNum: 95 },
					// { soc: "95%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 2, socNum: 95 },
					// { soc: "90%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 1, socNum: 90 },
					// { soc: "90%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 2, socNum: 90 },
					// { soc: "85%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 1, socNum: 85 },
					// { soc: "85%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 2, socNum: 85 },
					// { soc: "80%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 1, socNum: 80 },
					// { soc: "80%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 2, socNum: 80 },
					// { soc: "75%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 1, socNum: 75 },
					// { soc: "75%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 2, socNum: 75 },
					// { soc: "70%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 1, socNum: 70 },
					// { soc: "70%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 2, socNum: 70 },
					// { soc: "65%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 1, socNum: 65 },
					// { soc: "65%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 2, socNum: 65 },
					// { soc: "60%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 1, socNum: 60 },
					// { soc: "60%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 2, socNum: 60 },
					// { soc: "55%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 1, socNum: 55 },
					// { soc: "55%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 2, socNum: 55 },
					{ soc: "50%SOC", table: "step", stepId: 18, stepTime: "", orderNumber: 1, socNum: 50 },
					{ soc: "50%SOC", table: "step", stepId: 19, stepTime: "", orderNumber: 2, socNum: 50 },
					// { soc: "45%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 1, socNum: 45 },
					// { soc: "45%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 2, socNum: 45 },
					// { soc: "40%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 1, socNum: 40 },
					// { soc: "40%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 2, socNum: 40 },
					// { soc: "35%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 1, socNum: 35 },
					// { soc: "35%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 2, socNum: 35 },
					// { soc: "30%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 1, socNum: 30 },
					// { soc: "30%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 2, socNum: 30 },
					// { soc: "25%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 1, socNum: 25 },
					// { soc: "25%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 2, socNum: 25 },
					// { soc: "20%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 1, socNum: 20 },
					// { soc: "20%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 2, socNum: 20 },
					// { soc: "15%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 1, socNum: 15 },
					// { soc: "15%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 2, socNum: 15 },
					// { soc: "10%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 1, socNum: 10 },
					// { soc: "10%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 2, socNum: 10 },
					// { soc: "5%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 1, socNum: 5 },
					// { soc: "5%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 2, socNum: 5 },
					// { soc: "0%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 1, socNum: 0 },
					// { soc: "0%SOC", table: "step", stepId: '', stepTime: "", orderNumber: 2, socNum: 0 },


				],
				cycleReportSchemeData: [
					{ schemeName: "方案一", table: "cycle", flowId: "", orderNumber: 1, stepId: null },
					{ schemeName: "方案一", table: "cycle", flowId: "", orderNumber: 2, stepId: null },
					{ schemeName: "方案一", table: "cycle", flowId: "", orderNumber: 3, stepId: null },
					{ schemeName: "方案二", table: "cycle", flowId: "", orderNumber: 1, stepId: null },
					{ schemeName: "方案二", table: "cycle", flowId: "", orderNumber: 2, stepId: null },
					{ schemeName: "方案二", table: "cycle", flowId: "", orderNumber: 3, stepId: null },
				],
				cRateData: [
					{ stepId: "4", rateCurrent: "0.6" },
					{ stepId: "8", rateCurrent: "3" },
					{ stepId: "12", rateCurrent: "4" },
					{ stepId: "16", rateCurrent: "5" },
					{ stepId: "20", rateCurrent: "6" },
					{ stepId: "24", rateCurrent: "8" }
					// { stepId: "", rateCurrent: "" }
				],
				rateStressData: [
					{ stepId: "7", rateCurrent: "2.5" },
					{ stepId: "8", rateCurrent: "20" },
					{ stepId: "10", rateCurrent: "2.5" },
					{ stepId: "11", rateCurrent: "40" },
					{ stepId: "13", rateCurrent: "2.5" },
					{ stepId: "14", rateCurrent: "60" },
					{ stepId: "16", rateCurrent: "2.5" },
					{ stepId: "17", rateCurrent: "80" },
					{ stepId: "19", rateCurrent: "2.5" },
					{ stepId: "20", rateCurrent: "100" }
				],
				pulseDischargeMData: [
					{ pulseDischargeType: "放电", stepId: "6" },
					{ pulseDischargeType: "搁置", stepId: "7" },
				],
				pulseDischargeMapData: [
					{ pulseDischargeType: "放电", stepId: "6" },
				],
				resDcirFilterData: [
					{ table: "data", stepId: 7, stepTime: "0:00:08.000", orderNumber: 1,additionalCondition : null },
					{ table: "data", stepId: 8, stepTime: "0:00:03.000", orderNumber: 2,additionalCondition : null },
				],
				socOcvFilterDataCharge: [
					{ soc: "100%SOC", table: "step", stepId: 10, cycleId: 20, socNum: 100 },
					{ soc: "95%SOC", table: "step", stepId: 10, cycleId: 19, socNum: 95 },
					{ soc: "90%SOC", table: "step", stepId: 10, cycleId: 18, socNum: 90 },
					{ soc: "85%SOC", table: "step", stepId: 10, cycleId: 17, socNum: 85 },
					{ soc: "80%SOC", table: "step", stepId: 10, cycleId: 16, socNum: 80 },
					{ soc: "75%SOC", table: "step", stepId: 10, cycleId: 15, socNum: 75 },
					{ soc: "70%SOC", table: "step", stepId: 10, cycleId: 14, socNum: 70 },
					{ soc: "65%SOC", table: "step", stepId: 10, cycleId: 13, socNum: 65 },
					{ soc: "60%SOC", table: "step", stepId: 10, cycleId: 12, socNum: 60 },
					{ soc: "55%SOC", table: "step", stepId: 10, cycleId: 11, socNum: 55 },
					{ soc: "50%SOC", table: "step", stepId: 10, cycleId: 10, socNum: 50 },
					{ soc: "45%SOC", table: "step", stepId: 10, cycleId: 9, socNum: 45 },
					{ soc: "40%SOC", table: "step", stepId: 10, cycleId: 8, socNum: 40 },
					{ soc: "35%SOC", table: "step", stepId: 10, cycleId: 7, socNum: 35 },
					{ soc: "30%SOC", table: "step", stepId: 10, cycleId: 6, socNum: 30 },
					{ soc: "25%SOC", table: "step", stepId: 10, cycleId: 5, socNum: 25 },
					{ soc: "20%SOC", table: "step", stepId: 10, cycleId: 4, socNum: 20 },
					{ soc: "15%SOC", table: "step", stepId: 10, cycleId: 3, socNum: 15 },
					{ soc: "10%SOC", table: "step", stepId: 10, cycleId: 2, socNum: 10 },
					{ soc: "5%SOC", table: "step", stepId: 10, cycleId: 1, socNum: 5 },
					{ soc: "0%SOC", table: "step", stepId: 8, cycleId: 1, socNum: 0 },

				],
				socOcvFilterDataDischarge: [
					{ soc: "100%SOC", table: "step", stepId: 8, cycleId: 1, socNum: 100 },
					{ soc: "95%SOC", table: "step", stepId: 10, cycleId: 1, socNum: 95 },
					{ soc: "90%SOC", table: "step", stepId: 10, cycleId: 2, socNum: 90 },
					{ soc: "85%SOC", table: "step", stepId: 10, cycleId: 3, socNum: 85 },
					{ soc: "80%SOC", table: "step", stepId: 10, cycleId: 4, socNum: 80 },
					{ soc: "75%SOC", table: "step", stepId: 10, cycleId: 5, socNum: 75 },
					{ soc: "70%SOC", table: "step", stepId: 10, cycleId: 6, socNum: 70 },
					{ soc: "65%SOC", table: "step", stepId: 10, cycleId: 7, socNum: 65 },
					{ soc: "60%SOC", table: "step", stepId: 10, cycleId: 8, socNum: 60 },
					{ soc: "55%SOC", table: "step", stepId: 10, cycleId: 9, socNum: 55 },
					{ soc: "50%SOC", table: "step", stepId: 10, cycleId: 10, socNum: 50 },
					{ soc: "45%SOC", table: "step", stepId: 10, cycleId: 11, socNum: 45 },
					{ soc: "40%SOC", table: "step", stepId: 10, cycleId: 12, socNum: 40 },
					{ soc: "35%SOC", table: "step", stepId: 10, cycleId: 13, socNum: 35 },
					{ soc: "30%SOC", table: "step", stepId: 10, cycleId: 14, socNum: 30 },
					{ soc: "25%SOC", table: "step", stepId: 10, cycleId: 15, socNum: 25 },
					{ soc: "20%SOC", table: "step", stepId: 10, cycleId: 16, socNum: 20 },
					{ soc: "15%SOC", table: "step", stepId: 10, cycleId: 17, socNum: 15 },
					{ soc: "10%SOC", table: "step", stepId: 10, cycleId: 18, socNum: 10 },
					{ soc: "5%SOC", table: "step", stepId: 10, cycleId: 19, socNum: 5 },
					{ soc: "0%SOC", table: "step", stepId: 10, cycleId: 20, socNum: 0 },

				],
				socDcirCycleData: [
					{ cycleId: 3, cycle: 1 },
					{ cycleId: 53, cycle: 51 },
					{ cycleId: 103, cycle: 101 },
					{ cycleId: 153, cycle: 151 },
					{ cycleId: 203, cycle: 201 },
					{ cycleId: 253, cycle: 251 },
					{ cycleId: 303, cycle: 301 }
				],
        socDcrData: [
					{ soc: 0,cycleId: null },
					{ soc: 10,cycleId: null },
					{ soc: 20, cycleId:null },
					{ soc: 30, cycleId:null },
					{ soc: 40, cycleId:null },
					{ soc: 50, cycleId:null },
					{ soc: 60, cycleId:null },
					{ soc: 70, cycleId:null },
					{ soc: 80, cycleId:null },
					{ soc: 90, cycleId:null },
					{ soc: 100, cycleId:null },
				],
				resDcirTempData: [
				],
				socOcvTypeData: [
				],
				cycleReportCycleData: [
					{ cycle: 1, cycleId: 1 },
					// { cycle: 100, cycleId: 100 },
					// { cycle: 200, cycleId: 200 },
					// { cycle: 300, cycleId: 300 },
					// { cycle: 400, cycleId: 400 },
					// { cycle: 600, cycleId: 600 },
					// { cycle: 1000, cycleId: 1000 },
					// { cycle: 1500, cycleId: 1500 },
				],
				cycleReportStepData: [
					{ stepId: '' }
				],
				socOcvChartParam: {
					chargeTitle: '',
					chargeTemp: '',
					dischargeTitle: '',
					dischargeTemp: '',
				},
				loadData: parameter => {
					this.loadDataList = []
					return tLimsTestdataSchedulePageList(Object.assign(parameter, this.queryParam)).then(res => {
						return res.data
					})
				},
				dataType: "step",
				orderData: [],
				visible: false,
				confirmLoading: false,
				visible1: false,
				height: 200,
				socDcirOptions: [
					{ label: 1, value: 1 },
					{ label: 51, value: 51 },
					{ label: 101, value: 101 },
					{ label: 151, value: 151 },
					{ label: 201, value: 201 },
					{ label: 251, value: 251 },
					{ label: 301, value: 301 }
				],
				stepInfoOptions: [
					{ label: "工步号", value: "StepID", key: "stepId" },
					{ label: "工步名称", value: "StepName", key: "stepName" },
					{ label: "参数", value: "StepPara", key: "stepPara" },
					{ label: "截止参数", value: "CutoffCondition", key: "cutoffCondition" },
					{ label: "记录条件", value: "Recordcond", key: "recordcond" },
					{ label: "创建时间", value: "CreateTime", key: "createTime" }
				],
				socDcirTableType: [
					{ label: "工步数据", value: "step" },
					{ label: "详细数据", value: "data" }
				],
				socDcirType: 75,
				socDcirTypeOptions: [
					{ label: "100%SOC", value: 100 },
					{ label: "75%SOC", value: 75 },
					{ label: "50%SOC", value: 50 },
					{ label: "25%SOC", value: 25 }
				],
				cycleId: null,
				cycle: null,

				socOcvDataTypeOptions: [
					{ label: "充电数据", value: "chargeData" },
					{ label: "放电数据", value: "dischargeData" },
				],

				show: false,
				labelCol: {
					sm: {
						span: 5
					}
				},
				wrapperCol: {
					sm: {
						span: 17
					}
				},
				labelCol1: {
					sm: {
						span: 11
					}
				},
				wrapperCol1: {
					sm: {
						span: 13
					}
				},
				queryParam: {},
				data: [],
				headData: [],
				allAddress: null,
				// 测试项目选择表头
				orderColumns: [
					{
						title: "操作",
						align: "center",
						width: 30,
						scopedSlots: { customRender: "action" }
					},
					{
						title: "序号",
						align: "center",
						width: 30,
						customRender: (text, record, index) => index + 1
					},
					{
						title: "委托单号",
						dataIndex: "folderno",
						align: "center",
						width: 45
					},
					// {
					// 	title: "测试项目编码",
					// 	width: 70,
					// 	align: "center",
					// 	dataIndex: "testcode"
					// 	//scopedSlots: {customRender: 'updateText'},
					// },
					{
						title: "测试项目别名",
						width: 65,
						align: "center",
						dataIndex: "alias"
						//scopedSlots: {customRender: 'updateText'},
					},
					{
						title: "测试编码",
						width: 50,
						align: "center",
						dataIndex: "celltestcode",
						scopedSlots: { customRender: "celltestcode" }
					},
					{
						title: "数据位置",
						width: 50,
						align: "center",
						dataIndex: "dataPath",
						ellipsis: true,
						scopedSlots: { customRender: "dataPath" }
					},
					{
						title: "温度",
						width: 45,
						align: "center",
						dataIndex: "tem"
					},{
						title: "设备编号",
						width: 45,
						align: "center",
						dataIndex: "equiptcode"
					},
					{
						title: "通道编号",
						width: 45,
						align: "center",
						dataIndex: "channelno"
					}
				],
				filterColumns: [
					{
						title: "操作",
						align: "center",
						width: 40,
						scopedSlots: { customRender: "action" }
					},
					{
						title: "序号",
						align: "center",
						width: 40,
						customRender: (text, record, index) => index + 1
					},
					{
						title: "参数1",
						dataIndex: "key1",
						align: "center",
						width: 100,
						scopedSlots: { customRender: "key" }
					},
					{
						title: "值",
						width: 100,
						align: "center",
						dataIndex: "value1",
						scopedSlots: { customRender: "value" }
					},
					{
						title: "参数2",
						dataIndex: "key2",
						align: "center",
						width: 100,
						scopedSlots: { customRender: "key" }
					},
					{
						title: "值",
						width: 100,
						align: "center",
						dataIndex: "value2",
						scopedSlots: { customRender: "value" }
					},
					{
						title: "参数3",
						dataIndex: "key3",
						align: "center",
						width: 100,
						scopedSlots: { customRender: "key" }
					},
					{
						title: "值",
						width: 100,
						align: "center",
						dataIndex: "value3",
						scopedSlots: { customRender: "value" }
					}

					/*,
				{
				  title: '参数4',
				  dataIndex: 'key4',
				  align: 'center',
				  width: 90,
				  scopedSlots: {customRender: 'key'},
				}, {
				  title: '值',
				  width: 90,
				  align: 'center',
				  dataIndex: 'value4',
				  scopedSlots: {customRender: 'value'},
				}*/
				],
				texts1: "",
				socDcirSort: 2, // 1:正序  2：倒序
				socOcvChargingSort: 2, // 1:正序  2：倒序
				socOcvDischargingSort: 2, // 1:正序  2：倒序
				socDcirColumns: [
					{
						title: "操作",
						align: "center",
						width: "calc((45vw - 60px) * 0.14)",
						customRender: (text, record, index) => {
							const obj = {
								children: <a onClick={() => this.deleteSocDcir(record)}>删除</a>,
								attrs: {}
							}
							obj.attrs.rowSpan = 0
							if (index == 0 || index % 2 == 0) {
								obj.attrs.rowSpan = 2
							}

							return obj
						}
					},
					{
						title: "电芯",
						align: "center",
						dataIndex: "socNum",
						width: "calc((45vw - 60px) * 0.16)",
						// scopedSlots: { customRender: "soc" },
						customRender: (text, record, index) => {
							const obj = {
								children: (
									<div class="soc-input">
										<a-input-number
											style="width:calc((45vw - 60px) * 0.085)"
											v-model={record.socNum}
											onBlur={$event => this.handleSocInput($event, index)}
										/>
										<span class="text">%SOC</span>
									</div>
								),
								attrs: {}
							}
							obj.attrs.rowSpan = 0
							if (index == 0 || index % 2 == 0) {
								obj.attrs.rowSpan = 2
							}

							return obj
						}
					},
					{
						title: "工步名称",
						dataIndex: "orderNumber",
						align: "center",
						width: "calc((45vw - 60px) * 0.17)",
						customRender: (text, record, index) => {
							return "放电工步" + text
						}
					},
					{
						dataIndex: "table",
						align: "center",
						width: "calc((45vw - 60px) * 0.20)",
						scopedSlots: {
							customRender: "table",
							title: "tableTitle"
							// filterDropdown: "filterDropdown",
							// filterIcon: "filterIcon",
						}
					},
					{
						title: "工步号",
						width: "calc((45vw - 60px) * 0.15)",
						align: "center",
						dataIndex: "stepId",
						scopedSlots: { customRender: "stepId" }
					},
					{
						title: "工步时间",
						dataIndex: "stepTime",
						align: "center",
						width: "calc((45vw - 60px) * 0.18)",
						scopedSlots: { customRender: "stepTime" }
					}
				],
				socDcrDataColumns: [

          {
            title: "电芯",
            width: "calc((45vw - 60px) * 0.2)",
            align: "center",
            dataIndex: "orderno",
            scopedSlots: { customRender: "orderno" }
          },
					{
						title: "温度",
						align: "center",
						dataIndex: "tem",
						width: "calc((45vw - 60px) * 0.16)",
						// scopedSlots: { customRender: "soc" },
						customRender: (text, record, index) => {
							const obj = {
								children: (
									<div class="soc-input">
										<a-input-number
											style="width:calc((45vw - 50px) * 0.085)"
											v-model={record.tem}
										/>
										<span class="text">℃</span>
									</div>
								),
								attrs: {}
							}
							return obj
						}
					},

					{
						title: "充/放电工步",
						width: "calc((45vw - 60px) * 0.15)",
						align: "center",
						dataIndex: "socDcrStepId1",
						scopedSlots: { customRender: "socDcrStepId1" }
					},{
						title: "搁置工步",
						width: "calc((45vw - 60px) * 0.15)",
						align: "center",
						dataIndex: "socDcrStepId2",
						scopedSlots: { customRender: "socDcrStepId2" }
					}
				],
				cycleReportSchemeColumns: [
					{
						title: "操作",
						align: "center",
						width: "calc((45vw - 60px) * 0.15)",
						customRender: (text, record, index) => {
							const obj = {
								children: <a onClick={() => this.deleteCycleReportCycleSchemeName(record)}>删除</a>,
								attrs: {}
							}
							obj.attrs.rowSpan = 0
							if (index == 0 || index % 3 == 0) {
								obj.attrs.rowSpan = 3
							}

							return obj
						}
					},
					{
						title: "方案名",
						align: "center",
						dataIndex: "schemeName",
						width: "calc((45vw - 60px) * 0.3)",
						// scopedSlots: { customRender: "soc" },
						customRender: (text, record, index) => {
							const obj = {
								children: (
									<div>
										<a-input
											style="width:calc((45vw - 50px) * 0.25)"
											v-model={record.schemeName}
											onBlur={$event => this.handleSchemeNameInput($event, record, index)}
										/>
									</div>
								),
								attrs: {}
							}
							obj.attrs.rowSpan = 0
							if (index == 0 || index % 3 == 0) {
								obj.attrs.rowSpan = 3
							}

							return obj
						}
					},
					// {
					// 	title: "工步名称",
					// 	dataIndex: "orderNumber",
					// 	align: "center",
					// 	width: "calc((45vw - 50px) * 0.17)",
					// 	customRender: (text, record, index) => {
					// 		return "放电工步" + text
					// 	}
					// },
					{
						title: "测试数据",
						dataIndex: "flowId",
						align: "center",
						width: "calc((45vw - 60px) * 0.3)",
						scopedSlots: {
							customRender: "flowId",
							// title: "tableTitle"
							// filterDropdown: "filterDropdown",
							// filterIcon: "filterIcon",
						}
					},
					{
						title: "温度取值工步号",
						width: "calc((45vw - 60px) * 0.25)",
						align: "center",
						dataIndex: "stepId",
						scopedSlots: { customRender: "stepId" }
						// },
						// {
						// 	title: "工步时间",
						// 	dataIndex: "stepTime",
						// 	align: "center",
						// 	width: "calc((45vw - 50px) * 0.18)",
						// 	scopedSlots: { customRender: "stepTime" }
					}
				],
				cRateColumns: [
					{
						title: "操作",
						width: "calc((45vw - 60px) * 0.14)",
						align: "center",
						scopedSlots: { customRender: "action" }
					},
					{
						title: "工步号",
						dataIndex: "stepId",
						align: "center",
						width: "calc((45vw - 60px) * 0.43)",
						scopedSlots: { customRender: "stepId" }
					},
					{
						title: "充/放电电流(A)",
						dataIndex: "rateCurrent",
						align: "center",
						width: "calc((45vw - 60px) * 0.43)",
						scopedSlots: { customRender: "rateCurrent" }
					}
				],
				rateStressColumns: [
					{
						title: "操作",
						width: "calc((45vw - 60px) * 0.14)",
						align: "center",
						scopedSlots: { customRender: "action" }
					},
					{
						title: "工步号",
						dataIndex: "stepId",
						align: "center",
						width: "calc((45vw - 60px) * 0.43)",
						scopedSlots: { customRender: "stepId" }
					},
					{
						title: "放电电流(A)",
						dataIndex: "rateCurrent",
						align: "center",
						width: "calc((45vw - 60px) * 0.43)",
						scopedSlots: { customRender: "rateCurrent" }
					}
				],
				pulseDischargeMColumns: [
					{
						title: "类型",
						dataIndex: "pulseDischargeType",
						align: "center",
						width: "calc((45vw - 60px) * 0.43)",
						scopedSlots: { customRender: "pulseDischargeType" }
					},
					{
						title: "工步号",
						dataIndex: "stepId",
						align: "center",
						width: "calc((45vw - 60px) * 0.43)",
						scopedSlots: { customRender: "stepId" }
					}
				],
				pulseDischargeMapColumns: [
					{
						title: "类型",
						dataIndex: "pulseDischargeType",
						align: "center",
						width: "calc((45vw - 60px) * 0.43)",
						scopedSlots: { customRender: "pulseDischargeType" }
					},
					{
						title: "工步号",
						dataIndex: "stepId",
						align: "center",
						width: "calc((45vw - 60px) * 0.43)",
						scopedSlots: { customRender: "stepId" }
					}
				],
				resDcirColumns: [
					{
						title: "工步名称",
						dataIndex: "orderNumber",
						align: "center",
						width: "calc((45vw - 60px) * 0.25)",
						customRender: (text, record, index) => {
							return index ? '搁置工步' + text : "放电工步" + text
						}
					},
					{
						title: "工步号",
						align: "center",
						dataIndex: "stepId",
						width: "calc((45vw - 60px) * 0.25)",

						scopedSlots: { customRender: "stepId" }
					},
					{
						title: "工步时间",
						dataIndex: "stepTime",
						align: "center",
						width: "calc((45vw - 60px) * 0.25)",
						scopedSlots: { customRender: "stepTime" }
					},
					{
						title: "附加条件",
						align: "center",
						dataIndex: "additionalCondition",
						width: "calc((45vw - 60px) * 0.25)",
						scopedSlots: { customRender: "additionalCondition" }
					}
				],
				socOcvColumns: [
					{
						title: "操作",
						align: "center",
						width: "calc((45vw - 60px) * 0.25)",
						scopedSlots: { customRender: "action" },
					},
					{
						title: "SOC",
						align: "center",
						dataIndex: "socNum",
						width: "calc((45vw - 60px) * 0.25)",
						// scopedSlots: { customRender: "soc" },
						customRender: (text, record, index) => {
							const obj = {
								children: (
									<div class="soc-input">
										<a-input-number
											style="width:calc((45vw - 50px) * 0.085)"
											v-model={record.socNum}
										/>
										<span class="text">%SOC</span>
									</div>
								),
								attrs: {}
							}
							// obj.attrs.rowSpan = 0
							// if (index == 0 || index % 2 == 0) {
							//   obj.attrs.rowSpan = 2
							// }

							return obj
						}
					},
					{
						title: "循环号",
						width: "calc((45vw - 60px) * 0.25)",
						align: "center",
						dataIndex: "cycleId",
						scopedSlots: { customRender: "cycleId" }
					},
					{
						title: "工步号",
						width: "calc((45vw - 60px) * 0.25)",
						align: "center",
						dataIndex: "stepId",
						scopedSlots: { customRender: "stepId" }
					}
				],
				socDcirCylclesColumns: [
					{
						title: "操作",
						width: "calc((45vw - 60px) * 0.14)",
						align: "center",
						scopedSlots: { customRender: "action" }
					},
					{
						title: "循环周数",
						dataIndex: "cycle",
						align: "center",
						width: "calc((45vw - 60px) * 0.43)",
						scopedSlots: { customRender: "cycle" }
					},
					{
						title: "循环号",
						width: "calc((45vw - 60px) * 0.43)",
						align: "center",
						dataIndex: "cycleId",
						scopedSlots: { customRender: "cycleId" }
					}
				],
				socDcrColumns: [
					{
						title: "操作",
						width: "calc((45vw - 60px) * 0.14)",
						align: "center",
						scopedSlots: { customRender: "action" }
					},
					{
						title: "SOC",
						dataIndex: "soc",
						align: "center",
						width: "calc((45vw - 50px) * 0.43)",
						scopedSlots: { customRender: "soc" }
					},
					{
						title: "循环号",
						width: "calc((45vw - 60px) * 0.43)",
						align: "center",
						dataIndex: "cycleId",
						scopedSlots: { customRender: "cycleId" }
					}
				],
				resDcirTempColumns: [
					{
						title: "样品编号",
						width: "calc((45vw - 60px) * 0.25)",
						align: "center",
						dataIndex: "orderno"
					},
					{
						title: "设备编号",
						width: "calc((45vw - 60px) * 0.25)",
						align: "center",
						dataIndex: "equiptcode"
					},
					{
						title: "通道编号",
						width: "calc((45vw - 60px) * 0.25)",
						align: "center",
						dataIndex: "channelno"
					},
					{
						title: "温度",
						dataIndex: "temp",
						align: "center",
						width: "calc((45vw - 60px) * 0.25)",
						scopedSlots: { customRender: "temp" }
					},
				],
				socOcvTempColumns: [
					{
						title: "样品编号",
						width: "calc((45vw - 60px) * 0.33)",
						align: "center",
						dataIndex: "orderno"
					},
					{
						title: "设备编号",
						width: "calc((45vw - 60px) * 0.33)",
						align: "center",
						dataIndex: "equiptcode"
					},
					{
						title: "通道编号",
						width: "calc((45vw - 60px) * 0.33)",
						align: "center",
						dataIndex: "channelno"
					},
					// {
					// 	title: "数据类型",
					// 	dataIndex: "dataType",
					// 	align: "center",
					// 	width: "calc((45vw - 50px) * 0.25)",
					// 	scopedSlots: { customRender: "dataType" }
					// },
				],
				cyclesFillColumns: [
					// {
					// 	title: "操作",
					// 	width: "calc((45vw - 50px) * 0.14)",
					// 	align: "center",
					// 	scopedSlots: { customRender: "action" }
					// },
					// {
					// 	title: "循环序号",
					// 	dataIndex: "cycle",
					// 	align: "center",
					// 	width: "calc((45vw - 50px) * 0.43)",
					// 	scopedSlots: { customRender: "cycle" }
					// },
					// {
					// 	title: "循环号",
					// 	width: "calc((45vw - 50px) * 0.43)",
					// 	align: "center",
					// 	dataIndex: "cycleId",
					// 	scopedSlots: { customRender: "cycleId" }
					// }
					{
						title: "名称",
						dataIndex: "cycle",
						align: "center",
						width: "calc((45vw - 60px) * 0.43)",
						customRender: (text, record, index) => {
							return '工步号'
						}
					},
					{
            slots: { title: "stepIdTitle" },
						dataIndex: "stepId",
						align: "center",
						width: "calc((45vw - 60px) * 0.43)",
						scopedSlots: { customRender: "cycleId" }
					}
				],
				columns: [
					{
						title: "操作",
						align: "center",
						width: 70,
						scopedSlots: { customRender: "action1" }
					},
					{
						title: "序号",
						align: "center",
						width: 50,
						customRender: (text, record, index) => {
							if (!record.isChild) {
								return index + 1
							}
						}
					},
					{
						title: "委托单号",
						dataIndex: "folderno",
						align: "center",
						width: 90
					},
					{
						title: "主题",
						dataIndex: "theme",
						align: "center",
						ellipsis: true,
						width: 90,
						scopedSlots: { customRender: "theme" }
					},
					{
						title: "样品编号",
						width: 90,
						align: "center",
						dataIndex: "orderno"
					},
					{
						title: "测试项目编码",
						width: 90,
						align: "center",
						dataIndex: "testcode"
						//scopedSlots: {customRender: 'updateText'},
					},

					{
						title: "测试项目别名",
						width: 90,
						align: "center",
						dataIndex: "alias"
						//scopedSlots: {customRender: 'updateText'},
					},
					{
						title: "测试编码",
						width: 90,
						align: "center",
						dataIndex: "celltestcode",
						scopedSlots: { customRender: "celltestcode" }
					},
					{
						title: "数据位置",
						width: 60,
						align: "center",
						dataIndex: "dataPath",
						ellipsis: true,
						scopedSlots: { customRender: "dataPath" }
					},
					{
						title: "开始时间",
						width: 90,
						align: "center",
						dataIndex: "startTime",
						customRender: (text, record, index) => {
							if (null != text) {
								return moment(text).format("YYYY-MM-DD")
							}
							return text
						}
						//
						//scopedSlots: {customRender: 'updateText'},
					},
					{
						title: "结束时间",
						width: 90,
						align: "center",
						dataIndex: "endTime",
						customRender: (text, record, index) => {
							if (null != text) {
								return moment(text).format("YYYY-MM-DD")
							}
							return text
						}
					},
					{
						title: "设备编号",
						width: 60,
						align: "center",
						dataIndex: "equiptcode"
					},
					{
						title: "通道编号",
						width: 60,
						align: "center",
						dataIndex: "channelno"
					}
				],
				columns1: [
					{
						title: "序号",
						align: "center",
						width: 50,
						customRender: (text, record, index) => index + 1
					},
					{
						title: "委托单号",
						dataIndex: "folderno",
						align: "center",
						width: 90
					},
					{
						title: "样品编号",
						width: 90,
						align: "center",
						dataIndex: "orderno"
					},
					{
						title: "测试项目编码",
						width: 90,
						align: "center",
						dataIndex: "testcode"
						//scopedSlots: {customRender: 'updateText'},
					},
					{
						title: "测试项目别名",
						width: 90,
						align: "center",
						dataIndex: "alias"
						//scopedSlots: {customRender: 'updateText'},
					},
					{
						title: "测试编码",
						width: 90,
						align: "center",
						dataIndex: "celltestcode",
						scopedSlots: { customRender: "celltestcode" }
					},
					{
						title: "开始时间",
						width: 90,
						align: "center",
						dataIndex: "startTime",
						customRender: (text, record, index) => {
							if (null != text) {
								return moment(text).format("YYYY-MM-DD")
							}
							return text
						}
						//
						//scopedSlots: {customRender: 'updateText'},
					},
					{
						title: "结束时间",
						width: 90,
						align: "center",
						dataIndex: "endTime",
						customRender: (text, record, index) => {
							if (null != text) {
								return moment(text).format("YYYY-MM-DD")
							}
							return text
						}
					},
					{
						title: "设备编号",
						width: 60,
						align: "center",
						dataIndex: "equiptcode"
					},
					{
						title: "通道编号",
						width: 60,
						align: "center",
						dataIndex: "channelno"
					}
				],
				form: this.$form.createForm(this),
				selectedRowKeys: [],
				selectedRows: [],
        deleteSelectedRowKeys: [],
        deleteRowSelection: {
          columnWidth: 20,
          onChange: (selectedRowKeys, selectedRows) => {
            this.deleteSelectedRowKeys = selectedRowKeys
            this.deleteSelectedRow = selectedRows
          }
        },
        deleteSelectedRow: [],
				projectSelectedRowKeys: [],
				projectSelectedRows: [],
				height: "500px",
				saveParam: null,
				productList: [],
				// 存储3数据选择最后输入的select值
				lastDataSelect: {
					key1: "",
					key2: "",
					key3: ""
				}
			}
		},
		computed: {
			...mapGetters(["testTaskFilterData"]),
			isBasicInfo() {
				return (
					this.reportBasic.productName &&
					this.reportBasic.current &&
					this.reportBasic.num &&
					this.reportBasic.temp &&
					this.reportBasic.reportName &&
					this.reportBasic.testMethod &&
					this.reportBasic.cRateUnit &&
					this.reportBasic.charge &&
					this.reportBasic.chargeUnit &&
					this.reportBasic.discharge &&
					this.reportBasic.dischargeUnit
				)
			}
		},
		mounted() {
			getProductList({})
				.then(res => (this.productList = res.data))
				.then(() => {
					this.param = this.testTaskFilterData
					this.$store.commit("setTaskFilterData", null)
					if (this.param != null) {
						this.orderData = this.param.orderData
						this.reportBasic = this.param.reportBasic
						this.issueId = this.reportBasic.issueId + ""
						this.reportType = this.param.reportType
						if (this.param.reportType == '循环DCR') {
							this.socDcirFilterData = this.param.socDcirQueryParam.filterList;
							this.socDcirCycleData = this.param.socDcirQueryParam.cycleList
						} if (this.param.reportType == 'SOC-DCR') {
							this.socDcrData = this.param.socDcrData
						} else if (this.param.reportType == '内阻-DCR') {
							this.resDcirTempData = this.param.resDcirQueryParam.resDcirTempList;
							this.resDcirFilterData = this.param.resDcirQueryParam.resDcirFilterList
						} else if (this.param.reportType == '倍率-倍率充放电&恒功率放电') {
							this.reportBasic.cRateUnit = this.param.cRateQueryParam.cRateUnit
							this.cRateData = this.param.cRateQueryParam.stepIdAndCurrentList
							this.getCRateColumns()
						} else if (this.param.reportType == 'SOC-OCV map') {
							this.socOcvTypeData = this.param.socOcvQueryParam.socOcvTypeDataList;
							this.socOcvChartParam.chargeTitle = this.param.socOcvQueryParam.chargeTitle;
							this.socOcvChartParam.dischargeTitle = this.param.socOcvQueryParam.dischargeTitle;
							this.socOcvFilterDataCharge = this.param.socOcvQueryParam.socOcvFilterDataChargeList;
							this.socOcvFilterDataDischarge = this.param.socOcvQueryParam.socOcvFilterDataDischargeList;
						} else if (this.param.reportType == 'SOC-OCV map charge') {
							this.socOcvTypeData = this.param.socOcvQueryParam.socOcvTypeDataList;
							this.socOcvChartParam.chargeTitle = this.param.socOcvQueryParam.chargeTitle;
							this.socOcvFilterDataCharge = this.param.socOcvQueryParam.socOcvFilterDataChargeList;
						} else if (this.param.reportType == 'SOC-OCV map discharge') {
							this.socOcvTypeData = this.param.socOcvQueryParam.socOcvTypeDataList;
							this.socOcvChartParam.dischargeTitle = this.param.socOcvQueryParam.dischargeTitle;
							this.socOcvFilterDataDischarge = this.param.socOcvQueryParam.socOcvFilterDataDischargeList;
						} else if (this.param.reportType == '循环') {
							this.cycleReportStepData[0].stepId = this.param.cycleReportParam.cycleReportStepData;
							this.tableType = this.param.tableType?this.param.tableType:'step'
						} else if (this.param.reportType == '倍率-stress') {
							this.cRateData = this.param.cRateQueryParam.stepIdAndCurrentList
						} else if (this.param.reportType == '倍率-脉冲放电') {
							this.reportBasic.current = this.param.pulseDischargeQueryMParam.current
							this.pulseDischargeMData = this.param.pulseDischargeQueryMParam.stepIdAndPulseDischargeType
						}
					}
				})

      this.$nextTick(() => {
        let tableContainer = this.$refs.tableContainer
        this.rowDrop(tableContainer)
      })
		},
		created() {
			if (!this.reportBasic.cRateUnit) {
				this.reportBasic.cRateUnit = 'A'
			}
		},
		methods: {
      rowDrop(dom) {
        new Sortable.create(dom.querySelector('.ant-table>.ant-table-content .ant-table-tbody'), {
          handle: '.drag', // 按钮拖拽
          animation: 150,
          onEnd: ({newIndex, oldIndex}) => {
            // 拖拽后回调
            const currRow = this.orderData.splice(oldIndex, 1)[0]
            this.orderData.splice(newIndex, 0, currRow)
          }
        })
      },
			getCRateColumns() {
				let columnItem;
				this.cRateColumns.splice(this.cRateColumns.length - 1, 1)
				if (this.reportBasic.cRateUnit === 'A') {
					columnItem = {
						title: "充/放电电流(A)",
						dataIndex: "rateCurrent",
						align: "center",
						width: "calc((45vw - 50px) * 0.43)",
						scopedSlots: { customRender: "rateCurrent" }
					}
				} else {
					columnItem = {
						title: "充/放电功率(W)",
						dataIndex: "rateCurrent",
						align: "center",
						width: "calc((45vw - 50px) * 0.43)",
						scopedSlots: { customRender: "rateCurrent" }
					}
				}
				this.cRateColumns.push(columnItem)
			},
			// 同步按钮事件
			handleSynchronization() {
				this.socDcirCycleData.forEach(v => {
					if (!v.cycle && v.cycleId) {
						v.cycle = v.cycleId
					} else if (v.cycle && !v.cycleId) {
						v.cycleId = v.cycle
					}
				})
			},



			/* 输入事件 */
			handleSocInput(value, index) {
				// 电芯
				// if (isSoc > 1) {
				let have = this.socDcirFilterData.filter(v => v.socNum == value.target._value)
				if (have.length > 2) {
					this.$message.warn("请勿重复添加")
					this.socDcirFilterData[index].soc = ""
					return (this.socDcirFilterData[index].socNum = null)
				}
				this.socDcirFilterData[index].soc = value.target._value + "%SOC"
				this.socDcirFilterData[index + 1].soc = value.target._value + "%SOC"
				this.socDcirFilterData[index + 1].socNum = value.target._value
				// 检查是否有一样的cycleId
				const unique = []
				const duplicateList = []
				for (let i = 0; i < this.socDcirFilterData.length; i++) {
					const duplicate = unique.find(obj => obj.stepId == this.socDcirFilterData[i].stepId && this.socDcirFilterData[i].stepId !== null && this.socDcirFilterData[i].stepId !== '')
					if (!duplicate) unique.push(this.socDcirFilterData[i])
					if (duplicate) duplicateList.push({ index: i, ...this.socDcirFilterData[i] })
				}

				// 如果有相同的cycleId,提醒+清空
				if (duplicateList.length > 0) {
					this.$message.warn("循环号" + duplicateList.map(item => item.stepId).join(",") + "已存在，请勿重复添加")
					duplicateList.forEach(v => {
						this.socDcirFilterData[v.index].stepId = null
					})
				}

				// 填写完才能排序
				let haveEmpty = this.socDcirFilterData.find(
					s =>
						s.stepId == null ||
						s.stepId == "" ||
						s.socNum == null ||
						s.socNum < 0 ||
						s.orderNumber == null ||
						s.orderNumber == "" ||
						(s.table == "data" && (s.stepTime == null || s.stepTime == ""))
				)

				if (haveEmpty) {
					return
				}

				// 排序
				this.socDcirFilterData.sort((item1, item2) => {
					if (item1.socNum == item2.socNum) {
						return Number(item1.stepId) - Number(item2.stepId)
					} else {
						return item2.socNum - item1.socNum
					}
				})
			},
			handleChargingInput(value, index) {
				// 填写完才能排序
				let haveEmpty = this.socOcvFilterDataCharge.find(
					s =>
						s.stepId == null ||
						s.stepId == "" ||
						s.socNum == null ||
						s.socNum < 0 ||
						s.cycleId == null ||
						s.cycleId == ""
				)
				if (haveEmpty) {
					return
				}
				// 排序(正向)

				this.socOcvFilterDataCharge.sort((item1, item2) => {
					if (this.socOcvChargingSort == 1) return Number(item1.socNum) - Number(item2.socNum)
					if (this.socOcvChargingSort == 2) return Number(item2.socNum) - Number(item1.socNum)
				})
			},
			handleDischargingInput(value, index) {
				// 填写完才能排序
				let haveEmpty = this.socOcvFilterDataDischarge.find(
					s =>
						s.stepId == null ||
						s.stepId == "" ||
						s.socNum == null ||
						s.socNum < 0 ||
						s.cycleId == null ||
						s.cycleId == ""
				)

				if (haveEmpty) {
					return
				}
				// 排序(正向)
				this.socOcvFilterDataDischarge.sort((item1, item2) => {
					if (this.socOcvDischargingSort == 1) return Number(item1.socNum) - Number(item2.socNum)
					if (this.socOcvDischargingSort == 2) return Number(item2.socNum) - Number(item1.socNum)
				})
			},
			handleCRateInput(value, index) {
				// isStep 0：工步号 1：充/放电电流(A)
				// 检查是否有一样的cycleId
				const unique = []
				const duplicateList = []
				for (let i = 0; i < this.cRateData.length; i++) {
					const duplicate = unique.find(obj => obj.stepId == this.cRateData[i].stepId && this.cRateData[i].stepId !== null && this.cRateData[i].stepId !== '')
					if (!duplicate) unique.push(this.cRateData[i])
					if (duplicate) duplicateList.push({ index: i, ...this.cRateData[i] })
				}

				// 如果有相同的cycleId,提醒+清空
				if (duplicateList.length > 0) {
					this.$message.warn("工步号" + duplicateList.map(item => item.stepId).join(",") + "已存在，请勿重复添加")
					duplicateList.forEach(v => {
						this.cRateData[v.index].stepId = null
					})
				}


				// 填写完成后才排序
				let haveEmpty = this.cRateData.find(
					s => s.stepId == null || s.stepId == "" || s.rateCurrent == null || s.rateCurrent == ""
				)

				if (haveEmpty) {
					return
				}

				// 排序(正向)
				this.cRateData.sort((item1, item2) => {
					return Number(item1.stepId) - Number(item2.stepId)
				})
			},
			handleRateStressInput(value, index, isStep = 0) {
				// 检查是否有一样的cycleId
				const unique = []
				const duplicateList = []
				for (let i = 0; i < this.rateStressData.length; i++) {
					const duplicate = unique.find(obj => obj.stepId == this.rateStressData[i].stepId && this.rateStressData[i].stepId !== null && this.rateStressData[i].stepId !== '')
					if (!duplicate) unique.push(this.rateStressData[i])
					if (duplicate) duplicateList.push({ index: i, ...this.rateStressData[i] })
				}

				// 如果有相同的stepId,提醒+清空
				if (duplicateList.length > 0) {
					this.$message.warn("工步号" + duplicateList.map(item => item.stepId).join(",") + "已存在，请勿重复添加")
					duplicateList.forEach(v => {
						this.rateStressData[v.index].stepId = null
					})
				}

				// 填写完成后才排序
				let haveEmpty = this.rateStressData.find(
					s => s.stepId == null || s.stepId == "" || s.rateCurrent == null || s.rateCurrent == ""
				)

				if (haveEmpty) {
					return
				}

				// 排序(正向)
				this.rateStressData.sort((item1, item2) => {
					return Number(item1.stepId) - Number(item2.stepId)
				})
			},
			handleCycleIdInput(value, index, isCycle = 0) {
				// 循环周数 isCycle 0:循环号 1：循环周数
				// 检查是否有一样的cycleId
				const unique = []
				const duplicateList = []
				for (let i = 0; i < this.socDcirCycleData.length; i++) {
					const duplicate = unique.find(obj => obj.cycleId == this.socDcirCycleData[i].cycleId && this.socDcirCycleData[i].cycleId !== null && this.socDcirCycleData[i].cycleId !== '')
					if (!duplicate) unique.push(this.socDcirCycleData[i])
					if (duplicate) duplicateList.push({ index: i, ...this.socDcirCycleData[i] })
				}

				// 如果有相同的cycleId,提醒+清空
				if (duplicateList.length > 0) {
					this.$message.warn("循环号" + duplicateList.map(item => item.cycleId).join(",") + "已存在，请勿重复添加")
					duplicateList.forEach(v => {
						this.socDcirCycleData[v.index].cycleId = null
					})
				}

				// 填写完成后才排序
				let haveEmpty = this.socDcirCycleData.find(
					s => s.cycleId == null || s.cycleId == "" || s.cycle == null || s.cycle == ""
				)

				if (haveEmpty) {
					return
				}

				// 排序(正向)
				this.socDcirCycleData.sort((item1, item2) => {
					return Number(item1.cycleId) - Number(item2.cycleId)
				})
			},
			handleSocDcrInput(value, index, isCycle = 0) {
				// 循环周数 isCycle 0:循环号 1：循环周数
				// 检查是否有一样的cycleId
				const unique = []
				const duplicateList = []
				for (let i = 0; i < this.socDcrData.length; i++) {
					const duplicate = unique.find(obj => obj.soc == this.socDcrData[i].soc && this.socDcrData[i].soc !== null && this.socDcrData[i].soc !== '')
					if (!duplicate) unique.push(this.socDcrData[i])
					if (duplicate) duplicateList.push({ index: i, ...this.socDcrData[i] })
				}

				// 如果有相同的cycleId,提醒+清空
				if (duplicateList.length > 0) {
					this.$message.warn("循环号" + duplicateList.map(item => item.soc).join(",") + "已存在，请勿重复添加")
					duplicateList.forEach(v => {
						this.socDcrData[v.index].soc = null
					})
				}

				// 填写完成后才排序
				let haveEmpty = this.socDcrData.find(
					s => s.soc == null || s.soc == "" || s.soc == null || s.soc == ""
				)

				if (haveEmpty) {
					return
				}

				// 排序(正向)
				/*this.socDcrData.sort((item1, item2) => {
					return Number(item1.soc) - Number(item2.soc)
				})*/
			},
			handlePulseDischargeMInput(value, index, isStep = 0) {
				if (!isStep) {
					let have = this.pulseDischargeMData.filter(v => v.stepId == value.target._value)
					if (have.length > 1) {
						this.$message.warn("工步号" + value.target._value + "已存在，请勿重复添加")
						return (this.pulseDischargeMData[index].stepId = null)
					}
				}
			},
			// 内阻DCIR 填写工布时间，清空附加条件
			handleResDcirBlur({ target }, index) {
				this.resDcirFilterData[index].additionalCondition = null
			},

			/* 动态改变事件 */
			// 内阻DCIR 选择附加条件，工步时间清空
			handleResDcirChange(value, index) {
				this.resDcirFilterData[index].stepTime = ''
			},
			changeProduct(product) {
				let checkProduct = this.productList.find(p => p.id == product)

				this.reportBasic.issueId = checkProduct.issueId
				this.reportBasic.productName = checkProduct.productName
			},
			cRateUnitChange() {
				this.getCRateColumns()
				this.cRateData.length = 0
				if (this.reportBasic.cRateUnit === 'A') {
					this.cRateData = [
						{ stepId: "4", rateCurrent: "0.6" },
						{ stepId: "8", rateCurrent: "3" },
						{ stepId: "12", rateCurrent: "4" },
						{ stepId: "16", rateCurrent: "5" },
						{ stepId: "20", rateCurrent: "6" },
						{ stepId: "24", rateCurrent: "8" }
					]
				} else {
					this.cRateData = [
						{ stepId: "6", rateCurrent: "1" },
						{ stepId: "11", rateCurrent: "15" },
						{ stepId: "16", rateCurrent: "30" },
						{ stepId: "21", rateCurrent: "45" },
						{ stepId: "26", rateCurrent: "50" },
						{ stepId: "31", rateCurrent: "60" }
					]
				}
			},
			onSelectChange(record, selected) {
				this.outFlowRecord = record
				this.outQueryFlowRecord = record

				if (selected) {
					if (record.flowId == null) {
						this.$message.warn("测试数据为空")
						return
					}

					if (!this.selectedRowKeys.includes(record.uuid)) {
						this.selectedRowKeys.push(record.uuid)
						this.selectedRows.push(record)
						this.orderData.push(record)
						this.resDcirTempData.push(record)
						this.socOcvTypeData.push(record)
					}
				} else {
					for (let i = 0; i < this.selectedRowKeys.length; i++) {
						if (this.selectedRowKeys[i] === record.uuid) {
							this.selectedRowKeys.splice(i, 1)
							this.selectedRows.splice(i, 1)
							this.orderData.splice(i, 1)
							this.resDcirTempData.splice(i, 1)
							this.socOcvTypeData.splice(i, 1)
							break
						}
					}
				}
			},
			handleVerifyInput(data, index, target) {
				// 只允许输入数字、空格、英文逗号
				data[index][target] = data[index][target].replace(/[^0-9,\s]/g, '')

				// 讲空格转为英文逗号
				data[index][target] = data[index][target].replace(/\s+/g, ',')
			},
			onSelectChangeFlow(record, handle) {
				if (handle == "查看") {
					this.outQueryFlowRecord.flowId = record.flowId
					this.$refs.stepData.query(this.outQueryFlowRecord, false)
					return
				}

				this.outFlowRecord.flowId = record.flowId

				if (!this.selectedRowKeys.includes(this.outFlowRecord.uuid)) {
					this.selectedRowKeys.push(this.outFlowRecord.uuid)
					this.selectedRows.push(this.outFlowRecord)
					this.orderData.push(this.outFlowRecord)
				}

				this.visibleFlow = false
			},

			onSelectAllChange(selected, selectedRows, changeRows) {
				if (selected) {
					selectedRows.forEach(item => {
						if (!this.selectedRowKeys.includes(item.uuid) && item.flowId != null) {
							this.selectedRowKeys.push(item.uuid)
							this.selectedRows.push(item)
							this.orderData.push(item)
							this.resDcirTempData.push(item)
							this.socOcvTypeData.push(item)
						}
					})
				} else {
					for (let i = 0; i < changeRows.length; i++) {
						if (this.selectedRowKeys.includes(changeRows[i].uuid)) {
							let index = this.selectedRowKeys.indexOf(changeRows[i].uuid)
							this.selectedRowKeys.splice(index, 1)
							this.selectedRows.splice(index, 1)
							this.orderData.splice(index, 1)
							this.resDcirTempData.splice(index, 1)
							this.socOcvTypeData.splice(index, 1)
						}
					}
				}
			},


			/* 增加事件 */
			addSocDcirFilterData() {
				this.socDcirFilterData.push(
					{
						soc: "",
						table: "step",
						stepId: null,
						stepTime: "",
						orderNumber: 1,
						socNum: null
					},
					{
						soc: "",
						table: "step",
						stepId: null,
						stepTime: "",
						orderNumber: 2,
						socNum: null
					}
				)
			},

			addSocDcirCycleData() {
				this.socDcirCycleData.push({
					cycleId: "",
					cycle: ""
				})
			},
			addSocCycleData() {
				this.socDcrData.push({
					cycleId: "",
					soc: ""
				})
			},

			addCRateData() {
				this.cRateData.push({
					stepId: "",
					rateCurrent: ""
				})
			},

			addRateStressData() {
				this.rateStressData.push({
					stepId: "",
					rateCurrent: ""
				})
			},

			addSocOcvChargeData() {
				this.socOcvFilterDataCharge.push({ soc: "", table: "step", stepId: "", cycleId: "", socNum: "" });
			},

			addSocOcvDischargeData() {
				this.socOcvFilterDataDischarge.push({ soc: "", table: "step", stepId: "", cycleId: "", socNum: "" });
			},

			/*粘贴事件*/
			handleChargingPaste(value, index, dataIndex) {
				// dataIndex 0: 工步号 stepId 1: 循环号 cycleId
				const temList = event.clipboardData.getData("text").replace(/[\n]$/, "").split("\n")
				temList.forEach((v, vIndex) => {
					const targetValue = v.replace(/[\r]$/, "").split("\t")
					if (!this.socOcvFilterDataCharge[index + vIndex]) {
						this.socOcvFilterDataCharge.push({ soc: "", table: "step", stepId: "", cycleId: "", socNum: "" })
					}

					// 从循环号开始粘贴
					if (dataIndex) {
						this.socOcvFilterDataCharge[index + vIndex].cycleId = targetValue[0]
						this.socOcvFilterDataCharge[index + vIndex].stepId = targetValue.length > 1 ? targetValue[1] : this.socOcvFilterDataCharge[index + vIndex].stepId
					} else {
						this.socOcvFilterDataCharge[index + vIndex].stepId = targetValue[0]
					}
				})
				setTimeout(() => {
					if (dataIndex) {
						this.socOcvFilterDataCharge[index].cycleId = temList[0].replace(/[\r]$/, "").split("\t")[0]
					} else {
						this.socOcvFilterDataCharge[index].stepId = temList[0].replace(/[\r]$/, "").split("\t")[0]
					}
				}, 10)
			},
			handleDischargingPaste(value, index, dataIndex = 0) {
				// dataIndex 0: 工步号 stepId 1: 循环号 cycleId
				const temList = event.clipboardData.getData("text").replace(/[\n]$/, "").split("\n")
				temList.forEach((v, vIndex) => {
					const targetValue = v.replace(/[\r]$/, "").split("\t")
					if (!this.socOcvFilterDataDischarge[index + vIndex]) {
						this.socOcvFilterDataDischarge.push({ soc: "", table: "step", stepId: "", cycleId: "", socNum: "" })
					}

					// 从循环号开始粘贴
					if (dataIndex) {
						this.socOcvFilterDataDischarge[index + vIndex].cycleId = targetValue[0]
						this.socOcvFilterDataDischarge[index + vIndex].stepId = targetValue.length > 1 ? targetValue[1] : this.socOcvFilterDataDischarge[index + vIndex].stepId
					} else {
						this.socOcvFilterDataDischarge[index + vIndex].stepId = targetValue[0]

					}
					setTimeout(() => {
						if (dataIndex) {
							this.socOcvFilterDataDischarge[index].cycleId = temList[0].replace(/[\r]$/, "").split("\t")[0]
						} else {
							this.socOcvFilterDataDischarge[index].stepId = temList[0].replace(/[\r]$/, "").split("\t")[0]
						}
					}, 10)


				})
			},
			handleCRatePaste(value, index, isStep = 0) {
				const temList = event.clipboardData.getData("text").replace(/[\n]$/, "").split("\n")
				temList.forEach((v, vIndex) => {
					const targetValue = v.replace(/[\r]$/, "").split("\t")
					if (!this.cRateData[index + vIndex]) {
						this.cRateData.push({
							stepId: "",
							rateCurrent: ""
						})
					}

					// 从充/放电电流(A)开始粘贴
					if (isStep) {
						this.cRateData[index + vIndex].rateCurrent = targetValue[0]
					} else {
						this.cRateData[index + vIndex].stepId = targetValue[0]
						this.cRateData[index + vIndex].rateCurrent = targetValue.length > 1 ? targetValue[1] : this.cRateData[index + vIndex].rateCurrent
					}
				})
				setTimeout(() => {
					if (isStep) {
						this.cRateData[index].rateCurrent = temList[0].replace(/[\r]$/, "").split("\t")[0]
					} else {
						this.cRateData[index].stepId = temList[0].replace(/[\r]$/, "").split("\t")[0]
					}
				}, 10)
			},
			handleRateStressPaste(value, index, isStep = 0) {
				// isStep 0：工步号 1：放电电流(A)
				const temList = event.clipboardData.getData("text").replace(/[\n]$/, "").split("\n")
				temList.forEach((v, vIndex) => {
					const targetValue = v.replace(/[\r]$/, "").split("\t")
					if (!this.rateStressData[index + vIndex]) {
						this.rateStressData.push({
							stepId: "",
							rateCurrent: ""
						})
					}

					// 放电电流(A)
					if (isStep) {
						this.rateStressData[index + vIndex].rateCurrent = targetValue[0]
					} else {
						this.rateStressData[index + vIndex].stepId = targetValue[0]
						this.rateStressData[index + vIndex].rateCurrent = targetValue.length > 1 ? targetValue[1] : this.rateStressData[index + vIndex].rateCurrent
					}
				})
				setTimeout(() => {
					if (isStep) {
						this.rateStressData[index].rateCurrent = temList[0].replace(/[\r]$/, "").split("\t")[0]
					} else {
						this.rateStressData[index].stepId = temList[0].replace(/[\r]$/, "").split("\t")[0]
					}
				}, 10)
			},
			handleCycleIdPaste(value, index, isCycle = 0) {
				// 循环周数 isCycle 0:循环号 1：循环周数
				const temList = event.clipboardData.getData("text").replace(/[\n]$/, "").split("\n")
				temList.forEach((v, vIndex) => {
					const targetValue = v.replace(/[\r]$/, "").split("\t")
					if (!this.socDcirCycleData[index + vIndex]) {
						this.socDcirCycleData.push({
							cycleId: "",
							cycle: ""
						})
					}
					// 从循环周数开始粘贴
					if (isCycle) {
						this.socDcirCycleData[index + vIndex].cycle = targetValue[0]
						this.socDcirCycleData[index + vIndex].cycleId = targetValue.length > 1 ? targetValue[1] : this.socDcirCycleData[index + vIndex].cycleId
					} else {
						this.socDcirCycleData[index + vIndex].cycleId = targetValue[0]
					}
				})
				setTimeout(() => {
					if (isCycle) {
						this.socDcirCycleData[index].cycle = temList[0].replace(/[\r]$/, "").split("\t")[0]
					} else {
						this.socDcirCycleData[index].cycleId = temList[0].replace(/[\r]$/, "").split("\t")[0]
					}
				}, 10)
			},
			handleSocDcrPaste(value, index,isCycle = 0) {
				// 循环周数 isCycle 0:循环号 1：循环周数
				const temList = event.clipboardData.getData("text").replace(/[\n]$/, "").split("\n")
				temList.forEach((v, vIndex) => {
					const targetValue = v.replace(/[\r]$/, "").split("\t")
					if (!this.socDcrData[index + vIndex]) {
						this.socDcrData.push({
							soc: "",
              cycleId: ""
						})
					}
					// 从循环周数开始粘贴
					if (isCycle) {
						this.socDcrData[index + vIndex].soc = targetValue[0]
						this.socDcrData[index + vIndex].cycleId = targetValue.length > 1 ? targetValue[1] : this.socDcrData[index + vIndex].cycleId
					} else {
						this.socDcrData[index + vIndex].cycleId = targetValue[0]
					}
				})
				setTimeout(() => {
					if (isCycle) {
						this.socDcrData[index].soc = temList[0].replace(/[\r]$/, "").split("\t")[0]
					} else {
						this.socDcrData[index].cycleId = temList[0].replace(/[\r]$/, "").split("\t")[0]
					}
				}, 10)
			},
			// isSoc 0 : 工步号  1: 工步时间
			handleSocPaste(value, index, isSoc = 0) {
				const temList = event.clipboardData.getData("text").replace(/[\n]$/, "").split("\n")
				temList.forEach((v, vIndex) => {
					const targetValue = v.replace(/[\r]$/, "").split("\t")
					if (!this.socDcirFilterData[index + vIndex]) {
						this.socDcirFilterData.push(
							{
								soc: "",
								table: "step",
								stepId: null,
								stepTime: "",
								orderNumber: 1,
								socNum: null
							},
							{
								soc: "",
								table: "step",
								stepId: null,
								stepTime: "",
								orderNumber: 2,
								socNum: null
							}
						)
					}

					// 从工步号开始粘贴
					if (isSoc === 0) {
						this.socDcirFilterData[index + vIndex].stepId = targetValue[0]
						this.socDcirFilterData[index + vIndex].stepTime = targetValue.length > 1 ? targetValue[1] : this.socDcirFilterData[index + vIndex].stepTime
					} else {
						this.socDcirFilterData[index + vIndex].stepTime = targetValue[0]
					}
				})
				setTimeout(() => {
					if (isSoc === 0) {
						this.socDcirFilterData[index].stepId = temList[0].replace(/[\r]$/, "").split("\t")[0]
					} else {
						this.socDcirFilterData[index].stepTime = temList[0].replace(/[\r]$/, "").split("\t")[0]
					}
				}, 10)
			},
			handleSchemeNameInput(value, record, index) {
				this.cycleReportSchemeData[index].schemeName = value.target._value
				this.cycleReportSchemeData[index + 1].schemeName = value.target._value
				this.cycleReportSchemeData[index + 2].schemeName = value.target._value
			},

			/* 排序事件 */
			handleSort() {
				// 排序
				this.socDcirFilterData.sort((item1, item2) => {
					if (item1.socNum == item2.socNum) {
						return item1.orderNumber - item2.orderNumber
					} else {
						if (this.socDcirSort == 1) return item1.socNum - item2.socNum
						if (this.socDcirSort == 2) return item2.socNum - item1.socNum
					}
				})
			},
      handleSocDcrSort() {

				// 排序
				this.orderData.sort((item1, item2) => {

          if (this.socDcirSort == 1) return item1.socDcrTemp - item2.socDcrTemp
          if (this.socDcirSort == 2) return item2.socDcrTemp - item1.socDcrTemp

				})

			},
			handleChargingSort() {
				// 排序
				this.socOcvFilterDataCharge.sort((item1, item2) => {
					if (this.socOcvChargingSort == 1) return item1.socNum - item2.socNum
					if (this.socOcvChargingSort == 2) return item2.socNum - item1.socNum
				})
			},
			handleDischargingSort() {
				// 排序
				this.socOcvFilterDataDischarge.sort((item1, item2) => {
					if (this.socOcvDischargingSort == 1) return item1.socNum - item2.socNum
					if (this.socOcvDischargingSort == 2) return item2.socNum - item1.socNum
				})
			},

			/*删除事件*/
			deleteCRate(record) {
				this.cRateData = this.cRateData.filter(r => r.stepId != record.stepId)
			},
			deleteRateStress(record) {
				this.rateStressData = this.rateStressData.filter(r => r.stepId != record.stepId)
			},
			deleteCycle(record) {
				this.socDcirCycleData = this.socDcirCycleData.filter(r => r.cycleId != record.cycleId)
			},
			deleteSoc(record) {
				this.socDcrData = this.socDcrData.filter(r => r.soc != record.soc)
			},
			deleteSocDcir(record) {
				this.socDcirFilterData = this.socDcirFilterData.filter(r => r.socNum != record.socNum)
			},
			deleteCycleReportCycle(record) {
				this.cycleReportCycleData = this.cycleReportCycleData.filter(r => r.cycleId != record.cycleId)
			},
			deleteSocOcvFilterDataDischarge(text, record, index, columns) {
				this.socOcvFilterDataDischarge.splice(index, 1);
			},
			deleteCycleReportCycleSchemeName(record) {
				this.cycleReportSchemeData = this.cycleReportSchemeData.filter(r => r.schemeName != record.schemeName)
			},
			deleteSocOcvFilterDataCharge(text, record, index, columns) {
				this.socOcvFilterDataCharge.splice(index, 1);
			},
			deleteDataOne(record, index) {
				this.selectedRows.splice(index, 1)
				this.selectedRowKeys.splice(index, 1)
				this.orderData.splice(index, 1)
				this.resDcirTempData.splice(index, 1)
				this.socOcvTypeData.splice(index, 1)
			},
      deleteSelect(){
        this.orderData = this.orderData.filter(item => !this.deleteSelectedRowKeys.includes(item.uuid));
        this.selectedRows = this.selectedRows.filter(item => !this.deleteSelectedRowKeys.includes(item.uuid));
        this.selectedRowKeys = this.selectedRowKeys.filter(item => !this.deleteSelectedRowKeys.includes(item));
        this.deleteSelectedRowKeys = []
        this.deleteSelectedRow = []
      },

      moveDown(arr, index) {
        if (arr.length > 1 && index < arr.length - 1) { // 确保数组至少有两个元素，且索引有效
          arr[index] = arr.splice(index + 1, 1, arr[index])[0]; // 移除元素后立即插入到后一个位置
        }
      },

      moveUp(arr, index) {
        if (arr.length > 1 && index > 0) { // 确保数组至少有两个元素，且索引有效
          arr[index] = arr.splice(index - 1, 1, arr[index])[0]; // 移除元素后立即插入到前一个位置
        }
      },


			/* 弹窗事件 */
			showData(record) {
				showData({ celltestcode: record.celltestcode }).then(res => {
					this.$refs.table.refresh()
				})
			},
			hideData(record) {
				hideData({ id: record.id }).then(res => {
					this.$refs.table.refresh()
				})
			},

			filterOption(input, option) {
				return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
			},

			handleSubmit() {
				const {
					form: { validateFields }
				} = this

				this.confirmLoading = true
				validateFields((errors, values) => {
					if (!errors) {
						shenghongDataFilterExport(Object.assign(values, this.saveParam)).then(res => {
							if (res.success) {
								this.$message.success("导出任务创建成功")
								this.$router.push("/testDataHistory")
							} else {
								this.$message.warn(res.message)
							}
						})
					}
					this.confirmLoading = false
				})
			},

			exportData() {
				let param = {}
				param.reportType = this.reportType
				param.orderData = this.orderData
				param.reportBasic = this.reportBasic


				if (param.orderData.length == 0) {
					this.$message.warn("请选择测试数据")
					return
				}

				if (param.reportType == null) {
					this.$message.warn("请选择报告类型")
					return
				}

				if (param.reportBasic.reportName == null || param.reportBasic.reportName == "") {
					this.$message.warn("请输入报告名称")
					return
				}
				if (param.reportBasic.testMethod == null || param.reportBasic.testMethod == "") {
					this.$message.warn("请输入测试方法")
					return
				}
				if (param.reportBasic.num == null || param.reportBasic.num == "") {
					this.$message.warn("请输入测试数量")
					return
				}
				if (param.reportType != 'SOC-DCR' && (param.reportBasic.temp == null || param.reportBasic.temp == "")) {
					this.$message.warn("请输入测试温度")
					return
				}
				if (param.reportBasic.issueId == null || param.reportBasic.issueId == "") {
					this.$message.warn("请选择产品")
					return
				}
        if (param.reportType == "SOC-DCR") {
          param.socDcrData = this.socDcrData

          let haveEmpty = this.socDcrData.find(
            s => s.cycleId === null || s.cycleId == "" || s.soc === null || s.soc === ""
          )

          if (haveEmpty != null) {
            this.$message.warn("SOC-循环号请填写完整")
            return
          }

          if (param.orderData[0].socDcrStepId1 == null || param.orderData[0].socDcrStepId2 == null || param.orderData[0].socDcrStepId1 == '' || param.orderData[0].socDcrStepId2 == '') {
            this.$message.warn("第一条数据工步号1和工步号2不能为空")
            return
          }
          let haveEmpty1 = this.orderData.find(
            s => s.tem == null || s.tem == ""
          )

          if (haveEmpty1 != null) {
            this.$message.warn("温度请填写完整")
            return
          }


        }

				if (param.reportType == "循环DCR") {
					param.socDcirQueryParam = {}

					param.socDcirQueryParam.cycleList = this.socDcirCycleData

					param.socDcirQueryParam.filterList = this.socDcirFilterData

					let haveEmpty = this.socDcirCycleData.find(
						s => s.cycleId === null || s.cycleId === "" || s.cycle === null || s.cycle === ""
					)

					if (haveEmpty != null) {
						this.$message.warn("循环号-循环周数请填写完整")
						return
					}
					let have = this.socDcirFilterData.filter(
						s => s.stepId === null || s.stepId === "" || s.socNum === null || s.socNum === ""
					)
					if (have.length) {
						this.$message.warn("请填写数据获取逻辑")
						return
					}

				} else if (param.reportType == "倍率-倍率充放电&恒功率放电") {
					if (param.reportBasic.cRateUnit == null || param.reportBasic.cRateUnit == "") {
						this.$message.warn("请选择单位")
						return
					}
					param.cRateQueryParam = {}
					param.cRateQueryParam.stepIdAndCurrentList = this.cRateData
					param.cRateQueryParam.cRateUnit = this.reportBasic.cRateUnit
					let haveEmpty = this.cRateData.find(
						s => s.stepId === null || s.stepId === "" || s.rateCurrent === null || s.rateCurrent === ""
					)
					if (haveEmpty != null) {
						this.$message.warn("工步号-充/放电电流请填写完整")
						return
					}
				} else if (param.reportType == "倍率-stress") {
					param.rateStressQueryParam = {}
					param.rateStressQueryParam.stepIdAndCurrentList = this.rateStressData
					let haveEmpty = this.rateStressData.find(
						s => s.stepId === null || s.stepId === "" || s.rateCurrent === null || s.rateCurrent === ""
					)
					if (haveEmpty != null) {
						this.$message.warn("工步号-放电电流请填写完整")
						return
					}
				} else if (param.reportType == "倍率-脉冲放电") {
					if (param.reportBasic.current === null || param.reportBasic.current === "") {
						this.$message.warn("请输入电流")
						return
					}
					param.pulseDischargeQueryMParam = {}
					param.pulseDischargeQueryMParam.stepIdAndPulseDischargeType = this.pulseDischargeMData
					param.pulseDischargeQueryMParam.current = this.reportBasic.current
					let haveEmpty = this.pulseDischargeMData.find(
						s => s.stepId === null || s.stepId === "")
					if (haveEmpty != null) {
						this.$message.warn("工步号请填写完整")
						return
					}
				}
				else if (param.reportType == "内阻-DCR") {
					//提交校验 温度 工步号 工步时间
					param.resDcirQueryParam = {}

					param.resDcirQueryParam.resDcirTempList = this.resDcirTempData

					param.resDcirQueryParam.resDcirFilterList = this.resDcirFilterData

					let tempHaveEmpty = this.resDcirTempData.find(
						s => s.temp === null || s.temp === ""
					);

					if (tempHaveEmpty != null) {
						this.$message.warn("温度请填写完整")
						return
					}

					let stepHaveEmpty = this.resDcirFilterData.find(
						s => s.stepId === null || s.stepId === "" || ((s.stepTime === null || s.stepTime === "") && s.additionalCondition === null)
					);
					if (stepHaveEmpty != null) {
						this.$message.warn("工步号-工步时间请填写完整")
						return
					}


				} else if (param.reportType == 'SOC-OCV map') {
					//提交校验 数据类型 图表标题 SOC 循环号 工步号
					param.socOcvQueryParam = {}

					param.socOcvQueryParam.socOcvTypeDataList = this.socOcvTypeData

					param.socOcvQueryParam.chargeTitle = this.socOcvChartParam.chargeTitle

					param.socOcvQueryParam.dischargeTitle = this.socOcvChartParam.dischargeTitle

					param.socOcvQueryParam.socOcvFilterDataChargeList = this.socOcvFilterDataCharge

					param.socOcvQueryParam.socOcvFilterDataDischargeList = this.socOcvFilterDataDischarge

					let typeDataHaveEmpty = this.socOcvTypeData.find(
						s => s.dataType === null || s.dataType === ""
					);
					if (typeDataHaveEmpty != null) {
						this.$message.warn("请选择测试数据所属类型");
						return;
					}

					let chartParamHaveEmpty = this.socOcvChartParam.chargeTitle == null ||
						this.socOcvChartParam.chargeTitle == '' ||
						this.socOcvChartParam.dischargeTitle == null ||
						this.socOcvChartParam.dischargeTitle == '';
					if (chartParamHaveEmpty == true) {
						this.$message.warn("请填写图表标题");
						return;
					}

					let chargeDataHaveEmpty = this.socOcvFilterDataCharge.find(
						s => s.stepId === null || s.stepId === "" || s.cycleId === null || s.cycleId === "" || s.socNum === null || s.socNum === ""
					);
					if (chargeDataHaveEmpty != null) {
						this.$message.warn("请填写充电SOC-OCV数据获取逻辑");
						return;
					}

					let dischargeDataHaveEmpty = this.socOcvFilterDataDischarge.find(
						s => s.stepId === null || s.stepId === "" || s.cycleId === null || s.cycleId === "" || s.socNum === null || s.socNum === ""
					);
					if (dischargeDataHaveEmpty != null) {
						this.$message.warn("请填写放电SOC-OCV数据获取逻辑");
						return;
					}

				} else if (param.reportType == 'SOC-OCV map charge') {
					//提交校验 数据类型 图表标题 SOC 循环号 工步号
					param.socOcvQueryParam = {}

					this.socOcvTypeData.forEach(v => v.dataType = 'chargeData')
					param.socOcvQueryParam.socOcvTypeDataList = this.socOcvTypeData

					param.socOcvQueryParam.chargeTitle = this.socOcvChartParam.chargeTitle

					param.socOcvQueryParam.socOcvFilterDataChargeList = this.socOcvFilterDataCharge

					let chartParamHaveEmpty = this.socOcvChartParam.chargeTitle === null || this.socOcvChartParam.chargeTitle === ''
					if (chartParamHaveEmpty == true) {
						this.$message.warn("请填写图表标题");
						return;
					}

					let chargeDataHaveEmpty = this.socOcvFilterDataCharge.find(
						s => s.stepId == null || s.stepId === "" || s.cycleId === null || s.cycleId === "" || s.socNum === null || s.socNum === ""
					);
					if (chargeDataHaveEmpty != null) {
						this.$message.warn("请填写充电SOC-OCV数据获取逻辑");
						return;
					}

				} else if (param.reportType == 'SOC-OCV map discharge') {
					//提交校验 数据类型 图表标题 SOC 循环号 工步号
					param.socOcvQueryParam = {}

					this.socOcvTypeData.forEach(v => v.dataType = 'dischargeData')
					param.socOcvQueryParam.socOcvTypeDataList = this.socOcvTypeData

					param.socOcvQueryParam.dischargeTitle = this.socOcvChartParam.dischargeTitle

					param.socOcvQueryParam.socOcvFilterDataDischargeList = this.socOcvFilterDataDischarge


					let chartParamHaveEmpty = this.socOcvChartParam.dischargeTitle === null || this.socOcvChartParam.dischargeTitle === ''

					if (chartParamHaveEmpty == true) {
						this.$message.warn("请填写图表标题");
						return;
					}

					let dischargeDataHaveEmpty = this.socOcvFilterDataDischarge.find(
						s => s.stepId === null || s.stepId === "" || s.cycleId === null || s.cycleId === "" || s.socNum === null || s.socNum === ""
					);
					if (dischargeDataHaveEmpty != null) {
						this.$message.warn("请填写放电SOC-OCV数据获取逻辑");
						return;
					}
				} else if (param.reportType == '循环') {
					if (param.reportBasic.charge == null || param.reportBasic.charge == ""
						|| param.reportBasic.chargeUnit == null || param.reportBasic.chargeUnit == ""
						|| param.reportBasic.discharge == null || param.reportBasic.discharge == ""
						|| param.reportBasic.dischargeUnit == null || param.reportBasic.dischargeUnit == ""
					) {
						this.$message.warn("请输入充放电参数")
						return
					}
					//提交校验 数据类型 图表标题 SOC 循环号 工步号
					param.cycleReportParam = {}

          param.tableType = this.tableType
					let haveEmpty = this.cycleReportStepData.find(
						s => s.stepId === null || s.stepId === ""
					)
					if (haveEmpty != null) {
						this.$message.warn("请填写工步号")
						return
					} else {
						param.cycleReportParam.cycleReportStepData = this.cycleReportStepData[0].stepId;
					}
				}

				this.saveParam = param

				testReportSave(this.saveParam).then(res => {
					if (res.success) {
						this.$message.success("创建成功")
						this.$router.push("/v_report_online_manager?type=" + this.reportType)
					} else {
						this.$message.warn(res.message)
					}
				})
			},



			openStepData(record, flag) {
				this.outQueryFlowRecord = record
				this.outFlowRecord = record

				//历史数据处理
				if (null == record.flowInfoList && !flag) {
					this.$refs.stepData.query(record, false)
          return;
				}

				if (record.flowId != null) {
					this.outQueryFlowRecord.flowId = record.flowId
					this.$refs.stepData.query(this.outQueryFlowRecord, false)
				} else {
					this.$message.warn("测试数据为空")
					return
				}
			},



			handleCancel() {
				this.visible = false
			},
			handleCancelFlow() {
				this.visibleFlow = false
			},
			handleCancel1() {
				this.visible1 = false
			},
			openTestOrder() {
				this.visible = true
			},
			handleOk() {
				this.getList()
			},
			getList(flag) {
				this.$refs.table2.refresh()
			},
			openData(folderId) {
				this.$refs.testData.query(folderId)
			},


			// 下一页
			nextPage(current) {
				this.current++
				this.stepStatus[current] = "finish"
				if (this.stepStatus[current + 1] !== "finish") this.stepStatus[current + 1] = "process"
			},
			handleCheckFile(index) {
				this.isShowExample = true
				this.iframeUrl = index
					? "http://10.1.3.78:89/sysFileInfo/previewPdf?id=1696770190973132802"
					: "http://10.1.3.78:89/sysFileInfo/previewPdf?id=1696770532343341058"
			},
			handleCloseExample() {
				this.isShowExample = false
			},
			handleClose(index) {
				switch (index) {
					case 1:
						this.isCloseOne = !this.isCloseOne
						break
					case 2:
						this.isCloseTwo = !this.isCloseTwo
						break
					case 3:
						this.isCloseThree = !this.isCloseThree
						break
					case 4:
						this.isCloseFour = !this.isCloseFour
						break
					case 5:
						this.isCloseFive = !this.isCloseFive
						break
					case 6:
						this.isCloseSix = !this.isCloseSix
						break
					case 7:
						this.isCloseSeven = !this.isCloseSeven
						break
					case 8:
						this.isCloseEight = !this.isCloseEight
						break
					case 9:
						this.isCloseNine = !this.isCloseNine
						break
					case 10:
						this.isCloseTen = !this.isCloseTen
						break
					case 11:
						this.isCloseEleven = !this.isCloseEleven
						break
					case 12:
						this.isCloseTwelve = !this.isCloseTwelve
						break
					case 13:
						this.isCloseThirteen = !this.isCloseThirteen
						break
				}
			},







			handleTableSelect(value, record, index) {
				if (value == "data" && record.orderNumber == 1) {
					this.socDcirFilterData[index].stepTime = "0:00:08.000"
				} else if (value == "data" && record.orderNumber == 2) {
					this.socDcirFilterData[index].stepTime = "0:00:03.000"
				} else if (value == "step") {
					this.socDcirFilterData[index].stepTime = ""
				}
			},
			handleAllTableSelect(value) {
				this.socDcirFilterData.forEach(v => {
					v.table = value
					if (value == "data" && v.orderNumber == 1) {
						v.stepTime = "0:00:08.000"
					} else if (value == "data" && v.orderNumber == 2) {
						v.stepTime = "0:00:03.000"
					} else if (value == "step") {
						v.stepTime = ""
					}
				})
				this.allTable = "数据表选择"
			},


			/* 未使用事件 */
			addCycNumToSocDcirOptions() {
				let have = this.socDcirOptions.find(c => c.value == this.addCycNum)
				if (have != null) {
					this.$message.warn("请勿重复添加")
					return
				}
				this.socDcirOptions.push({ label: this.addCycNum, value: this.addCycNum })

				this.socDcirCyclesChecked.push(this.addCycNum)
			},
			// 3数据选择--添加按钮事件
			addFilterData() {
				this.filterData.push({
					key1: this.lastDataSelect["key1"]
						? this.lastDataSelect["key1"]
						: this.dataType == "stepInfo"
							? "StepID"
							: "StepId",
					value1: null,
					key2: this.lastDataSelect["key2"] ? this.lastDataSelect["key2"] : null,
					value2: null,
					key3: this.lastDataSelect["key3"] ? this.lastDataSelect["key3"] : null,
					value3: null,
					key4: null,
					value4: null
				})
			}, // 3数据选择--添加按钮事件
			addCycleReportCycleData() {
				this.cycleReportCycleData.push({
					cycle: "",
					cycleId: ""
				})
			},
			addCycleReportScheme() {
				this.cycleReportSchemeData.push(
					{ schemeName: "", table: "cycle", flowId: "", orderNumber: 1, stepId: null },
					{ schemeName: "", table: "cycle", flowId: "", orderNumber: 2, stepId: null },
					{ schemeName: "", table: "cycle", flowId: "", orderNumber: 3, stepId: null }
				);
			},
			deleteData() {
				const orderIdArray = this.orderData.map(item => item.uuid)
				this.projectSelectedRow.forEach(item => {
					let index = orderIdArray.indexOf(item.uuid)
					orderIdArray.splice(index, 1)
					this.orderData.splice(index, 1)
					this.selectedRows.splice(index, 1)
					this.selectedRowKeys.splice(index, 1)
				})
				this.projectSelectedRow = []
				this.projectSelectedRowKeys = []
			},
			copyFromExcel(event, column, index) {
				let arr = event.clipboardData.getData("text").split("\n")

				if (arr.length > 1) {
					for (let i = 1; i < arr.length; i++) {
						if (null != arr[i] && "" != arr[i] && arr[i].length != 0) {
							if (this.filterData.length > index + i) {
								this.filterData[index + i][column] = arr[i]
							} else {
								this.filterData.push({
									key1: this.filterData[index].key1,
									value1: null,
									key2: this.filterData[index].key2,
									value2: null,
									key3: this.filterData[index].key3,
									value3: null,
									key4: this.filterData[index].key4,
									value4: null
								})
								this.filterData[index + i][column] = arr[i]
							}
						}
					}
				}

				setTimeout(() => {
					this.filterData[index][column] = arr[0]
				}, 10)
			},
			// 循环序号
			cycleReportCycleIdInput(value, index, isCycle = 0) {
				// isCycle 0：循环号 1：循环序号

				// excel 多行复制
				const temList = String(value.target._value).split(" ").map(v => v.split('\t'))
				temList.forEach((v, vIndex) => {
					if (!this.cycleReportCycleData[index + vIndex]) {
						this.cycleReportCycleData.push({
							cycle: "",
							cycleId: ""
						})
					}

					// 从循环周数开始粘贴
					if (isCycle) {
						this.cycleReportCycleData[index + vIndex].cycle = v[0]
						this.cycleReportCycleData[index + vIndex].cycleId = v.length > 1 ? v[1] : this.cycleReportCycleData[index + vIndex].cycleId
					} else {
						this.cycleReportCycleData[index + vIndex].cycleId = v[0]
					}
				})

				// 检查是否有一样的cycleId
				const unique = []
				const duplicateList = []
				for (let i = 0; i < this.cycleReportCycleData.length; i++) {
					const duplicate = unique.find(obj => obj.cycleId == this.cycleReportCycleData[i].cycleId && this.cycleReportCycleData[i].cycleId !== null && this.cycleReportCycleData[i].cycleId !== '')
					if (!duplicate) unique.push(this.cycleReportCycleData[i])
					if (duplicate) duplicateList.push({ index: i, ...this.cycleReportCycleData[i] })
				}

				// 如果有相同的cycleId,提醒+清空
				if (duplicateList.length > 0) {
					this.$message.warn("循环号" + duplicateList.map(item => item.cycleId).join(",") + "已存在，请勿重复添加")
					duplicateList.forEach(v => {
						this.cycleReportCycleData[v.index].cycleId = null
					})
				}

				// 填写完成后才排序
				let haveEmpty = this.cycleReportCycleData.find(
					s => s.cycleId == null || s.cycleId == "" || s.cycle == null || s.cycle == ""
				)

				if (haveEmpty) {
					return
				}

				// 排序(正向)
				this.cycleReportCycleData.sort((item1, item2) => {
					return Number(item1.cycleId) - Number(item2.cycleId)
				})
			},
			onChange() {
				let num =
					this.dataType == "step"
						? this.stepChecked.length
						: this.dataType == "data"
							? this.dataChecked.length
							: this.dataType == "cyc"
								? this.cycChecked.length
								: this.stepInfoChecked.length
				let allNum =
					this.dataType == "step"
						? this.stepOptions.length
						: this.dataType == "data"
							? this.dataOptions.length
							: this.dataType == "cyc"
								? this.cycOptions.length
								: this.stepInfoOptions.length

				this.indeterminate = num == 0 ? null : 0 < num < allNum
				this.checkAll = num > 0 && num == allNum
				this.$nextTick(() => {
					if (this.checkAll) {
						this.indeterminate = false
					}
				})
			},
			// 3数据选择select改变事件
			handleDataSelectChange(value, option, index, record) {
				let flag = true
				if (index.indexOf("1") > -1) {
					if (value == record.key2) {
						flag = false
						this.$message.warn("请选择不同参数")
						record.key2 = null
					} else if (value == record.key3) {
						flag = false
						this.$message.warn("请选择不同参数")
						record.key3 = null
					}
				}

				if (index.indexOf("2") > -1) {
					if (record.key1 == value) {
						flag = false
						this.$message.warn("请选择不同参数")
						record.key2 = null
					} else if (value == record.key3) {
						flag = false
						this.$message.warn("请选择不同参数")
						record.key3 = null
					}
				}
				if (index.indexOf("3") > -1) {
					if (record.key1 == value) {
						flag = false
						this.$message.warn("请选择不同参数")
						record.key3 = null
					} else if (record.key2 == value) {
						flag = false
						this.$message.warn("请选择不同参数")
						record.key3 = null
					}
				}

				if (flag) {
					this.lastDataSelect[index] = value
				}
			},
			onCheckAllChange(e) {
				Object.assign(this, {
					socDcirCyclesChecked: e.target.checked ? this.filterValue(this.socDcirOptions) : [],
					indeterminate: null,
					checkAll: e.target.checked
				})
			},
			filterValue(list) {
				let newList = []
				for (let i = 0; i < list.length; i++) {
					newList.push(list[i].value)
				}
				return newList
			},
			projectOnChange(selectedRowKeys, selectedRows) {
				this.projectSelectedRow = selectedRows
				this.projectSelectedRowKeys = selectedRowKeys
			},
			handleStepChange(current) {
				this.stepStatus.forEach((v, index) => {
					if (v === "finish") return
					this.stepStatus[index] = current === index ? "process" : "wait"
				})
			},
			orderDataSync() {
				this.resDcirTempData = this.orderData;
			},
      socDcrStepId1Change(value,index){

        if(index == 0){
          this.orderData.forEach(o => o.socDcrStepId1 = value)
        }

      },
      socDcrStepId2Change(value,index){

        if(index == 0){
          this.orderData.forEach(o => o.socDcrStepId2 = value)
        }

      }
		}
	}
</script>
<style lang="less" scoped>
	/* :root {
	--height: 600px;
} */

	.wrapper {
		
		padding: 0 10px 10px;
		background-color: #f0f2f5;
	}

	.head_title {
		color: #333;
		padding: 10px 0;
		font-size: 20px;
		font-weight: 600;
	}

	.head_title::before {
		width: 8px;
		background: #1890ff;
		margin-right: 8px;
		content: "\00a0"; //填充空格
	}

	/deep/.right-content input:not([type="range"]) {
		text-align: center;
	}

	/deep/ input:not([type="range"]) {
		font-size: 12px;
	}

	/deep/ textarea.ant-input {
		font-size: 12px;
	}

	/deep/ .ant-table-thead>tr>th {
		padding: 2px 0 !important;
		font-size: 12px !important;
	}

	/deep/ .ant-table-tbody>tr>td {
		padding: 0px !important;
		height: 32px !important;
		font-size: 12px !important;
	}

	/deep/ .ant-calendar-picker-icon {
		display: none;
	}

	/deep/ .ant-calendar-picker-input.ant-input {
		color: black;
		font-size: 12px;
		border: 0;
		text-align: center;
		padding: 0;
	}

	.red {
		background-color: #ed0000;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.yellow {
		background-color: #ffc000;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.grey {
		background-color: rgba(223, 223, 223, 0.25);
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.ant-modal-body {
		padding: 0;
	}

	/deep/ .ant-btn>i,
	/deep/ .ant-btn>span {
		display: flex;
		justify-content: center;
	}

	/deep/ .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
		color: #fff;
		background: #1890ff;
	}

	.green {
		background-color: #58a55c;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	/deep/ #table1>div>div>div>div>div>div>table>thead {
		height: 64px;
	}

	/deep/ #table1>.ant-table-wrapper>div>div>ul {
		display: none;
	}

	/deep/ .ant-table-pagination.ant-pagination {
		float: right;
		margin: 0;
	}

	.float {
		// width: 36%;
		// float: left;
		// margin-right: 10px;
		// text-align: center;
		padding-bottom: 10px;
	}

	.float1 {
		width: 12%;
		float: left;
		margin-right: 10px;
		text-align: center;
	}

	/deep/ .ant-checkbox-group-item {
		display: block;
		width: 100%;
		text-align: left;
	}

	.title {
		font-size: large;
		margin-bottom: 20px;
	}

	.numTitle {
		font-size: xx-large;
	}

	/deep/ .ant-table-footer {
		padding: 0;
	}

	/deep/ .ant-table-row-expand-icon {
		margin-right: 0px;
	}

	.export-btn {
		position: fixed;
		bottom: 10px;
		right: 10px;
		width: 15%;
	}

	.all-wrapper {
		padding: 0 0 10px;
		display: flex;
		justify-content: space-between;
	}

	.btn-wrap {
		text-align: right;
	}

	.example-icon {
		width: 20px;
		height: 20px;
		color: #1890ff;
		vertical-align: middle;
		margin-left: 3px;
		margin-top: 3px;
	}

	// 通用
	.mt10 {
		margin-top: 10px;
	}

	.ml10 {
		margin-left: 10px;
	}

	.mr5 {
		margin-right: 5px;
	}

	.mb5 {
		margin-bottom: 5px;
	}

	.mr10 {
		margin-right: 10px;
	}

	.fs14 {
		font-size: 14px;
	}

	/* 标题 */
	.flex-sb-center-row {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.title-line {
		width: 8px;
		height: 30px;
		background: #1890ff;
		border-radius: 2px;
		margin-right: 8px;
		content: "\00a0"; //填充空格

		position: absolute;
		top: 8px;
		left: -4px;
	}

	.flex-column {
		display: flex;
	}

	.strong {
		// background-image: url("../../../../assets/icons/Rectangle.png");
		background-size: 100% 100%;
		width: 210px;
		height: 25px;
		color: #333;
		display: flex;
		align-items: center;
		padding: 5px;
	}

	.block {
		height: fit-content;
		padding: 10px;
		background: #fff;
		border-radius: 10px;
		box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
		position: relative;
	}

	.left-content {
		width: 55%;
		margin-right: 10px;
	}

	.right-content {
		width: 45%;
	}

	.right-content .all-checkbox {
		padding-bottom: 5px;
		margin-bottom: 5px;
		border-bottom: 1px solid #e9e9e9;
	}

	.normal-btn {
		padding: 5px 10px;
		color: #fff;
		background-color: #1890ff;
		letter-spacing: 2px;
		cursor: pointer;
	}

	.footer-btn {
		width: 100%;
		height: 32px;
		border: 1px solid #e8e8e8;
		background: #fff;
		color: #999;
		font-size: 16px;
		display: flex;
		justify-content: center;
		align-items: center;
		cursor: pointer;
	}

	.footer-btn:hover {
		color: #1890ff;
	}

	// 组件
	/deep/ .ant-steps {
		padding: 15px 50px;
	}

	/* 左边表格 */
	/deep/ .left-content .ant-table-body {
		min-height: calc(100vh - 570px);
		max-height: 300px;
		border: 1px solid #e8e8e8;
		overflow: auto;
	}

	/deep/ .right-content .ant-table-body {
		/* min-height: var(--height) !important; */
		min-height: calc((100vh - 330px) / 2);
		border: 1px solid #e8e8e8;
		overflow: auto;
	}

	/deep/ .all-wrapper .ant-table-thead {
		position: sticky;
		top: 0;
		z-index: 2;
	}

	/deep/ .all-wrapper .ant-table-placeholder {
		border: none !important;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		padding: 0;
		z-index: 0;
		background: transparent;
	}

	/deep/ .right-content .ant-empty-normal {
		margin: -2px 0;
	}

	/deep/ .ant-empty-image {
		display: none;
	}

	/deep/ .right-content .ant-input {
		border: none;
	}

	/deep/ .ant-checkbox-group {
		width: 100%;
		display: flex;
		flex-wrap: wrap;
	}

	/deep/ .ant-checkbox-group-item {
		font-size: 12px;
		width: 23%;
	}

	/deep/ .ant-radio-inner {
		top: 1px;
		left: 1px;
	}

	/* /deep/ .ant-table-body::-webkit-scrollbar {
		height: 10px;
		width: 5px;
	}

	/deep/ .ant-table-body::-webkit-scrollbar-thumb {
		-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
		border-radius: 0;

		background: #dddbdb;
	}

	/deep/ .ant-table-body::-webkit-scrollbar-track {
		-webkit-box-shadow: 0;
		border-radius: 0;
		background: #f1f1f1;
	} */

	/deep/ .ant-select {
		font-size: 12px;
	}

	/deep/ .ant-select-selection__rendered {
		margin-right: 0px;
	}

	/deep/ .ant-form-item {
		margin-bottom: 0;
		font-size: 12px;
	}

	/deep/ .ant-popover-buttons {
		display: flex !important;
		flex-direction: column !important;
		margin-bottom: 15px;
	}

	// /deep/.ant-popover-buttons .ant-btn-sm{
	// 	margin-bottom: 5px;
	// 	background-color: #1890ff;
	// 	color: #fff;
	// }

	.tips {
		color: #1890ff;
	}

	.button-tips {
		display: flex;
		flex-direction: column;
	}

	/* 霓虹灯按钮 */

	.btn {
		width: 100%;
		height: 32px;
		border: 1px solid;
		background-color: transparent;
		text-transform: uppercase;
		font-size: 12px;
	}

	.btn:hover {
		color: white;
		border: 0;
	}

	.neon {
		color: #1890ff;
	}

	.neon:hover {
		background-color: #1890ff;
		box-shadow: 10px 10px 99px 6px rgba(24, 144, 255, 1);
	}

	.soc-input {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.soc-input .text {
		margin: 0 2px;
	}

	.tableTitle {
		width: calc(100% + 10px);
		margin-left: -5px;
		font-size: 13px;
		color: #000000d9;
	}

	/deep/ .soc-input .ant-input-number-handler-wrap {
		display: none;
	}

	/deep/ .ant-table-thead>tr>th .ant-table-header-column {
		width: 100%;
	}

	/deep/ .tableTitle .ant-select-selection {
		border: none;
	}

  /* /deep/.left-content .ant-table-header colgroup col:last-child {

    min-width: 47.2px !important;
  } */
</style>