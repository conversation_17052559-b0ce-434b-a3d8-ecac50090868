<template>
<div>
    <div class="crumb">
        <a-breadcrumb class="breadcrumb" separator=">">
            <a-breadcrumb-item><a @click="gotoIndex(-2)">信息对齐表</a></a-breadcrumb-item>
            <a-breadcrumb-item><a @click="gotoIndex(-1)">产品开发进展</a></a-breadcrumb-item>
            <a-breadcrumb-item>项目看板</a-breadcrumb-item>
        </a-breadcrumb>
    </div>
    <div class="btns">
        <div @click="gotoProjectFramework">项目架构</div>
        <div>项目甘特图</div>
        <div @click="gotoProjectMeeting">客户议题管理</div>
        <div>项目周进展</div>
    </div>
    <div class="dashboard" ref="dashboard" :style="`height:${windowHeight}px;`">
    
        <div class="col1">

        <div class="item">
            <div class="head_div"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 iKfgJk svg-icon-path-icon fill" viewBox="0 0 32 32" width="18" height="18"><defs data-reactroot=""></defs><g><path d="M4 5.333h24v2.667h-24v-2.667zM4 14.667h24v2.667h-24v-2.667zM4 24h24v2.667h-24v-2.667z"></path></g></svg><span>任务状态</span></div>
            <div class='chart_table' ref="task_status"></div>
        </div>

        <div class="item">
            <div class="head_div"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 iKfgJk svg-icon-path-icon fill" viewBox="0 0 32 32" width="18" height="18"><defs data-reactroot=""></defs><g><path d="M4 5.333h24v2.667h-24v-2.667zM4 14.667h24v2.667h-24v-2.667zM4 24h24v2.667h-24v-2.667z"></path></g></svg><span>产品质量管理</span></div>
            <div class='chart_table' ref="quality"></div> 
        </div>
        </div>
        <div class="col2">


        <div class="item">
            <div class="head_div"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 iKfgJk svg-icon-path-icon fill" viewBox="0 0 32 32" width="18" height="18"><defs data-reactroot=""></defs><g><path d="M4 5.333h24v2.667h-24v-2.667zM4 14.667h24v2.667h-24v-2.667zM4 24h24v2.667h-24v-2.667z"></path></g></svg><span>任务分类</span></div>
            <div class='chart_table' ref="task"></div>
        </div>


        <div class="item" >
            <div class="head_div"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 iKfgJk svg-icon-path-icon fill" viewBox="0 0 32 32" width="18" height="18"><defs data-reactroot=""></defs><g><path d="M4 5.333h24v2.667h-24v-2.667zM4 14.667h24v2.667h-24v-2.667zM4 24h24v2.667h-24v-2.667z"></path></g></svg><span>费用管理</span></div>
            <div class='chart_table' ref="charge"></div>
        </div>


        </div>

        <div class="col3">
        <div class="item">
            <div class="head_div"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 iKfgJk svg-icon-path-icon fill" viewBox="0 0 32 32" width="18" height="18"><defs data-reactroot=""></defs><g><path d="M4 5.333h24v2.667h-24v-2.667zM4 14.667h24v2.667h-24v-2.667zM4 24h24v2.667h-24v-2.667z"></path></g></svg><span>任务延期情况</span></div>
            <div class='chart_table delay'>
                <div>0</div>
                <div>延期任务</div>
            </div>
        </div>
        <div class="item">
            <div class="head_div"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 iKfgJk svg-icon-path-icon fill" viewBox="0 0 32 32" width="18" height="18"><defs data-reactroot=""></defs><g><path d="M4 5.333h24v2.667h-24v-2.667zM4 14.667h24v2.667h-24v-2.667zM4 24h24v2.667h-24v-2.667z"></path></g></svg><span>产品变更管理</span></div>
            <div class='chart_table' style="overflow:hidden">
            <a-table :pagination="false" style="margin:5px" :data-source="data" :columns="columns" size="small" >
            </a-table>
            </div>
        </div>
        </div>
    </div>
</div>
</template>
<script>
  import {

    addOrUpdateIssue,
    getIssueByIssueId
  } from "@/api/modular/system/projectIssue"
export default {
    data(){
        return {
            windowHeight: document.documentElement.clientHeight - 90,
            projectIssue: {},
            data:[
                {
                    content:'BOM',
                    num:1
                },
                {
                    content:'MI',
                    num:1
                },
                {
                    content:'图纸',
                    num:1
                },
                {
                    content:'其他项目变更',
                    num:1
                }
            ],
            columns:[
                {
                    title: '序号',
                    dataIndex: 'seq',
                    align:'center',
                    width: '15%',
                    customRender: (text, record, index) => (<span>{index+1}</span>)
                },
                {
                    title: '变更内容',
                    dataIndex: 'content',
                    align:'center',
                },
                {
                    title: '数量',
                    dataIndex: 'num',
                    align:'center',
                },
            ]
        }
    },
    methods: {
        gotoIndex(index){
            this.$router.go(index)
        },
        gotoProjectFramework(){
          this.$router.push({
            path:'/project_framework',

            query:this.$route.query,
          })
        },
        gotoProjectMeeting(){
          this.$router.push({
            path:'/project_meeting',

            query:this.$route.query,
          })
        },
        initChart(){
            this.$nextTick(() => {
                this.initTask()
                this.initTaskStatus()
                this.initCharge()
                this.initQuality()
            })
        },
        initTask(){
            let chart = this.echarts.init(this.$refs.task)
            chart.off("click")
            let datas = [{value:3,name:'测试'},{value:9,name:'变更'},{value:4,name:'质量'},{value:20,name:'技术'},{value:34,name:'制程'}]
            let sum = datas.reduce((sum, e) => sum + Number(e.value || 0), 0)
            chart.clear()
            const options = {
            tooltip: {
                trigger: 'item'
            },
            legend: {
                show: false
            },
            color: ['#acd1f7', '#cacaca', '#f09696', '#f7e771','#89d77f'],
            grid: {
                left: '3%',
                right: '4%',
                top: '40%',
                bottom:'4%',
            },
            series: [{
                type: 'pie',
                radius: ['55%', '80%'],
                itemStyle: {
                    borderRadius: 0,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: true,
                    position: 'center', //将百分比显示在饼图内部
                    color:'#5c1b68',
                    formatter: `任务总数\n${sum}`,
                    fontSize: '20',
                    lineHeight: 32,
                    fontWeight:'bold'
                },
                labelLine: {
                    show: false
                },
                data: datas
                },{
                type: 'pie',
                radius: ['55%', '80%'],
                itemStyle: {
                    borderRadius: 0,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: true,
                },
                labelLine: {
                    show: true
                },
                data: datas
                },
                {
                type: 'pie',
                radius: ['55%', '80%'],
                itemStyle: {
                    borderRadius: 0,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: true,
                    position: 'inner', //将百分比显示在饼图内部
                    color: '#fff',
                    formatter: '{c}',
                },
                labelLine: {
                    show: false
                },
                data: datas
                },
                
            ]
            }
            chart.setOption(options)
            chart.resize()
        },
        initTaskStatus(){
            let chart = this.echarts.init(this.$refs.task_status)
            const options = {
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    show: true
                },
                xAxis: {
                    type: 'category',
                    data: ['进行中', '已完成', '未开始']
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        data: [
                            {
                                value:200,
                                itemStyle: {
                                    color: '#f4e471'
                                }
                            },
                            {
                                value: 130,
                                itemStyle: {
                                    color: '#89d77f'
                                }
                            },
                            {
                                value: 120,
                                itemStyle: {
                                    color: '#acd1f7'
                                }
                            },
                        ],
                        type: 'bar',
                        itemStyle: {
                            normal: {
                                label: {
                                show: true, //开启数值显示
                                position: "top", //在上方显示
                                textStyle: {
                                    //数值样式
                                    color: "#ffc000",
                                    fontSize: 14,
                                },
                                },
                            },
                        },
                    }
                ]
            };
            chart.setOption(options)
            chart.resize()
        },
        initCharge(){
            let chart = this.echarts.init(this.$refs.charge)
            const options = {
                color:['#f7e771','#89d77f'],
                legend: {
                    show: true,
                    bottom: '6px',
                    itemWidth: 8,
                    itemHeight: 8
                },
                tooltip: {},
                dataset: {
                    source: [
                        ['product', '预算', '实际'],
                        ['K0', 43.3, 85.8],
                        ['M1', 83.1, 73.4],
                        ['M2', 86.4, 65.2],
                        ['M3', 72.4, 53.9],
                        ['M4', 79.4, 87.9],
                        ['M5', 80, 53.9],
                        ['M6', 73.4, 53.9],
                    ]
                },
                xAxis: { type: 'category' },
                yAxis: {},
                series: [
                    { 
                        type: 'bar',
                        itemStyle: {
                            normal: {
                                color: "#ffc000", //柱状颜色
                                label: {
                                show: true, //开启数值显示
                                position: "top", //在上方显示
                                textStyle: {
                                    //数值样式
                                    color: "#ffc000",
                                    fontSize: 14,
                                },
                                },
                            },
                        },
                    }, 
                    { 
                        type: 'bar',
                        itemStyle: {
                            normal: {
                                color: "#70ad47",
                                label: {
                                show: true,
                                position: "top",
                                textStyle: {
                                    color: "#70ad47",
                                    fontSize: 12,
                                },
                                },
                            },
                        },
                    }
                ]
            };
            chart.setOption(options)
            chart.resize()
        },

        initQuality() {
            let chart = this.echarts.init(this.$refs.quality)
            chart.clear()
            const options = {
            color:['#89d77f','#f7e771'],
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                bottom: '6px',
                itemWidth: 8,
                itemHeight: 8,
                show:true
            },
            grid: {
                left: '3%',
                right: '3%',
                bottom: '15%',
                top:'10%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['一次送样合格率','阶段问题关闭率','阶段风险关闭率','计划评审达成率','文件质量达成率'],
                axisLabel: { //设置x轴的字
                    show:true,
                    interval:0,//使x轴横坐标全部显示
                },
            },
            dataZoom:[
                {
                    show: true,
                    start: 0,
                    end: 50,
                    height: 3,
                    bottom:'15%',
                    textStyle:false
                }
            ],
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '目标',
                    type: 'bar',
                    stack: 'total',
                    barWidth: 40,
                    label: {
                        show: true,
                        color: '#fff'
                    },
                    emphasis: {
                        focus: 'series'
                    },
                    data: [98,78,83,89,87]
                },
                {
                    name: '业绩',
                    type: 'bar',
                    stack: 'total',
                    barWidth: 40,
                    label: {
                        show: true,
                        color: '#fff'
                    },
                    emphasis: {
                        focus: 'series'
                    },
                    data: [3,4,3,1,2]
                },
            ]
            }
            chart.setOption(options)
            chart.resize()
        },
    },
    mounted() {

        getIssueByIssueId(this.$route.query).then((res) => {
          if(res.success){
            if(res.data == null){

              addOrUpdateIssue(this.$route.query).then(res1 => {
                if(res1.success){
                  this.projectIssue = res1.data
                }
              })
            }else{
              this.projectIssue = res.data
            }
          }
        })

        this.initChart()
    }
}
</script>

<style lang="css" scoped=''>
  .dashboard {
    display: flex;
    margin: auto;
    justify-content: center;
    overflow-y: scroll;
    max-width: 1510px;
  }
  .crumb{
    margin: auto;
  }
  .crumb,.btns{
    max-width: 1510px;
  }
  .col1,
  .col3 {
    width: 28%;
    max-width: 380px;
  }
  .col2 {
    width: 44%;
    max-width: 750px;
  }
  .item {
    width: 96%;
    margin: 0px auto;
    margin-bottom: 8px;
    border-radius: 8px;
    overflow: hidden;
    background: #fff;
    box-shadow: 2px 2px 3px #ccc;
  }
  .col1 .item,
  .col3 .item {
    width: 100%;
  }
  .head_div {
    color: #000;
    font-size: 18px;
    border-bottom: 2px solid #e5e5e5;
    padding: 10px 18px;
    display: flex;
    align-items: center;
  }
  .head_div svg{
    margin-right:2px ;
  }
  .chart_table {
    width: 100%;
    height: 280px;
  }
  .chart_table.delay{
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
  }
  .delay div{
    color: #f09696;
  }
  .delay div:first-child{
    font-size: 100px;
  }
  .delay div:last-child{
    font-size: 28px;
    margin-top: -28px;
  }
  .breadcrumb{
    padding: 5px 0;
    padding-left: 13px;
    }.ant-breadcrumb a{
    color:#5d90fa !important;
    }.ant-breadcrumb{
    font-size: 12px !important;
    }
    .btns{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 5px auto;
    }
    .btns div{
        background: rgb(90, 134, 232);;
        color: #fff;
        width: 20%;
        font-size: 15px;
        font-weight: bold;
        text-align: center;
        border-radius: 4px;
        line-height: 48px;
        font-size: 20px;
    }
</style>

