<template>
  <div class="formula-info-form">
    <h5>公式信息</h5>

    <div class="form-container">
      <a-form-item label="公式ID（必填）">
        <a-input v-model="localFormulaId" placeholder="公式ID" @change="emitFormulaIdChange" />
      </a-form-item>

      <a-form-item label="公式说明（选填）">
        <a-textarea
          v-model="localFormulaDesc"
          placeholder="请输入公式说明"
          :autoSize="{ minRows: 2, maxRows: 4 }"
          @change="emitFormulaDescChange"
        />
      </a-form-item>

      <div class="save-button-container">
        <a-button type="primary" @click="saveFormula" block>
          保存自定义公式
        </a-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FormulaInfoForm',
  props: {
    formulaId: {
      type: String,
      default: ''
    },
    formulaDescription: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      localFormulaId: this.formulaId,
      localFormulaDesc: this.formulaDescription
    };
  },
  watch: {
    formulaId(newVal) {
      this.localFormulaId = newVal;
    },
    formulaDescription(newVal) {
      this.localFormulaDesc = newVal;
    }
  },
  methods: {
    emitFormulaIdChange() {
      this.$emit('update:formulaId', this.localFormulaId);
    },

    emitFormulaDescChange() {
      this.$emit('update:formulaDescription', this.localFormulaDesc);
    },

    saveFormula() {
      this.$emit('save-formula');
    }
  }
};
</script>

<style scoped>
.formula-info-form {
  width: 100%;
  padding: 0;
  background-color: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.form-container {
  padding: 16px;
}

h5 {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 0;
  color: #333;
  padding: 10px 0;
  background-color: #f5f5f5;
  border-bottom: 1px solid #f0f0f0;
  text-align: center;
  border-radius: 8px 8px 0 0;
}

.save-button-container {
  text-align: center;
  margin-top: 12px;
}

:deep(.ant-form-item-label) {
  line-height: 20px;
  margin-bottom: 4px;
}

:deep(.ant-form-item-label > label) {
  font-size: 13px;
  color: #333;
}

:deep(.ant-btn-primary) {
  height: 38px;
  font-weight: 500;
  transition: all 0.3s;
  border-radius: 4px;
}

:deep(.ant-btn-primary:hover) {
  opacity: 0.9;
}
</style>
