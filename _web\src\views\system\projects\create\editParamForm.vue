<template>
    <div style="background:#fff;overflow: hidden;">
        <a-tabs style="margin-top:15px" type="card" v-model="activeKey">
            <a-tab-pane key="1" tab="参数信息">
                <a-form size='small' :form="form" layout='horizontal'>
                    <template v-for="(item,i) in formAttr[formIndex]">
                        <div class="divider" :key="i">{{i == 0 ? '一' : i}}、{{item.head}}</div>
                        <a-row :gutter="24" :key="i">
                            <a-col v-for="(_item,_i) in item.attr" :key="_i" :span="_item.span">
                                <a-form-item :label="_item.label" :labelCol="_item.span == 24 ? labelCol_JG : labelCol" :wrapperCol=" _item.span == 24 ? wrapperCol_JG : wrapperCol">
                                    <a-input
                                    v-if="_item.type == 'input'"
                                    v-decorator="[
                                        `${_item.name}`
                                    ]"
                                    disabled></a-input>
                                    <a-select 
                                    v-if="_item.type == 'select'"
                                    v-decorator="[
                                        `${_item.name}`
                                    ]"   style="width: 100%" @change="handleChange" disabled>
                                        <a-select-option v-for="($item,$i) in _item.options" :key="$i" :value="$item.val">
                                            {{$item.label}}
                                        </a-select-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </template>
                </a-form>
            </a-tab-pane>
            <a-tab-pane key="2" tab="工艺信息">
                <a-form size='small' :form="form" layout='horizontal'>
                    <template v-for="(item,i) in craftForm">
                        <div class="divider" :key="i">{{item.head}}</div>
                        <a-row :gutter="24" v-for="(_item,_i) in item.attr" :key="_i">
                            <a-col :span="_item.span">
                                <a-form-item :label="_item.label" :labelCol="labelCol" :wrapperCol="wrapperCol">
                                    <a-input
                                    v-if="_item.type == 'input'"
                                    v-decorator="[
                                        `${_item.name}`
                                    ]"
                                    disabled></a-input>
                                    <a-select 
                                    v-if="_item.type == 'select'"
                                    v-decorator="[
                                        `${_item.name}`
                                    ]"   style="width: 100%" disabled>
                                        <a-select-option v-for="($item,$i) in _item.options" :key="$i" :value="$item.val">
                                            {{$item.label}}
                                        </a-select-option>
                                    </a-select>

                                    <a-upload  v-if="_item.type == 'upload' && _item.name == 'pic'" @preview="handlePreview" list-type="picture-card" :headers="headers" :action="postUrl" name="file" :default-file-list="picList"  @change="handlePicChange">
                                        <!-- <div v-if="picList.length < 6" >
                                            <a-icon type="plus" />
                                            <div class="ant-upload-text">Upload</div>
                                        </div> -->
                                    </a-upload>

                                    <a-upload  v-if="_item.type == 'upload' && _item.name == 'structurePic'" @preview="handlePreview" list-type="picture-card" :headers="headers" :action="postUrl" name="file" :default-file-list="structPicList" @change="handleStructPicChange">
                                        <!-- <div v-if="structPicList.length < 6" >
                                            <a-icon type="plus" />
                                            <div class="ant-upload-text">Upload</div>
                                        </div> -->
                                    </a-upload>

                                </a-form-item>
                            </a-col>
                        </a-row>
                    </template>
                </a-form>
            </a-tab-pane>
        </a-tabs>
        
        <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel">
            <img alt="example" style="width: 100%" :src="previewImage" />
        </a-modal>
    </div>
</template>

<script>
import Vue from 'vue'
import {ACCESS_TOKEN} from '@/store/mutation-types'
function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}
export default {
    data(){
        return{
            activeKey:'1',
            previewVisible: false,
            previewImage: '',
            postUrl: '/api/sysFileInfo/uploadfile',
            headers: {
                Authorization: 'Bearer ' + Vue.ls.get(ACCESS_TOKEN),
            },
            picList:[],
            baseUrl:process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id=',
            structPicList:[],
           
            formIndex:1,
            labelCol: {
                xs: { span: 24 },
                sm: { span: 8 }
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: { span: 12 }
            },
            labelCol_JG: {
                xs: { span: 24 },
                sm: { span: 4 }
            },
            wrapperCol_JG: {
                xs: { span: 24 },
                sm: { span: 12 }
            },
            formAttr:{
                1:[
                    {
                        head:'产品参数',
                        attr:[
                            {
                                label:'产品类型',
                                name:'productType',
                                span:12,
                                type:'select',
                                options:[
                                    {
                                        val:1,
                                        label:'方形电池'
                                    },
                                    {
                                        val:2,
                                        label:'圆柱电池'
                                    },
                                    {
                                        val:3,
                                        label:'软包电池'
                                    }
                                ]
                            },
                        ]
                    },
                    {
                        head:'尺寸、重量参数填写',
                        attr:[
                            {
                                label:'产品高度(mm不含极柱)',
                                name:'nonePolarHeight',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'产品高度(mm含极柱)',
                                name:'polarHeight',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'产品宽度(mm不含蓝膜)',
                                name:'noneBlueFilmWidth',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'产品宽度(mm含蓝膜)',
                                name:'blueFilmWidth',
                                span:12,
                                type:'input',
                            },

                            {
                                label:'产品厚度(mm不含蓝膜)',
                                name:'noneBlueFilmThickness',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'产品厚度(mm含蓝膜)',
                                name:'blueFilmThickness',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'产品重量(g)',
                                name:'weight',
                                span:12,
                                type:'input',
                            },
                        ]
                    },
                    {
                        head:'电性能参数填写',
                        attr:[
                            {
                                label:'额定容量(Ah)',
                                name:'ah',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'额定容量点倍率(C)',
                                name:'capacity',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'标称电压(V)',
                                name:'voltage',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'能量密度(Wh/kg)',
                                name:'density',
                                span:12,
                                type:'input',
                            },

                            {
                                label:'ACR(mΩ)≤',
                                name:'acr',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'DCR(mΩ)≤',
                                name:'dcr',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'循环描述',
                                name:'loop',
                                span:24,
                                type:'input',
                            },
                            {
                                label:'性能描述',
                                name:'performance',
                                span:24,
                                type:'input',
                            },
                        ]
                    },
                    {
                        head:'测试参数',
                        attr:[
                            {
                                label:'1C对应电流(A)',
                                name:'rateCurrent',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'电压范围',
                                name:'voltageRange',
                                span:12,
                                type:'input',
                            },
                        ]
                    },
                ],

                2:[
                    {
                        head:'产品参数',
                        attr:[
                            {
                                label:'产品类型',
                                name:'productType',
                                span:12,
                                type:'select',
                                options:[
                                    {
                                        val:1,
                                        label:'方形电池'
                                    },
                                    {
                                        val:2,
                                        label:'圆柱电池'
                                    },
                                    {
                                        val:3,
                                        label:'软包电池'
                                    }
                                ]
                            },
                        ]
                    },
                    {
                        head:'尺寸、重量参数填写',
                        attr:[
                            {
                                label:'产品高度(mm不含极柱)',
                                 name:'nonePolarHeight',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'产品高度(mm含极柱)',
                                 name:'polarHeight',
                                span:12,
                                type:'input',
                            },
                            
                            {
                                label:'产品直径(mm)',
                                name:'diameter',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'产品重量(g)',
                                name:'weight',
                                span:12,
                                type:'input',
                            },

                        ]
                    },
                    {
                        head:'电性能参数填写',
                        attr:[
                            {
                                label:'额定容量(Ah)',
                                name:'ah',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'额定容量点倍率(C)',
                                name:'capacity',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'标称电压(V)',
                                name:'voltage',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'能量密度(Wh/kg)',
                                name:'density',
                                span:12,
                                type:'input',
                            },

                            {
                                label:'ACR(mΩ)≤',
                                name:'acr',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'DCR(mΩ)≤',
                                name:'dcr',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'循环描述',
                                name:'loop',
                                span:24,
                                type:'input',
                            },
                            {
                                label:'性能描述',
                                name:'performance',
                                span:24,
                                type:'input',
                            },
                        ]
                    },
                ],

                3:[
                    {
                        head:'产品参数',
                        attr:[
                            {
                                label:'产品类型',
                                name:'productType',
                                span:12,
                                type:'select',
                                options:[
                                    {
                                        val:1,
                                        label:'方形电池'
                                    },
                                    {
                                        val:2,
                                        label:'圆柱电池'
                                    },
                                    {
                                        val:3,
                                        label:'软包电池'
                                    }
                                ]
                            },
                        ]
                    },
                    {
                        head:'尺寸、重量参数填写',
                        attr:[
                            {
                                label:'产品高度(mm不含极柱)',
                                name:'nonePolarHeight',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'产品高度(mm含极柱)',
                                name:'polarHeight',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'产品宽度(mm不含蓝膜)',
                                name:'noneBlueFilmWidth',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'产品宽度(mm含蓝膜)',
                                name:'blueFilmWidth',
                                span:12,
                                type:'input',
                            },

                            {
                                label:'产品厚度(mm不含蓝膜)',
                                name:'noneBlueFilmThickness',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'产品厚度(mm含蓝膜)',
                                name:'blueFilmThickness',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'产品重量(g)',
                                name:'weight',
                                span:12,
                                type:'input',
                            },
                        ]
                    },
                    {
                        head:'电性能参数填写',
                        attr:[
                            {
                                label:'额定容量(Ah)',
                                name:'ah',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'额定容量点倍率(C)',
                                name:'capacity',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'标称电压(V)',
                                name:'voltage',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'能量密度(Wh/kg)',
                                name:'density',
                                span:12,
                                type:'input',
                            },

                            {
                                label:'ACR(mΩ)≤',
                                name:'acr',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'DCR(mΩ)≤',
                                name:'dcr',
                                span:12,
                                type:'input',
                            },
                            {
                                label:'循环描述',
                                name:'loop',
                                span:24,
                                type:'input',
                            },
                            {
                                label:'性能描述',
                                name:'performance',
                                span:24,
                                type:'input',
                            },
                        ]
                    },
                ]
            },
            craftForm:[
                {
                    head:'二、产品工艺信息填写',
                    attr:[
                        {
                            label:'卷芯工艺',
                            name:'rollCore',
                            span:12,
                            type:'input',
                            type:'select',
                            options:[
                                {
                                    val:1,
                                    label:'卷绕'
                                },
                                {
                                    val:2,
                                    label:'叠片'
                                }
                            ]
                        },
                        {
                            label:'卷芯出极耳方式',
                            name:'earbud',
                            span:12,
                            type:'input',
                        },
                        {
                            label:'电芯端子方式',
                            name:'terminal',
                            span:12,
                            type:'input',
                        },
                        {
                            label:'裸电芯数量',
                            name:'bareWireCount',
                            span:12,
                            type:'input',
                        },

                        {
                            label:'内部结构类型',
                            name:'internalStructure',
                            span:12,
                            type:'input',
                        },
                        {
                            label:'内部结构图',
                            name:'structurePic',
                            span:12,
                            type:'upload',
                        },
                        {
                            label:'产品图片',
                            name:'pic',
                            span:12,
                            type:'upload',
                        },
                    ]
                },
            ],
            form: this.$form.createForm(this)
        }
    },
    methods: {
        handleCancel() {
            this.previewVisible = false;
        },
        async handlePreview(file) {
            if (!file.url && !file.preview) {
                file.preview = await getBase64(file.originFileObj);
            }
            this.previewImage = file.url || file.preview;
            this.previewVisible = true;
        },
        handleStructPicChange(info){

            if (info.file.status !== 'uploading') {
            }
            if (info.file.status === 'done') {
                let res = info.file.response
                if (res.success) {
                    this.structPicList.push({
                        status: 'done',
                        uid: res.data.id,
                        name: res.data.fileOriginName,
                        url: this.baseUrl+res.data.id
                    })
                } else {
                    _this.$message.error(res.message)
                }
            }
            
            if (info.file.status === 'error') {
                _this.$message.error(`${info.file.name}上传失败`);
            }
        },
        handlePicChange(info) {

            if (info.file.status !== 'uploading') {
            }
            if (info.file.status === 'done') {
                let res = info.file.response
                if (res.success) {
                    this.picList.push({
                        status: 'done',
                        uid: res.data.id,
                        name: res.data.fileOriginName,
                        url: this.baseUrl+res.data.id
                    })
                } else {
                    _this.$message.error(res.message)
                }
            }
            if (info.file.status === 'error') {
                _this.$message.error(`${info.file.name}上传失败`);
            }
        },
        getByClass(parent, cls) {
            if (parent.getElementsByClassName) {
                return Array.from(parent.getElementsByClassName(cls));
            } else {
                var res = [];
                var reg = new RegExp(' ' + cls + ' ', 'i')
                var ele = parent.getElementsByTagName('*');
                for (var i = 0; i < ele.length; i++) {
                    if (reg.test(' ' + ele[i].className + ' ')) {
                    res.push(ele[i]);
                    }
                }
                return res;
            }
        },
        initSm4Width(){
            setTimeout(() => {
                this.$nextTick(()=>{
                    let _item = this.getByClass(document,'ant-col-sm-8')[0]
                    let items = this.getByClass(document,'ant-col-sm-4')
                    for (let e of items) {
                        e.style.width = _item.offsetWidth+'px'
                    }
                })
            }, 500);
            
        },
        handleChange(val) {
            this.formIndex = val
        },
        next(){
            let formAtrr = this.formAttr[this.formIndex]
            let fields = []
            formAtrr.forEach(item => {
                item.attr.forEach(_item => {
                    fields.push(_item.name)
                });
            });
            let isError = false
            const { form: { validateFields } } = this
            validateFields(fields, (valid, values) => {
                if (valid) {
                    isError = true
                }
            })
            if (!isError) {
                this.step = 1
            }
        },
        prev(){
            this.step = 0
        },
        add(){
            let formAtrr = this.craftForm
            let fields = []
            formAtrr.forEach(item => {
                item.attr.forEach(_item => {
                    fields.push(_item.name)
                });
            });
            const { form: { validateFields } } = this
            let isError = false
            validateFields(fields, (valid, values) => {
                if (valid) {
                    isError = true
                }
            })

            if (!isError) {
                validateFields((errors, values) => {
                    console.log(values)
                })
            }
        }
    },
    created() {
        
    },
    mounted(){
        this.initSm4Width()
        window.addEventListener('resize', this.initSm4Width)
    },
    beforeDestroy(){
        window.removeEventListener('resize',this.initSm4Width)
    },
}
</script>

<style lang="less" scoped=''>
/deep/.ant-row{
    margin: auto 35px 4px !important;
}
.divider{
    background: #f2f2f2;
    padding: 4px 12px;
    font-size: 14px;
    margin: 4px 0;
}
.divider:first-child{
    font-size: 15px;
    margin-top: 20px;
}
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
/deep/.ant-tabs-nav-scroll{
    margin-left: 100px;
}
/deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab{
    margin-right: 20px;
}
/deep/.ant-input[disabled],/deep/.ant-select-disabled .ant-select-selection{
    color: rgba(0, 0, 0, 0.7);
    background-color: #fff;
    cursor: initial;
}
/deep/.ant-form label{
    font-size: 12px;
}
</style>