/* 通用 */

/* 字号 */
.f-30 {
  font-size: 30px;
}

.f-20 {
  font-size: 20px;
}

.f-18 {
  font-size: 18px;
}

.f-16 {
  font-size: 16px;
}

.f-14 {
  font-size: 14px;
}

.f-12 {
  font-size: 12px;
}

/* 字重 */
.fw-7 {
  font-weight: 700;
}

.fw-6 {
  font-weight: 600;
}

.fw-5 {
  font-weight: 500;
}


/* 透明度 */
.op-9 {
  opacity: 0.9;
}

.op-6 {
  opacity: 0.6;
}

.op-0 {
  opacity: 0;
}

/* padding */
.p-30 {
  padding: 30px;
}

.p-10 {
  padding: 10px;
}

.px-10 {
  padding: 0 10px;
}

.pt-30 {
  padding-top: 30px;
}

.pl-30 {
  padding-left: 30px;
}

.pl-20 {
  padding-left: 20px;
}

.pl-15 {
  padding-left: 15px;
}

.pl-10 {
  padding-left: 15px;
}

/* margin */

.mt-50 {
  margin-top: 50px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-30 {
  margin-top: 30px;
}

.mt-20 {
  margin-top: 20px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-5 {
  margin-top: 5px;
}

.mb-20 {
  margin-bottom: 20px;
}

.ml-40 {
  margin-left: 40px;
}

.ml-20 {
  margin-left: 20px;
}

.ml-30 {
  margin-left: 30px;
}

.ml-5 {
  margin-left: 5px;
}


.mr-130 {
  margin-right: 130px;
}


.mr-30 {
  margin-right: 30px;
}

.mr-20 {
  margin-right: 20px;
}

.mr-10 {
  margin-right: 10px;
}

.mr-5 {
  margin-right: 5px;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

/* 排版 */

.center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex{
  display: flex;
}

.f-column {
  flex-direction: column;
}


.h-center {
  display: flex;
  align-items: center;

}

.h-between {
  display: flex;
  justify-content: space-between;
}

/* 单行省略 */
.singe-line {
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
  white-space: nowrap;

}

/* 两行省略 */
.double-line {
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 三行省略 */
.three-line {
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

/* 手指 */
.pointer {
  cursor: pointer;
}

.link-btn{
  color: #1890ff;
  cursor: pointer;
  font-size: 12px;
}

.text-left{
  text-align: left;
}
.text-right{
  text-align: right;
}

.file-list{
  display: flex;
  margin-top: 12px;
  flex-direction: column;
  align-items: flex-start;
}
.file-head{
  padding-top: 12px;
  line-height: 30px;
  font-size: 14px;
  font-weight: bold;
}

.file-list{
  margin-top: 4px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.file-lists{
  width: 100%;
  font-size: 13px;
}

.file-item{
  display: flex;
  border-bottom: 1px solid #cccccc3d;

}

.file-name{
  padding: 18px 0;
}

.file-link,.head-link{
  margin-right: 6px;
}