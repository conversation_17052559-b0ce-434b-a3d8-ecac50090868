<template>
  <a-modal title="新增样品追溯" :width="750" :dialog-style="{ top: '20px' }" :visible="visible"
           :confirmLoading="confirmLoading" @ok="handleSubmit"
           @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">

        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item label="管理单号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-decorator="['code', {rules: [{required: true, message: '请输入管理单号!'}]}]"
                       placeholder="请输入管理单号"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="样品编号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-decorator="['cellCode', {rules: [{required: true, message: '请输入样品编号!'}]}]"
                       placeholder="请输入样品编号"/>
            </a-form-item>
          </a-col>

        </a-row>
        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item label="样品状态" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <!--                <a-input v-decorator="['productName', {rules: [{required: true, message: '请输入产品名称!'}]}]" placeholder="请输入产品名称" />-->
              <a-select style="width: 100%"
                        v-decorator="['cellStatus', {rules: [{required: true, message: '请选择样品状态!'}]}]"
                        placeholder="请选择样品状态">
                <a-select-option value="testing">在测</a-select-option>
                <a-select-option value="saving">在库</a-select-option>
                <a-select-option value="disassemble">拆解</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="样品位置" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-decorator="['cellAddress']"
                       placeholder="请输入样品位置"/>
            </a-form-item>
          </a-col>

        </a-row>
        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item label="类型" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-decorator="['competitiveType', {rules: [{required: true, message: '请输入类型!'}]}]"
                       placeholder="请输入类型"/>
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="厂商" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-decorator="['factory', {rules: [{required: true, message: '请输入厂商!'}]}]"
                       placeholder="请输入厂商"/>
            </a-form-item>
          </a-col>

        </a-row>
        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item label="化学体系" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-decorator="['chemicalSystem', {rules: [{required: true, message: '请输入化学体系!'}]}]"
                       placeholder="请输入化学体系"/>
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="容量" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input-number v-decorator="['capacity']" style="width: 100%"
                              placeholder="请输入容量"/>
            </a-form-item>
          </a-col>

        </a-row>
        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item label="型号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-decorator="['model']"
                       placeholder="请输入型号"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="尺寸" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-decorator="['competitiveSize']"
                       placeholder="请输入尺寸"/>
            </a-form-item>
          </a-col>

        </a-row>
        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item label="应用领域" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-decorator="['applicationArea']"
                       placeholder="请输入应用领域"/>
            </a-form-item>
          </a-col>
        </a-row>

      </a-form>
    </a-spin>
    <template slot="footer">
      <a-button @click="handleCancel">
        取消
      </a-button>
      <a-button type="primary" @click="handleSubmit">
        确定
      </a-button>
    </template>
  </a-modal>
</template>

<script>
import Vue from 'vue'
import {competitiveAnalysisSampleAdd} from "@/api/modular/system/competitveAnalysisManager"


export default {


  data() {
    return {

      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 8
        }
      },
      labelCol1: {
        xs: {
          span: 24
        },
        sm: {
          span: 4
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 15
        }
      },
      wrapperCol1: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this),
      record: {}
    }
  },
  methods: {
    // 初始化方法
    add() {
      this.visible = true
    },

    async handleSubmit() {
      const {
        form: {
          validateFields
        }
      } = this
      this.confirmLoading = true
      validateFields(async (errors, values) => {
        if (!errors) {

          let $params = {
            ...values
          };
          competitiveAnalysisSampleAdd($params).then((res) => {
            if (res.success) {

              this.$message.success('新增成功')
              this.visible = false
              this.confirmLoading = false
              this.handleCancel()
            } else {
              this.$message.error('新增失败：' + res.message)
            }
          }).finally((res) => {
            this.confirmLoading = false
          })
        } else {
          this.confirmLoading = false
        }
      })
    },


    handleCancel() {
      this.form.resetFields()
      this.visible = false
      this.$emit('ok')
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .ant-modal-header {
  padding: 16px;
}

/deep/ .ant-modal-body {
  padding: 16px;
}

/deep/ .ant-modal-footer {
  padding: 16px 16px !important;
}

/deep/ .ant-form-item {
  margin-bottom: 10px;
}

/deep/ .ant-upload.ant-upload-drag p.ant-upload-text {
  font-size: 14px;
}

/deep/ .ant-upload.ant-upload-drag p.ant-upload-drag-icon {
  margin-bottom: 10px;
}

/deep/ .ant-upload.ant-upload-drag p.ant-upload-drag-icon .anticon {
  font-size: 40px;
}

</style>
