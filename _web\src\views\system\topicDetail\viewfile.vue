<template>
    <a-modal
        title="文件列表"
        :width="800"
        :visible="visible"
        :confirmLoading="confirmLoading"
        @cancel="handleCancel"
    >
        <template slot="footer">
            <a-button key="back" @click="handleCancel">
                  关闭
            </a-button>
        </template>
        <div>1.项目信息</div>
        <a-table
            :pagination="false"
            :columns="vcolumns"
            size="middle"
            :dataSource="datas"
            :rowKey="(record) => record.issueId"
        >
        </a-table>
        <div style="margin-top:20px">2.文件信息</div>
        <a-spin :spinning="confirmLoading">
            <a-table
                :pagination="false"
                :loading="confirmLoading"
                :columns="columns"
                :dataSource="loadData"
                size="middle"
                :rowKey="(record) => record.attachmentId"
                >
                <template slot="attachmentName" slot-scope="text,record">
                    <!-- <a @click="preview(record)">{{text}}</a> -->
                    <a target="_blank" :href="baseUrl+record.attachmentId">{{text}}</a>
                </template>
                <!-- <template slot="dept" slot-scope="text,record">
                    <span v-for="(item,i) in record.departmentCateList" :key="i">
                        <label>{{item.value}}</label>
                        <label v-if="item.pid == 1">-</label>
                    </span>
                </template> -->
            </a-table>   
        </a-spin>
    </a-modal>
</template>

<script>
    import Vue from 'vue'
    import {
        STable
    } from '@/components'
    import {reviewFiles} from "@/api/modular/system/topic"
 
    export default {
        components: {
            STable
        },
        
        data() {
            return {
                baseUrl:`http://jira.evebattery.com/rest/oa2jira/1.0/attachment/preview?auth=${Vue.ls.get("jtoken")}&attachmentId=`,
                datas:[],
                vcolumns:[
                    
                ],
                queryParam:{
                },
                confirmLoading:false,
                visible:false,
                loading:false,
                columns: [
                ],
                loadData: []/* parameter => {
					return reviewFiles(Object.assign(parameter, this.queryParam)).then((res) => {
						return res.data
					})
				} */,
            }
        },
        created() {
        },
        methods: {
            preview(record){
                this.$emit('preview', record.attachmentId,record.attachmentName)
            },
            callReviewFiles(){
                this.confirmLoading = true
                reviewFiles(this.queryParam).then((res) => {
                    if (res.success) {
                        this.loadData = res.data
                    } else {
                        this.$message.error(res.message)
                    }
                    this.confirmLoading = false
				}).finally((res) => {
                    this.confirmLoading = false
                })
            },
            handleCancel(){
                this.queryParam = {}
                this.datas = []
                this.loadData = []
                this.visible = false
            },
            view(record,flag){
                record.deptList = ''
                record.deptList = record.departmentCateList.map(e=>e.value).join('-')

                record.cateList = ''
                record.mainCompletePersonList = ''
                if (flag == 0) {
                    record.cateList = record.projectCateList.map(e=>e.value).join('-')
                    this.vcolumns = [
                        {
                        title: '课题分类',
                        dataIndex: 'cateList',
                        align: 'center',
                        width: 100
                        },
                        {
                            title: '课题名称',
                            width: 120,
                            dataIndex: 'projectName',
                        },
                        {
                            title: '课题负责人',
                            width: 80,
                            dataIndex: 'projectLeader',
                        },
                        {
                            title: '部门',
                            width: 100,
                            dataIndex: 'deptList',
                        },
                    ];
                    this.columns=[
                      {
                        title: '序号',
                        dataIndex: 'index',
                        key: 'index',
                        align: 'center',
                        width: 50,
                        customRender: (text, record, index) => `${index+1}`,
                      },
                      {
                        title: '阶段',
                        dataIndex: 'stageName',
                        width: 80,
                      },
                      {
                        title: '文件名称',
                        dataIndex: 'attachmentName',
                        width: 180,
                        scopedSlots: { customRender: 'attachmentName' }

                      },
                      {
                        title: '时间',
                        dataIndex: 'createDate',
                        width: 80
                      },
                    ];
                }else{
                    record.cateList = record.resultCateList.map(e=>e.value).join('-')
                    record.mainCompletePersonList = record.mainCompletePerson.join(',')
                    this.vcolumns = [
                        {
                        title: '课题分类',
                        dataIndex: 'cateList',
                        align: 'center',
                        width: 100
                        },
                        {
                            title: '成果名称',
                            width: 120,
                            dataIndex: 'resultName',
                        },
                        {
                            title: '负责人',
                            width: 80,
                            dataIndex: 'resultLeader',
                        },
                        {
                            title: '部门',
                            width: 100,
                            dataIndex: 'deptList',
                        },
                    ];
                  this.columns = [
                    {
                      title: '序号',
                      dataIndex: 'index',
                      key: 'index',
                      align: 'center',
                      width: 50,
                      customRender: (text, record, index) => `${index + 1}`,
                    },
/*                    {
                      title: '阶段',
                      dataIndex: 'stageName',
                      width: 80,
                    },*/
                    {
                      title: '文件名称',
                      dataIndex: 'attachmentName',
                      width: 180,
                      scopedSlots: {customRender: 'attachmentName'}

                    },
                    {
                      title: '时间',
                      dataIndex: 'createDate',
                      width: 80
                    },
                  ];
                }
                
                this.datas.push(record)
                this.queryParam.issueId = record.issueId
                setTimeout(() => {
                    this.callReviewFiles()
                }, 100);
                this.visible = true
            },
        },
        watch: {}
    }
</script>

<style>

</style>