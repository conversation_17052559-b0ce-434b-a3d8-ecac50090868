<template>
<a-modal title="OCV&ACR" :width="1200" :visible="visible" :loading="loading" @cancel="handleCancel"
         :dialog-style="{ top: '20px' }"
>
	<template slot="footer">
		<a-button key="back" @click="handleCancel">关闭</a-button>
  </template>
  <a-descriptions title="电芯信息">
    <a-descriptions-item label="产品名称">
      {{ record.productName }}
    </a-descriptions-item>
    <a-descriptions-item label="电芯编码">
      {{ record.cellCode }}
    </a-descriptions-item>
  </a-descriptions>
<!--	<div class="ocvTable">-->
<!--		<a-table :style="{height:'initial'}" :rowKey="record => record.id" :pagination="false" :loading="loading" :columns="tableColumns" :data-source="tableData" size="middle" bordered>-->
<!--		</a-table>-->
<!--	</div>-->
<!--  图表-->
  <div class="all-wrapper">
    <div class="block left-content ">
      <div id="ocvEchart" ref="ocvEchart"
           style="width: 600px;height: 417px;border: 0.5px solid #ccc;"
      ></div>
    </div>
<!--  </div>-->
<!--  <div class="all-wrapper">-->
    <div class="block right-content " id="export">
      <div >
        <div style="display: flex;justify-content: center;font-weight: bold;font-size: 18px">OCV&ACR数据</div>
        <div class="ocvTable">
          <s-table :columns="tableColumns"
                   ref="ocvTable"
                   :data="loadData"
                   :loading="loading"
                   :rowKey="(record) => record.id"
                   bordered
                   size="middle">

          </s-table>
<!--          <a-table :style="{height:'initial'}" :rowKey="record => record.id" :pagination="false" :loading="loading" :columns="tableColumns" :data-source="tableData" size="middle" bordered>-->
<!--          </a-table>-->
        </div>
      </div>
    </div>
  </div>


</a-modal>
</template>

<script>
import {clamp, STable} from '@/components'
import {
  getFailureCellOcvRecordList,
  getFailureCellOcvRecordPageList
} from "@/api/modular/system/testFailure"
export default {
  components: {
    clamp,
    STable,
  },
	data() {
		return{
			visible: false,
			loading: false,
      record:{},
      queryParam:{},
      loadData: parameter => {
        this.queryParam.testFailureId = this.record.id
        return getFailureCellOcvRecordPageList(Object.assign(parameter, this.queryParam)).then((res) => {
          return res.data
        })
      },
			tableColumns:[
				{
          title: '序号',
          dataIndex: 'index',
          align: 'center',
          customRender: (text, record, index) => `${index+1}`
        },
				{
					title: '产品名称',
					dataIndex: 'productName',
          align: 'center',
				},
				{
					title: '电芯编码',
					dataIndex: 'cellCode',
          align: 'center',
				},
				{
					title: '内阻/mΩ',
					dataIndex: 'insideRes',
          align: 'center',
				},
				{
					title: '电压/mV',
					dataIndex: 'voltage',
          align: 'center',
				},
				{
					title: '记录日期',
					dataIndex: 'recordDate',
          align: 'center',
				},
			],
			tableData:[]
		}
	},
  // mounted() {
  //   console.log(this.$refs)
  //   // this.initdisChargeCapacityEchart()
  // },
  methods: {
		view(record){
      this.record = record;
			this.getFailureCellOcvRecordList({testFailureId:record.id})
      // this.initdisChargeCapacityEchart()
			this.visible = true
      this.$refs.ocvTable.refresh()
		},
    // 放电容量图表
    initdisChargeCapacityEchart() {

      this.disChargeCapacityEchart = this.echarts.init(this.$refs.ocvEchart, 'walden',  { devicePixelRatio: 2 })

      // let disChargeCapacityEchartList = this.allDataJson.disChargeCapacityEchartList
      // let disChargeCapacityEchartList = {innerResDataList:[[1,3],[2,7]],voltageDataList:[[1,6],[2,10]]}
      let disChargeCapacityEchartList = {innerResDataList: [], voltageDataList: []}
      console.log(this.tableData);
      for (let i = 0,j=0; i < this.tableData.length; i++){
        var record1 = this.tableData[i];
        var recordDate = record1.recordDate;
        var insideRes = record1.insideRes
        var voltage = record1.voltage
        if (isNaN(insideRes) || isNaN(voltage)) {
          continue
        }
        disChargeCapacityEchartList.innerResDataList[j] = [recordDate, insideRes];
        disChargeCapacityEchartList.voltageDataList[j] = [recordDate, voltage];
        j++
      }
      console.log(disChargeCapacityEchartList)
      let disChargeCapacityseriesList = []
      // for (let i = 0; i < disChargeCapacityEchartList.length; i++) {
        let series = [{
          name: 'Voltage',
          type: 'line',
          data: disChargeCapacityEchartList.voltageDataList,
          tooltip: {
            valueFormatter: function (value) {
              return '  ' + value + ' mV';
            }
          },
        }, 
        {
          name: 'ACR',
          type: 'line',
          yAxisIndex: 1,
          data: disChargeCapacityEchartList.innerResDataList,
          tooltip: {
            valueFormatter: function (value) {
              return '  ' + value + ' mΩ';
            }
          },
          lineStyle: {
            type: "dotted"
          },
        }];
        disChargeCapacityseriesList.push(...series);

      // }

      let chartOption = {
        animationDuration: 2000,
        title: {
          text: this.disChargeCapacityEchartTitle || 'OCV&ACR曲线',
          fontSize: 16,
          top:10,
          left: 'center',
          color: "#333"
        },
        grid: {
          show: true,
          borderWidth: 1,
          top:70,
          left:90,
          right:90,
          borderColor: '#ccc'
        },
        tooltip: {
          trigger: 'axis',
          // formatter: function (params) {
          //   var result = params[0].axisValue +' Cycle'+ '<br>'; // 添加 x 轴的数值
          //   // console.log(params);
          //   params.forEach(function (item,dataIndex) {
          //     // var type= item.data.name+'#'
          //     result += '<div style="width:150px;display: inline-block;">'+item.marker+item.seriesName+'</div>'  + item.data[1] + '<br>'; // 添加每个系列的数值
          //   });
          //   return result;
          // }
        },

        legend: {
          // data: legendList,
          show: true,
          top: 40,
          fontSize: 14,
          color: "#333"
        },

        xAxis: [
          {
            type: 'category',
            axisTick: { show: false },
            axisLabel: {
              show: true,
              width: 0.5,
              fontSize: 14,
              color: "#333",
              margin:15
            },
            name: 'Date',
            nameLocation: 'middle', // 将名称放在轴线的中间位置
            nameGap: 35,
            nameTextStyle: {
              fontSize: 14, // 可以根据需要调整字体大小
              color: "#333"
            },
          }
        ],
        textStyle: {
          fontFamily: "Times New Roman"
        },
        yAxis: [
          {
            type: 'value',
            name: 'Voltage / mV',
            position: 'left',
            alignTicks: true,
            nameGap: 60,
            splitLine: {
              show: true,  // 显示分隔线
              lineStyle: {
                type: 'solid',  // 设置分隔线的样式，比如虚线
                width: 0.5
              }
            },
            axisTick: {
              show: true,  // 显示刻度
            },
            axisLabel: {
              show: true,
              width: 0.5,
              fontSize: 14,
              color: "#333"
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              }
            },
            nameLocation: 'middle', // 将名称放在轴线的起始位置
            nameRotate: 90, // 旋转角度，使名称竖排
            nameTextStyle: {
              fontSize: 14, // 可以根据需要调整字体大小
              color: "#333"
            },
            splitNumber: 6,
          },
          {
            type: 'value',
            name: 'ACR / mΩ',
            // position: 'right',
            nameGap: 60,
            splitLine: {
              show: true,  // 显示分隔线
              lineStyle: {
                type: 'solid',  // 设置分隔线的样式，比如虚线
                width: 0.5
              }
            },
            axisTick: {
              show: true,  // 显示刻度
            },
            axisLabel: {
              show: true,
              width: 0.5,
              fontSize: 14,
              color: "#333"
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              }
            },
            nameLocation: 'middle', // 将名称放在轴线的起始位置
            nameRotate: 90, // 旋转角度，使名称竖排
            nameTextStyle: {
              fontSize: 14, // 可以根据需要调整字体大小
              color: "#333"
            },
            splitNumber: 6,
          }
        ],
        series: disChargeCapacityseriesList
      }

      this.disChargeCapacityEchart.clear()
      this.disChargeCapacityEchart.setOption(chartOption)
    },
		getFailureCellOcvRecordList(params){
			getFailureCellOcvRecordList(params).then(res => {
				this.loading = true
				if (!res.success) {
					return
				}
				this.tableData = res.data
        // this.$nextTick(() => {
        //   console.log(this.$refs); // 确保DOM已更新
        //   this.initdisChargeCapacityEchart()
        // });
        setTimeout(this.initdisChargeCapacityEchart(), 1500)
			}).finally(() => {
				this.loading = false
			})
		},
		handleCancel(){
			this.tableData = []
			this.visible = false
		},
	}
}
</script>
<style lang="less" scoped=''>

.all-wrapper{
  display: flex;
  justify-content: center;
  overflow: scroll;
}

.block {
  height: fit-content;
  padding: 10px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
  position: relative;
}
.block-content {
  width: 720px;
  margin-top: 10px;
  margin-bottom: 5px;
}

.left-content {
  width: 620px;
  margin-top: 15px;
  margin-bottom: 5px;
  margin-left: 5px;
  margin-right: 10px;
}

.right-content {
  width: calc(100% - 650px);
  margin-top: 15px;
  margin-bottom: 5px;
  margin-right: 5px;
}

.mt10 {
  margin-top: 10px;
}

.mb10 {
  margin-bottom: 10px;
}

/deep/ .ant-modal-body{
  padding: 12px !important;
  //display: flex;
  //justify-content: space-around;
  height: calc(100vh - 200px);
  //height: 600px;
  overflow-y: auto;
  &::-webkit-scrollbar {
    width: 8px;
    height: 2px;
    background: #dee4e9;
    border-radius: 6px; /*外层轨道*/
  }
  &::-webkit-scrollbar-thumb {
    display: block;
    width: 8px;
    margin: 0 auto;
    border-radius: 6px;
    background: #aaaaaa; /*内层轨道*/
  }
}
/deep/.ocvTable .ant-table-body {
	height: initial !important;
	overflow-y: scroll;
}

/deep/ .ant-descriptions-row:last-child{
  display: flex;
  justify-content: space-evenly;
}

/deep/.ant-descriptions-title{
  text-align: center;
  color: #333;
  font-size: 18px;
}

/deep/.ocvTable .ant-table-placeholder{
	display: none !important;
}
</style>