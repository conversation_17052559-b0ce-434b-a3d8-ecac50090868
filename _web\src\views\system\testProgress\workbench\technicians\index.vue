<template>
	<div class="container">
		<!-- 标题部分 start -->
		<div class="head-wrapper" id="head">
			<div class="salutation">
				<p>{{ tips }}</p>
				<p>时间:{{ now }}</p>
			</div>

			<div class="circle-block" v-for="(item, index) in dataArr">
				<div class="data-packet" v-if="!item.type">
					<div class="icon" :style="
							`background: ${item.color};
	border: 8px solid ${item.borderColor};`
						">
						<a-icon :type="item.icon" />
					</div>
					<div class="detail">
						<p>{{ item.num < 10 ? "0" : "" }}{{ item.num }}</p>
								<p>{{ item.detail }}</p>
					</div>
				</div>

				<div v-else class="line"></div>
			</div>
		</div>
		<!-- 标题部分 end -->

		<!-- 内容部分 start -->
		<div class="content-wrapper">
			<div class="left" id="left">
				<div class="options">
					<a-input-search class="mr10" size="small" placeholder="请输入项目名称/委托单号/委托人/测试员" @search="handleInputSearch" />
					<!-- <a-button class="mr8" size="small" @click="getTodoTaskList">
						刷新
					</a-button> -->
          <a-tooltip>
            <template v-if="serialObj.serialPortOpen" slot="title">
              断开电压内阻测试仪
            </template>
            <template v-else slot="title">
              连接电压内阻测试仪
            </template>
            <div v-if="serialObj.serialPortOpen" class="icon-btn mr8">
              <a-popconfirm title="确认断开设备？" ok-text="断开" cancel-text="取消" @confirm="handleCloseConnect" @cancel="cancel">
                <svg t="1715755661860" class="icon" viewBox="0 0 1024 1024" version="1.1"
                  xmlns="http://www.w3.org/2000/svg" p-id="7657" width="26" height="26">
                  <path
                    d="M867.328 189.44H76.8c-12.8 0-25.6 12.8-25.6 25.6v629.76c0 12.8 12.8 25.6 25.6 25.6h128v25.6c0 12.8 12.8 25.6 25.6 25.6s25.6-12.8 25.6-25.6v-25.6h432.128v25.6c0 12.8 12.8 25.6 25.6 25.6s25.6-12.8 25.6-25.6v-25.6h128c12.8 0 25.6-12.8 25.6-25.6V215.04c0-12.8-12.8-25.6-25.6-25.6z m-31.744 621.056h-727.04V249.344h727.04v561.152z"
                    p-id="7658" fill="#1890FF"></path>
                  <path
                    d="M230.4 585.216h129.024c12.8 0 22.528-9.728 29.184-19.456l25.6-51.712 41.984 174.08c3.072 9.728 12.8 19.456 25.6 22.528h6.656c9.728 0 19.456-3.072 25.6-3.072l87.04-115.712h112.64c19.456 0 32.256-12.8 32.256-32.256s-12.8-32.256-32.256-32.256h-129.024c-9.728 0-19.456 3.072-25.6 12.8L504.32 614.4l-48.128-193.024c-3.072-15.872-15.872-25.6-29.184-25.6-12.8-3.072-25.6 3.072-32.256 15.872l-54.784 109.568H230.4c-19.456 0-32.256 12.8-32.256 32.256 0.512 18.944 13.312 31.744 32.256 31.744zM674.816 299.52h75.776V343.04h-75.776zM674.816 375.808h75.776v43.52h-75.776z"
                    p-id="7659" fill="#1890FF"></path>
                </svg>
              </a-popconfirm>
            </div>
            <div v-else class="icon-btn mr8" @click="handleOpenConnect">
              <svg t="1715755661860" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
                p-id="7657" width="26" height="26">
                <path
                  d="M867.328 189.44H76.8c-12.8 0-25.6 12.8-25.6 25.6v629.76c0 12.8 12.8 25.6 25.6 25.6h128v25.6c0 12.8 12.8 25.6 25.6 25.6s25.6-12.8 25.6-25.6v-25.6h432.128v25.6c0 12.8 12.8 25.6 25.6 25.6s25.6-12.8 25.6-25.6v-25.6h128c12.8 0 25.6-12.8 25.6-25.6V215.04c0-12.8-12.8-25.6-25.6-25.6z m-31.744 621.056h-727.04V249.344h727.04v561.152z"
                  p-id="7658" fill="#cccccc"></path>
                <path
                  d="M230.4 585.216h129.024c12.8 0 22.528-9.728 29.184-19.456l25.6-51.712 41.984 174.08c3.072 9.728 12.8 19.456 25.6 22.528h6.656c9.728 0 19.456-3.072 25.6-3.072l87.04-115.712h112.64c19.456 0 32.256-12.8 32.256-32.256s-12.8-32.256-32.256-32.256h-129.024c-9.728 0-19.456 3.072-25.6 12.8L504.32 614.4l-48.128-193.024c-3.072-15.872-15.872-25.6-29.184-25.6-12.8-3.072-25.6 3.072-32.256 15.872l-54.784 109.568H230.4c-19.456 0-32.256 12.8-32.256 32.256 0.512 18.944 13.312 31.744 32.256 31.744zM674.816 299.52h75.776V343.04h-75.776zM674.816 375.808h75.776v43.52h-75.776z"
                  p-id="7659" fill="#cccccc"></path>
              </svg>
            </div>
          </a-tooltip>
          <a-tooltip>
            <template slot="title">
              更改检测人
            </template>
            <div class="icon-btn" v-if="isShowAlterButton" @click="isShowWorkTransfer = true">
              <svg t="1726820556816" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="28106" width="24" height="24">
                <path d="M828.096 562.026667c64.256 0 116.266667 52.352 116.266667 116.757333a116.778667 116.778667 0 0 1-78.805334 110.570667A153.856 153.856 0 0 1 981.333333 938.666667a42.666667 42.666667 0 1 1-85.333333 0c0-37.973333-30.464-68.650667-67.754667-68.650667-37.482667 0.128-67.946667 30.784-68.053333 68.8a42.666667 42.666667 0 0 1-85.333333-0.298667 154.282667 154.282667 0 0 1 115.968-149.141333 116.672 116.672 0 0 1-78.997334-110.592c0-64.426667 51.989333-116.757333 116.266667-116.757333z m-601.92-61.482667l110.592 111.317333a42.666667 42.666667 0 1 1-60.544 60.16l-37.653333-37.930666v81.92c0 58.453333 46.869333 105.749333 104.533333 105.898666H564.266667a42.666667 42.666667 0 1 1 0 85.333334H342.976c-104.917333-0.277333-189.738667-85.866667-189.738667-191.210667v-81.941333L115.605333 672a42.666667 42.666667 0 0 1-57.6 2.688l-2.730666-2.474667a42.666667 42.666667 0 0 1-0.213334-60.352l110.592-111.317333a42.666667 42.666667 0 0 1 60.522667 0z m601.92 146.816c-17.024 0-30.933333 14.016-30.933333 31.424 0 17.429333 13.909333 31.424 30.933333 31.424 17.002667 0 30.933333-13.994667 30.933333-31.424 0-17.408-13.930667-31.424-30.933333-31.424z m-147.2-567.466667c104.917333 0 189.866667 85.504 189.866667 190.848l-0.021334 82.154667 37.717334-37.802667a42.666667 42.666667 0 0 1 57.6-2.581333l2.730666 2.474667a42.666667 42.666667 0 0 1 0.085334 60.352l-107.456 107.818666a42.581333 42.581333 0 0 1-66.688 0l-107.434667-107.818666a42.666667 42.666667 0 0 1 60.437333-60.245334l37.674667 37.802667v-82.154667c0-58.325333-46.848-105.514667-104.512-105.514666H459.733333a42.666667 42.666667 0 1 1 0-85.333334h221.162667zM195.904 42.666667c64.277333 0 116.266667 52.330667 116.266667 116.757333a116.778667 116.778667 0 0 1-78.805334 110.549333 153.813333 153.813333 0 0 1 115.797334 149.333334 42.666667 42.666667 0 1 1-85.333334 0c0-37.973333-30.464-68.672-68.053333-68.672a67.157333 67.157333 0 0 0-47.893333 19.904A68.650667 68.650667 0 0 0 128 419.157333a42.666667 42.666667 0 0 1-85.333333 0.277334 153.984 153.984 0 0 1 44.672-109.034667 152.746667 152.746667 0 0 1 70.997333-40.490667 116.714667 116.714667 0 0 1-78.677333-110.506666C79.658667 95.018667 131.626667 42.666667 195.925333 42.666667z m0 85.333333c-17.002667 0-30.933333 13.994667-30.933333 31.424 0 17.408 13.930667 31.402667 30.933333 31.402667 17.024 0 30.933333-13.994667 30.933333-31.402667 0-17.429333-13.909333-31.424-30.933333-31.424z" fill="#cccccc" p-id="28107"></path>
              </svg>
            </div>
          </a-tooltip>


				</div>
				<pbiTabs :tabsList="tabsArr" :activeKey="activeKey" @clickTab="handleTabsChange"></pbiTabs>
				<div class="tab" :style="{borderRadius:activeKey == 0 ? '0 10px 10px 10px' : '10px' }">
					<a-table id="outTable" :style="`height:${tableHeight}px`" :columns="columns" :rowKey="record => record.id" :scroll="{x:1200}"
						:data-source="tableData" :pagination="pagination" :loading="tableLoading">

						<!-- 测试员筛选 -->
						<div slot="testerFilterDropdown" slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }">
							<div class="tester-filter-dropdown">
								<a-input-search v-model="testerUserName" placeholder="请输入姓名" class="tester-filter-input" @change.sync="handleTesterFilter"/>
								<a-checkbox-group v-model="testerUser" :options="testerUserList" @change="handleChangeTester" />
							</div>
							<!-- 筛选图标 -->
							<a-icon slot="testerFilterIcon" slot-scope="filtered" type="user"
							:style="{ color: filtered ? '#108ee9' : undefined }" />
						</div>


						<!-- 日历筛选 -->
						<div slot="filterDropdown"
							slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }">
							<div class="filter-dropdown">
								<a-date-picker :value="filterData" style="width: 120px;margin: 7px 7px 0;"
									@change="handleDataChange" />
								<div class="btn-wrapper mt10">
									<a-button size="small" type="link" class="search-btn"
										@click="() => handleCalendarSearch(selectedKeys, confirm, column.dataIndex)">
										确定
									</a-button>
									<a-button class="search-btn" type="link" size="small"
										@click="() => handleCalendarReset(clearFilters)">
										重置
									</a-button>
								</div>
							</div>
						</div>
						<!-- 筛选图标 -->
						<a-icon slot="filterIcon" slot-scope="filtered" type="calendar"
							:style="{ color: filtered ? '#108ee9' : undefined }" />
						<!-- 日历筛选 -->

						<span slot="testName" slot-scope="text">
							<a-tooltip>
								<template slot="title">
									{{ text }}
								</template>
								<div class="oneline">{{ text }}</div>
							</a-tooltip>
						</span>

            <span slot="orderNos" slot-scope="text">
							<a-tooltip>
								<template slot="title" v-if="text">
									{{ text }}
								</template>
								<div class="oneline" v-if="text">{{ text }}</div>
								<div class="oneline" v-else>-</div>
							</a-tooltip>
						</span>

						<span slot="taskType" slot-scope="text">
							{{ text === "jmcs" ? "精密实验室" : text }}
						</span>
						<span slot="taskStatus" slot-scope="text">
							<a-tag :color="colorRender(text)" style="margin: 0">
								{{ text }}
							</a-tag>
						</span>
						<span class="btn-slot" slot="action" slot-scope="text, record, index">
               <a-dropdown-button >
                  <a-button v-if="record.stageFlag === 1" type="link" @click="handleDetail(record)">
								<!-- 已完成状态 -->
								<span v-if="record.taskStatus === '已完成'"> 详情 </span>
                    <!-- 第一阶段 -->
								<span v-else-if="record.taskType === 'rlsmcs_first'">{{
                    record.currentStep === null || record.currentStep === "0" ? "首次中检" : "进箱"
                  }}</span>
                    <!-- 中检阶段 -->
								<span v-else-if="
										(record.taskType === 'rlsmcs_middle' && record.currentStep === null) ||
											record.currentStep === '0'
									">出箱</span>
								<span v-else-if="record.taskType === 'rlsmcs_middle' && record.currentStep === '1'">出箱中检</span>
								<span v-else-if="
										record.taskType === 'rlsmcs_middle' && record.currentStep === '2' && record.haveInspection === 1
									">进箱中检</span>
								<span
                  v-else-if="record.taskType === 'rlsmcs_middle' && record.currentStep === '2' && record.haveInspection === 0">电芯转移</span>
								<span
                  v-else-if="record.taskType === 'rlsmcs_middle' && record.currentStep === '3' && record.haveInspection === 1">电芯转移</span>
                    <!-- 最后一阶段  -->
								<span v-else-if="record.taskType === 'rlsmcs_last'">{{
                    record.currentStep === null || record.currentStep === "0" ? "出箱" : "出箱中检"
                  }}</span>
								<span v-else>详情</span>
							</a-button>
							    <a-button v-else type="link" @click="handleDetail(record)">
								<!-- 已完成状态 -->
								<span v-if="record.taskStatus === '已完成'"> 详情 </span>
                <!-- 第一阶段 -->
								<span v-else-if="record.taskType === 'rlsmcs_first'">{{
                    record.currentStep === null || record.currentStep === "0" ? "首次中检" : ((record.pictureFlag === "1" || record.videoFlag === "1") && record.currentStep === "1") ? "拍照" : "进箱"
                  }}</span>
                <!-- 中间阶段 -->
								<span v-else-if="
										(record.taskType === 'rlsmcs_middle' && record.currentStep === null) ||
											record.currentStep === '0'
									">出箱</span>
								<span v-else-if="record.taskType === 'rlsmcs_middle' && record.currentStep === '1'">出箱中检</span>
								<span v-else-if="
										record.taskType === 'rlsmcs_middle' && record.currentStep === '2' && record.haveInspection === 1
									">进箱中检</span>
                <span v-else-if="
                    (record.pictureFlag === '1' || record.videoFlag === '1' ) && record.taskType === 'rlsmcs_middle'
                    && ((record.currentStep === '3' && record.haveInspection === 1) || (record.currentStep === '2' && record.haveInspection === 0))
									">拍照</span>
								<span v-else-if="
										record.taskType === 'rlsmcs_middle' &&
										(
											(!(record.pictureFlag === '1' || record.videoFlag === '1') && ((record.currentStep === '2' && record.haveInspection === 0) || record.currentStep === '3')) ||
											((record.pictureFlag === '1' || record.videoFlag === '1') && (record.currentStep === '3' && record.haveInspection === 0) || record.currentStep === '4')
										)
									">进箱</span>
                <!-- 最后一阶段  -->
								<span v-else-if="record.taskType === 'rlsmcs_last'">{{
                    record.currentStep === null || record.currentStep === "0" ? "出箱" : ((record.pictureFlag === "1" || record.videoFlag === "1") && record.currentStep === "2") ? "拍照" : "出箱中检"
                  }}</span>
								<span v-else-if="record.taskType === 'failureCell_ocv'">填写OCV</span>
								<span v-else>详情</span>
							</a-button>
                  <a-menu slot="overlay" >
                    <a-menu-item key="1" v-if="record.taskType.indexOf('rlsmcs') === -1 && record.taskType != 'failureCell_ocv' && record.taskType != 'safety_ba_test' && record.taskType != 'safety_tc_test'">
                      <a-button type="link"
                                @click="handleFile(record)">
                        附件管理
                      </a-button>
                    </a-menu-item>
                    <a-menu-item key="2" v-if="record.taskType.indexOf('rlsmcs') !== -1">
                      <a-button type="link"
                                @click="handleSchedule(record)">
                        计划表
                      </a-button>
                    </a-menu-item>
                    <a-menu-item key="3" v-if="record.taskType != 'safety_ba_test' && record.taskType != 'safety_tc_test' && record.taskStatus != '已完成'" :disabled="record.taskStatus === '已完成'">
                      <a-popconfirm title="确认完成?" ok-text="确认"  cancel-text="取消"
                                    @confirm="handleOk(record)" @cancel="handleCancel">
                        <a-button type="link">
                          完成
                        </a-button>
                      </a-popconfirm>
                    </a-menu-item>
                    <a-menu-item key="4" v-if="isShowAlterButton && ['rlsmcs_first', 'rlsmcs_middle', 'rlsmcs_last'].includes(record.taskType) && record.taskStatus !== '已完成'">
                      	<!-- 更改测试员 -->
                      <a-button type="link" @click="handleYfAlterTester(record)"
                                >
                        更改测试员
                      </a-button>
                    </a-menu-item>
                    <a-menu-item key="5" v-if="isShowAlterButton && ['jmcs', 'aqcs'].includes(record.taskType) && record.taskStatus !== '已完成'">
                      <a-button type="link" @click="handleAlterTester(record)"
                                >
                        更改测试员
                      </a-button>
                    </a-menu-item>
                    <a-menu-item key="6" v-if="isShowAlterButton && ['jmcs', 'aqcs'].includes(record.taskType) && record.taskStatus !== '已完成'">

                      <!-- 更改计划时间 -->
                      <a-button type="link" @click="handleAlterPlanTime(record)"
                                >
                        更改计划时间
                      </a-button>
                    </a-menu-item>
                  </a-menu>
                </a-dropdown-button>








						</span>
					</a-table>
				</div>
			</div>
			<div class="right">
				<div class="block mb12">
					<div class="title">
						<img src="@/assets/icons/bookmark.png" alt="" />
						<span class="text">任务管理</span>
					</div>
					<a-empty class="empty-block" description="正在加急开发中~" />
					<!-- <div class="box detail">
						<div
							class="detail-block"
							v-for="(item, index) in 12"
							:key="index"
						>
							<img src="@/assets/icons/bookmark.png" alt="" />
							<p>入口</p>
						</div>
					</div> -->
				</div>
				<div class="block bottom-block">
					<div class="title">
						<img src="@/assets/icons/bookmark.png" alt="" />
						<div class="sop-block">
							<div>SOP</div>
							<a-input-search v-model="sopSearch" placeholder="请输入文件名称" style="width: 150px"
								@search="handleFileSearch" />
						</div>
					</div>
					<div class="box row-content">
						<div class="row hand" v-for="(item, index) in sopArr" :key="index" @click="handleSopFile(item)">
							{{ item.name }}
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- 内容部分 end -->

		<!-- modal start -->
		<div>
			<!-- 精密实验室 -->
			<DetailsModal v-if="isShowModal" :modalData="modalData" @cancel="handleModalCancel"></DetailsModal>
			<!-- 日历寿命 -->
			<CalendarAroundModal v-if="isShowAroundModal" :modalData="modalData" :serialObj="serialObj"
				@submit="handleModalSubmit" @cancel="handleModalCancel" @openPort="handleOpenConnect"
				@closePort="handleCloseConnect"></CalendarAroundModal>
			<calendarMiddleModal v-if="isShowMiddleModal" :modalData="modalData" :serialObj="serialObj"
				@submit="handleModalSubmit" @cancel="handleModalCancel" @openPort="handleOpenConnect"
				@closePort="handleCloseConnect"></calendarMiddleModal>
      <BeforeAfterTestModal v-if="isShowBaTestModal" :modalData="modalData" :serialObj="serialObj"
        @submit="handleBaTestSubmit" @cancel="handleModalCancel" @openPort="handleOpenConnect"
        @closePort="handleCloseConnect"></BeforeAfterTestModal>
      <TempCycleTestModal v-if="isShowTcTestModal" :modalData="modalData" :serialObj="serialObj"
        @submit="handleTcTestSubmit" @cancel="handleModalCancel" @openPort="handleOpenConnect"
        @closePort="handleCloseConnect"></TempCycleTestModal>

			<FileModal v-if="isShowFileMOdal" :modalData="modalData" @cancel="handleModalCancel"></FileModal>
			<!-- 计划表 -->
			<ScheduleModal v-if="isShowScheduleModal" :modalData="modalData" @cancel="handleModalCancel"></ScheduleModal>
			<!-- 更改测试员 -->
			<AlterTesterModal v-if="isShowAlterTesterModal" :modalData="modalData" @cancel="handleModalCancel">
			</AlterTesterModal>
			<!-- 更改计划时间 -->
			<AlterPlanTimeModal v-if="isShowAlterPlanTimeModal" :modalData="modalData" @cancel="handleModalCancel">
			</AlterPlanTimeModal>
      <!-- 第四实验室/第六实验室的日历寿命测试待办任务：更改测试员 -->
			<AlterYfTesterModal v-if="isShowAlterYfTesterModal" :modalData="modalData" @cancel="handleModalCancel">
			</AlterYfTesterModal>

			<FailureCellAddOcvModal ref="addOcv" :serialObj="serialObj" />

			<!-- 批量修改测试员 -->
			 <workTransfer v-if="isShowWorkTransfer" @cancel="isShowWorkTransfer = false"></workTransfer>
		</div>
		<!-- modal end -->

		<a-drawer :bodyStyle="{ height: '100%' }" width="70%" :closable="false" placement="right" :visible="drawerVisible"
			@close="drawerVisible = false">
			<iframe :src="sopUrl" width="100%" height="100%"></iframe>
		</a-drawer>
	</div>
</template>

<script>
	import Vue from 'vue'
	import { formatDate } from "@/utils/format"
	import { timeFix } from "@/utils/util"
	import {
		getTodoTaskList,
		getLimsResultByOrdTaskId,
		getFileList,
		getTestProDetailByTaskId,
		InputDataSubmit,
		finishCalLifeTodoTask,

		getTodoTaskStatistics,
		getTesterList
	} from "@/api/modular/system/testProgressManager"

	import DetailsModal from "../components/detailsModal"
	import CalendarAroundModal from "../components/calendarAroundModal.vue"
	import calendarMiddleModal from "../components/calendarMiddleModal"
	import BeforeAfterTestModal from "../components/beforeAfterTestModal"
	import TempCycleTestModal from "../components/tempCycleTestModal"
	import FileModal from "@/components/uploadModal/index"
	import ScheduleModal from "../components/scheduleModal"
	import AlterTesterModal from "../components/alterTesterModal"
	import AlterPlanTimeModal from "../components/alterPlanTimeModal"
	import AlterYfTesterModal from "../components/alterYfTesterModal"
	import workTransfer from "../components/workTransfer"

	import pbiTabs from '@/components/pageTool/components/pbiTabs.vue'
	import FailureCellAddOcvModal from "@/views/system/dpvTestFailure/stock/addOcv.vue"


	import _ from "lodash"
	import { mapGetters } from "vuex";

	export default {
		name: "Technicians",
		data() {
			return {
				// 测试员表头筛选
				testerUserName:'',   // 测试员名称筛选
				testerUser:[],  // 选中的测试员
				testerUserList:[],   // 测试员列表（包含筛选条件）
				testerUserAllList:[],  // 全部测试员列表

				sopUrl: '',
				drawerVisible: false,
				now: "",
				tips: "",
				fileNames: "",
				searchValue: "",
				searchTesterValue: "",
				sopSearch: "",
				filterData: '',
				modalData: {},
				tableHeight: 0,
				tableWidth:0,
				activeKey: '0',

				// 串口
				serialObj: {
					serialPort: {},
					serialReader: {},
					serialWriter: {},
					serialPortOpen: false,
				},



				tableLoading: false,
				isShowModal: false,
				isShowMiddleModal: false,
        isShowBaTestModal: false,
        isShowTcTestModal: false,
				isShowFileMOdal: false,
				isShowScheduleModal: false,
				isShowAroundModal: false,
				isShowAlterTesterModal: false,
				isShowAlterYfTesterModal: false,
				isShowAlterPlanTimeModal: false,
				isShowAlterButton: false,
				isShowFailureCellOcvModal: false,
				isShowWorkTransfer:false,
				isFirst: true,//初次进入页面

				pagination: {
					size:'small',
					current: 1,
					pageSize: 10,
					total: 0,
					showSizeChanger: true,
					showQuickJumper: true,
					pageSizeOptions: ['10', '20', '30', '40', '50'],
					onChange: (current, size) => {
						this.storeList[this.arrName[this.activeKey]].pagination.pageNo = current

						this.getTodoTaskList()
					},
					onShowSizeChange: (current, pageSize) => {
						this.storeList[this.arrName[this.activeKey]].pagination.pageSize = pageSize

						this.getTodoTaskList()
					},
				},

				storeList:{},
				tabsArr:[
					{value:'0',label:'今日截止'},
					{value:'1',label:'今日任务'},
					{value:'2',label:'待办清单'},
					{value:'3',label:'逾期任务'},
					{value:'4',label:'任务总览'},
				],

				// tabsArr: ["今日截止", "今日任务", "待办清单", "逾期任务", "任务总览"],
				arrName: ["dueTodayTodo", "todayTodo", "allTodo", "delayTodo", "all"],


				selectOption: [
					{ value: 0, label: "SOP" },
					{ value: 1, label: "制度规范" },
					{ value: 2, label: "常用资料" },
					{ value: 3, label: "服务支持" }
				],
				sopOriginal: [
					{
						name: "01-大型产气测试仪作业指导书-钱锦华-A版",
						type: 'pdf',
						id: '1729408250193686529'
					},
					{
						name: "03-ACR测试规范作业指导书--谢梦茹--A版--2020.10",
						type: 'pdf',
						id: "1729408412165124098"
					},
					{
						name: "04-过充测试规范作业指导书--曹晶晶--A版--2020.10.28",
						type: 'pdf',
						id: "1729408458503794690"
					},
					{
						name: "05-浓硫酸浸泡铜丝作业指导书-周永兵-A版-2020.2.14",
						type: 'pdf',
						id: "1729408515869290498"
					},
					{
						name: "06-绝缘阻值测试作业指导书-杜金田--C版--2020.12",
						type: 'pdf',
						id: "1729408566293213185"
					},
					{
						name: "07-电芯上柜测试SOP-曾盼盼-B版-2021.3.30",
						type: 'pdf',
						id: "1729408644751863810"
					},
					{
						name: "08-三元材料扣电制作及测试作业指导书-B-冯东东",
						type: 'xlsx',
						id: "1729408726201053185"
					},
					{
						name: "09-石墨材料扣电制作及测试作业指导书-B-冯东东",
						type: 'xlsx',
						id: "1729408797453889537"
					},
					{
						name: "10-软包电芯拆解作业指导书-B-闫俊杰-2021.3.16",
						type: 'pdf',
						id: "1729408845768077313"
					},
					{
						name: "11-极片液相电阻测试作业指导书-A-蒋懋-2021.3.16",
						type: 'pdf',
						id: "1729408911534764034"
					},
					{
						name: "13-软包电池焊接金相作业指导书-闫俊杰-A-2021.4.30",
						type: 'pdf',
						id: "1729409103122182145"
					},
					{
						name: "16-大圆柱电芯上柜规范作业指导书-A-邓国欢-2021.08.24 (2)",
						type: 'pdf',
						id: "1729409218490707969"
					},
					{
						name: "17-E23电芯规范拍照作业指导书-A1-肖曾元-20210827",
						type: 'pdf',
						id: "1729409355984187394"
					},
					{
						name: "19-EV圆柱电芯拆解作业指导书-A-王震-2021.9.15",
						type: 'pdf',
						id: "1729409433264238594"
					},
					{
						name: "20-低阻值大电流多段可调电阻箱测规范作业指导书 -A-张诵权-2021.9.16",
						type: 'xlsx',
						id: "1729409507402756097"
					},
					{
						name: "21-电阻焊接机规范作业指导书 -A-张诵权-2021.9.16",
						type: 'xlsx',
						id: "1729409582786981889"
					},
					{
						name: "22-对称电池法拉第阻抗测试作业指导书-A-袁超平-2021.10.11",
						type: 'pdf',
						id: "1729409705411653634"
					},
					{
						name: "23-精密金相切割机作业指导书 - 20211105-A",
						type: 'xlsx',
						id: "1729409777985695746"
					},
					{
						name: "24-金相磨抛机作业指导书 - 20211105-A",
						type: 'xlsx',
						id: "1729409837381234690"
					},
					{
						name: "25-真空镶嵌机作业指导书 - 20211105-A",
						type: 'xlsx',
						id: "1729409894138556417"
					},
					{
						name: "26-壳体承压测试机SOP-2021.11.25",
						type: 'xlsx',
						id: "1729409932625489921"
					},
					{
						name: "27-对称电池制作作业指导书",
						type: 'xlsx',
						id: "1729409962765758466"
					}
				],
				sopArr: [],


				dataArr: [
					//type 0: 数据  1: 线
					{
						num: '--',
						type: 0,
						detail: "今日截止",
						icon: "exclamation",
						color: "#ffb794",
						borderColor: "rgba(255, 183, 148,0.15)"
					},
					{
						type: 1
					},
					{
						num: '--',
						type: 0,
						detail: "今日任务",
						icon: "check",
						color: "#34E09E",
						borderColor: "rgba(52,224,158,0.15)"
					},
					{
						type: 1
					},
					{
						num: '--',
						type: 0,
						detail: "待办清单",
						icon: "menu",
						color: "#66ADF9",
						borderColor: "rgba(102,173,249,0.15)"
					},
					{
						type: 1
					},
					{
						num: '--',
						type: 0,
						detail: "逾期任务",
						icon: "close",
						color: "#F2AE00",
						borderColor: "rgba(242,174,0,0.15)"
					},
					{
						type: 1
					},
					{
						num: '--',
						type: 0,
						detail: "任务总览",
						icon: "clock-circle",
						color: "#8382F5",
						borderColor: "rgba(131,130,245,0.15)"
					}
				],
				tableData: [],
				testerList: [],
				originalData: [],
				columns: [
					// {
					// 	title: "优先级",
					// 	dataIndex: "priority"
					// },
					{
						title: "序号",
						dataIndex: "index",
						align: "center",
            width:50,
						customRender: (text, record, index) => `${index + 1}`
					},
					{
						title: "项目名称",
						align: "center",
						dataIndex: "testName",
            width:100,
            ellipsis:true,
						scopedSlots: {
							customRender: "testName"
						}
					},
					{
						title: "委托单号",
						align: "center",
						dataIndex: "folderNo",
            width:100,
					},
					// {
					// 	title: "实验室",
					// 	dataIndex: "taskType",
					// 	align: "center",
					// 	scopedSlots: {
					// 		customRender: "taskType"
					// 	}
					// },

					{
						title: "委托人",
						align: "center",
						dataIndex: "wtrName",
            ellipsis:true,
            width:80,
					},
					// {
					// 	title: "测试员",
					// 	align: "center",
					// 	dataIndex: "tester",
					// 	filters: [],
					// 	filterMode: 'tree',
					// 	filterSearch: true,
					// 	onFilter: (value, record) => record.tester == value + "",
					// },
					{
						title: "测试员",
						align: "center",
						dataIndex: "tester",
            ellipsis:true,
						scopedSlots: {
							filterDropdown: "testerFilterDropdown",
							filterIcon: "testerFilterIcon",
						},
            width:80,
					},
					{
						title: "计划开始日期",
						align: "center",
						dataIndex: "planStartTime",
						customRender: (text, record, index) => {
							return text.split(" ")[0]
						},
            width:100,
					},
					{
						title: "计划结束日期",
						align: "center",
						dataIndex: "planEndTime",
						customRender: (text, record, index) => {
							return text.split(" ")[0]
						},
						scopedSlots: {
							filterDropdown: "filterDropdown",
							filterIcon: "filterIcon",
						},
            width:120,
					},
					{
						title: "任务状态",
						align: "center",
						dataIndex: "taskStatus",
						scopedSlots: {
							customRender: "taskStatus"
						},
            width:80,
					},
					// {
					// 	title: "备注",
					// 	align: "center",
					// 	dataIndex: "remark",
					// 	customRender: text => text || "-"
					// },

					// {
					// 	title: "计划进度",
					// 	key: "action4"
					// },
					{
						title: "操作",
						align: "center",
						dataIndex: "action",
						scopedSlots: {
							customRender: "action"
						},
            width:150,
					}
				],
			}
		},
		components: {
			DetailsModal,
			FileModal,
			CalendarAroundModal,
			calendarMiddleModal,
      BeforeAfterTestModal,
      TempCycleTestModal,
			ScheduleModal,
			AlterTesterModal,
			AlterPlanTimeModal,
			AlterYfTesterModal,
			workTransfer,
			FailureCellAddOcvModal,
			pbiTabs
		},

		watch: {
			tableData(newVal, oldVal) {
				this.handleHeight()
			}
		},

		created() {
			if (this.userInfo.account === "superAdmin") {
				this.isShowAlterButton = true
			} else {
				// "研发检测中心-测试组长"才能看到修改测试员按钮
				let yfList = this.userInfo.roles.filter(item => item.id === "1676772241413427202")
        // "第六实验室(JM)-测试组长"才能看到修改测试员按钮
        let jmcsList = this.userInfo.roles.filter(item => item.id === "1839274430373945345")
				// "第零实验室-测试组长"和"第零实验室-计划管理员"角色才能看到修改测试员/计划时间按钮
				let jmList = this.userInfo.roles.filter(item => item.id === "1712686842365419522" || item.id === "1720008384229163010")
				// "第四实验室-测试组长"角色才能看到修改测试员/计划时间按钮
				let aqList = this.userInfo.roles.filter(item => item.id === "1754070159908036609")
        // "第四实验室-日历寿命测试组长"才能看到修改测试员按钮
        let aqrlList = this.userInfo.roles.filter(item => item.id === "********48870113281")
				if (jmList.length > 0) {
					this.isShowAlterButton = true
				} else if (aqList.length > 0) {
					this.isShowAlterButton = true
				} else if (yfList.length > 0) {
					this.isShowAlterButton = true
        } else if (jmcsList.length > 0) {
          this.isShowAlterButton = true
				} else if (aqrlList.length > 0) {
          this.isShowAlterButton = true
        }
			}
			this.sopArr = this.sopOriginal.map(v => {
				return v
			})
			this.getTodoTaskList(1)

			this.now = formatDate(new Date())
			this.tips = timeFix()
			setInterval(() => {
				this.now = formatDate(new Date())
			}, 60000)
		},
		computed: {
			...mapGetters(['userInfo'])
		},
		mounted() {
			this.getTodoTaskStatistics()

			this.handleHeight()

			//窗口尺寸改变
			window.addEventListener("resize", () => {
				this.handleHeight()
			})
		},
		methods: {
			getTodoTaskStatistics(){
				getTodoTaskStatistics().then(res => {
					if (!res.success) return this.$message.error("错误提示：" + err.message)
					this.dataArr[0].num = res.data.dueTodayTodoNum
					this.dataArr[2].num = res.data.todayTodoNum
					this.dataArr[4].num = res.data.allTodoNum
					this.dataArr[6].num = res.data.delayTodoNum
					this.dataArr[8].num = res.data.allNum
				})
			},
			getTodoTaskList(isThShow = 0) {
				this.tableLoading = true

				// searchType   1:今日任务,2:待办清单,3:逾期任务,4:任务总览,5:今日截止      初次进入展示'今日截止  5'
				const params = {
					pageNo: this.storeList[this.arrName[this.activeKey]] ? this.storeList[this.arrName[this.activeKey]].pagination.pageNo : 1,
					pageSize:this.storeList[this.arrName[this.activeKey]] ? this.storeList[this.arrName[this.activeKey]].pagination.pageSize : 10,
					searchValue:this.searchValue,
          searchTesterValue:this.searchTesterValue,
					searchType:this.activeKey == 0 ? 5 : this.activeKey,
				}

				getTodoTaskList(params)
				.then(async res => {
					if (!res.success) return this.$message.error("错误提示：" + err.message)

					if(!this.storeList[this.arrName[this.activeKey]]){
						this.storeList[this.arrName[this.activeKey]] = {
							pagination:{
								pageNo:1,
								pageSize:10,
								total:res.data.totalRows
							}
						}
					}

					// 初次进入，如果今日截止无数据，则展示今日任务

          if(this.isFirst) {
            await this.getTesterList()
          }
					if(this.isFirst && res.data.rows.length === 0){
						this.activeKey = 1
						this.isFirst = false

						// 赋值测试员列表(只需要拿一次)
						return this.getTodoTaskList(1)
					}

					this.tableData = res.data.rows
					this.pagination.total = res.data.totalRows
					this.pagination.current = this.storeList[this.arrName[this.activeKey]].pagination.pageNo
					this.pagination.pageSize = this.storeList[this.arrName[this.activeKey]].pagination.pageSize



					// // 如果有做表头时间筛选
					// if (this.filterData) {
					// 	this.tableData = this.tableData.filter(item => this.filterData == item.planEndTime.split(" ")[0])
					// }


					// jmcs 精密（第0实验室）  aqcs 安全（第四实验室）  rlsmcs_ 日历（第六实验室【惠州】）
					const isShow =
							(JSON.stringify(res.data).indexOf("jmcs") !== -1 || JSON.stringify(res.data).indexOf("aqcs") !== -1)
								&& JSON.stringify(res.data).indexOf("rlsmcs_") === -1
								? false
								: true

					if (isThShow && isShow) {
						this.columns.splice(
							this.columns.length - 1,
							0,
							{
								title: "数量",
								align: "center",
								customRender: text => text || "-",
								dataIndex: "sampleNum",
                width: 80
							},
              {
                title: "在测数量",
                align: "center",
                customRender: text => text || "-",
                dataIndex: "testingQuantity",
                width: 80
              },
							{
								title: "存储位置",
								align: "center",
                ellipsis:true,
								customRender: text => text || "-",
								dataIndex: "inBoxPosition",
                width: 80
							},
							{
								title: "产品名称",
								align: "center",
                ellipsis:true,
								customRender: text => text || "-",
								dataIndex: "productName",
                width: 80
							},
							{
								title: "测试类型",
								align: "center",
                ellipsis:true,
								customRender: text => text || "-",
								dataIndex: "testType",
                width: 80
							}
						)
					} else if (isThShow && JSON.stringify(res.data).indexOf("jmcs") !== -1) {
						// 包含精密实验室的数据，需展示数量列
						this.columns.splice(
							this.columns.length - 1,
							0,
							{
								title: "数量",
								align: "center",
								customRender: text => text || "-",
								dataIndex: "sampleNum",
                width: 80
							}
						)
					}
          if ((JSON.stringify(res.data).indexOf("safety_ba_test") !== -1 || JSON.stringify(res.data).indexOf("safety_tc_test") !== -1) &&
            this.columns.findIndex(v => v.title === '样品编号') === -1) {
            // 包含第四实验室外部短路的数据，需展示样品编号列
            this.columns.splice(
              3,
              0,
              {
                title: "样品编号",
                align: "center",
                width: 100,
                ellipsis: true,
                scopedSlots: {
                  customRender: "orderNos"
                },
                dataIndex: "orderNos"
              }
            )
            if (JSON.stringify(res.data).indexOf("rlsmcs") === -1) {
              this.columns = this.columns.filter(column => column.dataIndex !== "inBoxPosition" && column.dataIndex !== "testingQuantity")
            }
          } else if ((JSON.stringify(res.data).indexOf("safety_ba_test") === -1 && JSON.stringify(res.data).indexOf("safety_tc_test") === -1) &&
            this.columns.findIndex(v => v.title === '样品编号') !== -1) {
            // 不是第四实验室的待办无需展示样品编号列
            this.columns = this.columns.filter(column => column.dataIndex !== "orderNos")
          }

          if (JSON.stringify(res.data).indexOf("jmcs") !== -1) {
            // 含精密实验室的数据，需添加参与人列
            if (this.columns.findIndex(column => column.dataIndex === "participator") === -1) {
              this.columns.splice(5, 0,
                {
                  title: "参与人",
                  align: "center",
                  ellipsis:true,
                  dataIndex: "participator",
                  customRender: text => text || "-",
                  width: 80
                })
            }
          } else {
            // 不包含精密实验室的数据，无需展示参与人列
            this.columns = this.columns.filter(column => column.dataIndex !== "participator")
            // 只有日历寿命的数据时，无需数量列
            if (JSON.stringify(res.data).indexOf("rlsmcs") !== -1 &&
              (JSON.stringify(res.data).indexOf("safety_ba_test") === -1 && JSON.stringify(res.data).indexOf("safety_tc_test") === -1)) {
              this.columns = this.columns.filter(column => column.dataIndex !== "sampleNum")
            }
          }

				}).finally(() => {
					this.tableLoading = false
					this.isFirst = false
				})
			},
			getTesterList() {
				getTesterList({ searchType: this.activeKey === 0 ? 5 : this.activeKey	}).then(res => {
					if (!res.success) return this.$message.error("错误提示：" + res.message)
					// this.columns[4].filters = res.data.map(mapItem => { return { label:mapItem,value:mapItem } })
					this.testerUserList = res.data
					this.testerUserAllList = JSON.parse(JSON.stringify(res.data))   //存一份全部的
				}).catch(err => {
					// 处理网络请求错误或其他异常
					this.$message.error("网络请求失败：" + err.message);
				});
			},

			// 电压内阻测试仪
			// 串口连接
			async handleOpenConnect() {
				try {
					this.serialObj.serialPort = await navigator.serial.requestPort();
					await this.serialObj.serialPort.open({
						baudRate: 9600,
						dataBits: 8,
						stopBits: 1,
						parity: 'none'
					})

					this.serialObj.serialReader = this.serialObj.serialPort.readable.getReader();
					this.serialObj.serialWriter = this.serialObj.serialPort.writable.getWriter();

					this.serialObj.serialPortOpen = true
					this.$notification.success({
						message: '设备连接成功',
					});
				} catch (error) {
					this.$notification.error({
						message: '用户取消连接/设备出错',
					});
				}
			},
			// 串口断开
			async handleCloseConnect() {
				try {
					this.serialObj.serialReader.releaseLock();
					this.serialObj.serialWriter.releaseLock();
					await this.serialObj.serialPort.close();
					this.serialObj.serialPortOpen = false
					this.$notification.success({
						message: '设备连接已断开',
					});
				} catch (error) {
					this.$notification.error({
						message: '连接断开失败',
					});
				}
			},
			cancel() { },

			// 获取详情
			async getLimsResultByOrdTaskId(id) {
				let result = 0
				let length = 0
				await getLimsResultByOrdTaskId({ ordtaskid: id }).then(res => {
					if (!res.success) return this.$message.error("错误提示：" + err.message)
					length = res.data.length
					if (res.data.length > 0) {
						res.data.forEach(v => {
							if (v.originalresult !== null) result++
						})
					}
				})
				// 如果全部填写完成，且长度不为零
				return [result, length]
			},
			// 获取附件列表详情
			async getFileList(id) {
				let temLength = 0
				await getFileList(id).then(res => {
					temLength = res.length
				})
				return temLength > 0
			},
			// 获取数据是否填写完成
			async getTestProDetailByTaskId(record) {
				let result = 0
				let poinResult = 0
				await getTestProDetailByTaskId(record.ordTaskId).then(res => {
					if (!res.success) return this.$message.error("错误提示：" + res.message)
					// 不是最后一阶段 && 实际进箱时间为空 && 至少有一个电芯状态为ongoing
					if (record.taskType.indexOf("last") === -1 && (res.data.actualInDate === null || res.data.actualInDate === "") &&
						res.data.lifeTestRecordDataMap.findIndex(v => v.batteryStatus === 'ongoing') !== -1) {
						return result++
					}
					// 不是初始阶段 && 实际出箱时间为空 && 至少有一个电芯状态为ongoing
					if (record.taskType.indexOf("first") === -1 && (res.data.actualOutDate === null || res.data.actualOutDate === "") &&
						res.data.lifeTestRecordDataMap.findIndex(v => v.batteryStatus === 'ongoing') !== -1) {
						return result++
					}
          // 第四实验室日历寿命勾选了过程视频的必填
          if (record.videoFlag === "1") {
            if ((!res.data.videoId || !res.data.videoName) && res.data.lifeTestRecordDataMap.findIndex(v => v.batteryStatus === 'ongoing') !== -1) {
              return result++
            }
          }
          // 第四实验室日历寿命勾选了照片的，每个电芯只要有一张照片即可
          if (record.pictureFlag === "1") {
            var allSamplePictureList = [];
            res.data.lifeTestRecordDataMap.forEach(item => {
              const keys = Object.keys(item.samplePicture);
              var samplePictureList = [];
              for (const index in keys) {
                let picture = item.samplePicture[keys[index]]
                if (item.batteryStatus === 'ongoing') {
                  samplePictureList.push(picture);
                }
              }
              if (item.batteryStatus === 'ongoing') {
                allSamplePictureList.push(samplePictureList);
              }
            })
            for (const i in allSamplePictureList) {
              if (allSamplePictureList[i].findIndex(item => item.id && item.name) === -1) {
                return result++
              }
            }
          }
					// 去除不需要的字段
					res.data.lifeTestRecordDataMap.forEach(v => {
						if (v.batteryStatus === 'ongoing') {
							Reflect.ownKeys(v).forEach(e => {
								if (e === "heightType" || e === "timeOfFillInnerres" || e === "timeOfFillInnerres2" || e === "checkData" || e === "samplePicture") return
								if (e.replaceAll(/[^0-9]/g, "") > 1) {
									if (v[e] === null || v[e] === "") poinResult++
									return
								}
								if (v[e] === null || v[e] === "") result++
								if (e === "isMiddleClick" && v[e] === false) result++
							})
						}
					})
				})
				// 等于0 说明没有空数据
				return [result === 0, poinResult === 0]
			},
			// 获取数据是否填写完成-阶段式日历寿命测试
			async getTestProDetailByTaskIdOfStage(record) {
				let result = 0
				let poinResult = 0
				await getTestProDetailByTaskId(record.ordTaskId).then(res => {
					if (!res.success) return this.$message.error("错误提示：" + res.message)
					// 是初始阶段 && 实际进箱时间为空 && 至少有一个电芯状态为ongoing
					if (record.taskType.indexOf("first") !== -1 && (res.data.actualInDate === null || res.data.actualInDate === "") &&
						res.data.lifeTestRecordDataMap.findIndex(v => v.batteryStatus === 'ongoing') !== -1) {
						return result++
					}
					// 不是初始阶段 && 实际出箱时间为空 && 至少有一个电芯状态为ongoing
					if (record.taskType.indexOf("first") === -1 && (res.data.actualOutDate === null || res.data.actualOutDate === "") &&
						res.data.lifeTestRecordDataMap.findIndex(v => v.batteryStatus === 'ongoing') !== -1) {
						return result++
					}
					// 去除不需要的字段
					res.data.lifeTestRecordDataMap.forEach(v => {
						if (v.batteryStatus === 'ongoing') {
							Reflect.ownKeys(v).forEach(e => {
								if (e === "heightType" || e === "timeOfFillInnerres" || e === "timeOfFillInnerres2" || e === "checkData" || e === "samplePicture") return
								if (e.replaceAll(/[^0-9]/g, "") > 1) {
									if (v[e] === null || v[e] === "") poinResult++
									return
								}
								if (v[e] === null || v[e] === "") result++
								if (e === "isMiddleClick" && v[e] === false) result++
							})
						}
					})
				})
				// 等于0 说明没有空数据
				return [result === 0, poinResult === 0]
			},
			// 判断转移温箱数据是否填写完整,untransferBoxCount等于0说明没有空数据
			async validTransferWarmBoxData(record) {
				let untransferBoxCount = 0
				await getTestProDetailByTaskId(record.ordTaskId).then(res => {
					if (res.data.transferBoxFlag !== 1 && res.data.transferBoxFlag !== 2) {
						untransferBoxCount = 0
					} else {
						let transferBoxInfoData = JSON.parse(res.data.transferBoxInfo)
						transferBoxInfoData.forEach(v => {
							if (v.batteryStatus === 'ongoing') {
								Reflect.ownKeys(v).forEach(e => {
									if (v[e] === null || v[e] === "") {
										untransferBoxCount++
									}
								})
							}
						})
					}
				})
				return untransferBoxCount
			},
			handleTesterFilter(){
				this.testerUserList = JSON.parse(JSON.stringify(this.testerUserAllList)).filter(filterItem => filterItem.indexOf(this.testerUserName) !== -1)
			},
			handleChangeTester(value){
        this.searchTesterValue = value.join(',')
        this.getTodoTaskList()
			},
			// 搜索框事件
			handleInputSearch(value) {
				this.searchValue = value.trim()

				// 重置页数
				this.storeList[this.arrName[this.activeKey]].pagination.pageNo = 1
				this.storeList[this.arrName[this.activeKey]].pagination.pageSize = 10

				this.getTodoTaskList()
			},
			handleTabsChange(value) {
				this.activeKey = value
				this.filterData = ''

				this.getTodoTaskList()
			},
			// 详情事件
			handleDetail(record) {
				this.modalData = record
				if (record.taskType.indexOf("first") !== -1 || record.taskType.indexOf("last") !== -1)
					return (this.isShowAroundModal = true)  //  日历寿命初始或者最后阶段的测试结果录入
				if (record.taskType.indexOf("middle") !== -1) return (this.isShowMiddleModal = true)  //  日历寿命中间阶段的测试结果录入
				if (record.taskType.indexOf("failureCell_ocv") !== -1) {
					this.$refs.addOcv.add();
					return;
				}
        if (record.taskType.indexOf("safety_ba_test") !== -1) return (this.isShowBaTestModal = true)
        if (record.taskType.indexOf("safety_tc_test") !== -1) return (this.isShowTcTestModal = true)
				this.isShowModal = true  // 精密实验室的测试结果录入
			},
			// 附件管理事件
			handleFile(record) {
				this.modalData = record
				this.isShowFileMOdal = true
			},
			// 计划表
			handleSchedule(record) {
				this.modalData = record
				this.isShowScheduleModal = true
			},
			// 更改测试员
			handleAlterTester(record) {
				this.modalData = record
				this.isShowAlterTesterModal = true
			},
			handleYfAlterTester(record) {
				this.modalData = record
				this.isShowAlterYfTesterModal = true
			},
			// 更改计划时间
			handleAlterPlanTime(record) {
				this.modalData = record
				this.isShowAlterPlanTimeModal = true
			},
			// 完成按钮事件
			handleOk(record) {
				if (record.testName === "氩离子截面抛光" || record.testName === "清洁度萃取") {
					const params = { folderNo: record.folderNo, ordTaskId: record.ordTaskId, todoTaskId: record.id }
					InputDataSubmit(params).then(res => {
						if (!res.success) {
							this.$message.error("错误提示：" + res.message)
							return
						}
						this.$message.success("提交成功")
						this.getTodoTaskList()
					})
					return
				}
				// 精密实验室
				if (record.taskType.indexOf("jmcs") !== -1) {
					return this._handleOkJM(record)
				} else if (record.taskType.indexOf("aqcs") !== -1) {
					// 安全检测室
					return this._handleOkAQ(record)
				} else if (record.taskType.indexOf("failureCell_ocv") !== -1) {
					// DPV测试失效电芯OCV测试完成
					return this._handleOkAQ(record)
				} else {
					if (record.stageFlag === 1) {
						// 阶段式日历寿命测试
						this._handleOkRLOfStage(record)
					} else {
						// 普通日历寿命测试
						this._handleOkRL(record)
					}
				}
			},
			async _handleOkAQ(record) {
				const tem = await this.getLimsResultByOrdTaskId(record.ordTaskId)
				// 无详情情况（无详情，有附件）
				if ((tem[0] === tem[1] && tem[1] !== 0) || (tem[1] === 0 && (await this.getFileList(record.ordTaskId)))) {
					const params = { folderNo: record.folderNo, ordTaskId: record.ordTaskId, todoTaskId: record.id }
					InputDataSubmit(params).then(res => {
						if (!res.success) {
							this.$message.error("错误提示：" + res.message)
							return
						}
						this.$message.success("提交成功")
						this.getTodoTaskList()
					})
				} else {
					this.$message.warning("请完整填写或上传信息")
				}
			},
			async _handleOkJM(record) {
				const tem = await this.getLimsResultByOrdTaskId(record.ordTaskId)
				// 无详情情况（无详情，有附件）
				if ((tem[0] === tem[1] && tem[1] !== 0) || (tem[1] === 0 && (await this.getFileList(record.ordTaskId)))) {
					const params = { folderNo: record.folderNo, ordTaskId: record.ordTaskId, todoTaskId: record.id }
					InputDataSubmit(params).then(res => {
						if (!res.success) {
							this.$message.error("错误提示：" + res.message)
							return
						}
						this.$message.success("提交成功")
						this.getTodoTaskList()
					})
				} else {
					this.$message.warning("请完整填写或上传信息")
				}
			},
			async _handleOkRLOfStage(record) {
				if ((await this.getTestProDetailByTaskIdOfStage(record))[0] && !(await this.getTestProDetailByTaskIdOfStage(record))[1]) {
					this.$message.warning("请先将尺寸数据填写完整")
				} else if ((await this.getTestProDetailByTaskIdOfStage(record))[0]) {
					let untransferBoxCount = (await this.validTransferWarmBoxData(record))
					if (untransferBoxCount === 0) {
						finishCalLifeTodoTask({ id: record.ordTaskId }).then(res => {
							if (!res.success) {
								this.$message.error("错误提示：" + res.message)
								return
							}
							this.$message.success("提交成功")
							this.getTodoTaskList()
						})
					} else {
						this.$message.warning("完成待办任务前请先将电芯转移")
					}
				} else {
					this.$message.warning("请完整填写数据")
				}
			},
			async _handleOkRL(record) {
				if ((await this.getTestProDetailByTaskId(record))[0] && !(await this.getTestProDetailByTaskId(record))[1]) {
					this.$message.warning("请先将尺寸数据填写完整")
				} else if ((await this.getTestProDetailByTaskId(record))[0]) {
					finishCalLifeTodoTask({ id: record.ordTaskId }).then(res => {
						if (!res.success) {
							this.$message.error("错误提示：" + res.message)
							return
						}
						this.$message.success("提交成功")
						this.getTodoTaskList()
					})
				} else {
					this.$message.warning("请完整填写数据")
				}
			},
			/**
			 * 弹窗事件
			 */
			handleCancel() {
				this.$message.success("取消成功")
			},
			handleModalCancel() {
				this.getTodoTaskList()

				this.isShowModal = false
				this.isShowFileMOdal = false
				this.isShowMiddleModal = false
				this.isShowBaTestModal = false
				this.isShowTcTestModal = false
				this.isShowAroundModal = false
				this.isShowScheduleModal = false
				this.isShowAlterTesterModal = false
				this.isShowAlterYfTesterModal = false
				this.isShowAlterPlanTimeModal = false
			},
			handleModalSubmit() {
				this.isShowMiddleModal = false
				this.isShowAroundModal = false

				this.getTodoTaskList()
			},
      handleBaTestSubmit() {
        this.isShowBaTestModal = false
        this.getTodoTaskList()
      },
      handleTcTestSubmit() {
        this.isShowTcTestModal = false
        this.getTodoTaskList()
      },
			handleHeight() {
				this.tableHeight =
				document.body.clientHeight - document.getElementById("head").clientHeight - 40 - 16 * 2 - 12 - 40 - 10

				document.documentElement.style.setProperty(`--height`, `${this.tableHeight - 24.5 - 15}px`)

				if (this.tableData.length === 0) {
					document.documentElement.style.setProperty(`--height`, `${this.tableHeight}px`)
				}
			},
			handleSopFile(file) {
				if (file.type === 'pdf') {
					this.sopUrl = '/api/sysFileInfo/previewPdf?Authorization=Bearer ' + Vue.ls.get('Access-Token') + '&id=' + file.id + '#toolbar=0'
					this.drawerVisible = true
				} else {
					const a = document.createElement('a')
					a.style.display = 'none'
					a.href = '/api/sysFileInfo/previewPdf?Authorization=Bearer ' + Vue.ls.get('Access-Token') + '&id=' + file.id + '#toolbar=0'
					a.download = file.name
					a.click()
				}

			},
			handleFileSearch(value) {
				this.sopArr = []
				this.sopOriginal.forEach(v => {
					if (v.name.indexOf(value) !== -1) {
						this.sopArr.push(v)
					}
				})
			},
			handleDataChange(date, dateString) {
				this.filterData = dateString
			},

			// 表头日历筛选
			handleCalendarSearch(selectedKeys, confirm, dataIndex) {
        confirm()
        this.searchValue = this.filterData
				this.getTodoTaskList()
			},
			// 表头日历重置
			handleCalendarReset(clearFilters) {
				clearFilters();
				this.filterData = ''
        this.searchValue = ''
				this.getTodoTaskList()
			},
			colorRender(text) {
				if (text === '已完成') {
					return 'green'
				} else if (text === '待处理') {
					return 'orange'
				} else if (text === '已退回') {
					return 'red'
				}
			}
		}
	}
</script>

<style lang="less" scoped>
	:root {
		--height: 600px;
	}

	p {
		margin: 0;
	}

	.container {
		display: flex;
		flex-direction: column;
		height: 100%;
		color: #333333;
		font-size: 16px;
		font-weight: 400;
		line-height: 1;
		padding: 16px 16px 14px;
	}

	// 标题部分
	.head-wrapper {
		display: flex;
		justify-content: space-between;
		height: 68px;
		margin-right: 50px;
	}

	.salutation {
		margin-right: 35px;
	}

	.salutation p:first-child {
		font-size: 35px;
		font-weight: 500;
		margin-bottom: 10px;
	}

	.salutation p:last-child {
		color: #666;
	}

	.head-wrapper .circle-block {
		display: flex;
		justify-content: space-around;
		align-items: center;
	}

	.circle-block .data-packet {
		display: flex;
		align-items: center;
	}

	.circle-block .icon {
		width: 60px;
		height: 60px;
		font-size: 33px;
		color: #fff;
		border-radius: 50%;
		margin-right: 10px;
		box-sizing: content-box;
		background-clip: padding-box !important;

		display: flex;
		justify-content: center;
		align-items: center;
		flex-shrink: 0;
	}

	.circle-block .detail p:first-child {
		font-size: 25px;
		font-weight: 500;
		margin-bottom: 5px;
	}

	.circle-block .detail p:last-child {
		color: #666;
	}

	.circle-block .line {
		margin: 0 20px;
		width: 0;
		height: 55px;
		border: 1px solid #bfbaba;
	}

	//内容部分
	.content-wrapper {
		display: flex;
		flex: 1;
		margin-top: 12px;
	}

	.content-wrapper .left {
		margin-right: 14px;
		width: 75%;
		height: 100%;
		position: relative;
	}

	.left .options {
		display: flex;
		position: absolute;
		top: 0;
		right: 0;
		z-index: 999;
	}

	.left .tab {
		padding: 5px;
		background-color: #fff;
		/* box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.25); */
	}

	.left .icon-btn {
		font-size: 25px;
		margin: auto;
		cursor: pointer;
	}

	// 内容--右边
	.content-wrapper .right {
		height: 100%;
		width: 25%;
	}

	.right .block {
		height: calc((100vh - 40px - 16px - 16px - 68px - 12px - 12px) / 2);
		background-color: #fff;
		border-radius: 10px;
		/* box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.25); */
		position: relative;
	}

	.block .title {
		padding: 12px 0;
		position: sticky;
		top: 0;
	}

	.block .title img {
		width: 20px;
		height: 35px;
		position: absolute;
		top: 0;
		left: 13px;
	}

	.block .title .text {
		display: block;
		line-height: 1;
		margin-left: 40px;
	}

	/* sop */
	.block .title .sop-block {
		margin: 0 20px 0 40px;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.block .empty-block {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}

	.block .box {
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		overflow: auto;
		box-sizing: border-box;
	}

	.block .detail {
		margin-left: 10px;
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-start;
		height: calc(100% - 1.0156vw - 1.25vw);
	}

	.block .detail .detail-block {
		display: flex;
		flex: 1;
		height: 60px;
		width: 50px;
		max-width: 50px;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		font-size: 12px;
		margin: 0 10px 10px 0;
	}

	.block .detail .detail-block img {
		width: 40px;
		height: 40px;
		margin-bottom: 4px;
		border-radius: 50%;
	}

	.block .row-content {
		display: block;
		height: 100%;
		height: calc(100% - 24px - 32px - 8px);
		padding: 0 20px;
		overflow: auto;
	}

	.block .row-content .row {
		width: 100%;
		font-size: 12px;
		padding-bottom: 10px;
		border-bottom: 1px solid #bfbaba;
		margin-top: 15px;
	}

	.block .row-content .row:hover {
		color: #1890ff;
	}

	// 通用
	.mb12 {
		margin-bottom: 12px;
	}

	.mr0 {
		margin-right: 0 !important;
	}

	.mr8 {
		margin-right: 8px;
	}

	.mr10 {
		margin-right: 10px;
	}

	.oneline {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
	/* 表头筛选 */
	.tester-filter-dropdown{
		width: 134px;
		max-height: 220px;
		overflow-y: scroll;
		padding: 0 7px;
	}
	.tester-filter-input{
		width: 120px;
		position: sticky;
		top: 0;
		background-color: #fff;
		z-index: 1;
		padding: 7px 0;
	}

	/* 表头筛选 */

	.filter-dropdown .search-btns {
		width: 60px;
		height: 21px;
	}

	.filter-dropdown .btn-wrapper {
		border-top: 1px solid #e8e8e8;
		margin-top: 7px;
		display: flex;
		justify-content: space-around;
		align-items: center;
		font-size: 14px;
	}


	/deep/ #outTable .ant-table-thead>tr>th {
		font-size: 13px;
    padding: 5px;
    color: rgba(0, 0, 0, .85);
    font-weight: 500;
	}

	/deep/#outTable .ant-table-tbody>tr>td {
    padding: 0px;
    color: #333;
    font-size: 12px;
    font-weight: 400;
	}


  /deep/.ant-table-thead>tr>th {
    line-height: 1;
    font-size: 13px;
    font-weight: 500;
    padding: 8px;
    height: 32px !important;
  }

  /deep/.ant-table-tbody>tr>td {
    font-size: 12px;
    font-weight: 400;
    padding: 8px;
  }

	// table
	/deep/.ant-table-body {
		height: var(--height);
		overflow-y: scroll;
	}

	/deep/.ant-table-thead {
		position: sticky;
		top: 0;
		z-index: 1;
	}

	/deep/.ant-table-placeholder {
		border: none;
		position:absolute;
		top:50%;
		left:50%;
		transform:translate(-50%, -50%);
	}
	/deep/.ant-pagination.mini .ant-pagination-options-quick-jumper{
		height: 32px;
		line-height: 32px;
	}
	/deep/.ant-table-pagination.ant-pagination{
		margin: 8px 0;
	}

	/* 右侧：搜索框 + 按钮 */
	/deep/.options .ant-input-affix-wrapper .ant-input {
		height: 32px;
		width: 250px;
	}

	/deep/ .ant-btn-sm {
		height: 32px;
	}

	// table btn
	/deep/.btn-slot .ant-btn {
		padding: 0 5px;
	}

	/deep/.btn-slot .ant-btn span {
		font-size: 12px;
	}


	/deep/.pbi-tabs{
		font-size: 14px;
	}
	/deep/.pbi-tab-item{
		padding: 12px 16px;
	}
	/deep/.pbi-tab-active{
		font-size: 15px;
	}

  /deep/.ant-table-thead > tr > th.ant-table-column-has-actions.ant-table-column-has-filters {
    padding-right: 20px !important;
  }

  /deep/.ant-btn-group .ant-btn {
    height: 28px;
    border: 0
  }
</style>