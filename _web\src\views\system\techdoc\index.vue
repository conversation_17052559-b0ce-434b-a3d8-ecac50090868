<template>
    <a-spin :spinning="loading" style="background:#fff">

        <a-modal title="新增电芯BOM" :width="800" :visible="selectvisible" @ok="handleSubmit" @cancel="handleCancel">
			<!-- <v-selectpage title="电芯BOM代码" placeholder="选择电芯BOM代码" ref="sp" v-model="sapNumber" :page-size="6" :data="parts" key-field="sapNumber" show-field="sapNumber" :tb-columns="vcolumns">
			</v-selectpage> -->
			<a-dropdown v-model="dropdownvisible" placement="bottomCenter" :trigger="['click']">
				<a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">{{sapNumber ? sapNumber+'-规格：['+sapDec+']' : '电芯BOM代码'}}<a-icon type="down" /></a-button>
				<a-menu slot="overlay">
						<a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:800px">
							<a-input-search v-model="queryParam.partNumber" placeholder="搜索电芯BOM代码" @change="onSearch"/>
								<s-table
										style="width:100%"
										ref="table"
										:rowKey="(record) => record.sapNumber"
										:columns="vcolumns"
										:data="loadData"
										:customRow="customRow"
										:scroll="{x: 900,y:200}"
										>
								</s-table>
						</a-spin>
				</a-menu>
			</a-dropdown>
            <a-input v-model="bomNo"  :style="{ marginTop: '12px' }" placeholder="电芯BOM编号" />
		</a-modal>
		<div style="text-align:right">
			<a-button v-if="hasPerm('sysBom:add')" type="primary" @click="addBom" style="margin-top:8px;margin-bottom:8px;margin-right:8px">
				新增电芯BOM
			</a-button>
		</div>
        
        <a-drawer placement="right" :closable="false" width="80%" :visible="visible1" @close="onClose1" :destroyOnClose="true">
            <checkhistory :param="param"></checkhistory>
        </a-drawer>
        <a-drawer :bodyStyle="{ height: '100%' }" placement="right" :closable="false" width="80%" :visible="visible2" @close="onClose2">
            <iframe :src="pdfUrl+'#view=FitH,top'" width="100%" height="100%"></iframe>
        </a-drawer>

        <!-- :pagination="false" :showPagination="false" -->
        <a-table :pagination="pagination" :rowKey="(record) => record.no" :columns="columns" :data-source="data" bordered size="small">
            <span slot="techStatus" slot-scope="text,record">
                      <a-dropdown  v-if="record.techStatus == -1">
                        <a @click="$refs.checkhistory2.edit(record)" >审核记录</a>
                      </a-dropdown>
                        <a-dropdown  v-if="record.techStatus != -1">
                                <a class="ant-dropdown-link">
                                    {{mapStatus[record.techStatus]}}<a-icon type="down" />
                                </a>
                                 <a-menu v-if="record.techStatus == 1" slot="overlay">
                                    <a-menu-item v-if="record.fileId !=null">
                                        <a @click="$refs.checkhistory2.edit(record)" >审核记录</a>
            						</a-menu-item>
            						<a-menu-item>
            							<a @click="$refs.techhistory.view(record)">历史记录</a>
            						</a-menu-item>
                        <a-menu-item>
            							<a v-if="hasPerm('docs:import')" @click="$refs.techdoc.docsImport(record)">导入，跳过审核</a>
            						</a-menu-item>
            					</a-menu>
                                <a-menu v-if="record.techStatus == 0 || record.techStatus == 4" slot="overlay">
                                    <a-menu-item>
                                        <a @click="$refs.techdoc.add(record)" >提交文件</a>
                                    </a-menu-item>
                                    <a-menu-item v-if="record.fileId !=null">
                                        <a @click="$refs.checkhistory2.edit(record)" >审核记录</a>
            						</a-menu-item>
            						<a-menu-item>
            							<a @click="$refs.techhistory.view(record)">历史记录</a>
            						</a-menu-item>
                                </a-menu>
                                <a-menu v-if="record.techStatus == 2" slot="overlay">
                                    <a-menu-item>
            							<a-popconfirm placement="topRight" title="确认修订？" @confirm="() => $refs.techdoc.add(record)">
            								<a>修订文件</a>
            							</a-popconfirm>
                                    </a-menu-item>
                                    <a-menu-item v-if="record.fileId !=null">
            							<a @click="$refs.checkhistory2.edit(record)" v-if="record.techType != 4">审核记录</a>
            						</a-menu-item>
            						<a-menu-item>
            							<a @click="$refs.techhistory.view(record)">历史记录</a>
            						</a-menu-item>
                                </a-menu>
            					<a-menu v-if="record.techStatus == 3" slot="overlay">
                                    <a-popconfirm placement="topRight" title="确认修订？" @confirm="() => $refs.techdoc.add(record)">
            								<a>修订文件</a>
            						</a-popconfirm>
            						<a-menu-item v-if="record.fileId !=null">
            							<a @click="$refs.checkhistory2.edit(record)" >审核记录</a>
            						</a-menu-item>
            						<a-menu-item>
            							<a @click="$refs.techhistory.view(record)">历史记录</a>
            						</a-menu-item>
            					</a-menu>
                        </a-dropdown>
                    </span>
                <a slot="fileName" slot-scope="text,record" @click="fileInfoDownload(record)" :title="text">{{text}}</a>
        </a-table>
        <techdoc ref="techdoc" @ok="handleOk" />
        <techhistory ref="techhistory" />
        <checkhistory2 ref="checkhistory2" @ok="handleOk" />
    </a-spin>
</template>

<script>
    import {
        sysFileInfoDownload
    } from '@/api/modular/system/fileManage'
    import checkhistory2 from '../projects/checkhistory2'
    import checkhistory from '../projects/checkhistory'
    import {
		sysAddBom,
	} from "@/api/modular/system/bomManage"
    const OtherComp = {
        name: "OtherComp",
        template: `
                    <div class="other-comp">
                        <div v-for='item in arr'>{{item}}</div>
                    </div>
                `,
        props: {
            arr: Array,
        },
    }
    import {
		getPartList
	} from "@/api/modular/system/partManage"

    import techdoc from './techdoc'
    import techhistory from './techhistory'
    import {
        getDocs
    } from "@/api/modular/system/docManage"
    import {
		STable
	} from '@/components'
    export default {
        components: {
            techdoc,
            techhistory,
            checkhistory2,
            checkhistory,
            STable
        },
        props: {
            issueId: {
                type: Number,
                default: 0
            }
        },
        data() {
            return {
                queryParam:{
				},
                selectvisible: false,
                dropdownvisible:false,
                sapNumber: '',
                sapDec:'',
                bomNo:'',
                vcolumns: [{
						title: '物料代码',
						dataIndex: 'sapNumber'
					},
					{
						title: '物料',
						dataIndex: 'partName'
					},
					{
						title: '物料规格',
						dataIndex: 'partDescription',
						width:700
					},
				],
                loadData: parameter => {
					parameter = { ...parameter,
						...{
							flag: 0
						}
					}
					return getPartList(Object.assign(parameter, this.queryParam)).then((res) => {
						return res.data
					})
				},
                pagination: {
                    current: 1,
                    pageSize: 12,
                    total: 0,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    pageSizeOptions: ["12", "18"],
                    onChange: (current, size) => {
                        this.pagination.current = current
                        this.pagination.pageSize = size
                    },
                    onShowSizeChange: (current, pageSize) => {
                        this.pagination.current = 1
                        this.pagination.pageSize = pageSize
                    },
                },
                pdfUrl: "",
                historyBomId: '',
                param: {},
                visible1: false,
                visible2: false,
                windowHeight: document.documentElement.clientHeight - 20,
                mapStatus: ['编辑', '审核中', '已审核', '失败中', '被驳回'],
                merges: ['classType'],
                loading: false,
                columns: [{
                    title: '产品名称',
                    align: 'left',
                    children: [{
                            title: '序号',
                            dataIndex: 'no',
                            width: 40,
                        },
                        {
                            title: '分类',
                            dataIndex: 'classType',
                            width: 80,
                            customRender: (text, row, index) => {
                                return {
                                    children: text,
                                    attrs: {
                                        rowSpan: row['classType_rowSpan'] ? row['classType_rowSpan'] : 0
                                    }
                                }
                            },
                        },
                        {
                            title: '文件类别',
                            dataIndex: 'fileType',
                            width: 100,
                        },
                        {
                            title: '文件名',
                            dataIndex: 'fileName',
                            scopedSlots: {
                                customRender: 'fileName'
                            }
                        },
                        {
                            title: '版本',
                            dataIndex: 'version',
                            width: 60,
                        },
                        {
                            title: '日期',
                            dataIndex: 'updatedDate',
                            width: 80,
                        },
                        {
                            title: '工厂-产线',
                            dataIndex: 'werkLineName',
                            customRender: (text, row, index) => {
                                let arr = text.split(';')
                                return {
                                    children: < OtherComp arr = {
                                        arr
                                    }
                                    />,
                                    attrs: {
                                        rowSpan: row['classType_rowSpan'] ? row['classType_rowSpan'] : 0
                                    }
                                }
                            },
                        },
                        {
                            title: '负责人',
                            dataIndex: 'manager',
                            width: 80,
                        },
                        {
                            title: '提交人',
                            dataIndex: 'submitter',
                            width: 80,
                        },
                        {
                            title: '审批人',
                            dataIndex: 'approver',
                            width: 80,
                        },
                        {
                            title: '批准人',
                            dataIndex: 'ratifier',
                            width: 80,
                        },
                        {
                            title: '状态',
                            dataIndex: 'techStatus',
                            width: 80,
                            scopedSlots: {
                                customRender: 'techStatus'
                            }
                        }
                    ]
                }],
                data: []
            }
        },
        created() {
            this.callDocsData()
        },
        methods: {
            onSearch(e){
				this.$refs.table.refresh()
			},
            customRow(row,index){
				return{
					on:{
						click :()=>{
							this.sapNumber = row.sapNumber
							this.sapDec = row.partDescription
							this.dropdownvisible = false
						}
					}
				}
			},
            handleCancel() {
				this.sapNumber = null
				this.sapDec = ''
				this.selectvisible = false
			},
            handleSubmit() {
				if (!this.sapNumber || this.sapNumber == '') {
					this.$message.error('请选择电芯BOM代码')
					return false
				}
                if (!this.bomNo || this.bomNo == '') {
                    this.$message.error('请输入bom编号')
					return false
                }
				/* if (JSON.stringify(this.projectdetail) == '{}') {
					this.$message.error('访问jira数据出错,请刷新页面再新增')
					return false
				} */
				sysAddBom({
						bomPartName: this.sapNumber,
						bomIssueId: this.issueId,
                        bomNo:this.bomNo,
						bomType: 0
					})
					.then((res) => {
						if (res.success) {
							this.selectvisible = false
							this.sapNumber = ''
							this.sapDec = ''
                            this.callDocsData()
						} else {
							this.$message.error(res.message, 1);
						}
					})
					.catch((err) => {
						this.$message.error('错误提示：' + err.message, 1)
					});
			},
            addBom() {
				this.selectvisible = true
			},
            fileInfoDownload(record) {
              window.open(process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id=' + record.fileId + '#navpanes=0', "_blank");


             /* this.cardLoading = true
                sysFileInfoDownload({
                    id: record.fileId
                }).then((res) => {
                    this.cardLoading = false
                    this.downloadfile(res)
                    // eslint-disable-next-line handle-callback-err
                }).catch((err) => {
                    this.cardLoading = false
                    this.$message.error('下载错误：获取文件流错误')
                })*/
            },
            downloadfile(res) {
                var blob = new Blob([res.data], {
                    type: 'application/octet-stream;charset=UTF-8'
                })
                var contentDisposition = res.headers['content-disposition']
                var patt = new RegExp('filename=([^;]+\\.[^\\.;]+);*')
                var result = patt.exec(contentDisposition)
                var filename = result[1]
                var downloadElement = document.createElement('a')
                var href = window.URL.createObjectURL(blob) // 创建下载的链接
                var reg = /^["](.*)["]$/g
                downloadElement.style.display = 'none'
                downloadElement.href = href
                downloadElement.download = decodeURI(filename.replace(reg, '$1')) // 下载后文件名
                document.body.appendChild(downloadElement)
                downloadElement.click() // 点击下载
                document.body.removeChild(downloadElement) // 下载完成移除元素
                window.URL.revokeObjectURL(href)
            },
            onClose1() {
                this.visible1 = false;
            },
            preview(id, processId) {
                this.historyBomId = id
                this.param.historyBomId = id
                this.param.processId = processId
                this.visible1 = !this.visible1
            },
            onClose2() {
                this.visible2 = false;
            },
            previewPdf(id) {
                this.pdfUrl = process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id=' + id
                //this.onClose3()
                this.visible2 = !this.visible2
            },
            handleOk() {
                this.callDocsData()
            },
            callDocsData() {
                this.loading = true
                let params = {
                    issueId: this.issueId,
                    title: ''
                }
                getDocs(params)
                    .then((res) => {
                        if (res.success) {
                            this.merges.forEach((item) => {
                                for (let i = 0, j = res.data.length; i < j; i++) {
                                    let rowSpan = 0;
                                    let n = i;
                                    while (
                                        res.data[n + 1] &&
                                        res.data[n + 1][item] == res.data[n][item]
                                    ) {
                                        rowSpan++;
                                        n++;
                                        res.data[n].rowSpan = 0;
                                    }
                                    if (rowSpan) res.data[i][item + "_rowSpan"] = rowSpan + 1;
                                    if (!rowSpan) res.data[i][item + "_rowSpan"] = 1;
                                    i += rowSpan;
                                }
                            });
                            this.data = res.data
                        } else {
                            this.$message.error(res.message, 1);
                        }
                        this.loading = false
                    })
                    .catch((err) => {
                        this.loading = false
                        this.$message.error('错误提示：' + err.message, 1)
                    });
            },
        }
    }
</script>

<style lang='less' scoped=''>
/deep/.ant-table{
    margin: 0 2px;
    margin-top:2px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}
/deep/.ant-table-thead > tr > th{
    font-weight: bold;
    background: #f3f3f3 !important;
}
/deep/.ant-table-small > .ant-table-content > .ant-table-body{
    margin: 0;
}
</style>