<template>
  <div>
<!--    <a-tabs type="card" @change="callback">-->
<!--      <a-tab-pane key="NOT_ASSIGN" tab="未分配">-->
<!--      </a-tab-pane>-->
<!--      <a-tab-pane key="ALREADY_ASSIGN" tab="已分配">-->
<!--      </a-tab-pane>-->
<!--    </a-tabs>-->
      <div v-if="isNotAssignFlag" style="float: left;padding:15px 0px 10px 10px;width: 100%;">
        <a-row :gutter="24">
          <a-col :span="4">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="委托单号"  has-feedback>
              <a-input style="z-index: 10" v-model="queryFolderno" :allowClear="true" @keyup.enter="getAssignTaskList" @change="getAssignTaskList"/>
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="产品名称"  has-feedback>
              <a-input style="z-index: 10" v-model="queryProductType" :allowClear="true" @keyup.enter="getAssignTaskList" @change="getAssignTaskList"/>
            </a-form-item>
          </a-col>
          <a-col :span="5">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="计划分配情况"  has-feedback>
              <a-select option-filter-prop="children" style="width: 150px;z-index: 10" @change="getAssignTaskList" v-model="searchAssignDateParam">
                <a-select-option value="all">
                  所有
                </a-select-option>
                <a-select-option value="alreadyAssign">
                  已分配计划
                </a-select-option>
                <a-select-option value="notAssign">
                  未分配计划
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="5" style="margin-top: 6px">
            <a-button v-if="hasPerm('tLimsOrdtask:updateOrdtaskStatus')" type="primary" style="width:100px;z-index: 10" @click="assgin()">分配检测人</a-button>
            <a-button v-if="hasPerm('testProjectTodoTask:assignPlanStartDate')" type="primary" style="width:120px;z-index: 10;margin-left: 5px" @click="assginPlan()">分配计划</a-button>
          </a-col>
        </a-row>
      </div>
      <div style="background-color: #FFFFFF;padding: 10px">
        <div class="box table-scroll" ref="box">
          <div class="mid">
            <s-table :columns="columns"
                     :data="loadData"
                     :rowClassName="tableRowClassName"
                     :expandedRowKeys="expandedRowKeys"
                     :pagination="paginationConfig"
                     @expand="onTableExpand"
                     bordered
                     :rowKey="(record) => record.id"
                     :row-selection="{
                  selectedRowKeys: selectedRowKeys, selectedRows: selectedRows,
                  onSelectAll: onSelectAll,
                  onSelect:onSelect, columnWidth:40}"
                     ref="table4">
              <template
                slot="sorter"
                slot-scope="text, record, index, columns">
                <a v-if="record.isParent" @click="handleNo(record.id)" style="text-align: left" >
                  委托单号：{{text}} {{record.createdbyname}}
                </a>
                <P v-else-if="record.isMidParent" style="text-align: center;width: 100px;float: right;margin: 1px 94px 0px 0px" >
                  {{record.sorter}}
                </P>
                <p v-else style="text-align: center">{{ text }}</p>
              </template>
              <template slot="testcontent" slot-scope="text, record, index, columns">
                <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
                  <template slot="title">
                    {{ text }}
                  </template>
                  {{ text }}
                </a-tooltip>
              </template>
              <template slot="judgebasis" slot-scope="text, record, index, columns">
                <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
                  <template slot="title">
                    {{ text }}
                  </template>
                  {{ text }}
                </a-tooltip>
              </template>
              <template slot="teststep" slot-scope="text, record, index, columns">
                <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
                  <template slot="title">
                    {{ text }}
                  </template>
                  {{ text }}
                </a-tooltip>
              </template>
              <template slot="remark" slot-scope="text, record, index, columns">
                <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
                  <template slot="title">
                    {{ text }}
                  </template>
                  {{ text }}
                </a-tooltip>
              </template>
            </s-table>
          </div>
        </div>

        <a-modal id="miModalId" title="分配检测人" centered :width="1000" :visible="personVisible" @cancel="personHandleCancel">
          <template slot="footer">
            <a-button @click="personHandleCancel">
              取消
            </a-button>
            <a-button type="primary" @click="personSubmit" :disabled="isSubmitFlag">
              确定
            </a-button>
          </template>
          <div style="margin: 0px 0px 20px 100px">
            <a-row :gutter="[8,8]">
              <a-col :span="10">
                <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="姓名">
                  <a-input v-model="searchTesterParam.USERNAME" @keyup.enter="searchTesters" @change="searchTesters"/>
                </a-form-item>
              </a-col>
              <a-col :span="10">
                <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="工号">
                  <a-input v-model="searchTesterParam.ID" @keyup.enter="searchTesters" @change="searchTesters"/>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <a-spin :spinning="personIsLoading">
            <a-table :columns="personColumns"
                     :data-source="personResultData"
                     bordered
                     :rowKey="(record) => record.ID"
                     :row-selection="{
                  type: 'radio',
                  selectedRowKeys: personSelectedRowKeys,
                  selectedRows: personSelectedRows,
                  onChange: personOnSelect,
                  columnWidth:20
                 }"
                     ref="personTable">
            </a-table>
          </a-spin>
        </a-modal>

        <a-modal title="分配计划" :width="1000" :visible="planDateVisible" :confirmLoading="confirmLoading" @ok="planSubmit" @cancel="planCancel">
          <template slot="footer">
            <a-button @click="planCancel">
              取消
            </a-button>
            <a-button type="primary" @click="planSubmit" :disabled="isSubmitFlag">
              确定
            </a-button>
          </template>
          <a-spin :spinning="confirmLoading">
            <a-form>
              <a-form-item label="计划时间" :labelCol="{sm:{span:3}}" :wrapperCol="{sm:{span:15}}" has-feedback>
                <a-range-picker format="YYYY-MM-DD"  v-model="timeRange">
                </a-range-picker>
              </a-form-item>
              <a-form-item label="是否加急" :labelCol="{sm:{span:3}}" :wrapperCol="{sm:{span:15}}" has-feedback>
                <a-radio-group name="radioGroup" v-model="isUrgent">
                  <a-radio :value="1">
                    是
                  </a-radio>
                  <a-radio :value="0">
                    否
                  </a-radio>
                </a-radio-group>
                <span v-if="!urgentFile">
                  <a-upload
                    name="file"
                    :headers="headers"
                    :data="uploadData"
                    :action="uploadUrgentUrl"
                    :multiple="false"
                    :showUploadList="false"
                    @change="uploadUrgentFile($event)">
                    <a style="color: #0d84ff;fontSize: 12px;"><u>上传加急单</u></a>
                  </a-upload>
                </span>
                <span v-else>
                      <a style="color: green" @click="openFileOrDownload(urgentFile.response.data,urgentFile.name)">{{ urgentFile.name }}</a>
                        <a-popconfirm
                          placement="topRight"
                          ok-text="删除"
                          cancel-text="取消"
                          @confirm="deleteUrgentFile()">
                          <template slot="title"> 确认删除文件"{{ urgentFile.name }}"吗 </template>
                          <a-icon type="close" style="padding: 0px 0px 0px 3px" />
                        </a-popconfirm>
                </span>
                <a-spin :spinning="uploadLoading"/>
              </a-form-item>
              <div style="margin-bottom: 10px">
                <a-row :gutter="[0,0]">
                  <a-col :span="9">
                    <a-form-item label="设备名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                      <a-input style="width: 200px;" v-model="searchEquiptParam.equiptname" @keyup.enter="searchEquiptments" @change="searchEquiptments"/>
                    </a-form-item>
                  </a-col>
                  <a-col :span="9">
                    <a-form-item label="业务编号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                      <a-input style="width: 200px;" v-model="searchEquiptParam.servicecode" @keyup.enter="searchEquiptments" @change="searchEquiptments"/>
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>
              <a-table :columns="equiptInfoColumns"
                       :data-source="equiptInfoData"
                       bordered
                       :pagination="paginationConfig"
                       :rowKey="(record) => record.id"
                       :row-selection="{
                        selectedRowKeys: equiptSelectedRowKeys,
                        selectedRows: equiptSelectedRows,
                        onChange: equiptOnSelect,
                        columnWidth:20
                       }"
                       ref="equiptTable">
                <template slot="range" slot-scope="text, record, index, columns">
                  <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
                    <template slot="title">
                      {{ text }}
                    </template>
                    {{ text }}
                  </a-tooltip>
                </template>
              </a-table>
            </a-form>
          </a-spin>
        </a-modal>
      </div>
    <!-- 弹窗 start -->
      <InquiryModal v-if="isShowModal" :modalData="modalData" @cancel="handleModalCancel"></InquiryModal>
    <!-- 弹窗 end -->
    <!-- 预览视频/图片  -->
    <a-drawer
      :bodyStyle="{ height: '100%' }"
      placement="right"
      :closable="false"
      width="70%"
      :visible="filePreviewVisible"
      @close="filePreviewVisible = false"
    >
      <iframe :src="iframeUrl" width="100%" height="100%"></iframe>
    </a-drawer>
  </div>
</template>
<script>
import InquiryModal from "@/views/system/testProgress/orderInquiry/components/inquiryModal.vue"
import {
  assignTask,
  getLimsOrdtaskList, getTestPerson, getLimsOrdtaskListOfSafety, assignTaskOfAq, getFolderByFolderNo
} from '@/api/modular/system/limsManager'
  import { STable } from '@/components'
  import moment from "moment/moment";
import {
  assignPlan,
  assignPlanStartDate, getEquiptInfoList
} from "@/api/modular/system/testProgressManager";
import { mapActions, mapGetters } from 'vuex'
import Vue from "vue";
import { ACCESS_TOKEN } from "@/store/mutation-types";
import { getMinioDownloadUrl, getMinioPreviewUrl } from "@/api/modular/system/fileManage";
import { downloadMinioFile } from "@/utils/util";

  export default {
    components: {
     STable,InquiryModal,
    },
    data() {
      return {
        loadData: parameter => {
          return getLimsOrdtaskListOfSafety(Object.assign(parameter, {
              status: this.ordTaskStatus,
              laboratoryId: "HZ_YJ_DL_AQ",
              queryFolderno: this.queryFolderno,
              queryProductType: this.queryProductType,
              searchAssignDateParam: this.searchAssignDateParam
            })).then((res) => {
            this.resultData = res.data
            if (this.isNotAssignFlag) {
              let folderNoSet = [...new Set(this.resultData.rows.map(item => item.id))];
              this.resultData.rows.forEach(item => {
                item.children.forEach(o => {
                  folderNoSet.push(o.id)
                })
              })
              this.expandedRowKeys = folderNoSet
            } else {
              // 已分配页面默认不展开
              this.expandedRowKeys = []
            }
            return this.resultData
          })
        },
        searchAssignDateParam: null,
        queryFolderno: null,
        queryProductType: null,
        isShowModal: false,
        isNotAssignFlag: true,
        ordTaskStatus: 'Assign',
        urgentFile: null,
        iframeUrl: '',
        filePreviewVisible: false,
        modalData: {},
        personVisible: false,
        confirmLoading: false,
        allPersonResultData: [],
        allEquiptResultData: [],
        paginationConfig: {
          showSizeChanger: true,
          pageSizeOptions: ['10', '20', '30', '40', '50'], // 显示的每页数量选项
          size: "small",
          showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
        },
        equiptInfoData: [],
        equiptInfoColumns: [
          // {
          //   title: '设备编号',
          //   dataIndex: 'equiptcode',
          //   align: 'center',
          //   width: 50
          // },
          {
            title: '业务编号',
            dataIndex: 'servicecode',
            align: 'center',
            width: 50
          },
          {
            title: '设备名称',
            dataIndex: 'equiptname',
            align: 'center',
            width: 70
          },
          {
            title: '设备能力',
            dataIndex: 'range',
            align: 'center',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: "range" }
          },
          {
            title: '最后使用时间',
            dataIndex: 'lastUsedTime',
            align: 'center',
            width: 60,
            customRender: (text, record, index) => (text ? moment(new Date(text)).format("YYYY-MM-DD") : "")
          },
          {
            title: '最后使用委托单',
            dataIndex: 'lastUsedFolderNo',
            align: 'center',
            width: 60
          }
        ],
        searchTesterParam: {},
        searchEquiptParam: {},
        planDateVisible: false,
        timeRange: null,
        uploadLoading: false,
        isUrgent: 0,
        headers: {
          Authorization: "Bearer " + Vue.ls.get(ACCESS_TOKEN)
        },
        uploadData: { bucket: 'safetylab' },
        uploadUrgentUrl: "/api/sysFileInfo/minioUpload",
        timeForm: this.$form.createForm(this,{ name: 'timeForm' }),
        personIsLoading: false,
        isSubmitFlag: false,
        height: 200,
        labelCol: {
          sm: {
            span: 8
          }
        },
        wrapperCol: {
          sm: {
            span: 15
          }
        },
        data: [],
        resultData: [],
        personResultData: [],
        personColumns: [
          {
            title: '序号',
            dataIndex: 'index',
            align: 'center',
            width: 30,
            ellipsis: true,
            customRender: (text, record, index) => `${index + 1}`
          },
          {
            title: '工号',
            dataIndex: 'ID',
            align: 'center',
            width: 40,
          },
          {
            title: '姓名',
            dataIndex: 'USERNAME',
            align: 'center',
            width: 40,
          },
          {
            title: '部门',
            dataIndex: 'ORGNAME',
            align: 'center',
            width: 50,
          },
          {
            title: '在测测试项目数量',
            dataIndex: 'ORDTASKNUMBER',
            align: 'center',
            width: 50,
            scopedSlots: { customRender: 'ORDTASKNUMBER' },
          },
          {
            title: '在测样品数量',
            dataIndex: 'ORDERNUMBER',
            align: 'center',
            width: 50,
          },
          {
            title: '在测委托单数量',
            dataIndex: 'FOLDERNUMBER',
            align: 'center',
            width: 50,
          },
        ],
        columns: [
          {
            title: '试验顺序',
            dataIndex: 'sorter',
            // align: 'center',
            width: 250,
            scopedSlots: { customRender: 'sorter' },
            // customRender: (text, record, index) => `${index + 1}`
          }, {
            title: '流程状态',
            width: 70,
            align: 'center',
            dataIndex: 'status',
            customRender: (text, record, index) => {
              switch (text) {
                case 'Examine': return '结果审核'
                case 'Outtestassign': return '委外测试任务分配'
                case 'Result_return': return '退回'
                case 'Draft': return '新建'
                case 'assignPlanStartDate': return 'PMC分配计划开始时间'
                case 'Preschedule': return '待排程'
                case 'Assign': return '任务分配'
                case 'Result': return '结果录入'
                case 'Review': return '结果复核'
                case 'Report': return '报告编制'
                case 'Done': return '完成'
                case 'Outsource': return '任务委外'
                case 'Backfill': return '数据回填'
                case 'Testreport': return '测试报告上传'
                case 'Herbaceousapprove': return '草本审核'
                case 'Herbaceousaudit': return '草本审批'
                case 'Originalupload': return '正本上传'
                case 'Originalapprove': return '正本审核'
                case 'Originalaudit': return '正本审批'
                case 'HerbaceousapproveReject': return '草本审核退回'
                case 'HerbaceousauditReject': return '草本审批退回'
                case 'OriginalapproveReject': return '正本审核退回'
                case 'Cancel': return '已取消'
                case 'Inreview': return '报告审核中'
                case 'OriginalauditReject': return '正本审批退回'
                case 'Cfmschedule': return '排程确认'
                case 'Testassign': return '测试任务分配'
                case "OrdtaskUnderChange": return '委托变更中'
                default: return ''
              }
          }
          },{
            title: '委托单号',
            width: 90,
            align: 'center',
            dataIndex: 'folderno',
          },{
            title: '测试项目编号',
            width: 100,
            align: 'center',
            dataIndex: 'testcode',
          }, {
            title: '测试数量',
            width: 70,
            align: 'center',
            dataIndex: 'testnumber',
          },  {
            title: '测试项目名称',
            width: 120,
            align: 'center',
            dataIndex: 'testname',
          }, {
            title: '测试项目别名',
            width: 120,
            align: 'center',
            dataIndex: 'alias',
          }, {
            title: '标准编号',
            width: 70,
            align: 'center',
            dataIndex: 'methodcode',
          }, {
            title: '标准名称',
            width: 70,
            align: 'center',
            dataIndex: 'methodname',
          }, {
            title: '检测内容',
            width: 200,
            align: 'center',
            dataIndex: 'testcontent',
            ellipsis: true,
            scopedSlots: { customRender: "testcontent" }
          }, {
            title: '计划开始时间',
            width: 120,
            align: 'center',
            dataIndex: 'planstarttime',
            customRender: (text, record, index) => {
              if (text === '额定容量') {
                return text
              } else if (text) {
                return moment(text).format("YYYY-MM-DD")
              }
              return text
            }
          }, {
            title: '计划结束时间',
            width: 120,
            align: 'center',
            dataIndex: 'planendtime',
            customRender: (text, record, index) => {
              let result = ''
              if (text) {
                result = moment(text).format("YYYY-MM-DD")
              }
              if (result && result !== 'Invalid date') {
                return result
              }
              return text
            }
          }, {
            title: '设备',
            width: 150,
            align: 'center',
            dataIndex: 'equipts',
          }, {
            title: '判定标准',
            width: 150,
            align: 'center',
            dataIndex: 'judgebasis',
            ellipsis: true,
            scopedSlots: { customRender: "judgebasis" }
          }, {
            title: '测试步骤',
            width: 150,
            align: 'center',
            ellipsis: true,
            dataIndex: 'teststep',
            scopedSlots: { customRender: "teststep" }
          }, {
            title: '备注',
            width: 100,
            align: 'center',
            dataIndex: 'remark',
            ellipsis: true,
            scopedSlots: { customRender: "remark" }
          }
        ],
        selectedRowKeys: [],
        expandedRowKeys: [],
        personSelectedRowKeys: [],
        selectedRows: [],
        personSelectedRows: [],
        equiptSelectedRowKeys: [],
        equiptSelectedRows: [],
      }
    },
    created() {
      this.personLoadData()
    },
    computed: {
    ...mapGetters(['userInfo'])
    },
    mounted() {},
    methods: {
      handleNo(folderNo) {
        getFolderByFolderNo({folderno: folderNo}).then((res) => {
          if (res.data && res.data.length > 0) {
            this.modalData = res.data[0]
            this.modalData.pageType = 'taskAssignmentOfSafety'
            this.isShowModal = true
          } else {
            this.$message.warning('获取委托单信息失败，请联系管理员！')
          }
        })
      },
      handleModalCancel() {
        this.isShowModal = false
      },
      tableRowClassName(record, index) {
        if (record.isParent) {
          return 'folderRowClass'
        }
      },
      onTableExpand(expanded, record) {
        if (expanded) {
          this.expandedRowKeys.push(record.id)
        } else {
          this.expandedRowKeys.splice(this.expandedRowKeys.indexOf(record.id), 1)
        }
      },
      getAssignTaskList() {
        this.selectedRows = []
        this.selectedRowKeys = []
        if (this.$refs.table4) {
          this.$refs.table4.refresh()
        }
      },
      planSubmit() {
        if (this.timeRange === null || this.timeRange.length !== 2) {
          this.$message.warning('请选择计划开始和结束时间！')
          return
        }
        if (this.isUrgent === 1 && this.urgentFile === null) {
          this.$message.warning('请上传加急单！')
          return
        }
        if (this.equiptSelectedRowKeys.length === 0) {
          this.$message.warning('请选择设备！')
          return
        }
        var targetDatas = this.selectedRows.filter(item => !item.isParent && !item.isMidParent).map(item => ({
          id: item.id.split('-')[1],
          ordTaskId: item.ordtaskid,
          orderNo: item.testcode,
          equiptIds: this.equiptSelectedRowKeys.join(','),
          planStartDate: moment(this.timeRange[0]).format('YYYY-MM-DD HH:mm:ss'),
          planEndDate: moment(this.timeRange[1]).format('YYYY-MM-DD HH:mm:ss'),
          isUrgent: this.isUrgent,
          urgentFile: this.urgentFile ? { id: this.urgentFile.response.data, name: this.urgentFile.name } : null,
        }))
        this.isSubmitFlag = true
        this.confirmLoading = true
        assignPlan(targetDatas).then((res) => {
          if (res.success) {
            setTimeout(() => {
              this.clearOldData()
              this.getAssignTaskList()
              this.$message.success('分配计划成功！')
            }, 500)
          } else {
            this.isSubmitFlag = false
            this.confirmLoading = false
            this.$message.error(res.message)
          }
        })
      },
      clearOldData() {
        this.personSelectedRows = []
        this.personSelectedRowKeys = []
        this.searchEquiptParam.equiptname = null
        this.searchEquiptParam.servicecode = null
        this.equiptSelectedRows = []
        this.equiptSelectedRowKeys = []
        this.selectedRows = []
        this.selectedRowKeys = []
        this.personVisible = false
        this.personIsLoading = false
        this.isSubmitFlag = false
        this.planDateVisible = false
        this.timeRange = null
        this.confirmLoading = false
        this.urgentFile = null
        this.isUrgent = 0
      },
      planCancel() {
        this.timeRange = null
        this.planDateVisible = false
        this.searchEquiptParam.equiptname = null
        this.searchEquiptParam.servicecode = null
        this.equiptSelectedRowKeys = []
        this.equiptSelectedRows = []
      },
      personOnSelect(selectedRowKeys, selectedRows) {
        this.personSelectedRows = selectedRows
        this.personSelectedRowKeys = selectedRowKeys
      },
      equiptOnSelect(selectedRowKeys, selectedRows) {
        this.equiptSelectedRows = selectedRows
        this.equiptSelectedRowKeys = selectedRowKeys
      },
      personSubmit() {
        if (this.personSelectedRows.length === 0) {
          this.$message.warning('请选择一人进行任务分配！')
          return
        }
        this.executeAssignTaskOfAQ()
      },
      executeAssignTaskOfAQ() {
        let person = {
          appointTester: "appointTester",
          type: "submit",
          userName: this.personSelectedRows[0].USERNAME,
          operation: "分配检测人",
          userId: this.personSelectedRows[0].ID,
          opinion: "分配检测人"
        }
        this.executeAssignTask(person)
      },
      executeAssignTask(person) {
        this.personIsLoading = true
        this.isSubmitFlag = true
        let folderTaskOrders = this.selectedRows.filter(item => !item.isParent && !item.isMidParent) //过滤掉测试项目的父项和子项
        assignTaskOfAq({ folderTaskOrders: folderTaskOrders, person: person }).then((res) => {
          if (res.success === true) {
            setTimeout(() => {
              this.clearOldData()
              this.getAssignTaskList()
              this.$message.success('任务分配成功')
            }, 300)
          } else {
            this.$message.warning('任务分配失败：' + res.message)
            this.personIsLoading = false
            this.isSubmitFlag = false
          }
        })
      },
      personHandleCancel() {
        this.personSelectedRows = []
        this.personSelectedRowKeys = []
        this.personVisible = false
      },
      onSelectAll(selected, selectedRows, changeRows) {
        this.selectedRows = selectedRows
        this.selectedRowKeys = selectedRows.map(item => item.id)
      },
      onSelect(record, selected, selectedRows, nativeEvent) {
        let initSelectedRows = JSON.parse(JSON.stringify(selectedRows))
        let selectedRowKeys = initSelectedRows.map(item => item.id)
        this.selectedRowKeys = JSON.parse(JSON.stringify(selectedRowKeys))
        this.selectedRows = selectedRows
        let parentData = this.resultData.rows
        let childData = []
        parentData.forEach(item => {
          item.children.forEach(res => {
            childData.push(res)
          });
        });
        let subChildData = []
        childData.forEach(item => {
          item.children.forEach(res => {
            subChildData.push(res)
          });
        });
        if (selected) { // 判断是选择还是取消:选择
          if (record.isParent) { // 判断是父项/子项/孙项，父项：查询到对应的子项然后push
            let child = childData.filter(obj => obj.folderid === record.folderid);
            let subChild = subChildData.filter(obj => obj.folderid === record.folderid);
            child.forEach(object => {
              if (!this.selectedRows.includes(object)) {
                this.selectedRows.push(object)
                this.selectedRowKeys.push(object.id)
              }
            })
            subChild.forEach(object => {
              if (!this.selectedRows.includes(object)) {
                this.selectedRows.push(object)
                this.selectedRowKeys.push(object.id)
              }
            })
          } else if (record.isMidParent) {
            // 如果是子项
            // ①查询到对应的孙项然后push
            let subChild = subChildData.filter(obj => obj.ordtaskid === record.id);
            subChild.forEach(object => {
              if (!this.selectedRows.includes(object)) {
                this.selectedRows.push(object)
                this.selectedRowKeys.push(object.id)
              }
            })
            // ②判断selectedRows里子项的兄弟项是否被全选中，全选中需要push父项
            let brother = childData.filter(obj => obj.folderid === record.folderid);
            let selectedRows = this.selectedRows.filter(obj => obj.isMidParent && obj.folderid === record.folderid);
            if (brother.length === selectedRows.length) {
              let parent = parentData.filter(obj => obj.folderid === record.folderid);
              this.selectedRows.push(parent[0])
              this.selectedRowKeys.push(parent[0].id)
            }
          } else {
            // 如果是孙项
            // ①判断selectedRows里所有孙项是否被选中，全选中需要push子项
            let subBrother = subChildData.filter(obj => obj.ordtaskid === record.ordtaskid);
            let subSelectedRows = this.selectedRows.filter(obj => obj.ordtaskid === record.ordtaskid);
            if (subBrother.length === subSelectedRows.length) {
              let parent = childData.filter(obj => obj.id === record.ordtaskid);
              this.selectedRows.push(parent[0])
              this.selectedRowKeys.push(parent[0].id)
            }
            // ②判断selectedRows里所有子项是否被选中，全选中需要push父项
            let brother = childData.filter(obj => obj.folderid === record.folderid);
            let selectedRows = this.selectedRows.filter(obj => obj.isMidParent && obj.folderid === record.folderid);
            if (brother.length === selectedRows.length) {
              let parent = parentData.filter(obj => obj.folderid === record.folderid);
              this.selectedRows.push(parent[0])
              this.selectedRowKeys.push(parent[0].id)
            }
          }
        } else { // 判断是选择还是取消:取消
          if (record.isParent) { // 判断是父项/子项/孙项，父项：查询到对应的子项和孙项然后splice
            let child = childData.filter(obj => obj.folderid === record.folderid);
            child.forEach(object => {
              const indexRow = this.selectedRows.findIndex(data => data.folderid === object.folderid);
              const indexKey = this.selectedRowKeys.findIndex(data => data === object.id);
              if (indexRow > -1) {
                this.selectedRows.splice(indexRow, 1);
              }
              if (indexKey > -1) {
                this.selectedRowKeys.splice(indexKey, 1);
              }
            })
            let subChild = subChildData.filter(obj => obj.folderid === record.folderid);
            subChild.forEach(object => {
              const indexRow = this.selectedRows.findIndex(data => data.folderid === object.folderid);
              const indexKey = this.selectedRowKeys.findIndex(data => data === object.id);
              if (indexRow > -1) {
                this.selectedRows.splice(indexRow, 1);
              }
              if (indexKey > -1) {
                this.selectedRowKeys.splice(indexKey, 1);
              }
            })
          } else if (record.isMidParent) {
            // 如果是子项
            // ①查询到对应的孙项然后splice
            let subChild = subChildData.filter(obj => obj.ordtaskid === record.id);
            subChild.forEach(object => {
              const indexRow = this.selectedRows.findIndex(data => data.folderid === object.folderid);
              const indexKey = this.selectedRowKeys.findIndex(data => data === object.id);
              if (indexRow > -1) {
                this.selectedRows.splice(indexRow, 1);
              }
              if (indexKey > -1) {
                this.selectedRowKeys.splice(indexKey, 1);
              }
            })
            // ②判断selectedRows里子项的兄弟项是否有一个被取消选中，是则需要splice父项
            let selectedRows = this.selectedRows.filter(obj => obj.isMidParent && obj.folderid === record.folderid);
            let child = childData.filter(obj => obj.folderid === record.folderid);
            if (selectedRows.length < child.length) {
              let parent = parentData.filter(obj => obj.folderid === record.folderid);
              const indexRow = this.selectedRows.findIndex(data => data.folderid === parent[0].folderid);
              const indexKey = this.selectedRowKeys.findIndex(data => data === parent[0].id);
              if (indexRow > -1) {
                this.selectedRows.splice(indexRow, 1);
              }
              if (indexKey > -1) {
                this.selectedRowKeys.splice(indexKey, 1);
              }
            }
          } else { // 如果是孙项，判断selectedRows里孙项的兄弟项是否有一个被取消选中，是则需要splice子项和父项
            let subChild = subChildData.filter(obj => obj.ordtaskid === record.ordtaskid);
            let selectedRows = this.selectedRows.filter(obj => !obj.isParent && !obj.isMidParent && obj.ordtaskid === record.ordtaskid);
            if (selectedRows.length < subChild.length) {
              let child = childData.filter(obj => obj.id === record.ordtaskid);
              const childIndexRow = this.selectedRows.findIndex(data => data.isMidParent && data.folderid === child[0].folderid);
              const childIndexKey = this.selectedRowKeys.findIndex(data => data === child[0].id);
              if (childIndexRow > -1) {
                this.selectedRows.splice(childIndexRow, 1);
              }
              if (childIndexKey > -1) {
                this.selectedRowKeys.splice(childIndexKey, 1);
              }
              let parent = parentData.filter(obj => obj.folderid === record.folderid);
              const indexRow = this.selectedRows.findIndex(data => data.isParent && data.folderid === parent[0].folderid);
              const indexKey = this.selectedRowKeys.findIndex(data => data === parent[0].id);
              if (indexRow > -1) {
                this.selectedRows.splice(indexRow, 1);
              }
              if (indexKey > -1) {
                this.selectedRowKeys.splice(indexKey, 1);
              }
            }
          }
        }
      },
      assgin() {
        if (this.selectedRows.length === 0) {
          this.$message.warning('请至少选择一条数据')
          return
        }
        var needAssignTasks = this.selectedRows.filter(item => !item.isParent && !item.isMidParent && !item.planstarttime)
        if (needAssignTasks.length > 0) {
          this.$message.warning('没有分配计划，无法分配检测人！')
          return;
        }
        this.personVisible = true
      },
      assginPlan() {
        var needAssignTasks = this.selectedRows.filter(item => !item.isParent && !item.isMidParent)
        if (needAssignTasks.length === 0) {
          this.$message.warning('请至少选择一条数据')
          return
        }
        // 获取设备信息
        getEquiptInfoList().then(res => {
          if (res.success) {
            this.equiptInfoData = res.data
            this.allEquiptResultData = JSON.parse(JSON.stringify(res.data))
          }
        })
        this.planDateVisible = true
      },
      handleOk() {
      },
      personLoadData() {
        return getTestPerson({ status: "deprecated", orgId: "HZ_YJ_DL_AQ", roleId: "699586598579968" }).then((res) => {
          this.personResultData = res.data
          this.allPersonResultData = JSON.parse(JSON.stringify(res.data))
        })
      },
      searchTesters() {
        if (this.searchTesterParam.ID) {
          this.personResultData = this.allPersonResultData.filter(item => item.ID.includes(this.searchTesterParam.ID))
        }
        if (this.searchTesterParam.USERNAME) {
          this.personResultData = this.allPersonResultData.filter(item => item.USERNAME.includes(this.searchTesterParam.USERNAME))
        }
        if (!this.searchTesterParam.ID && !this.searchTesterParam.USERNAME) {
          this.personResultData = this.allPersonResultData
        }
      },
      searchEquiptments() {
        if (this.searchEquiptParam.equiptname && this.searchEquiptParam.servicecode) {
          this.equiptInfoData = this.allEquiptResultData.filter(item => item.equiptname.includes(this.searchEquiptParam.equiptname))
          this.equiptInfoData = this.equiptInfoData.filter(item => item.servicecode && item.servicecode.includes(this.searchEquiptParam.servicecode))
        } else if (!this.searchEquiptParam.equiptname && !this.searchEquiptParam.servicecode) {
          this.equiptInfoData = this.allEquiptResultData
        } else if (this.searchEquiptParam.equiptname) {
          this.equiptInfoData = this.allEquiptResultData.filter(item => item.equiptname.includes(this.searchEquiptParam.equiptname))
        } else if (this.searchEquiptParam.servicecode) {
          this.equiptInfoData = this.allEquiptResultData.filter(item => item.servicecode && item.servicecode.includes(this.searchEquiptParam.servicecode))
        }
      },
      //直接调用lims接口预览或下载
      async openFileOrDownload(fileId, fileName) {
        //pbi上传的文件
        if (fileId) {
          getMinioPreviewUrl(fileId).then(res => {
            //预览
            let suffixArray = ['.mp4','.MP4','.jpg','.JPG','.png','.PNG','.gif','.GIF','.pdf','.PDF']
            if (fileName && suffixArray.findIndex(item => fileName.indexOf(item) > -1) !== -1) {
              this.iframeUrl = res.data.replace("http://***********:9000/", "/minioDownload/")
              this.filePreviewVisible = true
            } else {
              //下载
              getMinioDownloadUrl(fileId).then(res1 => {
                downloadMinioFile(res1.data)
              })
            }
          })
        }
      },
      deleteUrgentFile() {
        this.uploadLoading = true
        setTimeout(() => {
          this.uploadLoading = false
          this.urgentFile = null
          this.$message.success(`删除成功`)
        },500)
      },
      uploadUrgentFile(info) {
        this.uploadLoading = true
        if (info.file.status === "done") {
          this.urgentFile = info.file
            setTimeout(() => {
              this.uploadLoading = false
              this.$message.success(`${info.file.name} 上传成功`)
            },600)
        } else if (info.file.status === "error") {
          this.uploadLoading = false
          this.$message.error(`${info.file.name} 上传失败`)
        } else {
          this.uploadLoading = false
        }
      },
      // callback(key) {
        // if (key === 'NOT_ASSIGN') {
        //   this.ordTaskStatus = "Assign"
        //   this.isNotAssignFlag = true
        //   this.clearOldData()
        //   this.getAssignTaskList()
        // } else {
        //   this.ordTaskStatus = "Result"
        //   this.isNotAssignFlag = false
        //   this.clearOldData()
        //   this.getAssignTaskList()
        // }
      // }
    }
  }
</script>
<style lang="less" scoped=''>
  /deep/ .ant-tabs-bar {
    margin: 0px 0px 0px 0px;
  }

  /deep/ .ant-table-thead > tr > th {
    text-align: center;
    padding: 5px!important;
    font-size: 14px!important;
  }

  /deep/ .ant-table-tbody > tr > td {
    padding: 0px!important;
    height: 32px!important;
    font-size: 12px!important;
  }

  /deep/ .ant-calendar-picker-icon {
    display: none;
  }

  /deep/ .ant-calendar-picker-input.ant-input {
    color: black;
    font-size: 12px;
    text-align: center;
    padding: 0;
  }

  .red {
    background-color: #ed0000;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .yellow {
    background-color: #ffc000;
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .grey {
    background-color: rgba(223, 223, 223, 0.25);
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .ant-modal-body {
    padding: 0;
  }

  /deep/ .ant-col {
    padding: 0 !important;
    height: 40px !important;
  }

  /deep/.ant-btn > i, /deep/.ant-btn > span {
    display: flex;
    justify-content: center;
  }

  /deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
    color: #fff;
    background: #1890FF;
  }

  .green{
    background-color: #58a55c;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

  }

  /deep/#table1>div>div>div>div>div>div>table>thead{
    height: 64px;
  }

  /deep/#table1>.ant-table-wrapper>div>div>ul{
    display: none;
  }

  /deep/ .ant-modal-header{
    padding: 16px 24px  0;
    border: none;
  }
  /deep/ .ant-modal-body{
    padding: 16px;
  }

/deep/ .ant-modal-footer{
  padding: 0 24px 16px ;
}

/deep/.ant-table-tbody .folderRowClass{
  background-color: gainsboro !important;
}

/deep/.ant-btn {
  height: 28px;
}
/deep/tr td {
  padding: 4px;
  color: #333;
  font-size: 12px;
  font-weight: 400;
}
</style>