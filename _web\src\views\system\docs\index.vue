<template>
	<div class="tabs-wrapper">
		<a-tabs :activeKey="activeKey" @change="key => onTabChange(key)" destroyInactiveTabPane>
			<a-tab-pane v-for="(val, key) in stagesMap" :key="key" :tab="val">
				<div class="tab-wrapper" :style="`height:${tabHeight}px`">
					<docslist
						:isNoEdit="isNoEdit"
						:issueId="issueId"
						:stage="activeKey"
						:projectdetail="projectdetail"
						@edit="handleEdit"
					/>
				</div>
			</a-tab-pane>
		</a-tabs>
	</div>
</template>

<script>
import docslist from "./docs"
export default {
	components: {
		docslist
	},
	props: {
		issueId: {
			type: Number,
			default: 0
		},
		projectdetail: {
			type: Object,
			default: {}
		}
	},
	data() {
		return {
			isNoEdit: true,
			editOption: ["", true, true, true, true, true, true, true, true],

			activeKey: null,
			stagesMap: {
				"1": "K0 立项评审",
				"2": "M1 项目规划",
				"4": "M2 转阶段",
				"6": "M3 转阶段",
				"7": "M4 C样方案冻结",
				"8": "M5 PPAP",
				"9": "M6 SOP"
			},
			// 内容高度（不包含面包屑）
			// 40:标题高度 32:面包屑高度 45:tabs标题
			tabHeight: document.documentElement.clientHeight - 40 - 32 - 45 - 16
		}
	},
	created() {
		if (this.$route.query.stage) {
			this.activeKey = this.$route.query.stage + ""
			this.$emit('showView', this.$route.query.open,this.$route.query.nav)
		}else{
			this.activeKey = this.projectdetail.mstatus + ""
		}
		
	},
	methods: {
		onTabChange(key) {
			this.activeKey = key

			this.isNoEdit = this.editOption[key]
		},
		handleEdit() {
			this.isNoEdit = !this.isNoEdit
			this.editOption[this.activeKey] = !this.editOption[this.activeKey]
		}
	}
}
</script>

<style lang="less" scoped>
.tabs-wrapper {
	background-color: #fff;
}
.tab-wrapper {
	overflow: auto;
}
</style>
