<template>
  <div class="pbi-btns">
    <div class="pbi-btn-item" v-for="item in tabsList" :key="item[valueName]"
      :class="activeKey == item[valueName] ?  'pbi-btn-active' : ''" @click="handleClickBtn(item)">{{item[labelName]}}</div>
  </div>
</template>
<script>

  /*
  格式：
        tabsList : [{valueName(可自定义):'点击时，返回的值',labelName(可自定义):'页面上展示的值'}] 
  例：
        template
          <pbiBtnGroup :tabsList="tabsList" :activeKey="activeKey" :valueName="value" :labelName="label" @clickTab="handleClickTab"></pbiBtnGroup>
          
        script
          import pbiBtnGroup from '@/components/pageTool/components/pbiBtnGroup.vue'
          components : {
            pbiBtnGroup
          },
          data(){
            return{
              tabsList:[{value:'2',label:'A样'}]
              activeKey:'2'
            }
          },
          methods:{
            handleClickTab(e) {
              // 传回来的e:{valueName(可自定义):'点击时，返回的值',labelName(可自定义):'页面上展示的值'}
            },
          }
  */
  export default {
    props: {
      tabsList: {
        type: Array,
        default: []
      },
      valueName: {
        type: String,
        default: 'value'
      },
      labelName: {
        type: String,
        default: 'label'
      },
      activeKey: {
        type: [Number, String],
        required: false,
      }
    },
    methods: {
      handleClickBtn(value) {
        this.$emit('clickBtn', value)
      }
    }
  }
</script>
<style lang='less' scoped>
  .pbi-btns {
    display: flex;
    align-items: center;
    font-size: 12px;
  }

  .pbi-btns .pbi-btn-item {
    cursor: pointer;
    border-radius: 5px;
    padding: 8px 16px;
    margin-right: 2px;
    line-height: 1;
  }

  .pbi-btns .pbi-btn-item:hover {
    color: #1890FF;
    background: #f7f7f7;
  }

  .pbi-btns .pbi-btn-active {
    color: #1890FF;
    font-size: 13px;
    background: #E2F0FF;
    box-shadow: inset 0 0 2px #b7d0ea, 0 0 2px #b7d0ea;
  }
</style>