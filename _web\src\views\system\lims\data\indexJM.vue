<template>
	<div class="wrapper">
		<div class="flex-sb-center-row">
			<div class="head_title">离线数据提取</div>
			<!-- <a-button type="primary" :class="{ 'animate__animated animate__pulse animate__infinite': fileList.length !== 0 && filterData.length !== 0 && sheetName !== null && checked.length !== 0 }" @click="exportData">开始任务</a-button> -->

			<div
				class="normal-btn"
				:class="{
					'streamer-btn anima':
						fileList.length !== 0 && filterData.length !== 0 && sheetName !== null && checked.length !== 0
				}"
				@click="exportData"
			>
				<span></span>
				<span></span>
				<span></span>
				<span></span>
				开始任务
			</div>
		</div>

		<a-spin tip="EXCEL表头读取中" :spinning="fileList.length > 0 && sheets.length == 0">
			<div class="all-wrapper">
				<div class="left-content">
					<div class="block" :class="{ 'pbi-block-line': fileList.length === 0 }">
            <span>
              <strong>一、测试数据上传 </strong>
              <a-tooltip placement="topLeft" :overlayStyle="{maxWidth:'600px'}" arrow-point-at-center>
                <a-icon class="tips" type="question-circle" />
                <template slot="title">
                  <span>
                    1、请上传测试数据的 Excel 文件。<br>
                    2、请确保 Excel 文件的首行作为表头，用于识别输出结果项的选择。
                  </span>
                </template>
              </a-tooltip>

              <div style="float: right">

                <a-popconfirm placement="topRight" title="确认清空？" @confirm="() => fileList = []">
                <a-button style="margin-top: -5px;margin-right: 5px;margin-bottom: 3px">清空</a-button>
              </a-popconfirm>

              <a-popconfirm placement="topRight" title="确认删除？" @confirm="deleteSelectedFiles">
                <a-button>删除</a-button>
              </a-popconfirm>
              </div>





            </span>
						<div class="mt10" style="margin-top: 15px" id="left-upload">
							<a-upload-dragger
								name="file"
								:fileList="fileList"
								:multiple="true"
								:action="postUrl"
                accept=".xlsx, .xls,.csv"
								@change="fileChange"
								:beforeUpload="beforeUpload"
								:showUploadList="false"
								:data="{ first: fileList.length == 0 }"
							>

								<p class="ant-upload-drag-icon">
									<a-icon type="inbox" />
								</p>
								<p class="ant-upload-text">
									点击上传文件
								</p>
							</a-upload-dragger>
              <div v-if="fileList.length > 0" style="overflow-y: auto;max-height: 300px">
                <div v-for="(file, index) in fileList" :key="file.uid">
                  <div style="width: 110px;display: inline-block;">
                    <a-checkbox :value="file.uid" @change="toggleSelect(file.uid, $event)" style="margin-right: 5px"></a-checkbox>
                    <a-tooltip placement="top" title="删除" arrow-point-at-center>
                      <a @click="deleteFile(record, index)" style="text-align: center">
                        <a-icon type="delete" style="font-size: large;margin-right: 5px"/>
                      </a>
                    </a-tooltip>
                    <a-tooltip>
                      <template slot="title">
                        文件上传中
                      </template>
                      <a-spin :spinning="file.status != 'done'" size="small" style="margin-right: 5px"/>
                    </a-tooltip>




                    <a-tooltip placement="top" title="上移" arrow-point-at-center v-if="index != 0">
                      <a @click="moveUp(index)" style="text-align: center">
                        <a-icon type="arrow-up" style="font-size: large;margin-right: 5px"/>
                      </a>
                    </a-tooltip>
                    <a-tooltip placement="top" title="下移" arrow-point-at-center v-if="index != fileList.length -1">
                      <a @click="moveDown(index)" style="text-align: center">
                        <a-icon type="arrow-down" style="font-size: large"/>
                      </a>
                    </a-tooltip>
                  </div>


                  {{ file.name }}

                </div>
              </div>
						</div>
					</div>
					<div
						class="block mt10"
						:class="{ 'pbi-block-line': fileList.length !== 0 && sheetName === null }"
						:style="`min-height:${blockHeight}px`"
					>
						<strong>二、输出结果定义</strong>
						<div class="mt10">
							<a-select v-model="sheetName" placeholder="请先上传文件" style="width: 50%" @change="changeDataType">
								<a-select-option v-for="sheet in sheets" :key="sheet">
									{{ sheet }}
								</a-select-option>
							</a-select>
						</div>

						<div class="all-checkbox mt10">
							<a-checkbox
								:indeterminate="indeterminate"
								:checked="checkAll"
								@change="onCheckAllChange"
								style="text-align: center"
								id="checkAll"
							>
								{{ checkAll ? "取消" : "全选" }}
							</a-checkbox>
						</div>
						<a-checkbox-group @change="onChange" v-model="checked" :options="options" />
					</div>
				</div>
				<div class="right-content block">
					<strong>三、数据处理逻辑配置 </strong>
          <div style="float: right">
            <a-popconfirm placement="topRight" title="确认清空？" @confirm="() => {
            filterData = [];
            deleteLogicSelectedRowKeys = []
          }">
              <a-button style="margin-top: -5px;margin-right: 5px">清空</a-button>
            </a-popconfirm>

            <a-popconfirm placement="topRight" title="确认删除？" @confirm="deleteLogicSelect">
              <a-button style="margin-top: -5px;">删除</a-button>
            </a-popconfirm>
          </div>
          <a-tooltip placement="topLeft"
                     :overlayStyle="{maxWidth:'600px'}"
                     arrow-point-at-center>
            <a-icon class="tips" type="question-circle" />
            <template slot="title">
                <span>
                  1、【值】列可从Excel复制一列数据，会自动生成多行数据<br>
                  2、每一行的参数1，参数2之间是“与” 的关系<br>
                  3、行与行之间是“或”的关系
                </span>
            </template>
          </a-tooltip>
					<a-table
						:columns="filterColumns"
						class="mt10"
						:data-source="filterData"
						bordered
            			:row-key="record => record.id"
						:pagination="false"
						:row-selection="{ selectedRowKeys: deleteLogicSelectedRowKeys,
                		onChange: deleteLogicRowOnChange, columnWidth:20}"
					>
						<template slot="action" slot-scope="text, record, index, columns">
							<a @click="() => filterData.splice(index, 1)" style="text-align: center">删除</a>
						</template>

						<template slot="footer">
							<!-- <a-button @click="addFilterData" style="width: 100%;"><a-icon type="plus"></a-icon></a-button> -->
							<div
								class="footer-btn"
								:class="{
									'plus-btn':
									fileList.length !== 0 && sheetName !== null && filterData.length === 0
								}"
								@click="addFilterData"
							>
								<span></span>
								<span></span>
								<span></span>
								<span></span>
								<a-icon type="plus"></a-icon>
							</div>
						</template>

						<template slot="key" slot-scope="text, record, index, columns">
							<a-select
								style="width: 100%"
								v-model="record[columns.dataIndex]"
								:allow-clear="true"
								@change="(value, option) => handleDataSelectChange(value, option, columns.dataIndex,record)"
								:options="options"
							/>
						</template>

						<template slot="value" slot-scope="text, record, index, columns">
							<a-input
								@paste="copyFromExcel($event, columns.dataIndex, index)"
								style="width: 100%;text-align: center"
								v-model="record[columns.dataIndex]"
							/>
						</template>

            <template slot="value4" slot-scope="text, record, index, columns">
              <a-select v-model="record[columns.dataIndex]" allow-clear
                        style="width: 100%">
                <a-select-option value="first">
                  取第一条
                </a-select-option>
                <a-select-option value="last">
                  取最后一条
                </a-select-option>

              </a-select>
						</template>
					</a-table>
				</div>
			</div>

			<!-- <div class="float">
				<div><span class="numTitle">①</span><span class="title">Excel文件上传</span></div>

				<a-upload-dragger
					name="file"
					:fileList="fileList"
					:multiple="true"
					:action="postUrl"
					@change="fileChange"
					:beforeUpload="beforeUpload"
					:showUploadList="true"
					:data="{ first: fileList.length == 0 }"
				>
					<p class="ant-upload-drag-icon">
						<a-icon type="inbox" />
					</p>
					<p class="ant-upload-text">
						点击上传文件
					</p>
				</a-upload-dragger>
			</div>
			<div class="float1">
				<div><span class="numTitle">②</span><span class="title">数据表选择</span></div>
				<a-select style="width: 120px;margin-top: 20px" v-model="sheetName" @change="changeDataType">
					<a-select-option v-for="sheet in sheets" :key="sheet">
						{{ sheet }}
					</a-select-option>
				</a-select>
			</div>
			<div class="floatTable">
				<div>
					<span class="numTitle">③</span>
					<span class="title">数据选择</span>
					<a-table
						:columns="filterColumns"
						:data-source="filterData"
						bordered
						style="margin-top: 20px;"
						bordered
						:scroll="{ x: false }"
						:pagination="false"
					>
						<template slot="action" slot-scope="text, record, index, columns">
							<a @click="() => filterData.splice(index, 1)" style="text-align: center">删除</a>
						</template>

						<template slot="footer">
							<a-button @click="addFilterData" style="width: 100%;"><a-icon type="plus"></a-icon></a-button>
						</template>

						<template slot="key" slot-scope="text, record, index, columns">
							<a-select
								style="width: 100%"
								v-model="record[columns.dataIndex]"
								:allow-clear="true"
								@change="(value, option) => handleDataSelectChange(value, option, columns.dataIndex)"
								:options="options"
							/>
						</template>

						<template slot="value" slot-scope="text, record, index, columns">
							<a-input
								@paste="copyFromExcel($event, columns.dataIndex, index)"
								style="width: 100%;text-align: center"
								v-model="record[columns.dataIndex]"
							/>
						</template>
					</a-table>
				</div>
			</div>
			<div class="float2">
				<div class="title"><span class="numTitle">④</span><span class="title">导出项选择</span></div>

				<div :style="{ borderBottom: '1px solid #E9E9E9' }">
					<a-checkbox
						:indeterminate="indeterminate"
						:checked="checkAll"
						@change="onCheckAllChange"
						style="text-align: center"
						id="checkAll"
					>
						{{ checkAll ? "取消" : "全选" }}
					</a-checkbox>
				</div>
				<br />
				<a-checkbox-group @change="onChange" v-model="checked" :options="options" />
			</div> -->
		</a-spin>

		<a-modal
			title="导出"
			:width="800"
			:height="600"
			:bodyStyle="{ padding: 0 }"
			:visible="visible1"
			:confirmLoading="confirmLoading"
			@ok="handleSubmit"
			style="padding: 0"
			@cancel="handleCancel1"
		>
			<a-form :form="form">
				<a-row :gutter="24">
					<a-col :md="18" :sm="24">
						<a-form-item label="任务名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
							<a-input
								placeholder="请输入任务名称"
								v-decorator="['taskName', { rules: [{ required: true, message: '请输入任务名称！' }] }]"
							/>
						</a-form-item>
					</a-col>
				</a-row>

        <a-row :gutter="24">
          <a-col :md="18" :sm="24">
            <a-form-item label="Excel格式" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select v-decorator="['excelType', {rules: [{required: true, message: '请选择Excel格式！'}]}]" default-value="one"
                        style="width: 100%"  placeholder="请选择Excel格式">
                <a-select-option value="one">
                  保存于同一个Sheet中
                </a-select-option>
                <a-select-option value="more">
                  保存于不同的Sheet中
                </a-select-option>

              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
			</a-form>
		</a-modal>
	</div>
</template>
<script>
import { addJMTask } from "@/api/modular/system/exportTaskManager"

import { mapGetters } from "vuex"

export default {
  props: {
    width:{
      type: Number,
      default: 0
    }
  },
	data() {
		return {
			param: null,
			headerSpinning: false,
			postUrl: "http://***********:8080/testDataExportTask/jmFileUpload",
			fileList: [],
			sheets: [],
			options: [],
			headerData: [],
			indeterminate: false,
			checkAll: false,
			checked: [],
			filterData: [],
			sheetName: null,
			visible: false,
			confirmLoading: false,
			visible1: false,
			height: 200,
			labelCol: {
				sm: {
					span: 11
				}
			},
			wrapperCol: {
				sm: {
					span: 13
				}
			},
			show: false,
      deleteLogicSelectedRowKeys:[],
			// 表头
			filterColumns: [
        {
          title: "序号",
          align: "center",
          width: 40,
          customRender: (text, record, index) => {
            record.id = index + 1
            return index + 1
          }
        },
				{
					title: "参数1",
					dataIndex: "index1",
					align: "center",
					width: 90,
					scopedSlots: { customRender: "key" }
				},
				{
					title: "值",
					width: 75,
					align: "center",
					dataIndex: "value1",
					scopedSlots: { customRender: "value" }
				},
				{
					title: "参数2",
					dataIndex: "index2",
					align: "center",
					width: 90,
					scopedSlots: { customRender: "key" }
				},
				{
					title: "值",
					width: 75,
					align: "center",
					dataIndex: "value2",
					scopedSlots: { customRender: "value" }
				},
				{
					title: "参数3",
					dataIndex: "index3",
					align: "center",
					width: 90,
					scopedSlots: { customRender: "key" }
				},
				{
					title: "值",
					width: 75,
					align: "center",
					dataIndex: "value3",
					scopedSlots: { customRender: "value" }
				},{
					title: "附加条件",
					width: 75,
					align: "center",
					dataIndex: "value4",
					scopedSlots: { customRender: "value4" }
				}
				/* {
            title: '参数4',
            dataIndex: 'index4',
            align: 'center',
            width: 90,
            scopedSlots: {customRender: 'key'},
          }, {
            title: '值',
            width: 90,
            align: 'center',
            dataIndex: 'value4',
            scopedSlots: {customRender: 'value'},
          },
          {
            title: '参数5',
            dataIndex: 'index5',
            align: 'center',
            width: 90,
            scopedSlots: {customRender: 'key'},
          }, {
            title: '值',
            width: 90,
            align: 'center',
            dataIndex: 'value5',
            scopedSlots: {customRender: 'value'},
          }*/
			],
			form: this.$form.createForm(this),
			height: "500px",
			saveParam: null,
			// 存储3数据选择最后输入的select值
			lastDataSelect: {
				index1: "",
				index2: "",
				index3: ""
			},
			blockHeight: "",
      selectedFiles: []
		}
	},
	computed: {
		...mapGetters(["testTaskExcelData"])
	},
	created() {
		this.$nextTick(() => {
			console.log(document.getElementsByClassName("ant-upload")[0].clientHeight)
		})
	},
	mounted() {
		this.param = this.testTaskExcelData
		this.$store.commit("setTaskExcelData", null)

		if (this.param != null) {
			this.fileList = this.param.fileListSource
			this.sheets = []
			this.options = []
			for (let i = 0; i < this.param.headers.length - 1; i++) {
				this.sheets.push(this.param.headers[i].sheetName)
				if (this.param.sheetName == this.param.headers[i].sheetName) {
					for (let j = 0; j < this.param.headers[i].headers.length; j++) {
					  if(this.param.headers[i].headers[j].value){
              this.options.push({ label: this.param.headers[i].headers[j].value, value: this.param.headers[i].headers[j].index })
            }else{
              this.options.push({ label: this.param.headers[i].headers[j], value: j })
            }
					}
				}
			}
			this.headerData = this.param.headers
			this.filterData = this.param.filterParam
			this.checked = []
			for (let i = 0; i < this.param.exportOptions.length; i++) {
				this.checked.push(this.param.exportOptions[i].index)
			}
			this.sheetName = this.param.sheetName
		} else {
			//document.getElementById("checkAll").click()
		}

		this.blockHeight = document.body.clientHeight - 296 - this.width
    document.documentElement.style.setProperty(
      `--width`,
      this.width + 'px'
    )

	},

	methods: {
    deleteLogicRowOnChange(selectedRowKeys, selectedRows) {
      this.deleteLogicSelectedRowKeys = selectedRowKeys
    },
    deleteLogicSelect(){
      this.filterData = this.filterData.filter(item => !this.deleteLogicSelectedRowKeys.includes(item.id));
      this.deleteLogicSelectedRowKeys = []
      this.deleteLogicSelectedRow = []
    },
		fileChange(file) {
			this.fileList = []
			if (file.file.response) {

			  if(!file.file.response.success){
          this.$message.error(file.file.response.message,5)
        }

				if (Array.isArray(file.file.response.data)) {
					this.headerData = file.file.response.data
					let data = file.file.response.data
					this.sheets = []
					//{label: '工步序号', value: 'StepNum', key: 'stepNum'},
					for (let i = 0; i < data.length - 1; i++) {
						this.sheets.push(data[i].sheetName)
					}
					this.headerSpinning = false
          this.sheetName = null
          this.changeDataType(null)
				}
			}
      for (let i = 0; i < file.fileList.length; i++) {
        if((file.fileList[i].type.indexOf('sheet') > -1 || file.fileList[i].type.indexOf('text/csv') > -1 || file.fileList[i].type.indexOf('excel')) && (file.fileList[i].response == undefined || file.fileList[i].response.success)){
          this.fileList.push(file.fileList[i])
        }
      }
		},
		beforeUpload(file,fileList) {
			const fileName = file.name
			const isExcel = fileName.endsWith(".xls") || fileName.endsWith(".xlsx") || fileName.endsWith(".csv")
			if (!isExcel) {
				this.$message.warn("请上传excel文件！")
        this.fileList = []
			}
			return isExcel
		},
		onChange() {
			let num = this.checked.length
			let allNum = this.options.length

			this.indeterminate = num == 0 ? null : 0 < num < allNum
			this.checkAll = num > 0 && num == allNum
			this.$nextTick(() => {
				if (this.checkAll) {
					this.indeterminate = false
				}
			})
		},

		// 3数据选择select改变事件
		handleDataSelectChange(value, option, index,record) {
      let flag = true
      if(index.indexOf('1') > -1){
        if(value == record.index2){
          flag = false
          this.$message.warn("请选择不同参数")
          record.index2 = null;
        }else if (value == record.index3){
          flag = false
          this.$message.warn("请选择不同参数")
          record.index3 = null;
        }
      }

      if(index.indexOf('2') > -1){
        if(record.index1 == value){
          flag = false
          this.$message.warn("请选择不同参数")
          record.index2 = null;
        }else if (value == record.index3){
          flag = false
          this.$message.warn("请选择不同参数")
          record.index3 = null;
        }
      }
      if(index.indexOf('3') > -1){
        if(record.index1 == value){
          flag = false
          this.$message.warn("请选择不同参数")
          record.index3 = null;
        }else if (record.index2 == value){
          flag = false
          this.$message.warn("请选择不同参数")
          record.index3 = null;
        }
      }

      if(flag){
        this.lastDataSelect[index] = value
      }
		},

		onCheckAllChange(e) {
			Object.assign(this, {
				checked: e.target.checked ? this.filterValue(this.options) : [],
				indeterminate: null,
				checkAll: e.target.checked
			})
		},
		filterValue(list) {
			let newList = []
			for (let i = 0; i < list.length; i++) {
				newList.push(list[i].value)
			}
			return newList
		},

		handleSubmit() {
			const {
				form: { validateFields }
			} = this

			this.confirmLoading = true

			let param = {}
			param.filterParam = this.filterData
			let exportOptions = []
			for (let i = 0; i < this.checked.length; i++) {
				exportOptions.push({ index: this.checked[i], value: this.options.find(e => e.value == this.checked[i]).label })
			}
			param.exportOptions = exportOptions
			param.sheetName = this.sheetName

			let fileList = []
			for (let i = 0; i < this.fileList.length; i++) {
				if (Array.isArray(this.fileList[i].response.data)) {
					fileList.push({
						url:
							"http://***********:8080/sysFileInfo/download?id=" +
							this.fileList[i].response.data.find(e => e.sheetName == "fileId").headers,
						name: this.fileList[i].name
					})
				} else {
					fileList.push({
						url: "http://***********:8080/sysFileInfo/download?id=" + this.fileList[i].response.data,
						name: this.fileList[i].name
					})
				}
			}

			param.fileList = fileList
			param.headers = this.headerData
			param.fileListSource = this.fileList
			validateFields((errors, values) => {
				if (!errors) {
					addJMTask(Object.assign(values, param)).then(res => {
						if (res.success) {
							this.$message.success("导出任务创建成功")
							this.$router.push("/testDataHistory")
							//this.$parent.showView(12)
						} else {
							this.$message.warn(res.message)
						}
					})
				}
				this.confirmLoading = false
				this.visible1 = false
			})

			/*validateFields((errors, values) => {

          this.confirmLoading = false
        })*/
		},

		exportData() {

      for (let i = 0; i < this.fileList.length; i++) {
        if(this.fileList[i].status != 'done'){
          this.$message.warn("请等待文件上传完成")
          return
        }
      }

			let param = {}
			param.sheetName = this.sheetName
			param.checked = this.checked
			param.fileList = this.fileList
			param.filterData = this.filterData

			if (param.fileList.length == 0) {
				this.$message.warn("请上传文件")
				return
			}
			if (param.sheetName == null) {
				this.$message.warn("请选择数据表")
				return
			}
			if (param.filterData.length == 0) {
				this.$message.warn("请填写数据选择参数")
				return
			}
			if (param.checked.length == 0) {
				this.$message.warn("请勾选导出项")
				return
			}

			this.visible1 = true

			this.saveParam = param
		},
		// 3数据选择--添加按钮事件
		addFilterData() {
			if (this.filterData.length > 0) {
				this.filterData.push({
					index1: this.filterData[this.filterData.length - 1]["index1"]
						? this.filterData[this.filterData.length - 1]["index1"]
						: null,
					value1: null,
					index2: this.filterData[this.filterData.length - 1]["index2"]
						? this.filterData[this.filterData.length - 1]["index2"]
						: null,
					value1: null,
					index3: this.filterData[this.filterData.length - 1]["index3"]
						? this.filterData[this.filterData.length - 1]["index3"]
						: null,
					value1: null
				})
			} else {
				this.filterData.push({})
			}
		},

		handleCancel() {
			this.visible = false
		},
		handleCancel1() {
			this.visible1 = false
		},
    deleteFile(index) {
      this.fileList.splice(index, 1);
    },
    toggleSelect(uid, event) {
      if (event.target.checked) {
        this.selectedFiles.push(uid);
      } else {
        this.selectedFiles = this.selectedFiles.filter(id => id !== uid);
      }
    },
    deleteSelectedFiles() {
      // 过滤掉被选中的文件
      this.fileList = this.fileList.filter(file => !this.selectedFiles.includes(file.uid));
      // 清空选中状态
      this.selectedFiles = [];
    },
    moveUp(index) {
      if (index > 0) {
        const temp = this.fileList[index];
        this.fileList.splice(index, 1);
        this.fileList.splice(index - 1, 0, temp);
      }
    },
    moveDown(index) {
      if (index < this.fileList.length - 1) {
        const temp = this.fileList[index];
        this.fileList.splice(index, 1);
        this.fileList.splice(index + 1, 0, temp);
      }
    },

		changeDataType(value) {
			this.filterData = []

			let columns = this.headerData.find(e => e.sheetName == value)

			//{label: '工步序号', value: 'StepNum', key: 'stepNum'},
			this.options = []
			for (let i = 0; i < columns.headers.length; i++) {
				this.options.push({ label: columns.headers[i].value, value: columns.headers[i].index })
			}
			this.checkAll = false
			this.$nextTick(() => {
				document.getElementById("checkAll").click()
			})
		},

		copyFromExcel(event, column, index) {
			let arr = event.clipboardData.getData("text").split("\n")
			if (arr.length > 1) {
				for (let i = 1; i < arr.length; i++) {
					if (null != arr[i] && "" != arr[i] && arr[i].length != 0) {
						if (this.filterData.length > index + i) {
							this.filterData[index + i][column] = arr[i]
						} else {
							this.filterData.push({
								index1: this.filterData[index].index1,
								value1: null,
								index2: this.filterData[index].index2,
								value2: null,
								index3: this.filterData[index].index3,
								value3: null
							})
							this.filterData[index + i][column] = arr[i]
						}
					}
				}
			}

			setTimeout(() => {
				this.filterData[index][column] = arr[0]
			}, 10)
		}
	}
}
</script>
<style lang="less" scoped="">
/deep/ .ant-table-thead > tr > th {
	padding: 5px !important;
	font-size: 14px !important;
}

/deep/ .ant-table-tbody > tr > td {
	padding: 0px !important;
	height: 32px !important;
	font-size: 12px !important;
}

/deep/ .ant-calendar-picker-icon {
	display: none;
}

/deep/ .ant-calendar-picker-input.ant-input {
	color: black;
	font-size: 12px;
	border: 0;
	text-align: center;
	padding: 0;
}

.red {
	background-color: #ed0000;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}

.yellow {
	background-color: #ffc000;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}

.grey {
	background-color: rgba(223, 223, 223, 0.25);
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}

.ant-modal-body {
	padding: 0;
}

/deep/ .ant-btn > i,
/deep/ .ant-btn > span {
	display: flex;
	justify-content: center;
}

/deep/ .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
	color: #fff;
	background: #1890ff;
}

.green {
	background-color: #58a55c;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}

/deep/ #table1 > div > div > div > div > div > div > table > thead {
	height: 64px;
}

/deep/ #table1 > .ant-table-wrapper > div > div > ul {
	display: none;
}

/deep/ .ant-table-pagination.ant-pagination {
	float: right;
	margin: 0;
}

.float {
	width: 28%;
	float: left;
	margin-right: 10px;
	text-align: center;
}
.floatTable {
	width: 40%;
	float: left;
	margin-right: 10px;
	text-align: center;
}

.float1 {
	width: 11%;
	float: left;
	margin-right: 10px;
	text-align: center;
}
.float2 {
	width: 17%;
	float: left;
	margin-right: 10px;
	text-align: center;
}

/deep/ .ant-checkbox-group-item {
	display: block;
	width: 100%;
	text-align: left;
}

.title {
	font-size: large;
	margin-bottom: 20px;
}

.numTitle {
	font-size: xx-large;
}

/deep/.ant-table-footer {
	padding: 0;
}

.wrapper {
	padding: 0 10px;
	height: 100%;
	display: flow-root;
}

.all-wrapper {
	padding: 0 0 10px;
	display: flex;
	justify-content: space-between;
}

.left-content {
	width: 50%;
	margin-right: 10px;
}

.right-content {
	width: 50%;
}

.block {
	height: fit-content;
	padding: 10px;
	background: #fff;
	border-radius: 10px;
	box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
	position: relative;
}

// 通用
.mt10 {
	margin-top: 10px;
}
.mr10 {
	margin-right: 10px;
}
.flex-sb-center-row {
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.head_title {
	color: #333;
	padding: 10px 0;
	font-size: 20px;
	font-weight: 600;
}
.head_title::before {
	width: 8px;
	background: #1890ff;
	margin-right: 8px;
	content: "\00a0"; //填充空格
}

.left-content .all-checkbox {
	padding-bottom: 5px;
	margin-bottom: 5px;
	border-bottom: 1px solid #e9e9e9;
}
// 组件
/deep/.ant-checkbox-group {
	width: 100%;
	display: flex;
	flex-wrap: wrap;
}
/deep/.ant-checkbox-group-item {
	font-size: 12px;
	width: 45%;
}

/deep/.right-content .ant-table-body {
	/* width: calc(100vw - 70px); */
	height: calc(100vh - 218px + 33px - var(--width));
	border: 1px solid #e8e8e8;
	overflow: auto;
}

/deep/.all-wrapper .ant-table-thead {
	position: sticky;
	top: 0;
	z-index: 2;
}
/deep/.all-wrapper .ant-table-placeholder {
	border: none !important;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	padding: 0;
}
/deep/.right-content .ant-select-selection {
	border: none;
}
/deep/.right-content .ant-input {
	border: none;
}
/* /deep/ .ant-table-body::-webkit-scrollbar {
	height: 10px;
	width: 5px;
}
/deep/ .ant-table-body::-webkit-scrollbar-thumb {
	-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	border-radius: 0;

	background: #c1c1c1;
}
/deep/ .ant-table-body::-webkit-scrollbar-track {
	-webkit-box-shadow: 0;
	border-radius: 0;
	background: #f1f1f1;
} */

.normal-btn {
	padding: 5px 10px;
	color: #fff;
	background-color: #1890ff;
	letter-spacing: 2px;
	cursor: pointer;

}
.footer-btn {
	width: 100%;
	height: 32px;
	border: 1px solid #e8e8e8;
	background: #fff;
	color: #999;
	font-size: 16px;
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;

}

.footer-btn:hover {
	color: #1890ff;
}


.tips {
  color: #1890ff;
}



/* 滚动条整体 */
/* ::-webkit-scrollbar {
  width: 8px;
} */

/* 滚动条轨道 */
/* ::-webkit-scrollbar-track {
  background: #f1f1f1;
} */

/* 滚动条滑块 */
/* ::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
} */

/* 滚动条滑块悬停 */
/* ::-webkit-scrollbar-thumb:hover {
  background: #555;
} */

/* /deep/.ant-table-header colgroup col:last-child {
  width: 79px !important; /* 强制覆盖其他样式 
  min-width: 79px !important;
} */

</style>
