<template>
  <a-table
        ref="table"
        size="middle"
        :rowKey="(record) => record.no"
        :pagination="false"
        :columns="columns"
        :dataSource="loadData"
        :loading="loading"
        showPagination="false">
    </a-table>
</template>

<script>
import { getProjectDocs } from "@/api/modular/system/report"
export default {
    props: {
        issueId: {
            type: Number,
            default: 0
        }
    },
    data () {
        return {
            loading: true,
            columns: [],
            loadData: []
        }
    },
    methods:{
        callReportDocsData(){
            this.loading = true
            let params = {issueId: this.issueId,title:''}
            getProjectDocs(params)
            .then((res)=>{
                if (res.result) {
                    this.columns = res.data.columndata
                    this.loadData = res.data.rowdata
                } else {
                    this.$message.error(res.message,1);
                }
                this.loading = false
                })
            .catch((err)=>{
                this.loading = false
                this.$message.error('错误提示：' + err.message,1)
            });
        },
    },
    created () {
        this.callReportDocsData()
    }
}
</script>

<style>

</style>