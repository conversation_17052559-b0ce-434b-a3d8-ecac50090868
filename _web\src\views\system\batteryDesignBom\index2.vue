<template>
  <div style="background:#fff;overflow: hidden;">
    <a-breadcrumb separator=">" style="padding-left: 15px">
      <a-breadcrumb-item><span @click="gotoDevelop" style="cursor: pointer">开发</span></a-breadcrumb-item>
      <a-breadcrumb-item>
        {{structureType1}}
      </a-breadcrumb-item>
      <a-breadcrumb-item>
        {{design.productName}} 设计开发
      </a-breadcrumb-item>
      <a-breadcrumb-item>
        <a>电芯BOM设计</a>
        <a-menu slot="overlay">

          <a-menu-item>
            <a target="_blank" @click="gotoDesign">
              方案设计
            </a>
          </a-menu-item>
          <a-menu-item>
            <a target="_blank" @click="gotoMi">
              MI设计
            </a>
          </a-menu-item>
        </a-menu>
      </a-breadcrumb-item>

    </a-breadcrumb>
    <div class="tab-title">
      <div class="tab-head">

        <div class="active">电芯BOM设计</div>
        <div class="sub-title" style="float: right">
          <div >
            <span  @click="exportDataMethod()" style="cursor:pointer"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 jZPaJQ svg-icon-path-icon fill" viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path d="M5 8C5 6.89543 5.89543 6 7 6H19L24 12H41C42.1046 12 43 12.8954 43 14V40C43 41.1046 42.1046 42 41 42H7C5.89543 42 5 41.1046 5 40V8Z" fill="none" stroke="rgb(84, 152, 255)" stroke-width="4" stroke-linejoin="round"></path><path d="M30 28L23.9933 34L18 28.0134" stroke="rgb(84, 152, 255)" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path><path d="M24 20V34" stroke="rgb(84, 152, 255)" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path></g></svg></span>
            <a class="tip"  @click="exportDataMethod()" style="cursor:pointer">
              电芯BOM设计导出
            </a>
          </div>


        </div>
      </div>
    </div>
    <div class="sub-title">
      <div >
        <span v-if="updateTime != null" style="float:right;">更新时间：{{updateTime}}</span>
        <div>(*为必填项，灰色为自动生成内容)</div>
      </div>


    </div>


    <a-form :form="form" layout="inline" style="padding-left:45px;padding-right: 12px;padding-bottom: 20px">

      <a-row :gutter="24">

        <a-col :span="6">
          <a-form-item :label="fileCode">
            <a-input :disabled="isOwn == 0 || design.manCheckStatus == 10 || design.manCheckStatus == 70"
              @change="update($event,'fileCode')"
              v-decorator="['fileCode']"
            />
          </a-form-item>
        </a-col>

        <a-col :span="6">
          <a-form-item :label="fileName">
            <a-input @change="update($event,'fileName')" :disabled="isOwn == 0 || design.manCheckStatus == 10 || design.manCheckStatus == 70"

                     v-decorator="['fileName']"
            />
          </a-form-item>
        </a-col>


        <a-col :span="6">
          <a-form-item :label="version" >
            <a-input @change="update($event,'version')" :disabled="isOwn == 0 || design.manCheckStatus == 10 || design.manCheckStatus == 70"

                     v-decorator="['version']"
            />
          </a-form-item>
        </a-col>

        <a-col :span="6">
          <a-form-item :label="page">
            <a-input @change="update($event,'page')" :disabled="isOwn == 0 || design.manCheckStatus == 10 || design.manCheckStatus == 70"

                     v-decorator="['page']"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">

        <a-col :span="6">
          <a-form-item :label="productName">
            <a-input @change="update($event,'productName')" :disabled="isOwn == 0 || design.manCheckStatus == 10 || design.manCheckStatus == 70"

                     v-decorator="['productName']"
            />
          </a-form-item>
        </a-col>

        <a-col :span="6" id="productTypeCol">
          <a-form-item :label="productType">
            <a-input @change="update($event,'productType')" :disabled="isOwn == 0 || design.manCheckStatus == 10 || design.manCheckStatus == 70"

                     v-decorator="['productType']"
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item :label="factory">
            <a-input  @change="update($event,'factory')" :disabled="isOwn == 0 || design.manCheckStatus == 10 || design.manCheckStatus == 70"

                      v-decorator="['factory']"
            />
          </a-form-item>
        </a-col>


      </a-row>
      <a-row :gutter="24">

        <a-col :span="6">
          <a-form-item :label="projectStatus">

            <a-select style="width: 100%" v-decorator="['projectStatus']"
                      :disabled="isOwn == 0 || design.manCheckStatus == 10 || design.manCheckStatus == 70"
                      @change="updateSelectData($event,'projectStatus')">
              <a-select-option value="K0" >
                K0
              </a-select-option>

              <a-select-option value="M1" >
                M1
              </a-select-option>

              <a-select-option value="M2" >
                M2
              </a-select-option>

              <a-select-option value="M3" >
                M3
              </a-select-option>

              <a-select-option value="M4" >
                M4
              </a-select-option>

              <a-select-option value="M5" >
                M5
              </a-select-option>

              <a-select-option value="M6" >
                M6
              </a-select-option>

            </a-select>


          </a-form-item>
        </a-col>

        <a-col :span="6">
          <a-form-item :label="sampleStatus">

            <a-select v-decorator="['sampleStatus']"
                      :disabled="isOwn == 0 || design.manCheckStatus == 10 || design.manCheckStatus == 70"
                      @change="updateSelectData($event,'sampleStatus')">
              <a-select-option value="A样" >
                A样
              </a-select-option>

              <a-select-option value="B样" >
                B样
              </a-select-option>

              <a-select-option value="C样" >
                C样
              </a-select-option>

              <a-select-option value="D样" >
                D样
              </a-select-option>


            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="6">
          <a-form-item :label="partName">
            <a-input @change="update($event,'partName')" :disabled="isOwn == 0 || design.manCheckStatus == 10 || design.manCheckStatus == 70"

                     v-decorator="['partName']"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item :label="partCode">
            <a-input @change="update($event,'partCode')" :disabled="isOwn == 0 || design.manCheckStatus == 10 || design.manCheckStatus == 70"

                     v-decorator="['partCode']"
            />
          </a-form-item>
        </a-col>





      </a-row>
      <a-row :gutter="24">

        <a-col :span="12">
          <a-form-item :label="transport">
            <a-input @change="update($event,'transport')" :disabled="isOwn == 0 || design.manCheckStatus == 10 || design.manCheckStatus == 70"

                     v-decorator="['transport']"
            />
          </a-form-item>
        </a-col>

        <a-col :span="6">
          <a-form-item :label="batteryNum">
            <a-input @change="update($event,'batteryNum')" :disabled="isOwn == 0 || design.manCheckStatus == 10 || design.manCheckStatus == 70"

                     v-decorator="['batteryNum']"
            />
          </a-form-item>
        </a-col>

        <a-col :span="6">
          <a-form-item :label="unit">
            <a-input @change="update($event,'unit')" :disabled="isOwn == 0 || design.manCheckStatus == 10 || design.manCheckStatus == 70"

                     v-decorator="['unit']"
            />
          </a-form-item>
        </a-col>


      </a-row>


    </a-form>


    <div >
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :row-key="(record) => record.id"
        :pagination="false"
        bordered
        :row-selection="rowSelection"
        style="border-radius: 5px;
    margin:0 40px 0 40px"
      >


        <div slot="partCode"  slot-scope="text,record">

          <input class="detail-input" :disabled="isOwn == 0 || design.manCheckStatus == 10 || design.manCheckStatus == 70"

                 :value="text"  @change="updateDetailData($event,record,'partCode')" />

        </div>
        <div slot="partName"  slot-scope="text,record">

          <input class="detail-input" :disabled="isOwn == 0 || design.manCheckStatus == 10 || design.manCheckStatus == 70"

                 :value="text"  @change="updateDetailData($event,record,'partName')" />

        </div>
        <div slot="partDetail"  slot-scope="text,record">

          <input class="detail-input" :disabled="isOwn == 0 || design.manCheckStatus == 10 || design.manCheckStatus == 70"

                 :value="text"  @change="updateDetailData($event,record,'partDetail')" />

        </div>
        <div slot="unit"  slot-scope="text,record">

          <input class="detail-input" :disabled="isOwn == 0 || design.manCheckStatus == 10 || design.manCheckStatus == 70"

                 :value="text"  @change="updateDetailData($event,record,'unit')" />

        </div>
        <div slot="parentName"  slot-scope="text,record">

          <input class="detail-input" :disabled="isOwn == 0 || design.manCheckStatus == 10 || design.manCheckStatus == 70"
            :value="text"  @change="updateDetailData($event,record,'parentName')" />

        </div>
        <div slot="parentCode"  slot-scope="text,record">

          <input class="detail-input" :disabled="isOwn == 0 || design.manCheckStatus == 10 || design.manCheckStatus == 70"

                 :value="text"  @change="updateDetailData($event,record,'parentCode')" />

        </div>
        <div slot="partUse"  slot-scope="text,record">

          <input class="detail-input" :disabled="isOwn == 0 || design.manCheckStatus == 10 || design.manCheckStatus == 70"

                 :value="text"  @change="updateDetailData($event,record,'partUse')" />

        </div>
      </a-table>

    </div>







    <a-form :form="form" layout="inline" labelAlign="left" style="margin-top:12px;padding-left:45px;padding-right: 12px;">
      <a-row :gutter="24">

        <a-col :span="6" style="margin-top: 7px;">


          <a-button  @click="addDetail" type="primary" style="margin-right: 50px;
    height: 25px;" v-if="isOwn == 1 && (design.manCheckStatus == 0 || design.manCheckStatus == 20 || design.manCheckStatus == 80 )">新增</a-button>
          <a-button type="primary" @click="deleteDetail" style="height: 25px;" v-if="isOwn == 1 && (design.manCheckStatus == 0 || design.manCheckStatus == 20 || design.manCheckStatus == 80 )">删除</a-button>

        </a-col>


        <a-col :span="6">
          <a-form-item label="编制">
            <a-input @change="update($event,'organizationMan')" :disabled="isOwn == 0 || design.manCheckStatus == 10 || design.manCheckStatus == 70"

                     v-decorator="['organizationMan']"
            />
          </a-form-item>
        </a-col>

        <a-col :span="6">
          <a-form-item label="审核">
            <a-input @change="update($event,'checkMan')" :disabled="isOwn == 0 || design.manCheckStatus == 10 || design.manCheckStatus == 70"

                     v-decorator="['checkMan']"
            />
          </a-form-item>
        </a-col>

        <a-col :span="6">
          <a-form-item label="批准">
            <a-input @change="update($event,'approveMan')" :disabled="isOwn == 0 || design.manCheckStatus == 10 || design.manCheckStatus == 70"

                     v-decorator="['approveMan']"
            />
          </a-form-item>
        </a-col>


      </a-row>

    </a-form>

    <div style="padding-left: 40px;font-size:10px;">
      填写说明：<br/>
      1.BOM的组成只包括出货成品（包括电芯和包装）的物料，不包括中间用来转运和周转的容器，如转运纸箱、周转卷筒、周转卡板等<br/>
      2.研究院输出的BOM，只对设计本身负责，箔材的用量根据产品工艺设计需要切边的，直接计算进用量即可。其余的物料按照设计值输出<br/>
      3.工艺损耗，由产线控制<br/>
      4.所有物料的用量均以1000个电芯做输出<br/>
      5.BOM正文使用思源黑体 CN Regular10号字<br/>
    </div>


  </div>
</template>

<script>
  import { ALL_APPS_MENU } from '@/store/mutation-types'
  import Vue from 'vue'
  import {
    getBom,
    updateBom,
    addBom,
    exportBom,
    exportBom2,
    addBomDetail,
    updateBomDetail
  } from '@/api/modular/system/batterydesignManage'
  import { getBatteryDesign} from '@/api/modular/system/batterydesignManage';
  import {mapActions, mapGetters} from "vuex";


  export default {

    mounted() {

      this.batteryId = this.$route.query.batteryId
      this.getData()

    },
    created() {


      getBatteryDesign({inBatteryId:this.$route.query.batteryId,type:'bom'}).then(res => {
        this.design = res.data
        switch (res.data.structureType) {
          case 'g_cylinder':
            this.structureType1 = 'G圆柱'
            break
          case 'c_cylinder':
            this.structureType1 = 'C圆柱'
            break
          case 'v_cylinder':
            this.structureType1 = 'V圆柱'
            break
          case 'winding':
            this.structureType1 = '方形卷绕'
            break
          case 'lamination':
            this.structureType1 = '方形叠片'
            break
          case 'soft_roll':
            this.structureType1 = '软包'
            break

        }
      })
      this.initSelect()

    },
    data() {
      return {
        addWidth:0,
        design:{},

        structureType1:'',
        isOwn:0,
        fileCode:(<span>文件编号<span style='color:red;'>*</span></span>),
        fileName:(<span>文件名称<span style='color:red;'>*</span></span>),
        version:(<span>版本<span style='color:red;'>*</span></span>),
        page:(<span>页次<span style='color:red;'>*</span></span>),
      productName:(<span>产品名称<span style='color:red;'>*</span></span>),
      productType:(<span>产品型号<span style='color:red;'>*</span></span>),
      factory:(<span>适用工厂<span style='color:red;'>*</span></span>),
      projectStatus:(<span>项目阶段<span style='color:red;'>*</span></span>),
      sampleStatus:(<span>样品阶段<span style='color:red;'>*</span></span>),
      partName:(<span>物料名称<span style='color:red;'>*</span></span>),
      partCode:(<span>物料代码<span style='color:red;'>*</span></span>),
      transport:(<span>运输方式<span style='color:red;'>*</span></span>),
      batteryNum:(<span>电芯数量<span style='color:red;'>*</span></span>),
      unit:(<span>单位<span style='color:red;'>*</span></span>),
      partUse:(<span>用量<span style='color:red;'>*</span></span>),

      selectedRowKeys:[],
        rowSelection :{
        columnWidth:30,
          onChange: (selectedRowKeys, selectedRows) => {
          this.selectedRowKeys = selectedRowKeys
        },
      },
        columns: [
          {
            title: '序号',
            dataIndex: 'index',
            align: 'center',
            width: 30,
            customRender: (text, record, index) => index+1
          },
          {
            title: '物料代码',
            dataIndex: 'partCode',
            align: 'center',
            width:80,
            scopedSlots: { customRender: 'partCode' },
          }, {
            title: '物料名称',
            dataIndex: 'partName',
            scopedSlots: { customRender: 'partName' },
            width:80,
            align: 'center',
          },

          {
            title: '物料规格',
            dataIndex: 'partDetail',
            align: 'center',
            width:100,
            scopedSlots: { customRender: 'partDetail' },
          },
          {
            title: '单位',
            dataIndex: 'unit',
            align: 'center',
            width:60,
            scopedSlots: { customRender: 'unit' },
          },{
            title: '用量',
            dataIndex: 'partUse',
            align: 'center',
            width:80,
            scopedSlots: { customRender: 'partUse' },
          },

          {
            title: '半成品/成品名称',
            dataIndex: 'parentName',
            align: 'center',
            width:80,
            scopedSlots: { customRender: 'parentName' },
          },

          {
            title: '半成品/成品代码',
            dataIndex: 'parentCode',
            align: 'center',
            width:80,
            scopedSlots: { customRender: 'parentCode' },
          }


          ],
        id:'',
        dataSource:[],
        batteryId:null,
        form: this.$form.createForm(this),
        updateTime:null
      }
    },

    computed: {
      ...mapGetters(['userInfo'])
    },

    methods: {
      ...mapActions(['MenuChange']),
      gotoDevelop(record) {
        //this.switchApp()
        this.$router.push({
          path: "/batterydesign"
        });
      },
      gotoDesign(record) {
        //this.switchApp()
        this.$router.push({
          path: "/battery_design_manager",
          query: {
            batteryId: this.$route.query.batteryId
          }
        });
      },
      update(event,column){

        let param = {}
        param[column] = event.target.value
        param['id'] = this.id
        updateBom(param).then((res) => {
          this.$nextTick(() => {
            if (res.success) {

              getBom({batteryId:this.batteryId}).then(res => {
                this.updateTime = res.data.updateTime
              })


            }else {
              this.$message.error(res.message)
            }
          })
        })


      },

      deleteDetail(){
        if(this.selectedRowKeys.length == 0){
          this.$message.error("请先选中要删除的数据");
        }

        for (let i = 0; i < this.selectedRowKeys.length; i++) {
          updateBomDetail({id:this.selectedRowKeys[i],status:1})
        }

        try {
          this.selectedRowKeys = []
        }catch (e) {

        }

        setTimeout(() => {
          this.$nextTick(() => {
            this.getData()
          })
        }, 100)





      },
      updateDetailData(event,record,column){

        let param = {}
        param[column] = event.target.value
        param['id'] = record.id
        updateBomDetail(param).then((res) => {
          this.$nextTick(() => {
            if (res.success) {

             this.getData()


            }else {
              this.$message.error(res.message)
            }
          })
        })


      },

      updateSelectData(value,column){
        let param = {}
        param[column] = value
        param['id'] = this.id

        updateBom(param).then((res) => {
          this.$nextTick(() => {
            if (res.success) {

              getBom({batteryId:this.batteryId}).then(res => {
                this.updateTime = res.data.updateTime
              })


            }else {
              this.$message.error(res.message)
            }
          })
        })
      },


      addDetail(){
        addBomDetail({bomId:this.id}).then(() => this.getData())
      },


      getData(){
        getBom({
          batteryId: this.batteryId
        })
          .then((res) => {

            if(null == res.data){
              addBom({
                batteryId: this.batteryId
              }).then(res => {
                if (res.success) {
                  getBom({
                    batteryId: this.batteryId
                  }).then((res) => {
                    if (res.success) {
                      this.updateTime = res.data.updateTime
                      this.form.setFieldsValue(res.data)
                      this.id = res.data.id
                      this.dataSource = res.data.detailList
                    } else {
                      this.$message.error(res.message, 1);
                    }
                  })
                }
              })
            }else{
              if (res.success) {
                this.updateTime = res.data.updateTime
                this.form.setFieldsValue(res.data)
                this.id = res.data.id
                this.dataSource = res.data.detailList
              } else {
                this.$message.error(res.message, 1);
              }
            }

            getBatteryDesign({inBatteryId:this.batteryId,type:'design'}).then(res =>{

              this.isOwn = res.data.isOwn
              this.design = res.data

            })


          })
          .catch((err) => {
            this.dloading = false
            this.vloading = false
            this.$message.error('错误提示：' + err.message, 1)
          }).finally(() =>{

        });



/*

            this.addWidth = document.getElementsByClassName('ant-table-align-center')[0].offsetWidth/2-7
            document.getElementById('addWidth').style.paddingLeft=this.addWidth+'px'


*/


      },

      exportDataMethod() {

        exportBom2({batteryId:this.batteryId}).then(res => {

          const fileName = 'BOM导出.xlsx';

          if(!res) return
          const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' }) // 构造一个blob对象来处理数据，并设置文件类型

          if (window.navigator.msSaveOrOpenBlob) { //兼容IE10
            navigator.msSaveBlob(blob, fileName)
          } else {
            const href = URL.createObjectURL(blob) //创建新的URL表示指定的blob对象
            const a = document.createElement('a') //创建a标签
            a.style.display = 'none'
            a.href = href // 指定下载链接
            a.download = fileName //指定下载文件名
            a.click() //触发下载
            URL.revokeObjectURL(a.href) //释放URL对象
          }

        })


      },
      getByClass(parent, cls) {
        if (parent.getElementsByClassName) {
          return Array.from(parent.getElementsByClassName(cls));
        } else {
          var res = [];
          var reg = new RegExp(' ' + cls + ' ', 'i')
          var ele = parent.getElementsByTagName('*');
          for (var i = 0; i < ele.length; i++) {
            if (reg.test(' ' + ele[i].className + ' ')) {
              res.push(ele[i]);
            }
          }
          return res;
        }
      },

      initSelect(){
        this.$nextTick(()=>{
          let inputEleWidth = document.getElementById('productType').offsetWidth
          let formItemEleWidth = document.getElementById('productTypeCol').offsetWidth
          let diffItemEleWidth = formItemEleWidth - 70 - inputEleWidth
          let items = this.getByClass(document,'ant-form-item-control-wrapper')
          for (const e of items) {
            e.style.width = inputEleWidth+'px'
          }
          document.getElementById('factory').style.width = ( inputEleWidth*2 + 70 + diffItemEleWidth) + 'px'
        })
      },


      switchApp() {
        const applicationData = Vue.ls.get(ALL_APPS_MENU)
        this.MenuChange(applicationData[0]).then((res) => {}).catch((err) => {
          this.$message.error('错误提示：' + err.message, 1)
        })
      },


      gotoSor(){
        //this.switchApp()
        this.$router.push({
          path: "/system_battery_design_sor",
          query: {
            batteryId:this.batteryId
          },
        });
      }, gotoMi(){
        //this.switchApp()
        this.$router.push({
          path: "/g_cylinder_mi_standard_manage",
          query: {
            batteryId: this.batteryId,
          },
        });
      }, gotoManager(){
        //this.switchApp()
        this.$router.push({
          path: "/battery_design_manager",
          query: {
            batteryId:this.batteryId
          },
        });
      },

    }

  }
</script>

<style lang="less" scoped="">
  .tab-title{
    padding: 0 10px;
  } div.tab-head{
      border-bottom: 1px solid #d3d2d2c9;
    }

  div.tab-head div{
    display: inline-block;

    font-weight: 700;
    font-size: 18px;
    color: rgb(128, 128, 128);
    margin-bottom: -6px;
    cursor: pointer;
  }

  div.tab-head div.active{
    font-size: 24px;
    color: rgba(0,73,176,1);
    margin-bottom: -4px;
    cursor: text;
  }

  div.sub-title{
    overflow: hidden;
    padding: 6px 10px;
  }

  div.sub-title::after{
    content: ' ';
    display: block;
    height: 0;
    clear: both;
  }

  div.sub-title span{
    display: block;
    float: left;
    margin-right: 6px;
  }
  div.sub-title span:first-child{
    margin-top: 1px;
  }
  div.sub-title .tip{
    font-family: SourceHanSansSC;
    font-weight: 400;
    font-size: 15px;
    color: rgba(0,101,255,0.67);
  }
  .input {
    color: #000;
    height: 23px;
    line-height: 23px;
    width: 60px;
    font-size: 14px;
    border: 1px solid #d9d9d9;
    outline: none;
  }
  /deep/.ant-form-item label{
    width: 70px;
    display: inherit;
    text-align: left;
  }
  /deep/.ant-form-item-label > label::after{
    content: '';
  }

  /deep/.ant-table-thead > tr > th:first-child{
        padding: 0;
      }


  /deep/.ant-table-thead > tr > th{
    padding:2px 0 2px 4px;
    border-bottom: 1px solid #d2d4d7;
    border-right: 0;
    font-size: 13px;
    /* font-weight: bold; */
    background: rgba(0, 73, 176, 0.7);
    color: #fff;
  }
  /deep/.ant-table-tbody > tr > td {
    padding: 0;
    margin: 0;
    border-bottom: 1px solid #d2d4d7;
    font-size: 12px;
  }
  .detail-input{
    width: 100%;
    border: none;
    text-align: center;
    height: 25px;
    outline: none;
  }

  /deep/.ant-table-thead > tr:first-child > th:last-child {
    border-top-right-radius: 5px;
  }
  /deep/.ant-table-thead > tr:first-child  {
    border-top-left-radius: 5px;
  }

  /deep/.ant-layout-content {
    background-color: #fff!important;
  }
  /deep/.ant-table-placeholder {
    display: none!important;
  }
  /deep/.ant-input[disabled] {
    color: black;
  }
  /deep/.ant-select-selection-selected-value {
    color: black;
  }
  /deep/.ant-input[disabled] {

    background-color: #ffffff;

  }
  /deep/.ant-select-disabled .ant-select-selection {
    background: #ffffff;
  }

</style>

