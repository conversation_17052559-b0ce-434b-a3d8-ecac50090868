<template>
<div style="background:#fff">
    <div style="text-align:right">
        <a-button style="margin-right: 25px;margin-bottom: 10px;margin-top: 10px" @click="$refs.addForm.add(issueId)" type="primary">新增</a-button>
    </div>
    <a-table
        ref="table"
        size="small"
        :rowKey="(record) => record.id"
        :pagination="false"
        :columns="columns"
        :dataSource="loadData"
        :loading="loading"
        showPagination="false">
        <span slot="productName">
          {{ projectdetail.productProjectName }}
        </span>
        <span slot="projectName">
          {{ projectdetail.projectName }}
        </span>
        <span slot="productState" slot-scope="text,record">
            <div class="sateflex">
                <span :class="['state',text == 1 ? 'green' : (text == 2 ? 'yellow': 'red')]"></span>
                <div>
                    <clamp :text="record.productTxt" :sourceText="[record.productTxt]" :isCenter="true"></clamp>
                </div>
            </div>
        </span>

        <span slot="qualityState" slot-scope="text,record">
            <div class="sateflex">
                <span :class="['state',text == 1 ? 'green' : (text == 2 ? 'yellow': 'red')]"></span>
                <div>
                    <clamp :text="record.qualityTxt" :sourceText="[record.qualityTxt]" :isCenter="true"></clamp>
                </div>
            </div>
        </span>

        <span slot="summitState" slot-scope="text,record">
            <div class="sateflex">
                <span :class="['state',text == 1 ? 'green' : (text == 2 ? 'yellow': 'red')]"></span>
                <div><clamp :text="record.summitTxt" :sourceText="[record.summitTxt]" :isCenter="true"></clamp></div>
            </div>
        </span>


        <span slot="industryState" slot-scope="text,record">
            <div class="sateflex">
                <span :class="['state',text == 1 ? 'green' : (text == 2 ? 'yellow': 'red')]"></span>
                <div><clamp :text="record.industryTxt" :sourceText="[record.industryTxt]" :isCenter="true"></clamp></div>
            </div>
        </span>

        <span slot="action" slot-scope="text, record">
            <a  @click="$refs.editForm.edit(record)">编辑</a>
            <a-divider type="vertical" />
            <a-popconfirm  placement="topRight" title="确认删除？" @confirm="() => weekProcessDelete(record)">
              <a>删除</a>
            </a-popconfirm>
          </span>
    </a-table>
    <add-form ref="addForm" @ok="handleOk" />
    <edit-form ref="editForm" @ok="handleOk" />
</div>
</template>

<script>
import { getList,weekProcessDelete } from "@/api/modular/system/weekProcessManage"
import editForm from './editForm'
import addForm from './addForm'
import {clamp} from '@/components'
export default {
    components: {
      editForm,
      addForm,
      clamp
    },
    props: {
        issueId: {
            type: Number,
            default: 0
        },
        projectdetail: {
            type: Object,
            default: {}
        }
    },
    data () {
        return {
            loading: true,
            columns: [
                {
                    title: '产品名称',
                    dataIndex: 'productName',
                    align: 'center',
                    scopedSlots: {
                        customRender: 'productName'
                    }
                },
                {
                    title: '项目名称',
                    align: 'center',
                    dataIndex: 'projectName',
                    scopedSlots: {
                        customRender: 'projectName'
                    }
                },
                {
                    title: '时间',
                    align: 'center',
                    dataIndex: 'weekTime',
                    customRender: (text, record, index) => `CW${text}`
                },
                {
                    title: '产品状态',
                    align: 'center',
                    width:120,
                    dataIndex: 'productState',
                    scopedSlots: {
                        customRender: 'productState'
                    }
                },
                {
                    title: '质量状态',
                    align: 'center',
                    width:120,
                    dataIndex: 'qualityState',
                    scopedSlots: {
                        customRender: 'qualityState'
                    }
                },
                {
                    title: '交付状态',
                    align: 'center',
                    width:120,
                    dataIndex: 'summitState',
                    scopedSlots: {
                        customRender: 'summitState'
                    }
                },
                {
                    title: '产业化状态',
                    align: 'center',
                    width:120,
                    dataIndex: 'industryState',
                    scopedSlots: {
                        customRender: 'industryState'
                    }
                },
                {
                    title: '操作',
                    dataIndex: 'action',
                    scopedSlots: {
                        customRender: 'action'
                    }
                },
            ],
            loadData: []
        }
    },
    methods:{
        handleOk () {
            this.callWeekProcess()
        },
        callWeekProcess(){
            this.loading = true
            let params = {issueId: this.issueId,title:''}
            getList(params)
            .then((res)=>{
                if (res.success) {
                    this.loadData = res.data
                } else {
                    this.$message.error(res.message,1);
                }
                this.loading = false
                })
            .catch((err)=>{
                this.loading = false
                this.$message.error('错误提示：' + err.message,1)
            });
        },

        weekProcessDelete (record) {
            this.loading = true
            weekProcessDelete(record).then((res) => {
            this.loading = false
            if (res.success) {
                this.$message.success('删除成功')
                this.callWeekProcess()
            } else {
                this.$message.error('删除失败：' + res.message)
            }
            }).catch((err) => {
            this.$message.error('删除错误：' + err.message)
            })
        }
    },
    created () {
        this.callWeekProcess()
    }
}
</script>

<style lang="less" scoped=''>
/deep/.ant-table{
    margin: 0 2px;
    margin-top:2px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}
/deep/.ant-table-thead > tr > th{
    font-weight: bold;
    background: #f3f3f3 !important;
}
/deep/.ant-table-small > .ant-table-content > .ant-table-body{
    margin: 0;
}
.state{
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 3px;
    margin-left: 3px;
}
.state.green{
    background: #91cc75;
}
.state.yellow{
    background: #efeb73;
}
.state.red{
    background: #f54747;
}

.sateflex{
    display: flex;
    align-items: center;
    justify-content: center;
}
.sateflex span{
    margin-right: 12px;
    
}
.sateflex div{
    width: 80px;
    text-align: left;
}
.ellipsis-tip {
	width: 200px;
	word-break: break-all;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 3; /* 这里是超出几行省略 */
	overflow: hidden;
}
</style>