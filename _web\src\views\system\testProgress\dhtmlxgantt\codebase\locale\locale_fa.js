/*
@license

dhtmlxGantt v.6.3.3 Standard

This version of dhtmlxGantt is distributed under GPL 2.0 license and can be legally used in GPL projects.

To use dhtmlxGantt in non-GPL projects (and get Pro version of the product), please obtain Commercial/Enterprise or Ultimate license on our site https://dhtmlx.com/docs/products/dhtmlxGantt/#licensing or contact <NAME_EMAIL>

(c) XB Software Ltd.

*/
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("locale/locale_fa",[],t):"object"==typeof exports?exports["locale/locale_fa"]=t():e["locale/locale_fa"]=t()}(window,function(){return function(e){var t={};function n(o){if(t[o])return t[o].exports;var r=t[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(o,r,function(t){return e[t]}.bind(null,r));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="/codebase/",n(n.s=202)}({202:function(e,t){gantt.locale={date:{month_full:["ژانویه","فوریه","مارس","آوریل","مه","ژوئن","ژوئیه","اوت","سپتامبر","اکتبر","نوامبر","دسامبر"],month_short:["1","2","3","4","5","6","7","8","9","10","11","12"],day_full:["يکشنبه","دوشنبه","سه‌شنبه","چهارشنبه","پنجشنبه","جمعه","شنبه"],day_short:["ی","د","س","چ","پ","ج","ش"]},labels:{new_task:"وظیفه جدید",new_event:"رویداد جدید",icon_save:"ذخیره",icon_cancel:"لغو",icon_details:"جزییات",icon_edit:"ویرایش",icon_delete:"حذف",confirm_closing:"تغییرات شما ازدست خواهد رفت، آیا مطمئن هستید؟",confirm_deleting:"این مورد برای همیشه حذف خواهد شد، آیا مطمئن هستید؟",section_description:"توضیحات",section_time:"مدت زمان",section_type:"نوع",column_wbs:"WBS",column_text:"عنوان",column_start_date:"زمان شروع",column_duration:"مدت",column_add:"",link:"ارتباط",confirm_link_deleting:"حذف خواهد شد",link_start:" (آغاز)",link_end:" (پایان)",type_task:"وظیفه",type_project:"پروژه",type_milestone:"نگارش",minutes:"دقایق",hours:"ساعات",days:"روزها",weeks:"هفته",months:"ماه‌ها",years:"سال‌ها",message_ok:"تایید",message_cancel:"لغو",section_constraint:"Constraint",constraint_type:"Constraint type",constraint_date:"Constraint date",asap:"As Soon As Possible",alap:"As Late As Possible",snet:"Start No Earlier Than",snlt:"Start No Later Than",fnet:"Finish No Earlier Than",fnlt:"Finish No Later Than",mso:"Must Start On",mfo:"Must Finish On",resources_filter_placeholder:"type to filter",resources_filter_label:"hide empty"}}}})});
//# sourceMappingURL=locale_fa.js.map