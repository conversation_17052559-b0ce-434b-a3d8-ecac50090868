<template>
	<div class="container">
		<!-- 标题 start -->
		<div class="head-title">
			<div class="line mr10"></div>
			<span class="title">测试失效登记</span>
		</div>

<!--    <a-tabs default-active-key="null" v-model="productDepartment" type="card" @change="$refs.table.refresh()">-->
<!--      <a-tab-pane :key="null" tab="全部"> </a-tab-pane>-->
<!--      <a-tab-pane v-for="item in productDepartmentList" :key="item.id" :tab="item.customvalue"></a-tab-pane>-->

<!--    </a-tabs>-->
    <pbiTabs :tabsList="laboratoryList" :activeKey="laboratoryId" @clickTab="handleTabsChange"></pbiTabs>
<!--    <div class="tabs-List">-->
<!--      <div class="tab" v-for="item in productDepartmentList" :class="{'tab-active':item.value === productDepartment}" @click="tabClick(item.value)">{{item.label}}</div>-->
<!--    </div>-->
<!--    <slot name="tabs"></slot>-->
<!--    <slot name="search"></slot>-->
		<!-- 标题 end -->
		<!-- 筛选 start -->
		<div v-if="false" class="filter-wrapper mt5">
      <div class="filter-left">
        <div v-if="issueId==0" class="filter-block mr10">
          <a-input-search class="filter-input" @change="$refs.table.refresh()" v-model="queryParam.productName" placeholder="请输入产品名称" />
        </div>
        <div class="filter-block mr10">
          <a-input class="filter-input" @change="$refs.table.refresh()" v-model="queryParam.fileCode" placeholder="请输入文件编号" />
        </div>
<!--        <div class="filter-block mr10">-->
<!--          <a-input class="filter-input" @change="$refs.table.refresh()" v-model="queryParam.cellCode" placeholder="电芯编码" />-->
<!--        </div>-->
        <div class="filter-block mr10">
          <a-input class="filter-input" @change="$refs.table.refresh()" v-model="queryParam.testProjectName" placeholder="测试项目名称" />
        </div>
<!--        <div class="filter-block mr10">-->
<!--          <a-select mode="multiple" :maxTagCount="1" class="filter-select" @change="$refs.table.refresh()" v-model="queryParam.faStatusList" placeholder="请选择FA状态" allow-clear >-->
<!--            <a-select-option v-for="(item,index) in faStatusList" :key="index" :value="item.code" >{{ item.value }}</a-select-option>-->
<!--          </a-select>-->
<!--        </div>-->
        <div class="filter-block mr10">
          <a-select mode="multiple" :maxTagCount="1" class="filter-select" @change="$refs.table.refresh()" v-model="queryParam.testCateList" placeholder="请选择测试类别" allow-clear >
            <a-select-option v-for="(item,index) in testCateList" :key="index" :value="item.code" >{{ item.value }}</a-select-option>
          </a-select>
        </div>
        <div class="filter-block mr10">
          <a-select mode="multiple" :maxTagCount="1" class="filter-select" @change="$refs.table.refresh()" v-model="queryParam.failureCateList" placeholder="请选择失效类别" allow-clear >
            <a-select-option v-for="(item,index) in failureCateList" :key="index" :value="item.code" >{{ item.value }}</a-select-option>
          </a-select>
        </div>
<!--        <div class="filter-block mr10">-->
<!--          <a-select mode="multiple" :maxTagCount="1" class="filter-select" @change="searchData" v-model="queryParam.problemDimensionList" placeholder="请选择问题维度">-->
<!--            <a-select-option v-for="(item,i) in this.problemDimensionList" :key="i" :value="parseInt(item.id)">{{ item.customvalue }}</a-select-option>-->
<!--          </a-select>-->
<!--        </div>-->



        <div class="filter-block mr10">
          <a-button style="" @click="resetSearch">重置</a-button>
        </div>
      </div>

			<div class="filter-right">
				<a-button type="primary" class="mr10" @click="addOpen">发起申请</a-button>
			</div>
		</div>
		<!-- 筛选 end -->
		<!-- 表格 start -->
		<div class="tab" :style="{borderRadius:laboratoryId == '' ? '0 10px 10px 10px' : '10px' }">
<!--      <template #search>-->
        <pbiSearchContainer>
          <pbiSearchItem label='试验类型' :span="6">
            <a-select mode="multiple" :maxTagCount="1" class="filter-select" @change="$refs.table.refresh()" v-model="queryParam.testTypeList" placeholder="请选择试验类型" allow-clear >
              <a-select-option v-for="(item,index) in testTypeList" :key="index" :value="item.code" >{{ item.value }}</a-select-option>
            </a-select>
          </pbiSearchItem>
          <pbiSearchItem label='测试类别' :span="6">
            <a-select mode="multiple" :maxTagCount="1" class="filter-select" @change="$refs.table.refresh()" v-model="queryParam.testCateList" placeholder="请选择测试类别" allow-clear >
              <a-select-option v-for="(item,index) in testCateList" :key="index" :value="item.code" >{{ item.value }}</a-select-option>
            </a-select>
          </pbiSearchItem>
          <pbiSearchItem label='失效类别' :span="6">
            <a-select mode="multiple" :maxTagCount="1" class="filter-select" @change="$refs.table.refresh()" v-model="queryParam.failureCateList" placeholder="请选择失效类别" allow-clear >
              <a-select-option v-for="(item,index) in failureCateList" :key="index" :value="item.code" >{{ item.value }}</a-select-option>
            </a-select>
          </pbiSearchItem>
          <pbiSearchItem label='产品名称'  :span="6" v-if='isShowAllSearch'>
            <a-input size='small' class="filter-input" @change="$refs.table.refresh()" v-model="queryParam.productName" placeholder="请输入产品名称" />
          </pbiSearchItem>
          <pbiSearchItem label='文件编号' :span="6" v-if='isShowAllSearch'>
            <a-input size='small' class="filter-input" @change="$refs.table.refresh()" v-model="queryParam.fileCode" placeholder="请输入文件编号" />
          </pbiSearchItem>
          <pbiSearchItem label='测试项目名称' :span="6" v-if='isShowAllSearch'>
            <a-input size='small' class="filter-input" @change="$refs.table.refresh()" v-model="queryParam.testProjectName" placeholder="测试项目名称" />
          </pbiSearchItem>
          <pbiSearchItem type='btn' :span="isShowAllSearch ? 12 : 6">
            <div class="secondary-btn">
              <a-button type="primary" class="mr12" @click="$refs.table.refresh()">查询</a-button>
            </div>
            <div class="secondary-btn">
              <a-button type="primary" class="mr12" @click="addOpen">登记</a-button>
            </div> <div class="secondary-btn">
              <a-button @click="resetSearch">重置</a-button>
            </div>
            <div class='toggle-btn'>
              <a-button size='small' type='link' @click='handleChangeSearch'>
                {{ isShowAllSearch ? '收起' : '展开' }}
                <span v-if='isShowAllSearch'>
										<a-icon type='double-left'/>
									</span>
                <span v-else>
										<a-icon type='double-right'/>
									</span>
              </a-button>
            </div>
          </pbiSearchItem>
        </pbiSearchContainer>
<!--      </template>-->
      <s-table :columns="tableColumns"
               ref="table"
               :data="loadData"
               :loading="searchLoading"
               :rowKey="(record) => record.id"
               bordered
               size="middle">
        <span slot="simpleText" slot-scope="text,record">
<!--          <clamp :text="text" :sourceText="text?text:'-'" :isCenter="true"></clamp>-->
          {{text?text:'-'}}
        </span>

        <span slot="clampText" slot-scope="text,record">
          <clamp :text="text" :sourceText="text?text.split(/[(\r\n)\r\n]+/):['-']" :isCenter="true" :key="new Date()"></clamp>
        </span>

        <span slot="productDepartment" slot-scope="text,record">
          {{text?(productDepartmentList.filter(e=>e.id==text).length>0?productDepartmentList.filter(e=>e.id==text)[0].customvalue:text):'-'}}
        </span>

        <span slot="laboratoryId" slot-scope="text,record">
          {{text?(laboratoryList.filter(e=>e.code==text).length>0?testTypeList.filter(e=>e.code==text)[0].value:text):'-'}}
        </span>

        <span slot="testType" slot-scope="text,record">
          {{text?(testTypeList.filter(e=>e.code==text).length>0?testTypeList.filter(e=>e.code==text)[0].value:text):'-'}}
        </span>

        <span slot="testCate" slot-scope="text,record">
          {{text?(testCateList.filter(e=>e.code==text).length>0?testCateList.filter(e=>e.code==text)[0].value:text):'-'}}
        </span>

        <span slot="failureCate" slot-scope="text,record">
          {{text?failureCateList.filter(e=>e.code==text)[0].value:'-'}}
        </span>

        <span slot="laboratoryId" slot-scope="text,record">
          {{text?laboratoryList1.filter(e=>e.code==text)[0].value:'-'}}
        </span>

        <span slot="reviewStatus" slot-scope="text,record">
          <span v-if="record.dpvTestFailureRecordIssueKey==null">{{reviewStatusList.filter(e=>e.code==text)[0].value}}</span>
          <a v-else-if="text" @click="handleToJira(record.dpvTestFailureRecordIssueKey)" >{{reviewStatusList.filter(e=>e.code==text)[0].value}}</a>
          <span v-else>-</span>
        </span>

<!--        <span slot="fileName" slot-scope="text,record">-->
<!--          <a @click="previewFile(record)">{{ text }}</a>-->
<!--        </span>-->
        <span slot="fileCode" slot-scope="text,record">
          <span v-if="record.fileName == null">{{text}}</span>
          <a v-else-if="record.fileName.includes('pdf') || record.fileName.includes('PDF') || record.fileName.includes('png') || record.fileName.includes('jpg') || record.fileName.includes('jpeg')" @click="previewFile(record.fileId)">{{text}}</a>
          <a v-else @click="callFileInfoDownload(record.fileId)">{{text}}</a>
        </span>
        <span slot="fileName" slot-scope="text,record">
          <span v-if="record.fileName == null">{{text}}</span>
          <a v-else-if="record.fileName.includes('pdf') || record.fileName.includes('PDF') || record.fileName.includes('png') || record.fileName.includes('jpg') || record.fileName.includes('jpeg')" @click="previewFile(record.fileId)">{{text}}</a>
          <a v-else @click="callFileInfoDownload(record.fileId)">{{text}}</a>
        </span>

			</s-table>
		</div>
		<!-- 表格 end -->

    <!-- 弹窗选择待办推送人-->
    <div class="classmodal">
      <a-modal title="选择待办人" :width="600" :height="400" :visible="todoPushVisible"
               :confirmLoading="todoPushConfirmLoading" @ok="pushReviewTodo" @cancel="handleCancel">
        <a-spin :spinning="todoPushConfirmLoading">
          <a-form :form="form">
            <a-row :gutter="24">
              <a-col :md="20" :sm="24">
                <a-form-item label="待办推送人" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input type="hidden"
                           v-decorator="['userName', { rules: [{ required: true, message: '请选择待办推送人！' }] }]" />
                  <a-dropdown v-model="dropdownvisible" placement="bottomCenter" :trigger="['click']">
                    <a-button
                      style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;">{{
                        userNameDisplay ? userNameDisplay : "选择待办推送人" }}

                      <a-icon type="down" /></a-button>
                    <a-menu slot="overlay">
                      <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                        <a-input-search v-model="userQueryParam.searchValue" placeholder="搜索..." @change="todoOnSearch" />
                        <s-table style="width:100%;" ref="todoTablePeople" :rowKey="record => record.id"
                                 :columns="selectUserColumns" :data="userLoadData" :customRow="todoPushCustomRow"
                                 :scroll="{ y: 120, x: 120 }">>
                        </s-table>
                      </a-spin>
                    </a-menu>
                  </a-dropdown>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-spin>
      </a-modal>
    </div>

    <!-- 新建 start -->
    <add ref="addForm" @ok="handleOk" :productDepartmentList="productDepartmentList" :testCateList="testCateList" :productList="productList"/>

<!--    <edit ref="editForm" @ok="handleOk" :productList="productList" :problemStatusList="problemStatusList" :problemDimensionList="problemDimensionList"/>-->
    <!-- 新建 end -->
    <a-drawer :bodyStyle="{ height: '100%' }" placement="right" :closable="false" width="80%" :visible="filePreviewVisible" @close="filePreviewOnClose">
      <iframe :src="fileUrl"  width="100%" height="100%"></iframe>
    </a-drawer>

	</div>
</template>
<script>
import { clamp,STable } from '@/components'
import {getUserLists} from "@/api/modular/system/userManage";
import Vue from "vue";
import {DICT_TYPE_TREE_DATA} from "@/store/mutation-types";
import moment from "moment";
import add from './add'
import {getStageProblem} from "@/api/modular/system/report";
import {getDpvTestFailureList, getDpvTestFailureListPage} from "@/api/modular/system/testFailure";
import {getJiraOptionList} from "@/api/modular/system/jiraCustomTool";
import {sysFileInfoDownload} from "@/api/modular/system/fileManage";
import {getProductList} from "@/api/modular/system/limsManager";

import pbiTabs from '@/components/pageTool/components/pbiTabs.vue'

export default {
  components: {
    add,
    // edit,
    clamp,
    STable,
    pbiTabs},
  props: {
    listType: {
      type: Number,
      default: 0
    },
    issueId: {
      type: Number,
      default: 0
    },
  },
	data() {
		return {
      templateHeight: document.documentElement.clientHeight - 40 - 20 - 24 - 10 - 32 - 10 - 80,
      tableHeight: document.documentElement.clientHeight - 40 - 20 - 24 - 10 - 32 - 10 - 92,
      tableList:[],
      loadData: parameter => {
        // this.queryParam.productDepartment = this.productDepartment
        this.queryParam.laboratoryId = this.laboratoryId
        console.log(this.laboratoryId);
        return getDpvTestFailureListPage(Object.assign(parameter, this.queryParam)).then((res) => {
          this.tableList = res.data.rows
          return res.data
        })
      },
      productDepartment: null,
      laboratoryId:'',
      //search
      isShowAllSearch:false,
      filePreviewVisible: false,
      fileUrl:'',
      previewBaseUrl:'/api/sysFileInfo/preview?Authorization=Bearer '+Vue.ls.get('Access-Token')+'&id=',
      addVisible: false,
      addLoading: false,
      addForm: this.$form.createForm(this, { name: 'addForm' }),

      //{code:'动力圆柱电池研究所',value:'动力圆柱电池研究所'},{code:'方形电池研究所',value:'方形电池研究所'},{code:'新型电池研究所',value:'新型电池研究所'},{code:'V型圆柱电池研究所',value:'V型圆柱电池研究所'},{code:'动力电池研究所',value:'动力电池研究所'},{code:'储能电池研究所',value:'储能电池研究所'}
      productDepartmentList:[],
      laboratoryList:[{value:'',label:"全部"},{value:"HZ_YJ_DL_AQ",label:"第四实验室"},{value:"JM_YJ_DL_CS",label:"第六实验室(JM)"},{value:"HZ_YJ_DL_CS",label:"第六实验室(HZ)"}],
      laboratoryList1:[{code:"HZ_YJ_DL_AQ",value:"第四实验室"},{code:"JM_YJ_DL_CS",value:"第六实验室(JM)"},{code:"HZ_YJ_DL_CS",value:"第六实验室(HZ)"}],
      productList:[],
      orderTypeList:[{code:'G圆柱',value:'G圆柱'},{code:'C圆柱',value:'C圆柱'},{code:'方型',value:'方型'},{code:'软包',value:'软包'},{code:'V圆柱',value:'V圆柱'}],
      projectLevelList:[{code:'S',value:'S'},{code:'A',value:'A'},{code:'B',value:'B'},{code:'C',value:'C'}],
      researchStageList:[{code:'A样',value:'A样'},{code:'B样',value:'B样'},{code:'C样',value:'C样'}],
      testCateList:[{code:"电性能测试",value:"电性能测试"},{code:"循环寿命测试",value:"循环寿命测试"},{code:"日历寿命测试",value:"日历寿命测试"},{code:"安全测试",value:"安全测试"},{code:"精密",value:"精密"}],
      testTypeList:[{code:"研发测试",value:"研发测试"},{code:"产品验证测试",value:"产品验证测试"},{code:"产品鉴定测试",value:"产品鉴定测试"}],
      failureCateList:[{code:1,value:"不满足指标"},{code:2,value:"起火"},{code:3,value:"漏液"},{code:4,value:"壳体开裂"},{code:5,value:"其它"}],
      reviewStatusList:[{code:1,value:"审核中"},{code:2,value:"审核完成"}],

      reviewResultList:['请选择', '通过', '驳回'],
      loading:false,

      selectUserLoading: false,
      selectUserColumns: [{
        title: '账号',
        dataIndex: 'account'
      }, {
        title: '姓名',
        dataIndex: 'name'
      }],
      //提出人
      presenterVisible: false,
      userQueryParam: {},
      presenterName: '',
      userLoadData: parameter => {
        return getUserLists(Object.assign(parameter, this.userQueryParam)).then((res) => {
          return res.data
        })
      },

      //待办相关
      showTodoPushBtn: this.hasPerm("oaTodo:productProblem"),
      todoPushConfirmLoading: false,
      todoPushVisible: false,
      form: this.$form.createForm(this),
      dropdownvisible: false,
      userNameDisplay: "",


      queryParam: {
        listType:this.listType,
        issueId:this.issueId,
        productName: '',
        testProjectName: '',
        testTypeList: [],
        testCateList: [],
        failureCateList: [],
        // keyWord: '',
      },
      labelCol: {
        xs: {span: 24},
        sm: {span: 6}
      },
      wrapperCol: {
        xs: {span: 24},
        sm: {span: 18}
      },
      searchLoading: false,
      searchDataTimer: 0,
      problemDimensionList: [],
      problemStatusList: [],
      problemCateList: [],
      projectStageList: [],
			tableColumnsOld: [
        {
          title: '序号',
          dataIndex: 'index',
          key: 'index',
          align: 'center',
          width: 60,
          customRender: (text, record, index) => record.parentId == 1?`${index+1}`:'',
        },
        {
          title: '审核状态',
          dataIndex: 'reviewStatus',
          align: 'center',
          width: 100,
          scopedSlots: { customRender: 'reviewStatus' }
        },
        {
          title: '电芯基本信息',
          children: [
            {
              title: '产品所属研究所',
              dataIndex: 'productDepartment',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'productDepartment' }
            },
            {
              title: '样品类型',
              dataIndex: 'orderType',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'orderType' }
            },
            {
              title: '产品名称',
              dataIndex: 'productName',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '项目等级',
              dataIndex: 'projectLevel',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'projectLevel' }
            },
            {
              title: '研制阶段',
              dataIndex: 'researchStage',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'researchStage' }
            },
            {
              title: '测试样品阶段',
              dataIndex: 'testSampleStage',
              align: 'center',
              width: 100,
              // scopedSlots: { customRender: 'testSampleStage' }
            },
            {
              title: '样品数量',
              dataIndex: 'testSampleNum',
              align: 'center',
              width: 100,
              customRender: (text, record, index)  => {
                if (record.parentId != 1) {//1为父级，显示数量，否则不显示
                  return '';
                }
                if (record.children == null|| record.children.length == 0) {
                  return 1;
                }
                return record.children.length + 1;
              }
            },
          ],
        },
        {
          title: '电芯测试信息',
          children: [
            {
              title: '委托单号',
              dataIndex: 'folderNo',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '测试项目名称',
              dataIndex: 'testProjectName',
              align: 'center',
              width: 125,
              scopedSlots: { customRender: 'clampText' }
            },
            {
              title: '电芯编码',
              dataIndex: 'cellCode',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '电芯批次',
              dataIndex: 'cellBatch',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '测试类别',
              dataIndex: 'testCate',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'testCate' }
            },
            {
              title: '失效类别',
              dataIndex: 'failureCate',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'failureCate' }
            },
            {
              title: '测试失效描述',
              dataIndex: 'testFailureDescription',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'clampText' }
            },
          ],
        },
        {
          title: 'DPV测试失效告知书',
          children: [
            {
              title: '发起人',
              dataIndex: 'initiatorName',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '发起时间',
              dataIndex: 'initiationTime',
              align: 'center',
              width: 100,
            },
            {
              title: '文件编号',
              dataIndex: 'fileCode',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'fileCode' }
            },
            // {
            //   title: '附件',
            //   dataIndex: 'fileName',
            //   align: 'center',
            //   width: 100,
            //   scopedSlots: { customRender: 'fileName' }
            // },
          ],
        },
      ],
      tableColumns:[
        {
          title: '序号',
          dataIndex: 'index',
          key: 'index',
          align: 'center',
          width: 60,
          customRender: (text, record, index) => record.parentId == 1?`${index+1}`:'',
        },
        {
          title: '登记信息',
          children: [
            {
              title: '审核状态',
              dataIndex: 'reviewStatus',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'reviewStatus' }
            },
            {
              title: '失效登记编号',
              dataIndex: 'fileCode',
              align: 'center',
              width: 100,
              ellipsis:true,
              scopedSlots: { customRender: 'fileCode' }
            },
            {
              title: '登记人',
              dataIndex: 'initiatorName',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '登记时间',
              dataIndex: 'initiationTime',
              align: 'center',
              width: 100,
            },
          ]
        },
        {
          title: '基本信息',
          children: [
            {
              title: '委托单号',
              dataIndex: 'folderNo',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '电芯编码',
              dataIndex: 'cellCode',
              align: 'center',
              width: 100,
              ellipsis:true,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '检测实验室',
              dataIndex: 'laboratoryId',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'laboratoryId' }
            },
            {
              title: '试验类型',
              dataIndex: 'testType',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'testType' }
            },
            {
              title: '测试类别',
              dataIndex: 'testCate',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'testCate' }
            },
            {
              title: '失效类别',
              dataIndex: 'failureCate',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'failureCate' }
            },
          ],
        },
        {
          title: '电芯测试信息',
          children: [
            {
              title: '委托部门',
              dataIndex: 'folderDepartment',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '委托人',
              dataIndex: 'folderSubmitter',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'simpleText' }
            },
            // {
            //   title: '产品所属研究所',
            //   dataIndex: 'productDepartment',
            //   align: 'center',
            //   width: 150,
            //   scopedSlots: { customRender: 'productDepartment' }
            // },
            {
              title: '产品名称',
              dataIndex: 'productName',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '产品技术状态',
              dataIndex: 'testSampleStage',
              align: 'center',
              width: 100,
              // scopedSlots: { customRender: 'testSampleStage' }
            },
            {
              title: '项目等级',
              dataIndex: 'projectLevel',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'projectLevel' }
            },
            {
              title: '研制阶段',
              dataIndex: 'researchStage',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'researchStage' }
            },
            {
              title: '样品类型',
              dataIndex: 'orderType',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'orderType' }
            },
            {
              title: '样品数量',
              dataIndex: 'testSampleNum',
              align: 'center',
              width: 100,
              customRender: (text, record, index)  => {
                if (record.parentId != 1) {//1为父级，显示数量，否则不显示
                  return '';
                }
                if (record.children == null|| record.children.length == 0) {
                  return 1;
                }
                return record.children.length + 1;
              }
            },
            {
              title: '测试项目名称',
              dataIndex: 'testProjectName',
              align: 'center',
              width: 125,
              ellipsis:true,
              scopedSlots: { customRender: 'clampText' }
            },
            {
              title: '电芯批次',
              dataIndex: 'cellBatch',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '测试失效描述',
              dataIndex: 'testFailureDescription',
              align: 'center',
              width: 100,
              ellipsis:true,
              scopedSlots: { customRender: 'clampText' }
            },
          ],
        },
      ],
      tableData: [],
      initData: {},
		}
	},
	watch: {
    tableList(newVal, oldVal) {

      // document.documentElement.style.setProperty(`--height`, `${this.tableHeight - subtrahend}px`)
      document.documentElement.style.setProperty(`--height`, document.documentElement.clientHeight - 40 - 20 - 24 - 10 - 32 - 10 - 92 - (this.isShowAllSearch == true?40:0) + (this.tableList.length>0?0:30) + `px`)

    },
    isShowAllSearch(newVal, oldVal) {

      // document.documentElement.style.setProperty(`--height`, `${this.tableHeight - subtrahend}px`)
      document.documentElement.style.setProperty(`--height`, document.documentElement.clientHeight - 40 - 20 - 24 - 10 - 32 - 10 - 92 - (this.isShowAllSearch == true?40:0) + (this.tableList.length>0?0:30) + `px`)
    }
	},
	created() {
    // this.getJiraOptionList();
    // console.log(this.listType);
    setTimeout(() => {
      this.getJiraOptionList();
      // this.getData(this.queryParam);
    }, 200);
	},
	mounted() {
    document.documentElement.style.setProperty(`--height`, document.documentElement.clientHeight - 40 - 20 - 24 - 10 - 32 - 10 - 92 - (this.isShowAllSearch == true?40:0) + (this.tableList.length>0?0:30) + `px`)

  },
	methods: {

    handleChangeSearch() {
      this.isShowAllSearch = !this.isShowAllSearch;
      this.tableHeight = this.isShowAllSearch ? this.templateHeight - 80 : this.templateHeight - 40
    },
    addOpen() {
      this.$refs.addForm.add();
    },
    editOpen(record){
      // this.searchLoading = true;
      this.$refs.editForm.edit(record);
    },
    handleOk() {
      this.getData(this.queryParam);
    },
    filePreviewOnClose(){
      this.filePreviewVisible = false;
    },
    previewFile(fileId) {
      this.fileUrl = this.previewBaseUrl + fileId;
      //判断，pdf进行预览，非pdf触发下载
      console.log(this.fileUrl);
      this.filePreviewVisible = true;
    },

    // getJiraOptionList() {
    //   getJiraOptionList({fieldName:'problemDimension'}).then(res => {
    //     this.problemDimensionList = res.data;
    //   })
    //   getJiraOptionList({fieldName:'problemStatus'}).then(res => {
    //     this.problemStatusList = res.data;
    //   })
    //   // getJiraOptionList({fieldName:'problemCate'}).then(res => {
    //   //   this.problemCateList = res.data;
    //   // })
    //   getJiraOptionList({fieldName:'projectStage'}).then(res => {
    //     this.projectStageList = res.data;
    //   })
    //
    //   getProductList({productOrProject:1}).then(res => {
    //     if (res.success) {
    //       this.productList = res.data;
    //     } else {
    //       this.productList = [];
    //     }
    //   })
    // },
    resetSearch() {
      this.queryParam.fileCode = null
      this.queryParam.productName = null
      this.queryParam.cellCode = null
      this.queryParam.testProjectName = null
      this.queryParam.testCateList = []
      this.queryParam.failureCateList = []
      this.$refs.table.refresh()
    },
    searchData() {
      console.log(this.queryParam);
      if (this.searchDataTimer === 0 ) {//首次调用，设置定时器
        this.searchDataTimer = setTimeout (() => {
          this.getData(this.queryParam)//  调用数据请求方法
        }, 600)
      } else {
        clearTimeout(this.searchDataTimer)//多次调用，取消定时器，重新设置
        this.searchDataTimer = setTimeout (() => {
          this.getData(this.queryParam)//  调用数据请求方法
        }, 600)
      }
    },
    selectProduct(value, label, extra) {
      var product = this.productList.filter(item => item.issueId == value)[0];
      this.addForm.setFieldsValue({
        productName : product.productName
      })
    },
    handleTabsChange(value) {
      this.laboratoryId = value
      // this.filterData = ''
      // this.getTodoTaskList()
      this.$refs.table.refresh()
    },
    editReviewResult(id) {
      // console.log(id);
      const $i = this.tableData.findIndex(item => id === item.id);
      this.tableData[$i].editable = true
      // console.log(this.tableData);
    },

    selectReviewTodoUser() {
      this.todoPushVisible = true
    },
    pushReviewTodo() {
      const {
        form: { validateFields }
      } = this
      this.todoPushConfirmLoading = true
      validateFields((errors, values) => {
        if (!errors) {
          let $params = { ...values }
          oaTodoPushProductProblem($params)
            .then(res => {
              console.log(res);
              if (res.success) {
                if (res.data.result == "1") {
                  this.$message.success(res.data.message)
                  this.visible = false
                  this.todoPushConfirmLoading = false
                  this.handleCancel()
                } else if (res.data.result == "0") {
                  //弹出确认框
                  this.$confirm({
                    // iconClass: 'el-icon-question', //自定义图标样式
                    title: "提示",
                    content: res.data.message,
                    // confirmButtonText: '确认', //确认按钮文字更换
                    // cancelButtonText: '取消', //取消按钮文字更换
                    // showClose: true, //是否显示右上角关闭按钮
                    type: "warning", //提示类型  success/info/warning/error
                    onOk: () => {
                      $params = {
                        ...values,
                        repeatPush: 1
                      }
                      oaTodoPushProductProblem($params).then(res => {
                        if (res.success) {
                          this.$message.success(res.data.message)
                          this.visible = false
                          this.todoPushConfirmLoading = false
                          this.handleCancel()
                        } else {
                          this.$message.error("待办推送失败" + res.message)
                        }
                      })
                    }
                  })
                } else {
                  this.$message.error("待办推送失败：" + res.message)
                }
              } else {
                this.$message.error("待办推送失败：" + res.message)
              }
            })
            .finally(res => {
              this.todoPushConfirmLoading = false
            })
        } else {
          this.todoPushConfirmLoading = false
          this.todoPushVisible = false
        }
      })
    },
    doneReviewTodo() {
      oaTodoDonePushProductProblem({})
        .then(res => {
          console.log(res);
        })
        .finally(() => { })
    },
    cancelReviewTodo() {
      oaTodoCancelPushProductProblem({})
        .then(res => {
          console.log(res);
          if (res.success) {
            this.$message.success(res.data.message)
          } else {
            this.$message.error("待办取消失败：" + res.message)
          }
        })
        .finally(() => { })
    },
    //待办用户选择
    todoPushCustomRow(row, index) {
      return {
        on: {
          click: () => {
            this.form.setFieldsValue({
              userName: row.account
            })
            this.userNameDisplay = row.name
            this.dropdownvisible = false
          }
        }
      }
    },
    todoOnSearch(e) {
      this.$refs.todoTablePeople.refresh()
    },


    handleCancel() {
      this.selectvisible = false
      this.dropdownvisible = false
      this.userQueryParam.searchValue = null
      this.todoPushVisible = false
      this.form.setFieldsValue({
        userName: ""
      })
      this.userNameDisplay = ""
    },
    handleToJira(issueKey) {
      console.log(issueKey);
      if (issueKey == null) {
        return;
      }
      let $url = `http://jira.evebattery.com/browse/` + issueKey + `?auth=` + Vue.ls.get("jtoken");
      console.log($url);
      // let $url = `http://jira.evebattery.com/browse/` + record.issueKey;
      window.open($url, "_blank");
    },

    handleDetail() {
      // this.$router.push({path: "/problemDetail"})
      this.$router.push({
        path: "/problemDetail",
        query: {}
      })
      // system/productQualityPlatform/problemBoard/problemDetail
    },

    callFileInfoDownload (fileId) {
      this.loading = true
      sysFileInfoDownload({ id: fileId }).then((res) => {
        this.loading = false
        this.downloadfile(res)
      }).catch((err) => {
        this.loading = false
        this.$message.error('下载错误：获取文件流错误')
      })
    },
    downloadfile (res) {
      var blob = new Blob([res.data], { type: 'application/octet-stream;charset=UTF-8' })
      var contentDisposition = res.headers['content-disposition']
      var patt = new RegExp('filename=([^;]+\\.[^\\.;]+);*')
      var result = patt.exec(contentDisposition)
      var filename = result[1]
      var downloadElement = document.createElement('a')
      var href = window.URL.createObjectURL(blob) // 创建下载的链接
      var reg = /^["](.*)["]$/g
      downloadElement.style.display = 'none'
      downloadElement.href = href
      downloadElement.download = decodeURI(filename.replace(reg, '$1')) // 下载后文件名
      document.body.appendChild(downloadElement)
      downloadElement.click() // 点击下载
      document.body.removeChild(downloadElement) // 下载完成移除元素
      window.URL.revokeObjectURL(href)
    },

    getData(queryParam) {
      this.searchLoading = true
      getDpvTestFailureList(queryParam).then(res => {
        this.tableData = res.data
      }).finally(res => {
        this.searchLoading = false
      })
    },
    getJiraOptionList(){
      getJiraOptionList({fieldName:'department'}).then(res => {
        var list=['18863','22101','18846','22105','18711','22269'];
        this.productDepartmentList = res.data.filter(e => list.includes(e.id));
        console.log(this.productDepartmentList);
        // this.productDepartmentList = res.data;
      })
      getProductList({}).then(res => {
        if (res.success) {
          this.productList = res.data.filter(e => e.productOrProject == 1);
          // this.productList = res.data;
        } else {
          this.productList = [];
        }
      })
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
  }
}
</script>
<style lang="less" scoped>
@import '/src/components/pageTool/style/pbiSearchItem.less';
:root {
	--height: 600px;
}

/* 标题 */
.head-title {
	display: flex;
	align-items: center;
}
.head-title .line {
	width: 4px;
	height: 22px;
	background: #3293ff;
	border-radius: 20px;
}
.head-title .title {
  color: #333;
	font-size: 18px;
	font-weight: 600;
}
/* 筛选 */
.filter-wrapper {
	display: flex;
	justify-content: space-between;
}
.filter-left {
	display: flex;
  margin-right: auto;
}
.filter-right {
	display: flex;
  margin-left: auto;
}
/* 表格 */
.table-wrapper {
	padding: 10px;
	background: #fff;
	border-radius: 10px;
}

.red {
  display: block;
  background: #ff3333;
  text-align: center;
  color: #fff;
}

.yellow {
  display: block;
  background: #fac858;
  text-align: center;
  color: #fff;
}

.green {
  display: block;
  background: #58a55c;
  text-align: center;
  color: #fff;
}

.btn_pn {
  display: block;
  min-height: 18px;
  min-width: 70px;
}

/deep/ .problemStatusSelect .ant-select-selection {
  background-color: rgba(255, 255, 255, 0);
  border: none;
}
.problem-status-show {
  justify-content: center;
  display: flex;
  align-items: center;
}
.problem-status-show .circle {
  width: 13px;
  height: 13px;
  border-radius: 50%;
  margin-right: 8px;
}

.select-box {
  display: flex;
  align-items: center;
}
.select-box .circle {
  width: 13px;
  height: 13px;
  border-radius: 50%;
  margin-right: 8px;
}

/* 通用  */

.status-lamp {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  margin: auto;
  flex-shrink:0;
}

.mr10 {
	margin-right: 10px;
}
.mr12{
	margin-right: 12px;
}
.mt10 {
	margin-top: 10px;
}
.mt5 {
  margin-top: 5px;
}

.filter-select {
	width: 200px;
}
.filter-input {
	width: 200px;
}

/* 表格组件 */
///deep/ .ant-table tr th {
//	background: #f4f4f4;
//	font-size: 13px;
//
//  padding: 8px 5px !important;
//}
//
///deep/ .ant-table tr td {
//  padding: 8px 5px !important;
//}

/deep/.ant-table-body {
	height: var(--height) !important;
	overflow-y: scroll;
}

/deep/.ant-table-thead {
  position: sticky;
  top: 0;
  z-index: 10;
}
/deep/ .ant-pagination {
	margin: 5px 0 0;
}
/deep/ .ant-table-placeholder {
	border: none !important; 
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.tab {
  padding: 1px;
  background-color: #fff;
  /* box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.25); */
}


/deep/.pbi-tabs{
  font-size: 14px;
  margin-top: 5px;
}
/deep/.pbi-tab-item{
  padding: 12px 16px;
}
/deep/.pbi-tab-active{
  font-size: 15px;
}

/deep/.s-table-tool{
  padding-bottom:0;
}
/deep/ .ant-table-middle > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th{
  padding: 5px;
  font-size: 13px;
  color: rgba(0, 0, 0, .85);
  font-weight: 500;
}
/deep/ .ant-table-middle > .ant-table-content > .ant-table-body > table > .ant-table-tbody > tr > td{
  padding: 4px;
  color: #333;
  font-size: 12px;
  font-weight: 400;
}
/deep/.ant-select-sm .ant-select-selection--single {
  height: 24px;
}
/deep/.ant-select-sm .ant-select-selection__rendered {
   line-height: 24px;
}
</style>
