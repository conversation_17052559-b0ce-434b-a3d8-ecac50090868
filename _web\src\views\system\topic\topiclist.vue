<template>
  <div>
    <div class="title">
      <span style="float:left;">{{titles[this.listType]}}</span>

      <a-button type="primary" style="margin-left: 8px;width: 100px;height: 30px;" @click="selectReviewTodoUser" v-if="this.listType == 4&&this.showTodoPushBtn">推送评审待办</a-button>
      <a-button type="primary" style="margin-left: 8px;width: 100px;height: 30px;" @click="cancelReviewTodo" v-if="this.listType == 4&&this.showTodoPushBtn">取消评审待办</a-button>
<!--      <a-button type="primary" style="margin-left: 8px;width: 100px;height: 30px;" @click="doneReviewTodo" v-if="this.listType == 4 && this.showTodoPushBtn">办结评审待办</a-button>-->
      <a-button type="primary" style="margin-left: 8px;width: 100px;height: 30px;" @click="showModal" >结束评审</a-button>
<!--      <a class="btn" @click="showModal">结束评审</a>-->

    </div>

    <x-card >
      <div slot="content" class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="24">
            <a-col v-if="this.listType == 3" :md="6" :sm="8">
              <a-form-item label="创建日期">
                <a-range-picker size="small"  @change="dateChange" />
              </a-form-item>
            </a-col>
            <a-col :md="4" :sm="8">
              <a-form-item label="">
                <treeselect :limit="1" @input="change" :max-height="200" placeholder="请选择部门" value-consists-of="BRANCH_PRIORITY" v-model="paramquery.deptId" :multiple="true" :options="_depts" :normalizer="normalizer" />
              </a-form-item>
            </a-col>
            <a-col :md="4" :sm="8">
              <a-form-item label="">
               <treeselect :limit="1" @input="change" :max-height="200" placeholder="请选择评审结果状态" value-consists-of="ALL" v-model="paramquery.statuses" :multiple="true" :options="statuses" />
              </a-form-item>
            </a-col>
            <a-col :md="4" :sm="8">
              <a-form-item label="">
                <div class="input-name">
                  <a-input size="small"  @keyup.enter.native="change" v-model="paramquery.projectName"  placeholder="请输入课题名称"/>
                </div>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </x-card>

    <div style="background:#fff">
      <a-spin :spinning="loading">

        <a-table style="background:#fff" size="small" :scroll="{ x: 1300 }"

                 ref="table" :rowKey="(record) => record.issueId" :columns="listType == 3?columnsProject:columns" :dataSource="list"
                 :loading="loading" >


          <div slot="cate1">1级<br/>分类</div>
          <div slot="projectNameTitle">项目名称</div>
          <div slot="projectLevelTitle">课题等级</div>
          <div slot="projectLeaderTitle">课题负责人</div>
          <div slot="platformAndTopicTitle">平台<br/>课题</div>


          <div slot="affiliatedPlatform" slot-scope="text,record,index,column">
            <a-tree-select style="width:100%"
                           :value="record[column.dataIndex]"
                           :dropdown-style="{ maxHeight: '300px', overflow: 'auto' }"
                           @change="changePlatformTree($event,record,column)"
                           :autoExpandParent="false"
                           :tree-data="affiliatedPlatformTreeData" placeholder="请选择分类">
            </a-tree-select>
          </div>

          <div slot="cate" slot-scope="text,record">
            <a-tree-select style="width:100%"
                           :value="record.cate1 != null&& record.cate1 != ''?record.cate1+(record.cate2 != null
                           && record.cate2 != ''?'-'+(record.cate2 + (record.cate3 != null && record.cate3  != ''?'-'+record.cate3:'')):''):''"
                           :dropdown-style="{ maxHeight: '300px', overflow: 'auto' }"

                           @change="changeCate($event,record)"
                           :autoExpandParent="false"
                           :tree-data="cate" placeholder="请选择分类">
            </a-tree-select>
          </div>

          <div slot="department" slot-scope="text,record">
            <a-tree-select style="width:100%"
                           :value="record.department2 != null && record.department2!=''?record.department2+(record.department3 != null&& record.department3!=''?'-'+record.department3:'') :''"
                           @change="changeDepts($event,record)"
                           :autoExpandParent="false"
                           :dropdown-style="{ maxHeight: '300px', overflow: 'auto' }"
                           :tree-data="depts" placeholder="请选择部门">
            </a-tree-select>
          </div>

          <div slot="projectName" slot-scope="text,record">
            <div @click="() => editFullTxt(record.issueId,0)">
              <div v-if="!record.editFull0" class="ellipsis">{{text}}</div>
              <a-textarea style="font-size:12px" :auto-size="{ minRows: 1, maxRows:5 }" v-model="record.projectName"
                          v-else v-focus='record.editFull0' @blur="(e) => {
												const { value } = e.target
                        
												record.projectName = value
                        callUpdateReviewUncheck(record)
											}"/>
            </div>
          </div>

          <div slot="platformAndTopic" slot-scope="text,record" style="width: 100%;text-align: center">
            <a-select :default-value="text" :dropdownMatchSelectWidth="false"
                      @change="changeSelect($event,record,'issueType')">
              <a-select-option value="1">
                平台
              </a-select-option>
              <a-select-option value="2">
                课题
              </a-select-option>
            </a-select>
          </div>

          <div slot="projectLevel" slot-scope="text,record" style="width: 100%;text-align: center">
            <a-select :key="record.issueId" v-if="record.platformAndTopic != 2" :default-value="text == 0 ? '-': parseInt(text)" :dropdownMatchSelectWidth="false"  @change="changeSelect($event,record,'projectLevel')">
              
              <a-select-option :value="parseInt(2)">
                S
              </a-select-option>
              <a-select-option :value="parseInt(3)">
                A
              </a-select-option>
              <a-select-option :value="parseInt(4)">
                B
              </a-select-option>

            </a-select>

          </div>

<!--          <div slot="researchContent" slot-scope="text,record">
            <div @click="() => editFullTxt(record.issueId,1)">
              <div v-if="!record.editFull1" class="ellipsis">{{text}}</div>
              <a-textarea style="font-size:12px" :auto-size="{ minRows: 1 ,maxRows:5}" v-else v-focus='record.editFull1'
                          v-model="record.researchContent" @blur="(e) => {
												const { value } = e.target
                        
												record.researchContent = value
                        callUpdateReviewUncheck(record)
											}"/>
            </div>
          </div>-->
          <div slot="researchContent" slot-scope="text">
            <clamp :text="text" :sourceText="[text]" :isCenter="true"></clamp>
          </div>

<!--          <div slot="projectBackGround" slot-scope="text,record">
            <div @click="() => editFullTxt(record.issueId,2)">
              <div v-if="!record.editFull2" class="ellipsis">{{text}}</div>
              <a-textarea style="font-size:12px" :auto-size="{ minRows: 1 ,maxRows:5}" v-else v-focus='record.editFull1'
                          v-model="record.projectBackGround" @blur="(e) => {
												const { value } = e.target

												record.projectBackGround = value
                        callUpdateReviewUncheck(record)
											}"/>
            </div>
          </div>-->
          <div slot="projectBackGround" slot-scope="text">
            <clamp :text="text" :sourceText="[text]" :isCenter="true"></clamp>
          </div>

          <div style="text-align:center" slot="projectLeader" slot-scope="text,record" @click="projectLeaderClick(record)">
            <span style="cursor:pointer; width:100%;">{{text ? text : '选择责任人'}}
            </span>
          </div>

          <div slot="reviewResult1" slot-scope="text,record">
            <div v-if="record.secondReview == 0" @click="() => edit(record.issueId)">
              <a-select :open="true" placeholder="请选择" :autoFocus='true'
                        :default-value="reviewRes[text]"
                        @select="(val) => {
                          change1(val, record)
                        }"

                        @blur="(val) => {
                          record.editable = false
                        }"
                        v-if="record.editable" style="width: 100%"
              >
                <a-select-option :value="parseInt(0)">
                  请选择
                </a-select-option>
                <a-select-option :value="parseInt(1)">
                  通过
                </a-select-option>
                <a-select-option :value="parseInt(2)">
                  不通过
                </a-select-option>
                <a-select-option :value="parseInt(3)">
                  再确认
                </a-select-option>
              </a-select>
              <span v-else-if="!record.editable">
                <span class="green" v-if="text == 1">{{reviewRes[text]}}</span>
                <span class="red" v-else-if="text == 2">{{reviewRes[text]}}</span>
                <span class="yellow" v-else-if="text == 3">{{reviewRes[text]}}</span>
                <span class="btn_pn" v-else>请选择</span>
              </span>
            </div>
            <div v-else>
              <span class="green" v-if="text == 1">{{reviewRes[text]}}</span>
              <span class="red" v-else-if="text == 2">{{reviewRes[text]}}</span>
              <span class="yellow" v-else-if="text == 3">{{reviewRes[text]}}</span>
              <span class="btn_pn" v-else>请选择</span>
            </div>
          </div>

          <template slot="reviewResult2" slot-scope="text,record">
            <div v-if="record.secondReview == 1 && record.reviewResult1 == 3" @click="() => edit(record.issueId)">
              <a-select :open="true"
                        :autoFocus='true'
                        @select="(val) => {
                          change2(val, record)
                        }"
                        @blur="(val) => {
                          record.editable = false
                        }"
                        :default-value="reviewRes[text]" v-if="record.editable" style="width: 100%"
              >
                <a-select-option :value="parseInt(0)">
                  请选择
                </a-select-option>
                <a-select-option :value="parseInt(1)">
                  通过
                </a-select-option>
                <a-select-option :value="parseInt(2)">
                  不通过
                </a-select-option>

              </a-select>
              <span v-if="!record.editable">
                <span class="green" v-if="text == 1">{{reviewRes[text]}}</span>
                <span class="red" v-else-if="text == 2">{{reviewRes[text]}}</span>
                <span class="yellow" v-else-if="text == 3">{{reviewRes[text]}}</span>
                <span class="btn_pn" v-else>请选择</span>
              </span>
            </div>
          </template>

          <span slot="reviewOpinion1" slot-scope="text,record">
              <div v-if="record.secondReview == 0">
                <a-textarea
                  :auto-size="{ maxRows:5,minRows:1 }"
                  style="width:100%"
                  v-model="record.reviewOpinion1"
                  @blur="(e) => {
												const { value } = e.target
												record.reviewOpinion1 = value
                        callUpdateReview(record)
											}"
                  placeholder="填写评审意见"
                />
              </div>
              <span v-else>
                  <span>{{text}}</span>
              </span>
          </span>

          <span slot="reviewOpinion2" slot-scope="text,record">
              <div v-if="record.secondReview == 1 && record.reviewResult1 == 3">
                <a-textarea
                  :auto-size="{ maxRows:5,minRows:1 }"
                  style="width:100%"
                  v-model="record.reviewOpinion2"
                  @blur="(e) => {
												const { value } = e.target
												record.reviewOpinion2 = value
                        callUpdateReview(record)
											}"
                  placeholder="填写评审意见"
                />
              </div>
              <span v-else>
                  <span>{{text}}</span>
              </span>
          </span>


          <!-- <span slot="action" slot-scope="text,record">
            <a @click="$refs.editForm.open(record,listType)">填写评审结果</a>
          </span> -->


        </a-table>
        <edit-form ref="editForm" @ok="handleOk"/>
        <review-summary ref="reviewSummary"></review-summary>
      </a-spin>
    </div>

    <a-modal
      :title="year+'年'+month+'月'+day+'日评审报告'"
      :width="width"
      v-model="visible"
      :zIndex="parseInt(2)"
      :bodyStyle="{overflow:'hidden',overflowY: 'scroll',maxHeight:clientHeight - 180 + 'px'}"
      @ok="() => callTransitionReview()"
      @cancel="() => this.visible = false"
    >

      <template slot="footer">
        <a-button key="submit" type="primary" :loading="loading" @click="callTransitionReview()">
          确认评审结果
        </a-button>
        <a-button key="back" @click="() => visible=false">
          取消
        </a-button>

      </template>

      <div>
        <div class="title1">一、立项小结分析</div>
        <div style="background-color: white;display: flex;">


          <div class="box1" style="position: relative" :style="{position:'relative',height:height1+'px'}">
            <div id="chart1" :style="{height:height1+'px'}"></div>

            <table align="center" style="border: 1px solid rgba(184,184,184,0.13);width: 60%;color: #000c17;    margin-top: -15%;">
              <tr style="background-color:  rgba(184,184,184,0.13)">
                <th style="text-align: center">通过</th>
                <th style="text-align: center">不通过</th>
                <th style="text-align: center">再确认</th>
                <th style="text-align: center">未审核</th>
              </tr>
              <tr>
                <td style="text-align: center">{{passTotal}}</td>
                <td style="text-align: center">{{unPassTotal}}</td>
                <td style="text-align: center">{{unConfirmTotal}}</td>
                <td style="text-align: center">{{unCheckTotal}}</td>
              </tr>
            </table>
            <div style="position: absolute;bottom: -7px;text-align: center;width: 100%">
              <span style="color: rgb(70 70 70);font-size: 18px;font-weight: bold;width: 100%;">本次技术课题审核分析</span>
            </div>

          </div>


          <div class="box2" id="chart2" :style="{height:height1+'px'}">
            <a-spin :spinning="chart2spinning"></a-spin>
          </div>

        </div>
        <div class="title1" style="padding-top: 30px">二、等级情况分析</div>
        <div style="background-color: white;display: flex;">

          <div class="box3" id="chart3" :style="{height:height1+'px'}"></div>


          <div class="box4" id="chart4" :style="{height:height1+'px'}">
            <a-spin :spinning="chart4spinning"></a-spin>
          </div>

        </div>

        <div class="title1" style="padding-top: 30px">三、评审明细清单 <a style="color:rgba(0, 0, 0, 0.65)" @click="swich"><a-icon :type="up ? 'up':'down'" /></a></div>

        <a-table v-show="up" style="background:#fff" size="small"

                 :scroll="{x:bigClient?false:true}"

                 :rowKey="(record) => record.issueId" :columns="columnsIn" :dataSource="listIn" :loading="loading"
                 >

          <div slot="affiliatedPlatform" slot-scope="text,record,index,column">
            <a-tree-select style="width:100%"
                           :value="record[column.dataIndex]"
                           :dropdown-style="{ maxHeight: '300px', overflow: 'auto' }"
                           @change="changePlatformTree($event,record,column)"
                           :autoExpandParent="false"
                           :tree-data="affiliatedPlatformTreeData" placeholder="请选择分类">
            </a-tree-select>
          </div>

          <div slot="cate" slot-scope="text,record">
            <a-tree-select style="width:100%"
                           :value="record.cate1 != null&& record.cate1!=''?record.cate1+(record.cate2 != null&& record.cate2!=''
                           ?'-'+(record.cate2 + (record.cate3 != null&& record.cate3!=''?'-'+record.cate3:'')):''):''"
                           :dropdown-style="{ maxHeight: '300px', overflow: 'auto' }"

                           @change="changeCate($event,record)"
                           :autoExpandParent="false"
                           :tree-data="cate" placeholder="请选择分类">
            </a-tree-select>
          </div>

          <div slot="department" slot-scope="text,record">
            <a-tree-select style="width:100%"
                           :value="record.department2 != null?record.department2+(record.department3 != null?'-'+record.department3:'') :''"
                           @change="changeDepts($event,record)"
                           :autoExpandParent="false"
                           :dropdown-style="{ maxHeight: '300px', overflow: 'auto' }"
                           :tree-data="depts" placeholder="请选择部门">
            </a-tree-select>
          </div>


          <div slot="projectName" slot-scope="text,record">
            <div @click="() => editFullInTxt(record.issueId,0)">
              <div v-if="!record.editFull0" class="ellipsis">{{text}}</div>
              <a-textarea style="font-size:12px" :auto-size="{ minRows: 1, maxRows:5 }" v-model="record.projectName"
                          v-else v-focus='record.editFull0' @blur="(e) => {
												const { value } = e.target

												record.projectName = value
                        callUpdateReviewUncheck(record)
											}"/>
            </div>
          </div>


          <div slot="projectLevel" slot-scope="text,record" style="width: 100%;text-align: center">
            <a-select :key="record.issueId" :default-value="text == 0 ? '-': parseInt(text)"
                      :dropdownMatchSelectWidth="false" @change="changeSelectIn($event,record,'projectLevel')">

              <a-select-option :value="parseInt(2)">
                S
              </a-select-option>
              <a-select-option :value="parseInt(3)">
                A
              </a-select-option>
              <a-select-option :value="parseInt(4)">
                B
              </a-select-option>

            </a-select>

          </div>

<!--          <div slot="researchContent" slot-scope="text,record">
            <div @click="() => editFullInTxt(record.issueId,1)">
              <div v-if="!record.editFull1" class="ellipsis">{{text}}</div>
              <a-textarea style="font-size:12px" :auto-size="{ minRows: 1 ,maxRows:5}" v-else v-focus='record.editFull1'
                          v-model="record.researchContent" @blur="(e) => {
												const { value } = e.target

												record.researchContent = value
                        callUpdateReviewUncheck(record)
											}"/>
            </div>
          </div>-->
          <div slot="researchContent" slot-scope="text">
            <clamp :text="text" :sourceText="[text]" :isCenter="true"></clamp>
          </div>

<!--          <div slot="projectBackGround" slot-scope="text,record">
            <div @click="() => editFullInTxt(record.issueId,2)">
              <div v-if="!record.editFull2" class="ellipsis">{{text}}</div>
              <a-textarea style="font-size:12px" :auto-size="{ minRows: 1 ,maxRows:5}" v-else v-focus='record.editFull1'
                          v-model="record.projectBackGround" @blur="(e) => {
												const { value } = e.target

												record.projectBackGround = value
                        callUpdateReviewUncheck(record)
											}"/>
            </div>
          </div>-->
          <div slot="projectBackGround" slot-scope="text">
            <clamp :text="text" :sourceText="[text]" :isCenter="true"></clamp>
          </div>

          <div style="text-align:center" slot="projectLeader" slot-scope="text,record"
               @click="projectLeaderClick(record)">
            <span style="cursor:pointer; width:100%;">{{text ? text : '选择责任人'}}
            </span>
          </div>

          <div slot="cate1">1级<br/>分类</div>
          <div slot="cate2">2级<br/>分类</div>
          <div slot="cate3">3级<br/>分类</div>
          <div slot="projectNameTitle">项目<br/>名称</div>
          <div slot="projectLevelTitle">课题等级</div>
          <div slot="projectLeaderTitle">课题负责人</div>
          <div slot="platformAndTopic">平台<br/>课题</div>

          <div slot="reviewResult1" slot-scope="text,record">
            <div v-if="record.secondReview == 0" @click="() => editpop(record.issueId)">
              <a-select :open="true" placeholder="请选择" :autoFocus='true'
                        @select="(val) => {
                          changeIn1(val, record)
                        }"

                        @blur="(val) => {
                          record.editable = false
                        }"

                        :default-value="reviewRes[text]" v-if="record.editable" style="width: 100%"
              >
                <a-select-option :value="parseInt(0)">
                  请选择
                </a-select-option>
                <a-select-option :value="parseInt(1)">
                  通过
                </a-select-option>
                <a-select-option :value="parseInt(2)">
                  不通过
                </a-select-option>
                <a-select-option :value="parseInt(3)">
                  再确认
                </a-select-option>
              </a-select>
              <span v-else-if="!record.editable">
                <span class="green" v-if="text == 1">{{reviewRes[text]}}</span>
                <span class="red" v-else-if="text == 2">{{reviewRes[text]}}</span>
                <span class="yellow" v-else-if="text == 3">{{reviewRes[text]}}</span>
                <span class="btn_pn" v-else>请选择</span>
              </span>
            </div>
            <div v-else>
              <span class="green" v-if="text == 1">{{reviewRes[text]}}</span>
              <span class="red" v-else-if="text == 2">{{reviewRes[text]}}</span>
              <span class="yellow" v-else-if="text == 3">{{reviewRes[text]}}</span>
              <span class="btn_pn" v-else>请选择</span>
            </div>
          </div>

          <template slot="reviewResult2" slot-scope="text,record">
            <div v-if="record.secondReview == 1 && record.reviewResult1 == 3" @click="() => editpop(record.issueId)">
              <a-select placeholder="请选择" :open="true"
                        @select="(val) => {
                          changeIn2(val, record)
                        }"

                        @blur="(val) => {
                          record.editable = false
                        }"
                        :autoFocus='true'
                        :default-value="reviewRes[text]" v-if="record.editable" style="width: 100%"
              >
                <a-select-option :value="parseInt(0)">
                  请选择
                </a-select-option>
                <a-select-option :value="parseInt(1)">
                  通过
                </a-select-option>
                <a-select-option :value="parseInt(2)">
                  不通过
                </a-select-option>

              </a-select>
              <span v-if="!record.editable">
                <span class="green" v-if="text == 1">{{reviewRes[text]}}</span>
                <span class="red" v-else-if="text == 2">{{reviewRes[text]}}</span>
                <span class="yellow" v-else-if="text == 3">{{reviewRes[text]}}</span>
                <span class="btn_pn" v-else>请选择</span>
              </span>
            </div>
          </template>

          <span slot="reviewOpinion1" slot-scope="text,record">
              <div v-if="record.secondReview == 0">
                <a-textarea
                  style="width:100%"
                  :auto-size="{ maxRows:5,minRows:1 }"
                  v-model="record.reviewOpinion1"
                  @blur="(e) => {
												const { value } = e.target
												record.reviewOpinion1 = value
                        callUpdateReview(record)
											}"
                  placeholder="填写评审意见"
                />
              </div>
              <span v-else>
                  <span>{{text}}</span>
              </span>
          </span>

          <span slot="reviewOpinion2" slot-scope="text,record">
              <div v-if="record.secondReview == 1 && record.reviewResult1 == 3">
                <a-textarea
                  :auto-size="{ maxRows:5,minRows:1 }"
                  style="width:100%"
                  v-model="record.reviewOpinion2"
                  @blur="(e) => {
												const { value } = e.target
												record.reviewOpinion2 = value
                        callUpdateReview(record)
											}"
                  placeholder="填写评审意见"
                />
              </div>
              <span v-else>
                  <span>{{text}}</span>
              </span>
          </span>

          <span slot="reviewOpinion1" slot-scope="text,record">
              <div v-if="record.secondReview == 0">
                <a-textarea
                  :auto-size="{ maxRows:5,minRows:1 }"
                  style="width:100%"
                  v-model="record.reviewOpinion1"
                  @blur="(e) => {
												const { value } = e.target
												record.reviewOpinion1 = value
                        callUpdateReview(record)
											}"
                  placeholder="填写评审意见"
                />
              </div>
              <span v-else>
                  <span>{{text}}</span>
              </span>
          </span>

          <span slot="reviewOpinion2" slot-scope="text,record">
              <div v-if="record.secondReview == 1 && record.reviewResult1 == 3">
                <a-textarea
                  style="width:100%"
                  :auto-size="{ maxRows:5,minRows:1 }"
                  v-model="record.reviewOpinion2"
                  @blur="(e) => {
												const { value } = e.target
												record.reviewOpinion2 = value
                        callUpdateReview(record)
											}"
                  placeholder="填写评审意见"
                />
              </div>
              <span v-else>
                  <span>{{text}}</span>
              </span>
          </span>


        </a-table>
      </div>

    </a-modal>
    <a-modal
      :title="dateMonthString+'月评审清单明细'"
      :width="width"
      v-model="xmglbVisible"
      :zIndex="parseInt(2)"
      :bodyStyle="{overflow:'hidden',overflowY: 'scroll',maxHeight:clientHeight - 180 + 'px'}"
      @ok="() => callTransitionReview()"
      @cancel="() => this.xmglbVisible = false"
    >

      <div style="float:right;padding: 10px 0;position: relative;z-index: 2;">
        <span>
          评审月份:
        </span>
        <a-month-picker format="yyyy-MM" style="width: 200px;margin-left: 10px" @change="onChangeMonth"
                        placeholder = "月份"
                        v-model="dateMonth">
        </a-month-picker>
      </div>


      <template slot="footer">
        <a-button key="submit" type="primary" :loading="loading" @click="callTransitionReview()">
          确认评审结果
        </a-button>
        <a-button key="back" @click="() => xmglbVisible=false">
          取消
        </a-button>

      </template>

      <div>

        <a-table style="background:#fff" size="small"

                 :scroll="{x:bigClient?false:true}"

                 :rowKey="(record) => record.issueId" :columns="columnsIn" :dataSource="listIn" :loading="loading"
                 bordered>

          <div slot="affiliatedPlatform" slot-scope="text,record,index,column">
            <a-tree-select style="width:100%"
                           :value="record[column.dataIndex]"
                           :dropdown-style="{ maxHeight: '300px', overflow: 'auto' }"
                           @change="changePlatformTree($event,record,column)"
                           :autoExpandParent="false"
                           :tree-data="affiliatedPlatformTreeData" placeholder="请选择分类">
            </a-tree-select>
          </div>

          <div slot="cate" slot-scope="text,record">
            <a-tree-select style="width:100%"
                           :value="record.cate1 != null&&record.cate1 != ''?record.cate1+(record.cate2 != null&&record.cate2 != ''
                           ?'-'+(record.cate2 + (record.cate3 != null&&record.cate3 != ''?'-'+record.cate3:'')):''):''"
                           :dropdown-style="{ maxHeight: '300px', overflow: 'auto' }"

                           @change="changeCate($event,record)"
                           :autoExpandParent="false"
                           :tree-data="cate" placeholder="请选择分类">
            </a-tree-select>
          </div>

          <div slot="department" slot-scope="text,record">
            <a-tree-select style="width:100%"
                           :value="record.department2 != null?record.department2+(record.department3 != null?'-'+record.department3:'') :''"
                           @change="changeDepts($event,record)"
                           :autoExpandParent="false"
                           :dropdown-style="{ maxHeight: '300px', overflow: 'auto' }"
                           :tree-data="depts" placeholder="请选择部门">
            </a-tree-select>
          </div>


          <div slot="projectName" slot-scope="text,record">
            <div @click="() => editFullInTxt(record.issueId,0)">
              <div v-if="!record.editFull0" class="ellipsis">{{text}}</div>
              <a-textarea style="font-size:12px" :auto-size="{ minRows: 1, maxRows:5 }" v-model="record.projectName"
                          v-else v-focus='record.editFull0' @blur="(e) => {
												const { value } = e.target

												record.projectName = value
                        callUpdateReviewUncheck(record)
											}"/>
            </div>
          </div>


          <div slot="projectLevel" slot-scope="text,record" style="width: 100%;text-align: center">
            <a-select :key="record.issueId" :default-value="text == 0 ? '-': parseInt(text)"
                      :dropdownMatchSelectWidth="false" @change="changeSelectIn($event,record,'projectLevel')">

              <a-select-option :value="parseInt(2)">
                S
              </a-select-option>
              <a-select-option :value="parseInt(3)">
                A
              </a-select-option>
              <a-select-option :value="parseInt(4)">
                B
              </a-select-option>

            </a-select>

          </div>

<!--          <div slot="researchContent" slot-scope="text,record">
            <div @click="() => editFullInTxt(record.issueId,1)">
              <div v-if="!record.editFull1" class="ellipsis">{{text}}</div>
              <a-textarea style="font-size:12px" :auto-size="{ minRows: 1 ,maxRows:5}" v-else v-focus='record.editFull1'
                          v-model="record.researchContent" @blur="(e) => {
												const { value } = e.target

												record.researchContent = value
                        callUpdateReviewUncheck(record)
											}"/>
            </div>
          </div>-->
          <div slot="researchContent" slot-scope="text">
            <clamp :text="text" :sourceText="[text]" :isCenter="true"></clamp>
          </div>

<!--          <div slot="projectBackGround" slot-scope="text,record">
            <div @click="() => editFullInTxt(record.issueId,2)">
              <div v-if="!record.editFull2" class="ellipsis">{{text}}</div>
              <a-textarea style="font-size:12px" :auto-size="{ minRows: 1 ,maxRows:5}" v-else v-focus='record.editFull1'
                          v-model="record.projectBackGround" @blur="(e) => {
												const { value } = e.target

												record.projectBackGround = value
                        callUpdateReviewUncheck(record)
											}"/>
            </div>
          </div>-->
          <div slot="projectBackGround" slot-scope="text">
            <clamp :text="text" :sourceText="[text]" :isCenter="true"></clamp>
          </div>

          <div style="text-align:center" slot="projectLeader" slot-scope="text,record"
               @click="projectLeaderClick(record)">
            <span style="cursor:pointer; width:100%;">{{text ? text : '选择责任人'}}
            </span>
          </div>

          <div slot="cate1">1级<br/>分类</div>
          <div slot="cate2">2级<br/>分类</div>
          <div slot="cate3">3级<br/>分类</div>
          <div slot="projectNameTitle">课题名称</div>
          <div slot="projectLevelTitle">课题等级</div>
          <div slot="projectLeaderTitle">课题负责人</div>
          <div slot="platformAndTopic">平台课题</div>

          <div slot="reviewResult1" slot-scope="text,record">
            <div v-if="record.secondReview == 0" @click="() => editpop(record.issueId)">
              <a-select :open="true" placeholder="请选择" :autoFocus='true'
                        @select="(val) => {
                          changeIn1(val, record)
                        }"

                        @blur="(val) => {
                          record.editable = false
                        }"

                        :default-value="reviewRes[text]" v-if="record.editable" style="width: 100%"
              >
                <a-select-option :value="parseInt(0)">
                  请选择
                </a-select-option>
                <a-select-option :value="parseInt(1)">
                  通过
                </a-select-option>
                <a-select-option :value="parseInt(2)">
                  不通过
                </a-select-option>
                <a-select-option :value="parseInt(3)">
                  再确认
                </a-select-option>
              </a-select>
              <span v-else-if="!record.editable">
                <span class="green" v-if="text == 1">{{reviewRes[text]}}</span>
                <span class="red" v-else-if="text == 2">{{reviewRes[text]}}</span>
                <span class="yellow" v-else-if="text == 3">{{reviewRes[text]}}</span>
                <span class="btn_pn" v-else>请选择</span>
              </span>
            </div>
            <div v-else>
              <span class="green" v-if="text == 1">{{reviewRes[text]}}</span>
              <span class="red" v-else-if="text == 2">{{reviewRes[text]}}</span>
              <span class="yellow" v-else-if="text == 3">{{reviewRes[text]}}</span>
              <span class="btn_pn" v-else>请选择</span>
            </div>
          </div>

          <template slot="reviewResult2" slot-scope="text,record">
            <div v-if="record.secondReview == 1 && record.reviewResult1 == 3" @click="() => editpop(record.issueId)">
              <a-select placeholder="请选择" :open="true"
                        @select="(val) => {
                          changeIn2(val, record)
                        }"

                        @blur="(val) => {
                          record.editable = false
                        }"
                        :autoFocus='true'
                        :default-value="reviewRes[text]" v-if="record.editable" style="width: 100%"
              >
                <a-select-option :value="parseInt(0)">
                  请选择
                </a-select-option>
                <a-select-option :value="parseInt(1)">
                  通过
                </a-select-option>
                <a-select-option :value="parseInt(2)">
                  不通过
                </a-select-option>

              </a-select>
              <span v-if="!record.editable">
                <span class="green" v-if="text == 1">{{reviewRes[text]}}</span>
                <span class="red" v-else-if="text == 2">{{reviewRes[text]}}</span>
                <span class="yellow" v-else-if="text == 3">{{reviewRes[text]}}</span>
                <span class="btn_pn" v-else>请选择</span>
              </span>
            </div>
          </template>

          <span slot="reviewOpinion1" slot-scope="text,record">
              <div v-if="record.secondReview == 0">
                <a-textarea
                  style="width:100%"
                  :auto-size="{ maxRows:5,minRows:1 }"
                  v-model="record.reviewOpinion1"
                  @blur="(e) => {
												const { value } = e.target
												record.reviewOpinion1 = value
                        callUpdateReview(record)
											}"
                  placeholder="填写评审意见"
                />
              </div>
              <span v-else>
                  <span>{{text}}</span>
              </span>
          </span>

          <span slot="reviewOpinion2" slot-scope="text,record">
              <div v-if="record.secondReview == 1 && record.reviewResult1 == 3">
                <a-textarea
                  :auto-size="{ maxRows:5,minRows:1 }"
                  style="width:100%"
                  v-model="record.reviewOpinion2"
                  @blur="(e) => {
												const { value } = e.target
												record.reviewOpinion2 = value
                        callUpdateReview(record)
											}"
                  placeholder="填写评审意见"
                />
              </div>
              <span v-else>
                  <span>{{text}}</span>
              </span>
          </span>

          <span slot="reviewOpinion1" slot-scope="text,record">
              <div v-if="record.secondReview == 0">
                <a-textarea
                  :auto-size="{ maxRows:5,minRows:1 }"
                  style="width:100%"
                  v-model="record.reviewOpinion1"
                  @blur="(e) => {
												const { value } = e.target
												record.reviewOpinion1 = value
                        callUpdateReview(record)
											}"
                  placeholder="填写评审意见"
                />
              </div>
              <span v-else>
                  <span>{{text}}</span>
              </span>
          </span>

          <span slot="reviewOpinion2" slot-scope="text,record">
              <div v-if="record.secondReview == 1 && record.reviewResult1 == 3">
                <a-textarea
                  style="width:100%"
                  :auto-size="{ maxRows:5,minRows:1 }"
                  v-model="record.reviewOpinion2"
                  @blur="(e) => {
												const { value } = e.target
												record.reviewOpinion2 = value
                        callUpdateReview(record)
											}"
                  placeholder="填写评审意见"
                />
              </div>
              <span v-else>
                  <span>{{text}}</span>
              </span>
          </span>


        </a-table>
      </div>

    </a-modal>

    <div class="cumuDownModal" ref="mymodal">

      <a-modal :getContainer='()=>$refs.mymodal' title="选择课题负责人" :zIndex="parseInt(3)" :width="500" :visible="dropdownvisible"
               @cancel="handleCancel">
        <template slot="footer">
          <div></div>
        </template>
        <a-dropdown v-model="selectvisible" placement="bottomCenter" :trigger="['click']">
          <a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">{{account ?
            account : ''}}
            <a-icon type="down"/>
          </a-button>
          <a-menu slot="overlay">
            <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:500px">
              <a-input-search v-model="queryParam.searchValue" placeholder="搜索用户" @change="onSearch"/>
              <s-table
                style="width:100%"
                ref="tablePeople"
                :rowKey="(record) => record.id"
                :columns="vcolumns"
                :data="loadData"
                :customRow="customRow"
                :scroll="{y:200}"
              >
              </s-table>
            </a-spin>
          </a-menu>
        </a-dropdown>
      </a-modal>
    </div>


    <div class="classmodal">

      <a-modal title="选择待办人" :width="600" :height="400" :visible="todoPushVisible" :confirmLoading="todoPushConfirmLoading"
                @ok="pushReviewTodo" @cancel="handleCancel">
        <a-spin :spinning="todoPushConfirmLoading">
          <a-form :form="form">

            <a-row :gutter="24">
              <a-col :md="20" :sm="24">
                <a-form-item label="待办推送人" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input type='hidden' v-decorator="['userName', {rules: [{required: true, message: '请选择待办推送人！'}]}]" />
                  <a-dropdown v-model="dropdownvisible" placement="bottomCenter" :trigger="['click']">
                    <a-button  style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;">{{userNameDisplay ? userNameDisplay : '选择待办推送人'}}

                      <a-icon type="down" /></a-button>
                    <a-menu slot="overlay">
                      <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                        <a-input-search v-model="queryParam.searchValue" placeholder="搜索..." @change="todoOnSearch" />
                        <s-table style="width:100%;" ref="todoTablePeople" :rowKey="(record) => record.id" :columns="vcolumns" :data="loadData" :customRow="todoPushCustomRow" :scroll="{ y: 120,x:120}">>
                        </s-table>
                      </a-spin>
                    </a-menu>
                  </a-dropdown>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-spin>
      </a-modal>
    </div>
  </div>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
  import {mapActions, mapGetters} from 'vuex'
  import {XCard} from '@/components'
  import { ALL_APPS_MENU } from '@/store/mutation-types'
  import moment from 'moment'
import {
  getReviewListData,
  transitionReview,
  updateReview,
  getCateTree,
  updateReviewUncheck, oaTodoPushTopicReview, oaTodoDonePushTopicReview, oaTodoCancelPushTopicReview,getPassNumSixMonth, getAllProject
} from "@/api/modular/system/topic"

  import {
    getUserLists
  } from '@/api/modular/system/userManage'
  import {clamp, clampEdit} from '@/components'
  import editForm from './edit'
  import reviewSummary from "./review_summary";
import { Modal } from 'ant-design-vue';
  import {
    STable
  } from '@/components'
  import Vue from "vue";

  export default {
    computed: {
      ...mapGetters(['userInfo'])
    },
    components: {
      editForm,
      clamp,
      clampEdit,
      reviewSummary,
      STable,
      XCard,
      Treeselect
    },
    props: {
      listType: {
        type: Number,
        default: 0
      },
    },
    data() {
      return {
        paramquery:{},
        normalizer(node) {
          return {
            id: node.value,
            label: node.title,
            children: node.children && node.children.length > 0 ? node.children: 0,
          }
        },
        statuses:[
                {
                    id:1,
                    label:'通过'
                },{
                    id:2,
                    label:'不通过'
                },{
                    id:3,
                    label:'再确认'
                },{
                    id:0,
                    label:'未选择'
                }
        ],
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 8
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          }
        },
        up:true,
        dropdownvisible: false,
        xmglbVisible:false,
        account: '',
        selectvisible: false,

        chart1spinning: true,
        chart2spinning: true,
        chart3spinning: true,
        chart4spinning: true,
        depts: [],
        user: null,
        queryParam: {},
        dqueryParam: {},//直属领导
        hqueryParam: {},//所长
        iqueryParam: {},//总监
        pqueryParam: {},//院长
        todoPushConfirmLoading: false,
        form: this.$form.createForm(this),
        loading1: false,
        vcolumns: [{
            title: '账号',
            dataIndex: 'account'
          },
          {
            title: '姓名',
            dataIndex: 'name'
          },
        ],
        loadData: parameter => {
          return getUserLists(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },

        //直属领导
        loaddData: parameter => {
          return getUserLists(Object.assign(parameter, this.dqueryParam)).then((res) => {
            return res.data
          })
        },
        //所长
        loadhData: parameter => {
          return getUserLists(Object.assign(parameter, this.hqueryParam)).then((res) => {
            return res.data
          })
        },
        //总监
        loadiData: parameter => {
          return getUserLists(Object.assign(parameter, this.iqueryParam)).then((res) => {
            return res.data
          })
        },

        //院长
        loadpData: parameter => {
          return getUserLists(Object.assign(parameter, this.pqueryParam)).then((res) => {
            return res.data
          })
        },
        cate: [],
        affiliatedPlatformTreeData:[],
        _depts:[],
        width: document.documentElement.clientWidth * 0.8,
        height: document.documentElement.clientHeight * 0.5,
        bigClient: document.documentElement.clientHeight > 700,
        height1: document.documentElement.clientHeight * 0.3 + 100,
        clientHeight: document.documentElement.clientHeight,
        visible: false,
        todoPushVisible: false,
        userNameDisplay:'',
        titles: ['', '立项确认（所）', '', '立项确认（院级）', '立项评审（院级）'],

        reviewRes: ['请选择', '通过', '不通过', '再确认'],
        windowHeight: document.documentElement.clientHeight - 150,
        list: [],
        month: new Date().getMonth() + 1,
        day: new Date().getDate(),
        year: new Date().getFullYear(),
        val1: 0,
        val2: 0,
        level: ['-', 'S+', 'S', 'A', 'B', 'C'],
        selectedRowKeys: [],
        columns: [{
            title: '序号',
            dataIndex: 'index',
            align: 'center',
            width: 40,
            customRender: (text, record, index) => ( <div
            class = 'divcls div_border_right div_btns' > {index+1} </div>)
          },
          {
            title: '平台一级分类',
            dataIndex: 'affiliatedPlatform1',
            align: 'center',
            width: 90,
            //slots: {title: 'affiliatedPlatform1'},
            scopedSlots: {customRender: 'affiliatedPlatform'},
/*            customRender:(value,row,index)=>{
              return row.affiliatedPlatform1
              // return "123"
            },*/
          },
          {
            title: '平台二级分类',
            dataIndex: 'affiliatedPlatform2',
            align: 'center',
            width: 90,
            //slots: {title: 'affiliatedPlatform2'},
            scopedSlots: {customRender: 'affiliatedPlatform'},
          },
          {
            title: '课题分类',
            dataIndex: 'cate1',
            width: 80,
            align: 'center',
            slots: {title: 'cate1'},
            scopedSlots: {customRender: 'cate'},
            // className: this.listType === 4 ? 'tableHide' : 'tableShow',
          },

          /* {
             dataIndex: 'cate2',
             width:50,
             align:'center',
             slots: { title: 'cate2' },
             scopedSlots: { customRender: 'cate' },
              customRender:(value,row,index)=>{
               const obj = {
                 children:value,
                 attrs:{}
               }
               obj.attrs.rowSpan = this.renderData(value,this.list,index,'cate2')
               return obj
             } 

           },
           {
             dataIndex: 'cate3',
             width:50,
             align:'center',
             slots: { title: 'cate3' },
             scopedSlots: { customRender: 'cate' },
           }, */
          {
            title: '课题名称',
            dataIndex: 'projectName',
            width:120,
            align:'center',
            scopedSlots: { customRender: 'projectName' },
          },
          {
            title: '研究内容',
            dataIndex: 'researchContent',
            width:120,
            align:'center',
            scopedSlots: { customRender: 'researchContent' },
          },
          {
            title: '课题背景',
            dataIndex: 'projectBackGround',
            width:120,
            align:'center',
            scopedSlots: { customRender: 'projectBackGround' },
          },
          /*{
            dataIndex: 'platformAndTopic',
            slots: { title: 'platformAndTopicTitle' },
            scopedSlots: { customRender: 'platformAndTopic' },
            width:50,
            align:'center',
          },*/
          {
            slots: {title: 'projectLevelTitle'},
            dataIndex: 'projectLevel',
            scopedSlots: {customRender: 'projectLevel'},
            width: 80,
            align: 'center',

          },
          {

            dataIndex: 'projectLeader',
            slots: {title: 'projectLeaderTitle'},
            scopedSlots: {customRender: 'projectLeader'},
            width: 80,
            align: 'center',
          },
          {
            title: '部门',
            dataIndex: 'department2',
            width:80,
            align:'center',
            scopedSlots: { customRender: 'department' },
          },
          /*{
            title: '3级部门',
            dataIndex: 'department3',
            width:80,
            align:'center',
            scopedSlots: { customRender: 'department' },
          },*/
          {
            title: '评审结果',
            dataIndex: 'reviewResult1',
            width: 80,
            align: 'center',
            scopedSlots: {customRender: 'reviewResult1'},
          },
          {
            title: '评审意见',
            dataIndex: 'reviewOpinion1',
            align: 'center',
            scopedSlots: {customRender: 'reviewOpinion1'},
            width: 100
          },
          {
            title: '二次评审结果',
            dataIndex: 'reviewResult2',
            align: 'center',
            width: 90,
            scopedSlots: {customRender: 'reviewResult2'},
          },
          {
            title: '二次评审意见',
            dataIndex: 'reviewOpinion2',
            width: 120,
            align: 'center',
            scopedSlots: {customRender: 'reviewOpinion2'},
          },
        ],
        columnsProject: [{
            title: '序号',
            dataIndex: 'index',
            align: 'center',
            width: 40,
            customRender: (text, record, index) => ( <div
            class = 'divcls div_border_right div_btns' > {index+1} </div>)
          },
          {
            title: '平台一级分类',
            dataIndex: 'affiliatedPlatform1',
            align: 'center',
            width: 90,
            slots: {title: 'affiliatedPlatform1'},
            scopedSlots: {customRender: 'affiliatedPlatform'},
           /* customRender:(value,row,index)=>{
              return row.affiliatedPlatform1
            },*/
          },
          {
            title: '平台二级分类',
            dataIndex: 'affiliatedPlatform2',
            align: 'center',
            width: 90,
            slots: {title: 'affiliatedPlatform2'},
            scopedSlots: {customRender: 'affiliatedPlatform'},
          },
          {
            title: '课题分类',
            dataIndex: 'cate1',
            width: 80,
            align: 'center',
            slots: {title: 'cate1'},
            scopedSlots: {customRender: 'cate'},
            // className: this.listType === 4 ? 'tableHide' : 'tableShow',
          },
          /* {
             dataIndex: 'cate2',
             width:50,
             align:'center',
             slots: { title: 'cate2' },
             scopedSlots: { customRender: 'cate' },
              customRender:(value,row,index)=>{
               const obj = {
                 children:value,
                 attrs:{}
               }
               obj.attrs.rowSpan = this.renderData(value,this.list,index,'cate2')
               return obj
             }

           },
           {
             dataIndex: 'cate3',
             width:50,
             align:'center',
             slots: { title: 'cate3' },
             scopedSlots: { customRender: 'cate' },
           }, */
          {
            title: '课题名称',
            dataIndex: 'projectName',
            width:120,
            align:'center',
            scopedSlots: { customRender: 'projectName' },
          },
          {
            title: '研究内容',
            dataIndex: 'researchContent',
            width:120,
            align:'center',
            scopedSlots: { customRender: 'researchContent' },
          },
          {
            title: '课题背景',
            dataIndex: 'projectBackGround',
            width:120,
            align:'center',
            scopedSlots: { customRender: 'projectBackGround' },
          },
          /*{
            dataIndex: 'platformAndTopic',
            slots: { title: 'platformAndTopicTitle' },
            scopedSlots: { customRender: 'platformAndTopic' },
            width:50,
            align:'center',
          },*/
          {
            slots: {title: 'projectLevelTitle'},
            dataIndex: 'projectLevel',
            scopedSlots: {customRender: 'projectLevel'},
            width: 80,
            align: 'center',

          },
          {

            dataIndex: 'projectLeader',
            slots: {title: 'projectLeaderTitle'},
            scopedSlots: {customRender: 'projectLeader'},
            width: 80,
            align: 'center',
          },
          {
            title: '部门',
            dataIndex: 'department2',
            width:80,
            align:'center',
            scopedSlots: { customRender: 'department' },
          },{
            title: '创建时间',
            dataIndex: 'createDate',
            width:80,
            align:'center'
          },
          /*{
            title: '3级部门',
            dataIndex: 'department3',
            width:80,
            align:'center',
            scopedSlots: { customRender: 'department' },
          },*/
          {
            title: '评审结果',
            dataIndex: 'reviewResult1',
            width: 80,
            align: 'center',
            scopedSlots: {customRender: 'reviewResult1'},
          },
          {
            title: '评审意见',
            dataIndex: 'reviewOpinion1',
            align: 'center',
            scopedSlots: {customRender: 'reviewOpinion1'},
            width: 100
          },
          {
            title: '二次评审结果',
            dataIndex: 'reviewResult2',
            align: 'center',
            width: 90,
            scopedSlots: {customRender: 'reviewResult2'},
          },
          {
            title: '二次评审意见',
            dataIndex: 'reviewOpinion2',
            width: 120,
            align: 'center',
            scopedSlots: {customRender: 'reviewOpinion2'},
          },
        ],
        columnsIn: [
          {
            title: '序号',
            dataIndex: 'index',
            align: 'center',
            width: 40,
            customRender: (text, record, index) => ( <div
            class = 'divcls div_border_right div_btns' > {index+1} </div>)
          },
          {
            title: '平台一级分类',
            dataIndex: 'affiliatedPlatform1',
            align: 'center',
            width: 90,
            slots: {title: 'affiliatedPlatform1'},
            scopedSlots: {customRender: 'affiliatedPlatform'},
          },
          {
            title: '平台二级分类',
            dataIndex: 'affiliatedPlatform2',
            align: 'center',
            width: 90,
            slots: {title: 'affiliatedPlatform2'},
            scopedSlots: {customRender: 'affiliatedPlatform'},
          },
          {
            dataIndex: 'cate1',
            width: 80,
            align: 'center',
            title: '课题分类',
            scopedSlots: {customRender: 'cate'},
            // className: this.listType === 4 ? 'tableHide' : 'tableShow',
          },
          {
            dataIndex: 'projectName',
            width: 120,
            title: '课题名称',
            align: 'center',
            scopedSlots: {customRender: 'projectName'},
          },

          {
            title: '研究内容',
            dataIndex: 'researchContent',
            align:'center',
            width:120,
            scopedSlots: { customRender: 'researchContent' },
          },
          {
            title: '课题背景',
            dataIndex: 'projectBackGround',
            align:'center',
            width:120,
            scopedSlots: { customRender: 'projectBackGround' },
          },
/*          {
            dataIndex: 'platformAndTopic',
            slots: { title: 'platformAndTopic' },
            width:80,
            align:'center',
            customRender:(value,row,index)=>{
              let level = ['','平台','课题']
              return level[parseInt(value)]
            }
          },*/

          {
            slots: {title: 'projectLevelTitle'},
            dataIndex: 'projectLevel',
            scopedSlots: {customRender: 'projectLevel'},
            width: 80,
            align: 'center',

          },
          {
            slots: { title: 'projectLeaderTitle' },
            dataIndex: 'projectLeader',
            
            align:'center',
            width:80
          },
          {
            title: '部门',
            dataIndex: 'department2',
            width: 100,
            align: 'center',
            scopedSlots: {customRender: 'department'},
          },

          {
            title: '评审结果',
            dataIndex: 'reviewResult1',
            width: 80,
            align: 'center',
            scopedSlots: {customRender: 'reviewResult1'},
          },
          {
            title: '评审意见',
            dataIndex: 'reviewOpinion1',
            align: 'center',
            scopedSlots: {customRender: 'reviewOpinion1'},
            width: 100
          },
          {
            title: '二次评审结果',
            dataIndex: 'reviewResult2',
            align: 'center',
            width: 90,
            scopedSlots: {customRender: 'reviewResult2'},
          },
          {
            title: '二次评审意见',
            dataIndex: 'reviewOpinion2',
            width: 120,
            align: 'center',
            scopedSlots: {customRender: 'reviewOpinion2'},
          },

        ],
        projectName:'',
        loading: false,
        myChart1: null,
        myChart2: null,
        myChart3: null,
        myChart4: null,
        allTotal: 0,
        passTotal: 0,
        unPassTotal: 0,
        unCheckTotal: 0,
        checkTotal: 0,
        unConfirmTotal: 0,
        sLevel: 0,
        aLevel: 0,
        pLevel: 0,
        startCreateDate:null,
        endCreateDate:null,
        listIn: [],
        updateIssueid: null,
        refList: [],
        dateMonth:moment(new Date()),
        dateMonthString:moment(new Date()).format("YYYY-MM"),
        showTodoPushBtn: this.hasPerm('oaTodo:topicReview'),
      }
    },
    mounted() {
      this.callUserInfo()
      this.callGetPlatformTree()
      this.callGetTree()
      this.callGetDeptTree()

    },
    methods: {
      swich(){
        this.up = !this.up
      },
      setItemRef(el) {
        this.refList.push(el)
      },
      callUserInfo() {

        this.user = this.userInfo

       /* let checkRoles = ['', '', '', 'topic_xmglb', 'topic_yz']

        let role = checkRoles[this.listType]

        let $i = this.userInfo.roles.findIndex(item => item.code == role)*/

        //if ($i > -1) {
          this.callReviewList()
        //}
      },
      callGetDeptTree() {
        this.confirmLoading = true
        getCateTree({
          fieldName: 'department'
        }).then((res) => {
          if (res.success) {
            this.depts = res.data
          } else {
            this.$message.error('错误提示：' + res.message, 1)
          }
          this.confirmLoading = false
        }).catch((err) => {
          this.confirmLoading = false
          this.$message.error('错误提示：' + err.message, 1)
        });
      },

      getPassSixMonth(callback){
        getPassNumSixMonth().then(res => {
          if(res.data != null){
            callback(res);
          }else{
            this.getPassSixMonth()
          }
          }
        )
      },

      customRow(row, index) {
        return {
          on: {
            click: () => {

              updateReviewUncheck({
                issueId: this.updateIssueid,
                projectLeaderId: row.account
              }).then((res) => {
                if (res.success) {
                  this.dropdownvisible = false
                  this.selectvisible = false
                  this.account = row.name
                  let $i = this.list.findIndex(e=>e.issueId == this.updateIssueid)
                  this.list[$i].projectLeaderId = row.account
                  this.list[$i].projectLeader = row.name
                  this.getEchartData(false)

                } else {
                  this.$message.error('保存失败：' + res.message)
                }
                let option2 = null



                this.getPassSixMonth(res => {


                  let data = res.data

                  let month = []
                  let num = []
                  for (let i = 0; i < data.length; i++) {
                    month.push(data[i].month)
                    num.push(data[i].num)
                  }

                  option2 = {
                    color: ['#58a55c', '#5770a4', '#fac858'],
                    title: {
                      text: '近6个月立项通过率',
                      x: 'center',
                      bottom:-7
                    },
                    xAxis: {
                      type: 'category',
                      data: month
                    },
                    yAxis: {
                      type: 'value',
                      axisLabel: {
                        formatter: (value, index) => {
                          return value + "%";
                        }
                      }
                    },
                    legend: {
                      data: ['立项通过率', '平均通过率'],
                      show: true,
                      top: '5%',
                      left: 'center'
                    },

                    tooltip: {
                      trigger: 'axis', //坐标轴触发，主要在柱状图，折线图等会使用类目轴的图表中使用
                      axisPointer: {// 坐标轴指示器，坐标轴触发有效
                        type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
                      }
                    },
                    series: [
                      {
                        data: num,
                        type: 'line',
                        name: '立项通过率',
                        itemStyle: {
                          normal: {
                            label: {
                              show: true, // 在折线拐点上显示数据
                              formatter: function (params) {
                                return params.value + '%'
                              }
                            }
                          }
                        },
                        markLine: {
                          data: [{type: 'average', name: '平均通过率'}],
                          symbol: 'none',
                          silent: true,
                          label: {
                            normal: {
                              // position: 'end',
                              formatter: function (params) {
                                return '平均通过率\n' + params.value + '%'
                              }
                            }
                          },


                          lineStyle: {
                            color: '#5770a4',
                            width: 2
                          }
                        }
                      }, {
                        type: 'line',
                        name: '平均通过率'
                      }

                    ]
                  };

                  this.myChart2 = this.echarts.init(document.getElementById('chart2'))

                  this.myChart2.setOption(option2)
                  this.chart2spinning = false
                })

                getAllProject().then(res => {
                  this.myChart4 = this.echarts.init(document.getElementById('chart4'))


                  const option4 = {
                    color: ['#ed7d31', '#70ad47', '#5b9bd5'],
                    title: {
                      text: '累计技术课题等级分析',
                      x: 'center',
                      y: 'bottom',
                    },
                    tooltip: {
                      trigger: 'item'
                    },
                    legend: {
                      top: '5%',
                      left: 'center'
                    },
                    series: [
                      {
                        type: 'pie',
                        minAngle:10,
                        avoidLabelOverlap: false,
                        radius: ['30%', '50%'],
                        data: res.data,
                        label: {
                          show: true,
                          formatter: function(data){
                            return `${data.name}\n${data.percent.toFixed(1)}%`
                          }
                          // formatter: '{d}%'
                        },
                        emphasis: {
                          itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                          }
                        }
                      }
                    ]
                  };
                  this.myChart4.setOption(option4)

                  this.chart4spinning = false

                })
              }).finally((res) => {
                this.confirmLoading = false
              })

            }
          }
        }
      },
      todoPushCustomRow(row, index) {
        return {
          on: {
            click: () => {
              this.form.setFieldsValue({
                userName: row.account
              })
              this.userNameDisplay = row.name
              this.dropdownvisible = false
            }
          }
        }
      },
      change(value, label, extra){
            this.callReviewList()
      },

      dateChange(date, dateString){

        if(dateString[0] != null && dateString[0] != ''){
          this.paramquery.startCreateDate = dateString[0] + ' 00:00'
        }else{
          this.paramquery.startCreateDate = null
        }
         if(dateString[1] != null && dateString[1] != ''){
          this.paramquery.endCreateDate = dateString[1] + ' 23:59'
        }else{
           this.paramquery.endCreateDate = null
         }


        this.callReviewList()
      },

      handleCancel() {
        this.selectvisible = false
        this.dropdownvisible = false
        this.queryParam.searchValue = null
        this.todoPushVisible = false
        this.form.setFieldsValue({
          userName: ''
        })
        this.userNameDisplay = ''
      },
      todoPushHandleCancel() {
        this.selectvisible = false
        this.dropdownvisible = false
      },
      onChangeMonth(date, dateString) {
        if("" != dateString && null !=  dateString){
          this.dateMonthString = dateString
        }else{
          this.dateMonthString = null
        }

      },

      onSearch(e) {
        this.$refs.tablePeople.refresh()
      },
      todoOnSearch(e) {
        this.$refs.todoTablePeople.refresh()
      },
      callGetPlatformTree(){
        this.confirmLoading = true
        getCateTree({
          fieldName: 'affiliatedPlatform'
        }).then((res) => {
          if (res.success) {
            this.affiliatedPlatformTreeData = res.data
          } else {
            this.$message.error('错误提示：' + res.message, 1)
          }
          this.confirmLoading = false
        }).catch((err) => {
          this.confirmLoading = false
          this.$message.error('错误提示：' + err.message, 1)
        });
      },
      callGetTree() {
        this.confirmLoading = true
        getCateTree({
          fieldName: 'projectCate'
        }).then((res) => {
          if (res.success) {
            this.cate = res.data
          } else {
            this.$message.error('错误提示：' + res.message, 1)
          }
          this.confirmLoading = false
        }).catch((err) => {
          this.confirmLoading = false
          this.$message.error('错误提示：' + err.message, 1)
        });
      },
      selectReviewTodoUser() {
        this.todoPushVisible = true;
      },
      pushReviewTodo() {
        const {
          form: {
            validateFields
          }
        } = this
        this.todoPushConfirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            let $params = { ...values,
              dateMonth: this.$route.query.dateMonth,
            }
            oaTodoPushTopicReview($params).then((res) => {
              // console.log(res);
              if (res.success) {
                if (res.data.result == "1") {
                  this.$message.success(res.data.message)
                  this.visible = false
                  this.todoPushConfirmLoading = false
                  this.handleCancel()
                } else if (res.data.result == "0") {
                  //弹出确认框
                  this.$confirm({
                    // iconClass: 'el-icon-question', //自定义图标样式
                    title: '提示',
                    content: res.data.message,
                    // confirmButtonText: '确认', //确认按钮文字更换
                    // cancelButtonText: '取消', //取消按钮文字更换
                    // showClose: true, //是否显示右上角关闭按钮
                    type: 'warning', //提示类型  success/info/warning/error
                    onOk:() => {
                        $params = {
                          ...values,
                          dateMonth: this.$route.query.dateMonth,
                          repeatPush: 1,
                        };
                        oaTodoPushTopicReview($params).then(res => {
                          if (res.success) {
                            this.$message.success(res.data.message);
                            this.visible = false;
                            this.todoPushConfirmLoading = false;
                            this.handleCancel();
                          } else {
                            this.$message.error('待办推送失败' + res.message);
                          }

                        })

                    },

                  })
                }else {
                  this.$message.error('待办推送失败：' + res.message)
                }
              } else {
                this.$message.error('待办推送失败：' + res.message)
              }
            }).finally((res) => {
              this.todoPushConfirmLoading = false
            })
          } else {
            this.todoPushConfirmLoading = false
            this.todoPushVisible = false;
          }
        })


      },
      doneReviewTodo() {

        var param = {
          dateMonth: this.$route.query.dateMonth,
        };
        oaTodoDonePushTopicReview(param).then(res => {
          // console.log(res);

        }).finally(() => {

        });
      },
      cancelReviewTodo() {

        var param={
          dateMonth:this.$route.query.dateMonth,
        }
        oaTodoCancelPushTopicReview(param).then(res => {
          // console.log(res);
          if (res.success) {
            this.$message.success(res.data.message);
          } else {
            this.$message.error('待办取消失败：' + res.message)
          }
        }).finally(() => {

        });
      },
      showModal() {
        let index = this.userInfo.roles.findIndex(item => item.code == 'topic_fsz')
        if (index > -1 || this.listType == 1) {
          this.callTransitionReview();
          return;
        }

        //项目管理部
        if(this.listType == 3){
          this.xmglbVisible = true
          setTimeout(() => {
            this.callReviewList()
          }, 100)
          return;
        }

        this.visible = true;
        setTimeout(() => {
          this.getEchartData(true)
        }, 100)

      },

      async getEchartData(updateOther) {


        await this.callReviewList()


        if (updateOther) {
          let option2 = null
          this.getPassSixMonth(res => {
            let data = res.data

            let month = []
            let num = []
            for (let i = 0; i < data.length; i++) {
              month.push(data[i].month)
              num.push(data[i].num)
            }

            option2 = {
              color: ['#58a55c', '#5770a4', '#fac858'],
              title: {
                text: '近6个月立项通过率',
                x: 'center',

                bottom:-7
              },
              xAxis: {
                type: 'category',
                data: month
              },
              yAxis: {
                type: 'value',
                axisLabel: {
                  formatter: (value, index) => {
                    return value + "%";
                  }
                },
                max: 100,
                min: 0,
              },
              legend: {
                data: ['立项通过率', '平均通过率'],
                show: true,
                top: '5%',
                left: 'center'
              },

              tooltip: {
                trigger: 'axis', //坐标轴触发，主要在柱状图，折线图等会使用类目轴的图表中使用
                axisPointer: {// 坐标轴指示器，坐标轴触发有效
                  type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
                }
              },
              series: [
                {
                  data: num,
                  type: 'line',
                  name: '立项通过率',
                  itemStyle: {
                    normal: {
                      label: {
                        show: true, // 在折线拐点上显示数据
                        formatter: function (params) {
                          return params.value + '%'
                        }
                      }
                    }
                  },
                  markLine: {
                    data: [{type: 'average', name: '平均通过率'}],
                    symbol: 'none',
                    silent: true,
                    label: {
                      normal: {
                        // position: 'end',
                        formatter: function (params) {
                          return '平均通过率\n' + params.value + '%'
                        }
                      }
                    },


                    lineStyle: {
                      color: '#5770a4',
                      width: 2
                    }
                  }
                }, {
                  type: 'line',
                  name: '平均通过率'
                }

              ]
            };

            this.myChart2 = this.echarts.init(document.getElementById('chart2'))

            this.myChart2.setOption(option2)
            this.chart2spinning = false
          })

          getAllProject().then(res => {
            this.myChart4 = this.echarts.init(document.getElementById('chart4'))


            const option4 = {
              color: ['#ed7d31', '#70ad47', '#5b9bd5'],
              title: {
                text: '累计技术课题等级分析',
                x: 'center',
                y: 'bottom',
              },
              tooltip: {
                trigger: 'item'
              },
              legend: {
                top: '5%',
                left: 'center'
              },
              series: [
                {
                  type: 'pie',
                  minAngle:10,
                  avoidLabelOverlap: false,
                  radius: ['30%', '50%'],
                  itemStyle: {
                    borderRadius: 4,
                    borderColor: '#fff',
                    borderWidth: 1
                  },
                  data: res.data,
                  label: {
                    show: true,
                    formatter: function(data){
                      return `${data.name}\n${data.percent.toFixed(1)}%`
                    }
                    // formatter: '{d}%'
                  },
                  emphasis: {
                    itemStyle: {
                      shadowBlur: 10,
                      shadowOffsetX: 0,
                      shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                  }
                }
              ]
            };
            this.myChart4.setOption(option4)

            this.chart4spinning = false

          })
        }


        this.myChart1 = this.echarts.init(document.getElementById('chart1'))

        this.myChart3 = this.echarts.init(document.getElementById('chart3'))


        const option = {

          color: ['#58a55c', '#ff3333', '#fac858'],
          title: [/*{
              text: '技术课题立项分析',
              x:'center',
              y: 'bottom',
              bottom:'-20%'
            },*/{
            text: '通过总数：' + this.passTotal + '\n——————\n申请总数：' + this.allTotal,
            left: "center",//对齐方式居中
            top: "27%",//距离顶部
            textStyle: {//文字配置
              color: "black",//文字颜色
              fontSize: this.bigClient ? 14 : 12,//字号
              align: "center"//对齐方式
            }
          }],
          tooltip: {
            trigger: 'item'
          },
          legend: {
            top: '0%',
            left: 'center'
          },
          series: [
            {
              type: 'pie',
              minAngle:10,
              // avoidLabelOverlap: false,
              radius: ['30%', '50%'],
              itemStyle: {
                borderRadius: 4,
                borderColor: '#fff',
                borderWidth: 1
              },
              center: ['50%', '35%'],
              avoidLabelOverlap: true,
              label: {
                show: true,
                formatter: function(data){
                  return `${data.name}\n${data.percent.toFixed(1)}%`
                }
                // formatter: '{d}%'
              },
              emphasis: {
                scale:false
              },
              labelLine: {

              },
              data: [
                {value: this.passTotal == 0 ? null : this.passTotal, name: '通过'},
                {value: this.unPassTotal == 0 ? null : this.unPassTotal, name: '不通过'},
                {value: this.unConfirmTotal == 0 ? null : this.unConfirmTotal, name: '再确认'}
              ]
            }
          ]
        }


        const option3 = {
          color: ['#ed7d31', '#70ad47', '#5b9bd5'],
          title: {
            text: '本次通过技术课题等级分析',
            x: 'center',
            y: 'bottom',
          },
          tooltip: {
            trigger: 'item'
          },
          legend: {
            top: '5%',
            left: 'center'
          },
          grid: {
            left: '0%',
            right: '0%',
            top: '33%',
            bottom: '20%'
          },
          series: [
            {
              type: 'pie',
              minAngle:10,
              avoidLabelOverlap: false,
              radius: ['30%', '50%'],
              itemStyle: {
                borderRadius: 4,
                borderColor: '#fff',
                borderWidth: 1
              },
              data: [
                {value: this.sLevel == 0 ? null : this.sLevel, name: 'S级'},
                {value: this.aLevel == 0 ? null : this.aLevel, name: 'A级'},
                {value: this.pLevel == 0 ? null : this.pLevel, name: 'B级'}
              ],
              label: {
                show: true,
                formatter: function(data){
                  return `${data.name}\n${data.percent.toFixed(1)}%`
                }
                // formatter: '{d}%'
              },
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        };

        this.myChart1.setOption(option, true)
        this.chart1spinning = false

        this.myChart3.setOption(option3, true)
        this.chart3spinning = false


      },
      handleOk() {
        this.callReviewList()
      },
      renderData(text, dataSource, index, key) {
        if (dataSource.length > 0) {
          if (index !== 0 && text === dataSource[index - 1][key]) { //&& index % this.pageSize != 0
            return 0
          }
          let rowSpan = 1
          for (let i = index + 1; i < dataSource.length; i++) {
            if (text !== dataSource[i][key]) {
              break
            }
            rowSpan++
          }
          return rowSpan
        }
      },
      change1(val, record) {
        /* if (val == 0) {
          record.editable = false
          record.reviewResult1 = 0
          return
        } */
        if (record.reviewResult1 == val) {
          record.editable = false
          return
        }
        record.reviewResult1 = val
        this.callUpdateReview(record)
      },
      change2(val, record) {

        /* if (val == 0) {
          record.editable = false
          record.reviewResult2 = 0
          return
        } */
        if (record.reviewResult2 == val) {
          record.editable = false
          return
        }
        record.reviewResult2 = val
        this.callUpdateReview(record)
      }, changeIn1(val, record) {
        if (record.reviewResult1 == val) {
          record.editable = false
          return
        }
        record.reviewResult1 = val
        this.callUpdateReview(record)

        this.$nextTick(() => this.getEchartData(false))
      },
      changeIn2(val, record) {

        /* if (val == 0) {
          record.editable = false
          record.reviewResult2 = 0
          return
        } */
        if (record.reviewResult2 == val) {
          record.editable = false
          return
        }
        record.reviewResult2 = val
        this.callUpdateReview(record)
        this.$nextTick(() => this.getEchartData(false))
      },

      editFullTxt(id, flag) {

        const $i = this.list.findIndex(item => id === item.issueId);
        if (flag == 1) {
          this.list[$i].editFull1 = true
        } else if (flag == 2) {
          this.list[$i].editFull2 = true
        } else {
          this.list[$i].editFull0 = true
        }

      },
      editFullInTxt(id, flag) {

        const $i = this.listIn.findIndex(item => id === item.issueId);
        if (flag == 1) {
          this.listIn[$i].editFull1 = true
        } else if (flag == 2) {
          this.listIn[$i].editFull2 = true
        } else {
          this.listIn[$i].editFull0 = true
        }

      },
      edit(id) {
        const $i = this.list.findIndex(item => id === item.issueId);
        this.list[$i].editable = true
      },
      editpop(id) {
        const $i = this.listIn.findIndex(item => id === item.issueId);
        this.listIn[$i].editable = true
      },
      callUpdateReview(record) {
        updateReview({
          issueId: record.issueId,
          secondReview: record.secondReview,
          reviewListNum: this.listType,
          reviewResult: record.secondReview == 0 ? record.reviewResult1 : record.reviewResult2,
          reviewOpinion: record.secondReview == 0 ? record.reviewOpinion1 : record.reviewOpinion2,
        }).then((res) => {
          if (res.success) {

            let $i = this.list.findIndex(item => item.issueId == record.issueId)
            if ($i > -1) {
              this.list[$i][record.secondReview == 0 ? 'reviewResult1' : 'reviewResult2'] = record.secondReview == 0 ? record.reviewResult1 : record.reviewResult2
              this.list[$i][record.secondReview == 0 ? 'reviewOpinion1' : 'reviewOpinion2'] = record.secondReview == 0 ? record.reviewOpinion1 : record.reviewOpinion2
            }
            let _i = this.listIn.findIndex(item => item.issueId == record.issueId)
            if (_i > -1) {
              this.listIn[_i][record.secondReview == 0 ? 'reviewResult1' : 'reviewResult2'] = record.secondReview == 0 ? record.reviewResult1 : record.reviewResult2;
              this.listIn[_i][record.secondReview == 0 ? 'reviewOpinion1' : 'reviewOpinion2'] = record.secondReview == 0 ? record.reviewOpinion1 : record.reviewOpinion2;
            } else {
              if (record.secondReview == 0) {
                if (record.reviewResult1 != 0) {
                  this.listIn.push(record);
                }
              } else {
                if (record.reviewResult2 != 0) {
                  this.listIn.push(record);
                }
              }
            }
            record.editable = false
            /* this.callReviewList() */
          } else {
            this.$message.error('保存失败：' + res.message)
          }
        }).finally((res) => {
          this.confirmLoading = false
        })
      },
      callTransitionReview() {
        if (this.listIn.length < 1) {
          this.$message.error('清单未评审', 1);
          return
        }

        //项目管理部审核
        if(this.listType == 3){
          if(this.dateMonthString == null){
            this.$message.error('请先选择评审月份')
            return
          }
        }
        this.loading = true

        let ids = []



        for (let i = 0; i < this.listIn.length; i++) {
          ids.push(this.listIn[i].issueId)
        }

        transitionReview({
          issueIds: ids,
          reviewListNum: this.listType,
          dateMonth:this.listType==3?this.dateMonthString:this.$route.query.dateMonth,
          originData:this.listIn
        })
          .then((res) => {
            const h = this.$createElement;
            const msgs = []
            for (const item of res.data) {
              msgs.push(h('p', item))
            }
            let that = this
            that.$info({
              title: '评审结果',
              content: h('div', {}, msgs),
              onOk() {
                that.callReviewList()
              },
            });
            this.visible = false
            this.xmglbVisible = false
            this.selectedRowKeys = []
            this.loading = false
            
          })
          .catch((err) => {
            this.loading = false
            this.$message.error('错误提示：' + err.message, 1)
          }).finally(res => {
          if (this.listType == 4) {
            this.doneReviewTodo()//点击结束评审，待办办结
            this.gotoCheck();
          }
        });
      },



      gotoCheck(record){
        //this.switchApp()
        this.$router.push({
          path: "/topic_project"
        });
      },

      onSelectChange(selectedRowKeys) {
        console.log('selectedRowKeys changed: ', selectedRowKeys);
        this.selectedRowKeys = selectedRowKeys;
      },

      callUpdateReviewUncheck(record) {
        updateReviewUncheck({
          issueId: record.issueId,
          researchContent: record.researchContent,
          projectBackGround: record.projectBackGround,
          projectName: record.projectName
        }).then((res) => {
          if (res.success) {
            record.editFull1 = false
            record.editFull2 = false
            record.editFull0 = false
          } else {
            this.$message.error('保存失败：' + res.message)
          }
        }).finally((res) => {
          this.confirmLoading = false
        })
      },
      changePlatformTree(value, record,column) {
        console.log(column)
        updateReviewUncheck({
          issueId: record.issueId,
          affiliatedPlatform: value
        }).then((res) => {
          if (res.success) {
            //this.callReviewList()
            console.log(res);
            //console.log(this.cate)

            console.log(this.affiliatedPlatformTreeData)
            for (const item of this.affiliatedPlatformTreeData) {
              if(null != item.children){
                for (const _item of item.children) {
                  if (_item.value == value) {
                    record.affiliatedPlatform1 = item.title
                    record.affiliatedPlatform2 = _item.title
                    console.log(record)
                    break
                  }
                }

              }
            }
          } else {
            this.$message.error('保存失败：' + res.message)
          }
        }).finally((res) => {
          this.confirmLoading = false
        })

      },
      changeCate(value, record) {
        updateReviewUncheck({
          issueId: record.issueId,
          projectCate: value
        }).then((res) => {
          if (res.success) {
            //this.callReviewList()

            //console.log(this.cate)

            for (const item of this.cate) {
              for (const _item of item.children) {
                if (_item.value == value
                ) {

                  record.cate2 = ''
                  record.cate3 = ''
                  record.cate1 = item.title+'-'+_item.title

                  /* let arr =  _item.title.split('-')
                  record.cate2 = arr[0]
                  if (arr[1]) {
                    record.cate3 = arr[1]
                  }else{
                    record.cate3 = '-'
                  } */
                  break
                }
              }
            }
          } else {
            this.$message.error('保存失败：' + res.message)
          }
        }).finally((res) => {
          this.confirmLoading = false
        })

      },

      changeDepts(value, record) {
        updateReviewUncheck({
          issueId: record.issueId,
          departmentCate: value
        }).then((res) => {
          if (res.success) {
            //this.callReviewList()
            for (const item of this.depts) {
              for (const _item of item.children) {
                if (_item.value == value
                ) {
                  record.department2 = item.title
                  record.department3 = _item.title
                  break
                }
              }
            }
          } else {
            this.$message.error('保存失败：' + res.message)
          }
        }).finally((res) => {
          this.confirmLoading = false
        })

      },
      changeSelect(value, record, column) {

        let param = {}
        param['issueId'] = record.issueId
        param[column] = value

        if(column == 'projectLevel'){
          //B级别  课题
          if(value == 4){
            param.issueType = 2
          }else{
            param.issueType = 1
          }
        }

        updateReviewUncheck(param).then((res) => {
          if (res.success) {
            record[column] = value
            if (column == 'issueType') {
              record.platformAndTopic = value
            }
          } else {
            this.$message.error('保存失败：' + res.message)
          }
        }).finally((res) => {
          this.confirmLoading = false
        })

      },

      changeSelectIn(value, record, column) {

        let param = {}
        param['issueId'] = record.issueId
        param[column] = value

        if(column == 'projectLevel'){
          //B级别  课题
          if(value == 4){
            param.issueType = 2
          }else{
            param.issueType = 1
          }
        }

        updateReviewUncheck(param).then((res) => {
          if (res.success) {
            record[column] = value
            if (column == 'issueType') {
              record.platformAndTopic = value
            }
          } else {
            this.$message.error('保存失败：' + res.message)
          }
        }).finally((res) => {
          this.confirmLoading = false
          this.$nextTick(() => this.getEchartData(false))
        })

      },
      changeInput(value, record, column) {
        let param = {}
        param['issueId'] = record.issueId
        param[column] = value.target.value
        updateReviewUncheck(param).then((res) => {
          if (res.success) {
            //this.callReviewList()
          } else {
            this.$message.error('保存失败：' + res.message)
          }
        }).finally((res) => {
          this.confirmLoading = false
        })

      },


      projectLeaderClick(record) {
        this.updateIssueid = record.issueId
        this.dropdownvisible = true
        this.account = record.projectLeader
      },

      callGetDepts(){
            getCateTree({
                fieldName:'department',
                // flag:1
            }).then((res)=>{
                if (res.success) {
                    this._depts = res.data
                } else {
                    this.$message.error('错误提示：' + res.message, 1)
                }
            }).catch((err) => {
                this.$message.error('错误提示：' + err.message, 1)
            });
        },


      async callReviewList() {
        this.loading = true

        this.paramquery = {...this.paramquery,reviewListNum: this.listType,dateMonth:this.listType==4?this.$route.query.dateMonth:null}

        await getReviewListData(this.paramquery)
          .then((res) => {
            if (res.success) {

              this.allTotal = 0,
                this.passTotal = 0,
                this.unPassTotal = 0,
                this.unCheckTotal = 0,
                this.checkTotal = 0,
                this.unConfirmTotal = 0,
                this.sLevel = 0,
                this.aLevel = 0,
                this.pLevel = 0,
                this.list = res.data ? res.data : []
              this.allTotal = this.list.length

              let listIn = []
              for (let i = 0; i < this.list.length; i++) {
                //一次审核且结果不为空 || 二次审核且结果不为空
                if (((this.list[i].reviewResult1 != null || this.list[i].reviewResult1 != '' || this.list[i].reviewResult1 != 0) && this.list[i].secondReview == 0) ||
                  ((this.list[i].reviewResult2 != null || this.list[i].reviewResult2 != '' || this.list[i].reviewResult2 != 0) && this.list[i].secondReview == 1)
                ) {
                  this.checkTotal++;

                  //项目等级（S+、S、A、B、C）X为无等级
                  if (this.list[i].projectLevel == "1" || this.list[i].projectLevel == "2") {
                    if (this.list[i].reviewResult1 == 1 || this.list[i].reviewResult2 == 1) {
                      this.sLevel++;
                    }

                  } else if (this.list[i].projectLevel == "3") {
                    if (this.list[i].reviewResult1 == 1 || this.list[i].reviewResult2 == 1) {
                      this.aLevel++;
                    }

                  } else if (this.list[i].projectLevel == "4") {
                    if (this.list[i].reviewResult1 == 1 || this.list[i].reviewResult2 == 1) {
                      this.pLevel++;
                    }

                  }
                }

                //一次审核且结果为空  || 二次审核且结果为空
                if (((this.list[i].reviewResult1 == null || this.list[i].reviewResult1 == '' || this.list[i].reviewResult1 == 0) && this.list[i].secondReview == 0) ||
                  ((this.list[i].reviewResult2 == null || this.list[i].reviewResult2 == '' || this.list[i].reviewResult2 == 0) && this.list[i].secondReview == 1)
                ) {
                  this.unCheckTotal++;
                } else {
                  listIn.push(this.list[i])
                }


                //一次审核且结果为不通过 || 二次审核且结果为不通过
                if ((this.list[i].reviewResult1 == 2 && this.list[i].secondReview == 0) || (this.list[i].reviewResult2 == 2 && this.list[i].secondReview == 1)) {
                  this.unPassTotal++;
                }

                //一次审核且结果为通过 || 二次审核且结果为通过
                if ((this.list[i].reviewResult1 == 1 && this.list[i].secondReview == 0) || (this.list[i].reviewResult2 == 1 && this.list[i].secondReview == 1)) {
                  this.passTotal++;
                }
                //一次审核且结果为再确认 || 二次审核且结果为再确认
                if ((this.list[i].reviewResult1 == 3 && this.list[i].secondReview == 0) || (this.list[i].reviewResult2 == 3 && this.list[i].secondReview == 1)) {
                  this.unConfirmTotal++;
                }
              }

              this.listIn = JSON.parse(JSON.stringify(listIn))


            } else {
              this.$message.error(res.message, 1);
            }
            this.loading = false
          })
          .catch((err) => {
            this.loading = false
            this.$message.error('错误提示：' + err.message, 1)
          });
      },
      removeOneColumn(columns,dataIndex) {
        // console.log("移除该列:" + dataIndex);
        // columns.forEach((column,index) => {
        //   if (column.dataIndex === dataIndex) {
        //     columns.splice(index, 1)
        //   }
        // })
        for (let i = 0; i < columns.length; i++) {
          if (columns[i].dataIndex === dataIndex) {
            columns.splice(i, 1)
            break
          }
        }
      },
      removeColumnsCate(){
        if (this.listType === 4 || this.listType === 1) {//院长评审页隐藏分类字段
          this.removeOneColumn(this.columns,"cate1")
          this.removeOneColumn(this.columnsIn,"cate1")
        }
      },
    },
    created() {
      this.callGetDepts()
      this.removeColumnsCate()
    },
  }
</script>


<style lang='less' scoped=''>


/deep/.vue-treeselect__multi-value-item{
    background: transparent;
    font-size: 13px;
    vertical-align: initial;
}
/deep/.vue-treeselect{
    /* display: inline-block; */
    min-width: 80%;
    max-width: 90%;
    margin-top: 4px;
}
/deep/.vue-treeselect__control{
    display: flex;
    align-items: center;
    justify-content: center;
    height: 24px;
    overflow: hidden;
    border-radius: initial;
}
/deep/.vue-treeselect__control *{
    padding: 0 !important;
    margin: 0 !important;
    line-height:initial !important;
    white-space: nowrap;
}
/deep/.vue-treeselect__value-remove{
    color: #e9e9e9;
}
/deep/.vue-treeselect__multi-value-item{
    color: #b2b0b0;
}

  /deep/ .ant-select-selection__rendered, /deep/ .ant-select-selection--single {
    height: 100%;
    line-height: 100%;
    font-size: 12px;
    margin: 0;
    border: 0;
    background: transparent;
    text-align: center;
    display: flex;
    flex-direction: row;
    justify-content: center;
  }
  /deep/ .ant-select {
    width: 100%;
  }
  /deep/ .ant-select-dropdown-menu-item {
    text-align: center;
  }

  /deep/ .ant-select-dropdown-menu {
    text-align: center!important;
  }

  /deep/ .ant-col-md-8 {
    height: 38px;
  }

  .btn {
    padding: 8px 10px;
    background: #1890FF;
    color: #fff;
    font-weight: initial;
    font-size: 12px;
    cursor: pointer;
    display: inline-block;
  }

  .red {
    display: block;
    background: #ff3333;
    text-align: center;
    color: #fff;
  }

  .yellow {
    display: block;
    background: #fac858;
    text-align: center;
    color: #fff;
  }

  .green {
    display: block;
    background: #58a55c;
    text-align: center;
    color: #fff;
  }

  .title {
    background: #fff;
    padding: 15px 24px 0 24px;
    font-weight: 700;
    font-size: 16px;
    line-height: 1.5;
    text-align: right;
  }

  /deep/ .ant-btn {
    height: initial;
    border: none;
  }

  .title1 {
    background: #fff;
    font-weight: 700;
    font-size: 20px;
    line-height: 1.5;
  }

  /deep/ .m_text .temp {
    background: #fff;
  }
  /deep/ .ant-table-small > .ant-table-content > .ant-table-header > table > .ant-table-thead > tr > th, 
  /deep/ .ant-table-small > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th,
   /deep/ .ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-header > table > .ant-table-thead > tr > th,
    /deep/ .ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-thead > tr > th,
    /deep/  .ant-table-small > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table > .ant-table-thead > tr > th, 
     /deep/ .ant-table-small > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table > .ant-table-thead > tr > th,
      /deep/ .ant-table-small > .ant-table-content > .ant-table-fixed-left > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-thead > tr > th,
       /deep/ .ant-table-small > .ant-table-content > .ant-table-fixed-right > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-thead > tr > th{
        background-color: #f5f5f5;
  }

  /deep/ .ant-table-thead > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td .m_text .temp,
  /deep/ .ant-table-tbody > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td .m_text .temp,
  /deep/ .ant-table-thead > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td .m_text .temp,
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td .m_text .temp {
    background: #e6f7ff;
  }


  /deep/ .ant-table-small > .ant-table-content > .ant-table-body > table > .ant-table-tbody > tr > td, .ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-header > table > .ant-table-tbody > tr > td, .ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-tbody > tr > td, .ant-table-small > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table > .ant-table-tbody > tr > td, .ant-table-small > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table > .ant-table-tbody > tr > td, .ant-table-small > .ant-table-content > .ant-table-fixed-left > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-tbody > tr > td, .ant-table-small > .ant-table-content > .ant-table-fixed-right > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-tbody > tr > td {
    padding: 2px 2px;
  }

  input {
    color: #000;
    height: 23px;
    line-height: 23px;
    width: 100%;
    font-size: 14px;
    border: 1px solid #d9d9d9;
    outline: none;
    text-align: center;
  }

.input-name input {
  height: 32px;
  line-height: 32px;
  margin-top: 4px;
}

  textarea {
    color: #000;
    width: 100%;
    font-size: 14px;
    border: 1px solid #d9d9d9;
    outline: none;
  }

  .content {
    padding-top: 0;
    padding-bottom: 0;
  }

  .box1 {
    width: 47%;
    float: left;
  }

  .box2 {
    width: 52%;

  }

  .box3 {
    width: 47%;
    float: left;
  }

  .box4 {
    width: 46%;

  }

  /deep/ .ant-btn {

    padding: 0;

  }
  /deep/ .ant-select-selection-selected-value {
    color: black;
  }

  .btn_pn {
    display: block;
    min-height: 18px;
    min-width: 70px;
  }

  .ellipsis {
    overflow: hidden;
    display: -webkit-inline-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    vertical-align: middle;
    text-align: center;
  }


  /deep/.ant-card-body {
    padding: 0 0 0 24px;
  }

  .cumuDownModal /deep/ .ant-modal-body {
    padding: 12px;
    background: #fff;
  }

  .cumuDownModal /deep/ .ant-modal-header, .cumuDownModal /deep/ .ant-modal-close-x, .cumuDownModal /deep/ .ant-modal-footer {
    display: none;
  }

  .cumuDownModal /deep/ .ant-modal {
    top: 100px;
  }
  /deep/.ant-select-selection-selected-value{
    overflow: initial;
    white-space: initial;
  }
</style>
<style lang='css'>
  .ant-select-dropdown-menu-item {
    font-size: 12px;
    padding: 4px 8px;
    text-align: center;
  }

  .ant-modal-body {
    padding: 0 24px;
  }

  .ant-modal {
    top: 50px;
  }

  .ant-select-arrow .ant-select-arrow-icon svg {

    display: none;
  }
  .ant-select-dropdown-menu {
    text-align: center!important;
  }

  button {
    border: 0;
  }

  input {

    height: 100%;
    width: 100%;
  }

  .ant-select-dropdown-menu-item {
    padding: 2px;
  }
</style>