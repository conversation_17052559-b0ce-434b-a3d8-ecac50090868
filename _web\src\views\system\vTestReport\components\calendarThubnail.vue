<template>
  <a-drawer placement="left" :visible="visible" @close="handleClose">
    <div class="thumbnail-content" @mouseleave="handleClose">

      <template v-for="(item,index) in thumbnailList"  >
        <div class="flex-column-center" v-if="item.show">
          <div class="thumbnail-block" @click="handleChoose(item.id,index)">
            <div class="title">{{ item.subheading}}</div>
            <div :id="`${item.id}Thumbnail`" :ref="`${item.id}Thumbnail`" style="width: 125px;height: 70px;"></div>
          </div>
        </div>
      </template>



    </div>
  </a-drawer>
</template>
<script>
  export default {
    props: {
      thumbnailList: {
        type: Array,
        default: []
      },
    },
    data() {
      return {
        visible: true,
        thumbnailArr:this.thumbnailList.map(item => {return item.id}),
      }
    },
    mounted() {
      // 定时器强行异步获取
      setTimeout(() => {
        this.thumbnailArr.forEach((v, index) => {
          if (this.thumbnailList[index].show) {
            this.initThumbnailEchart(v, index)
          }
        })
      }, 0)
    },
    methods: {
      initThumbnailEchart(target, index) {
        const thumbnailEchart = this.echarts.init(document.getElementById(`${target}Thumbnail`))
        let cyclicList = _.cloneDeep(this.thumbnailList[index].echartList)
        let echartY2List = this.thumbnailList[index].echartY2List ? _.cloneDeep(this.thumbnailList[index].echartY2List) : []
        const options = this._handleThumbnailEchart(cyclicList, echartY2List)
        thumbnailEchart.setOption(options)
      },
      _handleThumbnailEchart(cyclicList, echartY2List = []) {
        let series = []
        cyclicList.forEach((v, index) => {
          v.echartDataList = v.data.filter(filter => filter.value ? filter.value[1] !== '' && filter.value[1] !== null : filter[1] !== '' && filter[1] !== null )

          const multiplier = Math.trunc(v.echartDataList.length / 10)

          const seriesData = v.echartDataList.length > 10 ? new Array(10).fill(1).map((mapItem, mapIndex) => v.echartDataList[mapIndex * multiplier]) : v.echartDataList

          series.push(
            {
              name: v.sampleCode,
              type: 'line',
              symbol: 'none',
              data: seriesData
            }
          )

        })

        // 添加次Y轴数据
        if (echartY2List != []) {
          echartY2List.forEach((v, index) => {
            v.echartDataList = v.data.filter(filter => filter.value ? filter.value[1] !== '' && filter.value[1] !== null : filter[1] !== '' && filter[1] !== null )

            const multiplier = Math.trunc(v.echartDataList.length / 10)

            const seriesData = v.echartDataList.length > 10 ? new Array(10).fill(1).map((mapItem, mapIndex) => v.echartDataList[mapIndex * multiplier]) : v.echartDataList

            series.push(
              {
                name: v.sampleCode,
                type:'line',
                symbol:'none',
                lineStyle: {
                  type: "dashed"
                },
                data:seriesData
              }
            )

          })
        }

        return {
          grid: {
            show: true,
            top: 1,
            left: 1,
            right: 1,
            bottom: 4,
          },
          xAxis: {
            type: 'value',
            axisLabel: { show: false },
            axisTick: { show: false }
          },
          yAxis: {
            type: 'value',
            axisLabel: { show: false },
            axisTick: { show: false }
          },
          series
        }
      },
      handleClose(){
        this.$emit('cancel')
      },
      handleChoose(id,index){
        this.$emit('choose',{id,index})
      },
    }
  }
</script>
<style lang="less" scoped>

  .flex-column-center {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    cursor: pointer;
  }

  .thumbnail-content .thumbnail-block {
    width: 150px;
    height: 100px;
    margin-bottom: 10px;
    background: #f0f2f5;
    border-radius: 5px;

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .thumbnail-content .thumbnail-block:hover {
    background: #fff;
  }

  .thumbnail-content .title {
    font-size: 12px;
    margin-bottom: 2px;
  }
</style>