<template>
  <div style="background-color: #FFFFFF;padding: 10px">


    <!--<div style="float: right;position: relative;z-index: 1;padding-bottom: 5px">


    </div>-->
    <div style="float: left;position: relative;z-index: 1;padding-bottom: 5px;width: 65%">
      <a-row :gutter="[8,8]">
        <a-col :span="4">
          <a-form-item label="温箱编号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryparam.code" @keyup.enter="getList(true)" @change="getList(true)"/>
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-form-item label="温箱型号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryparam.model" @keyup.enter="getList(true)" @change="getList(true)"/>
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-form-item label="温度" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input-number v-model="queryparam.tem" @keyup.enter="getList(true)" @change="getList(true)"/>
          </a-form-item>
        </a-col>

        <a-col :span="4">
          <a-form-item label="委托单号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryparam.folderNo" @keyup.enter="getList(true)" @change="getList(true)"/>
          </a-form-item>
        </a-col>

        <a-col :span="4">
          <a-form-item label="温箱类型" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-select v-model="queryparam.type" @change="getList(true)" style="width: 100%" :allow-clear="true">
              <a-select-option value="高低温箱">
                高低温箱
              </a-select-option>
              <a-select-option value="高温箱">
                高温箱
              </a-select-option>
              <a-select-option value="412常温存储室">
                412常温存储室
              </a-select-option>
              <a-select-option value="413常温存储室">
                413常温存储室
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-form-item label="电池类型" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-select v-model="queryparam.batteryType" @change="getList(true)" style="width: 100%" :allow-clear="true">
              <a-select-option value="G圆柱">
                G圆柱
              </a-select-option>
              <a-select-option value="C圆柱">
                C圆柱
              </a-select-option>
              <a-select-option value="V圆柱">
                V圆柱
              </a-select-option>
              <a-select-option value="方形">
                方形
              </a-select-option>
              <a-select-option value="软包">
                软包
              </a-select-option>
              <a-select-option value="G26">
                G26
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <!--<a-col :span="6"
               v-if="userInfo.account == 'superAdmin' || userInfo.roles.find(r => r.id == 1694647012972855298) != null">
          <a-form-item label="创建人" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryparam.createName" @keyup.enter="getList(true)" @change="getList(true)"/>
          </a-form-item>
        </a-col>-->

      </a-row>
    </div>
    <div style="float: right;position: relative;z-index: 1;margin-top: 10px">

      <a-button style="margin-left: 8px;" @click="getList(true)" type="primary">刷新</a-button>
    </div>


    <div class="box" ref="box">

      <div class="mid">
        <s-table :columns="columns" :data="loadData" bordered :scroll="{x:true}" :rowKey="(record) => record.id"
                 ref="table2">




          <template
            slot="totalPcsInNum"
            slot-scope="text, record, index, columns"
          >
            <a slot="totalPcsInNum" v-if="text > 0" @click="openDetail(record)">{{text}}</a>
            <span slot="totalPcsInNum" v-else>{{text}}</span>

          </template>


        </s-table>

      </div>
    </div>

    <a-modal title="存放电芯" width="50%" :visible="visible3" :footer="null"
             @cancel="() => visible3 = false">
      <a-row :gutter="[4,4]">
        <a-col :span="6">
          <a-form-item label="产品名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="detailQueryparam.productName" @keyup.enter="getInOutDetailList" @change="getInOutDetailList"/>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="样品编号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="detailQueryparam.sampleCode" @keyup.enter="getInOutDetailList" @change="getInOutDetailList"/>
          </a-form-item>
        </a-col>


      </a-row>
      <a-table :columns="batteryInOutColumns" :data-source="batteryInOutData" :rowKey="(record) => record.id"
      >

      </a-table>

    </a-modal>

  </div>
</template>

<script>
  import {
    testWarmBoxPage, inOutDetailList
  } from '@/api/modular/system/warmBoxManager'
  import {mapGetters} from "vuex";
  import axios from 'axios'
  import {
    ACCESS_TOKEN
  } from '@/store/mutation-types'
  import {STable} from '@/components'
  import Vue from "vue";
  import jsonBigint from 'json-bigint'

  export default {
    components: {
      STable
    },
    computed: {
      ...mapGetters(['userInfo', 'testTaskFilterData']),
    },
    data() {
      return {
        batteryInOutData:[],
        loadData: parameter => {

          return testWarmBoxPage(Object.assign(parameter, this.queryparam)).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: [],
        beforeDeleteFlag: false,
        id: null,
        visible3:false,
        batteryInOutColumns: [
          {
            title: '序号',
            align: 'center',
            dataIndex: 'index',
            width: 50,
            customRender: (text, record, index) => `${index + 1}`
          },{
            title: '存储阶段',
            align: 'center',
            dataIndex: 'progressDetailOrderNumber',
            width: 50,
            customRender: (text, record, index) => `${text -1 }`
          },{
            title: '进箱时间',
            align: 'center',
            dataIndex: 'inDate',
            width: 80,
          },{
            title: '出箱时间',
            align: 'center',
            dataIndex: 'actOutDate',
            width: 80,
          },{
            title: '存放天数',
            align: 'center',
            dataIndex: 'day',
            width: 50,
          },{
            title: '产品名称',
            align: 'center',
            dataIndex: 'productName',
            width: 50,
          },{
            title: '样品编号',
            align: 'center',
            dataIndex: 'sampleCode',
            width: 100
          }


        ],
        columns: [
          {
            title: '序号',
            align: 'center',
            dataIndex: 'index',
            width: 50,
            customRender: (value, row, index) => {
              const obj = {
                children: value,
                attrs: {},
              };
              if (row.isFirst) {
                obj.attrs.rowSpan = row.rowSpan;
              } else {
                obj.attrs.rowSpan = 0;
              }
              return obj;
            }
          },
          {
            title: '温箱硬件基础信息',
            children: [
              {
                title: '温箱类型',
                dataIndex: 'type',
                align: 'center',
                width: 90,
                customRender: (value, row, index) => {
                  const obj = {
                    children: value,
                    attrs: {},
                  };
                  if (row.isFirst) {
                    obj.attrs.rowSpan = row.rowSpan;
                  } else {
                    obj.attrs.rowSpan = 0;
                  }
                  return obj;
                }
              }, {
                title: '温箱编号',
                width: 90,
                align: 'center',
                dataIndex: 'code',
                customRender: (value, row, index) => {
                  const obj = {
                    children: value,
                    attrs: {},
                  };
                  if (row.isFirst) {
                    obj.attrs.rowSpan = row.rowSpan;
                  } else {
                    obj.attrs.rowSpan = 0;
                  }
                  return obj;
                }
                //scopedSlots: {customRender: 'updateText'},
              }, {
                title: '温箱型号',
                width: 90,
                align: 'center',
                dataIndex: 'model',
                customRender: (value, row, index) => {
                  const obj = {
                    children: value,
                    attrs: {},
                  };
                  if (row.isFirst) {
                    obj.attrs.rowSpan = row.rowSpan;
                  } else {
                    obj.attrs.rowSpan = 0;
                  }
                  return obj;
                }
                //scopedSlots: {customRender: 'updateText'},
              }, {
                title: '温箱容积/L',
                width: 90,
                align: 'center',
                dataIndex: 'volume',
                customRender: (value, row, index) => {
                  const obj = {
                    children: value,
                    attrs: {},
                  };
                  if (row.isFirst) {
                    obj.attrs.rowSpan = row.rowSpan;
                  } else {
                    obj.attrs.rowSpan = 0;
                  }
                  return obj;
                }
                //scopedSlots: {customRender: 'updateText'},
              }, {
                title: '温箱样品层架号',
                width: 90,
                align: 'center',
                dataIndex: 'storeyCode',
                //scopedSlots: {customRender: 'updateText'},
              }
            ]

          },

          {
            title: '温箱测试信息',
            children: [
              {
                title: '电池类型',
                width: 90,
                align: 'center',
                dataIndex: 'batteryType',
                //scopedSlots: {customRender: 'updateText'},
              }, {
                title: '测试方法',
                width: 90,
                align: 'center',
                dataIndex: 'testMethod',
                customRender: (value, row, index) => {
                  const obj = {
                    children: value,
                    attrs: {},
                  };
                  if (row.isFirst) {
                    obj.attrs.rowSpan = row.rowSpan;
                  } else {
                    obj.attrs.rowSpan = 0;
                  }
                  return obj;
                }
                //scopedSlots: {customRender: 'updateText'},
              }, {
                title: '温箱温度/℃',
                width: 90,
                align: 'center',
                dataIndex: 'tem',
                customRender: (value, row, index) => {
                  const obj = {
                    children: value,
                    attrs: {},
                  };
                  if (row.isFirst) {
                    obj.attrs.rowSpan = row.rowSpan;
                  } else {
                    obj.attrs.rowSpan = 0;
                  }
                  return obj;
                }
                //scopedSlots: {customRender: 'updateText'},
              }, {
                title: '在测周期',
                width: 90,
                align: 'center',
                dataIndex: 'period',
                customRender: (value, row, index) => {
                  const obj = {
                    children: row.periodSymbol+value,
                    attrs: {},
                  };
                  if (row.periodSymbolIsFirst) {
                    obj.attrs.rowSpan = row.periodSymbolRowSpan;
                  } else {
                    obj.attrs.rowSpan = 0;
                  }
                  return obj;
                }
                //scopedSlots: {customRender: 'updateText'},
              },
            ]
          },

          {
            title: '样品层架数/层',
            width: 100,
            align: 'center',
            dataIndex: 'totalStorey',
            customRender: (value, row, index) => {
              const obj = {
                children: value,
                attrs: {},
              };
              if (row.isFirst) {
                obj.attrs.rowSpan = row.rowSpan;
              } else {
                obj.attrs.rowSpan = 0;
              }
              return obj;
            }
            //scopedSlots: {customRender: 'updateText'},
          },

          {
            title: 'C&G圆柱',
            children: [
              {
                title: '每层层架吸塑盒摞数/摞',
                width: 150,
                align: 'center',
                dataIndex: 'storeyXshLNum',
                //scopedSlots: {customRender: 'updateText'},
              }, {
                title: '每摞吸塑盒数/个',
                width: 120,
                align: 'center',
                dataIndex: 'xshLHNum',
                //scopedSlots: {customRender: 'updateText'},
              }, {
                title: '每个吸塑盒可放电池数/pcs',
                width: 150,
                align: 'center',
                dataIndex: 'xshPcsNum',
                //scopedSlots: {customRender: 'updateText'},
              }, {
                title: '每层可放电池数',
                width: 100,
                align: 'center',
                dataIndex: 'storeyPcsNum',
                customRender: (value, row, index) => {
                  return row.storeyXshLNum != null && row.storeyXshLNum != 0 ?value:0
                }
                //scopedSlots: {customRender: 'updateText'},
              }
            ]
          },
          {
            title: 'V圆柱',
            children: [
              {
                title: '单层放托盘数/个',
                width: 90,
                align: 'center',
                dataIndex: 'storeyTpGNum',
                //scopedSlots: {customRender: 'updateText'},
              }, {
                title: '每个托盘电池数/pcs',
                width: 90,
                align: 'center',
                dataIndex: 'tpPcsNum',
                //scopedSlots: {customRender: 'updateText'},
              }, {
                title: '每层可放电池数',
                width: 90,
                align: 'center',
                dataIndex: 'storeyPcsNum',
                customRender: (value, row, index) => {
                  return row.storeyTpGNum != null && row.storeyTpGNum != 0 ?value:0
                }
                //scopedSlots: {customRender: 'updateText'},
              }
            ]
          },
          {
            title: '方形/软包',
            children: [
              {
                title: '每层可放电池数',
                width: 90,
                align: 'center',
                dataIndex: 'storeyPcsNum',
                customRender: (value, row, index) => {
                  return row.storeyTpGNum == 0 && row.storeyXshLNum == 0?value:0
                }
                //scopedSlots: {customRender: 'updateText'},
              }, {
                title: '可放吸塑盒/个',
                width: 90,
                align: 'center',
                customRender: (value, row, index) => {
                 return "0"
                }

              }
            ]
          }, {
            title: '总计',
            align: 'center',
            children: [
              {
                title: '温箱可放电池数/pcs',
                width: 90,
                align: 'center',
                dataIndex: 'totalPcsNum',
                customRender: (value, row, index) => {
                  const obj = {
                    children: row.storeyPcsNumNotNeedRowSpan?row.storeyPcsNum:value,
                    attrs: {},
                  };
                  if(!row.storeyPcsNumNotNeedRowSpan){
                    if (row.isFirst ) {
                      obj.attrs.rowSpan = row.rowSpan;
                    } else {
                      obj.attrs.rowSpan = 0;
                    }
                  }else{
                    obj.attrs.rowSpan = 1;
                  }


                  return obj;
                }
                //scopedSlots: {customRender: 'updateText'},
              }
            ]
          }, {
            title: '现状',
            children: [
              {
                title: '已存放吸塑盒数/托盘数',
                width: 90,
                align: 'center',
                dataIndex: 'totalXshInNum',

                //scopedSlots: {customRender: 'updateText'},
              }, {
                title: '已存放电池数/pcs',
                width: 90,
                align: 'center',
                dataIndex: 'totalPcsInNum',

                scopedSlots: {customRender: 'totalPcsInNum'},
              }
            ]
          },

        ],
        record: {},
        labelCol: {
          xs: {
            span: 12
          },
          sm: {
            span: 8
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 14
          }
        },
        visible1: false,
        confirmLoading1: false,
        showExport: true,
        queryparam: {},
        detailQueryparam: {},
        record:{}
        /*headers: {
          //Authorization: 'Bearer ' + Vue.ls.get(ACCESS_TOKEN),
          Authorization: 'Bearer eyJhbGciOiJIUzUxMiJ9.***********************************************************************************************************************************************************************************************************************.LgAC7GwkaFwV5NJD7wrr4gQGYzFMfbJL46hei2EclvHWVE1WGgV8ucyJ5XkuF5Cio_24ZuIqwAyVXyD02snFmQ',
        },*/
      }
    },

    mounted() {

      this.$nextTick(() => {
        this.$refs.table2.refresh()
      })
    },
    methods: {



      openDetail(record){
        this.record = record
        inOutDetailList({cengId:record.id}).then(res => {
          this.batteryInOutData = res.data
          this.visible3 = true
        })
      },

      getInOutDetailList(){

        this.detailQueryparam.cengId = this.record.id

        inOutDetailList(this.detailQueryparam).then(res => {
          this.batteryInOutData = res.data
          this.visible3 = true
        })
      },

      getList(flag) {
        if (flag) {
          this.id = null
        }
        this.$refs.table2.refresh()
      }

    }
  }
</script>
<style lang="less" scoped>
  .tips {
    color: #1890ff;
  }

</style>
