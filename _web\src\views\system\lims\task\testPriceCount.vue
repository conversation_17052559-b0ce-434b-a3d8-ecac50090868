<template>
  <div style="padding: 10px;">
    <pbiTabs :tabsList="laboratoryList" :activeKey="laboratoryId" @clickTab="callback"></pbiTabs>
    <tableIndex
        :pageLevel='1'
        :tableTotal= 'tableTotal'
        :pageTitleShow=false
        :otherHeight="parseInt(105)"
        :loading='tableLoading'
        @paginationChange="handlePageChange"
        @paginationSizeChange="handlePageChange"
        @tableFocus="tableFocus"
        @tableBlur="tableBlur"
    >
      <template #search>
        <pbiSearchContainer>
          <pbiSearchItem label='时间维度' :span="4">
            <a-select style="width: 100%;" v-model="timeDimension" @change="getTestPriceStatistics">
              <a-select-option value="按周">按周</a-select-option>
              <a-select-option value="按月">按月</a-select-option>
            </a-select>
          </pbiSearchItem>
          <pbiSearchItem label='起止日期' :span="6">
            <a-range-picker :allowClear="false" v-model="timeRange" @change="timeRangeChange"></a-range-picker>
          </pbiSearchItem>

          <pbiSearchItem :span="14" type='btn'>
            <a-button style="margin-right: 8px;" @click="getTestPriceStatistics" type="primary">查询</a-button>
            <a-button style="margin-right: 8px;" @click="reset">重置</a-button>
            <a-button type="primary" @click="exportTestPriceStatistics">导出</a-button>
          </pbiSearchItem>
        </pbiSearchContainer>
      </template>
      <template #table>
        <ag-grid-vue class='ag-theme-balham'
                     :style="{height:`${tableHeight}px`}"
                     :tooltipShowDelay="0"
                     :columnDefs='columns'
                     :rowData='dataList'
                     :defaultColDef='defaultColDef'>
        </ag-grid-vue>
      </template>
    </tableIndex>
  </div>
</template>

<script>
import XLSX from "xlsx/dist/xlsx.full.min.js";
import {saveAs} from "file-saver";
import {getTestPriceStatistics} from "@/api/modular/system/testProgressManager";
import moment from "moment";
import pbiTabs from "@/components/pageTool/components/pbiTabs.vue";
require('moment-timezone');

export default {
  name: "testPriceCount",
  components: {
    pbiTabs
  },
  data() {
    return {
      laboratoryList: [
        {value:'HZ_YJ_DL_JM', label:'精密实验室', show: true}
      ],
      laboratoryId: "HZ_YJ_DL_JM",
      testPriceStatistics: [],
      dataList: [],
      depts: [],
      columns: [],
      pageNo: 1,
      pageSize: 20,
      tableTotal: 0,
      tableLoading: true,
      tableHeight: 400,
      defaultColDef: {
        filter: false,
        floatingFilter: false,
        editable: false,
        cellStyle: { 'text-align': 'center' }
      },
      timeRange: [],
      timeRangeBegin: null,
      timeRangeEnd: null,
      timeDimension: "按周",
    }
  },
  created() {
    // 默认展示当年数据，获取当年1号、今天日期
    const firstDayOfYear = moment().startOf('year'); // 当年1号
    const today = moment();// 当前日期
    this.timeRange = [firstDayOfYear, today];
    // 传递到后端格式：YYYY-MM-DD
    this.timeRangeBegin = firstDayOfYear.format('YYYY-MM-DD');
    this.timeRangeEnd = today.format('YYYY-MM-DD');

    this.getTestPriceStatistics();
  },
  computed: {},
  mounted() {
    this.handleHeight()
  },
  methods: {
    handleHeight() {
      this.tableHeight = document.body.clientHeight - 105 - 100
    },
    handlePageChange(value) {
      let {current, pageSize} = value
      this.pageNo = current
      this.pageSize = pageSize
      this.dataList = this.testPriceStatistics.slice((this.pageNo - 1)*this.pageSize, this.pageNo*this.pageSize)
    },
    // 鼠标进入
    tableFocus() {
      this.$el.style.setProperty('--scroll-border-bottom-fixed', 'none');
      this.$el.style.setProperty('--scroll-display', 'unset');
      this.$el.style.setProperty('--scroll-border-bottom', '1px solid #Dee1e8');
    },
    // 鼠标移出
    tableBlur() {
      this.$el.style.setProperty('--scroll-border-bottom-fixed', '1px solid #dee1e8');
      this.$el.style.setProperty('--scroll-display', 'none');
      this.$el.style.setProperty('--scroll-border-bottom', 'none');
    },
    callback(key) {
      this.laboratoryId = key
    },
    timeRangeChange(a, b) {
      this.timeRangeBegin = b[0]
      this.timeRangeEnd = b[1]
      this.getTestPriceStatistics()
    },
    reset() {
      this.timeDimension = "按周"
      // 默认展示当年数据，获取当年1号、今天日期
      const firstDayOfYear = moment().startOf('year'); // 当年1号
      const today = moment();// 当前日期
      this.timeRange = [firstDayOfYear, today];
      // 传递到后端格式：YYYY-MM-DD
      this.timeRangeBegin = firstDayOfYear.format('YYYY-MM-DD');
      this.timeRangeEnd = today.format('YYYY-MM-DD');
      this.getTestPriceStatistics()
    },
    getTestPriceStatistics() {
      this.tableLoading = true
      getTestPriceStatistics({
        timeRangeBegin: this.timeRangeBegin,
        timeRangeEnd: this.timeRangeEnd,
        timeDimension: this.timeDimension
      }).then((res) => {
        if (res.success) {
          this.testPriceStatistics = res.data
          this.tableTotal = this.testPriceStatistics.length
          this.dataList = this.testPriceStatistics.slice((this.pageNo - 1)*this.pageSize, this.pageNo*this.pageSize)

          // 部门个数不确定，先将部门放到集合中
          let deptSet = new Set()
          this.testPriceStatistics.forEach(row => {
            Object.keys(row).forEach(key => {
              if (key !== 'period') {
                deptSet.add(key)
              }
            })
          })
          this.depts = Array.from(deptSet).sort()

          // 处理表头
          this.columns = [
            {
              headerName: "周期",
              width: 80,
              field: "period",
              pinned:'left',
            },
            ...this.depts.map(dept => {
              return {
                headerName: dept,
                flex: 1,
                minWidth: 100,
                field: dept,
                cellRenderer: function (params) {
                  return params.value ? params.value : "-"
                }
              }
            })
          ]
        }
      }).finally(() => {
        if (this.pageNo > 1 && this.dataList.length === 0) {
          this.pageNo = Math.max(1, Math.ceil(this.tableTotal / this.pageSize))
          this.dataList = this.testPriceStatistics.slice((this.pageNo - 1)*this.pageSize, this.pageNo*this.pageSize)
        }
        this.tableLoading = false
      })
    },
    exportTestPriceStatistics() {
      // 创建二维数组
      let rowDatas = [
        ['周期', ...this.depts],
        ...this.testPriceStatistics.map(row => {
          let rowData = [row.period]
          this.depts.forEach(dept => {
            rowData.push(row[dept] || '-')
          })
          return rowData
        })
      ]
      // 创建工作簿
      let workSheet = XLSX.utils.aoa_to_sheet(rowDatas);
      let workbook = {
        Sheets: {['测试费用统计']: workSheet},
        SheetNames: ['测试费用统计']
      };
      // 导出为Excel文件
      let excelBuffer = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
      let blob = new Blob([excelBuffer], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
      saveAs(blob, '测试费用统计' + this.formattedDate(new Date()) + '.xlsx');
    },
    formattedDate(date) {
      const year = date.getFullYear();
      const month = ("0" + (date.getMonth() + 1)).slice(-2);
      const day = ("0" + date.getDate()).slice(-2);
      const hours = ("0" + date.getHours()).slice(-2);
      const minutes = ("0" + date.getMinutes()).slice(-2);
      return `${year}${month}${day}${hours}${minutes}`;
    },
  }
}
</script>

<style lang="less" scoped>
@import '/src/components/pageTool/style/pbiSearchItem.less';

:root {
  --scroll-display: none;
  --scroll-border-bottom: none;
  --scroll-border-bottom-fixed: none;
}
/deep/.ag-body-horizontal-scroll{
  border-bottom: var(--scroll-border-bottom) !important;
}
/deep/.ag-body-horizontal-scroll-viewport {
  display: var(--scroll-display) !important;
  border-bottom: var(--scroll-border-bottom) !important;
}

/deep/.ag-horizontal-left-spacer,
/deep/.ag-horizontal-right-spacer{
  border-bottom: var(--scroll-border-bottom-fixed) !important;
}
</style>