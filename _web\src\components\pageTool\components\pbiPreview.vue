<template>
  <a-drawer :visible="true" :bodyStyle="{ height: '100%' }" width="70%" :closable="false" placement="right"
    @close="handleClose">
    <iframe v-if="previewFileName.indexOf('pdf') !== -1 " :src="previewUrl" width="100%" height="100%"></iframe>
    <img v-else width="100%" height="100%" :src="previewUrl" alt="">
  </a-drawer>
</template>
<script>
  /*
  例：
        template
          <pbi-preview v-if="previewVisible" :previewFileName="previewFileName" :previewFileID="previewFileID"  @close="previewVisible = false"></pbi-preview>
          <pbi-preview v-if="previewVisible" :previewFileName="previewFileName" :previewFileUrl="previewFileUrl"  @close="previewVisible = false"></pbi-preview>
          
        script
          import pbiPreview from '@/components/pageTool/components/pbiPreview.vue'
          components : {
            pbiPreview
          },
          data(){
            return{
              previewVisible:false,
              previewFileName:'',
              previewFileID:'',
            }
          },
          methods:{
            // 预览文件+下载
            previewFile(record){
              // 如果是可预览的
              if(['.xbm','.tif','.pjp','.svgz','.jpg','.jpeg','.ico','.tiff','.gif','.svg','.jfif','.webp','.png','.bmp','.pjpeg','.avif','.pdf'].some(someItem => { return typeof record.docName === 'string' && record.docName.indexOf(someItem) !== -1})){
                this.previewVisible = true
                this.previewFileName = record.docName
                this.previewFileID = record.fileId
              }else{
                // 不可预览就下载
                const a = document.createElement('a') 
                a.style.display = 'none'
                a.href = '/api/sysFileInfo/preview?Authorization=Bearer '+Vue.ls.get('Access-Token')+'&id=' + record.fileId + "#navpanes=0"
                a.download = record.docName
                a.click()
              }
            }
          }
  */
  import Vue from "vue";
  export default {
    props: {
      previewFileName: {   // 预览文件名称
        type: String,
        default: ''
      },
      previewFileID: {   // 预览文件ID
        type: String,
        default: ''
      },
      previewFileUrl: {  //预览文件URl
        type: String,
        default: ''
      }

    },
    data() {
      return {
        previewUrl: '',
      }
    },
    created() {
      if (this.previewFileID) {
        this.previewUrl = '/api/sysFileInfo/previewPdf?Authorization=Bearer ' + Vue.ls.get('Access-Token') + '&id=' + this.previewFileID + '#toolbar=0'
      }

      if (this.previewFileUrl) {
        this.previewUrl = this.previewFileUrl
      }
    },
    methods: {
      handleClose() {
        this.$emit('close')
      }
    }
  }
</script>