<template>
  <div>
    <!--<div>
      <a-breadcrumb class="breadcrumb" separator=">">
        <a-breadcrumb-item><a @click="gotoIndex(-3)">首页看板</a></a-breadcrumb-item>
        <a-breadcrumb-item><a @click="gotoIndex(-2)">产品对齐表</a></a-breadcrumb-item>
        <a-breadcrumb-item><a @click="gotoIndex(-1)">{{$route.query.parentTitle}}</a></a-breadcrumb-item>
        <a-breadcrumb-item><a @click="gotoIndex(-1)">{{$route.query.title}}</a></a-breadcrumb-item>
        <a-breadcrumb-item>项目架构</a-breadcrumb-item>
      </a-breadcrumb>
    </div>-->
    <div style="background-color: #FFFFFF;">
      <div>
       <!--  <span style="padding-left: 20px;font-size: 20px;">项目架构</span> -->
        <div style="text-align:right;padding:5px">
          <a-upload
            :headers="headers" :action="postUrl" :multiple="false" :before-upload="beforeUpload"
            name="file" @change="handleChange" :showUploadList="false"  accept=".jpg, .jpeg, .png,.pdf"
          >
          <a-button type="primary">上传</a-button>

          </a-upload>
          <a-popconfirm placement="topRight" ok-text="确定" cancel-text="取消" @confirm="deleteByIssueId" v-if="projectIssue != null">
            <template slot="title">
              确认删除吗？
            </template>
            <a-button style="margin-left: 10px;margin-right: 10px;" type="primary">删除</a-button>
          </a-popconfirm>
        </div>

        <!-- <hr/> -->
      </div>
      <div style="text-align: center">
        <img  v-if="showImg" style="text-align: center;max-width:100%"
              :src="imgUrl" :onerror="defaultImg">  

        <iframe :src="imgUrl+'#toolbar=0&embedded=true'"  :height="windowHeight" :width="windowWidth" v-else-if="showPdf"></iframe>

        <div :style="{height:windowHeight+'px'}" v-else></div>
      </div>
    </div>

  </div>
</template>

<script>

  import noneimg from '@/assets/none.svg'
  import {
    getIssueByIssueId,
    addOrUpdateIssue,deleteByIssueId
  } from "@/api/modular/system/projectIssue"
  import Vue from "vue";
  import {
    ACCESS_TOKEN
  } from '@/store/mutation-types'
  import Iframe from "../../../../layouts/Iframe";

  export default {
    props: {
        issueId: {
            type: Number,
            default: 0
        },
        projectdetail: {
            type: Object,
            default: {}
        }
    },
    components: {Iframe},
    data(){
      return{
        defaultImg:'this.src="' + noneimg + '"',
        windowHeight: document.documentElement.clientHeight - 120,
        windowWidth: document.documentElement.clientWidth - 100,
        postUrl: '/api/sysFileInfo/uploadfile',
        imgUrl: '/api/sysFileInfo/preview?id=',
        projectIssue: {},
        showImg:true,
        showPdf:false,
        headers: {
          Authorization: 'Bearer ' + Vue.ls.get(ACCESS_TOKEN),
        },
      }
    },
    methods: {
      beforeUpload(file, e) {
        let isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'|| file.type === 'image/jpg' || file.type === 'application/pdf';
        if (!isJpgOrPng) {
          this.$message.error('格式错误，只能上传图片或pdf文件');
          return false;
        }
      },

      gotoIndex(index){
        this.$router.go(index)
      },
      handleChange(info) {
        if (info.file.status !== 'uploading') {

        }
        if (info.file.status === 'done') {


          let res = info.file.response
          if (res.success) {

            addOrUpdateIssue({issueId:this.issueId,
              frameworkId:res.data.id,frameworkType:info.file.type}).then(res1 => {
              this.projectIssue = res1.data

              if(!res1.data.frameworkId){
                this.showImg = false
                this.showPdf = false
              }else{
                if(res1.data.frameworkType == 'application/pdf'){
                  this.showPdf = true
                  this.imgUrl = '/api/sysFileInfo/previewPdf?id=' + res1.data.frameworkId
                  this.showImg = false
                }else{
                  this.showPdf = false
                  this.showImg = true
                  this.imgUrl = '/api/sysFileInfo/preview?id=' + res1.data.frameworkId
                }
              }


            })


            this.$message.success(`${info.file.name} 文件上传成功`)




            this.imgUrl= '/api/sysFileInfo/preview?id=' + res.data.id

          } else {
            this.$message.error(res.message)
          }
        } else if (info.file.status === 'error') {
          this.$message.error(`${info.file.name} 文件上传失败`);
        }
      },

      deleteByIssueId(){
        deleteByIssueId({issueId:this.issueId}).then(res =>{
          if(res.success){
            this.$message.success('删除成功')
            this.getIssueByIssueId()
          }else{
            this.$message.error('删除失败：' + res.message)
          }
        })
      },
      getIssueByIssueId(){
        getIssueByIssueId({issueId:this.issueId}).then(res =>{
          if(res.success){
            this.projectIssue = res.data


            if(null == res.data || !res.data.frameworkId){
              this.showImg = false
              this.showPdf = false
            }else{
              if(res.data.frameworkType == 'application/pdf'){
                this.showPdf = true
                this.showImg = false
                this.imgUrl = '/api/sysFileInfo/previewPdf?id=' + res.data.frameworkId
              }else{
                this.showPdf = false
                this.showImg = true
                this.imgUrl = '/api/sysFileInfo/preview?id=' + res.data.frameworkId
              }
            }



          }
        })
      }

    },



    mounted() {
      this.getIssueByIssueId()
    }
  }
</script>

<style lang="less" scoped=''>
  .breadcrumb{
    padding: 5px 0;
    padding-left: 13px;
  }.ant-breadcrumb a{
     color:#5d90fa !important;
   }.ant-breadcrumb{
      font-size: 12px !important;
    }
</style>