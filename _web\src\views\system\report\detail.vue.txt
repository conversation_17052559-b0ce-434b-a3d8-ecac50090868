<template>
  <div>
    <a-breadcrumb class="breadcrumb" separator=">">
      <a-breadcrumb-item><a @click="gotoIndex">信息对齐表</a></a-breadcrumb-item>
      <a-breadcrumb-item>产品开发进展</a-breadcrumb-item>
    </a-breadcrumb>
    <div class="head" v-if="showDetail">{{ cateTitle }}产品开发进展</div>
    <ve-table
      v-if="showDetail"
      :border-y="true"
      ref="tableRef"
      fixed-header
      :max-height="windowHeight > 380 ? windowHeight : 380"
      :columns="columns"
      :table-data="rows"
      :cell-style-option="cellStyleOption"
      :cell-span-option="cellSpanOption"
      :scroll-width="scrollWidth"
      :footer-data="footerData"
      :fixedFooter="fixedFooter"
      id="loading-detail"
      
    />
  </div>
</template>

<script>
const OtherComp = {
        name: "OtherComp",
        template: `
            <div class="feedback">
                <div v-for='item in arr'>{{item}}</div>
            </div>
        `,
        props: {
            arr: Array,
        },
    }
import { getReportProductData } from "@/api/modular/system/report"
import { mapActions, mapGetters } from 'vuex'
import { clamp } from '@/components'
import { ALL_APPS_MENU } from '@/store/mutation-types'
    import Vue from 'vue'
export default {
  name:'detail',
  data() {
    return {
      fixedFooter:false,
      scrollWidth:null,
      showDetail: false,
      loadingInstance: null,
      cateTitle:"",
      windowHeight: document.documentElement.clientHeight - 85,
      showTitle: {
        showTitle: true,
      },
      colsselects:[],
      columns: [],
      col4:[],
      col5:[],
      col2:[],
      col1:[],
      col0:[],
      col3:[],
      rows: [],
      merges: [],
      exmerges: [],
      pmerge:['productProjectName','productLevel'],
      footerData: [{ productName: "" }],
      cellStyleOption: {
        bodyCellClass: ({ row, column, rowIndex }) => {
          let statusarrtxt = [
            "fixedState",
            "ko",
            "m1",
            "m2",
            "m2t",
            "m3",
            "m3t",
            "m4",
            "m5",
            "m6",
            "sop",
          ];

          let statusarr = [
            "fixedState",
            "ko",
            "m1",
            "m2",
            "m2t",
            "m3",
            "m3t",
            "m4",
            "m5",
            "m6",
            "sop",
          ];
          
          let obj = {
            0: '',//"dangerdetail",
            1: '',//"warningdetail",
            2: "successdetail",
            3: "tbd",
            4: "processdetail",
          };
          let index = statusarrtxt.indexOf(column.key);
          if (index > -1) {
            
            if (statusarr[index] == 'm6' && row['state'] == 8) {
              return 'tbd';//"warningdetail";
            }


            if (row[statusarr[index]] == 4) {
              if(row['state'] == 7) {
                return 'tbd';
              }
              if (row["overDays"] < 0) {
                return obj[2];
              }
              if (row["overDays"] == 0 || row["overDays"] < 7) {
                return obj[1];
              }
              if (row["overDays"] >= 7) {
                return obj[0];
              }
            }
            if (row[statusarr[index]] == -1) {
              return obj[1];
            }
            if (statusarr[index] == 'fixedState' && row[statusarr[index]] == 1) {
              return '';//"warningdetail";
            }

            
            
            return obj[row[statusarr[index]]];
          }
        },
      },
      cellSpanOption: {
        bodyCellSpan: this.bodyCellSpan,
        footerCellSpan: this.footerCellSpan,
      },
      
      filter: {
        isMultiple: true,
        filterList: [
          {
            value: 1,
            label: "产品信息",
            selected:true
          },
          {
            value: 2,
            label: "产品开发进度",
            selected:true
          },
          {
            value: 3,
            label: "开发进展",
            selected:true
          },
          {
            value: 4,
            label: "产能规划",
            selected:true
          },
          {
            value: 5,
            label: "信息反馈",
            selected:true
          },
        ],
        filterConfirm: (filterList) => {
          let cols = []
          cols.push(this.col0)
          for (const item of filterList) {
            if (item.selected) {
              let _cols = this.filterCos(item.value)
              cols.push(_cols)
            }
          }
          const vals = filterList.filter((x) => x.selected).map((x) => x.value);
          this.colsselects = vals
          this.columns =cols
        },
        filterReset: (filterList) => {
          let cols = []
          cols.push(this.col0)
          cols.push(this.col1)
          cols.push(this.col2)
          cols.push(this.col3)
          cols.push(this.col4)
          cols.push(this.col5)
          this.columns = cols
          this.colsselects = [1,2,3,4,5]
        },
        beforeVisibleChange:({nextVisible})=>{
          this.$nextTick(() => {
            let items = this.getByClass(document, 've-dropdown-operation-item')
            items[0].innerText = '重置'
            items[1].innerText = '确认'
          })
        }
      },
    };
  },
  components: {
      clamp
  },
  methods: {
    getByClass(parent, cls) {
      if (parent.getElementsByClassName) {
        return Array.from(parent.getElementsByClassName(cls));
      } else {
        var res = [];
        var reg = new RegExp(' ' + cls + ' ', 'i')
        var ele = parent.getElementsByTagName('*');
        for (var i = 0; i < ele.length; i++) {
          if (reg.test(' ' + ele[i].className + ' ')) {
            res.push(ele[i]);
          }
        }
        return res;
      }
    },
    filterCos(val){
      if (val == 1) {
        return this.col1
      }
      if (val == 2) {
        return this.col2
      }
      if (val == 3) {
        return this.col3
      }
      if (val == 4) {
        return this.col4
      }
      if (val == 5) {
        return this.col5
      }
    },
    gotoIndex(){
      this.$router.go(-1)
    },
    tipwidth(){
      this.$nextTick(() => {
        var tips=document.getElementById('tips');
        tips.style.width=document.documentElement.clientWidth;
      })
    },

    renderHeaderCell({ column }) {
      return (<a-tooltip placement="bottom">
                <template slot="title">
                  <div>圆柱:直径*高</div>
                  <div>方形:宽*厚*高</div>
                </template>
                <span>{column.title}</span>
              </a-tooltip>);
    },
    getCharCount(str,char){
      let count=0;
      while(str.indexOf(char) != -1 ) {
          str = str.replace(char,"")//把已计数的替换为空
          count++;
      }
      return count;
    },
    callReportProductData() {
      this.show()
      getReportProductData(this.$route.query)
        .then((res) => {
          if (res.result) {
            let feedbackArr = ['size',"dischargeRate","processStr","performance","produceFeedback","sellFeedback","supplyFeedback"]
            if(res.data.columndata)
              for (var item of res.data.columndata) {

                if (item.field == 'ee') {
                  item.filter = this.filter
                }
              
                if (item.children) {
                  
                  for (var _item of item.children) {
                    _item.renderBodyCell = this.renderBodyCell;

                    if (_item.field == "productName") {
                      _item.renderFooterCell = this.renderFooterCell;
                    }

                    if (feedbackArr.indexOf(_item.field) < 0) {
                      _item.ellipsis = this.showTitle;
                    }

                    if (_item.field == 'size') {
                      _item.renderHeaderCell = this.renderHeaderCell
                    }
                    
                    if (_item.children) {

                      for (const $item of _item.children) {
                        $item.renderBodyCell = this.renderBodyCell;
                      }
                    }
                    
                  }
                }
              }
            if(res.data.merges)
              res.data.merges.forEach((item) => {
              for (let i = 0, j = res.data.rowdata.length; i < j; i++) {
                let rowSpan = 0;
                let n = i;
                while (
                  //res.data.rowdata[n + 1] != '' &&
                  res.data.rowdata[n + 1] &&
                  res.data.rowdata[n + 1][item] == res.data.rowdata[n][item]
                ) {
                  rowSpan++;
                  n++;
                  res.data.rowdata[n].rowSpan = 0;
                }
                if (rowSpan) res.data.rowdata[i][item + "_rowSpan"] = rowSpan + 1;

                if (!rowSpan) res.data.rowdata[i][item + "_rowSpan"] = 1;

                i += rowSpan;
              }
            });

            
            this.columns = res.data.columndata
            
            this.col5 = res.data.columndata[5]
            this.col4 = res.data.columndata[4]
            this.col3 = res.data.columndata[3]
            this.col2 = res.data.columndata[2]
            this.col1 = res.data.columndata[1]
            this.col0 = res.data.columndata[0]

            this.rows = res.data.rowdata;
            
            this.showDetail = true;
            this.merges = res.data.merges;
            this.exmerges = res.data.exmerges;
            this.scrollWidth = 2720

            //this.columnHiddenOption.defaultHiddenColumnKeys.push(['supplierCustomer','dept' ,'factory','ppm','productLine','productCap','sop','kpcs2022','kpcs2023','produceFeedback','sellFeedback','supplyFeedback'])
          } else {
            this.$message.error('错误提示：' + res.message,1)
          }
          this.close()
        })
        .catch((err) => {
          this.close()
          this.$message.error('错误提示：' + err.message,1)
        });
    },
    bodyCellSpan({ row, column, rowIndex }) {
      if (this.merges.includes(column.key)) {
        const _col = row[column.key + "_rowSpan"] > 0 ? 1 : 0;
        return {
          colspan: _col,
          rowspan: row[column.key + "_rowSpan"],
        };
      }
      if (!this.exmerges.includes(column.key)) {
    

        let $index = this.pmerge.includes(column.key) ? this.merges.length - 2 : this.merges.length - 1

        let dataindex = this.merges[$index];
        const _row = row[dataindex + "_rowSpan"];
        const _col = _row > 0 ? 1 : 0;

        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
    footerCellSpan({ row, column, rowIndex }) {
      if (column.field == "productName") {
        return {
          rowspan: 1,
          colspan: 20,//33
        };
      } else {
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    },

    renderBodyCell({ row, column, rowIndex }) {
      let statusarrtxt = [
        "ko",
        "m1",
        "m2",
        "m2t",
        "m3",
        "m3t",
        "m4",
        "m5",
        "m6",
      ];

      let statusarr = ["fixedState"]//["fixedState", "sop"];

      let index = statusarrtxt.indexOf(column.key);

      if (index > -1) {
        return (
          <span
            class="clickcellstyle"
            onClick={() => {
              this.handleToDetail(row, column,index);
            }}
          >
            {row[column.key + "Date"]}
          </span>
        );
      }
      if (statusarr.indexOf(column.key) > -1) {
        let isfixed = row[column.key]
        if (isfixed == 2) {
          return <span>已定点</span>;
        }else{
          return <span>{row['plannedFixedDate']}</span>;
        }
        
      }
      if ("productProjectName" == column.key) {
        return (
          <span
            class="clickcellstyle"
            onClick={() => {
              this.handleTo(row, column);
            }}
          >
            {row[column.key]}
          </span>
        );
      }

      if ("projectName" == column.key) {
        return (
          <span
            class="clickcellstyle"
            onClick={() => {
              this.handleToChart(row, column);
            }}
          >
            {row[column.key]}
          </span>
        );
      }

      let classArr = ["productName", "productCateName"];
      if (classArr.indexOf(column.key) > -1) {
        return <span class="vertical-lr">{row[column.key]}</span>;
      }
      let feedbackArr = ["performance","produceFeedback","sellFeedback","supplyFeedback"]
      if (feedbackArr.indexOf(column.key) > -1) {
        //return <div class='feedback'>{row[column.key]}</div>
        return <clamp text={row[column.key]} sourceText={[row[column.key]]}></clamp>
      }
      let rateArr = ["dischargeRate"]
      if (rateArr.indexOf(column.key) > -1) {
        return <span>{row[column.key]}</span>
      }
      let processArr = ["processStr"]
      if (processArr.indexOf(column.key)>-1) {
        let strArr = row[column.key].split('#')
        let str = strArr.join('\n')
        //return <OtherComp arr = {strArr} />
        return <clamp text={str} sourceText={strArr}></clamp>
        
      }

      let sizeArr = ['size']
      if (sizeArr.indexOf(column.key) > -1) {

        let count = this.getCharCount(row[column.key],'*')
        let txt = count > 1 ? '方形:宽*厚*高' : ( count  == 0 ? '' :'圆柱:直径*高')
        return txt ? <a-tooltip placement="top">
                <template slot="title">
                  <span>{txt}</span>
                </template>
                <span>{row[column.key]}</span>
              </a-tooltip> : <div>{row[column.key]}</div>
      }
      return row[column.key];
    },

    /* <span>
              <label>待完成</label>
              <label class="yellow"></label>
            </span>
            <span>
              <label>逾期大于7天</label>
              <label class="danger"></label>
            </span> */
    renderFooterCell({ row, column, rowIndex }) {
      return (
        <div id="tips" class="tips" style="justify-content: center;height:30px;align-items:center;background:#fff;">
            
            <span>
              <label>已完成</label>
              <label class="green"></label>
            </span>
            
            <span>
              <label>暂停</label>
              <label class="brown"></label>
            </span>
          </div>
      );
    },
    handleToDetail(row, column,index) {
      if (row[column.key + "Date"] == '--') {
        return false;
      }
      this.switchApp()
      this.$router.push({
        path: "/stage",
        query: {
          issueId: row["issueId"],
          productProjectName: row["productProjectName"],
          date:row[column.key + "Date"],
          stage:index+1
        },
      });
    },
    handleToChart(row,column){
      this.switchApp()
      this.$router.push({
        path: "/project_chart",
        query: {
           title: row[column.key],
          issueId: row["issueId"],
          issueKey: row["issueKey"].replace(row["cateId"],""),
          cateId:row['cateId'],
          parentTitle:row['productProjectName'],
          mstatus:row['mstatus']
        },
      });
    },
    handleTo(row, column) {
      this.switchApp()
      this.$router.push({
        path: "/docs",
        query: {
          title: row[column.key],
          issueId: row["issueId"],
          issueKey: row["issueKey"].replace(row["cateId"],""),
          cateId:row['cateId'],
        },
      });
    },
    switchApp() {
        /* const apps = Vue.ls.get(ALL_APPS_MENU)
                const _newApps = []
                for (const item of apps) {
                    
                    _newApps.push(item)
                }
                Vue.ls.set(ALL_APPS_MENU, _newApps) */
    },
    show() {
      this.loadingInstance.show();
    },
    close() {
      this.loadingInstance.close();
    },
    ...mapActions(['MenuChange']),
    hideColumns(keys) {
      this.$refs['tableRef'].hideColumnsByKeys(keys);
    },
  },
  /* watch:{
    columns: {
      handler: function (val, oldVal) { 
        this.columns = val
      },
      deep: true,
      immediate: true
    }
  }, */
  computed: {
    ...mapGetters(['userInfo'])
  },
  watch: {
    colsselects(val){
      for (const item of this.filter.filterList) {
        if (val.indexOf(item.value) > -1) {
          item.selected = true
        }else{
          item.selected = false
        }
      }
    }
  },
  /* mounted() {
     this.$nextTick(() => {
         let items = this.getByClass(document, 've-dropdown-operation-item')
         items[0].innerText = '重置'
         items[1].innerText = '确认'
      })
  }, */
  created() {
    this.loadingInstance = this.$veLoading({
      target: document.querySelector("#loading-detail"),
      name: "flow",
    });
    this.cateTitle=this.$route.query.cateName
    this.callReportProductData()
    
  },
  destroyed() {
    this.loadingInstance.destroy();
  },
};
</script>

<style lang="less">
@import './vetable.less';
.infodetail {
  background: #e9e9eb !important;
  color: #909399;
}
.warningdetail {
  background: #fff5ad !important;
  color: #000;
}
.successdetail {
  background: #b4f7ac !important;
  color: #000;
}
.processdetail {
  background: #eaf5e4 !important;
  color: #000;
}
.dangerdetail {
  background: #fc8585 !important;
  color: #000;
}
.tbd{
  background: #cacaca !important;
  color: #000;
}
.head {
  border: 1px solid rgb(219, 219, 219);
  border-bottom: none;
  background: #5c81cb;
  text-align: center;
  padding: 10px 0;
  color: #fff;
  font-size: 20px;
}

.vertical-lr{
  display: block;
  writing-mode: vertical-lr;
  margin: auto;
  letter-spacing: 5px;
}

.feedback{
  padding-left:2px;
  text-align: left;
  word-break: break-all;
}

/* .other-comp div{
  vertical-align: baseline;
  padding: 0 5px;
} */
.flex_1 {
  display: flex;
}
.flex_2{
  flex: 1;
}
.flex_3{
  margin-right: 8px;
}
.breadcrumb{
  padding: 5px 0;
  padding-left: 13px;
}.ant-breadcrumb a{
  color:#5d90fa !important;
}.ant-breadcrumb{
  font-size: 12px !important;
}
</style>