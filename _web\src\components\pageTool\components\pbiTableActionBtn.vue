<template>
  <div>
    <template v-for="btnItem in params.btnList" overlayClassName="pbi-table-btn-tooltip">
      <!-- 有提示语 -->
      <a-tooltip v-if="btnItem.btnTooltip">
        <template slot="title">
          {{btnItem.btnTooltip}}
        </template>
        <a-button type="link" @click="params.handleClickBtn(btnItem.btnType,params.data)"><a-icon v-if="btnItem.btnIcon"
            :type="btnItem.btnIcon" /> {{btnItem.btnName || ''}}</a-button>
      </a-tooltip>

      <!-- 纯按钮 -->
      <a-button v-else type="link" @click="params.handleClickBtn(btnItem.btnType,params.data)"><a-icon v-if="btnItem.btnIcon"
          :type="btnItem.btnIcon" /> {{btnItem.btnName || ''}}</a-button>

    </template>

  </div>

</template>

<style lang="less" scoped>
  /deep/ .ant-btn {
    font-size: 12px;
    padding: 0 8px;
  }

  /deep/ .ant-btn i {
    vertical-align: inherit;
  }

  /deep/.ant-btn>.anticon+span,
  /deep/.ant-btn>span+.anticon {
    margin-left: 4px;
  }
</style>

<!-- 为什么写全局样式：因为ant-tooltip 是在<div id='app'>外面,<body>里面 -->

<style>
  .pbi-table-btn-tooltip .ant-tooltip-content .ant-tooltip-inner {
    color: #333;
    background-color: #fff;
    font-size: 12px;
  }

  .pbi-table-btn-tooltip .ant-tooltip-content .ant-tooltip-arrow::before {
    opacity: 0;
  }
</style>