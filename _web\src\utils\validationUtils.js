/**
 * 验证工具函数
 * 提供通用的验证方法，减少组件中的重复代码
 */
import { showWarning } from './errorUtils';

/**
 * 验证文件类型是否为Excel
 * @param {File} file - 文件对象
 * @returns {boolean} 是否为Excel文件
 */
export function isExcelFile(file) {
  const acceptedTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel'
  ];
  
  if (!acceptedTypes.includes(file.type)) {
    showWarning('请上传Excel文件 (.xlsx, .xls)');
    return false;
  }
  
  return true;
}

/**
 * 验证文件大小是否在限制范围内
 * @param {File} file - 文件对象
 * @param {number} maxSize - 最大文件大小（MB）
 * @returns {boolean} 文件大小是否合法
 */
export function isValidFileSize(file, maxSize = 10) {
  const fileSize = file.size / 1024 / 1024; // 转换为MB
  
  if (fileSize > maxSize) {
    showWarning(`文件大小不能超过${maxSize}MB!`);
    return false;
  }
  
  return true;
}

/**
 * 验证文件是否合法（类型和大小）
 * @param {File} file - 文件对象
 * @param {number} maxSize - 最大文件大小（MB）
 * @returns {boolean} 文件是否合法
 */
export function validateFile(file, maxSize = 10) {
  return isExcelFile(file) && isValidFileSize(file, maxSize);
}

/**
 * 验证公式是否包含等号
 * @param {string} formula - 公式字符串
 * @returns {boolean} 是否包含等号
 */
export function hasEquals(formula) {
  if (!formula || !formula.includes('=')) {
    showWarning('公式必须包含等号');
    return false;
  }
  
  return true;
}

/**
 * 验证公式是否为空
 * @param {string} formula - 公式字符串
 * @returns {boolean} 公式是否非空
 */
export function isFormulaNotEmpty(formula) {
  if (!formula || formula.trim() === '') {
    showWarning('请输入公式');
    return false;
  }
  
  return true;
}

/**
 * 验证参数列表是否非空
 * @param {Array} params - 参数数组
 * @returns {boolean} 参数列表是否非空
 */
export function hasValidParams(params) {
  if (!params || !Array.isArray(params) || params.length === 0) {
    showWarning('请至少添加一个参数');
    return false;
  }
  
  // 过滤掉空参数
  const validParams = params.filter(param => param && param.trim && param.trim() !== '');
  
  if (validParams.length === 0) {
    showWarning('请至少添加一个有效参数');
    return false;
  }
  
  return true;
}

/**
 * 验证ID是否有效
 * @param {string} id - ID字符串
 * @returns {boolean} ID是否有效
 */
export function isValidId(id) {
  if (!id || id.trim() === '') {
    showWarning('ID不能为空');
    return false;
  }
  
  return true;
}

/**
 * 验证数值是否在指定范围内
 * @param {number} value - 数值
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {boolean} 数值是否在范围内
 */
export function isInRange(value, min, max) {
  if (value < min || value > max) {
    showWarning(`值必须在${min}和${max}之间`);
    return false;
  }
  
  return true;
}

/**
 * 验证对象是否有所有必需的属性
 * @param {Object} obj - 对象
 * @param {Array} requiredProps - 必需属性数组
 * @returns {boolean} 是否有所有必需属性
 */
export function hasRequiredProps(obj, requiredProps) {
  if (!obj || typeof obj !== 'object') {
    return false;
  }
  
  for (const prop of requiredProps) {
    if (obj[prop] === undefined || obj[prop] === null) {
      showWarning(`缺少必需的属性: ${prop}`);
      return false;
    }
  }
  
  return true;
}
