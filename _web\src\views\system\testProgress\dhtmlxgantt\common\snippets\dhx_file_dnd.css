.gantt-file-hover{
	position: absolute;
	width: 100%;
	height: 100%;
	background: rgba(184, 227, 255, 0.65);
	/*background: rgba(70, 173, 81, 0.26);*/
	top: 0;
	left: 0;
	box-sizing: border-box;
	-moz-box-sizing: border-box;
	padding:33px;
	pointer-events:none;
}

.gantt-file-hover.not-supported{
	background: rgba(169, 169, 169, 0.65);
	color:#6b6b6b;
}

.gantt-file-hover-inner{
	border: 3px dashed #929292;
	height: 100%;
	text-align: center;
}
.gantt-file-hover-content-pending{
	position: absolute;
	top: 50%;
	left: 50%;
	opacity: 0.8;
	text-align: center;
	transform: translateX(-50%) translateY(-50%);
}

.gantt-file-hover-content-upload-message{
	font-size: 2em;
	font-family: arial;
	padding-left: 25px;
}

.gantt-file-upload-spinner{
	width:256px;
	height:64px;
	position: relative;
}
/* spinner */
.gantt-file-upload-spinner-inner:before,
.gantt-file-upload-spinner-inner:after,
.gantt-file-upload-spinner-inner {
	border-radius: 50%;
	width: 2.5em;
	height: 2.5em;
	-webkit-animation-fill-mode: both;
	animation-fill-mode: both;
	-webkit-animation: gantt-upload-spinner 1.8s infinite ease-in-out;
	animation: gantt-upload-spinner 1.8s infinite ease-in-out;
}
.gantt-file-upload-spinner-inner {
	color: rgba(70, 173, 81, 0.36);
	font-size: 10px;
	margin: 0 auto;
	position: relative;
	text-indent: -9999em;
	-webkit-transform: translateZ(0);
	-ms-transform: translateZ(0);
	transform: translateZ(0);
	-webkit-animation-delay: -0.16s;
	animation-delay: -0.16s;
}
.gantt-file-upload-spinner-inner:before {
	left: -3.5em;
	-webkit-animation-delay: -0.32s;
	animation-delay: -0.32s;
}
.gantt-file-upload-spinner-inner:after {
	left: 3.5em;
}
.gantt-file-upload-spinner-inner:before,
.gantt-file-upload-spinner-inner:after {
	content: '';
	position: absolute;
	top: 0;
}


@-webkit-keyframes gantt-upload-spinner {
	0%,
	80%,
	100% {
		box-shadow: 0 2.5em 0 -1.3em;
	}
	40% {
		box-shadow: 0 2.5em 0 0;
	}
}
@keyframes gantt-upload-spinner {
	0%,
	80%,
	100% {
		box-shadow: 0 2.5em 0 -1.3em;
	}
	40% {
		box-shadow: 0 2.5em 0 0;
	}
}