<template>
	<div class="container">
		<div class="head_title">产品管理平台</div>
		<div class="main">
			<div class="left">
				<div class="top">
					<div class="item">
						<div class="head">
							<div class="left"><a-icon type="pie-chart" :style="iconStyle" /><strong>产品分类</strong></div>
						</div>
						<div class="head_button hand" @click="handleAll(0)">
							<strong class="tips">信息对齐表 ></strong>
						</div>
						<!-- :style="{ height: clientHeight / 2 - 100 + 'px' }"  -->
						<a-spin :spinning="statusLoading">
							<div class="chart_table" :style="{ height: (clientHeight - 191) / 2 + 'px' }" ref="status"></div>
						</a-spin>
					</div>
					<div class="item">
						<div class="head">
							<div class="left"><a-icon type="pie-chart" :style="iconStyle" /><strong>产品等级</strong></div>
						</div>
						<a-spin :spinning="levelLoading">
							<div class="chart_table" :style="{ height: (clientHeight - 191) / 2 + 'px' }" ref="level"></div>
						</a-spin>
					</div>
					<div class="item">
						<div class="head">
							<div class="left"><a-icon type="radar-chart" :style="iconStyle" /><strong>项目费用</strong></div>
						</div>
						<a-spin :spinning="loading">
							<div class="chart_table" :style="{ height: (clientHeight - 191) / 2 + 'px' }" ref="charge"></div>
						</a-spin>
					</div>
				</div>
				<div class="bottom">
					<div class="item">
						<div class="head">
							<div class="left"><a-icon type="bar-chart" :style="iconStyle" /><strong>项目进度</strong></div>
						</div>
						<div class="head_button hand" @click="handleAll(1)">
							<strong class="tips">查看所有 ></strong>
						</div>
						<a-spin :spinning="processLoading">
							<div class="chart_table" :style="{ height: (clientHeight - 191) / 2 + 'px' }" ref="process"></div>
						</a-spin>
					</div>
					<div class="item">
						<div class="head">
							<div class="left"><a-icon type="bar-chart" :style="iconStyle" /><strong>设计变更</strong></div>
						</div>
						<a-spin :spinning="alterLoading">
							<div class="chart_table" :style="{ height: (clientHeight - 191) / 2 + 'px' }" ref="alter"></div>
						</a-spin>
					</div>
				</div>
			</div>

			<div class="right">
				<div class="top">
					<div class="item">
						<div class="head">
							<div class="left"><a-icon type="heat-map" :style="iconStyle" /><strong>动态监控</strong></div>
						</div>
						<a-spin :spinning="loading">
							<div class="chart_table monitor" :style="{ height: (clientHeight - 218) * 0.18 + 'px' }" ref="monitor">
								<div class="row">
									<span class="circle"></span>红灯问题 ({{ lampData.statusRedLampCount }})
									<span class="blue hand ml8" @click="handleQuery(1)" v-if="lampData.statusRedLampCount > 0">
										请关注</span
									>
								</div>
								<div class="row">
									<span class="circle "></span>黄灯问题 ({{ lampData.statusYellowLampCount }})
									<span class="blue hand ml8" @click="handleQuery(2)" v-if="lampData.statusYellowLampCount > 0">
										请关注</span
									>
								</div>
								<!-- <div class="row">
									<span class="circle mr4"></span>新增立项产品项目 (3) <span class="blue hand"> 请关注</span>
								</div>
								<div class="row">
									<span class="circle mr4"></span>完成B转C阶段产品 (2) <span class="blue hand"> 请关注</span>
								</div> -->
							</div>
						</a-spin>
					</div>
				</div>
				<div class="bottom">
					<div class="item">
						<div class="head">
							<div class="left"><a-icon type="bar-chart" :style="iconStyle" /><strong>研发质量</strong></div>
						</div>
						<a-spin :spinning="qualityloading">
							<div
								class="chart_table"
								style="margin-top: 36px;"
								ref="quality"
								:style="{ height: (clientHeight - 218) * 0.82 + 'px' }"
							></div>
						</a-spin>
					</div>
				</div>
			</div>
		</div>
		<div>
			<ProcessModel :title="modeltitle" v-if="isProcessModelShow" @cancel="handelModelCancel" />
			<AltersModel :title="modeltitle" v-if="isAltersModelShow" @cancel="handelModelCancel" />
		</div>
	</div>
</template>

<script>
import {
	getChartProductClassification,
	getChartProjectLevels,
	getProjectProcess,
	getProjectAlters,
	getDashStage,
	getProjectStageCount
} from "@/api/modular/system/chartManage"

import ProcessModel from "./model/processModel.vue"
import AltersModel from "./model/altersModel.vue"
export default {
	data() {
		return {
			loading: false,
			qualityloading: false,
			statusLoading: false,
			levelLoading: false,
			processLoading: false,
			alterLoading: false,
			clientHeight: document.body.clientHeight,
			iconStyle: "font-size: 18px; color: #1890FF;margin-right: 8px;",
			isProcessModelShow: false,
			isAltersModelShow: false,
			modeltitle: "",
			lampData: {},
			qualityData: {}
		}
	},
	components: {
		ProcessModel,
		AltersModel
	},
	methods: {
		/**
		 * echarts 事件
		 */
		// 产品状态
		initProductStatus() {
			let chart = this.echarts.init(this.$refs.status, null, { renderer: "svg" })
			chart.off("click")
			let datas = this.productStatus
			let sum = datas.reduce((sum, e) => sum + Number(e.value || 0), 0)
			chart.clear()
			let that = this
			const options = {
				tooltip: {
					trigger: "item"
				},
				legend: {
					type: "scroll", // 图例滚动
					show: true,
					itemWidth: 8,
					itemHeight: 8,
					y: "bottom",
					x: "center",
					textStyle: {
						fontSize: 10
					}
				},
				color: ["#ed7d31", "#5b9bd5", "#4472c4", "#f6d530", "#a5a5a5"],
				grid: {},
				series: [
					/* {
						type: "pie",
						radius: ["55%", "80%"],
						center: ["50%", "45%"],
						avoidLabelOverlap: false,
						itemStyle: {
							borderRadius: 5,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: true,
							position: "center",
							color: "#000",
							formatter: `产品总数\n${sum}`,
							fontSize: "15",
							lineHeight: 30,
							fontWeight: "bold"
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					}, */
					{
						type: "pie",
						radius: ["55%", "80%"],
						center: ["50%", "45%"],
						avoidLabelOverlap: false,
						itemStyle: {
							borderRadius: 5,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: false
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					}
				],
				graphic: {
					// 添加原生图形元素组件
					elements: [
						{
							type: "text", // 组件类型
							left: "center", //定位
							top: "38%", // 定位
							style: {
								// 样式
								text: "产品总数", //文字
								fontSize: 15, //文字大小
								textAlign: "center", //定位
								fontWeight: "bold",
								width: 30,
								height: 25,
								fill: "dark" // 字体颜色
							}
						},
						{
							type: "text",
							left: "center",
							top: "50%",
							style: {
								text: `${sum}`,
								fontSize: 15,
								textAlign: "center",
								fontWeight: "bold",
								width: 30,
								height: 25,
								fill: "dark"
							}
						}
					]
				}
			}
			chart.setOption(options)
			chart.on("click", function(params) {
				if (params.componentType == "graphic") {
					that.$router.push({
						path: "/report_sum" ///project_gain
					})
				}
				/* that.$router.push({
					path: "/report_sum" ///project_gain
				}) */
			})
			chart.resize()
		},
		// 产品等级
		initProductLevel() {
			let chart = this.echarts.init(this.$refs.level, null, { renderer: "svg" })
			chart.off("click")
			let datas = this.productsLevels
			let sum = datas.reduce((sum, e) => sum + Number(e.value || 0), 0)

			chart.clear()
			let that = this
			const options = {
				tooltip: {
					trigger: "item"
				},
				legend: {
					show: true,
					itemWidth: 8,
					itemHeight: 8,
					y: "bottom",
					x: "center",
					textStyle: {
						fontSize: 10
					}
				},
				color: ["#ed7d31", "#5b9bd5", "#4472c4", "#f6d530"],
				grid: {},
				series: [
					{
						type: "pie",
						//minAngle:10,
						radius: ["55%", "80%"],
						center: ["50%", "45%"],
						itemStyle: {
							borderRadius: 5,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: false
							/* position: "center",
							color: "#000",
							formatter: `产品等级`, //\n${sum}
							fontSize: "15",
							lineHeight: 30,
							fontWeight: "bold" */
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					}
				],
				graphic: {
					// 添加原生图形元素组件
					elements: [
						{
							type: "text", // 组件类型
							left: "center", //定位
							top: "42%", // 定位
							style: {
								// 样式
								text: "产品等级", //文字
								fontSize: 15, //文字大小
								textAlign: "center", //定位
								fontWeight: "bold",
								width: 30,
								height: 25,
								fill: "dark" // 字体颜色
							}
						}
					]
				}
			}
			chart.setOption(options)
			// chart.on('click', function(params) {
			//     that.$router.push({
			//         path:'/product_dashboard',///project_gain
			//     })
			// });
			chart.resize()
		},
		// 项目费用
		initProductCharge() {
			let chart = this.echarts.init(this.$refs.charge, null, { renderer: "svg" })
			chart.off("click")
			chart.clear()
			let options = {
				color: ["#9bdfe9", "#c4ebad"],
				legend: {
					show: true,
					itemWidth: 8,
					itemHeight: 8,
					y: "bottom",
					x: "center",
					textStyle: {
						fontSize: 10
					}
				},
				radar: {
					nameGap: 5,
					startAngle: 90,
					// radius: 80,
					center: ["50%", "50%"],
					axisName: {
						color: "#000",
						padding: [1, 1],
						fontSize: 10,
						lineHeight: 6,
						height: 6
					},
					indicator: [
						{ text: "项目管理费用" },
						{ text: "其他费用" },
						{ text: "样品费用" },
						{ text: "检测费用" },
						{ text: "硬件开发" }
					]
				},
				series: [
					{
						name: "Budget vs spending",
						type: "radar",
						data: [
							{
								value: [4200, 3000, 20000, 35000, 50000, 18000],
								name: "实际值",
								areaStyle: {
									color: "#9ee5e5"
								},
								symbol: "none",
								lineStyle: {
									width: 2,
									opacity: 0.5,
									color: "#9ee5e5"
								}
							},
							{
								value: [5000, 14000, 28000, 26000, 42000, 21000],
								name: "预算值",
								areaStyle: {
									color: "#77a7e5"
								},
								symbol: "none",
								lineStyle: {
									width: 2,
									opacity: 0.5,
									color: "#77a7e5"
								}
							}
						]
					}
				]
			}
			chart.setOption(options)
			chart.resize()
		},
		// 项目进度
		initProcess() {
			let that = this
			let chart = that.echarts.init(that.$refs.process, null, { renderer: "svg" })
			chart.off("click")
			chart.clear()

			let dataSum = [0, 0, 0, 0, 0]
			for (const k in that.process) {
				for (let i = 0, j = this.process[k].length; i < j; i++) {
					dataSum[i] = dataSum[i] + this.process[k][i]
				}
			}

			let dataRatio = []

			for (let i = 0, j = this.process["1"].length; i < j; i++) {
				dataRatio.push(dataSum[i] == 0 ? 0 : ((this.process["1"][i] / dataSum[i]) * 100).toFixed(1))
			}

			const options = {
				color: ["#5b9bd5", "#ed7d31", "#a5a5a5"],

				tooltip: {
					trigger: "axis",
					axisPointer: {
						type: "shadow"
					}
				},
				legend: {
					show: true,
					itemWidth: 8,
					itemHeight: 8,
					y: "bottom",
					x: "center",
					textStyle: {
						fontSize: 10
					}
				},
				grid: {
					left: 0,
					right: 0,
					bottom: "15%",
					top: "10%",
					show: false,
					containLabel: true
				},
				xAxis: {
					type: "category",
					data: this.processAxis,
					axisLabel: {
						interval: 0,
						textStyle: {
							color: "#000",
							fontSize: "10"
						}
					}
				},
				yAxis: [
					{
						show: false,
						type: "value",
						axisLabel: {
							textStyle: {
								color: "#000",
								fontSize: "10"
							}
						}
					},
					{
						type: "value",
						show: false,
						axisLabel: {
							formatter: (value, index) => {
								return value + "%"
							},
							textStyle: {
								color: "#000",
								fontSize: "10"
							}
						}
					}
				],
				tooltip: {
					formatter: (params, ticket, callback) => {
						return (
							params.marker +
							params.name +
							":" +
							params.seriesName +
							(params.componentSubType == "line" ? params.value + "%" : params.value)
						)
					}
				},
				series: [
					{
						name: "正常",
						type: "bar",
						stack: "total",
						barWidth: 40,
						emphasis: {
							focus: "series"
						},
						barMaxWidth: "40%",
						data: this.process["1"]
					},
					{
						name: "延期",
						type: "bar",
						stack: "total",
						barWidth: 40,
						emphasis: {
							focus: "series"
						},
						barMaxWidth: "40%",
						data: this.process["2"]
					},
					{
						name: "停止",
						type: "bar",
						stack: "total",
						barWidth: 40,
						emphasis: {
							focus: "series"
						},
						barMaxWidth: "40%",
						data: this.process["3"]
					},
					{
						name: "进度正常率",
						type: "line",
						data: dataRatio,
						yAxisIndex: 1,
						itemStyle: {
							normal: {
								color: "#91cc75",
								label: {
									show: true,
									position: "top",
									formatter: (value, index) => {
										return value.data + "%"
									},
									textStyle: {
										color: "#000",
										fontSize: "8"
									}
								}
							}
						}
					}
				]
			}
			chart.setOption(options)
			chart.on("click", params => {
				// this.isProcessModelShow = true
				// this.modeltitle = params.name
				// return
				return this.$router.push({
					path: "/project_process",
					query: {
						dept: params.name,
						projectStatu: params.seriesName
					}
				})
			})
			chart.resize()
		},
		initAlter() {
			let that = this
			let chart = that.echarts.init(that.$refs.alter, null, { renderer: "svg" })
			chart.off("click")
			chart.clear()
			let axis = this.alterAxis //['动力电池研究所','储能电池研究所','方形电池研究所','动力圆柱电池研究所','新型电池研究所']
			let dataBom = []
			let dataMi = []
			let dataMap = []

			for (const item of axis) {
				dataBom.push(that.bomAlter[item] ? that.bomAlter[item] : 0)
				dataMi.push(that.mIAlter[item] ? that.mIAlter[item] : 0)
				dataMap.push(that.mapAlter[item] ? that.mapAlter[item] : 0)
			}

			const options = {
				color: ["#5b9bd5", "#ed7d31", "#a5a5a5"],
				tooltip: {
					trigger: "axis",
					axisPointer: {
						type: "shadow"
					}
				},
				legend: {
					show: true,
					itemWidth: 8,
					itemHeight: 8,
					y: "bottom",
					x: "center",
					textStyle: {
						fontSize: 10
					}
				},
				grid: {
					left: "3%",
					right: "3%",
					bottom: "15%",
					top: "10%",
					show: false,
					containLabel: true
				},
				xAxis: {
					type: "category",
					data: axis,
					axisLabel: {
						interval: 0,
						textStyle: {
							color: "#000",
							fontSize: "10"
						}
					}
				},
				yAxis: {
					show: false,
					type: "value"
				},
				series: [
					{
						name: "BOM",
						type: "bar",
						stack: "total",
						barWidth: 40,
						emphasis: {
							focus: "series"
						},
						barMaxWidth: "40%",
						data: dataBom
					},
					{
						name: "MI",
						type: "bar",
						stack: "total",
						barWidth: 40,
						emphasis: {
							focus: "series"
						},
						barMaxWidth: "40%",
						data: dataMi
					},
					{
						name: "图纸",
						type: "bar",
						stack: "total",
						barWidth: 40,
						emphasis: {
							focus: "series"
						},
						barMaxWidth: "40%",
						data: dataMap
					}
				]
			}

			chart.setOption(options)
			// chart.on("click", function(params) {
			// 	that.$router.push({
			// 		path: "/project_alter",
			// 		query: {
			// 			dept: params.name
			// 		}
			// 	})
			// 	return
			// })
			chart.on("click", params => {
				return this.$router.push({
					path: "/project_alter",
					query: {
						dept: params.name
					}
				})
				// this.isAltersModelShow = true
				// this.modeltitle = params.name
				// return
			})
			chart.resize()
		},
		initQuality() {
			let that = this
			let chart = this.echarts.init(this.$refs.quality, null, { renderer: "svg" })
			let qualityData = this.qualityData
			chart.off("click")
			chart.clear()
			let options = {
				color: ["#5b9bd5", "#9bbb59", "#ed7d31", "#a5a5a5", "#ffc000"],
				tooltip: {
					trigger: "axis",
					axisPointer: {
						type: "shadow"
					}
				},
				legend: {
					show: true,
					type: "scroll", // 图例滚动
					itemWidth: 8,
					itemHeight: 8,
					y: "bottom",
					x: "center",
					textStyle: {
						fontSize: 10
					}
				},
				grid: {
					bottom: "10%",
					top: "3%",
					right: "10%",
					left: "1%",
					containLabel: true,
					show: false
				},
				xAxis: {
					show: false,
					type: "value",
					boundaryGap: [0, 0.01]
				},
				yAxis: {
					type: "category",
					data: ["转阶段文件达成率", "风险关闭率", "问题关闭率", "制样合格率"],
					axisLabel: {
						interval: 0,
						textStyle: {
							color: "#000",
							fontSize: "10"
						}
					}
				},
				series: [
					/* {
						name: "方形电池所",
						type: "bar",
						barWidth: "10%",
						data: [40, 70, 68, 30],
						itemStyle: {
							barBorderRadius: [0, 8, 8, 0]
						}
					},
					{
						name: "动力圆柱所",
						type: "bar",
						barWidth: "10%",
						data: [24, 54, 57, 80],
						itemStyle: {
							barBorderRadius: [0, 8, 8, 0]
						}
					},
					{
						name: "新型电池所",
						type: "bar",
						barWidth: "10%",
						data: [33, 52, 89, 80],
						itemStyle: {
							barBorderRadius: [0, 8, 8, 0]
						}
					},
					{
						name: "动力电池所",
						type: "bar",
						barWidth: "10%",
						data: [21, 45, 36, 45],
						itemStyle: {
							barBorderRadius: [0, 8, 8, 0]
						}
					},
					{
						name: "储能电池所",
						type: "bar",
						barWidth: "10%",
						data: [21, 45, 36, 45],
						itemStyle: {
							barBorderRadius: [0, 8, 8, 0]
						}
					} */
				]
			}
			for (const k in qualityData) {
				options.series.push(
					{
						name: k,
						type: "bar",
						barWidth: "10%",
						data: [(qualityData[k]*100).toFixed(2),0,0,0],
						itemStyle: {
							barBorderRadius: [0, 8, 8, 0]
						}
					}
				)
			}
			chart.setOption(options)
			chart.on("click", params => {
				if (params.dataIndex == 0) {
					that.$router.push({
						path: "/project_stagedoc"
					})
				}
			})
			chart.resize()
		},
		initMenus() {
			this.initProductStatus()
			this.initProductLevel()
		},

		/**
		 * 获取数据
		 */
		callChartProductClassification() {
			this.statusLoading = true
			getChartProductClassification({})
				.then(res => {
					if (res.result) {
						this.productStatus = res.data.list ? res.data.list : []
						this.$nextTick(() => {
							this.initProductStatus()
						})
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
				})
				.catch(err => {
					this.$message.error("错误提示：" + err.message, 1)
				})
				.finally(() => {
					this.statusLoading = false
				})
		},

		callChartProjectLevels() {
			this.levelLoading = true
			getChartProjectLevels({})
				.then(res => {
					if (res.result) {
						this.productsLevels = res.data.list ? res.data.list : []
						this.$nextTick(() => {
							this.initProductLevel()
						})
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
				})
				.catch(err => {
					this.$message.error("错误提示：" + err.message, 1)
				})
				.finally(() => {
					this.levelLoading = false
				})
		},

		callChartProjectProcess() {
			this.processLoading = true
			getProjectProcess({})
				.then(res => {
					if (res.result) {
						let process = res.data.list ? res.data.list : {}
						for (const i in process) {
							for (let $i = 0, j = process[i].length; $i < j; $i++) {
								process[i][$i] = process[i][$i] == "0" ? null : parseInt(process[i][$i])
							}
						}
						this.process = process
						this.processAxis = res.data.axis ? res.data.axis : []
						this.$nextTick(() => {
							this.initProcess()
						})
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
				})
				.catch(err => {
					this.$message.error("错误提示：" + err.message, 1)
				})
				.finally(() => {
					this.processLoading = false
				})
		},

		callChartProjectAlter() {
			this.alterLoading = true
			getProjectAlters({})
				.then(res => {
					if (res.result) {
						this.bomAlter = res.data.BOMCOUNT ? res.data.BOMCOUNT : {}
						this.mIAlter = res.data.MICOUNT ? res.data.MICOUNT : {}
						this.mapAlter = res.data.MAPCOUNT ? res.data.MAPCOUNT : {}
						this.alterAxis = res.data.axis ? res.data.axis : []
						this.$nextTick(() => {
							this.initAlter()
						})
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
				})
				.catch(err => {
					this.$message.error("错误提示：" + err.message, 1)
				})
				.finally(() => {
					this.alterLoading = false
				})
		},
		getDashStage() {
			getDashStage().then(res => {
				if (!res.result) return this.$message.error("错误提示：" + res.message, 1)

				this.lampData = JSON.parse(JSON.stringify(res.data))
			})
		},

		getProjectStageCount() {
			this.qualityloading = true
			getProjectStageCount().then(res => {
				if (!res.result) return this.$message.error("错误提示：" + res.message, 1)

				this.qualityData = res.data
				this.$nextTick(() => {
					this.initQuality()
				})
				this.qualityloading = false
			})
		},

		// 查看所有事件（院）
		handleAll(index) {
			if (index) return this.$router.push({ path: "/project_process" })
			this.$router.push({ path: "/report_sum" })
		},
		// 弹窗事件
		handelModelCancel() {
			this.isProcessModelShow = false
			this.isAltersModelShow = false
		},
		// 处理问题
		handleQuery(statusLamp) {
			this.$router.push({
				path: "/monitor",
				query: {
					statusLamp
				}
			})
		}
	},
	beforeDestroy() {
		//window.removeEventListener('resize',this.initMenus)
	},
	mounted() {
		this.callChartProductClassification()
		this.callChartProjectLevels()
		this.initProductCharge()
		this.callChartProjectProcess()
		this.callChartProjectAlter()
		this.getProjectStageCount()

		this.getDashStage()
	}
}
</script>

<style lang="less" scoped>
.container {
	padding-top: 3px;
}
.main {
	display: flex;
	flex-wrap: nowrap;
	height: 100%;
}
.left {
	flex: 1;
	height: 100%;
	// margin-top: 8px;
	margin-left: 5px;
}
.left .item {
	flex: 1;
	padding: 8px;
	margin-right: 16px;
	background: #fff;
	border-radius: 10px;
	box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
	position: relative;
}
.left .top,
.left .bottom {
	display: flex;
	flex-wrap: nowrap;
}
.left .bottom .item {
	margin-top: 16px;
}
.left .chart_table {
	min-height: 200px;
	margin-top: 20px;
	display: flex;
	justify-content: center;
	align-items: center;
}
.right {
	width: 25%;
	height: 100%;
}
.right .item {
	position: relative;
	background: #fff;
	border-radius: 10px;
	padding: 8px;
}
.right .bottom .item {
	flex: 1;
	margin-top: 16px;
}
.head {
	position: absolute;
	top: 12px;
	left: 12px;
	color: #333;
	display: flex;
	align-items: center;
	justify-content: space-between;
	font-weight: 500;
}
.head_button {
	position: absolute;
	top: 14px;
	right: 12px;
	line-height: 1;
}
.right_arrow {
	font-size: 12px;
	vertical-align: bottom;
	margin-left: 4px;
}
.head svg {
	margin-right: 8px;
}
.head .right {
	font-weight: initial;
	font-size: 13px;
	float: right;
}
.head .left {
	display: flex;
	align-items: center;
	margin: 0;
}
.head_title {
	color: #333;
	padding: 10px 0;
	font-size: 20px;
	font-weight: 600;
}
.head_title::before {
	width: 8px;
	background: #1890ff;
	margin-right: 8px;
	content: "\00a0"; //填充空格
}

.monitor {
	margin-top: 31px;
	font-size: 12px;
	font-weight: bold;
	overflow: auto;
}
.monitor .row {
	display: flex;
	align-items: center;
	padding: 6px 0;
	padding-left: 24px;
}
.monitor .row:nth-child(odd) {
	background: #f2f2f2;
}

.monitor .circle {
	display: inline-block;
	width: 9px;
	height: 9px;
	background: #5b9bd5;
	border-radius: 50%;
	margin-right: 6px;
}

.ml8 {
	margin-left: 8px;
}

.tips {
	font-size: 12px;
}
</style>
