<template>
	<div class="product_width" :style="`height:${tabHeight}px;`">
		<a-spin :spinning="loading">
			<a-drawer
				placement="right"
				:closable="false"
				width="80%"
				:visible="visible1"
				@close="onClose1"
				:destroyOnClose="true"
			>
				<checkhistory :param="param"></checkhistory>
			</a-drawer>
			<a-drawer
				:bodyStyle="{ height: '100%' }"
				placement="right"
				:closable="false"
				width="80%"
				:visible="visible2"
				@close="onClose2"
			>
				<iframe :src="pdfUrl + '#view=FitH,top'" width="100%" height="100%"></iframe>
			</a-drawer>
			<strong class="head2">{{ productName }}产品主要技术文档</strong>
			<a-table
				:pagination="pagination"
				:rowKey="record => record.no"
				:columns="columns"
				:data-source="data"
				:style="`height:${tableHeight - 44}px;`"
				bordered
				size="small"
			>
				<a slot="fileName" slot-scope="text, record" @click="previewPdf(record.fileId)" :title="text">{{ text }}</a>
				<span slot="techStatus" slot-scope="text, record">
					<a v-if="record.fileId != null" @click="$refs.checkhistory2.edit(record)">审核记录</a>
					<sapn v-else></sapn>
				</span>
				<span slot="version" slot-scope="text, record">
					<a v-if="record.fileType.toLowerCase().indexOf('bom') < 0" @click="$refs.techhistory.view(record)">{{
						text
					}}</a>
					<a v-else @click="showHistory(record)">{{ text }}</a>
				</span>
			</a-table>
			<techhistory ref="techhistory" />
			<checkhistory2 ref="checkhistory2" @ok="handleOk" />
			<bomhistory ref="bomhistory" @ok="handleOk" />
		</a-spin>
	</div>
</template>

<script>
import { sysFileInfoDownload } from "@/api/modular/system/fileManage"
import checkhistory2 from "../../projects/checkhistory2"
import checkhistory from "../../projects/checkhistory"
import techhistory from "../../techdoc/techhistory"
import bomhistory from "../../projects/bomhistory"
const OtherComp = {
	name: "OtherComp",
	template: `
                    <div class="other-comp">
                        <div v-for='item in arr'>{{item}}</div>
                    </div>
                `,
	props: {
		arr: Array
	}
}

import { getDocs } from "@/api/modular/system/docManage"
export default {
	components: {
		techhistory,
		checkhistory2,
		checkhistory,
		bomhistory
	},
	props: {
		issueId: {
			type: Number,
			default: 0
		},
		productName: {
			type: String,
			default: ""
		},
		techStatus: {
			type: Number,
			default: null
		},
		// 表格高度
		tabHeight: {
			type: Number,
			default: 0
		},
		// 表格滚动高度
		scrollHeigh: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			pagination: {
				current: 1,
				pageSize: 12,
				total: 0,
				showSizeChanger: true,
				showQuickJumper: true,
				pageSizeOptions: ["12", "18"],
				onChange: (current, size) => {
					this.pagination.current = current
					this.pagination.pageSize = size
				},
				onShowSizeChange: (current, pageSize) => {
					this.pagination.current = 1
					this.pagination.pageSize = pageSize
				}
			},
			pdfUrl: "",
			historyBomId: "",
			param: {},
			visible1: false,
			visible2: false,
			windowHeight: document.documentElement.clientHeight - 20,
			mapStatus: ["编辑", "审核中", "已审核", "失败中", "被驳回"],
			merges: ["classType"],
			loading: false,
			columns: [
				{
					title: "序号",
					align: "center",
					dataIndex: "no",
					width: 60
				},
				{
					title: "分类",
					dataIndex: "classType",
					align: "center",
					width: 80,
					customRender: (text, row, index) => {
						return {
							children: text,
							attrs: {
								rowSpan: row["classType_rowSpan"] ? row["classType_rowSpan"] : 0
							}
						}
					}
				},
				{
					title: "文件类别",
					align: "center",
					dataIndex: "fileType",
					width: 100
				},
				{
					title: "文件名",
					align: "center",
					dataIndex: "fileName",
					scopedSlots: {
						customRender: "fileName"
					}
				},
				{
					title: "版本",
					align: "center",
					dataIndex: "version",
					width: 60,
					scopedSlots: {
						customRender: "version"
					}
				},
				{
					title: "日期",
					align: "center",
					dataIndex: "updatedDate",
					width: 80
				},
				{
					title: "工厂-产线",
					align: "center",
					dataIndex: "werkLineName",
					customRender: (text, row, index) => {
						let arr = text.split(";")
						return {
							children: <OtherComp arr={arr} />,
							attrs: {
								rowSpan: row["classType_rowSpan"] ? row["classType_rowSpan"] : 0
							}
						}
					}
				},
				{
					title: "负责人",
					align: "center",
					dataIndex: "manager",
					width: 80
				},
				{
					title: "提交人",
					align: "center",
					dataIndex: "submitter",
					width: 80
				},
				{
					title: "审批人",
					align: "center",
					dataIndex: "approver",
					width: 80
				},
				{
					title: "批准人",
					align: "center",
					dataIndex: "ratifier",
					width: 80
				},
				{
					title: "详情",
					dataIndex: "techStatus",
					width: 80,
					scopedSlots: {
						customRender: "techStatus"
					}
				}
			],
			data: []
		}
	},
	created() {
		this.callDocsData()

		// 动态修改--height的值
		document.documentElement.style.setProperty(`--height`, `${this.scrollHeigh - 44}px`)

	},
	methods: {
		showHistory(row) {
			let record = { ...{ id: row.bomId }, ...row }
			this.$refs.bomhistory.edit(record)
		},
		fileInfoDownload(record) {
			this.cardLoading = true
			sysFileInfoDownload({
				id: record.fileId
			})
				.then(res => {
					this.cardLoading = false
					this.downloadfile(res)
				})
				.catch(err => {
					this.cardLoading = false
					this.$message.error("下载错误：获取文件流错误")
				})
		},
		downloadfile(res) {
			var blob = new Blob([res.data], {
				type: "application/octet-stream;charset=UTF-8"
			})
			var contentDisposition = res.headers["content-disposition"]
			var patt = new RegExp("filename=([^;]+\\.[^\\.;]+);*")
			var result = patt.exec(contentDisposition)
			var filename = result[1]
			var downloadElement = document.createElement("a")
			var href = window.URL.createObjectURL(blob) // 创建下载的链接
			var reg = /^["](.*)["]$/g
			downloadElement.style.display = "none"
			downloadElement.href = href
			downloadElement.download = decodeURI(filename.replace(reg, "$1")) // 下载后文件名
			document.body.appendChild(downloadElement)
			downloadElement.click() // 点击下载
			document.body.removeChild(downloadElement) // 下载完成移除元素
			window.URL.revokeObjectURL(href)
		},
		onClose1() {
			this.visible1 = false
		},
		preview(id, processId) {
			this.historyBomId = id
			this.param.historyBomId = id
			this.param.processId = processId
			this.visible1 = !this.visible1
		},
		onClose2() {
			this.visible2 = false
		},
		previewPdf(id) {
			if (null == id) {
				this.$message.info("文件为空")
				return
			}

			this.pdfUrl = process.env.VUE_APP_API_BASE_URL + "/sysFileInfo/preview?id=" + id
			this.visible2 = !this.visible2
		},
		handleOk() {
			this.callDocsData()
		},
		callDocsData() {
			this.loading = true
			let params = {
				issueId: this.issueId,
				techStatus: this.techStatus
			}
			getDocs(params)
				.then(res => {
					if (res.success) {
						this.merges.forEach(item => {
							for (let i = 0, j = res.data.length; i < j; i++) {
								let rowSpan = 0
								let n = i
								while (res.data[n + 1] && res.data[n + 1][item] == res.data[n][item]) {
									rowSpan++
									n++
									res.data[n].rowSpan = 0
								}
								if (rowSpan) res.data[i][item + "_rowSpan"] = rowSpan + 1
								if (!rowSpan) res.data[i][item + "_rowSpan"] = 1
								i += rowSpan
							}
						})
						this.data = res.data
					} else {
						this.$message.error(res.message, 1)
					}
					this.loading = false
				})

				/*.then((res) => {
                        if (res.result) {
                            this.merges.forEach((item) => {
                            for (let i = 0, j = res.data.rowdata.length; i < j; i++) {
                                let rowSpan = 0;
                                let n = i;
                                while (
                                res.data.rowdata[n + 1] &&
                                res.data.rowdata[n + 1][item] == res.data.rowdata[n][item]
                                ) {
                                rowSpan++;
                                n++;
                                res.data.rowdata[n].rowSpan = 0;
                                }
                                if (rowSpan) res.data.rowdata[i][item + "_rowSpan"] = rowSpan + 1;

                                if (!rowSpan) res.data.rowdata[i][item + "_rowSpan"] = 1;

                                i += rowSpan;
                            }
                            });

                            this.data = res.data.rowdata
                        } else {
                            this.$message.error(res.message, 1);
                        }
                        this.loading = false
                    })*/
				.catch(err => {
					this.loading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		}
	}
}
</script>

<style lang="less" scoped>
@import "../productoption.less";

:root {
	--height: 600px;
}

.product_width{
	overflow: auto;
}

/deep/.ant-table-body {
	height: var(--height) !important;
	overflow-y: scroll;
}

</style>
