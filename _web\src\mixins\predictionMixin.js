/**
 * 预测相关的Mixin
 * 提供预测功能的公共方法，减少组件中的重复代码
 */
import { renderLatex, renderMathJax } from '@/utils/mathUtils';
import { showWarning, showSuccess } from '@/utils/errorUtils';
import { api } from '@/api';
import { chartColors, createCapacityCurveOptions, createLineSeries, createScatterSeries, createTooltipFormatter } from '@/utils/chartUtils';

export default {
  methods: {
    /**
     * 渲染LaTeX公式
     * @param {string} formula - 公式字符串
     * @returns {string} 渲染后的HTML
     */
    renderLatex(formula) {
      return renderLatex(formula);
    },

    /**
     * 触发MathJax渲染
     * @param {boolean} immediate - 是否立即渲染
     * @param {string} selector - 可选的CSS选择器，用于限制只渲染特定区域的LaTeX内容
     */
    renderMathJax(immediate = false, selector = null) {
      renderMathJax(immediate, selector);
    },

    /**
     * 格式化数值，保留指定小数位
     * @param {number} value - 数值
     * @param {number} precision - 小数位数
     * @returns {string} 格式化后的字符串
     */
    formatValue(value, precision = 4) {
      if (value === null || value === undefined) return '--';
      const numValue = typeof value === 'string' ? parseFloat(value) : value;
      if (isNaN(numValue)) return '--';
      return numValue.toFixed(precision);
    },

    /**
     * 获取公式详情
     * @param {number} formulaId - 公式ID
     * @returns {Promise} 公式详情Promise
     */
    async fetchFormulaDetail(formulaId) {
      try {
        if (!formulaId) {
          showWarning('请选择公式');
          return null;
        }

        const response = await api.formula.getFormulaById(formulaId);
        if (response.data.success) {
          return response.data.formula;
        } else {
          showWarning(response.data.message || '获取公式详情失败');
          return null;
        }
      } catch (error) {
        console.error('获取公式详情出错:', error);
        showWarning('获取公式详情失败: ' + (error.message || '未知错误'));
        return null;
      }
    },

    /**
     * 执行预测
     * @param {Object} params - 预测参数
     * @returns {Promise} 预测结果Promise
     */
    async executePrediction(params) {
      try {
        if (!params.formula_id) {
          showWarning('请选择公式');
          return null;
        }

        if (!params.temperature || !params.soc) {
          showWarning('请设置温度和SOC');
          return null;
        }

        if (!params.days || params.days.length === 0) {
          showWarning('请设置存储天数');
          return null;
        }

        const response = await api.prediction.predict(params);
        if (response.data.success) {
          showSuccess('预测成功');
          return response.data;
        } else {
          showWarning(response.data.message || '预测失败');
          return null;
        }
      } catch (error) {
        console.error('预测出错:', error);
        showWarning('预测失败: ' + (error.message || '未知错误'));
        return null;
      }
    },

    /**
     * 渲染预测结果图表
     * @param {Object} chartContainer - 图表容器DOM元素
     * @param {Array} days - 天数数组
     * @param {Array} capacities - 容量数组
     * @param {string} title - 图表标题
     */
    renderPredictionChart(chartContainer, days, capacities, title = '容量预测曲线') {
      if (!chartContainer || !days || !capacities) return;

      // 确保数据长度一致
      const minLength = Math.min(days.length, capacities.length);
      if (minLength === 0) return;

      // 格式化数据点
      const dataPoints = [];
      for (let i = 0; i < minLength; i++) {
        dataPoints.push([
          parseFloat(this.formatValue(days[i])),
          parseFloat(this.formatValue(capacities[i] * 100)) // 转换为百分比
        ]);
      }

      // 创建图表实例
      const echarts = require('echarts');
      const chart = echarts.init(chartContainer);

      // 创建数据系列
      const series = [
        createLineSeries('预测容量', dataPoints, 0, true, {
          showSymbol: true,
          symbolSize: 6
        })
      ];

      // 设置图表选项
      const option = createCapacityCurveOptions(title, series, {
        tooltip: {
          formatter: createTooltipFormatter
        },
        gridConfig: {
          bottom: 60,
          top: 50
        }
      });

      // 应用选项并渲染图表
      chart.setOption(option);

      // 监听窗口大小变化，自动调整图表大小
      window.addEventListener('resize', () => {
        chart.resize();
      });

      return chart;
    },

    /**
     * 按字母分组系数
     * @param {Array} coefficients - 系数数组
     * @returns {Array} 分组后的系数数组
     */
    groupCoefficientsByLetter(coefficients) {
      if (!coefficients || !Array.isArray(coefficients)) {
        return [];
      }

      const groups = {};

      coefficients.forEach(coefficient => {
        // 获取系数名称的第一个字母
        let name = coefficient.name;
        // 处理可能的下标或花括号
        if (name.includes('_') || name.includes('{')) {
          name = name.split(/[_{]/)[0];
        }
        const firstLetter = name.charAt(0).toUpperCase();

        if (!groups[firstLetter]) {
          groups[firstLetter] = {
            letter: firstLetter,
            coefficients: []
          };
        }

        groups[firstLetter].coefficients.push(coefficient);
      });

      // 将组按字母顺序排序
      return Object.values(groups).sort((a, b) => a.letter.localeCompare(b.letter));
    }
  }
};
