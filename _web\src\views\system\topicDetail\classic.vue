<template>
    <div>
        <div class="topic_wid">
            <a-breadcrumb class="breadcrumb" separator=">">
                <a-breadcrumb-item><a @click="goBack">课题看板</a></a-breadcrumb-item>
                <a-breadcrumb-item>课题类别</a-breadcrumb-item>
            </a-breadcrumb>
        </div>
        
        <x-card class="topic_wid">
            <div slot="content" class="table-page-search-wrapper">
                <a-form layout="inline">
                <a-row :gutter="48">
                    <a-col :md="5" :sm="24">
                        <a-form-item label="立项申请">
                           <a-range-picker
                                size="small"
                                :placeholder="['开始月份', '结束月份']"
                                v-model="dates"
                                :mode="['month', 'month']"
                                format="YYYY-MM"
                                @panelChange="handlePanelChange"
                                @openChange="handleOpenChange"
                                :open="monthPickShow"
                            >
                                <a-icon slot="suffixIcon" type="calendar" style="color:#d9d9d9" />
                            </a-range-picker>
                        </a-form-item>
                    </a-col>
                    <a-col :md="3" :sm="24">
                    <a-form-item label="">
                        <treeselect :limit="1" @input="change" :max-height="200" placeholder="请选择分类" value-consists-of="BRANCH_PRIORITY" v-model="queryParam.cateId" :multiple="true" :options="cate" :normalizer="normalizer">
<!--                          <p-->
<!--                            style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;width: 90%;"-->
<!--                            slot="option-label"-->
<!--                            slot-scope="{node}"-->
<!--                            :title="node.label">-->
<!--                            <template> {{ node.label }}</template>-->
<!--                          </p>-->
                        </treeselect>
                        <!-- <a-tree-select 
                        size="small"
                        multiple
                        :show-checked-strategy="SHOW_PARENT" @change="this.change"  v-model="queryParam.cateId" :dropdown-style="{ maxHeight: '130px', overflow: 'auto' }" :tree-data="cate" placeholder="分类" tree-default-expand-all>
                        </a-tree-select> -->
                    </a-form-item>
                    </a-col>
                    <!-- <template v-if="advanced"> -->
                        <a-col :md="3" :sm="24">
                            <a-form-item label="">
                                <!-- <a-tree-select multiple size="small"
                                    :show-checked-strategy="SHOW_PARENT" @change="this.change"  v-model="queryParam.deptId" :dropdown-style="{ maxHeight: '130px', overflow: 'auto' }" :tree-data="depts" placeholder="部门" tree-default-expand-all>
                                </a-tree-select> -->
                                <treeselect :limit="1" @input="change" :max-height="200" placeholder="请选择部门" value-consists-of="BRANCH_PRIORITY" v-model="queryParam.deptId" :multiple="true" :options="depts" :normalizer="deptNormalizer">
<!--                                  <p
                                    style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;width: 90%;"
                                    slot="option-label"
                                    slot-scope="{node}"
                                    :title="node.label">
                                    <template> {{ node.label }}</template>
                                  </p>-->
                                </treeselect>
                            </a-form-item>
                        </a-col>
                        <a-col :md="3" :sm="24">
                            <a-form-item label="">
                                <treeselect :limit="1" @input="change" :max-height="200" placeholder="请选择状态" value-consists-of="BRANCH_PRIORITY" v-model="queryParam.statuses" :multiple="true" :options="statuses" />
                                <!-- <a-select mode="multiple" size="small" @change="this.change" v-model="queryParam.statuses" placeholder="课题状态" >
                                    <a-select-option :value="1" >正常</a-select-option>
                                    <a-select-option :value="2" >延期</a-select-option>
                                    <a-select-option :value="3" >暂停</a-select-option>
                                    <a-select-option :value="4" >停止</a-select-option>
                                    <a-select-option :value="5" >结项</a-select-option>
                                </a-select> -->
                            </a-form-item>
                        </a-col>
                         <a-col :md="3" :sm="24">
                            <a-form-item label="">
                                <treeselect :limit="1" @input="change" :max-height="200" placeholder="请选择等级" value-consists-of="BRANCH_PRIORITY" v-model="queryParam.level" :multiple="true" :options="levels" />
                                <!-- <a-select size="small" @change="this.change" v-model="queryParam.level" placeholder="请选择等级" >
                                    <a-select-option value="S" >S</a-select-option>
                                    <a-select-option value="A" >A</a-select-option>
                                    <a-select-option value="B" >B</a-select-option>
                                </a-select> -->
                            </a-form-item>
                        </a-col>
                        <a-col :md="3" :sm="24">
                            <a-form-item label="">
                                <a-input size="small" @keyup.enter.native="change" v-model="queryParam.searchValue"  placeholder="请输入课题名称"/>
                            </a-form-item>
                        </a-col>
                    <!-- </template> -->
                    <a-col :md="!advanced && 1 || 24" :sm="24">
                    <span class="table-page-search-submitButtons" :style="advanced && { float: 'right', overflow: 'hidden' } || {} ">
                        <!-- <a-button type="primary" @click="callGetAllProjects">查询</a-button> -->
                        <a-button size="small" style="margin-left: 8px" @click="resetquery">重置</a-button>
                        <!-- <a @click="toggleAdvanced" style="margin-left: 8px">
                            {{ advanced ? '收起' : '展开' }}
                            <a-icon :type="advanced ? 'up' : 'down'"/>
                        </a> -->
                    </span>
                    </a-col>
                </a-row>
                </a-form>
            </div>
        </x-card>
        <div class="topic_wid head2" >技术课题清单</div>
        <a-table class="topic_wid" :rowKey="(record) => record.issueId" style="background: #fff" :columns="columns" :dataSource="loadData" :loading="loading" :pagination="pagination" >
            <span slot="num" slot-scope="text,records,index">
                {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
            </span>
            <template slot="projectName" slot-scope="text">
                <clamp :expend="true" :isCenter="true" :text="text" :sourceText="[text]" :isOneLine='true'></clamp>
            </template>
            <template slot="projectTarget" slot-scope="text">
                <clamp :expend="true" :isCenter="true" :text="text" :sourceText="[text]" :isOneLine='true'></clamp>
            </template>
            <template slot="cate" slot-scope="text,record">
                <div v-if="record.projectCateList[0]">{{record.projectCateList[0].value}}</div>
                <div v-if="record.projectCateList[1]">{{record.projectCateList[1].value}}</div>
            </template>
            <template slot="dept" slot-scope="text,record">
                <clamp :expend="true" :isCenter="true" :text="record.departmentCateList.map(e=>e.value).join('-')" :sourceText="[record.departmentCateList.map(e=>e.value).join('-')]" :isOneLine='true'></clamp>
            </template>
            <template slot="status" slot-scope="text,record">
                <span v-if="record.projectStatus == 1">正常</span>
                <span v-else-if="record.projectStatus == 2">延期</span>
                <span v-else-if="record.projectStatus == 3">暂停</span>
                <span v-else-if="record.projectStatus == 4">停止</span>
                <span v-else>结项</span>
            </template>
            <template slot="action" slot-scope="text,record">
                <a @click="$refs.viewfile.view(record,0)">查阅文件</a>
            </template>
        </a-table>
        <viewfile @preview="preview"  ref="viewfile" @ok="handleOk" />
        <!-- <div id="blobIframe" style="height: 500px"></div> -->
        <a-drawer
			  placement="right"
			  :closable="false"
			  width="80%"
			  :visible="drawerVisible"
			  @close="onClose"
			>
                <!-- <vue-file-viewer :fileUrl="file"  style="height: 500px;" /> -->
                <!-- <iframe :src="pdfUrl+'#view=FitH,top'" width="100%" height="100%" ></iframe> -->
                <!-- <div ref="file"></div> -->
                
		</a-drawer>
    </div>
</template>

<script>
import Vue from 'vue'
import axios from 'axios'
import {XCard,clamp} from '@/components'
import {getAllProjects,getCateTree,previewFile } from "@/api/modular/system/topic"
import moment from 'moment';
import viewfile from './viewfile'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
/* import VueFileViewer from '@zuiyouliao/vue-file-viewer' */
/* let docx = require("docx-preview");
window.JSZip = require("jszip"); */
export default {
    components: {
        XCard,
        clamp,
        viewfile,
        Treeselect
        /* VueFileViewer */
    },
    data() {
        return {
            levels:[
                {
                    id:'S',
                    label:'S'
                },{
                    id:'A',
                    label:'A'
                },{
                    id:"B",
                    label:'B'
                }
            ],
            statuses:[
                {
                    id:1,
                    label:'正常'
                },{
                    id:2,
                    label:'延期'
                },{
                    id:3,
                    label:'暂停'
                },{
                    id:4,
                    label:'停止'
                },{
                    id:5,
                    label:'结项'
                }
            ],
            normalizer(node) {
				return {
					id: node.value,
					label: node.title,
          children: node.children && node.children.length > 0 ? node.children: 0,
				}
			},
            deptNormalizer(node) {
				return {
					id: node.value,
					label: node.title,
          children: node.children && node.children.length > 0 ? node.children: 0,
        }
			},
            context: {
                origin: location.origin + '/file-viewer/index.html',
                // 目标frame
                frame: null,
            },
            file:'',
            drawerVisible:false,
            dates: [],
            depts:[],
            cate:[],
            monthPickShow: false,
            advanced: false,
            pagination: {
                current: 1,
                pageSize: 8,
                total: 0,
                showSizeChanger: true,
                showQuickJumper: true,
                onChange: (current, size) => {
                    this.pagination.current = current
                    this.pagination.pageSize = size
                },
                onShowSizeChange: (current, pageSize) => {
                    this.pagination.current = 1
                    this.pagination.pageSize = pageSize
                },
            },
            windowHeight: document.documentElement.clientHeight,
            loading: true,
            columns: [
                {
                    title: '序号',
                    width: 40,
                    dataIndex: 'no',
                    align:'center',
                    /* scopedSlots: {
                        customRender: 'num'
                    } */
                    customRender: (text, record, index) => `${index+1}`
                },
              // {
              //   title: '平台一级分类',
              //   dataIndex: 'affiliatedPlatform1',
              //   align: 'center',
              //   width: 80,
              //   slots: {title: 'affiliatedPlatform'},
              // },
              // {
              //   title: '平台二级分类',
              //   dataIndex: 'affiliatedPlatform2',
              //   align: 'center',
              //   width: 80,
              //   slots: {title: 'affiliatedPlatform'},
              // },
                {
                    title: '课题分类',
                    width: 120,
                    dataIndex: 'cate',
                    align:'center',
                    scopedSlots: { customRender: 'cate' },
                },
                {
                    title: '课题名称',
                    width: 120,
                    align:'center',
                    dataIndex: 'projectName',
                    scopedSlots: { customRender: 'projectName' },
                },
/*                {
                    title: '研究内容',
                    width: 200,
                    align:'center',
                    dataIndex: 'projectTarget',
                    scopedSlots: { customRender: 'projectTarget' },
                },*/
                {
                    title: '课题等级',
                    width: 60,
                    align:'center',
                    dataIndex: 'projectLevel',
                },
                {
                    title: '课题负责人',
                    width: 80,
                    align:'center',
                    dataIndex: 'projectLeader',
                },
                {
                    title: '部门',
                    width: 120,
                    align:'center',
                    dataIndex: 'dept',
                    scopedSlots: { customRender: 'dept' },
                },
                {
                    title: '立项评审日期',
                    width: 80,
                    align:'center',
                    dataIndex: 'initiationDate',
                },
                {
                    title: '课题状态',
                    width: 60,
                    align:'center',
                    dataIndex: 'status',
                    scopedSlots: { customRender: 'status' },
                },
                {
                    title: '操作',
                    width: 60,
                    align:'center',
                    dataIndex: 'action',
                    scopedSlots: { customRender: 'action' },
                },
            ],
            loadData: [],
            queryParam: {},
            loading:false
        }
    },
    methods:{
        resetquery(){
            this.queryParam = {}
            this.dates = [moment().subtract(1, 'year').startOf('year'),moment()],

            this.queryParam.startDate = moment(this.dates[0]._d).format('YYYY-MM')
            this.queryParam.endDate = moment(this.dates[1]._d).format('YYYY-MM')
            this.callGetAllProjects()
        },
        appendFrame(src) {
            var blobIframe = document.getElementById('blobIframe')
            if (this.context.frame) {
                blobIframe.removeChild(this.context.frame)
            }
            // 构建frame
            var frame = (this.context.frame = document.createElement('iframe'))
            frame.src = src
            frame.style = 'border:0;height: 100%;width:100%'
            return blobIframe.appendChild(frame)
        },
        preview(attachmentId,attachmentName){
            var blob = null;
            var url = null;
            previewFile({issueId:attachmentId}).then((res) => {
                /* docx.renderAsync(res, this.$refs.file) 
                this.drawerVisible = true */
                blob = new Blob([res])
                url = URL.createObjectURL(blob);
                var src = `${this.context.origin}?name=${encodeURIComponent(attachmentName)}&from=${encodeURIComponent(location.origin)}`
                var frame = this.appendFrame(src)
                frame.onload = () => {
                    previewFile({issueId:attachmentId}).then((res) => {
                        console.log(res)
                        frame.contentWindow.postMessage(res, this.context.origin)
                        //this.drawerVisible = true
                    })
                    .catch((err) => {
                        this.$message.error('错误提示：' + err.message,1)
                    }); 
                }
            })
            .catch((err) => {
                this.$message.error('错误提示：' + err.message,1)
            });
            
            
        },
        onClose() {
			this.drawerVisible = false;
		},
        goBack(){
            this.$router.push({
                path: "/topic_chart",
            })
        },
        handleOk() {},
        moment,
        change(value, label, extra){
            this.callGetAllProjects()
        },
        handlePanelChange(value, mode) {

            if (this.dates[1] && this.dates[1]._d != value[1]._d) {
                this.dates = value
                this.monthPickShow = false;
                this.queryParam.startDate = moment(this.dates[0]._d).format('YYYY-MM')
                this.queryParam.endDate = moment(this.dates[1]._d).format('YYYY-MM')
                this.callGetAllProjects()
            }
            this.dates = value
        },
        handleOpenChange(status) {
            if(status){
                this.monthPickShow = true;
            }else{
                this.monthPickShow = false
            }
        },
        toggleAdvanced () {
            this.advanced = !this.advanced
        },
        callGetAllProjects() {
            this.loading = true
            getAllProjects(this.queryParam).then((res) => {
            if (res.success) {
                this.loadData = res.data.list
                
            } else {
                this.$message.error('错误提示：' + res.message,1)
            }
            this.loading = false
            })
            .catch((err) => {
                this.$message.error('错误提示：' + err.message,1)
                this.loading = false
            });
        },

        callGetDeptTree(){
            getCateTree({
                fieldName:'department',
                flag:1
            }).then((res)=>{
                if (res.success) {
                    this.depts = res.data
                } else {
                    this.$message.error('错误提示：' + res.message, 1)
                }
            }).catch((err) => {
                this.$message.error('错误提示：' + err.message, 1)
            });
        },

        callGetTree(){
            getCateTree({
                fieldName:'projectCate',
                flag:1
            }).then((res)=>{
                if (res.success) {
                    this.cate = res.data
                } else {
                    this.$message.error('错误提示：' + res.message, 1)
                }
            }).catch((err) => {
                this.$message.error('错误提示：' + err.message, 1)
            });
        },
    },
    created(){
        if (this.$route.query.cateId) {
            this.queryParam.cateId = [this.$route.query.cateId]
        }
        if (this.$route.query.deptId) {
            this.queryParam.deptId = [this.$route.query.deptId] 
        }
        if (this.$route.query.statuses) {
            this.queryParam.statuses = [this.$route.query.statuses]
        }
        this.queryParam.startDate = this.$route.query.startDate
        this.queryParam.endDate = this.$route.query.endDate

        this.dates =[moment(this.$route.query.startDate, 'YYYY-MM'),moment(this.$route.query.endDate, 'YYYY-MM')]

        this.callGetTree()
        this.callGetDeptTree()
        this.callGetAllProjects()
    }
}
</script>

<style lang="less" scoped=''>
@import './topic.less';
span.foot{
    margin-right: 12px;
}
</style>