/*
@license

dhtmlxGantt v.6.3.3 Standard

This version of dhtmlxGantt is distributed under GPL 2.0 license and can be legally used in GPL projects.

To use dhtmlxGantt in non-GPL projects (and get Pro version of the product), please obtain Commercial/Enterprise or Ultimate license on our site https://dhtmlx.com/docs/products/dhtmlxGantt/#licensing or contact <NAME_EMAIL>

(c) XB Software Ltd.

*/
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("locale/locale_pt",[],t):"object"==typeof exports?exports["locale/locale_pt"]=t():e["locale/locale_pt"]=t()}(window,function(){return function(e){var t={};function o(n){if(t[n])return t[n].exports;var a=t[n]={i:n,l:!1,exports:{}};return e[n].call(a.exports,a,a.exports,o),a.l=!0,a.exports}return o.m=e,o.c=t,o.d=function(e,t,n){o.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.t=function(e,t){if(1&t&&(e=o(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(o.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)o.d(n,a,function(t){return e[t]}.bind(null,a));return n},o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,"a",t),t},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.p="/codebase/",o(o.s=188)}({188:function(e,t){gantt.locale={date:{month_full:["Janeiro","Fevereiro","Março","Abril","Maio","Junho","Julho","Agosto","Setembro","Outubro","Novembro","Dezembro"],month_short:["Jan","Fev","Mar","Abr","Mai","Jun","Jul","Ago","Set","Out","Nov","Dez"],day_full:["Domingo","Segunda","Terça","Quarta","Quinta","Sexta","Sábado"],day_short:["Dom","Seg","Ter","Qua","Qui","Sex","Sab"]},labels:{new_task:"Nova tarefa",dhx_cal_today_button:"Hoje",day_tab:"Dia",week_tab:"Semana",month_tab:"Mês",new_event:"Novo evento",icon_save:"Salvar",icon_cancel:"Cancelar",icon_details:"Detalhes",icon_edit:"Editar",icon_delete:"Deletar",confirm_closing:"Suas alterações serão perdidas. Você tem certeza?",confirm_deleting:"Tem certeza que deseja excluir?",section_description:"Descrição",section_time:"Período de tempo",section_type:"Type",column_wbs:"EAP",column_text:"Nome tarefa",column_start_date:"Data início",column_duration:"Duração",column_add:"",link:"Link",confirm_link_deleting:"será apagado",link_start:" (início)",link_end:" (fim)",type_task:"Task",type_project:"Project",type_milestone:"Milestone",minutes:"Minutos",hours:"Horas",days:"Dias",weeks:"Semanas",months:"Meses",years:"Anos",message_ok:"OK",message_cancel:"Cancelar",section_constraint:"Constraint",constraint_type:"Constraint type",constraint_date:"Constraint date",asap:"As Soon As Possible",alap:"As Late As Possible",snet:"Start No Earlier Than",snlt:"Start No Later Than",fnet:"Finish No Earlier Than",fnlt:"Finish No Later Than",mso:"Must Start On",mfo:"Must Finish On",resources_filter_placeholder:"type to filter",resources_filter_label:"hide empty"}}}})});
//# sourceMappingURL=locale_pt.js.map