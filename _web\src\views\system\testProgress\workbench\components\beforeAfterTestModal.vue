<template>
  <a-modal
    :maskClosable="false"
    :title="`${modalData.testName}`"
    :visible="true"
    width="85%"
    :centered="true"
    :cancel-button-props="{ style: { display: 'none' } }"
    okText="关闭"
    @cancel="handleModelCancel"
    @ok="handleModelCancel">

    <div class="modal-wrapper">

      <div>
        <a-spin :spinning="modalLoading">
          <div>
            <a-descriptions title="详细信息">
              <a-descriptions-item label="测试项目名称">
                {{ originalData[0].testAlias.replaceAll(/[0-9]/g, "") }}
              </a-descriptions-item>
              <a-descriptions-item>
                <template slot="label">
                  <span style="font-weight: bold">中检类型</span>
                </template>
                <div style="font-weight: bold;color: black">
                  {{
                    originalData[0].middleCheck === "small"
                      ? "小中检"
                      : originalData[0].middleCheck === "large"
                        ? "大中检"
                        : originalData[0].middleCheck === "recharge"
                          ? "补电"
                          : "-"
                  }}
                </div>
              </a-descriptions-item>
              <a-descriptions-item label="测试设备">
                {{ originalData[0].equiptCodeNames || "-" }}
              </a-descriptions-item>
              <a-descriptions-item label="计划开始时间">
                {{ originalData[0].planStartTime || "-" }}
              </a-descriptions-item>
              <a-descriptions-item label="计划结束时间">
                {{ originalData[0].planEndTime || "-" }}
              </a-descriptions-item>
              <a-descriptions-item>
                <template slot="label">
                  <span style="font-weight: bold">测试阶段</span>
                </template>
                <div style="font-weight: bold;color: black">
                {{
                  originalData[0].stage
                    ? originalData[0].stage === "0"
                      ? "测试前"
                      : "测试后"
                    : "-"
                }}
                </div>
              </a-descriptions-item>
              <a-descriptions-item label="实际进箱时间">
                <a-date-picker
                  placeholder="请选择实际进箱时间"
                  format="YYYY-MM-DD"
                  :disabled="modalData.taskStatus === '已完成'"
                  :allowClear="false"
                  @change="(date, dateString) => actualDateChange(date, dateString, originalData[0] ,'in')"
                  style="width: 170px;"
                  v-model="originalData[0].actualInDate">
                </a-date-picker>
              </a-descriptions-item>
              <a-descriptions-item label="实际出箱时间">
                <a-date-picker
                  placeholder="请选择实际出箱时间"
                  format="YYYY-MM-DD"
                  v-if="originalData[0].testType!=='before_after'"
                  :disabled="modalData.taskStatus === '已完成'"
                  :allowClear="false"
                  @change="(date, dateString) => actualDateChange(date, dateString, originalData[0] ,'out')"
                  style="width: 170px;"
                  v-model="originalData[0].actualOutDate">
                </a-date-picker>
                <span v-else>-</span>
              </a-descriptions-item>
              <a-descriptions-item label="是否加急">
                {{ modalData.isUrgent !== null ? (modalData.isUrgent == 1 ? '是' : '否') : '/' }}
              </a-descriptions-item>
            </a-descriptions>
          </div>
          <div>
            <div style="margin:10px 0px 15px 0px">
              <a-upload
                :disabled="modalData.taskStatus === '已完成' ? true : false"
                :headers="headers"
                :action="`/api/testProjectTodoTask/importOfAq`"
                name="file"
                :fileList="fileList"
                :data="{ safetyTestId: originalData[0].id }"
                :showUploadList="false"
                accept="*"
                @change="handleUploadFile($event)"
              >
                <a-button
                  :disabled="modalData.taskStatus === '已完成' || firstData.findIndex(v => v.batteryStatus === 'ongoing') === -1"
                  v-if="originalData[0].testData"
                  type="primary"
                  size="small"
                  class="mr10"
                  ghost
                >导入数据</a-button
                >
              </a-upload>
              <a-button v-if="originalData[0].testData" class="mr10" type="primary" size="small" ghost @click="handleDownload($event,0, originalData[0])">导出模板</a-button>
              <a-tooltip>
                <template slot="title">
                  提示内容
                </template>
                <a-button v-if="serialObj.serialPortOpen && originalData[0].testData"  class="mr10" type="primary" size="small" ghost @click="handleCloseConnect">断开电压内阻测试仪</a-button>
                <a-button id="connectBtn" v-else-if="originalData[0].testData" class="mr10" type="primary" size="small" ghost @click="handleOpenConnect">连接电压内阻测试仪</a-button>
              </a-tooltip>

            </div>
            <div class="auto-table">
              <a-table
                bordered
                class="mt10"
                v-if="originalData[0].testData"
                :columns="firstColums"
                :rowKey="record => record.cellTestCode"
                :data-source="firstData"
                :pagination="false"
              >
								<span v-for="item in firstList" :key="item.cellTestCode" :slot="item.dataIndex" slot-scope="text, record, index">
									<span v-if="item.dataIndex === 'cellTestCode'">
										<div class="blue hand" @click="handleCopy(text)">{{ text }}<a-icon type="copy" /></div>
									</span>
									<span v-else-if="item.dataIndex === 'alias'">
										<div class="blue hand" @click="aliasCopy(originalData[0])">{{ text }}<a-icon type="copy" /></div>
									</span>
                  <!-- class:first:第一步，middleCheck：对应的字段，index：那行,为了未填写标红 -->
									<span :id="`first-middleCheck-${index}`" v-else-if="item.dataIndex === 'middleCheck'">
										<a-button
                      v-if="originalData[0].middleCheck !== 'normal'"
                      type="link"
                      :disabled="record.batteryStatus !== 'ongoing'"
                      :style="record.isMiddleClick ? 'color:green' : ''"
                      @click="() => chooseMgData(record, `first-middleCheck-${index}`, originalData[0], firstData)"
                    >
											{{ record.isMiddleClick ? (record.checkData === null ? '无匹配数据' : (text === "large" ? "大中检" : text === "small" ? "小中检" : "补电")) : "请选择数据"
                      }}<a-icon v-if="record.isMiddleClick" type="edit" />
										</a-button>
										<span v-else type="link">-</span>
									</span>

                  <!-- 电芯状态 -->
                  <span v-else-if="item.dataIndex === 'batteryStatus'">
                    <a-select v-model="record.batteryStatus" @change="changeBatteryStatus(originalData[0], record)"
                              :disabled="modalData.taskStatus === '已完成' || record.batteryStatus !== 'ongoing'"
                              style="width: 160px;font-size: 14px;">
                      <a-select-option value="ongoing">
                        进行中
                      </a-select-option>
                      <a-select-option value="testDone">
                        状态正常-测试完成
                      </a-select-option>
                      <a-select-option value="earlyEnd">
                        状态正常-提前结束
                      </a-select-option>
                      <a-select-option value="batteryDisassembly">
                        状态正常-电池拆解
                      </a-select-option>
                      <a-select-option value="pressureDrop">
                        掉压失效-终止测试
                      </a-select-option>
                      <a-select-option value="abnormalHot">
                        异常发热-终止测试
                      </a-select-option>
                      <a-select-option value="openShellAndLeak">
                        开壳漏液-终止测试
                      </a-select-option>
                      <a-select-option value="shellRust">
                        壳体生锈-终止测试
                      </a-select-option>
                      <a-select-option value="operationError">
                        作业错误-终止测试
                      </a-select-option>
                      <a-select-option value="thermalRunaway">
                        热失控-终止测试
                      </a-select-option>
                      <a-select-option value="acrException">
                        内阻异常-终止测试
                      </a-select-option>
                      <a-select-option value="swelling">
                        鼓包形变-终止测试
                      </a-select-option>
                    </a-select>
                  </span>

                  <!-- 驳回信息 -->
                  <span v-else-if="item.dataIndex === 'rejectMsg'">
                    <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
                      <template slot="title">
                        {{text}}
                      </template>
                      <div style="max-width: 100px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;color: red;">
                        {{text}}
                      </div>
                    </a-tooltip>
                  </span>

                  <!-- 图片上传 -->
                  <span v-else-if="picOrVidMenu[item.dataIndex]">
                    <span v-if="record.batteryStatus !== 'ongoing' && !record.samplePicture[item.dataIndex].id ">
                      <a style="color: darkgrey;">上传</a>
                    </span>
                    <span v-else-if="!record.samplePicture[item.dataIndex].id && modalData.taskStatus !== '已完成'" style="display: flex;justify-content: center; align-items: center;">
                      <a-upload
                        name="file"
                        :headers="headers"
                        :data="uploadData"
                        :action="picOrVidPostUrl"
                        :before-upload="beforeUploadPicture"
                        :multiple="false"
                        :showUploadList="false"
                        @change="uploadPicture($event, item.dataIndex, index, originalData[0])"
                        accept=".jpg,.png,.gif">
                        <a>上传</a>
                      </a-upload>
                    </span>
                    <span v-else-if="record.samplePicture[item.dataIndex].uid">
                      <a-upload
                          list-type="picture-card"
                          class="avatar-uploader"
                          :disabled="modalData.taskStatus === '已完成' || record.batteryStatus !== 'ongoing'"
                          @change="deletePicture(item.dataIndex, index, originalData[0])"
                          :fileList="[record.samplePicture[item.dataIndex]]"
                          @preview="handlePreview">
                      </a-upload>
                    </span>
                    <span v-else>
                      <a style="color: green" @click="openFileOrDownload(record.samplePicture[item.dataIndex].id,record.samplePicture[item.dataIndex].name)">{{ record.samplePicture[item.dataIndex].name }}</a>
                        <a-popconfirm
                          placement="topRight"
                          ok-text="删除"
                          cancel-text="取消"
                          @confirm="deletePicture(item.dataIndex, index, originalData[0])">
                          <template slot="title"> 确认删除文件"{{ record.samplePicture[item.dataIndex].name }}"吗 </template>
                          <a-icon v-if="modalData.taskStatus !== '已完成'" type="close" style="float: right;padding: 5px 0px 0px 5px" />
                        </a-popconfirm>
                     </span>
                  </span>

                  <!-- 输入框上传 -->
                  <a-input
                    v-else
                    :id="`first-${item.dataIndex}-${index}`"
                    v-model="firstData[index][item.dataIndex]"
                    :disabled="(modalData.taskStatus === '已完成' ? true : false) || record.batteryStatus !== 'ongoing'"
                    @paste="copyFromExcel($event, firstColums, firstData, index, item.dataIndex)"
                    @keyup.enter="handleWrite(item.dataIndex,index,`first-${item.dataIndex}-${index + 1}`, originalData[0], firstData)"
                    @blur="value => handleInput(value.target._value, `first-${item.dataIndex}-${index}`, index, originalData[0], firstData)"
                  />

								</span>
              </a-table>
            </div>
          </div>
          <a-divider style="font-weight: bold;color: black" />
          <div>
            <a-descriptions title="">
              <a-descriptions-item label="测试项目名称">
                {{ originalData[1].testAlias.replaceAll(/[0-9]/g, "") }}
              </a-descriptions-item>
              <a-descriptions-item>
                <template slot="label">
                  <span style="font-weight: bold">中检类型</span>
                </template>
                <div style="font-weight: bold;color: black">
                  {{
                    originalData[1].middleCheck === "small"
                      ? "小中检"
                      : originalData[1].middleCheck === "large"
                        ? "大中检"
                        : originalData[1].middleCheck === "recharge"
                          ? "补电"
                          : "-"
                  }}
                </div>
              </a-descriptions-item>
              <a-descriptions-item label="测试设备">
                {{ originalData[1].equiptCodeNames || "-" }}
              </a-descriptions-item>
              <a-descriptions-item label="计划开始时间">
                {{ originalData[1].planStartTime || "-" }}
              </a-descriptions-item>
              <a-descriptions-item label="计划结束时间">
                {{ originalData[1].planEndTime || "-" }}
              </a-descriptions-item>
              <a-descriptions-item>
                <template slot="label">
                  <span style="font-weight: bold">测试阶段</span>
                </template>
                <div style="font-weight: bold;color: black">
                  {{
                    originalData[1].stage
                      ? originalData[1].stage === "0"
                        ? "测试前"
                        : "测试后"
                      : "-"
                  }}
                </div>
              </a-descriptions-item>
              <a-descriptions-item label="实际进箱时间">
                <a-date-picker
                  placeholder="请选择实际进箱时间"
                  format="YYYY-MM-DD"
                  v-if="originalData[1].testType!=='before_after'"
                  :disabled="modalData.taskStatus === '已完成'"
                  :allowClear="false"
                  @change="(date, dateString) => actualDateChange(date, dateString, originalData[1] ,'in')"
                  style="width: 170px;"
                  v-model="originalData[1].actualInDate">
                </a-date-picker>
                <span v-else>-</span>
              </a-descriptions-item>
              <a-descriptions-item label="实际出箱时间">
                <a-date-picker
                  placeholder="请选择实际出箱时间"
                  format="YYYY-MM-DD"
                  :disabled="modalData.taskStatus === '已完成'"
                  :allowClear="false"
                  @change="(date, dateString) => actualDateChange(date, dateString, originalData[1] ,'out')"
                  style="width: 170px;"
                  v-model="originalData[1].actualOutDate">
                </a-date-picker>
              </a-descriptions-item>
              <a-descriptions-item label="是否加急">
                {{ modalData.isUrgent !== null ? (modalData.isUrgent == 1 ? '是' : '否') : '/' }}
              </a-descriptions-item>
            </a-descriptions>
          </div>
          <div>
            <div style="margin:10px 0px 15px 0px">
              <a-upload
                :disabled="modalData.taskStatus === '已完成' ? true : false"
                :headers="headers"
                :action="`/api/testProjectTodoTask/importOfAq`"
                name="file"
                :fileList="fileList"
                :data="{ safetyTestId: originalData[1].id }"
                :showUploadList="false"
                accept="*"
                @change="handleUploadFile($event)"
              >
                <a-button
                  :disabled="modalData.taskStatus === '已完成' || lastData.findIndex(v => v.batteryStatus === 'ongoing') === -1"
                  v-if="originalData[1].testData"
                  type="primary"
                  size="small"
                  class="mr10"
                  ghost
                >导入数据</a-button
                >
              </a-upload>
              <a-button v-if="originalData[1].testData" class="mr10" type="primary" size="small" ghost @click="handleDownload($event,1, originalData[1])">导出模板</a-button>
              <a-tooltip>
                <template slot="title">
                  提示内容
                </template>
                <a-button v-if="serialObj.serialPortOpen && originalData[1].testData"  class="mr10" type="primary" size="small" ghost @click="handleCloseConnect">断开电压内阻测试仪</a-button>
                <a-button id="connectBtn" v-else-if="originalData[1].testData" class="mr10" type="primary" size="small" ghost @click="handleOpenConnect">连接电压内阻测试仪</a-button>
              </a-tooltip>
              <a-button :loading="videoLoading" v-if="isNewestBeforeAfterFlag && originalData[1].videoFlag === '1'" :disabled="modalData.taskStatus === '已完成'" class="mr10" type="primary" size="small" ghost @click="() => (chooseCodeVideoVisible = true, selectedRowCodeVideoKeys = [])">导入测试视频</a-button>
              <a-button :loading="attachLoading" v-if="isNewestBeforeAfterFlag && originalData[1].attachmentFlag === '1'" :disabled="modalData.taskStatus === '已完成'" class="mr10" type="primary" size="small" ghost @click="() => (chooseCodeAttachVisible = true, selectedRowCodeAttachKeys = [])">导入过程数据</a-button>
              <a-upload
                name="file"
                :headers="headers"
                :customRequest="$event => handleUploadOfAttach($event, originalData[1], 1)"
                :data="uploadData"
                :disabled="attachLoading || modalData.taskStatus === '已完成'"
                :action="picOrVidPostUrl"
                :multiple="false"
                :showUploadList="false"
                @change="uploadAttachment($event, originalData[1])">
                <a-button :loading="attachLoading" v-if="!isNewestBeforeAfterFlag && originalData[1].attachmentFlag === '1'" :disabled="modalData.taskStatus === '已完成'" class="mr10" type="primary" size="small" ghost @click="">导入过程数据</a-button>
              </a-upload>
              <div :style="{ float: 'right', width: '22%', paddingTop: isNewestBeforeAfterFlag ? '0px' : '45px' }" v-if="uploadProgress > 0 || uploadProgressShow">
                测试视频<a-progress :strokeWidth="12" :percent="uploadProgress"></a-progress>
              </div>
              <div :style="{ float: 'right', width: '22%', paddingTop: isNewestBeforeAfterFlag ? '0px' : '45px' }" v-show="'uploading' === uploadingProgressList[1].uploadStatusOfAttach">
                过程数据<a-progress :strokeWidth="12" :percent="uploadingProgressList[1].percentOfAttach"></a-progress>
              </div>
            </div>
            <div class="auto-table" style="margin-top: -32px">
              <div :style="{ width: '100%', height: isNewestBeforeAfterFlag ? '25px' : '50px' }">
                <!-- 过程视频 -->
                <div v-if="!isNewestBeforeAfterFlag && originalData[1].videoFlag ==='1'" style="margin-bottom: 50px">
                  <div style="border: 1px solid lightgrey;width: 100px;height:40px;float: left;display: flex;justify-content: center; align-items: center;">
                    <span style="color: black;">测试视频</span>
                  </div>

                  <div style="border: 1px solid lightgrey;width: 280px;height:40px;float: left;display: flex;justify-content: center; align-items: center;">
                    <span v-if="!originalData[1].videoName" style="display: flex;justify-content: center; align-items: center;">
                      <a-upload
                        name="file"
                        :headers="headers"
                        :customRequest="handleUpload"
                        :data="uploadData"
                        :action="picOrVidPostUrl"
                        :before-upload="beforeUploadVideo"
                        :multiple="false"
                        :disabled="videoLoading || modalData.taskStatus === '已完成'"
                        :showUploadList="false"
                        @change="uploadVideo($event, originalData[1])"
                        accept=".mp4,.avi,.wmv,.mov">
                        <a-spin :spinning="videoLoading"><a :disabled="modalData.taskStatus === '已完成'">上传</a></a-spin>
                      </a-upload>

                    </span>
                    <span v-else style="display: flex;justify-content: center; align-items: center;">
                      <a style="color: green;text-align: center;" @click="openFileOrDownload(originalData[1].videoId, originalData[1].videoName)">{{ originalData[1].videoName }}</a>
                        <a-popconfirm
                          placement="topRight"
                          ok-text="删除"
                          cancel-text="取消"
                          @confirm="deleteVideo($event, originalData[1])">
                          <template slot="title"> 确认删除视频"{{ originalData[1].videoName }}"吗 </template>
                          <a-icon v-if="modalData.taskStatus !== '已完成'"  type="close" style="float: right;padding: 5px 0px 0px 5px" />
                        </a-popconfirm>
                  </span>
                  </div>
<!--                  <div style="float: right;width: 22%;padding-top: 0px;" v-if="uploadProgress > 0 || uploadProgressShow">-->
<!--                    测试视频<a-progress strokeWidth="12" :percent="uploadProgress"></a-progress>-->
<!--                  </div>-->
                </div>

                <!-- 过程数据 -->
                <div v-if="!isNewestBeforeAfterFlag && originalData[1].attachmentFlag ==='1'" style="margin-bottom: 50px">
                  <div style="border: 1px solid lightgrey;width: 100px;height:40px;float: left;display: flex;justify-content: center; align-items: center;">
                    <span style="color: black;">过程数据</span>
                  </div>
                  <div style="border: 1px solid lightgrey;width: 400px;height:40px;float: left;display: flex;justify-content: center; align-items: center;">
                    <span style="display: flex;justify-content: center; align-items: center;">
                      <span v-for="fileItem in attachmentList">
                        <a style="color: green;text-align: center;" @click="openFileOrDownload(fileItem.id, fileItem.name)">{{ fileItem.name }}</a>
                        <a-popconfirm
                          placement="topRight"
                          ok-text="删除"
                          cancel-text="取消"
                          @confirm="deleteAttachment($event, originalData[1], fileItem)">
                          <template slot="title"> 确认删除过程数据"{{ fileItem.name }}"吗 </template>
                          <a-icon v-if="modalData.taskStatus !== '已完成'"  type="close" style="float: right;padding: 5px 0px 0px 5px" />
                        </a-popconfirm>
                      </span>
                    </span>
                  </div>
<!--                  <div style="float: right;width: 22%;padding-top: 0px;" v-show="'uploading' === originalData[1].uploadStatusOfAttach">-->
<!--                    过程数据<a-progress strokeWidth="12" :percent="originalData[1].percentOfAttach"></a-progress>-->
<!--                  </div>-->
                </div>

              </div>

              <a-table
                bordered
                class="mt10"
                v-if="originalData[1].testData"
                :columns="lastColums"
                :rowKey="record => record.cellTestCode"
                :data-source="lastData"
                :pagination="false"
              >
								<span v-for="item in lastList" :key="item.cellTestCode" :slot="item.dataIndex" slot-scope="text, record, index">
									<span v-if="item.dataIndex === 'cellTestCode'">
										<div class="blue hand" @click="handleCopy(text)">{{ text }}<a-icon type="copy" /></div>
									</span>
									<span v-else-if="item.dataIndex === 'alias'">
										<div class="blue hand" @click="aliasCopy(originalData[1])">{{ text }}<a-icon type="copy" /></div>
									</span>
                  <!-- class:first:第一步，middleCheck：对应的字段，index：那行,为了未填写标红 -->
									<span :id="`first-middleCheck-${index}`" v-else-if="item.dataIndex === 'middleCheck'">
										<a-button
                      v-if="originalData[1].middleCheck !== 'normal'"
                      type="link"
                      :disabled="record.batteryStatus !== 'ongoing'"
                      :style="record.isMiddleClick ? 'color:green' : ''"
                      @click="() => chooseMgData(record, `first-middleCheck-${index}`, originalData[1], lastData)"
                    >
											{{ record.isMiddleClick ? (record.checkData === null ? '无匹配数据' : (text === "large" ? "大中检" : text === "small" ? "小中检" : "补电")) : "请选择数据"
                      }}<a-icon v-if="record.isMiddleClick" type="edit" />
										</a-button>
										<span v-else type="link">-</span>
									</span>

                  <!-- 电芯状态 -->
                  <span v-else-if="item.dataIndex === 'batteryStatus'">
                    <a-select v-model="record.batteryStatus" @change="changeBatteryStatus(originalData[1], record)"
                              :disabled="modalData.taskStatus === '已完成' || record.batteryStatus !== 'ongoing'"
                              style="width: 160px;font-size: 14px;">
                      <a-select-option value="ongoing">
                        进行中
                      </a-select-option>
                      <a-select-option value="testDone">
                        状态正常-测试完成
                      </a-select-option>
                      <a-select-option value="earlyEnd">
                        状态正常-提前结束
                      </a-select-option>
                      <a-select-option value="batteryDisassembly">
                        状态正常-电池拆解
                      </a-select-option>
                      <a-select-option value="pressureDrop">
                        掉压失效-终止测试
                      </a-select-option>
                      <a-select-option value="abnormalHot">
                        异常发热-终止测试
                      </a-select-option>
                      <a-select-option value="openShellAndLeak">
                        开壳漏液-终止测试
                      </a-select-option>
                      <a-select-option value="shellRust">
                        壳体生锈-终止测试
                      </a-select-option>
                      <a-select-option value="operationError">
                        作业错误-终止测试
                      </a-select-option>
                      <a-select-option value="thermalRunaway">
                        热失控-终止测试
                      </a-select-option>
                      <a-select-option value="acrException">
                        内阻异常-终止测试
                      </a-select-option>
                    </a-select>
                  </span>

                  <!-- 驳回信息 -->
                  <span v-else-if="item.dataIndex === 'rejectMsg'">
                    <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
                      <template slot="title">
                        {{text}}
                      </template>
                      <div style="max-width: 100px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;color: red;">
                        {{text}}
                      </div>
                    </a-tooltip>
                  </span>

                  <span v-else-if="item.dataIndex === 'sampleVideo'">
                    <a style="color: green;text-align: center;" @click="openFileOrDownload(record.sampleVideo.id, record.sampleVideo.name)">{{ record.sampleVideo.name }}</a>
                    <a-popconfirm
                      placement="topRight"
                      ok-text="删除"
                      cancel-text="取消"
                      @confirm="deleteCodeVideo(originalData[1], record)">
                      <template slot="title"> 确认删除测试视频"{{ record.sampleVideo.name }}"吗 </template>
                      <a-icon v-if="record.sampleVideo.name && modalData.taskStatus !== '已完成' && record.batteryStatus === 'ongoing'" type="close" style="float: right;padding: 5px 0px 0px 5px" />
                    </a-popconfirm>
									</span>
                  <span v-else-if="item.dataIndex === 'sampleAttachment'">
                    <a style="color: green;text-align: center;" @click="openFileOrDownload(record.sampleAttachment.id, record.sampleAttachment.name)">{{ record.sampleAttachment.name }}</a>
                    <a-popconfirm
                      placement="topRight"
                      ok-text="删除"
                      cancel-text="取消"
                      @confirm="deleteCodeAttachment(originalData[1], record)">
                      <template slot="title"> 确认删除过程数据"{{ record.sampleAttachment.name }}"吗 </template>
                      <a-icon v-if="record.sampleAttachment.name && modalData.taskStatus !== '已完成' && record.batteryStatus === 'ongoing'" type="close" style="float: right;padding: 5px 0px 0px 5px" />
                    </a-popconfirm>
									</span>

                  <!-- 图片上传 -->
                  <span v-else-if="picOrVidMenu[item.dataIndex]">
                    <span v-if="record.batteryStatus !== 'ongoing' && !record.samplePicture[item.dataIndex].id ">
                      <a style="color: darkgrey;">上传</a>
                    </span>
                    <span v-else-if="!record.samplePicture[item.dataIndex].id && modalData.taskStatus !== '已完成'" style="display: flex;justify-content: center; align-items: center;">
                      <a-upload
                        name="file"
                        :headers="headers"
                        :data="uploadData"
                        :action="picOrVidPostUrl"
                        :before-upload="beforeUploadPicture"
                        :multiple="false"
                        :showUploadList="false"
                        @change="uploadPicture($event, item.dataIndex, index, originalData[1])"
                        accept=".jpg,.png,.gif">
                        <a>上传</a>
                      </a-upload>
                    </span>
                    <span v-else-if="record.samplePicture[item.dataIndex].uid">
                      <a-upload
                        list-type="picture-card"
                        class="avatar-uploader"
                        :disabled="modalData.taskStatus === '已完成' || record.batteryStatus !== 'ongoing'"
                        @change="deletePicture(item.dataIndex, index, originalData[1])"
                        :fileList="[record.samplePicture[item.dataIndex]]"
                        @preview="handlePreview">
                      </a-upload>
                    </span>
                    <span v-else>
                      <a style="color: green" @click="openFileOrDownload(record.samplePicture[item.dataIndex].id,record.samplePicture[item.dataIndex].name)">{{ record.samplePicture[item.dataIndex].name }}</a>
                        <a-popconfirm
                          placement="topRight"
                          ok-text="删除"
                          cancel-text="取消"
                          @confirm="deletePicture(item.dataIndex, index, originalData[1])">
                          <template slot="title"> 确认删除文件"{{ record.samplePicture[item.dataIndex].name }}"吗 </template>
                          <a-icon v-if="modalData.taskStatus !== '已完成'" type="close" style="float: right;padding: 5px 0px 0px 5px" />
                        </a-popconfirm>
                     </span>
                  </span>

                  <!-- 输入框上传 -->
                  <a-input
                    v-else
                    :id="`last-${item.dataIndex}-${index}`"
                    v-model="lastData[index][item.dataIndex]"
                    :disabled="(modalData.taskStatus === '已完成' ? true : false) || record.batteryStatus !== 'ongoing'"
                    @paste="copyFromExcel($event, lastColums, lastData, index, item.dataIndex)"
                    @keyup.enter="handleWrite(item.dataIndex, index,`last-${item.dataIndex}-${index + 1}`, originalData[1], lastData)"
                    @blur="value => handleInput(value.target._value, `last-${item.dataIndex}-${index}`, index, originalData[1], lastData)"
                  />

								</span>
              </a-table>
            </div>
          </div>
        </a-spin>
      </div>

    </div>
    <!-- 底部按钮 -->
    <template slot="footer">
      <div>
        <a-button :disabled="modalData.taskStatus === '已完成'" type="primary" @click="handleSaveTestData()">保存</a-button>
        <a-popconfirm
          placement="top"
          ok-text="确认"
          cancel-text="取消"
          @confirm="handleSubmit"
        >
          <template slot="title">
            <p>确认完成吗？</p>
          </template>
          <a-button v-if="modalData.taskStatus !== '已完成'" type="primary">完成</a-button>
        </a-popconfirm>
        <a-button @click="handleModelCancel">关闭</a-button>
      </div>
    </template>
    <step-data ref="stepData"></step-data>
    <div>
<!--      <a-modal-->
<!--        :maskClosable="false" title="条码启动天数输入" :visible="isShowDays" @ok="handleModalOk" @cancel="handleModalCancel">-->
<!--        <a-form :label-col="{ span: 11 }" :wrapper-col="{ span: 13 }">-->
<!--          <a-form-item label="启动天数">-->
<!--            <a-input-number v-model="startDay" :precision="0" :min="0" @keyup.enter="handleModalOk" />-->
<!--          </a-form-item>-->
<!--        </a-form>-->
<!--      </a-modal>-->

    </div>
    <!-- 测试数据选择弹窗 start  -->
    <div>
      <a-modal
        title="测试数据选择"
        width="90%"
        :height="300"
        :bodyStyle="{ padding: 0 }"
        :visible="mgVisible"
        style="padding: 0"
        :maskClosable="false"
        :centered="true"
        @cancel="handleCloseModal"
        destroyOnClose
      >
        <div class="child-table">
          <a-table
            :columns="mgColumns"
            :dataSource="mgData"
            class="mt10"
            bordered
            :rowKey="record => record.flowId"
            :pagination="false"
            :rowSelection="{
							type: 'radio',
							onSelect: selectTestData,
							getCheckboxProps: getCheckboxProps
						}"
          >
            <template slot="celltestcode" slot-scope="text, record, index, columns">
              <a @click="openStepData(record)" style="text-align: center">{{ text }}</a>
            </template>
          </a-table>
        </div>
        <template slot="footer" slot-scope="text, record">
          <a-button key="back" @click="handleCloseModal">
            关闭
          </a-button>
        </template>
      </a-modal>
    </div>
    <!-- 测试数据选择弹窗 end  -->
    <!-- 预览视频/图片  -->
    <a-drawer
      :bodyStyle="{ height: '100%' }"
      placement="right"
      :closable="false"
      width="70%"
      :visible="filePreviewVisible"
      @close="filePreviewVisible = false"
    >
      <iframe :src="iframeUrl" width="100%" height="100%"></iframe>
    </a-drawer>
    <div>
      <a-modal :visible="previewVisible" :footer="null" @cancel="handlePreviewCancel">
        <img alt="example" style="width: 100%" :src="previewImage"/>
      </a-modal>
    </div>
    <div>
      <!-- 视频的测试编码选择  -->
      <a-modal
        title="测试视频"
        width="30%"
        :height="200"
        :bodyStyle="{ padding: 0 }"
        :visible="chooseCodeVideoVisible"
        style="padding: 0"
        :maskClosable="false"
        :centered="true"
        @cancel="() => (chooseCodeVideoVisible = false)"
      >
        <div class="child-table">
          <p style="margin-left: 8px;color: red">提示：已有测试视频的电芯再次勾选并上传成功后，原有的测试视频会被覆盖</p>
          <a-table
            :columns="chooseCodeColumns"
            :dataSource="lastData.filter(item => item.batteryStatus === 'ongoing')"
            class="mt10"
            bordered
            :rowKey="record => record.cellTestCode"
            :rowSelection="{ selectedRowKeys: selectedRowCodeVideoKeys, onChange: onSelectCodeVideoChange }"
            :pagination="false"
          >
          </a-table>
        </div>
        <template slot="footer" slot-scope="text, record">
            <a-button :style="{marginRight: selectedRowCodeVideoKeys.length === 0 ? '0px' : '8px'}" @click="() => (chooseCodeVideoVisible = false)">
              取消
            </a-button>
            <a-button v-if="selectedRowCodeVideoKeys.length === 0" type="primary" @click="chooseAtLeastOneCode">确定</a-button>
            <a-upload
              v-else
              name="file"
              :headers="headers"
              :customRequest="handleUpload"
              :data="uploadData"
              :action="picOrVidPostUrl"
              :before-upload="beforeUploadVideo"
              :multiple="false"
              :disabled="videoLoading || modalData.taskStatus === '已完成'"
              :showUploadList="false"
              @change="uploadCodeVideo($event, originalData[1])"
              accept=".mp4,.avi,.wmv,.mov">
              <a-spin :spinning="videoLoading">
                <a-button type="primary">确定</a-button>
              </a-spin>
            </a-upload>
        </template>
      </a-modal>
      <!-- 附件的测试编码选择  -->
      <a-modal
        title="过程数据"
        width="30%"
        :height="200"
        :bodyStyle="{ padding: 0 }"
        :visible="chooseCodeAttachVisible"
        style="padding: 0"
        :maskClosable="false"
        :centered="true"
        @cancel="() => (chooseCodeAttachVisible = false)"
      >
        <div class="child-table">
          <p style="margin-left: 8px;color: red">提示：已有过程数据的电芯再次勾选并上传成功后，原有的过程数据会被覆盖</p>
          <a-table
            :columns="chooseCodeColumns"
            :dataSource="lastData.filter(item => item.batteryStatus === 'ongoing')"
            class="mt10"
            bordered
            :rowKey="record => record.cellTestCode"
            :rowSelection="{ selectedRowKeys: selectedRowCodeAttachKeys, onChange: onSelectCodeAttachChange }"
            :pagination="false"
          >
          </a-table>
        </div>
        <template slot="footer" slot-scope="text, record">
            <a-button :style="{marginRight: selectedRowCodeAttachKeys.length === 0 ? '0px' : '8px'}" @click="() => (chooseCodeAttachVisible = false)">
              取消
            </a-button>
            <a-button v-if="selectedRowCodeAttachKeys.length === 0" type="primary" @click="chooseAtLeastOneCode">确定</a-button>
            <a-upload
              v-else
              name="file"
              :headers="headers"
              :customRequest="$event => handleUploadOfAttach($event, originalData[1], 1)"
              :data="uploadData"
              :action="picOrVidPostUrl"
              :multiple="false"
              :disabled="attachLoading || modalData.taskStatus === '已完成'"
              :showUploadList="false"
              @change="uploadCodeAttachment($event, originalData[1])">
              <a-spin :spinning="attachLoading">
                <a-button type="primary">确定</a-button>
              </a-spin>
            </a-upload>
        </template>
      </a-modal>
    </div>
  </a-modal>

</template>

<script>
import moment from "moment"
import { STable } from "@/components"
import {
  aliasCopyOfAq,
  exportModelOfSafetyTest,
  finishCalLifeTodoTask, finishSafetyTestTodoTask,
  getSafetyTestByTask, handleSaveSafetyTestData, updateBatteryStatusOfTc,
  updatePicOrVidOfAq,
  updateSafetyTestData,
  updateTestProDetail,
} from "@/api/modular/system/testProgressManager"

import { mixin } from "../mixin/index"

import { downloadfile1 } from "@/utils/util"
import { formatDate } from "@/utils/format";
import { tLimsTestdataScheduleList } from "@/api/modular/system/limsManager";
import { getMinioPreviewUrl } from "@/api/modular/system/fileManage";

export default {
  components: {
    STable
  },
  data() {
    return {
      chooseCodeColumns: [
        {
          title: "序号",
          dataIndex: "index",
          align: "center",
          customRender: (text, record, index) => `${index + 1}`
        },
        {
          title: "测试编码",
          dataIndex: "cellTestCode",
          align: "center",
          scopedSlots: {
            customRender: "cellTestCode"
          }
        }
      ],
      chooseCodeVideoVisible: false,
      chooseCodeAttachVisible: false,
      selectedRowCodeVideoKeys: [],
      selectedRowCodeAttachKeys: [],
      previewImage: '',
      previewVisible: false,
      isNewestBeforeAfterFlag: true,
    }
  },
  mixins: [mixin],
  created() {
    this.loadSafetyTestByTask(true)
  },
  methods: {
    loadSafetyTestByTask(init = false) {
      this.modalLoading = true
      getSafetyTestByTask(this.modalData)
        .then(res => {
          if (!res.success) return this.$message.error("错误提示：" + res.message)
          this.originalData = res.data
          this.firstData = JSON.parse(this.originalData[0].testData)
          this.lastData = JSON.parse(this.originalData[1].testData)
          for (let i = 0; i < this.lastData.length; i++) {
            if (!this.lastData[i].sampleVideo && !this.lastData[i].sampleAttachment) {
              this.isNewestBeforeAfterFlag = false
            }
          }
          if (!this.isNewestBeforeAfterFlag) {
            if (this.originalData[1].attachment) {
              this.attachmentList = JSON.parse(this.originalData[1].attachment)
            } else {
              this.attachmentList = []
            }
          }
          if (this.firstData) {
            // 设置大小中检是否被点击
            this.firstData.forEach(v => {
              // 图片预览增加token
              if (v.samplePicture) {
                Object.keys(v.samplePicture).forEach(function(key) {
                  let authString = "&Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************.P1JWgtRVk1sTPPLiCgZNuleYyPZRf2ooByC_mmu9scs6SVbpJHgSsKd8AtscjDwg3Fw7D4QN31vgtA5jeedj3g"
                  if (v.samplePicture[key].thumbUrl && v.samplePicture[key].thumbUrl.indexOf(authString) === -1) {
                    v.samplePicture[key].thumbUrl += authString
                  }
                });
              }
              // 有中检，且没有选择数据
              if (v.isMiddleClick === null || v.isMiddleClick === undefined || v.isMiddleClick === '') {
                v.isMiddleClick = (v.middleCheck !== "normal" && v.checkData) || (this.modalData.taskStatus === "已完成")
                  ? true
                  : false
                //方便后续校验
                if (v.middleCheck === "normal") v.isMiddleClick = true
              }
            })
          }
          if (this.lastData) {
            this.lastData.forEach(v => {
              // 图片预览增加token
              if (v.samplePicture) {
                Object.keys(v.samplePicture).forEach(function(key) {
                  let authString = "&Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************.P1JWgtRVk1sTPPLiCgZNuleYyPZRf2ooByC_mmu9scs6SVbpJHgSsKd8AtscjDwg3Fw7D4QN31vgtA5jeedj3g"
                  if (v.samplePicture[key].thumbUrl && v.samplePicture[key].thumbUrl.indexOf(authString) === -1) {
                    v.samplePicture[key].thumbUrl += authString
                  }
                });
              }
              if (v.isMiddleClick === null || v.isMiddleClick === undefined || v.isMiddleClick === '') {
                v.isMiddleClick = (v.middleCheck !== "normal" && v.checkData) || (this.modalData.taskStatus === "已完成")
                  ? true
                  : false
                if (v.middleCheck === "normal") v.isMiddleClick = true
              }
            })
          }
          if (this.firstData) {
            this.firstColums = this.handleColums(this.firstData)[0]
            this.firstList = this.handleColums(this.firstData)[1]
          }
          if (this.lastData) {
            this.lastColums = this.handleColums(this.lastData)[0]
            this.lastList = this.handleColums(this.lastData)[1]
          }
        })
        .finally(() => {
          this.modalLoading = false
          if (init) {
            if (this.originalData[0].middleCheck !== 'normal' && this.firstData) {
              this._handleMiddleCheck(this.originalData[0], this.firstData)
            }
            if (this.originalData[1].middleCheck !== 'normal' && this.lastData) {
              this._handleMiddleCheck(this.originalData[1], this.lastData)
            }
            for (let i = 0; i < this.originalData.length; i++) {
              this.uploadingProgressList.push({
                uploadStatusOfAttach: 'done',
                percentOfAttach: 0,
              })
            }
          }
        })
    },

    // 处理表头新
    handleColums(value) {
      let rejectMsgFlag = value.some(item => item.hasOwnProperty('rejectMsg'));
      let temColuns = []
      const temList = []

      for (let i in value[0]) {
        const temObj = {
          title: this.tableNameMenu[i],
          dataIndex: i,
          width: "100px",
          align: "center",
          scopedSlots: {
            customRender: i
          }
        }

        const childrenObj = {
          title: i.replaceAll(/[^0-9]/g, ""),
          dataIndex: i,
          width: "100px",
          align: "center",
          scopedSlots: {
            customRender: i
          }
        }

        const childrenObj1 = {
          title: this.tableNameMenu[i.replaceAll(/[0-9]/g, "")] + i.replaceAll(/[^0-9]/g, ""),
          dataIndex: i,
          width: "100px",
          align: "center",
          scopedSlots: {
            customRender: i
          }
        }

        const temObj1 = {
          title: this.tableNameMenu[i.replaceAll(/[0-9]/g, "")],
          dataIndex: i.replaceAll(/[0-9]/g, ""),
          width: "100px",
          align: "center",
          children: [childrenObj]
        }

        if (
          (i === "middleCheck" && value[0][i] === "normal") ||
          i === "heightType" ||
          i === "isMiddleClick" ||
          i === "checkData" ||
          i === "timeOfFillInnerres" ||
          i === "timeOfFillInnerres2" ||
          i === "sampleVideo" ||
          i === "sampleAttachment" ||
          i === "samplePicture" ||
          i === "rejectMsg" ||
          i === "safetyTestReviewId"
        )
          continue


        if (i.match(/\d/g)) {
          const temIndex = temColuns.findIndex(v => v.dataIndex === i.replaceAll(/[0-9]/g, ""))
          if (temIndex === -1) {
            temColuns.push(temObj1)
            temList.push(childrenObj1)
          } else {
            temColuns[temIndex].children.push(childrenObj)
            temList.push(childrenObj1)
          }
        } else {
          temColuns.push(temObj)
          temList.push(temObj)
        }
      }

      const chilList = []
      const temColuns1 = []
      // 划分尺寸表头
      temColuns.forEach(v => {
        if (v.dataIndex === "timeOfFillInnerres" || v.dataIndex === "timeOfFillInnerres2") {
          return
        }
        if (
          v.dataIndex === "cellTestCode" ||
          v.dataIndex === "alias" ||
          v.dataIndex === "middleCheck" ||
          v.dataIndex === "batteryStatus" ||
          v.dataIndex === "beforeVoltage" ||
          v.dataIndex === "beforeInnerres" ||
          v.dataIndex === "afterVoltage" ||
          v.dataIndex === "afterInnerres" ||
          v.dataIndex === "volume" ||
          v.dataIndex === "weight" ||
          v.dataIndex === "heightType" ||
          v.dataIndex === "isolateres"
        ) {
          if (v.dataIndex === "alias" || v.dataIndex === "isolateres" || v.dataIndex === "batteryStatus") {
            v.width = "100px"
          }
          if (v.dataIndex === "cellTestCode") {
            v.width = "175px"
          }
          return temColuns1.push(v)
        }
        chilList.push(v)
      })

      temColuns = temColuns1

      if (chilList.length !== 0) {
        temColuns.push({
          title: "尺寸/mm",
          dataIndex: "dimension",
          align: "center",
          children: chilList
        })
      }

      // 增加样品视频和附件列,并且排在样品照片列前面
      let needTestFields = Object.keys(value[0])
      if (needTestFields.findIndex(v => v === "sampleVideo") > 0) {
        let videoItem = {
          title: "测试视频",
          dataIndex: "sampleVideo",
          width: "100px",
          align: "center",
          scopedSlots: {
            customRender: "sampleVideo"
          }
        }
        temColuns.push(videoItem)
        temList.push(videoItem)
      }
      if (needTestFields.findIndex(v => v === "sampleAttachment") > 0) {
        let attachmentItem = {
          title: "过程数据",
          dataIndex: "sampleAttachment",
          width: "100px",
          align: "center",
          scopedSlots: {
            customRender: "sampleAttachment"
          }
        }
        temColuns.push(attachmentItem)
        temList.push(attachmentItem)
      }

      // 增加样品照片列
      const pictureChilList = []
      if (value[0].samplePicture) {
        for (let i in value[0].samplePicture) {
          const temObj = {
            title: this.picOrVidMenu[i],
            dataIndex: i,
            align: "center",
            width: "100px",
            scopedSlots: {
              customRender: i
            }
          }
          pictureChilList.push(temObj)
          temList.push(temObj)
        }
      }
      if (pictureChilList.length !== 0) {
        temColuns.push({
          title: "样品照片",
          dataIndex: "samplePicture",
          align: "center",
          children: pictureChilList
        })
      }

      // 获取【中检类型】列索引
      let insertIndexC = temColuns.findIndex(item => item.dataIndex === 'middleCheck');
      let insertIndexL = temList.findIndex(item => item.dataIndex === 'middleCheck');
      if (insertIndexC === -1 || insertIndexL === -1) {
        // 无【中检类型】列则获取【测试编码】列索引
        insertIndexC = temColuns.findIndex(item => item.dataIndex === 'cellTestCode');
        insertIndexL = temList.findIndex(item => item.dataIndex === 'cellTestCode');
      }
      if (insertIndexC !== -1 && insertIndexL !== -1) {
        // 获取【电芯状态】列
        let batteryTitleC = temColuns.filter(item => item.dataIndex === 'batteryStatus');
        let batteryTitleL = temList.filter(item => item.dataIndex === 'batteryStatus');
        // 移除【电芯状态】列
        let indexToRemoveC = temColuns.findIndex(item => item.dataIndex === 'batteryStatus');
        let indexToRemoveL = temList.findIndex(item => item.dataIndex === 'batteryStatus');
        if (indexToRemoveC !== -1) {
          temColuns.splice(indexToRemoveC, 1);
        }
        if (indexToRemoveL !== -1) {
          temList.splice(indexToRemoveL, 1);
        }
        // 将【电芯状态】列添加到【中检类型】/【测试编码】列后面
        if (batteryTitleC.length > 0 && batteryTitleL.length > 0) {
          temColuns.splice(insertIndexC + 1, 0, batteryTitleC[0]);
          temList.splice(insertIndexL + 1, 0, batteryTitleL[0]);
          // 在【电芯状态】列后添加【驳回信息】列
          if (rejectMsgFlag) {
            let rejectMsgItem = {
              title: "驳回信息",
              dataIndex: "rejectMsg",
              width: "100px",
              align: "center",
              scopedSlots: {
                customRender: "rejectMsg"
              }
            }
            temColuns.splice(insertIndexC + 2, 0, rejectMsgItem);
            temList.splice(insertIndexL + 2, 0, rejectMsgItem);
          }
        }
      }
      return [temColuns, temList]
    },

    handleUploadFile(info) {
      this.fileList = [...info.fileList]
      if (info.file.response.success) {
        this.loadSafetyTestByTask()
        this.$message.success(`${info.file.name} 数据导入成功`)
      } else {
        this.$message.error(`${info.file.name} 数据导入失败:` + info.file.response.message)
      }
      this.$forceUpdate()
    },

    handleDownload($event, stage, curSafetyTest) {
      exportModelOfSafetyTest({ safetyTestId: curSafetyTest.id }).then(res => {
        const fileName = `${this.modalData.folderNo}-${this.modalData.wtrName}-${this.modalData.testName}-${stage === 0 ? "测试前" : "测试后" }${curSafetyTest.middleCheck === "small"
          ? "-小中检"
          : curSafetyTest.middleCheck === "large"
            ? "-大中检"
            : curSafetyTest.middleCheck === "recharge" ? "-补电" : ""}.xlsx`
        if (res) {
          downloadfile1(res, fileName)
        }
      })
    },

    deleteAttachment(event, curSafetyTest, fileItem) {
      this.attachmentList = this.attachmentList.filter(o => o.id !== fileItem.id)
      let update = {}
      update.id = curSafetyTest.id
      update.attachment = JSON.stringify(this.attachmentList)
      updatePicOrVidOfAq(update, 'delete', 'attachment', -1).then(res => {
        if (res.success) {
          this.loadSafetyTestByTask()
          this.$message.success("删除成功")
        } else {
          this.$message.error("删除失败：" + res.message)
        }
      })
    },

    uploadAttachment(info, curSafetyTest) {
      this.attachLoading = true
      if (info.file.status === "done") {
        let file = info.file
        let update = {}
        this.attachmentList.push({
          id: file.response.data,
          name: file.name,
          time: formatDate(new Date(), true)
        })
        update.id = curSafetyTest.id
        update.attachment = JSON.stringify(this.attachmentList)
        updatePicOrVidOfAq(update, 'add', 'attachment', -1).then(res => {
          if (res.success) {
            this.loadSafetyTestByTask()
            this.$message.success(`${info.file.name} 上传成功`)
          } else {
            this.$message.error("上传失败：" + res.message)
          }
          setTimeout(() => {
            this.attachLoading = false
          },500)
        })
      } else if (info.file.status === "error") {
        this.attachLoading = false
        this.$message.error(`${info.file.name} 上传失败`)
      } else {
        this.attachLoading = false
      }
    },

    uploadCodeAttachment(info, curSafetyTest) {
      this.chooseCodeAttachVisible = false
      this.attachLoading = true
      if (info.file.status === "done") {
        let file = info.file
        let update = {}
        let uploadAttachment = {
          id: file.response.data,
          name: file.name,
          time: formatDate(new Date(), true)
        }
        update.id = curSafetyTest.id
        update.attachment = JSON.stringify(uploadAttachment)
        update.cellTestCodes = this.selectedRowCodeAttachKeys.join(',')
        updatePicOrVidOfAq(update, 'add', 'newestAttachment', -1).then(res => {
          if (res.success) {
            this.selectedRowCodeAttachKeys = []
            this.loadSafetyTestByTask()
            this.$message.success(`${info.file.name} 上传成功`)
          } else {
            this.$message.error("上传失败：" + res.message)
          }
          setTimeout(() => {
            this.attachLoading = false
          },500)
        })
      } else if (info.file.status === "error") {
        this.attachLoading = false
        this.$message.error(`${info.file.name} 上传失败`)
      } else {
        this.attachLoading = false
      }
    },

    uploadCodeVideo(info, curSafetyTest) {
      this.chooseCodeVideoVisible = false
      this.videoLoading = true
      if (info.file.status === "done") {
        let file = info.file
        let update = {}
        update.id = curSafetyTest.id
        update.videoId = file.response.data
        update.videoName = file.name
        update.cellTestCodes = this.selectedRowCodeVideoKeys.join(',')
        updatePicOrVidOfAq(update, 'add', 'newestVideo', -1).then(res => {
          if (res.success) {
            this.selectedRowCodeVideoKeys = []
            this.loadSafetyTestByTask()
            this.$message.success(`${info.file.name} 上传成功`)
          } else {
            this.$message.error("上传失败：" + res.message)
          }
          setTimeout(() => {
            this.videoLoading = false
          },500)
        })
      } else if (info.file.status === "error") {
        this.videoLoading = false
        this.$message.error(`${info.file.name} 上传失败`)
      } else {
        this.videoLoading = false
      }
    },

    uploadVideo(info, curSafetyTest) {
      this.videoLoading = true
      if (info.file.status === "done") {
        let file = info.file
        let update = {}
        update.id = curSafetyTest.id
        update.videoId = file.response.data
        update.videoName = file.name
        updatePicOrVidOfAq(update, 'add', 'video', -1).then(res => {
          if (res.success) {
            curSafetyTest.videoId = file.response.data
            curSafetyTest.videoName = file.name
            this.$message.success(`${info.file.name} 上传成功`)
          } else {
            this.$message.error("上传失败：" + res.message)
          }
          setTimeout(() => {
            this.videoLoading = false
          },500)
        })
      } else if (info.file.status === "error") {
        this.videoLoading = false
        this.$message.error(`${info.file.name} 上传失败`)
      } else {
        this.videoLoading = false
      }
    },

    deleteVideo(event, curSafetyTest) {
      let update = {}
      update.id = curSafetyTest.id
      updatePicOrVidOfAq(update, 'delete', 'video', -1).then(res => {
        if (res.success) {
          curSafetyTest.videoId = null
          curSafetyTest.videoName = null
          this.$message.success("删除成功")
        } else {
          this.$message.error("删除失败：" + res.message)
        }
      })
    },

    deleteCodeVideo(curSafetyTest, battery) {
      let update = {}
      update.id = curSafetyTest.id
      update.cellTestCodes = battery.cellTestCode
      updatePicOrVidOfAq(update, 'delete', 'newestVideo', -1).then(res => {
        if (res.success) {
          this.loadSafetyTestByTask()
          this.$message.success("删除成功")
        } else {
          this.$message.error("删除失败：" + res.message)
        }
      })
    },

    deleteCodeAttachment(curSafetyTest, battery) {
      let update = {}
      update.id = curSafetyTest.id
      update.cellTestCodes = battery.cellTestCode
      updatePicOrVidOfAq(update, 'delete', 'newestAttachment', -1).then(res => {
        if (res.success) {
          this.loadSafetyTestByTask()
          this.$message.success("删除成功")
        } else {
          this.$message.error("删除失败：" + res.message)
        }
      })
    },

    async handleWrite(dataIndex, row, focusId, curSafetyTest, testData){
      if(!this.serialObj.serialPort || !this.serialObj.serialReader || !this.serialObj.serialWriter || !this.serialObj.serialPortOpen)  return

      // 如果焦点在的位置是内阻、电压
      if(dataIndex == 'beforeInnerres' || dataIndex == 'beforeVoltage' || dataIndex == 'afterInnerres' || dataIndex == 'afterVoltage'){
        const serialValue = await this.writeToSerial()

        if(dataIndex == 'beforeInnerres' || dataIndex == 'beforeVoltage'){
          testData[row].beforeInnerres = Number(serialValue.split(',')[0] * 1000)//由于之前工作的excel模板上的单位为欧，所以测试设备输出的数据，自动除于1000，故此处需要还原  1mΩ === 0.001Ω
          testData[row].beforeVoltage = Number(serialValue.split(',')[1] * 1000)
          testData[row].timeOfFillInnerres = formatDate(new Date(), true)
        }else{
          testData[row].afterInnerres = Number(serialValue.split(',')[0] * 1000)  //由于之前工作的excel模板上的单位为欧，所以测试设备输出的数据，自动除于1000，故此处需要还原  1mΩ === 0.001Ω
          testData[row].afterVoltage = Number(serialValue.split(',')[1] * 1000)
          testData[row].timeOfFillInnerres2 = formatDate(new Date(), true)
        }
        const params = {
          id: curSafetyTest.id,
          testData: JSON.stringify(testData)
        }
        this.updateSafetyTestData(params)
      }

      if(row !== testData.length - 1){
        document.getElementById(focusId).focus();
      }
    },

    handleInput(value, id, row, curSafetyTest, testData) {
      // value:填写的值，id：对应的id，row：那一行数据发生修改

      if (value !== "" && document.getElementById(id).style.backgroundColor === "rgb(255, 233, 237)") {
        document.getElementById(id).style.backgroundColor = "transparent"
      }
      if (id.indexOf('before') !== -1 && value) {
        testData[row].timeOfFillInnerres = formatDate(new Date(), true)
      }
      if (id.indexOf('after') !== -1 && value) {
        testData[row].timeOfFillInnerres2 = formatDate(new Date(), true)
      }
      let property =  Object.getOwnPropertyNames(testData[row])
      let testDataColumn = ['beforeVoltage','afterVoltage','beforeInnerres','afterInnerres']
      if (property.findIndex(item => testDataColumn.indexOf(item) > -1) === -1) { //如果没有电压内阻和中检后电压内阻（即只有尺寸重量）
        if (property.findIndex(item => item === 'weight') === -1) { // 如果测试内容只有尺寸，timeOfFillInnerres取填写尺寸的时间
          if (value) {
            testData[row].timeOfFillInnerres = formatDate(new Date(), true)
          }
        } else { // 如果测试内容为①只有重量和尺寸②只有重量，timeOfFillInnerres取填写重量的时间
          if (id.indexOf('weight') !== -1 && value) {
            testData[row].timeOfFillInnerres = formatDate(new Date(), true)
          }
        }
      }
      const params = {
        id: curSafetyTest.id,
        testData: JSON.stringify(testData)
      }
      this.updateSafetyTestData(params)
    },

    updateSafetyTestData(params) {
      updateSafetyTestData(params).then(res => {
        if (!res.success) return this.$message.error("错误提示：" + res.message)
      })
    },

    handleSaveSafetyTestData(params) {
      return new Promise((resolve, reject) => {
        handleSaveSafetyTestData(params).then(res => {
          if (res.success) {
            resolve([true, "保存成功！"])
          } else {
            resolve([false, "错误提示：" + res.message])
          }
        })
      });
    },

    async handleSaveTestData () {
      const firstParams = {
        id: this.originalData[0].id,
        testData: this.firstData ? JSON.stringify(this.firstData) : ""
      }
      let result1 = await this.handleSaveSafetyTestData(firstParams)
      const lastParams = {
        id: this.originalData[1].id,
        testData: this.lastData ? JSON.stringify(this.lastData) : ""
      }
      let result2 = await this.handleSaveSafetyTestData(lastParams)
      if (result1[0] && result2[0]) {
        return this.$message.success("保存成功！")
      } else if (result1[0]) {
        return this.$message.error("【测试后】" + result2[1])
      } else {
        return this.$message.error("【测试前】" + result1[1])
      }
    },

    async changeBatteryStatus(curSafetyTest, changeBattery) {
      // 选择电芯状态为【状态正常-测试完成】时需校验数据完整性
      if (changeBattery.batteryStatus === "testDone") {
        let stageAndTestCode = (curSafetyTest.stage === "0" ? "测试前" : "测试后") + changeBattery.cellTestCode + "："
        let curTestData = curSafetyTest.stage === "0" ?
          this.firstData.filter(item => item.cellTestCode === changeBattery.cellTestCode) :
          this.lastData.filter(item => item.cellTestCode === changeBattery.cellTestCode)
        if (!(await this._handleIsNull(curTestData))[0] || curTestData.findIndex(v => !v.isMiddleClick) !== -1) {
          this.loadSafetyTestByTask()
          return this.$warning({
            content: stageAndTestCode + "请将数据填写完整"
          })
        } else {
          if (!(await this._handleIsNull(curTestData))[1]) {
            this.loadSafetyTestByTask()
            return this.$warning({
              content: stageAndTestCode + "请将尺寸数据填写完整"
            })
          }
        }
        let noFillPicture = this.validPictureAndVideo(curSafetyTest, curTestData)
        if (noFillPicture) {
          this.loadSafetyTestByTask()
          return this.$warning({
            content: stageAndTestCode + "每个电芯至少需要上传一张照片"
          })
        }
        const regex = /^-?\d+(\.\d+)?$/
        let errorDigitFormatIndex = curTestData.findIndex(v => v.weight && !regex.test(v.weight))
        if (errorDigitFormatIndex !== -1) {
          this.loadSafetyTestByTask()
          return this.$warning({
            content: stageAndTestCode + "请确认" + curTestData[errorDigitFormatIndex].cellTestCode + "重量的数据格式"
          })
        }
      }
      const params = {
        id: curSafetyTest.id,
        cellTestCode: changeBattery.cellTestCode,
        batteryStatus: changeBattery.batteryStatus,
        todoTaskId: this.modalData.id
      }
      updateBatteryStatusOfTc(params).then(res => {
        if (res.success) {
          this.loadSafetyTestByTask()
        } else {
          this.loadSafetyTestByTask()
          return this.$error({ content: res.message })
        }
      })
    },

    actualDateChange (date, dateString, curSafetyTest, type) {
      const params = {
        id: curSafetyTest.id,
        actualInDate: null,
        actualOutDate: null
      }
      if (type === 'in') {
        params.actualInDate = dateString
      } else {
        params.actualOutDate = dateString
      }
      this.updateSafetyTestData(params)
    },

    uploadPicture(info, field, index, curSafetyTest) {
      if (info.file.status === "done") {
        let file = info.file
        let update = {}
        update.id = curSafetyTest.id
        update.pictureId = file.response.data
        update.pictureName = file.name
        this.modalLoading = true
        updatePicOrVidOfAq(update, 'add', field, index).then(res => {
          if (res.success) {
            this.loadSafetyTestByTask()
            this.$message.success(`${info.file.name} 上传成功`)
          } else {
            this.$message.error("上传失败：" + res.message)
          }
          setTimeout(() => {
            this.modalLoading = false
          },500)
        })
      } else if (info.file.status === "error") {
        this.$message.error(`${info.file.name} 上传失败`)
      }
    },

    deletePicture(field, index, curSafetyTest) {
      let update = {}
      update.id = curSafetyTest.id
      this.modalLoading = true
      updatePicOrVidOfAq(update, 'delete', field, index).then(res => {
        if (res.success) {
          this.loadSafetyTestByTask()
          this.$message.success("删除成功")
        } else {
          this.$message.error("删除失败：" + res.message)
        }
        setTimeout(() => {
          this.modalLoading = false
        },500)
      })
    },

    handlePreviewCancel() {
      this.previewVisible = false;
    },

    async handlePreview(file) {
      getMinioPreviewUrl(file.id).then(res => {
        if (res.data) {
          this.previewImage = res.data.replace("http://10.100.1.99:9000/", "/minioDownload/")
          setTimeout(() => {
            this.previewVisible = true
          }, 100)
        } else {
          this.$message.error("服务器错误，请联系管理员！")
        }
      })
    },

    // 点击中检按钮,查找对应的数据
    chooseMgData(record, dataIndex, curSafetyTest, testData) {
      this.currentSafetyTest = curSafetyTest
      this.currentTestData = testData
      if (document.getElementById(dataIndex).style.backgroundColor === "rgb(255, 233, 237)") {
        document.getElementById(dataIndex).style.backgroundColor = "transparent"
      }

      // 如果已经有checkData的数据，获取flowId
      if (record.checkData) this.flowId = JSON.parse(record.checkData).flowId

      // 测试数据
      // tLimsTestdataScheduleList({ celltestcode: "04QCE34221101HD152126077-202303090041-0001", alias: "日历寿命1" })
      //正式情况
      tLimsTestdataScheduleList({ celltestcode: record.cellTestCode || String(new Date()), alias: record.alias })
        .then(res => {
          if (res.success) {
            this.mgData = res.data
            if (res.data.length === 0 || res.data[0].flowId === '' || res.data[0].flowId === null) {
              record.isMiddleClick = true
              const params = {
                id: curSafetyTest.id,
                testData: JSON.stringify(testData)
              }
              this.updateSafetyTestData(params)
            }
          } else {
            this.$message.error("查询失败：" + res.message)
          }
        })
        .finally(() => {
          this.mgVisible = true
        })
    },

    // 中检弹窗 选中数据
    selectTestData(record) {
      // 测试数据
      // this.currentTestData[
      //   this.currentTestData.findIndex(v => v.cellTestCode === "3-202410230002-0003")
      // ].checkData = JSON.stringify(record)
      // this.currentTestData[
      //   this.currentTestData.findIndex(v => v.cellTestCode === "3-202410230002-0003")
      // ].isMiddleClick = true

      //正式情况
      this.currentTestData[
        this.currentTestData.findIndex(v => v.cellTestCode === record.celltestcode)
        ].checkData = JSON.stringify(record)
      this.currentTestData[
        this.currentTestData.findIndex(v => v.cellTestCode === record.celltestcode)
        ].isMiddleClick = true
      const params = {
        id: this.currentSafetyTest.id,
        testData: JSON.stringify(this.currentTestData)
      }
      this.updateSafetyTestData(params)
    },

    // 处理选中大小中检、补电
    async _handleMiddleCheck(curSafetyTest, testData) {
      let currentStage = curSafetyTest.stage === "0" ? "测试前" : "测试后"
      if (testData.length === 0) return
      await testData.forEach(async v => {
        // 如果本来就选择了
        if (v.checkData || v.isMiddleClick) return
        await tLimsTestdataScheduleList({
          celltestcode: v.cellTestCode || String(new Date()),
          alias: v.alias
        }).then(res => {
          console.log(v)

          // 没有数据，默认选中
          if (res.data.length === 0) {
            v.isMiddleClick = true
          } else {
            // 没有选择的，就把存储天数相同的第一条赋给(父层级)
            const have = res.data.filter(filterItem => filterItem.dataPath.indexOf(currentStage) !== -1)
            if (have.length > 0) {
              // 只有一条的时候，默认勾选上,多条需手动匹配
              v.isMiddleClick = have.length == 1 ? true : false
              if (have.length == 1) v.checkData = JSON.stringify(have[0])
              //匹配层级数据
            } else {
              console.log(res.data)

              // 是否有多条的数据的情况，多条数据的情况，需要工程师自动勾选
              const isHave = false

              // 找父层级底下的子层级
              for (let i = 0; i < res.data.length; i++) {
                const da = res.data[i]
                console.log(da)
                if (da.children && da.children.length > 0) {
                  const haveChild = da.children.filter(child => child.dataPath.indexOf(currentStage) !== -1)

                  // 多条的情况，需要工程师自动勾选
                  if (haveChild.length > 1) {
                    console.log('需自动匹配')
                    isHave = true
                    break
                  }

                  // 只有一条的时候，默认勾选上
                  if (haveChild.length === 1) {
                    console.log("匹配上了")
                    console.log(haveChild)
                    v.isMiddleClick = true
                    v.checkData = JSON.stringify(haveChild[0])
                    break
                  }
                }
              }

              // 所有都找完后，都没有就自动勾选上
              if (!isHave && !v.isMiddleClick) {
                console.log('都没匹配上')
                v.isMiddleClick = true
              }

            }
          }

          const params = {
            id: curSafetyTest.id,
            testData: JSON.stringify(testData)
          }
          this.updateSafetyTestData(params)
        })
      })
    },

    aliasCopy(curSafetyTest) {
      aliasCopyOfAq({ ordTaskId: this.modalData.ordTaskId, safetyTestId: curSafetyTest.id }).then(res => {
        this.handleCopy(res.data)
      })
    },

    async handleSubmit() {
      // 校验
      for (let i = 0; i < this.originalData.length; i++) {
        let curTestData = []
        let firstOrLast = ""
        let stage = "第" + i + "阶段："
        if (this.originalData[i].testType === "before_after") {
          stage = this.originalData[i].stage === "0" ? "测试前：" : "测试后："
          firstOrLast = this.originalData[i].stage === "0" ? "first" : "last"
          curTestData = this.originalData[i].stage === "0" ? this.firstData : this.lastData
          // 测试前:如果没有检测内容(20250716：现在所有待办都有检测内容，正常情况不会走这个逻辑)，则默认进行中
          // 测试后:如果没有检测内容(20250716：现在所有待办都有检测内容，正常情况不会走这个逻辑)，测试前无测试数据则测试为进行中，否则需根据测试数据的电芯状态判断是进行中还是停止
          let ongoingFlag = true
          if (firstOrLast === "last") {
            if (this.firstData) {
              if (this.firstData.findIndex(v => v.batteryStatus === 'ongoing' || v.batteryStatus === 'testDone') === -1) {
                ongoingFlag = false
              }
            }
          }
          let noDataAndOngoingFlag = !curTestData && ongoingFlag
          let haveOngoing = noDataAndOngoingFlag ||
            (curTestData && curTestData.findIndex(v => v.batteryStatus === 'ongoing' || v.batteryStatus === 'testDone') !== -1)
          if (stage.indexOf("测试前") !== -1) {
            if (!this.originalData[i].actualInDate && haveOngoing) {
              return this.$message.warning(stage + "实际进箱时间未填写!")
            }
          } else {
            if (!this.originalData[i].actualOutDate && haveOngoing) {
              return this.$message.warning(stage + "实际出箱时间未填写!")
            }
          }

          if (curTestData) {
            if (!(await this._handleIsNull(curTestData))[0] ||
              curTestData.findIndex(v => !v.isMiddleClick && (v.batteryStatus === 'ongoing' || v.batteryStatus === 'testDone')) !== -1) {
              this._handleSetBGC(curTestData, firstOrLast)
              return this.$warning({
                content: stage + "请将数据完整填写再点击完成"
              })
            } else {
              if (!(await this._handleIsNull(curTestData))[1]) {
                this._handleSetBGC(curTestData, firstOrLast)
                return this.$warning({
                  content: stage + "请先将尺寸数据填写完整再点击完成"
                })
              }
            }
          }

          if (this.isNewestBeforeAfterFlag) {
            if (this.originalData[i].videoFlag === '1' && haveOngoing && firstOrLast === "last") {
              if (this.lastData.findIndex(item => (item.batteryStatus === 'ongoing' || item.batteryStatus === 'testDone') && item.sampleVideo &&
                item.sampleVideo.id && item.sampleVideo.name && item.sampleVideo.time) === -1) {
                return this.$warning({
                  content: stage + "电芯状态为【状态正常-测试完成】的请至少上传一个测试视频"
                })
              }
            }
            if (this.originalData[i].attachmentFlag === '1' && haveOngoing && firstOrLast === "last") {
              if (this.lastData.findIndex(item => (item.batteryStatus === 'ongoing' || item.batteryStatus === 'testDone') && item.sampleAttachment &&
                item.sampleAttachment.id && item.sampleAttachment.name && item.sampleAttachment.time) === -1) {
                return this.$warning({
                  content: stage + "电芯状态为【状态正常-测试完成】的请至少上传一个过程数据"
                })
              }
            }
          } else {
            if (this.originalData[i].videoFlag === '1' && (!this.originalData[i].videoId || !this.originalData[i].videoName) && haveOngoing) {
              return this.$warning({
                content: stage + "测试视频未上传"
              })
            }
            if (this.originalData[i].attachmentFlag === '1' && haveOngoing) {
              if (!this.originalData[i].attachment ||
                (this.originalData[i].attachment && (JSON.parse(this.originalData[i].attachment)).length === 0)) {
                return this.$warning({
                  content: stage + "过程数据未上传"
                })
              }
            }
          }
          let noFillPicture = this.validPictureAndVideo(this.originalData[i], curTestData)
          if (noFillPicture) {
            return this.$warning({
              content: stage + "每个电芯至少需要上传一张照片"
            })
          }
          const regex = /^-?\d+(\.\d+)?$/
          let errorDigitFormatIndex = curTestData.findIndex(v => v.weight && !regex.test(v.weight))
          if (errorDigitFormatIndex !== -1) {
            return this.$warning({
              content: stage + "请确认" + curTestData[errorDigitFormatIndex].cellTestCode + "重量的数据格式"
            })
          }
          // 新增【状态正常-测试完成】后，提交待办需校验电芯状态不能等于进行中
          if(curTestData.findIndex(v => v.batteryStatus && v.batteryStatus === 'ongoing') !== -1) {
            return this.$warning({
              content: stage + "电芯状态不能等于进行中"
            })
          }
        }
      }

      finishSafetyTestTodoTask(this.modalData).then(res => {
        if (!res.success) {
          this.$message.error("错误提示：" + res.message)
        } else {
          this.$message.success("完成成功")
          this.$emit("submit")
        }
      })
    },

    chooseAtLeastOneCode() {
      return this.$message.warning("请至少选择一个电芯")
    },
    onSelectCodeVideoChange(selectedRowKeys, selectedRows) {
      this.selectedRowCodeVideoKeys = selectedRowKeys
    },
    onSelectCodeAttachChange(selectedRowKeys, selectedRows) {
      this.selectedRowCodeAttachKeys = selectedRowKeys
    },

    validPictureAndVideo(curSafetyTest, curTestData) {
      let noFillPicture = false
      if (curSafetyTest.pictureFlag === "1") {
        var allSamplePictureList = [];
        curTestData.forEach(item => {
          const keys = Object.keys(item.samplePicture);
          var samplePictureList = [];
          for (const index in keys) {
            let picture = item.samplePicture[keys[index]]
            if (item.batteryStatus === 'ongoing' || item.batteryStatus === 'testDone') {
              samplePictureList.push(picture);
            }
          }
          if (item.batteryStatus === 'ongoing' || item.batteryStatus === 'testDone') {
            allSamplePictureList.push(samplePictureList);
          }
        })
        for (const i in allSamplePictureList) {
          if (allSamplePictureList[i].findIndex(item => item.id && item.name) === -1) {
            noFillPicture = true
          }
        }
      }
      return noFillPicture
    },

  }
}
</script>

<style lang="less" scoped>
@import "../style/calendar.less";
/deep/.ant-table-pagination.ant-pagination {
  float: right;
  margin: 50px 0px 20px 0px;
}
/deep/.ant-upload-list-picture-card .ant-upload-list-item {
  width: 80px;
  height: 80px;
  margin: 0px 0px 0px 0px;
}
/deep/.ant-upload-list-picture-card-container {
  width: 80px;
  height: 70px;
  margin: 0px 0px 7px 0px;
  overflow-wrap: break-word;
}
/deep/.ant-upload-picture-card-wrapper {
  zoom: 1;
  display: ruby;
}
</style>
