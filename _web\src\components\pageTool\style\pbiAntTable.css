/* 表头的字号、字色;表内容的字号 */
.ant-table-thead {
  font-size: 13px;
  color: #000000D9;
}

.ant-table-tbody {
  color: #333;
}

/* 将高度设为28px */
.ant-table-middle>.ant-table-content>.ant-table-header>table>.ant-table-thead>tr>th,
.ant-table-middle>.ant-table-content>.ant-table-body>table>.ant-table-thead>tr>th,
.ant-table-middle>.ant-table-content>.ant-table-scroll>.ant-table-header>table>.ant-table-thead>tr>th,
.ant-table-middle>.ant-table-content>.ant-table-scroll>.ant-table-body>table>.ant-table-thead>tr>th,
.ant-table-middle>.ant-table-content>.ant-table-fixed-left>.ant-table-header>table>.ant-table-thead>tr>th,
.ant-table-middle>.ant-table-content>.ant-table-fixed-right>.ant-table-header>table>.ant-table-thead>tr>th,
.ant-table-middle>.ant-table-content>.ant-table-fixed-left>.ant-table-body-outer>.ant-table-body-inner>table>.ant-table-thead>tr>th,
.ant-table-middle>.ant-table-content>.ant-table-fixed-right>.ant-table-body-outer>.ant-table-body-inner>table>.ant-table-thead>tr>th,
.ant-table-middle>.ant-table-content>.ant-table-header>table>.ant-table-tbody>tr>td {
  padding: 3.92px 8px;
}

.ant-table-middle>.ant-table-content>.ant-table-body>table>.ant-table-tbody>tr>td,
.ant-table-middle>.ant-table-content>.ant-table-scroll>.ant-table-header>table>.ant-table-tbody>tr>td,
.ant-table-middle>.ant-table-content>.ant-table-scroll>.ant-table-body>table>.ant-table-tbody>tr>td,
.ant-table-middle>.ant-table-content>.ant-table-fixed-left>.ant-table-header>table>.ant-table-tbody>tr>td,
.ant-table-middle>.ant-table-content>.ant-table-fixed-right>.ant-table-header>table>.ant-table-tbody>tr>td,
.ant-table-middle>.ant-table-content>.ant-table-fixed-left>.ant-table-body-outer>.ant-table-body-inner>table>.ant-table-tbody>tr>td,
.ant-table-middle>.ant-table-content>.ant-table-fixed-right>.ant-table-body-outer>.ant-table-body-inner>table>.ant-table-tbody>tr>td {
  padding: 4.17px 8px;
}

/* 固定表头 */
.ant-table-thead{
  position: sticky;
  top: 0;
  z-index: 10;
}

/* 空状态位置 */
.ant-table-placeholder {
	border: none !important; 
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}


/* 边框颜色 */

.ant-table-bordered .ant-table-thead>tr>th,
.ant-table-bordered .ant-table-tbody>tr>td {
  border-right: 1px solid transparent;
}

.ant-table-thead>tr>th,
.ant-table-tbody>tr>td {
  border-bottom: 1px solid #dee1e8;
}

.ant-table-bordered .ant-table-thead > tr:not(:last-child) > th{
  border-bottom: 1px solid #dee1e8;
}

.ant-pagination-options .ant-select-selection{
  border: 1px solid #dee1e8;
}




/* 分页组件的字号、字色 */
.ant-pagination{
  font-size: 12px;
  color: #666;
}

.ant-pagination-options .ant-select{
  color: #333;
  font-size: 12px;
}

.ant-pagination-item a{
  color:#666;
}

/* 分页 页数选择 高度设为 24px */
.ant-select-sm .ant-select-selection--single{
  height: 24px;
  display: flex;
  align-items: center;
}
.ant-select-sm .ant-select-selection__rendered{
  line-height: 24px;
}


/* 分页 页数选中 下拉icon居中 */
.ant-select-arrow{
  margin-top: -5px;
  color:#bfbfbf;
}

.ant-table-pagination.ant-pagination{
  margin: 8px 0 0;
  display: flex;
  align-items: center;
}


/* 去除表格顶部边距 */
.s-table-tool{
  padding-bottom: 0;
}


/* 双层表头   增加表头右边的边框线 */
.double-table-head .ant-table-middle>.ant-table-content>.ant-table-header>table>.ant-table-thead>tr>th,
.double-table-head .ant-table-middle>.ant-table-content>.ant-table-body>table>.ant-table-thead>tr>th,
.double-table-head .ant-table-middle>.ant-table-content>.ant-table-scroll>.ant-table-header>table>.ant-table-thead>tr>th,
.double-table-head .ant-table-middle>.ant-table-content>.ant-table-scroll>.ant-table-body>table>.ant-table-thead>tr>th,
.double-table-head .ant-table-middle>.ant-table-content>.ant-table-fixed-left>.ant-table-header>table>.ant-table-thead>tr>th,
.double-table-head .ant-table-middle>.ant-table-content>.ant-table-fixed-right>.ant-table-header>table>.ant-table-thead>tr>th,
.double-table-head .ant-table-middle>.ant-table-content>.ant-table-fixed-left>.ant-table-body-outer>.ant-table-body-inner>table>.ant-table-thead>tr>th,
.double-table-head .ant-table-middle>.ant-table-content>.ant-table-fixed-right>.ant-table-body-outer>.ant-table-body-inner>table>.ant-table-thead>tr>th,
.double-table-head .ant-table-middle>.ant-table-content>.ant-table-header>table>.ant-table-tbody>tr>td {
  border-right: 1px solid #dee1e8;
}
