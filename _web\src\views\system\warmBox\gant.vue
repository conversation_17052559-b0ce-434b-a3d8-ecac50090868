<template>
  <div style="background-color: #FFFFFF;padding: 20px 20px 60px 20px;">

    <div style="float: left;position: relative;z-index: 1;padding-bottom: 5px;width: 65%">
      <a-row :gutter="[8,8]">
        <a-col :span="3">
          <a-form-item label="温箱编号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryparam.code" @keyup.enter="init()" @change="init()"/>
          </a-form-item>
        </a-col>
        <a-col :span="3">
          <a-form-item label="温箱型号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryparam.model" @keyup.enter="init()" @change="init()"/>
          </a-form-item>
        </a-col>

        <a-col :span="3">
          <a-form-item label="温度" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryparam.tem" @keyup.enter="init()" @change="init()"/>
          </a-form-item>
        </a-col>

        <a-col :span="4">
          <a-form-item label="委托单号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryparam.folderNo" @keyup.enter="init()" @change="init()"/>
          </a-form-item>
        </a-col>

        <a-col :span="3">
          <a-form-item label="温箱类型" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-select v-model="queryparam.type" @change="init()" style="width: 100%" :allow-clear="true">
              <a-select-option value="高低温箱">
                高低温箱
              </a-select-option>
              <a-select-option value="高温箱">
                高温箱
              </a-select-option>
              <a-select-option value="412常温存储室">
                412常温存储室
              </a-select-option>
              <a-select-option value="413常温存储室">
                413常温存储室
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="3">
          <a-form-item label="电池类型" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-select v-model="queryparam.batteryType" @change="init()" style="width: 100%" :allow-clear="true">
              <a-select-option value="G圆柱">
                G圆柱
              </a-select-option>
              <a-select-option value="C圆柱">
                C圆柱
              </a-select-option>
              <a-select-option value="V圆柱">
                V圆柱
              </a-select-option>
              <a-select-option value="方形">
                方形
              </a-select-option>
              <a-select-option value="软包">
                软包
              </a-select-option>
              <a-select-option value="G26">
                G26
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="3">
          <a-form-item label="出箱范围" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-range-picker @change="rangeChange" v-model="queryparam.range" style="width: 260px">
            </a-range-picker>
          </a-form-item>
        </a-col>
        <!--<a-col :span="6"
               v-if="userInfo.account == 'superAdmin' || userInfo.roles.find(r => r.id == 1694647012972855298) != null">
          <a-form-item label="创建人" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
            <a-input v-model="queryparam.createName" @keyup.enter="getList(true)" @change="getList(true)"/>
          </a-form-item>
        </a-col>-->

      </a-row>
    </div>

    <div style="float: right;position: relative;z-index: 1;padding-bottom: 5px;">
      <a-button style="margin-left: 8px;width: 50px" @click="init(true)">重置</a-button>
      切换：
      <a-radio-group :options="plainOptions" defaultValue="month" :v-model="timeRange" @change="changeRange"/>


    </div>
    <div id="gantt" ref="gantt" :style="{height: height+'px'}"></div>

    <div style="height: 45px">
      <a-pagination @change="changePageSize" @showSizeChange="changePageSize"
                    style="position: absolute;bottom: 20px;right: 20px;"
                    :total="page.totalRows"
                    :show-total="(total, range) => `${range[0]}-${range[1]} 共 ${total} 条`"
                    show-size-changer
                    :page-size="page.pageSize"
                    :pageSizeOptions="['10', '20', '30', '40']"
                    size="small"
                    :default-current="1"
      />
    </div>


  </div>

</template>

<script>
  import {gantt} from '../testProgress/dhtmlxgantt/codebase/dhtmlxgantt';
  import "../testProgress/dhtmlxgantt/codebase/dhtmlxgantt.css";
  import "../testProgress/dhtmlxgantt/codebase/ext/dhtmlxgantt_drag_timeline.js";
  import "../testProgress/dhtmlxgantt/codebase/locale/locale_cn.js";
  import "../testProgress/dhtmlxgantt/codebase/ext/dhtmlxgantt_marker.js";
  import "../testProgress/dhtmlxgantt/codebase/ext/dhtmlxgantt_tooltip.js";
  import {Pagination} from 'ant-design-vue';
  import moment from 'moment';

  import {
    testProgressGantList
  } from '@/api/modular/system/testProgressManager'
  import {
    testWarmBoxGanttPage
  } from '@/api/modular/system/warmBoxManager'

  export default {
    components: {
      'a-pagination': Pagination
    },
    name: 'outGant',

    data() {
      return {
        queryparam: {},
        allAddress: null,
        statusList: ['Plan', 'Ongoing'],
        address: 'all',
        page: {pageNo: 1, pageSize: 10, totalRows: 10, totalPage: 1},
        show: false,
        labelCol: {

          sm: {
            span: 10
          }
        },
        wrapperCol: {

          sm: {
            span: 13
          }
        },
        timeRange: 'month',
        data: [],
        height: document.documentElement.clientHeight - 220,
        plainOptions: [{label: '年', value: 'year'},
          {label: '季度', value: 'quarter'},
          {label: '月', value: 'month'},
          {label: '周', value: 'week'},
          {label: '日', value: 'day'}],
        columns: [
          {
            name: "text",
            label: "展开/折叠",
            tree: true,
            resize: true,
            width: 100
          },
          {
            label: '序号',
            name: 'index',
            align: 'center',
            resize: true,
            width: 40,
            fixed: 'left',
          },{
            label: '温箱样品层架号',
            width: 120,

            resize: true,
            align: 'center',
            name: 'storeyCode',
          },
          {
            label: '温箱类型',
            width: 70,
            fixed: 'left',
            align: 'center',
            resize: true,
            name: 'warmType',
          }, {
            label: '温箱编号',
            width: 70,

            resize: true,
            resizable: false,
            align: 'center',
            name: 'code',
          }, {
            label: '温箱型号',
            width: 70,
            resize: true,
            align: 'center',
            name: 'model',
          }, {
            label: '温箱容积/L',
            width: 100,

            resize: true,
            align: 'center',
            name: 'volume',
          }, {
            label: '电池类型',
            width: 120,
            align: 'center',

            resize: true,
            name: 'batteryType',
          }, {
            label: '测试方法',
            width: 70,

            resize: true,
            align: 'center',
            name: 'testMethod',
          }, {
            label: '温箱温度/℃',
            width: 120,
            align: 'center',

            resize: true,
            name: 'tem',
          }, {
            label: '在测周期',
            width: 120,
            align: 'center',
            resize: true,
            name: 'period',
            template:function(task){
              return task.periodSymbol+task.period
            }
          }, {
            label: '样品层架数/层',
            width: 70,
            align: 'center',

            resize: true,
            name: 'totalStorey',
          }, {
            label: '每层层架吸塑盒摞数/摞',
            width: 70,
            align: 'center',

            resize: true,
            name: 'storeyXshLNum',
          }, {
            label: '每摞吸塑盒数/个',
            width: 70,
            align: 'center',

            resize: true,
            name: 'xshLHNum',
          }, {
            label: '每个吸塑盒可放电池数/pcs',
            width: 70,
            align: 'center',

            resize: true,
            name: 'xshPcsNum',
          }, {
            label: '单层放托盘数/个',
            width: 80,
            align: 'center',
            name: 'storeyTpGNum',
          },  {
            label: '可放吸塑盒/个',
            width: 100,
            align: 'center',

            resize: true,
            name: 'storeyPcsNum',
          }, {
            label: '每层可放电池数',
            width: 70,
            align: 'center',

            resize: true,
            name: 'storeyPcsNum',
          },{
            label: '温箱可放电池数/pcs',
            resize: true,
            width: 90,
            align: 'center',
            name: 'totalPcsNum'
          },
          {
            label: '已存放吸塑盒数/个',
            resize: true,
            width: 90,
            align: 'center',
            name: 'totalXshInNum',
          }, {
            label: '已存放电池数/pcs',
            resize: true,
            width: 90,
            align: 'center',
            name: 'totalPcsInNum',
          },


        ]
      };
    },

    mounted() {
      this.init()
    },
    destroyed() {
      gantt.clearAll()
    },
    methods: {
      rangeChange(a, b) {


        this.queryparam.rangeBegin = b[0]
        this.queryparam.rangeEnd = b[1]

        this.$nextTick(() => {
          this.init()
        })
      },
      changePageSize(a, b) {
        this.page.pageSize = b
        this.page.pageNo = a
        this.$nextTick(() => {
          this.init()
        })

      },
      changeRange(e) {
        this.timeRange = e.target.value
        gantt.ext.zoom.setLevel(this.timeRange);
        var today = gantt.date.date_part(new Date());
        gantt.showDate(today);
      },

      changeStatus(a) {

        this.queryparam.statusList = a

        this.init()
      },

      init(flag) {

        if (flag == true) {
          this.queryparam = {}
        }


        gantt._clear_data()

        this.queryparam.pageSize = this.page.pageSize
        this.queryparam.pageNo = this.page.pageNo
        let _that = this
        testWarmBoxGanttPage(this.queryparam).then(res => {
          this.data = res.data.rows
          this.page = res.data
          this.page.rows = null
        }).then(res => {
          gantt.config.show_errors = false;
          gantt.config.autoscroll = true;
          //gantt.config.sort = true;
          gantt.config.scroll_on_click = false;
          gantt.config.rtl = false;
          gantt.config.row_height = 35;
          gantt.config.task_height = 24;
          gantt.config.bar_height = 24;
          gantt.config.open_tree_initially = true;
          gantt.config.drag_links = true;
          gantt.config.drag_resize = false;
          gantt.config.details_on_dblclick = false;
          gantt.config.drag_lightbox = true;
          gantt.config.drag_progress = true;


          gantt.config.drag_move = true;//能否调整
          gantt.config.preserve_scroll = true;//图表刷新后，滚动条的位置跟原来保持一致
          //gantt.config.resize_rows = true;
          gantt.config.min_task_grid_row_height = 30;

          gantt.config.scroll_size = 10;

          gantt.config.open_split_tasks = true;
          var labelYear = "年";
          var labelMonth = "月";
          var labelDay = "日";
          var labelWeek = "周";
          var labelOrder = "第";

          var zoomConfig = {
            levels: [{
              name: "hour",
              scale_height: 80,
              min_column_width: 60,
              scales: [
                {
                  unit: "day", step: 1, format: "%Y" + labelYear + "%n" + labelMonth + "%d" + labelDay,
                }, {
                  unit: "hour", step: 1, format: "%H" + ":00"
                }
              ]
            }, {
              name: "day",
              scale_height: 80,
              min_column_width: 50,
              scales: [
                // {unit: "day", step: 1, format: "%n" + labelMonth + "%d" + labelDay}
                {unit: "month", format: "%Y" + labelYear + "%n" + labelMonth},
                {
                  unit: "day", step: 1, format: "%d"

                }
              ]
            }, {
              name: "week",
              scale_height: 80,
              min_column_width: 52,
              scales: [
                {
                  unit: "week", step: 1, format: function (date) {
                    var dateToStr = gantt.date.date_to_str("%n" + labelMonth + "%d" + labelDay);
                    var endDate = gantt.date.add(date, 6, "day");
                    var weekNum = gantt.date.date_to_str("%W")(date);
                    return "W" + weekNum + "&nbsp;&nbsp;" + dateToStr(date) + " - " + dateToStr(endDate);
                  }
                },
                // {unit: "day", step: 1, format: "%j %D"}
                {
                  unit: "day", step: 1, format: function (date) {
                    var dateToStr = gantt.date.date_to_str("%d");
                    var weekToStr = gantt.date.date_to_str("%D");
                    return "<div style='line-height: 20px;'>" + dateToStr(date) + "<br /><span class='cell-text'>" + weekToStr(date) + "</span></div>";
                  }
                }
              ]
            }, {
              name: "month",
              scale_height: 80,
              min_column_width: 60,
              scales: [
                {unit: "month", format: "%Y" + labelYear + "%n" + labelMonth},
                // {unit: "week", format: labelOrder + "%W " + labelWeek}
                {
                  unit: "week", format: function (date) {
                    var weekNum = gantt.date.date_to_str("%W")(date);
                    return "W" + weekNum;
                  }
                }
              ]
            }, {
              name: "quarter",
              height: 50,
              min_column_width: 90,
              scales: [
                {unit: "month", step: 1, format: "%Y" + labelYear + "%n" + labelMonth},
                {
                  unit: "quarter", step: 1, format: function (date) {
                    var dateToStr = gantt.date.date_to_str("%Y年%n月");
                    var endDate = gantt.date.add(gantt.date.add(date, 3, "month"), -1, "day");
                    return dateToStr(date) + " - " + dateToStr(endDate);
                  }
                }
              ]
            }, {
              name: "year",
              scale_height: 80,
              min_column_width: 50,
              scales: [
                {unit: "year", step: 1, format: "%Y" + labelYear},
                {unit: "month", step: 1, format: "%n" + labelMonth}
              ]
            }]
          };

          gantt.ext.zoom.init(zoomConfig);

          gantt.ext.zoom.setLevel(this.timeRange);


          gantt.config.xml_date = "%Y-%m-%d";
          gantt.config.scale_height = 80 //设置甘特图的表头高度
          //鼠标移入展示信息
          /*gantt.plugins({
            tooltip: true
          })*/
          //时间展示 2021-10-11 07:22
          gantt.templates.tooltip_date_format = gantt.date.date_to_str("%Y-%m-%d")
          //鼠标移入展示信息
          gantt.config.readonly = true //甘蔗图只读属性
          gantt.config.round_dnd_dates = false //将任务开始时间和结束时间自动“四舍五入'
          gantt.config.root_id = "root"
          //添加taba栏
          gantt.config.columns = this.columns
          gantt.config.layout = {
            css: "gantt_container",
            cols: [
              {
                width: 400,
                min_width: 300,
                left_column_width: 150,
                // adding horizontal scrollbar to the grid via the scrollX attribute
                rows: [
                  {view: "grid", scrollX: "gridScroll", scrollable: true, scrollY: "scrollVer"},
                  {view: "scrollbar", id: "gridScroll"}
                ]
              },
              {resizer: true, width: 1},
              {
                rows: [
                  {view: "timeline", scrollX: "scrollHor", scrollY: "scrollVer"},
                  {view: "scrollbar", id: "scrollHor"}
                ]
              },
              {view: "scrollbar", id: "scrollVer"}
            ]
          };

          gantt.getMarker(gantt.addMarker({
            start_date: new Date(),
            css: 'marker',
            text: '今天',
          }));

          gantt.templates.tooltip_text = function(start, end, task) {
            var warmType = '温箱类型';
            var code = '温箱编号';
            var model = '温箱型号';
            var volume = '温箱容积';
            var storeyCode = '温箱样品层架号';
            var batteryType = '电池类型';
            var testMethod = '测试方法';
            var tem = '温箱温度/℃';
            var period = '在测周期';
            var totalStorey = '样品层架数/层';
            var storeyXshLNum = '每层层架吸塑盒摞数/摞';
            var xshLHNum = '每摞吸塑盒数/个';
            var xshPcsNum = '每个吸塑盒可放电池数/pcs';
            var storeyPcsNum = '每层可放电池数';
            var storeyTpGNum = '单层放托盘数/个';
            var tpPcsNum = '每个托盘电池数/pcs';
           /* var totalPcsNum = '温箱可放电池数/pcs';
            var testNum = '中检次数';
            var realStarttime = '开始时间';
            var realEndtime = '结束时间';*/
            var str = ''
            if (task.parent != 'root') {
              str =
                '<span class="tooltip-scale-text">电芯编码：</span><b>'+task.text+'</b><br/>' +
                '<span class="tooltip-scale-text">开始时间：</span><b>' + moment(task.start_date).format("YYYY-MM-DD")  + '</b><br/>'+
                '<span class="tooltip-scale-text">结束时间：</span><b>' + moment(task.end_date).subtract(1, 'days').format("YYYY-MM-DD") + '</b><br/>'+
                '<span class="tooltip-scale-text">存储天数：</span><b>'+task.day+'</b><br/>' ;
            } else {
              str = '<div style="width: 350px"><div style="width: 50%;float: left"><span class="tooltip-scale-text">' + warmType + '：</span><b>' + task.warmType + '</b></div>' +
                '<div style="width: 50%;float: left"><span class="tooltip-scale-text">' + code + '：</span><b>' + task.code + '</b></div></div>' +
                '<div style="width: 350px"><div style="width: 50%;float: left"><span class="tooltip-scale-text">' + model + '：</span><b>' + task.model + '</b></div>' +
                '<div style="width: 50%;float: left"><span class="tooltip-scale-text">' + volume + '：</span><b>' + task.volume + '</b></div></div>'+
                '<div style="width: 350px"><div style="width: 50%;float: left"><span class="tooltip-scale-text">' + storeyCode + '：</span><b>' + task.storeyCode + '</b></div>'+
                '<div style="width: 50%;float: left"><span class="tooltip-scale-text">' + batteryType + '：</span><b>' + task.batteryType + '</b></div></div>'+
                '<div style="width: 350px"><div style="width: 50%;float: left"><span class="tooltip-scale-text">' + testMethod + '：</span><b>' + task.testMethod + '</b></div>'+
                '<div style="width: 50%;float: left"><span class="tooltip-scale-text">' + tem + '：</span><b>' + task.tem + '</b></div></div>'+

                '<div style="width: 50%;float: left"><span class="tooltip-scale-text">' + period + '：</span><b>' + task.symbol+task.period + '</b></div></div>'+
                '<div style="width: 350px"><div style="width: 50%;float: left"><span class="tooltip-scale-text">' + totalStorey + '：</span><b>' + task.totalStorey + '</b></div>'+
                '<div style="width: 50%;float: left"><span class="tooltip-scale-text">' + storeyXshLNum + '：</span><b>' + task.storeyXshLNum + '</b></div></div>'+
                '<div style="width: 350px"><div style="width: 50%;float: left"><span class="tooltip-scale-text">' + xshLHNum + '：</span><b>' + task.xshLHNum + '</b></div>'+
                '<div style="width: 50%;float: left"><span class="tooltip-scale-text">' + xshPcsNum + '：</span><b>' + task.xshPcsNum + '</b></div></div>'+

                '<div style="width: 350px"><div style="width: 50%;float: left"><span class="tooltip-scale-text">'+storeyPcsNum+'：</span><b>' + task.storeyPcsNum + '</b></div>'+

                '<div style="width: 350px"><div style="width: 50%;float: left"><span class="tooltip-scale-text">' + storeyTpGNum + '：</span><b>' + task.storeyTpGNum + '</b></div>'+

                '<div style="width: 350px"><div style="width: 50%;float: left"><span class="tooltip-scale-text">' +  tpPcsNum + '：</span><b>' +task.tpPcsNum  + '</b></div>'
                ;
            }

            gantt.attachEvent("onTaskClick", function(id,e){
              console.log("fff")
              return true;
            });

            if (!gantt.ext.tooltips.tooltip._isTooltipVisible) {
              // 启动倒计时关闭tooltip
              setTimeout(function() {
                gantt.ext.tooltips.tooltip.hide();
              }, 5000); // 设置延迟时间，单位为毫秒
            }
            return str

          };


          gantt.init(this.$refs.gantt)

          gantt.parse({data: this.data})
          let today = gantt.date.date_part(new Date());

          gantt.showDate(today);
          gantt.init(this.$refs.gantt)


        })


      },
    },
  };
</script>

<style lang='less' scoped=''>

  /deep/ .gantt_task_cell {
    color: #000000;
    border-right: 0;
  }

  /deep/ .gantt_cell {
    color: #000000;
    border-right: 1px solid #ebebeb;
  }

  /deep/ .gantt_grid_head_cell {
    color: #000000;
    border-right: 1px solid #ebebeb !important;;
  }

  /deep/ .gantt_task {
    color: #000000 !important;
  }

  /deep/ .gantt_task_scale {
    color: #000000 !important;
  }

  /deep/ .gantt_scale_cell {
    color: #000000 !important;
  }

  /deep/ .gantt_task_line {
    border-radius: 10px;
  }

  /deep/ .gantt_resizer {
    cursor: e-resize;
    position: relative;
  }

  /* /deep/ .gantt_task_line {
     background: url("caps-unlock-filling.png") left / 25px 25px no-repeat,url("caps-unlock-filling - 副本.png") right / 25px 25px no-repeat;
   }*/

  /deep/ .gantt_container {

    box-shadow: rgb(126 139 169 / 20%) 2px 2px 13px 2px;
    border-radius: 6px;
  }

  /deep/ .gantt_grid_data .gantt_row.gantt_selected, .gantt_grid_data .gantt_row.odd.gantt_selected, .gantt_task_row.gantt_selected {
    background-color: unset;
  }

  /deep/ .gantt_task_row.gantt_selected .gantt_task_cell {
    border-right-color: unset;
  }

  /deep/ .gantt_selected {
    background-color: unset;
  }

  /deep/ .gantt_row.gantt_row_task:hover {
    background-color: #00000000;
  }

  /deep/ span.ant-radio + * {
    padding-right: 0px;
    padding-left: 0px;
  }

  /deep/ .ant-col {
    padding: 0 !important;
    height: 40px !important;
  }

  /deep/ .ant-btn > i, /deep/ .ant-btn > span {
    display: flex;
    justify-content: center;
  }

  /deep/ .gantt_task_drag {
    cursor: ew-resize;
  }

  /deep/ .gantt_task_resize.gantt_task_resize_right:before {
    width: 10px;
  }


  /deep/ .gantt_layout_cell {
    border-top: 0;
    border-right: 0;
    box-shadow: -1px 4px 6px 5px rgba(126, 139, 169, 0.07);
    border-radius: 6px 0 0 6px;
  }

  /deep/ .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
    color: #fff;
    background: #1890FF;
  }

  /deep/ .gantt_container {
    border: 1px solid #bcbcbc;
    border-radius: 10px;
  }

  /deep/ .gantt_fixed_column {
    width: 200px; /* 固定左侧 2 列 */
    pointer-events: none; /* 禁止拖动 */
  }

</style>