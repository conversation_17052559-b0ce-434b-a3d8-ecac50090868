{"version": 3, "sources": ["webpack://[name]/webpack/universalModuleDefinition", "webpack://[name]/webpack/bootstrap", "webpack://[name]/./sources/ext/smart_rendering.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "window", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "gantt", "config", "smart_rendering", "_smart_render", "getViewPort", "timeline", "$ui", "get<PERSON>iew", "grid", "view", "$layout", "isVisible", "viewSize", "getSize", "scrollPos", "getScrollState", "y", "y_end", "x", "x_end", "getScrollSizes", "scroll", "getVisibleTaskCount", "row_height", "isInViewPort", "item", "viewPort", "isTaskDisplayed", "id", "task", "$keyboardNavigation", "dispatcher", "isTaskFocused", "this", "getTaskPosition", "isLinkDisplayed", "link", "getLinkPosition", "getTaskTop", "from_pos", "source", "to_pos", "target", "Math", "min", "max", "getRange", "buffer", "port", "firstTask", "floor", "lastTask", "ceil", "visibleTasks", "$data", "tasksStore", "getIndexRange", "visibleIds", "length", "push", "_redrawItems", "renderers", "visibleItems", "shouldBeVisible", "alreadyVisible", "render", "rendered", "node", "parentNode", "hide", "restore", "_getVisibleTasks", "ids", "rows", "getTask", "$index", "resetProjectDates", "_getVisibleLinks", "visible_links", "links", "linksStore", "_recalculateLinkedProjects", "visibleLinks", "recalculateTasks", "isTaskExists", "updateRender", "callEvent", "layers", "$services", "getService", "<PERSON><PERSON><PERSON><PERSON>", "getDataRender", "<PERSON><PERSON><PERSON><PERSON>", "_redrawTasks", "getLayers", "cached", "_takeFrom<PERSON>ache", "payload", "cacheName", "undefined", "initCache", "caches", "method", "calculate", "cache", "invalidateCache", "smartRender", "clearViewPortCache", "clearAllCache", "attachEvent", "oldLeft", "oldTop", "left", "top", "attachOnce", "filters", "detachEvent"], "mappings": ";;;;;;;;;;;;CAAA,SAAAA,EAAAC,GACA,iBAAAC,SAAA,iBAAAC,OACAA,OAAAD,QAAAD,IACA,mBAAAG,eAAAC,IACAD,OAAA,qCAAAH,GACA,iBAAAC,QACAA,QAAA,mCAAAD,IAEAD,EAAA,mCAAAC,IARA,CASCK,OAAA,WACD,mBCTA,IAAAC,KAGA,SAAAC,EAAAC,GAGA,GAAAF,EAAAE,GACA,OAAAF,EAAAE,GAAAP,QAGA,IAAAC,EAAAI,EAAAE,IACAC,EAAAD,EACAE,GAAA,EACAT,YAUA,OANAU,EAAAH,GAAAI,KAAAV,EAAAD,QAAAC,IAAAD,QAAAM,GAGAL,EAAAQ,GAAA,EAGAR,EAAAD,QA0DA,OArDAM,EAAAM,EAAAF,EAGAJ,EAAAO,EAAAR,EAGAC,EAAAQ,EAAA,SAAAd,EAAAe,EAAAC,GACAV,EAAAW,EAAAjB,EAAAe,IACAG,OAAAC,eAAAnB,EAAAe,GAA0CK,YAAA,EAAAC,IAAAL,KAK1CV,EAAAgB,EAAA,SAAAtB,GACA,oBAAAuB,eAAAC,aACAN,OAAAC,eAAAnB,EAAAuB,OAAAC,aAAwDC,MAAA,WAExDP,OAAAC,eAAAnB,EAAA,cAAiDyB,OAAA,KAQjDnB,EAAAoB,EAAA,SAAAD,EAAAE,GAEA,GADA,EAAAA,IAAAF,EAAAnB,EAAAmB,IACA,EAAAE,EAAA,OAAAF,EACA,KAAAE,GAAA,iBAAAF,QAAAG,WAAA,OAAAH,EACA,IAAAI,EAAAX,OAAAY,OAAA,MAGA,GAFAxB,EAAAgB,EAAAO,GACAX,OAAAC,eAAAU,EAAA,WAAyCT,YAAA,EAAAK,UACzC,EAAAE,GAAA,iBAAAF,EAAA,QAAAM,KAAAN,EAAAnB,EAAAQ,EAAAe,EAAAE,EAAA,SAAAA,GAAgH,OAAAN,EAAAM,IAAqBC,KAAA,KAAAD,IACrI,OAAAF,GAIAvB,EAAA2B,EAAA,SAAAhC,GACA,IAAAe,EAAAf,KAAA2B,WACA,WAA2B,OAAA3B,EAAA,SAC3B,WAAiC,OAAAA,GAEjC,OADAK,EAAAQ,EAAAE,EAAA,IAAAA,GACAA,GAIAV,EAAAW,EAAA,SAAAiB,EAAAC,GAAsD,OAAAjB,OAAAkB,UAAAC,eAAA1B,KAAAuB,EAAAC,IAGtD7B,EAAAgC,EAAA,aAIAhC,IAAAiC,EAAA,yBClFAC,MAAAC,OAAAC,iBAAA,EAEAF,MAAAG,eACAC,YAAA,WAEA,IAAAC,EAAAL,MAAAM,IAAAC,QAAA,YACAC,EAAAR,MAAAM,IAAAC,QAAA,QACAE,EAAAT,MAAAU,QACAL,KAAAM,YACAF,EAAAJ,EACGG,KAAAG,cACHF,EAAAD,GAGA,IAAAI,EAAAH,EAAAI,UACAC,EAAAd,MAAAe,iBAEA,OACAC,EAAAF,EAAAE,EACAC,MAAAH,EAAAE,EAAAJ,EAAAI,EACAE,EAAAJ,EAAAI,EACAC,MAAAL,EAAAI,EAAAN,EAAAM,IAGAE,eAAA,WACA,IAAAC,EAAArB,MAAAe,iBAGA,OAFAM,EAAAH,EAAAG,EAAAH,GAAA,EACAG,EAAAL,EAAAK,EAAAL,GAAAhB,MAAAsB,sBAAAtB,MAAAC,OAAAsB,WACAF,GAEAG,aAAA,SAAAC,EAAAC,GACA,SAAAD,EAAAT,EAAAU,EAAAT,OAAAQ,EAAAR,MAAAS,EAAAV,IAGAW,gBAAA,SAAAC,EAAAC,GACA,SAAA7B,MAAA8B,sBAAA9B,MAAA8B,oBAAAC,WAAAC,cAAAJ,KAIAK,KAAAT,aAAAS,KAAAC,gBAAAN,GAAAK,KAAA7B,gBAEA+B,gBAAA,SAAAP,EAAAQ,GACA,OAAAH,KAAAT,aAAAS,KAAAI,gBAAAT,EAAAQ,GAAAH,KAAA7B,gBAEA8B,gBAAA,SAAAN,GACA,IAAAZ,EAAAhB,MAAAsC,WAAAV,GACA,OACAZ,IACAC,MAAAD,EAAAhB,MAAAC,OAAAsB,aAGAc,gBAAA,SAAAT,EAAAQ,GACA,IAAAG,EAAAvC,MAAAsC,WAAAF,EAAAI,QACAC,EAAAzC,MAAAsC,WAAAF,EAAAM,QAEA,OACA1B,EAAA2B,KAAAC,IAAAL,EAAAE,GACAxB,MAAA0B,KAAAE,IAAAN,EAAAE,GAAAzC,MAAAC,OAAAsB,aAGAuB,SAAA,SAAAC,GACAA,KAAA,EASA,IAPA,IAAAC,EAAAf,KAAA7B,cAEA6C,EAAAN,KAAAO,MAAAP,KAAAE,IAAA,EAAAG,EAAAhC,GAAAhB,MAAAC,OAAAsB,YAAAwB,EACAI,EAAAR,KAAAS,KAAAT,KAAAE,IAAA,EAAAG,EAAA/B,OAAAjB,MAAAC,OAAAsB,YAAAwB,EAEAM,EAAArD,MAAAsD,MAAAC,WAAAC,cAAAP,EAAAE,GACAM,KACAzF,EAAA,EAAgBA,EAAAqF,EAAAK,OAAyB1F,IACzCyF,EAAAE,KAAAN,EAAArF,GAAA4D,IAGA,OAAA6B,GAEAG,aAAA,SAAAC,EAAAC,GAEA,IADA,IAAAC,KACA7E,EAAA,EAAgBA,EAAA4E,EAAAJ,OAAyBxE,IACzC6E,EAAAD,EAAA5E,GAAA0C,KAAA,EAIA,IAFA,IAAAoC,KAEAlF,EAAA,EAAgBA,EAAA+E,EAAAH,OAAsB5E,IAAA,CACtC,IAAAmF,EAAAJ,EAAA/E,GAEA,QAAAd,KAAAiG,EAAAC,SACA,GAAAH,EAAA/F,GAEK,CACL,IAAAmG,EAAAF,EAAAC,SAAAlG,GACAmG,KAAAC,aACAJ,EAAAhG,IAAA,QAJAiG,EAAAI,KAAArG,GASA,IAAAkB,EAAA,EAAiBA,EAAA4E,EAAAJ,OAAyBxE,IAC1C8E,EAAAF,EAAA5E,GAAA0C,KACAqC,EAAAK,QAAAR,EAAA5E,MAKAqF,iBAAA,WAGA,IAFA,IAAAC,EAAAvC,KAAAa,WACA2B,KACAzG,EAAA,EAAcA,EAAAwG,EAAAd,OAAgB1F,IAAA,CAC9B,IAAAyD,EAAAzB,MAAA0E,QAAAF,EAAAxG,IACAyD,EAAAkD,OAAA3G,EAEAgC,MAAA4E,kBAAAnD,GACAgD,EAAAd,KAAAlC,GAEA,OAAAgD,GAEAI,iBAAA,WAIA,IAHA,IAAAC,KACAC,EAAA/E,MAAAsD,MAAA0B,WAAAxB,gBAEAxF,EAAA,EAAgBA,EAAA+G,EAAArB,OAAkB1F,IAClCiE,KAAAE,gBAAA4C,EAAA/G,GAAA4D,GAAAmD,EAAA/G,KACA8G,EAAAnB,KAAAoB,EAAA/G,IAGA,OAAA8G,GAGAG,2BAAA,SAAAC,GAIA,IADA,IAAAC,KACAnH,EAAA,EAAgBA,EAAAkH,EAAAxB,OAAyB1F,IACzCmH,EAAAD,EAAAlH,GAAAwE,SAAA,EACA2C,EAAAD,EAAAlH,GAAA0E,SAAA,EAGA,QAAA1E,KAAAmH,EACAnF,MAAAoF,aAAApH,IACAgC,MAAA4E,kBAAA5E,MAAA0E,QAAA1G,KAGAqH,aAAA,WACArF,MAAAsF,UAAA,0BACA,IAAAjC,EAAApB,KAAAsC,mBACAW,EAAAjD,KAAA4C,mBAGA5C,KAAAgD,2BAAAC,GAEA,IAAAK,EAAAvF,MAAAwF,UAAAC,WAAA,UAEAC,EAAAH,EAAAI,cAAA,QACAC,EAAAL,EAAAI,cAAA,QAEA1D,KAAA4D,aAAAH,EAAAI,YAAAzC,GACApB,KAAA2B,aAAAgC,EAAAE,YAAAZ,GACAlF,MAAAsF,UAAA,qBAIAO,aAAA,SAAAN,EAAAlC,GACApB,KAAA2B,aAAA2B,EAAAlC,IAGA0C,UAEAC,eAAA,SAAApE,EAAAqE,EAAAC,GACAjE,KAAA8D,OAAAG,KACAjE,KAAA8D,OAAAG,GAAA,MACA,IAAAH,EAAA9D,KAAA8D,OAAAG,GAEA,YAAAC,IAAAvE,GACAmE,IACAA,EAAA9D,KAAA8D,OAAAG,YAGAC,IAAAJ,EAAAnE,KACAmE,EAAAnE,GAAAqE,EAAArE,IAGAmE,EAAAnE,KAEAmE,IACAA,EAAAE,KAEAF,IAIAK,UAAA,WAUA,IATA,IAAAC,GACA,kBACA,kBACA,kBACA,kBACA,cACA,kBAGArI,EAAA,EAAgBA,EAAAqI,EAAA3C,OAAmB1F,IAAA,CACnC,IAAAsI,EAAAD,EAAArI,GACAiI,EAAAjG,MAAAR,KAAAyC,KAAAqE,GAAArE,MAEAA,KAAAqE,GAAA,SAAAC,EAAAC,GACA,gBAAA5E,GACA,OAAAK,KAAA+D,eAAApE,EAAA2E,EAAAC,IAFA,CAIIP,EAAAK,GAGJrE,KAAAwE,kBACAxE,KAAAmE,UAAA,cAEAK,gBAAA,WACA,IAAAC,EAAAzE,KAGA,SAAA0E,IACAD,EAAAX,OAAA3F,YAAA,KACAsG,EAAAX,OAAA3E,eAAA,KACAsF,EAAAX,OAAApE,gBAAA,KACA+E,EAAAX,OAAA5D,gBAAA,KASA,SAAAyE,IACAD,IAPAD,EAAAX,OAAApE,gBAAA,KACA+E,EAAAX,OAAA5D,gBAAA,KACAuE,EAAAX,OAAA1D,gBAAA,KACAqE,EAAAX,OAAA7D,gBAAA,KA0BAlC,MAAA6G,YAAA,qBACAD,MAEA5G,MAAA6G,YAAA,qBACAD,MAGA5G,MAAA6G,YAAA,oBAfA,SAAAjF,GACA8E,EAAAX,OAAA5D,kBACAuE,EAAAX,OAAA5D,gBAAAP,QAAAuE,GAEAO,EAAAX,OAAA1D,kBACAqE,EAAAX,OAAA1D,gBAAAT,QAAAuE,KAWAnG,MAAA6G,YAAA,iBAAAD,GACA5G,MAAA6G,YAAA,oBAAAD,GACA5G,MAAA6G,YAAA,oBA5BA,SAAAjF,GACA8E,EAAAX,OAAApE,kBACA+E,EAAAX,OAAApE,gBAAAC,QAAAuE,GAEAO,EAAAX,OAAA7D,kBACAwE,EAAAX,OAAA7D,gBAAAN,QAAAuE,KAwBAnG,MAAA6G,YAAA,gBAAAF,GACA3G,MAAA6G,YAAA,eAAAD,GAEA3E,KAAAwE,gBAAA,eAKAzG,MAAA6G,YAAA,yBAAAC,EAAAC,EAAAC,EAAAC,GACAjH,MAAAC,OAAAC,kBAEA6G,GAAAE,GAAAH,GAAAE,GAGAhH,MAAAG,cAAAkF,kBAOArF,MAAA6G,YAAA,0BACA7G,MAAAC,OAAAC,iBACAF,MAAAG,cAAAkF,iBAIA,WACA,IAAA6B,EAAAlH,MAAA6G,YAAA,0BACA,IAAAtB,EAAAvF,MAAAwF,UAAAC,WAAA,UACAF,EAAAI,cAAA,QACAwB,QAAAxD,KAAA,SAAA/B,EAAAC,GACA,OAAA7B,MAAAC,OAAAC,mBAGAF,MAAAG,cAAAwB,gBAAAC,EAAAC,KAGA0D,EAAAI,cAAA,QACAwB,QAAAxD,KAAA,SAAA/B,EAAAQ,GACA,OAAApC,MAAAC,OAAAC,mBAGAF,MAAAG,cAAAgC,gBAAAP,EAAAQ,KAGApC,MAAAoH,YAAAF,KAnBA", "file": "ext/dhtmlxgantt_smart_rendering.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"ext/dhtmlxgantt_smart_rendering\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ext/dhtmlxgantt_smart_rendering\"] = factory();\n\telse\n\t\troot[\"ext/dhtmlxgantt_smart_rendering\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/codebase/\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 224);\n", "gantt.config.smart_rendering = true;\n\ngantt._smart_render = {\n\tgetViewPort: function(){\n\n\t\tvar timeline = gantt.$ui.getView(\"timeline\");\n\t\tvar grid = gantt.$ui.getView(\"grid\");\n\t\tvar view = gantt.$layout;\n\t\tif (timeline && timeline.isVisible()) {\n\t\t\tview = timeline;\n\t\t} else if (grid && grid.isVisible()) {\n\t\t\tview = grid;\n\t\t}\n\n\t\tvar viewSize = view.getSize();\n\t\tvar scrollPos = gantt.getScrollState();\n\n\t\treturn {\n\t\t\ty: scrollPos.y,\n\t\t\ty_end: scrollPos.y + viewSize.y,\n\t\t\tx: scrollPos.x,\n\t\t\tx_end: scrollPos.x + viewSize.x\n\t\t};\n\t},\n\tgetScrollSizes: function(){\n\t\tvar scroll = gantt.getScrollState();\n\t\tscroll.x = scroll.x || 0;\n\t\tscroll.y = scroll.y || gantt.getVisibleTaskCount()*gantt.config.row_height;\n\t\treturn scroll;\n\t},\n\tisInViewPort: function(item, viewPort){\n\t\treturn !!(item.y < viewPort.y_end && item.y_end > viewPort.y);\n\t},\n\n\tisTaskDisplayed: function(id, task){\n\t\tif(gantt.$keyboardNavigation && gantt.$keyboardNavigation.dispatcher.isTaskFocused(id)){\n\t\t\treturn true;\n\t\t}\n\n\t\treturn this.isInViewPort(this.getTaskPosition(id), this.getViewPort());\n\t},\n\tisLinkDisplayed: function(id, link){\n\t\treturn this.isInViewPort(this.getLinkPosition(id, link), this.getViewPort());\n\t},\n\tgetTaskPosition: function(id){\n\t\tvar y = gantt.getTaskTop(id);\n\t\treturn {\n\t\t\ty: y,\n\t\t\ty_end: y + gantt.config.row_height\n\t\t};\n\t},\n\tgetLinkPosition: function(id, link){\n\t\tvar from_pos = gantt.getTaskTop(link.source),\n\t\t\tto_pos = gantt.getTaskTop(link.target);\n\n\t\treturn {\n\t\t\ty: Math.min(from_pos, to_pos),\n\t\t\ty_end: Math.max(from_pos, to_pos) + gantt.config.row_height\n\t\t};\n\t},\n\tgetRange: function(buffer){\n\t\tbuffer = buffer || 0;\n\n\t\tvar port = this.getViewPort();\n\n\t\tvar firstTask = Math.floor(Math.max(0, port.y) / gantt.config.row_height) - buffer;\n\t\tvar lastTask = Math.ceil(Math.max(0, port.y_end) / gantt.config.row_height) + buffer;\n\n\t\tvar visibleTasks = gantt.$data.tasksStore.getIndexRange(firstTask, lastTask);\n\t\tvar visibleIds = [];\n\t\tfor(var i = 0; i < visibleTasks.length; i++){\n\t\t\tvisibleIds.push(visibleTasks[i].id);\n\t\t}\n\n\t\treturn visibleIds;\n\t},\n\t_redrawItems: function(renderers, visibleItems){\n\t\tvar shouldBeVisible = {};\n\t\tfor(var t = 0; t < visibleItems.length; t++){\n\t\t\tshouldBeVisible[visibleItems[t].id] = true;\n\t\t}\n\t\tvar alreadyVisible = {};\n\n\t\tfor(var r = 0; r < renderers.length; r++){\n\t\t\tvar render = renderers[r];\n\n\t\t\tfor(var i in render.rendered){\n\t\t\t\tif(!shouldBeVisible[i]){\n\t\t\t\t\trender.hide(i);\n\t\t\t\t}else{\n\t\t\t\t\tvar node = render.rendered[i];\n\t\t\t\t\tif(node && node.parentNode) {\n\t\t\t\t\t\talreadyVisible[i] = true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfor(var t = 0; t < visibleItems.length; t++){\n\t\t\t\tif(!alreadyVisible[visibleItems[t].id])\n\t\t\t\t\trender.restore(visibleItems[t]);\n\t\t\t}\n\t\t}\n\t},\n\n\t_getVisibleTasks: function(){\n\t\tvar ids = this.getRange();\n\t\tvar rows = [];\n\t\tfor(var i=0; i < ids.length; i++){\n\t\t\tvar item = gantt.getTask(ids[i]);\n\t\t\titem.$index = i;\n\t\t\t//this._update_parents(item.id, true);\n\t\t\tgantt.resetProjectDates(item);\n\t\t\trows.push(item);\n\t\t}\n\t\treturn rows;\n\t},\n\t_getVisibleLinks: function(){\n\t\tvar visible_links = [];\n\t\tvar links = gantt.$data.linksStore.getIndexRange();\n\n\t\tfor(var i = 0; i < links.length; i++){\n\t\t\tif(this.isLinkDisplayed(links[i].id, links[i])){\n\t\t\t\tvisible_links.push(links[i]);\n\t\t\t}\n\t\t}\n\t\treturn visible_links;\n\t},\n\n\t_recalculateLinkedProjects: function(visibleLinks){\n\t\t// projects have dynamic duration, make sure that durations are recalculated before links display,\n\t\t// so links are shown on correct dates\n\t\tvar recalculateTasks = {};\n\t\tfor(var i = 0; i < visibleLinks.length; i++){\n\t\t\trecalculateTasks[visibleLinks[i].source] = true;\n\t\t\trecalculateTasks[visibleLinks[i].target] = true;\n\t\t}\n\n\t\tfor(var i in recalculateTasks){\n\t\t\tif(gantt.isTaskExists(i))\n\t\t\t\tgantt.resetProjectDates(gantt.getTask(i));\n\t\t}\n\t},\n\tupdateRender: function(){\n\t\tgantt.callEvent(\"onBeforeSmartRender\", []);\n\t\tvar visibleTasks = this._getVisibleTasks();\n\t\tvar visibleLinks = this._getVisibleLinks();\n\n\t\t// TODO: performance test\n\t\tthis._recalculateLinkedProjects(visibleLinks);\n\n\t\tvar layers = gantt.$services.getService(\"layers\");\n\n\t\tvar taskRenderer = layers.getDataRender(\"task\");\n\t\tvar linkRenderer = layers.getDataRender(\"link\");\n\n\t\tthis._redrawTasks(taskRenderer.getLayers(), visibleTasks);\n\t\tthis._redrawItems(linkRenderer.getLayers(), visibleLinks);\n\t\tgantt.callEvent(\"onSmartRender\", []);\n\t},\n\n\t// hook to override from key nav\n\t_redrawTasks: function(layers, visibleTasks){\n\t\tthis._redrawItems(layers, visibleTasks);\n\t},\n\n\tcached:{},\n\n\t_takeFromCache: function(id, payload, cacheName){\n\t\tif(!this.cached[cacheName])\n\t\t\tthis.cached[cacheName] = null;\n\t\tvar cached = this.cached[cacheName];\n\n\t\tif(id !== undefined){\n\t\t\tif(!cached){\n\t\t\t\tcached = this.cached[cacheName] = {};\n\t\t\t}\n\n\t\t\tif(cached[id] === undefined){\n\t\t\t\tcached[id] = payload(id);\n\t\t\t}\n\n\t\t\treturn cached[id];\n\t\t}else{\n\t\t\tif(!cached){\n\t\t\t\tcached = payload();\n\t\t\t}\n\t\t\treturn cached;\n\t\t}\n\n\t},\n\tinitCache:function(){\n\t\tvar caches = [\n\t\t\t\"getLinkPosition\",\n\t\t\t\"getTaskPosition\",\n\t\t\t\"isTaskDisplayed\",\n\t\t\t\"isLinkDisplayed\",\n\t\t\t\"getViewPort\",\n\t\t\t\"getScrollSizes\"\n\t\t];\n\n\t\tfor(var i = 0; i < caches.length; i++){\n\t\t\tvar method = caches[i];\n\t\t\tvar payload = gantt.bind(this[method], this);\n\n\t\t\tthis[method] = (function(calculate, cache){\n\t\t\t\treturn function(id){\n\t\t\t\t\treturn this._takeFromCache(id, calculate, cache);\n\t\t\t\t};\n\t\t\t})(payload, method);\n\t\t}\n\n\t\tthis.invalidateCache();\n\t\tthis.initCache = function(){};\n\t},\n\tinvalidateCache: function(){\n\t\tvar smartRender = this;\n\n\n\t\tfunction clearViewPortCache(){\n\t\t\tsmartRender.cached.getViewPort = null;\n\t\t\tsmartRender.cached.getScrollSizes = null;\n\t\t\tsmartRender.cached.isTaskDisplayed = null;\n\t\t\tsmartRender.cached.isLinkDisplayed = null;\n\t\t}\n\t\tfunction clearDataCache(){\n\t\t\tsmartRender.cached.isTaskDisplayed = null;\n\t\t\tsmartRender.cached.isLinkDisplayed = null;\n\t\t\tsmartRender.cached.getLinkPosition = null;\n\t\t\tsmartRender.cached.getTaskPosition = null;\n\t\t}\n\n\t\tfunction clearAllCache(){\n\t\t\tclearViewPortCache();\n\t\t\tclearDataCache();\n\t\t}\n\n\t\tfunction clearTaskCache(id){\n\t\t\tif(smartRender.cached.isTaskDisplayed){\n\t\t\t\tsmartRender.cached.isTaskDisplayed[id] = undefined;\n\t\t\t}\n\t\t\tif(smartRender.cached.getTaskPosition){\n\t\t\t\tsmartRender.cached.getTaskPosition[id] = undefined;\n\t\t\t}\n\t\t}\n\n\n\t\tfunction clearLinkCache(id){\n\t\t\tif(smartRender.cached.isLinkDisplayed){\n\t\t\t\tsmartRender.cached.isLinkDisplayed[id] = undefined;\n\t\t\t}\n\t\t\tif(smartRender.cached.getLinkPosition){\n\t\t\t\tsmartRender.cached.getLinkPosition[id] = undefined;\n\t\t\t}\n\t\t}\n\t\tgantt.attachEvent(\"onClear\", function(){\n\t\t\tclearAllCache();\n\t\t});\n\t\tgantt.attachEvent(\"onParse\", function(){\n\t\t\tclearAllCache();\n\t\t});\n\n\t\tgantt.attachEvent(\"onAfterLinkUpdate\", clearLinkCache);\n\t\tgantt.attachEvent(\"onAfterTaskAdd\", clearAllCache);\n\t\tgantt.attachEvent(\"onAfterTaskDelete\", clearAllCache);\n\t\tgantt.attachEvent(\"onAfterTaskUpdate\", clearTaskCache);\n\t\tgantt.attachEvent(\"onGanttScroll\",  clearViewPortCache);\n\t\tgantt.attachEvent(\"onDataRender\", clearAllCache);\n\n\t\tthis.invalidateCache = function(){};\n\t}\n\n};\n\ngantt.attachEvent(\"onGanttScroll\", function(oldLeft, oldTop, left, top){\n\tif(gantt.config.smart_rendering){\n\n\t\tif((oldTop != top) || (oldLeft == left)){\n\n\t\t\t//var visibleTasks = gantt._smart_render.getRange();\n\t\t\tgantt._smart_render.updateRender();\n\n\t\t}\n\n\t}\n});\n\ngantt.attachEvent(\"onDataRender\", function() {\n\tif(gantt.config.smart_rendering){\n\t\tgantt._smart_render.updateRender();\n\t}\n});\n\n(function(){\n\tvar attachOnce = gantt.attachEvent(\"onGanttReady\", function(){\n\t\tvar layers = gantt.$services.getService(\"layers\");\n\t\tvar taskRenderer = layers.getDataRender(\"task\");\n\t\ttaskRenderer.filters.push(function(id, task){\n\t\t\tif(!gantt.config.smart_rendering)\n\t\t\t\treturn true;\n\t\t\telse\n\t\t\t\treturn !!gantt._smart_render.isTaskDisplayed(id, task);\n\t\t});\n\n\t\tvar linkRenderer = layers.getDataRender(\"link\");\n\t\tlinkRenderer.filters.push(function(id, link){\n\t\t\tif(!gantt.config.smart_rendering)\n\t\t\t\treturn true;\n\t\t\telse\n\t\t\t\treturn !!gantt._smart_render.isLinkDisplayed(id, link);\n\t\t});\n\n\t\tgantt.detachEvent(attachOnce);\n\t});\n})();\n\n"], "sourceRoot": ""}