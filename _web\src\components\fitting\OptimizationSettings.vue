<template>
  <div class="weight-settings">
    <h5>权重设置</h5>

    <div class="weight-settings-container">
      <a-form layout="vertical">
        <!-- 温度设置 -->
        <div class="weight-setting-group">
          <div class="weight-setting-header">温度设置</div>
          <a-row :gutter="16" type="flex" align="middle">
            <a-col :xs="24" :sm="16" :md="16" :lg="16" :xl="16">
              <a-form-item label="温度（°C）选择" class="form-item">
                <a-select
                  mode="multiple"
                  v-model="localWeightConfig.selected_temperatures"
                  placeholder="选择温度"
                  class="select-control"
                  @change="updateWeightConfig"
                >
                  <a-select-option v-for="temp in fittingData.temperatures" :key="temp">
                    {{ temp }}°C
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <a-form-item label="权重值" class="form-item">
                <a-input-number
                  v-model="localWeightConfig.temperatures_weight"
                  :min="0.1"
                  :max="10"
                  :step="0.1"
                  :precision="1"
                  class="input-number"
                  @change="updateWeightConfig"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- SOC设置 -->
        <div class="weight-setting-group">
          <div class="weight-setting-header">SOC设置</div>
          <a-row :gutter="16" type="flex" align="middle">
            <a-col :xs="24" :sm="16" :md="16" :lg="16" :xl="16">
              <a-form-item label="SOC（%）选择" class="form-item">
                <a-select
                  mode="multiple"
                  v-model="localWeightConfig.selected_socs"
                  placeholder="选择SOC"
                  class="select-control"
                  @change="updateWeightConfig"
                >
                  <a-select-option v-for="soc in fittingData.socs" :key="soc">
                    {{ soc * 100 }}%
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <a-form-item label="权重值" class="form-item">
                <a-input-number
                  v-model="localWeightConfig.socs_weight"
                  :min="0.1"
                  :max="10"
                  :step="0.1"
                  :precision="1"
                  class="input-number"
                  @change="updateWeightConfig"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 存储天数设置 -->
        <div class="weight-setting-group">
          <div class="weight-setting-header">存储天数设置</div>
          <a-row :gutter="16" type="flex" align="middle">
            <a-col :xs="24" :sm="16" :md="16" :lg="16" :xl="16">
              <a-form-item label="存储天数范围" class="form-item">
                <div class="range-input-container">
                  <a-input-number
                    v-model="localWeightConfig.days_min"
                    placeholder="最小天数"
                    :min="0"
                    :max="localWeightConfig.days_max || 1000"
                    class="range-input"
                    @change="updateWeightConfig"
                  />
                  <span class="range-separator">~</span>
                  <a-input-number
                    v-model="localWeightConfig.days_max"
                    placeholder="最大天数"
                    :min="localWeightConfig.days_min || 0"
                    :max="10000"
                    class="range-input"
                    @change="updateWeightConfig"
                  />
                </div>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <a-form-item label="权重值" class="form-item">
                <a-input-number
                  v-model="localWeightConfig.days_weight"
                  :min="0.1"
                  :max="10"
                  :step="0.1"
                  :precision="1"
                  class="input-number"
                  @change="updateWeightConfig"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script>
import fittingMixin from '@/mixins/fittingMixin';

export default {
  name: 'OptimizationSettings',
  mixins: [fittingMixin],
  props: {
    fittingData: {
      type: Object,
      required: true
    },
    weightConfig: {
      type: Object,
      required: true
    },
    algorithmParams: {
      type: Object,
      required: true
    },
    optimizing: {
      type: Boolean,
      default: false
    }
  },
  data: () => ({
    localWeightConfig: null,
    localAlgorithmParams: null
  }),
  created() {
    this.localWeightConfig = {
      ...this.weightConfig,
      selected_days: this.weightConfig.selected_days || []
    };
    this.localAlgorithmParams = { ...this.algorithmParams };
  },
  watch: {
    weightConfig: {
      handler(newValue) {
        this.localWeightConfig = { ...newValue };
      },
      deep: true
    },
    algorithmParams: {
      handler(newValue) {
        this.localAlgorithmParams = { ...newValue };
      },
      deep: true
    }
  },
  methods: {
    updateWeightConfig() {
      this.$emit('weight-config-updated', this.localWeightConfig);
    },
    updateAlgorithmParams() {
      this.$emit('algorithm-params-updated', this.localAlgorithmParams);
    }
  }
};
</script>

<style scoped>
.weight-settings-container {
  padding: 12px;
}

.weight-setting-group {
  margin-bottom: 4px;
  padding: 4px 12px 2px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.weight-setting-group:last-child {
  margin-bottom: 0;
}

.weight-setting-header {
  font-size: 15px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 12px;
  padding-bottom: 6px;
  border-bottom: 1px solid #e8e8e8;
}

.form-item {
  margin-bottom: 8px;
}

:deep(.ant-form-item-label) {
  line-height: 22px;
  margin-bottom: 2px;
}

:deep(.ant-form-item-label > label) {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  height: 22px;
}

.select-control, .input-number {
  width: 100%;
  line-height: 1.5;
}

.range-input-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.range-input {
  width: 45%;
}

.range-separator {
  display: inline-block;
  width: 10%;
  text-align: center;
  font-weight: bold;
}

@media (max-width: 768px) {
  .weight-settings-container {
    padding: 10px;
  }

  .weight-setting-group {
    padding: 10px;
    margin-bottom: 12px;
  }

  :deep(.ant-form-item-label) {
    text-align: left;
  }
}

@media (max-width: 576px) {
  .range-input {
    width: 42%;
  }

  .range-separator {
    width: 16%;
  }
}
</style>