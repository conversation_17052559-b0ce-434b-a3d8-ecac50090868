<template>
  <a-row :gutter="16" class="model-details-section">
    <!-- 自变量卡片在前 -->
    <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" style="display: flex;">
      <div class="form-section variable-panel" style="width: 100%;">
        <h5>自变量信息</h5>

        <div class="variable-list">
          <div class="variable-content">
            <div
              v-for="variable in variables"
              :key="variable.name"
              class="variable-item"
            >
              <a-tooltip
                :title="variable.describe || '暂无说明'"
                placement="top"
              >
                <div class="variable-item-content">
                  <a-tag color="green" class="variable-tag">
                    <span v-html="renderLatex(variable.name)"></span>
                  </a-tag>
                  <span v-if="variable.describe" class="variable-description">
                    {{ variable.describe }}
                  </span>
                </div>
              </a-tooltip>
            </div>
          </div>
          <div v-if="variableDescription" class="variable-description-note">
            <a-alert
              message="变量说明"
              :description="variableDescription"
              type="info"
              show-icon
              style="margin-top: 10px;"
            />
          </div>
        </div>
      </div>
    </a-col>

    <!-- 系数卡片在后 -->
    <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" style="display: flex;">
      <div class="form-section coefficient-panel" style="width: 100%;">
        <h5>系数信息</h5>

        <div>
          <a-tabs :default-active-key="firstGroupKey" :active-key="activeTabKey" @change="handleCoefficientTabChange" :force-render="true" class="unified-tabs coefficient-tabs">
            <a-tab-pane v-for="group in groupedCoefficients" :key="`${group.letter}-group`" :tab="`${group.letter}组`" :force-render="true">
              <a-table
                :columns="coefficientColumns"
                :data-source="group.coefficients"
                row-key="name"
                size="small"
                :pagination="false"
                :scroll="{ x: false }"
              >
                <template slot="name" slot-scope="text">
                  <div class="latex-cell formula-container">
                    <!-- 加载指示器 - 无文字 -->
                    <div class="formula-loading"></div>
                    <div v-html="renderLatex(text)"></div>
                  </div>
                </template>
                <template slot="value" slot-scope="text, record">
                  <div class="coefficient-value">{{ formatCoefficientValue(record.value) }}</div>
                </template>
                <template slot="describe" slot-scope="text, record">
                  <div class="coefficient-description">{{ record.describe || '-' }}</div>
                </template>
              </a-table>
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>
    </a-col>
  </a-row>
</template>

<script>
import { renderMathJax } from '@/utils/mathUtils';
import predictionMixin from '@/mixins/predictionMixin';

export default {
  name: 'ModelInfoPanel',
  mixins: [predictionMixin],
  props: {
    variables: {
      type: Array,
      default: () => []
    },
    coefficients: {
      type: Array,
      default: () => []
    },
    variableDescription: {
      type: String,
      default: ''
    }
  },
  data: () => ({
    activeTabKey: null,
    coefficientColumns: [
      {
        title: '系数',
        dataIndex: 'name',
        key: 'name',
        width: '10%',
        scopedSlots: { customRender: 'name' },
      },
      {
        title: '值',
        dataIndex: 'value',
        key: 'value',
        width: '40%',
        scopedSlots: { customRender: 'value' },
      },
      {
        title: '说明',
        dataIndex: 'describe',
        key: 'describe',
        width: '50%',
        scopedSlots: { customRender: 'describe' },
      }
    ]
  }),
  computed: {
    // 按字母分组的系数
    groupedCoefficients() {
      return this.groupCoefficientsByLetter(this.coefficients);
    },
    // 获取首组参数的key
    firstGroupKey() {
      if (this.groupedCoefficients && this.groupedCoefficients.length > 0) {
        return `${this.groupedCoefficients[0].letter}-group`;
      }
      return '0';
    }
  },
  methods: {
    // 处理系数标签页切换
    handleCoefficientTabChange(activeKey) {
      // 更新当前激活的标签页
      this.activeTabKey = activeKey;

      // 标签页切换后，等待DOM更新完成再渲染
      this.$nextTick(() => {
        // 获取当前激活的标签页的索引
        const activeIndex = this.groupedCoefficients.findIndex(
          group => `${group.letter}-group` === activeKey
        );

        if (activeIndex !== -1) {
          // 使用更可靠的选择器：通过类名和索引选择当前激活的标签页
          const selector = `.ant-tabs-content .ant-tabs-tabpane:nth-child(${activeIndex + 1})`;
          // 强制立即渲染当前标签页内的LaTeX内容
          renderMathJax(true, selector);
        } else {
          // 如果找不到匹配的标签页，渲染整个标签页容器
          renderMathJax(true, '.coefficient-tabs');
        }
      });
    },
    // 处理系数值，避免将数字显示为LaTeX
    formatCoefficientValue(value) {
      if (value === null || value === undefined) return '-';

      // 对于数字类型，确保不使用科学计数法显示
      if (typeof value === 'number') {
        // 使用toFixed方法避免科学计数法，然后移除末尾的0
        return value.toFixed(20).replace(/\.?0+$/, '');
      }

      // 对于字符串类型，检查是否为科学计数法格式
      if (typeof value === 'string') {
        const numValue = parseFloat(value);
        if (!isNaN(numValue)) {
          return numValue.toFixed(20).replace(/\.?0+$/, '');
        }
      }

      return String(value);
    },
    // 设置首组参数的key
    setFirstGroupKey() {
      if (this.groupedCoefficients && this.groupedCoefficients.length > 0) {
        // 获取首组参数的key
        const firstGroup = this.groupedCoefficients[0];
        const newFirstGroupKey = `${firstGroup.letter}-group`;

        // 如果当前没有选中的标签页，或者选中的标签页不存在于当前的分组中，
        // 则将activeTabKey设置为firstGroupKey
        const groupKeys = this.groupedCoefficients.map(g => `${g.letter}-group`);
        if (!this.activeTabKey || !groupKeys.includes(this.activeTabKey)) {
          this.activeTabKey = newFirstGroupKey;
        }
      }
    }
  },
  mounted() {
    // 设置首组参数的key
    this.setFirstGroupKey();

    // 强制立即渲染一次MathJax，确保系数名称正确显示
    this.$nextTick(() => {
      renderMathJax(true);
    });
  },
  watch: {
    coefficients: {
      handler() {
        this.setFirstGroupKey();
        this.$nextTick(() => {
          renderMathJax(true);
        });
      },
      deep: true
    }
  }
};
</script>

<style scoped>
.model-details-section {
  margin-bottom: 24px;
}

.form-section {
  padding: 0;
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  height: 100%;
}

.form-section > div:not(h5) {
  padding: 16px;
}

h5 {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 0;
  color: #333;
  padding: 10px 0;
  background-color: #f5f5f5;
  border-bottom: 1px solid #f0f0f0;
  text-align: center;
  border-radius: 8px 8px 0 0;
}

/* 系数面板样式 */
.coefficient-panel {
  width: 100%;
}

/* 自变量面板样式 */
.variable-panel {
  width: 100%;
}

.variable-list {
  padding: 16px;
  background-color: #fff;
}

.variable-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.variable-item {
  width: 100%;
}

.variable-item-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
}

.variable-tag {
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  flex-shrink: 0;
}

.latex-cell {
  min-height: 24px;
  display: flex;
  align-items: center;
  padding: 4px 0;
}

.variable-description {
  color: #666;
  font-size: 12px;
  line-height: 1.4;
}

.coefficient-value {
  min-height: 24px;
  display: flex;
  align-items: center;
  padding: 4px 0;
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
}

.coefficient-description {
  color: #666;
  font-size: 12px;
}

.variable-description-note {
  margin-top: 10px;
}
</style>
