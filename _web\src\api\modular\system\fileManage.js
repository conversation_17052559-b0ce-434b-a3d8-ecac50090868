import { axios } from '@/utils/request'

/**
 * 分页查询文件信息表
 *
 * <AUTHOR>
 * @date 2020/6/30 00:20
 */
export function sysFileInfoPage (parameter) {
  return axios({
    url: '/sysFileInfo/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 获取全部文件信息表
 *
 * <AUTHOR>
 * @date 2020/6/30 00:20
 */
export function sysFileInfoList (parameter) {
  return axios({
    url: '/sysFileInfo/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 上传文件
 *
 * <AUTHOR>
 * @date 2020/6/30 00:20
 */
export function sysFileInfoUpload (parameter) {
  return axios({
    url: '/sysFileInfo/upload',
    method: 'post',
    data: parameter
  })
}

///sysFileInfo/uploadfile

export function sysUploadForFile (parameter) {
  return axios({
    url: '/sysFileInfo/uploadfile',
    method: 'post',
    data: parameter
  })
}

/**
 * 下载文件
 *
 * <AUTHOR>
 * @date 2020/6/30 00:20
 */
export function sysFileInfoDownload (parameter) {
  return axios({
    url: '/sysFileInfo/download',
    method: 'get',
    params: parameter,
    responseType: 'blob'
  })
}

/**
 * 查看图片
 *
 * <AUTHOR>
 * @date 2020/6/30 00:20
 */
export function sysFileInfoPreview (parameter) {
  return axios({
    url: '/sysFileInfo/preview',
    method: 'get',
    params: parameter,
    responseType: 'arraybuffer'
  })
}

/**
 * 查看详情文件信息表
 *
 * <AUTHOR>
 * @date 2020/6/30 00:20
 */
export function sysFileInfoDetail (parameter) {
  return axios({
    url: '/sysFileInfo/detail',
    method: 'get',
    params: parameter
  })
}

/**
 * 删除文件信息表
 *
 * <AUTHOR>
 * @date 2020/6/30 00:20
 */
export function sysFileInfoDelete (parameter) {
  return axios({
    url: '/sysFileInfo/delete',
    method: 'post',
    data: parameter
  })
}

/**
 * 获取在线文档配置
 *
 * <AUTHOR>
 * @date 2020/6/30 00:20
 */
export function sysFileInfoGetOnlineConfig (parameter) {
  return axios({
    url: '/sysFileInfo/getOnlineFileConfig',
    method: 'get',
    params: parameter
  })
}

// 获取minio下载url
export function getMinioDownloadUrl(fileId, fileName) {
  let url = '/minioFile/getDownloadUrl?fileId=' + fileId
  if (fileName) {
    url += '&fileName=' + fileName
  }
  return axios({
    url: url,
    method: 'post'
  })
}

// 获取minio预览url
export function getMinioPreviewUrl(fileId) {
  return axios({
    url: '/minioFile/getFileUrl?fileId='+fileId,
    method: 'post'
  })
}

// minio文件上传
export function minioUpload(parameter) {
  return axios({
    url: '/sysFileInfo/minioUpload',
    method: 'post',
    params:parameter
  })
}

// pbi文件转换为pdf
export function convertToPdf(parameter) {
  return axios({
    url: 'http://10.5.65.248:8111/open/file/convertToPdf',
    method: 'get',
    params:parameter
  })
}
