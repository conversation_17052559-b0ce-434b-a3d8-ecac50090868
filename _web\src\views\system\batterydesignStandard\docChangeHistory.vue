<template>
  <div>
    <div style="margin: 35px 0px 10px 15px;">
      <a-breadcrumb :routes="routes" separator=">">
        <template slot="itemRender" slot-scope="{ route, routes }">
          <router-link v-if="routes.indexOf(route) === 1" :to="route.path">
            {{ route.breadcrumbName }}
          </router-link>
          <span v-else-if="routes.indexOf(route) === routes.length - 1" >
            {{ route.breadcrumbName }}
          </span>
          <router-link v-else :to="route.path">
            {{ route.breadcrumbName }}
          </router-link>
        </template>
      </a-breadcrumb>
    </div>
    <div>
      <p style="font-size: 8px;margin-left: 15px;color: black;font-weight: bold;">填写说明：1.仅限于工艺部门维护</p>
      <p style="font-size: 8px;margin-left: 75px;margin-bottom:15px;color: black;font-weight: bold;">2.附图文件版本需同MI版本一致</p>
    </div>
    <div style="margin-bottom: 30px">
      <a-table
        id="develop"
        :columns="columns"
        :data-source="resultData"
        :row-key="(record) => record.id"
        :pagination="false"
        :scroll="{ y: windowHeight }"
        bordered
      >
      <div slot="version" slot-scope="text,record">
        <input :value="record.version != null ? text : '/'"
               @change="updateData($event,record,'version')"/>
      </div>
      <div slot="changeContent" slot-scope="text,record">
        <a-textarea v-model="record.changeContent" :auto-size="{ minRows: 1, maxRows: 25 }"
              style="text-align: center" @blur="updateData($event,record,'changeContent')"/>
      </div>
      <div slot="changeReason" slot-scope="text,record">
        <input :value="record.changeReason != null ? text : '/'"
               @change="updateData($event,record,'changeReason')"/>
      </div>
      <div slot="changeDate" slot-scope="text,record">
        <input :value="record.changeDate != null ? text : '/'"
          @change="updateData($event,record,'changeDate')"/>
      </div>
      <div slot="changeBy" slot-scope="text,record">
        <input :value="record.changeBy != null ? text : '/'" style="width:80%"
               @change="updateData($event,record,'changeBy')"/>
        <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => deleteChangeHistory(record.id)">
          <a-icon type="minus-circle" style="color: red;margin-left: 7px" :style="{paddingLeft: bigClient?'2.1%':'1.85%'}"/>
        </a-popconfirm>
      </div>
      <template slot="title">
        <table>
          <tr>
            <td style="padding: 0;border: 0;width: 13%;"></td>
            <td style="padding: 0;border: 0;width: 10%;"></td>
            <td style="padding: 0;border: 0;width: 11%;"></td>
            <td style="padding: 0;border: 0;width: 11%;"></td>
            <td style="padding: 0;border: 0;width: 11%;"></td>
            <td style="padding: 0;border: 0;width: 11%;"></td>
            <td style="padding: 0;border: 0;width: 11%;"></td>
            <td style="padding: 0;border: 0;width: 9%;"></td>
            <td style="padding: 0;border: 0;width: 9%;"></td>
            <td style="padding: 0;border: 0;width: 9%;"></td>
            <td style="padding: 0;border: 0;width: 8%;"></td>
            <td style="padding: 0;border: 0;width: 8%;"></td>
            <td style="padding: 0;border: 0;width: 9%;"></td>
          </tr>
          <tr class="renderTr">
            <td colspan="13">
              <img src="/img/logo.53575418.png" alt="logo" style="float:left;width: 80px;height: 50px">
            </td>
          </tr>
          <tr class="renderTr">
            <td style="color: black;font-weight: bold;">文件名称</td>
            <td colspan="6">
              <input :value="libraryData.documentName"
                     @change="updateLibData($event, libraryData, 'documentName')"/>
            </td>
            <td style="color: black;font-weight: bold;">受控号</td>
            <td colspan="3">
              <input :value="libraryData.controlledNumber"
                     @change="updateLibData($event, libraryData, 'controlledNumber')"/>
            </td>
            <td style="color: black;font-weight: bold;">页次</td>
            <td>
              <input :value="libraryData.page"
                     @change="updateLibData($event, libraryData, 'page')"/>
            </td>
          </tr>
          <tr class="renderTr">
            <td style="color: black;font-weight: bold;">文件编号</td>
            <td colspan="6">
              <input :value="libraryData.documentNo"
                     @change="updateLibData($event, libraryData, 'documentNo')"/>
            </td>
            <td style="color: black;font-weight: bold;">实施日期</td>
            <td colspan="3">
              <input :value="libraryData.implementDate"
                     @change="updateLibData($event, libraryData, 'implementDate')"/>
            </td>
            <td style="color: black;font-weight: bold;">版本</td>
            <td>
              <input :value="libraryData.version"
                     @change="updateLibData($event, libraryData, 'version')"/>
            </td>
          </tr>
          <tr class="renderTr">
            <td style="color: black;font-weight: bold;">文件状态</td>
            <td colspan="2">
              <a-checkbox @change="getCheckBox(0)" :checked="libraryData.documentStatus == 0 ? true : false"></a-checkbox>
              <span style="color: black;font-weight: bold;margin-left: 15px;">制定</span>
            </td>
            <!-- <td style="color: black;font-weight: bold;">制定</td> -->
            <td colspan="2">
              <a-checkbox @change="getCheckBox(1)" :checked="libraryData.documentStatus == 1 ? true : false"></a-checkbox>
              <span style="color: black;font-weight: bold;margin-left: 15px;">修订</span>
            </td>
            <!-- <td style="color: black;font-weight: bold;">修订</td> -->
            <td colspan="2">
              <a-checkbox @change="getCheckBox(2)"  :checked="libraryData.documentStatus == 2 ? true : false"></a-checkbox>
              <span style="color: black;font-weight: bold;margin-left: 15px;">作废</span>

            </td>
            <!-- <td style="color: black;font-weight: bold;">作废</td> -->
            <td style="color: black;font-weight: bold;">文件等级</td>
            <td colspan="3">
              <a-checkbox @change="getCheckBox(3)" :checked="libraryData.documentLevel == 1 ? true : false"></a-checkbox>
              <span style="color: black;font-weight: bold;margin-left: 15px;">管制</span>

            </td>
            <!-- <td style="color: black;font-weight: bold;">管制</td> -->
            <td colspan="2">
              <a-checkbox @change="getCheckBox(4)" :checked="libraryData.documentLevel == 0 ? true : false"></a-checkbox>
              <span style="color: black;font-weight: bold;margin-left: 15px;">非管制</span>
              
            </td>
            <!-- <td style="color: black;font-weight: bold;" colspan="2">非管制</td> -->
          </tr>
          <tr class="renderTr">
            <td colspan="13" style="font: bold normal 13px arial;height: 30px">
              文件变更履历
            </td>
          </tr>
        </table>
      </template>
      <template slot="footer" slot-scope="currentPageData">
        <table>
          <tr>
            <td style="padding: 0;border: 0;width: 9.95%;"></td>
            <td style="padding: 0;border: 0;width: 16.67%;"></td>
            <td style="padding: 0;border: 0;width: 16.67%;"></td>
            <td style="padding: 0;border: 0;width: 16.67%;"></td>
            <td style="padding: 0;border: 0;width: 14%;"></td>
            <td style="padding: 0;border: 0;width: 13.02%;"></td>
            <td style="padding: 0;border: 0;width: 13.02%;"></td>
          </tr>
          <tr style="border: 1px solid black;">
            <td id="iconPlusTd">
              <a-icon type="plus" slot="footer" :style="{paddingLeft: bigClient?'2.1%':'1.85%'}"
                      @click="addMIChangeHistory(libraryData)"/>
            </td>
          </tr>
          <tr>
            <td colspan="13" style="height: 30px"><span style="float:left">注：“更改原因”栏填制更改的证据。例①：技术变更，填写***变更，详见技术图纸编号***。例②：工艺变更，填写***变更，详见报告编号***。
              例③：物料变更，填写***变更，详见ECN编号***。例④：其它变更，如更改作业图片等，可直接在更改原因栏注明。</span></td>
          </tr>
          <tr>
            <td>发放范围</td>
            <td>
              <input :value="libraryData.distributionScope1"
                     @change="updateLibData($event, libraryData, 'distributionScope1')"/>
            </td>
            <td>
              <input :value="libraryData.distributionScope2"
                     @change="updateLibData($event, libraryData, 'distributionScope2')"/>
            </td>
            <td>
              <input :value="libraryData.distributionScope3"
                     @change="updateLibData($event, libraryData, 'distributionScope3')"/>
            </td>
            <td>
              <input :value="libraryData.distributionScope4"
                     @change="updateLibData($event, libraryData, 'distributionScope4')"/>
            </td>
            <td>
              <input :value="libraryData.distributionScope5"
                     @change="updateLibData($event, libraryData, 'distributionScope5')"/>
            </td>
            <td>
              <input :value="libraryData.distributionScope6"
                     @change="updateLibData($event, libraryData, 'distributionScope6')"/>
            </td>
          </tr>
          <tr>
            <td>发放份数</td>
            <td>
              <input :value="libraryData.distributionNum1"
                     @change="updateLibData($event, libraryData, 'distributionNum1')"/>
            </td>
            <td>
              <input :value="libraryData.distributionNum2"
                     @change="updateLibData($event, libraryData, 'distributionNum2')"/>
            </td>
            <td>
              <input :value="libraryData.distributionNum3"
                     @change="updateLibData($event, libraryData, 'distributionNum3')"/>
            </td>
            <td>
              <input :value="libraryData.distributionNum4"
                     @change="updateLibData($event, libraryData, 'distributionNum4')"/>
            </td>
            <td>
              <input :value="libraryData.distributionNum5"
                     @change="updateLibData($event, libraryData, 'distributionNum5')"/>
            </td>
            <td>
              <input :value="libraryData.distributionNum6"
                     @change="updateLibData($event, libraryData, 'distributionNum6')"/>
            </td>
          </tr>
        </table>
      </template>
    </a-table>
      <p style="margin:5px 0px 0px 75px;font: bold normal 13px arial;color: black">表单编号：EP-IATF-16-45A</p>
    </div>
  </div>
</template>
<script>
import {
  getMIStandardLibById,
  updateMIStandardLib
} from '@/api/modular/system/gCylinderMILibManage';
import {
  getMIChangeHistoryList,
  insertMIChangeHistory,
  updateMIChangeHistory
} from "@/api/modular/system/miChangeHistoryManage";
  export default {
    components: {
    },
    data() {
      return {
        // 表头
        columns: [
          {
            title: '版本',
            dataIndex: 'version',
            scopedSlots: { customRender: 'version' },
            align: 'center',
            width: '10%',
            // customRender: (text, record, index) => `${index + 1}`
          },
          {
            title: '更改内容',
            dataIndex: 'changeContent',
            scopedSlots: { customRender: 'changeContent' },
            align: 'center',
            width: '50%',
          },
          {
            title: '更改原因',
            dataIndex: 'changeReason',
            width: '14%',
            align: 'center',
            scopedSlots: { customRender: 'changeReason' },
          },
          {
            title: '日期',
            dataIndex: 'changeDate',
            align: 'center',
            width: '13%',
            scopedSlots: { customRender: 'changeDate' },
            // colSpan: 4,
          },
          {
            title: '变更人',
            dataIndex: 'changeBy',
            align: 'center',
            // colSpan: 1,
            width: '13%',
            scopedSlots: { customRender: 'changeBy' },
          },
    ],
        visible: false,
        editVisible: false,
        confirmLoading: false,
        mIStandardLibData: {},
        bigClient: document.documentElement.clientHeight > 700,
        windowHeight: document.documentElement.clientHeight - 200,
        form: this.$form.createForm(this,{ name: 'form' }),
        editForm: this.$form.createForm(this,{ name: 'editForm' }),
        resultData: [],
        libraryData: {},
        routes: [
          {
            path: '/batterydesignStandard',
            breadcrumbName: '标准规范',
          },
          // {
          //   breadcrumbName: 'G圆柱',
          // },
          {
            path: '/g_cylinder_mi_library',
            breadcrumbName: 'G圆柱 MI设计与组合',
          },
          {
            breadcrumbName: 'xxx',
          },
        ],
      };
    },
    created() {
      this.routes[2].breadcrumbName = this.$route.query.documentChangeHistoryName
      this.getMIStandardLib(this.$route.query.libraryId)
      this.getDataByLibraryId(this.$route.query.libraryId)
    },
    mounted() {
      document.getElementsByClassName("ant-layout-content")[0].style.backgroundColor = 'white';
    },
    methods: {
      getMIStandardLib(id) {
        getMIStandardLibById({ id: id }).then((res) => {
          if (res.success) {
            this.libraryData = res.data[0]
          } else {
            this.$message.error(res.message)
          }
        })
      },
      getCheckBox(option) {
        if (option === 0) {
          this.libraryData.documentStatus = 0
        } else if (option === 1) {
          this.libraryData.documentStatus = 1
        } else if (option === 2) {
          this.libraryData.documentStatus = 2
        } else if (option === 3) {
          this.libraryData.documentLevel = 1
        } else {
          this.libraryData.documentLevel = 0
        }
        let param = {}
        param['documentStatus'] = this.libraryData.documentStatus
        param['documentLevel'] = this.libraryData.documentLevel
        param['id'] = this.libraryData.id
        updateMIStandardLib(param).then((res) => {
          this.$nextTick(() => {
            if (res.success) {
              this.$message.success('保存成功')
            } else {
              this.$message.error(res.message)
            }
          })
        })
      },
      deleteChangeHistory(id) {
        updateMIChangeHistory({ id: id, status: 2 }).then(() => this.getDataByLibraryId(this.libraryData.id))
      },
      addMIChangeHistory(data) {
        let params = {}
        params.libraryId = data.id
        insertMIChangeHistory(params).then(() => this.getDataByLibraryId(this.libraryData.id))
      },
      updateData(event, record, column) {
        //修改时禁止输入
        let inputs = document.getElementsByTagName("input");
        let textareas = document.getElementsByTagName("textarea");
        let controlInput = [];
        let controltextarea = [];
        for (let i = 0; i < inputs.length; i++) {
          if (!inputs[i].disabled) {
            controlInput.push(inputs[i])
          }
        }
        for (let i = 0; i < textareas.length; i++) {
          if (!textareas[i].disabled) {
            controltextarea.push(textareas[i])
          }
        }
        for (let i = 0; i < controlInput.length; i++) {
          controlInput[i].disabled = true
        }
        for (let i = 0; i < controltextarea.length; i++) {
          controltextarea[i].disabled = true
        }
        let param = {}
        param[column] = event.target.value
        param['id'] = record.id
        updateMIChangeHistory(param).then((res) => {
          this.$nextTick(() => {
            if (res.success) {
              this.$message.success('保存成功')
            } else {
              this.$message.error(res.message)
            }
            for (let i = 0; i < controlInput.length; i++) {
              controlInput[i].disabled = false
            }
            for (let i = 0; i < controltextarea.length; i++) {
              controltextarea[i].disabled = false
            }
          })
        })
      },
      updateLibData(event, record, column) {
        //修改时禁止输入
        let inputs = document.getElementsByTagName("input");
        let textareas = document.getElementsByTagName("textarea");
        let controlInput = [];
        let controltextarea = [];
        for (let i = 0; i < inputs.length; i++) {
          if (!inputs[i].disabled) {
            controlInput.push(inputs[i])
          }
        }
        for (let i = 0; i < textareas.length; i++) {
          if (!textareas[i].disabled) {
            controltextarea.push(textareas[i])
          }
        }
        for (let i = 0; i < controlInput.length; i++) {
          controlInput[i].disabled = true
        }
        for (let i = 0; i < controltextarea.length; i++) {
          controltextarea[i].disabled = true
        }
        let param = {}
        param[column] = event.target.value
        param['id'] = record.id
        updateMIStandardLib(param).then((res) => {
          this.$nextTick(() => {
            if (res.success) {
              this.$message.success('保存成功')
            } else {
              this.$message.error(res.message)
            }
            for (let i = 0; i < controlInput.length; i++) {
              controlInput[i].disabled = false
            }
            for (let i = 0; i < controltextarea.length; i++) {
              controltextarea[i].disabled = false
            }
          })
        })
      },
      getDataByLibraryId(libraryId) {
        getMIChangeHistoryList({ libraryId: libraryId, status: 2 }).then(res => {
          this.resultData = res.data
          console.log('res.data:' + res.data)
        })
      },
    }
  }
</script>
<style lang="less" scoped>
/deep/.ant-table-thead > tr > th {
  background: white;
  padding: 12px 8px;
  font-weight: bold;
  border: 1px solid black;
}
/deep/.ant-table-bordered .ant-table-tbody > tr > td {
  border: 1px solid black;
}
/deep/.ant-table-bordered .ant-table-body > table {
  border-collapse: collapse;
  border-spacing: 0;
}
/deep/.ant-table-bordered .ant-table-header > table {
  border-collapse: collapse;
  border-spacing: 0;
  margin-top: -1px;
}
/deep/.ant-table-thead > tr > th {
  border-bottom: 0;
  border-top: 0;
}
/deep/.ant-table-bordered .ant-table-title > table {
  border-collapse: collapse;
  border-spacing: 0;
  margin-bottom: 1px;
}
/deep/.ant-table-bordered .ant-table-title > table > tr > td {
  border: 1px solid black;
}
/deep/.ant-table-bordered .ant-table-footer > table {
  border-collapse: collapse;
  border-spacing: 0;
  margin-top: -1px;
}
/deep/.ant-table-bordered .ant-table-footer > table > tr > td {
  border: 1px solid black;
}
#iconPlusTd {
  border-top: #e8e8e8 solid 0px;
}
#develop {
  font-size: 12px;
  margin: 0px 70px 0px 70px;
  color: #000;
}
textarea.ant-input {
  max-width: 100%;
  height: auto;
  min-height: 32px;
  line-height: 1.5;
  vertical-align: bottom;
  -webkit-transition: all 0.3s, height 0s;
  transition: all 0.3s, height 0s;
  border: none;
}
/deep/.ant-table-bordered.ant-table-empty .ant-table-placeholder {
  border: 1px solid black;
}
/deep/.ant-form-item {
    margin-bottom: 0px;
  }
  .renderTr td{
    border: #e8e8e8 solid 1px;
    text-align: center;
  }
  /deep/.ant-table.ant-table-bordered .ant-table-title {
     padding-right: 0px;
     padding-left: 0px;
    border: 1px solid #e8e8e8;
  }
  /deep/.ant-table-title {
    position: relative;
    padding: 0px;
    border-radius: 2px 2px 0 0;
  }
  /deep/.ant-table-footer {
    position: relative;
    padding: 0px;
    border-radius: 2px 2px 0 0;
    background-color: white;
  }
  /deep/.ant-table-footer tr td {
    border-right: #e8e8e8 solid 1px;
    border-top: #e8e8e8 solid 1px;
    text-align: center;
  }
  input {
    width: 100%;
    height: 25px;
    margin: 0;
    border: 0;
    outline: none;
    text-align: center;
  }
//   /deep/.ant-breadcrumb a, .ant-breadcrumb span {
//   color: black;
//   font-weight: bold;
// }

// /deep/.ant-breadcrumb > span:last-child {
//   color: black;
//   font-weight: bold;
// }

// .ant-breadcrumb-separator {
//   color: black;
//   font-weight: bold;
// }
</style>
