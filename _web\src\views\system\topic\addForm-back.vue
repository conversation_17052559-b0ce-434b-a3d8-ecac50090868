<template>
    <a-modal title="新增立项申请" :width="1200" :visible="visible" :confirmLoading="confirmLoading"
             @ok="handleSubmit" @cancel="handleCancel">


      <template slot="footer">
        <a-button key="back" @click="handleCancel">
          取消
        </a-button>

        <a-popconfirm key="submit" placement="topRight" title="请确认立项申请信息是否无误，点击确定后将提交立项审批流程"
                      :visible="visible1"
                      @confirm="handleSubmit">
          <a-button type="primary" @click="handleSubmitBefore">
            确认
          </a-button>
        </a-popconfirm>

      </template>





      <a-spin :spinning="confirmLoading">
            <a-form :form="form">
                <a-row :gutter="24">
                    
                    <a-col :md="8" :sm="24">
                        <a-form-item label="课题名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                            <a-input  placeholder="请输入课题名称" v-decorator="['projectName', {rules: [{required: true, message: '请输入课题名称!'}]}]"></a-input>
                        </a-form-item>
                    </a-col>
                    <a-col :md="8" :sm="24">
                        <a-form-item label="分类" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                            <a-input type='hidden'  v-decorator="['projectCate', {rules: [{required: true, message: '请选择分类！'}]}]" />
                            <a-tree-select v-model="projectCate" allow-clear @change="this.changeCate"  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" :tree-data="cate" placeholder="请选择分类" tree-default-expand-all>
                            </a-tree-select>
                        </a-form-item>
                    </a-col>
                    <a-col :md="8" :sm="24">
                        <a-form-item label="部门" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                            <a-input type='hidden'  v-decorator="['departmentCate', {rules: [{required: true, message: '请选择部门！'}]}]" />
                            <a-tree-select v-model="departmentCate" allow-clear @change="this.changeDept"  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" :tree-data="depts" placeholder="请选择部门" tree-default-expand-all>
                            </a-tree-select>
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row :gutter="24">
                    <a-col :md="8" :sm="24">
                        <a-form-item label="项目管理员" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                            <a-input type='hidden' v-decorator="['projectManager', {rules: [{required: true, message: '请选择项目管理员！'}]}]" />
                            <a-dropdown v-model="dropdownvisible" placement="bottomCenter" :trigger="['click']">
                                <a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">{{projectManagerName ? projectManagerName : '选择责任人'}}
                                    <a-icon type="down" /></a-button>
                                    <a-menu slot="overlay">
                                        <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                                            <a-input-search v-model="queryParam.searchValue" placeholder="搜索..." @change="onSearch" />
                                            <s-table style="width:100%;" ref="table" :rowKey="(record) => record.id" :columns="vcolumns" :data="loadData" :customRow="customRow" :scroll="{ y: 120,x:120}">>
                                            </s-table>
                                        </a-spin>
                                    </a-menu>
                            </a-dropdown>
                        </a-form-item>
                    </a-col>

                    <a-col :md="8" :sm="24">
                        <a-form-item label="直属领导" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                            <a-input type='hidden' v-decorator="['directLeader', {rules: [{required: true, message: '请选择直属领导！'}]}]" />
                            <a-dropdown v-model="ddropdownvisible" placement="bottomCenter" :trigger="['click']">
                                <a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">{{directLeaderName ? directLeaderName : '选择直属领导'}}
                                    <a-icon type="down" /></a-button>
                                    <a-menu slot="overlay">
                                        <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                                            <a-input-search v-model="dqueryParam.searchValue" placeholder="搜索..." @change="ondSearch" />
                                            <s-table style="width:100%;" ref="dtable" :rowKey="(record) => record.id" :columns="vcolumns" :data="loaddData" :customRow="customdRow" :scroll="{ y: 120,x:120}">>
                                            </s-table>
                                        </a-spin>
                                    </a-menu>
                            </a-dropdown>
                        </a-form-item>
                    </a-col>

                    <a-col :md="8" :sm="24">
                        <a-form-item label="所长" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                            <a-input type='hidden' v-decorator="['headOfTheInstitute', {rules: [{required: true, message: '请选择所长！'}]}]" />
                            <a-dropdown v-model="hdropdownvisible" placement="bottomCenter" :trigger="['click']">
                                <a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">{{headOfTheInstituteName ? headOfTheInstituteName : '选择所长'}}
                                    <a-icon type="down" /></a-button>
                                    <a-menu slot="overlay">
                                        <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                                            <a-input-search v-model="hqueryParam.searchValue" placeholder="搜索..." @change="onhSearch" />
                                            <s-table style="width:100%;" ref="htable" :rowKey="(record) => record.id" :columns="vcolumns" :data="loadhData" :customRow="customhRow" :scroll="{ y: 120,x:120}">>
                                            </s-table>
                                        </a-spin>
                                    </a-menu>
                            </a-dropdown>
                        </a-form-item>
                    </a-col>

                </a-row>

                <a-row :gutter="24">

                    <a-col :md="8" :sm="24">
                        <a-form-item label="总监" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                            <a-input type='hidden' v-decorator="['inspectorGeneral', {rules: [{required: true, message: '请选择总监！'}]}]" />
                            <a-dropdown v-model="idropdownvisible" placement="bottomCenter" :trigger="['click']">
                                <a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">{{inspectorGeneralName ? inspectorGeneralName : '选择总监'}}
                                    <a-icon type="down" /></a-button>
                                    <a-menu slot="overlay">
                                        <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                                            <a-input-search v-model="iqueryParam.searchValue" placeholder="搜索..." @change="oniSearch" />
                                            <s-table style="width:100%;" ref="itable" :rowKey="(record) => record.id" :columns="vcolumns" :data="loadiData" :customRow="customiRow" :scroll="{ y: 120,x:120}">>
                                            </s-table>
                                        </a-spin>
                                    </a-menu>
                            </a-dropdown>
                        </a-form-item>
                    </a-col>

                  <a-col :md="8" :sm="24">
                        <a-form-item label="课题分类" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                          <a-select v-decorator="['platformAndTopic', {rules: [{required: true, message: '请选择课题分类！'}]}]" placeholder="请选择课题分类">
                            <a-select-option :value="parseInt(1)">
                              平台
                            </a-select-option>
                            <a-select-option :value="parseInt(2)">
                              课题
                            </a-select-option>
                          </a-select>
                        </a-form-item>
                    </a-col>

                  <a-col :md="8" :sm="24">
                        <a-form-item label="项目等级" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                          <a-select v-decorator="['projectLevel', {rules: [{required: true, message: '请选择项目等级！'}]}]" placeholder="请选择项目等级">

                            <a-select-option :value="parseInt(1)">
                              S+
                            </a-select-option>
                            <a-select-option :value="parseInt(2)">
                              S
                            </a-select-option>
                            <a-select-option :value="parseInt(3)">
                              A
                            </a-select-option>
                            <a-select-option :value="parseInt(4)">
                              B
                            </a-select-option>
                            <a-select-option :value="parseInt(5)">
                              C
                            </a-select-option>
                          </a-select>
                        </a-form-item>
                    </a-col>
                    

                    <a-form-item style="display: none;">
                        <a-input v-decorator="['president']" />
                    </a-form-item>

                    <!-- <a-col :md="8" :sm="24">
                        <a-form-item label="院长" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                            <a-input type='hidden' v-decorator="['president', {rules: [{required: true, message: '请选择院长！'}]}]" />
                            <a-dropdown v-model="pdropdownvisible" placement="bottomCenter" :trigger="['click']">
                                <a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">{{presidentName ? presidentName : '选择院长'}}
                                    <a-icon type="down" /></a-button>
                                    <a-menu slot="overlay">
                                        <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                                            <a-input-search v-model="pqueryParam.searchValue" placeholder="搜索..." @change="onpSearch" />
                                            <s-table style="width:100%;" ref="ptable" :rowKey="(record) => record.id" :columns="vcolumns" :data="loadpData" :customRow="custompRow" :scroll="{ y: 120,x:120}">>
                                            </s-table>
                                        </a-spin>
                                    </a-menu>
                            </a-dropdown>
                        </a-form-item>
                    </a-col> -->


                </a-row>

                
                <a-row :gutter="24">
                    
                    <!--<a-col :md="8" :sm="24">
                        <a-form-item label="项目目的" :labelCol="labelCol" :wrapperCol="wrapperCol">
                            <a-textarea :rows="2" placeholder="请输入项目目的" v-decorator="['projectPurpose', {rules: [{required: true, message: '请输入项目目的!'}]}]"></a-textarea>
                        </a-form-item>
                    </a-col>-->
                    <a-col :md="8" :sm="24">
                        <a-form-item label="课题背景" :labelCol="labelCol" :wrapperCol="wrapperCol">
                            <a-textarea :rows="2" placeholder="请输入课题背景" v-decorator="['projectBackGround', {rules: [{required: true, message: '请填写课题背景!'}]}]"></a-textarea>
                        </a-form-item>
                    </a-col>

                    <a-col :md="8" :sm="24">
                        <a-form-item label="研究内容" :labelCol="labelCol" :wrapperCol="wrapperCol">
                            <a-textarea :rows="2" placeholder="请输入研究内容" v-decorator="['researchContent', {rules: [{required: true, message: '请填写研究内容!'}]}]"></a-textarea>
                        </a-form-item>
                    </a-col>
                </a-row>
                <!--<a-row :gutter="24">
                    <a-col :md="8" :sm="24">
                        <a-form-item label="项目目标" :labelCol="labelCol" :wrapperCol="wrapperCol">
                            <a-textarea :rows="2" placeholder="请输入项目目标" v-decorator="['projectTarget', {rules: [{required: true, message: '请填写项目目标!'}]}]"></a-textarea>
                        </a-form-item>
                    </a-col>
                </a-row>-->
            </a-form>
        </a-spin>
    </a-modal>
</template>

<script>
    import {
        getCateTree,
        addOrUpdateReview
    } from '@/api/modular/system/topic'
    import {
        getUserLists
    } from '@/api/modular/system/userManage'
    import {
        STable
    } from '@/components'
    export default {
        props: {
            issueId: {
                type: Number,
                default: 0
            },
        },
        components: {
            STable
        },
        data() {
            return {
                queryParam: {},

                dqueryParam: {},//直属领导
                hqueryParam:{},//所长
                iqueryParam:{},//总监
                pqueryParam:{},//院长

                loading: false,
                
                cate:[],
                depts:[],

                dropdownvisible: false,
                ddropdownvisible: false,//直属领导
                hdropdownvisible:false,//所长
                idropdownvisible:false,//总监
                pdropdownvisible:false,//院长

                projectCate:null,
                departmentCate:null,

                vcolumns: [{
                        title: '账号',
                        dataIndex: 'account'
                    },
                    {
                        title: '姓名',
                        dataIndex: 'name'
                    },
                ],
                loadData: parameter => {
                    return getUserLists(Object.assign(parameter, this.queryParam)).then((res) => {
                        return res.data
                    })
                },

                //直属领导
                loaddData: parameter => {
                    return getUserLists(Object.assign(parameter, this.dqueryParam)).then((res) => {
                        return res.data
                    })
                },
                //所长
                loadhData: parameter => {
                    return getUserLists(Object.assign(parameter, this.hqueryParam)).then((res) => {
                        return res.data
                    })
                },
                //总监
                loadiData: parameter => {
                    return getUserLists(Object.assign(parameter, this.iqueryParam)).then((res) => {
                        return res.data
                    })
                },

                //院长
                loadpData: parameter => {
                    return getUserLists(Object.assign(parameter, this.pqueryParam)).then((res) => {
                        return res.data
                    })
                },

                labelCol: {
                    xs: {
                        span: 24
                    },
                    sm: {
                        span: 8
                    }
                },
                wrapperCol: {
                    xs: {
                        span: 24
                    },
                    sm: {
                        span: 16
                    }
                },
                visible: false,
                visible1: false,
                confirmLoading: false,
                form: this.$form.createForm(this),
                projectManagerName: '',//项目责任人
                directLeaderName: '',//直属领导
                headOfTheInstituteName:'',//所长
                inspectorGeneralName:'',//总监
                presidentName:'',//院长
            }
        },
        methods: {
            
            onSearch(e) {
                this.$refs.table.refresh()
            },

            //直属领导
            ondSearch(e) {
                this.$refs.dtable.refresh()
            },
            //所长
            onhSearch(e) {
                this.$refs.htable.refresh()
            },
            //总监
            oniSearch(e) {
                this.$refs.itable.refresh()
            },
            //院长
            onpSearch(e) {
                this.$refs.ptable.refresh()
            },

            // 初始化方法
            add() {
                setTimeout(() => {
                    this.form.setFieldsValue(
                        {
                            president:'029026'
                        }
                    )
                }, 100)
                this.visible = true
            },
            customRow(row, index) {
                return {
                    on: {
                        click: () => {
                            this.form.setFieldsValue({
                                projectManager: row.account
                            })
                            this.projectManagerName = row.name
                            this.dropdownvisible = false
                        }
                    }
                }
            },

            //直属领导
            customdRow(row, index) {
                return {
                    on: {
                        click: () => {
                            this.form.setFieldsValue({
                                directLeader: row.account
                            })
                            this.directLeaderName = row.name
                            this.ddropdownvisible = false
                        }
                    }
                }
            },
            //所长
            customhRow(row, index) {
                return {
                    on: {
                        click: () => {
                            this.form.setFieldsValue({
                                headOfTheInstitute: row.account
                            })
                            this.headOfTheInstituteName = row.name
                            this.hdropdownvisible = false
                        }
                    }
                }
            },
            //总监
            customiRow(row, index) {
                return {
                    on: {
                        click: () => {
                            this.form.setFieldsValue({
                                inspectorGeneral: row.account
                            })
                            this.inspectorGeneralName = row.name
                            this.idropdownvisible = false
                        }
                    }
                }
            },
            //院长
            custompRow(row, index) {
                return {
                    on: {
                        click: () => {
                            this.form.setFieldsValue({
                                president: row.account
                            })
                            this.presidentName = row.name
                            this.pdropdownvisible = false
                        }
                    }
                }
            },

            handleSubmit() {

                const {
                    form: {
                        validateFields
                    }
                } = this
                this.confirmLoading = true
                validateFields((errors, values) => {
                    if (!errors) {
                        let $params = { ...values,
                            issueId:this.issueId,
                        }
                        addOrUpdateReview($params).then((res) => {
                            if (res.success) {
                                if (res.data) {
                                    this.$message.success('新增成功')
                                    this.$emit('ok')
                                    this.handleCancel()
                                } else {
                                    this.$message.error('新增失败')
                                }
                            } else {
                                this.$message.error('新增失败：' + res.message)
                            }
                        }).finally((res) => {
                            this.confirmLoading = false
                        })
                    } else {
                        this.confirmLoading = false
                    }
                })
            },
          handleSubmitBefore() {
            const {
              form: {
                validateFields
              }
            } = this
            this.confirmLoading = true
            validateFields((errors, values) => {
              if (!errors) {
                this.visible1 = true
              }
              this.confirmLoading = false
            })
            },


            handleCancel() {
                this.confirmLoading = false
                this.projectManagerName = ''
                this.directLeaderName = ''// 直属领导
                this.headOfTheInstituteName = ''//所长
                this.inspectorGeneralName = ''//总监
                this.presidentName = ''//院长
                this.form.resetFields()
                this.visible = false
                this.visible1 = false
                this.projectCate = null
                this.departmentCate = null
            },

            callGetDeptTree(){
                this.confirmLoading = true
                getCateTree({
                    fieldName:'department'
                }).then((res)=>{
                    if (res.success) {
                        this.depts = res.data
                    } else {
                        this.$message.error('错误提示：' + res.message, 1)
                    }
                    this.confirmLoading = false
                }).catch((err) => {
                    this.confirmLoading = false
                    this.$message.error('错误提示：' + err.message, 1)
                });
            },

            callGetTree(){
                this.confirmLoading = true
                getCateTree({
                    fieldName:'projectCate'
                }).then((res)=>{
                    if (res.success) {
                        this.cate = res.data
                    } else {
                        this.$message.error('错误提示：' + res.message, 1)
                    }
                    this.confirmLoading = false
                }).catch((err) => {
                    this.confirmLoading = false
                    this.$message.error('错误提示：' + err.message, 1)
                });
            },

            changeCate(value, label, extra){
                this.form.setFieldsValue({
                    projectCate: value
                })
                this.projectCate = value
            },

            changeDept(value,label,extra){

                this.form.setFieldsValue({
                    departmentCate: value
                })
                this.departmentCate = value
            }
            
        },
        created() {
            this.callGetTree()
            this.callGetDeptTree()
        }
    }
</script>
