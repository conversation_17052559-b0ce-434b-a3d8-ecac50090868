import { axios } from '@/utils/request'
import service from "@/utils/requestjira"

export const getReportTreeData = (parameter) => {
  return axios({
    url: '/report/tree_data',
    method: 'post',
    data: parameter
  })
}

export const getCatesTree = (params) => {
  return axios({
    url: '/report/cate_tree',
    method: 'get',
    params: params
  })
}

/* export const getReportProductData = (params) => {
  return axios({
    url: '/report/product_data',
    method: 'get',
    params: params
  })
} */

/* export const getReportDocsData = (params) => {
  return axios({
    url: '/report/docs_data',
    method: 'get',
    params: params
  })
} */


/* export const getReportMaterialsData = (params) => {
  return axios({
    url: '/report/material_data',
    method: 'get',
    params: params
  })
} */

/* export const getBomData = (params) => {
  return axios({
    url: '/report/bom_data',
    method: 'get',
    params: params
  })
} */

export const getJiraToken = (params) => {
  return service({
    url: '/plmjira/rest/sso/1.0/user/createToken',
    method: 'POST',
    data: params
  })
}

/* export const getProjectHead = (params) => {
  return axios({
    url: '/productjira/head',
    method: 'get',
    params: params
  })
} */

export const getProjects = (params) => {
  return axios({
    url: '/productjira/data',
    method: 'get',
    params: params
  })
}
/* export const getProjectDocs = (params) => {
  return axios({
    url: '/productjira/docs',
    method: 'get',
    params: params
  })
} */

export const getSuppliers = (params) => {
  return axios({
    url: '/productjira/supplier',
    method: 'get',
    params: params
  })
}

export const getProjectDetail = (params) => {
  return axios({
    url: '/productjira/detail',
    method: 'get',
    params: params
  })
}

export const getBom = (params) => {
  return axios({
    url: '/productjira/bom',
    method: 'get',
    params: params
  })
}

export const productMsgToUpdate = (params) => {
  return axios({
    url: '/product/msg/update',
    method: 'post',
    data: params
  })
}

export const productCreate = (params) => {
  return axios({
    url: '/product/jira/create',
    method: 'post',
    data: params
  })
}

export const getStageProblem=(params)=>{
  return axios({
    url: '/report/stage_trouble',
    method: 'get',
    params: params
  })
}

export const getStageRisk=(params)=>{
  return axios({
    url: '/report/stage_risk',
    method: 'get',
    params: params
  })
}

export const getStageReview=(params)=>{
  return axios({
    url: '/report/review',
    method: 'get',
    params: params
  })
}

/* export const getPassNumSixMonth=(params)=>{
  return axios({
    url: '/report/getPassNumSixMonth',
    method: 'get'
  })
} */

export const getAllProject=(params)=>{
  return axios({
    url: '/report/getAllProject',
    method: 'get'
  })
}

export function getStageRate (params) {
  return axios({
    url: '/report/stage_rate',
    method: 'get',
    params: params
  })
}

export function transiteStage (params) {
  return axios({
    url: '/stage/transite',
    method: 'get',
    params: params
  })
}

/* export function getSampleLists(params) {
  return axios({
    url: '/bomSample/list',
    method: 'get',
    params: params
  })
} */


export function stageSave (params) {
  return axios({
    url: '/stage/save',
    method: 'post',
    data: params
  })
}

export function stageRiskSave (params) {
  return axios({
    url: '/stage/risk_save',
    method: 'post',
    data: params
  })
}

export function stageDel (params) {
  return axios({
    url: '/stage/del',
    method: 'post',
    data: params
  })
}

/* export function sysBomSupplySave (params) {
  return axios({
    url: '/sysBomSupply/save',
    method: 'post',
    data: params
  })
} */
