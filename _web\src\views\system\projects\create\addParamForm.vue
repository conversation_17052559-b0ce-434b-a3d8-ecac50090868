<template>
	<div>
		<!-- <a-button :style="{position:'fixed',zIndex:99,right:'16px'}" type="primary" @click="add">
            保存
        </a-button> -->
		<a-spin :spinning="confirmLoading">
			<a-form size="small" :form="form" layout="horizontal">
				<div class="step_prev">
					<template v-for="(item, i) in formAttr[formIndex]">
						<div class="divider" :key="i">
							<span>{{ i == 0 ? "一" : i }}、{{ item.head }}</span>
						</div>
						<a-row :gutter="24" :key="i">
							<a-col v-for="(_item, _i) in item.attr" :key="_i" :span="_item.span">
								<a-form-item
									:label="_item.label"
									:labelCol="_item.span == 24 ? labelCol_JG : labelCol"
									:wrapperCol="_item.span == 24 ? wrapperCol_JG : wrapperCol"
								>
									<a-input
										:disabled="isNoEdit"
										v-if="_item.type == 'input'"
										v-decorator="[
											`${_item.name}`,
											{
												rules: [
													{
														required: true,
														message: `请输入${_item.label}!`
													}
												]
											}
										]"
									></a-input>
									<a-select
										:disabled="isNoEdit"
										v-if="_item.type == 'select'"
										v-decorator="[
											`${_item.name}`,
											{
												rules: [
													{
														required: true,
														message: `请选择${_item.label}!`
													}
												]
											}
										]"
										style="width: 100%"
										@change="handleChange"
									>
										<a-select-option v-for="($item, $i) in _item.options" :key="$i" :value="$item.val">
											{{ $item.label }}
										</a-select-option>
									</a-select>
								</a-form-item>
							</a-col>
						</a-row>
					</template>
					<!-- <a-row  :gutter="24" >
                        <a-col :span="24" :style="{textAlign:'right'}">
                            <a-button type="primary" @click="next">
                                下一步
                            </a-button>
                        </a-col>
                    </a-row> -->
				</div>
				<div class="step_next">
					<template v-for="(item, i) in craftForm">
						<div class="divider" :key="i">{{ item.head }}</div>
						<a-row :gutter="24" :key="i">
							<a-col v-for="(_item, _i) in item.attr" :key="_i" :span="_item.span">
								<a-form-item
									:labelCol="_item.span == 24 ? labelCol_JG : labelCol"
									:wrapperCol="_item.span == 24 ? wrapperCol_JG : wrapperCol"
									:label="_item.label"
								>
									<a-input
										:disabled="isNoEdit"
										v-if="_item.type == 'input'"
										v-decorator="[
											`${_item.name}`,
											{
												rules: [
													{
														required: true,
														message: `请输入${_item.label}!`
													}
												]
											}
										]"
									></a-input>
									<a-select
										:disabled="isNoEdit"
										v-if="_item.type == 'select'"
										v-decorator="[
											`${_item.name}`,
											{
												rules: [
													{
														required: true,
														message: `请选择${_item.label}!`
													}
												]
											}
										]"
										style="width: 100%"
									>
										<a-select-option v-for="($item, $i) in _item.options" :key="$i" :value="$item.val">
											{{ $item.label }}
										</a-select-option>
									</a-select>

									<a-upload
										:disabled="isNoEdit"
										v-if="_item.type == 'upload' && _item.name == 'pic'"
										:headers="headers"
										:action="postUrl"
										name="file"
										:file-list="picList"
										:default-file-list="picList"
										@change="handlePicChange"
									>
										<div v-if="picList.length < 6">
											<!-- <a-icon type="plus" />
                                            <div class="ant-upload-text">Upload</div> -->
											<a-button> <a-icon type="upload" /> Upload </a-button>
										</div>
									</a-upload>

									<a-upload
										:disabled="isNoEdit"
										v-if="_item.type == 'upload' && _item.name == 'structurePic'"
										:headers="headers"
										:action="postUrl"
										name="file"
										:file-list="structPicList"
										:default-file-list="structPicList"
										@change="handleStructPicChange"
									>
										<div v-if="structPicList.length < 6">
											<!-- <a-icon type="plus" />
                                            <div class="ant-upload-text">Upload</div> -->
											<a-button> <a-icon type="upload" /> Upload </a-button>
										</div>
									</a-upload>
								</a-form-item>
							</a-col>
						</a-row>
					</template>
					<!-- <a-row  :gutter="24" >
                        <a-col :span="24" :style="{textAlign:'right'}">
                            <a-button type="primary" :style="{marginRight:'20px'}" @click="prev">
                                上一步
                            </a-button>
                            <a-button type="primary" @click="add">
                                完成
                            </a-button>
                        </a-col>
                    </a-row> -->
				</div>
			</a-form>
		</a-spin>
		<a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel">
			<img alt="example" style="width: 100%" :src="previewImage" />
		</a-modal>
		<copyForm @copy="copyProductParam" ref="copyForm" />
	</div>
</template>

<script>
import Vue from "vue"
import { ACCESS_TOKEN } from "@/store/mutation-types"
import { getProductparam, saveProductparam } from "@/api/modular/system/productParamManage"
function getBase64(file) {
	return new Promise((resolve, reject) => {
		const reader = new FileReader()
		reader.readAsDataURL(file)
		reader.onload = () => resolve(reader.result)
		reader.onerror = error => reject(error)
	})
}
import copyForm from "./copyForm"
export default {
	props: {
		issueId: {
			type: Number,
			default: 0
		},
		projectdetail: {
			type: Object,
			default: {}
		},
		activeKey: {
			type: String,
			default: ""
		},
		// 是否可修改
		isNoEdit: {
			type: Boolean,
			default: true
		}
	},
	components: {
		copyForm
	},
	data() {
		return {
			confirmLoading: false,
			productparam: {},
			previewVisible: false,
			previewImage: "",
			postUrl: "/api/sysFileInfo/uploadfile",
			headers: {
				Authorization: "Bearer " + Vue.ls.get(ACCESS_TOKEN)
			},

			baseUrl: process.env.VUE_APP_API_BASE_URL + "/sysFileInfo/preview?id=",
			downUrl: process.env.VUE_APP_API_BASE_URL + "/sysFileInfo/download?id=",
			structPicList: [],
			picList: [],
			formIndex: 1,
			labelCol: {
				xs: { span: 24 },
				sm: { span: 8 }
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: { span: 12 }
			},
			labelCol_JG: {
				xs: { span: 24 },
				sm: { span: 4 }
			},
			wrapperCol_JG: {
				xs: { span: 24 },
				sm: { span: 12 }
			},
			formAttr: {
				1: [
					{
						head: "产品参数",
						attr: [
							{
								label: "产品类型",
								name: "productType",
								span: 12,
								type: "select",
								options: [
									{
										val: 1,
										label: "方形电池"
									},
									{
										val: 2,
										label: "圆柱电池"
									},
									{
										val: 3,
										label: "软包电池"
									}
								]
							}
						]
					},
					{
						head: "尺寸、重量参数填写",
						attr: [
							{
								label: "产品高度(mm不含极柱)",
								name: "nonePolarHeight",
								span: 12,
								type: "input"
							},
							{
								label: "产品高度(mm含极柱)",
								name: "polarHeight",
								span: 12,
								type: "input"
							},
							{
								label: "产品宽度(mm不含蓝膜)",
								name: "noneBlueFilmWidth",
								span: 12,
								type: "input"
							},
							{
								label: "产品宽度(mm含蓝膜)",
								name: "blueFilmWidth",
								span: 12,
								type: "input"
							},

							{
								label: "产品厚度(mm不含蓝膜)",
								name: "noneBlueFilmThickness",
								span: 12,
								type: "input"
							},
							{
								label: "产品厚度(mm含蓝膜)",
								name: "blueFilmThickness",
								span: 12,
								type: "input"
							},
							{
								label: "产品重量(g)",
								name: "weight",
								span: 12,
								type: "input"
							}
						]
					},
					{
						head: "电性能参数填写",
						attr: [
							{
								label: "额定容量(Ah)",
								name: "ah",
								span: 12,
								type: "input"
							},
							{
								label: "额定容量放电倍率(C)",
								name: "capacity",
								span: 12,
								type: "input"
							},
							{
								label: "标称电压(V)",
								name: "voltage",
								span: 12,
								type: "input"
							},
							{
								label: "能量密度(Wh/kg)",
								name: "density",
								span: 12,
								type: "input"
							},

							{
								label: "ACR(mΩ)≤",
								name: "acr",
								span: 12,
								type: "input"
							},
							{
								label: "DCR(mΩ)≤",
								name: "dcr",
								span: 12,
								type: "input"
							},
							{
								label: "循环描述",
								name: "loop",
								span: 24,
								type: "input"
							},
							{
								label: "性能描述",
								name: "performance",
								span: 24,
								type: "input"
							}
						]
					},
					{
						head: "测试参数",
						attr: [
							{
								label: "1C对应电流(A)",
								name: "rateCurrent",
								span: 12,
								type: "input"
							},
							{
								label: "电压范围",
								name: "voltageRange",
								span: 12,
								type: "input"
							}
						]
					}
				],

				2: [
					{
						head: "产品参数",
						attr: [
							{
								label: "产品类型",
								name: "productType",
								span: 12,
								type: "select",
								options: [
									{
										val: 1,
										label: "方形电池"
									},
									{
										val: 2,
										label: "圆柱电池"
									},
									{
										val: 3,
										label: "软包电池"
									}
								]
							}
						]
					},
					{
						head: "尺寸、重量参数填写",
						attr: [
							{
								label: "产品高度(mm不含极柱)",
								name: "nonePolarHeight",
								span: 12,
								type: "input"
							},
							{
								label: "产品高度(mm含极柱)",
								name: "polarHeight",
								span: 12,
								type: "input"
							},

							{
								label: "产品直径(mm)",
								name: "diameter",
								span: 12,
								type: "input"
							},
							{
								label: "产品重量(g)",
								name: "weight",
								span: 12,
								type: "input"
							}
						]
					},
					{
						head: "电性能参数填写",
						attr: [
							{
								label: "额定容量(Ah)",
								name: "ah",
								span: 12,
								type: "input"
							},
							{
								label: "额定容量放电倍率(C)",
								name: "capacity",
								span: 12,
								type: "input"
							},
							{
								label: "标称电压(V)",
								name: "voltage",
								span: 12,
								type: "input"
							},
							{
								label: "能量密度(Wh/kg)",
								name: "density",
								span: 12,
								type: "input"
							},

							{
								label: "ACR(mΩ)≤",
								name: "acr",
								span: 12,
								type: "input"
							},
							{
								label: "DCR(mΩ)≤",
								name: "dcr",
								span: 12,
								type: "input"
							},
							{
								label: "循环描述",
								name: "loop",
								span: 24,
								type: "input"
							},
							{
								label: "性能描述",
								name: "performance",
								span: 24,
								type: "input"
							}
						]
					},
					{
						head: "测试参数",
						attr: [
							{
								label: "1C对应电流(A)",
								name: "rateCurrent",
								span: 12,
								type: "input"
							},
							{
								label: "电压范围",
								name: "voltageRange",
								span: 12,
								type: "input"
							}
						]
					}
				],

				3: [
					{
						head: "产品参数",
						attr: [
							{
								label: "产品类型",
								name: "productType",
								span: 12,
								type: "select",
								options: [
									{
										val: 1,
										label: "方形电池"
									},
									{
										val: 2,
										label: "圆柱电池"
									},
									{
										val: 3,
										label: "软包电池"
									}
								]
							}
						]
					},
					{
						head: "尺寸、重量参数填写",
						attr: [
							{
								label: "产品长度(mm不含极柱)",
								name: "nonePolarHeight",
								span: 12,
								type: "input"
							},
							{
								label: "产品长度(mm含极柱)",
								name: "polarHeight",
								span: 12,
								type: "input"
							},
							{
								label: "产品宽度(mm不含蓝膜)",
								name: "noneBlueFilmWidth",
								span: 12,
								type: "input"
							},
							{
								label: "产品宽度(mm含蓝膜)",
								name: "blueFilmWidth",
								span: 12,
								type: "input"
							},

							{
								label: "产品厚度(mm不含蓝膜)",
								name: "noneBlueFilmThickness",
								span: 12,
								type: "input"
							},
							{
								label: "产品厚度(mm含蓝膜)",
								name: "blueFilmThickness",
								span: 12,
								type: "input"
							},
							{
								label: "产品重量(g)",
								name: "weight",
								span: 12,
								type: "input"
							}
						]
					},
					{
						head: "电性能参数填写",
						attr: [
							{
								label: "额定容量(Ah)",
								name: "ah",
								span: 12,
								type: "input"
							},
							{
								label: "额定容量放电倍率(C)",
								name: "capacity",
								span: 12,
								type: "input"
							},
							{
								label: "标称电压(V)",
								name: "voltage",
								span: 12,
								type: "input"
							},
							{
								label: "能量密度(Wh/kg)",
								name: "density",
								span: 12,
								type: "input"
							},

							{
								label: "ACR(mΩ)≤",
								name: "acr",
								span: 12,
								type: "input"
							},
							{
								label: "DCR(mΩ)≤",
								name: "dcr",
								span: 12,
								type: "input"
							},
							{
								label: "循环描述",
								name: "loop",
								span: 24,
								type: "input"
							},
							{
								label: "性能描述",
								name: "performance",
								span: 24,
								type: "input"
							}
						]
					},
					{
						head: "测试参数",
						attr: [
							{
								label: "1C对应电流(A)",
								name: "rateCurrent",
								span: 12,
								type: "input"
							},
							{
								label: "电压范围",
								name: "voltageRange",
								span: 12,
								type: "input"
							}
						]
					}
				]
			},
			craftForm: [
				{
					head: "二、产品工艺信息填写",
					attr: [
						{
							label: "卷芯工艺",
							name: "rollCore",
							span: 12,
							type: "input",
							type: "select",
							options: [
								{
									val: 1,
									label: "卷绕"
								},
								{
									val: 2,
									label: "叠片"
								}
							]
						},
						{
							label: "卷芯出极耳方式",
							name: "earbud",
							span: 12,
							type: "input"
						},
						{
							label: "电芯端子方式",
							name: "terminal",
							span: 12,
							type: "input"
						},
						{
							label: "裸电芯数量",
							name: "bareWireCount",
							span: 12,
							type: "input"
						},

						{
							label: "内部结构类型",
							name: "internalStructure",
							span: 12,
							type: "input"
						},
						{
							label: "内部结构图",
							name: "structurePic",
							span: 24,
							type: "upload"
						},
						{
							label: "产品图片",
							name: "pic",
							span: 24,
							type: "upload"
						}
					]
				}
			],
			//step:0,
			form: this.$form.createForm(this)
		}
	},
	methods: {
		handleCancel() {
			this.previewVisible = false
		},
		async handlePreview(file) {
			if (!file.url && !file.preview) {
				file.preview = await getBase64(file.originFileObj)
			}
			this.previewImage = file.url || file.preview
			this.previewVisible = true
		},
		handleStructPicChange(info) {
			let fileList = [...info.fileList]
			let suffx = ["jpg", "jpeg", "png", "gif", "bmp", "svg", "pdf"]
			fileList = fileList.map(file => {
				if (file.response) {
					if (file.response.success) {
						file.url =
							suffx.indexOf(file.response.data.fileSuffix) > -1
								? this.baseUrl + file.response.data.id + "#navpanes=0"
								: this.downUrl + file.response.data.id
						file.uid = file.response.data.id
						delete file.response
						delete file.percent
						delete file.lastModified
						delete file.lastModifiedDate
						delete file.originFileObj
						delete file.size
						delete file.thumbUrl
						delete file.type
						delete file.xhr
					}
				}
				return file
			})
			this.structPicList = fileList
			if (info.file.status !== "uploading") {
			}
			if (info.file.status === "done") {
				console.log(info.file.response)
				let res = info.file.response
				if (res.success) {
					/* this.structPicList.push({
                        status: 'done',
                        uid: res.data.id,
                        name: res.data.fileOriginName,
                        url: this.baseUrl+res.data.id
                    }) */
				} else {
					_this.$message.error(res.message)
				}
			}

			if (info.file.status === "error") {
				_this.$message.error(`${info.file.name}上传失败`)
			}
		},
		handlePicChange(info) {
			let fileList = [...info.fileList]
			//.jpg、.jpeg、.png、.gif、.bmp、.svg
			let suffx = ["jpg", "jpeg", "png", "gif", "bmp", "svg", "pdf"]
			fileList = fileList.map(file => {
				if (file.response) {
					if (file.response.success) {
						file.url =
							suffx.indexOf(file.response.data.fileSuffix) > -1
								? this.baseUrl + file.response.data.id + "#navpanes=0"
								: this.downUrl + file.response.data.id
						file.uid = file.response.data.id
						delete file.response
						delete file.percent
						delete file.lastModified
						delete file.lastModifiedDate
						delete file.originFileObj
						delete file.size
						delete file.thumbUrl
						delete file.type
						delete file.xhr
					}
				}
				return file
			})
			this.picList = fileList
			if (info.file.status !== "uploading") {
			}
			if (info.file.status === "done") {
				console.log(info.file.response)
				let res = info.file.response
				if (res.success) {
					/* this.picList.push({
                        status: 'done',
                        uid: res.data.id,
                        name: res.data.fileOriginName,
                        url: this.baseUrl+res.data.id
                    }) */
				} else {
					_this.$message.error(res.message)
				}
			}
			if (info.file.status === "error") {
				_this.$message.error(`${info.file.name}上传失败`)
			}
		},
		getByClass(parent, cls) {
			if (parent.getElementsByClassName) {
				return Array.from(parent.getElementsByClassName(cls))
			} else {
				var res = []
				var reg = new RegExp(" " + cls + " ", "i")
				var ele = parent.getElementsByTagName("*")
				for (var i = 0; i < ele.length; i++) {
					if (reg.test(" " + ele[i].className + " ")) {
						res.push(ele[i])
					}
				}
				return res
			}
		},
		initSm4Width() {
			setTimeout(() => {
				this.$nextTick(() => {
					let _item = this.getByClass(document, "ant-col-sm-8")[0]
					let items = this.getByClass(document, "ant-col-sm-4")
					for (let e of items) {
						e.style.width = _item.offsetWidth + "px"
					}
				})
			}, 500)
		},
		handleChange(val) {
			this.formIndex = val
		},
		/* next(){
            let formAtrr = this.formAttr[this.formIndex]
            let fields = []
            formAtrr.forEach(item => {
                item.attr.forEach(_item => {
                    fields.push(_item.name)
                });
            });
            let isError = false
            const { form: { validateFields } } = this
            validateFields(fields, (valid, values) => {
                if (valid) {
                    isError = true
                }
            })
            if (!isError) {
                this.step = 1
            }
        },
        prev(){
            this.step = 0
        }, */
		add() {
			/* let formAtrr = this.craftForm
            let fields = []
            formAtrr.forEach(item => {
                item.attr.forEach(_item => {
                    fields.push(_item.name)
                });
            }); */
			const {
				form: { validateFields }
			} = this
			let that = this

			/* let isError = false
            validateFields(fields, (valid, values) => {
                if (valid) {
                    isError = true
                }
            })

            if (!isError) {
                validateFields((errors, values) => {
                    console.log(values)
                })
            } */

			validateFields((err, values) => {
				if (!err) {
					let _structPic = that.structPicList
					for (let item of _structPic) {
						delete item.thumbUrl
					}
					let _pic = that.picList
					for (let item of _pic) {
						delete item.thumbUrl
					}
					that.confirmLoading = true
					values.structurePic = _structPic
					values.pic = _pic
					that.callSaveProductparam(values)
				}
			})
		},
		callProductparam() {
			let that = this
			getProductparam({
				issueId: that.issueId,
				stage: that.activeKey
			})
				.then(res => {
					if (res.success) {
						that.productparam = res.data
						if (res.data.params) {
							let _param = JSON.parse(res.data.params)
							that.formIndex = parseInt(_param.productType)

							that.$nextTick(() => {
								that.structPicList = _param.structurePic
								that.picList = _param.pic
							})

							setTimeout(() => {
								that.form.setFieldsValue(_param)
							}, 200)
						}
					} else {
						this.$message.error(res.message, 1)
					}
				})
				.catch(err => {
					this.$message.error("错误提示：" + err.message, 1)
				})
		},
		callSaveProductparam($params) {
			let that = this
			that.productparam.params = JSON.stringify($params)
			saveProductparam(that.productparam)
				.then(res => {
					if (res.success) {
						that.$message.success("保存成功")
					} else {
						that.$message.error("保存失败：" + res.message)
					}
					that.confirmLoading = false
				})
				.finally(res => {
					that.confirmLoading = false
				})
		},
		copyProductParam($params) {
			let that = this
			that.productparam.params = $params
			saveProductparam(that.productparam)
				.then(res => {
					if (res.success) {
						that.$message.success("复制成功")
						let _param = JSON.parse($params)
						setTimeout(() => {
							that.$nextTick(() => {
								that.structPicList = _param.structurePic
								that.picList = _param.pic
							})
							that.form.setFieldsValue(_param)
						}, 200)
					} else {
						that.$message.error("复制失败：" + res.message)
					}
					that.confirmLoading = false
				})
				.finally(res => {
					that.confirmLoading = false
				})
		},
		showCopy() {
			this.$refs.copyForm.view(this.productparam)
		}
	},
	mounted() {
		this.initSm4Width()
		window.addEventListener("resize", this.initSm4Width)
		this.callProductparam()
	},
	beforeDestroy() {
		window.removeEventListener("resize", this.initSm4Width)
	}
}
</script>

<style lang="less" scoped="">
/deep/.ant-row {
	margin: auto 35px 4px !important;
}
.divider {
	background: #f2f2f2;
	padding: 4px 12px;
	font-size: 14px;
	margin: 4px 0;
}
.divider:first-child,
.divider:nth-child(4) {
	font-size: 15px;
	/* margin-top: 20px; */
}
.ant-upload-select-picture-card i {
	font-size: 32px;
	color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
	margin-top: 8px;
	color: #666;
}
/deep/.ant-form label {
	font-size: 12px;
}
</style>
