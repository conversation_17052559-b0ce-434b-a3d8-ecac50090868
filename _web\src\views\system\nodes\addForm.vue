<template>
  <a-modal
    title="新增分类"
    :width="500"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="类名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入类名" v-decorator="['name', {rules: [{required: true, message: '请输入类名！'}]}]" />
        </a-form-item>

        <a-form-item
          label="类名id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入类名id" v-decorator="['id', {rules: [{required: true, message: '请输入类名id！'}]}]" />
        </a-form-item>

      </a-form>

    </a-spin>
  </a-modal>
</template>

<script>
import { sysNodeAdd } from '@/api/modular/system/nodeManage'
export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 18 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods:{
        add (record) {
            this.visible = true
        },
        handleCancel () {
            this.form.resetFields()
            this.visible = false
        },
        handleSubmit () {
            const { form: { validateFields } } = this
            this.confirmLoading = true
            validateFields((errors, values) => {
            if (!errors) {
                sysNodeAdd(values).then((res) => {
                if (res.success) {
                    this.$message.success('新增成功')
                    this.visible = false
                    this.confirmLoading = false
                    this.$emit('ok', values)
                    this.form.resetFields()
                } else {
                    this.$message.error('新增失败：' + res.message)
                }
                }).finally((res) => {
                this.confirmLoading = false
                })
            } else {
                this.confirmLoading = false
            }
            })
        },
    }
}
</script>

<style>

</style>