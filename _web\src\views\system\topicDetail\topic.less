/deep/.ant-table-thead > tr > th,
/deep/ .ant-table-tbody > tr > td{
    padding: 2.5px;
}
.topic_width{
    width: 96%;;margin-left: 2%;margin-right: 2%;
}
.topic_wid{
    width: 98%;;margin-left: 1%;margin-right: 1%;
}
.text_center{
    text-align: center;
}
/deep/.ant-col{
    padding: 4px !important;
}
.table-page-search-wrapper .table-page-search-submitButtons{
    margin: 0;
    margin-top: 8px;
}
/deep/.table-page-search-wrapper .ant-form-inline .ant-form-item{
    margin-bottom: 0;
}
/deep/.ant-card-body{
    padding: 0 24px;
}
/deep/.ant-table-footer{
    padding: 0 16px;
}
/deep/.ant-table-header table{
    border-bottom: 1px solid #e9e9e9;
}
/deep/.ant-table-pagination.ant-pagination{
    margin: 2px 0;
}
/deep/.ant-pagination-prev, 
/deep/.ant-pagination-next, 
/deep/.ant-pagination-jump-prev, 
/deep/.ant-pagination-jump-next{
    height: 20px;
    line-height: 20px;
}
/deep/.ant-pagination-item{
    height: 20px;
    line-height: 20px;
}
/deep/.ant-pagination-options-quick-jumper,
/deep/.ant-pagination-options-quick-jumper input{
    height: 20px;
    line-height: 20px;
}
/deep/.vue-treeselect__multi-value-item{
    background: transparent;
    font-size: 13px;
    vertical-align: initial;
}
/deep/.vue-treeselect{
    /* display: inline-block; */
    min-width: 80%;
    max-width: 90%;
    margin-top: 4px;
}
/deep/.vue-treeselect__control{
    display: flex;
    align-items: center;
    justify-content: center;
    height: 24px;
    overflow: hidden;
    border-radius: initial;
}
/deep/.vue-treeselect__control *{
    padding: 0 !important;
    margin: 0 !important;
    line-height:initial !important;
    white-space: nowrap;
}
/deep/.vue-treeselect__value-remove{
    color: #e9e9e9;
}
/deep/.vue-treeselect__multi-value-item{
    color: #b2b0b0;
}
/deep/.ant-pagination-options-size-changer.ant-select{
    display: none;
}
/deep/.tdcls{
    border-left: 1px solid #e7eef1;
  }

  /deep/.ant-table-thead > tr > th{
    background: #fafafa;
    font-size: 14px;
  }

  .head2{
    height: 30px;
    background: rgb(92 129 203);
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    text-align: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    color: #fff;
    font-size: 18px;
  }

  .breadcrumb{
    padding: 5px 0;
    }.ant-breadcrumb a{
    color:#5d90fa !important;
    }.ant-breadcrumb{
    font-size: 12px !important;
    }

    /deep/.ant-table-pagination.ant-pagination{
        float: none;
        text-align: center;
    }