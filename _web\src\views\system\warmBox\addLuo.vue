<template>
  <a-modal :title="'温箱型号:'+record.model+ '  温箱编号：'+ record.storeyCode+ '  新增摞'" width="30%" :visible="visible" :confirmLoading="confirmLoading"
           @ok="handleSubmit"
           @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">

        <a-form-item label="摞数" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input-number v-decorator="['luoNum']" :min="parseInt(1)"/>
        </a-form-item>
        <a-form-item label="每摞木质支架个数" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input-number v-decorator="['mjNum']" :min="parseInt(1)"/>
        </a-form-item>
        <a-form-item label="每摞吸塑盒个数" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input-number v-decorator="['xshNum']" :min="parseInt(1)"/>
        </a-form-item>
        <a-form-item label="每摞托盘数" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input-number v-decorator="['tpNum']" :min="parseInt(1)"/>
        </a-form-item>
        <a-form-item label="每摞方形/软包数" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input-number v-decorator="['fxRbNum']" :min="parseInt(1)"/>
        </a-form-item>

      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import {
    testWarmBoxLuoAdd
  } from '@/api/modular/system/warmBoxManager'

  export default {

    data() {
      return {
        record:{},
        labelCol: {
          xs: {
            span: 12
          },
          sm: {
            span: 12
          }
        },
        wrapperCol: {
          xs: {
            span: 12
          },
          sm: {
            span: 12
          }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this),

      }
    },

    methods: {
      add(record) {
        this.record = record
        this.visible = true
      },

      handleSubmit() {
        const {
          form: {
            validateFields
          }
        } = this

        this.confirmLoading = true
        validateFields((errors, values) => {

          if(values.luoNum == null || (values.mjNum == null  && values.xshNum == null && values.tpNum == null && values.fxRbNum == null)){
            this.$message.warn('请填写完整')
            this.confirmLoading = false
            return
          }

          if (!errors) {
            values.storeyId = this.record.id
            testWarmBoxLuoAdd(values).then((res) => {
              this.confirmLoading = false
              if (res.success) {
                this.$message.success('新增成功')
                this.handleCancel()
                this.$emit('ok', values)
              } else {
                this.$message.error('新增失败：' + res.message)
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel() {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>


