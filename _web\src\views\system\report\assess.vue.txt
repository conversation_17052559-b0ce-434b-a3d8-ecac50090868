<template>
  <div>
    <a-breadcrumb class="breadcrumb" separator=">" :style="`width:${_elWidth}px;margin:0 auto;`">
      <a-breadcrumb-item><a @click="gotoIndex(-3)">信息对齐表</a></a-breadcrumb-item>
      <a-breadcrumb-item><a @click="gotoIndex(-2)">产品开发进展</a></a-breadcrumb-item>
      <a-breadcrumb-item><a @click="gotoIndex(-1)">质量指标</a></a-breadcrumb-item>
      <a-breadcrumb-item>计划评审达成率</a-breadcrumb-item>
    </a-breadcrumb>
    <vxe-table :height="windowHeight" :loading="loading" show-footer border align="center" :data="list" :footer-span-method="footerColspanMethod" :footer-method="footerMethod" :span-method="mergeRowMethod">
    <vxe-column width="90" field="productStage" title="产品阶段">
      <template #default="{ row }">
        <span>{{ 'product_stage_status' | dictType(row.productStage) }}</span>
      </template>
    </vxe-column>
    <vxe-column width="90" field="productPlannedDate" title="计划评审日期"></vxe-column>
    <vxe-column width="90" field="productActualDate" title="实际评审日期">
    </vxe-column>
    <vxe-column width="180" field="reviewNum" title="评审次数">
    </vxe-column>
    <vxe-column field="reviewResult" width="180" title="评审结论">
      <template #default="{ row }">
        <span v-if="row.reviewResult == 1">通过</span>
        <span v-else-if="row.reviewResult == 2">不通过</span>
      </template>
    </vxe-column>
    <vxe-column field="reviewOpinion" width="180" title="评审意见">
    </vxe-column>
    <vxe-column field="delayDay" title="延期天数">
    </vxe-column>
    <vxe-column field="delayReason" width="180" title="延期原因">
    </vxe-column>
    <vxe-column width="150" field="reviewAchievementRate" title="计划评审达成率"></vxe-column>
  </vxe-table>
  </div>
  
</template>

<script>
  import {
    clamp
  } from '@/components'
  import {getStageReview} from "@/api/modular/system/report"
  export default {
    components: {
      clamp
    },
    data() {
      return {
        windowHeight: document.documentElement.clientHeight - 35,
        list: [],
        loading: false,
      }
    },
    methods: {
      gotoIndex(index){
      this.$router.go(index)
    },
      callStageReview() {
        this.loading = true
        getStageReview({
            stage: this.$route.query.stage,
            issueId: this.$route.query.issueId
          })
          .then((res) => {
            if (res.result) {
              this.list = res.data
            } else {
              this.$message.error(res.message, 1);
            }
            this.loading = false
          })
          .catch((err) => {
            this.loading = false
            this.$message.error('错误提示：' + err.message, 1)
          });
      },
    },
    created() {
      this.callStageReview()
    }
  }
</script>

<style lang="less">
  @import './vetable.less';
  .breadcrumb{
  padding: 5px 0;
  padding-left: 13px;
}
.ant-breadcrumb a{
  color:#5d90fa !important;
}
.ant-breadcrumb{
  font-size: 12px !important;
}
</style>