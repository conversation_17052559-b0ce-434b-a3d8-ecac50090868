<template>
    <a-modal title="填写版本(大写字母)" :width="400" :visible="visible" :confirmLoading="confirmLoading" @ok="handleSubmit" @cancel="handleCancel">
        <a-spin :spinning="confirmLoading">
            <a-form :form="form">
                <a-form-item>
                    <a-input v-decorator="['bomVersion', {rules: [{required: true, pattern: /^[A-Z]+$/, message: '请输入大写字母！'}]}]" />
                </a-form-item>
                <a-form-item>
                    <a-upload :file-list="fileList" :headers="headers" :action="postUrl" :multiple="false" name="file" @change="handleChange">
                        <a-button> <a-icon type="upload" /> 上传BOM文件 </a-button>
                    </a-upload>
                </a-form-item>
            </a-form>
        </a-spin>
    </a-modal>
</template>

<script>
    import Vue from 'vue'
    import {
        ACCESS_TOKEN
    } from '@/store/mutation-types'
    export default {
        data() {
            return {
                postUrl: '/api/sysFileInfo/uploadfile',
                headers: {
                    Authorization: 'Bearer ' + Vue.ls.get(ACCESS_TOKEN),
                },
                record :null,
                fileList: [],
                visible: false,
                confirmLoading: false,
                form: this.$form.createForm(this)
            }
        },
        methods: {
            handleChange(info) {
                let fileList = [...info.fileList];
                fileList = fileList.slice(-1);
                this.fileList = fileList;
                if (info.file.status !== 'uploading') {
                    console.log(info.file, info.fileList);
                }
                if (info.file.status === 'done') {
                    let res = info.file.response
                    if (res.success) {
                        this.$message.success(`${info.file.name} 文件上传成功`)
                        this.record = { ...this.record,
                            fileId: res.data.id
                        }
                    } else {
                        this.$message.error(res.message)
                    }
                } else if (info.file.status === 'error') {
                    this.$message.error(`${info.file.name} 文件上传失败`);
                }
            },
            handleSubmit() {
                const { form: { validateFields } } = this
                this.confirmLoading = true
                validateFields((errors, values) => {
                     if (!errors) {
                        let $params = {...this.record,...values}
                        $params.jiraIdStatus = 1
                        this.$emit('callBomUpgrade', $params)
                        this.handleCancel()
                     }else{
                        this.confirmLoading = false
                     }
                })
            },
            handleCancel() {
                this.visible = false
                this.confirmLoading = false
                this.record = null
                this.fileList = []
                this.form.resetFields()
            },
            edit(record) {
                this.record = record
                this.visible = true
                this.fileList = []
            }
        }
    }
</script>

<style>

</style>