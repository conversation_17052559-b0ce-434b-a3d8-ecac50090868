import axios from 'axios';
import Vue from "vue";
import {ACCESS_TOKEN} from "@/store/mutation-types";

// 创建axios实例
const request = axios.create({
  // baseURL: 'http://localhost:81',  // 后端API基础URL
  timeout: 1000000,  // 请求超时时间
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
request.interceptors.request.use(
  config => {
    const token = Vue.ls.get(ACCESS_TOKEN)
    config.headers['Authorization'] = 'Bearer ' + token
    // 在发送请求之前做些什么
    return config;
  },
  error => {
    // 对请求错误做些什么
    console.error('请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  response => {
    // 对响应数据做点什么
    return response;
  },
  error => {
    // 对响应错误做点什么
    console.error('响应错误:', error);
    // 添加更详细的错误信息
    if (error.response) {
      // 服务器响应了状态码
      console.error('错误状态码:', error.response.status);
      console.error('错误响应数据:', error.response.data);
      
      // 对422状态码进行特殊处理
      if (error.response.status === 422) {
        console.error('请求数据验证失败 (422)，详细信息:');
        if (error.response.data.detail) {
          console.error(JSON.stringify(error.response.data.detail, null, 2));
          error.message = `数据验证失败: ${JSON.stringify(error.response.data.detail)}`;
        }
      }
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      console.error('请求已发出但没有响应:', error.request);
    } else {
      // 设置请求时发生了错误
      console.error('请求配置错误:', error.message);
    }
    return Promise.reject(error);
  }
);

export default request; 