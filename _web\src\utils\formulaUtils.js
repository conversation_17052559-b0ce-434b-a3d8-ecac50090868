/**
 * 公式工具函数
 * 提供公式处理相关的通用功能
 */

/**
 * 解析公式参数
 * @param {Array} params - 参数数组
 * @returns {Object} 分类后的参数对象，包含系数和变量
 */
export function parseFormulaParams(params) {
  if (!params || !Array.isArray(params)) {
    return { coefficients: [], variables: [] };
  }

  return {
    coefficients: params.filter(param => param.type === 'coefficient'),
    variables: params.filter(param => param.type === 'variable')
  };
}

/**
 * 格式化系数值
 * @param {Number|String} value - 系数值
 * @param {Number} precision - 小数位数（可选，不传则不限制精度）
 * @returns {String} 格式化后的系数值
 */
export function formatCoefficientValue(value, precision = null) {
  if (value === null || value === undefined) return '-';

  // 对于数字，根据是否指定精度来处理
  if (typeof value === 'number') {
    // 如果指定了精度，则按精度格式化
    if (precision !== null && precision !== undefined) {
      // 如果是整数或者接近整数的值
      if (Number.isInteger(value) || Math.abs(value - Math.round(value)) < 0.0001) {
        return Math.round(value).toString();
      }
      // 否则保留指定小数位
      return value.toFixed(precision);
    } else {
      // 如果没有指定精度，确保不使用科学计数法显示
      return value.toFixed(20).replace(/\.?0+$/, '');
    }
  }

  // 对于字符串类型，检查是否为科学计数法格式
  if (typeof value === 'string') {
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      if (precision !== null && precision !== undefined) {
        return numValue.toFixed(precision);
      } else {
        return numValue.toFixed(20).replace(/\.?0+$/, '');
      }
    }
  }

  // 其他类型直接返回字符串表示
  return String(value);
}

/**
 * 从公式中提取主公式
 * @param {String|Object} latex - LaTeX公式字符串或对象
 * @returns {String} 主公式
 */
export function getMainFormula(latex) {
  if (!latex) return '';

  try {
    // 尝试解析JSON格式
    const parsed = typeof latex === 'string' ? JSON.parse(latex) : latex;
    return parsed.main_formula || latex;
  } catch (e) {
    // 如果不是JSON格式，直接返回原字符串
    return latex;
  }
}

/**
 * 从公式中提取子公式
 * @param {String|Object} latex - LaTeX公式字符串或对象
 * @returns {Array} 子公式数组
 */
export function getSubFormulas(latex) {
  if (!latex) return [];

  try {
    // 尝试解析JSON格式
    const parsed = typeof latex === 'string' ? JSON.parse(latex) : latex;
    return parsed.sub_formulas || [];
  } catch (e) {
    // 如果不是JSON格式，返回空数组
    return [];
  }
}

/**
 * 将表单数据转换为后端需要的参数列表格式
 * @param {Object} formData - 表单数据
 * @param {Array} originalParams - 原始参数列表，用于保留类型信息
 * @returns {Array} - 后端所需的参数列表
 */
export function formDataToParams(formData, originalParams) {
  if (!formData || !originalParams) return [];

  return originalParams.map(param => {
    const newParam = { ...param };
    if (param.name in formData) {
      newParam.value = formData[param.name];
    }
    return newParam;
  });
}

/**
 * 将后端返回的参数列表转换为表单可用的格式
 * @param {Array} params - 后端返回的参数列表
 * @returns {Object} - 表单可用的参数对象
 */
export function paramsToFormData(params) {
  const result = {};
  if (!params || !Array.isArray(params)) return result;

  params.forEach(param => {
    if (param.type === 'coefficient') {
      // 对于系数，直接存储值
      result[param.name] = param.value || 0;
    } else if (param.type === 'variable') {
      // 对于变量，存储范围
      result[param.name] = param.value || { min: 0, max: 1, default: 0.5 };
    }
  });

  return result;
}

/**
 * 查找系数所属的组
 * @param {String} name - 系数名称
 * @returns {String|null} 组标识符，如果没有找到则返回null
 */
export function findGroupLetterForCoefficient(name) {
  // 假设系数名称格式为 "A_1", "B_2" 等，其中字母表示组
  const match = name.match(/^([A-Za-z])_\d+$/);
  return match ? match[1] : null;
}
