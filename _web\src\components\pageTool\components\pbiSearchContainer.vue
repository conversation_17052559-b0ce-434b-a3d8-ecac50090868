<template>
  <div class="search-container">
    <a-row>
      <slot></slot>
    </a-row>
  </div>
</template>
<script>
   /*
    template
      <pbiSearchContainer>
            <pbiSearchItem label="立项日期">
                <a-range-picker :placeholder="['立项开始日期', '立项结束日期']" size="small" style="width: 100%;"
                    @change="dateChange" />
            </pbiSearchItem>
      </pbiSearchContainer>
    script
      import pbiSearchContainer from '@/components/pageTool/components/pbiSearchContainer.vue'
      import pbiSearchItem from '@/components/pageTool/components/pbiSearchItem.vue'
      components : {
        pbiSearchContainer,
        pbiSearchItem
      }
    style
      @import '/src/components/pageTool/style/pbiSearchItem.less';
      
  */
</script>
<style lang="less" scoped>
  .search-container {
    padding: 8px 0 0;
    font-size: 12px;
    color: #333;
  }
</style>