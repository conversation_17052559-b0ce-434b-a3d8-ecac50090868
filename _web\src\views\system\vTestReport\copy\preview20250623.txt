<template>
  <div ref="wrapper" class="wrapper">
    <div class="flex-sb-center-row">
      <div class="head_title">{{ record.type + ": " + record.reportName }}</div>
      <span id="revealText14" class="reveal-text-opacity" style="font-size: 14px;"></span> 
      <span id="revealText15" class="reveal-text-opacity" style="font-size: 15px;"></span> 
    </div>
    <span id="revealText14" class="reveal-text-opacity" style="font-size: 14px;"></span> 
    <span id="revealText15" class="reveal-text-opacity" style="font-size: 15px;"></span> 
    <div class="all-wrapper">
      <div class="left-content block">
        <pageComponent editObj="dcr"  @down="handleNormalDown('dcr')" @edit="handleEditEcharts('dcr')"></pageComponent>
        <div id="dcr" ref="dcr" class="mt3" style="width: 598px;height: 417px;border: 0.5px solid #ccc;"></div>

        <pageComponent class="mt10" editObj="growth"  @down="handleNormalDown('growth')" @edit="handleEditEcharts('growth')"></pageComponent>
        <div id="growth" ref="growth" class="mt3" style="width: 598px;height: 417px;border: 0.5px solid #ccc;">
        </div>
      </div>
      <div class="right-content">
        <div class="block" id="export">
          <div class="flex-column">
            <div>
              <div class="flex-center">
                <div @dblclick="() => (update = true)">
                  <svg t="1704333150396" class="icon" viewBox="0 0 1024 1024" version="1.1"
                    xmlns="http://www.w3.org/2000/svg" p-id="7872" width="20" height="20">
                    <path
                      d="M936.96 0v844.8h-665.6V0h665.6zM327.68 783.36h547.84V665.6H476.16v-61.44h399.36V476.16H476.16V409.6h399.36V271.36h-225.28V199.68h225.28V71.68H327.68v711.68z"
                      fill="#606266" p-id="7873"></path>
                    <path d="M143.36 250.88v711.68h578.56V1024H87.04V250.88z" fill="#606266" p-id="7874"></path>
                  </svg>
                </div>

                <strong class="ml10">原始数据</strong>
              </div>

              <div style="float: right;margin-top: -25px">
                <a-button type="primary" @click="cancel" v-if="update" style="margin-right: 20px">取消修改</a-button>
                <a-popconfirm placement="topRight" ok-text="确认" cancel-text="取消" @confirm="updateData" v-if="update">
                  <template slot="title">
                    <p>确认提交更改吗</p>
                  </template>
                  <a-button type="primary">提交数据</a-button>
                </a-popconfirm>
              </div>
            </div>

            <div class="mt10">
              <a-table :columns="originColumns" bordered :data-source="originData" :pagination="false">
              </a-table>
            </div>

            <div class="flex-center mt10">
              <svg t="1704333150396" class="icon" viewBox="0 0 1024 1024" version="1.1"
                xmlns="http://www.w3.org/2000/svg" p-id="7872" width="20" height="20">
                <path
                  d="M936.96 0v844.8h-665.6V0h665.6zM327.68 783.36h547.84V665.6H476.16v-61.44h399.36V476.16H476.16V409.6h399.36V271.36h-225.28V199.68h225.28V71.68H327.68v711.68z"
                  fill="#606266" p-id="7873"></path>
                <path d="M143.36 250.88v711.68h578.56V1024H87.04V250.88z" fill="#606266" p-id="7874">
                </path>
              </svg>
              <strong class="ml10">DCR</strong>
            </div>

            <div class="mt10">
              <a-table :columns="dcrColumns" bordered :data-source="dcrData" :pagination="false">
              </a-table>
            </div>
            <div class="flex-center mt10">
              <svg t="1704333150396" class="icon" viewBox="0 0 1024 1024" version="1.1"
                xmlns="http://www.w3.org/2000/svg" p-id="7872" width="20" height="20">
                <path
                  d="M936.96 0v844.8h-665.6V0h665.6zM327.68 783.36h547.84V665.6H476.16v-61.44h399.36V476.16H476.16V409.6h399.36V271.36h-225.28V199.68h225.28V71.68H327.68v711.68z"
                  fill="#606266" p-id="7873"></path>
                <path d="M143.36 250.88v711.68h578.56V1024H87.04V250.88z" fill="#606266" p-id="7874">
                </path>
              </svg>
              <strong class="ml10">DCR增长率</strong>
            </div>
            <div class="mt10">
              <a-table :columns="dcrGrowthColumns" bordered :data-source="dcrData" :pagination="false">
              </a-table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="drawerVisible">
      <PreviewDrawer :isDuplicateData="true"
        :legendOptions=" editObj === 'dcr' ? dcrOriginalLegend : growthOriginalLegend "
        :data="editObj === 'dcr' ? dcrOriginalSeries : growthOriginalSeries"
        :original="editObj === 'dcr' ? dcrResetOriginal : growthResetOriginal"
        :editData="editObj === 'dcr' ? dcrEditData : growthEditData"
        :checkObj="editObj === 'dcr' ? dcrCheckObj : growthCheckObj" @submit="handleDrawerSubmit"
        @reset="handleDrawerReset" @close="drawerVisible = false"></PreviewDrawer>
    </div>

    <!-- <pbiReturnTop v-if="isShowReturnTop" @returnTop="handleReturnTop"></pbiReturnTop> -->
  </div>
</template>
<script>
  import { testReportGet, testReportUpdateDate } from "@/api/modular/system/limsManager"
  import { mixin, chart } from "./mixin/index.js"

  export default {
    mixins: [mixin, chart],
    data: function () {
      return {
        id: null, // 请求接口的id
        update: false,

        /* 图表 */
        dcrEchart: null,
        growthEchart: null,
        isEditDcrXNum: 0,
        isEditGrowthXNum: 0,

        /* 右边表格 */
        originColumns: [],
        originData: [],

        dcrColumns: [],
        dcrGrowthColumns: [],
        dcrData: [],

        /* 在线编辑图表 */
        dcrRevealNum:0,

        // 第一张图表
        dcrCheckObj: {}, //点击图标的选中对象  editObj: axis 坐标轴  tag 线 legend 图例  tag: axs 选中的那条线
        dcrOriginalLegend: [], // 图例的原始值
        dcrOriginalSeries: [], // 图表数据原始值
        dcrEditData: {},
        dcrResetOriginal: {},

        growthRevealNum:0,

        // 第二张图
        growthCheckObj: {},
        growthOriginalLegend: [],
        growthOriginalSeries: [],
        growthEditData: {},
        growthResetOriginal: {}
      }
    },
    mounted() {
      this.init()
      // const box = this.$refs.wrapper
      // box.addEventListener("scroll", e => {
      //   if (e.target.scrollTop > 100 && !this.isShowReturnTop) {
      //     this.isShowReturnTop = true
      //   }
      //   if(e.target.scrollTop < 100 && this.isShowReturnTop){
      //     this.isShowReturnTop = false
      //   }
      // })
    },

    methods: {
      init() {
        this.id = this.$route.query.id
        testReportGet({ id: this.id })
          .then(res => {
            this.record = res.data
            this.allDataJson = JSON.parse(res.data.allDataJson)

            // 图例排序
            this.allDataJson.legendList = this.allDataJson.legendList.sort((item1,item2) => { return  Number(item1.replace("%SOC","")) -  Number(item2.replace("%SOC","")) })

            this.queryParam = JSON.parse(res.data.queryParam)
            this.originData = this.allDataJson.tableList
            this.dcrData = this.allDataJson.dcrList

            this.update = false
          })
          .then(async res => {
            // 右边表格数据
            if (this.originData.length > 0) {
              this.initTable()
            }

            this.dcrEditData = this._getInitData('dcr','edit')
            this.dcrResetOriginal = this._getInitData('dcr')

            this.growthEditData = this._getInitData('growth','edit')
            this.growthResetOriginal = this._getInitData('growth')

            await this.initDcr()
            await this.initGrowth()

            // 坐标轴原始值(默认y轴为数值轴)
            const dcrYAxis = this.dcrEchart.getModel().getComponent("yAxis").axis.scale
            this.dcrResetOriginal.yMin = dcrYAxis._extent[0]
            this.dcrResetOriginal.yMax = dcrYAxis._extent[1]
            this.dcrResetOriginal.yInterval = dcrYAxis._interval

            const growthYAxis = this.growthEchart.getModel().getComponent("yAxis").axis.scale
            this.growthResetOriginal.yMin = growthYAxis._extent[0]
            this.growthResetOriginal.yMax = growthYAxis._extent[1]
            this.growthResetOriginal.yInterval = growthYAxis._interval
          })
      },

      initTable() {
        this.originColumns = [
          {
            title: "循环周数",
            dataIndex: "cycle",
            align: "center",
            width: 60,
            customRender: (text, record, index) => {
              const obj = {
                children: text,
                attrs: {}
              }
              obj.attrs.rowSpan = 0

              let list = this.originData.filter(o => o.cycle == text)

              if (index == 0 || index % list.length == 0) {
                obj.attrs.rowSpan = list.length
              }

              return obj
            }
          },
          {
            title: "电芯",
            width: 60,
            align: "center",
            dataIndex: "soc",

            customRender: (text, record, index) => {
              const obj = {
                children: text,
                attrs: {}
              }
              obj.attrs.rowSpan = 0

              if (index == 0 || index % 2 == 0) {
                obj.attrs.rowSpan = 2
              }

              return obj
            }
          }
        ]

        for (let i = 0; i < this.originData[0].dataList.length; i++) {
          this.originColumns.push({
            title: i + 1 + "#",
            align: "center",
            children: [
              {
                title: "U",
                width: 60,
                align: "center",
                dataIndex: "dataList[" + i + "].u",
                customRender: (text, record, index) => {
                  const obj = {
                    children: this.update ? (
                      <a-input-number step="0.01" style="width:100%" v-model={record.dataList[i].u}></a-input-number>
                    ) : (
                      record.dataList[i].u
                    ),
                    attrs: {}
                  }
                  return obj
                }
              },
              {
                title: "I",
                width: 60,
                align: "center",
                dataIndex: "dataList[" + i + "].i",
                customRender: (text, record, index) => {
                  const obj = {
                    children: this.update ? (
                      <a-input-number step="0.01" style="width:100%" v-model={record.dataList[i].i}></a-input-number>
                    ) : (
                      record.dataList[i].i
                    ),
                    attrs: {}
                  }
                  return obj
                }
              }
            ]
          })
        }

        this.dcrColumns = [
          {
            title: "DCR (mΩ)",
            dataIndex: "socNum",
            align: "center",
            width: 60,
            customRender: (text, record, index) => {
              const obj = {
                children: text + "%SOC",
                attrs: {}
              }
              obj.attrs.rowSpan = 0

              let list = this.dcrData.filter(o => o.socNum == text)

              if (index == 0 || index % list.length == 0) {
                obj.attrs.rowSpan = list.length
              }

              return obj
            }
          },
          {
            title: "循环周数",
            width: 60,
            align: "center",
            dataIndex: "cycle"
          },
          {
            title: "Avg.",
            width: 60,
            align: "center",
            dataIndex: "average"
          }
        ]

        for (let i = 0; i < this.dcrData[0].dataList.length; i++) {
          this.dcrColumns.push({
            title: i + 1 + "#",
            align: "center",

            width: 60,
            align: "center",
            dataIndex: "dataList[" + i + "].dcr"
          })
        }

        this.dcrGrowthColumns = [
          {
            title: "DCR增长率(%)",
            dataIndex: "socNum",
            align: "center",
            width: 60,
            customRender: (text, record, index) => {
              const obj = {
                children: text + "%SOC",
                attrs: {}
              }
              obj.attrs.rowSpan = 0

              let list = this.dcrData.filter(o => o.socNum == text)

              if (index == 0 || index % list.length == 0) {
                obj.attrs.rowSpan = list.length
              }

              return obj
            }
          },
          {
            title: "循环周数",
            width: 60,
            align: "center",
            dataIndex: "cycle"
          },
          {
            title: "Avg.",
            width: 60,
            align: "center",
            dataIndex: "avgGrowth"
          }
        ]

        for (let i = 0; i < this.dcrData[0].dataList.length; i++) {
          this.dcrGrowthColumns.push({
            title: i + 1 + "#",
            align: "center",

            width: 60,
            align: "center",
            dataIndex: "dataList[" + i + "].growth"
          })
        }
      },

      initDcrOriginalData(checkData = []) {
        let dcrEchartsList = _.cloneDeep(this.allDataJson.dcrEcharts)
        let lineColorList = [] // 折线颜色

        let seriesList = []
        let dcrOriginalSeries = []
        let dcrOriginalCheckData = []
        // let duplicateDataOptions = []

        // 数据排序,避免颜色混乱
        dcrEchartsList.sort((item1,item2) => { return item1.soc -  item2.soc })
        const echartsColorList = this.allDataJson.legendList.length <= 2 ? this.echartsColorShortList : this.echartsColorLongList

        // 数据处理
        for (let i = 0; i < dcrEchartsList.length; i++) {
          // 设置折线的颜色
          const have = lineColorList.find(v => v.name === dcrEchartsList[i].soc)
          if (have == undefined) {
            lineColorList.push({ name: dcrEchartsList[i].soc, color: echartsColorList[lineColorList.length % echartsColorList.length] })
          }

          const temIndex = checkData.findIndex(findItem => findItem.soc.replace("%SOC","") == dcrEchartsList[i].soc)  //由于图例可以更改顺序，所以得定位到准确的位置
          
          let series = {
            name: dcrEchartsList[i].soc + "%SOC",
            index:i + 1,
            soc: dcrEchartsList[i].soc,
            id: dcrEchartsList[i].soc + "%SOC" + (i + 1),
            type: "line",
            barGap: 0,
            markPoint: {
              data: []
            },
            connectNulls:checkData.length === 0 ? false : Boolean(Number(checkData[temIndex].connectNulls)),
            symbol: checkData.length === 0 ? "rect" : checkData[temIndex].symbol,
            symbolSize: checkData.length === 0 ? 5 : checkData[temIndex].symbolSize,
            lineStyle: {
              width: checkData.length === 0 ? 1 : checkData[temIndex].lineWidth,
              type: checkData.length === 0 ? "solid" : checkData[temIndex].lineType,
              color:
                checkData.length === 0
                  ? lineColorList[lineColorList.findIndex(v => v.name === dcrEchartsList[i].soc)].color
                  : checkData[temIndex].lineColor
            },
            itemStyle: {
              color:
                checkData.length === 0
                  ? lineColorList[lineColorList.findIndex(v => v.name === dcrEchartsList[i].soc)].color
                  : checkData[temIndex].itemColor
            },
            emphasis: {
              focus: "series"
            },

            batteryNum: dcrEchartsList[i].batteryNum,
            data: dcrEchartsList[i].data.map((mapItem, index) => { return { id: index, name: mapItem.name, value: mapItem.value } }),
          }

          // 设置最大最小值
          if (checkData.length > 0 && checkData[temIndex].maxPoint) {
            series.markPoint.data.push({ type: "max", name: "Max" })
          }
          if (checkData.length > 0 && checkData[temIndex].minPoint) {
            series.markPoint.data.push({ type: "min", name: "Min" })
          }

          dcrOriginalSeries.push({
            id: dcrEchartsList[i].soc + "%SOC" + (i + 1),
            index:i + 1,
            soc: dcrEchartsList[i].soc + "%SOC",
            name: dcrEchartsList[i].soc + "%SOC",
            // data: dcrEchartsList[i].data.map(v => v.value[1].toString()),
            synchronization: checkData.length === 0 ? i : checkData[temIndex].synchronization,
            maxPoint: checkData.length === 0 ? false : checkData[temIndex].maxPoint,
            minPoint: checkData.length === 0 ? false : checkData[temIndex].minPoint,
            connectNulls:false,
            symbol: checkData.length === 0 ? "rect" : checkData[temIndex].symbol,
            symbolSize: checkData.length === 0 ? 5 : checkData[temIndex].symbolSize,
            itemColor:
              checkData.length === 0
                ? lineColorList[lineColorList.findIndex(v => v.name === dcrEchartsList[i].soc)].color
                : checkData[temIndex].itemColor,
            lineType: checkData.length === 0 ? "solid" : checkData[temIndex].lineType,
            lineWidth: checkData.length === 0 ? 1 : checkData[temIndex].lineWidth,
            lineColor:
              checkData.length === 0
                ? lineColorList[lineColorList.findIndex(v => v.name === dcrEchartsList[i].soc)].color
                : checkData[temIndex].lineColor
          })
          // 原始值
          dcrOriginalCheckData.push({
            soc: dcrEchartsList[i].soc + "%SOC",
            symbol: "rect",
            symbolSize: 5,
            itemColor: lineColorList[lineColorList.findIndex(v => v.name === dcrEchartsList[i].soc)].color,
            lineType: "solid",
            lineWidth: 1,
            lineColor: lineColorList[lineColorList.findIndex(v => v.name === dcrEchartsList[i].soc)].color
          })


          // duplicateDataOptions.push({
          //   id: dcrEchartsList[i].soc + "%SOC",
          //   data: dcrEchartsList[i].data.map((mapItem, index) => { return { id: index, name: mapItem.name, value: index, label: mapItem.value[1].toString() } })
          // })

          seriesList.push(series)
        }

        // return [dcrOriginalSeries, dcrOriginalCheckData, seriesList, duplicateDataOptions]
        return [dcrOriginalSeries, dcrOriginalCheckData, seriesList]

      },

      initDcr(
        legendData = {},
        checkData = [], //选中的数据
        axisData = {},
        titleData = {},
        gridData = {}
      ) {
        if (this.dcrEchart) this.dcrEchart.dispose();
        this.dcrEchart = this.echarts.init(this.$refs.dcr, "walden",{ devicePixelRatio: 2 })

        const processingResult = this.initDcrOriginalData(checkData)

        // 计算出  [0]:每行能容纳多少个图例   [1]:每个图例所占的位置
        const legendRevealNumResult = this.handleLegendRevealNum(this._getNewSocLegend(this.allDataJson.legendList[0]),501,legendData.legendWidth || 20,legendData.legendGap || 10,true)
        this.dcrRevealNum = legendRevealNumResult[0]

        if(!legendData.legendRevealList) this.dcrEditData.legendRevealList = _.cloneDeep(this.allDataJson.legendList).slice(0,this.dcrRevealNum * 2)
   
        this.dcrOriginalLegend = _.cloneDeep(this.allDataJson.legendList)

        this.dcrOriginalSeries = processingResult[0]
        this.dcrResetOriginal.checkData = processingResult[1]
        let dcrSeries = processingResult[2]
        // this.dcrEditData.duplicateDataOptions = _.cloneDeep(processingResult[3])

        this.dcrEditData.legend = _.cloneDeep(this.allDataJson.legendList)
        this.dcrEditData.series = _.cloneDeep(this.dcrOriginalSeries)

        /* 图例排序 开始 */
        if (legendData.legendSort) {
          this.dcrEditData.legend = _.cloneDeep(legendData.legendSort) // 将页面上的图例数组按照用户设置的顺序排序
        }
        this.dcrEditData.legendSort = _.cloneDeep(this.dcrEditData.legend) // 传回给在线编辑图表
        /* 图例排序 结束 */

        /* 图例变更名称 开始 */
        if (legendData.legendEditName) {
          // 遍历图例的变更
          legendData.legendEditName.forEach(v => {
            // 判断有新值，但是不是清空操作,修改对应的数据
            if (v.newName && !v.isReset) {
              // 修改原始图例  
              // 先找到原始图例改了新值（原始图例的值等于修改的原始值）（因为每次都在最开头赋予了最开始的原始图例）
              let temIndex1 = this.dcrOriginalLegend.findIndex(findItem => findItem == v.originName)
              this.dcrOriginalLegend[temIndex1] = v.newName

              // 修改在线编辑图表的选中值的名称
              let temIndex2 = this.dcrEditData.legend.findIndex(findItem => findItem == v.originName)
              this.dcrEditData.legend[temIndex2] = v.newName

              // 修改this.dcrEditData.series
              this.dcrEditData.series.forEach(findItem => {
                findItem.name = findItem.name == v.originName ? v.newName : findItem.name
              })

              // 修改图上的图表数据的图例
              dcrSeries.forEach(findItem => {
                findItem.name = findItem.name == v.originName ? v.newName : findItem.name
              })
            }

            // 如果没有新值，然后是重置的
            if (!v.newName && v.isReset) {
              // 操作
              // 操作完之后isReset设为false
              v.previousName = ''
              v.isReset = false
            }
          })

          // 赋予修改后的图例修改名称数组
          this.dcrEditData.legendEditName = legendData.legendEditName
        }

        // 图例修改名称数组  使用位置：在线编辑图表--图例--名称 ,首次进入，将图例的值给图例修改名称数组
        // originName 原始值 newName 新值 previousName 上一个值（用于清空的情况下使用）isReset 是否重置（用于清空的时候使用）
        if (this.dcrEditData.legendEditName.length === 0) {
          this.dcrEditData.legendEditName = this.dcrOriginalLegend.map(v => { return { originName: v, previousName: '', newName: '', isReset: false } })
        }
        /* 图例变更名称 开始 */

        /* 没选中的数据的移除处理 */
        if (legendData.legendEdit) {

          // 移除页面上的对应的图例
          for (let i = 0; i < this.dcrEditData.legend.length; i++) {
            if (!legendData.legendEdit.includes(this.dcrEditData.legend[i])) {
              this.dcrEditData.legend.splice(i, 1)
              i--
            }
          }

          // 移除页面上的对应的图例的图表数据
          for (let i = 0; i < dcrSeries.length; i++) {
            if (!legendData.legendEdit.includes(dcrSeries[i].name)) {
              dcrSeries.splice(i, 1)
              i--
            }
          }


          // 判断依据
          for (let i = 0; i < checkData.length; i++) {
            if (!legendData.legendEdit.includes(checkData[i].name)) {
              checkData.splice(i, 1)
              i--
            }
          }
        }

        // // 处理选中折线数据
        // if (checkData.length > 0) {
        //   dcrSeries.forEach((v, index) => {
        //     const handIndex = checkData.findIndex(findItem => findItem.id == v.id)
        //     for (let i = 0; i < v.data.length; i++) {
        //       if (!checkData[handIndex].duplicateData.includes(v.data[i].id)  && v.data[i].value[1] !== '') {
        //         v.data.splice(i, 1)
        //         i--
        //       }
        //     }
        //   })
        // }

        // 如果X轴是类目轴，数字转为字符串
        if(!axisData.xType || axisData.xType === 'category'){
          dcrSeries.forEach(forItem => {
            forItem.data = forItem.data.map(mapItem => { return { id:mapItem.id,name:mapItem.name,value: [mapItem.value[0].toString(),mapItem.value[1]] } } )
          })
        }

        if(!axisData.yType || axisData.yType === 'category'){
          dcrSeries.forEach(forItem => {
            forItem.data = forItem.data.map(mapItem => { return { id:mapItem.id,name:mapItem.name,value: [mapItem.value[0],mapItem.value[1].toString()] } } )
          })
        }


        // 结合图例数据（线+图例都存在）和图例显隐（只存在线，剔除图例）
        const legend = this._getLegend(this.dcrEditData)

        // option
        let dcrOption = {
          backgroundColor: '#ffffff',
          animationDuration: 2000,
          title: {
            text: titleData.chartTitle || "DCR@ " + this.queryParam.reportBasic.current + "A Cycle Life",
            left: "center",
            top: titleData.titleTop || 10,
            fontSize: 18,
            fontWeight: 500,
            color: "#000"
          },
          grid: {
            show: true,
            top: gridData.gridTop || 45,
            left: gridData.gridLeft || 55,
            right: gridData.gridRight || 30,
            bottom: gridData.gridBottom || 50,
            borderWidth: 0.5,
            borderColor: "#ccc"
          },
          textStyle: {
            fontFamily: "Times New Roman"
          },
          tooltip: {
            trigger: "axis",
            formatter: function (params) {
              var result = params[0].axisValue + "<br>" // 添加 x 轴的数值
              params.forEach(function (item, dataIndex) {
                var type = item.data.name + "#"
                result +=
                  item.marker +
                  item.seriesName +
                  '<div style="width:20px;display: inline-block;"></div><div style="display: inline-block;">' +
                  type +
                  "</div>" +
                  item.value[1] +
                  "<br>" // 添加每个系列的数值
              })
              return result
            }
          },

          legend: {
            backgroundColor: legendData.legendBgColor || "#f5f5f5",
            data: legend,
            itemWidth: legendData.legendWidth || 20,
            itemHeight: legendData.legendHeight || 5,
            itemGap: legendData.legendGap || 10,
            orient: legendData.legendOrient || 'horizontal',
            top: legendData.legendTop || 50,
            left: legendData.legendLeft || 'center',
            textStyle: {
              fontSize: 14,
              color: "#000000"
            },
            formatter: name => {
              return this._getNewSocLegend(name)
            },
          },

          xAxis: [
            {
              type: axisData.xType || 'category',
              axisTick: { show: false },
              boundaryGap: false,
              splitLine: {
                show: true, // 显示分隔线
                lineStyle: {
                  type: "solid",
                  width: 0.5
                }
              },
              axisLabel: {
                show: true,
                width: 0.5,
                textStyle: {
                  fontSize: "15",
                  color: "#000000"
                }
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#ccc",
                  width: 0.5
                }
              },
              name: titleData.XTitle || "Cycles",
              nameLocation: "middle", // 将名称放在轴线的中间位置
              nameGap: 25,
              nameTextStyle: {
                fontSize: 14,
                fontWeight: 500,
                color: "#000000" // 可以根据需要调整字体大小
              }
            }
          ],
          yAxis: [
            {
              type: axisData.yType || 'value',
              name: titleData.YTitle || "DCR (mΩ)",
              position: "left",
              nameGap: titleData.yTitleLetf || 30,
              splitLine: {
                show: true, // 显示分隔线
                lineStyle: {
                  type: "solid",
                  width: 0.5
                }
              },
              axisTick: {
                show: true // 显示刻度
              },
              axisLabel: {
                show: true,
                width: 0.5,
                textStyle: {
                  fontSize: "15",
                  color: "#000000"
                }
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#ccc",
                  width: 0.5
                }
              },
              nameLocation: "middle", // 将名称放在轴线的起始位置
              nameRotate: 90, // 旋转角度，使名称竖排
              nameTextStyle: {
                fontSize: 14, // 可以根据需要调整字体大小
                fontWeight: 500,
                color: "#000000"
              }
            }
          ],

          series: dcrSeries
        }

        // 传回给在线编辑图表，当前图标上有的点
        // this.dcrEditData.duplicateCheckedList = dcrSeries.map(mapItem => mapItem.data.filter(filterItem => filterItem.value[1] !== '').map(mapItem2 => mapItem2.id))

        // 处理坐标轴
        if (axisData.xMin) {
          dcrOption.xAxis[0].min = axisData.xMin
        }
        if (axisData.xMax) {
          dcrOption.xAxis[0].max = axisData.xMax
        }
        if (axisData.xInterval) {
          dcrOption.xAxis[0].interval = axisData.xInterval
        }
        if (axisData.yMin) {
          dcrOption.yAxis[0].min = axisData.yMin
        }
        if (axisData.yMax) {
          dcrOption.yAxis[0].max = axisData.yMax
        }
        if (axisData.yInterval) {
          dcrOption.yAxis[0].interval = axisData.yInterval
        }

        // 坐标轴类型赋值
        this.dcrEditData.xType = dcrOption.xAxis[0].type
        this.dcrEditData.yType = dcrOption.yAxis[0].type

        this.dcrEchart.clear()
        this.dcrEchart.getZr().off('click')
        this.dcrEchart.getZr().on('click', params => {
          const { target, topTarget } = params

          // Z 0:坐标轴
          if (topTarget?.z === 0 && this.drawerVisible) {
            this.$set(this.dcrCheckObj, 'editObj', 'axis')
          }
          // Z 3:线 
          if (topTarget?.z === 3 && this.drawerVisible) {
            const axs = target.parent?.parent?.__ecComponentInfo?.index
            this.$set(this.dcrCheckObj, 'tag', axs)
            this.$set(this.dcrCheckObj, 'editObj', 'tag')
          }
          // Z 4:图例
          if (topTarget?.z === 4 && this.drawerVisible) {
            const axs = target.parent?.__legendDataIndex
            this.$set(this.dcrCheckObj, 'editObj', 'legend')
          }
        });

        this.dcrEchart.setOption(dcrOption)
        // let graphicData = []
        // dcrSeries.forEach(forItem => {
        //   graphicData =  graphicData.concat(forItem.data)
        // })


        /* 拖拉拽开始  */

        // const that = this
        // // 拖拉拽
        // setTimeout(() => {
        //   // 在图上添加图层
        //   that.dcrEchart.setOption({
        //     graphic:
        //     that.echarts.util.map(graphicData, (item, dataIndex) => { // 此处需要把两条曲线的数组拼成一个数组进行遍历，多条曲线类似
        //       const temIndex = Math.trunc(dataIndex / dcrSeries[0].data.length)
        //       return {
        //           type: 'circle',
        //           position: that.dcrEchart.convertToPixel('grid', item.value),
        //           shape: {
        //             r: dcrSeries[temIndex].symbolSize / 2
        //           },
        //           // style: {
        //           //   fill: '#FF6347', // 设置填充颜色，这里使用的是番茄红
        //           // },
        //           invisible: true,
        //           draggable: true,
        //           ondrag: that.echarts.util.curry(onPointDragging, dataIndex), // 添加拖动的回调
        //           ondragend: function ($event) {
        //             ondragend($event,dataIndex)
        //           },
        //           z: 1000
        //         };
        //       })

        //   });
        // }, 0);

        // function ondragend(event,dataIndex){
        //   that.dcrEchart.setOption({
        //     graphic:
        //     that.echarts.util.map(graphicData, (item, dataIndex) => { // 此处需要把两条曲线的数组拼成一个数组进行遍历，多条曲线类似
        //           return {
        //             position: that.dcrEchart.convertToPixel('grid', item.value),
        //         };
        //       })
        //   })
        // }

        // 拖动的处理函数，因为把所有的曲线数组拼成了一个数组，所以添加的图层是一个整体，此处需要拆分图层，还原为两条曲线
        // function onPointDragging(dataIndex, dx, dy) {
        //   const dataLength = dcrSeries[0].data.length
        //   const temIndex = dataIndex - (Math.trunc(dataIndex / dataLength) * dataLength)

        //   let XValue = '',Yvalue = ''

        //   // 如果X轴是类目轴
        //   if(!axisData.xType ||  axisData.xType === 'category'){
        //     let categoriesX = []

        //     // 获取X轴所有类目
        //     dcrSeries.forEach(forItem => {
        //       categoriesX = _.uniq(categoriesX.concat(forItem.data.map(mapItem => mapItem.value[0])))
        //     })

        //     // 估算每个类目的大概宽度
        //     let categoryXWidth = (that.dcrEchart.getWidth() - (Object.keys(gridData).length === 0  ? 80 + 40 : gridData.gridLeft + gridData.gridRight )) / categoriesX.length

        //     // 获取移动到那个类目轴
        //     let nearestXIndex = Math.min(categoriesX.length, Math.floor(this.position[0] / categoryXWidth));
        //     XValue = categoriesX[nearestXIndex - 1]


        //   }

        //   // 如果X轴是数值轴
        //   if(axisData.xType === 'value'){
        //     XValue = that.dcrEchart.convertFromPixel('grid', this.position)[0]
        //   }

        //   // 如果Y轴是类目轴
        //   if( axisData.yType === 'category'){
        //     let categoriesY = []

        //     // 获取Y轴所有类目
        //     dcrSeries.forEach(forItem => {
        //       categoriesY = _.uniq(categoriesY.concat(forItem.data.map(mapItem => mapItem.value[1])))
        //     })

        //     // 估算每个类目的大概宽度
        //     let categoryYHeight = (that.dcrEchart.getHeight() - (Object.keys(gridData).length === 0  ? 60 + 70 : gridData.gridTop + gridData.gridBottom )) / categoriesY.length

        //     // 获取移动到那个类目轴
        //     let nearestYIndex = Math.min(categoriesY.length, Math.floor(this.position[1] / categoryYHeight));
        //     Yvalue = categoriesY.reverse()[nearestYIndex - 1]
        //   }

        //   if(!axisData.xType || axisData.yType === 'value'){
        //     Yvalue = that.dcrEchart.convertFromPixel('grid', this.position)[1]
        //   }

        //   // 赋值
        //   dcrSeries[Math.trunc(dataIndex / dataLength)].data[temIndex] = {
        //       id:dcrSeries[Math.trunc(dataIndex / dataLength)].data[temIndex].id,
        //       name:dcrSeries[Math.trunc(dataIndex / dataLength)].data[temIndex].name,
        //       value: [XValue, Yvalue]
        //     }

        //   graphicData[dataIndex].value = [XValue, Yvalue]

        //   // 更新图表
        //   that.dcrEchart.setOption({
        //    series: dcrSeries
        //  })

        // }
        /* 拖拉拽结束  */





        // 如果坐标轴类型为数值轴，则计算出最大值最小值，以及间距
        if (dcrOption.xAxis[0].type === "value") {
          const dcrXAxis = this.dcrEchart.getModel().getComponent("xAxis").axis.scale
          this.dcrEditData.xMin = dcrXAxis._extent[0]
          this.dcrEditData.xMax = dcrXAxis._extent[1]
          this.dcrEditData.xInterval = dcrXAxis._interval
        }

        if (dcrOption.yAxis[0].type === "value") {
          const dcrYAxis = this.dcrEchart.getModel().getComponent("yAxis").axis.scale
          this.dcrEditData.yMin = dcrYAxis._extent[0]
          this.dcrEditData.yMax = dcrYAxis._extent[1]
          this.dcrEditData.yInterval = dcrYAxis._interval
        }

        if (this.isEditDcrXNum === 0 && axisData.xType === "value") {
          this.isEditDcrXNum++
          this.dcrResetOriginal.xMin = this.dcrEditData.xMin
          this.dcrResetOriginal.xMax = this.dcrEditData.xMax
          this.dcrResetOriginal.xInterval = this.dcrEditData.xInterval
        }
      },

      initGrowthOriginalData(checkData = []) {
        let dcrEchartsList = _.cloneDeep(this.allDataJson.dcrEcharts)
        let growthEchartsList = _.cloneDeep(this.allDataJson.growthEcharts)

        let lineColorList = []
        let seriesList = []
        let seriesOriginal = []
        let checkDataOriginal = []

        // let duplicateDataOptions = []

        // 数据排序,避免颜色混乱
        growthEchartsList.sort((item1,item2) => { return item1.soc -  item2.soc })
        const echartsColorList = this.allDataJson.legendList.length <= 2 ? this.echartsColorShortList : this.echartsColorLongList


        for (let i = 0; i < growthEchartsList.length; i++) {
          const have = lineColorList.find(v => v.name === growthEchartsList[i].soc)
          if (have == undefined) {
            lineColorList.push({ name: growthEchartsList[i].soc, color: echartsColorList[lineColorList.length % echartsColorList.length] })
          }

          const temIndex = checkData.findIndex(findItem => findItem.soc.replace("%SOC","") == growthEchartsList[i].soc)  //由于图例可以更改顺序，所以得定位到准确的位置

          let series = {
            name: growthEchartsList[i].soc + "%SOC",
            index:i + 1,
            soc: growthEchartsList[i].soc,
            id: growthEchartsList[i].soc + "%SOC" + (i + 1),
            type: "line",
            barGap: 0,
            markPoint: {
              data: []
            },
            connectNulls:checkData.length === 0 ? false : Boolean(Number(checkData[temIndex].connectNulls)) ,
            symbol: checkData.length === 0 ? "rect" : checkData[temIndex].symbol,
            symbolSize: checkData.length === 0 ? 5 : checkData[temIndex].symbolSize,
            lineStyle: {
              width: checkData.length === 0 ? 1 : checkData[temIndex].lineWidth,
              type: checkData.length === 0 ? "solid" : checkData[temIndex].lineType,
              color:
                checkData.length === 0
                  ? lineColorList[lineColorList.findIndex(v => v.name === growthEchartsList[i].soc)].color
                  : checkData[temIndex].lineColor
            },
            itemStyle: {
              color:
                checkData.length === 0
                  ? lineColorList[lineColorList.findIndex(v => v.name === growthEchartsList[i].soc)].color
                  : checkData[temIndex].itemColor
            },
            batteryNum: growthEchartsList[i].batteryNum,
            emphasis: {
              focus: "series"
            },
            data: growthEchartsList[i].data.map((mapItem, index) => { return { id: index, name: mapItem.name, value: mapItem.value } })
          }


          if (checkData.length > 0 && checkData[temIndex].maxPoint) {
            series.markPoint.data.push({ type: "max", name: "Max" })
          }
          if (checkData.length > 0 && checkData[temIndex].minPoint) {
            series.markPoint.data.push({ type: "min", name: "Min" })
          }

          seriesOriginal.push({
            id: growthEchartsList[i].soc + "%SOC" + (i + 1),
            index:i + 1,
            soc: growthEchartsList[i].soc + "%SOC",
            name: growthEchartsList[i].soc + "%SOC",
            // data: growthEchartsList[i].data.map(v => v.value[1].toString()),
            synchronization: checkData.length === 0 ? i : checkData[temIndex].synchronization,
            maxPoint: checkData.length === 0 ? false : checkData[temIndex].maxPoint,
            minPoint: checkData.length === 0 ? false : checkData[temIndex].minPoint,
            connectNulls:false,
            symbol: checkData.length === 0 ? "rect" : checkData[temIndex].symbol,
            symbolSize: checkData.length === 0 ? 5 : checkData[temIndex].symbolSize,
            itemColor:
              checkData.length === 0
                ? lineColorList[lineColorList.findIndex(v => v.name === growthEchartsList[i].soc)].color
                : checkData[temIndex].itemColor,
            lineType: checkData.length === 0 ? "solid" : checkData[temIndex].lineType,
            lineWidth: checkData.length === 0 ? 1 : checkData[temIndex].lineWidth,
            lineColor:
              checkData.length === 0
                ? lineColorList[lineColorList.findIndex(v => v.name === growthEchartsList[i].soc)].color
                : checkData[temIndex].lineColor
          })
          checkDataOriginal.push({
            soc: growthEchartsList[i].soc + "%SOC",
            symbol: "rect",
            symbolSize: 5,
            itemColor: lineColorList[lineColorList.findIndex(v => v.name === growthEchartsList[i].soc)].color,
            lineType: "solid",
            lineWidth: 1,
            lineColor: lineColorList[lineColorList.findIndex(v => v.name === growthEchartsList[i].soc)].color
          })

          // duplicateDataOptions.push({
          //   id: growthEchartsList[i].soc + "%SOC",
          //   data: growthEchartsList[i].data.map((mapItem, index) => { return { id: index, name: mapItem.name, value: index, label: mapItem.value[1].toString() } })
          // })

          seriesList.push(series)
        }

        // return [seriesOriginal, checkDataOriginal, seriesList, duplicateDataOptions]
        return [seriesOriginal, checkDataOriginal, seriesList]

      },

      initGrowth(
        legendData = {},
        checkData = [],
        axisData = {},
        titleData = {},
        gridData = {}
      ) {
        if (this.growthEchart) this.growthEchart.dispose();
        this.growthEchart = this.echarts.init(this.$refs.growth, "walden",{ devicePixelRatio: 2 })

        const processingResult = this.initGrowthOriginalData(checkData)

        const legendRevealNumResult = this.handleLegendRevealNum(this._getNewSocLegend(this.allDataJson.legendList[0]),501,legendData.legendWidth || 20,legendData.legendGap || 10,true)
        this.growthRevealNum = legendRevealNumResult[0]

        // 首次渲染,所有图例都显示
        if(!legendData.legendRevealList) this.growthEditData.legendRevealList = _.cloneDeep(this.allDataJson.legendList).slice(0,this.growthRevealNum * 2)
        
        this.growthOriginalLegend = _.cloneDeep(this.allDataJson.legendList)

        this.growthOriginalSeries = processingResult[0]
        this.growthResetOriginal.checkData = processingResult[1]
        let growthSeries = processingResult[2]
        // this.growthEditData.duplicateDataOptions = _.cloneDeep(processingResult[3])

        this.growthEditData.legend = _.cloneDeep(this.allDataJson.legendList)
        this.growthEditData.series = _.cloneDeep(this.growthOriginalSeries)

        /* 图例排序 开始 */
        if (legendData.legendSort) {
          this.growthEditData.legend = _.cloneDeep(legendData.legendSort)
        }
        this.growthEditData.legendSort = _.cloneDeep(this.growthEditData.legend)
         /* 图例排序 结束 */

        /* 图例变更名称 开始 */
        if (legendData.legendEditName) {
          legendData.legendEditName.forEach(v => {
            if (v.newName && !v.isReset) {
              let temIndex1 = this.growthOriginalLegend.findIndex(findItem => findItem == v.originName)
              this.growthOriginalLegend[temIndex1] = v.newName

              let temIndex2 = this.growthEditData.legend.findIndex(findItem => findItem == v.originName)
              this.growthEditData.legend[temIndex2] = v.newName

              this.growthEditData.series.forEach(findItem => {
                findItem.name = findItem.name == v.originName ? v.newName : findItem.name
              })

              growthSeries.forEach(findItem => {
                findItem.name = findItem.name == v.originName ? v.newName : findItem.name
              })
            }

            if (!v.newName && v.isReset) {
              v.previousName = ''
              v.isReset = false
            }
          })
          this.growthEditData.legendEditName = legendData.legendEditName
        }
        if (this.growthEditData.legendEditName.length === 0) {
          this.growthEditData.legendEditName = this.growthOriginalLegend.map(v => { return { originName: v, previousName: '', newName: '', isReset: false } })
        }
        /* 图例变更名称 开始 */

        if (legendData.legendEdit) {
          for (let i = 0; i < this.growthEditData.legend.length; i++) {
            if (!legendData.legendEdit.includes(this.growthEditData.legend[i])) {
              this.growthEditData.legend.splice(i, 1)
              i--
            }
          }

          for (let i = 0; i < growthSeries.length; i++) {
            if (!legendData.legendEdit.includes(growthSeries[i].name)) {
              growthSeries.splice(i, 1)
              i--
            }
          }

          for (let i = 0; i < checkData.length; i++) {
            if (!legendData.legendEdit.includes(checkData[i].name)) {
              checkData.splice(i, 1)
              i--
            }
          }
        }

        // if (checkData.length > 0) {
        //   growthSeries.forEach((v, index) => {
        //     const handIndex = checkData.findIndex(findItem => findItem.id == v.id)
        //     for (let i = 0; i < v.data.length; i++) {
        //       if (!checkData[handIndex].duplicateData.includes(v.data[i].id) && v.data[i].value[1] !== '') {
        //         v.data.splice(i, 1)
        //         i--
        //       }
        //     }
        //   })
        // }

        // 结合图例数据（线+图例都存在）和图例显隐（只存在线，剔除图例）
        const legend = this._getLegend(this.growthEditData)

        let growthOption = {
          backgroundColor: '#ffffff',
          animationDuration: 2000,
          title: {
            text: titleData.chartTitle || "DCR Growth@ " + this.queryParam.reportBasic.current + "A Cycle Life",
            left: "center",
            top: titleData.titleTop || 10,
            fontSize: 18,
            fontWeight: 500,
            color: "#000"
          },
          grid: {
            show: true,
            top: gridData.gridTop || 45,
            left: gridData.gridLeft || 55,
            right: gridData.gridRight || 30,
            bottom: gridData.gridBottom || 50,
            borderWidth: 0.5,
            borderColor: "#ccc"
          },
          textStyle: {
            fontFamily: "Times New Roman"
          },
          tooltip: {
            trigger: "axis",
            formatter: function (params) {
              var result = params[0].axisValue + "<br>" // 添加 x 轴的数值
              params.forEach(function (item, dataIndex) {
                var type = item.data.name + "#"
                result +=
                  item.marker +
                  item.seriesName +
                  '<div style="width:20px;display: inline-block;"></div><div style="width:150px;display: inline-block;">' +
                  type +
                  "</div>" +
                  item.value[1] +
                  "<br>" // 添加每个系列的数值
              })
              return result
            }
          },

          legend: {
            data: legend,
            backgroundColor: legendData.legendBgColor || "#f5f5f5",
            itemWidth: legendData.legendWidth || 20,
            itemHeight: legendData.legendHeight || 5,
            itemGap: legendData.legendGap || 10,
            orient: legendData.legendOrient || 'horizontal',
            top: legendData.legendTop || 50,
            left: legendData.legendLeft || 'center',
            textStyle: {
              fontSize: 14,
              color: "#000000"
            },
            formatter: name => {
              return this._getNewSocLegend(name)
            },
          },

          xAxis: [
            {
              type: axisData.xType || 'category',
              axisTick: { show: false },
              boundaryGap: false,
              splitLine: {
                show: true, // 显示分隔线
                lineStyle: {
                  type: "solid",
                  width: 0.5
                }
              },
              axisLabel: {
                show: true,
                textStyle: {
                  fontSize: "13",
                  color: "#000000"
                }
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#ccc",
                  width: 0.5
                }
              },
              name: titleData.XTitle || "Cycles",
              nameLocation: "middle", // 将名称放在轴线的中间位置
              nameGap: 25,
              nameTextStyle: {
                fontSize: 14, // 可以根据需要调整字体大小
                fontWeight: 500,
                color: "#000000"
              }
            }
          ],
          yAxis: [
            {
              type: axisData.yType || 'value',
              name: titleData.YTitle || "DCR Growth (%)",
              position: "left",
              nameGap: titleData.yTitleLetf || 30,
              splitLine: {
                show: true, // 显示分隔线
                lineStyle: {
                  type: "solid",
                  width: 0.5
                }
              },
              axisTick: {
                show: true // 显示刻度
              },
              axisLabel: {
                show: true,
                textStyle: {
                  fontSize: "13",
                  color: "#000000"
                }
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#ccc",
                  width: 0.5
                }
              },
              nameLocation: "middle", // 将名称放在轴线的起始位置
              nameRotate: 90, // 旋转角度，使名称竖排
              nameTextStyle: {
                fontSize: 14, // 可以根据需要调整字体大小
                fontWeight: 500,
                color: "#000000"
              }
            }
          ],
          series: growthSeries
        }

        // 传回给在线编辑图表，当前图标上有的点
        // this.growthEditData.duplicateCheckedList = growthSeries.map(mapItem => mapItem.data.filter(filterItem => filterItem.value[1] !== '').map(mapItem2 => mapItem2.id))


        if (axisData.xMin) {
          growthOption.xAxis[0].min = axisData.xMin
        }
        if (axisData.xMax) {
          growthOption.xAxis[0].max = axisData.xMax
        }
        if (axisData.xInterval) {
          growthOption.xAxis[0].interval = axisData.xInterval
        }
        if (axisData.yMin) {
          growthOption.yAxis[0].min = axisData.yMin
        }
        if (axisData.yMax) {
          growthOption.yAxis[0].max = axisData.yMax
        }
        if (axisData.yInterval) {
          growthOption.yAxis[0].interval = axisData.yInterval
        }

        this.growthEditData.xType = growthOption.xAxis[0].type
        this.growthEditData.yType = growthOption.yAxis[0].type

        this.growthEchart.clear()

        this.growthEchart.getZr().off('click')
        this.growthEchart.getZr().on('click', params => {
          const { target, topTarget } = params

          if (topTarget?.z === 0 && this.drawerVisible) {
            this.$set(this.growthCheckObj, 'editObj', 'axis')
          }
          if (topTarget?.z === 3 && this.drawerVisible) {
            const axs = target.parent?.parent?.__ecComponentInfo?.index
            this.$set(this.growthCheckObj, 'tag', axs)
            this.$set(this.growthCheckObj, 'editObj', 'tag')
          }
          if (topTarget?.z === 4 && this.drawerVisible) {
            const axs = target.parent?.__legendDataIndex
            this.$set(this.growthCheckObj, 'editObj', 'legend')
          }
        });

        this.growthEchart.setOption(growthOption)

        if (growthOption.xAxis[0].type === "value") {
          const growthXAxis = this.growthEchart.getModel().getComponent("xAxis").axis.scale
          this.growthEditData.xMin = growthXAxis._extent[0]
          this.growthEditData.xMax = growthXAxis._extent[1]
          this.growthEditData.xInterval = growthXAxis._interval
        }

        if (growthOption.yAxis[0].type === "value") {
          const growthYAxis = this.growthEchart.getModel().getComponent("yAxis").axis.scale
          this.growthEditData.yMin = growthYAxis._extent[0]
          this.growthEditData.yMax = growthYAxis._extent[1]
          this.growthEditData.yInterval = growthYAxis._interval
        }
        if (this.isEditGrowthXNum === 0 && axisData.xType === "value") {
          this.isEditGrowthXNum++
          this.growthResetOriginal.xMin = this.growthEditData.xMin
          this.growthResetOriginal.xMax = this.growthEditData.xMax
          this.growthResetOriginal.xInterval = this.growthEditData.xInterval
        }
      },

      updateData() {
        testReportUpdateDate(this.originData, this.id).then(res => {
          this.init()
        })
      },

      
      // 生成
      handleDrawerSubmit(value) {
        const legendData = {
          legendEdit: value.legendList,
          legendRevealList: value.legendRevealList,
          legendWidth: value.legendWidth,
          legendHeight: value.legendHeight,
          legendGap: value.legendGap,
          legendSort: value.legendSort, // 图例排序
          legendEditName: value.legendEditName,
          legendBgColor: value.legendBgColor,
          legendOrient: value.legendOrient,
          legendTop: value.legendTop,
          legendLeft: value.legendLeft,
        }

        const axisData = {
          xMin: value.xMin,
          xMax: value.xMax,
          xInterval: value.xInterval,
          xType: value.xType,

          yMin: value.yMin,
          yMax: value.yMax,
          yInterval: value.yInterval,
          yType: value.yType
        }

        const titleData = {
          chartTitle: value.chartTitle,
          XTitle: value.XTitle,
          YTitle: value.YTitle,
          titleTop: value.titleTop,
          yTitleLetf: value.yTitleLetf,
        }

        const gridData = {
          gridTop: value.gridTop,
          gridLeft: value.gridLeft,
          gridRight: value.gridRight,
          gridBottom: value.gridBottom,
        }

        // 赋值的数组
        const assignArr = ['chartTitle', 'XTitle', 'YTitle', 'titleTop','yTitleLetf','legendRevealList', 'legendWidth', 'legendHeight', 'legendGap', 'legendEditName', 'legendOrient','legendBgColor', 'legendTop', 'legendX', 'xMin', 'xMax', 'xInterval', 'xType',
          'yMin', 'yMax', 'yInterval', 'yType', 'synchronization', 'gridTop', 'gridLeft', 'gridRight', 'gridBottom',"targetEditObj"]

        if (this.editObj === "dcr") {
          this.dcrEditData.series = _.cloneDeep(value.checkData)

          for (let i = 0; i < assignArr.length; i++) {
            this.dcrEditData[assignArr[i]] = value[assignArr[i]]
          }

          this.initDcr(
            legendData,
            value.checkData,
            axisData,
            titleData,
            gridData
          )
        } else if (this.editObj === "growth") {
          this.growthEditData.series = _.cloneDeep(value.checkData)

          for (let i = 0; i < assignArr.length; i++) {
            this.growthEditData[assignArr[i]] = value[assignArr[i]]
          }

          this.initGrowth(
            legendData,
            value.checkData,
            axisData,
            titleData,
            gridData
          )
        }

        this.$forceUpdate()
      },

      // 重置
      handleDrawerReset(editObj = null) {

        if(editObj !== null) this.editObj = editObj

        if (this.editObj === "dcr") {
          this.dcrEditData.series = _.cloneDeep(this.dcrOriginalSeries)

          this.dcrEditData.legendEditName = []
          this.dcrEditData.legendWidth = 20
          this.dcrEditData.legendHeight = 5
          this.dcrEditData.legendGap = 10
          this.dcrEditData.legendTop = 50

          this.dcrEditData.gridTop = 45
          this.dcrEditData.gridLeft = 55
          this.dcrEditData.gridRight = 30
          this.dcrEditData.gridBottom = 50

          this.dcrEditData.xMin = 0
          this.dcrEditData.xMax = 0
          this.dcrEditData.xInterval = 0
          this.dcrEditData.yMin = 0
          this.dcrEditData.yMax = 0
          this.dcrEditData.yInterval = 0

          this.dcrEditData.chartTitle = "DCR@ " + this.queryParam.reportBasic.current + "A Cycle Life"
          this.dcrEditData.XTitle = 'Cycles'
          this.dcrEditData.YTitle = 'DCR (mΩ)'
          this.dcrEditData.titleTop = 10
          this.dcrEditData.yTitleLetf = 30
          this.initDcr()
        } else if (this.editObj === "growth") {
          this.growthEditData.series = _.cloneDeep(this.growthOriginalSeries)

          this.growthEditData.legendEditName = []
          this.growthEditData.legendWidth = 20
          this.growthEditData.legendHeight = 5
          this.growthEditData.legendGap = 10
          this.growthEditData.legendTop = 50

          this.growthEditData.gridTop = 45
          this.growthEditData.gridLeft = 55
          this.growthEditData.gridRight = 30
          this.growthEditData.gridBottom = 50

          this.growthEditData.xMin = 0
          this.growthEditData.xMax = 0
          this.growthEditData.xInterval = 0
          this.growthEditData.yMin = 0
          this.growthEditData.yMax = 0
          this.growthEditData.yInterval = 0

          this.growthEditData.chartTitle = "DCR Growth@ " + this.queryParam.reportBasic.current + "A Cycle Life"
          this.growthEditData.XTitle = 'Cycles'
          this.growthEditData.YTitle = 'DCR Growth (%)'
          this.growthEditData.titleTop = 10
          this.growthEditData.yTitleLetf = 30

          this.initGrowth()
        }
        this.drawerVisible = false
      },
      
       // 获取编辑图表数据、原始图表数据
       _getInitData(targetObj,type = 'original'){
        const options = {
          XTitle: 'Cycles', //X轴标题
          titleTop: 10,
          yTitleLetf: 30,

          legendWidth: 20,
          legendHeight: 5,
          legendGap: 10,//图例间隙
          legendOrient: 'horizontal',
          legendTop: 50,
          legendBgColor:'#f5f5f5',

          gridTop: 45,
          gridLeft: 55,
          gridRight: 30,
          gridBottom: 50,

          xType: "category",
          xMin: 0,
          xMax: 0,
          xInterval: 0,

          yType: "value",
          yMin: 0,
          yMax: 0,
          yInterval: 0,
        }
        if(type === 'edit'){
          options.series = []
          options.legend = []
          options.legendSort = []
          options.legendEditName = []
        }
        if(type === 'original'){
          options.checkData = []
        }

        options.chartTitle = (targetObj === 'dcr' ? "DCR@ " : "DCR Growth@ ") + this.queryParam.reportBasic.current + "A Cycle Life"
        options.YTitle =targetObj === 'dcr' ? "DCR (mΩ)" : "DCR Growth (%)"

        return options
      },

      
    }
  }
</script>
<style lang="less" scoped>
  @import "./css/preview.less";
</style>