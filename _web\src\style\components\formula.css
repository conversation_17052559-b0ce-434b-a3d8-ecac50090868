/*
 * 公式渲染样式
 * 用于控制公式渲染过程中的显示效果和公式组件的通用样式
 */

/* 公式容器 */
.formula-container {
  position: relative;
  min-height: 30px;
  opacity: 1;
  transition: opacity 0.3s ease;
  width: 100%;
  overflow: hidden;
}

/* 公式渲染中状态 */
.formula-container.rendering,
.formula-preview-content.rendering,
.formula-cell.rendering,
.latex-cell.rendering {
  opacity: 0;
}

/* 公式渲染完成状态 */
.formula-container.rendered,
.formula-preview-content.rendered,
.formula-cell.rendered,
.latex-cell.rendered {
  opacity: 1;
}

/* 公式预览内容 */
.formula-preview-content {
  position: relative;
  min-height: 36px;
  display: flex;
  align-items: center;
  padding: 10px var(--spacing-md, 16px);
  background-color: #fff;
  border-radius: var(--border-radius-sm, 4px);
  margin-bottom: 8px;
  transition: opacity 0.3s ease;
  overflow-x: auto; /* 添加水平滚动 */
  max-width: 100%;
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent; /* Firefox */
}

/* 自定义滚动条样式 */
.formula-preview-content::-webkit-scrollbar {
  height: 6px;
  width: 6px;
}

.formula-preview-content::-webkit-scrollbar-track {
  background: transparent;
}

.formula-preview-content::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.formula-preview-content.main-formula {
  border-bottom: 1px dashed var(--border-color-split, #f0f0f0);
}

.formula-preview-content.sub-formula {
  border-top: 1px dashed var(--border-color-split, #f0f0f0);
  background-color: var(--background-color-light, #fafafa);
}

/* 公式标题样式 */
.formula-section-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin: 8px 0;
  padding: 6px 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
}

/* 公式加载指示器 - 隐藏文字，只使用背景遮罩 */
.formula-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 1); /* 完全不透明的白色背景 */
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.formula-container.rendering .formula-loading {
  opacity: 1;
}

/* 公式表达式列样式 */
.formula-cell, .latex-cell {
  position: relative;
  min-height: 30px;
  white-space: nowrap;
  overflow-x: auto;
  transition: opacity 0.3s ease;
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent; /* Firefox */
}

/* 自定义滚动条样式 */
.formula-cell::-webkit-scrollbar,
.latex-cell::-webkit-scrollbar {
  height: 6px;
  width: 6px;
}

.formula-cell::-webkit-scrollbar-track,
.latex-cell::-webkit-scrollbar-track {
  background: transparent;
}

.formula-cell::-webkit-scrollbar-thumb,
.latex-cell::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

/* 公式单元格内容包装器 */
.formula-cell-wrapper {
  display: inline-block;
  min-width: 100%;
  padding: 4px 0;
}

/* 公式单元格内部内容 */
.formula-cell-inner {
  display: inline-block;
  white-space: nowrap;
}

/* 公式编辑器容器 */
.formula-editor-content {
  height: 100%;
  overflow-y: auto;
  background-color: #fff;
  padding: var(--spacing-md, 16px);
  border-radius: var(--border-radius-base, 8px);
  border: 1px solid var(--border-color-split, #f0f0f0);
  box-shadow: var(--box-shadow-base, 0 2px 8px rgba(0, 0, 0, 0.05));
}

.formula-editor-section {
  margin-bottom: var(--spacing-lg, 24px);
  padding: var(--spacing-md, 16px);
  background-color: var(--background-color-light, #f9f9f9);
  border-radius: var(--border-radius-base, 8px);
  border: 1px solid var(--border-color-split, #eee);
}

.analysis-results-section {
  margin-top: var(--spacing-lg, 24px);
  padding: var(--spacing-md, 16px);
  background-color: var(--background-color-light, #f9f9f9);
  border-radius: var(--border-radius-base, 8px);
  border: 1px solid var(--border-color-split, #eee);
}

/* 系数面板样式 */
.coefficient-panel,
.coefficient-range-settings,
.formula-info-settings {
  margin-top: 0;
  padding: 0;
  background-color: #fff;
  border-radius: var(--border-radius-base, 8px);
  border: 1px solid var(--border-color-split, #f0f0f0);
  height: 100%;
  box-shadow: var(--box-shadow-base, 0 1px 2px rgba(0, 0, 0, 0.03));
}

.coefficient-panel > div:not(h5),
.coefficient-range-settings > div:not(h5),
.formula-info-settings > div:not(h5) {
  padding: var(--spacing-md, 16px);
}

/* 标题样式 */
.coefficient-panel h5,
.coefficient-range-settings h5,
.formula-info-settings h5,
.variable-wrapper h5,
.form-section h5 {
  font-size: var(--font-size-lg, 16px);
  font-weight: 700;
  margin-bottom: 0;
  color: var(--text-color-primary, #333);
  padding: 10px 0;
  background-color: var(--background-color-light, #f5f5f5);
  border-bottom: 1px solid var(--border-color-split, #f0f0f0);
  text-align: center;
  border-radius: var(--border-radius-base, 8px) var(--border-radius-base, 8px) 0 0;
}

/* 变量列表样式 */
.variable-wrapper {
  background-color: #fff;
  border-radius: var(--border-radius-base, 8px);
  border: 1px solid var(--border-color-split, #f0f0f0);
  overflow: hidden;
  box-shadow: var(--box-shadow-base, 0 1px 2px rgba(0, 0, 0, 0.03));
  margin-bottom: var(--spacing-md, 16px);
}

.variable-list {
  padding: var(--spacing-md, 16px);
  background-color: #fff;
}

.variable-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

/* 导入导出按钮样式 */
.import-export-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px 8px;
  background-color: var(--background-color-light, #f9f9f9);
  border-bottom: 1px solid var(--border-color-split, #f0f0f0);
}

.title-text {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color-secondary, #666);
}

.excel-button {
  margin-right: 8px;
  border-radius: var(--border-radius-sm, 4px);
  font-weight: 500;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.excel-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 统一标签页样式 - 适用于所有标签页 */
.unified-tabs,
.coefficient-tabs,
.formula-tabs,
.prediction-tabs,
.optimization-tabs,
.fitting-tabs,
.validation-tabs {
  background: #fff;
}

.unified-tabs :deep(.ant-tabs-bar),
.coefficient-tabs :deep(.ant-tabs-bar),
.formula-tabs :deep(.ant-tabs-bar),
.prediction-tabs :deep(.ant-tabs-bar),
.optimization-tabs :deep(.ant-tabs-bar),
.fitting-tabs :deep(.ant-tabs-bar),
.validation-tabs :deep(.ant-tabs-bar) {
  margin-bottom: 0;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.unified-tabs :deep(.ant-tabs-nav),
.coefficient-tabs :deep(.ant-tabs-nav),
.formula-tabs :deep(.ant-tabs-nav),
.prediction-tabs :deep(.ant-tabs-nav),
.optimization-tabs :deep(.ant-tabs-nav),
.fitting-tabs :deep(.ant-tabs-nav),
.validation-tabs :deep(.ant-tabs-nav) {
  margin-bottom: var(--spacing-md, 16px);
}

.unified-tabs :deep(.ant-tabs-tab),
.coefficient-tabs :deep(.ant-tabs-tab),
.formula-tabs :deep(.ant-tabs-tab),
.prediction-tabs :deep(.ant-tabs-tab),
.optimization-tabs :deep(.ant-tabs-tab),
.fitting-tabs :deep(.ant-tabs-tab),
.validation-tabs :deep(.ant-tabs-tab) {
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px 8px 0 0;
  margin-right: 2px;
  transition: all 0.3s ease;
  background: transparent;
  border: 1px solid transparent;
}

.unified-tabs :deep(.ant-tabs-tab:hover),
.coefficient-tabs :deep(.ant-tabs-tab:hover),
.formula-tabs :deep(.ant-tabs-tab:hover),
.prediction-tabs :deep(.ant-tabs-tab:hover),
.optimization-tabs :deep(.ant-tabs-tab:hover),
.fitting-tabs :deep(.ant-tabs-tab:hover),
.validation-tabs :deep(.ant-tabs-tab:hover) {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.05);
}

.unified-tabs :deep(.ant-tabs-tab-active),
.coefficient-tabs :deep(.ant-tabs-tab-active),
.formula-tabs :deep(.ant-tabs-tab-active),
.prediction-tabs :deep(.ant-tabs-tab-active),
.optimization-tabs :deep(.ant-tabs-tab-active),
.fitting-tabs :deep(.ant-tabs-tab-active),
.validation-tabs :deep(.ant-tabs-tab-active) {
  font-weight: 600;
  background: #fff;
  border-color: #f0f0f0;
  border-bottom: 2px solid #1890ff;
  color: #1890ff;
}

.unified-tabs :deep(.ant-tabs-ink-bar),
.coefficient-tabs :deep(.ant-tabs-ink-bar),
.formula-tabs :deep(.ant-tabs-ink-bar),
.prediction-tabs :deep(.ant-tabs-ink-bar),
.optimization-tabs :deep(.ant-tabs-ink-bar),
.fitting-tabs :deep(.ant-tabs-ink-bar),
.validation-tabs :deep(.ant-tabs-ink-bar) {
  background-color: var(--primary-color, #1890ff);
  height: 3px;
  border-radius: 3px 3px 0 0;
}

.unified-tabs :deep(.ant-tabs-content),
.coefficient-tabs :deep(.ant-tabs-content),
.formula-tabs :deep(.ant-tabs-content),
.prediction-tabs :deep(.ant-tabs-content),
.optimization-tabs :deep(.ant-tabs-content),
.fitting-tabs :deep(.ant-tabs-content),
.validation-tabs :deep(.ant-tabs-content) {
  padding: 16px;
}

.unified-tabs :deep(.ant-tabs-tabpane),
.coefficient-tabs :deep(.ant-tabs-tabpane),
.formula-tabs :deep(.ant-tabs-tabpane),
.prediction-tabs :deep(.ant-tabs-tabpane),
.optimization-tabs :deep(.ant-tabs-tabpane),
.fitting-tabs :deep(.ant-tabs-tabpane),
.validation-tabs :deep(.ant-tabs-tabpane) {
  height: 100%;
}

/* 高对比度模式支持 */
@media (forced-colors: active) {
  .formula-container,
  .formula-preview-content,
  .formula-cell,
  .latex-cell,
  .coefficient-panel,
  .coefficient-range-settings,
  .formula-info-settings,
  .variable-wrapper,
  .formula-editor-content,
  .formula-editor-section,
  .analysis-results-section,
  .unified-tabs,
  .coefficient-tabs,
  .formula-tabs,
  .prediction-tabs,
  .optimization-tabs,
  .fitting-tabs,
  .validation-tabs {
    forced-color-adjust: auto;
    border: 1px solid CanvasText;
  }

  .formula-loading {
    background-color: Canvas;
    border: 1px solid CanvasText;
  }

  .unified-tabs :deep(.ant-tabs-tab-active),
  .coefficient-tabs :deep(.ant-tabs-tab-active),
  .formula-tabs :deep(.ant-tabs-tab-active),
  .prediction-tabs :deep(.ant-tabs-tab-active),
  .optimization-tabs :deep(.ant-tabs-tab-active),
  .fitting-tabs :deep(.ant-tabs-tab-active),
  .validation-tabs :deep(.ant-tabs-tab-active) {
    forced-color-adjust: auto;
    background-color: Highlight;
    color: HighlightText;
  }

  h5 {
    color: CanvasText;
    border-bottom: 1px solid CanvasText;
  }
}
