{"version": 3, "sources": ["webpack://[name]/webpack/universalModuleDefinition", "webpack://[name]/webpack/bootstrap", "webpack://[name]/./sources/utils/helpers.js", "webpack://[name]/./sources/ext/drag_timeline/eventsManager.ts", "webpack://[name]/./sources/ext/drag_timeline/index.ts"], "names": ["root", "factory", "exports", "module", "define", "amd", "window", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "units", "second", "minute", "hour", "day", "week", "month", "quarter", "year", "arrayFilter", "arr", "callback", "result", "filter", "length", "getSecondsInUnit", "unit", "for<PERSON>ach", "workArray", "slice", "arrayMap", "map", "resArray", "push", "arrayFind", "find", "arrayDifference", "item", "arraySome", "hashToArray", "hash", "sortArrayOfHash", "field", "desc", "compare", "a", "b", "sort", "throttle", "timeout", "wait", "apply", "arguments", "setTimeout", "isArray", "obj", "Array", "undefined", "pop", "isDate", "getFullYear", "getMonth", "getDate", "isStringObject", "Function", "toString", "constructor", "isNumberObject", "isBooleanObject", "delay", "timer", "$cancelTimeout", "$pending", "args", "this", "clearTimeout", "$execute", "objectKeys", "keys", "requestAnimationFrame", "w", "webkitRequestAnimationFrame", "msRequestAnimationFrame", "mozRequestAnimationFrame", "oRequestAnimationFrame", "cb", "isEventable", "attachEvent", "detachEvent", "helpers_1", "EventsManager", "_this", "_mouseDown", "_calculateDirectionVector", "_trace", "dots", "vectors", "x", "y", "resultVector_1", "vector", "magnitude", "Math", "sqrt", "angleDegrees", "atan2", "abs", "PI", "_applyDndReadyStyles", "_timeline", "$task", "classList", "add", "_clearDndReadyStyles", "remove", "_getScrollPosition", "timeline", "gantt", "$ui", "get<PERSON>iew", "$config", "scrollX", "getScrollState", "position", "scrollY", "_countNewScrollPosition", "coords", "shiftX", "_startPoint", "shiftY", "_scrollState", "_setScrollPosition", "scroll", "_stopDrag", "event", "$root", "_originalR<PERSON>only", "config", "readonly", "_originAutoscroll", "autoscroll", "useKey", "drag_timeline", "_startDrag", "_a", "clientX", "clientY", "_domEvents", "_createDomEventScope", "destructor", "detachAll", "attach", "ignore", "enabled", "filterTargets", "join", "utils", "dom", "closest", "target", "document", "scrollPosition", "eventsManager_1", "ext", "dragTimeline"], "mappings": ";;;;;;;;;;;;CAAA,SAAAA,EAAAC,GACA,iBAAAC,SAAA,iBAAAC,OACAA,OAAAD,QAAAD,IACA,mBAAAG,eAAAC,IACAD,OAAA,mCAAAH,GACA,iBAAAC,QACAA,QAAA,iCAAAD,IAEAD,EAAA,iCAAAC,IARA,CASCK,OAAA,WACD,mBCTA,IAAAC,KAGA,SAAAC,EAAAC,GAGA,GAAAF,EAAAE,GACA,OAAAF,EAAAE,GAAAP,QAGA,IAAAC,EAAAI,EAAAE,IACAC,EAAAD,EACAE,GAAA,EACAT,YAUA,OANAU,EAAAH,GAAAI,KAAAV,EAAAD,QAAAC,IAAAD,QAAAM,GAGAL,EAAAQ,GAAA,EAGAR,EAAAD,QA0DA,OArDAM,EAAAM,EAAAF,EAGAJ,EAAAO,EAAAR,EAGAC,EAAAQ,EAAA,SAAAd,EAAAe,EAAAC,GACAV,EAAAW,EAAAjB,EAAAe,IACAG,OAAAC,eAAAnB,EAAAe,GAA0CK,YAAA,EAAAC,IAAAL,KAK1CV,EAAAgB,EAAA,SAAAtB,GACA,oBAAAuB,eAAAC,aACAN,OAAAC,eAAAnB,EAAAuB,OAAAC,aAAwDC,MAAA,WAExDP,OAAAC,eAAAnB,EAAA,cAAiDyB,OAAA,KAQjDnB,EAAAoB,EAAA,SAAAD,EAAAE,GAEA,GADA,EAAAA,IAAAF,EAAAnB,EAAAmB,IACA,EAAAE,EAAA,OAAAF,EACA,KAAAE,GAAA,iBAAAF,QAAAG,WAAA,OAAAH,EACA,IAAAI,EAAAX,OAAAY,OAAA,MAGA,GAFAxB,EAAAgB,EAAAO,GACAX,OAAAC,eAAAU,EAAA,WAAyCT,YAAA,EAAAK,UACzC,EAAAE,GAAA,iBAAAF,EAAA,QAAAM,KAAAN,EAAAnB,EAAAQ,EAAAe,EAAAE,EAAA,SAAAA,GAAgH,OAAAN,EAAAM,IAAqBC,KAAA,KAAAD,IACrI,OAAAF,GAIAvB,EAAA2B,EAAA,SAAAhC,GACA,IAAAe,EAAAf,KAAA2B,WACA,WAA2B,OAAA3B,EAAA,SAC3B,WAAiC,OAAAA,GAEjC,OADAK,EAAAQ,EAAAE,EAAA,IAAAA,GACAA,GAIAV,EAAAW,EAAA,SAAAiB,EAAAC,GAAsD,OAAAjB,OAAAkB,UAAAC,eAAA1B,KAAAuB,EAAAC,IAGtD7B,EAAAgC,EAAA,aAIAhC,IAAAiC,EAAA,uBClFA,IAAAC,GACAC,OAAA,EACAC,OAAA,GACAC,KAAA,KACAC,IAAA,MACAC,KAAA,OACAC,MAAA,OACAC,QAAA,OACAC,KAAA,SAgFA,SAAAC,EAAAC,EAAAC,GACA,IAAAC,KAEA,GAAAF,EAAAG,OACA,OAAAH,EAAAG,OAAAF,GAEA,QAAA3C,EAAA,EAAiBA,EAAA0C,EAAAI,OAAgB9C,IACjC2C,EAAAD,EAAA1C,QACA4C,IAAAE,QAAAJ,EAAA1C,IAGA,OAAA4C,EAkHAnD,EAAAD,SACAuD,iBA5MA,SAAAC,GACA,OAAAhB,EAAAgB,IAAAhB,EAAAG,MA4MAc,QAzMA,SAAAP,EAAAC,GACA,GAAAD,EAAAO,QACAP,EAAAO,QAAAN,QAGA,IADA,IAAAO,EAAAR,EAAAS,QACAnD,EAAA,EAAiBA,EAAAkD,EAAAJ,OAAsB9C,IACvC2C,EAAAO,EAAAlD,OAoMAoD,SA/LA,SAAAV,EAAAC,GACA,GAAAD,EAAAW,IACA,OAAAX,EAAAW,IAAAV,GAKA,IAHA,IAAAO,EAAAR,EAAAS,QACAG,KAEAtD,EAAA,EAAiBA,EAAAkD,EAAAJ,OAAsB9C,IACvCsD,EAAAC,KAAAZ,EAAAO,EAAAlD,OAEA,OAAAsD,GAsLAE,UAjLA,SAAAd,EAAAC,GACA,GAAAD,EAAAe,KACA,OAAAf,EAAAe,KAAAd,GAEA,QAAA3C,EAAA,EAAiBA,EAAA0C,EAAAI,OAAgB9C,IACjC,GAAA2C,EAAAD,EAAA1C,MACA,OAAA0C,EAAA1C,IA4KAyC,cACAiB,gBA7FA,SAAAhB,EAAAC,GACA,OAAAF,EAAAC,EAAA,SAAAiB,EAAA3D,GACA,OAAA2C,EAAAgB,EAAA3D,MA4FA4D,UAzGA,SAAAlB,EAAAC,GACA,OAAAD,EAAAI,OAAA,SAEA,QAAA9C,EAAA,EAAgBA,EAAA0C,EAAAI,OAAgB9C,IAChC,GAAA2C,EAAAD,EAAA1C,KAAA0C,GACA,SAGA,UAkGAmB,YAtHA,SAAAC,GACA,IAAAlB,KAEA,QAAArB,KAAAuC,EACAA,EAAAjC,eAAAN,IACAqB,EAAAW,KAAAO,EAAAvC,IAIA,OAAAqB,GA8GAmB,gBAlDA,SAAArB,EAAAsB,EAAAC,GACA,IAAAC,EAAA,SAAAC,EAAAC,GACA,OAAAD,EAAAC,GAGA1B,EAAA2B,KAAA,SAAAF,EAAAC,GACA,OAAAD,EAAAH,KAAAI,EAAAJ,GAAA,EAEAC,EAAAC,EAAAC,EAAAH,GAAAI,EAAAJ,IAAAE,EAAAE,EAAAJ,GAAAG,EAAAH,OA2CAM,SA3FA,SAAA3B,EAAA4B,GACA,IAAAC,GAAA,EAEA,kBACAA,IACA7B,EAAA8B,MAAA,KAAAC,WACAF,GAAA,EACAG,WAAA,WACAH,GAAA,GACID,MAmFJK,QA3KA,SAAAC,GACA,OAAAC,MAAAF,QACAE,MAAAF,QAAAC,GAGAA,QAAAE,IAAAF,EAAA/B,QAAA+B,EAAAG,KAAAH,EAAAtB,MAuKA0B,OAjJA,SAAAJ,GACA,SAAAA,GAAA,iBAAAA,KACAA,EAAAK,aAAAL,EAAAM,UAAAN,EAAAO,WAgJAC,eAnKA,SAAAR,GACA,OAAAA,GAAA,iBAAAA,GACA,wCAAAS,SAAA1D,UAAA2D,SAAApF,KAAA0E,EAAAW,cAkKAC,eA9JA,SAAAZ,GACA,OAAAA,GAAA,iBAAAA,GACA,wCAAAS,SAAA1D,UAAA2D,SAAApF,KAAA0E,EAAAW,cA6JAE,gBAzJA,SAAAb,GACA,OAAAA,GAAA,iBAAAA,GACA,yCAAAS,SAAA1D,UAAA2D,SAAApF,KAAA0E,EAAAW,cAwJAG,MAnFA,SAAAhD,EAAA4B,GACA,IAAAqB,EAEAhD,EAAA,WACAA,EAAAiD,iBACAlD,EAAAmD,UAAA,EACA,IAAAC,EAAAjB,MAAAlD,UAAAuB,MAAAhD,KAAAuE,WACAkB,EAAAjB,WAAA,WACAhC,EAAA8B,MAAAuB,KAAAD,GACAnD,EAAAkD,UAAA,GACGvB,IAaH,OAVA3B,EAAAkD,UAAA,EACAlD,EAAAiD,eAAA,WACAI,aAAAL,GACAjD,EAAAmD,UAAA,GAEAlD,EAAAsD,SAAA,WACAvD,IACAA,EAAAkD,kBAGAjD,GA6DAuD,WA9CA,SAAAtB,GACA,GAAAnE,OAAA0F,KACA,OAAA1F,OAAA0F,KAAAvB,GAEA,IACAtD,EADAqB,KAEA,IAAArB,KAAAsD,EACAnE,OAAAkB,UAAAC,eAAA1B,KAAA0E,EAAAtD,IACAqB,EAAAW,KAAAhC,GAGA,OAAAqB,GAoCAyD,sBAjCA,SAAA1D,GACA,IAAA2D,EAAA1G,OAOA,OANA0G,EAAAD,uBACAC,EAAAC,6BACAD,EAAAE,yBACAF,EAAAG,0BACAH,EAAAI,wBACA,SAAAC,GAAmBhC,WAAAgC,EAAA,UACnBhE,IA0BAiE,YAvBA,SAAA/B,GACA,OAAAA,EAAAgC,aAAAhC,EAAAiC,iGCjNA,IAAAC,EAAAjH,EAAA,GAOAkH,EAAA,WAaC,SAAAA,IAAA,IAAAC,EAAAjB,KATQA,KAAAkB,YAAsB,EAqFtBlB,KAAAmB,0BAA4B,WAEnC,GAAGF,EAAKG,OAAOtE,QADI,GACkB,CAIpC,IAHA,IAAMuE,EAAOJ,EAAKG,OAAOjE,MAAM8D,EAAKG,OAAOtE,OAFzB,IAIZwE,KACEtH,EAAI,EAAGA,EAAIqH,EAAKvE,OAAQ9C,IAC/BsH,EAAQ/D,MACPgE,EAAGF,EAAKrH,GAAGuH,EAAIF,EAAKrH,EAAI,GAAGuH,EAC3BC,EAAGH,EAAKrH,GAAGwH,EAAIH,EAAKrH,EAAI,GAAGwH,IAG7B,IAAMC,GAAgBF,EAAE,EAAGC,EAAE,GAU7B,OARAF,EAAQrE,QAAQ,SAACyE,GAChBD,EAAaF,GAAKG,EAAOH,EACzBE,EAAaD,GAAKE,EAAOF,KAOzBG,UAJiBC,KAAKC,KAAKJ,EAAaF,EAAEE,EAAaF,EAAIE,EAAaD,EAAEC,EAAaD,GAKvFM,aAJqF,IAAjEF,KAAKG,MAAMH,KAAKI,IAAIP,EAAaD,GAAII,KAAKI,IAAIP,EAAaF,IAAYK,KAAKK,IAQlG,OAAO,MAGAjC,KAAAkC,qBAAuB,WAC9BjB,EAAKkB,UAAUC,MAAMC,UAAUC,IAAI,kCAG5BtC,KAAAuC,qBAAuB,WAC9BtB,EAAKkB,UAAUC,MAAMC,UAAUG,OAAO,kCAG/BxC,KAAAyC,mBAAqB,SAACC,GAC7B,OACCnB,EAAGoB,MAAMC,IAAIC,QAAQH,EAASI,QAAQC,SAASC,iBAAiBC,SAChEzB,EAAGmB,MAAMC,IAAIC,QAAQH,EAASI,QAAQI,SAASF,iBAAiBC,WAG1DjD,KAAAmD,wBAA0B,SAACC,GAClC,IAAM1B,EAAST,EAAKE,4BAChBkC,EAASpC,EAAKqC,YAAY/B,EAAI6B,EAAO7B,EACrCgC,EAAStC,EAAKqC,YAAY9B,EAAI4B,EAAO5B,EAazC,OAZGE,IACCA,EAAOI,aAAe,GACxByB,EAAS,EACA7B,EAAOI,aAAe,KAC/BuB,EAAS,KAKV9B,EAAGN,EAAKuC,aAAajC,EAAI8B,EACzB7B,EAAGP,EAAKuC,aAAahC,EAAI+B,IAInBvD,KAAAyD,mBAAqB,SAACf,EAAeU,GAC5CrC,EAAAV,sBAAsB,WACrBsC,MAAMC,IAAIC,QAAQH,EAASI,QAAQC,SAASW,OAAON,EAAO7B,GAC1DoB,MAAMC,IAAIC,QAAQH,EAASI,QAAQI,SAASQ,OAAON,EAAO5B,MAGpDxB,KAAA2D,UAAY,SAACC,GACpB3C,EAAKG,UACLuB,MAAMkB,MAAMxB,UAAUG,OAAO,uBAECzD,IAA3BkC,EAAK6C,oBACPnB,MAAMoB,OAAOC,SAAW/C,EAAK6C,wBAGA/E,IAA3BkC,EAAKgD,oBACPtB,MAAMoB,OAAOG,WAAajD,EAAKgD,mBAGxB,IAAAE,EAAAxB,MAAAoB,OAAAK,cAAAD,OACJA,IAA4B,IAAlBP,EAAMO,IAChBlD,EAAKC,aACRD,EAAKC,YAAa,IAIZlB,KAAAqE,WAAa,SAACT,GACrB3C,EAAKgD,kBAAoBtB,MAAMoB,OAAOG,WACtCvB,MAAMoB,OAAOG,YAAa,EAE1BvB,MAAMkB,MAAMxB,UAAUC,IAAI,kBAC1BrB,EAAK6C,kBAAoBnB,MAAMoB,OAAOC,SACtCrB,MAAMoB,OAAOC,UAAW,EAExB/C,EAAKG,UACLH,EAAKC,YAAa,EACZ,IAAAoD,EAAArD,EAAAwB,mBAAAxB,EAAAkB,WAAEZ,EAAA+C,EAAA/C,EAAGC,EAAA8C,EAAA9C,EACXP,EAAKuC,cAAiBjC,EAACA,EAAEC,EAACA,GAC1BP,EAAKqC,aAAgB/B,EAAGqC,EAAMW,QAAS/C,EAAGoC,EAAMY,SAChDvD,EAAKG,OAAO7D,KAAK0D,EAAKqC,cAhLtBtD,KAAKyE,WAAa9B,MAAM+B,uBACxB1E,KAAKoB,UAiLP,OA/LQJ,EAAA1F,OAAP,WACC,OAAO,IAAI0F,GAgBZA,EAAApF,UAAA+I,WAAA,WACC3E,KAAKyE,WAAWG,aAGjB5D,EAAApF,UAAAiJ,OAAA,SAAOnC,GAAP,IAAAzB,EAAAjB,KACCA,KAAKmC,UAAYO,EAEjB1C,KAAKyE,WAAWI,OAAOnC,EAASN,MAAO,YAAa,SAACwB,GAC9C,IAAAU,EAAA3B,MAAAoB,OAAAK,cAAED,EAAAG,EAAAH,OAAOW,EAAAR,EAAAQ,OACf,IAAe,IADQR,EAAAS,QACvB,CAIA,IAAIC,EAAgB,0CACNjG,IAAX+F,IAEDE,EADEF,aAAkBhG,MACJgG,EAAOG,KAAK,MAEZH,GAGdE,GACCrC,MAAMuC,MAAMC,IAAIC,QAAQxB,EAAMyB,OAAQL,IAIvCb,IAA4B,IAAlBP,EAAMO,IAEpBlD,EAAKoD,WAAWT,MAGjB5D,KAAKyE,WAAWI,OAAOS,SAAU,UAAW,SAAC1B,GACpC,IAAAO,EAAAxB,MAAAoB,OAAAK,cAAAD,OACJA,IAA4B,IAAlBP,EAAMO,IACnBlD,EAAKiB,yBAGPlC,KAAKyE,WAAWI,OAAOS,SAAU,QAAS,SAAC1B,GAClC,IAAAO,EAAAxB,MAAAoB,OAAAK,cAAAD,OACJA,IAA4B,IAAlBP,EAAMO,KACnBlD,EAAKsB,uBACLtB,EAAK0C,UAAUC,MAIjB5D,KAAKyE,WAAWI,OAAOS,SAAU,UAAW,SAAC1B,GAC5C3C,EAAK0C,UAAUC,KAEhB5D,KAAKyE,WAAWI,OAAOlC,MAAMkB,MAAO,UAAW,SAACD,GAC/C3C,EAAK0C,UAAUC,KAEhB5D,KAAKyE,WAAWI,OAAOS,SAAU,aAAc,SAAC1B,GAC/C3C,EAAK0C,UAAUC,KAEhB5D,KAAKyE,WAAWI,OAAOlC,MAAMkB,MAAO,aAAc,SAACD,GAClD3C,EAAK0C,UAAUC,KAGhB5D,KAAKyE,WAAWI,OAAOlC,MAAMkB,MAAO,YAAa,SAACD,GACzC,IAAAO,EAAAxB,MAAAoB,OAAAK,cAAAD,OACR,KAAIA,IAA4B,IAAlBP,EAAMO,MACI,IAApBlD,EAAKC,WAAqB,CAC7BD,EAAKG,OAAO7D,MAAOgE,EAAGqC,EAAMW,QAAS/C,EAAGoC,EAAMY,UAC9C,IAAMe,EAAyBtE,EAAKkC,yBAA0B5B,EAAGqC,EAAMW,QAAS/C,EAAGoC,EAAMY,UACzFvD,EAAKwC,mBAAmBf,EAAU6C,GAClCtE,EAAKuC,aAAe+B,EACpBtE,EAAKqC,aAAgB/B,EAAGqC,EAAMW,QAAS/C,EAAGoC,EAAMY,aA4GpDxD,EAhMA,GAAaxH,EAAAwH,mGCRb,IAAAwE,EAAA1L,EAAA,KAEK6I,MAAM8C,MACV9C,MAAM8C,QAGP9C,MAAM8C,IAAIC,cACTpK,OAAQ,WAAM,OAAAkK,EAAAxE,cAAc1F,WAG7BqH,MAAMoB,OAAOK,eACZW,SAAS", "file": "ext/dhtmlxgantt_drag_timeline.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"ext/dhtmlxgantt_drag_timeline\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ext/dhtmlxgantt_drag_timeline\"] = factory();\n\telse\n\t\troot[\"ext/dhtmlxgantt_drag_timeline\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/codebase/\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 214);\n", "var units = {\n\t\"second\": 1,\n\t\"minute\": 60,\n\t\"hour\": 60 * 60,\n\t\"day\": 60 * 60 * 24,\n\t\"week\": 60 * 60 * 24 * 7,\n\t\"month\": 60 * 60 * 24 * 30,\n\t\"quarter\": 60 * 60 * 24 * 30 * 3,\n\t\"year\": 60 * 60 * 24 * 365\n};\nfunction getSecondsInUnit(unit){\n\treturn units[unit] || units.hour;\n}\n\nfunction forEach(arr, callback) {\n\tif (arr.forEach) {\n\t\tarr.forEach(callback);\n\t} else {\n\t\tvar workArray = arr.slice();\n\t\tfor (var i = 0; i < workArray.length; i++) {\n\t\t\tcallback(workArray[i], i);\n\t\t}\n\t}\n}\n\nfunction arrayMap(arr, callback) {\n\tif (arr.map) {\n\t\treturn arr.map(callback);\n\t} else {\n\t\tvar workArray = arr.slice();\n\t\tvar resArray = [];\n\n\t\tfor (var i = 0; i < workArray.length; i++) {\n\t\t\tresArray.push(callback(workArray[i], i));\n\t\t}\n\t\treturn resArray;\n\t}\n}\n\n\nfunction arrayFind(arr, callback) {\n\tif (arr.find) {\n\t\treturn arr.find(callback);\n\t} else {\n\t\tfor (var i = 0; i < arr.length; i++) {\n\t\t\tif (callback(arr[i], i)) {\n\t\t\t\treturn arr[i];\n\t\t\t}\n\t\t}\n\t}\n}\n\n// iframe-safe array type check instead of using instanceof\nfunction isArray(obj){\n\tif(Array.isArray){\n\t\treturn Array.isArray(obj);\n\t}else{\n\t\t// close enough\n\t\treturn (obj && obj.length !== undefined && obj.pop && obj.push);\n\t}\n}\n\n// non-primitive string object, e.g. new String(\"abc\")\nfunction isStringObject(obj){\n\treturn obj && typeof obj === \"object\"\n\t\t&& Function.prototype.toString.call(obj.constructor) === \"function String() { [native code] }\";\n}\n\n// non-primitive number object, e.g. new Number(5)\nfunction isNumberObject(obj){\n\treturn obj && typeof obj === \"object\"\n\t\t&& Function.prototype.toString.call(obj.constructor) === \"function Number() { [native code] }\";\n}\n\n// non-primitive number object, e.g. new Boolean(true)\nfunction isBooleanObject(obj){\n\treturn obj && typeof obj === \"object\"\n\t\t&& Function.prototype.toString.call(obj.constructor) === \"function Boolean() { [native code] }\";\n}\n\nfunction isDate(obj) {\n\tif (obj && typeof obj === \"object\") {\n\t\treturn !!(obj.getFullYear && obj.getMonth && obj.getDate);\n\t} else {\n\t\treturn false;\n\t}\n}\n\nfunction arrayFilter(arr, callback) {\n\tvar result = [];\n\n\tif (arr.filter) {\n\t\treturn arr.filter(callback);\n\t} else {\n\t\tfor (var i = 0; i < arr.length; i++) {\n\t\t\tif (callback(arr[i], i)) {\n\t\t\t\tresult[result.length] = arr[i];\n\t\t\t}\n\t\t}\n\t\treturn result;\n\t}\n}\n\nfunction hashToArray(hash) {\n\tvar result = [];\n\n\tfor (var key in hash) {\n\t\tif (hash.hasOwnProperty(key)) {\n\t\t\tresult.push(hash[key]);\n\t\t}\n\t}\n\n\treturn result;\n}\n\nfunction arraySome(arr, callback) {\n\tif (arr.length === 0) return false;\n\n\tfor (var i = 0; i < arr.length; i++) {\n\t\tif (callback(arr[i], i, arr)) {\n\t\t\treturn true;\n\t\t}\n\t}\n\treturn false;\n}\n\nfunction arrayDifference(arr, callback) {\n\treturn arrayFilter(arr, function(item, i) {\n\t\treturn !callback(item, i);\n\t});\n}\n\nfunction throttle (callback, timeout) {\n\tvar wait = false;\n\n\treturn function () {\n\t\tif (!wait) {\n\t\t\tcallback.apply(null, arguments);\n\t\t\twait = true;\n\t\t\tsetTimeout(function () {\n\t\t\t\twait = false;\n\t\t\t}, timeout);\n\t\t}\n\t};\n}\n\nfunction delay (callback, timeout){\n\tvar timer;\n\n\tvar result = function() {\n\t\tresult.$cancelTimeout();\n\t\tcallback.$pending = true;\n\t\tvar args = Array.prototype.slice.call(arguments);\n\t\ttimer = setTimeout(function(){\n\t\t\tcallback.apply(this, args);\n\t\t\tresult.$pending = false;\n\t\t}, timeout);\n\t};\n\t\n\tresult.$pending = false;\n\tresult.$cancelTimeout = function(){\n\t\tclearTimeout(timer);\n\t\tcallback.$pending = false;\n\t};\n\tresult.$execute = function(){\n\t\tcallback();\n\t\tcallback.$cancelTimeout();\n\t};\n\n\treturn result;\n}\n\nfunction sortArrayOfHash(arr, field, desc) {\n\tvar compare = function(a, b) {\n\t\treturn a < b;\n\t};\n\n\tarr.sort(function(a, b) {\n\t\tif (a[field] === b[field]) return 0;\n\n\t\treturn desc ? compare(a[field], b[field]) : compare(b[field], a[field]);\n\t});\n}\n\nfunction objectKeys(obj) {\n\tif (Object.keys) {\n\t\treturn Object.keys(obj);\n\t}\n\tvar result = [];\n\tvar key;\n\tfor (key in obj) {\n\t\tif (Object.prototype.hasOwnProperty.call(obj, key)) {\n\t\t\tresult.push(key);\n\t\t}\n\t}\n\treturn result;\n}\n\nfunction requestAnimationFrame(callback) {\n\tvar w = window;\n\tvar foundRequestAnimationFrame = w.requestAnimationFrame\n\t\t|| w.webkitRequestAnimationFrame\n\t\t|| w.msRequestAnimationFrame\n\t\t|| w.mozRequestAnimationFrame\n\t\t|| w.oRequestAnimationFrame\n\t\t|| function(cb) { setTimeout(cb, 1000/60); };\n\treturn foundRequestAnimationFrame(callback);\n}\n\nfunction isEventable(obj) {\n\treturn obj.attachEvent && obj.detachEvent;\n}\n\nmodule.exports = {\n\tgetSecondsInUnit: getSecondsInUnit,\n\tforEach: forEach,\n\tarrayMap: arrayMap,\n\tarrayFind: arrayFind,\n\tarrayFilter: arrayFilter,\n\tarrayDifference: arrayDifference,\n\tarraySome: arraySome,\n\thashToArray: hashToArray,\n\tsortArrayOfHash: sortArrayOfHash,\n\tthrottle: throttle,\n\tisArray: isArray,\n\tisDate: isDate,\n\tisStringObject: isStringObject,\n\tisNumberObject: isNumberObject,\n\tisBooleanObject: isBooleanObject,\n\tdelay: delay,\n\tobjectKeys: objectKeys,\n\trequestAnimationFrame: requestAnimationFrame,\n\tisEventable: isEventable\n};", "declare const gantt;\nimport { requestAnimationFrame } from \"../../utils/helpers\";\n\ninterface IPoint {\n\tx: number;\n\ty: number;\n}\n\nexport class EventsManager {\n\tstatic create() {\n\t\treturn new EventsManager();\n\t}\n\tprivate _mouseDown: boolean = false;\n\tprivate _startPoint: IPoint;\n\tprivate _scrollState: IPoint;\n\tprivate _originAutoscroll: boolean;\n\tprivate _domEvents: any;\n\tprivate _timeline: any;\n\tprivate _trace: IPoint[];\n\tprivate _originalReadonly: boolean;\n\n\tconstructor() {\n\t\tthis._domEvents = gantt._createDomEventScope();\n\t\tthis._trace = [];\n\t}\n\n\tdestructor() {\n\t\tthis._domEvents.detachAll();\n\t}\n\n\tattach(timeline: any): void {\n\t\tthis._timeline = timeline;\n\n\t\tthis._domEvents.attach(timeline.$task, \"mousedown\", (event) => {\n\t\t\tconst { useKey,ignore, enabled } = gantt.config.drag_timeline;\n\t\t\tif(enabled === false) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tlet filterTargets = \".gantt_task_line, .gantt_task_link\";\n\t\t\tif(ignore !== undefined) {\n\t\t\t\tif(ignore instanceof Array){\n\t\t\t\t\tfilterTargets = ignore.join(\", \");\n\t\t\t\t} else {\n\t\t\t\t\tfilterTargets = ignore;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (filterTargets) {\n\t\t\t\tif (gantt.utils.dom.closest(event.target, filterTargets)) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (useKey && event[useKey] !== true) { return; }\n\n\t\t\tthis._startDrag(event);\n\t\t});\n\n\t\tthis._domEvents.attach(document, \"keydown\", (event) => {\n\t\t\tconst { useKey } = gantt.config.drag_timeline;\n\t\t\tif (useKey && event[useKey] === true) {\n\t\t\t\tthis._applyDndReadyStyles();\n\t\t\t}\n\t\t});\n\t\tthis._domEvents.attach(document, \"keyup\", (event) => {\n\t\t\tconst { useKey } = gantt.config.drag_timeline;\n\t\t\tif (useKey && event[useKey] === false) {\n\t\t\t\tthis._clearDndReadyStyles();\n\t\t\t\tthis._stopDrag(event);\n\t\t\t}\n\t\t});\n\n\t\tthis._domEvents.attach(document, \"mouseup\", (event) => {\n\t\t\tthis._stopDrag(event);\n\t\t});\n\t\tthis._domEvents.attach(gantt.$root, \"mouseup\", (event) => {\n\t\t\tthis._stopDrag(event);\n\t\t});\n\t\tthis._domEvents.attach(document, \"mouseleave\", (event) => {\n\t\t\tthis._stopDrag(event);\n\t\t});\n\t\tthis._domEvents.attach(gantt.$root, \"mouseleave\", (event) => {\n\t\t\tthis._stopDrag(event);\n\t\t});\n\n\t\tthis._domEvents.attach(gantt.$root, \"mousemove\", (event) => {\n\t\t\tconst { useKey } = gantt.config.drag_timeline;\n\t\t\tif (useKey && event[useKey] !== true) { return; }\n\t\t\tif (this._mouseDown === true) {\n\t\t\t\tthis._trace.push({ x: event.clientX, y: event.clientY });\n\t\t\t\tconst scrollPosition: IPoint = this._countNewScrollPosition({ x: event.clientX, y: event.clientY });\n\t\t\t\tthis._setScrollPosition(timeline, scrollPosition);\n\t\t\t\tthis._scrollState = scrollPosition;\n\t\t\t\tthis._startPoint = { x: event.clientX, y: event.clientY };\n\t\t\t}\n\t\t});\n\t}\n\n\tprivate _calculateDirectionVector = () => {\n\t\tconst traceSteps = 10;\n\t\tif(this._trace.length >= traceSteps) {\n\t\t\tconst dots = this._trace.slice(this._trace.length - traceSteps);\n\n\t\t\tconst vectors = [];\n\t\t\tfor(let i = 1; i < dots.length; i++) {\n\t\t\t\tvectors.push({\n\t\t\t\t\tx: dots[i].x - dots[i - 1].x,\n\t\t\t\t\ty: dots[i].y - dots[i - 1].y\n\t\t\t\t});\n\t\t\t}\n\t\t\tconst resultVector = {x:0, y:0};\n\n\t\t\tvectors.forEach((vector) => {\n\t\t\t\tresultVector.x += vector.x;\n\t\t\t\tresultVector.y += vector.y;\n\t\t\t});\n\n\t\t\tconst magnitude = Math.sqrt(resultVector.x*resultVector.x + resultVector.y*resultVector.y);\n\t\t\tconst angleDegrees = Math.atan2(Math.abs(resultVector.y), Math.abs(resultVector.x)) * 180 / Math.PI;\n\n\t\t\treturn {\n\t\t\t\tmagnitude,\n\t\t\t\tangleDegrees\n\t\t\t};\n\n\t\t}\n\t\treturn null;\n\t}\n\n\tprivate _applyDndReadyStyles = (): void => {\n\t\tthis._timeline.$task.classList.add(\"gantt_timeline_move_available\");\n\t}\n\n\tprivate _clearDndReadyStyles = (): void => {\n\t\tthis._timeline.$task.classList.remove(\"gantt_timeline_move_available\");\n\t}\n\n\tprivate _getScrollPosition = (timeline: any): IPoint => {\n\t\treturn {\n\t\t\tx: gantt.$ui.getView(timeline.$config.scrollX).getScrollState().position,\n\t\t\ty: gantt.$ui.getView(timeline.$config.scrollY).getScrollState().position\n\t\t};\n\t}\n\tprivate _countNewScrollPosition = (coords: IPoint): IPoint => {\n\t\tconst vector = this._calculateDirectionVector();\n\t\tlet shiftX = this._startPoint.x - coords.x;\n\t\tlet shiftY = this._startPoint.y - coords.y;\n\t\tif(vector){\n\t\t\tif(vector.angleDegrees < 15){\n\t\t\t\tshiftY = 0;\n\t\t\t} else if(vector.angleDegrees > 75){\n\t\t\t\tshiftX = 0;\n\t\t\t}\n\t\t}\n\n\t\tconst result = {\n\t\t\tx: this._scrollState.x + shiftX,\n\t\t\ty: this._scrollState.y + shiftY\n\t\t};\n\t\treturn result;\n\t}\n\tprivate _setScrollPosition = (timeline: any, coords: IPoint): void => {\n\t\trequestAnimationFrame(() => {\n\t\t\tgantt.$ui.getView(timeline.$config.scrollX).scroll(coords.x);\n\t\t\tgantt.$ui.getView(timeline.$config.scrollY).scroll(coords.y);\n\t\t});\n\t}\n\tprivate _stopDrag = (event: Event): void => {\n\t\tthis._trace = [];\n\t\tgantt.$root.classList.remove(\"gantt_noselect\");\n\n\t\tif(this._originalReadonly !== undefined){\n\t\t\tgantt.config.readonly = this._originalReadonly;\n\t\t}\n\n\t\tif(this._originAutoscroll !== undefined){\n\t\t\tgantt.config.autoscroll = this._originAutoscroll;\n\t\t}\n\n\t\tconst { useKey } = gantt.config.drag_timeline;\n\t\tif (useKey && event[useKey] !== true) { return; }\n\t\tif (this._mouseDown) {\n\t\t\tthis._mouseDown = false;\n\t\t}\n\t}\n\n\tprivate _startDrag = (event: any) : void => {\n\t\tthis._originAutoscroll = gantt.config.autoscroll;\n\t\tgantt.config.autoscroll = false;\n\n\t\tgantt.$root.classList.add(\"gantt_noselect\");\n\t\tthis._originalReadonly = gantt.config.readonly;\n\t\tgantt.config.readonly = true;\n\n\t\tthis._trace = [];\n\t\tthis._mouseDown = true;\n\t\tconst { x, y } = this._getScrollPosition(this._timeline);\n\t\tthis._scrollState = { x, y };\n\t\tthis._startPoint = { x: event.clientX, y: event.clientY };\n\t\tthis._trace.push(this._startPoint);\n\t}\n}", "import { EventsManager } from \"./eventsManager\";\ndeclare const gantt;\nif (!gantt.ext) {\n\tgantt.ext = {};\n}\n\ngantt.ext.dragTimeline = {\n\tcreate: () => EventsManager.create()\n};\n\ngantt.config.drag_timeline = {\n\tenabled: true\n};"], "sourceRoot": ""}