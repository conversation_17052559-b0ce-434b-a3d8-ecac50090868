<template>
  <a-modal title="填写实际进箱时间" :width="800"
           :bodyStyle="{padding:0}"
           :visible="visible" :confirmLoading="confirmLoading"  style="padding: 0"
           :maskClosable="false"
           @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-tabs v-model="activeKey" @change="changeTab">
        <a-tab-pane key="basic" tab="基础信息">
          <a-form :form="form">
            <a-form-item style="display: none;" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['id']" />
            </a-form-item>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="测试状态" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>

                  <a-select  v-decorator="['testStatus', {rules: [{ message: '请选择测试状态！'}]}]" disabled
                             style="width: 100%">

                    <a-select-option value="Done">
                      Done
                    </a-select-option>
                    <a-select-option value="Ongoing">
                      Ongoing
                    </a-select-option>
                    <a-select-option value="Plan">
                      Plan
                    </a-select-option>
                    <a-select-option value="Stop">
                      Stop
                    </a-select-option>

                  </a-select>

                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="测试申请单" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input placeholder="请输入测试申请单" disabled
                           v-decorator="['testCode', {rules: [{ message: '请输入测试申请单！'}]}]"/>
                </a-form-item>
              </a-col>

            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="产品名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入产品名称" disabled
                           v-decorator="['productName', {rules: [{ message: '请输入产品名称！'}]}]"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="产品样品阶段" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入产品样品阶段" disabled
                           v-decorator="['productSampleStage', {rules: [{ message: '请输入产品样品阶段！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="测试类型" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-select v-decorator="['testType', {rules: [{ message: '请输入测试类型！'}]}]"  disabled
                            style="width: 100%">

                    <a-select-option value="研发测试">
                      研发测试
                    </a-select-option>
                    <a-select-option value="产品验证测试">
                      产品验证测试
                    </a-select-option>
                    <a-select-option value="产品鉴定测试">
                      产品鉴定测试
                    </a-select-option>

                  </a-select>

                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="申请部门" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入申请部门" disabled
                           v-decorator="['dept', {rules: [{ message: '请输入申请部门！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="申请人" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入申请人" disabled
                           v-decorator="['applicant', {rules: [{ message: '请输入申请人！'}]}]"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="测试项目" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入测试项目" disabled
                           v-decorator="['testProject', {rules: [{ message: '请输入测试项目！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="T/℃" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入T/℃" disabled
                           v-decorator="['t', {rules: [{ message: '请输入T/℃！'}]}]"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="SOC" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入soc" disabled
                           v-decorator="['soc', {rules: [{ message: '请输入soc！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="测试周期" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入存储天数" :min="1"  :precision="0" disabled
                                  v-decorator="['testPeriod', {rules: [{ message: '请输入存储天数！'}]}]"/>

                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="数量" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入数量" :min="1"  :precision="0" disabled
                           v-decorator="['quantity', {rules: [{ message: '请输入数量！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>


            <a-row :gutter="24">
              <a-col :md="12" :sm="24">

                <a-form-item label="测试技师" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入测试技师" disabled
                           v-decorator="['testMan', {rules: [{ message: '请输入测试技师！'}]}]"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="测试地点" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-select v-decorator="['testAddress', {rules: [{ message: '请输入测试地点！'}]}]" disabled
                            style="width: 100%">

                    <a-select-option value="A1_3F">
                      V圆柱检测室
                    </a-select-option>

                    <a-select-option value="R2_2F">
                      材料验证检测室
                    </a-select-option>

                    <a-select-option value="R4_4F">
                      动力电池检测室
                    </a-select-option>

                  </a-select>

                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">


                <a-form-item label="电芯载体" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">

                  <a-select v-decorator="['sampleType', {rules: [{ message: '请输入电芯载体！'}]}]" disabled
                            style="width: 100%">

                    <a-select-option value="G圆柱">
                      G圆柱
                    </a-select-option>
                    <a-select-option value="C圆柱">
                      C圆柱
                    </a-select-option>
                    <a-select-option value="V圆柱">
                      V圆柱
                    </a-select-option>
                    <a-select-option value="方形">
                      方形
                    </a-select-option>
                    <a-select-option value="软包_396389">
                      软包_396389
                    </a-select-option>
                    <a-select-option value="软包_动力">
                      软包_动力
                    </a-select-option>
                    <a-select-option value="模组">
                      模组
                    </a-select-option>

                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">


                <a-form-item label="存储位置" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">

                  <a-input disabled
                           v-decorator="['saveAddress']"/>

                </a-form-item>
              </a-col>

            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">


                <a-form-item label="测试目的" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">

                  <a-input disabled
                           v-decorator="['testPurpose']"/>

                </a-form-item>
              </a-col>

            </a-row>


            <!-- <a-row :gutter="24">

               <a-col :md="12" :sm="24">
                 <a-form-item label="已完成天数" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                   <a-input placeholder="请输入已完成天数"
                            v-decorator="['finishDay', {rules: [{ message: '请输入已完成天数！'}]}]"/>
                 </a-form-item>
               </a-col>
             </a-row>-->
          </a-form>
        </a-tab-pane>
        <a-tab-pane key="test" tab="中检信息">
          <a-form :form="form">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">

                <a-form-item label="中检次数" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input  placeholder="请输入中检次数" @change="changeNum"  :min="1"  :precision="0" disabled
                            v-decorator="['testNum']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">

                <a-form-item label="进箱开始时间" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback disabled>
                  <span style="color: black">{{record.zero!=null?moment(record.zero).format("YYYY-MM-DD"):null}}</span>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
          <a-table :columns="columns" :data-source="record.data" bordered
                   style="padding: 20px"
                   bordered :scroll="{y:205}"
                   :pagination="false"
                   :rowKey="(record) => record.id"
          >
            <template  slot="actualInDate" slot-scope="text, inRecord, index">
              <a-date-picker v-if="hasPerm('progress:updateInDate') || (inRecord.actualInDateStatus == 0 && (index == 0 || record.data[index - 1].actualInDate != null))" :allow-clear="false" :class="inRecord.actualInDateStatus == 1?'green':null"
                             placeholder=""
                             style="width: 100%;"
                             :value="null != text ?moment(text, 'YYYY-MM-DD'):null"
                             :disabledDate="(date) => disabledActStartDate(date,index)"
                             @change="changeActuInDate($event,index,inRecord)">
              </a-date-picker >
              <div v-else-if="inRecord.actualInDateStatus == 1" class="green">{{text}}</div>
              <div v-else-if="inRecord.actualInDateStatus == 0 && index != 0 && (record.data[index - 1].actualInDate == null || record.data[index - 1].actualInDate == 0)" style="text-align: center;height: 25px">{{text}}</div>


            </template>

            <template  slot="saveAddress" slot-scope="text, inRecord, index">
             <a-input :value="text" @change="changeSaveAddress($event,index,inRecord)" style="text-align: center">

             </a-input>

            </template>
           <!-- <template  slot="actualInDate" slot-scope="text, record, index">
              <div v-if="record.actualInDateStatus == 1" class="green">{{text}}</div>
              <a-date-picker v-else
                             placeholder=""
                             style="width: 100%;height: 25px"
                             :value="null != text ?moment(text, 'YYYY-MM-DD'):null"
                             @change="changeActuInDate($event,index,record)">
              </a-date-picker >
            </template>-->


           <!-- <template slot="day" slot-scope="text, record, index">
              <a-input disabled="true" :value="text" @change="changeDay($event,index,record)"   :min="1"  :precision="0" size="small"
                              style="border: 0"
                       ></a-input>
            </template>-->
<!--
            <template slot="footer">
              <a @click="addData">
                <a-icon type="plus" style="width: 15px;height: 15px;margin-left: 50px;cursor: pointer"/>
              </a>
            </template>-->

          </a-table>

        </a-tab-pane>
        <a-tab-pane key="remark" tab="备注">
          <a-textarea style="margin: 10px;border: 1px solid #d9d9d9;width: 98%"
                      v-model="record.remark"
                      :auto-size="{ minRows: 6, maxRows: 20 }"
          />

        </a-tab-pane>
      </a-tabs>


    </a-spin>

    <template slot="footer">

      <a-button key="back" @click="handleCancel">
        取消
      </a-button>
      <a-button key="primary" v-if="activeKey == 'remark'" type="primary" @click="handleOnlyBeanSubmit">
        保存备注
      </a-button>
      <a-popconfirm placement="topRight" ok-text="提交" cancel-text="取消" @confirm="handleSubmit">
        <template slot="title">
          <p>确定提交吗</p>
        </template>
        <a-button key="submit" type="primary" v-if="activeKey != 'remark'">
          确定
        </a-button>
      </a-popconfirm>

    </template>

  </a-modal>
</template>

<script>
  import {
    testProgressUpdate,testProgressUpdateOnlyBean
  } from '@/api/modular/system/testProgressManager'
  import moment from "moment";

  export default {
    props: {
      type: {
        type: String,
        default: ''
      },
    },
    data() {
      return {
        columns: [
          {
            title: '存储阶段',
            dataIndex: 'index',
            align: 'center',
            width: 60,
            customRender: (text, record, index) => {

              if(this.record.data[0].day == 0){
                if(index == 0){
                  return "初始阶段"
                }else{
                  return `存储第${index}阶段`
                }
              }else{
                return `存储第${index+1}阶段`
              }

            }
          }, {
            title: '存储天数',
            width: 60,
            align: 'center',
            dataIndex: 'day',
            scopedSlots: {
              customRender: 'day'
            },
          }, {
            title: '计划开始时间',
            width: 60,
            align: 'center',
            dataIndex: 'inDate'
          },{
            title: '实际进箱时间',
            width: 70,
            align: 'center',
            dataIndex: 'actualInDate',
            scopedSlots: {
              customRender: 'actualInDate'
            },

          }, {
            title: '结束时间',
            width: 60,
            align: 'center',
            dataIndex: 'outDate'
          },{
            title: '电芯异动说明',
            width: 70,
            align: 'center',
            dataIndex: 'saveAddress',
            scopedSlots: {
              customRender: 'saveAddress'
            },

          }
        ],
        activeKey: 'test',
        startDate: null,
        labelCol: {
          xs: {
            span: 12
          },
          sm: {
            span: 8
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 14
          }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this),
        record:{}
      }
    },

    methods: {
      moment,
      disabledActStartDate(startValue,index) {

        if(index == 0){
          return false
        }else{
          if(this.record.data[index - 1].actualInDate != null){
            return startValue < moment(this.record.data[index - 1].actualInDate)
          }
        }

        return false
      },
      changeTab(key){
        this.$nextTick(() => {
          if(key == 'basic'){

            if(this.form.getFieldValue('id') == null){
              this.form.setFieldsValue({
                id:this.record.id,
                testStatus:this.record.testStatus,
                testCode:this.record.testCode,
                productName:this.record.productName,
                productSampleStage:this.record.productSampleStage,
                testType:this.record.testType,
                dept:this.record.dept,
                applicant:this.record.applicant,
                testProject:this.record.testProject,
                t:this.record.t,
                soc:this.record.soc,
                testPeriod:this.record.testPeriod,
                quantity:this.record.quantity,
                testMan:this.record.testMan,
                testAddress:this.record.testAddress,
                sampleType:this.record.sampleType,
                saveAddress:this.record.saveAddress,
                testPurpose:this.record.testPurpose
              })
            }

          }else{
            if(this.form.getFieldValue('zero') == null
              || this.form.getFieldValue('zero') == undefined){
              this.form.setFieldsValue({
                testNum : this.record.data.length
              })
            }
          }

        })


      },
      changeZero(event) {

        //this.zero = moment(event).format('YYYY-MM-DD')
        if (this.record.data.length > 0) {
          this.record.data[0].inDate = moment(event).format('YYYY-MM-DD')
          if (this.record.data[0].day != null) {
            this.record.data[0].outDate = moment(event).add(this.record.data[0].day, 'days').format('YYYY-MM-DD')
          }
        }
        this.changeInOutDate(1);

      },

      changeInOutDate(index) {

        if (index == 0) {
          if(this.form.getFieldValue('zero') != null){
            this.record.data[0].inDate = moment(this.form.getFieldValue('zero')).format('YYYY-MM-DD')

            if (this.record.data[0].day != null && this.record.data[0].inDate != null) {
              this.record.data[0].outDate = moment(this.record.data[0].inDate).add(this.record.data[0].day, 'days').format('YYYY-MM-DD')
            }
          }


          index += 1;
        }
        for (let i = index; i < this.record.data.length; i++) {
          let before = this.record.data[i - 1]
          if (before.outDate != null) {
            this.record.data[i].inDate = moment(before.outDate).add(3, 'days').format('YYYY-MM-DD')
            if (this.record.data[i].inDate != null && this.record.data[i].day != null) {
              this.record.data[i].outDate = moment(this.record.data[i].inDate).add(this.record.data[i].day, 'days').format('YYYY-MM-DD')
            }
          }
        }


      },

      changeNum($event) {
        if (this.record.data.length > $event) {
          this.record.data = this.record.data.slice(0, $event)
        } else {
          for (let i = this.record.data.length; i < $event; i++) {
            this.record.data.push({uuid: i * 10000 + 1000, day: null, inDate: null, outDate: null})
          }
        }

        this.changeInOutDate(0);


      },
      addData() {
        this.record.data.push({uuid: this.record.data.length * 10000 + 1000, day: null, inDate: null, outDate: null})
        this.form.setFieldsValue(
          {
            testNum: this.record.data.length
          }
        )

      },

      changeActuInDate($event,index,record){


        if(index == 0){
          this.record.zero = moment($event).format('YYYY-MM-DD')
        }

        record.actualInDate = moment($event).format('YYYY-MM-DD')
        record.outDate = moment(record.actualInDate).add(record.day,'days').format('YYYY-MM-DD')

        this.changeInOutDate(index+1)

      },
      changeSaveAddress($event,index,record){

        record.saveAddress = $event.target.value
      },
      changeDay($event, index, record) {


         /* if(this.record.data.length > index+2 && this.record.data[index+1].day != null){

            if(this.record.data[index+1].day <= $event){
              this.$message.warn('天数应小于下次中检天数')
              return
            }
          }

        if(index > 0){
          if(this.record.data[index - 1] != null){

            if(this.record.data[index - 1].day >= $event){
              this.$message.warn('天数应大于上次中检天数')
              return
            }
          }
        }*/
        record.day = $event
        this.changeInOutDate(index);
      },
      add() {
        this.visible = true
      },
      edit(record) {
        this.record = record
        this.visible = true
        setTimeout(() => {

          /*if(this.record.data.length > 0){
            if(this.record.data[0].day == 0){
              this.record.data = this.record.data.slice(1);
            }
          }*/

          if(this.activeKey == 'basic'){
            this.form.setFieldsValue({
              id:this.record.id,
              testStatus:this.record.testStatus,
              testCode:this.record.testCode,
              productName:this.record.productName,
              productSampleStage:this.record.productSampleStage,
              testType:this.record.testType,
              dept:this.record.dept,
              applicant:this.record.applicant,
              testProject:this.record.testProject,
              t:this.record.t,
              soc:this.record.soc,
              testPeriod:this.record.testPeriod,
              quantity:this.record.quantity,
              testMan:this.record.testMan,
              testAddress:this.record.testAddress,
              sampleType:this.record.sampleType,
              testPurpose:this.record.testPurpose
            })
          }else{
            this.form.setFieldsValue({
              testNum : this.record.data.length
            })
          }
        }, 100)

      },
      onChangeSampleDate(date, dateString) {
        if (date == null) {
          this.startDate = ''
        } else {
          this.startDate = moment(date).format('YYYY-MM-DD')
        }
      },
      handleSubmit() {
        const {
          form: {
            validateFields
          }
        } = this

        this.confirmLoading = true
        validateFields((errors, values) => {

          if (!errors) {

            if(this.record.data.length == 0){
              this.$message.warn('请填写第中检信息')
              this.activeKey = 'test'
              this.confirmLoading = false
              return
            }
            for (let i = 0; i < this.record.data.length; i++) {
              if(this.record.data[i].day == null){
                this.$message.warn('请填写第'+(i+1)+'次中检天数')
                this.activeKey = 'test'
                this.confirmLoading = false
                return
              }
            }
            for (let i = 0; i < this.record.data.length; i++) {
              if(null != this.record.data[i].actualInDate){
                this.record.data[i].actualInDateStatus = 1
              }
            }
            values.data = this.record.data
            values.zero = this.record.zero

            let param = {}
            param.id = this.record.id
            param.data = this.record.data

            if(this.record.data.length > 0){
              param.zero = moment(this.record.data[0].actualInDate != null?this.record.data[0].actualInDate:
                this.record.data[0].inDate).format('YYYY-MM-DD')
            }
            testProgressUpdate(param).then((res) => {
              this.confirmLoading = false
              if (res.success) {
                this.$message.success('修改成功')
                this.handleCancel()
                this.$emit('ok', values)
              } else {
                this.$message.error('修改失败：' + res.message)
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            if(this.activeKey ==  'test'){
              if(values.testNum != null && values.zero != null){
                this.activeKey = 'basic'
              }
            }
            if(this.activeKey ==  'basic'){
              if(values.testStatus != null && values.testCode != null
                && values.productName != null
                && values.productSampleStage != null
                && values.testType != null
                && values.dept != null
                && values.applicant != null
                && values.testProject != null
                && values.t != null
                && values.soc != null
                && values.testPeriod != null
                && values.quantity != null
                && values.testMan != null
                && values.testAddress != null
                && values.sampleType != null
              ){
                this.activeKey = 'test'
              }
            }
            this.confirmLoading = false
          }
        })
      },
      handleOnlyBeanSubmit() {


          this.confirmLoading = true
            let param = {}
            param.id = this.record.id
            param.remark = this.record.remark
            testProgressUpdateOnlyBean(param).then((res) => {
              this.confirmLoading = false
              if (res.success) {
                this.$message.success('修改成功')
                this.handleCancel()
                this.$emit('ok', values)
              } else {
                this.$message.error('修改失败：' + res.message)
              }
            }).finally((res) => {
              this.confirmLoading = false
            })

      },
      handleCancel() {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
<style lang="less" scoped>
  .ant-form-item {

    margin-bottom: 0px;

  }

  /deep/.ant-modal-body {
    padding: 0!important;
  }


  /deep/ .ant-table-thead > tr > th, /deep/ .ant-table-tbody > tr > td {
    padding: 0px;
  }

  /deep/ .ant-table-footer {

    padding: 0px;
  }

  /deep/ .ant-table-pagination.ant-pagination {
    margin: 5px 0;
  }
  /deep/.ant-input-number {
    width: 100%;
  }

  /deep/.ant-input-number-sm>.ant-input-number-input-wrap>.ant-input-number-input{
    text-align: center;
  }

  /deep/.ant-select-selection--single {
    border: 0;
  }

  /deep/ .ant-input {
    border: 0;
  }
  /deep/ input {
    border: 0;
  }
  /deep/.anticon svg {
    display: none;
  }

  /deep/.ant-select-disabled .ant-select-selection{
    background: #f5f5f500!important;
    color: rgba(0, 0, 0, 0.99);
    cursor: default;
  }
  /deep/.ant-input[disabled] {
    color: rgba(0, 0, 0, 0.99);
    background-color: unset;
    cursor: default;

  }
  /deep/.ant-calendar-picker-input{
    text-align: center;
  }

  /deep/.ant-input {

    height: 25px;
    font-size: 12px;
  }

  .green{
    background-color: #58a55c;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 25px;
  }

  /deep/.ant-calendar-picker-input.ant-input {
    color: black;
  }

  /deep/.green>div>input{
    background-color: #58a55c;
  }

</style>
