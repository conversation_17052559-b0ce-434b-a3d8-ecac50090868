<template>
    <div class="layout-main">
        <div class="slide">
            <a-menu
            style="width: 200px"
            :default-selected-keys="[defaultKey]"
            mode="inline"
            theme="light"
            :inline-collapsed="collapsed"
            >
                <a-menu-item key="1" @click="showView(1)">
                    <a-icon type="info-circle" />
                    <span>产品信息</span>
                </a-menu-item>
                <a-menu-item key="2" @click="showView(2)">
                    <a-icon type="reconciliation" />
                    <span>产能</span>
                </a-menu-item>
                <a-menu-item key="3" @click="showView(3)">
                    <a-icon type="file" />
                    <span>转阶段文档</span>
                </a-menu-item>
                <a-menu-item key="4" @click="showView(4)">
                    <a-icon type="apartment" />
                    <span>项目架构</span>
                </a-menu-item>
                <a-menu-item key="5" @click="showView(5)">
                    <a-icon type="table" />
                    <span>项目计划</span>
                </a-menu-item>
                <a-menu-item key="6" @click="showView(6)">
                    <a-icon type="edit" />
                    <span>客户议题</span>
                </a-menu-item>
                <a-menu-item key="7" @click="showView(7)">
                    <a-icon type="desktop" />
                    <span>周进展</span>
                </a-menu-item>
                <a-menu-item key="8" @click="showView(8)">
                    <a-icon type="user" />
                    <span>任务管理</span>
                </a-menu-item>
                <a-menu-item key="9" @click="showView(9)">
                    <a-icon type="hdd" />
                    <span>制样管理</span>
                </a-menu-item>
                <a-menu-item key="10" @click="showView(10)">
                    <a-icon type="experiment" />
                    <span>测试管理</span>
                </a-menu-item>
                <a-menu-item key="11" @click="showView(11)">
                    <a-icon type="question-circle" />
                    <span>问题管理</span>
                </a-menu-item>
                <a-menu-item key="12" @click="showView(12)">
                    <a-icon type="warning" />
                    <span>风险管理</span>
                </a-menu-item>
                <a-menu-item key="13" @click="showView(13)">
                    <a-icon type="copy" />
                    <span>技术文档</span>
                </a-menu-item>
                <a-menu-item key="14" @click="showView(14)">
                    <a-icon type="radar-chart" />
                    <span>项目看板</span>
                </a-menu-item>
            </a-menu>
            <div class="collapsed_bar" :style="collapsed ? left40 : left20" @click="toggleCollapsed">
                <a-icon class="collapsed_btn" :type="collapsed ? 'double-right' : 'double-left'" />
            </div>
        </div>
        <div class="wrap" :style="{ padding: '16px'}">
            <detailview v-if="defaultKey == '1'" :issueId="issueId" :projectdetail="projectdetail" :loading="loading" />
            <docs v-if="defaultKey == '13'" :issueId="issueId" />
        </div>
    </div>
</template>

<script>
import { getProjectDetail } from "@/api/modular/system/report"
import detailview from './detailview'
import docs from '../techdoc/index'
export default {
    components: {
        detailview,
        docs
    },
    created(){
        this.issueId = parseInt(this.$route.query.issueId)
        this.callProjectDetail()
    },
    data() {
        return {
            issueId:null,
            loading:true,
            projectdetail:{},
            defaultKey:'1',
            collapsed: false,
            left20:{
                left:'187px'
            },
            left40:{
                left:'41px'
            }
        };
    },
    methods: {
        toggleCollapsed() {
            this.collapsed = !this.collapsed;
        },
        showView(tg){
            this.defaultKey = tg+''
        },
        callProjectDetail(){
            this.loading = true
            let params = {issueId: this.issueId,title:''}
            getProjectDetail(params)
            .then((res)=>{
                if (res.result) {
                    this.projectdetail = res.data
                } else {
                    this.$message.error(res.message,1);
                }
                this.loading = false
                })
            .catch((err)=>{
                this.loading = false
                this.$message.error('错误提示：' + err.message,1)
            });
        },
    },
}
</script>

<style lang='less' scoped=''>
.layout-main{
    display: flex;
    flex-direction: row;
}
.wrap{
    flex: 1;
    background: #fff;
}
.slide{
    min-height: 100vh;
    max-width: 200px;
    background-color: #fff;
    box-shadow: 2px 0px 8px 0px rgba(29, 35, 41, 5%);
    z-index: 100;
}
.collapsed_bar{
  position: fixed;
  width: 20px;
  bottom: 0;
  top: 0;
  cursor: pointer;
}
.collapsed_btn{
  position: absolute;
  top: 45%;
}
/deep/.ant-menu-light{
    border-right-color: transparent;
}
/deep/.ant-menu-inline-collapsed{
  width: 40px !important;
}
/deep/.ant-layout-sider-collapsed{
  flex: 0 0 40px !important;
  max-width: 40px !important;
  min-width: 40px !important;
  width: 40px !important;
}
/deep/.ant-menu-inline-collapsed > .ant-menu-item, 
/deep/.ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item, .ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-submenu > .ant-menu-submenu-title, .ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title{
  padding: 0 12px !important;
}
</style>
<style>
.ant-layout-sider-collapsed{
  flex: 0 0 40px !important;
  max-width: 40px !important;
  min-width: 40px !important;
  width: 40px !important;
}
.ant-menu-inline-collapsed > .ant-menu-item, 
.ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item, .ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-submenu > .ant-menu-submenu-title, .ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title{
  padding: 0 12px !important;
}
.ant-menu-vertical .ant-menu-item, 
.ant-menu-vertical-left .ant-menu-item, 
.ant-menu-vertical-right .ant-menu-item, 
.ant-menu-inline .ant-menu-item, 
.ant-menu-vertical .ant-menu-submenu-title, 
.ant-menu-vertical-left .ant-menu-submenu-title,
.ant-menu-vertical-right .ant-menu-submenu-title, 
.ant-menu-inline .ant-menu-submenu-title{
  font-size: 13px;
}
</style>