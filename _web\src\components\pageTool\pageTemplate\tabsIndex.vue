<template>
  <div class="page-container"
    :style="`margin:${pageMargin};marginLeft:${isShowTabs ? '12px' : '0'}`">

    <div class="tabs normal-block" :style="`width:${ isShowTabs ? '180px' : '0'};padding:${ isShowTabs ? '' : '0'};`">
      <pbiTitle v-if="pageTitleShow" :title="pageTitle"></pbiTitle>
      <a-divider />
      <a-menu :selected-keys="activeKey" style="width: 256px" mode="vertical" @click="tabClick">
        <template v-for="(tab, index) in tabsList">
          <a-sub-menu v-if="tab.children && tab.children.length > 0" :key="tab.key">
            <span slot="title"><a-icon v-if="tab.icon" :type="tab.icon" /><span>{{tab.label}}</span></span>
            <a-menu-item v-for="(tabChildren, indexChildren) in tab.children" :key="tabChildren.key">
              <a-icon v-if="tabChildren.icon" :type="tabChildren.icon" />
              {{tabChildren.label}}
            </a-menu-item>
          </a-sub-menu>
          <a-menu-item v-else :key="tab.key">
            <a-icon v-if="tab.icon" :type="tab.icon" />
            {{tab.label}}
          </a-menu-item>
        </template>
      </a-menu>
    </div>
    <div class="icon" @click="handleShowTabs">
      <img class="action-icon" :src=" isShowTabs ? closedIcon : openIcon " alt="">
    </div>
    <div class="content normal-block"
      :style="`width:${ isShowTabs ? 'calc(100vw - 40px - 24px - 180px)' : 'calc(100vw - 40px - 24px)'}`">
      <pbiTitle v-if="pageTitleShow && !isShowTabs" :title="pageTitle + '-' + subTitle"></pbiTitle>
      <slot name="search"></slot>
      <!-- tab类型 -->
      <slot name="tab"></slot>
      <!-- 表格类型 -->
      <div v-if="pageTableShow" @mouseenter="tableFocus" @mouseleave="tableBlur">
        <a-spin :spinning="loading">
          <slot name="table"></slot>
          <pbiPagination v-if="paginationShow" style="margin-top: 8px;" :total="tableTotal" @change="paginationChange"
            @showSizeChange="paginationSizeChange"></pbiPagination>
        </a-spin>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    props: {
      pageLevel: {
        type: Number,
        default: 2, // 1:一级页面，2：二级页面，3：三级页面
      },
      pageTitle: {
        type: String,
        default: ''
      },
      pageTitleShow: {
        type: Boolean,
        default: true
      },
      tabsList: {
        type: Array,
        default: () => []
      },
      activeKey: {
        type: Array,
        default: () => []
      },
      pageTableShow: {
        type: Boolean,
        default: true
      },
      loading: {
        type: Boolean,
        default: true
      },
      tableTotal: {
        type: Number,
        default: 0
      },
      paginationShow: {
        type: Boolean,
        default: true
      }
    },
    data() {
      return {
        pageMargin: '',
        isShowTabs: true,
        subTitle: '',
        closedIcon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAABCCAYAAABn0ufGAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAFaSURBVHgB7Zc5boQwFIbfjJCgQuEGKei4AR03SEUb5VpQROICgY4LQAcXoEhDAxQsBUsBjj0SIyAsdrpI/iUkFn98si2eMdR1/VmW5SuwpqoqhI/vpmnemcFxHBEGEZOdgHO6rqO3L0ESavsWpLYfgZf2M/DUTgPOdtJ25u5Amft93ZQa/PUi+GM4yEEO/hMwTVPI85wNDMMQfN8HSZJ2nwvbG8MwgOd5j3PTNEGW5WuwKApwXRdUVQXDMOAsTzCOY4iiCHRdB03T4CrPPoqiCLfbDaizrOR4jUC2bSM8MLvV/LCSk4EgA0KmwHEcwGsFnXGZIAiQZVmo7/td4+mikyQJyrJsFxTO+k+m5Sj8e+QgBzlIEfyju7oWrgBcox7rCT5IVfugAomlbVuYpsklkKIo1Sm4tWDA3bYRWCyrzAUZN5y3CXiLUb7BRQQmy9ZIa9mCXxh6Acb8AMgyITOCgOeBAAAAAElFTkSuQmCC',
        openIcon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAABCCAYAAABjJzf7AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAFZSURBVHgB7ZY/aoRAFMZfxHo3e4MU223pCbxBOisJAQ+SO1haBOyssjew00or7YTkBv4D/xQ6ec8gRNfRWQip/EAGxu/33syAnwN5nr+nafoEosqyjOHzWRTFizDQdR1DgAl1I2BUXdfb3X4DpM1uc2CzGw/gdXsg4Hg8rm0TmqaBtm2/JEl6EwJIfd9DWZYggaCw+s8Id2oHdmAH/grwff8+II5jcF2XEkIMMAxjGG3bBsyjZWIpyIIgYKZpsiiKJvPk5SYfph2zLIt5njcBuKdE+0DPzby8ZA7DEHBZoKoqnM9nPkBV6WiTJAFN0+BwOMDqKTmOMwSvruuL5psOl8sFFEWBVa39H+ZaPSWedmAHduA/ALzODaO8ZaQwo/jBhz7+V3mralVVdH27kvl0OmWySFU0Xsd3skjViWHMJTSMl1u8oqbPvGXKQlXnHbaqzoEPND+CoL4Bb0vfQ5Vb7AEAAAAASUVORK5CYII='
      }
    },
    created() {
      switch(this.pageLevel){
        case 2:
          this.pageMargin = '8px 12px 8px 12px'
          break;
        case 3:
          this.pageMargin = '32px 12px 8px 12px'
          break;
        case 4:
          this.pageMargin = '0px 12px 0px 12px'
      }

    },
    methods: {
      tableFocus() {
        this.$emit('tableFocus')
      },
      tableBlur() {
        this.$emit('tableBlur')
      },

      paginationChange(value) {
        this.$emit('paginationChange', value)
      },
      paginationSizeChange(value) {
        this.$emit('paginationSizeChange', value)
      },
      handleShowTabs() {
        this.isShowTabs = !this.isShowTabs

        this.subTitle = this.tabsList.filter(item => item.key === this.activeKey[this.activeKey.length - 1])[0].label
        this.$emit('tabsShow', this.isShowTabs)
      },
      tabClick(value) {
        this.$emit('tabClick', value.keyPath)
        // value.keyPath 为数组，从左到右依次为当前点击的菜单的key、父级菜单的key
      }
    }

  }
</script>
<style lang="less" scoped>
  .page-container {
    overflow: hidden;
    /* 40:顶栏  16:外边距 */
    height: calc(100vh - 56px);
    /* font-family: NotoSansCJK; */
    display: flex;
  }

  .normal-block {
    overflow: hidden;

    padding: 8px 12px;
    border-radius: 4px;
    background-color: #fff;
    height: 100%;
  }

  .page-container .tabs {
    width: 180px;
    padding-top: 16px;
  }

  .page-container .content {
    width: calc(100vw - 40px - 24px - 180px);
  }

  .page-container .icon {
    height: fit-content;
    margin: auto;
  }

  .page-container .icon .action-icon {
    width: 12px;
    height: 64px;
  }

  /* 分割线 */
  /deep/.tabs .ant-divider-horizontal {
    margin: 16px 0;
  }

  /deep/.ant-menu {
    color: #333;
  }

  /deep/.ant-menu-vertical {
    border-right: none;
    width: 156px !important;
  }

  /deep/.ant-menu-item {
    font-size: 12px;
    height: fit-content;
    line-height: normal;
    margin-top: 0;
    margin-bottom: 0;
    padding: 8px 0 8px 8px;
  }

  /deep/ .ant-menu-vertical .ant-menu-submenu .ant-menu-submenu-title {
    height: fit-content;
    line-height: normal;
  }

  /deep/.ant-menu-vertical .ant-menu-submenu-title {
    margin-top: 0;
    margin-bottom: 0;
    padding: 0;
  }

  /deep/.ant-menu-submenu {
    padding: 8px 0 8px 8px;
  }

  /deep/.ant-menu-vertical .ant-menu-submenu-title {
    font-size: 12px;
    display: flex;
    justify-content: space-between;
  }

  /deep/.ant-menu-item .anticon,
  /deep/.ant-menu-submenu-title .anticon {
    font-size: 12px;
    margin-right: 4px;
    min-width: 12px;
  }

  /deep/.ant-menu-vertical .ant-menu-item:not(:last-child) {
    margin-bottom: 0;
  }
</style>