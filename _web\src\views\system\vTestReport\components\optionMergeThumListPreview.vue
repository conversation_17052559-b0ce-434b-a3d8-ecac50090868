<template>
  <a-spin :spinning="isLoading">
    <div class="all-wrapper">
      <div class="head_title">报告名称：{{ reportName }}</div>

      <div v-if="Array.isArray(templateResultObj.resultJsonObjList)">
        <Carousel autoplay arrows>
          <div class="carousel-item" v-for="(resultJsonObj, optionIndex) in templateResultObj.resultJsonObjList || []">
            <div class="flex-center">
              <h3>方案：{{resultJsonObj.optionName}}</h3>
            </div>
            <div class="thumbnail-block" v-for="item in carouselChartList[optionIndex].filter(v => v.show)">
              <div class="title">{{ item.chartTitle}}</div>
              <div class="thumbnail-echarts-div" :id="item.id" :ref="item.id" :class="item.id" @click="pushToReview"></div>
            </div>
          </div>
          <template #prevArrow>
            <div class="custom-slick-arrow" style="left: 12px;">
              <a-icon type="left-circle" />
            </div>
          </template>
          <template #nextArrow>
            <div class="custom-slick-arrow" style="right: 12px">
              <a-icon type="right-circle" />
            </div>
          </template>
        </Carousel>
      </div>

    </div>
  </a-spin>
</template>

<script>
import {getOptionMergeReport} from "@/api/modular/system/limsManager";
import jsonBigint from "json-bigint";
import {decodeAndDecompress} from "@/utils/util";
import _ from "lodash";
import { Carousel } from 'ant-design-vue';

export default {
  name: "optionMergeThumListPreview",
  components: {
    Carousel,
  },
  props: {
    reportId: {
      type: String,
      required: true
    },
  },
  data() {
    return {
      id: null,
      data: {},
      reportName: '',
      templateResultObj: {},

      carouselChartList: [],

      echartObj: {},
      isLoading: false,

      echartsColorList: [
        "#c00000",
        "#0070c0",
        "#808080",
        "#7030a0",
        "#4472c4",
        "#a5a5a5",
        "#ed7d31",
        "#5b9bd5",
        "#70ad47",
        "#000000",
        "#ff9999",
        "#ffc000",
        "#00b050"
      ],
      // 折点类型数组 三角、圆形、矩形、菱形、箭头、图钉、【空心：三角、圆形、矩形、菱形、箭头、图钉】、倒三角、五角星
      echartsSymbolList: [
        'triangle', 'circle', 'rect', 'diamond', 'arrow', 'pin',
        'emptyTriangle', 'emptyCircle', 'emptyRect', 'emptyDiamond', 'emptyArrow', 'emptyPin',
        'path://M0,0 L10,0 L5,10 Z',
        'path://M100,22.4 L78.6,54.6 L44.2,54.6 L72.6,79.4 L62.8,112.6 L100,92.4 L137.2,112.6 L127.4,79.4 L155.8,54.6 L121.4,54.6 Z'
      ],
    }
  },
  watch: {
    reportId(newVal, oldVal) {
      this.fetchReportData(); // 重新获取缩报告数据
    }
  },
  computed: {
  },
  mounted() {
    this.fetchReportData();
    window.addEventListener('resize', this.updateFontSize);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateFontSize);
  },
  methods: {
    fetchReportData() {
      if (this.reportId) {
        this.isLoading = true
        getOptionMergeReport({id: this.reportId, needRefresh: 'false'}).then(res => {
          if (res.success) {
            this.data = res.data
            this.reportName = res.data.reportName

            let json = jsonBigint({storeAsString: true})
            this.templateResultObj = json.parse(decodeAndDecompress(res.data.allDataJson))

            this.initThumList()
          }
        }).finally(() => {
          this.isLoading = false
        })
      }
    },

    initThumList() {
      this.carouselChartList = []
      this.editObjList = ['voltage','innerres','height','volume','weight','isolateres']
      const titleDataObj = {
        voltage:    {chartTitle: 'Calendar Life_OCV', YTitle: 'OCV / mV', hasYTitle2: false, YTitle2: null},
        innerres:   {chartTitle: 'Calendar Life_ACR', YTitle: 'ACR / mΩ', hasYTitle2: false, YTitle2: null},
        height:     {chartTitle: 'Calendar Life_Size', YTitle: 'Size / mm', hasYTitle2: false, YTitle2: null},
        volume:     {chartTitle: 'Calendar Life_Volume', YTitle: 'Volume / g', hasYTitle2: false, YTitle2: null},
        weight:     {chartTitle: 'Calendar Life_Weight', YTitle: 'Weight / g', hasYTitle2: true, YTitle2: 'Mass Loss Rate / %'},
        isolateres: {chartTitle: 'Calendar Life_Insulation Resistance', YTitle: 'Insulation Resistance / mΩ', hasYTitle2: false, YTitle2: null},
      }
      let resultJsonObjList = this.templateResultObj.resultJsonObjList || []
      resultJsonObjList.forEach((resultJsonObj, optionIndex) => {
        let thumList = []
        // 是否展示 尺寸、产气量、重量、绝缘阻值
        const storageDaySetMap = resultJsonObj ? resultJsonObj.storageDaySetMap : {}
        const verifyList = ['height', 'volume', 'weight', 'isolateres']
        const testCondition = resultJsonObj ? resultJsonObj.testCondition + '_' : ''
        this.editObjList.forEach((item, index) => {
          let thumObj = { id: optionIndex + '-' + item, show: true, chartTitle: testCondition + titleDataObj[item].chartTitle, echartList: resultJsonObj[`${item}EchartList`],
            YTitle: titleDataObj[item].YTitle, hasYTitle2: titleDataObj[item].hasYTitle2, YTitle2: titleDataObj[item].YTitle2 }
          if (verifyList.includes(item)) {
            if (item === 'weight') {
              thumObj.echartY2List = resultJsonObj.weightLossRateEchartList
            }
            thumObj.show = Array.isArray(storageDaySetMap[item]) && storageDaySetMap[item].length > 0
          }
          thumList.push(thumObj)
        })

        this.carouselChartList.push(thumList)
      })

      document.documentElement.style.setProperty('--title-font-size', window.innerWidth > 1400 ? '12px' : '10px');
      // 赋值，echarts图初始化
      this.$nextTick(() => {
        this.carouselChartList.forEach((thumList, optionIndex) => {
          thumList.forEach((thumObj, index) => {
            if (thumObj.show) {
              this.initThumbnailEchart(thumObj, optionIndex, index)
            }
          })
        })
      })

    },
    initThumbnailEchart(thumObj, optionIndex, index) {
      // 检查是否存在 ECharts 实例 并清除
      if (this.echartObj[thumObj.id]) {
        this.echartObj[thumObj.id].dispose();
      }
      // 获取非克隆的幻灯片
      let element = null
      const slides = this.$el.querySelectorAll('.slick-slide:not(.slick-cloned)')
      for (let i = 0; i < slides.length; i++) {
        if (slides[i].getElementsByClassName(thumObj.id).length > 0) {
          element = slides[i].getElementsByClassName(thumObj.id)[0]
          break;
        }
      }

      if (element != null) {
        this.echartObj[thumObj.id] = this.echarts.init(element)
        let echartList = _.cloneDeep(this.carouselChartList[optionIndex][index].echartList)
        let echartY2List = this.carouselChartList[optionIndex][index].echartY2List ? _.cloneDeep(this.carouselChartList[optionIndex][index].echartY2List) : []
        const options = this._handleThumbnailEchart(echartList, echartY2List, thumObj)
        this.echartObj[thumObj.id].clear()
        this.echartObj[thumObj.id].setOption(options)
      }
    },
    _handleThumbnailEchart(echartList, echartY2List = [], thumObj) {
      let series = []

      let lineColorObj = {} // 折线颜色
      let lineSymbolObj = {} // 折点类型

      echartList.forEach((v, index) => {
        let sizeType = v.sizeType
        let sampleCode = v.sampleCode

        if (thumObj.id.includes('height')) {
          if (!lineColorObj.hasOwnProperty(sizeType)) {
            lineColorObj[sizeType] = this.echartsColorList[Object.keys(lineColorObj).length % this.echartsColorList.length]
          }
          if (!lineSymbolObj.hasOwnProperty(sampleCode)) {
            lineSymbolObj[sampleCode] = this.echartsSymbolList[Object.keys(lineSymbolObj).length % this.echartsSymbolList.length]
          }
        } else {
          if (!lineColorObj.hasOwnProperty(sampleCode)) {
            lineColorObj[sampleCode] = this.echartsColorList[Object.keys(lineColorObj).length % this.echartsColorList.length]
          }
        }

        const temColor = thumObj.id.includes('height') ? lineColorObj[sizeType] : lineColorObj[sampleCode]
        const temSymbol = thumObj.id.includes('height') ? lineSymbolObj[sampleCode] : 'rect'

        series.push(
          {
            name: v.sampleCode,
            type: 'line',
            symbol: temSymbol,
            lineStyle: {
              width: 1.5,
              type: 'solid',
              color: temColor,
            },
            itemStyle: {
              color: temColor
            },
            data: v.data,
          }
        )

          // 添加次Y轴数据
          if (echartY2List != []) {
            series.push(
              {
                name: v.sampleCode + '_two',
                type:'line',
                symbol: temSymbol,
                lineStyle: {
                  width: 1.5,
                  type: 'dashed',
                  color: temColor,
                },
                itemStyle: {
                  color: temColor
                },
                data: echartY2List.length > index ? echartY2List[index].data : []
              }
            )
          }
      })

      const isLarge = window.innerWidth > 1400
      const titleData = this.getEchartTitleData(thumObj, echartList, isLarge)

      let yAxis = [
        {
          type: 'value',
          name: titleData.YTitle,
          position: 'left',
          min: titleData.yMin,
          max: titleData.yMax,
          nameLocation: 'middle',
          nameGap: titleData.yTitleLetf,
          nameTextStyle: { fontSize: isLarge ? 10 : 8 },
          axisLabel: {show: true, margin: 4, textStyle: { fontSize: isLarge ? 10 : 8 } },
          axisLine: { onZero: false },
          axisTick: { show: false }
        }
      ]
      if (titleData.yInterval) {
        yAxis[0].interval = titleData.yInterval
      }

      if (titleData.hasYTitle2) {
        yAxis.push(
          {
            type: 'value',
            name: titleData.YTitle2,
            position: 'right',
            min: titleData.yMin2,
            max: titleData.yMax2,
            nameLocation: 'middle',
            nameGap:  isLarge ? 21 : 18,
            nameTextStyle: { fontSize: isLarge ? 10 : 8 },
            axisLabel: {show: true, margin: 4, textStyle: { fontSize: isLarge ? 10 : 8 } },
            axisLine: { onZero: false },
            axisTick: { show: false }
          }
        )
      }

      return {
        textStyle: {
          fontFamily: "Times New Roman"
        },
        grid: {
          show: true,
          top: 8,
          left: titleData.gridLeft + (isLarge ? 10 : 0),
          right: titleData.hasYTitle2 ? (isLarge ? 41 : 26) :  (isLarge ? 30 : 20),
          bottom: isLarge ? 35 : 27,
        },
        xAxis: {
          type: 'value',
          name: 'Storage Time / D',
          nameLocation: 'middle',
          nameGap: isLarge ? 18 : 15,
          nameTextStyle: { fontSize: isLarge ? 10 : 8 },
          axisLabel: {show: true, margin: 4, textStyle: { fontSize: isLarge ? 10 : 8 } },
          axisLine: { onZero: false },
          axisTick: { show: false },
          minInterval: 1,
        },
        yAxis: yAxis,
        series: series
      }
    },
    getEchartTitleData(thumObj, echartList, isLarge) {
      let titleData = _.cloneDeep(thumObj)

      if (thumObj.hasYTitle2) {
        // 次Y轴默认初始值
        titleData.yMin2 = -0.4
        titleData.yMax2 = 0.6
        titleData.yInterval2 = 0.2
      }

      let yAxisOneDataList = []
      echartList.forEach(seriesItem => {
        yAxisOneDataList.push(...seriesItem.data.map(mapItem => Number(mapItem[1])))
      })
      const rangeValue = thumObj.id.includes('voltage') ? [4500, 3000] : this._getYAxisRadius(Math.max.apply(null, yAxisOneDataList), Math.min.apply(null, yAxisOneDataList))
      let yMax = rangeValue[0]
      let yMin = rangeValue[1]
      let yInterval = thumObj.id.includes('voltage') ? 300 : null

      // 重量图次Y轴
      if (thumObj.id.includes('weight')) {
        // 重新计算主Y轴的最小值和间隔值，使得分割段数为5
        // let newDiff = (yMax - yMin) / 5 > 1 ? Math.ceil((yMax - yMin) / 5) * 5 : 5
        // yMin = yMax - newDiff
        // yInterval = newDiff / 5

        // 例如：重量最大值135.5，向下取整得到130，间隔为10，5个间隔，上2下3，150 140 130 120 110 100
        const number = Math.max.apply(null, yAxisOneDataList)
        const value = isFinite(number) && typeof number === 'number' ? Math.floor(number / 10) * 10 : 0
        yMax = value + 20
        yMin = value - 30
        yInterval = 10
      }

      titleData.yMax = yMax
      titleData.yMin = yMin
      if (yInterval === null) {
        delete titleData.yInterval
      } else {
        titleData.yInterval = yInterval
      }

      const len = isLarge ? 5 : 4

      const labelLen = yMax - yMin >= 4 ? (yMax.toString().length)*len : 2*len + 2
      titleData.yTitleLetf = len + labelLen + 4
      titleData.gridLeft = 3*len + labelLen + 4

      return titleData
    },
    // 获得y轴最大最小值
    _getYAxisRadius(originalMax, originalMin) {
      const differenceValue = originalMax - originalMin

      const transferMax = Math.trunc(originalMax + differenceValue).toString().split("")
      const transferMin = Math.trunc(originalMin - differenceValue).toString().split("")

      const newMax = Number(transferMax.map((mapItem, mapIndex) => mapIndex == 0 ? (Number(mapItem) + 1).toString() : '0').join(""))
      const newMin = Number(transferMin.map((mapItem, mapIndex) => mapIndex == 0 ? mapItem : '0').join(""))

      return [newMax, newMin]
    },

    updateFontSize() {
      this.initThumList();
    },

    pushToReview(){
      window.open("/v_report_preview?id=" + this.reportId + "&type=optionMerge", "_blank")
    },
  },
}
</script>

<style lang="less" scoped>
:root {
  --title-font-size: 12px;
}

.all-wrapper {
  padding: 10px;
  height: calc(100vh - 40px - 12*2px - 40px);
}

.flex-center {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 标题 */
.head_title {
  color: #333;
  padding-bottom: 6px;
  font-size: 16px;
  font-weight: 500;
}

.head_title::before {
  width: 6px;
  background: #1890ff;
  margin-right: 8px;
  content: "\00a0"; //填充空格
}

/deep/ .ant-tabs .ant-tabs-bar {
  margin: 0 0 10px 0;
}

/deep/ .ant-tabs .ant-tabs-tab {
  padding: 8px 12px;
  font-size: 12px;
}

.carousel-item {
  width: 100%;
  height: calc(100vh - 40px - 12*2px - 40px - 10*2px - 22px);
  padding: 8px 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow-y: auto;
  //background: #f0f2f5;
  border-radius: 5px;
}

.thumbnail-block {
  width: 100%;
  margin-bottom: 10px;
  border-radius: 5px;

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.thumbnail-block .title {
  font-size: var(--title-font-size);
  font-family: 'Times New Roman';
  margin-bottom: 2px;
}

.thumbnail-block .thumbnail-echarts-div {
  height: calc((100vh - 40px - 12*2px - 40px - 10*2px - 22px - 25px)*0.4);
  width: calc((100vh - 40px - 12*2px - 40px - 10*2px - 22px - 25px)*0.4*1.6);
}

/deep/ .slick-list .slick-slider {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* 自定义底部切换点样式 */
/deep/ .ant-carousel .slick-dots li button {
  background-color: rgba(24, 144, 255, 0.6);
}

/deep/ .ant-carousel .slick-dots li.slick-active button {
  background-color: rgba(24, 144, 255);
}

/* 自定义左右箭头样式 */
/deep/ .ant-carousel .slick-arrow.custom-slick-arrow {
  width: 25px;
  height: 25px;
  font-size: 25px;
  color: rgba(24, 144, 255, 0.6);
  z-index: 2;
}

/deep/ .ant-carousel .custom-slick-arrow:before {
  display: none;
}

/deep/ .ant-carousel .custom-slick-arrow:hover {
  opacity: 0.6;
}
</style>