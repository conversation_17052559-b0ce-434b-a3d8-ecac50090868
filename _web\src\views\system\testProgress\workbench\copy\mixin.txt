import Vue from "vue"
import moment from "moment"
import { ACCESS_TOKEN } from "@/store/mutation-types"

import { downloadfile1 } from "@/utils/util"
import { formatDate } from "@/utils/format"





import {
  getTestProgress,
  finishCalLifeTodoTask,
  updateTestProDetail,
  importModel,
  exportModel,
  aliasCopy,
  getInBoxPositionList,
  updateInBoxPosition
} from "@/api/modular/system/testProgressManager"
import { tLimsTestdataScheduleList } from "@/api/modular/system/limsManager"

import stepData from "../../../lims/folder/stepData.vue"


export const mixin = {

    components: {
        stepData
    },
    props: {
        modalData: {
            type: Object,
            default: {}
        }
    },
    data() {
        return {
          positionResultData: [],
          positionLoadData: parameter => {
              return getInBoxPositionList(parameter).then((res) => {
                this.positionResultData = res.data
                return this.positionResultData
              })
            },
            positionSelectedRowKeys: [],
            cellSelectedRowKeys: [],
            positionSelectedRows: [],
            cellSelectedRows: [],
            current: 0,
            mgVisible: false,
            modalLoading: false,
            isShowDays: false,
            isShowPositions: false,
            startDay: '',
            middleCheck: "",
            actualInDate: "",
            // 选中的大小中检的flowId
            flowId: "",
            // 最近的一条进箱时间
            lastActualInDate: "",

            postUrl: "/api/sysFileInfo/uploadfile",
            headers: {
                Authorization: "Bearer " + Vue.ls.get("Access-Token")
            },
            details: {},
            tableData: {},
            outQueryFlowRecord: {},

            mgData: [],
            columns: [],
            // 文件上传
            fileList: [],
            selectedRowKeys: [],
            tableNameMenu: {
                alias: "测试项目别名",
                cellTestCode: "测试编码",
                middleCheck: "大小中检",
                beforeVoltage: "电压/mV",
                beforeInnerres: "内阻/mΩ",
                afterVoltage: "中检后电压/mV",
                afterInnerres: "中检后内阻/mΩ",
                volume: "产气量/g",
                weight: "重量/g",
                isolateres: "绝缘阻值/mΩ",
                totalHeight: "端高/mm",
                shoulderHeight: "肩高/mm",
                aPointDiameter: "A点直径",
                bPointDiameter: "B点直径",
                cPointDiameter: "C点直径",
                topPointDiameter: "上部直径/mm",
                middlePointDiameter: "中部直径/mm",
                bottomPointDiameter: "下部直径/mm",
                thicknessOne: "厚度1/mm",
                thicknessTwo: "厚度2/mm",
                thicknessThree: "厚度3/mm",
                thicknessFour: "厚度4/mm",
                thicknessFive: "厚度5/mm",
                thickTopLeft: "上左厚度/mm",
                thickTopMiddle: "上中厚度/mm",
                thickTopRight: "上右厚度/mm",
                thickMiddleLeft: "中左厚度/mm",
                thickMiddle: "中心厚度/mm",
                thickMiddleRight: "中右厚度/mm",
                thickBottomLeft: "下左厚度/mm",
                thickBottomMiddle: "下中厚度/mm",
                thickBottomRight: "下右厚度/mm",
                coverInnerRing: "盖板内圈φ/mm",
                coverOuterRing: "盖板外圈φ/mm",
                topPoint:"顶点/mm",
                oneThird:"1/3处/mm",
                oneSecond:"1/2处/mm",
                secondThird:"2/3处/mm",
                bottom:"尾部/mm",

                // topPoint1: "顶点1/mm",
                // oneThird1: "1/3处1/mm",
                // oneSecond1: "1/2处1/mm",
                // secondThird1: "2/3处1/mm",
                // bottom1: "尾部1/mm",
                // topPoint2: " 顶点2/mm",
                // oneThird2: "1/3处2/mm",
                // oneSecond2: "1/2处2/mm",
                // secondThird2: "2/3处2/mm",
                // bottom2: "尾部2/mm",
                // topPoint3: "顶点3/mm",
                // oneThird3: "1/3处3/mm",
                // oneSecond3: "1/2处3/mm",
                // secondThird3: "2/3处3/mm",
                // bottom3: "尾部3/mm",
                // topPoint4: "顶点4/mm",
                // oneThird4: "1/3处4/mm",
                // oneSecond4: "1/2处4/mm",
                // secondThird4: "2/3处4/mm",
                // bottom4: "尾部4/mm",
                // topPoint5: "顶点5/mm",
                // oneThird5: "1/3处5/mm",
                // oneSecond5: "1/2处5/mm",
                // secondThird5: "2/3处5/mm",
                // bottom5: "尾部5/mm"
            },
            inBoxPositionColumns: [
              {
                title: '序号',
                dataIndex: 'index',
                align: 'center',
                width: 30,
                ellipsis: true,
                customRender: (text, record, index) => `${index + 1}`
              },
              {
                title: '温箱编号',
                dataIndex: 'warmBoxCode',
                align: 'center',
                width: 40,
              },
              {
                title: '温箱样品架层号',
                dataIndex: 'rackNumber',
                align: 'center',
                width: 40,
              },
              {
                title: '温箱类型',
                dataIndex: 'warmBoxType',
                align: 'center',
                width: 40,
              },
              {
                title: '温箱型号',
                dataIndex: 'warmBoxModel',
                align: 'center',
                width: 40,
              },
              {
                title: '温箱容积/L',
                dataIndex: 'warmBoxCapacity',
                align: 'center',
                width: 40,
              },
              {
                title: '检测室',
                dataIndex: 'testRoom',
                align: 'center',
                width: 40,
              },
            ],
            inBoxColumns: [
              {
                title: "序号",
                dataIndex: "index",
                align: "center",
                customRender: (text, record, index) => `${index + 1}`
              },
              {
                title: "测试编码",
                dataIndex: "cellTestCode",
                align: "center",
                scopedSlots: {
                  customRender: "cellTestCode"
                }
              },
              {
              	title: "进箱位置",
              	dataIndex: "inBoxPosition",
              	align: "center",
              	scopedSlots: {
              		customRender: "inBoxPosition"
              	}
              },
            ],
            allColumns: [
                {
                    title: "序号",
                    dataIndex: "index",
                    align: "center",
                    customRender: (text, record, index) => `${index + 1}`
                },
                {
                    title: "测试编码",
                    dataIndex: "cellTestCode",
                    align: "center",
                    scopedSlots: {
                        customRender: "cellTestCode"
                    }
                },
                // {
                // 	title: "测试项目别名",
                // 	dataIndex: "alias",
                // 	align: "center",
                // 	scopedSlots: {
                // 		customRender: "alias"
                // 	}
                // },
                {
                    title: "大小中检",
                    dataIndex: "middleCheck",
                    align: "center",
                    // customRender: value => {
                    // 	return value === "small" ? "小中检" : value === "large" ? "大中检" : "-"
                    // },
                    scopedSlots: {
                        customRender: "middleCheck"
                    }
                }
                // {
                // 	title: "计划开始时间",
                // 	dataIndex: "inDate",
                // 	align: "center",
                // 	scopedSlots: {
                // 		customRender: "inDate"
                // 	}
                // },
                // {
                // 	title: "计划结束时间",
                // 	dataIndex: "outDate",
                // 	align: "center",
                // 	scopedSlots: {
                // 		customRender: "outDate"
                // 	}
                // },
                // {
                // 	title: "存储天数",
                // 	dataIndex: "day",
                // 	align: "center",
                // 	scopedSlots: {
                // 		customRender: "day"
                // 	}
                // },
                // {
                // 	title: "存储阶段",
                // 	dataIndex: "orderNumber",
                // 	align: "center",
                // 	scopedSlots: {
                // 		customRender: "orderNumber"
                // 	}
                // }
            ],
            mgColumns: [
                {
                    title: "序号",
                    align: "center",
                    width: 50,
                    customRender: (text, record, index) => {
                        if (!record.isChild) {
                            return index + 1
                        }
                    }
                },
                {
                    title: "委托单号",
                    dataIndex: "folderno",
                    align: "center",
                    width: 90
                },
                {
                    title: "主题",
                    dataIndex: "theme",
                    align: "center",
                    ellipsis: true,
                    width: 90
                },
                {
                    title: "样品编号",
                    width: 90,
                    align: "center",
                    dataIndex: "orderno"
                },
                {
                    title: "测试项目编码",
                    width: 90,
                    align: "center",
                    dataIndex: "testcode"
                },
                {
                    title: "测试项名称",
                    width: 90,
                    align: "center",
                    dataIndex: "testname"
                },
                {
                    title: "测试项目别名",
                    width: 90,
                    align: "center",
                    dataIndex: "alias"
                },
                {
                    title: "测试编码",
                    width: 90,
                    align: "center",
                    dataIndex: "celltestcode",
                    scopedSlots: { customRender: "celltestcode" }
                },
                {
                    title: "数据位置",
                    width: 60,
                    align: "center",
                    dataIndex: "dataPath",
                    ellipsis: true
                },
                {
                    title: "开始时间",
                    width: 90,
                    align: "center",
                    dataIndex: "startTime",
                    customRender: (text, record, index) => {
                        if (null != text) {
                            return moment(text).format("YYYY-MM-DD")
                        }
                        return text
                    }
                },
                {
                    title: "结束时间",
                    width: 90,
                    align: "center",
                    dataIndex: "endTime",
                    customRender: (text, record, index) => {
                        if (null != text) {
                            return moment(text).format("YYYY-MM-DD")
                        }
                        return text
                    }
                },
                {
                    title: "设备编号",
                    width: 60,
                    align: "center",
                    dataIndex: "equiptcode"
                },
                {
                    title: "通道编号",
                    width: 60,
                    align: "center",
                    dataIndex: "channelno"
                }
            ],

            //大小中检阶段
            middleColums: [],
            middleData: [],
            middleList: [],

            // 数据填写阶段
            dataColums: [],
            dataInfo: [],
            dataList: [],

            // 最后一个阶段
            lastColums: [],
            lastData: [],
            lastList: [],

            mgSelectedRowKeys: [],
            selectionRows: [],
            mgSelectionRows: [],

            // 文件上传
            fileNames: "",
            fileList: [],
            postUrl: "/limsUpload/open/basemodule/sys/files/upload",
            headers: {
                Authorization: "Bearer " + Vue.ls.get(ACCESS_TOKEN)
            }
        }
    },
    methods: {
        getTestProgress() {
            getTestProgress({ id: this.modalData.testProgressId }).then(res => {
                if (!res.data.data) return
                res.data.data.forEach(v => {
                    if (v.orderNumber === res.data.data.length) {
                        this.lastActualInDate = v.actualInDate
                    }
                })
            })
        },
        updateTestProDetail(params) {
            console.log(params)
            if (params.lifeTestRecordDataMap) {
                params.lifeTestRecordDataMap.forEach(v => {
                    delete v.isMiddleClick
                })
            }
            updateTestProDetail(params).then(res => {
                if (!res.success) return this.$message.error("错误提示：" + res.message)
            })
        },
        openStepData(record) {
            this.outQueryFlowRecord = record

            if (record.flowId != null) {
                this.outQueryFlowRecord.flowId = record.flowId
                this.$refs.stepData.query(this.outQueryFlowRecord, false)
            } else {
                this.$message.warn("测试数据为空")
                return
            }
        },

        handleInput(value, index,row) {
            if (value !== "" && document.getElementById(index).style.backgroundColor === "rgb(255, 233, 237)") {
                document.getElementById(index).style.backgroundColor = "transparent"
            }
            if(index.indexOf('before') !== -1 && value){
                this.dataInfo[row].timeOfFillInnerres = formatDate(new Date(),true)
            }
            if(index.indexOf('after') !== -1 && value){
                this.dataInfo[row].timeOfFillInnerres2 = formatDate(new Date(),true)
            }
            const params = {
                id: this.modalData.ordTaskId,
                lifeTestRecordDataMap: JSON.parse(JSON.stringify(this.dataInfo))
            }
            this.updateTestProDetail(params)
        },

        /**
         * 大小中检弹窗
         * @param {*} record
         */
        // 选中数据
        selectTestData(record) {
            // 测试数据
            // this.details.lifeTestRecordDataMap[
            //     this.details.lifeTestRecordDataMap.findIndex(v => v.cellTestCode === "3-202308310010-0003")
            // ].checkData = JSON.stringify(record)
            // this.details.lifeTestRecordDataMap[
            //     this.details.lifeTestRecordDataMap.findIndex(v => v.cellTestCode === "3-202308310010-0003")
            // ].isMiddleClick = true

            //正式情况
            this.details.lifeTestRecordDataMap[
                this.details.lifeTestRecordDataMap.findIndex(v => v.cellTestCode === record.celltestcode)
            ].checkData = JSON.stringify(record)
            this.details.lifeTestRecordDataMap[
                this.details.lifeTestRecordDataMap.findIndex(v => v.cellTestCode === record.celltestcode)
            ].isMiddleClick = true

            const params = {
                id: this.modalData.ordTaskId,
                lifeTestRecordDataMap: JSON.parse(JSON.stringify(this.details.lifeTestRecordDataMap))
            }
            this.updateTestProDetail(params)
        },
        // 展示选择的数据
        getCheckboxProps(record) {
            return {
                props: {
                    defaultChecked: this.flowId ? record.flowId === this.flowId : false,
                    disabled: this.modalData.taskStatus === "已完成" ? true : false
                }
            }
        },
        // 点击大小中检按钮,查找对应的数据
        chooseMgData(record, dataIndex) {
            if (document.getElementById(dataIndex).style.backgroundColor === "rgb(255, 233, 237)") {
                document.getElementById(dataIndex).style.backgroundColor = "transparent"
            }

            // 如果已经有checkData的数据，获取flowId
            if (record.checkData) this.flowId = JSON.parse(record.checkData).flowId

            // 测试数据
            // tLimsTestdataScheduleList({ celltestcode: "04QCE34221101HD152126077-202303090041-0001", alias: "日历寿命1" })
            //正式情况
            tLimsTestdataScheduleList({ celltestcode: record.cellTestCode || String(new Date()), alias: record.alias })
                .then(res => {
                    if (res.success) {
                        this.mgData = res.data
                        if (res.data.length === 0 || res.data[0].flowId === '' || res.data[0].flowId === null) {
                            record.isMiddleClick = true
                        }
                    } else {
                        this.$message.error("查询失败：" + res.message)
                    }
                })
                .finally(() => {
                    this.mgVisible = true
                })
        },
        // excel  ->  table
        copyFromExcel(event, tableColumn, tableData, row, text) {
            // row:那行
            // column:那列
            let outArr = event.clipboardData.getData("text").split("\n")
            let inArr = []
            let column = tableColumn.findIndex(v => v.dataIndex === text)
            let firstData = ""
            if (outArr.length > 1) {
                // 列
                for (let i = 0; i < outArr.length - 1; i++) {
                    // 行
                    inArr = outArr[i].split("\t")
                    for (let j = 0; j < inArr.length; j++) {
                        if (inArr[j] !== null && inArr[j] !== "") {
                            if (i === 0 && j === 0) firstData = inArr[j].replace("\r", "")
                            if (tableData[row + i] && tableColumn[column + j]) {
                                tableData[row + i][tableColumn[column + j].dataIndex] = inArr[j].replace("\r", "")
                            }
                        }
                    }
                }
            }
            setTimeout(() => {
                tableData[row][tableColumn[column].dataIndex] = firstData
            }, 10)
        },

        // 置灰
        disabledDate(current) {
            return current && current < moment(this.lastActualInDate)
        },
        handleChangeDate(date, dateString) {
            const params = {
                id: this.modalData.ordTaskId,
                actualInDate: dateString
            }
            this.actualInDate = dateString
            this.updateTestProDetail(params)
        },
        // 单击复制
        handleCopy(text) {
            var input = document.createElement("input") // 创建input对象
            input.value = text // 设置复制内容
            document.body.appendChild(input) // 添加临时实例
            input.select() // 选择实例内容
            document.execCommand("Copy") // 执行复制
            document.body.removeChild(input) // 删除临时实例
            this.$message.success(`复制成功:${text}`)
        },
        async _handleIsNull(data) {
            let result = 0
            let poinResult = 0
            await data.forEach(v => {
                Reflect.ownKeys(v).forEach(e => {
                    if (e === 'checkData' || e === 'heightType' || e === 'inBoxPosition' || e === 'timeOfFillInnerres' || e === 'timeOfFillInnerres2') return


                    if (e.replaceAll(/[^0-9]/g, "") > 1) {
                        if (v[e] === null || v[e] === "") poinResult++
                        return
                    }
                    if (v[e] === null || v[e] === "") {
                        result++
                    }
                })
            })
            return [result === 0, poinResult === 0,]
        },
        _handleSetBGC(data, num) {
            data.forEach((v, index) => {
                Reflect.ownKeys(v).forEach(e => {
                    const classElement = document.getElementById(`${num}-${e}-${index}`)
                    if (!classElement) return
                    // 中检
                    if (e === "middleCheck") {
                        return (classElement.style.backgroundColor = v.isMiddleClick ? "transparent" : "#ffe9ed")
                    }
                    classElement.style.backgroundColor = v[e] === null || v[e] === "" ? "#ffe9ed" : "transparent"
                })
            })
        },

        // 下载处理
        handleDownload($event, index = 1) {
            exportModel({ ordTaskId: this.modalData.ordTaskId, middleCheckStage: index }).then(res => {
                const fileName = `${this.modalData.folderNo}-${this.modalData.wtrName}-${this.modalData.testName}-${this.details.orderNumber === 1 ? '初始性能检测' : '存储阶段' + (this.details.orderNumber - 1) }${this.middleData[0].middleCheck === "small"
                    ? "-小中检"
                    : this.middleData[0].middleCheck === "large"
                        ? "-大中检"
                        : ""}.xlsx`
                if (res) {
                    downloadfile1(res, fileName)
                }
            })
        },

        /**
         * 弹窗事件
         */
        handleModelCancel() {
            this.$emit("cancel")
        },
        handleCloseModal() {
            this.mgVisible = false
            this.flowId = ""
        },
        /*
         * 提交事件
         */
        handleSubmit() {

            // 校验
            if (!this.actualInDate && this.modalData.taskType.indexOf('last') === -1) return this.$message.warning("进箱时间未填写")
            let result = 0
            this.middleData.forEach(v => {
                if (!v.inBoxPosition) result++
            })
            if (result > 0 && this.modalData.taskType.indexOf('last') === -1) return this.$message.warning("进箱位置未填写")
            
            finishCalLifeTodoTask({ id: this.modalData.ordTaskId }).then(res => {
                if (!res.success) {
                    this.$message.error("错误提示：" + res.message)
                }
                this.$message.success("完成成功")
                this.$emit("submit")
            })
        },
        // 最后一个阶段提交事件
        handleLastSubmit() {
            let result = 0
            let poinResult = 0
            this.lastData.forEach(v => {
                Reflect.ownKeys(v).forEach(e => {
                    if (e === 'checkData' || e === 'heightType' || e === 'inBoxPosition' ) return
                    if (e.replaceAll(/[^0-9]/g, "") > 1) {
                        if (v[e] === null || v[e] === "") poinResult++
                        return
                    }
                    if (v[e] === null || v[e] === "") result++
                })
            })



            if (poinResult !== 0 && result === 0) {
                const that = this
                this.$confirm({
                    title: '尺寸测量数据不完整，是否完成？',
                    onOk() {
                        that.handleSubmit()
                    },
                    onCancel() { },
                    class: "test"
                })

            } else if (result === 0) {
                this.handleSubmit()
            } else {
                const temList = JSON.parse(JSON.stringify(this.lastData))
                this._handleSetBGC(temList, "last")
                return this.$warning({
                    content: "完成待办任务前请将数据填写完成"
                })
            }

        },
        // 弹窗
        // 测试别名复制
        handleAliasCopy() {
            this.isShowDays = true
            this.startDay = ''

        },
        handleModalOk() {
            if (this.startDay) {
                aliasCopy({ ordTaskId: this.modalData.ordTaskId, startDay: Number(this.startDay) }).then(res => {
                    this.handleCopy(res.data)
                })
            }
            this.isShowDays = false
        },
        handleModalCancel() {
            this.isShowDays = false
        },
        positionOnSelect(positionSelectedRowKeys, positionSelectedRows) {
          this.positionSelectedRows = positionSelectedRows
          this.positionSelectedRowKeys = positionSelectedRowKeys
        },
        cellOnSelect(cellSelectedRowKeys, cellSelectedRows) {
          this.cellSelectedRows = cellSelectedRows
          this.cellSelectedRowKeys = cellSelectedRowKeys
        },
        setInboxPosition() {
          if (this.cellSelectedRows.length < 1) {
            this.$message.warning('请至少选择一条数据')
            return
          }
          this.isShowPositions = true
        },
        handlePositionCancel() {
            this.isShowPositions = false
        },
        handlePositionOk() {
          const cellTestCodeList = this.cellSelectedRows.map(item => item.cellTestCode)
          updateInBoxPosition({
            id: this.modalData.ordTaskId,
            inBoxPosition: this.positionSelectedRows[0].rackNumber,
            cellTestCodeList: cellTestCodeList.join(",")
                }).then(res => {
                  this.$message.success('设置成功')
                  this.getTestProDetailByTaskId()
                })
          this.isShowPositions = false
        },
    }
}