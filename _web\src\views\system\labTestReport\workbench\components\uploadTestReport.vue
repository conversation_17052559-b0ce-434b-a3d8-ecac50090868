<template>
  <div class="container relative">
    <a-modal :title="`上传文件`" :visible="true" :width="1000" @cancel="handleCancel" @ok="handleSubmit">
      <template slot="footer">
        <a-button style="display: none">
          取消
        </a-button>
        <a-button style="display: none">
          确定
        </a-button>
      </template>
      <div class="left-container">
        <a-tabs default-active-key="1" @change="callback">
          <a-tab-pane key="1" :tab="form.taskStatus==='审核中'?'审核':form.taskStatus==='批准中'?'批准':'上传'">
            <div>
              <a-spin :spinning="btnLoading">
              <div class="wrapper">
                <!--            <div class="f-16 fw-6 mt10">{{isEdit ? '更新' : '新增'}}信息</div>-->
                <div class="filling-warpper flex mt-20">
                  <div class="filling-box-fileTile">
                    <div class="label">文档标题:</div>
                    <a-input v-model="form.fileTittle" placeholder="请填写文档标题" :disabled="false" @change="updateReportTodoTaskData" style="width:calc(100% - 100px);" />
                  </div>
                  <div class="filling-box">
                    <div class="label">委托单号:</div>
                    <a-input v-model="form.folderNo" placeholder="自动生成" :disabled="true" style="width:calc(100% - 100px);" />
                  </div>
                  <div class="filling-box">
                    <div class="label">检测实验室:</div>
                    <a-input v-model="form.laboratory" placeholder="自动生成" :disabled="true" style="width:calc(100% - 100px);" />
                  </div>
                  <div class="filling-box">
                    <div class="label">产品名称:</div>
                    <a-input v-model="form.productName" placeholder="自动生成" :disabled="true" style="width:calc(100% - 100px);" />
                  </div>
                  <div class="filling-box">
                    <div class="label">技术状态:</div>
                    <a-input v-model="form.technicalStatus" placeholder="自动生成" :disabled="true" style="width:calc(100% - 100px);" />
                  </div>
                  <div class="filling-box">
                    <div class="label">委托人:</div>
                    <a-input v-model="form.wtrName" placeholder="自动生成" :disabled="true" style="width:calc(100% - 100px);" />
                  </div>
                  <div class="filling-box">
                    <div class="label">测试类别:</div>
                    <a-input v-model="form.testTypeCategory" placeholder="自动生成" :disabled="true" style="width:calc(100% - 100px);" />
                  </div>
                  <div class="filling-box">
                    <div class="label">所属分类:</div>
                    <a-input v-model="form.reportType" placeholder="自动生成" :disabled="true" style="width:calc(100% - 100px);" />
                  </div>
                  <div class="filling-box">
                    <div class="label">文档编号:</div>
                    <a-input v-model="form.fileNo" placeholder="请填写文档编号" :disabled="false" @change="updateReportTodoTaskData" style="width:calc(100% - 100px);" />
                  </div>
                  <div class="filling-box">
                    <div class="label">关联单号:</div>
                    <a-textarea v-model="form.referenceNo" placeholder="请填写关联单号" :disabled="false" @change="updateReportTodoTaskData" style="width:calc(100% - 100px);" />
                  </div>
                  <div class="filling-box">
                    <div class="label">文档版本:</div>
                    <a-input v-model="form.fileVersion" placeholder="请填写文档版本" :disabled="true" style="width:calc(100% - 100px);" />
                  </div>
                  <div class="filling-box">
                    <div class="label">修改次数:</div>
                    <a-input type="number" v-model="form.updateTimes" placeholder="请填写修改次数" :disabled="true" style="width:calc(100% - 100px);" />
                  </div>
                  <div class="filling-box">
                    <div class="label">检测结论:</div>
                    <a-select v-model="form.testConclusion" :allow-clear="false" :dropdown-match-select-width="false" @change="updateReportTodoTaskData()" placeholder="请选择检测结论" style="width:calc(100% - 100px);" :disabled="isView">
                      <a-select-option value="合格">合格</a-select-option>
                      <a-select-option value="不合格">不合格</a-select-option>
                      <a-select-option value="不作判定">不作判定</a-select-option>
                    </a-select>
                  </div>
                  <div class="filling-box" style="width: calc(300% / 3);">
                    <div class="label">检测报告:</div>
                    <a-upload-dragger
                      name="file"
                      :data="uploadData"
                      :fileList="fileList"
                      :remove="handleDownloadFileRemove"
                      :showUploadList="true"
                      :multiple="true"
                      :disabled="isView"
                      style="width: calc(100% - 100px);"
                      :customRequest="$event => handleFileUpload($event)"
                      @preview="previewFile"
                    >
                      <p class="ant-upload-drag-icon">
                        <a-icon type="inbox" />
                      </p>
                      <p v-if="!isView" class="ant-upload-text">
                        点击上传检测报告
                      </p>
                    </a-upload-dragger>
                  </div>
                  <div class="filling-box" style="width: calc(300% / 3);">
                    <div class="label">数据处理表:</div>
                    <a-upload-dragger
                      name="file"
                      :data="uploadData"
                      :fileList="dataProcessList"
                      :remove="dataProcessFileRemove"
                      :showUploadList="true"
                      :multiple="true"
                      :disabled="isView"
                      style="width: calc(100% - 100px);"
                      :customRequest="$event => dataProcessFileUpload($event)"
                      @preview="previewFile"
                    >
                      <p class="ant-upload-drag-icon">
                        <a-icon type="inbox" />
                      </p>
                      <p v-if="!isView" class="ant-upload-text">
                        点击上传数据处理表
                      </p>
                    </a-upload-dragger>
                  </div>
                  <div v-if="isView" class="filling-box">
                    <div v-if="form.taskStatus==='审核中'" class="label">审核:</div>
                    <div v-else class="label">批准:</div>
                    <a-select v-model="judgeResult" :dropdown-style="{ align: 'center'}" :dropdown-match-select-width="false" :allow-clear="false" placeholder="请选择审核结果" style="width:calc(100% - 100px);">
                      <a-select-option value="通过">通过</a-select-option>
                      <a-select-option value="驳回">驳回</a-select-option>
                    </a-select>
                  </div>
                </div>

              </div>
              <div class="wrapper" v-if="!isView">
                <div class="filling-warpper flex mt-20">
                  <div class="filling-box">
                    <div class="label"> 审核经办人:</div>
                    <a-select style="width: 150px;z-index: 10" :dropdown-match-select-width="false" @change="chooseAuditManChange" v-model="form.chooseAuditManCode">
                      <a-select-option v-for="cItem in auditArray" :value="cItem.account">{{cItem.name}}</a-select-option>
                    </a-select>
                  </div>
                </div>
              </div>
              <div class="flex mt10" style="justify-content: flex-end;">
                <a-button class="mr10" size="small" type="primary" :loading="btnLoading" @click="handleSubmit" :disabled="false">提交</a-button>
<!--                <a-button class="mr10" size="small" type="primary" @click="editSave" :loading="saveLoading" v-if="!isView">暂存</a-button>-->
                <a-button size="small" @click="handleCancel">取 消</a-button>
              </div>
              </a-spin>
            </div>

          </a-tab-pane>
        </a-tabs>
      </div>
<!--      <div class="right-container">-->
<!--        <a-tabs default-active-key="1" @change="callback">-->
<!--          <a-tab-pane key="1" tab="文档信息">-->
<!--            <div class="filling-warpper flex">-->
<!--              <div>-->
<!--                <div class="tip-box-content">-->
<!--                  <div class="tip-label">录入者:&nbsp;&nbsp;  {{ rightForm.createUser }}</div>-->
<!--                </div>-->
<!--                <div class="tip-box-content">-->
<!--                  <div class="tip-label">所属部门:&nbsp;&nbsp; {{ rightForm.dept }}</div>-->
<!--                </div>-->
<!--                <div class="tip-box-content">-->
<!--                  <div class="tip-label">录入时间:&nbsp;&nbsp;{{ rightForm.createTime }}</div>-->
<!--                </div>-->
<!--                <div class="tip-box-content">-->
<!--                  <div class="tip-label">录入编号:&nbsp;&nbsp;<span class="grey">{{ rightForm.createNo }}</span></div>-->
<!--                </div>-->
<!--                <div class="tip-box-content">-->
<!--                  <div class="tip-label">文档状态:&nbsp;&nbsp;<span class="grey">{{ rightForm.fileStatus}}</span></div>-->
<!--                </div>-->
<!--              </div>-->

<!--            </div>-->
<!--          </a-tab-pane>-->
<!--        </a-tabs>-->

<!--      </div>-->
      <a-drawer
        :bodyStyle="{ height: '100%' }"
        placement="right"
        :closable="false"
        width="80%"
        :visible="visible3"
        @close="visible3 = false"
      >
        <iframe :src="pdfUrl + '#view=FitH,top&'" width="100%" height="100%"></iframe>
      </a-drawer>
    </a-modal>
  </div>

</template>

<script>

  import {getList,add,getById,edit,getProducts,summitJIRA} from "@/api/modular/system/standardManage"
import {getUserLists} from "@/api/modular/system/userManage"
  import { sysFileInfoUpload } from '@/api/modular/system/fileManage'

  import pbiPreview from '@/components/pageTool/components/pbiPreview.vue'
  //
  // import 'quill/dist/quill.core.css'
  // import 'quill/dist/quill.snow.css'
  // import 'quill/dist/quill.bubble.css'
  // import { quillEditor, Quill } from 'vue-quill-editor'
  //
  // // 设置调整图片大小
  // import ImageResize from "quill-image-resize-module";
  // Quill.register("modules/imageResize", ImageResize);

  // import { mixin } from "@/views/system/index/mixin/index"

  import Vue from "vue";
  import { mapGetters } from 'vuex'
  import {
    updateReportFile,
    updateReportTodoTaskData, uploadReportSubmit
  } from "@/api/modular/system/testProgressManager";
  import moment from "moment";
  import { STable } from "@/components";
  import { formatDate } from "@/utils/format";
  import axios from "axios";
  // import {getDepartmentOptionList, getJiraOptionList} from "@/api/modular/system/qualityManage";
  // import {getDictVue} from "@/utils/util";

  export default {
    props: {
      options: {
        type: Array,
        default: () => [],
      },
      modalData: {
        type: Object,
        default: {}
      },
    },
    components: {
      STable,
      // quillEditor,
      pbiPreview
    },
    // mixins: [mixin],
    data() {
      return {
        isView: false,
        visible3: false,
		    fileList:[],
        dataProcessList:[],
        uploadData: { bucket: 'testReportWorkbench' },
        id: '',
        btnLoading: false,
        saveLoading: false,
        isEdit: false,
        previewVisible: false,
        auditArray: [],
        judgeResult:'',
        form: {
          id: '',
          fileTittle: '',
          folderNo: '',
          laboratory: '',
          productName: '',
          technicalStatus: '',
          wtrName:'',
          testTypeCategory:'',
          reportType: '',
          fileNo:'',
          referenceNo:'',
          fileVersion:'',
          updateTimes:'',
          testConclusion:'',
          chooseAuditMan:'',
          chooseAuditManCode:'',
        },
        versionList:['A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z'],
        productDepartmentList:[{label:'动力圆柱电池研究所',value:'动力圆柱电池研究所'},{label:'方形电池研究所',value:'方形电池研究所'},{label:'新型电池研究所',value:'新型电池研究所'},{label:'V型圆柱电池研究所',value:'V型圆柱电池研究所'},{label:'动力电池研究所',value:'动力电池研究所'},{label:'储能电池研究所',value:'储能电池研究所'}],

        headers: {
          Authorization: 'Bearer '+ Vue.ls.get('Access-Token')
        },
        products: [],
        editorOption: {
          // 主题
          theme: "snow",
          modules: {
            imageResize: { //设置图片大小, 也可以使用"imageResize:true"，官网上采用的是下面的配置方式
              displayStyles: {
                backgroundColor: 'black',
                border: 'none',
                color: 'white'
              },
              modules: ['Resize', 'DisplaySize', 'Toolbar']
            },
            toolbar: {
              container: [
                ["bold", "italic", "underline", "strike"], // 加粗 斜体 下划线 删除线 -----['bold', 'italic', 'underline', 'strike']
                [{ 'header': 1 }, { 'header': 2 }],
                [{ script: "sub" }, { script: "super" }], // 上标/下标-----[{ script: 'sub' }, { script: 'super' }]
                [{ indent: "-1" }, { indent: "+1" }], //缩进
                [{ size: ['small', false, 'large', 'huge'] }], // 配置字号
                [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题-----[{ header: [1, 2, 3, 4, 5, 6, false] }]
                [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色-----[{ color: [] }, { background: [] }]
                [{ font: [] }], //显示字体选择
                [{ align: [] }], // 对齐方式-----
                ["clean"], // 清除文本格式-----
                ['link', 'image', 'video'], // 链接、图片、视频-----
              ],
              handlers: {
                // 重写点击组件上的图片按钮要执行的代码
                'image': function (value) {
                  document.querySelector('.quill-upload .ant-btn-link').click()
                }
              }
            },
          },
          placeholder: "例：G26E-V1.1 B样产品规范首板发行。",
        },
        // fileStatusList: ['草稿','待审','驳回','废弃','发布'],
        fileStatusList: [],
        firstLoadForDate: true,
        pdfUrl: '',
        userList: [],
        rightForm: {
          dept: '',
        },
        projectDepartmentList:[],
        techStatusList: [],
        // fileTypeList: ['产品规范','试验大纲'],
        fileTypeList:[],
      }
    },
    created() {
      this.auditArray = [{ name: "梁丽娜", account: "114377" }]
      if (this.modalData.laboratoryId == 'HZ_YJ_DL_CS') {
        this.auditArray = [{ name: "黄辉宁", account: "041092" }]
      }
      this.form.id = this.modalData.id
      this.form.fileTittle = this.modalData.fileTittle
      this.form.folderNo = this.modalData.folderNo
      this.form.laboratory = this.modalData.laboratory
      this.form.productName = this.modalData.productName
      this.form.technicalStatus = this.modalData.technicalStatus
      this.form.wtrName = this.modalData.wtrName
      this.form.testTypeCategory = this.modalData.testTypeCategory
      this.form.reportType = this.modalData.reportType
      this.form.fileNo = this.modalData.fileNo
      this.form.referenceNo = this.modalData.referenceNo
      this.form.fileVersion = this.modalData.fileVersion
      this.form.updateTimes = this.modalData.updateTimes
      this.form.testConclusion = this.modalData.testConclusion
      this.form.chooseAuditMan = this.modalData.chooseAuditName ? this.modalData.chooseAuditName : this.auditArray[0].name
      this.form.chooseAuditManCode = this.modalData.chooseAuditCode ? this.modalData.chooseAuditCode : this.auditArray[0].account
      this.form.taskStatus = this.modalData.taskStatus
      if (this.modalData.attachment) {
        let file = JSON.parse(this.modalData.attachment)
        if (file.length > 0) {
          this.form.fileId = file[0].id;
          this.form.fileName = file[0].name;
          this.fileList = file
        }
      }
      if (this.modalData.dataProcessTable) {
        let dataProcess = JSON.parse(this.modalData.dataProcessTable)
        if (dataProcess.length > 0) {
          this.form.dataProcessFileId = dataProcess[0].id;
          this.form.dataProcessFileName = dataProcess[0].name;
          this.dataProcessList = dataProcess
        }
      }
      this.isView = this.modalData.isView
    },
    computed: {
      ...mapGetters(['userInfo']),
    },
    methods: {
      previewFile(record){

        if(!record.id){
          const urlObj = new URL('https://example.com' + record.url); // 需要完整的URL才能创建URL对象
          const params = new URLSearchParams(urlObj.search);
          record.id=params.get('id');
        }

        // 如果是可预览的
        if(['.xbm','.tif','.pjp','.svgz','.jpg','.jpeg','.ico','.tiff','.gif','.svg','.jfif','.webp','.png','.bmp','.pjpeg','.avif','.pdf'].some(someItem => { return typeof record.name === 'string' && record.name.indexOf(someItem) !== -1})){
          this.visible3 = true
          this.pdfUrl = '/api/sysFileInfo/preview?Authorization=Bearer '+Vue.ls.get('Access-Token')+'&id=' + record.id+ "#navpanes=0"
        }else{
          // 不可预览就下载
          const a = document.createElement('a')
          a.style.display = 'none'
          a.href = '/api/sysFileInfo/preview?Authorization=Bearer '+Vue.ls.get('Access-Token')+'&id=' + record.id + "#navpanes=0"
          a.download = record.name
          // a.download = this.getTrueFileName(record.name)
          a.click()
        }
      },

      handleFileUpload(info) {
        const formData = new FormData()
        formData.append('file', info.file)
        axios.post('/api/sysFileInfo/minioUpload', formData, {
          headers: {
            Authorization: 'Bearer ' + Vue.ls.get('Access-Token'),
          }
        }).then((res) => {
          let uploadResult = res.data
          if (uploadResult.success) {
            // var fileName = this.getTrueFileName(info.file.name)
            var fileName = info.file.name
            this.$message.success('上传成功')
            this.pdfUrl = '/api/sysFileInfo/preview?Authorization=Bearer '+Vue.ls.get('Access-Token')+'&id=' + uploadResult.data
            this.fileList = [{
              uid: info.file.uid,
              id: uploadResult.data,
              name: fileName,
              status: 'done',
              type: info.file.type,
              url: this.pdfUrl
            }]
            this.form.fileId = uploadResult.data;
            this.form.fileName = fileName;
            let update = {}
            update.id = this.form.id
            update.addAttachList = this.fileList.length > 0 ? this.fileList : []
            updateReportFile(update, "attachment").then(res => {
              if (!res.success) {
                this.$message.error("上传失败：" + res.message)
              } else {
                this.$emit("refresh")
              }
            })
          } else {
            this.$message.error('上传失败：' + res.message)
          }
        })
      },
      getTrueFileName(oldFileName) {
        var fileName = this.form.fileNo
        var lastIndexOf = oldFileName.lastIndexOf('.')
        if (lastIndexOf >= 1) {
          fileName += '.' + oldFileName.substring(lastIndexOf + 1)
        }
        return fileName
      },
      // addFileManually(file) {
      //   // 手动添加文件到 fileList
      //   this.pdfUrl = '/api/sysFileInfo/preview?Authorization=Bearer '+Vue.ls.get('Access-Token')+'&id=' + file.id
      //   const newFile = {
      //     uid: -1, // 唯一标识符
      //     name: file.name, // 文件名
      //     status: 'done', // 文件状态
      //     response: '文件上传成功', // 服务器响应内容
      //     url: this.pdfUrl, // 下载链接
      //   };
      //   this.fileList = [newFile]; // 设置 fileList
      // },
      dataProcessFileUpload(info) {
        const formData = new FormData()
        formData.append('file', info.file)
        axios.post('/api/sysFileInfo/minioUpload', formData, {
          headers: {
            Authorization: 'Bearer ' + Vue.ls.get('Access-Token'),
          }
        }).then((res) => {
          let uploadResult = res.data
          if (uploadResult.success) {
            console.log('')
            this.$message.success('上传成功')
            this.pdfUrl = '/api/sysFileInfo/preview?Authorization=Bearer '+Vue.ls.get('Access-Token')+'&id=' + uploadResult.data
            this.dataProcessList = [{
              uid: info.file.uid,
              id: uploadResult.data,
              name: info.file.name,
              status: 'done',
              type: info.file.type,
              url: this.pdfUrl
            }]
            this.form.dataProcessFileId = uploadResult.data;
            this.form.dataProcessFileName = info.file.name;
            let update = {}
            update.id = this.form.id
            update.addAttachList = this.dataProcessList.length > 0 ? this.dataProcessList : []
            updateReportFile(update, "dataProcessTable").then(res => {
              if (!res.success) {
                this.$message.error("上传失败：" + res.message)
              } else {
                this.$emit("refresh")
              }
            })
          } else {
            this.$message.error('上传失败：' + res.message)
          }
        })
      },
      dataProcessFileRemove (file) {
        const index = this.dataProcessList.findIndex(item => item.id == file.id)
        const newFileList = this.dataProcessList.slice()
        newFileList.splice(index, 1)
        this.dataProcessList = newFileList
        let update = {}
        update.id = this.form.id
        update.addAttachList = this.dataProcessList.length > 0 ? this.dataProcessList : []
        updateReportFile(update, "dataProcessTable").then(res => {
          if (!res.success) {
            this.$message.error("上传失败：" + res.message)
          } else {
            this.form.dataProcessFileId = null
            this.form.dataProcessFileName = null
            this.$emit("refresh")
          }
        })
      },
      handleDownloadFileRemove (file) {
        const index = this.fileList.findIndex(item => item.id == file.id)
        const newFileList = this.fileList.slice()
        newFileList.splice(index, 1)
        this.fileList = newFileList
        let update = {}
        update.id = this.form.id
        update.addAttachList = this.fileList.length > 0 ? this.fileList : []
        updateReportFile(update, "attachment").then(res => {
          if (!res.success) {
            this.$message.error("上传失败：" + res.message)
          } else {
            this.form.fileId = null
            this.form.fileName = null
            this.$emit("refresh")
          }
        })
      },
      editSave() {
        this.$message.success("暂存成功！")
        // // 校验
        // if (!this._handleVerify(this.form)[0]) return this.$message.error(this._handleVerify(this.form)[1])
        //
        // this.form.productName = this.products.find(item => item.issueId == this.form.issueId)?.productProjectName
        // this.form.productStatus = this.products.find(item => item.issueId == this.form.issueId)?.productStateName
        // this.form.productStatusId = this.products.find(item => item.issueId == this.form.issueId)?.productState
        // //用户处理
        // if(this.form.accountRemark){
        //   const account_split = this.form.accountRemark.split("   ");
        //   // 提取 this.form.fileAuthor
        //   this.form.fileAuthorId = account_split[0];
        //   // 提取 this.form.fileAuthorId
        //   this.form.fileAuthor = account_split[1];
        // }
        //
        // // 找到第一个空格的位置
        // const account_split2 = this.form.accountCheck.split("   ");
        // // 提取 this.form.fileAuthor
        // this.form.auditUser = account_split2[0];
        // // 提取 this.form.fileAuthorId
        // this.form.auditUserName = account_split2[1];
        // if(this.form.fileId){
        //   this.form.testVerifyFileUrl =  process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id='+ this.form.fileId ;
        //   // this.form.testVerifyFileUrl =  'http://10.1.3.189:83/sysFileInfo/preview?id='+ this.form.fileId ;
        // }
        //
        // console.log("this.form",this.form)
        // this.saveLoading = true
        // if(this.isEdit){
        //   //编辑
        //   edit(this.form).then(res => {
        //     console.log(res,"res");
        //     this.$message.success("操作成功！")
        //     window.close()
        //     window.open(`/lab_standardManage`)
        //   }).finally(()=>{
        //     this.saveLoading = false
        //   })
        // }else{
        //   //新增
        //   add(this.form).then(res => {
        //     console.log(res,"res");
        //     this.$message.success("操作成功！")
        //     window.close()
        //     window.open(`/lab_standardManage`)
        //   }).finally(()=>{
        //     this.saveLoading = false
        //   })
        // }
      },
      chooseAuditManChange(account) {
        let audit = this.auditArray.find((item) => item.account === account);
        this.form.chooseAuditMan = audit.name
        this.form.chooseAuditManCode = audit.account
        this.updateReportTodoTaskData()
      },
      handleSubmit(){
        if (this.isView) {
          // if (this.form.taskStatus === '审核中') { // 审核
          //   if (!this.judgeResult) {
          //     this.$message.error("未选择审核结果")
          //     return
          //   }
          //   this.auditOrApproveSubmit('批准中')
          // } else if (this.form.taskStatus === '批准中') { // 批准
          //   if (!this.judgeResult) {
          //     this.$message.error("未选择批准结果")
          //     return
          //   }
          //   this.auditOrApproveSubmit('已完成')
          // }
        } else { // 上传
          if (!this._handleVerify(this.form)[0]) return this.$message.error(this._handleVerify(this.form)[1])
          if (!this.form.chooseAuditManCode || !this.form.chooseAuditMan) {
            this.$message.error("未选择审核经办人")
            return
          }
          this.submitReportTodoTaskData()
        }
      },
      handleCancel() {
        this.$emit("cancel")
      },
      callback(key) {
        console.log(key);
      },
      updateReportTodoTaskData() {
        updateReportTodoTaskData({
          id: this.form.id,
          testConclusion: this.form.testConclusion,
          fileTittle: this.form.fileTittle,
          fileNo: this.form.fileNo,
          referenceNo: this.form.referenceNo,
          chooseAuditName: this.form.chooseAuditMan,
          chooseAuditCode: this.form.chooseAuditManCode
        }).then(res => {
          if (!res.success) {
            return this.$message.error("错误提示：" + res.message)
          } else {
            this.$emit("refresh")
          }
        })
      },
      submitReportTodoTaskData() {
        // 测试使用, 上线需注释掉
        // Vue.ls.set("token", "123456")
        this.btnLoading = true
        uploadReportSubmit({
          id: this.form.id,
          // taskStatus: "审核中",
          reportFileUrl: process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id=' + this.form.fileId,
          auditName: this.form.chooseAuditMan,
          auditCode: this.form.chooseAuditManCode,
        }).then(res => {
          if (!res.success) {
            return this.$message.error("错误提示：" + res.message)
          } else {
            this.btnLoading = false
            this.$emit("refresh")
            this.$message.success("提交成功！")
            this.handleCancel()
          }
        })
      },
      // auditOrApproveSubmit(nextTaskStatus) {
      //   let param;
      //   if (this.form.taskStatus === '审核中' && this.judgeResult === '通过') { // 审核通过
      //     param = {
      //       id: this.form.id,
      //       currentTaskStatus: this.form.taskStatus,
      //       approveName: "夏冬冬",
      //       approveCode: "059166",
      //       taskStatus: nextTaskStatus
      //     }
      //   } else {
      //     param = {
      //       id: this.form.id,
      //       currentTaskStatus: this.form.taskStatus,
      //       taskStatus: this.judgeResult === '通过' ? nextTaskStatus : '驳回'
      //     }
      //   }
      //   updateReportTodoTaskData(param).then(res => {
      //     if (!res.success) {
      //       return this.$message.error("错误提示：" + res.message)
      //     } else {
      //       this.$emit("refresh")
      //       this.$message.success("提交成功！")
      //       this.handleCancel()
      //     }
      //   })
      // },
      _handleVerify(form) {
        const required = [
          { dataIndex: 'testConclusion', message: '请选择检测结论' },
          { dataIndex: 'fileId', message: '请上传检测报告' },
          { dataIndex: 'fileTittle', message: '请填写文档标题' },
          { dataIndex: 'fileNo', message: '请填写文档编号' },
        ]

        for (let i = 0; i < required.length; i++) {
          if(form[required[i].dataIndex] === 0){
            continue
          }
          if (!form[required[i].dataIndex]) {
            return [false, required[i].message]
          }
        }

        return [true, '']

      },
      // fileTitleEdit(){
      //   this.form.fileTitle = '';
      //   let productName = this.products.find(item => item.issueId == this.form.issueId)?.productProjectName
      //   if(productName){
      //     this.form.fileTitle = this.form.fileTitle + productName;
      //   }
      //   this.form.fileTitle = this.form.fileTitle + " "
      //   if(this.form.techStatus){
      //     this.form.fileTitle = this.form.fileTitle + this.form.techStatus;
      //   }
      //   if(this.form.fileType){
      //     this.form.fileTitle = this.form.fileTitle + this.form.fileType;
      //   }
      //   this.form.fileTitle = this.form.fileTitle + "_"
      //   if(this.form.fileNo){
      //     this.form.fileTitle = this.form.fileTitle + this.form.fileNo;
      //   }
      //   this.form.fileTitle = this.form.fileTitle + " "
      //   if(this.form.fileVersion){
      //     this.form.fileTitle = this.form.fileTitle +  this.form.fileVersion;
      //   }
      //   this.form.fileTitle = this.form.fileTitle + "版"
      // },
    },
    // watch: {
    //   'form.issueId':{
    //     handler(newVal, oldVal) {
    //       this.fileTitleEdit()
    //     },
    //     immediate: true,
    //     deep: true
    //   },
    //   'form.techStatusId':{
    //     handler(newVal, oldVal) {
    //       const find = this.techStatusList.find(e => e.id == newVal)
    //       if(find){
    //         this.form.techStatus = find.customvalue
    //         this.fileTitleEdit()
    //       }
    //     },
    //     immediate: true,
    //     deep: true
    //   },
    //   'form.fileTypeId':{
    //     handler(newVal, oldVal) {
    //       const find = this.fileTypeList.find(e => e.id == newVal)
    //       if(find){
    //         this.form.fileType = find.customvalue
    //         this.fileTitleEdit()
    //       }
    //     },
    //     immediate: true,
    //     deep: true
    //   },
    //   'form.fileNo':{
    //     handler(newVal, oldVal) {
    //       this.fileTitleEdit()
    //     },
    //     immediate: true,
    //     deep: true
    //   },
    //   'form.fileVersion':{
    //     handler(newVal, oldVal) {
    //       this.fileTitleEdit()
    //     },
    //     immediate: true,
    //     deep: true
    //   },
    //   // 监听 form.startTime 的变化
    //   'form.startTime': {
    //     handler(newVal) {
    //       if (newVal) {
    //         if(!this.isEdit || !this.firstLoadForDate){
    //           if(!this.form.loseTime){
    //             const startTimeDate = new Date(newVal);
    //             const loseTimeDate = new Date(startTimeDate);
    //             loseTimeDate.setFullYear(loseTimeDate.getFullYear() + 3);
    //             this.form.loseTime = loseTimeDate.toISOString().split('T')[0];
    //           }
    //         }else{
    //           this.firstLoadForDate = false;
    //         }
    //       }
    //     },
    //     immediate: true
    //   },
    // },
  }
</script>
<style lang="less" scoped>
  @import '/src/style/small-ant-modal.less';
  @import '~@/style/general.css';

  .container {
    //background: #fff;
    width: calc(100vw - 80px);
    height: 100%;
    overflow-y: scroll;

    //margin: 20px 20px 20px ;
    padding: 10px 100px 10px 10px;
  }

  .left-container {
    background: #fff;
    height: auto;
    width: 100%;
    position: absolute;
    left: 0px;
    top: 0px;
    padding: 10px 37px 10px 0px;
    /* 移除 height 属性 */
    overflow-y: hidden; /* 确保没有滚动条 */
  }
  .right-container{
    width: 20%;
    height: 212px;
    position: absolute;
    left: calc(74% + 40px);
    top: 10px;
    background: #fff;
  }

  .filling-warpper {
    flex-wrap: wrap;
  }

  .filling-box {
    width: fit-content;

    display: flex;
    align-items: center;
    margin-bottom: 10px;

    width: calc(100% / 3);
    input[type=text], input[type=password], input[type=email], input[type=number], input[type=tel], textarea {
      border: 1px solid #d9d9d9;
    }

  }

  .filling-box-fileTile{
    width: fit-content;

    display: flex;
    align-items: center;
    margin-bottom: 10px;

    width: 100%;

  }

  .label {
    margin-right: 5px;
    width: 100px;
    text-align: right;
    font-size: 14px;
  }

  .filling-box .label {
    margin-right: 5px;
    width: 100px;
    text-align: right;
    font-size: 14px;
  }

  .filling-box .plus-block {
    width: 100px;
    height: 100px;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
  }

  .filling-box .plus-block img {
    width: 100%;
    height: 100%;
  }

  .tip-box-title{
    width: fit-content;

    display: flex;
    align-items: center;
    margin-bottom: 10px;

    width: 100%;
  }

  .tip-box-content{
    width: fit-content;
    display: flex;
    align-items: center;
    width: 100%;

    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 12px;
    font-family: Source Han Sans SC-Regular;
    font-weight: normal;
    white-space: nowrap;
    line-height: 16px;
    margin: 0px 0px 10px 20px;
  }


  .tip-label {
    width: 60px;
    text-align: left;
  }

  .result-wrapper {
    background: #fff;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
	  position: fixed;
    z-index: 1;
  }

  .step{
    width: 50%;
    position: relative;
    left: 25%;
  }

  .grey{
    color: rgba(191, 191, 191, 1);
    font-size: 12px;
    font-family: Source Han Sans SC-Regular;
    font-weight: normal;
  }


</style>