
/deep/ .ant-table-thead > tr > th {
  padding: 2px !important;
  font-size: 13px !important;

}

/deep/ .ant-table-tbody > tr > td {
  padding: 0px !important;
  height: 32px !important;
  font-size: 12px !important;
}

/deep/ .ant-calendar-picker-icon {
  display: none;
}

/deep/ .ant-calendar-picker-input.ant-input {
  color: black;
  font-size: 12px;
  border: 0;
  text-align: center;
  padding: 0;
}

.red {
  background-color: #ed0000;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.yellow {
  background-color: #ffc000;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.grey {
  background-color: rgba(223, 223, 223, 0.25);
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.ant-modal-body {
  padding: 0;
}

/deep/ .ant-btn > i,
/deep/ .ant-btn > span {
  display: flex;
  justify-content: center;
}

/deep/ .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
  color: #fff;
  background: #1890ff;
}

.green {
  background-color: #58a55c;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/deep/ #table1 > div > div > div > div > div > div > table > thead {
  height: 64px;
}

/deep/ #table1 > .ant-table-wrapper > div > div > ul {
  display: none;
}

/deep/ .ant-table-pagination.ant-pagination {
  float: right;
  margin:2px 0 0;
  font-size: 12px;
}

/deep/ .ant-select-selection-selected-value {
  font-size: 12px;
}

.float {
  padding-bottom: 10px;
}

.float1 {
  width: 12%;
  float: left;
  margin-right: 10px;
  text-align: center;
}

/deep/ .ant-checkbox-group-item {
  display: block;
  width: 100%;
  text-align: left;
}

.title {
  font-size: large;
  margin-bottom: 20px;
}

.numTitle {
  font-size: xx-large;
}

/deep/ .ant-table-footer {
  padding: 0;
}

/deep/ .ant-table-row-expand-icon {
  margin-right: 0px;
}

.wrapper {
  height: 100vh;
  overflow-y: scroll; 
  padding: 10px;
  margin: 0 0 0 -40px;
  background-color: #f0f2f5;
}

.head_title {
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.head_title::before {
  width: 8px;
  background: #1890ff;
  margin-right: 8px;
  content: "\00a0"; //填充空格
}

.export-btn {
  position: fixed;
  bottom: 10px;
  right: 10px;
  width: 15%;
}

.all-wrapper {
  padding: 0 0 10px;
  display: flex;
  justify-content: space-between;
}

.btn-wrap {
  text-align: right;
}

.example-icon {
  width: 20px;
  height: 20px;
  color: #1890ff;
  vertical-align: middle;
  margin-left: 3px;
  margin-top: 3px;
}

// 通用
.mt2{
  margin-top: 2px;
}
.mt10 {
  margin-top: 10px;
}

.mr5 {
  margin-right: 5px;
}

.mb5 {
  margin-bottom: 5px;
}

.mb10 {
  margin-bottom: 10px;
}

.mr10 {
  margin-right: 10px;
}

.ml10 {
  margin-left: 10px;
}

.title-line {
  width: 8px;
  height: 30px;
  background: #1890ff;
  border-radius: 2px;
  margin-right: 8px;
  content: "\00a0"; //填充空格

  position: absolute;
  top: 8px;
  left: -4px;
}

.right-top-div {
  position: absolute;
  top: 48px;
  right: 10px;
  height:43.5px;
  display: flex;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-sb-center-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.strong {
  background-size: 100% 100%;
  width: 210px;
  height: 25px;
  color: #333;
  display: flex;
  align-items: center;
  padding: 5px;
}

.block {
  height: fit-content;
  padding: 10px;
  background: #fff;
  border-radius: 10px;
  //box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
  position: relative;
}

.left-content {
  width: calc(595px + 20px);
  margin-right: 10px;
}

.align-center {
  display: flex;
  align-items: center;
}

/deep/ .align-center .anticon {
  margin-left: 4px;
}

.right-content {
  width: calc(100% - 595px - 10px - 10px - 10px);
}

.right-content .all-checkbox {
  padding-bottom: 5px;
  margin-bottom: 5px;
  border-bottom: 1px solid #e9e9e9;
}

.normal-btn {
  padding: 5px 10px;
  color: #fff;
  background-color: #1890ff;
  letter-spacing: 2px;
  cursor: pointer;
}

.footer-btn {
  width: 100%;
  height: 32px;
  border: 1px solid #e8e8e8;
  background: #fff;
  color: #999;
  font-size: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.footer-btn:hover {
  color: #1890ff;
}

// 组件
/deep/ .ant-steps {
  padding: 15px 50px;
}

/deep/ .right-content .ant-table-body {
  border: 1px solid #e8e8e8;
  max-height: 382px !important; /* 设置容器最大高度 */
  overflow-y: auto; /* 垂直滚动条 */
  overflow-x: scroll; /* 强制显示水平滚动条 */
  margin: 0;
}

/deep/ .all-wrapper .ant-table-thead {
  position: sticky;
  top: 0;
  z-index: 12;
}

/deep/ .right-content .ant-input {
  border: none;
}

/deep/ .ant-checkbox-group {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

/deep/ .ant-checkbox-group-item {
  font-size: 12px;
  width: 23%;
}

/deep/ .ant-radio-inner {
  top: 1px;
  left: 1px;
}

/deep/ .ant-table-body::-webkit-scrollbar {
  height: 10px;
  width: 5px;
}

/deep/ .ant-table-body::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;

  background: #dddbdb;
}

/deep/ .ant-table-body::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: #f1f1f1;
}

/deep/ .ant-select-selection__rendered {
  margin-right: 0px;
}

/deep/ .ant-form-item {
  margin-bottom: 0;
}

/deep/ .ant-popover-buttons {
  display: flex !important;
  flex-direction: column !important;
  margin-bottom: 15px;
}

.tips {
  color: #1890ff;
}

.button-tips {
  display: flex;
  flex-direction: column;
}

/deep/ .ant-select-selection-selected-value {
  width: 80px;
}
.reveal-text-opacity{
  /* font-size: 14px; */
  font-family: 'Times New Roman';
  opacity: 0;
  padding: 0;
  margin: 0;
  line-height: 1;
}

/* 操作栏 */
.action-bar{
  font-size: 20px;
  color: #333;

  position:fixed ;
  bottom: 20px;
  left: 20px;
  z-index: 10;
  padding: 10px 15px;
  border-radius: 5px;
  backdrop-filter: blur(6px);
  background-color:rgba(238, 238, 238,.3);
  cursor: pointer;
  box-shadow: 2px 2px 4px 0 #dee4e6,-2px -2px 4px 0 #fff;
}
/deep/.icon{
  position: unset;
}

/* 缩略图 */
.flex-column-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  cursor: pointer;
}

.thumbnail-content .thumbnail-block {
  width: 150px;
  height: 100px;
  margin-right: 6px;
  background: #ccc;
  border-radius: 10px;

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.thumbnail-content .title {
  font-size: 12px;
  margin-bottom: 2px;
}

/deep/ .right-content .ant-input[disabled] {
  background-color: white;
}
/deep/ .right-content .ant-input-number-disabled  {
  font-size: 12px;
  text-align: center;
  border: 0;
  padding: 0;
  background: white;
  color: black;
  height: 32px;
}

/deep/ .right-content .ant-input-number-input{
  font-size: 12px;
  text-align: center;
  border: 0;
  padding: 0;
  background: white;
  color: black;
  height: 32px;
}

/deep/ .right-content .ant-input-number{
  font-size: 12px;
  text-align: center;
  border: 0;
  padding: 0;
  background: white;
  color: black;
  height: 32px;
}