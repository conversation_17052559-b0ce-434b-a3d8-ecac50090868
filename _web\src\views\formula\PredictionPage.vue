<template>
  <div class="prediction-page">
    <prediction-wrapper />
  </div>
</template>

<script>
import PredictionWrapper from '@/components/prediction/PredictionWrapper.vue';

export default {
  name: 'PredictionPage',
  components: {
    PredictionWrapper
  }
};
</script>

<style scoped>
.prediction-page {
  padding: 10px;
  height: 100%;
  overflow: auto;
  max-width: 100%;
  margin: 0 auto;
}
</style>