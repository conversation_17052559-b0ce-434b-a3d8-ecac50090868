import { axios } from '@/utils/request'
import service from "@/utils/requestjira"

export const getReviewListData = (params) => {
  return axios({
    url: '/topic/review_list',
    method: 'post',
    data: params
  })
}


export const getChartProjects = (params) => {
  return axios({
    url: '/chart/projects',
    method: 'get',
    params: params
  })
}

export const transitionReview = (params) => {
  return axios({
    url: '/topic/transition_review',
    method: 'post',
    data: params
  })
}


export function updateReview(parameter) {
  return axios({
      url: '/topic/update_review',
      method: 'post',
      data: parameter
  })
}

export function updateReviewUncheck(parameter) {
  return axios({
      url: '/topic/update_review_uncheck',
      method: 'post',
      data: parameter
  })
}


export const updateTransition = (params) => {
  return axios({
    url: '/topic/update_transition',
    method: 'get',
    params: params
  })
}

export const getCateTree = (params) => {
  return axios({
    url: '/topic/cate_tree',
    method: 'get',
    params: params
  })
}


export function addOrUpdateReview(parameter) {
  return axios({
      url: '/topic/add_or_update_review',
      method: 'post',
      data: parameter
  })
}


export const getChartPassTopics = (params) => {
  return axios({
    url: '/chart/pass_topics',
    method: 'get',
    params: params
  })
}

export const getAllProjects = (params) => {
  return axios({
    url: '/topic/projects',
    method: 'post',
    data: params
  })
}

export const getAllResult = (params) => {
  return axios({
    url: '/topic/results',
    method: 'post',
    data: params
  })
}

export const getAvgProjects = (params) => {
  return axios({
    url: '/topic/avg',
    method: 'post',
    data: params
  })
}

export const getEvoProjects = (params) => {
  return axios({
    url: '/topic/evo',
    method: 'post',
    data: params
})
}

export const getPassProjects = (params) => {
  return axios({
    url: '/topic/pass',
    method: 'post',
    data: params
})
}

export const getLevelProjects = (params) => {
  return axios({
    url: '/topic/level',
    method: 'post',
    data: params
})
}

export const getTopicReviewMonthList = (params) => {
  return axios({
    url: '/topicReviewMonth/list',
    method: 'post',
    data: params
  })
}

export const getTopicReviewMonthCheckedList = (params) => {
  return axios({
    url: '/topicReviewMonth/checkedList',
    method: 'post',
    data: params
  })
}

export const getTopicReviewMonthUncheckList = (params) => {
  return axios({
    url: '/topicReviewMonth/uncheckList',
    method: 'post',
    data: params
  })
}


export const reviewFiles = (params) => {
  return axios({
    url: '/topic/review_file',
    method: 'get',
    params: params
  })
}


export const cancelTransition = (params) => {
  return axios({
    url: '/topic/cancel_transition',
    method: 'get',
    params: params
  })
}

export function previewFile (parameter) {
  return axios({
    url: '/topic/preview',
    method: 'get',
    params: parameter,
    responseType: 'blob | ArrayBuffer | Uint8Array'
  })
}


export function getPlatformTopicsFromView (parameter) {
  return axios({
    url: '/platformTopics/list',
    method: 'post',
    data: parameter,
  })
}

export function oaTodoPushTopicReview (parameter) {
  return axios({
    url: '/oaTodo/topicReview',
    method: 'post',
    data: parameter,
  })
}

export function oaTodoDonePushTopicReview (parameter) {
  return axios({
    url: '/oaTodo/done/topicReview',
    method: 'post',
    data: parameter,
  })
}

export function oaTodoCancelPushTopicReview (parameter) {
  return axios({
    url: '/oaTodo/cancel/topicReview',
    method: 'post',
    data: parameter,
  })
}

 export const getPassNumSixMonth=(params)=>{
  return axios({
    url: '/topic/getPassNumSixMonth',
    method: 'get'
  })
}

export const getAllProject=(params)=>{
  return axios({
    url: '/topic/getAllProject',
    method: 'get'
  })
}