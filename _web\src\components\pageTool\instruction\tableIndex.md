- [1. 页面使用案例](#1-页面使用案例)
  - [1.1 tabs页面使用案例](#11-tabs页面使用案例)
    - [1.1.1 代码示例](#111-代码示例)
    - [1.1.2 效果图](#112-效果图)
  - [1.2 table页面使用案例](#12-table页面使用案例)
    - [1.2.1 滚动条(鼠标进入表格，滚动条出现；鼠标移出表格，滚动条隐藏)](#121-滚动条鼠标进入表格滚动条出现鼠标移出表格滚动条隐藏)
- [2. ag-grid 使用案例](#2-ag-grid-使用案例)
  - [2.1 安装ag-grid](#21-安装ag-grid)
  - [2.2 序号列示例](#22-序号列示例)
  - [2.3 单选列示例](#23-单选列示例)
  - [2.4 多选列示例](#24-多选列示例)
  - [2.5 下拉列示例](#25-下拉列示例)
  - [2.6 内联组件示例](#26-内联组件示例)
  - [2.7 外部组件示例](#27-外部组件示例)
  - [2.8 长文本示例](#28-长文本示例)
  - [2.9 有省略号才有文字提示、没省略号没有文字提示示例](#29-有省略号才有文字提示没省略号没有文字提示示例)
  - [2.10 表格操作列示例](#210-表格操作列示例)
  - [2.11 表格有处理事件的列（例如点击之后，需要弹出弹窗）等示例](#211-表格有处理事件的列例如点击之后需要弹出弹窗等示例)
  - [2.12 表格某列正文内容居左等示例](#212-表格某列正文内容居左等示例)
- [3. 组件](#3-组件)
  - [3.1 空状态组件](#31-空状态组件)


# 1. 页面使用案例
## 1.1 tabs页面使用案例
### 1.1.1 代码示例
  <blockTabsIndex
    :pageLevel="1"   // 1:一级页面，2：二级页面，3：三级页面  4:内部页面
    :pageTitleShow="false"  // 是否显示页面标题  true / false
    pageTitle="tabs页面"   // 页面标题
    :tabsList="tabsList"   // tabs列表
    :activeKey="activeKey" // 页面选中的tab
    @tabClick="callback"   // tab点击事件
    :tableTotal="dataSource.length"  // 表格总行数 显示在左下角   “共tableTotal条”
    :loading="loading"     // 表格部分的loading
    @paginationChange="handlePageChange"     // 分页方法-页数
    @paginationSizeChange="handlePageChange" // 分页方法-每页条数
  >
    <!-- 搜索框 -->
    <template #search>
      <pbiSearchContainer>
      <!-- 有几个筛选框放几个 <pbiSearchItem>   span为一行放几个，总共是24 ，例如span为6时，一行4个，span为8时，一行放3个 -->
        <pbiSearchItem :span="6" label="产品名称">
          <a-input size='small'  v-model='queryParam.productName' placeholder='请输入产品名称'  @pressEnter="callfilter" style='width: 100%;'/>
        </pbiSearchItem>
      </pbiSearchContainer>
    </template>
    <!-- 表格 -->
    <template #table>
      <ag-grid-vue></ag-grid-vue>
    </template>
  </blockTabsIndex>
  tabsList: [
                {
                    value: '1',
                    label: 'XXXX'
                },
                {
                    value: '2',
                    label: 'XXXX'
                }
            ],

### 1.1.2 效果图
![alt text](image.png)

## 1.2 table页面使用案例 
### 1.2.1 滚动条(鼠标进入表格，滚动条出现；鼠标移出表格，滚动条隐藏)
template
 <tableIndex
          @tableFocus="tableFocus"
          @tableBlur="tableBlur"
></tableIndex>
script
    // 鼠标进入
    tableFocus() {
        this.$el.style.setProperty('--scroll-border-bottom-fixed', 'none');
        this.$el.style.setProperty('--scroll-display', 'unset');
        this.$el.style.setProperty('--scroll-border-bottom', '1px solid #Dee1e8');
    },
    // 鼠标移出
    tableBlur() {
        this.$el.style.setProperty('--scroll-border-bottom-fixed', '1px solid #dee1e8');
        this.$el.style.setProperty('--scroll-display', 'none');
        this.$el.style.setProperty('--scroll-border-bottom', 'none');
    },
style
      :root {
          --scroll-display: none;
          --scroll-border-bottom: none;
          --scroll-border-bottom-fixed: '1px solid #dee1e8';
      }
      /deep/.ag-body-horizontal-scroll{
          border-bottom: var(--scroll-border-bottom) !important;
      }
      /deep/.ag-body-horizontal-scroll-viewport {
          display: var(--scroll-display) !important;
          border-bottom: var(--scroll-border-bottom) !important;
      }

      /deep/.ag-horizontal-left-spacer,
      /deep/.ag-horizontal-right-spacer{
          border-bottom: var(--scroll-border-bottom-fixed) !important;
      }


# 2. ag-grid 使用案例
## 2.1 安装ag-grid
npm install ag-grid-community ag-grid-vue --save-dev


## 2.2 序号列示例
  columnDefs:[{ headerName: '序号',  field: 'no' , cellRenderer:p => p.node.rowIndex + 1  }]

## 2.3 单选列示例
  columnDefs: [{ headerName: '序号', field: 'no', checkboxSelection: true  }],
  gridOptions: { rowSelection:"single" },
  @selection-changed="selectionChanged"   //点击选中后

## 2.4 多选列示例
  columnDefs: [{ headerName: '序号', field: 'no',headerCheckboxSelection: true, checkboxSelection: true  }],   //checkboxSelection true 有顶部的全选框 false 没有顶部的全选框
  gridOptions: { rowSelection:"multiple" },
  @selection-changed="selectionChanged"  //点击选中后

## 2.5 下拉列示例
  columnDefs:[{ 
                headerName: '下拉', 
                field: 'select',
                editable: true,
                cellEditor: "agSelectCellEditor",
                cellEditorParams: { values: ["下拉内容1", "下拉内容2", "下拉内容3"] }
              }]

## 2.6 内联组件示例
  components: {
                CustomButton: {
                    template: '<button @click="params.onhandle(params.testParams)">{{params.testParams}}</button>',
                }
              },
  columnDefs:[{
                headerName: '外部组件',
                field: 'component',
                cellRenderer: '组件名称',
                cellRendererParams: { 
                  传递参数1: 内容,
                  传递参数2: 内容,
                  组件内部定义方法: 处理内容
                }
                //cellRendererParams: { // 传递额外的参数
                //  testParams: 'This is a test',
                //  onhandle: this.previewDoc
                //}
  }],

## 2.7 外部组件示例
  import 组件名称 from "组件地址"
  components: { 组件名称 },
  columnDefs:[{
                headerName: '外部组件',
                field: 'component',
                cellRenderer: '组件名称',
                cellRendererParams: { 
                  传递参数1: 内容,
                  传递参数2: 内容,
                  组件内部定义方法: 处理内容
                }
                //cellRendererParams: { // 传递额外的参数
                //  testParams: 'This is a test',
                //  onhandle: this.previewDoc
                //}
  }],
  methods:{
    previewDoc(value){}
  }

  CustomButton.vue
    <template>
      <button @click="params.组件内部定义方法(params.testParams)">{{params.testParams}}</button>
    </template>

## 2.8 长文本示例
  columnDefs:[{  
                headerName: '长文本',    //显示的列名称，和数据没有关系显示给用户看的
                field: 'longText',   //字段，headerName对于的数据字段，如上面代码“姓名”列对应的数据字段是name
                width:  最多展示的字体个数 * 12 + 24,   //  例如：最多展示的字体个数为五，页面上则超过五个字后，展示省略号
                flex:1,  //填充剩下的宽度   flex与width并存时,以flex为准，所以width与flex二选一
                pinned: 'right', //固定列   left / right
                headerTooltip:'鼠标悬浮在标题上,展示的文字提示',
                valueFormatter:item => {
                  <!-- 过滤的方法 -->
                  return item.value.replaceAll(' ', ',')   // 替换字符串
                  return item.value === 0 ? '男' : '女'    //  0跟1显示的字符不同
                },
                tooltipValueGetter: (p) => {
                  <!-- 文字提示 -->
                  return p.value
                }
              }]

## 2.9 有省略号才有文字提示、没省略号没有文字提示示例
  <ag-grid-vue
    :tooltipShowDelay="0"
    :grid-options="gridOptions"
    :defaultColDef='defaultColDef'>
  </ag-grid-vue>

  // pbiTooltip、pbiRefreshCells  为全局组件，不需要引入 
  data() {
    return {
      defaultColDef:{
        tooltipValueGetter: this.pbiTooltip    // pbiTooltip  为全局组件，不需要引入 
      },
      gridOptions:{
        onColumnResized: _.debounce(this.pbiRefreshCells,500)
      }
    }
  }

## 2.10 表格操作列示例
  columnDefs:[{  
                headerName: '操作',
                field: 'action',
                cellRenderer:'pbiTableActionBtn',   //pbiTableActionBtn 为全局组件，不需要引入
                cellRendererParams:{
                    btnList:[
                        {
                            btnType:'delete',   // 按钮类型
                            btnName:'编辑',     // 按钮展示文字   可不传，不传就没有展示文字
                            btnIcon:'delete',   // 按钮展示图标   可不传，不传就没有展示图标
                            btnTooltip:'删除',  // 按钮的鼠标悬停-文字提示   可不传，不传就没有鼠标悬停-文字提示
                        }
                    ],
                    handleBtn:(type,params) => {    // 按钮点击事件
                      // type: 传进去的btnType
                      // params: 当前行的数据
                    }
                }
              }]

  例:
  columnDefs:[{  
                headerName: '操作',
                field: 'action',
                cellRenderer:'pbiTableActionBtn',
                cellRendererParams:{
                    btnList:[
                      <!-- 文字+ 图标，没有鼠标悬停 -->
                        {
                            btnType:'delete',  
                            btnName:'删除',    
                            btnIcon:'delete', 
                        },
                      <!-- 纯文字 -->
                        {
                            btnType:'edit',  
                            btnName:'编辑' 
                        },

                      <!-- 图标+鼠标悬停 -->
                        {
                            btnType:'reset',  
                            btnIcon:'sync', 
                            btnTooltip:'重置',
                        },

                    ],
                    handleClickBtn:(type,params) => {  
                      switch(type){
                        case 'delete':
                          //删除操作
                          break;
                        case 'edit':
                          //编辑操作
                          break;
                        case 'reset':
                          //重置操作
                          break;
                      }
                    }
                }
              }]
  
## 2.11 表格有处理事件的列（例如点击之后，需要弹出弹窗）等示例
  columnDefs:[{  
                headerName: '操作',
                field: 'action',
                cellRenderer:'pbiTableActionLink',   //pbiTableActionLink 为全局组件，不需要引入
                cellRendererParams:{
                    content:'',    //列展示的内容        如果不传，就是当前行的数据中的field    row[field]
                    handleClickLink:(params) => {    // 点击事件
                      // params: 当前行的数据
                    }
                }
              }]

例:
  columnDefs:[{  
                headerName: '测试项目',
                field: 'testProject',
                cellRenderer:'pbiTableActionBtn',
                cellRendererParams:{
                    handleClickLink:(params) => {
                      //点击后执行的操作
                    }
                }
              }]

## 2.12 表格某列正文内容居左等示例
columnDefs:[{  
              cellStyle: () =>  {return {textAlign:'left'}} //向需要正文居左的列增加cellStyle属性
          }]




# 3. 组件

## 3.1 空状态组件
 示例：<pbiEmpty height="100"></pbiEmpty>
 属性： height: 高度，非必填，不传时，为200



