<template>
  <div class="page-container" :style="`margin:${pageMargin};`">
    <pbiTitle v-if="pageTitleShow" :title="pageTitle"></pbiTitle>
    <slot name="search"></slot>
    <div @mouseenter="tableFocus" @mouseleave="tableBlur">
      <a-spin :spinning="loading">
        <!-- 自定义表格 -->
        <slot name="table"></slot>
        <pbiPagination v-if="paginationShow" ref="pbiPagination" style="margin-top: 8px;" :total="tableTotal" @change="paginationChange"
          @showSizeChange="paginationSizeChange"></pbiPagination>
      </a-spin>
    </div>
  </div>
</template>
<script>
  export default {
    props: {
      otherHeight:{   //除了该页面以外的高度
        type: Number,
        default: 56, // 默认56,  顶栏:40  上下padding:  8*2
      },
      pageLevel: {
        type: Number,
        default: 3, // 1:一级页面，2：二级页面，3：三级页面
      },
      pageTitle: {
        type: String,
        default: ''
      },
      pageTitleShow: {
        type: Boolean,
        default: true
      },
      loading: {
        type: Boolean,
        default: true
      },
      tableTotal: {
        type: Number,
        default: 0
      },
      paginationShow: {
        type: Boolean,
        default: true
      },
    },
    data(){
      return{
        pageMargin: ''
      }
    },
    created() {
      switch(this.pageLevel){
        case 2:
          this.pageMargin = '8px 12px'
          break;
        case 3:
          this.pageMargin = '32px 12px 8px'
          break;
        case 4:
          this.pageMargin = '0px 12px'
      }
    },
    methods: {
      tableFocus() {
        this.$emit('tableFocus')
      },
      tableBlur() {
        this.$emit('tableBlur')
      },

      paginationChange(value) {
        this.$emit('paginationChange', value)
      },
      paginationSizeChange(value) {
        this.$emit('paginationSizeChange', value)
      }
    }

  }
</script>
<style lang="less" scoped>
  .page-container {
    overflow: hidden;
    
    padding: 8px 12px;
    /* 40:顶栏  16:外边距 */
    /* font-family: SourceHanSansSC; */
    border-radius: 4px;
    background-color: #fff;
  }


  /* ant table */
  /deep/.ant-table{
    font-size: 12px;
    color: #333;
  }

  /deep/.ant-table-small .ant-table-content .ant-table-body{
    margin: 0;
  }

  /deep/.ant-table-thead{
    background: #f5f7fa;
  }

  /deep/.ant-table-thead tr th{
    color: #666;
    font-weight: 700;
    padding: 0 !important;
    height: 36px;
  }
  /deep/.ant-table-tbody tr td{
    color: #333;
    font-weight: 400;
    padding: 4px !important;
  }

  /deep/.ant-table-thead tr th,
  /deep/.ant-table-tbody tr td{
    font-size: 12px;
    border-bottom:1px solid #dee1e8
  }

  /deep/.ant-table-bordered .ant-table-thead tr th, 
  /deep/.ant-table-bordered .ant-table-tbody tr td {
    border-right:1px solid #dee1e8
  }

  /deep/.ant-table-tbody .ant-table-row:nth-child(even){
    background-color: #fafafa;
  }

  /deep/.ant-table-tbody .ant-table-row:hover{
    background: #f0f5ff;
  }

</style>