<template>
  <a-tabs hide-add type="editable-card" v-model="activeKey" :default-active-key="1" tab-position="top" style="background: #fff;padding: 24px 0" @edit="onEdit">
    <!-- <a-tab-pane key="1" tab="产品详情" :closable="false"><detailview :issueId="issueId" :projectdetail="projectdetail" :loading="loading" /></a-tab-pane>
    <a-tab-pane key="2" tab="技术文档" :closable="false"><docs :issueId="issueId" /></a-tab-pane>
    <a-tab-pane key="3" tab="产品工厂" :closable="false"><suppliers :issueId="issueId" /></a-tab-pane> -->
    <!-- <a-tab-pane key="4" tab="物料录入" :closable="false"><bomsuppliers :issueId="issueId" /></a-tab-pane> -->
    <!-- <a-tab-pane key="4" :closable="false" tab="产线管理"><werkline /></a-tab-pane> -->
    <a-tab-pane key="1" tab="包装BOM管理" :closable="false"><bompackglobe :parts="parts"  @onshow="onshow" @onUpdate="onUpdate"  /></a-tab-pane>
    <!-- <a-tab-pane key="5" tab="电芯BOM管理" :closable="false"><bommanage :parts="parts" :issueId="issueId" :projectdetail="projectdetail" @onshow="onshow" @onUpdate="onUpdate"  /></a-tab-pane> -->
    <a-tab-pane key="6" tab="BOM搭建" :closable="true" v-if="show"><bom  :projectdetail="null" :bomId="bomId"  @onSave="onSave" /></a-tab-pane>
  </a-tabs>
</template>

<script>
import docs from './docs'
import suppliers from './suppliers'
import detailview from './detailview'
import bomsuppliers from './bomsuppliers'
import bommanage from './bommanage'
import bompackglobe from './bompackglobe'
import werkline from '../werkline/index'
import bom from './bom'
/* import { getProjectDetail } from "@/api/modular/system/report" */
import {
		getPartList
	} from "@/api/modular/system/partManage"
export default {
    components: {
        docs,
        suppliers,
        detailview,
        bomsuppliers,
        bommanage,
        bompackglobe,
        bom,
        werkline
    },
    data(){
        return {
            parts:[],
            bomId:null,
            activeKey:'0',
            //issueId : 0,
            show:false,
            //projectdetail:{},
            loading:true,
            backActiveKey:''
        }
    },
    methods:{
        callPartList() {
				//this.vloading = true
				getPartList({
						flag: 0
					})
					.then((res) => {
						if (res.success) {
							this.parts = res.data
						} else {
							this.$message.error(res.message, 1);
						}
						//this.vloading = false
					})
					.catch((err) => {
						//this.vloading = false
						this.$message.error('错误提示：' + err.message, 1)
					});
			},
        onEdit(targetKey, action) {
            if (targetKey != '6') {
                return false;
            }
            this.show = !this.show
            if(!this.show){
                this.activeKey = this.backActiveKey
            }else{
                this.activeKey = targetKey
            }
            
        },
        onshow(targetKey,record){
            if (this.show) {
                this.$message.error('BOM在搭建，此次操作禁止')
                return false
            }
            this.bomId = null
            this.show = true
            this.activeKey = targetKey+""
        },
        onUpdate(targetKey,id,backKey){
            if (this.show) {
                this.$message.error('BOM在搭建，此次操作禁止')
                return false
            }
            this.bomId = id
            this.show = true
            this.activeKey = targetKey+""
            this.backActiveKey = backKey+""
        },
        /* callProjectDetail(){
            this.loading = true
            let params = {issueId: this.issueId,title:''}
            getProjectDetail(params)
            .then((res)=>{
                if (res.result) {
                    this.projectdetail = res.data
                } else {
                    this.$message.error(res.message,1);
                }
                this.loading = false
                })
            .catch((err)=>{
                this.loading = false
                this.$message.error('错误提示：' + err.message,1)
            });
        }, */
        onSave(data){
        }
    },
    created () {
        //this.issueId = parseInt(this.$route.query.issueId)
        this.activeKey = '1'
        /* this.callProjectDetail() */
        this.callPartList()
    }
}
</script>

<style scoped>
</style>