<template>
  <div class="icon" @click="returnTop">
    <svg t="1726808686436" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
      p-id="15911" :width="width" :height="height">
      <path
        d="M76.8 480l422.4-422.4c6.4-6.4 19.2-6.4 25.6 0l422.4 422.4c6.4 6.4 12.8 6.4 19.2 6.4 6.4 0 12.8 0 19.2-6.4 12.8-12.8 12.8-25.6 0-38.4L556.8 19.2c-25.6-25.6-76.8-25.6-102.4 0L32 441.6C25.6 448 25.6 467.2 32 480S64 492.8 76.8 480z"
        :fill="color" p-id="15912"></path>
      <path d="M32 672 140.8 672 140.8 1017.6 192 1017.6 192 672 300.8 672 300.8 633.6 32 633.6Z" :fill="color"
        p-id="15913"></path>
      <path
        d="M499.2 627.2c-57.6 0-102.4 19.2-134.4 57.6-32 38.4-51.2 89.6-51.2 147.2 0 57.6 19.2 102.4 51.2 140.8C396.8 1004.8 441.6 1024 492.8 1024c57.6 0 102.4-19.2 134.4-57.6s51.2-89.6 51.2-147.2c0-57.6-19.2-102.4-51.2-140.8C601.6 640 556.8 627.2 499.2 627.2zM595.2 940.8c-25.6 25.6-57.6 38.4-96 38.4-38.4 0-70.4-12.8-96-44.8-25.6-25.6-38.4-64-38.4-115.2 0-44.8 12.8-83.2 38.4-115.2 25.6-25.6 57.6-44.8 96-44.8 38.4 0 70.4 12.8 96 38.4 25.6 25.6 32 64 32 115.2C627.2 876.8 614.4 908.8 595.2 940.8z"
        :fill="color" p-id="15914"></path>
      <path
        d="M960 659.2c-25.6-19.2-57.6-32-96-32l-108.8 0 0 384 51.2 0 0-147.2 51.2 0c38.4 0 76.8-12.8 102.4-32 25.6-25.6 38.4-51.2 38.4-89.6C998.4 710.4 985.6 678.4 960 659.2zM921.6 806.4c-12.8 12.8-38.4 19.2-70.4 19.2l-44.8 0 0-153.6 51.2 0c57.6 0 89.6 25.6 89.6 76.8C947.2 774.4 934.4 793.6 921.6 806.4z"
        :fill="color" p-id="15915"></path>
    </svg>
  </div>
</template>
<script>
  export default {
    props: {
      width: {
        type: [Number, String],
        default: '30'
      },
      height: {
        type: [Number, String],
        default: '30'
      },
      color:{
        type: String,
        default: '#1890ff'
      }
    },
    methods: {
      returnTop() {
        this.$emit('returnTop')
      }
    }
  }
</script>
<style lang="less" scoped>
  .icon {
    position: fixed;
    right: 20px;
    bottom: 20px;
    cursor: pointer;
    z-index: 10;

  }
</style>