/*
@license

dhtmlxGantt v.6.3.3 Standard

This version of dhtmlxGantt is distributed under GPL 2.0 license and can be legally used in GPL projects.

To use dhtmlxGantt in non-GPL projects (and get Pro version of the product), please obtain Commercial/Enterprise or Ultimate license on our site https://dhtmlx.com/docs/products/dhtmlxGantt/#licensing or contact <NAME_EMAIL>

(c) XB Software Ltd.

*/
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("locale/locale_tr",[],t):"object"==typeof exports?exports["locale/locale_tr"]=t():e["locale/locale_tr"]=t()}(window,function(){return function(e){var t={};function n(a){if(t[a])return t[a].exports;var o=t[a]={i:a,l:!1,exports:{}};return e[a].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,a){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:a})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var a=Object.create(null);if(n.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(a,o,function(t){return e[t]}.bind(null,o));return a},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="/codebase/",n(n.s=182)}({182:function(e,t){gantt.locale={date:{month_full:["Ocak","Şubat","Mart","Nisan","Mayıs","Haziran","Temmuz","Ağustos","Eylül","Ekim","Kasım","Aralık"],month_short:["Oca","Şub","Mar","Nis","May","Haz","Tem","Ağu","Eyl","Eki","Kas","Ara"],day_full:["Pazar","Pazartesi","Salı","Çarşamba","Perşembe","Cuma","Cumartesi"],day_short:["Paz","Pzt","Sal","Çar","Per","Cum","Cmt"]},labels:{new_task:"Yeni görev",dhx_cal_today_button:"Bugün",day_tab:"Gün",week_tab:"Hafta",month_tab:"Ay",new_event:"Yeni etkinlik",icon_save:"Kaydet",icon_cancel:"İptal",icon_details:"Detaylar",icon_edit:"Düzenle",icon_delete:"Sil",confirm_closing:"",confirm_deleting:"Görev silinecek, emin misiniz?",section_description:"Açıklama",section_time:"Zaman Aralığı",section_type:"Tip",column_wbs:"WBS",column_text:"Görev Adı",column_start_date:"Başlangıç",column_duration:"Süre",column_add:"",link:"Bağlantı",confirm_link_deleting:"silinecek",link_start:" (başlangıç)",link_end:" (bitiş)",type_task:"Görev",type_project:"Proje",type_milestone:"Kilometretaşı",minutes:"Dakika",hours:"Saat",days:"Gün",weeks:"Hafta",months:"Ay",years:"Yıl",message_ok:"OK",message_cancel:"Ýptal",section_constraint:"Constraint",constraint_type:"Constraint type",constraint_date:"Constraint date",asap:"As Soon As Possible",alap:"As Late As Possible",snet:"Start No Earlier Than",snlt:"Start No Later Than",fnet:"Finish No Earlier Than",fnlt:"Finish No Later Than",mso:"Must Start On",mfo:"Must Finish On",resources_filter_placeholder:"type to filter",resources_filter_label:"hide empty"}}}})});
//# sourceMappingURL=locale_tr.js.map