<template>
  <a-spin :spinning="isLoading" class="hide-progress-parent">
    <div ref="wrapper" class="wrapper">
      <div class="flex-sb-center-row">
        <div class="head_title">{{ prefixName + (data.reportName ? ': ' + data.reportName : '') }}</div>
      </div>
      <div class="right-top ant-btn-div">
        <a-button class="mr10" size="small" @click="exportTempOrCRate"><a-icon type="download"/>导出数据</a-button>
      </div>

      <div class="all-wrapper mt10">
        <div class="left-content block">
          <div v-for="(item, index) in editObjList">
            <div class="flex-sb-center-row mt2">
              <pageComponent :editObj="item" @down="handleDown(item)" @edit="handleEditEcharts(item)"></pageComponent>
              <div class="ant-btn-div">
                <a-tooltip title="编辑建模参数">
                  <a-button size="small" @click="reExport(false)"><a-icon type="edit"/></a-button>
                </a-tooltip>
                <a-popconfirm title="确定要重新生成吗?" ok-text="确定" cancel-text="取消" @confirm="reExport(true)">
                  <a-tooltip title="重新生成">
                    <a-button class="ml10" size="small"><a-icon type="sync"/></a-button>
                  </a-tooltip>
                </a-popconfirm>
                <a-radio-group v-if="index >= 2" class="ml10" size="small" :value="queryParam.xaxisType || 'Capacity'" @change="xaxisTypeChange">
                  <a-radio-button value="Capacity">Capacity</a-radio-button>
                  <a-radio-button value="Energy">Energy</a-radio-button>
                </a-radio-group>
                <a-radio-group v-if="item.includes('tempRise')" class="ml10" size="small" :value="queryParam.tempRiseType || 'tempRise1'" @change="tempRiseTypeChange">
                  <a-radio-button value="tempRise1">正极</a-radio-button>
                  <a-radio-button value="tempRise2">负极</a-radio-button>
                  <a-radio-button value="tempRise3">主体</a-radio-button>
                </a-radio-group>
              </div>
            </div>
            <div :ref="item" :id="item" style="width: 595px; height: 415px; border: 0.5px solid #ccc;" :class="{ 'animate__animated animate__pulse': animateObj[item] }"></div>
          </div>
        </div>

        <div class="right-content block">
          <div class="mt2 table-title-div">容量数据</div>
          <div class="retention-table" style="height: 415px;">
            <a-table :data-source="allDataJson.retentionTableList"
                     :columns="capColumns"
                     :rowKey="record => record.rowKey"
                     :pagination="paginationConfig"
                     bordered>
            </a-table>
          </div>

          <div class="mt2 table-title-div">能量数据</div>
          <div class="retention-table" style="height: 415px;">
            <a-table :data-source="allDataJson.retentionTableList"
                     :columns="engColumns"
                     :rowKey="record => record.rowKey"
                     :pagination="paginationConfig"
                     bordered>
            </a-table>
          </div>

          <div v-for="(sampleCode, index) in sampleCodeList">
            <div class="mt2 table-title-div">{{ `${sampleCode} (${sampleInfoMap[sampleCode].batteryCode})` }}</div>
            <div class="record-table" style="height: 864px;">
              <a-table :data-source="tablePageObjList[index].records"
                       :columns="recordColumns"
                       :rowKey="record => record.rowKey"
                       :pagination="false"
                       :loading="tableLodingList[index]"
                       bordered>
              </a-table>
              <a-pagination style="margin-top: 2px; float: right;"
                            :current="tablePageObjList[index].current"
                            :pageSize="tablePageObjList[index].size"
                            :total="tablePageObjList[index].total"
                            :showSizeChanger="true"
                            :pageSizeOptions="paginationConfig.pageSizeOptions"
                            size="small"
                            :showTotal="(total, range) => `${range[0]}-${range[1]} 共 ${total} 条`"
                            @change="(page, pageSize) => onPageChange(page, pageSize, index)" @showSizeChange="(current, size) => onPageChange(current, size, index)">
              </a-pagination>
            </div>
          </div>
        </div>
      </div>

      <!-- 在线编辑图表 -->
      <div v-if="drawerVisible">
        <PreviewDrawer
          :screenImageId = "screenImageId"
          :templateParam = "reportChartTemplateList[editObj]"
          :legendNameTypeShow="true"
          :LegendNameTypeList = "this.chartLegendNameListObj[editObj]"
          :legendOptions="legendOptions[editObj]"
          :data="editData[editObj].series"
          :original="originalData[editObj]"
          :editData="editData[editObj]"
          :checkObj="chartCheckObj[editObj]"
          @submit="handleDrawerSubmit"
          @reset="handleDrawerReset"
          @close="() => this.drawerVisible = false"
          @changeTemplate ="handleChangeTemplate"
          @screenshot="handleScreenshot">
        </PreviewDrawer>
      </div>

      <!--  图表导航 -->
      <div class="action-bar">
        <a-popover placement="topLeft">
          <template slot="content">
            <div v-for="(item, index) in editObjList">
              <div class="navigate-item" @click="handleChoose(item, index)">{{ navigateNameList[index] }}</div>
            </div>
          </template>
          <a-icon type="menu"/>
        </a-popover>
        <pbiReturnTop class="mt10" width="20" height="20" color="#333" @returnTop="handleReturnTop"></pbiReturnTop>
      </div>

    </div>
  </a-spin>
</template>

<script>
import {getDongLiData, exportDongLiData, commitDongLiParam} from "@/api/modular/system/cycleReportManager";
import {downloadfile1} from "@/utils/util";
import jsonBigint from "json-bigint";
import {Pagination} from "ant-design-vue";
import _ from "lodash";
import {optionMergeCommon} from "@/views/system/vTestReport/mixin/optionMergeCommon";
import {chartTemplate} from "@/views/system/vTestReport/mixin/chartTemplate";

export default {
  name: "hlTempOrCRatePreview",
  components: {
    'a-pagination': Pagination
  },
  mixins: [optionMergeCommon,chartTemplate],
  data: function () {
    return {
      isLoading: false,

      id: null,
      reportType: 'HlTemp',
      queryParam: {},
      chOrDchType: '',
      prefixName: '',
      tempOrCRate: '',
      allDataJson: {},
      sampleCodeList: [],
      sampleInfoMap: {},
      tempOrCRateList: [],
      data: {},

      capColumns: [],
      engColumns: [],
      recordColumns: [],
      tablePageObjList: [],
      tableLodingList: [],
      paginationConfig: {
        defaultPageSize: 20,
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '30', '40', '50'], // 显示的每页数量选项
        size: "small",
        showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
      },

      navigateNameList: [],
      animateObj: {}, //导航栏动画

      legendOptions:{}, //图例-数据的选择项
      chartCheckObj: {}
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.isLoading = true

      this.id = this.$route.query.id
      getDongLiData({
        id: this.id,
      }).then(res => {
        if (res.data) {
          // 数据解析
          this.reportType = res.data.type;
          const json = jsonBigint({storeAsString: true});
          this.queryParam = json.parse(res.data.queryParam);
          this.chOrDchType = this.queryParam.chOrDchType;
          this.prefixName = (this.reportType === 'HlTemp' ? '高低温' : '倍率') + (this.chOrDchType === 'Charge' ? '充电' : '放电');
          this.tempOrCRate = this.queryParam.tempOrCRate;
          this.allDataJson = json.parse(res.data.allDataJson);
          this.sampleCodeList = this.allDataJson.sampleCodeList || [];
          this.tableLodingList = new Array(this.sampleCodeList.length).fill(false);
          this.sampleInfoMap = this.allDataJson.sampleInfoMap || {};
          this.tempOrCRateList = this.allDataJson.tempOrCRateList || [];
          // 分页数据
          this.tablePageObjList = this.allDataJson.allPageResultList || [];
          // 将过滤后的数据赋值给 this.data
          this.data =  _.omit(res.data, ['allDataJson']);

          // Echarts图相关变量初始化
          this.firstInit = {}
          this.titleDataObj = {}
          this.chartLegendNameListObj = {}
          this.editObjList = ['capRetRate', 'engRetRate']
          this.navigateNameList = [this.prefixName + '-容量保持率', this.prefixName + '-能量保持率']
          const yTitleList = ['Capacity Retention Rate / %', 'Energy Retention Rate / %']
          this.sampleCodeList.forEach((sampleCode, index) => {
            this.editObjList.push(`voltage-${index}`, `tempRise-${index}`)
            this.navigateNameList.push(sampleCode + this.prefixName + '-容量/能量&电压曲线', sampleCode + this.prefixName + '-容量/能量&温升曲线')
            yTitleList.push('Voltage / V', 'Temperature Rise / Δ℃')
          })
          const prefix = (this.queryParam.projectName ? this.queryParam.projectName + '_' : '') + (this.queryParam.phase ? this.queryParam.phase + '_' : '')
          const suffix = (this.tempOrCRate || '') + (this.reportType === 'HlTemp' ? 'C' : '℃')
          const chartTtile1 = prefix + (this.reportType === 'HlTemp' ? 'H/L Temp. ' : 'Rate ') + (this.chOrDchType || 'Discharge') + ' Curve@' + suffix
          const chartTtile2 = prefix + 'Temperature Rise Curve@' + suffix
          const xTitle = this.queryParam.xaxisType === 'Energy' ? 'Energy / Wh' : 'Capacity / Ah'
          this.editObjList.forEach((targetObj, index) => {
            this.firstInit[targetObj] = true
            this.titleDataObj[targetObj] = {
              chartTitle: targetObj.includes('tempRise') ? chartTtile2 : chartTtile1,
              XTitle: targetObj.includes('RetRate') ? (this.reportType === 'HlTemp' ? 'Temperature / ℃' : 'Rate / C') : xTitle,
              YTitle: yTitleList[index],
              tooltipPrefix: index === 0 ? '容量保持率 ' : index === 1 ? '能量保持率 ' : '',
              tooltipUnit: targetObj.includes('RetRate') ? ' %' : '',
              hasYTitle2: false,
              YTitle2: null,
            }
            this.chartLegendNameListObj[targetObj] = []
            this.chartCheckObj[targetObj] = {}
          })
        }
      }).then(async () => {
        // 初始化表头
        this.initTable()
        await this.getChartTemplateRelationList(this.$route.query.id,this.editObjList)
        // Echarts图初始化
        this.editObjList.forEach((targetObj, index) => {
          let tableIndex = 0
          if (targetObj.includes('-')) {
            tableIndex = parseInt(targetObj.split('-')[1])
          }
          const yaxisOneList = index < 2 ? _.cloneDeep(this.allDataJson[`${targetObj}EchartList`]) : _.cloneDeep(this.allDataJson.allRecordEchartList[tableIndex])
          this.initEchart(targetObj, this.queryParam.xaxisType, yaxisOneList, [], false)
        })

        // 字体渲染不正确，重新setOption
        document.fonts.ready.then(() => {
          this.editObjList.forEach((targetObj, index) => {
            this.echartObj[targetObj].setOption({
              textStyle: {
                fontFamily: "Times New Roman"
              }
            })
          })
        })
      }).finally(() => {
        this.isLoading = false
      })
    },
    initTable() {
      const sampleCodeList = this.sampleCodeList
      const tempOrCRateList = this.tempOrCRateList

      // 容量能量保持率表头处理
      this.capColumns = []
      this.engColumns = []
      const title0 = this.reportType === 'HlTemp' ? '温度' : '倍率'
      this.capColumns.push(
          {
            title: title0,
            dataIndex: "tempOrCRate",
            align: "center",
            width: 60,
          },
          {
            title: "容量/Ah",
            align: "center",
          },
          {
            title: "容量保持率/%",
            align: "center",
          }
      )
      this.engColumns.push(
          {
            title: title0,
            dataIndex: "tempOrCRate",
            align: "center",
            width: 60,
          },
          {
            title: "能量/Wh",
            align: "center",
          },
          {
            title: "能量保持率/%",
            align: "center",
          }
      )
      if (sampleCodeList.length > 0) {
        this.capColumns[1].children = []
        this.capColumns[2].children = []
        this.engColumns[1].children = []
        this.engColumns[2].children = []
        for (let i = 0; i < sampleCodeList.length; i++) {
          const sampleCode = sampleCodeList[i]
          const batteryCode = this.sampleInfoMap[sampleCode].batteryCode
          this.capColumns[1].children.push(this.getRetChildren(sampleCode, batteryCode, 'capacity'))
          this.capColumns[2].children.push(this.getRetChildren(sampleCode, batteryCode, 'capRetRate'))
          this.engColumns[1].children.push(this.getRetChildren(sampleCode, batteryCode, 'energy'))
          this.engColumns[2].children.push(this.getRetChildren(sampleCode, batteryCode, 'engRetRate'))
        }
      }

      // 详细数据表头处理
      this.recordColumns = []
      const columnKeyList = ['voltage', 'capacity', 'energy', 'temp1', 'temp2', 'temp3', 'tempRise1', 'tempRise2', 'tempRise3']
      const columnNameList = ['电压/V', '容量/Ah', '能量/Wh', '正极温度/℃', '负极温度/℃', '主体温度/℃', '正极温升/℃', '负极温升/℃', '主体温升/℃']
      for (let i = 0; i < tempOrCRateList.length; i++) {
        this.recordColumns.push(
            {
              title: tempOrCRateList[i],
              align: "center",
              children: []
            }
        )
        for (let j = 0; j < columnKeyList.length; j++) {
          this.recordColumns[i].children.push(
              {
                title: columnNameList[j],
                align: "center",
                width: "100px",
                customRender: (text, record) => {
                  return record.tempOrCRateObjectMap[i][columnKeyList[j]]
                }
              }
          )
        }
      }
    },
    getRetChildren(sampleCode, batteryCode, columnKey) {
      let result = {
        title: batteryCode,
        align: "center",
        width: "100px",
        customRender: (text, record) => {
          return record.primaryObjectMap[sampleCode][columnKey]
        }
      }

      if (sampleCode !== batteryCode) {
        result = {
          title: sampleCode,
          align: "center",
          children: [
            result
          ]
        }
      }

      return result
    },

    handleInitChart(targetObj) {
      let tableIndex = 0
      if (targetObj.includes('-')) {
        tableIndex = parseInt(targetObj.split('-')[1])
      }
      const yaxisOneList = targetObj.includes('RetRate') ? _.cloneDeep(this.allDataJson[`${targetObj}EchartList`]) : _.cloneDeep(this.allDataJson.allRecordEchartList[tableIndex])
      this.initEchart(targetObj, this.queryParam.xaxisType, yaxisOneList, [], false)
    },
    initEchart(targetObj, xaxisType = 'Capacity', yAxisOneList = [], yAxisTwoList = [], hasYAxisTwo) {

      // 检查是否存在 ECharts 实例 并清除
      if (this.echartObj[targetObj]) {
        this.echartObj[targetObj].dispose();
      }

      // this.echartObj[targetObj] = this.echarts.init(document.getElementById(targetObj), 'walden', { renderer: "svg" }) // 测试
      this.echartObj[targetObj] = this.echarts.init(document.getElementById(targetObj), 'walden', {devicePixelRatio: 2, width: 593, height:413})
       // 模板数据
      const templateParam = this.reportChartTemplateList[targetObj].templateParamJson
      const originalParam = this.reportChartTemplateList[targetObj].originalParamJson

      let seriesList = []

      if (this.firstInit[targetObj]) {  // 首次加载
        seriesList = this._handleHlTempOrCRateData(targetObj, xaxisType, yAxisOneList, yAxisTwoList, hasYAxisTwo)
      } else {  // 二次加载
        seriesList = this.editData[targetObj].editSeries
      }

      let chartOption = this._handleEchartOptions(targetObj, seriesList, hasYAxisTwo)

      // 如果模板有X轴的最大值、最小值、间隔,如果有就设置
      if(templateParam.xMin){
        chartOption.xAxis[0].min = templateParam.xMin
      }
      if(templateParam.xMax){
        chartOption.xAxis[0].max = templateParam.xMax
      }
      if(templateParam.xInterval){
        chartOption.xAxis[0].interval = templateParam.xInterval
      }

      this.echartObj[targetObj].clear()
      this.echartObj[targetObj].getZr().off('dblclick')
      this.echartObj[targetObj].getZr().on('dblclick', ({target, topTarget}) => {
        this._handleDblclickEchart(target, topTarget, targetObj)
      });
      this.echartObj[targetObj].setOption(chartOption)

      if (this.firstInit[targetObj]) {
        this._handleYAxisValue(targetObj, hasYAxisTwo)

        // 如果模板中没有最大值，则最大值重新赋值：多一个格子
        if(!templateParam.xMax){
          this.editData[targetObj].xMax = this.editData[targetObj].xMax + this.editData[targetObj].xInterval
          this.originalData[targetObj].xMax = this.editData[targetObj].xMax
          this.originalData[targetObj].xInterval = this.editData[targetObj].xInterval
          if (this.originalData[targetObj].xType === 'value') {
            chartOption.xAxis[0].max = this.originalData[targetObj].xMax
            chartOption.xAxis[0].interval =this.originalData[targetObj].xInterval
          }
          this.echartObj[targetObj].setOption(chartOption)
        }
        this.firstInit[targetObj] = false
      }
    },
    // 数据处理
    _getHlTempOrCRateOriginal(titleData, targetObj, hasYAxisTwo = false) {
      // 模板值
      const templateParam = this.reportChartTemplateList[targetObj].templateParamJson

      if (hasYAxisTwo) {
        // 次Y轴默认初始值
        titleData.yTitleRight = templateParam.yTitleRight ?? 40
        titleData.yType2 = templateParam.yType2 ?? "value"
        titleData.yMin2 = templateParam.yMin2 ?? -10
        titleData.yMax2 = templateParam.yMax2 ?? 150
        titleData.yInterval2 = templateParam.yInterval2 ?? 20
      }

      const yMin = this.queryParam.yaxisMin || 2
      const yMax = this.queryParam.yaxisMax || 4.4
      return {
        titleTop: templateParam.titleTop ?? 10,

        yTitleLetf: templateParam.yTitleLetf ?? 40,
        xType: templateParam.xType ?? targetObj.includes('RetRate') ? 'category' : 'value',
        yType: templateParam.yType ?? 'value',
        yMin: templateParam.yMin ?? (targetObj.includes('RetRate') ? 70 : targetObj.includes('voltage') ? yMin : 0),
        yMax: templateParam.yMax ?? (targetObj.includes('RetRate') ? 110 : targetObj.includes('voltage') ? yMax : 60),
        yInterval: templateParam.yInterval ?? (targetObj.includes('RetRate') ? 5 : null),

        legendWidth: templateParam.legendWidth ??  20,
        legendHeight: templateParam.legendHeight ?? 5,
        legendGap: templateParam.legendGap ?? 5, //图例间隙
        legendOrient: templateParam.legendOrient ?? 'vertical',
        legendBgColor: templateParam.legendBgColor ?? 'none',
        legendFontSize: templateParam.legendFontSize ?? 12,
        legendTop: templateParam.legendTop ?? 50,
        legendRight: templateParam.legendRight ?? (hasYAxisTwo ? 90 : 50),
        legendNameType:templateParam.legendNameType ?? 'sampleCode',

        gridTop: templateParam.gridTop ?? 40,
        gridLeft: templateParam.gridLeft ?? 80,
        gridRight: templateParam.gridRight ?? hasYAxisTwo ? 80 : 40,
        gridBottom: templateParam.gridBottom ?? 70,

        ...titleData,
      }
    },
    _handleHlTempOrCRateData(targetObj, xaxisType = 'Capacity', yAxisOneList, yAxisTwoList = [], hasYAxisTwo = false) {
      // 模板数据
      const templateParam = this.reportChartTemplateList[targetObj].templateParamJson
      const originalParam = this.reportChartTemplateList[targetObj].originalParamJson

      const isRetRate = targetObj.includes('RetRate');
      const xIndex = isRetRate ? 0 : xaxisType === 'Energy' ? 1 : 0;
      const tempRiseIndex = this.queryParam.tempRiseType === 'tempRise3' ? 5 : this.queryParam.tempRiseType === 'tempRise2' ? 4 : 3;
      const yIndex = isRetRate ? 1 : targetObj.includes('voltage') ? 2 : tempRiseIndex;
      const sampleCodeIndex = targetObj.includes('-') ? parseInt(targetObj.split('-')[1]) : 0;

      yAxisOneList = Array.isArray(yAxisOneList) ? yAxisOneList : [];

      let titleData = this._getHlTempOrCRateOriginal(this.titleDataObj[targetObj], targetObj, hasYAxisTwo);
      if (titleData.yInterval === null) {
        delete titleData.yInterval
      }

      let seriesList = []
      let lineColorList = [] // 折线颜色
      const echartsColorList = this.echartsColorList
      const normalData = {...titleData,series:[]}
      const normalLegent = []

      for (let i = 0; i < yAxisOneList.length; i++) {
        const sampleCode = isRetRate ? yAxisOneList[i].sampleCode : this.sampleCodeList[sampleCodeIndex] + '#' + yAxisOneList[i].tempOrCRate;
        const batteryCode = isRetRate ? yAxisOneList[i].batteryCode : this.sampleInfoMap[this.sampleCodeList[sampleCodeIndex]].batteryCode + '#' + yAxisOneList[i].tempOrCRate;
        const templateContent = (templateParam.checkData && templateParam.checkData.length !== 0) ? (templateParam.checkData.filter(item => item.id === sampleCode + `${i}`)[0] || {}) : {}


        this.chartLegendNameListObj[targetObj].push({sampleCode: sampleCode, batteryCode: batteryCode})

        const yAxisOneLegendName = this.chartLegendNameListObj[targetObj][i][titleData.legendNameType]

        const haveColor = lineColorList.find(v => v.name === sampleCode)
        if (haveColor == undefined) {
          lineColorList.push({name: sampleCode, color: echartsColorList[lineColorList.length]})
        }
        const temColor = lineColorList[lineColorList.findIndex(v => v.name === sampleCode)].color

        const seriesTemplate = {
          name: yAxisOneLegendName,
          type: 'line',
          sampling: 'lttb',
          large: true,
          barGap: 0,
          symbol: templateContent.symbol ?? (isRetRate ? 'rect' : 'none'),
          markPoint: {
            data: []
          },
          emphasis: {
            focus: "series"
          },
        }
        const seriesOriginalTemplate = {
          name: yAxisOneLegendName,
          index: i + 1,
          soc: yAxisOneLegendName,
          type: 'line',
          sampling: 'lttb',
          large: true,
          barGap: 0,
          symbol: templateContent.symbol ?? (isRetRate ? 'rect' : 'none'),
          maxPoint: templateContent.maxPoint ?? false,
          minPoint: templateContent.minPoint ?? false,
          connectNulls: templateContent.connectNulls ?? false,
          symbolSize: templateContent.symbolSize ?? 0,
          lineWidth: templateContent.lineWidth ?? 1.5,
          lineColor: templateContent.lineColor ?? temColor,
          itemColor: templateContent.itemColor ?? temColor
        }
        const originalSeries = [{         //小原始值
          id: sampleCode + `${i}`,
          lineType: templateContent.lineType ?? 'solid',
          synchronization: seriesList.length,
          dataName:yAxisOneLegendName + '_' + titleData.tooltipPrefix,
          ...seriesOriginalTemplate
        }]
        if(templateParam.legendData?.legendList === undefined || templateParam.legendData.legendList.includes(yAxisOneLegendName)){
          if(templateContent.symbolSize){
            seriesTemplate.symbolSize = templateContent.symbolSize
          }
          if(templateContent.connectNulls){
            seriesTemplate.connectNulls = Boolean(Number(templateContent.connectNulls))
          }
          if(templateContent.maxPoint){
            seriesTemplate.markPoint.data.push({type:'max',name:'max'})
          }
          if(templateContent.minPoint){
            seriesTemplate.markPoint.data.push({type:'min',name:'min'})
          }

          seriesList.push({
            ...seriesTemplate,
            id: sampleCode + `${i}`,
            tooltip: {
              valueFormatter: (value) => {
                return titleData.tooltipPrefix + (value != null ? value + titleData.tooltipUnit : '');
              }
            },
            lineStyle: {
                width: templateContent.lineWidth ?? 1.5,
                type: templateContent.lineType ?? 'solid',
                color: templateContent.lineColor ?? temColor
              },
              itemStyle: {
                color: templateContent.itemColor ?? temColor
              },
            data: yAxisOneList[i].data.map(item => {
              return [item[xIndex], item[yIndex]];
            }),
          })
        }
        normalData.series.push(...originalSeries)
        normalLegent.push(yAxisOneLegendName)
      }

      if(!this.originalData[targetObj]){
        this.originalData[targetObj] = originalParam ? _.cloneDeep (originalParam) : _.cloneDeep(normalData)
        this.originalData[targetObj].originalSeries = originalParam ? _.cloneDeep(originalParam.originalSeries) : _.cloneDeep(seriesList)
      }
      this.legendOptions[targetObj] = _.cloneDeep(normalLegent)
      this.editData[targetObj] = {
        ..._.cloneDeep(normalData),
        editSeries:_.cloneDeep(seriesList),
        originalSeries:originalParam ? _.cloneDeep(originalParam.originalSeries) : _.cloneDeep(seriesList),
        legend:templateParam.legendData?.legendList ?? _.cloneDeep(normalLegent),
        legendSort:templateParam.legendData?.legendSort ??  _.cloneDeep(normalLegent),
        legendRevealList:templateParam.legendData?.legendRevealList ??  _.cloneDeep(normalLegent).slice(0, 6),
        legendEditName:templateParam.legendData?.legendEditName ??  _.cloneDeep(normalLegent).map(v => {
          return {id: v,originName: v, previousName: '', newName: '', isReset: false}
        }),
        allData:templateParam.allData ?? {},
      }

      for (let key of Object.keys(templateParam)) {
          if(['allData','checkData'].includes(key)) continue
          if(key === 'legendData'){
            for (let key1 of Object.keys(templateParam.legendData)) {
              if(templateParam.legendData?.[key1] !== undefined){
                this.editData[targetObj][key1] = templateParam.legendData[key1]
              }
            }
          }else{
            this.editData[targetObj][key] = templateParam[key]
          }
      }

      return seriesList
    },
    _handleEchartOptions(targetObj, seriesList, hasYAxisTwo = false) {
      // 模板数据
      const templateParam = this.reportChartTemplateList[targetObj].templateParamJson
      const originalParam = this.reportChartTemplateList[targetObj].originalParamJson

      const originalObj = this.originalData[targetObj]
      const firstItem = this.firstInit[targetObj] && !originalParam
      const editItem = this.editData[targetObj]

      const options = {
        textStyle: {
          fontFamily: "Times New Roman"
        },
        backgroundColor: '#ffffff',
        title: {
          text: firstItem ? originalObj.chartTitle : editItem.chartTitle,
          left: 'center',
          top: firstItem ? originalObj.titleTop : editItem.titleTop,
          textStyle: {
            fontSize: 16,
            fontWeight: 500,
            color: "#000"
          }
        },
        grid: {
          show: true,
          top:firstItem ? originalObj.gridTop : editItem.gridTop,
          left: firstItem ? originalObj.gridLeft : editItem.gridLeft,
          right: firstItem ? originalObj.gridRight : editItem.gridRight,
          bottom: firstItem ? originalObj.gridBottom : editItem.gridBottom,
          borderWidth: 0.5,
          borderColor: "#ccc"
        },
        tooltip: {
          trigger: 'axis',
          confine: true,
          enterable: true,
          hideDelay: 300,
          extraCssText: 'max-height: 400px; overflow-y: auto; scrollbar-width: thin; scrollbar-color: #888 #f1f1f1; pointer-events: auto;',
        },
        legend: {
          data: editItem.legendRevealList,
          backgroundColor: firstItem ? originalObj.legendBgColor : editItem.legendBgColor,
          itemWidth: firstItem ? originalObj.legendWidth : editItem.legendWidth,
          itemHeight: firstItem ? originalObj.legendHeight : editItem.legendHeight,
          itemGap: firstItem ? originalObj.legendGap : editItem.legendGap,
          orient: firstItem ? originalObj.legendOrient : editItem.legendOrient,
          top: firstItem ? originalObj.legendTop : editItem.legendTop,
          right: firstItem ? originalObj.legendRight : editItem.legendRight,
          textStyle: {
            fontSize: firstItem ? originalObj.legendFontSize : editItem.legendFontSize,
            color: "#000000"
          }
        },
        xAxis: [
          {
            name: firstItem ? originalObj.XTitle : editItem.XTitle,
            type: firstItem ? originalObj.xType : editItem.xType,
            nameLocation: 'middle', // 将名称放在轴线的中间位置
            nameGap: 30,
            nameTextStyle: {
              fontSize: 14, // 可以根据需要调整字体大小
              fontWeight: 500,
              color: "#000000" // 可以根据需要调整字体大小
            },
            axisTick: {show: false},
            axisLabel: {
              show: true,
              width: 0.5,
              fontSize: 14,
              color: "#000000",
              formatter: function (value) {
                return value
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              },
              onZero: false, // 次Y轴为数值轴且包含0刻度, 确保X轴的轴线不在次Y轴的0刻度上
            },
            splitLine: {
              show: true, // 显示分隔线
              lineStyle: {
                type: "solid",
                width: 0.5
              }
            },
            minInterval: 1,
            axisPointer: {
              label: {
                show: false,
                formatter: function(params) {
                  return params.value + '';
                }
              },
            },
          }
        ],
        yAxis: [
          {
            name: firstItem ? originalObj.YTitle : editItem.YTitle,
            type: firstItem ? originalObj.yType : editItem.yType,
            nameGap: firstItem ? originalObj.yTitleLetf : editItem.yTitleLetf,
            position: 'left',
            min: firstItem ? originalObj.yMin : editItem.yMin,
            max: firstItem ? originalObj.yMax : editItem.yMax,
            interval: firstItem ? originalObj.yInterval : editItem.yInterval,
            nameLocation: 'middle', // 将名称放在轴线的起始位置
            nameRotate: 90, // 旋转角度，使名称竖排
            nameTextStyle: {
              fontSize: 14, // 可以根据需要调整字体大小
              fontWeight: 500,
              color: "#000000"
            },
            splitLine: {
              show: true,  // 显示分隔线
              lineStyle: {
                type: 'solid',  // 设置分隔线的样式，比如虚线
                width: 0.5
              }
            },
            axisTick: {
              show: false,  // 显示刻度
            },
            axisLabel: {
              show: true,
              width: 0.5,
              fontSize: 14,
              color: "#000000",
              formatter: function (value) {
                return value
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              }
            },
          }
        ],
        series: seriesList
      }

      if (!targetObj.includes('RetRate')) {
        options.dataZoom = {type: 'inside'}
      }

      // 非首次加载才需要
      //处理图例
      //图例名称
      if (!this.firstInit[targetObj]  || templateParam) {
        const newSeriesList = []
        _.cloneDeep(seriesList).forEach(v => {
          const haveNameList = editItem.legendEditName.filter(filterItem => filterItem.originName === v.name && filterItem.newName)
          v.name = haveNameList.length === 0 ? v.name : haveNameList[0].newName
          newSeriesList.push(v)
        })
        options.series = newSeriesList

        const legend = []
        editItem.legendSort.forEach(v => {
          if (editItem.legend.includes(v) && editItem.legendRevealList.includes(v)) {
            const haveList = editItem.legendEditName.filter(filterItem => filterItem.originName === v && filterItem.newName)
            legend.push(haveList.length === 0 ? v : haveList[0].newName)
          }
        })
        options.legend.data = legend
      }


      // 如果非首次编辑
      // 默认是水平，如果是水平，就不添加，垂直才添加
      if (editItem.legendOrient === 'vertical') {
        options.legend.orient = editItem.legendOrient
      }

      // X轴可能没有最大最小值、间隔
      if (!firstItem && editItem.xInterval && editItem.xType === 'value') {
        options.xAxis[0].min = editItem.xMin
        options.xAxis[0].max = editItem.xMax
        options.xAxis[0].interval = editItem.xInterval
      }
      if (!firstItem && editItem.yType === 'value') {
        options.yAxis[0].min = editItem.yMin
        options.yAxis[0].max = editItem.yMax
        options.yAxis[0].interval = editItem.yInterval
      }
      if (hasYAxisTwo && !firstItem && editItem.yType2 === 'value') {
        options.yAxis[1].min = editItem.yMin2
        options.yAxis[1].max = editItem.yMax2
        options.yAxis[1].interval = editItem.yInterval2
      }
      return options
    },

    _handleInitLegendList(seriesList, legend) {
      if (this.echartObj[this.editObj]) this.echartObj[this.editObj].dispose();
      this.echartObj[this.editObj] = this.echarts.init(document.getElementById(this.editObj), 'walden', { devicePixelRatio: 2 })

      let chartOption = this._handleEchartOptions(this.editObj, seriesList, false)

      chartOption.legend.data = legend
      this.echartObj[this.editObj].setOption(chartOption)
    },
    xaxisTypeChange(event) {
      this.$set(this.queryParam, 'xaxisType', event.target.value)
      this.refreshChartsAndParam('xaxisType')
    },
    tempRiseTypeChange(event) {
      this.$set(this.queryParam, 'tempRiseType', event.target.value)
      this.refreshChartsAndParam('tempRiseType')
    },
    refreshChartsAndParam(changeType) {
      // 重新加载Echarts图
      this.isLoading = true
      setTimeout(() => {
        for (let i = 2; i < this.editObjList.length; i++) {
          const targetObj = this.editObjList[i]
          if (changeType === 'tempRiseType' && !targetObj.includes('tempRise')) {
            continue; // 改变温升类型只更新温升图
          }

          this.titleDataObj[targetObj].XTitle = this.queryParam.xaxisType === 'Energy' ? 'Energy / Wh' : 'Capacity / Ah'
          this.originalData[targetObj].XTitle = this.titleDataObj[targetObj].XTitle

          let tableIndex = 0
          if (targetObj.includes('-')) {
            tableIndex = parseInt(targetObj.split('-')[1])
          }
          this.firstInit[targetObj] = true
          const yaxisOneList = _.cloneDeep(this.allDataJson.allRecordEchartList[tableIndex])
          this.initEchart(targetObj, this.queryParam.xaxisType, yaxisOneList, [], false)
        }

        this.isLoading = false
      }, 100)

      // 更改queryParam
      const json = jsonBigint({storeAsString: true})
      const paramStr = json.stringify(this.queryParam)
      commitDongLiParam({id: this.id, fileStatus: 60, queryParam: paramStr})
      this.data.queryParam = paramStr // 编辑模型参数会用到queryParam
    },
    handleChoose(targetObj, targetIndex) {
      const divElement = this.$refs.wrapper;
      divElement.scrollTop = targetIndex === 0 ? 0 : 10 + 30 + 10 + 10 + targetIndex * (2 + 32 + 415);
      this.$set(this.animateObj, targetObj, true);

      setTimeout(() => {
        this.$set(this.animateObj, targetObj, false);
      }, 2000)
    },

    onPageChange(page, pageSize, index) {
      this.$set(this.tableLodingList, index, true)

      this.tablePageObjList[index].current = page
      this.tablePageObjList[index].size = pageSize
      getDongLiData({
        id: this.id,
        tableIndex: index,
        tablePageObj: {current: this.tablePageObjList[index].current, size: this.tablePageObjList[index].size},
      }).then(res => {
        if (res.data) {
          const json = jsonBigint({storeAsString: true});
          const pageObj = json.parse(res.data.allDataJson);
          // 数据分页
          this.tablePageObjList[index] = pageObj || {};
        }
      }).finally(() => {
        this.$set(this.tableLodingList, index, false)
      })
    },
    reExport(rebuildFlag) {
      this.$store.commit('setTaskFilterData', this.data);
      if (rebuildFlag) {
        this.$router.push('/dong_li_report_build');
      } else {
        this.$router.push('/dong_li_report_build?id=' + this.data.id);
      }
    },
    exportTempOrCRate() {
      this.isLoading = true
      exportDongLiData({
        id: this.id
      }).then(res => {
        if (res.data.size > 0) {
          const reportName = this.data.reportName
          const fileName = this.prefixName + '数据_' + reportName + '.xlsx'
          downloadfile1(res, fileName)
        } else {
          this.$message.warning("暂无数据！")
        }
      }).catch(error => {
        console.error(error)
        this.$message.warning("导出失败！")
      }).finally(() => {
        this.isLoading = false
      })
    },
  },
}
</script>

<style lang="less" scoped>
// 通用
.mt2 {
  margin-top: 2px;
}

.mt10 {
  margin-top: 10px;
}

.mr10 {
  margin-right: 10px;
}

.ml10 {
  margin-left: 10px;
}

.wrapper {
  height: 100vh;
  overflow-y: scroll;
  padding: 10px;
  margin: 0 0 0 -40px;
  background-color: #f0f2f5;
}

.head_title {
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.head_title::before {
  width: 8px;
  background: #1890ff;
  margin-right: 8px;
  content: "\00a0"; //填充空格
}

.reveal-text-opacity {
  /* font-size: 14px; */
  font-family: 'Times New Roman';
  opacity: 0;
  padding: 0;
  margin: 0;
  line-height: 1;
}

.right-top {
  position: absolute;
  top: 10px;
  right: 10px;
  height: 30px;
  display: flex;
  align-items: end;
}

.flex-sb-center-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.block {
  height: fit-content;
  padding: 10px;
  background: #fff;
  border-radius: 10px;
  //box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
  position: relative;
}

.all-wrapper {
  padding: 0 0 10px;
  display: flex;
  justify-content: space-between;
}

.left-content {
  width: calc(595px + 20px);
  margin-right: 10px;
}

.right-content {
  width: calc(100% - 595px - 10px - 10px - 10px);
}

/deep/ .ant-btn-div .ant-btn > i,
/deep/ .ant-btn-div .ant-btn > span {
  display: inline-block;
}

.table-title-div {
  height: 32px;
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}

/deep/ .ant-table-thead {
  position: sticky;
  top: 0;
  z-index: 12;
}

/deep/ .ant-table-body {
  border: 1px solid #e8e8e8;
  overflow-y: auto; /* 垂直滚动条 */
  overflow-x: scroll; /* 强制显示水平滚动条 */
  margin: 0;
}

/deep/ .retention-table .ant-table-body {
  max-height: 382px !important; /* 设置容器最大高度 */
}

/deep/ .record-table .ant-table-body {
  max-height: 831px !important; /* 设置容器最大高度 */
}

/deep/ .ant-table-thead > tr > th {
  padding: 2px !important;
  font-size: 13px !important;
}

/deep/ .ant-table-tbody > tr > td {
  padding: 0px !important;
  height: 32px !important;
  font-size: 12px !important;
}

/deep/ .ant-table-body::-webkit-scrollbar {
  height: 10px;
  width: 5px;
}

/deep/ .ant-table-body::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;

  background: #dddbdb;
}

/deep/ .ant-table-body::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: #f1f1f1;
}

/deep/ .ant-table-pagination.ant-pagination {
  float: right;
  margin:2px 0 0;
  font-size: 12px;
}

.action-bar {
  font-size: 20px;
  color: #333;
  position: fixed;
  bottom: 20px;
  left: 20px;
  z-index: 10;
  padding: 10px 15px;
  border-radius: 5px;
  backdrop-filter: blur(6px);
  background-color:rgba(238, 238, 238,.3);
  cursor: pointer;
  box-shadow: 2px 2px 4px 0 #dee4e6,-2px -2px 4px 0 #fff;
}
/deep/.icon{
  position: unset;
}

.navigate-item {
  padding: 8px 12px;
  font-size: 14px;
  cursor: pointer;
}
</style>