<template>
	<a-layout :class="['layout', device]">
		<!-- SideMenu -->
		<!-- <a-drawer
      v-if="isMobile()"
      placement="left"
      :wrapClassName="`drawer-sider ${navTheme}`"
      :closable="false"
      :visible="collapsed"
      @close="drawerClose"
    >
      <side-menu
        mode="inline"
        :menus="menus"
        :theme="navTheme"
        :collapsed="false"
        :collapsible="true"
        @menuSelect="menuSelect"
      ></side-menu>
    </a-drawer>-->
		<!-- !collapsed && -->
		<global-header
			v-if="$route.meta.showNav == 1"
			:mode="layoutMode"
			:menus="menus"
			:theme="navTheme"
			:blankMenus="blankMenus"
			:collapsed="collapsed"
			:device="device"
			@toggle="toggle"
			@setShowNav="setShowNav"
			@togglefalse="togglefalse"
		/>

		<a-layout
			:class="[layoutMode, `content-width-${contentWidth}`]"
			:style="{ paddingLeft: contentPaddingLeft, minHeight: '100vh' }"
		>
			<side-menu
				v-if="isSideMenu() && showSideNav == 1"
				mode="inline"
				:menus="menus"
				:theme="navTheme"
				:collapsed="collapsed"
				:collapsible="true"
				@mouseenter="mouseenter"
				@mouseleave="mouseleave"
				:style="{ zIndex: 100, paddingTop: fixedHeader ? '40px' : '0', position: 'fixed', left: 0 }"
			></side-menu>

			<!-- layout header -->

			<!-- layout content -->
			<a-layout-content
				:style="{
					margin: '0',
					paddingTop: fixedHeader && $route.meta.showNav == 1 ? '40px' : '0',
					paddingLeft: '40px'
				}"
			>
				<!-- <multi-tab v-if="multiTab"></multi-tab> -->
				<transition name="page-transition">
					<route-view :key="$route.fullPath" />
				</transition>
			</a-layout-content>

			<!-- layout footer -->
			<!-- <a-layout-footer>
        <global-footer />
      </a-layout-footer> -->

			<!-- Setting Drawer (show in development mode) -->
			<!-- <setting-drawer v-if="!production"></setting-drawer> -->
		</a-layout>
	</a-layout>
</template>

<script>
import { triggerWindowResizeEvent } from "@/utils/util"
import { mapState, mapActions } from "vuex"
import { mixin, mixinDevice } from "@/utils/mixin"
import config from "@/config/defaultSettings"

import RouteView from "./RouteView"
import SideMenu from "@/components/Menu/SideMenu"
import GlobalHeader from "@/components/GlobalHeader"
import GlobalFooter from "@/components/GlobalFooter"
import SettingDrawer from "@/components/SettingDrawer"
import { convertRoutes } from "@/utils/routeConvert"
import Vue from "vue"
import { ACCESS_TOKEN, ALL_APPS_MENU } from "@/store/mutation-types"
export default {
	name: "BasicLayout",
	mixins: [mixin, mixinDevice],
	components: {
		RouteView,
		SideMenu,
		GlobalHeader,
		GlobalFooter,
		SettingDrawer
	},
	data() {
		return {
			production: config.production,
			collapsed: false,
			menus: [],
			blankMenus: [],
			showSideNav: 1
		}
	},
	computed: {
		...mapState({
			// 动态主路由
			mainMenu: state => state.permission.addRouters
		}),
		contentPaddingLeft() {
			if (!this.fixSidebar || this.isMobile()) {
				return "0"
			}
			if (this.sidebarOpened) {
				return "200px"
			}
			return "80px"
		}
	},
	watch: {
		sidebarOpened(val) {
			this.collapsed = !val
		},
		// 菜单变化
		mainMenu(val) {
			this.setMenus()
		}
		/* time(val){
      this.setMenus()
    } */
	},
	created() {
		this.setMenus()
		/* const routes = convertRoutes(this.mainMenu.find(item => item.path === '/'))
    this.menus = (routes && routes.children) || [] */
		this.collapsed = !this.sidebarOpened
	},
	mounted() {
		this.setShowNav(this.$route.meta.showSideNav)
		const userAgent = navigator.userAgent
		if (userAgent.indexOf("Edge") > -1) {
			this.$nextTick(() => {
				this.collapsed = !this.collapsed
				setTimeout(() => {
					this.collapsed = !this.collapsed
				}, 16)
			})
		}
	},
	methods: {
		...mapActions(["setSidebar"]),
		// 重新生成
		setMenus() {
			const routes = convertRoutes(this.mainMenu.find(item => item.path === "/"))
			const apps = Vue.ls.get(ALL_APPS_MENU)
			let app = apps.find(e => e.active == true)
			if (app) {
				this.menus = (routes && routes.children.filter(item => item.meta.code == app.code)) || []
				//this.menus = (routes && routes.children.filter(item=>item.meta.target !="_blank" && item.meta.code == app.code)) || []
			}
			//this.blankMenus = routes.children.filter(item=>item.meta.target =="_blank")
		},
		toggle() {
			this.collapsed = !this.collapsed
			this.setSidebar(this.collapsed)
			// triggerWindowResizeEvent()
		},
		setShowNav(val) {
			this.showSideNav = val
			this.setMenus()
		},
		togglefalse() {
			this.collapsed = false
			this.setSidebar(this.collapsed)
			// triggerWindowResizeEvent()
		},
		mouseenter() {
			this.collapsed = true
			this.setSidebar(this.collapsed)
		},
		mouseleave() {
			this.collapsed = false
			this.setSidebar(this.collapsed)
		},
		paddingCalc() {
			let left = ""
			if (this.sidebarOpened) {
				left = this.isDesktop() ? "200px" : "0"
			} else {
				left = (this.isMobile() && "0") || (this.fixSidebar && "0") || "0"
			}
			return left
		},
		menuSelect() {},
		drawerClose() {
			this.collapsed = false
		}
	}
}
</script>

<style lang="less">
/*
 * The following styles are auto-applied to elements with
 * transition="page-transition" when their visibility is toggled
 * by Vue.js.
 *
 * You can easily play with the page transition by editing
 * these styles.
 */

.page-transition-enter {
	opacity: 0;
}

.page-transition-leave-active {
	opacity: 0;
}

.page-transition-enter .page-transition-container,
.page-transition-leave-active .page-transition-container {
	-webkit-transform: scale(1.1);
	transform: scale(1.1);
}
</style>
