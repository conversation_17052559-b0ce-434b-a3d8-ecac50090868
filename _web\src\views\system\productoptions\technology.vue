<template>
	<div class="product_width">
		<a-spin :spinning="loading">
			<!-- 筛选区域 start -->
			<div class="table-page-search-wrapper">
				<a-form layout="inline">
					<a-row :gutter="48">
						<a-col :md="6" :sm="24">
							<a-form-item label="立项日期">
								<a-range-picker
									class="filter-form"
									:placeholder="['开始日期', '结束日期']"
									size="small"
									@change="dateChange"
								/>
							</a-form-item>
						</a-col>
						<!-- <a-col :md="4" :sm="24">
						<a-form-item label="产品分类">
							<treeselect
								class="filter-form"
								:limit="1"
								@input="change"
								:max-height="200"
								placeholder="请选择产品分类"
								value-consists-of="BRANCH_PRIORITY"
								v-model="queryparam.productCates"
								:options="typeOptions"
								:multiple="true"
							/>
						</a-form-item>
					</a-col> -->
						<a-col :md="5" :sm="24">
							<a-form-item label="产品类别">
								<div class="filter-box">
									<treeselect
										class="filter-form"
										:limit="1"
										@input="change"
										:max-height="200"
										placeholder="请选择产品类别"
										value-consists-of="BRANCH_PRIORITY"
										v-model="queryparam.cates"
										:multiple="true"
										:options="cate"
									/>
								</div>
							</a-form-item>
						</a-col>
						<a-col :md="5" :sm="24">
							<a-form-item label="产品状态">
								<treeselect
									class="filter-form"
									:limit="1"
									@input="change"
									:max-height="200"
									placeholder="请选择产品状态"
									value-consists-of="BRANCH_PRIORITY"
									v-model="queryparam.states"
									:multiple="true"
									:options="statuses"
								/>
							</a-form-item>
						</a-col>

						<a-col :md="3" :sm="24">
							<a-form-item label="">
								<a-input
									class="filter-form"
									size="small"
									@keyup.enter.native="change"
									v-model="queryparam.keyword"
									placeholder="请输入产品名称"
								>
									<a-icon slot="suffix" type="search" style="color: rgba(0,0,0,.45)" />
								</a-input>
							</a-form-item>
						</a-col>

						<a-col :md="1" :sm="24" :style="{ float: 'right' }">
							<div class="table-page-search-submitButtons" :style="{ float: 'right' }">
								<a-button size="small" style="margin-left: 120px;" type="primary" @click="query">查询</a-button>
								<!-- <a-button size="small" style="margin-left: 20px;margin-top:6px" @click="resetquery">重置</a-button> -->
							</div>
						</a-col>
					</a-row>
				</a-form>
			</div>
			<!-- 筛选区域 end -->

			<!-- 表格 start -->
			<div>
				<a-table
					ref="table2"
					:style="`height:${tableHeight}px;`"
					:rowKey="record => record.issueId + record.productCate"
					:columns="columns_process"
					:dataSource="loadData"
				>
					<span slot="productCate" slot-scope="text, record">
						{{ record.productOrProject == 1 ? record.productCateParent + (text != "" ? "->" + text : "") : "" }}
					</span>

					<span slot="rollCore" slot-scope="text">
						<span v-if="text">{{ coreMap[text] }}</span>
						<span v-else></span>
					</span>

					<div slot="structurePic" slot-scope="text, record" :style="{ textAlign: 'center' }">
						<a
							v-if="record.structurePic && record.structurePic.length > 0"
							target="_blank"
							:href="record.structurePic[0].url + '#navpanes=0'"
							>{{ record.structurePic[0].name }}</a
						>
						<span v-else></span>
					</div>

					<div slot="pic" slot-scope="text, record" :style="{ textAlign: 'center' }">
						<a v-if="record.pic && record.pic.length > 0" target="_blank" :href="record.pic[0].url + '#navpanes=0'">{{
							record.pic[0].name
						}}</a>
						<span v-else></span>
					</div>
				</a-table>
			</div>
			<!-- 表格 end -->
		</a-spin>
	</div>
</template>

<script>
import { dashboardParams } from "@/api/modular/system/dashboardManage"
import { getCatesTree } from "@/api/modular/system/report"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"

import { classType, statusType } from "@/utils/enum.js"
export default {
	components: {
		Treeselect
	},
	data() {
		return {
			previewVisible: false,
			previewImage: "",

			coreMap: {
				1: "卷绕",
				2: "叠片"
			},
			activeKey: "1",
			typeOptions: [
				{
					id: 1,
					label: "预研产品"
				},
				{
					id: 2,
					label: "A|B新产品"
				},
				{
					id: 3,
					label: "试产新产品"
				},
				{
					id: 4,
					label: "量产品"
				},
				{
					id: 5,
					label: "其他"
				},
				{
					id: 6,
					label: "停止"
				}
			],
			statuses: [
				{
					id: 0,
					label: "立项讨论"
				},
				{
					id: 1,
					label: "A/B样"
				},
				{
					id: 2,
					label: "C/D样"
				},
				{
					id: 3,
					label: "暂停开发"
				},
				{
					id: 4,
					label: "停产"
				},
				{
					id: 5,
					label: "SOP"
				}
			],
			queryparam: {
				productCates: [], //产品分类
				cates: [],
				states: [],
				keyword: null,
				projectId: null
			},
			loading: true,
			columns: [
				{
					title: "序号",
					dataIndex: "no",
					width: 60,
					align: "center",
					customRender: (text, record, index) => {
						if (record.productOrProject == 1) {
							return `${index + 1}`
						}
						return ""
					}
				},
				{
					title: "产品名称",
					align: "center",
					dataIndex: "productProjectName",
					scopedSlots: {
						customRender: "productProjectName"
					}
				},
				/* {
                    title: '项目名称',
                    dataIndex: 'projectName',
                },
                {
                    title: '项目等级',
                    align: 'center',
                    dataIndex: 'productLevel',
                    customRender: (text, record, index) => {
                        return text+'级'
                    }
                },
                {
                    title: '项目阶段',
                    align: 'center',
                    dataIndex: 'mstatus',
                    scopedSlots: {
                        customRender: 'mstatus'
                    }
                }, */
				{
					title: "客户",
					align: "center",
					dataIndex: "customer"
				},

				{
					title: "E/D(Wh/kg)",
					dataIndex: "density",
					align: "center"
				},

				{
					title: "额定容量放电倍率(C)",
					align: "center",
					dataIndex: "capacity"
				},
				{
					title: "额定容量(Ah)",
					align: "center",
					dataIndex: "ah"
				},
				{
					title: "标称电压(V)",
					align: "center",
					dataIndex: "voltage"
				},

				{
					title: "ACR(mΩ)≤",
					align: "center",
					dataIndex: "acr"
				},

				{
					title: "DCR(mΩ)≤",
					align: "center",
					dataIndex: "dcr"
				},

				{
					title: "重量(g)",
					align: "center",
					dataIndex: "weight"
				},
				{
					title: "循环",
					align: "center",
					dataIndex: "loop"
				},
				{
					title: "性能描述",
					align: "center",
					dataIndex: "performance"
				}
			],
			columns_process: [
				{
					title: "序号",
					dataIndex: "no",
					align: "center",
					customRender: (text, record, index) => {
						if (record.productOrProject == 1) {
							return `${index + 1}`
						}
						return ""
					}
				},

				{
					title: "产品名称",
					align: "center",
					dataIndex: "productProjectName",
					scopedSlots: {
						customRender: "productProjectName"
					}
				},

				{
					title: "卷芯工艺(卷绕/叠片)",
					align: "center",
					dataIndex: "rollCore",
					scopedSlots: {
						customRender: "rollCore"
					}
				},
				{
					title: "卷芯出极耳方式",
					align: "center",
					dataIndex: "earbud"
				},
				{
					title: "电芯端子方式",
					align: "center",
					dataIndex: "terminal"
				},
				{
					title: "裸电芯数量",
					align: "center",
					dataIndex: "bareWireCount"
				},
				{
					title: "内部结构类型",
					align: "center",
					dataIndex: "structurePic",
					scopedSlots: {
						customRender: "structurePic"
					}
				},
				{
					title: "产品图片",
					dataIndex: "pic",
					align: "center",
					scopedSlots: {
						customRender: "pic"
					}
				}
				// {
				// 	title: "客户",
				// 	align: "center",
				// 	dataIndex: "customer"
				// },
				// {
				// 	title: "产品状态",
				// 	align: "center",
				// 	dataIndex: "state",
				// 	customRender: text => statusType[text]
				// },
				// {
				// 	title: "产品分类",
				// 	align: "center",
				// 	dataIndex: "productClassification",
				// 	customRender: text => classType[text]
				// },
				// {
				// 	title: "产品类别",
				// 	align: "center",
				// 	dataIndex: "productCate",
				// 	scopedSlots: {
				// 		customRender: "productCate"
				// 	}
				// },
			],
			loadData: [],
			totalData: [],
			cate: [],

			parentId: null,
			cateId: null,
			projectId: null
		}
	},
	props: {
		// 表格高度
		tableHeight: {
			type: Number,
			default: 0
		},
		// 表格滚动高度
		scrollHeigh: {
			type: Number,
			default: 0
		}
	},
	watch: {
		loadData(newVal, oldVal) {
			if (this.loadData.length > 0) {
				// 动态修改--height的值
				document.documentElement.style.setProperty(`--height`, `${this.scrollHeigh}px`)
			} else {
				// 动态修改--height的值
				document.documentElement.style.setProperty(`--height`, "50px")
			}
		}
	},
	methods: {
		handleCancel() {
			this.previewVisible = false
		},
		async handlePreview(file) {
			this.previewImage = file
			this.previewVisible = true
		},
		query() {
			if (this.parentId != null) {
				let index = this.queryparam["cates"].findIndex(e => e == this.parentId)
				this.queryparam["cates"].splice(index, 1)
				this.parentId = null
			}
			if (this.cateId != null) {
				let index = this.queryparam["cates"].findIndex(e => e == this.cateId)
				this.queryparam["cates"].splice(index, 1)
				this.cateId = null
			}

			if (this.projectId != null) {
				this.queryparam.projectId = null
				this.projectId = null
			}
			this.callFilter()
		},
		onTabChange(key) {
			this.activeKey = key
		},
		change() {
			this.callFilter()
		},
		resetquery() {
			this.queryparam = {
				productCates: [],
				cates: [],
				states: [],
				keyword: null,
				projectId: null
			}
			let filterData = JSON.parse(JSON.stringify(this.totalData))
			this.loadData = filterData
		},
		callGetTree() {
			this.loading = true
			getCatesTree()
				.then(res => {
					if (res.result) {
						let cate = []
						for (const item of res.data) {
							let $item = {
								id: parseInt(item.value),
								label: item.title
							}
							if (item.children) {
								$item.children = []
								for (const _item of item.children) {
									$item.children.push({
										id: parseInt(_item.value),
										label: _item.title
									})
								}
							}
							cate.push($item)
						}
						this.cate = cate
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
					this.loading = false
				})
				.catch(err => {
					this.loading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		},

		// 数据筛选
		callFilter() {
			let filterData = JSON.parse(JSON.stringify(this.totalData))

			// 产品分类
			if (this.queryparam["productCates"].length > 0) {
				filterData = filterData.filter(
					item => this.queryparam["productCates"].join(",").indexOf(parseInt(item.productClassification)) > -1
				)
			}

			if (this.queryparam.projectId) {
				if (this.queryparam["cates"].length > 0) {
					filterData = filterData.filter(
						item =>
							this.queryparam["cates"].indexOf(parseInt(item.cateId)) > -1 ||
							this.queryparam["cates"].indexOf(parseInt(item.catepid)) > -1
					)
				}
				filterData = filterData.filter(item => item.issueId == this.queryparam.projectId)
				this.loadData = filterData
				return
			}

			if (this.queryparam["cates"].length > 0) {
				filterData = filterData.filter(
					item =>
						this.queryparam["cates"].indexOf(parseInt(item.cateId)) > -1 ||
						this.queryparam["cates"].indexOf(parseInt(item.catepid)) > -1
				)
			}
			if (this.queryparam["states"].length > 0) {
				filterData = filterData.filter(item => this.queryparam["states"].indexOf(parseInt(item.state)) > -1)
			}
			if (this.queryparam.keyword != null && this.queryparam.keyword != "") {
				filterData = filterData.filter(
					item => item.productProjectName.toLowerCase().indexOf(this.queryparam.keyword.toLowerCase()) > -1
				)
			}

			if (this.queryparam.startDate != null) {
				filterData = filterData.filter(
					item =>
						Date.parse(item.initiationDate) >= this.queryparam.startDate &&
						Date.parse(item.initiationDate) < this.queryparam.endDate
				)
			}

			this.loadData = filterData
		},

		//数据筛选
		dateChange(date, dateString) {
			if (dateString[0] != null && dateString[0] != "") {
				this.queryparam.startDate = Date.parse(dateString[0])
			} else {
				this.queryparam.startDate = null
			}
			if (dateString[1] != null && dateString[1] != "") {
				this.queryparam.endDate = Date.parse(dateString[1])
			} else {
				this.queryparam.endDate = null
			}
			this.callFilter()
		},

		handleOk() {
			this.callDashboardParams()
		},
		callDashboardParams() {
			this.loading = true
			dashboardParams({})
				.then(res => {
					if (res.success) {
						this.totalData = JSON.parse(JSON.stringify(res.data))
						this.loadData = res.data
						if (this.$route.query.parentId) {
							this.projectId = parseInt(this.$route.query.parentId)
							this.queryparam["cates"].push(parseInt(this.$route.query.parentId))
						}
						if (this.$route.query.cateId) {
							this.cateId = parseInt(this.$route.query.cateId)
							this.queryparam["cates"].push(parseInt(this.$route.query.cateId))
						}
						if (this.$route.query.projectId) {
							this.projectId = parseInt(this.$route.query.projectId)
							this.queryparam.projectId = parseInt(this.$route.query.projectId)
						}
						this.callFilter()
					} else {
						this.$message.error(res.message, 1)
					}
					this.loading = false
				})
				.catch(err => {
					this.loading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		}
	},
	created() {
		this.callDashboardParams()
		this.callGetTree()

		// 动态修改--height的值
		document.documentElement.style.setProperty(`--height`, `${this.scrollHeigh}px`)
	}
}
</script>

<style lang="less" scoped>
@import "./productoption.less";

:root {
	--height: 600px;
}

/deep/.ant-table-body {
	height: var(--height) !important;
	overflow-y: scroll;
}
</style>
