<template>
  <div class="integrated-view">
    <page-header title="公式拟合系统" subtitle="用于数据拟合和分析的应用程序"></page-header>
    <div class="content-wrapper">
      <a-tabs
        v-model="activeTabKey"
        class="unified-tabs formula-tabs"
        @change="handleTabChange"
      >
        <a-tab-pane key="1" tab="生成公式">
          <template slot="tab">
            <span>
              <a-icon type="form" />
              生成公式
            </span>
          </template>
          <formula-editor-page />
        </a-tab-pane>

        <a-tab-pane key="2" tab="查询公式">
          <template slot="tab">
            <span>
              <a-icon type="search" />
              查询公式
            </span>
          </template>
          <formula-query-page />
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script>
import FormulaEditorPage from '@/views/formula/FormulaEditorPage.vue';
import FormulaQueryPage from '@/views/formula/FormulaQueryPage.vue';
import PageHeader from '@/components/common/PageHeader.vue';

export default {
  name: 'IntegratedView',
  components: {
    FormulaEditorPage,
    FormulaQueryPage,
    PageHeader
  },
  data() {
    return {
      activeTabKey: '1'
    };
  },
  methods: {
    handleTabChange(activeKey) {
      this.activeTabKey = activeKey;
    }
  }
}
</script>

<style scoped>
.integrated-view {
  padding: 10px;
  max-width: 100%;
  margin: 0 auto;
}

.content-wrapper {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* 标签页样式已统一到 formula.css 中 */
.formula-tabs :deep(.ant-tabs-content) {
  min-height: 600px;
}

/* 高对比度模式支持已统一到 formula.css 中 */
</style>