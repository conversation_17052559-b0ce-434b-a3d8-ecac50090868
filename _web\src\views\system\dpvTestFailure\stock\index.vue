<template>
	<div class="container">

		<!-- 标题 start -->
		<div class="head-title">
			<div class="line mr10"></div>
      <span class="title">测试失效样品追溯管理表</span>
		</div>

    <div class="content-wrapper">
      <div class="content-left" id="content-left">
        <div class="block">
          <div class="top" :style="{zoom:clientHeight>700?1:0.8}">
            <a-select style="width: 140px" showArrow="true" mode="multiple" :maxTagCount="1" class="filter-select" @change="$refs.table.refresh()" v-model="queryParam.testTypeList" placeholder="请选择试验类型" allow-clear >
              <a-select-option v-for="(item,index) in testTypeList" :key="index" :value="item.code" >{{ item.value }}</a-select-option>
            </a-select>
            <div class="top-title">
              <span>样品总数</span>
            </div>
            <div class="sample-num" >
              <span class="sample-num-sub" v-for="num in sampleNum">
                {{num}}
              </span>
            </div>
            <div class="top-block1">
<!--              <span>样品总数</span>-->
              <div class="block1-sub" v-for="object in sampleType">
                <div class="sub-title">
                  <img :src="require('./icon/' + object.icon)"/>
<!--                  <img :src="require('./icon/<EMAIL>')"/>-->
                  <span>{{object.title}}</span>
                </div>
                <div class="sub-num">
                  {{object.num}}
                </div>
              </div>
            </div>

            <div class="top-block2">
              <span>第四实验室</span>
              <span>20个</span>
            </div>

            <div class="top-block3">
              <span>第六实验室(JM)</span>
              <span>20个</span>
            </div>

            <div class="top-block4">
              <span>第六实验室(HZ)</span>
              <span>20个</span>
            </div>

          </div>
          <div class="bottom" :style="{zoom:clientHeight>700?1:0.8}">
            <a-select  showArrow="true" mode="multiple" style="width: 140px" :maxTagCount="1"  @change="$refs.table.refresh()" v-model="queryParam.testTypeList" placeholder="请选择试验类型" allow-clear >
              <a-select-option v-for="(item,index) in testTypeList" :key="index" :value="item.code" >{{ item.value }}</a-select-option>
            </a-select>
            <!-- <div class="chart" ref="failureCellView">
            </div> -->
			<div class="charts">
				<div class="chart-item">
					<div class="chart-label">研发测试</div>
					<div class="chart-wrapper">
						<div class="chart-instore" style="width:30px">30</div>
						<div class="chart-analyse" style="width:20px">20</div>
						<div class="chart-scrap" style="width:30px">30</div>
					</div>
				</div>
				<div class="chart-item">
					<div class="chart-label">产品验证测试</div>
					<div class="chart-wrapper">
						<div class="chart-instore" style="width:30px">30</div>
						<div class="chart-analyse" style="width:20px">20</div>
						<div class="chart-scrap" style="width:30px">30</div>
					</div>
				</div>
				<div class="chart-item">
					<div class="chart-label">产品鉴定测试</div>
					<div class="chart-wrapper">
						<div class="chart-instore" style="width:30px">30</div>
						<div class="chart-analyse" style="width:20px">20</div>
						<div class="chart-scrap" style="width:30px">30</div>
					</div>
				</div>
			</div>
			<div class="chart-titles">
				<div class="chart-li">
					<div class="item-block"></div>
					<div class="item-txt">在库</div>
				</div>
				<div class="chart-li">
					<div class="item-block b1"></div>
					<div class="item-txt">拆解分析</div>
				</div>
				<div class="chart-li">
					<div class="item-block b2"></div>
					<div class="item-txt">报废</div>
				</div>
			</div>
          </div>

        </div>
      </div>
      <div class="content-right" id="content-right">
        <div class="filter-wrapper mt5" >
          <pbiTabs class="filter-left" :tabsList="laboratoryList" :activeKey="laboratoryId" @clickTab="handleTabsChange"></pbiTabs>
          <div class="filter-right">
<!--            <a-button class="mr10" @click="resetSearch" type="primary">入库申请</a-button>-->
<!--            <a-button class="mr10" @click="resetSearch" type="primary">出库申请</a-button>-->
            <img class="right-img-button" @click="instoreRequestClick" src="./icon/ruku2.png" />
            <img class="right-img-button" @click="outstoreRequestClick" src="./icon/outstoreRequest.png" />
          </div>
        </div>
        <!-- 表格 start -->
        <div class="tab" :style="{borderRadius:laboratoryId == '' ? '0 10px 10px 10px' : '10px' }">
          <!-- 筛选 start -->
          <pbiSearchContainer style="padding: 8px 8px 0 0">
            <pbiSearchItem label='样品状态' :span="6">
              <a-select mode="multiple" :maxTagCount="1" class="filter-select" @change="$refs.table.refresh()" v-model="queryParam.sampleStatusList" placeholder="请选择样品状态" allow-clear >
                <a-select-option v-for="(item,index) in sampleStatusList" :key="index" :value="item.code" >{{ item.value }}</a-select-option>
              </a-select>
            </pbiSearchItem>
            <pbiSearchItem label='试验类型' :span="6">
              <a-select mode="multiple" :maxTagCount="1" class="filter-select" @change="$refs.table.refresh()" v-model="queryParam.testTypeList" placeholder="请选择试验类型" allow-clear >
                <a-select-option v-for="(item,index) in testTypeList" :key="index" :value="item.code" >{{ item.value }}</a-select-option>
              </a-select>
            </pbiSearchItem>
            <pbiSearchItem label='测试类别' :span="6">
              <a-select mode="multiple" :maxTagCount="1" class="filter-select" @change="$refs.table.refresh()" v-model="queryParam.testCateList" placeholder="请选择测试类别" allow-clear >
                <a-select-option v-for="(item,index) in testCateList" :key="index" :value="item.code" >{{ item.value }}</a-select-option>
              </a-select>
            </pbiSearchItem>
            <pbiSearchItem label='失效类别' :span="6" v-if='isShowAllSearch'>
              <a-select mode="multiple" :maxTagCount="1" class="filter-select" @change="$refs.table.refresh()" v-model="queryParam.failureCateList" placeholder="请选择失效类别" allow-clear >
                <a-select-option v-for="(item,index) in failureCateList" :key="index" :value="item.code" >{{ item.value }}</a-select-option>
              </a-select>
            </pbiSearchItem>
            <pbiSearchItem label='产品名称'  :span="6" v-if='isShowAllSearch'>
              <a-input size='small' class="filter-input" @change="$refs.table.refresh()" v-model="queryParam.productName" placeholder="请输入产品名称" />
            </pbiSearchItem>
            <pbiSearchItem label='文件编号' :span="6" v-if='isShowAllSearch'>
              <a-input size='small' class="filter-input" @change="$refs.table.refresh()" v-model="queryParam.fileCode" placeholder="请输入文件编号" />
            </pbiSearchItem>
            <pbiSearchItem label='测试项目名称' :span="6" v-if='isShowAllSearch'>
              <a-input size='small' class="filter-input" @change="$refs.table.refresh()" v-model="queryParam.testProjectName" placeholder="测试项目名称" />
            </pbiSearchItem>
            <pbiSearchItem type='btn' :span="isShowAllSearch ? 6 : 6">
              <div class="secondary-btn">
                <a-button class="mr10" @click="$refs.table.refresh()" type="primary">查询</a-button>
              </div>
              <div class="secondary-btn">
                <a-button class="mr10" @click="resetSearch">重置</a-button>
              </div>
              <div class='toggle-btn'>
                <a-button size='small' type='link' @click='handleChangeSearch'>
					{{ isShowAllSearch ? '收起' : '展开' }}
					<span v-if='isShowAllSearch'>
						<a-icon type='double-left'/>
					</span>
					<span v-else>
						<a-icon type='double-right'/>
					</span>
                </a-button>
              </div>
            </pbiSearchItem>
          </pbiSearchContainer>
          <!-- 筛选 end -->
          <s-table :columns="tableColumns"
                   ref="table"
                   :data="loadData"
                   :loading="searchLoading"
                   :rowKey="(record) => record.id"
                   bordered
                   size="middle">
        <span slot="fileCode" slot-scope="text,record">
          <span v-if="record.fileName == null">{{text}}</span>
          <a v-else-if="record.fileName.includes('pdf') || record.fileName.includes('PDF') || record.fileName.includes('png') || record.fileName.includes('jpg') || record.fileName.includes('jpeg')" @click="previewFile(record.fileId)">{{text}}</a>
          <a v-else @click="callFileInfoDownload(record.fileId)">{{text}}</a>
        </span>
				<span slot="simpleText" slot-scope="text" :title="text">
					{{ text ? text : '-' }}
				</span>

            	<span slot="clampText" slot-scope="text">
					<clamp :text="text" :sourceText="text ? text.split(/[(\r\n)\r\n]+/) : ['-']" :isCenter="true"
                 :key="new Date().getTime()" />
				</span>

            	<span slot="productDepartment" slot-scope="text">
					{{ productDepartmentList.find(e => e.id == text).customvalue || text }}
				</span>

            	<span slot="stockLocate" slot-scope="text">
					{{ text ? (stockLocateList.find(e => e.id == text)?stockLocateList.find(e => e.id == text).customvalue : text) : '/' }}
				</span>

            <span slot="testCate" slot-scope="text,record">
			{{text?(testCateList.filter(e=>e.code==text).length>0?testCateList.filter(e=>e.code==text)[0].value:text):'-'}}
			</span>

        <span slot="laboratoryId" slot-scope="text,record">
          {{text?laboratoryList1.filter(e=>e.code==text)[0].value:'-'}}
        </span>

            <span slot="failureCate" slot-scope="text">
					{{ failureCateList.find(e => e.code == text).value || '-' }}
				</span>

            <span slot="reviewStatus" slot-scope="text">
					{{ reviewStatusList.find(e => e.code == text).value || '-' }}
				</span>

            <span slot="stockStatus" slot-scope="text">
					{{ stockStatusList.find(e => e.code == text).value || '-' }}
				</span>

            <span slot="fileName" slot-scope="text,record">
					<a @click="previewFile(record)">{{ text }}</a>
				</span>

            <span slot="action" slot-scope="text, record">
					<a @click="viewOcvData(record)">查看OCV</a>
					<a-divider v-if="record.stockStatus=='preInStore'" type="vertical" />
          <a-popconfirm v-if="record.stockStatus=='preInStore'"
                        placement="topRight"
                        title="确认发起入库流程？"
                        @confirm="() => startInStore(record)"
                        :disabled="inStoreDisable"
          >
              <a>发起入库</a>
          </a-popconfirm>
					<a-divider v-if="record.parentId==1&&(record.stockStatus=='inStore'||record.children.filter(e=>e.stockStatus=='inStore').length>0)" type="vertical" />
          <a-popconfirm v-if="record.parentId==1&&(record.stockStatus=='inStore'||record.children.filter(e=>e.stockStatus=='inStore').length>0)"
                        placement="topRight"
                        title="确认发起出库流程？"
                        @confirm="() => selectOutStoreCell(record)"
                        :disabled="outStoreDisable"
          >
              <a>发起出库</a>
          </a-popconfirm>
              <!--					<a @click="startOutStore(record)">发起出库</a>-->
				</span>
            <span slot="jiraProcess" slot-scope="text,record">
          <span v-if="record.failureCellInStoreIssueKey==null">入库流程</span>
          <a v-else @click="handleToJira(record.failureCellInStoreIssueKey)" >入库流程</a>
          <a-divider type="vertical" />
          <span v-if="record.failureCellOutStoreIssueKey==null">出库流程</span>
          <a v-else @click="handleToJira(record.failureCellOutStoreIssueKey)" >出库流程</a>
        </span>
          </s-table>
        </div>
        <!-- 表格 end -->
      </div>
    </div>

		<!-- 弹窗选择待办推送人-->
		<div class="classmodal">
			<a-modal title="选择待办人" :width="600" :height="400" :visible="todoPushVisible"
				:confirmLoading="todoPushConfirmLoading" @ok="pushReviewTodo" @cancel="handleCancel">
				<a-spin :spinning="todoPushConfirmLoading">
					<a-form :form="form">
						<a-row :gutter="24">
							<a-col :md="20" :sm="24">
								<a-form-item label="待办推送人" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
									<a-input type="hidden"
										v-decorator="['userName', { rules: [{ required: true, message: '请选择待办推送人！' }] }]" />
									<a-dropdown v-model="dropdownvisible" placement="bottomCenter" :trigger="['click']">
										<a-button
											style="padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;">{{
												userNameDisplay ? userNameDisplay : "选择待办推送人" }}
											<a-icon type="down" /></a-button>
										<a-menu slot="overlay">
											<a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
												<a-input-search v-model="userQueryParam.searchValue" placeholder="搜索..."
													@change="todoOnSearch" />
												<s-table style="width:100%;" ref="todoTablePeople"
													:rowKey="record => record.id" :columns="selectUserColumns"
													:data="userLoadData" :customRow="todoPushCustomRow"
													:scroll="{ y: 120, x: 120 }">>
												</s-table>
											</a-spin>
										</a-menu>
									</a-dropdown>
								</a-form-item>
							</a-col>
						</a-row>
					</a-form>
				</a-spin>
			</a-modal>
		</div>

		<a-drawer :bodyStyle="{ height: '100%' }" placement="right" :closable="false" width="80%"
			:visible="filePreviewVisible" @close="filePreviewOnClose">
			<iframe :src="fileUrl" width="100%" height="100%"></iframe>
		</a-drawer>

		<addocv ref="addOcv" />
		<ocvlist ref="ocvlist" />
    <ocvConfig ref="ocvConfig" />
    <outStoreCellSelect ref="outStoreCellSelect" />

	</div>
</template>
<script>
import {clamp, STable} from '@/components'
import {getUserLists} from "@/api/modular/system/userManage";
import Vue from "vue";
import moment from "moment";
import {
  exportOcvTemplate,
  failureCellInStore,
  failureCellOutStore,
  getDpvTestFailureListPage,
  getDpvTestFailureStockList
} from "@/api/modular/system/testFailure";
import {getJiraOptionList} from "@/api/modular/system/jiraCustomTool";

import addocv from './addOcv'
import ocvlist from './ocvList'
import ocvConfig from './ocvConfig'
import outStoreCellSelect from './outStoreCellSelect'
import {exportModel} from "@/api/modular/system/testProgressManager";
import {downloadfile1} from "@/utils/util";
import {ACCESS_TOKEN} from "@/store/mutation-types";

import pbiTabs from '@/components/pageTool/components/pbiTabs.vue'
import {sysFileInfoDownload} from "@/api/modular/system/fileManage";

export default {
	components: {
		clamp,
		STable,
		addocv,
		ocvlist,
    ocvConfig,
    outStoreCellSelect,
    pbiTabs
	},
	props: {
		listType: {
			type: Number,
			default: 0
		},
		issueId: {
			type: Number,
			default: 0
		},
	},
	data() {
		return {
      //导入导出相关
      headers: {
        Authorization: "Bearer " + Vue.ls.get(ACCESS_TOKEN)
      },
      fileList: [],
      storeCellList: [],
      recordStoreCellList: [],
      outStoreCellList: [],

      loadData: parameter => {
        // this.queryParam.productDepartment = this.productDepartment
        this.queryParam.laboratoryId = this.laboratoryId
        this.queryParam.reviewStatus = 2//FA显示审核过后的数据

        return getDpvTestFailureListPage(Object.assign(parameter, this.queryParam)).then((res) => {
          this.storeCellList = res.data.rows;
          return res.data
        })
      },
      productDepartment:null,
      laboratoryId:'',
      //search
      isShowAllSearch:false,
      templateHeight: document.documentElement.clientHeight - 40 - 20 - 24 - 10 - 32 - 10 - 80,
      clientHeight: document.documentElement.clientHeight,
      tableHeight: document.documentElement.clientHeight - 40 - 20 - 24 - 10 - 32 - 10 - 92,

      sampleNum: [1,9,9,9],
      sampleType: [{icon:'<EMAIL>',title:'在库',num:800},{icon:'<EMAIL>',title:'拆解分析',num:190},{icon:'<EMAIL>',title:'报废',num:10}],

			filePreviewVisible: false,
			fileUrl: '',
			previewBaseUrl: '/api/sysFileInfo/preview?Authorization=Bearer '+Vue.ls.get('Access-Token')+'&id=',
			addVisible: false,
			addLoading: false,
			addForm: this.$form.createForm(this, { name: 'addForm' }),

      chartLaboratoryList:[],
			//{code:'动力圆柱电池研究所',value:'动力圆柱电池研究所'},{code:'方形电池研究所',value:'方形电池研究所'},{code:'新型电池研究所',value:'新型电池研究所'},{code:'V型圆柱电池研究所',value:'V型圆柱电池研究所'},{code:'动力电池研究所',value:'动力电池研究所'},{code:'储能电池研究所',value:'储能电池研究所'}
			productDepartmentList: [],
      laboratoryList:[{value:'',label:"全部"},{value:"HZ_YJ_DL_AQ",label:"第四实验室"},{value:"JM_YJ_DL_CS",label:"第六实验室(JM)"},{value:"HZ_YJ_DL_CS",label:"第六实验室(HZ)"}],
      stockLocateList:[],
      laboratoryList1:[{code:"HZ_YJ_DL_AQ",value:"第四实验室"},{code:"JM_YJ_DL_CS",value:"第六实验室(JM)"},{code:"HZ_YJ_DL_CS",value:"第六实验室(HZ)"}],
      sampleStatusList:[{ code: '在库', value: '在库' }, { code: '拆解分析', value: '拆解分析' }, { code: '报废', value: '报废' }],
			orderTypeList: [{ code: 'G圆柱', value: 'G圆柱' }, { code: 'C圆柱', value: 'C圆柱' }, { code: '方型', value: '方型' }, { code: '软包', value: '软包' }, { code: 'V圆柱', value: 'V圆柱' }],
			projectLevelList: [{ code: 'S', value: 'S' }, { code: 'A', value: 'A' }, { code: 'B', value: 'B' }, { code: 'C', value: 'C' }],
			researchStageList: [{ code: 'A样', value: 'A样' }, { code: 'B样', value: 'B样' }, { code: 'C样', value: 'C样' }],
      testTypeList:[{code:"研发测试",value:"研发测试"},{code:"产品验证测试",value:"产品验证测试"},{code:"产品鉴定测试",value:"产品鉴定测试"}],
      testCateList:[{code:"电性能测试",value:"电性能测试"},{code:"循环寿命测试",value:"循环寿命测试"},{code:"日历寿命测试",value:"日历寿命测试"},{code:"安全测试",value:"安全测试"},{code:"精密",value:"精密"}],
			failureCateList: [{ code: 1, value: "不满足指标" }, { code: 2, value: "起火" }, { code: 3, value: "漏液" }, { code: 4, value: "壳体开裂" }, { code: 5, value: "其它" }],
			reviewStatusList: [{ code: 1, value: "审核中" }, { code: 2, value: "审核完成" }],
      stockStatusList: [{ code: 'preInStore', value: "待入库" }, { code: 'inStoring', value: "入库审核中" }, { code: 'inStore', value: "在库" }, { code: 'outStoring', value: "出库审核中" }, { code: 'outStore', value: "出库" }],

			reviewResultList: ['请选择', '通过', '驳回'],
			loading: false,
      inStoreDisable:false,
      outStoreDisable:false,

			selectUserLoading: false,
			selectUserColumns: [{
				title: '账号',
				dataIndex: 'account'
			}, {
				title: '姓名',
				dataIndex: 'name'
			}],
			//提出人
			presenterVisible: false,
			userQueryParam: {},
			presenterName: '',
			userLoadData: parameter => {
				return getUserLists(Object.assign(parameter, this.userQueryParam)).then((res) => {
					return res.data
				})
			},

			//待办相关
			showTodoPushBtn: this.hasPerm("oaTodo:productProblem"),
			todoPushConfirmLoading: false,
			todoPushVisible: false,
			form: this.$form.createForm(this),
			dropdownvisible: false,
			userNameDisplay: "",


			queryParam: {
				listType: this.listType,
				issueId: this.issueId,
				productName: '',
				projectName: '',
				// problemDimensionList: [],
        testTypeList: [],
        stockStatusList: [],
				// keyWord: '',
			},
			labelCol: {
				xs: { span: 24 },
				sm: { span: 6 }
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: { span: 18 }
			},
			searchLoading: false,
			searchDataTimer: 0,
			problemDimensionList: [],
			problemStatusList: [],
			problemCateList: [],
			projectStageList: [],
			productList: [],
			tableColumns: [
				{
					title: '序号',
					dataIndex: 'index',
					key: 'index',
					align: 'center',
					width: 60,
					customRender: (text, record, index) => record.parentId == 1?`${index+1}`:'',
				},
        // {
        //   title: '流程跳转',
        //   dataIndex: 'failureCellInStoreIssueKey',
        //   align: 'center',
        //   width: 150,
        //   scopedSlots: { customRender: 'jiraProcess' }
        // },
        {
          title: '失效登记编号',
          dataIndex: 'fileCode',
          align: 'center',
          width: 200,
          ellipsis:true,
          scopedSlots: { customRender: 'fileCode' }
        },
        {
          title: '样品状态',
          dataIndex: 'sampleStatus',
          align: 'center',
          width: 150,
          scopedSlots: { customRender: 'simpleText' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          scopedSlots: { customRender: 'action' }
        },
        {
          title: '入库信息',
          children: [
            // {
            //   title: '送样人',
            //   dataIndex: 'inStoreName',
            //   align: 'center',
            //   width: 100,
            //   scopedSlots: { customRender: 'simpleText' }
            // },
            {
              title: '日期',
              dataIndex: 'inStoreDate',
              align: 'center',
              width: 125,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '存放库位',
              dataIndex: 'stockLocate',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'stockLocate' }
            },
            // {
            //   title: '库存状态',
            //   dataIndex: 'stockStatus',
            //   align: 'center',
            //   width: 100,
            //   scopedSlots: { customRender: 'stockStatus' }
            // },
          ],
        },
        {
          title: '出库信息',
          children: [
            {
              title: '日期',
              dataIndex: 'outStoreDate',
              align: 'center',
              width: 125,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '申请人',
              dataIndex: 'outStoreName',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '领取目的',
              dataIndex: 'outStoreGoal',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'clampText' }
            },
          ],
        },
				{
					title: '基本信息',
					children: [
            {
              title: '委托单号',
              dataIndex: 'folderNo',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '电芯编码',
              dataIndex: 'cellCode',
              align: 'center',
              width: 200,
              ellipsis:true,
              scopedSlots: { customRender: 'simpleText' }
            },
            {
              title: '检测实验室',
              dataIndex: 'laboratoryId',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'laboratoryId' }
            },
						{
							title: '产品所属研究所',
							dataIndex: 'productDepartment',
							align: 'center',
							width: 150,
							scopedSlots: { customRender: 'productDepartment' }
						},
						{
							title: '样品类型',
							dataIndex: 'orderType',
							align: 'center',
							width: 100,
							scopedSlots: { customRender: 'orderType' }
						},
						{
							title: '产品名称',
							dataIndex: 'productName',
							align: 'center',
							width: 100,
							scopedSlots: { customRender: 'simpleText' }
						},
						{
							title: '项目等级',
							dataIndex: 'projectLevel',
							align: 'center',
							width: 100,
							scopedSlots: { customRender: 'projectLevel' }
						},
						{
							title: '研制阶段',
							dataIndex: 'researchStage',
							align: 'center',
							width: 100,
							scopedSlots: { customRender: 'researchStage' }
						},
						{
							title: '测试样品阶段',
							dataIndex: 'testSampleStage',
							align: 'center',
							width: 100,
						},
            {
              title: '样品数量',
              dataIndex: 'testSampleNum',
              align: 'center',
              width: 100,
              customRender: (text, record, index)  => {
                if (record.parentId != 1) {//1为父级，显示数量，否则不显示
                  return '';
                }
                if (record.children == null|| record.children.length == 0) {
                  return 1;
                }
                return record.children.length + 1;
              }
            },
            {
              title: '测试项目名称',
              dataIndex: 'testProjectName',
              align: 'center',
              width: 125,
              ellipsis:true,
              scopedSlots: { customRender: 'clampText' }
            },
					],
				},
				/*{
					title: '电芯测试信息',
					children: [
						{
							title: '委托单号',
							dataIndex: 'folderNo',
							align: 'center',
							width: 100,
							scopedSlots: { customRender: 'simpleText' }
						},
						{
							title: '电芯编码',
							dataIndex: 'cellCode',
							align: 'center',
							width: 100,
              ellipsis:true,
							scopedSlots: { customRender: 'simpleText' }
						},
						
					],
				},*/
				{
					title: '入库信息',
					children: [
						{
							title: '送样人',
							dataIndex: 'inStoreName',
							align: 'center',
							width: 100,
							scopedSlots: { customRender: 'simpleText' }
						},
						{
							title: '入库日期',
							dataIndex: 'inStoreDate',
							align: 'center',
							width: 125,
							scopedSlots: { customRender: 'simpleText' }
						},
						{
							title: '存放库位',
							dataIndex: 'stockLocate',
							align: 'center',
							width: 100,
							scopedSlots: { customRender: 'stockLocate' }
						},
						{
							title: '库存状态',
							dataIndex: 'stockStatus',
							align: 'center',
							width: 100,
							scopedSlots: { customRender: 'stockStatus' }
						},
					],
				},
				{
					title: '出库信息',
					children: [
						{
							title: '领取人',
							dataIndex: 'outStoreName',
							align: 'center',
							width: 100,
							scopedSlots: { customRender: 'simpleText' }
						},
						{
							title: '领取日期',
							dataIndex: 'outStoreDate',
							align: 'center',
							width: 125,
							scopedSlots: { customRender: 'simpleText' }
						},
					],
				},
				
			],
			tableData: [],
			initData: {},
		}
	},
	watch: {
    storeCellList(newVal, oldVal) {

			// document.documentElement.style.setProperty(`--height`, `${this.tableHeight - subtrahend}px`)
      document.documentElement.style.setProperty(`--height`, document.documentElement.clientHeight - 40 - 20 - 24 - 10 - 32 - 10 - 92 - (this.isShowAllSearch == true?40:0) + (this.storeCellList.length>0?0:45) + `px`)

    },
    isShowAllSearch(newVal, oldVal) {

			// document.documentElement.style.setProperty(`--height`, `${this.tableHeight - subtrahend}px`)
			document.documentElement.style.setProperty(`--height`, document.documentElement.clientHeight - 40 - 20 - 24 - 10 - 32 - 10 - 92 - (this.isShowAllSearch == true?40:0) + (this.storeCellList.length>0?0:45) + `px`)
		}
	},
	created() {
		this.getJiraOptionList();
		this.getData(this.queryParam);
	},
	mounted() {

    // document.documentElement.clientHeight - 40 - 20 - 24 - 10 - 32 - 10 - 92

		document.documentElement.style.setProperty(`--height`, document.documentElement.clientHeight - 40 - 20 - 24 - 10 - 32 - 10 - 92 + `px`)
    	this.initFailureCellViewChart();

  },
	methods: {
		moment,
    	ocvConfigSet(record) {
      //弹出弹窗填写OCV
      	this.$refs.ocvConfig.set()
    },
		fillOcvData(record) {
			//弹出弹窗填写OCV
			this.$refs.addOcv.add(record)
		},
		viewOcvData(record){
			this.$refs.ocvlist.view(record)
		},
    handleChangeSearch() {
      this.isShowAllSearch = !this.isShowAllSearch;
      this.tableHeight = this.isShowAllSearch ? this.templateHeight - 80 : this.templateHeight - 40
    },
    handleTabsChange(value) {
      this.laboratoryId = value
      // this.filterData = ''
      // this.getTodoTaskList()
      this.$refs.table.refresh()
    },
    // 下载处理
    handleDownload($event, index = 1) {
      exportModel({ ordTaskId: this.modalData.ordTaskId, middleCheckStage: index }).then(res => {
        const fileName = `${this.modalData.folderNo}-${this.modalData.wtrName}-${this.modalData.testName}-${this.originalData.orderNumber === 1 ? '初始性能检测' : '存储阶段' + (this.originalData.orderNumber - 1)}${this.middleCheck === "small"
          ? "-小中检"
          : this.middleCheck === "large"
            ? "-大中检"
            : this.middleCheck === "recharge" ? "-补电" : ""}.xlsx`
        if (res) {
          downloadfile1(res, fileName)
        }
      })
    },
    exportOcvTemplate($event, index = 1){
      //调用后端，获取当天的OCV填写模板，并触发下载
      exportOcvTemplate({}).then(res =>{
        const fileName="失效电芯OCV填写模板.xlsx"
        if (res) {
          downloadfile1(res, fileName)
        }
       
      })
    },
    importOcvDate(){
      //获取上传的文件，校验填写结果

    },
    /**
     * 附件事件
     */
    handleUploadFile(info) {
      this.fileList = [...info.fileList]
      if (info.file.response.success) {
        this.$message.success(`${info.file.name} 数据导入成功`)
      } else {
        this.$message.error(`${info.file.name} 数据导入失败:` + info.file.response.message)
      }
    },
    initFailureCellViewChart() {
      var _that = this;
      let chart = this.echarts.init(this.$refs.failureCellView)

      chart.clear()

      const options = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
          }
        },
        color: ['#0168b7', '#4084d7', '#88c0ff', '#2478f2', '#498ff4', '#5f9cf8', '#84b7f9', '#aacff9', '#cee5fc', '#e5f3ff'],
        legend: {
          orient: 'horizontal',
          left: 'center',
          top:'bottom',
          bottom:30
        },
        grid: {
          top: '3%',
          left: '3%',
          right: '4%',
          containLabel: true
        },
        xAxis: {
          show: false,
          type: 'value',
          splitLine: false
        },
        yAxis: {
          type: 'category',
          inverse: true,//数据逆向
          axisTick: {
            show: false // 不显示坐标轴刻度线
          },
          axisLine: {
            show: false, // 不显示坐标轴线
          },
          data: ['研发测试', '产品验证测试', '产品鉴定测试'],
        },
        series: [
          {
            name: '在库',
            type: 'bar',
            barWidth: 25,//设置柱的宽度
            stack: 'total',
            label: {
              show: true
            },
            emphasis: {
              focus: 'series'
            },
            data: [320, 302, 301]
          },
          {
            name: '拆解分析',
            type: 'bar',
            barWidth: 25,//设置柱的宽度
            stack: 'total',
            label: {
              show: true
            },
            emphasis: {
              focus: 'series'
            },
            data: [120, 132, 101]
          },
          {
            name: '报废',
            type: 'bar',
            barWidth: 25,//设置柱的宽度
            stack: 'total',
            label: {
              show: true
            },
            emphasis: {
              focus: 'series'
            },
            data: [220, 182, 191]
          }
        ]
      };
      chart.setOption(options)
      chart.resize()
    },
		startInStore(record) {
			//调用后端，发起jira入库申请
      this.outStoreDisable = true;
      failureCellInStore(record).then(res=>{
        if (res.success) {
          this.$message.success('流程发起成功，请前往JIRA查看')
          this.$refs.table.refresh()
          // this.getData(this.queryParam);
        } else {
          this.$message.error('流程发起失败：' + res.message)
        }
      }).finally((res) => {
        this.outStoreDisable = false;
      })
		},
    selectOutStoreCell(record){
      console.log(this.storeCellList);
      var recordStoreCell = this.storeCellList.filter(item => item.id === record.id)[0];
      var var1 = [];
      if (recordStoreCell.stockStatus == "inStore"){//在库状态，才能发起出库
        var1.push({id: recordStoreCell.id, cellCode: recordStoreCell.cellCode});
      }
      for(var i = 0; i < recordStoreCell.children.length; i++){
        var child = recordStoreCell.children[i];
        if (child.stockStatus == "inStore"){
          var1.push({id: child.id, cellCode: child.cellCode});
        }
      }
      this.$refs.outStoreCellSelect.show(var1);
    },
		startOutStore(record) {
			//调用后端，发起jira出库申请
      this.outStoreDisable = true;
      failureCellOutStore(record).then(res=>{
        if (res.success) {
          this.$message.success('流程发起成功，请前往JIRA查看')
          this.$refs.table.refresh()
          // this.getData(this.queryParam);
        } else {
          this.$message.error('流程发起失败：' + res.message)
        }
      }).finally((res) => {
        this.outStoreDisable = false;
      })
		},

		addOpen() {
			this.$refs.addForm.add();
		},
		editOpen(record) {
			// this.searchLoading = true;
			this.$refs.editForm.edit(record);
		},
		handleOk() {
			this.getData(this.queryParam);
		},
		filePreviewOnClose() {
			this.filePreviewVisible = false;
		},
		previewFile(record) {
			this.fileUrl = this.previewBaseUrl + record.fileId;
			//判断，pdf进行预览，非pdf触发下载
			this.filePreviewVisible = true;
		},
    callFileInfoDownload (fileId) {
      this.loading = true
      sysFileInfoDownload({ id: fileId }).then((res) => {
        this.loading = false
        this.downloadfile(res)
      }).catch((err) => {
        this.loading = false
        this.$message.error('下载错误：获取文件流错误')
      })
    },
    instoreRequestClick(){
      console.log('instoreRequestClick');
    },
    outstoreRequestClick(){
      console.log('outstoreRequestClick');
    },
    resetSearch() {
      this.queryParam.fileCode = null
      this.queryParam.productName = null
      this.queryParam.cellCode = null
      this.queryParam.testProjectName = null
      this.queryParam.stockStatusList = []
      this.$refs.table.refresh()
    },
		searchData() {
			if (this.searchDataTimer === 0) {//首次调用，设置定时器
				this.searchDataTimer = setTimeout(() => {
					this.getData(this.queryParam)//  调用数据请求方法
				}, 600)
			} else {
				clearTimeout(this.searchDataTimer)//多次调用，取消定时器，重新设置
				this.searchDataTimer = setTimeout(() => {
					this.getData(this.queryParam)//  调用数据请求方法
				}, 600)
			}
		},
		selectProduct(value, label, extra) {
			var product = this.productList.filter(item => item.issueId == value)[0];
			this.addForm.setFieldsValue({
				productName: product.productName
			})
		},

		editReviewResult(id) {
			const $i = this.tableData.findIndex(item => id === item.id);
			this.tableData[$i].editable = true
		},

		selectReviewTodoUser() {
			this.todoPushVisible = true
		},
		pushReviewTodo() {
			const {
				form: { validateFields }
			} = this
			this.todoPushConfirmLoading = true
			validateFields((errors, values) => {
				if (!errors) {
					let $params = { ...values }
					oaTodoPushProductProblem($params)
						.then(res => {
							if (res.success) {
								if (res.data.result == "1") {
									this.$message.success(res.data.message)
									this.visible = false
									this.todoPushConfirmLoading = false
									this.handleCancel()
								} else if (res.data.result == "0") {
									//弹出确认框
									this.$confirm({
										title: "提示",
										content: res.data.message,
										type: "warning", //提示类型  success/info/warning/error
										onOk: () => {
											$params = {
												...values,
												repeatPush: 1
											}
											oaTodoPushProductProblem($params).then(res => {
												if (res.success) {
													this.$message.success(res.data.message)
													this.visible = false
													this.todoPushConfirmLoading = false
													this.handleCancel()
												} else {
													this.$message.error("待办推送失败" + res.message)
												}
											})
										}
									})
								} else {
									this.$message.error("待办推送失败：" + res.message)
								}
							} else {
								this.$message.error("待办推送失败：" + res.message)
							}
						})
						.finally(res => {
							this.todoPushConfirmLoading = false
						})
				} else {
					this.todoPushConfirmLoading = false
					this.todoPushVisible = false
				}
			})
		},
		doneReviewTodo() {
			oaTodoDonePushProductProblem({})
				.then(res => {
				})
				.finally(() => { })
		},
		cancelReviewTodo() {
			oaTodoCancelPushProductProblem({})
				.then(res => {
					if (res.success) {
						this.$message.success(res.data.message)
					} else {
						this.$message.error("待办取消失败：" + res.message)
					}
				})
				.finally(() => { })
		},
		//待办用户选择
		todoPushCustomRow(row, index) {
			return {
				on: {
					click: () => {
						this.form.setFieldsValue({
							userName: row.account
						})
						this.userNameDisplay = row.name
						this.dropdownvisible = false
					}
				}
			}
		},
		todoOnSearch(e) {
			this.$refs.todoTablePeople.refresh()
		},


		handleCancel() {
			this.selectvisible = false
			this.dropdownvisible = false
			this.userQueryParam.searchValue = null
			this.todoPushVisible = false
			this.form.setFieldsValue({
				userName: ""
			})
			this.userNameDisplay = ""
		},
		handleToJira(issueKey) {
			if (issueKey == null) {
				return;
			}
			let $url = `http://jira.evebattery.com/browse/` + issueKey + `?auth=` + Vue.ls.get("jtoken");
			window.open($url, "_blank");
		},

		handleDetail() {
			this.$router.push({
				path: "/problemDetail",
				query: {}
			})
		},

		getData(queryParam) {
			this.searchLoading = true
			getDpvTestFailureStockList(queryParam).then(res => {
				this.tableData = res.data
			}).finally(res => {
				this.searchLoading = false
			})
		},
		getJiraOptionList() {
			getJiraOptionList({ fieldName: 'department' }).then(res => {
				var list = ['18863', '22101', '18846', '22105', '18711', '22269'];
				this.productDepartmentList = res.data.filter(e => list.includes(e.id));
			})
      getJiraOptionList({ fieldName: 'stockLocate' }).then(res => {
        this.stockLocateList = res.data;
      })
		},
		filterOption(input, option) {
			return (
				option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
			);
		},
	}
}
</script>
<style lang="less" scoped="">
@import '/src/components/pageTool/style/pbiSearchItem.less';
:root {
	--height: 600px;
}

/* 标题 */
.head-title {
	display: flex;
	align-items: center;
}

.head-title .line {
	width: 4px;
	height: 22px;
	background: #3293ff;
	border-radius: 20px;
}

.head-title .title {
	font-size: 18px;
	font-weight: 600;
  color: #333;
}

/* 筛选 */
.filter-wrapper {
	display: flex;
	justify-content: space-between;
}

.filter-left {
	display: flex;
	margin-right: auto;
}

.filter-right {
  display: inline;
  align-self: center;
}

/deep/.ant-select-sm .ant-select-selection__rendered{
	line-height: initial;
}

/deep/.ant-select-selection__rendered {
  position: unset!important;
}

/* 表格 */
.table-wrapper {
	//padding: 10px;
  padding-left: 10px;
  padding-right: 10px;
	background: #fff;
	border-radius: 10px;
}

.red {
	display: block;
	background: #ff3333;
	text-align: center;
	color: #fff;
}

.yellow {
	display: block;
	background: #fac858;
	text-align: center;
	color: #fff;
}

.green {
	display: block;
	background: #58a55c;
	text-align: center;
	color: #fff;
}

.btn_pn {
	display: block;
	min-height: 18px;
	min-width: 70px;
}

/deep/ .problemStatusSelect .ant-select-selection {
	background-color: rgba(255, 255, 255, 0);
	border: none;
}

.problem-status-show {
	justify-content: center;
	display: flex;
	align-items: center;
}

.problem-status-show .circle {
	width: 13px;
	height: 13px;
	border-radius: 50%;
	margin-right: 8px;
}

.select-box {
	display: flex;
	align-items: center;
}

.select-box .circle {
	width: 13px;
	height: 13px;
	border-radius: 50%;
	margin-right: 8px;
}

/* 通用  */

.status-lamp {
	width: 15px;
	height: 15px;
	border-radius: 50%;
	margin: auto;
	flex-shrink: 0;
}

.mr10 {
	margin-right: 10px;
}

.mt10 {
	margin-top: 10px;
}

.mt5 {
	margin-top: 5px;
}

.filter-select {
	width: 100%;
}

.filter-input {
	width: 100%;
}

/* 表格组件 */
/deep/ .ant-table tr th {
	background: #f4f4f4;
	font-size: 13px;
}

/deep/.ant-table-body {
	height: var(--height) !important;
	overflow-y: scroll;
}

/deep/.ant-table-thead {
	position: sticky;
	top: 0;
	z-index: 10;
}

/deep/ .ant-pagination {
	margin: 10px 0;
}

/deep/ .ant-table-placeholder {
	border: none;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

/deep/ .ant-tabs-bar{
	margin: 10px 0;
}

.tab {
  padding: 1px;
  background-color: #fff;
}


/deep/.pbi-tabs{
  font-size: 14px;
}
/deep/.pbi-tab-item{
  padding: 12px 16px;
}
/deep/.pbi-tab-active{
  font-size: 15px;
}

//内容部分
.content-wrapper {
  display: flex;
  flex: 1;
  margin-top: 3px;
}

.content-wrapper .content-left {
  margin-right: 14px;
  width: 25%;
  height: 100%;
  position: relative;
}
.content-left .block {
  padding: 5px;
  background-color: #fff;
  border-radius:10px;
  height: calc(100vh - 40px - 10px - 22px - 22px);
  overflow-y: scroll;
  padding-bottom: 12px;
  margin-top: 5px;
}

.content-left .block .top{
  margin-top: 12px;
  /* height:50%; */
}
.content-left .block .top .top-title{
  text-align: center;
  margin-top: 8px;
  font-size: 26px;
  font-weight: 400;
  color: #0168B7;
  line-height: 30px;
}
.content-left .block .top .sample-num{
	display: flex;
    align-items: center;
    justify-content: center;
    column-gap: 3px;
    margin: 8px;
}
.content-left .block .top .sample-num .sample-num-sub{
	font-size: 20px;
    color: #ffffff;
    background: #0168B7;
    height: 30px;
    width: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
}

.content-left .block .top .top-block1{
  //text-align: center;
  margin-top: 12px;
  margin-left: 12px;
  margin-right: 12px;
  background-image: linear-gradient(#E2F0FF, #ffffff);
  /* height: 56px; */
  display: flex;
  //flex-flow:  row nowrap;
  justify-content: space-around;
  border-radius:2px;
}
.content-left .block .top .top-block1 .block1-sub{
  margin-top: 5px;
  margin-left: 8px;
  margin-right: 8px;
  font-size: 20px;
  display: flex;
  flex-direction: column;
  row-gap: 4px;
}
.content-left .block .top .top-block1 .block1-sub .sub-title{
  font-size: 12px;
  font-weight: 700;
  display: flex;
  align-items: center;
  column-gap: 2px;
}
.content-left .block .top .top-block1 .block1-sub .sub-num{
  text-align: center;
  font-size: 17px;
  font-weight: 700;
}

.content-left .block .top .top-block2{
  //text-align: center;
  margin-top: 12px;
  margin-left: 12px;
  margin-right: 12px;
  //font-size: 20px;
  //font-weight: 700;
  //background: #0168B7;
  background-image: linear-gradient(to right, #E5EDFE, #ffffff);
  height: 38px;
  display: flex;
  //flex-flow:  row nowrap;
  align-items: center;
  justify-content: space-between;
  border-radius:2px;
  padding: 4px;
}

.content-left .block .top .top-block3{
  //text-align: center;
  margin-top: 12px;
  margin-left: 12px;
  margin-right: 12px;
  //font-size: 20px;
  //font-weight: 700;
  //background: #0168B7;
  background-image: linear-gradient(to right, #E1F8F6, #ffffff);
  height: 38px;
  display: flex;
  //flex-flow:  row nowrap;
  align-items: center;
  justify-content: space-between;
  border-radius:2px;
  padding: 4px;
}

.content-left .block .top .top-block4{
  margin-top: 12px;
  margin-left: 12px;
  margin-right: 12px;
  background-image: linear-gradient(to right, #E4FCEB, #ffffff);
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius:2px;
  padding: 4px;
}


.content-left .block .bottom{

  /* height:50%; */
  margin-top: 12px;
}
.content-left .block .bottom .chart{
  height:calc(100% - 40px);
  width:100%;
}

// 内容--右边
.content-wrapper .content-right {
  height: 100%;
  width: 75%;
}


.charts{
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin-top: 12px;
}
.chart-item{
	display: flex;
	align-items: center;
	width: 100%;
	font-size: 12px;
	padding: 8px 12px;
	margin-bottom: 6px;
}
.chart-label{
	width: 70px;
	text-align: right;
	padding-right: 6px;
	color: #000;
	font-size: 10px;
}
.chart-wrapper{
	display: flex;
	align-items: center;
	height: 20px;
	flex: 1;
	background: #F2F2F2;
	
}
.chart-instore,.chart-analyse,.chart-scrap{
	color: #fff;
	height: 100%;
	display: flex;
    align-items: center;
    justify-content: center;
}
.chart-instore{
	background: #0168B7;
}
.chart-analyse{
	background: #4084D7;
}
.chart-scrap{
	background: #88C0FF;
}
.chart-titles{
	margin-top: 6px;
	display: flex;
	align-items: center;
	justify-content: space-around;
	font-size: 12px;
	padding: 0 40px;
}
.chart-li{
	display: flex;
	align-items: center;
	column-gap: 5px;
}
.item-block{
	height: 16px;
	width: 16px;
	background: #0168B7;
	border-radius: 3px;
}
.item-block.b1{
	background: #4084D7;
}
.item-block.b2{
	background: #88C0FF;
}

/deep/.s-table-tool{
  padding-bottom:0;
}
/deep/ .ant-table-middle > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th{
  padding: 5px;
  font-size: 13px;
  color: rgba(0, 0, 0, .85);
  font-weight: 500;
}
/deep/ .ant-table-middle > .ant-table-content > .ant-table-body > table > .ant-table-tbody > tr > td{
  padding: 4px;
  color: #333;
  font-size: 12px;
  font-weight: 400;
}
/deep/.ant-select-sm .ant-select-selection--single {
   height: 24px;
}

/deep/.ant-select-selection--multiple {
  min-height: 28px;
}

</style>
