legendData:在线编辑图标关于图例的编辑对象
  legendEdit:图例的数据
  legendRevealList:图例的显隐
  legendWidth:每个图例的宽度
  legendHeight:每个图例的高度
  legendGap:每个图例的间隔
  legendSort:图例的排序
  legendEditName:图例的名称修改
  legendOrient:图例的朝向（垂直/水平）
  legendX:图例离左边的距离
  legendTop:图例离顶边的距离
  legendBgColor:图例背景颜色

checkData:在线编辑图标关于图里面每根线的编辑对象
  name:折线的名称
  id:折线的唯一标识
  soc:折线的soc/batteryNum
  data:折线选中数据（{"50%soc"}）
  duplicateData:折线选中数据({id:"50%soc",data：{id:索引值,name:"50%soc",value:"50%soc",label:"50%soc"}})
  maxPoint:折线的最大值
  minPoint:折线的最小值
  symbol:折点的形状
  symbolSize:折点的大小
  itemColor:折点的颜色
  lineType:折线的类型
  lineColor:折线的颜色
  lineWidth:折线的宽度
  synchronization:折线的同步对象，默认为自己

axisData = 在线编辑图标关于坐标轴的编辑对象
  xMin:X坐标的最小值
  xMax:X坐标的最大值
  xInterval:X坐标的间隔值
  xType:X坐标的类型（数值轴/类目轴）
  yMin:Y坐标的最小值
  yMax:Y坐标的最大值
  yInterval:Y坐标的间隔值
  yType:Y坐标的类型（数值轴/类目轴）

titleData = 在线编辑图标关于标题的编辑对象
  chartTitle:图表标题
  XTitle:X坐标轴主标题
  YTitle:Y坐标图表主标题
  YTitle2:Y坐标图表副标题
  titleTop:顶部标题离顶边的距离
  yTitleLetf:Y轴标题离左边的距离

gridData = 在线编辑图标关于图表位置的编辑对象
  gridTop:图表离顶边的距离
  gridLeft:图表离左边的距离
  gridRight:图表离右边的距离
  gridBottom:图表离底边的距离

****Echart:图表实例
****EditData:传给在线编辑图表的对象,作为父子组件互相修改的通信
****OriginalLegend:传给在线编辑图表的图例原始值，只做原始选项使用,不做修改
****OriginalSeries:传给在线编辑图表的数据（每条折线）原始值，只做原始选项使用,不做修改
****ResetOriginal:传给在线编辑图表的原始值，作为在线编辑图表重置是重新赋值对象使用