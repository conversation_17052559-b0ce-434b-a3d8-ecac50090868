<template>
<a-spin :spinning="loading">
	<a-button type="primary" @click="doAdd" style="margin-bottom:8px">
      新增
    </a-button>
	<a-button type="primary" @click="doSave" style="margin-bottom:8px;margin-left:8px;">
      保存
    </a-button>
    <a-table
		ref="table"
        size="middle"
        :columns="tablecolumns"
        :dataSource="tabledatas"
		:pagination="false"
		showPagination="false"
		:rowKey="(record) => record.id"
		:scroll="{ x: 2160, y: windowHeight }"
		bordered
    />
</a-spin>
</template>

<script>
import { getBom,sysBomSupplySave } from "@/api/modular/system/report"
export default {
	name:'bomsuppliers',

	props: {
		issueId: {
			type: Number,
			default: 0
		},
	},
	data() {
		return {
			windowHeight: document.documentElement.clientHeight - 403,// +60
			loading:true,
			suppliers:{
				issueId:null,
				bomSuppliers:null
			},
			tabledatas: [],
			tablecolumns: [],
			treeLeafList:[],
			tableitem:{}
		}
	},
	methods: {
		doAdd(){
			this.tabledatas.push(this.tableitem)
		},
		doSave(){
			if (this.tabledatas.length <= 0) {
				this.$message.error('错误提示：数据为空',1)
				return
			}
			this.loading = true
			this.suppliers.issueId = this.issueId
			this.suppliers.bomSuppliers = JSON.stringify(this.tabledatas)
			sysBomSupplySave(this.suppliers).then((res) => {
              if (res.success) {
                this.$message.success('保存成功')
              } else {
                this.$message.error('保存失败：' + res.message)
              }
			  this.loading = false
            }).catch((err)=>{
                this.loading = false
                this.$message.error('错误提示：' + err.message,1)
            })
		},
		toCopy(index){
			let B = JSON.parse( JSON.stringify( this.tabledatas[index] ) );
			this.tabledatas.splice(index+1,0,B)
		},
		toDelete(index){
			this.tabledatas.splice(index,1)
		},
		getTreeLeaf(treeData, arr) {
			if (Array.isArray(treeData)) {
				treeData.forEach(item => {
					if (item.children && item.children.length > 0) {
						this.getTreeLeaf(item.children, arr)
					} else {
						if(arr.indexOf(item.key)>-1){
							item.width = 120
							item.customRender = (text, row, index) => {
								return (
									<a-Input
										change={(e) => {
											const { value } = e.target
											row[item.key] = value
										}}
										placeholder={`填写`}
										v-model={row[item.key]}
									/>
								)
							}
						}else{
							item.customRender = (text, row, index) => {
								return (
									<a-select placeholder='选择'
										name="item.key"
										default-value="{row[item.key]}"
										v-model={row[item.key]}
										change={(value) => {
											row[item.key] = value
										}}
									>
										<a-select-option value='1'>是</a-select-option>
										<a-select-option value='0'>否</a-select-option>
									</a-select>
								)
							}
						}
					}
				})
			} 
		},
		callBom(){
            this.loading = true
            let params = {issueId: this.issueId,title:''}
            getBom(params)
            .then((res)=>{
                if (res.result) {
					let renderInputArr = ['material','supplierType','materialNo','supplierCode','materialDemand2022','materialDemand2023','materialSupply2022','materialSupply2023']
                    this.getTreeLeaf(res.data.columsData,renderInputArr)
					res.data.columsData.push({
						title: '操作',
						dataIndex: 'action',
						key: 'action',
						fixed:'right',
						width:80,
						customRender:(text, row, index) => {
							return <div>
								<a onClick={() => {this.toCopy(index)}}>复制</a>
								&nbsp;
								<a onClick={() => {this.toDelete(index)}}>删除</a>
							</div>
						}
					})
					this.tablecolumns = res.data.columsData
					if (res.data.rowData !== null) {
						this.suppliers = res.data.rowData
					}
                    this.tabledatas = res.data.rowData !== null ? JSON.parse(res.data.rowData.bomSuppliers): []
					this.tableitem = res.data.rowItem
                } else {
                    this.$message.error(res.message,1);
                }
                this.loading = false
                })
            .catch((err)=>{
                this.loading = false
                this.$message.error('错误提示：' + err.message,1)
            });
        },
	},
    created(){
		this.callBom()
    }
}
</script>

<style lang="less">
</style>