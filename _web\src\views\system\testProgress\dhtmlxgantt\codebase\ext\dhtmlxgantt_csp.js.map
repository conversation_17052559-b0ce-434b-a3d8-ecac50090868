{"version": 3, "sources": ["webpack://[name]/webpack/universalModuleDefinition", "webpack://[name]/webpack/bootstrap", "webpack://[name]/./sources/ext/csp.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "window", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "gantt", "date", "date_to_str", "format", "utc", "replace", "a", "to_fixed", "getUTCDate", "getDate", "getUTCMonth", "getMonth", "getUTCFullYear", "getFullYear", "locale", "day_short", "getUTCDay", "getDay", "day_full", "month_short", "month_full", "getUTCHours", "getHours", "getUTCMinutes", "getMinutes", "getUTCSeconds", "getSeconds", "getUTCISOWeek", "getISOWeek", "str_to_date", "set", "temp", "match", "mask", "length", "toLowerCase", "month_short_hash", "month_full_hash", "Date", "UTC", "config", "task_attribute", "link_attribute", "grid_resizer_column_attribute", "grid_resizer_attribute"], "mappings": ";;;;;;;;;;;;CAAA,SAAAA,EAAAC,GACA,iBAAAC,SAAA,iBAAAC,OACAA,OAAAD,QAAAD,IACA,mBAAAG,eAAAC,IACAD,OAAA,yBAAAH,GACA,iBAAAC,QACAA,QAAA,uBAAAD,IAEAD,EAAA,uBAAAC,IARA,CASCK,OAAA,WACD,mBCTA,IAAAC,KAGA,SAAAC,EAAAC,GAGA,GAAAF,EAAAE,GACA,OAAAF,EAAAE,GAAAP,QAGA,IAAAC,EAAAI,EAAAE,IACAC,EAAAD,EACAE,GAAA,EACAT,YAUA,OANAU,EAAAH,GAAAI,KAAAV,EAAAD,QAAAC,IAAAD,QAAAM,GAGAL,EAAAQ,GAAA,EAGAR,EAAAD,QA0DA,OArDAM,EAAAM,EAAAF,EAGAJ,EAAAO,EAAAR,EAGAC,EAAAQ,EAAA,SAAAd,EAAAe,EAAAC,GACAV,EAAAW,EAAAjB,EAAAe,IACAG,OAAAC,eAAAnB,EAAAe,GAA0CK,YAAA,EAAAC,IAAAL,KAK1CV,EAAAgB,EAAA,SAAAtB,GACA,oBAAAuB,eAAAC,aACAN,OAAAC,eAAAnB,EAAAuB,OAAAC,aAAwDC,MAAA,WAExDP,OAAAC,eAAAnB,EAAA,cAAiDyB,OAAA,KAQjDnB,EAAAoB,EAAA,SAAAD,EAAAE,GAEA,GADA,EAAAA,IAAAF,EAAAnB,EAAAmB,IACA,EAAAE,EAAA,OAAAF,EACA,KAAAE,GAAA,iBAAAF,QAAAG,WAAA,OAAAH,EACA,IAAAI,EAAAX,OAAAY,OAAA,MAGA,GAFAxB,EAAAgB,EAAAO,GACAX,OAAAC,eAAAU,EAAA,WAAyCT,YAAA,EAAAK,UACzC,EAAAE,GAAA,iBAAAF,EAAA,QAAAM,KAAAN,EAAAnB,EAAAQ,EAAAe,EAAAE,EAAA,SAAAA,GAAgH,OAAAN,EAAAM,IAAqBC,KAAA,KAAAD,IACrI,OAAAF,GAIAvB,EAAA2B,EAAA,SAAAhC,GACA,IAAAe,EAAAf,KAAA2B,WACA,WAA2B,OAAA3B,EAAA,SAC3B,WAAiC,OAAAA,GAEjC,OADAK,EAAAQ,EAAAE,EAAA,IAAAA,GACAA,GAIAV,EAAAW,EAAA,SAAAiB,EAAAC,GAAsD,OAAAjB,OAAAkB,UAAAC,eAAA1B,KAAAuB,EAAAC,IAGtD7B,EAAAgC,EAAA,aAIAhC,IAAAiC,EAAA,yBCzEAC,MAAAC,KAAAC,YAAA,SAAAC,EAAAC,GACA,gBAAAH,GACA,OAAAE,EAAAE,QAAA,sBAAAC,GACA,OAAAA,GACA,gBAAAF,EAAAJ,MAAAC,KAAAM,SAAAN,EAAAO,cAAAR,MAAAC,KAAAM,SAAAN,EAAAQ,WACA,gBAAAL,EAAAJ,MAAAC,KAAAM,SAAAN,EAAAS,cAAA,GAAAV,MAAAC,KAAAM,SAAAN,EAAAU,WAAA,GACA,gBAAAP,EAAAH,EAAAO,aAAAP,EAAAQ,UACA,gBAAAL,EAAAH,EAAAS,cAAA,EAAAT,EAAAU,WAAA,EACA,gBAAAP,EAAAJ,MAAAC,KAAAM,SAAAN,EAAAW,iBAAA,KAAAZ,MAAAC,KAAAM,SAAAN,EAAAY,cAAA,KACA,gBAAAT,EAAAH,EAAAW,iBAAAX,EAAAY,cACA,gBAAAT,EAAAJ,MAAAc,OAAAb,KAAAc,UAAAd,EAAAe,aAAAhB,MAAAc,OAAAb,KAAAc,UAAAd,EAAAgB,UACA,gBAAAb,EAAAJ,MAAAc,OAAAb,KAAAiB,SAAAjB,EAAAe,aAAAhB,MAAAc,OAAAb,KAAAiB,SAAAjB,EAAAgB,UACA,gBAAAb,EAAAJ,MAAAc,OAAAb,KAAAkB,YAAAlB,EAAAS,eAAAV,MAAAc,OAAAb,KAAAkB,YAAAlB,EAAAU,YACA,gBAAAP,EAAAJ,MAAAc,OAAAb,KAAAmB,WAAAnB,EAAAS,eAAAV,MAAAc,OAAAb,KAAAmB,WAAAnB,EAAAU,YACA,gBAAAP,EAAAJ,MAAAC,KAAAM,UAAAN,EAAAoB,cAAA,UAAArB,MAAAC,KAAAM,UAAAN,EAAAqB,WAAA,UACA,gBAAAlB,GAAAH,EAAAoB,cAAA,UAAApB,EAAAqB,WAAA,SACA,gBAAAlB,EAAAH,EAAAoB,cAAApB,EAAAqB,WACA,gBAAAlB,EAAAJ,MAAAC,KAAAM,SAAAN,EAAAoB,eAAArB,MAAAC,KAAAM,SAAAN,EAAAqB,YACA,gBAAAlB,EAAAJ,MAAAC,KAAAM,SAAAN,EAAAsB,iBAAAvB,MAAAC,KAAAM,SAAAN,EAAAuB,cACA,gBAAApB,EAAAH,EAAAoB,cAAA,aAAApB,EAAAqB,WAAA,aACA,gBAAAlB,EAAAH,EAAAoB,cAAA,aAAApB,EAAAqB,WAAA,aACA,gBAAAlB,EAAAJ,MAAAC,KAAAM,SAAAN,EAAAwB,iBAAAzB,MAAAC,KAAAM,SAAAN,EAAAyB,cACA,gBAAAtB,EAAAJ,MAAAC,KAAAM,SAAAP,MAAAC,KAAA0B,cAAA1B,IAAAD,MAAAC,KAAAM,SAAAP,MAAAC,KAAA2B,WAAA3B,IACA,eAAAK,OAKAN,MAAAC,KAAA4B,YAAA,SAAA1B,EAAAC,GACA,gBAAAH,GAKA,IAJA,IAAA6B,GAAA,aACAC,EAAA9B,EAAA+B,MAAA,qBACAC,EAAA9B,EAAA6B,MAAA,cAEAhE,EAAA,EAAiBA,EAAAiE,EAAAC,OAAiBlE,IAClC,OAAAiE,EAAAjE,IACA,SACA,SACA8D,EAAA,GAAAC,EAAA/D,IAAA,EACA,MACA,SACA,SACA8D,EAAA,IAAAC,EAAA/D,IAAA,KACA,MACA,SACA8D,EAAA,KAAAC,EAAA/D,IAAA+D,EAAA/D,GAAA,aACA,MACA,SACA,SACA,SACA,SACA8D,EAAA,GAAAC,EAAA/D,IAAA,EACA,MACA,SACA8D,EAAA,GAAAC,EAAA/D,IAAA,EACA,MACA,SACA8D,EAAA,GAAAC,EAAA/D,IAAA,EACA,MACA,SACA,SACA8D,EAAA,GAAAA,EAAA,cAAAC,EAAA/D,IAAA,IAAAmE,cAAA,MACA,MACA,SACAL,EAAA,GAAAC,EAAA/D,IAAA,EACA,MACA,SACA8D,EAAA,GAAA9B,MAAAc,OAAAb,KAAAmC,iBAAAL,EAAA/D,KAAA,EACA,MACA,SACA8D,EAAA,GAAA9B,MAAAc,OAAAb,KAAAoC,gBAAAN,EAAA/D,KAAA,EAOA,OAAAoC,EACA,IAAAkC,UAAAC,IAAAT,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,KAGA,IAAAQ,KAAAR,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,MAKA9B,MAAAwC,OAAAC,eAAA,eACAzC,MAAAwC,OAAAE,eAAA,eACA1C,MAAAwC,OAAAG,8BAAA,oBACA3C,MAAAwC,OAAAI,uBAAA", "file": "ext/dhtmlxgantt_csp.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"ext/dhtmlxgantt_csp\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ext/dhtmlxgantt_csp\"] = factory();\n\telse\n\t\troot[\"ext/dhtmlxgantt_csp\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/codebase/\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 240);\n", "/*\n Compatibility with Content Security Policy\n https://github.com/denys86/gantt/commit/f64f62f14086a8ec33d5667cfc5dc3a7e775fd2a\n\n Removes evil JS. Inline styles are still used for rendering, may need to allow them or redefine unsafe methods\n style-src 'unsafe-inline'\n\n*/\n\ngantt.date.date_to_str = function(format,utc){\n\treturn function(date) {\n\t\treturn format.replace(/%[a-zA-Z]/g, function (a) {\n\t\t\tswitch (a) {\n\t\t\t\tcase \"%d\": return utc ? gantt.date.to_fixed(date.getUTCDate()) : gantt.date.to_fixed(date.getDate());\n\t\t\t\tcase \"%m\": return utc ? gantt.date.to_fixed((date.getUTCMonth() + 1)) : gantt.date.to_fixed((date.getMonth() + 1));\n\t\t\t\tcase \"%j\": return utc ? date.getUTCDate() : date.getDate();\n\t\t\t\tcase \"%n\": return utc ? (date.getUTCMonth() + 1) : (date.getMonth() + 1);\n\t\t\t\tcase \"%y\": return utc ? gantt.date.to_fixed(date.getUTCFullYear() % 100) : gantt.date.to_fixed(date.getFullYear() % 100);\n\t\t\t\tcase \"%Y\": return utc ? date.getUTCFullYear() : date.getFullYear();\n\t\t\t\tcase \"%D\": return utc ? gantt.locale.date.day_short[date.getUTCDay()] : gantt.locale.date.day_short[date.getDay()];\n\t\t\t\tcase \"%l\": return utc ? gantt.locale.date.day_full[date.getUTCDay()] : gantt.locale.date.day_full[date.getDay()];\n\t\t\t\tcase \"%M\": return utc ? gantt.locale.date.month_short[date.getUTCMonth()] : gantt.locale.date.month_short[date.getMonth()];\n\t\t\t\tcase \"%F\": return utc ? gantt.locale.date.month_full[date.getUTCMonth()] : gantt.locale.date.month_full[date.getMonth()];\n\t\t\t\tcase \"%h\": return utc ? gantt.date.to_fixed((date.getUTCHours() + 11) % 12 + 1) : gantt.date.to_fixed((date.getHours() + 11) % 12 + 1);\n\t\t\t\tcase \"%g\": return utc ? ((date.getUTCHours() + 11) % 12 + 1) : ((date.getHours() + 11) % 12 + 1);\n\t\t\t\tcase \"%G\": return utc ? date.getUTCHours() : date.getHours();\n\t\t\t\tcase \"%H\": return utc ? gantt.date.to_fixed(date.getUTCHours()) : gantt.date.to_fixed(date.getHours());\n\t\t\t\tcase \"%i\": return utc ? gantt.date.to_fixed(date.getUTCMinutes()) : gantt.date.to_fixed(date.getMinutes());\n\t\t\t\tcase \"%a\": return utc ? (date.getUTCHours() > 11 ? \"pm\" : \"am\") : (date.getHours() > 11 ? \"pm\" : \"am\");\n\t\t\t\tcase \"%A\": return utc ? (date.getUTCHours() > 11 ? \"PM\" : \"AM\") : (date.getHours() > 11 ? \"PM\" : \"AM\");\n\t\t\t\tcase \"%s\": return utc ? gantt.date.to_fixed(date.getUTCSeconds()) : gantt.date.to_fixed(date.getSeconds());\n\t\t\t\tcase \"%W\": return utc ? gantt.date.to_fixed(gantt.date.getUTCISOWeek(date)) : gantt.date.to_fixed(gantt.date.getISOWeek(date));\n\t\t\t\tdefault: return a;\n\t\t\t}\n\t\t});\n\t};\n};\ngantt.date.str_to_date = function(format,utc){\n\treturn function(date) {\n\t\tvar set = [0, 0, 1, 0, 0, 0];\n\t\tvar temp = date.match(/[a-zA-Z]+|[0-9]+/g);\n\t\tvar mask = format.match(/%[a-zA-Z]/g);\n\n\t\tfor (var i = 0; i < mask.length; i++) {\n\t\t\tswitch (mask[i]) {\n\t\t\t\tcase \"%j\":\n\t\t\t\tcase \"%d\":\n\t\t\t\t\tset[2] = temp[i] || 1;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"%n\":\n\t\t\t\tcase \"%m\":\n\t\t\t\t\tset[1] = (temp[i] || 1) - 1;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"%y\":\n\t\t\t\t\tset[0] = temp[i] * 1 + (temp[i] > 50 ? 1900 : 2000);\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"%g\":\n\t\t\t\tcase \"%G\":\n\t\t\t\tcase \"%h\":\n\t\t\t\tcase \"%H\":\n\t\t\t\t\tset[3] = temp[i] || 0;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"%i\":\n\t\t\t\t\tset[4] = temp[i] || 0;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"%Y\":\n\t\t\t\t\tset[0] = temp[i] || 0;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"%a\":\n\t\t\t\tcase \"%A\":\n\t\t\t\t\tset[3] = set[3] % 12 + ((temp[i] || '').toLowerCase() == 'am' ? 0 : 12);\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"%s\":\n\t\t\t\t\tset[5] = temp[i] || 0;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"%M\":\n\t\t\t\t\tset[1] = gantt.locale.date.month_short_hash[temp[i]] || 0;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"%F\":\n\t\t\t\t\tset[1] = gantt.locale.date.month_full_hash[temp[i]] || 0;\n\t\t\t\t\tbreak;\n\t\t\t\tdefault:\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\tif (utc) {\n\t\t\treturn new Date(Date.UTC(set[0], set[1], set[2], set[3], set[4], set[5]));\n\t\t}\n\n\t\treturn new Date(set[0], set[1], set[2], set[3], set[4], set[5]);\n\t};\n};\n\n// custom DOM attributes may be stripped in some environemnts, make sure data attributes used instead\ngantt.config.task_attribute = \"data-task-id\";\ngantt.config.link_attribute = \"data-link-id\";\ngantt.config.grid_resizer_column_attribute = \"data-column-index\";\ngantt.config.grid_resizer_attribute = \"data-grid-resizer\";"], "sourceRoot": ""}