<template>
  <div class="container">
    <!-- 面包屑 start -->
    <a-breadcrumb separator=">">
      <a-breadcrumb-item href="">
        <router-link :to="{ path: '/dpvTestRecord'}">
          <a-icon type="rollback" class="rollback-icon" />
          DPV测试进展概览
        </router-link></a-breadcrumb-item>
      <a-breadcrumb-item>测试项目结构化</a-breadcrumb-item>
    </a-breadcrumb>
    <!-- 面包屑 end  -->

    <div style="display: flex;justify-content: space-between;margin-top: 10px;">
      <div class="head-title">
        <div class="line mr10"></div>
        <span class="title">{{$route.query.productName + '-' + $route.query.phase + '-测试项目结构化'}} </span>
      </div>
      <div class="filter-wrapper">
        <div class="filter-right">
          <div>
            <a-button class="mr10" size="small" type="primary" @click="isEdit = !isEdit">{{isEdit ? '取消编辑' : '编辑' }}</a-button>
            <a-button class="mr10" size="small" :loading="aiAsking" type="primary" @click="aiCheckResult">AI判定</a-button>
            <a-button size="small" class="mr10" type="primary" ghost @click="() => isShowExport = true">导出</a-button>
            <a-upload :headers="headers" :action="`/api/testProjectStructureData/importStructureDataTemplate`"
              name="file" :data="{ ...$route.query }" :showUploadList="false" accept="*"
              @change="handleUploadFile">
              <a-button type="primary" ghost size="small" class="mr10">导入数据</a-button>
            </a-upload>
				    
            <a-button class="mr10" size="small" @click="handleDownload">模板下载</a-button>
            <a-button size="small" @click="handleRefresh">刷新</a-button>
          </div>
        </div>
      </div>
    </div>
    <div class="table-wrapper mt10">
      <div class="table-content">
        <a-spin :spinning="spinning">
          <a-table id="structureTable" :columns="tableColumns" :data-source="tableData" :row-key="(record) => record.id"
            :pagination="false" bordered :customRow="customRowEvent">

            <div slot="index" slot-scope="text,record,index" :id="'serialNumber'+index" >
              <div v-if="isEdit" class="serial-number-content">
                <a-icon  type="plus" id="plusIcon" class="plus-icon" @click="handleAddRow(index)" />
                {{index + 1}}
              </div>
              <span v-else>{{index + 1}}</span>
            </div>

            <div slot="tooltipInput" slot-scope="text,record,index,columns">
              <a-tooltip v-if="isEdit">
                <template slot="title">
                  {{text ? text : '请填写'}}
                </template>
                <a-textarea v-model="record[columns.key]" 
                  :auto-size="{ minRows: 1, maxRows: 3 }" style="width: 100%"
                  @blur="$event => handleInputBlur($event,record,columns)" />
              </a-tooltip>
              <a-tooltip :title="text" placement="topLeft" v-else>{{text}}</a-tooltip>
            </div>
            

            <span slot="selectInput" slot-scope="text,record,index,columns">
              <a-select v-if="isEdit" v-model="record[columns.key]" style="width: 100%"
                @change="$event => handleInputBlur($event,record,columns)">
                <a-select-option v-for="(item,i) in getDict(columns.key === 'judgeResult' ? 'dev_test_manage_judge_result' : 'dev_test_manage_status')" :key="i"
                  :value="item.code">{{item.name}}</a-select-option>
              </a-select>
              <div v-else>
                <div v-if="columns.key === 'judgeResult' ">
                  <a-tag v-if="text === 'PASS'" color="green">
                    PASS
                  </a-tag>
                  <a-tag v-if="text === 'NG'" color="red">
                    NG
                  </a-tag>
                  <a-tag v-if="text === 'TBD'" color="orange">
                    TBD
                  </a-tag>
                  <a-tag v-if="text === 'FIO'" color="purple">
                    FIO
                  </a-tag>
                </div>
                <span v-else>{{ text }}</span>
                
              </div>
            </span>

            

            <a-tooltip slot="judgeResultAI" slot-scope="text,record,index">
              <template slot="title">
              {{record.judgeResultAiReason}}
              </template>
              <a-tag v-if="record.judgeResultAi === 'PASS'" color="green">
                PASS
              </a-tag>
              <a-tag v-if="record.judgeResultAi === 'NG'" color="red">
                NG
              </a-tag>
              <a-tag v-if="record.judgeResultAi === 'TBD'" color="orange">
                TBD
              </a-tag>
              <a-tag v-if="record.judgeResultAi === 'FIO'" color="purple">
                FIO
              </a-tag>
            </a-tooltip>

    
            <span slot="tLimsOrdtaskId" slot-scope="text,record,index">
              <a-button v-if="isEdit" type="link" :style="{color:text ? '#7bbe4e' : '#1890ff'}" class="choose-btn"
                @click="handleChooseTestData(record)">{{text ? '重新选择' : '选择'}}</a-button>
              <span v-else>{{ text }}</span>
            </span>

            <div slot="fileUpload" slot-scope="text,record,index,columns">
              <a-upload 
                v-if="isEdit"
                :headers="headers"
                :showUploadList="false"
                :action="`/api/sysFileInfo/upload`"
                name="file"
                :customRequest="$event => customRequest($event,index,columns)"
              >
                <a-button type="link" style="color:#1890ff;"
                  class="choose-btn">上传</a-button>
              </a-upload>
              <div>
                <div v-for="(cItem,cIndex) in record[columns.key]" class="file-content">
                  <a-tooltip :title="`${cIndex + 1}、${cItem.name}`" placement="topLeft">
                    <span class="file-title" @click="previewFile(cItem)">{{cIndex + 1}}、{{cItem.name}}</span>
                  </a-tooltip>
                  <a-icon v-if="isEdit" type="close" @click="handleDelFile(index,columns,cItem.id)" />
                </div>
              </div>
            </div>
        
            <span slot="realTestNumber" slot-scope="text,record,index">
              <a-button v-if="record.realTestNumber && isEdit" type="link" class="choose-btn"
                @click="handleDetail(record)">{{text}}</a-button>
              <span v-else>{{ text }}</span>
            </span> 
            
            <template slot="bolMolEol" slot-scope="text, record, index, columns">
              <a-input v-if="isEdit" @change="changeBolMolEol($event,record,columns)" :value="text"
                style="width: 100%;text-align: center" />
              <span v-else>{{text}}</span>
            </template>

            <template slot="bolMolEolType" slot-scope="text, record, index, columns">
              <a-select v-if="isEdit" dropdown-class-name="dropdownClassName" @change="changeBolMolEolTypeName($event,record,columns)" v-model="text" :options="columns.dataIndex.indexOf('2') > 0?cDOptions:
              columns.dataIndex.indexOf('3') > 0 ? warmBoxOptions:jjOptions"
                style="width: 100%;text-align: center" ></a-select>
              <span v-else>{{text}}</span>
            </template>

              <span slot="testDays" slot-scope="text,record,index,columns">
                <a-input-number v-if="isEdit" v-model="record.testDays" :min="1" placeholder="测试天数"
                  @blur="$event => handleInputBlur($event,record,columns)" />
                <span v-else>{{text}}</span>
              </span>

              <span slot="dataPicker" slot-scope="text,record,index,columns">
                <a-date-picker v-if="isEdit" v-model="record[columns.key]" format='YYYY-MM-DD' style="width: 100%" :allowClear="false"
                   @change="(date, dateString) => handleTime(date, dateString,record,columns)" />
                <span v-else>{{text}}</span>
              </span>

              <span slot="action" slot-scope="text,record,index">
                <a-popconfirm v-if="isEdit" placement="topRight" title="确认删除？" @confirm="() => deleteReport(record.id)">
                  <a-icon type="minus-circle" style="color: red;" />
                </a-popconfirm>
              </span> 


            

          </a-table>
        </a-spin>
      </div>
    </div>

    <div v-if="isTestProjectShow">
      <test-project-modal :folderNoCheck="folderNoCheck" :tlimsOrdtaskIdCheck="tlimsOrdtaskIdCheck"
        @submit="handleChooseTestProject" @cancel="handleCancel('isTestProjectShow')"></test-project-modal>
    </div>

    <div v-if="isShowTestNumberDetail">
      <test-number-modal :testProjectStructureDataId="testProjectStructureDataId"
        @cancel="handleCancel('isShowTestNumberDetail')"></test-number-modal>
    </div>

    <div v-if="isShowExport">
			<export-modal @submit="handleExport" @cancel="isShowExport = false"/>
		</div>

    <!-- 预览文件 start -->
		<pbi-preview v-if="previewVisible" :previewFileName="previewFileName" :previewFileID="previewFileID"  @close="previewVisible = false"></pbi-preview>
		<!-- 预览文件 end -->

  </div>
</template>
<script>
  import $ from 'jquery';
  import Vue from 'vue'
  import moment from "moment";

  import { downloadfile1 } from "@/utils/util"
  import pbiPreview from '@/components/pageTool/components/pbiPreview.vue'

  import {
    DICT_TYPE_TREE_DATA, ACCESS_TOKEN
  } from '@/store/mutation-types'

  import testNumberModal from './detail/testNumber.vue'
  import testProjectModal from './detail/testProject.vue'

  import { getTestStructureList,aiCheck, addTestStructureRecord, updateTestStructureRecord, updateNullTestStructureRecord, deleteTestStructureRecord, exportModel,exportStructureData } from "@/api/modular/system/dpvTestManage";
  import { sysFileInfoUpload } from '@/api/modular/system/fileManage'
  import TagSelectOption from "../../../components/TagSelect/TagSelectOption";
  import exportModal from './export/index.vue'


  export default {
    components: {
      TagSelectOption,
      testNumberModal,
      testProjectModal,
      exportModal,
      pbiPreview
    },
    data() {
      return {

        aiAsking: false,
        spinning: false,
        isFrist:false,
        isEdit:false,
        isTestProjectShow: false,
        isShowTestNumberDetail: false,
        isShowExport:false,
			  previewVisible:false,
        previewFileName:'',
        previewFileID:'',
        selectedId: '',
        folderNoCheck: '',
        tlimsOrdtaskIdCheck: '',
        testProjectStructureDataId: '',
        secondCategoryType: 'dev_test_manage_second_category_safety',
        headers: {
          Authorization: "Bearer " + Vue.ls.get(ACCESS_TOKEN)
        },
        tableData: [],
        cDOptions: [
          {label: '5V100A16CH', value: '5V100A16CH'},
          {label: '5V200A16CH', value: '5V200A16CH'},
          {label: '5V300A8CH',  value: '5V300A8CH'},
          {label: '5V500A4CH',  value: '5V500A4CH'},
          {label: '5V600A4CH',  value: '5V600A4CH'},
          {label: '5V1000A2CH', value: '5V1000A2CH'},
          {label: '5V200A12CH', value: '5V200A12CH'},
        ],
        warmBoxOptions: [
          {label: 'BTG-512A',   value: 'BTG-512A'},
          {label: 'BTG-1440A',  value: 'BTG-1440A'},
          {label: 'BTG-620A',   value: 'BTG-620A'},
          {label: 'BTG-650A',   value: 'BTG-650A'},
          {label: 'BTT-960C',   value: 'BTT-960C'},
          {label: 'BTL-D2-300C',value: 'BTL-D2-300C'},
          {label: 'BTT-650C',   value: 'BTT-650C'},
        ],
        jjOptions: [
          {label: '普通夹具',value: '普通夹具'},
          {label: '水冷夹具',value: '水冷夹具'},
          {label: '膨胀力夹具',value: '膨胀力夹具'},
          {label: '膨胀力&水冷夹具',value: '膨胀力&水冷夹具'},
        ],
        tableColumns: [
          {
            title: '序号',
            dataIndex: 'index',
            align: 'center',
            width: 45,
            scopedSlots: { customRender: 'index' },
          },
          {
            title: '测试结果判定',
            dataIndex: 'judgeResult',
            align: 'center',
            className: 'selectInput-column-style',
            scopedSlots: { customRender: 'selectInput' },
            width:100,
          },
          {
            title: 'AI测试结果判定',
            dataIndex: 'judgeResultAI',
            align: 'center',
            width:100,
            scopedSlots: { customRender: 'judgeResultAI' },
          },
          {
            title: '测试项目名称（客户命名）',
            dataIndex: 'testProjectName',
            align: 'center',
            width:160,
            scopedSlots: { customRender: 'tooltipInput' },
            ellipsis: true,
          },
          {
            title: '测试项目',
            dataIndex: 'testContent',
            align: 'center',
            width:160,
            scopedSlots: { customRender: 'tooltipInput' },
            ellipsis: true,
          },
          {
            title: '测试单号选择',
            dataIndex: 'tLimsOrdtaskId',
            align: 'center',
            width: 120,
            scopedSlots: { customRender: 'tLimsOrdtaskId' },
          },
          {
            title: '一级分类',
            dataIndex: 'firstCategory',
            align: 'center',
            width: 90,
          },
          {
            title: '二级分类',
            dataIndex: 'secondCategory',
            align: 'center',
            width: 120,
          },
          {
            title: '测试项目名称（内部命名）',
            dataIndex: 'testName',
            align: 'center',
            width: 160,
            ellipsis: true,
            customRender: (text) => <a-tooltip title={text} placement='topLeft'>{text}</a-tooltip>
          },
          {
            title: '实际测试数量/pcs',
            dataIndex: 'realTestNumber',
            align: 'center',
            width: 120,
            scopedSlots: { customRender: 'realTestNumber' },
          }, 
          {
            title: '需求来源(PDF or EXCEL)',
            dataIndex: 'sourceFile',
            align: 'center',
            className: 'fileUpload-column-style',
            scopedSlots: { customRender: 'fileUpload' },
            width: 160,
            ellipsis: true,
          },
          {
            title: '判定标准',
            dataIndex: 'judgeStandard',
            align: 'center',
            className: 'tooltipInput-short-column-style',
            scopedSlots: { customRender: 'tooltipInput' },
            width: 160,
            ellipsis: true,
          },
          {
            title: '测试状态',
            dataIndex: 'testStatus',
            align: 'center',
            className: 'selectInput-column-style',
            scopedSlots: { customRender: 'selectInput' },
            width:100,
          },
          {
            title: '测试结果',
            dataIndex: 'testResultData',
            align: 'center',
            className: 'tooltipInput-short-column-style',
            scopedSlots: { customRender: 'tooltipInput' },
            width:160,
            ellipsis: true,
          },
          {
            title: '测试报告(PPT or EXCEL)',
            dataIndex: 'testReportFile',
            align: 'center',
            className: 'fileUpload-column-style',
            scopedSlots: { customRender: 'fileUpload' },
            width:160,
            ellipsis: true,
          },
          {
            title: '备注',
            dataIndex: 'remark',
            align: 'center',
            className: 'tooltipInput-short-column-style',
            scopedSlots: { customRender: 'tooltipInput' },
            width:160,
            ellipsis: true,
          },
          {
            title: '测试计划电芯样品数（计划）',
            align: 'center',
            children:[
              {
                title: 'BOL',
                align: 'center',
                dataIndex: 'bolMolEolNumList[0].bol',
                scopedSlots: { customRender: 'bolMolEol' },
                width: 70
              },{
                title: 'MOL',
                align: 'center',
                dataIndex: 'bolMolEolNumList[0].mol',
                scopedSlots: { customRender: 'bolMolEol' },
                width: 70
              },{
                title: 'EOL',
                align: 'center',
                dataIndex: 'bolMolEolNumList[0].eol',
                scopedSlots: { customRender: 'bolMolEol' },
                width: 70
              }
            ]
          },
          {
            title: '测试计划电芯样品数（实际）',
            align: 'center',
            children:[
              {
                title: 'BOL',
                align: 'center',
                dataIndex: 'bolMolEolNumList[1].bol',
                scopedSlots: { customRender: 'bolMolEol' },
                width: 70
              },{
                title: 'MOL',
                align: 'center',
                dataIndex: 'bolMolEolNumList[1].mol',
                scopedSlots: { customRender: 'bolMolEol' },
                width: 70
              },{
                title: 'EOL',
                align: 'center',
                dataIndex: 'bolMolEolNumList[1].eol',
                scopedSlots: { customRender: 'bolMolEol' },
                width: 70
              }
            ]
          },
          {
            title: '设备通道类型',
            align: 'center',
            dataIndex: 'bolMolEolNumList[2].typeValue',
            width: 100,
            scopedSlots: { customRender: 'bolMolEolType' },
          },
          {
            title: '单个测试项目下需求通道数（计划）',
            align: 'center',
            children:[
              {
                title: 'BOL',
                align: 'center',
                dataIndex: 'bolMolEolNumList[2].bol',
                scopedSlots: { customRender: 'bolMolEol' },
                width: 70
              },{
                title: 'MOL',
                align: 'center',
                dataIndex: 'bolMolEolNumList[2].mol',
                scopedSlots: { customRender: 'bolMolEol' },
                width: 70
              },{
                title: 'EOL',
                align: 'center',
                dataIndex: 'bolMolEolNumList[2].eol',
                scopedSlots: { customRender: 'bolMolEol' },
                width: 70
              }
            ]
          },
          {
            title: '温箱通道类型',
            align: 'center',
            dataIndex: 'bolMolEolNumList[3].typeValue',
            width: 100,
            scopedSlots: { customRender: 'bolMolEolType' },
          },
          {
            title: '单个测试项目下需求温箱数（计划）',
            align: 'center',
            children:[
              {
                title: 'BOL',
                align: 'center',
                dataIndex: 'bolMolEolNumList[3].bol',
                scopedSlots: { customRender: 'bolMolEol' },
                width: 70
              },{
                title: 'MOL',
                align: 'center',
                dataIndex: 'bolMolEolNumList[3].mol',
                scopedSlots: { customRender: 'bolMolEol' },
                width: 70
              },{
                title: 'EOL',
                align: 'center',
                dataIndex: 'bolMolEolNumList[3].eol',
                scopedSlots: { customRender: 'bolMolEol' },
                width: 70
              }
            ]
          },
          // {
          //   title: '夹具类型',
          //   align: 'center',
          //   dataIndex: 'bolMolEolNumList[4].typeValue',
          //   width: 100,
          //   scopedSlots: { customRender: 'bolMolEolType' },
          // },
          {
            title: '夹具数量（计划）',
            align: 'center',
            children:[
              {
                title: 'BOL',
                align: 'center',
                dataIndex: 'bolMolEolNumList[4].bol',
                // scopedSlots: { customRender: 'bolMolEol' },
                width: 70
              },{
                title: 'MOL',
                align: 'center',
                dataIndex: 'bolMolEolNumList[4].mol',
                // scopedSlots: { customRender: 'bolMolEol' },
                width: 70
              },{
                title: 'EOL',
                align: 'center',
                dataIndex: 'bolMolEolNumList[4].eol',
                // scopedSlots: { customRender: 'bolMolEol' },
                width: 70
              }
            ]
          },
          {
            title: '批次',
            dataIndex: 'cellBatch',
            align: 'center',
            width: 85,
          },
          {
            title: '测试地点',
            dataIndex: 'testLocation',
            align: 'center',
            width: 130,
            ellipsis: true,
            customRender: (text) => <a-tooltip title={text} placement='topLeft'>{text}</a-tooltip>
          },
          {
            title: '测试天数/D',
            dataIndex: 'testDays',
            align: 'center',
            width: 100,
            scopedSlots: { customRender: 'testDays' },
          },
          {
            title: '计划开始时间',
            dataIndex: 'planStartTime',
            align: 'center',
            width: 120,
            scopedSlots: { customRender: 'dataPicker' },
          },
          {
            title: '计划结束时间',
            dataIndex: 'planEndTime',
            align: 'center',
            width: 120,
            scopedSlots: { customRender: 'dataPicker' },
          },
          {
            title: '测试开始时间',
            dataIndex: 'realStartTime',
            align: 'center',
            width: 120,
            scopedSlots: { customRender: 'realStartTime' },
          },
          {
            title: '测试结束时间',
            dataIndex: 'realEndTime',
            align: 'center',
            width: 120,
            scopedSlots: { customRender: 'realEndTime' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 45,
            scopedSlots: { customRender: 'action' },
          },
          
          
         
          
         
         
          
          
          
          
          
        ],
      }
    },
    async created() {
      await this.getTestStructureList(this.$route.query, true)
      setTimeout(() => { this.getTestStructureFristList({ ...this.$route.query, isUpdateOrdtaskInfo: true }) }, 3000)
    },
    methods: {
      changeBolMolEol($event,record,columns,key){
        record.bolMolEolNumList[columns.dataIndex.replace("bolMolEolNumList[","").charAt(0)][key?key:columns.dataIndex.slice(-3)] = $event.target.value

         // 测试计划电芯样品数量 = 夹具数
        if(columns.dataIndex.indexOf('bolMolEolNumList[0]').indexOf !== -1){
          console.log(columns.dataIndex.slice(-3))
          record.bolMolEolNumList[4][key?key:columns.dataIndex.slice(-3)] = $event.target.value
        }

        let params = {}
        params.id = record.id
        params.bolMolEolNumJson = JSON.stringify(record.bolMolEolNumList)
        this.updateTestStructureRecord(params)
      },
      changeBolMolEolTypeName($event,record,columns){
        record.bolMolEolNumList[columns.dataIndex.replace("bolMolEolNumList[","").charAt(0)]["typeValue"] = $event
        let params = {}
        params.id = record.id
        params.bolMolEolNumJson = JSON.stringify(record.bolMolEolNumList)
        this.updateTestStructureRecord(params)
      },
      getDict(code) {
        const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
        return dictTypeTree.filter(item => item.code == code)[0].children
      },
      getTestStructureList(params, isSpin = false) {
        this.spinning = isSpin
        getTestStructureList(params).then(res => {
          res.data.forEach(v => {
            v.testReportFile = JSON.parse(v.testReportFile)
            v.sourceFile = JSON.parse(v.sourceFile)
          })
          this.tableData = res.data

          // 如果是空，自动添加一条
          if (this.tableData.length === 0 && isSpin) {
            const params = {
              serialNumber: 0,
              ...this.$route.query
            }
            addTestStructureRecord(params).then(res => {
              this.getTestStructureList(this.$route.query)
              this.isFrist = true
              // 首次进来
            })
          }
        }).finally(() => {
          this.spinning = false
        })
      },
      // 首次更新，为了无感刷新（只更新测试开始时间、测试结束时间）
      getTestStructureFristList(params) {
        if(this.isFrist) return this.isFrist = false
        getTestStructureList(params).then(res => {
          res.data.forEach((v, index) => {
            this.tableData[index].realStartTime = v.realStartTime
            this.tableData[index].realEndTime = v.realEndTime
          })
        })
      },

      updateTestStructureRecord(params) {
        updateTestStructureRecord(params).then(res => {
          if (!res.success) this.$message.warn('更新失败')
        }).finally(() => {
					this.getTestStructureList(this.$route.query)
				})
      },

      updateNullTestStructureRecord(params) {
        updateNullTestStructureRecord(params).then(res => {
          if (!res.success) this.$message.warn('更新失败')
        })
      },
      deleteReport(targetId) {
        deleteTestStructureRecord({ id: targetId }).then(res => {
          this.getTestStructureList(this.$route.query)
        })
      },
      customRowEvent(record, index) {
        return {
          on: {
            mouseenter: (event) => {
              $("#serialNumber" + index).find("#plusIcon").css('display', 'block')
            },
            mouseleave: (event) => {
              $("#serialNumber" + index).find("#plusIcon").css('display', 'none')
            }
          },
        };
      },
      handleChooseTestData(record) {
        // 记录是那条数据点击的选择
        this.selectedId = record.id

        // 如果已经选中了
        this.tlimsOrdtaskIdCheck = record.tLimsOrdtaskId ? record.tLimsOrdtaskId : ''
        this.folderNoCheck = record.folderNo ? record.folderNo : ''

        this.isTestProjectShow = true

      },
      handleChooseTestProject(e) {
        const params = {
          id: this.selectedId,
          tLimsOrdtaskId: e.tLimsOrdtaskId,
          folderNo: e.folderNo,
          firstCategory: e.firstcategory,
          secondCategory: e.secondcategory,
          testName: e.testname,
        }
        this.updateTestStructureRecord(params)
      },

      handleTime(date, dateString, record,{key}){
        // 如果是清空
        if(!(date && dateString)){
          const params = {
            id: record.id,
            updateNullColumn: key
          }
          return this.updateNullTestStructureRecord(params)
        }

        record[key] = dateString

        const params = {
          id: record.id,
          [key]: dateString
        }

        // 如果有测试天数
        if (record.testDays && key === 'planStartTime') {
          const endTime = this._addDate(dateString, record.testDays)
          record.planEndTime = endTime
          params.planEndTime = endTime
        }
        this.updateTestStructureRecord(params)

      },
      handleInputBlur(e, record, {key}) {

        // 如果产品规范-测试数量/pcs为空的时候
        if (!record[key] && key === 'planTestNumber') {
          const params = {
            id: record.id,
            updateNullColumn: key
          }
          return this.updateNullTestStructureRecord(params)
        }

        // 如果测试天数/D为空的时候
        if (!record[key] && key === 'testDays') {
          record.planEndTime = ''
          return ['testDays','planEndTime'].forEach(v => {
            const params = {
              id: record.id,
              updateNullColumn: v
            }
            this.updateNullTestStructureRecord(params)
          })
        }

        const params = {
          id: record.id,
          [key]: record[key] == null || record[key] == '' ? '' : record[key]
        }
        if (key === 'testDays' && record.planStartTime) {
          const endTime = this._addDate(record.planStartTime, record.testDays)
          record.planEndTime = endTime
          params.planEndTime = endTime
        }
        this.updateTestStructureRecord(params)
      },
      handleAddRow(index) {
        let params = {}
        if (index !== undefined) {
          if (this.tableData.length > 1 && index < this.tableData.length - 1) {
            let current = parseInt(this.tableData[index].serialNumber);
            let lastNumber = parseInt(this.tableData[index + 1].serialNumber);
            let subtracted = (lastNumber - current) / 10;
            params.serialNumber = lastNumber - subtracted
          } else if (this.tableData.length > 1 && index === this.tableData.length - 1) {
            params.serialNumber = parseInt(this.tableData[this.tableData.length - 1].serialNumber) + 10000000000000
          } else {
            params.serialNumber = parseInt(this.tableData[this.tableData.length - 1].serialNumber) + 10000000000000
          }
        } else if (this.tableData.length === 0) {
          params.serialNumber = 0
        } else {
          params.serialNumber = parseInt(this.tableData[this.tableData.length - 1].serialNumber) + 10000000000000
        }

        params.dpvTestProgressRecordId = this.$route.query.dpvTestProgressRecordId
        params.phase = this.$route.query.phase

        addTestStructureRecord(params).then(res => {
          this.getTestStructureList(this.$route.query)
        })
      },
      // handleFirstCategoryChange(e, record, target) {

      //   switch (e) {
      //     case '安全':
      //       this.secondCategoryType = 'dev_test_manage_second_category_safety'
      //       record.secondCategory = '电滥用测试'
      //       break;
      //     case '电性能':
      //       this.secondCategoryType = 'dev_test_manage_second_category_property'
      //       record.secondCategory = 'SOC'
      //       break;
      //     case '委外':
      //       this.secondCategoryType = 'dev_test_manage_second_category_outsource'
      //       record.secondCategory = '委外测试'
      //       break;
      //   }

      //   const params = {
      //     id: record.id,
      //     firstCategory: e,
      //     secondCategory: record.secondCategory
      //   }
      //   this.updateTestStructureRecord(params)

      // },
      // handleSecondCategoryChange(record) {
      //   switch (record.firstCategory) {
      //     case '安全':
      //       this.secondCategoryType = 'dev_test_manage_second_category_safety'
      //       break;
      //     case '电性能':
      //       this.secondCategoryType = 'dev_test_manage_second_category_property'
      //       break;
      //     case '委外':
      //       this.secondCategoryType = 'dev_test_manage_second_category_outsource'
      //       break;
      //   }
      // },


      handleCancel(target) {
        this[target] = false
        if (target === 'isTestProjectShow') {
          this.getTestStructureList(this.$route.query)
        }
      },
      handleDetail(record) {
        this.testProjectStructureDataId = record.id
        this.isShowTestNumberDetail = true
      },
      /**
       * 上传文件
       */
      customRequest(data, index, {key}) {
        // 需求来源：pdf + excel
        if(key === 'sourceFile'){
          if(!['.xlsx','.pdf'].some(someItem => data.file.name.includes(someItem))){
            return this.$message.warn('请上传Excel或pdf')
          }
        // 测试报告：ppt + excel
        }else if(key === 'testReportFile'){
          if(!['.xlsx','.pptx'].some(someItem => data.file.name.includes(someItem))){
            return this.$message.warn('请上传Excel或ppt')
          }
        }
        

        const formData = new FormData()
        formData.append('file', data.file)
        sysFileInfoUpload(formData).then((res) => {
          if (res.success) {
            this.$message.success('上传成功')

            const fileList = this.tableData[index][key] || []

            fileList.push({id:res.data,name:data.file.name})
            const params = {
              id: this.tableData[index].id,
              [key]: JSON.stringify(fileList),
            }
            this.updateTestStructureRecord(params)
            this.$forceUpdate()
          } else {
            this.$message.error('上传失败：' + res.message)
          }
        })
      },

      //预览文件
      previewFile(value){
        // 如果是可预览的
        if(['.xbm','.tif','.pjp','.svgz','.jpg','.jpeg','.ico','.tiff','.gif','.svg','.jfif','.webp','.png','.bmp','.pjpeg','.avif','.pdf'].some(someItem => { return typeof value.name === 'string' && value.name.indexOf(someItem) !== -1})){
          this.previewVisible = true
          this.previewFileName = value.name
          this.previewFileID = value.id
        }else{
          // 不可预览就下载
          const a = document.createElement('a') 
          a.style.display = 'none'
          a.href = '/api/sysFileInfo/preview?Authorization=Bearer '+Vue.ls.get('Access-Token')+'&id=' + value.id + "#navpanes=0"
          a.download = value.name
          a.click()
        }

      },

      // 删除文件
      handleDelFile(index,{key},targetId){
        const haveIndex = this.tableData[index][key].findIndex(findItem => findItem.id === targetId)
        this.tableData[index][key].splice(haveIndex,1)
        const params = {
          id: this.tableData[index].id,
          [key]: JSON.stringify(this.tableData[index][key]),
        }
        this.updateTestStructureRecord(params)
      },

      // 导入模板
      handleUploadFile(info) {
        if (info.file.status === "done") {
          this.$message.success(`${info.file.name} 文件上传成功`)
          this.handleRefresh()
        } else if (info.file.status === "error") {
          this.$message.error(`${info.file.name} 文件上传失败`)
        }
      },
      // 下载处理
      handleDownload() {
        exportModel({}).then(res => {
          if (res) {
            // 项目名称+阶段（例如：A0、A1）+DPV测试进展
            downloadfile1(res,  this.$route.query.projectName + '-' + this.$route.query.phase + '-DPV测试进展填写模版.xlsx')
          }
        })
      },
      // 刷新
      handleRefresh() {
        this.getTestStructureList({ ...this.$route.query }, true)
      },

      aiCheckResult(){
        this.aiAsking = true
        aiCheck({ ...this.$route.query }).then(() =>{
            this.getTestStructureList({ ...this.$route.query })
        } ).finally(() => this.aiAsking = false)

      },

      // date为相加前的时间， days 为 需要相加的天数
      _addDate(date, days) {
        var date = new Date(date);
        date.setDate(date.getDate() + days);
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var day = date.getDate();
        var mm = "'" + month + "'";
        var dd = "'" + day + "'";
        if (mm.length == 3) {
          month = "0" + month;
        }
        if (dd.length == 3) {
          day = "0" + day;
        }
        var time = year + "-" + month + "-" + day
        return time;
      },

      // 导出
      handleExport(e){
        const params = {
          dpvTestProgressRecordId:this.$route.query.dpvTestProgressRecordId,
          phase:this.$route.query.phase,
          selectedColList:e
        }
        exportStructureData(params).then(res => {
          if (res) {
            downloadfile1(res, this.$route.query.productName + '-' + this.$route.query.phase + '-测试项目结构化.xlsx')
          }
        }).finally(() => {
          this.isShowExport = false
        })

      },
    }
  }
</script>
<style scoped lang="less">
  .rollback-icon {
    margin-right: 4px;
  }

  /* 标题 */
  .head-title {
    display: flex;
    align-items: center;
  }

  .head-title .line {
    width: 4px;
    height: 22px;
    background: #3293ff;
    border-radius: 20px;
  }

  .head-title .title {
    font-size: 16px;
    font-weight: 600;
  }

  /* 筛选 */
  .filter-wrapper {
    display: flex;
    justify-content: space-between;
  }

  .filter-right {
    display: flex;
  }

  .table-wrapper {
    padding: 10px;
    background: #fff;
    border-radius: 10px;
    width: calc(100vw - 20px - 20px - 20px);
    height: calc(100vh - 20px - 18px - 24px - 10px - 10px - 20px - 20px);
    overflow: hidden;
  }

  .table-content {
    width: 100%;
    height: 100%;
    overflow: scroll;
  }

  .file-content{
    cursor: pointer;
    color: #1890ff;
    display: flex;
    align-items: center;
    padding: 2px 4px;
  }
  .file-title{
    width: 150px;
    text-align: left;
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
  }

  .serial-number-content {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .serial-number-content .plus-icon {
    display: none;
    margin-right: 5px;
    cursor: pointer;
  }

  .input-search-block {
    display: flex;
  }

  .choose-btn {
    font-size: 12px;
  }

  

  .mt10 {
    margin-top: 10px;
  }

  /deep/ .ant-btn {
    font-size: 12px;
  }

  /deep/.ant-input {
    font-size: 12px;
  }

  /deep/.ant-select {
    font-size: 12px;
  }

  /deep/ .ant-breadcrumb {
    font-size: 12px;
  }

  /deep/ .ant-table-thead {
    position: sticky;
    top: 0;
    z-index: 12;
  }

  /deep/.ant-table-thead>tr>th,
  /deep/.ant-table-tbody>tr>td {
    padding: 4px;
  }

  /deep/.ant-table-tbody>tr>td {
    background-color: #fff;
  }

  /deep/ .table-wrapper .ant-input-number-input {
    text-align: center !important;
  }

  /deep/ .tooltipInput-long-column-style {
    min-width: 160px;
  }
  /deep/ .tooltipInput-short-column-style {
    min-width: 100px;
  }
  /deep/ .selectInput-column-style {
    min-width: 100px;
  }

  /deep/ .fileUpload-column-style {
    min-width: 140px;
  }

  /* 固定列 */
  /deep/ .table-content .ant-table-thead tr:nth-child(1) th:nth-child(1),
  /deep/ .table-content .ant-table-tbody tr td:nth-child(1){
    position: sticky;
    left: 0;
    z-index: 11;
  }
  /deep/ .table-content .ant-table-thead tr:nth-child(1) th:nth-child(4),
  /deep/ .table-content .ant-table-tbody tr td:nth-child(4){
    position: sticky;
    left: 45px;
    z-index: 11;
  }
  /deep/ .table-content .ant-table-thead tr:nth-child(1) th:nth-child(5),
  /deep/ .table-content .ant-table-tbody tr td:nth-child(5){
    position: sticky;
    left: 205px;
    z-index: 11;
  }


</style>
<style lang='less'>
  .dropdownClassName {
    .ant-select-dropdown-menu-item {
      text-align: center;
      padding: 2px 4px;
      font-size: 12px;
    }
  }

</style>
