<template>
  <a-modal title="审批记录" width="80%" :visible="visible" :confirmLoading="confirmLoading" :footer="null"
           @cancel="handleCancel">
    <a-table :columns="columns" :data-source="data" :rowKey="(record) => record.id"
    >


      <template slot="url" slot-scope="text, record">
        <a @click="openJIRA(text)" v-if="text != null">JIRA</a>
        <span v-else>/</span>
      </template>


    </a-table>


  </a-modal>
</template>

<script>
  import Vue from 'vue'
  import {
    checkRecordGetListByBusinessId,checkRecordSubmitCheck
  } from '@/api/modular/system/batterydesignCheckRecordManage'

  import moment from "moment";

  export default {
    data() {
      return {
        data:[],
        //操作 0 提交 10 审核 20  批准 30 归档 40 结束
        operate:{0:'提交',10:'审核',20:'批准',30:'归档',40:'结束流程'},
        columns: [

          {
            title: '时间',
            width: 70,
            dataIndex: 'createTime',
            align: 'center',
            customRender: (text, record, index) => {
              if(null != text){
                return moment(text).format('YYYY-MM-DD HH:mm')
              }
            }
          }, {
            title: '操作人',
            width: 60,
            dataIndex: 'checkJson',
            align: 'center',
            customRender: (text, record, index) => {


              /**
               * 1//项目产品SOR审批(A/B样)
               * 2//项目产品冻结审批(A样)
               * 3//项目产品冻结审批(B样)
               * 4//项目产品方案制样审批(A/B样)
               * 5//预研产品SOR审批(A/B样)
               * 6//预研产品冻结审批
               * 7//预研产品方案制样审批
               **/

              let checkMans = JSON.parse(text)
              let business = JSON.parse(record.businessJson)
              let relation = JSON.parse(record.relationJson)
              if(record.operate == 0){
                return record.createName
              }
              if(record.operate == 40){
                return '系统'
              }
              //审核
              if(record.operate == 10){
                if("sor" == record.businessType){
                  //预研产品SOR
                  if(record.issueType == 5){
                    return checkMans.zj.split("-")[1]
                  }


                  //S级
                  if(business.grade == 'S'){
                    return checkMans.sz.split("-")[1]
                  }else{
                    return checkMans.zj.split("-")[1]
                  }
                }
                if("design" == record.businessType){
                  return checkMans.zj.split("-")[1]
                }
                if("freeze" == record.businessType){
                  //A样
                  if('a' == relation.stage){
                    if(business.grade == 'S'){
                      return checkMans.sz.split("-")[1]
                    }else {
                      return checkMans.zj.split("-")[1]
                    }
                  }else{
                    return checkMans.sz.split("-")[1]
                  }
                }


              }
              //批准
              if(record.operate == 20){
                if("sor" == record.businessType){

                  //预研产品SOR
                  if(record.issueType == 5){
                    return checkMans.sz.split("-")[1]
                  }

                  //S级
                  if(business.grade == 'S'){
                    return checkMans.yz.split("-")[1]
                  }else{
                    return checkMans.sz.split("-")[1]
                  }

                }

                if("design" == record.businessType){
                  return checkMans.sz.split("-")[1]
                }
                if("freeze" == record.businessType){

                  if(6 == record.issueType){
                    return checkMans.sz.split("-")[1]
                  }

                  //A样
                  if('a' == relation.stage){
                    if(business.grade == 'S'){
                      return checkMans.yz.split("-")[1]
                    }else {
                      return checkMans.sz.split("-")[1]
                    }
                  }else{
                    return checkMans.yz.split("-")[1]
                  }
                }


              }
              //文控
              if(record.operate == 30){

                return checkMans.wk.split("-")[1]
              }
            }
          }, {
            title: '操作',
            width: 60,
            dataIndex: 'operate',
            align: 'center',
            customRender: (text, record, index) => {
              return this.operate[text]
            }
          }, {
            title: '地址',
            width: 60,
            dataIndex: 'url',
            align: 'center',
            scopedSlots: {
              customRender: 'url'
            }
          },{
            title: '处理结果',
            width: 60,
            dataIndex: 'handleResult',
            align: 'center',
            customRender: (text, record, index) => {
              if(text == 10){
                return '同意'
              }else if(text == 20 && record.operate == 0){
                return '废弃'
              }else if(text == 20){
                return '驳回'
              }else{
                return '/'
              }
            }
          },  {
            title: '处理意见',
            width: 140,
            dataIndex: 'handleRemark',
            align: 'center',
            customRender: (text, record, index) => {
              if(text == null){
                return '/'
              }
              return text

            }
          },

        ],

        visible: false,
        confirmLoading: false,
      }
    },

    methods: {
      openJIRA(url){
        let $url = `http://jira.evebattery.com/browse/`+ url+`?auth=` + Vue.ls.get("jtoken");
        window.open($url, "_blank");
      },

      open(businessId,type) {
        checkRecordGetListByBusinessId({businessId:businessId,businessType:type}).then((res) => {
          this.data = res.data
        })
        this.visible = true
      },
      handleCancel() {
        this.data = []
        this.visible = false
      }
    }
  }
</script>
<style lang="less" scoped>
  .ant-form-item {
    margin-bottom: 0px;
  }

  .title {
    margin-bottom: 20px;
  }

  .xing {
    color: red;
    font-weight: bolder;
  }

  .label {
    width: 30%;
    display: inline-flex;
  }

  .theme {
    font-size: larger;
    font-weight: bolder;
    margin-bottom: 20px;
  }

  div {
    color: black;
  }

  span {
    color: black;
  }

  .select {
    margin-bottom: 3px;
  }

  .check {
    height: 210px;
  }

  .approve {
    height: 90px;
  }

  /deep/ .ant-modal-title {

    font-weight: bolder;
    font-size: 22px;
    color: #0049b0;
  }
  /deep/.ant-table-tbody tr td{
	padding: 10px 16px !important;
}
</style>
