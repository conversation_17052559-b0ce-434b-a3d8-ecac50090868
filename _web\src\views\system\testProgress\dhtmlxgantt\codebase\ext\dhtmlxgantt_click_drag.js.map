{"version": 3, "sources": ["webpack://[name]/webpack/universalModuleDefinition", "webpack://[name]/webpack/bootstrap", "webpack://[name]/./sources/utils/helpers.js", "webpack://[name]/./sources/ext/click_drag/selectedRegion.ts", "webpack://[name]/./sources/ext/click_drag/eventsManager.ts", "webpack://[name]/./sources/ext/click_drag/index.ts", "webpack://[name]/./sources/utils/eventable.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "window", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "units", "second", "minute", "hour", "day", "week", "month", "quarter", "year", "arrayFilter", "arr", "callback", "result", "filter", "length", "getSecondsInUnit", "unit", "for<PERSON>ach", "workArray", "slice", "arrayMap", "map", "resArray", "push", "arrayFind", "find", "arrayDifference", "item", "arraySome", "hashToArray", "hash", "sortArrayOfHash", "field", "desc", "compare", "a", "b", "sort", "throttle", "timeout", "wait", "apply", "arguments", "setTimeout", "isArray", "obj", "Array", "undefined", "pop", "isDate", "getFullYear", "getMonth", "getDate", "isStringObject", "Function", "toString", "constructor", "isNumberObject", "isBooleanObject", "delay", "timer", "$cancelTimeout", "$pending", "args", "this", "clearTimeout", "$execute", "objectKeys", "keys", "requestAnimationFrame", "w", "webkitRequestAnimationFrame", "msRequestAnimationFrame", "mozRequestAnimationFrame", "oRequestAnimationFrame", "cb", "isEventable", "attachEvent", "detachEvent", "eventable", "helpers_1", "_countSize", "start", "end", "SelectedRegion", "config", "_this", "_el", "document", "createElement", "_viewPort", "viewPort", "classList", "add", "className", "_callback", "render", "_startPoint", "_endPoint", "draw", "_singleRow", "singleRow", "_useRequestAnimationFrame", "useRequestAnimationFrame", "setStyles", "height", "gantt", "row_height", "style", "top", "Math", "ceil", "_positionPoint", "relative", "width", "left", "append<PERSON><PERSON><PERSON>", "getElement", "clear", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "getViewPort", "setStart", "startPoint", "_startDate", "dateFromPos", "callEvent", "setEnd", "endPoint", "_endDate", "absolute", "setPosition", "positionPoint", "dragEnd", "valueOf", "_a", "tasksByTime", "getTaskByTime", "tasksByIndex", "_getTasksByTop", "getInBounds", "startValue", "endValue", "startIndex", "endIndex", "getTaskByIndex", "EventsManager", "_mouseDown", "_domEvents", "_createDomEventScope", "attach", "selectedRegion", "useKey", "_target", "_originPosition", "getComputedStyle", "display", "_restoreOriginPosition", "position", "state", "$services", "getService", "registerProvider", "autoscroll", "scheduledDndCoordinates", "event", "utils", "dom", "closest", "target", "_getCoordinates", "body", "coordinates", "abs", "detach", "detachAll", "unregisterProvider", "destructor", "viewPortBounds", "getBoundingClientRect", "clientX", "clientY", "scrollLeft", "scrollTop", "eventsManager_1", "selectedRegion_1", "ext", "defaultConfig", "eventsManager", "clickDrag", "__assign", "$task_data", "click_drag", "EventHost", "_connected", "_silent_mode", "_silentStart", "_silentEnd", "createEventStorage", "dhx_catch", "z", "res", "zr", "addEvent", "ev", "removeEvent", "id", "eventHost", "catcher", "callObj", "toLowerCase", "attachAll", "arg0", "handler<PERSON>ame", "concat", "checkEvent", "list", "split", "eventName", "eventId", "detachAllEvents", "indexOf"], "mappings": ";;;;;;;;;;;;CAAA,SAAAA,EAAAC,GACA,iBAAAC,SAAA,iBAAAC,OACAA,OAAAD,QAAAD,IACA,mBAAAG,eAAAC,IACAD,OAAA,gCAAAH,GACA,iBAAAC,QACAA,QAAA,8BAAAD,IAEAD,EAAA,8BAAAC,IARA,CASCK,OAAA,WACD,mBCTA,IAAAC,KAGA,SAAAC,EAAAC,GAGA,GAAAF,EAAAE,GACA,OAAAF,EAAAE,GAAAP,QAGA,IAAAC,EAAAI,EAAAE,IACAC,EAAAD,EACAE,GAAA,EACAT,YAUA,OANAU,EAAAH,GAAAI,KAAAV,EAAAD,QAAAC,IAAAD,QAAAM,GAGAL,EAAAQ,GAAA,EAGAR,EAAAD,QA0DA,OArDAM,EAAAM,EAAAF,EAGAJ,EAAAO,EAAAR,EAGAC,EAAAQ,EAAA,SAAAd,EAAAe,EAAAC,GACAV,EAAAW,EAAAjB,EAAAe,IACAG,OAAAC,eAAAnB,EAAAe,GAA0CK,YAAA,EAAAC,IAAAL,KAK1CV,EAAAgB,EAAA,SAAAtB,GACA,oBAAAuB,eAAAC,aACAN,OAAAC,eAAAnB,EAAAuB,OAAAC,aAAwDC,MAAA,WAExDP,OAAAC,eAAAnB,EAAA,cAAiDyB,OAAA,KAQjDnB,EAAAoB,EAAA,SAAAD,EAAAE,GAEA,GADA,EAAAA,IAAAF,EAAAnB,EAAAmB,IACA,EAAAE,EAAA,OAAAF,EACA,KAAAE,GAAA,iBAAAF,QAAAG,WAAA,OAAAH,EACA,IAAAI,EAAAX,OAAAY,OAAA,MAGA,GAFAxB,EAAAgB,EAAAO,GACAX,OAAAC,eAAAU,EAAA,WAAyCT,YAAA,EAAAK,UACzC,EAAAE,GAAA,iBAAAF,EAAA,QAAAM,KAAAN,EAAAnB,EAAAQ,EAAAe,EAAAE,EAAA,SAAAA,GAAgH,OAAAN,EAAAM,IAAqBC,KAAA,KAAAD,IACrI,OAAAF,GAIAvB,EAAA2B,EAAA,SAAAhC,GACA,IAAAe,EAAAf,KAAA2B,WACA,WAA2B,OAAA3B,EAAA,SAC3B,WAAiC,OAAAA,GAEjC,OADAK,EAAAQ,EAAAE,EAAA,IAAAA,GACAA,GAIAV,EAAAW,EAAA,SAAAiB,EAAAC,GAAsD,OAAAjB,OAAAkB,UAAAC,eAAA1B,KAAAuB,EAAAC,IAGtD7B,EAAAgC,EAAA,aAIAhC,IAAAiC,EAAA,uBClFA,IAAAC,GACAC,OAAA,EACAC,OAAA,GACAC,KAAA,KACAC,IAAA,MACAC,KAAA,OACAC,MAAA,OACAC,QAAA,OACAC,KAAA,SAgFA,SAAAC,EAAAC,EAAAC,GACA,IAAAC,KAEA,GAAAF,EAAAG,OACA,OAAAH,EAAAG,OAAAF,GAEA,QAAA3C,EAAA,EAAiBA,EAAA0C,EAAAI,OAAgB9C,IACjC2C,EAAAD,EAAA1C,QACA4C,IAAAE,QAAAJ,EAAA1C,IAGA,OAAA4C,EAkHAnD,EAAAD,SACAuD,iBA5MA,SAAAC,GACA,OAAAhB,EAAAgB,IAAAhB,EAAAG,MA4MAc,QAzMA,SAAAP,EAAAC,GACA,GAAAD,EAAAO,QACAP,EAAAO,QAAAN,QAGA,IADA,IAAAO,EAAAR,EAAAS,QACAnD,EAAA,EAAiBA,EAAAkD,EAAAJ,OAAsB9C,IACvC2C,EAAAO,EAAAlD,OAoMAoD,SA/LA,SAAAV,EAAAC,GACA,GAAAD,EAAAW,IACA,OAAAX,EAAAW,IAAAV,GAKA,IAHA,IAAAO,EAAAR,EAAAS,QACAG,KAEAtD,EAAA,EAAiBA,EAAAkD,EAAAJ,OAAsB9C,IACvCsD,EAAAC,KAAAZ,EAAAO,EAAAlD,OAEA,OAAAsD,GAsLAE,UAjLA,SAAAd,EAAAC,GACA,GAAAD,EAAAe,KACA,OAAAf,EAAAe,KAAAd,GAEA,QAAA3C,EAAA,EAAiBA,EAAA0C,EAAAI,OAAgB9C,IACjC,GAAA2C,EAAAD,EAAA1C,MACA,OAAA0C,EAAA1C,IA4KAyC,cACAiB,gBA7FA,SAAAhB,EAAAC,GACA,OAAAF,EAAAC,EAAA,SAAAiB,EAAA3D,GACA,OAAA2C,EAAAgB,EAAA3D,MA4FA4D,UAzGA,SAAAlB,EAAAC,GACA,OAAAD,EAAAI,OAAA,SAEA,QAAA9C,EAAA,EAAgBA,EAAA0C,EAAAI,OAAgB9C,IAChC,GAAA2C,EAAAD,EAAA1C,KAAA0C,GACA,SAGA,UAkGAmB,YAtHA,SAAAC,GACA,IAAAlB,KAEA,QAAArB,KAAAuC,EACAA,EAAAjC,eAAAN,IACAqB,EAAAW,KAAAO,EAAAvC,IAIA,OAAAqB,GA8GAmB,gBAlDA,SAAArB,EAAAsB,EAAAC,GACA,IAAAC,EAAA,SAAAC,EAAAC,GACA,OAAAD,EAAAC,GAGA1B,EAAA2B,KAAA,SAAAF,EAAAC,GACA,OAAAD,EAAAH,KAAAI,EAAAJ,GAAA,EAEAC,EAAAC,EAAAC,EAAAH,GAAAI,EAAAJ,IAAAE,EAAAE,EAAAJ,GAAAG,EAAAH,OA2CAM,SA3FA,SAAA3B,EAAA4B,GACA,IAAAC,GAAA,EAEA,kBACAA,IACA7B,EAAA8B,MAAA,KAAAC,WACAF,GAAA,EACAG,WAAA,WACAH,GAAA,GACID,MAmFJK,QA3KA,SAAAC,GACA,OAAAC,MAAAF,QACAE,MAAAF,QAAAC,GAGAA,QAAAE,IAAAF,EAAA/B,QAAA+B,EAAAG,KAAAH,EAAAtB,MAuKA0B,OAjJA,SAAAJ,GACA,SAAAA,GAAA,iBAAAA,KACAA,EAAAK,aAAAL,EAAAM,UAAAN,EAAAO,WAgJAC,eAnKA,SAAAR,GACA,OAAAA,GAAA,iBAAAA,GACA,wCAAAS,SAAA1D,UAAA2D,SAAApF,KAAA0E,EAAAW,cAkKAC,eA9JA,SAAAZ,GACA,OAAAA,GAAA,iBAAAA,GACA,wCAAAS,SAAA1D,UAAA2D,SAAApF,KAAA0E,EAAAW,cA6JAE,gBAzJA,SAAAb,GACA,OAAAA,GAAA,iBAAAA,GACA,yCAAAS,SAAA1D,UAAA2D,SAAApF,KAAA0E,EAAAW,cAwJAG,MAnFA,SAAAhD,EAAA4B,GACA,IAAAqB,EAEAhD,EAAA,WACAA,EAAAiD,iBACAlD,EAAAmD,UAAA,EACA,IAAAC,EAAAjB,MAAAlD,UAAAuB,MAAAhD,KAAAuE,WACAkB,EAAAjB,WAAA,WACAhC,EAAA8B,MAAAuB,KAAAD,GACAnD,EAAAkD,UAAA,GACGvB,IAaH,OAVA3B,EAAAkD,UAAA,EACAlD,EAAAiD,eAAA,WACAI,aAAAL,GACAjD,EAAAmD,UAAA,GAEAlD,EAAAsD,SAAA,WACAvD,IACAA,EAAAkD,kBAGAjD,GA6DAuD,WA9CA,SAAAtB,GACA,GAAAnE,OAAA0F,KACA,OAAA1F,OAAA0F,KAAAvB,GAEA,IACAtD,EADAqB,KAEA,IAAArB,KAAAsD,EACAnE,OAAAkB,UAAAC,eAAA1B,KAAA0E,EAAAtD,IACAqB,EAAAW,KAAAhC,GAGA,OAAAqB,GAoCAyD,sBAjCA,SAAA1D,GACA,IAAA2D,EAAA1G,OAOA,OANA0G,EAAAD,uBACAC,EAAAC,6BACAD,EAAAE,yBACAF,EAAAG,0BACAH,EAAAI,wBACA,SAAAC,GAAmBhC,WAAAgC,EAAA,UACnBhE,IA0BAiE,YAvBA,SAAA/B,GACA,OAAAA,EAAAgC,aAAAhC,EAAAiC,iGClNA,IAAAC,EAAAjH,EAAA,GACAkH,EAAAlH,EAAA,GAuBA,SAASmH,EAAWC,EAAeC,GAClC,IAAMvE,EAASsE,EAAQC,EACvB,OAAIvE,GAAU,EACNA,GAEAA,EAGT,IAAAwE,EAAA,WAWC,SAAAA,EAAYC,GAAZ,IAAAC,EAAAtB,KATQA,KAAAuB,IAAmBC,SAASC,cAAc,OAUjDzB,KAAK0B,UAAYL,EAAOM,SACxB3B,KAAKuB,IAAIK,UAAUC,IAAIR,EAAOS,WACC,mBAApBT,EAAO1E,WACjBqD,KAAK+B,UAAYV,EAAO1E,UAEI,mBAAlB0E,EAAOW,SACjBhC,KAAKgC,OAAS,WACbV,EAAKC,IAAMF,EAAOW,OAAOV,EAAKW,YAAaX,EAAKY,WACvB,KAArBb,EAAOS,WACVR,EAAKC,IAAIK,UAAUC,IAAIR,EAAOS,WAE/BR,EAAKa,SAGFnB,EAAAJ,YAAYZ,KAAK0B,YACrBX,EAAUf,KAAK0B,WAEhB1B,KAAKoC,WAAaf,EAAOgB,UACzBrC,KAAKsC,0BAA4BjB,EAAOkB,yBAmI1C,OAhICnB,EAAAxF,UAAA4G,UAAA,WACC,GAAIxC,KAAKoC,WAAY,CACpB,IAAMK,EAASC,MAAMrB,OAAOsB,WAC5B3C,KAAKuB,IAAIqB,MAAMH,OAASA,EAAS,KACjCzC,KAAKuB,IAAIqB,MAAMC,KAAOC,KAAKC,KAAK/C,KAAKgD,eAAeC,SAASJ,IAAMJ,GAAU,GAAKA,EAAS,UAE3FzC,KAAKuB,IAAIqB,MAAMH,OAASxB,EAAWjB,KAAKkC,UAAUe,SAASJ,IAAK7C,KAAKiC,YAAYgB,SAASJ,KAAO,KACjG7C,KAAKuB,IAAIqB,MAAMC,IAAM7C,KAAKgD,eAAeC,SAASJ,IAAM,KAEzD7C,KAAKuB,IAAIqB,MAAMM,MAAQjC,EAAWjB,KAAKkC,UAAUe,SAASE,KAAMnD,KAAKiC,YAAYgB,SAASE,MAAQ,KAClGnD,KAAKuB,IAAIqB,MAAMO,KAAOnD,KAAKgD,eAAeC,SAASE,KAAO,MAG3D/B,EAAAxF,UAAAoG,OAAA,WACChC,KAAKwC,YACLxC,KAAKmC,QAGNf,EAAAxF,UAAAuG,KAAA,eAAAb,EAAAtB,KACC,GAAIA,KAAKsC,0BACR,OAAOtB,EAAAX,sBAAsB,WAC5BiB,EAAKI,UAAU0B,YAAY9B,EAAK+B,gBAGjCrD,KAAK0B,UAAU0B,YAAYpD,KAAKqD,eAIlCjC,EAAAxF,UAAA0H,MAAA,eAAAhC,EAAAtB,KACC,GAAIA,KAAKsC,0BACR,OAAOtB,EAAAX,sBAAsB,WACvBiB,EAAKC,IAAIgC,YAGdjC,EAAKI,UAAU8B,YAAYlC,EAAKC,OAG5BvB,KAAKuB,IAAIgC,YAGdvD,KAAK0B,UAAU8B,YAAYxD,KAAKuB,MAIlCH,EAAAxF,UAAAyH,WAAA,WACC,OAAOrD,KAAKuB,KAGbH,EAAAxF,UAAA6H,YAAA,WACC,OAAOzD,KAAK0B,WAGbN,EAAAxF,UAAA8H,SAAA,SAASC,GACR3D,KAAKiC,YAAc0B,EACnB3D,KAAK4D,WAAalB,MAAMmB,YAAY7D,KAAKiC,YAAYgB,SAASE,MAC9DnD,KAAK0B,UAAUoC,UAAU,gBAAiB9D,KAAKiC,eAGhDb,EAAAxF,UAAAmI,OAAA,SAAOC,GAEN,GADAhE,KAAKkC,UAAY8B,EACbhE,KAAKoC,WAAY,CACpB,IAAMK,EAASC,MAAMrB,OAAOsB,WAC5B3C,KAAKkC,UAAUe,SAASJ,IAAOC,KAAKC,KAAK/C,KAAKiC,YAAYgB,SAASJ,IAAMJ,GAAWA,EAErFzC,KAAKiE,SAAWvB,MAAMmB,YAAY7D,KAAKkC,UAAUe,SAASE,MACtDnD,KAAKiC,YAAYgB,SAASE,KAAOnD,KAAKkC,UAAUe,SAASE,OAC5DnD,KAAKgD,gBACJC,UAAYE,KAAMnD,KAAKkC,UAAUe,SAASE,KAAMN,IAAK7C,KAAKgD,eAAeC,SAASJ,KAClFqB,UAAYf,KAAMnD,KAAKkC,UAAUgC,SAASf,KAAMN,IAAK7C,KAAKgD,eAAekB,SAASrB,OAGhF7C,KAAKiC,YAAYgB,SAASJ,IAAM7C,KAAKkC,UAAUe,SAASJ,MAC3D7C,KAAKgD,gBACJC,UAAYE,KAAMnD,KAAKgD,eAAeC,SAASE,KAAMN,IAAK7C,KAAKkC,UAAUe,SAASJ,KAClFqB,UAAYf,KAAMnD,KAAKgD,eAAekB,SAASf,KAAMN,IAAK7C,KAAKkC,UAAUgC,SAASrB,OAKpF7C,KAAK0B,UAAUoC,UAAU,UAAW9D,KAAKiC,YAAajC,KAAKkC,aAG5Dd,EAAAxF,UAAAuI,YAAA,SAAYC,GACXpE,KAAKgD,eAAiBoB,GAGvBhD,EAAAxF,UAAAyI,QAAA,SAAQL,SACJA,EAASf,SAASE,KAAO,IAC3Ba,EAASf,SAASE,KAAO,GAE1BnD,KAAK0B,UAAUoC,UAAU,mBAAoB9D,KAAKiC,YAAa+B,IAC/DhE,KAAK+D,OAAOC,GACRhE,KAAK4D,WAAWU,UAAYtE,KAAKiE,SAASK,YAC7CC,GAAAvE,KAAAiE,SAAAjE,KAAA4D,YAAE5D,KAAA4D,WAAAW,EAAA,GAAiBvE,KAAAiE,SAAAM,EAAA,IAEpBvE,KAAKsD,QACL,IAAMkB,EAAc9B,MAAM+B,cAAczE,KAAK4D,WAAY5D,KAAKiE,UACxDS,EAAe1E,KAAK2E,eAAe3E,KAAKiC,YAAYgB,SAASJ,IAAK7C,KAAKkC,UAAUe,SAASJ,KAEhG7C,KAAK0B,UAAUoC,UAAU,aAAc9D,KAAKiC,YAAajC,KAAKkC,YAC1DlC,KAAK+B,WACR/B,KAAK+B,UAAU/B,KAAKiC,YAAajC,KAAKkC,UAAWlC,KAAK4D,WAAY5D,KAAKiE,SAAUO,EAAaE,IAIhGtD,EAAAxF,UAAAgJ,YAAA,WACC,OAAO5E,KAAKoC,YAGLhB,EAAAxF,UAAA+I,eAAR,SAAuBzD,EAAeC,GACrC,IAAI0D,EAAa3D,EACb4D,EAAW3D,EACXD,EAAQC,IACX0D,EAAa1D,EACb2D,EAAW5D,GAMZ,IAJA,IAAMuB,EAASC,MAAMrB,OAAOsB,WACtBoC,EAAajC,KAAKC,KAAK8B,EAAapC,GAAU,EAC9CuC,EAAWlC,KAAKC,KAAK+B,EAAWrC,GAAU,EAC1C7F,KACG5C,EAAI+K,EAAY/K,GAAKgL,EAAUhL,IAAK,CAC/B0I,MAAMuC,eAAejL,IAEjC4C,EAAOW,KAAKmF,MAAMuC,eAAejL,IAGnC,OAAO4C,GAETwE,EAjKA,GAAa5H,EAAA4H,oGC7Bb,IAAA8D,EAAA,WAKC,SAAAA,IAJQlF,KAAAmF,YAAsB,EAK7BnF,KAAKoF,WAAa1C,MAAM2C,uBAsG1B,OAnGCH,EAAAtJ,UAAA0J,OAAA,SAAOC,EAAgCC,GAAvC,IAAAlE,EAAAtB,KACOyF,EAAUF,EAAe9B,cAC/BzD,KAAK0F,gBAAkB9L,OAAO+L,iBAAiBF,GAASG,QACxD5F,KAAK6F,uBAAyB,WAC7BJ,EAAQ7C,MAAMkD,SAAWxE,EAAKoE,iBAEF,WAAzB1F,KAAK0F,kBACRD,EAAQ7C,MAAMkD,SAAW,YAE1B,IAAMC,EAAQrD,MAAMsD,UAAUC,WAAW,SACzCF,EAAMG,iBAAiB,YAAa,WAEnC,OADiBC,YAAY,KAK9B,IAAIC,EAA0B,KAa9BpG,KAAKoF,WAAWE,OAAOG,EAAS,YAAa,SAACY,GAC7CD,EAA0B,KACtB1D,MAAM4D,MAAMC,IAAIC,QAAQH,EAAMI,OAAQ,wCAC1CV,EAAMG,iBAAiB,YAAa,WAEnC,OADiBC,WAAY7E,EAAK6D,cAI/BK,IAA4B,IAAlBa,EAAMb,KACpBY,EAA0B9E,EAAKoF,gBAAgBL,EAAOd,OAGvDvF,KAAKoF,WAAWE,OAAO9D,SAASmF,KAAM,UAAW,SAACN,GAEjD,GADAD,EAA0B,OACtBZ,IAA4B,IAAlBa,EAAMb,MACI,IAApBlE,EAAK6D,WAAqB,CAC7B7D,EAAK6D,YAAa,EAClB,IAAMyB,EAActF,EAAKoF,gBAAgBL,EAAOd,GAChDA,EAAelB,QAAQuC,MAGzB5G,KAAKoF,WAAWE,OAAOG,EAAS,YAAa,SAACY,GAC7C,IAAIb,IAA4B,IAAlBa,EAAMb,GAApB,CACA,IAAIoB,EAAc,KAClB,IAAItF,EAAK6D,YAAciB,EAMtB,OALAQ,EAActF,EAAKoF,gBAAgBL,EAAOd,QACvCzC,KAAK+D,IAAIT,EAAwBnD,SAASE,KAAOyD,EAAY3D,SAASE,MAAQ,GArC7EiD,IAIL9E,EAAK6D,YAAa,EAClBI,EAAe7B,SAAS0C,GACxBb,EAAepB,YAAYiC,GAC3Bb,EAAexB,OAAOqC,GACtBA,EAA0B,QAmCF,IAApB9E,EAAK6D,aACRyB,EAActF,EAAKoF,gBAAgBL,EAAOd,GAC1CA,EAAexB,OAAO6C,GACtBrB,EAAevD,cAKlBkD,EAAAtJ,UAAAkL,OAAA,WACC9G,KAAKoF,WAAW2B,YACb/G,KAAK6F,wBACP7F,KAAK6F,yBAGQnD,MAAMsD,UAAUC,WAAW,SACnCe,mBAAmB,cAG1B9B,EAAAtJ,UAAAqL,WAAA,WACCjH,KAAK8G,UAGE5B,EAAAtJ,UAAA8K,gBAAR,SAAwBL,EAAmBd,GAC1C,IAAM5D,EAAW4D,EAAe9B,cAC1ByD,EAAiBvF,EAASwF,wBACxBC,EAAAf,EAAAe,QAASC,EAAAhB,EAAAgB,QAWjB,OATCnD,UACCf,KAAMiE,EACNvE,IAAKwE,GAENpE,UACCE,KAAMiE,EAAUF,EAAe/D,KAAOxB,EAAS2F,WAC/CzE,IAAKwE,EAAUH,EAAerE,IAAMlB,EAAS4F,aAKjDrC,EA5GA,GAAa1L,EAAA0L,2UCHb,IAAAsC,EAAA1N,EAAA,KACA2N,EAAA3N,EAAA,KAEK4I,MAAMgF,MACVhF,MAAMgF,QAGP,IAAMC,GACL7F,UAAW,wBACXS,0BAA0B,EAC1B5F,cAAUoC,EACVsD,WAAW,GAGNuF,EAAgB,IAAIJ,EAAAtC,cAE1BxC,MAAMgF,IAAIG,UAAYD,EAEtBlF,MAAM7B,YAAY,eAAgB,WACjC,IAAMQ,EAAMyG,GAA4BnG,SAAUe,MAAMqF,YAAeJ,GACvE,GAAIjF,MAAMrB,OAAO2G,WAAY,CAC5B,IAAMH,EAAYnF,MAAMrB,OAAO2G,WAC/B3G,EAAOW,OAAS6F,EAAU7F,QAAU2F,EAAc3F,OAClDX,EAAOS,UAAY+F,EAAU/F,WAAa6F,EAAc7F,UACxDT,EAAO1E,SAAWkL,EAAUlL,UAAYgL,EAAchL,SACtD0E,EAAOM,SAAWkG,EAAUlG,UAAYe,MAAMqF,WAC9C1G,EAAOkB,8BAAkExD,IAAvC8I,EAAUtF,yBAC3CoF,EAAcpF,yBAA2BsF,EAAUtF,yBAEpDlB,EAAOgB,eAAoCtD,IAAxB8I,EAAUxF,UAA0BsF,EAActF,UAAYwF,EAAUxF,UAC3F,IAAMkD,EAAiB,IAAIkC,EAAArG,eAAeC,GAC1CqB,MAAMgF,IAAIG,UAAUvC,OAAOC,EAAgBsC,EAAUrC,WAIvD9C,MAAM7B,YAAY,YAAa,WAC9B+G,EAAcX,gCCpCf,IAAAgB,EAAA,WACAjI,KAAAkI,cACAlI,KAAAmI,cAAA,GAGAF,EAAArM,WACAwM,aAAA,WACApI,KAAAmI,cAAA,GAEAE,WAAA,WACArI,KAAAmI,cAAA,IAIA,IAAAG,EAAA,SAAAzJ,GACA,IAAA0J,KACAC,EAAA,WAEA,IADA,IAAAC,GAAA,EACAzO,EAAA,EAAiBA,EAAAuO,EAAAzL,OAAsB9C,IACvC,GAAAuO,EAAAvO,GAAA,CACA,IAAA0O,EAAAH,EAAAvO,GAAAyE,MAAAI,EAAAH,WACA+J,KAAAC,EAGA,OAAAD,GAUA,OARAD,EAAAG,SAAA,SAAAC,GACA,4BACAL,EAAAhL,KAAAqL,GAAA,GAGAJ,EAAAK,YAAA,SAAAC,GACAP,EAAAO,GAAA,MAEAN,GAqDA/O,EAAAD,QAlDA,SAAAqF,GAEA,IAAAkK,EAAA,IAAAd,EACApJ,EAAAgC,YAAA,SAAAtG,EAAAyO,EAAAC,GAKA,OAJA1O,EAAA,MAAAA,EAAA2O,cACAH,EAAAxO,KACAwO,EAAAxO,GAAA+N,EAAAW,GAAAjJ,OAEAzF,EAAA,IAAAwO,EAAAxO,GAAAoO,SAAAK,IAEAnK,EAAAsK,UAAA,SAAAxM,EAAAsM,GACAjJ,KAAAa,YAAA,aAAAlE,EAAAsM,IAGApK,EAAAiF,UAAA,SAAAvJ,EAAA6O,EAAAH,GACA,GAAAF,EAAAZ,aAAA,SAEA,IAAAkB,EAAA,MAAA9O,EAAA2O,cAMA,OAJAH,EAAA,eACAA,EAAA,cAAAtK,MAAAwK,GAAAjJ,MAAAzF,GAAA+O,OAAAF,KAGAL,EAAAM,IACAN,EAAAM,GAAA5K,MAAAwK,GAAAjJ,KAAAoJ,IAGAvK,EAAA0K,WAAA,SAAAhP,GACA,QAAAwO,EAAA,MAAAxO,EAAA2O,gBAEArK,EAAAiC,YAAA,SAAAgI,GACA,GAAAA,EAAA,CACA,IAAAU,EAAAV,EAAAW,MAAA,KACAC,EAAAF,EAAA,GACAG,EAAAH,EAAA,GAEAT,EAAAW,IACAX,EAAAW,GAAAb,YAAAc,KAIA9K,EAAA+K,gBAAA,WACA,QAAArP,KAAAwO,EACA,IAAAxO,EAAAsP,QAAA,eACAd,EAAAxO", "file": "ext/dhtmlxgantt_click_drag.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"ext/dhtmlxgantt_click_drag\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ext/dhtmlxgantt_click_drag\"] = factory();\n\telse\n\t\troot[\"ext/dhtmlxgantt_click_drag\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/codebase/\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 217);\n", "var units = {\n\t\"second\": 1,\n\t\"minute\": 60,\n\t\"hour\": 60 * 60,\n\t\"day\": 60 * 60 * 24,\n\t\"week\": 60 * 60 * 24 * 7,\n\t\"month\": 60 * 60 * 24 * 30,\n\t\"quarter\": 60 * 60 * 24 * 30 * 3,\n\t\"year\": 60 * 60 * 24 * 365\n};\nfunction getSecondsInUnit(unit){\n\treturn units[unit] || units.hour;\n}\n\nfunction forEach(arr, callback) {\n\tif (arr.forEach) {\n\t\tarr.forEach(callback);\n\t} else {\n\t\tvar workArray = arr.slice();\n\t\tfor (var i = 0; i < workArray.length; i++) {\n\t\t\tcallback(workArray[i], i);\n\t\t}\n\t}\n}\n\nfunction arrayMap(arr, callback) {\n\tif (arr.map) {\n\t\treturn arr.map(callback);\n\t} else {\n\t\tvar workArray = arr.slice();\n\t\tvar resArray = [];\n\n\t\tfor (var i = 0; i < workArray.length; i++) {\n\t\t\tresArray.push(callback(workArray[i], i));\n\t\t}\n\t\treturn resArray;\n\t}\n}\n\n\nfunction arrayFind(arr, callback) {\n\tif (arr.find) {\n\t\treturn arr.find(callback);\n\t} else {\n\t\tfor (var i = 0; i < arr.length; i++) {\n\t\t\tif (callback(arr[i], i)) {\n\t\t\t\treturn arr[i];\n\t\t\t}\n\t\t}\n\t}\n}\n\n// iframe-safe array type check instead of using instanceof\nfunction isArray(obj){\n\tif(Array.isArray){\n\t\treturn Array.isArray(obj);\n\t}else{\n\t\t// close enough\n\t\treturn (obj && obj.length !== undefined && obj.pop && obj.push);\n\t}\n}\n\n// non-primitive string object, e.g. new String(\"abc\")\nfunction isStringObject(obj){\n\treturn obj && typeof obj === \"object\"\n\t\t&& Function.prototype.toString.call(obj.constructor) === \"function String() { [native code] }\";\n}\n\n// non-primitive number object, e.g. new Number(5)\nfunction isNumberObject(obj){\n\treturn obj && typeof obj === \"object\"\n\t\t&& Function.prototype.toString.call(obj.constructor) === \"function Number() { [native code] }\";\n}\n\n// non-primitive number object, e.g. new Boolean(true)\nfunction isBooleanObject(obj){\n\treturn obj && typeof obj === \"object\"\n\t\t&& Function.prototype.toString.call(obj.constructor) === \"function Boolean() { [native code] }\";\n}\n\nfunction isDate(obj) {\n\tif (obj && typeof obj === \"object\") {\n\t\treturn !!(obj.getFullYear && obj.getMonth && obj.getDate);\n\t} else {\n\t\treturn false;\n\t}\n}\n\nfunction arrayFilter(arr, callback) {\n\tvar result = [];\n\n\tif (arr.filter) {\n\t\treturn arr.filter(callback);\n\t} else {\n\t\tfor (var i = 0; i < arr.length; i++) {\n\t\t\tif (callback(arr[i], i)) {\n\t\t\t\tresult[result.length] = arr[i];\n\t\t\t}\n\t\t}\n\t\treturn result;\n\t}\n}\n\nfunction hashToArray(hash) {\n\tvar result = [];\n\n\tfor (var key in hash) {\n\t\tif (hash.hasOwnProperty(key)) {\n\t\t\tresult.push(hash[key]);\n\t\t}\n\t}\n\n\treturn result;\n}\n\nfunction arraySome(arr, callback) {\n\tif (arr.length === 0) return false;\n\n\tfor (var i = 0; i < arr.length; i++) {\n\t\tif (callback(arr[i], i, arr)) {\n\t\t\treturn true;\n\t\t}\n\t}\n\treturn false;\n}\n\nfunction arrayDifference(arr, callback) {\n\treturn arrayFilter(arr, function(item, i) {\n\t\treturn !callback(item, i);\n\t});\n}\n\nfunction throttle (callback, timeout) {\n\tvar wait = false;\n\n\treturn function () {\n\t\tif (!wait) {\n\t\t\tcallback.apply(null, arguments);\n\t\t\twait = true;\n\t\t\tsetTimeout(function () {\n\t\t\t\twait = false;\n\t\t\t}, timeout);\n\t\t}\n\t};\n}\n\nfunction delay (callback, timeout){\n\tvar timer;\n\n\tvar result = function() {\n\t\tresult.$cancelTimeout();\n\t\tcallback.$pending = true;\n\t\tvar args = Array.prototype.slice.call(arguments);\n\t\ttimer = setTimeout(function(){\n\t\t\tcallback.apply(this, args);\n\t\t\tresult.$pending = false;\n\t\t}, timeout);\n\t};\n\t\n\tresult.$pending = false;\n\tresult.$cancelTimeout = function(){\n\t\tclearTimeout(timer);\n\t\tcallback.$pending = false;\n\t};\n\tresult.$execute = function(){\n\t\tcallback();\n\t\tcallback.$cancelTimeout();\n\t};\n\n\treturn result;\n}\n\nfunction sortArrayOfHash(arr, field, desc) {\n\tvar compare = function(a, b) {\n\t\treturn a < b;\n\t};\n\n\tarr.sort(function(a, b) {\n\t\tif (a[field] === b[field]) return 0;\n\n\t\treturn desc ? compare(a[field], b[field]) : compare(b[field], a[field]);\n\t});\n}\n\nfunction objectKeys(obj) {\n\tif (Object.keys) {\n\t\treturn Object.keys(obj);\n\t}\n\tvar result = [];\n\tvar key;\n\tfor (key in obj) {\n\t\tif (Object.prototype.hasOwnProperty.call(obj, key)) {\n\t\t\tresult.push(key);\n\t\t}\n\t}\n\treturn result;\n}\n\nfunction requestAnimationFrame(callback) {\n\tvar w = window;\n\tvar foundRequestAnimationFrame = w.requestAnimationFrame\n\t\t|| w.webkitRequestAnimationFrame\n\t\t|| w.msRequestAnimationFrame\n\t\t|| w.mozRequestAnimationFrame\n\t\t|| w.oRequestAnimationFrame\n\t\t|| function(cb) { setTimeout(cb, 1000/60); };\n\treturn foundRequestAnimationFrame(callback);\n}\n\nfunction isEventable(obj) {\n\treturn obj.attachEvent && obj.detachEvent;\n}\n\nmodule.exports = {\n\tgetSecondsInUnit: getSecondsInUnit,\n\tforEach: forEach,\n\tarrayMap: arrayMap,\n\tarrayFind: arrayFind,\n\tarrayFilter: arrayFilter,\n\tarrayDifference: arrayDifference,\n\tarraySome: arraySome,\n\thashToArray: hashToArray,\n\tsortArrayOfHash: sortArrayOfHash,\n\tthrottle: throttle,\n\tisArray: isArray,\n\tisDate: isDate,\n\tisStringObject: isStringObject,\n\tisNumberObject: isNumberObject,\n\tisBooleanObject: isBooleanObject,\n\tdelay: delay,\n\tobjectKeys: objectKeys,\n\trequestAnimationFrame: requestAnimationFrame,\n\tisEventable: isEventable\n};", "import * as eventable from \"../../utils/eventable\";\nimport { isEventable, requestAnimationFrame } from \"../../utils/helpers\";\ndeclare const gantt;\n\nexport interface ISelectedRegionConfig {\n\tclassName?: string;\n\trender?: (startPoint: IPoint, endPoint: IPoint) => HTMLElement;\n\tviewPort?: HTMLElement;\n\tuseRequestAnimationFrame: boolean;\n\tcallback?: (startPoint: IPoint, endPoint: IPoint, startDate: Date, endDate: Date, tasksByDate: any[], tasksByIndex: any[]) => void;\n\tsingleRow: boolean;\n}\n\ninterface ICoordinates {\n\tleft: number;\n\ttop: number;\n}\n\nexport interface IPoint {\n\tabsolute: ICoordinates;\n\trelative: ICoordinates;\n}\n\n\nfunction _countSize(start: number, end: number) {\n\tconst result = start - end;\n\tif (result >= 0) {\n\t\treturn result;\n\t}\n\treturn -result;\n}\n\nexport class SelectedRegion {\n\tprivate _viewPort: HTMLElement & eventable;\n\tprivate _el: HTMLElement = document.createElement(\"div\");\n\tprivate _callback: (startPoint: IPoint, endPoint: IPoint, startDate: Date, endDate: Date, tasksByDate: any[], tasksByIndex: any[]) => void;\n\tprivate _startPoint: IPoint;\n\tprivate _endPoint: IPoint;\n\tprivate _positionPoint: IPoint;\n\tprivate _useRequestAnimationFrame: boolean;\n\tprivate _startDate: Date;\n\tprivate _endDate: Date;\n\tprivate _singleRow: boolean;\n\tconstructor(config: ISelectedRegionConfig) {\n\t\tthis._viewPort = config.viewPort;\n\t\tthis._el.classList.add(config.className);\n\t\tif (typeof config.callback === \"function\") {\n\t\t\tthis._callback = config.callback;\n\t\t}\n\t\tif (typeof config.render === \"function\") {\n\t\t\tthis.render = () => {\n\t\t\t\tthis._el = config.render(this._startPoint, this._endPoint);\n\t\t\t\tif (config.className !== \"\") {\n\t\t\t\t\tthis._el.classList.add(config.className);\n\t\t\t\t}\n\t\t\t\tthis.draw();\n\t\t\t};\n\t\t}\n\t\tif (!isEventable(this._viewPort)) {\n\t\t\teventable(this._viewPort);\n\t\t}\n\t\tthis._singleRow = config.singleRow;\n\t\tthis._useRequestAnimationFrame = config.useRequestAnimationFrame;\n\t}\n\n\tsetStyles() {\n\t\tif (this._singleRow) {\n\t\t\tconst height = gantt.config.row_height;\n\t\t\tthis._el.style.height = height + \"px\";\n\t\t\tthis._el.style.top = (Math.ceil(this._positionPoint.relative.top / height) - 1) * height + \"px\";\n\t\t} else {\n\t\t\tthis._el.style.height = _countSize(this._endPoint.relative.top, this._startPoint.relative.top) + \"px\";\n\t\t\tthis._el.style.top = this._positionPoint.relative.top + \"px\";\n\t\t}\n\t\tthis._el.style.width = _countSize(this._endPoint.relative.left, this._startPoint.relative.left) + \"px\";\n\t\tthis._el.style.left = this._positionPoint.relative.left + \"px\";\n\t}\n\n\trender() {\n\t\tthis.setStyles();\n\t\tthis.draw();\n\t}\n\n\tdraw() {\n\t\tif (this._useRequestAnimationFrame) {\n\t\t\treturn requestAnimationFrame(() => {\n\t\t\t\tthis._viewPort.appendChild(this.getElement());\n\t\t\t});\n\t\t} else {\n\t\t\tthis._viewPort.appendChild(this.getElement());\n\t\t}\n\t}\n\n\tclear() {\n\t\tif (this._useRequestAnimationFrame) {\n\t\t\treturn requestAnimationFrame(() => {\n\t\t\t\tif (!this._el.parentNode) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthis._viewPort.removeChild(this._el);\n\t\t\t});\n\t\t} else {\n\t\t\tif (!this._el.parentNode) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis._viewPort.removeChild(this._el);\n\t\t}\n\t}\n\n\tgetElement() {\n\t\treturn this._el;\n\t}\n\n\tgetViewPort() {\n\t\treturn this._viewPort;\n\t}\n\n\tsetStart(startPoint: IPoint) {\n\t\tthis._startPoint = startPoint;\n\t\tthis._startDate = gantt.dateFromPos(this._startPoint.relative.left);\n\t\tthis._viewPort.callEvent(\"onBeforeDrag\", [this._startPoint]);\n\t}\n\n\tsetEnd(endPoint: IPoint) {\n\t\tthis._endPoint = endPoint;\n\t\tif (this._singleRow) {\n\t\t\tconst height = gantt.config.row_height;\n\t\t\tthis._endPoint.relative.top = (Math.ceil(this._startPoint.relative.top / height)) * height;\n\t\t}\n\t\tthis._endDate = gantt.dateFromPos(this._endPoint.relative.left);\n\t\tif (this._startPoint.relative.left > this._endPoint.relative.left) {\n\t\t\tthis._positionPoint = {\n\t\t\t\trelative: { left: this._endPoint.relative.left, top: this._positionPoint.relative.top },\n\t\t\t\tabsolute: { left: this._endPoint.absolute.left, top: this._positionPoint.absolute.top }\n\t\t\t};\n\t\t}\n\t\tif (this._startPoint.relative.top > this._endPoint.relative.top) {\n\t\t\tthis._positionPoint = {\n\t\t\t\trelative: { left: this._positionPoint.relative.left, top: this._endPoint.relative.top },\n\t\t\t\tabsolute: { left: this._positionPoint.absolute.left, top: this._endPoint.absolute.top }\n\t\t\t};\n\t\t}\n\n\n\t\tthis._viewPort.callEvent(\"onDrag\", [this._startPoint, this._endPoint]);\n\t}\n\n\tsetPosition(positionPoint: IPoint) {\n\t\tthis._positionPoint = positionPoint;\n\t}\n\n\tdragEnd(endPoint: IPoint) {\n\t\tif(endPoint.relative.left < 0){\n\t\t\tendPoint.relative.left = 0;\n\t\t}\n\t\tthis._viewPort.callEvent(\"onBeforeDragEnd\", [this._startPoint, endPoint]);\n\t\tthis.setEnd(endPoint);\n\t\tif (this._startDate.valueOf() > this._endDate.valueOf()) {\n\t\t\t[ this._startDate, this._endDate ] = [ this._endDate, this._startDate ];\n\t\t}\n\t\tthis.clear();\n\t\tconst tasksByTime = gantt.getTaskByTime(this._startDate, this._endDate);\n\t\tconst tasksByIndex = this._getTasksByTop(this._startPoint.relative.top, this._endPoint.relative.top);\n\n\t\tthis._viewPort.callEvent(\"onDragEnd\", [this._startPoint, this._endPoint]);\n\t\tif (this._callback) {\n\t\t\tthis._callback(this._startPoint, this._endPoint, this._startDate, this._endDate, tasksByTime, tasksByIndex);\n\t\t}\n\t}\n\n\tgetInBounds() {\n\t\treturn this._singleRow;\n\t}\n\n\tprivate _getTasksByTop(start: number, end:number) {\n\t\tlet startValue = start;\n\t\tlet endValue = end;\n\t\tif (start > end) {\n\t\t\tstartValue = end;\n\t\t\tendValue = start;\n\t\t}\n\t\tconst height = gantt.config.row_height;\n\t\tconst startIndex = Math.ceil(startValue / height) - 1;\n\t\tconst endIndex = Math.ceil(endValue / height) - 1;\n\t\tconst result = [];\n\t\tfor (let i = startIndex; i <= endIndex; i++) {\n\t\t\tconst task = gantt.getTaskByIndex(i);\n\t\t\tif (task) {\n\t\t\t\tresult.push(gantt.getTaskByIndex(i));\n\t\t\t}\n\t\t}\n\t\treturn result;\n\t}\n}", "import { SelectedRegion } from \"./selectedRegion\";\n\ndeclare const gantt;\nexport class EventsManager {\n\tprivate _mouseDown: boolean = false;\n\tprivate _domEvents: any;\n\tprivate _originPosition: string;\n\tprivate _restoreOriginPosition: () => void;\n\tconstructor() {\n\t\tthis._domEvents = gantt._createDomEventScope();\n\t}\n\n\tattach(selectedRegion: SelectedRegion, useKey?: \"shiftKey\" | \"ctrlKey\" | \"altKey\"): void {\n\t\tconst _target = selectedRegion.getViewPort();\n\t\tthis._originPosition = window.getComputedStyle(_target).display;\n\t\tthis._restoreOriginPosition = () => {\n\t\t\t_target.style.position = this._originPosition;\n\t\t};\n\t\tif (this._originPosition === \"static\") {\n\t\t\t_target.style.position = \"relative\";\n\t\t}\n\t\tconst state = gantt.$services.getService(\"state\");\n\t\tstate.registerProvider(\"clickDrag\", () => {\n\t\t\tconst result = { autoscroll: false };\n\t\t\treturn result;\n\t\t});\n\n\n\t\tlet scheduledDndCoordinates = null;\n\t\tconst startDragAndDrop = () => {\n\t\t\tif (!scheduledDndCoordinates) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis._mouseDown = true;\n\t\t\tselectedRegion.setStart(scheduledDndCoordinates);\n\t\t\tselectedRegion.setPosition(scheduledDndCoordinates);\n\t\t\tselectedRegion.setEnd(scheduledDndCoordinates);\n\t\t\tscheduledDndCoordinates = null;\n\t\t};\n\n\t\tthis._domEvents.attach(_target, \"mousedown\", (event) => {\n\t\t\tscheduledDndCoordinates = null;\n\t\t\tif (gantt.utils.dom.closest(event.target, \".gantt_task_line, .gantt_task_link\")) { return; }\n\t\t\tstate.registerProvider(\"clickDrag\", () => {\n\t\t\t\tconst result = { autoscroll: this._mouseDown };\n\t\t\t\treturn result;\n\t\t\t});\n\n\t\t\tif (useKey && event[useKey] !== true) { return; }\n\t\t\tscheduledDndCoordinates = this._getCoordinates(event, selectedRegion);\n\t\t});\n\n\t\tthis._domEvents.attach(document.body, \"mouseup\", (event) => {\n\t\t\tscheduledDndCoordinates = null;\n\t\t\tif (useKey && event[useKey] !== true) { return; }\n\t\t\tif (this._mouseDown === true) {\n\t\t\t\tthis._mouseDown = false;\n\t\t\t\tconst coordinates = this._getCoordinates(event, selectedRegion);\n\t\t\t\tselectedRegion.dragEnd(coordinates);\n\t\t\t}\n\t\t});\n\t\tthis._domEvents.attach(_target, \"mousemove\", (event) => {\n\t\t\tif (useKey && event[useKey] !== true) { return; }\n\t\t\tlet coordinates = null;\n\t\t\tif(!this._mouseDown && scheduledDndCoordinates){\n\t\t\t\tcoordinates = this._getCoordinates(event, selectedRegion);\n\t\t\t\tif(Math.abs(scheduledDndCoordinates.relative.left - coordinates.relative.left) > 5){\n\t\t\t\t\t// add small threshold not to start dnd on simple click\n\t\t\t\t\tstartDragAndDrop();\n\t\t\t\t}\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (this._mouseDown === true) {\n\t\t\t\tcoordinates = this._getCoordinates(event, selectedRegion);\n\t\t\t\tselectedRegion.setEnd(coordinates);\n\t\t\t\tselectedRegion.render();\n\t\t\t}\n\t\t});\n\t}\n\n\tdetach(): void {\n\t\tthis._domEvents.detachAll();\n\t\tif(this._restoreOriginPosition){\n\t\t\tthis._restoreOriginPosition();\n\t\t}\n\n\t\tconst state = gantt.$services.getService(\"state\");\n\t\tstate.unregisterProvider(\"clickDrag\");\n\t}\n\n\tdestructor(): void {\n\t\tthis.detach();\n\t}\n\n\tprivate _getCoordinates(event: MouseEvent, selectedRegion: SelectedRegion) {\n\t\tconst viewPort = selectedRegion.getViewPort();\n\t\tconst viewPortBounds = viewPort.getBoundingClientRect();\n\t\tconst { clientX, clientY } = event;\n\t\tconst result = {\n\t\t\tabsolute: {\n\t\t\t\tleft: clientX,\n\t\t\t\ttop: clientY,\n\t\t\t},\n\t\t\trelative: {\n\t\t\t\tleft: clientX - viewPortBounds.left + viewPort.scrollLeft,\n\t\t\t\ttop: clientY - viewPortBounds.top + viewPort.scrollTop\n\t\t\t}\n\t\t};\n\t\treturn result;\n\t}\n}", "import { EventsManager } from \"./eventsManager\";\nimport { ISelectedRegionConfig, SelectedRegion } from \"./selectedRegion\";\ndeclare const gantt;\nif (!gantt.ext) {\n\tgantt.ext = {};\n}\n\nconst defaultConfig: ISelectedRegionConfig = {\n\tclassName: \"gantt_click_drag_rect\",\n\tuseRequestAnimationFrame: true,\n\tcallback: undefined,\n\tsingleRow: false\n};\n\nconst eventsManager = new EventsManager();\n\ngantt.ext.clickDrag = eventsManager;\n\ngantt.attachEvent(\"onGanttReady\", () => {\n\tconst config: ISelectedRegionConfig = { viewPort: gantt.$task_data, ...defaultConfig };\n\tif (gantt.config.click_drag) {\n\t\tconst clickDrag = gantt.config.click_drag;\n\t\tconfig.render = clickDrag.render || defaultConfig.render;\n\t\tconfig.className = clickDrag.className || defaultConfig.className;\n\t\tconfig.callback = clickDrag.callback || defaultConfig.callback;\n\t\tconfig.viewPort = clickDrag.viewPort || gantt.$task_data;\n\t\tconfig.useRequestAnimationFrame = clickDrag.useRequestAnimationFrame === undefined ?\n\t\t\tdefaultConfig.useRequestAnimationFrame : clickDrag.useRequestAnimationFrame;\n\n\t\tconfig.singleRow = clickDrag.singleRow === undefined ? defaultConfig.singleRow : clickDrag.singleRow;\n\t\tconst selectedRegion = new SelectedRegion(config);\n\t\tgantt.ext.clickDrag.attach(selectedRegion, clickDrag.useKey);\n\t}\n});\n\ngantt.attachEvent(\"onDestroy\", () => {\n\teventsManager.destructor();\n});\n", "var EventHost = function(){\n\tthis._connected = [];\n\tthis._silent_mode = false;\n};\n\nEventHost.prototype = {\n\t_silentStart: function() {\n\t\tthis._silent_mode = true;\n\t},\n\t_silentEnd: function() {\n\t\tthis._silent_mode = false;\n\t}\n};\n\nvar\tcreateEventStorage = function(obj) {\n\tvar dhx_catch = [];\n\tvar z = function(){\n\t\tvar res = true;\n\t\tfor (var i = 0; i < dhx_catch.length; i++){\n\t\t\tif (dhx_catch[i]){\n\t\t\t\tvar zr = dhx_catch[i].apply(obj, arguments);\n\t\t\t\tres=res&&zr;\n\t\t\t}\n\t\t}\n\t\treturn res;\n\t};\n\tz.addEvent=function(ev){\n\t\tif (typeof (ev) == \"function\")\n\t\t\treturn dhx_catch.push(ev)-1;\n\t\treturn false;\n\t};\n\tz.removeEvent=function(id){\n\t\tdhx_catch[id]=null;\n\t};\n\treturn z;\n};\n\nfunction makeEventable(obj){\n\n\tvar eventHost = new EventHost();\n\tobj.attachEvent=function(name, catcher, callObj){\n\t\tname='ev_'+name.toLowerCase();\n\t\tif (!eventHost[name])\n\t\t\teventHost[name] = createEventStorage(callObj||this);\n\n\t\treturn(name+':'+eventHost[name].addEvent(catcher)); //return ID (event name & event ID)\n\t};\n\tobj.attachAll = function(callback, callObj){\n\t\tthis.attachEvent('listen_all', callback, callObj);\n\t};\n\n\tobj.callEvent=function(name, arg0, callObj){\n\t\tif (eventHost._silent_mode) return true;\n\n\t\tvar handlerName = 'ev_'+name.toLowerCase();\n\n\t\tif (eventHost['ev_listen_all']){\n\t\t\teventHost['ev_listen_all'].apply(callObj || this, [name].concat(arg0));\n\t\t}\n\n\t\tif (eventHost[handlerName])\n\t\t\treturn eventHost[handlerName].apply(callObj || this, arg0);\n\t\treturn true;\n\t};\n\tobj.checkEvent=function(name){\n\t\treturn (!!eventHost['ev_'+name.toLowerCase()]);\n\t};\n\tobj.detachEvent=function(id){\n\t\tif (id){\n\t\t\tvar list = id.split(':');//get EventName and ID\n\t\t\tvar eventName = list[0];\n\t\t\tvar eventId = list[1];\n\n\t\t\tif(eventHost[eventName]){\n\t\t\t\teventHost[eventName].removeEvent(eventId); //remove event\n\t\t\t}\n\t\t}\n\t};\n\tobj.detachAllEvents = function(){\n\t\tfor (var name in eventHost){\n\t\t\tif (name.indexOf(\"ev_\") === 0)\n\t\t\t\tdelete eventHost[name];\n\t\t}\n\t};\n\n}\n\nmodule.exports = makeEventable;"], "sourceRoot": ""}