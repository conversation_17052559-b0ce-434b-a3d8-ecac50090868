<template>
  <a-modal :title="title" :width="900" :visible="visible" @cancel="handleCancel" :confirmLoading="confirmLoading">
    <template slot="footer">
        <a-button key="back" @click="handleCancel">
        关闭
        </a-button>
    </template>
    <s-table ref="table" :columns="columns" :data="loadData" :alert="false" :rowKey="(record) => record.id">
    </s-table>
  </a-modal>
</template>

<script>
  import {
    getTechHistory
  } from "@/api/modular/system/docManage"
  import {
    STable
  } from '@/components'
  export default {
    components: {
      STable
    },
    data() {
      return {
        title: '历史记录',
        queryParam: {},
        visible: false,
        confirmLoading: false,
        loadData: parameter => {
          return getTechHistory(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        columns: [{
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            width: 50,
            customRender: (text, record, index) => `${index+1}`,
          }, {
            title: '文件',
            dataIndex: 'techName',
            key: 'techName',
          },
          {
            title: '文件版本',
            dataIndex: 'techVersion',
            key: 'techVersion',
          },
          {
            title: '提交人',
            dataIndex: 'summitor',
            key: 'summitor'
          },
          {
            title: '时间',
            dataIndex: 'createTime'
          },
          {
            title: '备注',
            dataIndex: 'remark'
          },
        ]
      }
    },
    methods: {
      view(record) {
        this.queryParam.techId = record.docId
        this.visible = true
        setTimeout(() => {
          this.$refs.table.refresh()
        }, 100);
      },
      handleCancel() {
        this.visible = false
      },
    }
  }
</script>

<style>

</style>