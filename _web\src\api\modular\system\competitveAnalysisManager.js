import { axios } from '@/utils/request'

export function competitiveAnalysisPageList(parameter) {
    return axios({
        url: '/competitiveAnalysis/pageList',
        method: 'post',
        data: parameter
    })
}

export function competitiveAnalysisAdd(parameter) {
    return axios({
        url: '/competitiveAnalysis/add',
        method: 'post',
        data: parameter
    })
}

export function competitiveAnalysisRemove(parameter) {
    return axios({
        url: '/competitiveAnalysis/remove',
        method: 'post',
        data: parameter
    })
}

export function competitiveAnalysisUpdate(parameter) {
    return axios({
        url: '/competitiveAnalysis/update',
        method: 'post',
        data: parameter
    })
}

export function competitiveAnalysisEchartsData(parameter) {
    return axios({
        url: '/competitiveAnalysis/echartsData',
        method: 'post',
        data: parameter
    })
}

export function competitiveAnalysisTreeData(parameter) {
    return axios({
        url: '/competitiveAnalysis/treeData',
        method: 'post',
        data: parameter
    })
}


export function competitiveAnalysisFileAdd(parameter) {
    return axios({
        url: '/competitiveAnalysisFile/add',
        method: 'post',
        data: parameter
    })
}

export function competitiveAnalysisFileRemove(parameter) {
    return axios({
        url: '/competitiveAnalysisFile/remove',
        method: 'post',
        data: parameter
    })
}

export function competitiveAnalysisFileUpdate(parameter) {
    return axios({
        url: '/competitiveAnalysisFile/update',
        method: 'post',
        data: parameter
    })
}


export function competitiveAnalysisSamplePageList(parameter) {
    return axios({
        url: '/competitiveAnalysisSample/pageList',
        method: 'post',
        data: parameter
    })
}

export function competitiveAnalysisSampleChartData(parameter) {
    return axios({
        url: '/competitiveAnalysisSample/chartData',
        method: 'post',
        data: parameter
    })
}

export function competitiveAnalysisSampleAdd(parameter) {
    return axios({
        url: '/competitiveAnalysisSample/add',
        method: 'post',
        data: parameter
    })
}

export function competitiveAnalysisSampleRemove(parameter) {
    return axios({
        url: '/competitiveAnalysisSample/remove',
        method: 'post',
        data: parameter
    })
}

export function competitiveAnalysisSampleUpdate(parameter) {
    return axios({
        url: '/competitiveAnalysisSample/update',
        method: 'post',
        data: parameter
    })
}



