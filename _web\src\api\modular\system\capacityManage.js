/**
 * 系统应用
 *
 * <AUTHOR>
 * @date 2020年4月23日12:10:57
 */
 import { axios } from '@/utils/request'


 export function getCapacityById (parameter) {
   return axios({
     url: '/projectIssueCapacity/getById',
     method: 'post',
     data: parameter
   })
 }


 export function saveCapacity (parameter) {
   return axios({
     url: '/projectIssueCapacity/save',
     method: 'post',
     data: parameter
   })
 }



 export function editCapacity (parameter) {
   return axios({
     url: '/projectIssueCapacity/edit',
     method: 'post',
     data: parameter
   })
 }





 export function listCapacity (parameter) {
   return axios({
     url: '/projectIssueCapacity/list',
     method: 'post',
     data: parameter
   })
 }





 export function listByPageCapacity (parameter) {
   return axios({
     url: '/projectIssueCapacity/listByPage',
     method: 'post',
     data: parameter
   })
 }


 export function capacityData (parameter) {
   return axios({
     url: '/projectIssueCapacity/capacityData',
     method: 'post',
     data: parameter
   })
 }

