<template>
	<div class="container">
		<!-- 按钮 start -->
		<!-- <div class="btn" @click="handleShow(0)">
			查询
		</div> -->
		<!-- 按钮 end -->

		<!-- 内容 start -->
		<div class="content-wrap">
			<div
				v-for="(item, index) in menu"
				class="content"
				:style="`background-image: url(${item.images});`"
				:class="{ mt80: index % 2 !== 0 }"
				@click="handleMoudle(item.path, item.code)"
			>
				<div class="title">{{ item.title }}</div>
				<a-tooltip placement="topLeft" :title="item.detail">
					<div class="detail">{{ item.detail }}</div>
				</a-tooltip>
			</div>
		</div>
		<!-- 内容 end -->

		<!-- 底部 start -->
		<!-- <div class="bottom">
			<img v-for="item in 12" class="icon" src="../../../assets/images/mem.png" alt="" />
			<div class="icon add-icon" @click="handleShow(1)">
				<a-icon type="plus" />
			</div>
		</div> -->
		<!-- 底部 end -->

		<!-- 弹窗 start -->
		<home-query v-if="isQuery" @cancel="handleCancel" />
		<home-add v-if="isAdd" @cancel="handleCancel" />
		<!-- 弹窗 end -->
	</div>
</template>

<script>
import HomeQuery from "./model/homeQuery.vue"
import HomeAdd from "./model/homeAdd.vue"

import Vue from "vue"
import { ALL_APPS_MENU } from "@/store/mutation-types"

export default {
	name: "Home",
	data() {
		return {
			isQuery: false,
			isAdd: false,
			menu: [
				{
					title: "产品设计平台",
					detail: "Product Design Platform",
					path: "/batterydesign1",
					code: "batteryDesignManage",
					images: require("../../../assets/images/design_index.png")
				},
				{
					title: "产品管理平台",
					detail: "Product Management Platform",
					path: "/product_chart",
					code: "productManage",
					images: require("../../../assets/images/manage_index.png")
				},
				{
					title: "课题管理平台",
					detail: "Project Management Platform",
					path: "/topic_chart",
					code: "platFormManage",
					images: require("../../../assets/images/topic_index.png")
				},
				{
					title: "产品测试平台",
					detail: "Product Testing Platform",
					path: "/product_test",
					code: "testProgress",
					images: require("../../../assets/images/test_index.png")
				},
				{
					title: "产品质量平台",
					detail: "Product Quality Platform",
					path: "/quality_platform_anode_stirring",
					code: "anode_stirring_process",
					images: require("../../../assets/images/quality_index.png")
				}
			]
		}
	},
	components: {
		HomeQuery,
		HomeAdd
	},
	mounted() {
		document.querySelectorAll(".bottom img").forEach(img => {
			img.addEventListener("click", e => {
				e.currentTarget.classList.add("loading")
			})

			img.addEventListener("mousemove", e => {
				let item = e.target
				let itemRect = item.getBoundingClientRect()
				let offset = Math.abs(e.clientX - itemRect.left) / itemRect.width

				let prev = item.previousElementSibling || null
				let next = item.nextElementSibling || null

				let scale = 0.6

				this.resetScale()

				if (prev) {
					prev.style.setProperty("--scale", 1 + scale * Math.abs(offset - 1))
				}

				item.style.setProperty("--scale", 1 + scale)

				if (next) {
					next.style.setProperty("--scale", 1 + scale * offset)
				}
			})
		})

		document.querySelector(".bottom").addEventListener("mouseleave", e => {
			this.resetScale()
		})
	},
	methods: {
		handleShow(value) {
			value ? (this.isAdd = true) : (this.isQuery = true)
		},
		handleCancel() {
			this.isQuery = false
			this.isAdd = false
		},
		handleMoudle(path, code) {
			let apps = Vue.ls.get(ALL_APPS_MENU)
			let menu = null

			for (let item of apps) {
				if (item.code == code) {
					item.active = true
					console.log(item.menu)
					menu = item.menu.find(
						item => item.component != "PageView" && item.meta.showNav == 1 && item.meta.show == true
					)
				} else {
					item.active = false
				}
			}

			Vue.ls.set(ALL_APPS_MENU, apps)
			this.setShowNav(menu, code)
		},
		setShowNav(menu,code) {
			if (menu) {
				this.$router.push(menu.path)
			} else {
				this.$info({
					title:
						code === "anode_stirring_process"
							? "抱歉，该功能正在开发中"
							: "抱歉，该功能暂未对您开放。\n请企业微信联系动力电池研究院IT服务号以获取帮助。"
				})
			}
		},
		resetScale() {
			document.querySelectorAll(".bottom img").forEach(img => {
				img.style.setProperty("--scale", 1)
			})
		}
	}
}
</script>

<style lang="less" scoped>
.container {
	height: calc(100vh - 2.5rem);
	padding: 0 0 0.7813vw;
	margin-left: -3.125vw;

	display: flex;
	flex-direction: column;
	justify-content: center;
	font-size: 0.9375vw;
	background: linear-gradient(180deg, #ffffff 26%, #2d77e3 65%);
}

// 按钮
.btn {
	position: fixed;
	top: 3.9063vw;
	right: 0;

	width: 5.0781vw;
	height: 2.7344vw;

	cursor: pointer;

	background: linear-gradient(180deg, rgba(17, 156, 237, 0.45) 0%, #195ae7 100%);
	box-shadow: 0rem 0.25rem 0.25rem 0rem rgba(0, 0, 0, 0.25);
	border-radius: 1.9531vw 0rem 0rem 1.9531vw;

	text-align: center;
	line-height: 2.7344vw;
	font-size: 1.5625vw;
	color: rgba(255, 255, 255, 0.8);
}

// 内容
.content-wrap {
	padding: 0 1.5625vw;
	display: flex;
	justify-content: space-around;
}

.content-wrap .content {
	width: 15.625vw;
	height: 25.7813vw;
	padding: 3.9063vw 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	box-sizing: border-box;
	background-size: 100% 100%;
	box-shadow: 0rem 0rem 0.4688vw 0rem rgba(0, 0, 0, 0.25);
	border-radius: 0.3906vw;

	cursor: pointer;
}

.content-wrap .content .title {
	font-family: AlibabaPuHuiTi;
	font-size: 2.0313vw;
	color: #fff;
	margin-bottom: 1.25vw;
}

.content-wrap .content .detail {
	color: #ccc;
	cursor: pointer;
}

.content-wrap .content:hover {
	transform: scale(1.2);
}

// 底部
// .bottom {
// 	width: calc(100vw - 3.125vw);
// 	position: fixed;
// 	left: 1.5625vw;
// 	bottom: .7813vw;

// 	display: flex;

// 	background: rgba(255, 255, 255, 0.5);
// 	border-radius: .7813vw;
// }
.bottom {
	width: calc(100vw - 3.125vw);
	position: fixed;
	left: 1.5625vw;
	bottom: 0.7813vw;
	background: rgba(255, 255, 255, 0.5);
	border-radius: 0.7813vw;

	--scale: 1;

	list-style: none;
	margin: 0;
	padding: 0;
	display: flex;
}

.bottom img {
	font-size: calc(96px * var(--scale));
	cursor: pointer;

	position: relative;
	top: calc((96px * var(--scale) - 96px) / 2 * -1);

	transition: 15ms all ease-out;
}

.bottom img.loading {
	animation: 1s loading ease-in infinite;
}

@keyframes loading {
	0%,
	100% {
		transform: translateY(0rem);
	}
	60% {
		transform: translateY(-3.125vw);
	}
}

.icon {
	width: 3.125vw;
	height: 3.125vw;
	border-radius: 0.3125rem;
	margin: 0.8125rem 0 0.8125rem 1.5625vw;
	cursor: pointer;
}
.add-icon {
	text-align: center;
	line-height: 3.125vw;
	font-size: 1.4375rem;
	color: #999;
	background: rgba(204, 204, 204, 0.6);
}

.mt80 {
	margin-top: 6.25vw;
}
</style>
