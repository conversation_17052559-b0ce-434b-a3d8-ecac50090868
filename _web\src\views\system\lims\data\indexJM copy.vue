<template>
  <div style="background-color: #FFFFFF;padding: 10px;height: 100%;display: flow-root;">

    <a-spin tip="EXCEL表头读取中" :spinning="fileList.length > 0 && sheets.length == 0">
      <div class="float">
        <div><span class="numTitle">①</span><span class="title">Excel文件上传</span></div>


        <a-upload-dragger
          name="file"
          :fileList="fileList"
          :multiple="true"
          :action="postUrl"
          @change="fileChange"
          :beforeUpload="beforeUpload"
          :showUploadList="true"
          :data="{first:fileList.length == 0}"
        >
          <p class="ant-upload-drag-icon">
            <a-icon type="inbox" />
          </p>
          <p class="ant-upload-text">
            点击上传文件
          </p>

        </a-upload-dragger>

      </div>
      <div class="float1">
        <div><span class="numTitle">②</span><span class="title">数据表选择</span></div>
        <a-select style="width: 120px;margin-top: 20px" v-model="sheetName" @change="changeDataType">
          <a-select-option v-for="sheet in sheets" :key="sheet">
            {{sheet}}
          </a-select-option>

        </a-select>
      </div>
      <div class="floatTable">
        <div>
          <span class="numTitle">③</span>
          <span class="title">数据选择</span>
          <a-table :columns="filterColumns" :data-source="filterData" bordered style="margin-top: 20px;"
                   bordered
                   :scroll="{x: false}"
                   :pagination="false"
          >
            <template
              slot="action"
              slot-scope="text, record, index, columns"
            >
              <a @click="() => filterData.splice(index,1)" style="text-align: center">删除</a>

            </template>

            <template slot="footer" >
              <a-button @click="addFilterData" style="width: 100%;"><a-icon type="plus"></a-icon></a-button>
            </template>

            <template
              slot="key"
              slot-scope="text, record, index, columns"
            >
              <a-select style="width: 100%" v-model="record[columns.dataIndex]" :allow-clear="true" @change="(value,option) => handleDataSelectChange(value,option,columns.dataIndex)"
                        :options="options"
              />
            </template>

            <template
              slot="value"
              slot-scope="text, record, index, columns"
            >
              <a-input  @paste="copyFromExcel($event,columns.dataIndex,index)" style="width: 100%;text-align: center" v-model="record[columns.dataIndex]"
              />
            </template>

          </a-table>
        </div>
      </div>
      <div class="float2">
        <div class="title"><span class="numTitle">④</span><span class="title">导出项选择</span></div>

        <div :style="{ borderBottom: '1px solid #E9E9E9' }">
          <a-checkbox :indeterminate="indeterminate" :checked="checkAll" @change="onCheckAllChange" style="text-align: center" id="checkAll">
            {{checkAll?'取消':'全选'}}
          </a-checkbox>
        </div>
        <br/>
        <a-checkbox-group @change="onChange"
                          v-model="checked"
                          :options="options"
        />

      </div>
      <div style="position: absolute; left: 45%;top: 70%;">
        <a-button type="primary" @click="exportData">导出</a-button>
      </div>
    </a-spin>


    <a-modal title="导出" :width="800" :height="600"
             :bodyStyle="{padding:0}"
             :visible="visible1" :confirmLoading="confirmLoading" @ok="handleSubmit" style="padding: 0"
             @cancel="handleCancel1">

            <a-form :form="form">

              <a-row :gutter="24">
                <a-col :md="18" :sm="24">
                  <a-form-item label="任务名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                    <a-input placeholder="请输入任务名称"
                             v-decorator="['taskName', {rules: [{required: true, message: '请输入任务名称！'}]}]"/>
                  </a-form-item>
                </a-col>
              </a-row>

<!--


              <a-row :gutter="24">
                <a-col :md="18" :sm="24">

                  <a-form-item label="Excel格式" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">

                    <a-select v-decorator="['excelType', {rules: [{required: true, message: '请选择Excel格式！'}]}]" default-value="one"
                              style="width: 100%"  placeholder="请选择Excel格式">
                      <a-select-option value="one">
                        保存于同一个Sheet中
                      </a-select-option>
                      <a-select-option value="more">
                        保存于不同的Sheet中
                      </a-select-option>

                    </a-select>
                  </a-form-item>
                </a-col>


              </a-row>
-->


            </a-form>

    </a-modal>
  </div>



</template>
<script>
  import {
    addJMTask
  } from '@/api/modular/system/exportTaskManager'

  import {mapGetters} from 'vuex';


  export default {


    data() {
      return {
        param:null,
        headerSpinning:false,
        postUrl: 'http://**********:82/testDataExportTask/jmFileUpload',
        fileList:[],
        sheets:[],
        options:[],
        headerData:[],
        indeterminate: false,
        checkAll: false,
        checked: [],
        filterData: [],
        sheetName: null,
        visible: false,
        confirmLoading: false,
        visible1: false,
        height: 200,
        labelCol: {

          sm: {
            span: 11
          }
        },
        wrapperCol: {

          sm: {
            span: 13
          }
        },
        show: false,
        // 表头
        filterColumns: [
          {
            title: '操作',
            align: 'center',
            width: 50,
            scopedSlots: {customRender: 'action'},
          },
          {
            title: '参数1',
            dataIndex: 'index1',
            align: 'center',
            width: 90,
            scopedSlots: {customRender: 'key'},
          }, {
            title: '值',
            width: 90,
            align: 'center',
            dataIndex: 'value1',
            scopedSlots: {customRender: 'value'},
          },
          {
            title: '参数2',
            dataIndex: 'index2',
            align: 'center',
            width: 90,
            scopedSlots: {customRender: 'key'},
          }, {
            title: '值',
            width: 90,
            align: 'center',
            dataIndex: 'value2',
            scopedSlots: {customRender: 'value'},
          },
          {
            title: '参数3',
            dataIndex: 'index3',
            align: 'center',
            width: 90,
            scopedSlots: {customRender: 'key'},
          }, {
            title: '值',
            width: 90,
            align: 'center',
            dataIndex: 'value3',
            scopedSlots: {customRender: 'value'},
          },
         /* {
            title: '参数4',
            dataIndex: 'index4',
            align: 'center',
            width: 90,
            scopedSlots: {customRender: 'key'},
          }, {
            title: '值',
            width: 90,
            align: 'center',
            dataIndex: 'value4',
            scopedSlots: {customRender: 'value'},
          },
          {
            title: '参数5',
            dataIndex: 'index5',
            align: 'center',
            width: 90,
            scopedSlots: {customRender: 'key'},
          }, {
            title: '值',
            width: 90,
            align: 'center',
            dataIndex: 'value5',
            scopedSlots: {customRender: 'value'},
          }*/
        ],
        form: this.$form.createForm(this),
        height: '500px',
        saveParam:null,
        // 存储3数据选择最后输入的select值
        lastDataSelect:{
          'index1':'',
          'index2':'',
          'index3':''
        },

      }
    },
    computed: {
      ...mapGetters(['testTaskExcelData']),
    },
    created() {

    },
    mounted() {
      this.param = this.testTaskExcelData
      this.$store.commit('setTaskExcelData', null);

      if(this.param != null){
        this.fileList = this.param.fileListSource
        this.sheets = []
        this.options = []
        for (let i = 0; i < this.param.headers.length - 1; i++) {
          this.sheets.push(this.param.headers[i].sheetName)
          if(this.param.sheetName == this.param.headers[i].sheetName){
            for (let j = 0; j < this.param.headers[i].headers.length; j++) {
              this.options.push({label: this.param.headers[i].headers[j], value: j})
            }
          }
        }
        this.headerData = this.param.headers
        this.filterData = this.param.filterParam
        this.checked = []
        for (let i = 0; i < this.param.exportOptions.length; i++) {
          this.checked.push(this.param.exportOptions[i].index)
        }
        this.sheetName = this.param.sheetName
      }else{
        //document.getElementById("checkAll").click()
      }
    },

    methods: {
      fileChange(file){
        this.fileList = file.fileList
        console.log(this.fileList)
        if(file.file.response){

          if(Array.isArray(file.file.response.data)){
            this.headerData = file.file.response.data
            let data = file.file.response.data
            this.sheets = []
            //{label: '工步序号', value: 'StepNum', key: 'stepNum'},
            for (let i = 0; i < data.length-1; i++) {
              this.sheets.push(data[i].sheetName)
            }
            this.headerSpinning = false
          }

        }
      },
      beforeUpload(file) {
        const fileName = file.name;
        const isExcel = fileName.endsWith('.xls') || fileName.endsWith('.xlsx');
        if(!isExcel){
          this.$message.warn("请上传excel文件！")
        }
        return isExcel;
      },
      onChange() {

        let num = this.checked.length
        let allNum = this.options.length

        this.indeterminate = num == 0 ?null: 0 < num < allNum;
        this.checkAll = num > 0 && num == allNum
        this.$nextTick(() => {
          if(this.checkAll){
            this.indeterminate = false
          }
        })

      },

      // 3数据选择select改变事件
      handleDataSelectChange(value,option,index){
        this.lastDataSelect[index] = value
      },

      onCheckAllChange(e) {
        Object.assign(this, {
          checked: e.target.checked ?  this.filterValue(this.options): [],
          indeterminate: null,
          checkAll: e.target.checked,
        });

      },
      filterValue(list){
        let newList = []
        for (let i = 0; i < list.length; i++) {
          newList.push(list[i].value)
        }
        return newList
      },


      handleSubmit(){
        const {
          form: {
            validateFields
          }
        } = this

        this.confirmLoading = true

        let param = {}
        param.filterParam = this.filterData
        let exportOptions = []
        for (let i = 0; i < this.checked.length; i++) {
          exportOptions.push({index:this.checked[i],value:this.options.find(e => e.value == this.checked[i]).label})
        }
        param.exportOptions = exportOptions
        param.sheetName = this.sheetName

        let fileList = []
        for (let i = 0; i < this.fileList.length; i++) {
          if(Array.isArray(this.fileList[i].response.data)){

            fileList.push({url:'http://**********:82/sysFileInfo/download?id='+
              this.fileList[i].response.data.find(e => e.sheetName == 'fileId').headers,name:this.fileList[i].name})
          }else{
            fileList.push({url:'http://**********:82/sysFileInfo/download?id='+
              this.fileList[i].response.data,name:this.fileList[i].name})
          }
        }

        param.fileList = fileList
        param.headers = this.headerData
        param.fileListSource = this.fileList
        validateFields((errors, values) => {
          if (!errors) {
            addJMTask(Object.assign(values,param)).then(res => {
              if (res.success) {
                this.$message.success('导出任务创建成功')
                this.$router.push('/testDataHistory');
                //this.$parent.showView(12)
              } else {
                this.$message.warn(res.message)
              }
            })
          }
          this.confirmLoading = false
          this.visible1 = false

        })


        /*validateFields((errors, values) => {

          this.confirmLoading = false
        })*/
      },

      exportData(){
        let param = {}
        param.sheetName = this.sheetName
        param.checked = this.checked
        param.fileList = this.fileList
        param.filterData = this.filterData

        if(param.fileList.length == 0){
          this.$message.warn('请上传文件')
          return
        }
        if(param.sheetName == null){
          this.$message.warn('请选择数据表')
          return
        }
        if(param.filterData.length == 0){
          this.$message.warn('请填写数据选择参数')
          return
        }
        if(param.checked.length == 0){
          this.$message.warn('请勾选导出项')
          return
        }

        this.visible1 = true

        this.saveParam = param


      },
      // 3数据选择--添加按钮事件
      addFilterData(){
        if(this.filterData.length > 0){
          this.filterData.push({
            index1: this.filterData[this.filterData.length -1]['index1'] ? this.filterData[this.filterData.length -1]['index1'] : null,value1:null,
            index2: this.filterData[this.filterData.length -1]['index2'] ? this.filterData[this.filterData.length -1]['index2'] : null,value1:null,
            index3: this.filterData[this.filterData.length -1]['index3'] ? this.filterData[this.filterData.length -1]['index3'] : null,value1:null,
          })
        }else{
          this.filterData.push({
           })
        }


      },

      handleCancel() {
        this.visible = false
      },
      handleCancel1() {
        this.visible1 = false
      },

      changeDataType(value){
        this.filterData = []

        let columns = this.headerData.find(e => e.sheetName == value)

        //{label: '工步序号', value: 'StepNum', key: 'stepNum'},
        this.options = []
        for (let i = 0; i < columns.headers.length; i++) {
          this.options.push({label: columns.headers[i], value: i})
        }
        this.checkAll = false
        this.$nextTick(() => {
          document.getElementById("checkAll").click()
        })

      },

      copyFromExcel(event,column,index){

        let arr = event.clipboardData.getData('text').split("\n")
        if(arr.length > 1){
          for (let i = 1; i < arr.length; i++) {
            if(null != arr[i] && '' != arr[i] && arr[i].length != 0){
              if(this.filterData.length > index+i){
                this.filterData[index+i][column] = arr[i]
              }else{
                this.filterData.push({
                  index1:this.filterData[index].index1,value1:null,
                  index2:this.filterData[index].index2,value2:null,
                  index3:this.filterData[index].index3,value3:null,
                })
                this.filterData[index+i][column] = arr[i]
              }

            }

          }

        }

        setTimeout(() => {
          this.filterData[index][column] = arr[0]
        }, 10)


      }



    }
  }
</script>
<style lang="less" scoped=''>
  /deep/ .ant-table-thead > tr > th {
    padding: 5px !important;
    font-size: 14px !important;
  }

  /deep/ .ant-table-tbody > tr > td {
    padding: 0px !important;
    height: 32px !important;
    font-size: 12px !important;
  }


  /deep/ .ant-calendar-picker-icon {
    display: none;
  }


  /deep/ .ant-calendar-picker-input.ant-input {
    color: black;
    font-size: 12px;
    border: 0;
    text-align: center;
    padding: 0;
  }

  .red {
    background-color: #ed0000;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .yellow {
    background-color: #ffc000;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .grey {
    background-color: rgba(223, 223, 223, 0.25);
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .ant-modal-body {
    padding: 0;
  }



  /deep/ .ant-btn > i, /deep/ .ant-btn > span {
    display: flex;
    justify-content: center;
  }

  /deep/ .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
    color: #fff;
    background: #1890FF;
  }

  .green {
    background-color: #58a55c;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

  }

  /deep/ #table1 > div > div > div > div > div > div > table > thead {
    height: 64px;
  }

  /deep/ #table1 > .ant-table-wrapper > div > div > ul {
    display: none;
  }


  /deep/ .ant-table-pagination.ant-pagination {
    float: right;
    margin: 0;
  }

  .float {
    width: 28%;
    float: left;
    margin-right: 10px;
    text-align: center;
  }
  .floatTable {
    width: 40%;
    float: left;
    margin-right: 10px;
    text-align: center;
  }

  .float1 {
    width: 11%;
    float: left;
    margin-right: 10px;
    text-align: center;
  }
  .float2 {
    width: 17%;
    float: left;
    margin-right: 10px;
    text-align: center;
  }

  /deep/ .ant-checkbox-group-item {
    display: block;
    width: 100%;
    text-align: left;
  }

  .title {
    font-size: large;
    margin-bottom: 20px;
  }

  .numTitle {
    font-size: xx-large;
  }

  /deep/.ant-table-footer {
   padding: 0;
  }
</style>