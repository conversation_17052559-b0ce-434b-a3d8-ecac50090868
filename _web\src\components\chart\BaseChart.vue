<template>
  <div class="base-chart-container" ref="chartContainer">
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: '<PERSON><PERSON><PERSON>',
  props: {
    height: {
      type: [String, Number],
      default: '300px'
    },
    width: {
      type: [String, Number],
      default: '100%'
    }
  },
  mounted() {
    const container = this.$refs.chartContainer;
    if (container) {
      container.style.width = typeof this.width === 'number' ? `${this.width}px` : this.width;
      container.style.height = typeof this.height === 'number' ? `${this.height}px` : this.height;
    }
  }
}
</script>

<style scoped>
.base-chart-container {
  position: relative;
  overflow: hidden;
}
</style>