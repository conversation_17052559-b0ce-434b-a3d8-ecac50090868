<template>
  <a-modal title="新增" :width="500" :visible="visible" :confirmLoading="confirmLoading" @ok="handleSubmit" @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">

        <a-form-item label="项目类别" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input placeholder="请输入项目类别" v-decorator="['projectCategory', {rules: [{required: true, message: '请输入项目类别！'}]}]" />
        </a-form-item>
        <a-form-item label="项目名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input placeholder="请输入项目名称" v-decorator="['projectName', {rules: [{required: true, message: '请输入项目名称！'}]}]" />
        </a-form-item>
        <a-form-item label="单位" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input placeholder="请输入单位" v-decorator="['unit', {rules: [{required: true, message: '请输入单位！'}]}]" />
        </a-form-item>
        <a-form-item label="客户sor输入" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input placeholder="请输入客户sor输入" v-decorator="['customerSor']"/>
        </a-form-item>
        <a-form-item label="EVE定义" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input placeholder="请输入EVE定义" v-decorator="['eveSor']"/>
        </a-form-item>
        <a-form-item label="状态识别" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>

          <a-select  size="small" v-decorator="['checkStatus']">
            <a-select-option :value="parseInt(1)">
              满足
            </a-select-option>
            <a-select-option :value="parseInt(2)">
              不满足
            </a-select-option>
            <a-select-option :value="parseInt(3)">
              TBD
            </a-select-option>
            <a-select-option :value="parseInt(0)">
              /
            </a-select-option>

          </a-select>
        </a-form-item>
        <a-form-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
          <a-input placeholder="请输入备注" v-decorator="['remark']"/>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import {
    add
  } from '@/api/modular/system/batteryDesignSorManage'
  export default {
    data() {
      return {
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 18
          }
        },
        visible: false,
        batteryId: null,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      add(batteryId) {
        this.visible = true
        this.batteryId = batteryId
      },
      handleSubmit() {
        const {
          form: {
            validateFields
          }
        } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {

            values['batteryId'] = this.batteryId
            add(values).then((res) => {
              this.confirmLoading = false
              if (res.success) {
                this.$message.success('新增成功')
                this.handleCancel()
                this.$emit('ok', values)
              } else {
                this.$message.error('新增失败：' + res.message)
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel() {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
<style lang="less">
  .ant-form-item {
    margin-bottom: 3px;

  }
</style>