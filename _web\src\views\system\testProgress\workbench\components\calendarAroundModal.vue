<template>
	<a-modal
		:title="`${modalData.testName}`"
		:visible="true"
		width="90%"
		:centered="true"
		:cancel-button-props="{ style: { display: 'none' } }"
		okText="关闭"
		@cancel="handleModelCancel"
		@ok="handleModelCancel"
	>
		<div class="modal-wrapper">
			<!-- 第一阶段（first） -->
			<div v-if="modalData.taskType.indexOf('first') !== -1">
				<!-- 步骤条 -->
				<div>
					<a-steps :current="current" size="small">
						<a-step title="首次中检" />
						<a-step v-if="pictureOrVideo" title="拍照" />
						<a-step title="进箱" />
					</a-steps>
				</div>

				<!-- 首次中检 -->
				<template v-if="current === 0">
					<a-spin :spinning="modalLoading">
						<!-- 描述信息 -->
						<div>
							<a-descriptions title="详细信息">
								<a-descriptions-item label="测试项目别名">
									{{ originalData.lifeTestRecordDataMap[0].alias }}
								</a-descriptions-item>
								<a-descriptions-item>
                  <template slot="label">
                    <span style="font-weight: bold">中检类型</span>
                  </template>
                  <div style="font-weight: bold;color: black">
									{{
										originalData.lifeTestRecordDataMap[0].middleCheck === "small"
											? "小中检"
											: originalData.lifeTestRecordDataMap[0].middleCheck === "large"
											? "大中检"
                      : originalData.lifeTestRecordDataMap[0].middleCheck === "recharge"
                      ? "补电"
											: "-"
									}}
                  </div>
								</a-descriptions-item>
								<a-descriptions-item label="计划开始时间">
									{{ originalData.inDate || "-" }}
								</a-descriptions-item>
								<a-descriptions-item label="计划结束时间">
									{{ originalData.outDate || "-" }}
								</a-descriptions-item>
								<a-descriptions-item label="总存储天数">
									{{ originalData.totalDay || "-" }}
								</a-descriptions-item>
								<a-descriptions-item label="存储阶段">
									{{
										originalData.orderNumber
											? originalData.orderNumber === 1
												? "初始性能检测"
												: originalData.orderNumber - 1
											: "-"
									}}
								</a-descriptions-item>
                <a-descriptions-item label="测试温度&SOC">
                  {{ progress.t ? progress.t.replaceAll("℃", "") + "℃" : "-" }} & {{ progress.soc ? progress.soc.replaceAll("%", "") + "%" : "-" }}
                </a-descriptions-item>
							</a-descriptions>
						</div>
						<!-- 表格 -->
						<div class="auto-table">
							<a-descriptions title="详细信息"></a-descriptions>
							<div>
								<a-upload
									:disabled="modalData.taskStatus === '已完成' ? true : false"
									:headers="headers"
									:action="`/api/testProjectTodoTask/import`"
									name="file"
									:fileList="fileList"
									:data="{ ordTaskId: modalData.ordTaskId, middleCheckStage: 1 }"
									:showUploadList="false"
									accept="*"
									@change="handleUploadFile($event)"
								>
									<a-button
										:disabled="modalData.taskStatus === '已完成' ? true : false"
										type="primary"
										size="small"
										class="mr10"
										ghost
										>导入数据</a-button
									>
								</a-upload>
								<a-button class="mr10" type="primary" size="small" ghost @click="handleDownload($event)">导出模板</a-button>
								<!-- <a-button type="primary" size="small" ghost @click="handleResetCheckData">清除checkData</a-button> -->
								<a-tooltip>
									<template slot="title">
									  提示内容
									</template>
									<a-button v-if="serialObj.serialPortOpen"  class="mr10" type="primary" size="small" ghost @click="handleCloseConnect">断开电压内阻测试仪</a-button>
									<a-button id="connectBtn" v-else class="mr10" type="primary" size="small" ghost @click="handleOpenConnect">连接电压内阻测试仪</a-button>
								</a-tooltip>
							</div>
							<a-table
								bordered
								class="mt10"
								:columns="firstColums"
								:rowKey="record => record.cellTestCode"
								:data-source="firstData"
								:pagination="false"
							>
								<span v-for="item in firstList" :slot="item.dataIndex" slot-scope="text, record, index">
									<!-- 测试项目别名 -->
									<span v-if="item.dataIndex === 'alias'">
										<div class="blue hand" @click="handleAliasCopy">{{ text }}<a-icon type="copy" /></div>
									</span>

									<!-- 测试编码 -->
									<span v-else-if="item.dataIndex === 'cellTestCode'">
										<div class="blue hand" @click="handleCopy(text)">{{ text }}<a-icon type="copy" /></div>
									</span>

									<!-- 中检类型 -->
									<span
										v-else-if="item.dataIndex === 'middleCheck'"
										:id="`first-middleCheck-${index}`"
										slot="middleCheck"
										slot-scope="text, record, index"
									>
										<a-button
											type="link"
                      :disabled="record.batteryStatus !== 'ongoing'"
											:style="record.isMiddleClick ? 'color:green' : ''"
											@click="() => chooseMgData(record, `first-middleCheck-${index}`)"
										>
											{{ record.isMiddleClick ? (record.checkData === null ? '无匹配数据' : (text === "large" ? "大中检" : text === "small" ? "小中检" : "补电")) : "请选择数据"
											}}<a-icon v-if="record.isMiddleClick" type="edit" />
										</a-button>
									</span>

                  <!-- 电芯状态 -->
                  <span v-else-if="item.dataIndex === 'batteryStatus'">
                    <a-select v-model="record.batteryStatus" @change="changeBatteryStatus"
                              :disabled="modalData.taskStatus === '已完成' ? true : false"
                              style="width: 160px;font-size: 14px;">
                      <a-select-option value="ongoing">
                        进行中
                      </a-select-option>
                      <a-select-option value="earlyEnd">
                        状态正常-提前结束
                      </a-select-option>
                      <a-select-option value="batteryDisassembly">
                        状态正常-电池拆解
                      </a-select-option>
                      <a-select-option value="pressureDrop">
                        掉压失效-终止测试
                      </a-select-option>
                      <a-select-option value="abnormalHot">
                        异常发热-终止测试
                      </a-select-option>
                      <a-select-option value="openShellAndLeak">
                        开壳漏液-终止测试
                      </a-select-option>
                      <a-select-option value="shellRust">
                        壳体生锈-终止测试
                      </a-select-option>
                      <a-select-option value="operationError">
                        作业错误-终止测试
                      </a-select-option>
                      <a-select-option value="thermalRunaway">
                        热失控-终止测试
                      </a-select-option>
                      <a-select-option value="acrException">
                        内阻异常-终止测试
                      </a-select-option>

                      <a-select-option value="swelling">
                        鼓包形变-终止测试
                      </a-select-option>
                    </a-select>
                  </span>

									<!-- 其他填写数据 -->
									<a-input
										v-else
										:id="`first-${item.dataIndex}-${index}`"
                    :style="abnormalList.find(a => a.cellCode == firstData[index]['cellTestCode']) &&
                    abnormalList.find(a => a.cellCode == firstData[index]['cellTestCode'])[item.dataIndex+'Flag']? {borderColor:'red'}:null
                    "
                    :title="abnormalList.find(a => a.cellCode == firstData[index]['cellTestCode']) &&
                    abnormalList.find(a => a.cellCode == firstData[index]['cellTestCode'])[item.dataIndex+'Flag']?
                    abnormalList.find(a => a.cellCode == firstData[index]['cellTestCode'])[item.dataIndex+'Msg']:null
                    "
										v-model="firstData[index][item.dataIndex]"
										:disabled="abnormalStatus == 'techConfirm' || (modalData.taskStatus === '已完成' ? true : false) || record.batteryStatus !== 'ongoing'"
										@paste="copyFromExcel($event, firstColums, firstData, index, item.dataIndex)"
										@keyup.enter="handleWrite(item.dataIndex,index,`first-${item.dataIndex}-${index + 1}`)"
										@blur="value => handleInput(value.target._value, `first-${item.dataIndex}-${index}`, index)"
									/>
								</span>
							</a-table>
						</div>
					</a-spin>
				</template>

        <!-- 拍照 -->
        <div v-if="pictureOrVideo && current === 1">
          <a-spin :spinning="modalLoading">
            <!-- 描述信息 -->
            <div>
              <a-descriptions title="详细信息">
                <a-descriptions-item label="测试项目别名">
                  {{ originalData.lifeTestRecordDataMap[0].alias }}
                </a-descriptions-item>
                <a-descriptions-item>
                  <template slot="label">
                    <span style="font-weight: bold">中检类型</span>
                  </template>
                  <div style="font-weight: bold;color: black">
                    {{
                      originalData.lifeTestRecordDataMap[0].middleCheck === "small"
                        ? "小中检"
                        : originalData.lifeTestRecordDataMap[0].middleCheck === "large"
                          ? "大中检"
                          : originalData.lifeTestRecordDataMap[0].middleCheck === "recharge"
                            ? "补电"
                            : "-"
                    }}
                  </div>
                </a-descriptions-item>
                <a-descriptions-item label="计划开始时间">
                  {{ originalData.inDate || "-" }}
                </a-descriptions-item>
                <a-descriptions-item label="计划结束时间">
                  {{ originalData.outDate || "-" }}
                </a-descriptions-item>
                <a-descriptions-item label="总存储天数">
                  {{ originalData.totalDay || "-" }}
                </a-descriptions-item>
                <a-descriptions-item label="存储阶段">
                  {{
                    originalData.orderNumber
                      ? originalData.orderNumber === 1
                        ? "初始性能检测"
                        : originalData.orderNumber - 1
                      : "-"
                  }}
                </a-descriptions-item>
                <a-descriptions-item label="测试温度&SOC">
                  {{ progress.t ? progress.t.replaceAll("℃", "") + "℃" : "-" }} & {{ progress.soc ? progress.soc.replaceAll("%", "") + "%" : "-" }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
            <div class="auto-table">
              <a-descriptions title="详细信息"></a-descriptions>
              <!-- 过程视频 -->
              <div v-if="originalData.video ==='1'" style="margin-bottom: 50px">
                <div style="border: 1px solid lightgrey;width: 100px;height:40px;float: left;display: flex;justify-content: center; align-items: center;">
                  <span style="color: black;">测试视频</span>
                </div>
                <div style="float: right;width: 50%;padding-top: 8px;" v-if="uploadProgress > 0 || uploadProgressShow">
                  <a-progress strokeWidth="12" :percent="uploadProgress"></a-progress>
                </div>
                <div style="border: 1px solid lightgrey;width: 280px;height:40px;float: left;display: flex;justify-content: center; align-items: center;">
                  <span v-if="!originalData.videoName" style="display: flex;justify-content: center; align-items: center;">
                    <a-upload
                      name="file"
                      :customRequest="handleUpload"
                      :headers="headers"
                      :data="uploadData"
                      :action="picOrVidPostUrl"
                      :multiple="false"
                      :before-upload="beforeUploadVideo"
                      :showUploadList="false"
                      @change="uploadVideo($event)"
                      accept=".mp4,.avi,.wmv,.mov">
                      <a-spin :spinning="videoLoading"><a>上传</a></a-spin>
                    </a-upload>
                  </span>
                  <span v-else style="display: flex;justify-content: center; align-items: center;">
                    <a style="color: green;text-align: center;" @click="openFileOrDownload(originalData.videoId, originalData.videoName)">{{ originalData.videoName }}</a>
                      <a-popconfirm
                        placement="topRight"
                        ok-text="删除"
                        cancel-text="取消"
                        @confirm="deleteVideo($event)">
                        <template slot="title"> 确认删除视频"{{ originalData.videoName }}"吗 </template>
                        <a-icon v-if="modalData.taskStatus !== '已完成'" type="close" style="float: right;padding: 5px 0px 0px 5px" />
                      </a-popconfirm>
                </span>
                </div>
              </div>
              <!-- 图片 -->
              <a-table
                bordered
                v-if="originalData.picture ==='1'"
                class="mt10"
                :columns="picOrVidColumn"
                :rowKey="record => record.cellTestCode"
                :data-source="firstData"
                :pagination="false"
              >
								<span v-for="item in picOrVidList" :slot="item.dataIndex" slot-scope="text, record, index">
									<!-- 测试项目别名 -->
									<span v-if="item.dataIndex === 'alias'">
										<div class="blue hand" @click="handleAliasCopy">{{ text }}<a-icon type="copy" /></div>
									</span>

                  <!-- 测试编码 -->
									<span v-else-if="item.dataIndex === 'cellTestCode'">
										<div class="blue hand" @click="handleCopy(text)">{{ text }}<a-icon type="copy" /></div>
									</span>

                  <!-- 图片上传 -->
                  <span v-else>
                    <span v-if="record.batteryStatus !== 'ongoing'">
                      <a style="color: darkgrey;">上传</a>
                    </span>
                    <span v-else-if="!record.samplePicture[item.dataIndex].id && modalData.taskStatus !== '已完成'" style="display: flex;justify-content: center; align-items: center;">
                      <a-upload
                        name="file"
                        :headers="headers"
                        :data="uploadData"
                        :action="picOrVidPostUrl"
                        :before-upload="beforeUploadPicture"
                        :multiple="false"
                        :showUploadList="false"
                        @change="uploadPicture($event, item.dataIndex, index)"
                        accept=".jpg,.png,.gif">
                        <a>上传</a>
                      </a-upload>
                    </span>
                    <span v-else-if="record.samplePicture[item.dataIndex].uid">
                      <a-upload
                        list-type="picture-card"
                        class="avatar-uploader"
                        :disabled="modalData.taskStatus === '已完成'"
                        @change="deletePicture(item.dataIndex, index)"
                        :fileList="[record.samplePicture[item.dataIndex]]"
                        @preview="handlePreview">
                      </a-upload>
                    </span>
                    <span v-else>
                      <a style="color: green" @click="openFileOrDownload(record.samplePicture[item.dataIndex].id,record.samplePicture[item.dataIndex].name)">{{ record.samplePicture[item.dataIndex].name }}</a>
                        <a-popconfirm
                          placement="topRight"
                          ok-text="删除"
                          cancel-text="取消"
                          @confirm="deletePicture(item.dataIndex, index)">
                          <template slot="title"> 确认删除文件"{{ record.samplePicture[item.dataIndex].name }}"吗 </template>
                          <a-icon v-if="modalData.taskStatus !== '已完成'" type="close" style="float: right;padding: 5px 0px 0px 5px" />
                        </a-popconfirm>
                     </span>
                  </span>
								</span>
              </a-table>
            </div>
          </a-spin>
        </div>

				<!-- 进箱 -->
				<div class="time-block" v-if="(pictureOrVideo && current === 2)
				|| (!pictureOrVideo && current === 1)">
					<div v-if="firstData.findIndex(v => v.batteryStatus === 'ongoing') !== -1">
						<span class="mr10">进箱时间：</span>
						<a-date-picker
              :allowClear="false"
							:disabled="modalData.taskStatus === '已完成' ? true : false"
							:disabled-date="disabledDate"
							:default-value="actualInDate"
							@change="(date, dateString) => handleChangeDate(dateString)"
						/>
					</div>
					<div style="margin-top: 50px">
						<span class="mr10">进箱位置：</span>
						<a-button type="primary" :disabled="(modalData.taskStatus === '已完成' ? true : false)
						|| (firstData.findIndex(v => v.batteryStatus === 'ongoing') === -1)" @click="progress.testAddress == 'R3' || progress.testAddress == 'JM'?openSelectBoxSafe():openSelectBox()">设置进箱位置</a-button>
						<a-table
							class="mt10"
							bordered
							style="width: 80%;"
							:columns="inBoxColumns"
							:rowKey="record => record.cellTestCode"
							:data-source="firstData"
							:pagination="false"
						>
						</a-table>
					</div>
				</div>
			</div>

			<!-- 最后一阶段(last) -->
			<div v-if="modalData.taskType.indexOf('last') !== -1">
				<!-- 步骤条 -->
				<div>
					<a-steps :current="current" size="small">
						<a-step title="出箱" />
						<a-step title="出箱中检" />
            <a-step v-if="pictureOrVideo" title="拍照" />
					</a-steps>
				</div>

				<!-- 出箱 -->
				<div class="time-block" v-if="current === 0">
          <div v-if="modalData.stageFlag === 1" style="margin-bottom: 20px;">
            <span style="margin-right: 166px" class="mr10">出箱测试编码：</span>
            <a-table
              bordered
              class="mt10"
              style="display: flex;justifyContent: center;alignItems: center;margin-left: 30px;"
              :columns="stageColumns"
              :show-header="false"
              :rowKey="record => record.cellTestCode"
              :data-source="firstData"
              :pagination="false"
            ></a-table>
          </div>
					<div>
						<span class="mr10">出箱时间：</span>
						<a-date-picker
              :allowClear="false"
							:disabled="modalData.taskStatus === '已完成' ? true : false"
							:disabled-date="disabledOutDate"
							:value="actualOutDate"
							@change="(date, dateString) => handleChangeDate(dateString, 'out')"
						/>
					</div>
				</div>

				<!-- 出箱中检 -->
				<template v-if="current === 1">
					<a-spin :spinning="modalLoading">
						<!-- 详细信息 -->
						<div>
							<a-descriptions title="详细信息">
								<a-descriptions-item label="测试项目别名">
									{{ originalData.lifeTestRecordDataMap[0].alias }}
								</a-descriptions-item>
								<a-descriptions-item>
                  <template slot="label">
                    <span style="font-weight: bold">中检类型</span>
                  </template>
                  <div style="font-weight: bold;color: black">
									{{
										originalData.lifeTestRecordDataMap[0].middleCheck === "small"
											? "小中检"
											: originalData.lifeTestRecordDataMap[0].middleCheck === "large"
											? "大中检"
                      : originalData.lifeTestRecordDataMap[0].middleCheck === "recharge"
                      ? "补电"
											: "-"
									}}
                  </div>
								</a-descriptions-item>
								<a-descriptions-item label="计划开始时间">
									{{ originalData.inDate || "-" }}
								</a-descriptions-item>
								<a-descriptions-item label="计划结束时间">
									{{ originalData.outDate || "-" }}
								</a-descriptions-item>
								<a-descriptions-item label="总存储天数">
									{{ originalData.totalDay || "-" }}
								</a-descriptions-item>
								<a-descriptions-item label="存储阶段">
									{{
										originalData.orderNumber
											? originalData.orderNumber === 1
												? "初始性能检测"
												: originalData.orderNumber - 1
											: "-"
									}}
								</a-descriptions-item>
                <a-descriptions-item label="测试温度&SOC">
                  {{ progress.t ? progress.t.replaceAll("℃", "") + "℃" : "-" }} & {{ progress.soc ? progress.soc.replaceAll("%", "") + "%" : "-" }}
                </a-descriptions-item>
							</a-descriptions>
						</div>
						<div>
							<a-descriptions title="详细信息"> </a-descriptions>
							<div>
								<a-upload
									:headers="headers"
									:disabled="modalData.taskStatus === '已完成' ? true : false"
									:action="`/api/testProjectTodoTask/import`"
									name="file"
									:fileList="fileList"
									:data="{ ordTaskId: modalData.ordTaskId, middleCheckStage: 1 }"
									:showUploadList="false"
									accept="*"
									@change="handleUploadFile($event)"
								>
									<a-button
										:disabled="modalData.taskStatus === '已完成' ? true : false"
										type="primary"
										size="small"
										class="mr10"
										ghost
										>导入数据</a-button
									>
								</a-upload>
								<a-button class="mr10" type="primary" size="small" ghost @click="handleDownload($event)">导出模板</a-button>
								<!-- <a-button type="primary" size="small" ghost @click="handleResetCheckData">清除checkData</a-button> -->

				<a-tooltip>
					<template slot="title">
					  提示内容
					</template>
					<a-button v-if="serialObj.serialPortOpen"  class="mr10" type="primary" size="small" ghost @click="handleCloseConnect">断开电压内阻测试仪</a-button>
					<a-button id="connectBtn" v-else class="mr10" type="primary" size="small" ghost @click="handleOpenConnect">连接电压内阻测试仪</a-button>
				</a-tooltip>
							</div>
							<div class="auto-table">
								<a-table
									bordered
									class="mt10"
									:columns="lastColums"
									:rowKey="record => record.cellTestCode"
									:data-source="lastData"
									:pagination="false"
								>
									<span v-for="item in lastList" :slot="item.dataIndex" slot-scope="text, record, index">
										<!-- 测试项目别名 -->
										<span v-if="item.dataIndex === 'alias'">
											<div class="blue hand" @click="handleAliasCopy">{{ text }}<a-icon v-if="text" type="copy" /></div>
										</span>

										<!-- 测试项目编码 -->
										<span v-else-if="item.dataIndex === 'cellTestCode'">
											<div class="blue hand" @click="handleCopy(text)">
												{{ text }}<a-icon v-if="text" type="copy" />
											</div>
										</span>

										<!-- 中检类型 -->
										<span
											v-else-if="item.dataIndex === 'middleCheck'"
											:id="`last-middleCheck-${index}`"
											slot="middleCheck"
											slot-scope="text, record, index"
										>
											<a-button
												type="link"
                        :disabled="record.batteryStatus !== 'ongoing'"
												:style="record.isMiddleClick ? 'color:green' : ''"
												@click="() => chooseMgData(record, `last-middleCheck-${index}`)"
											>
												{{ record.isMiddleClick ? (record.checkData === null ? '无匹配数据' : (text === "large" ? "大中检" : text === "small" ? "小中检" : "补电")) : "请选择数据"
												}}<a-icon v-if="record.isMiddleClick" type="edit" />
											</a-button>
										</span>

                    <!-- 电芯状态 -->
                    <span v-else-if="item.dataIndex === 'batteryStatus'">
                      <a-select v-model="record.batteryStatus" @change="changeBatteryStatus"
                                :disabled="modalData.taskStatus === '已完成' ? true : false"
                                style="width: 160px;font-size: 14px;">
                        <a-select-option value="ongoing">
                          进行中
                        </a-select-option>
                        <a-select-option value="earlyEnd">
                          状态正常-提前结束
                        </a-select-option>
                        <a-select-option value="batteryDisassembly">
                          状态正常-电池拆解
                        </a-select-option>
                        <a-select-option value="pressureDrop">
                          掉压失效-终止测试
                        </a-select-option>
                        <a-select-option value="abnormalHot">
                          异常发热-终止测试
                        </a-select-option>
                        <a-select-option value="openShellAndLeak">
                          开壳漏液-终止测试
                        </a-select-option>
                        <a-select-option value="shellRust">
                          壳体生锈-终止测试
                        </a-select-option>
                        <a-select-option value="operationError">
                          作业错误-终止测试
                        </a-select-option>
                        <a-select-option value="thermalRunaway">
                          热失控-终止测试
                        </a-select-option>
                        <a-select-option value="acrException">
                          内阻异常-终止测试
                        </a-select-option>
                        <a-select-option value="acrException">
                          内阻异常-终止测试
                        </a-select-option>
                        <a-select-option value="swelling">
                          鼓包形变-终止测试
                        </a-select-option>
                      </a-select>
                    </span>

										<!-- 其他填写数据 -->
										<a-input
											v-else
											:id="`last-${item.dataIndex}-${index}`"
                      :style="abnormalList.find(a => a.cellCode == firstData[index]['cellTestCode']) &&
                      abnormalList.find(a => a.cellCode == firstData[index]['cellTestCode'])[item.dataIndex+'Flag']? {borderColor:'red'}:null
                      "
                      :title="abnormalList.find(a => a.cellCode == firstData[index]['cellTestCode']) &&
                      abnormalList.find(a => a.cellCode == firstData[index]['cellTestCode'])[item.dataIndex+'Flag']?
                      abnormalList.find(a => a.cellCode == firstData[index]['cellTestCode'])[item.dataIndex+'Msg']:null
                      "
											v-model="lastData[index][item.dataIndex]"
											:disabled="abnormalStatus == 'techConfirm' || (modalData.taskStatus === '已完成' ? true : false) || record.batteryStatus !== 'ongoing'"
											@paste="copyFromExcel($event, lastColums, lastData, index, item.dataIndex)"
                      						@keyup.enter="handleWrite(item.dataIndex,index,`last-${item.dataIndex}-${index + 1}`)"
											@blur="value => handleInput(value.target._value, `last-${item.dataIndex}-${index}`, index)"
										/>
									</span>
								</a-table>
							</div>
						</div>
					</a-spin>
				</template>

        <!-- 拍照 -->
        <div v-if="pictureOrVideo && current === 2">
          <a-spin :spinning="modalLoading">
            <!-- 描述信息 -->
            <div>
              <a-descriptions title="详细信息">
                <a-descriptions-item label="测试项目别名">
                  {{ originalData.lifeTestRecordDataMap[0].alias }}
                </a-descriptions-item>
                <a-descriptions-item>
                  <template slot="label">
                    <span style="font-weight: bold">中检类型</span>
                  </template>
                  <div style="font-weight: bold;color: black">
                    {{
                      originalData.lifeTestRecordDataMap[0].middleCheck === "small"
                        ? "小中检"
                        : originalData.lifeTestRecordDataMap[0].middleCheck === "large"
                          ? "大中检"
                          : originalData.lifeTestRecordDataMap[0].middleCheck === "recharge"
                            ? "补电"
                            : "-"
                    }}
                  </div>
                </a-descriptions-item>
                <a-descriptions-item label="计划开始时间">
                  {{ originalData.inDate || "-" }}
                </a-descriptions-item>
                <a-descriptions-item label="计划结束时间">
                  {{ originalData.outDate || "-" }}
                </a-descriptions-item>
                <a-descriptions-item label="总存储天数">
                  {{ originalData.totalDay || "-" }}
                </a-descriptions-item>
                <a-descriptions-item label="存储阶段">
                  {{
                    originalData.orderNumber
                      ? originalData.orderNumber === 1
                        ? "初始性能检测"
                        : originalData.orderNumber - 1
                      : "-"
                  }}
                </a-descriptions-item>
                <a-descriptions-item label="测试温度&SOC">
                  {{ progress.t ? progress.t.replaceAll("℃", "") + "℃" : "-" }} & {{ progress.soc ? progress.soc.replaceAll("%", "") + "%" : "-" }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
            <div class="auto-table">
              <a-descriptions title="详细信息"></a-descriptions>
              <!-- 过程视频 -->
              <div v-if="originalData.video ==='1'" style="margin-bottom: 50px">
                <div style="border: 1px solid lightgrey;width: 100px;height:40px;float: left;display: flex;justify-content: center; align-items: center;">
                  <span style="color: black;">测试视频</span>
                </div>
                <div style="float: right;width: 50%;padding-top: 8px;" v-if="uploadProgress > 0 || uploadProgressShow">
                  <a-progress strokeWidth="12" :percent="uploadProgress"></a-progress>
                </div>
                <div style="border: 1px solid lightgrey;width: 280px;height:40px;float: left;display: flex;justify-content: center; align-items: center;">
                  <span v-if="!originalData.videoName" style="display: flex;justify-content: center; align-items: center;">
                    <a-upload
                      name="file"
                      :customRequest="handleUpload"
                      :headers="headers"
                      :data="uploadData"
                      :action="picOrVidPostUrl"
                      :multiple="false"
                      :before-upload="beforeUploadVideo"
                      :showUploadList="false"
                      @change="uploadVideo($event)"
                      accept=".mp4,.avi,.wmv,.mov">
                      <a-spin :spinning="videoLoading"><a>上传</a></a-spin>
                    </a-upload>
                  </span>
                  <span v-else style="display: flex;justify-content: center; align-items: center;">
                    <a style="color: green;text-align: center;" @click="openFileOrDownload(originalData.videoId, originalData.videoName)">{{ originalData.videoName }}</a>
                      <a-popconfirm
                        placement="topRight"
                        ok-text="删除"
                        cancel-text="取消"
                        @confirm="deleteVideo($event)">
                        <template slot="title"> 确认删除视频"{{ originalData.videoName }}"吗 </template>
                        <a-icon v-if="modalData.taskStatus !== '已完成'" type="close" style="float: right;padding: 5px 0px 0px 5px" />
                      </a-popconfirm>
                </span>
                </div>
              </div>
              <!-- 图片 -->
              <a-table
                bordered
                v-if="originalData.picture ==='1'"
                class="mt10"
                :columns="picOrVidColumn"
                :rowKey="record => record.cellTestCode"
                :data-source="firstData"
                :pagination="false"
              >
								<span v-for="item in picOrVidList" :slot="item.dataIndex" slot-scope="text, record, index">
									<!-- 测试项目别名 -->
									<span v-if="item.dataIndex === 'alias'">
										<div class="blue hand" @click="handleAliasCopy">{{ text }}<a-icon type="copy" /></div>
									</span>

                  <!-- 测试编码 -->
									<span v-else-if="item.dataIndex === 'cellTestCode'">
										<div class="blue hand" @click="handleCopy(text)">{{ text }}<a-icon type="copy" /></div>
									</span>

                  <!-- 图片上传 -->
                  <span v-else>
                    <span v-if="record.batteryStatus !== 'ongoing'">
                      <a style="color: darkgrey;">上传</a>
                    </span>
                    <span v-else-if="!record.samplePicture[item.dataIndex].id && modalData.taskStatus !== '已完成'" style="display: flex;justify-content: center; align-items: center;">
                      <a-upload
                        name="file"
                        :headers="headers"
                        :data="uploadData"
                        list-type="picture"
                        :action="picOrVidPostUrl"
                        :before-upload="beforeUploadPicture"
                        :multiple="false"
                        :showUploadList="false"
                        @change="uploadPicture($event, item.dataIndex, index)"
                        accept=".jpg,.png,.gif">
                        <a>上传</a>
                      </a-upload>
                    </span>
                    <span v-else-if="record.samplePicture[item.dataIndex].uid">
                      <a-upload
                        list-type="picture-card"
                        class="avatar-uploader"
                        :disabled="modalData.taskStatus === '已完成'"
                        @change="deletePicture(item.dataIndex, index)"
                        :fileList="[record.samplePicture[item.dataIndex]]"
                        @preview="handlePreview">
                      </a-upload>
                    </span>
                    <span v-else>
                      <a style="color: green" @click="openFileOrDownload(record.samplePicture[item.dataIndex].id,record.samplePicture[item.dataIndex].name)">{{ record.samplePicture[item.dataIndex].name }}</a>
                        <a-popconfirm
                          placement="topRight"
                          ok-text="删除"
                          cancel-text="取消"
                          @confirm="deletePicture(item.dataIndex, index)">
                          <template slot="title"> 确认删除文件"{{ record.samplePicture[item.dataIndex].name }}"吗 </template>
                          <a-icon v-if="modalData.taskStatus !== '已完成'" type="close" style="float: right;padding: 5px 0px 0px 5px" />
                        </a-popconfirm>
                     </span>
                  </span>
								</span>
              </a-table>
            </div>
          </a-spin>
        </div>
			</div>
		</div>

		<!-- 按钮条 -->
		<template slot="footer">
      <a-button :disabled="modalData.taskStatus === '已完成'" v-if="(modalData.taskType.indexOf('first') !== -1 && current === 0) ||
      (modalData.taskType.indexOf('last') !== -1 && current === 1)" type="primary" @click="handleSaveTestData()">
        保存
      </a-button>
			<a-button v-if="current === 1 || (pictureOrVideo && current === 2)" type="primary" @click="handleStep(-1)">上一步</a-button>
      <template v-if="current === 0 || (pictureOrVideo && current === 1)" type="primary" @click="handleStep(1)">

        <a-popconfirm placement="topRight" ok-text="传递实验室工程师处理" cancel-text="重新测试" @confirm="techConfirmCalendar" v-if="isAbnormal && abnormalStatus == 'create' && !isEngineerConfirmNotChange">
          <template slot="title">
            <div v-for="abnormal in abnormalList.filter(a => a.isAbnormal )">
              <span style="margin-right: 30px">{{abnormal.cellCode}}</span>
              <span>{{abnormal.msg}}</span>
            </div>
          </template>
          <a-button
            type="primary"
          >下一步</a-button>
        </a-popconfirm>


        <a-popconfirm placement="topRight" ok-text="确定下一步" cancel-text="取消" @confirm="handleStep(1)" v-else-if="isAbnormal &&  isEngineerConfirmNotChange">
          <template slot="title">
            <div v-for="abnormal in abnormalList.filter(a => a.isAbnormal || (a.handleMsg != null && a.handleMsg != ''))">
              <span style="margin-right: 30px">{{abnormal.cellCode}}</span>
              <span style="margin-right: 30px">{{abnormal.msg}}</span>
              <span>工程师处理意见:{{abnormal.handleMsg}}</span>
            </div>
          </template>
          <a-button
            type="primary"
          >下一步</a-button>
        </a-popconfirm>

        <a-button
          v-else-if="abnormalStatus == 'techConfirm'"
          type="primary"
          title="异常处理中"
          disabled
        >下一步</a-button>

        <a-button v-else type="primary" @click="handleStep(1)">下一步</a-button>




      </template>


			<!-- 第一阶段 -->
			<a-popconfirm
				v-if="((current === 1 && !pictureOrVideo) || (current === 2 && pictureOrVideo)) && modalData.taskType.indexOf('first') !== -1 && modalData.taskStatus !== '已完成'"
				placement="top"
				ok-text="确认"
				cancel-text="取消"
				@confirm="handleSubmit"
			>
				<template slot="title">
					<p>确认完成吗？</p>
				</template>
				<a-button type="primary">完成</a-button>
			</a-popconfirm>

			<!-- 最后一阶段 -->

      <template v-if="((current === 1 && !pictureOrVideo) || (current === 2 && pictureOrVideo)) && modalData.taskType.indexOf('last') !== -1 && modalData.taskStatus !== '已完成'">

        <a-popconfirm placement="topRight" ok-text="传递实验室工程师处理" cancel-text="重新测试" @confirm="techConfirmCalendar" v-if="current != 0 && isAbnormal && abnormalStatus == 'create' && !isEngineerConfirmNotChange">
          <template slot="title">
            <div v-for="abnormal in abnormalList.filter(a => a.isAbnormal )">
              <span style="margin-right: 30px">{{abnormal.cellCode}}</span>
              <span>{{abnormal.msg}}</span>
            </div>
          </template>
          <a-button
            type="primary"
          >完成</a-button>
        </a-popconfirm>

        <a-popconfirm placement="topRight" ok-text="确定完成" cancel-text="取消" @confirm="handleStep(1)" v-else-if="isAbnormal &&  isEngineerConfirmNotChange">
          <template slot="title">
            <div v-for="abnormal in abnormalList.filter(a => a.isAbnormal || (a.handleMsg != null && a.handleMsg != ''))">
              <span style="margin-right: 30px">{{abnormal.cellCode}}</span>
              <span style="margin-right: 30px">{{abnormal.msg}}</span>
              <span>工程师处理意见:{{abnormal.handleMsg}}</span>
            </div>
          </template>
          <a-button
            type="primary"
          >完成</a-button>
        </a-popconfirm>

        <a-button
          v-else-if="abnormalStatus == 'techConfirm'"
          type="primary"
          title="异常处理中"
          disabled
        >完成</a-button>
        <a-popconfirm
          v-else
          placement="top"
          ok-text="确认"
          cancel-text="取消"
          @confirm="handleLastSubmit"
        >
          <template slot="title">
            <p>确认完成吗？</p>
          </template>
          <a-button type="primary">完成</a-button>
        </a-popconfirm>

      </template>





			<a-button @click="handleModelCancel">关闭</a-button>
		</template>
		<step-data ref="stepData"></step-data>
		<div>
			<a-modal title="条码启动天数输入" :visible="isShowDays" @ok="handleModalOk" @cancel="handleModalCancel">
				<a-form :label-col="{ span: 11 }" :wrapper-col="{ span: 13 }">
					<a-form-item label="启动天数">
						<a-input-number v-model="startDay" :precision="0" :min="0" @keyup.enter="handleModalOk" />
					</a-form-item>
				</a-form>
			</a-modal>
      <a-modal
        :maskClosable="false"
        width="80%"
        title="设置进箱位置"
        :visible="isShowPositions"
        @ok="xshOk(false)"
        @cancel="isShowPositions = false"
      >

        <a-button @click="addXsh" style="margin-right: 20px" :disabled="
                ['方形','方型'].includes(progress.sampleType) || (progress.sampleType == '软包' && progress.testAddress != 'R2_2F')">新增吸塑盒</a-button>
        <a-popconfirm
          title="确认删除"
          ok-text="确认"
          cancel-text="取消"
          @confirm="deleteXsh()"
        >
          <a-button>删除吸塑盒</a-button>
        </a-popconfirm>

        <a-button @click="openListSelectLuo" style="float: right;margin-right: 20px">选择摞</a-button>

        <a-table
          :columns="xshColumns"
          :dataSource="xshData"
          class="mt10"
          bordered
          :rowKey="record => record.index"
          :pagination="false"
          :rowSelection="{
							selectedRowKeys: xshSelectedRowKeys, onChange: onXshSelectChange
						}"
        >
          <template slot="action" slot-scope="text, record, index, columns">
            <a @click="openSelectSampleData(record)" style="text-align: center" :disabled="
                ['方形','方型'].includes(progress.sampleType) || (progress.sampleType == '软包' && progress.testAddress != 'R2_2F')">选择电芯</a>
          </template>

          <template slot="action2" slot-scope="text, record, index, columns">
            <a @click="openSelectLuo(record)" :disabled="!(record.code && record.code.length > 0)" style="text-align: center">选择摞</a>
          </template>

          <template slot="type" slot-scope="text, record, index, columns">
            <a-select style="width: 100%;height: 100%;border: 0;" v-model="record.type" @change="changeXshType"
                      :dropdownStyle="{fontSize: '4px'}"
                      :options="xshOptions"></a-select>
          </template>

          <template slot="code" slot-scope="text, record, index, columns">
            <p style="margin: 0" v-for="item in text">{{item}}</p>
          </template>

        </a-table>


      </a-modal>
      <a-modal
        :maskClosable="false"
        width="80%"
        title="设置进箱位置"
        :visible="isShowPositionsSafe"
        @ok="xshOkSafe(false)"
        @cancel="isShowPositionsSafe = false"
      >

        <a-button @click="addXshSafe" style="margin-right: 20px" >新增</a-button>
        <a-popconfirm
          title="确认删除"
          ok-text="确认"
          cancel-text="取消"
          @confirm="progress.testAddress == 'R3' || progress.testAddress == 'JM'?deleteXshSafe():deleteXsh()"
        >
          <a-button>删除</a-button>
        </a-popconfirm>


        <a-table
          :columns="safeXshColumns"
          :dataSource="xshData"
          class="mt10"
          bordered
          :rowKey="record => record.index"
          :pagination="false"
          :rowSelection="{
							selectedRowKeys: xshSelectedRowKeys, onChange: onXshSelectChange
						}"
        >
          <template slot="action" slot-scope="text, record, index, columns">
            <a @click="openSelectSampleData(record)" style="text-align: center" >选择电芯</a>
          </template>

          <template slot="action2" slot-scope="text, record, index, columns">
            <a @click="openSelectLuoSafe(record)" :disabled="!(record.code && record.code.length > 0)" style="text-align: center">选择温箱</a>
          </template>

          <template slot="code" slot-scope="text, record, index, columns">
            <p style="margin: 0" v-for="item in text">{{item}}</p>
          </template>

        </a-table>


      </a-modal>

      <a-modal
        :maskClosable="false"
        width="30%"
        title="选择电芯"
        :visible="xshSelectSampleVisible"
        @ok="handleSelectSampleOk"
        @cancel="handleSelectSampleCancel"
      >
        <a-table
          class="mt10"
          bordered
          style="width: 80%;"
          :columns="selectSampleColumns"
          :rowKey="record => record.cellTestCode"
          :data-source="firstData.filter(f => 'ongoing' == f.batteryStatus).filter(f => f.inBoxPositionId == xshSelectIndex || f.inBoxPositionId == null)"
          :pagination="false"
          :row-selection="{
								selectedRowKeys: cellSelectedRowKeys,
								onChange: progress.testAddress == 'R3' || progress.testAddress == 'JM'?cellOnSelectSafe:cellOnSelect,
								columnWidth: 20
							}"
        >
        </a-table>

      </a-modal>
      <a-modal title="存放电芯" width="50%" :visible="visible3" :footer="null"
               @cancel="() => visible3 = false">
        <a-row :gutter="[4,4]">
          <a-col :span="6">
            <a-form-item label="产品名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-model="detailQueryparam.productName" @keyup.enter="getInOutDetailList" @change="getInOutDetailList"/>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="委托单号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-model="detailQueryparam.folderNo" @keyup.enter="getInOutDetailList" @change="getInOutDetailList"/>
            </a-form-item>
          </a-col>


        </a-row>
        <a-table :columns="luoInOutDetail" :data-source="batteryInOutData" :rowKey="(record) => record.id" bordered
        >

          <template slot="sampleCodeList" slot-scope="text, record, index, columns">
            <p style="margin: 0" v-for="item in text">{{item}}</p>
          </template>

        </a-table>

      </a-modal>
      <a-modal
        :maskClosable="false"
        width="60%"
        title="选择摞"
        :visible="selectLuoVisible"
        @ok="handleLuoPositionOk"
        @cancel="selectLuoVisible = false"
      >
        <a-row :gutter="[8, 8]">
          <a-col :span="10">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="温箱编号">
              <a-input
                v-model="luoQueryparam.code"
                @keyup.enter="refreshCanInLuoData"
                @change="refreshCanInLuoData"
              />
            </a-form-item>
          </a-col>
          <a-col :span="10">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="温箱型号">
              <a-input
                v-model="luoQueryparam.model"
                @keyup.enter="refreshCanInLuoData"
                @change="refreshCanInLuoData"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-table
          bordered
          :data-source="canInLuoData"
          :columns="canInLuoColumns"
          :rowKey="record => record.id"
          :row-selection="{
						type: 'radio',
						selectedRowKeys: positionSelectedRowKeys,
						selectedRows: positionSelectedRows,
						onChange: positionOnSelect,
						columnWidth: 20
					}"
        >

          <template
            slot="inXshTpFxRbNum"
            slot-scope="text, record, index, columns"
          >
            <a v-if="text > 0" @click="openDetail(record)">{{text}}</a>

            <span v-else>{{text}}</span>


          </template>



        </a-table>
        <p style="
        position: absolute;
        margin-top: -85px;
        z-index: 2;">提示：当可选择项与实际存储温箱不一致时，请暂停操作，并联系相应组长整合信息反馈给黄辉宁处理</p>
      </a-modal>
      <a-modal
        :maskClosable="false"
        width="60%"
        title="选择温箱"
        :visible="selectLuoVisibleSafe"
        @ok="handleLuoPositionOkSafe"
        @cancel="selectLuoVisibleSafe = false"
      >
        <a-row :gutter="[8, 8]">
          <a-col :span="10">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="温箱编号">
              <a-input
                v-model="luoQueryparam.code"
                @keyup.enter="refreshCanInLuoData"
                @change="refreshCanInLuoData"
              />
            </a-form-item>
          </a-col>
          <a-col :span="10">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="温箱型号">
              <a-input
                v-model="luoQueryparam.model"
                @keyup.enter="refreshCanInLuoData"
                @change="refreshCanInLuoData"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-table
          bordered
          :data-source="canInLuoData"
          :columns="progress.testAddress == 'R3'?safeCanInLuoColumns:jmCanInLuoColumns"
          :rowKey="record => record.id"
          :row-selection="{
						type: 'radio',
						selectedRowKeys: positionSelectedRowKeys,
						selectedRows: positionSelectedRows,
						onChange: positionOnSelect,
						columnWidth: 20
					}"
        >

          <template
            slot="inXshTpFxRbNum"
            slot-scope="text, record, index, columns"
          >
            <a v-if="text > 0" @click="openDetail(record)">{{text}}</a>

            <span v-else>{{text}}</span>


          </template>



        </a-table>
      </a-modal>
		</div>
		<!-- 测试数据选择弹窗 start  -->
		<div>
			<a-modal
				title="测试数据选择"
				width="90%"
				:height="300"
				:bodyStyle="{ padding: 0 }"
				:visible="mgVisible"
				style="padding: 0"
				:maskClosable="false"
				:centered="true"
				@cancel="handleCloseModal"
				destroyOnClose
			>
				<div class="child-table">
					<a-table
						:columns="mgColumns"
						:dataSource="mgData"
						class="mt10"
						bordered
						:rowKey="record => record.flowId"
						:pagination="false"
						:rowSelection="{
							type: 'radio',
							onSelect: selectTestData,
							getCheckboxProps: getCheckboxProps
						}"
					>
						<template slot="celltestcode" slot-scope="text, record, index, columns">
							<a @click="openStepData(record)" style="text-align: center">{{ text }}</a>
						</template>
					</a-table>
				</div>
				<template slot="footer" slot-scope="text, record">
					<a-button key="back" @click="handleCloseModal">
						关闭
					</a-button>
				</template>
			</a-modal>
		</div>

		<!-- 测试数据选择弹窗 end  -->
    <!-- 预览视频/图片  -->
      <a-drawer
        :bodyStyle="{ height: '100%' }"
        placement="right"
        :closable="false"
        width="70%"
        :visible="filePreviewVisible"
        @close="filePreviewVisible = false"
      >
        <iframe :src="iframeUrl" width="100%" height="100%"></iframe>
      </a-drawer>

      <div>
        <a-modal :visible="previewVisible" :footer="null" @cancel="handlePreviewCancel">
          <img alt="example" style="width: 100%" :src="previewImage"/>
        </a-modal>
      </div>

	</a-modal>
</template>

<script>
import moment from "moment"
import { debounce } from "@/utils/util"
import { STable } from "@/components"
import { mixin } from "../mixin/index"

import { getTestProDetailByTaskId } from "@/api/modular/system/testProgressManager"
import {earlyWarningCalendarCheck} from "@/api/modular/system/testAbnormalManager";

export default {
	components: {
		STable
	},
	name: "CalendarAroundModal",
	mixins: [mixin],
	created() {
		this.getTestProDetailByTaskId()
		this.getTestProgress()
		this.positionLoadData()



		// document.getElementById('connectBtn').click()
	},
	mounted() {
		this.$nextTick(() => {
			console.log(document.getElementById('connectBtn'))
		})

	},
	methods: {
		getTestProDetailByTaskId(current = 0) {
			this.modalLoading = true
			getTestProDetailByTaskId(this.modalData.ordTaskId)
				.then(res => {
					if (!res.success) return this.$message.error("错误提示：" + err.message)

					// 原始数据：详情信息使用
					this.originalData = res.data
          this.pictureOrVideo = this.originalData.picture === '1' || this.originalData.video === '1'

          if(res.data.boxInfo != null){
            this.xshData = JSON.parse(res.data.boxInfo)
          }

					// 设置弹窗处于那个页面
					if (this.modalData.taskStatus !== "已完成" && current === 0) {
						this.current = res.data.currentStep ? Number(res.data.currentStep) : current
					}

					// 设置大小中检是否被点击
					res.data.lifeTestRecordDataMap.forEach(v => {
            // 图片预览增加token
            if (v.samplePicture) {
              Object.keys(v.samplePicture).forEach(function(key) {
                if (v.samplePicture[key].thumbUrl) {
                  v.samplePicture[key].thumbUrl += "&Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************.P1JWgtRVk1sTPPLiCgZNuleYyPZRf2ooByC_mmu9scs6SVbpJHgSsKd8AtscjDwg3Fw7D4QN31vgtA5jeedj3g"
                }
              });
            }
            if (v.isMiddleClick === null || v.isMiddleClick === undefined || v.isMiddleClick === '') {
              v.isMiddleClick =
                (v.middleCheck !== "normal" && v.checkData) ||
                (v.middleCheck !== "normal" &&
                  res.data.currentStep !== null &&
                  this.modalData.taskType.indexOf("last") === -1) ||
                this.modalData.taskStatus === "已完成"
                  ? true
                  : false

              //方便后续校验
              if (v.middleCheck === "normal") v.isMiddleClick = true
            }
					})

					// 首次中检数据
					this.firstData = res.data.lifeTestRecordDataMap
					this.firstColums = this.handleColums(res.data.lifeTestRecordDataMap)[0]
					this.firstList = this.handleColums(res.data.lifeTestRecordDataMap)[1]

					// 最后一次中检数据
					this.lastData = res.data.lifeTestRecordDataMap
					this.lastColums = this.handleColums(res.data.lifeTestRecordDataMap)[0]
					this.lastList = this.handleColums(res.data.lifeTestRecordDataMap)[1]

          this.picOrVidColumn = this.handlePicOrVidColums(res.data.lifeTestRecordDataMap)[0]
          this.picOrVidList = this.handlePicOrVidColums(res.data.lifeTestRecordDataMap)[1]
          // console.log('this.firstData',this.firstData)
          // console.log('this.firstColums',this.firstColums)
          // console.log('this.firstList',this.firstList)
          // console.log('this.picOrVidColumn',this.picOrVidColumn)
          // console.log('this.picOrVidList',this.picOrVidList)
          // console.log('this.originalData.lifeTestRecordDataMap',this.originalData.lifeTestRecordDataMap)

					// 中检类型
					this.middleCheck = this.originalData.lifeTestRecordDataMap[0].middleCheck

					// 进箱时间
					this.actualInDate = res.data.actualInDate
						? moment(res.data.actualInDate, "YYYY-MM-DD")
						: this.actualInDate

					//出箱时间
					this.actualOutDate = res.data.actualOutDate
						? moment(res.data.actualOutDate, "YYYY-MM-DD")
						: this.actualOutDate

					// 进箱时间
					// if (!res.data.actualInDate && this.modalData.taskType === "rlsmcs_first" && this.current === 1) {
					// 	this.handleChangeDate(String(moment(new Date()).format("yyyy-MM-DD")))
					// }

					// 出箱时间
					// if (!res.data.actualOutDate && this.modalData.taskType === "rlsmcs_last" && this.current === 0) {
					// 	this.handleChangeDate(String(moment(new Date()).format("yyyy-MM-DD")), "out")
					// }
				})
				.finally(() => {
          earlyWarningCalendarCheck({id : this.modalData.id}).then(res =>{
            this.abnormalList = res.data.abnormalList
            this.isAbnormal = res.data.isAbnormal
            this.abnormalStatus = res.data.abnormalStatus
            this.isEngineerConfirmNotChange = res.data.isEngineerConfirmNotChange
          })
					this.modalLoading = false

					//第一个阶段时， 首次中检
					if(this.modalData.taskType.indexOf('first') !== -1 && this.current === 0){
						this._handleMiddleCheck()
					}
				})
		},

		// 处理表头
		handleColums(value) {
			let temColuns = []
			const temList = []

			for (let i in value[0]) {
				const temObj = {
					title: this.tableNameMenu[i],
					dataIndex: i,
					align: "center",
					width: "82px",
					scopedSlots: {
						customRender: i
					}
				}

				const childrenObj = {
					title: i.replaceAll(/[^0-9]/g, ""),
					dataIndex: i,
					align: "center",
					width: "82px",
					scopedSlots: {
						customRender: i
					}
				}

				const childrenObj1 = {
					title: this.tableNameMenu[i.replaceAll(/[0-9]/g, "")] + i.replaceAll(/[^0-9]/g, ""),
					dataIndex: i,
					width: "82px",
					align: "center",
					scopedSlots: {
						customRender: i
					}
				}

				const temObj1 = {
					title: this.tableNameMenu[i.replaceAll(/[0-9]/g, "")],
					dataIndex: i.replaceAll(/[0-9]/g, ""),
					align: "center",
					width: "82px",
					children: [childrenObj]
				}

				if (
					(i === "middleCheck" && value[0][i] === "normal") ||
					i === "checkData" ||
					i === "isMiddleClick" ||
					i === "heightType" ||
					i === "inBoxPositionId" ||
					i === "timeOfFillInnerres" ||
					i === "timeOfFillInnerres2" ||
					i === "samplePicture" ||
					i === "inBoxPosition"
				)
					continue

				if (i.match(/\d/g)) {
					const temIndex = temColuns.findIndex(v => v.dataIndex === i.replaceAll(/[0-9]/g, ""))
					if (temIndex === -1) {
						temColuns.push(temObj1)
						temList.push(childrenObj1)
					} else {
						temColuns[temIndex].children.push(childrenObj)
						temList.push(childrenObj1)
					}
				} else {
					temColuns.push(temObj)
					temList.push(temObj)
				}
			}
			const chilList = []
			const temColuns1 = []
			// 划分尺寸表头
			temColuns.forEach(v => {
				if (v.dataIndex === "timeOfFillInnerres" || v.dataIndex === "timeOfFillInnerres2" || v.dataIndex === "samplePicture") {
					return
				}
				if (
					v.dataIndex === "cellTestCode" ||
					v.dataIndex === "alias" ||
					v.dataIndex === "middleCheck" ||
          v.dataIndex === "batteryStatus" ||
					v.dataIndex === "beforeVoltage" ||
					v.dataIndex === "beforeInnerres" ||
					v.dataIndex === "afterVoltage" ||
					v.dataIndex === "afterInnerres" ||
					v.dataIndex === "volume" ||
					v.dataIndex === "weight" ||
					v.dataIndex === "heightType" ||
					v.dataIndex === "inBoxPositionId" ||
					v.dataIndex === "isolateres"
				) {
					if (v.dataIndex === "alias" || v.dataIndex === "isolateres" || v.dataIndex === "batteryStatus") {
						v.width = "100px"
					}
					if (v.dataIndex === "cellTestCode") {
						v.width = "175px"
					}
					return temColuns1.push(v)
				}
				chilList.push(v)
			})

			temColuns = temColuns1

			if (chilList.length !== 0) {
				temColuns.push({
					title: "尺寸/mm",
					dataIndex: "dimension",
					align: "center",
					children: chilList
				})
			}
      // 获取【中检类型】列索引
      let insertIndexC = temColuns.findIndex(item => item.dataIndex === 'middleCheck');
      let insertIndexL = temList.findIndex(item => item.dataIndex === 'middleCheck');
      if (insertIndexC === -1 || insertIndexL === -1) {
        // 无【中检类型】列则获取【测试编码】列索引
        insertIndexC = temColuns.findIndex(item => item.dataIndex === 'cellTestCode');
        insertIndexL = temList.findIndex(item => item.dataIndex === 'cellTestCode');
      }
      if (insertIndexC !== -1 && insertIndexL !== -1) {
        // 获取【电芯状态】列
        let batteryTitleC = temColuns.filter(item => item.dataIndex === 'batteryStatus');
        let batteryTitleL = temList.filter(item => item.dataIndex === 'batteryStatus');
        // 移除【电芯状态】列
        let indexToRemoveC = temColuns.findIndex(item => item.dataIndex === 'batteryStatus');
        let indexToRemoveL = temList.findIndex(item => item.dataIndex === 'batteryStatus');
        if (indexToRemoveC !== -1) {
          temColuns.splice(indexToRemoveC, 1);
        }
        if (indexToRemoveL !== -1) {
          temList.splice(indexToRemoveL, 1);
        }
        // 将【电芯状态】列添加到【中检类型】/【测试编码】列后面
        if (batteryTitleC.length > 0 && batteryTitleL.length > 0) {
          temColuns.splice(insertIndexC + 1, 0, batteryTitleC[0]);
          temList.splice(insertIndexL + 1, 0, batteryTitleL[0]);
        }
      }
			return [temColuns, temList]
		},

		// 上一步/下一步
		async handleStep(index) {

			//最后一个阶段时， 由出箱点击下一步进入出箱中检时
			if(this.modalData.taskType.indexOf('last') !== -1 && this.current === 0 && index > 0){
        if (!this.actualOutDate) {
          this.$message.warn('出箱时间未填写')
          return
        }
				this._handleMiddleCheck()
			}

      //最后一个阶段时， 由出箱中检点击下一步进入拍照时
      if(this.modalData.taskType.indexOf('last') !== -1 && this.current === 1 && index > 0){
        // 中检是否完成
        const isMiddleCheckOk = this.lastData.findIndex(v => !v.isMiddleClick && v.batteryStatus === 'ongoing') === -1
        // 数据是否填写完成
        const isDataCheckOk = await this._handleIsNull(this.lastData)
        // 校验
        //提醒功能 ：(中检 ：√ && 数据 ：√ ) || 尺寸数据 ： ×
        if (isMiddleCheckOk && isDataCheckOk[0] && !isDataCheckOk[1]) {
          return this.$warning({
            content: "请先将尺寸数据填写完整在进行下一步"
          })
        }
        // 中检 ：× || 数据 ：×
        if (!isMiddleCheckOk || !isDataCheckOk[0]) {
          const temList = JSON.parse(JSON.stringify(this.lastData))
          this._handleSetBGC(temList, "last")
          return this.$warning({
            content:
              this.middleCheck !== "normal"
                ? "请先完成中检或将数据填写完整在进行下一步"
                : "请先将数据填写完整在进行下一步"
          })
        }
        this.current += index
        this.updateTestProDetail({
          id: this.modalData.ordTaskId,
          currentStep: this.current
        })
        return
      }

			//区分阶段 ：最后阶段 / 数据完成 /上一步
			if (this.modalData.taskType.indexOf("last") !== -1 || this.modalData.taskStatus === "已完成" || index < 0) {
				this.current += index
				this.updateTestProDetail({
					id: this.modalData.ordTaskId,
					currentStep: this.current
				})
				return
			}

			//区分阶段 ：第一阶段
			if (this.modalData.taskType.indexOf("first") !== -1) {
				// 中检是否完成
				const isMiddleCheckOk = this.firstData.findIndex(v => !v.isMiddleClick && v.batteryStatus === 'ongoing') === -1
				// 数据是否填写完成
				const isDataCheckOk = await this._handleIsNull(this.firstData)

				// 校验
				//提醒功能 ：(中检 ：√ && 数据 ：√ ) || 尺寸数据 ： ×
				if (isMiddleCheckOk && isDataCheckOk[0] && !isDataCheckOk[1]) {
          return this.$warning({
            content: "请先将尺寸数据填写完整在进行下一步"
          })
				}

				// 中检 ：× || 数据 ：×
				if (!isMiddleCheckOk || !isDataCheckOk[0]) {
					const temList = JSON.parse(JSON.stringify(this.firstData))
					this._handleSetBGC(temList, "first")
					return this.$warning({
						content:
							this.middleCheck !== "normal"
								? "请先完成中检或将数据填写完整在进行下一步"
								: "请先将数据填写完整在进行下一步"
					})
				}

        if (this.pictureOrVideo && this.current === 1) {
          let noFillVideo = this.validPictureAndVideo()[0]
          let noFillPicture = this.validPictureAndVideo()[1]
          if (noFillVideo) {
            return this.$warning({
              content: "请先上传视频再进行下一步"
            })
          }
          if (noFillPicture) {
            return this.$warning({
              content: "每个电芯至少需要上传一张照片"
            })
          }
        }

				this.current += index
				this.updateTestProDetail({
					id: this.modalData.ordTaskId,
					currentStep: this.current
				})

				// 如果进箱时间为空，则设置进箱时间为今天
				// if (!this.originalData.actualInDate) this.handleChangeDate(String(moment(new Date()).format("yyyy-MM-DD")))
			}
		},
		/**
		 * 附件事件
		 */
		handleUploadFile(info) {
			this.fileList = [...info.fileList]
			if (info.file.response.success) {
				this.getTestProDetailByTaskId(this.current)
				this.$message.success(`${info.file.name} 数据导入成功`)
			} else {
				this.$message.error(`${info.file.name} 数据导入失败:` + info.file.response.message)
			}
			this.$forceUpdate()
		}
	}
}
</script>

<style lang="less" scoped>
@import "../style/calendar.less";
/deep/.ant-table-pagination.ant-pagination {
  float: right;
  margin: 50px 0px 20px 0px;
}
/deep/.ant-upload-list-picture-card .ant-upload-list-item {
  width: 80px;
  height: 80px;
  margin: 0px 0px 0px 0px;
}
/deep/.ant-upload-list-picture-card-container {
  width: 80px;
  height: 70px;
  margin: 0px 0px 7px 0px;
  overflow-wrap: break-word;
}
/deep/.ant-upload-picture-card-wrapper {
  zoom: 1;
  display: ruby;
}
</style>
