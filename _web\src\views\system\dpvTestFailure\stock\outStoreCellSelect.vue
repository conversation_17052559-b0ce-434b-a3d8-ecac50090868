<template>
  <a-modal title="出库电芯选择" :width="800" :visible="visible" :confirmLoading="loading" @ok="handleSubmit" @cancel="handleCancel">
    <a-spin :spinning="loading">

      <div >
        <a-form :form="form">
<!--          <a-form-item-->
<!--            :labelCol="labelCol"-->
<!--            :wrapperCol="wrapperCol"-->
<!--            label="测试周期配置">-->
<!--            <a-cron ref="innerVueCron" v-decorator="['cronExpression', {'initialValue':cronDict.value,rules:-->
<!--          [{ required: true, message: '请输入测试周期，使用cron表达式!' }]}]" @change="setCorn"></a-cron>-->
<!--          </a-form-item>-->
          <a-form-item
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            label="出库电芯">
            <a-select
              v-decorator="['outStoreCellList', {rules: [{ required: true, message: '请选择出库电芯!' }]}]"
              placeholder="请选择出库电芯"
              style="width: 100%"
              allowClear
              mode="multiple"
            >
              <a-select-option v-for="(item,index) in recordStoreCellList" :key="item.id" :value="item.id">{{item.cellCode}}</a-select-option>
            </a-select>
<!--            <a-input v-decorator="['tester', {rules: [{ required: true, message: '请填写测试人员，规则：[工号,姓名]!' }]}]" placeholder="请填写测试人员，规则：[工号,姓名]" />-->
          </a-form-item>

        </a-form>
      </div>
    </a-spin>
  </a-modal>

</template>

<script>
import {clamp, STable} from '@/components'
import moment from "moment";
import { Pagination } from 'ant-design-vue';
import ACron from "@/components/Acron";
import Vue from "vue";
import {DICT_TYPE_TREE_DATA} from "@/store/mutation-types";
import {
  failureCellGetOcvConfigDict,
  failureCellOutStore,
  failureCellUpdateOcvCron
} from "@/api/modular/system/testFailure";
import {sysDictDataPage} from "@/api/modular/system/dictDataManage";
export default {
  components: {
    clamp,
    STable,
    ACron,
    'a-pagination': Pagination,
  },
  data() {
    return {
      cronDict: {value: '0 0 0 2 * ?'},
      testerDict:'',
      formLayout: 'horizontal',
      form: this.$form.createForm(this, { name: 'coordinated' }),
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 },
        md: { span: 5 },
        lg: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 14 },
        sm: { span: 14 },
        md: { span: 14 },
        lg: { span: 14 },
      },

      recordDate: moment(),
      queryParam:{},
      tableData:[],

      visible: false,
      loading: false,
      recordStoreCellList: [],
      outStoreCellList: [],
      //form: this.$form.createForm(this)


    }
  },
  methods: {
    handleSubmit (e) {
      // e.preventDefault()
      this.form.validateFields((err, values) => {
        if (!err) {
          console.log('Received values of form: ', values)

          failureCellOutStore({idList:values.outStoreCellList}).then(res=>{
            if (res.success) {
              this.$message.success('流程发起成功，请前往JIRA查看')
              // this.$refs.table.refresh()
              // this.getData(this.queryParam);
            } else {
              this.$message.error('流程发起失败：' + res.message)
            }
          }).finally((res) => {
            this.visible = false;
          })



          // if (values.tester === undefined || values.tester.indexOf(",") == -1) {
          //   this.$message.warn("请按规则填写测试人配置，[工号,姓名]");
          // }
          // //后端验证cron表达式是否符合规则，不符合返回错误，符合保存字典
          // failureCellUpdateOcvCron({id: this.cronDict.id, value: values.cronExpression}).then(res => {
          //   if(res.success){
          //     if (res.data == true) {
          //       this.$message.success("保存成功");
          //       this.visible = false
          //     }else {
          //       this.$message.error("cron表达式校验失败，请重新填写");
          //     }
          //   }else {
          //     this.$message.error(res.message)
          //   }
          // });

        }
      })
    },
    setCorn(data){
      if(data !== undefined){
        this.$nextTick(() => {
          this.form.setFieldsValue({cronExpression: data})
        })
      }
    },
    show(recordStoreCellList){
      this.recordStoreCellList = recordStoreCellList;
      this.visible = true
    },
    getDict(code) {
      const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
      return dictTypeTree.filter(item => item.code == code)[0].children
    },
    handleCancel() {
      this.tableData = []
      this.form.resetFields()
      this.visible = false
    },

  }
}
</script>
<style lang="less" scoped=''>
/deep/ .ant-modal-body{
  padding: 12px !important;
}
/deep/.ocvTable .ant-table-body {
  height: initial !important;
  overflow-y: scroll;
}
/deep/.ocvTable .ant-table-placeholder{
  display: none !important;
}
</style>