<template>
  <base-chart ref="baseChart" :width="width" :height="height">
    <canvas ref="canvas"></canvas>
  </base-chart>
</template>

<script>
import Chart from 'chart.js/auto';
import BaseChart from './BaseChart.vue';

export default {
  name: 'LineChart',
  components: { BaseChart },
  props: {
    chartData: {
      type: Object,
      required: true
    },
    chartOptions: {
      type: Object,
      default: () => ({})
    },
    width: {
      type: [String, Number],
      default: '100%'
    },
    height: {
      type: [String, Number],
      default: '300px'
    }
  },
  data: () => ({ chart: null }),
  mounted() {
    this.renderChart();
  },
  methods: {
    renderChart() {
      if (this.chart) this.chart.destroy();

      const ctx = this.$refs.canvas.getContext('2d');
      this.chart = new Chart(ctx, {
        type: 'line',
        data: this.chartData,
        options: {
          maintainAspectRatio: false,
          ...this.chartOptions
        }
      });
    }
  },
  watch: {
    chartData: {
      handler() {
        this.$nextTick(this.renderChart);
      },
      deep: true
    }
  },
  beforeDestroy() {
    if (this.chart) this.chart.destroy();
  }
};
</script>

<style scoped>
canvas {
  width: 100%;
  height: 100%;
}
</style>