<template>
	<div class="container">
		<div class="content-wrapper">
			<div v-for="(item, index) in titleMenu" :key="index" class="content">
				<div class="title">{{ item.title }}</div>
				<div class="tabs">
					<a-tabs :activeKey="item.activeKey" @change="activeKey => handleChangeTab(activeKey, index)">
						<a-tab-pane v-for="(chilItem, chilIndex) in item.children" :key="chilIndex" :tab="chilItem">
							<div class="tab">
								<a-tree
									:tree-data="treeData"
									show-icon
									default-expand-all
									:default-selected-keys="['0-0-0']"
									@select="selectedKeys => handleTreeSelect(selectedKeys, index, chilIndex)"
								>
									<a-icon slot="switcherIcon" type="down" />
								</a-tree>
							</div>
						</a-tab-pane>
					</a-tabs>
				</div>
			</div>
		</div>
	</div>
	<!-- <div style="background-color: #FFFFFF;display: flex;padding-top: 10%;
  flex-direction: column;align-items: center;" id="inside">
    <div>
      <div style="float: left;width:33.3%">
        <div class="big_title">G圆柱</div>
        <div class="sub_title"><a @click="gotoMI()">MI规范库</a></div>
        <div class="sub_title"><a>电化学体系库</a></div>
        <div class="sub_title_last"><a>结构设计库</a></div>
      </div>
      <div style="float: left;width:33.3%">
        <div class="big_title">C圆柱</div>
        <div class="sub_title"><a>MI规范库</a></div>
        <div class="sub_title"><a>电化学体系库</a></div>
        <div class="sub_title_last"><a>结构设计库</a></div>
      </div>
      <div style="float: left;width:33.3%">
        <div class="big_title">V圆柱</div>
        <div class="sub_title"><a>MI规范库</a></div>
        <div class="sub_title"><a>电化学体系库</a></div>
        <div class="sub_title_last"><a>结构设计库</a></div>
      </div>
      <div style="float: left;width:33.3%">
        <div class="big_title">方形叠片</div>
        <div class="sub_title"><a>MI规范库</a></div>
        <div class="sub_title"><a>电化学体系库</a></div>
        <div class="sub_title"><a>结构设计库</a></div>
      </div>
      <div style="float: left;width:33.3%">
        <div class="big_title">方形卷绕</div>
        <div class="sub_title"><a>MI规范库</a></div>
        <div class="sub_title"><a>电化学体系库</a></div>
        <div class="sub_title"><a>结构设计库</a></div>
      </div>
      <div style="float: left;width:33.3%">
        <div class="big_title">软包</div>
        <div class="sub_title"><a>MI规范库</a></div>
        <div class="sub_title"><a>电化学体系库</a></div>
        <div class="sub_title"><a>结构设计库</a></div>
      </div>


    </div>

  </div> -->
</template>
<script>
export default {
	components: {},
	data() {
		return {
			titleMenu: [
				{
					title: "圆柱",
					activeKey: 0,
					children: ["G圆柱", "C圆柱", "V圆柱"]
				},
				{
					title: "方形",
					activeKey: 0,
					children: ["卷绕", "叠片"]
				},
				{
					title: "软包",
					activeKey: 0,
					children: ["叠片"]
				}
			],
			treeData: [
				{
					title: "正负极材料体系开发",
					key: "0-0"
				},
				{
					title: "电极技术开发",
					key: "0-1",
					children: [
						{ title: "箔材选型", key: "0-1-0" },
						{ title: "粘结剂选型", key: "0-1-1" },
						{ title: "导电剂选型", key: "0-1-2" },
						{ title: "电极功能添加剂", key: "0-1-3" }
					]
				},
				{
					title: "功能开发与标定",
					key: "0-2",
					children: [
						{ title: "功能电解液选型", key: "0-2-0" },
						{ title: "功能隔膜选型", key: "0-2-1" },
						{ title: "功能箔材选型", key: "0-2-2" }
					]
				},
				{
					title: "结构设计库",
					key: "0-3"
				},
				{
					title: "MI设计与组合",
					url: "/g_cylinder_mi_library",
					key: "0-4"
				},
				{
					title: "包装设计库",
					key: "0-5"
				}
			]
		}
	},
	created() {},
	mounted() {
		// document.getElementById("inside").parentElement.style.background = "#FFFFFF"
	},

	methods: {
		gotoMI() {
			this.$router.push("/g_cylinder_mi_library")
		},

		// tab改变事件
		handleChangeTab(key, index) {
			this.titleMenu[index].activeKey = key
		},

		// 树形节点点击事件
		handleTreeSelect(selectedKeys, index, chilIndex) {
			if (selectedKeys.length) {
				const keyArr = selectedKeys[0].split("-")
				const url =
					keyArr.length === 2 ? this.treeData[keyArr[1]].url : this.treeData[keyArr[1]].children[keyArr[2]].url
				// 当前只有g圆柱有数据
				if (url && index === 0 && chilIndex === 0) {
					this.$router.push(url)
				}
			}
		}
	}
}
</script>
<style lang="less" scoped>
.big_title {
	font-size: xx-large;
	font-weight: bolder;
	color: black;
	padding-bottom: 1.5625vw;
}

.sub_title {
	font-size: large;
	padding-bottom: 0.7813vw;
}
.sub_title_last {
	font-size: large;
	padding-bottom: 2.3438vw;
}

// xiaodong

.container {
	display: flex;
	height: calc(100vh - 40px);
}
.content-wrapper {
	width: 100%;
	display: flex;
	justify-content: space-around;
}

.content-wrapper .content {
	display: flex;
	flex-direction: column;
	width: 28%;
	padding: 0.7813vw;
	margin: 1.9531vw 0;
	background-color: rgba(255, 255, 255, 1);
	border-radius: 0.7813vw;
}

.title {
	text-align: center;
	font-size: 1.5625vw;
	font-weight: 600;
}

.tab {
	font-weight: 500;
}

// tabs样式修改
/deep/.ant-tabs-nav .ant-tabs-tab {
	padding: 0.3906vw 1.25vw;
}

/deep/.ant-tabs-bar {
  margin: 0 0 .7813vw 0;
}

/deep/.ant-tree {
	font-size: 1.0938vw;
}

/deep/.ant-tabs-nav-container {
	font-size: 1.0938vw;
}
</style>
