<template>
  <div class="prediction-panel">
    <!-- 模型信息和返回按钮 -->
    <div class="model-header">
      <a-button type="link" icon="arrow-left" @click="goBackToSelection">返回选择</a-button>
      <h3 class="model-title">{{ selectedModel.id }}</h3>
    </div>

    <a-divider />

    <!-- LaTeX公式显示组件 -->
    <formula-display
      :main-formula="mainFormula"
      :sub-formulas="subFormulas"
    />

    <!-- 模型信息面板组件 -->
    <model-info-panel
      :variables="variables"
      :coefficients="coefficients"
      :variable-description="selectedModel.variable_description"
    />

    <!-- 标签页：拟合验证和预测曲线 -->
    <a-tabs defaultActiveKey="fitting" class="unified-tabs prediction-tabs validation-tabs">
      <!-- 拟合验证标签页 -->
      <a-tab-pane key="fitting" tab="拟合验证">
        <fitting-validation-tab
          :model-id="selectedModel.id"
          :main-formula="mainFormula"
          :model-description="selectedModel.description"
        />
      </a-tab-pane>

      <!-- 预测曲线标签页 -->
      <a-tab-pane key="prediction" tab="预测曲线">
        <prediction-curve-tab
          :model-id="selectedModel.id"
        />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import { renderMathJax } from '@/utils/mathUtils';
import predictionMixin from '@/mixins/predictionMixin';
import FormulaDisplay from './FormulaDisplay.vue';
import ModelInfoPanel from './ModelInfoPanel.vue';
import FittingValidationTab from './FittingValidationTab.vue';
import PredictionCurveTab from './PredictionCurveTab.vue';

export default {
  name: 'PredictionPanel',
  components: {
    FormulaDisplay,
    ModelInfoPanel,
    FittingValidationTab,
    PredictionCurveTab
  },
  mixins: [predictionMixin],
  props: {
    selectedModel: {
      type: Object,
      required: true
    }
  },
  computed: {
    // 分离系数和变量
    coefficients() {
      return (this.selectedModel.params || [])
        .filter(param => param.type === 'coefficient')
        .map(param => ({
          ...param,
          coefficientMode: param.coefficientMode || '自定义'
        }));
    },
    variables() {
      return (this.selectedModel.params || [])
        .filter(param => param.type === 'variable')
        .map(param => ({
          ...param
        }));
    },
    // 判断是否有子公式
    hasSubFormulas() {
      return this.selectedModel.latex &&
             this.selectedModel.latex.sub_formulas &&
             this.selectedModel.latex.sub_formulas.length > 0;
    },
    // 获取主公式
    mainFormula() {
      return this.selectedModel.latex && this.selectedModel.latex.main_formula
        ? this.selectedModel.latex.main_formula
        : '';
    },
    // 获取子公式数组
    subFormulas() {
      return this.selectedModel.latex && this.selectedModel.latex.sub_formulas
        ? this.selectedModel.latex.sub_formulas
        : [];
    }
  },
  methods: {
    goBackToSelection() {
      this.$emit('reset-selection');
    }
  },
  mounted() {
    // 在组件挂载后初始化LaTeX渲染
    this.$nextTick(() => {
      // 强制立即渲染一次MathJax，确保系数名称正确显示
      renderMathJax(true);
    });
  }
};
</script>

<style scoped>
.prediction-panel {
  padding: 16px 0;
}

.model-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.model-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 0 8px;
  color: #1890ff;
}

/* 标签页样式已统一到 formula.css 中 */
.prediction-tabs {
  margin-top: 16px;
}
</style>

<style>
/* LaTeX渲染样式 */
.latex-cell .MathJax_Display {
  margin: 0 !important;
}

.latex-cell .MathJax {
  display: inline-block !important;
}

/* 标签样式修复 */
.variable-content .ant-tag .MathJax {
  vertical-align: middle;
}

/* 高对比度模式支持已统一到 formula.css 中 */
</style>
