<template>
    <a-modal title="反馈历史" :width="800" :visible="visible" :confirmLoading="confirmLoading" @cancel="handleCancel">
        <template slot="footer">
                <a-button key="back" @click="handleCancel">
                      关闭
                </a-button>
        </template>
        <a-spin :spinning="confirmLoading">
            <s-table
                ref="table"
                :loading="confirmLoading"
                :columns="columns"
                :data="loadData"
                :rowKey="(record) => record.id"
                >
                <span slot="bomStatus" slot-scope="text, record">
                    {{mapStatus[record.bomStatus]}}
                </span>
                <template slot="lines" slot-scope="text, record">
                    <label v-for="(item, i) in record.lines" :key="i">
                        {{dataLines[item]}}；
                    </label>
                </template>
            </s-table>   
        </a-spin>
    </a-modal>
</template>

<script>
    import {
        STable
    } from '@/components'
    import {
        getFeedBackHistorys,
    } from "@/api/modular/system/feedbackManage"
    export default {
        components: {
            STable
        },
        props: {
        },
        data() {
            return {
                queryParam: {},
                confirmLoading: false,
                visible: false,
                loading: false,
                columns: [/* {
                        title: '序号',
                        dataIndex: 'index',
                        key: 'index',
                        align: 'center',
                        width: 50,
                        customRender: (text, record, index) => `${index+1}`,
                    }, */
                    {
                        title: '反馈内容',
                        dataIndex: 'feedbackContent',
                        width: 180,
                    },
                    {
                        title: '反馈人',
                        dataIndex: 'feedbackUserName',
                        width: 120,
                    },
                    {
                        title: '时间',
                        dataIndex: 'feedbackDate',
                        width: 100
                    },
                ],
                loadData: parameter => {
                    return getFeedBackHistorys(Object.assign(parameter, this.queryParam)).then((res) => {
                        return res.data
                    })
                },
            }
        },
        created() {},
        methods: {
            handleCancel() {
                this.queryParam = {}
                this.visible = false
            },
            view(record,type) {
                this.queryParam.issueId = record.issueId
                this.queryParam.feedbackType = type
                setTimeout(() => {
                    this.$refs.table.refresh()
                }, 100);
                this.visible = true
            },
        },
    }
</script>

<style>

</style>