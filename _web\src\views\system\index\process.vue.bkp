<template>
	<div class="container">
		<!-- Breadcrumb 面包屑 start -->
		<div>
			<a-breadcrumb class="breadcrumb" separator=">">
				<a-breadcrumb-item
					><a @click="gotoIndex(1)"
						><span class="" style="width: 100%; height: 100%; min-width: 14px; min-height: 14px; margin-right:4px"
							><svg
								xmlns="http://www.w3.org/2000/svg"
								class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 xuJzg svg-icon-path-icon fill"
								viewBox="0 0 48 48"
								width="14"
								height="14"
							>
								<defs data-reactroot=""></defs>
								<g>
									<rect width="48" height="48" fill="white" fill-opacity="0.01"></rect>
									<path
										fill-rule="evenodd"
										clip-rule="evenodd"
										d="M44 40.8361C39.1069 34.8632 34.7617 31.4739 30.9644 30.6682C27.1671 29.8625 23.5517 29.7408 20.1182 30.303V41L4 23.5453L20.1182 7V17.167C26.4667 17.2172 31.8638 19.4948 36.3095 24C40.7553 28.5052 43.3187 34.1172 44 40.8361Z"
										fill="none"
										stroke="#333"
										stroke-width="4"
										stroke-linejoin="round"
									></path>
								</g></svg></span
						>首页看板</a
					></a-breadcrumb-item
				>
				<a-breadcrumb-item>{{ $route.query.dept }}项目进展</a-breadcrumb-item>
			</a-breadcrumb>
		</div>
		<!-- Breadcrumb 面包屑 end -->

		<!-- 主标题 start -->
		<div class="head-title">{{ $route.query.dept || "动力电池研究院" }}</div>
		<!-- 主标题 end -->

		<div :style="`height:${windowHeight}px;`">
			<!-- 图表 start -->
			<div class="board">
				<div class="col1">
					<div class="item mr10">
						<strong class="head">项目进展分析</strong>
						<a-spin :spinning="loading">
							<div class="chart_table" ref="total"></div>
						</a-spin>
					</div>
				</div>
				<div class="col2">
					<div class="item">
						<strong class="head">进展异常分析</strong>
						<a-spin :spinning="loading">
							<div class="chart_table" ref="abnormal"></div>
						</a-spin>
					</div>
				</div>
			</div>
			<!-- 图表 end -->

			<!-- 表格 start -->
			<div class="table-wrapper">
				<a-table
					:rowKey="record => record.issueId"
					:pagination="pagination"
					:data-source="data"
					:columns="columns"
					:loading="loading"
					:style="`height:${tableHeight}px;`"
					size="middle"
				>
					<span slot="mstatus" slot-scope="text">
						{{ "product_stage_status" | dictType(text) }}
					</span>
					<span slot="state" slot-scope="text">
						{{ "product_state_status" | dictType(text) }}
					</span>
					<span slot="productPlannedDate" slot-scope="text, record">
						{{ record.productStageItems.filter(e => e.stage == record.mstatus).map(e => e.planReviewDate)[0] }}
					</span>
					<span slot="delayDays" slot-scope="text, record">
						{{ record.productStageItems.filter(e => e.stage == record.mstatus).map(e => e.overDays)[0] }}
					</span>
					<span slot="num" slot-scope="text, records, index">
						{{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
					</span>

					<!-- 项目进度 start -->
					<span slot="schedule" slot-scope="text, record">
						{{ record.state === 7 || record.state === 8 ? "停止" : record.delayDays > 0 ? "延期" : "正常" }}
					</span>
					<!-- 项目进度 end -->
				</a-table>
			</div>
			<!-- 表格 end -->
		</div>
	</div>
</template>

<script>
import { getProjectProcessDetail } from "@/api/modular/system/chartManage"
import _, { result } from "lodash"
import { all } from "q"
export default {
	data() {
		return {
			loading: false,
			windowHeight: document.documentElement.clientHeight - 64 - 30 - 40,
			tableHeight:
				document.documentElement.clientHeight - 64 - document.documentElement.clientHeight * 0.35 - 30 - 10 - 10 - 40,
			//column_map:[],
			projectProcessTotal: {},
			projectProcessTotalAxis: [],
			data: [],
			deptFilters: [],
			cateFilters: [],
			nameFilters: [],
			pdFilters: [],
			rpmFilters: [],
			custFilters: [],
			dateFilters: [],
			dayFilters: [],
			columns: [],

			delayMap: {},
			stopMap: {},
			depts: [],
			// 分页
			pagination: {
				current: 1,
				pageSize: 10,
				total: 0,
				//pageSizeOptions: ['10', '20', '50', '100'],
				showSizeChanger: true,
				showQuickJumper: true,
				onChange: (current, size) => {
					this.pagination.current = current
					this.pagination.pageSize = size
				},
				onShowSizeChange: (current, pageSize) => {
					this.pagination.current = 1
					this.pagination.pageSize = pageSize
				}
			}
		}
	},
	methods: {
		gotoIndex(index) {
			this.$router.push("/product_chart")
		},
		handleSearch(selectedKeys, confirm, dataIndex) {
			confirm()
		},

		handleReset(clearFilters) {
			clearFilters()
		},

		/**
		 * echarts
		 */

		initTotal() {
			let chart = this.echarts.init(this.$refs.total)
			chart.clear()

			let dataSum = [0, 0, 0, 0, 0]

			for (const k in this.projectProcessTotal) {
				for (let i = 0, j = this.projectProcessTotal[k].length; i < j; i++) {
					dataSum[i] += Number(this.projectProcessTotal[k][i])
				}
			}

			let dataRatio = []

			for (let i = 0, j = this.projectProcessTotal[1].length; i < j; i++) {
				dataRatio.push(dataSum[i] == 0 ? 0 : ((this.projectProcessTotal[1][i] / dataSum[i]) * 100).toFixed(1))
			}
			if (this.projectProcessTotalAxis.length === 0) {
				const options = {
					title: {
						text: "暂无数据",
						x: "center",
						y: "center",
						textStyle: {
							fontSize: 14,
							fontWeight: "normal"
						}
					}
				}
				chart.setOption(options)
				chart.resize()
			} else {
				const options = {
					legend: {
						itemWidth: 8,
						itemHeight: 8,
						bottom: "1%"
					},
					grid: {
						left: "3%",
						right: "4%",
						bottom: "12%",
						containLabel: true
					},
					xAxis: {
						type: "category",
						data: this.projectProcessTotalAxis
					},
					yAxis: [
						{
							show: false,
							type: "value"
						},
						{
							show: false,
							type: "value"
						}
					],
					series: [
						{
							name: "正常",
							type: "bar",
							stack: "total",
							label: {
								show: false,
								position: "top"
							},
							itemStyle: {
								color: "#5b9bd5"
							},
							barMaxWidth: "10%",
							data: this.projectProcessTotal[1]
						},
						{
							name: "延期",
							type: "bar",
							stack: "total",
							label: {
								show: false,
								position: "top"
							},
							itemStyle: {
								color: "#ed7d31"
							},
							barMaxWidth: "10%",
							data: this.projectProcessTotal[2]
						},
						{
							name: "停止",
							type: "bar",
							stack: "total",
							label: {
								show: false,
								position: "top"
							},
							itemStyle: {
								color: "#a5a5a5"
							},
							barMaxWidth: "10%",
							data: this.projectProcessTotal[3]
						},
						// 进度正常率
						{
							name: "进度正常率",
							type: "line",
							data: dataRatio,
							yAxisIndex: 1,
							itemStyle: {
								normal: {
									color: "#91cc75",
									label: {
										show: true,
										position: "top",
										formatter: (value, index) => {
											return value.data + "%"
										},
										textStyle: {
											color: "#000",
											fontSize: "8"
										}
									}
								}
							}
						}
					]
				}
				chart.setOption(options)
				chart.resize()
			}
		},
		async initNormal() {
			let chart = this.echarts.init(this.$refs.abnormal)
			let depts = Object.keys(this.stopMap)
			const color = ['#ed7d31','#a5a5a5']
			const hours = ["k0", "M1", "M2", "M3"]
			const days = Object.keys(this.stopMap)
			const title = []
			const singleAxis = []
			const series = []
			let data = []

			//数据处理
			await this._handleArray(this.delayMap, data, color[0])
			await this._handleArray(this.stopMap, data, color[1])

			// 排序
			data = data.sort(function(x, y) {
				return y[1] - x[1]
			})

			days.forEach(function(day, idx) {
				title.push({
					textBaseline: "middle",
					top: ((idx + 0.5) * 100) / 5 + 15 + "%",
					text: day
				})
				singleAxis.push({
					left: 170,
					type: "category",
					boundaryGap: false,
					data: hours,
					top: (idx * 100) / 5 + 15 + "%",
					height: 100 / 5 - 10 + "%"
				})
				series.push({
					singleAxisIndex: idx,
					coordinateSystem: "singleAxis",
					type: "scatter",
					data: [],
					symbolSize: function(dataItem) {
						return dataItem[1] * 4
					}
				})
			})

			// data.forEach(function(dataItem) {
			// 	series[dataItem[0]].data.push([dataItem[1], dataItem[2]])
			// })
			data.forEach(dataItem => {
				series[dataItem[0]].data.push({ value: [dataItem[1], dataItem[2]], itemStyle: { color: dataItem[3] } }) // 设置颜色属性
			})
			let options = {
				tooltip: {
					position: "bottom",
					formatter: function(data) {
						return `项目阶段:${data.name}\n${data.color === "#ed7d31" ? "延期天数:" + data.value[1] * 10 : "停止"}`
					}
				},
				title,
				singleAxis,
				series
			}

			// let options = {
			// 	tooltip: {
			// 		trigger: "axis",
			// 		axisPointer: {
			// 			type: "cross",
			// 			crossStyle: {
			// 				color: "#999"
			// 			}
			// 		}
			// 	},
			// 	legend: {
			// 		itemWidth: 8,
			// 		itemHeight: 8,
			// 		bottom: "0.5%",
			// 		type: "scroll" // 图例滚动
			// 	},

			// 	grid: {
			// 		left: "3%",
			// 		right: "4%",
			// 		bottom: "12%",
			// 		containLabel: true
			// 	},
			// 	xAxis: [
			// 		{
			// 			type: "category",
			// 			data: ["K0", "M1", "M2", "M3"],
			// 			axisPointer: {
			// 				type: "shadow"
			// 			}
			// 		}
			// 	],
			// 	yAxis: [
			// 		{
			// 			show: false,
			// 			type: "value"
			// 		},
			// 		{
			// 			show: false,
			// 			type: "value"
			// 		}
			// 	],
			// 	series: []
			// }
			// // 延期--柱状
			// Object.keys(this.delayMap).forEach((v, index) => {
			// 	options.series.push({
			// 		name: `${v}-延期`,
			// 		type: "bar",
			// 		label: {
			// 			show: true,
			// 			position: "top"
			// 		},
			// 		itemStyle: {
			// 			barBorderRadius: [5, 5, 0, 0],
			// 			color: color[index]
			// 		},
			// 		barMaxWidth: "20%",
			// 		data: this.delayMap[v]
			// 	})
			// })
			// // 暂停--折线
			// Object.keys(this.stopMap).forEach((v, index) => {
			// 	options.series.push({
			// 		name: `${v}-暂停`,
			// 		type: "line",
			// 		itemStyle: {
			// 			color: color[index]
			// 		},
			// 		data: this.stopMap[v]
			// 	})
			// })

			chart.setOption(options)
			chart.resize()
		},
		callProjectProcessDetail() {
			this.loading = true
			getProjectProcessDetail(this.$route.query)
				.then(res => {
					if (res.result) {
						let level = res.data.list ? res.data.list : {}
						for (const i in level) {
							for (let $i = 0, j = level[i].length; $i < j; $i++) {}
						}
						this.projectProcessTotal = level
						this.projectProcessTotalAxis = res.data.axis ? res.data.axis : []
						this.data = res.data.datas

						for (const item of res.data.datas) {
							item.productPlannedDate = item.productStageItems
								.filter(e => e.stage == item.mstatus)
								.map(e => e.planReviewDate)[0]
							item.delayDays = item.productStageItems.filter(e => e.stage == item.mstatus).map(e => e.overDays)[0]
						}

						// for (const key in res.data.delayMap) {
						// 	res.data.delayMap[key] = res.data.delayMap[key].map(item => 0 - item)
						// }

						this.delayMap = res.data.delayMap
						this.stopMap = res.data.stopMap

						this.nameFilters = _.chain(res.data.datas)
							.map(item => item.productProjectName)
							.uniq()
							.map(item => ({
								text: item,
								value: item
							}))
							.value()

						this.projectNameFilters = _.chain(res.data.datas)
							.map(item => item.projectName)
							.uniq()
							.map(item => ({
								text: item,
								value: item
							}))
							.value()

						this.deptFilters = _.chain(res.data.datas)
							.map(item => item.dept)
							.uniq()
							.map(item => ({
								text: item,
								value: item
							}))
							.value()

						this.cateFilters = _.chain(res.data.datas)
							.map(item => item.productCate)
							.uniq()
							.map(item => ({
								text: item,
								value: item
							}))
							.value()

						this.pdFilters = _.chain(res.data.datas)
							.map(item => item.productManager)
							.uniq()
							.map(item => ({
								text: item,
								value: item
							}))
							.value()

						this.rpmFilters = _.chain(res.data.datas)
							.map(item => item.researchProjectManager)
							.uniq()
							.map(item => ({
								text: item,
								value: item
							}))
							.value()

						this.custFilters = _.chain(res.data.datas)
							.map(item => item.customer)
							.uniq()
							.map(item => ({
								text: item,
								value: item
							}))
							.value()

						this.dateFilters = _.chain(res.data.datas)
							.map(item => item.productStageItems.filter(e => e.stage == item.mstatus).map(e => e.planReviewDate))
							.uniq()
							.map(item => ({
								text: item,
								value: item
							}))
							.value()

						this.dayFilters = _.chain(res.data.datas)
							.map(item => item.productStageItems.filter(e => e.stage == item.mstatus).map(e => e.overDays))
							.uniq()
							.map(item => ({
								text: item,
								value: item
							}))
							.value()

						this.columns = [
							{
								title: "序号",
								dataIndex: "seq",
								align: "center",
								width: 50,
								customRender: (text, record, index) => <span>{index + 1}</span>
							},
							// {
							// 	title: "三级部门",
							// 	dataIndex: "dept",
							// 	align: "center",
							// 	filters: this.deptFilters,
							// 	onFilter: (value, record) => record.dept == value + ""
							// },
							// {
							// 	title: "产品类别",
							// 	dataIndex: "productCate",
							// 	align: "center",
							// 	filters: this.cateFilters,
							// 	onFilter: (value, record) => record.productCate == value + ""
							// },
							{
								title: "产品名称",
								dataIndex: "productProjectName",
								align: "center",
								filters: this.nameFilters,
								onFilter: (value, record) => record.productProjectName == value + ""
							},
							{
								title: "项目名称",
								dataIndex: "projectName",
								align: "center",
								filters: this.projectNameFilters,
								onFilter: (value, record) => record.projectName == value + ""
							},
							{
								title: "项目等级",
								dataIndex: "productLevel",
								align: "center",
								filters: [
									{ text: "S", value: "S" },
									{ text: "A", value: "A" },
									{ text: "B", value: "B" },
									{ text: "C", value: "C" }
								],
								onFilter: (value, record) => record.productLevel === value
							},
							{
								title: "客户",
								dataIndex: "customer",
								align: "center",
								filters: this.custFilters,
								onFilter: (value, record) => record.customer == value + ""
							},
							{
								title: "PD",
								dataIndex: "productManager",
								align: "center",
								filters: this.pdFilters,
								onFilter: (value, record) => record.productManager == value + ""
							},
							{
								title: "RPM",
								dataIndex: "researchProjectManager",
								align: "center",
								filters: this.rpmFilters,
								onFilter: (value, record) => record.researchProjectManager == value + ""
							},
							// {
							// 	title: "状态",
							// 	dataIndex: "state",
							// 	align: "center",
							// 	filters: [
							// 		{ text: "立项讨论", value: "1" },
							// 		{ text: "A样", value: "2" },
							// 		{ text: "B样", value: "3" },
							// 		{ text: "C样", value: "4" },
							// 		{ text: "D样", value: "5" },
							// 		{ text: "SOP", value: "6" },
							// 		{ text: "TBD", value: "7" },
							// 		{ text: "停产", value: "8" }
							// 	],
							// 	onFilter: (value, record) => record.state == value + "",
							// 	scopedSlots: { customRender: "state" }
							// },
							{
								title: "项目阶段",
								dataIndex: "mstatus",
								align: "center",
								filters: [
									{ text: "K0立项评审", value: "1" },
									{ text: "M1项目规划", value: "2" },
									{ text: "M2 A样方案冻结", value: "3" },
									{ text: "M2 转阶段", value: "4" },
									{ text: "M3 B样方案冻结", value: "5" },
									{ text: "M3 转阶段", value: "6" },
									{ text: "M4 C样方案冻结", value: "7" },
									{ text: "M5 PPAP", value: "8" },
									{ text: "M6 SOP", value: "9" },
									{ text: "结项", value: "10" }
								],
								onFilter: (value, record) => record.mstatus == value + "",
								scopedSlots: { customRender: "mstatus" }
							},
							{
								title: "项目进度",
								dataIndex: "schedule",
								align: "center",
								scopedSlots: {
									customRender: "schedule"
								}
							},
							{
								title: "预计完成时间",
								dataIndex: "productPlannedDate",
								align: "center",
								filters: this.dateFilters,
								onFilter: (value, record) => record.productPlannedDate == value + ""
								//scopedSlots: { customRender: 'productPlannedDate' }
							},
							{
								title: "延迟天数",
								dataIndex: "delayDays",
								align: "center",
								filters: this.dayFilters,
								onFilter: (value, record) => record.delayDays == value + ""
								//scopedSlots: { customRender: 'delayDays' }
							}
							// {
							// 	title: "原因说明",
							// 	dataIndex: "reason",
							// 	align: "center"
							// }
						]

						this.$nextTick(() => {
							this.initTotal()
							this.initNormal()
						})
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
					this.loading = false
				})
				.catch(err => {
					this.loading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		},
		resize() {
			setTimeout(() => {
				this.initTotal()
			}, 500)
		},
		// 处理数据
		_handleArray(obj, data = [], color) {
			Object.keys(obj).forEach((value, index) => {
				if (obj[value].length) {
					obj[value].forEach((e, idx) => {
						if (e.length) {
							e.forEach(v => {
								if (color === "#a5a5a5") {
									v = Number(v) * 30
								}
								data.push([index, idx, Number(v) / 10, color])
							})
						}
					})
				}
			})
			return data
		}
	},
	mounted() {
		this.callProjectProcessDetail()
		window.addEventListener("resize", this.resize)

		// 动态修改--height的值
		document.documentElement.style.setProperty(`--height`, `${this.tableHeight - 63}px`)
	},
	destroyed() {
		window.removeEventListener("resize", this.resize)
	}
}
</script>

<style lang="less" scoped>
// 面包屑

.ant-breadcrumb {
	font-size: 12px !important;
}

.ant-breadcrumb a {
	color: #5d90fa !important;
}

// 图表
.board {
	display: flex;
	margin-bottom: 10px;
}

.col1 {
	width: 50%;
}

.col2 {
	width: 50%;
}

.head {
	position: absolute;
	top: 12px;
	left: 12px;
}

.item {
	padding: 8px;
	border-radius: 10px;
	overflow: hidden;
	background: #fff;
	box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
	position: relative;
}

.chart_table {
	width: 100%;
	height: 35vh;
}

/deep/.ant-table-placeholder {
	padding: 0;
}

// 表头
/deep/.ant-table-thead > tr > th {
	font-size: 13px;
	background: #f5f5f5 !important;
	color: #333;
}

// 表格内容
/deep/.ant-table-tbody {
	background: #fff;
	color: #666;
}

// 表头icon
/deep/.ant-table-thead > tr > th .anticon-filter,
/deep/.ant-table-thead > tr > th .ant-table-filter-icon {
	color: #999;
}

/deep/.ant-table-thead
	> tr
	> th.ant-table-column-has-actions.ant-table-column-has-filters
	.anticon-filter.ant-table-filter-open,
/deep/.ant-table-thead
	> tr
	> th.ant-table-column-has-actions.ant-table-column-has-filters
	.ant-table-filter-icon.ant-table-filter-open,
/deep/.ant-table-thead > tr > th.ant-table-column-has-actions.ant-table-column-has-filters:hover .anticon-filter:hover,
/deep/.ant-table-thead
	> tr
	> th.ant-table-column-has-actions.ant-table-column-has-filters:hover
	.ant-table-filter-icon:hover {
	background: #fff;
	color: rgb(201, 201, 201);
}

/deep/.ant-checkbox-group-item {
	display: block;
	margin: 0 8px;
}

// 表格
.table-wrapper {
	border-radius: 10px;
	overflow: hidden;
	box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
}

:root {
	--height: 600px;
}

/deep/.ant-table-body {
	height: var(--height) !important;
	overflow-y: scroll;
}

// 表头固定
/deep/.ant-table-thead {
	position: sticky;
	top: 0;
}

/* 主标题 */

.head-title {
	color: #333;
	padding: 10px 0;
	font-size: 18px;
	font-weight: 600;
}

.head-title::before {
	width: 8px;
	background: #1890ff;
	margin-right: 8px;
	content: "\00a0"; /* 填充空格 */

	color: #5aaef4;
}

/deep/tr th:hover {
	background-color: #e8e8e8 !important;
	color: #333 !important;
}

/deep/.ant-table-thead > tr > th.ant-table-column-has-actions.ant-table-column-has-filters:hover .anticon-filter:hover {
	background-color: transparent;
	color: #333;
}
/deep/.ant-table-thead
	> tr
	> th.ant-table-column-has-actions.ant-table-column-has-filters:hover
	.ant-table-filter-icon:hover {
	background-color: transparent;
	color: #333;
}
</style>
