<template>
  <a-modal title="新增竞品分析" :width="750" :dialog-style="{ top: '20px' }" :visible="visible"
           :confirmLoading="confirmLoading" @ok="handleSubmit"
           @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">

        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item label="管理单号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-decorator="['code', {rules: [{required: true, message: '请输入管理单号!'}]}]"
                       placeholder="请输入管理单号"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="状态" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <!--                <a-input v-decorator="['productName', {rules: [{required: true, message: '请输入产品名称!'}]}]" placeholder="请输入产品名称" />-->
              <a-select style="width: 100%"
                        v-decorator="['status', {rules: [{required: true, message: '请选择状态!'}]}]"
                        placeholder="请选择状态">
                <a-select-option value="finished">完成</a-select-option>
                <a-select-option value="ongoing">进行中</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

        </a-row>
        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item label="厂商" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-decorator="['factory', {rules: [{required: true, message: '请输入厂商!'}]}]"
                       placeholder="请输入厂商"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="化学体系" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-decorator="['chemicalSystem', {rules: [{required: true, message: '请输入化学体系!'}]}]"
                       placeholder="请输入化学体系"/>
            </a-form-item>
          </a-col>


        </a-row>
        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item label="类型" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-decorator="['competitiveType', {rules: [{required: true, message: '请输入类型!'}]}]"
                       placeholder="请输入类型"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="型号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-decorator="['model']"
                       placeholder="请输入型号"/>
            </a-form-item>
          </a-col>


        </a-row>
        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item label="容量" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input-number v-decorator="['capacity']" style="width: 100%"
                              placeholder="请输入容量"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="应用领域" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-decorator="['applicationArea']"
                       placeholder="请输入应用领域"/>
            </a-form-item>
          </a-col>


        </a-row>

        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item label="委托人" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input type='hidden' v-decorator="['owner',{rules: [{required: true, message: '请选择委托人!'}]}]" />
              <a-dropdown v-model="testManDownVisible" placement="bottomCenter" :trigger="['click']" style="width: 100%;text-align: left">
                <a-button class="man_button" :style="{color:testMan?'rgba(0, 0, 0, 0.65)':'#b7b7b7'}">{{testMan ? testMan : '选择委托人'}}

                </a-button>
                <a-menu slot="overlay">
                  <a-spin :spinning="testManLoading" style="padding:10px 24px 0 24px;width:100%">
                    <a-input-search v-model="testManQueryParam.searchValue" placeholder="搜索..." @change="ontestManSearch" />
                    <s-table style="width:100%;" ref="testManTable" :rowKey="(record) => record.id" :columns="vColumns" :data="loadtestManData" :customRow="customtestManRow" :scroll="{ y: 120,x:120}">
                      <div slot="name" slot-scope="text, record, index, columns" style="display: flex; align-items: center;">
                        <a-avatar size="small" :src="record.avatarUrl" class="avatar"></a-avatar>
                        <div style="margin-left: 8px;">{{ text }}</div>
                      </div>
                    </s-table>
                  </a-spin>
                </a-menu>
              </a-dropdown>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="部门" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-select style="width: 100%"
                        v-decorator="['dept', {rules: [{required: true, message: '请选择部门!'}]}]"
                        placeholder="请选择状态">
                <a-select-option value="C圆柱电池研究所">C圆柱电池研究所</a-select-option>
                <a-select-option value="G圆柱电池研究所">G圆柱电池研究所</a-select-option>
                <a-select-option value="V圆柱电池研究所">V圆柱电池研究所</a-select-option>
                <a-select-option value="铁锂电池研究二所">铁锂电池研究二所</a-select-option>
                <a-select-option value="新型电池研究所">新型电池研究所</a-select-option>
                <a-select-option value="研发管理">研发管理</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>


        </a-row>
        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item label="尺寸" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-decorator="['competitiveSize']"
                       placeholder="请输入尺寸"/>
            </a-form-item>
          </a-col>


        </a-row>

        <a-row :gutter="12">
          <a-col :span="24">
            <a-form-item label="竞品分析报告" :labelCol="labelCol1" :wrapperCol="wrapperCol1">
              <a-upload :file-list="fileList" :headers="headers" :action="postUrl" :multiple="false" name="file"
                        @change="handleChange">
                <a-button>
                  <a-icon type="upload"/>
                  点击上传
                </a-button>
              </a-upload>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="12">
          <a-col :span="24">
            <a-form-item label="拆解详细数据" :labelCol="labelCol1" :wrapperCol="wrapperCol1">
              <a-upload :file-list="fileList1" :headers="headers" :action="postUrl" :multiple="true" name="file"
                        @change="$event => handleChange1($event,'fileList1')">
                <a-button>
                  <a-icon type="upload"/>
                  点击上传
                </a-button>
              </a-upload>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="12">
          <a-col :span="24">
            <a-form-item label="电性能检测数据" :labelCol="labelCol1" :wrapperCol="wrapperCol1">
              <a-upload :file-list="fileList2" :headers="headers" :action="postUrl" :multiple="true" name="file"
                        @change="$event => handleChange1($event,'fileList2')"
              >
                <a-button>
                  <a-icon type="upload"/>
                  点击上传
                </a-button>
              </a-upload>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="12">
          <a-col :span="24">
            <a-form-item label="安全性能检测数据" :labelCol="labelCol1" :wrapperCol="wrapperCol1">
              <a-upload :file-list="fileList3" :headers="headers" :action="postUrl" :multiple="true" name="file"
                        @change="$event => handleChange1($event,'fileList3')"
              >
                <a-button>
                  <a-icon type="upload"/>
                  点击上传
                </a-button>
              </a-upload>
            </a-form-item>
          </a-col>
        </a-row>

      </a-form>
    </a-spin>
    <template slot="footer">
      <a-button @click="handleCancel">
        取消
      </a-button>
      <a-button type="primary" @click="handleSubmit">
        确定
      </a-button>
    </template>
  </a-modal>
</template>

<script>
import Vue from 'vue'
import {competitiveAnalysisAdd} from "@/api/modular/system/competitveAnalysisManager"
import {sysFileInfoDetail} from "@/api/modular/system/fileManage"
import {ACCESS_TOKEN} from '@/store/mutation-types'
import {STable} from "@/components";
import {getUserLists} from "@/api/modular/system/userManage";


export default {
  components: {STable},


  data() {
    return {

      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 8
        }
      },
      labelCol1: {
        xs: {
          span: 24
        },
        sm: {
          span: 4
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 15
        }
      },
      wrapperCol1: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this),
      postUrl: '/api/minioFile/upload',
      headers: {
        Authorization: 'Bearer ' + Vue.ls.get(ACCESS_TOKEN),
      },
      fileList: [],
      fileList1: [],
      fileList2: [],
      fileList3: [],
      record: {},
      testMan:null,
      testManLoading:false,
      testManQueryParam:{},
      testManDownVisible:false,
      vColumns: [{
        title: '账号',
        dataIndex: 'account'
      },
        {
          title: '姓名',
          dataIndex: 'name',
          scopedSlots: { customRender: 'name' }
        },
      ],
      loadtestManData: parameter => {
        return getUserLists(Object.assign(parameter, this.testManQueryParam)).then((res) => {
          return res.data
        })
      },
    }
  },
  methods: {
    // 初始化方法
    add() {

      this.visible = true
    },
    ontestManSearch(e) {
      this.$refs.testManTable.refresh()
    },
    customtestManRow(row, index) {
      return {
        on: {
          click: () => {
            this.form.setFieldsValue({
              owner: row.name,
              ownerAccount: row.account,
              ownerAvatar: row.avatarUrl
            })
            this.testMan = row.name
            this.testManDownVisible = false
          }
        }
      }
    },
    handleChange(info) {
      let fileList = [...info.fileList];
      fileList = fileList.slice(-1);
      this.fileList = fileList;
      if (info.file.status === 'done') {
        let res = info.file.response
        if (res.success) {
          this.$message.success(`${info.file.name} 文件上传成功`)
          this.record.reportName = info.file.name
          this.record.reportId = res.data
        } else {
          this.$message.error(res.message)
        }
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 文件上传失败`);
      }
    },
    handleChange1(info, fileList) {
      this[fileList] = info.fileList
    },

    async handleSubmit() {
      const {
        form: {
          validateFields
        }
      } = this
      this.confirmLoading = true
      validateFields(async (errors, values) => {
        if (!errors) {

          let $params = {
            ...this.record,
            ...values,
            disassembleList: await this.setFileInfo(this.fileList1),
            electricalList: await this.setFileInfo(this.fileList2),
            safeList: await this.setFileInfo(this.fileList3)
          };
          competitiveAnalysisAdd($params).then((res) => {
            if (res.success) {

              this.$message.success('新增成功')
              this.visible = false
              this.confirmLoading = false
              this.handleCancel()
            } else {
              this.$message.error('新增失败：' + res.message)
            }
          }).finally((res) => {
            this.confirmLoading = false
          })
        } else {
          this.confirmLoading = false
        }
      })
    },

    async setFileInfo(fileList) {
      let resultList = []
      for (let i = 0; i < fileList.length; i++) {
        let file = fileList[i]
        if (file.status === 'done') {
          await sysFileInfoDetail({id: file.response.data}).then(res => {
            let detail = res.data
            detail.fileId = file.response.data
            detail.fileName = res.data.fileOriginName
            detail.id = null
            resultList.push(detail)
          })
        }
      }
      return resultList


    },

    handleCancel() {

      this.form.resetFields()
      this.visible = false
      this.fileList = []
      this.fileList1 = []
      this.fileList2 = []
      this.fileList3 = []
      this.record = {}
      this.$emit('ok')
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .ant-modal-header {
  padding: 16px;
}

/deep/ .ant-modal-body {
  padding: 16px;
}

/deep/ .ant-modal-footer {
  padding: 16px 16px !important;
}

/deep/ .ant-form-item {
  margin-bottom: 10px;
}

/deep/ .ant-upload.ant-upload-drag p.ant-upload-text {
  font-size: 14px;
}

/deep/ .ant-upload.ant-upload-drag p.ant-upload-drag-icon {
  margin-bottom: 10px;
}

/deep/ .ant-upload.ant-upload-drag p.ant-upload-drag-icon .anticon {
  font-size: 40px;
}

</style>
