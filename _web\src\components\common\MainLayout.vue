<template>
  <a-layout class="main-layout">
    <a-layout-header class="header">
      <div class="logo">公式拟合系统</div>
      <a-menu
        theme="dark"
        mode="horizontal"
        :selected-keys="[activeKey]"
        class="custom-menu"
      >
        <a-menu-item key="/">
          <router-link to="/"><a-icon type="home" /><span>首页</span></router-link>
        </a-menu-item>
        <a-menu-item key="/prediction">
          <router-link to="/prediction"><a-icon type="line-chart" /><span>预测</span></router-link>
        </a-menu-item>
      </a-menu>
    </a-layout-header>

    <a-layout-content class="content">
      <slot></slot>
    </a-layout-content>

    <a-layout-footer class="footer">
      公式拟合系统 ©2023
    </a-layout-footer>
  </a-layout>
</template>

<script>
export default {
  name: 'MainLayout',
  computed: {
    activeKey() {
      return this.$route.path;
    }
  }
}
</script>

<style scoped>
.main-layout {
  min-height: 100vh;
}

.header {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.logo {
  color: white;
  font-size: 20px;
  font-weight: bold;
  margin-right: 30px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.content {
  padding: 0 20px;
  margin-top: 16px;
  background: #fff;
  min-height: calc(100vh - 134px);
  width: 100%;
  max-width: 1600px;
  margin: 16px auto 0;
}

.footer {
  text-align: center;
  background-color: #f5f7fa;
  padding: 16px 0;
  color: #666;
  font-size: 14px;
  border-top: 1px solid #e8e8e8;
}

.custom-menu {
  background: transparent;
  border-bottom: none;
}

:deep(.custom-menu .ant-menu-item) {
  margin: 8px 4px 0;
  padding: 0 20px;
  border-radius: 4px;
  border-bottom: none !important;
  height: 48px;
  line-height: 48px;
}

:deep(.custom-menu .ant-menu-item:hover) {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

:deep(.custom-menu .ant-menu-item-selected) {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: 500;
}

:deep(.custom-menu .ant-menu-item-selected:after) {
  display: none;
}

:deep(.custom-menu .ant-menu-item a) {
  color: rgba(255, 255, 255, 0.85);
}

:deep(.custom-menu .ant-menu-item-selected a) {
  color: white;
}

:deep(.custom-menu .ant-menu-item .anticon) {
  margin-right: 8px;
  font-size: 16px;
}
</style>