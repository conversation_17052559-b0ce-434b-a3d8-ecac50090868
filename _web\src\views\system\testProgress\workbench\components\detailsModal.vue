<template>
	<a-modal
		:title="`${modalData.testName}`"
		:visible="true"
		width="85%"
		:centered="true"
		:cancel-button-props="{ style: { display: 'none' } }"
		okText="关闭"
		@cancel="handleModelCancel"
		@ok="handleModelCancel"
	>
		<div class="model-wrapper">
			<a-spin :spinning="modalLoading">
				<div>
					<a-descriptions title="详细信息" bordered>
						<a-descriptions-item label="测试参数" :span="3" v-if="!isAQFlag">
							<div class="descriptions-item">
								{{ modalData.testParameter || "-" }}
							</div>
						</a-descriptions-item>
						<a-descriptions-item label="备注" :span="3">
							<div class="descriptions-item">
								{{ modalData.remark || "-" }}
							</div>
						</a-descriptions-item>
            <a-descriptions-item label="复核退回备注" :span="3" v-if="modalData.reviewSendbackOpinion">
              <div class="descriptions-item">
                {{ modalData.reviewSendbackOpinion || "-" }}
              </div>
            </a-descriptions-item>
					</a-descriptions>
				</div>
				<a-descriptions title="录入信息"> </a-descriptions>
				<!-- 数据量小且页面小，所以不做分页 -->
				<a-table bordered :columns="columns" :rowKey="record => record.id" :data-source="tableData" :pagination="false">
					<span slot="originalresult" slot-scope="text, record">
            <a-select v-if="isAQFlag && record.datatype==='select'"
                      :disabled="modalData.taskStatus === '已完成' ? true : false"
                      v-model="record.originalresult"
                      @change="handleInput(record)" placeholder="请输入原始结果" style="width: 100%;">
              <a-select-option value="是">
                是
              </a-select-option>
              <a-select-option value="否">
                否
              </a-select-option>
            </a-select>
						<a-input v-else
							placeholder="请输入原始结果"
							:disabled="modalData.taskStatus === '已完成' ? true : false"
							v-model="record.originalresult"
							@blur="handleInput(record)"
						/>
					</span>
					<span slot="status" slot-scope="text">
						<a-tag v-if="text" color="green">
							{{ text }}
						</a-tag>
						<span v-else>-</span>
					</span>
				</a-table>
				<div v-if="modalData.review && fileList.length > 0">
					<div style="font-size: 16px;color: rgba(0, 0, 0, 0.85);margin: 20px 0px 20px 0px" v-if="modalData.review">
						附件
					</div>
					<a-table
						v-if="modalData.review"
						:columns="attachColumns"
						:rowKey="record => record.id"
						:data-source="fileList"
						:pagination="false"
					>
						<span slot="action" slot-scope="text, record, index">
							<a-button type="link" @click="handleCheck(record)">查看</a-button>
							<a-button type="link" @click="handleDownload(record)">下载</a-button>
						</span>
					</a-table>
				</div>
			</a-spin>
		</div>
		<div v-if="modalData.review" slot="footer">
			<a-button type="primary" style="z-index: 10">
				<a-popconfirm title="确认提交？" @confirm="reviewSubmit()" placement="topRight">
					<a>提交</a>
				</a-popconfirm>
			</a-button>
			<a-button v-if="modalData.taskType == 'aqcs'" type="danger" style="margin-left:5px;z-index: 10">
				<a-popconfirm title="确认退回？" @confirm="reviewSendBack()" placement="topRight">
					<a>退回</a>
				</a-popconfirm>
			</a-button>
      <a-button v-else type="danger" style="margin-left:5px;z-index: 10" @click="beforeReviewSendBack">退回</a-button>
			<a-button style="margin-left:5px;z-index: 10" @click="handleModelCancel">关闭</a-button>
		</div>

    <a-modal title="退回" :width="350" :visible="beforeReviewSendBackFlag" @ok="reviewSendBack()" @cancel="cancelSendBack()">
      <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="备注"  has-feedback>
        <a-textarea style="z-index: 10" v-model="reviewSendBackOpinion" auto-size/>
      </a-form-item>
    </a-modal>
	</a-modal>
</template>

<script>
import Vue from "vue"

import {
	getLimsResultByOrdTaskId,
	updateLimsResultByOrdTaskId,
	InputDataSubmit,
	getFileList,
	getFiles1,
	getFiles
} from "@/api/modular/system/testProgressManager"

import { downloadfile, handleMergeRow } from "@/utils/util"
import { executeReviewSendBack, executeReviewSubmit } from "@/api/modular/system/limsManager"

export default {
	name: "TechniciansModal",
	props: {
		modalData: {
			type: Object,
			default: {}
		}
	},
	data() {
		return {
      labelCol: {
        sm: {
          span: 6
        }
      },
      wrapperCol: {
        sm: {
          span: 15
        }
      },
      beforeReviewSendBackFlag: false,
      reviewSendBackOpinion: null,
      isAQFlag: false,
			modalLoading: false,
			attachColumns: [
				{
					title: "序号",
					align: "center",
					dataIndex: "index",
					customRender: (text, record, index) => `${index + 1}`
				},
				{
					title: "附件名",
					align: "center",
					dataIndex: "name"
				},
				{
					title: "操作",
					align: "center",
					dataIndex: "action",
					scopedSlots: {
						customRender: "action"
					}
				}
			],
			columns: [
				{
					title: "序号",
					dataIndex: "index",
					align: "center",
					customRender: (text, record, index) => `${index + 1}`
				},
				{
					title: "样品编号",
					dataIndex: "sampleno",
					align: "center",
					customRender: (value, row, index) => {
						const obj = {
							children: value,
							attrs: {}
						}
						obj.attrs.rowSpan = row.rowSpan
						return obj
					}
				},
        {
          title: "样品名称",
          dataIndex: "samplename",
          align: "center"
        },
        {
          title: "料号",
          dataIndex: "partno",
          align: "center"
        },
        {
          title: "批次号",
          dataIndex: "batchno",
          align: "center"
        },
				{
					title: "数据类型",
					dataIndex: "analytegrouphead",
					align: "center"
				},
				{
					title: "分析项",
					dataIndex: "analytename",
					align: "center"
				},
				{
					title: "技术规格",
					dataIndex: "requirement",
					align: "center",
					ellipsis: true,
					customRender: text => text || "-"
				},
				{
					title: "原始结果",
					dataIndex: "originalresult",
					align: "center",
					scopedSlots: {
						customRender: "originalresult"
					}
				},
				{
					title: "单位",
					dataIndex: "unit",
					align: "center",
					customRender: text => text || "-"
				},
				{
					title: "状态",
					dataIndex: "status",
					align: "center",
					scopedSlots: {
						customRender: "status"
					}
				},
				{
					title: "录入人员工号",
					align: "center",
					dataIndex: "entrypersoncode",
					customRender: text => text || "-"
				}
				// {
				// 	title: "操作",
				// 	dataIndex: "action",
				// 	scopedSlots: {
				// 		customRender: "action"
				// 	}
				// }
				// {
				// 	title: "备注",
				// 	dataIndex: "remark",
				// 	,
				// },
			],
      aqColumns: [
        {
          title: "序号",
          dataIndex: "index",
          align: "center",
          width: "12.5%",
          customRender: (text, record, index) => `${index + 1}`
        },
        {
          title: "样品编号",
          dataIndex: "sampleno",
          align: "center",
          width: "12.5%",
          customRender: (value, row, index) => {
            const obj = {
              children: value,
              attrs: {}
            }
            obj.attrs.rowSpan = row.rowSpan
            return obj
          }
        },
        {
          title: "数据类型",
          dataIndex: "analytegrouphead",
          width: "12.5%",
          align: "center"
        },
        {
          title: "分析项",
          dataIndex: "analytename",
          width: "12.5%",
          align: "center"
        },
        {
          title: "原始结果",
          dataIndex: "originalresult",
          align: "center",
          width: "12.5%",
          scopedSlots: {
            customRender: "originalresult"
          }
        },
        {
          title: "单位",
          dataIndex: "unit",
          align: "center",
          width: "12.5%",
          customRender: text => text || "-"
        },
        {
          title: "状态",
          dataIndex: "status",
          align: "center",
          width: "12.5%",
          scopedSlots: {
            customRender: "status"
          }
        },
        {
          title: "录入人员工号",
          align: "center",
          width: "12.5%",
          dataIndex: "entrypersoncode",
          customRender: text => text || "-"
        }
      ],
			tableData: [],

			// 文件上传
			postUrl: "/api/sysFileInfo/uploadfile",
			headers: {
				Authorization: "Bearer " + Vue.ls.get("Access-Token")
			},
			fileList: []
		}
	},
	created() {
    // console.log('this.modalData',this.modalData)
    if (this.modalData.taskType === 'aqcs') {
      this.columns = this.aqColumns
      this.isAQFlag = true
    }
		this.getLimsResultByOrdTaskId()
		this.getFileList(this.modalData.ordTaskId)
	},
	mounted() {},
	methods: {
		// 下载处理
		handleDownload(record) {
			getFiles(record.response || record.id).then(async res => {
				if (res) {
					downloadfile(res, record.name)
				}
			})
		},
		// 查看处理
		handleCheck(record) {
			getFiles1(record.response || record.id).then(res => {
				window.open(res, "_blank")
			})
		},
		getFileList(param) {
			this.modalLoading = true
			getFileList(param)
				.then(res => {
					if (res.data === 0) return
					this.fileList = [...res]
				})
				.finally(() => {
					this.modalLoading = false
				})
		},
		getLimsResultByOrdTaskId() {
			this.modalLoading = true
			getLimsResultByOrdTaskId({ ordtaskid: this.modalData.ordTaskId })
				.then(res => {
					if (!res.success) return this.$message.error("错误提示：" + err.message)
					this.tableData = res.data
					handleMergeRow(this.tableData, "sampleno")
				})
				.finally(() => {
					this.modalLoading = false
				})
		},

		updateLimsResultByOrdTaskId(params) {
			updateLimsResultByOrdTaskId(params).then(res => {
				if (!res.success) {
					this.$message.error("错误提示：" + err.message)
				} else {
					this.getLimsResultByOrdTaskId()
				}
			})
		},

		InputDataSubmit(params) {
			InputDataSubmit(params).then(res => {
				if (!res.success) {
					this.$message.error("错误提示：" + res.message)
          return
				}
				this.$message.success("提交成功")
				this.handleModelCancel()
			})
		},

		handleInput(value) {
			const params = {
				id: value.id,
				originalresult: value.originalresult.trim()
			}
			this.updateLimsResultByOrdTaskId(params)
		},

		/**
		 * 弹窗事件
		 */
		handleModelCancel() {
			this.$emit("cancel")
		},

		reviewSubmit() {
			executeReviewSubmit([{ id: this.modalData.ordTaskId, folderno: this.modalData.folderNo }]).then(res => {
				if (res.success === true) {
					setTimeout(() => {
						this.$message.success("结果复核成功")
						this.$emit("refresh")
					}, 200)
				} else {
					this.$message.warning("结果复核失败：" + res.message)
				}
			})
		},

		reviewSendBack() {
			executeReviewSendBack({ ordTaskList: [{ id: this.modalData.ordTaskId }], opinion: this.reviewSendBackOpinion ? this.reviewSendBackOpinion : "拒绝" }).then(res => {
				if (res.success === true) {
					setTimeout(() => {
						this.$message.success("结果退回成功")
						this.$emit("refresh")
					}, 200)
				} else {
					this.$message.warning("结果退回失败：" + res.message)
				}
			})
		},

    cancelSendBack() {
      this.beforeReviewSendBackFlag = false
      this.reviewSendBackOpinion = null
    },

    beforeReviewSendBack() {
        this.beforeReviewSendBackFlag = true
    },

		handleModelOk() {
			// 校验
			if (this.tableData.findIndex(v => v.originalresult === null || v.originalresult === "") > -1) {
				return this.$message.warning("请将所有原始数据填写完整")
			}

			this.InputDataSubmit({
				folderNo: this.modalData.folderNo,
				ordTaskId: this.modalData.ordTaskId,
				todoTaskId: this.modalData.id
			})
		},

		handleShow() {
			this.isTotalChart = !this.isTotalChart
		}
	}
}
</script>

<style lang="less" scoped>
.descriptions-item{
}

/deep/.ant-modal-header {
	border: none !important;
}
/deep/.ant-modal-body {
	padding: 5px 24px;
}
/deep/.ant-modal-footer {
	border: none !important;
}
/deep/.ant-table-thead {
	z-index: 999;
}
/deep/.ant-table-body {
	min-height: 280px;
	overflow: auto;
}
/deep/.ant-table-placeholder {
	border: none !important;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

/deep/.ant-descriptions-title {
	margin: 10px 0;
	text-align: center;
	font-size: 1.25vw;
}
/deep/.ant-modal-title {
	font-size: 1.25vw;
}

/deep/.ant-descriptions-item{
	display: flex;
}
/deep/.ant-descriptions-item-label {
	font-size: 1.0938vw;
}
/deep/.ant-descriptions-item-content {
	font-size: 1.0938vw;
	width: 85%;
}
</style>
