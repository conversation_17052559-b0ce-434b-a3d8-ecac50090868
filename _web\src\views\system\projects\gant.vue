<template>
<div class="ganttMain">
    <div ref="gantt" :style="{height: height+'px'}"></div>
</div>
</template>

<script>
import { gantt } from 'dhtmlx-gantt';
import moment from 'moment';
import { ganttSave,ganttInfo } from '@/api/modular/system/ganttManage'

export default {
    props: {
		issueId: {
			type: Number,
			default: 0
		},
	},
    data(){
        return {
            ganttItem:{},
            form: this.$form.createForm(this),
            height: document.documentElement.clientHeight,
            datas:{
                data:[
                    {id:1, text:"Milestones",  type:"project", progress: 0, open: false,hide_bar:true},

                    {id:2, text:"Development",  type:"project", progress: 0, open: false,hide_bar:true},

                    {id:3, text:"Prototype Production",  type:"project", progress: 0, open: false,hide_bar:true},

                    {id:4, text:"Industrialization",  type:"project", progress: 0, open: false,hide_bar:true},

                    {id:5, text:"Equipment",  type:"project", progress: 0, open: false,hide_bar:true},

                    {id:10, text:"T&V sample",  type:"project", progress: 0, open: false,hide_bar:true},

                    {id:6, text:"A sample",  type:"project", progress: 0, open: false,hide_bar:true},

                    {id:7, text:"B sample",  type:"project", progress: 0, open: false,hide_bar:true},

                    {id:8, text:"C sample",  type:"project", progress: 0, open: false,hide_bar:true},

                    {id:9, text:"D sample",  type:"project", progress: 0, open: false,hide_bar:true},
                ],
                links:[]
            }
            
        }
    },
    methods:{
        moment,
        init(){
            let that = this

            let columns = [
                { name: 'text', label: '项目计划', tree: true, width:120, align: 'left'},
                { name:"add", width: 30}
            ]
            
            gantt.clearAll();

            gantt.config.scales = [
                {unit: "year", step: 1, format: "%Y"},
                {unit: "month", step: 1, format: "%m",}
            ];

            gantt.form_blocks.dhx_calendar = {
                render: function(config) {
                    return '<div class="gantt_cal_labeled_block" style="padding: 0 0 0 10px;">' +
                        '<input class="gantt_start_date" type="date" value="">' + ' - '+ '<input class="gantt_end_date" type="date" value="">'
                        '</div>';
                },
                set_value: function(node, value, task) {
                    
                    var day = ("0" + task.start_date.getDate()).slice(-2)
                    var month = ("0" + (task.start_date.getMonth() + 1)).slice(-2)
                    var start = task.start_date.getFullYear()+"-"+(month)+"-"+(day)


                    day = ("0" + task.end_date.getDate()).slice(-2)
                    month = ("0" + (task.end_date.getMonth() + 1)).slice(-2)
                    var end = task.end_date.getFullYear()+"-"+(month)+"-"+(day)

                    node.querySelector(".gantt_start_date").value = start
                    node.querySelector(".gantt_end_date").value = end 
                },
                get_value: function(node, task) {
                    var start =  node.querySelector(".gantt_start_date").value;
                    var end = node.querySelector(".gantt_end_date").value;
                    task.start_date = new Date(start);
                    task.end_date = new Date(end);  
                    task.duration = gantt.calculateDuration(task);  
                },
                focus: function(node) {
                }
            };


            gantt.config.lightbox.milestone_sections = [
                {name: "type", type: "select", map_to: "type",options: [
                    {key: 'milestone', label: "里程碑"},
                    {key: 'task', label: "计划"},
			    ]},
                {name: "time", type: "dhx_calendar", map_to: "auto",date_format: '%d %M %Y'},
                {name:"", height: 0, map_to: "color", type: "color_picker" },
                {name: "description",  map_to: "text", type: "textarea"},
            ];

            gantt.config.lightbox.project_sections = [
                /* {name: "type", type: "select", map_to: "type",options: [
                    {key: 'project', label: "项目"},
			    ]}, */
                {name: "description",  map_to: "text", type: "textarea"},
            ];


            gantt.config.lightbox.sections = [
                {name: "type", type: "select", map_to: "type",options: [
                    {key: 'milestone', label: "里程碑"},
                    {key: 'task', label: "计划"},
			    ]},
                {name: "time", type: "dhx_calendar", map_to: "auto",date_format: '%d %M %Y'},
                {name:"", height: 0, map_to: "color", type: "color_picker" },
                {name: "description",  map_to: "text", type: "textarea"},
            ];

            

            gantt.form_blocks.color_picker = {
                render: function(config) {
                    return '<div class="gantt_cal_labeled_block" style="padding: 12px 0 5px 10px;">' +
                        '<div class="gantt_label" style="color: rgba(0, 0, 0, 0.85);font-size: 13px;margin-bottom: 5px;">颜色</div>' +
                        '<div class="gantt_cal_block">' +
                        '<input class="gantt_color_picker" type="color" value="' + (config.value || "#ffffff") + '">' +
                        '</div></div>';
                },
                set_value: function(node, value, task) {
                    let defaultColor = "#a19edc"
                    if (task.$new) {
                        /* let typeColor = {
                            1:'#a19edc',
                            2:'#b4dcfa',
                            3:'#fda835',
                            4:'#cccac8',
                            5:'#bca754',
                            6:'#b0e17b',
                            7:'#b0e17b',
                            8:'#b0e17b',
                            9:'#b0e17b',
                            10:'#b0e17b',
                        }
                        defaultColor = typeColor[task.parent] */
                    }
                    node.querySelector(".gantt_color_picker").value = value || defaultColor;
                },
                get_value: function(node, task) {
                    //task.color = node.querySelector(".gantt_color_picker").value;
                    return node.querySelector(".gantt_color_picker").value;
                },
                focus: function(node) {
                    node.querySelector(".gantt_color_picker").focus();
                }
            };


            gantt.config.autosize = true;
            gantt.config.bar_height = 15;
            gantt.config.scroll_size = 8;
            gantt.config.duration_unit ="month";
            gantt.config.columns = columns
            gantt.config.autofit = true;
            gantt.i18n.setLocale("cn");
            gantt.config.xml_date = "%Y-%m-%d";
            gantt.config.scale_height = 25;
            gantt.config.row_height = 48;
            gantt.config.min_column_width = 20;
            gantt.config.tooltip = false;

            gantt.templates.rightside_text = function(start, end, task) {
                if (task.type == 'milestone') {
                    return moment(task.start_date).format('MM/DD')+"<br/>"+task.text;
                }
                return ''
                    
            };

            gantt.templates.task_text = function(start, end, task) {
                return ''
                    
            };
            gantt.plugins({ 
                marker: true 
            }); 
            gantt.templates.marker_text = function(marker){
                return marker.text;
            };
            // 添加一个红色的标记，代表今天
            var dateToStr = gantt.date.date_to_str(gantt.config.task_date);
            gantt.addMarker({
                start_date: new Date(), //a Date object that sets the marker's date
                css: "gantt_task_marker", //a CSS class applied to the marker
                text: "今天", //the marker title
                title: dateToStr( new Date()) // the marker's tooltip
            });
            gantt.config.layout = {
                css: "gantt_container",
                cols: [{
                    width: 150,
                    min_width: 150,
                    rows: [
                        {
                            view: "grid",
                            scrollX: "gridScroll",
                            scrollable: true,
                            scrollY: "scrollVer"
                        },
                        { view: "scrollbar", id: "gridScroll", group: "horizontal" }
                    ]
                },
                { resizer: true, width: 1 },
                {
                    rows: [
                        {
                            view: "timeline",
                            scrollX: "scrollHor",
                            scrollY: "scrollVer"
                        },
                        {
                            view: "scrollbar",
                            id: "scrollHor",
                            group: "horizontal"
                        }
                    ]
                },
                { view: "scrollbar", id: "scrollVer" }]
            };
            
            gantt.init(this.$refs.gantt);

            gantt.attachEvent("onAfterTaskAdd",function(id,item){
                /* let typeColor = {
                    1:'#a19edc',
                    2:'#b4dcfa',
                    3:'#fda835',
                    4:'#cccac8',
                    5:'#bca754',
                    6:'#b0e17b',
                    7:'#b0e17b',
                    8:'#b0e17b',
                    9:'#b0e17b'
                } */
                item.rollup = true
                /* if (item.type == 'milestone') {
                    item.color = '#4E67C8'
                }
                if (item.type == 'task') {
                    item.color = typeColor[item.parent]
                } */
                return true
            })

            gantt.templates.progress_text=function(start, end, task){return "";};
            
            gantt.attachEvent("onLightboxDelete", function (id) {
                if (id < 13) {
                    return false
                }
                return true;
            });

            gantt.attachEvent("onLightboxCancel", function (id) {
                let datas = JSON.parse(JSON.stringify(that.datas))
                for (const item of datas['data']) {
                    item.open = false
                    item.$open = false
                }
                that.datas = datas
                gantt.parse(that.datas)
            })

            

            gantt.createDataProcessor({
                task: {
                    create: function(data) {
                        that.datas['data'].push(data)
                        that.callGanttSave(0)
                        return
                    },
                    update: function(data, id) {
                        let $i= that.datas['data'].findIndex(o=>o.id==id)
                        that.datas['data'].splice($i,1)
                        that.datas['data'].splice($i,0,data)
                        that.callGanttSave(0)
                        return
                    },
                    delete: function(id) {
                        let $i = that.datas['data'].findIndex(e=>e.id == id)
                        that.datas['data'].splice($i,1)
                        that.callGanttSave(0)
                        return
                    }
                },
                link: {
                    create: function(data) {
                        that.datas['links'].push(data)
                        that.callGanttSave(0)
                        return
                    },
                    update: function(data, id) {
                        let $i= that.datas['links'].findIndex(o=>o.id==id)
                        that.datas['links'].splice($i,1)
                        that.datas['links'].splice($i,0,data)
                        that.callGanttSave(0)
                        return
                    },
                    delete: function(id) {
                        let $i = that.datas['data'].findIndex(e=>e.id == id)
                        that.datas['links'].splice($i,1)
                        that.callGanttSave(0)
                        return
                    }
                }
            });
            gantt.parse(that.datas);
        },
        callGanttInfo(){
            let that = this
            ganttInfo({issueId:that.issueId}).then((res) => {
                if (res.success) {
                    if (res.data) {
                        that.ganttItem = res.data
                        that.datas = JSON.parse(that.ganttItem.ganttValue) 
                    }
                    setTimeout(() => {
                        that.$nextTick(() => {
                            that.init()
                        })
                    }, 100);

                } else {
                    this.$message.error('网络错误：' + res.message)
                }
            }).catch((err) => {
                this.$message.error('错误：' + err.message)
            }).finally((res) => {

            }) 
        },
        callGanttSave(refresh){
            let that = this
            that.ganttItem.issueId = that.issueId
            let datas = JSON.parse(JSON.stringify(that.datas))
            for (const item of datas['data']) {
                item.open = false
                item.$open = false
            }
            that.ganttItem.ganttValue = JSON.stringify(datas)
            ganttSave(that.ganttItem).then((res) => {
                if (res.success) {
                    that.ganttItem.id = res.data
                    that.datas = datas
                    gantt.parse(that.datas)
                } else {
                    this.$message.error('网络错误：' + res.message)
                }
            }).catch((err) => {
                this.$message.error('错误：' + err.message)
            }).finally((res) => {
            }) 
        },
    },
    mounted(){
        this.callGanttInfo()
    }
}
</script>
<style>
    @import "~dhtmlx-gantt/codebase/dhtmlxgantt.css";
    .gantt_cal_ltext{
        overflow: initial;
    }
    .gantt_time{
        font-weight: 500;
        font-size: 13px;
    }
    .gantt_duration .gantt_duration_dec,.gantt_duration .gantt_duration_inc{
        border-radius: 0;
    }

    .gantt_cal_lsection:nth-child(5){
        display: none !important;
    }

    .gantt_cal_light{
        border-radius: 0;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    .gantt_cancel_btn,.gantt_save_btn,.gantt_delete_btn{
        display: none;
    }
    .gantt_btn_set{
        padding: 5px 15px 5px 15px;
        border-radius: 0;
        float: right;
        font-weight:initial;
    }
    .gantt_popup_button{
        border-radius: 0;
        font-weight: normal !important;
    }
    .gantt_cal_cover{
        background-color: rgba(0, 0, 0, 0.45);
        opacity: initial;
    }
    .gantt_btn_set.gantt_delete_btn_set{
        background: #ff4d4f;
    }
    .gantt_btn_set.gantt_save_btn_set,.gantt_popup_button.gantt_ok_button{
        background: #1890ff;
    }
    .gantt_modal_box{
        border-radius: 0;
    }.gantt_cal_lsection{
        font-weight: normal;
        color: rgba(0, 0, 0, 0.85);
    }input{
        outline: none;
    }

    
</style>
<style lang="less" scoped=''>
.ganttMain{
    background: #fff;
}
.ganttMain div.form{
    text-align: center;
    padding: 15px 0;
}
/deep/.gantt_grid_head_add {
    display: none;
}
/deep/div.gantt_row:not([aria-level="0"]) .gantt_add{
    display: none;
}
/deep/div.gantt_row[task_id="10"] .gantt_add{
    display: block;
}
/deep/div.gantt_row[task_id="6"] .gantt_add{
    display: block;
}
/deep/div.gantt_row[task_id="7"] .gantt_add{
    display: block;
}
/deep/div.gantt_row[task_id="8"] .gantt_add{
    display: block;
}
/deep/div.gantt_row[task_id="9"] .gantt_add{
    display: block;
}
/deep/.task-color-cell{
        width:15px;
        height:15px;
        border:1px solid #cecece;
        display:inline-block;
        border-radius:20px;
    }
    /deep/.gantt_grid_editor_placeholder{
        text-align: center;
    }
    /deep/.gantt_grid_editor_placeholder input {
        width:22px;
        height:22px;
        border: 0;
        outline: none;
    }
    /deep/.gantt_task_line.gantt_task_inline_color{
        border: none;
    }
    /deep/.gantt_folder_open,/deep/.gantt_folder_closed,/deep/.gantt_file{
        display: none;
    }
    /deep/.gantt_task_line.gantt_milestone .gantt_task_content{
        border-radius: 0;
        z-index: 1;
    }
    /deep/.gantt_task_cell{
        border-right: none;
    }

    /deep/div.gantt_row[task_id="3"],
    /deep/div.gantt_row[task_id="4"],
    /deep/div.gantt_row[task_id="6"],
    /deep/div.gantt_row[task_id="7"],
    /deep/div.gantt_row[task_id="8"],
    /deep/div.gantt_row[task_id="10"]{
        border-bottom: none;
    }

    /deep/.gantt_side_content{
        bottom: -17px;
        top: initial;
        left: -100% !important;
        padding-left: 0 !important;
        overflow: initial;
        line-height: 11px;
    }

    /deep/div.gantt_task_row[task_id="3"],
    /deep/div.gantt_task_row[task_id="4"],
    /deep/div.gantt_task_row[task_id="6"],
    /deep/div.gantt_task_row[task_id="7"],
    /deep/div.gantt_task_row[task_id="8"],
    /deep/div.gantt_task_row[task_id="10"]{
        border-bottom: none;
    }

    /deep/.gantt_task .gantt_task_scale .gantt_scale_cell{
        color: #454545;
    }

    

/deep/.gantt_color_picker {
  padding: 0;
  border: none;
  width: 100%;
  height: 30px;
  background-color: transparent;
}

/deep/.gantt_color_picker::-webkit-color-swatch-wrapper {
  padding: 0;
}

/deep/.gantt_color_picker::-webkit-color-swatch {
  border: none;
  border-radius: 0;
  width: 100%;
  height: 30px;
  overflow: hidden;
}

/deep/.gantt_color_picker::-moz-color-swatch {
  border: none;
  border-radius: 0;
  width: 100%;
  height: 30px;  
  overflow :hidden;
}

/deep/.gantt_task_line.gantt_task_inline_color.gantt_selected{
    box-shadow: none;
}
/deep/.gantt_tree_icon.gantt_open{
    background-image: url('data:image/svg+xml,<svg class="bi bi-chevron-right" width="10px" height="10px" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z"/></svg>')
}
/deep/.gantt_tree_icon.gantt_close{
    background-image: url('data:image/svg+xml,<svg class="bi bi-chevron-down" width="10px" height="10px" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z"/></svg>')
}
/deep/.gantt_add{
    background-image: url('data:image/svg+xml,<svg class="bi bi-plus" width="1em" height="1em" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M8 3.5a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5H4a.5.5 0 0 1 0-1h3.5V4a.5.5 0 0 1 .5-.5z"/><path fill-rule="evenodd" d="M7.5 8a.5.5 0 0 1 .5-.5h4a.5.5 0 0 1 0 1H8.5V12a.5.5 0 0 1-1 0V8z"/></svg>')
}
/deep/.gantt_cell{
    font-size: 12px;
}
/deep/.gantt_grid_scale .gantt_grid_head_cell{
    color: #45455d;
}

/deep/.gantt_task_marker {
    border: 1px dashed #efacac;
    background-color: #fff;
}
/deep/.gantt_marker .gantt_marker_content{
    background: #ffc4c4;
}
/deep/.gantt_tree_icon{
    display: none !important;
}

</style>
