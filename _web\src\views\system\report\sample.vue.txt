<template>
  <div>
    <a-breadcrumb class="breadcrumb" separator=">" :style="`width:${_elWidth}px;margin:0 auto;`">
      <a-breadcrumb-item><a @click="gotoIndex(-3)">信息对齐表</a></a-breadcrumb-item>
      <a-breadcrumb-item><a @click="gotoIndex(-2)">产品开发进展</a></a-breadcrumb-item>
      <a-breadcrumb-item><a @click="gotoIndex(-1)">质量指标</a></a-breadcrumb-item>
      <a-breadcrumb-item>一次送样合格率</a-breadcrumb-item>
    </a-breadcrumb>
    <vxe-table :height="windowHeight" :loading="loading" show-footer border align="center" :data="list" :footer-method="footerMethod" >
    <!-- <vxe-column type="seq" width="60" title="序号"></vxe-column> -->
    <vxe-column field="productStage" width="90" title="产品阶段">
      <template #default="{ row }">
              <span>{{ 'product_stage_status' | dictType(row.productStage) }}</span>
      </template>
    </vxe-column>
    <vxe-column field="slap" width="90" title="送样批次"></vxe-column>
    <vxe-column field="customer" width="90" title="客户"></vxe-column>
    <vxe-column field="dqe" width="180" title="DQE"></vxe-column>
    <vxe-column field="sampleDate" width="180" title="送样日期"></vxe-column>
    <vxe-column field="sampleCount" width="80" title="送样数量"></vxe-column>
    <vxe-column field="isFeedBack" width="100" title="客户是否反馈">
      <template #default="{ row }">
              <span>{{ 'is_or_no' | dictType(row.isFeedBack) }}</span>
      </template>
    </vxe-column>
    <vxe-column field="feedback" width="200" title="客户反馈">
      <template #default="{ row }">
            <clamp :text="row.feedback" :maxLines=3 :sourceText="[row.feedback]"></clamp>
      </template>
    </vxe-column>
    <vxe-column field="eveFeedback" width="200" title="EVE回复">
      <template #default="{ row }">
            <clamp :text="row.eveFeedback" :maxLines=3 :sourceText="[row.eveFeedback]"></clamp>
      </template>
    </vxe-column>
    <vxe-column field="isPass" width="150" title="批次样品是否合格">
      <template #default="{ row }">
              <span>{{ 'is_or_no' | dictType(row.isPass) }}</span>
      </template>
    </vxe-column>
  </vxe-table>
  </div>
  
</template>

<script>
  import {
    clamp
  } from '@/components'
  import {getSampleLists} from "@/api/modular/system/report"
  export default {
    components: {
      clamp
    },
    data() {
      return {
        windowHeight: document.documentElement.clientHeight - 35,
        list: [],
        loading: false,
      }
    },
    methods: {
      gotoIndex(index){
      this.$router.go(index)
    },
      /* footerColspanMethod({
        $rowIndex,
        _columnIndex
      }) {
        if ($rowIndex === 0) {
          if (_columnIndex === 0) {
            return {
              rowspan: 1,
              colspan: 4
            }
          }
          if (_columnIndex === 4) {
            return {
              rowspan: 1,
              colspan: 2
            }
          }
          if (_columnIndex === 6) {
            return {
              rowspan: 1,
              colspan: 2
            }
          }
          if (_columnIndex === 8) {
            return {
              rowspan: 1,
              colspan: 2
            }
          }
          if (_columnIndex === 10) {
            return {
              rowspan: 1,
              colspan: 2
            }
          }
          if (_columnIndex === 12) {
            return {
              rowspan: 1,
              colspan: 2
            }
          }
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }, */
      callSampleLists() {
        this.loading = true
        getSampleLists({
            productStage: this.$route.query.stage,
            issueId: this.$route.query.issueId
          })
          .then((res) => {
            if (res.success) {
              this.list = res.data
            } else {
              this.$message.error(res.message, 1);
            }
            this.loading = false
          })
          .catch((err) => {
            this.loading = false
            this.$message.error('错误提示：' + err.message, 1)
          });
      },
      footerMethod({
        columns
      }) {
       
        let a = 0
        for (const item of this.list) {
          if (item.sampleCount) {
            a = a+ parseInt(item.sampleCount)
          }
        }


        let b = this.list.filter(e => e.isPass == 1).length
        let c = !b ? 0 : parseFloat((b / this.list.length) * 100).toFixed(2)

        return [
          columns.map((column, _columnIndex) => {
            if (_columnIndex == 0) {
              return '累计送样批次'
            }

            if (_columnIndex == 1 ) {
              return this.list.length
            }

            if (_columnIndex == 4) {
              return '累计送样数量'
            }

            if (_columnIndex == 5) {
              return a
            }
            

            if (_columnIndex === 8) {
              return '一次送样合格率'
            }

            if (_columnIndex === 9) {
              return c + '%'
            }
           
            return ''
          }),
        ]
      },
    },
    created() {
      this.callSampleLists()
    }
  }
</script>

<style lang="less">
  @import './vetable.less';
  .breadcrumb{
  padding: 5px 0;
  padding-left: 13px;
}.ant-breadcrumb a{
  color:#5d90fa !important;
}.ant-breadcrumb{
  font-size: 12px !important;
}
</style>