<template>
	<a-modal
		:title="`${modalData.testName}附件管理`"
		:visible="true"
		width="50%"
		:centered="true"
		okText="关闭"
		:cancel-button-props="{ style: { display: 'none' } }"
		@cancel="handleModelCancel"
		@ok="handleModelOk"
	>
		<div class="model-wrapper">
			<div class="modal-btn mb10">
				<!-- T_LIMS_FOLDER$:Lims下载需要表名 -->
				<a-upload
					:headers="headers"
					:action="postUrl"
					name="file"
					:beforeUpload="beforeFileUpload"
          :multiple="true"
					:fileList="fileList"
					:data="{ targetId: modalData.ordTaskId, name: fileNames }"
					:showUploadList="false"
					accept="*"
					@change="handleUploadFile($event)"
				>
					<a-button type="primary" :disabled="modalData.taskStatus === '已完成'">上传附件</a-button>
				</a-upload>
			</div>

			<a-spin :spinning="modalLoading">
				<a-table :columns="columns" :rowKey="record => record.id" :data-source="fileList" :pagination="false">
					<span slot="action" slot-scope="text, record, index">
						<a-button type="link" @click="handleCheck(record)">查看</a-button>
						<a-button type="link" @click="handleDownload(record)">下载</a-button>
						<a-popconfirm
							title="确认删除"
							ok-text="确认"
							cancel-text="取消"
							@confirm="handleDelect(record)"
							@cancel="handleCancel"
						>
							<a-button type="link" :disabled="modalData.taskStatus === '已完成'">删除</a-button>
						</a-popconfirm>
					</span>
				</a-table>
			</a-spin>
		</div>
	</a-modal>
</template>

<script>
import Vue from "vue"
import { ACCESS_TOKEN } from "@/store/mutation-types"

import { getFiles, getFileList, deleteFiles, getFiles1 } from "@/api/modular/system/testProgressManager"

import { downloadfile } from "@/utils/util"

export default {
	name: "TechniciansModal",
	props: {
		modalData: {
			type: Object,
			default: {}
		}
	},
	data() {
		return {
			modalLoading: false,
			columns: [
				{
					title: "序号",
					dataIndex: "index",
					customRender: (text, record, index) => `${index + 1}`
				},
				{
					title: "附件名",
					dataIndex: "name",
					width: 200
				},
				{
					title: "操作",
					dataIndex: "action",
					scopedSlots: {
						customRender: "action"
					}
				}
			],
			tableData: [],
			fileList: [],

			// 文件上传
			fileNames: "",
			postUrl: "/limsUpload/open/basemodule/sys/files/upload",
			headers: {
				Authorization: "Bearer " + Vue.ls.get(ACCESS_TOKEN)
			}
		}
	},
	created() {
		this.getFileList(this.modalData.ordTaskId)
	},
	mounted() {},
	methods: {
		getFileList(param) {
			this.modalLoading = true
			getFileList(param)
				.then(res => {
					if(res.data === 0) return
					this.fileList = [...res]
				})
				.finally(() => {
					this.modalLoading = false
				})
		},
		// 获取上传的文件名称
		beforeFileUpload(file) {
			this.fileNames = file.name
			return true
		},
		// 上传处理
		handleUploadFile(info) {
			this.fileList = [...info.fileList]
			if (info.file.status === "done") {
				this.$message.success(`${info.file.name} 文件上传成功`)
			} else if (info.file.status === "error") {
				this.$message.error(`${info.file.name} 文件上传失败`)
			}
			this.$forceUpdate()
		},

		// 删除处理
		handleDelect(record) {
			const id = record.id || record.response
			deleteFiles({ b: ["", [{ id: id }]] }).then(res => {
				this.getFileList(this.modalData.ordTaskId)
			})
		},

		// 查看处理
		handleCheck(record) {
			getFiles1(record.response || record.id).then(res => {
				window.open(res, "_blank")
			})
		},
		handleCancel() {
			this.$message.success("删除取消")
		},

		// 下载处理
		handleDownload(record) {
			getFiles(record.response || record.id).then(async res => {
				if (res) {
					downloadfile(res, record.name)
				}
			})
		},
		/**
		 * 弹窗事件
		 */
		handleModelCancel() {
			this.$emit("cancel")
		},
		handleModelOk() {
			this.$emit("cancel")
		},
		handleShow() {
			this.isTotalChart = !this.isTotalChart
		}
	}
}
</script>

<style lang="less" scoped>
.modal-btn {
	text-align: right;
}

.mb10{
	margin-bottom: 10px;
}
/deep/.ant-table-body {
	position: relative;
}

/deep/.ant-table-placeholder {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}
/deep/.ant-modal-header {
	border: none;
}
/deep/.ant-modal-body {
	padding: 5px 24px;
}
/deep/.ant-modal-footer {
	border: none;
}
/deep/.ant-table-thead {
	z-index: 999;
}
</style>
