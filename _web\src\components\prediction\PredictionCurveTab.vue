<template>
  <div class="prediction-curve-section">
    <a-row :gutter="16">
      <!-- 左侧参数输入卡片 -->
      <a-col :xs="24" :sm="24" :md="10" :lg="10" :xl="10">
        <div class="prediction-params-card">
          <h5>
            预测参数设置
            <a-tooltip placement="right">
              <template slot="title">
                设置不同的温度、SOC和存储天数，预测电池在不同条件下的容量衰减曲线。
              </template>
              <a-icon type="info-circle" class="info-icon" />
            </a-tooltip>
          </h5>
          <div class="prediction-params-content">

            <!-- 参数表单组 -->
            <div class="prediction-params-form">
              <div
                v-for="(param, index) in predictionParams"
                :key="param.id"
                class="param-group-card"
              >
                <div class="param-group-header">
                  <span class="param-group-title">预测组 {{ param.id }}</span>
                  <a-button
                    v-if="predictionParams.length > 1"
                    type="link"
                    icon="delete"
                    size="small"
                    @click="removeParamRow(index)"
                    class="delete-button"
                    title="删除此预测组"
                  />
                </div>

                <div class="param-group-content">
                  <div class="param-inline-form">
                    <!-- 温度输入 -->
                    <div class="param-inline-item">
                      <label class="param-label">温度</label>
                      <div class="param-input-wrapper">
                        <a-input-number
                          v-model="param.temperature"
                          :min="-40.0"
                          :max="65.0"
                          :precision="1"
                          placeholder="25.0"
                          class="param-input"
                          size="small"
                          @change="handleParamChange"
                        />
                        <span class="param-unit">°C</span>
                      </div>
                    </div>

                    <!-- SOC输入 -->
                    <div class="param-inline-item">
                      <label class="param-label">SOC</label>
                      <div class="param-input-wrapper">
                        <a-input-number
                          v-model="param.soc"
                          :min="0"
                          :max="1"
                          :step="0.1"
                          :precision="2"
                          :formatter="value => `${(value * 100).toFixed(0)}`"
                          :parser="value => value.replace('%', '') / 100"
                          placeholder="100"
                          class="param-input"
                          size="small"
                          @change="handleParamChange"
                        />
                        <span class="param-unit">%</span>
                      </div>
                    </div>

                    <!-- 存储天数输入 -->
                    <div class="param-inline-item">
                      <label class="param-label">存储天数</label>
                      <div class="param-input-wrapper">
                        <a-input-number
                          v-model="param.days"
                          :min="1"
                          :max="5000"
                          :precision="0"
                          placeholder="365"
                          class="param-input"
                          size="small"
                          @change="handleParamChange"
                        />
                        <span class="param-unit">天</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 添加新预测按钮 -->
            <div class="add-prediction-action">
              <a-button type="dashed" block @click="addNewParamRow" icon="plus" class="add-prediction-button">
                添加新预测组
              </a-button>
            </div>

            <!-- 预测按钮 -->
            <div class="run-prediction-action">
              <a-button
                type="primary"
                block
                icon="line-chart"
                :loading="calculatingPrediction"
                @click="calculatePredictionCurves"
                class="calculate-prediction-button"
              >
                计算预测曲线
              </a-button>
            </div>
          </div>
        </div>
      </a-col>

      <!-- 右侧容量曲线预测图 -->
      <a-col :xs="24" :sm="24" :md="14" :lg="14" :xl="14">
        <div class="capacity-curve-card">
          <h5>容量曲线预测图</h5>
          <div class="capacity-curve-content">
            <div v-if="predictionCurves.length === 0" class="empty-chart">
              <div class="empty-chart-content">
                <a-icon type="line-chart" class="empty-chart-icon" />
                <h3>暂无预测曲线数据</h3>
                <p>请在左侧设置预测参数，然后点击"计算预测曲线"按钮生成预测图</p>
              </div>
            </div>
            <div v-else ref="capacityCurveChart" class="capacity-curve-chart"></div>
          </div>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { api } from '@/api';
import { message } from 'ant-design-vue';
import predictionMixin from '@/mixins/predictionMixin';

export default {
  name: 'PredictionCurveTab',
  mixins: [predictionMixin],
  props: {
    modelId: {
      type: [Number, String],
      required: true
    }
  },
  data: () => ({
    predictionParams: [
      {
        id: 1,
        temperature: 25,
        soc: 0.5,
        days: 365
      }
    ],
    predictionCurves: [],
    calculatingPrediction: false
  }),
  methods: {
    // 添加新的预测参数行
    addNewParamRow() {
      const newId = this.predictionParams.length > 0
        ? Math.max(...this.predictionParams.map(p => p.id)) + 1
        : 1;

      this.predictionParams.push({
        id: newId,
        temperature: 25,
        soc: 1.0,
        days: 365
      });
    },

    // 移除参数行
    removeParamRow(index) {
      if (this.predictionParams.length > 1) {
        this.predictionParams.splice(index, 1);
      }
    },

    // 处理参数变化
    handleParamChange() {
      // 可以在这里添加参数验证逻辑
      // 参数变化时的处理逻辑
    },

    // 计算预测曲线
    async calculatePredictionCurves() {
      this.calculatingPrediction = true;

      try {
        // 清空现有曲线数据
        this.predictionCurves = [];

        // 准备预测数据点
        const dataPoints = this.predictionParams.map(param => {
          // 生成天数数组
          const daysArray = [];
          for (let i = 0; i <= param.days; i += Math.max(1, Math.floor(param.days / 50))) {
            daysArray.push(i);
          }
          // 确保最大天数在数组中
          if (daysArray[daysArray.length - 1] !== param.days) {
            daysArray.push(param.days);
          }

          return {
            temperature: param.temperature,
            soc: param.soc,
            days: daysArray,
            // 容量字段需要发送但不是必需的值，设置为空数组
            capacities: []
          };
        });

        // 构建请求数据
        const requestData = {
          model_id: this.modelId,
          data_points: dataPoints
        };

        // 调用预测API
        const response = await api.data.predictCapacity(requestData);

        if (response.data.success) {
          const predictedDataPoints = response.data.predicted_data || [];

          // 处理预测数据
          this.predictionCurves = predictedDataPoints.map((dataPoint, index) => {
            const paramId = this.predictionParams[index]?.id || (index + 1);

            return {
              id: paramId,
              temperature: dataPoint.temperature,
              soc: dataPoint.soc,
              days: dataPoint.days,
              data: {
                days: dataPoint.days || [],
                capacities: dataPoint.capacities || []
              }
            };
          });

          // 绘制图表
          this.$nextTick(() => {
            this.renderCapacityCurveChart();
          });

          message.success('预测曲线计算完成');
        } else {
          message.error(response.data.message || '预测曲线计算失败');
        }
      } catch (error) {
        console.error('计算预测曲线出错:', error);
        message.error('计算预测曲线请求失败');
      } finally {
        this.calculatingPrediction = false;
      }
    },

    // 渲染容量曲线图表
    renderCapacityCurveChart() {
      if (this.predictionCurves.length === 0) return;

      try {
        // 导入echarts和图表工具函数
        const echarts = require('echarts');
        const { createLineSeries, createCapacityCurveOptions, createTooltipFormatter } = require('@/utils/chartUtils');

        // 确保DOM元素存在
        if (!this.$refs.capacityCurveChart) {
          console.warn('图表DOM元素不存在');
          return;
        }

        // 初始化图表
        const chartDom = this.$refs.capacityCurveChart;
        const chart = echarts.init(chartDom);

        // 准备数据系列
        const series = [];

        this.predictionCurves.forEach((curve, index) => {
          // 组装数据点，并格式化为小数点后4位
          const days = curve.data.days || [];
          const capacities = curve.data.capacities || [];
          const dataPoints = days.map((day, i) => [
            parseFloat(day.toFixed(4)),
            parseFloat((capacities[i] * 100).toFixed(4))
          ]);

          // 创建系列 - 使用折线图
          series.push(createLineSeries(
            `#${curve.id}: ${curve.temperature}°C, SOC ${(curve.soc * 100).toFixed(0)}%`,
            dataPoints,
            index,
            true,
            {
              symbolSize: 0,
              showSymbol: false,
              lineStyle: {
                width: 2
              }
            }
          ));
        });

        // 设置图表选项
        const option = createCapacityCurveOptions('', series, { // 删除图表上方的标签
          legendPosition: 'bottom',
          gridConfig: {
            bottom: 80,
            top: 30 // 减小上方间距
          },
          tooltip: {
            formatter: createTooltipFormatter
          }
        });

        // 应用选项并渲染图表
        chart.setOption(option);

        // 监听窗口大小变化，自动调整图表大小
        const resizeChart = () => chart.resize();
        window.addEventListener('resize', resizeChart);

        // 当组件销毁时移除事件监听
        this.$once('hook:beforeDestroy', () => {
          window.removeEventListener('resize', resizeChart);
          chart.dispose();
        });

      } catch (error) {
        console.error('渲染容量曲线图表出错:', error);
        message.error('渲染容量曲线图表失败');
      }
    }
  }
};
</script>

<style scoped>
.prediction-curve-section {
  padding: 16px;
  background-color: transparent;
  border: none;
  margin-top: 8px;
}

.prediction-params-card,
.capacity-curve-card {
  background-color: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  height: 100%;
}

.prediction-params-content,
.capacity-curve-content {
  padding: 16px;
  height: calc(100% - 46px);
}

h5 {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 0;
  color: #333;
  padding: 10px 0;
  background-color: #f5f5f5;
  border-bottom: 1px solid #f0f0f0;
  text-align: center;
  border-radius: 8px 8px 0 0;
}

/* 表单式参数设置样式 */
.prediction-params-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.param-group-card {
  background-color: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.param-group-card:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.param-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f0f0f0;
  border-bottom: 1px solid #e8e8e8;
}

.param-group-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.param-group-content {
  padding: 12px 16px;
  background-color: #fff;
  min-width: 0; /* 确保flex容器能够收缩 */
}

/* 一行紧凑排列样式 */
.param-inline-form {
  display: flex !important;
  align-items: center;
  gap: 8px;
  flex-wrap: nowrap !important; /* 强制不换行 */
  justify-content: flex-start;
  width: 100%;
  overflow-x: auto; /* 如果内容过宽，允许水平滚动 */
  min-height: 32px;
}

.param-inline-item {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 0 0 auto;
  min-width: 90px;
  white-space: nowrap;
}

.param-inline-item:not(:last-child) {
  margin-right: 4px;
}

.param-label {
  font-size: 12px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  min-width: 35px;
  text-align: right;
  flex-shrink: 0;
}

.param-input-wrapper {
  display: flex;
  align-items: center;
  position: relative;
  flex-shrink: 0;
}

.param-input {
  width: 65px !important;
  flex-shrink: 0;
}

.param-input :deep(.ant-input-number-input) {
  padding: 0 4px !important;
  font-size: 12px !important;
}

.param-unit {
  margin-left: 3px;
  color: #999;
  font-size: 11px;
  font-weight: 500;
  min-width: 14px;
  white-space: nowrap;
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .param-inline-form {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .param-inline-item {
    min-width: auto;
    max-width: none;
    justify-content: space-between;
    margin-right: 0;
  }

  .param-label {
    text-align: left;
    min-width: 70px;
  }

  .param-input {
    width: 90px;
  }
}

/* 删除按钮样式 */
.delete-button {
  color: #ff4d4f;
  transition: all 0.3s;
  padding: 4px 8px;
  border-radius: 4px;
}

.delete-button:hover {
  color: #ff7875;
  background-color: #fff2f0;
}

.delete-button:focus {
  color: #ff4d4f;
  background-color: #fff2f0;
}

.add-prediction-action {
  margin-top: 16px;
  margin-bottom: 16px;
}

.add-prediction-button {
  height: 36px;
  font-weight: 500;
  border-color: #d9d9d9;
  color: #595959;
}

.add-prediction-button:hover {
  color: #40a9ff;
  border-color: #40a9ff;
}

.run-prediction-action {
  margin-top: 16px;
}

.calculate-prediction-button {
  height: 40px;
  font-weight: 500;
  font-size: 15px;
}

.empty-chart {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 600px;
  background-color: #fafafa;
  border-radius: 4px;
  border: 1px dashed #d9d9d9;
}

.empty-chart-content {
  text-align: center;
  padding: 24px;
}

.empty-chart-icon {
  font-size: 48px;
  color: #1890ff;
  margin-bottom: 16px;
}

.empty-chart-content h3 {
  font-size: 18px;
  color: #333;
  margin-bottom: 8px;
}

.empty-chart-content p {
  font-size: 14px;
  color: #666;
}

.capacity-curve-chart {
  height: 600px;
  width: 100%;
}
</style>
