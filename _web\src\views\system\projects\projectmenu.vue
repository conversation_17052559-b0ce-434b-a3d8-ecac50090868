<template>
	<div>
		<div class="layout-main">
			<!-- 导航菜单 start -->
			<div @mouseenter.stop="mouseenter" @mouseleave.stop="mouseleave" class="slide" :style="{ overflowY: 'hiden' ,position: 'fixed', left:0}">
				<a-menu
					:inlineIndent="12"
					style="width: 200px"
					:default-selected-keys="[navTag]"
					mode="inline"
					theme="light"
					:inline-collapsed="collapsed"
				>
					<a-sub-menu key="A">
						<span slot="title"><a-icon type="calendar" /><span>项目管理</span></span>
						<a-menu-item key="1" @click="showView('A', 1)">
							<a-icon type="info-circle" />
							<span>产品信息</span>
						</a-menu-item>

						<a-menu-item key="15" @click="showView('A', 15)">
							<a-icon type="copy" />
							<span>参数信息</span>
						</a-menu-item>
						<!-- <a-menu-item key="16" @click="showView('A',16)">
            <a-icon type="copy" />
            <span>样品管理</span>
          </a-menu-item> -->
						<a-menu-item key="2" @click="showView('A', 2)">
							<a-icon type="reconciliation" />
							<span>产能</span>
						</a-menu-item>

						<a-menu-item key="4" @click="showView('A', 4)">
							<a-icon type="apartment" />
							<span>项目架构</span>
						</a-menu-item>
						<a-menu-item key="5" @click="showView('A', 5)">
							<a-icon type="table" />
							<span>项目里程碑</span>
						</a-menu-item>
						<a-menu-item key="6" @click="showView('A', 6)">
							<a-icon type="edit" />
							<span>客户议题</span>
						</a-menu-item>
						<a-menu-item key="7" @click="showView('A', 7)">
							<a-icon type="desktop" />
							<span>甘特图</span>
						</a-menu-item>
						<a-menu-item key="8" @click="showView('A', 8)">
							<a-icon type="desktop" />
							<span>周进展</span>
						</a-menu-item>
						<a-menu-item key="11" @click="showView('A', 11)">
							<a-icon type="question-circle" />
							<span>问题升级管理</span>
						</a-menu-item>
						<!-- <a-menu-item key="8" @click="showView('A',8)">
            <a-icon type="user" />
            <span>任务管理</span>
          </a-menu-item>
          <a-menu-item key="9" @click="showView('A',9)">
            <a-icon type="hdd" />
            <span>制样管理</span>
          </a-menu-item> -->
					</a-sub-menu>

					<a-sub-menu key="B" v-if="projectdetail.productOrProject == 1">
						<span slot="title"><a-icon type="calendar" /><span>产品管理</span></span>
						<a-menu-item v-if="hasPerm('project:bom')" key="18" @click="showView('B', 18)">
							<a-icon type="copy" />
							<span>电芯BOM管理</span>
						</a-menu-item>
						<a-menu-item v-if="hasPerm('project:bom')" key="19" @click="showView('B', 19)">
							<a-icon type="copy" />
							<span>包装BOM管理</span>
						</a-menu-item>
						<a-menu-item v-if="hasPerm('sysBomEnd:page')" key="17" @click="showView('B', 17)">
							<a-icon type="copy" />
							<span>成品BOM管理</span>
						</a-menu-item>
						<a-menu-item v-if="hasPerm('doc:list')" key="13" @click="showView('B', 13)">
							<a-icon type="copy" />
							<span>技术文档</span>
						</a-menu-item>
					</a-sub-menu>

					<a-sub-menu key="C">
						<span slot="title"><a-icon type="calendar" /><span>质量管理</span></span>
						<!-- <a-menu-item key="10" @click="showView('C',10)">
              <a-icon type="experiment" />
              <span>测试管理</span>
            </a-menu-item> -->
						
						<a-menu-item key="12" @click="showView('C', 12)">
							<a-icon type="warning" />
							<span>风险管理</span>
						</a-menu-item>
						<a-menu-item key="3" @click="showView('C', 3)">
							<a-icon type="file" />
							<span>转阶段文档</span>
						</a-menu-item>
					</a-sub-menu>

					<a-menu-item key="14" @click="toProjectChart()">
						<a-icon type="radar-chart" />
						<span>项目看板</span>
					</a-menu-item>
				</a-menu>

				<!-- <div class="collapsed_bar" :style="collapsed ? width40 : width187" @click="toggleCollapsed">
					<a-icon class="collapsed_btn" :type="collapsed ? 'menu-unfold' : 'menu-fold'" />
				</div> -->
			</div>
			<!-- 导航菜单 end -->

			<div class="wrap" :style="{ padding: '0', overflowX: 'auto' }">
				<!-- 面包屑 start -->
				<div>
					<a-breadcrumb class="breadcrumb" separator=">">
						<a-breadcrumb-item><a @click="goBack">产品列表</a></a-breadcrumb-item>
						<a-breadcrumb-item>{{ projectdetail.productProjectName }}</a-breadcrumb-item>
						<a-breadcrumb-item>{{ menuTitle }}</a-breadcrumb-item>
					</a-breadcrumb>
				</div>
				<!-- 面包屑 end -->

				<!-- container start -->
				<div class="content" :style="`height:${contentHeight}px`">
					<detailview
						@ok="callProjectDetail"
						v-if="navTag == '1'"
						:issueId="issueId"
						:projectdetail="projectdetail"
						:loading="loading"
					/>
					<docs v-if="navTag == '13'" :issueId="issueId" />
					<stagetrouble v-if="navTag == '11'" :issueId="issueId" :projectdetail="projectdetail" />
					<stagerisk v-if="navTag == '12'" :issueId="issueId" :productCustomer="projectdetail.customer" />
					<!-- <material v-if="navTag == '15'" :issueId="issueId"/>
        <sample v-if="navTag == '16'" :issueId="issueId"/> -->
					<stagedocs @showView="showView" v-if="navTag == '3'" :issueId="issueId" :projectdetail="projectdetail" />
					<frameword v-if="navTag == '4'" :issueId="issueId" :projectdetail="projectdetail" />
					<meeting v-if="navTag == '6'" :issueId="issueId" :projectdetail="projectdetail" />
					<endbom v-if="navTag == '17'" :issueId="issueId" />
					<!--<suppliers v-if="navTag == '2'" :issueId="issueId" />-->
					<capacity v-if="navTag == '2'" :issueId="issueId" :projectdetail="projectdetail" />
					<detail v-if="navTag == '18'" :issueId="issueId" :projectdetail="projectdetail" />
					<detailpack v-if="navTag == '19'" :issueId="issueId" :projectdetail="projectdetail" />
					<projectPlan v-if="navTag == '5'" :issueId="issueId" :projectdetail="projectdetail" />
					<addparam v-if="navTag == '15'" :issueId="issueId" :projectdetail="projectdetail" />
					<weekprocess v-if="navTag == '8'" :issueId="issueId" :projectdetail="projectdetail" />
					<gant v-if="navTag == '7'" :issueId="issueId" />
					<!-- container end -->
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { getProjectDetail } from "@/api/modular/system/report"
import detailview from "./detailview"
import docs from "../techdoc/index"
import stagedocs from "../docs/index"
/*   import material from '../bommaterial/index' */
import stagetrouble from "../stage/stagetrouble"
import stagerisk from "../risk/stagerisk"
/*  import sample from '../bomsample/index' */
import frameword from "../report/project/frameword"
import meeting from "../report/project/meeting"
import endbom from "./endbom"
import suppliers from "./suppliers"
import detail from "./detail"
import detailpack from "./detailpack"
import gant from "./gant"
import projectPlan from "./create/projectPlan"
import capacity from "./capacity/index"
import addparam from "./create/addParam"
import weekprocess from "./weekprocess/index"
import { mixin } from "@/utils/mixin"
import { mapActions } from "vuex"
export default {
	mixins: [mixin],
	components: {
		detailview,
		docs,
		stagetrouble,
		stagerisk,
		//material,
		//sample,
		stagedocs,
		frameword,
		meeting,
		endbom,
		suppliers,
		detail,
		detailpack,
		projectPlan,
		gant,
		addparam,
		weekprocess,
		capacity
	},
	
	created() {
		this.issueId = parseInt(this.$route.query.issueId)
		this.callProjectDetail()
		
		if (window.sessionStorage.getItem('navTag') !== null) {

			this.navTag = window.sessionStorage.getItem('navTag');
			this.openTag = window.sessionStorage.getItem('openTag')
			this.projectdetail = JSON.parse(window.sessionStorage.getItem("project_detail"))
			this.menuTitle = this.menusinfos[this.navTag]
		}

		this.collapsed = !this.sidebarOpened
	},
	watch: {
		sidebarOpened(val) {
			this.collapsed = !val
		}
	},
	data() {
		return {
			menuTitle: "",
			menusinfos: {
				1: "产品信息",
				15: "参数信息",
				16: "样品管理",
				2: "产能",
				4: "项目架构",
				5: "项目计划",
				6: "客户议题",
				7: "甘特图",
				18: "电芯BOM管理",
				19: "包装BOM管理",
				17: "成品BOM管理",
				13: "技术文档",
				11: "问题管理",
				12: "风险管理",
				3: "转阶段文档",
				8: "周进展"
			},
			issueId: null,
			loading: true,
			projectdetail: {},
			navTag: "1",
			openTag: "A",
			collapsed: false,
			width187: {
				width: "187px",
				textAlign: "right"
			},
			width40: {
				width: "40px",
				textAlign: "center"
			},
      // 内容高度（不包含面包屑）
      // 40:标题高度 32:面包屑高度
      contentHeight:document.documentElement.clientHeight - 40 - 32 - 16
		}
	},
	methods: {
		...mapActions(["setSidebar"]),
		goBack() {
			this.$router.push({
				path: "/projects_list"
			})
		},
		toProjectChart() {
			window.open(
				"/project_chart?issueId=" + this.projectdetail.issueId + "&title=" + this.projectdetail.productProjectName,
				"_blank"
			)
		},
		mouseenter() {
			this.collapsed = true
			this.setSidebar(this.collapsed)
		},
		mouseleave() {
			this.collapsed = false
			this.setSidebar(this.collapsed)
		},
		showView(opentg, tg) {
			this.openTag = opentg + ""
			this.navTag = tg + ""
			window.sessionStorage.setItem('navTag',tg)
			window.sessionStorage.setItem('openTag',opentg)
			this.menuTitle = this.menusinfos[this.navTag]
		},
		callProjectDetail() {
			this.loading = true
			let params = { issueId: this.issueId, title: "" }
			getProjectDetail(params)
				.then(res => {
					if (res.result) {
						this.projectdetail = res.data
						if (this.$route.query.nav) {
							setTimeout(() => {
								this.showView(this.$route.query.open, this.$route.query.nav)
							}, 100);
							
						}
						window.sessionStorage.setItem("project_detail", JSON.stringify(res.data))
						this.menuTitle = this.menusinfos[this.navTag]
					} else {
						this.$message.error(res.message, 1)
					}
					this.loading = false
				})
				.catch(err => {
					this.loading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		}
	},
	mounted() {
		if (window.sessionStorage.getItem('navTag') == null) {

			window.sessionStorage.setItem('navTag',this.navTag)
			window.sessionStorage.setItem('openTag',this.openTag)

		} else if (window.sessionStorage.getItem('navTag') !== null) {
			this.navTag = window.sessionStorage.getItem('navTag')
			this.openTag = window.sessionStorage.getItem('openTag')
			this.projectdetail = JSON.parse(window.sessionStorage.getItem("project_detail"))
		}
		
	},
	destroyed() {
		window.sessionStorage.removeItem('navTag')
		window.sessionStorage.removeItem('openTag')
		window.sessionStorage.removeItem("project_detail")
	}
}
</script>

<style lang="less" scoped>
.layout-main {
	display: flex;
}
.wrap {
	flex: 1;
}
.slide {
	max-width: 200px;
	background-color: #fff;
	box-shadow: 2px 0px 8px 0px rgba(29, 35, 41, 5%);
	z-index: 100;
  // 40标题高度
	height: calc(100vh - 40px);
}
.collapsed_bar {
	position: fixed;
	width: 20px;
	bottom: 0;
	/* top: 0; */
	cursor: pointer;
}
/*  .collapsed_btn{
    position: absolute;
    top: 45%;
  } */
/deep/.ant-menu-light {
	border-right-color: transparent;
}
/deep/.ant-menu-inline-collapsed {
	width: 40px !important;
}
/deep/.ant-layout-sider-collapsed {
	flex: 0 0 40px !important;
	max-width: 40px !important;
	min-width: 40px !important;
	width: 40px !important;
}
/deep/.ant-menu-inline-collapsed > .ant-menu-item,
/deep/.ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item,
.ant-menu-inline-collapsed
	> .ant-menu-item-group
	> .ant-menu-item-group-list
	> .ant-menu-submenu
	> .ant-menu-submenu-title,
.ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title {
	padding: 0 12px !important;
}
// 面包屑
.breadcrumb {
	padding:10px;
}
.ant-breadcrumb a {
	color: #5d90fa !important;
}
.ant-breadcrumb {
  line-height: 1;
	font-size: 12px !important;
	color: rgba(0, 0, 0, 0.65) !important;
}
</style>
<style>
.ant-layout-sider-collapsed {
	flex: 0 0 40px !important;
	max-width: 40px !important;
	min-width: 40px !important;
	width: 40px !important;
}
.ant-menu-inline-collapsed > .ant-menu-item,
.ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item,
.ant-menu-inline-collapsed
	> .ant-menu-item-group
	> .ant-menu-item-group-list
	> .ant-menu-submenu
	> .ant-menu-submenu-title,
.ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title {
	padding: 0 12px !important;
}
.ant-menu-vertical .ant-menu-item,
.ant-menu-vertical-left .ant-menu-item,
.ant-menu-vertical-right .ant-menu-item,
.ant-menu-inline .ant-menu-item,
.ant-menu-vertical .ant-menu-submenu-title,
.ant-menu-vertical-left .ant-menu-submenu-title,
.ant-menu-vertical-right .ant-menu-submenu-title,
.ant-menu-inline .ant-menu-submenu-title {
	font-size: 13px;
}
</style>
