<template>
  <a-modal title="新增" :width="800" :height="600"
           :bodyStyle="{padding:0}"
           :visible="visible" :confirmLoading="confirmLoading" @ok="handleSubmit" style="padding: 0"
           :maskClosable="false"
           @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-tabs v-model="activeKey" @change="changeTab">
        <a-tab-pane key="basic" tab="基础信息">
          <a-form :form="form">
            <a-row :gutter="24">
              <a-col :md="20" :sm="20">

                <a-form-item label="选择复制来源" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-select
                    style="width: 100%"
                    :filter-option="false"
                    @search="searchFolder"
                    :showSearch="true"
                    @change="changeSource"
                  >
                    <a-select-option v-for="d in folderData" :key="d.id">
                      {{ d.testCode }}
                    </a-select-option>
                  </a-select>
                </a-form-item>


              </a-col>


            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="测试状态" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>

                  <a-select v-decorator="['testStatus', {rules: [{required: true, message: '请选择测试状态！'}]}]"
                            style="width: 100%" placeholder="请选择测试状态">

                    <a-select-option value="Done">
                      Done
                    </a-select-option>
                    <a-select-option value="Ongoing">
                      Ongoing
                    </a-select-option>
                    <a-select-option value="Plan">
                      Plan
                    </a-select-option>
                    <a-select-option value="Stop">
                      Stop
                    </a-select-option>

                  </a-select>

                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="测试申请单" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input placeholder="请输入测试申请单"
                           v-decorator="['testCode', {rules: [{required: true, message: '请输入测试申请单！'}]}]"/>
                </a-form-item>
              </a-col>

            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="产品名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入产品名称"
                           v-decorator="['productName', {rules: [{required: true, message: '请输入产品名称！'}]}]"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="产品样品阶段" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入产品样品阶段"
                           v-decorator="['productSampleStage', {rules: [{required: true, message: '请输入产品样品阶段！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="测试类型" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-select v-decorator="['testType', {rules: [{required: true, message: '请选择测试类型！'}]}]"
                            style="width: 100%" placeholder="请选择测试类型">

                    <a-select-option value="研发测试">
                      研发测试
                    </a-select-option>
                    <a-select-option value="产品验证测试">
                      产品验证测试
                    </a-select-option>
                    <a-select-option value="产品鉴定测试">
                      产品鉴定测试
                    </a-select-option>

                  </a-select>

                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="申请部门" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入申请部门"
                           v-decorator="['dept', {rules: [{required: true, message: '请输入申请部门！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="申请人" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入申请人"
                           v-decorator="['applicant', {rules: [{required: true, message: '请输入申请人！'}]}]"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="测试项目" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入测试项目"
                           v-decorator="['testProject', {rules: [{required: true, message: '请输入测试项目！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="T/℃" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入T/℃"
                           v-decorator="['t', {rules: [{required: true, message: '请输入T/℃！'}]}]"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="SOC" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input placeholder="请输入soc"
                           v-decorator="['soc', {rules: [{required: true, message: '请输入soc！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="测试周期" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input-number placeholder="请输入存储天数" :min="1" :precision="0"
                                  v-decorator="['testPeriod', {rules: [{required: true, message: '请输入存储天数！'}]}]"/>

                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="数量" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input-number placeholder="请输入数量" :min="1" :precision="0"
                                  v-decorator="['quantity', {rules: [{required: true, message: '请输入数量！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>


            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="测试技师" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input type='hidden' v-decorator="['testMan', {rules: [{required: true, message: '请选择测试技师！'}]}]" />
                  <a-dropdown v-model="testManDownVisible" placement="bottomCenter" :trigger="['click']">
                    <a-button class="man_button" :style="{color:testMan?'rgba(0, 0, 0, 0.65)':'#b7b7b7'}">{{testMan ? testMan : '选择测试技师'}}
                      <a-icon type="down" /></a-button>
                    <a-menu slot="overlay">
                      <a-spin :spinning="testManLoading" style="padding:10px 24px 0 24px;width:100%">
                        <a-input-search v-model="testManQueryParam.searchValue" placeholder="搜索..." @change="ontestManSearch" />
                        <s-table style="width:100%;" ref="testManTable" :rowKey="(record) => record.id" :columns="vColumns" :data="loadtestManData" :customRow="customtestManRow" :scroll="{ y: 120,x:120}">
                        </s-table>
                      </a-spin>
                    </a-menu>
                  </a-dropdown>
                </a-form-item>




              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="测试地点" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-select v-decorator="['testAddress', {rules: [{required: true, message: '请选择测试地点！'}]}]"
                            style="width: 100%"  placeholder="请选择测试地点">

                    <a-select-option value="A1_3F">
                      V圆柱检测室
                    </a-select-option>

                    <a-select-option value="R2_2F">
                      材料验证检测室
                    </a-select-option>

                    <a-select-option value="R4_4F">
                      动力电池检测室
                    </a-select-option>

                    <a-select-option value="JM">
                      第六实验室(JM)
                    </a-select-option>

                  </a-select>

                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">


                <a-form-item label="电芯载体" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">

                  <a-select v-decorator="['sampleType', {rules: [{required: true, message: '请选择电芯载体！'}]}]"
                            style="width: 100%"  placeholder="请选择电芯载体">

                    <a-select-option value="G圆柱">
                      G圆柱
                    </a-select-option>
                    <a-select-option value="C圆柱">
                      C圆柱
                    </a-select-option>
                    <a-select-option value="V圆柱">
                      V圆柱
                    </a-select-option>
                    <a-select-option value="方型">
                      方型
                    </a-select-option>
                    <a-select-option value="软包">
                      软包
                    </a-select-option>
                    <a-select-option value="模组">
                      模组
                    </a-select-option>

                  </a-select>
                </a-form-item>
              </a-col>

              <a-col :md="12" :sm="24">


                <a-form-item label="存储位置" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">

                  <a-input placeholder="请输入存储位置"
                           v-decorator="['saveAddress', {rules: [{required: true, message: '请输入存储位置！'}]}]"/>

                </a-form-item>
              </a-col>

            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="测试目的" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input placeholder="请输入测试目的"
                           v-decorator="['testPurpose']"/>
                </a-form-item>
              </a-col>

              <a-col :md="12" :sm="24">
                <a-form-item label="是否阶段式" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-select v-decorator="['stageFlag', {rules: [{required: true, message: '请输入是否阶段式！'}]}]"
                            style="width: 100%"  placeholder="请选择是否阶段式">
                    <a-select-option value=1>
                      是
                    </a-select-option>
                    <a-select-option value=0>
                      否
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="湿度/%RH" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input-number placeholder="湿度/%RH"
                                  v-decorator="['humidity']"/>
                </a-form-item>
              </a-col>

              <a-col :md="12" :sm="24">
                <a-form-item label="样品类型" has-feedback :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-select v-decorator="['sampleOrderType']" style="width: 100%">
                    <a-select-option value="mz">
                      模组
                    </a-select-option>
                    <a-select-option value="dx">
                      电芯
                    </a-select-option>
                    <a-select-option value="dxlc">
                      电芯-Live Cell
                    </a-select-option>
                    <a-select-option value="dxdc">
                      电芯-Dummy Cell
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <!-- <a-row :gutter="24">

               <a-col :md="12" :sm="24">
                 <a-form-item label="已完成天数" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                   <a-input placeholder="请输入已完成天数"
                            v-decorator="['finishDay', {rules: [{required: true, message: '请输入已完成天数！'}]}]"/>
                 </a-form-item>
               </a-col>
             </a-row>-->
          </a-form>
        </a-tab-pane>
        <a-tab-pane key="test" tab="中检信息">
          <a-form :form="form">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">

                <a-form-item label="中检次数" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-input-number placeholder="请输入中检次数" @change="changeNum" :min="1" :precision="0"
                                  v-decorator="['testNum', {rules: [{required: true, message: '请输入中检次数！'}]}]"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">

                <a-form-item label="进箱开始时间" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                  <a-date-picker :allowClear="false" placeholder=""
                                 v-decorator="['zero', {rules: [{required: true, message: '请选择进箱开始时间！'}]}]"
                                 @change="changeZero($event)">
                  </a-date-picker>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
          <a-table :columns="columns" :data-source="data" bordered
                   style="padding: 20px;height: 280px"
                   bordered :scroll="{y:210}"
                   :rowKey="(record) => record.uuid"
                   :pagination="false"
          >

            <template slot="day" slot-scope="text, record, index">
              <a-input-number :value="text" @change="changeDay($event,index,record)" :min="0" :precision="0"
                              size="small"
                              style="border: 0;"></a-input-number>
            </template>

            <template slot="footer">
              <a @click="addData">
                <a-icon type="plus" style="width: 15px;height: 15px;margin-left: 50px;cursor: pointer"/>
              </a>
            </template>

          </a-table>

        </a-tab-pane>
      </a-tabs>


    </a-spin>
  </a-modal>
</template>

<script>
  import {
    testProgressAdd,listToCopy,get
  } from '@/api/modular/system/testProgressManager'
  import {
    getUserLists
  } from '@/api/modular/system/userManage'
  import moment from "moment";
  import {
    STable
  } from '@/components'

  export default {
    components: {
      STable
    },
    props: {
      address: {
        type: String,
        default: null
      },
    },
    data() {
      return {
        folderData:[],
        fetching: false,
        source:null,
        loadtestManData: parameter => {
          return getUserLists(Object.assign(parameter, this.testManQueryParam)).then((res) => {
            return res.data
          })
        },
        vColumns: [{
          title: '账号',
          dataIndex: 'account'
        },
          {
            title: '姓名',
            dataIndex: 'name'
          },
        ],
        data: [],
        zero: null,
        testNum: null,
        testMan:null,
        testManId:null,
        testManLoading:false,
        testManQueryParam:{},
        testManDownVisible:false,

        loadSzData: parameter => {
          return getUserLists(Object.assign(parameter, this.szQueryParam)).then((res) => {
            return res.data
          })
        },
        columns: [
          {
            title: '存储阶段',
            dataIndex: 'index',
            align: 'center',
            width: 50,
            customRender: (text, record, index) => `存储第${index + 1}次阶段`
          }, {
            title: '存储天数',
            width: 90,
            align: 'center',
            dataIndex: 'day',
            scopedSlots: {
              customRender: 'day'
            },
          }, {
            title: '开始时间',
            width: 90,
            align: 'center',
            dataIndex: 'inDate'
          }, {
            title: '结束时间',
            width: 90,
            align: 'center',
            dataIndex: 'outDate'
          }
        ],
        activeKey: 'basic',
        labelCol: {
          xs: {
            span: 12
          },
          sm: {
            span: 8
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 14
          }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this),
      }
    },

    methods: {
      changeTab(a){
        if(a == 'test' && this.source != null && (this.form.getFieldValue('zero') == null
          || this.form.getFieldValue('zero') == undefined)){
          this.data = []
          this.source.data.forEach(d => {
            let param = {}
            param.day = d.day
            param.orderNumber = d.orderNumber
            param.inDate = d.inDate
            param.outDate = d.outDate
            this.data.push(param)
          })
          this.$nextTick(() => {
            this.form.setFieldsValue({
              testNum:this.source.data.length,
              zero:this.source.data.length > 0?moment(this.source.data[0].inDate):null
            })
          })
        }


      },
      changeSource(a){

        get({id:a}).then(res => {
          this.source = res.data
        }).then(() => {

          this.data = []
          this.source.data.forEach(d => {
            let param = {}
            param.day = d.day
            param.orderNumber = d.orderNumber
            param.inDate = d.inDate
            param.outDate = d.outDate
            this.data.push(param)
          })

          this.form.setFieldsValue({
            testStatus:this.source.testStatus,
            testCode:this.source.testCode,
            productName:this.source.productName,
            productSampleStage:this.source.productSampleStage,
            testType:this.source.testType,
            dept:this.source.dept,
            applicant:this.source.applicant,
            testProject:this.source.testProject,
            t:this.source.t,
            soc:this.source.soc,
            testPeriod:this.source.testPeriod,
            quantity:this.source.quantity,
            testMan:this.source.testMan,
            testManId:this.source.testManId,
            testAddress:this.source.testAddress,
            sampleType:this.source.sampleType,
            humidity:this.source.humidity,
            sampleOrderType:this.source.sampleOrderType,
            stageFlag:this.source.stageFlag,
            testPurpose:this.source.testPurpose,
            saveAddress:this.source.saveAddress,
            testNum:this.source.data.length,
            zero:this.source.data.length > 0?moment(this.source.data[0].inDate):null
          })

        })
      },
      searchFolder(value){

        let param = {}
        param.keyword = value
        if(this.address != null  && this.address != 'all'){
          param.testAddress = this.address
        }

        listToCopy(param).then(res => {
          this.folderData = res.data
        }).finally(res => this.fetching = true)
      },
      ontestManSearch(e) {
        this.$refs.testManTable.refresh()
      },
      customtestManRow(row, index) {
        return {
          on: {
            click: () => {
              this.form.setFieldsValue({
                testMan: row.name,
                testManId: row.account
              })
              this.testMan = row.name
              this.testManId = row.account
              this.testManDownVisible = false
            }
          }
        }
      },
      changeZero(event) {

        for (let i = 0; i < this.data.length; i++) {
          if (this.data[i].day == null) {
            if (i == 0) {
              this.data[i].day = 15
            } else if (i == 1) {
              this.data[i].day = 15
            } else {
              this.data[i].day = 30
            }
          }
        }

        //this.zero = moment(event).format('YYYY-MM-DD')
        if (this.data.length > 0) {
          this.data[0].inDate = moment(event).format('YYYY-MM-DD')
          if (this.data[0].day != null) {
            this.data[0].outDate = moment(event).add(this.data[0].day, 'days').format('YYYY-MM-DD')
          }
        }

        this.changeInOutDate(1);

      },

      changeInOutDate(index) {
        if (index == 0) {

          if (this.form.getFieldValue('zero') != null) {
            this.data[0].inDate = moment(this.form.getFieldValue('zero')).format('YYYY-MM-DD')

            if (this.data[0].day != null && this.data[0].inDate != null) {
              this.data[0].outDate = moment(this.data[0].inDate).add(this.data[0].day, 'days').format('YYYY-MM-DD')
            }
          }

          index += 1;
        }
        for (let i = index; i < this.data.length; i++) {
          let before = this.data[i - 1]
          if (before.outDate != null) {
            if(index == 1 && before.day == 0){
              this.data[i].inDate = before.outDate
            }else{
              this.data[i].inDate = moment(before.outDate).add(3, 'days').format('YYYY-MM-DD')
            }

            if (this.data[i].inDate != null && this.data[i].day != null) {
              this.data[i].outDate = moment(this.data[i].inDate).add(this.data[i].day, 'days').format('YYYY-MM-DD')
            }
          }
        }


      },

      changeNum($event) {

        if (this.data.length > $event) {
          this.data = this.data.slice(0, $event)
        } else {
          for (let i = this.data.length; i < $event; i++) {
            this.data.push({uuid: i * 10000 + 1000, day: null, inDate: null, outDate: null})
          }
        }

        this.changeInOutDate(0);


      },
      addData() {
        this.data.push({uuid: this.data.length * 10000 + 1000, day: null, inDate: null, outDate: null})
        this.form.setFieldsValue(
          {
            testNum: this.data.length
          }
        )

      },

      changeDay($event, index, record) {

       /* if (this.data.length > index + 2 && this.data[index + 1].day != null) {

          if (this.data[index + 1].day <= $event) {
            this.$message.warn('天数应小于下次中检天数')
            return
          }
        }

        if (index > 0) {
          if (this.data[index - 1] != null) {

            if (this.data[index - 1].day >= $event) {
              this.$message.warn('天数应大于上次中检天数')
              return
            }
          }
        }*/


        record.day = $event
        this.changeInOutDate(index);
      },

      add() {

        this.visible = true

        let param = {}
        if(this.address != null  && this.address != 'all'){
          param.testAddress = this.address
        }

        listToCopy(param).then(res => {
          this.folderData = res.data
        }).then(() => {
          if(this.address != null  && this.address != 'all'){
            this.form.setFieldsValue({
              testAddress:this.address
            })
          }
        }).finally(res => this.fetching = true)
      },
      onChangeSampleDate(date, dateString) {
        if (date == null) {
          this.startDate = ''
        } else {
          this.startDate = moment(date).format('YYYY-MM-DD')
        }
      },
      handleSubmit() {
        const {
          form: {
            validateFields
          }
        } = this

        this.confirmLoading = true
        validateFields((errors, values) => {

          if (!errors) {
            if (this.data.length == 0) {
              this.$message.warn('请填写第中检信息')
              this.activeKey = 'test'
              this.confirmLoading = false
              return
            }
            for (let i = 0; i < this.data.length; i++) {
              if (this.data[i].day == null) {
                this.$message.warn('请填写第' + (i + 1) + '次中检天数')
                this.activeKey = 'test'
                this.confirmLoading = false
                return
              }
            }

            values.data = this.data
            values.testManId = this.testManId
            testProgressAdd(values).then((res) => {
              this.confirmLoading = false
              if (res.success) {
                this.$message.success('新增成功')
                this.handleCancel()
                this.$emit('ok')
              } else {
                this.$message.error('新增失败：' + res.message)
              }
            }).finally((res) => {
              this.data = []
              this.form.resetFields()
              this.confirmLoading = false
            })
          } else {
            if (this.activeKey == 'test') {
              if (values.testNum != null && values.zero != null) {
                this.activeKey = 'basic'
              }
            }
            if (this.activeKey == 'basic') {

              if (values.testStatus != null && values.testCode != null
                && values.productName != null
                && values.productSampleStage != null
                && values.testType != null
                && values.dept != null
                && values.applicant != null
                && values.testProject != null
                && values.t != null
                && values.soc != null
                && values.testPeriod != null
                && values.quantity != null
                && values.testMan != null
                && values.testManId != null
                && values.testAddress != null
                && values.sampleType != null
                && values.humidity != null
                && values.sampleOrderType != null
                && values.stageFlag != null
                && values.testPurpose != null
                && values.saveAddress != null
              ) {
                this.activeKey = 'test'

                if(this.source != null && (this.form.getFieldValue('zero') == null
                  || this.form.getFieldValue('zero') == undefined)){
                  this.data = []
                  this.source.data.forEach(d => {
                    let param = {}
                    param.day = d.day
                    param.orderNumber = d.orderNumber
                    param.inDate = d.inDate
                    param.outDate = d.outDate
                    this.data.push(param)
                  })
                  this.$nextTick(() => {
                    this.form.setFieldsValue({
                      testNum:this.source.data.length,
                      zero:this.source.data.length > 0?moment(this.source.data[0].inDate):null
                    })
                  })
                }

              }
            }
            this.confirmLoading = false
          }
        })
      },
      handleCancel() {
        this.visible = false
      }
    }
  }
</script>
<style lang="less" scoped>
  .ant-form-item {

    margin-bottom: 0px;

  }

  .man_button{
    padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;
  }

  /deep/ .ant-modal-body {
    padding: 0 !important;
  }

  /deep/ .ant-table-thead > tr > th, /deep/ .ant-table-tbody > tr > td {
    padding: 3px;
  }

  /deep/ .ant-table-footer {

    padding: 0px;
  }

  /deep/ .ant-table-pagination.ant-pagination {
    margin: 5px 0;
  }

  /deep/ .ant-input-number {
    width: 100%;
  }

  /deep/ .ant-input-number-sm > .ant-input-number-input-wrap > .ant-input-number-input {
    text-align: center;
  }

</style>
