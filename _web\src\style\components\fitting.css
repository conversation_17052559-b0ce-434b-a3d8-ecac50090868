/**
 * 拟合组件通用样式
 */

/* 拟合设置卡片 */
.fitting-settings,
.weight-settings,
.fitting-results,
.coefficient-optimization-panel {
  margin-top: 0;
  padding: 0;
  background-color: #fff;
  border-radius: var(--border-radius-base, 8px);
  border: 1px solid var(--border-color-split, #f0f0f0);
  height: 100%;
  box-shadow: var(--box-shadow-base, 0 1px 2px rgba(0, 0, 0, 0.03));
}

/* 卡片内容区域 */
.fitting-settings > div:not(h5),
.weight-settings > div:not(h5),
.fitting-results > div:not(h5),
.coefficient-optimization-panel > div:not(h5) {
  padding: var(--spacing-md, 16px);
}

/* 卡片标题 */
.fitting-settings h5,
.weight-settings h5,
.fitting-results h5,
.coefficient-optimization-panel h5 {
  font-size: var(--font-size-lg, 16px);
  font-weight: 700;
  margin-bottom: 0;
  color: var(--text-color-primary, #333);
  padding: 10px 0;
  background-color: var(--background-color-light, #f5f5f5);
  border-bottom: 1px solid var(--border-color-split, #f0f0f0);
  text-align: center;
  border-radius: var(--border-radius-base, 8px) var(--border-radius-base, 8px) 0 0;
}

/* 表单容器 */
.fitting-form-container {
  padding: var(--spacing-md, 16px);
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 表单样式 */
.fitting-form-container .ant-form {
  width: 100%;
  max-width: 360px;
}

.fitting-form-container .ant-form-item {
  margin-bottom: 12px;
  display: flex;
  justify-content: center;
}

.fitting-form-container .ant-form-item-label {
  text-align: right;
  line-height: 32px;
}

/* 指标卡片 */
.metrics-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.metric-card {
  text-align: center;
}

.metric-title {
  font-weight: 500;
  font-size: 14px;
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--primary-color, #1890ff);
}

/* 卡片样式 */
.ant-card-head {
  min-height: 36px;
  padding: 0 12px;
}

.ant-card-head-title {
  padding: 8px 0;
}

.ant-card-body {
  padding: 12px;
}

/* LaTeX单元格 */
.latex-cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 标签页样式 */
.ant-tabs-nav {
  margin-bottom: 16px;
}

/* 标签页样式已统一到 formula.css 中 */

/* 表单项标签 */
.ant-form-item-label {
  line-height: 20px;
  margin-bottom: 4px;
}

.ant-form-item-label > label {
  font-size: 13px;
  color: var(--text-color-primary, #333);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .fitting-settings > div:not(h5),
  .weight-settings > div:not(h5),
  .fitting-results > div:not(h5),
  .coefficient-optimization-panel > div:not(h5) {
    padding: 12px;
  }
}

/* 高对比度模式支持 */
@media (forced-colors: active) {
  .fitting-settings,
  .weight-settings,
  .fitting-results,
  .coefficient-optimization-panel {
    forced-color-adjust: auto;
    border: 1px solid CanvasText;
  }

  h5 {
    color: CanvasText;
    border-bottom: 1px solid CanvasText;
  }
}
