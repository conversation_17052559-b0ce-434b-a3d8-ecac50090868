{"version": 3, "sources": ["webpack://[name]/webpack/universalModuleDefinition", "webpack://[name]/webpack/bootstrap", "webpack://[name]/./sources/ext/undo/undo.ts", "webpack://[name]/./sources/ext/undo/monitor.ts", "webpack://[name]/./sources/ext/undo/index.ts"], "names": ["root", "factory", "exports", "module", "define", "amd", "window", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "MAX_UNDO_STEPS", "Undo", "_this", "this", "maxSteps", "undoEnabled", "redoEnabled", "action", "commands", "slice", "invert", "revert", "gantt", "copy", "command", "length", "type", "update", "move", "_a", "oldValue", "entity", "inverseCommands", "remove", "add", "assert", "_undoStack", "_redoStack", "getUndoStack", "getRedoStack", "clearUndoStack", "clearRedoStack", "updateConfigs", "config", "undo_steps", "undo_types", "undo_actions", "undo", "redo", "_pop", "_reorderCommands", "callEvent", "_applyAction", "_push", "logAction", "stack", "event", "push", "shift", "pop", "weights", "any", "link", "task", "actionWeights", "sort", "a", "b", "parent", "$index", "weightA", "entities", "actions", "methods", "isExists", "batchUpdate", "method", "getMethod", "check", "id", "item", "prop", "startsWith", "noTrack", "onBeforeUndo", "onBeforeRedo", "batchActions", "Monitor", "_batchAction", "_batchMode", "_ignore", "_ignoreMoveEvents", "_initialTasks", "_initialLinks", "_nestedTasks", "_nestedLinks", "_undo", "_attachEvents", "store", "overwrite", "_storeTask", "_storeLink", "isMoveEventsIgnored", "toggleIgnoreMoveEvents", "newValue", "startIgnore", "stopIgnore", "startBatchAction", "_timeout", "clearTimeout", "setTimeout", "stopBatchAction", "onTaskAdded", "_storeTaskCommand", "onTaskUpdated", "onTaskMoved", "_storeEntityCommand", "getInitialTask", "onTaskDeleted", "children", "childrenLinks", "_storeLinkCommand", "onLinkAdded", "onLinkUpdated", "onLinkDeleted", "setNestedTasks", "taskIds", "tasks", "linkIds", "_getLinks", "getTask", "setInitialTask", "concat", "uniqueLinks", "links", "setInitialLink", "getTaskIndex", "setInitialTaskObject", "clearInitialTasks", "getLink", "getInitialLink", "clearInitialLinks", "deleteCacheCooldown", "saveInitialAll", "eachTask", "getLinks", "for<PERSON>ach", "getMoveObjectByTaskId", "attachEvent", "target", "nested", "datastore", "getDatastore", "tindex", "e", "taskId", "ext", "inlineEditors", "state", "_storeCommand", "obj", "old", "actionType", "entityType", "$source", "$target", "child", "linkId", "monitor_1", "monitor", "updTask", "oldId", "newId", "changeTaskCommandId", "updLink", "oldTaskId", "newTaskId", "source", "changeLinkCommandId", "updateTasksIds", "log", "entry", "j", "updateLinksIds", "saveState"], "mappings": ";;;;;;;;;;;;CAAA,SAAAA,EAAAC,GACA,iBAAAC,SAAA,iBAAAC,OACAA,OAAAD,QAAAD,IACA,mBAAAG,eAAAC,IACAD,OAAA,0BAAAH,GACA,iBAAAC,QACAA,QAAA,wBAAAD,IAEAD,EAAA,wBAAAC,IARA,CASCK,OAAA,WACD,mBCTA,IAAAC,KAGA,SAAAC,EAAAC,GAGA,GAAAF,EAAAE,GACA,OAAAF,EAAAE,GAAAP,QAGA,IAAAC,EAAAI,EAAAE,IACAC,EAAAD,EACAE,GAAA,EACAT,YAUA,OANAU,EAAAH,GAAAI,KAAAV,EAAAD,QAAAC,IAAAD,QAAAM,GAGAL,EAAAQ,GAAA,EAGAR,EAAAD,QA0DA,OArDAM,EAAAM,EAAAF,EAGAJ,EAAAO,EAAAR,EAGAC,EAAAQ,EAAA,SAAAd,EAAAe,EAAAC,GACAV,EAAAW,EAAAjB,EAAAe,IACAG,OAAAC,eAAAnB,EAAAe,GAA0CK,YAAA,EAAAC,IAAAL,KAK1CV,EAAAgB,EAAA,SAAAtB,GACA,oBAAAuB,eAAAC,aACAN,OAAAC,eAAAnB,EAAAuB,OAAAC,aAAwDC,MAAA,WAExDP,OAAAC,eAAAnB,EAAA,cAAiDyB,OAAA,KAQjDnB,EAAAoB,EAAA,SAAAD,EAAAE,GAEA,GADA,EAAAA,IAAAF,EAAAnB,EAAAmB,IACA,EAAAE,EAAA,OAAAF,EACA,KAAAE,GAAA,iBAAAF,QAAAG,WAAA,OAAAH,EACA,IAAAI,EAAAX,OAAAY,OAAA,MAGA,GAFAxB,EAAAgB,EAAAO,GACAX,OAAAC,eAAAU,EAAA,WAAyCT,YAAA,EAAAK,UACzC,EAAAE,GAAA,iBAAAF,EAAA,QAAAM,KAAAN,EAAAnB,EAAAQ,EAAAe,EAAAE,EAAA,SAAAA,GAAgH,OAAAN,EAAAM,IAAqBC,KAAA,KAAAD,IACrI,OAAAF,GAIAvB,EAAA2B,EAAA,SAAAhC,GACA,IAAAe,EAAAf,KAAA2B,WACA,WAA2B,OAAA3B,EAAA,SAC3B,WAAiC,OAAAA,GAEjC,OADAK,EAAAQ,EAAAE,EAAA,IAAAA,GACAA,GAIAV,EAAAW,EAAA,SAAAiB,EAAAC,GAAsD,OAAAjB,OAAAkB,UAAAC,eAAA1B,KAAAuB,EAAAC,IAGtD7B,EAAAgC,EAAA,aAIAhC,IAAAiC,EAAA,yFC9EA,IAAMC,EAAiB,GAEvBC,EAAA,oBAAAA,IAAA,IAAAC,EAAAC,KACCA,KAAAC,SAAWJ,EACXG,KAAAE,aAAc,EACdF,KAAAG,aAAc,EACdH,KAAAI,QACCjB,OAAQ,SAACkB,GACR,OAASA,SAAWA,EAAWA,EAASC,aAEzCC,OAAQ,SAACH,GAGR,UAFMI,EAASC,MAAMC,KAAKN,GACpBC,EAAWN,EAAKY,QACb9C,EAAI,EAAGA,EAAIuC,EAAOC,SAASO,OAAQ/C,IAAK,CAChD,IAAM8C,EAAUH,EAAOH,SAASxC,GAAKwC,EAASE,OAAOC,EAAOH,SAASxC,IACjE8C,EAAQE,OAASR,EAASQ,KAAKC,QAAUH,EAAQE,OAASR,EAASQ,KAAKE,OAC3EC,GAAAL,EAAAM,SAAAN,EAAA7B,OAAC6B,EAAA7B,MAAAkC,EAAA,GAAeL,EAAAM,SAAAD,EAAA,IAGlB,OAAOR,IAGTR,KAAAW,SAECO,OAAQ,KAGRL,KAAM,KAEN1B,OAAA,SAAOL,EAAmBmC,EAAsBJ,EAAmBK,GAClE,OACCA,OAAMA,EACNL,KAAIA,EACJ/B,MAAO2B,MAAMC,KAAK5B,GAClBmC,SAAUR,MAAMC,KAAKO,GAAYnC,KAGnCyB,OAAA,SAAOI,GACN,IAAMH,EAASC,MAAMC,KAAKC,GAE1B,OADAH,EAAOK,KAAOb,KAAKmB,gBAAgBR,EAAQE,MACpCL,GAERW,gBAAA,SAAgBR,GACf,OAAQA,GACP,KAAKX,KAAKa,KAAKC,OACd,OAAOd,KAAKa,KAAKC,OAClB,KAAKd,KAAKa,KAAKO,OACd,OAAOpB,KAAKa,KAAKQ,IAClB,KAAKrB,KAAKa,KAAKQ,IACd,OAAOrB,KAAKa,KAAKO,OAClB,KAAKpB,KAAKa,KAAKE,KACd,OAAOf,KAAKa,KAAKE,KAClB,QAEC,OADAN,MAAMa,QAAO,EAAO,mBAAoBX,GACjC,QAKHX,KAAAuB,cACAvB,KAAAwB,cA8KT,OA5KC1B,EAAAL,UAAAgC,aAAA,WACC,OAAOzB,KAAKuB,YAGbzB,EAAAL,UAAAiC,aAAA,WACC,OAAO1B,KAAKwB,YAGb1B,EAAAL,UAAAkC,eAAA,WACC3B,KAAKuB,eAGNzB,EAAAL,UAAAmC,eAAA,WACC5B,KAAKwB,eAGN1B,EAAAL,UAAAoC,cAAA,WACC7B,KAAKC,SAAWQ,MAAMqB,OAAOC,YAAclC,EAC3CG,KAAKW,QAAQO,OAAST,MAAMqB,OAAOE,WACnChC,KAAKW,QAAQE,KAAOJ,MAAMqB,OAAOG,aACjCjC,KAAKE,cAAgBO,MAAMqB,OAAOI,KAClClC,KAAKG,cAAiBM,MAAMqB,OAAOI,QAAYzB,MAAMqB,OAAOK,MAG7DrC,EAAAL,UAAAyC,KAAA,WAEC,GADAlC,KAAK6B,gBACA7B,KAAKE,YAAV,CAIA,IAAME,EAASJ,KAAKoC,KAAKpC,KAAKuB,YAI9B,GAHInB,GACHJ,KAAKqC,iBAAiBjC,IAE2B,IAA9CK,MAAM6B,UAAU,gBAAiBlC,KAChCA,EAIH,OAHAJ,KAAKuC,aAAavC,KAAKI,OAAOG,OAAOH,IACrCJ,KAAKwC,MAAMxC,KAAKwB,WAAYf,MAAMC,KAAKN,SACvCK,MAAM6B,UAAU,eAAgBlC,IAIlCK,MAAM6B,UAAU,eAAgB,SAGjCxC,EAAAL,UAAA0C,KAAA,WAEC,GADAnC,KAAK6B,gBACA7B,KAAKG,YAAV,CAIA,IAAMC,EAASJ,KAAKoC,KAAKpC,KAAKwB,YAK9B,GAJIpB,GACHJ,KAAKqC,iBAAiBjC,IAG2B,IAA9CK,MAAM6B,UAAU,gBAAiBlC,KAChCA,EAIH,OAHAJ,KAAKuC,aAAanC,GAClBJ,KAAKwC,MAAMxC,KAAKuB,WAAYd,MAAMC,KAAKN,SACvCK,MAAM6B,UAAU,eAAgBlC,IAIlCK,MAAM6B,UAAU,eAAgB,SAIjCxC,EAAAL,UAAAgD,UAAA,SAAUrC,GACTJ,KAAKwC,MAAMxC,KAAKuB,WAAYnB,GAC5BJ,KAAKwB,eAGE1B,EAAAL,UAAA+C,MAAR,SAAcE,EAAmBtC,GAChC,GAAKA,EAAOC,SAASO,OAArB,CAIA,IAAM+B,EAAQD,IAAU1C,KAAKuB,WAAa,oBAAsB,oBAChE,IAAyC,IAArCd,MAAM6B,UAAUK,GAAQvC,KAIvBA,EAAOC,SAASO,OAArB,CAKA,IADA8B,EAAME,KAAKxC,GACJsC,EAAM9B,OAASZ,KAAKC,UAC1ByC,EAAMG,QAEP,OAAOzC,KAGAN,EAAAL,UAAA2C,KAAR,SAAaM,GACZ,OAAOA,EAAMI,OAGNhD,EAAAL,UAAA4C,iBAAR,SAAyBjC,GAIxB,IAAM2C,GAAYC,IAAK,EAAGC,KAAK,EAAGC,KAAK,GACjCC,GAAkBpC,KAAM,EAAGiC,IAAI,GACrC5C,EAAOC,SAAS+C,KAAK,SAASC,EAAGC,GAChC,GAAiB,SAAbD,EAAEnC,QAAkC,SAAboC,EAAEpC,OAC5B,OAAImC,EAAExC,OAASyC,EAAEzC,MACRsC,EAAcG,EAAEzC,OAAS,IAAMsC,EAAcE,EAAExC,OAAS,GAC3C,SAAXwC,EAAExC,MAAmBwC,EAAEpC,UAAYqC,EAAErC,UAAYqC,EAAErC,SAASsC,SAAWF,EAAEpC,SAASsC,OACrFF,EAAEpC,SAASuC,OAASF,EAAErC,SAASuC,OAE/B,EAGR,IAAMC,EAAUV,EAAQM,EAAEnC,SAAW6B,EAAQC,IAE7C,OADgBD,EAAQO,EAAEpC,SAAW6B,EAAQC,KAC5BS,KAMZ3D,EAAAL,UAAA8C,aAAR,SAAqBnC,GACpB,IAAIO,EAAU,KACR+C,EAAW1D,KAAKW,QAAQO,OACxByC,EAAU3D,KAAKW,QAAQE,KAEvB+C,KACNA,EAAQF,EAASR,OAChB7B,IAAK,UACL3C,IAAK,UACLoC,OAAQ,aACRM,OAAQ,aACRL,KAAM,WACN8C,SAAU,gBAEXD,EAAQF,EAAST,OAChB5B,IAAK,UACL3C,IAAK,UACLoC,OAAQ,aACRM,OAAQ,aACRyC,SAAU,gBAGXpD,MAAMqD,YAAY,WACjB,IAAK,IAAIjG,EAAI,EAAGA,EAAIuC,EAAOC,SAASO,OAAQ/C,IAAK,CAChD8C,EAAUP,EAAOC,SAASxC,GAC1B,IAAMkG,EAASH,EAAQjD,EAAQO,QAAQP,EAAQE,MACzCmD,EAAYJ,EAAQjD,EAAQO,QAAQxC,IACpCuF,EAAQL,EAAQjD,EAAQO,QAAQ2C,SAEtC,GAAIlD,EAAQE,OAAS8C,EAAQtC,IAC5BZ,MAAMsD,GAAQpD,EAAQM,SAAUN,EAAQM,SAASsC,OAAQ5C,EAAQM,SAASuC,aACpE,GAAI7C,EAAQE,OAAS8C,EAAQvC,OAC/BX,MAAMwD,GAAOtD,EAAQ7B,MAAMoF,KAC9BzD,MAAMsD,GAAQpD,EAAQ7B,MAAMoF,SAEvB,GAAIvD,EAAQE,OAAS8C,EAAQ7C,OAAQ,CAC3C,IAAMqD,EAAO1D,MAAMuD,GAAWrD,EAAQ7B,MAAMoF,IAC5C,IAAI,IAAME,KAAQzD,EAAQ7B,MACrBsF,EAAKC,WAAW,MAASD,EAAKC,WAAW,OAC5CF,EAAKC,GAAQzD,EAAQ7B,MAAMsF,IAI7B3D,MAAMsD,GAAQpD,EAAQ7B,MAAMoF,SAClBvD,EAAQE,OAAS8C,EAAQ5C,MACnCN,MAAMsD,GAAQpD,EAAQ7B,MAAMoF,GAAIvD,EAAQ7B,MAAM0E,OAAQ7C,EAAQ7B,MAAMyE,YAKzEzD,EAxOA,GAAazC,EAAAyC,0FCFb,IAAMwE,GACLC,aAAc,cACdC,aAAc,eAGTC,GACL,kBACA,oBACA,oBACA,uBAGDC,EAAA,WAWC,SAAAA,EAAYxC,GAVJlC,KAAA2E,aAAe,KACf3E,KAAA4E,YAAa,EACb5E,KAAA6E,SAAU,EACV7E,KAAA8E,mBAAoB,EACpB9E,KAAA+E,iBACA/E,KAAAgF,iBACAhF,KAAAiF,gBACAjF,KAAAkF,gBAIPlF,KAAKmF,MAAQjD,EACblC,KAAKoF,gBA6TP,OA1TCV,EAAAjF,UAAA4F,MAAA,SAAMnB,EAAqBrD,EAAmByE,GAC7C,YAD6C,IAAAA,OAAA,GACzCzE,IAASJ,MAAMqB,OAAOE,WAAWkB,KAC7BlD,KAAKuF,WAAWrB,EAAIoB,GAExBzE,IAASJ,MAAMqB,OAAOE,WAAWiB,MAC7BjD,KAAKwF,WAAWtB,EAAIoB,IAI7BZ,EAAAjF,UAAAgG,oBAAA,WACC,OAAOzF,KAAK8E,mBAEbJ,EAAAjF,UAAAiG,uBAAA,SAAuBC,GACtB3F,KAAK8E,kBAAoBa,IAAY,GAEtCjB,EAAAjF,UAAAmG,YAAA,WACC5F,KAAK6E,SAAU,GAEhBH,EAAAjF,UAAAoG,WAAA,WACC7F,KAAK6E,SAAU,GAEhBH,EAAAjF,UAAAqG,iBAAA,eAAA/F,EAAAC,KAEKA,KAAK+F,UACRC,aAAahG,KAAK+F,UAEnB/F,KAAK+F,SAAWE,WAAW,WAC1BlG,EAAKmG,mBACH,IAEClG,KAAK6E,SAAW7E,KAAK4E,aAIzB5E,KAAK4E,YAAa,EAClB5E,KAAK2E,aAAe3E,KAAKmF,MAAM/E,OAAOjB,WAEvCuF,EAAAjF,UAAAyG,gBAAA,WACC,IAAIlG,KAAK6E,QAAT,CAGA,IAAM3C,EAAOlC,KAAKmF,MACdnF,KAAK2E,cACRzC,EAAKO,UAAUzC,KAAK2E,cAErB3E,KAAK4E,YAAa,EAClB5E,KAAK2E,aAAe,OAErBD,EAAAjF,UAAA0G,YAAA,SAAYjD,GACNlD,KAAK6E,SACT7E,KAAKoG,kBAAkBlD,EAAMlD,KAAKmF,MAAMxE,QAAQE,KAAKQ,MAGvDqD,EAAAjF,UAAA4G,cAAA,SAAcnD,GACRlD,KAAK6E,SACT7E,KAAKoG,kBAAkBlD,EAAMlD,KAAKmF,MAAMxE,QAAQE,KAAKC,SAGvD4D,EAAAjF,UAAA6G,YAAA,SAAYpD,GACNlD,KAAK6E,SACT7E,KAAKuG,oBACJrD,EACAlD,KAAKwG,eAAetD,EAAKgB,IACzBlE,KAAKmF,MAAMxE,QAAQE,KAAKE,KACxBf,KAAKmF,MAAMxE,QAAQO,OAAOgC,OAI7BwB,EAAAjF,UAAAgH,cAAA,SAAcvD,GACb,IAAKlD,KAAK6E,QAAS,CAElB,GADA7E,KAAKoG,kBAAkBlD,EAAMlD,KAAKmF,MAAMxE,QAAQE,KAAKO,QACjDpB,KAAKiF,aAAa/B,EAAKgB,IAE1B,IADA,IAAMwC,EAAW1G,KAAKiF,aAAa/B,EAAKgB,IAC/BrG,EAAI,EAAGA,EAAI6I,EAAS9F,OAAQ/C,IACpCmC,KAAKoG,kBAAkBM,EAAS7I,GAAImC,KAAKmF,MAAMxE,QAAQE,KAAKO,QAG9D,GAAIpB,KAAKkF,aAAahC,EAAKgB,IAC1B,KAAMyC,EAAgB3G,KAAKkF,aAAahC,EAAKgB,IAC7C,IAASrG,EAAI,EAAGA,EAAI8I,EAAc/F,OAAQ/C,IACzCmC,KAAK4G,kBAAkBD,EAAc9I,GAAImC,KAAKmF,MAAMxE,QAAQE,KAAKO,WAKrEsD,EAAAjF,UAAAoH,YAAA,SAAY5D,GACNjD,KAAK6E,SACT7E,KAAK4G,kBAAkB3D,EAAMjD,KAAKmF,MAAMxE,QAAQE,KAAKQ,MAGvDqD,EAAAjF,UAAAqH,cAAA,SAAc7D,GACRjD,KAAK6E,SACT7E,KAAK4G,kBAAkB3D,EAAMjD,KAAKmF,MAAMxE,QAAQE,KAAKC,SAGvD4D,EAAAjF,UAAAsH,cAAA,SAAc9D,GACRjD,KAAK6E,SACT7E,KAAK4G,kBAAkB3D,EAAMjD,KAAKmF,MAAMxE,QAAQE,KAAKO,SAGvDsD,EAAAjF,UAAAuH,eAAA,SAAe9C,EAAY+C,GAK1B,IAJA,IAAI/D,EAAO,KACLgE,KACFC,EAAUnH,KAAKoH,UAAU3G,MAAM4G,QAAQnD,IAElCrG,EAAI,EAAGA,EAAIoJ,EAAQrG,OAAQ/C,IACnCqF,EAAOlD,KAAKsH,eAAeL,EAAQpJ,IACnCsJ,EAAUA,EAAQI,OAAOvH,KAAKoH,UAAUlE,IACxCgE,EAAMtE,KAAKM,GAGZ,IAAMsE,KACN,IAAS3J,EAAI,EAAGA,EAAIsJ,EAAQvG,OAAQ/C,IACnC2J,EAAYL,EAAQtJ,KAAM,EAE3B,IAAM4J,KACN,IAAK,IAAM5J,KAAK2J,EACfC,EAAM7E,KAAK5C,KAAK0H,eAAe7J,IAEhCmC,KAAKiF,aAAaf,GAAMgD,EACxBlH,KAAKkF,aAAahB,GAAMuD,GAEzB/C,EAAAjF,UAAA6H,eAAA,SAAepD,EAAYoB,GAC1B,GAAIA,IAAetF,KAAK+E,cAAcb,KAAQlE,KAAK4E,WAAa,CAC/D,IAAM1B,EAAOzC,MAAMC,KAAKD,MAAM4G,QAAQnD,IACtChB,EAAKM,OAAS/C,MAAMkH,aAAazD,GACjClE,KAAK4H,qBAAqB1D,EAAIhB,GAE/B,OAAOlD,KAAK+E,cAAcb,IAE3BQ,EAAAjF,UAAA+G,eAAA,SAAetC,GACd,OAAOlE,KAAK+E,cAAcb,IAE3BQ,EAAAjF,UAAAoI,kBAAA,WACC7H,KAAK+E,kBAENL,EAAAjF,UAAAmI,qBAAA,SAAqB1D,EAAY3E,GAChCS,KAAK+E,cAAcb,GAAM3E,GAE1BmF,EAAAjF,UAAAiI,eAAA,SAAexD,EAAYoB,GAI1B,OAHKtF,KAAKgF,cAAcd,IAAQlE,KAAK4E,aACpC5E,KAAKgF,cAAcd,GAAMzD,MAAMC,KAAKD,MAAMqH,QAAQ5D,KAE5ClE,KAAKgF,cAAcd,IAE3BQ,EAAAjF,UAAAsI,eAAA,SAAe7D,GACd,OAAOlE,KAAKgF,cAAcd,IAE3BQ,EAAAjF,UAAAuI,kBAAA,WACChI,KAAKgF,kBAEEN,EAAAjF,UAAA2F,cAAR,eAAArF,EAAAC,KACKiI,EAAsB,KAEpBC,EAAiB,WACjBD,IACJA,EAAsBhC,WAAW,WAChCgC,EAAsB,OAGvBlI,EAAK8H,oBACLpH,MAAM0H,SAAS,SAACjF,GACfnD,EAAKuH,eAAepE,EAAKgB,MAG1BnE,EAAKiI,oBACLvH,MAAM2H,WAAWC,QAAQ,SAACpF,GACzBlD,EAAK2H,eAAezE,EAAKiB,QAItBoE,EAAwB,SAACpE,GAC9B,OAAOzD,MAAMC,KAAKD,MAAM4G,QAAQnD,KAGjC,IAAK,IAAMrG,KAAKyG,EACf7D,MAAM8H,YAAY1K,EAAG,WAEpB,OADAkC,EAAK6F,eACE,IAERnF,MAAM8H,YAAYjE,EAAQzG,GAAI,WAE7B,OADAkC,EAAK8F,cACE,IAIT,IAAShI,EAAK,EAAGA,EAAI4G,EAAa7D,OAAQ/C,IACzC4C,MAAM8H,YAAY9D,EAAa5G,GAAI,WAElC,OADAkC,EAAK+F,oBACE,IAITrF,MAAM8H,YAAY,UAAW,WAC5BxI,EAAKoF,MAAMxD,iBACX5B,EAAKoF,MAAMvD,iBACXsG,MAEDzH,MAAM8H,YAAY,iBAAkB,SAACrE,EAAYhB,GAChDnD,EAAKuH,eAAepD,GAAI,GACxBnE,EAAKoG,YAAYjD,KAElBzC,MAAM8H,YAAY,oBAAqB,SAACrE,EAAYhB,GACnDnD,EAAKsG,cAAcnD,KAEpBzC,MAAM8H,YAAY,oBAAqB,SAACrE,EAAYhB,GACnDnD,EAAK0G,cAAcvD,KAEpBzC,MAAM8H,YAAY,iBAAkB,SAACrE,EAAYjB,GAChDlD,EAAK2H,eAAexD,GAAI,GACxBnE,EAAK8G,YAAY5D,KAElBxC,MAAM8H,YAAY,oBAAqB,SAACrE,EAAYjB,GACnDlD,EAAK+G,cAAc7D,KAEpBxC,MAAM8H,YAAY,oBAAqB,SAACrE,EAAYjB,GACnDlD,EAAKgH,cAAc9D,KAEpBxC,MAAM8H,YAAY,eAAgB,SAACrE,EAAYsE,GAG9C,OAFAzI,EAAKuG,YAAYgC,EAAsBpE,IACvCnE,EAAK2F,0BACE,IAERjF,MAAM8H,YAAY,qBAAsB,SAACrE,GACxCnE,EAAKsF,MAAMnB,EAAIzD,MAAMqB,OAAOE,WAAWkB,MACvC,IAAMuF,KASN,OANAP,IAEAzH,MAAM0H,SAAS,SAACjF,GACfuF,EAAO7F,KAAKM,EAAKgB,KACfA,GACHnE,EAAKiH,eAAe9C,EAAIuE,IACjB,IAER,IAAMC,EAAYjI,MAAMkI,aAAa,QAErCD,EAAUH,YAAY,mBAAoB,SAACrE,EAAYX,EAAgBqF,GAItE,OAHK7I,EAAK0F,uBACTyC,KAEM,IAGRQ,EAAUH,YAAY,kBAAmB,SAACrE,EAAYX,EAAgBqF,GAIrE,OAHK7I,EAAK0F,uBACT1F,EAAKuG,YAAYgC,EAAsBpE,KAEjC,IAGRzD,MAAM8H,YAAY,iBAAkB,SAACrE,EAAYsE,EAAgBK,GAGhE,OAFA9I,EAAK2F,wBAAuB,GAC5BwC,KACO,IAGRzH,MAAM8H,YAAY,mBAAoB,SAACO,GAAmB,OAAA/I,EAAKsF,MAAMyD,EAAQrI,MAAMqB,OAAOE,WAAWkB,QAErGzC,MAAM8H,YAAY,aAAc,SAACO,GAAmB,OAAA/I,EAAKsF,MAAMyD,EAAQrI,MAAMqB,OAAOE,WAAWkB,QAE/FzC,MAAM8H,YAAY,2BAA4B,SAACrF,GAE9C,OADAnD,EAAKsF,MAAMnC,EAAKgB,GAAIzD,MAAMqB,OAAOE,WAAWkB,OACrC,IAGJzC,MAAMsI,IAAIC,eACbvI,MAAMsI,IAAIC,cAAcT,YAAY,cAAe,SAACU,GACnDlJ,EAAKsF,MAAM4D,EAAM/E,GAAIzD,MAAMqB,OAAOE,WAAWkB,SAKxCwB,EAAAjF,UAAAyJ,cAAR,SAAsBvI,GACrB,IAAMuB,EAAOlC,KAAKmF,MAGlB,GAFAjD,EAAKL,gBAEAK,EAAKhC,YAIV,GAAIF,KAAK4E,WACR5E,KAAK2E,aAAatE,SAASuC,KAAKjC,OAC1B,CACN,IAAMP,EAAS8B,EAAK9B,OAAOjB,QAAQwB,IACnCuB,EAAKO,UAAUrC,KAGTsE,EAAAjF,UAAA8G,oBAAR,SAA4B4C,EAAiBC,EAAiBC,EAAyBC,GACtF,IACM3I,EADOX,KAAKmF,MACGxE,QAAQxB,OAAOgK,EAAKC,EAAKC,EAAYC,GAC1DtJ,KAAKkJ,cAAcvI,IAEZ+D,EAAAjF,UAAA2G,kBAAR,SAA0B+C,EAAYtI,GACrCb,KAAKuG,oBAAoB4C,EAAKnJ,KAAKwG,eAAe2C,EAAIjF,IAAKrD,EAAMb,KAAKmF,MAAMxE,QAAQO,OAAOgC,OAEpFwB,EAAAjF,UAAAmH,kBAAR,SAA0BuC,EAAYtI,GACrCb,KAAKuG,oBAAoB4C,EAAKnJ,KAAK+H,eAAeoB,EAAIjF,IAAKrD,EAAMb,KAAKmF,MAAMxE,QAAQO,OAAO+B,OAEpFyB,EAAAjF,UAAA2H,UAAR,SAAkBlE,GACjB,OAAOA,EAAKqG,QAAQhC,OAAOrE,EAAKsG,UAEzB9E,EAAAjF,UAAA8F,WAAR,SAAmBuD,EAAgBxD,GAAnC,IAAAvF,EAAAC,KAKC,YALkC,IAAAsF,OAAA,GAClCtF,KAAKsH,eAAewB,EAAQxD,GAC5B7E,MAAM0H,SAAS,SAACsB,GACf1J,EAAKuH,eAAemC,EAAMvF,KACxB4E,IACI,GAEApE,EAAAjF,UAAA+F,WAAR,SAAmBkE,EAAgBpE,GAElC,YAFkC,IAAAA,OAAA,GAClCtF,KAAK0H,eAAegC,EAAQpE,IACrB,GAETZ,EA1UA,GAAarH,EAAAqH,6FCdb,IAAAiF,EAAAhM,EAAA,KAGMwH,EAAQ,IADdxH,EAAA,KACkBmC,MACZ8J,EAAoB,IAAID,EAAAjF,QAAQS,GA8CtC,SAAS0E,EAAQ3G,EAAa4G,EAAeC,GACvC7G,IAEDA,EAAKgB,KAAO4F,IACf5G,EAAKgB,GAAK6F,GAGP7G,EAAKK,SAAWuG,IACnB5G,EAAKK,OAASwG,IAIhB,SAASC,EAAoBrJ,EAAuBmJ,EAAeC,GAClEF,EAAQlJ,EAAQ7B,MAAOgL,EAAOC,GAC9BF,EAAQlJ,EAAQM,SAAU6I,EAAOC,GAGlC,SAASE,EAAQhH,EAAaiH,EAAmBC,GAC3ClH,IACDA,EAAKmH,SAAWF,IACnBjH,EAAKmH,OAASD,GAEXlH,EAAKuF,SAAW0B,IACnBjH,EAAKuF,OAAS2B,IAIhB,SAASE,EAAoB1J,EAAuBmJ,EAAeC,GAClEE,EAAQtJ,EAAQ7B,MAAOgL,EAAOC,GAC9BE,EAAQtJ,EAAQM,SAAU6I,EAAOC,GAGlC,SAASO,EAAeC,EAAiBT,EAAeC,GAGvD,IAFA,IAAM7H,EAAOiD,EAEJtH,EAAI,EAAGA,EAAI0M,EAAI3J,OAAQ/C,IAE/B,IADA,IAAM2M,EAAQD,EAAI1M,GACT4M,EAAI,EAAGA,EAAID,EAAMnK,SAASO,OAAQ6J,IACtCD,EAAMnK,SAASoK,GAAGvJ,SAAWgB,EAAKvB,QAAQO,OAAOgC,KACpD8G,EAAoBQ,EAAMnK,SAASoK,GAAIX,EAAOC,GACpCS,EAAMnK,SAASoK,GAAGvJ,SAAWgB,EAAKvB,QAAQO,OAAO+B,MAC3DoH,EAAoBG,EAAMnK,SAASoK,GAAIX,EAAOC,GAMlD,SAASW,EAAeH,EAAiBT,EAAeC,GAGvD,IAFA,IAAM7H,EAAOiD,EAEJtH,EAAI,EAAGA,EAAI0M,EAAI3J,OAAQ/C,IAE/B,IADA,IAAM2M,EAAQD,EAAI1M,GACT4M,EAAI,EAAGA,EAAID,EAAMnK,SAASO,OAAQ6J,IAAK,CAC/C,IAAM9J,EAAU6J,EAAMnK,SAASoK,GAC3B9J,EAAQO,SAAWgB,EAAKvB,QAAQO,OAAO+B,OACtCtC,EAAQ7B,OAAS6B,EAAQ7B,MAAMoF,KAAO4F,IACzCnJ,EAAQ7B,MAAMoF,GAAK6F,GAEhBpJ,EAAQM,UAAYN,EAAQM,SAASiD,KAAO4F,IAC/CnJ,EAAQM,SAASiD,GAAK6F,KAvG3BtJ,MAAMqB,OAAOI,MAAO,EACpBzB,MAAMqB,OAAOK,MAAO,EAMpB1B,MAAMqB,OAAOE,YACZiB,KAAM,OACNC,KAAM,QAOPzC,MAAMqB,OAAOG,cACZnB,OAAQ,SACRM,OAAQ,SACRC,IAAK,MACLN,KAAM,QAGFN,MAAMsI,MACVtI,MAAMsI,QAGPtI,MAAMsI,IAAI7G,MACTA,KAAM,WAAM,OAAAiD,EAAMjD,QAClBC,KAAM,WAAM,OAAAgD,EAAMhD,QAClBV,aAAc,WAAM,OAAA0D,EAAM1D,gBAC1BC,aAAc,WAAM,OAAAyD,EAAMzD,gBAC1BC,eAAgB,WAAM,OAAAwD,EAAMxD,kBAC5BC,eAAgB,WAAM,OAAAuD,EAAMvD,kBAC5B+I,UAAW,SAACzG,EAAqBrD,GAAsB,OAAA+I,EAAQvE,MAAMnB,EAAIrD,GAAM,KAGhFJ,MAAMyB,KAAOzB,MAAMsI,IAAI7G,KAAKA,KAC5BzB,MAAM0B,KAAO1B,MAAMsI,IAAI7G,KAAKC,KAC5B1B,MAAMgB,aAAehB,MAAMsI,IAAI7G,KAAKT,aACpChB,MAAMiB,aAAejB,MAAMsI,IAAI7G,KAAKR,aACpCjB,MAAMkB,eAAiBlB,MAAMsI,IAAI7G,KAAKP,eACtClB,MAAMmB,eAAiBnB,MAAMsI,IAAI7G,KAAKN,eAoEtCnB,MAAM8H,YAAY,iBAAkB,SAACuB,EAAeC,GACnD,IAAM7H,EAAOiD,EACbmF,EAAepI,EAAKT,eAAgBqI,EAAOC,GAC3CO,EAAepI,EAAKR,eAAgBoI,EAAOC,KAG5CtJ,MAAM8H,YAAY,iBAAkB,SAACuB,EAAeC,GACnD,IAAM7H,EAAOiD,EACbuF,EAAexI,EAAKT,eAAgBqI,EAAOC,GAC3CW,EAAexI,EAAKR,eAAgBoI,EAAOC,KAG5CtJ,MAAM8H,YAAY,eAAgB,WACjCpD,EAAMtD", "file": "ext/dhtmlxgantt_undo.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"ext/dhtmlxgantt_undo\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ext/dhtmlxgantt_undo\"] = factory();\n\telse\n\t\troot[\"ext/dhtmlxgantt_undo\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/codebase/\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 220);\n", "declare var gantt;\n\nimport { IUndo, IUndoCommand, IUndoCommands, IUndoPropAction, IUndoPropCommand, TActionType, TEntityType, TUndoStack, TUndoValue } from \"./types\";\n\nconst MAX_UNDO_STEPS = 10;\n\nexport class Undo implements IUndo {\n\tmaxSteps = MAX_UNDO_STEPS;\n\tundoEnabled = true;\n\tredoEnabled = true;\n\taction: IUndoPropAction = {\n\t\tcreate: (commands: IUndoCommand[]): IUndoCommands => {\n\t\t\treturn { commands: (commands ? commands.slice() : []) };\n\t\t},\n\t\tinvert: (action: IUndoCommands): IUndoCommands => {\n\t\t\tconst revert = gantt.copy(action);\n\t\t\tconst commands = this.command;\n\t\t\tfor (let i = 0; i < action.commands.length; i++) {\n\t\t\t\tconst command = revert.commands[i] = commands.invert(revert.commands[i]);\n\t\t\t\tif (command.type === commands.type.update || command.type === commands.type.move) {\n\t\t\t\t\t[command.value, command.oldValue] = [command.oldValue, command.value];\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn revert as IUndoCommands;\n\t\t}\n\t};\n\tcommand: IUndoPropCommand = {\n\t\t// entities that require different processing for undoing-redoing changes (gantt.config.undo_types)\n\t\tentity: null,\n\n\t\t// types of traced actions (gantt.config.undo_actions)\n\t\ttype: null,\n\n\t\tcreate(value: TUndoValue, oldValue: TUndoValue, type: TActionType, entity: TEntityType): IUndoCommand {\n\t\t\treturn {\n\t\t\t\tentity,\n\t\t\t\ttype,\n\t\t\t\tvalue: gantt.copy(value),\n\t\t\t\toldValue: gantt.copy(oldValue || value)\n\t\t\t};\n\t\t},\n\t\tinvert(command: IUndoCommand): IUndoCommand {\n\t\t\tconst revert = gantt.copy(command);\n\t\t\trevert.type = this.inverseCommands(command.type);\n\t\t\treturn revert;\n\t\t},\n\t\tinverseCommands(command: TActionType): TActionType {\n\t\t\tswitch (command) {\n\t\t\t\tcase this.type.update:\n\t\t\t\t\treturn this.type.update;\n\t\t\t\tcase this.type.remove:\n\t\t\t\t\treturn this.type.add;\n\t\t\t\tcase this.type.add:\n\t\t\t\t\treturn this.type.remove;\n\t\t\t\tcase this.type.move:\n\t\t\t\t\treturn this.type.move;\n\t\t\t\tdefault:\n\t\t\t\t\tgantt.assert(false, \"Invalid command \"+ command);\n\t\t\t\t\treturn null;\n\t\t\t}\n\t\t}\n\t};\n\n\tprivate _undoStack = [];\n\tprivate _redoStack = [];\n\n\tgetUndoStack() {\n\t\treturn this._undoStack;\n\t}\n\n\tgetRedoStack() {\n\t\treturn this._redoStack;\n\t}\n\n\tclearUndoStack() {\n\t\tthis._undoStack = [];\n\t}\n\n\tclearRedoStack() {\n\t\tthis._redoStack = [];\n\t}\n\n\tupdateConfigs() {\n\t\tthis.maxSteps = gantt.config.undo_steps || MAX_UNDO_STEPS;\n\t\tthis.command.entity = gantt.config.undo_types;\n\t\tthis.command.type = gantt.config.undo_actions;\n\t\tthis.undoEnabled = !!gantt.config.undo;\n\t\tthis.redoEnabled = (!!gantt.config.undo) && (!!gantt.config.redo);\n\t}\n\n\tundo() {\n\t\tthis.updateConfigs();\n\t\tif (!this.undoEnabled) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst action = this._pop(this._undoStack);\n\t\tif (action) {\n\t\t\tthis._reorderCommands(action);\n\t\t}\n\t\tif (gantt.callEvent(\"onBeforeUndo\", [action]) !== false) {\n\t\t\tif (action) {\n\t\t\t\tthis._applyAction(this.action.invert(action));\n\t\t\t\tthis._push(this._redoStack, gantt.copy(action));\n\t\t\t\tgantt.callEvent(\"onAfterUndo\", [action]);\n\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\t\tgantt.callEvent(\"onAfterUndo\", [null]);\n\t}\n\n\tredo() {\n\t\tthis.updateConfigs();\n\t\tif (!this.redoEnabled) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst action = this._pop(this._redoStack);\n\t\tif (action) {\n\t\t\tthis._reorderCommands(action);\n\t\t}\n\n\t\tif (gantt.callEvent(\"onBeforeRedo\", [action]) !== false) {\n\t\t\tif (action) {\n\t\t\t\tthis._applyAction(action);\n\t\t\t\tthis._push(this._undoStack, gantt.copy(action));\n\t\t\t\tgantt.callEvent(\"onAfterRedo\", [action]);\n\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\t\tgantt.callEvent(\"onAfterRedo\", [null]);\n\t}\n\n\t// storeUndo:\n\tlogAction(action: IUndoCommands) {\n\t\tthis._push(this._undoStack, action);\n\t\tthis._redoStack = [];\n\t}\n\n\tprivate _push(stack: TUndoStack, action: IUndoCommands): IUndoCommands {\n\t\tif (!action.commands.length) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst event = stack === this._undoStack ? \"onBeforeUndoStack\" : \"onBeforeRedoStack\";\n\t\tif (gantt.callEvent(event, [action]) === false){\n\t\t\treturn;\n\t\t}\n\t\t// commands can be removed from event handler\n\t\tif (!action.commands.length) {\n\t\t\treturn;\n\t\t}\n\n\t\tstack.push(action);\n\t\twhile (stack.length > this.maxSteps) {\n\t\t\tstack.shift();\n\t\t}\n\t\treturn action;\n\t}\n\n\tprivate _pop(stack: TUndoStack): IUndoCommands {\n\t\treturn stack.pop();\n\t}\n\n\tprivate _reorderCommands(action) {\n\t\t// firstly process tasks and only then links\n\t\t// in order to ensure links are added not earlier than their tasks\n\t\t// firstly to 'move' actions and only then updates\n\t\tconst weights = { any: 0, link:1, task:2 };\n\t\tconst actionWeights = { move: 1, any:0 };\n\t\taction.commands.sort(function(a, b) {\n\t\t\tif (a.entity === \"task\" && b.entity === \"task\") {\n\t\t\t\tif (a.type !== b.type) {\n\t\t\t\t\treturn (actionWeights[b.type] || 0) - (actionWeights[a.type] || 0);\n\t\t\t\t} else if (a.type === \"move\" && a.oldValue && b.oldValue && b.oldValue.parent === a.oldValue.parent) {\n\t\t\t\t\treturn a.oldValue.$index - b.oldValue.$index;\n\t\t\t\t} else {\n\t\t\t\t\treturn 0;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tconst weightA = weights[a.entity] || weights.any;\n\t\t\t\tconst weightB = weights[b.entity] || weights.any;\n\t\t\t\treturn weightB - weightA;\n\t\t\t}\n\n\t\t});\n\t}\n\n\tprivate _applyAction(action: IUndoCommands) {\n\t\tlet command = null;\n\t\tconst entities = this.command.entity;\n\t\tconst actions = this.command.type;\n\n\t\tconst methods = {};\n\t\tmethods[entities.task] = {\n\t\t\tadd: \"addTask\",\n\t\t\tget: \"getTask\",\n\t\t\tupdate: \"updateTask\",\n\t\t\tremove: \"deleteTask\",\n\t\t\tmove: \"moveTask\",\n\t\t\tisExists: \"isTaskExists\"\n\t\t};\n\t\tmethods[entities.link] = {\n\t\t\tadd: \"addLink\",\n\t\t\tget: \"getLink\",\n\t\t\tupdate: \"updateLink\",\n\t\t\tremove: \"deleteLink\",\n\t\t\tisExists: \"isLinkExists\"\n\t\t};\n\n\t\tgantt.batchUpdate(function() {\n\t\t\tfor (let i = 0; i < action.commands.length; i++) {\n\t\t\t\tcommand = action.commands[i];\n\t\t\t\tconst method = methods[command.entity][command.type];\n\t\t\t\tconst getMethod = methods[command.entity].get;\n\t\t\t\tconst check = methods[command.entity].isExists;\n\n\t\t\t\tif (command.type === actions.add) {\n\t\t\t\t\tgantt[method](command.oldValue, command.oldValue.parent, command.oldValue.$index);\n\t\t\t\t} else if (command.type === actions.remove) {\n\t\t\t\t\tif (gantt[check](command.value.id)) {\n\t\t\t\t\t\tgantt[method](command.value.id);\n\t\t\t\t\t}\n\t\t\t\t} else if (command.type === actions.update) {\n\t\t\t\t\tconst item = gantt[getMethod](command.value.id);\n\t\t\t\t\tfor(const prop in command.value){\n\t\t\t\t\t\tif(!prop.startsWith(\"$\") && !prop.startsWith(\"_\")){\n\t\t\t\t\t\t\titem[prop] = command.value[prop];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tgantt[method](command.value.id);\n\t\t\t\t} else if (command.type === actions.move) {\n\t\t\t\t\tgantt[method](command.value.id, command.value.$index, command.value.parent);\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t}\n}", "declare var gantt;\n\nimport { IInlineEditState, IMonitor, IUndo, IUndoCommand, TActionType, TEntityType, TUndoValue } from \"./types\";\n\nconst noTrack = {\n\tonBeforeUndo: \"onAfterUndo\",\n\tonBeforeRedo: \"onAfterRedo\"\n};\n\nconst batchActions = [\n\t\"onTaskDragStart\",\n\t\"onAfterTaskUpdate\",\n\t\"onAfterTaskDelete\",\n\t\"onBeforeBatchUpdate\"\n];\n\nexport class Monitor implements IMonitor {\n\tprivate _batchAction = null;\n\tprivate _batchMode = false;\n\tprivate _ignore = false;\n\tprivate _ignoreMoveEvents = false;\n\tprivate _initialTasks = {};\n\tprivate _initialLinks = {};\n\tprivate _nestedTasks =  {};\n\tprivate _nestedLinks = {};\n\tprivate _timeout;\n\tprivate _undo;\n\tconstructor(undo: IUndo) {\n\t\tthis._undo = undo;\n\t\tthis._attachEvents();\n\t}\n\n\tstore(id: TaskID | LinkID, type: TEntityType, overwrite: boolean = false) {\n\t\tif (type === gantt.config.undo_types.task) {\n\t\t\treturn this._storeTask(id, overwrite);\n\t\t}\n\t\tif (type === gantt.config.undo_types.link) {\n\t\t\treturn this._storeLink(id, overwrite);\n\t\t}\n\t\treturn false;\n\t}\n\tisMoveEventsIgnored() {\n\t\treturn this._ignoreMoveEvents;\n\t}\n\ttoggleIgnoreMoveEvents(newValue?: boolean) {\n\t\tthis._ignoreMoveEvents = newValue || false;\n\t}\n\tstartIgnore() {\n\t\tthis._ignore = true;\n\t}\n\tstopIgnore() {\n\t\tthis._ignore = false;\n\t}\n\tstartBatchAction() {\n\t\t// try catching updates made from event handlers using timeout\n\t\tif (this._timeout) {\n\t\t\tclearTimeout(this._timeout);\n\t\t}\n\t\tthis._timeout = setTimeout(() => {\n\t\t\tthis.stopBatchAction();\n\t\t}, 10);\n\n\t\tif (this._ignore || this._batchMode) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._batchMode = true;\n\t\tthis._batchAction = this._undo.action.create();\n\t}\n\tstopBatchAction() {\n\t\tif (this._ignore) {\n\t\t\treturn;\n\t\t}\n\t\tconst undo = this._undo;\n\t\tif (this._batchAction) {\n\t\t\tundo.logAction(this._batchAction);\n\t\t}\n\t\tthis._batchMode = false;\n\t\tthis._batchAction = null;\n\t}\n\tonTaskAdded(task: ITask) {\n\t\tif (!this._ignore) {\n\t\t\tthis._storeTaskCommand(task, this._undo.command.type.add);\n\t\t}\n\t}\n\tonTaskUpdated(task: ITask) {\n\t\tif (!this._ignore) {\n\t\t\tthis._storeTaskCommand(task, this._undo.command.type.update);\n\t\t}\n\t}\n\tonTaskMoved(task: ITask) {\n\t\tif (!this._ignore) {\n\t\t\tthis._storeEntityCommand(\n\t\t\t\ttask,\n\t\t\t\tthis.getInitialTask(task.id),\n\t\t\t\tthis._undo.command.type.move,\n\t\t\t\tthis._undo.command.entity.task\n\t\t\t);\n\t\t}\n\t}\n\tonTaskDeleted(task: ITask) {\n\t\tif (!this._ignore) {\n\t\t\tthis._storeTaskCommand(task, this._undo.command.type.remove);\n\t\t\tif (this._nestedTasks[task.id]) {\n\t\t\t\tconst children = this._nestedTasks[task.id];\n\t\t\t\tfor (let i = 0; i < children.length; i++) {\n\t\t\t\t\tthis._storeTaskCommand(children[i], this._undo.command.type.remove);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (this._nestedLinks[task.id]) {\n\t\t\t\tconst childrenLinks = this._nestedLinks[task.id];\n\t\t\t\tfor (let i = 0; i < childrenLinks.length; i++) {\n\t\t\t\t\tthis._storeLinkCommand(childrenLinks[i], this._undo.command.type.remove);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\tonLinkAdded(link: ILink) {\n\t\tif (!this._ignore) {\n\t\t\tthis._storeLinkCommand(link, this._undo.command.type.add);\n\t\t}\n\t}\n\tonLinkUpdated(link: ILink) {\n\t\tif (!this._ignore) {\n\t\t\tthis._storeLinkCommand(link, this._undo.command.type.update);\n\t\t}\n\t}\n\tonLinkDeleted(link: ILink) {\n\t\tif (!this._ignore) {\n\t\t\tthis._storeLinkCommand(link, this._undo.command.type.remove);\n\t\t}\n\t}\n\tsetNestedTasks(id: TaskID, taskIds: TaskID[]) {\n\t\tlet task = null;\n\t\tconst tasks = [];\n\t\tlet\tlinkIds = this._getLinks(gantt.getTask(id));\n\n\t\tfor (let i = 0; i < taskIds.length; i++) {\n\t\t\ttask = this.setInitialTask(taskIds[i]);\n\t\t\tlinkIds = linkIds.concat(this._getLinks(task));\n\t\t\ttasks.push(task);\n\t\t}\n\n\t\tconst uniqueLinks = {};\n\t\tfor (let i = 0; i < linkIds.length; i++) {\n\t\t\tuniqueLinks[linkIds[i]] = true;\n\t\t}\n\t\tconst links = [];\n\t\tfor (const i in uniqueLinks) {\n\t\t\tlinks.push(this.setInitialLink(i));\n\t\t}\n\t\tthis._nestedTasks[id] = tasks;\n\t\tthis._nestedLinks[id] = links;\n\t}\n\tsetInitialTask(id: TaskID, overwrite?: boolean) {\n\t\tif (overwrite || (!this._initialTasks[id] || !this._batchMode)) {\n\t\t\tconst task = gantt.copy(gantt.getTask(id));\n\t\t\ttask.$index = gantt.getTaskIndex(id);\n\t\t\tthis.setInitialTaskObject(id, task);\n\t\t}\n\t\treturn this._initialTasks[id];\n\t}\n\tgetInitialTask(id: TaskID) {\n\t\treturn this._initialTasks[id];\n\t}\n\tclearInitialTasks() {\n\t\tthis._initialTasks = {};\n\t}\n\tsetInitialTaskObject(id: TaskID, object: ITask) {\n\t\tthis._initialTasks[id] = object;\n\t}\n\tsetInitialLink(id: LinkID, overwrite?: boolean) {\n\t\tif (!this._initialLinks[id] || !this._batchMode) {\n\t\t\tthis._initialLinks[id] = gantt.copy(gantt.getLink(id));\n\t\t}\n\t\treturn this._initialLinks[id];\n\t}\n\tgetInitialLink(id: LinkID) {\n\t\treturn this._initialLinks[id];\n\t}\n\tclearInitialLinks() {\n\t\tthis._initialLinks = {};\n\t}\n\tprivate _attachEvents() {\n\t\tlet deleteCacheCooldown = null;\n\n\t\tconst saveInitialAll = () => {\n\t\t\tif (!deleteCacheCooldown) {\n\t\t\t\tdeleteCacheCooldown = setTimeout(() => {\n\t\t\t\t\tdeleteCacheCooldown = null;\n\t\t\t\t});\n\n\t\t\t\tthis.clearInitialTasks();\n\t\t\t\tgantt.eachTask((task: ITask) => {\n\t\t\t\t\tthis.setInitialTask(task.id);\n\t\t\t\t});\n\n\t\t\t\tthis.clearInitialLinks();\n\t\t\t\tgantt.getLinks().forEach((link: ILink) => {\n\t\t\t\t\tthis.setInitialLink(link.id);\n\t\t\t\t});\n\t\t\t}\n\t\t};\n\t\tconst getMoveObjectByTaskId = (id: TaskID) => {\n\t\t\treturn gantt.copy(gantt.getTask(id));\n\t\t};\n\n\t\tfor (const i in noTrack) {\n\t\t\tgantt.attachEvent(i, () => {\n\t\t\t\tthis.startIgnore();\n\t\t\t\treturn true;\n\t\t\t});\n\t\t\tgantt.attachEvent(noTrack[i], () => {\n\t\t\t\tthis.stopIgnore();\n\t\t\t\treturn true;\n\t\t\t});\n\t\t}\n\n\t\tfor (let i  = 0; i < batchActions.length; i++) {\n\t\t\tgantt.attachEvent(batchActions[i], () => {\n\t\t\t\tthis.startBatchAction();\n\t\t\t\treturn true;\n\t\t\t});\n\t\t}\n\n\t\tgantt.attachEvent(\"onParse\", () => {\n\t\t\tthis._undo.clearUndoStack();\n\t\t\tthis._undo.clearRedoStack();\n\t\t\tsaveInitialAll();\n\t\t});\n\t\tgantt.attachEvent(\"onAfterTaskAdd\", (id: TaskID, task: ITask) => {\n\t\t\tthis.setInitialTask(id, true);\n\t\t\tthis.onTaskAdded(task);\n\t\t});\n\t\tgantt.attachEvent(\"onAfterTaskUpdate\", (id: TaskID, task: ITask) => {\n\t\t\tthis.onTaskUpdated(task);\n\t\t});\n\t\tgantt.attachEvent(\"onAfterTaskDelete\", (id: TaskID, task: ITask) => {\n\t\t\tthis.onTaskDeleted(task);\n\t\t});\n\t\tgantt.attachEvent(\"onAfterLinkAdd\", (id: LinkID, link: ILink) => {\n\t\t\tthis.setInitialLink(id, true);\n\t\t\tthis.onLinkAdded(link);\n\t\t});\n\t\tgantt.attachEvent(\"onAfterLinkUpdate\", (id: LinkID, link: ILink) => {\n\t\t\tthis.onLinkUpdated(link);\n\t\t});\n\t\tgantt.attachEvent(\"onAfterLinkDelete\", (id: LinkID, link: ILink) => {\n\t\t\tthis.onLinkDeleted(link);\n\t\t});\n\t\tgantt.attachEvent(\"onRowDragEnd\", (id: TaskID, target: TaskID) => {\n\t\t\tthis.onTaskMoved(getMoveObjectByTaskId(id));\n\t\t\tthis.toggleIgnoreMoveEvents();\n\t\t\treturn true;\n\t\t});\n\t\tgantt.attachEvent(\"onBeforeTaskDelete\", (id: TaskID) => {\n\t\t\tthis.store(id, gantt.config.undo_types.task);\n\t\t\tconst nested = [];\n\n\t\t\t// remember task indexes in case their being deleted in a loop, so they could be restored in the correct order\n\t\t\tsaveInitialAll();\n\n\t\t\tgantt.eachTask((task: ITask) => {\n\t\t\t\tnested.push(task.id);\n\t\t\t}, id);\n\t\t\tthis.setNestedTasks(id, nested);\n\t\t\treturn true;\n\t\t});\n\t\tconst datastore = gantt.getDatastore(\"task\");\n\n\t\tdatastore.attachEvent(\"onBeforeItemMove\", (id: TaskID, parent: TaskID, tindex: number) => {\n\t\t\tif (!this.isMoveEventsIgnored()) {\n\t\t\t\tsaveInitialAll();\n\t\t\t}\n\t\t\treturn true;\n\t\t});\n\n\t\tdatastore.attachEvent(\"onAfterItemMove\", (id: TaskID, parent: TaskID, tindex: number) => {\n\t\t\tif (!this.isMoveEventsIgnored()) {\n\t\t\t\tthis.onTaskMoved(getMoveObjectByTaskId(id));\n\t\t\t}\n\t\t\treturn true;\n\t\t});\n\n\t\tgantt.attachEvent(\"onRowDragStart\", (id: TaskID, target: TaskID, e: Event) => {\n\t\t\tthis.toggleIgnoreMoveEvents(true);\n\t\t\tsaveInitialAll();\n\t\t\treturn true;\n\t\t});\n\n\t\tgantt.attachEvent(\"onBeforeTaskDrag\", (taskId: TaskID) => this.store(taskId, gantt.config.undo_types.task));\n\n\t\tgantt.attachEvent(\"onLightbox\", (taskId: TaskID) => this.store(taskId, gantt.config.undo_types.task));\n\n\t\tgantt.attachEvent(\"onBeforeTaskAutoSchedule\", (task: ITask) => {\n\t\t\tthis.store(task.id, gantt.config.undo_types.task);\n\t\t\treturn true;\n\t\t});\n\n\t\tif (gantt.ext.inlineEditors) {\n\t\t\tgantt.ext.inlineEditors.attachEvent(\"onEditStart\", (state: IInlineEditState) => {\n\t\t\t\tthis.store(state.id, gantt.config.undo_types.task);\n\t\t\t});\n\t\t}\n\t}\n\n\tprivate _storeCommand(command: IUndoCommand) {\n\t\tconst undo = this._undo;\n\t\tundo.updateConfigs();\n\n\t\tif (!undo.undoEnabled) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (this._batchMode) {\n\t\t\tthis._batchAction.commands.push(command);\n\t\t} else {\n\t\t\tconst action = undo.action.create([command]);\n\t\t\tundo.logAction(action);\n\t\t}\n\t}\n\tprivate _storeEntityCommand(obj: TUndoValue, old: TUndoValue, actionType: TActionType, entityType: TEntityType) {\n\t\tconst undo = this._undo;\n\t\tconst command = undo.command.create(obj, old, actionType, entityType);\n\t\tthis._storeCommand(command);\n\t}\n\tprivate _storeTaskCommand(obj: ITask, type: TActionType) {\n\t\tthis._storeEntityCommand(obj, this.getInitialTask(obj.id), type, this._undo.command.entity.task);\n\t}\n\tprivate _storeLinkCommand(obj: ILink, type: TActionType) {\n\t\tthis._storeEntityCommand(obj, this.getInitialLink(obj.id), type, this._undo.command.entity.link);\n\t}\n\tprivate _getLinks(task: ITask) {\n\t\treturn task.$source.concat(task.$target);\n\t}\n\tprivate _storeTask(taskId: TaskID, overwrite: boolean = false) {\n\t\tthis.setInitialTask(taskId, overwrite);\n\t\tgantt.eachTask((child: ITask) => {\n\t\t\tthis.setInitialTask(child.id);\n\t\t}, taskId);\n\t\treturn true;\n\t}\n\tprivate _storeLink(linkId: LinkID, overwrite: boolean = false) {\n\t\tthis.setInitialLink(linkId, overwrite);\n\t\treturn true;\n\t}\n}", "declare var gantt;\n\nimport { Monitor } from \"./monitor\";\nimport { IMonitor, IUndoCommand, TEntityType, TUndoStack } from \"./types\";\nimport { Undo } from \"./undo\";\nconst _undo = new Undo();\nconst monitor: IMonitor = new Monitor(_undo);\n\ngantt.config.undo = true;\ngantt.config.redo = true;\n\n/**\n * entities that require different processing for undoing-redoing changes\n * @type {{link: string, task: string}}\n */\ngantt.config.undo_types = {\n\tlink: \"link\",\n\ttask: \"task\"\n};\n\n/**\n * types of traced actions\n * @type {{update: string, remove: string, add: string}}\n */\ngantt.config.undo_actions = {\n\tupdate: \"update\",\n\tremove: \"remove\", // remove item from datastore\n\tadd: \"add\",\n\tmove: \"move\" // move task in grid\n};\n\nif (!gantt.ext) {\n\tgantt.ext = {};\n}\n\ngantt.ext.undo = {\n\tundo: () => _undo.undo(),\n\tredo: () => _undo.redo(),\n\tgetUndoStack: () => _undo.getUndoStack(),\n\tgetRedoStack: () => _undo.getRedoStack(),\n\tclearUndoStack: () => _undo.clearUndoStack(),\n\tclearRedoStack: () => _undo.clearRedoStack(),\n\tsaveState: (id: TaskID | LinkID, type: TEntityType) => monitor.store(id, type, true)\n};\n\ngantt.undo = gantt.ext.undo.undo;\ngantt.redo = gantt.ext.undo.redo;\ngantt.getUndoStack = gantt.ext.undo.getUndoStack;\ngantt.getRedoStack = gantt.ext.undo.getRedoStack;\ngantt.clearUndoStack = gantt.ext.undo.clearUndoStack;\ngantt.clearRedoStack = gantt.ext.undo.clearRedoStack;\n\nfunction updTask(task: ITask, oldId: TaskID, newId: TaskID) {\n\tif (!task) { return; }\n\n\tif (task.id === oldId) {\n\t\ttask.id = newId;\n\t}\n\n\tif (task.parent === oldId) {\n\t\ttask.parent = newId;\n\t}\n}\n\nfunction changeTaskCommandId(command: IUndoCommand, oldId: TaskID, newId: TaskID) {\n\tupdTask(command.value, oldId, newId);\n\tupdTask(command.oldValue, oldId, newId);\n}\n\nfunction updLink(link: ILink, oldTaskId: TaskID, newTaskId: TaskID) {\n\tif (!link) { return; }\n\tif (link.source === oldTaskId) {\n\t\tlink.source = newTaskId;\n\t}\n\tif (link.target === oldTaskId) {\n\t\tlink.target = newTaskId;\n\t}\n}\n\nfunction changeLinkCommandId(command: IUndoCommand, oldId: LinkID, newId: LinkID) {\n\tupdLink(command.value, oldId, newId);\n\tupdLink(command.oldValue, oldId, newId);\n}\n\nfunction updateTasksIds(log: TUndoStack, oldId: TaskID, newId: TaskID) {\n\tconst undo = _undo;\n\n\tfor (let i = 0; i < log.length; i++) {\n\t\tconst entry = log[i];\n\t\tfor (let j = 0; j < entry.commands.length; j++) {\n\t\t\tif (entry.commands[j].entity === undo.command.entity.task) {\n\t\t\t\tchangeTaskCommandId(entry.commands[j], oldId, newId);\n\t\t\t} else if (entry.commands[j].entity === undo.command.entity.link) {\n\t\t\t\tchangeLinkCommandId(entry.commands[j], oldId, newId);\n\t\t\t}\n\t\t}\n\t}\n}\n\nfunction updateLinksIds(log: TUndoStack, oldId: LinkID, newId: LinkID) {\n\tconst undo = _undo;\n\n\tfor (let i = 0; i < log.length; i++) {\n\t\tconst entry = log[i];\n\t\tfor (let j = 0; j < entry.commands.length; j++) {\n\t\t\tconst command = entry.commands[j];\n\t\t\tif (command.entity === undo.command.entity.link) {\n\t\t\t\tif (command.value && command.value.id === oldId) {\n\t\t\t\t\tcommand.value.id = newId;\n\t\t\t\t}\n\t\t\t\tif (command.oldValue && command.oldValue.id === oldId) {\n\t\t\t\t\tcommand.oldValue.id = newId;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\ngantt.attachEvent(\"onTaskIdChange\", (oldId: TaskID, newId: TaskID) => {\n\tconst undo = _undo;\n\tupdateTasksIds(undo.getUndoStack(), oldId, newId);\n\tupdateTasksIds(undo.getRedoStack(), oldId, newId);\n});\n\ngantt.attachEvent(\"onLinkIdChange\", (oldId: LinkID, newId: LinkID) => {\n\tconst undo = _undo;\n\tupdateLinksIds(undo.getUndoStack(), oldId, newId);\n\tupdateLinksIds(undo.getRedoStack(), oldId, newId);\n});\n\ngantt.attachEvent(\"onGanttReady\", () => {\n\t_undo.updateConfigs();\n});\n"], "sourceRoot": ""}