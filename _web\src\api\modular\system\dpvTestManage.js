import { axios } from '@/utils/request'

// 获取产品信息列表
export function getProductList(parameter) {
  return axios({
    url: '/productManager/list',
    method: 'post',
    data: parameter
  })
}

// 测试进展-列表查询
export function getTestProgressList(parameter) {
  return axios({
    url: '/dpvTestProgressRecord/list',
    method: 'post',
    data: parameter
  })
}

// 测试进展-根据id更新测试进展记录属性
export function updateTestProgressRecord(parameter) {
  return axios({
    url: '/dpvTestProgressRecord/updateById',
    method: 'post',
    data: parameter
  })
}

// 测试进展-新增
export function addTestProgressRecord(parameter) {
  return axios({
    url: '/dpvTestProgressRecord/save',
    method: 'post',
    data: parameter
  })
}





// 结构化数据-列表查询
export function getTestStructureList(parameter) {
  return axios({
    url: '/testProjectStructureData/list',
    method: 'post',
    data: parameter
  })
}

// 结构化数据-列表查询
export function aiCheck(parameter) {
  return axios({
    url: '/testProjectStructureData/aiCheck',
    method: 'post',
    data: parameter
  })
}

// 结构化数据-新增
export function addTestStructureRecord(parameter) {
  return axios({
    url: '/testProjectStructureData/save',
    method: 'post',
    data: parameter
  })
}

// 结构化数据-根据id更新属性
export function updateTestStructureRecord(parameter) {
  return axios({
    url: '/testProjectStructureData/updateById',
    method: 'post',
    data: parameter
  })
}

// 结构化数据-根据id更新属性为null
export function updateNullTestStructureRecord(parameter) {
  return axios({
    url: '/testProjectStructureData/updateNullById',
    method: 'post',
    data: parameter
  })
}

// 结构化数据-根据id删除记录
export function deleteTestStructureRecord(parameter) {
  return axios({
    url: '/testProjectStructureData/deleteById',
    method: 'post',
    data: parameter
  })
}

// 结构化数据-excel模版导出
export function exportModel(parameter) {
  return axios({
    url: '/testProjectStructureData/exportStructureDataTemplate',
    method: 'post',
    responseType: 'blob',
    data: parameter
  })
}

// 结构化数据-导出特定列数据
export function exportStructureData(parameter) {
  return axios({
    url: '/testProjectStructureData/exportStructureData',
    method: 'post',
    responseType: 'blob',
    data: parameter
  })
}

// 测试结果-列表查询
export function getTestResultList(parameter) {
  return axios({
    url: '/testResultData/list',
    method: 'post',
    data: parameter
  })
}

// 测试结果-根据id更新测试结果
export function updateTestResultData(parameter) {
  return axios({
    url: '/testResultData/updateById',
    method: 'post',
    data: parameter
  })
}


// DPV-产品认证数据-分页列表查询
export function testProjectOutTestDataPageList(parameter) {
  return axios({
    url: '/testProjectOutTestData/pageList',
    method: 'post',
    data: parameter
  })
}

// DPV-产品认证数据-更新
export function testProjectOutTestDataUpdate(parameter) {
  return axios({
    url: '/testProjectOutTestData/update',
    method: 'post',
    data: parameter
  })
}

// DPV-产品认证数据-文件列表
export function testProjectOutTestDataGetManagerFileList(parameter) {
  return axios({
    url: '/testProjectOutTestData/getManagerFileList',
    method: 'post',
    data: parameter
  })
}
// DPV-产品认证数据-一览表
export function testProjectOutTestDataEchartsData(parameter) {
  return axios({
    url: '/testProjectOutTestData/echartsData',
    method: 'post',
    data: parameter
  })
}
// DPV-产品认证数据-认证情况概览
export function testProjectOutTestDataGetManagerTable(parameter) {
  return axios({
    url: '/testProjectOutTestData/getManagerTable',
    method: 'post',
    data: parameter
  })
}

// DPV-产品认证数据-更新
export function testProjectOutTestDataAdd() {
  return axios({
    url: '/testProjectOutTestData/save',
    method: 'post'
  })
}
