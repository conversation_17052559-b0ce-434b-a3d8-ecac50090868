import { axios } from '@/utils/request'

export function getMIChangeHistoryCurList (parameter, impBatteryId) {
  return axios({
    url: '/miChangeHistoryCur/getMIChangeHistoryCurList/' + impBatteryId,
    method: 'get',
    params: parameter
  })
}

export function insertMIChangeHistoryCur (parameter) {
  return axios({
    url: '/miChangeHistoryCur/insertMIChangeHistoryCur',
    method: 'post',
    data: parameter
  })
}

export function updateMIChangeHistoryCur (parameter) {
  return axios({
    url: '/miChangeHistoryCur/updateMIChangeHistoryCur',
    method: 'post',
    data: parameter
  })
}
