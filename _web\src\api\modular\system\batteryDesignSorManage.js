import { axios } from '@/utils/request'

export function list(parameter) {
    return axios({
        url: '/batteryDesignSor/list',
        method: 'post',
        data: parameter
    })
}

export function update(parameter) {
    return axios({
        url: '/batteryDesignSor/update',
        method: 'post',
        data: parameter
    })
}

export function add(parameter) {
    return axios({
        url: '/batteryDesignSor/add',
        method: 'post',
        data: parameter
    })
}


export function copy(parameter) {
    return axios({
        url: '/batteryDesignSor/copy',
        method: 'post',
        data: parameter
    })
}

export function exportExcel(parameter) {
    return axios({
        url: '/batteryDesignSor/export',
        method: 'get',
        params: parameter,
    })
}

export function exportExcel1(parameter) {
    return axios({
        url: '/batteryDesignSor/export1',
        method: 'get',
        params: parameter,
        responseType: 'blob'
    })
}


export function batteryDesignMiUpdate(parameter) {
    return axios({
        url: '/batteryDesignMi/update',
        method: 'post',
        data: parameter,

    })
}


export function batteryDesignMiList(parameter) {
    return axios({
        url: '/batteryDesignMi/list',
        method: 'post',
        data: parameter,

    })
}

