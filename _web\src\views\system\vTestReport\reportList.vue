<template>
  <div :style="{padding: padding}">
<!--    <pbiTabs :tabsList="tabsList" :activeKey="queryparam.type" @clickTab="changeTab"></pbiTabs>-->
    <tableIndex
      :pageLevel='1'
      :tableTotal='tableTotal'
      :pageTitleShow=false
      :otherHeight="parseInt(58 + width)"
      :loading='loading'
      @paginationChange="handlePageChange"
      @paginationSizeChange="handlePageChange"
    >
      <template #search>
        <pbiSearchContainer>
          <pbiSearchItem :span="4" label='建模名称' >
            <a-input v-model="queryparam.reportName" @keyup.enter="getList(true)" @change="getList(true)"/>
          </pbiSearchItem>
          <pbiSearchItem :span="4" label='建模类型'>
            <a-select show-search optionFilterProp="children" v-model="queryparam.type" :allowClear="true" @change="getList(true)" style="width: 100%" >
<!--              <a-select-option value="Cycle" v-if="hasPerm('report:dongLi')">动力-循环寿命</a-select-option>-->
<!--              <a-select-option value="Calendar" v-if="hasPerm('report:dongLi')">动力-日历寿命</a-select-option>-->
<!--              <a-select-option value="HlTemp" v-if="hasPerm('report:dongLi')">动力-高低温充放电</a-select-option>-->
<!--              <a-select-option value="V" v-if="hasPerm('report:v')">V圆柱</a-select-option>-->
<!--              <a-select-option value="循环DCR" v-if="hasPerm('report:v')">循环DCR</a-select-option>-->
<!--              <a-select-option value="SOC-DCR" v-if="hasPerm('report:v')">SOC-DCR</a-select-option>-->
<!--              <a-select-option value="内阻-DCR" v-if="hasPerm('report:v')">内阻-DCR</a-select-option>-->
<!--              <a-select-option value="SOC-OCV map charge" v-if="hasPerm('report:v')">SOC-OCV map charge</a-select-option>-->
<!--              <a-select-option value="SOC-OCV map discharge" v-if="hasPerm('report:v')">SOC-OCV map discharge</a-select-option>-->
<!--              <a-select-option value="循环" v-if="hasPerm('report:v')">循环</a-select-option>-->
<!--              <a-select-option value="倍率-倍率充放电&恒功率放电" v-if="hasPerm('report:v')">倍率-倍率充放电&恒功率放电</a-select-option>-->
<!--              <a-select-option value="倍率-stress" v-if="hasPerm('report:v')">倍率-stress</a-select-option>-->
<!--              <a-select-option value="倍率-脉冲放电" v-if="hasPerm('report:v')">倍率-脉冲放电</a-select-option>-->
<!--              <a-select-option value="G26" v-if="hasPerm('report:g26List')">G26循环</a-select-option>-->
<!--              <a-select-option value="G26Calendar" v-if="hasPerm('report:g26List')">G26日历寿命</a-select-option>-->

              <a-select-option value="Cycle">动力-循环寿命</a-select-option>
              <a-select-option value="Calendar">动力-日历寿命</a-select-option>
              <a-select-option value="HlTemp">动力-高低温充放电</a-select-option>
              <a-select-option value="CRate">动力-倍率充放电</a-select-option>
              <a-select-option value="G26">G26循环寿命</a-select-option>
              <a-select-option value="G26Calendar">G26日历寿命</a-select-option>
              <a-select-option value="V">V圆柱</a-select-option>
              <a-select-option value="循环DCR">循环DCR</a-select-option>
              <a-select-option value="SOC-DCR">SOC-DCR</a-select-option>
              <a-select-option value="内阻-DCR">内阻-DCR</a-select-option>
              <a-select-option value="SOC-OCV map charge">SOC-OCV map charge</a-select-option>
              <a-select-option value="SOC-OCV map discharge">SOC-OCV map discharge</a-select-option>
              <a-select-option value="循环">循环</a-select-option>
              <a-select-option value="倍率-倍率充放电&恒功率放电">倍率-倍率充放电&恒功率放电</a-select-option>
              <a-select-option value="倍率-stress">倍率-stress</a-select-option>
              <a-select-option value="倍率-脉冲放电">倍率-脉冲放电</a-select-option>
            </a-select>
          </pbiSearchItem>
          <pbiSearchItem :span="4" label='建模状态'>
            <a-select v-model="queryparam.fileStatus" @change="getList(true)" style="width: 100%" :allow-clear="true">
<!--              <a-select-option value="-10" v-if="queryparam.type == 'G26' || queryparam.type == 'G26Calendar'">-->

              <a-select-option value="0">
                待处理
              </a-select-option>

              <a-select-option value="10">
                进行中
              </a-select-option>
              <a-select-option value="20">
                已完成
              </a-select-option>
              <a-select-option value="-10">
                草稿箱
              </a-select-option>
              <a-select-option value="30">
                数据异常
              </a-select-option>
            </a-select>
          </pbiSearchItem>
          <pbiSearchItem :span="4" label='创建人' >
            <a-input v-model="queryparam.createName" @keyup.enter="getList(true)" @change="getList(true)"/>
          </pbiSearchItem>

          <pbiSearchItem :span="8" type='btn'>
<!--          (queryparam.type !== 'G26' && queryparam.type !== 'G26Calendar' ? 8 : 8):(queryparam.type !== 'G26' && queryparam.type !== 'G26Calendar' ? 12 : 12)" type='btn'>-->
            <a-button  style="margin-right: 12px;" @click="getList(true)" type="primary">刷新</a-button>
            <a-button v-if="queryparam.type == 'G26'" style="margin-right: 12px;"  @click="isShowAddPhase = true" type="primary">新增</a-button>
            <a-button v-if="queryparam.type == 'G26Calendar'" style="margin-right: 12px;"  @click="handleReportName" type="primary">新增</a-button>
            <a-button   @click="shareBatch" style="margin-right: 12px;" type="primary">传阅</a-button>
            <a-popconfirm
              title="确定删除吗?"
              ok-text="确定"
              cancel-text="取消"
              @confirm="deleteData()"
              :visible="beforeDeleteFlag"
              @cancel="() => beforeDeleteFlag = false"
              placement="topRight">
              <a-button class="right-btn" @click="beforeDelete" style="margin-right: 12px;">
                删除
              </a-button>
            </a-popconfirm>
            <a-button   @click="reset(true)">重置</a-button>
          </pbiSearchItem>

        </pbiSearchContainer>
      </template>


      <template #table>
        <ag-grid-vue :style="{height:tableHeight}"
                     class='table ag-theme-balham'
                     :tooltipShowDelay="0"
                     :columnDefs="exportTaskAdminColumns"
                     :rowData='rowData'
                     rowSelection="multiple"
                     :gridOptions="gridOptions"
                     @grid-ready="onGridReady"
                     :defaultColDef='defaultColDef'>
        </ag-grid-vue>
      </template>
    </tableIndex>

    <!-- 添加阶段弹窗 -->
    <a-modal title="样品阶段添加" :width="350" :visible="isShowAddPhase" @cancel="handleCancel">
      <template slot="footer">
        <a-button @click="handleCancel">
          取消
        </a-button>
        <a-button type="primary" @click="handlePhase">
          新增
        </a-button>
      </template>
      <div class="phase-modal">
        <span class="mr10">样品阶段名称:</span><a-input v-model="phaseNameActivity" style="width: 200px;"
          placeholder="请填写样品阶段名称" @keyup.enter="handlePhase" />
      </div>
    </a-modal>
    <a-modal
      title="传阅"
      :width="500"
      :zIndex="1"
      :visible="shareVisible"
      @cancel="() => shareVisible = false"
    >
      <div class="share-modal">
        <div class="title">已传阅人员</div>
        <div class="action-content mt16">
          <a-input v-model="shareUserParam.userName" placeholder="请输入账号/姓名" allow-clear style="width: 200px" @change="getShareUserList(checkRecord.id)">
            <a-icon slot="prefix" type="search" />
          </a-input>
          <a-button type="primary"  ghost @click="handleShowAddUser">新增传阅人员<a-icon type="plus"/></a-button>
        </div>

        <div class="mt16">
          <a-table
            :columns="shareUserColumns"
            :data-source="shareUserData"
            :rowKey="(record) => record.userAccount"
          >
              <span slot="action" slot-scope="text, record">
                  <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => shareUserDelete(record)">
                    <a>删除</a>
                  </a-popconfirm>
              </span>

          </a-table>
        </div>
      </div>



      <template slot="footer">
        <a-button key="back" @click="() => shareVisible = false">
          <a>关闭</a>
        </a-button>
      </template>
    </a-modal>

    <a-modal
      title="新增传阅人员"
      :visible="isShowAddUser"
      :width="528"
      centered
      :zIndex="99"
      @cancel="() => {
          isShowAddUser = false
          checkRecord = {}
        }"
    >
      <div class="share-modal">
        <div class="action-content">
          <a-input v-model="addUserParam.account" placeholder="请输入账号/姓名" allow-clear style="width: 200px" @change="getUserList">
            <a-icon slot="prefix" type="search" />
          </a-input>
          <treeselect
            v-model="addUserParam.grantOrgIdList"
            placeholder="请选择部门"
            value-consists-of="BRANCH_PRIORITY"
            :limit="1"
            :multiple="true"
            :max-width="270"
            :options="orgOptions"
            @input="getUserList"
          />
        </div>
        <div class="mt16">
          <a-table
            :columns="addUserColumns"
            :dataSource="addUserData"
            :rowKey="(record) => record.account"
            :rowSelection="{ selectedRowKeys: userSelectedRowKeys, onChange: onUserSelectChange }"
            :pagination="userPagination"
            @change="handleUserTableChange"
          >
          </a-table>
        </div>
      </div>
      <template slot="footer">
        <a-button key="confirm" type="primary" @click="addShareUsers(checkRecord.id)">
          <a>传阅</a>
        </a-button>
        <a-button key="back" @click="() => {
          isShowAddUser = false
          checkRecord = {}
        }">
          <a>取消传阅</a>
        </a-button>
      </template>
    </a-modal>

    <a-modal
      title="变更履历"
      :visible="historyVisible"
      width="60%"
      @cancel="() => historyVisible = false"
    >

      <template slot="footer" >
        <a-button key="back" @click="() => historyVisible = false">
          <a>关闭</a>
        </a-button>
      </template>

      <a-table
        :columns="historyColumns"
        :data-source="historyData"
        :rowKey="(record) => record.id"
      >
            <span slot="reportName" slot-scope="text, record">
              <a @click="historyPushToReview(record)" v-if="record.fileStatus == 20">{{text}}</a>
              <span v-else>{{text}}</span>
            </span>

      </a-table>


    </a-modal>
  </div>
</template>

<script>
import {
  testReportRegenerateData,testReportDownload,testReportDelete,
  testReportPageList,g26BeginExportData
} from '@/api/modular/system/limsManager'

import {
  testReportShareList,testReportShareAdd,testReportShareDelete,
  testReportHistoryList
} from '@/api/modular/system/reportManager'
import {mapGetters } from "vuex";
import { userList } from '@/api/modular/system/roleManage'

import {
  getMinioDownloadUrl
} from '@/api/modular/system/fileManage'

import { getUserPage } from '@/api/modular/system/userManage'
import { getOrgTree } from '@/api/modular/system/orgManage'


import { STable } from '@/components'
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"

import jsonBigint from 'json-bigint'
import checkRecord from "../batterydesign/checkRecord";
import axios from "axios";
//解压字符串
import {decodeAndDecompress} from "@/utils/util"
import pbiTabs from "@/components/pageTool/components/pbiTabs.vue";

  export default {
    props: {
      width:{
        type: Number,
        default: 0
      },
      padding:{
        type: String,
        default: '8px'
      }
    },
    components: {
      pbiTabs,
      STable,
      Treeselect,
      reportName:{
        template: '<div><div v-if="params.data.fileStatus != 20" style="text-align: left">{{params.value}}</div><a style="text-align: left" v-else-if="params.data.fileStatus == 20 && [\'G26\', \'G26Calendar\'].includes(params.data.type)" @click="params.onG26DownloadFile(params.data,params.data.reportName,null,\'.xlsx\')"><a-spin :spinning="params.data.status == 1" tip="正在下载中" size="small">{{params.value}}</a-spin></a><a target="_blank" @click="params.onClick(params.data)" v-else style="margin-right: 12px">{{params.value}}</a></div>'
      },
      action:{
        template: '<div><a-popconfirm title="确定要刷新数据吗?" v-if="params.data.fileStatus != 10 && params.data.fileStatus != 0" ok-text="确定" cancel-text="取消" @confirm="params.onRefreshData(params.data)" placement="left" :style="{marginRight: params.data.type != \'all\'?\'12px\':\'0px\'}"><a-tooltip><template slot="title">刷新数据</template><a-icon type="reload" /></a-tooltip></a-popconfirm><a-popconfirm title="确定要重新生成吗?" ok-text="确定" cancel-text="取消" @confirm="params.onReExport(params.data)" placement="left" style="margin-right: 12px"><a-tooltip><template slot="title">重新生成</template><a-icon type="file-sync"/></a-tooltip></a-popconfirm><a-tooltip  style="margin-right: 12px" @click="params.onGetShareUserList(params.data.id,true,params.data)"><template slot="title">传阅</template><a-icon type="share-alt" /></a-tooltip><a-tooltip @click="params.onGetHistoryList(params.data.id,true,params.data)" v-if="params.data.type != \'G26\' && params.data.type != \'G26Calendar\'"><template slot="title">变更履历</template><a-icon type="read"/></a-tooltip></div>'
      },
      template:{
        template:'<div><div v-if="params.data.fileStatus != 20" style="text-align: left">{{params.value}}</div><a style="text-align: left" v-if="params.data.fileStatus == 20" @click="params.onG26DownloadFile(params.data,params.data.reportName,null,\'.xlsx\')"><a-spin :spinning="params.data.status == 1" tip="正在下载中" size="small">{{params.data.reportName}}</a-spin></a></template>'
      }
    },
    computed: {
      ...mapGetters(['userInfo','testTaskFilterData']),
    },
    data() {
      return {
        tabsList:[
          {value:'V',label:"V圆柱",permission:'report:v'},
          {value:'循环DCR',label:"循环DCR",permission:'report:v'},
          {value:'SOC-DCR',label:"SOC-DCR",permission:'report:v'},
          {value:'内阻-DCR',label:"内阻-DCR",permission:'report:v'},
          {value:'SOC-OCV map charge',label:"SOC-OCV map charge",permission:'report:v'},
          {value:'SOC-OCV map discharge',label:"SOC-OCV map discharge",permission:'report:v'},
          {value:'循环',label:"循环",permission:'report:v'},
          {value:'倍率-倍率充放电&恒功率放电',label:"倍率-倍率充放电&恒功率放电",permission:'report:v'},
          {value:'倍率-stress',label:"倍率-stress",permission:'report:v'},
          {value:'倍率-脉冲放电',label:"倍率-脉冲放电",permission:'report:v'},
          {value:'G26',label:"G26循环",permission:'report:g26List'},
          {value:'G26Calendar',label:"G26日历寿命",permission:'report:g26List'},
          {value:'DongLi',label:"动力",permission:'report:dongLi'},
        ],
        tableTotal:0,
        rowData:[],
        loading: false,
        pageNo: 1,
        pageSize: 20,
        gridApi: null,
        columnApi: null,
        gridOptions: {
          onSelectionChanged: this.onSelectionChanged,
          suppressCellSelection: false,
          animateRows:false,
          suppressRowTransform :true,
          onGridReady: (event) => {
            event.api.sizeColumnsToFit();
          }
        },
        defaultColDef: {
          filter: false,
          floatingFilter: false,
          editable: false,
          wrapText: true,
          autoHeight: true,
          width:100,
        },
        tableHeight: document.body.clientHeight - 156 - this.width +'px' ,
        checkRecord:{},
        shareVisible:false,
        isShowAddPhase:false,
        isShowAddUser:false,
        phaseNameActivity:'',
        shareUserColumns:[
          {
            title: '账号',
            dataIndex: 'userAccount',
            align:'center'
          },
          {
            title: '姓名',
            dataIndex: 'userName',
            align:'center'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:'center',
            scopedSlots: { customRender: 'action' }
          }],
        shareUserData:[],

        historyVisible:false,
        historyColumns:[
          {
            title: '序号',
            align: 'center',
            width: 50,
            customRender: (text, record, index) => index + 1
          }, {
            title: '建模名称',
            dataIndex: 'reportName',
            align: 'center',

            scopedSlots: {customRender: 'reportName'},
          }, {
            title: '建模类型',
            width: 180,
            align: 'center',
            dataIndex: 'type',
            customRender: (text, record, index) => {
              switch (text) {
                case 'Cycle':
                  return '动力-循环寿命';
                case 'Calendar':
                  return '动力-日历寿命';
                case 'HlTemp':
                  return '动力-高低温充放电';
                case 'CRate':
                  return '动力-倍率充放电';
                default:
                  return text;
              }
            }
          }, {
            title: '操作类型',
            width: 70,
            align: 'center',
            dataIndex: 'operateType',
            customRender: (text, record, index) => {
              if(record.operateType == 'add'){
                return "初次创建"
              }
              if(record.operateType == 'update'){
                return "更新数据"
              }
              if(record.operateType == 'refresh'){
                return "刷新数据"
              }
            }
          },  {
            title: '操作时间',
            width: 150,
            align: 'center',
            dataIndex: 'createTime',

          },{
            title: '操作人',
            width: 70,
            align: 'center',
            dataIndex: 'createName',

          }
        ],
        historyData:[],
        shareTab:'list',
        shareUserParam:{},
        addUserParam:{
          grantOrgIdList:[]
        },
        orgOptions:[],
        addUserColumns: [

          {
            title: '账号',
            dataIndex: 'account',
            align:'center'
          },
          {
            title: '姓名',
            dataIndex: 'name',
            align:'center'
          }
        ],
        addUserData:[],
        userSelectedRowKeys:[],
        userSelectedRows:[],
        userPagination:{
          current:1,
          pageSize:10,
          total:0,
          size:"small"
        },

        selectedRowKeys: [],
        selectedRows: [],
        beforeDeleteFlag: false,
        id:null,
        exportTaskAdminColumns: [
          {
            width: 40,
            sortable: false,
            checkboxSelection: true,
            headerCheckboxSelection:true
          },
          {
            headerName: '序号',
            field: 'id',
            width: 50,
            cellRenderer: function (params) {
              return parseInt(params.node.id) + 1
            }
          },  {
            headerName: '建模名称',
            field: 'reportName',
            width: 200,
            cellRenderer: 'reportName',
            cellRendererParams: {onClick: this.pushToReview, onG26DownloadFile : this.g26DownloadFile},
            tooltipValueGetter: (p) => p.value,
            cellStyle: () =>  {return {textAlign:'left'}},
          }, {
            headerName: '建模类型',
            width: 105,
            field: 'type',
            cellRenderer: function (params) {
              switch (params.data.type) {
                case 'Cycle':
                  return '动力-循环寿命';
                case 'Calendar':
                  return '动力-日历寿命';
                case 'HlTemp':
                  return '动力-高低温充放电';
                case 'CRate':
                  return '动力-倍率充放电';
                case 'G26':
                  return 'G26循环寿命';
                case 'G26Calendar':
                  return 'G26日历寿命';
                default:
                  return params.data.type;
              }
            },
            tooltipValueGetter: (params) => {
              switch (params.data.type) {
                case 'Cycle':
                  return '动力-循环寿命';
                case 'Calendar':
                  return '动力-日历寿命';
                case 'HlTemp':
                  return '动力-高低温充放电';
                case 'CRate':
                  return '动力-倍率充放电';
                case 'G26':
                  return 'G26循环寿命';
                case 'G26Calendar':
                  return 'G26日历寿命';
                default:
                  return params.data.type;
              }
            }
          }, {
            headerName: '建模状态',
            width: 80,
            field: 'fileStatus',
            cellRenderer: function (params) {
              if(params.data.fileStatus == -10){
                return "草稿"
              }
              if(params.data.fileStatus == 0){
                return "待处理"
              }
              if(params.data.fileStatus == 10){
                return "进行中"
              }
              if(params.data.fileStatus == 20){
                return "已完成"
              }
              if(params.data.fileStatus == 30){
                //导出失败  展示为 新建
                return "数据异常"
              }
            }
          },
          {
            headerName: '创建人',
            width: 90,
            field: 'createName',
          },{
            headerName: '创建时间',
            field: 'createTime',
          },{
            headerName: '开始时间',
            field: 'beginTime',
          }, {
            headerName: '完成时间',
            field: 'finishTime',
          }, {
            headerName: '操作',
            width: 140,
            field: 'action',
            cellRenderer: 'action',
            cellRendererParams: {onDownloadFile: this.downloadFile,onRefreshData:this.refreshData,
              onReExport:this.reExport,onGetShareUserList:this.getShareUserList,onGetHistoryList:this.getHistoryList},
          }
        ],
        exportTaskcolumns: [
          {
            width: 40,
            sortable: false,
            checkboxSelection: true,
            headerCheckboxSelection:true
          },
          {
            headerName: '序号',
            field: 'id',
            width: 50,
            cellRenderer: function (params) {
              return parseInt(params.node.id) + 1
            }
          },  {
            headerName: '建模名称',
            field: 'reportName',
            width: 200,
            cellRenderer: 'reportName',
            flex: 1.5,
            cellRendererParams: {onClick: this.pushToReview},
            tooltipValueGetter: (p) => p.value,
            cellStyle: () =>  {return {textAlign:'left'}},
          }, {
            headerName: '建模类型',
            width: 110,
            field: 'type',
            flex: 1,
            cellRenderer: function (params) {
              switch (params.data.type) {
                case 'Cycle':
                  return '动力-循环寿命';
                case 'Calendar':
                  return '动力-日历寿命';
                case 'HlTemp':
                  return '动力-高低温充放电';
                case 'CRate':
                  return '动力-倍率充放电';
                default:
                  return params.data.type;
              }
            },
            tooltipValueGetter: (params) => {
              switch (params.data.type) {
                case 'Cycle':
                  return '动力-循环寿命';
                case 'Calendar':
                  return '动力-日历寿命';
                case 'HlTemp':
                  return '动力-高低温充放电';
                case 'CRate':
                  return '动力-倍率充放电';
                default:
                  return params.data.type;
              }
            }
          }, {
            headerName: '建模状态',
            width: 80,
            field: 'fileStatus',
            cellRenderer: function (params) {
              if(params.data.fileStatus == -10){
                return "草稿"
              }
              if(params.data.fileStatus == 0){
                return "待处理"
              }
              if(params.data.fileStatus == 10){
                return "进行中"
              }
              if(params.data.fileStatus == 20){
                return "已完成"
              }
              if(params.data.fileStatus == 30){
                //导出失败  展示为 新建
                return "数据异常"
              }
            }
          },
          {
            headerName: '创建人',
            width: 90,
            field: 'createName',
          },
          {
            headerName: '创建时间',
            field: 'createTime',
          },{
            headerName: '开始时间',
            field: 'beginTime',
          }, {
            headerName: '完成时间',
            field: 'finishTime',
          }, {
            headerName: '操作',
            width: 140,
            field: 'action',
            cellRenderer: 'action',
            cellRendererParams: {onDownloadFile: this.downloadFile,onRefreshData:this.refreshData,
              onReExport:this.reExport,onGetShareUserList:this.getShareUserList,onGetHistoryList:this.getHistoryList},
          }
        ],
        g26ExportTaskcolumns: [
          {
            width: 40,
            sortable: false,
            checkboxSelection: true,
            headerCheckboxSelection:true
          },
          {
            headerName: '序号',
            field: 'id',
            width: 50,
            cellRenderer: function (params) {
              return parseInt(params.node.id) + 1
            }
          },{
            headerName: '建模名称',
            field: 'reportName',
            width: 100,
            flex: 1.5,
            cellRenderer: 'template',
            cellRendererParams: {onG26DownloadFile : this.g26DownloadFile},
          },{
            headerName: '原始数据',
            field: 'reportData',
            width: 100,
            flex: 1,
            cellRenderer: 'reportData'
          }, {
            headerName: '建模类型',
            width: 110,
            field: 'type',
            cellRenderer: function (params) {
              if(params.data.type == "G26"){
                return "G26循环"
              }
              if(params.data.type == "G26Calendar"){
                return "G26日历寿命"
              }
            },
            tooltipValueGetter: (params) => {
              if(params.data.type == "G26"){
                return "G26循环"
              }
              if(params.data.type == "G26Calendar"){
                return "G26日历寿命"
              }
            }
          }, {
            headerName: '建模状态',
            width: 80,
            field: 'fileStatus',
            cellRenderer: function (params) {
              if(params.data.fileStatus == -10){
                return "草稿"
              }
              if(params.data.fileStatus == 0){
                return "待处理"
              }
              if(params.data.fileStatus == 10){
                return "进行中"
              }
              if(params.data.fileStatus == 20){
                return "已完成"
              }
              if(params.data.fileStatus == 30){
                //导出失败  展示为 新建
                return "数据异常"
              }
            }
          },
          {
            headerName: '创建人',
            width: 90,
            field: 'createName',
          },{
            headerName: '创建时间',
            field: 'createTime',
          },{
            headerName: '开始时间',
            field: 'beginTime',
          }, {
            headerName: '完成时间',
            field: 'finishTime',
          }, {
            headerName: '操作',
            width: 140,
            field: 'action',
            cellRenderer: 'action',
            cellRendererParams: {onDownloadFile: this.downloadFile,onRefreshData:this.refreshData,
              onReExport:this.reExport,onGetShareUserList:this.getShareUserList,onGetHistoryList:this.getHistoryList},
          }
        ],

        record:{},
        labelCol: {
          xs: {
            span: 12
          },
          sm: {
            span: 8
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 14
          }
        },
        visible1: false,
        confirmLoading1: false,
        showExport:true,
        // queryparam:{type:this.hasPerm('report:dongLi')?"Cycle":this.hasPerm('report:g26List')?"G26":this.hasPerm('report:v')?"V":null},
        queryparam:{},
        /*headers: {
          //Authorization: 'Bearer ' + Vue.ls.get(ACCESS_TOKEN),
          Authorization: 'Bearer eyJhbGciOiJIUzUxMiJ9.***********************************************************************************************************************************************************************************************************************.LgAC7GwkaFwV5NJD7wrr4gQGYzFMfbJL46hei2EclvHWVE1WGgV8ucyJ5XkuF5Cio_24ZuIqwAyVXyD02snFmQ',
        },*/
      }
    },

    mounted() {
      // if(this.$route.query.type){
      //   this.queryparam.type = this.$route.query.type
      // }
      this.$nextTick(() => {
        setTimeout(() => {
          this.loadData()
        }, 100)

      })
    },
    methods: {
      handlePageChange(value) {
        let {current, pageSize} = value
        this.pageNo = current
        this.pageSize = pageSize
        this.loadData()

      },
      loadData() {
        this.loading = true
        // if(this.queryparam.type == null && this.$route.query.type != null){
        //   this.queryparam.type = this.$route.query.type
        // }else if(this.hasPerm('report:dongLi') && this.queryparam.type == null){
        //   this.queryparam.type = "Cycle"
        // }else if(!this.hasPerm('report:v') && this.queryparam.type == null){
        //   this.queryparam.type = this.hasPerm('report:g26List') ? 'G26' : this.hasPerm('report:dongLi') ? 'DongLi' : null
        // }

        // if(this.queryparam.type == null){
        //   this.queryparam.type = 'V'
        // }

        this.queryparam.id = this.id

        // 动力筛选报告类型，不改变原type不影响tabs
        // const param = _.cloneDeep(this.queryparam)
        // if (this.queryparam.type == 'DongLi' && this.queryparam.dongLiType) {
        //   param.type = this.queryparam.dongLiType
        // }

        testReportPageList({
          ...{
            pageNo: this.pageNo,
            pageSize: this.pageSize
          }, ...this.queryparam
          } ).then((res) => {
          if (res.success) {
            this.rowData = res.data.rows
            this.tableTotal = res.data.totalRows
          }
        }).finally(() => {
          if(this.rowData.length == 0 && this.pageNo > 1){
            // this.pageNo -= 1
            this.pageNo = Math.max(1, Math.ceil(this.tableTotal / this.pageSize))
            this.loadData()
          }
          this.loading = false
        })
      },
      onSelectionChanged(event) {
        // 获取当前选中的行
        const selectedNodes = this.gridApi.getSelectedNodes();
        const selectedData = selectedNodes.map(node => node.data);

        // 更新选中的行数据
        this.selectedRows = selectedData;
        this.selectedRowKeys = selectedData;

      },

      onGridReady(params) {
        this.gridApi = params.api;
        this.columnApi = params.columnApi;
        // params.api.sizeColumnsToFit();
      },
      g26UpdateDataFileStatus(record){
        g26BeginExportData(record).then(() => {
          this.loadData()
        })
      },
      g26DownloadFile(record,name,fileId,suffix) {
        getMinioDownloadUrl(fileId != null?fileId:record.fileId,name+suffix).then(res => {
          if(res.data == null){
            record.status = 1
            //test用8280
            //pro用8080
            let url = 'http://10.5.65.248:8080/sysFileInfo/downloadWithAuth'
            axios({
              url: url,
              method: 'POST',
              //headers:this.headers,
              data:{id:fileId != null?fileId:record.fileId,userAccount:this.userInfo.account,userName:this.userInfo.name},
              responseType: 'blob' // 设置响应类型为blob
            })
              .then(response => {
                if(response.data.size < 500){
                  location.reload()
                  return
                }
                const url = window.URL.createObjectURL(new Blob([response.data]));
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', name+(suffix != null?suffix:'.xlsx')); // 设置下载文件的名称和扩展名
                document.body.appendChild(link);
                link.click();
                record.status = 0
              })
              .catch(error => {
                record.status = 0
                //console.error(error);
              });
          }else{
            let fileUrl = res.data
            fileUrl = fileUrl.replace("http://***********:9000/", "/minioDownload/");
            const link = document.createElement('a');
            link.href = fileUrl;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link)
          }
        })
      },
      g26DownloadZipData(fileId,url) {
        getMinioDownloadUrl(fileId).then(res => {
          if(res.data == null){
            let fileUrl = url
            const link = document.createElement('a');
            link.href = fileUrl;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link)
          }else{
            let fileUrl = res.data
            const link = document.createElement('a');
            link.href = fileUrl;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link)
          }
        })

      },
      g26DownloadData(record,name,fileId,suffix) {

      },


      pushToReview(record){
        if (record.type === '倍率-脉冲放电' || record.type === '倍率-脉放map') {
          window.open("/v_report_preview?id="+record.id+"&type="+'倍率-stress',"_blank")
          return
        }
        window.open("/v_report_preview?id="+record.id+"&type="+record.type,"_blank")
      },
      historyPushToReview(record){
        window.open("/v_report_preview?id="+record.id+"&type="+record.type,"_blank")
      },
      downloadFile(record,name) {
        record.status = 1

        testReportDownload({id:record.id})
          .then(response => {
            if(response.data.size < 500){
              location.reload()
              return
            }
            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', name+'.xlsx'); // 设置下载文件的名称和扩展名
            document.body.appendChild(link);
            link.click();
            record.status = 0
          })
          .catch(error => {
            record.status = 0
          });
      },
      beforeDelete() {
        console.log(this.selectedRows)
        console.log(this.userInfo)
        if (this.selectedRows.length === 0) {
          this.$message.warning("请至少选择一条数据进行删除！")
        } else {
          if (this.selectedRows.some(row => row.createAccount !== this.userInfo.account)) {
            this.$message.warning("请选择自己创建的数据进行删除！")
            return;
          }
          this.beforeDeleteFlag = true
        }
      },
      shareBatch() {
        if (this.selectedRows.length === 0) {
          this.$message.warning("请至少选择一条数据进行分享！")
        } else {
          this.checkRecord = {}
          this.handleShowAddUser()
        }
      },
      deleteData() {
        this.selectedRows.forEach(item => {
          testReportDelete({ id: item.id })
        })
        this.beforeDeleteFlag = false
        setTimeout(() => {
          this.selectedRows = []
          this.selectedRowKeys = []
          this.loadData()
        }, 800)
      },
      onSelectChange(selectedRowKeys, selectedRows) {
        // console.log('selectedRowKeys',this.selectedRowKeys)
        this.selectedRows = selectedRows
        this.selectedRowKeys = selectedRowKeys
      },
      refreshData(record) {
        testReportRegenerateData(record).then((res) => {
          setTimeout(() => {
            this.loadData()
          }, 600)
        })
      },
      reExport(record){
        if(record.type == 'G26') {
          this.$store.commit('setTaskFilterData', record);
          this.$router.push('/g26_report_inline');
        } else if (record.type == 'G26Calendar') {
          this.$store.commit('setTaskFilterData', record);
          this.$router.push('/g26CalendarAgingReport');
        } else if (['Cycle', 'Calendar', 'HlTemp', 'CRate'].includes(record.type)) {
          this.$store.commit('setTaskFilterData', record);
          this.$router.push('/dong_li_report_build');
        } else {
          let json = jsonBigint({storeAsString:true})
          let param = json.parse(decodeAndDecompress(record.queryParam))
          this.$store.commit('setTaskFilterData', param);
          this.$router.push('/v_report_inline');
        }

      },
      // async handleChangType(){
      //   await this.getList(true)
      //   this.gridOptions.sizeColumnsToFit()
      // },
      getList(flag){
        if(flag){
          this.id = null
        }
        this.loadData()
      },
      reset(){
        this.queryparam = {}
        this.loadData()
      },

      changeTab(value){
        this.queryparam.type = value
        this.id = null
        this.loadData()
      },

      getUserList(){
        const params = {
          pageNo:this.userPagination.current,
          pageSize:this.userPagination.pageSize,
          searchValue:this.addUserParam.account || '',
          grantOrgIdList: this.addUserParam.grantOrgIdList.join(','),
        }

        getUserPage(params).then(res => {
          if (res.success) {
            this.userSelectedRows = []
            this.userSelectedRowKeys = []
            this.userPagination.total = res.data.totalRows
            for (let i = 0; i < res.data.rows.length; i++) {
              if(this.shareUserData.find(s => s.userAccount == res.data.rows[i].account) != null){
                  this.userSelectedRows.push(res.data.rows[i])
                  this.userSelectedRowKeys.push(res.data.rows[i].account)
              }
            }
            this.addUserData = res.data.rows
            this.$forceUpdate()
          } else {
            this.$message.error('查询失败：' + res.message)
          }

        })

      },
      // 获取组织架构
      getOrgList(){
        getOrgTree({}).then(res => {
          if(res.success){
            this.orgOptions = []
            res.data[0].children.forEach(v => {
              let $item = {
                id: v.id,
                label:v.title
              }
              if(v.children.length !== 0){
                $item.children = []
                v.children.forEach(chilV => {
                  $item.children.push({
                    id:chilV.id,
                    label:chilV.title
                  })
                })
              }
              this.orgOptions.push($item)
            })
          }else{
            this.$message.error('部门信息查询失败：' + res.message)
          }
        })
      },
      onUserSelectChange(keys,rows){
        this.userSelectedRowKeys = keys
        this.userSelectedRows = rows
      },
      // 新增传阅人员--分页
      handleUserTableChange(pagination, filters, sorter) {
        this.userPagination.current = pagination.current

        this.getUserList()
      },
      addShareUsers(id){
        let reportIdList = []
        if(id){
          reportIdList.push(id)
        }else{
          this.selectedRows.forEach(row => {
            reportIdList.push(row.id);
          });
        }
        let flag = true
        reportIdList.forEach(reportId => {
          for (let i = 0; i < this.userSelectedRows.length; i++) {
            testReportShareAdd({reportId:reportId,
              userAccount:this.userSelectedRows[i].account,userName:this.userSelectedRows[i].name}).then((res) => {
              if (res.success) {
                // this.$message.success('传阅成功')
                this.isShowAddUser = false
                testReportShareList(this.shareUserParam).then(res => {
                  this.shareUserData = res.data
                })
              } else {
                flag = false
                // this.$message.error('传阅失败：' + res.message)
              }
            }).catch((err) => {
              flag = false
              // this.$message.error('传阅失败：' + err.message)
            })
          }
        })

        if(flag){
          this.$message.success('传阅成功')
        }else{
          this.$message.error('传阅失败：' + err.message)
        }


      },
      getShareUserList(recordId,first,record){
        if(first){
          this.checkRecord = record
        }
        this.shareUserParam.reportId = recordId
        testReportShareList(this.shareUserParam).then(res => {
          this.shareUserData = res.data
          this.shareVisible = true
        })
      },
      getHistoryList(recordId,first,record){
        if(first){
          this.checkRecord = record
        }

        testReportHistoryList({reportId:recordId}).then(res => {
          this.historyData = res.data
          this.historyVisible = true
        })
      },
      shareUserDelete (record) {
        testReportShareDelete({id:record.id}).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.getShareUserList(this.checkRecord.id)
          } else {
            this.$message.error('删除失败：' + res.message)
          }
        }).catch((err) => {
          this.$message.error('删除错误：' + err.message)
        })
      },
      handlePhase(){
        this.$store.commit('setTaskID', this.phaseNameActivity);
        this.$router.push("/g26_report_inline")
      },
      handleReportName() {
        this.$router.push("/g26CalendarAgingReport")
      },
      handleCancel(){
        this.isShowAddPhase = false
      },
      handleShowAddUser(){
        this.isShowAddUser = true
        this.getUserList()
        this.getOrgList()
      }

    },

  }
</script>
<style lang="less" scoped>
@import '/src/components/pageTool/style/pbiSearchItem.less';
  .tips {
    color: #1890ff;
  }

  /deep/ .ant-tabs-bar{
    margin:  0 0 10px;
  }

  /deep/ .ant-form-item{
    margin-bottom: 10px;
  }

  /deep/ .anticon svg {
    display: inline-block;
    width: 15px;
    height: 15px;
  }

 /deep/ .ant-table-thead tr th{
  padding: 8px !important;
 }

 /deep/ .ant-table-tbody tr td{
  padding: 8px !important;
 }

 /* 分享用户 */

 .share-modal .title{
  font-size: 16px;
  font-weight: 600;
  text-align: center;
 }
 .share-modal .action-content{
  display: flex;
  justify-content: space-between;
  align-items: center;

 }
 /deep/.ant-input{
  border-radius: 5px;
 }

 /deep/.vue-treeselect{
  max-width: 270px;
 }
 /deep/.vue-treeselect--has-value .vue-treeselect__multi-value{
  margin-top: -3px;
  margin-bottom: 0;
 }
 /deep/.vue-treeselect__placeholder{
  line-height: 30px;
 }

 /deep/.ant-modal-footer{
  padding: 0 24px 24px;
 }



 .mt16{
  margin-top: 16px;
 }

  /deep/.ant-select-dropdown-menu-item {
    font-size: 12px;
  }

  
  /deep/.ag-ltr .ag-cell {
    line-height: 1.5;
  }
  /deep/.ag-header-cell-label {
    text-overflow: clip;
    overflow: visible;
    white-space: normal;
  }
  /deep/.ag-cell {
      white-space: normal;
      word-wrap: break-word;
      height: auto;
      padding: 8px;
      min-height: 100%;
      display: flex;
      justify-content: center;
  }
  /deep/ .ag-cell-value,
  /deep/ .ag-cell-value div{
    text-align: center !important;
  }

  /deep/ .ag-ltr .ag-header-select-all,
  /deep/ .ag-ltr .ag-selection-checkbox{
    margin-right: 0;
  }

  /deep/ .ag-header-row .ag-header-cell:first-child .ag-header-cell-comp-wrapper{
    display: none;
  }

  /deep/ .ag-header-cell{
    display: flex;
    justify-content: center;
  }

  // 序号的高度
  /deep/.ag-cell-wrapper > *:not(.ag-cell-value):not(.ag-group-value){
    height: fit-content;
  }

</style>
