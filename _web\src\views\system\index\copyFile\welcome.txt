<template>
  <div>

    <div class="dashboard" ref="dashboard">
      <div class="col1">
        <div class="item">
          <div class="head"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 iKfgJk svg-icon-path-icon fill" viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path fill-rule="evenodd" clip-rule="evenodd" d="M19 10C19 12.2091 17.2091 14 15 14C12.7909 14 11 12.2091 11 10C11 7.79086 12.7909 6 15 6C17.2091 6 19 7.79086 19 10ZM15 28C17.2091 28 19 26.2091 19 24C19 21.7909 17.2091 20 15 20C12.7909 20 11 21.7909 11 24C11 26.2091 12.7909 28 15 28ZM15 42C17.2091 42 19 40.2091 19 38C19 35.7909 17.2091 34 15 34C12.7909 34 11 35.7909 11 38C11 40.2091 12.7909 42 15 42Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M37 10C37 12.2091 35.2091 14 33 14C30.7909 14 29 12.2091 29 10C29 7.79086 30.7909 6 33 6C35.2091 6 37 7.79086 37 10ZM33 28C35.2091 28 37 26.2091 37 24C37 21.7909 35.2091 20 33 20C30.7909 20 29 21.7909 29 24C29 26.2091 30.7909 28 33 28ZM33 42C35.2091 42 37 40.2091 37 38C37 35.7909 35.2091 34 33 34C30.7909 34 29 35.7909 29 38C29 40.2091 30.7909 42 33 42Z" fill="#333"></path></g></svg><span>项目费用管理</span></div>
          <div class='chart_table charge' ref="charge"></div>
        </div>
        <div class="item">
          <div class="head"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 iKfgJk svg-icon-path-icon fill" viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path fill-rule="evenodd" clip-rule="evenodd" d="M19 10C19 12.2091 17.2091 14 15 14C12.7909 14 11 12.2091 11 10C11 7.79086 12.7909 6 15 6C17.2091 6 19 7.79086 19 10ZM15 28C17.2091 28 19 26.2091 19 24C19 21.7909 17.2091 20 15 20C12.7909 20 11 21.7909 11 24C11 26.2091 12.7909 28 15 28ZM15 42C17.2091 42 19 40.2091 19 38C19 35.7909 17.2091 34 15 34C12.7909 34 11 35.7909 11 38C11 40.2091 12.7909 42 15 42Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M37 10C37 12.2091 35.2091 14 33 14C30.7909 14 29 12.2091 29 10C29 7.79086 30.7909 6 33 6C35.2091 6 37 7.79086 37 10ZM33 28C35.2091 28 37 26.2091 37 24C37 21.7909 35.2091 20 33 20C30.7909 20 29 21.7909 29 24C29 26.2091 30.7909 28 33 28ZM33 42C35.2091 42 37 40.2091 37 38C37 35.7909 35.2091 34 33 34C30.7909 34 29 35.7909 29 38C29 40.2091 30.7909 42 33 42Z" fill="#333"></path></g></svg><span>产品质量管理</span></div>
          <div class='chart_table' ref="quality"></div>
        </div>
      </div>
      <div class="col2">
        <div class="item" style="position:relative" v-if="projectFlag == 1">
          <a-dropdown :trigger="['click']" class="dropdown">
            <a class="ant-dropdown-link" @click="e => e.preventDefault()">
              各研究所产品等级分析<a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item key="1">
                <a @click="switchToProject(2)">各研究所产品开发状态分析</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
          <div class='chart_table level'  ref="productLevel">
          </div>
        </div>

        <div class="item" style="position:relative" v-if="projectFlag == 2">
          <a-dropdown :trigger="['click']" class="dropdown">
            <a class="ant-dropdown-link" @click="e => e.preventDefault()">
              各研究所产品开发状态分析<a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item key="0">
                <a @click="switchToProject(1)">各研究所产品等级分析</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
          <div class='chart_table level' ref="projectStatus">
          </div>
        </div>


        <div class="item" style="position:relative" v-if="totalFlag == 1">
          <a-dropdown :trigger="['click']" class="dropdown prop">
            <a class="ant-dropdown-link" @click="e => e.preventDefault()">
              产品开发状态分析<a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item key="0">
                <a @click="switchTotalFlag(2)">部门产品数量分析</a>
              </a-menu-item>
              <a-menu-item key="1">
                <a @click="switchTotalFlag(3)">产品等级分析</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
          <div class='chart_table product' ref="products"></div>
        </div>

        <div class="item" style="position:relative" v-if="totalFlag == 3">
          <a-dropdown :trigger="['click']" class="dropdown prop">
            <a class="ant-dropdown-link" @click="e => e.preventDefault()">
              产品等级分析<a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item key="0">
                <a @click="switchTotalFlag(1)">产品开发状态分析</a>
              </a-menu-item>
              <a-menu-item key="1">
                <a @click="switchTotalFlag(2)">部门产品数量分析</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
          <div class='chart_table product' ref="productsLevels"></div>
        </div>


        <div class="item" style="position:relative" v-if="totalFlag == 2">
          <a-dropdown :trigger="['click']" class="dropdown prop">
            <a class="ant-dropdown-link" @click="e => e.preventDefault()">
              部门产品数量分析<a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item key="0">
                <a @click="switchTotalFlag(1)">产品开发状态分析</a>
              </a-menu-item>
              <a-menu-item key="1">
                <a @click="switchTotalFlag(3)">产品等级分析</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
          <div class='chart_table product' ref="productsCountByDept"></div>
        </div>


        <div class="item" >
          <div class="head"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 iKfgJk svg-icon-path-icon fill" viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path fill-rule="evenodd" clip-rule="evenodd" d="M19 10C19 12.2091 17.2091 14 15 14C12.7909 14 11 12.2091 11 10C11 7.79086 12.7909 6 15 6C17.2091 6 19 7.79086 19 10ZM15 28C17.2091 28 19 26.2091 19 24C19 21.7909 17.2091 20 15 20C12.7909 20 11 21.7909 11 24C11 26.2091 12.7909 28 15 28ZM15 42C17.2091 42 19 40.2091 19 38C19 35.7909 17.2091 34 15 34C12.7909 34 11 35.7909 11 38C11 40.2091 12.7909 42 15 42Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M37 10C37 12.2091 35.2091 14 33 14C30.7909 14 29 12.2091 29 10C29 7.79086 30.7909 6 33 6C35.2091 6 37 7.79086 37 10ZM33 28C35.2091 28 37 26.2091 37 24C37 21.7909 35.2091 20 33 20C30.7909 20 29 21.7909 29 24C29 26.2091 30.7909 28 33 28ZM33 42C35.2091 42 37 40.2091 37 38C37 35.7909 35.2091 34 33 34C30.7909 34 29 35.7909 29 38C29 40.2091 30.7909 42 33 42Z" fill="#333"></path></g></svg><span>重点关注事项</span></div>
          <div>
            <a-table :pagination="false" style="margin:13px 8px" :data-source="data" :columns="columns" size="small" >
            </a-table>
          </div>
        </div>
      </div>
      <div class="col3">
        <div class="item">
          <div class="head"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 iKfgJk svg-icon-path-icon fill" viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path fill-rule="evenodd" clip-rule="evenodd" d="M19 10C19 12.2091 17.2091 14 15 14C12.7909 14 11 12.2091 11 10C11 7.79086 12.7909 6 15 6C17.2091 6 19 7.79086 19 10ZM15 28C17.2091 28 19 26.2091 19 24C19 21.7909 17.2091 20 15 20C12.7909 20 11 21.7909 11 24C11 26.2091 12.7909 28 15 28ZM15 42C17.2091 42 19 40.2091 19 38C19 35.7909 17.2091 34 15 34C12.7909 34 11 35.7909 11 38C11 40.2091 12.7909 42 15 42Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M37 10C37 12.2091 35.2091 14 33 14C30.7909 14 29 12.2091 29 10C29 7.79086 30.7909 6 33 6C35.2091 6 37 7.79086 37 10ZM33 28C35.2091 28 37 26.2091 37 24C37 21.7909 35.2091 20 33 20C30.7909 20 29 21.7909 29 24C29 26.2091 30.7909 28 33 28ZM33 42C35.2091 42 37 40.2091 37 38C37 35.7909 35.2091 34 33 34C30.7909 34 29 35.7909 29 38C29 40.2091 30.7909 42 33 42Z" fill="#333"></path></g></svg><span>项目进度管理</span></div>
          <div class='chart_table' ref="process"></div>
        </div>
        <div class="item">
          <div class="head"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 iKfgJk svg-icon-path-icon fill" viewBox="0 0 48 48" width="18" height="18"><defs data-reactroot=""></defs><g><path fill-rule="evenodd" clip-rule="evenodd" d="M19 10C19 12.2091 17.2091 14 15 14C12.7909 14 11 12.2091 11 10C11 7.79086 12.7909 6 15 6C17.2091 6 19 7.79086 19 10ZM15 28C17.2091 28 19 26.2091 19 24C19 21.7909 17.2091 20 15 20C12.7909 20 11 21.7909 11 24C11 26.2091 12.7909 28 15 28ZM15 42C17.2091 42 19 40.2091 19 38C19 35.7909 17.2091 34 15 34C12.7909 34 11 35.7909 11 38C11 40.2091 12.7909 42 15 42Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M37 10C37 12.2091 35.2091 14 33 14C30.7909 14 29 12.2091 29 10C29 7.79086 30.7909 6 33 6C35.2091 6 37 7.79086 37 10ZM33 28C35.2091 28 37 26.2091 37 24C37 21.7909 35.2091 20 33 20C30.7909 20 29 21.7909 29 24C29 26.2091 30.7909 28 33 28ZM33 42C35.2091 42 37 40.2091 37 38C37 35.7909 35.2091 34 33 34C30.7909 34 29 35.7909 29 38C29 40.2091 30.7909 42 33 42Z" fill="#333"></path></g></svg><span>产品变更管理</span></div>
          <div>
            <div class='chart_table' ref="alter"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { getChartProductStatus,getChartProjectLevel,getChartProjectLevels,getProjectsCountByDept , getProjectsStatus,getProjectProcess,getProjectAlters} from "@/api/modular/system/chartManage"
  import { mapActions, mapGetters } from 'vuex'
import { forIn } from '@antv/util'
import { ALL_APPS_MENU } from '@/store/mutation-types'
    import Vue from 'vue'
  export default {
    name: 'welcome',
    data() {
      return {
        columns: [
          {
            title: '序号',
            dataIndex: 'seq',
            align:'center',
            width: '15%',
            customRender: (text, record, index) => (<span>{index+1}</span>)
          },
          {
            title: '内容',
            dataIndex: 'content',
          },
        ],
        data:[
          {
            content:'S级项目里程碑提醒'
          },{
            content:'材料供需状态总况'
          },{
            content:'制造、质量、供应链反馈'
          }
        ],

        productStatus:[],
        productLevels:[],
        productCountByDept:[],

        projectLevels:{},
        projectLevelsAxis:[],

        projectStatus:{},
        projectStatusAxis:[],

        projectFlag:1,
        totalFlag:1,


        projectProcess:{},
        projectProcessAxis:[],

        bomAlter:{},
        mIAlter:{},
        mapAlter:{},
      }
    },
    methods: {
      ...mapActions(['MenuChange']),
      switchApp() {
        const applicationData = Vue.ls.get(ALL_APPS_MENU)
        this.MenuChange(applicationData[0]).then((res) => {
        }).catch((err) => {
          this.$message.error('应用切换异常')
        })
      },
      switchToProject(flag){
        this.projectFlag = flag
        if (flag == 1) {
          this.callChartProjectLevel()
          return
        }
        if (flag == 2) {
          this.callChartProjectStatus()
          return
        }
      },
      switchTotalFlag(flag){
        this.totalFlag = flag
        if (flag == 1) {
          this.callChartProductStatus()
          return
        }
        if (flag == 2) {
          this.callChartProductCountByDept()
          return
        }
        if (flag == 3) {
          this.callChartProjectLevels()
          return
        }
      },
      getScale(width = 1920,height = 1080){
        let ww = window.innerWidth / width
        let wh = window.innerHeight /height
        return ww < wh ? ww : wh
      },
      resize(){
        /* let scale = this.getScale()
        this.$refs.dashboard.style.transform = `scale(${scale}) translate(-50%,0)` */
        //let zoom = this.getScale()
        //this.$refs.dashboard.style.zoom = zoom
        //this.initChart()
        
        setTimeout(() => {
          if (this.totalFlag  == 1) {
            this.initProducts()
          }
          if (this.totalFlag  == 2) {
            this.initProductCountBydept()
          }
          if (this.totalFlag  == 3) {
            this.initProductLevels()
          }
          if (this.projectFlag == 1) {
            this.initProductLevel()
          }
          if (this.projectFlag == 2) {
            this.initProjectStatus()
          }
          this.initProcess()
          this.initAlter()
          this.initChart()
        }, 500);

      },
      initChart(){
        this.$nextTick(() => {
          this.initQuality()
          this.initCharge()
          //this.initAlter()
        })
      },
      initCharge() {
        let chart = this.echarts.init(this.$refs.charge)
        chart.clear()
        const options = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          color: ['#4472c4', '#91cc75', '#fac858', '#5b99d3'],
          legend: {
            show: true,
            bottom: '6px',
            itemWidth: 8,
            itemHeight: 8
          },
          grid: {
            left: '3%',
            right: '5%',
            top: '3%',
            bottom:'12%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
          },
          yAxis: {
            type: 'category',
            data: ['方形电池', '动力圆柱', '新型电池', '混动电池']
          },
          series: [{
            name: '项目管理',
            type: 'bar',
            stack: 'total',
            barWidth: 20,
            emphasis: {
              focus: 'series'
            },
            label: {
              show: true,
              color: '#fff'
            },
            data: [200, 300, 400, 500,90]
          },
            {
              name: '硬件费用',
              type: 'bar',
              stack: 'total',
              barWidth: 20,
              emphasis: {
                focus: 'series'
              },
              label: {
                show: true,
                color: '#fff'
              },
              data: [400, 600, 1500, 120]
            },
            {
              name: '检测费用',
              type: 'bar',
              stack: 'total',
              barWidth: 20,
              emphasis: {
                focus: 'series'
              },
              label: {
                show: true,
                color: '#fff'
              },
              data: [600, 90, 60, 80]
            },
            {
              name: '样品费用',
              type: 'bar',
              stack: 'total',
              barWidth: 20,
              emphasis: {
                focus: 'series'
              },
              label: {
                show: true,
                color: '#fff'
              },
              data: [400, 120, 600, 200, 120]
            },

            {
              name: '项目管理',
              type: 'bar',
              stack: 'budget',
              barWidth: 20,
              emphasis: {
                focus: 'series'
              },
              label: {
                show: true,
                color: '#fff'
              },
              data: [200, 300, 400, 500]
            },
            {
              name: '硬件费用',
              type: 'bar',
              stack: 'budget',
              barWidth: 20,
              emphasis: {
                focus: 'series'
              },
              label: {
                show: true,
                color: '#fff'
              },
              data: [400, 600, 800, 120]
            },
            {
              name: '检测费用',
              type: 'bar',
              stack: 'budget',
              barWidth: 20,
              emphasis: {
                focus: 'series'
              },
              label: {
                show: true,
                color: '#fff'
              },
              data: [600, 90, 60, 80,180]
            },
            {
              name: '样品费用',
              type: 'bar',
              stack: 'budget',
              barWidth: 20,
              emphasis: {
                focus: 'series'
              },
              label: {
                show: true,
                color: '#fff'
              },
              data: [800, 120, 300, 400]
            }
          ]
        }
        chart.setOption(options)
        chart.resize()
      },
      initQuality() {
        let that = this
        let chart = this.echarts.init(this.$refs.quality)
        chart.clear()
        const options = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          color: ['#4472c4', '#91cc75', '#fac858', '#5b99d3'],
          legend: [
            {
              orient: 'horizontal',
              x: '0',
              y: '90%',
              itemWidth:5,
              itemHeight:5,
              align: 'left',
              data: ['一次送样合格率'],
            },
            {
              orient: 'horizontal',
              x: '28%',
              y: '90%',
              itemWidth:5,
              itemHeight:5,
              align: 'left',
              data: ['问题关闭率'],
            },
            {
              orient: 'horizontal',
              x: '50%',
              y: '90%',
              itemWidth:5,
              itemHeight:5,
              align: 'left',
              data: ['风险关闭率'],
            },
            {
              orient: 'horizontal',
              x: '72%',
              y: '90%',
              itemWidth:5,
              itemHeight:5,
              align: 'left',
              data: ['文件质量达成率'],
            },
          ],
          grid: {
            left: '3%',
            right: '6%',
            top: '3%',
            bottom:'12%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
          },
          yAxis: {
            type: 'category',
            /* axisLabel: {
              interval:'auto',
              rotate:60
            }, */
            data: ['方形电池', '动力圆柱', '新型电池', '混动电池']
          },
          series: [{
            name: '一次送样合格率',
            type: 'bar',
            stack: 'total',
            barWidth: 30,
            emphasis: {
              focus: 'series'
            },
            label: {
              show: true,
              color: '#fff'
            },
            data: [200, 300, 400, 500]
          },
            {
              name: '问题关闭率',
              type: 'bar',
              stack: 'total',
              barWidth: 30,
              emphasis: {
                focus: 'series'
              },
              label: {
                show: true,
                color: '#fff'
              },
              data: [400, 600, 1500, 120]
            },
            {
              name: '风险关闭率',
              type: 'bar',
              stack: 'total',
              barWidth: 30,
              emphasis: {
                focus: 'series'
              },
              label: {
                show: true,
                color: '#fff'
              },
              data: [600, 90, 60, 80]
            },
            {
              name: '文件质量达成率',
              type: 'bar',
              stack: 'total',
              barWidth: 30,
              emphasis: {
                focus: 'series'
              },
              label: {
                show: true,
                color: '#fff'
              },
              data: [800, 120, 600, 400, 120]
            }
          ]
        }
        chart.setOption(options)

        chart.on('click', function(params) {
          //that.switchApp()


          if(params.seriesName == '问题关闭率'){

            that.$router.push({
              path:'/project_problem_level2',
            })
          }
          if(params.seriesName == '风险关闭率'){

            that.$router.push({
              path:'/project_risk_level2',
            })
          }

          return
        });

        chart.resize()
      },
      initProcess() {
        let that = this
        let chart = this.echarts.init(this.$refs.process)
        chart.off('click')
        chart.clear()
        const options = {
          color:['#91cc75','#fac858','#cacaca'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {
            bottom: '6px',
            itemWidth: 8,
            itemHeight: 8,
            show:true,
          },
          grid: {
            left: '3%',
            right: '3%',
            bottom: '15%',
            top:'10%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: this.projectProcessAxis

          },
          yAxis: {
            type: 'value'
          },
          series: [{
            name: '正常',
            type: 'bar',
            stack: 'total',
            barWidth: 40,
            label: {
              show: true,
              color: '#fff'
            },
            emphasis: {
              focus: 'series'
            },
            data: this.projectProcess['1']
          },
            {
              name: '延期',
              type: 'bar',
              stack: 'total',
              barWidth: 40,
              label: {
                show: true,
                color: '#fff'
              },
              emphasis: {
                focus: 'series'
              },
              data: this.projectProcess['2']
            },
            {
              name: '暂停',
              type: 'bar',
              stack: 'total',
              barWidth: 40,
              label: {
                show: true,
                color: '#fff'
              },
              emphasis: {
                focus: 'series'
              },
              data: this.projectProcess['3']
            },
          ]
        }
        chart.setOption(options)
        chart.on('click', function(params) {
          //that.switchApp()
          that.$router.push({
            path:'/project_process_level2',
            query: {
              dept:params.name
            },
          })
          return
        });
        chart.resize()
      },

      initAlter() {
        let chart = this.echarts.init(this.$refs.alter)
        chart.clear()
        let axis = []
        let dataBom = []
        let dataMi = []
        let dataMap = []
        for (const key in this.bomAlter) {
          axis.push(key.substring(0,4))
          dataBom.push(this.bomAlter[key])
          dataMi.push(this.mIAlter[key])
          dataMap.push(this.mapAlter[key])
        }

        const options = {
          color:['#5a86e8','#91cc75','#fac858'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {
            bottom: '6px',
            itemWidth: 8,
            itemHeight: 8,
            show:true
          },
          grid: {
            left: '3%',
            right: '3%',
            bottom: '15%',
            top:'10%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: axis

          },
          yAxis: {
            type: 'value'
          },
          series: [{
            name: 'BOM',
            type: 'bar',
            stack: 'total',
            barWidth: 40,
            label: {
              show: true,
              color: '#fff'
            },
            emphasis: {
              focus: 'series'
            },
            data: dataBom
          },
            {
              name: 'MI',
              type: 'bar',
              stack: 'total',
              barWidth: 40,
              label: {
                show: true,
                color: '#fff'
              },
              emphasis: {
                focus: 'series'
              },
              data: dataMi
            },
            {
              name: '图纸',
              type: 'bar',
              stack: 'total',
              barWidth: 40,
              label: {
                show: true,
                color: '#fff'
              },
              emphasis: {
                focus: 'series'
              },
              data: dataMap
            },
          ]
        }
        let that = this
        chart.on('click', function(params) {
          //that.switchApp()
          that.$router.push({
            path:'/project_alter_level2',
            query: {
              dept:params.name
            },
          })
          return
        });
        chart.setOption(options)
        chart.resize()
      },

      initProductLevel() {
        let chart = this.echarts.init(this.$refs.productLevel)
        chart.clear()
        const options = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          color: ['#4472c4', '#91cc75', '#fac858', '#5b99d3'],
          legend: {
            orient: 'vertical',
            right: 10,
            top: '50px',
            itemWidth: 8,
            itemHeight: 8,
            show:true
          },
          grid: {
            left: '3%',
            right: '8%',
            bottom: '4%',
            top:'20%',
            containLabel: true
          },
          xAxis: {
            type: 'value'
          },
          yAxis: {
            type: 'category',
            data: this.projectLevelsAxis //['混动电池研究所', '新型电池研究所', '动力圆柱电池研究所', '方形电池研究所']
          },
          series: [{
            name: 'S',
            type: 'bar',
            stack: 'total',
            barWidth: 18,
            label: {
              show: true,
              color: '#fff'
            },
            emphasis: {
              focus: 'series'
            },
            data: this.projectLevels['S']
          },
            {
              name: 'A',
              type: 'bar',
              stack: 'total',
              barWidth: 18,
              label: {
                show: true,
                color: '#fff'
              },
              emphasis: {
                focus: 'series'
              },
              data: this.projectLevels['A']
            },
            {
              name: 'B',
              type: 'bar',
              stack: 'total',
              barWidth: 18,
              label: {
                show: true,
                color: '#fff'
              },
              emphasis: {
                focus: 'series'
              },
              data: this.projectLevels['B']
            },
            {
              name: 'C',
              type: 'bar',
              stack: 'total',
              barWidth: 18,
              label: {
                show: true,
                color: '#fff'
              },
              emphasis: {
                focus: 'series'
              },
              data: this.projectLevels['C']
            }
          ]
        }
        chart.setOption(options)
        chart.resize()
      },

      initProjectStatus() {
        let chart = this.echarts.init(this.$refs.projectStatus)
        chart.clear()
        const options = {
          color: ['#4472c4', '#fac858', '#91cc75', '#5b99d3','#cacaca'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {
            orient: 'vertical',
            right: 10,
            top: '50px',
            itemWidth: 8,
            itemHeight: 8,
            show:false
          },
          grid: {
            left: '3%',
            right: '3%',
            bottom: '4%',
            top:'20%',
            containLabel: true
          },
          xAxis: {
            type: 'value'
          },
          yAxis: {
            type: 'category',
            data: this.projectStatusAxis //['混动电池研究所', '新型电池研究所', '动力圆柱电池研究所', '方形电池研究所']
          },
          series: [{
            name: '立项讨论',
            type: 'bar',
            stack: 'total',
            barWidth: 18,
            label: {
              show: true,
              color: '#fff'
            },
            emphasis: {
              focus: 'series'
            },
            data: this.projectStatus['立项讨论']
          },
            {
              name: 'A/B样',
              type: 'bar',
              stack: 'total',
              barWidth: 18,
              label: {
                show: true,
                color: '#fff'
              },
              emphasis: {
                focus: 'series'
              },
              data: this.projectStatus['A/B样']
            },
            {
              name: 'C/D样',
              type: 'bar',
              stack: 'total',
              barWidth: 18,
              label: {
                show: true,
                color: '#fff'
              },
              emphasis: {
                focus: 'series'
              },
              data: this.projectStatus['C/D样']
            },
            {
              name: '停产',
              type: 'bar',
              stack: 'total',
              barWidth: 18,
              label: {
                show: true,
                color: '#fff'
              },
              emphasis: {
                focus: 'series'
              },
              data: this.projectStatus['停产']
            },
            {
              name: '开发暂停',
              type: 'bar',
              stack: 'total',
              barWidth: 18,
              label: {
                show: true,
                color: '#fff'
              },
              emphasis: {
                focus: 'series'
              },
              data: this.projectStatus['开发暂停']
            }
          ]
        }
        chart.setOption(options)
        chart.resize()
      },
      initProducts() {
        let chart = this.echarts.init(this.$refs.products)
        chart.off("click")
        let datas = this.productStatus
        let sum = datas.reduce((sum, e) => sum + Number(e.value || 0), 0)
        chart.clear()
        const options = {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            show: false
          },
          color: ['#4472c4', '#fac858', '#91cc75', '#5b99d3','#cacaca'],
          grid: {
            left: '3%',
            right: '4%',
            top: '40%',
            bottom:'4%',
          },
          series: [{
            type: 'pie',
            radius: ['55%', '80%'],
            itemStyle: {
              borderRadius: 0,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true,
              position: 'center', //将百分比显示在饼图内部
              color:'#5c1b68',
              formatter: `产品总数\n${sum}`,
              fontSize: '20',
              lineHeight: 32,
              fontWeight:'bold'
            },
            labelLine: {
              show: false
            },
            data: datas
          },{
            type: 'pie',
            radius: ['55%', '80%'],
            itemStyle: {
              borderRadius: 0,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true,
            },
            labelLine: {
              show: true
            },
            data: datas
          },
            {
              type: 'pie',
              radius: ['55%', '80%'],
              itemStyle: {
                borderRadius: 0,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: true,
                position: 'inner', //将百分比显示在饼图内部
                color: '#fff',
                formatter: '{c}',
              },
              labelLine: {
                show: false
              },
              data: datas
            },

          ]
        }
        chart.setOption(options)
        chart.on('click', function(params) {
          console.log(params)
          if (params.seriesIndex == 0) {
            window.open('/report_sum', "_blank");
            return
          }
          let flag = 0
          if (params.name == '立项讨论') {

            flag = 0
          }

          if (params.name == '停产') {
            flag = 3
          }

          if (params.name == 'C/D样') {
            flag =2
          }

          if (params.name == 'A/B样') {
            flag = 1
          }

          if (params.name == '开发暂停') {
            flag = 4
          }
          window.open(`/report_detail?flag=${flag}&cateName=${params.name}`, "_blank");
        });
        chart.resize()
      },
      initProductLevels(){
        let chart = this.echarts.init(this.$refs.productsLevels)
        chart.off("click")
        let datas = this.productsLevels
        let sum = datas.reduce((sum, e) => sum + Number(e.value || 0), 0)
        chart.clear()
        const options = {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            show: false
          },
          grid: {
            left: '3%',
            right: '4%',
            top: '40%',
            bottom:'4%',
          },
          series: [{
            type: 'pie',
            radius: ['55%', '80%'],
            itemStyle: {
              borderRadius: 0,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true,
              position: 'center', //将百分比显示在饼图内部
              color:'#5c1b68',
              formatter: `产品总数\n${sum}`,
              fontSize: '20',
              lineHeight: 32,
              fontWeight:'bold'
            },
            labelLine: {
              show: false
            },
            data: datas
          },{
            type: 'pie',
            radius: ['55%', '80%'],
            itemStyle: {
              borderRadius: 0,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true,
            },
            labelLine: {
              show: true
            },
            data: datas
          },
            {
              type: 'pie',
              radius: ['55%', '80%'],
              itemStyle: {
                borderRadius: 0,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: true,
                position: 'inner', //将百分比显示在饼图内部
                color: '#fff',
                formatter: '{c}',
              },
              labelLine: {
                show: false
              },
              data: datas
            },

          ]
        }
        chart.setOption(options)
        chart.on('click', function(params) {
          if (params.seriesIndex == 0) {
            window.open('/report_sum', "_blank");
            return
          }
        });
        chart.resize()
      },
      initProductCountBydept(){

        let chart = this.echarts.init(this.$refs.productsCountByDept)
        chart.off("click")
        let datas = this.productCountByDept
        let sum = datas.reduce((sum, e) => sum + Number(e.value || 0), 0)
        chart.clear()
        const options = {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            show: false
          },
          grid: {
            left: '3%',
            right: '4%',
            top: '40%',
            bottom:'4%',
          },
          series: [{
            type: 'pie',
            radius: ['55%', '80%'],
            itemStyle: {
              borderRadius: 0,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true,
              position: 'center', //将百分比显示在饼图内部
              color:'#5c1b68',
              formatter: `产品总数\n${sum}`,
              fontSize: '20',
              lineHeight: 32,
              fontWeight:'bold'
            },
            labelLine: {
              show: false
            },
            data: datas
          },{
            type: 'pie',
            radius: ['55%', '80%'],
            itemStyle: {
              borderRadius: 0,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true,
            },
            labelLine: {
              show: true
            },
            data: datas
          },
            {
              type: 'pie',
              radius: ['55%', '80%'],
              itemStyle: {
                borderRadius: 0,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: true,
                position: 'inner', //将百分比显示在饼图内部
                color: '#fff',
                formatter: '{c}',
              },
              labelLine: {
                show: false
              },
              data: datas
            },

          ]
        }
        chart.setOption(options)
        chart.on('click', function(params) {
          if (params.seriesIndex == 0) {
            window.open('/report_sum', "_blank");
            return
          }
        });
        chart.resize()
      },
      callChartProductStatus() {
        getChartProductStatus({}).then((res) => {
          if (res.result) {
            this.productStatus = res.data.list ? res.data.list : []
            this.$nextTick(()=>{
              this.initProducts()
            })
          } else {
            this.$message.error('错误提示：' + res.message,1)
          }
        })
          .catch((err) => {
            this.$message.error('错误提示：' + err.message,1)
          });
      },
      callChartProjectLevel() {
        getChartProjectLevel({}).then((res) => {
          if (res.result) {
            let level = res.data.list ? res.data.list : {}
            for (const i in level) {
              for (let $i = 0,j = level[i].length; $i < j; $i++) {
                level[i][$i] = level[i][$i] == '0' ? null : parseInt(level[i][$i])
              }
            }
            this.projectLevels = level
            this.projectLevelsAxis = res.data.axis ? res.data.axis :[]
            this.$nextTick(()=>{
              this.initProductLevel()
            })
          } else {
            this.$message.error('错误提示：' + res.message,1)
          }
        })
          .catch((err) => {
            this.$message.error('错误提示：' + err.message,1)
          });
      },
      callChartProjectStatus() {
        getProjectsStatus({}).then((res) => {
          if (res.result) {
            let level = res.data.list ? res.data.list : {}
            for (const i in level) {
              for (let $i = 0,j = level[i].length; $i < j; $i++) {
                level[i][$i] = level[i][$i] == '0' ? null : parseInt(level[i][$i])
              }
            }
            this.projectStatus = level
            this.projectStatusAxis = res.data.axis ? res.data.axis :[]
            this.$nextTick(()=>{
              this.initProjectStatus()
            })
          } else {
            this.$message.error('错误提示：' + res.message,1)
          }
        })
          .catch((err) => {
            this.$message.error('错误提示：' + err.message,1)
          });
      },
      callChartProjectLevels() {
        getChartProjectLevels({}).then((res) => {
          if (res.result) {
            this.productsLevels = res.data.list ? res.data.list : []
            this.$nextTick(()=>{
              this.initProductLevels()
            })
          } else {
            this.$message.error('错误提示：' + res.message,1)
          }
        })
          .catch((err) => {
            this.$message.error('错误提示：' + err.message,1)
          });
      },
      callChartProductCountByDept() {
        getProjectsCountByDept({}).then((res) => {
          if (res.result) {
            this.productCountByDept = res.data.list ? res.data.list : []
            this.$nextTick(()=>{
              this.initProductCountBydept()
            })
          } else {
            this.$message.error('错误提示：' + res.message,1)
          }
        })
          .catch((err) => {
            this.$message.error('错误提示：' + err.message,1)
          });
      },

      callChartProjectProcess() {
        getProjectProcess({}).then((res) => {
          if (res.result) {
            let level = res.data.list ? res.data.list : {}
            for (const i in level) {
              for (let $i = 0,j = level[i].length; $i < j; $i++) {
                level[i][$i] = level[i][$i] == '0' ? null : parseInt(level[i][$i])
              }
            }
            this.projectProcess = level
            this.projectProcessAxis = res.data.axis ? res.data.axis :[]
            this.$nextTick(()=>{
              this.initProcess()
            })
          } else {
            this.$message.error('错误提示：' + res.message,1)
          }
        })
          .catch((err) => {
            this.$message.error('错误提示：' + err.message,1)
          });
      },

      callChartProjectAlter() {
        getProjectAlters({}).then((res) => {
          if (res.result) {
            this.bomAlter = res.data.BOMCOUNT ? res.data.BOMCOUNT : {}
            this.mIAlter = res.data.MICOUNT ? res.data.MICOUNT : {}
            this.mapAlter = res.data.MAPCOUNT ? res.data.MAPCOUNT :{}
            this.$nextTick(()=>{
              this.initAlter()
            })
          } else {
            this.$message.error('错误提示：' + res.message,1)
          }
        })
          .catch((err) => {
            this.$message.error('错误提示：' + err.message,1)
          });
      },
    },
    computed: {
      ...mapGetters(['userInfo'])
    },
    mounted() {
      /* let scale = this.getScale()
      this.$refs.dashboard.style.transform = `scale(${scale}) translate(-50%,0)`
      this.$refs.dashboard.style.width = '1920px'
      this.$refs.dashboard.style.height = '1080px' */
      this.$refs.dashboard.style.zoom = 1
      this.$refs.dashboard.style.width = '100%'
      this.switchTotalFlag(1)
      this.switchToProject(1)
      this.callChartProjectProcess()
      this.callChartProjectAlter()
      this.initChart()
      window.addEventListener('resize',this.resize)
    },
    destroyed(){
      window.removeEventListener('resize',this.resize)
    }
  }
</script>

<style lang="css" scoped=''>
  .dashboard {
    display: flex;
    margin: auto;
    justify-content: center;
    /* position: fixed;
    top: 42px;
    left: 50%;
    transform-origin: left top; */
  }
  .col1,
  .col3 {
    width: 28%;
    max-width: 380px;
  }
  .col2 {
    width: 44%;
    max-width: 750px;
  }
  .item {
    width: 96%;
    margin: 8px auto;
    border-radius: 8px;
    overflow: hidden;
    background: #fff;
    box-shadow: 2px 2px 3px #ccc;
  }
  .col1 .item,
  .col3 .item {
    width: 100%;
  }
  .head {
    color: #000;
    font-size: 18px;
    border-bottom: 2px solid #e5e5e5;
    padding: 10px 18px;
    display: flex;
    align-items: center;
  }
  .head svg{
    margin-right:2px ;
  }
  .chart_table {
    width: 100%;
    min-height: 280px;
  }
  .chart_table.level{
    height: 180px;
    min-height:initial;
  }
  .chart_table.product{
    height: 253px;
    min-height:initial;
  }
  .dropdown{
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 1000;
    color: #000000aa;
  }
  .dropdown.prop{
    top: 2px;
    left: 8px;
  }
</style>


