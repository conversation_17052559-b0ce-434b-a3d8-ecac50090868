<template>
  <div class="wrapper">
    <div class="flex-sb-center-row">
      <div class="head_title">循环建模{{
          cycleReportParam.reportName ? "《" + cycleReportParam.reportName + "》" : ""
        }}
      </div>
      <div class="btn-wrap mt5">
        <a-button class="mr10" @click="handleOpen('isShowHistorical')">历史记录</a-button>
        <a-button class="mr10" @click="handleOpen('isShowDrafts')">草稿箱</a-button>
        <div style="position: relative;">
          <div class="normal-btn" :class="{ 'streamer-btn anima': _handleVerify(cycleReportParam)[0] }"
               @click="handleAllOk">
            完成模型搭建
          </div>
        </div>
      </div>
    </div>

    <div class="all-wrapper">
      <div class="left-content block">
        <div class="flex-sb-center-row">
          <h3>一、测试数据选择</h3>
          <div style="float: right">
            <a-popconfirm placement="topRight" title="确认清空？" @confirm="() => {
            this.cycleReportParam.orderDataList = []
            this.selectedRowKeys = []
            this.selectedRows = []
            this.$set(this.cycleReportParam, 'temp', '')
            this.primaryGroupChange()
            // 实时保存
            this.cycleTestReportTask(this.cycleReportParam, this.testReportID)
            }">
              <a-button class="mr5">清空</a-button>
            </a-popconfirm>
            <a-popconfirm placement="topRight" title="确认删除？" @confirm="deleteSelect">
              <a-button>删除</a-button>
            </a-popconfirm>
          </div>
        </div>
        <a-table class="mt10"
                 bordered
                 :columns="orderColumns"
                 :data-source="cycleReportParam.orderDataList"
                 :row-selection="deleteRowSelection"
                 childrenColumnName="child"
                 :rowKey="record => record.uuid"
                 :pagination="false"
        >
          <template slot="celltestcode" slot-scope="text, record, index, columns">
            <a @click="openStepData(record)" v-if="record.flowId != null" style="text-align: center">{{ text }}</a>
            <span v-else style="text-align: center">{{ text }}</span>
          </template>
          <template slot="action" slot-scope="text, record, index, columns">
            <a-tooltip placement="top" title="删除" arrow-point-at-center>
              <a @click="deleteDataOne(record, index)" style="text-align: center">
                <a-icon type="delete" style="font-size: large;margin-right: 3px"/>
              </a>
            </a-tooltip>
            <a-tooltip placement="top" title="上移" arrow-point-at-center v-if="index != 0">
              <a @click="moveUp(cycleReportParam.orderDataList, index)" style="text-align: center">
                <a-icon type="arrow-up" style="font-size: large;margin-right: 3px"/>
              </a>
            </a-tooltip>
            <a-tooltip placement="top" title="下移" arrow-point-at-center
                       v-if="index != cycleReportParam.orderDataList.length - 1">
              <a @click="moveDown(cycleReportParam.orderDataList, index)" style="text-align: center">
                <a-icon type="arrow-down" style="font-size: large"/>
              </a>
            </a-tooltip>
          </template>
          <template slot="dataPath" slot-scope="text, record, index, columns">
            <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
              <template slot="title">
                {{ text ? text : record.testcontent }}
              </template>
              {{ text ? text : record.testcontent }}
            </a-tooltip>
          </template>
          <template slot="footer">
            <div class="footer-btn" :class="{ 'plus-btn': cycleReportParam.orderDataList.length === 0 }"
                 @click="handleOpen('visible')">
              <span></span>
              <span></span>
              <span></span>
              <span></span>
              <a-icon type="plus"></a-icon>
            </div>
          </template>
        </a-table>
      </div>

      <div class="right-content ml10">
        <div class="block">
          <div class="flex-sb-center-row">
            <h3>二、基本信息填写</h3>
            <a-icon @click="handleClose('isTempClose')" :type="isTempClose ? 'down' : 'up'"/>
          </div>
          <div v-show="!isTempClose" class="info-row-div">
            <div class="label-span">项目名称 :</div>
            <a-tooltip :title="cycleReportParam.projectName">
              <a-input style="width: 100px; margin-top: 10px;" v-model="cycleReportParam.projectName" @blur="handleBlur"/>
            </a-tooltip>
            <div class="label-span">样品阶段 :</div>
            <a-input style="width: 100px; margin-top: 10px;" v-model="cycleReportParam.phase" @blur="handleBlur"/>
            <div class="label-span">温度 :</div>
            <a-input class="number-input" suffix="℃" v-model="cycleReportParam.temp"
                     @keyup="cycleReportParam.temp = (cycleReportParam.temp + '').replace(/[^0-9.]/g, '')"
                     @blur="handleNumberBlur('temp')"/>
            <div class="label-span">循环机制 :</div>
            <a-tooltip title="示例：可填写0.33C或者3N3F_10min">
              <a-input class="number-input" placeholder="0.33C/3N3F_10min" v-model="cycleReportParam.rate" @blur="handleBlur"/>
            </a-tooltip>
            <div class="info-row-div">
              <div class="label-span">起止SOC :</div>
              <a-input class="number-input" suffix="%" v-model="cycleReportParam.startSoc"
                       @keyup="cycleReportParam.startSoc = (cycleReportParam.startSoc + '').replace(/[^0-9.]/g, '')"
                       @blur="handleNumberBlur('startSoc')"/>
              <span style="margin-top: 10px; margin-left: 4px; margin-right: 4px; display: flex; align-items: center;"> ~ </span>
              <a-input class="number-input" suffix="%" v-model="cycleReportParam.endSoc"
                       @keyup="cycleReportParam.endSoc = (cycleReportParam.endSoc + '').replaceAll(/[^0-9.]/g, '')"
                       @blur="handleNumberBlur('endSoc')"/>
            </div>
          </div>
        </div>

        <div class="block mt10">
          <h3>三、模型生成逻辑</h3>

          <div class="flex-sb-center-row">
            <strong class="mt10">1、Cycle-参数填写</strong>
            <a-icon @click="handleClose('isCycleParamClose')" :type="isCycleParamClose ? 'down' : 'up'"/>
          </div>
          <div v-show="!isCycleParamClose" style="margin-top: 10px; width: 100%;">
            <a-popconfirm placement="topRight" title="确认清空？" @confirm="() => {
              this.cycleReportParam.cycleStepParamList = [ {} ]
              this.cycleListDeleteSelectedRowKeys = []
              // 实时保存
              this.cycleTestReportTask(this.cycleReportParam, this.testReportID)
              }">
              <a-button size="small" class="mr5">清空</a-button>
            </a-popconfirm>
            <a-popconfirm placement="topRight" title="确认删除？" @confirm="deleteSelectedParams('cycle')">
              <a-button size="small">删除</a-button>
            </a-popconfirm>
          </div>
          <a-table v-show="!isCycleParamClose" class="mt5"
                   bordered
                   :columns="cycleStepColumns"
                   :data-source="cycleReportParam.cycleStepParamList"
                   :row-selection="{columnWidth: 20, selectedRowKeys: this.cycleListDeleteSelectedRowKeys, onChange: cycleDeleteRowOnChange}"
                   :pagination="false">
            <template slot="action" slot-scope="text, record, index, columns">
              <a v-if="index > 0" @click="deleteParam('cycleStepParamList', index)" style="text-align: center">删除</a>
              <span v-else>删除</span>
            </template>
            <template slot="groupIndexTitle">
              电芯组别
              <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
                <a-icon type="question-circle" style="color: #1890ff;"/>
                <template slot="title">
								    <span>
								    	1、当一个电芯只有一条数据时，电芯组别可不填写；<br/>
                      2、当一个电芯有多条数据时，电芯组别填写对应编号，并填写对应工步号；<br/>
                      3、当一个电芯有多条数据时，电芯组别不填写，即默认所有组别选择相同工步号；
								    </span>
                </template>
              </a-tooltip>
            </template>
            <template slot="groupIndex" slot-scope="text, record, index, columns">
              <a-select style="width: 100%;" allow-clear v-model="cycleReportParam.cycleStepParamList[index].groupIndex"
                        :options="primaryGroupOptions" @change="cycleTestReportTask(cycleReportParam, testReportID)"/>
            </template>
            <template slot="ceStepTitle">
              放电容量&能量工步号
              <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
                <a-icon type="question-circle" style="color: #1890ff;"/>
                <template slot="title">
                    <span>
                      请输入放电容量&能量工步号；<br/>
                      如是多个工步号，需以逗号,隔开，计算加和
                    </span>
                </template>
              </a-tooltip>
            </template>
            <template slot="ceStep" slot-scope="text, record, index, columns">
              <a-input class="input"
                       v-model="cycleReportParam.cycleStepParamList[index].ceStep"
                       @keyup="cycleReportParam.cycleStepParamList[index].ceStep = (cycleReportParam.cycleStepParamList[index].ceStep + '').replaceAll(/[\t\n\s]/g, ',').replaceAll(/[^0-9,，]/g, '').replaceAll(/[，]/g, ',')"
                       @blur="handleBlur('cycleStepParamList', index, 'ceStep')"/>
            </template>
            <template slot="chCeStepTitle">
              充电容量&能量工步号
              <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
                <a-icon type="question-circle" style="color: #1890ff;"/>
                <template slot="title">
                    <span>
                      充电容量&能量工步号非必填；<br/>
                      如是多个工步号，需以逗号,隔开，计算加和
                    </span>
                </template>
              </a-tooltip>
            </template>
            <template slot="chCeStep" slot-scope="text, record, index, columns">
              <a-input class="input"
                       v-model="cycleReportParam.cycleStepParamList[index].chCeStep"
                       @keyup="cycleReportParam.cycleStepParamList[index].chCeStep = (cycleReportParam.cycleStepParamList[index].chCeStep + '').replaceAll(/[\t\n\s]/g, ',').replaceAll(/[^0-9,，]/g, '').replaceAll(/[，]/g, ',')"
                       @blur="handleBlur('cycleStepParamList', index, 'chCeStep')"/>
            </template>
            <template slot="footer">
              <div class="footer-btn" @click="addParam('cycleStepParamList')">
                <span></span>
                <span></span>
                <span></span>
                <span></span>
                <a-icon type="plus"></a-icon>
              </div>
            </template>
          </a-table>

          <div class="flex-sb-center-row">
            <strong class="mt10">2、RPT(中检)-参数填写</strong>
            <a-icon @click="handleClose('isRptParamClose')" :type="isRptParamClose ? 'down' : 'up'"/>
          </div>
          <div v-show="!isRptParamClose">
            <div class="mt10">①X轴：循环圈数填写</div>
            <div style="width: 188px;">
              <div class="mt10 footer-btn" :class="{ 'plus-btn': cycleReportParam.rptCycleList.length === 0 && (cycleReportParam.cycleStepParamList.length > 1 || cycleReportParam.cycleStepParamList[0].ceStep || cycleReportParam.cycleStepParamList[0].chCeStep || cycleReportParam.cycleStepParamList[0].groupIndex) }"
                   @click="handleOpen('isShowCompute')">
                <span></span>
                <span></span>
                <span></span>
                <span></span>
                <span style="font-size: 14px; color: rgba(0, 0, 0, 0.65); font-weight: 400;">循环规则输入</span>
              </div>
            </div>

            <div class="mt10">②Y轴：工步信息填写</div>
            <div style="margin-top: 10px; width: 100%;">
              <a-popconfirm placement="topRight" title="确认清空？" @confirm="() => {
              this.cycleReportParam.rptStepParamList = [ { dcirStepParamList:[] } ]
              this.rptListDeleteSelectedRowKeys = []
              this.cycleReportParam.dcirTiTleList = ['']
              this.dcirNum = 1
              this.changeRptParamColumns(false)
              }">
                <a-button size="small" class="mr5">清空</a-button>
              </a-popconfirm>
              <a-popconfirm placement="topRight" title="确认删除？" @confirm="deleteSelectedParams('rpt')">
                <a-button size="small">删除</a-button>
              </a-popconfirm>
            </div>
            <a-table class="mt5"
                     bordered
                     :columns="rptParamColumns"
                     :data-source="cycleReportParam.rptStepParamList"
                     :row-selection="{columnWidth: 20, selectedRowKeys: this.rptListDeleteSelectedRowKeys, onChange: rptDeleteRowOnChange}"
                     :pagination="false">
              <template slot="action" slot-scope="text, record, index, columns">
                <a v-if="index > 0" @click="deleteParam('rptStepParamList', index)" style="text-align: center">删除</a>
                <span v-else>删除</span>
              </template>
              <template slot="groupIndexTitle">
                电芯组别
                <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
                  <a-icon type="question-circle" style="color: #1890ff;"/>
                  <template slot="title">
								    <span>
								    	1、当一个电芯只有一条数据时，电芯组别可不填写；<br/>
                      2、当一个电芯有多条数据时，电芯组别填写对应编号，并填写对应工步号；<br/>
                      3、当一个电芯有多条数据时，电芯组别不填写，即默认所有组别选择相同工步号；
								    </span>
                  </template>
                </a-tooltip>
              </template>
              <template slot="groupIndex" slot-scope="text, record, index, columns">
                <a-select style="width: 100%;" allow-clear v-model="cycleReportParam.rptStepParamList[index].groupIndex"
                          :options="primaryGroupOptions" @change="cycleTestReportTask(cycleReportParam, testReportID)"/>
              </template>
              <template slot="ceStepTitle">
                容量&能量工步号
                <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
                  <a-icon type="question-circle" style="color: #1890ff;"/>
                  <template slot="title">
                    <span>
                      请输入容量&能量工步号；<br/>
                      如是多个工步号，需以逗号,隔开，计算平均值
                    </span>
                  </template>
                </a-tooltip>
              </template>
              <template slot="ceStep" slot-scope="text, record, index, columns">
                <a-input class="input"
                         v-model="cycleReportParam.rptStepParamList[index].ceStep"
                         @keyup="cycleReportParam.rptStepParamList[index].ceStep = (cycleReportParam.rptStepParamList[index].ceStep + '').replaceAll(/[\t\n\s]/g, ',').replaceAll(/[^0-9,，]/g, '').replaceAll(/[，]/g, ',')"
                         @blur="handleBlur('rptStepParamList', index, 'ceStep')"/>
              </template>
              <template v-for="(item, i) in dcirTitleIndexArr" :slot="`dcirTitle_${i}`">
                <div class="flex-sb-center-row">
                  <a-input class="input" v-model="cycleReportParam.dcirTiTleList[i]" placeholder="示例：XX SOC XX s" @blur="handleBlur"/>
                  <a-tooltip v-if="i === cycleReportParam.dcirTiTleList.length - 1 && i < 3">
                    <template slot="title">增加DCIR参数组</template>
                    <a-icon class="ml10" type="plus" style="color: #1890ff;" @click="changeDcirParamList(false)"/>
                  </a-tooltip>
                  <a-tooltip v-if="i === cycleReportParam.dcirTiTleList.length - 1 && i > 0">
                    <template slot="title">删除DCIR参数组</template>
                    <a-icon class="ml10" type="minus" style="color: #1890ff;" @click="changeDcirParamList(true)"/>
                  </a-tooltip>
                </div>
              </template>
              <template slot="dcirStepParam" slot-scope="text, record, index, columns">
                <a-input class="input"
                         v-model="record.dcirStepParamList[columns.dataIndex.replace('dcirStepParamList[','').charAt(0)][columns.dataIndex.replace('dcirStepParamList[','').substring(3)]"
                         @keyup="record.dcirStepParamList[columns.dataIndex.replace('dcirStepParamList[','').charAt(0)][columns.dataIndex.replace('dcirStepParamList[','').substring(3)] = (record.dcirStepParamList[columns.dataIndex.replace('dcirStepParamList[','').charAt(0)][columns.dataIndex.replace('dcirStepParamList[','').substring(3)] + '').replaceAll(columns.dataIndex.includes('dchStepTime') ? /[^0-9:.]/g : /[^0-9]/g, '')"
                         @blur="handleBlur('rptStepParamList', index, null, record)"/>
              </template>
              <template slot="dchStepTimeTitle">
                放电工步时间
                <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
                  <a-icon type="question-circle" style="color: #1890ff;"/>
                  <template slot="title">
                  <span>
                    未填写放电工步时间默认取工步数据表数据；<br/>
                    填写了放电工步时间则取详细数据表数据；<br/>
                    格式：HH:mm:ss.SSS；例如：0:00:10.000
                  </span>
                  </template>
                </a-tooltip>
              </template>
              <template slot="footer">
                <div class="footer-btn" @click="addParam('rptStepParamList')">
                  <span></span>
                  <span></span>
                  <span></span>
                  <span></span>
                  <a-icon type="plus"></a-icon>
                </div>
              </template>
            </a-table>
          </div>

          <div class="flex-sb-center-row">
            <strong class="mt10">
              3、ETP(累计能量)-参数填写
              <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center>
                <a-icon type="question-circle" style="color: #1890ff;"/>
                <template slot="title">
                    <span>
                      若需要以ETP为X轴生成图表，请填写ETP参数
                    </span>
                </template>
              </a-tooltip>
            </strong>
            <a-icon @click="handleClose('isEtpParamClose')" :type="isEtpParamClose ? 'down' : 'up'"/>
          </div>
          <div v-show="!isEtpParamClose">
            <div class="mt10">
              <span class="mr10">ETP计算规则 : </span>
              <a-radio-group name="radioGroup" buttonStyle="solid" :value="cycleReportParam.etpType" @change="etpTypeChange">
                <a-radio value="dch">累计放电能量</a-radio>
                <a-radio value="chAndDch">累计充放电能量</a-radio>
              </a-radio-group>
              <a-popconfirm placement="topRight" title="确认清除所有ETP参数吗？" @confirm="cleanEtpParam">
                <a-button style="float: right;">清除</a-button>
              </a-popconfirm>
            </div>

            <div class="mt10">①Cycle：ETP起始及结束工步输入</div>
            <a-table class="mt10"
                     bordered
                     :columns="cycleEtpColumns"
                     :data-source="cycleReportParam.cycleStepParamList"
                     :pagination="false">
              <template slot="etpStartStep" slot-scope="text, record, index, columns">
                <a-input class="input" :disabled="!cycleReportParam.etpType"
                         v-model="cycleReportParam.cycleStepParamList[index].etpStartStep"
                         @keyup="cycleReportParam.cycleStepParamList[index].etpStartStep = (cycleReportParam.cycleStepParamList[index].etpStartStep + '').replaceAll(/[^0-9]/g, '')"
                         @blur="handleBlur('cycleStepParamList', index, null, record)"/>
              </template>
              <template slot="etpEndStep" slot-scope="text, record, index, columns">
                <a-input class="input" :disabled="!cycleReportParam.etpType"
                         v-model="cycleReportParam.cycleStepParamList[index].etpEndStep"
                         @keyup="cycleReportParam.cycleStepParamList[index].etpEndStep = (cycleReportParam.cycleStepParamList[index].etpEndStep + '').replaceAll(/[^0-9]/g, '')"
                         @blur="handleBlur('cycleStepParamList', index, null, record)"/>
              </template>
            </a-table>

            <div class="mt10">②RPT：ETP结束工步输入</div>
            <a-table class="mt10"
                     bordered
                     :columns="rptEtpColumns"
                     :data-source="cycleReportParam.rptStepParamList"
                     :pagination="false">
              <template slot="etpEndStep" slot-scope="text, record, index, columns">
                <a-input class="input" :disabled="!cycleReportParam.etpType"
                         v-model="cycleReportParam.rptStepParamList[index].etpEndStep"
                         @keyup="cycleReportParam.rptStepParamList[index].etpEndStep = (cycleReportParam.rptStepParamList[index].etpEndStep + '').replaceAll(/[^0-9]/g, '')"
                         @blur="handleBlur"/>
              </template>
            </a-table>
          </div>
        </div>
      </div>
    </div>

    <!-- 测试项目选择弹窗 -->
    <a-modal
      title="测试项目选择"
      :width="1200"
      :height="600"
      :bodyStyle="{ padding: 0 }"
      :visible="visible"
      style="padding: 0"
      :maskClosable="false"
      @cancel="handleCancel('visible')"
    >
      <div style="float: left;position: relative;z-index: 1;padding-bottom: 5px;width: 65%">
        <a-row :gutter="[8, 8]">
          <a-col :span="6">
            <a-form-item label="委托单号" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input
                v-model="queryParam.folderno"
                @keyup.enter="$refs.table.refresh()"
                @change="$refs.table.refresh()"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="主题" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input
                v-model="queryParam.theme"
                @keyup.enter="$refs.table.refresh()"
                @change="$refs.table.refresh()"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="测试项目别名" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input
                v-model="queryParam.alias"
                @keyup.enter="$refs.table.refresh()"
                @change="$refs.table.refresh()"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="存储天数" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input
                v-model="queryParam.day"
                @keyup.enter="$refs.table.refresh()"
                @change="$refs.table.refresh()"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <s-table
        :columns="columns"
        :data="loadData"
        bordered
        :rowKey="record1 => record1.uuid"
        ref="table"
        :row-selection="{
					selectedRowKeys: selectedRowKeys,
					selectedRows: selectedRows,
					onSelect: onSelectChange,
					onSelectAll: onSelectAllChange,
					columnWidth: 30
				}"
      >
        <template slot="celltestcode" slot-scope="text, record, index, columns">
          <a @click="openStepData(record)" v-if="record.flowId != null" style="text-align: center">{{ text }}</a>
          <span v-else style="text-align: center">{{ text }}</span>
        </template>

        <template slot="action1" slot-scope="text, record, index, columns">
          <template v-if="record.showHide">
            <a @click="showData(record)" v-if="record.showHide" style="text-align: center">初始化</a>
            <a-divider v-if="record.children != null" type="vertical"/>
          </template>

          <a @click="hideData(record)" v-if="record.children != null || record.isChild"
             style="text-align: center">隐藏</a>
        </template>

        <template slot="theme" slot-scope="text, record, index, columns">
          <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
            <template slot="title">
              {{ text }}
            </template>
            {{ text }}
          </a-tooltip>
        </template>
        <template slot="dataPath" slot-scope="text, record, index, columns">
          <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
            <template slot="title">
              {{ text ? text : record.testcontent }}
            </template>
            {{ text ? text : record.testcontent }}
          </a-tooltip>
        </template>
      </s-table>

      <template slot="footer">
        <a-button key="back" @click="handleCancel('visible')">关闭</a-button>
      </template>
    </a-modal>

    <!-- 完成模型搭建弹窗 -->
    <a-modal title="完成模型搭建" :width="350" :visible="isShowReportName" @cancel="handleCancel('isShowReportName')">
      <template slot="footer">
        <div class="btn-wrap">
          <a-button @click="handleCancel('isShowReportName')">
            取消
          </a-button>
          <a-button type="primary" @click="exportData">
            完成模型搭建
          </a-button>
        </div>
      </template>
      <div class="phase-modal">
        <span class="mr10">任务名称:</span>
        <a-input v-model="cycleReportParam.reportName" style="width: 200px;" placeholder="请填写任务名称"
                 @keyup.enter="exportData"/>
      </div>
    </a-modal>

    <!-- 历史记录弹窗 -->
    <a-modal title="历史记录" :width="1200" :height="600" :bodyStyle="{ padding: 0 }" :visible="isShowHistorical"
             style="padding: 0" :maskClosable="false" @cancel="handleCancel('isShowHistorical')">

      <div style="float: left;position: relative;z-index: 1;padding-bottom: 5px;width: 65%">
        <a-row :gutter="[8, 8]">
          <a-col :span="8">
            <a-form-item label="任务状态" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-select v-model="queryHistoryParam.fileStatus" @change="$refs.historyTable.refresh()"
                        style="width: 100%" :allow-clear="true">
                <a-select-option value="0">
                  待处理
                </a-select-option>
                <a-select-option value="10">
                  进行中
                </a-select-option>
                <a-select-option value="20">
                  已完成
                </a-select-option>
                <a-select-option value="30">
                  数据异常
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="创建人" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-model="queryHistoryParam.createName" @keyup.enter="$refs.historyTable.refresh()"
                       @change="$refs.historyTable.refresh()"/>
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <s-table :columns="historyColumns" :data="historyData" bordered :rowKey="record1 => record1.id"
               ref="historyTable">
        <template slot="reportName" slot-scope="text, record, index, columns">
          <a target="_blank" @click="pushToReview(record)" v-if="record.fileStatus == 20"
             style="margin-right: 12px">{{ text }}</a>
        </template>
        <template slot="action" slot-scope="text, record, index, columns">
          <a-button type="link" @click="handleCopyHistory(record)">复制</a-button>
        </template>
      </s-table>
      <template slot="footer">
        <a-button key="back" @click="handleCancel('isShowHistorical')">
          关闭
        </a-button>
      </template>
    </a-modal>

    <!-- 草稿箱弹窗 -->
    <a-modal title="草稿箱" :width="1200" :height="600" :bodyStyle="{ padding: 0 }" :visible="isShowDrafts"
             style="padding: 0" :maskClosable="false" @cancel="handleCancel('isShowDrafts')">

      <div style="float: left;position: relative;z-index: 1;padding-bottom: 5px;width: 65%">
        <a-row :gutter="[8, 8]">
          <a-col :span="8">
            <a-form-item label="创建人" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
              <a-input v-model="queryDraftParam.createName" @keyup.enter="$refs.draftTable.refresh()"
                       @change="$refs.draftTable.refresh()"/>
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <s-table :columns="draftColumns" :data="draftData" bordered :rowKey="record1 => record1.id"
               ref="draftTable">
        <template slot="action" slot-scope="text, record, index, columns">
          <a-button type="link" @click="handleDraf(record)">编辑</a-button>
        </template>
      </s-table>
      <template slot="footer">
        <a-button key="back" @click="handleCancel('isShowDrafts')">
          关闭
        </a-button>
      </template>
    </a-modal>

    <!-- 圈数计算规则输入弹窗 -->
    <a-modal title="循环规则输入" :width="1200" :height="600" :bodyStyle="{ padding: 0 }" :visible="isShowCompute"
             style="padding: 0" :maskClosable="false" @cancel="handleCancel('isShowCompute')">
      <div class="mt10">
        <a-button class="ml10 mr10" size="small" @click="addParam('rptCycComList')">
          <a-icon type="plus"/>
          增加循环阶段
        </a-button>
        <a-button class="mr10" size="small" @click="computeCycles">
          <a-icon type="calculator"/>
          计算
        </a-button>
        <a-button size="small" @click="handleOpen('isShowRptCycleList')">
          <a-icon type="ordered-list"/>
          详情
        </a-button>
      </div>
      <a-table id="cycComputeTable" class="mt10"
               bordered
               :columns="rptCycComColumns"
               :data-source="cycleReportParam.rptCycComList"
               :pagination="false">
        <template slot="action" slot-scope="text, record, index, columns">
          <a @click="deleteParam('rptCycComList', index)" style="text-align: center">删除</a>
        </template>
        <template slot="cycStart" slot-scope="text, record, index, columns">
          <a-input-number class="input" :min="0" :formatter="formatter" :parser="parser" v-model="cycleReportParam.rptCycComList[index].cycStart"/>
        </template>
        <template slot="cycEnd" slot-scope="text, record, index, columns">
          <a-input-number class="input" :min="0" :formatter="formatter" :parser="parser" v-model="cycleReportParam.rptCycComList[index].cycEnd"/>
        </template>
        <template slot="cycInterval" slot-scope="text, record, index, columns">
          <a-input-number class="input" :min="0" :formatter="formatter" :parser="parser" v-model="cycleReportParam.rptCycComList[index].cycInterval"/>
        </template>
      </a-table>
      <template slot="footer">
        <a-button key="back" @click="handleCancel('isShowCompute')">关闭</a-button>
      </template>
    </a-modal>

    <!-- 圈数详情展示弹窗 -->
    <a-modal title="详情" :width="600" :height="1200" :bodyStyle="{ padding: 0 }" :visible="isShowRptCycleList"
             style="padding: 0" :maskClosable="false" @cancel="handleCancel('isShowRptCycleList')">
      <a-table class="mt10"
               bordered
               :columns="rptCycleColumns"
               :data-source="cycleReportParam.rptCycleList"
               :pagination="false">
      </a-table>
      <template slot="footer">
        <a-button key="back" @click="handleCancel('isShowRptCycleList')">关闭</a-button>
      </template>
    </a-modal>

    <step-data ref="stepData"></step-data>

  </div>
</template>

<script>
import {STable} from "@/components";
import stepData from "@/views/system/lims/folder/stepData";
import moment from "moment";
import {hideData, showData, testReportPageList, tLimsTestdataSchedulePageList} from "@/api/modular/system/limsManager";
import jsonBigint from "json-bigint";
import {cycleTestReportTask, commitCycle, getCycleById} from "@/api/modular/system/cycleReportManager";
import {mapGetters} from "vuex";

export default {
  name: "cycleReportBuild",
  components: {
    STable,
    stepData
  },
  data() {
    return {
      labelCol: {
        sm: {
          span: 11
        }
      },
      wrapperCol: {
        sm: {
          span: 13
        }
      },

      testReportID: null, //该份测试报告的ID

      visible: false,
      isShowReportName: false, // 完成模型搭建-报告名称填写弹窗
      isShowHistorical: false,
      isShowDrafts: false,
      queryHistoryParam: {
        type: "Cycle"
      },
      queryDraftParam: {
        fileStatus: -10,
        type: 'Cycle'
      },
      historyColumns: [
        {
          title: '序号',
          align: 'center',
          width: 50,
          customRender: (text, record, index) => index + 1
        },
        {
          title: '任务名称',
          dataIndex: 'reportName',
          align: 'center',
          width: 100,
          scopedSlots: {customRender: 'reportName'},
        },
        /*{
          title: '任务名称',
          dataIndex: 'reportName',
          align: 'center',
          width: 200,
          scopedSlots: { customRender: 'reportName' },
        },
        {
          title: '任务类型',
          width: 90,
          align: 'center',
          dataIndex: 'type'
        },
        {
          title: '操作类型',
          width: 90,
          align: 'center',
          dataIndex: 'operateType',
          customRender: (text, record, index) => {
            if (record.operateType == 'add') {
              return "初次创建"
            }
            if (record.operateType == 'update') {
              return "更新数据"
            }
            if (record.operateType == 'refresh') {
              return "刷新数据"
            }
          }
        },*/
        {
          title: '操作时间',
          width: 90,
          align: 'center',
          dataIndex: 'createTime',

        },
        {
          title: '操作人',
          width: 90,
          align: 'center',
          dataIndex: 'createName',

        },
        {
          title: "操作",
          align: "center",
          width: 30,
          scopedSlots: {customRender: "action"}
        },
      ],
      draftColumns: [
        {
          title: '序号',
          align: 'center',
          width: 50,
          customRender: (text, record, index) => index + 1
        },
        {
          title: '任务名称',
          dataIndex: 'reportName',
          align: 'center',
          width: 200,
          scopedSlots: {customRender: 'reportName'},
        },
        /*{
          title: '任务类型',
          width: 90,
          align: 'center',
          dataIndex: 'type'
        },
        {
          title: '操作类型',
          width: 90,
          align: 'center',
          dataIndex: 'operateType',
          customRender: (text, record, index) => {
            if (record.operateType == 'add') {
              return "初次创建"
            }
            if (record.operateType == 'update') {
              return "更新数据"
            }
            if (record.operateType == 'refresh') {
              return "刷新数据"
            }
          }
        },*/
        {
          title: '操作时间',
          width: 90,
          align: 'center',
          dataIndex: 'createTime',

        },
        {
          title: '操作人',
          width: 90,
          align: 'center',
          dataIndex: 'createName',

        },
        {
          title: "操作",
          align: "center",
          width: 30,
          scopedSlots: {customRender: "action"}
        },
      ],
      historyData: parameter => {
        return testReportPageList(Object.assign(parameter, this.queryHistoryParam)).then((res) => {
          return res.data
        })
      },
      draftData: parameter => {
        return testReportPageList(Object.assign(parameter, this.queryDraftParam)).then((res) => {
          return res.data
        })
      },
      // 测试项目选择
      queryParam: {},
      columns: [
        {
          title: "操作",
          align: "center",
          width: 105,
          scopedSlots: {customRender: 'action1'},
        }, {
          title: "序号",
          align: "center",
          width: 40,
          customRender: (text, record, index) => {
            if (!record.isChild) {
              return index + 1
            }
          }
        },
        {
          title: "委托单号",
          dataIndex: "folderno",
          align: "center",
          width: 90
        },
        {
          title: "主题",
          dataIndex: "theme",
          align: "center",
          ellipsis: true,
          width: 90,
          scopedSlots: {customRender: 'theme'},
        },
        {
          title: "样品编号",
          width: 90,
          align: "center",
          dataIndex: "orderno"
        },
        /*{
          title: "测试项目编码",
          width: 90,
          align: "center",
          dataIndex: "testcode"
          //scopedSlots: {customRender: 'updateText'},
        },*/
        {
          title: "测试项目别名",
          width: 90,
          align: "center",
          dataIndex: "alias"
          //scopedSlots: {customRender: 'updateText'},
        },
        {
          title: "测试编码",
          width: 90,
          align: "center",
          dataIndex: "celltestcode",
          scopedSlots: {customRender: "celltestcode"}
        },
        {
          title: "数据位置",
          width: 80,
          align: "center",
          dataIndex: "dataPath",
          ellipsis: true,
          scopedSlots: {customRender: 'dataPath'},
        }, {
          title: "存储天数",
          width: 75,
          align: "center",
          dataIndex: "day"
        },
        {
          title: "温度",
          width: 40,
          align: "center",
          dataIndex: "tem"
        }, {
          title: "soc",
          width: 40,
          align: "center",
          dataIndex: "soc"
        },
        {
          title: "开始时间",
          width: 80,
          align: "center",
          dataIndex: "startTime",
          customRender: (text, record, index) => {
            if (null != text) {
              return moment(text).format("YYYY-MM-DD")
            }
            return text
          }
          //
          //scopedSlots: {customRender: 'updateText'},
        },
        {
          title: "结束时间",
          width: 80,
          align: "center",
          dataIndex: "endTime",
          customRender: (text, record, index) => {
            if (null != text) {
              return moment(text).format("YYYY-MM-DD")
            }
            return text
          }
        },
        {
          title: "设备编号",
          width: 60,
          align: "center",
          dataIndex: "equiptcode"
        },
        {
          title: "通道编号",
          width: 60,
          align: "center",
          dataIndex: "channelno"
        }
      ],
      loadData: parameter => {
        this.loadDataList = []
        return tLimsTestdataSchedulePageList(Object.assign(parameter, this.queryParam))
          .then(res => {
            return res.data
          })

      },
      selectedRowKeys: [],
      selectedRows: [],
      deleteSelectedRowKeys: [],
      deleteSelectedRows: [],
      outFlowRecord: null,
      outQueryFlowRecord: null,
      // 测试数据表头
      orderColumns: [
        {
          title: "操作",
          align: "center",
          width: 40,
          scopedSlots: {customRender: "action"}
        },
        {
          title: "序号",
          align: "center",
          width: 30,
          customRender: (text, record, index) => index + 1
        },
        {
          title: "委托单号",
          dataIndex: "folderno",
          align: "center",
          width: 45
        },
        // {
        // 	title: "测试项目编码",
        // 	width: 70,
        // 	align: "center",
        // 	dataIndex: "testcode"
        // 	//scopedSlots: {customRender: 'updateText'},
        // },
        {
          title: "测试项目别名",
          width: 65,
          align: "center",
          dataIndex: "alias"
          //scopedSlots: {customRender: 'updateText'},
        },
        {
          title: "样品编号",
          width: 50,
          align: "center",
          dataIndex: "orderno"
        },
        {
          title: "测试编码",
          width: 50,
          align: "center",
          dataIndex: "celltestcode",
          scopedSlots: {customRender: "celltestcode"}
        },
        {
          title: "数据位置",
          width: 50,
          align: "center",
          dataIndex: "dataPath",
          ellipsis: true,
          scopedSlots: {customRender: 'dataPath'},
        },
        {
          title: "设备编号",
          width: 45,
          align: "center",
          dataIndex: "equiptcode",
          customRender: (text, record, index) => {
            return (null != text ? text : "") + "-" + (null != record.channelno ? record.channelno : "")
          }
        },
        {
          title: "开始时间",
          width: 50,
          align: "center",
          dataIndex: "startTime",
          customRender: (text, record, index) => {
            if (null != text) {
              return moment(text).format("YYYY-MM-DD")
            }
            return text
          }
        },
        {
          title: "结束时间",
          width: 50,
          align: "center",
          dataIndex: "endTime",
          customRender: (text, record, index) => {
            if (null != text) {
              return moment(text).format("YYYY-MM-DD")
            }
            return text
          }
        },
        {
          title: "电芯组别",
          width: 45,
          align: "center",
          dataIndex: "groupIndex"
        },
      ],
      deleteRowSelection: {
        columnWidth: 20,
        onChange: (selectedRowKeys, selectedRows) => {
          this.deleteSelectedRowKeys = selectedRowKeys
          this.deleteSelectedRows = selectedRows
        }
      },

      isTempClose: false,
      isCycleParamClose: false,
      isRptParamClose: false,
      isEtpParamClose: true,

      cycleReportParam: {
        reportName: '',
        temp: '',
        etpType: '',
        orderDataList: [], // 选择的测试数据
        cycleStepParamList: [ {} ], // Cycle工步参数列表
        rptCycComList: [], // RPT圈数计算列表
        rptCycleList: [], // RPT循环圈数列表
        rptStepParamList: [ { dcirStepParamList:[] } ], // RPT工步参数列表
        dcirTiTleList: [""], // DCIR标题列表
      },
      cycleListDeleteSelectedRowKeys: [],
      rptListDeleteSelectedRowKeys: [],

      primaryGroupOptions: [],

      // cycle工步参数表头
      cycleStepColumns: [
        {
          title: "操作",
          align: "center",
          width: 40,
          scopedSlots: {customRender: "action"}
        },
        {
          title: "序号",
          align: "center",
          width: 40,
          customRender: (text, record, index) => index + 1
        },
        {
          dataIndex: "groupIndex",
          align: "center",
          width: 100,
          scopedSlots: {
            title: "groupIndexTitle",
            customRender: "groupIndex"
          }
        },
        {
          dataIndex: "ceStep",
          align: "center",
          width: 120,
          scopedSlots: {
            title: "ceStepTitle",
            customRender: "ceStep"
          }
        },
        {
          dataIndex: "chCeStep",
          align: "center",
          width: 120,
          scopedSlots: {
            title: "chCeStepTitle",
            customRender: "chCeStep"
          }
        },
      ],
      // cycleETP参数表头
      cycleEtpColumns: [
        {
          title: "序号",
          align: "center",
          width: 40,
          customRender: (text, record, index) => index + 1
        },
        {
          title: "电芯组别",
          width: 100,
          align: "center",
          customRender: (text, record, index) => typeof record.groupIndex === 'number' ? record.groupIndex + 1 : ''
        },
        {
          title: "ETP起始工步号",
          dataIndex: "etpStartStep",
          align: "center",
          width: 100,
          scopedSlots: {customRender: "etpStartStep"}
        },
        {
          title: "ETP结束工步号",
          dataIndex: "etpEndStep",
          align: "center",
          width: 100,
          scopedSlots: {customRender: "etpEndStep"}
        },
      ],

      isShowCompute: false,
      // rpt圈数计算列表表头
      rptCycComColumns: [
        {
          title: "操作",
          align: "center",
          width: 40,
          scopedSlots: {customRender: "action"}
        },
        {
          title: "序号",
          align: "center",
          width: 40,
          customRender: (text, record, index) => index + 1
        },
        {
          title: "开始循环圈数",
          dataIndex: "cycStart",
          align: "center",
          width: 100,
          scopedSlots: {customRender: "cycStart"}
        },
        {
          title: "结束循环圈数",
          dataIndex: "cycEnd",
          align: "center",
          width: 100,
          scopedSlots: {customRender: "cycEnd"}
        },
        {
          title: "中检间隔圈数",
          dataIndex: "cycInterval",
          align: "center",
          width: 100,
          scopedSlots: {customRender: "cycInterval"}
        },
        {
          title: "圈数列表",
          dataIndex: "cycleList",
          align: "center",
          width: 200,
          customRender: (text, record, index, column) => {
            let cycles = record.cycleList || []
            return cycles.join(',')
          }
        },
        {
          title: "圈数数量",
          dataIndex: "cycleNum",
          align: "center",
          width: 100
        },
      ],

      isShowRptCycleList: false,
      // rpt循环圈数表头
      rptCycleColumns: [
        {
          title: "序号",
          align: "center",
          width: 40,
          customRender: (text, record, index) => index + 1
        },
        {
          title: "圈数",
          dataIndex: "cycle",
          align: "center",
          width: 100,
          scopedSlots: {customRender: "cycle"}
        },
      ],

      dcirNum: 1,
      dcirTitleIndexArr: [],
      // rpt工步参数表头
      rptParamColumns: [],
      // rptETP参数表头
      rptEtpColumns: [
        {
          title: "序号",
          align: "center",
          width: 40,
          customRender: (text, record, index) => index + 1
        },
        {
          title: "电芯组别",
          width: 100,
          align: "center",
        customRender: (text, record, index) => typeof record.groupIndex === 'number' ? record.groupIndex + 1 : ''
        },
        {
          title: "ETP结束工步号",
          dataIndex: "etpEndStep",
          align: "center",
          width: 100,
          scopedSlots: {customRender: "etpEndStep"}
        },
      ],
    }
  },
  computed: {
    ...mapGetters(["testTaskFilterData", "testTaskId", "userInfo"]),
  },
  mounted() {
  },
  created() {

    // 刷新页面
    if (window.sessionStorage.getItem('cycleTestReportID')) {
      this.testReportID = window.sessionStorage.getItem('cycleTestReportID')
      return this.getCycleById({id: window.sessionStorage.getItem('cycleTestReportID')})
    }

    // 在测试报告窗口新增
    if (this.testTaskId !== null) {
      this.cycleReportParam = {
        reportName: this.testTaskId,
        temp: '',
        etpType: '',
        orderDataList: [], // 选择的测试数据
        cycleStepParamList: [ {} ], // Cycle工步参数列表
        rptCycComList: [], // RPT圈数计算列表
        rptCycleList: [], // RPT循环圈数列表
        rptStepParamList: [ { dcirStepParamList:[] } ], // RPT工步参数列表
        dcirTiTleList: [""], // DCIR标题列表
      }
      // 初始化rpt参数表头
      this.changeRptParamColumns(true)

      this.cycleTestReportTask(this.cycleReportParam)
      this.$store.commit("setTaskID", null)
      return
    }

    // 如果是重新生成
    if (this.testTaskFilterData !== null) {
      this.cycleReportParam = this.testTaskFilterData
      // 初始化rpt参数表头
      this.changeRptParamColumns(true)

      this.primaryGroupChange()

      this.cycleTestReportTask(this.cycleReportParam)
      this.$store.commit("setTaskFilterData", null)
      return
    }

    // 如果没有数据，初始化rpt参数表头
    this.changeRptParamColumns(true)
  },
  destroyed() {
    window.sessionStorage.removeItem('cycleTestReportID')
  },
  methods: {
    handleOpen(target) {
      this[target] = true
    },
    handleCancel(target) {
      this[target] = false
    },
    handleClose(index) {
      this[index] = !this[index]
    },
    cycleDeleteRowOnChange(selectedRowKeys, selectedRows) {
      this.cycleListDeleteSelectedRowKeys = selectedRowKeys
    },
    rptDeleteRowOnChange(selectedRowKeys, selectedRows) {
      this.rptListDeleteSelectedRowKeys = selectedRowKeys
    },
    deleteSelectedParams(targetObj) {
      // 按照索引顺序降序排列，避免删除元素影响后续索引
      this[`${targetObj}ListDeleteSelectedRowKeys`].sort((a, b) => b - a)
      this[`${targetObj}ListDeleteSelectedRowKeys`].forEach(item => this.cycleReportParam[`${targetObj}StepParamList`].splice(item, 1))
      this[`${targetObj}ListDeleteSelectedRowKeys`] = []
      // 全删除需要保留一行
      if(this.cycleReportParam[`${targetObj}StepParamList`].length === 0) {
        this.cycleReportParam[`${targetObj}StepParamList`] = targetObj === 'cycle' ? [ {} ] : [ { dcirStepParamList:[] } ]
        if (targetObj === 'rpt') {
          this.cycleReportParam.dcirTiTleList = ['']
          this.dcirNum = 1
          this.changeRptParamColumns(false)
          return
        }
      }
      // 实时保存
      this.cycleTestReportTask(this.cycleReportParam, this.testReportID)
    },
    deleteSelect() {
      this.cycleReportParam.orderDataList = this.cycleReportParam.orderDataList.filter(item => !this.deleteSelectedRowKeys.includes(item.uuid));
      this.selectedRows = this.selectedRows.filter(item => !this.deleteSelectedRowKeys.includes(item.uuid));
      this.selectedRowKeys = this.selectedRowKeys.filter(item => !this.deleteSelectedRowKeys.includes(item));
      this.deleteSelectedRowKeys = []
      this.deleteSelectedRows = []

      this.$set(this.cycleReportParam, 'temp', this.cycleReportParam.orderDataList && this.cycleReportParam.orderDataList.length > 0 ? this.cycleReportParam.orderDataList[0].tem : '')

      this.primaryGroupChange()

      // 实时保存
      this.cycleTestReportTask(this.cycleReportParam, this.testReportID)
    },
    onSelectChange(record, selected) {
      this.outFlowRecord = record
      this.outQueryFlowRecord = record

      if (selected) {
        if (record.flowId == null) {
          this.$message.warn("测试数据为空")
          return
        }
        /*if(record.flowInfoList.length == 0){
            this.$message.warn("测试数据为空")
            return
          }else if(record.flowInfoList.length > 1){
            this.visibleFlow = true
            this.flowInfoData = record.flowInfoList
            this.inFlowActionName = '选中'
            return
          }else if(record.flowInfoList.length == 1){
            record.flowId = record.flowInfoList[0].flowId
          }*/

        if (!this.selectedRowKeys.includes(record.uuid)) {
          this.selectedRowKeys.push(record.uuid)
          this.selectedRows.push(record)
          this.cycleReportParam.orderDataList.push(record)
        }
      } else {
        for (let i = 0; i < this.selectedRowKeys.length; i++) {
          if (this.selectedRowKeys[i] === record.uuid) {
            this.selectedRowKeys.splice(i, 1)
            this.selectedRows.splice(i, 1)
            this.cycleReportParam.orderDataList.splice(i, 1)
            break
          }
        }
      }

      this.$set(this.cycleReportParam, 'temp', this.cycleReportParam.orderDataList && this.cycleReportParam.orderDataList.length > 0 ? this.cycleReportParam.orderDataList[0].tem : '')

      this.primaryGroupChange()

      // 实时保存
      this.cycleTestReportTask(this.cycleReportParam, this.testReportID)
    },
    onSelectAllChange(selected, selectedRows, changeRows) {
      if (selected) {
        /*for (let i = 0; i < selectedRows.length; i++) {
          if (selectedRows[i].flowId == null) {
            this.$message.warn("序号" + (i + 1) + "测试数据为空")
            return
          }
        }*/
        selectedRows.forEach(item => {
          if (!this.selectedRowKeys.includes(item.uuid) && item.flowId != null) {
            this.selectedRowKeys.push(item.uuid)
            this.selectedRows.push(item)
            this.cycleReportParam.orderDataList.push(item)
          }

          /*if(item.children && item.children.length > 0){
              item.children.forEach(inItem => {
                if (!this.selectedRowKeys.includes(inItem.id)) {
                  this.selectedRowKeys.push(inItem.id)
                  this.selectedRows.push(inItem)
                  this.cycleReportParam.orderDataList.push(inItem)
                }
              })
            }*/
        })
      } else {
        for (let i = 0; i < changeRows.length; i++) {
          if (this.selectedRowKeys.includes(changeRows[i].uuid)) {
            let index = this.selectedRowKeys.indexOf(changeRows[i].uuid)
            this.selectedRowKeys.splice(index, 1)
            this.selectedRows.splice(index, 1)
            this.cycleReportParam.orderDataList.splice(index, 1)
          }
        }
      }

      this.$set(this.cycleReportParam, 'temp', this.cycleReportParam.orderDataList && this.cycleReportParam.orderDataList.length > 0 ? this.cycleReportParam.orderDataList[0].tem : '')

      this.primaryGroupChange()

      // 实时保存
      this.cycleTestReportTask(this.cycleReportParam, this.testReportID)
    },
    openStepData(record, flag) {
      this.outQueryFlowRecord = record
      this.outFlowRecord = record

      //历史数据处理
      if (null == record.flowInfoList && !flag) {
        this.$refs.stepData.query(record, false)
        return;
      }

      if (record.flowId != null) {
        this.outQueryFlowRecord.flowId = record.flowId
        this.$refs.stepData.query(this.outQueryFlowRecord, false)
      } else {
        this.$message.warn("测试数据为空")
        return
      }
    },
    showData(record) {
      showData({celltestcode: record.celltestcode}).then(res => {
        this.$refs.table.refresh()
      })
    },
    hideData(record) {
      hideData({id: record.id}).then(res => {
        this.$refs.table.refresh()
      })
    },
    deleteDataOne(record, index) {
      this.selectedRows.splice(index, 1)
      this.selectedRowKeys.splice(index, 1)
      this.cycleReportParam.orderDataList.splice(index, 1)

      this.$set(this.cycleReportParam, 'temp', this.cycleReportParam.orderDataList && this.cycleReportParam.orderDataList.length > 0 ? this.cycleReportParam.orderDataList[0].tem : '')

      this.primaryGroupChange()

      // 实时保存
      this.cycleTestReportTask(this.cycleReportParam, this.testReportID)
    },
    moveUp(arr, index) {
      if (arr.length > 1 && index > 0) { // 确保数组至少有两个元素，且索引有效
        arr[index] = arr.splice(index - 1, 1, arr[index])[0]; // 移除元素后立即插入到前一个位置
      }

      this.$set(this.cycleReportParam, 'temp', this.cycleReportParam.orderDataList[0] ? this.cycleReportParam.orderDataList[0].tem : '')

      this.primaryGroupChange()

      // 实时保存
      this.cycleTestReportTask(this.cycleReportParam, this.testReportID)
    },
    moveDown(arr, index) {
      if (arr.length > 1 && index < arr.length - 1) { // 确保数组至少有两个元素，且索引有效
        arr[index] = arr.splice(index + 1, 1, arr[index])[0]; // 移除元素后立即插入到后一个位置
      }

      this.$set(this.cycleReportParam, 'temp', this.cycleReportParam.orderDataList[0] ? this.cycleReportParam.orderDataList[0].tem : '')

      this.primaryGroupChange()

      // 实时保存
      this.cycleTestReportTask(this.cycleReportParam, this.testReportID)
    },

    /*
    * 历史记录按钮事件
    */
    pushToReview(record) {
      window.open("/v_report_preview?id=" + record.id + "&type=" + record.type, "_blank")
    },
    handleCopyHistory(record) {
      // 生成新的数据
      let json = jsonBigint({storeAsString: true})
      this.cycleTestReportTask(json.parse(record.queryParam))
      this.isShowHistorical = false
    },
    /*
    * 草稿箱按钮事件
    */
    handleDraf(record) {
      this.isEtpParamClose = true
      this.testReportID = record.id
      this.getCycleById({id: record.id})
      window.sessionStorage.setItem("cycleTestReportID", record.id)
      this.isShowDrafts = false
    },

    primaryGroupChange() {
      // 根据电芯编码分组，组内编号
      const groupedByOrderno = this.cycleReportParam.orderDataList.reduce((acc, curr) => {
        const orderno = curr.orderno
        if (!acc[orderno]) {
          acc[orderno] = []
        }
        curr.groupIndex = acc[orderno].length + 1
        acc[orderno].push(curr)
        return acc
      }, {})
      // 将分组后的结果展平成一个新的列表
      this.cycleReportParam.orderDataList = [].concat(...Object.values(groupedByOrderno))

      // 找到最大的分组大小
      const maxGroupSize = Object.values(groupedByOrderno).reduce((maxGroupSize, group) => Math.max(maxGroupSize, group.length), 0)

      // 分组数量变化
      if (maxGroupSize !== this.primaryGroupOptions.length) {
        this.maxGroupSizeChange(maxGroupSize)
      }
    },

    maxGroupSizeChange(maxGroupSize = 0) {
      this.primaryGroupOptions = []
      for (let i = 0; i < maxGroupSize; i++) {
        this.primaryGroupOptions.push({value:i, label:i+1})
      }

      const clearAll = this.cycleReportParam.orderDataList.length === 0 || this.primaryGroupOptions.length === 0
      for (let i = 0; i < this.cycleReportParam.cycleStepParamList.length; i++) {
        let row = this.cycleReportParam.cycleStepParamList[i]
        if ('groupIndex' in row && (clearAll || row.groupIndex > this.primaryGroupOptions.length - 1)) {
          delete row.groupIndex
        }
      }
      for (let i = 0; i < this.cycleReportParam.rptStepParamList.length; i++) {
        let row = this.cycleReportParam.rptStepParamList[i]
        if ('groupIndex' in row && (clearAll || row.groupIndex > this.primaryGroupOptions.length - 1)) {
          delete row.groupIndex
        }
      }

    },

    changeDcirParamList(isDelete = false) {
      if (isDelete) {
        if (this.dcirNum > 1) {
          this.dcirNum--
          this.cycleReportParam.dcirTiTleList.splice(this.dcirNum, 1)
        } else {
          return this.$message.warn('至少一组DCIR参数，无法减少')
        }
      } else {
        if (this.dcirNum < 4) {
          this.dcirNum++
          this.cycleReportParam.dcirTiTleList.push("")
        } else {
          return this.$message.warn('最多四组DCIR参数，无法增加')
        }
      }

      this.changeRptParamColumns(false)
    },

    changeRptParamColumns(isFirst = false) {
      if (isFirst) {
        if (Array.isArray(this.cycleReportParam.dcirTiTleList) && this.cycleReportParam.dcirTiTleList.length > 0) {
          this.dcirNum = this.cycleReportParam.dcirTiTleList.length
        } else {
          this.cycleReportParam.dcirTiTleList = [""]
          this.dcirNum = 1
        }
      }

      for (let i = 0; i < this.cycleReportParam.rptStepParamList.length; i++) {
        // DCIR参数组赋值
        this.cycleReportParam.rptStepParamList[i].dcirStepParamList = this.cycleReportParam.rptStepParamList[i].dcirStepParamList || []
        for (let j = this.cycleReportParam.rptStepParamList[i].dcirStepParamList.length; j < this.dcirNum; j++) {
          this.cycleReportParam.rptStepParamList[i].dcirStepParamList.push({})
        }

        // 删除DCIR需要清空参数组
        for (let j = this.cycleReportParam.rptStepParamList[i].dcirStepParamList.length; j > this.dcirNum; j--) {
          this.cycleReportParam.rptStepParamList[i].dcirStepParamList.splice(j - 1, 1)
        }
      }

      this.rptParamColumns =  [
        {
          title: "操作",
          align: "center",
          width: 40,
          scopedSlots: {customRender: "action"}
        },
        {
          title: "序号",
          align: "center",
          width: 40,
          customRender: (text, record, index) => index + 1
        },
        {
          dataIndex: "groupIndex",
          align: "center",
          width: 100,
          scopedSlots: {
            title: "groupIndexTitle",
            customRender: "groupIndex"
          }
        },
        {
          dataIndex: "ceStep",
          align: "center",
          width: 130,
          scopedSlots: {
            title: "ceStepTitle",
            customRender: "ceStep"
          }
        },
      ]
      this.dcirTitleIndexArr = []
      for (let i = 0; i < this.dcirNum; i++) {
        this.rptParamColumns.push(
          {
            children: [
              {
                title: "搁置工步号",
                width: 100,
                align: "center",
                dataIndex: "dcirStepParamList[" + i + "].restStep",
                scopedSlots: {customRender: "dcirStepParam"}
              },
              {
                title: "放电工步号",
                width: 100,
                align: "center",
                dataIndex: "dcirStepParamList[" + i + "].dchStep",
                scopedSlots: {customRender: "dcirStepParam"}
              },
              {
                width: 130,
                align: "center",
                dataIndex: "dcirStepParamList[" + i + "].dchStepTime",
                scopedSlots: {
                  title: "dchStepTimeTitle",
                  customRender: "dcirStepParam"
                }
              }
            ],
            scopedSlots: {
              title: "dcirTitle_" + i
            }
          }
        )

        this.dcirTitleIndexArr.push(i)
      }

      // 实时保存
      if (!isFirst) {
        this.cycleTestReportTask(this.cycleReportParam, this.testReportID)
      }
    },

    // 循环报告任务新增、暂存或实时修改
    cycleTestReportTask(parameter, id = null) {
      // 如果没有id就是新增
      // 如果有id就是暂存或实时修改
      let json = jsonBigint({storeAsString: true})
      const params = json.parse(JSON.stringify(parameter))
      cycleTestReportTask(params, id).then(res => {
        // 新建
        if (id == null) {
          this.testReportID = res.data.id
          window.sessionStorage.setItem("cycleTestReportID", this.testReportID)

          this.getCycleById({id: this.testReportID})
        }
      })
    },
    // 循环报告-根据id查询报告任务
    getCycleById(parameter) {
      getCycleById(parameter).then(res => {
        let json = jsonBigint({storeAsString: true})
        this.cycleReportParam = json.parse(res.data.queryParam)
        // 初始化rpt参数表头
        this.changeRptParamColumns(true)

        // 若填了ETP参数，则默认展开
        if (this.cycleReportParam.etpType === 'dch' || this.cycleReportParam.etpType === 'chAndDch') {
          this.isEtpParamClose = false
        }

        // 测试数据选择赋值
        this.selectedRowKeys = []
        this.selectedRows = []
        if (Array.isArray(this.cycleReportParam.orderDataList)) {
          this.cycleReportParam.orderDataList.forEach(item => {
            this.selectedRowKeys.push(item.uuid)
            this.selectedRows.push(item)
          })
        }

        this.primaryGroupChange()
      })
    },

    addParam(targetList) {
      if (targetList === 'rptStepParamList') {
        let dcirStepParamList = []
        for(let i = 0; i < this.dcirNum; i++) {
          dcirStepParamList.push({})
        }
        this.cycleReportParam[targetList].push({ dcirStepParamList:dcirStepParamList })
      } else {
        this.cycleReportParam[targetList].push({})
      }

      // 实时保存
      this.cycleTestReportTask(this.cycleReportParam, this.testReportID)
    },
    deleteParam(targetList, index) {
      this.cycleReportParam[targetList].splice(index, 1)

      // 实时保存
      this.cycleTestReportTask(this.cycleReportParam, this.testReportID)
    },
    handleNumberBlur(target = null) {
      if (target && this.cycleReportParam[target]) {
        this.cycleReportParam[target] = Number.parseFloat(this.cycleReportParam[target].replace(/\.+/g, '.').replace(/^\.|\.$/g, ''))
      }

      this.$forceUpdate()

      // 实时保存
      this.cycleTestReportTask(this.cycleReportParam, this.testReportID)
    },
    handleBlur(targetList = null, index = null, key = null, record = null) {
      if (targetList && typeof index == 'number' && ['ceStep', 'chCeStep'].includes(key)) {
        this.cycleReportParam[targetList][index][key] = this.cycleReportParam[targetList][index][key] === undefined ? '' : (this.cycleReportParam[targetList][index][key] + '').replaceAll(/,+/g, ',').replaceAll(/^,|,$/g, '')
        this.cycleReportParam[targetList][index][`${[key]}List`] = this.cycleReportParam[targetList][index][key] !== '' ? this.cycleReportParam[targetList][index][key].split(',') : []
      }

      // 解决无法感知列表参数变化的问题
      if (typeof index == 'number' && record !== null) {
        this.$set(this.cycleReportParam[targetList], index, record)
      }

      this.$forceUpdate()

      // 实时保存
      this.cycleTestReportTask(this.cycleReportParam, this.testReportID)
    },
    etpTypeChange(event) {
      this.cycleReportParam.etpType = event.target.value

      // 实时保存
      this.cycleTestReportTask(this.cycleReportParam, this.testReportID)
    },
    cleanEtpParam() {
      if (!this.cycleReportParam) {
        return
      }

      this.cycleReportParam.etpType = ''
      let cycleStepParamList = this.cycleReportParam.cycleStepParamList
      if (Array.isArray(cycleStepParamList) && cycleStepParamList.length > 0) {
        cycleStepParamList.forEach(item => {
          delete item.etpStartStep
          delete item.etpEndStep
        })
      }
      let rptStepParamList = this.cycleReportParam.rptStepParamList
      if (Array.isArray(rptStepParamList) && rptStepParamList.length > 0) {
        rptStepParamList.forEach(item => {
          delete item.etpEndStep
        })
      }

      // 实时保存
      this.cycleTestReportTask(this.cycleReportParam, this.testReportID)
    },
    formatter(value) {
      // 格式化显示的值，去除小数部分
      if (typeof value === 'number') {
        return Math.floor(value);
      }
      return value;
    },
    parser(value) {
      // 解析用户输入的值，确保只接受整数
      const parsedValue = parseInt(value, 10);
      return isNaN(parsedValue) ? 0 : parsedValue;
    },
    computeCycles() {
      if (!Array.isArray(this.cycleReportParam.rptCycComList) || this.cycleReportParam.rptCycComList.length === 0) {
        return this.$message.warn('请填写数据')
      }

      if (this.cycleReportParam.rptCycComList[0].cycStart != 0) {
        return this.$message.warn('请填写正确数据，中检天数须由0开始！')
      }

      //校验
      for (let i = 0; i < this.cycleReportParam.rptCycComList.length; i++) {
        let row = this.cycleReportParam.rptCycComList[i]

        if (typeof row.cycStart !== 'number' || typeof row.cycEnd !== 'number' || typeof row.cycInterval !== 'number') {
          return this.$message.warn('请填写完整数据')
        }

        if ( (row.cycEnd - row.cycStart) != row.cycInterval && (row.cycEnd - row.cycStart) % row.cycInterval != 0 ) {
          return this.$message.warn('请填写正确数据，开始与结束的差值需为间隔天数的倍数')
        }

        if (i + 1 < this.cycleReportParam.rptCycComList.length && row.cycEnd != this.cycleReportParam.rptCycComList[i + 1].cycStart) {
          return this.$message.warn('请填写正确数据，开始中检天数请与上一阶段的结束中检天数对应')
        }
      }

      // 计算
      for (let i = 0; i < this.cycleReportParam.rptCycComList.length; i++) {
        let row = this.cycleReportParam.rptCycComList[i]

        let cycleList = [row.cycStart]
        let sum = row.cycStart + row.cycInterval
        while (sum < row.cycEnd) {
          cycleList.push(sum)
          sum += row.cycInterval
        }

        if (i == this.cycleReportParam.rptCycComList.length - 1) {
          if (row.cycStart != row.cycEnd) {
            cycleList.push(row.cycEnd)
          }
        }

        this.$set(this.cycleReportParam.rptCycComList[i], 'cycleList', cycleList)
        this.$set(this.cycleReportParam.rptCycComList[i], 'cycleNum', cycleList.length)
      }

      // 赋值给循环报告参数
      this.refreshRptCycleList()

    },
    refreshRptCycleList() {
      this.cycleReportParam.rptCycleList = []
      for (let i = 0; i < this.cycleReportParam.rptCycComList.length; i++) {
        let row = this.cycleReportParam.rptCycComList[i]
        this.cycleReportParam.rptCycleList.push(
          ...row.cycleList.map((item, index) => {
            return {cycle: item}
          })
        )
      }

      // 实时保存
      this.cycleTestReportTask(this.cycleReportParam, this.testReportID)
    },

    handleAllOk() {
      const temList = this._handleVerify(this.cycleReportParam)
      if (!temList[0]) {
        // 如果校验不通过
        return this.$message.warn(temList[1])
      } else {
        return this.isShowReportName = true
      }
    },

    // 完成模型搭建事件
    exportData() {
      if (!this.cycleReportParam.reportName) return this.$message.warn("请正确填写循环建模任务名称")

      let json = jsonBigint({storeAsString: true})
      const params = json.parse(JSON.stringify(this.cycleReportParam))

      commitCycle(params, this.testReportID).then(res => {
        if (res.success) {
          this.$message.success("创建成功")
          // this.$router.push("/v_report_online_manager?type=Cycle")
          this.$router.push("/v_report_online_manager?type=DongLi")
        } else {
          this.$message.warn(res.message)
        }
      })
    },

    // 校验
    _handleVerify(data) {
      // ---------------------- 校验：测试数据选择 ------------------------
      let orderDataList = data.orderDataList
      if (!Array.isArray(orderDataList) || orderDataList.length === 0) {
        return [false, '请选择测试数据']
      }

      // ---------------------- 校验：标题信息填写 ------------------------
      if (!data.projectName) {
        return [false, '请填写项目名称']
      }
      if (!data.phase) {
        return [false, '请填写样品阶段']
      }
      if (!data.temp && data.temp !== 0) {
        return [false, '请填写温度']
      }
      if (!data.rate && data.rate !== 0) {
        return [false, '请填写循环机制']
      }
      if (!data.startSoc && data.startSoc !== 0) {
        return [false, '请填写起始SOC']
      }
      if (!data.endSoc && data.endSoc !== 0) {
        return [false, '请填写结束SOC']
      }

      // ---------------------- 校验：Cycle工步参数填写 ------------------------
      let cycleStepParamList = data.cycleStepParamList
      if (!Array.isArray(cycleStepParamList) || cycleStepParamList.length === 0) {
        return [false, '请填写Cycle参数']
      }

      // 若所有行的groupIndex均不为空，则必须包含所有组别
      const cycGroupIndexList = cycleStepParamList.map(item => item.groupIndex)
      if (!cycGroupIndexList.includes(undefined)) {
        const listSet = new Set(cycGroupIndexList)
        for (let i = 0; i < this.primaryGroupOptions.length; i++) {
          if (!listSet.has(i)) {
            return [false, '请填写Cycle参数-第 ' + (i+1) + ' 组电芯的工步参数']
          }
        }
      }

      for(let i = 0; i < cycleStepParamList.length; i++) {
        if (!cycleStepParamList[i].ceStep) {
          return [false, '请填写Cycle参数-第 ' + (i+1) + ' 行放电容量&能量工步号']
        }
      }

      // ---------------------- 校验：RPT参数填写 ------------------------
      let rptCycleList = data.rptCycleList
      if (!Array.isArray(rptCycleList) || rptCycleList.length === 0) {
        return [false, '请填写RPT循环圈数']
      }

      let rptStepParamList = data.rptStepParamList
      if (!Array.isArray(rptStepParamList) || rptStepParamList.length === 0) {
        return [false, '请填写RPT工步信息']
      }

      // 若所有行的groupIndex均不为空，则必须包含所有组别
      const rptGroupIndexList = rptStepParamList.map(item => item.groupIndex)
      if (!rptGroupIndexList.includes(undefined)) {
        const listSet = new Set(rptGroupIndexList)
        for (let i = 0; i < this.primaryGroupOptions.length; i++) {
          if (!listSet.has(i)) {
            return [false, '请填写Cycle参数-第 ' + (i+1) + ' 组电芯的工步参数']
          }
        }
      }

      // rptStepParamList校验
      let isDcirParamFull = false // DCIR参数组校验：任意一行的DCIR参数列表完整即可
      for(let i = 0; i < rptStepParamList.length; i++) {
        let row = rptStepParamList[i]
        if (!row.ceStep) {
          return [false, '请填写RPT工步信息-第 ' + (i+1) + ' 行容量&能量工步号']
        }

        if (row.dcirStepParamList.every(item => item.restStep && item.dchStep)) {
          isDcirParamFull = true
        }
      }
      if (!isDcirParamFull) {
        return [false, '请填写完整任意一行RPT的DCIR参数列表']
      }

      // ---------------------- 校验：DCIR参数组标题填写 ------------------------
      let dcirTiTleList = data.dcirTiTleList
      if (!Array.isArray(dcirTiTleList) || dcirTiTleList.length === 0) {
        return [false, '请填写RPT工步信息-DCIR参数组标题']
      }

      for (let i = 0; i < dcirTiTleList.length; i++) {
        if (!dcirTiTleList[i]) {
          return [false, '请填写RPT工步信息-第 ' + (i+1) + ' 组DCIR参数组标题']
        }
      }

      // ---------------------- 校验：ETP参数填写：选择了ETP类型，才能填写ETP参数；选择了ETP类型则必须填写参数列表 ------------------------
      let etpType = data.etpType
      if (etpType === 'dch' || etpType === 'chAndDch') {
        for (let i = 0; i < cycleStepParamList.length; i++) {
          if (!cycleStepParamList[i].etpStartStep || !cycleStepParamList[i].etpEndStep) {
            return [false, '请填写完整Cycle的ETP参数']
          }
        }
        for (let i = 0; i < rptStepParamList.length; i++) {
          if (!rptStepParamList[i].etpEndStep) {
            return [false, '请填写完整RPT的ETP参数']
          }
        }
      }

      return [true, '']
    }

  }
}
</script>

<style lang="less" scoped>
// 通用
.mt5 {
  margin-top: 5px;
}

.mr5 {
  margin-right: 5px;
}

.mt10 {
  margin-top: 10px;
}

.ml10 {
  margin-left: 10px;
}

.mr10 {
  margin-right: 10px;
}

.fs14 {
  font-size: 14px;
}

.wrapper {
  height: calc(100vh - 40px);
  padding: 0 10px 10px;
  background-color: #f0f2f5;
}

/* 标题 */
.head_title {
  color: #333;
  padding: 10px 0;
  font-size: 20px;
  font-weight: 600;
}

.head_title::before {
  width: 8px;
  background: #1890ff;
  margin-right: 8px;
  content: "\00a0"; //填充空格
}

.head_title .subheading {
  font-size: 14px;
  font-weight: 400;
}

h3 {
  font-size: 15px;
  font-weight: bold;
  padding: 0;
  margin: 0;
}

.flex-sb-center-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.all-wrapper {
  height: calc(100vh - 40px - 50px);
  padding: 0 0 10px;
  display: flex;
  justify-content: space-between;
}

.btn-wrap {
  display: flex;
}

.normal-btn {
  padding: 5px 10px;
  color: #fff;
  background-color: #1890ff;
  letter-spacing: 2px;
  cursor: pointer;
}

.left-content {
  height: calc(100vh - 40px - 50px);
  width: 50%;
}

/deep/ .left-content .ant-table-body {
  min-height: calc(100vh - 40px - 50px - 20px - 20px - 32px - 32px);
  overflow: auto;
}

.right-content {
  width: 50%;
}

/deep/ .right-content .ant-table-body {
  min-height: 60px;
  overflow: auto;
}

.block {
  height: fit-content;
  padding: 10px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
  position: relative;
}

/deep/ .ant-table-thead > tr > th {
  padding: 5px !important;
  font-size: 13px !important;
}

/deep/ .ant-table-tbody > tr > td {
  padding: 0px !important;
  height: 24px !important;
  font-size: 12px !important;
}

/deep/ .all-wrapper .ant-table-placeholder {
  border: none !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 0;
}

/deep/ .all-wrapper .ant-empty-normal {
  margin: -2px 0;
}

/deep/ .ant-empty-image {
  display: none;
}

/deep/ .ant-table-row-expand-icon {
  margin-right: 0px;
}

/deep/ .ant-table-footer {
  padding: 0;
}

.footer-btn {
  width: 100%;
  height: 32px;
  border: 1px solid #e8e8e8;
  background: #fff;
  color: #999;
  font-size: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.footer-btn:hover {
  color: #1890ff;
}

/deep/ .ant-form-item {
  margin-bottom: 0;
}

/deep/ .flex-sb-center-row .ant-input-number {
  width: 130px;
}

.input {
  width: 100%;
  text-align: center;
}

/deep/ #cycComputeTable .ant-input-number .ant-input-number-input {
  text-align: center;
}

.info-row-div {
  display: flex;
  flex-wrap: wrap;
}

.label-span {
  width: 80px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-right: 10px;
  margin-top: 10px;
}

.number-input {
  width: 100px;
  text-align: left;
  margin-top: 10px;
}
</style>