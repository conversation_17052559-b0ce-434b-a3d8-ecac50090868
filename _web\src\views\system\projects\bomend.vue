<template>
  <a-modal :title="title" :width="1250" :visible="visible" @cancel="handleCancel">
    <template slot="footer">
                <a-button key="back" @click="handleCancel">
                  关闭
                </a-button>
                    </template>
                                <div class="main_modal">
                                    <div class="left_main_modal" >
                    <template>
                      <a-button v-if="!openStatus" type="primary" @click="showDrawer" style="margin-bottom:8px;margin-right:8px" icon="menu-fold">
                        选择包装BOM
                      </a-button>
                      <a-button v-else type="primary" @click="showDrawer" style="margin-bottom:8px;margin-right:8px" icon="menu-unfold">
                        取消选择
                      </a-button>
                    </template>
                    <a-spin :spinning="vloading">
                        <a-table
                            row-key="index"
                            size="small"
                            :columns="treeData.columns"
                            :dataSource="treeData.lists"
                            :showPagination="false"
                            :rowKey="(record) => record.id"
                            :pagination="false"
                        >
                        <span slot="bomCode" slot-scope="text,record">
                                  <a v-if="!record.bomCode" @click="$refs.bomupdate.edit(record)">设置成品代码</a>
                                  <label v-else>{{record.bomCode}}</label>
                        </span>
                        <span slot="bomRelateStatus" slot-scope="text, record">
                          {{mapEndStatus[record.bomRelateStatus]}}
                        </span>
                        <template slot="bomLines" slot-scope="text, record">
                            <label v-if="record.bomLines != '[]'">
                              <label v-for="(item, i) in JSON.parse(record.bomLines)" :key="i">
                                <span v-if="i < JSON.parse(record.bomLines).length -1">
                                  {{dataLines[item]}}，
                                </span>
                                <span v-else>
                                  {{dataLines[item]}}
											        </span>
                              </label>
                            </label>
                            <a v-else-if="record.bomRelateStatus == 2 || record.bomRelateStatus == 0 || record.bomRelateStatus == 4" @click="showLine(record)" >设置产线</a>
                          </template>
                        <template slot="bomSapVersion" slot-scope="text, record">
                          <template v-if="record.bomSapVersion">
                                                          <div  v-for="(item,i) in JSON.parse(record.bomSapVersion)" :key="i">
                                                              {{i}}-{{item}}
                                                          </div>
                        </template>
                                                </template>
                        <template slot="action" slot-scope="text, record">
                          <a-dropdown>
                            <a class="ant-dropdown-link">
                              更多<a-icon type="down" />
                            </a>
                            <a-menu v-if="record.bomRelateStatus == 0 || record.bomRelateStatus == 4" slot="overlay">
                              
                              <a-menu-item>
                                <a @click="callBomEndRelate(record)" >提交</a>
                              </a-menu-item>
                              <a-menu-item>
                                <a @click="showsapimport(record)" >从sap导入</a>
                              </a-menu-item>
                              <a-menu-item>
                                <a v-if="record.bomLines != '[]'" @click="showLine(record)" >添加产线</a>
                                <a v-else @click="showLine(record)" >设置产线</a>
                              </a-menu-item>
                              <a-menu-item>
                                <a @click="showDelLine(record)" >删除产线</a>
                              </a-menu-item>
                              <a-menu-item>
                                <a @click="showHistory(record)" >审核记录</a>
                              </a-menu-item>
                              <a-menu-item>
                                <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => callDelBomEnd(record)">
                                  <a>删除</a>
                                </a-popconfirm>
                              </a-menu-item>
                            </a-menu>
                            <a-menu v-if="record.bomRelateStatus == 2" slot="overlay">
                              <a-menu-item>
                                <a v-if="record.bomLines != '[]'" @click="showLine(record)" >添加产线</a>
                                <a v-else @click="showLine(record)" >设置产线</a>
                              </a-menu-item>
                              <a-menu-item>
                                <a @click="showDelLine(record)" >删除产线</a>
                              </a-menu-item>
                              <a-menu-item>
                                <a @click="showHistory(record)" >审核记录</a>
                              </a-menu-item>
                            </a-menu>
                            <a-menu v-if="record.bomRelateStatus == 3 || record.bomRelateStatus == 7" slot="overlay">
                              <!-- <a-menu-item>
                                <a @click="callBomEndRelate(record)" >重试</a>
                              </a-menu-item> -->
                              <a-menu-item>
                                <a @click="callBomEndError(record)" >查看</a>
                              </a-menu-item>
                              <a-menu-item>
                                <a @click="showHistory(record)" >审核记录</a>
                              </a-menu-item>
                            </a-menu>
                             <a-menu v-if="record.bomRelateStatus == 1 || record.bomRelateStatus == 5 || record.bomRelateStatus == 6" slot="overlay">
                              <a-menu-item>
                                <a @click="showHistory(record)" >审核记录</a>
                              </a-menu-item>
                            </a-menu>
                          </a-dropdown>
                        </template>
                    </a-table>
                </a-spin>

            </div>
            <div
                :class="{ right_main_show: !openStatus }"
                class="right_main_modal"
            >
                    <div>
                        <div slot="content" class="table-page-search-wrapper">
                            <a-form layout="inline">
                                <a-row :gutter="24">
                                    <a-col :md="8" :sm="24">
                                        <a-form-item>
                                            <a-input v-model="queryParam.bomNo" @pressEnter="$refs.table.refresh(true)" allow-clear placeholder="请输入编号"/>
                                        </a-form-item>
                                    </a-col>
                                    <a-col :md="8" :sm="24">
                                        <a-form-item>
                                            <a-input v-model="queryParam.bomName" @pressEnter="$refs.table.refresh(true)" allow-clear placeholder="请输入文件名"/>
                                        </a-form-item>
                                    </a-col>
                                    <a-col :md="8" :sm="24">
                                        <a-form-item>
                                            <span class="table-page-search-submitButtons">
                                                <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                                                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                                            </span>
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                            </a-form>
                        </div>
                    </div>
                    <div>
                        <a-spin :spinning="loading">
                            <s-table
                                ref="table"
                                :rowKey="(record) => record.id"
                                :columns="columns"
                                :data="loadData"
                                >
                                <span slot="action" slot-scope="text, record">
                                    <a v-if="selectIds.indexOf(record.id) < 0" @click="selectBom(record)">选择</a>
                                    <a style="color:rgb(165 165 165)" v-else >已选择</a>
                                    <a-divider type="vertical" />
                                    <a @click="viewBom(record)">查看</a>
                                </span>
                                <span slot="bomStatus" slot-scope="text, record">
                                    {{mapStatus[record.bomStatus]}}
                                </span>
                            </s-table>
                        </a-spin>
                    </div>
            </div>
        </div>
        <checkhistory2 ref="checkhistory2" @ok="handleOk" @preview1="preview1" />
        <bomupdate  ref="bomupdate" @ok="handleOk" /><!-- :parts="parts" -->
        
        <a-modal :title="addWerkTitle" :width="600" :visible="lineModalVisible" @ok="ok" @cancel="cancel">
          <div>
            <p style="margin:0">请选择成品BOM适用产线 ({{endBomVersoin}}):</p>
            <a-checkbox-group v-model="checkedList" :options="options" />
          </div>
          <div>
            <p style="margin:0;margin-top:10px">包装BOM适用产线:</p>
            <span v-for="(item,i) in packLineIds" :key="i">{{dataLines[item]}}&nbsp;&nbsp;&nbsp;&nbsp;</span>
          </div>
        </a-modal>


        <a-modal title="失败原因" :width="400" :visible="errorsVisible" @cancel="handelcancel">
          <template slot="footer">
            <a-button key="back" @click="handelcancel">
                  关闭
            </a-button>
          </template>
          <p>{{errors}}</p>
        </a-modal>

        <a-modal title="查看BOM" :width="1200" :visible="bomVisible" @cancel="handelcancel">
          <template slot="footer">
            <a-button key="back" @click="handelcancel">
                  关闭
            </a-button>
          </template>
          <dragTreeTable
              :data='treeList'
					    id="dtreess" :isdraggable="false" style="width:100%;margin:0">
              <template #version="{row}">
                <template v-if="row.version">
                  <div  v-for="(item,i) in JSON.parse(row.version)" :key="i">
                    {{i}}-{{item}}
                  </div>
              </template>
              </template>
          </dragTreeTable>
        </a-modal>


        <a-modal title="删除产线" :width="600" :visible="dellineModalVisible" @ok="delok" @cancel="delcancel">
          <div>
            <p style="margin:0">请选择成品BOM适用产线 ({{endBomVersoin}}):</p>
            <a-checkbox-group v-model="delcheckedList" :options="deloptions" />
          </div>
        </a-modal>

        <a-modal title="输入sap版本" :width="200" :visible="sapvisible" @ok="sapok" @cancel="sapcancel">
          <div>
            <a-input v-model="sapverison" />
          </div>
        </a-modal>
        
  </a-modal>
  
</template>

<script>
  
  import {
    getBomPage,
    getBomEnd,
    bomEndSave,
    getBomEndError,
    bomEndRelate,
    bomEndSetLines,
    getBomPackLineIds,
    getBomLineIds,
    bomEndDelLines,
    endBomPreview,
    delBomEnd,
    sapImportBomEnd
  } from "@/api/modular/system/bomManage"
  import {
    getPartList
  } from "@/api/modular/system/partManage"
  import bomupdate from './bomupdate'
  import checkhistory2 from "./checkhistory2"
  import checkhistory from './checkhistory'
  import {
    STable
  } from '@/components'
   import dragTreeTable from "drag-tree-table";
   
  export default {
    components: {
      checkhistory2,
      checkhistory,
      STable,
      bomupdate,
      dragTreeTable
    },
    props: {
      dataLines: {
        type: Object,
        default: {}
      },
    },
    data() {
      return {
        sapverison:'',
        sapvisible:false,
        addWerkTitle:'',
        endBomVersoin:'',
        visible1: false,
        id: 0,
        pdfUrl: '',
        lineflag:0,
        packLineIds:[],
        checkedList: [],
        delcheckedList: [],
        options: [],
        deloptions:[],
        //parts: [],
        selectIds: [],
        mapEndStatus: ['编辑中', '审核中', '已审核', '失败中', '被驳回','新增工厂申请','删除工厂申请','废弃'],
        mapStatus: ['编辑中', '审核中', '已审核', '失败中', '被驳回'],
        title: '',
        loading: false,
        vloading: false,
        visible: false,
        lineModalVisible: false,
        dellineModalVisible:false,
        errorsVisible: false,
        bomVisible:false,
        openStatus: false,
        row: {},
        errors: '',
        loadData: parameter => {
          parameter = { ...parameter,
            ...{
              bomIssueId: this.row.bomIssueId
            }
          }
          return getBomPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        queryParam: {},
        columns: [{
            title: '操作',
            dataIndex: 'action',
            scopedSlots: {
              customRender: 'action'
            }
          },
          {
            title: '包装BOM',
            dataIndex: 'bomStatus',
            scopedSlots: {
              customRender: 'bomStatus'
            }
          },
          {
            title: 'BOM编号',
            dataIndex: 'bomNo',
            width: 200
          },
          {
            title: 'BOM版本',
            dataIndex: 'bomVersion'
          }
        ],
        treeData: {
          row: {},
          lists: [],
          columns: [{
              title: '成品代码',
              dataIndex: 'bomCode',
              scopedSlots: {
                customRender: 'bomCode'
              }
            },
            {
              title: '成品物料',
              dataIndex: 'bomPartName',
              width: 200
            },
            {
              title: 'sap版本',
              dataIndex: 'bomSapVersion',
              scopedSlots: {
                customRender: 'bomSapVersion'
              }
            },
            {
              title: '产线',
              dataIndex: 'bomLines',
              scopedSlots: {
                customRender: 'bomLines'
              }
            },
            {
              title: '关联状态',
              dataIndex: 'bomRelateStatus',
              scopedSlots: {
                customRender: 'bomRelateStatus'
              }
            },
            {
              title: '操作',
              dataIndex: 'action',
              scopedSlots: {
                customRender: 'action'
              }
            },
          ]
        },
        treeList: {
          columns: [{
              type: 'selection',
              field: 'id',
              title: '物料名称',
              width: 350,
              align: 'left',
              formatter: (item) => {
                return '<a id=' + item.id + ' name=' + item.partName + '>' + item.partName + '</a>'
              }
            },
            {
              field: 'sapNumber',
              title: '物料代码',
              width: 150,
              align: 'center',
            },
            {
              field: 'partDescription',
              title: '物料规格',
              width: 400,
              align: 'left'
            },
            {
              field: 'partUnit',
              title: '单位',
              width: 110,
              align: 'center'
            },
            {
              field: 'partUse',
              title: '理论用量(B0)',
              width: 168,
              align: 'center'
            },
            {
              field: 'partLoss',
              title: '设计损耗(B1)',
              width: 168,
              align: 'center'
            },
            {
              type: 'version',
              field: 'version',
              title: '工厂版本号',
              width: 150,
              align: 'center'
            },
            {
              field: 'posnr',
              title: '行号',
              width: 100,
              align: 'center'
            },
            {
              field: 'desc',
              title: '备注',
              width: 150,
              align: 'center'
            },
            
          ],
          lists: []
        }
      }
    },
    methods: {

      showsapimport(record){
        this.id = record.id
        this.sapvisible = true
      },
      sapcancel(){
        this.id = 0
        this.sapverison = ''
        this.sapvisible = false
      },
      sapok(){
        this.vloading = true
        sapImportBomEnd({
            id: this.id,
            sapVersion: this.sapverison
          })
          .then((res) => {
            if (res.success) {
              let index = this.treeData.lists.findIndex(item => item.id == this.id)
              this.treeData.lists[index].bomRelateStatus = 6
              this.$message.info('已导入', 1);
              this.sapcancel()
            } else {
              this.$message.error(res.message, 1);
            }
            this.vloading = false
          })
          .catch((err) => {
            this.vloading = false
            this.$message.error('错误提示：' + err.message, 1)
          });
      },


      onClose3(arg) {
        this.vloading = arg;
      },
      preview1(id) {

        this.vloading = true
        endBomPreview({
          id: id
        }).then((res) => {
          if (res.success) {
            this.$parent.previewPdf(res.data.fileId)
          }
        }).finally((res) => {
          this.vloading = false
        })
      },
      showHistory(record){
        this.$refs.checkhistory2.edit(record)
      },
      viewBom(record){
        this.treeList.lists = JSON.parse(record.bomData)
        this.bomVisible = true
      },
      showLine(record) {
        this.id = record.id
        this.lineModalVisible = true
        this.addWerkTitle = record.bomLines == '[]' ? '设置产线' : '添加产线'
        this.callGetBomPackLineIds(record)
      },

      showDelLine(record){
        this.id = record.id
        this.dellineModalVisible = true
        this.callGetBomLineIds(record)
      },
      delcancel(){
        this.id = 0
        this.delcheckedList = []
        this.deloptions = []
        this.dellineModalVisible = false
      },
      delok(){
        this.vloading = true
        bomEndDelLines({
            id: this.id,
            bomLines: JSON.stringify(this.delcheckedList)
          })
          .then((res) => {
            if (res.success) {
              let index = this.treeData.lists.findIndex(item => item.id == this.id)
              let arr = JSON.parse(this.treeData.lists[index].bomLines)
              let newArr = arr.filter((x) => !this.delcheckedList.some((item) => x == item));
              this.treeData.lists[index].bomLines = JSON.stringify(newArr)
              if (this.lineflag == 1) {
                this.treeData.lists[index].bomRelateStatus = 6
              }
              this.$message.info('已删除产线', 1);
              this.delcancel()
            } else {
              this.$message.error(res.message, 1);
            }
            this.vloading = false
          })
          .catch((err) => {
            this.vloading = false
            this.$message.error('错误提示：' + err.message, 1)
          });
      },
      ok() {
        if (this.checkedList.length <=0 ) {
          this.$message.error('请选择产线')
          return false
        }
        this.vloading = true
        bomEndSetLines({
            id: this.id,
            bomLines: JSON.stringify(this.checkedList)
          })
          .then((res) => {
            if (res.success) {
              let index = this.treeData.lists.findIndex(item => item.id == this.id)
              let bomlines = JSON.parse(this.treeData.lists[index].bomLines)
              bomlines = [...bomlines,...this.checkedList]
              this.treeData.lists[index].bomLines = JSON.stringify(bomlines)
              if (this.lineflag == 1) {
                this.treeData.lists[index].bomRelateStatus = 5
              }
              this.$message.info('已添加产线', 1);
              this.cancel()
            } else {
              this.$message.error(res.message, 1);
            }
            this.vloading = false
          })
          .catch((err) => {
            this.vloading = false
            this.$message.error('错误提示：' + err.message, 1)
          });
      },
      callPartList() {
        this.vloading = true
        getPartList({
            flag: 1
          })
          .then((res) => {
            if (res.success) {
              this.parts = res.data
            } else {
              this.$message.error(res.message, 1);
            }
            this.vloading = false
          })
          .catch((err) => {
            this.vloading = false
            this.$message.error('错误提示：' + err.message, 1)
          });
      },
      handelcancel() {
        this.errors = ''
        this.errorsVisible = false
        this.bomVisible = false
      },
      handleOk() {},
      equal(a, b) {
        const floatEpsilon = Math.pow(2, -23)
        return Math.abs(a - b) <= floatEpsilon * Math.max(Math.abs(a), Math.abs(b));
      },
      edit(record) {
        this.record = record
        this.row = record
        this.visible = true
        this.title = (record.bomNo ? record.bomNo : '') + '电芯BOM-关联包装BOM'
        setTimeout(() => {
          this.$refs.table.refresh(true)
        }, 1000);
        this.callBomEnd(record)
        this.callPartList()
        console.log(this.dataLines)
      },
      callGetBomLineIds(record) {
        getBomLineIds({
            id: record.id
          })
          .then((res) => {
            if (res.success) {
              this.lineflag = res.data.lineflag
              this.endBomVersoin = res.data.endBomVersoin
              for (const _item of res.data.bomlines) {
                this.deloptions.push({
                  label: this.dataLines[_item],
                  value: _item
                })
              }
            } else {
              this.$message.error(res.message, 1);
            }
          })
          .catch((err) => {
            this.$message.error('错误提示：' + err.message, 1)
          });
      },
      callGetBomPackLineIds(record) {
        //this.vloading = true
        getBomPackLineIds({
            id: record.id
          })
          .then((res) => {
            if (res.success) {
              this.lineflag = res.data.lineflag
              this.packLineIds = res.data.packlines
              this.endBomVersoin = res.data.endBomVersoin
              for (const _item of res.data.bomlines) {
                this.options.push({
                  label: this.dataLines[_item],
                  value: _item
                })
              } 
            } else {
              this.$message.error(res.message, 1);
            }
            //this.vloading = false
          })
          .catch((err) => {
            this.$message.error('错误提示：' + err.message, 1)
          });
      },
      showDrawer() {
        this.openStatus = !this.openStatus
      },
      handleCancel() {
        this.row = {}
        this.options = []
        this.visible = false
      },
      cancel() {
        this.id = 0
        this.options = []
        this.checkedList = []
        //this.selectedList = []
        this.lineModalVisible = false
      },
      callBomEnd(record) {
        this.vloading = true
        getBomEnd({
            bomId: record.id
          })
          .then((res) => {
            if (res.success) {
              this.selectIds = Array.from(res.data, ({
                bomPackId
              }) => bomPackId)
              this.treeData.lists = res.data
            } else {
              this.$message.error(res.message, 1);
            }
            this.vloading = false
          })
          .catch((err) => {
            this.vloading = false
            this.$message.error('错误提示：' + err.message, 1)
          });
      },
      callBomEndError(record) {
        this.vloading = true
        getBomEndError({
            id: record.id
          })
          .then((res) => {
            if (res.success) {
              this.errors = res.data.erros
              this.errorsVisible = true
            } else {
              this.$message.error(res.message, 1);
            }
            this.vloading = false
          })
          .catch((err) => {
            this.vloading = false
            this.$message.error('错误提示：' + err.message, 1)
          });
      },
      selectBom(record) {
        let index = this.treeData.lists.findIndex(item => item.bomPackId == record.id)
        if (index > -1) {
          this.$message.error('不能选择已存在的BOM')
          return false;
        }
        let bomEnd = {
          id: null,
          bomId: this.row.id,
          bomPackId: record.id,
          bomRelateStatus: 0,
          bomLines: '[]',
          bomPartName: '',
          bomCode: '',
          bomIssueId: this.row.bomIssueId
        }
        this.callBomEndSave(bomEnd)
      },
      callBomEndSave(params) {
        this.vloading = true
        bomEndSave(params)
          .then((res) => {
            if (res.success) {
              if (params.id == null) {
                this.record.packIds.push(params.bomPackId)
                params.id = res.data
                this.selectIds.push(params.bomPackId)
                this.treeData.lists.push(params)
              }
            } else {
              this.$message.error(res.message, 1);
            }
            this.vloading = false
          })
          .catch((err) => {
            this.vloading = false
            this.$message.error('错误提示：' + err.message, 1)
          });
      },
      callBomEndRelate(record) {
        this.vloading = true
        bomEndRelate({
          id: record.id
        }).then((res) => {
          if (res.success) {
            let index = this.treeData.lists.findIndex(item => item.id == record.id)
            this.treeData.lists[index].bomRelateStatus = 1
            this.$message.success('关联成功')
          } else {
            if (res.code == 505) {
              let index = this.treeData.lists.findIndex(item => item.id == record.id)
              this.treeData.lists[index].bomRelateStatus = 3
            }
            this.$message.error('关联失败：' + res.message)
          }
          this.vloading = false
        }).finally((res) => {
          this.vloading = false
        })
      },
      callDelBomEnd(record){
        this.vloading = true
        delBomEnd({
          id: record.id
        }).then((res) => {
          if (res.success) {
            let index = this.treeData.lists.findIndex(item => item.id == record.id)
            let _index = this.selectIds.findIndex(item => item == record.bomPackId)
            let $index = this.record.packIds.findIndex(item => item == record.bomPackId)
            this.treeData.lists.splice(index, 1)
            this.selectIds.splice(_index,1)
            this.record.packIds.splice($index,1)
            this.$message.success('删除成功')
          } else {
            this.$message.error('删除失败：' + res.message)
          }
          this.vloading = false
        }).finally((res) => {
          this.vloading = false
        })
      },
    },
    created() {}
  }
</script>

<style lang="less" scoped=''>
  .main_modal {
    display: flex;
    width: 100%;
  }
  .left_main_modal {
    flex: 1;
    overflow: auto;
  }
  .right_main_modal {
    margin-left: 12px;
    width: 480px;
    display: block;
    overflow: auto;
    transition: width 0.5s;
  }
  .right_main_show {
    width: 0;
    margin: 0;
  }
  .right_main_show * {
    display: none;
  }
  /deep/.table-page-search-wrapper .ant-form-inline .ant-form-item {
    margin-bottom: 14px;
  }
  /deep/.table-page-search-wrapper .ant-form-inline .ant-form-item .ant-form-item-control {
    height: auto;
  }/deep/.tree-column{
		padding: 2px 0 !important;
	}/deep/.tree-row{
		line-height: initial !important;
	}/deep/.drag-tree-table-header{
    height: initial !important;
  }
  /deep/.drag-tree-table-header {
		background: #fafafa;
		border-bottom: 1px solid #e8e8e8;
    line-height: inherit;
	}
  /deep/.drag-tree-table {
		margin: 0;
	}
  
</style>