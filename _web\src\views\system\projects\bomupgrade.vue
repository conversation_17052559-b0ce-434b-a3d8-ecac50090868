<template>
  <a-modal :title="(record.bomNo == null || (record.bomVersion == 'A' && record.jiraIdStatus == 0)) ?'请确认是否需要生成BOM文件？': '更改'" :width="500" :visible="visible" :confirmLoading="confirmLoading" @ok="handleSubmit" @cancel="handleCancel" style="text-align: center">
    <a-drawer :bodyStyle="{ height: '100%' }" placement="right" :closable="false" width="80%" :visible="visible2" @close="onClose1">
      <iframe :src="pdfUrl+'#view=FitH,top'" width="100%" height="100%"></iframe>
    </a-drawer>
    <a-form :form="form">
      <a-form-item style="display: none;" :labelCol="labelCol" :wrapperCol="wrapperCol">
        <a-input v-decorator="['id']" />
      </a-form-item>
      <a-form-item style="display: none;" :labelCol="labelCol" :wrapperCol="wrapperCol">
        <a-input v-decorator="['productState']" />
      </a-form-item>
      <a-form-item :label="(record.bomNo == null || (record.bomVersion == 'A' && record.jiraIdStatus == 0)) ?'备注':'更改原因'" :labelCol="labelCol" :wrapperCol="wrapperCol" v-if="confirmLoading1">
        <a-textarea :placeholder="placeholder" :auto-size="{ minRows: 3, maxRows: 5 }" v-decorator="[
                          'remark',
                          {
                              rules: [
                              { required: true, message: placeholder},
                              ],
                          },
                      ]" />
      </a-form-item>
    </a-form>
    <a v-if="!confirmLoading1" @click="preview()" style="margin-bottom: 8px;color: #0093fb;">{{fileName}}</a>
    <a-spin :spinning="confirmLoading" v-if="!confirmLoading1">
    </a-spin>
    <template slot="footer">
      <a-button key="back" @click="handleCancel">
        取消
      </a-button>
      <a-button key="submit" v-if="fileStatus1" type="primary" :loading="fileStatus2" @click="pdfUpdate1">
        生成文件
      </a-button>
      <a-button key="submit" v-if="!fileStatus1" type="primary" :loading="confirmLoading" @click="handleSubmit">
        提交
      </a-button>
    </template>


  </a-modal>
</template>

<script>
  import {
    sysBomCommit,
    pdfUpdateAndCode,
    getBom
  } from "@/api/modular/system/bomManage"
  export default {
    data() {
      return {
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 18
          }
        },
        visible: false,
        confirmLoading: false,
        id: 0,
        form: this.$form.createForm(this),
        confirmLoading1: false,
        visible2: false,
        fileStatus: false,
        fileStatus1: true,
        fileStatus2: false,
        placeholder: '',
        pdfUrl: '',
        remark: '',
        fileName:'',
        record:null
      }
    },
    created() {
    },
    methods: {
      preview() {
        this.visible2 = true;
      },
      onClose1() {
        this.visible2 = false;
      },
      edit(record) {
        this.confirmLoading = true


        this.id = record.id
        getBom({
          id: record.id
        }).then((res) => {
          this.record = res.data
          this.placeholder = this.record.bomNo == null || (this.record.bomVersion == 'A' && this.record.jiraIdStatus == 0) ?'请输入备注':'请输入更改原因'

          if(res.data.bomTransport == null && res.data.bomType == 1){
            this.$message.error("请先选择运输方式");
            return false;
          }
          if(JSON.parse(res.data.bomData)[0].lists.length < 1){
            this.$message.error("请先搭建bom");
            return false;
          }
          if (res.data.bomData.length < 3) {
            this.$message.error("请先搭建bom");


            return false;
          } else {
            this.confirmLoading1 = true
            this.visible = true
          }

        })
      },
      pdfUpdate(record) {
        pdfUpdateAndCode({
          id: record.id
        }).then((res) => {
          if (res.success) {
            this.confirmLoading1 = false
            this.confirmLoading = false
            this.pdfUrl = process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id=' + res.data
          } else {
            this.$message.error(res.message)
            this.handleCancel()
          }
        }).finally((res) => {
          //this.confirmLoading = false
        })
      },
      pdfUpdate1() {
        const {
          form: {
            validateFields
          }
        } = this
        validateFields((errors, values) => {
          if (!errors) {
            this.fileStatus = true
            this.fileStatus1 = true
            this.fileStatus2 = true
            let $params = { ...values,
              id: this.record.id
            }
            pdfUpdateAndCode($params).then((res) => {
              if (res.success) {
                this.confirmLoading1 = false
                this.confirmLoading = false
                this.fileStatus = false
                this.fileStatus1 = false
                this.pdfUrl = process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id=' + res.data.fileId
                this.fileName = res.data.fileName
              } else {
                this.$message.error(res.message)
                this.handleCancel()
              }
            }).finally((res) => {
              //this.confirmLoading = false
            })
          }
        })
      },
      handleSubmit() {
        const {
          form: {
            validateFields
          }
        } = this
        this.confirmLoading = true
        this.confirmLoading1 = false
        validateFields((errors, values) => {
          if (!errors) {
            let $params = { ...values,
              id: this.id
            }
            sysBomCommit($params).then((res) => {
              if (res.success) {
                this.record.bomStatus = 1
                this.$message.success('提交成功')
                this.visible = false
                this.confirmLoading = false
                this.$emit('ok', values)
                this.form.resetFields()

                this.$emit('updateVis');

              } else {
                this.$message.error('提交失败：' + res.message)
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel() {
        this.form.resetFields()
        this.visible = false
        this.fileStatus = false
        this.fileStatus1 = true
        this.fileStatus2 = false
      }
    }
  }
</script>
