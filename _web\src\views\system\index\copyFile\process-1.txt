<template>
    <ve-table :max-height="300" :fixed-header="true" :columns="columns" :table-data="tableData" />
</template>

<script>
    import _ from 'lodash'
    // name fiter list
    /* const NAME_FILTER_LIST = [
        { value: 0, label: "<PERSON>", selected: true },
        { value: 1, label: "<PERSON><PERSON>", selected: true },
        { value: 2, label: "<PERSON>", selected: false },
        { value: 3, label: "Geneva", selected: true },
        { value: 4, label: "<PERSON><PERSON>", selected: false },
    ];

    // date fiter list
    const Date_FILTER_LIST = [
        { value: 0, label: "1900-05-20", selected: false },
        { value: 1, label: "1910-06-20", selected: false },
        { value: 2, label: "2000-07-20", selected: false },
        { value: 3, label: "2010-08-20", selected: false },
        { value: 4, label: "2020-09-20", selected: false },
    ]; */

    export default {
        data() {
            return {
                NAME_FILTER_LIST:[],
                Date_FILTER_LIST:[],
                // search data
                searchData: {
                    names: [],
                    date: "",
                },
                columns: [
                    
                ],
                // real table data
                tableData: [],
                // source data
                sourceData: [
                    {
                        name: "<PERSON>",
                        date: "1900-05-20",
                        hobby: "coding and coding repeat",
                        address: "No.1 Century Avenue, Shanghai",
                        rowKey: 0,
                    },
                    {
                        name: "Dickerson",
                        date: "1910-06-20",
                        hobby: "coding and coding repeat",
                        address: "No.1 Century Avenue, Beijing",
                        rowKey: 1,
                    },
                    {
                        name: "Larsen",
                        date: "2000-07-20",
                        hobby: "coding and coding repeat",
                        address: "No.1 Century Avenue, Chongqing",
                        rowKey: 2,
                    },
                    {
                        name: "Geneva",
                        date: "2010-08-20",
                        hobby: "coding and coding repeat",
                        address: "No.1 Century Avenue, Xiamen",
                        rowKey: 3,
                    },
                    {
                        name: "Jami",
                        date: "2020-09-20",
                        hobby: "coding and coding repeat",
                        address: "No.1 Century Avenue, Shenzhen",
                        rowKey: 4,
                    },
                ],
            };
        },
        watch: {
            searchData: {
                handler: function () {
                    this.search();
                },
                immediate: true,
                deep: true,
            },
        },
        methods: {
            // search
            search() {
                const { names, date } = this.searchData;
                this.tableData = this.sourceData.filter(
                    (x) =>
                        (date === "" || date === x.date) &&
                        (names.length === 0 || names.includes(x.name)),
                );
            },
        },
        
        created() {
            this.NAME_FILTER_LIST =  _.chain(this.sourceData)
                        .map(item => item.name)
                        .uniq()
                        .map(item => ({
                        label: item,
                        value: item,
                        }))
                        .value()
            

            this.Date_FILTER_LIST = _.chain(this.sourceData)
                        .map(item => item.date)
                        .uniq()
                        .map(item => ({
                        label: item,
                        value: item,
                        }))
                        .value()

            this.columns = [
                {
                        field: "name",
                        key: "a",
                        title: "Name",
                        align: "left",
                        width: "15%",
                        // filter
                        filter: {
                            filterList: this.NAME_FILTER_LIST,
                            isMultiple: true,
                            // filter confirm hook
                            filterConfirm: (filterList) => {
                                const labels = filterList
                                    .filter((x) => x.selected)
                                    .map((x) => x.label);
                                this.searchData.names = labels;
                            },
                            // filter reset hook
                            filterReset: (filterList) => {
                                this.searchData.names = [];
                            },
                        },
                    },
                    {
                        field: "date",
                        key: "b",
                        title: "Date",
                        align: "left",
                        width: "15%",
                        // filter
                        filter: {
                            filterList: this.Date_FILTER_LIST,
                            filterConfirm: (filterList) => {
                                const item = filterList.find((x) => x.selected);
                                this.searchData.date = item ? item.label : "";
                            },
                            filterReset: (filterList) => {
                                this.searchData.date = "";
                            },
                        },
                    },
                    {
                        field: "hobby",
                        key: "c",
                        title: "Hobby",
                        align: "center",
                        width: "30%",
                    },
                    {
                        field: "address",
                        key: "d",
                        title: "Address",
                        align: "left",
                        width: "40%",
                    },
            ]
            // default search by names
            //this.searchData.names = NAME_FILTER_LIST.filter((x) => x.selected).map((x) => x.label);
        },
    };
</script>
