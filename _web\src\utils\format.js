
/**
 * 时间格式化
 */
export function formatDate(time, isShowSec = false) {
	var date = new Date(time);
	var year = date.getFullYear(),
		month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1,//月份是从0开始的
		day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate(),
		hour = date.getHours() < 10 ? '0' + date.getHours() : date.getHours(),
		min = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes(),
		sec = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
	var newTime = isShowSec ? (year + '-' +
		month + '-' +
		day + ' ' +
		hour + ':' +
		min + ':' +
		sec) : (year + '-' +
			month + '-' +
			day + ' ' +
			hour + ':' +
			min)
	return newTime;
}

export function formatDate1(time,isAdd = false) {
	var date1 = new Date(time);
	var year1 = date1.getFullYear(),
		month1 = date1.getMonth() + 1 < 10 ? '0' + (date1.getMonth() + 1) : date1.getMonth() + 1,//月份是从0开始的
		day1 = date1.getDate() < 10 ? '0' + date1.getDate() : date1.getDate()

		if(isAdd){
			day1 ++
		}
	return year1 + '-' +
		month1 + '-' +
		day1;
}

// 比较时间
export function compareTime(s1, s2) {
	s1 = new Date(s1.replace(/-/g, '/'));
	s2 = new Date(s2.replace(/-/g, '/'));
	var ms = s1.getTime() - s2.getTime();
	return ms / 1000 / 60 / 60;
}