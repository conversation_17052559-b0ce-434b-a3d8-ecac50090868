<template>
  <div>
    <x-card>
    <div slot="content" class="table-page-search-wrapper" >
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="24">
            <a-form-item label="关键字">
              <a-input v-model="queryParam.keyword"/>
            </a-form-item>
          </a-col>

          <a-col :md="8" :sm="24">
            <a-form-item label="文件类型">


              <a-select  v-model="queryParam.type" style="width: 120px" @change="$refs.table.refresh(true)">
                <a-select-option value="0">
                  BOM
                </a-select-option>
                <a-select-option value="1">
                  MI
                </a-select-option>
                <a-select-option value="2">
                  图纸
                </a-select-option>
                <a-select-option value="3">
                  特殊属性清单
                </a-select-option>
                <a-select-option value="4">
                  测试验证
                </a-select-option>
              </a-select>

            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {isOut: true}">重置</a-button>
              </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    </x-card>
    <s-table ref="table" :columns="columns" :data="loadData" :alert="false" :rowKey="(record) => record.id">
      <template slot="pushStatus" slot-scope="text,record">
        <a   @click="preview(record.bomId,record.processId)">{{showStatus(text,record)}}</a>
      </template>

      <template slot="sys" slot-scope="text,record">
        <a  @click="previewPdf(record.fileId)">{{showSys(text,record)}}</a>
      </template>

    </s-table>
    <a-drawer
      placement="right"
      :closable="false"
      width="80%"
      :bodyStyle="{ height: '100%' }"
      :visible="visible1"
      @close="onClose1"
    >
      <iframe :src="pdfUrl+'#view=FitH,top'" width="100%" style="height: 100%" ></iframe>
    </a-drawer>

    <a-drawer placement="right" :closable="false" width="80%" :visible="visible2" @close="onClose2" :destroyOnClose="true">
      <checkhistory :param="param" ></checkhistory>
    </a-drawer>
  </div>




</template>

<script>
  import {
    bomPushList
  } from "@/api/modular/system/bomManage"
  import {
    STable,XCard
  } from '@/components'
  import checkhistory from "./checkhistory";


  export default {
    components: {
      STable,
      XCard,
      checkhistory
    },
    data() {
      return {
        title: '提交记录',
        pdfUrl:'',
        queryParam: {isOut : true},
        visible: false,
        visible1: false,
        visible2: false,
        param:{},
        historyBomId:'',
        processId:100,
        confirmLoading: false,
        loadData: parameter => {
          return bomPushList(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        columns: [{
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            width: 50,
            customRender: (text, record, index) => `${index+1}`,
          }, {
            title: '项目名称',
            dataIndex: 'name'
          }, {
          title: '文件类型',
          dataIndex: 'type',
          customRender: (text, record, index) => {

            if(record.type == -1 || record.type == null ){
              return 'BOM'
            }
            if(record.type == 0 ){
              return 'MI'
            }
            if(record.type == 1){
              return '图纸'
            }
            if(record.type == 2){
              return '特殊属性清单'
            }
            if(record.type == 3){
              return '测试验证'
            }
            if(record.type == 4){
              return '产品规格书'
            }
            return "BOM"

          }
        },
          {
            title: '操作类型',
            dataIndex: 'operateType',
            customRender: (text, record, index) => record.bomType + " "+ text,
          },
          {
            title: '样品阶段',
            dataIndex: 'stage'
          },
          {
            title: '状态',
            dataIndex: 'pushStatus',
            scopedSlots: {
              customRender: 'pushStatus'
            }
          },

          {
            title: '创建时间',
            dataIndex: 'createTime'
          },
          {
            title: '结束时间',
            dataIndex: 'updateTime',
            customRender: (text, record, index) =>{
              if(record.pushStatus == 4){
                return text
              }else{
                return ''
              }
            }
          },{
            title: '受控文件',
            dataIndex: 'sys',
            scopedSlots: {
              customRender: 'sys'
            }
          },
        ]
      }
    },
    methods: {

      preview(id,processId) {
        this.historyBomId = id
        this.param.historyBomId = id
        this.param.processId = processId
        this.visible2 = !this.visible1
      },

      previewPdf(id) {
        this.pdfUrl = process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id='+  id
        this.visible1 = !this.visible1
      },

      onClose1() {
        this.visible1 = false;
      },

      onClose2() {
        this.visible2 = false;
      },

      handleCancel() {
        this.visible = false
      },
      toJIRA(url){
        window.open('http://jira.evebattery.com/browse/' + url, "_blank");
      },

      toOA(url){
        window.open(process.env.VUE_APP_OA_URL+'/km/review/km_review_main/kmReviewMain.do?method=view&fdId=' + url, "_blank");
      },

      toThird(record){
        if(record.stage == 'A样' || record.stage == 'B样'){
          window.open('http://jira.evebattery.com/browse/' + record.url, "_blank");
        }else{
          window.open(process.env.VUE_APP_OA_URL+'/km/review/km_review_main/kmReviewMain.do?method=view&fdId=' + record.url, "_blank");
        }
      },

      openPdf(record){
        this.$parent.previewPdf(record.fileId)
      },

      showSys(text,record){
        if(record.pushStatus == 4){
          return text
        }else{
          return ''
        }
      },

      showStatus(text,record){

        if(0 == text){
          if(record.stage == 'A样' || record.stage == 'B样'){
            return 'JIRA 待审核'
          }else{
            return 'OA 待审核'
          }
        }

        if(2 == text){
          return 'OA 已审核'
        }

        if(3 == text){
          return '已提交 SAP'
        }

        if(4 == text){
          return '已完成'
        }
        if(6 == text){
          return '推送SAP失败'
        }

      }

    }
  }
</script>

<style>
.ant-drawer-body {
  height: 100%;
}
</style>

