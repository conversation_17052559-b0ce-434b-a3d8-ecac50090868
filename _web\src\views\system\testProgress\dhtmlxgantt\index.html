<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">

<script src="codebase/dhtmlxgantt.js"></script>
<script src="codebase/ext/dhtmlxgantt_marker.js"></script>
<script src="codebase/ext/dhtmlxgantt_tooltip.js"></script>
<script src="codebase/ext/dhtmlxgantt_keyboard_navigation.js"></script>


<link rel="stylesheet" href="codebase/dhtmlxgantt.css">
<link rel="stylesheet" href="common/controls_styles.css">
<link rel="stylesheet" id="skin">
</head>
<style>
    html {
        height: 100%;
    }

    body {
        height: 100%;
        margin: 0;
    }

    .dragging_task,
    .dragging_task.gantt_task_line.gantt_milestone .gantt_link_control .gantt_link_point,
    .dragging_task.gantt_task_line.gantt_milestone .gantt_task_content,
    .dragging_task.gantt_task_line.gantt_milestone .gantt_side_content {
        visibility: hidden !important;
    }

    .gantt_layout_cell.gantt_layout_root.gantt_layout.gantt_layout_y.gantt_container.gantt_layout_cell_border_left.gantt_layout_cell_border_top.gantt_layout_cell_border_right.gantt_layout_cell_border_bottom {
        height: 100% !important;
    }
    
</style>

<body>
    <div id="gantt_here" style="width:100%; height:100%;"></div>
</body>
<script>
    gantt.initForm = function (zoomConfig) {
        if (!zoomConfig || !zoomConfig.levels || !zoomConfig.levels.length || zoomConfig.levels.length <= 0) {
            return
        }

        var input = ''
        zoomConfig.levels.forEach( function (element, i){
            _input = '<input type="radio" id=' + element.name + ' class="gantt_radio" name="scale" value=' + element.name + '> <label for= ' + element.name + ' > ' + element.label + '</label > '
            input += _input
        });

        var gantt_control = document.createElement('div')
        gantt_control.setAttribute('class', 'gantt_control')
        gantt_control.innerHTML = input

        document.body.insertBefore(gantt_control, document.getElementById('gantt_here'))
        document.getElementById('gantt_here').style.height = 'calc(100% - 45px)'

        this.ext.zoom.init(zoomConfig);

        this.ext.zoom.setLevel("month");

        document.getElementsByClassName("gantt_control")[0].onclick = function (event) {
            if (event.target.hasAttribute('name')) {
                this.ext.zoom.setLevel(event.target.value);
            }
        }.bind(this);
    }
     
    function changeSkin(name) {
        var link = document.createElement("link");
        link.rel = "stylesheet";
        link.type = "text/css";
        link.id = "skin";
        link.href = "codebase/skins/dhtmlxgantt_" + name + ".css";
        document.head.replaceChild(link, document.querySelector("#skin"));
    }
    
    function addWeekendStyle() {
        var css ='.gantt_task_cell.week_end, .gantt_task_cell.weekend { background-color: #EFF5FD; }';
        var oldCss = document.getElementsByTagName('style')[0].textContent;
        document.getElementsByTagName('style')[0].textContent = oldCss + css;
    }
    
    function init(ganttVue) {
        
        if (ganttVue.options.skin) {
            this.changeSkin(ganttVue.options.skin);
        }
        
        if (ganttVue.options.weekend) {
            this.addWeekendStyle();
        }
        
        if (ganttVue.options.draggable) {
            var dragging_task = 0;
            gantt.attachEvent('onAfterTaskDrag', function (id, mode, e) {
                gantt.dragId = null, gantt.afterDragParentId = null, gantt.beforeDragParentId = null;
                var modes = gantt.config.drag_mode;
                if (mode == modes.move) {
                    if (dragging_task && id == dragging_task) {
                        var target_index = Math.floor(y_pos / gantt.config.row_height);
                        var target_task = gantt.getTaskByIndex(target_index - 1);
                        if (!target_task) target_task = gantt.getTaskByIndex(gantt.getVisibleTaskCount() - 1);
                        if (target_task.type != 'project') {
                            var copy_object = gantt.copy(target_task);
                            gantt.changeTaskId(target_task.id, +new Date());
                            gantt.addTask(copy_object, target_task.id);
                            target_task.type = 'project';
                            target_task.render = ['split'];
                        }
                        var task = gantt.getTask(id);
                        //获取拖拽后的id
                        gantt.dragId = id;
                        if (task.parent) {
                            gantt.beforeDragParentId = task.parent;
                            var original_parent = gantt.getTask(task.parent);
                            var children = gantt.getChildren(task.parent);
                            if (children.length == 1) original_parent.type = gantt.config.types.task;
                        }
                        gantt.afterDragParentId = target_task.id;
                        task.parent = target_task.id;
                        gantt.updateTask(target_task.id);
                        gantt.updateTask(id);
                        gantt.render();
                    }
                    dragging_task = 0;
                    gantt.refreshTask(id);
                }
            });
            // 渲染红能或者配置对象，将在图层中显示Dom元素
            gantt.addTaskLayer({
                renderer: function highlight_area() {
                    var task = gantt.getTask(dragging_task);
                    var sizes = gantt.getTaskPosition(task, task.start_date, task.end_date);
                    var el = document.createElement('div');
                    el.className = 'gantt_task_line';
                    el.style.top = y_pos - 54 + 'px';
                    if (task.type == gantt.config.types.milestone) {
                        el.style.left = (sizes.left - 15) + 'px';
                        el.style.width = '30px';
                        el.style.height = '30px';
                        el.style.transform = 'rotate(45deg)';
                        el.style.background = '#D33DAF';
                    }
                    else {
                        el.style.left = sizes.left + 'px';
                        el.style.width = sizes.width + 'px';
                        el.style.height = sizes.height + 'px';
                    }
                    el.style.opacity = 0.5;
                    if (dragging_task) return el;
                },
                filter: function (task) {
                    return dragging_task == gantt.getState().drag_id;
                }
            });
            gantt.templates.tooltip_text = function(start,end,task) {
                 return '<b>Task:</b> '+task.text+'<br/><b>任务：</b>' + task.text1;
            };
            var y_pos;
            gantt.attachEvent('onMouseMove', function (id, e) {
                y_pos = e.clientY;
            });
            gantt.attachEvent('onBeforeTaskDrag', function (id, mode, e) {
                var modes = gantt.config.drag_mode;
                if (mode == modes.move) {
                    dragging_task = id;
                    gantt.refreshTask(id);
                }
                return true;
            });
            gantt.templates.task_class = function (start, end, task) {
                if (dragging_task && task.id == dragging_task) return 'dragging_task';
            };

            //激活“分支”模式，该模式允许在同一树级别内垂直重新排序任务
            gantt.config.order_branch = true;
            //激活“分支”模式，该模式允许在整个甘特图中重新排序任务
            gantt.config.order_branch_free = true;
            
            //甘特图以自动扩展时间范围以适合所有显示的任务
            gantt.config.fit_tasks = true;
            //最初打开所有分支
            gantt.config.open_tree_initially = true;
            //可以将任务的开始和结束日期四舍五入到最接近的刻度
            gantt.config.round_dnd_dates = false;
        }
        return gantt;
    }
</script>

</html>