<template>
  <div class="all-wrapper" :style="{paddingTop: padding}">
    <div class="flex-sb-center-row">
      <div style="display: flex;">
        <div :class="pageKey == 'create'?'tooltip':'normal'" style="margin-right: 10px" @click="pageKey = 'create'">
          方案合并模型搭建
          <div :class="pageKey == 'create'?'tooltip-arrow':null"></div>
        </div>
        <div :class="pageKey == 'list'?'tooltip':'normal'" @click="pageKey = 'list'">
          方案合并建模记录查询
          <div :class="pageKey == 'list'?'tooltip-arrow':null"></div>
        </div>
      </div>

      <div class="wrapper mt5" v-if="pageKey == 'create'">
        <div style="position: relative;">
          <div class="normal-btn" :class="{ 'streamer-btn anima': _handleVerify()[0] }" @click="handleAllOk">
            完成模型搭建
          </div>
        </div>
      </div>
    </div>

    <div class="block" v-if="pageKey == 'create'" :style="{minHeight: 'calc(100vh - 92px - '+width+'px)'}">
      <h3>一、基本信息</h3>
      <div class="wrapper search-container" style="margin: 10px 0 10px 0;">
        <div class="label-span">任务名称 :</div>
        <a-tooltip :title="templateParam.projectName">
          <a-input style="width: 800px;" v-model="templateParam.projectName"/>
        </a-tooltip>
      </div>
      <h3>二、数据选择</h3>
      <div class="wrapper" style="margin-top: 10px;">
        <a-popconfirm placement="topRight" title="确认清空？" @confirm="handleDeleteAll">
          <a-button class="mr5">清空</a-button>
        </a-popconfirm>
        <a-popconfirm placement="topRight" title="确认删除？" @confirm="handleDeleteSelected('testProgress')">
          <a-button>删除</a-button>
        </a-popconfirm>
      </div>
      <div class="mt10" ref="tableContainer">
        <a-table bordered
                 :rowKey="record => record.id"
                 :columns="testProgressColumns"
                 :data-source="templateParam.testProgressList"
                 :row-selection="{
                   selectedRowKeys: testProgressDeleteSelectedRowKeys,
                   columnWidth: 30,
                   onChange: (selectedRowKeys, selectedRows) => {
                     this.testProgressDeleteSelectedRowKeys = selectedRowKeys
                   }
                 }"
                 :pagination="false">
          <template slot="action" slot-scope="text, record, index, columns">
            <a-tooltip placement="top" title="删除" arrow-point-at-center>
              <a @click="deleteDataOne('testProgress', index)" style="text-align: center">
                <a-icon type="delete" style="font-size: large;margin-right: 3px"/>
              </a>
            </a-tooltip>

            <a-tooltip placement="top" title="拖拽修改位置" arrow-point-at-center >
              <a-icon :style="setMultStyle" style="color: #1890ff;font-size: large;" heignt="18" width="18" class="drag" type="unordered-list"/>
            </a-tooltip>

          </template>


          <template slot="testProject" slot-scope="text, record, index, columns">
            <a @click="checkCalendarReportByRecord(record)">{{text}}</a>
          </template>
          <template slot="optionName" slot-scope="text, record, index, columns">
            <a-input class="input-text" v-model="templateParam.testProgressList[index].optionName" />
          </template>
          <template slot="footer">
            <div class="footer-btn" :class="{ 'plus-btn': !Array.isArray(templateParam.testProgressList) || templateParam.testProgressList.length === 0 }" @click="handleCancel('isShowTestProgressModal')">
              <span></span>
              <span></span>
              <span></span>
              <span></span>
              <a-icon type="plus"></a-icon>
            </div>
          </template>
        </a-table>
      </div>
    </div>
    <optionMergeReportList :width="parseInt(30 + width)" padding="0px" v-if="pageKey == 'list'" @gotoCreate="gotoCreate"></optionMergeReportList>
    <!-- 测试项目选择弹窗 -->
    <a-modal title="测试项目选择"
             :width="1200"
             :height="600"
             :bodyStyle="{ padding: 0 }"
             :visible="isShowTestProgressModal"
             style="padding: 0"
             :maskClosable="false"
             @cancel="handleCancel('isShowTestProgressModal')">

      <div class="operate-wrap">
        <div class="filter-box search-container">
          <div class="operate-block" v-for="(item, index) in filterOptions" v-if="isShowAllSearch || index < 5">
            <div class="label mr8">{{item.label}} : </div>
            <div v-if="item.type === 'input'" class="input-short">
              <a-input v-model="queryParam[item.dataIndex]" @keyup.enter="getList" @change="getList"/>
            </div>
            <div v-else-if="item.type === 'select'" class="input-short">
              <a-select v-model="queryParam[item.dataIndex]" dropdown-class-name="dropdownClassName" allowClear style="width:100%" @keyup.enter="getList" @change="getList">
                <a-select-option v-for="cItem in item.selectOption" :value="cItem.value">{{cItem.label}}</a-select-option>
              </a-select>
            </div>
          </div>
        </div>
        <div class="button-box search-container">
          <div class="secondary-btn">
            <a-button class="mr8" @click="getList()" type="primary">查询</a-button>
            <a-button class="mr8" @click="getList(true)">重置</a-button>
          </div>
          <div class='toggle-btn'>
            <a-button size='small' type='link' @click='isShowAllSearch = !isShowAllSearch'>
              {{ isShowAllSearch ? '收起' : '展开' }}
              <span v-if='isShowAllSearch'><a-icon type='double-left'/></span>
              <span v-else><a-icon type='double-right'/></span>
            </a-button>
          </div>
        </div>
      </div>

      <s-table class="testProgressTable2"
               bordered
               :columns="testProgressColumns2"
               :data="loadData"
               :rowKey="record1 => record1.id"
               :row-selection="{
                 selectedRowKeys: testProgressSelectedRowKeys,
                 onSelect: onSelectChange,
                 onSelectAll: onSelectAllChange,
                 columnWidth: 30
               }"
               :scroll="{x:true}"
               ref="testProgressTable">
        <template slot="testProject" slot-scope="text, record, index, columns">
          <a @click="checkCalendarReportByRecord(record)">{{text}}</a>
        </template>
      </s-table>

      <template slot="footer">
        <a-button key="back" @click="handleCancel('isShowTestProgressModal')">关闭</a-button>
      </template>
    </a-modal>

    <!-- 完成模型搭建弹窗 -->
    <a-modal title="完成模型搭建" :width="420" :visible="isShowReportName" @cancel="handleCancel('isShowReportName')">
      <div class="wrapper">
        <span class="label-span">任务名称:</span>
        <a-input v-model="reportName" placeholder="请填写任务名称" style="width: 200px;" @keyup.enter="exportData" />
      </div>
      <template slot="footer">
        <div class="btn-wrap">
          <a-button @click="handleCancel('isShowReportName')">取消</a-button>
          <a-button type="primary" @click="exportData">完成模型搭建</a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script>
import jsonBigint from "json-bigint";
import {decodeAndDecompress} from "@/utils/util";
import {mapGetters} from "vuex";
import {commitOptionMergeParam, tLimsFolderListPage} from "@/api/modular/system/limsManager";
import {testProgressListPage, validExportSizeOriData} from "@/api/modular/system/testProgressManager";
import {STable} from "@/components";
import optionMergeReportList from "..//vTestReport/optionMergeReportList"
import Sortable from 'sortablejs';
export default {
  name: "optionMergeBuild",
  components: {
    STable,
    optionMergeReportList
  },
  props: {
    width:{
      type: Number,
      default: 0
    },
    padding:{
      type: String,
      default: '8px'
    }
  },
  data() {
    return {
      pageKey:'create',
      isShowReportName: false,
      reportName: '',

      templateParam: {projectName: '', testProgressList: []},
      // testProgress选择
      isShowTestProgressModal: false,
      filterOptions:[
        {
          label:'测试申请单',
          dataIndex:'testCode',
          type:'input'
        },
        {
          label:'申请人',
          dataIndex:'applicant',
          type:'input'
        },
        {
          label:'测试技师',
          dataIndex:'testMan',
          type:'input'
        },
        {
          label:'产品名称',
          dataIndex:'productName',
          type:'input'
        },
        {
          label:'部门',
          dataIndex:'dept',
          type:'input'
        },
        {
          label:'电芯载体',
          dataIndex:'sampleType',
          type:'select',
          selectOption:[
            {
              label:'G圆柱',
              value:'G圆柱'
            },
            {
              label:'C圆柱',
              value:'C圆柱'
            },
            {
              label:'V圆柱',
              value:'V圆柱'
            },
            {
              label:'方型',
              value:'方型'
            },
            {
              label:'软包',
              value:'软包'
            }
          ]
        },
        {
          label:'测试项目',
          dataIndex:'testProject',
          type:'input'
        },
        {
          label:'样品阶段',
          dataIndex:'productSampleStage',
          type:'input'
        },
      ],
      testProgressSelectedRowKeys: [],
      // 表头
      testProgressColumns2: [
        {
          title: '序号',
          dataIndex: 'index',
          align: 'center',
          width: 50,
          customRender: (text, record, index) => `${index + 1}`
        }, {
          title: '测试状态',
          width: 80,
          align: 'center',
          dataIndex: 'testStatus',
        }, {
          title: '测试申请单',
          width: 100,
          align: 'center',
          dataIndex: 'testCode',
        }, {
          title: '电芯载体',
          width: 70,
          align: 'center',
          dataIndex: 'sampleType',
        }, {
          title: '产品名称',
          width: 70,
          align: 'center',
          dataIndex: 'productName',
        }, {
          title: '产品样品阶段',
          width: 100,
          align: 'center',
          dataIndex: 'productSampleStage',
        }, {
          title: '测试类型',
          width: 100,
          align: 'center',
          dataIndex: 'testType',
        }, {
          title: '申请部门',
          width: 100,
          align: 'center',
          dataIndex: 'dept',
        }, {
          title: '申请人',
          width: 80,
          align: 'center',
          dataIndex: 'applicant',
        }, {
          title: '测试项目',
          width: 140,
          align: 'center',
          dataIndex: 'testProject',
          scopedSlots: { customRender: 'testProject' },
        }, {
          title: '测试项目别名',
          width: 100,
          align: 'center',
          ellipsis:true,
          dataIndex: 'testAlias',
        }, {
          title: '数量',
          width: 50,
          align: 'center',
          dataIndex: 'quantity',
        }, {
          title: '测试技师',
          width: 80,
          align: 'center',
          dataIndex: 'testMan',
        },
      ],
      queryParam: {},
      loadData: parameter => {
        return testProgressListPage(Object.assign(parameter, this.queryParam), false).then((res) => {
          return res.data
        })
      },
      isShowAllSearch: false,

      // 表头
      testProgressColumns: [
        {
          title: "操作",
          align: "center",
          width: 50,
          scopedSlots: {customRender: "action"}
        },
        {
          title: '序号',
          align: 'center',
          width: 50,
          customRender: (text, record, index) => `${index + 1}`
        }, {
          title: '测试状态',
          width: 80,
          align: 'center',
          dataIndex: 'testStatus',
        }, {
          title: '测试申请单',
          width: 100,
          align: 'center',
          dataIndex: 'testCode',
        }, {
          title: '电芯载体',
          width: 70,
          align: 'center',
          dataIndex: 'sampleType',
        }, {
          title: '产品名称',
          width: 70,
          align: 'center',
          dataIndex: 'productName',
        }, {
          title: '测试类型',
          width: 80,
          align: 'center',
          dataIndex: 'testType',
        }, {
          title: '申请人',
          width: 80,
          align: 'center',
          dataIndex: 'applicant',
        }, {
          title: '测试项目',
          width: 140,
          align: 'center',
          dataIndex: 'testProject',
          scopedSlots: { customRender: 'testProject' },
        }, {
          title: '测试项目别名',
          width: 100,
          align: 'center',
          ellipsis:true,
          dataIndex: 'testAlias',
        }, {
          title: '方案名称',
          width: 100,
          align: 'center',
          scopedSlots: {
            customRender: 'optionName'
          }
        },
      ],
      testProgressDeleteSelectedRowKeys: [],
    }
  },
  computed: {
    ...mapGetters(["userInfo","testTaskFilterData"]),
    setMultStyle() {
      return {
        cursor: this.enabled ? 'move' : 'default'
      }
    }
  },
  created() {
    // 如果是重新生成
    if (this.testTaskFilterData !== null) {
      let json = jsonBigint({storeAsString: true})
      this.reportName = this.testTaskFilterData.reportName
      this.templateParam = json.parse(decodeAndDecompress(this.testTaskFilterData.queryParam))
      this.$store.commit("setTaskFilterData", null)

      this.$nextTick(() => {
        let tableContainer = this.$refs.tableContainer
        this.rowDrop(tableContainer)
      })
    }
  },
  mounted() {
    this.$nextTick(() => {
      let tableContainer = this.$refs.tableContainer
      this.rowDrop(tableContainer)
    })
  },
  methods: {
    rowDrop(dom) {

      new Sortable.create(dom.querySelector('.ant-table>.ant-table-content .ant-table-tbody'), {
        handle: '.drag', // 行元素
        animation: 150,
        onEnd: ({newIndex, oldIndex}) => {
          // 拖拽后回调
          const currRow = this.templateParam.testProgressList.splice(oldIndex, 1)[0]
          this.templateParam.testProgressList.splice(newIndex, 0, currRow)
        }
      })
    },
    gotoCreate(rebuildFlag = false) {
      this.pageKey = 'create'

      if (rebuildFlag) {
        this.reportName = ''
        this.templateParam = {projectName: '', testProgressList: []}
        this.$store.commit("setTaskFilterData", null)
      } else {
        // 如果是重新生成
        if (this.testTaskFilterData !== null) {
          let json = jsonBigint({storeAsString: true})
          this.reportName = this.testTaskFilterData.reportName
          this.templateParam = json.parse(decodeAndDecompress(this.testTaskFilterData.queryParam))
          this.$store.commit("setTaskFilterData", null)
          this.$nextTick(() => {
            let tableContainer = this.$refs.tableContainer
            this.rowDrop(tableContainer)
          })
        }
      }
    },
    handleCancel(target) {
      if (target === 'isShowTestProgressModal' && this.isShowTestProgressModal === false) {
        this.testProgressSelectedRowKeys = this.templateParam.testProgressList.map(item => item.id)
      }

      this[target] = !this[target]
    },

    handleDeleteAll() {
      this.templateParam.testProgressList = []
      this.testProgressDeleteSelectedRowKeys = []
    },
    handleDeleteSelected(target) {
      // 按照索引顺序降序排列，避免删除元素影响后续索引
      this[`${target}DeleteSelectedRowKeys`].sort((a, b) => b - a)
      this[`${target}DeleteSelectedRowKeys`].forEach(item => {
        this.templateParam[`${target}List`].splice(item, 1)
      })
      this[`${target}DeleteSelectedRowKeys`] = []
    },
    deleteDataOne(target, index) {
      this.templateParam[`${target}List`].splice(index, 1)
    },
    moveUp(arr, index) {
      if (arr.length > 1 && index > 0) { // 确保数组至少有两个元素，且索引有效
        arr[index] = arr.splice(index - 1, 1, arr[index])[0]; // 移除元素后立即插入到前一个位置
      }
    },
    moveDown(arr, index) {
      if (arr.length > 1 && index < arr.length - 1) { // 确保数组至少有两个元素，且索引有效
        arr[index] = arr.splice(index + 1, 1, arr[index])[0]; // 移除元素后立即插入到后一个位置
      }
    },

    getList(flag) {
      if(flag==true){
        this.queryParam = {}
      }
      this.$refs.testProgressTable.refresh()
    },
    onSelectChange(record, selected) {
      this.templateParam.testProgressList = this.templateParam.testProgressList || []

      if (selected) {
        this.handleValidRole(record)
      } else {
        for (let i = 0; i < this.testProgressSelectedRowKeys.length; i++) {
          if (this.testProgressSelectedRowKeys[i] === record.id) {
            this.testProgressSelectedRowKeys.splice(i, 1)
            this.templateParam.testProgressList.splice(i, 1)
            break
          }
        }
      }

    },
    onSelectAllChange(selected, selectedRows, changeRows) {
      this.templateParam.testProgressList = this.templateParam.testProgressList || []

      if (selected) {
        selectedRows.forEach(item => {
          this.handleValidRole(item)
        })
      } else {
        for (let i = 0; i < changeRows.length; i++) {
          if (this.testProgressSelectedRowKeys.includes(changeRows[i].id)) {
            let index = this.testProgressSelectedRowKeys.indexOf(changeRows[i].id)

            this.testProgressSelectedRowKeys.splice(index, 1)
            this.templateParam.testProgressList.splice(index, 1)
          }
        }
      }
    },
    handleValidRole(record) {
      let checkAllDataFlag = this.userInfo.roles.filter(item => item.code === "check_all_calendar_report")
      // 管理员或者有【查看所有日历寿命测试项目报告】角色的用户
      if (this.userInfo.account === "superAdmin" || checkAllDataFlag.length > 0 || this.userInfo.name === record.applicant || this.userInfo.account == record.applicantAccount) {
        this.handleValidExportData(record)
      } else {
        tLimsFolderListPage({folderno:record.testCode}).then(res => {
          if(res.data.rows.length === 0){
            this.$message.warning('无权限选择其他申请人的报告！')
          }else{
            this.handleValidExportData(record)
          }
        })
      }
    },
    handleValidExportData(record) {
      validExportSizeOriData({ testProgressId: record.id, exportType: "handleResult" }).then(res => {
        if (!res.success) {
          this.$message.warn("离线数据为空！");
          return;
        }

        if (!this.testProgressSelectedRowKeys.includes(record.id)) {
          this.testProgressSelectedRowKeys.push(record.id);

          this.templateParam.testProgressList.push(record);
        }
      });
    },
    checkCalendarReportByRecord(record) {
      let checkAllDataFlag = this.userInfo.roles.filter(item => item.code === "check_all_calendar_report")

      // 管理员或者有【查看所有日历寿命测试项目报告】角色的用户
      if (this.userInfo.account === "superAdmin" || checkAllDataFlag.length > 0 || this.userInfo.name === record.applicant || this.userInfo.account == record.applicantAccount) {
        this.getCalendarReport(record)
      } else {
        tLimsFolderListPage({folderno:record.testCode}).then(res => {
          if(res.data.rows.length === 0){
            this.$message.warning('无权限查看其他申请人的报告！')
          }else{
            this.getCalendarReport(record)
          }
        })
      }
    },
    getCalendarReport(record) {
      const id = record.id;
      const alias = record.testAlias ? record.testAlias : "";
      validExportSizeOriData({ testProgressId: id, exportType: "handleResult" }).then(res => {
        if (res.success) {
          // 有离线数据，通过record.onlineReportStatus判断是否有在线数据
          window.open("/v_report_preview?testProgressId=" + id + "&alias=" + encodeURIComponent(alias) + "&offFlag=1&onlineFlag=" + (record.onlineReportStatus === 20 ? "1" : "0") + "&type=日历寿命", "_blank")
        } else {
          // 无离线数据，通过record.onlineReportStatus进行判断
          if (record.onlineReportStatus === 20) {
            window.open("/v_report_preview?testProgressId=" + id + "&alias=" + encodeURIComponent(alias) + "&offFlag=0&onlineFlag=1" + "&type=日历寿命", "_blank") // 需要展示在线报告
          } else {
            this.$message.warning(res.message.replace("导出", "查看"))
          }
        }
      })
    },

    // 校验
    _handleVerify() {
      const templateParam = this.templateParam

      if (!templateParam.projectName) {
        return [false, '请填写任务名称']
      }

      if (!Array.isArray(templateParam.testProgressList) || templateParam.testProgressList.length === 0) {
        return [false, '请选择测试项目']
      }

      for (let i = 0; i < templateParam.testProgressList.length; i++) {
        if (typeof templateParam.testProgressList[i].optionName !== 'string') {
          return [false, i, '方案名称']
        }
      }

      return [true]
    },

    handleAllOk() {
      const progressList = this._handleVerify()

      // 如果校验不通过
      if (!progressList[0]) {
        return this.$message.warn(
          progressList.length < 3
            ? progressList[1]
            : '请填写完整第 ' + (progressList[1] + 1) + ' 行测试项目的' + progressList[2]
        )
      }

      if (progressList[0]) {
        // 任务名称赋值给reportName
        if(this.templateParam.projectName) this.reportName = this.templateParam.projectName
        this.isShowReportName = true
      }
    },

    // 完成模型搭建事件
    exportData() {
      if(!this.reportName || !this.reportName.trim()) return this.$message.warn("请正确填写任务名称")

      // 清除queryParam、resultDataJson属性，减少查询参数大小
      this.templateParam.testProgressList.forEach(record => {
        delete record.queryparam
        delete record.resultDataJson
      })

      commitOptionMergeParam(this.templateParam, encodeURIComponent(this.reportName), this.$route.query.id || null).then(res => {
        if (res.success) {
            this.$message.success("创建成功")
            this.$router.push("/optionMergeReportList?type=optionMerge")
          } else {
            this.$message.warn(res.message)
          }
      })
    },

  },
}
</script>

<style lang="less" scoped>
@import "./css/optionMergeBuild.less";
@import '/src/components/pageTool/style/pbiSearchItem.less';

.all-wrapper {
  padding: 0 10px 10px;
  background-color: #f0f2f5;
}

.wrapper{
  overflow-y: auto;
}

/* 标题 */
.head_title {
  color: #333;
  padding: 10px 0;
  font-size: 20px;
  font-weight: 600;
}

.head_title::before {
  width: 8px;
  background: #1890ff;
  margin-right: 8px;
  content: "\00a0"; //填充空格
}

.head_title .subheading {
  font-size: 14px;
  font-weight: 400;
}

.label-span {
  width: 100px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-right: 10px;
}

.input-text {
  width: 100%;
  text-align: center;
  border: 0;
}

.operate-wrap {
  display: flex;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
  margin-top: 10px;
}

.operate-wrap .filter-box {
  width: 85%;
  display: flex;
  flex-wrap: wrap;
}

.operate-wrap .button-box {
  width: 15%;
  display: flex;
  flex-wrap: wrap;
  margin-left: 20px;
}

.filter-box .operate-block {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.operate-block .label {
  width: 70px;
  text-align: right;

  font-size: 12px;
  color: #333;
}

.operate-block .input-short {
  width: 120px;
}

.operate-block .ant-input {
  font-size: 12px;
}

.operate-block .ant-select {
  font-size: 12px;
}

/deep/ .testProgressTable2 .ant-table-body {
  border: 1px solid #e8e8e8;
  overflow: auto; /* 滚动条 */
}

/deep/ .ant-table-thead > tr > th {
  padding: 5px !important;
  font-size: 13px !important;
  color: rgba(0, 0, 0, .85) !important;
  font-weight: 500 !important;
}

/deep/ .ant-table-tbody > tr > td {
  padding: 4px !important;
  font-size: 12px !important;
  color: #333 !important;
  font-weight: 400 !important;
}

/deep/ .testProgressTable2 .ant-table-pagination.ant-pagination {
  float: right;
  margin:2px 0 0;
  font-size: 12px;
}

/deep/ .testProgressTable2 .ant-select-selection-selected-value {
  font-size: 12px;
}

/deep/ .ant-modal-header {
  padding: 20px !important;
  font-size: 14px;
}

/deep/ .s-table-tool {
  padding: 0;
}

/deep/ .ant-modal-footer {
  padding: 16px !important;
}

.tooltip {
  position: relative;
  background-color: #118cff;
  color: #fff;
  padding: 2px;
  border-radius: 2px;
  width: 150px;
  text-align: center;
  margin-bottom: 5px;
  cursor: pointer;
}

.normal {
  position: relative;
  background-color: #dcdfe6;
  color: #aaabac;
  padding: 2px;
  border-radius: 2px;
  width: 150px;
  text-align: center;
  margin-bottom: 5px;
  cursor: pointer;
}

.tooltip-arrow[data-v-701e5e54] {
  position: absolute;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #1890ff;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
}

/deep/.ant-table-placeholder {
  padding:0;
}

</style>