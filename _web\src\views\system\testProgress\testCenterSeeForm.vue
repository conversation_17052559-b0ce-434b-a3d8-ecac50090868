<template>
  <a-modal title="中检结果" :width="1200" :height="600"
           :bodyStyle="{padding:0}"
           :visible="visible" :confirmLoading="confirmLoading" @ok="handleSubmit" style="padding: 0"
           :maskClosable="false"
           @cancel="handleCancel">

    <template slot="footer">
      <a-button key="back" @click="handleCancel">
        关闭
      </a-button>
    </template>

    <a-spin :spinning="confirmLoading">


      <a-tabs v-model="activeKey"  type="card">
        <a-tab-pane key="size" tab="尺寸|产气">
          <a-table :columns="sizeColumns" :data-source="data" bordered
                   style="padding: 5px;height: 280px"
                   bordered
                   :scroll="{x: true}"
                   :rowKey="(record) => Math.random()"
                   :pagination="false"
          >

            <input slot="updateText" slot-scope="text, record,index,column" v-model="text" @change="changeText($event,index,column)" style="text-align: center;">

            </input>
          </a-table>
        </a-tab-pane>
        <a-tab-pane key="ocv" tab="OCV|ACR|重量">
          <a-table :columns="ocvColumns" :data-source="data" bordered
                   style="padding: 5px;height: 280px"
                   bordered
                   :scroll="{x: true}"
                   :rowKey="(record) => Math.random()"
                   :pagination="false"
          >

            <input slot="updateText" slot-scope="text, record,index,column" v-model="text" @change="changeText($event,index,column)" style="text-align: center;">
          </a-table>

        </a-tab-pane>
        <a-tab-pane key="dcr" tab="容量|能量|DCR">
          <a-table :columns="dcrColumns" :data-source="data" bordered
                   style="padding: 5px;height: 280px"
                   bordered
                   :scroll="{ x: true}	"
                   :rowKey="(record) => Math.random()"
                   :pagination="false"
          >

            <input slot="updateText" slot-scope="text, record,index,column" v-model="text" @change="changeText($event,index,column)" style="text-align: center;">
          </a-table>
        </a-tab-pane>
      </a-tabs>


    </a-spin>
  </a-modal>
</template>

<script>
  import {
    centerList
  } from '@/api/modular/system/testProgressManager'
  import {
    getUserLists
  } from '@/api/modular/system/userManage'
  import moment from "moment";
  import {
    STable
  } from '@/components'

  export default {
    components: {
      STable
    },
    props: {
      address: {
        type: String,
        default: null
      },
    },
    data() {
      return {
        data: [],
        sizeColumns: [{
          title: '序号',
          dataIndex: 'index0',
          align: 'center',
          width: 30,
          customRender: (text, record, index) => index + 1
        },
          {
            title: '存储阶段',
            dataIndex: 'index',
            align: 'center',
            width: 90,
            customRender: (text, record, index) => {
              if(record.orderNumber == 0){
                return "初始性能"
              }
              return "第"+record.orderNumber+"次中检"
            }
          }, {
            title: '电芯编码',
            width: 90,
            align: 'center',
            dataIndex: 'batteryCode',
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '样品编号',
            width: 90,
            align: 'center',
            dataIndex: 'sampleCode',
            //scopedSlots: {customRender: 'updateText'},
          }, {
            title: '',
            width: 90,
            align: 'center',
            children: [
              {
                title: '测试时间',
                width: 90,
                align: 'center',
                dataIndex: 'sizeData.testDate',
                //scopedSlots: {customRender: 'updateText'},
              }
            ]
          }, {
            title: '尺寸/mm',
            width: 90,
            align: 'center',
            children: [
              {
                title: '总高',
                width: 70,
                align: 'center',
                dataIndex: 'sizeData.totalHeight',
                //scopedSlots: {customRender: 'updateText'},
              }, {
                title: '肩高',
                width: 70,
                align: 'center',
                dataIndex: 'sizeData.shoulderHeight',
                //scopedSlots: {customRender: 'updateText'},
              }, {
                title: '直径1',
                width: 70,
                align: 'center',
                dataIndex: 'sizeData.diameter1',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '直径2',
                width: 70,
                align: 'center',
                dataIndex: 'sizeData.diameter2',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '直径3',
                width: 70,
                align: 'center',
                dataIndex: 'sizeData.diameter3',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '宽度',
                width: 70,
                align: 'center',
                dataIndex: 'sizeData.width',
                //scopedSlots: {customRender: 'updateText'}
              },{
                title: '厚度',
                width: 70,
                align: 'center',
                dataIndex: 'sizeData.thickness',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '长度',
                width: 70,
                align: 'center',
                dataIndex: 'sizeData.length',
                //scopedSlots: {customRender: 'updateText'}
              }
            ]
          }, {
            title: '产气量',
            width: 90,
            align: 'center',
            children: [
              {
                title: '重量/g',
                width: 90,
                align: 'center',
                dataIndex: 'sizeData.gasWeight',
                //scopedSlots: {customRender: 'updateText'}
              }
            ]
          }
        ],
        ocvColumns: [ {
          title: '序号',
          dataIndex: 'index0',
          align: 'center',
          width: 30,
          customRender: (text, record, index) => index+1
        },
          {
            title: '存储阶段',
            dataIndex: 'index',
            align: 'center',
            width: 90,
            customRender: (text, record, index) => {
              if(record.orderNumber == 0){
                return "初始性能"
              }
              return "第"+record.orderNumber+"次中检"
            }
          }, {
            title: '电芯编码',
            width: 90,
            align: 'center',
            dataIndex: 'batteryCode',
          //scopedSlots: {customRender: 'updateText'}
          }, {
            title: '样品编号',
            width: 90,
            align: 'center',
            dataIndex: 'sampleCode',
          //scopedSlots: {customRender: 'updateText'}
          }, {
            title: '存储开始',
            width: 90,
            align: 'center',
            children: [
              {
                title: '测试时间',
                width: 90,
                align: 'center',
                dataIndex: 'ocvData.beginTestDate',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: 'OCV/mV',
                width: 90,
                align: 'center',
                dataIndex: 'ocvData.beginOcv',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: 'ACR/mΩ',
                width: 90,
                align: 'center',
                dataIndex: 'ocvData.beginAcr',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '重量/g',
                width: 90,
                align: 'center',
                dataIndex: 'ocvData.beginWeight',
                //scopedSlots: {customRender: 'updateText'}
              }
            ]
          }, {
            title: '存储结束',
            width: 90,
            align: 'center',
            children: [
              {
                title: '测试时间',
                width: 90,
                align: 'center',
                dataIndex: 'ocvData.endTestDate',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: 'OCV/mV',
                width: 90,
                align: 'center',
                dataIndex: 'ocvData.endOcv',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: 'ACR/mΩ',
                width: 90,
                align: 'center',
                dataIndex: 'ocvData.endAcr',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '重量/g',
                width: 90,
                align: 'center',
                dataIndex: 'ocvData.endWeight',
                //scopedSlots: {customRender: 'updateText'}
              }
            ]
          },
        ],
        dcrColumns: [ {
          title: '序号',
          dataIndex: 'index0',
          align: 'center',
          width: 40,
          fixed:'left',
          customRender: (text, record, index) => index+1
        },
          {
            title: '存储阶段',
            dataIndex: 'orderNumber',
            align: 'center',
            width: 90,
            fixed:'left',
            customRender: (text, record, index) => {
              if(record.orderNumber == 0){
                return "初始性能"
              }
              return "第"+record.orderNumber+"次中检"
            }
          }, {
            title: '电芯编码',
            width: 90,
            align: 'center',
            dataIndex: 'batteryCode',
            fixed:'left',
            //scopedSlots: {customRender: 'updateText'}
          }, {
            title: '样品编号',
            width: 90,
            align: 'center',
            dataIndex: 'sampleCode',
            fixed:'left',
            //scopedSlots: {customRender: 'updateText'}
          }, {
            title: '',
            width: 90,
            align: 'center',
            children: [
              {
                title: '测试时间',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.testDate',
                //scopedSlots: {customRender: 'updateText'}
              }
            ]
          }, {
            title: '',
            width: 90,
            align: 'center',
            children: [
              {
                title: '测试通道',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.thoroughfare',
                //scopedSlots: {customRender: 'updateText'}
              }
            ]
          }, {
            title: '',
            width: 90,
            align: 'center',
            children: [
              {
                title: '原始数据',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.sourceData',
                //scopedSlots: {customRender: 'updateText'}
              }
            ]
          }, {
            title: '放电容量/Ah',
            width: 90,
            align: 'center',
            children: [
              {
                title: '放电容量1',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.outCapacity1',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '放电容量2',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.outCapacity2',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '放电容量3',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.outCapacity3',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '放电容量4',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.outCapacity4',
                //scopedSlots: {customRender: 'updateText'}
              }
            ]
          }, {
            title: '充电容量/Ah',
            width: 90,
            align: 'center',
            children: [
              {
                title: '充电容量1',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.inCapacity1',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '充电容量2',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.inCapacity2',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '充电容量3',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.inCapacity3',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '充电容量4',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.inCapacity4',
                //scopedSlots: {customRender: 'updateText'}
              }
            ]
          }, {
            title: '放电能量/Wh',
            width: 90,
            align: 'center',
            children: [
              {
                title: '放电能量1',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.outEnergy1',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '放电能量2',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.outEnergy2',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '放电能量3',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.outEnergy3',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '放电能量4',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.outEnergy4',
                //scopedSlots: {customRender: 'updateText'}
              }
            ]
          }, {
            title: '充电能量/Wh',
            width: 90,
            align: 'center',
            children: [
              {
                title: '充电能量1',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.inEnergy1',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '充电能量2',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.inEnergy2',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '充电能量3',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.inEnergy3',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '放电能量4',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.inEnergy4',
                //scopedSlots: {customRender: 'updateText'}
              }
            ]
          }, {
            title: 'DCR1/mΩ',
            width: 90,
            align: 'center',
            children: [
              {
                title: '搁置结束电压V₁/V',
                width: 120,
                align: 'center',
                dataIndex: 'dcrData.dcr1StopV1',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '放电结束电压V₂/V',
                width: 120,
                align: 'center',
                dataIndex: 'dcrData.dcr1OutV2',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '电流/A',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.dcr1A',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '计算DCR值',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.dcr1',
                //scopedSlots: {customRender: 'updateText'}
              }
            ]
          }, {
            title: 'DCR2/mΩ',
            width: 90,
            align: 'center',
            children: [
              {
                title: '搁置结束电压V₃/V',
                width: 120,
                align: 'center',
                dataIndex: 'dcrData.dcr2StopV3',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '放电结束电压V₄/V',
                width: 120,
                align: 'center',
                dataIndex: 'dcrData.dcr2OutV4',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '电流/A',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.dcr2A',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '计算DCR值',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.dcr2',
                //scopedSlots: {customRender: 'updateText'}
              }
            ]
          }, {
            title: 'DCR3/mΩ',
            width: 90,
            align: 'center',
            children: [
              {
                title: '搁置结束电压V₅/V',
                width: 120,
                align: 'center',
                dataIndex: 'dcrData.dcr3StopV5',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '放电结束电压V₆/V',
                width: 120,
                align: 'center',
                dataIndex: 'dcrData.dcr3OutV6',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '电流/A',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.dcr3A',
                //scopedSlots: {customRender: 'updateText'}
              }, {
                title: '计算DCR值',
                width: 90,
                align: 'center',
                dataIndex: 'dcrData.dcr3',
                //scopedSlots: {customRender: 'updateText'}
              }
            ]
          }
        ],
        activeKey: 'size',
        labelCol: {
          xs: {
            span: 12
          },
          sm: {
            span: 8
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 14
          }
        },
        visible: false,
        sampleNum: 0,
        orderNum: 0,
        record: {},
        confirmLoading: false,
        form: this.$form.createForm(this),
      }
    },

    methods: {
      ontestManSearch(e) {
        this.$refs.testManTable.refresh()
      },
      customtestManRow(row, index) {
        return {
          on: {
            click: () => {
              this.form.setFieldsValue({
                testMan: row.name,
                testManId: row.account
              })
              this.testMan = row.name
              this.testManDownVisible = false
            }
          }
        }
      },
      changeZero(event) {

        for (let i = 0; i < this.data.length; i++) {
          if (this.data[i].day == null) {
            if (i == 0) {
              this.data[i].day = 15
            } else if (i == 1) {
              this.data[i].day = 15
            } else {
              this.data[i].day = 30
            }
          }
        }

        //this.zero = moment(event).format('YYYY-MM-DD')
        if (this.data.length > 0) {
          this.data[0].inDate = moment(event).format('YYYY-MM-DD')
          if (this.data[0].day != null) {
            this.data[0].outDate = moment(event).add(this.data[0].day, 'days').format('YYYY-MM-DD')
          }
        }

        this.changeInOutDate(1);

      },

      changeInOutDate(index) {
        if (index == 0) {

          if (this.form.getFieldValue('zero') != null) {
            this.data[0].inDate = moment(this.form.getFieldValue('zero')).format('YYYY-MM-DD')

            if (this.data[0].day != null && this.data[0].inDate != null) {
              this.data[0].outDate = moment(this.data[0].inDate).add(this.data[0].day, 'days').format('YYYY-MM-DD')
            }
          }

          index += 1;
        }
        for (let i = index; i < this.data.length; i++) {
          let before = this.data[i - 1]
          if (before.outDate != null) {
            this.data[i].inDate = moment(before.outDate).add(3, 'days').format('YYYY-MM-DD')
            if (this.data[i].inDate != null && this.data[i].day != null) {
              this.data[i].outDate = moment(this.data[i].inDate).add(this.data[i].day, 'days').format('YYYY-MM-DD')
            }
          }
        }


      },

      changeNum($event) {

        if (this.data.length > $event) {
          this.data = this.data.slice(0, $event)
        } else {
          for (let i = this.data.length; i < $event; i++) {
            this.data.push({uuid: i * 10000 + 1000, day: null, inDate: null, outDate: null})
          }
        }

        this.changeInOutDate(0);


      },
      addData() {
        this.data.push({uuid: this.data.length * 10000 + 1000, day: null, inDate: null, outDate: null})
        this.form.setFieldsValue(
          {
            testNum: this.data.length
          }
        )

      },

      changeText($event,index,column){

        let keys = column.dataIndex.split('.')
        if(keys.length == 1){
          this.data[index][column.dataIndex] = $event.target.value
        }else{
          this.data[index][keys[0]][keys[1]] = $event.target.value
        }




      },

      changeDay($event, index, record) {

        /* if (this.data.length > index + 2 && this.data[index + 1].day != null) {

           if (this.data[index + 1].day <= $event) {
             this.$message.warn('天数应小于下次中检天数')
             return
           }
         }

         if (index > 0) {
           if (this.data[index - 1] != null) {

             if (this.data[index - 1].day >= $event) {
               this.$message.warn('天数应大于上次中检天数')
               return
             }
           }
         }*/


        record.day = $event
        this.changeInOutDate(index);
      },


      changeOrderNum() {
        this.data.forEach(d => {

          d.index = this.orderNum == 0 ? '初始性能' : '第' + this.orderNum + '次中检'

        })
      },
      add(detailId,id) {

        let param = {}
        if(id != null){
          param.progressId = id
          param.orderNumber = 0
        }else{
          param.detailId=detailId
        }
        centerList(param).then((res) => {
          this.visible = false
          if (res.success) {
            this.data = res.data
          } else {
            this.$message.error('查询失败：' + res.message)
          }
        }).finally((res) => {
          this.visible = true
        })


      },
      changeSampleNum($event) {
        if (this.data.length > $event) {
          this.data = this.data.slice(0, $event)
        } else {
          for (let i = this.data.length; i < $event; i++) {
            this.data.push({
              index: this.orderNum == 0 ? '初始性能' : '第' + this.orderNum + '次中检'

            })
          }
        }

      },


      onChangeSampleDate(date, dateString) {
        if (date == null) {
          this.startDate = ''
        } else {
          this.startDate = moment(date).format('YYYY-MM-DD')
        }
      },
      handleSubmit() {

        this.confirmLoading = true
        this.data.forEach(d => {
          d.orderNumber = this.orderNum
          d.progressId = this.record.id
        } )
        centerAdd(this.data).then((res) => {
          this.confirmLoading = false
          if (res.success) {
            this.$message.success('提交成功')
            this.handleCancel()
            this.$emit('ok', values)
          } else {
            this.$message.error('提交失败：' + res.message)
          }
        }).finally((res) => {
          this.confirmLoading = false
        })




      },
      handleCancel() {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
<style lang="less" scoped>
  .ant-form-item {

    margin-bottom: 0px;

  }

  .man_button {
    padding-left: 11px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  /deep/ .ant-modal-body {
    padding: 0 !important;
  }

  /deep/ .ant-table-thead > tr > th, /deep/ .ant-table-tbody > tr > td {
    padding: 3px;
  }

  /deep/ .ant-table-footer {

    padding: 0px;
  }

  /deep/ .ant-table-pagination.ant-pagination {
    margin: 5px 0;
  }

  /deep/ .ant-input-number {
    width: 100%;
  }

  /deep/ .ant-input-number-sm > .ant-input-number-input-wrap > .ant-input-number-input {
    text-align: center;
  }

  /deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
    color: #fff;
    background: #1890FF;
  }

  /deep/ input {
    width: 100%;
    border: 0;
  }
  /deep/ input:focus {
    outline: 0;
  }

</style>
