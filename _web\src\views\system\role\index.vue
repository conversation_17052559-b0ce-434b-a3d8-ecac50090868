<template>
  <div>
    <x-card v-if="hasPerm('sysRole:page')">
      <div slot="content" class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="角色名">
                <a-input @keyup.enter.native="$refs.table.refresh(true)" v-model="queryParam.name" allow-clear placeholder="请输入角色名"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="唯一编码">
                <a-input @keyup.enter.native="$refs.table.refresh(true)" v-model="queryParam.code" allow-clear placeholder="请输入唯一编码"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
              <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </x-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="false"
        :rowKey="(record) => record.id"
      >
        <template slot="operator" v-if="hasPerm('sysRole:add')">
          <a-button @click="$refs.addForm.add()" icon="plus" type="primary" v-if="hasPerm('sysRole:add')">新增角色</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a @click="openAddRoleUser(record)" v-if="hasPerm('sysRole:edit')">新增用户</a>
          <a-divider type="vertical" v-if="hasPerm('sysRole:edit')"/>
          <a @click="openRoleUser(record)" v-if="hasPerm('sysRole:edit')">角色用户</a>
          <a-divider type="vertical" v-if="hasPerm('sysRole:edit')"/>
          <a v-if="hasPerm('sysRole:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('sysRole:edit')"/>
          <a-dropdown v-if="hasPerm('sysRole:grantMenu') || hasPerm('sysRole:grantData') || hasPerm('sysRole:delete')">
            <a class="ant-dropdown-link">
              更多 <a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item v-if="hasPerm('sysRole:grantMenu')">
                <a @click="$refs.roleMenuForm.roleMenu(record)">授权菜单</a>
              </a-menu-item>
              <!-- <a-menu-item v-if="hasPerm('sysRole:grantData')">
                <a @click="$refs.roleOrgForm.roleOrg(record)">授权数据</a>
              </a-menu-item> -->
              <a-menu-item v-if="hasPerm('sysRole:delete')">
                <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => sysRoleDelete(record)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </s-table>

      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
      <role-menu-form ref="roleMenuForm" @ok="handleOk"/>
      <role-org-form ref="roleOrgForm" @ok="handleOk"/>

    </a-card>

    <a-modal
      title="角色用户"
      :visible="userVisible"
      :footer="null"
      @cancel="closeRoleUser"
    >

      <a-input @change="$refs.roleUserTable.refresh()" v-model="roleUserParam.userName" allow-clear placeholder="账号/姓名"/>

      <s-table
        ref="roleUserTable"
        :columns="roleUserColumns"
        :data="loadRoleUserData"
        :rowKey="(record) => record.id"
      >
        <span slot="action" slot-scope="text, record">

            <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => sysRoleUserDelete(record)">
              <a>删除</a>
            </a-popconfirm>

        </span>

      </s-table>
    </a-modal>

    <a-modal
      title="新增用户"
      :visible="addUserVisible"
      @cancel="closeRoleUsers"

    >

      <template slot="footer"  v-if="selectedRowKeys.length > 0" >
        <a-button key="confirm" type="primary"@click="addRoleUsers">
          <a>提交</a>
        </a-button>
        <a-button key="back" @click="closeRoleUsers">
          <a>关闭</a>
        </a-button>
      </template>
      <template slot="footer"  v-if="selectedRowKeys.length == 0" >
        <a-button key="back" @click="closeRoleUsers">
          <a>关闭</a>
        </a-button>
      </template>

      <template>

      </template>

      <a-input @change="getUserList" v-model="addUserParam.account" allow-clear placeholder="账号/姓名"/>

      <a-table
        :columns="addUserColumns"
        :dataSource="addUserData"
        :rowKey="(record) => record.account"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onUserSelectChange }"
      >
        <span slot="action" slot-scope="text, record">



        </span>

      </a-table>
    </a-modal>

  </div>
</template>

<script>
  import { STable, XCard } from '@/components'
  import { getRolePage, sysRoleDelete,roleUserPage,deleteRoleUser,addRoleUser,userList } from '@/api/modular/system/roleManage'
  import addForm from './addForm'
  import editForm from './editForm'
  import roleMenuForm from './roleMenuForm'
  import roleOrgForm from './roleOrgForm'
  export default {
    components: {
      XCard,
      STable,
      addForm,
      editForm,
      roleMenuForm,
      roleOrgForm
    },

    data () {
      return {
        role:{},
        addUserVisible:false,
        userVisible:false,
        roleUserParam:{},
        addUserParam:{},
        selectedRowKeys:[],
        selectedRows:[],
        // 查询参数
        queryParam: {},
        addUserData: [],
        // 表头
        roleUserColumns: [
          {
            title: '角色名',
            dataIndex: 'roleName'
          },
          {
            title: '账号',
            dataIndex: 'userAccount'
          },
          {
            title: '姓名',
            dataIndex: 'userName'
          },
          {
            title: '操作',
            dataIndex: 'action',
            scopedSlots: { customRender: 'action' }
          }
        ],
        addUserColumns: [

          {
            title: '账号',
            dataIndex: 'account'
          },
          {
            title: '姓名',
            dataIndex: 'name'
          }
        ],
        columns: [
          {
            title: '角色名',
            dataIndex: 'name'
          },
          {
            title: '唯一编码',
            dataIndex: 'code'
          },
          {
            title: '排序',
            dataIndex: 'sort'
          }
        ],
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return getRolePage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        loadRoleUserData: parameter => {
          return roleUserPage(Object.assign(parameter, this.roleUserParam)).then((res) => {
            return res.data
          })
        }
    }
    },

    created () {
      if (this.hasPerm('sysRole:edit') || this.hasPerm('sysRole:grantMenu') || this.hasPerm('sysRole:grantData') || this.hasPerm('sysRole:delete')) {
        this.columns.push({
          title: '操作',
          width: '250px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },

    methods: {
      sysRoleDelete (record) {
        sysRoleDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败：' + res.message)
          }
        }).catch((err) => {
          this.$message.error('删除错误：' + err.message)
        })
      },
      sysRoleUserDelete (record) {
        deleteRoleUser(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.roleUserTable.refresh()
          } else {
            this.$message.error('删除失败：' + res.message)
          }
        }).catch((err) => {
          this.$message.error('删除错误：' + err.message)
        })
      },

      handleOk () {
        this.$refs.table.refresh()
      },
      closeRoleUser(){
        this.roleUserParam = {}
        this.userVisible = false
      },
      openRoleUser(record){
        this.userVisible = true
        this.roleUserParam.id = record.id
        setTimeout(() => {
          this.$refs.roleUserTable.refresh()
        }, 200)

      },
      openAddRoleUser(record){
        this.role = record
        this.addUserVisible = true
        this.getUserList()
      },

      addRoleUsers(){
        let param = []
        for (let i = 0; i < this.selectedRows.length; i++) {
          param.push({roleId:this.role.id,userAccount:this.selectedRows[i].account})
        }
        addRoleUser(param).then((res) => {
          if (res.success) {
            this.$message.success('新增成功')
          } else {
            this.$message.error('新增失败：' + res.message)
          }
        }).catch((err) => {
          this.$message.error('新增失败：' + err.message)
        })
      },
      closeRoleUsers(){
        this.role = {}
        this.roleUserParam = {}
        this.addUserVisible = false

      },
      getUserList(){
        this.addUserParam.roleId = this.role.id
        userList(this.addUserParam).then((res) => {
          if (res.success) {
            this.selectedRows = []
            this.selectedRowKeys = []
            for (let i = 0; i < res.data.length; i++) {
              if(res.data[i].haveRole){
                if(this.selectedRows.find(s => s.account == res.data[i].account) == null ){
                  this.selectedRows.push(res.data[i])
                  this.selectedRowKeys.push(res.data[i].account)
                }
              }
            }

            this.addUserData = res.data
          } else {
            this.$message.error('查询失败：' + res.message)
          }
        }).catch((err) => {
          this.$message.error('查询失败：' + err.message)
        })
      },
      onUserSelectChange(keys,rows){
        this.selectedRowKeys = keys
        this.selectedRows = rows
      }

    }

  }
</script>

<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }

</style>
