import _ from "lodash";
import pageComponent from "@/views/system/vTestReport/components/pageComponent";
import PreviewDrawer from "@/views/system/vTestReport/components/previewDrawer";
import pbiReturnTop from '@/components/pageTool/components/pbiReturnTop.vue';
import html2canvas from "html2canvas";
import moment from "moment";

export const optionMergeCommon = {
  components: {
    pageComponent,
    PreviewDrawer,
    pbiReturnTop,
  },
  data: function () {
    return {
      editObjList: [],
      titleDataObj: {},
      testCondition: "",
      editObj: "", // 选中的图表类型

      xaxisType: '',

      echartObj: {},

      // 全局X轴最大值、间距
      globalXMax: 0,
      globalXInterval: 0,

      echartsColorList: [
        "#c00000",
        "#0070c0",
        "#808080",
        "#7030a0",
        "#4472c4",
        "#a5a5a5",
        "#ed7d31",
        "#5b9bd5",
        "#70ad47",
        "#000000",
        "#ff9999",
        "#ffc000",
        "#00b050"
      ],
      // 折点类型数组 三角、圆形、矩形、菱形、箭头、图钉、【空心：三角、圆形、矩形、菱形、箭头、图钉】、倒三角、五角星
      echartsSymbolList: [
        'triangle', 'circle', 'rect', 'diamond', 'arrow', 'pin',
        'emptyTriangle', 'emptyCircle', 'emptyRect', 'emptyDiamond', 'emptyArrow', 'emptyPin',
        'path://M0,0 L10,0 L5,10 Z',
        'path://M100,22.4 L78.6,54.6 L44.2,54.6 L72.6,79.4 L62.8,112.6 L100,92.4 L137.2,112.6 L127.4,79.4 L155.8,54.6 L121.4,54.6 Z'
      ],

      firstInit: {},
      drawerVisible: false,
      chartLegendNameListObj: {}, //不同类型图例的汇总  合并
      originalLegent: {}, //原始图例
      originalData: {}, //原始数据
      editData: {}, //编辑数据
      checkObj: {}, //暂时无用属性
    }

  },
  methods: {

    // 数据处理
    _getEchartOriginal(titleData, targetObj, hasYAxisTwo = false) {
      if (hasYAxisTwo) {
        // 次Y轴默认初始值
        titleData.yType2 = "value"
        titleData.yMin2 = targetObj === 'weight' ? -0.4 : -10
        titleData.yMax2 = targetObj === 'weight' ? 0.6 : 150
        titleData.yInterval2 = targetObj === 'weight' ? 0.2 : 20
      }

      return {
        titleTop: 10,

        yTitleLetf: targetObj === 'voltage' ? 45 : 35,
        yTitleRight: 35,
        xType: 'value',
        yType: 'value',
        yMin: targetObj === 'voltage' ? 3000 : 70,
        yMax: targetObj === 'voltage' ? 4500 : 110,
        yInterval: targetObj === 'voltage' ? 300 : 5,

        legendWidth: 20,
        legendHeight: targetObj === 'height' ? 7 : 5,
        legendGap: 5, //图例间隙
        legendOrient: 'vertical',
        legendBgColor: 'none',
        legendFontSize: 12,
        legendBottom: 80,
        legendRight: hasYAxisTwo ? 80 : 45,

        gridTop: 40,
        gridLeft: targetObj === 'voltage' ? 75 : 70,
        gridRight: hasYAxisTwo ? 70 : 35,
        gridBottom: 70,

        ...titleData,
      }
    },
    _handleEchartData(targetObj, xaxisType = '', yAxisOneList, yAxisTwoList = [], hasYAxisTwo = false) {
      yAxisOneList = Array.isArray(yAxisOneList) ? yAxisOneList : []

      let titleData = this._getEchartOriginal(this.titleDataObj[targetObj], targetObj, hasYAxisTwo)

      // 5个图需要计算Y轴最大最小值及左边长度
      if (['innerres','height','volume','weight','isolateres'].includes(targetObj)) {
        let yAxisOneDataList = []
        yAxisOneList.forEach(seriesItem => {
          yAxisOneDataList.push(...seriesItem.data.map(mapItem => Number(mapItem[1])))
        })

        const rangeValue = this._getYAxisRadius(Math.max.apply(null, yAxisOneDataList), Math.min.apply(null, yAxisOneDataList))
        let yMax = rangeValue[0]
        let yMin = rangeValue[1]
        let yInterval = null

        if (targetObj === 'weight') {
          // 例如：重量最大值135.5，向下取整得到130，间隔为10，5个间隔，上2下3，150 140 130 120 110 100
          const number = Math.max.apply(null, yAxisOneDataList)
          const value = isFinite(number) && typeof number === 'number' ? Math.floor(number / 10) * 10 : 0
          yMax = value + 20
          yMin = value - 30
          yInterval = 10
        }

        const valueLength = yMax.toString().length * 7 + (yMax >= 4 ? 0 : 9)
        console.log("targetObj, yaxis valueLength: ", targetObj, valueLength)

        // 重量图次Y轴
        // if (targetObj === 'weight') {
        //   // 重新计算主Y轴的最小值和间隔值，使得分割段数为5
        //   let newDiff = (yMax - yMin) / 5 > 1 ? Math.ceil((yMax - yMin) / 5) * 5 : 5
        //   yMin = yMax - newDiff
        //   yInterval = newDiff / 5
        // }

        titleData.yMax = yMax
        titleData.yMin = yMin
        if (yInterval === null) {
          delete titleData.yInterval
        } else {
          titleData.yInterval = yInterval
        }
        titleData.yTitleLetf = valueLength + 15
        titleData.gridLeft = valueLength + 15 + 35
      }

      let seriesList = []

      let lineColorObj = {} // 折线颜色
      let lineSymbolObj = {} // 折点类型

      // 只有首次加载才赋值
      this.originalLegent[targetObj] = []  // 原始图例
      this.originalData[targetObj] = titleData  //大原始值（图例宽度啥的）
      this.originalData[targetObj].series = []   //小原始值（每条线对应的属性）（无层级）
      this.originalData[targetObj].originalSeries = []  //小原始值（每条线对应的原始series）（有层级）
      this.editData[targetObj] = {}  //编辑数据

      const legendNameType = titleData.legendNameType || 'sampleCode'

      for (let i = 0; i < yAxisOneList.length; i++) {
        let sizeType = yAxisOneList[i].sizeType
        let sampleCode = yAxisOneList[i].sampleCode

        let yAxisOneLegendId = (targetObj === 'height' ? sizeType + '-' : '') + sampleCode
        let yAxisOneLegendName = (targetObj === 'height' ? sizeType + '-' : '') + yAxisOneList[i][legendNameType]

        this.chartLegendNameListObj[targetObj].push({
          sampleCode:  (targetObj === 'height' ? sizeType + '-' : '') + sampleCode,
          batteryCode: (targetObj === 'height' ? sizeType + '-' : '') + yAxisOneList[i].batteryCode
        })

        if (targetObj === 'height') {
          if (!lineColorObj.hasOwnProperty(sizeType)) {
            lineColorObj[sizeType] = this.echartsColorList[Object.keys(lineColorObj).length % this.echartsColorList.length]
          }
          if (!lineSymbolObj.hasOwnProperty(sampleCode)) {
            lineSymbolObj[sampleCode] = this.echartsSymbolList[Object.keys(lineSymbolObj).length % this.echartsSymbolList.length]
          }
        } else {
          if (!lineColorObj.hasOwnProperty(sampleCode)) {
            lineColorObj[sampleCode] = this.echartsColorList[Object.keys(lineColorObj).length % this.echartsColorList.length]
          }
        }

        const temColor = targetObj === 'height' ? lineColorObj[sizeType] : lineColorObj[sampleCode]
        const temSymbol = targetObj === 'height' ? lineSymbolObj[sampleCode] : 'rect'

        const seriesTemplate = {
          name: yAxisOneLegendName,
          type: 'line',
          sampling: 'lttb',
          large: true,
          barGap: 0,
          symbol: temSymbol,
          symbolSize: targetObj === 'height' ? 7 : 5,
            markPoint: {
            data: []
          },
        }
        const seriesOriginalTemplate = {
          index: i + 1,
          name: yAxisOneLegendName,
          soc: yAxisOneLegendName,
          type: 'line',
          sampling: 'lttb',
          large: true,
          barGap: 0,
          symbol: temSymbol,
          symbolSize: targetObj === 'height' ? 7 : 5,
          maxPoint: false,
          minPoint: false,
          connectNulls: false,
          lineWidth: 1,
          lineColor: temColor,
          itemColor: temColor
        }

        let series = [
          {
            id: yAxisOneLegendId,
            lineStyle: {
              width: 1,
              type: 'solid',
              color: temColor
            },
            itemStyle: {
              color: temColor
            },
            data: yAxisOneList[i].data.map((item, index) => {
              let lineName1 = titleData.tooltipPrefix1 || ''
              if (['voltage','innerres','height','volume','weight','isolateres'].includes(targetObj)) {
                lineName1 = item[2] + (lineName1 ? '-' + lineName1 : '')
              }
              return {id: index, value: xaxisType === 'cycle' || xaxisType === '' ? item : [item[2], item[1]], lineName: lineName1, unit: titleData.tooltipUnit1}
            }),
            ...seriesTemplate,
          }
        ]

        // 只有首次加载才赋值
        this.originalLegent[targetObj].push(yAxisOneLegendName) // 原始图例
        const originalSeries = [{         //小原始值
          id: yAxisOneLegendId,
          lineType: 'solid',
          synchronization: seriesList.length,
          ...seriesOriginalTemplate
        }]

        if (yAxisTwoList.length > 0) {
          series.push(
              {
                id: yAxisTwoList[i].sampleCode + 'two',
                yAxisIndex: 1,
                lineStyle: {
                  width: 1,
                  type: 'dashed',
                  color: temColor
                },
                itemStyle: {
                  color: temColor
                },
                data: yAxisTwoList[i].data.map((item, index) => {
                  let lineName2 = titleData.tooltipPrefix2
                  if (['voltage','innerres','height','volume','weight','isolateres'].includes(targetObj)) {
                    lineName2 = item[2] + (lineName2 ? '-' + lineName2 : '')
                  }
                  return {id: index, value: xaxisType === 'cycle' || xaxisType === '' ? item : [item[2], item[1]], lineName: lineName2, unit: titleData.tooltipUnit2}
                }),
                ...seriesTemplate,
              }
            )

            originalSeries.push(
              {
                id: yAxisTwoList[i].sampleCode + 'two',
                lineType: 'dashed',
                synchronization: seriesList.length,
                ...seriesOriginalTemplate
              }
          )
        }

        // 每根线的属性
        this.originalData[targetObj].series.push(...originalSeries)
        seriesList.push(...series)
      }

      // 根据 图例数量 确定 legendTop，legendLeft 原始值赋值
      const legendRevealList = this.originalLegent[targetObj];
      if (Array.isArray(legendRevealList) && legendRevealList.length > 0) {
        const legendLength = this.handleValueLength(legendRevealList[0])
        this.originalData[targetObj].legendTop = 415 - this.originalData[targetObj].legendBottom - Math.min(legendRevealList.length, 6) * (this.originalData[targetObj].legendHeight + this.originalData[targetObj].legendGap + 6)
        this.originalData[targetObj].legendLeft = 595 - this.originalData[targetObj].legendRight - legendLength - this.originalData[targetObj].legendWidth - 6
      }

      // 只有首次加载才将原始数据给编辑数据
      // 每根线的原始series
      this.originalData[targetObj].originalSeries = _.cloneDeep(seriesList)
      this.editData[targetObj] = _.cloneDeep(this.originalData[targetObj])  //编辑数据
      this.editData[targetObj].editSeries = _.cloneDeep(this.originalData[targetObj].originalSeries)
      this.editData[targetObj].legend = _.cloneDeep(this.originalLegent[targetObj])
      this.editData[targetObj].legendSort = _.cloneDeep(this.originalLegent[targetObj])
      this.editData[targetObj].legendRevealList = _.cloneDeep(this.originalLegent[targetObj]).slice(0, 6)
      this.editData[targetObj].legendEditName = _.cloneDeep(this.originalLegent[targetObj]).map(v => {
        return {originName: v, previousName: '', newName: '', isReset: false}
      })

      return seriesList
    },
    _handleEchartOptions(targetObj, seriesList, hasYAxisTwo = false) {

      const options = {
        backgroundColor: '#ffffff',
        animationDuration: 2000,
        tooltip: {
          trigger: "axis",
          confine: true,
          formatter: function (params) {
            var result = params[0].axisValue // 添加 x 轴的数值

            // 添加 绝对时间
            if (params[0].data.absoluteTime) {
              let absoluteTime = ''
              let date = moment(params[0].data.absoluteTime, 'YYYY/MM/DD HH:mm:ss.SSS')
              absoluteTime = date.isValid() ? date.format('YYYY-MM-DD HH:mm:ss') : params[0].data.absoluteTime
              result += '<div style="width:20px;display: inline-block;"></div>' + absoluteTime + "<br>"
            } else if (['voltage','innerres','height','volume','weight','isolateres'].includes(targetObj)) {
              result += "<br>"

              if(params[0].value[2] && params[0].value[3]){
                result += params[0].value[2] + ":" + params[0].value[3] +  "<br>" // 添加 出箱后 时间
              }

              if(params[0].value[2] && !params[0].value[3]){
                result += params[0].value[2] +  "<br>" // 添加 出箱后 时间
              }

              if(params.length > 1 && params[0].value[0] == params[1].value[0] && params[0].seriesId == params[1].seriesId){
                result += params[1].value[2] + ":" + (params[1].value[3]?params[1].value[3]:"" )+  "<br>" // 添加 中检后 时间
              }
            } else {
              result += "<br>"
            }

            params.forEach(function (item, dataIndex) {
              result +=
                item.marker +
                item.seriesName +
                (item.data.lineName ? '<div style="width:10px;display: inline-block;"></div>' + item.data.lineName : '') +
                '<div style="width:20px;display: inline-block;"></div>' +
                (item.value[1] !== null ? (item.value[1] >= 10000000000 ? "异常值" : item.value[1]) : '') +
                (item.value[1] !== null ? (item.data.unit || '') : '') +
                "<br>" // 添加每个系列的数值
            })
            return result
          }
        },

        title: {
          text: this.firstInit[targetObj] ? this.originalData[targetObj].chartTitle : this.editData[targetObj].chartTitle,
          left: 'center',
          top: this.firstInit[targetObj] ? this.originalData[targetObj].titleTop : this.editData[targetObj].titleTop,
          textStyle: {
            fontSize: 16,
            fontWeight: 500,
            color: "#000"
          }
        },
        grid: {
          show: true,
          top: this.firstInit[targetObj] ? this.originalData[targetObj].gridTop : this.editData[targetObj].gridTop,
          left: this.firstInit[targetObj] ? this.originalData[targetObj].gridLeft : this.editData[targetObj].gridLeft,
          right: this.firstInit[targetObj] ? this.originalData[targetObj].gridRight : this.editData[targetObj].gridRight,
          bottom: this.firstInit[targetObj] ? this.originalData[targetObj].gridBottom : this.editData[targetObj].gridBottom,
          borderWidth: 0.5,
          borderColor: "#ccc"
        },
        textStyle: {
          fontFamily: "Times New Roman"
        },
        legend: {
          data: this.editData[targetObj].legendRevealList,
          backgroundColor: this.firstInit[targetObj] ? this.originalData[targetObj].legendBgColor : this.editData[targetObj].legendBgColor,
          itemWidth: this.firstInit[targetObj] ? this.originalData[targetObj].legendWidth : this.editData[targetObj].legendWidth,
          itemHeight: this.firstInit[targetObj] ? this.originalData[targetObj].legendHeight : this.editData[targetObj].legendHeight,
          itemGap: this.firstInit[targetObj] ? this.originalData[targetObj].legendGap : this.editData[targetObj].legendGap,
          orient: this.firstInit[targetObj] ? this.originalData[targetObj].legendOrient : this.editData[targetObj].legendOrient,
          // top: this.firstInit[targetObj] && this.editData[targetObj].legendTop ? this.editData[targetObj].legendTop:null,
          // left: this.firstInit[targetObj] &&  this.editData[targetObj].legendLeft ?this.editData[targetObj].legendLeft:null ,
          right: this.firstInit[targetObj] ? this.originalData[targetObj].legendRight : this.editData[targetObj].legendRight,
          bottom: this.firstInit[targetObj] ? this.originalData[targetObj].legendBottom : this.editData[targetObj].legendBottom,
          padding: [0, 0],
          textStyle: {
            fontSize: this.firstInit[targetObj] ? this.originalData[targetObj].legendFontSize : this.editData[targetObj].legendFontSize,
            color: "#000000"
          }
        },
        xAxis: [
          {
            name: this.firstInit[targetObj] ? this.originalData[targetObj].XTitle : this.editData[targetObj].XTitle,
            type: this.firstInit[targetObj] ? this.originalData[targetObj].xType : this.editData[targetObj].xType,
            nameLocation: 'middle', // 将名称放在轴线的中间位置
            nameGap: 30,
            nameTextStyle: {
              fontSize: 14, // 可以根据需要调整字体大小
              fontWeight: 500,
              color: "#000000" // 可以根据需要调整字体大小
            },
            axisTick: {show: false},
            axisLabel: {
              show: true,
              width: 0.5,
              fontSize: 14,
              color: "#000000",
              formatter: function (value) {
                return value
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              },
              onZero: false, // 次Y轴为数值轴且包含0刻度, 确保X轴的轴线不在次Y轴的0刻度上
            },
            splitLine: {
              show: true, // 显示分隔线
              lineStyle: {
                type: "solid",
                width: 0.5
              }
            },
            minInterval: 1,
          }
        ],
        yAxis: [
          {
            name: this.firstInit[targetObj] ? this.originalData[targetObj].YTitle : this.editData[targetObj].YTitle,
            type: this.firstInit[targetObj] ? this.originalData[targetObj].yType : this.editData[targetObj].yType,
            nameGap: this.firstInit[targetObj] ? this.originalData[targetObj].yTitleLetf : this.editData[targetObj].yTitleLetf,
            position: 'left',
            min: this.firstInit[targetObj] ? this.originalData[targetObj].yMin : this.editData[targetObj].yMin,
            max: this.firstInit[targetObj] ? this.originalData[targetObj].yMax : this.editData[targetObj].yMax,
            nameLocation: 'middle', // 将名称放在轴线的起始位置
            nameRotate: 90, // 旋转角度，使名称竖排
            nameTextStyle: {
              fontSize: 14, // 可以根据需要调整字体大小
              fontWeight: 500,
              color: "#000000"
            },
            splitLine: {
              show: true,  // 显示分隔线
              lineStyle: {
                type: 'solid',  // 设置分隔线的样式，比如虚线
                width: 0.5
              }
            },
            axisTick: {
              show: false,  // 显示刻度
            },
            axisLabel: {
              show: true,
              width: 0.5,
              fontSize: 14,
              color: "#000000",
              formatter: function (value) {
                return value
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              }
            },
          }
        ],
        series: seriesList
      }

      if (hasYAxisTwo) {
        options.yAxis.push(
          {
            name: this.firstInit[targetObj] ? this.originalData[targetObj].YTitle2 : this.editData[targetObj].YTitle2,
            type: this.firstInit[targetObj] ? this.originalData[targetObj].yType2 : this.editData[targetObj].yType2,
            position: 'right',
            min: this.originalData[targetObj].yMin2,
            max: this.originalData[targetObj].yMax2,
            interval: this.originalData[targetObj].yInterval2,
            nameGap: this.firstInit[targetObj] ? this.originalData[targetObj].yTitleRight : this.editData[targetObj].yTitleRight,
            nameLocation: 'middle', // 将名称放在轴线的起始位置
            nameRotate: 90, // 旋转角度，使名称竖排
            nameTextStyle: {
              fontSize: 14,
              fontWeight: 500,
              color: "#000000" // 可以根据需要调整字体大小
            },
            splitLine: {
              show: true,  // 显示分隔线
              lineStyle: {
                type: 'solid'  // 设置分隔线的样式，比如虚线
              }
            },
            axisTick: {
              show: true,  // 显示刻度
            },
            axisLabel: {
              show: true,
              width: 0.5,
              fontSize: 14,
              color: "#000000",
              formatter: function (value) {
                return value
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              }
            },
          }
        )
      }

      if (this.originalData[targetObj].yInterval) {
        options.yAxis[0].interval = this.originalData[targetObj].yInterval
      }

      // 非首次加载才需要
      //处理图例
      //图例名称
      if (!this.firstInit[targetObj]) {
        const newSeriesList = []
        _.cloneDeep(seriesList).forEach(v => {
          const haveNameList = this.editData[targetObj].legendEditName.filter(filterItem => filterItem.originName === v.name && filterItem.newName)
          v.name = haveNameList.length === 0 ? v.name : haveNameList[0].newName
          newSeriesList.push(v)
        })
        options.series = newSeriesList

        const legend = []
        this.editData[targetObj].legendSort.forEach(v => {
          if (this.editData[targetObj].legend.includes(v) && this.editData[targetObj].legendRevealList.includes(v)) {
            const haveList = this.editData[targetObj].legendEditName.filter(filterItem => filterItem.originName === v && filterItem.newName)
            legend.push(haveList.length === 0 ? v : haveList[0].newName)
          }
        })
        options.legend.data = legend
      }


      // 如果非首次编辑
      // 默认是水平，如果是水平，就不添加，垂直才添加
      if (this.editData[targetObj].legendOrient === 'vertical') {
        options.legend.orient = this.editData[targetObj].legendOrient
      }
      if (this.editData[targetObj].legendLeft !== undefined) {
        // options.legend.left = this.editData[targetObj].legendLeft
      }

      // X轴可能没有最大最小值、间隔
      if (!this.firstInit[targetObj] && this.editData[targetObj].xInterval && this.editData[targetObj].xType === 'value') {
        options.xAxis[0].min = this.editData[targetObj].xMin
        options.xAxis[0].max = this.editData[targetObj].xMax
        options.xAxis[0].interval = this.editData[targetObj].xInterval
      }
      if (!this.firstInit[targetObj] && this.editData[targetObj].yType === 'value') {
        options.yAxis[0].min = this.editData[targetObj].yMin
        options.yAxis[0].max = this.editData[targetObj].yMax
        options.yAxis[0].interval = this.editData[targetObj].yInterval
      }
      if (hasYAxisTwo && !this.firstInit[targetObj] && this.editData[targetObj].yType2 === 'value') {
        options.yAxis[1].min = this.editData[targetObj].yMin2
        options.yAxis[1].max = this.editData[targetObj].yMax2
        options.yAxis[1].interval = this.editData[targetObj].yInterval2
      }
      return options
    },
    _handleYAxisValue(targetObj, hasYAxisTwo) {
      const XAxis = this.echartObj[targetObj].getModel().getComponent("xAxis", 0).axis.scale
      this.editData[targetObj].xMin = XAxis._extent[0]
      this.editData[targetObj].xMax = XAxis._extent[1]
      this.editData[targetObj].xInterval = XAxis._interval

      this.originalData[targetObj].xMin = XAxis._extent[0]
      this.originalData[targetObj].xMax = XAxis._extent[1]
      this.originalData[targetObj].xInterval = XAxis._interval

      const YAxis1 = this.echartObj[targetObj].getModel().getComponent("yAxis", 0).axis.scale

      this.editData[targetObj].yMin = YAxis1._extent[0]
      this.editData[targetObj].yMax = YAxis1._extent[1]
      this.editData[targetObj].yInterval = YAxis1._interval

      this.originalData[targetObj].yMin = YAxis1._extent[0]
      this.originalData[targetObj].yMax = YAxis1._extent[1]
      this.originalData[targetObj].yInterval = YAxis1._interval

      if (hasYAxisTwo) {
        const YAxis2 = this.echartObj[targetObj].getModel().getComponent("yAxis", 1).axis.scale

        this.editData[targetObj].yMin2 = YAxis2._extent[0]
        this.editData[targetObj].yMax2 = YAxis2._extent[1]
        this.editData[targetObj].yInterval2 = YAxis2._interval

        this.originalData[targetObj].yMin2 = YAxis2._extent[0]
        this.originalData[targetObj].yMax2 = YAxis2._extent[1]
        this.originalData[targetObj].yInterval2 = YAxis2._interval
      }
    },

    // 获得y轴最大最小值
    _getYAxisRadius(originalMax, originalMin) {
      const differenceValue = originalMax - originalMin

      const transferMax = Math.trunc(originalMax + differenceValue).toString().split("")
      const transferMin = Math.trunc(originalMin - differenceValue).toString().split("")

      const newMax = Number(transferMax.map((mapItem, mapIndex) => mapIndex == 0 ? (Number(mapItem) + 1).toString() : '0').join(""))
      const newMin = Number(transferMin.map((mapItem, mapIndex) => mapIndex == 0 ? mapItem : '0').join(""))

      return [newMax, newMin]
    },
    handleValueLength(data) {
      const element = document.getElementById('revealText12')
      element.innerHTML = data
      element.style.fontSize = '12px'
      element.offsetWidth

      console.log("data, element.clientWidth: ", data, element.clientWidth)
      return element.clientWidth
    },

    // 在线编辑图表
    handleEditEcharts(target) {
      this.editObj = target
      this.drawerVisible = true
    },

    // 生成
    handleDrawerSubmit(value) {
      const isEdit = !!value.targetEditObj

      // 编辑数据更新  //  编辑时
      if (isEdit) {
        if (value.targetEditIndex === undefined) {
          this.editData[this.editObj][value.targetEditObj] = value[value.targetEditObj]
        } else if (value.targetEditIndex === 'all' || value.targetEditObj === '"synchronization"' || value.targetEditObj === 'legendEditName') {
        } else {
          this.editData[this.editObj].series[value.targetEditIndex][value.targetEditObj] = value.checkData[value.targetEditIndex][value.targetEditObj]
        }
        // 编辑数据更新  //  重置时
      } else {
        if (value.targetResetIndex === undefined) {
          this.editData[this.editObj][value.targetResetObj] = value[value.targetResetObj]
        } else if (value.targetResetIndex === 'all' || value.targetResetIndex === '"synchronization"' || value.targetResetIndex === 'legendEditName') {
        } else {
          this.editData[this.editObj].series[value.targetResetIndex][value.targetResetObj] = value.checkData[value.targetResetIndex][value.targetResetObj]
        }
      }

      const titleOptions = ['chartTitle', 'titleTop']
      const xAxisOptions = ['XTitle', 'xType', 'xMin', 'xMax', 'xInterval']
      const yAxisOptions = ['YTitle', 'YTitle2', 'yType', 'yType2', 'yMin', 'yMin2', 'yMax', 'yMax2', 'yInterval', 'yInterval2', 'yTitleLetf', 'yTitleRight']
      const gridOptions = ['gridTop', 'gridLeft', 'gridRight', 'gridBottom']
      const legendOptions = ['legendBgColor', 'legendOrient', 'legendTop', 'legendLeft', 'legendWidth', 'legendHeight', 'legendGap']
      const legendListOptions = ['legendList', 'legendEditName', 'legendSort', 'legendRevealList']
      const dataLabelOptions = ['maxPoint', 'minPoint', 'connectNulls', 'symbol', 'symbolSize', 'itemColor', 'lineType', 'lineWidth', 'lineColor']

      let targetObj = isEdit ? value.targetEditObj : value.targetResetObj  // 需要修改或者重置的目标值
      let targetContentText = isEdit ? 'targetEditObj' : 'targetResetObj'   // 目标内容文本

      if (legendListOptions.includes(value.targetEditObj)) {
        return this._handleEchartsLegend(value.targetEditObj, value[value.targetEditObj])
      }

      // 标题 修改 targetEditObj  // 标题  重置 targetResetObj
      if (titleOptions.includes(value[targetContentText])) {
        if (value[targetContentText] === 'chartTitle') targetObj = 'text'
        if (value[targetContentText] === 'titleTop') targetObj = 'top'
        return this._handleEchartsNormal('title', targetObj, value[value[targetContentText]])
      }

      // 图例 修改 targetEditObj  // 图例  重置 targetResetObj
      if (legendOptions.includes(value[targetContentText])) {
        if (value[targetContentText] === 'legendBgColor') targetObj = 'backgroundColor'
        if (value[targetContentText] === 'legendOrient') targetObj = 'orient'
        if (value[targetContentText] === 'legendTop') targetObj = 'top'
        if (value[targetContentText] === 'legendLeft') targetObj = 'left'
        if (value[targetContentText] === 'legendWidth') targetObj = 'itemWidth'
        if (value[targetContentText] === 'legendHeight') targetObj = 'itemHeight'
        if (value[targetContentText] === 'legendGap') targetObj = 'itemGap'
        return this._handleEchartsNormal('legend', targetObj, value[value[targetContentText]])
      }

      // X轴 修改 targetEditObj  // X轴  重置 targetResetObj
      if (xAxisOptions.includes(value[targetContentText])) {
        if (value[targetContentText] === 'XTitle') targetObj = 'name'
        if (value[targetContentText] === 'xType') targetObj = 'type'
        if (value[targetContentText] === 'xMin') targetObj = 'min'
        if (value[targetContentText] === 'xMax') targetObj = 'max'
        if (value[targetContentText] === 'xInterval') targetObj = 'interval'
        return this._handleEchartsXAxis(targetObj, value[value[targetContentText]])
      }

      // Y轴 修改 targetEditObj  // Y轴  重置 targetResetObj
      if (yAxisOptions.includes(value[targetContentText])) {
        if (value[targetContentText] === 'YTitle' || value[targetContentText] === 'YTitle2') targetObj = 'name'
        if (value[targetContentText] === 'yTitleLetf' || value[targetContentText] === 'yTitleRight') targetObj = 'nameGap'
        if (value[targetContentText] === 'yType' || value[targetContentText] === 'yType2') targetObj = 'type'
        if (value[targetContentText] === 'yMin' || value[targetContentText] === 'yMin2') targetObj = 'min'
        if (value[targetContentText] === 'yMax' || value[targetContentText] === 'yMax2') targetObj = 'max'
        if (value[targetContentText] === 'yInterval' || value[targetContentText] === 'yInterval2') targetObj = 'interval'
        return this._handleEchartsYAxis(value[targetContentText], targetObj, value[value[targetContentText]])
      }

      // 数据标签 修改 targetEditObj // 数据标签  重置 targetResetObj
      if (value.targetEditIndex === 'all' || value.targetResetIndex === 'all') {
        if (value[targetContentText] === 'lineType') targetObj = 'type'
        if (value[targetContentText] === 'lineWidth') targetObj = 'width'
        if (value[targetContentText] === 'itemColor' || value[targetContentText] === 'lineColor') targetObj = 'color'
        return this._handleEchartsAllDataLabel(value[targetContentText], targetObj, value.targetEditIndex === 'all' ? value.allData[value[targetContentText]] : 'resetAll')
      }

      if (dataLabelOptions.includes(value[targetContentText])) {
        if (value[targetContentText] === 'maxPoint') targetObj = 'max'
        if (value[targetContentText] === 'minPoint') targetObj = 'min'

        if (value[targetContentText] === 'lineType') targetObj = 'type'
        if (value[targetContentText] === 'lineWidth') targetObj = 'width'
        if (value[targetContentText] === 'itemColor' || value[targetContentText] === 'lineColor') targetObj = 'color'

        return this._handleEchartsDataLabel(value[targetContentText], targetObj, value.checkData[isEdit ? value.targetEditIndex : value.targetResetIndex][value[targetContentText]], isEdit ? value.targetEditIndex : value.targetResetIndex)
      }

      if (value.targetEditObj === 'synchronization') {
        return this._handleEchartsSynchronization(value.targetEditIndex, value.checkData[value.targetEditIndex].synchronization)
      }

      // 图表位置 修改 targetEditObj  // 图表位置  重置 targetResetObj
      if (gridOptions.includes(value[targetContentText])) {
        if (value[targetContentText] === 'gridTop') targetObj = 'top'
        if (value[targetContentText] === 'gridLeft') targetObj = 'left'
        if (value[targetContentText] === 'gridRight') targetObj = 'right'
        if (value[targetContentText] === 'gridBottom') targetObj = 'bottom'
        return this._handleEchartsNormal('grid', targetObj, value[value[targetContentText]])
      }

      // 修改图例名称
      if (this.editData[this.editObj].legendNameType !== value.legendNameType) {
        this.drawerVisible = false
        this.firstInit[this.editObj] = true
        this.titleDataObj[this.editObj].legendNameType = value.legendNameType
        this.handleInitChart(this.editObj)
      }

    },

    // 重置
    handleDrawerReset() {
      this.firstInit[this.editObj] = true
      this.drawerVisible = false

      this.handleInitChart(this.editObj)
    },

    _handleEchartsLegend(targetObj, targetValue, targetIndex) {
      const option = { legend: {}, series: [] }

      let legend = []

      // 名称
      if (targetObj === 'legendEditName') {
        this.editData[this.editObj].legendSort.forEach(v => {
          // 处理 legend
          // 条件1 : 数据勾选
          const conditions1 = this.editData[this.editObj].legend.includes(v)
          // 条件2 : 显隐勾选
          const conditions2 = this.editData[this.editObj].legendRevealList.includes(v)
          // 是否改名 如果改了名就用改的名，如果没改，就用原来的名
          const name = this.editData[this.editObj].legendEditName.filter(filterItem => filterItem.newName && (v === filterItem.originName))
          if (conditions1 && conditions2) legend.push(name.length === 0 ? v : name[0].newName)
        })

        _.cloneDeep(this.editData[this.editObj].editSeries).forEach((v, vIndex) => {
          // 是否改名
          const name = this.editData[this.editObj].legendEditName.filter(filterItem => filterItem.newName && (v.name === filterItem.originName))
          option.series[vIndex] = v
          option.series[vIndex].name = name.length === 0 ? v.name : name[0].newName
        })

        option.legend.data = legend
        return this.echartObj[this.editObj].setOption(option)
      }

      // 数据
      if (targetObj === 'legendList') {
        const seriesList = []
        this.editData[this.editObj].legendSort.forEach(v => {
          // 处理 series
          if (targetValue.includes(v)) {
            let haveList = this.editData[this.editObj].editSeries.filter(filterItem => filterItem.name === v)
            if (haveList.length === 0) {
              haveList = _.cloneDeep(this.editData[this.editObj].originalSeries).filter(filterItem => filterItem.name === v)
              // 同步之前的修改样式
              haveList.forEach(forItem => {
                const have = this.editData[this.editObj].series.filter(filterItem => filterItem.id === forItem.id)

                forItem.symbol = have[0].symbol
                forItem.symbolSize = have[0].symbolSize

                forItem.connectNulls = Boolean(Number(have[0].connectNulls))

                forItem.itemStyle.color = have[0].itemColor

                forItem.lineStyle.type = have[0].lineType
                forItem.lineStyle.width = have[0].lineWidth
                forItem.lineStyle.color = have[0].lineColor

                forItem.markPoint = { data: [] }
                if (have[0].maxPoint) forItem.markPoint.data.push({ type: "max", name: "Max" })
                if (have[0].minPoint) forItem.markPoint.data.push({ type: "min", name: "Min" })
              })
            }

            seriesList.push(...haveList)
          }
          // 处理 legend
          // 条件1 : 数据勾选
          const conditions1 = targetValue.includes(v)
          // 条件2 : 显隐勾选
          const conditions2 = this.editData[this.editObj].legendRevealList.includes(v)

          const name = this.editData[this.editObj].legendEditName.filter(filterItem => filterItem.originName === v && filterItem.newName)
          if (conditions1 && conditions2) legend.push(name.length === 0 ? v : name[0].newName)
        })
        this.editData[this.editObj].legend = targetValue

        this.editData[this.editObj].editSeries = _.cloneDeep(seriesList)   //这个需要保留原值

        // 是否改名
        seriesList.forEach((v, vIndex) => {
          const name = this.editData[this.editObj].legendEditName.filter(filterItem => filterItem.originName === v.name && filterItem.newName)
          seriesList[vIndex].name = name.length === 0 ? v.name : name[0].newName
        })


        // 重新生成图表
        return this._handleInitLegendList(seriesList, legend)
      }

      // 排序
      if (targetObj === 'legendSort') {
        this.editData[this.editObj].legendSort.forEach(v => {
          // 是否改名
          const name = this.editData[this.editObj].legendEditName.filter(filterItem => filterItem.originName === v && filterItem.newName)
          if (this.editData[this.editObj].legendRevealList.includes(v)) legend.push(name.length === 0 ? v : name[0].newName)
        })
      }

      // 显隐
      if (targetObj === 'legendRevealList') {
        this.editData[this.editObj].legendSort.forEach(v => {
          // 是否改名
          const name = this.editData[this.editObj].legendEditName.filter(filterItem => filterItem.originName === v && filterItem.newName)
          if (targetValue.includes(v)) legend.push(name.length === 0 ? v : name[0].newName)
        })
      }

      option.legend.data = legend
      this.echartObj[this.editObj].setOption(option)
    },
    _handleEchartsNormal(editObj, targetObj, targetValue) {
      this.echartObj[this.editObj].setOption({
        [editObj]: {
          [targetObj]: targetValue
        }
      })
    },
    _handleEchartsXAxis(targetObj, targetValue) {
      const option = { xAxis: [{}] }
      // 保留设置用户上一次留下的值,最大最小以及间隔值
      if (targetObj === 'type' && targetValue === 'value' && this.editData[this.editObj].xInterval) {
        option.xAxis[0].min = this.editData[this.editObj].xMin
        option.xAxis[0].max = this.editData[this.editObj].xMax
        option.xAxis[0].interval = this.editData[this.editObj].xInterval
      }
      option.xAxis[0][targetObj] = targetValue;

      // 样式
      option.xAxis[0].axisTick = { show: false }
      option.xAxis[0].axisLabel = { show: true, width: 0.5, fontSize: 14, color: "#000000" }
      option.xAxis[0].axisLine = { show: true, lineStyle: { color: "#ccc", width: 0.5 } }
      option.xAxis[0].splitLine = { show: true, lineStyle: { type: "solid", width: 0.5 } }
      option.xAxis[0].nameTextStyle = { fontSize: 14, fontWeight: 500, color: "#000000" }
      option.xAxis[0].nameLocation = 'middle'
      option.xAxis[0].nameGap = 30

      if (targetObj !== 'name') option.xAxis[0].name = this.editData[this.editObj].XTitle



      this.echartObj[this.editObj].setOption(option)

      // X轴首次由类目轴修改为数值轴，获取最大最小值、间隔
      if (targetObj === 'type' && targetValue === 'value' && !this.editData[this.editObj].xInterval) {
        const XAxis = this.echartObj[this.editObj].getModel().getComponent("xAxis").axis.scale

        this.editData[this.editObj].xMin = XAxis._extent[0]
        this.editData[this.editObj].xMax = XAxis._extent[1]
        this.editData[this.editObj].xInterval = XAxis._interval

        this.originalData[this.editObj].xMin = XAxis._extent[0]
        this.originalData[this.editObj].xMax = XAxis._extent[1]
        this.originalData[this.editObj].xInterval = XAxis._interval
      }
    },
    _handleEchartsYAxis(originalValue, targetObj, targetValue) {
      const option = { yAxis: [{}, { axisLine: { show: true, lineStyle: { color: "#ccc", width: 0.5 } } }] }
      const yAxisOptions = ['YTitle', 'yType', 'yMin', 'yMax', 'yInterval', 'yTitleLetf']
      const targetIndex = yAxisOptions.includes(originalValue) ? 0 : 1
      // 保留设置用户上一次留下的值,最大最小以及间隔值
      if (targetObj === 'type' && targetValue === 'value') {
        option.yAxis[targetIndex].min = this.editData[this.editObj][`yMin${yAxisOptions.includes(originalValue) ? '' : '2'}`]
        option.yAxis[targetIndex].max = this.editData[this.editObj][`yMax${yAxisOptions.includes(originalValue) ? '' : '2'}`]
        option.yAxis[targetIndex].interval = this.editData[this.editObj][`yInterval${yAxisOptions.includes(originalValue) ? '' : '2'}`]
      }
      option.yAxis[targetIndex][targetObj] = targetValue;

      // 样式
      option.yAxis[targetIndex].axisTick = { show: targetObj ? false : true }
      option.yAxis[targetIndex].axisLabel = { show: true, width: 0.5, fontSize: 14, color: "#000000" }
      option.yAxis[targetIndex].axisLine = { show: true, lineStyle: { color: "#ccc", width: 0.5 } }
      option.yAxis[targetIndex].splitLine = { show: true, lineStyle: { type: "solid", width: 0.5 } }
      option.yAxis[targetIndex].nameTextStyle = { fontSize: 14, fontWeight: 500, color: "#000000" }
      option.yAxis[targetIndex].nameLocation = 'middle'
      option.yAxis[targetIndex].nameGap = this.editData[this.editObj][`${yAxisOptions.includes(originalValue) ? 'yTitleLetf' : 'yTitleRight'}`]

      if (targetObj !== 'name') option.yAxis[targetIndex].name = this.editData[this.editObj][`YTitle${yAxisOptions.includes(originalValue) ? '' : '2'}`]

      this.echartObj[this.editObj].setOption(option)
    },
    _handleEchartsDataLabel(originalValue, targetObj, targetValue, targetIndex) {
      const option = { series: [] }
      const arr = ['connectNulls', 'symbol', 'symbolSize']
      const arr1 = ['lineWidth', 'lineType', 'lineColor']

      // 找到要修改的线的位置
      const haveIndex = this.editData[this.editObj].editSeries.findIndex(findItem => findItem.id === this.editData[this.editObj].originalSeries[targetIndex].id)

      // 拿到要修改的那根线
      const newSeries = _.cloneDeep(this.editData[this.editObj].editSeries[haveIndex])

      // 最大值、最小值
      if ((originalValue === 'maxPoint' || originalValue === 'minPoint') && targetValue) {
        newSeries.markPoint.data.push({ type: targetObj, name: targetObj })
      }
      if ((originalValue === 'maxPoint' || originalValue === 'minPoint') && !targetValue) {
        const haveIndex1 = newSeries.markPoint.data.findIndex(findItem => findItem.type === targetObj)
        newSeries.markPoint.data.splice(haveIndex1, 1)
      }

      // 连线、折点类型、折点大小
      if (arr.includes(originalValue)) {
        newSeries[targetObj] = targetObj === 'connectNulls' ? Boolean(Number(targetValue)) : targetValue
      }

      // 折点颜色
      if (originalValue === 'itemColor') {
        newSeries.itemStyle[targetObj] = targetValue
      }

      // 折线类型、折线宽度、折现颜色
      if (arr1.includes(originalValue)) {
        newSeries.lineStyle[targetObj] = targetValue
      }

      option.series[targetIndex] = newSeries;
      this.editData[this.editObj].editSeries[haveIndex] = newSeries
      this.echartObj[this.editObj].setOption(option)
    },
    _handleEchartsSynchronization(originalValue, targetValue) {
      // originalValue 要修改的原始值   targetValue 需要修改过后的值
      const option = { series: [] }

      const selectIndex = this.editData[this.editObj].editSeries.findIndex(findItem => findItem.id === this.editData[this.editObj].originalSeries[originalValue].id)
      const templateIndex = this.editData[this.editObj].editSeries.findIndex(findItem => findItem.id === this.editData[this.editObj].originalSeries[targetValue].id)

      const newSeries = { ...this.editData[this.editObj].editSeries[selectIndex] }
      const templateSeries = _.cloneDeep(this.editData[this.editObj].editSeries[templateIndex])
      const arr = ['symbol', 'symbolSize', 'markPoint', 'lineStyle', 'itemStyle', 'connectNulls']  // 需要修改的值

      Object.keys(templateSeries).forEach(key => {
        if (arr.includes(key)) {
          newSeries[key] = key === 'connectNulls' ? Boolean(Number(templateSeries[key])) : templateSeries[key]

          if (key === 'lineStyle') {
            this.editData[this.editObj].series[selectIndex].lineType = this.editData[this.editObj].series[templateIndex].lineType
            this.editData[this.editObj].series[selectIndex].lineWidth = this.editData[this.editObj].series[templateIndex].lineWidth
            this.editData[this.editObj].series[selectIndex].lineColor = this.editData[this.editObj].series[templateIndex].lineColor
          } else if (key === 'itemStyle') {
            this.editData[this.editObj].series[selectIndex].itemColor = this.editData[this.editObj].series[templateIndex].itemColor
          } else {
            this.editData[this.editObj].series[selectIndex][key] = this.editData[this.editObj].series[templateIndex][key]
          }
        }
      });
      // 最大最小值
      this.editData[this.editObj].series[selectIndex].maxPoint = this.editData[this.editObj].series[templateIndex].maxPoint
      this.editData[this.editObj].series[selectIndex].minPoint = this.editData[this.editObj].series[templateIndex].minPoint

      option.series[selectIndex] = newSeries;
      this.editData[this.editObj].editSeries[selectIndex] = newSeries
      this.echartObj[this.editObj].setOption(option)

    },
    _handleEchartsAllDataLabel(originalValue, targetObj, targetValue) {

      const option = { series: [] }
      const arr = ['lineWidth', 'lineType', 'lineColor']

      // 拿到所有的线
      const newSeries = [...this.editData[this.editObj].editSeries]

      // 修改值
      newSeries.forEach((v, index) => {
        if (arr.includes(originalValue)) {
          v.lineStyle[targetObj] = targetValue == 'resetAll' ? this.editData[this.editObj].originalSeries.filter(filterItem => filterItem.id === v.id)[0].lineStyle[targetObj] : targetValue
        } else if (originalValue === 'itemColor') {
          v.itemStyle[targetObj] = targetValue == 'resetAll' ? this.editData[this.editObj].originalSeries.filter(filterItem => filterItem.id === v.id)[0].itemStyle[targetObj] : targetValue
        } else {
          v[targetObj] = targetValue == 'resetAll' ? this.editData[this.editObj].originalSeries.filter(filterItem => filterItem.id === v.id)[0][targetObj] : (targetObj === 'connectNulls' ? Boolean(Number(targetValue)) : targetValue)
        }

        // 编辑数据更新
        this.editData[this.editObj].series[index][originalValue] = targetValue == 'resetAll' ? this.originalData[this.editObj].series.filter(filterItem => filterItem.id === v.id)[0][originalValue] : targetValue
      })

      option.series = newSeries;
      this.echartObj[this.editObj].setOption(option)
    },


    // 下载
    handleDown(targetObj) {
      const dom = document.getElementById(targetObj)

      let fileName = this.firstInit[targetObj] ? this.originalData[targetObj].chartTitle : this.editData[targetObj].chartTitle
      if (typeof fileName === 'string') {
        fileName = fileName.replaceAll('.', ' ')
      }

      html2canvas(dom, {
        useCORS: true,
        backgroundColor: "#fff"
      }).then(canvas => {
        let canvasImg = canvas.toDataURL("image/png")
        const blob = this.handleB64toBlob(canvasImg.replace("data:image/png;base64,", ""))
        if (window.navigator.msSaveOrOpenBlob) {
          //兼容IE10
          navigator.msSaveBlob(blob, fileName)
        } else {
          const href = URL.createObjectURL(blob)
          const a = document.createElement("a")
          a.style.display = "none"
          a.href = href // 指定下载链接
          a.download = fileName
          a.click() //触发下载
          URL.revokeObjectURL(a.href)
        }
      })
    },
    // base64 转 blob
    handleB64toBlob(b64Data, contentType = null, sliceSize = null) {
      contentType = contentType || "image/png"
      sliceSize = sliceSize || 512
      let byteCharacters = window.atob(b64Data.substring(b64Data.indexOf(",") + 1))
      let byteArrays = []
      for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
        let slice = byteCharacters.slice(offset, offset + sliceSize)
        let byteNumbers = new Array(slice.length)
        for (let i = 0; i < slice.length; i++) {
          byteNumbers[i] = slice.charCodeAt(i)
        }
        var byteArray = new Uint8Array(byteNumbers)
        byteArrays.push(byteArray)
      }
      return new Blob(byteArrays, { type: contentType })
    },

    // handleReturnTop() {
    //   this.$refs.wrapper.scrollTop = 0
    // },
  },
}