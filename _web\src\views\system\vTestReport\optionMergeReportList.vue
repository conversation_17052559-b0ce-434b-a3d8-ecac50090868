<template>
  <div :style="{paddingTop: padding}">
    <tableIndex
        ref="pbiTableIndex"
        :pageLevel='1'
        :tableTotal= 'tableTotal'
        :pageTitleShow=false
        :otherHeight="parseInt(58 + width)"
        :loading='tableLoading'
        @paginationChange="handlePageChange"
        @paginationSizeChange="handlePageChange"
    >
      <template #search>
        <pbiSearchContainer>
          <pbiSearchItem label='任务状态' :span="4">
            <a-select v-model="queryparam.fileStatus" @change="loadData" :allow-clear="true" style="width: 100%;">
              <a-select-option value="0">待处理</a-select-option>
              <a-select-option value="10">进行中</a-select-option>
              <a-select-option value="20">已完成</a-select-option>
              <a-select-option value="30">数据异常</a-select-option>
            </a-select>
          </pbiSearchItem>
          <pbiSearchItem label='任务名称' :span="4">
            <a-input v-model="queryparam.reportName" @keyup.enter="handleInputSearch('reportName')" @change="handleInputSearch('reportName')"/>
          </pbiSearchItem>
          <pbiSearchItem label='创建人' :span="4">
            <a-input v-model="queryparam.createName" @keyup.enter="handleInputSearch('createName')" @change="handleInputSearch('createName')"/>
          </pbiSearchItem>

          <pbiSearchItem :span="12" type='btn'>
            <a-button style="margin-right: 12px;" @click="getList()" type="primary">查询</a-button>
            <a-button type="primary" style="margin-right: 12px;" @click="goToModelBuild">新建</a-button>
            <a-button @click="reset()" >重置</a-button>

          </pbiSearchItem>
        </pbiSearchContainer>
      </template>
      <template #table>
        <ag-grid-vue class='ag-theme-balham'
                     :style="{height:`${tableHeight}px`}"
                     :tooltipShowDelay="0"
                     :columnDefs='columnDefs'
                     :rowData='tableList'
                     :defaultColDef='defaultColDef'>
        </ag-grid-vue>
      </template>
    </tableIndex>

    <!-- 标题部分 start -->
<!--    <div class="head-wrapper" id="head">-->
<!--      <div class="circle-block" v-for="(item, index) in dataArr">-->
<!--        <div class="data-packet" v-if="!item.type">-->
<!--          <div class="icon" :style="-->
<!--							`background: ${item.color};-->
<!--	border: 8px solid ${item.borderColor};`-->
<!--						">-->
<!--            <a-icon :type="item.icon" />-->
<!--          </div>-->
<!--          <div class="detail">-->
<!--            <p>{{ item.num < 10 ? "0" : "" }}{{ item.num }}</p>-->
<!--            <p>{{ item.detail }}</p>-->
<!--          </div>-->
<!--        </div>-->

<!--        <div v-else class="line"></div>-->
<!--      </div>-->
<!--    </div>-->
    <!-- 标题部分 end -->

    <!-- 内容部分 start -->
<!--    <div class="content-wrapper">-->
<!--      <div class="left block" id="left">-->
<!--        <tableIndex-->
<!--          :pageLevel='2'-->
<!--          :tableTotal= 'tableTotal'-->
<!--          :pageTitleShow=false-->
<!--          :loading='tableLoading'-->
<!--          @paginationChange="handlePageChange"-->
<!--          @paginationSizeChange="handlePageChange"-->
<!--        >-->
<!--          <template #search>-->
<!--            <pbiSearchContainer>-->
<!--              <pbiSearchItem label='任务状态' :span="6">-->
<!--                <a-select v-model="queryparam.fileStatus" @change="loadData" :allow-clear="true" style="width: 100%;">-->
<!--                  <a-select-option value="0">待处理</a-select-option>-->
<!--                  <a-select-option value="10">进行中</a-select-option>-->
<!--                  <a-select-option value="20">已完成</a-select-option>-->
<!--                  <a-select-option value="30">数据异常</a-select-option>-->
<!--                </a-select>-->
<!--              </pbiSearchItem>-->
<!--              <pbiSearchItem label='任务名称' :span="6">-->
<!--                <a-input v-model="queryparam.reportName" @keyup.enter="handleInputSearch('reportName')" @change="handleInputSearch('reportName')"/>-->
<!--              </pbiSearchItem>-->
<!--              <pbiSearchItem label='创建人' :span="6">-->
<!--                <a-input v-model="queryparam.createName" @keyup.enter="handleInputSearch('createName')" @change="handleInputSearch('createName')"/>-->
<!--              </pbiSearchItem>-->
<!--            </pbiSearchContainer>-->
<!--          </template>-->
<!--          <template #table>-->
<!--            <ag-grid-vue class='ag-theme-balham'-->
<!--                         :style="{height:`${tableHeight}px`}"-->
<!--                         :tooltipShowDelay="0"-->
<!--                         :columnDefs='columnDefs'-->
<!--                         :rowData='tableList'-->
<!--                         :defaultColDef='defaultColDef'>-->
<!--            </ag-grid-vue>-->
<!--          </template>-->
<!--        </tableIndex>-->
<!--        <div class="option-btn-div">-->
<!--          <a-button type="primary" style="margin-right: 10px;" @click="goToModelBuild">新建</a-button>-->
<!--          <a-button type="primary" @click="loadData">刷新</a-button>-->
<!--        </div>-->
<!--      </div>-->

<!--      <div class="right block">-->
<!--        <div class="title">-->
<!--          <img src="@/assets/icons/bookmark.png" alt="" />-->
<!--          <span class="text">示例</span>-->
<!--        </div>-->
<!--        <div style="width: 100%; height: fit-content; padding: 10px 10px;">-->
<!--          <div class="head_title">报告预览：高温存储专项_正极材料正交验证_60℃ 100%SOC</div>-->
<!--          <Carousel autoplay arrows>-->
<!--            <div class="carousel-item"><img alt="example" src="./carouselPictures/1.png" style="height: 100%; width: auto;" /></div>-->
<!--            <div class="carousel-item"><img alt="example" src="./carouselPictures/2.png" style="height: 100%; width: auto;" /></div>-->
<!--            <div class="carousel-item"><img alt="example" src="./carouselPictures/3.png" style="height: 100%; width: auto;" /></div>-->
<!--            <div class="carousel-item"><img alt="example" src="./carouselPictures/4.png" style="height: 100%; width: auto;" /></div>-->
<!--            <div class="carousel-item"><img alt="example" src="./carouselPictures/5.png" style="height: 100%; width: auto;" /></div>-->
<!--            <div class="carousel-item"><img alt="example" src="./carouselPictures/6.png" style="height: 100%; width: auto;" /></div>-->
<!--            <div class="carousel-item"><img alt="example" src="./carouselPictures/7.png" style="height: 100%; width: auto;" /></div>-->
<!--            <div class="carousel-item"><img alt="example" src="./carouselPictures/8.png" style="height: 100%; width: auto;" /></div>-->
<!--            <div class="carousel-item"><img alt="example" src="./carouselPictures/9.png" style="height: 100%; width: auto;" /></div>-->
<!--            <div class="carousel-item"><img alt="example" src="./carouselPictures/10.png" style="height: 100%; width: auto;" /></div>-->
<!--            <div class="carousel-item"><img alt="example" src="./carouselPictures/11.png" style="height: 100%; width: auto;" /></div>-->
<!--            <div class="carousel-item"><img alt="example" src="./carouselPictures/12.png" style="height: 100%; width: auto;" /></div>-->
<!--            <div class="carousel-item"><img alt="example" src="./carouselPictures/13.png" style="height: 100%; width: auto;" /></div>-->
<!--            <div class="carousel-item"><img alt="example" src="./carouselPictures/14.png" style="height: 100%; width: auto;" /></div>-->
<!--            <div class="carousel-item"><img alt="example" src="./carouselPictures/15.png" style="height: 100%; width: auto;" /></div>-->
<!--            <div class="carousel-item"><img alt="example" src="./carouselPictures/16.png" style="height: 100%; width: auto;" /></div>-->
<!--            <template #prevArrow>-->
<!--              <div class="custom-slick-arrow" style="left: 12px;">-->
<!--                <a-icon type="left-circle" />-->
<!--              </div>-->
<!--            </template>-->
<!--            <template #nextArrow>-->
<!--              <div class="custom-slick-arrow" style="right: 12px">-->
<!--                <a-icon type="right-circle" />-->
<!--              </div>-->
<!--            </template>-->
<!--          </Carousel>-->
<!--        </div>-->
<!--        <div v-if="!reportId">-->
<!--          <a-empty class="empty-block" description="暂无数据" />-->
<!--        </div>-->
<!--        <optionMergeThumListPreview v-else :reportId="reportId">-->
<!--        </optionMergeThumListPreview>-->
<!--      </div>-->
<!--    </div>-->
    <!-- 内容部分 end -->

    <a-modal title="传阅" :width="500" :visible="shareVisible" @cancel="() => shareVisible = false">
      <div class="share-modal">
        <div class="title">已传阅人员</div>
        <div class="action-content mt10">
          <a-input v-model="shareUserParam.userName" placeholder="请输入账号/姓名" allow-clear style="width: 200px" @change="getShareUserList(checkRecord.id)">
            <a-icon slot="prefix" type="search" />
          </a-input>
          <a-button type="primary"  ghost @click="handleShowAddUser">新增传阅人员<a-icon type="plus"/></a-button>
        </div>

        <div class="mt10">
          <a-table :columns="shareUserColumns"
                   :data-source="shareUserData"
                   :rowKey="(record) => record.userAccount">
              <span slot="action" slot-scope="text, record">
                  <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => shareUserDelete(record)">
                    <a>删除</a>
                  </a-popconfirm>
              </span>
          </a-table>
        </div>
      </div>

      <a-modal title="新增传阅人员" :visible="isShowAddUser" :width="528" centered @cancel="() => isShowAddUser = false">
        <div class="share-modal">
          <div class="action-content">
            <a-input v-model="addUserParam.account" placeholder="请输入账号/姓名" allow-clear style="width: 200px" @change="getUserList">
              <a-icon slot="prefix" type="search" />
            </a-input>
            <treeselect
              v-model="addUserParam.grantOrgIdList"
              placeholder="请选择部门"
              value-consists-of="BRANCH_PRIORITY"
              :limit="1"
              :multiple="true"
              :max-width="270"
              :options="orgOptions"
              @input="getUserList"
            />
          </div>
          <div class="mt10">
            <a-table :columns="addUserColumns"
                     :dataSource="addUserData"
                     :rowKey="(record) => record.account"
                     :rowSelection="{ selectedRowKeys: userSelectedRowKeys, onChange: onUserSelectChange }"
                     :pagination="userPagination"
                     @change="handleUserTableChange">
            </a-table>
          </div>
        </div>
        <template slot="footer">
          <a-button key="confirm" type="primary" @click="addShareUsers(checkRecord.id)">
            <a>传阅</a>
          </a-button>
          <a-button key="back" @click="() => isShowAddUser = false">
            <a>取消传阅</a>
          </a-button>
        </template>
      </a-modal>

      <template slot="footer">
        <a-button key="back" @click="() => shareVisible = false">
          <a>关闭</a>
        </a-button>
      </template>
    </a-modal>

    <a-modal title="变更履历" :visible="historyVisible" width="60%" @cancel="() => historyVisible = false">
      <template slot="footer" >
        <a-button key="back" @click="() => historyVisible = false">
          <a>关闭</a>
        </a-button>
      </template>

      <a-table :columns="historyColumns"
               :data-source="historyData"
               :rowKey="(record) => record.id">

            <template slot="reportName" slot-scope="text, record, index, columns">
              <a target="_blank" @click="pushToReview(record)" v-if="record.fileStatus == 20" style="margin-right: 12px">{{text}}</a>
              <span v-else>{{text}}</span>
            </template>
      </a-table>

    </a-modal>
  </div>
</template>

<script>
import {testReportDelete, testReportPageList, testReportRegenerateData} from "@/api/modular/system/limsManager";
import {
  testReportHistoryList,
  testReportShareAdd,
  testReportShareDelete,
  testReportShareList
} from "@/api/modular/system/reportManager";
import {getUserPage} from "@/api/modular/system/userManage";
import {getOrgTree} from "@/api/modular/system/orgManage";
import {mapGetters} from "vuex";
import optionMergeThumListPreview from "@/views/system/vTestReport/components/optionMergeThumListPreview";
import { Carousel } from 'ant-design-vue';

export default {
  name: "optionMergeReportList",
  props: {
    width:{
      type: Number,
      default: 0
    },
    padding:{
      type: String,
      default: '8px'
    }
  },
  components: {
    Carousel,
    optionMergeThumListPreview,
    showFileStatus: {
      template: '<a-tag :color="params.getStatusColor(params.value)">{{params.formatValue(params.value)}}</a-tag>'
    },
    showReportName: {
      template: '<a target="_blank" @click="params.pushToReview(params.data)" v-if="params.data.fileStatus == 20" style="margin-right: 12px">{{params.value}}</a><span v-else>{{params.value}}</span>'
    },
    showOptions: {
      template: `
        <div>
          <a-popconfirm v-if="params.data.fileStatus != 10 || params.data.fileStatus != 0"
                        title="确定要刷新数据吗?" ok-text="确定" cancel-text="取消" placement="left"
                        @confirm="params.refreshData(params.data)">
            <a-tooltip title="刷新数据">
              <a-icon class="mr10" type="reload" />
            </a-tooltip>
          </a-popconfirm>
          <a-tooltip title="编辑建模参数">
            <a-icon class="mr10" type="edit" @click="params.reExport(params.data)" />
          </a-tooltip>
          <a-popconfirm title="确定要重新生成吗?" ok-text="确定" cancel-text="取消" placement="left"
                        @confirm="params.reExport(params.data, true)">
            <a-tooltip title="重新生成">
              <a-icon class="mr10" type="file-sync"/>
            </a-tooltip>
          </a-popconfirm>
          <a-tooltip title="变更履历" @click="params.getHistoryList(params.data.id,true,params.data)">
            <a-icon class="mr10" type="read"/>
          </a-tooltip>
          <a-tooltip title="传阅" @click="params.getShareUserList(params.data.id,true,params.data)">
            <a-icon class="mr10" type="share-alt" />
          </a-tooltip>
          <a-popconfirm title="确定删除吗?" ok-text="确定" cancel-text="取消" placement="topRight"
                        @confirm="params.deleteDataById(params.data.id)">
            <a-tooltip title="删除数据">
              <a-icon v-if="params.showDeleteButton(params.data.createAccount)" type="delete" />
            </a-tooltip>
          </a-popconfirm>
        </div>
      `
    }
  },
  data() {
    return {
      dataArr: [
        //type 0: 数据  1: 线
        {
          num: '--',
          type: 0,
          detail: "待处理",
          icon: "exclamation",
          color: "#ffb794",
          borderColor: "rgba(255, 183, 148,0.15)"
        },
        {
          type: 1
        },
        {
          num: '--',
          type: 0,
          detail: "进行中",
          icon: "sync",
          color: "#66ADF9",
          borderColor: "rgba(102,173,249,0.15)"
        },
        {
          type: 1
        },
        {
          num: '--',
          type: 0,
          detail: "已完成",
          icon: "check",
          color: "#34E09E",
          borderColor: "rgba(52,224,158,0.15)"
        },
        {
          type: 1
        },
        {
          num: '--',
          type: 0,
          detail: "数据异常",
          icon: "close",
          color: "#FF8586",
          borderColor: "rgba(255, 133, 134,0.15)"
        },
        {
          type: 1
        },
        {
          num: '--',
          type: 0,
          detail: "任务总览",
          icon: "menu",
          color: "#8382F5",
          borderColor: "rgba(131,130,245,0.15)"
        }
      ],

      tableHeight: 450,
      tableLoading: false,
      queryparam: { pageNo: 1, pageSize: 20, type: 'optionMerge'},
      tableList: [],
      tableTotal: 0,
      defaultColDef: {
        filter: false,
        floatingFilter: false,
        editable: false,
      },
      columnDefs: [
        {
          headerName: '序号',
          minWidth: 50,
          width: 50,
          cellRenderer: function (params) {
            return params.rowIndex + 1
          },
        },
        {
          headerName: '任务名称',
          field: 'reportName',
          flex:1,
          cellRenderer: 'showReportName',
          cellRendererParams: { pushToReview:this.pushToReview },
          cellStyle: () =>  {return {textAlign:'left'}},
          tooltipValueGetter: (p) => p.value,
        },
        {
          headerName: '任务状态',
          field: 'fileStatus',
          width: 120,
          cellRenderer: 'showFileStatus',
          cellRendererParams: { formatValue:this.formatValue, getStatusColor:this.getStatusColor },
        },
        {
          headerName: '创建时间',
          field: 'createTime',
          width: 140,
        },
        // {
        //   headerName: '开始时间',
        //   field: 'beginTime',
        //   minWidth: 140,
        //   width: 140,
        // },
        // {
        //   headerName: '完成时间',
        //   field: 'finishTime',
        //   minWidth: 140,
        //   width: 140,
        // },
        {
          headerName: '刷新时间',
          field: 'finishTime',
          width: 140,
        },
        {
          headerName: '创建人',
          field: 'createName',
          width: 130,
          tooltipValueGetter: (p) => p.value,
        },
        {
          headerName: '操作',
          width: 200,
          cellRenderer: 'showOptions',
          cellRendererParams: {refreshData:this.refreshData, reExport:this.reExport,
            getHistoryList:this.getHistoryList, getShareUserList:this.getShareUserList,
            showDeleteButton:this.showDeleteButton, deleteDataById:this.deleteDataById }
        }
      ],

      historyVisible: false,
      historyColumns:[
        {
          title: '序号',
          align: 'center',
          width: 50,
          customRender: (text, record, index) => index + 1
        }, {
          title: '任务名称',
          dataIndex: 'reportName',
          align: 'center',
          width: 300,
          scopedSlots: {customRender: 'reportName'},
        }, {
          title: '操作类型',
          width: 100,
          align: 'center',
          dataIndex: 'operateType',
          customRender: (text, record, index) => {
            if(record.operateType == 'add'){
              return "初次创建"
            }
            if(record.operateType == 'update'){
              return "更新数据"
            }
            if(record.operateType == 'refresh'){
              return "刷新数据"
            }
          }
        },  {
          title: '操作时间',
          width: 150,
          align: 'center',
          dataIndex: 'createTime',
        },{
          title: '操作人',
          width: 90,
          align: 'center',
          dataIndex: 'createName',
        }
      ],
      historyData:[],

      checkRecord:{},
      shareVisible:false,
      isShowAddUser:false,
      shareUserColumns:[
        {
          title: '账号',
          dataIndex: 'userAccount',
          align:'center'
        },
        {
          title: '姓名',
          dataIndex: 'userName',
          align:'center'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align:'center',
          scopedSlots: { customRender: 'action' }
        }],
      shareUserData:[],
      shareUserParam:{},
      addUserParam:{
        grantOrgIdList:[]
      },
      orgOptions:[],
      addUserColumns: [
        {
          title: '账号',
          dataIndex: 'account',
          align:'center'
        },
        {
          title: '姓名',
          dataIndex: 'name',
          align:'center'
        }
      ],
      addUserData:[],
      userSelectedRowKeys:[],
      userSelectedRows:[],
      userPagination:{
        current:1,
        pageSize:10,
        total:0,
        size:"small"
      },

      reportId: '',
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  watch: {
  },
  created() {
    this.loadData()
  },
  mounted() {
    this.handleHeight()
    //窗口尺寸改变
    window.addEventListener("resize", () => {
      this.handleHeight()
    })
  },
  methods: {
    handlePageChange(value){
      let { current, pageSize } = value
      this.queryparam.pageNo = current
      this.queryparam.pageSize = pageSize
      this.loadData()
    },
    handleHeight() {
      this.tableHeight = document.body.clientHeight - 156 - this.width
    },
    formatValue(value) {
      switch (value) {
        case 0:
          return '待处理';
        case 10:
          return '进行中';
        case 20:
          return '已完成';
        case 30:
          return '数据异常';
      }
    },
    getStatusColor(value) {
      switch (value) {
        case 0:
          return 'orange';
        case 10:
          return 'blue';
        case 20:
          return 'green';
        case 30:
          return 'red';
      }
    },
    showDeleteButton(createAccount) {
      return this.userInfo.account === 'superAdmin' || this.userInfo.account === createAccount
    },
    getList() {
      this.loadData()
    },
    reset(){
      delete this.queryparam.fileStatus
      delete this.queryparam.reportName
      delete this.queryparam.createName
      this.loadData()
    },
    loadData() {
      this.tableLoading = true

      if (this.queryparam.fileStatus === null && this.$route.query.fileStatus != null) {
        this.queryparam.fileStatus = this.$route.query.fileStatus
      }

      testReportPageList( this.queryparam )
        .then((res) => {
          if (res.success) {
            this.tableList = res.data.rows
            this.tableTotal = res.data.totalRows

            // 更新报告预览窗口的报告ID
            if (this.tableList.length === 0) {
              this.reportId = null
            }
            for (let i = 0; i < this.tableList.length; i++) {
              if (this.tableList[i].fileStatus === 20) {
                this.reportId = this.tableList[i].id
                break;
              }
            }
          }
        })
        .finally(() => {

          if(this.tableList.length == 0 && this.queryparam.pageNo > 1){
            // this.queryparam.pageNo -= 1
            this.queryparam.pageNo = Math.max(1, Math.ceil(this.tableTotal / this.queryparam.pageSize))
            this.$refs.pbiTableIndex.$refs.pbiPagination.handleWithoutChange(this.queryparam.pageNo,this.queryparam.pageSize)
            this.loadData()
          }

          this.tableLoading = false
        })
    },
    handleInputSearch(target) {
      this.queryparam[target] = this.queryparam[target].trim()

      this.loadData()
    },
    pushToReview(record){
      window.open("/v_report_preview?id="+record.id+"&type="+record.type,"_blank")
    },
    refreshData(record) {
      testReportRegenerateData(record).then((res) => {
        setTimeout(() => {
          this.loadData()
        }, 600)
      })
    },
    reExport(record, rebuildFlag = false) {
      this.$store.commit('setTaskFilterData', record);
      const fullPath = this.$route.fullPath;
      let newPath = rebuildFlag ? '/optionMergeBuild' : '/optionMergeBuild?id=' + record.id;
      if (fullPath === newPath) {
        this.$emit('gotoCreate', false);
      } else {
        this.$router.push(newPath);
      }
    },
    goToModelBuild() {
      const fullPath = this.$route.fullPath;
      if (fullPath === '/optionMergeBuild') {
        this.$emit('gotoCreate', true);
      } else {
        this.$router.push('/optionMergeBuild');
      }
    },
    handleShowAddUser() {
      this.isShowAddUser = true
      this.getUserList()
      this.getOrgList()
    },
    getUserList(){
      const params = {
        pageNo:this.userPagination.current,
        pageSize:this.userPagination.pageSize,
        searchValue:this.addUserParam.account || '',
        grantOrgIdList: this.addUserParam.grantOrgIdList.join(','),
      }

      getUserPage(params).then(res => {
        if (res.success) {
          this.userSelectedRows = []
          this.userSelectedRowKeys = []
          this.userPagination.total = res.data.totalRows
          for (let i = 0; i < res.data.rows.length; i++) {
            if(this.shareUserData.find(s => s.userAccount == res.data.rows[i].account) != null){
              this.userSelectedRows.push(res.data.rows[i])
              this.userSelectedRowKeys.push(res.data.rows[i].account)
            }
          }
          this.addUserData = res.data.rows
          this.$forceUpdate()
        } else {
          this.$message.error('查询失败：' + res.message)
        }

      })

    },
    // 获取组织架构
    getOrgList(){
      getOrgTree({}).then(res => {
        if(res.success){
          this.orgOptions = []
          res.data[0].children.forEach(v => {
            let $item = {
              id: v.id,
              label:v.title
            }
            if(v.children.length !== 0){
              $item.children = []
              v.children.forEach(chilV => {
                $item.children.push({
                  id:chilV.id,
                  label:chilV.title
                })
              })
            }
            this.orgOptions.push($item)
          })
        }else{
          this.$message.error('部门信息查询失败：' + res.message)
        }
      })
    },
    onUserSelectChange(keys,rows){
      this.userSelectedRowKeys = keys
      this.userSelectedRows = rows
    },
    // 新增传阅人员--分页
    handleUserTableChange(pagination, filters, sorter) {
      this.userPagination.current = pagination.current

      this.getUserList()
    },
    addShareUsers(reportId){
      for (let i = 0; i < this.userSelectedRows.length; i++) {
        testReportShareAdd({reportId:reportId,
          userAccount:this.userSelectedRows[i].account,userName:this.userSelectedRows[i].name}).then((res) => {
          if (res.success) {
            this.$message.success('传阅成功')
            this.isShowAddUser = false
            testReportShareList(this.shareUserParam).then(res => {
              this.shareUserData = res.data
            })
          } else {
            this.$message.error('传阅失败：' + res.message)
          }
        }).catch((err) => {
          this.$message.error('传阅失败：' + err.message)
        })
      }

    },
    getShareUserList(recordId,first,record){
      if(first){
        this.checkRecord = record
      }
      this.shareUserParam.reportId = recordId
      testReportShareList(this.shareUserParam).then(res => {
        this.shareUserData = res.data
        this.shareVisible = true
      })
    },
    shareUserDelete (record) {
      testReportShareDelete({id:record.id}).then((res) => {
        if (res.success) {
          this.$message.success('删除成功')
          this.getShareUserList(this.checkRecord.id)
        } else {
          this.$message.error('删除失败：' + res.message)
        }
      }).catch((err) => {
        this.$message.error('删除错误：' + err.message)
      })
    },
    getHistoryList(recordId,first,record) {
      if(first){
        this.checkRecord = record
      }

      testReportHistoryList({reportId:recordId}).then(res => {
        this.historyData = res.data
        this.historyVisible = true
      })
    },
    deleteDataById(id) {
      testReportDelete({ id: id })
      .then(() => {
        this.loadData()
      })
    },
  },
}
</script>

<style lang="less" scoped>
@import '/src/components/pageTool/style/pbiSearchItem.less';

.mr10 {
  margin-right: 10px;
}

p {
  margin: 0;
}

.container {
  display: flex;
  flex-direction: column;
  color: #333333;
  font-size: 16px;
  font-weight: 400;
  line-height: 1;
  padding: 10px 10px;
}

// 标题部分
.head-wrapper {
  display: flex;
  justify-content: space-between;
  height: 68px;
  margin-right: 50px;
}

.head-wrapper .circle-block {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.circle-block .data-packet {
  display: flex;
  align-items: center;
}

.circle-block .icon {
  width: 60px;
  height: 60px;
  font-size: 33px;
  color: #fff;
  border-radius: 50%;
  margin-right: 10px;
  box-sizing: content-box;
  background-clip: padding-box !important;

  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.circle-block .detail p:first-child {
  font-size: 25px;
  font-weight: 500;
  margin-bottom: 5px;
}

.circle-block .detail p:last-child {
  color: #666;
}

.circle-block .line {
  margin: 0 20px;
  width: 0;
  height: 55px;
  border: 1px solid #bfbaba;
}

//内容部分
.content-wrapper {
  display: flex;
}

.content-wrapper .left {
  width: 65%;
  margin-right: 10px;
}

// 内容--右边
.content-wrapper .right {
  width: calc(35% - 10px);
}

.content-wrapper .block {
  background-color: #fff;
  border-radius: 10px;
  /* box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.25); */
  position: relative;
}

.option-btn-div {
  position: absolute;
  top: 16px;
  right: 24px;
  margin: 6px 0 0;
}

.block .title {
  padding: 12px 0;
  position: sticky;
  top: 0;
}

.block .title img {
  width: 20px;
  height: 35px;
  position: absolute;
  top: 0;
  left: 13px;
}

.block .title .text {
  display: block;
  line-height: 1;
  margin-left: 40px;
  font-weight: 500;
}

/* 分享用户 */
.share-modal .title {
  font-size: 16px;
  font-weight: 600;
  text-align: center;
}
.share-modal .action-content {
  display: flex;
  justify-content: space-between;
  align-items: center;

}

/deep/ .ant-modal-footer {
  padding: 0 24px 24px;
}

.block .empty-block {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.page-container {
  height: fit-content !important;
}

/* 标题 */
.head_title {
  color: #333;
  padding-bottom: 6px;
  font-size: 14px;
}

.head_title::before {
  width: 6px;
  background: #1890ff;
  margin-right: 8px;
  content: "\00a0"; //填充空格
}

.carousel-item {
  width: 100%;
  height: calc(100vh - 180px);
  padding: 0 10px 10px 10px;
  display: flex !important;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
}

/* 自定义底部切换点样式 */
/deep/ .ant-carousel .slick-dots-bottom {
  bottom: 0;
}

/deep/ .ant-carousel .slick-dots li button {
  background-color: rgba(24, 144, 255, 0.6);
}

/deep/ .ant-carousel .slick-dots li.slick-active button {
  background-color: rgba(24, 144, 255);
}

/* 自定义左右箭头样式 */
/deep/ .ant-carousel .slick-arrow.custom-slick-arrow {
  width: 25px;
  height: 25px;
  font-size: 25px;
  color: rgba(24, 144, 255, 0.6);
  z-index: 2;
}

/deep/ .ant-carousel .custom-slick-arrow:before {
  display: none;
}

/deep/ .ant-carousel .custom-slick-arrow:hover {
  opacity: 0.6;
}

/deep/.ant-tag-green {
  color: #52c41a!important;
  background: #f6ffed!important;
  border-color: #b7eb8f!important;
}
/deep/.ant-tag-blue {
  color: #1890ff!important;
  background: #e6f7ff!important;
  border-color: #91d5ff!important;
}

/deep/.ant-tag-orange {
  color: #fa8c16!important;
  background: #fff7e6!important;
  border-color: #ffd591!important;
}

/deep/.ant-tag-red {
  color: #f5222d!important;
  background: #fff1f0!important;
  border-color: #ffa39e!important;
}
</style>