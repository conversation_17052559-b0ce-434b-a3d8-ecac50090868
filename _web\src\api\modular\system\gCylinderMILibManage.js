import { axios } from '@/utils/request'

export function getMIStandardLibList (parameter) {
  return axios({
    url: '/miStandardLib/getMIStandardLibList/asc',
    method: 'get',
    params: parameter
  })
}

export function getMIStandardLibListDesc (parameter) {
  return axios({
    url: '/miStandardLib/getMIStandardLibList/desc',
    method: 'get',
    params: parameter
  })
}

export function getMIStandardByImpBatteryId (impBatteryId) {
  return axios({
    url: '/miStandardLib/getMIStandardByImpBatteryId/' + impBatteryId,
    method: 'get'
  })
}

export function getMIStandardLibById (parameter) {
  return axios({
    url: '/miStandardLib/getMIStandardLibById',
    method: 'get',
    params: parameter
  })
}

export function insertMIStandardLib (parameter) {
  return axios({
    url: '/miStandardLib/insertMIStandardLib',
    method: 'post',
    data: parameter
  })
}

export function updateMIStandardLib (parameter) {
  return axios({
    url: '/miStandardLib/updateMIStandardLib',
    method: 'post',
    data: parameter
  })
}

export function copyMIStandardLib (parameter) {
  return axios({
    url: '/miStandardLib/copyMIStandardLib',
    method: 'post',
    data: parameter
  })
}

export function deleteMIStandardLibFile(parameter) {
  return axios({
    url: '/miStandardLib/deleteMIStandardLibFile',
    method: 'post',
    data: parameter
  })
}

export function updateAndReImportData (parameter, oldLibraryId, impBatteryId) {
  return axios({
    url: '/miStandardLib/updateAndReImportData/' + oldLibraryId + '/' + impBatteryId,
    method: 'post',
    data: parameter
  })
}

export function miOutputExportExcel (parameter) {
  return axios({
    url: '/miStandardLib/miOutputExportExcel',
    method: 'get',
    params: parameter,
    responseType: 'blob'
  })
}
