<template>
  <a-modal :title="title" :width="900" :visible="visible" @cancel="handleCancel" :confirmLoading="confirmLoading">
    <template slot="footer">
            <a-button key="back" @click="handleCancel">
            关闭
            </a-button>
    </template>
    <!-- <a-card style="margin-bottom:3px;font-size:12px;color:#000;">
      <p>当前更改状态：</p>
      <p v-for="(item,i) in latestHistory" :key="i">
        主物料:{{item.msapNumber}}--{{item.flag}}子物料:{{item.sapNumber}}-{{item.partDescription}}&nbsp;-使用量:{{item.partUse}}&nbsp;-变更前使用量:{{item.prePartUse}}
      </p>
    </a-card> -->
    <s-table ref="table" :columns="columns" :data="loadData" :alert="false" :rowKey="(record) => record.id">
      <template slot="productState" slot-scope="text">
          {{ 'product_state_status' | dictType(text) }}
      </template>
      <template slot="createTime" slot-scope="text">
        {{ text }}
      </template>
      <div slot="expandedRowRender" slot-scope="record" style="margin: 0">
        <template >
          <div style="display:none">{{bomChangeJson = JSON.parse(record.bomChange)}}</div>
          <div>{{record.isAddWerk == 1 ? '新增 ' : '删除 '}}工厂：
            <span v-for="(item,i) in bomChangeJson.werkLines" :key="i">{{item.werkNo}}-{{item.lineName}};</span>
          </div>
          <p v-for="(item,i) in bomChangeJson.changes" :key="i">
            主物料:{{item.mSapNumber}}{{item.flag}}子物料:{{item.sapNumber}}-{{item.partDescription}}&nbsp;-使用量:{{item.partUse}}&nbsp;-变更前使用量:{{item.prePartUse}}
          </p>
        </template>
      </div>
    </s-table>
  </a-modal>
</template>

<script>
  import {
    getBomHistory,
    getBomLastHistory
  } from "@/api/modular/system/bomManage"
  import {
    STable
  } from '@/components'
  export default {
    components: {
      STable
    },
    data() {
      return {
        title: '变更记录',
        queryParam: {},
        visible: false,
        confirmLoading: false,
        latestHistory:[],
        loadData: parameter => {
          return getBomHistory(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        columns: [{
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            width: 50,
            customRender: (text, record, index) => `${index+1}`,
          }, {
            title: 'BOM编号',
            dataIndex: 'bomNo',
            key: 'bomNo',
          },
          {
            title: 'BOM版本',
            dataIndex: 'bomVersion',
            key: 'bomVersion',
          },
          {
            title: '产品阶段',
            dataIndex: 'productState',
            key: 'productState',
            scopedSlots: {
              customRender: 'productState'
            }
          },
          {
            title: '变更时间',
            dataIndex: 'createTime',
            scopedSlots: {
              customRender: 'createTime'
            }
          },
        ]
      }
    },
    methods: {
      edit(record) {
        this.queryParam.id = record.id
        this.visible = true
        setTimeout(() => {
          this.$refs.table.refresh()
        }, 100);
        
        //this.callBomLastHistory(record)
      },
      callBomLastHistory(record) {
        this.confirmLoading = true
        getBomLastHistory({
          id: record.id
        }).then((res) => {
          if (res.success) {
            this.latestHistory = res.data
          } else {
            this.$message.error(res.message, 1);
          }
          this.confirmLoading = false
        }).catch((err) => {
          this.confirmLoading = false
          this.$message.error('错误提示：' + err.message, 1)
        });
      },
      handleCancel() {
        this.latestHistory = []
        this.visible = false
      },
    }
  }
</script>

<style>

</style>