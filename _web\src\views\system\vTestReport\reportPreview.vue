<template>
	<preview v-if="type == '循环DCR'"></preview>
	<socDcrPreview v-else-if="type == 'SOC-DCR'"></socDcrPreview>
	<cRatePreview v-else-if="type.startsWith('倍率-倍率充放电')"></cRatePreview>
	<rateStressPreview v-else-if="type == '倍率-stress'"></rateStressPreview>
	<!-- <previewSocOcv v-else-if="type == 'SOC-OCV map'"></previewSocOcv> -->
	<previewSocOcv v-else-if="type.startsWith('SOC-OCV map')"></previewSocOcv>
	<previewResDCIR v-else-if="type == '内阻-DCR'"></previewResDCIR>
  <previewCycleReport v-else-if="type == '循环'"></previewCycleReport>
  <PreviewCalendar v-else-if="type == '日历寿命'"></PreviewCalendar>
  <PreviewCalendar v-else-if="type == '安全测试'"></PreviewCalendar>
  <PreviewDongLiCycle v-else-if="type == 'Cycle'"></PreviewDongLiCycle>
  <calendarReportPreview v-else-if="type == 'Calendar'"></calendarReportPreview>
  <hlTempOrCRatePreview v-else-if="type == 'HlTemp' || type == 'CRate'"></hlTempOrCRatePreview>
  <OptionMergePreview v-else-if="type == 'optionMerge'"></OptionMergePreview>
  <Chat v-else-if="type == 'chat'"></Chat>
</template>
<script>

import preview from "./preview";
import socDcrPreview from "./socDcrPreview";
import cRatePreview from "./cRatePreview";
import rateStressPreview from "./rateStressPreview";
import previewSocOcv from "./previewSocOcv";
import previewResDCIR from "./previewResDCIR";
import previewCycleReport from "./previewCycleReport";
import PreviewCalendar from "./PreviewCalendar";
import Chat from "./chat";
import PreviewDongLiCycle from "./previewDongLiCycle";
import calendarReportPreview from "./components/dongLiReport/calendarReportPreview";
import OptionMergePreview from "@/views/system/vTestReport/optionMergePreview";
import hlTempOrCRatePreview from "./components/dongLiReport/hlTempOrCRatePreview";


export default {
	components: {
    OptionMergePreview,
    calendarReportPreview,
    hlTempOrCRatePreview,
    preview,socDcrPreview,cRatePreview,rateStressPreview,previewSocOcv,previewResDCIR,previewCycleReport,PreviewCalendar,Chat,PreviewDongLiCycle
	},
	data: function() {
		return {
		  type:''
    }
	},
	watch: {},
	created() {},
	computed: {},
	mounted() {
    this.type = this.$route.query.type
	},

	methods: {

  }
}
</script>
<style lang="less" scoped>

</style>
