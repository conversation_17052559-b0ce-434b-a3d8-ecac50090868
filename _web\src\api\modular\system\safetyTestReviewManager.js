import { axios } from '@/utils/request'

export function safetyTestReviewListPage(parameter) {
  return axios({
    url: `/safetyTestReview/listPage`,
    method: 'post',
    data: parameter
  })
}

export function approveStReview(parameter) {
  return axios({
    url: `/safetyTestReview/approveStReview`,
    method: 'post',
    data: parameter
  })
}

export function rejectStReview(parameter) {
  return axios({
    url: `/safetyTestReview/rejectStReview`,
    method: 'post',
    data: parameter
  })
}

export function getSubmitData(parameter) {
  return axios({
    url: `/safetyTestReview/getSubmitData`,
    method: 'post',
    data: parameter
  })
}

export function updateStDataAtReview(parameter) {
  return axios({
    url: `/safetyTestReview/updateStDataAtReview`,
    method: 'post',
    data: parameter
  })
}

export function updatePicVidAttAtReview(parameter, type, field) {
  return axios({
    url: `/safetyTestReview/updatePicVidAttAtReview/${type}/${field}`,
    method: 'post',
    data: parameter
  })
}

export function batchApproveStReview(parameter) {
  return axios({
    url: `/safetyTestReview/batchApproveStReview`,
    method: 'post',
    data: parameter
  })
}
