<template>
  <div>
    <a-modal title="修改电芯状态" :width="1200" :height="600"
             :bodyStyle="{padding:0}"
             :visible="visible"
             style="padding: 0"
             :maskClosable="false"
             @cancel="handleCancel">

      <template slot="footer">
        <a-button key="back" @click="handleCancel">
          取消
        </a-button>

        <a-popconfirm key="primary" placement="topRight" ok-text="提交" cancel-text="取消" @confirm="handleSubmit">
          <template slot="title">
            <p>确定提交吗</p>
          </template>
          <a-button key="submit" type="primary">
            确定
          </a-button>
        </a-popconfirm>
      </template>
      <a-row>
        <a-col :md="7" :sm="24">
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="总存储天数" has-feedback>
            <a-input-number style="width:120px;" @change="searchDataByTotalDay" v-model="totalDayParam" />
          </a-form-item>
        </a-col>
        <a-col :md="16" :sm="24">
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="只更新当前阶段的电芯状态" has-feedback>
            <a-select v-model="onlyUpdateCurStage" @change="changeSelect($event)" style="width:120px;">
              <a-select-option value="是">
                是
              </a-select-option>
              <a-select-option value="否">
                否
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="1" :sm="24">
          <a-form-item has-feedback>
            <a-button style="float: right;z-index: 10;margin: 5px 5px 5px 0px" type="primary" @click="updateBatteryStatus">
              批量修改电芯状态
            </a-button>
          </a-form-item>
        </a-col>
      </a-row>
      <a-table :columns="columns"
               :scroll="{ x: true }"
               :data-source="data" bordered
               :rowKey="(record1) => record1.id"
               :pagination="false"
               :row-selection="{ selectedRowKeys: selectedRowKeys, onChange:selectedBattery,selectedRows: selectedRows, columnWidth:30}"
      >
        <span slot="batteryStatus" slot-scope="text, record, index">
          <a-select v-model="record.batteryStatus" style="width: 160px;font-size: 14px;">
            <a-select-option value="ongoing">
              进行中
            </a-select-option>
            <a-select-option value="earlyEnd">
              状态正常-提前结束
            </a-select-option>
            <a-select-option value="batteryDisassembly">
              状态正常-电池拆解
            </a-select-option>
            <a-select-option value="pressureDrop">
              掉压失效-终止测试
            </a-select-option>
            <a-select-option value="abnormalHot">
              异常发热-终止测试
            </a-select-option>
            <a-select-option value="openShellAndLeak">
              开壳漏液-终止测试
            </a-select-option>
            <a-select-option value="shellRust">
              壳体生锈-终止测试
            </a-select-option>
            <a-select-option value="operationError">
              作业错误-终止测试
            </a-select-option>
            <a-select-option value="thermalRunaway">
              热失控-终止测试
            </a-select-option>
            <a-select-option value="acrException">
              内阻异常-终止测试
            </a-select-option>
            <a-select-option value = "swelling" >
              鼓包形变-终止测试
            </a-select-option>
          </a-select>
        </span>
        <span slot="timeOfFillInnerres" slot-scope="text, record, index">
          <a-date-picker
            show-time
            format="YYYY-MM-DD HH:mm:ss"
            style="width: 100px;"
            v-model="record.timeOfFillInnerres">
          </a-date-picker>
        </span>
        <span slot="beforeInnerres" slot-scope="text, record, index">
          <a-input v-model="record.beforeInnerres" style="width: 100px;">
          </a-input>
        </span>
        <span slot="beforeVoltage" slot-scope="text, record, index">
          <a-input v-model="record.beforeVoltage" style="width: 100px;">
          </a-input>
        </span>
        <span slot="timeOfFillInnerres2" slot-scope="text, record, index">
          <a-date-picker
            show-time
            :disabled="!record.hasOwnProperty('timeOfFillInnerres2')"
            format="YYYY-MM-DD HH:mm:ss"
            style="width: 100px;"
            v-model="record.timeOfFillInnerres2">
          </a-date-picker>
        </span>
        <span slot="afterInnerres" slot-scope="text, record, index">
          <a-input :disabled="!record.hasOwnProperty('afterInnerres')" v-model="record.afterInnerres" style="width: 100px;">
          </a-input>
        </span>
        <span slot="afterVoltage" slot-scope="text, record, index">
          <a-input :disabled="!record.hasOwnProperty('afterVoltage')" v-model="record.afterVoltage" style="width: 100px;">
          </a-input>
        </span>
        <span slot="volume" slot-scope="text, record, index">
          <a-input :disabled="!record.hasOwnProperty('volume')" v-model="record.volume" style="width: 100px;">
          </a-input>
        </span>
        <span slot="weight" slot-scope="text, record, index">
          <a-input :disabled="!record.hasOwnProperty('weight')" v-model="record.weight" style="width: 100px;">
          </a-input>
        </span>
        <span slot="isolateres" slot-scope="text, record, index">
          <a-input :disabled="!record.hasOwnProperty('isolateres')" v-model="record.isolateres" style="width: 100px;">
          </a-input>
        </span>
        <span v-for="item in sizeInfoList" :slot="item.dataIndex" slot-scope="text, record, index">
          <a-input
            :id="`first-${item.dataIndex}-${index}`"
            v-model="record[item.dataIndex]"
            style="width: 100px;"
          />
        </span>
      </a-table>
    </a-modal>

    <a-modal title="修改电芯状态" :width="350" :visible="updateStatusVisible" @ok="updateStatusSubmit"
             @cancel="updateStatusCancel">
      <a-form>
        <div style="display: flex;margin: 20px">
          <span style="width: 70px">电芯状态:</span>
          <a-select style="width: 220px" v-model="afterUpdateStatus"
          >
            <a-select-option value="ongoing">
              进行中
            </a-select-option>
            <a-select-option value="earlyEnd">
              状态正常-提前结束
            </a-select-option>
            <a-select-option value="batteryDisassembly">
              状态正常-电池拆解
            </a-select-option>
            <a-select-option value="pressureDrop">
              掉压失效-终止测试
            </a-select-option>
            <a-select-option value="abnormalHot">
              异常发热-终止测试
            </a-select-option>
            <a-select-option value="openShellAndLeak">
              开壳漏液-终止测试
            </a-select-option>
            <a-select-option value="shellRust">
              壳体生锈-终止测试
            </a-select-option>
            <a-select-option value="operationError">
              作业错误-终止测试
            </a-select-option>
            <a-select-option value="thermalRunaway">
              热失控-终止测试
            </a-select-option>
            <a-select-option value="acrException">
              内阻异常-终止测试
            </a-select-option>
          </a-select>
        </div>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import {
  getBatteryInfoById, updateBatteryStatusAndData
} from '@/api/modular/system/testProgressManager'
import moment from "moment";

export default {
  props: {},
  data() {
    return {
      onlyUpdateCurStage: "是",
      progressId: null,
      totalDayParam: null,
      labelCol: {
        xs: {
          span: 12
        },
        sm: {
          span: 8
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 14
        }
      },
      folderNo: null,
      progressDetailId: null,
      afterUpdateStatus: null,
      selectedRowKeys: [],
      selectedRows: [],
      data: [],
      loading: false,
      visible: false,
      updateStatusVisible: false,
      sizeInfoList: [],
      sizeInfoColumnNameList: {
        gTopPointDiameter: "上部直径/mm",
        gMiddlePointDiameter: "中部直径/mm",
        gBottomPointDiameter: "下部直径/mm",
        gTotalHeight: "端高/mm",
        gShoulderHeight: "肩高/mm",
        gCoverInnerRing: "盖板内圈φ/mm",
        gCoverOuterRing: "盖板外圈φ/mm",
        cTopPointDiameter: "上部直径/mm",
        cBottomPointDiameter: "下部直径/mm",
        cTotalHeight: "总高/mm",
        cShoulderHeight: "肩高/mm",
        sThickTopLeft: "上左厚度/mm",
        sThickTopMiddle: "上中厚度/mm",
        sThickTopRight: "上右厚度/mm",
        sThickMiddleLeft: "中左厚度/mm",
        sThickMiddle: "中心厚度/mm",
        sThickMiddleRight: "中右厚度/mm",
        sThickBottomLeft: "下左厚度/mm",
        sThickBottomMiddle: "下中厚度/mm",
        sThickBottomRight: "下右厚度/mm",
        sTopPointLength: "上部长度/mm",
        sMiddlePointLength: "中部长度/mm",
        sBottomPointLength: "下部长度/mm",
        sLeftPointHeight: "左部高度/mm",
        sMiddlePointHeight: "中部高度/mm",
        sRightPointHeight: "右部高度/mm",
        vTopPoint: "顶点直径/mm",
        vOneThird: "1/3处直径/mm",
        vOneSecond: "1/2处直径/mm",
        vSecondThird: "2/3处直径/mm",
        vBottom: "尾部直径/mm",
        vTotalHeight: "总高/mm",
        vShoulderHeight: "肩高/mm",
        vBottomHump: "底部凸起/mm",
        oThicknessOne: "厚度1/mm",
        oThicknessTwo: "厚度2/mm",
        oThicknessThree: "厚度3/mm",
        oThicknessFour: "厚度4/mm",
        oThicknessFive: "厚度5/mm"
      },
      columns: [],
      allColumns: [
        {
          title: '序号',
          align: 'center',
          width: 30,
          customRender: (text, record, index) => index + 1
        }, {
          title: '测试项目别名',
          width: 60,
          align: 'center',
          dataIndex: 'alias',
        }, {
          title: '测试编码',
          width: 60,
          align: 'center',
          dataIndex: 'cellTestCode',
        }, {
          title: '电芯状态',
          width: 80,
          align: 'center',
          dataIndex: 'batteryStatus',
          scopedSlots: { customRender: 'batteryStatus' }
        }, {
          title: '出箱后绝对时间',
          width: 40,
          align: 'center',
          dataIndex: 'timeOfFillInnerres',
          scopedSlots: { customRender: 'timeOfFillInnerres' }
        }, {
          title: '出箱后内阻/mΩ',
          width: 60,
          align: 'center',
          dataIndex: 'beforeInnerres',
          scopedSlots: { customRender: 'beforeInnerres' }
        }, {
          title: '出箱后电压/mV',
          width: 60,
          align: 'center',
          dataIndex: 'beforeVoltage',
          scopedSlots: { customRender: 'beforeVoltage' }
        }, {
          title: '中检后绝对时间',
          width: 40,
          align: 'center',
          dataIndex: 'timeOfFillInnerres2',
          scopedSlots: { customRender: 'timeOfFillInnerres2' }
        }, {
          title: '中检后内阻/mΩ',
          width: 60,
          align: 'center',
          dataIndex: 'afterInnerres',
          scopedSlots: { customRender: 'afterInnerres' }
        }, {
          title: '中检后电压/mV',
          width: 60,
          align: 'center',
          dataIndex: 'afterVoltage',
          scopedSlots: { customRender: 'afterVoltage' }
        }, {
          title: '产气量/g',
          align: 'center',
          width: 60,
          dataIndex: 'volume',
          scopedSlots: { customRender: 'volume' }
        }, {
          title: '重量/g',
          align: 'center',
          width: 60,
          dataIndex: 'weight',
          scopedSlots: { customRender: 'weight' }
        }, {
          title: '绝缘阻值/mΩ',
          align: 'center',
          width: 60,
          dataIndex: 'isolateres',
          scopedSlots: { customRender: 'isolateres' }
        }
      ],
      confirmLoading: false
    }
  },

  methods: {
    // 处理表头
    handleColums(columnFieldList) {
      let finalFieldList = []
      let finalNameList = []
      let sizeFieldList = []
      let sizeNameList = []
      let sizePropertys = Object.getOwnPropertyNames(this.sizeInfoColumnNameList)
      sizePropertys = sizePropertys.filter(item => item !== '__ob__');
      for (let i in columnFieldList) {
        let field = columnFieldList[i].replaceAll(/[0-9]/g, "")
        if (sizePropertys.findIndex(k => k === field) !== -1) {
          finalFieldList.push(columnFieldList[i])
          if (sizeFieldList.indexOf(field) === -1) {
            sizeFieldList.push(field)
          }
        }
      }
      Object.entries(this.sizeInfoColumnNameList).forEach(([key, value]) => {
        for (let i in finalFieldList) {
          let field = finalFieldList[i].replaceAll(/[0-9]/g, "")
          if (field === key) {
            finalNameList.push(value)
            if (sizeNameList.indexOf(value) === -1) {
              sizeNameList.push(value)
            }
          }
        }
      });
      // console.log('finalFieldList',finalFieldList)
      // console.log('finalNameList',finalNameList)
      // console.log('sizeFieldList',sizeFieldList)
      // console.log('sizeNameList',sizeNameList)
      let slotList = []
      let firstObj = {
        title: "尺寸/mm",
        dataIndex: "dimension",
        align: "center",
        children: []
      }
      for (let i in sizeFieldList) {
        let midFieldColumn = sizeFieldList[i]
        let midNameColumn = sizeNameList[i]
        let secondObj = {
          title: midNameColumn,
          dataIndex: midFieldColumn,
          align: "center",
          width: "82px",
          children: []
        }
        for (let j in finalFieldList) {
          let field = finalFieldList[j].replaceAll(/[0-9]/g, "")
          let num = finalFieldList[j].replaceAll(/[^0-9]/g, "")
          if (midFieldColumn === field) {
            let thirdObj = {
              title: num,
              dataIndex: finalFieldList[j],
              align: "center",
              width: "82px",
              scopedSlots: {
                customRender: finalFieldList[j]
              }
            }
            secondObj.children.push(thirdObj)
            slotList.push({
              align: "center",
              dataIndex: finalFieldList[j],
              scopedSlots: {
                customRender: finalFieldList[j]
              },
              title: midNameColumn + num,
              width: "82px"
            })
          }
        }
        firstObj.children.push(secondObj)
      }
      return [firstObj, slotList]
    },
    open(progressId) {
      this.visible = true
      this.progressId = progressId
    },
    changeSelect() {
      // console.log('this.onlyUpdateCurStage',this.onlyUpdateCurStage)
    },
    searchDataByTotalDay() {
      getBatteryInfoById({ progressId: this.progressId, totalDay: this.totalDayParam }).then((res) => {
        if (res.success) {
          if (res.data.batteryList.length > 0 && res.data.detailId) {
            this.data = res.data.batteryList
            this.progressDetailId = res.data.detailId
            // console.log('this.columns',this.columns)
            // console.log('this.data[0]',this.data[0])
            // console.log('Object.getOwnPropertyNames(this.data[0])过滤前',Object.getOwnPropertyNames(this.data[0]))
            // console.log('this.data[0].hasOwnProperty(timeOfFillInnerres)',this.data[0].hasOwnProperty('timeOfFillInnerres'))
            let columnFieldList = Object.getOwnPropertyNames(this.data[0])
            columnFieldList = columnFieldList.filter(item => item !== '__ob__');
            let newColumns = []
            this.allColumns.forEach(o => {
              if (columnFieldList.findIndex(k => o.title === '序号' || o.title === '测试项目别名' || o.dataIndex === k) !== -1) {
                newColumns.push(o)
              }
            })
            let sizeResult = this.handleColums(columnFieldList)
            let sizeColumns = sizeResult[0]
            let slotList = sizeResult[1]
            if (sizeColumns.children.length > 0) {
              newColumns.push(sizeColumns)
            }
            this.sizeInfoList = slotList
            // console.log('newColumns', newColumns)
            this.columns = newColumns
          } else {
            this.data = []
            this.progressDetailId = null
          }
        } else {
          this.$message.error('获取电芯信息失败!')
        }
      })
    },
    selectedBattery(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    updateStatusSubmit() {
      if (!this.afterUpdateStatus) {
        this.$message.warning('请选择电芯状态')
      } else {
        this.selectedRows.forEach(item => {
          item.batteryStatus = this.afterUpdateStatus
        })
      }
      this.updateStatusVisible = false
    },
    updateStatusCancel() {
      this.afterUpdateStatus = null;
      this.updateStatusVisible = false
    },
    updateBatteryStatus() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请选择电芯')
        return
      }
      this.updateStatusVisible = true
    },
    handleSubmit() {
      if (!this.progressDetailId || this.data.length === 0) {
        this.$message.warning('无可修改的电芯')
        return
      }
      this.data.forEach(o => {
        if (o.timeOfFillInnerres) {
          o.timeOfFillInnerres = moment(o.timeOfFillInnerres).format('YYYY-MM-DD HH:mm:ss')
        }
        if (o.timeOfFillInnerres2) {
          o.timeOfFillInnerres2 = moment(o.timeOfFillInnerres2).format('YYYY-MM-DD HH:mm:ss')
        }
      })
      this.confirmLoading = true
      let params = {
        id: this.progressDetailId,
        lifeTestRecord: JSON.stringify(this.data)
      }
      let type = this.onlyUpdateCurStage === "是" ? "1" : "0"
      updateBatteryStatusAndData(params, type).then((res) => {
        this.confirmLoading = false
        if (res.success) {
          this.$message.success('修改成功！')
          this.$emit('ok')
          this.handleCancel()
          this.updateStatusCancel()
        } else {
          this.$message.error('修改失败：' + res.message)
        }
      }).finally((res) => {
        this.confirmLoading = false
      })
    },
    handleCancel() {
      this.selectedRows = []
      this.selectedRowKeys = []
      this.visible = false
      this.totalDayParam = null
      this.data = []
      this.progressDetailId = null
      this.onlyUpdateCurStage = "是"
    }
  }
}
</script>
<style lang="less" scoped>
.ant-form-item {

  margin-bottom: 0px;

}

.man_button {
  padding-left: 11px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/deep/ .ant-modal-body {
  padding: 0 !important;
}

/deep/ .ant-table-thead > tr > th, /deep/ .ant-table-tbody > tr > td {
  padding: 3px;
}

/deep/ .ant-table-footer {

  padding: 0px;
}

/deep/ .ant-table-pagination.ant-pagination {
  margin: 5px 0;
}

/deep/ .ant-input-number {
  width: 100%;
}

/deep/ .ant-input-number-sm > .ant-input-number-input-wrap > .ant-input-number-input {
  text-align: center;
}

</style>