/**
 * 渲染LaTeX公式为可显示格式
 * @param {string} formula - 原始LaTeX公式
 * @returns {string} 处理后的LaTeX公式
 */
export function renderLatex(formula) {
  try {
    // 如果输入为空，返回空字符串
    if (!formula) return '';

    let tex = String(formula).trim(); // 确保是字符串并去除首尾空格

    // 如果已经包含$符号，直接返回，避免重复处理
    if (tex.startsWith('$') && tex.endsWith('$')) {
      return tex;
    }

    // 处理下标（例如：A_1 -> A_{1}）
    tex = tex.replace(/([A-Za-z])_([A-Za-z0-9]+)/g, '$1_{$2}');

    // 处理希腊字母（例如：alpha -> \alpha）
    const greekLetters = ['alpha', 'beta', 'gamma', 'delta', 'epsilon', 'zeta', 'eta', 'theta',
                          'iota', 'kappa', 'lambda', 'mu', 'nu', 'xi', 'omicron', 'pi',
                          'rho', 'sigma', 'tau', 'upsilon', 'phi', 'chi', 'psi', 'omega'];

    greekLetters.forEach(letter => {
      // 只替换独立的希腊字母单词，不替换作为其他单词一部分的情况
      const regex = new RegExp(`\\b${letter}\\b`, 'g');
      tex = tex.replace(regex, `\\${letter}`);
    });

    // 检查是否已经包含LaTeX定界符
    const hasDelimiters = (tex.startsWith('$') && tex.endsWith('$')) ||
                          (tex.startsWith('\\(') && tex.endsWith('\\)')) ||
                          (tex.startsWith('\\[') && tex.endsWith('\\]'));

    // 如果没有定界符，则添加 $...$
    if (!hasDelimiters && tex) {
      tex = `$${tex}$`;
    }

    return tex;
  } catch (e) {
    console.error('渲染LaTeX出错:', e);
    return formula; // 出错时返回原始输入
  }
}

// 渲染状态管理
let _renderQueue = false;
let _renderTimer = null;
let _lastRenderTime = 0;
const DEBOUNCE_DELAY = 100; // 防抖延迟时间（毫秒）
const MIN_RENDER_INTERVAL = 300; // 最小渲染间隔（毫秒）

/**
 * 使用MathJax渲染页面上的LaTeX内容
 * @param {boolean} immediate - 是否立即渲染，不使用防抖
 * @param {string} selector - 可选的CSS选择器，用于限制只渲染特定区域的LaTeX内容
 * @returns {Promise} MathJax渲染Promise
 */
export function renderMathJax(immediate = false, selector = null) {
  // 如果MathJax未加载，则直接返回
  if (!window.MathJax) return Promise.resolve();

  // 清除之前的定时器
  if (_renderTimer) {
    clearTimeout(_renderTimer);
    _renderTimer = null;
  }

  // 如果已经在渲染队列中，则直接返回
  if (_renderQueue) return Promise.resolve();

  // 检查是否需要强制执行间隔限制
  const now = Date.now();
  const timeSinceLastRender = now - _lastRenderTime;

  // 如果距离上次渲染时间太短且不是强制立即渲染，则延迟执行
  if (timeSinceLastRender < MIN_RENDER_INTERVAL && !immediate) {
    return new Promise(resolve => {
      _renderTimer = setTimeout(() => {
        renderMathJax(true, selector).then(resolve);
      }, MIN_RENDER_INTERVAL - timeSinceLastRender);
    });
  }

  // 使用防抖，延迟执行渲染
  return new Promise(resolve => {
    if (immediate) {
      // 立即执行渲染
      executeRender(resolve, selector);
    } else {
      // 使用防抖延迟执行
      _renderTimer = setTimeout(() => {
        executeRender(resolve, selector);
      }, DEBOUNCE_DELAY);
    }
  });
}

/**
 * 更新公式元素的CSS类
 * @param {string} selector - CSS选择器
 * @param {boolean} isRendering - 是否正在渲染
 */
function updateFormulaElementClasses(selector, isRendering) {
  const elements = document.querySelectorAll(selector);
  elements.forEach(element => {
    if (isRendering) {
      element.classList.add('rendering');
      element.classList.remove('rendered');
    } else {
      element.classList.remove('rendering');
      element.classList.add('rendered');
    }
  });
}

/**
 * 在渲染前隐藏公式元素
 * @param {string} containerSelector - 可选的容器选择器，用于限制只隐藏特定容器内的公式元素
 */
function hideFormulaElements(containerSelector = null) {
  // 为公式相关元素添加rendering类
  const elementSelectors = [
    '.formula-container',
    '.formula-preview-content',
    '.formula-cell',
    '.latex-cell'
  ];

  elementSelectors.forEach(elementSelector => {
    // 如果提供了容器选择器，只隐藏容器内的元素
    if (containerSelector) {
      const fullSelector = `${containerSelector} ${elementSelector}`;
      updateFormulaElementClasses(fullSelector, true);
    } else {
      // 否则隐藏所有匹配的元素
      updateFormulaElementClasses(elementSelector, true);
    }
  });
}

/**
 * 在渲染后显示公式元素
 * @param {string} containerSelector - 可选的容器选择器，用于限制只显示特定容器内的公式元素
 */
function showFormulaElements(containerSelector = null) {
  // 延迟一小段时间，确保MathJax已完成渲染
  setTimeout(() => {
    // 为公式相关元素移除rendering类，添加rendered类
    const elementSelectors = [
      '.formula-container',
      '.formula-preview-content',
      '.formula-cell',
      '.latex-cell'
    ];

    elementSelectors.forEach(elementSelector => {
      // 如果提供了容器选择器，只显示容器内的元素
      if (containerSelector) {
        const fullSelector = `${containerSelector} ${elementSelector}`;
        updateFormulaElementClasses(fullSelector, false);
      } else {
        // 否则显示所有匹配的元素
        updateFormulaElementClasses(elementSelector, false);
      }
    });
  }, 50); // 50毫秒的延迟，确保MathJax已完成渲染
}

/**
 * 执行MathJax渲染
 * @param {Function} resolve - Promise的resolve函数
 * @param {string} selector - 可选的CSS选择器，用于限制只渲染特定区域的LaTeX内容
 */
function executeRender(resolve, selector = null) {
  // 如果已经在渲染中，则直接返回
  if (_renderQueue) {
    resolve();
    return;
  }

  // 标记为正在渲染
  _renderQueue = true;

  // 更新最后渲染时间
  _lastRenderTime = Date.now();

  try {
    // 确定要渲染的元素
    let elements = [];

    if (selector) {
      // 如果提供了选择器，只渲染选择器匹配的元素
      const container = document.querySelector(selector);
      if (container) {
        // 在渲染前只隐藏选择器内的公式元素
        hideFormulaElements(selector);

        // 检查选择器内是否有需要渲染的LaTeX元素
        const hasLatexElements = container.querySelectorAll('.latex-cell, .formula-preview-content').length > 0;

        if (!hasLatexElements) {
          // 如果没有LaTeX元素，直接返回
          _renderQueue = false;
          resolve();
          return;
        }

        // 收集选择器内的所有MathJax元素
        elements = Array.from(container.querySelectorAll('.MJX-TEX, [class^="MathJax"]'));
      } else {
        // 如果选择器没有匹配任何元素，直接返回
        _renderQueue = false;
        resolve();
        return;
      }
    } else {
      // 如果没有提供选择器，渲染整个页面
      // 在渲染前隐藏所有公式元素
      hideFormulaElements();

      // 检查是否有需要渲染的LaTeX元素
      const hasLatexElements = document.querySelectorAll('.latex-cell, .formula-preview-content').length > 0;

      if (!hasLatexElements) {
        // 如果没有LaTeX元素，直接返回
        _renderQueue = false;
        resolve();
        return;
      }
    }

    // 检查MathJax版本并使用相应的API
    if (window.MathJax.version && window.MathJax.version[0] === '3') {
      // MathJax 3.x
      const typesetPromise = selector && elements.length > 0
        ? window.MathJax.typesetPromise(elements) // 只渲染选择器内的元素
        : window.MathJax.typeset(); // 渲染整个页面

      typesetPromise
        .then(() => {
          _renderQueue = false;
          // 在渲染后显示公式元素
          showFormulaElements(selector);
          resolve();
        })
        .catch(error => {
          _renderQueue = false;
          console.error('MathJax 3.x渲染错误:', error);
          // 即使出错也显示公式元素
          showFormulaElements(selector);
          resolve(); // 即使出错也解决Promise，避免阻塞
        });
    } else {
      // MathJax 2.x
      if (selector && elements.length > 0) {
        // 只渲染选择器内的元素
        window.MathJax.Hub.Queue(["Typeset", window.MathJax.Hub, elements]);
      } else {
        // 渲染整个页面
        window.MathJax.Hub.Queue(["Typeset", window.MathJax.Hub]);
      }

      window.MathJax.Hub.Queue(() => {
        _renderQueue = false;
        // 在渲染后显示公式元素
        showFormulaElements(selector);
        resolve();
      });
    }
  } catch (e) {
    _renderQueue = false;
    console.error('MathJax渲染错误:', e);
    // 即使出错也显示公式元素
    showFormulaElements(selector);
    resolve(); // 即使出错也解决Promise，避免阻塞
  }
}

/**
 * 初始化MathJax库
 * @returns {Promise} 初始化Promise
 */
export function initMathJax() {
  return new Promise((resolve, reject) => {
    if (window.MathJax) {
      resolve();
      return;
    }

    // 创建MathJax配置
    window.MathJax = {
      tex: {
        inlineMath: [['$', '$'], ['\\(', '\\)']],
        displayMath: [['$$', '$$'], ['\\[', '\\]']],
        processEscapes: true
      },
      options: {
        skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre', 'code'],
        ignoreHtmlClass: 'tex2jax_ignore',
        processHtmlClass: 'tex2jax_process'
      },
      startup: {
        ready: function() {
          window.MathJax.startup.defaultReady();
          resolve();
        }
      }
    };

    // 加载MathJax脚本
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js';
    script.async = true;
    script.onerror = reject;
    document.head.appendChild(script);
  });
}