<template>
  <div class="container">

    <pbiTabs :tabsList="abnormalStatusList" :activeKey="queryParam.abnormalStatus" @clickTab="handleTabsChange"></pbiTabs>

    <tableIndex
      :pageLevel='1'
      :tableTotal='tableTotal'
      :otherHeight="parseInt(100)"
      :pageTitleShow=false
      :loading='loading'
      @paginationChange="handlePageChange"
      @paginationSizeChange="handlePageChange"
    >
      <template #search>
        <pbiSearchContainer>
          <pbiSearchItem label='委托单号' :span="4">
            <a-input size='small' @keyup.enter.native='loadData' v-model='queryParam.testCode'>

            </a-input>
          </pbiSearchItem>
          <pbiSearchItem label='委托人' :span="4">
            <a-input size='small' @keyup.enter.native='loadData' v-model='queryParam.applicant'>
            </a-input>
          </pbiSearchItem>
<!--          <pbiSearchItem label='处理状态' :span="4">
            <a-select v-model="queryParam.abnormalStatus" style="width: 100%" @change='loadData' allowClear>
              <a-select-option value="techConfirm">
                未处理
              </a-select-option>
              <a-select-option value="engineerConfirm">
                已处理
              </a-select-option>
            </a-select>
          </pbiSearchItem>-->
          <pbiSearchItem label='累计天数' :span="4">
            <a-input size='small' @keyup.enter.native='loadData' v-model='queryParam.day'>

            </a-input>
          </pbiSearchItem>

          <pbiSearchItem :span="12" type='btn' class="search-container">
            <div class="secondary-btn">
              <a-button class="mr12" @click="loadData" type="primary">查询</a-button>
            </div>
            <div class="secondary-btn">
              <a-button class="mr12" v-if="hasPerm('abnormal:edit')" @click="showHandleAbnormal({})" type="primary">异常处理</a-button>
            </div>
            <div class="secondary-btn">
              <a-button class="mr12" v-if="hasPerm('abnormal:edit')" @click="openAgentList" type="primary">异常代理</a-button>
            </div>
            <div class="secondary-btn">
              <a-button class="mr12" v-if="hasPerm('abnormal:edit')" @click="ignoreAbnormal" type="primary">去除判定</a-button>
            </div>
            <div class="secondary-btn">
              <a-button @click="resetSearch">重置</a-button>
            </div>

          </pbiSearchItem>
        </pbiSearchContainer>
      </template>


      <template #table>
        <ag-grid-vue :style="{height:tableHeight}"
                     class='table ag-theme-balham'
                     :tooltipShowDelay="0"
                     :columnDefs='columnDefs'
                     :rowData='rowData'
                     rowSelection="multiple"
                     :gridOptions="gridOptions"
                     @grid-ready="onGridReady"
                     :defaultColDef='defaultColDef'>
        </ag-grid-vue>
      </template>
    </tableIndex>

    <a-modal :title="record.cellCode == null ? selectedRows.map(s => s.cellCode).join('\n')+'\n异常处理': record.cellCode +'\n异常处理'" width="80%" :height="600" :visible="confirmVisible"
             @ok="handleConfirm" @cancel="cancelHandle" @close="cancelHandle">

      <dataDetailin ref="dataDetailin2" ></dataDetailin>

      <a-form-item label="处理结果">
        <a-select v-model="handleResult"
                  @change="changeHandleMsg"
                  style="width: 160px;font-size: 14px;">
          <a-select-option value="ongoing">
            进行中
          </a-select-option>
          <a-select-option value="earlyEnd">
            状态正常-提前结束
          </a-select-option>
          <a-select-option value="acrException">
            内阻异常-终止测试
          </a-select-option>
          <a-select-option value="shellRust">
            壳体生锈-终止测试
          </a-select-option>
          <a-select-option value="batteryDisassembly">
            状态正常-电池拆解
          </a-select-option>
          <a-select-option value="operationError" >
            作业错误-终止测试
          </a-select-option>
          <a-select-option value="pressureDrop" >
            掉压失效-终止测试
          </a-select-option>
          <a-select-option value="abnormalHot">
            异常发热-终止测试
          </a-select-option>
          <a-select-option value="openShellAndLeak" >
            开壳漏液-终止测试
          </a-select-option>

          <a-select-option value="thermalRunaway" >
            热失控-终止测试
          </a-select-option>
          <a-select-option value="swelling" >
            鼓包形变-终止测试
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="描述">
        <a-select v-model="handleMsg"
                  v-if="handleResult == 'ongoing'"
                  style="width: 300px;font-size: 14px;">
          <a-select-option  v-for="value in abnormalDesc" :value="value">
            {{value}}
          </a-select-option>
        </a-select>
        <a-select v-model="handleMsg"
                  v-else-if="handleResult == 'operationError'"
                  style="width: 300px;font-size: 14px;">
          <a-select-option  value="作业错误不可恢复，终止">
            作业错误不可恢复，终止
          </a-select-option>
        </a-select>
        <a-select v-model="handleMsg"
                  v-else-if="handleResult == 'earlyEnd'"
                  style="width: 300px;font-size: 14px;">
          <a-select-option  value="确认终止">
            确认终止
          </a-select-option>
        </a-select>
        <a-select v-model="handleMsg"
                  v-else-if="handleResult == 'batteryDisassembly'"
                  style="width: 300px;font-size: 14px;">
          <a-select-option  value="确认拆解">
            确认拆解
          </a-select-option>
        </a-select>
        <a-select v-model="handleMsg"
                  v-else-if="handleResult == 'swelling'"
                  style="width: 300px;font-size: 14px;">
          <a-select-option  value="确认形变">
            确认形变
          </a-select-option>
        </a-select>
        <a-select v-model="handleMsg"
                  v-else
                  style="width: 300px;font-size: 14px;">
          <a-select-option value="电池性能异常">
            电池性能异常
          </a-select-option>
        </a-select>

      </a-form-item>


    </a-modal>
    <a-modal title="代理记录" :width="300" :height="600" :visible="agentVisible"
             @ok="confirmAgent" @cancel="cancelAgent" @close="cancelAgent">
      <div>
        <a-form-item label="代理人">
          <a @click="handleShowAddUser" v-if="!agent.agentAccount">选择</a>
          <div v-if="agent.agentAccount" style="display: flex; align-items: center;width: 80px" @click="handleShowAddUser">
            <a-avatar size="small" :src="agent.agentAvatar" class="avatar"></a-avatar>
            <div style="margin-left: 8px;">{{ agent.agentName }}</div>
          </div>
        </a-form-item>
        <a-form-item label="开始时间">
          <a-date-picker @change="onChangeBegin" />
        </a-form-item>
        <a-form-item label="结束时间">
          <a-date-picker @change="onChangeEnd" />
        </a-form-item>
      </div>

    </a-modal>
    <a-modal
      title="代理人选择"
      :visible="isShowAddUser"
      :width="528"
      centered
      @cancel="() => isShowAddUser = false"
    >
      <div class="share-modal">
        <div class="action-content">
          <a-input v-model="addUserParam.account" placeholder="请输入账号/姓名" allow-clear style="width: 200px;border-radius: 5px" @change="getUserList">
            <a-icon slot="prefix" type="search" />
          </a-input>
          <treeselect
            v-model="addUserParam.grantOrgIdList"
            placeholder="请选择部门"
            value-consists-of="BRANCH_PRIORITY"
            :limit="1"
            :multiple="true"
            :max-width="270"
            :options="orgOptions"
            @input="getUserList"
          />
        </div>
        <div class="mt16">
          <a-table
            :columns="addUserColumns"
            :dataSource="addUserData"
            :rowKey="(record) => record.account"
            :rowSelection="{ selectedRowKeys: userSelectedRowKeys, onChange: onUserSelectChange,type:'radio' }"
            :pagination="userPagination"
            @change="handleUserTableChange"
          >
            <div slot="name" slot-scope="text, record, index, columns" style="display: flex; align-items: center;">
              <a-avatar size="small" :src="record.avatarUrl" class="avatar"></a-avatar>
              <div style="margin-left: 8px;">{{ text }}</div>
            </div>

          </a-table>
        </div>
      </div>
      <template slot="footer">
        <a-button key="confirm" type="primary" @click="selectUser">
          <a>确定</a>
        </a-button>
        <a-button key="back" @click="() => isShowAddUser = false">
          <a>取消</a>
        </a-button>
      </template>
    </a-modal>
    <a-modal
      title="代理记录"
      :visible="isShowAgent"
      :width="568"
      height="600"
      centered
      @cancel="() => isShowAgent = false"
    >
      <tableIndex
        :pageLevel='1'
        :tableTotal='agentTableTotal'
        :pageTitleShow=false
        :loading='agentLoading'
        :otherHeight="500"
        @paginationChange="agentHandlePageChange"
        @paginationSizeChange="agentHandlePageChange"
      >
        <template #search>
          <pbiSearchContainer>
            <pbiSearchItem label='代理人' :span="12">
              <a-input size='small' @keyup.enter.native='loadAgentData' v-model='agentQueryParam.agentName'>
              </a-input>
            </pbiSearchItem>
            <pbiSearchItem :span="12" type='btn' class="search-container">

              <div class="secondary-btn">
                <a-button class="mr12" @click="loadAgentData" type="primary">查询</a-button>
              </div>
              <div class="secondary-btn">
                <a-button class="mr12" @click="agentVisible = true" type="primary">新增</a-button>
              </div>
              <div class="secondary-btn">
                <a-button @click="resetAgentSearch">重置</a-button>
              </div>
            </pbiSearchItem>
          </pbiSearchContainer>
        </template>
        <template #table>
          <ag-grid-vue style="height:300px"
                       class='table ag-theme-balham'
                       :tooltipShowDelay="0"
                       :columnDefs='agentColumnDefs'
                       :rowData='agentRowData'
                       :gridOptions="agentGridOptions"
                       @grid-ready="agentOnGridReady"
                       :defaultColDef='agentDefaultColDef'>
          </ag-grid-vue>
        </template>
      </tableIndex>

      <template slot="footer">
        <a-button key="back" @click="() => isShowAgent = false">
          <a>关闭</a>
        </a-button>
      </template>
    </a-modal>
    <a-modal :title="record.testCode +'\n去除异常判定'" :width="400" :height="200" :visible="conditionVisible"
             @ok="handleIgnoreConfirm" @cancel="cancelIgnoreHandle" @close="cancelIgnoreHandle">
      <a-form-item label="选择需要忽略的条件">
        <a-checkbox-group
          v-model="conditionChecked"
          :options="conditionOptions"
        />
      </a-form-item>

    </a-modal>
    <a-modal title="测试数据" width="80%" :height="600" :visible="dataVisible"  @cancel="closeData" >
      <template slot="footer">
        <div>
          <a-button key="back" @click="closeData">关闭</a-button>
        </div>
      </template>


      <dataDetailin ref="dataDetailin" ></dataDetailin>

    </a-modal>


    <logDetail ref="log"></logDetail>
  </div>
</template>

<script>
  import {
    earlyWarningCalendarListPage,
    earlyWarningEngineerConfirmCalendar,
    calendarAgentPageList,
    calendarAgentAdd
  } from "@/api/modular/system/testAbnormalManager";


  import logDetail from "@/views/system/earlyWarning/logDetail.vue";
  import {tLimsFolderListPage} from "@/api/modular/system/limsManager";
  import {testProgressGetBase,testProgressUpdateOnlyBean,validExportSizeOriData} from "@/api/modular/system/testProgressManager";
  import {mapGetters} from "vuex";
  import {getUserPage} from "@/api/modular/system/userManage";
  import Treeselect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css"
  import { getOrgTree } from '@/api/modular/system/orgManage'
  import pbiTabs from "@/components/pageTool/components/pbiTabs.vue";
  import dataDetailin from "@/views/system/earlyWarning/dataDetailin.vue";

  export default {
    components: {
      dataDetailin,
      pbiTabs,
      Treeselect,
      showHandle: {
        template: "<a class='code_link' @click='params.onClick(params.data)' v-if='params.data.handleResult == null && params.canHandle'>异常处理</a><span v-else>{{params.formatValue(params.value)}}</span>"
      },
      showData: {
        template: "<a class='code_link' @click='params.onClick(params.data)'>{{params.value}}</a>"
      },
      showLog: {
        template: "<a class='code_link' @click='params.onClick(params.data)'>{{params.value == 'engineerConfirm' ? '已处理' : '未处理'}}</a>"
      },
      avatar:{
        template:`<div style="display: flex; align-items: center;">
              <a-avatar size="small" :src="params.data[params.avatarUrl]" class="avatar"></a-avatar>
              <div style="margin-left: 8px;">{{ params.value }}</div>
            </div>`
      },
      logDetail
    },
    data() {
      return {
        abnormalStatusList:[
          {value:null,label:"全部"},
          {value:"techConfirm",label:"未处理"},
          {value:"engineerConfirm",label:"已处理"}
        ],
        agentQueryParam:{},
        isShowAgent:false,
        agent:{},
        addUserColumns: [
          {
            title: '姓名',
            dataIndex: 'name',
            width:100,
            scopedSlots: {
              customRender: "name"
            }
          },
          {
            title: '账号',
            dataIndex: 'account',
            align:'center'
          }

        ],
        addUserData:[],
        orgOptions:[],
        userSelectedRowKeys:[],
        userSelectedRows:[],
        userPagination:{
          current:1,
          pageSize:10,
          total:0,
          size:"small"
        },
        addUserParam:{
          grantOrgIdList:[]
        },
        isShowAddUser:false,
        conditionChecked:[],
        conditionOptions:[
          { label: "电芯电压≤2500mv", value: 1 },
          { label: "电芯电压＞4500mv，模组电压>45000mv", value: 2 },
          { label: "电压值≥平行样均值+100mv", value: 3 },
          { label: "电压值≤平行样均值-100mv", value: 4 },
          { label: "100%SOC中检后电压小于前阶段出箱电压", value: 5 },
          { label: "G/C/方形电池＞3mΩ，V圆柱&软包&模组＞30mΩ", value: 6 },
          { label: "产气值≥平行样均值*110%", value: 7 },
          { label: "重量值≥平行样均值*105%", value: 8 },
          { label: "重量值≤平行样均值*95%", value: 9 },
        ],
        conditionVisible:false,
        gridOptions: {
          onSelectionChanged: this.onSelectionChanged,
          suppressCellSelection: false
        },
        agentGridOptions: {
          suppressCellSelection: false
        },
        selectedRows:[],
        abnormalKeyValue: {
          ongoing: '进行中',
          earlyEnd: '状态正常-提前结束',
          batteryDisassembly: '状态正常-电池拆解',
          pressureDrop: '掉压失效-终止测试',
          abnormalHot: '异常发热-终止测试',
          openShellAndLeak: '开壳漏液-终止测试',
          shellRust: '壳体生锈-终止测试',
          operationError: '作业错误-终止测试',
          thermalRunaway: '热失控-终止测试',
          acrException: '内阻异常-终止测试',
          swelling: '鼓包形变-终止测试'
        },
        abnormalDesc: ["工步错误复测","设备故障复测","人为作业错误复测","二次确认复测","电池性能异常，继续测试","电池性能差异，继续测试","鼓包形变，继续测试"],
        dataVisible:false,
        handleMsg: null,
        handleResult: null,
        record: {},
        confirmVisible: false,
        agentVisible: false,
        visible: false,
        loading: false,
        agentLoading: false,
        hide: false,
        gridApi: null,
        columnApi: null,
        agentGridApi: null,
        agentColumnApi: null,
        isShowAllSearch: false,
        tableTotal: 0,
        agentTableTotal: 0,
        queryParam: {abnormalStatus:'techConfirm'},
        rowData: [],
        agentRowData: [],
        defaultColDef: {
          filter: false,
          floatingFilter: false,
          editable: false,
        },
        agentDefaultColDef: {
          filter: false,
          floatingFilter: false,
          editable: false,
        },
        tableHeight: document.body.clientHeight - 201 +'px' ,
        pageNo: 1,
        pageSize: 20,
        agentPageNo: 1,
        agentPageSize: 20,
        originalData:{lifeTestRecordDataMap:[{}]},
        agentColumnDefs: [
          {
            headerName: '序号',
            field: 'id',
            width: 50,
            cellRenderer: function (params) {
              return parseInt(params.node.id) + 1
            },
          },{
            headerName: '代理人',
            field: 'agentName',
            width: 120,
            cellRenderer: 'avatar',
            cellRendererParams: {avatarUrl: 'agentAvatar'},
          },{
            headerName: '被代理人',
            field: 'applicantName',
            width: 120,
            cellRenderer: 'avatar',
            cellRendererParams: {avatarUrl: 'applicantAvatar'},
          },{
            headerName: '开始时间',
            field: 'beginTime',
            width: 100
          },{
            headerName: '结束时间',
            field: 'endTime',
            width: 100
          },
        ],
        columnDefs: [
          {
            width: 40,
            checkboxSelection: true,
            headerCheckboxSelection:true
          },
          {
            headerName: '序号',
            field: 'id',
            width: 50,
            cellRenderer: function (params) {
              return parseInt(params.node.id) + 1
            },
          },

          {
            headerName: '处理状态',
            field: 'abnormalStatus',
            width: 80,
            cellRenderer: 'showLog',
            cellRendererParams: {onClick: this.openLog},
          },
          {
            headerName: '累计天数',
            field: 'day',
            width: 80,
          },
          {
            headerName: '测试阶段',
            field: 'isBefore',
            width: 80,
            cellRenderer: function (params) {
              if(params.data.orderNumber == 1){
                return "入箱中检"
              }else if(params.data.isBefore == 1 ){
                return "出箱中检"
              }else{
                return "入箱中检"
              }
            },
          }, {
            headerName: '测试状态',
            field: 'testStatus',
            width: 80,
          },{
            headerName: '技师提交时间',
            field: 'createTime',
            width: 140,
          },
          {
            headerName: '产品名称',
            field: 'productName',
            width: 80,
          }, {
            headerName: '委托单号',
            field: 'testCode',
            width: 110,
          }, {
            headerName: '委托人',
            field: 'applicant',
            width: 80,
          }, {
            headerName: '测试类型',
            field: 'testType',
            width: 110,
          }, {
            headerName: '测试项目',
            field: 'testProject',
            width: 150,
            cellRenderer: 'showData',
            cellStyle: () =>  {return {textAlign:'left'}},
            cellRendererParams: {onClick: this.checkCalendarReportByRecord},
            tooltipValueGetter: (p) => p.value,
          }, {
            headerName: '测试编码',
            field: 'cellCode',
            flex:1,
            minWidth: 150,
            cellRenderer: 'showData',
            cellStyle: () =>  {return {textAlign:'left'}},
            cellRendererParams: {onClick: this.openData},
            tooltipValueGetter: (p) => p.value,
          }, {
            headerName: '异常信息',
            field: 'abnormalMsg',
            flex:1.2,
            minWidth: 150,
            cellStyle: () =>  {return {textAlign:'left'}},
            tooltipValueGetter: (p) => p.value,
          }, {
            headerName: '测试员',
            field: 'testMan',
            width: 80,
          },/* {
            headerName: '组长',
            field: 'groupName',
            width: 80,
          },*/ {
            headerName: '责任人',
            field: 'engineerName',
            width: 100,
            cellRenderer: function (params) {
              if(params.data.isGroupCanHandle == 1){
                return params.data.groupName
              }else {
                return params.data.engineerName
              }
            },
          }, {
            headerName: '异常处理',
            field: 'handleResult',
            cellRenderer: 'showHandle',
            cellRendererParams: {onClick: this.showHandleAbnormal, formatValue: this.formatValue,canHandle:this.hasPerm('abnormal:edit')},
            tooltipValueGetter: (p) => p.data.handleMsg,
            width: 150,
          }
        ],

      }
    },
    computed:{
      ...mapGetters(['userInfo']),
    },
    methods: {
      cancelAgent(){
        this.agent = {}
        this.agentVisible = false
      },
      confirmAgent(){
        if(!this.agent.agentName || !this.agent.beginTime || !this.agent.endTime || this.agent.beginTime == '' || this.agent.endTime == ''){
          this.$message.warn('请先填写完整')
          return
        }

        calendarAgentAdd(this.agent).then(res => {
          if (res.success) {
            this.$message.success('申请成功')
            this.agentVisible = false
            this.loadAgentData()
          } else {
            this.$message.warn(res.message)
          }
        })
      },
      onChangeBegin(date, dateString) {
        this.agent.beginTime = dateString
      },
      onChangeEnd(date, dateString) {
        this.agent.endTime = dateString
      },
      handleShowAddUser(){
        this.isShowAddUser = true
        this.getUserList()
        this.getOrgList()
      },
      // 获取组织架构
      getOrgList(){
        getOrgTree({}).then(res => {
          if(res.success){
            this.orgOptions = []
            res.data[0].children.forEach(v => {
              let $item = {
                id: v.id,
                label:v.title
              }
              if(v.children.length !== 0){
                $item.children = []
                v.children.forEach(chilV => {
                  $item.children.push({
                    id:chilV.id,
                    label:chilV.title
                  })
                })
              }
              this.orgOptions.push($item)
            })
          }else{
            this.$message.error('部门信息查询失败：' + res.message)
          }
        })
      },
      handleUserTableChange(pagination, filters, sorter) {
        this.userPagination.current = pagination.current

        this.getUserList()
      },
      onUserSelectChange(keys,rows){
        this.userSelectedRowKeys = keys
        this.userSelectedRows = rows
      },
      selectUser(){
        if(this.userSelectedRows.length < 1){
          this.$message.warn('请先选择代理人')
          return
        }
        this.agent.agentAccount = this.userSelectedRows[0].account
        this.agent.agentName = this.userSelectedRows[0].name
        this.agent.agentAvatar = this.userSelectedRows[0].avatarUrl
        this.isShowAddUser = false
      },
      getUserList(){
        const params = {
          pageNo:this.userPagination.current,
          pageSize:this.userPagination.pageSize,
          searchValue:this.addUserParam.account || '',
          grantOrgIdList: this.addUserParam.grantOrgIdList.join(','),
        }

        getUserPage(params).then(res => {
          if (res.success) {
            this.userSelectedRows = []
            this.userSelectedRowKeys = []
            this.userPagination.total = res.data.totalRows
            this.addUserData = res.data.rows
            this.$forceUpdate()
          } else {
            this.$message.error('查询失败：' + res.message)
          }

        })

      },
      changeHandleMsg(){
        if(this.handleResult == 'operationError'){
          this.handleMsg = '作业错误不可恢复，终止'
        } else if(this.handleResult == 'earlyEnd' ){
          this.handleMsg = '确认终止'
        }else if(this.handleResult == 'swelling' ){
          this.handleMsg = '确认形变'
        }else if(this.handleResult == 'batteryDisassembly' ){
          this.handleMsg = '确认拆解'
        }else if(this.handleResult != 'operationError' && this.handleResult != 'ongoing'
          && this.handleResult != 'earlyEnd' && this.handleResult != 'swelling' && this.handleResult != 'batteryDisassembly'){
          this.handleMsg = '电池性能异常'
        }

      },

      onGridReady(params) {
        this.gridApi = params.api;
        this.columnApi = params.columnApi;
      },
      agentOnGridReady(params) {
        this.agentGridApi = params.api;
        this.agentColumnApi = params.columnApi;
      },
      onSelectionChanged(event) {
        // 获取当前选中的行
        const selectedNodes = this.gridApi.getSelectedNodes();
        const selectedData = selectedNodes.map(node => node.data);

        // 更新选中的行数据
        this.selectedRows = selectedData;

      },
      openData(record) {
        this.dataVisible = true
        this.$nextTick(() => {
          this.$refs.dataDetailin.openData(record)
        })
      },
      checkCalendarReportByRecord(record) {

        let checkAllDataFlag = this.userInfo.roles.filter(item => item.code === "check_all_calendar_report")

        // 管理员或者有【查看所有日历寿命测试项目报告】角色的用户
        if (this.userInfo.account === "superAdmin" || checkAllDataFlag.length > 0 || this.userInfo.name === record.applicant || this.userInfo.account == record.applicantAccount) {
          this.getCalendarReport(record)
        } else {
          tLimsFolderListPage({folderno:record.testCode}).then(res => {
            if(res.data.rows.length == 0){
              this.$message.warning('无权限查看其他申请人的报告！')
            }else{
              this.getCalendarReport(record)
            }

          })

        }
      },
      getCalendarReport(record) {
        const id = record.progressId;
        const alias = record.testAlias ? record.testAlias : "";
        validExportSizeOriData({ testProgressId: id, exportType: "handleResult" }).then(res => {
          if (res.success) {
            const videoFlag = res.data.videoFlag === '1' ? 1 : 0
            const pictureFlag = res.data.pictureFlag === '1' ? 1 : 0
            // 有离线数据，通过res.data.resultDataJson判断是否有在线数据
            window.open("/v_report_preview?testProgressId=" + id + "&alias=" + encodeURIComponent(alias) + "&offFlag=1&onlineFlag=" + (res.data.resultDataJson ? "1" : "0")
              + '&videoFlag=' + videoFlag + '&pictureFlag=' + pictureFlag + "&type=日历寿命", "_blank")
          } else {

            // 无离线数据，通过record.resultDataJson判断在线数据
            if (record.resultDataJson) {
              window.open("/v_report_preview?testProgressId=" + id + "&alias=" + encodeURIComponent(alias) + "&offFlag=0&onlineFlag=1" + "&type=日历寿命", "_blank") // 需要展示在线报告
            } else {
              this.$message.warning(res.message.replace("导出", "查看"))
            }
          }
        })
      },
      showHandleAbnormal(record) {

        if(Object.keys(record).length == 0 && this.selectedRows.length == 0){
          this.$message.warning("请先选择数据")
          return;
        }else if(Object.keys(record).length == 0 ){
          for (let i = 0; i < this.selectedRows.length; i++) {
            if(this.rowData.find(r => r.id == this.selectedRows[i].id).abnormalStatus == 'engineerConfirm'){
              this.$message.warning("请选择未处理的数据")
              return;
            }
          }

          const firstTodoId = this.selectedRows[0].todoId;
          if(this.selectedRows.some(item => item.todoId != firstTodoId)){
            this.$message.warning("请选择同一个单据的异常")
            return;
          }

        }
        this.record = record
        this.confirmVisible = true
        this.$nextTick(() => {
          this.$refs.dataDetailin2.openData(Object.keys(record).length != 0?record:this.selectedRows[0])
        })
      },
      ignoreAbnormal() {
        if(this.selectedRows.length == 0){
          this.$message.warning("请先选择数据")
          return;
        }
        if(this.selectedRows.length > 1){
          this.$message.warning("请选择一条数据")
          return;
        }
        this.record = this.selectedRows[0]

        testProgressGetBase({id:this.record.progressId}).then(res => {
          if(res.data.ignoreAbnormal){
            this.conditionChecked = JSON.parse(res.data.ignoreAbnormal)
          }
        })
        this.conditionVisible = true
      },
      openLog(record) {
        this.$refs.log.openData(record)
      },
      formatValue(key) {
        return this.abnormalKeyValue[key]
      },
      async handleConfirm() {
        if (this.handleResult == null || this.handleMsg == null) {
          this.$message.warning("请填写完整")
          return
        }
        if(this.record == undefined || this.record == null || Object.keys(this.record).length == 0){
          if(this.selectedRows.length == 0){
            this.$message.warning("请先选择数据")
            return;
          }
          for (let i = 0; i < this.selectedRows.length; i++) {
            await earlyWarningEngineerConfirmCalendar({
              id: this.selectedRows[i].id,
              handleResult: this.handleResult,
              handleMsg: this.handleMsg
            })

          }
          this.$message.success("提交成功")
          await this.loadData()
          await this.cancelHandle()

        }else{
          earlyWarningEngineerConfirmCalendar({
            id: this.record.id,
            handleResult: this.handleResult,
            handleMsg: this.handleMsg
          }).then(res => {
            if (res.success) {
              this.$message.success("提交成功")
              this.loadData()
              this.cancelHandle()
            } else {
              this.$message.error("提交异常")
            }
          })
        }


      },
      async handleIgnoreConfirm() {
        if(this.conditionChecked == undefined || this.conditionChecked == null || this.conditionChecked.length == 0){
          this.$message.warning("请先选择数据")
          return;
        }else{
          testProgressUpdateOnlyBean({
            id: this.record.progressId,
            ignoreAbnormal: JSON.stringify(this.conditionChecked)
          }).then(res => {
            if (res.success) {
              this.$message.success("提交成功")
              this.loadData()
            } else {
              this.$message.error("提交异常")
            }
          }).finally(() => {
              this.cancelIgnoreHandle()
          })
        }


      },

      cancelHandle() {
        this.handleResult = null
        this.handleMsg = null
        this.confirmVisible = false
        this.record = {}
      },
      async cancelIgnoreHandle() {
        this.conditionVisible = false
        this.conditionChecked = []
        this.record = {}
      },
      closeData() {
        this.dataVisible = false
      },

      handlePageChange(value) {
        let {current, pageSize} = value
        this.pageNo = current
        this.pageSize = pageSize
        this.loadData()

      },
      agentHandlePageChange(value) {
        let {current, pageSize} = value
        this.agentPageNo = current
        this.agentPageSize = pageSize
        this.loadAgentData()

      },
      resetSearch() {
        this.queryParam = {abnormalStatus:'techConfirm'}
        this.loadData()
      },
      resetAgentSearch() {
        this.agentQueryParam = {}
        this.loadAgentData()
      },

      openAgentList(){
        this.isShowAgent = true
        this.loadAgentData()
      },

      handleTabsChange(value) {
        this.queryParam.abnormalStatus = value
        this.loadData()
      },

      loadData() {
        this.loading = true
        earlyWarningCalendarListPage({
          ...{
            pageNo: this.pageNo,
            pageSize: this.pageSize
          }, ...this.queryParam
        }).then((res) => {
          if (res.success) {
            this.rowData = res.data.rows
            this.tableTotal = res.data.totalRows

          }
        }).finally(() => {
          if(this.rowData.length == 0 && this.pageNo > 1){
            // this.pageNo -= 1
            this.pageNo = Math.max(1, Math.ceil(this.tableTotal / this.pageSize))
            this.loadData()
          }
          this.loading = false
        })
      },
      loadAgentData() {
        this.agentLoading = true
        calendarAgentPageList({
          ...{
            pageNo: this.agentPageNo,
            pageSize: this.agentPageSize
          }, ...this.agentQueryParam
        }).then((res) => {
          if (res.success) {
            this.agentRowData = res.data.rows
            this.agentTableTotal = res.data.totalRows

          }
        }).finally(() => {
          if(this.rowData.length == 0 && this.agentPageNo > 1){
            // this.agentPageNo -= 1
            this.agentPageNo = Math.max(1, Math.ceil(this.agentTableTotal / this.agentPageSize))
            this.loadAgentData()
          }
          this.agentLoading = false
        })
      },

    }
    ,
    mounted() {
      this.loadData()
    }

  }
</script>

<style lang='less' scoped="">
  @import '/src/components/pageTool/style/pbiSearchItem.less';

  :root {
    --scroll-display: none;
    --scroll-border-bottom: none;
    --scroll-border-bottom-fixed: none;
  }

  /deep/ .ag-body-horizontal-scroll {
    border-bottom: var(--scroll-border-bottom) !important;
  }

  /deep/ .ag-body-horizontal-scroll-viewport {
    display: var(--scroll-display) !important;
    border-bottom: var(--scroll-border-bottom) !important;
  }

  /deep/ .ag-horizontal-left-spacer,
  /deep/ .ag-horizontal-right-spacer {
    border-bottom: var(--scroll-border-bottom-fixed) !important;
  }

  .page-container {
    height: auto;
  }

  /deep/ .code_link {
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  /deep/.ant-checkbox-group-item {
    display: block;
  }

  .share-modal .title{
    font-size: 16px;
    font-weight: 600;
    text-align: center;
  }
  .share-modal .action-content{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }


  /deep/ .ant-table-thead > tr > th {
    padding: 5px!important;
    font-size: 13px!important;
    color: rgba(0, 0, 0, .85)!important;
    font-weight: 500!important;
    text-align: center !important;
  }

  /deep/ .ant-table-tbody > tr > td {
    padding: 4px!important;
    color: #333!important;
    font-size: 12px!important;
    font-weight: 400!important;
  }


  /deep/.vue-treeselect{
    max-width: 270px;
  }

  /deep/.ant-input-affix-wrapper .ant-input:not(:last-child) {
    border-radius: 5px;
  }


  /deep/.ant-modal-body {
    padding: 10px;
  }
  /deep/.ant-form-item {
    margin: 0;
  }

</style>