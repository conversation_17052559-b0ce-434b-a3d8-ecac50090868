<template>

  <div>
    <div id="container" />
    <a-drawer
      placement="right"
      :closable="false"
      width="80%"
      :visible="visible2"
      @close="onClose1"
      :bodyStyle="{ height: '100%' }"
    >
      <iframe :src="pdfUrl+'#view=FitH,top'" width="100%" height="100%" ></iframe>
    </a-drawer>
  </div>

</template>


<script>

  import G6 from '@antv/g6';
  import { isObject } from '@antv/util';
  import {
   bomPushQuery
  } from "@/api/modular/system/bomManage"

  export default {
    props:["param"],
    data() {
      return {
        visible2: false,
        pdfUrl:''
      }
    },

    methods: {
      onClose1() {
        this.visible2= false;
      },

      getData(value,processId){
        bomPushQuery({id: value,processId:processId}).then((res) => {
          if (res.success) {

            const colorMap = {
              A样: '#1A91FF',
              B样: '#1A91FF',
              C样: '#FFAA15',
              D样: '#FFAA15',
            };

            G6.registerNode(
              'round-rect',
              {
                drawShape: function drawShape(cfg, group) {
                  const width = cfg.style.width;
                  const stroke = cfg.style.stroke;
                  const rect = group.addShape('rect', {
                    attrs: {
                      x: -width / 2,
                      y: -15,
                      width,
                      height: 30,
                      radius: 15,
                      stroke,
                      lineWidth: 3,
                      fillOpacity: 2,
                    },
                    name: 'rect-shape',
                  });
                  group.addShape('circle', {
                    attrs: {
                      x: -width / 2,
                      y: 0,
                      r: 3,
                      fill: stroke,
                    },
                    name: 'circle-shape',
                  });
                  group.addShape('circle', {
                    attrs: {
                      x: width / 2,
                      y: 0,
                      r: 3,
                      fill: stroke,
                    },
                    name: 'circle-shape2',
                  });
                  return rect;
                },
                getAnchorPoints: function getAnchorPoints() {
                  return [
                    [0, 0.5],
                    [1, 0.5],
                  ];
                },
                update: function update(cfg, item) {
                  const group = item.getContainer();
                  const children = group.get('children');
                  const node = children[0];
                  const circleLeft = children[1];
                  const circleRight = children[2];

                  const stroke = cfg.style.stroke;

                  if (stroke) {
                    node.attr('stroke', stroke);
                    circleLeft.attr('fill', stroke);
                    circleRight.attr('fill', stroke);
                  }
                },
              },
              'single-node',
            );

            G6.registerEdge('fund-polyline', {
              itemType: 'edge',
              draw: function draw(cfg, group) {
                const startPoint = cfg.startPoint;
                const endPoint = cfg.endPoint;

                const Ydiff = endPoint.y - startPoint.y;

                const slope = Ydiff !== 0 ? Math.min(500 / Math.abs(Ydiff), 20) : 0;

                const cpOffset = slope > 15 ? 0 : 16;
                const offset = Ydiff < 0 ? cpOffset : -cpOffset;

                const line1EndPoint = {
                  x: startPoint.x + slope,
                  y: endPoint.y + offset,
                };
                const line2StartPoint = {
                  x: line1EndPoint.x + cpOffset,
                  y: endPoint.y,
                };

                // 控制点坐标
                const controlPoint = {
                  x:
                    ((line1EndPoint.x - startPoint.x) * (endPoint.y - startPoint.y)) /
                    (line1EndPoint.y - startPoint.y) +
                    startPoint.x,
                  y: endPoint.y,
                };

                let path = [
                  ['M', startPoint.x, startPoint.y],
                  ['L', line1EndPoint.x, line1EndPoint.y],
                  ['Q', controlPoint.x, controlPoint.y, line2StartPoint.x, line2StartPoint.y],
                  ['L', endPoint.x, endPoint.y],
                ];

                if (Math.abs(Ydiff) <= 5) {
                  path = [
                    ['M', startPoint.x, startPoint.y],
                    ['L', endPoint.x, endPoint.y],
                  ];
                }

                const endArrow = cfg?.style && cfg.style.endArrow ? cfg.style.endArrow : false;
                if (isObject(endArrow)) endArrow.fill = stroke;
                const line = group.addShape('path', {
                  attrs: {
                    path,
                    stroke: colorMap[cfg.data && cfg.data.type],
                    lineWidth: 1.2,
                    endArrow,
                  },
                  name: 'path-shape',
                });

                const labelLeftOffset = 0;
                const labelTopOffset = 8;
                // amount
                const amount = group.addShape('text', {
                  attrs: {
                    text: cfg.data && cfg.data.amount,
                    x: line2StartPoint.x + labelLeftOffset,
                    y: endPoint.y - labelTopOffset - 2,
                    fontSize: 14,
                    textAlign: 'left',
                    textBaseline: 'middle',
                    fill: '#000000D9',
                  },
                  name: 'text-shape-amount',
                });
                // type
                group.addShape('text', {
                  attrs: {
                    text: cfg.data && cfg.data.type,
                    x: line2StartPoint.x + labelLeftOffset,
                    y: endPoint.y - labelTopOffset - amount.getBBox().height - 2,
                    fontSize: 10,
                    textAlign: 'left',
                    textBaseline: 'middle',
                    fill: '#000000D9',
                  },
                  name: 'text-shape-type',
                });
                // date
                group.addShape('text', {
                  attrs: {
                    text: cfg.data && cfg.data.date,
                    x: line2StartPoint.x + labelLeftOffset,
                    y: endPoint.y + labelTopOffset + 4,
                    fontSize: 12,
                    fontWeight: 300,
                    textAlign: 'left',
                    textBaseline: 'middle',
                    fill: '#000000D9',
                  },
                  name: 'text-shape-date',
                });
                return line;
              },
            });

            const width = document.documentElement.clientWidth*0.75;
            const height = document.documentElement.clientHeight - 50;




            const toolbar = new G6.ToolBar({

            });

            const graph = new G6.Graph({
              container: 'container',
              plugins: [toolbar],
              width,
              height,
              layout: {
                type: 'dagre',
                rankdir: 'LR',
                nodesep: 30,
                ranksep: 100,
              },
              maxZoom:6,
              fitView:'true',
              modes: {
                default: ['drag-canvas'],
              },
              defaultNode: {
                type: 'round-rect',
                labelCfg: {
                  style: {
                    fill: '#000000A6',
                    fontSize: 15,
                  },
                },
                style: {
                  stroke: '#72CC4A',
                  width: 150,
                },
              },
              defaultEdge: {
                type: 'fund-polyline',
              },
            });

            graph.data(res.data);



            graph.render();

            const edges = graph.getEdges();
            edges.forEach(function (edge) {
              const line = edge.getKeyShape();
              const stroke = line.attr('stroke');
              const targetNode = edge.getTarget();
              targetNode.update({
                style: {
                  stroke,
                },
              });
            });



            graph.paint();
            if (typeof window !== 'undefined')
              window.onresize = () => {
                if (!graph || graph.get('destroyed')) return;
                if (!container || !container.scrollWidth || !container.scrollHeight) return;
                graph.changeSize(container.scrollWidth, container.scrollHeight);
              };



            graph.on('node:click', e => {
              const nodeItem = e.item

              if(nodeItem._cfg.model.label == 'JIRA'){

                window.open('http://jira.evebattery.com/browse/' + nodeItem._cfg.model.url, "_blank");
              }
              if(nodeItem._cfg.model.label == 'OA'){

                window.open(process.env.VUE_APP_OA_URL+'/km/review/km_review_main/kmReviewMain.do?method=view&fdId=' + nodeItem._cfg.model.url, "_blank");
              }
            });


            graph.on('node:click', e => {

              const nodeItem = e.item

              if(null != nodeItem._cfg.model.fileId ){
                this.pdfUrl = process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id='+nodeItem._cfg.model.fileId
                this.visible2 = true
              }

            });



            graph.on('edge:click', e => {


              const nodeItem = e.item

              if(nodeItem._cfg.model.label == 'JIRA'){

                window.open('http://jira.evebattery.com/browse/' + nodeItem._cfg.model.url, "_blank");
              }
              if(nodeItem._cfg.model.label == 'OA'){

                window.open(process.env.VUE_APP_OA_URL+'/km/review/km_review_main/kmReviewMain.do?method=view&fdId=' + nodeItem._cfg.model.url, "_blank");
              }
            });


            graph.on('edge:click', e => {
              const nodeItem = e.item
              if(null != nodeItem._cfg.model.fileId ){
                this.pdfUrl = process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id='+nodeItem._cfg.model.fileId
                this.visible2 = true
              }

            });

            graph.on('click', e => {
             console.info(e)
            });






/*

            graph.on('node:mouseenter', ev=>{
              const nodeItem = ev.item
              if(nodeItem._cfg.model.label == 'JIRA' || nodeItem._cfg.model.label == 'OA'){


                nodeItem._cfg.model.labelCfg.style.fill = '#40a9ff'

                ev.item.destroy();
              }

            });
            graph.on('node:mouseleave', ev=>{
              const nodeItem = ev.item
              if(nodeItem._cfg.model.label == 'JIRA' || nodeItem._cfg.model.label == 'OA'){
                nodeItem._cfg.model.labelCfg.style.fill = '#000000A6'
              }

            });

            graph.on('canvas:click', ev=>{

            });
*/


          } else {
            this.$message.error('查询出错：' + res.message)
          }

        })

      }


		},
    mounted () {
      this.getData(this.param.historyBomId,this.param.processId)
    },

  }
</script>

<style>

</style>