<template>



  <div>


    <a-form :form="form3">

      <div class="Timeline" style="height: 300px;padding: 50px 0;background: white">

        <a-button type="primary" style="position: absolute;right: 10px;top: 75px;" v-if="hasPerm('project:updatePlan')" @click="updateDate">修改</a-button>
        <div class="timeaxis">

          <div>
            <div class="timeaxis-box">
              <div class="timeaxis-topText" :class="{'bd-empty':false}">

              </div>
              <div :class="k0?'circular':'circular1'"></div>
              <div class="timeaxis-bootomText">
                <div class="downText">K0</div>
                <div class="downTime">
                  <a-form-item>
                    <a-date-picker style="width: 120px" placeholder="请选择开始时间"
                                   :allow-clear="false"
                                   :disabled="k0 || unEdit"
                                   :disabledDate="(date) => disabledStartDate(date,null)"
                                   v-decorator="['productPlannedK0', {rules: [{required: true, message: ''}]}]" @change="(date) => changeDate(date,'productPlannedK0')"/>
                  </a-form-item>
                </div>
                <div class="downMsg">立项评审{{k0?'实际':'计划'}}日期</div>
              </div>
            </div>
          </div>
          <div>
            <div class="timeaxis-box">
              <div class="timeaxis-topText" :class="{'bd-empty':false}">
                <div class="upText">M1</div>
                <div class="upTime">
                  <a-form-item>
                    <a-date-picker style="width: 120px" placeholder="请选择开始时间"
                                   :allow-clear="false"
                                   :disabled="M1 || unEdit"
                                   :disabledDate="(date) => disabledStartDate(date,'productPlannedK0')"
                                   @change="(date) => changeDate(date,'productPlannedM1')"
                                   v-decorator="['productPlannedM1', {rules: [{required: true, message: ''}]}]"/>
                  </a-form-item>
                </div>
                <div class="upMsg">项目{{k0?'实际':'计划'}}日期</div>
              </div>
              <div :class="M1?'circular':'circular1'"></div>

            </div>
          </div>
          <div>
            <div class="timeaxis-box">
              <div class="timeaxis-topText" :class="{'bd-empty':false}">

              </div>
              <div :class="M2A?'circular':'circular1'"></div>
              <div class="timeaxis-bootomText">
                <div class="downText">M2</div>
                <div class="downTime">
                  <a-form-item>
                    <a-date-picker style="width: 120px" placeholder="请选择开始时间"
                                   :allow-clear="false"
                                   :disabled="M2A || unEdit"
                                   :disabledDate="(date) => disabledStartDate(date,'productPlannedM1')"
                                   @change="(date) => changeDate(date,'productPlannedM2A')"
                                   v-decorator="['productPlannedM2A', {rules: [{required: true, message: ''}]}]"/>
                  </a-form-item>
                </div>
                <div class="downMsg">A样冻结{{M2A?'实际':'计划'}}日期</div>
              </div>
            </div>
          </div>
          <div>
            <div class="timeaxis-box">
              <div class="timeaxis-topText" :class="{'bd-empty':false}">
                <div class="upText">M2</div>
                <div class="upTime">
                  <a-form-item>
                    <a-date-picker style="width: 120px" placeholder="请选择开始时间"
                                   :allow-clear="false"
                                   :disabled="M2z || unEdit"
                                   :disabledDate="(date) => disabledStartDate(date,'productPlannedM2A')"
                                   @change="(date) => changeDate(date,'productPlannedM2z')"
                                   v-decorator="['productPlannedM2z', {rules: [{required: true, message: ''}]}]"/>
                  </a-form-item>
                </div>
                <div class="upMsg">转阶段{{M2z?'实际':'计划'}}日期</div>
              </div>
              <div :class="M2z?'circular':'circular1'"></div>

            </div>
          </div>
          <div>
            <div class="timeaxis-box">
              <div class="timeaxis-topText" :class="{'bd-empty':false}">

              </div>
              <div :class="M3B?'circular':'circular1'"></div>
              <div class="timeaxis-bootomText">
                <div class="downText">M3</div>
                <div class="downTime">
                  <a-form-item>
                    <a-date-picker style="width: 120px" placeholder="请选择开始时间"
                                   :allow-clear="false"
                                   :disabled="M3B || unEdit"
                                   :disabledDate="(date) => disabledStartDate(date,'productPlannedM2z')"
                                   @change="(date) => changeDate(date,'productPlannedM3B')"
                                   v-decorator="['productPlannedM3B', {rules: [{required: true, message: ''}]}]"/>
                  </a-form-item>
                </div>
                <div class="downMsg">B样冻结{{M3B?'实际':'计划'}}日期</div>
              </div>
            </div>
          </div>
          <div>
            <div class="timeaxis-box">
              <div class="timeaxis-topText" :class="{'bd-empty':false}">
                <div class="upText">M3</div>
                <div class="upTime">
                  <a-form-item>
                    <a-date-picker style="width: 120px" placeholder="请选择开始时间"
                                   :allow-clear="false"
                                   :disabled="M3z || unEdit"
                                   :disabledDate="(date) => disabledStartDate(date,'productPlannedM3B')"
                                   @change="(date) => changeDate(date,'productPlannedM3z')"
                                   v-decorator="['productPlannedM3z', {rules: [{required: true, message: ''}]}]"/>
                  </a-form-item>
                </div>
                <div class="upMsg">转阶段{{M3z?'实际':'计划'}}时间</div>
              </div>
              <div :class="M3z?'circular':'circular1'"></div>

            </div>
          </div>
          <div>
            <div class="timeaxis-box">
              <div class="timeaxis-topText" :class="{'bd-empty':false}">

              </div>
              <div :class="M4?'circular':'circular1'"></div>
              <div class="timeaxis-bootomText">
                <div class="downText">M4</div>
                <div class="downTime">
                  <a-form-item>
                    <a-date-picker style="width: 120px" placeholder="请选择开始时间"
                                   :allow-clear="false"
                                   :disabled="M4 || unEdit"
                                   :disabledDate="(date) => disabledStartDate(date,'productPlannedM3z')"
                                   @change="(date) => changeDate(date,'productPlannedM4')"
                                   v-decorator="['productPlannedM4', {rules: [{required: true, message: ''}]}]"/>
                  </a-form-item>
                </div>
                <div class="downMsg">C样冻结{{M4?'实际':'计划'}}日期</div>
              </div>
            </div>
          </div>
          <div>
            <div class="timeaxis-box">
              <div class="timeaxis-topText" :class="{'bd-empty':false}">
                <div class="upText">M5</div>
                <div class="upTime">
                  <a-form-item>
                    <a-date-picker style="width: 120px" placeholder="请选择开始时间"
                                   :allow-clear="false"
                                   :disabled="M5 || unEdit"
                                   :disabledDate="(date) => disabledStartDate(date,'productPlannedM4')"
                                   @change="(date) => changeDate(date,'productPlannedM5')"
                                   v-decorator="['productPlannedM5', {rules: [{required: true, message: ''}]}]"/>
                  </a-form-item>
                </div>
                <div class="upMsg">PPAP{{M5?'实际':'计划'}}日期</div>
              </div>
              <div :class="M5?'circular':'circular1'"></div>

            </div>
          </div>
          <div>
            <div class="timeaxis-box1">
              <div class="timeaxis-topText" :class="{'bd-empty':false}">
                <a-icon type="caret-right" style="font-size: 20px;font-size: 20px;position: relative;top: -11px;right: -80px;color: #c9cdd4"/>
              </div>
              <div :class="M6?'circular':'circular1'"></div>
              <div class="timeaxis-bootomText" style="top: -23px;">
                <div class="downText">M6</div>
                <div class="downTime">
                  <a-form-item>
                    <a-date-picker style="width: 120px" placeholder="请选择开始时间"
                                   :allow-clear="false"
                                   :disabled="M6 || unEdit"
                                   :disabledDate="(date) => disabledStartDate(date,'productPlannedM5')"
                                   @change="(date) => changeDate(date,'productPlannedM6')"
                                   v-decorator="['productPlannedM6', {rules: [{required: true, message: ''}]}]"/>
                  </a-form-item>
                </div>
                <div class="downMsg">SOP{{M6?'实际':'计划'}}日期</div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </a-form>
    <planEditForm ref="planEditForm" :issueId="issueId" :projectdetail="projectdetail" @ok="refreshDetail"/>
  </div>







</template>

<script>
  import moment from "moment";
  import planEditForm from './planEditForm'

  import {productMsgToUpdate,getProjectDetail} from "@/api/modular/system/report";



  export default {
    components: {
      planEditForm
    },
    props: {
      issueId: {
        type: Number,
        default: 0
      },

      projectdetail: {
        type: Object,
        default: {}
      }
    },
    data() {
      return {
        form3:this.$form.createForm(this),
        unEdit:true,
        k0:false,
        M1:false,
        M2A:false,
        M2z:false,
        M3B:false,
        M3z:false,
        M4:false,
        M5:false,
        M6:false,
        keys:['k0','M1','M2A','M2z','M3B','M3z','M4','M5','M6']

      }
    },
    computed: {

    },
    created() {
      /**
       *   private String productPlannedK0;//K0立项评审计划日期
       private String productPlannedM1;//M1项目计划日期
       private String productPlannedM2A;//M2A样冻结计划日期
       private String productPlannedM2z;//M2转阶段计划日期
       private String productPlannedM3B;//M3B样冻结计划日期
       private String productPlannedM3z;//M3转阶段计划日期
       private String productPlannedM4;//M4C样冻结计划日期
       private String productPlannedM5;//M5 PPAP计划日期
       private String productPlannedM6;//M6 SOP计划日期
       */
      this.initDate()
    },
    methods: {

      initDate(){

        let k0 = this.projectdetail.productStageItems.find(e=>e.stage == 1)
        let M1 = this.projectdetail.productStageItems.find(e=>e.stage == 2)
        let M2A = this.projectdetail.productStageItems.find(e=>e.stage == 3)
        let M2z = this.projectdetail.productStageItems.find(e=>e.stage == 4)
        let M3B = this.projectdetail.productStageItems.find(e=>e.stage == 5)
        let M3z = this.projectdetail.productStageItems.find(e=>e.stage == 6)
        let M4 = this.projectdetail.productStageItems.find(e=>e.stage == 7)
        let M5 = this.projectdetail.productStageItems.find(e=>e.stage == 8)
        let M6 = this.projectdetail.productStageItems.find(e=>e.stage == 9)

        if(k0 != null && k0.actualCompletionDate != null){
          this.k0 = true
        }
        if(M1 != null && M1.actualCompletionDate != null){
          this.M1 = true
        }
        if(M2A  != null&& M2A.actualCompletionDate != null){
          this.M2A = true
        }
        if(M2z != null && M2z.actualCompletionDate != null){
          this.M2z = true
        }
        if(M3B != null && M3B.actualCompletionDate != null){
          this.M3B = true
        }
        if(M3z != null && M3z.actualCompletionDate != null){
          this.M3z = true
        }
        if(M4 != null && M4.actualCompletionDate != null){
          this.M4 = true
        }
        if(M5 != null && M5.actualCompletionDate != null){
          this.M5 = true
        }
        if(M6 != null && M6.actualCompletionDate != null){
          this.M6 = true
        }




        //判断有后面阶段的时间，则前面时间不显示
        /*for (let i = 0; i < this.keys.length; i++) {
          for (let j = i+1; j < this.keys.length; j++) {
            if(this[this.keys[j]]){
              this[this.keys[i]] = true
              break
            }
          }
        }*/

        this.$nextTick(() => {

          this.form3.setFieldsValue({
            productPlannedK0:this.k0?(k0.actualCompletionDate == null?null:moment(k0.actualCompletionDate)):this.projectdetail.productPlannedK0 == null?null:moment(this.projectdetail.productPlannedK0),
            productPlannedM1:this.M1?(M1.actualCompletionDate == null?null:moment(M1.actualCompletionDate)):this.projectdetail.productPlannedM1 == null?null:moment(this.projectdetail.productPlannedM1),
            productPlannedM2A:this.M2A?(M2A.actualCompletionDate == null?null:moment(M2A.actualCompletionDate)):this.projectdetail.productPlannedM2A == null?null:moment(this.projectdetail.productPlannedM2A),
            productPlannedM2z:this.M2z?(M2z.actualCompletionDate == null?null:moment(M2z.actualCompletionDate)):this.projectdetail.productPlannedM2z == null?null:moment(this.projectdetail.productPlannedM2z),
            productPlannedM3B:this.M3B?(M3B.actualCompletionDate == null?null:moment(M3B.actualCompletionDate)):this.projectdetail.productPlannedM3B == null?null:moment(this.projectdetail.productPlannedM3B),
            productPlannedM3z:this.M3z?(M3z.actualCompletionDate == null?null:moment(M3z.actualCompletionDate)):this.projectdetail.productPlannedM3z == null?null:moment(this.projectdetail.productPlannedM3z),
            productPlannedM4:this.M4?(M4.actualCompletionDate == null?null:moment(M4.actualCompletionDate)):this.projectdetail.productPlannedM4 == null?null:moment(this.projectdetail.productPlannedM4),
            productPlannedM5:this.M5?(M5.actualCompletionDate == null?null:moment(M5.actualCompletionDate)):this.projectdetail.productPlannedM5 == null?null:moment(this.projectdetail.productPlannedM5),
            productPlannedM6:this.M6?(M6.actualCompletionDate == null?null:moment(M6.actualCompletionDate)):this.projectdetail.productPlannedM6 == null?null:moment(this.projectdetail.productPlannedM6)
          })
        })
      },

      refreshDetail(){
        let params = {issueId: this.issueId,title:''}
        getProjectDetail(params).then((res)=>{
          if (res.result) {
            this.projectdetail = res.data
            this.initDate()
          } else {
            this.$message.error(res.message,1);
          }
          this.loading = false
        })
          .catch((err)=>{
            this.loading = false
            this.$message.error('错误提示：' + err.message,1)
          });

      },
      disabledStartDate(startValue,column,column2) {

        if(column == null){
          return startValue >  moment(this.form3.getFieldValue(column2))
        }else
        if(column2 == null){
          return startValue < moment(this.form3.getFieldValue(column))
        }else{
          return startValue < moment(this.form3.getFieldValue(column))|| startValue >  moment(this.form3.getFieldValue(column2))

        }

      },
      updateDate(){
        this.$refs.planEditForm.edit()
      },
      changeDate(date,column){

        if(null != date){
          let param = {}
          param[column] = moment(date).format("YYYY-MM-DD")
          param['issueId'] = this.issueId
          productMsgToUpdate(param).then((res) => {
            if (res.result) {
              this.$message.success('产品信息更新完成',1)
            } else {
              this.$message.error('错误提示：' + res.message, 1)
            }
          }).catch((res) => {
            this.$message.error('错误提示：' + res.message, 1)
          })
        }


      }
    }
  }
</script>
<style lang="less" scoped=''>

  /deep/.ant-input[disabled]{
    border: none;
  }

  .Timeline {
    display: flex;
    justify-content: center;
    /* margin-top: 40px; */
    .timeaxis {
      height: 50px;
      margin-top: 80px;
      margin-left: -20px;
      display: flex;
      .timeaxis-box {
        width: 100px;
        .circular {
          width: 12px;
          height: 12px;
          border-radius: 16px;
          background: #4ef72d;
          margin-bottom: -4px;
          position: relative;
          top: -8px;
        }
        .circular1 {
          width: 12px;
          height: 12px;
          border-radius: 16px;
          background: #23a9d5;
          margin-bottom: -4px;
          position: relative;
          top: -8px;

        }
        .timeaxis-topText {
          border-bottom: 2px solid #c9cdd4;
          position: relative;
          .text {
            position: absolute;
            top: -60px;
          }
          .tiem {
            position: absolute;
            top: -35px;
            font-size: 14px;
            color: #9ba3ad;
          }
        }
        .bd-empty {
          border: 0;
        }
        .timeaxis-bootomText {
          position: relative;
          .text {
            position: absolute;
            top: 10px;
            font-size: 14px;
          }
        }
      }
      .timeaxis-box1 {
        width: 90px;
        .circular {
          width: 12px;
          height: 12px;
          border-radius: 16px;
          background: #0ef753;
          margin-bottom: -4px;
          position: relative;
          top: -8px;
        }
        .circular1 {
          width: 12px;
          height: 12px;
          border-radius: 16px;
          background: #23a9d5;
          margin-bottom: -4px;
          position: relative;
          top: -30px;

        }
        .timeaxis-topText {
          border-top: 2px solid #c9cdd4;
          position: relative;
          .text {
            position: absolute;
            top: -60px;
          }
          .tiem {
            position: absolute;
            top: -35px;
            font-size: 14px;
            color: #9ba3ad;
          }
        }
        .bd-empty {
          border: 0;
        }
        .timeaxis-bootomText {
          position: relative;
          .text {
            position: absolute;
            top: 10px;
            font-size: 14px;
          }
        }
      }
    }
  }


  .downText{
    position: absolute;
    top: 5px;
    color: black;
    font-weight: bolder;
  }
  .downTime{
    position: absolute;
    top: 25px;
  }
  .downMsg{
    position: absolute;
    top: 60px;
    width: 120px;
  }

  .upText{
    position: absolute;
    top: -100px;
    color: black;
    font-weight: bolder;
  }
  .upTime{
    position: absolute;
    top: -75px;
  }
  .upMsg{
    position: absolute;
    top: -40px;
  }


  /deep/.ant-input[disabled] {
    color: black;
    background-color: white;
  }

  /deep/.ant-input {
    padding: 0;
  }

  /deep/ svg[data-icon="calendar"]{
    display: none;
  }


</style>
