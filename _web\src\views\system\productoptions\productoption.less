/deep/.ant-table {
  margin: 0;
}

/deep/.ant-table-wrapper {
  background: #fff;
}

/deep/.ant-table-thead>tr>th {
  font-weight: bold;
  background: #fbfbfb !important;
}

/deep/.ant-table-small>.ant-table-content>.ant-table-body {
  margin: 0;
}

.head2 {
  padding: 14px 0;
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  color: #333;
  font-size: 16px;
  line-height: 1;
}

/deep/.vue-treeselect__multi-value-item {
  background: transparent;
  font-size: 13px;
  vertical-align: initial;
}

/deep/.vue-treeselect {
  min-width: 80%;
  max-width: 95%;
  margin-top: 4px;
}

/deep/.vue-treeselect__control {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  overflow: hidden;
  border-radius: initial;
}

/deep/.vue-treeselect__control * {
  padding: 0 !important;
  white-space: nowrap;
}

/deep/.vue-treeselect__value-remove {
  color: #e9e9e9;
}

/deep/.vue-treeselect__multi-value-item {
  color: #695959;
}

/deep/ .ant-col {
  padding: 2px !important;
}

// 筛选框
/deep/.table-page-search-wrapper {
  padding: 14px;
  background: #fff;
}

/deep/.table-page-search-wrapper .ant-form-inline .ant-form-item {
  margin: 0;
}

/deep/.ant-row {
  margin: 0 !important;
}

/deep/.table-page-search-wrapper .table-page-search-submitButtons {
  line-height: 2.5;
  margin: 0;
}

.state {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 3px;
  margin-left: 3px;
}

.state.green {
  background: #91cc75;
}

.state.yellow {
  background: #efeb73;
}

.state.red {
  background: #f54747;
}

// 子组件
.product_width {
  padding: 0 10px;
}

// 筛选框高度
/deep/.ant-input-affix-wrapper .ant-input {
  height: 30px !important;
}

// 为了调整筛选框的统一性
/deep/.vue-treeselect {
  margin-top: 1px;
}

/deep/.vue-treeselect__multi-value-item-container {
  vertical-align: text-top;
}

// 特殊处理 产品类别居中
/deep/.filter-box .vue-treeselect__multi-value-item {
  margin: 8px 0 0;
}

/deep/.vue-treeselect__limit-tip-text {
  margin: 15px 4px 0;
}

// 图表标题
.form-title {
  text-align: center;
  padding: 8px 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
  white-space: 2px;
}


// 表格padding
/deep/.ant-table-thead>tr>th,
.ant-table-tbody>tr>td {
  padding: 10px;
}

// 分页大小
/deep/.ant-pagination-disabled,
.ant-pagination-next {
  min-width: 20px;
  height: 20px;
  line-height: 20px;
}

/deep/.ant-pagination-prev {
  min-width: 20px;
  height: 20px;
  line-height: 20px;
}

/deep/.ant-pagination-next {
  min-width: 20px;
  height: 20px;
  line-height: 20px;
}

/deep/.ant-pagination-jump-prev {
  min-width: 20px;
  height: 20px;
  line-height: 20px;
}

/deep/.ant-pagination-jump-next {
  min-width: 20px;
  height: 20px;
  line-height: 20px;
}

/deep/.ant-pagination-item {
  min-width: 25px;
  height: 25px;
  line-height: 25px;
}

/deep/.ant-table-thead tr th {
  background: #f3f3f3 !important;
  color: #333;
}

// 表头固定
/deep/.ant-table-thead {
  position: sticky;
  top: 0;
}

/deep/.ant-table-placeholder {
  border: none;
}