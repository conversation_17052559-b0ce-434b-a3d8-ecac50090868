<template>
	<ScreenAdapter>
		<div class="container">
			<!-- 筛选框 start -->
			<div class="filter-wrapper">
				<span>创建时间：</span>
				<a-date-picker class="mr10" :default-value="startCreateDate" @change="handleStartDateChange" />
				<span class="mr10">~</span>
				<a-date-picker class="mr10" :default-value="endCreateDate" @change="handleEndDateChange" />
				<span>
					<a-button icon="search" type="primary" @click="handleDateSearch(0)">
						搜索
					</a-button>
				</span>
			</div>
			<!-- 筛选框 end -->

			<!-- 数据 start -->
			<a-spin :spinning="dataSpinning">
				<div class="data-wrapper mt10">
					<div
						class="data-block"
						v-for="(item, index) in dataMenu"
						:key="index"
						:style="`background-image: url(${item.url})`"
					>
						<p>{{ item.title }}</p>
						<p>{{ item.num || 0 }}{{ item.unit }}</p>
					</div>
				</div>
			</a-spin>
			<!-- 数据 end -->

			<!-- echarts start -->
			<div class="echarts-wrapper mt10">
				<div class="frame mr10">
					<div class="title">
						<div class="line" style="background: #3293ff;"></div>
						实验报告提交情况
					</div>
					<a-spin :spinning="submitSpinning">
						<div class="chart_table h1" ref="submit"></div>
					</a-spin>
				</div>
				<div class="frame">
					<div class="title">
						<div class="line" style="background: #F55C84"></div>
						实验报告延期状态
					</div>
					<a-spin :spinning="delaySpinning">
						<div class="chart_table h2" ref="delay"></div>
					</a-spin>
				</div>
			</div>
			<!-- echarts end -->

			<!-- 筛选框 start -->
			<div class="filter-wrapper mt10">
				<div>
					<span>到期日：</span>
					<a-date-picker class="mr10" :default-value="startDueDate" @change="handleStartDueDateChange" />
					<span class="mr10">~</span>
					<a-date-picker class="mr10" :default-value="endDueDate" @change="handleEndDueDateChange" />
				</div>
				<div class="filter-block mr10">
					<span>所属部门：</span>
					<treeselect
						class="filter-tree"
						:limit="1"
						@input="handleChangeDept"
						placeholder="请选择所属部门"
						:multiple="true"
						:options="departmentCateTreeData"
						value-consists-of="BRANCH_PRIORITY"
						v-model="depts"
					>
					</treeselect>
				</div>
				<div class="filter-block mr10">
					<div>责任工程师：</div>
					<div>
						<a-input type="hidden" />
						<a-dropdown v-model="isShowStaff" placement="bottomCenter" :trigger="['click']">
							<a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;"
								>{{ responsible ? responsible : "选择责任工程师"
								}}<a-icon class="close mr5" @click="handleClear" type="close"/>
								<a-icon :type="isShowStaff ? 'up' : 'down'"
							/></a-button>

							<a-menu slot="overlay">
								<a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
									<a-input-search
										v-model="usqueryParam.searchValue"
										placeholder="请输入工号或姓名"
										@change="onusSearch"
									/>
									<!-- :pagination="{defaultPageSize:5, pageSizeOptions: ['5', '10', '15', '20'],  size:'small' }" -->
									<s-table
										style="width:100%;"
										ref="ustable"
										:rowKey="record => record.id"
										:columns="vcolumns"
										:data="loadusData"
										:customRow="customusRow"
										:pagination="{
											pageSizeOptions: ['5', '10', '15', '20']
										}"
										:pageSize="5"
										:scroll="{ y: 270, x: 120 }"
									>
									</s-table>
								</a-spin>
							</a-menu>
						</a-dropdown>
					</div>
				</div>
				<div>
					<a-button icon="search" type="primary" @click="handleDateSearch(1)">
						搜索
					</a-button>
				</div>
			</div>
			<!-- 筛选框 end -->

			<!-- table start -->
			<div class="frame mt10">
				<div class="title">
					<div class="line" style="background: #FA9A50;"></div>
					延期实验报告清单
				</div>

				<a-spin :spinning="tableSpinning">
					<div class="table-wrapper">
						<a-table style="height: 390px;" :columns="columns" :data-source="tableData" :rowKey="record => record.id" />
					</div>
				</a-spin>
			</div>
			<!-- table end -->
		</div>
	</ScreenAdapter>
</template>
<script>
import html2canvas from "html2canvas"

import moment from "moment"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"

import ScreenAdapter from "@/components/ScreenAdapter/index"
import { STable } from "@/components"

import { getStatusNum, getSubmitStatus, getDelayStatus, getReportList } from "@/api/modular/system/testReport"
import { getCateTree } from "@/api/modular/system/topic"
import { getUserLists } from "@/api/modular/system/userManage"

export default {
	data() {
		return {
			responsible: "",
			responsiblePersonId: "",
			form: this.$form.createForm(this),

			dataSpinning: false,
			submitSpinning: false,
			delaySpinning: false,
			tableSpinning: false,
			isShowStaff: false,
			loading: false,

			startCreateDate: moment()
				.startOf("year")
				.format("YYYY-MM-DD"),
			endCreateDate: moment()
				.subtract(0, "d")
				.format("YYYY-MM-DD"),
			startDueDate: moment()
				.startOf("year")
				.format("YYYY-MM-DD"),
			endDueDate: moment()
				.subtract(0, "d")
				.format("YYYY-MM-DD"),
			depts: [],
			delayStatus: {},
			usqueryParam: {},
			tableData: [],

			departmentCateTreeData: [],
			vcolumns: [
				{
					title: "账号",
					dataIndex: "account"
				},
				{
					title: "姓名",
					dataIndex: "name"
				}
			],
			dataMenu: [
				{ url: require("../../../assets/images/blue-bg.png"), title: "总实验单（份）", num: 1952, unit: "" },
				{ url: require("../../../assets/images/green-bg.png"), title: "已提交报告（份）", num: 1859, unit: "" },
				{ url: require("../../../assets/images/orange-bg.png"), title: "延期报告", num: 83, unit: "" },
				{ url: require("../../../assets/images/purple-bg.png"), title: "报告完成率", num: "95", unit: "%" },
				{ url: require("../../../assets/images/pink-bg.png"), title: "报告延期率", num: "5", unit: "%" }
			],
			columns: [
				{
					title: "序号",
					dataIndex: "no",
					align: "center",
					customRender: (text, record, index) => {
						return `${index + 1}`
					}
				},
				{
					title: "部门",
					dataIndex: "deptName",
					align: "center"
				},
				{
					title: "实验型号",
					dataIndex: "experimentType",
					align: "center",
					customRender: text => {
						return text ? text : "-"
					}
				},
				{
					title: "部门+基地+年月日+流水号",
					dataIndex: "experimentNumber",
					align: "center"
				},
				{
					title: "到期日",
					dataIndex: "dueDate",
					align: "center"
				},
				{
					title: "延期天数",
					dataIndex: "delayDay",
					align: "center"
				},
				{
					title: "责任工程师",
					dataIndex: "responsiblePerson",
					align: "center",
					customRender: text => {
						return text ? text : "-"
					}
				},
				{
					title: "方案审批经理",
					dataIndex: "directLeader",
					align: "center"
				},
				{
					title: "实验报告状态/延误原因",
					dataIndex: "issuestatusName",
					align: "center"
				}
			],
			loadusData: parameter => {
				return getUserLists(Object.assign(parameter, this.usqueryParam)).then(res => {
					return res.data
				})
			},
			config: {
				time: "00:00:00", // 每天几点执行
				interval: 1, // 以天为单位
				runNow: false, // 是否立即执行
				intervalTimer: "",
				timeOutTimer: ""
			}
		}
	},
	components: {
		ScreenAdapter,
		Treeselect,
		STable
	},
	created() {
		// 定时任务
		this.scheduledTasks()
	},
	mounted() {
		this.init()
		this.getCateTree()
		this.getReportList({ startDueDate: this.startDueDate, endDueDate: this.endDueDate })
	},
	methods: {
		init(startCreateDate = this.startCreateDate, endCreateDate = this.endCreateDate) {
			const params = { startCreateDate, endCreateDate }
			this.getStatusNum(params)
			this.getSubmitStatus(params)
			this.getDelayStatus(params)
		},
		// 查询数据展示
		getStatusNum(params) {
			this.dataSpinning = true
			getStatusNum(params)
				.then(res => {
					if (!res.result) this.$message.error(`错误信息：${res.message}`)
					this.dataMenu[0].num = res.data.allReport
					this.dataMenu[1].num = res.data.submitReport
					this.dataMenu[2].num = res.data.delayReport
					this.dataMenu[3].num = res.data.reportCompleteRate
					this.dataMenu[4].num = res.data.reportDelayRate
				})
				.finally(() => {
					this.dataSpinning = false
				})
		},
		getCateTree() {
			getCateTree({
				fieldName: "department"
			})
				.then(res => {
					if (!res.success) this.$message.error(`错误信息：${res.message}`)
					this.departmentCateTreeData = JSON.parse(JSON.stringify(res.data))
					this.departmentCateTreeData.forEach(v => {
						v.label = v.title
						v.id = v.key
						if (v.children !== null) {
							v.children.forEach(e => {
								e.label = e.title
								e.id = e.key
								if (e.children === null) delete e.children
							})
						} else {
							delete v.children
						}
					})
				})
				.finally(() => {})
		},

		// 提交情况
		getSubmitStatus(params) {
			this.submitSpinning = true
			getSubmitStatus(params)
				.then(async res => {
					if (!res.result) this.$message.error(`错误信息：${res.message}`)
					const xAxisData = [],
						submitData = [],
						waitSubmitData = [],
						delayData = [],
						rateData = []
					const temList = this._handleData(res.data)
					// 推值
					temList.forEach(value2 => {
						xAxisData.push(value2.name)
						submitData.push(value2.isSubmit)
						waitSubmitData.push(value2.unDelay)
						delayData.push(value2.isDelay)
						rateData.push(value2.reportCompleteRate)
					})
					this.initChartSubmit(xAxisData, submitData, waitSubmitData, delayData, rateData)
				})
				.finally(() => {
					this.submitSpinning = false
				})
		},
		// 延期状态
		getDelayStatus(params) {
			this.delaySpinning = true
			getDelayStatus(params)
				.then(res => {
					if (!res.result) this.$message.error(`错误信息：${res.message}`)
					this.delayStatus = { ...res.data }
					this.initChartDelay(this.delayStatus)
				})
				.finally(() => {
					this.delaySpinning = false
				})
		},
		//
		getReportList(params) {
			this.tableSpinning = true
			getReportList(params)
				.then(res => {
					if (!res.result) this.$message.error(`错误信息：${res.message}`)
					this.tableData = [...res.data]
				})
				.finally(() => {
					this.tableSpinning = false
				})
		},

		// 提交图表
		initChartSubmit(xAxisData, submitData, waitSubmitData, delayData, rateData) {
			let chart = this.echarts.init(this.$refs.submit, null, { renderer: "svg" })
			chart.off("click")
			chart.clear()
			const options = {
				color: ["#3293FF", "#0074DC", "#FA9A50", "#63BE59"],
				tooltip: {
					trigger: "axis",
					axisPointer: {
						type: "cross"
					},
					formatter: function(arg) {
						if (arg[0].axisValue === "") return null

						let result = arg[0].axisValue + "\n"
						arg.forEach(v => {
							result += v.seriesName + ":" + v.value + "\n"
						})
						return result
					},
					extraCssText: "white-space:pre-wrap"
				},
				legend: {
					data: ["已提交报告数", "待提交未延期报告数", "延期提交报告数", "提交达成率"],
					top: "bottom",
					itemHeight: 10,
					itemWidth: 15
				},
				grid: {
					top: "5%",
					left: "2%",
					right: "2%",
					bottom: "10",
					containLabel: true
				},
				xAxis: [
					{
						type: "category",
						axisTick: {
							alignWithLabel: true
						},
						axisLabel: {
							show: true,
							interval: 0,
							rotate: -45,
							textStyle: {
								fontSize: "10"
							}
						},
						data: xAxisData
					}
				],
				yAxis: [
					{
						type: "value"
					},
					{
						type: "value",
						show: false
					}
				],
				series: [
					{
						name: "已提交报告数",
						type: "bar",
						yAxisIndex: 0,
						data: submitData,
						stack: "x"
					},
					{
						name: "待提交未延期报告数",
						type: "bar",
						yAxisIndex: 0,
						data: waitSubmitData,
						stack: "x"
					},
					{
						name: "延期提交报告数",
						type: "bar",
						yAxisIndex: 0,
						data: delayData,
						stack: "x"
					},
					{
						name: "提交达成率",
						type: "line",
						yAxisIndex: 1,
						data: rateData
					}
				]
			}
			chart.setOption(options)
			chart.resize()
		},
		// 延期图表
		initChartDelay(data) {
			let chart = this.echarts.init(this.$refs.delay, null, { renderer: "svg" })
			chart.off("click")
			chart.clear()

			const options = {
				color: ["#3293FF", "#0074DC", "#FA9A50"],
				legend: {
					top: "bottom",
					icon: "circle",
					type: "scroll"
				},
				tooltip: {
					trigger: "item"
				},
				grid: {
					top: "5%",
					left: "5%",
					right: "5%",
					bottom: "15%"
				},
				series: [
					{
						type: "pie",
						radius: "70%",
						center: ["50%", "50%"],
						label: {
							normal: {
								show: true,
								color: "#ffffff",
								position: "inner", // 数值显示在内部
								formatter: function(params) {
									return "{a|" + params.data.value + "}\n{hr|}\n  {b|" + params.data.name + "}"
								},
								rich: {
									a: {
										color: "#fff",
										align: "center",
										fontSize: 18,
										fontWeight: "bold"
									},
									b: {
										color: "#fff",
										fontSize: 12,
										align: "center"
									}
								}
							}
						},
						data: [
							{ value: data.gt0le30, name: "0<LT≤30" },
							{ value: data.gt30le90, name: "30<LT≤90" },
							{ value: data.gt90, name: "LT>90" }
						]
						// .sort(function(a, b) {
						// 	return b.value - a.value
						// })
					}
				]
			}
			chart.setOption(options)
			chart.resize()
		},
		// 处理数据
		_handleData(data) {
			const list = []

			for (let v in data.departmentMap) {
				Reflect.ownKeys(data.departmentMap[v]).forEach((e, index, arr) => {
					Reflect.ownKeys(data.submitStatusMap).forEach(value => {
						// 如果是一个所的最后一项
						if (index === arr.length - 1 && value === e) {
							list.push({ department: v, name: data.departmentMap[v][e], ...data.submitStatusMap[value] })
							list.push({ department: "", name: "", isDelay: 0, isSubmit: 0, unDelay: 0, reportCompleteRate: 0 })
							return
						}
						if (value === e) {
							list.push({ department: v, name: data.departmentMap[v][e], ...data.submitStatusMap[value] })
						}
					})
				})
			}

			// 删除最后一个部门的最后一项
			list.pop()

			// 计算没有柱子的拐点值
			list.forEach((element, index1) => {
				if (element.name === "") {
					const temValue = (
						(Number(list[index1 - 1].reportCompleteRate) + Number(list[index1 + 1].reportCompleteRate)) /
						2
					).toFixed(2)
					element.reportCompleteRate = {
						value: temValue,
						symbolSize: 0
					}
				}
			})

			return list
		},
		handleStartDateChange(date, dateString) {
			this.startCreateDate = dateString
		},
		handleEndDateChange(date, dateString) {
			this.endCreateDate = dateString
		},
		handleStartDueDateChange(date, dateString) {
			this.startDueDate = dateString
		},
		handleEndDueDateChange(date, dateString) {
			this.endDueDate = dateString
		},
		handleDateSearch(index) {
			if (index)
				return this.getReportList({
					startDueDate: this.startDueDate,
					endDueDate: this.endDueDate,
					departmentIdList: this.depts,
					responsiblePersonId: this.responsiblePersonId
				})

			this.init(this.startCreateDate, this.endCreateDate, this.depts, this.responsiblePersonId)
		},
		handleChangeDept(value) {
			this.depts = value
		},
		handleClear() {
			this.responsible = ""
			this.responsiblePersonId = ""
		},
		onusSearch(e) {
			this.$refs.ustable.refresh()
		},
		customusRow(row, index) {
			return {
				on: {
					click: () => {
						this.form.setFieldsValue({
							projectLeaderId: row.account
						})
						this.responsible = row.name
						this.responsiblePersonId = row.account
						this.isShowStaff = false
					}
				}
			}
		},

		//截图
		screenShot() {
			//获取页面dom
			//这里的html标签是获取页面最大的dom元素；根据实际业务场景自行更改
			const el = document.querySelector("html")
			html2canvas(el, { allowTaint: true }).then(canvas => {
				//document.body.appendChild(canvas)  页面布局会乱
				//转换base64
				const capture = canvas.toDataURL("image/png")
				//下载浏览器弹出下载信息的属性
				const saveInfo = {
					//导出文件格式自己定义，我这里用的是时间作为文件名
					download: moment().format("YYYY-MM-DD HH:mm:ss") + `.png`,
					href: capture
				}
				//下载，浏览器弹出下载文件提示
				this.downloadFile(saveInfo)

				//调用保存接口 如果需要后台保存，放开注释
				/*   uploadImage({capture:capture}).then(res => {
          if (res.code == 200) {
            this.$message.success("截取成功！")
          }
        });*/
			})
		},

		//下载截图
		downloadFile(saveInfo) {
			const element = document.createElement("a")
			element.style.display = "none"
			for (const key in saveInfo) {
				element.setAttribute(key, saveInfo[key])
			}
			document.body.appendChild(element)
			element.click()
			setTimeout(() => {
				document.body.removeChild(element)
			}, 300)
		},

		scheduledTasks() {
			if (this.config.runNow) {
				// 如果配置了立刻运行则立刻运行任务函数
				this.screenShot()
			}
			// 获取下次要执行的时间，如果执行时间已经过了今天，就让把执行时间设到明天的按时执行的时间
			var nowTime = new Date().getTime()
			var timePoint = this.config.time.split(":").map(i => parseInt(i))

			var recent = new Date().setHours(...timePoint) // 获取执行时间的时间戳

			if (recent <= nowTime) {
				recent += 24 * 60 * 60 * 1000
			}
			// 未来程序执行的时间减去现在的时间，就是程序要多少秒之后执行
			var doRunTime = recent - nowTime

			this.config.timeOutTimer = setTimeout(this.setTimer, doRunTime)
		},
		setTimer() {
			//配置后的第一天12点执行
			this.screenShot()
			// 每隔多少天再执行一次
			var intTime = this.config.interval * 24 * 60 * 60 * 1000
			this.config.intervalTimer = setInterval(this.screenShot, intTime)
		}
	}
}
</script>

<style lang="less" scoped>
p {
	margin-bottom: 0;
}
/deep/tr th {
	padding: 0.375rem 0.5rem;
}
/deep/tr td {
	padding: 0.375rem 0.5rem;
}
.container {
	background: #fff;
}

// 筛选框
.filter-wrapper {
	display: flex;
	align-items: center;
}

.filter-wrapper .filter-block {
	display: flex;
	align-items: center;
}

.filter-wrapper .filter-tree {
	width: 270px;
}
// 数据
.data-wrapper {
	display: flex;
	justify-content: space-between;
}
.data-block {
	width: 226px;
	height: 85px;
	background-size: 100% 100%;
	padding: 17px 0 0 31px;
	box-sizing: border-box;
}

.data-block p:first-child {
	font-size: 18px;
	color: rgba(255, 255, 255, 0.85);
}
.data-block p:last-child {
	margin-top: 5px;
	font-size: 26px;
	color: #fff;
}

// 图表
.echarts-wrapper {
	display: flex;
	justify-content: space-between;
}

.chart_table {
	height: 280px;
}

.h1 {
	width: 877px;
}

.h2 {
	width: 289px;
}

.table-wrapper {
	// height: 390px;
	overflow: auto;
}
.levitated-sphere {
	position: fixed;
	bottom: 5px;
	left: 5px;
	z-index: 2;
}

.levitated-sphere img {
	width: 40px;
	height: 40px;
}
.close {
	pointer-events: auto;
}

// 通用
.mt10 {
	margin-top: 10px;
}
.mr5 {
	margin-right: 5px;
}
.mr10 {
	margin-right: 10px;
}
// 边框
.frame {
	border-radius: 2px;
	border: 1px solid #cccccc;
	padding: 10px;
}

// 标题
.title {
	display: flex;
	align-items: center;
}
.title .line {
	margin-right: 7px;

	width: 6px;
	height: 35px;
	border-radius: 20px;
}

// 组件

/deep/.ant-calendar-picker-input {
	width: 150px;
}
/deep/.ant-table-placeholder {
	height: 365px;
	box-sizing: border-box;
	border: none;
}

/deep/.ant-table-thead {
	position: sticky;
	top: 0;
}
/deep/.vue-treeselect__control {
	border-radius: 2px;
}
/deep/.vue-treeselect--has-value .vue-treeselect__multi-value {
	margin-bottom: 0;
}
</style>
