<template>
  <a-modal title="支持角色" width="50%" :visible="visible" :confirmLoading="confirmLoading" :footer="null"
           @cancel="handleCancel">

    <a-button type="primary" @click="openAdd" style="float: right;margin-right: 20px;margin-bottom: 10px;z-index: 5" v-if="canUpdate">新增</a-button>


    <a-table :columns="columns" :data-source="data" :rowKey="(record) => record.id"
    >

      <template slot="action" slot-scope="text, record">

        <a-popconfirm placement="topRight" ok-text="确定" cancel-text="取消" @confirm="deleteRole(record.id)" v-if="canUpdate">
          <template slot="title">
            确定删除吗？
          </template>
          <a>删除</a>
        </a-popconfirm>


      </template>


    </a-table>

    <role-add ref="roleAdd" @ok="getList" />
  </a-modal>
</template>

<script>

  import {mapGetters} from "vuex";
  import {
    batteryDesignRoleList,batteryDesignRoleDelete
  } from '@/api/modular/system/batterydesignCheckRecordManage'
  import roleAdd from "./roleAdd"


  export default {
    components: {
      roleAdd
    },
    computed: {
      ...mapGetters(['userInfo'])
    },
    data() {
      return {
        batteryId:null,
        canUpdate:true,
        design:{},
        data:[],
        columns: [

          {
            title: '人员',
            width: 70,
            dataIndex: 'name',
            align: 'center',
            scopedSlots: {
              customRender: 'name'
            }
          },  {
            title: '角色',
            width: 70,
            dataIndex: 'roleName',
            align: 'center',
          }, {
            title: '操作',
            width: 60,
            align: 'center',
            scopedSlots: {
              customRender: 'action'
            }
          }

        ],

        visible: false,
        confirmLoading: false,
      }
    },

    methods: {
      deleteRole(id){
        batteryDesignRoleDelete({id:id}).then((res) => {
          this.getList()
        })
      },

      open(batteryId,record) {
        this.design = record
        let role = this.userInfo.roles.find(r => r.id == 1684366114540503042)

        if(this.userInfo.account != 'superAdmin' && this.userInfo.account != record.pdId
          && this.userInfo.account != record.rpmId && this.userInfo.account != record.ptId  ){
          if(role != null && role != undefined){
            this.canUpdate = true
          }else{
            this.canUpdate = false
          }
        }else{
          this.canUpdate = true
        }


        this.batteryId = batteryId
        batteryDesignRoleList({batteryId:batteryId}).then((res) => {
          this.data = res.data
        })
        this.visible = true
      },

      getList(){
        batteryDesignRoleList({batteryId:this.batteryId}).then((res) => {
          this.data = res.data
        })
      },

      handleCancel() {
        this.data = []
        this.visible = false
      },
      openAdd() {
        this.$refs.roleAdd.open(this.batteryId)
      },

    }
  }
</script>
<style lang="less" scoped>
  .ant-form-item {
    margin-bottom: 0px;
  }

  .title {
    margin-bottom: 20px;
  }

  .xing {
    color: red;
    font-weight: bolder;
  }

  .label {
    width: 30%;
    display: inline-flex;
  }

  .theme {
    font-size: larger;
    font-weight: bolder;
    margin-bottom: 20px;
  }

  div {
    color: black;
  }

  span {
    color: black;
  }

  .select {
    margin-bottom: 3px;
  }

  .check {
    height: 210px;
  }

  .approve {
    height: 90px;
  }

  /deep/ .ant-modal-title {

    font-weight: bolder;
    font-size: 22px;
    color: #0049b0;
  }

  /deep/.ant-table-tbody tr td{
	padding: 10px 16px !important;
}

</style>
