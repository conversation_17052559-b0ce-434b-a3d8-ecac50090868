<template>
  <div class="container">

    <div style="display: flex;justify-content: space-between;">
      <div class="head-title">
        <div class="line mr10"></div>
        <span class="title">产品认证</span>
      </div>
      <div class="filter-wrapper">
        <div class="filter-right">
          <div>

          </div>
        </div>
      </div>
    </div>
    <div class="table-wrapper mt10">

      <tableIndex
        :pageLevel='1'
        :tableTotal='tableTotal'
        :pageTitleShow=false
        :loading='loading'
        :otherHeight="parseInt(96 + width)"
        @paginationChange="handlePageChange"
        @paginationSizeChange="handlePageChange"
      >
        <template #search>
          <pbiSearchContainer>
            <pbiSearchItem :span="6" label='产品名称'>
              <a-input v-model="queryParam.productName" @keyup.enter="loadData"
                       @blur="loadData"/>
            </pbiSearchItem>
            <pbiSearchItem :span="6" label='认证项目'>
              <a-input v-model="queryParam.testProjectName" @keyup.enter="loadData"
                       @blur="loadData"/>
            </pbiSearchItem>
            <pbiSearchItem :span="6" label='委托单号'>
              <a-input v-model="queryParam.folderNo" @keyup.enter="loadData"
                       @blur="loadData"/>
            </pbiSearchItem>

            <pbiSearchItem :span="6" type='btn'>
              <a-button type="primary" style="float: right;margin-right: 12px" @click="refresh">查询</a-button>
              <a-button type="primary" style="float: right;margin-right: 12px" @click="add">新增</a-button>
              <a-button type="primary" style="float: right;margin-right: 12px" @click="updateSwitch(false)">
                {{ !updateStatus ? '修改' : '完成' }}
              </a-button>
              <a-button  @click="reset" >重置</a-button>
            </pbiSearchItem>

          </pbiSearchContainer>
        </template>


        <template #table>
          <ag-grid-vue :style="{height:tableHeight}"
                       class='table ag-theme-balham'
                       :tooltipShowDelay="0"
                       :columnDefs="tableColumns"
                       :rowData='rowData'
                       :gridOptions="gridOptions"
                       @grid-ready="onGridReady"
                       :defaultColDef='defaultColDef'>
          </ag-grid-vue>
        </template>
      </tableIndex>

    </div>

    <pbi-preview-new ref="pbiPreviewNew" width="80%" :show-file-name="true"/>
<!--    <a-drawer
      :bodyStyle="{ height: '100%' }"
      placement="right"
      :closable="false"
      width="80%"
      destroy-on-close
      :visible="filePreviewVisible"
      @close="filePreviewVisible = false"
    >
      <iframe :src="iframeUrl" width="100%" height="100%"></iframe>
    </a-drawer>-->

  </div>
</template>
<script>
import pbiPreviewNew from '@/components/pageTool/components/pbiPreviewNew.vue'
import {
  testProjectOutTestDataPageList, testProjectOutTestDataUpdate,testProjectOutTestDataAdd
} from "@/api/modular/system/dpvTestManage";
import Vue from "vue";
import {
  getMinioDownloadUrl, getMinioPreviewUrl
} from '@/api/modular/system/fileManage'
import {downloadMinioFile} from "@/utils/util"
import {shenghongDataExportTaskPageList} from "@/api/modular/system/limsManager";
import _ from "lodash"

export default {
  props: {
    width:{
      type: Number,
      default: 0
    },
    padding:{
      type: String,
      default: '8px'
    }
  },
  components: {
    pbiPreviewNew,
    updateString:{
      template:'<div><a-input v-if="params.updateStatus" v-model="params.data[params.column.colId]" @blur="params.onUpdateData($event,params.data,params)"></a-input><span v-else>{{ params.value }}</span></div>'
    },
    file:{
      template:`
        <div style="display: flex">
          <div v-if="params.data.fileName">
            <div v-for="(name, index) in params.data.fileName.split('?')" :key="index" style="display: inline-block; margin-right: 10px;">
              <a @click="params.openFileOrDownloadByFileId(params.data.fileIds.split(',')[index])">
                {{ name.split('.').slice(0, -1).join('.') }}
              </a>

              <a-popconfirm
                placement="top"
                ok-text="删除"
                cancel-text="取消"
                @confirm="params.deleteFile(params.data, 'fileStatus',params.data.fileIds.split(',')[index],'fileId')"
              >
                <template #title>确定删除 "{{ name }}" 吗</template>
                <a-icon type="delete" style="margin-left: 5px; cursor: pointer;" />
              </a-popconfirm>
            </div>
          </div>


          <a-upload
            :headers="params.headers"
            :action="params.postUrl"
            :data="params.uploadData"
            @change="params.fileChange($event, params.data, 'fileId', 'fileName')"
            :showUploadList="false"
          >
            <a-icon type="upload"/>
          </a-upload>
        </div>
      `
    },
    certificateFile:{
      template:`<div style="display: flex">
          <div v-if="params.data.certificateFileName">
            <div v-for="(name, index) in params.data.certificateFileName.split('?')" :key="index" style="display: inline-block; margin-right: 10px;">
              <a @click="params.openFileOrDownloadByFileId(params.data.certificateFileIds.split(',')[index])">
                {{ name.split('.').slice(0, -1).join('.') }}
              </a>

              <a-popconfirm
                placement="top"
                ok-text="删除"
                cancel-text="取消"
                @confirm="params.deleteFile(params.data, 'certificateFileStatus',params.data.certificateFileIds.split(',')[index],'certificateFileId')"
              >
                <template #title>确定删除 "{{ name }}" 吗</template>
                <a-icon type="delete" style="margin-left: 5px; cursor: pointer;" />
              </a-popconfirm>
            </div>
          </div>


          <a-upload
            :headers="params.headers"
            :action="params.postUrl"
            :data="params.uploadData"
            @change="params.fileChange($event, params.data, 'certificateFileId', 'certificateFileName')"
            :showUploadList="false"
          >
            <a-icon type="upload"/>
          </a-upload>
        </div>`
    },
    updateSelect:{
      template:'<div>\n' +
        '              <a-select v-if="params.updateStatus" style="width: 100%" v-model="params.data[params.column.colId]"\n' +
        '                        @change="params.updateSelectData($event,params.data,params)">\n' +
        '                <a-select-option value="申请中">\n' +
        '                  申请中\n' +
        '                </a-select-option>\n' +
        '                <a-select-option value="有效">\n' +
        '                  有效\n' +
        '                </a-select-option>\n' +
        '                <a-select-option value="失效">\n' +
        '                  失效\n' +
        '                </a-select-option>\n' +
        '              </a-select>\n' +'<span v-else \n' +
        '                    :style="{background:params.value == \'申请中\' ?\'#fec303\':\n' +
        '              params.value == \'有效\' ? \'#66b72a\':params.value == \'失效\' ?\'#e05328\':\'unset\',display: \'block\'}"\n' +
        '                    >{{ params.value }}</span>' +
        '            </div>'
    },
    updateNum:{
      template:'<div slot="updateNum">\n' +
        '              <a-input-number v-if="params.updateStatus" v-model="params.data[params.column.colId]"\n' +
        '                              @blur="params.updateData($event,params.data,params)"></a-input-number>\n' +
        '              <span v-else :title="params.value">{{ params.value }}</span>\n' +
        '            </div>'
    },
    updateDate:{
      template:'<div>\n' +
        '              <a-date-picker v-if="params.updateStatus" :allowClear="false" v-model="params.data[params.column.colId]" format=\'YYYY-MM-DD\'\n' +
        '                             style="width: 100%"\n' +
        '                             @change="(date, dateString) => params.updateDataDate(dateString, params.data,params)"/>\n' +
        '              <span v-else :title="params.value">{{ params.value }}</span>\n' +
        '            </div>'
    },
    delete:{
      template:'<div slot="delete">\n' +
        '              <a-popconfirm placement="topLeft" ok-text="删除" cancel-text="取消"\n' +
        '                            @confirm="params.deleteOne(params.data)">\n' +
        '                <template slot="title">\n' +
        '                  确定删除吗\n' +
        '                </template>\n' +
        '                <a-icon style="margin-right: 5px" type="delete"/>\n' +
        '              </a-popconfirm>\n' +
        '            </div>'
    }
  },
  data() {
    return {
      fileName:null,
      previewFileUrl:null,
      tableTotal:0,
      rowData:[],
      loading: false,
      pageNo: 1,
      pageSize: 20,
      gridApi: null,
      columnApi: null,
      gridOptions: {
        onSelectionChanged: this.onSelectionChanged,
        suppressCellSelection: false,
        onColumnResized: _.debounce(this.pbiRefreshCells,500)

      },
      defaultColDef: {
        filter: false,
        floatingFilter: false,
        editable: false,
        tooltipValueGetter: this.pbiTooltip,
        cellStyle: { 'text-align': 'center' }
      },
      tableHeight: document.body.clientHeight - 196 - this.width +'px' ,
      uploadData: {bucket: 'dpvproductcert'},
      postUrl: '/api/sysFileInfo/minioUpload',
      headers: {
        Authorization: 'Bearer ' + Vue.ls.get('Access-Token'),
      },
      updateStatus: false,
      filePreviewVisible: false,
      iframeUrl: '',
      queryParam: {},
      spinning: false,
      tableColumns:[],
      tableData: [],
    }
  },
  created() {

  },
  mounted() {
    this.loadData()
  },
  methods: {
    updateSwitch(flag){
      if(flag){
        // this.updateStatus = !this.updateStatus
      }else{
        this.updateStatus = !this.updateStatus
      }

      this.tableColumns =  [
        {
          headerName: '序号',
          field: 'id',
          width: 50,
          pinned:'left',
          cellRenderer: function (params) {
            return parseInt(params.node.id) + 1
          }
        },
        {
          headerName: '产品名称',
          field: 'productName',
          width: 200,
          pinned: true,
          cellRenderer: 'updateString',
          cellRendererParams: {onUpdateData: this.updateData,updateStatus:this.updateStatus}
        }, {
          headerName: '样品阶段',
          field: 'phase',
          width: 80,
          pinned: true,
          cellRenderer: 'updateString',
          cellRendererParams: {onUpdateData: this.updateData,updateStatus:this.updateStatus}
        }, {
          headerName: '证书类别',
          field: 'secondCategory',
          width: 80,
          pinned: true,
          cellRenderer: 'updateString',
          cellRendererParams: {onUpdateData: this.updateData,updateStatus:this.updateStatus}
        }, {
          headerName: '认证项目',
          field: 'testProjectName',
          width: 150,
          pinned: true,
          cellRenderer: 'updateString',
          cellRendererParams: {onUpdateData: this.updateData,updateStatus:this.updateStatus}
        }, {
          headerName: '检测依据',
          field: 'code',
          width: 150,
          pinned: true,
          cellRenderer: 'updateString',
          cellRendererParams: {onUpdateData: this.updateData,updateStatus:this.updateStatus}
        }, {
          headerName: '检测报告编号',
          field: 'testReportCode',
          width: 300,
          cellRenderer: 'file',
          cellStyle: () =>  {return {textAlign:'left'}},
          cellRendererParams: {openFileOrDownloadByFileId: this.openFileOrDownloadByFileId,deleteFile:this.deleteFileByFileId,
            fileChange:this.fileChange,postUrl:this.postUrl,uploadData:this.uploadData,headers:this.headers}
        }, {
          headerName: '证书编号',
          field: 'certificateNo',
          width: 300,
          cellRenderer: 'certificateFile',
          cellStyle: () =>  {return {textAlign:'left'}},
          cellRendererParams: {openFileOrDownloadByFileId: this.openFileOrDownloadByFileId,deleteFile:this.deleteFileByFileId,
            fileChange:this.fileChange,postUrl:this.postUrl,uploadData:this.uploadData,headers:this.headers}
        }, {
          headerName: '证书状态',
          field: 'certificateStatus',
          width: 80,
          cellRenderer: 'updateSelect',
          cellRendererParams: {updateStatus: this.updateStatus,updateSelectData:this.updateSelectData}
        }, {
          headerName: '发证日期',
          field: 'certificateDate',
          width: 120,
          
          cellRenderer: 'updateDate',
          cellRendererParams: {updateDataDate: this.updateDataDate,updateStatus:this.updateStatus}

        }, {
          headerName: '电芯型号',
          field: 'cellModel',
          width: 80,
          cellRenderer: 'updateString',
          cellRendererParams: {onUpdateData: this.updateData,updateStatus:this.updateStatus}
        }, {
          headerName: '持证人/申请人',
          field: 'holder',
          width: 160,
          cellRenderer: 'updateString',
          cellRendererParams: {onUpdateData: this.updateData,updateStatus:this.updateStatus}
        }, {
          headerName: '生产工厂',
          field: 'factory',
          width: 160,
          
          cellRenderer: 'updateString',
          cellRendererParams: {onUpdateData: this.updateData,updateStatus:this.updateStatus}
        }, {
          headerName: '发证机构',
          field: 'issuingAuthority',
          width: 160,
          
          cellRenderer: 'updateString',
          cellRendererParams: {onUpdateData: this.updateData,updateStatus:this.updateStatus}
        }, {
          headerName: '检测实验室',
          field: 'testingLaboratory',
          width: 160,
          cellRenderer: 'updateString',
          cellRendererParams: {onUpdateData: this.updateData,updateStatus:this.updateStatus}
        }, {
          headerName: '电压(V)',
          field: 'voltage',
          width: 80,
          cellRenderer: 'updateNum',
          cellRendererParams: {updateData: this.updateData,updateStatus:this.updateStatus}
          
        }, {
          headerName: '容量(Ah)',
          field: 'capacity',
          width: 80,
          
          cellRenderer: 'updateNum',
          cellRendererParams: {updateData: this.updateData,updateStatus:this.updateStatus}
        }, {
          headerName: '能量(Wh)',
          field: 'energy',
          width: 80,
          
          cellRenderer: 'updateNum',
          cellRendererParams: {updateData: this.updateData,updateStatus:this.updateStatus}
        }, {
          headerName: '有效期',
          field: 'periodValidity',
          width: 120,
          cellRenderer: 'updateDate',
          cellRendererParams: {updateDataDate: this.updateDataDate,updateStatus:this.updateStatus}

        }, {
          headerName: '有效期剩余时间',
          field: 'remainingTime',
          width: 110,
        }, {
          headerName: '费用(元)',
          field: 'fee',
          width: 80,
          
          cellRenderer: 'updateNum',
          cellRendererParams: {updateData: this.updateData,updateStatus:this.updateStatus}
        }, {
          headerName: '认证类型',
          field: 'authenticationType',
          width: 80,
          
          cellRenderer: 'updateString',
          cellRendererParams: {onUpdateData: this.updateData,updateStatus:this.updateStatus}
        }, {
          headerName: '适用市场',
          field: 'applicableMarket',
          width: 80,
          
          cellRenderer: 'updateString',
          cellRendererParams: {onUpdateData: this.updateData,updateStatus:this.updateStatus}
        }, {
          headerName: '产品分类',
          field: 'classification',
          width: 80,
          
          cellRenderer: 'updateString',
          cellRendererParams: {onUpdateData: this.updateData,updateStatus:this.updateStatus}
        }, {
          headerName: '申请单号',
          field: 'folderNo',
          width: 100,
          
          cellRenderer: 'updateString',
          cellRendererParams: {onUpdateData: this.updateData,updateStatus:this.updateStatus}
        }, {
          headerName: '备注',
          field: 'remark',
          width: 60,
          cellRenderer: 'updateString',
          cellRendererParams: {onUpdateData: this.updateData,updateStatus:this.updateStatus}
        }, {
          headerName: '操作',
          field: 'action',
          width: 60,
          cellRenderer: 'delete',
          cellRendererParams: {deleteOne: this.deleteOne}

        }
      ]
      this.gridApi.refreshCells({
        force: true
      })
    },
    onGridReady(params) {
      this.gridApi = params.api;
      this.columnApi = params.columnApi;
      // params.api.sizeColumnsToFit();
    },
    loadData() {
      this.loading = true
      return testProjectOutTestDataPageList({
        ...{
          pageNo: this.pageNo,
          pageSize: this.pageSize
        }, ...this.queryParam
      }).then((res) => {
        if (res.success) {
          this.rowData = res.data.rows
          this.tableTotal = res.data.totalRows
          this.updateSwitch(true)
        }
      }).finally(() => {
        if(this.rowData.length == 0 && this.pageNo > 1){
          // this.pageNo -= 1
          this.pageNo = Math.max(1, Math.ceil(this.tableTotal / this.pageSize))
          this.loadData()
        }
        this.loading = false
      })

    },
    handlePageChange(value) {
      let {current, pageSize} = value
      this.pageNo = current
      this.pageSize = pageSize
      this.loadData()

    },
    refresh() {
      this.loadData()
    },
    reset(){
      this.queryParam = {}
      this.loadData()
    },
    add() {
      testProjectOutTestDataAdd().then(res => {
        this.refresh()
      })
    },
    colorUpdate() {
      setTimeout(function () {
        var childElements = document.querySelectorAll('.yellow');
        childElements.forEach(function (childElement) {
          var parentElement = childElement.parentElement;
          parentElement.style.backgroundColor = '#fec303';
        });
        var childElements2 = document.querySelectorAll('.red');
        childElements2.forEach(function (childElement) {
          var parentElement = childElement.parentElement;
          parentElement.style.backgroundColor = '#e05328';
        });
        var childElements3 = document.querySelectorAll('.green');
        childElements3.forEach(function (childElement) {
          var parentElement = childElement.parentElement;
          parentElement.style.backgroundColor = '#66b72a';
        });
      }, 500)
    },
    fileChange($event, record, fileIdColumn, fileNameColumn) {
      //上传完成
      if ($event.file.status == 'done') {
        //文件id
        let fileId = $event.file.response.data
        //文件名称
        let fileName = $event.file.name

        //更新文件
        let param = {}
        param.id = record.id
        param[fileIdColumn] = fileId
        param[fileNameColumn] = fileName
        if (fileIdColumn == 'fileId') {
          param.fileStatus = 0
        } else {
          param.certificateFileStatus = 0
        }
        testProjectOutTestDataUpdate(param).then(res => {
          if (res.success) {
            this.loadData()
          }
        })

      }

    },

    deleteFile(record, column) {
      let param = {id: record.id}
      param[column] = 1
      testProjectOutTestDataUpdate(param).then(res => {
        if (res.success) {
          this.loadData()
        }
      })
    },
    deleteFileByFileId(record, column,fileId,deleteColumn) {
      let param = {id: record.id}
      param[column] = 1
      param[deleteColumn] = fileId
      testProjectOutTestDataUpdate(param).then(res => {
        if (res.success) {
          this.loadData()
        }
      })
    },
    updateData($event, record, columns) {
      let param = {}
      param.id = record.id
      record[columns.key] = $event.target.value
      param[columns.column.colId] = $event.target.value
      testProjectOutTestDataUpdate(param).then(res => {
        if (res.success) {

        }
      })
    },
    updateSelectData($event, record, columns) {
      let param = {}
      param.id = record.id
      record[columns.column.colId] = $event
      param[columns.column.colId] = $event
      testProjectOutTestDataUpdate(param).then(res => {
        if (res.success) {

        }
      })
    },
    deleteOne(record){
      testProjectOutTestDataUpdate({id:record.id,handDeleteStatus: 1}).then(res => {
        this.refresh()
      })
    },
    updateDataDate(value, record, columns) {
      let param = {}
      param.id = record.id
      record[columns.column.colId] = value
      param[columns.column.colId] = value
      testProjectOutTestDataUpdate(param).then(res => {
        if (res.success) {

        }
      })
    },
    //直接调用lims接口预览或下载
    async openFileOrDownload(record, fileIdColumn, fileNameColumn) {
      //pbi上传的文件
      if (record[fileIdColumn]) {
        this.$refs.pbiPreviewNew.init(record[fileIdColumn])
      } else if (fileIdColumn == 'fileId' && record.fileUrl /*&& (record.fileUrl.indexOf(".pdf") > -1 || record.fileUrl.indexOf(".PDF") > -1)*/) {
        this.$refs.pbiPreviewNew.init(null,record.fileName,record.fileUrl)
      }
      this.updateSwitch(true)
    },//直接调用lims接口预览或下载
    async openFileOrDownloadByFileId(fileId) {

      this.$refs.pbiPreviewNew.init(fileId)

      this.updateSwitch(true)
    }
  }
}
</script>
<style scoped lang="less">
@import '/src/components/pageTool/style/pbiSearchItem.less';
.rollback-icon {
  margin-right: 4px;
}

/* 标题 */
.head-title {
  display: flex;
  align-items: center;
}

.head-title .line {
  width: 4px;
  height: 22px;
  background: #3293ff;
  border-radius: 20px;
}

.head-title .title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

/* 筛选 */
.filter-wrapper {
  display: flex;
  justify-content: space-between;
}

.filter-right {
  display: flex;
}

.table-wrapper {
  //padding: 10px;
  background: #fff;
  border-radius: 10px;
}

/deep/ .ant-table-middle > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-tbody > tr > td {
  padding: 4px;
}

/deep/ .ant-table-middle > .ant-table-content > .ant-table-fixed-left > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-tbody > tr > td {
  padding: 4px;
}

.table-content {
  width: 100%;
  height: 100%;
}

.serial-number-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.serial-number-content .plus-icon {
  display: none;
  margin-right: 5px;
  cursor: pointer;
}

.input-search-block {
  display: flex;
}


.choose-btn {
  font-size: 12px;
}

.mt10 {
  margin-top: 10px;
}

/deep/ .ant-btn {
  font-size: 12px;
}

/deep/ .ant-input {
  font-size: 12px;
}

/deep/ .ant-select {
  font-size: 12px;
}

/deep/ .ant-breadcrumb {
  font-size: 12px;
}


/deep/ .ant-table-thead > tr > th,
/deep/ .ant-table-tbody > tr > td {
  padding: 4px;
}

/deep/ .table-wrapper .ant-input-number-input {
  text-align: center !important;
}

/deep/ .testProjectName-column-style {
  min-width: 80px;
}

.mr8 {
  margin-right: 8px;
}

.input-short {
  width: 120px;
}

.label {
  width: 100px;
  text-align: right;
  font-size: 12px;
  color: #333;
}

.operate-block {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

/deep/ .ant-upload {
  font-size: unset;
}

.yellow {
  background: #fec303;
  display: block;
}

.red {
  background: #e05328;
  display: block;
}

.green {
  background: #66b72a;
  display: block;
}

</style>
