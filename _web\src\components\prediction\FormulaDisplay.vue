<template>
  <div class="formula-section">
    <div class="form-section" style="width: 100%;">
      <h5>LaTeX公式</h5>
      <div class="formula-preview formula-container">
        <!-- 加载指示器 - 无文字 -->
        <div class="formula-loading"></div>

        <!-- 主公式 -->
        <div class="formula-section-title">主公式</div>
        <div class="formula-preview-content main-formula">
          <div ref="mainFormulaPreview"></div>
        </div>

        <!-- 子公式 - 每个子公式单独展示 -->
        <template v-if="hasSubFormulas">
          <div class="formula-section-title">子公式</div>
          <template v-for="(_, index) in subFormulas">
            <div :key="index" class="formula-preview-content sub-formula">
              <div :ref="`subFormulaPreview_${index}`"></div>
            </div>
          </template>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { renderLatex, renderMathJax } from '@/utils/mathUtils';
import predictionMixin from '@/mixins/predictionMixin';

export default {
  name: 'FormulaDisplay',
  mixins: [predictionMixin],
  props: {
    mainFormula: {
      type: String,
      default: ''
    },
    subFormulas: {
      type: Array,
      default: () => []
    }
  },
  data: () => ({
    lastRenderedFormula: null
  }),
  computed: {
    hasSubFormulas() {
      return this.subFormulas && this.subFormulas.length > 0;
    }
  },
  methods: {
    // 更新公式预览
    updateFormulaPreview() {
      // 检查公式是否发生变化，避免不必要的渲染
      const currentFormula = this.mainFormula + (this.hasSubFormulas ? this.subFormulas.join('') : '');
      if (this.lastRenderedFormula === currentFormula) return;

      // 更新最后渲染的公式
      this.lastRenderedFormula = currentFormula;

      // 渲染主公式
      if (this.mainFormula && this.$refs.mainFormulaPreview) {
        const mainFormulaLatex = renderLatex(this.mainFormula);
        this.$refs.mainFormulaPreview.innerHTML = mainFormulaLatex;
      }

      // 如果存在子公式，单独渲染每个子公式
      if (this.hasSubFormulas && this.subFormulas.length > 0) {
        this.subFormulas.forEach((subFormula, index) => {
          const refName = `subFormulaPreview_${index}`;
          if (this.$refs[refName] && this.$refs[refName][0]) {
            const subFormulaLatex = renderLatex(subFormula);
            this.$refs[refName][0].innerHTML = subFormulaLatex;
          }
        });
      }

      // 触发MathJax渲染，使用immediate=true确保立即渲染一次
      renderMathJax(true);
    }
  },
  watch: {
    mainFormula() {
      this.$nextTick(this.updateFormulaPreview);
    },
    subFormulas: {
      handler() {
        this.$nextTick(this.updateFormulaPreview);
      },
      deep: true
    }
  },
  mounted() {
    this.$nextTick(this.updateFormulaPreview);
  }
};
</script>

<style scoped>
.formula-section {
  margin-bottom: 24px;
  width: 100%;
}

.form-section {
  padding: 0;
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  height: 100%;
}

.form-section > div:not(h5) {
  padding: 16px;
}

h5 {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 0;
  color: #333;
  padding: 10px 0;
  background-color: #f5f5f5;
  border-bottom: 1px solid #f0f0f0;
  text-align: center;
  border-radius: 8px 8px 0 0;
}

/* 公式预览样式 */
.formula-preview {
  background-color: #fff;
  padding: 0;
  margin-bottom: 0;
  border: none;
  border-radius: 0;
  overflow-x: auto;
  display: flex;
  flex-direction: column;
}

.formula-section-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.formula-preview-content {
  min-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 16px;
  background-color: #fff;
  border-radius: 4px;
  min-width: 100%;
  margin-bottom: 8px;
}

.formula-preview-content.main-formula {
  border-bottom: 1px dashed #f0f0f0;
}

.formula-preview-content.sub-formula {
  border-top: 1px dashed #f0f0f0;
  background-color: #fafafa;
}
</style>
