/**
 * 公式相关的Mixin
 * 提供公式处理的公共方法，减少组件中的重复代码
 */
import { renderLatex, renderMathJax } from '@/utils/mathUtils';
import { hasEquals, isFormulaNotEmpty, hasValidParams } from '@/utils/validationUtils';
import { handleFormulaError, showWarning } from '@/utils/errorUtils';
import { api } from '@/api';

export default {
  data() {
    return {
      // 防抖定时器
      _latexDebounce: null,
      // 最后渲染的公式
      lastRenderedFormula: '',
    };
  },
  methods: {
    /**
     * 渲染LaTeX公式
     * @param {string} formula - 公式字符串
     * @returns {string} 渲染后的HTML
     */
    renderLatex(formula) {
      return renderLatex(formula);
    },

    /**
     * 触发MathJax渲染
     * @param {boolean} immediate - 是否立即渲染
     * @param {string} selector - 可选的CSS选择器，用于限制只渲染特定区域的LaTeX内容
     */
    renderMathJax(immediate = false, selector = null) {
      renderMathJax(immediate, selector);
    },

    /**
     * 防抖更新公式预览
     * @param {string} mainFormula - 主公式
     * @param {Array} subFormulas - 子公式数组
     * @param {Function} updateCallback - 更新回调函数
     */
    debouncedUpdateFormulaPreview(mainFormula, subFormulas = [], updateCallback) {
      // 清除之前的定时器
      if (this._latexDebounce) {
        clearTimeout(this._latexDebounce);
      }

      // 构建完整公式
      const subFormulasText = Array.isArray(subFormulas) && subFormulas.length > 0
        ? subFormulas.join('')
        : '';
      const currentFormula = mainFormula + subFormulasText;

      // 检查公式是否发生变化，避免不必要的渲染
      if (this.lastRenderedFormula === currentFormula) return;

      // 更新最后渲染的公式
      this.lastRenderedFormula = currentFormula;

      // 使用防抖延迟执行渲染
      this._latexDebounce = setTimeout(() => {
        if (typeof updateCallback === 'function') {
          updateCallback();
        }
        this.renderMathJax(true);
      }, 500);
    },

    /**
     * 解析LaTeX公式
     * @param {string} mainFormula - 主公式
     * @param {Array} subFormulas - 子公式数组
     * @param {Array} independentParams - 自变量数组
     * @returns {Promise} 解析结果Promise
     */
    async parseLatexFormula(mainFormula, subFormulas = [], independentParams = []) {
      try {
        // 验证公式
        if (!isFormulaNotEmpty(mainFormula)) {
          return { success: false, message: '请输入主公式' };
        }

        // 确保主公式包含等号
        let mainFormulaWithEquals = mainFormula.trim();
        if (!hasEquals(mainFormulaWithEquals)) {
          mainFormulaWithEquals = `Q = ${mainFormulaWithEquals}`;
          showWarning('已为公式自动添加左侧等号部分：Q = ...');
        }

        // 验证参数
        if (!hasValidParams(independentParams)) {
          return { success: false, message: '请至少添加一个自变量' };
        }

        // 过滤掉空的子公式
        const filteredSubFormulas = Array.isArray(subFormulas)
          ? subFormulas.filter(formula => formula && formula.trim() !== '')
          : [];

        // 检查子公式是否都包含等号
        const invalidSubFormulas = filteredSubFormulas.filter(formula => !hasEquals(formula));
        if (invalidSubFormulas.length > 0) {
          showWarning('子公式必须包含等号，请检查');
          return { success: false, message: '子公式必须包含等号' };
        }

        // 构建LaTeX公式对象
        const latexFormula = {
          main_formula: mainFormulaWithEquals,
          sub_formulas: filteredSubFormulas
        };

        // 过滤有效的独立参数
        const filteredParams = independentParams.filter(param => param && param.trim() !== '');

        // 调用API解析公式
        const response = await api.formula.parseLatexFormula(latexFormula, filteredParams);

        if (response.data.success) {
          return {
            success: true,
            parsedLatex: {
              main_formula: mainFormulaWithEquals,
              sub_formulas: filteredSubFormulas
            },
            parsedParams: response.data.params || []
          };
        } else {
          showWarning(response.data.message || '公式解析失败');
          return { success: false, message: response.data.message };
        }
      } catch (error) {
        handleFormulaError(error, mainFormula);
        return { success: false, message: error.message || '公式解析出错' };
      }
    },

    /**
     * 格式化系数名称为LaTeX格式
     * @param {string} name - 系数名称
     * @returns {string} 格式化后的名称
     */
    formatCoefficientName(name) {
      if (!name) return '';

      // 如果已经是LaTeX格式，直接返回
      if (name.includes('_{') || name.includes('^{')) {
        return name;
      }

      // 处理下标，例如 A_1 -> A_{1}
      return name.replace(/([A-Za-z])_(\d+)/g, '$1_{$2}');
    },

    /**
     * 按字母分组系数
     * @param {Array} coefficients - 系数数组
     * @returns {Array} 分组后的系数数组
     */
    groupCoefficientsByLetter(coefficients) {
      if (!coefficients || !Array.isArray(coefficients)) {
        return [];
      }

      const groups = {};

      coefficients.forEach(coefficient => {
        // 获取系数名称的第一个字母
        let name = coefficient.name;
        // 处理可能的下标或花括号
        if (name.includes('_') || name.includes('{')) {
          name = name.split(/[_{]/)[0];
        }
        const firstLetter = name.charAt(0).toUpperCase();

        if (!groups[firstLetter]) {
          groups[firstLetter] = {
            letter: firstLetter,
            coefficients: [],
            description: coefficient.groupDescription || ''
          };
        }

        groups[firstLetter].coefficients.push(coefficient);
      });

      // 将组按字母顺序排序
      return Object.values(groups).sort((a, b) => a.letter.localeCompare(b.letter));
    }
  }
};
