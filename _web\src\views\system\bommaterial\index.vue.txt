<template>
    <div>
        <a-button @click="addData" type="primary" style="margin:8px 2px">添加数据</a-button>
        <a-button @click="delData" type="primary" style="margin:8px 2px">删除数据</a-button>
        <a-button @click="addColumns" type="primary" style="margin:8px 2px">增加列</a-button>
        <a-button @click="delColumns" type="primary" style="margin:8px 2px">删除列</a-button>
        <div class="head">{{title}}</div>
        <ve-table :scroll-width="1300" border-y :show-header="false" :columns="bomColumns" :table-data="bomTableData" :cell-span-option="cellSpanOption"/>
        <ve-table :scroll-width="1300" style="border:none" border-y :columns="columns" :table-data="tableData" />
    </div>
</template>

<script>
const materialSelect = {
        name: "materialSelect",
        template: `
            <a-select style='width:110px;font-size:12px' placeholder='选择'
										name="column.key"
										:default-value="row[column.key]"
										v-model="row[column.key]"
                                        @change="change"
									>
										<a-select-option v-for="(value,key) in cats" :key='key'>{{value}}</a-select-option>
									</a-select>
        `,
        props: {
            cats: Object,
            row:Object,
            column:Object,
            callSaveBomMaterial:Function
        },
        methods:{
            change(val){
                this.row[this.column.key] = val
                this.callSaveBomMaterial()
            }
        }
    }
    import {
        getBomMaterial,
        saveBomMaterial
    } from "@/api/modular/system/bomMaterialManage"
    export default {
        data() {
            return {
                type:{},
                title: '',
                issueId: 0,
                loading: false,
                vo: {},
                cellSpanOption: {
                    bodyCellSpan: this.bodyCellSpan,
                },
                bomColumns:[
                    {
                        title:'BOM',
                        field:'bom',
                        key:'bom',
                        width:200
                    },
                    {
                        title:'BOM功能',
                        field:'bom_type',
                        key:'bom_type',
                        width:200
                    },
                    {
                        title:'B01',
                        field:'b01',
                        key:'b01',
                        width:300,
                        renderBodyCell: this.renderBodyCell,
                    }
                ],
                bomTableData:[
                    {
                        bom:'BOM',
                        bom_type:'适用产品状态',
                    },
                    {
                        bom:'BOM',
                        bom_type:'适用工厂',
                    },
                    {
                        bom:'BOM',
                        bom_type:'工厂产能',
                    },
                ],
                columns: [
                    {
                        title:'序号',
                        field:'seq',
                        key:'seq',
                        width:40,
                        renderBodyCell: ({ row, column, rowIndex }, h) => {
                            return ++rowIndex;
                        },
                    },
                    {
                        title:'产品应用',
                        field:'apply',
                        key:'apply',
                        width:160,
                        renderBodyCell: this.renderCatCell,
                    },
                    {
                        title:'材料类别',
                        field:'material_type',
                        key:'material_type',
                        width:100,
                        renderBodyCell:this.renderSelectCell
                    },
                    {
                        title:'名称',
                        field:'material_name',
                        key:'material_name',
                        width:100,
                        renderBodyCell: this.renderCell,
                    },
                    {
                        title:'B01',
                        children:[
                            {
                                title:'物料代码',
                                field:'material_code_01',
                                key:'material_code_01',
                                width:100,
                                renderBodyCell: this.renderCell,
                            },
                            {
                                title:'型号',
                                field:'material_model_01',
                                key:'material_model_01',
                                width:100,
                                renderBodyCell: this.renderCell,
                            },
                            {
                                title:'供应商',
                                field:'supplyer_01',
                                key:'supplyer_01',
                                width:100,
                                renderBodyCell: this.renderCell,
                            }
                        ]
                    }
                ],
                tableData: [
                ]
            }
        },
        methods: {
            addData(){
                this.tableData.push({})
            },
            delData(){
                if (this.tableData.length < 1) {
                    return
                }
                this.tableData.splice(this.tableData.length-1,1)
            },
            
            bodyCellSpan({ row, column, rowIndex }) {
                if (column.field === "bom") {
                    if (rowIndex === 0) {
                        return {
                            rowspan: 3,
                            colspan: 1,
                        };
                    }
                    else{
                        return {
                            rowspan: 0,
                            colspan: 0,
                        };
                    }
                }
            },
            renderCatCell({row,column,rowIndex}){
                return <materialSelect callSaveBomMaterial={this.callSaveBomMaterial} row={row} column={column} cats={this.type}></materialSelect>
            },
            renderSelectCell({row,column,rowIndex}){
                return (<a-select style='width:100%' placeholder='选择'
										name="column.key"
										default-value="{row[column.key]}"
										v-model={row[column.key]}
										on-change={(value) => {
											row[column.key] = value
                                            this.callSaveBomMaterial()
										}}
									>
										<a-select-option value="1">化学材料</a-select-option>
                                        <a-select-option value="2">结构件</a-select-option>
                                        <a-select-option value="3">包装材料</a-select-option>
									</a-select>)
            },
            renderCell({ row, column, rowIndex }){
                return <a-Input  style="width:100%;outline:none;border:1px solid #eee" on-change={(e) => {
											const { value } = e.target
											row[column.field] = value
                                            this.callSaveBomMaterial()
										}} v-model={row[column.field]}></a-Input>;
            },
            renderBodyCell({ row, column, rowIndex }) {
                if (rowIndex == 0) {

                    return (<a-select style='width:100%' placeholder='是否量产'
										name="column.key"
										default-value="{row[column.key]}"
										v-model={row[column.key]}
										on-change={(value) => {
											row[column.key] = value
                                            this.callSaveBomMaterial()
										}}
									>
										<a-select-option value="0">非量产</a-select-option>
                                        <a-select-option value="1">量产</a-select-option>
									</a-select>)
                }
                const placeholder =  rowIndex == 1 ? '填写适用工厂':'填写产能'
                return <a-Input placeholder={placeholder} style="width:100%;outline:none;border:1px solid #eee" on-change={(e) => {
											const { value } = e.target
											row[column.field] = value
                                            this.callSaveBomMaterial()
										}} v-model={row[column.field]}></a-Input>;
            },
            delColumns() {
                if (this.bomColumns.length < 4) {
                    return;
                }
                this.bomColumns.splice(this.bomColumns.length - 1, 1)

                if (this.columns.length < 6) {
                    return;
                }

                this.columns.splice(this.columns.length - 1, 1)
            },
            addColumns() {
                let next_id = this.columns.length - 3
                var id = next_id < 10 ? '0' + next_id : '' + next_id
                if (this.columns.length > 6+5) {
                    return
                }
                this.columns.push({
                    title:'B'+id,
                    children:[
                        {
                            title:'物料代码',
                            field:'material_code'+id,
                            key:'material_code'+id,
                            width:100,
                            renderBodyCell: this.renderCell,
                        },
                        {
                            title:'型号',
                            field:'material_model'+id,
                            key:'material_model'+id,
                            width:100,
                            renderBodyCell: this.renderCell,
                        },
                        {
                            title:'供应商',
                            field:'supplyer'+id,
                            key:'supplyer'+id,
                            width:100,
                            renderBodyCell: this.renderCell,
                        }
                    ]
                })

                next_id = this.bomColumns.length - 1
                id = next_id < 10 ? '0' + next_id : '' + next_id

                if (this.bomColumns.length > 4+5) {
                    return;
                }
                this.bomColumns.push({
                    title:'B'+id,
                    field:'b'+id,
                    key:'b'+id,
                    width:300,
                    renderBodyCell: this.renderBodyCell,
                })
            },
            getBomMaterial() {
               this.loading = true
               getBomMaterial({
                   issueId: this.$route.query.issueId
                }).then((res) => {
                   if (res.success) {
                        let type = {}
                        for (const item of res.data.productCateOptionBeans) {
                            type[item.id] = item.value
                        }
                        this.type = type
                        let columnObj = res.data.materialColumns ? JSON.parse(res.data.materialColumns) : null
                        if (columnObj) {
                            for (const item of columnObj.bomColumns) {
                                item.renderBodyCell = this.renderBodyCell
                            }
                            this.bomColumns.push(...columnObj.bomColumns)
                        }
                        if (columnObj) {
                            for (const item of columnObj.columns) {
                                if (item.children) {
                                    for (const _item of item.children) {
                                        _item.renderBodyCell = this.renderCell
                                    }
                                }
                            }
                            this.columns.push(...columnObj.columns)
                        }
                        let rowsObj = res.data.materialRows ? JSON.parse(res.data.materialRows): null
                        this.bomTableData = rowsObj ?  rowsObj.bomTableData : this.bomTableData
                        this.tableData = rowsObj ? rowsObj.tableData: []
                        this.vo = res.data
                   } else {
                       this.$message.error(res.message)
                   }
                   this.loading = false
                }).catch((err) => {
                   this.$message.error('错误：' + err.message)
                   this.loading = false
                })
           },
            callSaveBomMaterial(){
                this.loading = true
                this.vo.issueId = this.issueId
                if (this.bomColumns.length > 3) {
                    this.vo.materialColumns = JSON.stringify({columns: this.columns.slice(5), bomColumns:this.bomColumns.slice(3)})
                }else{
                    this.vo.materialColumns = null
                }
                this.vo.materialRows = JSON.stringify({bomTableData:this.bomTableData,tableData:this.tableData})
                saveBomMaterial(this.vo).then((res) => {
                    if (res.success) {
                        this.vo.id = res.data
                        this.$message.success('已保存')
                    } else {
                        this.$message.error(res.message, 1);
                    }
                    this.loading = false
                }).catch((err) => {
                    this.loading = false
                    this.$message.error('错误提示：' + err.message, 1)
                });
            }
        },
        created(){
            this.issueId = this.$route.query.issueId
            this.title = this.$route.query.name + '产品材料管理'
            this.getBomMaterial()
        }
    }
</script>

<style lang="less">
@import '../report/vetable.less';
.head {
  border: 1px solid #97b1e7;
  border-bottom: none;
  background: #eaf0fa;
  text-align: center;
  padding: 10px 0;
  color: #000;
  font-size: 20px;
}
</style>