<!-- 设计开发页面 -->
<template>
	<div style="background: white">
		<a-breadcrumb separator=">" style="padding-left: 15px">
			<a-breadcrumb-item>
				<router-link :to="{ path: '/batterydesign', query: { type: breadcrumb.type } }">
					<a-icon type="rollback" class="rollback-icon" />
          {{ breadcrumb.type === 'product' ? '项目产品' : '技术平台' }}设计
				</router-link>
			</a-breadcrumb-item>

			<a-breadcrumb-item>
				{{ breadcrumb.batteryName }}
			</a-breadcrumb-item>
		</a-breadcrumb>

		<div class="tab-title">
			<div class="tab-head">
				<div class="active">设计开发</div>
			</div>
		</div>

		<!-- <div style="font-size: x-large;font-weight: bold;padding-left: 20px;color: #1890FF;">设计开发</div>-->
		<a-tabs
			v-model="designType"
			@change="() => changePage(designType == 'design' ? changePageCallback : changeExportPageCallback)"
		>
			<a-tab-pane key="design" tab="研发设计记录">
				<div style="float: left;position: relative;z-index: 1;padding-bottom: 5px">
					<a-button type="primary" style="margin-left: 8px;width: 80px" @click="gotoIndex">返回</a-button>
				</div>
				<div style="float: right;position: relative;z-index: 1;padding-bottom: 5px">
					<a-button type="primary" style="margin-left: 8px;width: 80px" @click="addDesign">新增</a-button>
					<a-button type="primary" style="margin-left: 8px;width: 80px" @click="copyDesign">复制 </a-button>
					<a-popconfirm
						title="确定删除吗?"
						ok-text="确定"
						cancel-text="取消"
						@confirm="deleteDesign"
						placement="topRight"
						:visible="deleteDesignVisible"
						@cancel="() => (deleteDesignVisible = false)"
					>
						<a-button type="primary" style="margin-left: 8px;width: 80px" @click="deleteDesignBefore">删除</a-button>
					</a-popconfirm>
				</div>
				<a-table
					:columns="designColumns"
					:data-source="designData"
					:rowKey="record => record.id"
					:row-selection="designRowSelection"
					:pagination="{ pageSize: 27 }"
					@change="changePage(changePageCallback)"
					bordered
				>
					<span slot="name" slot-scope="text, record">
						<a @click="gotoManager(record)">{{ text }}</a>
					</span>

					<span slot="submit" slot-scope="text, record">
						<a>审批提交</a>
					</span>
				</a-table>
			</a-tab-pane>
			<a-tab-pane key="export" tab="冻结方案记录">
				<div style="float: left;position: relative;z-index: 1;padding-bottom: 5px">
					<a-button type="primary" style="margin-left: 8px;width: 80px" @click="gotoIndex">返回</a-button>
				</div>
				<div style="float: right;position: relative;z-index: 1;padding-bottom: 5px">
					<a-button type="primary" style="margin-left: 8px;width: 80px" @click="openExportDesign">新增</a-button>
					<a-button type="primary" style="margin-left: 8px;width: 80px" @click="copyExportDesign">复制</a-button>
					<a-popconfirm
						title="确定删除吗?"
						ok-text="确定"
						cancel-text="取消"
						@confirm="deleteExportDesign"
						placement="topRight"
						:visible="deleteExportDesignVisible"
						@cancel="() => (deleteExportDesignVisible = false)"
					>
						<a-button type="primary" style="margin-left: 8px;width: 80px" @click="deleteExportDesignBefore"
							>删除</a-button
						>
					</a-popconfirm>
				</div>

				<a-modal
					title="新增"
					:visible="designAddVisible"
					width="60%"
					@ok="addExportDesign"
					@cancel="() => (designAddVisible = false)"
				>
					<a-table
						:columns="designExportAddColumns"
						:data-source="onlyDesignList"
						:rowKey="record => record.id"
						:row-selection="onlyDesignListRowSelection"
						:pagination="false"
						:rowClassName="() => 'unSet'"
						bordered
					>
					</a-table>
				</a-modal>

				<a-table
					:columns="designExportColumns"
					:data-source="designExportData"
					:rowKey="record => record.id"
					:row-selection="designExportRowSelection"
					:pagination="{ pageSize: 27 }"
					@change="changePage(changeExportPageCallback)"
					bordered
				>
					<span slot="name" slot-scope="text, record">
						<a @click="gotoManager(record)">{{ text }}</a>
					</span>

					<span slot="submit" slot-scope="text, record">
						<a>审批提交</a>
					</span>
				</a-table>
			</a-tab-pane>
		</a-tabs>
		<check-record ref="checkRecord"></check-record>
	</div>
</template>
<script>
import { ALL_APPS_MENU } from "@/store/mutation-types"
import Vue from "vue"
import { STable, XCard } from "@/components"
import {
	getBatteryDesignPage,
	sysBatteryDesignEdit,
	sysBatteryDesignCopy,
	getBatteryDesignCheckPage,
	sysBatteryDesignSubmit,
	batteryDesignRelationOnlyDesignList,
	copyDesign,
	getAuth,
	batteryDesignRelationUpdate,
	batteryDesignRelationUpdateByInBatteryId,
	addSor,
  getBom,
	addDesign,
	batteryDesignRelationDesignList,
	batteryDesignRelationSorList,
	batteryDesignRelationAddExportDesign,
	getBatteryDesign
} from "@/api/modular/system/batterydesignManage"
import {
	checkRecordGetListByBusinessId,
	checkRecordSubmitCheck
} from "@/api/modular/system/batterydesignCheckRecordManage"

import checkRecord from "./checkRecord"
import { mapActions, mapGetters } from "vuex"
import editForm from "./editForm"
import addForm from "./addForm"
import addFormPlatform from "./addFormPlatform"
import moment from "moment"

export default {
	components: {
		XCard,
		STable,
		editForm,
		addForm,
		addFormPlatform,
		moment,
		checkRecord
	},
	data() {
		return {
      breadcrumb:{}, // 面包屑
			design: {},
			structureType1: "",
			sorData: [],
			designType: "design",
			sorVisible: false,
			designAddVisible: false,
			designData: [],
			designExportData: [],
			onlyDesignExportData: [],
			designVisible: false,
			structureType: null,
			selectedRowKeys: [],
			selectedRow: [],
			deleteExportDesignVisible: false,
			rowSelection: {
				columnWidth: 30,
				onChange: (selectedRowKeys, selectedRows) => {
					this.selectedRowKeys = selectedRowKeys
					this.selectedRow = selectedRows
				}
			},

			sorSelectedRowKeys: [],
			sorSelectedRow: [],

			sorRowSelection: {
				columnWidth: 40,
				onChange: (selectedRowKeys, selectedRows) => {
					this.sorSelectedRowKeys = selectedRowKeys
					this.sorSelectedRow = selectedRows
				}
			},
			designSelectedRowKeys: [],
			designSelectedRow: [],

			designRowSelection: {
				columnWidth: 40,
				onChange: (selectedRowKeys, selectedRows) => {
					this.designSelectedRowKeys = selectedRowKeys
					this.designSelectedRow = selectedRows
				}
			},

			designExportSelectedRowKeys: [],
			designExportSelectedRow: [],

			designExportRowSelection: {
				columnWidth: 40,
				onChange: (selectedRowKeys, selectedRows) => {
					this.designExportSelectedRowKeys = selectedRowKeys
					this.designExportSelectedRow = selectedRows
				}
			},
			onlyDesignListRowKeys: [],
			onlyDesignListRow: [],
			onlyDesignListRowSelection: {
				columnWidth: 40,
				onChange: (selectedRowKeys, selectedRows) => {
					this.onlyDesignListRowKeys = selectedRowKeys
					this.onlyDesignListRow = selectedRows
				}
			},
			queryParam: {},
			checkNum: "",
			type: "product",
			auth: [],
			showQuery: false,
			record: {},
			showCheck: false,
			deleteDesignVisible: false,
			onlyDesignData: [],
			onlyDesignList: [],
			width: document.documentElement.clientWidth,
			// 表头
			columns: [
				{
					title: "序号",
					dataIndex: "index",
					align: "center",
					width: 50,
					ellipsis: true,
					customRender: (text, record, index) => `${index + 1}`
				},
				{
					title: "电池结构",
					width: 70,
					dataIndex: "structureType",
					align: "center",
					customRender: (text, record, index) => {
						if (text == "g_cylinder") {
							return "G圆柱"
						}
						if (text == "c_cylinder") {
							return "C圆柱"
						}
						if (text == "v_cylinder") {
							return "V圆柱"
						}
						if (text == "winding") {
							return "方形卷绕"
						}

						if (text == "lamination") {
							return "方形叠片"
						}
						if (text == "soft_roll") {
							return "软包"
						}
					}
				},

				{
					title: "应用场景",
					width: 70,
					dataIndex: "scenario",
					align: "center"
				},
				{
					title: "产品名称",
					width: 70,
					dataIndex: "productName",
					align: "center"
				},
				{
					title: "项目等级",
					width: 70,
					dataIndex: "grade",
					align: "center"
				},
				{
					title: "项目名称",
					width: 70,
					dataIndex: "batteryName",
					align: "center"
				},
				{
					title: "客户",
					width: 70,
					dataIndex: "customer",
					align: "center"
				},
				{
					width: 70,
					dataIndex: "rpm",
					align: "center",
					slots: { title: "rpmTitle" }
				},
				{
					width: 70,
					dataIndex: "pd",
					align: "center",
					slots: { title: "pdTitle" }
				},
				{
					title: "项目状态",
					width: 70,
					dataIndex: "projectStatus",
					align: "center"
				},
				{
					title: "SOR管理",
					width: 70,
					dataIndex: "sor",
					align: "center",
					scopedSlots: {
						customRender: "sor"
					}
				},
				{
					width: 70,
					dataIndex: "startDate",
					align: "center",
					slots: { title: "startDateTitle" }
				},
				{
					title: "设计开发",
					width: 70,
					dataIndex: "design",
					align: "center",
					scopedSlots: {
						customRender: "design"
					}
				},
				{
					title: "开发阶段",
					width: 90,
					ellipsis: true,
					dataIndex: "productDevelopmentStage",
					align: "center",
					customRender: (text, record, index) => {
						if (text == "a") {
							return "A样"
						}
						if (text == "b") {
							return "B样"
						}
						if (text == "c") {
							return "C样"
						}
						if (text == "d") {
							return "D样"
						}
						if (text == "platform") {
							return "平台课题方案"
						}
					}
				}
			],
			columnsNoType: [
				{
					title: "序号",
					dataIndex: "index",
					align: "center",
					width: 50,
					ellipsis: true,
					customRender: (text, record, index) => `${index + 1}`
				},

				{
					title: "应用场景",
					width: 70,
					dataIndex: "scenario",
					align: "center"
				},
				{
					title: "产品名称",
					width: 70,
					dataIndex: "productName",
					align: "center"
				},
				{
					title: "项目等级",
					width: 70,
					dataIndex: "grade",
					align: "center"
				},
				{
					title: "项目名称",
					width: 70,
					dataIndex: "batteryName",
					align: "center"
				},
				{
					title: "客户",
					width: 70,
					dataIndex: "customer",
					align: "center"
				},
				{
					width: 70,
					dataIndex: "rpm",
					align: "center",
					slots: { title: "rpmTitle" }
				},
				{
					width: 70,
					dataIndex: "pd",
					align: "center",
					slots: { title: "pdTitle" }
				},
				{
					title: "项目状态",
					width: 70,
					dataIndex: "projectStatus",
					align: "center"
				},
				{
					title: "SOR管理",
					width: 70,
					dataIndex: "sor",
					align: "center",
					scopedSlots: {
						customRender: "sor"
					}
				},
				{
					width: 70,
					dataIndex: "startDate",
					align: "center",
					slots: { title: "startDateTitle" }
				},
				{
					title: "设计开发",
					width: 70,
					dataIndex: "design",
					align: "center",
					scopedSlots: {
						customRender: "design"
					}
				},
				{
					title: "开发阶段",
					width: 90,
					ellipsis: true,
					dataIndex: "productDevelopmentStage",
					align: "center",
					customRender: (text, record, index) => {
						if (text == "a") {
							return "A样"
						}
						if (text == "b") {
							return "B样"
						}
						if (text == "c") {
							return "C样"
						}
						if (text == "d") {
							return "D样"
						}
						if (text == "platform") {
							return "平台课题方案"
						}
					}
				}
			],
			columnsPlatForm: [
				{
					title: "序号",
					dataIndex: "index",
					align: "center",
					width: 50,
					ellipsis: true,
					customRender: (text, record, index) => `${index + 1}`
				},
				{
					title: "电池结构",
					width: 70,
					dataIndex: "structureType",
					align: "center",
					customRender: (text, record, index) => {
						if (text == "g_cylinder") {
							return "G圆柱"
						}
						if (text == "c_cylinder") {
							return "C圆柱"
						}
						if (text == "v_cylinder") {
							return "V圆柱"
						}
						if (text == "winding") {
							return "方形卷绕"
						}

						if (text == "lamination") {
							return "方形叠片"
						}
						if (text == "soft_roll") {
							return "软包"
						}
					}
				},

				{
					title: "应用场景",
					width: 70,
					dataIndex: "scenario",
					align: "center"
				},
				{
					title: "产品名称",
					width: 70,
					dataIndex: "productName",
					align: "center"
				},
				{
					title: "课题等级",
					width: 70,
					dataIndex: "grade",
					align: "center"
				},
				{
					title: "课题名称",
					width: 70,
					dataIndex: "batteryName",
					align: "center"
				},
				{
					width: 70,
					dataIndex: "pt",
					align: "center",
					slots: { title: "ptTitle" }
				},
				{
					title: "课题状态",
					width: 70,
					dataIndex: "platformStatus",
					align: "center"
				},
				{
					title: "SOR管理",
					width: 70,
					dataIndex: "sor",
					align: "center",
					scopedSlots: {
						customRender: "sor"
					}
				},
				{
					title: "立项评审",
					width: 70,
					dataIndex: "startDate",
					align: "center"
				},
				{
					title: "设计开发",
					width: 70,
					dataIndex: "design",
					align: "center",
					scopedSlots: {
						customRender: "design"
					}
				}
			],
			columnsPlatFormNoType: [
				{
					title: "序号",
					dataIndex: "index",
					align: "center",
					width: 50,
					ellipsis: true,
					customRender: (text, record, index) => `${index + 1}`
				},

				{
					title: "应用场景",
					width: 70,
					dataIndex: "scenario",
					align: "center"
				},
				{
					title: "产品名称",
					width: 70,
					dataIndex: "productName",
					align: "center"
				},
				{
					title: "课题等级",
					width: 70,
					dataIndex: "grade",
					align: "center"
				},
				{
					title: "课题名称",
					width: 70,
					dataIndex: "batteryName",
					align: "center"
				},
				{
					width: 70,
					dataIndex: "pt",
					align: "center",
					slots: { title: "ptTitle" }
				},
				{
					title: "课题状态",
					width: 70,
					dataIndex: "platformStatus",
					align: "center"
				},
				{
					title: "SOR管理",
					width: 70,
					dataIndex: "sor",
					align: "center",
					scopedSlots: {
						customRender: "sor"
					}
				},
				{
					title: "立项评审",
					width: 70,
					dataIndex: "startDate",
					align: "center"
				},
				{
					title: "设计开发",
					width: 70,
					dataIndex: "design",
					align: "center",
					scopedSlots: {
						customRender: "design"
					}
				}
			],
			designColumns: [
				{
					title: "开发阶段",
					dataIndex: "stage",
					align: "center",
					width: 70,
					customRender: (text, record, index) => {
						const obj = {
							children: (
								<a-select
									style="width:100%;height:100%;border: 0;"
                  disabled={!(record.checkStatus == 0 || record.checkStatus == 80)}
									v-model={text}
									onChange={$event => this.onDesignSelectChange(record.inBatteryId, "stage", $event)}
								>
									<a-select-option value="a">A样</a-select-option>
									<a-select-option value="b">B样</a-select-option>
									<a-select-option value="c">C样</a-select-option>
									<a-select-option value="d">D样</a-select-option>
								</a-select>
							),
							attrs: {}
						}

						if (index == 0 || index % 3 == 0) {
							obj.attrs.rowSpan = 3
						} else {
							obj.attrs.rowSpan = 0
						}

						return obj
					}
				},
				{
					title: "样品阶段",
					width: 70,
					dataIndex: "sample",
					align: "center",

					customRender: (text, record, index) => {
						const obj = {
							children: (
								<a-input
                  disabled={!(record.checkStatus == 0 || record.checkStatus == 80)}
									placeholder="例：A1"
									defaultValue={text}
									onBlur={$event => this.onDesignCellChange(record.inBatteryId, "sample", $event)}
									style="text-align: center;width:100%;height:100%;border:0"
								/>
							),
							attrs: {}
						}

						if (index == 0 || index % 3 == 0) {
							obj.attrs.rowSpan = 3
						} else {
							obj.attrs.rowSpan = 0
						}

						return obj
					}
				},
				{
					title: "方案编号",
					width: 70,
					dataIndex: "code",
					align: "center",

					customRender: (text, record, index) => {
						const obj = {
							children: (
								<a-input
                  disabled={!(record.checkStatus == 0 || record.checkStatus == 80)}
									placeholder="例：A1-01#"
									defaultValue={text}
									onBlur={$event => this.onDesignCellChange(record.inBatteryId, "code", $event)}
									style="text-align: center;width:100%;height:100%;border:0"
								/>
							),
							attrs: {}
						}

						if (index == 0 || index % 3 == 0) {
							obj.attrs.rowSpan = 3
						} else {
							obj.attrs.rowSpan = 0
						}

						return obj
					}
				},
				{
					title: "方案描述",
					width: 140,
					dataIndex: "remark",
					align: "center",
					customRender: (text, record, index) => {
						const obj = {
							children: (
								<a-input
                  disabled={!(record.checkStatus == 0 || record.checkStatus == 80)}
									defaultValue={text}
									onBlur={$event => this.onDesignCellChange(record.inBatteryId, "remark", $event)}
									style="text-align: center;width:100%;height:100%;border:0"
								/>
							),
							attrs: {}
						}

						if (index == 0 || index % 3 == 0) {
							obj.attrs.rowSpan = 3
						} else {
							obj.attrs.rowSpan = 0
						}

						return obj
					}
				},
				{
					title: "文件内容",
					width: 70,
					dataIndex: "name",
					align: "center",
					scopedSlots: {
						customRender: "name"
					}
				},
				{
					title: "试验线",
					width: 70,
					dataIndex: "factory",
					align: "center",
					customRender: (text, record, index) => {
						const obj = {
							children: (
								<a-input
                  disabled={!(record.checkStatus == 0 || record.checkStatus == 80)}
									defaultValue={text}
									onBlur={$event => this.onDesignCellChange(record.inBatteryId, "factory", $event)}
									style="text-align: center;width:100%;height:100%;border:0"
								/>
							),
							attrs: {}
						}

						if (index == 0 || index % 3 == 0) {
							obj.attrs.rowSpan = 3
						} else {
							obj.attrs.rowSpan = 0
						}

						return obj
					}
				},
				{
					title: "审批状态",
					width: 70,
					dataIndex: "checkStatus",
					align: "center",
					customRender: (text, record, index) => {
						/**
               * (<a-select style="width:100%;height:100%;border: 0;" v-model={text}
               onChange={($event) => this.onDesignSelectChange(record.inBatteryId, 'stage',$event)} >
               <a-select-option value="a">A样</a-select-option>
               <a-select-option value="b">B样</a-select-option>
               <a-select-option value="c">C样</a-select-option>
               <a-select-option value="d">D样</a-select-option>
               </a-select>)
               * @type {{children: boolean, style: *}}
               */

						const obj = {
							children: (
								<a onClick={() => this.openCheckRecord(record.id, "design")} style="text-align: center">
									{this.checkStatus.find(e => e.key == text).value}
								</a>
							),
							attrs: {}
						}

						if (index == 0 || index % 3 == 0) {
							obj.attrs.rowSpan = 3
						} else {
							obj.attrs.rowSpan = 0
						}
						return obj
					}
				},
        {
          title: "编辑人",
          width: 70,
          dataIndex: "updateName",
          align: "center"
        },
				{
					title: "更新时间",
					width: 70,
					dataIndex: "updateTime",
					align: "center",
					customRender: (text, record, index) => (null == text ? "" : moment(new Date(text)).format("YYYY-MM-DD"))
				},
				/*{
            title: '审核人',
            width: 70,
            dataIndex: 'checkMan',
            align: 'center',

          }, {
            title: '批准人',
            width: 70,
            dataIndex: 'approveMan',
            align: 'center',

          }, */ {
					title: "操作",
					width: 70,
					dataIndex: "submit",
					align: "center",
					customRender: (text, record, index) => {

						const obj = {
							children:
                (record.checkStatus == 0 || record.checkStatus == 80) && (record.stage == 'a' || record.stage == 'b') ? (
									<a-popconfirm
										placement="topRight"
										title="确定提交吗?"
										ok-text="确定"
										cancel-text="取消"
										onConfirm={() => this.checkRecordSubmit(record, "design")}
										visible={record.isDefine == true}
										onCancel={() => (record.isDefine = false)}
									>
                  <a-spin size="small" spinning={record.submitLoading}>
                    <a onClick={() => this.checkIsDefine(record)}>审批提交</a>
                  </a-spin>
									</a-popconfirm>
								) : (<span>审批提交</span>),
							attrs: {}
						}

						if (index == 0 || index % 3 == 0) {
							obj.attrs.rowSpan = 3
						} else {
							obj.attrs.rowSpan = 0
						}
						return obj
					}
				}
			],
			designExportColumns: [
				{
					title: "开发阶段",
					dataIndex: "stage",
					align: "center",
					width: 70,
					customRender: (text, record, index) => {
						const obj = {
							children: (
								<a-select
                  disabled='true'
									style="width:100%;height:100%;border: 0;"
									v-model={text}
									onChange={$event => this.onDesignSelectChange(record.inBatteryId, "stage", $event)}
								>
									<a-select-option value="a">A样</a-select-option>
									<a-select-option value="b">B样</a-select-option>
									<a-select-option value="c">C样</a-select-option>
									<a-select-option value="d">D样</a-select-option>
								</a-select>
							),
							attrs: {}
						}

						if (index == 0 || index % 3 == 0) {
							obj.attrs.rowSpan = 3
						} else {
							obj.attrs.rowSpan = 0
						}

						return obj
					}
				},
				{
					title: "样品阶段",
					width: 70,
					dataIndex: "sample",
					align: "center",

					customRender: (text, record, index) => {
						const obj = {
							children: (
								<a-input
                  disabled='true'
									placeholder="例：A1"
									defaultValue={text}
									onBlur={$event => this.onDesignCellChange(record.inBatteryId, "sample", $event)}
									style="text-align: center;width:100%;height:100%;border:0"
								/>
							),
							attrs: {}
						}

						if (index == 0 || index % 3 == 0) {
							obj.attrs.rowSpan = 3
						} else {
							obj.attrs.rowSpan = 0
						}

						return obj
					}
				},
				{
					title: "方案编号",
					width: 70,
					dataIndex: "code",
					align: "center",

					customRender: (text, record, index) => {
						const obj = {
							children: (
								<a-input
                  disabled='true'
									placeholder="例：A1-01#"
									defaultValue={text}
									onBlur={$event => this.onDesignCellChange(record.inBatteryId, "sample", $event)}
									style="text-align: center;width:100%;height:100%;border:0"
								/>
							),
							attrs: {}
						}

						if (index == 0 || index % 3 == 0) {
							obj.attrs.rowSpan = 3
						} else {
							obj.attrs.rowSpan = 0
						}

						return obj
					}
				},
				{
					title: "方案描述",
					width: 140,
					dataIndex: "remark",
					align: "center",
					customRender: (text, record, index) => {
						const obj = {
							children: (
								<a-input
                  disabled='true'
									defaultValue={text}
									onBlur={$event => this.onDesignCellChange(record.inBatteryId, "remark", $event)}
									style="text-align: center;width:100%;height:100%;border:0"
								/>
							),
							attrs: {}
						}

						if (index == 0 || index % 3 == 0) {
							obj.attrs.rowSpan = 3
						} else {
							obj.attrs.rowSpan = 0
						}

						return obj
					}
				},
				{
					title: "文件内容",
					width: 70,
					dataIndex: "name",
					align: "center",
					scopedSlots: {
						customRender: "name"
					}
				},
				{
					title: "试验线",
					width: 70,
					dataIndex: "factory",
					align: "center",
					customRender: (text, record, index) => {
						const obj = {
							children: (
								<a-input
                  disabled='true'
									defaultValue={text}
									onBlur={$event => this.onDesignCellChange(record.inBatteryId, "factory", $event)}
									style="text-align: center;width:100%;height:100%;border:0"
								/>
							),
							attrs: {}
						}

						if (index == 0 || index % 3 == 0) {
							obj.attrs.rowSpan = 3
						} else {
							obj.attrs.rowSpan = 0
						}

						return obj
					}
				},
				{
					title: "冻结状态",
					width: 70,
					dataIndex: "freezeStatus",
					align: "center",
					customRender: (text, record, index) => {
            const obj = {
              children: (
                <a onClick={() => this.openCheckRecord(record.id, "freeze")} style="text-align: center">
              {text == 10?"已冻结":(text == 5?"冻结中":(text == 8?"已驳回":"未冻结"))}
              </a>
            ),
            attrs: {}
          }

            if (index == 0 || index % 3 == 0) {
              obj.attrs.rowSpan = 3
            } else {
              obj.attrs.rowSpan = 0
            }
            return obj
          }
				},/*{
					title: "审批状态",
					width: 70,
					dataIndex: "checkStatus",
					align: "center",
					customRender: (text, record, index) => {

            const obj = {
              children: (
                <a onClick={() => this.openCheckRecord(record.id, "design")} style="text-align: center">
              {this.checkStatus.find(e => e.key == text).value}
                </a>
              ),
              attrs: {}
            }

              if (index == 0 || index % 3 == 0) {
                obj.attrs.rowSpan = 3
              } else {
                obj.attrs.rowSpan = 0
              }
              return obj
            }

				},*/
        {
          title: "编辑人",
          width: 70,
          dataIndex: "updateName",
          align: "center"
        },
				{
					title: "更新时间",
					width: 70,
					dataIndex: "updateTime",
					align: "center",
					customRender: (text, record, index) => (null == text ? "" : moment(new Date(text)).format("YYYY-MM-DD"))
				},
				/* {
            title: '审核人',
            width: 70,
            dataIndex: 'checkMan',
            align: 'center',

          }, {
            title: '批准人',
            width: 70,
            dataIndex: 'approveMan',
            align: 'center',

          }, */ {
					title: "操作",
					width: 70,
					dataIndex: "submit",
					align: "center",
          customRender: (text, record, index) => {
            const obj = {
              children:
                 (record.freezeStatus == 0 ||  record.freezeStatus == 8)  && (record.stage == 'a' || record.stage == 'b') ? (
                  <a-popconfirm
                  placement="topRight"
              title="确定提交吗?"
              ok-text="确定"
              cancel-text="取消"
              onConfirm={() => this.checkRecordSubmit(record, "freeze")}
            visible={record.isDefine == true}
            onCancel={() => (record.isDefine = false)}
          >
          <a-spin size="small" spinning={record.submitLoading}>
              <a onClick={() => this.checkIsDefine(record,"freeze")}>冻结申请</a>
            </a-spin>
            </a-popconfirm>
          ) : (<span>冻结申请</span>),
            attrs: {}
          }

            if (index == 0 || index % 3 == 0) {
              obj.attrs.rowSpan = 3
            } else {
              obj.attrs.rowSpan = 0
            }
            return obj
          }

				}
			],
			designExportAddColumns: [
				{
					title: "开发阶段",
					dataIndex: "stage",
					align: "center",
					width: 70,
					customRender: (text, record, index) => {
						if (text == "a") {
							return "A样"
						}

						if (text == "b") {
							return "B样"
						}
						if (text == "c") {
							return "C样"
						}
						if (text == "d") {
							return "D样"
						}
						if (text == "platform") {
							return "平台课题方案"
						}
						return text
					}
				},
				{
					title: "样品阶段",
					width: 70,
					dataIndex: "sample",
					align: "center"
				},
				{
					title: "方案编号",
					width: 70,
					dataIndex: "code",
					align: "center"
				},
				{
					title: "变更说明",
					width: 140,
					dataIndex: "remark",
					align: "center"
				},
				{
					title: "试验线",
					width: 70,
					dataIndex: "factory",
					align: "center"
				}
			],
			checkStatus: [
				{ key: 0, value: "待提交" },
				{ key: 10, value: "审核中" },
				{ key: 20, value: "批准中" },
				{ key: 30, value: "已审批" },
				{ key: 40, value: "归档中" },
				{ key: 50, value: "启用中" },
				{ key: 60, value: "禁用中" },
				{ key: 70, value: "已归档" },
				{ key: 80, value: "已驳回" }
			],
			// 加载数据方法 必须为 Promise 对象
			loadData: parameter => {
				this.queryParam.structureType = this.structureType
				this.queryParam.type = this.type
				return getBatteryDesignPage(Object.assign(parameter, this.queryParam)).then(res => {
					return res.data
				})
			},
			loading: false
		}
	},
	created() {
    this.breadcrumb = JSON.parse(localStorage.getItem('breadcrumb'))
    if(this.breadcrumb.designType){
      this.designType = this.breadcrumb.designType
    }
		getBatteryDesign({ id: this.$route.query.batteryId || this.breadcrumb.id }).then(res => {
			this.design = res.data
			switch (res.data.structureType) {
				case "g_cylinder":
					this.structureType1 = "G圆柱"
					break
				case "c_cylinder":
					this.structureType1 = "C圆柱"
					break
				case "v_cylinder":
					this.structureType1 = "V圆柱"
					break
				case "winding":
					this.structureType1 = "方形卷绕"
					break
				case "lamination":
					this.structureType1 = "方形叠片"
					break
				case "soft_roll":
					this.structureType1 = "软包"
					break
			}
		})

    


	},
	mounted() {
		this.openDesign({ id: this.$route.query.batteryId })
	},
	computed: {
		...mapGetters(["userInfo"])
	},
	methods: {
		...mapActions(["MenuChange"]),

    checkBomCanSubmit(bom,flag){
		  if(bom.lists.length > 0){

        for (let i = 0; i < bom.lists.length; i++) {
          if(bom.lists[i].sapNumber == null || bom.lists[i].sapNumber == ""){
            this.$message.warn("BOM有临时物料，请替换后再提交",3)
            flag.canSubmit = false
            return
          }
          this.checkBomCanSubmit(bom.lists[i],flag)
        }
      }
    },

		checkRecordSubmit(record, type) {
      record.isDefine = false
      record.submitLoading = true
			checkRecordSubmitCheck({ businessId: record.id, batteryId: record.batteryId, businessType: type }).then(res => {
				if (res.success) {
          record.submitLoading = false
					this.$message.success("提交成功")
					this.openDesign(this.design)
				} else {
          record.submitLoading = false
					this.$message.error("提交失败：" + res.message)
				}
			})
		},
		checkIsDefine(re,type) {



		  let flag = {canSubmit:true}
			//审核人已定义
			if (this.design.isDefine == 1) {

        if("freeze" == type){
          getBom({batteryId:re.inBatteryId}).then(res => {
            let bomData = JSON.parse(res.data.bomData)
            if(bomData.length > 0){
              let lists = bomData[0].lists
              for (let i = 0; i < lists.length; i++) {
                if(lists[i].sapNumber == null || lists[i].sapNumber == ''){
                  this.$message.warn("BOM有临时物料，请替换后再提交",3)
                  flag.canSubmit = false
                  return
                }
                this.checkBomCanSubmit(lists[i],flag)
              }
            }
          }).finally(() => {
            re.isDefine = flag.canSubmit
            return
          })
        }
      } else {
				re.isDefine = false
				this.$message.warn("审核人未定义")
				return
			}

			re.isDefine = true

		},
		openCheckRecord(businessId, type) {
			this.$refs.checkRecord.open(businessId,type)
		},
		handleOk() {
			this.$refs.table.refresh()
		},
		gotoDevelop(record) {
			//this.switchApp()
			this.$router.push({
				path: "/batterydesign"
			})
		},
		gotoDesign(record) {
			//this.switchApp()
			this.$router.push({
				path: "/battery_design_manager",
				query: {
					batteryId: this.design.id
				}
			})
		},
		openExportDesign() {
			batteryDesignRelationOnlyDesignList({ batteryId: this.record.id })
				.then(res => (this.onlyDesignList = res.data))
				.finally(() => (this.designAddVisible = true))
		},
		onSorCellChange(id, key, value) {
			let update = {}
			update.id = id
			update[key] = value.target.value
			batteryDesignRelationUpdate(update)
				.then()
				.finally(() => this.openSor(this.record))
		},
		submit(id) {
			sysBatteryDesignSubmit({ id: id }).then(() => this.openDesign(this.record))
		},

		onSorSelectChange(id, key, value) {
			let update = {}
			update.id = id
			update[key] = value
			batteryDesignRelationUpdate(update)
				.then()
				.finally(() => this.openSor(this.record))
		},
		onDesignSelectChange(id, key, value) {
			let update = {}
			update.inBatteryId = id
			update[key] = value
			batteryDesignRelationUpdateByInBatteryId(update)
				.then()
				.finally(() => this.openDesign(this.record))
		},

		onDesignCellChange(id, key, value) {
			let update = {}
			update.inBatteryId = id
			update[key] = value.target.value
			batteryDesignRelationUpdateByInBatteryId(update)
				.then()
				.finally(() => this.openDesign(this.record))
		},
		onDesignCellChangeById(id, key, value) {
			let update = {}
			update.id = id
			update[key] = value.target.value
			batteryDesignRelationUpdate(update)
				.then()
				.finally(() => this.openDesign(this.record))
		},
		addSor() {
			addSor({ id: this.record.id })
				.then()
				.finally(() => this.openSor(this.record))
		},
		addDesign() {
			addDesign({ id: this.record.id })
				.then()
				.finally(() => this.openDesign(this.record))
		},
		gotoIndex() {
			this.$router.go(-1)
		},
		addExportDesign() {
			let select = this.onlyDesignListRowKeys
			let params = {}
			params.batteryId = this.record.id
			params.copyIds = select

			batteryDesignRelationAddExportDesign(params).then(res => {
				this.onlyDesignListRowKeys = []
				this.onlyDesignListRow = []
				this.designAddVisible = false
				this.openDesign(this.record)
			})
		},

		copyDesign() {
			if (this.designSelectedRowKeys.length == 0) {
				this.$message.warn("请至少选中一条数据")
				return
			}

			let select = this.designSelectedRow
			let filter = []
			for (let i = 0; i < select.length; i++) {
				if (select[i].name == "方案设计") {
					filter.push(select[i].id)
				}
			}

			/*this.designSelectedRow = []
        this.designSelectedRowKeys = []*/

			let params = {}
			params.batteryId = this.record.id
			params.copyIds = filter

			copyDesign(params).then(res => {
				this.openDesign(this.record)
			})
		},

		copyExportDesign() {
			if (this.designExportSelectedRowKeys.length == 0) {
				this.$message.warn("请至少选中一条数据")
				return
			}

			let select = this.designExportSelectedRow
			let filter = []
			for (let i = 0; i < select.length; i++) {
				if (select[i].name == "方案设计") {
					filter.push(select[i].id)
				}
			}

			this.designExportSelectedRow = []
			this.designExportSelectedRowKeys = []

			let params = {}
			params.batteryId = this.record.id
			params.copyIds = filter

			copyDesign(params).then(res => {
				this.openDesign(this.record)
			})
		},

		deleteSor() {
			let select = this.sorSelectedRow
			for (let i = 0; i < select.length; i++) {
				let update = {}
				update.id = this.sorSelectedRow[i].id
				update["status"] = 1
				batteryDesignRelationUpdate(update)
					.then()
					.finally(() => this.openSor(this.record))
			}
			this.sorSelectedRow = []
			this.sorSelectedRowKeys = []
		},
		deleteDesign() {
			let select = this.designSelectedRow

			let filter = []
			for (let i = 0; i < select.length; i++) {
				if (select[i].name == "方案设计") {
					filter.push(select[i])
				}
			}

			for (let i = 0; i < filter.length; i++) {
				let update = {}
				update.inBatteryId = filter[i].inBatteryId
				update["status"] = 1
				batteryDesignRelationUpdateByInBatteryId(update)
					.then()
					.finally(() => {
						this.deleteDesignVisible = false
						this.openDesign(this.record)
					})
			}
			this.designSelectedRow = []
			this.designSelectedRowKeys = []
		},
		deleteDesignBefore() {
			if (this.designSelectedRowKeys.length == 0) {
				this.$message.warn("请选中一条数据")
				return
			} else {
        for (let i = 0; i < this.designSelectedRow.length; i++) {
          if(!this.designSelectedRow[i].checkStatus == 0){
            this.$message.warn("只有未提交的数据才能删除")
            return
          }
        }

				this.deleteDesignVisible = true
			}
		},
		deleteExportDesign() {
			let select = this.designExportSelectedRow

			let filter = []
			for (let i = 0; i < select.length; i++) {
				if (select[i].name == "方案设计") {
					filter.push(select[i])
				}
			}

			for (let i = 0; i < filter.length; i++) {
				let update = {}
				update.inBatteryId = filter[i].inBatteryId
				update["status"] = 1
				batteryDesignRelationUpdateByInBatteryId(update)
					.then()
					.finally(() => {
						this.deleteExportDesignVisible = false
						this.openDesign(this.record)
					})
			}
			this.designSelectedRow = []
			this.designSelectedRowKeys = []
		},
		deleteExportDesignBefore() {
			if (this.designExportSelectedRowKeys.length == 0) {
				this.$message.warn("请选中一条数据")
				return
			} else {

        for (let i = 0; i < this.designExportSelectedRow.length; i++) {
          if(!this.designExportSelectedRow[i].freezeStatus == 0){
            this.$message.warn("只有未提交的数据才能删除")
            return
          }
        }

				this.deleteExportDesignVisible = true
			}
		},
		openSor(record) {
			this.record = record
			batteryDesignRelationSorList({ batteryId: record.id }).then(res => {
				if (res.success) {
					this.sorData = res.data
				}
			})

			this.sorVisible = true
		},
		openDesign(record) {
			this.record = record
			batteryDesignRelationDesignList({ batteryId: record.id, designType: "design" })
				.then(res => {
					if (res.success) {
						this.designData = res.data

						this.onlyDesignData = []
						for (let i = 0; i < res.data.length; i++) {
							if (res.data[i].name == "方案设计") {
								this.onlyDesignData.push(res.data[i])
							}
						}

						batteryDesignRelationDesignList({ batteryId: record.id, designType: "export" })
							.then(res1 => {
								this.designExportData = res1.data
								this.onlyDesignExportData = []
								for (let i = 0; i < res1.data.length; i++) {
									if (res1.data[i].name == "方案设计") {
										this.onlyDesignExportData.push(res1.data[i])
									}
								}
							})
							.finally(() => {
								let trs = document.getElementsByTagName("tr")

								for (let i = 0; i < trs.length; i++) {
									let first = false
									if (trs[i]._prevClass != null && trs[i]._prevClass.indexOf("unSet") != -1) {
										continue
									}
									for (let j = 0; j < this.onlyDesignExportData.length; j++) {
										if (this.onlyDesignExportData[j].id == trs[i].dataset.rowKey) {
											first = true
										}
									}

									if (first) {
										trs[i].firstChild.setAttribute("rowspan", 3)
									} else {
										if (
											trs[i]._prevClass != null &&
											trs[i]._prevClass.indexOf("ant-table-row ant-table-row-level-0") != -1 &&
											trs[i].firstChild.rowSpan != 3
										) {
											trs[i].firstChild.style.display = "none"
										}
									}
								}
							})
					}
				})
				.finally(res => {
					let trs = document.getElementsByTagName("tr")

					for (let i = 0; i < trs.length; i++) {
						let first = false
						if (trs[i]._prevClass != null && trs[i]._prevClass.indexOf("unSet") != -1) {
							continue
						}
						for (let j = 0; j < this.onlyDesignData.length; j++) {
							if (this.onlyDesignData[j].id == trs[i].dataset.rowKey) {
								first = true
							}
						}
						if (first) {
							trs[i].firstChild.setAttribute("rowspan", 3)
						} else {
							if (
								trs[i]._prevClass != null &&
								trs[i]._prevClass.indexOf("ant-table-row ant-table-row-level-0") != -1 &&
								trs[i].firstChild.rowSpan != 3
							) {
								trs[i].firstChild.style.display = "none"
							}
						}
					}
				})

			this.designVisible = true
		},

		designTypeChange(type) {
			if (type == "design") {
				this.changePageCallback()
			} else {
				this.changeExportPageCallback()
			}
		},

		changePage(callback) {
			this.$nextTick(() => {
				callback()
			})
		},
		changePageCallback() {
			let trs = document.getElementsByTagName("tr")

			for (let i = 0; i < trs.length; i++) {
				let first = false
				if (trs[i]._prevClass != null && trs[i]._prevClass.indexOf("unSet") != -1) {
					continue
				}
				for (let j = 0; j < this.onlyDesignData.length; j++) {
					if (this.onlyDesignData[j].id == trs[i].dataset.rowKey) {
						first = true
					}
				}
				if (first) {
					trs[i].firstChild.setAttribute("rowspan", 3)
				} else {
					if (
						trs[i]._prevClass != null &&
						trs[i]._prevClass.indexOf("ant-table-row ant-table-row-level-0") != -1 &&
						trs[i].firstChild.rowSpan != 3
					) {
						trs[i].firstChild.style.display = "none"
					}
				}
			}
		},
		changeExportPageCallback() {
			let trs = document.getElementsByTagName("tr")

			for (let i = 0; i < trs.length; i++) {
				let first = false

				if (trs[i]._prevClass != null && trs[i]._prevClass.indexOf("unSet") != -1) {
					continue
				}

				for (let j = 0; j < this.onlyDesignExportData.length; j++) {
					if (this.onlyDesignExportData[j].id == trs[i].dataset.rowKey) {
						first = true
					}
				}
				if (first) {
					trs[i].firstChild.setAttribute("rowspan", 3)
				} else {
					if (
						trs[i]._prevClass != null &&
						trs[i]._prevClass.indexOf("ant-table-row ant-table-row-level-0") != -1 &&
						trs[i].firstChild.rowSpan != 3
					) {
						trs[i].firstChild.style.display = "none"
					}
				}
			}
		},

		openAdd() {
			if (this.type == "product") {
				this.$refs.addForm.add()
			} else {
				this.$refs.addFormPlatform.add()
			}
		},

		deleteBattery() {
			let select = this.selectedRow
			for (let i = 0; i < select.length; i++) {
				if (select[i].type == this.type) {
					this.designDelete(select[i].id)
				}
			}
		},

		designDelete(id) {
			sysBatteryDesignEdit({ id: id, status: 1 }).then(() => this.$refs.table.refresh())
		},

		submit(id) {
			sysBatteryDesignSubmit({ id: id }).then(() => this.$refs.table.refresh())
		},

		designCopy(record) {
			sysBatteryDesignCopy(record).then(() => this.$refs.table.refresh())
		},

		switchApp() {
			const applicationData = Vue.ls.get(ALL_APPS_MENU)
			this.MenuChange(applicationData[0])
				.then(res => {})
				.catch(err => {
					this.$message.error("错误提示：" + err.message, 1)
				})
		},
		gotoSor(record) {
			window.open("/system_battery_design_sor?batteryId=" + record.inBatteryId, "_blank")
			/*this.switchApp()
        this.$router.push({
          path: "/system_battery_design_sor",
          name: "SystemBatteryDesignSor",
          query: {
            batteryId: record.inBatteryId
          }
        });*/
		},
		gotoManager(record) {
			if (record.name == "方案设计") {
				window.open(
					`/battery_design_manager?batteryId=${record.inBatteryId}`,
					"_blank"
				)
			}
			if (record.name == "MI设计") {
				window.open(
					`/g_cylinder_mi_standard_manage?batteryId=${record.inBatteryId}`,
					"_blank"
				)
			}
			if (record.name == "电芯BOM设计") {
				window.open(
					`/sys_battery_design_bom?batteryId=${record.inBatteryId}`,
					"_blank"
				)
			}
      localStorage.setItem('breadcrumb',JSON.stringify({
        batteryId:this.$route.query.batteryId,
				batteryName:this.breadcrumb.batteryName,
        code:record.code,
				type:this.breadcrumb.type,
        designType:this.designType,
        id: this.$route.query.batteryId   //当前页面的batteryId
			}))

			/*this.switchApp()
        this.$router.push({
          path: "/battery_design_manager",
          query: {
            batteryId: record.id
          },
        });*/
		},

		gotoMi(record) {
			//this.switchApp()
			this.$router.push({
				path: "/g_cylinder_mi_standard_manage",
				query: {
					batteryId: record.id
				}
			})
		},
		gotoCheck(record) {
			//this.switchApp()
			this.$router.push({
				path: "/batteryCheckIndex"
			})
		},
		gotoBom(record) {
			//this.switchApp()
			this.$router.push({
				path: "/sys_battery_design_bom",
				query: {
					batteryId: record.id
				}
			})
		},

		gotoDevelop(record) {
			//this.switchApp()
			this.$router.push({
				path: "/batterydesign"
			})
		}
		/* sysAppDelete (record) {
        this.loading = true
        sysAppDelete(record).then((res) => {
          this.loading = false
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败：' + res.message)
          }
        }).catch((err) => {
          this.$message.error('删除错误：' + err.message)
        })
      } */
	}
}
</script>
<style lang="less" scoped="">
.table-operator {
	margin-bottom: 18px;
}

.tab-title {
	padding: 0 10px;
}
div.tab-head div.active {
	padding: 0 10px;
	font-size: 24px;
	font-weight: 700;
	color: rgba(0, 73, 176, 1);
	margin-bottom: -4px;
	cursor: text;
}

button {
	margin-right: 8px;
}

/deep/ .ant-card-body {
	padding: 10px 24px !important;
}

/deep/ .table-page-search-wrapper .ant-form-inline .ant-form-item,
/deep/ .table-page-search-wrapper .table-page-search-submitButtons {
	margin-bottom: 0;
}

/deep/ .ant-col-md-8 {
	padding-top: 10px;
}

/deep/ .ant-form-item-label > label::after {
	content: "";
}

/deep/
	.ant-table-middle
	> .ant-table-content
	> .ant-table-scroll
	> .ant-table-body
	> table
	> .ant-table-tbody
	> tr
	> td {
	padding: 5.5px 5px;
}

/deep/ .ant-table-pagination.ant-pagination {
	margin: 5px 0;
}

/deep/ .ant-table-tbody > tr > td {
	padding: 0;
	height: 25px;
}

/deep/.ant-select-selection--single {
	position: relative;
	/* height: 32px; */
	cursor: pointer;
	height: 100%;
	text-align: center;
	display: flex;
	justify-content: center;
	align-items: center;
}

/deep/ .ant-table-thead > tr > th {
	font-weight: bold;
	background-color: rgba(232, 232, 232, 0.5);
}

/deep/ .ant-input[disabled] {
  background-color: white;
  color:rgba(0, 0, 0, 0.65);
}

/deep/.ant-select-disabled .ant-select-selection{
  background-color: white;
  color:rgba(0, 0, 0, 0.65);
}

</style>
