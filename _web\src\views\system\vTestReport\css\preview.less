.wrapper {
    background: #fff;
    height: 100vh;
    padding: 6px 16px 16px;
    margin: 0 0 0 -40px;
    background-color: #f0f2f5;

    overflow: scroll;
}

.head_title {
    color: #333;
    padding: 10px 0;
    font-size: 20px;
    font-weight: 600;
    /* font-family: 'Times New Roman'; */
}

.head_title::before {
    width: 8px;
    background: #1890ff;
    margin-right: 8px;
    content: "\00a0"; //填充空格
}

.all-wrapper {
    padding: 0 0 10px;
    display: flex;
    justify-content: space-between;
}

/* 通用 */
.mt3{
    margin-top: 3px;
}
.mt10 {
    margin-top: 10px;
}

.ml10{
    margin-left: 10px;
}

.mr10 {
    margin-right: 10px;
}

.flex-sb-center-row {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.flex-column {
    display: flex;
    flex-direction: column;
}

.flex-center{
    display: flex;
    align-items: center;
}

.block {
    height: fit-content;
    padding: 10px;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
    position: relative;
}

.left-content {
    width: 618px;
    margin-right: 10px;
}

.right-content {
    width: calc(100% - 628px);
}

/* 组件 */

/* 表格组件 */
/deep/ .all-wrapper .ant-table-thead {
    position: sticky;
    top: 0;
    z-index: 2;
}

/deep/ .all-wrapper .ant-table-placeholder {
    border: none !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 0;
}

/deep/ .right-content .ant-table-body {
    border: 1px solid #e8e8e8;
    overflow: auto;
}

/deep/ .ant-table-body::-webkit-scrollbar {
    height: 10px;
    width: 5px;
}

/deep/ .ant-table-body::-webkit-scrollbar-thumb {
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: #dddbdb;
}

/deep/ .ant-table-body::-webkit-scrollbar-track {
    -webkit-box-shadow: 0;
    border-radius: 0;
    background: #f1f1f1;
}

/deep/ #table1>div>div>div>div>div>div>table>thead {
    height: 64px;
}

/deep/ #table1>.ant-table-wrapper>div>div>ul {
    display: none;
}

/deep/ .ant-table-pagination.ant-pagination {
    float: right;
    margin: 0;
}

/deep/ .ant-table-thead>tr>th {
    padding: 5px !important;
    font-size: 13px !important;
}

/deep/ .ant-table-tbody>tr>td {
    padding: 0px !important;
    height: 32px !important;
    font-size: 12px !important;
}

/deep/ .ant-table-footer {
    padding: 0;
}

/deep/ .ant-table-row-expand-icon {
    margin-right: 0px;
}

/deep/ .right-content .ant-empty-normal {
    margin: -2px 0;
}

/deep/ .ant-empty-image {
    display: none;
}

/deep/ .right-content .ant-input {
    border: none;
}

/deep/ .ant-checkbox-group {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
}

/deep/ .ant-checkbox-group-item {
    font-size: 12px;
    width: 23%;
}

/deep/ .ant-radio-inner {
    top: 1px;
    left: 1px;
}

/deep/ .ant-select-selection__rendered {
    margin-right: 0px;
}

/deep/ .ant-form-item {
    margin-bottom: 0;
}

/deep/ .ant-popover-buttons {
    display: flex !important;
    flex-direction: column !important;
    margin-bottom: 15px;
}

/deep/ .ant-calendar-picker-icon {
    display: none;
}

/deep/ .ant-calendar-picker-input.ant-input {
    color: black;
    font-size: 12px;
    border: 0;
    text-align: center;
    padding: 0;
}

.ant-modal-body {
    padding: 0;
}

/deep/ .ant-btn>i,
/deep/ .ant-btn>span {
    display: flex;
    justify-content: center;
}

/deep/ .ant-checkbox-group-item {
    display: block;
    width: 100%;
    text-align: left;
}

.reveal-text-opacity{
    /* font-size: 14px; */
    font-family: 'Times New Roman';
    opacity: 0;
    padding: 0;
    margin: 0;
    line-height: 1;
    white-space: pre-wrap;  //避免空格不被计算在内
  }