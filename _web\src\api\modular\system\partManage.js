/**
 * 系统应用
 *
 * <AUTHOR>
 * @date 2020年4月23日12:10:57
 */
 import { axios } from '@/utils/request'

 /**
  * 系统应用列表
  *
  * <AUTHOR>
  * @date 2020年7月9日15:05:01
  */
 export function getPartPage (parameter) {
   return axios({
     url: '/sysPart/page',
     method: 'post',
     data: parameter
   })
 }

 export function getPartRandom (parameter) {
    return axios({
      url: '/sysPart/random',
      method: 'get',
      params: parameter
    })
  }

  export function getPartList (parameter) {
    return axios({
      url: '/sysPart/list',
      method: 'get',
      params: parameter
    })
  }