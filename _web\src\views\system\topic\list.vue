<template>
<div>
     <div class="title">
        立项申请
        <a class="btn" @click="$refs.addForm.add()">新增立项申请</a>
    </div>
<!--    <x-card>
      <div slot="content" class="table-page-search-wrapper">
          <a-form layout="inline">
              <a-row :gutter="48">
                  <a-col :md="8" :sm="24">
                      <a-form-item label="状态">
                          <a-select @change="this.change" v-model="queryParam.statusId" placeholder="请选择课题状态" >
                              <a-select-option :value="1" >在研</a-select-option>
                              <a-select-option :value="2" >结项</a-select-option>
                              <a-select-option :value="3" >暂停</a-select-option>
                              <a-select-option :value="4" >不通过</a-select-option>
                          </a-select>
                      </a-form-item>
                  </a-col>
                  <a-col :md="8" :sm="24">
                      <a-form-item label="课题名称">
                          <a-input @keyup.enter.native="change" v-model="queryParam.projectName" allow-clear placeholder="请输入课题名称"/>
                      </a-form-item>
                  </a-col>
                  <a-col :md="8" :sm="24">
                      <span class="table-page-search-submitButtons">
                      <a-button type="primary" @click="callReviewList" >查询</a-button>
                      <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                      <a class="btn" @click="$refs.addForm.add()">新增立项申请</a>
                  </span>
                  </a-col>
              </a-row>
          </a-form>
      </div>
    </x-card>-->
    <div style="background:#fff">
      <a-spin :spinning="loading">
          
        <a-table :scroll="{ x: 1300 }"
                 style="background:#fff" size="small" ref="table"
                 :rowKey="(record) => record.issueId" :columns="columns" :dataSource="list" :loading="loading"  >

          <div slot="cate1">分类</div>
          <div slot="projectName">项目名称</div>
          <!-- <div slot="platformLevel">课题等级</div> -->
          <div slot="platformOrProduct">平台|产品</div>
          <div slot="platformAndTopic">平台<br/>课题</div>
          <div slot="projectLevel">课题等级</div>
          <div slot="projectLeader">项目负责人</div>

          <template slot="cate" slot-scope="text,record">
                <div v-if="text">{{text}}</div>
                <div v-if="record.cate2">-{{record.cate2}}<span v-if="record.cate3">-{{record.cate3}}</span></div>
          </template>
          <span slot-scope="text,record" slot="affiliatedPlatform">
<!--            {{text != null && text != ''?text+record.affiliatedPlatform :''}}-->
            {{text}}
          </span>

          <span slot-scope="text,record" slot="department">
            {{text != null && text != ''?text+(record.department3 != null && record.department3 != ''?'-'+record.department3:'') :''}}
          </span>

          <template slot="reviewResult1" slot-scope="text">
            <div>
              <span class="green" v-if="text == 1">{{reviewRes[text]}}</span>
              <span class="red" v-else-if="text == 2">{{reviewRes[text]}}</span>
              <span class="yellow" v-else-if="text == 3">{{reviewRes[text]}}</span>

            </div>
          </template>

          <template slot="reviewResult2" slot-scope="text">
            <div>
                <span class="green" v-if="text == 1">{{reviewRes[text]}}</span>
                <span class="red" v-else-if="text == 2">{{reviewRes[text]}}</span>
                <span class="yellow" v-else-if="text == 3">{{reviewRes[text]}}</span>

            </div>
          </template>

          <template slot="viewResult" slot-scope="text,record">
            <div style="text-align:center;"><a @click="openResult(record)"> 查阅</a></div>
          </template>

          <template slot="researchContent" slot-scope="text">
            <clamp :text="text" :sourceText="[text]" :isCenter="true"></clamp>
          </template>
          <template slot="projectBackGround" slot-scope="text">
            <clamp :text="text" :sourceText="[text]" :isCenter="true"></clamp>
          </template>
          <span slot="action" slot-scope="text,record">
            

            <a v-if="record.statusId == 11907" @click="$refs.editForm.edit(record)">修改</a>
            <a-popconfirm v-if="record.statusId == 11907" placement="topRight" title="确认取消？" @confirm="() => callCancelTransition(record)">
              <a> 取消立项</a>
            </a-popconfirm>
            <!-- <a @click="openResult(record)"> 查看结果</a> -->
            <a @click="handleToJira(record)"> 上传文件</a>
          </span>

        </a-table>

        <a-modal :visible="showResult"
                 title="立项评审结果"
                 @cancel="() => showResult = false"
                 width="50%"

        >
          <template slot="footer">
            <a-button key="back" @click="() => showResult = false">
              关闭
            </a-button>
          </template>



          <a-table
            :columns="resultColumns" :dataSource="resultList"
            :rowKey="(record) => record.issueId"
            :pagination="false"
          >
            <template slot="result" slot-scope="text">
              <div>
<!--                <span class="green" v-if="text == 1">{{reviewRes[text]}}</span>-->
<!--                <span class="red" v-else-if="text == 2">{{reviewRes[text]}}</span>-->
<!--                <span class="yellow" v-else-if="text == 3">{{reviewRes[text]}}</span>-->
                <span>{{reviewRes[text]}}</span>
              </div>
            </template>


            <template slot="opinion" slot-scope="text">
              <clamp :text="text" :sourceText="[text]"></clamp>
            </template>
          </a-table>


        </a-modal>

        <add-form ref="addForm" @ok="handleOk"/>
        <edit-form ref="editForm" @ok="handleOk"/>
      </a-spin>
    </div>

</div>
</template>

<script>
  import {
    getReviewListData,
    cancelTransition
  } from "@/api/modular/system/topic"
  import addForm from './addForm'
  import editForm from './editForm'
  import {clamp} from '@/components'
  import Vue from "vue";
  import {XCard} from '@/components'
  export default {
    components: {
      addForm,
      editForm,
      clamp,
      XCard
    },

    data() {
      return {
        queryParam:{},
        showResult: false,
        resultList:[],
        windowHeight: document.documentElement.clientHeight - 200,
        listType: 5,
        width: document.documentElement.clientWidth - 200,
        visible: false,
        reviewRes: ['/', '通过', '不通过', '再确认'],
        list: [],
        record: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 8
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          }
        },
        resultColumns: [

          {
            dataIndex: 'cate',
            width: 70,
            align: 'center',
            title: '分类'

          },

          {
            title: '评审结果',
            dataIndex: 'result',
            width: 70,
            align: 'center',
            scopedSlots: {customRender: 'result'},
          },
          {
            title: '评审意见',
            dataIndex: 'opinion',
            width: 200,
            align: 'center'
          }
        ],
        columns: [
          {
            title: '序号',
            dataIndex: 'index',
            align: 'center',
            width: 35,
            customRender: (text, record, index) => (<span> {index+1} </span>)
          },
          {
            title: '平台一级分类',
            dataIndex: 'affiliatedPlatform1',
            align: 'center',
            width: 80,
            // scopedSlots: {customRender: 'affiliatedPlatform'},
          },
          {
            title: '平台二级分类',
            dataIndex: 'affiliatedPlatform2',
            align: 'center',
            width: 80,
            // scopedSlots: {customRender: 'affiliatedPlatform'},
          },
          {
            dataIndex: 'cate1',
            width: 70,
            align: 'center',
            title: '课题分类',
            scopedSlots: {customRender: 'cate'},

          },
          {
            title: '课题名称',
            dataIndex: 'projectName',
            width: 120,
            align: 'center'
          },
          {
            title: '研究内容',
            dataIndex: 'researchContent',
            width: 120,
            align: 'center',
            scopedSlots: {customRender: 'researchContent'},
          },
          {
            title: '课题背景',
            dataIndex: 'projectBackGround',
            width: 120,
            align: 'center',
            scopedSlots: {customRender: 'projectBackGround'},
          },

          {
            slots: {title: 'projectLevel'},
            dataIndex: 'projectLevel',
            width: 50,
             align:'center',
            customRender: (value, row, index) => {

              if (row.platformAndTopic == 2) {
                value = 4;
              }

              let level = ['-', 'S+', 'S', 'A', 'B', 'C']
              return level[parseInt(value)]
            }
          },
          {

            dataIndex: 'projectLeader',
            slots: {title: 'projectLeader'},
            align: 'center',
            width: 80
          },
          {
            title: '部门',
            dataIndex: 'department2',
            align: 'center',
            width: 80,
            scopedSlots: {customRender: 'department'},
          },
          {
            title: '创建时间',
            dataIndex: 'createDate',
            align: 'center',
            width:80,
          },
          {
            title: '当前节点',
            dataIndex: 'statusName',
            align: 'center',
            width: 50
          },
          {
            title:'评审结果',
            dataIndex:'viewResult',
            align:'center',
            width: 50,
            scopedSlots: {customRender: 'viewResult'}
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:'center',
            width: 120,
            scopedSlots: {customRender: 'action'}
          }
        ],
        /* columnsShow: [
          {
            title: '序号',
            dataIndex: 'index',
            align: 'center',
            width: 40,
            customRender: (text, record, index) => ( <span> {index+1} </span>)
          },
          {
            dataIndex: 'cate1',
            width: 70,
            align: 'center',
            title: '课题分类',
            scopedSlots: {customRender: 'cate'},

          },

          {
            title: '课题名称',
            dataIndex: 'projectName',
            width: 120,
            align: 'center'
          },
          {
            title: '研究内容',
            dataIndex: 'researchContent',
            width: 120,
            align: 'center',
            scopedSlots: {customRender: 'researchContent'},
          },
          {
            title: '课题背景',
            dataIndex: 'projectBackGround',
            width: 120,
            align: 'center',
            scopedSlots: {customRender: 'projectBackGround'},
          },

          {
            slots: {title: 'projectLevel'},
            dataIndex: 'projectLevel',
            width:50,
            align:'center',
            customRender:(value,row,index)=>{

              if (row.platformAndTopic == 2) {
                value = 4;
              }

              let level = ['-', 'S+', 'S', 'A', 'B', 'C']
              return level[parseInt(value)]
            }
          },
          {

            dataIndex: 'projectLeader',
            slots: {title: 'projectLeader'},
            align: 'center',
            width: 80
          },
          {
            title: '部门',
            dataIndex: 'department2',
            align: 'center',
            width: 80,
            scopedSlots: {customRender: 'department'},
          }, {
            title: '当前节点状态',
            dataIndex: 'statusName',
            align: 'center',
            width: 50
          },
          {
            title: '评审结果',
            dataIndex: 'reviewResult1',
            width: 80,
            align: 'center',
            scopedSlots: {customRender: 'reviewResult1'},

          },
          {
            title: '评审意见',
            dataIndex: 'reviewOpinion1',
            align: 'center',
            width: 100
          },
          {
            title: '二次评审结果',
            dataIndex: 'reviewResult2',
            align: 'center',
            width: 90,
            scopedSlots: {customRender: 'reviewResult2'}

          },
          {
            title: '二次评审意见',
            dataIndex: 'reviewOpinion2',
            width: 120,
            align: 'center'

          },
          {
            title: '操作',
            dataIndex: 'action',
            width: 120,
            scopedSlots: {customRender: 'action'}
          }
        ], */
        loading: false,
      }
    },
    methods: {

      change(value, label, extra){
        this.callReviewList()
      },

      callCancelTransition(record){
        cancelTransition({
          issueId:record.issueId
        }).then((res) => {
          if (res.success) {
           if (res.data.result) {
              this.$message.success('取消成功')
              this.callReviewList()
           }else{
            this.$message.error(res.data.message)
           }
          } else {
            this.$message.error('取消失败：' + res.message)
          }
        }).finally((res) => {
        })
      },

      openResult(result) {
        this.record = result

        this.resultList =[]

        this.resultList.push({id:1,cate:'一次评审',result:this.record.reviewResult1,opinion:this.record.reviewOpinion1})

        if(!(this.record.reviewResult2 == '' ||this.record.reviewResult2 == null)){
          this.resultList.push({id:2,cate:'二次评审',result:this.record.reviewResult2,opinion:this.record.reviewOpinion2})
        }



        this.showResult = true
      },

      handleOk() {
        this.callReviewList()
      },
      handleToJira(record) {
        if (record.issueKey == null) {
          return;
        }
        let $url = `http://jira.evebattery.com/browse/` + record.issueKey + `?auth=` + Vue.ls.get("jtoken");
        // let $url = `http://jira.evebattery.com/browse/` + record.issueKey;
        window.open($url, "_blank");
      },
      callReviewList() {
        this.loading = true
        this.queryParam.reviewListNum = this.listType
        getReviewListData(this.queryParam)
          .then((res) => {
            if (res.success) {
              this.list = res.data ? res.data : []
            } else {
              this.$message.error(res.message, 1);
            }
            this.loading = false
          })
          .catch((err) => {
            this.loading = false
            this.$message.error('错误提示：' + err.message, 1)
          });
      },
    },
    created() {
      this.callReviewList()
    },
  }
</script>


<style lang='less' scoped=''>
  /* /deep/ .ant-select-selection__rendered, /deep/ .ant-select-selection--single {
    height: 16px;
    line-height: 16px;
    font-size: 12px;
    margin: 0;
    border: 0;
    background: transparent;
    text-align: center;
  }

  /deep/ .ant-select-dropdown-menu-item {
    text-align: center;
  } */

  .btn {
    padding: 6px 10px;
    background: #1890FF;
    color: #fff;
    font-weight: initial;
    font-size: 12px;
    cursor: pointer;
    float: right;
  }

  .red {
    display: block;
    background: #ff3333;
    text-align: center;
    color: #fff;
  }

  .yellow {
    display: block;
    background: #fac858;
    text-align: center;
    color: #fff;
  }

  .green {
    display: block;
    background: #58a55c;
    text-align: center;
    color: #fff;
  }

  .title {
    background: #fff;
    padding: 15px 24px;
    font-weight: 700;
    font-size: 16px;
    line-height: 1.5;
  }

  /deep/ .m_text .temp {
    background: transparent;
  }

  /deep/ .ant-table-small > .ant-table-content > .ant-table-header > table > .ant-table-thead > tr > th, 
  /deep/ .ant-table-small > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th,
   /deep/ .ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-header > table > .ant-table-thead > tr > th,
    /deep/ .ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-thead > tr > th,
    /deep/  .ant-table-small > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table > .ant-table-thead > tr > th, 
     /deep/ .ant-table-small > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table > .ant-table-thead > tr > th,
      /deep/ .ant-table-small > .ant-table-content > .ant-table-fixed-left > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-thead > tr > th,
       /deep/ .ant-table-small > .ant-table-content > .ant-table-fixed-right > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-thead > tr > th{
        background-color: #f5f5f5;
  }

  /deep/ .ant-table-thead > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td .m_text .temp *,
  /deep/ .ant-table-tbody > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td .m_text .temp *,
  /deep/ .ant-table-thead > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td .m_text .temp *,
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td .m_text .temp * {
    background: inherit;
  }

  /* input {
    color: #000;
    height: 23px;
    line-height: 23px;
    width: 100%;
    font-size: 14px;
    border: 1px solid #d9d9d9;
    outline: none;
  } */

</style>
<style lang='css'>
  .ant-select-dropdown-menu-item {
    font-size: 12px;
    padding: 4px 8px;
  }

</style>