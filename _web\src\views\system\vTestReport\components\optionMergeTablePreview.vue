<template>
  <div>
    <div v-for="item in tableObjList">
      <div class="table-title-div" style="margin-top: 2px;">{{ item.tableName }}</div>
      <div class="calendar-table" style="height: 415px;">
        <a-table :data-source="item.dataList"
                 :columns="item.tableColumns"
                 :rowKey="record => record[item.rowKey]"
                 :pagination="paginationConfig"
                 bordered>
        </a-table>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  components: {
  },
  props: {
    tableObjList: {
      type: Array,
      default: []
    },
  },
  data() {
    return {
      paginationConfig: {
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '30', '40', '50'], // 显示的每页数量选项
        size: "small",
        showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
      },
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    onClose() {
      this.$emit("close")
    }
  },
}
</script>

<style lang="less" scoped>
/deep/ .calendar-table .ant-table-body {
  border: 1px solid #e8e8e8;
  max-height: 382px !important; /* 设置容器最大高度 */
  overflow-y: auto; /* 垂直滚动条 */
  overflow-x: scroll; /* 强制显示水平滚动条 */
  margin: 0;
}

/deep/ .calendar-table .ant-table-thead {
  position: sticky;
  top: 0;
  z-index: 12;
}

/deep/ .calendar-table .ant-table-pagination.ant-pagination {
  float: right;
  margin: 2px 0 0 0;
  font-size: 12px;
}

/deep/ .ant-table-thead > tr > th {
  padding: 2px !important;
  font-size: 13px !important;
  color: rgba(0, 0, 0, .85) !important;
  font-weight: 500 !important;
}

/deep/ .ant-table-tbody > tr > td {
  padding: 0px !important;
  font-size: 12px !important;
  color: #333 !important;
  font-weight: 400 !important;
  height: 32px !important;
}

/deep/ .ant-table-body::-webkit-scrollbar {
  height: 8px;
  width: 6px;
}

/deep/ .ant-table-body::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;

  background: #dddbdb;
}

/deep/ .ant-table-body::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: #f1f1f1;
}

/* 固定列 */
/deep/ .calendar-table .ant-table-thead tr:nth-child(1) th:nth-child(1),
/deep/ .calendar-table .ant-table-tbody tr td:nth-child(1){
  position: sticky;
  left: 0;
  z-index: 11;
}
/deep/ .calendar-table .ant-table-thead tr:nth-child(1) th:nth-child(2),
/deep/ .calendar-table .ant-table-tbody tr td:nth-child(2){
  position: sticky;
  left: 100px;
  z-index: 11;
}
/deep/ .calendar-table .ant-table-thead tr:nth-child(1) th:nth-child(3),
/deep/ .calendar-table .ant-table-tbody tr td:nth-child(3){
  position: sticky;
  left: 200px;
  z-index: 11;
}
/* 固定列数据背景颜色 */
/deep/ .calendar-table .ant-table-tbody tr td:nth-child(1),
/deep/ .calendar-table .ant-table-tbody tr td:nth-child(2),
/deep/ .calendar-table .ant-table-tbody tr td:nth-child(3) {
  background-color: #FFFFFF;
}

.table-title-div {
  height: 32px;
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}
</style>