<template>
	<div class="product_width">
		<a-spin :spinning="loading">
			<!-- 筛选区域 start -->
			<div class="table-page-search-wrapper">
				<a-form layout="inline">
					<a-row :gutter="48">
						<a-col :md="6" :sm="24">
							<a-form-item label="立项日期">
								<a-range-picker
									class="filter-form"
									:placeholder="['开始日期', '结束日期']"
									size="small"
									@change="dateChange"
								/>
							</a-form-item>
						</a-col>
						<a-col :md="5" :sm="24">
							<a-form-item label="产品类别">
								<div class="filter-box">
									<treeselect
										:limit="1"
										class="filter-form"
										@input="change"
										:max-height="200"
										placeholder="请选择产品类别"
										value-consists-of="BRANCH_PRIORITY"
										v-model="queryparam.cates"
										:multiple="true"
										:options="cate"
									/>
								</div>
							</a-form-item>
						</a-col>
						<a-col :md="5" :sm="24">
							<a-form-item label="产品状态">
								<treeselect
									:limit="1"
									class="filter-form"
									@input="change"
									:max-height="200"
									placeholder="请选择产品状态"
									value-consists-of="BRANCH_PRIORITY"
									v-model="queryparam.states"
									:multiple="true"
									:options="statuses"
								/>
							</a-form-item>
						</a-col>

						

						<a-col :md="5" :sm="24">
							<a-form-item label="产品部门">
								<treeselect
									class="filter-form"
									:limit="1"
									@input="change"
									:max-height="200"
									placeholder="请选择所属部门"
									:multiple="true"
									:options="departmentCateTreeData"
									value-consists-of="BRANCH_PRIORITY"
									v-model="queryparam.depts"
								>
								</treeselect>
							</a-form-item>
						</a-col>

						<a-col :md="3" :sm="24">
							<a-form-item label="">
								<a-input
									size="small"
									class="filter-form"
									@keyup.enter.native="change"
									v-model="queryparam.keyword"
									placeholder="请输入产品名称"
								>
									<a-icon slot="suffix" type="search" style="color: rgba(0,0,0,.45)" />
								</a-input>
							</a-form-item>
						</a-col>

						<a-col :md="4" :sm="24">
							<a-form-item label="时间">
								<a-week-picker placeholder="请选择周" @change="handleSelectWeek" />
							</a-form-item>
						</a-col>

						

						<a-col :md="1" :sm="24" :style="{ float: 'right' }">
							<div class="table-page-search-submitButtons" :style="{ float: 'right' }">
								<a-button size="small" style="margin-left: 120px;" type="primary" @click="query">查询</a-button>
								<!-- <a-button size="small" style="margin-left: 20px;margin-top:6px" @click="resetquery">重置</a-button> -->
							</div>
						</a-col>
					</a-row>
				</a-form>
			</div>
			<!-- 筛选区域 end -->

			<!-- 表格 start -->
			<div class="table-wrapper" :style="`height:${tableHeight}px;`">
				<a-table
					ref="table"
					:style="`height:${tableHeight}px;`"
					:rowKey="record => record.issueId + record.productCate"
					:columns="columns"
					:dataSource="loadData"
					:expandIconColumnIndex="9"
					:expandIconAsCell="false"
					:expandIcon="expandIcon"
				>
					<span slot="productCate" slot-scope="text, record">
						{{ record.productOrProject == 1 ? record.productCateParent + (text != "" ? "->" + text : "") : "" }}
					</span>
					<span slot="productProjectName" slot-scope="text, record">
						{{ record.productOrProject == 1 ? text : "" }}
					</span>
					<span slot="mstatus" slot-scope="text">{{ "product_stage_status" | dictType(text) }}</span>

					<span slot="weektime" slot-scope="text, record">
						<span v-if="record.weekProcess"> CW{{ record.weekProcess.weekTime }} </span>
						<span v-else></span>
					</span>

					<span slot="productState" slot-scope="text, record">
						<a-popover placement="bottom">
							<template slot="content">
								<span v-if="record.weekProcess">{{ record.weekProcess.productTxt }}</span>
								<span v-else></span>
							</template>
							<div>
								<span
									v-if="record.weekProcess"
									:class="[
										'state',
										record.weekProcess.productState == '1'
											? 'green'
											: record.weekProcess.productState == '2'
											? 'yellow'
											: 'red'
									]"
									@click="handleShowEvolve(record.weekProcess.productTxt)"
								></span>
								<span v-else></span>
							</div>
						</a-popover>
					</span>

					<span slot="qualityState" slot-scope="text, record">
						<a-popover placement="bottom">
							<template slot="content">
								<span v-if="record.weekProcess">{{ record.weekProcess.qualityTxt }}</span>
								<span v-else></span>
							</template>
							<div>
								<span
									v-if="record.weekProcess"
									:class="[
										'state',
										record.weekProcess.qualityState == '1'
											? 'green'
											: record.weekProcess.qualityState == '2'
											? 'yellow'
											: 'red'
									]"
									@click="handleShowEvolve(record.weekProcess.qualityTxt)"
								></span>
								<span v-else></span>
							</div>
						</a-popover>
					</span>

					<span slot="summitState" slot-scope="text, record">
						<a-popover placement="bottom">
							<template slot="content">
								<span v-if="record.weekProcess">{{ record.weekProcess.summitTxt }}</span>
								<span v-else></span>
							</template>
							<div>
								<span
									v-if="record.weekProcess"
									:class="[
										'state',
										record.weekProcess.summitState == '1'
											? 'green'
											: record.weekProcess.summitState == '2'
											? 'yellow'
											: 'red'
									]"
									@click="handleShowEvolve(record.weekProcess.summitTxt)"
								></span>
								<span v-else></span>
							</div>
						</a-popover>
					</span>

					<span slot="industryState" slot-scope="text, record">
						<a-popover placement="bottom">
							<template slot="content">
								<span v-if="record.weekProcess">{{ record.weekProcess.industryTxt }}</span>
								<span v-else></span>
							</template>
							<div>
								<span
									v-if="record.weekProcess"
									:class="[
										'state',
										record.weekProcess.industryState == '1'
											? 'green'
											: record.weekProcess.industryState == '2'
											? 'yellow'
											: 'red'
									]"
									@click="handleShowEvolve(record.weekProcess.industryTxt)"
								></span>
								<span v-else></span>
							</div>
						</a-popover>
					</span>
				</a-table>
			</div>
			<!-- 表格 end -->
		</a-spin>
	</div>
</template>

<script>
import { dashboardProcess } from "@/api/modular/system/dashboardManage"
import { getCatesTree } from "@/api/modular/system/report"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"

import { classType, statusType } from "@/utils/enum.js"

import { getWeek } from "@/utils/formatData"
import { indexOf } from "lodash"
import { getCateTree } from "@/api/modular/system/topic"

export default {
	components: {
		Treeselect
	},

	data() {
		return {
			departmentCateTreeData:[],
			typeOptions: [
				{
					id: 1,
					label: "预研产品"
				},
				{
					id: 2,
					label: "A|B新产品"
				},
				{
					id: 3,
					label: "试产新产品"
				},
				{
					id: 4,
					label: "量产品"
				},
				{
					id: 5,
					label: "其他"
				},
				{
					id: 6,
					label: "停止"
				}
			],
			statuses: [
				{
					id: 0,
					label: "立项讨论"
				},
				{
					id: 1,
					label: "A/B样"
				},
				{
					id: 2,
					label: "C/D样"
				},
				{
					id: 3,
					label: "暂停开发"
				},
				{
					id: 4,
					label: "停产"
				},
				{
					id: 5,
					label: "SOP"
				}
			],
			queryparam: {
				productCates: [], //产品分类
				cates: [],
				states: [],
				depts:[],
				keyword: null,
				projectId: null
			},
			loading: true,
			columns: [
				{
					title: "序号",
					width: 100,
					align: "center",
					dataIndex: "no",
					customRender: (text, record, index) => {
						if (record.productOrProject == 1) {
							return `${index + 1}`
						}
						return ""
					}
				},
				{
					title: "产品名称",
					align: "center",
					width: 100,
					dataIndex: "productProjectName",
					scopedSlots: {
						customRender: "productProjectName"
					}
				},
				{
					title: "项目名称",
					width: 100,
					align: "center",
					dataIndex: "projectName"
				},

				{
					title: "客户",
					width: 100,
					align: "center",
					dataIndex: "customer"
				},

				{
					title: "项目阶段",
					width: 100,
					align: "center",
					dataIndex: "mstatus",
					scopedSlots: {
						customRender: "mstatus"
					}
				},
				{
					title: `CW${getWeek(new Date()) - 1}周进展`,
					align: "center",
					children: [
						{
							title: "产品",
							align: "center",
							dataIndex: "productState",
							scopedSlots: {
								customRender: "productState"
							}
						},
						{
							title: "质量",
							align: "center",
							dataIndex: "qualityState",
							scopedSlots: {
								customRender: "qualityState"
							}
						},
						{
							title: "交付",
							align: "center",
							dataIndex: "summitState",
							scopedSlots: {
								customRender: "summitState"
							}
						},
						{
							title: "产业化",
							align: "center",
							dataIndex: "industryState",
							scopedSlots: {
								customRender: "industryState"
							}
						}
					]
				},
				{
					title: "操作",
					align: "center",
					width: 45,
					dataIndex: "action"
				}

				/* {
                    title: '产品经理',
                    align: 'center',
                    dataIndex: 'productManager',
                },
                {
                    title: 'RPM',
                    align: 'center',
                    dataIndex: 'researchProjectManager',
                },
                {
                    title: '定点状态',
                    align: 'center',
                    dataIndex: 'fixedState',
                    scopedSlots: {
                        customRender: 'fixedState'
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    dataIndex: 'action',
                }, */
				// {
				// 	title: "提出时间",
				// 	align: "center",
				// 	dataIndex: "weektime",
				// 	scopedSlots: {
				// 		customRender: "weektime"
				// 	}
				// },
				// {
				// 	title: "产品状态",
				// 	align: "center",
				// 	dataIndex: "state",
				// 	customRender: text => statusType[text]
				// },
				// {
				// 	title: "产品分类",
				// 	align: "center",
				// 	dataIndex: "productClassification",
				// 	customRender: text => classType[text]
				// },
				// {
				// 	title: "产品类别",
				// 	align: "center",
				// 	dataIndex: "productCate",
				// 	scopedSlots: {
				// 		customRender: "productCate"
				// 	}
				// },
				// {
				// 	title: "项目等级",
				// 	align: "center",
				// 	dataIndex: "productLevel",
				// 	customRender: (text, record, index) => {
				// 		return text + "级"
				// 	}
				// },
			],
			loadData: [],
			totalData: [],
			cate: [],
			tips: "",
			parentId: null,
			cateId: null,
			projectId: null,
			weekTime: getWeek(new Date()) - 1
		}
	},
	props: {
		// 表格高度
		tableHeight: {
			type: Number,
			default: 0
		},
		// 表格滚动高度
		scrollHeigh: {
			type: Number,
			default: 0
		}
	},
	watch: {
		loadData(newVal, oldVal) {
			if (this.loadData.length > 0) {
				// 动态修改--height的值
				document.documentElement.style.setProperty(`--height`, `${this.scrollHeigh}px`)
			} else {
				// 动态修改--height的值
				document.documentElement.style.setProperty(`--height`, "50px")
			}
		}
	},
	methods: {
		callGetDepartmentCateTree() {
			this.confirmLoading = true
			getCateTree({
				fieldName: "department"
			})
				.then(res => {
					if (res.success) {
						let cate = []
						for (const item of res.data) {
							let $item = {
								id: parseInt(item.value),
								label: item.title
							}
							
							cate.push($item)
						}
						this.departmentCateTreeData = cate
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
					this.confirmLoading = false
				})
				.catch(err => {
					this.confirmLoading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		},
		expandIcon(props) {
			if (props.record.children && props.record.children.length > 0) {
				if (props.expanded) {
					return (
						<a-icon
							type="up"
							onClick={e => {
								props.onExpand(props.record, e)
							}}
						/>
					)
				} else {
					return (
						<a-icon
							type="down"
							onClick={e => {
								props.onExpand(props.record, e)
							}}
						/>
					)
				}
			} else {
				return <span style="padding-left: 21px;" />
			}
		},
		query() {
			if (this.parentId != null) {
				let index = this.queryparam["cates"].findIndex(e => e == this.parentId)
				this.queryparam["cates"].splice(index, 1)
				this.parentId = null
			}
			if (this.cateId != null) {
				let index = this.queryparam["cates"].findIndex(e => e == this.cateId)
				this.queryparam["cates"].splice(index, 1)
				this.cateId = null
			}

			if (this.projectId != null) {
				this.queryparam.projectId = null
				this.projectId = null
			}
			this.callFilter()
		},
		change() {
			this.callFilter()
		},
		resetquery() {
			this.queryparam = {
				productCates: [],
				cates: [],
				states: [],
				keyword: null,
				projectId: null
			}
			this.weekTime = getWeek(new Date()) - 1
			this.columns[5].title = `CW${this.weekTime}周进展`
			let filterData = JSON.parse(JSON.stringify(this.totalData))
			this.loadData = filterData
		},
		callGetTree() {
			this.loading = true
			getCatesTree()
				.then(res => {
					if (res.result) {
						let cate = []
						for (const item of res.data) {
							let $item = {
								id: parseInt(item.value),
								label: item.title
							}
							if (item.children) {
								$item.children = []
								for (const _item of item.children) {
									$item.children.push({
										id: parseInt(_item.value),
										label: _item.title
									})
								}
							}
							cate.push($item)
						}
						this.cate = cate
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
					this.loading = false
				})
				.catch(err => {
					this.loading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		},

		// 数据筛选
		callFilter() {
			let filterData = JSON.parse(JSON.stringify(this.totalData))

			// 产品分类
			if (this.queryparam["productCates"].length > 0) {
				filterData = filterData.filter(
					item => this.queryparam["productCates"].join(",").indexOf(parseInt(item.productClassification)) > -1
				)
			}

			if (this.queryparam.projectId) {
				if (this.queryparam["cates"].length > 0) {
					filterData = filterData.filter(
						item =>
							this.queryparam["cates"].indexOf(parseInt(item.cateId)) > -1 ||
							this.queryparam["cates"].indexOf(parseInt(item.catepid)) > -1
					)
				}
				filterData = filterData.filter(item => item.issueId == this.queryparam.projectId)
				this.loadData = filterData
				return
			}

			if (this.queryparam["cates"].length > 0) {
				filterData = filterData.filter(
					item =>
						this.queryparam["cates"].indexOf(parseInt(item.cateId)) > -1 ||
						this.queryparam["cates"].indexOf(parseInt(item.catepid)) > -1
				)
			}
			if (this.queryparam["states"].length > 0) {
				filterData = filterData.filter(item => this.queryparam["states"].indexOf(parseInt(item.state)) > -1)
			}
			if (this.queryparam["depts"].length > 0) {
				filterData = filterData.filter(item => this.queryparam["depts"].indexOf(parseInt(item.deptId)) > -1)
			}
			if (this.queryparam.keyword != null && this.queryparam.keyword != "") {
				filterData = filterData.filter(
					item => item.productProjectName.toLowerCase().indexOf(this.queryparam.keyword.toLowerCase()) > -1
				)
			}

			if (this.queryparam.startDate != null) {
				filterData = filterData.filter(
					item =>
						Date.parse(item.initiationDate) >= this.queryparam.startDate &&
						Date.parse(item.initiationDate) < this.queryparam.endDate
				)
			}

			this.loadData = filterData
		},
		dateChange(date, dateString) {
			if (dateString[0] != null && dateString[0] != "") {
				this.queryparam.startDate = Date.parse(dateString[0])
			} else {
				this.queryparam.startDate = null
			}
			if (dateString[1] != null && dateString[1] != "") {
				this.queryparam.endDate = Date.parse(dateString[1])
			} else {
				this.queryparam.endDate = null
			}
			this.callFilter()
		},
		// 选择周
		handleSelectWeek(date, dateString) {
			const week = dateString.slice(dateString.indexOf("周") - 2, dateString.indexOf("周"))
			this.weekTime = week
			this.columns[5].title = `CW${this.weekTime}周进展`
			this.callDashboardProcess(this.weekTime)
		},
		/* expandIcon(props) {
            console.log(props)
            if (props.record.children && props.record.children.length > 0) {
                if (props.expanded) {
                    return (
                        <a-icon type='up' onClick={(e) => {
                            props.onExpand(props.record, e);
                        }} />
                    );
                } else {
                    return (
                        <a-icon type='down' onClick={(e) => {
                            props.onExpand(props.record, e);
                        }} />
                    );
                }
            } else {
                return <span style="padding-left: 21px;" />
            }
        }, */
		callDashboardProcess(weekTime) {
			this.loading = true
			dashboardProcess({ weekTime })
				.then(res => {
					if (res.success) {
						this.totalData = JSON.parse(JSON.stringify(res.data))
						this.loadData = res.data
						if (this.$route.query.parentId) {
							this.projectId = parseInt(this.$route.query.parentId)
							this.queryparam["cates"].push(parseInt(this.$route.query.parentId))
						}
						if (this.$route.query.cateId) {
							this.cateId = parseInt(this.$route.query.cateId)
							this.queryparam["cates"].push(parseInt(this.$route.query.cateId))
						}
						if (this.$route.query.projectId) {
							this.projectId = parseInt(this.$route.query.projectId)
							this.queryparam.projectId = parseInt(this.$route.query.projectId)
						}
						this.callFilter()
					} else {
						this.$message.error(res.message, 1)
					}
					this.loading = false
				})
				.catch(err => {
					this.loading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		},

		// 点击周进展
		handleShowEvolve(value) {
			this.$success({
				title: `${value}`,
				okText: "关闭"
			})
		}
	},
	created() {
		this.callDashboardProcess(this.weekTime)
		this.callGetTree()
		this.callGetDepartmentCateTree()
		// 动态修改--height的值
		document.documentElement.style.setProperty(`--height`, `${this.scrollHeigh}px`)
	}
}
</script>

<style lang="less" scoped>
@import "./productoption.less";

:root {
	--height: 600px;
}

/deep/.ant-table-body {
	height: var(--height) !important;
	overflow-y: scroll;
}

// 表头居中、固定
/deep/.ant-table-thead tr th {
	border: 1px solid #dfdbdb;
	padding: 5px;
}

/deep/.ant-table-thead tr th:first-child {
	border-left: none;
}
/deep/.ant-table-thead tr th:last-child {
	border-right: none;
}
</style>
