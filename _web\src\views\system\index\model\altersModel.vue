<template>
	<a-modal
		:title="`${title}设计进度`"
		:visible="true"
		:width="700"
		:centered="true"
		okText="查看详情"
		cancelText="关闭"
		@cancel="handleModelCancel"
		@ok="handleModelOk"
	>
		<div class="model-wrapper">
			<a-spin :spinning="spinning">
				<a-icon slot="indicator" type="loading" style="font-size: 24px" spin />
				<div class="chart-wrapper">
					<a-icon @click="handleShow" class="icon" :class="{ 'no-point': isTotalChart }" type="left" />
					<div v-show="isTotalChart" class="chart_table" ref="total"></div>
					<div v-show="!isTotalChart" class="chart_table" ref="abnormal"></div>
					<a-icon @click="handleShow" class="icon" :class="{ 'no-point': !isTotalChart }" type="right" />
				</div>
			</a-spin>
		</div>
	</a-modal>
</template>

<script>
import { getProjectAltersBydept } from "@/api/modular/system/chartManage"

import { title } from "process"

export default {
	props: {
		title: {
			type: String,
			default: ""
		}
	},
	data() {
		return {
			bomAlter: {},
			mIAlter: {},
			mapAlter: {},
			stateAlterTypeCount: {},
			depts: [],
			stateCount: {},

			isTotalChart: true,
			spinning: false
		}
	},
	watch: {
		title() {
			this.callProjectAlterBydept()
		}
	},
	mounted() {
		this.callProjectAlterBydept()
	},
	methods: {
		// 获取数据
		callProjectAlterBydept() {
			this.spinning = true
			getProjectAltersBydept({ dept: this.title })
				.then(res => {
					if (res.result) {
						this.bomAlter = res.data.BOMCOUNT ? res.data.BOMCOUNT : {}
						this.mIAlter = res.data.MICOUNT ? res.data.MICOUNT : {}
						this.mapAlter = res.data.MAPCOUNT ? res.data.MAPCOUNT : {}
						this.depts = res.data.depts
						this.stateCount = res.data.stateCount ? res.data.stateCount : {}
						this.stateAlterTypeCount = res.data.stateAlterTypeCount ? res.data.stateAlterTypeCount : {}

						this.$nextTick(() => {
							this.initTotal()
							this.initAbnormal()
						})
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
				})
				.catch(err => {
					this.$message.error("错误提示：" + err.message, 1)
				})
				.finally(() => {
					this.spinning = false
				})
		},

		/**
		 * echarts 图表
		 */

		initTotal() {
			let chart = this.echarts.init(this.$refs.total)
			chart.clear()

			let axis = []
			let dataBom = []
			let dataMi = []
			let dataMap = []

			for (const key in this.bomAlter) {
				axis.push(key)
				dataBom.push(this.bomAlter[key])
				dataMi.push(this.mIAlter[key])
				dataMap.push(this.mapAlter[key])
			}

			const options = {
				legend: {
					itemWidth: 8,
					itemHeight: 8,
					bottom: "1%"
				},
				grid: {
					left: "3%",
					right: "4%",
					bottom: "12%",
					containLabel: true
				},
				xAxis: {
					type: "category",
					data: [Object.keys(this.bomAlter)[0], Object.keys(this.bomAlter)[1]],
					axisLabel: {
						interval: 0,
						textStyle: {
							color: "#000",
							fontSize: "10"
						}
					}
				},
				yAxis: {
					show: false
				},
				series: [
					{
						name: "BOM",
						type: "bar",
						label: {
							show: true,
							position: "top"
						},
						itemStyle: {
							color: "#5b9bd5",
							barBorderRadius: [5, 5, 0, 0]
						},
						barWidth: "10%",
						data: dataBom
					},
					{
						name: "MI",
						type: "bar",
						label: {
							show: true,
							position: "top"
						},
						itemStyle: {
							color: "#ed7d31",
							barBorderRadius: [5, 5, 0, 0]
						},
						barWidth: "10%",
						data: dataMi
					},
					{
						name: "图纸",
						type: "bar",
						label: {
							show: true,
							position: "top"
						},
						itemStyle: {
							color: "#a5a5a5",
							barBorderRadius: [5, 5, 0, 0]
						},
						barMaxWidth: "10%",
						data: dataMap
					}
				]
			}
			chart.setOption(options)
			chart.resize()
		},
		initAbnormal() {
			let chart = this.echarts.init(this.$refs.abnormal)

			let map = {
				0: 2,
				1: 3,
				2: 4
			}

			let depts = this.depts
			let stateCount = this.stateCount
			let stateAlterCount = this.stateAlterTypeCount

			chart.clear()
			let option = {
				color: ["#5b9bd5", "#4472c4", "#a5a5a5", "#f6d530", "#ed7d31"],
				title: {
					show: false
				},
				tooltip: {
					trigger: "axis",
					axisPointer: {
						type: "shadow"
					},
					formatter(params) {
						let bomNum = stateAlterCount[params[0].seriesName + "#" + map[params[0].dataIndex] + "$2"]
							? stateAlterCount[params[0].seriesName + "#" + map[params[0].dataIndex] + "$2"]
							: 0
						let mINum = stateAlterCount[params[0].seriesName + "#" + map[params[0].dataIndex] + "$0"]
							? stateAlterCount[params[0].seriesName + "#" + map[params[0].dataIndex] + "$0"]
							: 0
						let tzNum = stateAlterCount[params[0].seriesName + "#" + map[params[0].dataIndex] + "$1"]
							? stateAlterCount[params[0].seriesName + "#" + map[params[0].dataIndex] + "$1"]
							: 0
						return (
							params[0].seriesName +
							params[0].name +
							"<br/>" +
							"BOM数量:" +
							bomNum +
							"<br/>" +
							"MI数量:" +
							mINum +
							"<br/>" +
							"图纸数量:" +
							tzNum
						)
					}
				},
				legend: {
					itemWidth: 8,
					itemHeight: 8,
					bottom: "1%"
				},
				grid: {
					left: "3%",
					right: "4%",
					top: "4%",
					bottom: "12%",
					containLabel: true
				},
				xAxis: {
					type: "category",
					data: ["A样", "B样", "C样以后"],
					axisLabel: {
						interval: 0,
						textStyle: {
							color: "#000",
							fontSize: "10"
						}
					},
					axisTick: {
						//y轴刻度线
						show: false
					},
					splitLine: {
						//网格线
						show: false
					},
					axisLine: {
						//y轴
						show: true
					}
				},
				yAxis: {
					splitLine: {
						show: false
					},
					axisTick: {
						show: false
					},
					gridIndex: 0,
					axisLine: {
						//y轴
						show: false
					},
					type: "value",
					show: false,
					boundaryGap: [0, 0.01]
				},
				series: []
			}
			for (const item of depts) {
				option.series.push({
					name: item,
					type: "bar",
					barMaxWidth: "22",
					itemStyle: {
						barBorderRadius: [5, 5, 0, 0]
					},
					data: [
						stateCount.hasOwnProperty(item + "#2") ? stateCount[item + "#2"] : 0,
						stateCount.hasOwnProperty(item + "#3") ? stateCount[item + "#3"] : 0,
						stateCount.hasOwnProperty(item + "#4") ? stateCount[item + "#4"] : 0
					]
				})
			}
			chart.setOption(option)
			chart.resize()
		},

		/**
		 * 弹窗事件
		 */
		handleModelCancel() {
			this.$emit("cancel")
			this.isTotalChart = true
		},

		handleModelOk() {
			this.$router.push({
				path: "/project_alter",
				query: {
					dept: this.title
				}
			})
		},

		handleShow() {
			this.isTotalChart = !this.isTotalChart
		}
	}
}
</script>

<style lang="less" scoped>
.chart-wrapper {
	display: flex;
}
.chart-wrapper .chart_table {
	width: 532px;
	height: 350px;
}

.chart-wrapper .icon {
	margin: auto 0;
	font-size: 60px;
	color: #595757;
}

.no-point {
	color: #f2f2f2 !important;
}
</style>
