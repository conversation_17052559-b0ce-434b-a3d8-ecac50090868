<template>
  <a-modal
    title="新增产线"
    :width="500"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="工厂"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-select
            :disabled ="disabled"
						v-decorator="[
              'werkNo',
              {
                rules: [
                  { required: true, message: '请选择工厂'},
                ],
              },
            ]"
						>
						<a-select-option value=''>选择工厂</a-select-option>
						<a-select-option v-for="(item, index) in werks" :key="index" :value="item.werks" >{{item.name1}}</a-select-option>
					</a-select>
        </a-form-item>
        <a-form-item
          label="产线"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入产线名" v-decorator="['lineName', {rules: [{required: true, message: '请输入产线名！'}]}]" />
        </a-form-item>
      </a-form>

    </a-spin>
  </a-modal>
</template>

<script>
import { sysWerklineAdd } from '@/api/modular/system/werklineManage'
export default {
    props:{
      werks:{
        type:Array,
        default:[]
      }
    },
    data () {
      return {
        disabled:false,
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 18 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods:{
        add (record) {
          setTimeout(() => {
            this.form.setFieldsValue(
              {
                werkNo: record ? record.werks : '',
              }
            )
          }, 100)
          this.disabled = record ? true : false
          this.visible = true
        },
        handleCancel () {
            this.disabled = false
            this.form.resetFields()
            this.visible = false
        },
        handleSubmit () {
            const { form: { validateFields } } = this
            this.confirmLoading = true
            validateFields((errors, values) => {
            if (!errors) {
                sysWerklineAdd(values).then((res) => {
                if (res.success) {
                    this.$message.success('新增成功')
                    this.visible = false
                    this.confirmLoading = false
                    this.$emit('ok', values)
                    this.form.resetFields()
                } else {
                    this.$message.error('新增失败：' + res.message)
                }
                }).finally((res) => {
                this.confirmLoading = false
                })
            } else {
                this.confirmLoading = false
            }
            })
        },
    }
}
</script>

<style>

</style>