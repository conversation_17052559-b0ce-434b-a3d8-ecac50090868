@import './index.less';


.ant-menu-horizontal{
  line-height: 40px;
}

#app {
  height: 100%;

  &.colorWeak {
    filter: invert(80%);
  }
  &.userLayout {
    overflow: auto;
  }
}


.ant-header-fixedHeader {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: 100%;
  transition: width 0.2s;

  /* &.ant-header-side-opened {
    width: calc(100% - 200px);
  }

  &.ant-header-side-closed {
    width: calc(100% - 40px);//calc(100%);//calc(100% - 80px);
  } */
}

.layout.ant-layout {
  height: auto;
  overflow-x: hidden;

  &.mobile,
  &.tablet {
    .ant-layout-content {
      .content {
        margin: 24px 0 0;
      }
    }

    /**
     * ant-table-wrapper
     * 覆盖的表格手机模式样式，如果想修改在手机上表格最低宽度，可以在这里改动
     */
    .ant-table-wrapper {
      .ant-table-content {
        overflow-y: auto;
      }
      .ant-table-body {
        min-width: 800px;
      }
    }
    .topmenu {
      /* 必须为 topmenu  才能启用流式布局 */
      &.content-width-Fluid {
        .header-index-wide {
          margin-left: 0;
        }
      }
    }
  }

  &.mobile {
    .sidemenu {
      .ant-header-fixedHeader {
        &.ant-header-side-opened,
        &.ant-header-side-closed {
          width: 100%;
        }
      }
    }
  }

  &.ant-layout-has-sider {
    flex-direction: row;
  }

  .trigger {
    font-size: 20px;
    line-height: 40px;
    padding: 0 24px;
    cursor: pointer;
    transition: color 0.3s;
    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .topmenu {
    .ant-header-fixedHeader {
      position: fixed;
      top: 0;
      right: 0;
      z-index: 9;
      width: 100%;
      transition: width 0.2s;

      &.ant-header-side-opened {
        width: 100%;
      }

      &.ant-header-side-closed {
        width: 100%;
      }
    }
    /* 必须为 topmenu  才能启用流式布局 */
    &.content-width-Fluid {
      .header-index-wide {
        max-width: unset;
        .header-index-left {
          flex: 1 1 1000px;
          /* justify-content: space-between; */
          .logo{
            margin-left: 25px;
          }
          .ant-menu.ant-menu-horizontal{
            max-width: calc(100vw - 190px - 238px - 25px);
            flex: 1 1 calc(100vw - 190px - 238px - 25px);
          }
        }
        .header-index-right{
          margin-right:25px;
        }
      }

      .page-header-index-wide {
        max-width: unset;
      }
    }
  }

  /* .sidemenu {
    
  } */

  .header {
    height: 40px;
    // padding: 0 12px 0 0;
    background: #fff;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    position: relative;
  }

  .header,
  .top-nav-header-index {
    .user-wrapper {
      float: right;
      height: 100%;

      .action {
        line-height: 40px;
        cursor: pointer;
        padding: 0 12px;
        display: inline-block;
        transition: all 0.3s;
        height: 100%;
        color: rgba(0, 0, 0, 0.65);

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }

        .avatar {
          margin: 15px 8px 15px 0;
          color: #1890ff;
          background: hsla(0, 0%, 100%, 0.85);
          vertical-align: middle;
        }

        .icon {
          font-size: 16px;
          padding: 4px;
        }
      }
    }

    &.dark {
      .user-wrapper {
        .action {
          color: rgba(255, 255, 255, 0.85);
          a {
            color: rgba(255, 255, 255, 0.85);
          }

          &:hover {
            background: rgba(255, 255, 255, 0.16);
          }
        }
      }
    }
  }

  &.mobile,
  &.tablet {
    .top-nav-header-index {
      .header-index-wide {
        .header-index-left {
          .trigger {
            color: rgba(255, 255, 255, 0.85);
            padding: 0 12px;
          }

          .logo.top-nav-header {
            flex: 0 0 56px;
            text-align: center;
            line-height: 58px;
            h1 {
              display: none;
            }
          }
        }
      }

      &.light {
        .header-index-wide {
          .header-index-left {
            .trigger {
              color: rgba(0, 0, 0, 0.65);
            }
          }
        }
      }
    }
  }

  &.tablet {
    // overflow: hidden; text-overflow:ellipsis; white-space: nowrap;
    .top-nav-header-index {
      .header-index-wide {
        .header-index-left {
          .logo > a {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        .ant-menu.ant-menu-horizontal {
          flex: 1 1 auto;
          white-space: normal;
        }
      }
    }
  }

  .top-nav-header-index {
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    position: relative;
    transition: background 0.3s, width 0.2s;

    .header-index-wide {
      max-width: 1600px;
      margin: auto;
      padding-left: 0;
      display: flex;
      height: 40px;

      .ant-menu.ant-menu-horizontal {
        max-width: 835px;
        /* flex: 0 1 835px; */
        border: none;
        height: 40px;
        line-height: 40px;
      }

      .header-index-left {
        flex: 1;
        display: flex;
        /* justify-content: space-between; */
        .logo.top-nav-header {
          flex: 0 0 80px;
          width: 80px;
          height: 40px;
          position: relative;
          line-height: 40px;
          transition: all 0.3s;
          overflow: hidden;
          text-align: center;
          img,
          svg {
            display: inline-block;
            vertical-align: middle;
            height: 32px;
            /* width: 32px; */
          }

          h1 {
            color: #fff;
            display: inline-block;
            vertical-align: top;
            font-size: 16px;
            margin: 0 0 0 12px;
            font-weight: 400;
          }
        }
      }

      .header-index-right {
        /* flex: 0 0 238px; */
        align-self: flex-end;
        height: 40px;
        overflow: hidden;

        .content-box {
          float: right;
          .action {
            max-width: 140px;
            overflow: hidden;
            text-overflow:ellipsis;
            white-space:nowrap;
          }
        }
      }
    }

    &.light {
      background-color: #fff;

      .header-index-wide {
        .header-index-left {
          .logo {
            h1 {
              color: #002140;
            }
          }
        }
      }
    }
  }

  // 内容区
  .layout-content {
    margin: 24px 24px 0px;
    //height: 100%;
    //height: 64px;
    padding: 0 12px 0 0;
  }

  // footer
  .ant-layout-footer {
    padding: 0;
  }
}

.topmenu {
  .page-header-index-wide {
    margin: 0 auto;
  }
}

// drawer-sider 自定义
.ant-drawer.drawer-sider {
  .sider {
    box-shadow: none;
  }

  &.dark {
    .ant-drawer-content {
      background-color: rgb(0, 21, 41);
    }
  }
  &.light {
    box-shadow: none;
    .ant-drawer-content {
      background-color: #fff;
    }
  }

  .ant-drawer-body {
    padding: 0;
  }
}

// 菜单样式
.sider {
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
  position: relative;
  z-index: @ant-global-sider-zindex;
  min-height: 100vh;

  .ant-layout-sider-children {
    overflow-y: hidden;

    &:hover {
      overflow-y: auto;
    }
  }

  &.ant-fixed-sidemenu {
    position: fixed;
    height: 100%;
  }

  // logo区域样式
  .logo {
    position: relative;
    height: 40px;
    padding-left: 24px;
    overflow: hidden;
    line-height: 40px;
    background: #002140;
    transition: all .3s;

    img,
    svg,
    h1 {
      display: inline-block;
      vertical-align: middle;
    }

    img,
    svg {
      height: 32px;
      /* width: 32px; */
    }

    h1 {
      color: #fff;
      font-size: 20px;
      margin: 0 0 0 12px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      font-weight: 600;
      vertical-align: middle;
    }
  }

  &.light {
    background-color: #fff;
    box-shadow: 2px 0px 8px 0px rgba(29, 35, 41, 0.05);

    .logo {
      background: #fff;
      box-shadow: 1px 1px 0px 0px #e8e8e8;

      h1 {
        color: unset;
      }
    }

    .ant-menu-light {
      border-right-color: transparent;
    }
  }
}

// 外置的样式控制
.user-dropdown-menu {
  span {
    user-select: none;
  }
}
.user-dropdown-menu-wrapper.ant-dropdown-menu {
  padding: 4px 0;

  .ant-dropdown-menu-item {
    width: 160px;
  }

  .ant-dropdown-menu-item > .anticon:first-child,
  .ant-dropdown-menu-item > a > .anticon:first-child,
  .ant-dropdown-menu-submenu-title > .anticon:first-child .ant-dropdown-menu-submenu-title > a > .anticon:first-child {
    min-width: 12px;
    margin-right: 8px;
  }
}

// 数据列表 样式
.table-alert {
  margin-bottom: 16px;
}

.table-page-search-wrapper {
  .ant-form-inline {
    .ant-form-item {
      display: flex;
      margin-bottom: 24px;
      margin-right: 0;

      .ant-form-item-control-wrapper {
        flex: 1 1;
        display: inline-block;
        vertical-align: middle;
      }

      > .ant-form-item-label {
        line-height: 32px;
        padding-right: 8px;
        width: auto;
      }
      .ant-form-item-control {
        height: 32px;
        line-height: 32px;
      }
    }
  }

  .table-page-search-submitButtons {
    display: block;
    margin-bottom: 24px;
    white-space: nowrap;
  }
}

.content {
  .table-operator {
    margin-bottom: 18px;

    button {
      margin-right: 8px;
    }
  }
}
