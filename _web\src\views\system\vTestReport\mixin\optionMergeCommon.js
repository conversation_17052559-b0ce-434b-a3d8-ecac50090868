import _ from "lodash";
import pageComponent from "@/views/system/vTestReport/components/pageComponent";
import PreviewDrawer from "@/views/system/vTestReport/components/previewDrawer";
import pbiReturnTop from '@/components/pageTool/components/pbiReturnTop.vue';
import html2canvas from "html2canvas";
import moment from "moment";
import { sysFileInfoUpload } from '@/api/modular/system/fileManage'

export const optionMergeCommon = {
  components: {
    pageComponent,
    PreviewDrawer,
    pbiReturnTop,
  },
  data: function () {
    return {
      editObjList: [],
      titleDataObj: {},
      testCondition: "",
      editObj: "", // 选中的图表类型

      xaxisType: '',

      echartObj: {},

      // 全局X轴最大值、间距
      globalXMax: 0,
      globalXInterval: 0,

      echartsColorList: [
        "#c00000",
        "#0070c0",
        "#808080",
        "#7030a0",
        "#4472c4",
        "#a5a5a5",
        "#ed7d31",
        "#5b9bd5",
        "#70ad47",
        "#000000",
        "#ff9999",
        "#ffc000",
        "#00b050"
      ],
      // 折点类型数组 三角、圆形、矩形、菱形、箭头、图钉、【空心：三角、圆形、矩形、菱形、箭头、图钉】、倒三角、五角星
      echartsSymbolList: [
        'triangle', 'circle', 'rect', 'diamond', 'arrow', 'pin',
        'emptyTriangle', 'emptyCircle', 'emptyRect', 'emptyDiamond', 'emptyArrow', 'emptyPin',
        'path://M0,0 L10,0 L5,10 Z',
        'path://M100,22.4 L78.6,54.6 L44.2,54.6 L72.6,79.4 L62.8,112.6 L100,92.4 L137.2,112.6 L127.4,79.4 L155.8,54.6 L121.4,54.6 Z'
      ],

      firstInit: {},
      drawerVisible: false,
      chartLegendNameListObj: {}, //不同类型图例的汇总  合并
      originalLegent: {}, //原始图例
      originalData: {}, //原始数据
      editData: {}, //编辑数据
      checkObj: {}, //暂时无用属性
      screenImageId:'', //传递回去的图片id
    }

  },
  methods: {

    // 原始数据处理
    _getEchartOriginal(titleData, targetObj, hasYAxisTwo = false) {

      // 模板值
      const templateParam = this.reportChartTemplateList[targetObj].templateParamJson

      if (hasYAxisTwo) {
        // 次Y轴默认初始值
        titleData.yType2 = templateParam.yType2 ?? "value"
        titleData.yMin2 = templateParam.yMin2 ?? (targetObj === 'weight' ? -0.4 : -10)
        titleData.yMax2 = templateParam.yMax2 ?? (targetObj === 'weight' ? 0.6 : 150)
        titleData.yInterval2 = templateParam.yInterval2 ?? (targetObj === 'weight' ? 0.2 : 20)
      }

      return {
        titleTop:templateParam.titleTop ??  10,
        yTitleLetf: templateParam.yTitleLetf ?? 40,
        yTitleRight: templateParam.yTitleRight ?? 40,
        legendBgColor: templateParam.legendBgColor ?? 'none',
        legendOrient: templateParam.legendOrient ??  'vertical',
        legendTop:templateParam.legendTop ??  50,
        legendRight: templateParam.legendRight ?? (hasYAxisTwo ? 90 : 50),
        legendWidth: templateParam.legendWidth ??  20,
        legendHeight: templateParam.legendHeight ?? (targetObj === 'height' ? 7 : 5),
        legendGap: templateParam.legendGap ?? 5,
        legendFontSize: templateParam.legendFontSize ?? 12,
        legendNameType:templateParam.legendNameType ?? 'sampleCode',
        xType: templateParam.xType ?? 'value',
        yType: templateParam.yType ?? 'value',
        yMin: templateParam.yMin ?? (targetObj === 'voltage' ? 3000 : 70),
        yMax: templateParam.yMax ?? (targetObj === 'voltage' ? 4500 : 110),
        yInterval: templateParam.yInterval ?? (targetObj === 'voltage' ? 300 : 5),
        gridTop: templateParam.gridTop ??  40,
        gridLeft: templateParam.gridLeft ??  80,
        gridRight: templateParam.gridRight ??  (hasYAxisTwo ? 80 : 40),
        gridBottom: templateParam.gridBottom ??  70,
        ...titleData,
      }
    },
    _handleEchartData(targetObj, xaxisType = '', yAxisOneList, yAxisTwoList = [], hasYAxisTwo = false) {
      // 模板数据
      const templateParam = this.reportChartTemplateList[targetObj].templateParamJson
      const originalParam = this.reportChartTemplateList[targetObj].originalParamJson

      yAxisOneList = Array.isArray(yAxisOneList) ? yAxisOneList : []

      let titleData = this._getEchartOriginal(this.titleDataObj[targetObj], targetObj, hasYAxisTwo)

      // 5个图需要计算Y轴最大最小值及左边长度
      if (['innerres','height','volume','weight','isolateres'].includes(targetObj)) {
        let yAxisOneDataList = []
        yAxisOneList.forEach(seriesItem => {
          yAxisOneDataList.push(...seriesItem.data.map(mapItem => Number(mapItem[1])))
        })

        const rangeValue = this._getYAxisRadius(Math.max.apply(null, yAxisOneDataList), Math.min.apply(null, yAxisOneDataList))
        let yMax = rangeValue[0]
        let yMin = rangeValue[1]
        let yInterval = null

        if (targetObj === 'weight') {
          // 例如：重量最大值135.5，向下取整得到130，间隔为10，5个间隔，上2下3，150 140 130 120 110 100
          const number = Math.max.apply(null, yAxisOneDataList)
          const value = isFinite(number) && typeof number === 'number' ? Math.floor(number / 10) * 10 : 0
          yMax = value + 20
          yMin = value - 30
          yInterval = 10
        }

        const valueLength = yMax.toString().length * 7 + (yMax >= 4 ? 0 : 9)

        titleData.yMax = templateParam.yMax ?? yMax
        titleData.yMin = templateParam.yMin ?? yMin
        if (yInterval === null) {
          delete titleData.yInterval
        } else {
          titleData.yInterval = templateParam.yInterval ?? yInterval
        }
        titleData.yTitleLetf = templateParam.yTitleLetf ??  valueLength + 15
        titleData.gridLeft = templateParam.gridLeft ??  valueLength + 15 + 35
      }

      let seriesList = []

      let lineColorObj = {} // 折线颜色
      let lineSymbolObj = {} // 折点类型

      // 定义通用值
      const normalData = {...titleData,series:[]}
      const normalLegent = []

      const legendNameType = titleData.legendNameType || 'sampleCode'

      for (let i = 0; i < yAxisOneList.length; i++) {
        let sizeType = yAxisOneList[i].sizeType
        let sampleCode = yAxisOneList[i].sampleCode

        let yAxisOneLegendId = (targetObj === 'height' ? sizeType + '-' : '') + sampleCode
        let yAxisOneLegendName = (targetObj === 'height' ? sizeType + '-' : '') + yAxisOneList[i][legendNameType]
        let templateContent = (templateParam.checkData && templateParam.checkData.length !== 0) ? (templateParam.checkData.filter(item => item.id === sampleCode)[0] || {}) : {}

        this.chartLegendNameListObj[targetObj].push({
          sampleCode:  (targetObj === 'height' ? sizeType + '-' : '') + sampleCode,
          batteryCode: (targetObj === 'height' ? sizeType + '-' : '') + yAxisOneList[i].batteryCode
        })

        if (targetObj === 'height') {
          if (!lineColorObj.hasOwnProperty(sizeType)) {
            lineColorObj[sizeType] = this.echartsColorList[Object.keys(lineColorObj).length % this.echartsColorList.length]
          }
          if (!lineSymbolObj.hasOwnProperty(sampleCode)) {
            lineSymbolObj[sampleCode] = this.echartsSymbolList[Object.keys(lineSymbolObj).length % this.echartsSymbolList.length]
          }
        } else {
          if (!lineColorObj.hasOwnProperty(sampleCode)) {
            lineColorObj[sampleCode] = this.echartsColorList[Object.keys(lineColorObj).length % this.echartsColorList.length]
          }
        }

        const temColor = targetObj === 'height' ? lineColorObj[sizeType] : lineColorObj[sampleCode]
        const temSymbol = targetObj === 'height' ? lineSymbolObj[sampleCode] : 'rect'

        const seriesTemplate = {
          name: yAxisOneLegendName,
          type: 'line',
          sampling: 'lttb',
          large: true,
          barGap: 0,
          symbol: templateContent.symbol ?? temSymbol,
          symbolSize: templateContent.symbolSize ?? (targetObj === 'height' ? 7 : 5),
          markPoint: {
            data: []
          },
        }
        const seriesOriginalTemplate = {
          name: yAxisOneLegendName,
          index: i + 1,
          soc: yAxisOneLegendName,
          type: 'line',
          sampling: 'lttb',
          large: true,
          barGap: 0,
        }
        const originalSeries = [{         //小原始值
          id: yAxisOneLegendId,
          lineType: templateContent.lineType ?? 'solid',
          synchronization: templateContent.synchronization ?? seriesList.length,
          symbol: templateContent.symbol ?? temSymbol,
          symbolSize: templateContent.symbolSize ?? targetObj === 'height' ? 7 : 5,
          maxPoint: templateContent.maxPoint ?? false,
          minPoint: templateContent.minPoint ??  false,
          connectNulls: Boolean(Number(templateContent.connectNulls))  ??  false,
          lineWidth: templateContent.lineWidth ?? 1,
          lineColor: templateContent.lineColor ?? temColor,
          itemColor: templateContent.itemColor ?? temColor,
          dataName:yAxisOneLegendName + '-' + (titleData.tooltipPrefix1 || ''),
          ...seriesOriginalTemplate
        }]

        // 判断是否有模板，并且模板是否有该值
        if(templateParam.legendData?.legendList === undefined || templateParam.legendData.legendList.includes(yAxisOneLegendName)){
          if(templateContent.connectNulls){
            seriesTemplate.connectNulls = Boolean(Number(templateContent.connectNulls))
          }
          if(templateContent.maxPoint){
            seriesTemplate.markPoint.data.push({type:'max',name:'max'})
          }
          if(templateContent.minPoint){
            seriesTemplate.markPoint.data.push({type:'min',name:'min'})
          }
          
          seriesList.push({
            ...seriesTemplate,
            id: yAxisOneLegendId,
            lineStyle: {
              width: templateContent.lineWidth ?? 1,
              type: templateContent.lineType ?? 'dashed',
              color: templateContent.lineColor ?? temColor
            },
            itemStyle: {
              color: templateContent.itemColor ?? temColor
            },
            data: yAxisOneList[i].data.map((item, index) => {
              
              let lineName1 = titleData.tooltipPrefix1 || ''
              if (['voltage','innerres','height','volume','weight','isolateres'].includes(targetObj)) {
                lineName1 = item[2] + (lineName1 ? '-' + lineName1 : '')
              }
              return {id: index, value: xaxisType === 'cycle' || xaxisType === '' ? item : [item[2], item[1]], lineName: lineName1, unit: titleData.tooltipUnit1}
            }),
          })
        }
        if (yAxisTwoList.length > 0) {
          let templateContent = (templateParam.checkData && templateParam.checkData.length !== 0) ? (templateParam.checkData.filter(item => item.id === yAxisTwoList[i].sampleCode + 'two')[0] || {}) : {}
          if(templateParam.legendData?.legendList === undefined || templateParam.legendData.legendList.includes(yAxisOneLegendName)){
            seriesList.push(
              {
                id: yAxisTwoList[i].sampleCode + 'two',
                yAxisIndex: 1,
                lineStyle: {
                  width: templateContent.lineWidth ?? 1,
                  type: templateContent.lineType ?? 'dashed',
                  color: templateContent.lineColor ?? temColor
                },
                itemStyle: {
                  color: templateContent.itemColor ?? temColor
                },
                data: yAxisTwoList[i].data.map((item, index) => {
                  let lineName2 = titleData.tooltipPrefix2
                  if (['voltage','innerres','height','volume','weight','isolateres'].includes(targetObj)) {
                    lineName2 = item[2] + (lineName2 ? '-' + lineName2 : '')
                  }
                  return {id: index, value: xaxisType === 'cycle' || xaxisType === '' ? item : [item[2], item[1]], lineName: lineName2, unit: titleData.tooltipUnit2}
                }),
                ...seriesTemplate,
              }
            )
          }
          originalSeries.push(
            {
              id: yAxisTwoList[i].sampleCode + 'two',
              lineType: templateContent.lineType ?? 'dashed',
              synchronization: templateContent.synchronization ??  seriesList.length,
              symbol: templateContent.symbol ?? temSymbol,
              symbolSize: templateContent.symbolSize ?? targetObj === 'height' ? 7 : 5,
              maxPoint: templateContent.maxPoint ?? false,
              minPoint: templateContent.minPoint ??  false,
              connectNulls: Boolean(Number(templateContent.connectNulls))  ??  false,
              lineWidth: templateContent.lineWidth ?? 1,
              lineColor: templateContent.lineColor ?? temColor,
              itemColor: templateContent.itemColor ?? temColor,
              dataName:yAxisOneLegendName + '-' + (titleData.tooltipPrefix2 || ''),
              ...seriesOriginalTemplate
            }
          )
        }
        normalData.series.push(...originalSeries)
        normalLegent.push(yAxisOneLegendName) 
      }

      if(!this.originalData[targetObj]){
        this.originalData[targetObj] = originalParam ? _.cloneDeep (originalParam) : _.cloneDeep(normalData)
        this.originalData[targetObj].originalSeries = originalParam ? _.cloneDeep(originalParam.originalSeries) : _.cloneDeep(seriesList)
      }

      this.legendOptions[targetObj] = _.cloneDeep(normalLegent)
      this.editData[targetObj] = {
        ..._.cloneDeep(normalData),
        editSeries:_.cloneDeep(seriesList),
        originalSeries:originalParam ? _.cloneDeep(originalParam.originalSeries) : _.cloneDeep(seriesList),
        legend:templateParam.legendData?.legendList ?? _.cloneDeep(normalLegent),
        legendSort:templateParam.legendData?.legendSort ??  _.cloneDeep(normalLegent),
        legendRevealList:templateParam.legendData?.legendRevealList ??  _.cloneDeep(normalLegent).slice(0, 6),
        legendEditName:templateParam.legendData?.legendEditName ??  _.cloneDeep(normalLegent).map(v => {
          return {id: v,originName: v, previousName: '', newName: '', isReset: false}
        }),
        allData:templateParam.allData ?? {},
      }

      //修改this.editData的值，把之前修改过的值同步给this.editData
      for (let key of Object.keys(templateParam)) {
        if(['allData','checkData'].includes(key)) continue
        if(key === 'legendData'){
          for (let key1 of Object.keys(templateParam.legendData)) {
            if(templateParam.legendData?.[key1] !== undefined){
              this.editData[targetObj][key1] = templateParam.legendData[key1]
            } 
          }
        }else{
          this.editData[targetObj][key] = templateParam[key]
        }          
    }

      return seriesList
    },
    _handleEchartOptions(targetObj, seriesList, hasYAxisTwo = false) {
      // 模板数据
      const templateParam = this.reportChartTemplateList[targetObj].templateParamJson
      const originalParam = this.reportChartTemplateList[targetObj].originalParamJson

      const originalObj = this.originalData[targetObj] 
      const firstItem = this.firstInit[targetObj] && !originalParam
      const editItem = this.editData[targetObj]

      const options = {
        textStyle: {
          fontFamily: "Times New Roman"
        },
        backgroundColor: '#ffffff',
        animationDuration: 2000,
        title: {
          text: firstItem? originalObj.chartTitle : editItem.chartTitle,
          left: 'center',
          top: firstItem? originalObj.titleTop : editItem.titleTop,
          textStyle: {
            fontSize: 16,
            fontWeight: 500,
            color: "#000"
          }
        },
        grid: {
          show: true,
          top: firstItem? originalObj.gridTop : editItem.gridTop,
          left: firstItem? originalObj.gridLeft : editItem.gridLeft,
          right: firstItem? originalObj.gridRight : editItem.gridRight,
          bottom: firstItem? originalObj.gridBottom : editItem.gridBottom,
          borderWidth: 0.5,
          borderColor: "#ccc"
        },
        tooltip: {
          trigger: "axis",
          confine: true,
          enterable: true,
          hideDelay: 300,
          extraCssText: 'max-height: 400px; overflow-y: auto; scrollbar-width: thin; scrollbar-color: #888 #f1f1f1; pointer-events: auto;',
          formatter: function (params) {
            var result = params[0].axisValue // 添加 x 轴的数值

            // 添加 绝对时间
            if (params[0].data.absoluteTime) {
              let absoluteTime = ''
              let date = moment(params[0].data.absoluteTime, 'YYYY/MM/DD HH:mm:ss.SSS')
              absoluteTime = date.isValid() ? date.format('YYYY-MM-DD HH:mm:ss') : params[0].data.absoluteTime
              result += '<div style="width:20px;display: inline-block;"></div>' + absoluteTime + "<br>"
            } else if (['voltage','innerres','height','volume','weight','isolateres'].includes(targetObj)) {
              result += "<br>"

              if(params[0].value[2] && params[0].value[3]){
                result += params[0].value[2] + ":" + params[0].value[3] +  "<br>" // 添加 出箱后 时间
              }

              if(params[0].value[2] && !params[0].value[3]){
                result += params[0].value[2] +  "<br>" // 添加 出箱后 时间
              }

              if(params.length > 1 && params[0].value[0] == params[1].value[0] && params[0].seriesId == params[1].seriesId){
                result += params[1].value[2] + ":" + (params[1].value[3]?params[1].value[3]:"" )+  "<br>" // 添加 中检后 时间
              }
            } else {
              result += "<br>"
            }

            // 过滤掉 value[1] 没有值的数据项
            const validParams = params.filter(function (item) {
              return item.value &&
                     item.value[1] !== null &&
                     item.value[1] !== undefined &&
                     item.value[1] !== '' &&
                     item.value[1] !== '-' &&
                     !isNaN(item.value[1])
            })

            validParams.forEach(function (item, dataIndex) {
              result +=
                item.marker +
                item.seriesName +
                (item.data.lineName ? '<div style="width:10px;display: inline-block;"></div>' + item.data.lineName : '') +
                '<div style="width:20px;display: inline-block;"></div>' +
                (item.value[1] >= 10000000000 ? "异常值" : item.value[1]) +
                (item.data.unit || '') +
                "<br>" // 添加每个系列的数值
            })

            // 如果没有有效数据，返回空字符串隐藏tooltip
            if (validParams.length === 0) {
              return ''
            }

            // 直接返回内容，滚动由 extraCssText 控制
            return result
          }
        },   
        legend: {
          data: editItem.legendRevealList,
          backgroundColor: firstItem? originalObj.legendBgColor : editItem.legendBgColor,
          itemWidth: firstItem? originalObj.legendWidth : editItem.legendWidth,
          itemHeight: firstItem? originalObj.legendHeight : editItem.legendHeight,
          itemGap: firstItem? originalObj.legendGap : editItem.legendGap,
          orient: firstItem? originalObj.legendOrient : editItem.legendOrient,
          right: firstItem? originalObj.legendRight : editItem.legendRight,
          top: firstItem? originalObj.legendTop : editItem.legendTop,
          padding: [0, 0],
          textStyle: {
            fontSize: firstItem? originalObj.legendFontSize : editItem.legendFontSize,
            color: "#000000"
          }
        },
        xAxis: [
          {
            name: firstItem? originalObj.XTitle : editItem.XTitle,
            type: firstItem? originalObj.xType : editItem.xType,
            nameLocation: 'middle', // 将名称放在轴线的中间位置
            nameGap: 30,
            nameTextStyle: {
              fontSize: 14, // 可以根据需要调整字体大小
              fontWeight: 500,
              color: "#000000" // 可以根据需要调整字体大小
            },
            axisTick: {show: false},
            axisLabel: {
              show: true,
              width: 0.5,
              fontSize: 14,
              color: "#000000",
              formatter: function (value) {
                return value
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              },
              onZero: false, // 次Y轴为数值轴且包含0刻度, 确保X轴的轴线不在次Y轴的0刻度上
            },
            splitLine: {
              show: true, // 显示分隔线
              lineStyle: {
                type: "solid",
                width: 0.5
              }
            },
            minInterval: 1,
          }
        ],
        yAxis: [
          {
            name: firstItem? originalObj.YTitle : editItem.YTitle,
            type: firstItem? originalObj.yType : editItem.yType,
            nameGap: firstItem? originalObj.yTitleLetf : editItem.yTitleLetf,
            position: 'left',
            min: firstItem? originalObj.yMin : editItem.yMin,
            max: firstItem? originalObj.yMax : editItem.yMax,
            nameLocation: 'middle', // 将名称放在轴线的起始位置
            nameRotate: 90, // 旋转角度，使名称竖排
            nameTextStyle: {
              fontSize: 14, // 可以根据需要调整字体大小
              fontWeight: 500,
              color: "#000000"
            },
            splitLine: {
              show: true,  // 显示分隔线
              lineStyle: {
                type: 'solid',  // 设置分隔线的样式，比如虚线
                width: 0.5
              }
            },
            axisTick: {
              show: false,  // 显示刻度
            },
            axisLabel: {
              show: true,
              width: 0.5,
              fontSize: 14,
              color: "#000000",
              formatter: function (value) {
                return value
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              }
            },
          }
        ],
        series: seriesList
      }

      if (hasYAxisTwo) {
        options.yAxis.push(
          {
            name: firstItem ? originalObj.YTitle2 : editItem.YTitle2,
            type: firstItem ? originalObj.yType2 : editItem.yType2,
            position: 'right',
            min: originalObj.yMin2,
            max: originalObj.yMax2,
            interval: originalObj.yInterval2,
            nameGap: firstItem ? originalObj.yTitleRight : editItem.yTitleRight,
            nameLocation: 'middle', // 将名称放在轴线的起始位置
            nameRotate: 90, // 旋转角度，使名称竖排
            nameTextStyle: {
              fontSize: 14,
              fontWeight: 500,
              color: "#000000" // 可以根据需要调整字体大小
            },
            splitLine: {
              show: true,  // 显示分隔线
              lineStyle: {
                type: 'solid'  // 设置分隔线的样式，比如虚线
              }
            },
            axisTick: {
              show: true,  // 显示刻度
            },
            axisLabel: {
              show: true,
              width: 0.5,
              fontSize: 14,
              color: "#000000",
              formatter: function (value) {
                return value
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              }
            },
          }
        )
      }

      if (originalObj.yInterval) {
        options.yAxis[0].interval = originalObj.yInterval
      }

      // 非首次加载才需要
      //处理图例
      //图例名称
      if (!firstItem || templateParam) {
        const newSeriesList = []
        _.cloneDeep(seriesList).forEach(v => {
          const haveNameList = editItem.legendEditName.filter(filterItem => filterItem.originName === v.name && filterItem.newName)
          v.name = haveNameList.length === 0 ? v.name : haveNameList[0].newName
          newSeriesList.push(v)
        })
        options.series = newSeriesList

        const legend = []
        editItem.legendSort.forEach(v => {
          if (editItem.legend.includes(v) && editItem.legendRevealList.includes(v)) {
            const haveList = editItem.legendEditName.filter(filterItem => filterItem.originName === v && filterItem.newName)
            legend.push(haveList.length === 0 ? v : haveList[0].newName)
          }
        })
        options.legend.data = legend
      }


      // 如果非首次编辑
      // 默认是水平，如果是水平，就不添加，垂直才添加
      if (editItem.legendOrient === 'vertical') {
        options.legend.orient = editItem.legendOrient
      }

      // X轴可能没有最大最小值、间隔
      if (!firstItem && editItem.xInterval && editItem.xType === 'value') {
        options.xAxis[0].min = editItem.xMin
        options.xAxis[0].max = editItem.xMax
        options.xAxis[0].interval = editItem.xInterval
      }
      if (!firstItem && editItem.yType === 'value') {
        options.yAxis[0].min = editItem.yMin
        options.yAxis[0].max = editItem.yMax
        options.yAxis[0].interval = editItem.yInterval
      }
      if (hasYAxisTwo && !firstItem && editItem.yType2 === 'value') {
        options.yAxis[1].min = editItem.yMin2
        options.yAxis[1].max = editItem.yMax2
        options.yAxis[1].interval = editItem.yInterval2
      }
      return options
    },

    // 第一次进行，动态获取XY轴相关数据
    _handleYAxisValue(targetObj, hasYAxisTwo) {
      const xyList = ['x','y','y']
      const property = ['xAxis','yAxis','yAxis']

      // 模板数据
      const templateParam = this.reportChartTemplateList[targetObj].templateParamJson
      const originalParam = this.reportChartTemplateList[targetObj].originalParamJson
      const originalItem = this.originalData[targetObj]
      const editItem = this.editData[targetObj]

      for(let i = 0;i< xyList.length;i++){
        if (!hasYAxisTwo && i === 2)  continue
        const AxisData = this.echartObj[targetObj].getModel().getComponent(property[i], i === 2 ? 1 : 0).axis.scale

        // (i === 2 ? '2' : '') ::判断是不是副轴，副轴尾巴需要加个2

        editItem[xyList[i] + 'Min' + (i === 2 ? '2' : '')] = templateParam[xyList[i] + 'Min' + (i === 2 ? '2' : '')] ?? AxisData._extent[0]
        editItem[xyList[i] + 'Max' + (i === 2 ? '2' : '')] = templateParam[xyList[i] + 'Max' + (i === 2 ? '2' : '')] ?? AxisData._extent[1]
        editItem[xyList[i] + 'Interval' + (i === 2 ? '2' : '')] = templateParam[xyList[i] + 'Interval' + (i === 2 ? '2' : '')] ?? AxisData._interval

        // 避免重复赋值
        if(!originalParam){
          originalItem[xyList[i] + 'Min' + (i === 2 ? '2' : '')] = templateParam[xyList[i] + 'Min' + (i === 2 ? '2' : '')] ?? AxisData._extent[0]
          originalItem[xyList[i] + 'Max' + (i === 2 ? '2' : '')] = templateParam[xyList[i] + 'Max' + (i === 2 ? '2' : '')] ?? AxisData._extent[1]
          originalItem[xyList[i] + 'Interval' + (i === 2 ? '2' : '')] = templateParam[xyList[i] + 'Interval' + (i === 2 ? '2' : '')] ?? AxisData._interval
        }
      }
    },

    // 获得y轴最大最小值
    _getYAxisRadius(originalMax, originalMin) {
      const differenceValue = originalMax - originalMin

      const transferMax = Math.trunc(originalMax + differenceValue).toString().split("")
      const transferMin = Math.trunc(originalMin - differenceValue).toString().split("")

      const newMax = Number(transferMax.map((mapItem, mapIndex) => mapIndex == 0 ? (Number(mapItem) + 1).toString() : '0').join(""))
      const newMin = Number(transferMin.map((mapItem, mapIndex) => mapIndex == 0 ? mapItem : '0').join(""))

      return [newMax, newMin]
    },
    handleValueLength(data) {
      const element = document.getElementById('revealText12')
      element.innerHTML = data
      element.style.fontSize = '12px'
      element.offsetWidth
      return element.clientWidth
    },

    // 在线编辑图表
    handleEditEcharts(target) {
      this.editObj = target
      this.drawerVisible = true
    },

    // 生成
    async handleDrawerSubmit(value)   {

      const isEdit = !!value.targetEditObj
      const templateParam = this.reportChartTemplateList[this.editObj].templateParamJson
      // 数据编辑
      if(isEdit && value.targetEditObj !== 'legendNameType'){
        if (value.targetEditIndex === undefined) {
          this.editData[this.editObj][value.targetEditObj] = value[value.targetEditObj]
        }else if(value.targetEditIndex === 'all' || value.targetEditObj === '"synchronization"' || value.targetEditObj === 'legendEditName'){

        }else {
          this.editData[this.editObj].series[value.targetEditIndex][value.targetEditObj] = value.checkData[value.targetEditIndex][value.targetEditObj]
        }
      }

      if(isEdit){
        if(value.targetEditIndex === undefined){
          if(value.targetEditObj && !['legendList','legendNameType'].includes(value.targetEditObj)){
            templateParam[value.targetEditObj] = value[value.targetEditObj]
          }

          if(value.targetEditObj === 'legendNameType'){
            templateParam.legendNameType = value.legendNameType
            templateParam.checkData = value.checkData
            templateParam.legendData = {
              ...templateParam.legendData,
              legendSort: value.legendSort,
              legendEditName: value.legendEditName,
              legendList: value.legendList,
              legendRevealList: value.legendRevealList,
              legendRevealOptions: value.legendRevealOptions
            }
          }
        }else if(value.targetEditIndex === 'all'){
          for(let i = 0; i < value.checkData.length ; i++){
            if(templateParam.checkData[i] === undefined) templateParam.checkData[i] = {}
            templateParam.checkData[i] = {
              ...templateParam.checkData[i],
              id:value.checkData[i].id,
              [value.targetEditObj]:value.checkData[i][value.targetEditObj]
            }
          }
          templateParam.allData[value.targetEditObj] = value.allData[value.targetEditObj]
        }else if(value.targetEditIndex !== 'legendEditName'){
            let haveIndex = templateParam.checkData.findIndex(item => item.id === value.checkData[value.targetEditIndex].id)
            if(haveIndex === -1){
              templateParam.checkData.push({id:value.checkData[value.targetEditIndex].id})
              haveIndex = templateParam.checkData.length - 1
            } 
            // 如果是同步标签，则需要同步property中的所有属性
            if(value.targetEditObj === 'synchronization'){
              const property = ['maxPoint','minPoint','connectNulls','symbol','symbolSize','itemColor','lineType','lineWidth','lineColor','synchronization']
              for(let i = 0; i < property.length ; i++){
                templateParam.checkData[haveIndex][property[i]] = value.checkData[value.targetEditIndex][property[i]]
              }
            }else{
              templateParam.checkData[haveIndex][value.targetEditObj] = value.checkData[value.targetEditIndex][value.targetEditObj]
            }
          }
      }

      // 数据重置
      if(!isEdit){
        if (value.targetResetIndex === undefined) {
          this.editData[this.editObj][value.targetResetObj] = value[value.targetResetObj]
        } else if (value.targetResetIndex === 'all' || value.targetResetIndex === '"synchronization"' || value.targetResetIndex === 'legendEditName') {
        } else {
          this.editData[this.editObj].series[value.targetResetIndex][value.targetResetObj] = value.checkData[value.targetResetIndex][value.targetResetObj]
        }

        if(value.targetResetIndex === undefined && ['legendList','legendNameType'].includes(value.targetEditObj)){
          templateParam[value.targetResetObj] = value[value.targetResetObj]
        }
      }

      if(!isEdit){
        if(value.targetResetIndex === undefined){
          if(value.targetResetObj && !['legendList','legendNameType'].includes(value.targetResetObj)){
            delete templateParam[value.targetResetObj]
          }
        }else if(value.targetResetIndex === 'all'){
          for(let i = 0; i < value.checkData.length ; i++){
            delete templateParam.checkData[i][value.targetResetObj]
          }
          delete templateParam.allData[value.targetResetObj]
        }else{
          let haveIndex = templateParam.checkData.findIndex(item => item.id === value.checkData[value.targetResetIndex].id)
          delete templateParam.checkData[haveIndex][value.targetResetObj]
        }
      }

      // 执行内容更改
      await this.handleChartContent(value,isEdit)

      // 图例-数据
      if(value.targetEditObj === 'legendList'){
        templateParam.legendData.legendIndeterminate = value.legendIndeterminate
        templateParam.legendData.checkAll = value.checkAll
        templateParam.legendData.legendList = value.legendList
        templateParam.legendData.legendOptions = templateParam.legendData.legendOptions ?? this.legendOptions[this.editObj]
      }

      // 图例-名称
      if(value.targetEditObj === 'legendEditName'){
        templateParam.legendData.legendList = value.legendList // 需同步更名后的数组
        templateParam.legendData.legendEditName = value.legendEditName 
        templateParam.legendData.legendRevealList = value.legendRevealList 

        // 找到改名的那根线，存储修改后的线名称
        const haveIndex =  templateParam.checkData.findIndex(item => item.id === value.checkData[value.targetEditIndex].id)
        if(haveIndex === -1){
          templateParam.checkData.push({id:value.checkData[value.targetEditIndex].id,name:value.checkData[value.targetEditIndex].name})
        }else{
          templateParam.checkData[haveIndex].name=value.checkData[value.targetEditIndex].name
        }

      }

      // 图例-排序
      if(value.targetEditObj === 'legendSort'){
        templateParam.legendData.legendSort = value.legendSort 
      }

       // 图例-显隐
      if(value.targetEditObj === 'legendRevealList'){
        templateParam.legendData.legendRevealIndeterminate = value.legendRevealIndeterminate
        templateParam.legendData.legendRevealcheckAll = value.legendRevealcheckAll
        templateParam.legendData.legendRevealList = value.legendRevealList
        templateParam.legendData.legendRevealOptions = templateParam.legendData.legendRevealOptions
      }


      // 记录数据到后端
      let chartTemplateParams = {}
      // 如果有templateId，则走修改路线，如果没有则走新建路线
      if(!this.reportChartTemplateList[this.editObj].templateId){
        chartTemplateParams = {
          templateName:'报告ID修改默认模板',
          originalParamJson:JSON.stringify(this.originalData[this.editObj]),
          templateParamJson:JSON.stringify(this.reportChartTemplateList[this.editObj].templateParamJson),
          reportId:this.id,
          targetChart:this.editObj 
        }

        if(this.optionKey !== undefined) chartTemplateParams.optionKey = this.optionKey


       this.saveChartTemplate(chartTemplateParams)

      }else{
        chartTemplateParams = {
          id:this.reportChartTemplateList[this.editObj].templateId,
          templateParamJson:JSON.stringify(this.reportChartTemplateList[this.editObj].templateParamJson),
        }

        if(this.optionKey !== undefined) chartTemplateParams.optionKey = this.optionKey

       this.updateChartTemplate(chartTemplateParams)
      }
    },

    handleChartContent(value,isEdit){
      const titleOptions = ['chartTitle', 'titleTop']
      const xAxisOptions = ['XTitle', 'xType', 'xMin', 'xMax', 'xInterval']
      const yAxisOptions = ['YTitle', 'YTitle2', 'yType', 'yType2', 'yMin', 'yMin2', 'yMax', 'yMax2', 'yInterval', 'yInterval2', 'yTitleLetf', 'yTitleRight']
      const gridOptions = ['gridTop', 'gridLeft', 'gridRight', 'gridBottom']
      const legendOptions = ['legendBgColor', 'legendOrient', 'legendTop', 'legendRight', 'legendWidth', 'legendHeight', 'legendGap']
      const legendListOptions = ['legendList', 'legendEditName', 'legendSort', 'legendRevealList']
      const dataLabelOptions = ['maxPoint', 'minPoint', 'connectNulls', 'symbol', 'symbolSize', 'itemColor', 'lineType', 'lineWidth', 'lineColor']

      let targetObj = isEdit ? value.targetEditObj : value.targetResetObj  // 需要修改或者重置的目标值
      let targetContentText = isEdit ? 'targetEditObj' : 'targetResetObj'   // 目标内容文本

      if (legendListOptions.includes(value.targetEditObj)) {
        return this._handleEchartsLegend(value.targetEditObj, value[value.targetEditObj])
      }

      // 标题 修改 targetEditObj  // 标题  重置 targetResetObj
      if (titleOptions.includes(value[targetContentText])) {
        if (value[targetContentText] === 'chartTitle') targetObj = 'text'
        if (value[targetContentText] === 'titleTop') targetObj = 'top'
        return this._handleEchartsNormal('title', targetObj, value[value[targetContentText]])
      }

      // 图例 修改 targetEditObj  // 图例  重置 targetResetObj
      if (legendOptions.includes(value[targetContentText])) {
        if (value[targetContentText] === 'legendBgColor') targetObj = 'backgroundColor'
        if (value[targetContentText] === 'legendOrient') targetObj = 'orient'
        if (value[targetContentText] === 'legendTop') targetObj = 'top'
        if (value[targetContentText] === 'legendRight') targetObj = 'right'
        if (value[targetContentText] === 'legendWidth') targetObj = 'itemWidth'
        if (value[targetContentText] === 'legendHeight') targetObj = 'itemHeight'
        if (value[targetContentText] === 'legendGap') targetObj = 'itemGap'
        return this._handleEchartsNormal('legend', targetObj, value[value[targetContentText]])
      }

      // X轴 修改 targetEditObj  // X轴  重置 targetResetObj
      if (xAxisOptions.includes(value[targetContentText])) {
        if (value[targetContentText] === 'XTitle') targetObj = 'name'
        if (value[targetContentText] === 'xType') targetObj = 'type'
        if (value[targetContentText] === 'xMin') targetObj = 'min'
        if (value[targetContentText] === 'xMax') targetObj = 'max'
        if (value[targetContentText] === 'xInterval') targetObj = 'interval'
        return this._handleEchartsXAxis(targetObj, value[value[targetContentText]])
      }

      // Y轴 修改 targetEditObj  // Y轴  重置 targetResetObj
      if (yAxisOptions.includes(value[targetContentText])) {
        if (value[targetContentText] === 'YTitle' || value[targetContentText] === 'YTitle2') targetObj = 'name'
        if (value[targetContentText] === 'yTitleLetf' || value[targetContentText] === 'yTitleRight') targetObj = 'nameGap'
        if (value[targetContentText] === 'yType' || value[targetContentText] === 'yType2') targetObj = 'type'
        if (value[targetContentText] === 'yMin' || value[targetContentText] === 'yMin2') targetObj = 'min'
        if (value[targetContentText] === 'yMax' || value[targetContentText] === 'yMax2') targetObj = 'max'
        if (value[targetContentText] === 'yInterval' || value[targetContentText] === 'yInterval2') targetObj = 'interval'
        return this._handleEchartsYAxis(value[targetContentText], targetObj, value[value[targetContentText]])
      }

      // 数据标签 修改 targetEditObj // 数据标签  重置 targetResetObj
      if (value.targetEditIndex === 'all' || value.targetResetIndex === 'all') {
        if (value[targetContentText] === 'lineType') targetObj = 'type'
        if (value[targetContentText] === 'lineWidth') targetObj = 'width'
        if (value[targetContentText] === 'itemColor' || value[targetContentText] === 'lineColor') targetObj = 'color'
        return this._handleEchartsAllDataLabel(value[targetContentText], targetObj, value.targetEditIndex === 'all' ? value.allData[value[targetContentText]] : 'resetAll')
      }
      if (dataLabelOptions.includes(value[targetContentText])) {
        if (value[targetContentText] === 'maxPoint') targetObj = 'max'
        if (value[targetContentText] === 'minPoint') targetObj = 'min'
        if (value[targetContentText] === 'lineType') targetObj = 'type'
        if (value[targetContentText] === 'lineWidth') targetObj = 'width'
        if (value[targetContentText] === 'itemColor' || value[targetContentText] === 'lineColor') targetObj = 'color'

        return this._handleEchartsDataLabel(value[targetContentText], targetObj, value.checkData[isEdit ? value.targetEditIndex : value.targetResetIndex][value[targetContentText]], isEdit ? value.targetEditIndex : value.targetResetIndex)
      }

      if (value.targetEditObj === 'synchronization') {
        return this._handleEchartsSynchronization(value.targetEditIndex, value.checkData[value.targetEditIndex].synchronization)
      }

      // 图表位置 修改 targetEditObj  // 图表位置  重置 targetResetObj
      if (gridOptions.includes(value[targetContentText])) {
        if (value[targetContentText] === 'gridTop') targetObj = 'top'
        if (value[targetContentText] === 'gridLeft') targetObj = 'left'
        if (value[targetContentText] === 'gridRight') targetObj = 'right'
        if (value[targetContentText] === 'gridBottom') targetObj = 'bottom'
        return this._handleEchartsNormal('grid', targetObj, value[value[targetContentText]])
      }

      // 修改图例名称
      if (this.editData[this.editObj].legendNameType !== value.legendNameType) {
        this.drawerVisible = false
        this.firstInit[this.editObj] = true
        this.titleDataObj[this.editObj].legendNameType = value.legendNameType
        this.handleInitChart(this.editObj)
      }
    },
    // 重置
    handleDrawerReset() {
      this.$confirm({
        title: '请确认是否重置图表?',
        content: '图表重置后，图表修改内容无法恢复',
        okText: '重置',
        cancelText: '取消',
        onOk:async () => {
          await this.deleteChartTemplate({  reportId:this.$route.query.id,id:this.reportChartTemplateList[this.editObj].templateId,optionKey:this.optionKey,targetChart:this.editObj })
          this.firstInit[this.editObj] = true
          this.drawerVisible = false
          this.titleDataObj[this.editObj].legendNameType = 'sampleCode'
          this.handleInitChart(this.editObj)
          this.$message.success("重置成功")
        },
        onCancel() {}
      });
    },

    // 重新选择模板
    async handleChangeTemplate(targetObj){
      await this.getChartTemplateRelationList(this.$route.query.id,[targetObj])
      this.firstInit[targetObj] = true
      this.drawerVisible = false
      // this.titleDataObj[targetObj].legendNameType = 'sampleCode'
      this.handleInitChart(targetObj)
    },

    // 截取保存模板的图表
    handleScreenshot(){
      const dom = this.optionKey === undefined ? document.getElementById(this.editObj) : document.getElementById(this.optionKey + '-' + this.editObj)
      setTimeout(() => {
        html2canvas(dom, {
          useCORS: true,
          backgroundColor: "#fff"
        }).then(canvas => {
          let canvasImg = canvas.toDataURL("image/png")
          const blob = this.handleB64toBlob(canvasImg.replace("data:image/png;base64,", ""))

          const formData = new FormData()
          formData.append('file', blob)
          sysFileInfoUpload(formData).then((res) => {
            this.screenImageId = res.data
          })
        })  
      },500)
      
    },

    _handleEchartsLegend(targetObj, targetValue, targetIndex) {

      const option = { legend: {}, series: [] }

      let legend = []

      // 名称
      if (targetObj === 'legendEditName') {
        this.editData[this.editObj].legendSort.forEach(v => {
          // 处理 legend
          // 条件1 : 数据勾选
          const conditions1 = this.editData[this.editObj].legend.includes(v)
          // 条件2 : 显隐勾选
          const conditions2 = this.editData[this.editObj].legendRevealList.includes(v)
          // 是否改名 如果改了名就用改的名，如果没改，就用原来的名
          const name = this.editData[this.editObj].legendEditName.filter(filterItem => filterItem.newName && (v === filterItem.originName))
          if (conditions1 && conditions2) legend.push(name.length === 0 ? v : name[0].newName)
        })
        _.cloneDeep(this.editData[this.editObj].editSeries).forEach((v, vIndex) => {
          // 有存在一个电芯有多条线的情况,
          const editName = this.editData[this.editObj].legendEditName.filter(item =>{
            const idLength = item.id.split('').length 
            const newId =  v.id.slice(0,idLength)
            return item.id === newId
          })
          option.series[vIndex] = v
          option.series[vIndex].name = editName[0].newName !== '' ? editName[0].newName : editName[0].originName
        })

        option.legend.data = legend
        return this.echartObj[this.editObj].setOption(option)
      }

      // 数据
      if (targetObj === 'legendList') {
        const seriesList = []
        this.editData[this.editObj].legendSort.forEach(v => {
          // 处理 series
          if (targetValue.includes(v)) {
            let haveList = this.editData[this.editObj].editSeries.filter(filterItem => filterItem.name === v)
            if (haveList.length === 0) {
              const editName = this.editData[this.editObj].legendEditName.filter(findItem => findItem.originName === v || findItem.newName === v)[0]
              haveList = _.cloneDeep(this.editData[this.editObj].originalSeries).filter(filterItem =>{  
                const idLength = editName.id.split('').length
                const newId = filterItem.id.slice(0,idLength)
                return newId === editName.id
              })
              // 同步之前的修改样式
              haveList.forEach(forItem => {
                const have = this.editData[this.editObj].series.filter(filterItem => filterItem.id === forItem.id)

                forItem.name = editName.newName !== "" ? editName.newName : editName.originName
                forItem.symbol = have[0].symbol
                forItem.symbolSize = have[0].symbolSize

                forItem.connectNulls = Boolean(Number(have[0].connectNulls))

                forItem.itemStyle.color = have[0].itemColor

                forItem.lineStyle.type = have[0].lineType
                forItem.lineStyle.width = have[0].lineWidth
                forItem.lineStyle.color = have[0].lineColor

                forItem.markPoint = { data: [] }
                if (have[0].maxPoint) forItem.markPoint.data.push({ type: "max", name: "Max" })
                if (have[0].minPoint) forItem.markPoint.data.push({ type: "min", name: "Min" })
              })
            }
            seriesList.push(...haveList)
          }
          // 处理 legend
          // 条件1 : 数据勾选
          const conditions1 = targetValue.includes(v)
          // 条件2 : 显隐勾选
          const conditions2 = this.editData[this.editObj].legendRevealList.includes(v)

          const name = this.editData[this.editObj].legendEditName.filter(filterItem => filterItem.originName === v && filterItem.newName)
          if (conditions1 && conditions2) legend.push(name.length === 0 ? v : name[0].newName)
        })
        this.editData[this.editObj].legend = targetValue

        this.editData[this.editObj].editSeries = _.cloneDeep(seriesList)   //这个需要保留原值


        // 是否改名
        seriesList.forEach((v, vIndex) => {
          const name = this.editData[this.editObj].legendEditName.filter(filterItem => filterItem.originName === v.name && filterItem.newName)
          seriesList[vIndex].name = name.length === 0 ? v.name : name[0].newName
        })

        // 重新生成图表
        return this._handleInitLegendList(seriesList, legend)
      }

      // 排序
      if (targetObj === 'legendSort') {
        this.editData[this.editObj].legendSort.forEach(v => {
          // 是否改名
          const name = this.editData[this.editObj].legendEditName.filter(filterItem => filterItem.originName === v && filterItem.newName)
          if (this.editData[this.editObj].legendRevealList.includes(v)) legend.push(name.length === 0 ? v : name[0].newName)
        })
      }

      // 显隐
      if (targetObj === 'legendRevealList') {
        this.editData[this.editObj].legendSort.forEach(v => {
          // 是否改名
          const name = this.editData[this.editObj].legendEditName.filter(filterItem => filterItem.originName === v && filterItem.newName)
          if (targetValue.includes(v)) legend.push(name.length === 0 ? v : name[0].newName)
        })
      }

      option.legend.data = legend
      this.echartObj[this.editObj].setOption(option)
    },
    _handleEchartsNormal(editObj, targetObj, targetValue) {
      this.echartObj[this.editObj].setOption({
        [editObj]: {
          [targetObj]: targetValue
        }
      })
    },
    _handleEchartsXAxis(targetObj, targetValue) {
      const option = { xAxis: [{}] }
      // 保留设置用户上一次留下的值,最大最小以及间隔值
      if (targetObj === 'type' && targetValue === 'value' && this.editData[this.editObj].xInterval) {
        option.xAxis[0].min = this.editData[this.editObj].xMin
        option.xAxis[0].max = this.editData[this.editObj].xMax
        option.xAxis[0].interval = this.editData[this.editObj].xInterval
      }
      option.xAxis[0][targetObj] = targetValue;

      // 样式
      option.xAxis[0].axisTick = { show: false }
      option.xAxis[0].axisLabel = { show: true, width: 0.5, fontSize: 14, color: "#000000" }
      option.xAxis[0].axisLine = { show: true, lineStyle: { color: "#ccc", width: 0.5 } }
      option.xAxis[0].splitLine = { show: true, lineStyle: { type: "solid", width: 0.5 } }
      option.xAxis[0].nameTextStyle = { fontSize: 14, fontWeight: 500, color: "#000000" }
      option.xAxis[0].nameLocation = 'middle'
      option.xAxis[0].nameGap = 30

      if (targetObj !== 'name') option.xAxis[0].name = this.editData[this.editObj].XTitle



      this.echartObj[this.editObj].setOption(option)

      // X轴首次由类目轴修改为数值轴，获取最大最小值、间隔
      if (targetObj === 'type' && targetValue === 'value' && !this.editData[this.editObj].xInterval) {
        const XAxis = this.echartObj[this.editObj].getModel().getComponent("xAxis").axis.scale

        this.editData[this.editObj].xMin = XAxis._extent[0]
        this.editData[this.editObj].xMax = XAxis._extent[1]
        this.editData[this.editObj].xInterval = XAxis._interval

        this.originalData[this.editObj].xMin = XAxis._extent[0]
        this.originalData[this.editObj].xMax = XAxis._extent[1]
        this.originalData[this.editObj].xInterval = XAxis._interval
      }
    },
    _handleEchartsYAxis(originalValue, targetObj, targetValue) {
      const option = { yAxis: [{}, { axisLine: { show: true, lineStyle: { color: "#ccc", width: 0.5 } } }] }
      const yAxisOptions = ['YTitle', 'yType', 'yMin', 'yMax', 'yInterval', 'yTitleLetf']
      const targetIndex = yAxisOptions.includes(originalValue) ? 0 : 1
      // 保留设置用户上一次留下的值,最大最小以及间隔值
      if (targetObj === 'type' && targetValue === 'value') {
        option.yAxis[targetIndex].min = this.editData[this.editObj][`yMin${yAxisOptions.includes(originalValue) ? '' : '2'}`]
        option.yAxis[targetIndex].max = this.editData[this.editObj][`yMax${yAxisOptions.includes(originalValue) ? '' : '2'}`]
        option.yAxis[targetIndex].interval = this.editData[this.editObj][`yInterval${yAxisOptions.includes(originalValue) ? '' : '2'}`]
      }
      option.yAxis[targetIndex][targetObj] = targetValue;

      // 样式
      option.yAxis[targetIndex].axisTick = { show: targetObj ? false : true }
      option.yAxis[targetIndex].axisLabel = { show: true, width: 0.5, fontSize: 14, color: "#000000" }
      option.yAxis[targetIndex].axisLine = { show: true, lineStyle: { color: "#ccc", width: 0.5 } }
      option.yAxis[targetIndex].splitLine = { show: true, lineStyle: { type: "solid", width: 0.5 } }
      option.yAxis[targetIndex].nameTextStyle = { fontSize: 14, fontWeight: 500, color: "#000000" }
      option.yAxis[targetIndex].nameLocation = 'middle'
      option.yAxis[targetIndex].nameGap = this.editData[this.editObj][`${yAxisOptions.includes(originalValue) ? 'yTitleLetf' : 'yTitleRight'}`]

      if (targetObj !== 'name') option.yAxis[targetIndex].name = this.editData[this.editObj][`YTitle${yAxisOptions.includes(originalValue) ? '' : '2'}`]

      this.echartObj[this.editObj].setOption(option)
    },
    _handleEchartsDataLabel(originalValue, targetObj, targetValue, targetIndex) {
      const option = { series: [] }
      const arr = ['connectNulls', 'symbol', 'symbolSize']
      const arr1 = ['lineWidth', 'lineType', 'lineColor']

      // 找到要修改的线的位置
      const haveIndex = this.editData[this.editObj].editSeries.findIndex(findItem => findItem.id === this.editData[this.editObj].originalSeries[targetIndex].id)

      // 拿到要修改的那根线
      const newSeries = _.cloneDeep(this.editData[this.editObj].editSeries[haveIndex])

      // 最大值、最小值
      if ((originalValue === 'maxPoint' || originalValue === 'minPoint') && targetValue) {
        newSeries.markPoint.data.push({ type: targetObj, name: targetObj })
      }
      if ((originalValue === 'maxPoint' || originalValue === 'minPoint') && !targetValue) {
        const haveIndex1 = newSeries.markPoint.data.findIndex(findItem => findItem.type === targetObj)
        newSeries.markPoint.data.splice(haveIndex1, 1)
      }

      // 连线、折点类型、折点大小
      if (arr.includes(originalValue)) {
        newSeries[targetObj] = targetObj === 'connectNulls' ? Boolean(Number(targetValue)) : targetValue
      }

      // 折点颜色
      if (originalValue === 'itemColor') {
        newSeries.itemStyle[targetObj] = targetValue
      }

      // 折线类型、折线宽度、折现颜色
      if (arr1.includes(originalValue)) {
        newSeries.lineStyle[targetObj] = targetValue
      }

      // 确认是否更改名称
      const editName = this.editData[this.editObj].legendEditName.filter(item =>{
        const idLength = item.id.split('').length 
        const newId =  newSeries.id.slice(0,idLength)
        return item.id === newId
      })
      newSeries.name = editName[0].newName !== '' ? editName[0].newName : newSeries.name 

      option.series[targetIndex] = newSeries;
      this.editData[this.editObj].editSeries[haveIndex] = newSeries
      this.echartObj[this.editObj].setOption(option)
    },
    _handleEchartsSynchronization(originalValue, targetValue) {
      // originalValue 要修改的原始值   targetValue 需要修改过后的值
      const option = { series: [] }

      const selectIndex = this.editData[this.editObj].editSeries.findIndex(findItem => findItem.id === this.editData[this.editObj].originalSeries[originalValue].id)
      const templateIndex = this.editData[this.editObj].editSeries.findIndex(findItem => findItem.id === this.editData[this.editObj].originalSeries[targetValue].id)

      const newSeries = { ...this.editData[this.editObj].editSeries[selectIndex] }
      const templateSeries = _.cloneDeep(this.editData[this.editObj].editSeries[templateIndex])
      const arr = ['symbol', 'symbolSize', 'markPoint', 'lineStyle', 'itemStyle', 'connectNulls']  // 需要修改的值

      Object.keys(templateSeries).forEach(key => {
        if (arr.includes(key)) {
          newSeries[key] = key === 'connectNulls' ? Boolean(Number(templateSeries[key])) : templateSeries[key]

          if (key === 'lineStyle') {
            this.editData[this.editObj].series[selectIndex].lineType = this.editData[this.editObj].series[templateIndex].lineType
            this.editData[this.editObj].series[selectIndex].lineWidth = this.editData[this.editObj].series[templateIndex].lineWidth
            this.editData[this.editObj].series[selectIndex].lineColor = this.editData[this.editObj].series[templateIndex].lineColor
          } else if (key === 'itemStyle') {
            this.editData[this.editObj].series[selectIndex].itemColor = this.editData[this.editObj].series[templateIndex].itemColor
          } else {
            this.editData[this.editObj].series[selectIndex][key] = this.editData[this.editObj].series[templateIndex][key]
          }
        }
      });
      // 最大最小值
      this.editData[this.editObj].series[selectIndex].maxPoint = this.editData[this.editObj].series[templateIndex].maxPoint
      this.editData[this.editObj].series[selectIndex].minPoint = this.editData[this.editObj].series[templateIndex].minPoint

      option.series[selectIndex] = newSeries;
      this.editData[this.editObj].editSeries[selectIndex] = newSeries
      this.echartObj[this.editObj].setOption(option)

    },
    _handleEchartsAllDataLabel(originalValue, targetObj, targetValue) {

      const option = { series: [] }
      const arr = ['lineWidth', 'lineType', 'lineColor']

      // 拿到所有的线
      const newSeries = [...this.editData[this.editObj].editSeries]

      // 修改值
      newSeries.forEach((v, index) => {
        if (arr.includes(originalValue)) {
          v.lineStyle[targetObj] = targetValue == 'resetAll' ? this.editData[this.editObj].originalSeries.filter(filterItem => filterItem.id === v.id)[0].lineStyle[targetObj] : targetValue
        } else if (originalValue === 'itemColor') {
          v.itemStyle[targetObj] = targetValue == 'resetAll' ? this.editData[this.editObj].originalSeries.filter(filterItem => filterItem.id === v.id)[0].itemStyle[targetObj] : targetValue
        } else {
          v[targetObj] = targetValue == 'resetAll' ? this.editData[this.editObj].originalSeries.filter(filterItem => filterItem.id === v.id)[0][targetObj] : (targetObj === 'connectNulls' ? Boolean(Number(targetValue)) : targetValue)
        }

        // 判断是否改名
        const currentNameList = this.editData[this.editObj].legendEditName.filter(item =>{
          const idLength = item.id.split('').length 
          const newId =  v.id.slice(0,idLength)
          return item.id === newId
        })

        // 需同步最新的名字
        v.name = currentNameList[0]?.newName ? currentNameList[0].newName : v.name

        // 编辑数据更新
        this.editData[this.editObj].series[index][originalValue] = targetValue == 'resetAll' ? this.originalData[this.editObj].series.filter(filterItem => filterItem.id === v.id)[0][originalValue] : targetValue
      })

      option.series = newSeries;
      this.echartObj[this.editObj].setOption(option)
    },

    // 处理用户双击图表
    _handleDblclickEchart(target, topTarget, targetObj) {
      if (topTarget.z === undefined) return
      // Z: 0:坐标轴,3:折线,4:图例,6:标题,50:折点
      this.drawerVisible = true
      this.editObj = targetObj

      switch (topTarget.z) {
        case 0:
          this.$set(this.chartCheckObj[targetObj], 'editObj', 'axis')
          break;
        case 3:
        case 50:
          const axs = target.parent?.parent?.__ecComponentInfo?.index
          this.$set(this.chartCheckObj[targetObj], 'tag', axs)
          this.$set(this.chartCheckObj[targetObj], 'editObj', 'tag')
          break;
        case 4:
          this.$set(this.chartCheckObj[targetObj], 'editObj', 'legend')
          break;
        case 6:
          this.$set(this.chartCheckObj[targetObj], 'editObj', 'title')
          break;
      }

    },


    // 下载
    handleDown(targetObj) {
      const dom = document.getElementById(targetObj)

      let fileName = this.firstInit[targetObj] ? this.originalData[targetObj].chartTitle : this.editData[targetObj].chartTitle
      if (typeof fileName === 'string') {
        fileName = fileName.replaceAll('.', ' ')
      }

      html2canvas(dom, {
        useCORS: true,
        backgroundColor: "#fff"
      }).then(canvas => {
        let canvasImg = canvas.toDataURL("image/png")
        const blob = this.handleB64toBlob(canvasImg.replace("data:image/png;base64,", ""))
        if (window.navigator.msSaveOrOpenBlob) {
          //兼容IE10
          navigator.msSaveBlob(blob, fileName)
        } else {
          const href = URL.createObjectURL(blob)
          const a = document.createElement("a")
          a.style.display = "none"
          a.href = href // 指定下载链接
          a.download = fileName
          a.click() //触发下载
          URL.revokeObjectURL(a.href)
        }
      })
    },
    // base64 转 blob
    handleB64toBlob(b64Data, contentType = null, sliceSize = null) {
      contentType = contentType || "image/png"
      sliceSize = sliceSize || 512
      let byteCharacters = window.atob(b64Data.substring(b64Data.indexOf(",") + 1))
      let byteArrays = []
      for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
        let slice = byteCharacters.slice(offset, offset + sliceSize)
        let byteNumbers = new Array(slice.length)
        for (let i = 0; i < slice.length; i++) {
          byteNumbers[i] = slice.charCodeAt(i)
        }
        var byteArray = new Uint8Array(byteNumbers)
        byteArrays.push(byteArray)
      }
      return new Blob(byteArrays, { type: contentType })
    },

    handleReturnTop() {
      this.$refs.wrapper.scrollTop = 0
    },
  },
}