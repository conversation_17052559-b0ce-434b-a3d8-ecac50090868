<template>
  <div class="pagination-container">
    <div>
      共{{total}}条
    </div>
    <a-pagination size="small" :total="total" :current="current" :pageSize="pageSize" :defaultPageSize="20" show-size-changer show-quick-jumper @change="handleChange"
      @showSizeChange="handleSizeChange" />
  </div>
</template>
<script>
  /*
    template
      <pbiPagination :total="tableTotal" @change="handleChangePagination" @showSizeChange="handleSizeChangePagination"></pbiPagination>
    script
      import pbiPagination from '@/components/pageTool/components/pbiPagination.vue'
      components : {
        pbiPagination
      },
      data(){
        current:0,
        pageSize:0,
        tableTotal:0
      },
      methods:{
       handleChangePagination(value){},
       handleSizeChangePagination(value){}
      }
  */
  import { Pagination } from 'ant-design-vue';
  export default {
    props: {
      total: {
        type: Number,
        default: 0
      }
    },
    data(){
      return{
        current:1,
        pageSize:20,
      }
    },
    components: {
      'a-pagination': Pagination,
    },
    methods: {
      // 页数改变事件
      handleChange(current, pageSize) {
        // current 当前页码
        // pageSize 每页条数
        this.current = current
        this.pageSize = pageSize
        this.$emit('change', { current, pageSize });
      },
      handleSizeChange(current, pageSize) {
        // current 当前页码
        // pageSize 每页条数
        this.current = current
        this.pageSize = pageSize
        this.$emit('showSizeChange', { current, pageSize });
      },
      // 外部修改current和pageSize
      handleWithoutChange(current,pageSize){
        this.current = current
        this.pageSize = pageSize
      }
    }
  }
</script>
<style lang="less" scoped>
  .pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;

    font-size: 12px;
    color: #666;
  }


  /deep/.ant-pagination {
    font-size: 12px;
    color: #666;
    margin: 0 !important;
  }

  /deep/.ant-pagination.mini .ant-pagination-options {
    margin-top: 2px;
  }

  /deep/.ant-select-sm .ant-select-selection--single {
    height: 24px;
  }

  /deep/.ant-select-sm .ant-select-selection__rendered {
    line-height: 22px;
  }

  /deep/.ant-select {
    font-size: 12px;
    color: #666;
  }

  /deep/.ant-select-arrow {
    margin-top: -6px;
  }

  /deep/.ant-pagination.mini .ant-pagination-options-quick-jumper input {
    font-size: 12px;
    text-align: center;
  }
</style>