<template>
  <div>
    <tableIndex
      :pageLevel='1'
      :pageTitleShow = false
      :paginationShow = false
      :loading='loading'
      :otherHeight="parseInt(50)"
    >
      <template #table>
        <ag-grid-vue :style="{height:tableHeight}"
                     class='table ag-theme-balham'
                     :tooltipShowDelay="0"
                     :columnDefs="columns"
                     :rowData='tableData'
                     rowSelection="multiple"
                     :gridOptions="gridOptions"
                     @grid-ready="onGridReady"
                     :defaultColDef='defaultColDef'>
        </ag-grid-vue>
      </template>
    </tableIndex>
    <!-- 预览视频/图片  -->
    <a-drawer
      :bodyStyle="{ height: '100%' }"
      placement="right"
      :closable="false"
      width="70%"
      :visible="filePreviewVisible"
      @close="filePreviewVisible = false"
    >
      <iframe :src="iframeUrl" width="100%" height="100%"></iframe>
    </a-drawer>
  </div>
</template>

<script>
import {
  getAndSetTestReportFile,
  getTestReportByFolderNo,
} from '@/api/modular/system/testProgressManager'
  import { downloadfile1 } from "@/utils/util";
  import { getMinioPreviewUrl } from "@/api/modular/system/fileManage";
  import Vue from "vue";

  export default {
    components: {
      attachment: {
        template: `<div><a @click="params.onGetReport(params.data.attachment, params.data.fileVersion)">
                    {{!params.data.attachment ? '' : params.data.attachment.length > 0 ? params.data.attachment[0].name : ''}}</a></div>`
      },
      dataProcessTable: {
        template: `<div><a @click="params.onGetReport(params.data.dataProcessTable, params.data.fileVersion)">
                    {{!params.data.dataProcessTable ? '' : params.data.dataProcessTable.length > 0 ? params.data.dataProcessTable[0].name : ''}}</a></div>`
      },
    },
    props: {
      width: {
        type: Number,
        default: 0
      },
      padding: {
        type: String,
        default: '8px'
      },
      folderInfo: {
        type: Object
      }
    },
    data() {
      return {
        iframeUrl: '',
        filePreviewVisible: false,
        tableHeight: (document.body.clientHeight - 365) + 'px' ,
        gridApi: null,
        columnApi: null,
        columns: [
          {
            headerName: '序号',
            field: 'index',
            width: 50,
            cellRenderer: function (params) {
              return parseInt(params.node.id) + 1
            }
          },
          {
            headerName: '文档标题',
            width: 400,
            field: 'fileTittle',
            tooltipValueGetter: (p) => p.value,
          },
          {
            headerName: '文档编号',
            width: 180,
            field: 'fileNo',
          },
          {
            headerName: '文档版本',
            width: 100,
            field: 'fileVersion',
          },
          {
            headerName: '签发日期',
            width: 100,
            field: 'signDate',
          },
          {
            headerName: '检测结论',
            width: 100,
            field: 'testConclusion',
          },
          {
            headerName: '检测报告',
            width: 420,
            field: 'attachment',
            tooltipValueGetter: (p) => p.value && p.value.length > 0 ? p.value[0].name : null,
            cellRenderer: "attachment",
            cellRendererParams: { onGetReport: this.getReport },
          },
           {
            headerName: '数据处理表',
            width: 300,
            field: 'dataProcessTable',
            tooltipValueGetter: (p) => p.value && p.value.length > 0 ? p.value[0].name : null,
            minWidth: 300,
            flex: 1,
            cellRenderer: "dataProcessTable",
            cellRendererParams: { onGetReport: this.getReport },
          },
        ],
        tableData: [],
        reportTodoTaskId: null,
        gridOptions: {
          onSelectionChanged: this.onSelectionChanged,
          suppressCellSelection: false
        },
        defaultColDef: {
          filter: false,
          floatingFilter: false,
          editable: false,
        },

        selectedRowKeys: [],
        selectedRows: [],
        loading: false
      }
    },
    created() {
    },
    computed: {
    },
    mounted() {
      this.getTestReportList()
    },
    methods: {
      getTestReportList () {
        getTestReportByFolderNo({ folderNo: this.folderInfo.folderno }).then(res => {
          if (res.success) {
            this.reportTodoTaskId = res.data.id
            this.tableData = res.data.changeHistoryList
            this.tableData.forEach(item => {
              item.attachment = JSON.parse(item.attachment)
              item.dataProcessTable = JSON.parse(item.dataProcessTable)
            })
          } else {
            this.$message.error("错误提示：" + res.message)
          }
        })
      },
      getReport(reportItem, fileVersion) {
        if (!this.reportTodoTaskId) {
          this.$message.error("检测报告Id为空，请联系管理员！")
          return
        }
        getAndSetTestReportFile(reportItem[0], fileVersion, this.reportTodoTaskId).then(res => {
          let fileList = res.data
          let index = reportItem[0].name.lastIndexOf('.')
          let suffix = reportItem[0].name.substring(index)
          if (fileList && fileList.length > 0) {
            // 如果是可预览的
            if (['.xbm', '.tif', '.pjp', '.svgz', '.jpg', '.jpeg', '.ico', '.tiff', '.gif', '.svg', '.jfif', '.webp', '.png', '.bmp', '.pjpeg', '.avif', '.pdf'].some(someItem => {
              return typeof reportItem[0].name === 'string' && suffix.toLowerCase() === someItem
            })) {
              this.iframeUrl = '/api/sysFileInfo/preview?Authorization=Bearer ' + Vue.ls.get('Access-Token') + '&id=' + fileList[0].id
              setTimeout(() => {
                this.filePreviewVisible = true
              }, 500)
            } else {
              // 不可预览就下载
              const a = document.createElement('a')
              a.style.display = 'none'
              a.href = '/api/sysFileInfo/preview?Authorization=Bearer ' + Vue.ls.get('Access-Token') + '&id=' + fileList[0].id
              a.download = reportItem[0].name
              a.click()
            }
          } else {
            this.$message.error("服务器错误，请联系管理员：" + res.message)
          }
        })
      },
      onSelectionChanged(event) {
        // 获取当前选中的行
        // const selectedNodes = this.gridApi.getSelectedNodes();
        // const selectedData = selectedNodes.map(node => node.data);
        // 更新选中的行数据
        // this.selectedRows = selectedData;
        // this.selectedRowKeys = selectedData;
      },
      onGridReady(params) {
        this.gridApi = params.api;
        this.columnApi = params.columnApi;
      },
    }
  }
</script>
<style lang="less" scoped>
  .ant-form-item {

    margin-bottom: 0px;

  }

  .man_button{
    padding-left:11px;width:100%;display:flex;justify-content: space-between;align-items: center;
  }

  /deep/ .ant-modal-body {
    padding: 0 !important;
  }

  /deep/ .ant-table-thead > tr > th, /deep/ .ant-table-tbody > tr > td {
    padding: 3px;
  }

  /deep/ .ant-table-footer {

    padding: 0px;
  }

  /deep/ .ant-table-pagination.ant-pagination {
    margin: 5px 0;
  }

  /deep/ .ant-input-number {
    width: 100%;
  }

  /deep/ .ant-input-number-sm > .ant-input-number-input-wrap > .ant-input-number-input {
    text-align: center;
  }

</style>