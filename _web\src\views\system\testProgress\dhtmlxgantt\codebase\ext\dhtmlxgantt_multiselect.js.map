{"version": 3, "sources": ["webpack://[name]/webpack/universalModuleDefinition", "webpack://[name]/webpack/bootstrap", "webpack://[name]/./sources/ext/multiselect.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "window", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "gantt", "config", "multiselect", "multiselect_one_level", "_multiselect", "_selected", "_one_level", "_active", "_first_selected_when_shift", "getDefaultSelected", "selected", "this", "getSelected", "length", "setFirstSelected", "id", "getFirstSelected", "isActive", "updateState", "active", "reset", "setLastSelected", "$data", "tasksStore", "silent", "store", "select", "unselect", "getLastSelected", "last", "getSelectedId", "isTaskExists", "e", "callEvent", "afterSelect", "toggle", "isSelected", "res", "push", "sort", "a", "b", "getGlobalTaskIndex", "forSelected", "callback", "isSameLevel", "calculateTaskLevel", "getTask", "refreshTask", "doSelection", "_is_icon_open_click", "target_ev", "locate", "defaultLast", "isLast", "shift<PERSON>ey", "ctrl<PERSON>ey", "metaKey", "first_indx", "target_indx", "last_indx", "tmp", "getNext", "getPrev", "old_selectTask", "selectTask", "old_unselectTask", "unselectTask", "toggleTaskSelection", "getSelectedTasks", "eachSelectedTask", "isSelectedTask", "getLastSelectedTask", "attachEvent", "new_id", "item", "task_id", "state"], "mappings": ";;;;;;;;;;;;CAAA,SAAAA,EAAAC,GACA,iBAAAC,SAAA,iBAAAC,OACAA,OAAAD,QAAAD,IACA,mBAAAG,eAAAC,IACAD,OAAA,iCAAAH,GACA,iBAAAC,QACAA,QAAA,+BAAAD,IAEAD,EAAA,+BAAAC,IARA,CASCK,OAAA,WACD,mBCTA,IAAAC,KAGA,SAAAC,EAAAC,GAGA,GAAAF,EAAAE,GACA,OAAAF,EAAAE,GAAAP,QAGA,IAAAC,EAAAI,EAAAE,IACAC,EAAAD,EACAE,GAAA,EACAT,YAUA,OANAU,EAAAH,GAAAI,KAAAV,EAAAD,QAAAC,IAAAD,QAAAM,GAGAL,EAAAQ,GAAA,EAGAR,EAAAD,QA0DA,OArDAM,EAAAM,EAAAF,EAGAJ,EAAAO,EAAAR,EAGAC,EAAAQ,EAAA,SAAAd,EAAAe,EAAAC,GACAV,EAAAW,EAAAjB,EAAAe,IACAG,OAAAC,eAAAnB,EAAAe,GAA0CK,YAAA,EAAAC,IAAAL,KAK1CV,EAAAgB,EAAA,SAAAtB,GACA,oBAAAuB,eAAAC,aACAN,OAAAC,eAAAnB,EAAAuB,OAAAC,aAAwDC,MAAA,WAExDP,OAAAC,eAAAnB,EAAA,cAAiDyB,OAAA,KAQjDnB,EAAAoB,EAAA,SAAAD,EAAAE,GAEA,GADA,EAAAA,IAAAF,EAAAnB,EAAAmB,IACA,EAAAE,EAAA,OAAAF,EACA,KAAAE,GAAA,iBAAAF,QAAAG,WAAA,OAAAH,EACA,IAAAI,EAAAX,OAAAY,OAAA,MAGA,GAFAxB,EAAAgB,EAAAO,GACAX,OAAAC,eAAAU,EAAA,WAAyCT,YAAA,EAAAK,UACzC,EAAAE,GAAA,iBAAAF,EAAA,QAAAM,KAAAN,EAAAnB,EAAAQ,EAAAe,EAAAE,EAAA,SAAAA,GAAgH,OAAAN,EAAAM,IAAqBC,KAAA,KAAAD,IACrI,OAAAF,GAIAvB,EAAA2B,EAAA,SAAAhC,GACA,IAAAe,EAAAf,KAAA2B,WACA,WAA2B,OAAA3B,EAAA,SAC3B,WAAiC,OAAAA,GAEjC,OADAK,EAAAQ,EAAAE,EAAA,IAAAA,GACAA,GAIAV,EAAAW,EAAA,SAAAiB,EAAAC,GAAsD,OAAAjB,OAAAkB,UAAAC,eAAA1B,KAAAuB,EAAAC,IAGtD7B,EAAAgC,EAAA,aAIAhC,IAAAiC,EAAA,yBClFAC,MAAAC,OAAAC,aAAA,EACAF,MAAAC,OAAAE,uBAAA,EAEAH,MAAAI,cACAC,aACAC,YAAA,EACAC,SAAA,EACAC,2BAAA,KACAC,mBAAA,WACA,IAAAC,EAAAC,KAAAC,cACA,OAAAF,EAAAG,OAAAH,IAAAG,OAAA,SAEAC,iBAAA,SAAAC,GACAJ,KAAAH,2BAAAO,GAEAC,iBAAA,WACA,OAAAL,KAAAH,4BAEAS,SAAA,WAEA,OADAN,KAAAO,cACAP,KAAAJ,SAEAW,YAAA,WACAP,KAAAL,WAAAN,MAAAC,OAAAE,sBACA,IAAAgB,EAAAR,KAAAJ,QACAI,KAAAJ,QAAAP,MAAAC,OAAAC,YACAS,KAAAJ,SAAAY,GACAR,KAAAS,SAGAA,MAAA,WACAT,KAAAN,cAEAgB,gBAAA,SAAAN,GACAf,MAAAsB,MAAAC,WAAAC,OAAA,WACA,IAAAC,EAAAzB,MAAAsB,MAAAC,WACAR,EACAU,EAAAC,OAAAX,EAAA,IAEAU,EAAAE,SAAA,SAGAC,gBAAA,WACA,IAAAC,EAAA7B,MAAAsB,MAAAC,WAAAO,gBACA,OAAAD,GAAA7B,MAAA+B,aAAAF,GACAA,EACA,MAEAH,OAAA,SAAAX,EAAAiB,GACA,SAAAjB,GAAAf,MAAAiC,UAAA,2BAAAlB,GAAA,EAAAiB,KAAAhC,MAAAiC,UAAA,wBAAAlB,OACAJ,KAAAN,UAAAU,IAAA,EACAJ,KAAAU,gBAAAN,GACAJ,KAAAuB,YAAAnB,GACAf,MAAAiC,UAAA,qBAAAlB,GAAA,EAAAiB,IACAhC,MAAAiC,UAAA,kBAAAlB,KACA,IAIAoB,OAAA,SAAApB,EAAAiB,GACArB,KAAAN,UAAAU,GACAJ,KAAAgB,SAAAZ,EAAAiB,GAEArB,KAAAe,OAAAX,EAAAiB,IAGAL,SAAA,SAAAZ,EAAAiB,GACAjB,GAAAf,MAAAiC,UAAA,2BAAAlB,GAAA,EAAAiB,MACArB,KAAAN,UAAAU,IAAA,EACAJ,KAAAiB,mBAAAb,GACAJ,KAAAU,gBAAAV,KAAAF,sBACAE,KAAAuB,YAAAnB,GACAf,MAAAiC,UAAA,qBAAAlB,GAAA,EAAAiB,IACAhC,MAAAiC,UAAA,oBAAAlB,MAGAqB,WAAA,SAAArB,GACA,SAAAf,MAAA+B,aAAAhB,KAAAJ,KAAAN,UAAAU,KAEAH,YAAA,WACA,IAAAyB,KACA,QAAArE,KAAA2C,KAAAN,UACAM,KAAAN,UAAArC,IAAAgC,MAAA+B,aAAA/D,GACAqE,EAAAC,KAAAtE,GAEA2C,KAAAN,UAAArC,IAAA,EAMA,OAHAqE,EAAAE,KAAA,SAAAC,EAAAC,GACA,OAAAzC,MAAA0C,mBAAAF,GAAAxC,MAAA0C,mBAAAD,GAAA,OAEAJ,GAEAM,YAAA,SAAAC,GAEA,IADA,IAAAlC,EAAAC,KAAAC,cACA5C,EAAA,EAAiBA,EAAA0C,EAAAG,OAAqB7C,IACtC4E,EAAAlC,EAAA1C,KAGA6E,YAAA,SAAA9B,GACA,IAAAJ,KAAAL,WACA,SACA,IAAAuB,EAAAlB,KAAAiB,kBACA,OAAAC,KAEA7B,MAAA+B,aAAAF,KAAA7B,MAAA+B,aAAAhB,MAEAf,MAAA8C,mBAAA9C,MAAA+C,QAAAlB,KAAA7B,MAAA8C,mBAAA9C,MAAA+C,QAAAhC,OAEAmB,YAAA,SAAAnB,GACAf,MAAA+B,aAAAhB,IACAf,MAAAgD,YAAAjC,IAEAkC,YAAA,SAAAjB,GACA,IAAArB,KAAAM,WACA,SAGA,GAAAjB,MAAAkD,oBAAAlB,GACA,SAEA,IAAAmB,EAAAnD,MAAAoD,OAAApB,GACA,IAAAmB,EACA,SAEA,IAAAnD,MAAAiC,UAAA,uBAAAD,IACA,SAEA,IAAAtB,EAAAC,KAAAC,cACAyC,EAAA1C,KAAAK,mBACAsC,GAAA,EACAzB,EAAAlB,KAAAiB,kBAYA,GAVAI,EAAAuB,SACAvD,MAAA+B,aAAApB,KAAAK,qBAAA,OAAAL,KAAAK,oBACAL,KAAAG,iBAAAqC,IAEGnB,EAAAwB,SAAAxB,EAAAyB,UACH9C,KAAAyB,WAAAe,IAGAxC,KAAAG,iBAAAqC,GAEAnB,EAAAwB,SAAAxB,EAAAyB,QACAN,GACAxC,KAAAwB,OAAAgB,EAAAnB,QAEG,GAAAA,EAAAuB,UAAA7C,EAAAG,OACH,GAAAgB,GAEA,GAAAsB,EAAA,CAOA,IANA,IAAAO,EAAA1D,MAAA0C,mBAAA/B,KAAAK,oBACA2C,EAAA3D,MAAA0C,mBAAAS,GACAS,EAAA5D,MAAA0C,mBAAAb,GAGAgC,EAAAhC,EACA7B,MAAA0C,mBAAAmB,KAAAH,GACA/C,KAAAgB,SAAAkC,EAAA7B,GACA6B,EAAAH,EAAAE,EAAA5D,MAAA8D,QAAAD,GAAA7D,MAAA+D,QAAAF,GAGA,IADAA,EAAAV,EACAnD,MAAA0C,mBAAAmB,KAAAH,GACA/C,KAAAe,OAAAmC,EAAA7B,KAAAsB,IACAA,GAAA,EACAD,EAAAQ,GAEAA,EAAAH,EAAAC,EAAA3D,MAAA8D,QAAAD,GAAA7D,MAAA+D,QAAAF,SAlBAhC,EAAAsB,MAqBG,CACHxC,KAAAyB,WAAAe,IACAxC,KAAAe,OAAAyB,EAAAnB,GAEAtB,EAAAC,KAAAC,cACA,QAAA5C,EAAA,EAAgBA,EAAA0C,EAAAG,OAAmB7C,IACnC0C,EAAA1C,KAAAmF,GACAxC,KAAAgB,SAAAjB,EAAA1C,GAAAgE,GAoBA,OAfArB,KAAAyB,WAAAe,GACAxC,KAAAU,gBAAA8B,GACGE,EACHF,GAAAtB,GACAlB,KAAAU,gBAAAW,EAAAuB,SAAAF,EAAA1C,KAAAF,sBAEAE,KAAAU,gBAAA,MAGAV,KAAAC,cAAAC,QACAF,KAAAU,gBAAA,MAEAV,KAAAiB,mBAAAjB,KAAAyB,WAAAzB,KAAAK,qBACAL,KAAAG,iBAAAH,KAAAiB,oBAEA,IAIA,WACA,IAAAoC,EAAAhE,MAAAiE,WACAjE,MAAAiE,WAAA,SAAAlD,GACA,IAAAA,EACA,SACA,IAAAb,EAAAF,MAAAI,aACAiC,EAAAtB,EASA,OARAb,EAAAe,YACAf,EAAAwB,OAAAX,EAAA,OACAb,EAAAmB,gBAAAN,GAEAb,EAAAY,iBAAAZ,EAAA0B,oBAEAS,EAAA2B,EAAA7F,KAAAwC,KAAAI,GAEAsB,GAGA,IAAA6B,EAAAlE,MAAAmE,aACAnE,MAAAmE,aAAA,SAAApD,GACA,IAAAb,EAAAF,MAAAI,aACAa,EAAAf,EAAAe,YACAF,KAAAb,EAAA0B,oBACAX,IACAf,EAAAyB,SAAAZ,EAAA,MACAA,GAAAb,EAAA0B,mBACA1B,EAAAmB,gBAAA,MACArB,MAAAgD,YAAAjC,GACAb,EAAAY,iBAAAZ,EAAA0B,oBAEA,IAAAS,EAAAtB,EAGA,OAFAE,IACAoB,EAAA6B,EAAA/F,KAAAwC,KAAAI,IACAsB,GAGArC,MAAAoE,oBAAA,SAAArD,GACA,IAAAb,EAAAF,MAAAI,aACAW,GAAAb,EAAAe,aACAf,EAAAiC,OAAApB,GACAb,EAAAY,iBAAAZ,EAAA0B,qBAGA5B,MAAAqE,iBAAA,WACA,IAAAnE,EAAAF,MAAAI,aAEA,OADAF,EAAAe,WACAf,EAAAU,eAEAZ,MAAAsE,iBAAA,SAAA1B,GACA,OAAAjC,KAAAP,aAAAuC,YAAAC,IAEA5C,MAAAuE,eAAA,SAAAxD,GACA,OAAAJ,KAAAP,aAAAgC,WAAArB,IAEAf,MAAAwE,oBAAA,WACA,OAAA7D,KAAAP,aAAAwB,mBAEA5B,MAAAyE,YAAA,0BACAzE,MAAAsB,MAAAC,WAAAa,WAAA,SAAArB,GACA,OAAAf,MAAAI,aAAAgC,WAAArB,MA3DA,GAgEAf,MAAAyE,YAAA,0BAAA1D,EAAA2D,GACA,IAAAxE,EAAAF,MAAAI,aACA,IAAAF,EAAAe,WACA,SACAjB,MAAAuE,eAAAxD,KACAb,EAAAyB,SAAAZ,EAAA,MACAb,EAAAwB,OAAAgD,EAAA,SAIA1E,MAAAyE,YAAA,6BAAA1D,EAAA4D,GACA,IAAAzE,EAAAF,MAAAI,aACA,IAAAF,EAAAe,WACA,SAEAf,EAAAG,UAAAU,KACAb,EAAAyB,SAAAZ,EAAA,MACAb,EAAAG,UAAAU,IAAA,EACAb,EAAAmB,gBAAAnB,EAAAO,uBAGAP,EAAAyC,YAAA,SAAAiC,GACA5E,MAAA+B,aAAA6C,IACA1E,EAAAyB,SAAAiD,EAAA,UAIA5E,MAAAyE,YAAA,mCAAA1D,EAAA8D,EAAA7C,GACA,IAAA9B,EAAAF,MAAAI,aACA,QAAAyE,GAAA3E,EAAAe,YACAf,EAAAI,aACAJ,EAAA2C,YAAA9B,KAMAf,MAAAyE,YAAA,uBAAA1D,EAAAiB,GAGA,OAFAhC,MAAAI,aAAA6C,YAAAjB,IACAhC,MAAAiC,UAAA,iBAAAD,KACA", "file": "ext/dhtmlxgantt_multiselect.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"ext/dhtmlxgantt_multiselect\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ext/dhtmlxgantt_multiselect\"] = factory();\n\telse\n\t\troot[\"ext/dhtmlxgantt_multiselect\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/codebase/\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 226);\n", "gantt.config.multiselect = true;\ngantt.config.multiselect_one_level = false;\n\ngantt._multiselect = {\n\t_selected: {},\n\t_one_level: false,\n\t_active: true,\n\t_first_selected_when_shift: null,\n\tgetDefaultSelected: function() {\n\t\tvar selected = this.getSelected();\n\t\treturn selected.length ? selected[selected.length - 1] : null;\n\t},\n\tsetFirstSelected: function(id) {\n\t\tthis._first_selected_when_shift = id;\n\t},\n\tgetFirstSelected: function() {\n\t\treturn this._first_selected_when_shift;\n\t},\n\tisActive: function() {\n\t\tthis.updateState();\n\t\treturn this._active;\n\t},\n\tupdateState: function() {\n\t\tthis._one_level = gantt.config.multiselect_one_level;\n\t\tvar active = this._active;\n\t\tthis._active = gantt.config.multiselect;\n\t\tif (this._active != active) {\n\t\t\tthis.reset();\n\t\t}\n\t},\n\treset: function () {\n\t\tthis._selected = {};\n\t},\n\tsetLastSelected: function (id) {\n\t\tgantt.$data.tasksStore.silent(function(){\n\t\t\tvar store = gantt.$data.tasksStore;\n\t\t\tif (id)\n\t\t\t\tstore.select(id+\"\");\n\t\t\telse\n\t\t\t\tstore.unselect(null);\n\t\t});\n\t},\n\tgetLastSelected: function () {\n\t\tvar last = gantt.$data.tasksStore.getSelectedId();\n\t\tif (last && gantt.isTaskExists(last))\n\t\t\treturn last;\n\t\treturn null;\n\t},\n\tselect: function (id, e) {\n\t\tif (id && gantt.callEvent(\"onBeforeTaskMultiSelect\", [id, true, e]) && gantt.callEvent(\"onBeforeTaskSelected\", [id])) {\n\t\t\tthis._selected[id] = true;\n\t\t\tthis.setLastSelected(id);\n\t\t\tthis.afterSelect(id);\n\t\t\tgantt.callEvent(\"onTaskMultiSelect\", [id, true, e]);\n\t\t\tgantt.callEvent(\"onTaskSelected\", [id]);\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t},\n\ttoggle: function (id, e) {\n\t\tif (this._selected[id]) {\n\t\t\tthis.unselect(id, e);\n\t\t} else {\n\t\t\tthis.select(id, e);\n\t\t}\n\t},\n\tunselect: function (id, e) {\n\t\tif (id && gantt.callEvent(\"onBeforeTaskMultiSelect\", [id, false, e])) {\n\t\t\tthis._selected[id] = false;\n\t\t\tif (this.getLastSelected() == id)\n\t\t\t\tthis.setLastSelected(this.getDefaultSelected());\n\t\t\tthis.afterSelect(id);\n\t\t\tgantt.callEvent(\"onTaskMultiSelect\", [id, false, e]);\n\t\t\tgantt.callEvent(\"onTaskUnselected\", [id]);\n\t\t}\n\t},\n\tisSelected: function (id) {\n\t\treturn !!(gantt.isTaskExists(id) && this._selected[id]);\n\t},\n\tgetSelected: function () {\n\t\tvar res = [];\n\t\tfor (var i in this._selected) {\n\t\t\tif (this._selected[i] && gantt.isTaskExists(i)) {\n\t\t\t\tres.push(i);\n\t\t\t} else {\n\t\t\t\tthis._selected[i] = false;\n\t\t\t}\n\t\t}\n\t\tres.sort(function(a, b) {\n\t\t\treturn gantt.getGlobalTaskIndex(a) > gantt.getGlobalTaskIndex(b) ? 1 : -1;\n\t\t});\n\t\treturn res;\n\t},\n\tforSelected: function (callback) {\n\t\tvar selected = this.getSelected();\n\t\tfor (var i = 0; i < selected.length; i++) {\n\t\t\tcallback(selected[i]);\n\t\t}\n\t},\n\tisSameLevel: function(id) {\n\t\tif (!this._one_level)\n\t\t\treturn true;\n\t\tvar last = this.getLastSelected();\n\t\tif (!last)\n\t\t\treturn true;\n\t\tif (!(gantt.isTaskExists(last) && gantt.isTaskExists(id)))\n\t\t\treturn true;\n\t\treturn !!(gantt.calculateTaskLevel(gantt.getTask(last)) == gantt.calculateTaskLevel(gantt.getTask(id)));\n\t},\n\tafterSelect: function(id) {\n\t\tif (gantt.isTaskExists(id))\n\t\t\tgantt.refreshTask(id);\n\t},\n\tdoSelection: function(e) {\n\t\tif (!this.isActive())\n\t\t\treturn false;\n\n\t\t// deny selection when click on 'expand' or 'collapse' icons\n\t\tif (gantt._is_icon_open_click(e))\n\t\t\treturn false;\n\n\t\tvar target_ev = gantt.locate(e);\n\t\tif (!target_ev)\n\t\t\treturn false;\n\n\t\tif (!gantt.callEvent(\"onBeforeMultiSelect\", [e]))\n\t\t\treturn false;\n\n\t\tvar selected = this.getSelected();\n\t\tvar defaultLast = this.getFirstSelected();\n\t\tvar isLast = false;\n\t\tvar last = this.getLastSelected();\n\n\t\tif (e.shiftKey) {\n\t\t\tif (!gantt.isTaskExists(this.getFirstSelected()) || this.getFirstSelected() === null) {\n\t\t\t\tthis.setFirstSelected(target_ev);\n\t\t\t}\n\t\t} else if (e.ctrlKey || e.metaKey) {\n\t\t\tif (!this.isSelected(target_ev))\n\t\t\t\tthis.setFirstSelected(target_ev);\n\t\t} else {\n\t\t\tthis.setFirstSelected(target_ev);\n\t\t}\n\t\tif (e.ctrlKey || e.metaKey) {\n\t\t\tif (target_ev) {\n\t\t\t\tthis.toggle(target_ev, e);\n\t\t\t}\n\t\t} else if (e.shiftKey && selected.length) {\n\t\t\tif (!last)\n\t\t\t\tlast = target_ev;\n\t\t\telse if (target_ev) {\n\t\t\t\tvar first_indx = gantt.getGlobalTaskIndex(this.getFirstSelected());\n\t\t\t\tvar target_indx = gantt.getGlobalTaskIndex(target_ev);\n\t\t\t\tvar last_indx = gantt.getGlobalTaskIndex(last);\n\n\t\t\t\t// clear prev selection\n\t\t\t\tvar tmp = last;\n\t\t\t\twhile (gantt.getGlobalTaskIndex(tmp) !== first_indx) {\n\t\t\t\t\tthis.unselect(tmp, e);\n\t\t\t\t\ttmp = (first_indx > last_indx) ? gantt.getNext(tmp) : gantt.getPrev(tmp);\n\t\t\t\t}\n\t\t\t\ttmp = target_ev;\n\t\t\t\twhile (gantt.getGlobalTaskIndex(tmp) !== first_indx) {\n\t\t\t\t\tif (this.select(tmp, e) && !isLast) {\n\t\t\t\t\t\tisLast = true;\n\t\t\t\t\t\tdefaultLast = tmp;\n\t\t\t\t\t}\n\t\t\t\t\ttmp = (first_indx > target_indx) ? gantt.getNext(tmp) : gantt.getPrev(tmp);\n\t\t\t\t}\n\t\t\t}\n\t\t} else { // no key press when mouse click\n\t\t\tif (!this.isSelected(target_ev)) {\n\t\t\t\tthis.select(target_ev, e);\n\t\t\t}\n\t\t\tselected = this.getSelected();\n\t\t\tfor (var i=0; i<selected.length; i++) {\n\t\t\t\tif (selected[i] !== target_ev) {\n\t\t\t\t\tthis.unselect(selected[i], e);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (this.isSelected(target_ev)) {\n\t\t\tthis.setLastSelected(target_ev);\n\t\t} else if (defaultLast) {\n\t\t\tif (target_ev == last)\n\t\t\t\tthis.setLastSelected(e.shiftKey ? defaultLast : this.getDefaultSelected());\n\t\t} else {\n\t\t\tthis.setLastSelected(null);\n\t\t}\n\n\t\tif (!this.getSelected().length)\n\t\t\tthis.setLastSelected(null);\n\n\t\tif (!this.getLastSelected() || !this.isSelected(this.getFirstSelected()))\n\t\t\tthis.setFirstSelected(this.getLastSelected());\n\n\t\treturn true;\n\t}\n};\n\n(function(){\n\tvar old_selectTask = gantt.selectTask;\n\tgantt.selectTask = function(id) {\n\t\tif (!id)\n\t\t\treturn false;\n\t\tvar multiselect = gantt._multiselect;\n\t\tvar res = id;\n\t\tif (multiselect.isActive()) {\n\t\t\tif (multiselect.select(id, null)) {\n\t\t\t\tmultiselect.setLastSelected(id);\n\t\t\t}\n\t\t\tmultiselect.setFirstSelected(multiselect.getLastSelected());\n\t\t} else {\n\t\t\tres = old_selectTask.call(this, id);\n\t\t}\n\t\treturn res;\n\t};\n\n\tvar old_unselectTask = gantt.unselectTask;\n\tgantt.unselectTask = function(id) {\n\t\tvar multiselect = gantt._multiselect;\n\t\tvar isActive = multiselect.isActive();\n\t\tid = id || multiselect.getLastSelected();\n\t\tif(id && isActive) {\n\t\t\tmultiselect.unselect(id, null);\n\t\t\tif (id == multiselect.getLastSelected())\n\t\t\t\tmultiselect.setLastSelected(null);\n\t\t\tgantt.refreshTask(id);\n\t\t\tmultiselect.setFirstSelected(multiselect.getLastSelected());\n\t\t}\n\t\tvar res = id;\n\t\tif (!isActive)\n\t\t\tres = old_unselectTask.call(this, id);\n\t\treturn res;\n\t};\n\n\tgantt.toggleTaskSelection = function(id) {\n\t\tvar multiselect = gantt._multiselect;\n\t\tif (id && multiselect.isActive()) {\n\t\t\tmultiselect.toggle(id);\n\t\t\tmultiselect.setFirstSelected(multiselect.getLastSelected());\n\t\t}\n\t};\n\tgantt.getSelectedTasks = function() {\n\t\tvar multiselect = gantt._multiselect;\n\t\tmultiselect.isActive();\n\t\treturn multiselect.getSelected();\n\t};\n\tgantt.eachSelectedTask = function(callback){\n\t\treturn this._multiselect.forSelected(callback);\n\t};\n\tgantt.isSelectedTask = function(id){\n\t\treturn this._multiselect.isSelected(id);\n\t};\n\tgantt.getLastSelectedTask = function(){\n\t\treturn this._multiselect.getLastSelected();\n\t};\n\tgantt.attachEvent(\"onGanttReady\", function(){\n\t\tgantt.$data.tasksStore.isSelected = function(id){\n\t\t\treturn gantt._multiselect.isSelected(id);\n\t\t};\n\t});\n})();\n\ngantt.attachEvent(\"onTaskIdChange\", function (id, new_id) {\n\tvar multiselect = gantt._multiselect;\n\tif (!multiselect.isActive())\n\t\treturn true;\n\tif (gantt.isSelectedTask(id)) {\n\t\tmultiselect.unselect(id, null);\n\t\tmultiselect.select(new_id, null);\n\t}\n});\n\ngantt.attachEvent(\"onAfterTaskDelete\", function (id, item) {\n\tvar multiselect = gantt._multiselect;\n\tif (!multiselect.isActive())\n\t\treturn true;\n\n\tif (multiselect._selected[id]) {\n\t\tmultiselect.unselect(id, null);\n\t\tmultiselect._selected[id] = false;\n\t\tmultiselect.setLastSelected(multiselect.getDefaultSelected());\n\t}\n\n\tmultiselect.forSelected(function (task_id) {\n\t\tif (!gantt.isTaskExists(task_id))\n\t\t\tmultiselect.unselect(task_id, null);\n\t});\n});\n\ngantt.attachEvent(\"onBeforeTaskMultiSelect\", function(id, state, e){\n\tvar multiselect = gantt._multiselect;\n\tif (state && multiselect.isActive()) {\n\t\tif (multiselect._one_level) {\n\t\t\treturn multiselect.isSameLevel(id);\n\t\t}\n\t}\n\treturn true;\n});\n\ngantt.attachEvent(\"onTaskClick\", function(id, e) {\n\tif (gantt._multiselect.doSelection(e))\n\t\tgantt.callEvent(\"onMultiSelect\", [e]);\n\treturn true;\n});"], "sourceRoot": ""}