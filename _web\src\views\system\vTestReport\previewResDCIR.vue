<template>
	<div ref="wrapper" class="wrapper">
		<div class="flex-sb-center-row">
			<div class="head_title">{{ record.type + ": " + record.reportName }}</div>
		</div>

		<div class="all-wrapper">
			<div class="left-content block">
				<pageComponent editObj="resDcir"  @down="handleNormalDown('resDcir')" @edit="handleEditEcharts('resDcir')"></pageComponent>
				<div id="resDcir" ref="resDcir" class="mt10" style="width: 598px;height: 417px;border: 0.5px solid #ccc;"></div>
			</div>
			<div class="right-content">
				<div class="block" id="export">
					<div class="flex-column">
						<div>
							<div>原始数据</div>
							<div style="float: right;margin-top: -25px">
								<a-button type="primary" @click="() => (update = true)" v-if="!update">修改数据</a-button>
								<a-button type="primary" @click="cancel" v-if="update" style="margin-right: 20px">取消修改</a-button>
								<a-popconfirm placement="topRight" ok-text="确认" cancel-text="取消" @confirm="updateData" v-if="update">
									<template slot="title">
										<p>确认提交更改吗</p>
									</template>
									<a-button type="primary">提交数据</a-button>
								</a-popconfirm>
							</div>
						</div>

						<div class="mt10">
							<a-table :columns="originColumns" bordered :data-source="originData" :pagination="false">
							</a-table>
						</div>
						<strong>DCR</strong>
						<div class="mt10">
							<a-table :columns="resDcirColumns" bordered :data-source="resDcirData" :pagination="false">
							</a-table>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div v-if="drawerVisible">
			<PreviewDrawer :screenImageId = "screenImageId" :templateParam = "reportChartTemplateList[editObj]" :isLegendLeft =  "true" :legendOptions="resDcirLegendList" :data="resDcirSeriesList" :original="resDcirOriginalData"
				:editData="resDcirEditData" :checkObj="chartCheckObj[editObj]"  @submit="handleDrawerSubmit" @reset="handleDrawerReset"
				@close="drawerVisible = false" @changeTemplate ="handleChangeTemplate" @screenshot="handleScreenshot"></PreviewDrawer>
		</div>
		<!-- <pbiReturnTop v-if="isShowReturnTop" @returnTop="handleReturnTop"></pbiReturnTop> -->

	</div>
</template>
<script>
	import { testReportGet, testReportUpdateDate } from "@/api/modular/system/limsManager"
  import { mixin,chart } from "./mixin/index.js"
  import {chartTemplate} from "@/views/system/vTestReport/mixin/chartTemplate";

	export default {
		mixins: [mixin,chart,chartTemplate],
		data: function () {
			return {
				id: null,
				update: false,

				/* 图表 */
				iisEditResDcirXNum: 0,
				resDcirEchart: null,

				/* 右边表格 */
				originColumns: [],
				originData: [],

				resDcirColumns: [],
				resDcirData: [],

				/* 在线编辑图表 */
				chartCheckObj:{
					resDcir:{}
				},
				resDcirLegendList: [], // 图例的原始值
				resDcirSeriesList: [], // 原始值
				// resDcirCheckObj: {}, //点击图标的选中对象  editObj: axis 坐标轴  tag 线 legend 图例
				resDcirEditData: {},
				resDcirOriginalData: {},
			}
		},
		async mounted() {
			await this.getChartTemplateRelationList(this.$route.query.id,['resDcir'])
			this.init()

	// 		const box = this.$refs.wrapper
    //   box.addEventListener("scroll", e => {
    //     if (e.target.scrollTop > 100 && !this.isShowReturnTop) {
    //       this.isShowReturnTop = true
    //     }
    //     if(e.target.scrollTop < 100 && this.isShowReturnTop){
    //       this.isShowReturnTop = false
    //     }
    //   })
		},

		methods: {
			init() {
				this.id = this.$route.query.id
				testReportGet({ id: this.id })
					.then(res => {
						this.record = res.data
						this.allDataJson = JSON.parse(res.data.allDataJson)
						this.queryParam = JSON.parse(res.data.queryParam)
						this.originData = JSON.parse(res.data.allDataJson).tableList
						this.resDcirData = JSON.parse(res.data.allDataJson).dcrList

						this.update = false
					})
					.then(async res => {
						// 右边表格数据
						if (this.originData.length > 0) {
							this.initTable()
						}

						this.resDcirEditData = this._getInitData('resDcir','edit')
						this.resDcirOriginalData = this._getInitData('resDcir')

						await this.initResDcir()
					})
			},

			initTable() {

				this.originColumns = [
					{
						title: "温度",
						dataIndex: "temp",
						align: "center",
						width: 60,
						customRender: (text, record, index) => {
							const obj = {
								children: text,
								attrs: {}
							}
							obj.attrs.rowSpan = 0

							let list = this.originData.filter(o => o.temp == text)

							if (index == 0 || index % list.length == 0) {
								obj.attrs.rowSpan = list.length
							}

							return obj
						}
					}
				]
				for (let i = 0; i < this.originData[0].dataList.length; i++) {
					this.originColumns.push({
						//title: '第' + res.data[0].data[i].day + '天',
						title: i + 1 + "#",
						align: "center",
						children: [
							{
								title: "U",
								width: 60,
								align: "center",
								dataIndex: "dataList[" + i + "].u",
								customRender: (text, record, index) => {
									const obj = {
										children: this.update ? (
											<a-input-number step="0.01" style="width:100%" v-model={record.dataList[i].u}></a-input-number>
										) : (
											record.dataList[i].u
										),
										attrs: {}
									}
									return obj
								}
							},
							{
								title: "I",
								width: 60,
								align: "center",
								dataIndex: "dataList[" + i + "].i",
								customRender: (text, record, index) => {
									const obj = {
										children: this.update ? (
											<a-input-number step="0.01" style="width:100%" v-model={record.dataList[i].i}></a-input-number>
										) : (
											record.dataList[i].i
										),
										attrs: {}
									}
									return obj
								}
							}
						]
					})
				}

				this.resDcirColumns = [
					{
						title: "温度",
						width: 60,
						align: "center",
						dataIndex: "temp"
					},
					{
						title: "Avg.",
						width: 60,
						align: "center",
						dataIndex: "average"
					}
				]

				for (let i = 0; i < this.resDcirData[0].dataList.length; i++) {
					this.resDcirColumns.push({
						//title: '第' + res.data[0].data[i].day + '天',
						title: i + 1 + "#",
						align: "center",
						width: 60,
						dataIndex: "dataList[" + i + "].dcr"
					})
				}
			},

			initResDcir(
				legendData = {},
				checkData = [], //选中的数据
				axisData = {},
				titleData = {},
				gridData = {}
			) {
				if(this.resDcirEchart) this.resDcirEchart.dispose();
				this.resDcirEchart = this.echarts.init(this.$refs.resDcir, 'walden',{ devicePixelRatio: 2 })

				const templateParam = this.reportChartTemplateList['resDcir'].templateParamJson
				const originalParam = this.reportChartTemplateList['resDcir'].originalParamJson
				let dcrEchartsList = _.cloneDeep(this.allDataJson.dcrEcharts)
				dcrEchartsList.sort((item1,item2) => { return item1.batteryNum -  item2.batteryNum })



				// 原始图例  使用位置：在线编辑图表--图例数据
				this.resDcirLegendList = _.cloneDeep(this.allDataJson.dcrEcharts).map(e => e.batteryNum + '#')

				// 把模板的数据拼接进去
				const {
					titleData: newTitleData,
					gridData: newGridData,
					legendData: newLegendData,
					axisData: newAxisData,
					legend: newLegend
				} = this._getTemplateParams('resDcir', titleData, gridData, legendData, axisData, legend);
				titleData = newTitleData;
				gridData = newGridData;
				legendData = newLegendData;
				axisData = newAxisData;

				// 首次渲染,所有图例都显示
        if(!legendData.legendRevealList || templateParam.legendData.legendRevealList){
          const revealNum = this.handleRevealNum(520,this.resDcirLegendList)
          this.resDcirEditData.legendRevealList = templateParam.legendData.legendRevealList ?? _.cloneDeep(this.resDcirLegendList).slice(0,revealNum)
        }

				 //页面上有哪些图例 使用位置：在线编辑图表--选中的图例数据（默认全部选中）
				this.resDcirEditData.legend = _.cloneDeep(this.resDcirLegendList)



				// 数据
				let resDcirSeries = []
				let lineColorList = [] // 折线颜色
				// let duplicateDataOptions = []

				// 数据排序,避免颜色混乱
        		const echartsColorList = this.resDcirEditData.legend.length <= 2 ? this.echartsColorShortList : this.echartsColorLongList
				const isCheck = checkData.length === 0

				this.resDcirSeriesList = []
				this.resDcirOriginalData.checkData = []
				this.resDcirOriginalData.series = []

				// 数据处理
				for (let i = 0; i < dcrEchartsList.length; i++) {
					// 设置折线的颜色
					const have = lineColorList.find(v => v.name === dcrEchartsList[i].batteryNum)
					if (have == undefined) {
						lineColorList.push({ name: dcrEchartsList[i].batteryNum, color: echartsColorList[lineColorList.length % echartsColorList.length] })
					}

					const templateContent = templateParam.checkData.length > 0 ? (templateParam.checkData.filter(item => item.id === dcrEchartsList[i].batteryNum + "#" + (i + 1))[0] || {}) : {}
					const editContentIndex = checkData.findIndex(findItem => findItem.id ===  dcrEchartsList[i].batteryNum + "#" + (i + 1))
					const editContent = editContentIndex !== -1 ? checkData[editContentIndex] : {}

					let series = {
						id: dcrEchartsList[i].batteryNum + "#" + (i + 1),
						name: dcrEchartsList[i].batteryNum + '#',
						soc: dcrEchartsList[i].batteryNum,
						type: 'line',
						barGap: 0,
						markPoint: {
							data: []
						},
						connectNulls: templateContent.connectNulls ?? (isCheck ? false : Boolean(Number(editContent.connectNulls))),
						symbol: templateContent.symbol ?? (isCheck ? "rect" : editContent.symbol),
						symbolSize: templateContent.symbolSize ?? (isCheck ? 5 : editContent.symbolSize),
						lineStyle: {
							width: templateContent.lineWidth ?? (isCheck ? 1 : editContent.lineWidth),
							type: templateContent.lineType ?? (isCheck ? "solid" : editContent.lineType),
							color: templateContent.lineColor ?? (
								isCheck
									? lineColorList[lineColorList.findIndex(v => v.name === dcrEchartsList[i].batteryNum)].color
									: editContent.lineColor
							)
						},
						itemStyle: {
							color: templateContent.itemColor ?? (
								isCheck
									? lineColorList[lineColorList.findIndex(v => v.name === dcrEchartsList[i].batteryNum)].color
									: editContent.itemColor
							)
						},
						emphasis: {
							focus: "series"
						},

						batteryNum: dcrEchartsList[i].batteryNum,
						data:dcrEchartsList[i].dcrDataList.map((mapItem, index) => { return { id: index, name: dcrEchartsList[i].batteryNum + '#', value: mapItem } }),
					}

					// 设置最大最小值
					if (checkData.length > 0 && editContent.maxPoint || templateContent.maxPoint) {
						series.markPoint.data.push({ type: "max", name: "Max" })
					}
					if (checkData.length > 0 && editContent.minPoint || templateContent.minPoint) {
						series.markPoint.data.push({ type: "min", name: "Min" })
					}

					this.resDcirSeriesList.push({
            			id: dcrEchartsList[i].batteryNum + "#" + (i + 1),
						index: i + 1,
						soc: dcrEchartsList[i].batteryNum + "#",
						name: dcrEchartsList[i].batteryNum + "#",
						data: dcrEchartsList[i].dcrDataList.map(v => v[1] ? v[1].toString() : null),
						duplicateData:dcrEchartsList[i].dcrDataList.map((mapItem, index) => { return { id: index, name: dcrEchartsList[i].batteryNum + '#', value: mapItem } }),
						synchronization: templateContent.synchronization ?? (isCheck ? i : editContent.synchronization),
						maxPoint: templateContent.maxPoint ?? (isCheck ? false : editContent.maxPoint),
						minPoint: templateContent.minPoint ?? (isCheck ? false : editContent.minPoint),
						connectNulls: templateContent.connectNulls ?? false,
						symbol: templateContent.symbol ?? (isCheck ? "rect" : editContent.symbol),
						symbolSize: templateContent.symbolSize ?? (isCheck ? 5 : editContent.symbolSize),
						itemColor: templateContent.itemColor ?? (
							isCheck
								? lineColorList[lineColorList.findIndex(v => v.name === dcrEchartsList[i].batteryNum)].color
								: editContent.itemColor
						),
						lineType: templateContent.lineType ?? (isCheck ? "solid" : editContent.lineType),
						lineWidth: templateContent.lineWidth ?? (isCheck ? 1 : editContent.lineWidth),
						lineColor: templateContent.lineColor ?? (
							isCheck
								? lineColorList[lineColorList.findIndex(v => v.name === dcrEchartsList[i].batteryNum)].color
								: editContent.lineColor
						)
					})
					// 原始值
					this.resDcirOriginalData.checkData.push({
						id: dcrEchartsList[i].batteryNum + "#" + (i + 1),
						index: i + 1,
						soc: dcrEchartsList[i].batteryNum + "#",
						name: dcrEchartsList[i].batteryNum + "#",
						connectNulls: false,
						synchronization: i,
						maxPoint: false,
						minPoint: false,
						symbol: "rect",
						symbolSize: 5,
						itemColor: lineColorList[lineColorList.findIndex(v => v.name === dcrEchartsList[i].batteryNum)].color,
						lineType: "solid",
						lineWidth: 1,
						lineColor: lineColorList[lineColorList.findIndex(v => v.name === dcrEchartsList[i].batteryNum)].color
					})

					this.resDcirOriginalData.series.push({
						id: dcrEchartsList[i].batteryNum + "#" + (i + 1),
						index: i + 1,
						soc: dcrEchartsList[i].batteryNum + "#",
						name: dcrEchartsList[i].batteryNum + "#",
						connectNulls: false,
						synchronization: i,
						maxPoint: false,
						minPoint: false,
						symbol: "rect",
						symbolSize: 5,
						itemColor: lineColorList[lineColorList.findIndex(v => v.name === dcrEchartsList[i].batteryNum)].color,
						lineType: "solid",
						lineWidth: 1,
						lineColor: lineColorList[lineColorList.findIndex(v => v.name === dcrEchartsList[i].batteryNum)].color
					})

					// duplicateDataOptions.push({
          //   id: dcrEchartsList[i].batteryNum + "#",
					// 	data: dcrEchartsList[i].dcrDataList.map((mapItem, index) => { return { id: index, name: dcrEchartsList[i].batteryNum + "#", value: index, label: mapItem[1].toString() } })
          // })

					resDcirSeries.push(series)
				}



				this.resDcirEditData.series = _.cloneDeep(this.resDcirSeriesList)

        // this.resDcirEditData.duplicateDataOptions = _.cloneDeep(duplicateDataOptions)

				/* 图例排序 开始 */
        if (legendData.legendSort) {
          this.resDcirEditData.legend = _.cloneDeep(legendData.legendSort) // 将页面上的图例数组按照用户设置的顺序排序
        }
        this.resDcirEditData.legendSort = !legendData.legendSort ? _.cloneDeep(this.resDcirLegendList) : _.cloneDeep(this.resDcirEditData.legend)
        /* 图例排序 结束 */

				/* 图例变更名称 开始 */
        if (legendData.legendEditName) {
          legendData.legendEditName.forEach(v => {
            if (v.newName && !v.isReset) {
              let temIndex1 = this.resDcirLegendList.findIndex(findItem => findItem == v.originName)
              this.resDcirLegendList[temIndex1] = v.newName
              let temIndex2 = this.resDcirEditData.legend.findIndex(findItem => findItem == v.originName)
              this.resDcirEditData.legend[temIndex2] = v.newName
              this.resDcirEditData.series.forEach(findItem => {
                findItem.name = findItem.name == v.originName ? v.newName : findItem.name
              })
              resDcirSeries.forEach(findItem => {
                findItem.name = findItem.name == v.originName ? v.newName : findItem.name
              })
            }

            if (!v.newName && v.isReset) {
              v.previousName = ''
              v.isReset = false
            }
          })

          // 赋予修改后的图例修改名称数组
          this.resDcirEditData.legendEditName = legendData.legendEditName
        }

        // 图例修改名称数组  使用位置：在线编辑图表--图例--名称 ,首次进入，将图例的值给图例修改名称数组
        // originName 原始值 newName 新值 previousName 上一个值（用于清空的情况下使用）isReset 是否重置（用于清空的时候使用）
        if (this.resDcirEditData.legendEditName.length === 0) {
          this.resDcirEditData.legendEditName = this.resDcirLegendList.map(v => { return {id:v, originName: v, previousName: '', newName: '', isReset: false } })
        }
        /* 图例变更名称 结束 */

				// 处理选中图例
				if (legendData.legendEdit) {
				// 移除页面上的对应的图例
					for (let i = 0; i < this.resDcirEditData.legend.length; i++) {
						if (!legendData.legendEdit.includes(this.resDcirEditData.legend[i])) {
							this.resDcirEditData.legend.splice(i, 1)
							i--
						}
					}

					 // 移除页面上的对应的图例的图表数据
					for (let i = 0; i < resDcirSeries.length; i++) {
						if (!legendData.legendEdit.includes(resDcirSeries[i].name)) {
							resDcirSeries.splice(i, 1)
							i--
						}
					}

					// 判断依据
					for (let i = 0; i < checkData.length; i++) {
						// 这里的soc 就是 batteryNum
						if (!legendData.legendEdit.includes(checkData[i].name)) {
							checkData.splice(i, 1)
							i--
						}
					}
				}

				// 处理选中数据
				// if (checkData.length > 0) {
				// 	resDcirSeries.forEach((v, index) => {
				// 	const handIndex = checkData.findIndex(findItem => findItem.id == v.id)
				// 		for (let i = 0; i < v.data.length; i++) {
				// 			if (!checkData[handIndex].duplicateData.includes(v.data[i].id)) {
				// 				v.data.splice(i, 1)
				// 				i--
				// 			}
				// 		}
				// 	})
				// }

				// 结合图例数据（线+图例都存在）和图例显隐（只存在线，剔除图例）
        const legend = this._getLegend(this.resDcirEditData)

				// option
				let options = {
					backgroundColor: '#ffffff',
					animationDuration: 2000,
					title: {
						text: titleData.chartTitle || 'DCR vs. Temperature',
						left: 'center',
						top: titleData.titleTop ||  10,
						fontSize: 18,
						fontWeight: 500,
						color: "#000"
					},
					grid: {
						show: true,
						top: gridData.gridTop ||  45,
						left: gridData.gridLeft || 55,
						right: gridData.gridRight || 30,
						bottom: gridData.gridBottom || 50,
						borderWidth: 0.5,
						borderColor: "#ccc"
					},
					textStyle: {
						fontFamily: "Times New Roman"
					},
					tooltip: {
						trigger: 'axis',
						formatter: function (params) {
							var result = params[0].axisValue + '℃' + '<br>'; // 添加 x 轴的数值
							params.forEach(function (item, dataIndex) {
								result += '<div style="width:150px;display: inline-block;">' + item.marker + item.seriesName + '</div>' + item.data.value[1] + '<br>'; // 添加每个系列的数值
							});
							return result;
						}
					},

					legend: {
						show: true,
						backgroundColor:legendData.legendBgColor || "#f5f5f5",
						data: legend,
						itemWidth: legendData.legendWidth || 20,
						itemHeight: legendData.legendHeight || 5,
						itemGap: legendData.legendGap || 10,
						orient: legendData.legendOrient || 'horizontal',
						left: legendData.legendLeft || 'center',
            top: legendData.legendTop || 50,
						textStyle: {
							fontSize: 14,
							color: "#000000"
						}
					},

					xAxis: [
						{
							name: titleData.XTitle || 'Temperature (℃)',
							type: axisData.xType || 'category',
							boundaryGap: false,
							nameLocation: 'middle', // 将名称放在轴线的中间位置
							nameGap: 25,
							axisTick: { show: false },
							splitLine: {
								show: true, // 显示分隔线
								lineStyle: {
									type: "solid",
									width: 0.5
								}
							},
							axisLabel: {
								show: true,
								width: 0.5,
								textStyle: {
									fontSize: "15",
									color: "#000000"
								}
							},
							axisLine: {
								show: true,
								lineStyle: {
									color: "#ccc",
									width: 0.5
								}
							},
							nameTextStyle: {
								fontSize: 14,
								fontWeight: 500,
								color: "#000000" // 可以根据需要调整字体大小
							}
						}
					],
					yAxis: [
						{
							type: axisData.yType || 'value',
							name: titleData.YTitle || "DCR (mΩ)",
							position: "left",
							nameGap: titleData.yTitleLetf || 30,
							splitLine: {
								show: true, // 显示分隔线
								lineStyle: {
									type: "solid",
									width: 0.5
								}
							},
							axisTick: {
								show: true // 显示刻度
							},
							axisLabel: {
								show: true,
								width: 0.5,
								textStyle: {
									fontSize: "15",
									color: "#000000"
								}
							},
							axisLine: {
								show: true,
								lineStyle: {
									color: "#ccc",
									width: 0.5
								}
							},
							nameLocation: "middle", // 将名称放在轴线的起始位置
							nameRotate: 90, // 旋转角度，使名称竖排
							nameTextStyle: {
								fontSize: 14, // 可以根据需要调整字体大小
								fontWeight: 500,
								color: "#000000"
							}
						}
					],
					series: resDcirSeries
				}

				// 传回给在线编辑图表，当前图标上有的点
        // this.resDcirEditData.duplicateCheckedList = []

				// _.cloneDeep(this.resDcirSeriesList).forEach((forItem,forIndex) => {
				// 	const haveIndex = resDcirSeries.findIndex(findItem => findItem.id === forItem.id)
				// 	if(haveIndex == -1) return this.resDcirEditData.duplicateCheckedList.push(forItem.duplicateData.filter(filterItem => filterItem !== '' ).map(mapItem => mapItem.id))
				// 	this.resDcirEditData.duplicateCheckedList.push(resDcirSeries[haveIndex].data.filter(filterItem => filterItem !== '' ).map(mapItem => mapItem.id))
				// })

				// 处理坐标轴
				if (axisData.xMin) {
					options.xAxis[0].min = axisData.xMin
				}
				if (axisData.xMax) {
					options.xAxis[0].max = axisData.xMax
				}
				if (axisData.xInterval) {
					options.xAxis[0].interval = axisData.xInterval
				}
				if (axisData.yMin) {
					options.yAxis[0].min = axisData.yMin
				}
				if (axisData.yMax) {
					options.yAxis[0].max = axisData.yMax
				}
				if (axisData.yInterval) {
					options.yAxis[0].interval = axisData.yInterval
				}

				// 坐标轴类型赋值
				this.resDcirEditData.xType = options.xAxis[0].type
				this.resDcirEditData.yType = options.yAxis[0].type

				this.resDcirEchart.clear()
				// this.resDcirEchart.getZr().off('click')
				// this.resDcirEchart.getZr().on('click', params => {
				// 	const { target, topTarget } = params

				// 	// Z 0:坐标轴
				// 	if (topTarget?.z === 0 && this.drawerVisible) {
				// 		this.$set(this.resDcirCheckObj, 'editObj', 'axis')
				// 	}
				// 	// Z 3:线
				// 	if (topTarget?.z === 3 && this.drawerVisible) {
				// 		const axs = target.parent?.parent?.__ecComponentInfo?.index
				// 		this.$set(this.resDcirCheckObj, 'tag', axs)
				// 		this.$set(this.resDcirCheckObj, 'editObj', 'tag')
				// 	}
				// 	// Z 4:图例
				// 	if (topTarget?.z === 4 && this.drawerVisible) {
				// 		const axs = target.parent?.__legendDataIndex
				// 		// this.$set(this.resDcirCheckObj,'legend',axs)
				// 		this.$set(this.resDcirCheckObj, 'editObj', 'legend')
				// 	}
				// });
				this.resDcirEchart.getZr().off('dblclick')
				this.resDcirEchart.getZr().on('dblclick', ({target, topTarget}) => {
				this._handleDblclickEchart(target, topTarget, 'resDcir')
				});
				this.resDcirEchart.setOption(options)

				// 如果坐标轴类型为数值轴，则计算出最大值最小值，以及间距
				if (options.xAxis[0].type === "value") {
					const XAxis = this.resDcirEchart.getModel().getComponent("xAxis").axis.scale
					this.resDcirEditData.xMin = XAxis._extent[0]
					this.resDcirEditData.xMax = XAxis._extent[1]
					this.resDcirEditData.xInterval = XAxis._interval
				}

				if (options.yAxis[0].type === "value") {
					const YAxis = this.resDcirEchart.getModel().getComponent("yAxis").axis.scale
					this.resDcirEditData.yMin = YAxis._extent[0]
					this.resDcirEditData.yMax = YAxis._extent[1]
					this.resDcirEditData.yInterval = YAxis._interval

					this.resDcirOriginalData.yMin = YAxis._extent[0]
					this.resDcirOriginalData.yMax = YAxis._extent[1]
					this.resDcirOriginalData.yInterval = YAxis._interval
				}

				if (this.iisEditResDcirXNum === 0 && axisData.xType === "value") {
					this.iisEditResDcirXNum++
					this.resDcirOriginalData.xMin = this.resDcirEditData.xMin
					this.resDcirOriginalData.xMax = this.resDcirEditData.xMax
					this.resDcirOriginalData.xInterval = this.resDcirEditData.xInterval
				}

				if(originalParam?.xMax > 0){
					this.resDcirOriginalData.xMin = originalParam.xMin
					this.resDcirOriginalData.xMax = originalParam.xMax
					this.resDcirOriginalData.xInterval = originalParam.xInterval
				}
				if(originalParam?.yMax > 0){
					this.resDcirOriginalData.yMin = originalParam.yMin
					this.resDcirOriginalData.yMax = originalParam.yMax
					this.resDcirOriginalData.yInterval = originalParam.yInterval
				}
			},



			updateData() {
				testReportUpdateDate(this.originData, this.id).then(res => {
					this.init()
				})
			},

			// 生成
			handleDrawerSubmit(value) {

				const legendData = {
					legendEdit: value.legendList,
					legendRevealList: value.legendRevealList,
					legendWidth: value.legendWidth,
					legendHeight: value.legendHeight,
					legendGap: value.legendGap,
					legendSort: value.legendSort, // 图例排序
					legendEditName: value.legendEditName,
					legendOrient: value.legendOrient,
					legendTop:value.legendTop,
					legendBgColor:value.legendBgColor,
					legendLeft: value.legendLeft,
				}

				console.log(legendData)


				const axisData = {
					xMin: value.xMin,
					xMax: value.xMax,
					xInterval: value.xInterval,
					xType: value.xType,

					yMin: value.yMin,
					yMax: value.yMax,
					yInterval: value.yInterval,
					yType: value.yType
				}

				const titleData = {
					chartTitle: value.chartTitle,
					XTitle: value.XTitle,
					YTitle: value.YTitle,
					titleTop: value.titleTop,
					yTitleLetf: value.yTitleLetf,
				}

				const gridData = {
					gridTop: value.gridTop,
					gridLeft: value.gridLeft,
					gridRight: value.gridRight,
					gridBottom: value.gridBottom,
				}

				// 赋值的数组
				const assignArr = ['chartTitle', 'XTitle', 'YTitle','titleTop','yTitleLetf','legendRevealList', 'legendWidth', 'legendHeight', 'legendGap', 'legendEditName', 'legendSort', 'legendOrient','legendTop','legendBgColor','legendX','xMin', 'xMax', 'xInterval', 'xType',
					'yMin', 'yMax', 'yInterval', 'yType', 'synchronization','gridTop','gridLeft','gridRight','gridBottom',"targetEditObj"]

				// 处理模板参数
				this._handleTemplateParams(value)

				this.resDcirEditData.series = _.cloneDeep(value.checkData)

				for (let i = 0; i < assignArr.length; i++) {
					this.resDcirEditData[assignArr[i]] = value[assignArr[i]]
				}

				this.initResDcir(
					legendData,
					value.checkData,
					axisData,
					titleData,
					gridData
				)

				this.$forceUpdate()



				// 记录数据到后端
				let chartTemplateParams = {}
				if(!this.reportChartTemplateList['resDcir'].templateId){
					chartTemplateParams = {
						targetChart:'resDcir',
						templateName:'报告ID修改默认模板',
						reportId:this.$route.query.id,
						originalParamJson:JSON.stringify(this.resDcirOriginalData),
						templateParamJson:JSON.stringify(this.reportChartTemplateList['resDcir'].templateParamJson),
					}
					this.reportChartTemplateList['resDcir'].originalParamJson = this.resDcirOriginalData
					this.saveChartTemplate(chartTemplateParams)
				}else{
					chartTemplateParams = {
						id:this.reportChartTemplateList['resDcir'].templateId,
						templateParamJson:JSON.stringify(this.reportChartTemplateList['resDcir'].templateParamJson),
					}
					if(this.iisEditResDcirXNum === 1 && this.reportChartTemplateList['resDcir'].originalParamJson.xMax == 0){
						chartTemplateParams.originalParamJson = JSON.stringify(this.resDcirOriginalData)
						this.reportChartTemplateList['resDcir'].originalParamJson = this.resDcirOriginalData
					}
					this.updateChartTemplate(chartTemplateParams)
				}
			},

			// 重置
			handleDrawerReset() {
				this.$confirm({
					title: '请确认是否重置图表?',
					content: '图表重置后，图表修改内容无法恢复',
					okText: '重置',
					cancelText: '取消',
					onOk:async () => {
						await this.deleteChartTemplate({ reportId:this.$route.query.id,id:this.reportChartTemplateList[this.editObj].templateId,targetChart:'resDcir' },false)
						this.resDcirEditData = this._getInitData('resDcir','edit')
						this.resDcirEditData.series = _.cloneDeep(this.resDcirSeriesList)
						this.initResDcir()
						this.drawerVisible = false
						this.$message.success("重置成功")
					},
					onCancel() {}
				});
			},

			async handleChangeTemplate(targetObj){
				await this.getChartTemplateRelationList(this.$route.query.id,[targetObj])
				this.resDcirEditData = this._getInitData('resDcir','edit')
				this.resDcirEditData.series = _.cloneDeep(this.resDcirSeriesList)
				this.initResDcir()
				this.drawerVisible = false
			},

			// 获取编辑图表数据、原始图表数据
			_getInitData(targetObj,type = 'original'){
				const isEdit = type === 'edit'
				const templateParam = this.reportChartTemplateList[targetObj].templateParamJson

        const options = {
          chartTitle: isEdit && templateParam.chartTitle ? templateParam.chartTitle : 'DCR vs. Temperature',
					XTitle: isEdit && templateParam.XTitle ? templateParam.XTitle : 'Temperature (℃)', //X轴标题
					YTitle: isEdit && templateParam.YTitle ? templateParam.YTitle : 'DCR (mΩ)', //Y轴标题
					titleTop: isEdit && templateParam.titleTop ? templateParam.titleTop : 10,
					yTitleLetf: isEdit && templateParam.yTitleLetf ? templateParam.yTitleLetf : 30,

          legendWidth: isEdit && templateParam.legendWidth ? templateParam.legendWidth : 20,
          legendHeight: isEdit && templateParam.legendHeight ? templateParam.legendHeight : 5,
          legendGap: isEdit && templateParam.legendGap ? templateParam.legendGap : 10,//图例间隙
          legendOrient: isEdit && templateParam.legendOrient ? templateParam.legendOrient : 'horizontal',
          legendTop: isEdit && templateParam.legendTop ? templateParam.legendTop : 50,
          legendBgColor: isEdit && templateParam.legendBgColor ? templateParam.legendBgColor : '#f5f5f5',

          gridTop: isEdit && templateParam.gridTop ? templateParam.gridTop : 45,
          gridLeft: isEdit && templateParam.gridLeft ? templateParam.gridLeft : 55,
          gridRight: isEdit && templateParam.gridRight ? templateParam.gridRight : 30,
          gridBottom: isEdit && templateParam.gridBottom ? templateParam.gridBottom : 50,

          xType: isEdit && templateParam.xType ? templateParam.xType : "category",
          xMin: isEdit && templateParam.xMin ? templateParam.xMin : 0,
          xMax: isEdit && templateParam.xMax ? templateParam.xMax : 0,
          xInterval: isEdit && templateParam.xInterval ? templateParam.xInterval : 0,

          yType: isEdit && templateParam.yType ? templateParam.yType : "value",
          yMin: isEdit && templateParam.yMin ? templateParam.yMin : 0,
          yMax: isEdit && templateParam.yMax ? templateParam.yMax : 0,
          yInterval: isEdit && templateParam.yInterval ? templateParam.yInterval : 0,
        }
        if(type === 'edit'){
          options.series = []
          options.legend = []
          options.legendSort = []
          options.legendEditName = []
		  options.allData = templateParam.allData ?? {}
          if(templateParam.legendLeft) options.legendLeft = templateParam.legendLeft
        }
        if(type === 'original'){
          options.checkData = []
        }

        return options
      },

			// 处理模板值
			_handleTemplateParams(value){
				const isEdit = !!value.targetEditObj
				const templateParam = this.reportChartTemplateList['resDcir'].templateParamJson

				if(isEdit && !['legendList','legendEditName','legendSort','legendRevealList'].includes(value.targetEditObj)){
					if(value.targetEditIndex === undefined){
						templateParam[value.targetEditObj] = value[value.targetEditObj]
					}else if(value.targetEditIndex === 'all'){
						for(let i = 0; i < value.checkData.length ; i++){
							if(templateParam.checkData[i] === undefined) templateParam.checkData[i] = {}
							templateParam.checkData[i] = {
								...templateParam.checkData[i],
								id:value.checkData[i].id,
								[value.targetEditObj]:value.checkData[i][value.targetEditObj]
							}
						}
						templateParam.allData[value.targetEditObj] = value.allData[value.targetEditObj]
					}else{
						let haveIndex = templateParam.checkData.findIndex(item => item.id === value.checkData[value.targetEditIndex].id)
						if(haveIndex === -1){
							templateParam.checkData.push({id:value.checkData[value.targetEditIndex].id})
							haveIndex = templateParam.checkData.length - 1
						}
						templateParam.checkData[haveIndex][value.targetEditObj] = value.checkData[value.targetEditIndex][value.targetEditObj]
					}
				}

				if(!isEdit){
					if (value.targetResetIndex === undefined || value.targetResetIndex === 'yDecimalNum') {
						delete templateParam[value.targetResetObj]

						// 如果是XY轴的类型重置，需同步去除最大最小值
						if(value.targetResetObj === 'xType' && value.xType === 'category'){
							delete templateParam.xMin
							delete templateParam.xMax
							delete templateParam.xInterval
						}
						if(value.targetResetObj === 'yType' && value.yType === 'value'){
							delete templateParam.yMin
							delete templateParam.yMax
							delete templateParam.yInterval
						}
					} else if (value.targetResetIndex === 'all') {
						for(let i = 0; i < templateParam.checkData.length ; i++){
							delete templateParam.checkData[i][value.targetResetObj]
						}
						delete templateParam.allData[value.targetResetObj]
					} else {
						let haveIndex = templateParam.checkData.findIndex(item => item.id === value.checkData[value.targetResetIndex].id)
						if(haveIndex !== -1){
							delete templateParam.checkData[haveIndex][value.targetResetObj]
						}
					}
				}
				// 图例-数据
				if(value.targetEditObj === 'legendList'){
					templateParam.legendData.legendIndeterminate = value.legendIndeterminate
					templateParam.legendData.checkAll = value.checkAll
					templateParam.legendData.legendList = value.legendList
					templateParam.legendData.legendOptions = templateParam.legendData.legendOptions ?? this.resDcirLegendList
				}

				// 图例-名称
				if(value.targetEditObj === 'legendEditName'){
					templateParam.legendData.legendList = value.legendList // 需同步更名后的数组
					templateParam.legendData.legendEditName = value.legendEditName
					templateParam.legendData.legendRevealList = value.legendRevealList
					templateParam.legendData.legendRevealOptions = value.legendRevealOptions

					// 找到改名的那根线，存储修改后的线名称
					const haveIndex =  templateParam.checkData.findIndex(item => item.id === value.checkData[value.targetEditIndex].id)
					if(haveIndex === -1){
						templateParam.checkData.push({id:value.checkData[value.targetEditIndex].id})
						templateParam.checkData[templateParam.checkData.length - 1].name = value.checkData[value.targetEditIndex].name
					}else{
						templateParam.checkData[haveIndex].name = value.checkData[value.targetEditIndex].name
					}
				}

				// 如果有图例-排序的修改
				if(value.targetEditObj === 'legendSort'){
					templateParam.legendData.legendSort = value.legendSort
				}

				// 图例-显隐
				if(value.targetEditObj === 'legendRevealList'){
					templateParam.legendData.legendRevealIndeterminate = value.legendRevealIndeterminate
					templateParam.legendData.legendRevealcheckAll = value.legendRevealcheckAll
					templateParam.legendData.legendRevealList = value.legendRevealList
					templateParam.legendData.legendRevealOptions = value.legendRevealOptions
				}

				// 图例重置相关的处理
				if(['legendList','legendEditName','legendSort','legendRevealList'].includes(value.targetResetObj)){
					if(value.targetResetObj === 'legendList'){
						delete templateParam.legendData.legendList
						delete templateParam.legendData.checkAll
						delete templateParam.legendData.legendIndeterminate
					}
					if(value.targetResetObj === 'legendEditName'){
						delete templateParam.legendData.legendEditName
						delete templateParam.legendData.legendRevealList
					}
					if(value.targetResetObj === 'legendSort'){
						delete templateParam.legendData.legendSort
					}
					if(value.targetResetObj === 'legendRevealList'){
						delete templateParam.legendData.legendRevealList
					}
				}
			},

			// 获取模板参数
			_getTemplateParams(targetObj,titleData,gridData,legendData,axisData,legend){
				const templateParam = this.reportChartTemplateList[targetObj].templateParamJson

				const titleList = ['chartTitle', 'XTitle', 'YTitle', 'YTitle2', 'titleTop', 'yTitleLetf', 'yTitleRight']
				titleList.forEach(item => {
					titleData[item] = templateParam[item] ?? titleData[item]
				})

				const legendList = ['legendBgColor','legendOrient','legendTop', 'legendLeft','legendWidth', 'legendHeight', 'legendGap']
				legendList.forEach(item => {
					legendData[item] = templateParam[item] ?? legendData[item]
				})

				const gridList = ['gridTop', 'gridLeft', 'gridRight', 'gridBottom']
				gridList.forEach(item => {
					gridData[item] = templateParam[item] ?? gridData[item]
				})

				const axisList = ['xType','xMin', 'xMax', 'xInterval', 'yType', 'yMin', 'yMax', 'yInterval', 'yType2', 'yMin2', 'yMax2', 'yInterval2']
				axisList.forEach(item => {
					axisData[item] = templateParam[item] ?? axisData[item]
				})
				// 如果有图例-数据的修改
				if(templateParam.legendData.legendList){
					legendData.legendEdit = templateParam.legendData.legendList
					if(templateParam.legendData.checkAll !== undefined) this[`${targetObj}EditData`].checkAll = templateParam.legendData.checkAll
					if(templateParam.legendData.legendIndeterminate !== undefined) this[`${targetObj}EditData`].legendIndeterminate = templateParam.legendData.legendIndeterminate
				}

				// 如果有图例-名称的修改
				if(templateParam.legendData.legendEditName){
					legendData.legendEditName = templateParam.legendData.legendEditName
					legendData.legendRevealList = templateParam.legendData.legendRevealList
				}

				// 如果有图例-排序的修改
				if(templateParam.legendData.legendSort){
					legendData.legendSort = templateParam.legendData.legendSort
				}

				// 如果有图例-显隐的修改
				if(templateParam.legendData.legendRevealList){
					legendData.legendRevealList = templateParam.legendData.legendRevealList
					if(templateParam.legendData.legendRevealcheckAll !== undefined) this[`${targetObj}EditData`].legendRevealcheckAll = templateParam.legendData.legendRevealcheckAll
					if(templateParam.legendData.legendRevealIndeterminate !== undefined) this[`${targetObj}EditData`].legendRevealIndeterminate = templateParam.legendData.legendRevealIndeterminate
					if(templateParam.legendData.legendRevealOptions !== undefined) this[`${targetObj}EditData`].legendRevealOptions = templateParam.legendData.legendRevealOptions
				}

				return { titleData, gridData, legendData, axisData, legend }
			},
		}
	}
</script>
<style lang="less" scoped>
	@import "./css/preview.less";
</style>