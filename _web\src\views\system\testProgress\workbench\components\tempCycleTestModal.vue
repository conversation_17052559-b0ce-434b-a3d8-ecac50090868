<template>
  <a-modal
    :maskClosable="false"
    :title="`${modalData.testName}`"
    :visible="true"
    width="85%"
    :centered="true"
    :cancel-button-props="{ style: { display: 'none' } }"
    okText="关闭"
    @cancel="handleModelCancel"
    @ok="handleModelCancel">

    <div class="modal-wrapper">
      <a-descriptions title="详细信息"></a-descriptions>
      <div v-for="(singleSafetyTest, singleIndex) in originalData" :key="singleSafetyTest.id">
        <a-spin :spinning="modalLoading">
          <div>
            <a-descriptions title="">
              <a-descriptions-item label="测试项目名称">
                {{ singleSafetyTest.testAlias.replaceAll(/[0-9]/g, "") }}
              </a-descriptions-item>
              <a-descriptions-item>
                <template slot="label">
                  <span style="font-weight: bold">中检类型</span>
                </template>
                <div style="font-weight: bold;color: black">
                  {{
                    singleSafetyTest.middleCheck === "small"
                      ? "小中检"
                      : singleSafetyTest.middleCheck === "large"
                        ? "大中检"
                        : singleSafetyTest.middleCheck === "recharge"
                          ? "补电"
                          : "-"
                  }}
                </div>
              </a-descriptions-item>
              <a-descriptions-item label="测试设备">
                {{ singleSafetyTest.equiptCodeNames || "-" }}
              </a-descriptions-item>
              <a-descriptions-item label="计划开始时间">
                {{ singleSafetyTest.planStartTime || "-" }}
              </a-descriptions-item>
              <a-descriptions-item label="计划结束时间">
                {{ singleSafetyTest.planEndTime || "-" }}
              </a-descriptions-item>
              <a-descriptions-item>
                <template slot="label">
                  <span style="font-weight: bold">测试阶段</span>
                </template>
                <div style="font-weight: bold;color: black">
                {{
                    singleSafetyTest.stage
                      ? singleSafetyTest.stage === "0"
                        ? "初始性能检测"
                        : singleSafetyTest.stage
                      : "-"
                }}
                </div>
              </a-descriptions-item>
              <a-descriptions-item label="实际进箱时间" v-if="singleSafetyTest.stage !== '0'">
                <a-date-picker
                  placeholder="请选择实际进箱时间"
                  format="YYYY-MM-DD"
                  :disabled="modalData.taskStatus === '已完成'"
                  :allowClear="false"
                  @change="(date, dateString) => actualDateChange(date, dateString, singleSafetyTest ,'in')"
                  style="width: 170px;"
                  v-model="singleSafetyTest.actualInDate">
                </a-date-picker>
              </a-descriptions-item>
              <a-descriptions-item label="实际出箱时间" v-if="singleSafetyTest.stage !== '0'">
                <a-date-picker
                  placeholder="请选择实际出箱时间"
                  format="YYYY-MM-DD"
                  :disabled="modalData.taskStatus === '已完成'"
                  :allowClear="false"
                  @change="(date, dateString) => actualDateChange(date, dateString, singleSafetyTest ,'out')"
                  style="width: 170px;"
                  v-model="singleSafetyTest.actualOutDate">
                </a-date-picker>
              </a-descriptions-item>
            </a-descriptions>
          </div>
          <div>
            <div style="margin:10px 0px 15px 0px">
              <a-upload
                :disabled="modalData.taskStatus === '已完成' ? true : false"
                :headers="headers"
                :action="`/api/testProjectTodoTask/importOfAq`"
                name="file"
                :fileList="fileList"
                :data="{ safetyTestId: singleSafetyTest.id }"
                :showUploadList="false"
                accept="*"
                @change="handleUploadFile($event)"
              >
                <a-button
                  v-if="singleSafetyTest.testData"
                  :disabled="modalData.taskStatus === '已完成' || singleSafetyTest.testData.findIndex(v => v.batteryStatus === 'ongoing') === -1"
                  type="primary"
                  size="small"
                  class="mr10"
                  ghost
                >导入数据</a-button
                >
              </a-upload>
              <a-button class="mr10" type="primary" size="small" ghost v-if="singleSafetyTest.testData" @click="handleDownload($event,singleSafetyTest.stage, singleSafetyTest)">导出模板</a-button>
              <a-tooltip>
                <template slot="title">
                  提示内容
                </template>
                <a-button v-if="serialObj.serialPortOpen && singleSafetyTest.testData"  class="mr10" type="primary" size="small" ghost @click="handleCloseConnect">断开电压内阻测试仪</a-button>
                <a-button id="connectBtn" v-else-if="singleSafetyTest.testData" class="mr10" type="primary" size="small" ghost @click="handleOpenConnect">连接电压内阻测试仪</a-button>
              </a-tooltip>
              <a-upload
                name="file"
                :headers="headers"
                :customRequest="$event => handleUploadOfAttach($event, singleSafetyTest, singleIndex)"
                :data="uploadData"
                :disabled="modalData.taskStatus === '已完成' || 'uploading' === uploadingProgressList[singleIndex].uploadStatusOfAttach"
                :action="picOrVidPostUrl"
                :multiple="false"
                :showUploadList="false"
                @change="uploadAttachment($event, singleSafetyTest)">
                <a-button :loading="'uploading' === uploadingProgressList[singleIndex].uploadStatusOfAttach" v-if="singleSafetyTest.attachmentFlag === '1'" :disabled="modalData.taskStatus === '已完成'" class="mr10" type="primary" size="small" ghost @click="">导入过程数据</a-button>
              </a-upload>
            </div>
            <div class="auto-table">
              <div v-if="singleSafetyTest.videoFlag ==='1' || singleSafetyTest.attachmentFlag ==='1'" style="width: 100%;height: 50px;">
                <!-- 过程视频 -->
                <div v-if="singleSafetyTest.videoFlag ==='1'" style="margin-bottom: 0px">
                  <div style="border: 1px solid lightgrey;width: 100px;height:40px;float: left;display: flex;justify-content: center; align-items: center;">
                    <span style="color: black;">测试视频</span>
                  </div>

                  <div style="border: 1px solid lightgrey;width: 280px;height:40px;float: left;display: flex;justify-content: center; align-items: center;">
                    <span v-if="!singleSafetyTest.videoName" style="display: flex;justify-content: center; align-items: center;">
                      <a-upload
                        name="file"
                        :headers="headers"
                        :customRequest="$event => handleUpload($event, singleSafetyTest, singleIndex)"
                        :data="uploadData"
                        :action="picOrVidPostUrl"
                        :before-upload="beforeUploadVideo"
                        :multiple="false"
                        :disabled="modalData.taskStatus === '已完成' || 'uploading' === uploadingVideoProgressList[singleIndex].uploadStatus"
                        :showUploadList="false"
                        @change="uploadVideo($event, singleSafetyTest)"
                        accept=".mp4,.avi,.wmv,.mov">
                        <a-spin :spinning="'uploading' === uploadingVideoProgressList[singleIndex].uploadStatus"><a :disabled="modalData.taskStatus === '已完成'">上传</a></a-spin>
                      </a-upload>

                    </span>
                    <span v-else style="display: flex;justify-content: center; align-items: center;">
                      <a style="color: green;text-align: center;" @click="openFileOrDownload(singleSafetyTest.videoId, singleSafetyTest.videoName)">{{ singleSafetyTest.videoName }}</a>
                        <a-popconfirm
                          placement="topRight"
                          ok-text="删除"
                          cancel-text="取消"
                          @confirm="deleteVideo($event, singleSafetyTest)">
                          <template slot="title"> 确认删除视频"{{ singleSafetyTest.videoName }}"吗 </template>
                          <a-icon v-if="modalData.taskStatus !== '已完成'"  type="close" style="float: right;padding: 5px 0px 0px 5px" />
                        </a-popconfirm>
                  </span>
                  </div>
                  <div style="float: right;width: 22%;padding-top: 0px;" v-show="'uploading' === uploadingVideoProgressList[singleIndex].uploadStatus">
                    测试视频<a-progress :strokeWidth=12 :percent="uploadingVideoProgressList[singleIndex].percent" status="active"></a-progress>
                  </div>
                </div>

                <!-- 过程数据 -->
                <div v-if="singleSafetyTest.attachmentFlag ==='1'" style="margin-bottom: 0px">
                  <div style="border: 1px solid lightgrey;width: 100px;height:40px;float: left;display: flex;justify-content: center; align-items: center;">
                    <span style="color: black;">过程数据</span>
                  </div>
                  <div style="border: 1px solid lightgrey;width: 400px;height:40px;float: left;display: flex;justify-content: center; align-items: center;">
                    <span style="display: flex;justify-content: center; align-items: center;">
                      <span v-for="fileItem in singleSafetyTest.attachment">
                        <a style="color: green;text-align: center;" @click="openFileOrDownload(fileItem.id, fileItem.name)">{{ fileItem.name }}</a>
                        <a-popconfirm
                          placement="topRight"
                          ok-text="删除"
                          cancel-text="取消"
                          @confirm="deleteAttachment($event, singleSafetyTest, fileItem)">
                          <template slot="title"> 确认删除过程数据"{{ fileItem.name }}"吗 </template>
                          <a-icon v-if="modalData.taskStatus !== '已完成'"  type="close" style="float: right;padding: 5px 0px 0px 5px" />
                        </a-popconfirm>
                      </span>
                    </span>
                  </div>
                  <div style="float: right;width: 22%;padding-top: 0px;" v-show="'uploading' === uploadingProgressList[singleIndex].uploadStatusOfAttach">
                    过程数据<a-progress :strokeWidth=12 :percent="uploadingProgressList[singleIndex].percentOfAttach" status="active"></a-progress>
                  </div>
                </div>

              </div>
              <a-table
                bordered
                class="mt10"
                v-if="singleSafetyTest.testData"
                :columns="columnList[singleIndex]"
                :rowKey="record => record.cellTestCode"
                :data-source="singleSafetyTest.testData"
                :pagination="false"
              >
								<span v-for="item in columnDataList[singleIndex]" :key="item.cellTestCode" :slot="item.dataIndex" slot-scope="text, record, index">
									<span v-if="item.dataIndex === 'cellTestCode'">
										<div class="blue hand" @click="handleCopy(text)">{{ text }}<a-icon type="copy" /></div>
									</span>
									<span v-else-if="item.dataIndex === 'alias'">
										<div class="blue hand" @click="aliasCopy(singleSafetyTest)">{{ text }}<a-icon type="copy" /></div>
									</span>
                  <!-- class:singleIndex:某一步，middleCheck：对应的字段，index：那行,为了未填写标红 -->
									<span :id="`${singleIndex}-middleCheck-${index}`" v-else-if="item.dataIndex === 'middleCheck'">
										<a-button
                      v-if="singleSafetyTest.middleCheck !== 'normal'"
                      type="link"
                      :disabled="record.batteryStatus !== 'ongoing'"
                      :style="record.isMiddleClick ? 'color:green' : ''"
                      @click="() => chooseMgData(record, `${singleIndex}-middleCheck-${index}`, singleSafetyTest, singleSafetyTest.testData)"
                    >
											{{ record.isMiddleClick ? (record.checkData === null ? '无匹配数据' : (text === "large" ? "大中检" : text === "small" ? "小中检" : "补电")) : "请选择数据"
                      }}<a-icon v-if="record.isMiddleClick" type="edit" />
										</a-button>
										<span v-else type="link">-</span>
									</span>

                  <!-- 电芯状态 -->
                  <span v-else-if="item.dataIndex === 'batteryStatus'">
                    <a-select v-model="record.batteryStatus" @change="changeBatteryStatus(singleSafetyTest, record)"
                              :disabled="modalData.taskStatus === '已完成' || record.batteryStatus !== 'ongoing'"
                              style="width: 160px;font-size: 14px;">
                      <a-select-option value="ongoing">
                        进行中
                      </a-select-option>
                      <a-select-option value="testDone">
                        状态正常-测试完成
                      </a-select-option>
                      <a-select-option value="earlyEnd">
                        状态正常-提前结束
                      </a-select-option>
                      <a-select-option value="batteryDisassembly">
                        状态正常-电池拆解
                      </a-select-option>
                      <a-select-option value="pressureDrop">
                        掉压失效-终止测试
                      </a-select-option>
                      <a-select-option value="abnormalHot">
                        异常发热-终止测试
                      </a-select-option>
                      <a-select-option value="openShellAndLeak">
                        开壳漏液-终止测试
                      </a-select-option>
                      <a-select-option value="shellRust">
                        壳体生锈-终止测试
                      </a-select-option>
                      <a-select-option value="operationError">
                        作业错误-终止测试
                      </a-select-option>
                      <a-select-option value="thermalRunaway">
                        热失控-终止测试
                      </a-select-option>
                      <a-select-option value="acrException">
                        内阻异常-终止测试
                      </a-select-option>
                    </a-select>
                  </span>

                  <!-- 驳回信息 -->
                  <span v-else-if="item.dataIndex === 'rejectMsg'">
                    <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
                      <template slot="title">
                        {{text}}
                      </template>
                      <div style="max-width: 100px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;color: red;">
                        {{text}}
                      </div>
                    </a-tooltip>
                  </span>

                  <!-- 图片上传 -->
                  <span v-else-if="picOrVidMenu[item.dataIndex]">
                    <span v-if="record.batteryStatus !== 'ongoing' && !record.samplePicture[item.dataIndex].id ">
                      <a style="color: darkgrey;">上传</a>
                    </span>
                    <span v-else-if="!record.samplePicture[item.dataIndex].id && modalData.taskStatus !== '已完成'" style="display: flex;justify-content: center; align-items: center;">
                      <a-upload
                        name="file"
                        :headers="headers"
                        :data="uploadData"
                        :action="picOrVidPostUrl"
                        :before-upload="beforeUploadPicture"
                        :multiple="false"
                        :showUploadList="false"
                        @change="uploadPicture($event, item.dataIndex, index, singleSafetyTest)"
                        accept=".jpg,.png,.gif">
                        <a>上传</a>
                      </a-upload>
                    </span>
                    <span v-else-if="record.samplePicture[item.dataIndex].uid">
                      <a-upload
                        list-type="picture-card"
                        class="avatar-uploader"
                        :disabled="modalData.taskStatus === '已完成' || record.batteryStatus !== 'ongoing'"
                        @change="deletePicture(item.dataIndex, index, singleSafetyTest)"
                        :fileList="[record.samplePicture[item.dataIndex]]"
                        @preview="handlePreview">
                      </a-upload>
                    </span>
                    <span v-else>
                      <a style="color: green" @click="openFileOrDownload(record.samplePicture[item.dataIndex].id,record.samplePicture[item.dataIndex].name)">{{ record.samplePicture[item.dataIndex].name }}</a>
                        <a-popconfirm
                          placement="topRight"
                          ok-text="删除"
                          cancel-text="取消"
                          @confirm="deletePicture(item.dataIndex, index, singleSafetyTest)">
                          <template slot="title"> 确认删除文件"{{ record.samplePicture[item.dataIndex].name }}"吗 </template>
                          <a-icon v-if="modalData.taskStatus !== '已完成'" type="close" style="float: right;padding: 5px 0px 0px 5px" />
                        </a-popconfirm>
                     </span>
                  </span>

                  <!-- 输入框上传 -->
                  <a-input
                    v-else
                    :id="`${singleIndex}-${item.dataIndex}-${index}`"
                    v-model="singleSafetyTest.testData[index][item.dataIndex]"
                    :disabled="(modalData.taskStatus === '已完成' ? true : false) || record.batteryStatus !== 'ongoing'"
                    @paste="copyFromExcel($event, columnList[singleIndex], singleSafetyTest.testData, index, item.dataIndex)"
                    @keyup.enter="handleWrite(item.dataIndex,index,`${singleIndex}-${item.dataIndex}-${index + 1}`, singleSafetyTest, singleSafetyTest.testData)"
                    @blur="value => handleInput(value.target._value, `${singleIndex}-${item.dataIndex}-${index}`, index, singleSafetyTest, singleSafetyTest.testData)"
                  />

								</span>
              </a-table>
            </div>
          </div>
          <a-divider v-if="singleIndex !== originalData.length - 1" style="font-weight: bold;color: black" />

       </a-spin>
      </div>
    </div>
    <!-- 底部按钮 -->
    <template slot="footer">
      <div>
        <a-button :disabled="modalData.taskStatus === '已完成'" type="primary" @click="handleSaveTestData()">保存</a-button>
        <a-popconfirm
          placement="top"
          ok-text="确认"
          cancel-text="取消"
          @confirm="handleSubmit"
        >
          <template slot="title">
            <p>确认完成吗？</p>
          </template>
          <a-button v-if="modalData.taskStatus !== '已完成'" type="primary">完成</a-button>
        </a-popconfirm>
        <a-button @click="handleModelCancel">关闭</a-button>
      </div>
    </template>
    <step-data ref="stepData"></step-data>
    <div>
<!--      <a-modal-->
<!--        :maskClosable="false" title="条码启动天数输入" :visible="isShowDays" @ok="handleModalOk" @cancel="handleModalCancel">-->
<!--        <a-form :label-col="{ span: 11 }" :wrapper-col="{ span: 13 }">-->
<!--          <a-form-item label="启动天数">-->
<!--            <a-input-number v-model="startDay" :precision="0" :min="0" @keyup.enter="handleModalOk" />-->
<!--          </a-form-item>-->
<!--        </a-form>-->
<!--      </a-modal>-->

    </div>
    <!-- 测试数据选择弹窗 start  -->
    <div>
      <a-modal
        title="测试数据选择"
        width="90%"
        :height="300"
        :bodyStyle="{ padding: 0 }"
        :visible="mgVisible"
        style="padding: 0"
        :maskClosable="false"
        :centered="true"
        @cancel="handleCloseModal"
        destroyOnClose
      >
        <div class="child-table">
          <a-table
            :columns="mgColumns"
            :dataSource="mgData"
            class="mt10"
            bordered
            :rowKey="record => record.flowId"
            :pagination="false"
            :rowSelection="{
							type: 'radio',
							onSelect: selectTestData,
							getCheckboxProps: getCheckboxProps
						}"
          >
            <template slot="celltestcode" slot-scope="text, record, index, columns">
              <a @click="openStepData(record)" style="text-align: center">{{ text }}</a>
            </template>
          </a-table>
        </div>
        <template slot="footer" slot-scope="text, record">
          <a-button key="back" @click="handleCloseModal">
            关闭
          </a-button>
        </template>
      </a-modal>
    </div>
    <!-- 测试数据选择弹窗 end  -->
    <!-- 预览视频/图片  -->
    <a-drawer
      :bodyStyle="{ height: '100%' }"
      placement="right"
      :closable="false"
      width="70%"
      :visible="filePreviewVisible"
      @close="filePreviewVisible = false"
    >
      <iframe :src="iframeUrl" width="100%" height="100%"></iframe>
    </a-drawer>

    <div>
      <a-modal :visible="previewVisible" :footer="null" @cancel="handlePreviewCancel">
        <img alt="example" style="width: 100%" :src="previewImage"/>
      </a-modal>
    </div>

  </a-modal>

</template>

<script>
import moment from "moment"
import { STable } from "@/components"
import {
  aliasCopyOfAq,
  exportModelOfSafetyTest,
  finishSafetyTestTodoTask,
  getSafetyTestByTask, handleSaveSafetyTestData, updateBatteryStatusOfTc,
  updatePicOrVidOfAq,
  updateSafetyTestData,
} from "@/api/modular/system/testProgressManager"

import { mixin } from "../mixin/index"

import { downloadfile1 } from "@/utils/util"
import { formatDate } from "@/utils/format";
import { tLimsTestdataScheduleList } from "@/api/modular/system/limsManager";
import { getMinioPreviewUrl } from "@/api/modular/system/fileManage";
import axios from "axios";
import Vue from "vue";

export default {
  components: {
    STable
  },
  data() {
    return {
      previewImage: '',
      previewVisible: false,
      originalData:[],
      uploadingVideoProgressList: [],
      columnList:[],
      columnDataList:[],
      showProgressBar:false,
      uploadingStage:null,
    }
  },
  mixins: [mixin],
  created() {
    this.loadSafetyTestByTask(true)
  },

  methods: {
    loadSafetyTestByTask(init = false) {
      this.modalLoading = true
      getSafetyTestByTask(this.modalData)
        .then(res => {
          if (!res.success) return this.$message.error("错误提示：" + res.message)
          this.originalData = res.data

          this.originalData.forEach(o => {
            if (o.testData) {
              o.testData = JSON.parse(o.testData)
            }
            if (o.attachment) {
              o.attachment = JSON.parse(o.attachment)
            } else {
              o.attachment = []
            }
          })

          for (const resKey in this.originalData) {
            let curTestDatas = this.originalData[resKey].testData
            if (!curTestDatas) {
              continue
            }
            curTestDatas.forEach(v => {
              // 图片预览增加token
              if (v.samplePicture) {
                Object.keys(v.samplePicture).forEach(function(key) {
                  let authString = "&Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************.P1JWgtRVk1sTPPLiCgZNuleYyPZRf2ooByC_mmu9scs6SVbpJHgSsKd8AtscjDwg3Fw7D4QN31vgtA5jeedj3g"
                  if (v.samplePicture[key].thumbUrl && v.samplePicture[key].thumbUrl.indexOf(authString) === -1) {
                    v.samplePicture[key].thumbUrl += authString
                  }
                });
              }
              // 有中检，且没有选择数据
              if (v.isMiddleClick === null || v.isMiddleClick === undefined || v.isMiddleClick === '') {
                v.isMiddleClick = (v.middleCheck !== "normal" && v.checkData) || (this.modalData.taskStatus === "已完成")
                  ? true
                  : false
                //方便后续校验
                if (v.middleCheck === "normal") v.isMiddleClick = true
              }
            })
          }

          this.originalData.forEach(data => {
            this.columnList.push(data.testData ? this.handleColums(data.testData)[0] : "")
            this.columnDataList.push(data.testData ? this.handleColums(data.testData)[1] : "")
          })

        })
        .finally(() => {
          this.modalLoading = false
          if (init) {
            this.originalData.forEach(data => {
              if (data.testData && data.middleCheck !== 'normal') {
                this._handleMiddleCheck(data, data.testData)
              }
            })
            for (let i = 0; i < this.originalData.length; i++) {
              this.uploadingProgressList.push({
                uploadStatusOfAttach: 'done',
                percentOfAttach: 0,
              })
              this.uploadingVideoProgressList.push({
                uploadStatus: 'done',
                percent: 0,
              })
            }
          }
        })
    },

    // 处理表头新
    handleColums(value) {
      let rejectMsgFlag = value.some(item => item.hasOwnProperty('rejectMsg'));
      let temColuns = []
      const temList = []

      for (let i in value[0]) {
        const temObj = {
          title: this.tableNameMenu[i],
          dataIndex: i,
          width: "100px",
          align: "center",
          scopedSlots: {
            customRender: i
          }
        }

        const childrenObj = {
          title: i.replaceAll(/[^0-9]/g, ""),
          dataIndex: i,
          width: "100px",
          align: "center",
          scopedSlots: {
            customRender: i
          }
        }

        const childrenObj1 = {
          title: this.tableNameMenu[i.replaceAll(/[0-9]/g, "")] + i.replaceAll(/[^0-9]/g, ""),
          dataIndex: i,
          width: "100px",
          align: "center",
          scopedSlots: {
            customRender: i
          }
        }

        const temObj1 = {
          title: this.tableNameMenu[i.replaceAll(/[0-9]/g, "")],
          dataIndex: i.replaceAll(/[0-9]/g, ""),
          width: "100px",
          align: "center",
          children: [childrenObj]
        }

        if (
          (i === "middleCheck" && value[0][i] === "normal") ||
          i === "heightType" ||
          i === "isMiddleClick" ||
          i === "checkData" ||
          i === "timeOfFillInnerres" ||
          i === "timeOfFillInnerres2" ||
          i === "samplePicture" ||
          i === "rejectMsg" ||
          i === "safetyTestReviewId"
        )
          continue


        if (i.match(/\d/g)) {
          const temIndex = temColuns.findIndex(v => v.dataIndex === i.replaceAll(/[0-9]/g, ""))
          if (temIndex === -1) {
            temColuns.push(temObj1)
            temList.push(childrenObj1)
          } else {
            temColuns[temIndex].children.push(childrenObj)
            temList.push(childrenObj1)
          }
        } else {
          temColuns.push(temObj)
          temList.push(temObj)
        }
      }

      const chilList = []
      const temColuns1 = []
      // 划分尺寸表头
      temColuns.forEach(v => {
        if (v.dataIndex === "timeOfFillInnerres" || v.dataIndex === "timeOfFillInnerres2") {
          return
        }
        if (
          v.dataIndex === "cellTestCode" ||
          v.dataIndex === "alias" ||
          v.dataIndex === "middleCheck" ||
          v.dataIndex === "batteryStatus" ||
          v.dataIndex === "beforeVoltage" ||
          v.dataIndex === "beforeInnerres" ||
          v.dataIndex === "afterVoltage" ||
          v.dataIndex === "afterInnerres" ||
          v.dataIndex === "volume" ||
          v.dataIndex === "weight" ||
          v.dataIndex === "heightType" ||
          v.dataIndex === "isolateres"
        ) {
          if (v.dataIndex === "alias" || v.dataIndex === "isolateres" || v.dataIndex === "batteryStatus") {
            v.width = "100px"
          }
          if (v.dataIndex === "cellTestCode") {
            v.width = "175px"
          }
          return temColuns1.push(v)
        }
        chilList.push(v)
      })

      temColuns = temColuns1

      if (chilList.length !== 0) {
        temColuns.push({
          title: "尺寸/mm",
          dataIndex: "dimension",
          align: "center",
          children: chilList
        })
      }

      // 增加样品照片列
      const pictureChilList = []
      if (value[0].samplePicture) {
        for (let i in value[0].samplePicture) {
          const temObj = {
            title: this.picOrVidMenu[i],
            dataIndex: i,
            align: "center",
            width: "100px",
            scopedSlots: {
              customRender: i
            }
          }
          pictureChilList.push(temObj)
          temList.push(temObj)
        }
      }
      if (pictureChilList.length !== 0) {
        temColuns.push({
          title: "样品照片",
          dataIndex: "samplePicture",
          align: "center",
          children: pictureChilList
        })
      }

      // 获取【中检类型】列索引
      let insertIndexC = temColuns.findIndex(item => item.dataIndex === 'middleCheck');
      let insertIndexL = temList.findIndex(item => item.dataIndex === 'middleCheck');
      if (insertIndexC === -1 || insertIndexL === -1) {
        // 无【中检类型】列则获取【测试编码】列索引
         insertIndexC = temColuns.findIndex(item => item.dataIndex === 'cellTestCode');
         insertIndexL = temList.findIndex(item => item.dataIndex === 'cellTestCode');
      }
      if (insertIndexC !== -1 && insertIndexL !== -1) {
        // 获取【电芯状态】列
        let batteryTitleC = temColuns.filter(item => item.dataIndex === 'batteryStatus');
        let batteryTitleL = temList.filter(item => item.dataIndex === 'batteryStatus');
        // 移除【电芯状态】列
        let indexToRemoveC = temColuns.findIndex(item => item.dataIndex === 'batteryStatus');
        let indexToRemoveL = temList.findIndex(item => item.dataIndex === 'batteryStatus');
        if (indexToRemoveC !== -1) {
          temColuns.splice(indexToRemoveC, 1);
        }
        if (indexToRemoveL !== -1) {
          temList.splice(indexToRemoveL, 1);
        }
        // 将【电芯状态】列添加到【中检类型】/【测试编码】列后面
        if (batteryTitleC.length > 0 && batteryTitleL.length > 0) {
          temColuns.splice(insertIndexC + 1, 0, batteryTitleC[0]);
          temList.splice(insertIndexL + 1, 0, batteryTitleL[0]);
          // 在【电芯状态】列后添加【驳回信息】列
          if (rejectMsgFlag) {
            let rejectMsgItem = {
              title: "驳回信息",
              dataIndex: "rejectMsg",
              width: "100px",
              align: "center",
              scopedSlots: {
                customRender: "rejectMsg"
              }
            }
            temColuns.splice(insertIndexC + 2, 0, rejectMsgItem);
            temList.splice(insertIndexL + 2, 0, rejectMsgItem);
          }
        }
      }
      return [temColuns, temList]
    },

    handleUploadFile(info) {
      this.fileList = [...info.fileList]
      if (info.file.response.success) {
        this.loadSafetyTestByTask()
        this.$message.success(`${info.file.name} 数据导入成功`)
      } else {
        this.$message.error(`${info.file.name} 数据导入失败:` + info.file.response.message)
      }
      this.$forceUpdate()
    },

    handleDownload($event, stage, curSafetyTest) {
      exportModelOfSafetyTest({ safetyTestId: curSafetyTest.id }).then(res => {
        const fileName = `${this.modalData.folderNo}-${this.modalData.wtrName}-${this.modalData.testName}-${stage == 0 ? "初始性能检测" : "测试阶段" + curSafetyTest.stage}${curSafetyTest.middleCheck === "small"
          ? "-小中检"
          : curSafetyTest.middleCheck === "large"
            ? "-大中检"
            : curSafetyTest.middleCheck === "recharge" ? "-补电" : ""}.xlsx`
        if (res) {
          downloadfile1(res, fileName)
        }
      })
    },

    deleteAttachment(event, curSafetyTest, fileItem) {
      curSafetyTest.attachment = curSafetyTest.attachment.filter(o => o.id !== fileItem.id)
      let update = {}
      update.id = curSafetyTest.id
      update.attachment = JSON.stringify(curSafetyTest.attachment)
      updatePicOrVidOfAq(update, 'delete', 'attachment', -1).then(res => {
        if (res.success) {
          this.$message.success("删除成功")
        } else {
          this.$message.error("删除失败：" + res.message)
        }
      })
    },

    uploadAttachment(info, curSafetyTest) {
      this.uploadingStage = curSafetyTest.stage
      this.attachLoading = true
      if (info.file.status === "done") {
        let file = info.file
        let update = {}
        curSafetyTest.attachment.push({
          id: file.response.data,
          name: file.name,
          time: formatDate(new Date(), true)
        })
        update.id = curSafetyTest.id
        update.attachment = JSON.stringify(curSafetyTest.attachment)
        updatePicOrVidOfAq(update, 'add', 'attachment', -1).then(res => {
          if (res.success) {
            this.$message.success(`${info.file.name} 上传成功`)
          } else {
            this.$message.error("上传失败：" + res.message)
          }
          setTimeout(() => {
            this.attachLoading = false
          },500)
        })
      } else if (info.file.status === "error") {
        this.attachLoading = false
        this.$message.error(`${info.file.name} 上传失败`)
      } else {
        this.attachLoading = false
      }
    },

    handleUpload(options, singleSafetyTest, index) {
      const { file, onSuccess, onError } = options;
      const formData = new FormData();
      formData.append('file', file);
      formData.append('bucket','safetylab');

      axios.post('/api/sysFileInfo/minioUpload', formData, {
        headers: {
          // 'Content-Type': 'multipart/form-data;',
          Authorization: 'Bearer ' + Vue.ls.get('Access-Token'),
        },
        // data:this.uploadData,
        onUploadProgress: (progressEvent) => {



          this.videoLoading = true
          this.uploadingVideoProgressList[index].uploadStatus = 'uploading'
          if (progressEvent.total > 0) {
            this.uploadingVideoProgressList[index].percent = Math.round((progressEvent.loaded / progressEvent.total ) * 100)  == 100?99:Math.round((progressEvent.loaded / progressEvent.total ) * 100);
          }
        },
      })
        .then((response) => {
          onSuccess(response.data, file);
          this.uploadingVideoProgressList[index].percent = 100;
          setTimeout(() => {
            this.videoLoading = false
            this.uploadingVideoProgressList[index].percent = 0;
            this.uploadingStage = null
            this.uploadingVideoProgressList[index].uploadStatus = 'done'
          }, 2000)
          // 重置进度条
        })
        .catch((error) => {
          onError(error);
          this.uploadingVideoProgressList[index].percent = 0; // 重置进度条
        });
    },
    uploadVideo(info, curSafetyTest) {
      this.uploadingStage = curSafetyTest.stage
      this.videoLoading = true
      if (info.file.status === "done") {
        let file = info.file
        let update = {}
        update.id = curSafetyTest.id
        update.videoId = file.response.data
        update.videoName = file.name
        updatePicOrVidOfAq(update, 'add', 'video', -1).then(res => {
          if (res.success) {
            curSafetyTest.videoId = file.response.data
            curSafetyTest.videoName = file.name
            this.$message.success(`${info.file.name} 上传成功`)
          } else {
            this.$message.error("上传失败：" + res.message)
          }
          setTimeout(() => {
            this.videoLoading = false
          },500)
        })
      } else if (info.file.status === "error") {
        this.videoLoading = false
        this.$message.error(`${info.file.name} 上传失败`)
      } else {
        this.videoLoading = false
      }
    },

    deleteVideo(event, curSafetyTest) {
      let update = {}
      update.id = curSafetyTest.id
      updatePicOrVidOfAq(update, 'delete', 'video', -1).then(res => {
        if (res.success) {
          curSafetyTest.videoId = null
          curSafetyTest.videoName = null
          this.$message.success("删除成功")
        } else {
          this.$message.error("删除失败：" + res.message)
        }
      })
    },

    async handleWrite(dataIndex, row, focusId, curSafetyTest, testData){
      if(!this.serialObj.serialPort || !this.serialObj.serialReader || !this.serialObj.serialWriter || !this.serialObj.serialPortOpen)  return

      // 如果焦点在的位置是内阻、电压
      if(dataIndex == 'beforeInnerres' || dataIndex == 'beforeVoltage' || dataIndex == 'afterInnerres' || dataIndex == 'afterVoltage'){
        const serialValue = await this.writeToSerial()

        if(dataIndex == 'beforeInnerres' || dataIndex == 'beforeVoltage'){
          testData[row].beforeInnerres = Number(serialValue.split(',')[0] * 1000)//由于之前工作的excel模板上的单位为欧，所以测试设备输出的数据，自动除于1000，故此处需要还原  1mΩ === 0.001Ω
          testData[row].beforeVoltage = Number(serialValue.split(',')[1] * 1000)
          testData[row].timeOfFillInnerres = formatDate(new Date(), true)
        }else{
          testData[row].afterInnerres = Number(serialValue.split(',')[0] * 1000)  //由于之前工作的excel模板上的单位为欧，所以测试设备输出的数据，自动除于1000，故此处需要还原  1mΩ === 0.001Ω
          testData[row].afterVoltage = Number(serialValue.split(',')[1] * 1000)
          testData[row].timeOfFillInnerres2 = formatDate(new Date(), true)
        }
        const params = {
          id: curSafetyTest.id,
          testData: JSON.stringify(testData)
        }
        this.updateSafetyTestData(params)
      }

      if(row !== testData.length - 1){
        document.getElementById(focusId).focus();
      }
    },

    handleInput(value, id, row, curSafetyTest, testData) {
      // value:填写的值，id：对应的id，row：那一行数据发生修改

      if (value !== "" && document.getElementById(id).style.backgroundColor === "rgb(255, 233, 237)") {
        document.getElementById(id).style.backgroundColor = "transparent"
      }
      if (id.indexOf('before') !== -1 && value) {
        testData[row].timeOfFillInnerres = formatDate(new Date(), true)
      }
      if (id.indexOf('after') !== -1 && value) {
        testData[row].timeOfFillInnerres2 = formatDate(new Date(), true)
      }
      let property =  Object.getOwnPropertyNames(testData[row])
      let testDataColumn = ['beforeVoltage','afterVoltage','beforeInnerres','afterInnerres']
      if (property.findIndex(item => testDataColumn.indexOf(item) > -1) === -1) { //如果没有电压内阻和中检后电压内阻（即只有尺寸重量）
        if (property.findIndex(item => item === 'weight') === -1) { // 如果测试内容只有尺寸，timeOfFillInnerres取填写尺寸的时间
          if (value) {
            testData[row].timeOfFillInnerres = formatDate(new Date(), true)
          }
        } else { // 如果测试内容为①只有重量和尺寸②只有重量，timeOfFillInnerres取填写重量的时间
          if (id.indexOf('weight') !== -1 && value) {
            testData[row].timeOfFillInnerres = formatDate(new Date(), true)
          }
        }
      }
      const params = {
        id: curSafetyTest.id,
        testData: JSON.stringify(testData)
      }
      this.updateSafetyTestData(params)
    },

    updateSafetyTestData(params) {
      updateSafetyTestData(params).then(res => {
        if (!res.success) return this.$message.error("错误提示：" + res.message)
      })
    },

    async changeBatteryStatus(curSafetyTest, changeBattery) {
      // 选择电芯状态为【状态正常-测试完成】时需校验数据完整性
      if (changeBattery.batteryStatus === "testDone") {
        let singleIndex = this.originalData.indexOf(curSafetyTest)
        let stageAndTestCode = (singleIndex === 0 ? "初始性能检测" : "第" + singleIndex + "阶段") + changeBattery.cellTestCode + "："
        let curTestData = curSafetyTest.testData.filter(item => item.cellTestCode === changeBattery.cellTestCode)
        if (!(await this._handleIsNull(curTestData))[0] || curTestData.findIndex(v => !v.isMiddleClick) !== -1) {
          this.loadSafetyTestByTask()
          return this.$warning({
            content: stageAndTestCode + "请将数据填写完整"
          })
        } else {
          if (!(await this._handleIsNull(curTestData))[1]) {
            this.loadSafetyTestByTask()
            return this.$warning({
              content: stageAndTestCode + "请先将尺寸数据填写完整"
            })
          }
        }
        let noFillPicture = this.validPicture(curSafetyTest, curTestData)
        if (noFillPicture) {
          this.loadSafetyTestByTask()
          return this.$warning({
            content: stageAndTestCode + "每个电芯至少需要上传一张照片"
          })
        }
        const regex = /^-?\d+(\.\d+)?$/
        let errorDigitFormatIndex = curTestData.findIndex(v => v.weight && !regex.test(v.weight))
        if (errorDigitFormatIndex !== -1) {
          this.loadSafetyTestByTask()
          return this.$warning({
            content: stageAndTestCode + "请确认重量的数据格式"
          })
        }
      }
      const params = {
        id: curSafetyTest.id,
        cellTestCode: changeBattery.cellTestCode,
        batteryStatus: changeBattery.batteryStatus,
        todoTaskId: this.modalData.id
      }
      updateBatteryStatusOfTc(params).then(res => {
        if (res.success) {
          this.loadSafetyTestByTask()
        } else {
          this.loadSafetyTestByTask()
          return this.$error({ content: res.message })
        }
      })
    },

    actualDateChange (date, dateString, curSafetyTest, type) {
      const params = {
        id: curSafetyTest.id,
        actualInDate: null,
        actualOutDate: null
      }
      if (type === 'in') {
        params.actualInDate = dateString
      } else {
        params.actualOutDate = dateString
      }
      this.updateSafetyTestData(params)
    },

    uploadPicture(info, field, index, curSafetyTest) {
      if (info.file.status === "done") {
        let file = info.file
        let update = {}
        update.id = curSafetyTest.id
        update.pictureId = file.response.data
        update.pictureName = file.name
        this.modalLoading = true
        updatePicOrVidOfAq(update, 'add', field, index).then(res => {
          if (res.success) {
            this.loadSafetyTestByTask()
            this.$message.success(`${info.file.name} 上传成功`)
          } else {
            this.$message.error("上传失败：" + res.message)
          }
          setTimeout(() => {
            this.modalLoading = false
          },500)
        })
      } else if (info.file.status === "error") {
        this.$message.error(`${info.file.name} 上传失败`)
      }
    },

    deletePicture(field, index, curSafetyTest) {
      let update = {}
      update.id = curSafetyTest.id
      this.modalLoading = true
      updatePicOrVidOfAq(update, 'delete', field, index).then(res => {
        if (res.success) {
          this.loadSafetyTestByTask()
          this.$message.success("删除成功")
        } else {
          this.$message.error("删除失败：" + res.message)
        }
        setTimeout(() => {
          this.modalLoading = false
        },500)
      })
    },

    // 点击中检按钮,查找对应的数据
    chooseMgData(record, dataIndex, curSafetyTest, testData) {
      this.currentSafetyTest = curSafetyTest
      this.currentTestData = testData
      if (document.getElementById(dataIndex).style.backgroundColor === "rgb(255, 233, 237)") {
        document.getElementById(dataIndex).style.backgroundColor = "transparent"
      }

      // 如果已经有checkData的数据，获取flowId
      if (record.checkData) this.flowId = JSON.parse(record.checkData).flowId

      // 测试数据
      // tLimsTestdataScheduleList({ celltestcode: "04QCE34221101HD152126077-202303090041-0001", alias: "日历寿命1" })
      //正式情况
      tLimsTestdataScheduleList({ celltestcode: record.cellTestCode || String(new Date()), alias: record.alias })
        .then(res => {
          if (res.success) {
            this.mgData = res.data
            if (res.data.length === 0 || res.data[0].flowId === '' || res.data[0].flowId === null) {
              record.isMiddleClick = true
              const params = {
                id: curSafetyTest.id,
                testData: JSON.stringify(testData)
              }
              this.updateSafetyTestData(params)
            }
          } else {
            this.$message.error("查询失败：" + res.message)
          }
        })
        .finally(() => {
          this.mgVisible = true
        })
    },

    // 中检弹窗 选中数据
    selectTestData(record) {
      // 测试数据
      // this.currentTestData[
      //   this.currentTestData.findIndex(v => v.cellTestCode === "1-202411130004-0001")
      // ].checkData = JSON.stringify(record)
      // this.currentTestData[
      //   this.currentTestData.findIndex(v => v.cellTestCode === "1-202411130004-0001")
      // ].isMiddleClick = true

      //正式情况
      this.currentTestData[
        this.currentTestData.findIndex(v => v.cellTestCode === record.celltestcode)
        ].checkData = JSON.stringify(record)
      this.currentTestData[
        this.currentTestData.findIndex(v => v.cellTestCode === record.celltestcode)
        ].isMiddleClick = true
      const params = {
        id: this.currentSafetyTest.id,
        testData: JSON.stringify(this.currentTestData)
      }
      this.updateSafetyTestData(params)
    },

    // 处理选中大小中检、补电
    async _handleMiddleCheck(curSafetyTest, testData) {
      let curTotalCycleTime = "第" + curSafetyTest.totalCycleTime + "次循环"
      if (testData.length === 0) return
      await testData.forEach(async v => {
        // 如果本来就选择了
        if (v.checkData || v.isMiddleClick) return
        await tLimsTestdataScheduleList({
          celltestcode: v.cellTestCode || String(new Date()),
          alias: v.alias
        }).then(res => {
          console.log(v)

          // 没有数据，默认选中
          if (res.data.length === 0) {
            v.isMiddleClick = true
          } else {
            // 没有选择的，就把存储天数相同的第一条赋给(父层级)
            const have = res.data.filter(filterItem => filterItem.dataPath.indexOf(curTotalCycleTime) !== -1)
            if (have.length > 0) {
              // 只有一条的时候，默认勾选上,多条需手动匹配
              v.isMiddleClick = have.length == 1 ? true : false
              if (have.length == 1) v.checkData = JSON.stringify(have[0])
              //匹配层级数据
            } else {
              console.log(res.data)

              // 是否有多条的数据的情况，多条数据的情况，需要工程师自动勾选
              const isHave = false

              // 找父层级底下的子层级
              for (let i = 0; i < res.data.length; i++) {
                const da = res.data[i]
                console.log(da)
                if (da.children && da.children.length > 0) {
                  const haveChild = da.children.filter(child => child.dataPath.indexOf(curTotalCycleTime) !== -1)

                  // 多条的情况，需要工程师自动勾选
                  if (haveChild.length > 1) {
                    console.log('需自动匹配')
                    isHave = true
                    break
                  }

                  // 只有一条的时候，默认勾选上
                  if (haveChild.length === 1) {
                    console.log("匹配上了")
                    console.log(haveChild)
                    v.isMiddleClick = true
                    v.checkData = JSON.stringify(haveChild[0])
                    break
                  }
                }
              }

              // 所有都找完后，都没有就自动勾选上
              if (!isHave && !v.isMiddleClick) {
                console.log('都没匹配上')
                v.isMiddleClick = true
              }

            }
          }

          const params = {
            id: curSafetyTest.id,
            testData: JSON.stringify(testData)
          }
          this.updateSafetyTestData(params)
        })
      })
    },

    aliasCopy(curSafetyTest) {
      aliasCopyOfAq({ ordTaskId: this.modalData.ordTaskId, safetyTestId: curSafetyTest.id }).then(res => {
        this.handleCopy(res.data)
      })
    },

    async handleSubmit() {
      // 校验
      for (let i = 0; i < this.originalData.length; i++) {
        let curTestData = []
        let singleIndex = i
        let stage = i === 0 ? "初始性能检测：" : "第" + i + "阶段："
        if (this.originalData[i].testType === "temp_cycle") {
          curTestData = this.originalData[i].testData
          // 如果没有检测内容(20250716：现在所有待办都有检测内容，正常情况不会走这个逻辑)，需要从后往前遍历，前面阶段都无测试数据则测试为进行中，否则需根据测试数据的电芯状态判断是进行中还是停止
          let ongoingFlag = true
          for (let k = i - 1; k >= 0 ; k--) {
            let preTestData = this.originalData[k].testData
            if (preTestData) {
              if (preTestData.findIndex(v => v.batteryStatus === 'ongoing' || v.batteryStatus === 'testDone') === -1) {
                ongoingFlag = false
              }
              break;
            }
          }
          let noDataAndOngoingFlag = !curTestData && ongoingFlag
          let haveOngoing = noDataAndOngoingFlag ||
            (curTestData && curTestData.findIndex(v => v.batteryStatus === 'ongoing' || v.batteryStatus === 'testDone') !== -1)
          if (this.originalData[i].stage !== '0') {
            if (!this.originalData[i].actualInDate && haveOngoing) {
              return this.$message.warning(stage + "实际进箱时间未填写!")
            }
            if (!this.originalData[i].actualOutDate && haveOngoing) {
              return this.$message.warning(stage + "实际出箱时间未填写!")
            }
          }

          if (curTestData) {
            if (!(await this._handleIsNull(curTestData))[0] ||
              curTestData.findIndex(v => !v.isMiddleClick && (v.batteryStatus === 'ongoing' || v.batteryStatus === 'testDone')) !== -1) {
              this._handleSetBGC(curTestData, singleIndex)
              return this.$warning({
                content: stage + "请将数据完整填写再点击完成"
              })
            } else {
              if (!(await this._handleIsNull(curTestData))[1]) {
                this._handleSetBGC(curTestData, singleIndex)
                return this.$warning({
                  content: stage + "请先将尺寸数据填写完整再点击完成"
                })
              }
            }
          }

          if (this.originalData[i].videoFlag === '1' && (!this.originalData[i].videoId || !this.originalData[i].videoName) && haveOngoing) {
            return this.$warning({
              content: stage + "测试视频未上传"
            })
          }
          if (this.originalData[i].attachmentFlag === '1' && haveOngoing) {
            if (!this.originalData[i].attachment ||
              (this.originalData[i].attachment && this.originalData[i].attachment.length === 0)) {
                return this.$warning({
                  content: stage + "过程数据未上传"
                })
            }
          }
          let noFillPicture = this.validPicture(this.originalData[i], curTestData)
          if (noFillPicture) {
            return this.$warning({
              content: stage + "每个电芯至少需要上传一张照片"
            })
          }
          const regex = /^-?\d+(\.\d+)?$/
          let errorDigitFormatIndex = curTestData.findIndex(v => v.weight && !regex.test(v.weight))
          if (errorDigitFormatIndex !== -1) {
            return this.$warning({
              content: stage + "请确认" + curTestData[errorDigitFormatIndex].cellTestCode + "重量的数据格式"
            })
          }
          //新增【状态正常-测试完成】后，提交待办需校验电芯状态不能等于进行中
          if(curTestData.findIndex(v => v.batteryStatus && v.batteryStatus === 'ongoing') !== -1) {
            return this.$warning({
              content: stage + "电芯状态不能等于进行中"
            })
          }
        }
      }

      finishSafetyTestTodoTask(this.modalData).then(res => {
        if (!res.success) {
          this.$message.error("错误提示：" + res.message)
        } else {
          this.$message.success("完成成功")
          this.$emit("submit")
        }
      })
    },

    handleSaveSafetyTestData(params) {
      return new Promise((resolve, reject) => {
        handleSaveSafetyTestData(params).then(res => {
          if (res.success) {
            resolve([true, "保存成功！"])
          } else {
            resolve([false, "错误提示：" + res.message])
          }
        })
      });
    },

    async handleSaveTestData () {
      let resultList = []
      for (let item of this.originalData) {
        if (item.testData) {
          const params = {
            id: item.id,
            testData: JSON.stringify(item.testData)
          }
          let result = await this.handleSaveSafetyTestData(params)
          resultList.push(result)
        }
      }
      resultList = resultList.filter(item => item[0] === false)
      if (resultList.length === 0) {
        return this.$message.success("保存成功！")
      } else {
        return this.$message.error(resultList[0][1])
      }
    },

    validPicture(curSafetyTest, curTestData) {
      let noFillPicture = false
      if (curSafetyTest.pictureFlag === "1") {
        var allSamplePictureList = [];
        curTestData.forEach(item => {
          const keys = Object.keys(item.samplePicture);
          var samplePictureList = [];
          for (const index in keys) {
            let picture = item.samplePicture[keys[index]]
            if (item.batteryStatus === 'ongoing' || item.batteryStatus === 'testDone') {
              samplePictureList.push(picture);
            }
          }
          if (item.batteryStatus === 'ongoing' || item.batteryStatus === 'testDone') {
            allSamplePictureList.push(samplePictureList);
          }
        })
        for (const i in allSamplePictureList) {
          if (allSamplePictureList[i].findIndex(item => item.id && item.name) === -1) {
            noFillPicture = true
          }
        }
      }
      return noFillPicture
    },

    handlePreviewCancel() {
      this.previewVisible = false;
    },

    async handlePreview(file) {
      getMinioPreviewUrl(file.id).then(res => {
        if (res.data) {
          this.previewImage = res.data.replace("http://***********:9000/", "/minioDownload/")
          setTimeout(() => {
            this.previewVisible = true
          }, 100)
        } else {
          this.$message.error("服务器错误，请联系管理员！")
        }
      })
    },
  }
}
</script>

<style lang="less" scoped>
@import "../style/calendar.less";
/deep/.ant-table-pagination.ant-pagination {
  float: right;
  margin: 50px 0px 20px 0px;
}
/deep/.ant-upload-list-picture-card .ant-upload-list-item {
  width: 80px;
  height: 80px;
  margin: 0px 0px 0px 0px;
}
/deep/.ant-upload-list-picture-card-container {
  width: 80px;
  height: 70px;
  margin: 0px 0px 7px 0px;
  overflow-wrap: break-word;
}
/deep/.ant-upload-picture-card-wrapper {
  zoom: 1;
  display: ruby;
}
</style>
