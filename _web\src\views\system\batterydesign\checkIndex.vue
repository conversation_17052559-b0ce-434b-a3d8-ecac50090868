<template>
    <div>

        <a-card :bordered="false">

            <a-spin :spinning="loading">
                <s-table ref="table" :columns="columns" :data="loadData" :alert="false" :rowKey="(record) => record.id"
                         :scroll="{x:1000}"
                         :row-selection="rowSelection"
                >
                    <template slot="operator">

                    </template>


                  <div slot="checkMan" slot-scope="text, record"  :title="record.manCheckStatus >= 20?record.checkManTitle:null">
                    {{record.manCheckStatus == 20 || record.manCheckStatus == 80?text:''}}
                  </div>

                    <span slot="batterySorStatus" slot-scope="text, record">
                        <a @click="gotoSor(record)">SOR管理</a>
                    </span>
                    <span slot="batteryDesignScheme" slot-scope="text, record">
                        <a @click="gotoManager(record)">方案设计</a>
                    </span>
                    <span slot="batteryMiSpecs" slot-scope="text, record">
                        <a @click="gotoMi(record)">MI设计</a>
                    </span>
                    <span slot="batteryBomSpecs" slot-scope="text, record">
                        <a @click="gotoBom(record)">电芯BOM设计</a>
                    </span>

                </s-table>

                <a-button type="primary" style="margin-left: 30px;z-index: 1" v-if="showCheck">

                  <a-popconfirm placement="topRight" title="确认通过？" @confirm="() => pass()">
                    <a>通过</a>
                  </a-popconfirm>
                </a-button>
                <a-button style="z-index: 1" v-if="showCheck">
                  <a-popconfirm placement="topRight" title="确认驳回？" @confirm="() => refuse()">
                    <a>驳回</a>
                  </a-popconfirm>

                </a-button>

                <a-button style="z-index: 1">
                    <a @click="gotoDesign()">返回设计汇总主页</a>
                </a-button>


                <add-form ref="addForm" @ok="handleOk" />
                <edit-form ref="editForm" @ok="handleOk" />
            </a-spin>
        </a-card>
    </div>
</template>
<script>
    import { ALL_APPS_MENU } from '@/store/mutation-types'
    import Vue from 'vue'
    import {
        STable,
        XCard
    } from '@/components'
    import {
        getBatteryDesignCheckPage,sysBatteryDesignPass,sysBatteryDesignRefuse
    } from '@/api/modular/system/batterydesignManage'
    import {
        mapActions,
        mapGetters
    } from 'vuex'
    import editForm from './editForm'
    import addForm from './addForm'
    export default {
        components: {
            XCard,
            STable,
            editForm,
            addForm
        },
        data() {
            return {
                // 查询参数
              structureType:null,
                queryParam: {},
              selectedRowKeys:[],
              selectedRow:[],

              rowSelection :{
                columnWidth:30,
                onChange: (selectedRowKeys, selectedRows) => {
                  this.selectedRowKeys = selectedRowKeys
                  this.selectedRow = selectedRows
                },
              },

              showQuery:false,
                // 表头
                columns: [ {
                  title: '序号',
                  dataIndex: 'index',
                  align: 'center',
                  width: 50,
                  ellipsis:true,
                  customRender: (text, record, index) => `${index+1}`
                },
                    {
                        title: '结构类型',width: 70,
                        dataIndex: 'structureType',align: 'center',
                      customRender: (text, record, index) => {
                          if(text == 'g_cylinder'){
                            return 'G圆柱'
                          }
                          if(text == 'c_cylinder'){
                            return 'C圆柱'
                          }
                          if(text == 'v_cylinder'){
                            return 'V圆柱'
                          }
                          if(text == 'winding'){
                            return '方形卷绕'
                          }

                          if(text == 'lamination'){
                            return '方形叠片'
                          }
                          if(text == 'soft_roll'){
                            return '软包'
                          }
                      }
                    },


                  {
                        title: '正极体系',width: 70,ellipsis:true,
                        dataIndex: 'batterySystemPositive',
                        scopedSlots: {
                          customRender: 'batterySystemPositive'
                        },align: 'center',
                    },
                  {
                        title: '负极体系',align: 'center',width: 70,ellipsis:true,
                        dataIndex: 'batterySystemNegative',
                        scopedSlots: {
                          customRender: 'batterySystemNegative'
                        }

                    },{
                        title: '产品代号',ellipsis:true,
                    width: 70,
                        dataIndex: 'productCode',align: 'center'
                    },{
                        title: '客户',
                    width: 40,
                        dataIndex: 'customer',align: 'center',ellipsis:true,
                    },{
                        title: '开发阶段',
                    width: 70,ellipsis:true,
                        dataIndex: 'productDevelopmentStage',align: 'center',
                  customRender: (text, record, index) => {
            if(text == 'a'){
              return 'A样'
            } if(text == 'b'){
              return 'B样'
            } if(text == 'c'){
              return 'C样'
            } if(text == 'd'){
              return 'D样'
            } if(text == 'platform'){
              return '平台课题方案'
            }

          }
                    },{
                        title: '设计版本',
                        dataIndex: 'schemeDesignVersion',align: 'center',
                    width: 70,ellipsis:true,
                    },
                    {
                        title: 'SOR管理',
                        dataIndex: 'batterySorStatus',
                        scopedSlots: {
                            customRender: 'batterySorStatus'
                        },align: 'center',
                      width: 90,ellipsis:true,
                    },
                    {
                        title: '方案设计',
                      width: 90,ellipsis:true,
                        dataIndex: 'batteryDesignScheme',align: 'center',
                        scopedSlots: {
                            customRender: 'batteryDesignScheme'
                        }
                    },
                    {
                        title: 'BOM',
                        dataIndex: 'batteryBomSpecs',align: 'center',
                      width: 90,ellipsis:true,
                      scopedSlots: {
                        customRender: 'batteryBomSpecs'
                      },
                    },
                    {
                        title: 'MI',
                        dataIndex: 'batteryMiSpecs',
                      align: 'center',
                      scopedSlots: {
                        customRender: 'batteryMiSpecs'
                      },
                      width: 90,ellipsis:true,

                    },
                  {
                    title: '创建人',ellipsis:true,
                    dataIndex: 'createName',
                    align: 'center',
                    width: 60,

                  },{
                    title: '创建时间',ellipsis:true,
                    dataIndex: 'createTime',
                    width: 120,
                    align: 'center',
                  },
                  {
                        title: '状态',ellipsis:true,
                    align: 'center',width: 70,
                    dataIndex: 'manCheckStatus',
                    customRender: (text, record, index) => {


                      if(text == 0){
                        return '未提交'
                      }

                      if(text == 10){
                        return '方案审批中'
                      }

                      if(text == 20){
                        return '方案审批通过'
                      }

                      if(text == 70){
                        return '冻结确认审批中'
                      }
                      if(text == 70){
                        return '冻结确认审批中'
                      }
                      if(text == 80){
                        return '方案已冻结'
                      }
                    }

                    },{
                        title: '审批人',ellipsis:true,
                        align: 'center',dataIndex: 'checkMan',
                        width: 70,
                    }

                ],
                // 加载数据方法 必须为 Promise 对象
                loadData: parameter => {
                    //this.queryParam.structureType=this.structureType
                    return getBatteryDesignCheckPage(Object.assign(parameter, this.queryParam)).then((res) => {
                        if(res.data.totalRows > 0){
                          this.showCheck = true
                        }

                        return res.data
                    })
                },

                showCheck:false,
                loading: false,
            }
        },
        created() {},
        computed: {
            ...mapGetters(['userInfo'])
        },
        methods: {
            ...mapActions(['MenuChange']),
            handleOk() {
                this.$refs.table.refresh()
            },
          gotoDesign(){
            //this.switchApp()
            this.$router.push({
              path: "/batterydesign"

            });
          },
          refuse(){
            if(this.selectedRowKeys.length == 0){
              this.$message.error("请先选中要驳回的数据");
            }

            for (let i = 0; i < this.selectedRowKeys.length; i++) {
              sysBatteryDesignRefuse({id:this.selectedRowKeys[i]})
            }
            setTimeout(() => {
              this.selectedRowKeys = []
              this.$refs.table.refresh()
            }, 300)

          },


          pass(){

            if(this.selectedRowKeys.length == 0){
              this.$message.error("请先选中要通过的数据");
            }

            for (let i = 0; i < this.selectedRowKeys.length; i++) {
              sysBatteryDesignPass({id:this.selectedRowKeys[i]})
            }
            setTimeout(() => {
              this.selectedRowKeys = []
              this.$refs.table.refresh()
            }, 300)



          },


          switchApp() {
                const applicationData = Vue.ls.get(ALL_APPS_MENU)
                this.MenuChange(applicationData[0]).then((res) => {}).catch((err) => {
                    this.$message.error('错误提示：' + err.message, 1)
                })
            },
            gotoSor(record){
                //this.switchApp()
                this.$router.push({
                    path: "/system_battery_design_sor",
                    name: "SystemBatteryDesignSor",
                    query: {
                        batteryId:record.id
                    },
                });
            },gotoManager(record){
                //this.switchApp()
                this.$router.push({
                    path: "/battery_design_manager",
                    query: {
                        batteryId:record.id
                    },
                });
            },

          gotoMi(record){
            //this.switchApp()
            this.$router.push({
              path: "/g_cylinder_mi_standard_manage",
              query: {
                batteryId: record.id,
              },
            });
          },
          gotoBom(record){
            //this.switchApp()
            this.$router.push({
              path: "/sys_battery_design_bom",
              query: {
                batteryId:record.id
              },
            });
          },
            /* sysAppDelete (record) {
              this.loading = true
              sysAppDelete(record).then((res) => {
                this.loading = false
                if (res.success) {
                  this.$message.success('删除成功')
                  this.$refs.table.refresh()
                } else {
                  this.$message.error('删除失败：' + res.message)
                }
              }).catch((err) => {
                this.$message.error('删除错误：' + err.message)
              })
            } */
        }
    }
</script>
<style lang="less" scoped="">
    .table-operator {
        margin-bottom: 18px;
    }
    button {
        margin-right: 8px;
    }
    /deep/.ant-tabs-top .ant-tabs-ink-bar-animated, .ant-tabs-bottom .ant-tabs-ink-bar-animated{
        width: 0 !important;
    }
    /deep/.ant-card-body{
        padding: 10px 24px !important;
    }
    /deep/.table-page-search-wrapper .ant-form-inline .ant-form-item,/deep/.table-page-search-wrapper .table-page-search-submitButtons{
        margin-bottom: 0;
    }
    /deep/.ant-tabs-nav .ant-tabs-tab{
        background: #c2c4c4;
        margin: 0 2px 0 0;
        padding: 4px 16px;
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
        color: #fff;
    }
    /deep/.ant-tabs-nav .ant-tabs-tab-active{
        border: 1px solid #c2c4c4;
        color: #000;
        background: #fff;
        margin: 0;
    }

    /deep/.ant-col-md-8 {
      padding-top: 10px;
    }
    /deep/.ant-form-item-label > label::after{
      content: '';
    }
    /deep/.ant-table-middle > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-tbody > tr > td{
      padding: 5.5px 5px;
    }
    /deep/.ant-table-pagination.ant-pagination {
      margin: 5px 0;
    }

    button {
      top: -60px;
    }

    /deep/.ant-table-pagination.ant-pagination {
      margin-top: 30px;
    }




</style>

