import { axios } from '@/utils/request'
import service from "@/utils/requestjira"




export function getIssueByIssueId(parameter) {
  return axios({
      url: '/projectIssue/getByIssueId',
      method: 'post',
      data: parameter
  })
}


export function deleteByIssueId(parameter) {
  return axios({
      url: '/projectIssue/deleteByIssueId',
      method: 'post',
      data: parameter
  })
}


export function addOrUpdateIssue(parameter) {
  return axios({
      url: '/projectIssue/addOrUpdate',
      method: 'post',
      data: parameter
  })
}


export function getMeetingListByIssueId(parameter) {
  return axios({
      url: '/projectIssueMeeting/getListByIssueId',
      method: 'post',
      data: parameter
  })
}

export function getMeetingById(parameter) {
  return axios({
      url: '/projectIssueMeeting/get',
      method: 'post',
      data: parameter
  })
}

export function updateMeetingById(parameter) {
  return axios({
      url: '/projectIssueMeeting/update',
      method: 'post',
      data: parameter
  })
}

export function updateMeetingNull(parameter) {
  return axios({
      url: '/projectIssueMeeting/updateNull',
      method: 'post',
      data: parameter
  })
}

export function addMeeting(parameter) {
  return axios({
      url: '/projectIssueMeeting/add',
      method: 'post',
      data: parameter
  })
}

