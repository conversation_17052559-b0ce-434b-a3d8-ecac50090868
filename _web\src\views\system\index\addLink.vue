<template>
    <a-modal title="增加功能" :width="500" :visible="visible" :confirmLoading="confirmLoading" @ok="handleSubmit" @cancel="handleCancel">
        <a-spin :spinning="confirmLoading">
            <a-form :form="form">

                
                
                <a-form-item label="功能名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                    <a-input placeholder="请输入功能名称" v-decorator="['linkName', {rules: [{required: true, message: '请输入功能名称！'}]}]"></a-input>
                </a-form-item>
                <a-form-item label="功能链接" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                    <a-input placeholder="请输入功能链接" v-decorator="['linkUrl', {rules: [{required: true, message: '请输入功能链接！'}]}]"></a-input>
                </a-form-item>

                <a-form-item label="图标" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                    <a-upload :file-list="fileList" :headers="headers" :action="postUrl" :multiple="false" name="file" @change="handleChange">
                        <a-button> <a-icon type="upload" /> 上传图标 </a-button>
                    </a-upload>
                </a-form-item>
               <!--  <a-form-item label="排序" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                     <a-input-number v-decorator="['linkSort', { initialValue: 1 }]" :min="1" />
                </a-form-item> -->

               
            </a-form>
        </a-spin>
    </a-modal>
</template>

<script>
    import {addLinks} from "@/api/modular/system/linkManage"
    import Vue from 'vue'
    import {
        ACCESS_TOKEN
    } from '@/store/mutation-types'
    export default {
        data() {
            return {
                postUrl: '/api/sysFileInfo/uploadfile',
                headers: {
                    Authorization: 'Bearer ' + Vue.ls.get(ACCESS_TOKEN),
                },
                form: this.$form.createForm(this),
                visible: false,
                confirmLoading: false,
                fileList: [],
                record: {},
                wrapperCol: {},
                labelCol: {},
                iconId:0,
            }
        },
        methods: {
            handleChange(info) {
                let fileList = [...info.fileList];
                fileList = fileList.slice(-1);
                this.fileList = fileList;
                if (info.file.status !== 'uploading') {
                    console.log(info.file, info.fileList);
                }
                if (info.file.status === 'done') {
                    let res = info.file.response
                    if (res.success) {
                        this.$message.success(`${info.file.name}上传成功`)
                        this.iconId = res.data.id
                    } else {
                        this.$message.error(res.message)
                    }
                } else if (info.file.status === 'error') {
                    this.$message.error(`${info.file.name}上传失败`);
                }
            },
            add() {
                this.visible = true
            },
            handleCancel() {
                this.form.resetFields()
                this.confirmLoading = false
                this.visible = false
                this.fileList = []
            },
            handleSubmit() {
                const {
                    form: {
                        validateFields
                    }
                } = this
                this.confirmLoading = true
                validateFields((errors, values) => {
                    if (!errors) {
                        let $params = { ...values,
                            ...{iconId:this.iconId}
                        }
                        addLinks($params).then((res) => {
                            if (res.success) {
                                this.$message.success('提交成功')
                                this.handleCancel()
                                this.$emit('ok',$params)
                            } else {
                                this.$message.error('提交失败：' + res.message)
                            }
                            this.confirmLoading = false
                        }).finally((res) => {
                            this.confirmLoading = false
                        })
                    } else {
                        this.confirmLoading = false
                    }
                })
            },
        }
    }
</script>

<style>

</style>