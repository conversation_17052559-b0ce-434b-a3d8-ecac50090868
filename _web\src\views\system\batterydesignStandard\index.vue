<template>
	<div class="container">
		<div>
			<img class="content-title" src="../../../assets/title.png" alt="" />
		</div>
		<div class="content-wrapper">
			<div v-for="(item, index) in titleMenu" :key="index" class="content">
				<div class="title">
					<div class="detail">{{ item.title }}</div>
				</div>
				<div class="detail">
					<div class="left">
						<div
							v-for="(chilItem, chilIndex) in item.children"
							:key="chilIndex"
							class="item"
							:class="{ 'check-item': item.activeKey === chilIndex }"
							@click="handleChangeTab(chilIndex, index)"
						>
							{{ chilItem }}
						</div>
					</div>
					<div class="right">
						<ul class="domtree" :class="{ 'animate__animated animate__fadeIn': item.activeChange }">
							<li v-for="(treeItem, treeIndex) in treeData" :key="treeIndex">
								<div class="li-item" @click="handleTreeSelect(treeIndex, index)">{{ treeItem.title }}</div>
								<ul
									v-for="(treeChilItem, treeChilIndex) in treeItem.children"
									:key="treeChilIndex"
									style="margin:0;padding:0;"
								>
									<li>
										<div class="li-wrap">
											<div class="icon">
												<img class="li-icon" src="../../../assets/icons/li-icon.png" alt="" />
											</div>
											<div class="li-item chil-item" @click="handleTreeSelect(treeIndex, index, treeChilIndex)">
												{{ treeChilItem.title }}
											</div>
										</div>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- <div style="background-color: #FFFFFF;display: flex;padding-top: 10%;
  flex-direction: column;align-items: center;" id="inside">
    <div>
      <div style="float: left;width:33.3%">
        <div class="big_title">G圆柱</div>
        <div class="sub_title"><a @click="gotoMI()">MI规范库</a></div>
        <div class="sub_title"><a>电化学体系库</a></div>
        <div class="sub_title_last"><a>结构设计库</a></div>
      </div>
      <div style="float: left;width:33.3%">
        <div class="big_title">C圆柱</div>
        <div class="sub_title"><a>MI规范库</a></div>
        <div class="sub_title"><a>电化学体系库</a></div>
        <div class="sub_title_last"><a>结构设计库</a></div>
      </div>
      <div style="float: left;width:33.3%">
        <div class="big_title">V圆柱</div>
        <div class="sub_title"><a>MI规范库</a></div>
        <div class="sub_title"><a>电化学体系库</a></div>
        <div class="sub_title_last"><a>结构设计库</a></div>
      </div>
      <div style="float: left;width:33.3%">
        <div class="big_title">方形叠片</div>
        <div class="sub_title"><a>MI规范库</a></div>
        <div class="sub_title"><a>电化学体系库</a></div>
        <div class="sub_title"><a>结构设计库</a></div>
      </div>
      <div style="float: left;width:33.3%">
        <div class="big_title">方形卷绕</div>
        <div class="sub_title"><a>MI规范库</a></div>
        <div class="sub_title"><a>电化学体系库</a></div>
        <div class="sub_title"><a>结构设计库</a></div>
      </div>
      <div style="float: left;width:33.3%">
        <div class="big_title">软包</div>
        <div class="sub_title"><a>MI规范库</a></div>
        <div class="sub_title"><a>电化学体系库</a></div>
        <div class="sub_title"><a>结构设计库</a></div>
      </div>


    </div>

  </div> -->
</template>
<script>
export default {
	components: {},
	data() {
		return {
			titleMenu: [
				{
					title: "圆 柱",
					activeKey: 0,
					activeChange: false,
					children: ["G 圆柱", "C 圆柱", "V 圆柱"]
				},
				{
					title: "方 形",
					activeKey: 0,
					activeChange: false,
					children: ["卷绕", "叠片"]
				},
				{
					title: "软 包",
					activeKey: 0,
					activeChange: false,
					children: ["叠片"]
				}
			],
			treeData: [
				{
					title: "正负极材料体系开发",
					key: 0
				},
				{
					title: "电极技术开发",
					key: 1,
					children: [
						{ title: "箔材选型", key: 0 },
						{ title: "粘结剂选型", key: 1 },
						{ title: "导电剂选型", key: 2 },
						{ title: "电极功能添加剂", key: 3 }
					]
				},
				{
					title: "功能开发与标定",
					key: 2,
					children: [
						{ title: "功能电解液选型", key: 0 },
						{ title: "功能隔膜选型", key: 1 },
						{ title: "功能箔材选型", key: 2 }
					]
				},
				{
					title: "结构设计库",
					key: 3
				},
				{
					title: "MI设计与组合",
					url: "/g_cylinder_mi_library",
					key: 4
				},
				{
					title: "包装设计库",
					key: 5
				}
			]
		}
	},

	methods: {
		gotoMI() {
			this.$router.push("/g_cylinder_mi_library")
		},

		// tab改变事件
		handleChangeTab(key, index) {
			if (this.titleMenu[index].activeKey === key) return
			this.titleMenu[index].activeKey = key
			this.titleMenu[index].activeChange = true
			setTimeout(() => {
				this.titleMenu[index].activeChange = false
			}, 500)
		},

		// 树形节点点击事件
		handleTreeSelect(index, key, chilIndex = null) {
			// 点击三级
			if (chilIndex !== null && this.treeData[index].children[chilIndex].url) {
				this.$router.push(this.treeData[index].children[chilIndex].url)
			}
			// 点击二级
			if (chilIndex === null && this.treeData[index].url) {
				if (key === 0 && this.titleMenu[key].activeKey === 0) this.$router.push(this.treeData[index].url)
			}
		}
	}
}
</script>
<style lang="less" scoped>
.animated {
	animation-duration: 10s;
	animation-fill-mode: both;
}
.container {
	display: flex;
	flex-direction: column;
	height: calc(100vh - 40px);
	background-color: #012060;
	padding: 0 0.7813vw 0.7813vw;
}

.content-title {
	width: 100%;
	margin: -2.1875vw 0 0.7813vw;
}
.content-wrapper {
	box-sizing: border-box;
	background-image: url("../../../assets/frame.png");
	background-size: 100% 100%;
	width: 100%;
	height: 100%;
	overflow: auto;
	padding: 0.7813vw;

	display: flex;
	justify-content: space-around;
	align-items: center;
}
// .content .title {
// 	// margin: 1.25vw 0 1.25vw 0;
// 	// border-top: 2.3438vw solid #0071c1;
// 	// border-left: 4.6875vw solid transparent;
// 	// border-right: 4.6875vw solid transparent;
// 	// height: 0;
// 	// width: 100%;

// 	// line-height: 2.3438vw;

// 	// display: flex;
// 	// justify-content: center;
// 	// align-items: flex-end;
// 	width: 5rem;
// 	height: 11.25rem;
// 	background: red;
// 	transform: perspective(0.5em) rotateY(-3deg);
// }

.content .title {
	width: 80%;
	margin-left: 10%;
	height: 2.3438vw;
	position: relative;
	font-size: 1.875rem;
	text-align: center;

	transform: rotate(180deg);
}

.content .title::before {
	content: ""; /* 用伪元素来生成一个矩形 */
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background: #0071c1;
	box-shadow: 0 -2px 10px 2px #0071c1 !important;

	transform: perspective(0.5em) rotateX(5deg);
}

.content .title .detail {
	position: absolute;
	top: 0;
	left: 50%;
	font-size: 1.25vw;
	font-weight: 500;
	color: #fff;
	transform: rotate(180deg) translate(50%, 50%);
}

.content .detail {
	display: flex;
	margin-top: 1.25vw;
}

.detail .left {
	margin-right: 3.125vw;
}

.detail .left .item {
	width: 4.6875vw;
	padding: 0.3906vw 0;
	font-size: 0.9375vw;
	margin-bottom: 1.25vw;
	cursor: pointer;
	text-align: center;
	text-decoration: none;
	outline: none;
	color: #fff;
	background-color: #0071c1;
	border: none;
	border-radius: 0.2344vw;
	box-shadow: -0.2344vw 0.2344vw 0 #333;
}

.detail .left .item:hover {
	background-color: #948b54;
}

.detail .left .item:active {
	background-color: #948b54;
	box-shadow: -0.1563vw 0.1563vw 0 #333;
	transform: translate(-0.2344vw, 0.2344vw);
}

.check-item {
	background-color: #948b54 !important;
}

.domtree {
	padding: 0.7813vw;
	border-radius: 0.3906vw;
	border: 0.1563vw solid #948b54;
	margin-bottom: -0.3906vw;
}
li {
	list-style-type: none;
}

.li-item {
	width: 100%;
	padding: 0.3906vw 0.7813vw;
	font-size: 0.9375vw;
	line-height: 1;
	text-align: center;
	margin-bottom: 0.3906vw;
	cursor: pointer;
	text-align: center;
	text-decoration: none;
	outline: none;
	color: #fff;
	background-color: #948b54;
	border: none;
	border-radius: 0.2344vw;
	box-shadow: -0.2344vw 0.2344vw 0 #333;
}

.chil-item {
	width: 140%;
}

.li-item:hover {
	background-color: #948b54;
}

.li-item:active {
	background-color: #948b54;
	box-shadow: -0.1563vw 0.1563vw 0 #333;
	transform: translate(-0.2344vw, 0.2344vw);
}

.li-wrap {
	display: flex;
}

.li-wrap .icon{
	display: flex;
	justify-content: center;
	align-items: center;
}
.li-wrap .li-icon {
	width: 1.4063vw;
	height:1.4063vw;
	margin-right: 0.625vw;
}

@media screen and (min-width: 1920px) {
	.li-item {
		padding: .5469vw 0.7813vw;
		font-size: 1.0156vw;
	}

	.content .detail {
		margin-top: 1.9531vw;
	}

	.content .title .detail {
		transform: rotate(180deg) translate(50%, 75%);
	}
}

// .li-item {
// 	width: 100%;
// 	font-size: 0.8594vw;
// 	text-align: center;
// 	padding: 0.3906vw 0.7813vw;
// 	border-radius: 0.2344vw;
// 	margin-bottom: 0.3906vw;
// 	background-color: #948b54;
// 	color: #fff;
// 	cursor: pointer;
// 	position: relative;
// 	overflow: hidden;
// }

// .li-item::before {
// 	content: "";
// 	position: absolute;
// 	top: -1.5625vw;
// 	width: .9375rem;
// 	height: 200%;
// 	background-color: rgba(255, 255, 255, 0.6);
// 	transform: skew(45deg) translate3d(-12.5rem, 0, 0);
// }

// .li-item:hover {
// 	background-color: #2c3e50;
// }

// .li-item:hover::before {
// 	transition: ease-in-out 0.7s;
// 	transform: skew(45deg) translate3d(18.75rem, 0, 0);
// }

// .li-content {
// 	display: flex;
// 	align-items: center;
// }

// .radiu {
// 	width: .7813vw;
// 	height: .7813vw;
// 	border-radius: 50%;
// 	border: .125rem solid #fff;
// 	margin-right: .3906vw;
// 	flex-shrink: 1;
// }

// .content-wrapper {
// 	width: 100%;
// 	display: flex;
// 	justify-content: space-around;
// }

// .content-wrapper .content {
// 	display: flex;
// 	flex-direction: column;
// 	width: 28%;
// 	padding: .7813vw;
// 	margin: 1.5625rem 0;
// 	background-color: rgb(255, 255, 255);
// 	border-radius: .7813vw;
// 	box-shadow: 0rem .125rem .7813vw 0 rgba(51, 51, 51, 0.3);
// }

// .title {
// 	text-align: center;
// 	font-size: 1.25rem;
// 	font-weight: 600;
// }

// .tab {
// 	font-weight: 500;
// }

// // tabs样式修改
// /deep/.ant-tabs-nav .ant-tabs-tab {
// 	padding: .3906vw 1.25vw;
// }

// /deep/.ant-tabs-bar {
// 	margin: 0;
// }

// .big_title {
// 	font-size: xx-large;
// 	font-weight: bolder;
// 	color: black;
// 	padding-bottom: 1.25rem;
// }

// .sub_title {
// 	font-size: large;
// 	padding-bottom: .7813vw;
// }
// .sub_title_last {
// 	font-size: large;
// 	padding-bottom: 2.3438vw;
// }
</style>
