<template>
  <div class="logo">
    <router-link :to="{name:'<PERSON>sol<PERSON>'}">
      <!-- <LogoSvg /> -->
      <img src="~@/assets/logo.png"  alt="logo" style="margin-top:-5px">
      <h1 v-if="showTitle">{{ this.titles }}</h1>
    </router-link>
  </div>
</template>

<script>
/* import LogoSvg from '@/assets/logo.jpg?inline' */
import { mixin, mixinDevice } from '@/utils/mixin'

export default {
  name: 'Logo',
  /* components: {
    LogoSvg
  }, */
  mixins: [mixin, mixinDevice],
  data () {
    return {
      titles: ''
    }
  },
  props: {
    title: {
      type: String,
      default: '',
      required: false
    },
    showTitle: {
      type: Boolean,
      default: true,
      required: false
    }
  },
  created () {
    if (this.layoutMode === 'topmenu') {
      if (this.title.length > 8) {
        this.titles = this.title.substring(0, 7) + '...'
      } else {
        this.titles = this.title
      }
    } else {
      if (this.title.length > 10) {
        this.titles = this.title.substring(0, 9) + '...'
      } else {
        this.titles = this.title
      }
    }
  }
}
</script>
