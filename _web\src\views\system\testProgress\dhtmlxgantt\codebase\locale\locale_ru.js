/*
@license

dhtmlxGantt v.6.3.3 Standard

This version of dhtmlxGantt is distributed under GPL 2.0 license and can be legally used in GPL projects.

To use dhtmlxGantt in non-GPL projects (and get Pro version of the product), please obtain Commercial/Enterprise or Ultimate license on our site https://dhtmlx.com/docs/products/dhtmlxGantt/#licensing or contact <NAME_EMAIL>

(c) XB Software Ltd.

*/
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("locale/locale_ru",[],t):"object"==typeof exports?exports["locale/locale_ru"]=t():e["locale/locale_ru"]=t()}(window,function(){return function(e){var t={};function n(o){if(t[o])return t[o].exports;var r=t[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(o,r,function(t){return e[t]}.bind(null,r));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="/codebase/",n(n.s=186)}({186:function(e,t){gantt.locale={date:{month_full:["Январь","Февраль","Март","Апрель","Maй","Июнь","Июль","Август","Сентябрь","Oктябрь","Ноябрь","Декабрь"],month_short:["Янв","Фев","Maр","Aпр","Maй","Июн","Июл","Aвг","Сен","Окт","Ноя","Дек"],day_full:["Воскресенье","Понедельник","Вторник","Среда","Четверг","Пятница","Суббота"],day_short:["Вс","Пн","Вт","Ср","Чт","Пт","Сб"]},labels:{new_task:"Новое задание",dhx_cal_today_button:"Сегодня",day_tab:"День",week_tab:"Неделя",month_tab:"Месяц",new_event:"Новое событие",icon_save:"Сохранить",icon_cancel:"Отменить",icon_details:"Детали",icon_edit:"Изменить",icon_delete:"Удалить",confirm_closing:"",confirm_deleting:"Событие будет удалено безвозвратно, продолжить?",section_description:"Описание",section_time:"Период времени",section_type:"Тип",column_wbs:"ИСР",column_text:"Задача",column_start_date:"Начало",column_duration:"Длительность",column_add:"",link:"Связь",confirm_link_deleting:"будет удалена",link_start:" (начало)",link_end:" (конец)",type_task:"Task",type_project:"Project",type_milestone:"Milestone",minutes:"Минута",hours:"Час",days:"День",weeks:"Неделя",months:"Месяц",years:"Год",message_ok:"OK",message_cancel:"Отменить",section_constraint:"Constraint",constraint_type:"Constraint type",constraint_date:"Constraint date",asap:"As Soon As Possible",alap:"As Late As Possible",snet:"Start No Earlier Than",snlt:"Start No Later Than",fnet:"Finish No Earlier Than",fnlt:"Finish No Later Than",mso:"Must Start On",mfo:"Must Finish On",resources_filter_placeholder:"начните вводить слово для фильтрации",resources_filter_label:"спрятать не установленные"}}}})});
//# sourceMappingURL=locale_ru.js.map