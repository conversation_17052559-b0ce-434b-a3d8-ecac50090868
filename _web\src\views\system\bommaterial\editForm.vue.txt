<template>
    <a-modal title="新增材料" :width="600" :visible="visible" :confirmLoading="confirmLoading" @ok="handleSubmit" @cancel="handleCancel">
        <a-spin :spinning="confirmLoading">
            <a-form :form="form">
                <a-form-item label="产品类型" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                    <a-select placeholder="请选择产品类型" v-decorator="['type', {rules: [{required: true, message: '请选择产品类型！'}]}]">
                        <a-select-option v-for="(item,i) in type" :key="i" :value="item.id">{{item.value}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item style="display: none;" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input v-decorator="['id']" />
                </a-form-item>
                <a-form-item label="材料类别" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                    <a-select placeholder="请选择材料类别" v-decorator="['materialKind', {rules: [{required: true, message: '请选择材料类别！'}]}]">
                        <a-select-option value="0">BOM适用情况</a-select-option>
                        <a-select-option value="1">化学材料</a-select-option>
                        <a-select-option value="2">结构件</a-select-option>
                        <a-select-option value="3">包装材料</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="材料名称" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                    <a-input placeholder="请输入材料名称" v-decorator="['materialName', {rules: [{required: true, message: '请输入材料名称！'}]}]" />
                </a-form-item>
                <a-form-item label="物料代码" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                    <a-input placeholder="请输入物料代码" v-decorator="['materialNo', {rules: [{required: true, message: '请输入物料代码！'}]}]" />
                </a-form-item>
                <a-form-item label="B01" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                    <a-input placeholder="请输入b01" v-decorator="['b01', {rules: [{required: true, message: '请输入b01！'}]}]" />
                </a-form-item>
                <template v-for="(item,i) in slices">
                        <a-form-item :key="i" v-if="i > 4 && i < slices.length-1" :label="item.title" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback >
                            <a-input  :placeholder="item.title" v-decorator="[item.dataIndex]" />
                        </a-form-item>
                </template>
            </a-form>
        </a-spin>
    </a-modal>
</template>

<script>
    export default {
        data() {
            return {
                type:[],
                slices:[],
                labelCol: {
                    xs: {
                        span: 24
                    },
                    sm: {
                        span: 5
                    }
                },
                wrapperCol: {
                    xs: {
                        span: 24
                    },
                    sm: {
                        span: 18
                    }
                },
                visible: false,
                confirmLoading: false,
                form: this.$form.createForm(this)
            }
        },
        methods: {
            edit(type,record,slice) {
                this.type = type
                this.slices = slice
                this.visible = true
                setTimeout(() => {
                    let params = {
                        id: record.id,
                        type:record.type,
                        materialKind: record.materialKind,
                        materialName: record.materialName,
                        materialNo:record.materialNo,
                        b01: record.b01
                    }
                    for (let i = 0,j = slice.length; i < j; i++) {
                        if (i > 2  && i < j-1) {
                            params[slice[i]['dataIndex']] = record[slice[i]['dataIndex']]
                        }
                    }
                    this.form.setFieldsValue(params)
                }, 100)
            },
            handleSubmit() {
                const {
                    form: {
                        validateFields
                    }
                } = this
                this.confirmLoading = true
                validateFields((errors, values) => {
                    if (!errors) {
                        this.$emit('ok', values)
                        this.handleCancel()
                    }
                    this.confirmLoading = false
                })
            },
            handleCancel() {
                this.form.resetFields()
                this.visible = false
            }
        }
    }
</script>
