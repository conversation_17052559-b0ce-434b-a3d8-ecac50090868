/* 
 * 卡片组件样式
 */

.base-card {
  margin-bottom: var(--spacing-lg);
  background-color: #fff;
  border-radius: var(--border-radius-base);
  box-shadow: var(--box-shadow-card);
}

.base-card .ant-card-head {
  min-height: 48px;
  padding: 0 var(--spacing-lg);
  color: var(--text-color-primary);
  font-weight: 500;
  font-size: var(--font-size-lg);
  background: transparent;
  border-bottom: 1px solid var(--border-color-split);
  border-radius: var(--border-radius-base) var(--border-radius-base) 0 0;
}

.base-card .ant-card-body {
  padding: var(--spacing-lg);
}

/* 高对比度模式支持 */
@media (forced-colors: active) {
  .base-card {
    forced-color-adjust: auto;
    border: 1px solid CanvasText;
    box-shadow: none;
    background-color: Canvas;
  }
  
  .base-card .ant-card-head {
    border-bottom: 1px solid CanvasText;
  }
}
