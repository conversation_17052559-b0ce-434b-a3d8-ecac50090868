<template>
	<a-spin :spinning="loading">
		<!-- 标题 + 提交资料按钮 start -->
		<div class="btns">
			<span  class="count_tip">文档齐套率：{{ conformity }}</span>
			<span  class="count_tip">文档有效性：{{ effective }}</span>
			<!-- '241' => 确认资料 -->
			<div v-if="isNowStage" :style="{ float: 'right', marginRight: '10px' }">
				<a-popconfirm
					v-for="(item, i) in btns"
					:key="i"
					:loading="loading"
					style="margin-right:.1875rem"
					placement="topRight"
					title="确认执行？"
					@confirm="
						() => (item.transitionId == '241' ? callDocsCommit(item.transitionId) : callAgreeOrnot(item.transitionId))
					"
				>
					<a>{{ item.transitionName }}</a>
				</a-popconfirm>
				<a-button
					v-if="hasPerm('docs:add')"
					type="primary"
					:style="{ display: 'inline-block', marginLeft: '.5rem' }"
					@click="$refs.addForm.add()"
					>新建阶段文件</a-button
				>
			</div>
			<a-button
				type="primary"
				@click="handleEdit"
				:style="{ display: 'inline-block', marginRight: '10px', marginBottom: '10px', float: 'right' }"
				>{{ isNoEdit ? "编辑" : "取消" }}</a-button
			>
		</div>

		<!-- 标题 + 提交资料按钮 end -->

		<div class="docsitem" v-for="(val, key, index) in dosMap" :key="index">
			<div class="title">{{ index + 1 }}.{{ key }}</div>
			<a-table
				:pagination="false"
				:columns="columns"
				:data-source="dosMap[key]"
				:rowKey="record => record.id"
				size="middle"
			>
				<!-- 适用情况 start -->
				<div slot="docIsNeed" slot-scope="text, record" style="width: 100%;text-align: center">
					<a-select
						:disabled="isNoEdit"
						v-if="isNowStage && dqePerm"
						:key="record.docIsNeed"
						:default-value="text == -1 ? '-' : parseInt(text)"
						:dropdownMatchSelectWidth="false"
						@change="changeSelect($event, record, 'docIsNeed')"
					>
						<a-select-option :value="parseInt(-1)">-</a-select-option>
						<a-select-option :value="parseInt(0)">No</a-select-option>
						<a-select-option :value="parseInt(1)">Yes</a-select-option>
					</a-select>
					<div v-else>
						<span v-if="parseInt(text) == -1">-</span>
						<span v-if="parseInt(text) == 1">Yes</span>
						<span v-if="parseInt(text) == 0">No</span>
					</div>
				</div>
				<!-- 适用情况 end -->

				<!-- 评分 start -->
				<div slot="score" slot-scope="text, record" style="width: 100%;text-align: center">
					<a-select
						:disabled="isNoEdit"
						v-if="isNowStage && dqePerm && record.fileId != '0'"
						:key="record.score"
						:default-value="record.score == null ? '-' : parseInt(text)"
						:dropdownMatchSelectWidth="false"
						@change="changeSelect($event, record, 'score')"
					>
						<a-select-option :value="parseInt(0)">0</a-select-option>
						<a-select-option :value="parseInt(2)">2</a-select-option>
						<a-select-option :value="parseInt(6)">6</a-select-option>
						<a-select-option :value="parseInt(8)">8</a-select-option>
						<a-select-option :value="parseInt(10)">10</a-select-option>
					</a-select>
					<span v-else>{{ text }}</span>
				</div>
				<!-- 评分 end -->

				<div slot="sort" slot-scope="text, record">
					<a-input
						:disabled="isNoEdit"
						type="number"
						min="0"
						size="small"
						placeholder="填写排序"
						style="width:100%"
						v-if="isNowStage && dqePerm"
						@change="
							e => {
								const { value } = e.target
								let params = {}
								params['sort'] = value
								params['id'] = record.id
								callSaveDoc(params, record, 'sort', true)
							}
						"
						v-model="record.sort"
					/>
					<div v-else>{{ text }}</div>
				</div>
				<div slot="remark" slot-scope="text, record">
					<a-textarea
						v-if="isNowStage && dqePerm"
						:auto-size="{ maxRows: 3, minRows: 1 }"
						style="width:100%"
						v-model="record.remark"
						@blur="
							e => {
								const { value } = e.target
								let params = {}
								params['remark'] = value
								params['id'] = record.id
								callSaveDoc(params, record, 'remark')
							}
						"
						placeholder="填写备注"
					/>
					<div v-else>{{ text }}</div>
				</div>

				<span class="action" slot="action" slot-scope="text, record">
					<span v-if="(isNowStage && engeerPerm) || docsImport">
						<a-popconfirm
							:disabled="isNoEdit"
							v-if="record.fileId != '0'"
							placement="topRight"
							title="确认删除？"
							@confirm="() => callFileInfoDelete(record)"
						>
							<a class="a-upload" :class="{ none: isNoEdit }">删除</a>
						</a-popconfirm>
						<a-upload
							v-else
							:disabled="isNoEdit"
							:showUploadList="false"
							:file-list="fileList"
							:headers="headers"
							:action="postUrl"
							:multiple="false"
							name="file"
							@change="value => handleChange(value, record)"
						>
							<a class="a-upload" :class="{ none: isNoEdit }">上传</a>
						</a-upload>
					</span>
					<span v-else></span>
				</span>

				<span slot="planDate">
					{{ queryParam.planDate}}
				</span>

				<div slot="status" slot-scope="text, record">
					<span v-if="record.score == 0" class="circle red"></span>
					<span v-else-if="record.score > 0 && record.score < 10" class="circle yellow"></span>
					<span v-else-if="record.score == 10" class="circle green"></span>
				</div>
				<template slot="docName" slot-scope="text, record">
					<span v-if="record.fileId == '0'">{{ text }}</span>
					<a
						v-else-if="
							text.includes('pdf') ||
								text.includes('PDF') ||
								text.includes('png') ||
								text.includes('jpg') ||
								text.includes('jpeg')
						"
						@click="topreview(record)"
						>{{ text }}</a
					>
					<a v-else @click="callFileInfoDownload(record)">{{ text }}</a>
				</template>

				<!-- 文档齐套率 start -->
				<template slot="fileRate" slot-scope="text, record">
					<span :class="record.isNoPassVerify ? 'redTips' : ''" v-if="record.fileId == '0'">0%</span>
					<span v-else>100%</span>
				</template>
				<!-- 文档齐套率 end -->
			</a-table>
		</div>

		<!-- 新增弹窗 start -->
		<add-form :issueId="issueId" :stage="stage" :projectdetail="projectdetail" ref="addForm" @ok="callDocsData" />
		<!-- 新增弹窗 end -->

		<!-- 新增 弹窗上传文件 start-->
		<docsModel ref="docsModel" />
		<!-- 新增 弹窗上传文件 end-->
	</a-spin>
</template>

<script>
import addForm from "./addForm"
import moment from "moment"
import Vue from "vue"
import { ACCESS_TOKEN } from "@/store/mutation-types"
import { getDocList, saveDoc, getTransition, docsCommit, docAgreeOrNot } from "@/api/modular/system/docManage"
import { sysFileInfoDelete, sysFileInfoDownload } from "@/api/modular/system/fileManage"
import docsModel from "./model/docsupload.vue"
export default {
	components: {
		addForm,
		docsModel
	},
	props: {
		issueId: {
			type: Number,
			default: 0
		},
		stage: {
			type: String,
			default: ""
		},
		projectdetail: {
			type: Object,
			default: {}
		},
		isNoEdit: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			conformity: null,
			effective: null,
			btns: [],
			dqePerm: [],
			engeerPerm: [],
			docsImport: [],
			isNowStage: false,
			postUrl: "/api/sysFileInfo/uploadfile",
			headers: {
				Authorization: "Bearer " + Vue.ls.get(ACCESS_TOKEN)
			},
			fileList: [],
			queryParam: {},
			updateParam: {},
			dosMap: {},
			needUploadDocMap:{},
			columns: [
				{
					title: "序号",
					dataIndex: "no",
					width: 40,
					align: "center",
					customRender: (text, record, index) => `${index + 1}`
				},
				{
					title: "输出资料",
					dataIndex: "outputFile",
					width: 120,
					align: "center"
				},
				{
					title: "责任部门",
					dataIndex: "manager",
					width: 80,
					align: "center"
				},
				{
					title: "计划完成时间",
					dataIndex: "planDate",
					width: 80,
					align: "center",
					scopedSlots: { customRender: "planDate" }
				},
				{
					title: "实际完成时间",
					dataIndex: "actualDate",
					width: 80,
					align: "center"
				},
				{
					title: "适用情况",
					dataIndex: "docIsNeed",
					width: 80,
					align: "center",
					scopedSlots: { customRender: "docIsNeed" }
				},
				{
					title: "文件",
					dataIndex: "docName",
					width: 120,
					align: "center",
					scopedSlots: { customRender: "docName" }
				},
				{
					title: "文件齐套率",
					dataIndex: "fileRate",
					width: 60,
					align: "center",
					scopedSlots: { customRender: "fileRate" }
				},
				{
					title: "操作",
					dataIndex: "action",
					width: 60,
					align: "center",
					scopedSlots: { customRender: "action" }
				},
				{
					title: "评分",
					dataIndex: "score",
					width: 80,
					align: "center",
					scopedSlots: { customRender: "score" }
				},
				{
					title: "状态灯",
					dataIndex: "status",
					scopedSlots: { customRender: "status" },
					width: 80,
					align: "center"
				},
				{
					title: "备注",
					dataIndex: "remark",
					width: 120,
					scopedSlots: { customRender: "remark" }
				}
			],
			loading: false,
			YoN: ["NO", "YES"]
		}
	},
	created() {
		this.queryParam = {
			issueId: this.issueId,
			stage: parseInt(this.stage),
			docLevel: this.projectdetail.level
		}
		this.isNowStage = this.stage == this.projectdetail.mstatus + ""
		this.callDocsData()
	},
	methods: {
		moment,
		callFileInfoDownload(record) {
			this.loading = true
			sysFileInfoDownload({ id: record.fileId })
				.then(res => {
					this.loading = false
					this.downloadfile(res)
				})
				.catch(err => {
					console.log(err)
					this.loading = false
					this.$message.error("下载错误：获取文件流错误")
				})
		},
		downloadfile(res) {
			var blob = new Blob([res.data], { type: "application/octet-stream;charset=UTF-8" })
			var contentDisposition = res.headers["content-disposition"]
			var patt = new RegExp("filename=([^;]+\\.[^\\.;]+);*")
			var result = patt.exec(contentDisposition)
			var filename = result[1]
			var downloadElement = document.createElement("a")
			var href = window.URL.createObjectURL(blob) // 创建下载的链接
			var reg = /^["](.*)["]$/g
			downloadElement.style.display = "none"
			downloadElement.href = href
			downloadElement.download = decodeURI(filename.replace(reg, "$1")) // 下载后文件名
			document.body.appendChild(downloadElement)
			downloadElement.click() // 点击下载
			document.body.removeChild(downloadElement) // 下载完成移除元素
			window.URL.revokeObjectURL(href)
		},
		topreview(record) {
			window.open(
				process.env.VUE_APP_API_BASE_URL + "/sysFileInfo/preview?id=" + record.fileId + "#navpanes=0",
				"_blank"
			)
		},
		callFileInfoDelete(record) {
			this.loading = true
			let $params = {
				id: record.fileId
			}
			sysFileInfoDelete($params)
				.then(res => {
					if (res.success) {
						let params = {}
						params["docName"] = null
						params["id"] = record.id
						params["action"] = 3
						record.fileId = "0"
						record.score = null
						record.actualDate = null
						this.callCount()
						this.callSaveDoc(params, record, "docName")
					} else {
						this.$message.error("删除失败：" + res.message)
					}
				})
				.catch(err => {
					this.$message.error("删除错误：" + err.message)
				})
				.finally(res => {
					this.loading = false
				})
		},
		handleChange(info, record) {
			this.loading = true
			let fileList = [...info.fileList]
			this.fileList = fileList.slice(-1)

			if (info.file.status !== "uploading") {
			}
			if (info.file.status === "done") {
				let res = info.file.response
				if (res.success) {
					let params = {}
					params["docName"] = res.data.fileOriginName
					params["id"] = record.id
					params["fileId"] = res.data.id
					params["action"] = 2
					record.fileId = res.data.id
					record.actualDate = moment().format("YYYY-MM-DD")
					this.callCount()
					this.callSaveDoc(params, record, "docName")
				} else {
					this.$message.error(res.message)
					this.loading = false
				}
			} else if (info.file.status === "error") {
				this.$message.error(`${info.file.name} 文件上传失败`)
				this.loading = false
			}
		},

		handleEdit() {
			this.$emit("edit")
		},
		changeSelect(val, record, colName) {
			let params = {}
			params[colName] = val
			params["id"] = record.id
			if (colName == "score") {
				params["action"] = 1
			}
			this.callSaveDoc(params, record, colName)
		},
		callSaveDoc(params, record, colName, flag = false) {
			this.loading = true
			saveDoc(params)
				.then(res => {
					if (res.success) {
						record[colName] = params[colName]
						if (flag) {
							this.callDocsData()
						}
					} else {
						this.$message.error("保存失败：" + res.message)
					}
				})
				.finally(res => {
					this.callCount()
					this.loading = false
				})
		},
		// 获取表格资料
		callDocsData() {
			this.loading = true
			const stage = this.projectdetail.productStageItems.find(e => e.stage == this.stage)
			this.queryParam.planDate = stage ? stage.planReviewDate : null
			getDocList(this.queryParam)
				.then(res => {
					if (res.success) {
						if (this.hasPerm("docs:add")) {
							let index = this.columns.findIndex(item => item.dataIndex == "sort")
							if (index < 0)
								this.columns.push({
									title: "排序",
									dataIndex: "sort",
									scopedSlots: { customRender: "sort" },
									width: 80,
									align: "center"
								})
						}
						let obj = {}
						res.data.forEach(item => {
							if (!obj[item.process]) {
								obj[item.process] = []
							}
							obj[item.process].push(item)
						})
						this.dqePerm = this.hasPerm("docs:dqe") && res.data[0]["isOver"] != "2"
						this.engeerPerm =
							this.hasPerm("docs:engeneer") && (res.data[0]["isOver"] == "0" || res.data[0]["isOver"] == "3")
						this.docsImport = this.hasPerm("docs:import")
						this.dosMap = obj

						if (stage) {
							this.queryParam.stageIssueId = stage.issueId
							this.callGetTransition(this.queryParam)
						}

						this.callCount()
					} else {
						this.$message.error(res.message, 1)
					}
					this.loading = false
				})
				.catch(err => {
					this.loading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		},
		callGetTransition(params) {
			this.loading = true
			getTransition(params)
				.then(res => {
					if (res.success) {
						let btns = []
						if (this.engeerPerm && !this.dqePerm) {
							// jira 241 === '提交资料'
							btns = res.data.filter(e => e.transitionId == "241")
						} else if (!this.engeerPerm && this.dqePerm) {
							btns = res.data.filter(e => e.transitionId != "241")
						} else if (this.engeerPerm && this.dqePerm) {
							btns = res.data
						}

						this.btns = btns
					} else {
						this.$message.error(res.message, 1)
					}
					this.loading = false
				})
				.catch(err => {
					this.loading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		},
		// 确认资料事件
		callDocsCommit(transitionId) {
			this.loading = true
			this.queryParam.transitionId = transitionId

			// 如果文件齐套率不足
			if (!this._verifyFile()) {
				this.loading = false
				
				

				this.$refs.docsModel.view(this.needUploadDocMap,this.queryParam.planDate)

				this.$message.error("转阶段缺少必要的文件,请补充上传")

				return
			}

			docsCommit(this.queryParam)
				.then(res => {
					if (res.success) {
						this.callDocsData()
					} else {
						this.$message.error(res.message, 1)
					}
					this.loading = false
				})
				.catch(err => {
					this.loading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		},
		// 提交资料校验文件
		_verifyFile() {
			let result = 0
			let docNeedUpload = {}
			for (const key in this.dosMap) {
				this.dosMap[key].forEach(v => {
					if (v.docIsNeed === "1" && v.fileId == 0) {
						v.isNoPassVerify = true
						result++
						if (!docNeedUpload[key]) {
							docNeedUpload[key] = []
						}
						docNeedUpload[key].push(v)
					}
				})
			}

			this.needUploadDocMap = docNeedUpload

			return result > 0 ? false : true
		},
		callAgreeOrnot(transitionId) {
			this.loading = true
			this.queryParam.transitionId = transitionId

			docAgreeOrNot(this.queryParam)
				.then(res => {
					if (res.success) {
						this.callDocsData()
					} else {
						this.$message.error(res.message, 1)
					}
					this.loading = false
				})
				.catch(err => {
					this.loading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		},

		callCount() {
			let sum = 0
			let fileCount = 0
			let score = 0
			for (const key in this.dosMap) {
				for (const item of this.dosMap[key]) {
					if (item.docIsNeed == "1") {
						sum += 1
						if (item.fileId != "0") {
							fileCount += 1
						}
						if (item.score != null) {
							//console.log(item.score)
							score += parseInt(item.score)
						}
					}
				}
			}
			this.conformity = sum == 0 ? sum + "%" : ((fileCount / sum) * 100).toFixed(2) + "%"
			this.effective = sum == 0 ? sum + "%" : ((score / (sum * 10)) * 100).toFixed(2) + "%"
		}
	}
}
</script>

<style lang="less" scoped="">
@import "./docs.less";
.none {
	display: none;
}
</style>
<style scoped="">
@import "./docs.css";
</style>
