<template>
  <div class="container">
    <div class="topic_wid">
      <a-breadcrumb class="breadcrumb" separator="/">
        <a-breadcrumb-item>当前位置：首页</a-breadcrumb-item>
        <a-breadcrumb-item>科研管理</a-breadcrumb-item>
        <a-breadcrumb-item>竞品动态</a-breadcrumb-item>
        <a-breadcrumb-item>竞品分析</a-breadcrumb-item>
        <a-breadcrumb-item>样品追溯管理表</a-breadcrumb-item>
      </a-breadcrumb>
    </div>
    <pbiTitle title="样品追溯管理表" style="margin-top: 8px"></pbiTitle>
    <div class="content" :style="{marginTop: '8px'}">
      <div class="left mr10">
        <div class="left-top">
          <div class="flex-sb-center-row">

            <div style="color: black;font-weight: bolder">样品数:{{echartsData.allCount}}</div>
            <div class="btn-wrap mt5">
              <span>在库:{{echartsData.allSavingCount }}</span>
              <a-divider type="vertical" style="color: #333;"/>
              <span>在测:{{echartsData.allTestingCount }}</span>
              <a-divider type="vertical" style="color: #333;"/>
              <span>拆解:{{echartsData.allDisassembleCount }}</span>
            </div>
          </div>
        </div>
        <div class="left-bottom">
          <div class="overview-block">
            <div class="chart_bar_table" ref="gainOverview"></div>
          </div>
        </div>
      </div>
      <div class="right">
        <tableIndex
          :pageLevel='4'
          :pageTitleShow=false
          :loading='loading'
          @paginationChange="handlePageChange"
          @paginationSizeChange="handlePageChange"
          :tableTotal="tableTotal"
          @tableFocus="tableFocus"
          @tableBlur="tableBlur"
        >
          <template #search>
            <pbiSearchContainer>
              <pbiSearchItem :span="6" label='样品状态'>
                <a-select v-model="queryParam.cellStatus" style="width: 100%" @change='getPageData' allowClear>
                  <a-select-option value="saving">
                    在库
                  </a-select-option>
                  <a-select-option value="testing">
                    在测
                  </a-select-option>
                  <a-select-option value="disassemble">
                    拆解
                  </a-select-option>
                </a-select>
              </pbiSearchItem>
              <pbiSearchItem :span="6" label='样品位置'>
                <a-input @change='getPageData' v-model="queryParam.cellAddress"></a-input>
              </pbiSearchItem>
              <pbiSearchItem :span="6" label='类型'>
                <a-input @change='getPageData' v-model="queryParam.competitiveType"></a-input>
              </pbiSearchItem>
              <pbiSearchItem :span="6" label='厂商'  v-if='isShowAllSearch'>
                <a-input @change='getPageData' v-model="queryParam.factory"></a-input>
              </pbiSearchItem>
              <pbiSearchItem :span="6" label='管理单号'  v-if='isShowAllSearch'>
                <a-input @change='getPageData' v-model="queryParam.code"></a-input>
              </pbiSearchItem>

              <pbiSearchItem :span="6" label='化学体系'  v-if='isShowAllSearch'>
                <a-input @change='getPageData' v-model="queryParam.chemicalSystem"></a-input>
              </pbiSearchItem>
              <pbiSearchItem type='btn' :span="isShowAllSearch ? 12 : 6">
                <div class="secondary-btn">
                  <a-button @click="add" size="small" style="margin-right: 12px" type="primary">新增</a-button>
                </div>
                <div class="secondary-btn">
                  <a-button @click="getPageData" size="small" style="margin-right: 12px" type="primary">查询</a-button>
                </div>
                <div class="secondary-btn">
                  <a-button @click="reset" size="small" style="margin-right: 12px">重置</a-button>
                </div>
                <div class='toggle-btn'>
                  <a-button size='small' type='link' @click='showOrHide'>
                    {{ isShowAllSearch ? '收起' : '展开' }}
                    <span v-if='isShowAllSearch'>
										<a-icon type='double-left'/>
									</span>
                    <span v-else>
										<a-icon type='double-right'/>
									</span>
                  </a-button>
                </div>
              </pbiSearchItem>

            </pbiSearchContainer>
          </template>
          <template #table>
            <ag-grid-vue :style="`height: ${tableHeight}px`"
                         class='table ag-theme-balham'
                         :columnDefs='columns'
                         :rowData='pageData'
                         :defaultColDef='defaultColDef'>
            </ag-grid-vue>
          </template>
        </tableIndex>
      </div>
    </div>
    <sampleAdd @ok="getPageData" ref="sampleAdd"></sampleAdd>
  </div>
</template>

<script>
import {competitiveAnalysisSamplePageList,competitiveAnalysisSampleChartData} from "@/api/modular/system/competitveAnalysisManager"
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import sampleAdd from "./add/sampleAdd.vue";

export default {
  components: {sampleAdd},
  data() {
    return {
      pageNo: 1,
      pageSize: 20,
      tableTotal:0,
      echartsData:{
        allCount: 0,
        allDisassembleCount:0,
        allSavingCount:0,
        allTestingCount:0
      },
      defaultColDef: {
        filter: false,
        floatingFilter: false,
        editable: false,
      },

      isShowAllSearch: false,
      windowHeight: document.documentElement.clientHeight,
      templateHeight: document.documentElement.clientHeight - 40 - 20 - 24 - 10 - 32 - 10 - 14 - 20,
      tableHeight: 0,
      loading: true,
      columns: [
        {
          headerName: '序号',
          field: 'no',
          width: 50,
          cellRenderer: function (params) {
            return parseInt(params.node.id) + 1
          },
        },
        {
          headerName: '管理单号',
          field: 'code',
          width: 110,
          flex:1,
        },
        {
          headerName: '样品编号',
          field: 'cellCode',
          flex:1.2,
          cellStyle: () =>  {return {textAlign:'left'}}
        },
        {
          headerName: '样品状态',
          field: 'cellStatus',
          width: 80,
          cellRenderer: function (params) {
            return params.value == 'testing'?'在测':params.value == 'saving'?'在库':params.value == 'disassemble'?'拆解':params.value
          },
        },{
          headerName: '样品位置',
          field: 'cellAddress',
          width: 80,
        },{
          headerName: '类型',
          field: 'competitiveType',
          width: 60,
        },{
          headerName: '厂商',
          field: 'factory',
          width: 60,
        },{
          headerName: '型号',
          field: 'model',
          width: 50,
        },{
          headerName: '尺寸',
          field: 'competitiveSize',
          width: 60,
        },{
          headerName: '化学体系',
          field: 'chemicalSystem',
          width: 80,
        },{
          headerName: '容量',
          field: 'capacity',
          width: 60,
        },{
          headerName: '应用领域',
          field: 'applicationArea',
          width: 80,
        }

      ],
      loadData: [],
      pageData: [],
      allResultData: [],
      queryParam: {},
    }
  },
  methods: {
    showOrHide(){
      this.isShowAllSearch = !this.isShowAllSearch
      this.tableHeight =  document.documentElement.clientHeight - 40 - 20 - 24 - 10 - 32 - 10 - 14 - 20 - 37 - (this.isShowAllSearch?40:0)
    },
    reset(){
      this.queryParam = {}
      this.getPageData()
    },
    add(){
      this.$refs.sampleAdd.add()
    },

    tableFocus() {
        this.$el.style.setProperty('--scroll-border-bottom-fixed', 'none');
        this.$el.style.setProperty('--scroll-display', 'unset');
        this.$el.style.setProperty('--scroll-border-bottom', '1px solid #Dee1e8');
    },
    // 鼠标移出
    tableBlur() {
        this.$el.style.setProperty('--scroll-border-bottom-fixed', '1px solid #dee1e8');
        this.$el.style.setProperty('--scroll-display', 'none');
        this.$el.style.setProperty('--scroll-border-bottom', 'none');
    },

    handlePageChange(value) {
      let {current, pageSize} = value
      this.pageNo = current
      this.pageSize = pageSize
      //数据分页
      this.getPageData()

    },

    initChart() {
      let chart = this.echarts.init(this.$refs.gainOverview)

      chart.clear()
      let series = []
      for (let i = 0; i < this.echartsData.factoryDataList.length; i++) {
        let factoryData =  this.echartsData.factoryDataList[i]
        series.push({
          name: factoryData.name,
          type: 'bar',
          stack: 'total',
          label: {
            show: true,
            fontSize:10,
            color:'#fff'

          },
          barWidth: '30%',
          emphasis: {
            focus: 'series'
          },
          showBackground: true,
          backgroundStyle: {
            color: '#f2f2f2'
          },
          data: factoryData.data
        },)
      }
      
      const options = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          confine: true,
        },
        color: [ '#0168B7', '#4084D7', '#88C0FF', '#cee5fc', '#e5f3ff'],

        legend: {
          show: true,
          bottom:'2%',
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '5%',
		      top:'2%',
          containLabel: true
        },
        xAxis: {
          show: false,
          type: 'value',
          splitLine: false
        },
        yAxis: {
          type: 'category',
          nameGap: 25,
          inverse: true,//数据逆向
          axisTick: {
            show: false // 不显示坐标轴刻度线
          },
          axisLine: {
            show: false, // 不显示坐标轴线
          },
          axisLabel: {
            length: 12,
            fontSize:12
            // formatter: function (value) {
            //   if (value.length > 6) {
            //     return value.slice(0, 6) + '\n' + value.slice(6);
            //   } else {
            //     return value;
            //   }
            // }
          },
          data: this.echartsData.factoryList,
        },
        series: series
      };
      chart.setOption(options)
      chart.resize()
    },
    getEchartsData(){
      competitiveAnalysisSampleChartData().then(res => {
        this.echartsData = res.data
      }).then(() => {
        this.initChart()
      })
    },
    getPageData(){
      this.loading = true
      competitiveAnalysisSamplePageList({
        ...{
          pageNo: this.pageNo,
          pageSize: this.pageSize
        }, ...this.queryParam
      }).then((res) => {
        if (res.success) {
          this.pageData = res.data.rows
          this.tableTotal = res.data.totalRows

        }
      }).finally(() => {
        if(this.pageData.length == 0 && this.pageNo > 1){
          // this.pageNo -= 1
          this.pageNo = Math.max(1, Math.ceil(this.tableTotal / this.pageSize))
          this.getPageData()
        }
        this.loading = false
      })
    },
    async initGain() {
      await this.getPageData()
      await this.getEchartsData()

      /*await this.callGetTree()
      await this.callGetDeptTree()
      await this.callAffiliatedPlatformTree()
      await this.callGetResultsByParam()
      await this.getAllPassTopic()*/
      // await this.getAllResults()
    }
  },
  created() {
    this.tableHeight = this.templateHeight - 37

  },
  mounted() {
    this.$el.style.setProperty('--scroll-border-bottom-fixed', '1px solid #dee1e8');
    this.$el.style.setProperty('--scroll-display', 'none');
    this.$el.style.setProperty('--scroll-border-bottom', 'none');
    this.initGain()
  }
}
</script>


<style lang="less" scoped="">
//@import './topic.less';
@import '/src/components/pageTool/style/pbiSearchItem.less';

:root {
        --scroll-display: none;
        --scroll-border-bottom: none;
        --scroll-border-bottom-fixed: none;
    }
    /deep/.ag-body-horizontal-scroll{
        border-bottom: var(--scroll-border-bottom) !important;
    }
    /deep/.ag-body-horizontal-scroll-viewport {
        display: var(--scroll-display) !important;
        border-bottom: var(--scroll-border-bottom) !important;
    }

    /deep/.ag-horizontal-left-spacer,
    /deep/.ag-horizontal-right-spacer{
        border-bottom: var(--scroll-border-bottom-fixed) !important;
    }

  .container{
    color: #333;
  }

  .search-container{
    padding: 0;
  }

  /deep/ .ant-breadcrumb{
    font-size: 12px;
  }

  /deep/ .pbi-title .title {
  font-size: 18px;
}


.page-container {
  height: calc(100%)!important;
  border-radius: 4px;
  padding: 12px;
}

/deep/ .ant-modal-footer {
  padding: 0;
}

.page-container {
  margin: 0 !important;
}

.container {
  height: calc(100vh - 40px);
  background: #f4f5fc;
}

.content {
  height: calc(100% - 52px);
  display: flex;
}

/* 主标题 */

.head-title {
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.head-headerName::before {
  width: 8px;
  background: #1890ff;
  margin-right: 8px;
  content: "\00a0"; /* 填充空格 */

  color: #5aaef4;
}

.head-content {
  padding: 0 0 10px;
}

.left {
  width: 300px;
  background: #fff;
  text-align: center;
  border-radius: 4px;
  padding: 12px;
}

.left-top {
  width: 100%;
  margin-bottom: 20px;
}

.flex-sb-center-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
}

.left-bottom {
  width: 100%;
  height: calc(100% - 16px - 20px);
  position: relative;
}

.right {
  width: calc(100% - 300px);
  border-radius: 4px;
}

.top {
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 50px;
}

.center {
  width: 100%;
  display: flex;
  justify-content: space-between;
  height: calc((65% - 10px) * 0.4);
}

.empty-block {
  height: calc(100% - 64px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.empty-block-left {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-25%, -75%);
}

.bottom {
  height: calc(100% - 60px);
}

.block {
  width: 100%;
  height: 100%;
  text-align: center;
  background: #fff;
  border-radius: 10px;
  padding: 10px;
}

.block span,
.overview-block {
  width: 100%;
  height: 100%;
  text-align: center;
  padding:0 10px;
}

.left-top span {
  font-size: 14px;
  /* font-weight: 600; */
}

.dept {
  display: flex;
  justify-content: space-between;
}

.dept span {
  font-size: 14px;
  font-weight: 500;
  color: #1890ff;
}

.chart_bar_table {
  height: calc(100%);
}

.table-wrap {
  height: calc(100% - 30px);
  overflow: auto;
}

.table-wrap button {
  z-index: 88;
}

.status-lamp {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  margin: auto;
}

.mt10 {
  margin-top: 10px;
}

.mr10 {
  margin-right: 10px;
}



/deep/ .ant-pagination-options-size-changer.ant-select {
  display: inline-block;
}
/deep/.search-container .vue-treeselect__multi-value-label{
	white-space: nowrap;
	max-width: 41px;
    overflow: hidden;
    text-overflow: ellipsis;
}
/deep/.search-container .vue-treeselect--searchable.vue-treeselect--multi.vue-treeselect--has-value .vue-treeselect__input-container{
  display: none;
}
/deep/.search-container .vue-treeselect__limit-tip-text{
	margin: 0;
	margin-top: 4px;
	font-weight: initial;
	text-indent: -44px;
    overflow: hidden;
    display: none;
}
</style>