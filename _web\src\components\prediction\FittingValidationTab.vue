<template>
  <div class="prediction-data-section">
    <file-upload-section
      :has-formula="true"
      :uploading="uploading"
      :file-list="fileList"
      :data-loaded="dataLoaded"
      :temperatures="temperatures"
      :socs="socs"
      :data-points="dataPoints"
      @file-uploaded="handleFileUploaded"
      @file-removed="handleFileRemoved"
    />

    <div class="prediction-actions" v-if="dataLoaded">
      <a-button
        type="primary"
        icon="experiment"
        :loading="predicting"
        :disabled="!dataLoaded"
        size="large"
        @click="runPrediction"
      >
        运行预测
      </a-button>
    </div>
  </div>
</template>

<script>
import { api } from '@/api';
import { message } from 'ant-design-vue';
import FileUploadSection from '@/components/common/FileUploadSection.vue';
import predictionMixin from '@/mixins/predictionMixin';

export default {
  name: 'FittingValidationTab',
  components: {
    FileUploadSection
  },
  mixins: [predictionMixin],
  props: {
    modelId: {
      type: [Number, String],
      required: true
    },
    mainFormula: {
      type: String,
      default: ''
    },
    modelDescription: {
      type: String,
      default: ''
    }
  },
  data: () => ({
    // 文件上传相关
    uploading: false,
    fileList: [],
    dataLoaded: false,
    temperatures: [],
    socs: [],
    dataPoints: [],

    // 预测相关
    predicting: false,
    hasPredictionResult: false
  }),
  methods: {
    // 文件上传处理
    handleFileUploaded(data) {
      this.temperatures = data.temperatures || [];
      this.socs = data.socs || [];
      this.dataPoints = data.data_points || [];
      this.fileList = data.fileList || [];
      this.dataLoaded = true;
    },

    handleFileRemoved() {
      this.temperatures = [];
      this.socs = [];
      this.dataPoints = [];
      this.fileList = [];
      this.dataLoaded = false;
    },

    // 运行预测
    async runPrediction() {
      if (!this.dataLoaded) {
        message.warning('请先上传数据文件');
        return;
      }

      this.predicting = true;

      try {
        const requestData = {
          model_id: this.modelId,
          data_points: this.dataPoints,
          temperatures: this.temperatures,
          socs: this.socs,
        };

        const response = await api.data.predictCapacity(requestData);

        if (response.data.success) {
          // 处理预测结果
          this.handlePredictionSuccess(response.data);
        } else {
          message.error(response.data.message || '预测失败');
        }
      } catch (error) {
        console.error('预测请求失败:', error);
        message.error('预测请求失败，请检查网络连接');
      } finally {
        this.predicting = false;
      }
    },

    // 处理预测成功
    handlePredictionSuccess(data) {
      // 将预测数据转换为图表组件可接受的格式
      const predictedData = data.predicted_data || [];

      // 处理预测数据，确保实测值来源于原始数据，拟合值来源于预测结果
      const processedData = predictedData.map(point => {

        // 确保数据点有正确的原始数据引用
        const processedPoint = {
          ...point,
          // 使用原始数据的days和capacities作为实测值
          days: point.original_days || point.days,
          capacities: point.original_capacities || point.capacities,
          // 预测结果保持不变
          fitted_capacity: point.fitted_capacity || point.predicted_capacity
        };
        return processedPoint;
      });

      // 创建计算后的数据点（用于显示拟合曲线）
      const calculatedDataPoints = predictedData.map(point => {
        return {
          ...point,
          // 拟合值数据
          days: point.days,
          fitted_capacity: point.fitted_capacity || point.predicted_capacity
        };
      });

      // 存储预测结果数据
      const resultData = {
        temperatures: this.temperatures,
        socs: this.socs,
        dataPoints: processedData,
        calculatedDataPoints: calculatedDataPoints, // 添加计算后的数据点
        modelInfo: {
          id: this.modelId,
          description: this.modelDescription,
          formula: this.mainFormula
        }
      };

      // 更新预测结果状态
      this.hasPredictionResult = true;

      // 直接使用commit方式更新store
      this.$store.commit('setFittingResultData', resultData);
      this.$store.commit('setFittingResultModalVisible', true);

      // 通知用户
      message.success('预测完成，正在显示结果图表');
    },

    // 显示预测结果
    showPredictionResult() {
      if (this.hasPredictionResult) {
        this.$store.commit('setFittingResultModalVisible', true);
      } else {
        message.warning('请先运行预测');
      }
    }
  }
};
</script>

<style scoped>
.prediction-data-section {
  margin-top: 8px;
}

.prediction-actions {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}
</style>
