<template>
	<div class="container" style="position: fixed;left: 0;right: 0;bottom:0;top:40px;">
		<div :style="`width:${elWidth}px;height:${elHeight}px;position: fixed;left: 0;right: 0;margin:0 auto;`">
			<!-- breadcrumb start -->
			<div :style="`width:${_elWidth}px;zoom:${meter_zoom};`">
				<a-breadcrumb class="breadcrumb" separator=">">
					<a-breadcrumb-item
						><a @click="gohome"><a-icon class="rollback-icon" type="rollback" />产品看板</a></a-breadcrumb-item
					>
					<a-breadcrumb-item>产品信息对齐表</a-breadcrumb-item>
				</a-breadcrumb>
			</div>
			<!-- breadcrumb end -->

			<a-spin :spinning="loading">
				<div class="content" :style="`height: ${contentHeight}px;`">
					<!-- echarts start -->
					<div class="chart" :style="`zoom:${meter_zoom};`">
						<!-- 预研产品 -->
						<div class="chart_table" ref="prediction"></div>
						<!-- A|B新产品 -->
						<div class="chart_table" ref="abProduct"></div>
						<!-- 试产新产品 -->
						<div class="chart_table" ref="trialProduction"></div>
						<!-- 量产品 -->
						<div class="chart_table" ref="outputProduct"></div>

						<!-- 其他 -->
						<div class="chart_table" ref="otherProduct"></div>
						<!-- 停止 -->
						<div class="chart_table" ref="stopProduct"></div>
					</div>
					<!-- echarts end -->

					<!-- 筛选框 start -->
					<div class="table-page-search-wrapper" :style="`width:${_elWidth - 50}px;zoom:${meter_zoom};margin:auto`">
						<a-form layout="inline">
							<a-row :gutter="48">
								<a-col :md="6" :sm="24">
									<a-form-item label="立项日期">
										<a-range-picker
											class="filter-form"
											:placeholder="['开始日期', '结束日期']"
											size="small"
											@change="dateChange"
										/>
									</a-form-item>
								</a-col>
								<a-col :md="5" :sm="24">
									<a-form-item label="产品分类">
										<treeselect
											:limit="1"
											class="filter-form"
											@input="changeQuery"
											:max-height="200"
											placeholder="请选择产品分类"
											value-consists-of="BRANCH_PRIORITY"
											v-model="queryparam.productClassification"
											:options="typeOptions"
											:multiple="true"
										/>
									</a-form-item>
								</a-col>
								<a-col :md="5" :sm="24">
									<a-form-item label="产品部门">
										<treeselect
											class="filter-form"
											:limit="1"
											@input="changeQuery"
											:max-height="200"
											placeholder="请选择所属部门"
											:multiple="true"
											:options="departmentCateTreeData"
											value-consists-of="BRANCH_PRIORITY"
											v-model="queryparam.depts"
										>
										</treeselect>
									</a-form-item>
								</a-col>
								<!-- <a-col :md="4" :sm="24">
							<a-form-item label="产品类别">
								<treeselect
									:limit="1"
									@input="changeQuery"
									:max-height="200"
									placeholder="请选择产品类别"
									value-consists-of="BRANCH_PRIORITY"
									v-model="queryparam.//"
									:multiple="true"
									:options="cate"
								/>
							</a-form-item>
						</a-col> -->
								<!-- <a-col :md="4" :sm="24">
							<a-form-item label="产品状态">
								<treeselect
									:limit="1"
									@input="changeQuery"
									:max-height="200"
									placeholder="请选择产品状态"
									value-consists-of="BRANCH_PRIORITY"
									v-model="queryparam.statuses"
									:multiple="true"
									:options="statuses"
								/>
							</a-form-item>
						</a-col> -->

								<!-- <a-col :md="3" :sm="24">
                <a-form-item label="">
                  <treeselect :limit="1" @input="changeQuery" :max-height="200" placeholder="请选择产品类型" value-consists-of="BRANCH_PRIORITY" v-model="queryparam.types" :multiple="true" :options="cate" />
                </a-form-item>
              </a-col> -->

								<a-col :md="5" :sm="24">
									<a-form-item label="">
										<a-input
											size="small"
											class="filter-form"
											@keyup.enter.native="changeQuery"
											v-model="queryparam.keyword"
											placeholder="请输入产品名称"
										>
											<a-icon slot="suffix" type="search" style="color: rgba(0,0,0,.45)" />
										</a-input>
									</a-form-item>
								</a-col>

								<a-col :md="1" :sm="24" :style="{ float: 'right' }">
									<div class="table-page-search-submitButtons" :style="{ float: 'right' }">
										<a-button size="small" style="margin-left: 120px;" type="primary" @click="callReportData"
											>查询</a-button
										>
										<a-button size="small" style="margin-left: 20px;margin-top:6px" @click="resetquery">重置</a-button>
									</div>
								</a-col>
							</a-row>
						</a-form>
					</div>
					<!-- 筛选框 end -->

					<!-- table start -->
					<div class="table-wrapper" :style="`height: ${tableHeight}px;`">
						<ve-table
							v-if="showDetail"
							class="vetable"
							border-y
							rowKeyFieldName="rowKey"
							fixed-header
							:max-height="windowHeight"
							:border="false"
							:columns="tablecolumns"
							:table-data="tabledatas"
							:cell-style-option="cellStyleOption"
							:cell-span-option="cellSpanOption"
							id="loading-container"
							:style="`zoom:${meter_zoom};`"
						/>
					</div>
					<!-- table end -->
				</div>
			</a-spin>
		</div>
	</div>
</template>

<script>
import { getReportTreeData, getCatesTree } from "@/api/modular/system/report"
import { getCateTree } from "@/api/modular/system/topic"
import { mapActions, mapGetters } from "vuex"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"

export default {
	components: {
		Treeselect
	},
	data() {
		return {
			cate: [],
			departmentCateTreeData: [],
			typeOptions: [
				{
					id: 1,
					label: "预研产品"
				},
				{
					id: 2,
					label: "A|B新产品"
				},
				{
					id: 3,
					label: "试产新产品"
				},
				{
					id: 4,
					label: "量产品"
				},
				{
					id: 5,
					label: "其他"
				},
				{
					id: 6,
					label: "停止"
				}
			],
			statuses: [
				{
					id: 0,
					label: "立项讨论"
				},
				{
					id: 1,
					label: "A/B样"
				},
				{
					id: 2,
					label: "C/D样"
				},
				{
					id: 3,
					label: "暂停开发"
				},
				{
					id: 4,
					label: "停产"
				},
				{
					id: 5,
					label: "SOP"
				}
			],
			queryparam: {
				statuses: [],
				depts:[]
			},
			projectStatus: {},
			/* normalizer(node) {
				return {
					id: node.value,
					label: node.title,
				}
			}, */
			statuxTxt: ["", "fail", "warning", "success", "info", "blues", "sop"],
			showDetail: false,
			elWidth: 0,
			_elWidth: 0,
			elHeight: 0,
			zoom: 1,
			meter_zoom: 0,
			//fixedFooter:false,
			windowHeight: document.documentElement.clientHeight - 235,
			loadingInstance: null,
			rowcount: 0,
			columncount: 0,
			tablecolumns: [],
			// 20 :页面padding 18 面包屑 20 20 :margin /padding  110:echarts 40 select
			contentHeight: document.documentElement.clientHeight - 20 - 18 - 20 - 20 - 10,
			tableHeight: document.documentElement.clientHeight - 20 - 18 - 20 - 20 - 10 - 110 - 40 - 20,
			/* footerData: [
        {
          class: "",
        },
      ], */
			columfield: "",
			tabledatas: [],
			merges: ["class"],
			cellStyleOption: {
				bodyCellClass: ({ row, column, rowIndex }) => {
					/* if (row[column.field + "_status"] == 0) {
            return "fail";
          }
          if (row[column.field + "_status"] == 1) {
            return "warning";
          }
          if (row[column.field + "_status"] == 2) {
            return "success";
          }
          if (row[column.field + "_status"] == 3) {
            return "blue";
          }
          if (row[column.field + "_status"] == 4) {
            return "info";
          } */
				}
			},
			cellSpanOption: {
				bodyCellSpan: this.bodyCellSpan
				/* footerCellSpan: this.footerCellSpan, */
			}
		}
	},
	computed: {
		...mapGetters(["userInfo"])
	},
	created() {
		this.loadingInstance = this.$veLoading({
			target: document.querySelector("#loading-container"),
			name: "flow"
		})

		this.callGetTree()
		this.callGetDepartmentCateTree()
		this.callReportData()
		this.initBodySize()

		window.addEventListener("mousewheel", this.handleScroll, { passive: false })
	},
	methods: {
		callGetDepartmentCateTree() {
			this.confirmLoading = true
			getCateTree({
				fieldName: "department"
			})
				.then(res => {
					if (res.success) {
						let cate = []
						for (const item of res.data) {
							let $item = {
								id: parseInt(item.value),
								label: item.title
							}
							
							cate.push($item)
						}
						this.departmentCateTreeData = cate
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
					this.confirmLoading = false
				})
				.catch(err => {
					this.confirmLoading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		},
		gohome() {
			this.$router.push({
				path: "/product_chart"
			})
		},
		callGetTree() {
			this.loading = true
			getCatesTree()
				.then(res => {
					if (res.result) {
						let cate = []
						for (const item of res.data) {
							let $item = {
								id: parseInt(item.value),
								label: item.title
							}
							if (item.children) {
								$item.children = []
								for (const _item of item.children) {
									$item.children.push({
										id: parseInt(_item.value),
										label: _item.title
									})
								}
							}
							cate.push($item)
						}
						this.cate = cate
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
					this.loading = false
				})
				.catch(err => {
					this.loading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		},

		// 获取数据
		callReportData() {
			this.show() //展示loading
			getReportTreeData(this.queryparam)
				.then(res => {
					if (res.result) {
						if (res.data.tablecolumns)
							for (var item of res.data.tablecolumns /* .tablecolumns[0].children */) {
								//res.data.tablecolumns[0].renderHeaderCell = this.renderHeaderCellTop
								if (item.children) {
									for (var _item of item.children) {
										_item.renderHeaderCell = this.renderHeaderCell
										_item.renderBodyCell = this.renderBodyCell
									}
									this.columfield = item.children[0].field
									item.renderHeaderCell = this.renderHeaderCell
								} /* else if (item.field == "class") {
                item.renderFooterCell = this.renderFooterCell;
              } */ else {
									item.renderHeaderCell = this.renderHeaderCell
									item.renderBodyCell = this.renderBodyCell
								}
							}
						/* if(res.data.tabledatas)
            this.merges.forEach((item) => {
              for (let i = 0, j = res.data.tabledatas.length; i < j; i++) {
                let rowSpan = 0;
                let n = i;
                while (
                  res.data.tabledatas[n + 1] &&
                  res.data.tabledatas[n + 1][item] == res.data.tabledatas[n][item]
                ) {
                  rowSpan++;
                  n++;
                  res.data.tabledatas[n].rowSpan = 0;
                }
                if (rowSpan) res.data.tabledatas[i].rowSpan = rowSpan + 1;

                if (!rowSpan) res.data.tabledatas[i].rowSpan = 1;

                i += rowSpan;
              }
            }); */
						this.tablecolumns = res.data.tablecolumns ? res.data.tablecolumns : []
						this.tabledatas = res.data.tabledatas ? res.data.tabledatas : []
						this.rowcount = res.data.rows
						this.columncount = res.data.columns
						this.projectStatus = res.data.chartdatas ? res.data.chartdatas : {}
						this.initChart()
						this.showDetail = true
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
					this.close() //关闭loading
				})
				.catch(err => {
					this.close()
					this.$message.error("错误提示：" + err.message, 1)
				})
		},
		renderHeaderCellTop({ column }) {
			return <span style="font-size:20px;display:block;padding:10px 0;">{column.title}</span>
		},
		renderHeaderCell({ column }) {
			return (
				<span
					class="clickheadstyle"
					onClick={() => {
						this.handleTo(column)
					}}
				>
					{column.title}
				</span>
			)
		},
		renderBodyCell({ row, column, rowIndex }) {
			if (rowIndex < this.rowcount) {
				let statusCls = this.statuxTxt[row[column.field + "_productClassification"]]

				if (!row[column.field + "_isAllow"]) {
					return (
						<div class="statusrow">
							<span class={statusCls}></span>
							<span>{row[column.key]}</span>
						</div>
					)
				}
				return (
					<div
						class="statusrow clickheadstyle"
						onClick={() => {
							this.goto(row, column)
						}}
					>
						<span class={statusCls}></span>
						<span>{row[column.key]}</span>
					</div>
					/* <span
            class="clickheadstyle"
            onClick={() => {
              this.goto(row,column);
            }}
          >
            {row[column.key]}
          </span> */
				)
			}
			/* if (row[column.key] > 0) {
        return (
          <span
            class="clickheadstyle"
            onClick={() => {
              this.gotoflag(rowIndex)
            }}
          >
            {row[column.key]}
          </span>
        );
      } */
			return <span>{row[column.key]}</span>
		},
		/* renderFooterCell({ row, column, rowIndex }) {
      return (
        <div class="tips">
          <span>
            <label>立项讨论</label>
            <label class="pink"></label>
          </span>
          <span>
            <label>A/B样</label>
            <label class="yellow"></label>
          </span>
          <span>
            <label>C/D样</label>
            <label class="green"></label>
          </span>
          <span>
            <label>停产</label>
            <label class="blue"></label>
          </span>
          <span>
            <label>开发暂停</label>
            <label class="brown"></label>
          </span>
        </div>
      );
    }, */
		bodyCellSpan({ row, column, rowIndex }) {
			if (this.merges.includes(column.field)) {
				const _col = row.rowSpan > 0 ? 1 : 0
				return {
					colspan: _col,
					rowspan: row.rowSpan
				}
			}
			if (rowIndex >= this.rowcount) {
				if (column.field == this.columfield) {
					return {
						colspan: this.columncount,
						rowspan: 1
					}
				} else {
					return {
						colspan: 0,
						rowspan: 0
					}
				}
			}
		},
		/* footerCellSpan({ row, column, rowIndex }) {
      if (column.field == "class") {
        return {
          rowspan: 1,
          colspan: this.columncount + 1,
        };
      } else {
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    }, */
		handleTo(column) {
			this.switchApp()
			let $query = {
				//cateName: column.title,
			}
			if (column.pid > 1) {
				//$query.parentId = column.pid;
				$query.cateId = column.id
			} else {
				//$query.parentId = column.id;
				$query.cateId = column.id
			}
			this.$router.push({
				path: "/product_dashboard",
				query: $query
			})
		},
		goto(row, column) {
			this.switchApp()
			let $query = {
				//cateName: column.title,
			}

			if (row[column.key + "_pid"] == row[column.key + "_key"]) {
				//$query.parentId = row[column.key + "_pid"];
				$query.cateId = row[column.key + "_pid"]
			} else {
				//$query.parentId = row[column.key + "_pid"];
				$query.cateId = row[column.key + "_key"]
			}
			$query.projectId = row[column.key + "_projectIds"]

			this.$router.push({
				path: "/product_dashboard",
				query: $query
			})
		},
		/* gotoflag(rowIndex) {
      this.switchApp()
      let $query = {};
      if (rowIndex == this.rowcount) {
        $query.flag = 0;
        $query.cateName = "立项讨论";
      }
      if (rowIndex == this.rowcount + 1) {
        $query.flag = 1;
        $query.cateName = "A/B样";
      }
      if (rowIndex == this.rowcount + 2) {
        $query.flag = 2;
        $query.cateName = "C/D样";
      }
      if (rowIndex == this.rowcount + 3) {
        $query.flag = 3;
        $query.cateName = "停产";
      }

      if (rowIndex == this.rowcount + 4) {
        $query.flag = 4;
        $query.cateName = "开发暂停";
      }

      if (rowIndex == this.rowcount + 5) {
        $query.cateName = "";
      }

      this.$router.push({
        path: "/product_dashboard",
        query: $query,
      });
    }, */
		initBodySize() {
			this.initWidth = document.documentElement.clientWidth // 拿到父元素宽
			this.initHeight = (document.documentElement.clientHeight * document.documentElement.clientWidth) / this.initWidth // 根据宽计算高实现自适应
			this.elWidth = this.initWidth
			this._elWidth = this.elWidth * 0.94
			this.elHeight = this.initHeight
			this.meter_zoom = 1
		},
		handleScroll(e) {
			if (e.ctrlKey) {
				// 取消浏览器默认的放大缩小网页行为
				e.preventDefault()
				// 判断是向上滚动还是向下滚动
				if (e.deltaY > 0) {
					// 放大重写，业务代码
					this.handwheel(e)
				} else {
					// 缩小重写，业务代码
					this.handwheel(e)
				}
			}
		},
		handwheel(e) {
			if (e.wheelDelta < 0) {
				this.zoom -= 0.05
			} else {
				this.zoom += 0.05
			}
			if (this.zoom >= 1.05) {
				this.windowHeight = document.documentElement.clientHeight - 170
				this.zoom = 1
				return
			}
			if (this.zoom <= 0.75) {
				this.zoom = 0.75
				return
			}

			this.elWidth = this.initWidth * this.zoom
			this._elWidth = this.elWidth * 0.97
			this.elHeight = this.initHeight * this.zoom
			this.windowHeight = document.documentElement.clientHeight - 170
			this.meter_zoom = this.zoom
			this.initChart()
		},
		switchApp() {
			/* const apps = Vue.ls.get(ALL_APPS_MENU)
                const _newApps = []
                for (const item of apps) {
                    
                    _newApps.push(item)
                }
                Vue.ls.set(ALL_APPS_MENU, _newApps) */
		},

		/**
		 * loading事件
		 */
		show() {
			this.loadingInstance.show()
		},
		close() {
			this.loadingInstance.close()
		},

		...mapActions(["MenuChange"]),

		initChart() {
			this.$nextTick(() => {
				this.initPrediction()
				this.initAbProduct()
				this.initTrialProduction()
				this.initOutputProduct()
				this.initOtherProduct()
				this.initStopProduct()
			})
		},

		// 数据筛选
		changeQuery(value, label, extra) {
			this.callReportData()
		},

		// 数据筛选重置
		resetquery() {
			this.queryparam = {
				//productCates: [],
				//cates: [],
				statuses: [],
				keyword: null
			}
			this.callReportData()
		},

		// 日期修改
		dateChange(date, dateString) {
			if (dateString[0] != null && dateString[0] != "") {
				this.queryparam.startDate = dateString[0]
			} else {
				this.queryparam.startDate = null
			}
			if (dateString[1] != null && dateString[1] != "") {
				this.queryparam.endDate = dateString[1]
			} else {
				this.queryparam.endDate = null
			}

			this.callReportData()
		},

		/***
		 * echarts
		 */

		//  预研产品
		initPrediction() {
			let chart = this.echarts.init(this.$refs.prediction)
			chart.off("click")
			let status = this.projectStatus
			let datas = [
				{
					name: "预研产品",
					value: parseInt(status["0"])
				},
				{
					name: "其余",
					value: parseInt(status["z"]) - parseInt(status["0"])
				}
			]

			let sum = parseInt(status["z"])

			chart.clear()
			const options = {
				tooltip: {
					trigger: "item"
				},
				legend: {
					show: false,
					itemWidth: 8,
					itemHeight: 8,
					y: "bottom",
					x: "center",
					textStyle: {
						fontSize: 10
					}
				},
				color: ["#ed7d31", "#eceaea"],
				grid: {},
				series: [
					{
						type: "pie",
						minAngle: 10,
						radius: ["95%", "120%"],
						center: ["50%", "59%"],
						itemStyle: {
							borderRadius: 4,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: true,
							position: "center",
							color: "#ed7d31",
							formatter: `预研产品\n${status["0"]}/${sum}`,
							fontSize: "13",
							lineHeight: 24
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					},
					{
						type: "pie",
						minAngle: 10,
						radius: ["95%", "120%"],
						center: ["50%", "59%"],
						itemStyle: {
							borderRadius: 4,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: false,
							formatter: function(data) {
								return `${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
							}
						},
						tooltip: {
							formatter: function(data) {
								return `<span style="display:inline-block;margin-right:6px;border-radius:10px;width:10px;height:10px;background-color:${
									data.color
								};"></span>
                            ${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
							}
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					}
				]
			}
			chart.setOption(options)
			let that = this
			chart.on("click", function(params) {
				that.queryparam.productClassification = []
				that.queryparam.productClassification.push(1)
				that.callReportData()
			})
			chart.resize()
		},
		// A|B新产品
		initAbProduct() {
			let chart = this.echarts.init(this.$refs.abProduct)
			chart.off("click")

			let status = this.projectStatus

			let datas = [
				{
					name: "A|B新产品",
					value: parseInt(status["1"])
				},
				{
					name: "其余",
					value: parseInt(status["z"]) - parseInt(status["1"])
				}
			]

			let sum = parseInt(status["z"])

			chart.clear()
			const options = {
				tooltip: {
					trigger: "item"
				},
				legend: {
					show: false,
					itemWidth: 8,
					itemHeight: 8,
					y: "bottom",
					x: "center",
					textStyle: {
						fontSize: 10
					}
				},
				color: ["#7CBBF3", "#eceaea"],
				grid: {},
				series: [
					{
						type: "pie",
						minAngle: 10,
						radius: ["95%", "120%"],
						center: ["50%", "59%"],
						itemStyle: {
							borderRadius: 4,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: true,
							position: "center",
							color: "#7CBBF3",
							formatter: `A|B新产品\n${status["1"]}/${sum}`,
							fontSize: "13",
							lineHeight: 24
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					},
					{
						type: "pie",
						minAngle: 10,
						radius: ["95%", "120%"],
						center: ["50%", "59%"],
						itemStyle: {
							borderRadius: 4,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: false,
							formatter: function(data) {
								return `${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
							}
						},
						tooltip: {
							formatter: function(data) {
								return `<span style="display:inline-block;margin-right:6px;border-radius:10px;width:10px;height:10px;background-color:${
									data.color
								};"></span>
                            ${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
							}
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					}
				]
			}
			chart.setOption(options)
			let that = this
			chart.on("click", function(params) {
				that.queryparam.productClassification = []
				that.queryparam.productClassification.push(2)
				that.callReportData()
			})
			chart.resize()
		},
		// 试产新产品
		initTrialProduction() {
			let chart = this.echarts.init(this.$refs.trialProduction)
			chart.off("click")

			let status = this.projectStatus

			let datas = [
				{
					name: "试产新产品",
					value: parseInt(status["2"])
				},
				{
					name: "其余",
					value: parseInt(status["z"]) - parseInt(status["2"])
				}
			]

			let sum = parseInt(status["z"])

			chart.clear()
			const options = {
				tooltip: {
					trigger: "item"
				},
				legend: {
					show: false,
					itemWidth: 8,
					itemHeight: 8,
					y: "bottom",
					x: "center",
					textStyle: {
						fontSize: 10
					}
				},
				color: ["#5B9BD5", "#eceaea"],
				grid: {},
				series: [
					{
						type: "pie",
						minAngle: 10,
						radius: ["95%", "120%"],
						center: ["50%", "59%"],
						itemStyle: {
							borderRadius: 4,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: true,
							position: "center",
							color: "#5B9BD5",
							formatter: `试产新产品\n${status["2"]}/${sum}`,
							fontSize: "13",
							lineHeight: 24
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					},
					{
						type: "pie",
						minAngle: 10,
						radius: ["95%", "120%"],
						center: ["50%", "59%"],
						itemStyle: {
							borderRadius: 4,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: false,
							formatter: function(data) {
								return `${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
							}
						},
						tooltip: {
							formatter: function(data) {
								return `<span style="display:inline-block;margin-right:6px;border-radius:10px;width:10px;height:10px;background-color:${
									data.color
								};"></span>
                            ${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
							}
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					}
				]
			}
			chart.setOption(options)
			let that = this
			chart.on("click", function(params) {
				that.queryparam.productClassification = []
				that.queryparam.productClassification.push(3)
				that.callReportData()
			})
			chart.resize()
		},
		// 量产品
		initOutputProduct() {
			let chart = this.echarts.init(this.$refs.outputProduct)
			chart.off("click")

			let status = this.projectStatus

			let datas = [
				{
					name: "量产品",
					value: parseInt(status["3"])
				},
				{
					name: "其余",
					value: parseInt(status["z"]) - parseInt(status["3"])
				}
			]

			let sum = parseInt(status["z"])

			chart.clear()
			const options = {
				tooltip: {
					trigger: "item"
				},
				legend: {
					show: false,
					itemWidth: 8,
					itemHeight: 8,
					y: "bottom",
					x: "center",
					textStyle: {
						fontSize: 10
					}
				},
				color: ["#4472c4", "#eceaea"],
				grid: {},
				series: [
					{
						type: "pie",
						minAngle: 10,
						radius: ["95%", "120%"],
						center: ["50%", "59%"],
						itemStyle: {
							borderRadius: 4,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: true,
							position: "center",
							color: "#4472c4",
							formatter: `量产品\n${status["3"]}/${sum}`,
							fontSize: "13",
							lineHeight: 24
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					},
					{
						type: "pie",
						minAngle: 10,
						radius: ["95%", "120%"],
						center: ["50%", "59%"],
						itemStyle: {
							borderRadius: 4,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: false,
							formatter: function(data) {
								return `${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
							}
						},
						tooltip: {
							formatter: function(data) {
								return `<span style="display:inline-block;margin-right:6px;border-radius:10px;width:10px;height:10px;background-color:${
									data.color
								};"></span>
                            ${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
							}
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					}
				]
			}
			chart.setOption(options)
			let that = this
			chart.on("click", function(params) {
				that.queryparam.productClassification = []
				that.queryparam.productClassification.push(4)
				that.callReportData()
			})
			chart.resize()
		},
		// 其他
		initOtherProduct() {
			let chart = this.echarts.init(this.$refs.otherProduct)
			chart.off("click")

			let status = this.projectStatus

			let datas = [
				{
					name: "其他",
					value: parseInt(status["4"])
				},
				{
					name: "其余",
					value: parseInt(status["z"]) - parseInt(status["4"])
				}
			]

			let sum = parseInt(status["z"])

			chart.clear()
			const options = {
				tooltip: {
					trigger: "item"
				},
				legend: {
					show: false,
					itemWidth: 8,
					itemHeight: 8,
					y: "bottom",
					x: "center",
					textStyle: {
						fontSize: 10
					}
				},
				color: ["#f6d530", "#eceaea"],
				grid: {},
				series: [
					{
						type: "pie",
						minAngle: 10,
						radius: ["95%", "120%"],
						center: ["50%", "59%"],
						itemStyle: {
							borderRadius: 4,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: true,
							position: "center",
							color: "#f6d530",
							formatter: `其他\n${status["4"]}/${sum}`,
							fontSize: "13",
							lineHeight: 24
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					},
					{
						type: "pie",
						minAngle: 10,
						radius: ["95%", "120%"],
						center: ["50%", "59%"],
						itemStyle: {
							borderRadius: 4,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: false,
							formatter: function(data) {
								return `${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
							}
						},
						tooltip: {
							formatter: function(data) {
								return `<span style="display:inline-block;margin-right:6px;border-radius:10px;width:10px;height:10px;background-color:${
									data.color
								};"></span>
                            ${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
							}
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					}
				]
			}
			chart.setOption(options)
			let that = this
			chart.on("click", function(params) {
				that.queryparam.productClassification = []
				that.queryparam.productClassification.push(5)
				that.callReportData()
			})
			chart.resize()
		},
		// 停止
		initStopProduct() {
			let chart = this.echarts.init(this.$refs.stopProduct)
			chart.off("click")

			let status = this.projectStatus

			let datas = [
				{
					name: "停止",
					value: parseInt(status["5"])
				},
				{
					name: "其余",
					value: parseInt(status["z"]) - parseInt(status["5"])
				}
			]

			let sum = parseInt(status["z"])

			chart.clear()
			const options = {
				tooltip: {
					trigger: "item"
				},
				legend: {
					show: false,
					itemWidth: 8,
					itemHeight: 8,
					y: "bottom",
					x: "center",
					textStyle: {
						fontSize: 10
					}
				},
				color: ["#a5a5a5", "#eceaea"],
				grid: {},
				series: [
					{
						type: "pie",
						minAngle: 10,
						radius: ["95%", "120%"],
						center: ["50%", "59%"],
						itemStyle: {
							borderRadius: 4,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: true,
							position: "center",
							color: "#a5a5a5",
							formatter: `停止\n${status["5"]}/${sum}`,
							fontSize: "13",
							lineHeight: 24
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					},
					{
						type: "pie",
						minAngle: 10,
						radius: ["95%", "120%"],
						center: ["50%", "59%"],
						itemStyle: {
							borderRadius: 4,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: false,
							formatter: function(data) {
								return `${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
							}
						},
						tooltip: {
							formatter: function(data) {
								return `<span style="display:inline-block;margin-right:6px;border-radius:10px;width:10px;height:10px;background-color:${
									data.color
								};"></span>
                            ${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
							}
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					}
				]
			}
			chart.setOption(options)
			let that = this
			chart.on("click", function(params) {
				that.queryparam.productClassification = []
				that.queryparam.productClassification.push(6)
				that.callReportData()
			})
			chart.resize()
		}
	},
	beforeDestroy() {
		window.removeEventListener("mousewheel", this.handleScroll)
	},
	destroyed() {
		this.loadingInstance.destroy()
	}
}
</script>
<style scoped="">
html,
body,
.ant-layout {
	background: #fff;
}
</style>
<style lang="less">
@import "./vetable.less";
.fail {
	background: #ed7d31 !important;
	/* color: #000; */
	display: inline-block;
	width: 9px;
	height: 9px;
	border-radius: 50%;
	margin-right: 3px;
	margin-left: 3px;
}

.success {
	background: #5b9bd5 !important;
	/* color: #000; */
	display: inline-block;
	width: 9px;
	height: 9px;
	border-radius: 50%;
	margin-right: 3px;
	margin-left: 3px;
}

.warning {
	background: #7cbbf3 !important;
	/* color: #000; */
	display: inline-block;
	width: 9px;
	height: 9px;
	border-radius: 50%;
	margin-right: 3px;
	margin-left: 3px;
}

.info {
	background: #4472c4 !important;
	display: inline-block;
	/* color: #909399; */
	width: 9px;
	height: 9px;
	border-radius: 50%;
	margin-right: 3px;
	margin-left: 3px;
}
.sop {
	background: #a5a5a5 !important;
	/* color: #909399; */
	display: inline-block;
	width: 9px;
	height: 9px;
	border-radius: 50%;
	margin-right: 3px;
	margin-left: 3px;
}
.blues {
	background: #f6d530 !important;
	/* color: #000; */
	display: inline-block;
	width: 9px;
	height: 9px;
	border-radius: 50%;
	margin-right: 3px;
	margin-left: 3px;
}
.statusrow {
	text-align: left;
	// display: flex;
	// align-items: center;
	// justify-content: start;
}
.chart {
	display: flex;
	height: 110px;
	border: 1px solid #e8e8e8;
}
.chart .chart_table {
	flex: 1;
	height: 100%;
	margin: auto 2px;
	padding: 0 0 5px;
}
/* .vetable{
  zoom: 1;
}
@media screen and (max-width: 1300px){
    .vetable{
        zoom: 75%;
    }
} */
</style>
<style lang="less" scoped="">
/deep/.vue-treeselect__multi-value-item {
	background: transparent;
	font-size: 13px;
	vertical-align: initial;
}
/deep/.vue-treeselect {
	/* display: inline-block; */
	min-width: 80%;
	max-width: 95%;
	margin-top: 4px;
}
/deep/.vue-treeselect__control {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 24px;
	overflow: hidden;
	border-radius: initial;
}
/deep/.vue-treeselect__control * {
	padding: 0 !important;
	margin: 0 !important;
	line-height: initial !important;
	white-space: nowrap;
}
/deep/.vue-treeselect__limit-tip-text {
	margin-top: 2px !important;
}
/deep/.vue-treeselect__value-remove {
	color: #e9e9e9;
}
/deep/.vue-treeselect__multi-value-item {
	color: #695959;
}
/deep/ .ant-col {
	padding: 2px !important;
}
/deep/.table-page-search-wrapper .ant-form-inline .ant-form-item {
	margin: 0;
}
/deep/.ant-row {
	margin: 0 !important;
}
/deep/.table-page-search-wrapper .table-page-search-submitButtons {
	margin: 0;
}

// 筛选框高度
/deep/.ant-input-affix-wrapper .ant-input {
	height: 32px !important;
}

// 为了调整筛选框的统一性
/deep/.vue-treeselect {
	margin-top: 1px;
}

/deep/.vue-treeselect__multi-value-item-container {
	vertical-align: text-top;
}

// 特殊处理 产品类别居中
/deep/.filter-box .vue-treeselect__multi-value-item {
	margin: 8px 0 0;
}

/deep/.vue-treeselect__limit-tip-text {
	margin: 15px 4px 0;
}

// 表头颜色
/deep/.ve-table
	.ve-table-container
	.ve-table-content-wrapper
	table.ve-table-content
	thead.ve-table-header
	tr.ve-table-header-tr
	th.ve-table-header-th {
	color: #333;
	background-color: #f3f3f3 !important;
}

.select-box {
	margin-top: 2px;
}

// 全局
.container {
	background-color: #f0f2f5;
}

.content {
	background-color: #fff;
	margin: 10px;
	padding: 10px;
	border-radius: 10px;
	box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
	overflow: auto;
}

// 面包屑
.breadcrumb {
	padding: 0 10px;
}
.ant-breadcrumb a {
	color: #5d90fa !important;
}
.ant-breadcrumb a:first-child {
	color: rgba(0, 0, 0, 0.65) !important;
}
.ant-breadcrumb {
	font-size: 12px !important;
	color: rgba(0, 0, 0, 0.65) !important;
}
/deep/.ant-breadcrumb .anticon.anticon-home {
	font-size: 19px;
}

// 表格

/deep/.ve-table
	.ve-table-container
	.ve-table-content-wrapper
	table.ve-table-content
	tbody.ve-table-body
	tr.ve-table-body-tr
	td.ve-table-body-td {
	border: none;
	border-bottom: 1px solid #e8e8e8;
	// height: 40px;
}

/deep/.ve-table.ve-table-border-around {
	border: none;
	border-top: 1px solid #e8e8e8;
}

// /deep/.ve-table-header-th:hover {
// 	background-color: #5d90fa !important;
// }

/deep/tr td:hover {
	background-color: #dbe2e4 !important;
	color: #333 !important;
}

/deep/.ve-table
	.ve-table-container
	.ve-table-content-wrapper
	table.ve-table-content
	thead.ve-table-header
	tr.ve-table-header-tr
	th.ve-table-header-th:hover {
	background-color: #dbe2e4 !important;
	color: #333 !important;
}

/deep/.clickheadstyle {
	color: #333 !important;
}
</style>
