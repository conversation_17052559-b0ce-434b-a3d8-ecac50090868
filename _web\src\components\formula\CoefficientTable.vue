<template>
  <div class="coefficient-table-container">
    <a-table
      :columns="customCoefficientColumns"
      :data-source="coefficients"
      row-key="name"
      size="small"
      :pagination="false"
      :scroll="{ x: false }"
      @change="handleTableChange"
    >
      <template slot="name" slot-scope="text">
        <div class="latex-cell" v-html="renderLatex(text)"></div>
      </template>
      <template slot="value" slot-scope="text, record">
        <a-input-number
          :value="record.customValue"
          @change="(value) => handleCoefficientValueChange(record, value)"
          class="value-input"
          size="small"
          placeholder="请输入数值"
          :formatter="formatNumberDisplay"
          :parser="parseNumberInput"
        />
      </template>
      <template slot="describe" slot-scope="text, record">
        <a-input
          :value="describeLookup[record.name] || record.describe || ''"
          @input="(value) => handleDescribeInput(record, value)"
          @blur="() => handleDescribeBlur(record)"
          placeholder="系数说明"
          size="small"
          class="describe-input"
        />
      </template>
    </a-table>
  </div>
</template>

<script>
import formulaMixin from '@/mixins/formulaMixin';
import coefficientMixin from '@/mixins/coefficientMixin';

export default {
  name: 'CoefficientTable',
  mixins: [formulaMixin, coefficientMixin],
  props: {
    coefficients: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      describeLookup: {},
      customCoefficientColumns: [
        {
          title: '系数',
          dataIndex: 'name',
          key: 'name',
          width: '15%',
          scopedSlots: { customRender: 'name' },
        },
        {
          title: '值',
          dataIndex: 'value',
          key: 'value',
          width: '45%',
          scopedSlots: { customRender: 'value' },
        },
        {
          title: '说明',
          dataIndex: 'describe',
          key: 'describe',
          width: '40%',
          scopedSlots: { customRender: 'describe' },
        }
      ]
    };
  },
  watch: {
    coefficients: {
      handler(newCoefs) {
        // 当系数数据发生变化时，重新初始化描述查找表
        this.initializeDescribeLookup();

        // 强制更新视图
        this.$forceUpdate();
        this.$nextTick(() => {
          this.renderMathJax(true);
        });
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleTableChange() {
      this.renderMathJax(false);
      this.$emit('table-change');
    },

    handleCoefficientValueChange(record, value) {
      const numValue = typeof value === 'string' ? parseFloat(value) : value;
      // 使用Vue.set确保响应式更新
      this.$set(record, 'customValue', numValue);

      // 强制更新视图
      this.$forceUpdate();

      // 发出系数变化事件
      this.$emit('coefficient-change', record);

      // 发出系数更新事件，与系数范围设置保持一致
      this.$emit('coefficients-updated', this.coefficients);
    },

    handleDescribeInput(record, value) {
      // 更新临时的描述查找表
      this.$set(this.describeLookup, record.name, value);
    },

    handleDescribeBlur(record) {
      // 当失去焦点时，将描述值同步到record对象
      const value = this.describeLookup[record.name] || '';
      this.$set(record, 'describe', value);

      // 强制更新视图
      this.$forceUpdate();

      // 发出描述变化事件
      this.$emit('description-change', record);

      // 发出系数更新事件，与系数范围设置保持一致
      this.$emit('coefficients-updated', this.coefficients);
    },

    handleCoefficientDescriptionChange(record) {
      // 保留原有方法以兼容性
      record.describe = record.describe || '';

      // 强制更新视图
      this.$forceUpdate();

      // 发出描述变化事件
      this.$emit('description-change', record);

      // 发出系数更新事件，与系数范围设置保持一致
      this.$emit('coefficients-updated', this.coefficients);
    },

    // 初始化描述查找表
    initializeDescribeLookup() {
      if (this.coefficients?.length) {
        this.coefficients.forEach(coef => {
          this.$set(this.describeLookup, coef.name, coef.describe || '');
        });
      }
    },

    // 格式化数值显示，避免科学计数法
    formatNumberDisplay(value) {
      if (value === null || value === undefined || value === '') return '';

      const numValue = typeof value === 'string' ? parseFloat(value) : value;
      if (isNaN(numValue)) return '';

      // 使用toFixed避免科学计数法，然后移除末尾的0
      return numValue.toFixed(20).replace(/\.?0+$/, '');
    },

    // 解析用户输入的数值
    parseNumberInput(value) {
      if (!value) return null;
      const numValue = parseFloat(value);
      return isNaN(numValue) ? null : numValue;
    }
  },
  mounted() {
    // 初始化描述查找表
    this.initializeDescribeLookup();

    this.$nextTick(() => {
      this.renderMathJax(true);
    });
  }
};
</script>

<style scoped>
.coefficient-table-container {
  width: 100%;
}

.latex-cell {
  min-height: 24px;
  display: flex;
  align-items: center;
  padding: 4px 0;
}

.latex-cell :deep(.MathJax_Display) {
  margin: 0 !important;
}

.latex-cell :deep(.MathJax) {
  display: inline-block !important;
}

.value-input,
.describe-input {
  width: 100%;
  text-align: left;
}

:deep(.ant-table-small) {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

:deep(.ant-table-small .ant-table-thead > tr > th) {
  background-color: #fafafa;
  font-weight: 600;
  color: #333;
  text-align: left;
}

:deep(.ant-table-small .ant-table-tbody > tr > td) {
  padding: 8px;
  text-align: left;
}

:deep(.ant-table-body) {
  overflow-x: visible !important;
}

:deep(.ant-input-number-input) {
  text-align: left !important;
}

:deep(.ant-input) {
  text-align: left !important;
}
</style>
