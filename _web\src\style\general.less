.flex{
  display: flex;
}
.flex-center{
  display: flex;
  justify-content: center;
}
.flex-around{
  display: flex;
  justify-content: space-around;
}

.flex--center {
  display: flex;
  align-items: center;
}

.center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between-center {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.flex-around-center {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

// 小手指
.pointer{
  cursor: pointer;
}
.relative{
  position: relative;
}
.absolute{
  position: absolute;
}

/* 单行省略 */
.singe-line {
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
  white-space: nowrap;

}

/* 两行省略 */
.double-line {
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 三行省略 */
.three-line {
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
