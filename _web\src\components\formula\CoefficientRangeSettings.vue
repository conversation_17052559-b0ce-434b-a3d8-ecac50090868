<template>
  <div class="coefficient-range-settings">
    <h5>系数范围设置</h5>

    <div class="import-export-buttons">
      <div class="title-text">系数范围设置</div>
      <a-button-group>
        <a-button type="primary" size="small" @click="downloadExcelTemplate" class="excel-button">
          <a-icon type="download" /> 下载模板
        </a-button>
        <a-upload
          :beforeUpload="handleExcelImport"
          :showUploadList="false"
          accept=".xlsx,.xls"
        >
          <a-button type="primary" size="small" class="excel-button">
            <a-icon type="upload" /> 导入Excel
          </a-button>
        </a-upload>
      </a-button-group>
    </div>

    <div class="tabs-container">
      <a-tabs
        :default-active-key="firstGroupKey"
        :active-key="activeTabKey"
        @change="handleTabChange"
        :force-render="true"
        class="unified-tabs coefficient-tabs"
      >
        <a-tab-pane
          v-for="group in groupedCoefficients"
          :key="`${group.letter}-group`"
          :tab="`${group.letter}组`"
          :force-render="true"
        >
          <a-table
            :columns="optimizeCoefficientColumns"
            :data-source="group.coefficients"
            row-key="name"
            size="small"
            :pagination="false"
            :scroll="{ x: false }"
            @change="handleTableChange"
          >
            <template slot="name" slot-scope="text">
              <div class="latex-cell" v-html="renderLatex(text)"></div>
            </template>
            <template slot="min" slot-scope="text, record">
              <a-input-number
                :value="record.optRange.min"
                class="range-input"
                size="small"
                @change="(value) => handleOptRangeFieldChange(record, 'min', value)"
                :formatter="formatNumberDisplay"
                :parser="parseNumberInput"
              />
            </template>
            <template slot="max" slot-scope="text, record">
              <a-input-number
                :value="record.optRange.max"
                class="range-input"
                size="small"
                @change="(value) => handleOptRangeFieldChange(record, 'max', value)"
                :formatter="formatNumberDisplay"
                :parser="parseNumberInput"
              />
            </template>
            <template slot="initial" slot-scope="text, record">
              <a-input-number
                :value="record.optRange.initial"
                class="range-input"
                size="small"
                @change="(value) => handleOptRangeFieldChange(record, 'initial', value)"
                :formatter="formatNumberDisplay"
                :parser="parseNumberInput"
              />
            </template>
            <template slot="describe" slot-scope="text, record">
              <a-input
                v-model="describeLookup[record.name]"
                placeholder="系数说明"
                size="small"
                class="describe-input"
                @blur="() => handleInputBlur(record)"
              />
            </template>
          </a-table>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script>
import * as XLSX from 'xlsx';
import { message } from 'ant-design-vue';
import formulaMixin from '@/mixins/formulaMixin';
import { showSuccess, showWarning } from '@/utils/errorUtils';

export default {
  name: 'CoefficientRangeSettings',
  mixins: [formulaMixin],
  props: {
    coefficients: {
      type: Array,
      required: true
    }
  },
  data: () => ({
    groupDescriptions: {},
    describeLookup: {},
    activeTabKey: null,
    firstGroupKey: null,
    // 默认系数配置
    defaultCoefficientsConfig: {
      'A_{0}': { min: -100, max: 100, initial: 0 },
      'A_{1}': { min: -10, max: 10, initial: 0 },
      'A_{2}': { min: -10, max: 10, initial: 0 },

      'B_{1}': { min: -100000, max: 100000, initial: 0 },
      'B_{2}': { min: -100000000, max: 100000000, initial: 0 },
      'B_{3}': { min: -10000000000, max: 10000000000, initial: 0 },

      'C_{0}': { min: -1, max: 1, initial: 0 },
      'C_{1}': { min: -0.00001, max: 0, initial: -0.000001 },
      'C_{2}': { min: 0, max: 0.0001, initial: 0.00001 },

      'D_{1}': { min: 0, max: 0.2, initial: 0.1 },
      'D_{2}': { min: 0, max: 0.3, initial: 0.1 },

      'E_{0}': { min: -0.2, max: 0.2, initial: 0 },
      'E_{1}': { min: -0.1, max: 0.1, initial: 0 },
      'E_{2}': { min: -1, max: 1, initial: 0 },
      'E_{3}': { min: -0.01, max: 0.001, initial: -0.001 },

      'F_{0}': { min: -0.001, max: -0.00001, initial: -0.0001 },
      'F_{1}': { min: 0.001, max: 0.01, initial: 0.005 },
      'F_{2}': { min: 0, max: 1, initial: 0.5 },
      'F_{3}': { min: 250, max: 350, initial: 300 }
    },
    optimizeCoefficientColumns: [
      {
        title: '系数',
        dataIndex: 'name',
        key: 'name',
        width: '10%',
        align: "left",
        scopedSlots: { customRender: 'name' },
      },
      {
        title: '最小值',
        dataIndex: 'optRange.min',
        key: 'min',
        width: '25%',
        align: "left",
        scopedSlots: { customRender: 'min' },
      },
      {
        title: '最大值',
        dataIndex: 'optRange.max',
        key: 'max',
        width: '25%',
        align: "left",
        scopedSlots: { customRender: 'max' },
      },
      {
        title: '初始值',
        dataIndex: 'optRange.initial',
        key: 'initial',
        width: '25%',
        align: "left",
        scopedSlots: { customRender: 'initial' },
      },
      {
        title: '说明',
        dataIndex: 'describe',
        key: 'describe',
        width: '15%',
        align: "left",
        scopedSlots: { customRender: 'describe' },
      }
    ]
  }),
  computed: {
    groupedCoefficients() {
      if (!this.coefficients?.length) return [];

      const groups = {};

      this.coefficients.forEach(coef => {
        const normalizedName = coef.name.replace(/[_{}^\\]/g, '');
        const defaultConfig = this.defaultCoefficientsConfig[normalizedName];

        if (!coef.optRange) {
          this.$set(coef, 'optRange', defaultConfig ? {
            min: defaultConfig.min,
            max: defaultConfig.max,
            initial: defaultConfig.initial
          } : {
            min: 0,
            max: 0,
            initial: 0
          });
        }

        if (!coef.hasOwnProperty('describe')) {
          this.$set(coef, 'describe', '');
        }

        this.$set(this.describeLookup, coef.name, coef.describe || '');

        const letter = coef.name.charAt(0).toUpperCase();

        if (!groups[letter]) {
          groups[letter] = {
            letter,
            coefficients: [],
            description: this.groupDescriptions[letter] || ''
          };
        }

        groups[letter].coefficients.push(coef);
      });

      return Object.values(groups).sort((a, b) => a.letter.localeCompare(b.letter));
    }
  },
  watch: {
    coefficients: {
      handler(newCoefs) {
        if (newCoefs?.length) {
          this.$nextTick(() => {
            this.setFirstGroupKey();
            this.renderMathJax(true);
          });
        }
      },
      immediate: true,
      deep: true
    },
    describeLookup: {
      handler(newValue) {
        Object.keys(newValue).forEach(key => {
          const coef = this.coefficients.find(c => c.name === key);
          if (coef) {
            this.$set(coef, 'describe', newValue[key]);
          }
        });
        this.$emit('coefficients-updated', this.coefficients);
      },
      deep: true
    }
  },
  methods: {
    handleGroupDescriptionChange(groupLetter) {
      const group = this.groupedCoefficients.find(g => g.letter === groupLetter);
      if (group) {
        this.groupDescriptions[groupLetter] = group.description;
        this.$emit('coefficients-updated', this.coefficients);
        this.$emit('group-descriptions-updated', this.groupDescriptions);
      }
    },

    handleTabChange(activeKey) {
      this.activeTabKey = activeKey;
      this.$nextTick(() => {
        const activeIndex = this.groupedCoefficients.findIndex(
          group => `${group.letter}-group` === activeKey
        );

        if (activeIndex !== -1) {
          const selector = `.ant-tabs-content .ant-tabs-tabpane:nth-child(${activeIndex + 1})`;
          this.renderMathJax(true, selector);
        } else {
          this.renderMathJax(true, '.coefficient-tabs');
        }
      });
    },

    handleTableChange() {
      this.renderMathJax();
      this.updateCoefficients();
    },

    handleOptRangeFieldChange(record, field, value) {
      const numValue = typeof value === 'string' ? parseFloat(value) : value;
      if (record?.optRange) {
        this.$set(record.optRange, field, numValue);
      }
      this.$forceUpdate();
      this.$emit('coefficients-updated', this.coefficients);
    },

    updateCoefficients() {
      this.$emit('coefficients-updated', this.coefficients);
      this.$emit('group-descriptions-updated', this.groupDescriptions);
    },

    handleInputBlur(record) {
      this.$set(record, 'describe', this.describeLookup[record.name] || '');
      this.updateCoefficients();
    },

    setFirstGroupKey() {
      if (this.groupedCoefficients?.length) {
        const firstGroup = this.groupedCoefficients[0];
        const newFirstGroupKey = `${firstGroup.letter}-group`;

        if (this.firstGroupKey !== newFirstGroupKey) {
          this.firstGroupKey = newFirstGroupKey;
          const groupKeys = this.groupedCoefficients.map(g => `${g.letter}-group`);
          if (!this.activeTabKey || !groupKeys.includes(this.activeTabKey)) {
            this.activeTabKey = this.firstGroupKey;
          }
        }
      }
    },

    // 格式化数值显示，避免科学计数法
    formatNumberDisplay(value) {
      if (value === null || value === undefined || value === '') return '';

      const numValue = typeof value === 'string' ? parseFloat(value) : value;
      if (isNaN(numValue)) return '';

      // 使用toFixed避免科学计数法，然后移除末尾的0
      return numValue.toFixed(20).replace(/\.?0+$/, '');
    },

    // 解析用户输入的数值
    parseNumberInput(value) {
      if (!value) return null;
      const numValue = parseFloat(value);
      return isNaN(numValue) ? null : numValue;
    },

    downloadExcelTemplate() {
      try {
        const wb = XLSX.utils.book_new();
        const groupedParams = {};

        Object.keys(this.defaultCoefficientsConfig).forEach(paramName => {
          const letter = paramName.charAt(0).toUpperCase();
          if (!groupedParams[letter]) {
            groupedParams[letter] = [];
          }
          groupedParams[letter].push({
            name: paramName,
            config: this.defaultCoefficientsConfig[paramName]
          });
        });

        Object.keys(groupedParams).sort().forEach(letter => {
          const headers = ['参数', '最小值', '最大值', '默认值'];
          const data = [headers];

          groupedParams[letter]
            .sort((a, b) => a.name.localeCompare(b.name))
            .forEach(param => {
              data.push([
                param.name,
                param.config.min,
                param.config.max,
                param.config.initial
              ]);
            });

          const ws = XLSX.utils.aoa_to_sheet(data);
          ws['!cols'] = [{ wch: 15 }, { wch: 15 }, { wch: 15 }, { wch: 15 }];
          XLSX.utils.book_append_sheet(wb, ws, `${letter}组`);
        });

        XLSX.writeFile(wb, '系数范围设置模板.xlsx');
        showSuccess('模板下载成功');
      } catch (error) {
        console.error('下载模板失败:', error);
        showWarning('下载模板失败: ' + error.message);
      }
    },

    handleExcelImport(file) {
      try {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const data = new Uint8Array(e.target.result);
            const wb = XLSX.read(data, { type: 'array' });
            const importedData = {};

            wb.SheetNames.forEach(sheetName => {
              const groupMatch = sheetName.match(/([A-Z])组/);
              if (!groupMatch) return;

              const ws = wb.Sheets[sheetName];
              const sheetData = XLSX.utils.sheet_to_json(ws, { header: 1 });
              if (sheetData.length <= 1) return;

              for (let i = 1; i < sheetData.length; i++) {
                const row = sheetData[i];
                if (row.length < 4) continue;

                const paramName = String(row[0]).trim();
                const min = parseFloat(row[1]);
                const max = parseFloat(row[2]);
                const initial = parseFloat(row[3]);

                if (paramName && !isNaN(min) && !isNaN(max) && !isNaN(initial)) {
                  importedData[paramName] = { min, max, initial };
                }
              }
            });

            this.updateCoefficientsFromImport(importedData);
            showSuccess('Excel导入成功');
          } catch (error) {
            console.error('解析Excel文件失败:', error);
            showWarning('解析Excel文件失败: ' + error.message);
          }
        };

        reader.onerror = () => {
          showWarning('读取文件失败');
        };

        reader.readAsArrayBuffer(file);
        return false;
      } catch (error) {
        console.error('导入Excel失败:', error);
        message.error('导入Excel失败: ' + error.message);
        return false;
      }
    },

    updateCoefficientsFromImport(importedData) {
      if (!importedData || Object.keys(importedData).length === 0) {
        showWarning('导入的Excel文件中没有有效数据');
        return;
      }

      let updatedCount = 0;
      this.coefficients.forEach(coef => {
        if (importedData[coef.name]) {
          const data = importedData[coef.name];
          this.$set(coef.optRange, 'min', data.min);
          this.$set(coef.optRange, 'max', data.max);
          this.$set(coef.optRange, 'initial', data.initial);
          updatedCount++;
        }
      });

      this.$emit('coefficients-updated', this.coefficients);
      this.$nextTick(() => {
        this.renderMathJax(true);
      });
      showSuccess(`成功更新了${updatedCount}个系数的范围设置`);
    }
  },
  mounted() {
    if (this.coefficients?.length) {
      this.coefficients.forEach(coef => {
        this.$set(this.describeLookup, coef.name, coef.describe || '');
      });
    }
    this.setFirstGroupKey();
    this.$nextTick(() => {
      setTimeout(() => {
        this.renderMathJax(true);
      }, 50);
    });
  }
};
</script>

<style scoped>
.coefficient-range-settings {
  width: 100%;
  padding: 0;
  background-color: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.tabs-container {
  padding: 16px;
}

.import-export-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #f0f0f0;
}

.title-text {
  font-size: 15px;
  font-weight: 600;
  color: #666;
}

.excel-button {
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  height: 32px;
  padding: 0 16px;
}

.excel-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

h5 {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 0;
  color: #333;
  padding: 10px 0;
  background-color: #f5f5f5;
  border-bottom: 1px solid #f0f0f0;
  text-align: center;
  border-radius: 8px 8px 0 0;
}

.latex-cell {
  min-height: 24px;
  display: flex;
  align-items: center;
  padding: 4px 0;
}

.range-input,
.describe-input {
  width: 100%;
}

:deep(.ant-table) {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

:deep(.ant-table .ant-table-thead > tr > th) {
  background-color: #fafafa;
  font-weight: 600;
  color: #333;
  text-align: center;
  padding: 12px 8px;
}

:deep(.ant-table .ant-table-tbody > tr > td) {
  padding: 12px 8px;
  text-align: center;
}

:deep(.ant-table .ant-table-tbody > tr:hover > td) {
  background-color: #f0f7ff;
}

:deep(.ant-input-number-input) {
  text-align: center !important;
}

:deep(.ant-input) {
  text-align: left !important;
}

:deep(.ant-table-body) {
  overflow-x: visible !important;
}

:deep(.ant-tabs-nav) {
  margin-bottom: 16px;
}

:deep(.ant-tabs-tab) {
  padding: 8px 16px;
  transition: all 0.3s;
  margin-right: 4px;
  font-weight: 500;
}

:deep(.ant-tabs-tab-active) {
  font-weight: 600;
}

:deep(.ant-tabs-ink-bar) {
  height: 3px;
}



@media (forced-colors: active) {
  :deep(.ant-table .ant-table-thead > tr > th),
  :deep(.ant-table .ant-table-tbody > tr > td) {
    border: 1px solid CanvasText;
  }
}
</style>