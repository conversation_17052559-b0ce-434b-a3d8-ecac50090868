 <template>
    <a-modal title="修改风险" :width="1200" :visible="visible" :confirmLoading="confirmLoading" @ok="handleSubmit" @cancel="handleCancel">
        <a-spin :spinning="confirmLoading">
            <a-form :form="form">
                <a-row :gutter="24">
                    <a-col :md="8" :sm="24">
                        <a-form-item style="display: none;">
                            <a-input v-decorator="['issueId']" />
                        </a-form-item>
                        <a-form-item style="display: none;">
                            <a-input v-decorator="['productCustomer']" />
                        </a-form-item>
                        <a-form-item label="产品阶段" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                            <a-select style="width: 100%" v-decorator="['productStage', {rules: [{required: true, message: '请选择阶段!'}]}]" placeholder="请选择阶段">
                                <a-select-option v-for="(item,i) in getDict('product_stage_status')" :key="i" :value="item.code">{{item.name}}</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :md="8" :sm="24">
                        <a-form-item label="风险分类" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                            <a-select style="width: 100%" v-decorator="['riskCategory', {rules: [{required: true, message: '请选择阶段!'}]}]" placeholder="请选择阶段">
                                <a-select-option v-for="(item,i) in getDict('stage_risk_category')" :key="i" :value="item.code">{{item.name}}</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :md="8" :sm="24">
                        <a-form-item label="风险等级" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                            <a-select style="width: 100%" v-decorator="['riskLevel', {rules: [{required: true, message: '请选择风险等级!'}]}]" placeholder="请选择风险等级">
                                <a-select-option v-for="(item,i) in getDict('stage_risk_level')" :key="i" :value="item.code">{{item.name}}</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row :gutter="24">
                    <a-col :md="8" :sm="24">
                        <a-form-item label="计划完成时间" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                            <a-date-picker placeholder="请选择计划完成时间" @change="onChangeplannedCompletionDate" style="width: 100%" v-decorator="['plannedCompletionDate', {rules: [{required: true, message: '请选择计划完成时间!'}]}]" />
                        </a-form-item>
                    </a-col>
                    <a-col :md="8" :sm="24">
                        <a-form-item label="风险跟进状态" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                            <a-select style="width: 100%" v-decorator="['problemStatus', {rules: [{required: true, message: '请选择风险跟进状态!'}]}]" placeholder="请选择风险跟进状态">
                                <a-select-option v-for="(item,i) in getDict('stage_problem_status')" :key="i" :value="item.code">{{item.name}}</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :md="8" :sm="24">
                        <a-form-item label="责任人" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                            <a-input type='hidden' v-decorator="['responsiblePerson', {rules: [{required: true, message: '请选择责任人！'}]}]" />
                            <a-dropdown v-model="dropdownvisible" placement="bottomCenter" :trigger="['click']">
                                <a-button style="width:100%;display:flex;justify-content: space-between;align-items: center;">{{responsiblePersonName ? responsiblePersonName : '选择责任人'}}
                                    <a-icon type="down" /></a-button>
                                <a-menu slot="overlay">
                                    <a-spin :spinning="loading" style="padding:10px 24px 0 24px;width:100%">
                                        <a-input-search v-model="queryParam.searchValue" placeholder="搜索..." @change="onSearch" />
                                        <s-table style="width:100%;" ref="table" :rowKey="(record) => record.id" :columns="vcolumns" :data="loadData" :customRow="customRow" :scroll="{ y: 120,x:120}">>
                                        </s-table>
                                    </a-spin>
                                </a-menu>
                            </a-dropdown>
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row :gutter="24">
                    
                    <a-col :md="8" :sm="24">
                        <a-form-item label="风险事件" :labelCol="labelCol" :wrapperCol="wrapperCol">
                            <a-textarea :rows="2" placeholder="请输入风险事件" v-decorator="['riskEvent', {rules: [{required: true, message: '请输入风险事件!'}]}]"></a-textarea>
                        </a-form-item>
                    </a-col>
                    <a-col :md="8" :sm="24">
                        <a-form-item label="风险发生的原因" :labelCol="labelCol" :wrapperCol="wrapperCol">
                            <a-textarea :rows="2" placeholder="请输入风险发生的原因" v-decorator="['riskReason', {rules: [{required: true, message: '请填写风险发生的原因!'}]}]"></a-textarea>
                        </a-form-item>
                    </a-col>

                    <a-col :md="8" :sm="24">
                        <a-form-item label="可能导致的结果" :labelCol="labelCol" :wrapperCol="wrapperCol">
                            <a-textarea :rows="2" placeholder="请输入可能导致的结果" v-decorator="['riskResult', {rules: [{required: true, message: '请填写可能导致的结果!'}]}]"></a-textarea>
                        </a-form-item>
                    </a-col>
                </a-row>

                <a-row :gutter="24">

                    <a-col :md="8" :sm="24">
                        <a-form-item label="风险管理措施" :labelCol="labelCol" :wrapperCol="wrapperCol">
                            <a-textarea :rows="2" placeholder="请输入风险管理措施" v-decorator="['riskMeasures', {rules: [{required: true, message: '请填写风险管理措施!'}]}]"></a-textarea>
                        </a-form-item>
                    </a-col>
                </a-row>
            </a-form>
        </a-spin>
    </a-modal>
</template>

<script>
    import Vue from 'vue'
    import {
        DICT_TYPE_TREE_DATA
    } from '@/store/mutation-types'
    import moment from 'moment'
    import {
        stageRiskSave
    } from '@/api/modular/system/report'
    import {
        getUserLists
    } from '@/api/modular/system/userManage'
    import {
        STable
    } from '@/components'
    export default {
        components: {
            STable
        },
        data() {
            return {
                findDate: '',
                plannedCompletionDate: '',
                queryParam: {},
                vqueryParam: {},
                loading: false,
                dropdownvisible: false,
                vdropdownvisible: false,
                vcolumns: [{
                        title: '账号',
                        dataIndex: 'account'
                    },
                    {
                        title: '姓名',
                        dataIndex: 'name'
                    },
                ],
                loadData: parameter => {
                    return getUserLists(Object.assign(parameter, this.queryParam)).then((res) => {
                        return res.data
                    })
                },
                loadvData: parameter => {
                    return getUserLists(Object.assign(parameter, this.vqueryParam)).then((res) => {
                        return res.data
                    })
                },
                labelCol: {
                    xs: {
                        span: 24
                    },
                    sm: {
                        span: 8
                    }
                },
                wrapperCol: {
                    xs: {
                        span: 24
                    },
                    sm: {
                        span: 16
                    }
                },
                visible: false,
                confirmLoading: false,
                form: this.$form.createForm(this),
                responsiblePersonName: '',
                confirmedPersonName: ''
            }
        },
        methods: {
            moment,
            getDict(code) {
                const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
                return dictTypeTree.filter(item => item.code == code)[0].children
            },
            onSearch(e) {
                this.$refs.table.refresh()
            },
            onvSearch(e) {
                this.$refs.vtable.refresh()
            },
            // 初始化方法
            edit(record) {
                this.findDate = ''
                this.plannedCompletionDate = ''
                if (record.findDate != null && record.findDate != '') {
                    this.findDate = record.findDate
                    this.form.getFieldDecorator('findDate', {
                        initialValue: moment(record.findDate, 'YYYY-MM-DD')
                    })
                }
                if (record.plannedCompletionDate != null && record.plannedCompletionDate != '') {
                    this.plannedCompletionDate = record.plannedCompletionDate
                    this.form.getFieldDecorator('plannedCompletionDate', {
                        initialValue: moment(record.plannedCompletionDate, 'YYYY-MM-DD')
                    })
                }
                setTimeout(() => {
                    this.form.setFieldsValue(record)
                    this.responsiblePersonName = record.responsiblePersonName
                }, 100)
                
                this.visible = true
            },
            customRow(row, index) {
                return {
                    on: {
                        click: () => {
                            this.form.setFieldsValue({
                                responsiblePerson: row.account
                            })
                            this.responsiblePersonName = row.name
                            this.dropdownvisible = false
                        }
                    }
                }
            },
            customvRow(row, index) {
                return {
                    on: {
                        click: () => {
                            this.form.setFieldsValue({
                                confirmedPerson: row.account
                            })
                            this.confirmedPersonName = row.name
                            this.vdropdownvisible = false
                        }
                    }
                }
            },
            handleSubmit() {
                const {
                    form: {
                        validateFields
                    }
                } = this
                this.confirmLoading = true
                validateFields((errors, values) => {
                    if (!errors) {
                        if (this.findDate != '') {
                            values.findDate = this.findDate
                        }
                        if (this.plannedCompletionDate != '') {
                            values.plannedCompletionDate = this.plannedCompletionDate
                        }
                        let $params = { ...values
                        }
                        stageRiskSave($params).then((res) => {
                            if (res.result) {
                                if (res.data) {
                                    this.$message.success('修改成功')
                                    this.visible = false
                                    this.confirmLoading = false
                                    this.$emit('ok')
                                    this.handleCancel()
                                } else {
                                    this.$message.error('修改失败')
                                }
                            } else {
                                this.$message.error('修改失败：' + res.message)
                            }
                        }).finally((res) => {
                            this.confirmLoading = false
                        })
                    } else {
                        this.confirmLoading = false
                    }
                })
            },
            handleCancel() {
                this.responsiblePersonName = ''
                this.confirmedPersonName = ''
                this.findDate = ''
                this.plannedCompletionDate = ''
                this.form.resetFields()
                this.visible = false
                this.form.getFieldDecorator('findDate', {
                    initialValue: null
                })
                this.form.getFieldDecorator('plannedCompletionDate', {
                    initialValue: null
                })
            },
            onChangefindDate(date, dateString) {
                if (date == null) {
                    this.findDate = ''
                } else {
                    this.findDate = moment(date).format('YYYY-MM-DD')
                }
            },
            onChangeplannedCompletionDate(date, dateString) {
                if (date == null) {
                    this.plannedCompletionDate = ''
                } else {
                    this.plannedCompletionDate = moment(date).format('YYYY-MM-DD')
                }
            },
        }
    }
</script>
