{"version": 3, "sources": ["webpack://[name]/webpack/universalModuleDefinition", "webpack://[name]/webpack/bootstrap", "webpack://[name]/./sources/ext/marker.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "window", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "render_marker", "marker", "gantt", "config", "show_markers", "start_date", "state", "getState", "max_date", "end_date", "min_date", "div", "document", "createElement", "setAttribute", "id", "css", "templates", "marker_class", "title", "className", "start", "posFromDate", "style", "left", "height", "Math", "max", "getRowTop", "getVisibleTaskCount", "end", "width", "text", "innerHTML", "initMarkerArea", "$task_data", "markerArea", "append<PERSON><PERSON><PERSON>", "$marker_area", "_markers", "createDatastore", "initItem", "uid", "attachEvent", "renderMarkers", "$services", "getService", "createDataRender", "defaultContainer", "add<PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "this", "getItem", "add<PERSON><PERSON><PERSON>", "addItem", "delete<PERSON><PERSON><PERSON>", "exists", "removeItem", "updateMarker", "refresh", "_getMarkers", "getItems"], "mappings": ";;;;;;;;;;;;CAAA,SAAAA,EAAAC,GACA,iBAAAC,SAAA,iBAAAC,OACAA,OAAAD,QAAAD,IACA,mBAAAG,eAAAC,IACAD,OAAA,4BAAAH,GACA,iBAAAC,QACAA,QAAA,0BAAAD,IAEAD,EAAA,0BAAAC,IARA,CASCK,OAAA,WACD,mBCTA,IAAAC,KAGA,SAAAC,EAAAC,GAGA,GAAAF,EAAAE,GACA,OAAAF,EAAAE,GAAAP,QAGA,IAAAC,EAAAI,EAAAE,IACAC,EAAAD,EACAE,GAAA,EACAT,YAUA,OANAU,EAAAH,GAAAI,KAAAV,EAAAD,QAAAC,IAAAD,QAAAM,GAGAL,EAAAQ,GAAA,EAGAR,EAAAD,QA0DA,OArDAM,EAAAM,EAAAF,EAGAJ,EAAAO,EAAAR,EAGAC,EAAAQ,EAAA,SAAAd,EAAAe,EAAAC,GACAV,EAAAW,EAAAjB,EAAAe,IACAG,OAAAC,eAAAnB,EAAAe,GAA0CK,YAAA,EAAAC,IAAAL,KAK1CV,EAAAgB,EAAA,SAAAtB,GACA,oBAAAuB,eAAAC,aACAN,OAAAC,eAAAnB,EAAAuB,OAAAC,aAAwDC,MAAA,WAExDP,OAAAC,eAAAnB,EAAA,cAAiDyB,OAAA,KAQjDnB,EAAAoB,EAAA,SAAAD,EAAAE,GAEA,GADA,EAAAA,IAAAF,EAAAnB,EAAAmB,IACA,EAAAE,EAAA,OAAAF,EACA,KAAAE,GAAA,iBAAAF,QAAAG,WAAA,OAAAH,EACA,IAAAI,EAAAX,OAAAY,OAAA,MAGA,GAFAxB,EAAAgB,EAAAO,GACAX,OAAAC,eAAAU,EAAA,WAAyCT,YAAA,EAAAK,UACzC,EAAAE,GAAA,iBAAAF,EAAA,QAAAM,KAAAN,EAAAnB,EAAAQ,EAAAe,EAAAE,EAAA,SAAAA,GAAgH,OAAAN,EAAAM,IAAqBC,KAAA,KAAAD,IACrI,OAAAF,GAIAvB,EAAA2B,EAAA,SAAAhC,GACA,IAAAe,EAAAf,KAAA2B,WACA,WAA2B,OAAA3B,EAAA,SAC3B,WAAiC,OAAAA,GAEjC,OADAK,EAAAQ,EAAAE,EAAA,IAAAA,GACAA,GAIAV,EAAAW,EAAA,SAAAiB,EAAAC,GAAsD,OAAAjB,OAAAkB,UAAAC,eAAA1B,KAAAuB,EAAAC,IAGtD7B,EAAAgC,EAAA,aAIAhC,IAAAiC,EAAA,0BCjFA,WAcA,SAAAC,EAAAC,GACA,IAAAC,MAAAC,OAAAC,aACA,SAEA,IAAAH,EAAAI,WACA,SAEA,IAAAC,EAAAJ,MAAAK,WACA,MAAAN,EAAAI,YAAAC,EAAAE,YAEAP,EAAAQ,WAAAR,EAAAQ,UAAAH,EAAAI,YAAAT,EAAAI,YAAAC,EAAAI,UAAA,CAGA,IAAAC,EAAAC,SAAAC,cAAA,OAEAF,EAAAG,aAAA,iBAAAb,EAAAc,IAEA,IAAAC,EAAA,eACAd,MAAAe,UAAAC,eACAF,GAAA,IAAAd,MAAAe,UAAAC,aAAAjB,IAEAA,EAAAe,MACAA,GAAA,IAAAf,EAAAe,KAGAf,EAAAkB,QACAR,EAAAQ,MAAAlB,EAAAkB,OAEAR,EAAAS,UAAAJ,EAEA,IAAAK,EAAAnB,MAAAoB,YAAArB,EAAAI,YAGA,GAFAM,EAAAY,MAAAC,KAAAH,EAAA,KACAV,EAAAY,MAAAE,OAAAC,KAAAC,IAAAzB,MAAA0B,UAAA1B,MAAA2B,uBAAA,QACA5B,EAAAQ,SAAA,CACA,IAAAqB,EAAA5B,MAAAoB,YAAArB,EAAAQ,UACAE,EAAAY,MAAAQ,MAAAL,KAAAC,IAAAG,EAAAT,EAAA,QAQA,OAJApB,EAAA+B,OACArB,EAAAsB,UAAA,sCAAAhC,EAAA+B,KAAA,UAGArB,GAGA,SAAAuB,IACA,GAAAhC,MAAAiC,WAAA,CAGA,IAAAC,EAAAxB,SAAAC,cAAA,OACAuB,EAAAhB,UAAA,oBACAlB,MAAAiC,WAAAE,YAAAD,GACAlC,MAAAoC,aAAAF,GAjEAlC,MAAAqC,WACArC,MAAAqC,SAAArC,MAAAsC,iBACAjE,KAAA,SACAkE,SAAA,SAAAxC,GAEA,OADAA,EAAAc,GAAAd,EAAAc,IAAAb,MAAAwC,MACAzC,MAKAC,MAAAC,OAAAC,cAAA,EA0DAF,MAAAyC,YAAA,iCACAzC,MAAAoC,cACAJ,MAGAhC,MAAAyC,YAAA,0BACAzC,MAAAoC,eACAJ,IACAhC,MAAA0C,mBAIA1C,MAAAyC,YAAA,0BACAT,IAEAhC,MAAA2C,UAAAC,WAAA,UACAC,kBACAxE,KAAA,SACAyE,iBAAA,WAA+B,OAAA9C,MAAAoC,gBAE/BW,SAAAjD,KAGAE,MAAAgD,UAAA,SAAAnC,GACA,OAAAoC,KAAAZ,SAEAY,KAAAZ,SAAAa,QAAArC,GAFA,MAKAb,MAAAmD,UAAA,SAAApD,GACA,OAAAkD,KAAAZ,SAAAe,QAAArD,IAGAC,MAAAqD,aAAA,SAAAxC,GACA,QAAAoC,KAAAZ,SAAAiB,OAAAzC,KAGAoC,KAAAZ,SAAAkB,WAAA1C,IACA,IAEAb,MAAAwD,aAAA,SAAA3C,GACAoC,KAAAZ,SAAAoB,QAAA5C,IAGAb,MAAA0D,YAAA,WACA,OAAAT,KAAAZ,SAAAsB,YAGA3D,MAAA0C,cAAA,WACAO,KAAAZ,SAAAoB,WAvHA", "file": "ext/dhtmlxgantt_marker.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"ext/dhtmlxgantt_marker\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ext/dhtmlxgantt_marker\"] = factory();\n\telse\n\t\troot[\"ext/dhtmlxgantt_marker\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/codebase/\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 227);\n", "\n(function(){\n\nif(!gantt._markers) {\n\tgantt._markers = gantt.createDatastore({\n\t\tname: \"marker\",\n\t\tinitItem: function (marker) {\n\t\t\tmarker.id = marker.id || gantt.uid();\n\t\t\treturn marker;\n\t\t}\n\t});\n}\n\ngantt.config.show_markers = true;\n\nfunction render_marker(marker){\n\tif(!gantt.config.show_markers)\n\t\treturn false;\n\n\tif(!marker.start_date)\n\t\treturn false;\n\n\tvar state = gantt.getState();\n\tif(+marker.start_date > +state.max_date)\n\t\treturn;\n\tif((!marker.end_date || +marker.end_date < +state.min_date) && +marker.start_date < +state.min_date)\n\t\treturn;\n\n\tvar div = document.createElement(\"div\");\n\n\tdiv.setAttribute(\"data-marker-id\", marker.id);\n\n\tvar css = \"gantt_marker\";\n\tif(gantt.templates.marker_class)\n\t\tcss += \" \" + gantt.templates.marker_class(marker);\n\n\tif(marker.css){\n\t\tcss += \" \" + marker.css;\n\t}\n\n\tif(marker.title){\n\t\tdiv.title = marker.title;\n\t}\n\tdiv.className = css;\n\n\tvar start = gantt.posFromDate(marker.start_date);\n\tdiv.style.left = start + \"px\";\n\tdiv.style.height = Math.max(gantt.getRowTop(gantt.getVisibleTaskCount()), 0) + \"px\";\n\tif(marker.end_date){\n\t\tvar end = gantt.posFromDate(marker.end_date);\n\t\tdiv.style.width = Math.max((end - start), 0) + \"px\";\n\n\t}\n\n\tif(marker.text){\n\t\tdiv.innerHTML = \"<div class='gantt_marker_content' >\" + marker.text + \"</div>\";\n\t}\n\n\treturn div;\n}\n\nfunction initMarkerArea(){\n\tif(!gantt.$task_data)\n\t\treturn;\n\n\tvar markerArea = document.createElement(\"div\");\n\tmarkerArea.className = \"gantt_marker_area\";\n\tgantt.$task_data.appendChild(markerArea);\n\tgantt.$marker_area = markerArea;\n}\n\ngantt.attachEvent(\"onBeforeGanttRender\", function(){\n\tif(!gantt.$marker_area)\n\t\tinitMarkerArea();\n});\n\ngantt.attachEvent(\"onDataRender\", function(){\n\tif(!gantt.$marker_area){\n\t\tinitMarkerArea();\n\t\tgantt.renderMarkers();\n\t}\n});\n\ngantt.attachEvent(\"onGanttReady\", function(){\n\tinitMarkerArea();\n\n\tvar layers = gantt.$services.getService(\"layers\");\n\tvar markerRenderer = layers.createDataRender({\n\t\tname: \"marker\",\n\t\tdefaultContainer: function(){ return gantt.$marker_area;}\n\t});\n\tmarkerRenderer.addLayer(render_marker);\n});\n\ngantt.getMarker = function(id){\n\tif(!this._markers) return null;\n\n\treturn this._markers.getItem(id);\n};\n\ngantt.addMarker = function(marker){\n\treturn this._markers.addItem(marker);\n};\n\ngantt.deleteMarker = function(id){\n\tif(!this._markers.exists(id))\n\t\treturn false;\n\n\tthis._markers.removeItem(id);\n\treturn true;\n};\ngantt.updateMarker = function(id){\n\tthis._markers.refresh(id);\n};\n\ngantt._getMarkers = function(){\n\treturn this._markers.getItems();\n};\n\ngantt.renderMarkers = function () {\n\tthis._markers.refresh();\n};\n\n})();"], "sourceRoot": ""}