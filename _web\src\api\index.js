import * as formulaApi from './modular/formula/formula';
import * as dataApi from './modular/formula/data';
import request from './modular/formula/request';

// 导出请求实例
export { request };

// 导出所有API接口
export const api = {
  formula: formulaApi,
  data: dataApi
};

// 为了兼容旧代码，提供传统的导出方式
export const {
  parseLatexFormula,
  saveFormula,
  getModels  // 添加这个导出
} = formulaApi;

export const {
  uploadFile,
  fitData,
  calculateCapacity
} = dataApi;

export default api;