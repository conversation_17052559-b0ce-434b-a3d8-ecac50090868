<template>
  <a-col :span="span">
    <div v-if="type === 'search'" class="searchItem">
      <span class="label">{{label}}:</span>
      <div class="content">
        <slot></slot>
      </div>
    </div>
    <div v-if="type === 'btn'" class="searchItem">
      <div class="btn">
        <slot></slot>
      </div>
    </div>
  </a-col>
</template>
<script>
  export default {
    props: {
      type: {
        type: String,
        default: 'search' // search, btn
      },
      span: {
        type: Number,
        default: 8
      },
      label: {
        type: String,
        default: ''
      }
    }
  }
</script>
<style lang="less" scoped>
  .searchItem {
    display: flex;
    align-items: center;
    height: 28px;
    margin-bottom: 12px;
  }

  .searchItem .label {
    display: inline-block;
    width: 100px;
    padding-right: 8px;
    text-align: right;
  }

  .searchItem .content,
  .searchItem .btn {
    width: calc(100% - 100px);
  }

  .searchItem .btn{
    margin-left: 100px;
    display: flex;
    justify-content: flex-end;
    height: 100%;
    align-items: center;
}

</style>