<template>
	<div class="container">
		<!-- 标题 start -->
		<div class="head-title">
			<div class="line mr10"></div>
			<span class="title">DPV测试进展概览</span>
		</div>
		<!-- 标题 end -->
		<!-- 筛选 start -->
		<div class="filter-wrapper mt10">
			<div class="filter-left">
				<div class="filter-block">
					<a-switch checked-children="测试结果" un-checked-children="测试进度" default-checked @change="handleSwitchChange" />
				</div>
			</div>
			<div class="filter-right">
				<a-button size="small" @click="() => isShowAdd = true" type="primary" class="mr10">新增</a-button>
				<a-button v-if="isManager || isTve" size="small" @click="handleEdit"> {{ isEditStatus ? '关闭编辑' : '编辑'}}</a-button>
				<a-button v-if="isEditStatus" size="small" @click="isEditProject = !isEditProject" class="ml10"> {{ isEditProject ? '产品排序' : '项目排序'}}</a-button>
			</div>
		</div>
		<!-- 筛选 end -->
		<!-- 表格 start -->
		<div class="table-wrapper mt10">
			<a-table :columns="tableColumns" :data-source="tableData" size="middle" bordered>
				<span v-for="columns in cyclicColumns" :slot="columns.dataIndex" slot-scope="text, record, index">

					<span v-if="columns.dataIndex === 'productType'" style="white-space:nowrap">
						{{text}}
					</span>


					<span v-if="columns.dataIndex === 'productName'">
						<a-select v-if="isEditStatus && record.isEditProductOrProject && !record.issueId" v-model="record.productName" show-search labelInValue
							option-filter-prop="children" style="width: 120px"
							@change="$event => handleProductNameChange($event,record,index)">
							<a-select-option v-for="productName in productNameOptions" :key="productName.key" :value="productName.key"  :label="productName.label">
								{{ productName.label }}
							</a-select-option>
						</a-select>
						<div v-else style="white-space:nowrap">{{typeof text === 'object' ? text.label : text}}</div>
					</span>

					<span v-else-if="columns.dataIndex === 'projectName'">
						<a-select v-if="isEditStatus && record.isEditProductOrProject && !record.issueId" v-model="record.projectName" labelInValue style="width: 120px" @change="$event => handleProjectNameChange($event,record,index)">
							<a-select-option v-for="projectName in projectNameOptions" :key="projectName.key" :value="projectName.key"  :label="projectName.label">
								{{ projectName.label }}
							</a-select-option>
						</a-select>
						<div v-else style="white-space:nowrap">{{typeof text === 'object' ? text.label : text}}</div>
					</span>

					<span v-else-if="columns.dataIndex === 'phase'">
						<a-select v-if="isEditStatus && record.isEditProductOrProject" v-model="record.phase" style="width: 60px"
							@change="$event => handleSelectChange($event,record,'phase')">
							<a-select-option v-for="(item,i) in getDict('dpv_test_manage_phase')" :key="i"
								:value="item.code">{{item.name}}</a-select-option>
						</a-select>
						<span v-else>{{text}}</span>
					</span>

					<span v-else-if="columns.dataIndex === 'tve'">
						<a-dropdown v-if="isEditStatus && record.isEditProductOrProject" v-model="record['isShowTve' + index]" placement="bottomCenter"
							:trigger="['click']">
							<a-button class="dropdown-btn">
								<span>
									<span v-for="(item,i) in record.tve" :key="i" class="mr8">{{item.name}} <a-icon
											class="dropdown-btn-icon" type='close' @click.stop="clearTve(item,record.index,i)" /></span>
								</span>
								<a-icon style="color: rgba(0, 0, 0, 0.25);" type="down" />
							</a-button>
							<a-menu slot="overlay">
								<a-spin :spinning="tveLoading" style="padding:10px 24px 0 24px;width:100%">
									<a-input-search v-model="tveAccount" placeholder="搜索..." @change="handleTveSearch(record.index)" @key.enter="handleTveSearch(record.index)" />
									<s-table style="width:100%;" :ref="`tveTable${record.index}`" :rowKey="record1 => record1.id" :columns="UserColumns"
										:data="loadData" :customRow="record2 => customTveRow(record2,record.index)" :scroll="{ y: 120, x: 120 }">
									</s-table>
								</a-spin>
							</a-menu>
						</a-dropdown>
						<div v-else class="tve-block" v-for="(item,index) in record.tve"
							:style="{ borderBottom : index === record.tve.length - 1 ? 'none' :  '1px solid #e8e8e8' }">{{item.name}}
						</div>
					</span>

					<span v-else-if="columns.dataIndex === 'customer'">
						<a-input v-if="isEditStatus && record.isEditProductOrProject && !record.issueId" v-model="record.customer" placeholder="请输入客户"
							style="width: 70px;text-align: center;"
							@blur="$event => handleInputBlur($event,record,columns.dataIndex)" />
						<span v-else style="white-space:nowrap">{{text}}</span>
					</span>

					<span v-else-if="columns.dataIndex.indexOf('Progress') !== -1">
						<a-input v-if="isEditStatus && record.isEditProductOrProject" v-model="record[columns.dataIndex]" style="width: 60px;text-align: center;"
							@change="handleInputChange(columns.dataIndex , index)"
							@blur="$event => handleInputBlur($event,record,columns.dataIndex)" />
						<span v-else class="progress-text" @click="handleRouter(record,columns.title)">{{text}}</span>
					</span>

					<span v-else-if="columns.dataIndex.indexOf('State') !== -1">
						<a-select v-if="isEditStatus && record.isEditProductOrProject" v-model="record[columns.dataIndex]" style="width: 60px"
							@change="$event => handleSelectChange($event,record,columns.dataIndex)">
							<a-select-option v-for="item in lampOption" :value="item.value" :key="item.value">
								<div class="lamp-box">
									<div v-if="item.value == 1 || item.value == 2 || item.value == 3" class="lamp"
										:style="`background:${item.color}`"></div>
									<div v-else>{{item.label}}</div>
								</div>
							</a-select-option>
						</a-select>

						<div v-else class="lamp-box lamp-box-check" style="justify-content: center;"
							@click="handleRouter(record,columns.title)">
							<div v-if="text == 1 || text == 2 || text == 3">
								<a-tooltip>
									<template slot="title">
										{{lampOption[Number(text) - 1].placeholder}}
									</template>
									<div class="lamp" :style="`background:${lampOption[Number(text) - 1].color}`"></div>
								</a-tooltip>
							</div>
							<div v-else>{{ text !== null ? lampOption[Number(text) - 1].label : ''}}</div>
						</div>

					</span>

					<span v-else-if="columns.dataIndex === 'action'">
						<div class="action-content">
							<a-tooltip placement="top" title="上移" arrow-point-at-center>
								<a-button :disabled="isEditProject ? record.isNoUp : record.isNoUp2" type="link" class="mr5" @click="move(tableData,record.index,-1)">
									<a-icon type="arrow-up" />
								</a-button>
							</a-tooltip>
							<a-tooltip placement="top" title="下移" arrow-point-at-center>
								<a-button :disabled="isEditProject ? record.isNoDown : record.isNoDown2 " type="link" @click="move(tableData,record.index,1)">
									<a-icon type="arrow-down" />
								</a-button>
							</a-tooltip>
						</div>
					</span>
				</span>
			</a-table>
		</div>
		<!-- 表格 end -->

		<!-- 弹窗 start -->
		<div v-if="isShowAdd">
			<add-modal :selectedProduct="selectedProduct" :productNameOptions="productNameOptions"
				:projectNameAllOptions="projectNameAllOptions" @submit="handleModalSubmit"
				@cancel="handleCancel('isShowAdd')" />
		</div>
		<!-- 弹窗 end -->
	</div>
</template>
<script>
	import Vue from 'vue'
	import {
		DICT_TYPE_TREE_DATA
	} from '@/store/mutation-types'
	import _ from "lodash";
	import { mapGetters } from "vuex";


	import { STable } from "@/components"
	import addModal from './add/index.vue'

	import { getTestProgressList, getProductList, updateTestProgressRecord } from "@/api/modular/system/dpvTestManage";
	import { getUserLists } from "@/api/modular/system/userManage"

	export default {
		components: {
			STable,
			addModal
		},
		computed: {
			...mapGetters(['userInfo'])
		},
		data() {
			return {

				isManager:false,
				isTve:false,

				isEditStatus: false,
				isEditProject: false,
				tveLoading: false,
				isShowType: true,  // true:测试结果    false:测试进度
				isShowAdd: false,
				tableHeight: document.documentElement.clientHeight - 40 - 20 - 24 - 10 - 32 - 20,
				originalData:[],

				productTypeOptions: [],
				productData: [],
				selectedProduct:[],  // 列表中已经存在的产品，此处存储issueID
				productNameOptions: [],
				projectNameAllOptions: {},
				projectNameOptions: [],
				lampOption: [
					// { label: '空值', value: 0, color: '' },
					{ label: '绿灯', value: 1, color: '#8eb283',placeholder:'测试项目满足产品指标' },
					{ label: '黄灯', value: 2, color: '#f29700',placeholder:'测试项目满足产品成熟度要求但不满足最终产品指标' },
					{ label: '红灯', value: 3, color: '#d86e79',placeholder:'测试项目不满足产品成熟度要求' },
					{ label: '/', value: 4, color: '' },
				],

				UserColumns: [
					{
						title: "账号",
						dataIndex: "account"
					},
					{
						title: "姓名",
						dataIndex: "name"
					}
				],
				loadData: parameter => {
					return getUserLists({ searchValue: this.tveAccount || '',...parameter }).then(res => {
						return res.data
					})
				},
				tveAccount: '',

				tableData: [],
				tableColumns: [],
				cyclicColumns: [],
				mainColumns: [
					{
						title: '类别',
						key: "productType",
						dataIndex: "productType",
						align: 'center',
						width: '4%',
						scopedSlots: { customRender: 'productType' },
						customCell: ((record, rowIndex) => {
							return {
								attrs: {
									rowSpan: record.rowSpan,
								}
							}
						})
					},
					{
						title: '产品名称',
						key: "productName",
						dataIndex: "productName",
						align: 'center',
						width: '11%',
						scopedSlots: { customRender: 'productName' },
						customCell: ((record, rowIndex) => {
							return {
								attrs: {
									rowSpan: record.rowSpan2,
								}
							}
						})
					},
					{
						title: '项目名称',
						key: "projectName",
						dataIndex: "projectName",
						align: 'center',
						width: '11%',
						scopedSlots: { customRender: 'projectName' }
					},
					{
						title: 'Phase',
						key: "phase",
						dataIndex: "phase",
						align: 'center',
						width: '4%',
						scopedSlots: { customRender: 'phase' }
					},
					{
						title: 'TVE',
						key: "tve",
						dataIndex: "tve",
						align: 'center',
						className: 'tve-column-style',
						scopedSlots: { customRender: 'tve' }
					},
					{
						title: '客户',
						key: "customer",
						dataIndex: "customer",
						align: 'center',
						width: '4%',
						scopedSlots: { customRender: 'customer' }
					}
				],
				childStateColumns: [
					{
						title: 'A样',
						key: "aState",
						dataIndex: "aState",
						align: 'center',
						children: [
							{
								title: 'A0/Pre A',
								key: "a0State",
								dataIndex: "a0State",
								align: 'center',
								width: '5%',
								scopedSlots: { customRender: 'a0State' }
							},
							{
								title: 'A1',
								key: "a1State",
								dataIndex: "a1State",
								align: 'center',
								width: '5%',
								scopedSlots: { customRender: 'a1State' }
							},
							{
								title: 'A2',
								key: "a2State",
								dataIndex: "a2State",
								align: 'center',
								width: '5%',
								scopedSlots: { customRender: 'a2State' }
							},
							{
								title: 'A3',
								key: "a3State",
								dataIndex: "a3State",
								align: 'center',
								width: '5%',
								scopedSlots: { customRender: 'a3State' }
							},
						]
					},
					{
						title: 'B样',
						key: "bState",
						dataIndex: "bState",
						align: 'center',
						children: [
							{
								title: 'B0',
								key: "b0State",
								dataIndex: "b0State",
								align: 'center',
								width: '5%',
								scopedSlots: { customRender: 'b0State' }
							},
							{
								title: 'B1',
								key: "b1State",
								dataIndex: "b1State",
								align: 'center',
								width: '5%',
								scopedSlots: { customRender: 'b1State' }
							},
							{
								title: 'B2',
								key: "b2State",
								dataIndex: "b2State",
								align: 'center',
								width: '5%',
								scopedSlots: { customRender: 'b2State' }
							},
							{
								title: 'B3',
								key: "b3State",
								dataIndex: "b3State",
								align: 'center',
								width: '5%',
								scopedSlots: { customRender: 'b3State' }
							},
						]
					},
					{
						title: 'C样',
						key: "cState",
						dataIndex: "cState",
						align: 'center',
						children: [
							{
								title: 'C0',
								key: "c0State",
								dataIndex: "c0State",
								align: 'center',
								width: '5%',
								scopedSlots: { customRender: 'c0State' }
							},
							{
								title: 'C1',
								key: "c1State",
								dataIndex: "c1State",
								align: 'center',
								width: '5%',
								scopedSlots: { customRender: 'c1State' }
							},
							{
								title: 'C2',
								key: "c2State",
								dataIndex: "c2State",
								align: 'center',
								width: '5%',
								scopedSlots: { customRender: 'c2State' }
							}
						]
					},
				],
				childProgressColumns: [
					{
						title: 'A样',
						key: "aProgress",
						dataIndex: "aProgress",
						align: 'center',
						children: [
							{
								title: 'A0/Pre A',
								key: "a0Progress",
								dataIndex: "a0Progress",
								align: 'center',
								width: '5%',
								scopedSlots: { customRender: 'a0Progress' }
							},
							{
								title: 'A1',
								key: "a1Progress",
								dataIndex: "a1Progress",
								align: 'center',
								width: '5%',
								scopedSlots: { customRender: 'a1Progress' }
							},
							{
								title: 'A2',
								key: "a2Progress",
								dataIndex: "a2Progress",
								align: 'center',
								width: '5%',
								scopedSlots: { customRender: 'a2Progress' }
							},
							{
								title: 'A3',
								key: "a3Progress",
								dataIndex: "a3Progress",
								align: 'center',
								width: '5%',
								scopedSlots: { customRender: 'a3Progress' }
							},
						]
					},
					{
						title: 'B样',
						key: "bProgress",
						dataIndex: "bProgress",
						align: 'center',
						children: [
							{
								title: 'B0',
								key: "b0Progress",
								dataIndex: "b0Progress",
								align: 'center',
								width: '5%',
								scopedSlots: { customRender: 'b0Progress' }
							},
							{
								title: 'B1',
								key: "b1Progress",
								dataIndex: "b1Progress",
								align: 'center',
								width: '5%',
								scopedSlots: { customRender: 'b1Progress' }
							},
							{
								title: 'B2',
								key: "b2Progress",
								dataIndex: "b2Progress",
								align: 'center',
								width: '5%',
								scopedSlots: { customRender: 'b2Progress' }
							},
							{
								title: 'B3',
								key: "b3Progress",
								dataIndex: "b3Progress",
								align: 'center',
								width: '5%',
								scopedSlots: { customRender: 'b3Progress' }
							},
						]
					},
					{
						title: 'C样',
						key: "cProgress",
						dataIndex: "cProgress",
						align: 'center',
						children: [
							{
								title: 'C0',
								key: "c0Progress",
								dataIndex: "c0Progress",
								align: 'center',
								width: '5%',
								scopedSlots: { customRender: 'c0Progress' }
							},
							{
								title: 'C1',
								key: "c1Progress",
								dataIndex: "c1Progress",
								align: 'center',
								width: '5%',
								scopedSlots: { customRender: 'c1Progress' }
							},
							{
								title: 'C2',
								key: "c2Progress",
								dataIndex: "c2Progress",
								align: 'center',
								width: '5%',
								scopedSlots: { customRender: 'c2Progress' }
							}
						]
					},
				],
				actionColumn: [{
					title: '操作',
					key: "action",
					dataIndex: "action",
					align: 'center',
					width: 80,
					customCell: ((record, rowIndex) => {
						return {
							attrs: {
								rowSpan: this.isEditProject ? 1 : record.rowSpan2,
							}
						}
					}),
					scopedSlots: { customRender: 'action' }
				}]


			}
		},
		watch: {
			tableData(newVal, oldVal) {
				const subtrahend = this.tableData.length > 0 ? 45 : 0
				document.documentElement.style.setProperty(`--height`, `${this.tableHeight - subtrahend}px`)
			}
		},

		created() {
			this.isManager = this.userInfo.roles.some(item => item.id === "1831599125320130561")
			this.isTve = this.userInfo.roles.some(item => item.id === "1823606898631704578")



			this.getTestProgressList({ isUpdateProductInfo: true })
			this.getProductList()
		},
		mounted() {
			const subtrahend = this.tableData.length > 0 ? 45 : 0
			document.documentElement.style.setProperty(`--height`, `${this.tableHeight - subtrahend}px`)
		},
		methods: {
			getTestProgressList(params) {
				getTestProgressList(params).then(res => {
					this.cyclicColumns = []
					res.data.forEach((v, index) => {
						v.tve = JSON.parse(v.tve)
						v["isShowTve" + index] = false

						// 是否有编辑权限
						v.isEditProductOrProject = this.isManager ||  (this.isTve && v.tve.some(item => item.code === this.userInfo.account))

					})
					this.tableData = res.data

					// 如果tableData有数据,处理类别和产品的合并行
					if(this.tableData.length > 0){
						this._handleRowSpan(this.tableData)
					}
					

					if (this.isShowType) {
						this.tableColumns = this.mainColumns.concat(this.childStateColumns)
					} else {
						this.tableColumns = this.mainColumns.concat(this.childProgressColumns)
					}

					if (this.isEditStatus) this.tableColumns = this.tableColumns.concat(this.actionColumn)

					// 处理需要遍历的表头字段
					this.tableColumns.forEach(v => {
						if (v.children) {
							this.cyclicColumns.push(...v.children)
						} else {
							this.cyclicColumns.push(v)
						}
					})

					// 已经选中的产品/项目
					this.selectedProduct = _.uniq(this.tableData.map(mapItem => mapItem.issueId))
					this.originalData = JSON.parse(JSON.stringify(this.tableData))
				})
			},
			getProductList() {
				getProductList({}).then(res => {
					this.productNameOptions = []

					// productOrProject   1:产品   2：项目
					this.productData = res.data.filter(filterItem => filterItem.productOrProject == 1)

					this.productNameOptions = res.data.filter(filterItem => filterItem.productOrProject == 1).map(mapItem => {return {key:mapItem.issueId,label:mapItem.productName,customer:mapItem.customer } } )

					this.productNameOptions.forEach(v => {
						this.projectNameAllOptions[v.label] = res.data.filter(filterItem => filterItem.productName == v.label).map(mapItem => {return {key:mapItem.issueId,label:mapItem.projectName,customer:mapItem.customer } })
					})

					this.projectNameOptions = this.projectNameAllOptions[this.productNameOptions[0].label]


				})
			},
			getDict(code) {
				const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
				return dictTypeTree.filter(item => item.code == code)[0].children
			},
			updateTestProgressRecord(params) {
				updateTestProgressRecord(params).then(res => {
					if (!res.success) this.$message.warn('更新失败')
				})
			// .finally(() => {
			// 		this.getTestProgressList({})
			// 	})
			},

			handleEdit() {

				this.isEditStatus = !this.isEditStatus

				// 编辑状态
				if (this.isEditStatus) {
					this.tableColumns.push(this.actionColumn[0])
				} else {
					this.tableColumns.pop()
					this.getTestProgressList({})
				}

				this.cyclicColumns = []
				// 处理需要遍历的表头字段
				this.tableColumns.forEach(v => {
					if (v.children) {
						this.cyclicColumns.push(...v.children)
					} else {
						this.cyclicColumns.push(v)
					}
				})
			},
			handleModalSubmit() {
				this.isShowAdd = false
				this.getTestProgressList({})
			},
			handleCancel(target) {
				this[target] = false
			},
			handleInputBlur(e, record, target) {
				const params = {
					id: record.id,
					[target]: record[target] == null || record[target] == '' ? '' : record[target]
				}
				this.updateTestProgressRecord(params)
			},

			handleSwitchChange(e) {
				this.isShowType = e
				// 测试进度
				if (e) {
					this.tableColumns = this.mainColumns.concat(this.childStateColumns)
				} else {
					this.tableColumns = this.mainColumns.concat(this.childProgressColumns)
				}
				if (this.isEditStatus) this.tableColumns = this.tableColumns.concat(this.actionColumn)
				// 处理需要遍历的表头字段
				this.tableColumns.forEach(v => {
					if (v.children) {
						this.cyclicColumns.push(...v.children)
					} else {
						this.cyclicColumns.push(v)
					}
				})
			},
			// 选择产品
			handleProductNameChange(e, record,index) {
				// 去除前后空格
				e.label = e.label.trim()

				// 先判断是否被选择,由于数据的特殊性，需要把该产品下所有的项目的issueid进行对比

				// 如果该产品下的项目都被选择
				if(this.projectNameAllOptions[e.label].every(item => this.selectedProduct.includes(item.key))){
					record.productName = this.originalData[index].productName
					record.projectName = this.originalData[index].projectName
					return this.$message.error(e.label + '产品下的项目已全被选择')
				}

				// 如果没有被选择
				// 设置项目的选项
				this.projectNameOptions = this.projectNameAllOptions[e.label]

				// 默认选中一个产品中没被选择的项目,多个时，选择最靠前那个
				for(let i = 0 ; i < this.projectNameOptions.length ; i++){
					if(!this.selectedProduct.includes(this.projectNameOptions[i].key)){
						record.projectName =this.projectNameOptions[i]
						record.customer = this.projectNameOptions[i].customer

						const params = {
							id: record.id,
							issueId: this.projectNameOptions[i].key,
							productName: e.label,
							projectName: this.projectNameOptions[i].label,
							customer: this.projectNameOptions[i].customer
						}

						return this.updateTestProgressRecord(params)
					}
				}
			},
			// 选择项目
			handleProjectNameChange(e, record,index){
				// 去除前后空格
				e.label = e.label.trim()

				// 如果已被选中
				if(this.selectedProduct.includes(e.key)){
					// 默认选中一个产品中没被选择的项目,多个时，选择最靠前那个
					for(let i = 0 ; i < this.projectNameOptions.length ; i++){
						if(!this.selectedProduct.includes(this.projectNameOptions[i].key)){
							record.projectName =this.projectNameOptions[i]
							record.customer = this.projectNameOptions[i].customer

							const params = {
								id: record.id,
								issueId: this.projectNameOptions[i].key,
								projectName: this.projectNameOptions[i].label,
								customer: this.projectNameOptions[i].customer
							}

							this.updateTestProgressRecord(params)
							return this.$message.error('该项目已被选择')
						}
						
					}
				}

				// 如果没有被选中
				// 找到选中的那个
				const haveIndex = this.projectNameOptions.findIndex(filterItem => filterItem.key === e.key)
				record.projectName =this.projectNameOptions[haveIndex]
				record.customer = this.projectNameOptions[haveIndex].customer
				
				const params = {
					id: record.id,
					issueId: this.projectNameOptions[haveIndex].key,
					projectName: this.projectNameOptions[haveIndex].label,
					customer: this.projectNameOptions[haveIndex].customer
				}
				this.updateTestProgressRecord(params)
			},
			handleSelectChange(e, record, target) {
				const params = {
					id: record.id,
					[target]: record[target]
				}
				this.updateTestProgressRecord(params)
			},
			handleInputChange(target, index) {
				this.tableData[index][target] = this.tableData[index][target].replace(/[^\d.%\/]/g, '')
			},

			handleTveSearch(index) {
				this.$refs[`tveTable${index}`][0].refresh()
			},
			clearTve(item, tableIndex, targetIndex) {
				this.tableData[tableIndex].tve.splice(targetIndex, 1)
				const params = {
					id: this.tableData[tableIndex].id,
					tve: JSON.stringify(this.tableData[tableIndex].tve)
				}
				this.updateTestProgressRecord(params)
			},
			customTveRow(row, index) {
				return {
					on: {
						click: () => {
							const have = this.tableData[index].tve.filter(filterItem => filterItem.code == row.account)
							if (have.length === 0) {
								this.tableData[index].tve.push({ code: row.account, name: row.name })
								const params = {
									id: this.tableData[index].id,
									tve: JSON.stringify(this.tableData[index].tve)
								}
								this.updateTestProgressRecord(params)
							}
						}
					}
				}
			},

			async move(arr,index,moveStatus){
				// moveStatus  -1  上移   +1 下移

				const moveType = 'serialNumber' +  (this.isEditProject ? '2' : '')
				const moveNumber =(this.isEditProject ? 1 : arr[index  + (moveStatus > 0 ? 0  : - 1)].allRowSpan2) * moveStatus
				const promises = []


				const temIndex = arr[index][moveType]
				arr[index][moveType] = arr[index + moveNumber][moveType]
				arr[index + moveNumber][moveType] = temIndex

				if(this.isEditProject){
					// 项目排序
					for (let i = 0; i < 2; i++) {
						const params = {
							id: arr[index + Number(moveStatus)*i].id,
							serialNumber2: arr[index + Number(moveStatus)*i].serialNumber2
						}
						promises.push(updateTestProgressRecord(params))
					}
				}else{
					// 产品排序：更新产品下的其他项目
					// 先找到下移产品的所有项目
					const nameOptions = [arr[index].productName,arr[index + moveNumber].productName]
					const productType = arr[index].productType
				
					for (let i = 0; i < 2; i++) {
						arr.filter(filterItem => filterItem.productName === nameOptions[i] && filterItem.productType === productType).forEach( async (cItem,cIndex,cArr) => {
							cItem.serialNumber = cIndex === 0 ? cItem.serialNumber : cArr[0].serialNumber
							const params = {
								id: cItem.id,
								serialNumber: cItem.serialNumber
							}
							promises.push(updateTestProgressRecord(params))

						})
					}
				}


				Promise.all(promises).then(res => this.getTestProgressList({}))

				
			},

			handleRouter(record, phase) {
				if(!record.isEditProductOrProject) return
				this.$router.push({
					path: "/dpvStructureData",
					query: {
						dpvTestProgressRecordId: record.id,
						phase: phase === 'A0/Pre A' ? 'A0' : phase,
						productName: record.productName,
						projectName: record.projectName,
					}
				})
			},
			_handleRowSpan(data){
				let typeOptions = [] //一个页里面有多少类型，不包含重复的
				let allTypeOptions = []  //总共有多少类型 ，不包含重复的

				let productOptions = [] //一个页里面有多少产品，不包含重复的
				let productType = data[0].productType  // 当前是什么类型

				let rowNumber = 0 // 页数

				data.forEach((v,index) => {
					v.index = index   // 需要记录一个序号，方便后续处理数据

					// 换页时，清空记录的数据
					if(index % 10 === 0){
						typeOptions = []
						productOptions = []
						rowNumber++
					}

					// 换类型时，清空记录的数据
					if( productType !== v.productType ){
						productType = v.productType
						productOptions = []
					}

					// 当前页面的类型合并行数
					v.rowSpan = typeOptions.includes(v.productType) ? 0 : 
						data.slice((rowNumber - 1) * 10, rowNumber * 10).filter((filterItem, filterIndex) => 
						filterItem.productType == v.productType &&
						filterIndex - (rowNumber * 10) < 0).length

					// 当前页面当前类型的产品合并行数
					v.rowSpan2 = productOptions.includes(v.productName) ? 0 : 
						data.slice((rowNumber - 1) * 10, rowNumber * 10).filter((filterItem, filterIndex) => 
						filterItem.productName == v.productName &&   //产品名称相等
						filterIndex - (rowNumber * 10) < 0 &&        //
						productType === filterItem.productType).length	

					// //该类型的总数
					// v.allRowSpan = allTypeOptions.includes(v.productType) ? 0 : 
					// 	data.filter((filterItem, filterIndex) => filterItem.productType == v.productType).length

					//该类型该产品的总数
					v.allRowSpan2 = data.filter((filterItem, filterIndex) => 
						filterItem.productName == v.productName && 
						productType === filterItem.productType).length

					// 产品的排序

					// 禁止上移
					v.isNoUp2 = index === 0 || data[index - 1].productType !== productType ? true : false  

					// 禁止下移
					if(index === data.length - 1){   // 最后一行
						v.isNoDown2  = true
						if(v.allRowSpan2 !== 1) data[index - v.allRowSpan2 + 1].isNoDown2 = true  //如果是最后一行，并且是合并的，需要把合并产品的第一个项目的下移禁止
					}else if(data[index + 1].productType !== productType && v.rowSpan2 !== 0){   // 下一行的类型不等于记录类型 并且  他的合并行号不等于0
						v.isNoDown2  = true
					}else if(data[index + 1].productType !== productType && v.rowSpan2 === 0){   // 下一行的类型不等于记录类型 并且  他的合并行号等于0
						data[index - v.allRowSpan2 + 1].isNoDown2 = true
					}else{
						v.isNoDown2  = false
					}


					// 项目的排序

					// 禁止上移  (第一行、上一个与当前的产品名称不相同)
					v.isNoUp = index === 0 || data[index - 1].productName !== v.productName ? true : false

					// 禁止下移
					if(index === data.length - 1){  // 最后一行
						v.isNoDown = true
						if(index !== 0 && data[index - 1].productName !== v.productName && v.rowSpan2 !== 0) data[index - 1].isNoDown = true   //最后一行也要判断一下上一行是否要修改
					}else if(index === 0){  
						v.isNoDown = data[index + 1].productName !== v.productName ? true : false
					}else if(data[index - 1].productName !== v.productName && v.rowSpan2 !== 0){
						data[index - 1].isNoDown = true
					}else{
						v.isNoDown = false
					}

					// 本页:如果不包含该类型推入
					if (!typeOptions.includes(v.productType)) typeOptions.push(v.productType)
					// 全部数据:如果不包含该类型推入
					if (!allTypeOptions.includes(v.productType)) allTypeOptions.push(v.productType)

					// 本页:如果不包含该产品推入
					if(!productOptions.includes(v.productName)) productOptions.push(v.productName)
				})
			}
		}
	}
</script>
<style lang="less" scoped>
	:root {
		--height: 600px;
	}

	/* 标题 */
	.head-title {
		display: flex;
		align-items: center;
	}

	.head-title .line {
		width: 4px;
		height: 22px;
		background: #3293ff;
		border-radius: 20px;
	}

	.head-title .title {
		font-size: 16px;
		font-weight: 600;
	}

	/* 筛选 */
	.filter-wrapper {
		display: flex;
		justify-content: space-between;
	}

	.filter-left {
		display: flex;
	}

	.filter-right {
		display: flex;
	}

	/* 表格 */
	.table-wrapper {
		padding: 10px;
		background: #fff;
		border-radius: 10px;
	}

	.tve-block {
		padding: 4px 0;
	}

	.dropdown-btn {
		padding-left: 11px;
		font-size: 12px;
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.dropdown-btn-icon {
		pointer-events: auto;
		color: rgba(0, 0, 0, 0.25);
	}



	/deep/ .tve-column-style {
		padding: 0 !important;
		width: 11%;
	}



	/* 通用  */
	.mr5 {
		margin-right: 5px;
	}

	.mr10 {
		margin-right: 10px;
	}
	.ml10 {
		margin-left: 10px;
	}

	.mt10 {
		margin-top: 10px;
	}

	.filter-block {
		display: flex;
		align-items: center;
	}

	.filter-select {
		width: 175px;
	}

	.filter-input {
		width: 175px;
	}

	.lamp-box {
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 12px;
	}

	.lamp-box-check {
		cursor: pointer;
	}

	.lamp-box .lamp {
		width: 13px;
		height: 13px;
		border-radius: 50%;
		margin-right: 8px;
	}

	.progress-text {
		cursor: pointer;
	}

	/deep/ .action-content .ant-btn {
		padding: 0;
		font-size: 12px;
	}

	/deep/.ant-input {
		font-size: 12px;
	}

	/deep/.ant-select {
		font-size: 12px;
	}

	/deep/.ant-select-selection--single .ant-select-selection__rendered {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	/* 表格组件 */
	/deep/ .ant-table tr th {
		background: #f4f4f4;
	}

	/deep/.ant-table-body {
		height: var(--height) !important;
		overflow-y: scroll;
	}

	/deep/.ant-table-thead {
		position: sticky;
		top: 0;
		z-index: 1;

		font-size: 12px;
	}

	/* 设置边距 */
	/deep/ .ant-table-thead tr th,
	/deep/ .ant-table-tbody tr td {
		padding: 4px !important;
	}

	/deep/ .ant-pagination {
		margin: 10px 0 0;
	}

	/deep/ .ant-table-bordered.ant-table-empty .ant-table-placeholder {
		border: none;
	}

	/deep/ .ant-table-placeholder {
		border: none;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}
	/deep/td[rowSpan="0"]{
		display: none;
	}
</style>